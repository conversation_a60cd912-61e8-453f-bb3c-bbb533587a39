# 🚀 Guide de Déploiement - Agent Security

## Vue d'ensemble

L'Agent Security est un composant critique de l'écosystème Retreat And Be, responsable de la sécurité, de la conformité et de la surveillance des menaces.

## 📋 Prérequis

### Environnement de base
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **TypeScript**: >= 4.9.0

### Infrastructure
- **Docker**: >= 20.10.0 (optionnel)
- **Kubernetes**: >= 1.24.0 (optionnel)
- **Kafka**: >= 2.8.0
- **Redis**: >= 6.2.0
- **Weaviate**: >= 1.20.0

## 🔧 Installation

### 1. Installation des dépendances
```bash
cd agents/security
npm install
```

### 2. Configuration
```bash
# Copier le fichier de configuration
cp config/production.json.example config/production.json

# Éditer les variables d'environnement
nano .env
```

### 3. Construction
```bash
npm run build
```

## 🚀 Déploiement

### Déploiement automatique
```bash
# Déploiement complet avec intégration
./scripts/deploy-security-agent.sh
./scripts/integrate-with-ecosystem.sh
```

### Déploiement Docker
```bash
# Construction de l'image
docker build -t retreat-and-be/agent-security:latest .

# Démarrage du conteneur
docker run -d \
  --name agent-security \
  --restart unless-stopped \
  -p 3000:3000 \
  -p 8080:8080 \
  -p 9090:9090 \
  -e NODE_ENV=production \
  retreat-and-be/agent-security:latest
```

### Déploiement Kubernetes
```bash
# Application des manifestes
kubectl apply -f k8s/

# Vérification du déploiement
kubectl rollout status deployment/agent-security -n retreat-and-be
```

## 🔍 Vérification

### Tests de santé
```bash
# Santé de l'application
curl http://localhost:8080/health

# Métriques
curl http://localhost:9090/metrics

# API de sécurité
curl http://localhost:3000/api/security/status
```

### Monitoring
```bash
# Script de vérification automatique
./monitoring/health-check.sh
```

## 🔗 Intégration

### Avec Cortex Central
L'Agent Security s'enregistre automatiquement auprès du Cortex Central et reçoit des instructions via Kafka.

### Avec Agent Backend
- Routes de sécurité exposées: `/api/security/*`
- Middleware de sécurité intégré
- Audit automatique des requêtes

### Avec Agent Frontend
- Dashboard de sécurité disponible
- Alertes en temps réel
- Rapports de conformité

## 📊 Métriques et Monitoring

### Métriques exposées
- `security_vulnerabilities_total`: Nombre total de vulnérabilités
- `security_compliance_score`: Score de conformité (0-100)
- `security_scans_duration`: Durée des scans de sécurité
- `security_alerts_total`: Nombre total d'alertes

### Alertes configurées
- Vulnérabilités critiques détectées
- Score de conformité < 85%
- Échec de scan de sécurité
- Tentatives d'accès non autorisées

## 🔐 Sécurité

### Chiffrement
- Algorithme: AES-256-GCM
- Rotation des clés: 7 jours
- Stockage sécurisé dans Weaviate

### Audit
- Tous les événements sont loggés
- Rétention: 365 jours
- Conformité GDPR/SOC2

### Accès
- Authentification JWT requise
- Contrôle d'accès basé sur les rôles
- Sessions limitées à 8 heures

## 🛠️ Maintenance

### Mise à jour
```bash
# Arrêt gracieux
kubectl scale deployment agent-security --replicas=0

# Mise à jour de l'image
kubectl set image deployment/agent-security agent-security=retreat-and-be/agent-security:new-version

# Redémarrage
kubectl scale deployment agent-security --replicas=2
```

### Sauvegarde
```bash
# Sauvegarde automatique configurée
# Destination: s3://retreat-and-be-backups/security
# Fréquence: Toutes les 6 heures
# Rétention: 30 jours
```

### Logs
```bash
# Consultation des logs
kubectl logs -f deployment/agent-security -n retreat-and-be

# Logs locaux
tail -f /var/log/agent-security/security.log
```

## 🚨 Dépannage

### Problèmes courants

#### Agent Security ne démarre pas
```bash
# Vérifier les dépendances
kubectl get pods -n retreat-and-be | grep -E "(kafka|weaviate|redis)"

# Vérifier la configuration
kubectl describe configmap agent-security-config
```

#### Erreurs de connexion Kafka
```bash
# Tester la connectivité
kubectl exec -it agent-security -- nc -zv kafka 9092

# Vérifier les topics
kafka-topics.sh --list --bootstrap-server kafka:9092
```

#### Problèmes de performance
```bash
# Vérifier les ressources
kubectl top pod agent-security

# Ajuster les limites
kubectl patch deployment agent-security -p '{"spec":{"template":{"spec":{"containers":[{"name":"agent-security","resources":{"limits":{"memory":"2Gi","cpu":"1000m"}}}]}}}}'
```

## 📞 Support

### Contacts
- **Équipe Security**: <EMAIL>
- **DevOps**: <EMAIL>
- **Support 24/7**: <EMAIL>

### Documentation
- [Architecture Security](./docs/ARCHITECTURE.md)
- [API Reference](./docs/API.md)
- [Troubleshooting](./docs/TROUBLESHOOTING.md)

## 📈 Roadmap

### Version 1.1.0
- [ ] Intégration avec SIEM externe
- [ ] Scan de vulnérabilités en temps réel
- [ ] IA pour détection d'anomalies

### Version 1.2.0
- [ ] Conformité PCI-DSS
- [ ] Chiffrement homomorphe
- [ ] Zero-trust architecture

---

**Note**: Ce guide est maintenu par l'équipe Agent Security. Pour toute question ou suggestion, créez une issue dans le repository.
