/**
 * 🗣️ Hanuman Voice System - Système Vocal
 * 
 * Système de communication vocale de Hanuman permettant :
 * - Synthèse vocale (Text-to-Speech)
 * - Reconnaissance vocale (Speech-to-Text)
 * - Communication multilingue
 * - Interface conversationnelle
 */

import { EventEmitter } from 'events';
import { Logger } from '../../infrastructure/logging/Logger';

export interface VoiceConfig {
  language: string;
  voice: string;
  speed: number;
  pitch: number;
  volume: number;
  enableRecognition: boolean;
  enableSynthesis: boolean;
}

export interface VoiceCommand {
  id: string;
  text: string;
  confidence: number;
  language: string;
  timestamp: Date;
}

export interface VoiceResponse {
  id: string;
  text: string;
  audioUrl?: string;
  language: string;
  timestamp: Date;
}

export class VoiceSystem extends EventEmitter {
  private logger: Logger;
  private config: VoiceConfig;
  private isListening: boolean = false;
  private isSpeaking: boolean = false;
  private supportedLanguages: string[] = ['fr-FR', 'en-US', 'es-ES', 'de-DE'];
  private voiceHistory: Array<VoiceCommand | VoiceResponse> = [];

  constructor(config?: Partial<VoiceConfig>) {
    super();
    this.logger = new Logger('VoiceSystem');
    this.config = {
      language: 'fr-FR',
      voice: 'hanuman-voice',
      speed: 1.0,
      pitch: 1.0,
      volume: 0.8,
      enableRecognition: true,
      enableSynthesis: true,
      ...config
    };
  }

  /**
   * Initialise le système vocal
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('🗣️ Initialisation du système vocal de Hanuman');
      
      // Vérification des capacités du navigateur/système
      await this.checkCapabilities();
      
      // Initialisation de la synthèse vocale
      if (this.config.enableSynthesis) {
        await this.initializeSynthesis();
      }
      
      // Initialisation de la reconnaissance vocale
      if (this.config.enableRecognition) {
        await this.initializeRecognition();
      }
      
      this.logger.info('✅ Système vocal de Hanuman initialisé');
      this.emit('voice:ready');
      
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'initialisation du système vocal:', error);
      throw error;
    }
  }

  /**
   * Vérifie les capacités vocales disponibles
   */
  private async checkCapabilities(): Promise<void> {
    // Vérification de la synthèse vocale
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      this.logger.info('✅ Synthèse vocale disponible');
    } else {
      this.logger.warn('⚠️ Synthèse vocale non disponible');
      this.config.enableSynthesis = false;
    }

    // Vérification de la reconnaissance vocale
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      this.logger.info('✅ Reconnaissance vocale disponible');
    } else {
      this.logger.warn('⚠️ Reconnaissance vocale non disponible');
      this.config.enableRecognition = false;
    }
  }

  /**
   * Initialise la synthèse vocale
   */
  private async initializeSynthesis(): Promise<void> {
    if (typeof window === 'undefined') return;

    // Configuration des voix disponibles
    const voices = speechSynthesis.getVoices();
    this.logger.info(`🎵 ${voices.length} voix disponibles`);
    
    // Sélection de la voix française par défaut
    const frenchVoice = voices.find(voice => voice.lang.startsWith('fr'));
    if (frenchVoice) {
      this.logger.info(`🇫🇷 Voix française sélectionnée: ${frenchVoice.name}`);
    }
  }

  /**
   * Initialise la reconnaissance vocale
   */
  private async initializeRecognition(): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      // @ts-ignore
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = this.config.language;
      
      recognition.onresult = (event: any) => {
        const result = event.results[event.results.length - 1];
        if (result.isFinal) {
          const command: VoiceCommand = {
            id: this.generateId(),
            text: result[0].transcript,
            confidence: result[0].confidence,
            language: this.config.language,
            timestamp: new Date()
          };
          
          this.voiceHistory.push(command);
          this.logger.info(`🎤 Commande vocale: "${command.text}" (${Math.round(command.confidence * 100)}%)`);
          this.emit('voice:command', command.text);
        }
      };
      
      recognition.onerror = (event: any) => {
        this.logger.error('❌ Erreur de reconnaissance vocale:', event.error);
        this.emit('voice:error', event.error);
      };
      
      this.logger.info('✅ Reconnaissance vocale configurée');
      
    } catch (error) {
      this.logger.error('❌ Erreur lors de la configuration de la reconnaissance vocale:', error);
    }
  }

  /**
   * Fait parler Hanuman (synthèse vocale)
   */
  public async speak(text: string, options?: Partial<VoiceConfig>): Promise<VoiceResponse> {
    return new Promise((resolve, reject) => {
      try {
        if (!this.config.enableSynthesis) {
          this.logger.warn('⚠️ Synthèse vocale désactivée');
          return resolve({
            id: this.generateId(),
            text,
            language: this.config.language,
            timestamp: new Date()
          });
        }

        const utterance = new SpeechSynthesisUtterance(text);
        const config = { ...this.config, ...options };
        
        // Configuration de l'utterance
        utterance.lang = config.language;
        utterance.rate = config.speed;
        utterance.pitch = config.pitch;
        utterance.volume = config.volume;
        
        // Sélection de la voix
        const voices = speechSynthesis.getVoices();
        const selectedVoice = voices.find(voice => 
          voice.lang === config.language || voice.name.includes(config.voice)
        );
        if (selectedVoice) {
          utterance.voice = selectedVoice;
        }
        
        const response: VoiceResponse = {
          id: this.generateId(),
          text,
          language: config.language,
          timestamp: new Date()
        };
        
        utterance.onstart = () => {
          this.isSpeaking = true;
          this.logger.info(`🗣️ Hanuman parle: "${text}"`);
          this.emit('voice:speaking_start', response);
        };
        
        utterance.onend = () => {
          this.isSpeaking = false;
          this.voiceHistory.push(response);
          this.logger.info('✅ Hanuman a fini de parler');
          this.emit('voice:speaking_end', response);
          resolve(response);
        };
        
        utterance.onerror = (event) => {
          this.isSpeaking = false;
          this.logger.error('❌ Erreur de synthèse vocale:', event.error);
          this.emit('voice:error', event.error);
          reject(new Error(event.error));
        };
        
        speechSynthesis.speak(utterance);
        
      } catch (error) {
        this.logger.error('❌ Erreur lors de la synthèse vocale:', error);
        reject(error);
      }
    });
  }

  /**
   * Démarre l'écoute vocale
   */
  public async startListening(): Promise<void> {
    if (!this.config.enableRecognition) {
      this.logger.warn('⚠️ Reconnaissance vocale désactivée');
      return;
    }

    try {
      this.isListening = true;
      this.logger.info('👂 Hanuman commence à écouter...');
      this.emit('voice:listening_start');
      
      // Implémentation de l'écoute continue
      // (Le code de reconnaissance est déjà configuré dans initializeRecognition)
      
    } catch (error) {
      this.logger.error('❌ Erreur lors du démarrage de l\'écoute:', error);
      this.isListening = false;
      throw error;
    }
  }

  /**
   * Arrête l'écoute vocale
   */
  public async stopListening(): Promise<void> {
    this.isListening = false;
    this.logger.info('🔇 Hanuman arrête d\'écouter');
    this.emit('voice:listening_stop');
  }

  /**
   * Change la langue de communication
   */
  public async setLanguage(language: string): Promise<void> {
    if (!this.supportedLanguages.includes(language)) {
      throw new Error(`Langue non supportée: ${language}`);
    }
    
    this.config.language = language;
    this.logger.info(`🌍 Langue changée vers: ${language}`);
    this.emit('voice:language_changed', language);
  }

  /**
   * Obtient l'historique vocal
   */
  public getVoiceHistory(): Array<VoiceCommand | VoiceResponse> {
    return [...this.voiceHistory];
  }

  /**
   * Efface l'historique vocal
   */
  public clearVoiceHistory(): void {
    this.voiceHistory = [];
    this.logger.info('🗑️ Historique vocal effacé');
  }

  /**
   * Obtient l'état du système vocal
   */
  public async getHealthStatus(): Promise<any> {
    return {
      active: true,
      listening: this.isListening,
      speaking: this.isSpeaking,
      language: this.config.language,
      synthesisEnabled: this.config.enableSynthesis,
      recognitionEnabled: this.config.enableRecognition,
      supportedLanguages: this.supportedLanguages,
      historySize: this.voiceHistory.length,
      timestamp: new Date()
    };
  }

  /**
   * Arrêt du système vocal
   */
  public async shutdown(): Promise<void> {
    this.logger.info('🔇 Arrêt du système vocal');
    
    await this.stopListening();
    
    if (this.isSpeaking) {
      speechSynthesis.cancel();
      this.isSpeaking = false;
    }
    
    this.emit('voice:shutdown');
  }

  /**
   * Génère un ID unique
   */
  private generateId(): string {
    return `voice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export default VoiceSystem;
