import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { <PERSON>gger } from 'winston';
import { ExplorerAgent } from '../agents/ExplorerAgent';
import { MutationType } from '../types/evolution';

// Mock du logger
const mockLogger = {
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
} as unknown as <PERSON><PERSON>;

describe('ExplorerAgent', () => {
  let explorerAgent: ExplorerAgent;

  beforeEach(() => {
    explorerAgent = new ExplorerAgent(mockLogger);
    jest.clearAllMocks();
  });

  describe('generateVariants', () => {
    it('should generate the requested number of variants', async () => {
      const request = {
        problem: 'Sort an array',
        domain: 'sorting',
        count: 5,
        constraints: {}
      };

      const variants = await explorerAgent.generateVariants(request);

      expect(variants).toHaveLength(5);
      expect(variants[0]).toHaveProperty('id');
      expect(variants[0]).toHaveProperty('code');
      expect(variants[0]).toHaveProperty('approach');
      expect(variants[0]).toHaveProperty('fitness');
    });

    it('should generate variants with different approaches', async () => {
      const request = {
        problem: 'Find maximum element',
        domain: 'searching',
        count: 3,
        constraints: {}
      };

      const variants = await explorerAgent.generateVariants(request);
      const approaches = variants.map(v => v.approach);
      
      // Vérifier que nous avons des approches différentes
      const uniqueApproaches = new Set(approaches);
      expect(uniqueApproaches.size).toBeGreaterThan(1);
    });

    it('should emit variant-generated events', async () => {
      const request = {
        problem: 'Test problem',
        domain: 'test',
        count: 2,
        constraints: {}
      };

      const eventSpy = jest.fn();
      explorerAgent.on('variant-generated', eventSpy);

      await explorerAgent.generateVariants(request);

      expect(eventSpy).toHaveBeenCalledTimes(2);
      expect(eventSpy).toHaveBeenCalledWith({
        index: 1,
        total: 2,
        approach: expect.any(String)
      });
    });

    it('should handle empty constraints gracefully', async () => {
      const request = {
        problem: 'Simple problem',
        domain: 'general',
        count: 1,
        constraints: {}
      };

      const variants = await explorerAgent.generateVariants(request);

      expect(variants).toHaveLength(1);
      expect(variants[0].code).toBeDefined();
    });
  });

  describe('generateMutation', () => {
    const mockParent = {
      id: 'parent-1',
      code: 'function sort(arr) { return arr.sort(); }',
      description: 'Simple sort function',
      approach: 'built-in',
      fitness: {
        total: 0.7,
        performance: 0.8,
        correctness: 0.9,
        efficiency: 0.6,
        robustness: 0.7,
        maintainability: 0.8,
        innovation: 0.3
      },
      generation: 1,
      parentIds: [],
      mutations: [],
      performance: {
        executionTime: 100,
        memoryUsage: 1024,
        cpuUsage: 0.5,
        complexity: {
          timeComplexity: 'O(n log n)',
          spaceComplexity: 'O(1)',
          cyclomaticComplexity: 2,
          cognitiveComplexity: 1
        },
        scalability: 0.8
      }
    };

    const mockRequest = {
      problem: 'Sort array',
      domain: 'sorting',
      metrics: ['performance'],
      constraints: {},
      config: {} as any
    };

    it('should generate a mutation with SIMPLE type', async () => {
      const mutation = await explorerAgent.generateMutation(
        mockParent,
        MutationType.SIMPLE,
        mockRequest
      );

      expect(mutation.id).toContain('explorer-mut-');
      expect(mutation.parentIds).toContain(mockParent.id);
      expect(mutation.generation).toBe(mockParent.generation + 1);
      expect(mutation.mutations).toHaveLength(1);
      expect(mutation.mutations[0].type).toBe(MutationType.SIMPLE);
    });

    it('should generate a mutation with CREATIVE type', async () => {
      const mutation = await explorerAgent.generateMutation(
        mockParent,
        MutationType.CREATIVE,
        mockRequest
      );

      expect(mutation.approach).toContain(mockParent.approach);
      expect(mutation.mutations[0].type).toBe(MutationType.CREATIVE);
      expect(mutation.code).toBeDefined();
    });

    it('should emit mutation-generated event', async () => {
      const eventSpy = jest.fn();
      explorerAgent.on('mutation-generated', eventSpy);

      await explorerAgent.generateMutation(
        mockParent,
        MutationType.OPTIMIZATION,
        mockRequest
      );

      expect(eventSpy).toHaveBeenCalledWith({
        parent: mockParent.id,
        child: expect.any(String),
        type: MutationType.OPTIMIZATION
      });
    });

    it('should preserve parent information in mutation', async () => {
      const mutation = await explorerAgent.generateMutation(
        mockParent,
        MutationType.REFACTORING,
        mockRequest
      );

      expect(mutation.parentIds).toEqual([mockParent.id]);
      expect(mutation.description).toContain(mockParent.description);
      expect(mutation.generation).toBe(mockParent.generation + 1);
    });

    it('should handle different mutation types', async () => {
      const mutationTypes = [
        MutationType.SIMPLE,
        MutationType.CREATIVE,
        MutationType.OPTIMIZATION,
        MutationType.REFACTORING,
        MutationType.CROSSOVER,
        MutationType.HYBRIDIZATION
      ];

      for (const type of mutationTypes) {
        const mutation = await explorerAgent.generateMutation(
          mockParent,
          type,
          mockRequest
        );

        expect(mutation.mutations[0].type).toBe(type);
        expect(mutation.code).toBeDefined();
        expect(mutation.fitness).toBeDefined();
      }
    });
  });

  describe('fitness estimation', () => {
    it('should estimate reasonable fitness scores', async () => {
      const request = {
        problem: 'Test algorithm',
        domain: 'test',
        count: 1,
        constraints: {}
      };

      const variants = await explorerAgent.generateVariants(request);
      const fitness = variants[0].fitness;

      expect(fitness.total).toBeGreaterThanOrEqual(0);
      expect(fitness.total).toBeLessThanOrEqual(1);
      expect(fitness.performance).toBeGreaterThanOrEqual(0);
      expect(fitness.performance).toBeLessThanOrEqual(1);
      expect(fitness.correctness).toBe(0.5); // Valeur par défaut
      expect(fitness.innovation).toBeGreaterThan(0);
    });

    it('should estimate performance metrics', async () => {
      const request = {
        problem: 'Performance test',
        domain: 'performance',
        count: 1,
        constraints: {}
      };

      const variants = await explorerAgent.generateVariants(request);
      const performance = variants[0].performance;

      expect(performance.complexity.timeComplexity).toBeDefined();
      expect(performance.complexity.spaceComplexity).toBeDefined();
      expect(performance.complexity.cyclomaticComplexity).toBeGreaterThanOrEqual(1);
      expect(performance.complexity.cognitiveComplexity).toBeGreaterThanOrEqual(0);
    });
  });

  describe('code generation', () => {
    it('should generate valid code structure', async () => {
      const request = {
        problem: 'Generate function',
        domain: 'general',
        count: 1,
        constraints: {}
      };

      const variants = await explorerAgent.generateVariants(request);
      const code = variants[0].code;

      // Vérifier que le code contient des éléments de base
      expect(typeof code).toBe('string');
      expect(code.length).toBeGreaterThan(0);
    });

    it('should adapt code based on approach', async () => {
      const request = {
        problem: 'Adaptive generation',
        domain: 'adaptive',
        count: 3,
        constraints: {}
      };

      const variants = await explorerAgent.generateVariants(request);

      // Vérifier que les codes sont différents
      const codes = variants.map(v => v.code);
      const uniqueCodes = new Set(codes);
      expect(uniqueCodes.size).toBeGreaterThan(1);
    });
  });

  describe('error handling', () => {
    it('should handle invalid requests gracefully', async () => {
      const invalidRequest = {
        problem: '',
        domain: '',
        count: 0,
        constraints: null
      };

      // Ne devrait pas lever d'exception
      const variants = await explorerAgent.generateVariants(invalidRequest);
      expect(Array.isArray(variants)).toBe(true);
    });

    it('should handle mutation errors gracefully', async () => {
      const invalidParent = {
        ...mockParent,
        code: '' // Code vide
      } as any;

      const mockRequest = {
        problem: 'Test',
        domain: 'test',
        metrics: [],
        constraints: {},
        config: {} as any
      };

      // Ne devrait pas lever d'exception
      const mutation = await explorerAgent.generateMutation(
        invalidParent,
        MutationType.SIMPLE,
        mockRequest
      );

      expect(mutation).toBeDefined();
      expect(mutation.code).toBeDefined();
    });
  });

  describe('diversity and creativity', () => {
    it('should generate diverse solutions', async () => {
      const request = {
        problem: 'Diversity test',
        domain: 'test',
        count: 10,
        constraints: {}
      };

      const variants = await explorerAgent.generateVariants(request);
      
      // Vérifier la diversité des approches
      const approaches = variants.map(v => v.approach);
      const uniqueApproaches = new Set(approaches);
      
      expect(uniqueApproaches.size).toBeGreaterThan(1);
      expect(uniqueApproaches.size).toBeLessThanOrEqual(variants.length);
    });

    it('should include innovation scores', async () => {
      const request = {
        problem: 'Innovation test',
        domain: 'creative',
        count: 5,
        constraints: {}
      };

      const variants = await explorerAgent.generateVariants(request);
      
      variants.forEach(variant => {
        expect(variant.fitness.innovation).toBeGreaterThan(0);
        expect(variant.fitness.innovation).toBeLessThanOrEqual(1);
      });
    });
  });
});
