import { GeneticMemoryEngine } from '../engines/GeneticMemoryEngine';
import { GeneType, GeneticMemoryRequest, EvolutionSolution } from '../types/evolution';
import { mockLogger } from './setup';

describe('GeneticMemoryEngine Integration Tests', () => {
  let engine: GeneticMemoryEngine;

  beforeEach(async () => {
    engine = new GeneticMemoryEngine(mockLogger);
    await engine.initialize();
    jest.clearAllMocks();
  });

  describe('Advanced Pattern Analysis', () => {
    test('should extract complex patterns using AST analysis', async () => {
      const complexCode = `
        function quickSort(arr) {
          if (arr.length <= 1) return arr;
          
          const pivot = arr[Math.floor(arr.length / 2)];
          const left = [];
          const right = [];
          
          for (let i = 0; i < arr.length; i++) {
            if (i === Math.floor(arr.length / 2)) continue;
            if (arr[i] < pivot) {
              left.push(arr[i]);
            } else {
              right.push(arr[i]);
            }
          }
          
          return [...quickSort(left), pivot, ...quickSort(right)];
        }
      `;

      const solution: EvolutionSolution = {
        id: 'complex-solution',
        code: complexCode,
        description: 'QuickSort implementation',
        approach: 'divide-and-conquer',
        fitness: {
          total: 0.9,
          performance: 0.95,
          correctness: 0.9,
          efficiency: 0.9,
          robustness: 0.85,
          maintainability: 0.8,
          innovation: 0.7
        },
        generation: 1,
        parentIds: [],
        mutations: [],
        performance: {
          executionTime: 50,
          memoryUsage: 512,
          cpuUsage: 30,
          complexity: {
            timeComplexity: 'O(n log n)',
            spaceComplexity: 'O(log n)',
            cyclomaticComplexity: 5,
            cognitiveComplexity: 4
          },
          scalability: 0.9
        }
      };

      const extractedGenes = await engine.extractGenesFromSolution(solution);

      expect(extractedGenes.length).toBeGreaterThan(0);
      
      // Vérifier que différents types de patterns ont été extraits
      const geneTypes = new Set(extractedGenes.map(gene => gene.type));
      expect(geneTypes.size).toBeGreaterThan(1);
      
      // Vérifier que des patterns algorithmiques ont été détectés
      expect(extractedGenes.some(gene => gene.type === GeneType.ALGORITHM)).toBe(true);
    });

    test('should classify patterns correctly based on AST analysis', async () => {
      const classCode = `
        class DataProcessor {
          private cache = new Map();
          
          process(data) {
            if (this.cache.has(data.id)) {
              return this.cache.get(data.id);
            }
            
            const result = this.complexProcessing(data);
            this.cache.set(data.id, result);
            return result;
          }
          
          private complexProcessing(data) {
            // Complex processing logic
            return data.value * 2;
          }
        }
      `;

      const gene = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.STRUCTURE,
        pattern: classCode,
        context: { domain: 'data-processing' }
      });

      expect(gene.type).toBe(GeneType.STRUCTURE);
      expect(gene.sequence).toBe(classCode);
    });
  });

  describe('Advanced Database Operations', () => {
    beforeEach(async () => {
      // Stocker plusieurs gènes de test
      const testGenes = [
        {
          operation: 'store' as const,
          geneType: GeneType.ALGORITHM,
          pattern: 'function binarySearch(arr, target) { /* implementation */ }',
          context: { domain: 'search', performance: 0.9 }
        },
        {
          operation: 'store' as const,
          geneType: GeneType.ALGORITHM,
          pattern: 'function linearSearch(arr, target) { /* implementation */ }',
          context: { domain: 'search', performance: 0.6 }
        },
        {
          operation: 'store' as const,
          geneType: GeneType.OPTIMIZATION,
          pattern: 'const memo = new Map(); if (memo.has(key)) return memo.get(key);',
          context: { domain: 'caching', performance: 0.8 }
        }
      ];

      for (const geneData of testGenes) {
        const gene = await engine.storeGene(geneData);
        await engine.updateGeneFitness(gene.id, geneData.context.performance);
      }
    });

    test('should perform advanced semantic search', async () => {
      const results = await engine.searchGenesAdvanced({
        semanticTerms: ['search', 'algorithm'],
        minFitness: 0.7,
        limit: 10
      });

      expect(results.length).toBeGreaterThan(0);
      expect(results.every(gene => gene.fitness >= 0.7)).toBe(true);
      expect(results.some(gene => 
        gene.sequence.includes('search') || gene.expression.includes('search')
      )).toBe(true);
    });

    test('should find similar genes', async () => {
      const algorithmGenes = await engine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.ALGORITHM
      });

      if (algorithmGenes.length > 0) {
        const similarGenes = await engine.findSimilarGenes(algorithmGenes[0].id, 0.5);
        
        expect(Array.isArray(similarGenes)).toBe(true);
        // Les gènes similaires devraient avoir le même type
        expect(similarGenes.every(gene => gene.type === algorithmGenes[0].type)).toBe(true);
      }
    });

    test('should provide database statistics', async () => {
      const stats = engine.getDatabaseStatistics();

      expect(stats).toBeDefined();
      expect(stats.totalGenes).toBeGreaterThan(0);
      expect(stats.typeDistribution).toBeDefined();
      expect(stats.averageFitness).toBeGreaterThan(0);
      expect(stats.fitnessRange).toBeDefined();
      expect(stats.fitnessRange.min).toBeLessThanOrEqual(stats.fitnessRange.max);
    });

    test('should cleanup obsolete genes', async () => {
      const initialStats = engine.getDatabaseStatistics();
      
      // Nettoyer les gènes avec fitness très faible
      const cleanedCount = await engine.cleanupObsoleteGenes({
        minFitness: 0.95 // Seuil très élevé pour forcer le nettoyage
      });

      const finalStats = engine.getDatabaseStatistics();
      
      expect(cleanedCount).toBeGreaterThanOrEqual(0);
      expect(finalStats.totalGenes).toBeLessThanOrEqual(initialStats.totalGenes);
    });
  });

  describe('Phylogenetic Analysis', () => {
    test('should generate phylogenetic report', async () => {
      // Créer une lignée de gènes
      const parentGene = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: 'function sort(arr) { return arr.sort(); }',
        context: { generation: 0 }
      });

      await engine.updateGeneFitness(parentGene.id, 0.8);

      // Évoluer des gènes
      const evolvedGenes = await engine.evolveGenes({
        operation: 'evolve',
        geneType: GeneType.ALGORITHM
      });

      const report = await engine.generatePhylogeneticReport(GeneType.ALGORITHM);

      expect(report).toBeDefined();
      expect(report.geneType).toBe(GeneType.ALGORITHM);
      expect(report.diversity).toBeDefined();
      expect(report.trees).toBeDefined();
      expect(report.ancestralGenes).toBeDefined();
      expect(report.recentEvolutionEvents).toBeDefined();
      expect(report.recommendations).toBeDefined();
    });

    test('should analyze genetic diversity', async () => {
      const diversity = engine.analyzeDiversity(GeneType.ALGORITHM);

      expect(diversity).toBeDefined();
      expect(diversity.totalGenes).toBeGreaterThanOrEqual(0);
      expect(diversity.maxGeneration).toBeGreaterThanOrEqual(0);
      expect(diversity.generationalDiversity).toBeGreaterThanOrEqual(0);
      expect(diversity.phylogeneticDiversity).toBeGreaterThanOrEqual(0);
      expect(diversity.averageBranchingRate).toBeGreaterThanOrEqual(0);
      expect(diversity.extinctionRate).toBeGreaterThanOrEqual(0);
      expect(diversity.convergenceIndex).toBeGreaterThanOrEqual(0);
    });

    test('should trace evolutionary paths', async () => {
      // Créer des gènes avec lignée
      const ancestor = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: 'function ancestorSort() {}',
        context: { generation: 0 }
      });

      const descendant = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: 'function descendantSort() {}',
        context: { parents: [ancestor.id] }
      });

      const path = await engine.traceEvolutionaryPath(ancestor.id, descendant.id);

      if (path) {
        expect(path.fromGeneId).toBe(ancestor.id);
        expect(path.toGeneId).toBe(descendant.id);
        expect(path.pathLength).toBeGreaterThan(0);
        expect(path.evolutionaryDistance).toBeGreaterThanOrEqual(0);
        expect(Array.isArray(path.path)).toBe(true);
      }
    });
  });

  describe('Advanced Evolution', () => {
    test('should perform intelligent recombination', async () => {
      // Stocker des gènes parents de haute qualité
      const parent1 = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: 'function efficientSort(arr) { return arr.sort((a, b) => a - b); }',
        context: { performance: 0.9 }
      });

      const parent2 = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: 'function robustSort(arr) { if (!Array.isArray(arr)) return []; return arr.sort(); }',
        context: { robustness: 0.9 }
      });

      await engine.updateGeneFitness(parent1.id, 0.85);
      await engine.updateGeneFitness(parent2.id, 0.8);

      const evolvedGenes = await engine.evolveGenes({
        operation: 'evolve',
        geneType: GeneType.ALGORITHM
      });

      expect(evolvedGenes.length).toBeGreaterThan(0);
      
      // Les gènes évolués devraient avoir des caractéristiques des parents
      expect(evolvedGenes.every(gene => gene.type === GeneType.ALGORITHM)).toBe(true);
      expect(evolvedGenes.every(gene => gene.fitness > 0)).toBe(true);
    });

    test('should maintain genetic diversity during evolution', async () => {
      // Créer une population diverse
      const diverseGenes = [
        'function bubbleSort(arr) { /* implementation */ }',
        'function quickSort(arr) { /* implementation */ }',
        'function mergeSort(arr) { /* implementation */ }',
        'function heapSort(arr) { /* implementation */ }'
      ];

      for (const pattern of diverseGenes) {
        const gene = await engine.storeGene({
          operation: 'store',
          geneType: GeneType.ALGORITHM,
          pattern,
          context: { sorting: true }
        });
        await engine.updateGeneFitness(gene.id, 0.8);
      }

      const initialDiversity = engine.analyzeDiversity(GeneType.ALGORITHM);

      // Évoluer la population
      await engine.evolveGenes({
        operation: 'evolve',
        geneType: GeneType.ALGORITHM
      });

      const finalDiversity = engine.analyzeDiversity(GeneType.ALGORITHM);

      // La diversité ne devrait pas diminuer drastiquement
      expect(finalDiversity.phylogeneticDiversity).toBeGreaterThan(0);
      expect(finalDiversity.totalGenes).toBeGreaterThanOrEqual(initialDiversity.totalGenes);
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle large gene populations efficiently', async () => {
      const startTime = Date.now();
      
      // Créer une grande population de gènes
      const promises = [];
      for (let i = 0; i < 50; i++) {
        promises.push(engine.storeGene({
          operation: 'store',
          geneType: GeneType.ALGORITHM,
          pattern: `function algorithm${i}() { return ${i}; }`,
          context: { index: i }
        }));
      }

      await Promise.all(promises);
      
      const storageTime = Date.now() - startTime;
      expect(storageTime).toBeLessThan(10000); // Moins de 10 secondes

      // Test de recherche
      const searchStart = Date.now();
      const results = await engine.searchGenesAdvanced({
        type: GeneType.ALGORITHM,
        limit: 20
      });
      const searchTime = Date.now() - searchStart;

      expect(results.length).toBeGreaterThan(0);
      expect(searchTime).toBeLessThan(1000); // Moins d'1 seconde
    });

    test('should maintain performance with complex phylogenetic analysis', async () => {
      // Créer une lignée complexe
      let currentGene = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: 'function root() {}',
        context: { generation: 0 }
      });

      // Créer plusieurs générations
      for (let gen = 1; gen <= 5; gen++) {
        const newGene = await engine.storeGene({
          operation: 'store',
          geneType: GeneType.ALGORITHM,
          pattern: `function gen${gen}() {}`,
          context: { parents: [currentGene.id] }
        });
        currentGene = newGene;
      }

      const analysisStart = Date.now();
      const diversity = engine.analyzeDiversity(GeneType.ALGORITHM);
      const report = await engine.generatePhylogeneticReport(GeneType.ALGORITHM);
      const analysisTime = Date.now() - analysisStart;

      expect(diversity).toBeDefined();
      expect(report).toBeDefined();
      expect(analysisTime).toBeLessThan(2000); // Moins de 2 secondes
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle malformed code gracefully', async () => {
      const malformedCode = 'function broken( { return undefined';

      const gene = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: malformedCode,
        context: { malformed: true }
      });

      expect(gene).toBeDefined();
      expect(gene.sequence).toBe(malformedCode);
      
      // L'extraction de patterns devrait utiliser le fallback regex
      const solution: EvolutionSolution = {
        id: 'malformed-solution',
        code: malformedCode,
        description: 'Malformed code test',
        approach: 'test',
        fitness: {
          total: 0.1,
          performance: 0.1,
          correctness: 0.1,
          efficiency: 0.1,
          robustness: 0.1,
          maintainability: 0.1,
          innovation: 0.1
        },
        generation: 1,
        parentIds: [],
        mutations: [],
        performance: {
          executionTime: 1000,
          memoryUsage: 1024,
          cpuUsage: 100,
          complexity: {
            timeComplexity: 'O(1)',
            spaceComplexity: 'O(1)',
            cyclomaticComplexity: 1,
            cognitiveComplexity: 1
          },
          scalability: 0.1
        }
      };

      const extractedGenes = await engine.extractGenesFromSolution(solution);
      expect(Array.isArray(extractedGenes)).toBe(true);
    });

    test('should handle empty search results', async () => {
      const results = await engine.searchGenesAdvanced({
        semanticTerms: ['nonexistent', 'impossible'],
        minFitness: 0.99
      });

      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBe(0);
    });

    test('should handle invalid gene IDs in phylogenetic analysis', async () => {
      const path = await engine.traceEvolutionaryPath('invalid-id-1', 'invalid-id-2');
      expect(path).toBeNull();

      const similarGenes = await engine.findSimilarGenes('invalid-id');
      expect(Array.isArray(similarGenes)).toBe(true);
      expect(similarGenes.length).toBe(0);
    });
  });
});
