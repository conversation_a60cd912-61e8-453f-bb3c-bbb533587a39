import { EventEmitter } from 'events';
import { Logger } from '../utils/logger';
import {
  NeuralAdaptation,
  AdaptationType,
  NeuroplasticityRequest
} from '../types/evolution';

/**
 * Moteur de Neuroplasticité
 *
 * Implémente l'adaptation continue des connexions synaptiques entre agents,
 * inspiré de la neuroplasticité du cerveau humain :
 * - Renforcement synaptique (LTP - Long Term Potentiation)
 * - Affaiblissement synaptique (LTD - Long Term Depression)
 * - Formation de nouvelles connexions
 * - Élagage des connexions inutiles
 * - Optimisation des voies de communication
 */
export class NeuroplasticityEngine extends EventEmitter {
  private logger: Logger;
  private synapticConnections: Map<string, SynapticConnection> = new Map();
  private adaptationHistory: NeuralAdaptation[] = [];
  private learningRates: Map<string, number> = new Map();
  private plasticityThresholds: PlasticityThresholds;

  constructor(logger: Logger) {
    super();
    this.logger = logger;
    this.plasticityThresholds = {
      strengthening: 0.7,
      weakening: 0.3,
      pruning: 0.1,
      formation: 0.8
    };
  }

  /**
   * Initialise le moteur de neuroplasticité
   */
  async initialize(): Promise<void> {
    this.logger.info('🧠 Initialisation du moteur de neuroplasticité');

    // Chargement des connexions synaptiques existantes
    await this.loadSynapticConnections();

    // Initialisation des taux d'apprentissage par agent
    this.initializeLearningRates();

    // Démarrage du processus d'adaptation continue
    this.startContinuousAdaptation();

    this.logger.info(`✅ Neuroplasticité initialisée: ${this.synapticConnections.size} connexions`);
  }

  /**
   * Traite une demande d'adaptation neuroplastique
   */
  async processAdaptation(request: NeuroplasticityRequest): Promise<NeuralAdaptation> {
    this.logger.info(`🔄 Traitement adaptation: ${request.adaptationType} pour ${request.agentId}`);

    const adaptation = await this.createAdaptation(request);

    // Application de l'adaptation
    await this.applyAdaptation(adaptation);

    // Enregistrement dans l'historique
    this.adaptationHistory.push(adaptation);

    // Émission d'événement
    this.emit('adaptation-applied', adaptation);

    return adaptation;
  }

  /**
   * Renforce une connexion synaptique (LTP)
   */
  async strengthenConnection(fromAgent: string, toAgent: string, stimulus: any): Promise<void> {
    const connectionId = this.getConnectionId(fromAgent, toAgent);
    const connection = this.synapticConnections.get(connectionId);

    if (connection) {
      const learningRate = this.learningRates.get(fromAgent) || 0.1;
      const strengthIncrease = this.calculateStrengthIncrease(stimulus, learningRate);

      connection.strength = Math.min(1.0, connection.strength + strengthIncrease);
      connection.lastUsed = new Date();
      connection.usageCount++;
      connection.averageLatency = this.updateAverageLatency(connection, stimulus.latency || 0);

      this.logger.debug(`💪 Connexion renforcée ${fromAgent} -> ${toAgent}: ${connection.strength.toFixed(3)}`);

      // Mise à jour du taux d'apprentissage (métaplasticité)
      this.updateLearningRate(fromAgent, true);
    } else {
      // Création d'une nouvelle connexion si elle n'existe pas
      await this.createConnection(fromAgent, toAgent, stimulus);
    }
  }

  /**
   * Affaiblit une connexion synaptique (LTD)
   */
  async weakenConnection(fromAgent: string, toAgent: string, reason: string): Promise<void> {
    const connectionId = this.getConnectionId(fromAgent, toAgent);
    const connection = this.synapticConnections.get(connectionId);

    if (connection) {
      const learningRate = this.learningRates.get(fromAgent) || 0.1;
      const strengthDecrease = learningRate * 0.5; // Affaiblissement plus lent que renforcement

      connection.strength = Math.max(0.0, connection.strength - strengthDecrease);
      connection.lastUsed = new Date();

      this.logger.debug(`📉 Connexion affaiblie ${fromAgent} -> ${toAgent}: ${connection.strength.toFixed(3)} (${reason})`);

      // Élagage si la force devient trop faible
      if (connection.strength < this.plasticityThresholds.pruning) {
        await this.pruneConnection(connectionId);
      }

      // Mise à jour du taux d'apprentissage
      this.updateLearningRate(fromAgent, false);
    }
  }

  /**
   * Crée une nouvelle connexion synaptique
   */
  async createConnection(fromAgent: string, toAgent: string, stimulus: any): Promise<void> {
    const connectionId = this.getConnectionId(fromAgent, toAgent);

    const newConnection: SynapticConnection = {
      id: connectionId,
      fromAgent,
      toAgent,
      strength: 0.5, // Force initiale modérée
      lastUsed: new Date(),
      usageCount: 1,
      averageLatency: stimulus.latency || 0,
      createdAt: new Date(),
      adaptationCount: 0,
      metadata: {
        creationReason: 'new_communication_pattern',
        initialStimulus: stimulus
      }
    };

    this.synapticConnections.set(connectionId, newConnection);

    this.logger.info(`🆕 Nouvelle connexion créée: ${fromAgent} -> ${toAgent}`);

    // Émission d'événement
    this.emit('connection-created', newConnection);
  }



  /**
   * Démarre le processus d'adaptation continue
   */
  private startContinuousAdaptation(): void {
    // Adaptation toutes les 5 minutes
    setInterval(async () => {
      await this.performPeriodicAdaptation();
    }, 5 * 60 * 1000);

    // Nettoyage des connexions obsolètes toutes les heures
    setInterval(async () => {
      await this.cleanupObsoleteConnections();
    }, 60 * 60 * 1000);
  }

  /**
   * Adaptation périodique automatique
   */
  private async performPeriodicAdaptation(): Promise<void> {
    this.logger.debug('🔄 Adaptation périodique en cours...');

    const connections = Array.from(this.synapticConnections.values());

    for (const connection of connections) {
      // Décroissance naturelle des connexions inutilisées
      const timeSinceLastUse = Date.now() - connection.lastUsed.getTime();
      const hoursUnused = timeSinceLastUse / (1000 * 60 * 60);

      if (hoursUnused > 24) {
        const decayRate = Math.min(0.1, hoursUnused / 240); // Décroissance progressive
        connection.strength = Math.max(0, connection.strength - decayRate);

        if (connection.strength < this.plasticityThresholds.pruning) {
          await this.pruneConnection(connection.id);
        }
      }
    }
  }

  /**
   * Nettoyage des connexions obsolètes
   */
  private async cleanupObsoleteConnections(): Promise<void> {
    const connections = Array.from(this.synapticConnections.values());
    const obsoleteConnections = connections.filter(conn =>
      conn.strength < this.plasticityThresholds.pruning ||
      (Date.now() - conn.lastUsed.getTime()) > (7 * 24 * 60 * 60 * 1000) // 7 jours
    );

    for (const connection of obsoleteConnections) {
      await this.pruneConnection(connection.id);
    }

    if (obsoleteConnections.length > 0) {
      this.logger.info(`🧹 Nettoyage: ${obsoleteConnections.length} connexions obsolètes supprimées`);
    }
  }

  /**
   * Crée une adaptation neuroplastique
   */
  private async createAdaptation(request: NeuroplasticityRequest): Promise<NeuralAdaptation> {
    const learningRate = this.learningRates.get(request.agentId) || 0.1;

    // Extraire le toAgent du connectionId (format: "fromAgent->toAgent")
    const toAgent = request.connectionId.includes('->') ?
      request.connectionId.split('->')[1] || request.connectionId :
      request.connectionId;

    return {
      id: `adapt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      fromAgent: request.agentId,
      toAgent: toAgent,
      synapticStrength: this.calculateNewStrength(request),
      adaptationType: request.adaptationType,
      learningRate,
      timestamp: new Date()
    };
  }

  /**
   * Applique une adaptation
   */
  private async applyAdaptation(adaptation: NeuralAdaptation): Promise<void> {
    switch (adaptation.adaptationType) {
      case AdaptationType.SYNAPTIC_STRENGTHENING:
        await this.strengthenConnection(adaptation.fromAgent, adaptation.toAgent, {});
        break;
      case AdaptationType.SYNAPTIC_WEAKENING:
        await this.weakenConnection(adaptation.fromAgent, adaptation.toAgent, 'adaptation_request');
        break;
      case AdaptationType.NEW_CONNECTION:
        await this.createConnection(adaptation.fromAgent, adaptation.toAgent, {});
        break;
      case AdaptationType.CONNECTION_PRUNING:
        const connectionId = this.getConnectionId(adaptation.fromAgent, adaptation.toAgent);
        await this.pruneConnection(connectionId);
        break;
      case AdaptationType.PATHWAY_OPTIMIZATION:
        await this.optimizePathways();
        break;
    }
  }

  /**
   * Calcule l'augmentation de force synaptique
   */
  private calculateStrengthIncrease(stimulus: any, learningRate: number): number {
    const baseIncrease = learningRate;
    const stimulusIntensity = stimulus.intensity || 1.0;
    const successFactor = stimulus.success ? 1.2 : 0.8;

    return baseIncrease * stimulusIntensity * successFactor;
  }

  /**
   * Met à jour la latence moyenne
   */
  private updateAverageLatency(connection: SynapticConnection, newLatency: number): number {
    const alpha = 0.1; // Facteur de lissage exponentiel
    return connection.averageLatency * (1 - alpha) + newLatency * alpha;
  }

  /**
   * Met à jour le taux d'apprentissage (métaplasticité)
   */
  private updateLearningRate(agentId: string, success: boolean): void {
    const currentRate = this.learningRates.get(agentId) || 0.1;
    const adjustment = success ? 0.001 : -0.001;
    const newRate = Math.max(0.01, Math.min(0.5, currentRate + adjustment));

    this.learningRates.set(agentId, newRate);
  }

  /**
   * Calcule la nouvelle force synaptique
   */
  private calculateNewStrength(_request: NeuroplasticityRequest): number {
    // Implémentation simplifiée
    return Math.random() * 0.5 + 0.5;
  }

  /**
   * Analyse les patterns de communication
   */
  public analyzeCommunicationPatterns(): CommunicationPattern[] {
    const connections = Array.from(this.synapticConnections.values());
    const patterns: CommunicationPattern[] = [];

    // Grouper les connexions par agents
    const agentGroups = new Map<string, SynapticConnection[]>();

    connections.forEach(conn => {
      if (!agentGroups.has(conn.fromAgent)) {
        agentGroups.set(conn.fromAgent, []);
      }
      agentGroups.get(conn.fromAgent)!.push(conn);
    });

    // Analyser les patterns pour chaque groupe
    agentGroups.forEach((conns, agent) => {
      if (conns.length > 1) {
        const totalUsage = conns.reduce((sum, conn) => sum + conn.usageCount, 0);
        const avgLatency = conns.reduce((sum, conn) => sum + conn.averageLatency, 0) / conns.length;
        const avgStrength = conns.reduce((sum, conn) => sum + conn.strength, 0) / conns.length;

        patterns.push({
          agents: [agent, ...conns.map(c => c.toAgent)],
          frequency: totalUsage,
          efficiency: avgStrength * (1 / (avgLatency + 1)), // Plus la latence est faible, plus c'est efficace
          averageLatency: avgLatency,
          connectionCount: conns.length,
          pattern: this.identifyPattern(conns)
        });
      }
    });

    return patterns.sort((a, b) => b.efficiency - a.efficiency);
  }

  /**
   * Identifie le type de pattern de communication
   */
  private identifyPattern(connections: SynapticConnection[]): string {
    if (connections.length === 1) return 'direct';
    if (connections.length === 2) return 'bilateral';
    if (connections.length > 5) return 'hub';
    return 'cluster';
  }

  /**
   * Identifie les voies sous-optimales
   */
  public identifySuboptimalPaths(): CommunicationPath[] {
    const connections = Array.from(this.synapticConnections.values());
    const suboptimalPaths: CommunicationPath[] = [];

    connections.forEach(conn => {
      // Identifier les connexions avec latence élevée ou force faible
      const isHighLatency = conn.averageLatency > 100; // ms
      const isWeakConnection = conn.strength < 0.4;
      const isUnderutilized = conn.usageCount < 5;

      if (isHighLatency || isWeakConnection || isUnderutilized) {
        suboptimalPaths.push({
          from: conn.fromAgent,
          to: conn.toAgent,
          currentLatency: conn.averageLatency,
          currentStrength: conn.strength,
          usageCount: conn.usageCount,
          bottleneckType: this.identifyBottleneckType(conn),
          optimizationPotential: this.calculateOptimizationPotential(conn)
        });
      }
    });

    return suboptimalPaths.sort((a, b) => b.optimizationPotential - a.optimizationPotential);
  }

  /**
   * Identifie le type de goulot d'étranglement
   */
  private identifyBottleneckType(connection: SynapticConnection): string {
    if (connection.averageLatency > 100) return 'high_latency';
    if (connection.strength < 0.3) return 'weak_connection';
    if (connection.usageCount < 5) return 'underutilized';
    return 'unknown';
  }

  /**
   * Calcule le potentiel d'optimisation
   */
  private calculateOptimizationPotential(connection: SynapticConnection): number {
    const latencyFactor = Math.max(0, (connection.averageLatency - 50) / 100);
    const strengthFactor = Math.max(0, (0.7 - connection.strength) / 0.7);
    const usageFactor = Math.max(0, (10 - connection.usageCount) / 10);

    return (latencyFactor + strengthFactor + usageFactor) / 3;
  }

  /**
   * Optimise automatiquement les voies de communication
   */
  public async optimizeCommunicationPaths(): Promise<OptimizationResult> {
    this.logger.info('🔧 Optimisation des voies de communication en cours...');

    const suboptimalPaths = this.identifySuboptimalPaths();
    const optimizations: PathOptimization[] = [];

    for (const path of suboptimalPaths.slice(0, 5)) { // Optimiser les 5 pires
      const optimization = await this.optimizePath(path);
      optimizations.push(optimization);
    }

    const result: OptimizationResult = {
      timestamp: new Date(),
      pathsAnalyzed: suboptimalPaths.length,
      pathsOptimized: optimizations.length,
      optimizations,
      averageImprovement: this.calculateAverageImprovement(optimizations),
      networkEfficiencyGain: await this.calculateNetworkEfficiencyGain()
    };

    this.logger.info(`✅ Optimisation terminée: ${result.pathsOptimized} voies optimisées`);
    this.emit('paths-optimized', result);

    return result;
  }

  /**
   * Optimise une voie spécifique
   */
  private async optimizePath(path: CommunicationPath): Promise<PathOptimization> {
    const connectionId = this.getConnectionId(path.from, path.to);
    const connection = this.synapticConnections.get(connectionId);

    if (!connection) {
      return {
        path,
        action: 'create_connection',
        beforeMetrics: { latency: 0, strength: 0 },
        afterMetrics: { latency: 50, strength: 0.5 },
        improvement: 0.5
      };
    }

    const beforeMetrics = {
      latency: connection.averageLatency,
      strength: connection.strength
    };

    // Appliquer l'optimisation selon le type de goulot
    switch (path.bottleneckType) {
      case 'high_latency':
        await this.optimizeLatency(connection);
        break;
      case 'weak_connection':
        await this.strengthenConnection(path.from, path.to, { optimization: true });
        break;
      case 'underutilized':
        await this.promoteConnection(connection);
        break;
    }

    const afterMetrics = {
      latency: connection.averageLatency,
      strength: connection.strength
    };

    const improvement = this.calculateImprovement(beforeMetrics, afterMetrics);

    return {
      path,
      action: `optimize_${path.bottleneckType}`,
      beforeMetrics,
      afterMetrics,
      improvement
    };
  }

  /**
   * Optimise la latence d'une connexion
   */
  private async optimizeLatency(connection: SynapticConnection): Promise<void> {
    // Simulation d'optimisation de latence
    connection.averageLatency = Math.max(20, connection.averageLatency * 0.7);
    connection.adaptationCount++;
  }

  /**
   * Promeut une connexion sous-utilisée
   */
  private async promoteConnection(connection: SynapticConnection): Promise<void> {
    connection.strength = Math.min(1.0, connection.strength + 0.2);
    connection.adaptationCount++;
  }

  /**
   * Calcule l'amélioration entre avant et après
   */
  private calculateImprovement(before: any, after: any): number {
    const latencyImprovement = Math.max(0, (before.latency - after.latency) / before.latency);
    const strengthImprovement = Math.max(0, (after.strength - before.strength) / 1.0);

    return (latencyImprovement + strengthImprovement) / 2;
  }

  /**
   * Calcule l'amélioration moyenne
   */
  private calculateAverageImprovement(optimizations: PathOptimization[]): number {
    if (optimizations.length === 0) return 0;

    const totalImprovement = optimizations.reduce((sum, opt) => sum + opt.improvement, 0);
    return totalImprovement / optimizations.length;
  }

  /**
   * Calcule le gain d'efficacité du réseau
   */
  private async calculateNetworkEfficiencyGain(): Promise<number> {
    const metrics = this.getPlasticityMetrics();
    return metrics.networkEfficiency;
  }

  /**
   * Optimise les voies de communication (méthode appelée par applyAdaptation)
   */
  private async optimizePathways(): Promise<void> {
    await this.optimizeCommunicationPaths();
  }



  /**
   * Élague une connexion synaptique
   */
  public async pruneConnection(connectionId: string): Promise<void> {
    const connection = this.synapticConnections.get(connectionId);

    if (connection) {
      this.synapticConnections.delete(connectionId);
      this.logger.info(`🔥 Connexion élagée: ${connectionId}`);

      this.emit('connection-pruned', {
        connectionId,
        reason: 'manual_pruning',
        connection
      });
    }
  }

  /**
   * Charge les connexions synaptiques existantes
   */
  private async loadSynapticConnections(): Promise<void> {
    // Implémentation du chargement depuis la base de données
  }

  /**
   * Initialise les taux d'apprentissage
   */
  private initializeLearningRates(): void {
    const defaultRate = 0.1;
    const agents = ['cortex-central', 'agent-frontend', 'agent-backend', 'agent-devops', 'agent-qa', 'agent-security'];

    agents.forEach(agent => {
      this.learningRates.set(agent, defaultRate);
    });
  }

  /**
   * Obtient les métriques de plasticité
   */
  public getPlasticityMetrics(): PlasticityMetrics {
    const connections = Array.from(this.synapticConnections.values());
    const totalConnections = connections.length;

    if (totalConnections === 0) {
      return {
        totalConnections: 0,
        averageStrength: 0,
        strongConnections: 0,
        weakConnections: 0,
        averageLatency: 0,
        adaptationRate: 0,
        networkEfficiency: 0
      };
    }

    const totalStrength = connections.reduce((sum, conn) => sum + conn.strength, 0);
    const averageStrength = totalStrength / totalConnections;
    const strongConnections = connections.filter(conn => conn.strength > 0.7).length;
    const weakConnections = connections.filter(conn => conn.strength < 0.3).length;
    const averageLatency = connections.reduce((sum, conn) => sum + conn.averageLatency, 0) / totalConnections;
    const totalAdaptations = connections.reduce((sum, conn) => sum + conn.adaptationCount, 0);
    const adaptationRate = totalAdaptations / totalConnections;
    const networkEfficiency = averageStrength * (1 / (averageLatency + 1));

    return {
      totalConnections,
      averageStrength,
      strongConnections,
      weakConnections,
      averageLatency,
      adaptationRate,
      networkEfficiency
    };
  }

  /**
   * Obtient l'historique des adaptations
   */
  public getAdaptationHistory(): NeuralAdaptation[] {
    return [...this.adaptationHistory];
  }

  /**
   * Obtient l'ID de connexion
   */
  public getConnectionId(fromAgent: string, toAgent: string): string {
    return `${fromAgent}->${toAgent}`;
  }
}

// Interfaces supplémentaires
interface SynapticConnection {
  id: string;
  fromAgent: string;
  toAgent: string;
  strength: number;
  lastUsed: Date;
  usageCount: number;
  averageLatency: number;
  createdAt: Date;
  adaptationCount: number;
  metadata: any;
}

interface PlasticityThresholds {
  strengthening: number;
  weakening: number;
  pruning: number;
  formation: number;
}

interface CommunicationPattern {
  agents: string[];
  frequency: number;
  efficiency: number;
  averageLatency: number;
  connectionCount: number;
  pattern: string;
}

interface PlasticityMetrics {
  totalConnections: number;
  averageStrength: number;
  strongConnections: number;
  weakConnections: number;
  averageLatency: number;
  adaptationRate: number;
  networkEfficiency: number;
}

interface CommunicationPath {
  from: string;
  to: string;
  currentLatency: number;
  currentStrength: number;
  usageCount: number;
  bottleneckType: string;
  optimizationPotential: number;
}

interface OptimizationResult {
  timestamp: Date;
  pathsAnalyzed: number;
  pathsOptimized: number;
  optimizations: PathOptimization[];
  averageImprovement: number;
  networkEfficiencyGain: number;
}

interface PathOptimization {
  path: CommunicationPath;
  action: string;
  beforeMetrics: {
    latency: number;
    strength: number;
  };
  afterMetrics: {
    latency: number;
    strength: number;
  };
  improvement: number;
}
