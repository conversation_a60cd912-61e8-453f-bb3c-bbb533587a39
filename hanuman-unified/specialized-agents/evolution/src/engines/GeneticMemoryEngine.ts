import { EventEmitter } from 'events';
import { Logger } from 'winston';
import {
  GeneticCode,
  GeneType,
  GeneticMemoryRequest,
  EvolutionSolution
} from '../types/evolution';
import { ASTPatternAnalyzer, CodePattern } from '../utils/ASTPatternAnalyzer';
import { GeneticDatabase, GeneSearchQuery } from '../storage/GeneticDatabase';
import { PhylogeneticAnalyzer, GeneCreationContext } from '../analysis/PhylogeneticAnalyzer';

/**
 * Moteur de Mémoire Génétique
 *
 * Implémente un système de mémoire inspiré de l'ADN/ARN pour stocker,
 * récupérer et faire évoluer les "gènes" algorithmiques :
 * - Stockage de patterns de code réussis
 * - Indexation sémantique des solutions
 * - Évolution des gènes par recombinaison
 * - Hérédité des caractéristiques performantes
 * - Mutation contrôlée des gènes
 */
export class GeneticMemoryEngine extends EventEmitter {
  private logger: Logger;
  private genePool: Map<string, GeneticCode> = new Map();
  private geneIndex: Map<string, Set<string>> = new Map(); // Index par type/pattern
  private geneLineage: Map<string, GeneLineage> = new Map(); // Lignée génétique
  private expressionHistory: GeneExpression[] = [];
  private fitnessThreshold: number = 0.7;

  // Nouveaux composants Sprint 3
  private astAnalyzer: ASTPatternAnalyzer;
  private geneticDatabase: GeneticDatabase;
  private phylogeneticAnalyzer: PhylogeneticAnalyzer;

  constructor(logger: Logger) {
    super();
    this.logger = logger;

    // Initialisation des nouveaux composants
    this.astAnalyzer = new ASTPatternAnalyzer();
    this.geneticDatabase = new GeneticDatabase(logger);
    this.phylogeneticAnalyzer = new PhylogeneticAnalyzer(logger);
  }

  /**
   * Initialise le moteur de mémoire génétique
   */
  async initialize(): Promise<void> {
    this.logger.info('🧬 Initialisation de la mémoire génétique avancée');

    // Initialisation des nouveaux composants
    await this.geneticDatabase.initialize();
    await this.phylogeneticAnalyzer.initialize();

    // Chargement du pool génétique existant
    await this.loadGenePool();

    // Construction des index
    this.buildGeneIndex();

    // Initialisation des gènes fondamentaux
    await this.initializeFundamentalGenes();

    this.logger.info(`✅ Mémoire génétique avancée initialisée: ${this.genePool.size} gènes`);
  }

  /**
   * Traite une demande de mémoire génétique
   */
  async processRequest(request: GeneticMemoryRequest): Promise<any> {
    switch (request.operation) {
      case 'store':
        return await this.storeGene(request);
      case 'retrieve':
        return await this.retrieveGenes(request);
      case 'evolve':
        return await this.evolveGenes(request);
      default:
        throw new Error(`Opération non supportée: ${request.operation}`);
    }
  }

  /**
   * Stocke un nouveau gène dans la mémoire
   */
  async storeGene(request: GeneticMemoryRequest): Promise<GeneticCode> {
    const gene: GeneticCode = {
      id: `gene-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      sequence: request.pattern || '',
      type: request.geneType,
      expression: this.extractExpression(request.pattern || ''),
      fitness: 0.5, // Fitness initiale neutre
      age: 0,
      usageCount: 0,
      lastUsed: new Date()
    };

    // Stockage dans le pool
    this.genePool.set(gene.id, gene);

    // Stockage dans la base de données avancée
    await this.geneticDatabase.storeGene(gene);

    // Mise à jour de l'index
    this.updateGeneIndex(gene);

    // Création de la lignée et analyse phylogénétique
    this.createGeneLineage(gene, request.context);
    await this.phylogeneticAnalyzer.recordGeneCreation(gene, {
      type: 'creation',
      parentIds: request.context?.parents || []
    });

    this.logger.info(`🧬 Nouveau gène stocké: ${gene.id} (${gene.type})`);

    // Émission d'événement
    this.emit('gene-stored', gene);

    return gene;
  }

  /**
   * Récupère des gènes selon les critères
   */
  async retrieveGenes(request: GeneticMemoryRequest): Promise<GeneticCode[]> {
    let candidates: GeneticCode[] = [];

    if (request.geneType) {
      // Recherche par type
      const geneIds = this.geneIndex.get(request.geneType) || new Set();
      candidates = Array.from(geneIds).map(id => this.genePool.get(id)!).filter(Boolean);
    } else if (request.pattern) {
      // Recherche par pattern
      candidates = this.searchByPattern(request.pattern);
    } else {
      // Récupération de tous les gènes
      candidates = Array.from(this.genePool.values());
    }

    // Filtrage par fitness
    const filteredGenes = candidates.filter(gene => gene.fitness >= this.fitnessThreshold);

    // Tri par fitness et usage récent
    const sortedGenes = filteredGenes.sort((a, b) => {
      const fitnessScore = b.fitness - a.fitness;
      const recencyScore = (b.lastUsed.getTime() - a.lastUsed.getTime()) / 1000000;
      return fitnessScore + recencyScore * 0.1;
    });

    // Mise à jour des statistiques d'usage
    sortedGenes.forEach(gene => {
      gene.usageCount++;
      gene.lastUsed = new Date();
    });

    this.logger.debug(`🔍 Récupération: ${sortedGenes.length} gènes trouvés`);

    return sortedGenes;
  }

  /**
   * Fait évoluer les gènes par recombinaison
   */
  async evolveGenes(request: GeneticMemoryRequest): Promise<GeneticCode[]> {
    this.logger.info('🧬 Évolution génétique en cours...');

    // Sélection des gènes parents
    const parentGenes = await this.selectParentGenes(request.geneType);

    if (parentGenes.length < 2) {
      this.logger.warn('Pas assez de gènes parents pour l\'évolution');
      return [];
    }

    const evolvedGenes: GeneticCode[] = [];

    // Génération de nouvelles combinaisons
    for (let i = 0; i < parentGenes.length - 1; i++) {
      for (let j = i + 1; j < parentGenes.length; j++) {
        const parent1 = parentGenes[i];
        const parent2 = parentGenes[j];

        // Recombinaison génétique
        const offspring = await this.recombineGenes(parent1, parent2);
        evolvedGenes.push(...offspring);
      }
    }

    // Mutation des gènes évolués
    const mutatedGenes = await this.mutateGenes(evolvedGenes);

    // Stockage des nouveaux gènes
    for (const gene of mutatedGenes) {
      this.genePool.set(gene.id, gene);
      this.updateGeneIndex(gene);
    }

    this.logger.info(`🧬 Évolution terminée: ${mutatedGenes.length} nouveaux gènes créés`);

    return mutatedGenes;
  }

  /**
   * Extrait les gènes d'une solution performante
   */
  async extractGenesFromSolution(solution: EvolutionSolution): Promise<GeneticCode[]> {
    const extractedGenes: GeneticCode[] = [];

    // Analyse du code pour identifier les patterns
    const patterns = this.identifyCodePatterns(solution.code);

    for (const pattern of patterns) {
      const gene: GeneticCode = {
        id: `extracted-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        sequence: pattern.code,
        type: pattern.type,
        expression: pattern.description,
        fitness: solution.fitness.total,
        age: 0,
        usageCount: 1,
        lastUsed: new Date()
      };

      extractedGenes.push(gene);

      // Stockage automatique si la fitness est élevée
      if (gene.fitness >= this.fitnessThreshold) {
        await this.storeGene({
          operation: 'store',
          geneType: gene.type,
          pattern: gene.sequence,
          context: { extractedFrom: solution.id }
        });
      }
    }

    this.logger.info(`🧬 Extraction: ${extractedGenes.length} gènes extraits de ${solution.id}`);

    return extractedGenes;
  }

  /**
   * Met à jour la fitness d'un gène
   */
  async updateGeneFitness(geneId: string, newFitness: number, context?: any): Promise<void> {
    const gene = this.genePool.get(geneId);

    if (gene) {
      // Mise à jour progressive de la fitness (moyenne pondérée)
      const alpha = 0.3; // Facteur de lissage
      gene.fitness = gene.fitness * (1 - alpha) + newFitness * alpha;
      gene.age++;
      gene.lastUsed = new Date();

      // Enregistrement de l'expression
      this.recordGeneExpression(gene, newFitness, context);

      // Mise à jour de l'historique phylogénétique
      await this.phylogeneticAnalyzer.updateFitnessHistory(geneId, newFitness);

      this.logger.debug(`📊 Fitness mise à jour pour ${geneId}: ${gene.fitness.toFixed(3)}`);

      // Émission d'événement
      this.emit('gene-fitness-updated', { gene, newFitness, context });
    }
  }

  /**
   * Recherche sémantique avancée dans la base de données
   */
  async searchGenesAdvanced(query: GeneSearchQuery): Promise<GeneticCode[]> {
    const results = await this.geneticDatabase.searchGenes(query);
    return results.map(result => result.gene);
  }

  /**
   * Trouve les gènes similaires à un gène donné
   */
  async findSimilarGenes(geneId: string, threshold: number = 0.7): Promise<GeneticCode[]> {
    return await this.geneticDatabase.findSimilarGenes(geneId, threshold);
  }

  /**
   * Obtient les statistiques de la base de données génétique
   */
  getDatabaseStatistics() {
    return this.geneticDatabase.getStatistics();
  }

  /**
   * Génère un rapport phylogénétique
   */
  async generatePhylogeneticReport(geneType?: GeneType) {
    return await this.phylogeneticAnalyzer.generatePhylogeneticReport(geneType);
  }

  /**
   * Analyse la diversité génétique
   */
  analyzeDiversity(geneType?: GeneType) {
    return this.phylogeneticAnalyzer.analyzeDiversity(geneType);
  }

  /**
   * Trace le chemin évolutionnaire entre deux gènes
   */
  async traceEvolutionaryPath(fromGeneId: string, toGeneId: string) {
    return this.phylogeneticAnalyzer.traceEvolutionaryPath(fromGeneId, toGeneId);
  }

  /**
   * Nettoie les gènes obsolètes
   */
  async cleanupObsoleteGenes(criteria: { maxAge?: number; minFitness?: number; maxUnusedDays?: number }) {
    return await this.geneticDatabase.cleanup(criteria);
  }

  /**
   * Recherche par pattern sémantique
   */
  private searchByPattern(pattern: string): GeneticCode[] {
    const results: GeneticCode[] = [];
    const searchTerms = this.extractSearchTerms(pattern);

    for (const gene of this.genePool.values()) {
      const similarity = this.calculateSemanticSimilarity(searchTerms, gene);
      if (similarity > 0.5) {
        results.push(gene);
      }
    }

    return results.sort((a, b) => b.fitness - a.fitness);
  }

  /**
   * Sélectionne les gènes parents pour l'évolution
   */
  private async selectParentGenes(geneType?: GeneType): Promise<GeneticCode[]> {
    let candidates = Array.from(this.genePool.values());

    if (geneType) {
      const geneIds = this.geneIndex.get(geneType) || new Set();
      candidates = Array.from(geneIds).map(id => this.genePool.get(id)!).filter(Boolean);
    }

    // Sélection des meilleurs gènes
    return candidates
      .filter(gene => gene.fitness >= this.fitnessThreshold)
      .sort((a, b) => b.fitness - a.fitness)
      .slice(0, 10); // Top 10
  }

  /**
   * Recombinaison de deux gènes parents
   */
  private async recombineGenes(parent1: GeneticCode, parent2: GeneticCode): Promise<GeneticCode[]> {
    const offspring: GeneticCode[] = [];

    // Recombinaison simple : mélange des séquences
    const sequence1 = this.crossoverSequences(parent1.sequence, parent2.sequence, 0.6);
    const sequence2 = this.crossoverSequences(parent2.sequence, parent1.sequence, 0.6);

    const child1: GeneticCode = {
      id: `recomb-${Date.now()}-1`,
      sequence: sequence1,
      type: parent1.type,
      expression: `Recombinaison de ${parent1.expression} et ${parent2.expression}`,
      fitness: (parent1.fitness + parent2.fitness) / 2,
      age: 0,
      usageCount: 0,
      lastUsed: new Date()
    };

    const child2: GeneticCode = {
      id: `recomb-${Date.now()}-2`,
      sequence: sequence2,
      type: parent2.type,
      expression: `Recombinaison de ${parent2.expression} et ${parent1.expression}`,
      fitness: (parent1.fitness + parent2.fitness) / 2,
      age: 0,
      usageCount: 0,
      lastUsed: new Date()
    };

    offspring.push(child1, child2);

    // Création des lignées
    this.createGeneLineage(child1, { parents: [parent1.id, parent2.id] });
    this.createGeneLineage(child2, { parents: [parent2.id, parent1.id] });

    return offspring;
  }

  /**
   * Mutation des gènes
   */
  private async mutateGenes(genes: GeneticCode[]): Promise<GeneticCode[]> {
    const mutatedGenes: GeneticCode[] = [];
    const mutationRate = 0.1;

    for (const gene of genes) {
      if (Math.random() < mutationRate) {
        const mutatedGene: GeneticCode = {
          ...gene,
          id: `mut-${gene.id}-${Date.now()}`,
          sequence: this.mutateSequence(gene.sequence),
          expression: `Mutation de ${gene.expression}`,
          age: 0
        };

        mutatedGenes.push(mutatedGene);

        // Création de la lignée
        this.createGeneLineage(mutatedGene, { mutatedFrom: gene.id });
      } else {
        mutatedGenes.push(gene);
      }
    }

    return mutatedGenes;
  }

  /**
   * Identifie les patterns de code avec analyse AST avancée
   */
  private identifyCodePatterns(code: string): CodePattern[] {
    this.logger.debug('🔍 Analyse AST du code pour extraction de patterns');

    // Utilisation de l'analyseur AST avancé
    const patterns = this.astAnalyzer.analyzeCode(code);

    this.logger.debug(`📊 ${patterns.length} patterns identifiés via AST`);

    return patterns;
  }

  /**
   * Croisement de séquences
   */
  private crossoverSequences(seq1: string, seq2: string, ratio: number): string {
    const lines1 = seq1.split('\n');
    const lines2 = seq2.split('\n');
    const result: string[] = [];

    const maxLength = Math.max(lines1.length, lines2.length);

    for (let i = 0; i < maxLength; i++) {
      if (Math.random() < ratio && i < lines1.length) {
        result.push(lines1[i]);
      } else if (i < lines2.length) {
        result.push(lines2[i]);
      }
    }

    return result.join('\n');
  }

  /**
   * Mutation d'une séquence
   */
  private mutateSequence(sequence: string): string {
    // Mutation simple : modification de variables
    return sequence.replace(/\b[a-zA-Z_]\w*\b/g, (match) => {
      if (Math.random() < 0.1) {
        return match + '_v2';
      }
      return match;
    });
  }

  /**
   * Extraction de l'expression d'un pattern
   */
  private extractExpression(pattern: string): string {
    // Extraction simplifiée des mots-clés
    const keywords = pattern.match(/\b(for|while|if|function|class|return)\b/g) || [];
    return keywords.join(' ');
  }

  /**
   * Mise à jour de l'index des gènes
   */
  private updateGeneIndex(gene: GeneticCode): void {
    if (!this.geneIndex.has(gene.type)) {
      this.geneIndex.set(gene.type, new Set());
    }
    this.geneIndex.get(gene.type)!.add(gene.id);
  }

  /**
   * Création d'une lignée génétique
   */
  private createGeneLineage(gene: GeneticCode, context: any): void {
    const lineage: GeneLineage = {
      geneId: gene.id,
      generation: 0,
      parents: context.parents || [],
      children: [],
      createdAt: new Date(),
      context
    };

    this.geneLineage.set(gene.id, lineage);
  }

  /**
   * Enregistrement d'une expression génétique
   */
  private recordGeneExpression(gene: GeneticCode, fitness: number, context?: any): void {
    const expression: GeneExpression = {
      geneId: gene.id,
      fitness,
      timestamp: new Date(),
      context: context || {}
    };

    this.expressionHistory.push(expression);

    // Limitation de l'historique
    if (this.expressionHistory.length > 10000) {
      this.expressionHistory = this.expressionHistory.slice(-5000);
    }
  }

  /**
   * Calcul de similarité sémantique
   */
  private calculateSemanticSimilarity(searchTerms: string[], gene: GeneticCode): number {
    const geneTerms = this.extractSearchTerms(gene.sequence + ' ' + gene.expression);
    const intersection = searchTerms.filter(term => geneTerms.includes(term));
    const union = [...new Set([...searchTerms, ...geneTerms])];

    return intersection.length / union.length;
  }

  /**
   * Extraction des termes de recherche
   */
  private extractSearchTerms(text: string): string[] {
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(term => term.length > 2);
  }

  /**
   * Construction de l'index des gènes
   */
  private buildGeneIndex(): void {
    this.geneIndex.clear();

    for (const gene of this.genePool.values()) {
      this.updateGeneIndex(gene);
    }
  }

  /**
   * Chargement du pool génétique
   */
  private async loadGenePool(): Promise<void> {
    // Implémentation du chargement depuis la base de données
    // Pour l'instant, initialisation vide
  }

  /**
   * Initialisation des gènes fondamentaux
   */
  private async initializeFundamentalGenes(): Promise<void> {
    const fundamentalGenes = [
      {
        type: 'algorithm' as GeneType,
        sequence: 'for (let i = 0; i < array.length; i++)',
        expression: 'iteration lineaire'
      },
      {
        type: 'algorithm' as GeneType,
        sequence: 'while (left <= right) { const mid = Math.floor((left + right) / 2); }',
        expression: 'recherche binaire'
      },
      {
        type: 'pattern' as GeneType,
        sequence: 'const memo = new Map(); if (memo.has(key)) return memo.get(key);',
        expression: 'memoization'
      }
    ];

    for (const geneData of fundamentalGenes) {
      await this.storeGene({
        operation: 'store',
        geneType: geneData.type,
        pattern: geneData.sequence,
        context: { fundamental: true }
      });
    }
  }
}

// Interfaces supplémentaires
interface GeneLineage {
  geneId: string;
  generation: number;
  parents: string[];
  children: string[];
  createdAt: Date;
  context: any;
}

interface GeneExpression {
  geneId: string;
  fitness: number;
  timestamp: Date;
  context: any;
}

interface CodePattern {
  code: string;
  type: GeneType;
  description: string;
}
