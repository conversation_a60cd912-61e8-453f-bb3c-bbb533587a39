import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { GeneticCode, GeneType } from '../types/evolution';

/**
 * Analyseur Phylogénétique pour l'Évolution Génétique
 * 
 * Trace et analyse l'évolution des gènes algorithmiques :
 * - Construction d'arbres phylogénétiques
 * - Analyse des lignées génétiques
 * - Métriques d'hérédité et de diversité
 * - Détection de convergence évolutionnaire
 * - Identification des gènes ancestraux
 */
export class PhylogeneticAnalyzer extends EventEmitter {
  private logger: Logger;
  private lineages: Map<string, GeneLineage> = new Map();
  private phylogeneticTrees: Map<string, PhylogeneticTree> = new Map();
  private evolutionHistory: EvolutionEvent[] = [];
  private ancestralGenes: Map<string, AncestralGene> = new Map();

  constructor(logger: Logger) {
    super();
    this.logger = logger;
  }

  /**
   * Initialise l'analyseur phylogénétique
   */
  async initialize(): Promise<void> {
    this.logger.info('🧬 Initialisation de l\'analyseur phylogénétique');
    
    await this.loadEvolutionHistory();
    await this.buildPhylogeneticTrees();
    await this.identifyAncestralGenes();
    
    this.logger.info(`✅ Analyseur phylogénétique initialisé: ${this.lineages.size} lignées`);
  }

  /**
   * Enregistre la création d'un nouveau gène
   */
  async recordGeneCreation(gene: GeneticCode, context: GeneCreationContext): Promise<void> {
    const lineage: GeneLineage = {
      geneId: gene.id,
      generation: this.calculateGeneration(context.parentIds || []),
      parentIds: context.parentIds || [],
      childrenIds: [],
      createdAt: new Date(),
      creationType: context.type,
      mutationEvents: [],
      fitnessHistory: [{ fitness: gene.fitness, timestamp: new Date() }],
      branchingFactor: 0
    };

    this.lineages.set(gene.id, lineage);

    // Mettre à jour les parents
    for (const parentId of lineage.parentIds) {
      const parentLineage = this.lineages.get(parentId);
      if (parentLineage) {
        parentLineage.childrenIds.push(gene.id);
        parentLineage.branchingFactor = parentLineage.childrenIds.length;
      }
    }

    // Enregistrer l'événement d'évolution
    const event: EvolutionEvent = {
      id: `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: context.type,
      geneId: gene.id,
      parentIds: lineage.parentIds,
      timestamp: new Date(),
      generation: lineage.generation,
      context
    };

    this.evolutionHistory.push(event);
    
    // Mettre à jour l'arbre phylogénétique
    await this.updatePhylogeneticTree(gene.type, lineage);

    this.logger.debug(`📊 Lignée enregistrée: ${gene.id} (génération ${lineage.generation})`);
    this.emit('lineage-created', lineage);
  }

  /**
   * Enregistre une mutation génétique
   */
  async recordMutation(geneId: string, mutation: MutationEvent): Promise<void> {
    const lineage = this.lineages.get(geneId);
    if (!lineage) {
      this.logger.warn(`Lignée non trouvée pour le gène: ${geneId}`);
      return;
    }

    lineage.mutationEvents.push(mutation);
    
    const event: EvolutionEvent = {
      id: `mutation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'mutation',
      geneId,
      parentIds: [geneId],
      timestamp: new Date(),
      generation: lineage.generation,
      context: { mutation }
    };

    this.evolutionHistory.push(event);
    
    this.logger.debug(`🧬 Mutation enregistrée: ${geneId} -> ${mutation.type}`);
    this.emit('mutation-recorded', { geneId, mutation });
  }

  /**
   * Met à jour l'historique de fitness d'un gène
   */
  async updateFitnessHistory(geneId: string, fitness: number): Promise<void> {
    const lineage = this.lineages.get(geneId);
    if (!lineage) return;

    lineage.fitnessHistory.push({
      fitness,
      timestamp: new Date()
    });

    // Limiter l'historique
    if (lineage.fitnessHistory.length > 100) {
      lineage.fitnessHistory = lineage.fitnessHistory.slice(-50);
    }
  }

  /**
   * Analyse la diversité génétique
   */
  analyzeDiversity(geneType?: GeneType): DiversityAnalysis {
    let targetLineages = Array.from(this.lineages.values());
    
    if (geneType) {
      const tree = this.phylogeneticTrees.get(geneType);
      if (tree) {
        targetLineages = targetLineages.filter(lineage => 
          tree.nodes.has(lineage.geneId)
        );
      }
    }

    const totalGenes = targetLineages.length;
    const generations = new Set(targetLineages.map(l => l.generation));
    const maxGeneration = Math.max(...Array.from(generations));
    
    // Diversité générationnelle
    const generationalDiversity = this.calculateGenerationalDiversity(targetLineages);
    
    // Diversité phylogénétique
    const phylogeneticDiversity = this.calculatePhylogeneticDiversity(targetLineages);
    
    // Taux de branchement
    const branchingRates = targetLineages.map(l => l.branchingFactor);
    const averageBranchingRate = branchingRates.reduce((a, b) => a + b, 0) / branchingRates.length;
    
    return {
      totalGenes,
      maxGeneration,
      generationalDiversity,
      phylogeneticDiversity,
      averageBranchingRate,
      extinctionRate: this.calculateExtinctionRate(targetLineages),
      convergenceIndex: this.calculateConvergenceIndex(targetLineages)
    };
  }

  /**
   * Trouve les gènes ancestraux communs
   */
  findCommonAncestors(geneIds: string[]): string[] {
    if (geneIds.length < 2) return [];

    const ancestorSets = geneIds.map(geneId => this.getAncestors(geneId));
    
    // Intersection de tous les ensembles d'ancêtres
    let commonAncestors = ancestorSets[0];
    for (let i = 1; i < ancestorSets.length; i++) {
      commonAncestors = commonAncestors.filter(ancestor => 
        ancestorSets[i].includes(ancestor)
      );
    }

    return commonAncestors;
  }

  /**
   * Trace le chemin évolutionnaire entre deux gènes
   */
  traceEvolutionaryPath(fromGeneId: string, toGeneId: string): EvolutionaryPath | null {
    const fromLineage = this.lineages.get(fromGeneId);
    const toLineage = this.lineages.get(toGeneId);
    
    if (!fromLineage || !toLineage) return null;

    // Trouver l'ancêtre commun le plus récent
    const commonAncestors = this.findCommonAncestors([fromGeneId, toGeneId]);
    if (commonAncestors.length === 0) return null;

    const recentCommonAncestor = this.findMostRecentCommonAncestor(commonAncestors);
    
    // Construire le chemin
    const pathFromAncestor = this.buildPathFromAncestor(recentCommonAncestor, fromGeneId);
    const pathToAncestor = this.buildPathFromAncestor(recentCommonAncestor, toGeneId);
    
    return {
      fromGeneId,
      toGeneId,
      commonAncestor: recentCommonAncestor,
      pathLength: pathFromAncestor.length + pathToAncestor.length,
      divergencePoint: recentCommonAncestor,
      evolutionaryDistance: this.calculateEvolutionaryDistance(fromGeneId, toGeneId),
      path: [...pathFromAncestor.reverse(), recentCommonAncestor, ...pathToAncestor]
    };
  }

  /**
   * Génère un rapport phylogénétique complet
   */
  generatePhylogeneticReport(geneType?: GeneType): PhylogeneticReport {
    const diversity = this.analyzeDiversity(geneType);
    const trees = geneType ? 
      [this.phylogeneticTrees.get(geneType)!].filter(Boolean) :
      Array.from(this.phylogeneticTrees.values());

    const ancestralGenes = Array.from(this.ancestralGenes.values())
      .filter(gene => !geneType || gene.type === geneType);

    const recentEvents = this.evolutionHistory
      .filter(event => !geneType || this.getGeneType(event.geneId) === geneType)
      .slice(-50);

    return {
      timestamp: new Date(),
      geneType,
      diversity,
      trees: trees.map(tree => this.summarizeTree(tree)),
      ancestralGenes,
      recentEvolutionEvents: recentEvents,
      recommendations: this.generateRecommendations(diversity)
    };
  }

  // Méthodes privées

  private calculateGeneration(parentIds: string[]): number {
    if (parentIds.length === 0) return 0;
    
    const parentGenerations = parentIds
      .map(id => this.lineages.get(id)?.generation || 0);
    
    return Math.max(...parentGenerations) + 1;
  }

  private async updatePhylogeneticTree(geneType: GeneType, lineage: GeneLineage): Promise<void> {
    if (!this.phylogeneticTrees.has(geneType)) {
      this.phylogeneticTrees.set(geneType, {
        id: `tree-${geneType}`,
        type: geneType,
        nodes: new Map(),
        edges: [],
        root: null,
        depth: 0,
        createdAt: new Date(),
        lastUpdated: new Date()
      });
    }

    const tree = this.phylogeneticTrees.get(geneType)!;
    
    // Ajouter le nœud
    tree.nodes.set(lineage.geneId, {
      geneId: lineage.geneId,
      generation: lineage.generation,
      parentIds: lineage.parentIds,
      childrenIds: lineage.childrenIds,
      branchLength: 1 // Simplifié
    });

    // Ajouter les arêtes
    for (const parentId of lineage.parentIds) {
      tree.edges.push({
        from: parentId,
        to: lineage.geneId,
        weight: 1,
        mutationType: 'inheritance'
      });
    }

    // Mettre à jour la profondeur
    tree.depth = Math.max(tree.depth, lineage.generation);
    tree.lastUpdated = new Date();

    // Définir la racine si nécessaire
    if (!tree.root && lineage.generation === 0) {
      tree.root = lineage.geneId;
    }
  }

  private calculateGenerationalDiversity(lineages: GeneLineage[]): number {
    const generationCounts = new Map<number, number>();
    
    for (const lineage of lineages) {
      const count = generationCounts.get(lineage.generation) || 0;
      generationCounts.set(lineage.generation, count + 1);
    }

    // Indice de Shannon pour la diversité générationnelle
    const total = lineages.length;
    let diversity = 0;
    
    for (const count of generationCounts.values()) {
      const proportion = count / total;
      diversity -= proportion * Math.log2(proportion);
    }

    return diversity;
  }

  private calculatePhylogeneticDiversity(lineages: GeneLineage[]): number {
    // Diversité basée sur la structure de l'arbre
    const uniqueBranches = new Set<string>();
    
    for (const lineage of lineages) {
      const branchSignature = lineage.parentIds.sort().join('-');
      uniqueBranches.add(branchSignature);
    }

    return uniqueBranches.size / lineages.length;
  }

  private calculateExtinctionRate(lineages: GeneLineage[]): number {
    const extinctLineages = lineages.filter(lineage => 
      lineage.childrenIds.length === 0 && 
      lineage.generation < Math.max(...lineages.map(l => l.generation)) - 2
    );

    return extinctLineages.length / lineages.length;
  }

  private calculateConvergenceIndex(lineages: GeneLineage[]): number {
    // Mesure la tendance à la convergence évolutionnaire
    const recentLineages = lineages.filter(lineage => 
      lineage.generation >= Math.max(...lineages.map(l => l.generation)) - 3
    );

    if (recentLineages.length === 0) return 0;

    const averageFitness = recentLineages.reduce((sum, lineage) => {
      const lastFitness = lineage.fitnessHistory[lineage.fitnessHistory.length - 1];
      return sum + (lastFitness?.fitness || 0);
    }, 0) / recentLineages.length;

    const fitnessVariance = recentLineages.reduce((sum, lineage) => {
      const lastFitness = lineage.fitnessHistory[lineage.fitnessHistory.length - 1];
      const fitness = lastFitness?.fitness || 0;
      return sum + Math.pow(fitness - averageFitness, 2);
    }, 0) / recentLineages.length;

    // Plus la variance est faible, plus la convergence est élevée
    return 1 / (1 + fitnessVariance);
  }

  private getAncestors(geneId: string): string[] {
    const ancestors: string[] = [];
    const visited = new Set<string>();
    const queue = [geneId];

    while (queue.length > 0) {
      const currentId = queue.shift()!;
      if (visited.has(currentId)) continue;
      
      visited.add(currentId);
      const lineage = this.lineages.get(currentId);
      
      if (lineage) {
        for (const parentId of lineage.parentIds) {
          ancestors.push(parentId);
          queue.push(parentId);
        }
      }
    }

    return ancestors;
  }

  private findMostRecentCommonAncestor(ancestors: string[]): string {
    // Trouve l'ancêtre avec la génération la plus élevée
    let mostRecent = ancestors[0];
    let maxGeneration = -1;

    for (const ancestorId of ancestors) {
      const lineage = this.lineages.get(ancestorId);
      if (lineage && lineage.generation > maxGeneration) {
        maxGeneration = lineage.generation;
        mostRecent = ancestorId;
      }
    }

    return mostRecent;
  }

  private buildPathFromAncestor(ancestorId: string, targetId: string): string[] {
    const path: string[] = [];
    let currentId = targetId;

    while (currentId !== ancestorId) {
      const lineage = this.lineages.get(currentId);
      if (!lineage || lineage.parentIds.length === 0) break;
      
      path.push(currentId);
      // Prendre le premier parent (simplification)
      currentId = lineage.parentIds[0];
    }

    return path;
  }

  private calculateEvolutionaryDistance(geneId1: string, geneId2: string): number {
    const lineage1 = this.lineages.get(geneId1);
    const lineage2 = this.lineages.get(geneId2);
    
    if (!lineage1 || !lineage2) return Infinity;

    // Distance basée sur la différence de génération et la fitness
    const generationDistance = Math.abs(lineage1.generation - lineage2.generation);
    
    const fitness1 = lineage1.fitnessHistory[lineage1.fitnessHistory.length - 1]?.fitness || 0;
    const fitness2 = lineage2.fitnessHistory[lineage2.fitnessHistory.length - 1]?.fitness || 0;
    const fitnessDistance = Math.abs(fitness1 - fitness2);

    return generationDistance + fitnessDistance;
  }

  private summarizeTree(tree: PhylogeneticTree): TreeSummary {
    return {
      id: tree.id,
      type: tree.type,
      nodeCount: tree.nodes.size,
      edgeCount: tree.edges.length,
      depth: tree.depth,
      root: tree.root,
      createdAt: tree.createdAt,
      lastUpdated: tree.lastUpdated
    };
  }

  private getGeneType(geneId: string): GeneType | null {
    // Cette méthode devrait être implémentée pour récupérer le type d'un gène
    // Pour l'instant, retourne null
    return null;
  }

  private generateRecommendations(diversity: DiversityAnalysis): string[] {
    const recommendations: string[] = [];

    if (diversity.phylogeneticDiversity < 0.3) {
      recommendations.push("Diversité phylogénétique faible - Encourager plus de mutations créatives");
    }

    if (diversity.averageBranchingRate < 1.5) {
      recommendations.push("Taux de branchement faible - Augmenter les croisements génétiques");
    }

    if (diversity.extinctionRate > 0.5) {
      recommendations.push("Taux d'extinction élevé - Améliorer la sélection des gènes parents");
    }

    if (diversity.convergenceIndex > 0.8) {
      recommendations.push("Convergence élevée détectée - Introduire plus de diversité");
    }

    return recommendations;
  }

  private async loadEvolutionHistory(): Promise<void> {
    // À implémenter : chargement de l'historique depuis le stockage
  }

  private async buildPhylogeneticTrees(): Promise<void> {
    // À implémenter : reconstruction des arbres depuis les lignées
  }

  private async identifyAncestralGenes(): Promise<void> {
    // À implémenter : identification des gènes ancestraux importants
  }
}

// Interfaces

export interface GeneLineage {
  geneId: string;
  generation: number;
  parentIds: string[];
  childrenIds: string[];
  createdAt: Date;
  creationType: 'creation' | 'mutation' | 'crossover' | 'recombination';
  mutationEvents: MutationEvent[];
  fitnessHistory: FitnessRecord[];
  branchingFactor: number;
}

export interface MutationEvent {
  id: string;
  type: 'point' | 'insertion' | 'deletion' | 'duplication' | 'inversion';
  description: string;
  impact: number;
  timestamp: Date;
  success: boolean;
}

export interface FitnessRecord {
  fitness: number;
  timestamp: Date;
}

export interface EvolutionEvent {
  id: string;
  type: 'creation' | 'mutation' | 'crossover' | 'recombination' | 'selection';
  geneId: string;
  parentIds: string[];
  timestamp: Date;
  generation: number;
  context: any;
}

export interface PhylogeneticTree {
  id: string;
  type: GeneType;
  nodes: Map<string, TreeNode>;
  edges: TreeEdge[];
  root: string | null;
  depth: number;
  createdAt: Date;
  lastUpdated: Date;
}

export interface TreeNode {
  geneId: string;
  generation: number;
  parentIds: string[];
  childrenIds: string[];
  branchLength: number;
}

export interface TreeEdge {
  from: string;
  to: string;
  weight: number;
  mutationType: string;
}

export interface AncestralGene {
  geneId: string;
  type: GeneType;
  descendantCount: number;
  influence: number;
  discoveredAt: Date;
}

export interface DiversityAnalysis {
  totalGenes: number;
  maxGeneration: number;
  generationalDiversity: number;
  phylogeneticDiversity: number;
  averageBranchingRate: number;
  extinctionRate: number;
  convergenceIndex: number;
}

export interface EvolutionaryPath {
  fromGeneId: string;
  toGeneId: string;
  commonAncestor: string;
  pathLength: number;
  divergencePoint: string;
  evolutionaryDistance: number;
  path: string[];
}

export interface PhylogeneticReport {
  timestamp: Date;
  geneType?: GeneType;
  diversity: DiversityAnalysis;
  trees: TreeSummary[];
  ancestralGenes: AncestralGene[];
  recentEvolutionEvents: EvolutionEvent[];
  recommendations: string[];
}

export interface TreeSummary {
  id: string;
  type: GeneType;
  nodeCount: number;
  edgeCount: number;
  depth: number;
  root: string | null;
  createdAt: Date;
  lastUpdated: Date;
}

export interface GeneCreationContext {
  type: 'creation' | 'mutation' | 'crossover' | 'recombination';
  parentIds?: string[];
  mutationDetails?: any;
  crossoverDetails?: any;
}
