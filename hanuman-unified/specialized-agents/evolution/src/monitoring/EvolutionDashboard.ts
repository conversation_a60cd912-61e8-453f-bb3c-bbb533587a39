import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { GeneticMemoryEngine } from '../engines/GeneticMemoryEngine';
import { PerformanceOptimizer } from '../optimization/PerformanceOptimizer';
import { EvolutionAPI } from '../api/EvolutionAPI';

/**
 * Dashboard Évolutionnaire pour Monitoring et Observabilité
 * 
 * Fournit une interface complète de monitoring :
 * - Visualisation des générations et convergence
 * - Métriques de performance en temps réel
 * - Graphiques de diversité génétique
 * - Alertes et notifications automatiques
 * - Rapports détaillés d'évolution
 */
export class EvolutionDashboard extends EventEmitter {
  private logger: Logger;
  private geneticMemoryEngine: GeneticMemoryEngine;
  private performanceOptimizer: PerformanceOptimizer;
  private evolutionAPI: EvolutionAPI;
  
  // Système d'alertes
  private alertSystem: AlertSystem;
  private anomalyDetector: AnomalyDetector;
  
  // Collecteurs de métriques
  private metricsCollector: MetricsCollector;
  private visualizationEngine: VisualizationEngine;
  
  // Configuration du dashboard
  private config: DashboardConfig = {
    updateInterval: 5000, // 5 secondes
    retentionPeriod: 86400000, // 24 heures
    alertThresholds: {
      convergenceStagnation: 50, // générations
      diversityLoss: 0.3, // seuil critique
      performanceDegradation: 0.2, // 20% de dégradation
      memoryUsage: 0.9 // 90% d'utilisation
    }
  };

  constructor(
    logger: Logger,
    geneticMemoryEngine: GeneticMemoryEngine,
    performanceOptimizer: PerformanceOptimizer,
    evolutionAPI: EvolutionAPI,
    config?: Partial<DashboardConfig>
  ) {
    super();
    this.logger = logger;
    this.geneticMemoryEngine = geneticMemoryEngine;
    this.performanceOptimizer = performanceOptimizer;
    this.evolutionAPI = evolutionAPI;
    
    if (config) {
      this.config = { ...this.config, ...config };
    }
    
    this.alertSystem = new AlertSystem(logger, this.config.alertThresholds);
    this.anomalyDetector = new AnomalyDetector(logger);
    this.metricsCollector = new MetricsCollector(logger, this.config.retentionPeriod);
    this.visualizationEngine = new VisualizationEngine(logger);
  }

  /**
   * Initialise le dashboard
   */
  async initialize(): Promise<void> {
    this.logger.info('📊 Initialisation du dashboard évolutionnaire');
    
    await this.alertSystem.initialize();
    await this.anomalyDetector.initialize();
    await this.metricsCollector.initialize();
    await this.visualizationEngine.initialize();
    
    // Démarrage de la collecte de métriques
    this.startMetricsCollection();
    
    // Configuration des alertes
    this.setupAlerts();
    
    this.logger.info('✅ Dashboard évolutionnaire initialisé');
  }

  /**
   * Obtient l'état global du système
   */
  async getSystemStatus(): Promise<SystemStatus> {
    const [
      geneticStats,
      performanceMetrics,
      apiMetrics,
      activeAlerts
    ] = await Promise.all([
      this.geneticMemoryEngine.getDatabaseStatistics(),
      this.performanceOptimizer.getPerformanceMetrics(),
      this.evolutionAPI.getMetrics(),
      this.alertSystem.getActiveAlerts()
    ]);

    const diversity = this.geneticMemoryEngine.analyzeDiversity();
    const systemHealth = this.calculateSystemHealth(performanceMetrics, diversity);

    return {
      timestamp: new Date(),
      health: systemHealth,
      genetic: {
        totalGenes: geneticStats.totalGenes,
        averageFitness: geneticStats.averageFitness,
        diversity: diversity.phylogeneticDiversity,
        convergenceIndex: diversity.convergenceIndex
      },
      performance: {
        workerUtilization: performanceMetrics.workerPool.utilization,
        cacheHitRate: performanceMetrics.cache.hitRate,
        memoryUsage: performanceMetrics.memory.usage,
        averageExecutionTime: performanceMetrics.overall.averageExecutionTime
      },
      api: {
        totalRequests: apiMetrics.totalRequests,
        activeRequests: apiMetrics.activeRequestsCount,
        queueLength: apiMetrics.queueLength,
        successRate: apiMetrics.completedRequests / (apiMetrics.completedRequests + apiMetrics.failedRequests)
      },
      alerts: {
        active: activeAlerts.length,
        critical: activeAlerts.filter(a => a.severity === 'critical').length,
        warnings: activeAlerts.filter(a => a.severity === 'warning').length
      }
    };
  }

  /**
   * Génère un rapport d'évolution détaillé
   */
  async generateEvolutionReport(timeRange?: TimeRange): Promise<EvolutionReport> {
    const range = timeRange || { start: new Date(Date.now() - 3600000), end: new Date() };
    
    const [
      phylogeneticReport,
      performanceHistory,
      convergenceAnalysis,
      diversityTrends
    ] = await Promise.all([
      this.geneticMemoryEngine.generatePhylogeneticReport(),
      this.metricsCollector.getPerformanceHistory(range),
      this.analyzeConvergence(range),
      this.analyzeDiversityTrends(range)
    ]);

    return {
      timestamp: new Date(),
      timeRange: range,
      phylogenetic: phylogeneticReport,
      performance: {
        history: performanceHistory,
        trends: this.calculatePerformanceTrends(performanceHistory),
        bottlenecks: this.identifyBottlenecks(performanceHistory)
      },
      convergence: convergenceAnalysis,
      diversity: diversityTrends,
      recommendations: this.generateRecommendations(phylogeneticReport, convergenceAnalysis, diversityTrends)
    };
  }

  /**
   * Obtient les métriques en temps réel
   */
  async getRealTimeMetrics(): Promise<RealTimeMetrics> {
    const currentTime = new Date();
    
    const [
      systemStatus,
      activeEvolutions,
      recentAlerts,
      performanceSnapshot
    ] = await Promise.all([
      this.getSystemStatus(),
      this.evolutionAPI.getActiveRequests(),
      this.alertSystem.getRecentAlerts(300000), // 5 minutes
      this.performanceOptimizer.getPerformanceMetrics()
    ]);

    return {
      timestamp: currentTime,
      system: systemStatus,
      evolutions: activeEvolutions.map(req => ({
        id: req.id,
        type: req.type,
        progress: req.progress,
        generation: req.currentGeneration,
        fitness: req.bestFitness,
        startedAt: req.startedAt
      })),
      alerts: recentAlerts,
      performance: performanceSnapshot
    };
  }

  /**
   * Génère des visualisations pour le dashboard
   */
  async generateVisualizations(): Promise<DashboardVisualizations> {
    const [
      convergenceChart,
      diversityChart,
      performanceChart,
      phylogeneticTree
    ] = await Promise.all([
      this.visualizationEngine.generateConvergenceChart(),
      this.visualizationEngine.generateDiversityChart(),
      this.visualizationEngine.generatePerformanceChart(),
      this.visualizationEngine.generatePhylogeneticTree()
    ]);

    return {
      convergence: convergenceChart,
      diversity: diversityChart,
      performance: performanceChart,
      phylogenetic: phylogeneticTree,
      generatedAt: new Date()
    };
  }

  /**
   * Configure une alerte personnalisée
   */
  async configureAlert(alertConfig: AlertConfiguration): Promise<string> {
    return await this.alertSystem.addCustomAlert(alertConfig);
  }

  /**
   * Exporte les données du dashboard
   */
  async exportData(format: 'json' | 'csv' | 'pdf', timeRange?: TimeRange): Promise<ExportResult> {
    const range = timeRange || { start: new Date(Date.now() - 86400000), end: new Date() };
    
    const data = await this.metricsCollector.exportData(range);
    
    switch (format) {
      case 'json':
        return { format, data: JSON.stringify(data, null, 2), filename: `evolution_data_${Date.now()}.json` };
      case 'csv':
        return { format, data: this.convertToCSV(data), filename: `evolution_data_${Date.now()}.csv` };
      case 'pdf':
        return { format, data: await this.generatePDFReport(data), filename: `evolution_report_${Date.now()}.pdf` };
      default:
        throw new Error(`Format d'export non supporté: ${format}`);
    }
  }

  // Méthodes privées

  /**
   * Démarre la collecte de métriques
   */
  private startMetricsCollection(): void {
    setInterval(async () => {
      try {
        await this.collectMetrics();
      } catch (error) {
        this.logger.error('Erreur lors de la collecte de métriques:', error);
      }
    }, this.config.updateInterval);
  }

  /**
   * Collecte les métriques actuelles
   */
  private async collectMetrics(): Promise<void> {
    const timestamp = new Date();
    
    const [
      systemStatus,
      performanceMetrics,
      diversity
    ] = await Promise.all([
      this.getSystemStatus(),
      this.performanceOptimizer.getPerformanceMetrics(),
      this.geneticMemoryEngine.analyzeDiversity()
    ]);

    // Stockage des métriques
    await this.metricsCollector.recordMetrics(timestamp, {
      system: systemStatus,
      performance: performanceMetrics,
      diversity
    });

    // Détection d'anomalies
    const anomalies = await this.anomalyDetector.detectAnomalies({
      timestamp,
      metrics: { system: systemStatus, performance: performanceMetrics, diversity }
    });

    // Déclenchement d'alertes si nécessaire
    for (const anomaly of anomalies) {
      await this.alertSystem.triggerAlert(anomaly);
    }

    // Émission d'événement pour les abonnés
    this.emit('metrics-updated', { timestamp, systemStatus, performanceMetrics, diversity });
  }

  /**
   * Configure les alertes système
   */
  private setupAlerts(): void {
    // Alerte de stagnation de convergence
    this.alertSystem.addRule({
      id: 'convergence-stagnation',
      name: 'Stagnation de Convergence',
      condition: (metrics) => {
        const generations = metrics.diversity?.maxGeneration || 0;
        return generations > this.config.alertThresholds.convergenceStagnation;
      },
      severity: 'warning',
      message: 'La convergence semble stagner depuis trop longtemps'
    });

    // Alerte de perte de diversité
    this.alertSystem.addRule({
      id: 'diversity-loss',
      name: 'Perte de Diversité Critique',
      condition: (metrics) => {
        const diversity = metrics.diversity?.phylogeneticDiversity || 1;
        return diversity < this.config.alertThresholds.diversityLoss;
      },
      severity: 'critical',
      message: 'La diversité génétique est dangereusement faible'
    });

    // Alerte de dégradation de performance
    this.alertSystem.addRule({
      id: 'performance-degradation',
      name: 'Dégradation de Performance',
      condition: (metrics) => {
        const utilization = metrics.performance?.workerPool?.utilization || 0;
        return utilization > 0.95;
      },
      severity: 'warning',
      message: 'Utilisation des ressources très élevée'
    });
  }

  /**
   * Calcule la santé globale du système
   */
  private calculateSystemHealth(performanceMetrics: any, diversity: any): SystemHealth {
    let score = 100;
    const issues: string[] = [];

    // Facteurs de performance
    if (performanceMetrics.memory.usage > 0.8) {
      score -= 20;
      issues.push('Utilisation mémoire élevée');
    }

    if (performanceMetrics.cache.hitRate < 0.5) {
      score -= 15;
      issues.push('Taux de cache faible');
    }

    // Facteurs de diversité
    if (diversity.phylogeneticDiversity < 0.4) {
      score -= 25;
      issues.push('Diversité génétique faible');
    }

    if (diversity.convergenceIndex > 0.9) {
      score -= 10;
      issues.push('Convergence excessive');
    }

    let status: 'healthy' | 'warning' | 'critical';
    if (score >= 80) status = 'healthy';
    else if (score >= 60) status = 'warning';
    else status = 'critical';

    return { status, score, issues };
  }

  /**
   * Analyse la convergence sur une période
   */
  private async analyzeConvergence(timeRange: TimeRange): Promise<ConvergenceAnalysis> {
    const history = await this.metricsCollector.getConvergenceHistory(timeRange);
    
    return {
      averageGenerations: history.reduce((sum, h) => sum + h.generations, 0) / history.length,
      convergenceRate: this.calculateConvergenceRate(history),
      stagnationPeriods: this.identifyStagnationPeriods(history),
      trends: this.calculateConvergenceTrends(history)
    };
  }

  /**
   * Analyse les tendances de diversité
   */
  private async analyzeDiversityTrends(timeRange: TimeRange): Promise<DiversityTrends> {
    const history = await this.metricsCollector.getDiversityHistory(timeRange);
    
    return {
      averageDiversity: history.reduce((sum, h) => sum + h.diversity, 0) / history.length,
      diversityTrend: this.calculateDiversityTrend(history),
      criticalPeriods: this.identifyCriticalDiversityPeriods(history),
      recoveryPatterns: this.identifyRecoveryPatterns(history)
    };
  }

  /**
   * Génère des recommandations basées sur l'analyse
   */
  private generateRecommendations(
    phylogeneticReport: any,
    convergenceAnalysis: ConvergenceAnalysis,
    diversityTrends: DiversityTrends
  ): string[] {
    const recommendations: string[] = [];

    if (convergenceAnalysis.stagnationPeriods.length > 0) {
      recommendations.push('Augmenter le taux de mutation pour éviter la stagnation');
    }

    if (diversityTrends.averageDiversity < 0.5) {
      recommendations.push('Introduire plus de diversité dans la population initiale');
    }

    if (phylogeneticReport.recommendations) {
      recommendations.push(...phylogeneticReport.recommendations);
    }

    return recommendations;
  }

  // Méthodes utilitaires

  private calculateConvergenceRate(history: any[]): number {
    // Implémentation simplifiée
    return history.length > 0 ? 1 / history[0].generations : 0;
  }

  private identifyStagnationPeriods(history: any[]): any[] {
    // Implémentation simplifiée
    return [];
  }

  private calculateConvergenceTrends(history: any[]): any {
    // Implémentation simplifiée
    return { trend: 'stable' };
  }

  private calculateDiversityTrend(history: any[]): string {
    // Implémentation simplifiée
    return 'stable';
  }

  private identifyCriticalDiversityPeriods(history: any[]): any[] {
    // Implémentation simplifiée
    return [];
  }

  private identifyRecoveryPatterns(history: any[]): any[] {
    // Implémentation simplifiée
    return [];
  }

  private calculatePerformanceTrends(history: any[]): any {
    // Implémentation simplifiée
    return { trend: 'improving' };
  }

  private identifyBottlenecks(history: any[]): string[] {
    // Implémentation simplifiée
    return [];
  }

  private convertToCSV(data: any): string {
    // Implémentation simplifiée de conversion CSV
    return JSON.stringify(data);
  }

  private async generatePDFReport(data: any): Promise<string> {
    // Implémentation simplifiée de génération PDF
    return 'PDF report data';
  }
}

// Classes auxiliaires

class AlertSystem {
  private logger: Logger;
  private thresholds: any;
  private activeAlerts: Alert[] = [];
  private alertRules: AlertRule[] = [];

  constructor(logger: Logger, thresholds: any) {
    this.logger = logger;
    this.thresholds = thresholds;
  }

  async initialize(): Promise<void> {
    this.logger.info('🚨 Système d\'alertes initialisé');
  }

  async addCustomAlert(config: AlertConfiguration): Promise<string> {
    const rule: AlertRule = {
      id: config.id || `alert_${Date.now()}`,
      name: config.name,
      condition: config.condition,
      severity: config.severity,
      message: config.message
    };
    
    this.alertRules.push(rule);
    return rule.id;
  }

  addRule(rule: AlertRule): void {
    this.alertRules.push(rule);
  }

  async triggerAlert(anomaly: any): Promise<void> {
    const alert: Alert = {
      id: `alert_${Date.now()}`,
      type: anomaly.type,
      severity: anomaly.severity,
      message: anomaly.message,
      timestamp: new Date(),
      acknowledged: false
    };
    
    this.activeAlerts.push(alert);
    this.logger.warn(`🚨 Alerte déclenchée: ${alert.message}`);
  }

  getActiveAlerts(): Alert[] {
    return this.activeAlerts.filter(alert => !alert.acknowledged);
  }

  getRecentAlerts(timeWindow: number): Alert[] {
    const cutoff = new Date(Date.now() - timeWindow);
    return this.activeAlerts.filter(alert => alert.timestamp > cutoff);
  }
}

class AnomalyDetector {
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    this.logger.info('🔍 Détecteur d\'anomalies initialisé');
  }

  async detectAnomalies(data: any): Promise<any[]> {
    // Implémentation simplifiée de détection d'anomalies
    return [];
  }
}

class MetricsCollector {
  private logger: Logger;
  private retentionPeriod: number;
  private metricsHistory: any[] = [];

  constructor(logger: Logger, retentionPeriod: number) {
    this.logger = logger;
    this.retentionPeriod = retentionPeriod;
  }

  async initialize(): Promise<void> {
    this.logger.info('📊 Collecteur de métriques initialisé');
  }

  async recordMetrics(timestamp: Date, metrics: any): Promise<void> {
    this.metricsHistory.push({ timestamp, ...metrics });
    
    // Nettoyage des anciennes métriques
    const cutoff = new Date(Date.now() - this.retentionPeriod);
    this.metricsHistory = this.metricsHistory.filter(m => m.timestamp > cutoff);
  }

  async getPerformanceHistory(timeRange: TimeRange): Promise<any[]> {
    return this.metricsHistory.filter(m => 
      m.timestamp >= timeRange.start && m.timestamp <= timeRange.end
    );
  }

  async getConvergenceHistory(timeRange: TimeRange): Promise<any[]> {
    return this.getPerformanceHistory(timeRange);
  }

  async getDiversityHistory(timeRange: TimeRange): Promise<any[]> {
    return this.getPerformanceHistory(timeRange);
  }

  async exportData(timeRange: TimeRange): Promise<any> {
    return this.getPerformanceHistory(timeRange);
  }
}

class VisualizationEngine {
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    this.logger.info('📈 Moteur de visualisation initialisé');
  }

  async generateConvergenceChart(): Promise<ChartData> {
    return { type: 'line', data: [], options: {} };
  }

  async generateDiversityChart(): Promise<ChartData> {
    return { type: 'area', data: [], options: {} };
  }

  async generatePerformanceChart(): Promise<ChartData> {
    return { type: 'bar', data: [], options: {} };
  }

  async generatePhylogeneticTree(): Promise<TreeVisualization> {
    return { nodes: [], edges: [], layout: 'hierarchical' };
  }
}

// Interfaces

export interface DashboardConfig {
  updateInterval: number;
  retentionPeriod: number;
  alertThresholds: {
    convergenceStagnation: number;
    diversityLoss: number;
    performanceDegradation: number;
    memoryUsage: number;
  };
}

export interface SystemStatus {
  timestamp: Date;
  health: SystemHealth;
  genetic: any;
  performance: any;
  api: any;
  alerts: any;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  score: number;
  issues: string[];
}

export interface EvolutionReport {
  timestamp: Date;
  timeRange: TimeRange;
  phylogenetic: any;
  performance: any;
  convergence: ConvergenceAnalysis;
  diversity: DiversityTrends;
  recommendations: string[];
}

export interface RealTimeMetrics {
  timestamp: Date;
  system: SystemStatus;
  evolutions: any[];
  alerts: Alert[];
  performance: any;
}

export interface DashboardVisualizations {
  convergence: ChartData;
  diversity: ChartData;
  performance: ChartData;
  phylogenetic: TreeVisualization;
  generatedAt: Date;
}

export interface TimeRange {
  start: Date;
  end: Date;
}

export interface Alert {
  id: string;
  type: string;
  severity: 'info' | 'warning' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: (metrics: any) => boolean;
  severity: 'info' | 'warning' | 'critical';
  message: string;
}

export interface AlertConfiguration {
  id?: string;
  name: string;
  condition: (metrics: any) => boolean;
  severity: 'info' | 'warning' | 'critical';
  message: string;
}

export interface ConvergenceAnalysis {
  averageGenerations: number;
  convergenceRate: number;
  stagnationPeriods: any[];
  trends: any;
}

export interface DiversityTrends {
  averageDiversity: number;
  diversityTrend: string;
  criticalPeriods: any[];
  recoveryPatterns: any[];
}

export interface ExportResult {
  format: string;
  data: string;
  filename: string;
}

export interface ChartData {
  type: string;
  data: any[];
  options: any;
}

export interface TreeVisualization {
  nodes: any[];
  edges: any[];
  layout: string;
}
