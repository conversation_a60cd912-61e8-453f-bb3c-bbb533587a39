import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { Worker } from 'worker_threads';
import { EvolutionSolution, FitnessMetrics } from '../types/evolution';

/**
 * Optimiseur de Performance pour l'Agent Évolution
 * 
 * Implémente la parallélisation et l'optimisation des performances :
 * - Pool de workers pour l'évaluation parallèle
 * - Cache intelligent des évaluations
 * - Distribution des calculs de fitness
 * - Optimisation mémoire et CPU
 */
export class PerformanceOptimizer extends EventEmitter {
  private logger: Logger;
  private workerPool: WorkerPool;
  private evaluationCache: EvaluationCache;
  private memoryOptimizer: MemoryOptimizer;
  private performanceMonitor: PerformanceMonitor;
  
  // Configuration
  private config: PerformanceConfig = {
    maxWorkers: 4,
    cacheSize: 1000,
    memoryThreshold: 0.8,
    enableProfiling: true
  };

  constructor(logger: Logger, config?: Partial<PerformanceConfig>) {
    super();
    this.logger = logger;
    
    if (config) {
      this.config = { ...this.config, ...config };
    }
    
    this.workerPool = new WorkerPool(logger, this.config.maxWorkers);
    this.evaluationCache = new EvaluationCache(logger, this.config.cacheSize);
    this.memoryOptimizer = new MemoryOptimizer(logger, this.config.memoryThreshold);
    this.performanceMonitor = new PerformanceMonitor(logger);
  }

  /**
   * Initialise l'optimiseur de performance
   */
  async initialize(): Promise<void> {
    this.logger.info('⚡ Initialisation de l\'optimiseur de performance');
    
    await this.workerPool.initialize();
    await this.evaluationCache.initialize();
    await this.memoryOptimizer.initialize();
    await this.performanceMonitor.initialize();
    
    this.logger.info(`✅ Optimiseur initialisé: ${this.config.maxWorkers} workers, cache ${this.config.cacheSize}`);
  }

  /**
   * Évalue une population de solutions en parallèle
   */
  async evaluatePopulation(
    solutions: EvolutionSolution[], 
    evaluationFunction: EvaluationFunction
  ): Promise<EvaluationResult[]> {
    const startTime = Date.now();
    this.logger.debug(`🔄 Évaluation parallèle de ${solutions.length} solutions`);
    
    // Vérification du cache
    const { cached, toEvaluate } = await this.evaluationCache.checkCache(solutions);
    this.logger.debug(`📊 Cache: ${cached.length} trouvées, ${toEvaluate.length} à évaluer`);
    
    // Optimisation mémoire avant évaluation
    await this.memoryOptimizer.optimizeBeforeEvaluation(solutions.length);
    
    // Évaluation parallèle des solutions non cachées
    const newEvaluations = await this.parallelEvaluate(toEvaluate, evaluationFunction);
    
    // Mise en cache des nouveaux résultats
    await this.evaluationCache.cacheResults(newEvaluations);
    
    // Combinaison des résultats
    const allResults = [...cached, ...newEvaluations];
    
    // Monitoring des performances
    const executionTime = Date.now() - startTime;
    this.performanceMonitor.recordEvaluation(solutions.length, executionTime);
    
    this.logger.debug(`✅ Évaluation terminée en ${executionTime}ms`);
    
    return allResults;
  }

  /**
   * Optimise une solution individuellement
   */
  async optimizeSolution(
    solution: EvolutionSolution,
    optimizationStrategy: OptimizationStrategy
  ): Promise<EvolutionSolution> {
    const startTime = Date.now();
    
    // Vérification du cache d'optimisation
    const cacheKey = this.generateOptimizationCacheKey(solution, optimizationStrategy);
    const cached = await this.evaluationCache.getCachedOptimization(cacheKey);
    
    if (cached) {
      this.logger.debug(`📊 Optimisation trouvée en cache: ${solution.id}`);
      return cached;
    }
    
    // Optimisation via worker dédié
    const optimizedSolution = await this.workerPool.optimizeSolution(solution, optimizationStrategy);
    
    // Mise en cache du résultat
    await this.evaluationCache.cacheOptimization(cacheKey, optimizedSolution);
    
    const executionTime = Date.now() - startTime;
    this.logger.debug(`⚡ Solution optimisée en ${executionTime}ms: ${solution.id}`);
    
    return optimizedSolution;
  }

  /**
   * Obtient les métriques de performance
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return {
      workerPool: this.workerPool.getMetrics(),
      cache: this.evaluationCache.getMetrics(),
      memory: this.memoryOptimizer.getMetrics(),
      overall: this.performanceMonitor.getMetrics()
    };
  }

  /**
   * Optimise la configuration dynamiquement
   */
  async optimizeConfiguration(): Promise<void> {
    const metrics = this.getPerformanceMetrics();
    
    // Ajustement du nombre de workers
    if (metrics.workerPool.utilization > 0.9 && this.config.maxWorkers < 8) {
      await this.workerPool.addWorker();
      this.config.maxWorkers++;
      this.logger.info(`⚡ Worker ajouté: ${this.config.maxWorkers} workers actifs`);
    }
    
    // Ajustement de la taille du cache
    if (metrics.cache.hitRate < 0.5 && this.config.cacheSize < 2000) {
      this.config.cacheSize = Math.min(this.config.cacheSize * 1.5, 2000);
      await this.evaluationCache.resize(this.config.cacheSize);
      this.logger.info(`📊 Cache étendu: ${this.config.cacheSize} entrées`);
    }
    
    // Nettoyage mémoire si nécessaire
    if (metrics.memory.usage > this.config.memoryThreshold) {
      await this.memoryOptimizer.forceCleanup();
      this.logger.info('🧹 Nettoyage mémoire forcé');
    }
  }

  /**
   * Arrêt propre de l'optimiseur
   */
  async shutdown(): Promise<void> {
    this.logger.info('🛑 Arrêt de l\'optimiseur de performance');
    
    await this.workerPool.shutdown();
    await this.evaluationCache.shutdown();
    await this.memoryOptimizer.shutdown();
    await this.performanceMonitor.shutdown();
    
    this.logger.info('✅ Optimiseur arrêté proprement');
  }

  // Méthodes privées

  /**
   * Évaluation parallèle des solutions
   */
  private async parallelEvaluate(
    solutions: EvolutionSolution[],
    evaluationFunction: EvaluationFunction
  ): Promise<EvaluationResult[]> {
    if (solutions.length === 0) return [];
    
    // Division en batches pour les workers
    const batchSize = Math.ceil(solutions.length / this.config.maxWorkers);
    const batches = this.chunkArray(solutions, batchSize);
    
    // Évaluation parallèle par batch
    const batchPromises = batches.map(batch => 
      this.workerPool.evaluateBatch(batch, evaluationFunction)
    );
    
    const batchResults = await Promise.all(batchPromises);
    
    // Aplatissement des résultats
    return batchResults.flat();
  }

  /**
   * Génère une clé de cache pour l'optimisation
   */
  private generateOptimizationCacheKey(
    solution: EvolutionSolution,
    strategy: OptimizationStrategy
  ): string {
    const solutionHash = this.hashSolution(solution);
    const strategyHash = this.hashStrategy(strategy);
    return `opt_${solutionHash}_${strategyHash}`;
  }

  /**
   * Hash d'une solution pour le cache
   */
  private hashSolution(solution: EvolutionSolution): string {
    const content = solution.code + solution.approach + JSON.stringify(solution.fitness);
    return this.simpleHash(content);
  }

  /**
   * Hash d'une stratégie d'optimisation
   */
  private hashStrategy(strategy: OptimizationStrategy): string {
    return this.simpleHash(JSON.stringify(strategy));
  }

  /**
   * Hash simple pour le cache
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Divise un tableau en chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}

/**
 * Pool de workers pour l'évaluation parallèle
 */
class WorkerPool {
  private logger: Logger;
  private workers: EvolutionWorker[] = [];
  private maxWorkers: number;
  private activeJobs: Map<string, WorkerJob> = new Map();
  private jobQueue: WorkerJob[] = [];

  constructor(logger: Logger, maxWorkers: number) {
    this.logger = logger;
    this.maxWorkers = maxWorkers;
  }

  async initialize(): Promise<void> {
    this.logger.info(`🔧 Initialisation du pool de ${this.maxWorkers} workers`);
    
    for (let i = 0; i < this.maxWorkers; i++) {
      const worker = new EvolutionWorker(i, this.logger);
      await worker.initialize();
      this.workers.push(worker);
    }
    
    this.startJobProcessor();
  }

  async evaluateBatch(
    solutions: EvolutionSolution[],
    evaluationFunction: EvaluationFunction
  ): Promise<EvaluationResult[]> {
    return new Promise((resolve, reject) => {
      const job: WorkerJob = {
        id: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'evaluate_batch',
        data: { solutions, evaluationFunction },
        resolve,
        reject,
        createdAt: new Date()
      };
      
      this.jobQueue.push(job);
    });
  }

  async optimizeSolution(
    solution: EvolutionSolution,
    strategy: OptimizationStrategy
  ): Promise<EvolutionSolution> {
    return new Promise((resolve, reject) => {
      const job: WorkerJob = {
        id: `optimize_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'optimize_solution',
        data: { solution, strategy },
        resolve,
        reject,
        createdAt: new Date()
      };
      
      this.jobQueue.push(job);
    });
  }

  async addWorker(): Promise<void> {
    const workerId = this.workers.length;
    const worker = new EvolutionWorker(workerId, this.logger);
    await worker.initialize();
    this.workers.push(worker);
    this.maxWorkers++;
  }

  getMetrics(): WorkerPoolMetrics {
    const activeWorkers = this.workers.filter(w => w.isBusy()).length;
    
    return {
      totalWorkers: this.workers.length,
      activeWorkers,
      utilization: activeWorkers / this.workers.length,
      queueLength: this.jobQueue.length,
      completedJobs: this.workers.reduce((sum, w) => sum + w.getCompletedJobs(), 0)
    };
  }

  async shutdown(): Promise<void> {
    await Promise.all(this.workers.map(worker => worker.shutdown()));
    this.workers = [];
  }

  private startJobProcessor(): void {
    setInterval(() => {
      this.processJobs();
    }, 100); // Vérification toutes les 100ms
  }

  private processJobs(): void {
    if (this.jobQueue.length === 0) return;
    
    const availableWorker = this.workers.find(w => !w.isBusy());
    if (!availableWorker) return;
    
    const job = this.jobQueue.shift()!;
    this.activeJobs.set(job.id, job);
    
    availableWorker.executeJob(job).then(result => {
      job.resolve(result);
      this.activeJobs.delete(job.id);
    }).catch(error => {
      job.reject(error);
      this.activeJobs.delete(job.id);
    });
  }
}

/**
 * Worker individuel pour l'évaluation
 */
class EvolutionWorker {
  private id: number;
  private logger: Logger;
  private worker: Worker | null = null;
  private busy: boolean = false;
  private completedJobs: number = 0;

  constructor(id: number, logger: Logger) {
    this.id = id;
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    // Pour cette démonstration, nous simulons les workers
    // En production, ceci créerait un vrai Worker Thread
    this.logger.debug(`🔧 Worker ${this.id} initialisé`);
  }

  async executeJob(job: WorkerJob): Promise<any> {
    this.busy = true;
    
    try {
      // Simulation de l'exécution du job
      await this.simulateJobExecution(job);
      
      let result;
      switch (job.type) {
        case 'evaluate_batch':
          result = await this.evaluateBatch(job.data.solutions, job.data.evaluationFunction);
          break;
        case 'optimize_solution':
          result = await this.optimizeSolution(job.data.solution, job.data.strategy);
          break;
        default:
          throw new Error(`Type de job non supporté: ${job.type}`);
      }
      
      this.completedJobs++;
      return result;
      
    } finally {
      this.busy = false;
    }
  }

  isBusy(): boolean {
    return this.busy;
  }

  getCompletedJobs(): number {
    return this.completedJobs;
  }

  async shutdown(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate();
    }
  }

  private async simulateJobExecution(job: WorkerJob): Promise<void> {
    // Simulation du temps d'exécution
    const executionTime = Math.random() * 100 + 50; // 50-150ms
    await new Promise(resolve => setTimeout(resolve, executionTime));
  }

  private async evaluateBatch(
    solutions: EvolutionSolution[],
    evaluationFunction: EvaluationFunction
  ): Promise<EvaluationResult[]> {
    // Simulation de l'évaluation
    return solutions.map(solution => ({
      solutionId: solution.id,
      fitness: {
        total: Math.random(),
        performance: Math.random(),
        correctness: Math.random(),
        efficiency: Math.random(),
        robustness: Math.random(),
        maintainability: Math.random(),
        innovation: Math.random()
      },
      executionTime: Math.random() * 100,
      memoryUsage: Math.random() * 1024
    }));
  }

  private async optimizeSolution(
    solution: EvolutionSolution,
    strategy: OptimizationStrategy
  ): Promise<EvolutionSolution> {
    // Simulation de l'optimisation
    return {
      ...solution,
      fitness: {
        ...solution.fitness,
        total: Math.min(1, solution.fitness.total + 0.1)
      }
    };
  }
}

/**
 * Cache intelligent pour les évaluations
 */
class EvaluationCache {
  private logger: Logger;
  private cache: Map<string, CacheEntry> = new Map();
  private maxSize: number;
  private hits: number = 0;
  private misses: number = 0;

  constructor(logger: Logger, maxSize: number) {
    this.logger = logger;
    this.maxSize = maxSize;
  }

  async initialize(): Promise<void> {
    this.logger.info(`📊 Initialisation du cache d'évaluation (${this.maxSize} entrées)`);
  }

  async checkCache(solutions: EvolutionSolution[]): Promise<{
    cached: EvaluationResult[];
    toEvaluate: EvolutionSolution[];
  }> {
    const cached: EvaluationResult[] = [];
    const toEvaluate: EvolutionSolution[] = [];
    
    for (const solution of solutions) {
      const cacheKey = this.generateCacheKey(solution);
      const entry = this.cache.get(cacheKey);
      
      if (entry && !this.isExpired(entry)) {
        cached.push(entry.result);
        this.hits++;
      } else {
        toEvaluate.push(solution);
        this.misses++;
      }
    }
    
    return { cached, toEvaluate };
  }

  async cacheResults(results: EvaluationResult[]): Promise<void> {
    for (const result of results) {
      const cacheKey = this.generateResultCacheKey(result);
      
      // Éviction LRU si nécessaire
      if (this.cache.size >= this.maxSize) {
        this.evictLRU();
      }
      
      this.cache.set(cacheKey, {
        result,
        timestamp: new Date(),
        accessCount: 1
      });
    }
  }

  async getCachedOptimization(cacheKey: string): Promise<EvolutionSolution | null> {
    const entry = this.cache.get(cacheKey);
    if (entry && !this.isExpired(entry)) {
      entry.accessCount++;
      return entry.result as EvolutionSolution;
    }
    return null;
  }

  async cacheOptimization(cacheKey: string, solution: EvolutionSolution): Promise<void> {
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }
    
    this.cache.set(cacheKey, {
      result: solution,
      timestamp: new Date(),
      accessCount: 1
    });
  }

  async resize(newSize: number): Promise<void> {
    this.maxSize = newSize;
    
    // Éviction si nécessaire
    while (this.cache.size > this.maxSize) {
      this.evictLRU();
    }
  }

  getMetrics(): CacheMetrics {
    const total = this.hits + this.misses;
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: total > 0 ? this.hits / total : 0,
      hits: this.hits,
      misses: this.misses
    };
  }

  async shutdown(): Promise<void> {
    this.cache.clear();
  }

  private generateCacheKey(solution: EvolutionSolution): string {
    return `eval_${this.hashSolution(solution)}`;
  }

  private generateResultCacheKey(result: EvaluationResult): string {
    return `eval_${result.solutionId}`;
  }

  private hashSolution(solution: EvolutionSolution): string {
    const content = solution.code + solution.approach;
    return this.simpleHash(content);
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString(36);
  }

  private isExpired(entry: CacheEntry): boolean {
    const maxAge = 3600000; // 1 heure
    return Date.now() - entry.timestamp.getTime() > maxAge;
  }

  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();
    
    for (const [key, entry] of this.cache) {
      if (entry.timestamp.getTime() < oldestTime) {
        oldestTime = entry.timestamp.getTime();
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }
}

/**
 * Optimiseur mémoire
 */
class MemoryOptimizer {
  private logger: Logger;
  private threshold: number;
  private lastCleanup: Date = new Date();

  constructor(logger: Logger, threshold: number) {
    this.logger = logger;
    this.threshold = threshold;
  }

  async initialize(): Promise<void> {
    this.logger.info(`🧹 Optimiseur mémoire initialisé (seuil: ${this.threshold * 100}%)`);
  }

  async optimizeBeforeEvaluation(populationSize: number): Promise<void> {
    const usage = this.getMemoryUsage();
    
    if (usage > this.threshold) {
      await this.performCleanup();
    }
  }

  async forceCleanup(): Promise<void> {
    await this.performCleanup();
  }

  getMetrics(): MemoryMetrics {
    return {
      usage: this.getMemoryUsage(),
      threshold: this.threshold,
      lastCleanup: this.lastCleanup
    };
  }

  async shutdown(): Promise<void> {
    // Nettoyage final
    await this.performCleanup();
  }

  private getMemoryUsage(): number {
    const used = process.memoryUsage();
    const total = used.heapTotal;
    return used.heapUsed / total;
  }

  private async performCleanup(): Promise<void> {
    this.logger.debug('🧹 Nettoyage mémoire en cours...');
    
    // Force garbage collection si disponible
    if (global.gc) {
      global.gc();
    }
    
    this.lastCleanup = new Date();
  }
}

/**
 * Moniteur de performance
 */
class PerformanceMonitor {
  private logger: Logger;
  private evaluationHistory: EvaluationRecord[] = [];
  private maxHistorySize: number = 1000;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    this.logger.info('📊 Moniteur de performance initialisé');
  }

  recordEvaluation(populationSize: number, executionTime: number): void {
    const record: EvaluationRecord = {
      timestamp: new Date(),
      populationSize,
      executionTime,
      throughput: populationSize / executionTime
    };
    
    this.evaluationHistory.push(record);
    
    // Limitation de l'historique
    if (this.evaluationHistory.length > this.maxHistorySize) {
      this.evaluationHistory.shift();
    }
  }

  getMetrics(): OverallMetrics {
    if (this.evaluationHistory.length === 0) {
      return {
        averageExecutionTime: 0,
        averageThroughput: 0,
        totalEvaluations: 0
      };
    }
    
    const totalTime = this.evaluationHistory.reduce((sum, r) => sum + r.executionTime, 0);
    const totalThroughput = this.evaluationHistory.reduce((sum, r) => sum + r.throughput, 0);
    
    return {
      averageExecutionTime: totalTime / this.evaluationHistory.length,
      averageThroughput: totalThroughput / this.evaluationHistory.length,
      totalEvaluations: this.evaluationHistory.length
    };
  }

  async shutdown(): Promise<void> {
    this.evaluationHistory = [];
  }
}

// Interfaces et types

export interface PerformanceConfig {
  maxWorkers: number;
  cacheSize: number;
  memoryThreshold: number;
  enableProfiling: boolean;
}

export interface EvaluationFunction {
  (solution: EvolutionSolution): Promise<FitnessMetrics>;
}

export interface OptimizationStrategy {
  type: 'performance' | 'memory' | 'quality' | 'hybrid';
  parameters: Record<string, any>;
}

export interface EvaluationResult {
  solutionId: string;
  fitness: FitnessMetrics;
  executionTime: number;
  memoryUsage: number;
}

export interface WorkerJob {
  id: string;
  type: 'evaluate_batch' | 'optimize_solution';
  data: any;
  resolve: (result: any) => void;
  reject: (error: any) => void;
  createdAt: Date;
}

export interface PerformanceMetrics {
  workerPool: WorkerPoolMetrics;
  cache: CacheMetrics;
  memory: MemoryMetrics;
  overall: OverallMetrics;
}

export interface WorkerPoolMetrics {
  totalWorkers: number;
  activeWorkers: number;
  utilization: number;
  queueLength: number;
  completedJobs: number;
}

export interface CacheMetrics {
  size: number;
  maxSize: number;
  hitRate: number;
  hits: number;
  misses: number;
}

export interface MemoryMetrics {
  usage: number;
  threshold: number;
  lastCleanup: Date;
}

export interface OverallMetrics {
  averageExecutionTime: number;
  averageThroughput: number;
  totalEvaluations: number;
}

interface CacheEntry {
  result: any;
  timestamp: Date;
  accessCount: number;
}

interface EvaluationRecord {
  timestamp: Date;
  populationSize: number;
  executionTime: number;
  throughput: number;
}
