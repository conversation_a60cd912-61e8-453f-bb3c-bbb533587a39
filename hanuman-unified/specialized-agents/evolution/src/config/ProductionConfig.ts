import { Logger } from 'winston';
import { OrchestratorConfig } from '../orchestration/EvolutionOrchestrator';
import { PerformanceConfig } from '../optimization/PerformanceOptimizer';
import { DashboardConfig } from '../monitoring/EvolutionDashboard';

/**
 * Configuration de Production Optimisée
 * 
 * Configuration complète pour le déploiement en production :
 * - Paramètres optimisés pour la performance
 * - Configuration de sécurité renforcée
 * - Monitoring et alertes avancés
 * - Scaling automatique
 * - Résilience et recovery
 */
export class ProductionConfig {
  private static instance: ProductionConfig;
  private environment: 'development' | 'staging' | 'production';
  private logger: Logger;

  private constructor(environment: string, logger: Logger) {
    this.environment = environment as any;
    this.logger = logger;
  }

  static getInstance(environment: string = 'production', logger: Logger): ProductionConfig {
    if (!ProductionConfig.instance) {
      ProductionConfig.instance = new ProductionConfig(environment, logger);
    }
    return ProductionConfig.instance;
  }

  /**
   * Configuration de l'orchestrateur pour la production
   */
  getOrchestratorConfig(): OrchestratorConfig {
    const baseConfig: OrchestratorConfig = {
      maxConcurrentEvolutions: 50,
      resourceAllocationStrategy: 'adaptive',
      priorityWeights: {
        CRITICAL: 1000,
        HIGH: 750,
        MEDIUM: 500,
        LOW: 250
      },
      adaptiveThresholds: {
        cpuUtilization: 0.8,
        memoryUsage: 0.85,
        queueLength: 100
      }
    };

    // Ajustements par environnement
    switch (this.environment) {
      case 'development':
        return {
          ...baseConfig,
          maxConcurrentEvolutions: 10,
          adaptiveThresholds: {
            ...baseConfig.adaptiveThresholds,
            queueLength: 20
          }
        };

      case 'staging':
        return {
          ...baseConfig,
          maxConcurrentEvolutions: 25,
          adaptiveThresholds: {
            ...baseConfig.adaptiveThresholds,
            queueLength: 50
          }
        };

      case 'production':
      default:
        return {
          ...baseConfig,
          // Configuration haute performance pour production
          maxConcurrentEvolutions: 100,
          adaptiveThresholds: {
            cpuUtilization: 0.75, // Plus conservateur en production
            memoryUsage: 0.8,
            queueLength: 200
          }
        };
    }
  }

  /**
   * Configuration de l'optimiseur de performance
   */
  getPerformanceConfig(): PerformanceConfig {
    const baseConfig: PerformanceConfig = {
      maxWorkers: 16,
      cacheSize: 5000,
      memoryThreshold: 0.8,
      enableProfiling: false
    };

    switch (this.environment) {
      case 'development':
        return {
          ...baseConfig,
          maxWorkers: 4,
          cacheSize: 1000,
          enableProfiling: true
        };

      case 'staging':
        return {
          ...baseConfig,
          maxWorkers: 8,
          cacheSize: 2500,
          enableProfiling: true
        };

      case 'production':
      default:
        return {
          ...baseConfig,
          maxWorkers: 32, // Maximum pour production
          cacheSize: 10000,
          memoryThreshold: 0.75, // Plus conservateur
          enableProfiling: false // Désactivé pour performance
        };
    }
  }

  /**
   * Configuration du dashboard
   */
  getDashboardConfig(): DashboardConfig {
    const baseConfig: DashboardConfig = {
      updateInterval: 5000,
      retentionPeriod: 86400000, // 24 heures
      alertThresholds: {
        convergenceStagnation: 100,
        diversityLoss: 0.2,
        performanceDegradation: 0.3,
        memoryUsage: 0.9
      }
    };

    switch (this.environment) {
      case 'development':
        return {
          ...baseConfig,
          updateInterval: 10000, // Moins fréquent
          retentionPeriod: 3600000, // 1 heure
          alertThresholds: {
            ...baseConfig.alertThresholds,
            performanceDegradation: 0.5 // Plus tolérant
          }
        };

      case 'staging':
        return {
          ...baseConfig,
          updateInterval: 7500,
          retentionPeriod: 43200000, // 12 heures
        };

      case 'production':
      default:
        return {
          ...baseConfig,
          updateInterval: 3000, // Plus fréquent en production
          retentionPeriod: 604800000, // 7 jours
          alertThresholds: {
            convergenceStagnation: 50, // Plus strict
            diversityLoss: 0.15,
            performanceDegradation: 0.2,
            memoryUsage: 0.85
          }
        };
    }
  }

  /**
   * Configuration de sécurité
   */
  getSecurityConfig(): SecurityConfig {
    return {
      encryption: {
        enabled: this.environment === 'production',
        algorithm: 'AES-256-GCM',
        keyRotationInterval: 86400000 // 24 heures
      },
      authentication: {
        enabled: this.environment !== 'development',
        tokenExpiration: 3600000, // 1 heure
        refreshTokenExpiration: 604800000 // 7 jours
      },
      rateLimit: {
        enabled: true,
        requestsPerMinute: this.environment === 'production' ? 1000 : 100,
        burstLimit: this.environment === 'production' ? 2000 : 200
      },
      cors: {
        enabled: true,
        allowedOrigins: this.getAllowedOrigins(),
        allowedMethods: ['GET', 'POST', 'PUT', 'DELETE'],
        allowCredentials: true
      },
      audit: {
        enabled: this.environment === 'production',
        logLevel: this.environment === 'production' ? 'info' : 'debug',
        retentionDays: 90
      }
    };
  }

  /**
   * Configuration de monitoring
   */
  getMonitoringConfig(): MonitoringConfig {
    return {
      metrics: {
        enabled: true,
        interval: this.environment === 'production' ? 30000 : 60000,
        retention: this.environment === 'production' ? ********** : 86400000, // 30 jours vs 1 jour
        exporters: this.getMetricsExporters()
      },
      logging: {
        level: this.environment === 'production' ? 'warn' : 'debug',
        format: 'json',
        transports: this.getLogTransports(),
        sampling: this.environment === 'production' ? 0.1 : 1.0 // 10% en production
      },
      tracing: {
        enabled: this.environment === 'production',
        samplingRate: 0.01, // 1% en production
        jaegerEndpoint: process.env.JAEGER_ENDPOINT
      },
      healthChecks: {
        enabled: true,
        interval: 30000,
        timeout: 5000,
        endpoints: ['/health', '/ready', '/metrics']
      }
    };
  }

  /**
   * Configuration de scaling
   */
  getScalingConfig(): ScalingConfig {
    return {
      autoScaling: {
        enabled: this.environment === 'production',
        minInstances: this.environment === 'production' ? 3 : 1,
        maxInstances: this.environment === 'production' ? 20 : 5,
        targetCpuUtilization: 70,
        targetMemoryUtilization: 75,
        scaleUpCooldown: 300000, // 5 minutes
        scaleDownCooldown: 600000 // 10 minutes
      },
      loadBalancing: {
        enabled: this.environment === 'production',
        algorithm: 'round_robin',
        healthCheckInterval: 30000,
        maxRetries: 3
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        timeout: 60000,
        resetTimeout: 300000
      }
    };
  }

  /**
   * Configuration de résilience
   */
  getResilienceConfig(): ResilienceConfig {
    return {
      retry: {
        enabled: true,
        maxAttempts: 3,
        backoffStrategy: 'exponential',
        initialDelay: 1000,
        maxDelay: 30000
      },
      timeout: {
        enabled: true,
        defaultTimeout: this.environment === 'production' ? 30000 : 60000,
        longRunningTimeout: this.environment === 'production' ? 300000 : 600000
      },
      bulkhead: {
        enabled: this.environment === 'production',
        maxConcurrentCalls: 100,
        maxWaitingCalls: 50
      },
      backup: {
        enabled: this.environment === 'production',
        interval: 3600000, // 1 heure
        retention: **********, // 30 jours
        compression: true
      }
    };
  }

  /**
   * Configuration complète pour l'environnement
   */
  getCompleteConfig(): CompleteConfig {
    return {
      environment: this.environment,
      orchestrator: this.getOrchestratorConfig(),
      performance: this.getPerformanceConfig(),
      dashboard: this.getDashboardConfig(),
      security: this.getSecurityConfig(),
      monitoring: this.getMonitoringConfig(),
      scaling: this.getScalingConfig(),
      resilience: this.getResilienceConfig(),
      deployment: this.getDeploymentConfig()
    };
  }

  /**
   * Validation de la configuration
   */
  validateConfig(): ValidationResult {
    const config = this.getCompleteConfig();
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validation de l'orchestrateur
    if (config.orchestrator.maxConcurrentEvolutions < 1) {
      errors.push('maxConcurrentEvolutions must be at least 1');
    }

    // Validation de la performance
    if (config.performance.maxWorkers < 1) {
      errors.push('maxWorkers must be at least 1');
    }

    if (config.performance.cacheSize < 100) {
      warnings.push('cacheSize is very low, consider increasing for better performance');
    }

    // Validation de la sécurité
    if (this.environment === 'production' && !config.security.encryption.enabled) {
      errors.push('Encryption must be enabled in production');
    }

    // Validation du monitoring
    if (this.environment === 'production' && !config.monitoring.tracing.enabled) {
      warnings.push('Tracing should be enabled in production for better observability');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Méthodes privées

  private getAllowedOrigins(): string[] {
    switch (this.environment) {
      case 'development':
        return ['http://localhost:3000', 'http://localhost:8080'];
      case 'staging':
        return ['https://staging.retreatandbe.com'];
      case 'production':
        return ['https://retreatandbe.com', 'https://app.retreatandbe.com'];
      default:
        return [];
    }
  }

  private getMetricsExporters(): string[] {
    const exporters = ['prometheus'];
    
    if (this.environment === 'production') {
      exporters.push('datadog', 'newrelic');
    }
    
    return exporters;
  }

  private getLogTransports(): any[] {
    const transports = [
      {
        type: 'console',
        level: this.environment === 'production' ? 'warn' : 'debug'
      }
    ];

    if (this.environment === 'production') {
      transports.push(
        {
          type: 'file',
          filename: '/var/log/evolution-agent/app.log',
          level: 'info',
          maxsize: 10485760, // 10MB
          maxFiles: 5
        },
        {
          type: 'elasticsearch',
          level: 'warn',
          index: 'evolution-agent-logs'
        }
      );
    }

    return transports;
  }

  private getDeploymentConfig(): DeploymentConfig {
    return {
      containerization: {
        enabled: true,
        registry: process.env.CONTAINER_REGISTRY || 'docker.io',
        image: 'evolution-agent',
        tag: process.env.IMAGE_TAG || 'latest'
      },
      kubernetes: {
        enabled: this.environment === 'production',
        namespace: `evolution-agent-${this.environment}`,
        resources: {
          requests: {
            cpu: this.environment === 'production' ? '2000m' : '500m',
            memory: this.environment === 'production' ? '4Gi' : '1Gi'
          },
          limits: {
            cpu: this.environment === 'production' ? '4000m' : '1000m',
            memory: this.environment === 'production' ? '8Gi' : '2Gi'
          }
        }
      },
      networking: {
        port: parseInt(process.env.PORT || '3000'),
        healthCheckPort: parseInt(process.env.HEALTH_PORT || '8080'),
        metricsPort: parseInt(process.env.METRICS_PORT || '9090')
      }
    };
  }
}

// Interfaces de configuration

export interface SecurityConfig {
  encryption: {
    enabled: boolean;
    algorithm: string;
    keyRotationInterval: number;
  };
  authentication: {
    enabled: boolean;
    tokenExpiration: number;
    refreshTokenExpiration: number;
  };
  rateLimit: {
    enabled: boolean;
    requestsPerMinute: number;
    burstLimit: number;
  };
  cors: {
    enabled: boolean;
    allowedOrigins: string[];
    allowedMethods: string[];
    allowCredentials: boolean;
  };
  audit: {
    enabled: boolean;
    logLevel: string;
    retentionDays: number;
  };
}

export interface MonitoringConfig {
  metrics: {
    enabled: boolean;
    interval: number;
    retention: number;
    exporters: string[];
  };
  logging: {
    level: string;
    format: string;
    transports: any[];
    sampling: number;
  };
  tracing: {
    enabled: boolean;
    samplingRate: number;
    jaegerEndpoint?: string;
  };
  healthChecks: {
    enabled: boolean;
    interval: number;
    timeout: number;
    endpoints: string[];
  };
}

export interface ScalingConfig {
  autoScaling: {
    enabled: boolean;
    minInstances: number;
    maxInstances: number;
    targetCpuUtilization: number;
    targetMemoryUtilization: number;
    scaleUpCooldown: number;
    scaleDownCooldown: number;
  };
  loadBalancing: {
    enabled: boolean;
    algorithm: string;
    healthCheckInterval: number;
    maxRetries: number;
  };
  circuitBreaker: {
    enabled: boolean;
    failureThreshold: number;
    timeout: number;
    resetTimeout: number;
  };
}

export interface ResilienceConfig {
  retry: {
    enabled: boolean;
    maxAttempts: number;
    backoffStrategy: string;
    initialDelay: number;
    maxDelay: number;
  };
  timeout: {
    enabled: boolean;
    defaultTimeout: number;
    longRunningTimeout: number;
  };
  bulkhead: {
    enabled: boolean;
    maxConcurrentCalls: number;
    maxWaitingCalls: number;
  };
  backup: {
    enabled: boolean;
    interval: number;
    retention: number;
    compression: boolean;
  };
}

export interface DeploymentConfig {
  containerization: {
    enabled: boolean;
    registry: string;
    image: string;
    tag: string;
  };
  kubernetes: {
    enabled: boolean;
    namespace: string;
    resources: {
      requests: { cpu: string; memory: string; };
      limits: { cpu: string; memory: string; };
    };
  };
  networking: {
    port: number;
    healthCheckPort: number;
    metricsPort: number;
  };
}

export interface CompleteConfig {
  environment: string;
  orchestrator: OrchestratorConfig;
  performance: PerformanceConfig;
  dashboard: DashboardConfig;
  security: SecurityConfig;
  monitoring: MonitoringConfig;
  scaling: ScalingConfig;
  resilience: ResilienceConfig;
  deployment: DeploymentConfig;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}
