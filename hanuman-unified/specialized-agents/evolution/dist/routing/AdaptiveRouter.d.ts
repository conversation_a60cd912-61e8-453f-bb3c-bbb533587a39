import { EventEmitter } from 'events';
import { NeuroplasticityEngine } from '../engines/NeuroplasticityEngine';
import { Logger } from '../utils/logger';
/**
 * Routeur Adaptatif pour Communications Synaptiques
 *
 * Implémente un système de routage intelligent qui s'adapte en temps réel
 * aux conditions du réseau pour optimiser les communications inter-agents.
 */
export declare class AdaptiveRouter extends EventEmitter {
    private engine;
    private logger;
    private routingTable;
    private loadBalancer;
    private circuitBreaker;
    private routingMetrics;
    constructor(engine: NeuroplasticityEngine, logger: Logger);
    /**
     * Initialise le routeur adaptatif
     */
    initialize(): Promise<void>;
    /**
     * Route un message de manière adaptative
     */
    routeMessage(message: RoutingMessage): Promise<RoutingResult>;
    /**
     * Trouve la route optimale entre deux agents
     */
    private findOptimalRoute;
    /**
     * Sélectionne la meilleure route selon les critères
     */
    private selectBestRoute;
    /**
     * Calcule le score d'une route
     */
    private calculateRouteScore;
    /**
     * Obtient les poids selon la priorité
     */
    private getPriorityWeights;
    /**
     * Crée une route directe
     */
    private createDirectRoute;
    /**
     * Trouve une route indirecte
     */
    private findIndirectRoute;
    /**
     * Construit un chemin optimal
     */
    private constructPath;
    /**
     * Exécute le routage
     */
    private executeRoute;
    /**
     * Met à jour les métriques de route
     */
    private updateRouteMetrics;
    /**
     * Gère les échecs de routage
     */
    private handleRoutingFailure;
    /**
     * Ajoute une route à la table
     */
    private addRoute;
    /**
     * Construit la table de routage initiale
     */
    private buildInitialRoutingTable;
    /**
     * Configure les listeners sur le moteur
     */
    private setupEngineListeners;
    /**
     * Reconstruit la table de routage
     */
    private rebuildRoutingTable;
    /**
     * Démarre l'optimisation périodique
     */
    private startPeriodicOptimization;
    /**
     * Optimise les routes
     */
    private optimizeRoutes;
    /**
     * Obtient les métriques de routage
     */
    getRoutingMetrics(): RoutingMetrics;
    /**
     * Obtient la table de routage
     */
    getRoutingTable(): Map<string, RouteEntry[]>;
}
interface RoutingMessage {
    id: string;
    from: string;
    to: string;
    payload: any;
    priority: MessagePriority;
    timestamp: Date;
}
interface RoutingResult {
    messageId: string;
    route: string[];
    latency: number;
    success: boolean;
    timestamp: Date;
    hops: number;
}
interface RouteEntry {
    path: string[];
    averageLatency: number;
    successRate: number;
    connectionStrength: number;
    currentLoad: number;
    maxLoad: number;
    isActive: boolean;
    priority: MessagePriority;
    lastUsed: Date;
    createdAt: Date;
}
interface RoutingMetrics {
    totalRoutes: number;
    successfulRoutes: number;
    failedRoutes: number;
    averageLatency: number;
    adaptationCount: number;
}
declare enum MessagePriority {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
}
export {};
//# sourceMappingURL=AdaptiveRouter.d.ts.map