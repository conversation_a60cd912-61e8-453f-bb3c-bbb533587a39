{"version": 3, "file": "AdaptiveRouter.d.ts", "sourceRoot": "", "sources": ["../../src/routing/AdaptiveRouter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AACzE,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AAEzC;;;;;GAKG;AACH,qBAAa,cAAe,SAAQ,YAAY;IAC9C,OAAO,CAAC,MAAM,CAAwB;IACtC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,YAAY,CAAwC;IAC5D,OAAO,CAAC,YAAY,CAAe;IACnC,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,cAAc,CAAiB;gBAE3B,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,MAAM;IAiBzD;;OAEG;IACU,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IASxC;;OAEG;IACU,YAAY,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC;IA0C1E;;OAEG;YACW,gBAAgB;IAqC9B;;OAEG;IACH,OAAO,CAAC,eAAe;IASvB;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAiB3B;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAa1B;;OAEG;YACW,iBAAiB;IA0B/B;;OAEG;YACW,iBAAiB;IA4B/B;;OAEG;IACH,OAAO,CAAC,aAAa;IAuBrB;;OAEG;YACW,YAAY;IAmC1B;;OAEG;YACW,kBAAkB;IAkBhC;;OAEG;YACW,oBAAoB;IAwBlC;;OAEG;IACH,OAAO,CAAC,QAAQ;IAQhB;;OAEG;YACW,wBAAwB;IA6BtC;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAc5B;;OAEG;YACW,mBAAmB;IAMjC;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAMjC;;OAEG;YACW,cAAc;IAmB5B;;OAEG;IACI,iBAAiB,IAAI,cAAc;IAI1C;;OAEG;IACI,eAAe,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC;CAGpD;AAsCD,UAAU,cAAc;IACtB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,GAAG,CAAC;IACb,QAAQ,EAAE,eAAe,CAAC;IAC1B,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,UAAU,aAAa;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,MAAM,EAAE,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,OAAO,CAAC;IACjB,SAAS,EAAE,IAAI,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;CACd;AAED,UAAU,UAAU;IAClB,IAAI,EAAE,MAAM,EAAE,CAAC;IACf,cAAc,EAAE,MAAM,CAAC;IACvB,WAAW,EAAE,MAAM,CAAC;IACpB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,OAAO,CAAC;IAClB,QAAQ,EAAE,eAAe,CAAC;IAC1B,QAAQ,EAAE,IAAI,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;CACjB;AASD,UAAU,cAAc;IACtB,WAAW,EAAE,MAAM,CAAC;IACpB,gBAAgB,EAAE,MAAM,CAAC;IACzB,YAAY,EAAE,MAAM,CAAC;IACrB,cAAc,EAAE,MAAM,CAAC;IACvB,eAAe,EAAE,MAAM,CAAC;CACzB;AAED,aAAK,eAAe;IAClB,GAAG,IAAI;IACP,MAAM,IAAI;IACV,IAAI,IAAI;IACR,QAAQ,IAAI;CACb"}