/**
 * Interface pour le système de logging
 */
export interface Logger {
    info(message: string, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
}
/**
 * Implémentation simple du logger pour les tests et le développement
 */
export declare class SimpleLogger implements Logger {
    private logLevel;
    constructor(logLevel?: LogLevel);
    info(message: string, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
}
/**
 * Logger silencieux pour les tests
 */
export declare class SilentLogger implements Logger {
    info(_message: string, ..._args: any[]): void;
    debug(_message: string, ..._args: any[]): void;
    warn(_message: string, ..._args: any[]): void;
    error(_message: string, ..._args: any[]): void;
}
export declare enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3
}
export declare const logger: SimpleLogger;
//# sourceMappingURL=logger.d.ts.map