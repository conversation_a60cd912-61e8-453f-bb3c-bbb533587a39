"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.LogLevel = exports.SilentLogger = exports.SimpleLogger = void 0;
/**
 * Implémentation simple du logger pour les tests et le développement
 */
class SimpleLogger {
    logLevel;
    constructor(logLevel = LogLevel.INFO) {
        this.logLevel = logLevel;
    }
    info(message, ...args) {
        if (this.logLevel <= LogLevel.INFO) {
            console.log(`[INFO] ${new Date().toISOString()} - ${message}`, ...args);
        }
    }
    debug(message, ...args) {
        if (this.logLevel <= LogLevel.DEBUG) {
            console.log(`[DEBUG] ${new Date().toISOString()} - ${message}`, ...args);
        }
    }
    warn(message, ...args) {
        if (this.logLevel <= LogLevel.WARN) {
            console.warn(`[WARN] ${new Date().toISOString()} - ${message}`, ...args);
        }
    }
    error(message, ...args) {
        if (this.logLevel <= LogLevel.ERROR) {
            console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, ...args);
        }
    }
}
exports.SimpleLogger = SimpleLogger;
/**
 * Logger silencieux pour les tests
 */
class SilentLogger {
    info(_message, ..._args) {
        // Ne fait rien
    }
    debug(_message, ..._args) {
        // Ne fait rien
    }
    warn(_message, ..._args) {
        // Ne fait rien
    }
    error(_message, ..._args) {
        // Ne fait rien
    }
}
exports.SilentLogger = SilentLogger;
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["ERROR"] = 0] = "ERROR";
    LogLevel[LogLevel["WARN"] = 1] = "WARN";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 3] = "DEBUG";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
// Instance par défaut
exports.logger = new SimpleLogger(LogLevel.INFO);
//# sourceMappingURL=logger.js.map