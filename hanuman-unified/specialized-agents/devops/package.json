{"name": "@retreat-and-be/agent-devops", "version": "1.0.0", "description": "Agent DevOps pour déploiement et gestion d'infrastructure automatisés", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "dev:watch": "nodemon --exec ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "docker:build": "docker build -t agent-devops .", "docker:run": "docker run -p 3007:3007 agent-devops", "terraform:init": "terraform init infrastructure/", "terraform:plan": "terraform plan infrastructure/", "terraform:apply": "terraform apply infrastructure/", "k8s:deploy": "kubectl apply -f kubernetes/", "k8s:status": "kubectl get pods -l app=retreat-and-be"}, "dependencies": {"@types/node": "^20.0.0", "axios": "^1.6.0", "express": "^4.18.0", "kafka-node": "^5.0.0", "redis": "^4.6.0", "winston": "^3.11.0", "dotenv": "^16.3.0", "weaviate-ts-client": "^1.4.0", "uuid": "^9.0.0", "joi": "^17.11.0", "helmet": "^7.1.0", "cors": "^2.8.5", "node-cron": "^3.0.0", "dockerode": "^4.0.0", "kubernetes-client": "^10.0.0", "terraform-node": "^1.0.0", "aws-sdk": "^2.1490.0", "@google-cloud/compute": "^4.1.0", "@azure/arm-compute": "^21.0.0", "vercel": "^32.0.0", "netlify": "^13.1.0", "archiver": "^6.0.0", "tar": "^6.2.0", "yaml": "^2.3.0", "shelljs": "^0.8.5", "node-ssh": "^13.1.0", "prometheus-api-metrics": "^3.2.0", "prom-client": "^15.0.0", "grafana-api": "^1.0.0", "datadog": "^0.2.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/uuid": "^9.0.0", "@types/cors": "^2.8.0", "@types/node-cron": "^3.0.0", "@types/dockerode": "^3.3.0", "@types/shelljs": "^0.8.0", "@types/tar": "^6.1.0", "@types/archiver": "^6.0.0", "typescript": "^5.2.0", "ts-node": "^10.9.0", "nodemon": "^3.0.0", "jest": "^29.7.0", "@types/jest": "^29.5.0", "eslint": "^8.52.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0"}, "keywords": ["ai-agent", "devops", "deployment", "infrastructure", "kubernetes", "terraform", "ci-cd", "retreat-and-be", "living-ai"], "author": "Retreat And Be Team", "license": "MIT"}