import { Logger } from 'winston';
import { TestRequest, TestResult, TestStatus } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import * as fs from 'fs-extra';
import * as path from 'path';
import { execSync } from 'child_process';

/**
 * Runner de Tests Unitaires
 * 
 * Exécute les tests unitaires avec <PERSON>, <PERSON><PERSON>, ou Vitest
 * selon le framework détecté.
 */
export class UnitTestRunner {
  private logger: Logger;
  private memory: WeaviateMemory;
  private workingDirectory: string;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    this.workingDirectory = process.env.TEST_WORKDIR || './test-workspace';
    
    // Créer le répertoire de travail
    fs.ensureDirSync(this.workingDirectory);
  }

  /**
   * Exécute les tests unitaires
   */
  async run(request: TestRequest, generatedTests?: any): Promise<TestResult> {
    this.logger.info('Exécution des tests unitaires', { id: request.id });

    const startTime = new Date();
    const testId = request.id;
    const projectPath = path.join(this.workingDirectory, testId);

    try {
      // 1. Préparer le projet de test
      await this.prepareTestProject(request, projectPath);

      // 2. Générer les tests unitaires si nécessaire
      if (generatedTests) {
        await this.writeGeneratedTests(projectPath, generatedTests);
      }

      // 3. Installer les dépendances
      await this.installDependencies(projectPath, request);

      // 4. Configurer le runner de tests
      await this.configureTestRunner(projectPath, request);

      // 5. Exécuter les tests
      const testOutput = await this.executeTests(projectPath, request);

      // 6. Parser les résultats
      const testResult = await this.parseTestResults(testOutput, request, startTime);

      // 7. Générer les artefacts
      await this.generateArtifacts(projectPath, testResult);

      // 8. Nettoyer
      await this.cleanup(projectPath);

      this.logger.info('Tests unitaires terminés', { 
        id: testId,
        status: testResult.status,
        successRate: testResult.summary.successRate 
      });

      return testResult;

    } catch (error) {
      this.logger.error('Erreur lors des tests unitaires', { 
        id: testId,
        error: error.message 
      });

      // Nettoyer en cas d'erreur
      await this.cleanup(projectPath);
      throw error;
    }
  }

  /**
   * Prépare le projet de test
   */
  private async prepareTestProject(request: TestRequest, projectPath: string): Promise<void> {
    await fs.ensureDir(projectPath);

    if (request.source.type === 'code' && request.source.code) {
      // Écrire les fichiers de code source
      for (const file of request.source.code.files) {
        const filePath = path.join(projectPath, file.path);
        await fs.ensureDir(path.dirname(filePath));
        await fs.writeFile(filePath, file.content);
      }

      // Créer package.json si nécessaire
      if (!request.source.code.files.find(f => f.path === 'package.json')) {
        const packageJson = this.generatePackageJson(request);
        await fs.writeFile(
          path.join(projectPath, 'package.json'),
          JSON.stringify(packageJson, null, 2)
        );
      }
    }
  }

  /**
   * Génère les tests unitaires automatiquement
   */
  private async writeGeneratedTests(projectPath: string, generatedTests: any): Promise<void> {
    const testsDir = path.join(projectPath, 'src', '__tests__');
    await fs.ensureDir(testsDir);

    for (const test of generatedTests.unitTests || []) {
      const testPath = path.join(testsDir, test.filename);
      await fs.writeFile(testPath, test.content);
    }
  }

  /**
   * Installe les dépendances de test
   */
  private async installDependencies(projectPath: string, request: TestRequest): Promise<void> {
    this.logger.info('Installation des dépendances de test');

    try {
      // Installer les dépendances principales
      execSync('npm install', { 
        cwd: projectPath,
        stdio: 'pipe',
        encoding: 'utf8'
      });

      // Installer les dépendances de test spécifiques
      const testDependencies = this.getTestDependencies(request);
      if (testDependencies.length > 0) {
        execSync(`npm install --save-dev ${testDependencies.join(' ')}`, {
          cwd: projectPath,
          stdio: 'pipe',
          encoding: 'utf8'
        });
      }

    } catch (error) {
      this.logger.error('Erreur lors de l\'installation des dépendances', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Configure le runner de tests
   */
  private async configureTestRunner(projectPath: string, request: TestRequest): Promise<void> {
    const framework = request.source.code?.framework || 'react';
    
    switch (framework) {
      case 'react':
        await this.configureJest(projectPath, request);
        break;
      case 'vue':
        await this.configureVitest(projectPath, request);
        break;
      case 'angular':
        await this.configureKarma(projectPath, request);
        break;
      default:
        await this.configureJest(projectPath, request);
    }
  }

  /**
   * Configure Jest pour React
   */
  private async configureJest(projectPath: string, request: TestRequest): Promise<void> {
    const jestConfig = {
      testEnvironment: 'jsdom',
      setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
      moduleNameMapping: {
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
        '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': 'jest-transform-stub'
      },
      collectCoverageFrom: [
        'src/**/*.{ts,tsx}',
        '!src/**/*.d.ts',
        '!src/index.tsx',
        '!src/reportWebVitals.ts'
      ],
      coverageDirectory: 'coverage',
      coverageReporters: ['text', 'lcov', 'html'],
      coverageThreshold: request.configuration.coverage ? {
        global: {
          statements: request.configuration.coverage.threshold.statements,
          branches: request.configuration.coverage.threshold.branches,
          functions: request.configuration.coverage.threshold.functions,
          lines: request.configuration.coverage.threshold.lines
        }
      } : undefined,
      testTimeout: request.configuration.timeout || 10000,
      verbose: true
    };

    await fs.writeFile(
      path.join(projectPath, 'jest.config.js'),
      `module.exports = ${JSON.stringify(jestConfig, null, 2)};`
    );

    // Créer setupTests.ts
    const setupTests = `
import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';

configure({ testIdAttribute: 'data-testid' });

// Mock des APIs du navigateur
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));
`;

    await fs.ensureDir(path.join(projectPath, 'src'));
    await fs.writeFile(path.join(projectPath, 'src', 'setupTests.ts'), setupTests);
  }

  /**
   * Configure Vitest pour Vue
   */
  private async configureVitest(projectPath: string, request: TestRequest): Promise<void> {
    const vitestConfig = `
import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/setupTests.ts'],
    coverage: {
      reporter: ['text', 'lcov', 'html'],
      exclude: [
        'node_modules/',
        'src/setupTests.ts',
      ]
    },
    globals: true
  }
});
`;

    await fs.writeFile(path.join(projectPath, 'vitest.config.ts'), vitestConfig);
  }

  /**
   * Configure Karma pour Angular
   */
  private async configureKarma(projectPath: string, request: TestRequest): Promise<void> {
    // Configuration Karma pour Angular
    const karmaConfig = `
module.exports = function (config) {
  config.set({
    basePath: '',
    frameworks: ['jasmine', '@angular-devkit/build-angular'],
    plugins: [
      require('karma-jasmine'),
      require('karma-chrome-headless-launcher'),
      require('karma-jasmine-html-reporter'),
      require('karma-coverage'),
      require('@angular-devkit/build-angular/plugins/karma')
    ],
    browsers: ['ChromeHeadless'],
    singleRun: true,
    coverageReporter: {
      dir: require('path').join(__dirname, './coverage'),
      subdir: '.',
      reporters: [
        { type: 'html' },
        { type: 'text-summary' },
        { type: 'lcov' }
      ]
    }
  });
};
`;

    await fs.writeFile(path.join(projectPath, 'karma.conf.js'), karmaConfig);
  }

  /**
   * Exécute les tests
   */
  private async executeTests(projectPath: string, request: TestRequest): Promise<string> {
    this.logger.info('Exécution des tests unitaires');

    const framework = request.source.code?.framework || 'react';
    let command = '';

    switch (framework) {
      case 'react':
        command = 'npx jest --coverage --ci --watchAll=false --passWithNoTests';
        break;
      case 'vue':
        command = 'npx vitest run --coverage';
        break;
      case 'angular':
        command = 'npx ng test --watch=false --code-coverage';
        break;
      default:
        command = 'npx jest --coverage --ci --watchAll=false --passWithNoTests';
    }

    try {
      const output = execSync(command, {
        cwd: projectPath,
        encoding: 'utf8',
        stdio: 'pipe',
        timeout: request.configuration.timeout || 300000 // 5 minutes
      });

      return output;
    } catch (error) {
      // Jest/Vitest retourne un code d'erreur même si seulement certains tests échouent
      // On récupère quand même la sortie pour l'analyser
      return error.stdout || error.stderr || error.message;
    }
  }

  /**
   * Parse les résultats des tests
   */
  private async parseTestResults(
    output: string, 
    request: TestRequest, 
    startTime: Date
  ): Promise<TestResult> {
    const framework = request.source.code?.framework || 'react';
    
    switch (framework) {
      case 'react':
        return this.parseJestResults(output, request, startTime);
      case 'vue':
        return this.parseVitestResults(output, request, startTime);
      case 'angular':
        return this.parseKarmaResults(output, request, startTime);
      default:
        return this.parseJestResults(output, request, startTime);
    }
  }

  /**
   * Parse les résultats Jest
   */
  private parseJestResults(output: string, request: TestRequest, startTime: Date): TestResult {
    const lines = output.split('\n');
    
    // Extraire les statistiques de base
    let total = 0, passed = 0, failed = 0, skipped = 0;
    let status: TestStatus = 'passed';

    // Parser la sortie Jest
    for (const line of lines) {
      if (line.includes('Tests:')) {
        const match = line.match(/(\d+) failed, (\d+) passed, (\d+) total/);
        if (match) {
          failed = parseInt(match[1]);
          passed = parseInt(match[2]);
          total = parseInt(match[3]);
        }
      }
      if (line.includes('FAIL') || line.includes('FAILED')) {
        status = 'failed';
      }
    }

    const successRate = total > 0 ? passed / total : 0;

    return {
      id: request.id,
      type: request.type,
      status,
      summary: {
        total,
        passed,
        failed,
        skipped,
        errors: 0,
        successRate
      },
      details: {
        suites: [], // À implémenter pour parser les suites détaillées
        environment: {
          os: process.platform,
          node: process.version,
          timestamp: new Date()
        },
        configuration: request.configuration
      },
      reports: [],
      artifacts: [],
      metrics: {},
      issues: [],
      recommendations: this.generateRecommendations(successRate, failed),
      startedAt: startTime,
      completedAt: new Date(),
      duration: Date.now() - startTime.getTime()
    };
  }

  /**
   * Parse les résultats Vitest
   */
  private parseVitestResults(output: string, request: TestRequest, startTime: Date): TestResult {
    // Logique similaire à Jest mais adaptée pour Vitest
    return this.parseJestResults(output, request, startTime);
  }

  /**
   * Parse les résultats Karma
   */
  private parseKarmaResults(output: string, request: TestRequest, startTime: Date): TestResult {
    // Logique similaire mais adaptée pour Karma/Jasmine
    return this.parseJestResults(output, request, startTime);
  }

  /**
   * Génère les artefacts de test
   */
  private async generateArtifacts(projectPath: string, testResult: TestResult): Promise<void> {
    const artifacts = [];

    // Copier les rapports de couverture
    const coveragePath = path.join(projectPath, 'coverage');
    if (await fs.pathExists(coveragePath)) {
      const artifactPath = path.join(this.workingDirectory, 'artifacts', testResult.id, 'coverage');
      await fs.copy(coveragePath, artifactPath);
      
      artifacts.push({
        type: 'coverage' as const,
        name: 'Coverage Report',
        path: artifactPath,
        size: await this.getDirectorySize(artifactPath),
        metadata: { format: 'html' }
      });
    }

    // Copier les rapports de test
    const reportsPath = path.join(projectPath, 'test-results');
    if (await fs.pathExists(reportsPath)) {
      const artifactPath = path.join(this.workingDirectory, 'artifacts', testResult.id, 'reports');
      await fs.copy(reportsPath, artifactPath);
      
      artifacts.push({
        type: 'log' as const,
        name: 'Test Reports',
        path: artifactPath,
        size: await this.getDirectorySize(artifactPath)
      });
    }

    testResult.artifacts = artifacts;
  }

  /**
   * Nettoie les fichiers temporaires
   */
  private async cleanup(projectPath: string): Promise<void> {
    try {
      await fs.remove(projectPath);
    } catch (error) {
      this.logger.warn('Erreur lors du nettoyage', { 
        projectPath,
        error: error.message 
      });
    }
  }

  // Méthodes utilitaires

  private generatePackageJson(request: TestRequest): any {
    const framework = request.source.code?.framework || 'react';
    
    const basePackage = {
      name: `test-project-${request.id}`,
      version: '1.0.0',
      private: true,
      scripts: {},
      dependencies: request.source.code?.dependencies.production || {},
      devDependencies: request.source.code?.dependencies.development || {}
    };

    // Ajouter les scripts selon le framework
    switch (framework) {
      case 'react':
        basePackage.scripts = {
          test: 'jest',
          'test:coverage': 'jest --coverage',
          'test:watch': 'jest --watch'
        };
        break;
      case 'vue':
        basePackage.scripts = {
          test: 'vitest',
          'test:coverage': 'vitest --coverage',
          'test:watch': 'vitest --watch'
        };
        break;
      case 'angular':
        basePackage.scripts = {
          test: 'ng test',
          'test:coverage': 'ng test --code-coverage'
        };
        break;
    }

    return basePackage;
  }

  private getTestDependencies(request: TestRequest): string[] {
    const framework = request.source.code?.framework || 'react';
    const dependencies = [];

    switch (framework) {
      case 'react':
        dependencies.push(
          'jest',
          '@testing-library/react',
          '@testing-library/jest-dom',
          '@testing-library/user-event',
          'jest-environment-jsdom',
          'identity-obj-proxy'
        );
        break;
      case 'vue':
        dependencies.push(
          'vitest',
          '@vue/test-utils',
          '@vitejs/plugin-vue',
          'jsdom'
        );
        break;
      case 'angular':
        dependencies.push(
          'karma',
          'karma-jasmine',
          'karma-chrome-headless-launcher',
          'karma-coverage',
          'jasmine-core'
        );
        break;
    }

    return dependencies;
  }

  private generateRecommendations(successRate: number, failedCount: number): string[] {
    const recommendations = [];

    if (successRate < 0.8) {
      recommendations.push('Taux de succès faible - Réviser les tests ou corriger le code');
    }

    if (failedCount > 0) {
      recommendations.push(`${failedCount} test(s) échoué(s) - Analyser les erreurs et corriger`);
    }

    if (successRate === 1) {
      recommendations.push('Tous les tests passent - Considérer l\'ajout de tests supplémentaires');
    }

    return recommendations;
  }

  private async getDirectorySize(dirPath: string): Promise<number> {
    try {
      const stats = await fs.stat(dirPath);
      if (stats.isFile()) {
        return stats.size;
      }
      
      let totalSize = 0;
      const files = await fs.readdir(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        totalSize += await this.getDirectorySize(filePath);
      }
      
      return totalSize;
    } catch (error) {
      return 0;
    }
  }
}
