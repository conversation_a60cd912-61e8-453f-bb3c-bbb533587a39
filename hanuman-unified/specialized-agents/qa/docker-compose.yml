version: '3.8'

services:
  # Agent QA Principal
  agent-qa:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: agent-qa
    ports:
      - "3008:3008"
      - "9090:9090" # Métriques Prometheus
    environment:
      - NODE_ENV=production
      - PORT=3008
      - AGENT_ID=agent-qa-001
      - LOG_LEVEL=info
      - WEAVIATE_URL=http://weaviate:8080
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis://redis:6379
      - DEFAULT_TIMEOUT=300000
      - MAX_PARALLEL_TESTS=3
      - HEADLESS_BROWSER=true
      - ENABLE_METRICS=true
      - METRICS_PORT=9090
    volumes:
      - ./reports:/app/reports
      - ./test-workspace:/app/test-workspace
      - ./logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock # Pour les tests de conteneurs
    depends_on:
      - weaviate
      - kafka
      - redis
    networks:
      - qa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3008/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Base de données vectorielle Weaviate
  weaviate:
    image: semitechnologies/weaviate:1.22.4
    container_name: weaviate-qa
    ports:
      - "8080:8080"
    environment:
      - QUERY_DEFAULTS_LIMIT=25
      - AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true
      - PERSISTENCE_DATA_PATH=/var/lib/weaviate
      - DEFAULT_VECTORIZER_MODULE=none
      - ENABLE_MODULES=text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai
      - CLUSTER_HOSTNAME=node1
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - qa-network
    restart: unless-stopped

  # Message Broker Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper-qa
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - qa-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka-qa
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    volumes:
      - kafka_data:/var/lib/kafka/data
    depends_on:
      - zookeeper
    networks:
      - qa-network
    restart: unless-stopped

  # Cache Redis
  redis:
    image: redis:7-alpine
    container_name: redis-qa
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    networks:
      - qa-network
    restart: unless-stopped

  # Interface Kafka (optionnel)
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui-qa
    ports:
      - "8081:8080"
    environment:
      - KAFKA_CLUSTERS_0_NAME=local
      - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=kafka:29092
      - KAFKA_CLUSTERS_0_ZOOKEEPER=zookeeper:2181
    depends_on:
      - kafka
    networks:
      - qa-network
    restart: unless-stopped

  # SonarQube pour l'analyse de qualité
  sonarqube:
    image: sonarqube:10.2-community
    container_name: sonarqube-qa
    ports:
      - "9000:9000"
    environment:
      - SONAR_JDBC_URL=*******************************************
      - SONAR_JDBC_USERNAME=sonar
      - SONAR_JDBC_PASSWORD=sonar
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_extensions:/opt/sonarqube/extensions
      - sonarqube_logs:/opt/sonarqube/logs
    depends_on:
      - postgres-sonar
    networks:
      - qa-network
    restart: unless-stopped

  # Base de données PostgreSQL pour SonarQube
  postgres-sonar:
    image: postgres:15-alpine
    container_name: postgres-sonar-qa
    environment:
      - POSTGRES_USER=sonar
      - POSTGRES_PASSWORD=sonar
      - POSTGRES_DB=sonar
    volumes:
      - postgres_sonar_data:/var/lib/postgresql/data
    networks:
      - qa-network
    restart: unless-stopped

  # Prometheus pour les métriques
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-qa
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - qa-network
    restart: unless-stopped

  # Grafana pour la visualisation
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-qa
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - qa-network
    restart: unless-stopped

  # Nginx pour le reverse proxy et servir les rapports
  nginx:
    image: nginx:alpine
    container_name: nginx-qa
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./reports:/usr/share/nginx/html/reports
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - agent-qa
    networks:
      - qa-network
    restart: unless-stopped

volumes:
  weaviate_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  redis_data:
    driver: local
  sonarqube_data:
    driver: local
  sonarqube_extensions:
    driver: local
  sonarqube_logs:
    driver: local
  postgres_sonar_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  qa-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
