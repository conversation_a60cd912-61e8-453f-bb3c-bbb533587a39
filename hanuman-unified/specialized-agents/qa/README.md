# 🧪 Agent QA - Test Automation & Quality Assurance

L'Agent QA est un composant intelligent du système nerveux distribué Retreat And Be qui automatise tous les aspects des tests et de l'assurance qualité.

## 🎯 Fonctionnalités Principales

### 🔬 Types de Tests Supportés
- **Tests Unitaires** - Je<PERSON>, <PERSON><PERSON><PERSON>, Mocha
- **Tests d'Intégration** - API, Base de données, Services
- **Tests E2E** - Playwright, Cypress, Selenium
- **Tests de Performance** - Lighthouse, Web Vitals, K6
- **Tests d'Accessibilité** - axe-core, pa11y, WCAG
- **Tests de Sécurité** - OWASP ZAP, Snyk, Audit
- **Tests Visuels** - Régression visuelle, Screenshots
- **Tests API** - REST, GraphQL, Postman
- **Tests de Charge** - Stress testing, Load testing

### 🎨 Frameworks Supportés
- **React** - Create React App, Next.js, Vite
- **Vue** - Vue CLI, Nuxt.js, Vite
- **Angular** - Angular CLI, Nx
- **Svelte** - SvelteKit, Vite
- **Vanilla** - JavaScript, TypeScript

### 🛠️ Outils Intégrés
- **Jest** - Tests unitaires et d'intégration
- **Playwright** - Tests E2E multi-navigateurs
- **Lighthouse** - Audit de performance et qualité
- **axe-core** - Tests d'accessibilité automatisés
- **ESLint** - Analyse statique de code
- **SonarQube** - Qualité et sécurité du code
- **K6** - Tests de performance et charge

## 🚀 Installation et Configuration

### Prérequis
```bash
# Node.js 18+
node --version

# Docker et Docker Compose
docker --version
docker-compose --version
```

### Installation
```bash
# Cloner le repository
git clone <repository-url>
cd agents/qa

# Installer les dépendances
npm install

# Installer les navigateurs Playwright
npx playwright install

# Copier la configuration
cp .env.example .env
```

### Configuration
```bash
# Éditer le fichier .env
nano .env

# Variables importantes :
PORT=3008
WEAVIATE_URL=http://weaviate:8080
KAFKA_BROKERS=kafka:9092
```

## 🏃‍♂️ Démarrage

### Mode Développement
```bash
npm run dev
```

### Mode Production
```bash
npm run build
npm start
```

### Avec Docker
```bash
# Build de l'image
docker build -t agent-qa .

# Démarrage du conteneur
docker run -p 3008:3008 agent-qa
```

### Avec Docker Compose
```bash
docker-compose up -d
```

## 📡 API Endpoints

### Santé et Statut
```http
GET /health          # État de santé de l'agent
GET /ready           # Disponibilité de l'agent
GET /api/info        # Informations sur l'agent
GET /api/status      # Statut détaillé
```

### Tests
```http
POST /api/test       # Exécuter des tests
POST /api/quality    # Analyser la qualité du code
POST /api/generate   # Générer des tests automatiquement
```

### Métriques et Rapports
```http
GET /api/metrics     # Métriques de test
GET /api/reports/:id # Rapports de test
```

## 🧪 Utilisation

### Exécution de Tests Unitaires
```javascript
const testRequest = {
  id: 'test-001',
  type: 'unit',
  source: {
    type: 'code',
    code: {
      framework: 'react',
      files: [
        {
          path: 'src/App.tsx',
          content: '...',
          type: 'source',
          language: 'typescript'
        }
      ]
    }
  },
  configuration: {
    timeout: 30000,
    coverage: {
      enabled: true,
      threshold: {
        statements: 80,
        branches: 80,
        functions: 80,
        lines: 80
      }
    }
  }
};

// Envoyer la requête
const response = await fetch('/api/test', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(testRequest)
});
```

### Tests E2E
```javascript
const e2eRequest = {
  id: 'e2e-001',
  type: 'e2e',
  source: {
    type: 'url',
    url: 'https://myapp.com'
  },
  configuration: {
    browsers: [
      { name: 'chromium', viewport: { width: 1920, height: 1080 } },
      { name: 'firefox', viewport: { width: 1920, height: 1080 } }
    ],
    headless: true
  }
};
```

### Analyse de Qualité
```javascript
const qualityRequest = {
  code: `
    import React from 'react';
    
    function App() {
      return <div>Hello World</div>;
    }
    
    export default App;
  `,
  framework: 'react'
};

const response = await fetch('/api/quality', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(qualityRequest)
});
```

## 📊 Métriques et Monitoring

### Métriques Collectées
- **Performance** - LCP, FID, CLS, Lighthouse scores
- **Accessibilité** - WCAG violations, score axe-core
- **Sécurité** - Vulnérabilités, score sécurité
- **Qualité** - Complexité, couverture, dette technique
- **Tests** - Taux de succès, durée, nombre de tests

### Dashboards
- **Grafana** - Visualisation des métriques
- **Weaviate** - Stockage des résultats
- **Rapports HTML** - Rapports détaillés

## 🔧 Configuration Avancée

### Seuils de Qualité
```javascript
// Dans .env
PERFORMANCE_THRESHOLD=90
ACCESSIBILITY_THRESHOLD=95
SECURITY_THRESHOLD=95
COVERAGE_THRESHOLD=80
```

### Navigateurs Personnalisés
```javascript
// Configuration des navigateurs
const browsers = [
  {
    name: 'chromium',
    viewport: { width: 1920, height: 1080 },
    userAgent: 'Custom User Agent',
    locale: 'fr-FR'
  }
];
```

### Tests Personnalisés
```javascript
// Générer des tests automatiquement
const generatedTests = await qaAgent.generateAutomaticTests({
  source: { type: 'code', code: sourceCode },
  type: 'unit'
});
```

## 🐛 Debugging

### Logs
```bash
# Voir les logs en temps réel
docker logs -f agent-qa

# Logs détaillés
DEBUG_MODE=true npm run dev
```

### Screenshots et Vidéos
```bash
# Activer la capture
SAVE_SCREENSHOTS=true
SAVE_VIDEOS=true
SAVE_TRACES=true
```

### Mode Verbose
```bash
VERBOSE_LOGGING=true
LOG_LEVEL=debug
```

## 🔒 Sécurité

### Authentification
```javascript
// Headers requis
{
  'Authorization': 'Bearer <token>',
  'X-API-Key': '<api-key>'
}
```

### CORS
```javascript
// Configuration CORS
CORS_ORIGIN=http://localhost:3000,https://myapp.com
```

## 🚀 Déploiement

### Production
```bash
# Build optimisé
npm run build

# Variables d'environnement
NODE_ENV=production
PORT=3008
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-qa
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agent-qa
  template:
    metadata:
      labels:
        app: agent-qa
    spec:
      containers:
      - name: agent-qa
        image: agent-qa:latest
        ports:
        - containerPort: 3008
        env:
        - name: WEAVIATE_URL
          value: "http://weaviate:8080"
        - name: KAFKA_BROKERS
          value: "kafka:9092"
```

## 🤝 Intégration avec les Autres Agents

### Agent Frontend
- Reçoit le code généré pour tests automatiques
- Envoie les rapports de qualité

### Agent Backend
- Teste les APIs générées
- Valide la sécurité

### Agent DevOps
- Intégration CI/CD
- Tests de déploiement

### Cortex Central
- Coordination des tests
- Agrégation des métriques

## 📈 Roadmap

### Version 1.1
- [ ] Tests de régression automatiques
- [ ] IA pour génération de tests
- [ ] Intégration Jira/GitHub

### Version 1.2
- [ ] Tests de compatibilité navigateurs
- [ ] Tests mobile natifs
- [ ] Rapports PDF avancés

### Version 2.0
- [ ] Tests basés sur l'IA
- [ ] Auto-réparation des tests
- [ ] Prédiction de bugs

## 🆘 Support

### Documentation
- [Guide d'utilisation](./docs/usage.md)
- [API Reference](./docs/api.md)
- [Troubleshooting](./docs/troubleshooting.md)

### Contact
- **Email** : <EMAIL>
- **Slack** : #agent-qa
- **Issues** : GitHub Issues

## 📄 Licence

MIT License - voir [LICENSE](./LICENSE) pour plus de détails.

---

**Agent QA** - Automatisation intelligente des tests pour un code de qualité professionnelle 🧪✨
