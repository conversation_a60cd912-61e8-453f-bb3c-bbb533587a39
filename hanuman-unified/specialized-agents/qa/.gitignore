# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Test results
test-results/
playwright-report/
test-results.xml
junit.xml

# Workspaces
test-workspace/
e2e-workspace/
performance-workspace/
accessibility-workspace/
security-workspace/
visual-workspace/

# Reports
reports/
*.html
*.json
*.xml
*.pdf

# Screenshots and videos
screenshots/
videos/
traces/

# Artifacts
artifacts/
temp/
tmp/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore
docker-compose.override.yml

# Monitoring
monitoring/prometheus/data/
monitoring/grafana/data/

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup
*.old

# Cache
.cache/
.npm/
.eslintcache
.stylelintcache

# Temporary files
*.tmp
*.temp

# Database
*.db
*.sqlite
*.sqlite3

# Secrets
secrets/
.secrets
*.secret

# Local configuration
config.local.js
config.local.json

# Performance monitoring
.clinic/

# Playwright
/test-results/
/playwright-report/
/playwright/.cache/

# SonarQube
.sonar/
.scannerwork/

# K6 results
k6-results/

# Lighthouse reports
lighthouse-reports/

# Visual regression
visual-regression/
baseline/
diff/

# Load testing
load-test-results/

# Security scan results
security-reports/

# Code quality reports
quality-reports/

# Accessibility reports
accessibility-reports/

# API testing
api-test-results/

# Mobile testing
mobile-test-results/

# Browser downloads
downloads/

# Webpack
.webpack/

# Parcel
.parcel-cache/

# Next.js
.next/

# Nuxt.js
.nuxt/

# Gatsby
.cache/
public/

# Storybook
storybook-static/

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local env files
.env*.local

# Vercel
.vercel

# Serverless
.serverless/

# FuseBox
.fusebox/

# DynamoDB Local
.dynamodb/

# TernJS
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# rollup.js default build output
dist/

# Uncomment the public line in if your project uses Gatsby
# https://nextjs.org/blog/next-9-1#public-directory-support
# https://create-react-app.dev/docs/using-the-public-folder/#docsNav
# public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
