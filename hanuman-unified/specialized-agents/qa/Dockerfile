FROM node:18-alpine

# Install system dependencies for testing tools
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    ca-certificates \
    curl \
    chromium \
    chromium-chromedriver \
    firefox \
    xvfb \
    dbus \
    ttf-freefont \
    bash \
    openjdk11-jre

# Install SonarQube Scanner
RUN wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.8.0.2856-linux.zip \
    && unzip sonar-scanner-cli-4.8.0.2856-linux.zip \
    && mv sonar-scanner-4.8.0.2856-linux /opt/sonar-scanner \
    && ln -s /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner \
    && rm sonar-scanner-cli-4.8.0.2856-linux.zip

# Install Lighthouse CI
RUN npm install -g @lhci/cli

# Install K6 for performance testing
RUN wget https://github.com/grafana/k6/releases/download/v0.47.0/k6-v0.47.0-linux-amd64.tar.gz \
    && tar -xzf k6-v0.47.0-linux-amd64.tar.gz \
    && mv k6-v0.47.0-linux-amd64/k6 /usr/local/bin/ \
    && rm -rf k6-v0.47.0-linux-amd64*

# Set environment variables for headless browsers
ENV CHROME_BIN=/usr/bin/chromium-browser
ENV CHROME_PATH=/usr/bin/chromium-browser
ENV CHROMIUM_PATH=/usr/bin/chromium-browser
ENV FIREFOX_BIN=/usr/bin/firefox
ENV DISPLAY=:99

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Install Playwright browsers
RUN npx playwright install --with-deps chromium firefox webkit

# Copy source code
COPY . .

# Build TypeScript
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S agent-qa -u 1001

# Create necessary directories
RUN mkdir -p /app/reports /app/screenshots /app/videos /app/logs /app/test-results
RUN chown -R agent-qa:nodejs /app

# Setup test environment
RUN mkdir -p /home/<USER>/.config
RUN chown -R agent-qa:nodejs /home/<USER>

USER agent-qa

# Expose port
EXPOSE 3008

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3008/health || exit 1

# Start Xvfb and the application
CMD ["sh", "-c", "Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 & npm start"]
