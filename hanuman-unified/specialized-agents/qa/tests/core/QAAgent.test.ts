import { QAAgent } from '../../src/core/QAAgent';
import { WeaviateMemory } from '../../src/memory/WeaviateMemory';
import { KafkaCommunication } from '../../src/communication/KafkaCommunication';
import { AgentConfig } from '../../src/types';
import winston from 'winston';
import { createMockTestRequest, createMockTestResult, expectTestResult } from '../setup';

describe('QAAgent', () => {
  let qaAgent: QAAgent;
  let mockLogger: winston.Logger;
  let mockMemory: jest.Mocked<WeaviateMemory>;
  let mockCommunication: jest.Mocked<KafkaCommunication>;
  let mockConfig: AgentConfig;

  beforeEach(() => {
    // Mock du logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    } as any;

    // Mo<PERSON> de la mémoire
    mockMemory = {
      storeTestResult: jest.fn().mockResolvedValue('test-id'),
      findSimilarTests: jest.fn().mockResolvedValue([]),
      getTestMetrics: jest.fn().mockResolvedValue([]),
      getOverallTestMetrics: jest.fn().mockResolvedValue({ connected: true }),
      storeTestPattern: jest.fn().mockResolvedValue('pattern-id'),
      isConnected: jest.fn().mockReturnValue(true),
      close: jest.fn().mockResolvedValue(undefined)
    } as any;

    // Mock de la communication
    mockCommunication = {
      on: jest.fn(),
      notifyTestComplete: jest.fn().mockResolvedValue(undefined),
      notifyTestFailed: jest.fn().mockResolvedValue(undefined),
      sendResponse: jest.fn().mockResolvedValue(undefined),
      sendError: jest.fn().mockResolvedValue(undefined),
      disconnect: jest.fn().mockResolvedValue(undefined)
    } as any;

    // Configuration mock
    mockConfig = {
      id: 'test-agent-qa',
      name: 'Test QA Agent',
      type: 'qa',
      version: '1.0.0',
      capabilities: ['unit-testing', 'e2e-testing'],
      endpoints: {},
      memory: { store: 'weaviate', collections: [] },
      communication: { kafka: { topics: [], groupId: 'test' }, redis: { channels: [] } },
      testing: {
        supportedTypes: ['unit', 'e2e', 'performance'],
        defaultTimeout: 30000,
        maxParallelTests: 2,
        browsers: ['chromium'],
        tools: []
      }
    };

    qaAgent = new QAAgent(mockConfig, mockLogger, mockMemory, mockCommunication);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Initialisation', () => {
    it('devrait initialiser l\'agent QA correctement', () => {
      expect(qaAgent).toBeDefined();
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('Agent QA test-agent-qa initialisé')
      );
    });

    it('devrait configurer les gestionnaires d\'événements', () => {
      expect(mockCommunication.on).toHaveBeenCalledWith('testRequest', expect.any(Function));
      expect(mockCommunication.on).toHaveBeenCalledWith('codeGenerated', expect.any(Function));
      expect(mockCommunication.on).toHaveBeenCalledWith('deploymentComplete', expect.any(Function));
    });
  });

  describe('runTests', () => {
    it('devrait exécuter des tests unitaires avec succès', async () => {
      const testRequest = createMockTestRequest({
        type: 'unit'
      });

      const result = await qaAgent.runTests(testRequest);

      expectTestResult(result);
      expect(result.type).toBe('unit');
      expect(result.status).toBe('passed');
      expect(mockMemory.storeTestResult).toHaveBeenCalledWith(result);
      expect(mockCommunication.notifyTestComplete).toHaveBeenCalledWith(result);
    });

    it('devrait exécuter des tests E2E avec succès', async () => {
      const testRequest = createMockTestRequest({
        type: 'e2e',
        source: {
          type: 'url',
          url: 'http://localhost:3000'
        }
      });

      const result = await qaAgent.runTests(testRequest);

      expectTestResult(result);
      expect(result.type).toBe('e2e');
      expect(mockMemory.storeTestResult).toHaveBeenCalledWith(result);
    });

    it('devrait exécuter des tests de performance', async () => {
      const testRequest = createMockTestRequest({
        type: 'performance',
        source: {
          type: 'url',
          url: 'http://localhost:3000'
        }
      });

      const result = await qaAgent.runTests(testRequest);

      expectTestResult(result);
      expect(result.type).toBe('performance');
      expect(result.metrics).toHaveProperty('performance');
    });

    it('devrait exécuter des tests d\'accessibilité', async () => {
      const testRequest = createMockTestRequest({
        type: 'accessibility',
        source: {
          type: 'url',
          url: 'http://localhost:3000'
        }
      });

      const result = await qaAgent.runTests(testRequest);

      expectTestResult(result);
      expect(result.type).toBe('accessibility');
      expect(result.metrics).toHaveProperty('accessibility');
    });

    it('devrait gérer les erreurs lors des tests', async () => {
      const testRequest = createMockTestRequest({
        id: '', // ID invalide pour déclencher une erreur
        type: 'unit'
      });

      await expect(qaAgent.runTests(testRequest)).rejects.toThrow('ID de test requis');
      expect(mockCommunication.notifyTestFailed).toHaveBeenCalled();
    });

    it('devrait valider les types de tests supportés', async () => {
      const testRequest = createMockTestRequest({
        type: 'unsupported-type' as any
      });

      await expect(qaAgent.runTests(testRequest)).rejects.toThrow(
        'Type de test unsupported-type non supporté'
      );
    });
  });

  describe('runFullTestSuite', () => {
    it('devrait exécuter une suite complète de tests', async () => {
      const testRequest = createMockTestRequest({
        type: 'full-suite'
      });

      const result = await qaAgent.runFullTestSuite(testRequest);

      expectTestResult(result);
      expect(result.type).toBe('full-suite');
      expect(result.details.suites.length).toBeGreaterThan(0);
    });

    it('devrait consolider les résultats de plusieurs types de tests', async () => {
      const testRequest = createMockTestRequest({
        type: 'full-suite'
      });

      const result = await qaAgent.runFullTestSuite(testRequest);

      expect(result.summary.total).toBeGreaterThan(0);
      expect(result.summary.successRate).toBeGreaterThanOrEqual(0);
      expect(result.summary.successRate).toBeLessThanOrEqual(1);
    });
  });

  describe('analyzeCodeQuality', () => {
    it('devrait analyser la qualité du code React', async () => {
      const code = `
        import React from 'react';
        
        function App() {
          return <div>Hello World</div>;
        }
        
        export default App;
      `;

      const analysis = await qaAgent.analyzeCodeQuality(code, 'react');

      expect(analysis).toHaveProperty('framework', 'react');
      expect(analysis).toHaveProperty('overallScore');
      expect(analysis).toHaveProperty('staticAnalysis');
      expect(analysis).toHaveProperty('complexityAnalysis');
      expect(analysis).toHaveProperty('recommendations');
      expect(typeof analysis.overallScore).toBe('number');
    });

    it('devrait analyser la qualité du code Vue', async () => {
      const code = `
        <template>
          <div>Hello World</div>
        </template>
        
        <script>
        export default {
          name: 'App'
        }
        </script>
      `;

      const analysis = await qaAgent.analyzeCodeQuality(code, 'vue');

      expect(analysis.framework).toBe('vue');
      expect(analysis.overallScore).toBeGreaterThanOrEqual(0);
      expect(analysis.overallScore).toBeLessThanOrEqual(100);
    });

    it('devrait détecter les problèmes de qualité', async () => {
      const codeWithIssues = `
        function badFunction() {
          console.log('debug');
          eval('dangerous code');
          var unused = 'variable';
        }
      `;

      const analysis = await qaAgent.analyzeCodeQuality(codeWithIssues, 'javascript');

      expect(analysis.staticAnalysis.issues.length).toBeGreaterThan(0);
      expect(analysis.securityAnalysis.issues.length).toBeGreaterThan(0);
      expect(analysis.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('generateAutomaticTests', () => {
    it('devrait générer des tests automatiquement', async () => {
      const testRequest = createMockTestRequest();

      const generatedTests = await qaAgent.generateAutomaticTests(testRequest);

      expect(generatedTests).toBeDefined();
      // Les tests générés dépendraient de l'implémentation réelle
    });
  });

  describe('getTestMetrics', () => {
    it('devrait récupérer les métriques d\'un test spécifique', async () => {
      const testId = 'test-001';
      const timeRange = '24h';

      const metrics = await qaAgent.getTestMetrics(testId, timeRange);

      expect(mockMemory.getTestMetrics).toHaveBeenCalledWith(testId, timeRange);
      expect(metrics).toBeDefined();
    });

    it('devrait récupérer les métriques globales', async () => {
      const timeRange = '7d';

      const metrics = await qaAgent.getTestMetrics(undefined, timeRange);

      expect(mockMemory.getOverallTestMetrics).toHaveBeenCalledWith(timeRange);
      expect(metrics).toBeDefined();
    });
  });

  describe('getStatus', () => {
    it('devrait retourner le statut de l\'agent', () => {
      const status = qaAgent.getStatus();

      expect(status).toHaveProperty('activeTests');
      expect(status).toHaveProperty('queuedTests');
      expect(status).toHaveProperty('isProcessingQueue');
      expect(status).toHaveProperty('supportedTypes');
      expect(status).toHaveProperty('maxParallelTests');
      expect(status).toHaveProperty('uptime');
    });
  });

  describe('shutdown', () => {
    it('devrait arrêter l\'agent gracieusement', async () => {
      await qaAgent.shutdown();

      expect(mockCommunication.disconnect).toHaveBeenCalled();
      expect(mockMemory.close).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('Agent QA arrêté');
    });
  });

  describe('Gestion des événements', () => {
    it('devrait traiter les demandes de test via Kafka', async () => {
      const testRequest = createMockTestRequest();
      const message = {
        id: 'msg-001',
        type: 'request',
        from: 'agent-frontend',
        to: 'agent-qa',
        payload: testRequest,
        timestamp: new Date(),
        correlationId: 'corr-001'
      };

      // Simuler la réception d'un message
      const handleTestRequest = (mockCommunication.on as jest.Mock).mock.calls
        .find(call => call[0] === 'testRequest')?.[1];

      if (handleTestRequest) {
        await handleTestRequest(message);
        expect(mockCommunication.sendResponse).toHaveBeenCalled();
      }
    });

    it('devrait traiter le code généré automatiquement', async () => {
      const message = {
        id: 'msg-002',
        type: 'notification',
        from: 'agent-frontend',
        to: 'agent-qa',
        payload: {
          generatedCode: {
            id: 'code-001',
            framework: 'react',
            files: []
          }
        },
        timestamp: new Date(),
        correlationId: 'corr-002'
      };

      const handleCodeGenerated = (mockCommunication.on as jest.Mock).mock.calls
        .find(call => call[0] === 'codeGenerated')?.[1];

      if (handleCodeGenerated) {
        await handleCodeGenerated(message);
        expect(mockCommunication.notifyTestComplete).toHaveBeenCalled();
      }
    });
  });
});
