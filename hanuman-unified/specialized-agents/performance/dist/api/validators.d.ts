import { Request, Response, NextFunction } from 'express';
/**
 * Validation des requêtes de benchmark
 */
export declare const validateBenchmarkRequest: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validation des requêtes d'optimisation
 */
export declare const validateOptimizationRequest: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validation des requêtes de monitoring
 */
export declare const validateMonitoringRequest: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validation des requêtes d'analyse
 */
export declare const validateAnalysisRequest: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validation des paramètres de requête pour les rapports
 */
export declare const validateReportsQuery: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validation des en-têtes d'authentification (si nécessaire)
 */
export declare const validateAuthHeaders: (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validation du rate limiting personnalisé
 */
export declare const validateRateLimit: (maxRequests: number, windowMs: number) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validation de la taille du payload
 */
export declare const validatePayloadSize: (maxSizeBytes: number) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
/**
 * Validation des types de contenu acceptés
 */
export declare const validateContentType: (allowedTypes: string[]) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
//# sourceMappingURL=validators.d.ts.map