import { Logger } from 'winston';
import { PerformanceAgent } from '../core/PerformanceAgent';
import { PerformanceConfig } from '../types';
/**
 * Serveur API REST pour l'Agent Performance
 */
export declare class PerformanceServer {
    private readonly app;
    private readonly config;
    private readonly logger;
    private readonly agent;
    private server;
    constructor(config: PerformanceConfig, logger: Logger, agent: PerformanceAgent);
    /**
     * Démarrer le serveur
     */
    start(): Promise<void>;
    /**
     * Arrêter le serveur
     */
    stop(): Promise<void>;
    /**
     * Configuration des middlewares
     */
    private setupMiddleware;
    /**
     * Configuration des routes
     */
    private setupRoutes;
    /**
     * Gestion des erreurs
     */
    private setupErrorHandling;
}
//# sourceMappingURL=server.d.ts.map