"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const validators_1 = require("./validators");
/**
 * Serveur API REST pour l'Agent Performance
 */
class PerformanceServer {
    app;
    config;
    logger;
    agent;
    server = null;
    constructor(config, logger, agent) {
        this.config = config;
        this.logger = logger;
        this.agent = agent;
        this.app = (0, express_1.default)();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
    }
    /**
     * Démarrer le serveur
     */
    async start() {
        return new Promise((resolve, reject) => {
            this.server = this.app.listen(this.config.port, () => {
                this.logger.info(`Serveur API démarré sur le port ${this.config.port}`);
                resolve();
            });
            this.server.on('error', (error) => {
                this.logger.error('Erreur du serveur:', error);
                reject(error);
            });
        });
    }
    /**
     * Arrêter le serveur
     */
    async stop() {
        if (this.server) {
            return new Promise((resolve) => {
                this.server.close(() => {
                    this.logger.info('Serveur API arrêté');
                    resolve();
                });
            });
        }
    }
    /**
     * Configuration des middlewares
     */
    setupMiddleware() {
        // Sécurité
        this.app.use((0, helmet_1.default)());
        // CORS
        this.app.use((0, cors_1.default)({
            origin: process.env.CORS_ORIGIN || '*',
            methods: ['GET', 'POST', 'PUT', 'DELETE'],
            allowedHeaders: ['Content-Type', 'Authorization']
        }));
        // Compression
        this.app.use((0, compression_1.default)());
        // Rate limiting
        const limiter = (0, express_rate_limit_1.default)({
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100, // limite chaque IP à 100 requêtes par windowMs
            message: 'Trop de requêtes depuis cette IP, réessayez plus tard.'
        });
        this.app.use(limiter);
        // Parsing JSON
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true }));
        // Logging des requêtes
        this.app.use((req, res, next) => {
            this.logger.info(`${req.method} ${req.path}`, {
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
            next();
        });
    }
    /**
     * Configuration des routes
     */
    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            const status = this.agent.getStatus();
            res.json({
                status: 'healthy',
                timestamp: new Date(),
                agent: status
            });
        });
        // Métriques Prometheus
        this.app.get('/metrics', async (req, res) => {
            try {
                const metrics = this.agent.getCurrentMetrics();
                res.set('Content-Type', 'text/plain');
                res.send(Object.entries(metrics).map(([name, values]) => `# HELP ${name} Performance metric\n# TYPE ${name} gauge\n${name} ${values[values.length - 1]?.value || 0}`).join('\n'));
            }
            catch (error) {
                this.logger.error('Erreur lors de la récupération des métriques:', error);
                res.status(500).json({ error: 'Erreur interne du serveur' });
            }
        });
        // Lancer un benchmark
        this.app.post('/benchmark', validators_1.validateBenchmarkRequest, async (req, res) => {
            try {
                const request = req.body;
                request.id = request.id || `benchmark-${Date.now()}`;
                this.logger.info(`Demande de benchmark reçue: ${request.id}`);
                // Exécuter le benchmark de manière asynchrone
                this.agent.executeBenchmark(request)
                    .then(result => {
                    this.logger.info(`Benchmark ${request.id} terminé`);
                })
                    .catch(error => {
                    this.logger.error(`Erreur lors du benchmark ${request.id}:`, error);
                });
                res.json({
                    message: 'Benchmark démarré',
                    benchmarkId: request.id,
                    status: 'started'
                });
            }
            catch (error) {
                this.logger.error('Erreur lors du démarrage du benchmark:', error);
                res.status(500).json({ error: 'Erreur interne du serveur' });
            }
        });
        // Demander des optimisations
        this.app.post('/optimize', validators_1.validateOptimizationRequest, async (req, res) => {
            try {
                const request = req.body;
                request.id = request.id || `optimization-${Date.now()}`;
                this.logger.info(`Demande d'optimisation reçue: ${request.id}`);
                const recommendations = await this.agent.requestOptimization(request);
                res.json({
                    message: 'Optimisations générées',
                    optimizationId: request.id,
                    recommendations
                });
            }
            catch (error) {
                this.logger.error('Erreur lors de la génération d\'optimisations:', error);
                res.status(500).json({ error: 'Erreur interne du serveur' });
            }
        });
        // Configurer le monitoring
        this.app.post('/monitor', validators_1.validateMonitoringRequest, async (req, res) => {
            try {
                const request = req.body;
                request.id = request.id || `monitor-${Date.now()}`;
                this.logger.info(`Configuration de monitoring reçue: ${request.id}`);
                await this.agent.configureMonitoring(request);
                res.json({
                    message: 'Monitoring configuré',
                    monitorId: request.id,
                    status: 'configured'
                });
            }
            catch (error) {
                this.logger.error('Erreur lors de la configuration du monitoring:', error);
                res.status(500).json({ error: 'Erreur interne du serveur' });
            }
        });
        // Analyser une application
        this.app.post('/analyze', async (req, res) => {
            try {
                const request = req.body;
                request.id = request.id || `analysis-${Date.now()}`;
                this.logger.info(`Demande d'analyse reçue: ${request.id}`);
                const analysis = await this.agent.analyzeApplication(request);
                res.json({
                    message: 'Analyse terminée',
                    analysisId: request.id,
                    analysis
                });
            }
            catch (error) {
                this.logger.error('Erreur lors de l\'analyse:', error);
                res.status(500).json({ error: 'Erreur interne du serveur' });
            }
        });
        // Obtenir les rapports
        this.app.get('/reports', async (req, res) => {
            try {
                const { type, limit = 10 } = req.query;
                // Simuler la récupération de rapports
                const reports = {
                    benchmarks: [],
                    optimizations: [],
                    analyses: []
                };
                res.json({
                    reports,
                    total: 0,
                    timestamp: new Date()
                });
            }
            catch (error) {
                this.logger.error('Erreur lors de la récupération des rapports:', error);
                res.status(500).json({ error: 'Erreur interne du serveur' });
            }
        });
        // Obtenir le statut de l'agent
        this.app.get('/status', (req, res) => {
            try {
                const status = this.agent.getStatus();
                const alerts = this.agent.getActiveAlerts();
                res.json({
                    ...status,
                    alerts: {
                        total: alerts.length,
                        critical: alerts.filter(a => a.severity === 'critical').length,
                        warning: alerts.filter(a => a.severity === 'warning').length,
                        recent: alerts.slice(0, 5)
                    }
                });
            }
            catch (error) {
                this.logger.error('Erreur lors de la récupération du statut:', error);
                res.status(500).json({ error: 'Erreur interne du serveur' });
            }
        });
        // Documentation API
        this.app.get('/api-docs', (req, res) => {
            res.json({
                name: 'Agent Performance API',
                version: '1.0.0',
                description: 'API pour l\'Agent Performance - Optimisation et monitoring avancé',
                endpoints: {
                    'GET /health': 'Health check de l\'agent',
                    'GET /metrics': 'Métriques Prometheus',
                    'POST /benchmark': 'Lancer un benchmark',
                    'POST /optimize': 'Demander des optimisations',
                    'POST /monitor': 'Configurer le monitoring',
                    'POST /analyze': 'Analyser une application',
                    'GET /reports': 'Obtenir les rapports',
                    'GET /status': 'Statut de l\'agent',
                    'GET /api-docs': 'Documentation de l\'API'
                },
                examples: {
                    benchmark: {
                        type: 'lighthouse',
                        target: { url: 'https://example.com' },
                        configuration: {
                            lighthouse: {
                                categories: ['performance', 'accessibility'],
                                formFactor: 'desktop'
                            }
                        }
                    },
                    optimize: {
                        target: { service: 'web-app' },
                        scope: ['frontend', 'backend'],
                        constraints: {
                            budget: 1000,
                            timeline: 'urgent',
                            technologies: ['react', 'nodejs']
                        },
                        goals: {
                            performance: 80,
                            scalability: 70
                        }
                    }
                }
            });
        });
    }
    /**
     * Gestion des erreurs
     */
    setupErrorHandling() {
        // 404 - Route non trouvée
        this.app.use((req, res) => {
            res.status(404).json({
                error: 'Route non trouvée',
                path: req.path,
                method: req.method
            });
        });
        // Gestionnaire d'erreurs global
        this.app.use((error, req, res, next) => {
            this.logger.error('Erreur non gérée:', error);
            res.status(500).json({
                error: 'Erreur interne du serveur',
                message: process.env.NODE_ENV === 'development' ? error.message : 'Une erreur est survenue'
            });
        });
    }
}
exports.PerformanceServer = PerformanceServer;
//# sourceMappingURL=server.js.map