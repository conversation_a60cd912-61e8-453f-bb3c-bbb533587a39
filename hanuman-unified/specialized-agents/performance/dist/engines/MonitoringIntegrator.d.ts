import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { MonitoringConfig, MonitoringRequest, PerformanceAlert } from '../types';
/**
 * Intégrateur de monitoring temps réel
 * Collecte et analyse les métriques de performance
 */
export declare class MonitoringIntegrator extends EventEmitter {
    private readonly config;
    private readonly logger;
    private readonly registry;
    private readonly metrics;
    private isRunning;
    private monitoringInterval;
    private activeMonitors;
    private alertHistory;
    private cpuUsageGauge;
    private memoryUsageGauge;
    private responseTimeHistogram;
    private requestCounter;
    private errorCounter;
    constructor(config: MonitoringConfig, logger: Logger);
    /**
     * Démarrer le monitoring
     */
    start(): Promise<void>;
    /**
     * Arrêter le monitoring
     */
    stop(): Promise<void>;
    /**
     * Configurer un monitoring spécifique
     */
    configure(request: MonitoringRequest): Promise<void>;
    /**
     * Enregistrer une métrique
     */
    recordMetric(name: string, value: number, labels?: Record<string, string>): void;
    /**
     * Enregistrer le temps de réponse
     */
    recordResponseTime(endpoint: string, duration: number, statusCode: number): void;
    /**
     * Obtenir les métriques Prometheus
     */
    getMetrics(): Promise<string>;
    /**
     * Obtenir les alertes actives
     */
    getActiveAlerts(): PerformanceAlert[];
    /**
     * Résoudre une alerte
     */
    resolveAlert(alertId: string): void;
    /**
     * Initialiser les métriques Prometheus
     */
    private initializePrometheusMetrics;
    /**
     * Démarrer la surveillance système
     */
    private startSystemMonitoring;
    /**
     * Collecter les métriques système
     */
    private collectSystemMetrics;
    /**
     * Démarrer un monitoring spécifique
     */
    private startSpecificMonitoring;
    /**
     * Monitorer une cible spécifique
     */
    private monitorTarget;
    /**
     * Mettre à jour une métrique Prometheus
     */
    private updatePrometheusMetric;
    /**
     * Vérifier les seuils et générer des alertes
     */
    private checkThresholds;
    /**
     * Créer une alerte
     */
    private createAlert;
    /**
     * Obtenir le seuil pour une métrique
     */
    private getThresholdForMetric;
}
//# sourceMappingURL=MonitoringIntegrator.d.ts.map