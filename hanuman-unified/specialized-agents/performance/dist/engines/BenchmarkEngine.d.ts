import { Logger } from 'winston';
import { BenchmarkingConfig, BenchmarkRequest, BenchmarkResult } from '../types';
/**
 * Moteur de benchmarking avancé
 * Exécute différents types de tests de performance
 */
export declare class BenchmarkEngine {
    private readonly config;
    private readonly logger;
    private readonly resultsDir;
    constructor(config: BenchmarkingConfig, logger: Logger);
    /**
     * Exécuter un benchmark selon le type demandé
     */
    execute(request: BenchmarkRequest): Promise<BenchmarkResult>;
    /**
     * Exécuter un test Lighthouse
     */
    private runLighthouseTest;
    /**
     * Exécuter un test de charge
     */
    private runLoadTest;
    /**
     * Exécuter un test avec Autocannon
     */
    private runAutocannonTest;
    /**
     * Exécuter un test avec K6
     */
    private runK6Test;
    /**
     * Exécuter un test de profiling
     */
    private runProfilingTest;
    /**
     * Extraire les opportunités d'optimisation de Lighthouse
     */
    private extractOpportunities;
    /**
     * Calculer la moyenne
     */
    private calculateAverage;
    /**
     * Calculer un percentile
     */
    private calculatePercentile;
    /**
     * S'assurer que le répertoire des résultats existe
     */
    private ensureResultsDirectory;
}
//# sourceMappingURL=BenchmarkEngine.d.ts.map