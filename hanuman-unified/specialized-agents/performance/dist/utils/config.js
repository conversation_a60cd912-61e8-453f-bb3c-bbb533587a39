"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDefaultConfig = getDefaultConfig;
exports.validateConfig = validateConfig;
exports.loadConfig = loadConfig;
/**
 * Configuration par défaut pour l'Agent Performance
 */
function getDefaultConfig() {
    return {
        port: parseInt(process.env.PORT || '3007'),
        kafka: {
            brokers: (process.env.KAFKA_BROKERS || 'localhost:9092').split(','),
            clientId: process.env.KAFKA_CLIENT_ID || 'performance-agent',
            groupId: process.env.KAFKA_GROUP_ID || 'performance-agent-group',
            topics: {
                requests: process.env.KAFKA_TOPIC_REQUESTS || 'performance.requests',
                results: process.env.KAFKA_TOPIC_RESULTS || 'performance.results',
                alerts: process.env.KAFKA_TOPIC_ALERTS || 'performance.alerts',
                recommendations: process.env.KAFKA_TOPIC_RECOMMENDATIONS || 'performance.recommendations'
            }
        },
        weaviate: {
            host: process.env.WEAVIATE_HOST || 'localhost:8080',
            scheme: process.env.WEAVIATE_SCHEME || 'http',
            apiKey: process.env.WEAVIATE_API_KEY,
            collections: {
                optimizations: process.env.WEAVIATE_COLLECTION_OPTIMIZATIONS || 'PerformanceOptimizations',
                benchmarks: process.env.WEAVIATE_COLLECTION_BENCHMARKS || 'PerformanceBenchmarks',
                recommendations: process.env.WEAVIATE_COLLECTION_RECOMMENDATIONS || 'PerformanceRecommendations',
                metrics: process.env.WEAVIATE_COLLECTION_METRICS || 'PerformanceMetrics'
            }
        },
        prometheus: {
            enabled: process.env.PROMETHEUS_ENABLED === 'true',
            port: parseInt(process.env.PROMETHEUS_PORT || '9090'),
            path: process.env.PROMETHEUS_PATH || '/metrics',
            collectDefaultMetrics: process.env.PROMETHEUS_COLLECT_DEFAULT !== 'false'
        },
        benchmarking: {
            lighthouse: {
                enabled: process.env.LIGHTHOUSE_ENABLED !== 'false',
                categories: (process.env.LIGHTHOUSE_CATEGORIES || 'performance,accessibility,best-practices,seo').split(','),
                formFactor: process.env.LIGHTHOUSE_FORM_FACTOR || 'desktop',
                throttling: process.env.LIGHTHOUSE_THROTTLING || 'simulated3G'
            },
            loadTesting: {
                enabled: process.env.LOAD_TESTING_ENABLED !== 'false',
                tool: process.env.LOAD_TESTING_TOOL || 'autocannon',
                duration: process.env.LOAD_TESTING_DURATION || '30s',
                connections: parseInt(process.env.LOAD_TESTING_CONNECTIONS || '10'),
                rps: parseInt(process.env.LOAD_TESTING_RPS || '100')
            },
            profiling: {
                enabled: process.env.PROFILING_ENABLED !== 'false',
                tool: process.env.PROFILING_TOOL || 'clinic',
                duration: parseInt(process.env.PROFILING_DURATION || '60')
            }
        },
        monitoring: {
            interval: parseInt(process.env.MONITORING_INTERVAL || '30000'), // 30 secondes
            thresholds: {
                responseTime: parseInt(process.env.THRESHOLD_RESPONSE_TIME || '1000'), // 1 seconde
                cpuUsage: parseInt(process.env.THRESHOLD_CPU_USAGE || '80'), // 80%
                memoryUsage: parseInt(process.env.THRESHOLD_MEMORY_USAGE || '85'), // 85%
                errorRate: parseInt(process.env.THRESHOLD_ERROR_RATE || '5') // 5%
            },
            alerts: {
                enabled: process.env.ALERTS_ENABLED !== 'false',
                channels: (process.env.ALERT_CHANNELS || 'kafka,log').split(',')
            }
        }
    };
}
/**
 * Valider la configuration
 */
function validateConfig(config) {
    // Validation du port
    if (config.port < 1 || config.port > 65535) {
        throw new Error(`Port invalide: ${config.port}`);
    }
    // Validation Kafka
    if (!config.kafka.brokers || config.kafka.brokers.length === 0) {
        throw new Error('Au moins un broker Kafka doit être configuré');
    }
    if (!config.kafka.clientId || !config.kafka.groupId) {
        throw new Error('ClientId et GroupId Kafka sont requis');
    }
    // Validation Weaviate
    if (!config.weaviate.host) {
        throw new Error('Host Weaviate est requis');
    }
    if (!['http', 'https'].includes(config.weaviate.scheme)) {
        throw new Error('Scheme Weaviate doit être http ou https');
    }
    // Validation des seuils de monitoring
    const thresholds = config.monitoring.thresholds;
    if (thresholds.cpuUsage < 0 || thresholds.cpuUsage > 100) {
        throw new Error('Seuil CPU doit être entre 0 et 100');
    }
    if (thresholds.memoryUsage < 0 || thresholds.memoryUsage > 100) {
        throw new Error('Seuil mémoire doit être entre 0 et 100');
    }
    if (thresholds.responseTime < 0) {
        throw new Error('Seuil temps de réponse doit être positif');
    }
    if (thresholds.errorRate < 0 || thresholds.errorRate > 100) {
        throw new Error('Seuil taux d\'erreur doit être entre 0 et 100');
    }
}
/**
 * Charger la configuration depuis les variables d'environnement
 */
function loadConfig() {
    const config = getDefaultConfig();
    validateConfig(config);
    return config;
}
//# sourceMappingURL=config.js.map