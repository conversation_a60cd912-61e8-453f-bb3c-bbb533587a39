"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
exports.createLogger = createLogger;
const winston_1 = __importDefault(require("winston"));
/**
 * Configuration du logger pour l'Agent Performance
 */
function createLogger(service) {
    const logLevel = process.env.LOG_LEVEL || 'info';
    const logger = winston_1.default.createLogger({
        level: logLevel,
        format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf(({ timestamp, level, message, service, ...meta }) => {
            return JSON.stringify({
                timestamp,
                level,
                service,
                message,
                ...meta
            });
        })),
        defaultMeta: { service },
        transports: [
            // Console transport
            new winston_1.default.transports.Console({
                format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple(), winston_1.default.format.printf(({ timestamp, level, message, service }) => {
                    return `${timestamp} [${service}] ${level}: ${message}`;
                }))
            }),
            // File transport pour les erreurs
            new winston_1.default.transports.File({
                filename: 'logs/error.log',
                level: 'error',
                maxsize: 5242880, // 5MB
                maxFiles: 5
            }),
            // File transport pour tous les logs
            new winston_1.default.transports.File({
                filename: 'logs/combined.log',
                maxsize: 5242880, // 5MB
                maxFiles: 5
            })
        ]
    });
    // En développement, ajouter des logs plus détaillés
    if (process.env.NODE_ENV === 'development') {
        logger.add(new winston_1.default.transports.Console({
            format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp(), winston_1.default.format.printf(({ timestamp, level, message, service, ...meta }) => {
                const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
                return `${timestamp} [${service}] ${level}: ${message} ${metaStr}`;
            }))
        }));
    }
    return logger;
}
/**
 * Logger par défaut pour l'application
 */
exports.logger = createLogger('PerformanceAgent');
//# sourceMappingURL=logger.js.map