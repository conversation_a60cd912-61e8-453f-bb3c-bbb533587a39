import { EventEmitter } from 'events';
import { PerformanceConfig, BenchmarkRequest, BenchmarkResult, OptimizationRequest, MonitoringRequest, AnalysisRequest, PerformanceMetric, PerformanceAlert, Recommendation } from '../types';
/**
 * Agent Performance principal
 * Orchestrateur intelligent pour l'optimisation et le monitoring des performances
 */
export declare class PerformanceAgent extends EventEmitter {
    private readonly logger;
    private readonly config;
    private readonly benchmarkEngine;
    private readonly optimizationAdvisor;
    private readonly monitoringIntegrator;
    private readonly communication;
    private readonly memory;
    private isRunning;
    private activeJobs;
    private metrics;
    private alerts;
    constructor(config: PerformanceConfig);
    /**
     * Démarrer l'agent
     */
    start(): Promise<void>;
    /**
     * Arrêter l'agent
     */
    stop(): Promise<void>;
    /**
     * Exécuter un benchmark
     */
    executeBenchmark(request: BenchmarkRequest): Promise<BenchmarkResult>;
    /**
     * Demander des optimisations
     */
    requestOptimization(request: OptimizationRequest): Promise<Recommendation[]>;
    /**
     * Configurer le monitoring
     */
    configureMonitoring(request: MonitoringRequest): Promise<void>;
    /**
     * Analyser une application/service
     */
    analyzeApplication(request: AnalysisRequest): Promise<any>;
    /**
     * Obtenir les métriques actuelles
     */
    getCurrentMetrics(): Record<string, PerformanceMetric[]>;
    /**
     * Obtenir les alertes actives
     */
    getActiveAlerts(): PerformanceAlert[];
    /**
     * Obtenir le statut de l'agent
     */
    getStatus(): any;
    /**
     * Configuration des gestionnaires d'événements
     */
    private setupEventHandlers;
    /**
     * Configuration des abonnements Kafka
     */
    private setupKafkaSubscriptions;
    /**
     * Traiter une requête de performance
     */
    private handlePerformanceRequest;
    /**
     * Traiter une métrique
     */
    private handleMetric;
    /**
     * Traiter une alerte
     */
    private handleAlert;
    /**
     * Analyser le contexte d'optimisation
     */
    private analyzeOptimizationContext;
}
//# sourceMappingURL=PerformanceAgent.d.ts.map