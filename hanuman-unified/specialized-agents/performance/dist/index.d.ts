#!/usr/bin/env node
import { PerformanceAgent } from './core/PerformanceAgent';
import { PerformanceServer } from './api/server';
import { loadConfig } from './utils/config';
import { createLogger } from './utils/logger';
/**
 * Fonction utilitaire pour les tests
 */
export declare function createTestAgent(config?: any): Promise<PerformanceAgent>;
/**
 * Fonction utilitaire pour les tests du serveur
 */
export declare function createTestServer(agent: PerformanceAgent, config?: any): Promise<PerformanceServer>;
export { PerformanceAgent, PerformanceServer, loadConfig, createLogger };
//# sourceMappingURL=index.d.ts.map