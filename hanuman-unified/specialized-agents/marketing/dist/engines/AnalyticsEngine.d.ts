import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { MarketingAnalytics, Campaign } from '../types';
import { MarketingMemory } from '../memory/MarketingMemory';
/**
 * Moteur d'analyse marketing
 * Responsable de la collecte et analyse des données marketing
 */
export declare class AnalyticsEngine extends EventEmitter {
    private logger;
    private memory;
    private isInitialized;
    constructor(logger: Logger, memory: MarketingMemory);
    initialize(): Promise<void>;
    collectData(period: {
        start: Date;
        end: Date;
    }): Promise<any>;
    generateAnalytics(rawData: any, period: {
        start: Date;
        end: Date;
    }): Promise<MarketingAnalytics>;
    analyzeCampaignPerformance(campaign: Campaign): Promise<any>;
    generateInsights(analytics: MarketingAnalytics): Promise<string[]>;
    private collectCampaignData;
    private collectChannelData;
    private collectAudienceData;
    private collectConversionData;
    private collectExternalData;
    private generateOverview;
    private generateCampaignAnalytics;
    private generateChannelAnalytics;
    private generateAudienceAnalytics;
    private generateConversionAnalytics;
    private generateTrendAnalysis;
    private calculateOverallPerformance;
    private analyzeChannelPerformance;
    private analyzeContentPerformance;
    private analyzeAudiencePerformance;
    private generatePerformanceRecommendations;
    private categorizePerformance;
    private getChannelRecommendations;
}
//# sourceMappingURL=AnalyticsEngine.d.ts.map