import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { ABTest, Campaign, ABTestResults } from '../types';
import { MarketingMemory } from '../memory/MarketingMemory';
/**
 * Gestionnaire de tests A/B
 * Responsable de la création et gestion des tests A/B
 */
export declare class ABTestManager extends EventEmitter {
    private logger;
    private memory;
    private isInitialized;
    private activeTests;
    constructor(logger: Logger, memory: MarketingMemory);
    initialize(): Promise<void>;
    setupABTest(campaign: Campaign): Promise<ABTest>;
    analyzeTestResults(testId: string): Promise<ABTestResults>;
    private generateVariation;
}
//# sourceMappingURL=ABTestManager.d.ts.map