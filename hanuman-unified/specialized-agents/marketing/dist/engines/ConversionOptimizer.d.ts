import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { Campaign } from '../types';
import { MarketingMemory } from '../memory/MarketingMemory';
/**
 * Optimiseur de conversion
 * Responsable de l'optimisation des campagnes pour maximiser les conversions
 */
export declare class ConversionOptimizer extends EventEmitter {
    private logger;
    private memory;
    private isInitialized;
    constructor(logger: Logger, memory: MarketingMemory);
    initialize(): Promise<void>;
    optimizeCampaign(campaign: Campaign): Promise<Campaign>;
    generateOptimizations(campaign: Campaign, performance: any): Promise<any[]>;
    applyOptimizations(campaign: Campaign, optimizations: any[]): Promise<Campaign>;
    private optimizeTargeting;
    private optimizeContent;
    private optimizeBudget;
}
//# sourceMappingURL=ConversionOptimizer.d.ts.map