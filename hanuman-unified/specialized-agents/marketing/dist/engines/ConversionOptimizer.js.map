{"version": 3, "file": "ConversionOptimizer.js", "sourceRoot": "", "sources": ["../../src/engines/ConversionOptimizer.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAKtC;;;GAGG;AACH,MAAa,mBAAoB,SAAQ,qBAAY;IAKnD,YAAY,MAAc,EAAE,MAAuB;QACjD,KAAK,EAAE,CAAC;QAHF,kBAAa,GAAY,KAAK,CAAC;QAIrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACrE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAkB;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhE,yBAAyB;QACzB,MAAM,iBAAiB,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;QAE1C,4BAA4B;QAC5B,iBAAiB,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAE/E,0BAA0B;QAC1B,iBAAiB,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAEzE,yBAAyB;QACzB,iBAAiB,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEzF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;QACvD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAkB,EAAE,WAAgB;QAC9D,MAAM,aAAa,GAAG,EAAE,CAAC;QAEzB,2BAA2B;QAC3B,IAAI,WAAW,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,iDAAiD;gBAC9D,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,WAAW,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YACrC,aAAa,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,kCAAkC;gBAC/C,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,QAAkB,EAAE,aAAoB;QAC/D,MAAM,iBAAiB,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;QAE1C,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;YACzC,QAAQ,YAAY,CAAC,IAAI,EAAE,CAAC;gBAC1B,KAAK,SAAS;oBACZ,iBAAiB,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;oBACzE,MAAM;gBACR,KAAK,WAAW;oBACd,iBAAiB,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;oBAC/E,MAAM;gBACR,KAAK,QAAQ;oBACX,iBAAiB,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBACzF,MAAM;YACV,CAAC;QACH,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,SAAc;QAC5C,sCAAsC;QACtC,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAc;QAC1C,oCAAoC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAW,EAAE,QAAe;QACvD,mCAAmC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA/FD,kDA+FC"}