import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { 
  Project, 
  Task, 
  ProjectPlan, 
  ProjectReport, 
  Risk,
  TeamMember,
  Notification,
  Dashboard,
  AgentConfig
} from '../types';
import { ProjectPlanningEngine } from '../engines/ProjectPlanningEngine';
import { TaskManagementEngine } from '../engines/TaskManagementEngine';
import { ResourceAllocationEngine } from '../engines/ResourceAllocationEngine';
import { RiskManagementEngine } from '../engines/RiskManagementEngine';
import { ReportingEngine } from '../engines/ReportingEngine';
import { NotificationEngine } from '../engines/NotificationEngine';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';

/**
 * Agent Project Manager - Gestion de projet et coordination d'équipes
 */
export class ProjectManagerAgent extends EventEmitter {
  private logger: Logger;
  private config: AgentConfig;
  private projectPlanningEngine: ProjectPlanningEngine;
  private taskManagementEngine: TaskManagementEngine;
  private resourceAllocationEngine: ResourceAllocationEngine;
  private riskManagementEngine: RiskManagementEngine;
  private reportingEngine: ReportingEngine;
  private notificationEngine: NotificationEngine;
  private memory: WeaviateMemory;
  private communication: KafkaCommunication;
  private isInitialized: boolean = false;

  constructor(config: AgentConfig, logger: Logger) {
    super();
    this.config = config;
    this.logger = logger;
    
    // Initialiser les engines
    this.projectPlanningEngine = new ProjectPlanningEngine(config, logger);
    this.taskManagementEngine = new TaskManagementEngine(config, logger);
    this.resourceAllocationEngine = new ResourceAllocationEngine(config, logger);
    this.riskManagementEngine = new RiskManagementEngine(config, logger);
    this.reportingEngine = new ReportingEngine(config, logger);
    this.notificationEngine = new NotificationEngine(config, logger);
    
    // Initialiser la mémoire et communication
    this.memory = new WeaviateMemory(config.weaviate, logger);
    this.communication = new KafkaCommunication(config.kafka, logger);
  }

  /**
   * Initialise l'agent
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing Project Manager Agent...');

      // Initialiser les composants
      await this.memory.initialize();
      await this.communication.initialize();
      await this.projectPlanningEngine.initialize();
      await this.taskManagementEngine.initialize();
      await this.resourceAllocationEngine.initialize();
      await this.riskManagementEngine.initialize();
      await this.reportingEngine.initialize();
      await this.notificationEngine.initialize();

      // Configurer les listeners
      this.setupEventListeners();

      this.isInitialized = true;
      this.logger.info('Project Manager Agent initialized successfully');
      this.emit('initialized');
    } catch (error) {
      this.logger.error('Failed to initialize Project Manager Agent:', error);
      throw error;
    }
  }

  /**
   * Crée un nouveau projet
   */
  async createProject(projectData: Partial<Project>): Promise<Project> {
    try {
      this.logger.info(`Creating new project: ${projectData.name}`);

      // Valider les données du projet
      this.validateProjectData(projectData);

      // Créer le projet
      const project = await this.projectPlanningEngine.createProject(projectData);

      // Générer le plan de projet
      const projectPlan = await this.projectPlanningEngine.generateProjectPlan(project);

      // Allouer les ressources
      await this.resourceAllocationEngine.allocateResources(project, projectPlan);

      // Identifier les risques initiaux
      const risks = await this.riskManagementEngine.identifyInitialRisks(project);
      project.risks = risks;

      // Sauvegarder le projet
      await this.memory.storeProject(project);
      await this.memory.storeProjectPlan(projectPlan);

      // Notifier les parties prenantes
      await this.notificationEngine.notifyProjectCreation(project);

      // Publier l'événement
      await this.communication.publishProjectCreated(project);

      this.logger.info(`Project created successfully: ${project.id}`);
      return project;
    } catch (error) {
      this.logger.error('Project creation failed:', error);
      throw error;
    }
  }

  /**
   * Met à jour un projet
   */
  async updateProject(projectId: string, updates: Partial<Project>): Promise<Project> {
    try {
      this.logger.info(`Updating project: ${projectId}`);

      // Récupérer le projet existant
      const existingProject = await this.memory.getProject(projectId);
      if (!existingProject) {
        throw new Error(`Project not found: ${projectId}`);
      }

      // Appliquer les mises à jour
      const updatedProject = { ...existingProject, ...updates };

      // Recalculer le plan si nécessaire
      if (this.shouldRecalculatePlan(updates)) {
        const newPlan = await this.projectPlanningEngine.updateProjectPlan(updatedProject);
        await this.memory.storeProjectPlan(newPlan);
      }

      // Sauvegarder les changements
      await this.memory.storeProject(updatedProject);

      // Notifier les changements
      await this.notificationEngine.notifyProjectUpdate(updatedProject, updates);

      this.logger.info(`Project updated successfully: ${projectId}`);
      return updatedProject;
    } catch (error) {
      this.logger.error('Project update failed:', error);
      throw error;
    }
  }

  /**
   * Crée une nouvelle tâche
   */
  async createTask(projectId: string, taskData: Partial<Task>): Promise<Task> {
    try {
      this.logger.info(`Creating task for project ${projectId}: ${taskData.name}`);

      // Créer la tâche
      const task = await this.taskManagementEngine.createTask(projectId, taskData);

      // Mettre à jour le plan de projet
      await this.projectPlanningEngine.addTaskToPlan(projectId, task);

      // Vérifier les conflits de ressources
      await this.resourceAllocationEngine.checkResourceConflicts(projectId, task);

      // Sauvegarder la tâche
      await this.memory.storeTask(task);

      // Notifier l'assigné
      if (task.assignee) {
        await this.notificationEngine.notifyTaskAssignment(task);
      }

      this.logger.info(`Task created successfully: ${task.id}`);
      return task;
    } catch (error) {
      this.logger.error('Task creation failed:', error);
      throw error;
    }
  }

  /**
   * Met à jour une tâche
   */
  async updateTask(taskId: string, updates: Partial<Task>): Promise<Task> {
    try {
      this.logger.info(`Updating task: ${taskId}`);

      const updatedTask = await this.taskManagementEngine.updateTask(taskId, updates);

      // Mettre à jour le plan de projet si nécessaire
      if (this.shouldUpdatePlan(updates)) {
        await this.projectPlanningEngine.updateTaskInPlan(updatedTask);
      }

      // Sauvegarder les changements
      await this.memory.storeTask(updatedTask);

      // Notifier les changements
      await this.notificationEngine.notifyTaskUpdate(updatedTask, updates);

      this.logger.info(`Task updated successfully: ${taskId}`);
      return updatedTask;
    } catch (error) {
      this.logger.error('Task update failed:', error);
      throw error;
    }
  }

  /**
   * Alloue des ressources à un projet
   */
  async allocateResources(projectId: string, resourceRequirements: any): Promise<void> {
    try {
      this.logger.info(`Allocating resources for project: ${projectId}`);

      const project = await this.memory.getProject(projectId);
      if (!project) {
        throw new Error(`Project not found: ${projectId}`);
      }

      const allocation = await this.resourceAllocationEngine.optimizeResourceAllocation(
        project, 
        resourceRequirements
      );

      // Sauvegarder l'allocation
      await this.memory.storeResourceAllocation(projectId, allocation);

      // Notifier les conflits s'il y en a
      if (allocation.conflicts.length > 0) {
        await this.notificationEngine.notifyResourceConflicts(allocation.conflicts);
      }

      this.logger.info(`Resources allocated successfully for project: ${projectId}`);
    } catch (error) {
      this.logger.error('Resource allocation failed:', error);
      throw error;
    }
  }

  /**
   * Évalue et gère les risques
   */
  async assessRisks(projectId: string): Promise<Risk[]> {
    try {
      this.logger.info(`Assessing risks for project: ${projectId}`);

      const project = await this.memory.getProject(projectId);
      if (!project) {
        throw new Error(`Project not found: ${projectId}`);
      }

      const risks = await this.riskManagementEngine.assessProjectRisks(project);

      // Mettre à jour les risques du projet
      project.risks = risks;
      await this.memory.storeProject(project);

      // Notifier les risques critiques
      const criticalRisks = risks.filter(r => r.riskScore > 0.8);
      if (criticalRisks.length > 0) {
        await this.notificationEngine.notifyCriticalRisks(criticalRisks);
      }

      this.logger.info(`Risk assessment completed for project: ${projectId}`);
      return risks;
    } catch (error) {
      this.logger.error('Risk assessment failed:', error);
      throw error;
    }
  }

  /**
   * Génère un rapport de projet
   */
  async generateProjectReport(projectId: string, reportType: string): Promise<ProjectReport> {
    try {
      this.logger.info(`Generating ${reportType} report for project: ${projectId}`);

      const project = await this.memory.getProject(projectId);
      if (!project) {
        throw new Error(`Project not found: ${projectId}`);
      }

      const report = await this.reportingEngine.generateReport(project, reportType);

      // Sauvegarder le rapport
      await this.memory.storeProjectReport(report);

      this.logger.info(`Report generated successfully: ${report.id}`);
      return report;
    } catch (error) {
      this.logger.error('Report generation failed:', error);
      throw error;
    }
  }

  /**
   * Coordonne les activités entre agents
   */
  async coordinateAgentActivities(activities: any[]): Promise<void> {
    try {
      this.logger.info(`Coordinating ${activities.length} agent activities`);

      // Analyser les dépendances entre activités
      const dependencies = this.analyzeDependencies(activities);

      // Optimiser l'ordre d'exécution
      const optimizedSchedule = await this.projectPlanningEngine.optimizeSchedule(activities, dependencies);

      // Publier le planning coordonné
      await this.communication.publishCoordinatedSchedule(optimizedSchedule);

      this.logger.info('Agent activities coordinated successfully');
    } catch (error) {
      this.logger.error('Agent coordination failed:', error);
      throw error;
    }
  }

  /**
   * Surveille les performances du projet
   */
  async monitorProjectPerformance(projectId: string): Promise<any> {
    try {
      this.logger.info(`Monitoring performance for project: ${projectId}`);

      const project = await this.memory.getProject(projectId);
      if (!project) {
        throw new Error(`Project not found: ${projectId}`);
      }

      const performance = await this.reportingEngine.calculateProjectPerformance(project);

      // Identifier les problèmes de performance
      const issues = this.identifyPerformanceIssues(performance);

      // Générer des recommandations
      const recommendations = await this.generatePerformanceRecommendations(performance, issues);

      // Notifier si nécessaire
      if (issues.length > 0) {
        await this.notificationEngine.notifyPerformanceIssues(projectId, issues);
      }

      return {
        performance,
        issues,
        recommendations
      };
    } catch (error) {
      this.logger.error('Performance monitoring failed:', error);
      throw error;
    }
  }

  /**
   * Crée un dashboard de projet
   */
  async createProjectDashboard(projectId: string, config: any): Promise<Dashboard> {
    try {
      this.logger.info(`Creating dashboard for project: ${projectId}`);

      const dashboard = await this.reportingEngine.createProjectDashboard(projectId, config);

      // Sauvegarder le dashboard
      await this.memory.storeDashboard(dashboard);

      this.logger.info(`Dashboard created successfully: ${dashboard.id}`);
      return dashboard;
    } catch (error) {
      this.logger.error('Dashboard creation failed:', error);
      throw error;
    }
  }

  /**
   * Configure les listeners d'événements
   */
  private setupEventListeners(): void {
    // Écouter les demandes de création de projet
    this.communication.on('project_creation_request', async (projectData: Partial<Project>) => {
      try {
        const project = await this.createProject(projectData);
        await this.communication.publishProjectCreated(project);
      } catch (error) {
        this.logger.error('Failed to handle project creation request:', error);
      }
    });

    // Écouter les mises à jour de tâches
    this.communication.on('task_update', async (taskUpdate: any) => {
      try {
        await this.updateTask(taskUpdate.taskId, taskUpdate.updates);
      } catch (error) {
        this.logger.error('Failed to handle task update:', error);
      }
    });

    // Écouter les demandes de coordination
    this.communication.on('coordination_request', async (activities: any[]) => {
      try {
        await this.coordinateAgentActivities(activities);
      } catch (error) {
        this.logger.error('Failed to handle coordination request:', error);
      }
    });
  }

  /**
   * Valide les données du projet
   */
  private validateProjectData(projectData: Partial<Project>): void {
    if (!projectData.name || projectData.name.trim().length === 0) {
      throw new Error('Project name is required');
    }

    if (!projectData.startDate) {
      throw new Error('Project start date is required');
    }

    if (!projectData.endDate) {
      throw new Error('Project end date is required');
    }

    if (projectData.startDate >= projectData.endDate) {
      throw new Error('Project end date must be after start date');
    }
  }

  /**
   * Détermine si le plan doit être recalculé
   */
  private shouldRecalculatePlan(updates: Partial<Project>): boolean {
    const criticalFields = ['startDate', 'endDate', 'team', 'tasks', 'budget'];
    return criticalFields.some(field => field in updates);
  }

  /**
   * Détermine si le plan doit être mis à jour
   */
  private shouldUpdatePlan(updates: Partial<Task>): boolean {
    const criticalFields = ['startDate', 'endDate', 'dependencies', 'assignee', 'estimatedHours'];
    return criticalFields.some(field => field in updates);
  }

  /**
   * Analyse les dépendances entre activités
   */
  private analyzeDependencies(activities: any[]): any[] {
    // Implémentation simplifiée
    return [];
  }

  /**
   * Identifie les problèmes de performance
   */
  private identifyPerformanceIssues(performance: any): any[] {
    const issues = [];

    if (performance.scheduleVariance < -0.1) {
      issues.push({
        type: 'schedule_delay',
        severity: 'high',
        description: 'Project is significantly behind schedule'
      });
    }

    if (performance.budgetVariance > 0.1) {
      issues.push({
        type: 'budget_overrun',
        severity: 'high',
        description: 'Project is over budget'
      });
    }

    return issues;
  }

  /**
   * Génère des recommandations de performance
   */
  private async generatePerformanceRecommendations(performance: any, issues: any[]): Promise<any[]> {
    const recommendations = [];

    for (const issue of issues) {
      switch (issue.type) {
        case 'schedule_delay':
          recommendations.push({
            type: 'action',
            title: 'Address Schedule Delays',
            description: 'Implement fast-tracking or crashing techniques',
            priority: 'high'
          });
          break;
        case 'budget_overrun':
          recommendations.push({
            type: 'action',
            title: 'Control Budget',
            description: 'Review and optimize resource allocation',
            priority: 'high'
          });
          break;
      }
    }

    return recommendations;
  }

  /**
   * Arrête l'agent
   */
  async shutdown(): Promise<void> {
    try {
      this.logger.info('Shutting down Project Manager Agent...');

      await this.notificationEngine.stopNotifications();
      await this.communication.disconnect();
      await this.memory.disconnect();

      this.isInitialized = false;
      this.logger.info('Project Manager Agent shut down successfully');
    } catch (error) {
      this.logger.error('Error during shutdown:', error);
      throw error;
    }
  }
}
