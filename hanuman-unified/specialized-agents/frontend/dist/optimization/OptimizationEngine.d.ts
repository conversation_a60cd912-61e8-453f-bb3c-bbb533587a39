import { Logger } from 'winston';
import { GeneratedCode, CodeGenerationRequest } from '../types';
/**
 * Moteur d'Optimisation du Code Frontend
 *
 * Optimise le code généré pour améliorer les performances,
 * réduire la taille des bundles et améliorer l'expérience utilisateur.
 */
export declare class OptimizationEngine {
    private logger;
    constructor(logger: Logger);
    /**
     * Optimise le code généré
     */
    optimizeCode(generatedCode: GeneratedCode, request: CodeGenerationRequest): Promise<GeneratedCode>;
    /**
     * Optimise les imports pour réduire la taille du bundle
     */
    private optimizeImports;
    /**
     * Optimise les composants pour les performances
     */
    private optimizeComponents;
    /**
     * Optimise les styles CSS/SCSS
     */
    private optimizeStyles;
    /**
     * Optimise les assets (images, fonts, etc.)
     */
    private optimizeAssets;
    /**
     * Optimise la configuration du bundle
     */
    private optimizeBundle;
    /**
     * Optimise l'accessibilité
     */
    private optimizeAccessibility;
    /**
     * Optimise pour le SEO
     */
    private optimizeSEO;
    private optimizeLibraryImports;
    private removeUnusedImports;
    private groupImports;
    private addReactMemo;
    private addPerformanceHooks;
    private addLazyLoading;
    private optimizeEventHandlers;
    private minifyCSS;
    private removeUnusedCSS;
    private optimizeSelectors;
    private addVendorPrefixes;
    private generateImageOptimizations;
    private generateFontOptimizations;
    private generateCodeSplittingConfig;
    private generateCompressionConfig;
    private generateCacheConfig;
    private calculateTotalSize;
    private getAppliedOptimizations;
    private calculatePerformanceMetrics;
    private calculateAccessibilityMetrics;
    private addMissingAriaAttributes;
    private optimizeKeyboardNavigation;
    private addSkipLinks;
    private optimizeColorContrast;
    private generateMetaTags;
    private generateSitemap;
    private generateStructuredData;
}
//# sourceMappingURL=OptimizationEngine.d.ts.map