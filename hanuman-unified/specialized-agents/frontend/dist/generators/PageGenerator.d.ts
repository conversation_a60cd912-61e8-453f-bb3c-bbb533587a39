import { Logger } from 'winston';
import { GeneratedFile, CodeGenerationRequest, ComprehensiveUXDesign } from '../types';
/**
 * Générateur de Pages
 *
 * Génère des pages complètes basées sur les wireframes
 * et les parcours utilisateur définis dans le design.
 */
export declare class PageGenerator {
    private logger;
    private framework;
    constructor(logger: Logger, framework: string);
    /**
     * Génère une page basée sur les wireframes
     */
    generatePage(pageName: string, wireframe: any, design: ComprehensiveUXDesign, request: CodeGenerationRequest): Promise<GeneratedFile>;
    /**
     * Génère la page d'accueil optimisée pour la conversion
     */
    private generateHomePage;
    /**
     * Génère la page de recherche avec filtres avancés
     */
    private generateSearchPage;
    private extractDependencies;
    private extractImports;
    private generateBookingPage;
    private generateDashboardPage;
    private generateProfilePage;
    private generateGenericPage;
}
//# sourceMappingURL=PageGenerator.d.ts.map