"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactGenerator = void 0;
const ComponentGenerator_1 = require("./ComponentGenerator");
const PageGenerator_1 = require("./PageGenerator");
const HookGenerator_1 = require("./HookGenerator");
const ServiceGenerator_1 = require("./ServiceGenerator");
/**
 * Générateur React Spécialisé
 *
 * Génère du code React optimisé à partir des designs UI/UX,
 * incluant composants, pages, hooks, services et configuration.
 */
class ReactGenerator {
    constructor(logger, memory) {
        this.logger = logger;
        this.memory = memory;
        // Initialiser les générateurs spécialisés
        this.componentGenerator = new ComponentGenerator_1.ComponentGenerator(logger, 'react');
        this.pageGenerator = new PageGenerator_1.PageGenerator(logger, 'react');
        this.hookGenerator = new HookGenerator_1.HookGenerator(logger, 'react');
        this.serviceGenerator = new ServiceGenerator_1.ServiceGenerator(logger, 'react');
    }
    /**
     * Génère une application React complète à partir d'un design
     */
    async generateFromDesign(design, request, templates) {
        this.logger.info('Génération d\'application React', {
            typescript: request.typescript,
            styling: request.styling
        });
        try {
            const generatedFiles = [];
            // 1. Génération de la structure de base
            this.logger.info('Génération de la structure de base');
            generatedFiles.push(...await this.generateProjectStructure(design, request));
            // 2. Génération des composants UI
            this.logger.info('Génération des composants UI');
            generatedFiles.push(...await this.generateUIComponents(design, request, templates));
            // 3. Génération des pages
            this.logger.info('Génération des pages');
            generatedFiles.push(...await this.generatePages(design, request, templates));
            // 4. Génération des hooks personnalisés
            this.logger.info('Génération des hooks');
            generatedFiles.push(...await this.generateCustomHooks(design, request));
            // 5. Génération des services
            this.logger.info('Génération des services');
            generatedFiles.push(...await this.generateServices(design, request));
            // 6. Génération du routing
            if (request.features.routing) {
                this.logger.info('Génération du routing');
                generatedFiles.push(...await this.generateRouting(design, request));
            }
            // 7. Génération de la gestion d'état
            if (request.stateManagement !== 'none') {
                this.logger.info('Génération de la gestion d\'état');
                generatedFiles.push(...await this.generateStateManagement(design, request));
            }
            // 8. Génération des styles
            this.logger.info('Génération des styles');
            generatedFiles.push(...await this.generateStyles(design, request));
            // 9. Génération de la configuration
            this.logger.info('Génération de la configuration');
            generatedFiles.push(...await this.generateConfiguration(design, request));
            // Assemblage du code généré
            const generatedCode = {
                id: `react-${Date.now()}`,
                framework: 'react',
                files: generatedFiles,
                structure: await this.generateProjectStructureInfo(design, request),
                dependencies: await this.generateDependencies(request),
                scripts: await this.generateScripts(request),
                configuration: await this.generateProjectConfiguration(request),
                documentation: {}, // Sera rempli par l'agent principal
                tests: [], // Sera rempli par l'agent principal
                metadata: {
                    generatedAt: new Date(),
                    generatedBy: 'agent-frontend-react',
                    designVersion: '1.0.0',
                    codeVersion: '1.0.0',
                    framework: 'react',
                    features: Object.keys(request.features).filter(key => request.features[key]),
                    metrics: await this.calculateMetrics(generatedFiles),
                    quality: await this.calculateQualityMetrics(generatedFiles, design)
                }
            };
            this.logger.info('Application React générée avec succès', {
                filesCount: generatedFiles.length,
                totalSize: generatedFiles.reduce((sum, file) => sum + file.size, 0)
            });
            return generatedCode;
        }
        catch (error) {
            this.logger.error('Erreur lors de la génération React', { error: error.message });
            throw error;
        }
    }
    /**
     * Génère la structure de base du projet
     */
    async generateProjectStructure(design, request) {
        const files = [];
        // package.json
        files.push({
            path: 'package.json',
            content: await this.generatePackageJson(design, request),
            type: 'config',
            language: 'json',
            size: 0,
            dependencies: [],
            exports: [],
            imports: []
        });
        // README.md
        files.push({
            path: 'README.md',
            content: await this.generateReadme(design, request),
            type: 'config',
            language: 'md',
            size: 0,
            dependencies: [],
            exports: [],
            imports: []
        });
        // index.html
        files.push({
            path: 'public/index.html',
            content: await this.generateIndexHtml(design, request),
            type: 'config',
            language: 'html',
            size: 0,
            dependencies: [],
            exports: [],
            imports: []
        });
        // App component
        const appExtension = request.typescript ? 'tsx' : 'jsx';
        files.push({
            path: `src/App.${appExtension}`,
            content: await this.generateAppComponent(design, request),
            type: 'component',
            language: request.typescript ? 'typescript' : 'javascript',
            size: 0,
            dependencies: [],
            exports: ['App'],
            imports: []
        });
        // Main entry point
        files.push({
            path: `src/index.${appExtension}`,
            content: await this.generateMainEntry(design, request),
            type: 'config',
            language: request.typescript ? 'typescript' : 'javascript',
            size: 0,
            dependencies: [],
            exports: [],
            imports: ['App']
        });
        // TypeScript configuration si nécessaire
        if (request.typescript) {
            files.push({
                path: 'tsconfig.json',
                content: await this.generateTsConfig(request),
                type: 'config',
                language: 'json',
                size: 0,
                dependencies: [],
                exports: [],
                imports: []
            });
        }
        return files;
    }
    /**
     * Génère les composants UI à partir du design system
     */
    async generateUIComponents(design, request, templates) {
        const files = [];
        const { componentLibrary, designSystem } = design;
        // Générer les composants de base
        const baseComponents = [
            'Button',
            'Input',
            'Card',
            'Modal',
            'Navigation',
            'Header',
            'Footer',
            'Layout'
        ];
        for (const componentName of baseComponents) {
            const componentFile = await this.componentGenerator.generateComponent(componentName, designSystem, componentLibrary[componentName] || {}, request);
            files.push(componentFile);
        }
        // Générer les composants spécialisés pour Retreat And Be
        const specializedComponents = [
            'RetreatCard',
            'BookingForm',
            'FilterPanel',
            'ReviewComponent',
            'PartnerProfile',
            'SearchBar',
            'PricingCard',
            'TrustSignals'
        ];
        for (const componentName of specializedComponents) {
            const componentFile = await this.componentGenerator.generateSpecializedComponent(componentName, design, request);
            files.push(componentFile);
        }
        return files;
    }
    /**
     * Génère les pages à partir des wireframes
     */
    async generatePages(design, request, templates) {
        const files = [];
        const { wireframes } = design;
        // Pages principales
        const pages = [
            { name: 'HomePage', wireframe: wireframes.landingPages },
            { name: 'SearchPage', wireframe: wireframes.userFlows },
            { name: 'BookingPage', wireframe: wireframes.signupFlow },
            { name: 'DashboardPage', wireframe: wireframes.dashboard },
            { name: 'ProfilePage', wireframe: wireframes.onboarding }
        ];
        for (const page of pages) {
            const pageFile = await this.pageGenerator.generatePage(page.name, page.wireframe, design, request);
            files.push(pageFile);
        }
        return files;
    }
    /**
     * Génère les hooks personnalisés
     */
    async generateCustomHooks(design, request) {
        const files = [];
        // Hooks de base
        const hooks = [
            'useAuth',
            'useApi',
            'useLocalStorage',
            'useDebounce',
            'useIntersectionObserver',
            'useRetreatSearch',
            'useBooking',
            'useUserPreferences'
        ];
        for (const hookName of hooks) {
            const hookFile = await this.hookGenerator.generateHook(hookName, design, request);
            files.push(hookFile);
        }
        return files;
    }
    /**
     * Génère les services
     */
    async generateServices(design, request) {
        const files = [];
        // Services principaux
        const services = [
            'apiService',
            'authService',
            'retreatService',
            'bookingService',
            'userService',
            'analyticsService'
        ];
        for (const serviceName of services) {
            const serviceFile = await this.serviceGenerator.generateService(serviceName, design, request);
            files.push(serviceFile);
        }
        return files;
    }
    /**
     * Génère le système de routing
     */
    async generateRouting(design, request) {
        const files = [];
        const extension = request.typescript ? 'tsx' : 'jsx';
        // Router principal
        files.push({
            path: `src/router/AppRouter.${extension}`,
            content: await this.generateAppRouter(design, request),
            type: 'component',
            language: request.typescript ? 'typescript' : 'javascript',
            size: 0,
            dependencies: ['react-router-dom'],
            exports: ['AppRouter'],
            imports: []
        });
        // Routes configuration
        files.push({
            path: `src/router/routes.${request.typescript ? 'ts' : 'js'}`,
            content: await this.generateRoutesConfig(design, request),
            type: 'config',
            language: request.typescript ? 'typescript' : 'javascript',
            size: 0,
            dependencies: [],
            exports: ['routes'],
            imports: []
        });
        return files;
    }
    /**
     * Génère la gestion d'état
     */
    async generateStateManagement(design, request) {
        const files = [];
        switch (request.stateManagement) {
            case 'zustand':
                files.push(...await this.generateZustandStore(design, request));
                break;
            case 'redux':
                files.push(...await this.generateReduxStore(design, request));
                break;
            case 'context':
                files.push(...await this.generateContextStore(design, request));
                break;
        }
        return files;
    }
    /**
     * Génère les styles
     */
    async generateStyles(design, request) {
        const files = [];
        const { designSystem } = design;
        switch (request.styling) {
            case 'tailwind':
                files.push(...await this.generateTailwindStyles(designSystem, request));
                break;
            case 'scss':
                files.push(...await this.generateScssStyles(designSystem, request));
                break;
            case 'styled-components':
                files.push(...await this.generateStyledComponents(designSystem, request));
                break;
            default:
                files.push(...await this.generateCssStyles(designSystem, request));
        }
        return files;
    }
    /**
     * Génère la configuration du projet
     */
    async generateConfiguration(design, request) {
        const files = [];
        // Vite/Webpack config
        if (request.buildTool === 'vite') {
            files.push({
                path: 'vite.config.ts',
                content: await this.generateViteConfig(request),
                type: 'config',
                language: 'typescript',
                size: 0,
                dependencies: [],
                exports: [],
                imports: []
            });
        }
        // ESLint config
        files.push({
            path: '.eslintrc.json',
            content: await this.generateEslintConfig(request),
            type: 'config',
            language: 'json',
            size: 0,
            dependencies: [],
            exports: [],
            imports: []
        });
        // Prettier config
        files.push({
            path: '.prettierrc',
            content: await this.generatePrettierConfig(),
            type: 'config',
            language: 'json',
            size: 0,
            dependencies: [],
            exports: [],
            imports: []
        });
        return files;
    }
    // Méthodes utilitaires pour la génération de contenu
    async generatePackageJson(design, request) {
        const packageJson = {
            name: design.userResearch?.industry || 'retreat-and-be-app',
            version: '1.0.0',
            private: true,
            dependencies: {
                'react': '^18.2.0',
                'react-dom': '^18.2.0',
                ...(request.typescript && { '@types/react': '^18.2.0', '@types/react-dom': '^18.2.0' }),
                ...(request.features.routing && { 'react-router-dom': '^6.8.0' }),
                ...(request.stateManagement === 'zustand' && { 'zustand': '^4.4.0' }),
                ...(request.stateManagement === 'redux' && { '@reduxjs/toolkit': '^1.9.0', 'react-redux': '^8.1.0' }),
                ...(request.styling === 'tailwind' && { 'tailwindcss': '^3.3.0' }),
                ...(request.styling === 'styled-components' && { 'styled-components': '^6.0.0' })
            },
            devDependencies: {
                ...(request.buildTool === 'vite' && { 'vite': '^4.4.0', '@vitejs/plugin-react': '^4.0.0' }),
                ...(request.typescript && { 'typescript': '^5.0.0' }),
                ...(request.testing === 'jest' && { 'jest': '^29.0.0', '@testing-library/react': '^13.0.0' })
            },
            scripts: {
                'dev': request.buildTool === 'vite' ? 'vite' : 'react-scripts start',
                'build': request.buildTool === 'vite' ? 'vite build' : 'react-scripts build',
                'preview': 'vite preview',
                'test': request.testing === 'jest' ? 'jest' : 'echo "No tests specified"',
                'lint': 'eslint src --ext .ts,.tsx,.js,.jsx',
                'lint:fix': 'eslint src --ext .ts,.tsx,.js,.jsx --fix'
            }
        };
        return JSON.stringify(packageJson, null, 2);
    }
    async generateAppComponent(design, request) {
        const isTypeScript = request.typescript;
        const hasRouting = request.features.routing;
        return `import React from 'react';
${hasRouting ? "import { BrowserRouter } from 'react-router-dom';" : ''}
${hasRouting ? "import AppRouter from './router/AppRouter';" : "import HomePage from './pages/HomePage';"}
${request.styling === 'tailwind' ? "import './index.css';" : "import './App.css';"}

${isTypeScript ? 'const App: React.FC = () => {' : 'function App() {'}
  return (
    <div className="App">
      ${hasRouting ? `
      <BrowserRouter>
        <AppRouter />
      </BrowserRouter>
      ` : '<HomePage />'}
    </div>
  );
${isTypeScript ? '};' : '}'}

export default App;`;
    }
    // Méthodes de calcul de métriques (simplifiées)
    async calculateMetrics(files) {
        return {
            totalFiles: files.length,
            totalLines: files.reduce((sum, file) => sum + file.content.split('\n').length, 0),
            totalSize: files.reduce((sum, file) => sum + file.size, 0),
            componentCount: files.filter(f => f.type === 'component').length,
            pageCount: files.filter(f => f.type === 'page').length,
            hookCount: files.filter(f => f.type === 'hook').length,
            serviceCount: files.filter(f => f.type === 'service').length,
            testCount: files.filter(f => f.type === 'test').length,
            complexity: 1.0
        };
    }
    async calculateQualityMetrics(files, design) {
        return {
            accessibility: 0.9,
            performance: 0.85,
            seo: 0.8,
            maintainability: 0.9,
            testCoverage: 0.7,
            codeQuality: 0.85,
            security: 0.9
        };
    }
    // Méthodes de génération simplifiées (à implémenter complètement)
    async generateReadme(design, request) {
        return `# ${design.userResearch?.industry || 'Retreat And Be'} App\n\nApplication React générée automatiquement.`;
    }
    async generateIndexHtml(design, request) {
        return `<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${design.userResearch?.industry || 'Retreat And Be'}</title>
</head>
<body>
  <div id="root"></div>
</body>
</html>`;
    }
    async generateMainEntry(design, request) {
        return `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root')!);
root.render(<App />);`;
    }
    async generateTsConfig(request) {
        return JSON.stringify({
            compilerOptions: {
                target: 'ES2020',
                lib: ['DOM', 'DOM.Iterable', 'ES6'],
                allowJs: true,
                skipLibCheck: true,
                esModuleInterop: true,
                allowSyntheticDefaultImports: true,
                strict: true,
                forceConsistentCasingInFileNames: true,
                moduleResolution: 'node',
                resolveJsonModule: true,
                isolatedModules: true,
                noEmit: true,
                jsx: 'react-jsx'
            },
            include: ['src']
        }, null, 2);
    }
    // Autres méthodes de génération (à implémenter)
    async generateProjectStructureInfo(design, request) { return {}; }
    async generateDependencies(request) { return {}; }
    async generateScripts(request) { return {}; }
    async generateProjectConfiguration(request) { return {}; }
    async generateAppRouter(design, request) { return ''; }
    async generateRoutesConfig(design, request) { return ''; }
    async generateZustandStore(design, request) { return []; }
    async generateReduxStore(design, request) { return []; }
    async generateContextStore(design, request) { return []; }
    async generateTailwindStyles(designSystem, request) { return []; }
    async generateScssStyles(designSystem, request) { return []; }
    async generateStyledComponents(designSystem, request) { return []; }
    async generateCssStyles(designSystem, request) { return []; }
    async generateViteConfig(request) { return ''; }
    async generateEslintConfig(request) { return ''; }
    async generatePrettierConfig() { return '{}'; }
}
exports.ReactGenerator = ReactGenerator;
//# sourceMappingURL=ReactGenerator.js.map