import { Logger } from 'winston';
import { GeneratedFile, CodeGenerationRequest, ComprehensiveUXDesign } from '../types';
/**
 * Générateur de Services
 *
 * Génère des services pour l'interaction avec les APIs,
 * la gestion des données et la logique métier.
 */
export declare class ServiceGenerator {
    private logger;
    private framework;
    constructor(logger: Logger, framework: string);
    /**
     * Génère un service
     */
    generateService(serviceName: string, design: ComprehensiveUXDesign, request: CodeGenerationRequest): Promise<GeneratedFile>;
    /**
     * Génère le service API principal
     */
    private generateApiService;
    /**
     * Génère le service de retraites
     */
    private generateRetreatService;
    private extractDependencies;
    private extractImports;
    private generateAuthService;
    private generateBookingService;
    private generateUserService;
    private generateAnalyticsService;
    private generateGenericService;
}
//# sourceMappingURL=ServiceGenerator.d.ts.map