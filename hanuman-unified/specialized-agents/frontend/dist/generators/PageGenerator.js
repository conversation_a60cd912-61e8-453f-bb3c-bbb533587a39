"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageGenerator = void 0;
/**
 * Générateur de Pages
 *
 * Génère des pages complètes basées sur les wireframes
 * et les parcours utilisateur définis dans le design.
 */
class PageGenerator {
    constructor(logger, framework) {
        this.logger = logger;
        this.framework = framework;
    }
    /**
     * Génère une page basée sur les wireframes
     */
    async generatePage(pageName, wireframe, design, request) {
        this.logger.info(`Génération de la page ${pageName}`, { framework: this.framework });
        const extension = request.typescript ? 'tsx' : 'jsx';
        let content;
        switch (pageName) {
            case 'HomePage':
                content = await this.generateHomePage(design, request);
                break;
            case 'SearchPage':
                content = await this.generateSearchPage(design, request);
                break;
            case 'BookingPage':
                content = await this.generateBookingPage(design, request);
                break;
            case 'DashboardPage':
                content = await this.generateDashboardPage(design, request);
                break;
            case 'ProfilePage':
                content = await this.generateProfilePage(design, request);
                break;
            default:
                content = await this.generateGenericPage(pageName, wireframe, design, request);
        }
        return {
            path: `src/pages/${pageName}/${pageName}.${extension}`,
            content,
            type: 'page',
            language: request.typescript ? 'typescript' : 'javascript',
            size: content.length,
            dependencies: this.extractDependencies(content),
            exports: [pageName],
            imports: this.extractImports(content)
        };
    }
    /**
     * Génère la page d'accueil optimisée pour la conversion
     */
    async generateHomePage(design, request) {
        const isTypeScript = request.typescript;
        const { wireframes, designSystem, personas } = design;
        return `import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Header/Header';
import Footer from '../components/Footer/Footer';
import Button from '../components/Button/Button';
import RetreatCard from '../components/RetreatCard/RetreatCard';
import SearchBar from '../components/SearchBar/SearchBar';
import TrustSignals from '../components/TrustSignals/TrustSignals';
import { useRetreatSearch } from '../hooks/useRetreatSearch';
import { retreatService } from '../services/retreatService';
${isTypeScript ? `
interface FeaturedRetreat {
  id: string;
  title: string;
  description: string;
  location: string;
  price: number;
  rating: number;
  images: string[];
}` : ''}

${isTypeScript ? 'const HomePage: React.FC = () => {' : 'const HomePage = () => {'}
  const navigate = useNavigate();
  const { searchRetreats } = useRetreatSearch();
  const [featuredRetreats, setFeaturedRetreats] = useState${isTypeScript ? '<FeaturedRetreat[]>' : ''}([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadFeaturedRetreats = async () => {
      try {
        const retreats = await retreatService.getFeatured();
        setFeaturedRetreats(retreats);
      } catch (error) {
        console.error('Erreur lors du chargement des retraites:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedRetreats();
  }, []);

  const handleSearch = async (query${isTypeScript ? ': string' : ''}, filters${isTypeScript ? ': any' : ''}) => {
    const results = await searchRetreats(query, filters);
    navigate('/search', { state: { results, query, filters } });
  };

  const handleBookNow = (retreatId${isTypeScript ? ': string' : ''}) => {
    navigate(\`/booking/\${retreatId}\`);
  };

  const handleViewDetails = (retreatId${isTypeScript ? ': string' : ''}) => {
    navigate(\`/retreat/\${retreatId}\`);
  };

  return (
    <div className="home-page">
      <Header />
      
      <main className="home-page__main">
        {/* Section Hero */}
        <section className="hero" aria-labelledby="hero-title">
          <div className="hero__content">
            <h1 id="hero-title" className="hero__title">
              Transformez votre vie avec des retraites authentiques
            </h1>
            <p className="hero__subtitle">
              Découvrez des retraites de bien-être soigneusement sélectionnées 
              pour vous aider à vous reconnecter, vous ressourcer et grandir.
            </p>
            
            {/* Barre de recherche principale */}
            <div className="hero__search">
              <SearchBar
                onSearch={handleSearch}
                placeholder="Où souhaitez-vous partir en retraite ?"
                showFilters={true}
                size="large"
              />
            </div>

            {/* Signaux de confiance immédiats */}
            <TrustSignals
              stats={{
                totalGuests: '2000+',
                averageRating: 4.9,
                partnersCount: 150
              }}
              guarantees={['Satisfaction garantie', 'Réservation sécurisée']}
            />
          </div>

          <div className="hero__image">
            <img
              src="/images/hero-meditation.jpg"
              alt="Personne méditant dans un cadre naturel serein"
              className="hero__image-main"
            />
          </div>
        </section>

        {/* Section Bénéfices */}
        <section className="benefits" aria-labelledby="benefits-title">
          <div className="container">
            <h2 id="benefits-title" className="benefits__title">
              Pourquoi choisir Retreat And Be ?
            </h2>
            
            <div className="benefits__grid">
              <div className="benefit-card">
                <div className="benefit-card__icon" aria-hidden="true">🧘‍♀️</div>
                <h3 className="benefit-card__title">Retraites authentiques</h3>
                <p className="benefit-card__description">
                  Partenaires vérifiés et expériences authentiques pour une transformation réelle.
                </p>
              </div>
              
              <div className="benefit-card">
                <div className="benefit-card__icon" aria-hidden="true">🌍</div>
                <h3 className="benefit-card__title">Destinations uniques</h3>
                <p className="benefit-card__description">
                  Des lieux exceptionnels dans le monde entier pour tous les goûts et budgets.
                </p>
              </div>
              
              <div className="benefit-card">
                <div className="benefit-card__icon" aria-hidden="true">💝</div>
                <h3 className="benefit-card__title">Support personnalisé</h3>
                <p className="benefit-card__description">
                  Accompagnement dédié avant, pendant et après votre retraite.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Section Retraites en vedette */}
        <section className="featured-retreats" aria-labelledby="featured-title">
          <div className="container">
            <header className="featured-retreats__header">
              <h2 id="featured-title" className="featured-retreats__title">
                Retraites en vedette
              </h2>
              <p className="featured-retreats__subtitle">
                Découvrez nos retraites les plus populaires
              </p>
            </header>

            {loading ? (
              <div className="featured-retreats__loading" role="status" aria-live="polite">
                <span className="sr-only">Chargement des retraites...</span>
                <div className="loading-spinner" aria-hidden="true">⏳</div>
              </div>
            ) : (
              <div className="featured-retreats__grid">
                {featuredRetreats.map((retreat) => (
                  <RetreatCard
                    key={retreat.id}
                    retreat={retreat}
                    onBookNow={handleBookNow}
                    onViewDetails={handleViewDetails}
                  />
                ))}
              </div>
            )}

            <div className="featured-retreats__actions">
              <Button
                variant="outline"
                size="lg"
                onClick={() => navigate('/search')}
              >
                Voir toutes les retraites
              </Button>
            </div>
          </div>
        </section>

        {/* Section Témoignages */}
        <section className="testimonials" aria-labelledby="testimonials-title">
          <div className="container">
            <h2 id="testimonials-title" className="testimonials__title">
              Ce que disent nos participants
            </h2>
            
            <div className="testimonials__grid">
              <blockquote className="testimonial">
                <p className="testimonial__quote">
                  "Une expérience transformatrice qui a changé ma perspective sur la vie. 
                  L'organisation était parfaite et l'accompagnement exceptionnel."
                </p>
                <footer className="testimonial__author">
                  <cite>Sarah M., Paris</cite>
                  <div className="testimonial__rating" aria-label="5 étoiles sur 5">
                    ⭐⭐⭐⭐⭐
                  </div>
                </footer>
              </blockquote>

              <blockquote className="testimonial">
                <p className="testimonial__quote">
                  "J'ai trouvé exactement ce que je cherchais. Le lieu était magique 
                  et les enseignements profonds. Je recommande vivement !"
                </p>
                <footer className="testimonial__author">
                  <cite>Marc L., Lyon</cite>
                  <div className="testimonial__rating" aria-label="5 étoiles sur 5">
                    ⭐⭐⭐⭐⭐
                  </div>
                </footer>
              </blockquote>

              <blockquote className="testimonial">
                <p className="testimonial__quote">
                  "Service client remarquable et retraite au-delà de mes attentes. 
                  Une pause nécessaire dans notre monde agité."
                </p>
                <footer className="testimonial__author">
                  <cite>Julie D., Marseille</cite>
                  <div className="testimonial__rating" aria-label="5 étoiles sur 5">
                    ⭐⭐⭐⭐⭐
                  </div>
                </footer>
              </blockquote>
            </div>
          </div>
        </section>

        {/* Section CTA finale */}
        <section className="final-cta" aria-labelledby="cta-title">
          <div className="container">
            <div className="final-cta__content">
              <h2 id="cta-title" className="final-cta__title">
                Prêt à commencer votre transformation ?
              </h2>
              <p className="final-cta__description">
                Rejoignez des milliers de personnes qui ont déjà transformé leur vie 
                grâce à nos retraites exceptionnelles.
              </p>
              
              <div className="final-cta__actions">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => navigate('/search')}
                >
                  Trouver ma retraite
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => navigate('/about')}
                >
                  En savoir plus
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default HomePage;`;
    }
    /**
     * Génère la page de recherche avec filtres avancés
     */
    async generateSearchPage(design, request) {
        const isTypeScript = request.typescript;
        return `import React, { useState, useEffect } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import Header from '../components/Header/Header';
import Footer from '../components/Footer/Footer';
import SearchBar from '../components/SearchBar/SearchBar';
import FilterPanel from '../components/FilterPanel/FilterPanel';
import RetreatCard from '../components/RetreatCard/RetreatCard';
import Button from '../components/Button/Button';
import { useRetreatSearch } from '../hooks/useRetreatSearch';
${isTypeScript ? `
interface SearchFilters {
  location?: string;
  priceRange?: [number, number];
  duration?: number[];
  type?: string[];
  rating?: number;
  dates?: [string, string];
}` : ''}

${isTypeScript ? 'const SearchPage: React.FC = () => {' : 'const SearchPage = () => {'}
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const { searchRetreats, loading, results, totalCount } = useRetreatSearch();
  
  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [filters, setFilters] = useState${isTypeScript ? '<SearchFilters>' : ''}({
    location: searchParams.get('location') || undefined,
    priceRange: undefined,
    duration: [],
    type: [],
    rating: undefined,
    dates: undefined
  });
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState('relevance');

  useEffect(() => {
    // Charger les résultats initiaux
    if (location.state?.results) {
      // Utiliser les résultats passés depuis la page d'accueil
    } else {
      // Effectuer une nouvelle recherche
      handleSearch();
    }
  }, []);

  const handleSearch = async (newQuery${isTypeScript ? '?: string' : ''}, newFilters${isTypeScript ? '?: SearchFilters' : ''}) => {
    const searchQuery = newQuery !== undefined ? newQuery : query;
    const searchFilters = newFilters !== undefined ? newFilters : filters;
    
    await searchRetreats(searchQuery, searchFilters, sortBy);
    
    // Mettre à jour l'URL
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (searchFilters.location) params.set('location', searchFilters.location);
    setSearchParams(params);
  };

  const handleFilterChange = (newFilters${isTypeScript ? ': SearchFilters' : ''}) => {
    setFilters(newFilters);
    handleSearch(query, newFilters);
  };

  const handleSortChange = (newSortBy${isTypeScript ? ': string' : ''}) => {
    setSortBy(newSortBy);
    handleSearch();
  };

  return (
    <div className="search-page">
      <Header />
      
      <main className="search-page__main">
        {/* Barre de recherche */}
        <section className="search-header">
          <div className="container">
            <SearchBar
              value={query}
              onSearch={(q, f) => {
                setQuery(q);
                handleSearch(q, f);
              }}
              placeholder="Rechercher des retraites..."
              showFilters={false}
            />
            
            <div className="search-controls">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                aria-expanded={showFilters}
                aria-controls="filter-panel"
              >
                Filtres {showFilters ? '▲' : '▼'}
              </Button>
              
              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value)}
                className="search-controls__sort"
                aria-label="Trier par"
              >
                <option value="relevance">Pertinence</option>
                <option value="price-asc">Prix croissant</option>
                <option value="price-desc">Prix décroissant</option>
                <option value="rating">Mieux notés</option>
                <option value="date">Plus récents</option>
              </select>
            </div>
          </div>
        </section>

        <div className="search-content">
          <div className="container">
            <div className="search-layout">
              {/* Panneau de filtres */}
              {showFilters && (
                <aside 
                  id="filter-panel"
                  className="search-layout__filters"
                  aria-label="Filtres de recherche"
                >
                  <FilterPanel
                    filters={filters}
                    onChange={handleFilterChange}
                    onReset={() => {
                      setFilters({});
                      handleSearch(query, {});
                    }}
                  />
                </aside>
              )}

              {/* Résultats */}
              <section className="search-layout__results">
                <header className="search-results__header">
                  <h1 className="search-results__title">
                    {query ? \`Résultats pour "\${query}"\` : 'Toutes les retraites'}
                  </h1>
                  <p className="search-results__count">
                    {loading ? 'Recherche en cours...' : \`\${totalCount} retraite(s) trouvée(s)\`}
                  </p>
                </header>

                {loading ? (
                  <div className="search-results__loading" role="status" aria-live="polite">
                    <div className="loading-spinner">⏳</div>
                    <span>Recherche en cours...</span>
                  </div>
                ) : results.length > 0 ? (
                  <div className="search-results__grid">
                    {results.map((retreat) => (
                      <RetreatCard
                        key={retreat.id}
                        retreat={retreat}
                        onBookNow={(id) => window.location.href = \`/booking/\${id}\`}
                        onViewDetails={(id) => window.location.href = \`/retreat/\${id}\`}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="search-results__empty">
                    <h2>Aucune retraite trouvée</h2>
                    <p>Essayez de modifier vos critères de recherche ou explorez nos suggestions.</p>
                    <Button
                      variant="primary"
                      onClick={() => {
                        setQuery('');
                        setFilters({});
                        handleSearch('', {});
                      }}
                    >
                      Voir toutes les retraites
                    </Button>
                  </div>
                )}
              </section>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default SearchPage;`;
    }
    // Méthodes utilitaires
    extractDependencies(content) {
        const importRegex = /import.*from ['"]([^'"]+)['"]/g;
        const dependencies = [];
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            const dep = match[1];
            if (!dep.startsWith('.') && !dep.startsWith('/')) {
                dependencies.push(dep);
            }
        }
        return [...new Set(dependencies)];
    }
    extractImports(content) {
        const importRegex = /import\s+(?:{([^}]+)}|(\w+))\s+from/g;
        const imports = [];
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            if (match[1]) {
                imports.push(...match[1].split(',').map(imp => imp.trim()));
            }
            else if (match[2]) {
                imports.push(match[2]);
            }
        }
        return [...new Set(imports)];
    }
    // Méthodes de génération simplifiées (à implémenter complètement)
    async generateBookingPage(design, request) {
        return `// BookingPage implementation`;
    }
    async generateDashboardPage(design, request) {
        return `// DashboardPage implementation`;
    }
    async generateProfilePage(design, request) {
        return `// ProfilePage implementation`;
    }
    async generateGenericPage(pageName, wireframe, design, request) {
        return `// Generic ${pageName} page implementation`;
    }
}
exports.PageGenerator = PageGenerator;
//# sourceMappingURL=PageGenerator.js.map