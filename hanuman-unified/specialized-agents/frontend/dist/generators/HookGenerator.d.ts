import { Logger } from 'winston';
import { GeneratedFile, CodeGenerationRequest, ComprehensiveUXDesign } from '../types';
/**
 * Générateur de Hooks React
 *
 * Génère des hooks personnalisés optimisés pour la logique métier
 * et la gestion d'état de l'application.
 */
export declare class HookGenerator {
    private logger;
    private framework;
    constructor(logger: Logger, framework: string);
    /**
     * Génère un hook personnalisé
     */
    generateHook(hookName: string, design: ComprehensiveUXDesign, request: CodeGenerationRequest): Promise<GeneratedFile>;
    /**
     * Gén<PERSON> le hook useAuth pour l'authentification
     */
    private generateUseAuth;
    /**
     * <PERSON><PERSON><PERSON> le hook useRetreatSearch pour la recherche de retraites
     */
    private generateUseRetreatSearch;
    /**
     * Gén<PERSON> le hook useLocalStorage
     */
    private generateUseLocalStorage;
    /**
     * G<PERSON><PERSON> le hook useDebounce
     */
    private generateUseDebounce;
    private extractDependencies;
    private extractImports;
    private generateUseApi;
    private generateUseIntersectionObserver;
    private generateUseBooking;
    private generateUseUserPreferences;
    private generateGenericHook;
}
//# sourceMappingURL=HookGenerator.d.ts.map