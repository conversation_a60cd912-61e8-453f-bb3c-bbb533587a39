import { FrontendAgent } from './core/FrontendAgent';
import { WeaviateMemory } from './memory/WeaviateMemory';
import { KafkaCommunication } from './communication/KafkaCommunication';
declare const app: import("express-serve-static-core").Express;
declare let frontendAgent: FrontendAgent;
declare let memory: WeaviateMemory;
declare let communication: KafkaCommunication;
export { app, frontendAgent, memory, communication };
//# sourceMappingURL=index.d.ts.map