import { Logger } from 'winston';
import { GeneratedCode, Template } from '../types';
/**
 * Système de Mémoire Weaviate pour l'Agent Frontend
 *
 * Stocke et récupère les codes générés, templates, et patterns
 * de développement pour l'apprentissage et la réutilisation.
 */
export declare class WeaviateMemory {
    private client;
    private logger;
    private isConnected;
    private readonly collections;
    constructor(logger: Logger, weaviateUrl?: string);
    /**
     * Initialise la connexion à Weaviate
     */
    private initializeConnection;
    /**
     * S'assure que les schémas existent
     */
    private ensureSchemas;
    /**
     * Crée un schéma s'il n'existe pas
     */
    private createSchemaIfNotExists;
    /**
     * Stocke du code généré
     */
    storeGeneratedCode(generatedCode: GeneratedCode): Promise<string>;
    /**
     * Recherche du code similaire
     */
    findSimilarCode(framework: string, features: string[], limit?: number): Promise<any[]>;
    /**
     * Stocke un template
     */
    storeTemplate(template: Template): Promise<string>;
    /**
     * Recherche des templates
     */
    findTemplates(framework: string, type?: string, limit?: number): Promise<Template[]>;
    /**
     * Stocke un pattern de code
     */
    storeCodePattern(pattern: any): Promise<string>;
    /**
     * Recherche des patterns de code
     */
    findCodePatterns(category?: string, framework?: string, limit?: number): Promise<any[]>;
    /**
     * Recherche sémantique
     */
    semanticSearch(query: string, className: string, limit?: number): Promise<any[]>;
    /**
     * Obtient les statistiques de la mémoire
     */
    getMemoryStats(): Promise<any>;
    /**
     * Génère des tags pour le code généré
     */
    private generateTags;
    /**
     * Vérifie l'état de la connexion
     */
    isConnected(): boolean;
    /**
     * Ferme la connexion
     */
    close(): Promise<void>;
}
//# sourceMappingURL=WeaviateMemory.d.ts.map