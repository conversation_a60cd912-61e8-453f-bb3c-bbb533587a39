import weaviate, { WeaviateClient } from 'weaviate-ts-client';
import { Logger } from 'winston';
import { GeneratedCode, Template } from '../types';

/**
 * Système de Mémoire Weaviate pour l'Agent Frontend
 * 
 * Stocke et récupère les codes générés, templates, et patterns
 * de développement pour l'apprentissage et la réutilisation.
 */
export class WeaviateMemory {
  private client: WeaviateClient;
  private logger: Logger;
  private isConnected: boolean = false;

  // Collections Weaviate
  private readonly collections = {
    GeneratedCode: 'GeneratedCode',
    Template: 'Template',
    CodePattern: 'CodePattern',
    ComponentLibrary: 'ComponentLibrary',
    OptimizationRule: 'OptimizationRule',
    ValidationRule: 'ValidationRule'
  };

  constructor(logger: Logger, weaviateUrl: string = 'http://weaviate:8080') {
    this.logger = logger;
    this.client = weaviate.client({
      scheme: 'http',
      host: weaviateUrl.replace('http://', '').replace('https://', ''),
    });
    
    this.initializeConnection();
  }

  /**
   * Initialise la connexion à Weaviate
   */
  private async initializeConnection(): Promise<void> {
    try {
      this.logger.info('Initialisation de la connexion Weaviate');
      
      // Vérifier la connexion
      const isReady = await this.client.misc.readyChecker().do();
      if (isReady) {
        this.isConnected = true;
        await this.ensureSchemas();
        this.logger.info('Connexion Weaviate établie avec succès');
      } else {
        throw new Error('Weaviate n\'est pas prêt');
      }
    } catch (error) {
      this.logger.error('Erreur lors de la connexion à Weaviate', { error: error.message });
      this.isConnected = false;
    }
  }

  /**
   * S'assure que les schémas existent
   */
  private async ensureSchemas(): Promise<void> {
    try {
      // Schéma pour le code généré
      await this.createSchemaIfNotExists(this.collections.GeneratedCode, {
        class: this.collections.GeneratedCode,
        description: 'Code généré par l\'agent frontend',
        properties: [
          {
            name: 'framework',
            dataType: ['text'],
            description: 'Framework utilisé (react, vue, etc.)'
          },
          {
            name: 'content',
            dataType: ['text'],
            description: 'Contenu du code généré'
          },
          {
            name: 'features',
            dataType: ['text[]'],
            description: 'Fonctionnalités implémentées'
          },
          {
            name: 'qualityScore',
            dataType: ['number'],
            description: 'Score de qualité du code'
          },
          {
            name: 'performanceScore',
            dataType: ['number'],
            description: 'Score de performance'
          },
          {
            name: 'accessibilityScore',
            dataType: ['number'],
            description: 'Score d\'accessibilité'
          },
          {
            name: 'generatedAt',
            dataType: ['date'],
            description: 'Date de génération'
          },
          {
            name: 'tags',
            dataType: ['text[]'],
            description: 'Tags pour la recherche'
          }
        ]
      });

      // Schéma pour les templates
      await this.createSchemaIfNotExists(this.collections.Template, {
        class: this.collections.Template,
        description: 'Templates de code réutilisables',
        properties: [
          {
            name: 'name',
            dataType: ['text'],
            description: 'Nom du template'
          },
          {
            name: 'framework',
            dataType: ['text'],
            description: 'Framework cible'
          },
          {
            name: 'type',
            dataType: ['text'],
            description: 'Type de template (component, page, hook, etc.)'
          },
          {
            name: 'template',
            dataType: ['text'],
            description: 'Contenu du template'
          },
          {
            name: 'variables',
            dataType: ['text'],
            description: 'Variables du template (JSON)'
          },
          {
            name: 'usage',
            dataType: ['text'],
            description: 'Instructions d\'utilisation'
          },
          {
            name: 'examples',
            dataType: ['text[]'],
            description: 'Exemples d\'utilisation'
          },
          {
            name: 'tags',
            dataType: ['text[]'],
            description: 'Tags pour la recherche'
          }
        ]
      });

      // Schéma pour les patterns de code
      await this.createSchemaIfNotExists(this.collections.CodePattern, {
        class: this.collections.CodePattern,
        description: 'Patterns de code réutilisables',
        properties: [
          {
            name: 'name',
            dataType: ['text'],
            description: 'Nom du pattern'
          },
          {
            name: 'category',
            dataType: ['text'],
            description: 'Catégorie du pattern'
          },
          {
            name: 'description',
            dataType: ['text'],
            description: 'Description du pattern'
          },
          {
            name: 'code',
            dataType: ['text'],
            description: 'Code du pattern'
          },
          {
            name: 'framework',
            dataType: ['text'],
            description: 'Framework applicable'
          },
          {
            name: 'useCase',
            dataType: ['text'],
            description: 'Cas d\'usage'
          },
          {
            name: 'benefits',
            dataType: ['text[]'],
            description: 'Avantages du pattern'
          },
          {
            name: 'complexity',
            dataType: ['text'],
            description: 'Niveau de complexité'
          }
        ]
      });

      this.logger.info('Schémas Weaviate initialisés');
    } catch (error) {
      this.logger.error('Erreur lors de la création des schémas', { error: error.message });
    }
  }

  /**
   * Crée un schéma s'il n'existe pas
   */
  private async createSchemaIfNotExists(className: string, schema: any): Promise<void> {
    try {
      const exists = await this.client.schema.exists(className);
      if (!exists) {
        await this.client.schema.classCreator().withClass(schema).do();
        this.logger.info(`Schéma ${className} créé`);
      }
    } catch (error) {
      this.logger.warn(`Erreur lors de la création du schéma ${className}`, { error: error.message });
    }
  }

  /**
   * Stocke du code généré
   */
  async storeGeneratedCode(generatedCode: GeneratedCode): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Weaviate non connecté');
    }

    try {
      this.logger.info('Stockage du code généré', { id: generatedCode.id });

      const data = {
        framework: generatedCode.framework,
        content: JSON.stringify(generatedCode.files),
        features: generatedCode.metadata.features,
        qualityScore: generatedCode.metadata.quality.codeQuality,
        performanceScore: generatedCode.metadata.quality.performance,
        accessibilityScore: generatedCode.metadata.quality.accessibility,
        generatedAt: generatedCode.metadata.generatedAt.toISOString(),
        tags: this.generateTags(generatedCode)
      };

      const result = await this.client.data
        .creator()
        .withClassName(this.collections.GeneratedCode)
        .withProperties(data)
        .do();

      this.logger.info('Code généré stocké avec succès', { id: result.id });
      return result.id;

    } catch (error) {
      this.logger.error('Erreur lors du stockage du code', { error: error.message });
      throw error;
    }
  }

  /**
   * Recherche du code similaire
   */
  async findSimilarCode(
    framework: string,
    features: string[],
    limit: number = 5
  ): Promise<any[]> {
    if (!this.isConnected) {
      return [];
    }

    try {
      this.logger.info('Recherche de code similaire', { framework, features });

      const result = await this.client.graphql
        .get()
        .withClassName(this.collections.GeneratedCode)
        .withFields('framework content features qualityScore performanceScore accessibilityScore')
        .withWhere({
          operator: 'And',
          operands: [
            {
              path: ['framework'],
              operator: 'Equal',
              valueText: framework
            },
            {
              path: ['features'],
              operator: 'ContainsAny',
              valueTextArray: features
            }
          ]
        })
        .withLimit(limit)
        .do();

      return result.data.Get[this.collections.GeneratedCode] || [];

    } catch (error) {
      this.logger.error('Erreur lors de la recherche de code', { error: error.message });
      return [];
    }
  }

  /**
   * Stocke un template
   */
  async storeTemplate(template: Template): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Weaviate non connecté');
    }

    try {
      this.logger.info('Stockage du template', { name: template.name });

      const data = {
        name: template.name,
        framework: template.framework,
        type: template.type,
        template: template.template,
        variables: JSON.stringify(template.variables),
        usage: template.examples.join('\n'),
        examples: template.examples,
        tags: [template.framework, template.type, ...template.dependencies]
      };

      const result = await this.client.data
        .creator()
        .withClassName(this.collections.Template)
        .withProperties(data)
        .do();

      this.logger.info('Template stocké avec succès', { id: result.id });
      return result.id;

    } catch (error) {
      this.logger.error('Erreur lors du stockage du template', { error: error.message });
      throw error;
    }
  }

  /**
   * Recherche des templates
   */
  async findTemplates(
    framework: string,
    type?: string,
    limit: number = 10
  ): Promise<Template[]> {
    if (!this.isConnected) {
      return [];
    }

    try {
      this.logger.info('Recherche de templates', { framework, type });

      let whereClause: any = {
        path: ['framework'],
        operator: 'Equal',
        valueText: framework
      };

      if (type) {
        whereClause = {
          operator: 'And',
          operands: [
            whereClause,
            {
              path: ['type'],
              operator: 'Equal',
              valueText: type
            }
          ]
        };
      }

      const result = await this.client.graphql
        .get()
        .withClassName(this.collections.Template)
        .withFields('name framework type template variables usage examples tags')
        .withWhere(whereClause)
        .withLimit(limit)
        .do();

      const templates = result.data.Get[this.collections.Template] || [];
      
      return templates.map(t => ({
        id: t.name,
        name: t.name,
        framework: t.framework,
        type: t.type,
        template: t.template,
        variables: JSON.parse(t.variables || '[]'),
        dependencies: [],
        examples: t.examples || []
      }));

    } catch (error) {
      this.logger.error('Erreur lors de la recherche de templates', { error: error.message });
      return [];
    }
  }

  /**
   * Stocke un pattern de code
   */
  async storeCodePattern(pattern: any): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Weaviate non connecté');
    }

    try {
      this.logger.info('Stockage du pattern de code', { name: pattern.name });

      const data = {
        name: pattern.name,
        category: pattern.category,
        description: pattern.description,
        code: pattern.code,
        framework: pattern.framework || 'react',
        useCase: pattern.useCase || '',
        benefits: pattern.benefits || [],
        complexity: pattern.complexity || 'medium'
      };

      const result = await this.client.data
        .creator()
        .withClassName(this.collections.CodePattern)
        .withProperties(data)
        .do();

      this.logger.info('Pattern de code stocké avec succès', { id: result.id });
      return result.id;

    } catch (error) {
      this.logger.error('Erreur lors du stockage du pattern', { error: error.message });
      throw error;
    }
  }

  /**
   * Recherche des patterns de code
   */
  async findCodePatterns(
    category?: string,
    framework?: string,
    limit: number = 10
  ): Promise<any[]> {
    if (!this.isConnected) {
      return [];
    }

    try {
      this.logger.info('Recherche de patterns de code', { category, framework });

      let whereClause: any = null;

      if (category && framework) {
        whereClause = {
          operator: 'And',
          operands: [
            {
              path: ['category'],
              operator: 'Equal',
              valueText: category
            },
            {
              path: ['framework'],
              operator: 'Equal',
              valueText: framework
            }
          ]
        };
      } else if (category) {
        whereClause = {
          path: ['category'],
          operator: 'Equal',
          valueText: category
        };
      } else if (framework) {
        whereClause = {
          path: ['framework'],
          operator: 'Equal',
          valueText: framework
        };
      }

      let query = this.client.graphql
        .get()
        .withClassName(this.collections.CodePattern)
        .withFields('name category description code framework useCase benefits complexity')
        .withLimit(limit);

      if (whereClause) {
        query = query.withWhere(whereClause);
      }

      const result = await query.do();
      return result.data.Get[this.collections.CodePattern] || [];

    } catch (error) {
      this.logger.error('Erreur lors de la recherche de patterns', { error: error.message });
      return [];
    }
  }

  /**
   * Recherche sémantique
   */
  async semanticSearch(
    query: string,
    className: string,
    limit: number = 5
  ): Promise<any[]> {
    if (!this.isConnected) {
      return [];
    }

    try {
      this.logger.info('Recherche sémantique', { query, className });

      const result = await this.client.graphql
        .get()
        .withClassName(className)
        .withNearText({ concepts: [query] })
        .withLimit(limit)
        .do();

      return result.data.Get[className] || [];

    } catch (error) {
      this.logger.error('Erreur lors de la recherche sémantique', { error: error.message });
      return [];
    }
  }

  /**
   * Obtient les statistiques de la mémoire
   */
  async getMemoryStats(): Promise<any> {
    if (!this.isConnected) {
      return { connected: false };
    }

    try {
      const stats = {};
      
      for (const [name, className] of Object.entries(this.collections)) {
        try {
          const result = await this.client.graphql
            .aggregate()
            .withClassName(className)
            .withFields('meta { count }')
            .do();
          
          stats[name] = result.data.Aggregate[className]?.[0]?.meta?.count || 0;
        } catch (error) {
          stats[name] = 0;
        }
      }

      return {
        connected: true,
        collections: stats,
        totalObjects: Object.values(stats).reduce((sum: number, count: number) => sum + count, 0)
      };

    } catch (error) {
      this.logger.error('Erreur lors de la récupération des stats', { error: error.message });
      return { connected: false, error: error.message };
    }
  }

  /**
   * Génère des tags pour le code généré
   */
  private generateTags(generatedCode: GeneratedCode): string[] {
    const tags = [
      generatedCode.framework,
      ...generatedCode.metadata.features,
      `quality-${Math.round(generatedCode.metadata.quality.codeQuality * 10)}`,
      `performance-${Math.round(generatedCode.metadata.quality.performance * 10)}`,
      `accessibility-${Math.round(generatedCode.metadata.quality.accessibility * 10)}`
    ];

    // Ajouter des tags basés sur les types de fichiers
    const fileTypes = [...new Set(generatedCode.files.map(f => f.type))];
    tags.push(...fileTypes);

    return tags;
  }

  /**
   * Vérifie l'état de la connexion
   */
  isConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Ferme la connexion
   */
  async close(): Promise<void> {
    this.isConnected = false;
    this.logger.info('Connexion Weaviate fermée');
  }
}
