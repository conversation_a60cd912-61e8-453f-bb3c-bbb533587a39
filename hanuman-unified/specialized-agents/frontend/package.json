{"name": "@retreat-and-be/agent-frontend", "version": "1.0.0", "description": "Agent Frontend pour génération automatique de code React/Vue", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "docker:build": "docker build -t agent-frontend .", "docker:run": "docker run -p 3006:3006 agent-frontend", "generate:react": "ts-node src/cli/generate-react.ts", "generate:vue": "ts-node src/cli/generate-vue.ts"}, "dependencies": {"@types/node": "^20.0.0", "axios": "^1.6.0", "express": "^4.18.0", "kafka-node": "^5.0.0", "redis": "^4.6.0", "winston": "^3.11.0", "dotenv": "^16.3.0", "openai": "^4.20.0", "weaviate-ts-client": "^1.4.0", "uuid": "^9.0.0", "joi": "^17.11.0", "helmet": "^7.1.0", "cors": "^2.8.5", "prettier": "^3.0.0", "handlebars": "^4.7.8", "fs-extra": "^11.1.0", "archiver": "^6.0.0", "multer": "^1.4.5-lts.1", "jsdom": "^23.0.0", "css-tree": "^2.3.0", "postcss": "^8.4.0", "autoprefixer": "^10.4.0", "tailwindcss": "^3.3.0", "@babel/core": "^7.23.0", "@babel/preset-react": "^7.23.0", "@babel/preset-typescript": "^7.23.0", "esbuild": "^0.19.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/uuid": "^9.0.0", "@types/cors": "^2.8.0", "@types/multer": "^1.4.0", "@types/fs-extra": "^11.0.0", "@types/archiver": "^6.0.0", "@types/jsdom": "^21.1.0", "typescript": "^5.2.0", "ts-node": "^10.9.0", "jest": "^29.7.0", "@types/jest": "^29.5.0", "eslint": "^8.52.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0"}, "keywords": ["ai-agent", "frontend", "code-generation", "react", "vue", "retreat-and-be", "living-ai"], "author": "Retreat And Be Team", "license": "MIT"}