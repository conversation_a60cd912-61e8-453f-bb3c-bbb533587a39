{"version": 3, "file": "DataAnalystAgent.js", "sourceRoot": "", "sources": ["../../src/core/DataAnalystAgent.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAWtC,gEAA6D;AAC7D,kFAA+E;AAC/E,wEAAqE;AACrE,gEAA6D;AAC7D,oEAAiE;AACjE,0EAAuE;AACvE,6DAA0D;AAC1D,4EAAyE;AAEzE;;GAEG;AACH,MAAa,gBAAiB,SAAQ,qBAAY;IAahD,YAAY,MAAmB,EAAE,MAAc;QAC7C,KAAK,EAAE,CAAC;QAHF,kBAAa,GAAY,KAAK,CAAC;QAIrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,0BAA0B;QAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3D,IAAI,CAAC,wBAAwB,GAAG,IAAI,mDAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7E,IAAI,CAAC,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnE,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3D,IAAI,CAAC,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/D,IAAI,CAAC,oBAAoB,GAAG,IAAI,2CAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAErE,0CAA0C;QAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,+BAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,IAAI,uCAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAEvD,6BAA6B;YAC7B,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,CAAC;YACjD,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC;YAC1C,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC;YAE7C,2BAA2B;YAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAChE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAAwB;QAC5C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,OAAO,CAAC,IAAI,cAAc,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAEvE,qBAAqB;YACrB,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAEtC,sBAAsB;YACtB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEhF,oCAAoC;YACpC,IAAI,YAAY,CAAC;YACjB,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACrB,KAAK,aAAa;oBAChB,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,0BAA0B,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACxG,MAAM;gBACR,KAAK,YAAY;oBACf,YAAY,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,yBAAyB,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBAChH,MAAM;gBACR,KAAK,cAAc;oBACjB,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,2BAA2B,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACzG,MAAM;gBACR,KAAK,YAAY;oBACf,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACvG,MAAM;gBACR,KAAK,aAAa;oBAChB,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,0BAA0B,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACxG,MAAM;gBACR,KAAK,OAAO;oBACV,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBAClG,MAAM;gBACR,KAAK,aAAa;oBAChB,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,0BAA0B,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACxG,MAAM;gBACR,KAAK,cAAc;oBACjB,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,2BAA2B,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACzG,MAAM;gBACR,KAAK,aAAa;oBAChB,YAAY,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACzG,MAAM;gBACR,KAAK,mBAAmB;oBACtB,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;oBACrG,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,8BAA8B,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAEzF,8BAA8B;YAC9B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAEnG,2BAA2B;YAC3B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,YAAY,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAEvG,yBAAyB;YACzB,MAAM,MAAM,GAAmB;gBAC7B,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrB,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,WAAW;gBACnB,OAAO,EAAE,YAAY;gBACrB,QAAQ;gBACR,eAAe;gBACf,cAAc;gBACd,QAAQ,EAAE;oBACR,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,0BAA0B;oBAC7D,QAAQ,EAAE,aAAa,CAAC,MAAM,IAAI,CAAC;oBACnC,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC;oBACjD,OAAO,EAAE,OAAO;oBAChB,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,OAAO,EAAE;wBACP,gBAAgB,EAAE,IAAI;wBACtB,aAAa,EAAE,IAAI;wBACnB,UAAU,EAAE,GAAG;wBACf,WAAW,EAAE,GAAG;wBAChB,QAAQ,EAAE,EAAE;qBACb;iBACF;aACF,CAAC;YAEF,yBAAyB;YACzB,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE9C,6BAA6B;YAC7B,MAAM,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAEvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,UAA8B;QAClD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;YAE3D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YAE7E,2BAA2B;YAC3B,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;YACvD,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,YAA6B;QAChD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;YAE5D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAEvE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3D,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,cAA+B;QAC7C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,cAAc,CAAC,MAAM,OAAO,CAAC,CAAC;YAE3D,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAEvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,MAAmB;QACvC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,MAAM,cAAc,CAAC,CAAC;YAE5D,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAEjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,UAAkB,EAAE,SAAe;QAC5D,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;YAErE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAE7F,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,qBAAqB,CAAC,CAAC;YACpE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAAC,IAAW,EAAE,MAAc,EAAE,QAAkB;QAC7E,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8CAA8C,MAAM,EAAE,CAAC,CAAC;YAEzE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YAEpG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAClD,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,UAAe;QACvC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAEpD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAE5E,sCAAsC;YACtC,MAAM,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAE3D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,iCAAiC;QACjC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAwB,EAAE,EAAE;YAC3E,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBACnD,MAAM,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YACjE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,mBAAmB,EAAE,KAAK,EAAE,UAA8B,EAAE,EAAE;YAClF,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;gBACzD,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACvD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAS,EAAE,EAAE;YAC1D,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACvC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,OAAwB;QACtD,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAY;QACtC,MAAM,UAAU,GAA8B;YAC5C,aAAa,EAAE,qBAAqB;YACpC,YAAY,EAAE,kBAAkB;YAChC,cAAc,EAAE,cAAc;YAC9B,YAAY,EAAE,qBAAqB;YACnC,aAAa,EAAE,wBAAwB;YACvC,OAAO,EAAE,sBAAsB;YAC/B,aAAa,EAAE,sBAAsB;YACrC,cAAc,EAAE,YAAY;YAC5B,aAAa,EAAE,yBAAyB;YACxC,mBAAmB,EAAE,mBAAmB;SACzC,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,OAAO,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAExD,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;YAC5C,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAE/B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA9XD,4CA8XC"}