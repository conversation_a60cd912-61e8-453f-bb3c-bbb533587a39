{"name": "weaviate-ts-client", "version": "1.6.0", "description": "TypeScript client for Weaviate", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}}, "engines": {"node": ">=16.0.0"}, "files": ["/dist/index.js", "/dist/index.mjs", "/dist/index.d.ts"], "scripts": {"test": "tsc -noEmit -p tsconfig-test.json && jest --useStderr --runInBand --detectOpenHandles", "build": "npm run lint && tsup src/index.ts --format cjs,esm --dts --clean --platform neutral --minify", "prepack": "npm run build", "lint": "eslint --ext .ts,.js .", "lint:fix": "npm run lint -- --fix", "format": "prettier --write --no-error-on-unmatched-pattern '**/*.{ts,js}' '!dist/**'", "format:check": "prettier --check --no-error-on-unmatched-pattern '**/*.{ts,js}' '!dist/**'", "prepare": "husky install", "schema": "./tools/refresh_schema.sh"}, "repository": {"type": "git", "url": "git+https://github.com/weaviate/typescript-client.git"}, "keywords": ["weaviate"], "author": "Weaviate", "license": "SEE LICENSE IN LICENSE", "bugs": {"url": "https://github.com/weaviate/typescript-client/issues"}, "homepage": "https://github.com/weaviate/typescript-client#readme", "dependencies": {"graphql-request": "^5.2.0", "isomorphic-fetch": "^3.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.20.7", "@curveball/bodyparser": "^0.5.0", "@curveball/core": "^0.20.0", "@rollup/plugin-babel": "^5.3.1", "@types/isomorphic-fetch": "^0.0.36", "@types/jest": "^29.4.0", "@types/node": "^18.14.0", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "babel-jest": "^29.4.3", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "jest": "^29.4.3", "lint-staged": "^13.2.0", "openapi-typescript": "^5.4.1", "prettier": "^2.8.4", "ts-jest": "^29.0.5", "tsup": "^6.7.0", "typescript": "^4.9.5"}, "lint-staged": {"*.{ts,js}": ["npm run format:check", "npm run lint -- --cache"]}}