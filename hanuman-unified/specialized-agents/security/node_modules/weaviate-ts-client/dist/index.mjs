var Se=class{constructor(t,e){this.refresh=async t=>{let e=await this.getOpenidConfig(t),r;switch(this.creds.constructor){case me:r=new Ve(this.http,this.creds,e);break;case $:r=new Fe(this.http,this.creds,e);break;case fe:r=new Le(this.http,this.creds,e);break;default:throw new Error("unsupported credential type")}return r.refresh().then(s=>{this.accessToken=s.accessToken,this.expiresAt=s.expiresAt,this.refreshToken=s.refreshToken,this.startTokenRefresh(r)})};this.getOpenidConfig=t=>this.http.externalGet(t.href).then(e=>{let r=t.scopes||[];return{clientId:t.clientId,provider:e,scopes:r}});this.startTokenRefresh=t=>{this.creds.silentRefresh&&!this.refreshRunning&&this.refreshTokenProvided()&&(this.refreshInterval=setInterval(async()=>{if(this.expiresAt-Date.now()<=6e4){let e=await t.refresh();this.accessToken=e.accessToken,this.expiresAt=e.expiresAt,this.refreshToken=e.refreshToken}},3e4),this.refreshRunning=!0)};this.stopTokenRefresh=()=>{clearInterval(this.refreshInterval),this.refreshRunning=!1};this.refreshTokenProvided=()=>this.refreshToken&&this.refreshToken!="";this.getAccessToken=()=>this.accessToken;this.getExpiresAt=()=>this.expiresAt;this.http=t,this.creds=e,this.accessToken="",this.refreshToken="",this.expiresAt=0,this.refreshRunning=!1,this.creds instanceof $&&(this.accessToken=this.creds.accessToken,this.expiresAt=this.creds.expiresAt,this.refreshToken=this.creds.refreshToken)}resetExpiresAt(){this.expiresAt=0}},me=class{constructor(t){this.username=t.username,this.password=t.password,this.scopes=t.scopes,this.silentRefresh=Ge(t.silentRefresh)}},Ve=class{constructor(t,e,r){this.refresh=()=>(this.validateOpenidConfig(),this.requestAccessToken().then(t=>({accessToken:t.access_token,expiresAt:ke(t.expires_in),refreshToken:t.refresh_token})).catch(t=>Promise.reject(new Error(`failed to refresh access token: ${t}`))));this.validateOpenidConfig=()=>{if(this.openidConfig.provider.grant_types_supported!==void 0&&!this.openidConfig.provider.grant_types_supported.includes("password"))throw new Error("grant_type password not supported");if(this.openidConfig.provider.token_endpoint.includes("https://login.microsoftonline.com"))throw new Error("microsoft/azure recommends to avoid authentication using username and password, so this method is not supported by this client");this.openidConfig.scopes.push("offline_access")};this.requestAccessToken=()=>{let t=this.openidConfig.provider.token_endpoint,e=new URLSearchParams({grant_type:"password",client_id:this.openidConfig.clientId,username:this.creds.username,password:this.creds.password,scope:this.openidConfig.scopes.join(" ")}),r="application/x-www-form-urlencoded;charset=UTF-8";return this.http.externalPost(t,e,r)};this.http=t,this.creds=e,this.openidConfig=r,e.scopes&&this.openidConfig.scopes.push(e.scopes)}},$=class{constructor(t){this.validate=t=>{if(t.expiresIn===void 0)throw new Error("AuthAccessTokenCredentials: expiresIn is required");if(!Number.isInteger(t.expiresIn)||t.expiresIn<=0)throw new Error("AuthAccessTokenCredentials: expiresIn must be int > 0")};this.validate(t),this.accessToken=t.accessToken,this.expiresAt=ke(t.expiresIn),this.refreshToken=t.refreshToken,this.silentRefresh=Ge(t.silentRefresh)}},Fe=class{constructor(t,e,r){this.refresh=()=>this.creds.refreshToken===void 0||this.creds.refreshToken==""?(console.warn("AuthAccessTokenCredentials not provided with refreshToken, cannot refresh"),Promise.resolve({accessToken:this.creds.accessToken,expiresAt:this.creds.expiresAt})):(this.validateOpenidConfig(),this.requestAccessToken().then(t=>({accessToken:t.access_token,expiresAt:ke(t.expires_in),refreshToken:t.refresh_token})).catch(t=>Promise.reject(new Error(`failed to refresh access token: ${t}`))));this.validateOpenidConfig=()=>{if(this.openidConfig.provider.grant_types_supported===void 0||!this.openidConfig.provider.grant_types_supported.includes("refresh_token"))throw new Error("grant_type refresh_token not supported")};this.requestAccessToken=()=>{let t=this.openidConfig.provider.token_endpoint,e=new URLSearchParams({grant_type:"refresh_token",client_id:this.openidConfig.clientId,refresh_token:this.creds.refreshToken}),r="application/x-www-form-urlencoded;charset=UTF-8";return this.http.externalPost(t,e,r)};this.http=t,this.creds=e,this.openidConfig=r}},fe=class{constructor(t){this.clientSecret=t.clientSecret,this.scopes=t.scopes,this.silentRefresh=Ge(t.silentRefresh)}},Le=class{constructor(t,e,r){this.refresh=()=>(this.validateOpenidConfig(),this.requestAccessToken().then(t=>({accessToken:t.access_token,expiresAt:ke(t.expires_in),refreshToken:t.refresh_token})).catch(t=>Promise.reject(new Error(`failed to refresh access token: ${t}`))));this.validateOpenidConfig=()=>{this.openidConfig.scopes.length>0||this.openidConfig.provider.token_endpoint.includes("https://login.microsoftonline.com")&&this.openidConfig.scopes.push(this.openidConfig.clientId+"/.default")};this.requestAccessToken=()=>{let t=this.openidConfig.provider.token_endpoint,e=new URLSearchParams({grant_type:"client_credentials",client_id:this.openidConfig.clientId,client_secret:this.creds.clientSecret,scope:this.openidConfig.scopes.join(" ")}),r="application/x-www-form-urlencoded;charset=UTF-8";return this.http.externalPost(t,e,r)};this.http=t,this.creds=e,this.openidConfig=r,e.scopes&&this.openidConfig.scopes.push(e.scopes)}},Ae=class{constructor(t){this.apiKey=t}};function ke(i){return Date.now()+(i-2)*1e3}function Ge(i){return i===void 0?!0:i}var v=class{constructor(t){this.do=()=>this.client.getRaw("/.well-known/openid-configuration").then(t=>t.status<400?t.json():t.status==404?Promise.resolve(void 0):Promise.reject(new Error(`unexpected status code: ${t.status}`)));this.client=t}};import l from"isomorphic-fetch";var ct=i=>{let t="/v1",e=`${i.host}${t}`,r=dt(e);return{post:(s,a,h=!0,c="")=>{let d={method:"POST",headers:{...i.headers,"content-type":"application/json"},body:JSON.stringify(a)};return S(d,c),l(r(s),d).then(y(h))},put:(s,a,h=!0,c="")=>{let d={method:"PUT",headers:{...i.headers,"content-type":"application/json"},body:JSON.stringify(a)};return S(d,c),l(r(s),d).then(y(h))},patch:(s,a,h="")=>{let c={method:"PATCH",headers:{...i.headers,"content-type":"application/json"},body:JSON.stringify(a)};return S(c,h),l(r(s),c).then(y(!1))},delete:(s,a,h=!1,c="")=>{let d={method:"DELETE",headers:{...i.headers,"content-type":"application/json"},body:a?JSON.stringify(a):void 0};return S(d,c),l(r(s),d).then(y(h))},head:(s,a,h="")=>{let c={method:"HEAD",headers:{...i.headers,"content-type":"application/json"},body:a?JSON.stringify(a):void 0};return S(c,h),l(r(s),c).then(lt(!1))},get:(s,a=!0,h="")=>{let c={method:"GET",headers:{...i.headers}};return S(c,h),l(r(s),c).then(y(a))},getRaw:(s,a="")=>{let h={method:"GET",headers:{...i.headers}};return S(h,a),l(r(s),h)},externalGet:s=>l(s,{method:"GET",headers:{...i.headers}}).then(y(!0)),externalPost:(s,a,h)=>{(h==null||h=="")&&(h="application/json");let c={body:void 0,method:"POST",headers:{...i.headers,"content-type":h}};return a!=null&&(c.body=a),l(s,c).then(y(!0))}}},dt=i=>t=>i+t,y=i=>t=>{if(t.status>=400)return t.text().then(e=>{let r;try{r=JSON.stringify(JSON.parse(e))}catch{r=e}return Promise.reject(new Error(`usage error (${t.status}): ${r}`))});if(i)return t.json()},lt=i=>t=>t.status==204||t.status==404?t.status==204:y(i)(t);function S(i,t){t!==""&&(i.headers.Authorization=`Bearer ${t}`)}var We=ct;import{GraphQLClient as pt}from"graphql-request";var ut=i=>{let t=i.headers,e="/v1/graphql",r=`${i.host}${e}`;return{query:(s,a,h)=>new pt(r,{headers:{...t,...h},fetch}).request(s,a,h).then(c=>({data:c}))}},Ue=ut;var ge=class{constructor(t){this.post=(t,e,r=!0)=>this.authEnabled?this.login().then(s=>this.http.post(t,e,r,s)):this.http.post(t,e,r);this.put=(t,e,r=!0)=>this.authEnabled?this.login().then(s=>this.http.put(t,e,r,s)):this.http.put(t,e,r);this.patch=(t,e)=>this.authEnabled?this.login().then(r=>this.http.patch(t,e,r)):this.http.patch(t,e);this.delete=(t,e,r=!1)=>this.authEnabled?this.login().then(s=>this.http.delete(t,e,r,s)):this.http.delete(t,e,r);this.head=(t,e)=>this.authEnabled?this.login().then(r=>this.http.head(t,e,r)):this.http.head(t,e);this.get=(t,e=!0)=>this.authEnabled?this.login().then(r=>this.http.get(t,e,r)):this.http.get(t,e);this.query=(t,e)=>this.authEnabled?this.login().then(r=>{let s={Authorization:`Bearer ${r}`};return this.gql.query(t,e,s)}):this.gql.query(t,e);this.login=async()=>{if(this.apiKey)return this.apiKey;if(!this.oidcAuth)return"";let t=await new v(this.http).do();return t===void 0?(console.warn("client is configured for authentication, but server is not"),""):(Date.now()>=this.oidcAuth.getExpiresAt()&&await this.oidcAuth.refresh(t),this.oidcAuth.getAccessToken())};t=this.sanitizeParams(t),this.host=t.host,this.http=We(t),this.gql=Ue(t),this.authEnabled=this.parseAuthParams(t)}parseAuthParams(t){if(t.authClientSecret&&t.apiKey)throw new Error("must provide one of authClientSecret (OIDC) or apiKey, cannot provide both");return t.authClientSecret?(this.oidcAuth=new Se(this.http,t.authClientSecret),!0):t.apiKey?(this.apiKey=t.apiKey?.apiKey,!0):!1}sanitizeParams(t){for(;t.host.endsWith("/");)t.host=t.host.slice(0,-1);let e=/^(https?|ftp|file)(?::\/\/)/,r=t.host.match(e);if(t.scheme){if(r&&r[1]!==`${t.scheme}`)throw new Error(`The host contains a different protocol than specified in the scheme (scheme: ${t.scheme} != host: ${r[1]})`);r||(t.host=`${t.scheme}://${t.host}`)}else if(!r)throw new Error("The host must start with a recognized protocol (e.g., http or https) if no scheme is provided.");return t}};var p=class{constructor(t){this.source=t}toString(){if(this.parse(),this.validate(),this.operands)return`{operator:${this.operator},operands:[${this.operands}]}`;{let t=this.getValueType(),e=this.marshalValueContent();return`{operator:${this.operator},${t}:${e},path:${JSON.stringify(this.path)}}`}}marshalValueContent(){return this.valueType=="valueGeoRange"?this.marshalValueGeoRange():JSON.stringify(this.valueContent)}getValueType(){switch(this.valueType){case"valueStringArray":return"valueString";case"valueTextArray":return"valueText";case"valueIntArray":return"valueInt";case"valueNumberArray":return"valueNumber";case"valueDateArray":return"valueDate";case"valueBooleanArray":return"valueBoolean";default:return this.valueType}}marshalValueGeoRange(){let t=[],e=this.valueContent.geoCoordinates;if(e){let s=[];e.latitude&&(s=[...s,`latitude:${e.latitude}`]),e.longitude&&(s=[...s,`longitude:${e.longitude}`]),t=[...t,`geoCoordinates:{${s.join(",")}}`]}let r=this.valueContent.distance;if(r){let s=[];r.max&&(s=[...s,`max:${r.max}`]),t=[...t,`distance:{${s.join(",")}}`]}return`{${t.join(",")}}`}validate(){if(!this.operator)throw new Error("where filter: operator cannot be empty");if(!this.operands){if(!this.valueType)throw new Error("where filter: value<Type> cannot be empty");if(!this.path)throw new Error("where filter: path cannot be empty")}}parse(){for(let t in this.source)switch(t){case"operator":this.parseOperator(this.source[t]);break;case"operands":this.parseOperands(this.source[t]);break;case"path":this.parsePath(this.source[t]);break;default:if(t.indexOf("value")!=0)throw new Error("where filter: unrecognized key '"+t+"'");this.parseValue(t,this.source[t])}}parseOperator(t){if(typeof t!="string")throw new Error("where filter: operator must be a string");this.operator=t}parsePath(t){if(!Array.isArray(t))throw new Error("where filter: path must be an array");this.path=t}parseValue(t,e){switch(t){case"valueString":case"valueText":case"valueInt":case"valueNumber":case"valueDate":case"valueBoolean":case"valueStringArray":case"valueTextArray":case"valueIntArray":case"valueNumberArray":case"valueDateArray":case"valueBooleanArray":case"valueGeoRange":break;default:throw new Error("where filter: unrecognized value prop '"+t+"'")}this.valueType=t,this.valueContent=e}parseOperands(t){if(!Array.isArray(t))throw new Error("where filter: operands must be an array");this.operands=t.map(e=>new p(e).toString()).join(",")}};var u=class{constructor(t){this.certainty=t.certainty,this.distance=t.distance,this.media=t.media,this.type=t.type}toString(t=!0){let e=[];if(this.media.startsWith("data:")){let r=";base64,";this.media=this.media.substring(this.media.indexOf(r)+r.length)}return e=[...e,`${this.type.toLowerCase()}:${JSON.stringify(this.media)}`],this.certainty&&(e=[...e,`certainty:${this.certainty}`]),this.distance&&(e=[...e,`distance:${this.distance}`]),t?`{${e.join(",")}}`:`${e.join(",")}`}};var m=class{constructor(t){this.autocorrect=t.autocorrect,this.certainty=t.certainty,this.concepts=t.concepts,this.distance=t.distance,this.moveAwayFrom=t.moveAwayFrom,this.moveTo=t.moveTo}toString(){this.validate();let t=[`concepts:${JSON.stringify(this.concepts)}`];if(this.certainty&&(t=[...t,`certainty:${this.certainty}`]),this.distance&&(t=[...t,`distance:${this.distance}`]),this.moveTo){let e=[];this.moveTo.concepts&&(e=[...e,`concepts:${JSON.stringify(this.moveTo.concepts)}`]),this.moveTo.objects&&(e=[...e,`objects:${this.parseMoveObjects("moveTo",this.moveTo.objects)}`]),this.moveTo.force&&(e=[...e,`force:${this.moveTo.force}`]),t=[...t,`moveTo:{${e.join(",")}}`]}if(this.moveAwayFrom){let e=[];this.moveAwayFrom.concepts&&(e=[...e,`concepts:${JSON.stringify(this.moveAwayFrom.concepts)}`]),this.moveAwayFrom.objects&&(e=[...e,`objects:${this.parseMoveObjects("moveAwayFrom",this.moveAwayFrom.objects)}`]),this.moveAwayFrom.force&&(e=[...e,`force:${this.moveAwayFrom.force}`]),t=[...t,`moveAwayFrom:{${e.join(",")}}`]}return this.autocorrect!==void 0&&(t=[...t,`autocorrect:${this.autocorrect}`]),`{${t.join(",")}}`}validate(){if(this.moveTo){if(!this.moveTo.concepts&&!this.moveTo.objects)throw new Error("nearText filter: moveTo.concepts or moveTo.objects must be present");if(!this.moveTo.force||!this.moveTo.concepts&&!this.moveTo.objects)throw new Error("nearText filter: moveTo must have fields 'concepts' or 'objects' and 'force'")}if(this.moveAwayFrom){if(!this.moveAwayFrom.concepts&&!this.moveAwayFrom.objects)throw new Error("nearText filter: moveAwayFrom.concepts or moveAwayFrom.objects must be present");if(!this.moveAwayFrom.force||!this.moveAwayFrom.concepts&&!this.moveAwayFrom.objects)throw new Error("nearText filter: moveAwayFrom must have fields 'concepts' or 'objects' and 'force'")}}parseMoveObjects(t,e){let r=[];for(let s in e){if(!e[s].id&&!e[s].beacon)throw new Error(`nearText: ${t}.objects[${s}].id or ${t}.objects[${s}].beacon must be present`);let a=[];e[s].id&&a.push(`id:"${e[s].id}"`),e[s].beacon&&a.push(`beacon:"${e[s].beacon}"`),r.push(`{${a.join(",")}}`)}return`[${r.join(",")}]`}};var f=class{constructor(t){this.certainty=t.certainty,this.distance=t.distance,this.vector=t.vector}toString(t=!0){let e=[`vector:${JSON.stringify(this.vector)}`];return this.certainty&&(e=[...e,`certainty:${this.certainty}`]),this.distance&&(e=[...e,`distance:${this.distance}`]),t?`{${e.join(",")}}`:`${e.join(",")}`}};var g=class{constructor(t){this.beacon=t.beacon,this.certainty=t.certainty,this.distance=t.distance,this.id=t.id}toString(t=!0){this.validate();let e=[];return this.id&&(e=[...e,`id:${JSON.stringify(this.id)}`]),this.beacon&&(e=[...e,`beacon:${JSON.stringify(this.beacon)}`]),this.certainty&&(e=[...e,`certainty:${this.certainty}`]),this.distance&&(e=[...e,`distance:${this.distance}`]),t?`{${e.join(",")}}`:`${e.join(",")}`}validate(){if(!this.id&&!this.beacon)throw new Error("nearObject filter: id or beacon needs to be set")}};function mt(i){return Number.isInteger(i)}function Je(i){return mt(i)&&i>=0}var n=class{constructor(t){this.client=t,this._errors=[]}get errors(){return this._errors}addError(t){this._errors=[...this.errors,t]}addErrors(t){this._errors=[...this.errors,...t]}};var B=class extends n{constructor(e){super(e);this.withFields=e=>(this.fields=e,this);this.withClassName=e=>(this.className=e,this);this.withWhere=e=>{try{this.whereString=new p(e).toString()}catch(r){this.addError(r)}return this};this.withNearMedia=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearMediaString=new u(e).toString(),this.nearMediaType=e.type,this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withNearImage=e=>this.withNearMedia({...e,media:e.image,type:"Image"});this.withNearAudio=e=>this.withNearMedia({...e,media:e.audio,type:"Audio"});this.withNearVideo=e=>this.withNearMedia({...e,media:e.video,type:"Video"});this.withNearDepth=e=>this.withNearMedia({...e,media:e.depth,type:"Depth"});this.withNearIMU=e=>this.withNearMedia({...e,media:e.imu,type:"IMU"});this.withNearText=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearTextString=new m(e).toString(),this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withNearObject=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearObjectString=new g(e).toString(),this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withNearVector=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearVectorString=new f(e).toString(),this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withObjectLimit=e=>{if(!Je(e))throw new Error("objectLimit must be a non-negative integer");return this.objectLimit=e,this};this.withLimit=e=>(this.limit=e,this);this.withGroupBy=e=>(this.groupBy=e,this);this.withTenant=e=>(this.tenant=e,this);this.validateGroup=()=>{if(this.groupBy&&!Array.isArray(this.groupBy))throw new Error("groupBy must be an array")};this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateGroup(),this.validateIsSet(this.className,"className",".withClassName(className)"),this.validateIsSet(this.fields,"fields",".withFields(fields)")};this.do=()=>{let e="";if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));if(this.whereString||this.nearTextString||this.nearObjectString||this.nearVectorString||this.limit||this.groupBy||this.tenant){let r=[];this.whereString&&(r=[...r,`where:${this.whereString}`]),this.nearTextString&&(r=[...r,`nearText:${this.nearTextString}`]),this.nearObjectString&&(r=[...r,`nearObject:${this.nearObjectString}`]),this.nearVectorString&&(r=[...r,`nearVector:${this.nearVectorString}`]),this.nearMediaString&&(r=[...r,`${this.nearMediaType}:${this.nearMediaString}`]),this.groupBy&&(r=[...r,`groupBy:${JSON.stringify(this.groupBy)}`]),this.limit&&(r=[...r,`limit:${this.limit}`]),this.objectLimit&&(r=[...r,`objectLimit:${this.objectLimit}`]),this.tenant&&(r=[...r,`tenant:"${this.tenant}"`]),e=`(${r.join(",")})`}return this.client.query(`{Aggregate{${this.className}${e}{${this.fields}}}}`)};this.includesNearMediaFilter=!1}};var ve=class{constructor(t){this.properties=t.properties,this.query=t.query}toString(){let t=[`query:${JSON.stringify(this.query)}`];return this.properties!==void 0&&(t=[...t,`properties:${JSON.stringify(this.properties)}`]),`{${t.join(",")}}`}};var De=(e=>(e.rankedFusion="rankedFusion",e.relativeScoreFusion="relativeScoreFusion",e))(De||{}),ye=class{constructor(t){this.alpha=t.alpha,this.query=t.query,this.vector=t.vector,this.properties=t.properties,this.fusionType=t.fusionType}toString(){let t=[`query:${JSON.stringify(this.query)}`];if(this.alpha!==void 0&&(t=[...t,`alpha:${JSON.stringify(this.alpha)}`]),this.vector!==void 0&&(t=[...t,`vector:${JSON.stringify(this.vector)}`]),this.properties&&this.properties.length>0){let e=this.properties.join('","');t=[...t,`properties:["${e}"]`]}return this.fusionType!==void 0&&(t=[...t,`fusionType:${this.fusionType}`]),`{${t.join(",")}}`}};var A=class{constructor(t){this.certainty=t.certainty,this.distance=t.distance,this.image=t.image}toString(t=!0){this.validate();let e=[];if(this.image){let r=this.image;if(r.startsWith("data:")){let s=";base64,";r=r.substring(r.indexOf(s)+s.length)}e=[...e,`image:${JSON.stringify(r)}`]}return this.certainty&&(e=[...e,`certainty:${this.certainty}`]),this.distance&&(e=[...e,`distance:${this.distance}`]),t?`{${e.join(",")}}`:`${e.join(",")}`}validate(){if(!this.image)throw new Error("nearImage filter: image field must be present")}};var k=class{constructor(t){this.autocorrect=t.autocorrect,this.certainty=t.certainty,this.distance=t.distance,this.properties=t.properties,this.question=t.question,this.rerank=t.rerank}toString(t=!0){this.validate();let e=[];return this.question&&(e=[...e,`question:${JSON.stringify(this.question)}`]),this.properties&&(e=[...e,`properties:${JSON.stringify(this.properties)}`]),this.certainty&&(e=[...e,`certainty:${this.certainty}`]),this.distance&&(e=[...e,`distance:${this.distance}`]),this.autocorrect!==void 0&&(e=[...e,`autocorrect:${this.autocorrect}`]),this.rerank!==void 0&&(e=[...e,`rerank:${this.rerank}`]),t?`{${e.join(",")}}`:`${e.join(",")}`}validate(){if(!this.question)throw new Error("ask filter: question needs to be set")}};var we=class{constructor(t){this.args=t}toString(){let t=[];return this.args.type&&(t=[...t,`type:${this.args.type}`]),this.args.force&&(t=[...t,`force:${this.args.force}`]),`{${t.join(",")}}`}};var Ce=class{constructor(t){this.args=t}toString(){let t=[];for(let e of this.args){let r=`{path:${JSON.stringify(e.path)}`;e.order?r=r.concat(`,order:${e.order}}`):r=r.concat("}"),t.push(r)}return t.join(",")}};var je=class{constructor(t){this.groupedTask=t.groupedTask,this.groupedProperties=t.groupedProperties,this.singlePrompt=t.singlePrompt}toString(){this.validate();let t="generate(",e=["error"];if(this.singlePrompt&&(t+=`singleResult:{prompt:"${this.singlePrompt.replace(/[\n\r]+/g,"")}"}`,e.push("singleResult")),this.groupedTask||this.groupedProperties!==void 0&&this.groupedProperties.length>0){let r=[];this.groupedTask&&r.push(`task:"${this.groupedTask.replace(/[\n\r]+/g,"")}"`),this.groupedProperties!==void 0&&this.groupedProperties.length>0&&r.push(`properties:${JSON.stringify(this.groupedProperties)}`),t+=`groupedResult:{${r.join(",")}}`,e.push("groupedResult")}return t+=`){${e.join(" ")}}`,t}validate(){if(!this.groupedTask&&!this.singlePrompt)throw new Error("must provide at least one of `singlePrompt` or `groupTask`");if(this.groupedTask!==void 0&&this.groupedTask=="")throw new Error("groupedTask must not be empty");if(this.singlePrompt!==void 0&&this.singlePrompt=="")throw new Error("singlePrompt must not be empty")}};var be=class{constructor(t){this.args=t}toString(){let t=[];return this.args.path&&(t=[...t,`path:${JSON.stringify(this.args.path)}`]),this.args.groups&&(t=[...t,`groups:${this.args.groups}`]),this.args.objectsPerGroup&&(t=[...t,`objectsPerGroup:${this.args.objectsPerGroup}`]),`{${t.join(",")}}`}};var E=class extends n{constructor(e){super(e);this.withFields=e=>(this.fields=e,this);this.withClassName=e=>(this.className=e,this);this.withAfter=e=>(this.after=e,this);this.withGroup=e=>{try{this.groupString=new we(e).toString()}catch(r){this.addError(r.toString())}return this};this.withWhere=e=>{try{this.whereString=new p(e).toString()}catch(r){this.addError(r.toString())}return this};this.withNearText=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");return this.nearTextString=new m(e).toString(),this.includesNearMediaFilter=!0,this};this.withBm25=e=>{try{this.bm25String=new ve(e).toString()}catch(r){this.addError(r.toString())}return this};this.withHybrid=e=>{try{this.hybridString=new ye(e).toString()}catch(r){this.addError(r.toString())}return this};this.withNearObject=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearObjectString=new g(e).toString(),this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withAsk=e=>{try{this.askString=new k(e).toString()}catch(r){this.addError(r.toString())}return this};this.withNearMedia=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearMediaString=new u(e).toString(),this.nearMediaType=e.type,this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withNearImage=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearMediaString=new A(e).toString(),this.nearMediaType="Image",this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withNearAudio=e=>this.withNearMedia({...e,type:"Audio",media:e.audio});this.withNearVideo=e=>this.withNearMedia({...e,type:"Video",media:e.video});this.withNearThermal=e=>this.withNearMedia({...e,type:"Thermal",media:e.thermal});this.withNearDepth=e=>this.withNearMedia({...e,type:"Depth",media:e.depth});this.withNearIMU=e=>this.withNearMedia({...e,type:"IMU",media:e.imu});this.withNearVector=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearVectorString=new f(e).toString(),this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withLimit=e=>(this.limit=e,this);this.withOffset=e=>(this.offset=e,this);this.withAutocut=e=>(this.autocut=e,this);this.withSort=e=>(this.sortString=new Ce(e).toString(),this);this.withGenerate=e=>(this.generateString=new je(e).toString(),this);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.withGroupBy=e=>{try{this.groupByString=new be(e).toString()}catch(r){this.addError(r.toString())}return this};this.withTenant=e=>(this.tenant=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateIsSet(this.className,"className",".withClassName(className)"),this.validateIsSet(this.fields,"fields",".withFields(fields)")};this.do=()=>{let e="";if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let r=[];return this.whereString&&(r=[...r,`where:${this.whereString}`]),this.nearTextString&&(r=[...r,`nearText:${this.nearTextString}`]),this.nearObjectString&&(r=[...r,`nearObject:${this.nearObjectString}`]),this.askString&&(r=[...r,`ask:${this.askString}`]),this.nearMediaString&&(r=[...r,`near${this.nearMediaType}:${this.nearMediaString}`]),this.nearVectorString&&(r=[...r,`nearVector:${this.nearVectorString}`]),this.bm25String&&(r=[...r,`bm25:${this.bm25String}`]),this.hybridString&&(r=[...r,`hybrid:${this.hybridString}`]),this.groupString&&(r=[...r,`group:${this.groupString}`]),this.limit&&(r=[...r,`limit:${this.limit}`]),this.offset&&(r=[...r,`offset:${this.offset}`]),this.autocut&&(r=[...r,`autocut:${this.autocut}`]),this.sortString&&(r=[...r,`sort:[${this.sortString}]`]),this.after&&(r=[...r,`after:"${this.after}"`]),this.generateString&&(this.fields?.includes("_additional")?this.fields.replace("_additional{",`_additional{${this.generateString}`):this.fields=this.fields?.concat(` _additional{${this.generateString}}`)),this.consistencyLevel&&(r=[...r,`consistencyLevel:${this.consistencyLevel}`]),this.groupByString&&(r=[...r,`groupBy:${this.groupByString}`]),this.tenant&&(r=[...r,`tenant:"${this.tenant}"`]),r.length>0&&(e=`(${r.join(",")})`),this.client.query(`{Get{${this.className}${e}{${this.fields}}}}`)};this.includesNearMediaFilter=!1}};var I=class extends n{constructor(e){super(e);this.withFields=e=>(this.fields=e,this);this.withLimit=e=>(this.limit=e,this);this.withNearText=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearTextString=new m(e).toString()}catch(r){this.addError(r.toString())}return this};this.withNearObject=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearObjectString=new g(e).toString()}catch(r){this.addError(r.toString())}return this};this.withAsk=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.askString=new k(e).toString()}catch(r){this.addError(r.toString())}return this};this.withNearMedia=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearMediaString=new u(e).toString(),this.nearMediaType=e.type,this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withNearImage=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearMediaString=new A(e).toString(),this.nearMediaType="Image",this.includesNearMediaFilter=!0}catch(r){this.addError(r.toString())}return this};this.withNearAudio=e=>this.withNearMedia({...e,media:e.audio,type:"Audio"});this.withNearVideo=e=>this.withNearMedia({...e,media:e.video,type:"Video"});this.withNearDepth=e=>this.withNearMedia({...e,media:e.depth,type:"Depth"});this.withNearThermal=e=>this.withNearMedia({...e,media:e.thermal,type:"Thermal"});this.withNearIMU=e=>this.withNearMedia({...e,media:e.imu,type:"IMU"});this.withNearVector=e=>{if(this.includesNearMediaFilter)throw new Error("cannot use multiple near<Media> filters in a single query");try{this.nearVectorString=new f(e).toString()}catch(r){this.addError(r.toString())}return this};this.validateGroup=()=>{if(this.group&&!Array.isArray(this.group))throw new Error("groupBy must be an array")};this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateIsSet(this.fields,"fields",".withFields(fields)")};this.do=()=>{let e="";if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let r=[];return this.nearTextString&&(r=[...r,`nearText:${this.nearTextString}`]),this.nearObjectString&&(r=[...r,`nearObject:${this.nearObjectString}`]),this.askString&&(r=[...r,`ask:${this.askString}`]),this.nearMediaString&&(r=[...r,`${this.nearMediaType}:${this.nearMediaString}`]),this.nearVectorString&&(r=[...r,`nearVector:${this.nearVectorString}`]),this.limit&&(r=[...r,`limit:${this.limit}`]),e=`(${r.join(",")})`,this.client.query(`{Explore${e}{${this.fields}}}`)};this.params={},this.includesNearMediaFilter=!1}};var R=class extends n{constructor(e){super(e);this.withQuery=e=>(this.query=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateIsSet(this.query,"query",".raw().withQuery(query)")};this.do=()=>{let e="";return this.validate(),this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):this.query?this.client.query(this.query):Promise.resolve(void 0)}}};var ft=i=>({get:()=>new E(i),aggregate:()=>new B(i),explore:()=>new I(i),raw:()=>new R(i)}),Ke=ft;var O=class extends n{constructor(e){super(e);this.withClass=e=>(this.class=e,this);this.validateClass=()=>{(this.class==null||this.class==null)&&this.addError("class object must be set - set with .withClass(class)")};this.do=()=>{if(this.validateClass(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e="/schema";return this.client.post(e,this.class)}}validate(){this.validateClass()}};function o(i){return typeof i=="string"&&i.length>0}var w=class extends n{constructor(e){super(e);this.withClassName=e=>(this.className=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with .withClassName(className)")};this.validate=()=>{this.validateClassName()};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e=`/schema/${this.className}`;return this.client.delete(e,void 0,!1)}}};var Ne=class extends n{constructor(e){super(e);this.withClassName=e=>(this.className=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with .withClassName(className)")};this.validate=()=>{this.validateClassName()};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e="/schema";return this.client.get(e).then(r=>r.classes?.some(s=>s.class===this.className))}}};var M=class extends n{constructor(e){super(e);this.withClassName=e=>(this.className=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with .withClassName(className)")};this.validate=()=>{this.validateClassName()};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e=`/schema/${this.className}`;return this.client.get(e)}}};var V=class extends n{constructor(e){super(e);this.withClassName=e=>(this.className=e,this);this.withProperty=e=>(this.property=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with .withClassName(className)")};this.validateProperty=()=>{(this.property==null||this.property==null)&&this.addError("property must be set - set with .withProperty(property)")};this.validate=()=>{this.validateClassName(),this.validateProperty()};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e=`/schema/${this.className}/properties`;return this.client.post(e,this.property)}}};var C=class extends n{constructor(e){super(e);this.do=()=>{if(this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e="/schema";return this.client.get(e)}}validate(){}};var Pe=class extends n{constructor(e){super(e);this.withClassName=e=>(this.className=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with .withClassName(className)")};this.validate=()=>{this.validateClassName()};this.do=()=>(this.validate(),this.errors.length>0?Promise.reject(new Error(`invalid usage: ${this.errors.join(", ")}`)):qe(this.client,this.className))}};function qe(i,t){let e=`/schema/${t}/shards`;return i.get(e)}var F=class extends n{constructor(e){super(e);this.withClassName=e=>(this.className=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with .withClassName(className)")};this.withShardName=e=>(this.shardName=e,this);this.validateShardName=()=>{o(this.shardName)||this.addError("shardName must be set - set with .withShardName(shardName)")};this.withStatus=e=>(this.status=e,this);this.validateStatus=()=>{o(this.status)||this.addError("status must be set - set with .withStatus(status)")};this.validate=()=>{this.validateClassName(),this.validateShardName(),this.validateStatus()};this.do=()=>(this.validate(),this.errors.length>0?Promise.reject(new Error(`invalid usage: ${this.errors.join(", ")}`)):_e(this.client,this.className,this.shardName,this.status))}};function _e(i,t,e,r){let s=`/schema/${t}/shards/${e}`;return i.put(s,{status:r},!0)}var L=class extends n{constructor(e){super(e);this.withClassName=e=>(this.className=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with .withClassName(className)")};this.withStatus=e=>(this.status=e,this);this.validateStatus=()=>{o(this.status)||this.addError("status must be set - set with .withStatus(status)")};this.validate=()=>{this.validateClassName(),this.validateStatus()};this.updateShards=async()=>{let e=await Promise.all(Array.from({length:this.shards.length},(r,s)=>_e(this.client,this.className,this.shards[s].name||"",this.status).then(a=>({name:this.shards[s].name,status:a.status})).catch(a=>this.addError(a.toString()))));return this.errors.length>0?Promise.reject(new Error(`failed to update shards: ${this.errors.join(", ")}`)):Promise.resolve(e)};this.do=()=>(this.validate(),this.errors.length>0?Promise.reject(new Error(`invalid usage: ${this.errors.join(", ")}`)):qe(this.client,this.className).then(e=>this.shards=e).then(()=>this.updateShards()).then(e=>e).catch(e=>Promise.reject(e)));this.shards=[]}};var G=class extends n{constructor(e,r,s){super(e);this.validate=()=>{};this.do=()=>this.client.post(`/schema/${this.className}/tenants`,this.tenants);this.className=r,this.tenants=s}};var D=class extends n{constructor(e,r){super(e);this.validate=()=>{};this.do=()=>this.client.get(`/schema/${this.className}/tenants`);this.className=r}};var q=class extends n{constructor(e,r,s){super(e);this.validate=()=>{};this.do=()=>this.client.put(`/schema/${this.className}/tenants`,this.tenants);this.className=r,this.tenants=s}};var _=class extends n{constructor(e,r,s){super(e);this.validate=()=>{};this.do=()=>this.client.delete(`/schema/${this.className}/tenants`,this.tenants,!1);this.className=r,this.tenants=s}};var ze=async i=>{let e=await new C(i).do();await Promise.all(e.classes?e.classes.map(r=>new w(i).withClassName(r.class).do()):[])};var gt=i=>({classCreator:()=>new O(i),classDeleter:()=>new w(i),classGetter:()=>new M(i),exists:t=>new Ne(i).withClassName(t).do(),getter:()=>new C(i),propertyCreator:()=>new V(i),deleteAll:()=>ze(i),shardsGetter:()=>new Pe(i),shardUpdater:()=>new F(i),shardsUpdater:()=>new L(i),tenantsCreator:(t,e)=>new G(i,t,e),tenantsGetter:t=>new D(i,t),tenantsUpdater:(t,e)=>new q(i,t,e),tenantsDeleter:(t,e)=>new _(i,t,e)}),Qe=gt;var W=class extends n{constructor(e,r){super(e);this.withVector=e=>(this.vector=e,this);this.withClassName=e=>(this.className=e,this);this.withProperties=e=>(this.properties=e,this);this.withId=e=>(this.id=e,this);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.withTenant=e=>(this.tenant=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with .withClassName(className)")};this.payload=()=>({tenant:this.tenant,vector:this.vector,properties:this.properties,class:this.className,id:this.id});this.validate=()=>{this.validateClassName()};this.do=()=>(this.validate(),this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):this.objectsPath.buildCreate(this.consistencyLevel).then(e=>this.client.post(e,this.payload())));this.objectsPath=r}};var U=class extends n{constructor(e){super(e);this.withClassName=e=>(this.className=e,this);this.withProperties=e=>(this.properties=e,this);this.withId=e=>(this.id=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with .withClassName(className)")};this.payload=()=>({properties:this.properties,class:this.className,id:this.id});this.validate=()=>{this.validateClassName()};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e="/objects/validate";return this.client.post(e,this.payload(),!1).then(()=>!0)}}};var J=class extends n{constructor(e,r){super(e);this.withProperties=e=>(this.properties=e,this);this.withId=e=>(this.id=e,this);this.withClassName=e=>(this.className=e,this);this.withTenant=e=>(this.tenant=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - use withClassName(className)")};this.validateId=()=>{(this.id==null||this.id==null||this.id.length==0)&&this.addError("id must be set - initialize with updater(id)")};this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.payload=()=>({tenant:this.tenant,properties:this.properties,class:this.className,id:this.id});this.validate=()=>{this.validateClassName(),this.validateId()};this.do=()=>(this.validate(),this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):this.objectsPath.buildUpdate(this.id,this.className,this.consistencyLevel).then(e=>this.client.put(e,this.payload())));this.objectsPath=r}};var H=class extends n{constructor(e,r){super(e);this.withProperties=e=>(this.properties=e,this);this.withClassName=e=>(this.className=e,this);this.withId=e=>(this.id=e,this);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.withTenant=e=>(this.tenant=e,this);this.validateClassName=()=>{o(this.className)||this.addError("className must be set - set with withClassName(className)")};this.validateId=()=>{(this.id==null||this.id==null||this.id.length==0)&&this.addError("id must be set - set with withId(id)")};this.payload=()=>({tenant:this.tenant,properties:this.properties,class:this.className,id:this.id});this.validate=()=>{this.validateClassName(),this.validateId()};this.do=()=>(this.validate(),this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):this.objectsPath.buildMerge(this.id,this.className,this.consistencyLevel).then(e=>this.client.patch(e,this.payload())));this.objectsPath=r}};var K=class extends n{constructor(e,r){super(e);this.withClassName=e=>(this.className=e,this);this.withAfter=e=>(this.after=e,this);this.withLimit=e=>(this.limit=e,this);this.withTenant=e=>(this.tenant=e,this);this.extendAdditional=e=>(this.additional=[...this.additional,e],this);this.withAdditional=e=>this.extendAdditional(e);this.withVector=()=>this.extendAdditional("vector");this.do=()=>this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):this.objectsPath.buildGet(this.className,this.limit,this.additional,this.after,this.tenant).then(e=>this.client.get(e));this.objectsPath=r,this.additional=[]}validate(){}};var z=class extends n{constructor(e,r){super(e);this.withId=e=>(this.id=e,this);this.withClassName=e=>(this.className=e,this);this.withTenant=e=>(this.tenant=e,this);this.extendAdditional=e=>(this.additional=[...this.additional,e],this);this.withAdditional=e=>this.extendAdditional(e);this.withVector=()=>this.extendAdditional("vector");this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.withNodeName=e=>(this.nodeName=e,this);this.validateId=()=>{(this.id==null||this.id==null||this.id.length==0)&&this.addError("id must be set - initialize with getterById(id)")};this.validate=()=>{this.validateId()};this.buildPath=()=>this.objectsPath.buildGetOne(this.id,this.className,this.additional,this.consistencyLevel,this.nodeName,this.tenant);this.do=()=>(this.validate(),this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):this.buildPath().then(e=>this.client.get(e)));this.objectsPath=r,this.additional=[]}};var Q=class extends n{constructor(e,r){super(e);this.withId=e=>(this.id=e,this);this.withClassName=e=>(this.className=e,this);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.withTenant=e=>(this.tenant=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validateId=()=>{this.validateIsSet(this.id,"id",".withId(id)")};this.validate=()=>{this.validateId()};this.do=()=>this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):(this.validate(),this.objectsPath.buildDelete(this.id,this.className,this.consistencyLevel,this.tenant).then(e=>this.client.delete(e,void 0,!1)));this.objectsPath=r}};var Y=class extends n{constructor(e,r){super(e);this.withId=e=>(this.id=e,this);this.withClassName=e=>(this.className=e,this);this.withTenant=e=>(this.tenant=e,this);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.buildPath=()=>this.objectsPath.buildCheck(this.id,this.className,this.consistencyLevel,this.tenant);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validateId=()=>{this.validateIsSet(this.id,"id",".withId(id)")};this.validate=()=>{this.validateId()};this.do=()=>this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):(this.validate(),this.buildPath().then(e=>this.client.head(e,void 0)));this.objectsPath=r}};var X=class extends n{constructor(e,r,s){super(e);this.withId=e=>(this.id=e,this);this.withReference=e=>(this.reference=e,this);this.withReferenceProperty=e=>(this.refProp=e,this);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.withTenant=e=>(this.tenant=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateIsSet(this.id,"id",".withId(id)"),this.validateIsSet(this.refProp,"referenceProperty",".withReferenceProperty(refProp)")};this.payload=()=>this.reference;this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));if(!this.reference.beacon)throw new Error("reference beacon must be set");return Promise.all([this.referencesPath.build(this.id,this.className,this.refProp,this.consistencyLevel,this.tenant),this.beaconPath.rebuild(this.reference.beacon)]).then(e=>{let r=e[0],s=e[1];return this.client.post(r,{beacon:s},!1)})};this.referencesPath=r,this.beaconPath=s}withClassName(e){return this.className=e,this}};var Z=class extends n{constructor(e,r,s){super(e);this.withId=e=>(this.id=e,this);this.withReferences=e=>(this.references=e,this);this.withReferenceProperty=e=>(this.refProp=e,this);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.withTenant=e=>(this.tenant=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateIsSet(this.id,"id",".withId(id)"),this.validateIsSet(this.refProp,"referenceProperty",".withReferenceProperty(refProp)")};this.payload=()=>this.references;this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e=Array.isArray(this.references)?Promise.all(this.references.map(r=>this.rebuildReferencePromise(r))):Promise.resolve([]);return Promise.all([this.referencesPath.build(this.id,this.className,this.refProp,this.consistencyLevel,this.tenant),e]).then(r=>{let s=r[0],a=r[1];return this.client.put(s,a,!1)})};this.beaconPath=s,this.referencesPath=r}withClassName(e){return this.className=e,this}rebuildReferencePromise(e){return this.beaconPath.rebuild(e.beacon).then(r=>({beacon:r}))}};var ee=class extends n{constructor(e,r,s){super(e);this.withId=e=>(this.id=e,this);this.withReference=e=>(this.reference=e,this);this.withReferenceProperty=e=>(this.refProp=e,this);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.withTenant=e=>(this.tenant=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateIsSet(this.id,"id",".withId(id)"),this.validateIsSet(this.refProp,"referenceProperty",".withReferenceProperty(refProp)")};this.payload=()=>this.reference;this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));if(!this.reference.beacon)throw new Error("reference beacon must be set");return Promise.all([this.referencesPath.build(this.id,this.className,this.refProp,this.consistencyLevel,this.tenant),this.beaconPath.rebuild(this.reference.beacon)]).then(e=>{let r=e[0],s=e[1];return this.client.delete(r,{beacon:s},!1)})};this.referencesPath=r,this.beaconPath=s}withClassName(e){return this.className=e,this}};var te=class extends n{constructor(e){super(e);this.withId=e=>(this.id=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateIsSet(this.id,"id",".withId(id)")};this.payload=()=>{if(this.validate(),this.errors.length>0)throw new Error(this.errors.join(", "));let e="weaviate://localhost";return o(this.className)&&(e=`${e}/${this.className}`),{beacon:`${e}/${this.id}`}}}withClassName(e){return this.className=e,this}do(){return Promise.reject(new Error("Should never be called"))}};function Te(i){if(typeof i=="string"){let t=i.split(".");if(t.length>=2){let e=parseInt(t[0],10),r=parseInt(t[1],10);return!(e<=1&&r<16)}}return!0}var Ye="/objects",$e=class{constructor(t){this.dbVersionSupport=t}buildCreate(t){return this.build({consistencyLevel:t},[this.addQueryParams])}buildDelete(t,e,r,s){return this.build({id:t,className:e,consistencyLevel:r,tenant:s},[this.addClassNameDeprecatedNotSupportedCheck,this.addId,this.addQueryParams])}buildCheck(t,e,r,s){return this.build({id:t,className:e,consistencyLevel:r,tenant:s},[this.addClassNameDeprecatedNotSupportedCheck,this.addId,this.addQueryParams])}buildGetOne(t,e,r,s,a,h){return this.build({id:t,className:e,additional:r,consistencyLevel:s,nodeName:a,tenant:h},[this.addClassNameDeprecatedNotSupportedCheck,this.addId,this.addQueryParams])}buildGet(t,e,r,s,a){return this.build({className:t,limit:e,additional:r,after:s,tenant:a},[this.addQueryParamsForGet])}buildUpdate(t,e,r){return this.build({id:t,className:e,consistencyLevel:r},[this.addClassNameDeprecatedCheck,this.addId,this.addQueryParams])}buildMerge(t,e,r){return this.build({id:t,className:e,consistencyLevel:r},[this.addClassNameDeprecatedCheck,this.addId,this.addQueryParams])}build(t,e){return this.dbVersionSupport.supportsClassNameNamespacedEndpointsPromise().then(r=>{let s=Ye;return e.forEach(a=>{s=a(t,s,r)}),s})}addClassNameDeprecatedNotSupportedCheck(t,e,r){if(r.supports){if(o(t.className))return`${e}/${t.className}`;r.warns.deprecatedNonClassNameNamespacedEndpointsForObjects()}else r.warns.notSupportedClassNamespacedEndpointsForObjects();return e}addClassNameDeprecatedCheck(t,e,r){if(r.supports){if(o(t.className))return`${e}/${t.className}`;r.warns.deprecatedNonClassNameNamespacedEndpointsForObjects()}return e}addId(t,e){return o(t.id)?`${e}/${t.id}`:e}addQueryParams(t,e){let r=[];return Array.isArray(t.additional)&&t.additional.length>0&&r.push(`include=${t.additional.join(",")}`),o(t.nodeName)&&r.push(`node_name=${t.nodeName}`),o(t.consistencyLevel)&&r.push(`consistency_level=${t.consistencyLevel}`),o(t.tenant)&&r.push(`tenant=${t.tenant}`),r.length>0?`${e}?${r.join("&")}`:e}addQueryParamsForGet(t,e,r){let s=[];return Array.isArray(t.additional)&&t.additional.length>0&&s.push(`include=${t.additional.join(",")}`),typeof t.limit=="number"&&t.limit>0&&s.push(`limit=${t.limit}`),o(t.className)&&(r.supports?s.push(`class=${t.className}`):r.warns.notSupportedClassParameterInEndpointsForObjects()),o(t.after)&&s.push(`after=${t.after}`),o(t.tenant)&&s.push(`tenant=${t.tenant}`),s.length>0?`${e}?${s.join("&")}`:e}},Be=class{constructor(t){this.dbVersionSupport=t}build(t,e,r,s,a){return this.dbVersionSupport.supportsClassNameNamespacedEndpointsPromise().then(h=>{let c=Ye;h.supports?o(e)?c=`${c}/${e}`:h.warns.deprecatedNonClassNameNamespacedEndpointsForReferences():h.warns.notSupportedClassNamespacedEndpointsForReferences(),h.version&&(Te(h.version)||h.warns.deprecatedWeaviateTooOld()),o(t)&&(c=`${c}/${t}`),c=`${c}/references`,o(r)&&(c=`${c}/${r}`);let d=[];return o(s)&&d.push(`consistency_level=${s}`),o(a)&&d.push(`tenant=${a}`),d.length>0&&(c=`${c}?${d.join("&")}`),c})}};var vt="weaviate://localhost",re=class{constructor(t){this.dbVersionSupport=t,this.beaconRegExp=/^weaviate:\/\/localhost(\/([^\\/]+))?(\/([^\\/]+))?[\\/]?$/gi}async rebuild(t){let e=await this.dbVersionSupport.supportsClassNameNamespacedEndpointsPromise(),r=new RegExp(this.beaconRegExp).exec(t);if(!r)return t;let s,a;r[4]!==void 0?(a=r[4],s=r[2]):a=r[2];let h=vt;return e.supports?o(s)?h=`${h}/${s}`:e.warns.deprecatedNonClassNameNamespacedEndpointsForBeacons():e.warns.notSupportedClassNamespacedEndpointsForBeacons(),e.version&&(Te(e.version)||e.warns.deprecatedWeaviateTooOld()),o(a)&&(h=`${h}/${a}`),h}};var yt=(i,t)=>{let e=new $e(t),r=new Be(t),s=new re(t);return{creator:()=>new W(i,e),validator:()=>new U(i),updater:()=>new J(i,e),merger:()=>new H(i,e),getter:()=>new K(i,e),getterById:()=>new z(i,e),deleter:()=>new Q(i,e),checker:()=>new Y(i,e),referenceCreator:()=>new X(i,r,s),referenceReplacer:()=>new Z(i,r,s),referenceDeleter:()=>new ee(i,r,s),referencePayloadBuilder:()=>new te(i)}},Xe=yt;var b=class extends n{constructor(e){super(e);this.withId=e=>(this.id=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validateId=()=>{this.validateIsSet(this.id,"id",".withId(id)")};this.validate=()=>{this.validateId()};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e=`/classifications/${this.id}`;return this.client.get(e)}}};var ie=class extends n{constructor(e){super(e);this.withType=e=>(this.type=e,this);this.withSettings=e=>(this.settings=e,this);this.withClassName=e=>(this.className=e,this);this.withClassifyProperties=e=>(this.classifyProperties=e,this);this.withBasedOnProperties=e=>(this.basedOnProperties=e,this);this.withWaitForCompletion=()=>(this.waitForCompletion=!0,this);this.withWaitTimeout=e=>(this.waitTimeout=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validateClassName=()=>{this.validateIsSet(this.className,"className",".withClassName(className)")};this.validateBasedOnProperties=()=>{this.validateIsSet(this.basedOnProperties,"basedOnProperties",".withBasedOnProperties(basedOnProperties)")};this.validateClassifyProperties=()=>{this.validateIsSet(this.classifyProperties,"classifyProperties",".withClassifyProperties(classifyProperties)")};this.validate=()=>{this.validateClassName(),this.validateClassifyProperties(),this.validateBasedOnProperties()};this.payload=()=>({type:this.type,settings:this.settings,class:this.className,classifyProperties:this.classifyProperties,basedOnProperties:this.basedOnProperties});this.pollForCompletion=e=>new Promise((r,s)=>{let a=setTimeout(()=>{clearInterval(h),clearTimeout(a),s(new Error("classification didn't finish within configured timeout, set larger timeout with .withWaitTimeout(timeout)"))},this.waitTimeout),h=setInterval(()=>{new b(this.client).withId(e).do().then(c=>{c.status==="completed"&&(clearInterval(h),clearTimeout(a),r(c))})},500)});this.do=()=>{if(this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));this.validate();let e="/classifications";return this.client.post(e,this.payload()).then(r=>this.waitForCompletion?this.pollForCompletion(r.id):Promise.resolve(r))};this.waitTimeout=10*60*1e3,this.waitForCompletion=!1}};var wt=i=>({scheduler:()=>new ie(i),getter:()=>new b(i)}),Ze=wt;function Ee(i){return tt("/batch/objects",i)}function et(i){return tt("/batch/references",i)}function tt(i,t){return t&&t.toString()!=""&&(i=`${i}?${t.toString()}`),i}var se=class extends n{constructor(e){super(e);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.payload=()=>({objects:this.objects});this.validateObjectCount=()=>{this.objects.length==0&&this.addError("need at least one object to send a request, add one with .withObject(obj)")};this.validate=()=>{this.validateObjectCount()};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e=new URLSearchParams;this.consistencyLevel&&e.set("consistency_level",this.consistencyLevel);let r=Ee(e);return this.client.post(r,this.payload())};this.objects=[]}withObjects(...e){let r=e;return e.length&&Array.isArray(e[0])&&(r=e[0]),this.objects=[...this.objects,...r],this}withObject(e){return this.withObjects(e)}};var ne=class extends n{constructor(e){super(e);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.payload=()=>({match:{class:this.className,where:this.whereFilter},output:this.output,dryRun:this.dryRun});this.validateClassName=()=>{o(this.className)||this.addError("string className must be set - set with .withClassName(className)")};this.validateWhereFilter=()=>{typeof this.whereFilter!="object"&&this.addError("object where must be set - set with .withWhere(whereFilter)")};this.validate=()=>{this.validateClassName(),this.validateWhereFilter()};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e=new URLSearchParams;this.consistencyLevel&&e.set("consistency_level",this.consistencyLevel),this.tenant&&e.set("tenant",this.tenant);let r=Ee(e);return this.client.delete(r,this.payload(),!0)}}withClassName(e){return this.className=e,this}withWhere(e){return this.whereFilter=e,this}withOutput(e){return this.output=e,this}withDryRun(e){return this.dryRun=e,this}withTenant(e){return this.tenant=e,this}};var ae=class extends n{constructor(e,r){super(e);this.withConsistencyLevel=e=>(this.consistencyLevel=e,this);this.payload=()=>this.references;this.validateReferenceCount=()=>{this.references.length==0&&this.addError("need at least one reference to send a request, add one with .withReference(obj)")};this.validate=()=>{this.validateReferenceCount()};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e=new URLSearchParams;this.consistencyLevel&&e.set("consistency_level",this.consistencyLevel);let r=et(e);return Promise.all(this.references.map(a=>this.rebuildReferencePromise(a))).then(a=>this.client.post(r,a))};this.rebuildReferencePromise=e=>this.beaconPath.rebuild(e.to).then(r=>({from:e.from,to:r,tenant:e.tenant}));this.beaconPath=r,this.references=[]}withReferences(...e){let r=e;return e.length&&Array.isArray(e[0])&&(r=e[0]),this.references=[...this.references,...r],this}withReference(e){return this.withReferences(e)}};var xe=class extends n{constructor(e){super(e);this.withFromId=e=>(this.fromId=e,this);this.withToId=e=>(this.toId=e,this);this.withFromClassName=e=>(this.fromClassName=e,this);this.withFromRefProp=e=>(this.fromRefProp=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateIsSet(this.fromId,"fromId",".withFromId(id)"),this.validateIsSet(this.toId,"toId",".withToId(id)"),this.validateIsSet(this.fromClassName,"fromClassName",".withFromClassName(className)"),this.validateIsSet(this.fromRefProp,"fromRefProp",".withFromRefProp(refProp)")};this.payload=()=>{if(this.validate(),this.errors.length>0)throw new Error(this.errors.join(", "));let e="weaviate://localhost";return o(this.toClassName)&&(e=`${e}/${this.toClassName}`),{from:`weaviate://localhost/${this.fromClassName}/${this.fromId}/${this.fromRefProp}`,to:`${e}/${this.toId}`}};this.do=()=>Promise.reject(new Error("Should never be called"))}withToClassName(e){return this.toClassName=e,this}};var Ct=(i,t)=>{let e=new re(t);return{objectsBatcher:()=>new se(i),objectsBatchDeleter:()=>new ne(i),referencesBatcher:()=>new ae(i,e),referencePayloadBuilder:()=>new xe(i)}},rt=Ct;var oe=class extends n{constructor(e,r){super(e);this.do=()=>this.client.get("/.well-known/live",!1).then(()=>(setTimeout(()=>this.dbVersionProvider.refresh()),Promise.resolve(!0))).catch(()=>Promise.resolve(!1));this.dbVersionProvider=r}validate(){}};var he=class extends n{constructor(e,r){super(e);this.do=()=>this.client.get("/.well-known/ready",!1).then(()=>(setTimeout(()=>this.dbVersionProvider.refresh()),Promise.resolve(!0))).catch(()=>Promise.resolve(!1));this.dbVersionProvider=r}validate(){}};var N=class extends n{constructor(e){super(e);this.do=()=>this.client.get("/meta",!0)}validate(){}};var bt=(i,t)=>({liveChecker:()=>new oe(i,t),readyChecker:()=>new he(i,t),metaGetter:()=>new N(i),openidConfigurationGetter:()=>new v(i.http)}),it=bt;var ce=class extends n{constructor(e){super(e);this.withConcept=e=>(this.concept=e,this);this.withDefinition=e=>(this.definition=e,this);this.withWeight=e=>(this.weight=e,this);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.validate=()=>{this.validateIsSet(this.concept,"concept","withConcept(concept)"),this.validateIsSet(this.definition,"definition","withDefinition(definition)"),this.validateIsSet(this.weight?.toString()||"","weight","withWeight(weight)")};this.payload=()=>({concept:this.concept,definition:this.definition,weight:this.weight});this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e="/modules/text2vec-contextionary/extensions";return this.client.post(e,this.payload())}}};var de=class extends n{constructor(e){super(e);this.validateIsSet=(e,r,s)=>{(e==null||e==null||e.length==0)&&this.addError(`${r} must be set - set with ${s}`)};this.withConcept=e=>(this.concept=e,this);this.validate=()=>{this.validateIsSet(this.concept,"concept","withConcept(concept)")};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e=`/modules/text2vec-contextionary/concepts/${this.concept}`;return this.client.get(e)}}};var Nt=i=>({conceptsGetter:()=>new de(i),extensionCreator:()=>new ce(i)}),st=Nt;var Ie=class{constructor(t){this.dbVersionProvider=t}supportsClassNameNamespacedEndpointsPromise(){return this.dbVersionProvider.getVersionPromise().then(t=>({version:t,supports:this.supportsClassNameNamespacedEndpoints(t),warns:{deprecatedNonClassNameNamespacedEndpointsForObjects:()=>console.warn(`Usage of objects paths without className is deprecated in Weaviate ${t}. Please provide className parameter`),deprecatedNonClassNameNamespacedEndpointsForReferences:()=>console.warn(`Usage of references paths without className is deprecated in Weaviate ${t}. Please provide className parameter`),deprecatedNonClassNameNamespacedEndpointsForBeacons:()=>console.warn(`Usage of beacons paths without className is deprecated in Weaviate ${t}. Please provide className parameter`),deprecatedWeaviateTooOld:()=>console.warn(`Usage of weaviate ${t} is deprecated. Please consider upgrading to the latest version. See https://www.weaviate.io/developers/weaviate for details.`),notSupportedClassNamespacedEndpointsForObjects:()=>console.warn(`Usage of objects paths with className is not supported in Weaviate ${t}. className parameter is ignored`),notSupportedClassNamespacedEndpointsForReferences:()=>console.warn(`Usage of references paths with className is not supported in Weaviate ${t}. className parameter is ignored`),notSupportedClassNamespacedEndpointsForBeacons:()=>console.warn(`Usage of beacons paths with className is not supported in Weaviate ${t}. className parameter is ignored`),notSupportedClassParameterInEndpointsForObjects:()=>console.warn(`Usage of objects paths with class query parameter is not supported in Weaviate ${t}. class query parameter is ignored`)}}))}supportsClassNameNamespacedEndpoints(t){if(typeof t=="string"){let e=t.split(".");if(e.length>=2){let r=parseInt(e[0],10),s=parseInt(e[1],10);return r==1&&s>=14||r>=2}}return!1}},nt="",Re=class{constructor(t){this.versionGetter=t,this.emptyVersionPromise=Promise.resolve(nt),this.versionPromise=void 0}getVersionPromise(){return this.versionPromise?this.versionPromise:this.versionGetter().then(t=>this.assignPromise(t))}refresh(t=!1){return t||!this.versionPromise?(this.versionPromise=void 0,this.versionGetter().then(e=>this.assignPromise(e)).then(()=>Promise.resolve(!0))):Promise.resolve(!1)}assignPromise(t){return t===nt?this.emptyVersionPromise:(this.versionPromise=Promise.resolve(t),this.versionPromise)}};function Oe(i){if(Array.isArray(i)){let t=[];return i.forEach(e=>{o(e)||t.push("string className invalid - set with .withIncludeClassNames(...classNames)")}),t}return i!=null?["strings classNames invalid - set with .withIncludeClassNames(...classNames)"]:[]}function Me(i){if(Array.isArray(i)){let t=[];return i.forEach(e=>{o(e)||t.push("string className invalid - set with .withExcludeClassNames(...classNames)")}),t}return i!=null?["strings classNames invalid - set with .withExcludeClassNames(...classNames)"]:[]}function P(i){return o(i)?[]:["string backend must set - set with .withBackend(backend)"]}function x(i){return o(i)?[]:["string backupId must be set - set with .withBackupId(backupId)"]}var Pt=1e3,le=class extends n{constructor(e,r){super(e);this.validate=()=>{this.addErrors([...Oe(this.includeClassNames),...Me(this.excludeClassNames),...P(this.backend),...x(this.backupId)])};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e={id:this.backupId,config:{},include:this.includeClassNames,exclude:this.excludeClassNames};return this.waitForCompletion?this._createAndWaitForCompletion(e):this._create(e)};this._create=e=>this.client.post(this._path(),e);this._createAndWaitForCompletion=e=>new Promise((r,s)=>{this._create(e).then(a=>{this.statusGetter.withBackend(this.backend).withBackupId(this.backupId);let h=()=>{this.statusGetter.do().then(c=>{c.status=="SUCCESS"||c.status=="FAILED"?r(this._merge(c,a)):setTimeout(h,Pt)}).catch(s)};h()}).catch(s)});this._path=()=>`/backups/${this.backend}`;this._merge=(e,r)=>{let s={};return"id"in e&&(s.id=e.id),"path"in e&&(s.path=e.path),"backend"in e&&(s.backend=e.backend),"status"in e&&(s.status=e.status),"error"in e&&(s.error=e.error),"classes"in r&&(s.classes=r.classes),s};this.statusGetter=r}withIncludeClassNames(...e){let r=e;return e.length&&Array.isArray(e[0])&&(r=e[0]),this.includeClassNames=r,this}withExcludeClassNames(...e){let r=e;return e.length&&Array.isArray(e[0])&&(r=e[0]),this.excludeClassNames=r,this}withBackend(e){return this.backend=e,this}withBackupId(e){return this.backupId=e,this}withWaitForCompletion(e){return this.waitForCompletion=e,this}};var j=class extends n{constructor(e){super(e);this.validate=()=>{this.addErrors([...P(this.backend),...x(this.backupId)])};this.do=()=>(this.validate(),this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):this.client.get(this._path()));this._path=()=>`/backups/${this.backend}/${this.backupId}`}withBackend(e){return this.backend=e,this}withBackupId(e){return this.backupId=e,this}};var xt=1e3,pe=class extends n{constructor(e,r){super(e);this.validate=()=>{this.addErrors([...Oe(this.includeClassNames||[]),...Me(this.excludeClassNames||[]),...P(this.backend),...x(this.backupId)])};this.do=()=>{if(this.validate(),this.errors.length>0)return Promise.reject(new Error("invalid usage: "+this.errors.join(", ")));let e={config:{},include:this.includeClassNames,exclude:this.excludeClassNames};return this.waitForCompletion?this._restoreAndWaitForCompletion(e):this._restore(e)};this._restore=e=>this.client.post(this._path(),e);this._restoreAndWaitForCompletion=e=>new Promise((r,s)=>{this._restore(e).then(a=>{this.statusGetter.withBackend(this.backend).withBackupId(this.backupId);let h=()=>{this.statusGetter.do().then(c=>{c.status=="SUCCESS"||c.status=="FAILED"?r(this._merge(c,a)):setTimeout(h,xt)}).catch(s)};h()}).catch(s)});this._path=()=>`/backups/${this.backend}/${this.backupId}/restore`;this._merge=(e,r)=>{let s={};return"id"in e&&(s.id=e.id),"path"in e&&(s.path=e.path),"backend"in e&&(s.backend=e.backend),"status"in e&&(s.status=e.status),"error"in e&&(s.error=e.error),"classes"in r&&(s.classes=r.classes),s};this.statusGetter=r}withIncludeClassNames(...e){let r=e;return e.length&&Array.isArray(e[0])&&(r=e[0]),this.includeClassNames=r,this}withExcludeClassNames(...e){let r=e;return e.length&&Array.isArray(e[0])&&(r=e[0]),this.excludeClassNames=r,this}withBackend(e){return this.backend=e,this}withBackupId(e){return this.backupId=e,this}withWaitForCompletion(e){return this.waitForCompletion=e,this}};var T=class extends n{constructor(e){super(e);this.validate=()=>{this.addErrors([...P(this.backend),...x(this.backupId)])};this.do=()=>(this.validate(),this.errors.length>0?Promise.reject(new Error("invalid usage: "+this.errors.join(", "))):this.client.get(this._path()));this._path=()=>`/backups/${this.backend}/${this.backupId}/restore`}withBackend(e){return this.backend=e,this}withBackupId(e){return this.backupId=e,this}};var St=i=>({creator:()=>new le(i,new j(i)),createStatusGetter:()=>new j(i),restorer:()=>new pe(i,new T(i)),restoreStatusGetter:()=>new T(i)}),at=St;var ue=class extends n{constructor(e){super(e);this.withClassName=e=>(this.className=e,this);this.do=()=>this.className?this.client.get(`/nodes/${this.className}`):this.client.get("/nodes")}validate(){}};var At=i=>({nodesStatusGetter:()=>new ue(i)}),ot=At;import{v5 as ht}from"uuid";function Io(i,t=""){let e=i.toString()+t.toString();return ht(e,ht.DNS).toString()}function Oo(i){if(typeof window>"u")throw new Error("This function is only available in the browser");let t=new FileReader;return new Promise((e,r)=>{t.onload=e,t.onerror=r,t.readAsDataURL(i)}).then(()=>{if(typeof t.result!="string")throw new Error(`Unexpected result when converting blob to base64 (result is not a string): ${t.result}`);return t.result})}var kt={client:function(i){if(!i.host)throw new Error("Missing `host` parameter");i.headers||(i.headers={});let t=new ge(i),e=jt(t),r=new Ie(e),s={graphql:Ke(t),schema:Qe(t),data:Xe(t,r),classifications:Ze(t),batch:rt(t,r),misc:it(t,e),c11y:st(t),backup:at(t),cluster:ot(t)};return t.oidcAuth&&(s.oidcAuth=t.oidcAuth),s},ApiKey:Ae,AuthUserPasswordCredentials:me,AuthAccessTokenCredentials:$,AuthClientCredentials:fe};function jt(i){let t=new N(i),e=()=>t.do().then(s=>s.version).catch(()=>Promise.resolve("")),r=new Re(e);return r.refresh(),r}var ah=kt;export{B as Aggregator,Ae as ApiKey,$ as AuthAccessTokenCredentials,fe as AuthClientCredentials,me as AuthUserPasswordCredentials,j as BackupCreateStatusGetter,le as BackupCreator,T as BackupRestoreStatusGetter,pe as BackupRestorer,Y as Checker,O as ClassCreator,w as ClassDeleter,M as ClassGetter,b as ClassificationsGetter,ie as ClassificationsScheduler,de as ConceptsGetter,W as Creator,Q as Deleter,I as Explorer,ce as ExtensionCreator,De as FusionType,K as Getter,z as GetterById,E as GraphQLGetter,oe as LiveChecker,H as Merger,N as MetaGetter,ue as NodesStatusGetter,ne as ObjectsBatchDeleter,se as ObjectsBatcher,Se as OidcAuthenticator,v as OpenidConfigurationGetter,V as PropertyCreator,R as Raw,he as ReadyChecker,X as ReferenceCreator,ee as ReferenceDeleter,te as ReferencePayloadBuilder,Z as ReferenceReplacer,ae as ReferencesBatcher,C as SchemaGetter,F as ShardUpdater,L as ShardsUpdater,G as TenantsCreator,_ as TenantsDeleter,D as TenantsGetter,q as TenantsUpdater,J as Updater,U as Validator,ah as default,Io as generateUuid5,Oo as toBase64FromBlob};
