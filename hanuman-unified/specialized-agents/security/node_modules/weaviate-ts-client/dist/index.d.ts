import { Variables } from 'graphql-request';

interface NearMediaBase {
    certainty?: number;
    distance?: number;
}
interface NearAudioArgs extends NearMediaBase {
    audio: string;
}
interface NearVideoArgs extends NearMediaBase {
    video: string;
}
interface NearThermalArgs extends NearMediaBase {
    thermal: string;
}
interface NearDepthArgs extends NearMediaBase {
    depth: string;
}
interface NearIMUArgs extends NearMediaBase {
    imu: string;
}

interface NearTextArgs {
    autocorrect?: boolean;
    certainty?: number;
    concepts: string[];
    distance?: number;
    moveAwayFrom?: Move;
    moveTo?: Move;
}
interface Move {
    objects?: MoveObject[];
    concepts?: string[];
    force?: number;
}
interface MoveObject {
    beacon?: string;
    id?: string;
}

interface NearVectorArgs {
    certainty?: number;
    distance?: number;
    vector: number[];
}

interface NearObjectArgs {
    beacon?: string;
    certainty?: number;
    distance?: number;
    id?: string;
}

interface HttpClient {
    patch: (path: string, payload: any, bearerToken?: string) => any;
    head: (path: string, payload: any, bearerToken?: string) => any;
    post: (path: string, payload: any, expectReturnContent?: boolean, bearerToken?: string) => any;
    get: (path: string, expectReturnContent?: boolean, bearerToken?: string) => any;
    externalPost: (externalUrl: string, body: any, contentType: any) => any;
    getRaw: (path: string, bearerToken?: string) => any;
    delete: (path: string, payload: any, expectReturnContent?: boolean, bearerToken?: string) => any;
    put: (path: string, payload: any, expectReturnContent?: boolean, bearerToken?: string) => any;
    externalGet: (externalUrl: string) => Promise<any>;
}

interface AuthenticatorResult {
    accessToken: string;
    expiresAt: number;
    refreshToken: string;
}
interface OidcCredentials {
    silentRefresh: boolean;
}
interface OidcAuthFlow {
    refresh: () => Promise<AuthenticatorResult>;
}
declare class OidcAuthenticator {
    private readonly http;
    private readonly creds;
    private accessToken;
    private refreshToken?;
    private expiresAt;
    private refreshRunning;
    private refreshInterval;
    constructor(http: HttpClient, creds: any);
    refresh: (localConfig: any) => Promise<void>;
    getOpenidConfig: (localConfig: any) => Promise<{
        clientId: any;
        provider: any;
        scopes: any;
    }>;
    startTokenRefresh: (authenticator: {
        refresh: () => any;
    }) => void;
    stopTokenRefresh: () => void;
    refreshTokenProvided: () => boolean | "" | undefined;
    getAccessToken: () => string;
    getExpiresAt: () => number;
    resetExpiresAt(): void;
}
interface UserPasswordCredentialsInput {
    username: string;
    password?: string;
    scopes?: any[];
    silentRefresh?: boolean;
}
declare class AuthUserPasswordCredentials implements OidcCredentials {
    private username;
    private password?;
    private scopes?;
    readonly silentRefresh: boolean;
    constructor(creds: UserPasswordCredentialsInput);
}
interface AccessTokenCredentialsInput {
    accessToken: string;
    expiresIn: number;
    refreshToken?: string;
    silentRefresh?: boolean;
}
declare class AuthAccessTokenCredentials implements OidcCredentials {
    readonly accessToken: string;
    readonly expiresAt: number;
    readonly refreshToken?: string;
    readonly silentRefresh: boolean;
    constructor(creds: AccessTokenCredentialsInput);
    validate: (creds: AccessTokenCredentialsInput) => void;
}
interface ClientCredentialsInput {
    clientSecret: string;
    scopes?: any[];
    silentRefresh?: boolean;
}
declare class AuthClientCredentials implements OidcCredentials {
    private clientSecret;
    private scopes?;
    readonly silentRefresh: boolean;
    constructor(creds: ClientCredentialsInput);
}
declare class ApiKey {
    readonly apiKey: string;
    constructor(apiKey: string);
}

declare class Connection {
    private apiKey?;
    private authEnabled;
    private gql;
    readonly host: string;
    readonly http: HttpClient;
    oidcAuth?: OidcAuthenticator;
    constructor(params: ConnectionParams);
    private parseAuthParams;
    private sanitizeParams;
    post: (path: string, payload: any, expectReturnContent?: boolean) => any;
    put: (path: string, payload: any, expectReturnContent?: boolean) => any;
    patch: (path: string, payload: any) => any;
    delete: (path: string, payload: any, expectReturnContent?: boolean) => any;
    head: (path: string, payload: any) => any;
    get: (path: string, expectReturnContent?: boolean) => any;
    query: (query: any, variables?: Variables) => Promise<{
        data: any;
    }>;
    login: () => Promise<string>;
}

interface ICommandBase {
    /**
     * The client's connection
     */
    client: Connection;
    /**
     * An array of validation errors
     */
    errors: string[];
    /**
     * Execute the command
     */
    do: () => Promise<any>;
    /**
     * Optional method to build the payload of an actual call
     */
    payload?: () => any;
    /**
     * validate that all the required parameters were feed to the builder
     */
    validate: () => void;
}
declare abstract class CommandBase implements ICommandBase {
    private _errors;
    readonly client: Connection;
    protected constructor(client: Connection);
    get errors(): string[];
    addError(error: string): void;
    addErrors(errors: string[]): void;
    abstract do(): Promise<any>;
    abstract validate(): void;
}

interface definitions {
    Link: {
        /** @description target of the link */
        href?: string;
        /** @description relationship if both resources are related, e.g. 'next', 'previous', 'parent', etc. */
        rel?: string;
        /** @description human readable name of the resource group */
        name?: string;
        /** @description weaviate documentation about this resource group */
        documentationHref?: string;
    };
    Principal: {
        /** @description The username that was extracted either from the authentication information */
        username?: string;
        groups?: string[];
    };
    /** @description An array of available words and contexts. */
    C11yWordsResponse: {
        /** @description Weighted results for all words */
        concatenatedWord?: {
            concatenatedWord?: string;
            singleWords?: unknown[];
            concatenatedVector?: definitions['C11yVector'];
            concatenatedNearestNeighbors?: definitions['C11yNearestNeighbors'];
        };
        /** @description Weighted results for per individual word */
        individualWords?: {
            word?: string;
            present?: boolean;
            info?: {
                vector?: definitions['C11yVector'];
                nearestNeighbors?: definitions['C11yNearestNeighbors'];
            };
        }[];
    };
    /** @description A resource describing an extension to the contextinoary, containing both the identifier and the definition of the extension */
    C11yExtension: {
        /**
         * @description The new concept you want to extend. Must be an all-lowercase single word, or a space delimited compound word. Examples: 'foobarium', 'my custom concept'
         * @example foobarium
         */
        concept?: string;
        /** @description A list of space-delimited words or a sentence describing what the custom concept is about. Avoid using the custom concept itself. An Example definition for the custom concept 'foobarium': would be 'a naturally occurring element which can only be seen by programmers' */
        definition?: string;
        /**
         * Format: float
         * @description Weight of the definition of the new concept where 1='override existing definition entirely' and 0='ignore custom definition'. Note that if the custom concept is not present in the contextionary yet, the weight cannot be less than 1.
         */
        weight?: number;
    };
    /** @description C11y function to show the nearest neighbors to a word. */
    C11yNearestNeighbors: {
        word?: string;
        /** Format: float */
        distance?: number;
    }[];
    /** @description A Vector in the Contextionary */
    C11yVector: number[];
    /** @description Receive question based on array of classes, properties and values. */
    C11yVectorBasedQuestion: {
        /** @description Vectorized classname. */
        classVectors?: number[];
        /** @description Vectorized properties. */
        classProps?: {
            propsVectors?: number[];
            /** @description String with valuename. */
            value?: string;
        }[];
    }[];
    Deprecation: {
        /** @description The id that uniquely identifies this particular deprecations (mostly used internally) */
        id?: string;
        /** @description Whether the problematic API functionality is deprecated (planned to be removed) or already removed */
        status?: string;
        /** @description Describes which API is effected, usually one of: REST, GraphQL */
        apiType?: string;
        /** @description What this deprecation is about */
        msg?: string;
        /** @description User-required object to not be affected by the (planned) removal */
        mitigation?: string;
        /** @description The deprecation was introduced in this version */
        sinceVersion?: string;
        /** @description A best-effort guess of which upcoming version will remove the feature entirely */
        plannedRemovalVersion?: string;
        /** @description If the feature has already been removed, it was removed in this version */
        removedIn?: string;
        /**
         * Format: date-time
         * @description If the feature has already been removed, it was removed at this timestamp
         */
        removedTime?: string;
        /**
         * Format: date-time
         * @description The deprecation was introduced in this version
         */
        sinceTime?: string;
        /** @description The locations within the specified API affected by this deprecation */
        locations?: string[];
    };
    /** @description An error response given by Weaviate end-points. */
    ErrorResponse: {
        error?: {
            message?: string;
        }[];
    };
    /** @description An error response caused by a GraphQL query. */
    GraphQLError: {
        locations?: {
            /** Format: int64 */
            column?: number;
            /** Format: int64 */
            line?: number;
        }[];
        message?: string;
        path?: string[];
    };
    /** @description GraphQL query based on: http://facebook.github.io/graphql/. */
    GraphQLQuery: {
        /** @description The name of the operation if multiple exist in the query. */
        operationName?: string;
        /** @description Query based on GraphQL syntax. */
        query?: string;
        /** @description Additional variables for the query. */
        variables?: {
            [key: string]: unknown;
        };
    };
    /** @description A list of GraphQL queries. */
    GraphQLQueries: definitions['GraphQLQuery'][];
    /** @description GraphQL based response: http://facebook.github.io/graphql/. */
    GraphQLResponse: {
        /** @description GraphQL data object. */
        data?: {
            [key: string]: definitions['JsonObject'];
        };
        /** @description Array with errors. */
        errors?: definitions['GraphQLError'][];
    };
    /** @description A list of GraphQL responses. */
    GraphQLResponses: definitions['GraphQLResponse'][];
    /** @description Configure the inverted index built into Weaviate */
    InvertedIndexConfig: {
        /**
         * Format: int
         * @description Asynchronous index clean up happens every n seconds
         */
        cleanupIntervalSeconds?: number;
        bm25?: definitions['BM25Config'];
        stopwords?: definitions['StopwordConfig'];
        /** @description Index each object by its internal timestamps */
        indexTimestamps?: boolean;
        /** @description Index each object with the null state */
        indexNullState?: boolean;
        /** @description Index length of properties */
        indexPropertyLength?: boolean;
    };
    /** @description Configure how replication is executed in a cluster */
    ReplicationConfig: {
        /** @description Number of times a class is replicated */
        factor?: number;
    };
    /** @description tuning parameters for the BM25 algorithm */
    BM25Config: {
        /**
         * Format: float
         * @description calibrates term-weight scaling based on the term frequency within a document
         */
        k1?: number;
        /**
         * Format: float
         * @description calibrates term-weight scaling based on the document length
         */
        b?: number;
    };
    /** @description fine-grained control over stopword list usage */
    StopwordConfig: {
        /** @description pre-existing list of common words by language */
        preset?: string;
        /** @description stopwords to be considered additionally */
        additions?: string[];
        /** @description stopwords to be removed from consideration */
        removals?: string[];
    };
    /** @description Configuration related to multi-tenancy within a class */
    MultiTenancyConfig: {
        /** @description Whether or not multi-tenancy is enabled for this class */
        enabled?: boolean;
    };
    /** @description JSON object value. */
    JsonObject: {
        [key: string]: unknown;
    };
    /** @description Contains meta information of the current Weaviate instance. */
    Meta: {
        /**
         * Format: url
         * @description The url of the host.
         */
        hostname?: string;
        /** @description Version of weaviate you are currently running */
        version?: string;
        /** @description Module-specific meta information */
        modules?: {
            [key: string]: unknown;
        };
    };
    /** @description Multiple instances of references to other objects. */
    MultipleRef: definitions['SingleRef'][];
    /** @description Either a JSONPatch document as defined by RFC 6902 (from, op, path, value), or a merge document (RFC 7396). */
    PatchDocumentObject: {
        /** @description A string containing a JSON Pointer value. */
        from?: string;
        /**
         * @description The operation to be performed.
         * @enum {string}
         */
        op: 'add' | 'remove' | 'replace' | 'move' | 'copy' | 'test';
        /** @description A JSON-Pointer. */
        path: string;
        /** @description The value to be used within the operations. */
        value?: {
            [key: string]: unknown;
        };
        merge?: definitions['Object'];
    };
    /** @description Either a JSONPatch document as defined by RFC 6902 (from, op, path, value), or a merge document (RFC 7396). */
    PatchDocumentAction: {
        /** @description A string containing a JSON Pointer value. */
        from?: string;
        /**
         * @description The operation to be performed.
         * @enum {string}
         */
        op: 'add' | 'remove' | 'replace' | 'move' | 'copy' | 'test';
        /** @description A JSON-Pointer. */
        path: string;
        /** @description The value to be used within the operations. */
        value?: {
            [key: string]: unknown;
        };
        merge?: definitions['Object'];
    };
    /** @description A single peer in the network. */
    PeerUpdate: {
        /**
         * Format: uuid
         * @description The session ID of the peer.
         */
        id?: string;
        /** @description Human readable name. */
        name?: string;
        /**
         * Format: uri
         * @description The location where the peer is exposed to the internet.
         */
        uri?: string;
        /** @description The latest known hash of the peer's schema. */
        schemaHash?: string;
    };
    /** @description List of known peers. */
    PeerUpdateList: definitions['PeerUpdate'][];
    /** @description Allow custom overrides of vector weights as math expressions. E.g. "pancake": "7" will set the weight for the word pancake to 7 in the vectorization, whereas "w * 3" would triple the originally calculated word. This is an open object, with OpenAPI Specification 3.0 this will be more detailed. See Weaviate docs for more info. In the future this will become a key/value (string/string) object. */
    VectorWeights: {
        [key: string]: unknown;
    };
    /** @description This is an open object, with OpenAPI Specification 3.0 this will be more detailed. See Weaviate docs for more info. In the future this will become a key/value OR a SingleRef definition. */
    PropertySchema: {
        [key: string]: unknown;
    };
    /** @description This is an open object, with OpenAPI Specification 3.0 this will be more detailed. See Weaviate docs for more info. In the future this will become a key/value OR a SingleRef definition. */
    SchemaHistory: {
        [key: string]: unknown;
    };
    /** @description Definitions of semantic schemas (also see: https://github.com/weaviate/weaviate-semantic-schemas). */
    Schema: {
        /** @description Semantic classes that are available. */
        classes?: definitions['Class'][];
        /**
         * Format: email
         * @description Email of the maintainer.
         */
        maintainer?: string;
        /** @description Name of the schema. */
        name?: string;
    };
    /** @description Indicates the health of the schema in a cluster. */
    SchemaClusterStatus: {
        /** @description True if the cluster is in sync, false if there is an issue (see error). */
        healthy?: boolean;
        /** @description Contains the sync check error if one occurred */
        error?: string;
        /** @description Hostname of the coordinating node, i.e. the one that received the cluster. This can be useful information if the error message contains phrases such as 'other nodes agree, but local does not', etc. */
        hostname?: string;
        /**
         * Format: int
         * @description Number of nodes that participated in the sync check
         */
        nodeCount?: number;
        /** @description The cluster check at startup can be ignored (to recover from an out-of-sync situation). */
        ignoreSchemaSync?: boolean;
    };
    Class: {
        /** @description Name of the class as URI relative to the schema URL. */
        class?: string;
        /** @description Name of the vector index to use, eg. (HNSW) */
        vectorIndexType?: string;
        /** @description Vector-index config, that is specific to the type of index selected in vectorIndexType */
        vectorIndexConfig?: {
            [key: string]: unknown;
        };
        /** @description Manage how the index should be sharded and distributed in the cluster */
        shardingConfig?: {
            [key: string]: unknown;
        };
        replicationConfig?: definitions['ReplicationConfig'];
        invertedIndexConfig?: definitions['InvertedIndexConfig'];
        multiTenancyConfig?: definitions['MultiTenancyConfig'];
        /** @description Specify how the vectors for this class should be determined. The options are either 'none' - this means you have to import a vector with each object yourself - or the name of a module that provides vectorization capabilities, such as 'text2vec-contextionary'. If left empty, it will use the globally configured default which can itself either be 'none' or a specific module. */
        vectorizer?: string;
        /** @description Configuration specific to modules this Weaviate instance has installed */
        moduleConfig?: {
            [key: string]: unknown;
        };
        /** @description Description of the class. */
        description?: string;
        /** @description The properties of the class. */
        properties?: definitions['Property'][];
    };
    Property: {
        /** @description Can be a reference to another type when it starts with a capital (for example Person), otherwise "string" or "int". */
        dataType?: string[];
        /** @description Description of the property. */
        description?: string;
        /** @description Configuration specific to modules this Weaviate instance has installed */
        moduleConfig?: {
            [key: string]: unknown;
        };
        /** @description Name of the property as URI relative to the schema URL. */
        name?: string;
        /** @description Optional. Should this property be indexed in the inverted index. Defaults to true. If you choose false, you will not be able to use this property in where filters, bm25 or hybrid search. This property has no affect on vectorization decisions done by modules (deprecated as of v1.19; use indexFilterable or/and indexSearchable instead) */
        indexInverted?: boolean;
        /** @description Optional. Should this property be indexed in the inverted index. Defaults to true. If you choose false, you will not be able to use this property in where filters. This property has no affect on vectorization decisions done by modules */
        indexFilterable?: boolean;
        /** @description Optional. Should this property be indexed in the inverted index. Defaults to true. Applicable only to properties of data type text and text[]. If you choose false, you will not be able to use this property in bm25 or hybrid search. This property has no affect on vectorization decisions done by modules */
        indexSearchable?: boolean;
        /**
         * @description Determines tokenization of the property as separate words or whole field. Optional. Applies to text and text[] data types. Allowed values are `word` (default; splits on any non-alphanumerical, lowercases), `lowercase` (splits on white spaces, lowercases), `whitespace` (splits on white spaces), `field` (trims). Not supported for remaining data types
         * @enum {string}
         */
        tokenization?: 'word' | 'lowercase' | 'whitespace' | 'field';
        /** @description The properties of the nested object(s). Applies to object and object[] data types. */
        nestedProperties?: definitions['NestedProperty'][];
    };
    NestedProperty: {
        dataType?: string[];
        description?: string;
        name?: string;
        indexFilterable?: boolean;
        indexSearchable?: boolean;
        /** @enum {string} */
        tokenization?: 'word' | 'lowercase' | 'whitespace' | 'field';
        nestedProperties?: definitions['NestedProperty'][];
    };
    /** @description The status of all the shards of a Class */
    ShardStatusList: definitions['ShardStatusGetResponse'][];
    /** @description Response body of shard status get request */
    ShardStatusGetResponse: {
        /** @description Name of the shard */
        name?: string;
        /** @description Status of the shard */
        status?: string;
    };
    /** @description The status of a single shard */
    ShardStatus: {
        /** @description Status of the shard */
        status?: string;
    };
    /** @description The definition of a backup create metadata */
    BackupCreateStatusResponse: {
        /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
        id?: string;
        /** @description Backup backend name e.g. filesystem, gcs, s3. */
        backend?: string;
        /** @description destination path of backup files proper to selected backend */
        path?: string;
        /** @description error message if creation failed */
        error?: string;
        /**
         * @description phase of backup creation process
         * @default STARTED
         * @enum {string}
         */
        status?: 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED';
    };
    /** @description The definition of a backup restore metadata */
    BackupRestoreStatusResponse: {
        /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
        id?: string;
        /** @description Backup backend name e.g. filesystem, gcs, s3. */
        backend?: string;
        /** @description destination path of backup files proper to selected backup backend */
        path?: string;
        /** @description error message if restoration failed */
        error?: string;
        /**
         * @description phase of backup restoration process
         * @default STARTED
         * @enum {string}
         */
        status?: 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED';
    };
    /** @description Request body for creating a backup of a set of classes */
    BackupCreateRequest: {
        /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
        id?: string;
        /** @description Custom configuration for the backup creation process */
        config?: {
            [key: string]: unknown;
        };
        /** @description List of classes to include in the backup creation process */
        include?: string[];
        /** @description List of classes to exclude from the backup creation process */
        exclude?: string[];
    };
    /** @description The definition of a backup create response body */
    BackupCreateResponse: {
        /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
        id?: string;
        /** @description The list of classes for which the backup creation process was started */
        classes?: string[];
        /** @description Backup backend name e.g. filesystem, gcs, s3. */
        backend?: string;
        /** @description destination path of backup files proper to selected backend */
        path?: string;
        /** @description error message if creation failed */
        error?: string;
        /**
         * @description phase of backup creation process
         * @default STARTED
         * @enum {string}
         */
        status?: 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED';
    };
    /** @description Request body for restoring a backup for a set of classes */
    BackupRestoreRequest: {
        /** @description Custom configuration for the backup restoration process */
        config?: {
            [key: string]: unknown;
        };
        /** @description List of classes to include in the backup restoration process */
        include?: string[];
        /** @description List of classes to exclude from the backup restoration process */
        exclude?: string[];
    };
    /** @description The definition of a backup restore response body */
    BackupRestoreResponse: {
        /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
        id?: string;
        /** @description The list of classes for which the backup restoration process was started */
        classes?: string[];
        /** @description Backup backend name e.g. filesystem, gcs, s3. */
        backend?: string;
        /** @description destination path of backup files proper to selected backend */
        path?: string;
        /** @description error message if restoration failed */
        error?: string;
        /**
         * @description phase of backup restoration process
         * @default STARTED
         * @enum {string}
         */
        status?: 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED';
    };
    /** @description The summary of Weaviate's statistics. */
    NodeStats: {
        /**
         * Format: int
         * @description The count of Weaviate's shards.
         */
        shardCount?: number;
        /**
         * Format: int64
         * @description The total number of objects in DB.
         */
        objectCount?: number;
    };
    /** @description The summary of a nodes batch queue congestion status. */
    BatchStats: {
        /**
         * Format: int
         * @description How many objects are currently in the batch queue.
         */
        queueLength?: number;
        /**
         * Format: int
         * @description How many objects are approximately processed from the batch queue per second.
         */
        ratePerSecond?: number;
    };
    /** @description The definition of a node shard status response body */
    NodeShardStatus: {
        /** @description The name of the shard. */
        name?: string;
        /** @description The name of shard's class. */
        class?: string;
        /**
         * Format: int64
         * @description The number of objects in shard.
         */
        objectCount?: number;
    };
    /** @description The definition of a backup node status response body */
    NodeStatus: {
        /** @description The name of the node. */
        name?: string;
        /**
         * @description Node's status.
         * @default HEALTHY
         * @enum {string}
         */
        status?: 'HEALTHY' | 'UNHEALTHY' | 'UNAVAILABLE';
        /** @description The version of Weaviate. */
        version?: string;
        /** @description The gitHash of Weaviate. */
        gitHash?: string;
        /** @description Weaviate overall statistics. */
        stats?: definitions['NodeStats'];
        /** @description Weaviate batch statistics. */
        batchStats?: definitions['BatchStats'];
        /** @description The list of the shards with it's statistics. */
        shards?: definitions['NodeShardStatus'][];
    };
    /** @description The status of all of the Weaviate nodes */
    NodesStatusResponse: {
        nodes?: definitions['NodeStatus'][];
    };
    /** @description Either set beacon (direct reference) or set class and schema (concept reference) */
    SingleRef: {
        /**
         * Format: uri
         * @description If using a concept reference (rather than a direct reference), specify the desired class name here
         */
        class?: string;
        /** @description If using a concept reference (rather than a direct reference), specify the desired properties here */
        schema?: definitions['PropertySchema'];
        /**
         * Format: uri
         * @description If using a direct reference, specify the URI to point to the cross-ref here. Should be in the form of weaviate://localhost/<uuid> for the example of a local cross-ref to an object
         */
        beacon?: string;
        /**
         * Format: uri
         * @description If using a direct reference, this read-only fields provides a link to the referenced resource. If 'origin' is globally configured, an absolute URI is shown - a relative URI otherwise.
         */
        href?: string;
        /** @description Additional Meta information about classifications if the item was part of one */
        classification?: definitions['ReferenceMetaClassification'];
    };
    /** @description Additional Meta information about a single object object. */
    AdditionalProperties: {
        [key: string]: {
            [key: string]: unknown;
        };
    };
    /** @description This meta field contains additional info about the classified reference property */
    ReferenceMetaClassification: {
        /**
         * Format: int64
         * @description overall neighbors checked as part of the classification. In most cases this will equal k, but could be lower than k - for example if not enough data was present
         */
        overallCount?: number;
        /**
         * Format: int64
         * @description size of the winning group, a number between 1..k
         */
        winningCount?: number;
        /**
         * Format: int64
         * @description size of the losing group, can be 0 if the winning group size equals k
         */
        losingCount?: number;
        /**
         * Format: float32
         * @description The lowest distance of any neighbor, regardless of whether they were in the winning or losing group
         */
        closestOverallDistance?: number;
        /**
         * Format: float32
         * @description deprecated - do not use, to be removed in 0.23.0
         */
        winningDistance?: number;
        /**
         * Format: float32
         * @description Mean distance of all neighbors from the winning group
         */
        meanWinningDistance?: number;
        /**
         * Format: float32
         * @description Closest distance of a neighbor from the winning group
         */
        closestWinningDistance?: number;
        /**
         * Format: float32
         * @description The lowest distance of a neighbor in the losing group. Optional. If k equals the size of the winning group, there is no losing group
         */
        closestLosingDistance?: number;
        /**
         * Format: float32
         * @description deprecated - do not use, to be removed in 0.23.0
         */
        losingDistance?: number;
        /**
         * Format: float32
         * @description Mean distance of all neighbors from the losing group. Optional. If k equals the size of the winning group, there is no losing group.
         */
        meanLosingDistance?: number;
    };
    BatchReference: {
        /**
         * Format: uri
         * @description Long-form beacon-style URI to identify the source of the cross-ref including the property name. Should be in the form of weaviate://localhost/<kinds>/<uuid>/<className>/<propertyName>, where <kinds> must be one of 'objects', 'objects' and <className> and <propertyName> must represent the cross-ref property of source class to be used.
         * @example weaviate://localhost/Zoo/a5d09582-4239-4702-81c9-92a6e0122bb4/hasAnimals
         */
        from?: string;
        /**
         * Format: uri
         * @description Short-form URI to point to the cross-ref. Should be in the form of weaviate://localhost/<uuid> for the example of a local cross-ref to an object
         * @example weaviate://localhost/97525810-a9a5-4eb0-858a-71449aeb007f
         */
        to?: string;
        /** @description Name of the reference tenant. */
        tenant?: string;
    };
    BatchReferenceResponse: definitions['BatchReference'] & {
        /**
         * Format: object
         * @description Results for this specific reference.
         */
        result?: {
            /**
             * @default SUCCESS
             * @enum {string}
             */
            status?: 'SUCCESS' | 'PENDING' | 'FAILED';
            errors?: definitions['ErrorResponse'];
        };
    };
    GeoCoordinates: {
        /**
         * Format: float
         * @description The latitude of the point on earth in decimal form
         */
        latitude?: number;
        /**
         * Format: float
         * @description The longitude of the point on earth in decimal form
         */
        longitude?: number;
    };
    PhoneNumber: {
        /** @description The raw input as the phone number is present in your raw data set. It will be parsed into the standardized formats if valid. */
        input?: string;
        /** @description Read-only. Parsed result in the international format (e.g. +49 123 ...) */
        internationalFormatted?: string;
        /** @description Optional. The ISO 3166-1 alpha-2 country code. This is used to figure out the correct countryCode and international format if only a national number (e.g. 0123 4567) is provided */
        defaultCountry?: string;
        /**
         * Format: uint64
         * @description Read-only. The numerical country code (e.g. 49)
         */
        countryCode?: number;
        /**
         * Format: uint64
         * @description Read-only. The numerical representation of the national part
         */
        national?: number;
        /** @description Read-only. Parsed result in the national format (e.g. 0123 456789) */
        nationalFormatted?: string;
        /** @description Read-only. Indicates whether the parsed number is a valid phone number */
        valid?: boolean;
    };
    Object: {
        /** @description Class of the Object, defined in the schema. */
        class?: string;
        vectorWeights?: definitions['VectorWeights'];
        properties?: definitions['PropertySchema'];
        /**
         * Format: uuid
         * @description ID of the Object.
         */
        id?: string;
        /**
         * Format: int64
         * @description Timestamp of creation of this Object in milliseconds since epoch UTC.
         */
        creationTimeUnix?: number;
        /**
         * Format: int64
         * @description Timestamp of the last Object update in milliseconds since epoch UTC.
         */
        lastUpdateTimeUnix?: number;
        /** @description This object's position in the Contextionary vector space. Read-only if using a vectorizer other than 'none'. Writable and required if using 'none' as vectorizer. */
        vector?: definitions['C11yVector'];
        /** @description Name of the Objects tenant. */
        tenant?: string;
        additional?: definitions['AdditionalProperties'];
    };
    ObjectsGetResponse: definitions['Object'] & {
        deprecations?: definitions['Deprecation'][];
    } & {
        /**
         * Format: object
         * @description Results for this specific Object.
         */
        result?: {
            /**
             * @default SUCCESS
             * @enum {string}
             */
            status?: 'SUCCESS' | 'PENDING' | 'FAILED';
            errors?: definitions['ErrorResponse'];
        };
    };
    BatchDelete: {
        /** @description Outlines how to find the objects to be deleted. */
        match?: {
            /**
             * @description Class (name) which objects will be deleted.
             * @example City
             */
            class?: string;
            /** @description Filter to limit the objects to be deleted. */
            where?: definitions['WhereFilter'];
        };
        /**
         * @description Controls the verbosity of the output, possible values are: "minimal", "verbose". Defaults to "minimal".
         * @default minimal
         */
        output?: string;
        /**
         * @description If true, objects will not be deleted yet, but merely listed. Defaults to false.
         * @default false
         */
        dryRun?: boolean;
    };
    /** @description Delete Objects response. */
    BatchDeleteResponse: {
        /** @description Outlines how to find the objects to be deleted. */
        match?: {
            /**
             * @description Class (name) which objects will be deleted.
             * @example City
             */
            class?: string;
            /** @description Filter to limit the objects to be deleted. */
            where?: definitions['WhereFilter'];
        };
        /**
         * @description Controls the verbosity of the output, possible values are: "minimal", "verbose". Defaults to "minimal".
         * @default minimal
         */
        output?: string;
        /**
         * @description If true, objects will not be deleted yet, but merely listed. Defaults to false.
         * @default false
         */
        dryRun?: boolean;
        results?: {
            /**
             * Format: int64
             * @description How many objects were matched by the filter.
             */
            matches?: number;
            /**
             * Format: int64
             * @description The most amount of objects that can be deleted in a single query, equals QUERY_MAXIMUM_RESULTS.
             */
            limit?: number;
            /**
             * Format: int64
             * @description How many objects were successfully deleted in this round.
             */
            successful?: number;
            /**
             * Format: int64
             * @description How many objects should have been deleted but could not be deleted.
             */
            failed?: number;
            /** @description With output set to "minimal" only objects with error occurred will the be described. Successfully deleted objects would be omitted. Output set to "verbose" will list all of the objets with their respective statuses. */
            objects?: {
                /**
                 * Format: uuid
                 * @description ID of the Object.
                 */
                id?: string;
                /**
                 * @default SUCCESS
                 * @enum {string}
                 */
                status?: 'SUCCESS' | 'DRYRUN' | 'FAILED';
                errors?: definitions['ErrorResponse'];
            }[];
        };
    };
    /** @description List of Objects. */
    ObjectsListResponse: {
        /** @description The actual list of Objects. */
        objects?: definitions['Object'][];
        deprecations?: definitions['Deprecation'][];
        /**
         * Format: int64
         * @description The total number of Objects for the query. The number of items in a response may be smaller due to paging.
         */
        totalResults?: number;
    };
    /** @description Manage classifications, trigger them and view status of past classifications. */
    Classification: {
        /**
         * Format: uuid
         * @description ID to uniquely identify this classification run
         * @example ee722219-b8ec-4db1-8f8d-5150bb1a9e0c
         */
        id?: string;
        /**
         * @description class (name) which is used in this classification
         * @example City
         */
        class?: string;
        /**
         * @description which ref-property to set as part of the classification
         * @example [
         *   "inCountry"
         * ]
         */
        classifyProperties?: string[];
        /**
         * @description base the text-based classification on these fields (of type text)
         * @example [
         *   "description"
         * ]
         */
        basedOnProperties?: string[];
        /**
         * @description status of this classification
         * @example running
         * @enum {string}
         */
        status?: 'running' | 'completed' | 'failed';
        /** @description additional meta information about the classification */
        meta?: definitions['ClassificationMeta'];
        /** @description which algorithm to use for classifications */
        type?: string;
        /** @description classification-type specific settings */
        settings?: {
            [key: string]: unknown;
        };
        /**
         * @description error message if status == failed
         * @default
         * @example classify xzy: something went wrong
         */
        error?: string;
        filters?: {
            /** @description limit the objects to be classified */
            sourceWhere?: definitions['WhereFilter'];
            /** @description Limit the training objects to be considered during the classification. Can only be used on types with explicit training sets, such as 'knn' */
            trainingSetWhere?: definitions['WhereFilter'];
            /** @description Limit the possible sources when using an algorithm which doesn't really on training data, e.g. 'contextual'. When using an algorithm with a training set, such as 'knn', limit the training set instead */
            targetWhere?: definitions['WhereFilter'];
        };
    };
    /** @description Additional information to a specific classification */
    ClassificationMeta: {
        /**
         * Format: date-time
         * @description time when this classification was started
         * @example 2017-07-21T17:32:28Z
         */
        started?: string;
        /**
         * Format: date-time
         * @description time when this classification finished
         * @example 2017-07-21T17:32:28Z
         */
        completed?: string;
        /**
         * @description number of objects which were taken into consideration for classification
         * @example 147
         */
        count?: number;
        /**
         * @description number of objects successfully classified
         * @example 140
         */
        countSucceeded?: number;
        /**
         * @description number of objects which could not be classified - see error message for details
         * @example 7
         */
        countFailed?: number;
    };
    /** @description Filter search results using a where filter */
    WhereFilter: {
        /** @description combine multiple where filters, requires 'And' or 'Or' operator */
        operands?: definitions['WhereFilter'][];
        /**
         * @description operator to use
         * @example GreaterThanEqual
         * @enum {string}
         */
        operator?: 'And' | 'Or' | 'Equal' | 'Like' | 'NotEqual' | 'GreaterThan' | 'GreaterThanEqual' | 'LessThan' | 'LessThanEqual' | 'WithinGeoRange' | 'IsNull' | 'ContainsAny' | 'ContainsAll';
        /**
         * @description path to the property currently being filtered
         * @example [
         *   "inCity",
         *   "City",
         *   "name"
         * ]
         */
        path?: string[];
        /**
         * Format: int64
         * @description value as integer
         * @example 2000
         */
        valueInt?: number;
        /**
         * Format: float64
         * @description value as number/float
         * @example 3.14
         */
        valueNumber?: number;
        /**
         * @description value as boolean
         * @example false
         */
        valueBoolean?: boolean;
        /**
         * @description value as text (deprecated as of v1.19; alias for valueText)
         * @example my search term
         */
        valueString?: string;
        /**
         * @description value as text
         * @example my search term
         */
        valueText?: string;
        /**
         * @description value as date (as string)
         * @example TODO
         */
        valueDate?: string;
        /**
         * @description value as integer
         * @example [100, 200]
         */
        valueIntArray?: number[];
        /**
         * @description value as number/float
         * @example [
         *   3.14
         * ]
         */
        valueNumberArray?: number[];
        /**
         * @description value as boolean
         * @example [
         *   true,
         *   false
         * ]
         */
        valueBooleanArray?: boolean[];
        /**
         * @description value as text (deprecated as of v1.19; alias for valueText)
         * @example [
         *   "my search term"
         * ]
         */
        valueStringArray?: string[];
        /**
         * @description value as text
         * @example [
         *   "my search term"
         * ]
         */
        valueTextArray?: string[];
        /**
         * @description value as date (as string)
         * @example TODO
         */
        valueDateArray?: string[];
        /** @description value as geo coordinates and distance */
        valueGeoRange?: definitions['WhereFilterGeoRange'];
    };
    /** @description filter within a distance of a georange */
    WhereFilterGeoRange: {
        geoCoordinates?: definitions['GeoCoordinates'];
        distance?: {
            /** Format: float64 */
            max?: number;
        };
    };
    /** @description attributes representing a single tenant within weaviate */
    Tenant: {
        /** @description name of the tenant */
        name?: string;
        /**
         * @description activity status of the tenant's shard. Optional for creating tenant (implicit `HOT`) and required for updating tenant. Allowed values are `HOT` - tenant is fully active, `WARM` - tenant is active, some restrictions are imposed (TBD; not supported yet), `COLD` - tenant is inactive; no actions can be performed on tenant, tenant's files are stored locally, `FROZEN` - as COLD, but files are stored on cloud storage (not supported yet)
         * @enum {string}
         */
        activityStatus?: 'HOT' | 'WARM' | 'COLD' | 'FROZEN';
    };
}

type WeaviateObject = definitions['Object'];
type WeaviateObjectsList = definitions['ObjectsListResponse'];
type WeaviateObjectsGet = definitions['ObjectsGetResponse'];
type Reference = definitions['SingleRef'];
type WeaviateError = definitions['ErrorResponse'];
type Properties = definitions['PropertySchema'];
type Property = definitions['Property'];
type DataObject = definitions['Object'];
type BackupCreateRequest = definitions['BackupCreateRequest'];
type BackupCreateResponse = definitions['BackupCreateResponse'];
type BackupCreateStatusResponse = definitions['BackupCreateStatusResponse'];
type BackupRestoreRequest = definitions['BackupRestoreRequest'];
type BackupRestoreResponse = definitions['BackupRestoreResponse'];
type BackupRestoreStatusResponse = definitions['BackupRestoreStatusResponse'];
type BatchDelete = definitions['BatchDelete'];
type BatchDeleteResponse = definitions['BatchDeleteResponse'];
type BatchRequest = {
    fields?: ('ALL' | 'class' | 'schema' | 'id' | 'creationTimeUnix')[];
    objects?: WeaviateObject[];
};
type BatchReference = definitions['BatchReference'];
type BatchReferenceResponse = definitions['BatchReferenceResponse'];
type C11yWordsResponse = definitions['C11yWordsResponse'];
type C11yExtension = definitions['C11yExtension'];
type Classification = definitions['Classification'];
type WhereFilter = definitions['WhereFilter'];
type WeaviateSchema = definitions['Schema'];
type WeaviateClass = definitions['Class'];
type ShardStatus = definitions['ShardStatus'];
type ShardStatusList = definitions['ShardStatusList'];
type Tenant = definitions['Tenant'];
type SchemaClusterStatus = definitions['SchemaClusterStatus'];
type NodesStatusResponse = definitions['NodesStatusResponse'];

interface NearImageArgs$1 extends NearMediaBase {
    image: string;
}
declare class Aggregator extends CommandBase {
    private className?;
    private fields?;
    private groupBy?;
    private includesNearMediaFilter;
    private limit?;
    private nearMediaString?;
    private nearMediaType?;
    private nearObjectString?;
    private nearTextString?;
    private nearVectorString?;
    private objectLimit?;
    private whereString?;
    private tenant?;
    constructor(client: Connection);
    withFields: (fields: string) => this;
    withClassName: (className: string) => this;
    withWhere: (where: WhereFilter) => this;
    private withNearMedia;
    withNearImage: (args: NearImageArgs$1) => this;
    withNearAudio: (args: NearAudioArgs) => this;
    withNearVideo: (args: NearVideoArgs) => this;
    withNearDepth: (args: NearDepthArgs) => this;
    withNearIMU: (args: NearIMUArgs) => this;
    withNearText: (args: NearTextArgs) => this;
    withNearObject: (args: NearObjectArgs) => this;
    withNearVector: (args: NearVectorArgs) => this;
    withObjectLimit: (objectLimit: number) => this;
    withLimit: (limit: number) => this;
    withGroupBy: (groupBy: string[]) => this;
    withTenant: (tenant: string) => this;
    validateGroup: () => void;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    do: () => Promise<{
        data: any;
    }>;
}

interface Bm25Args {
    properties?: string[];
    query: string;
}

interface HybridArgs {
    alpha?: number;
    query: string;
    vector?: number[];
    properties?: string[];
    fusionType?: FusionType;
}
declare enum FusionType {
    rankedFusion = "rankedFusion",
    relativeScoreFusion = "relativeScoreFusion"
}

interface NearImageArgs extends NearMediaBase {
    image?: string;
}

interface AskArgs {
    autocorrect?: boolean;
    certainty?: number;
    distance?: number;
    properties?: string[];
    question?: string;
    rerank?: boolean;
}

interface GroupArgs {
    type: string;
    force: number;
}

interface SortArgs {
    path: string[];
    order?: string;
}

interface GenerateArgs {
    groupedTask?: string;
    groupedProperties?: string[];
    singlePrompt?: string;
}

declare class DbVersionSupport {
    private dbVersionProvider;
    constructor(dbVersionProvider: VersionProvider);
    supportsClassNameNamespacedEndpointsPromise(): Promise<{
        version: string | undefined;
        supports: boolean;
        warns: {
            deprecatedNonClassNameNamespacedEndpointsForObjects: () => void;
            deprecatedNonClassNameNamespacedEndpointsForReferences: () => void;
            deprecatedNonClassNameNamespacedEndpointsForBeacons: () => void;
            deprecatedWeaviateTooOld: () => void;
            notSupportedClassNamespacedEndpointsForObjects: () => void;
            notSupportedClassNamespacedEndpointsForReferences: () => void;
            notSupportedClassNamespacedEndpointsForBeacons: () => void;
            notSupportedClassParameterInEndpointsForObjects: () => void;
        };
    }>;
    supportsClassNameNamespacedEndpoints(version?: string): boolean;
}
interface VersionProvider {
    getVersionPromise(): Promise<string>;
}
declare class DbVersionProvider implements VersionProvider {
    private versionPromise?;
    private readonly emptyVersionPromise;
    private versionGetter;
    constructor(versionGetter: () => Promise<string>);
    getVersionPromise(): Promise<string>;
    refresh(force?: boolean): Promise<boolean>;
    assignPromise(version: string): Promise<string>;
}

type ConsistencyLevel = 'ALL' | 'ONE' | 'QUORUM';

declare class ObjectsPath {
    private dbVersionSupport;
    constructor(dbVersionSupport: DbVersionSupport);
    buildCreate(consistencyLevel?: string): Promise<string>;
    buildDelete(id: string, className: string, consistencyLevel?: string, tenant?: string): Promise<string>;
    buildCheck(id: string, className: string, consistencyLevel?: ConsistencyLevel, tenant?: string): Promise<string>;
    buildGetOne(id: string, className: string, additional: string[], consistencyLevel?: ConsistencyLevel, nodeName?: string, tenant?: string): Promise<string>;
    buildGet(className?: string, limit?: number, additional?: string[], after?: string, tenant?: string): Promise<string>;
    buildUpdate(id: string, className: string, consistencyLevel?: string): Promise<string>;
    buildMerge(id: string, className: string, consistencyLevel?: string): Promise<string>;
    build(params: any, modifiers: any): Promise<string>;
    addClassNameDeprecatedNotSupportedCheck(params: any, path: string, support: any): string;
    addClassNameDeprecatedCheck(params: any, path: string, support: any): string;
    addId(params: any, path: string): string;
    addQueryParams(params: any, path: string): string;
    addQueryParamsForGet(params: any, path: string, support: any): string;
}
declare class ReferencesPath {
    private dbVersionSupport;
    constructor(dbVersionSupport: DbVersionSupport);
    build(id: string, className: string, property: string, consistencyLevel?: ConsistencyLevel, tenant?: string): Promise<string>;
}

declare class Creator extends CommandBase {
    private className?;
    private consistencyLevel?;
    private id?;
    private objectsPath;
    private properties?;
    private vector?;
    private tenant?;
    constructor(client: Connection, objectsPath: ObjectsPath);
    withVector: (vector: number[]) => this;
    withClassName: (className: string) => this;
    withProperties: (properties: Properties) => this;
    withId: (id: string) => this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    withTenant: (tenant: string) => this;
    validateClassName: () => void;
    payload: () => WeaviateObject;
    validate: () => void;
    do: () => Promise<WeaviateObject>;
}

declare class Validator extends CommandBase {
    private className?;
    private id?;
    private properties?;
    constructor(client: Connection);
    withClassName: (className: string) => this;
    withProperties: (properties: Properties) => this;
    withId: (id: string) => this;
    validateClassName: () => void;
    payload: () => {
        properties: {
            [key: string]: unknown;
        } | undefined;
        class: string | undefined;
        id: string | undefined;
    };
    validate: () => void;
    do: () => any;
}

declare class Updater extends CommandBase {
    private className;
    private consistencyLevel?;
    private id;
    private objectsPath;
    private properties?;
    private tenant?;
    constructor(client: Connection, objectsPath: ObjectsPath);
    withProperties: (properties: Properties) => this;
    withId: (id: string) => this;
    withClassName: (className: string) => this;
    withTenant: (tenant: string) => this;
    validateClassName: () => void;
    validateId: () => void;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    payload: () => WeaviateObject;
    validate: () => void;
    do: () => Promise<any>;
}

declare class Merger extends CommandBase {
    private className;
    private consistencyLevel?;
    private id;
    private objectsPath;
    private properties?;
    private tenant?;
    constructor(client: Connection, objectsPath: ObjectsPath);
    withProperties: (properties: Properties) => this;
    withClassName: (className: string) => this;
    withId: (id: string) => this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    withTenant: (tenant: string) => this;
    validateClassName: () => void;
    validateId: () => void;
    payload: () => WeaviateObject;
    validate: () => void;
    do: () => Promise<any>;
}

declare class Getter extends CommandBase {
    private additional;
    private after;
    private className?;
    private limit?;
    private tenant?;
    private objectsPath;
    constructor(client: Connection, objectsPath: ObjectsPath);
    withClassName: (className: string) => this;
    withAfter: (id: string) => this;
    withLimit: (limit: number) => this;
    withTenant: (tenant: string) => this;
    extendAdditional: (prop: string) => this;
    withAdditional: (additionalFlag: any) => this;
    withVector: () => this;
    validate(): void;
    do: () => Promise<WeaviateObjectsList>;
}

declare class GetterById extends CommandBase {
    private additional;
    private className;
    private id;
    private consistencyLevel?;
    private nodeName?;
    private tenant?;
    private objectsPath;
    constructor(client: Connection, objectsPath: ObjectsPath);
    withId: (id: string) => this;
    withClassName: (className: string) => this;
    withTenant: (tenant: string) => this;
    extendAdditional: (prop: string) => this;
    withAdditional: (additionalFlag: string) => this;
    withVector: () => this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    withNodeName: (nodeName: string) => this;
    validateId: () => void;
    validate: () => void;
    buildPath: () => Promise<string>;
    do: () => Promise<WeaviateObject>;
}

declare class Deleter extends CommandBase {
    private className;
    private consistencyLevel?;
    private id;
    private tenant?;
    private objectsPath;
    constructor(client: Connection, objectsPath: ObjectsPath);
    withId: (id: string) => this;
    withClassName: (className: string) => this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    withTenant: (tenant: string) => this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validateId: () => void;
    validate: () => void;
    do: () => Promise<any>;
}

declare class Checker extends CommandBase {
    private className;
    private consistencyLevel?;
    private id;
    private tenant?;
    private objectsPath;
    constructor(client: Connection, objectsPath: ObjectsPath);
    withId: (id: string) => this;
    withClassName: (className: string) => this;
    withTenant: (tenant: string) => this;
    withConsistencyLevel: (consistencyLevel: ConsistencyLevel) => this;
    buildPath: () => Promise<string>;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validateId: () => void;
    validate: () => void;
    do: () => Promise<boolean>;
}

declare class BeaconPath {
    private dbVersionSupport;
    private beaconRegExp;
    constructor(dbVersionSupport: DbVersionSupport);
    rebuild(beacon: string): Promise<string>;
}

declare class ReferenceCreator extends CommandBase {
    private beaconPath;
    private className;
    private consistencyLevel?;
    private id;
    private reference;
    private referencesPath;
    private refProp;
    private tenant?;
    constructor(client: Connection, referencesPath: ReferencesPath, beaconPath: BeaconPath);
    withId: (id: string) => this;
    withClassName(className: string): this;
    withReference: (ref: Reference) => this;
    withReferenceProperty: (refProp: string) => this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    withTenant: (tenant: string) => this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    payload: () => {
        class?: string | undefined;
        schema?: {
            [key: string]: unknown;
        } | undefined;
        beacon?: string | undefined;
        href?: string | undefined;
        classification?: {
            overallCount?: number | undefined;
            winningCount?: number | undefined;
            losingCount?: number | undefined;
            closestOverallDistance?: number | undefined;
            winningDistance?: number | undefined;
            meanWinningDistance?: number | undefined;
            closestWinningDistance?: number | undefined;
            closestLosingDistance?: number | undefined;
            losingDistance?: number | undefined;
            meanLosingDistance?: number | undefined;
        } | undefined;
    };
    do: () => Promise<any>;
}

declare class ReferenceReplacer extends CommandBase {
    private beaconPath;
    private className;
    private consistencyLevel?;
    private id;
    private references;
    private referencesPath;
    private refProp;
    private tenant?;
    constructor(client: Connection, referencesPath: ReferencesPath, beaconPath: BeaconPath);
    withId: (id: string) => this;
    withClassName(className: string): this;
    withReferences: (refs: any) => this;
    withReferenceProperty: (refProp: string) => this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    withTenant: (tenant: string) => this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    payload: () => {
        class?: string | undefined;
        schema?: {
            [key: string]: unknown;
        } | undefined;
        beacon?: string | undefined;
        href?: string | undefined;
        classification?: {
            overallCount?: number | undefined;
            winningCount?: number | undefined;
            losingCount?: number | undefined;
            closestOverallDistance?: number | undefined;
            winningDistance?: number | undefined;
            meanWinningDistance?: number | undefined;
            closestWinningDistance?: number | undefined;
            closestLosingDistance?: number | undefined;
            losingDistance?: number | undefined;
            meanLosingDistance?: number | undefined;
        } | undefined;
    }[];
    do: () => Promise<any>;
    rebuildReferencePromise(reference: any): Promise<{
        beacon: any;
    }>;
}

declare class ReferenceDeleter extends CommandBase {
    private beaconPath;
    private className;
    private consistencyLevel?;
    private id;
    private reference;
    private referencesPath;
    private refProp;
    private tenant?;
    constructor(client: Connection, referencesPath: ReferencesPath, beaconPath: BeaconPath);
    withId: (id: string) => this;
    withClassName(className: string): this;
    withReference: (ref: Reference) => this;
    withReferenceProperty: (refProp: string) => this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    withTenant: (tenant: string) => this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    payload: () => {
        class?: string | undefined;
        schema?: {
            [key: string]: unknown;
        } | undefined;
        beacon?: string | undefined;
        href?: string | undefined;
        classification?: {
            overallCount?: number | undefined;
            winningCount?: number | undefined;
            losingCount?: number | undefined;
            closestOverallDistance?: number | undefined;
            winningDistance?: number | undefined;
            meanWinningDistance?: number | undefined;
            closestWinningDistance?: number | undefined;
            closestLosingDistance?: number | undefined;
            losingDistance?: number | undefined;
            meanLosingDistance?: number | undefined;
        } | undefined;
    };
    do: () => Promise<any>;
}

declare class ReferencePayloadBuilder extends CommandBase {
    private className?;
    private id?;
    constructor(client: Connection);
    withId: (id: string) => this;
    withClassName(className: string): this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    payload: () => Reference;
    do(): Promise<any>;
}

interface Data {
    creator: () => Creator;
    validator: () => Validator;
    updater: () => Updater;
    merger: () => Merger;
    getter: () => Getter;
    getterById: () => GetterById;
    deleter: () => Deleter;
    checker: () => Checker;
    referenceCreator: () => ReferenceCreator;
    referenceReplacer: () => ReferenceReplacer;
    referenceDeleter: () => ReferenceDeleter;
    referencePayloadBuilder: () => ReferencePayloadBuilder;
}

interface GroupByArgs {
    path: string[];
    groups: number;
    objectsPerGroup: number;
}

declare class GraphQLGetter extends CommandBase {
    private after?;
    private askString?;
    private bm25String?;
    private className?;
    private fields?;
    private groupString?;
    private hybridString?;
    private includesNearMediaFilter;
    private limit?;
    private nearImageNotSet?;
    private nearMediaString?;
    private nearMediaType?;
    private nearObjectString?;
    private nearTextString?;
    private nearVectorString?;
    private offset?;
    private sortString?;
    private whereString?;
    private generateString?;
    private consistencyLevel?;
    private groupByString?;
    private tenant?;
    private autocut?;
    constructor(client: Connection);
    withFields: (fields: string) => this;
    withClassName: (className: string) => this;
    withAfter: (id: string) => this;
    withGroup: (args: GroupArgs) => this;
    withWhere: (whereObj: WhereFilter) => this;
    withNearText: (args: NearTextArgs) => this;
    withBm25: (args: Bm25Args) => this;
    withHybrid: (args: HybridArgs) => this;
    withNearObject: (args: NearObjectArgs) => this;
    withAsk: (askObj: AskArgs) => this;
    private withNearMedia;
    withNearImage: (args: NearImageArgs) => this;
    withNearAudio: (args: NearAudioArgs) => this;
    withNearVideo: (args: NearVideoArgs) => this;
    withNearThermal: (args: NearThermalArgs) => this;
    withNearDepth: (args: NearDepthArgs) => this;
    withNearIMU: (args: NearIMUArgs) => this;
    withNearVector: (args: NearVectorArgs) => this;
    withLimit: (limit: number) => this;
    withOffset: (offset: number) => this;
    withAutocut: (autocut: number) => this;
    withSort: (args: SortArgs[]) => this;
    withGenerate: (args: GenerateArgs) => this;
    withConsistencyLevel: (level: ConsistencyLevel) => this;
    withGroupBy: (args: GroupByArgs) => this;
    withTenant: (tenant: string) => this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    do: () => Promise<{
        data: any;
    }>;
}

declare class Explorer extends CommandBase {
    private askString?;
    private fields?;
    private group?;
    private limit?;
    private includesNearMediaFilter;
    private nearMediaString?;
    private nearMediaType?;
    private nearObjectString?;
    private nearTextString?;
    private nearVectorString?;
    private params;
    constructor(client: Connection);
    withFields: (fields: string) => this;
    withLimit: (limit: number) => this;
    withNearText: (args: NearTextArgs) => this;
    withNearObject: (args: NearObjectArgs) => this;
    withAsk: (args: AskArgs) => this;
    private withNearMedia;
    withNearImage: (args: NearImageArgs) => this;
    withNearAudio: (args: NearAudioArgs) => this;
    withNearVideo: (args: NearVideoArgs) => this;
    withNearDepth: (args: NearDepthArgs) => this;
    withNearThermal: (args: NearThermalArgs) => this;
    withNearIMU: (args: NearIMUArgs) => this;
    withNearVector: (args: NearVectorArgs) => this;
    validateGroup: () => void;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    do: () => Promise<any>;
}

declare class RawGraphQL extends CommandBase {
    private query?;
    constructor(client: Connection);
    withQuery: (query: string) => this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    do: () => Promise<any>;
}

interface GraphQL {
    get: () => GraphQLGetter;
    aggregate: () => Aggregator;
    explore: () => Explorer;
    raw: () => RawGraphQL;
}

declare class ClassCreator extends CommandBase {
    private class;
    constructor(client: Connection);
    withClass: (classObj: object) => this;
    validateClass: () => void;
    validate(): void;
    do: () => Promise<WeaviateClass>;
}

declare class ClassDeleter extends CommandBase {
    private className?;
    constructor(client: Connection);
    withClassName: (className: string) => this;
    validateClassName: () => void;
    validate: () => void;
    do: () => Promise<void>;
}

declare class ClassGetter extends CommandBase {
    private className?;
    constructor(client: Connection);
    withClassName: (className: string) => this;
    validateClassName: () => void;
    validate: () => void;
    do: () => Promise<WeaviateClass>;
}

declare class PropertyCreator extends CommandBase {
    private className;
    private property;
    constructor(client: Connection);
    withClassName: (className: string) => this;
    withProperty: (property: Property) => this;
    validateClassName: () => void;
    validateProperty: () => void;
    validate: () => void;
    do: () => Promise<Property>;
}

declare class SchemaGetter extends CommandBase {
    constructor(client: Connection);
    validate(): void;
    do: () => Promise<WeaviateSchema>;
}

declare class ShardsGetter extends CommandBase {
    private className?;
    constructor(client: Connection);
    withClassName: (className: string) => this;
    validateClassName: () => void;
    validate: () => void;
    do: () => Promise<ShardStatusList>;
}

declare class ShardUpdater extends CommandBase {
    private className;
    private shardName;
    private status;
    constructor(client: Connection);
    withClassName: (className: string) => this;
    validateClassName: () => void;
    withShardName: (shardName: string) => this;
    validateShardName: () => void;
    withStatus: (status: string) => this;
    validateStatus: () => void;
    validate: () => void;
    do: () => any;
}

declare class ShardsUpdater extends CommandBase {
    private className;
    private shards;
    private status;
    constructor(client: Connection);
    withClassName: (className: string) => this;
    validateClassName: () => void;
    withStatus: (status: string) => this;
    validateStatus: () => void;
    validate: () => void;
    updateShards: () => Promise<any>;
    do: () => Promise<ShardStatusList>;
}

declare class TenantsCreator extends CommandBase {
    private className;
    private tenants;
    constructor(client: Connection, className: string, tenants: Array<Tenant>);
    validate: () => void;
    do: () => Promise<Array<Tenant>>;
}

declare class TenantsGetter extends CommandBase {
    private className;
    constructor(client: Connection, className: string);
    validate: () => void;
    do: () => Promise<Array<Tenant>>;
}

declare class TenantsUpdater extends CommandBase {
    private className;
    private tenants;
    constructor(client: Connection, className: string, tenants: Array<Tenant>);
    validate: () => void;
    do: () => Promise<Array<Tenant>>;
}

declare class TenantsDeleter extends CommandBase {
    private className;
    private tenants;
    constructor(client: Connection, className: string, tenants: Array<string>);
    validate: () => void;
    do: () => Promise<void>;
}

interface Schema {
    classCreator: () => ClassCreator;
    classDeleter: () => ClassDeleter;
    classGetter: () => ClassGetter;
    exists: (className: string) => Promise<boolean>;
    getter: () => SchemaGetter;
    propertyCreator: () => PropertyCreator;
    deleteAll: () => Promise<void>;
    shardsGetter: () => ShardsGetter;
    shardUpdater: () => ShardUpdater;
    shardsUpdater: () => ShardsUpdater;
    tenantsCreator: (className: string, tenants: Array<Tenant>) => TenantsCreator;
    tenantsGetter: (className: string) => TenantsGetter;
    tenantsUpdater: (className: string, tenants: Array<Tenant>) => TenantsUpdater;
    tenantsDeleter: (className: string, tenants: Array<string>) => TenantsDeleter;
}

declare class ClassificationsScheduler extends CommandBase {
    private basedOnProperties?;
    private classifyProperties?;
    private className?;
    private settings?;
    private type?;
    private waitForCompletion;
    private waitTimeout;
    constructor(client: Connection);
    withType: (type: string) => this;
    withSettings: (settings: any) => this;
    withClassName: (className: string) => this;
    withClassifyProperties: (props: string[]) => this;
    withBasedOnProperties: (props: string[]) => this;
    withWaitForCompletion: () => this;
    withWaitTimeout: (timeout: number) => this;
    validateIsSet: (prop: string | undefined | null | any[], name: string, setter: string) => void;
    validateClassName: () => void;
    validateBasedOnProperties: () => void;
    validateClassifyProperties: () => void;
    validate: () => void;
    payload: () => Classification;
    pollForCompletion: (id: any) => Promise<Classification>;
    do: () => Promise<Classification>;
}

declare class ClassificationsGetter extends CommandBase {
    private id?;
    constructor(client: Connection);
    withId: (id: string) => this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validateId: () => void;
    validate: () => void;
    do: () => Promise<Classification>;
}

interface Classifications {
    scheduler: () => ClassificationsScheduler;
    getter: () => ClassificationsGetter;
}

declare class ObjectsBatcher extends CommandBase {
    private consistencyLevel?;
    objects: WeaviateObject[];
    constructor(client: Connection);
    /**
     * can be called as:
     *  - withObjects(...[obj1, obj2, obj3])
     *  - withObjects(obj1, obj2, obj3)
     *  - withObjects(obj1)
     * @param  {...WeaviateObject[]} objects
     */
    withObjects(...objects: WeaviateObject[]): this;
    withObject(object: WeaviateObject): this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    payload: () => BatchRequest;
    validateObjectCount: () => void;
    validate: () => void;
    do: () => Promise<WeaviateObjectsGet[]>;
}

declare class ObjectsBatchDeleter extends CommandBase {
    private className?;
    private consistencyLevel?;
    private dryRun?;
    private output?;
    private whereFilter?;
    private tenant?;
    constructor(client: Connection);
    withClassName(className: string): this;
    withWhere(whereFilter: WhereFilter): this;
    withOutput(output: DeleteOutput): this;
    withDryRun(dryRun: boolean): this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    withTenant(tenant: string): this;
    payload: () => BatchDelete;
    validateClassName: () => void;
    validateWhereFilter: () => void;
    validate: () => void;
    do: () => Promise<BatchDeleteResponse>;
}

declare class ReferencesBatcher$1 extends CommandBase {
    private beaconPath;
    private consistencyLevel?;
    references: BatchReference[];
    constructor(client: Connection, beaconPath: BeaconPath);
    /**
     * can be called as:
     *  - withReferences(...[ref1, ref2, ref3])
     *  - withReferences(ref1, ref2, ref3)
     *  - withReferences(ref1)
     * @param  {...BatchReference[]} references
     */
    withReferences(...references: BatchReference[]): this;
    withReference(reference: BatchReference): this;
    withConsistencyLevel: (cl: ConsistencyLevel) => this;
    payload: () => BatchReference[];
    validateReferenceCount: () => void;
    validate: () => void;
    do: () => Promise<BatchReferenceResponse[]>;
    rebuildReferencePromise: (reference: BatchReference) => Promise<BatchReference>;
}

declare class ReferencesBatcher extends CommandBase {
    private fromClassName?;
    private fromId?;
    private fromRefProp?;
    private toClassName?;
    private toId?;
    constructor(client: Connection);
    withFromId: (id: string) => this;
    withToId: (id: string) => this;
    withFromClassName: (className: string) => this;
    withFromRefProp: (refProp: string) => this;
    withToClassName(className: string): this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    payload: () => BatchReference;
    do: () => Promise<any>;
}

type DeleteOutput = 'verbose' | 'minimal';
type DeleteResultStatus = 'SUCCESS' | 'FAILED' | 'DRYRUN';
interface Batch {
    objectsBatcher: () => ObjectsBatcher;
    objectsBatchDeleter: () => ObjectsBatchDeleter;
    referencesBatcher: () => ReferencesBatcher$1;
    referencePayloadBuilder: () => ReferencesBatcher;
}

declare class LiveChecker extends CommandBase {
    private dbVersionProvider;
    constructor(client: Connection, dbVersionProvider: DbVersionProvider);
    validate(): void;
    do: () => any;
}

declare class ReadyChecker extends CommandBase {
    private dbVersionProvider;
    constructor(client: Connection, dbVersionProvider: DbVersionProvider);
    validate(): void;
    do: () => any;
}

declare class MetaGetter extends CommandBase {
    constructor(client: Connection);
    validate(): void;
    do: () => any;
}

declare class OpenidConfigurationGetterGetter {
    private client;
    constructor(client: HttpClient);
    do: () => any;
}

interface Misc {
    liveChecker: () => LiveChecker;
    readyChecker: () => ReadyChecker;
    metaGetter: () => MetaGetter;
    openidConfigurationGetter: () => OpenidConfigurationGetterGetter;
}

declare class ExtensionCreator extends CommandBase {
    private concept?;
    private definition?;
    private weight?;
    constructor(client: Connection);
    withConcept: (concept: string) => this;
    withDefinition: (definition: string) => this;
    withWeight: (weight: number) => this;
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    validate: () => void;
    payload: () => C11yExtension;
    do: () => Promise<C11yExtension>;
}

declare class ConceptsGetter extends CommandBase {
    private concept?;
    constructor(client: Connection);
    validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
    withConcept: (concept: string) => this;
    validate: () => void;
    do: () => Promise<C11yWordsResponse>;
}

interface C11y {
    conceptsGetter: () => ConceptsGetter;
    extensionCreator: () => ExtensionCreator;
}

declare class BackupCreateStatusGetter extends CommandBase {
    private backend?;
    private backupId?;
    constructor(client: Connection);
    withBackend(backend: Backend): this;
    withBackupId(backupId: string): this;
    validate: () => void;
    do: () => Promise<BackupCreateStatusResponse>;
    private _path;
}

declare class BackupCreator extends CommandBase {
    private backend;
    private backupId;
    private excludeClassNames?;
    private includeClassNames?;
    private statusGetter;
    private waitForCompletion;
    constructor(client: Connection, statusGetter: BackupCreateStatusGetter);
    withIncludeClassNames(...classNames: string[]): this;
    withExcludeClassNames(...classNames: string[]): this;
    withBackend(backend: Backend): this;
    withBackupId(backupId: string): this;
    withWaitForCompletion(waitForCompletion: boolean): this;
    validate: () => void;
    do: () => Promise<BackupCreateResponse>;
    _create: (payload: BackupCreateRequest) => Promise<BackupCreateResponse>;
    _createAndWaitForCompletion: (payload: BackupCreateRequest) => Promise<BackupCreateResponse>;
    private _path;
    _merge: (createStatusResponse: BackupCreateStatusResponse, createResponse: BackupCreateResponse) => BackupCreateResponse;
}

declare class BackupRestoreStatusGetter extends CommandBase {
    private backend?;
    private backupId?;
    constructor(client: Connection);
    withBackend(backend: Backend): this;
    withBackupId(backupId: string): this;
    validate: () => void;
    do: () => Promise<BackupRestoreStatusResponse>;
    private _path;
}

declare class BackupRestorer extends CommandBase {
    private backend;
    private backupId;
    private excludeClassNames?;
    private includeClassNames?;
    private statusGetter;
    private waitForCompletion?;
    constructor(client: Connection, statusGetter: BackupRestoreStatusGetter);
    withIncludeClassNames(...classNames: string[]): this;
    withExcludeClassNames(...classNames: string[]): this;
    withBackend(backend: Backend): this;
    withBackupId(backupId: string): this;
    withWaitForCompletion(waitForCompletion: boolean): this;
    validate: () => void;
    do: () => Promise<BackupRestoreResponse>;
    _restore: (payload: BackupRestoreRequest) => Promise<BackupRestoreResponse>;
    _restoreAndWaitForCompletion: (payload: BackupRestoreRequest) => Promise<BackupRestoreResponse>;
    private _path;
    _merge: (restoreStatusResponse: BackupRestoreStatusResponse, restoreResponse: BackupRestoreResponse) => BackupRestoreResponse;
}

type Backend = 'filesystem' | 's3' | 'gcs' | 'azure';
type BackupStatus = 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED';
interface Backup {
    creator: () => BackupCreator;
    createStatusGetter: () => BackupCreateStatusGetter;
    restorer: () => BackupRestorer;
    restoreStatusGetter: () => BackupRestoreStatusGetter;
}

declare class NodesStatusGetter extends CommandBase {
    private className?;
    constructor(client: Connection);
    withClassName: (className: string) => this;
    validate(): void;
    do: () => Promise<NodesStatusResponse>;
}

type NodeStatus = 'HEALTHY' | 'UNHEALTHY' | 'UNAVAILABLE';
interface Cluster {
    nodesStatusGetter: () => NodesStatusGetter;
}

declare function generateUuid5(identifier: string | number, namespace?: string | number): string;

/**
 * This function converts a file blob into a base64 string so that it can be
 * sent to Weaviate and stored as a media field.
 *
 * This specific function is only applicable within the browser since it depends on
 * the FileReader API. It will throw an error if it is called in a Node environment.
 *
 * @param {Blob} blob The file blob to convert
 * @returns {string} The base64 string
 * @throws An error if the function is called outside of the browser
 *
 * @example
 * // Vanilla JS
 * const file = document.querySelector('input[type="file"]').files[0];
 * toBase64FromBlob(file).then((base64) => console.log(base64));
 *
 * // React
 * const [base64, setBase64] = useState('');
 * const onChange = (event) => toBase64FromBlob(event.target.files[0]).then(setBase64);
 *
 * // Submit
 * const onSubmit = (base64: string) => client.data
 *     .creator()
 *     .withClassName('MyClass')
 *     .withProperties({ myMediaField: base64 })
 *     .do();
 *
 */
declare function toBase64FromBlob(blob: Blob): Promise<string>;

interface ConnectionParams {
    authClientSecret?: AuthClientCredentials | AuthAccessTokenCredentials | AuthUserPasswordCredentials;
    apiKey?: ApiKey;
    host: string;
    scheme?: string;
    headers?: HeadersInit;
}
interface WeaviateClient {
    graphql: GraphQL;
    schema: Schema;
    data: Data;
    classifications: Classifications;
    batch: Batch;
    misc: Misc;
    c11y: C11y;
    backup: Backup;
    cluster: Cluster;
    oidcAuth?: OidcAuthenticator;
}
declare const app: {
    client: (params: ConnectionParams) => WeaviateClient;
    ApiKey: typeof ApiKey;
    AuthUserPasswordCredentials: typeof AuthUserPasswordCredentials;
    AuthAccessTokenCredentials: typeof AuthAccessTokenCredentials;
    AuthClientCredentials: typeof AuthClientCredentials;
};

export { AccessTokenCredentialsInput, Aggregator, ApiKey, AuthAccessTokenCredentials, AuthClientCredentials, AuthUserPasswordCredentials, Backend, Backup, BackupCreateRequest, BackupCreateResponse, BackupCreateStatusGetter, BackupCreateStatusResponse, BackupCreator, BackupRestoreRequest, BackupRestoreResponse, BackupRestoreStatusGetter, BackupRestoreStatusResponse, BackupRestorer, BackupStatus, Batch, BatchDelete, BatchDeleteResponse, BatchReference, BatchReferenceResponse, BatchRequest, C11y, C11yExtension, C11yWordsResponse, Checker, ClassCreator, ClassDeleter, ClassGetter, Classification, Classifications, ClassificationsGetter, ClassificationsScheduler, ClientCredentialsInput, Cluster, ConceptsGetter, ConnectionParams, ConsistencyLevel, Creator, Data, DataObject, DeleteOutput, DeleteResultStatus, Deleter, Explorer, ExtensionCreator, FusionType, Getter, GetterById, GraphQL, GraphQLGetter, LiveChecker, Merger, MetaGetter, Misc, NodeStatus, NodesStatusGetter, NodesStatusResponse, ObjectsBatchDeleter, ObjectsBatcher, OidcAuthFlow, OidcAuthenticator, OpenidConfigurationGetterGetter as OpenidConfigurationGetter, Properties, Property, PropertyCreator, RawGraphQL as Raw, ReadyChecker, Reference, ReferenceCreator, ReferenceDeleter, ReferencePayloadBuilder, ReferenceReplacer, ReferencesBatcher$1 as ReferencesBatcher, Schema, SchemaClusterStatus, SchemaGetter, ShardStatus, ShardStatusList, ShardUpdater, ShardsUpdater, Tenant, TenantsCreator, TenantsDeleter, TenantsGetter, TenantsUpdater, Updater, UserPasswordCredentialsInput, Validator, WeaviateClass, WeaviateClient, WeaviateError, WeaviateObject, WeaviateObjectsGet, WeaviateObjectsList, WeaviateSchema, WhereFilter, app as default, generateUuid5, toBase64FromBlob };
