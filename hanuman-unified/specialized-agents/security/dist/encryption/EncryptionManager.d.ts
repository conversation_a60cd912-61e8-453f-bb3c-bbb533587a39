import { Logger } from 'winston';
import { EventEmitter } from 'events';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Gestionnaire de Chiffrement
 *
 * Gère tous les aspects du chiffrement et de la cryptographie
 */
export declare class EncryptionManager extends EventEmitter {
    private logger;
    private memory;
    private encryptionKeys;
    private certificates;
    private keyRotationSchedule;
    private isInitialized;
    private defaultAlgorithm;
    private keyDerivationIterations;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le gestionnaire de chiffrement
     */
    initialize(): Promise<void>;
    /**
     * Chiffre des données
     */
    encrypt(data: string | Buffer, keyId?: string, algorithm?: string): Promise<EncryptionResult>;
    /**
     * Déchiffre des données
     */
    decrypt(encryptedData: string, keyId: string, algorithm: string, iv?: string): Promise<string>;
    /**
     * Génère une nouvelle clé de chiffrement
     */
    generateKey(keyId: string, algorithm?: string, keySize?: number): Promise<EncryptionKey>;
    /**
     * Effectue la rotation d'une clé
     */
    rotateKey(keyId: string): Promise<EncryptionKey>;
    /**
     * Génère un hash sécurisé
     */
    generateHash(data: string, algorithm?: string, salt?: string): Promise<HashResult>;
    /**
     * Vérifie un hash
     */
    verifyHash(data: string, expectedHash: string, salt: string, algorithm?: string): Promise<boolean>;
    /**
     * Génère une signature numérique
     */
    generateSignature(data: string, privateKeyId: string, algorithm?: string): Promise<SignatureResult>;
    /**
     * Vérifie une signature numérique
     */
    verifySignature(data: string, signature: string, publicKeyId: string, algorithm?: string): Promise<boolean>;
    /**
     * Génère une paire de clés RSA
     */
    generateKeyPair(keyId: string, keySize?: number): Promise<{
        publicKey: EncryptionKey;
        privateKey: EncryptionKey;
    }>;
    /**
     * Charge les clés existantes
     */
    private loadExistingKeys;
    /**
     * Assure la présence des clés par défaut
     */
    private ensureDefaultKeys;
    /**
     * Configure la rotation automatique des clés
     */
    private setupKeyRotation;
    /**
     * Planifie la rotation d'une clé
     */
    private scheduleKeyRotation;
    /**
     * Vérifie les certificats
     */
    private verifyCertificates;
    /**
     * Récupère la clé par défaut
     */
    private getDefaultKey;
    /**
     * Audit des opérations de chiffrement
     */
    private auditEncryption;
    /**
     * Récupère les statistiques de chiffrement
     */
    getEncryptionStats(): EncryptionStats;
    /**
     * Récupère les clés par algorithme
     */
    private getKeysByAlgorithm;
    /**
     * Récupère la clé la plus ancienne
     */
    private getOldestKey;
    /**
     * Récupère la clé la plus récente
     */
    private getNewestKey;
    /**
     * Arrêt du gestionnaire de chiffrement
     */
    shutdown(): Promise<void>;
}
interface EncryptionKey {
    id: string;
    algorithm: string;
    type?: 'symmetric' | 'public' | 'private';
    value: Buffer;
    salt?: Buffer;
    keySize: number;
    createdAt: Date;
    expiresAt: Date;
    rotatedAt?: Date;
    isActive: boolean;
    version: number;
    previousVersion?: number;
    metadata: any;
}
interface EncryptionResult {
    encryptedData: string;
    algorithm: string;
    keyId: string;
    iv: string;
    timestamp: Date;
    processingTime: number;
}
interface HashResult {
    hash: string;
    algorithm: string;
    salt: string;
    timestamp: Date;
}
interface SignatureResult {
    signature: string;
    algorithm: string;
    keyId: string;
    timestamp: Date;
}
interface EncryptionStats {
    totalKeys: number;
    activeKeys: number;
    expiredKeys: number;
    keysByAlgorithm: Record<string, number>;
    oldestKey: Date | null;
    newestKey: Date | null;
}
export {};
//# sourceMappingURL=EncryptionManager.d.ts.map