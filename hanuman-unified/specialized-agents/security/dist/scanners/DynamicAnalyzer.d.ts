import { Logger } from 'winston';
import { SecurityScanRequest, Vulnerability } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Analyseur Dynamique de Sécurité
 *
 * Effectue des analyses dynamiques (DAST) sur les applications en cours d'exécution
 */
export declare class DynamicAnalyzer {
    private logger;
    private memory;
    private isInitialized;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise l'analyseur dynamique
     */
    initialize(): Promise<void>;
    /**
     * Effectue une analyse dynamique
     */
    analyze(request: SecurityScanRequest): Promise<Vulnerability[]>;
    /**
     * Scanne les vulnérabilités web communes
     */
    private scanWebVulnerabilities;
    /**
     * Scanne les vulnérabilités d'API
     */
    private scanAPIVulnerabilities;
    /**
     * Scanne les vulnérabilités d'authentification
     */
    private scanAuthenticationVulnerabilities;
    /**
     * Stocke les résultats en mémoire
     */
    private storeResults;
    /**
     * Arrêt de l'analyseur dynamique
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=DynamicAnalyzer.d.ts.map