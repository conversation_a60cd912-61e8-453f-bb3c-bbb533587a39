import { Logger } from 'winston';
import { SecurityScanRequest, Vulnerability } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Scanner API
 *
 * Analyse les vulnérabilités des APIs REST et GraphQL
 */
export declare class APIScanner {
    private logger;
    private memory;
    private isInitialized;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le scanner API
     */
    initialize(): Promise<void>;
    /**
     * Effectue un scan d'API
     */
    scan(request: SecurityScanRequest): Promise<Vulnerability[]>;
    /**
     * Scanne les vulnérabilités OWASP API Top 10
     */
    private scanOWASPAPITop10;
    /**
     * Scanne l'authentification API
     */
    private scanAPIAuthentication;
    /**
     * Scanne les autorisations API
     */
    private scanAPIAuthorization;
    /**
     * Stocke les résultats en mémoire
     */
    private storeResults;
    /**
     * Arrêt du scanner API
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=APIScanner.d.ts.map