"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VulnerabilityScanner = void 0;
const StaticAnalyzer_1 = require("./StaticAnalyzer");
const DynamicAnalyzer_1 = require("./DynamicAnalyzer");
const ContainerScanner_1 = require("./ContainerScanner");
const InfrastructureScanner_1 = require("./InfrastructureScanner");
const NetworkScanner_1 = require("./NetworkScanner");
const WebScanner_1 = require("./WebScanner");
const APIScanner_1 = require("./APIScanner");
/**
 * Scanner de Vulnérabilités
 *
 * Orchestrateur principal pour tous les types de scans de sécurité
 */
class VulnerabilityScanner {
    constructor(scanners, logger, memory) {
        this.isInitialized = false;
        this.scanners = scanners;
        this.logger = logger;
        this.memory = memory;
    }
    /**
     * Initialise le scanner de vulnérabilités
     */
    async initialize() {
        try {
            this.logger.info('🔍 Initialisation du Scanner de Vulnérabilités...');
            // Initialisation des scanners spécialisés
            this.staticAnalyzer = new StaticAnalyzer_1.StaticAnalyzer(this.logger, this.memory);
            this.dynamicAnalyzer = new DynamicAnalyzer_1.DynamicAnalyzer(this.logger, this.memory);
            this.containerScanner = new ContainerScanner_1.ContainerScanner(this.logger, this.memory);
            this.infrastructureScanner = new InfrastructureScanner_1.InfrastructureScanner(this.logger, this.memory);
            this.networkScanner = new NetworkScanner_1.NetworkScanner(this.logger, this.memory);
            this.webScanner = new WebScanner_1.WebScanner(this.logger, this.memory);
            this.apiScanner = new APIScanner_1.APIScanner(this.logger, this.memory);
            // Initialisation des scanners
            await Promise.all([
                this.staticAnalyzer.initialize(),
                this.dynamicAnalyzer.initialize(),
                this.containerScanner.initialize(),
                this.infrastructureScanner.initialize(),
                this.networkScanner.initialize(),
                this.webScanner.initialize(),
                this.apiScanner.initialize()
            ]);
            this.isInitialized = true;
            this.logger.info('✅ Scanner de Vulnérabilités initialisé');
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'initialisation du scanner:', error);
            throw error;
        }
    }
    /**
     * Effectue un scan de sécurité
     */
    async scan(request) {
        const startTime = new Date();
        this.logger.info(`🔄 Démarrage du scan ${request.type}: ${request.id}`);
        try {
            let vulnerabilities = [];
            // Exécution selon le type de scan
            switch (request.type) {
                case 'sast':
                    vulnerabilities = await this.staticAnalyzer.analyze(request);
                    break;
                case 'dast':
                    vulnerabilities = await this.dynamicAnalyzer.analyze(request);
                    break;
                case 'iast':
                    // Combinaison SAST + DAST
                    const sastResults = await this.staticAnalyzer.analyze(request);
                    const dastResults = await this.dynamicAnalyzer.analyze(request);
                    vulnerabilities = [...sastResults, ...dastResults];
                    break;
                case 'sca':
                    vulnerabilities = await this.staticAnalyzer.analyzeDependencies(request);
                    break;
                default:
                    throw new Error(`Type de scan non supporté: ${request.type}`);
            }
            // Déduplication des vulnérabilités
            vulnerabilities = this.deduplicateVulnerabilities(vulnerabilities);
            // Calcul du résumé
            const summary = this.calculateSummary(vulnerabilities);
            // Génération des recommandations
            const recommendations = await this.generateRecommendations(vulnerabilities);
            const result = {
                id: `result-${request.id}`,
                scanId: request.id,
                timestamp: new Date(),
                status: 'completed',
                duration: Date.now() - startTime.getTime(),
                summary,
                vulnerabilities,
                compliance: [], // Sera rempli par le ComplianceChecker
                recommendations,
                metadata: {
                    scanType: request.type,
                    target: request.target,
                    configuration: request.configuration
                }
            };
            this.logger.info(`✅ Scan terminé: ${request.id} - ${vulnerabilities.length} vulnérabilités trouvées`);
            return result;
        }
        catch (error) {
            this.logger.error(`❌ Erreur lors du scan ${request.id}:`, error);
            throw error;
        }
    }
    /**
     * Scan de conteneur
     */
    async scanContainer(request) {
        this.logger.info(`🐳 Scan de conteneur: ${request.target.location}`);
        const vulnerabilities = await this.containerScanner.scan(request);
        const summary = this.calculateSummary(vulnerabilities);
        const recommendations = await this.generateRecommendations(vulnerabilities);
        return {
            id: `result-${request.id}`,
            scanId: request.id,
            timestamp: new Date(),
            status: 'completed',
            duration: 0,
            summary,
            vulnerabilities,
            compliance: [],
            recommendations,
            metadata: { scanType: 'container' }
        };
    }
    /**
     * Scan d'infrastructure
     */
    async scanInfrastructure(request) {
        this.logger.info(`🏗️ Scan d'infrastructure: ${request.target.location}`);
        const vulnerabilities = await this.infrastructureScanner.scan(request);
        const summary = this.calculateSummary(vulnerabilities);
        const recommendations = await this.generateRecommendations(vulnerabilities);
        return {
            id: `result-${request.id}`,
            scanId: request.id,
            timestamp: new Date(),
            status: 'completed',
            duration: 0,
            summary,
            vulnerabilities,
            compliance: [],
            recommendations,
            metadata: { scanType: 'infrastructure' }
        };
    }
    /**
     * Scan réseau
     */
    async scanNetwork(request) {
        this.logger.info(`🌐 Scan réseau: ${request.target.location}`);
        const vulnerabilities = await this.networkScanner.scan(request);
        const summary = this.calculateSummary(vulnerabilities);
        const recommendations = await this.generateRecommendations(vulnerabilities);
        return {
            id: `result-${request.id}`,
            scanId: request.id,
            timestamp: new Date(),
            status: 'completed',
            duration: 0,
            summary,
            vulnerabilities,
            compliance: [],
            recommendations,
            metadata: { scanType: 'network' }
        };
    }
    /**
     * Scan web
     */
    async scanWeb(request) {
        this.logger.info(`🌐 Scan web: ${request.target.location}`);
        const vulnerabilities = await this.webScanner.scan(request);
        const summary = this.calculateSummary(vulnerabilities);
        const recommendations = await this.generateRecommendations(vulnerabilities);
        return {
            id: `result-${request.id}`,
            scanId: request.id,
            timestamp: new Date(),
            status: 'completed',
            duration: 0,
            summary,
            vulnerabilities,
            compliance: [],
            recommendations,
            metadata: { scanType: 'web' }
        };
    }
    /**
     * Scan API
     */
    async scanAPI(request) {
        this.logger.info(`🔌 Scan API: ${request.target.location}`);
        const vulnerabilities = await this.apiScanner.scan(request);
        const summary = this.calculateSummary(vulnerabilities);
        const recommendations = await this.generateRecommendations(vulnerabilities);
        return {
            id: `result-${request.id}`,
            scanId: request.id,
            timestamp: new Date(),
            status: 'completed',
            duration: 0,
            summary,
            vulnerabilities,
            compliance: [],
            recommendations,
            metadata: { scanType: 'api' }
        };
    }
    /**
     * Déduplique les vulnérabilités
     */
    deduplicateVulnerabilities(vulnerabilities) {
        const seen = new Set();
        const deduplicated = [];
        for (const vuln of vulnerabilities) {
            // Clé de déduplication basée sur le titre, la catégorie et la localisation
            const key = `${vuln.title}-${vuln.category}-${vuln.location.file || vuln.location.url}-${vuln.location.line || ''}`;
            if (!seen.has(key)) {
                seen.add(key);
                deduplicated.push(vuln);
            }
            else {
                // Mise à jour de la vulnérabilité existante si nécessaire
                const existing = deduplicated.find(v => v.title === vuln.title &&
                    v.category === vuln.category &&
                    (v.location.file === vuln.location.file || v.location.url === vuln.location.url));
                if (existing && this.getSeverityWeight(vuln.severity) > this.getSeverityWeight(existing.severity)) {
                    // Remplacer par la vulnérabilité de plus haute sévérité
                    const index = deduplicated.indexOf(existing);
                    deduplicated[index] = vuln;
                }
            }
        }
        return deduplicated;
    }
    /**
     * Calcule le résumé des vulnérabilités
     */
    calculateSummary(vulnerabilities) {
        const summary = {
            totalVulnerabilities: vulnerabilities.length,
            criticalCount: 0,
            highCount: 0,
            mediumCount: 0,
            lowCount: 0,
            infoCount: 0,
            riskScore: 0,
            complianceScore: 100,
            securityGrade: 'A'
        };
        // Comptage par sévérité
        vulnerabilities.forEach(vuln => {
            switch (vuln.severity) {
                case 'critical':
                    summary.criticalCount++;
                    break;
                case 'high':
                    summary.highCount++;
                    break;
                case 'medium':
                    summary.mediumCount++;
                    break;
                case 'low':
                    summary.lowCount++;
                    break;
                case 'info':
                    summary.infoCount++;
                    break;
            }
        });
        // Calcul du score de risque (0-100)
        const weights = { critical: 10, high: 5, medium: 2, low: 1, info: 0.1 };
        const totalWeight = summary.criticalCount * weights.critical +
            summary.highCount * weights.high +
            summary.mediumCount * weights.medium +
            summary.lowCount * weights.low +
            summary.infoCount * weights.info;
        summary.riskScore = Math.min(100, totalWeight);
        // Calcul du grade de sécurité
        if (summary.criticalCount > 0) {
            summary.securityGrade = 'F';
        }
        else if (summary.highCount > 5) {
            summary.securityGrade = 'D';
        }
        else if (summary.highCount > 0 || summary.mediumCount > 10) {
            summary.securityGrade = 'C';
        }
        else if (summary.mediumCount > 0 || summary.lowCount > 20) {
            summary.securityGrade = 'B';
        }
        else {
            summary.securityGrade = 'A';
        }
        // Score de compliance (inversement proportionnel au risque)
        summary.complianceScore = Math.max(0, 100 - summary.riskScore);
        return summary;
    }
    /**
     * Génère des recommandations de sécurité
     */
    async generateRecommendations(vulnerabilities) {
        const recommendations = [];
        const categoryCount = {};
        // Comptage par catégorie
        vulnerabilities.forEach(vuln => {
            categoryCount[vuln.category] = (categoryCount[vuln.category] || 0) + 1;
        });
        // Recommandations basées sur les catégories les plus fréquentes
        for (const [category, count] of Object.entries(categoryCount)) {
            if (count >= 3) { // Seuil pour générer une recommandation
                recommendations.push({
                    id: `rec-${category}-${Date.now()}`,
                    title: this.getCategoryRecommendationTitle(category),
                    description: this.getCategoryRecommendationDescription(category),
                    priority: count >= 5 ? 'high' : 'medium',
                    effort: 'medium',
                    impact: 'high',
                    category: 'security-improvement',
                    implementation: this.getCategoryImplementationSteps(category),
                    timeline: count >= 5 ? 'immediate' : '1-2 weeks'
                });
            }
        }
        // Recommandations pour vulnérabilités critiques
        const criticalVulns = vulnerabilities.filter(v => v.severity === 'critical');
        if (criticalVulns.length > 0) {
            recommendations.unshift({
                id: `rec-critical-${Date.now()}`,
                title: 'Correction immédiate des vulnérabilités critiques',
                description: `${criticalVulns.length} vulnérabilités critiques nécessitent une attention immédiate`,
                priority: 'critical',
                effort: 'high',
                impact: 'critical',
                category: 'immediate-action',
                implementation: [
                    'Analyser chaque vulnérabilité critique individuellement',
                    'Appliquer les correctifs de sécurité disponibles',
                    'Implémenter des mesures de contournement temporaires si nécessaire',
                    'Tester les correctifs en environnement de développement',
                    'Déployer les correctifs en production avec surveillance'
                ],
                timeline: 'immediate'
            });
        }
        return recommendations;
    }
    // Méthodes utilitaires pour les recommandations
    getCategoryRecommendationTitle(category) {
        const titles = {
            'injection': 'Renforcement contre les attaques par injection',
            'authentication': 'Amélioration de l\'authentification',
            'authorization': 'Renforcement du contrôle d\'accès',
            'data-exposure': 'Protection des données sensibles',
            'xml-external-entities': 'Sécurisation du traitement XML',
            'broken-access-control': 'Correction du contrôle d\'accès',
            'security-misconfiguration': 'Correction des configurations de sécurité',
            'cross-site-scripting': 'Protection contre XSS',
            'insecure-deserialization': 'Sécurisation de la désérialisation',
            'vulnerable-components': 'Mise à jour des composants vulnérables',
            'insufficient-logging': 'Amélioration de la journalisation',
            'cryptographic-failures': 'Renforcement cryptographique',
            'server-side-request-forgery': 'Protection contre SSRF',
            'software-data-integrity': 'Amélioration de l\'intégrité des données',
            'security-logging-monitoring': 'Renforcement du monitoring de sécurité'
        };
        return titles[category] || 'Amélioration de la sécurité';
    }
    getCategoryRecommendationDescription(category) {
        const descriptions = {
            'injection': 'Plusieurs vulnérabilités d\'injection ont été détectées. Implémentez une validation d\'entrée stricte et utilisez des requêtes préparées.',
            'authentication': 'Des faiblesses d\'authentification ont été identifiées. Renforcez les mécanismes d\'authentification et implémentez l\'authentification multi-facteurs.',
            'authorization': 'Des problèmes d\'autorisation ont été trouvés. Révisez et renforcez les contrôles d\'accès.',
            'data-exposure': 'Des données sensibles sont exposées. Implémentez un chiffrement approprié et révisez les permissions d\'accès.',
            'cross-site-scripting': 'Des vulnérabilités XSS ont été détectées. Implémentez une validation et un échappement appropriés des données utilisateur.'
        };
        return descriptions[category] || 'Des vulnérabilités de sécurité ont été détectées dans cette catégorie.';
    }
    getCategoryImplementationSteps(category) {
        const steps = {
            'injection': [
                'Implémenter une validation d\'entrée stricte',
                'Utiliser des requêtes préparées ou des ORM',
                'Appliquer le principe du moindre privilège pour les accès base de données',
                'Mettre en place une liste blanche pour les entrées utilisateur'
            ],
            'authentication': [
                'Implémenter l\'authentification multi-facteurs',
                'Renforcer les politiques de mots de passe',
                'Implémenter la limitation du taux de tentatives',
                'Utiliser des tokens sécurisés pour les sessions'
            ]
        };
        return steps[category] || [
            'Analyser les vulnérabilités spécifiques',
            'Appliquer les correctifs recommandés',
            'Tester les corrections',
            'Surveiller les améliorations'
        ];
    }
    getSeverityWeight(severity) {
        const weights = { critical: 5, high: 4, medium: 3, low: 2, info: 1 };
        return weights[severity] || 1;
    }
    /**
     * Arrêt du scanner
     */
    async shutdown() {
        this.logger.info('🛑 Arrêt du Scanner de Vulnérabilités...');
        // Arrêt des scanners spécialisés
        await Promise.all([
            this.staticAnalyzer.shutdown(),
            this.dynamicAnalyzer.shutdown(),
            this.containerScanner.shutdown(),
            this.infrastructureScanner.shutdown(),
            this.networkScanner.shutdown(),
            this.webScanner.shutdown(),
            this.apiScanner.shutdown()
        ]);
        this.isInitialized = false;
        this.logger.info('✅ Scanner de Vulnérabilités arrêté');
    }
}
exports.VulnerabilityScanner = VulnerabilityScanner;
//# sourceMappingURL=VulnerabilityScanner.js.map