import { Logger } from 'winston';
import { SecurityScanRequest, Vulnerability } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Scanner Web
 *
 * Analyse les vulnérabilités des applications web
 */
export declare class WebScanner {
    private logger;
    private memory;
    private isInitialized;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le scanner web
     */
    initialize(): Promise<void>;
    /**
     * Effectue un scan web
     */
    scan(request: SecurityScanRequest): Promise<Vulnerability[]>;
    /**
     * Scanne les vulnérabilités OWASP Top 10
     */
    private scanOWASPTop10;
    /**
     * Scanne les en-têtes de sécurité
     */
    private scanSecurityHeaders;
    /**
     * Scanne la sécurité des cookies
     */
    private scanCookieSecurity;
    /**
     * Obtient la recommandation pour un en-tête manquant
     */
    private getHeaderRecommendation;
    /**
     * <PERSON>e les résultats en mémoire
     */
    private storeResults;
    /**
     * Arrêt du scanner web
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=WebScanner.d.ts.map