"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditSystem = void 0;
const events_1 = require("events");
/**
 * Système d'Audit Complet
 *
 * Enregistre, analyse et rapporte toutes les activités de sécurité
 */
class AuditSystem extends events_1.EventEmitter {
    constructor(logger, memory, communication) {
        super();
        this.auditLogs = new Map();
        this.auditRules = new Map();
        this.auditReports = new Map();
        this.alertThresholds = new Map();
        this.isInitialized = false;
        this.isMonitoring = false;
        this.logger = logger;
        this.memory = memory;
        this.communication = communication;
    }
    /**
     * Initialise le système d'audit
     */
    async initialize() {
        try {
            this.logger.info('📋 Initialisation du Audit System...');
            // Chargement des règles d'audit
            await this.loadAuditRules();
            // Configuration des seuils d'alerte
            await this.setupAlertThresholds();
            // Chargement des logs existants
            await this.loadExistingLogs();
            // Démarrage du monitoring
            this.startAuditMonitoring();
            this.isInitialized = true;
            this.logger.info('✅ Audit System initialisé');
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'initialisation du Audit System:', error);
            throw error;
        }
    }
    /**
     * Enregistre un événement d'audit
     */
    async logEvent(event) {
        const auditLog = {
            id: this.generateAuditId(),
            timestamp: new Date(),
            event,
            source: event.source || 'security-agent',
            severity: this.calculateEventSeverity(event),
            category: event.category,
            metadata: {
                ...event.metadata,
                processingTime: Date.now()
            }
        };
        // Stockage du log
        this.auditLogs.set(auditLog.id, auditLog);
        await this.memory.storeAuditLog(auditLog);
        this.logger.info(`📝 Événement d'audit enregistré: ${event.type} (${auditLog.severity})`);
        // Vérification des règles d'audit
        await this.checkAuditRules(auditLog);
        // Notification
        this.emit('audit-logged', auditLog);
        // Vérification des seuils d'alerte
        await this.checkAlertThresholds(auditLog);
    }
    /**
     * Enregistre un accès système
     */
    async logAccess(accessEvent) {
        const event = {
            type: 'access',
            category: 'authentication',
            description: `Accès ${accessEvent.action} pour ${accessEvent.userId}`,
            userId: accessEvent.userId,
            resource: accessEvent.resource,
            action: accessEvent.action,
            result: accessEvent.result,
            ipAddress: accessEvent.ipAddress,
            userAgent: accessEvent.userAgent,
            metadata: accessEvent.metadata
        };
        await this.logEvent(event);
    }
    /**
     * Enregistre une modification de configuration
     */
    async logConfigurationChange(configEvent) {
        const event = {
            type: 'configuration-change',
            category: 'system',
            description: `Configuration modifiée: ${configEvent.component}`,
            userId: configEvent.userId,
            resource: configEvent.component,
            action: 'modify',
            result: 'success',
            metadata: {
                oldValue: configEvent.oldValue,
                newValue: configEvent.newValue,
                changeReason: configEvent.reason
            }
        };
        await this.logEvent(event);
    }
    /**
     * Enregistre un incident de sécurité
     */
    async logSecurityIncident(incident) {
        const event = {
            type: 'security-incident',
            category: 'security',
            description: `Incident de sécurité: ${incident.title}`,
            resource: incident.affectedResource,
            action: 'incident',
            result: 'detected',
            metadata: {
                incidentId: incident.incidentId,
                severity: incident.severity,
                category: incident.category,
                detectionMethod: incident.detectionMethod
            }
        };
        await this.logEvent(event);
    }
    /**
     * Génère un rapport d'audit
     */
    async generateAuditReport(reportType, period, filters) {
        this.logger.info(`📊 Génération du rapport d'audit: ${reportType}`);
        const reportId = this.generateReportId();
        const logs = await this.getLogsForPeriod(period, filters);
        const report = {
            id: reportId,
            type: reportType,
            period,
            generatedAt: new Date(),
            summary: this.generateReportSummary(logs),
            findings: this.analyzeLogsForFindings(logs),
            recommendations: this.generateRecommendations(logs),
            statistics: this.calculateStatistics(logs),
            logs: logs.slice(0, 1000), // Limiter pour la performance
            metadata: {
                totalLogs: logs.length,
                filters: filters || {}
            }
        };
        // Stockage du rapport
        this.auditReports.set(reportId, report);
        await this.memory.storeAuditReport(report);
        this.emit('audit-report-generated', report);
        return report;
    }
    /**
     * Recherche dans les logs d'audit
     */
    async searchLogs(query) {
        const allLogs = Array.from(this.auditLogs.values());
        return allLogs.filter(log => {
            // Filtrage par période
            if (query.startDate && log.timestamp < query.startDate)
                return false;
            if (query.endDate && log.timestamp > query.endDate)
                return false;
            // Filtrage par utilisateur
            if (query.userId && log.event.userId !== query.userId)
                return false;
            // Filtrage par type d'événement
            if (query.eventType && log.event.type !== query.eventType)
                return false;
            // Filtrage par catégorie
            if (query.category && log.category !== query.category)
                return false;
            // Filtrage par sévérité
            if (query.severity && log.severity !== query.severity)
                return false;
            // Recherche textuelle
            if (query.searchText) {
                const searchLower = query.searchText.toLowerCase();
                const description = log.event.description?.toLowerCase() || '';
                const resource = log.event.resource?.toLowerCase() || '';
                if (!description.includes(searchLower) && !resource.includes(searchLower)) {
                    return false;
                }
            }
            return true;
        }).slice(0, query.limit || 100);
    }
    /**
     * Vérifie les règles d'audit
     */
    async checkAuditRules(auditLog) {
        for (const [ruleId, rule] of this.auditRules) {
            if (rule.enabled && this.matchesRule(auditLog, rule)) {
                await this.executeRuleAction(rule, auditLog);
            }
        }
    }
    /**
     * Vérifie si un log correspond à une règle
     */
    matchesRule(auditLog, rule) {
        // Vérification du type d'événement
        if (rule.conditions.eventTypes &&
            !rule.conditions.eventTypes.includes(auditLog.event.type)) {
            return false;
        }
        // Vérification de la sévérité
        if (rule.conditions.minSeverity) {
            const severityLevels = { low: 1, medium: 2, high: 3, critical: 4 };
            const logLevel = severityLevels[auditLog.severity] || 0;
            const minLevel = severityLevels[rule.conditions.minSeverity] || 0;
            if (logLevel < minLevel)
                return false;
        }
        // Vérification des patterns
        if (rule.conditions.patterns) {
            const description = auditLog.event.description || '';
            const hasMatch = rule.conditions.patterns.some(pattern => {
                const regex = new RegExp(pattern, 'i');
                return regex.test(description);
            });
            if (!hasMatch)
                return false;
        }
        return true;
    }
    /**
     * Exécute l'action d'une règle
     */
    async executeRuleAction(rule, auditLog) {
        this.logger.info(`⚡ Exécution de la règle d'audit: ${rule.name}`);
        switch (rule.action.type) {
            case 'alert':
                await this.sendAlert(rule, auditLog);
                break;
            case 'escalate':
                await this.escalateIncident(rule, auditLog);
                break;
            case 'block':
                await this.blockAction(rule, auditLog);
                break;
            case 'notify':
                await this.sendNotification(rule, auditLog);
                break;
        }
    }
    /**
     * Envoie une alerte
     */
    async sendAlert(rule, auditLog) {
        const alert = {
            type: 'audit-rule-triggered',
            title: `Règle d'audit déclenchée: ${rule.name}`,
            description: auditLog.event.description,
            severity: auditLog.severity,
            ruleId: rule.id,
            auditLogId: auditLog.id,
            timestamp: new Date()
        };
        await this.communication.sendMessage('security-alerts', alert);
        this.emit('audit-alert', alert);
    }
    /**
     * Calcule la sévérité d'un événement
     */
    calculateEventSeverity(event) {
        // Logique de calcul de sévérité basée sur le type et le contenu
        if (event.type === 'security-incident')
            return 'critical';
        if (event.type === 'access' && event.result === 'failure')
            return 'high';
        if (event.type === 'configuration-change')
            return 'medium';
        return 'low';
    }
    /**
     * Charge les règles d'audit
     */
    async loadAuditRules() {
        // Règles par défaut
        this.auditRules.set('failed-login-attempts', {
            id: 'failed-login-attempts',
            name: 'Tentatives de connexion échouées',
            description: 'Détecte les tentatives de connexion multiples échouées',
            enabled: true,
            conditions: {
                eventTypes: ['access'],
                patterns: ['failure', 'failed'],
                minSeverity: 'medium'
            },
            action: {
                type: 'alert',
                parameters: { threshold: 5, timeWindow: 300 }
            }
        });
        this.auditRules.set('privilege-escalation', {
            id: 'privilege-escalation',
            name: 'Escalade de privilèges',
            description: 'Détecte les tentatives d\'escalade de privilèges',
            enabled: true,
            conditions: {
                eventTypes: ['access', 'configuration-change'],
                patterns: ['admin', 'root', 'privilege'],
                minSeverity: 'high'
            },
            action: {
                type: 'escalate',
                parameters: { severity: 'critical' }
            }
        });
    }
    /**
     * Configure les seuils d'alerte
     */
    async setupAlertThresholds() {
        this.alertThresholds.set('failed-logins', {
            id: 'failed-logins',
            name: 'Connexions échouées',
            eventType: 'access',
            threshold: 10,
            timeWindow: 300, // 5 minutes
            action: 'alert'
        });
        this.alertThresholds.set('security-incidents', {
            id: 'security-incidents',
            name: 'Incidents de sécurité',
            eventType: 'security-incident',
            threshold: 3,
            timeWindow: 3600, // 1 heure
            action: 'escalate'
        });
    }
    /**
     * Vérifie les seuils d'alerte
     */
    async checkAlertThresholds(auditLog) {
        for (const [thresholdId, threshold] of this.alertThresholds) {
            if (auditLog.event.type === threshold.eventType) {
                const recentLogs = await this.getRecentLogsOfType(threshold.eventType, threshold.timeWindow);
                if (recentLogs.length >= threshold.threshold) {
                    await this.triggerThresholdAlert(threshold, recentLogs);
                }
            }
        }
    }
    /**
     * Récupère les logs récents d'un type donné
     */
    async getRecentLogsOfType(eventType, timeWindow) {
        const cutoffTime = new Date(Date.now() - (timeWindow * 1000));
        return Array.from(this.auditLogs.values()).filter(log => log.event.type === eventType && log.timestamp > cutoffTime);
    }
    /**
     * Déclenche une alerte de seuil
     */
    async triggerThresholdAlert(threshold, logs) {
        const alert = {
            type: 'threshold-exceeded',
            title: `Seuil dépassé: ${threshold.name}`,
            description: `${logs.length} événements de type ${threshold.eventType} en ${threshold.timeWindow}s`,
            severity: 'high',
            thresholdId: threshold.id,
            eventCount: logs.length,
            timestamp: new Date()
        };
        await this.communication.sendMessage('security-alerts', alert);
        this.emit('threshold-alert', alert);
    }
    /**
     * Charge les logs existants
     */
    async loadExistingLogs() {
        try {
            const existingLogs = await this.memory.getAuditLogs();
            existingLogs.forEach(log => {
                this.auditLogs.set(log.id, log);
            });
            this.logger.info(`📚 ${existingLogs.length} logs d'audit chargés`);
        }
        catch (error) {
            this.logger.warn('⚠️ Erreur lors du chargement des logs existants:', error);
        }
    }
    /**
     * Démarre le monitoring d'audit
     */
    startAuditMonitoring() {
        this.isMonitoring = true;
        // Nettoyage périodique des anciens logs
        setInterval(() => {
            this.cleanupOldLogs();
        }, 60 * 60 * 1000); // Toutes les heures
        // Génération de rapports automatiques
        setInterval(() => {
            this.generateAutomaticReports();
        }, 24 * 60 * 60 * 1000); // Tous les jours
    }
    /**
     * Nettoie les anciens logs
     */
    cleanupOldLogs() {
        const retentionDays = 90; // 3 mois
        const cutoffDate = new Date(Date.now() - (retentionDays * 24 * 60 * 60 * 1000));
        let cleanedCount = 0;
        for (const [logId, log] of this.auditLogs) {
            if (log.timestamp < cutoffDate) {
                this.auditLogs.delete(logId);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            this.logger.info(`🧹 ${cleanedCount} logs d'audit anciens nettoyés`);
        }
    }
    /**
     * Génère des rapports automatiques
     */
    async generateAutomaticReports() {
        const yesterday = new Date(Date.now() - (24 * 60 * 60 * 1000));
        const today = new Date();
        try {
            await this.generateAuditReport('daily', {
                start: yesterday,
                end: today
            });
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la génération du rapport automatique:', error);
        }
    }
    /**
     * Récupère les logs pour une période
     */
    async getLogsForPeriod(period, filters) {
        return Array.from(this.auditLogs.values()).filter(log => {
            if (log.timestamp < period.start || log.timestamp > period.end) {
                return false;
            }
            if (filters?.eventTypes && !filters.eventTypes.includes(log.event.type)) {
                return false;
            }
            if (filters?.severity && log.severity !== filters.severity) {
                return false;
            }
            return true;
        });
    }
    /**
     * Génère un résumé de rapport
     */
    generateReportSummary(logs) {
        const eventTypes = new Map();
        const severities = new Map();
        logs.forEach(log => {
            eventTypes.set(log.event.type, (eventTypes.get(log.event.type) || 0) + 1);
            severities.set(log.severity, (severities.get(log.severity) || 0) + 1);
        });
        return {
            totalEvents: logs.length,
            eventsByType: Object.fromEntries(eventTypes),
            eventsBySeverity: Object.fromEntries(severities),
            timeRange: {
                start: logs.length > 0 ? Math.min(...logs.map(l => l.timestamp.getTime())) : 0,
                end: logs.length > 0 ? Math.max(...logs.map(l => l.timestamp.getTime())) : 0
            }
        };
    }
    /**
     * Analyse les logs pour trouver des découvertes
     */
    analyzeLogsForFindings(logs) {
        const findings = [];
        // Analyse des patterns suspects
        const failedLogins = logs.filter(l => l.event.type === 'access' && l.event.result === 'failure');
        if (failedLogins.length > 10) {
            findings.push({
                id: 'high-failed-logins',
                title: 'Nombre élevé de connexions échouées',
                description: `${failedLogins.length} tentatives de connexion échouées détectées`,
                severity: 'high',
                category: 'authentication',
                evidence: failedLogins.slice(0, 5).map(l => l.id)
            });
        }
        return findings;
    }
    /**
     * Génère des recommandations
     */
    generateRecommendations(logs) {
        const recommendations = [];
        // Recommandations basées sur l'analyse
        const securityIncidents = logs.filter(l => l.event.type === 'security-incident');
        if (securityIncidents.length > 0) {
            recommendations.push({
                id: 'review-security-incidents',
                title: 'Révision des incidents de sécurité',
                description: 'Réviser et analyser les incidents de sécurité récents',
                priority: 'high',
                category: 'security'
            });
        }
        return recommendations;
    }
    /**
     * Calcule les statistiques
     */
    calculateStatistics(logs) {
        return {
            totalEvents: logs.length,
            eventsPerDay: logs.length / 7, // Moyenne sur 7 jours
            topEventTypes: this.getTopEventTypes(logs),
            topUsers: this.getTopUsers(logs),
            riskScore: this.calculateRiskScore(logs)
        };
    }
    /**
     * Récupère les types d'événements les plus fréquents
     */
    getTopEventTypes(logs) {
        const counts = new Map();
        logs.forEach(log => {
            counts.set(log.event.type, (counts.get(log.event.type) || 0) + 1);
        });
        return Array.from(counts.entries())
            .map(([type, count]) => ({ type, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
    }
    /**
     * Récupère les utilisateurs les plus actifs
     */
    getTopUsers(logs) {
        const counts = new Map();
        logs.forEach(log => {
            if (log.event.userId) {
                counts.set(log.event.userId, (counts.get(log.event.userId) || 0) + 1);
            }
        });
        return Array.from(counts.entries())
            .map(([userId, count]) => ({ userId, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
    }
    /**
     * Calcule le score de risque
     */
    calculateRiskScore(logs) {
        let score = 0;
        logs.forEach(log => {
            switch (log.severity) {
                case 'critical':
                    score += 10;
                    break;
                case 'high':
                    score += 5;
                    break;
                case 'medium':
                    score += 2;
                    break;
                case 'low':
                    score += 1;
                    break;
            }
        });
        return Math.min(100, score / logs.length * 10);
    }
    /**
     * Génère un ID d'audit
     */
    generateAuditId() {
        return `audit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Génère un ID de rapport
     */
    generateReportId() {
        return `report-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    // Méthodes de notification (à implémenter selon les besoins)
    async escalateIncident(rule, auditLog) {
        // Implémentation de l'escalade d'incident
    }
    async blockAction(rule, auditLog) {
        // Implémentation du blocage d'action
    }
    async sendNotification(rule, auditLog) {
        // Implémentation de l'envoi de notification
    }
    /**
     * Arrêt du système d'audit
     */
    async shutdown() {
        this.logger.info('🛑 Arrêt du Audit System...');
        this.isInitialized = false;
        this.isMonitoring = false;
    }
}
exports.AuditSystem = AuditSystem;
//# sourceMappingURL=AuditSystem.js.map