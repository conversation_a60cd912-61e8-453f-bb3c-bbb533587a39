import { Logger } from 'winston';
import { ComplianceConfig, ComplianceResult, SecurityScanResult } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Moteur de Conformité
 *
 * Vérifie la conformité aux frameworks de sécurité (OWASP, CIS, NIST, ISO 27001, SOC 2)
 */
export declare class ComplianceChecker {
    private config;
    private logger;
    private memory;
    private frameworks;
    private complianceResults;
    private isInitialized;
    constructor(config: ComplianceConfig, logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le vérificateur de conformité
     */
    initialize(): Promise<void>;
    /**
     * Charge les frameworks de conformité
     */
    private loadComplianceFrameworks;
    /**
     * Charge les frameworks prédéfinis (OWASP, CIS, etc.)
     */
    private loadPredefinedFrameworks;
    /**
     * Vérifie la conformité d'un résultat de scan
     */
    checkCompliance(scanResult: SecurityScanResult): Promise<ComplianceResult[]>;
    /**
     * Vérifie la conformité pour un framework spécifique
     */
    checkFramework(frameworkName: string, target: any, configuration: any): Promise<ComplianceResult[]>;
    /**
     * Vérifie un contrôle spécifique
     */
    private checkControl;
    /**
     * Exécute une vérification de conformité
     */
    private executeComplianceCheck;
    /**
     * Vérifie la politique de mots de passe
     */
    private checkPasswordPolicy;
    /**
     * Vérifie le chiffrement
     */
    private checkEncryption;
    /**
     * Vérifie le contrôle d'accès
     */
    private checkAccessControl;
    /**
     * Charge les politiques personnalisées
     */
    private loadCustomPolicies;
    /**
     * Initialise les contrôles automatisés
     */
    private initializeAutomatedControls;
    /**
     * Vérifie la conformité d'un framework pour un résultat de scan
     */
    private checkFrameworkCompliance;
    /**
     * Vérifie un contrôle contre un résultat de scan
     */
    private checkControlAgainstScanResult;
    /**
     * Détermine si une vulnérabilité est pertinente pour un contrôle
     */
    private isVulnerabilityRelevantToControl;
    private createOWASPTop10Framework;
    private createCISControlsFramework;
    private createNISTCSFFramework;
    private createISO27001Framework;
    private createSOC2Framework;
    /**
     * Arrêt du vérificateur de conformité
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=ComplianceChecker.d.ts.map