{"version": 3, "file": "ComplianceChecker.js", "sourceRoot": "", "sources": ["../../src/compliance/ComplianceChecker.ts"], "names": [], "mappings": ";;;AAaA;;;;GAIG;AACH,MAAa,iBAAiB;IAU5B,YACE,MAAwB,EACxB,MAAc,EACd,MAAsB;QARhB,eAAU,GAAqC,IAAI,GAAG,EAAE,CAAC;QACzD,sBAAiB,GAAoC,IAAI,GAAG,EAAE,CAAC;QAE/D,kBAAa,GAAY,KAAK,CAAC;QAOrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAE/D,0CAA0C;YAC1C,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAEtC,2CAA2C;YAC3C,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEhC,2CAA2C;YAC3C,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAEzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAEtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2DAA2D,EAAE,KAAK,CAAC,CAAC;YACtF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB;QACpC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC/C,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;gBACtB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB;QACpC,eAAe;QACf,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACpD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAEhD,eAAe;QACf,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAEjD,+BAA+B;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAEzC,YAAY;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAE3C,QAAQ;QACR,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACxC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,UAA8B;QAClD,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,KAAK,MAAM,CAAC,aAAa,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,aAAa,KAAK,CAAC,CAAC;YAEnE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAC1D,SAAS,EACT,UAAU,CACX,CAAC;YAEF,OAAO,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC;QACpC,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACnD,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAEjE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,aAAqB,EACrB,MAAW,EACX,aAAkB;QAElB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACrD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,yBAAyB,aAAa,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,aAAa,SAAS,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC;QAE5E,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACzC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAC3C,SAAS,EACT,OAAO,EACP,MAAM,EACN,aAAa,CACd,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CACxB,SAA8B,EAC9B,OAA0B,EAC1B,MAAW,EACX,aAAkB;QAElB,MAAM,QAAQ,GAAwB,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAe,EAAE,CAAC;QAChC,IAAI,aAAa,GAAuE,WAAW,CAAC;QACpG,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,IAAI,CAAC;YACH,2CAA2C;YAC3C,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACnC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACnD,KAAK,EACL,MAAM,EACN,aAAa,CACd,CAAC;oBAEF,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;wBACxB,QAAQ,CAAC,IAAI,CAAC;4BACZ,EAAE,EAAE,WAAW,KAAK,CAAC,EAAE,EAAE;4BACzB,WAAW,EAAE,WAAW,CAAC,OAAO;4BAChC,QAAQ,EAAE,KAAK,CAAC,QAAQ;4BACxB,WAAW,EAAE,WAAW,CAAC,WAAW,IAAI,kCAAkC;4BAC1E,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,SAAS;yBAC5C,CAAC,CAAC;wBAEH,IAAI,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;4BAClC,aAAa,GAAG,eAAe,CAAC;4BAChC,KAAK,IAAI,EAAE,CAAC;wBACd,CAAC;6BAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;4BACrC,aAAa,GAAG,eAAe,CAAC;4BAChC,KAAK,IAAI,EAAE,CAAC;wBACd,CAAC;6BAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;4BACvC,KAAK,IAAI,EAAE,CAAC;wBACd,CAAC;6BAAM,CAAC;4BACN,KAAK,IAAI,CAAC,CAAC;wBACb,CAAC;oBACH,CAAC;oBAED,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;wBACzB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;oBACtC,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,yBAAyB;gBACzB,aAAa,GAAG,eAAe,CAAC;gBAChC,KAAK,GAAG,CAAC,CAAC;YACZ,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACxF,aAAa,GAAG,eAAe,CAAC;YAChC,KAAK,GAAG,CAAC,CAAC;QACZ,CAAC;QAED,OAAO;YACL,aAAa,EAAE,SAAS,CAAC,IAAI;YAC7B,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,MAAM,EAAE,aAAa;YACrB,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;YACzB,QAAQ;YACR,QAAQ;YACR,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,KAAsB,EACtB,MAAW,EACX,aAAkB;QAQlB,+CAA+C;QAC/C,8DAA8D;QAE9D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAE1D,kCAAkC;QAClC,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QACxD,CAAC;QAED,8DAA8D;QAC9D,OAAO;YACL,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,uCAAuC,KAAK,CAAC,IAAI,EAAE;YAC5D,WAAW,EAAE,uEAAuE;SACrF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,MAAW,EAAE,aAAkB;QAC/D,kEAAkE;QAClE,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,qCAAqC;SAC/C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,MAAW,EAAE,aAAkB;QAC3D,mDAAmD;QACnD,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,sBAAsB;SAChC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,MAAW,EAAE,aAAkB;QAC9D,wDAAwD;QACxD,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,4BAA4B;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CACpC,SAA8B,EAC9B,UAA8B;QAE9B,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,6BAA6B,CACrD,SAAS,EACT,OAAO,EACP,UAAU,CACX,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,6BAA6B,CACzC,SAA8B,EAC9B,OAA0B,EAC1B,UAA8B;QAE9B,MAAM,QAAQ,GAAwB,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAe,EAAE,CAAC;QAChC,IAAI,MAAM,GAAuE,WAAW,CAAC;QAC7F,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,qDAAqD;QACrD,KAAK,MAAM,aAAa,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;YACvD,IAAI,IAAI,CAAC,gCAAgC,CAAC,aAAa,EAAE,OAAO,CAAC,EAAE,CAAC;gBAClE,QAAQ,CAAC,IAAI,CAAC;oBACZ,EAAE,EAAE,QAAQ,aAAa,CAAC,EAAE,EAAE;oBAC9B,WAAW,EAAE,2BAA2B,aAAa,CAAC,KAAK,EAAE;oBAC7D,QAAQ,EAAE,aAAa,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,QAAkD;oBACtH,WAAW,EAAE,aAAa,CAAC,WAAW,CAAC,WAAW;oBAClD,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,SAAS;iBACjF,CAAC,CAAC;gBAEH,wCAAwC;gBACxC,IAAI,aAAa,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;oBAC1C,MAAM,GAAG,eAAe,CAAC;oBACzB,KAAK,IAAI,EAAE,CAAC;gBACd,CAAC;qBAAM,IAAI,aAAa,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;oBAC7C,MAAM,GAAG,eAAe,CAAC;oBACzB,KAAK,IAAI,EAAE,CAAC;gBACd,CAAC;qBAAM,IAAI,aAAa,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBAC/C,KAAK,IAAI,EAAE,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,KAAK,IAAI,CAAC,CAAC;gBACb,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,aAAa,EAAE,SAAS,CAAC,IAAI;YAC7B,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,MAAM;YACN,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;YACzB,QAAQ;YACR,QAAQ;YACR,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gCAAgC,CAAC,aAAkB,EAAE,OAA0B;QACrF,uDAAuD;QACvD,qEAAqE;QAErE,MAAM,kBAAkB,GAAG,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QAC7D,MAAM,YAAY,GAAG,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAE1D,OAAO,kBAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC;YACzC,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC;YAClC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC;IAED,gDAAgD;IACxC,yBAAyB;QAC/B,OAAO;YACL,IAAI,EAAE,cAAc;YACpB,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE;gBACR;oBACE,EAAE,EAAE,KAAK;oBACT,IAAI,EAAE,uBAAuB;oBAC7B,WAAW,EAAE,8BAA8B;oBAC3C,QAAQ,EAAE,gBAAgB;oBAC1B,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,IAAI;oBACf,MAAM,EAAE,EAAE;iBACX;gBACD;oBACE,EAAE,EAAE,KAAK;oBACT,IAAI,EAAE,wBAAwB;oBAC9B,WAAW,EAAE,+BAA+B;oBAC5C,QAAQ,EAAE,cAAc;oBACxB,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,IAAI;oBACf,MAAM,EAAE,EAAE;iBACX;gBACD,4BAA4B;aAC7B;SACF,CAAC;IACJ,CAAC;IAEO,0BAA0B;QAChC,OAAO;YACL,IAAI,EAAE,cAAc;YACpB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;IAEO,sBAAsB;QAC5B,OAAO;YACL,IAAI,EAAE,8BAA8B;YACpC,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;IAEO,uBAAuB;QAC7B,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;IAEO,mBAAmB;QACzB,OAAO;YACL,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;CACF;AA9cD,8CA8cC"}