"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KafkaCommunication = void 0;
const events_1 = require("events");
const kafkajs_1 = require("kafkajs");
/**
 * Gestionnaire de Communication Kafka pour l'Agent Security
 *
 * Gère la communication synaptique avec les autres agents
 */
class KafkaCommunication extends events_1.EventEmitter {
    constructor(logger, brokers, agentId) {
        super();
        this.isConnected = false;
        // Topics Kafka pour la communication
        this.topics = {
            // Topics entrants (écoute)
            securityScanRequest: 'security.scan.request',
            securityAlert: 'security.alert',
            complianceCheckRequest: 'compliance.check.request',
            securityIncident: 'security.incident',
            securityReportRequest: 'security.report.request',
            agentStatus: 'agent.status',
            cortexInstruction: 'cortex.instruction',
            // Topics sortants (envoi)
            securityScanResult: 'security.scan.result',
            complianceCheckResult: 'compliance.check.result',
            securityReportResult: 'security.report.result',
            securityAlertOut: 'security.alert.out',
            agentResponse: 'agent.response',
            agentHeartbeat: 'agent.heartbeat'
        };
        this.logger = logger;
        this.brokers = brokers;
        this.agentId = agentId;
        // Configuration Kafka
        this.kafka = new kafkajs_1.Kafka({
            clientId: `agent-security-${agentId}`,
            brokers: brokers.split(','),
            retry: {
                initialRetryTime: 100,
                retries: 8
            },
            connectionTimeout: 3000,
            requestTimeout: 30000
        });
        this.producer = this.kafka.producer({
            maxInFlightRequests: 1,
            idempotent: true,
            transactionTimeout: 30000
        });
        this.consumer = this.kafka.consumer({
            groupId: `security-agent-${agentId}`,
            sessionTimeout: 30000,
            rebalanceTimeout: 60000,
            heartbeatInterval: 3000
        });
    }
    /**
     * Initialise la communication Kafka
     */
    async initialize() {
        try {
            this.logger.info('📡 Initialisation de la communication Kafka Security...');
            // Connexion du producer
            await this.producer.connect();
            this.logger.info('📤 Producer Kafka connecté');
            // Connexion du consumer
            await this.consumer.connect();
            this.logger.info('📥 Consumer Kafka connecté');
            // Abonnement aux topics
            await this.subscribeToTopics();
            // Démarrage de l'écoute des messages
            await this.startListening();
            // Envoi du heartbeat initial
            await this.sendHeartbeat();
            // Démarrage du heartbeat périodique
            this.startHeartbeat();
            this.isConnected = true;
            this.logger.info('✅ Communication Kafka Security initialisée');
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'initialisation Kafka Security:', error);
            throw error;
        }
    }
    /**
     * Abonnement aux topics Kafka
     */
    async subscribeToTopics() {
        const incomingTopics = [
            this.topics.securityScanRequest,
            this.topics.securityAlert,
            this.topics.complianceCheckRequest,
            this.topics.securityIncident,
            this.topics.securityReportRequest,
            this.topics.agentStatus,
            this.topics.cortexInstruction
        ];
        for (const topic of incomingTopics) {
            await this.consumer.subscribe({ topic, fromBeginning: false });
            this.logger.debug(`📥 Abonné au topic: ${topic}`);
        }
    }
    /**
     * Démarre l'écoute des messages
     */
    async startListening() {
        await this.consumer.run({
            eachMessage: async ({ topic, partition, message }) => {
                try {
                    await this.handleMessage(topic, message);
                }
                catch (error) {
                    this.logger.error(`❌ Erreur lors du traitement du message ${topic}:`, error);
                }
            }
        });
    }
    /**
     * Traite un message reçu
     */
    async handleMessage(topic, message) {
        if (!message.value)
            return;
        try {
            const data = JSON.parse(message.value.toString());
            this.logger.debug(`📨 Message reçu sur ${topic}:`, {
                key: message.key?.toString(),
                timestamp: message.timestamp,
                size: message.value.length
            });
            // Routage des messages selon le topic
            switch (topic) {
                case this.topics.securityScanRequest:
                    this.emit('security-scan-request', data);
                    break;
                case this.topics.securityAlert:
                    this.emit('security-alert', data);
                    break;
                case this.topics.complianceCheckRequest:
                    this.emit('compliance-check-request', data);
                    break;
                case this.topics.securityIncident:
                    this.emit('security-incident', data);
                    break;
                case this.topics.securityReportRequest:
                    this.emit('security-report-request', data);
                    break;
                case this.topics.agentStatus:
                    this.emit('agent-status', data);
                    break;
                case this.topics.cortexInstruction:
                    if (this.isInstructionForSecurity(data)) {
                        this.emit('cortex-instruction', data);
                    }
                    break;
                default:
                    this.logger.warn(`⚠️ Topic non géré: ${topic}`);
            }
        }
        catch (error) {
            this.logger.error(`❌ Erreur lors du parsing du message ${topic}:`, error);
        }
    }
    /**
     * Vérifie si une instruction du Cortex concerne la sécurité
     */
    isInstructionForSecurity(instruction) {
        const securityKeywords = [
            'security', 'sécurité', 'scan', 'vulnerability', 'vulnérabilité',
            'compliance', 'conformité', 'threat', 'menace', 'incident',
            'malware', 'virus', 'attack', 'attaque', 'breach', 'faille'
        ];
        const text = (instruction.instruction || '').toLowerCase();
        return securityKeywords.some(keyword => text.includes(keyword));
    }
    /**
     * Envoie un message à un agent spécifique
     */
    async sendMessage(targetAgent, message) {
        try {
            const kafkaMessage = {
                key: targetAgent,
                value: JSON.stringify({
                    ...message,
                    from: this.agentId,
                    timestamp: new Date().toISOString()
                })
            };
            await this.producer.send({
                topic: this.topics.agentResponse,
                messages: [kafkaMessage]
            });
            this.logger.debug(`📤 Message envoyé à ${targetAgent}:`, message.type);
        }
        catch (error) {
            this.logger.error(`❌ Erreur lors de l'envoi du message à ${targetAgent}:`, error);
            throw error;
        }
    }
    /**
     * Envoie un résultat de scan de sécurité
     */
    async sendScanResult(scanResult) {
        try {
            await this.producer.send({
                topic: this.topics.securityScanResult,
                messages: [{
                        key: scanResult.scanId,
                        value: JSON.stringify({
                            ...scanResult,
                            from: this.agentId,
                            timestamp: new Date().toISOString()
                        })
                    }]
            });
            this.logger.debug(`📤 Résultat de scan envoyé: ${scanResult.scanId}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'envoi du résultat de scan:', error);
            throw error;
        }
    }
    /**
     * Envoie un résultat de vérification de compliance
     */
    async sendComplianceResult(complianceResult) {
        try {
            await this.producer.send({
                topic: this.topics.complianceCheckResult,
                messages: [{
                        key: complianceResult.requestId,
                        value: JSON.stringify({
                            ...complianceResult,
                            from: this.agentId,
                            timestamp: new Date().toISOString()
                        })
                    }]
            });
            this.logger.debug(`📤 Résultat de compliance envoyé: ${complianceResult.requestId}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'envoi du résultat de compliance:', error);
            throw error;
        }
    }
    /**
     * Envoie une alerte de sécurité
     */
    async sendSecurityAlert(alert) {
        try {
            await this.producer.send({
                topic: this.topics.securityAlertOut,
                messages: [{
                        key: alert.id || `alert-${Date.now()}`,
                        value: JSON.stringify({
                            ...alert,
                            from: this.agentId,
                            timestamp: new Date().toISOString()
                        })
                    }]
            });
            this.logger.debug(`📤 Alerte de sécurité envoyée: ${alert.type}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'envoi de l\'alerte:', error);
            throw error;
        }
    }
    /**
     * Envoie un rapport de sécurité
     */
    async sendSecurityReport(report) {
        try {
            await this.producer.send({
                topic: this.topics.securityReportResult,
                messages: [{
                        key: report.requestId,
                        value: JSON.stringify({
                            ...report,
                            from: this.agentId,
                            timestamp: new Date().toISOString()
                        })
                    }]
            });
            this.logger.debug(`📤 Rapport de sécurité envoyé: ${report.type}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'envoi du rapport:', error);
            throw error;
        }
    }
    /**
     * Envoie un heartbeat
     */
    async sendHeartbeat() {
        try {
            const heartbeat = {
                agentId: this.agentId,
                agentType: 'security',
                status: 'online',
                timestamp: new Date().toISOString(),
                capabilities: [
                    'vulnerability-scanning',
                    'compliance-checking',
                    'threat-intelligence',
                    'incident-response',
                    'security-monitoring'
                ],
                metadata: {
                    version: '1.0.0',
                    uptime: process.uptime(),
                    memory: {
                        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
                    }
                }
            };
            await this.producer.send({
                topic: this.topics.agentHeartbeat,
                messages: [{
                        key: this.agentId,
                        value: JSON.stringify(heartbeat)
                    }]
            });
            this.logger.debug('💓 Heartbeat envoyé');
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'envoi du heartbeat:', error);
        }
    }
    /**
     * Démarre le heartbeat périodique
     */
    startHeartbeat() {
        setInterval(async () => {
            if (this.isConnected) {
                await this.sendHeartbeat();
            }
        }, 30000); // Toutes les 30 secondes
    }
    /**
     * Diffuse un message à tous les agents
     */
    async broadcastMessage(message) {
        try {
            await this.producer.send({
                topic: this.topics.agentResponse,
                messages: [{
                        key: 'broadcast',
                        value: JSON.stringify({
                            ...message,
                            from: this.agentId,
                            broadcast: true,
                            timestamp: new Date().toISOString()
                        })
                    }]
            });
            this.logger.debug('📢 Message diffusé:', message.type);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la diffusion:', error);
            throw error;
        }
    }
    /**
     * Vérifie si la connexion est active
     */
    getConnectionStatus() {
        return this.isConnected;
    }
    /**
     * Déconnexion gracieuse
     */
    async disconnect() {
        try {
            this.logger.info('🔌 Déconnexion Kafka Security...');
            // Envoi d'un dernier heartbeat avec statut offline
            await this.producer.send({
                topic: this.topics.agentHeartbeat,
                messages: [{
                        key: this.agentId,
                        value: JSON.stringify({
                            agentId: this.agentId,
                            agentType: 'security',
                            status: 'offline',
                            timestamp: new Date().toISOString()
                        })
                    }]
            });
            // Déconnexion des clients
            await this.consumer.disconnect();
            await this.producer.disconnect();
            this.isConnected = false;
            this.logger.info('✅ Déconnexion Kafka Security terminée');
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la déconnexion Kafka:', error);
        }
    }
}
exports.KafkaCommunication = KafkaCommunication;
//# sourceMappingURL=KafkaCommunication.js.map