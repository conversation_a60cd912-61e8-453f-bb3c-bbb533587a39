import { EventEmitter } from 'events';
import { Logger } from 'winston';
/**
 * Gestionnaire de Communication Kafka pour l'Agent Security
 *
 * Gère la communication synaptique avec les autres agents
 */
export declare class KafkaCommunication extends EventEmitter {
    private logger;
    private kafka;
    private producer;
    private consumer;
    private brokers;
    private agentId;
    private isConnected;
    private readonly topics;
    constructor(logger: Logger, brokers: string, agentId: string);
    /**
     * Initialise la communication Kafka
     */
    initialize(): Promise<void>;
    /**
     * Abonnement aux topics Kafka
     */
    private subscribeToTopics;
    /**
     * Démarre l'écoute des messages
     */
    private startListening;
    /**
     * Traite un message reçu
     */
    private handleMessage;
    /**
     * Vérifie si une instruction du Cortex concerne la sécurité
     */
    private isInstructionForSecurity;
    /**
     * Envoie un message à un agent spécifique
     */
    sendMessage(targetAgent: string, message: any): Promise<void>;
    /**
     * Envoie un résultat de scan de sécurité
     */
    sendScanResult(scanResult: any): Promise<void>;
    /**
     * Envoie un résultat de vérification de compliance
     */
    sendComplianceResult(complianceResult: any): Promise<void>;
    /**
     * Envoie une alerte de sécurité
     */
    sendSecurityAlert(alert: any): Promise<void>;
    /**
     * Envoie un rapport de sécurité
     */
    sendSecurityReport(report: any): Promise<void>;
    /**
     * Envoie un heartbeat
     */
    sendHeartbeat(): Promise<void>;
    /**
     * Démarre le heartbeat périodique
     */
    private startHeartbeat;
    /**
     * Diffuse un message à tous les agents
     */
    broadcastMessage(message: any): Promise<void>;
    /**
     * Vérifie si la connexion est active
     */
    getConnectionStatus(): boolean;
    /**
     * Déconnexion gracieuse
     */
    disconnect(): Promise<void>;
}
//# sourceMappingURL=KafkaCommunication.d.ts.map