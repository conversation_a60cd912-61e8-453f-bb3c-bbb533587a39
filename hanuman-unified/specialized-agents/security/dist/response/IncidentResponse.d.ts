import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { SecurityIncident } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';
/**
 * Système de Réponse aux Incidents
 *
 * Gère la détection, l'analyse et la réponse aux incidents de sécurité
 */
export declare class IncidentResponse extends EventEmitter {
    private logger;
    private memory;
    private communication;
    private isInitialized;
    private activeIncidents;
    private responsePlaybooks;
    constructor(logger: Logger, memory: WeaviateMemory, communication: KafkaCommunication);
    /**
     * Initialise le système de réponse aux incidents
     */
    initialize(): Promise<void>;
    /**
     * Charge les playbooks de réponse
     */
    private loadResponsePlaybooks;
    /**
     * Charge les incidents actifs
     */
    private loadActiveIncidents;
    /**
     * Crée un nouvel incident
     */
    createIncident(incidentData: any): Promise<SecurityIncident>;
    /**
     * Traite un nouvel incident
     */
    handleIncident(incidentData: any): Promise<SecurityIncident>;
    /**
     * Initie la réponse à un incident
     */
    private initiateResponse;
    /**
     * Génère les actions de réponse
     */
    private generateResponseActions;
    /**
     * Exécute les actions automatiques
     */
    private executeAutomaticActions;
    /**
     * Simule l'exécution d'une action
     */
    private simulateActionExecution;
    /**
     * Notifie un incident
     */
    private notifyIncident;
    /**
     * Met à jour le statut d'un incident
     */
    updateIncidentStatus(incidentId: string, status: string, notes?: string): Promise<void>;
    /**
     * Obtient les incidents actifs
     */
    getActiveIncidents(): SecurityIncident[];
    /**
     * Obtient un incident par ID
     */
    getIncident(incidentId: string): SecurityIncident | undefined;
    /**
     * Obtient les statistiques des incidents
     */
    getIncidentStatistics(): any;
    private determineActionScope;
    private assessActionImpact;
    private determineEradicationMethod;
    private extractServiceFromDescription;
    private calculateAverageResponseTime;
    /**
     * Arrêt du système de réponse aux incidents
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=IncidentResponse.d.ts.map