{"version": 3, "file": "SecurityPolicyEngine.js", "sourceRoot": "", "sources": ["../../src/policies/SecurityPolicyEngine.ts"], "names": [], "mappings": ";;;AACA,mCAAsC;AAItC;;;;GAIG;AACH,MAAa,oBAAqB,SAAQ,qBAAY;IAapD,YACE,MAAc,EACd,MAAsB,EACtB,aAAiC;QAEjC,KAAK,EAAE,CAAC;QAbF,aAAQ,GAAgC,IAAI,GAAG,EAAE,CAAC;QAClD,iBAAY,GAA6B,IAAI,GAAG,EAAE,CAAC;QACnD,qBAAgB,GAAiC,IAAI,GAAG,EAAE,CAAC;QAC3D,qBAAgB,GAAiC,IAAI,GAAG,EAAE,CAAC;QAE3D,kBAAa,GAAY,KAAK,CAAC;QAC/B,gBAAW,GAAY,KAAK,CAAC;QAQnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAEnE,uCAAuC;YACvC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAElC,0CAA0C;YAC1C,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAElC,sCAAsC;YACtC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAElC,4CAA4C;YAC5C,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAE1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+DAA+D,EAAE,KAAK,CAAC,CAAC;YAC1F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,UAA+B;QAChD,IAAI,CAAC;YACH,MAAM,MAAM,GAAmB;gBAC7B,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBAC3B,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,IAAI;gBACnC,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,EAAE;gBACvC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,CAAC;gBACV,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,EAAE;aACpC,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACrC,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;YAE/E,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAEpC,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAgC;QACnE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC3C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,aAAa,GAAmB;gBACpC,GAAG,MAAM;gBACT,GAAG,OAAO;gBACV,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,MAAM,CAAC,OAAO,GAAG,CAAC;aAC5B,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC3C,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,aAAa,CAAC,IAAI,MAAM,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC;YAEhG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,aAAa,EAAE,CAAC,CAAC;YAEjE,OAAO,aAAa,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC3C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;YACxD,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC/B,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAEjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAE5D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QAEtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,MAAoB;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,MAAM,CAAC,IAAI,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAEtF,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,UAAU,GAAsB,EAAE,CAAC;YACzC,IAAI,eAAe,GAA8B,OAAO,CAAC;YACzD,IAAI,eAAe,GAA2C,KAAK,CAAC;YAEpE,KAAK,MAAM,MAAM,IAAI,kBAAkB,EAAE,CAAC;gBACxC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAE1E,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACxB,MAAM,SAAS,GAAoB;wBACjC,EAAE,EAAE,IAAI,CAAC,mBAAmB,EAAE;wBAC9B,QAAQ,EAAE,MAAM,CAAC,EAAE;wBACnB,UAAU,EAAE,MAAM,CAAC,IAAI;wBACvB,MAAM;wBACN,aAAa,EAAE,UAAU,CAAC,aAAa;wBACvC,QAAQ,EAAE,MAAM,CAAC,QAAQ;wBACzB,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,OAAO,EAAE,UAAU,CAAC,OAAO;wBAC3B,WAAW,EAAE,UAAU,CAAC,WAAW;qBACpC,CAAC;oBAEF,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC3B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;oBAEnD,uCAAuC;oBACvC,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;wBAC1C,eAAe,GAAG,MAAM,CAAC;oBAC3B,CAAC;yBAAM,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,IAAI,eAAe,KAAK,OAAO,EAAE,CAAC;wBAC/E,eAAe,GAAG,MAAM,CAAC;oBAC3B,CAAC;oBAED,sCAAsC;oBACtC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,CAAC;wBACpF,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC;oBACpC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,MAAM,MAAM,GAA2B;gBACrC,QAAQ,EAAE,eAAe;gBACzB,UAAU;gBACV,kBAAkB,EAAE,kBAAkB,CAAC,MAAM;gBAC7C,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,QAAQ,EAAE,eAAe;gBACzB,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC;aAC1D,CAAC;YAEF,gCAAgC;YAChC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACxD,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;YAElD,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAmC;QACzD,IAAI,CAAC;YACH,MAAM,KAAK,GAAgB;gBACzB,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE;gBAC1B,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,OAAO,EAAE,SAAS,CAAC,OAAO,IAAI,IAAI;gBAClC,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACvC,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAEhE,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAEzC,OAAO,KAAK,CAAC;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YAClF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,MAAoB;QAChD,MAAM,UAAU,GAAqB,EAAE,CAAC;QAExC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,OAAO;gBAAE,SAAS;YAE9B,wBAAwB;YACxB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/C,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC9B,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,MAAoB,EAAE,KAAkB;QAC9D,gCAAgC;QAChC,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,8BAA8B;QAC9B,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YACtF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,gCAAgC;QAChC,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,yBAAyB;QACzB,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACpC,MAAM,eAAe,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;YACnF,IAAI,CAAC,eAAe;gBAAE,OAAO,KAAK,CAAC;QACrC,CAAC;QAED,0CAA0C;QAC1C,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;gBACxE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,QAAgB,EAAE,QAAkB;QACjE,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAC7B,IAAI,OAAO,KAAK,GAAG;gBAAE,OAAO,IAAI,CAAC;YACjC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;YACD,OAAO,QAAQ,KAAK,OAAO,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,SAAe,EAAE,UAA0B;QACvE,MAAM,IAAI,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,2BAA2B;QAE3D,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;YAC5B,IAAI,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;gBAC/E,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1C,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,2BAA2B,CACvC,MAAsB,EACtB,MAAoB;QAEpB,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YAChC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAE3D,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC1B,QAAQ,GAAG,IAAI,CAAC;gBAChB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC5B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAElC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,QAAQ;YACR,aAAa;YACb,OAAO;YACP,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,IAAgB,EAAE,MAAoB;QAC/D,IAAI,CAAC;YACH,wCAAwC;YACxC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,gBAAgB;oBACnB,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAEtD,KAAK,iBAAiB;oBACpB,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAEvD,KAAK,gBAAgB;oBACnB,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAEvD,KAAK,kBAAkB;oBACrB,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAExD,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAE/C;oBACE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,4BAA4B,EAAE,CAAC;YACrE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAClF,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,8BAA8B,EAAE,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,IAAgB,EAAE,MAAoB;QACtE,gEAAgE;QAChE,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC7D,OAAO;oBACL,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE,yBAAyB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;iBAChE,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,sCAAsC,EAAE,CAAC;IAC7E,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,IAAgB,EAAE,MAAoB;QACvE,sEAAsE;QACtE,IAAI,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI,MAAM,CAAC,QAAQ,EAAE,qBAAqB,EAAE,CAAC;YAClF,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yBAAyB,EAAE,CAAC;gBAC/C,OAAO;oBACL,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE,8CAA8C;iBACvD,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,2CAA2C,EAAE,CAAC;IAClF,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,IAAgB,EAAE,MAAoB;QACvE,+DAA+D;QAC/D,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC;YAChE,OAAO;gBACL,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,yCAAyC;aAClD,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,qCAAqC,EAAE,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,IAAgB,EAAE,MAAoB;QACxE,+DAA+D;QAC/D,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC1D,OAAO;oBACL,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE,6BAA6B,MAAM,CAAC,QAAQ,EAAE;iBACvD,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,oCAAoC,EAAE,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAAgB,EAAE,MAAoB;QAC/D,2DAA2D;QAC3D,sEAAsE;QACtE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,+BAA+B,EAAE,CAAC;IACtE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,UAA6B,EAC7B,MAAoB;QAEpB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,2BAA2B;YAC3B,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAElD,eAAe;YACf,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;YAEzC,+BAA+B;YAC/B,IAAI,SAAS,CAAC,QAAQ,KAAK,UAAU,IAAI,SAAS,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;gBACvE,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,SAA0B,EAAE,MAAoB;QAC/E,MAAM,KAAK,GAAG;YACZ,IAAI,EAAE,kBAAkB;YACxB,KAAK,EAAE,2BAA2B,SAAS,CAAC,UAAU,EAAE;YACxD,WAAW,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YACzC,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,WAAW,EAAE,SAAS,CAAC,EAAE;YACzB,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,UAA6B;QAC3D,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,eAAe,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,2BAA2B;IACnE,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB;QACvC,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;QAC3D,OAAO,MAAM,CAAC,QAA+B,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;YACzD,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACxB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,kCAAkC,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAChC,2CAA2C;QAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE,CAAC;YACjD,MAAM,IAAI,CAAC,YAAY,CAAC;gBACtB,IAAI,EAAE,8BAA8B;gBACpC,WAAW,EAAE,wCAAwC;gBACrD,QAAQ,EAAE,gBAAgB;gBAC1B,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,wBAAwB;wBAC5B,IAAI,EAAE,0BAA0B;wBAChC,IAAI,EAAE,gBAAgB;wBACtB,UAAU,EAAE,EAAE,qBAAqB,EAAE,IAAI,EAAE;wBAC3C,WAAW,EAAE,sDAAsD;qBACpE;iBACF;gBACD,WAAW,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE;gBACjD,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,EAAE;aAC5C,CAAC,CAAC;QACL,CAAC;QAED,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,yBAAyB,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,CAAC,YAAY,CAAC;gBACtB,IAAI,EAAE,mCAAmC;gBACzC,WAAW,EAAE,+CAA+C;gBAC5D,QAAQ,EAAE,iBAAiB;gBAC3B,QAAQ,EAAE,UAAU;gBACpB,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,yBAAyB;wBAC7B,IAAI,EAAE,oCAAoC;wBAC1C,IAAI,EAAE,iBAAiB;wBACvB,UAAU,EAAE,EAAE,mBAAmB,EAAE,IAAI,EAAE;wBACzC,WAAW,EAAE,gEAAgE;qBAC9E;iBACF;gBACD,WAAW,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE;gBACjD,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAE,CAAC,kBAAkB,CAAC,EAAE;aACjF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAChC,oDAAoD;QACpD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,EAAE;YAC3C,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,kBAAkB;YACxB,MAAM,EAAE,OAAO;YACf,SAAS,EAAE,IAAI;YACf,oBAAoB,EAAE,IAAI;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,EAAE;YACxC,EAAE,EAAE,cAAc;YAClB,IAAI,EAAE,iCAAiC;YACvC,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,KAAK;YAChB,oBAAoB,EAAE,KAAK;SAC5B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,OAAO,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC1E,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,OAAO,aAAa,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACzC,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACzF,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAEnD,OAAO;YACL,aAAa;YACb,eAAe;YACf,gBAAgB,EAAE,aAAa,GAAG,eAAe;YACjD,eAAe;YACf,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACpD,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,MAAM,MAAM,GAA2B,EAAE,CAAC;QAE1C,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC;YACvD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACrD,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,MAAM,MAAM,GAA2B,EAAE,CAAC;QAE1C,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC;YACvD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;CACF;AApsBD,oDAosBC"}