import { Logger } from 'winston';
import { EventEmitter } from 'events';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';
/**
 * Moteur de Politiques de Sécurité
 *
 * Gère et applique les politiques de sécurité du système
 */
export declare class SecurityPolicyEngine extends EventEmitter {
    private logger;
    private memory;
    private communication;
    private policies;
    private policyGroups;
    private policyViolations;
    private enforcementRules;
    private isInitialized;
    private isEnforcing;
    constructor(logger: Logger, memory: WeaviateMemory, communication: KafkaCommunication);
    /**
     * Initialise le moteur de politiques
     */
    initialize(): Promise<void>;
    /**
     * Crée une nouvelle politique de sécurité
     */
    createPolicy(policyData: CreatePolicyRequest): Promise<SecurityPolicy>;
    /**
     * Met à jour une politique existante
     */
    updatePolicy(policyId: string, updates: Partial<SecurityPolicy>): Promise<SecurityPolicy>;
    /**
     * Supprime une politique
     */
    deletePolicy(policyId: string): Promise<void>;
    /**
     * Évalue une action contre les politiques
     */
    evaluateAction(action: PolicyAction): Promise<PolicyEvaluationResult>;
    /**
     * Crée un groupe de politiques
     */
    createPolicyGroup(groupData: CreatePolicyGroupRequest): Promise<PolicyGroup>;
    /**
     * Récupère les politiques applicables à une action
     */
    private getApplicablePolicies;
    /**
     * Vérifie si une action est dans le scope d'une politique
     */
    private isActionInScope;
    /**
     * Vérifie si une ressource correspond aux patterns
     */
    private matchesResourcePattern;
    /**
     * Vérifie les conditions temporelles
     */
    private matchesTimeConditions;
    /**
     * Évalue une politique contre une action
     */
    private evaluatePolicyAgainstAction;
    /**
     * Évalue une règle spécifique
     */
    private evaluateRule;
    /**
     * Évalue une règle de contrôle d'accès
     */
    private evaluateAccessControlRule;
    /**
     * Évalue une règle de protection des données
     */
    private evaluateDataProtectionRule;
    /**
     * Évalue une règle d'authentification
     */
    private evaluateAuthenticationRule;
    /**
     * Évalue une règle de sécurité réseau
     */
    private evaluateNetworkSecurityRule;
    /**
     * Évalue une règle personnalisée
     */
    private evaluateCustomRule;
    /**
     * Gère les violations de politique
     */
    private handlePolicyViolations;
    /**
     * Envoie une alerte de violation
     */
    private sendViolationAlert;
    /**
     * Génère des recommandations basées sur les violations
     */
    private generateRecommendations;
    /**
     * Récupère le niveau numérique d'une sévérité
     */
    private getSeverityLevel;
    /**
     * Charge les politiques existantes
     */
    private loadExistingPolicies;
    /**
     * Configure les politiques par défaut
     */
    private setupDefaultPolicies;
    /**
     * Charge les règles d'application
     */
    private loadEnforcementRules;
    /**
     * Démarre l'application des politiques
     */
    private startPolicyEnforcement;
    /**
     * Génère un ID de politique
     */
    private generatePolicyId;
    /**
     * Génère un ID de groupe
     */
    private generateGroupId;
    /**
     * Génère un ID de violation
     */
    private generateViolationId;
    /**
     * Récupère les statistiques des politiques
     */
    getPolicyStats(): PolicyStats;
    /**
     * Récupère les violations par catégorie
     */
    private getViolationsByCategory;
    /**
     * Récupère les violations par sévérité
     */
    private getViolationsBySeverity;
    /**
     * Arrêt du moteur de politiques
     */
    shutdown(): Promise<void>;
}
interface SecurityPolicy {
    id: string;
    name: string;
    description: string;
    category: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    enabled: boolean;
    rules: PolicyRule[];
    enforcement: PolicyEnforcement;
    scope: PolicyScope;
    exceptions: PolicyException[];
    createdAt: Date;
    updatedAt: Date;
    version: number;
    metadata: any;
}
interface PolicyRule {
    id: string;
    name: string;
    type: 'access-control' | 'data-protection' | 'authentication' | 'network-security' | 'custom';
    conditions: any;
    remediation?: string;
}
interface PolicyEnforcement {
    action: 'allow' | 'deny' | 'warn' | 'block';
    immediate: boolean;
    notificationRequired?: boolean;
    escalationRequired?: boolean;
}
interface PolicyScope {
    actions?: string[];
    resources?: string[];
    users?: string[];
    roles?: string[];
    timeConditions?: TimeConditions;
}
interface TimeConditions {
    allowedHours?: {
        start: number;
        end: number;
    };
    allowedDays?: number[];
}
interface PolicyException {
    id: string;
    reason: string;
    scope: PolicyScope;
    expiresAt?: Date;
}
interface PolicyGroup {
    id: string;
    name: string;
    description: string;
    policies: string[];
    enabled: boolean;
    priority: number;
    createdAt: Date;
    updatedAt: Date;
}
interface PolicyAction {
    type: string;
    resource: string;
    userId?: string;
    userRoles?: string[];
    sourceIP?: string;
    timestamp: Date;
    metadata?: any;
}
interface PolicyViolation {
    id: string;
    policyId: string;
    policyName: string;
    action: PolicyAction;
    violatedRules: string[];
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: Date;
    details: string[];
    remediation: string[];
}
interface PolicyEvaluationResult {
    decision: 'allow' | 'deny' | 'warn';
    violations: PolicyViolation[];
    applicablePolicies: number;
    evaluationTime: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    recommendations: string[];
}
interface CreatePolicyRequest {
    name: string;
    description: string;
    category: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    enabled?: boolean;
    rules: PolicyRule[];
    enforcement: PolicyEnforcement;
    scope: PolicyScope;
    exceptions?: PolicyException[];
    metadata?: any;
}
interface CreatePolicyGroupRequest {
    name: string;
    description: string;
    policies: string[];
    enabled?: boolean;
    priority: number;
}
interface PolicyStats {
    totalPolicies: number;
    enabledPolicies: number;
    disabledPolicies: number;
    totalViolations: number;
    violationsByCategory: Record<string, number>;
    violationsBySeverity: Record<string, number>;
}
export {};
//# sourceMappingURL=SecurityPolicyEngine.d.ts.map