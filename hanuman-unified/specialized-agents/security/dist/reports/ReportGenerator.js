"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportGenerator = void 0;
/**
 * Générateur de Rapports de Sécurité
 *
 * Génère des rapports détaillés sur l'état de la sécurité
 */
class ReportGenerator {
    constructor(logger, memory) {
        this.isInitialized = false;
        this.logger = logger;
        this.memory = memory;
    }
    /**
     * Initialise le générateur de rapports
     */
    async initialize() {
        try {
            this.logger.info('📊 Initialisation du Générateur de Rapports...');
            this.isInitialized = true;
            this.logger.info('✅ Générateur de Rapports initialisé');
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'initialisation du générateur:', error);
            throw error;
        }
    }
    /**
     * Génère un rapport de vulnérabilités
     */
    async generateVulnerabilityReport(period) {
        try {
            this.logger.info('📋 Génération du rapport de vulnérabilités...');
            const scanResults = await this.memory.getRecentScanResults(100);
            const filteredResults = scanResults.filter(r => r.timestamp >= period.start && r.timestamp <= period.end);
            const allVulnerabilities = filteredResults.flatMap(r => r.vulnerabilities);
            const summary = this.generateVulnerabilitySummary(allVulnerabilities);
            const trends = this.calculateVulnerabilityTrends(filteredResults);
            const recommendations = this.generateVulnerabilityRecommendations(allVulnerabilities);
            const report = {
                id: `vuln-report-${Date.now()}`,
                type: 'vulnerability',
                title: 'Rapport de Vulnérabilités',
                summary: `${allVulnerabilities.length} vulnérabilités analysées sur la période`,
                generatedAt: new Date(),
                period,
                sections: [
                    {
                        title: 'Résumé Exécutif',
                        content: this.generateExecutiveSummary(summary),
                        charts: [
                            {
                                type: 'pie',
                                title: 'Répartition par Sévérité',
                                data: summary.bySeverity
                            }
                        ]
                    },
                    {
                        title: 'Analyse Détaillée',
                        content: this.generateDetailedAnalysis(allVulnerabilities),
                        tables: [
                            {
                                title: 'Top 10 des Vulnérabilités Critiques',
                                headers: ['ID', 'Titre', 'Sévérité', 'Catégorie', 'Statut'],
                                rows: this.getTopVulnerabilities(allVulnerabilities, 10)
                            }
                        ]
                    },
                    {
                        title: 'Tendances',
                        content: this.generateTrendsAnalysis(trends),
                        charts: [
                            {
                                type: 'line',
                                title: 'Évolution des Vulnérabilités',
                                data: trends
                            }
                        ]
                    },
                    {
                        title: 'Recommandations',
                        content: recommendations.join('\n'),
                        priority: 'high'
                    }
                ],
                metadata: {
                    scanCount: filteredResults.length,
                    vulnerabilityCount: allVulnerabilities.length,
                    period: period,
                    generatedBy: 'SecurityAgent'
                }
            };
            this.logger.info(`✅ Rapport de vulnérabilités généré: ${allVulnerabilities.length} vulnérabilités`);
            return report;
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la génération du rapport de vulnérabilités:', error);
            throw error;
        }
    }
    /**
     * Génère un rapport de compliance
     */
    async generateComplianceReport(framework) {
        try {
            this.logger.info(`📋 Génération du rapport de compliance ${framework}...`);
            // Simulation de données de compliance
            const complianceResults = [
                {
                    frameworkName: framework,
                    controlId: 'AC-1',
                    status: 'compliant',
                    score: 95,
                    findings: [],
                    evidence: [],
                    lastChecked: new Date()
                }
            ];
            const summary = this.generateComplianceSummary(complianceResults);
            const recommendations = this.generateComplianceRecommendations(complianceResults);
            const report = {
                id: `compliance-report-${Date.now()}`,
                type: 'compliance',
                title: `Rapport de Compliance ${framework}`,
                summary: `Score de compliance: ${summary.overallScore}%`,
                generatedAt: new Date(),
                period: { start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), end: new Date() },
                sections: [
                    {
                        title: 'Score de Compliance',
                        content: `Score global: ${summary.overallScore}%\nContrôles conformes: ${summary.compliantControls}/${summary.totalControls}`,
                        charts: [
                            {
                                type: 'gauge',
                                title: 'Score de Compliance',
                                data: { value: summary.overallScore, max: 100 }
                            }
                        ]
                    },
                    {
                        title: 'Contrôles Non-Conformes',
                        content: this.generateNonCompliantControlsAnalysis(complianceResults),
                        tables: [
                            {
                                title: 'Contrôles à Corriger',
                                headers: ['Contrôle', 'Statut', 'Score', 'Dernière Vérification'],
                                rows: this.getNonCompliantControls(complianceResults)
                            }
                        ]
                    },
                    {
                        title: 'Plan d\'Action',
                        content: recommendations.join('\n'),
                        priority: 'medium'
                    }
                ],
                metadata: {
                    framework: framework,
                    controlsCount: complianceResults.length,
                    complianceScore: summary.overallScore,
                    generatedBy: 'SecurityAgent'
                }
            };
            this.logger.info(`✅ Rapport de compliance généré: ${summary.overallScore}% de conformité`);
            return report;
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la génération du rapport de compliance:', error);
            throw error;
        }
    }
    /**
     * Génère un rapport d'intelligence des menaces
     */
    async generateThreatIntelligenceReport() {
        try {
            this.logger.info('📋 Génération du rapport d\'intelligence des menaces...');
            const indicators = await this.memory.getThreatIndicators(1000);
            const recentIndicators = indicators.filter(i => Date.now() - i.lastSeen.getTime() < 7 * 24 * 60 * 60 * 1000 // 7 jours
            );
            const summary = this.generateThreatSummary(recentIndicators);
            const trends = this.calculateThreatTrends(indicators);
            const report = {
                id: `threat-intel-report-${Date.now()}`,
                type: 'threat-intelligence',
                title: 'Rapport d\'Intelligence des Menaces',
                summary: `${recentIndicators.length} indicateurs actifs détectés`,
                generatedAt: new Date(),
                period: { start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), end: new Date() },
                sections: [
                    {
                        title: 'Paysage des Menaces',
                        content: this.generateThreatLandscape(summary),
                        charts: [
                            {
                                type: 'bar',
                                title: 'Indicateurs par Type',
                                data: summary.byType
                            }
                        ]
                    },
                    {
                        title: 'Menaces Émergentes',
                        content: this.generateEmergingThreats(recentIndicators),
                        tables: [
                            {
                                title: 'Nouveaux Indicateurs',
                                headers: ['Type', 'Valeur', 'Niveau', 'Source', 'Première Détection'],
                                rows: this.getRecentIndicators(recentIndicators, 20)
                            }
                        ]
                    },
                    {
                        title: 'Analyse des Tendances',
                        content: this.generateThreatTrendsAnalysis(trends),
                        charts: [
                            {
                                type: 'line',
                                title: 'Évolution des Menaces',
                                data: trends
                            }
                        ]
                    }
                ],
                metadata: {
                    indicatorsCount: recentIndicators.length,
                    totalIndicators: indicators.length,
                    period: { start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), end: new Date() },
                    generatedBy: 'SecurityAgent'
                }
            };
            this.logger.info(`✅ Rapport d'intelligence des menaces généré: ${recentIndicators.length} indicateurs`);
            return report;
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la génération du rapport de menaces:', error);
            throw error;
        }
    }
    /**
     * Génère un rapport générique
     */
    async generateReport(type, period, scope, configuration) {
        switch (type) {
            case 'vulnerability':
                return this.generateVulnerabilityReport(period);
            case 'compliance':
                return this.generateComplianceReport(configuration.framework || 'ISO27001');
            case 'threat-intelligence':
                return this.generateThreatIntelligenceReport();
            default:
                throw new Error(`Type de rapport non supporté: ${type}`);
        }
    }
    /**
     * Génère les métriques de sécurité
     */
    async generateSecurityMetrics() {
        try {
            this.logger.debug('📈 Génération des métriques de sécurité...');
            const metrics = await this.memory.getSecurityMetrics();
            this.logger.debug('✅ Métriques de sécurité générées');
            return metrics;
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la génération des métriques:', error);
            throw error;
        }
    }
    /**
     * Génère les métriques (alias pour generateSecurityMetrics)
     */
    async generateMetrics() {
        return this.generateSecurityMetrics();
    }
    // Méthodes utilitaires pour la génération de rapports
    generateVulnerabilitySummary(vulnerabilities) {
        const bySeverity = vulnerabilities.reduce((acc, vuln) => {
            acc[vuln.severity] = (acc[vuln.severity] || 0) + 1;
            return acc;
        }, {});
        const byCategory = vulnerabilities.reduce((acc, vuln) => {
            acc[vuln.category] = (acc[vuln.category] || 0) + 1;
            return acc;
        }, {});
        return {
            total: vulnerabilities.length,
            bySeverity,
            byCategory,
            riskScore: this.calculateRiskScore(vulnerabilities)
        };
    }
    generateComplianceSummary(results) {
        const compliantControls = results.filter(r => r.status === 'compliant').length;
        const totalControls = results.length;
        const overallScore = totalControls > 0 ? Math.round((compliantControls / totalControls) * 100) : 0;
        return {
            overallScore,
            compliantControls,
            totalControls,
            nonCompliantControls: totalControls - compliantControls
        };
    }
    generateThreatSummary(indicators) {
        const byType = indicators.reduce((acc, indicator) => {
            acc[indicator.type] = (acc[indicator.type] || 0) + 1;
            return acc;
        }, {});
        const byThreatLevel = indicators.reduce((acc, indicator) => {
            acc[indicator.threatLevel] = (acc[indicator.threatLevel] || 0) + 1;
            return acc;
        }, {});
        return {
            total: indicators.length,
            byType,
            byThreatLevel,
            averageConfidence: indicators.reduce((sum, i) => sum + i.confidence, 0) / indicators.length
        };
    }
    calculateRiskScore(vulnerabilities) {
        const weights = { critical: 10, high: 5, medium: 2, low: 1, info: 0.1 };
        const totalWeight = vulnerabilities.reduce((sum, vuln) => {
            return sum + (weights[vuln.severity] || 0);
        }, 0);
        return Math.min(100, totalWeight);
    }
    calculateVulnerabilityTrends(scanResults) {
        // Simulation de calcul de tendances
        return scanResults.map(result => ({
            date: result.timestamp,
            vulnerabilities: result.vulnerabilities.length,
            critical: result.vulnerabilities.filter(v => v.severity === 'critical').length,
            high: result.vulnerabilities.filter(v => v.severity === 'high').length
        }));
    }
    calculateThreatTrends(indicators) {
        // Simulation de calcul de tendances des menaces
        const grouped = indicators.reduce((acc, indicator) => {
            const date = indicator.lastSeen.toISOString().split('T')[0];
            acc[date] = (acc[date] || 0) + 1;
            return acc;
        }, {});
        return Object.entries(grouped).map(([date, count]) => ({
            date: new Date(date),
            indicators: count
        }));
    }
    generateExecutiveSummary(summary) {
        return `Au cours de la période analysée, ${summary.total} vulnérabilités ont été identifiées avec un score de risque de ${summary.riskScore}/100. La répartition par sévérité montre ${summary.bySeverity.critical || 0} vulnérabilités critiques, ${summary.bySeverity.high || 0} de haute sévérité, ${summary.bySeverity.medium || 0} de sévérité moyenne et ${summary.bySeverity.low || 0} de faible sévérité.`;
    }
    generateDetailedAnalysis(vulnerabilities) {
        const categories = Object.keys(vulnerabilities.reduce((acc, vuln) => {
            acc[vuln.category] = true;
            return acc;
        }, {}));
        return `L'analyse détaillée révèle des vulnérabilités dans ${categories.length} catégories principales : ${categories.join(', ')}. Les vulnérabilités critiques nécessitent une attention immédiate pour réduire l'exposition aux risques.`;
    }
    generateTrendsAnalysis(trends) {
        if (trends.length < 2)
            return 'Données insuffisantes pour l\'analyse des tendances.';
        const latest = trends[trends.length - 1];
        const previous = trends[trends.length - 2];
        const change = latest.vulnerabilities - previous.vulnerabilities;
        return `Les tendances montrent ${change >= 0 ? 'une augmentation' : 'une diminution'} de ${Math.abs(change)} vulnérabilités par rapport à la période précédente.`;
    }
    generateVulnerabilityRecommendations(vulnerabilities) {
        const recommendations = [
            'Prioriser la correction des vulnérabilités critiques et de haute sévérité',
            'Mettre en place un processus de gestion des vulnérabilités automatisé',
            'Effectuer des scans de sécurité réguliers',
            'Former les équipes de développement aux bonnes pratiques de sécurité'
        ];
        const criticalCount = vulnerabilities.filter(v => v.severity === 'critical').length;
        if (criticalCount > 0) {
            recommendations.unshift(`Traiter immédiatement les ${criticalCount} vulnérabilités critiques identifiées`);
        }
        return recommendations;
    }
    generateComplianceRecommendations(results) {
        const nonCompliant = results.filter(r => r.status === 'non-compliant');
        const recommendations = [
            'Réviser et mettre à jour les politiques de sécurité',
            'Mettre en place des contrôles automatisés',
            'Former le personnel aux exigences de compliance',
            'Effectuer des audits réguliers'
        ];
        if (nonCompliant.length > 0) {
            recommendations.unshift(`Corriger les ${nonCompliant.length} contrôles non-conformes identifiés`);
        }
        return recommendations;
    }
    generateThreatLandscape(summary) {
        return `Le paysage des menaces actuel comprend ${summary.total} indicateurs avec une confiance moyenne de ${Math.round(summary.averageConfidence)}%. Les types d'indicateurs les plus fréquents sont les adresses IP (${summary.byType['ip-address'] || 0}), les domaines (${summary.byType.domain || 0}) et les hashes de fichiers (${summary.byType['file-hash'] || 0}).`;
    }
    generateEmergingThreats(indicators) {
        const recent = indicators.filter(i => Date.now() - i.firstSeen.getTime() < 24 * 60 * 60 * 1000);
        return `${recent.length} nouveaux indicateurs de menace ont été détectés dans les dernières 24 heures, nécessitant une surveillance accrue.`;
    }
    generateThreatTrendsAnalysis(trends) {
        if (trends.length < 2)
            return 'Données insuffisantes pour l\'analyse des tendances.';
        const totalRecent = trends.slice(-7).reduce((sum, t) => sum + t.indicators, 0);
        const avgDaily = Math.round(totalRecent / 7);
        return `En moyenne, ${avgDaily} nouveaux indicateurs de menace sont détectés quotidiennement.`;
    }
    generateNonCompliantControlsAnalysis(results) {
        const nonCompliant = results.filter(r => r.status === 'non-compliant');
        return `${nonCompliant.length} contrôles nécessitent une attention particulière pour améliorer le score de compliance global.`;
    }
    getTopVulnerabilities(vulnerabilities, limit) {
        return vulnerabilities
            .filter(v => v.severity === 'critical' || v.severity === 'high')
            .slice(0, limit)
            .map(v => [v.id, v.title, v.severity, v.category, v.status]);
    }
    getNonCompliantControls(results) {
        return results
            .filter(r => r.status === 'non-compliant')
            .map(r => [r.controlId, r.status, r.score, r.lastChecked.toLocaleDateString()]);
    }
    getRecentIndicators(indicators, limit) {
        return indicators
            .sort((a, b) => b.firstSeen.getTime() - a.firstSeen.getTime())
            .slice(0, limit)
            .map(i => [i.type, i.value.substring(0, 50), i.threatLevel, i.source, i.firstSeen.toLocaleDateString()]);
    }
    /**
     * Arrêt du générateur de rapports
     */
    async shutdown() {
        this.logger.info('🛑 Arrêt du Générateur de Rapports...');
        this.isInitialized = false;
    }
}
exports.ReportGenerator = ReportGenerator;
//# sourceMappingURL=ReportGenerator.js.map