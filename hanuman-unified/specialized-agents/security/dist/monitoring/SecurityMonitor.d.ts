import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { SecurityMonitoringConfig, SecurityMetrics } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Moniteur de Sécurité
 *
 * Surveille les événements de sécurité en temps réel et génère des alertes
 */
export declare class SecurityMonitor extends EventEmitter {
    private logger;
    private memory;
    private config;
    private isRunning;
    private monitoringIntervals;
    private alertQueue;
    private metrics;
    constructor(config: SecurityMonitoringConfig, logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le monitoring de sécurité
     */
    initialize(): Promise<void>;
    /**
     * Démarre le monitoring de sécurité
     */
    start(): Promise<void>;
    /**
     * Démarre le monitoring en temps réel
     */
    private startRealTimeMonitoring;
    /**
     * Vérifie les nouvelles vulnérabilités
     */
    private checkForNewVulnerabilities;
    /**
     * Vérifie les nouvelles menaces
     */
    private checkForNewThreats;
    /**
     * Vérifie les incidents actifs
     */
    private checkForActiveIncidents;
    /**
     * Collecte les métriques système
     */
    private collectSystemMetrics;
    /**
     * Génère une alerte de sécurité
     */
    private generateAlert;
    /**
     * Démarre le processeur d'alertes
     */
    private startAlertProcessor;
    /**
     * Traite une alerte
     */
    private processAlert;
    /**
     * Évalue une règle d'alerte
     */
    private evaluateAlertRule;
    /**
     * Envoie une alerte via un canal
     */
    private sendAlertToChannel;
    /**
     * Démarre les vérifications périodiques
     */
    private startPeriodicChecks;
    /**
     * Effectue une vérification de santé
     */
    private performHealthCheck;
    /**
     * Démarre la collecte de métriques
     */
    private startMetricsCollection;
    /**
     * Collecte et exporte les métriques
     */
    private collectAndExportMetrics;
    /**
     * Exporte les métriques
     */
    private exportMetrics;
    private initializeMetrics;
    private updateMetrics;
    private checkSystemThresholds;
    private getActiveConnectionsCount;
    /**
     * Obtient les métriques actuelles
     */
    getMetrics(): SecurityMetrics;
    /**
     * Obtient le statut du monitoring
     */
    getStatus(): any;
    /**
     * Envoie une alerte
     */
    sendAlert(alert: any): Promise<void>;
    /**
     * Arrête le monitoring
     */
    stop(): Promise<void>;
    /**
     * Arrêt du monitoring (alias pour stop)
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=SecurityMonitor.d.ts.map