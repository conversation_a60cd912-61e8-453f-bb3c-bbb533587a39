import { Logger } from 'winston';
import { SecurityScanRequest, SecurityScanResult, SecurityIncident, ThreatIndicator } from '../types';
/**
 * Gestionnaire de Mémoire Weaviate pour l'Agent Security
 *
 * Stockage et récupération des données de sécurité dans Weaviate
 */
export declare class WeaviateMemory {
    private logger;
    private client;
    private url;
    private isConnected;
    constructor(logger: Logger, weaviateUrl: string);
    /**
     * Initialise la connexion Weaviate
     */
    initialize(): Promise<void>;
    /**
     * Crée les schémas Weaviate pour les données de sécurité
     */
    private createSchemas;
    /**
     * Stocke une demande de scan
     */
    storeScanRequest(request: SecurityScanRequest): Promise<void>;
    /**
     * Stocke un résultat de scan
     */
    storeScanResult(result: SecurityScanResult): Promise<void>;
    /**
     * Stocke un incident de sécurité
     */
    storeSecurityIncident(incident: SecurityIncident): Promise<void>;
    /**
     * Stocke un indicateur de menace
     */
    storeThreatIndicator(indicator: ThreatIndicator): Promise<void>;
    /**
     * Stocke une alerte de sécurité
     */
    storeSecurityAlert(alert: any): Promise<void>;
    /**
     * Récupère les indicateurs de menace
     */
    getThreatIndicators(limit?: number): Promise<ThreatIndicator[]>;
    /**
     * Récupère les incidents actifs
     */
    getActiveIncidents(): Promise<SecurityIncident[]>;
    /**
     * Récupère les résultats de scan récents
     */
    getRecentScanResults(limit?: number): Promise<SecurityScanResult[]>;
    /**
     * Recherche d'indicateurs de menace par valeur
     */
    searchThreatIndicators(value: string): Promise<ThreatIndicator[]>;
    /**
     * Récupère les métriques de sécurité
     */
    getSecurityMetrics(): Promise<any>;
    private calculateVulnerabilityMetrics;
    private calculateIncidentMetrics;
    private calculateThreatMetrics;
    /**
     * Méthode générique pour stocker des données
     */
    store(key: string, data: any): Promise<void>;
    /**
     * Méthode générique pour récupérer des données
     */
    retrieve(key: string): Promise<any>;
    /**
     * Vérifie si la connexion est active
     */
    getConnectionStatus(): boolean;
    /**
     * Méthode générique pour stocker des données
     */
    private storeData;
    /**
     * Stocke un log d'accès
     */
    storeAccessLog(accessLog: any): Promise<void>;
    /**
     * Stocke un log d'audit
     */
    storeAuditLog(auditLog: any): Promise<void>;
    /**
     * Stocke un rapport d'audit
     */
    storeAuditReport(report: any): Promise<void>;
    /**
     * Récupère les logs d'audit
     */
    getAuditLogs(): Promise<any[]>;
    /**
     * Stocke les résultats de compliance
     */
    storeComplianceResults(scanId: string, results: any): Promise<void>;
    /**
     * Stocke une politique de sécurité
     */
    storeSecurityPolicy(policy: any): Promise<void>;
    /**
     * Supprime une politique de sécurité
     */
    deleteSecurityPolicy(policyId: string): Promise<void>;
    /**
     * Stocke un groupe de politiques
     */
    storePolicyGroup(group: any): Promise<void>;
    /**
     * Stocke une violation de politique
     */
    storePolicyViolation(violation: any): Promise<void>;
    /**
     * Récupère les politiques de sécurité
     */
    getSecurityPolicies(): Promise<any[]>;
    /**
     * Alias pour close()
     */
    disconnect(): Promise<void>;
    /**
     * Ferme la connexion
     */
    close(): Promise<void>;
}
//# sourceMappingURL=WeaviateMemory.d.ts.map