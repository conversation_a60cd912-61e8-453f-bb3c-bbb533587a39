import { SecurityAgent } from './core/SecurityAgent';
import { WeaviateMemory } from './memory/WeaviateMemory';
import { KafkaCommunication } from './communication/KafkaCommunication';
declare const app: import("express-serve-static-core").Express;
declare let securityAgent: SecurityAgent;
declare let memory: WeaviateMemory;
declare let communication: KafkaCommunication;
export { app, securityAgent, memory, communication };
//# sourceMappingURL=index.d.ts.map