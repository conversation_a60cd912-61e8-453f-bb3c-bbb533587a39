#!/usr/bin/env ts-node

import { SecurityAgent } from '../src/core/SecurityAgent';
import { SecurityConfig } from '../src/types';
import winston from 'winston';

/**
 * Script de démonstration de l'Agent Security
 * 
 * Démontre toutes les fonctionnalités de sécurité avancées
 */
class SecurityDemo {
  private securityAgent: SecurityAgent;
  private logger: winston.Logger;

  constructor() {
    // Configuration du logger
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.colorize(),
        winston.format.printf(({ timestamp, level, message }) => {
          return `${timestamp} [${level}] ${message}`;
        })
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'security-demo.log' })
      ]
    });

    // Configuration de l'Agent Security
    const config: SecurityConfig = {
      agent: {
        id: 'demo-security-agent',
        name: 'Demo Security Agent',
        version: '1.0.0'
      },
      scanning: {
        enabled: true,
        interval: 3600,
        targets: ['http://localhost:3000', 'http://localhost:8080'],
        depth: 'deep',
        concurrent: 3
      },
      compliance: {
        frameworks: [
          {
            name: 'OWASP Top 10',
            version: '2021',
            enabled: true,
            controls: []
          },
          {
            name: 'CIS Controls',
            version: '8.0',
            enabled: true,
            controls: []
          }
        ],
        customPolicies: []
      },
      monitoring: {
        enabled: true,
        interval: 60,
        alertThresholds: {
          criticalVulnerabilities: 1,
          highVulnerabilities: 3,
          failedLogins: 5
        }
      },
      reporting: {
        enabled: true,
        schedule: 'daily',
        recipients: ['<EMAIL>'],
        formats: ['json', 'pdf']
      }
    };

    // Mocks pour la démonstration
    const mockMemory = {
      initialize: async () => {},
      storeVulnerability: async () => {},
      getVulnerabilities: async () => [],
      storeComplianceResults: async () => {},
      storeAuditLog: async () => {},
      getAuditLogs: async () => [],
      storeEncryptionKey: async () => {},
      getEncryptionKeys: async () => [],
      storeSecurityPolicy: async () => {},
      getSecurityPolicies: async () => [],
      storePolicyViolation: async () => {},
      storeAuditReport: async () => {},
      storeAccessLog: async () => {},
      storeKeyRotationHistory: async () => {},
      storePolicyGroup: async () => {},
      deleteSecurityPolicy: async () => {},
      shutdown: async () => {}
    } as any;

    const mockCommunication = {
      initialize: async () => {},
      sendMessage: async (topic: string, message: any) => {
        this.logger.info(`📡 Message envoyé sur ${topic}: ${JSON.stringify(message, null, 2)}`);
      },
      subscribe: async () => {},
      shutdown: async () => {}
    } as any;

    this.securityAgent = new SecurityAgent(config, this.logger, mockMemory, mockCommunication);
  }

  /**
   * Lance la démonstration complète
   */
  async runDemo(): Promise<void> {
    try {
      this.logger.info('🚀 Démarrage de la démonstration de l\'Agent Security');

      // Initialisation
      await this.initializeAgent();

      // Démonstration des fonctionnalités
      await this.demoVulnerabilityScanning();
      await this.demoAccessControl();
      await this.demoEncryption();
      await this.demoSecurityPolicies();
      await this.demoAuditSystem();
      await this.demoComplianceChecking();

      // Métriques finales
      await this.showFinalMetrics();

    } catch (error) {
      this.logger.error('❌ Erreur lors de la démonstration:', error);
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Initialise l'agent de sécurité
   */
  private async initializeAgent(): Promise<void> {
    this.logger.info('🔧 Initialisation de l\'Agent Security...');
    await this.securityAgent.initialize();
    this.logger.info('✅ Agent Security initialisé avec succès');
  }

  /**
   * Démontre le scan de vulnérabilités
   */
  private async demoVulnerabilityScanning(): Promise<void> {
    this.logger.info('\n🔍 === DÉMONSTRATION: SCAN DE VULNÉRABILITÉS ===');

    try {
      const target = 'http://localhost:3000';
      this.logger.info(`🎯 Scan de vulnérabilités sur ${target}...`);

      const scanResult = await this.securityAgent.scanVulnerabilities(target, {
        depth: 'medium',
        timeout: 30000
      });

      this.logger.info(`📊 Scan terminé: ${scanResult.vulnerabilities.length} vulnérabilités trouvées`);
      
      if (scanResult.vulnerabilities.length > 0) {
        scanResult.vulnerabilities.slice(0, 3).forEach((vuln, index) => {
          this.logger.info(`  ${index + 1}. ${vuln.title} (${vuln.severity})`);
        });
      }

    } catch (error) {
      this.logger.warn('⚠️ Erreur lors du scan (normal en démonstration):', error.message);
    }
  }

  /**
   * Démontre le contrôle d'accès
   */
  private async demoAccessControl(): Promise<void> {
    this.logger.info('\n🔐 === DÉMONSTRATION: CONTRÔLE D\'ACCÈS ===');

    // Création d'une session utilisateur
    const userId = 'demo-user';
    const userAttributes = {
      roles: ['user', 'developer'],
      department: 'IT',
      ipAddress: '*************',
      userAgent: 'Demo Browser'
    };

    this.logger.info(`👤 Création de session pour ${userId}...`);
    const session = await this.securityAgent.createUserSession(userId, userAttributes, 'password');
    this.logger.info(`✅ Session créée: ${session.id}`);

    // Test d'accès autorisé
    this.logger.info('🔍 Test d\'accès autorisé...');
    const allowedAccess = await this.securityAgent.checkAccess(userId, '/api/users', 'read');
    this.logger.info(`📝 Résultat: ${allowedAccess.allowed ? 'AUTORISÉ' : 'REFUSÉ'} - ${allowedAccess.reason}`);

    // Test d'accès refusé
    this.logger.info('🔍 Test d\'accès refusé...');
    const deniedAccess = await this.securityAgent.checkAccess(userId, '/admin/system', 'delete');
    this.logger.info(`📝 Résultat: ${deniedAccess.allowed ? 'AUTORISÉ' : 'REFUSÉ'} - ${deniedAccess.reason}`);

    // Révocation de session
    this.logger.info('🚫 Révocation de la session...');
    await this.securityAgent.revokeUserSession(userId, 'Fin de démonstration');
    this.logger.info('✅ Session révoquée');
  }

  /**
   * Démontre le chiffrement
   */
  private async demoEncryption(): Promise<void> {
    this.logger.info('\n🔒 === DÉMONSTRATION: CHIFFREMENT ===');

    // Génération d'une clé
    const keyId = 'demo-key';
    this.logger.info(`🔑 Génération de la clé ${keyId}...`);
    const key = await this.securityAgent.generateEncryptionKey(keyId, 'aes-256-gcm');
    this.logger.info(`✅ Clé générée: ${key.id} (${key.algorithm})`);

    // Chiffrement de données
    const sensitiveData = 'Données confidentielles de démonstration';
    this.logger.info('🔐 Chiffrement des données...');
    const encryptionResult = await this.securityAgent.encryptData(sensitiveData, keyId);
    this.logger.info(`✅ Données chiffrées (${encryptionResult.processingTime}ms)`);

    // Déchiffrement
    this.logger.info('🔓 Déchiffrement des données...');
    const decryptedData = await this.securityAgent.decryptData(
      encryptionResult.encryptedData,
      encryptionResult.keyId,
      encryptionResult.algorithm,
      encryptionResult.iv
    );
    this.logger.info(`✅ Données déchiffrées: "${decryptedData}"`);

    // Rotation de clé
    this.logger.info('🔄 Rotation de la clé...');
    const newKey = await this.securityAgent.rotateEncryptionKey(keyId);
    this.logger.info(`✅ Clé mise à jour: version ${newKey.version}`);
  }

  /**
   * Démontre les politiques de sécurité
   */
  private async demoSecurityPolicies(): Promise<void> {
    this.logger.info('\n📋 === DÉMONSTRATION: POLITIQUES DE SÉCURITÉ ===');

    // Création d'une politique
    const policyData = {
      name: 'Politique de démonstration',
      description: 'Politique de sécurité pour la démonstration',
      category: 'access-control',
      severity: 'high' as const,
      rules: [
        {
          id: 'demo-rule',
          name: 'Règle de démonstration',
          type: 'access-control' as const,
          conditions: { requireMFA: true },
          remediation: 'Activez l\'authentification multi-facteurs'
        }
      ],
      enforcement: { action: 'warn' as const, immediate: false },
      scope: { actions: ['write', 'delete'], resources: ['/api/sensitive/*'] }
    };

    this.logger.info('📜 Création d\'une politique de sécurité...');
    const policy = await this.securityAgent.createSecurityPolicy(policyData);
    this.logger.info(`✅ Politique créée: ${policy.name} (${policy.id})`);

    // Évaluation d'une action
    const action = {
      type: 'write',
      resource: '/api/sensitive/data',
      userId: 'demo-user',
      timestamp: new Date(),
      metadata: { mfaVerified: false }
    };

    this.logger.info('⚖️ Évaluation d\'une action contre les politiques...');
    const evaluation = await this.securityAgent.evaluateAction(action);
    this.logger.info(`📊 Résultat: ${evaluation.decision.toUpperCase()}`);
    this.logger.info(`📈 Violations: ${evaluation.violations.length}`);
    
    if (evaluation.violations.length > 0) {
      evaluation.violations.forEach((violation, index) => {
        this.logger.info(`  ${index + 1}. ${violation.policyName}: ${violation.details.join(', ')}`);
      });
    }
  }

  /**
   * Démontre le système d'audit
   */
  private async demoAuditSystem(): Promise<void> {
    this.logger.info('\n📝 === DÉMONSTRATION: SYSTÈME D\'AUDIT ===');

    // Enregistrement d'événements d'audit
    const events = [
      {
        type: 'access',
        category: 'authentication',
        description: 'Connexion utilisateur réussie',
        userId: 'demo-user',
        resource: '/login',
        action: 'authenticate',
        result: 'success',
        ipAddress: '*************'
      },
      {
        type: 'security-incident',
        category: 'security',
        description: 'Tentative d\'accès non autorisé détectée',
        resource: '/admin/config',
        action: 'access',
        result: 'blocked',
        metadata: { severity: 'high', source: 'firewall' }
      },
      {
        type: 'configuration-change',
        category: 'system',
        description: 'Modification de la configuration de sécurité',
        userId: 'admin-user',
        resource: 'security-config',
        action: 'modify',
        result: 'success'
      }
    ];

    this.logger.info('📋 Enregistrement d\'événements d\'audit...');
    for (const event of events) {
      await this.securityAgent.logAuditEvent(event);
      this.logger.info(`  ✅ ${event.type}: ${event.description}`);
    }

    // Génération d'un rapport d'audit
    this.logger.info('📊 Génération d\'un rapport d\'audit...');
    const report = await this.securityAgent.generateAuditReport('daily', {
      start: new Date(Date.now() - 24 * 60 * 60 * 1000),
      end: new Date()
    });

    this.logger.info(`📈 Rapport généré: ${report.summary.totalEvents} événements`);
    this.logger.info(`📊 Types d'événements: ${Object.keys(report.summary.eventsByType).join(', ')}`);
  }

  /**
   * Démontre la vérification de conformité
   */
  private async demoComplianceChecking(): Promise<void> {
    this.logger.info('\n✅ === DÉMONSTRATION: VÉRIFICATION DE CONFORMITÉ ===');

    // Simulation d'un résultat de scan pour la conformité
    const mockScanResult = {
      id: 'demo-scan',
      target: 'http://localhost:3000',
      vulnerabilities: [
        {
          id: 'vuln-1',
          title: 'Weak Password Policy',
          severity: 'medium',
          category: 'authentication',
          remediation: { description: 'Implement stronger password requirements' }
        }
      ],
      timestamp: new Date()
    };

    this.logger.info('🔍 Vérification de conformité OWASP Top 10...');
    const complianceResults = await this.securityAgent.checkCompliance(mockScanResult);
    
    this.logger.info(`📋 Résultats de conformité: ${complianceResults.length} contrôles vérifiés`);
    
    complianceResults.slice(0, 3).forEach((result, index) => {
      this.logger.info(`  ${index + 1}. ${result.controlId}: ${result.status.toUpperCase()} (Score: ${result.score}%)`);
    });
  }

  /**
   * Affiche les métriques finales
   */
  private async showFinalMetrics(): Promise<void> {
    this.logger.info('\n📊 === MÉTRIQUES FINALES ===');

    const status = await this.securityAgent.getStatus();
    this.logger.info(`🔧 Statut de l'agent: ${status.isRunning ? 'ACTIF' : 'INACTIF'}`);
    this.logger.info(`⏱️ Temps de fonctionnement: ${Math.round(status.uptime)}s`);

    const metrics = await this.securityAgent.getSecurityMetrics();
    this.logger.info('📈 Métriques de sécurité générées avec succès');
  }

  /**
   * Nettoie les ressources
   */
  private async cleanup(): Promise<void> {
    this.logger.info('\n🧹 Nettoyage des ressources...');
    await this.securityAgent.shutdown();
    this.logger.info('✅ Démonstration terminée avec succès');
  }
}

// Exécution de la démonstration
if (require.main === module) {
  const demo = new SecurityDemo();
  demo.runDemo().catch(console.error);
}

export { SecurityDemo };
