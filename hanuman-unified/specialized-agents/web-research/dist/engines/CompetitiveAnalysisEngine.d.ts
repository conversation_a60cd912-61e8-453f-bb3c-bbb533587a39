import { Logger } from 'winston';
import { AgentConfig, SearchResult, CompetitorData, CompetitorUXAnalysis } from '../types';
/**
 * Moteur d'analyse concurrentielle
 */
export declare class CompetitiveAnalysisEngine {
    private logger;
    private config;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise le moteur
     */
    initialize(): Promise<void>;
    /**
     * Analyse les concurrents à partir des résultats de recherche
     */
    analyzeCompetitors(searchResults: SearchResult[]): Promise<CompetitorData[]>;
    /**
     * Analyse un concurrent spécifique
     */
    private analyzeCompetitor;
    /**
     * Analyse UX d'un concurrent
     */
    analyzeCompetitorUX(competitor: string): Promise<CompetitorUXAnalysis>;
    /**
     * Analyse les patterns UI
     */
    analyzeUIPatterns(designSources: SearchResult[]): Promise<any>;
    /**
     * Groupe les résultats par domaine
     */
    private groupResultsByDomain;
    /**
     * Extrait le nom de l'entreprise
     */
    private extractCompanyName;
    /**
     * Analyse les forces
     */
    private analyzeStrengths;
    /**
     * Analyse les faiblesses
     */
    private analyzeWeaknesses;
    /**
     * Détermine la position sur le marché
     */
    private determineMarketPosition;
    /**
     * Génère une description
     */
    private generateDescription;
    /**
     * Analyse la présence sociale
     */
    private analyzeSocialPresence;
    /**
     * Calcule le score UX
     */
    private calculateUXScore;
    /**
     * Identifie les forces UX
     */
    private identifyUXStrengths;
    /**
     * Identifie les faiblesses UX
     */
    private identifyUXWeaknesses;
    /**
     * Identifie les opportunités UX
     */
    private identifyUXOpportunities;
    /**
     * Capture des screenshots
     */
    private captureScreenshots;
    /**
     * Génère l'analyse UX
     */
    private generateUXAnalysis;
}
//# sourceMappingURL=CompetitiveAnalysisEngine.d.ts.map