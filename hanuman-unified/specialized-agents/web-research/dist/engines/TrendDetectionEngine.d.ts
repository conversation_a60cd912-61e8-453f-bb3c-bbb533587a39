import { Logger } from 'winston';
import { AgentConfig, SearchResult, TrendData, ColorTrend, TypographyTrend, InteractionTrend } from '../types';
/**
 * Moteur de détection de tendances
 */
export declare class TrendDetectionEngine {
    private logger;
    private config;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise le moteur
     */
    initialize(): Promise<void>;
    /**
     * Détecte les tendances à partir des résultats de recherche
     */
    detectTrends(searchResults: SearchResult[], query: string): Promise<TrendData[]>;
    /**
     * Analyse les tendances couleurs
     */
    analyzeColorTrends(designSources: SearchResult[]): Promise<ColorTrend[]>;
    /**
     * Analyse les tendances typographiques
     */
    analyzeTypographyTrends(designSources: SearchResult[]): Promise<TypographyTrend[]>;
    /**
     * Analyse les patterns d'interaction
     */
    analyzeInteractionPatterns(designSources: SearchResult[]): Promise<InteractionTrend[]>;
    /**
     * Analyse la fréquence des mots-clés
     */
    private analyzeKeywordFrequency;
    /**
     * Analyse les tendances temporelles
     */
    private analyzeTemporalTrends;
    /**
     * Détermine la direction de la tendance
     */
    private determineTrendDirection;
    /**
     * Calcule la croissance
     */
    private calculateGrowth;
    /**
     * Calcule la tendance linéaire
     */
    private calculateLinearTrend;
    /**
     * Trouve les termes liés
     */
    private findRelatedTerms;
    /**
     * Obtient les sources pour un mot-clé
     */
    private getSourcesForKeyword;
    /**
     * Extrait les mots-clés d'un texte
     */
    private extractKeywordsFromText;
    /**
     * Vérifie si un mot est un mot vide
     */
    private isStopWord;
}
//# sourceMappingURL=TrendDetectionEngine.d.ts.map