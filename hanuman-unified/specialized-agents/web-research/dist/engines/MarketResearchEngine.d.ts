import { Logger } from 'winston';
import { AgentConfig, SearchResult, ResearchAnalysis, BehaviorPattern, UserPreference, Demographics, DeviceUsage } from '../types';
/**
 * Moteur de recherche de marché
 */
export declare class MarketResearchEngine {
    private logger;
    private config;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise le moteur
     */
    initialize(): Promise<void>;
    /**
     * Analyse les résultats de recherche
     */
    analyzeResults(searchResults: SearchResult[], request: any): Promise<ResearchAnalysis>;
    /**
     * Analyse les patterns comportementaux
     */
    analyzeBehaviorPatterns(behaviorData: any): Promise<BehaviorPattern[]>;
    /**
     * Analyse les préférences utilisateur
     */
    analyzeUserPreferences(behaviorData: any): Promise<UserPreference[]>;
    /**
     * Analyse les démographiques
     */
    analyzeDemographics(behaviorData: any): Promise<Demographics>;
    /**
     * Analyse l'usage des devices
     */
    analyzeDeviceUsage(behaviorData: any): Promise<DeviceUsage[]>;
    /**
     * Génère un résumé
     */
    private generateSummary;
    /**
     * Identifie les insights clés
     */
    private identifyKeyInsights;
    /**
     * Identifie les opportunités
     */
    private identifyOpportunities;
    /**
     * Identifie les menaces
     */
    private identifyThreats;
    /**
     * Génère des recommandations
     */
    private generateRecommendations;
    /**
     * Calcule le niveau de confiance
     */
    private calculateConfidence;
    /**
     * Obtient les top domaines
     */
    private getTopDomains;
}
//# sourceMappingURL=MarketResearchEngine.d.ts.map