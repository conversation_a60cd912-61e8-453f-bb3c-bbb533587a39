import { Logger } from 'winston';
import { AgentConfig, ResearchRequest, SearchResult } from '../types';
/**
 * Moteur de scraping web intelligent
 */
export declare class WebScrapingEngine {
    private logger;
    private config;
    private browser;
    private isInitialized;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise le moteur de scraping
     */
    initialize(): Promise<void>;
    /**
     * Effectue une recherche multi-sources
     */
    search(request: ResearchRequest): Promise<SearchResult[]>;
    /**
     * Recherche web générale
     */
    private searchWeb;
    /**
     * Recherche via Google Search API
     */
    private searchGoogle;
    /**
     * Recherche via Bing Search API
     */
    private searchBing;
    /**
     * Recherche directe sur des sites
     */
    private searchDirect;
    /**
     * Scrape le contenu d'une page
     */
    scrapePageContent(url: string): Promise<string>;
    /**
     * Recherche de sources design
     */
    searchDesignSources(industry: string): Promise<SearchResult[]>;
    /**
     * Collecte des données comportementales
     */
    collectBehaviorData(domain: string): Promise<any>;
    /**
     * Recherche d'actualités
     */
    private searchNews;
    /**
     * Recherche académique
     */
    private searchAcademic;
    /**
     * Recherche sur les réseaux sociaux
     */
    private searchSocial;
    /**
     * Recherche chez les concurrents
     */
    private searchCompetitors;
    /**
     * Scrape un site de recherche
     */
    private scrapeSearchSite;
    /**
     * Extrait les mots-clés d'un texte
     */
    private extractKeywords;
    /**
     * Génère un ID unique
     */
    private generateId;
    /**
     * Arrête le moteur
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=WebScrapingEngine.d.ts.map