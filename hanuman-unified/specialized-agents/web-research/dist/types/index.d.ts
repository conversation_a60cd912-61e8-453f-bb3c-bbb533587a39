/**
 * Types pour l'Agent Web Research
 */
export interface AgentConfig {
    port: number;
    kafka: {
        brokers: string[];
        clientId: string;
        groupId: string;
    };
    weaviate: {
        scheme: string;
        host: string;
        port: number;
    };
    redis: {
        host: string;
        port: number;
        password?: string;
    };
    puppeteer: {
        headless: boolean;
        timeout: number;
        userAgent: string;
    };
    apis: {
        googleSearch?: {
            apiKey: string;
            searchEngineId: string;
        };
        bingSearch?: {
            apiKey: string;
        };
    };
}
export interface ResearchRequest {
    query: string;
    sources: ResearchSource[];
    options: ResearchOptions;
    metadata?: Record<string, any>;
}
export interface ResearchSource {
    type: 'web' | 'news' | 'academic' | 'social' | 'competitor';
    priority: 'high' | 'medium' | 'low';
    domains?: string[];
    excludeDomains?: string[];
}
export interface ResearchOptions {
    maxResults: number;
    timeFrame?: string;
    language?: string;
    region?: string;
    depth: 'shallow' | 'medium' | 'deep';
    includeImages?: boolean;
    includeVideos?: boolean;
    realTime?: boolean;
}
export interface ResearchResult {
    id: string;
    query: string;
    results: SearchResult[];
    analysis: ResearchAnalysis;
    trends: TrendData[];
    competitors: CompetitorData[];
    metadata: {
        timestamp: Date;
        duration: number;
        totalResults: number;
        sources: string[];
    };
}
export interface SearchResult {
    id: string;
    title: string;
    url: string;
    snippet: string;
    content?: string;
    images?: string[];
    publishDate?: Date;
    author?: string;
    domain: string;
    relevanceScore: number;
    credibilityScore: number;
    sentiment?: 'positive' | 'negative' | 'neutral';
    keywords: string[];
    entities: Entity[];
}
export interface Entity {
    name: string;
    type: 'person' | 'organization' | 'location' | 'product' | 'technology';
    confidence: number;
    mentions: number;
}
export interface ResearchAnalysis {
    summary: string;
    keyInsights: string[];
    opportunities: Opportunity[];
    threats: Threat[];
    recommendations: string[];
    confidence: number;
}
export interface Opportunity {
    description: string;
    impact: 'high' | 'medium' | 'low';
    effort: 'high' | 'medium' | 'low';
    timeframe: string;
    sources: string[];
}
export interface Threat {
    description: string;
    severity: 'high' | 'medium' | 'low';
    probability: number;
    mitigation: string[];
    sources: string[];
}
export interface TrendData {
    keyword: string;
    trend: 'rising' | 'stable' | 'declining';
    volume: number;
    growth: number;
    timeframe: string;
    relatedTerms: string[];
    sources: string[];
}
export interface CompetitorData {
    name: string;
    domain: string;
    description: string;
    strengths: string[];
    weaknesses: string[];
    marketPosition: string;
    recentNews: SearchResult[];
    socialPresence: SocialPresence;
}
export interface SocialPresence {
    platforms: {
        platform: string;
        followers: number;
        engagement: number;
        lastPost: Date;
    }[];
    sentiment: 'positive' | 'negative' | 'neutral';
    mentions: number;
}
export interface MonitoringAlert {
    id: string;
    type: 'keyword' | 'competitor' | 'trend' | 'news';
    trigger: string;
    severity: 'high' | 'medium' | 'low';
    message: string;
    data: any;
    timestamp: Date;
}
export interface DesignTrends {
    colorTrends: ColorTrend[];
    typographyTrends: TypographyTrend[];
    layoutTrends: LayoutTrend[];
    interactionTrends: InteractionTrend[];
    industry: string;
    timeframe: string;
    confidence: number;
}
export interface ColorTrend {
    palette: string[];
    usage: string;
    popularity: number;
    context: string[];
}
export interface TypographyTrend {
    fontFamily: string;
    style: string;
    usage: string;
    popularity: number;
}
export interface LayoutTrend {
    pattern: string;
    description: string;
    usage: string;
    popularity: number;
}
export interface InteractionTrend {
    type: string;
    description: string;
    implementation: string;
    popularity: number;
}
export interface UserBehaviorData {
    patterns: BehaviorPattern[];
    preferences: UserPreference[];
    demographics: Demographics;
    devices: DeviceUsage[];
    timeframe: string;
}
export interface BehaviorPattern {
    action: string;
    frequency: number;
    context: string;
    triggers: string[];
}
export interface UserPreference {
    category: string;
    preference: string;
    strength: number;
    demographic: string;
}
export interface Demographics {
    ageGroups: {
        range: string;
        percentage: number;
    }[];
    genders: {
        gender: string;
        percentage: number;
    }[];
    locations: {
        location: string;
        percentage: number;
    }[];
    interests: {
        interest: string;
        percentage: number;
    }[];
}
export interface DeviceUsage {
    device: string;
    percentage: number;
    sessionDuration: number;
    bounceRate: number;
}
export interface CompetitorUXAnalysis {
    competitor: string;
    domain: string;
    uxScore: number;
    strengths: UXStrength[];
    weaknesses: UXWeakness[];
    opportunities: UXOpportunity[];
    screenshots: string[];
    analysis: string;
}
export interface UXStrength {
    area: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    examples: string[];
}
export interface UXWeakness {
    area: string;
    description: string;
    severity: 'high' | 'medium' | 'low';
    suggestions: string[];
}
export interface UXOpportunity {
    area: string;
    description: string;
    potential: 'high' | 'medium' | 'low';
    implementation: string;
}
//# sourceMappingURL=index.d.ts.map