"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KafkaCommunication = void 0;
const events_1 = require("events");
const kafkajs_1 = require("kafkajs");
/**
 * Communication Kafka pour l'Agent Web Research
 */
class KafkaCommunication extends events_1.EventEmitter {
    constructor(config, logger) {
        super();
        this.isConnected = false;
        // Topics Kafka
        this.topics = {
            // Topics de sortie (publication)
            RESEARCH_RESULTS: 'web-research.results',
            DESIGN_TRENDS: 'web-research.design-trends',
            MONITORING_ALERTS: 'web-research.alerts',
            USER_BEHAVIOR: 'web-research.user-behavior',
            COMPETITOR_ANALYSIS: 'web-research.competitor-analysis',
            // Topics d'entrée (consommation)
            RESEARCH_REQUESTS: 'web-research.requests',
            DESIGN_TRENDS_REQUESTS: 'web-research.design-trends-requests',
            MONITORING_COMMANDS: 'web-research.monitoring-commands',
            // Topics de coordination
            AGENT_STATUS: 'agents.status',
            SYSTEM_EVENTS: 'system.events'
        };
        this.logger = logger;
        const kafkaConfig = {
            clientId: config.clientId || 'agent-web-research',
            brokers: config.brokers || ['localhost:9092'],
            retry: {
                initialRetryTime: 100,
                retries: 8
            }
        };
        this.kafka = new kafkajs_1.Kafka(kafkaConfig);
        this.producer = this.kafka.producer();
        this.consumer = this.kafka.consumer({
            groupId: config.groupId || 'web-research-group'
        });
    }
    /**
     * Initialise la communication Kafka
     */
    async initialize() {
        try {
            this.logger.info('Initializing Kafka communication...');
            // Connecter le producer
            await this.producer.connect();
            this.logger.info('Kafka producer connected');
            // Connecter le consumer
            await this.consumer.connect();
            this.logger.info('Kafka consumer connected');
            // S'abonner aux topics
            await this.subscribeToTopics();
            // Démarrer la consommation
            await this.startConsuming();
            this.isConnected = true;
            this.logger.info('Kafka communication initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize Kafka communication:', error);
            throw error;
        }
    }
    /**
     * S'abonne aux topics nécessaires
     */
    async subscribeToTopics() {
        const subscriptionTopics = [
            this.topics.RESEARCH_REQUESTS,
            this.topics.DESIGN_TRENDS_REQUESTS,
            this.topics.MONITORING_COMMANDS,
            this.topics.SYSTEM_EVENTS
        ];
        for (const topic of subscriptionTopics) {
            await this.consumer.subscribe({ topic, fromBeginning: false });
            this.logger.info(`Subscribed to topic: ${topic}`);
        }
    }
    /**
     * Démarre la consommation des messages
     */
    async startConsuming() {
        await this.consumer.run({
            eachMessage: async ({ topic, partition, message }) => {
                try {
                    const value = message.value?.toString();
                    if (!value)
                        return;
                    const data = JSON.parse(value);
                    this.logger.info(`Received message from topic ${topic}:`, data);
                    await this.handleIncomingMessage(topic, data);
                }
                catch (error) {
                    this.logger.error(`Error processing message from topic ${topic}:`, error);
                }
            }
        });
    }
    /**
     * Gère les messages entrants
     */
    async handleIncomingMessage(topic, data) {
        switch (topic) {
            case this.topics.RESEARCH_REQUESTS:
                this.emit('research_request', data);
                break;
            case this.topics.DESIGN_TRENDS_REQUESTS:
                this.emit('design_trends_request', data);
                break;
            case this.topics.MONITORING_COMMANDS:
                this.emit('monitoring_command', data);
                break;
            case this.topics.SYSTEM_EVENTS:
                this.emit('system_event', data);
                break;
            default:
                this.logger.warn(`Unhandled topic: ${topic}`);
        }
    }
    /**
     * Publie un résultat de recherche
     */
    async publishResearchResult(result) {
        try {
            await this.producer.send({
                topic: this.topics.RESEARCH_RESULTS,
                messages: [{
                        key: result.id,
                        value: JSON.stringify(result),
                        timestamp: Date.now().toString()
                    }]
            });
            this.logger.info(`Published research result: ${result.id}`);
        }
        catch (error) {
            this.logger.error('Failed to publish research result:', error);
            throw error;
        }
    }
    /**
     * Publie des tendances design
     */
    async publishDesignTrends(trends) {
        try {
            await this.producer.send({
                topic: this.topics.DESIGN_TRENDS,
                messages: [{
                        key: trends.industry,
                        value: JSON.stringify(trends),
                        timestamp: Date.now().toString()
                    }]
            });
            this.logger.info(`Published design trends for industry: ${trends.industry}`);
        }
        catch (error) {
            this.logger.error('Failed to publish design trends:', error);
            throw error;
        }
    }
    /**
     * Publie une alerte de monitoring
     */
    async publishAlert(alert) {
        try {
            await this.producer.send({
                topic: this.topics.MONITORING_ALERTS,
                messages: [{
                        key: alert.id,
                        value: JSON.stringify(alert),
                        timestamp: Date.now().toString(),
                        headers: {
                            severity: alert.severity,
                            type: alert.type
                        }
                    }]
            });
            this.logger.info(`Published monitoring alert: ${alert.id}`);
        }
        catch (error) {
            this.logger.error('Failed to publish monitoring alert:', error);
            throw error;
        }
    }
    /**
     * Publie des données de comportement utilisateur
     */
    async publishUserBehavior(behaviorData) {
        try {
            await this.producer.send({
                topic: this.topics.USER_BEHAVIOR,
                messages: [{
                        key: behaviorData.domain || 'unknown',
                        value: JSON.stringify(behaviorData),
                        timestamp: Date.now().toString()
                    }]
            });
            this.logger.info('Published user behavior data');
        }
        catch (error) {
            this.logger.error('Failed to publish user behavior data:', error);
            throw error;
        }
    }
    /**
     * Publie une analyse concurrentielle
     */
    async publishCompetitorAnalysis(analysis) {
        try {
            await this.producer.send({
                topic: this.topics.COMPETITOR_ANALYSIS,
                messages: [{
                        key: analysis.competitor || 'unknown',
                        value: JSON.stringify(analysis),
                        timestamp: Date.now().toString()
                    }]
            });
            this.logger.info('Published competitor analysis');
        }
        catch (error) {
            this.logger.error('Failed to publish competitor analysis:', error);
            throw error;
        }
    }
    /**
     * Publie le statut de l'agent
     */
    async publishAgentStatus(status) {
        try {
            const statusMessage = {
                agentId: 'web-research',
                status: status.status || 'online',
                timestamp: new Date().toISOString(),
                metrics: status.metrics || {},
                capabilities: [
                    'web_scraping',
                    'competitive_analysis',
                    'trend_detection',
                    'market_research',
                    'real_time_monitoring'
                ]
            };
            await this.producer.send({
                topic: this.topics.AGENT_STATUS,
                messages: [{
                        key: 'web-research',
                        value: JSON.stringify(statusMessage),
                        timestamp: Date.now().toString()
                    }]
            });
            this.logger.info('Published agent status');
        }
        catch (error) {
            this.logger.error('Failed to publish agent status:', error);
        }
    }
    /**
     * Envoie un message personnalisé
     */
    async sendMessage(topic, key, data) {
        try {
            await this.producer.send({
                topic,
                messages: [{
                        key,
                        value: JSON.stringify(data),
                        timestamp: Date.now().toString()
                    }]
            });
            this.logger.info(`Sent message to topic ${topic} with key ${key}`);
        }
        catch (error) {
            this.logger.error(`Failed to send message to topic ${topic}:`, error);
            throw error;
        }
    }
    /**
     * Déconnecte la communication Kafka
     */
    async disconnect() {
        try {
            this.logger.info('Disconnecting Kafka communication...');
            await this.consumer.disconnect();
            await this.producer.disconnect();
            this.isConnected = false;
            this.logger.info('Kafka communication disconnected');
        }
        catch (error) {
            this.logger.error('Error disconnecting Kafka communication:', error);
            throw error;
        }
    }
    /**
     * Vérifie si la communication est connectée
     */
    isConnectedToKafka() {
        return this.isConnected;
    }
    /**
     * Obtient les métriques de communication
     */
    getMetrics() {
        return {
            isConnected: this.isConnected,
            topics: this.topics,
            lastActivity: new Date().toISOString()
        };
    }
}
exports.KafkaCommunication = KafkaCommunication;
//# sourceMappingURL=KafkaCommunication.js.map