import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { ResearchRequest, ResearchResult, DesignTrends, UserBehaviorData, CompetitorUXAnalysis, AgentConfig } from '../types';
/**
 * Agent Web Research - Recherche web intelligente et veille concurrentielle
 */
export declare class WebResearchAgent extends EventEmitter {
    private logger;
    private config;
    private webScrapingEngine;
    private competitiveAnalysisEngine;
    private trendDetectionEngine;
    private marketResearchEngine;
    private monitoringEngine;
    private memory;
    private communication;
    private isInitialized;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise l'agent
     */
    initialize(): Promise<void>;
    /**
     * Effectue une recherche web intelligente
     */
    conductResearch(request: ResearchRequest): Promise<ResearchResult>;
    /**
     * Analyse les tendances design pour l'agent UI/UX
     */
    analyzeDesignTrends(industry: string): Promise<DesignTrends>;
    /**
     * Analyse le comportement utilisateur
     */
    analyzeUserBehavior(domain: string): Promise<UserBehaviorData>;
    /**
     * Effectue une analyse UX concurrentielle
     */
    performCompetitorUXAnalysis(competitors: string[]): Promise<CompetitorUXAnalysis[]>;
    /**
     * Démarre le monitoring en temps réel
     */
    startMonitoring(keywords: string[], competitors: string[]): Promise<void>;
    /**
     * Arrête le monitoring
     */
    stopMonitoring(): Promise<void>;
    /**
     * Gère les alertes de monitoring
     */
    private handleMonitoringAlert;
    /**
     * Configure les listeners d'événements
     */
    private setupEventListeners;
    /**
     * Valide une requête de recherche
     */
    private validateResearchRequest;
    /**
     * Génère un ID unique
     */
    private generateId;
    /**
     * Arrête l'agent
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=WebResearchAgent.d.ts.map