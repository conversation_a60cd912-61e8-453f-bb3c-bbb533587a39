import { Logger } from 'winston';
import { AgentConfig, SearchResult, TrendData, ColorTrend, TypographyTrend, InteractionTrend } from '../types';

/**
 * Moteur de détection de tendances
 */
export class TrendDetectionEngine {
  private logger: Logger;
  private config: AgentConfig;

  constructor(config: AgentConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
  }

  /**
   * Initialise le moteur
   */
  async initialize(): Promise<void> {
    this.logger.info('Trend Detection Engine initialized');
  }

  /**
   * Détecte les tendances à partir des résultats de recherche
   */
  async detectTrends(searchResults: SearchResult[], query: string): Promise<TrendData[]> {
    try {
      this.logger.info(`Detecting trends for query: ${query}`);

      const trends: TrendData[] = [];

      // Analyser les mots-clés fréquents
      const keywordFrequency = this.analyzeKeywordFrequency(searchResults);
      
      // Analyser les tendances temporelles
      const temporalTrends = this.analyzeTemporalTrends(searchResults);
      
      // Combiner les analyses
      for (const [keyword, frequency] of keywordFrequency.entries()) {
        if (frequency >= 3) { // Seuil minimum
          const trend: TrendData = {
            keyword,
            trend: this.determineTrendDirection(keyword, temporalTrends),
            volume: frequency,
            growth: this.calculateGrowth(keyword, temporalTrends),
            timeframe: 'last_30_days',
            relatedTerms: this.findRelatedTerms(keyword, searchResults),
            sources: this.getSourcesForKeyword(keyword, searchResults)
          };
          trends.push(trend);
        }
      }

      // Trier par volume et croissance
      trends.sort((a, b) => (b.volume * (1 + b.growth)) - (a.volume * (1 + a.growth)));

      this.logger.info(`Detected ${trends.length} trends`);
      return trends.slice(0, 10); // Top 10 tendances
    } catch (error) {
      this.logger.error('Trend detection failed:', error);
      return [];
    }
  }

  /**
   * Analyse les tendances couleurs
   */
  async analyzeColorTrends(designSources: SearchResult[]): Promise<ColorTrend[]> {
    try {
      this.logger.info('Analyzing color trends...');

      // Simulation d'analyse de couleurs - en production, analyser les images
      const colorTrends: ColorTrend[] = [
        {
          palette: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
          usage: 'Primary brand colors',
          popularity: 0.85,
          context: ['Tech startups', 'SaaS platforms', 'Mobile apps']
        },
        {
          palette: ['#2C3E50', '#34495E', '#7F8C8D', '#BDC3C7', '#ECF0F1'],
          usage: 'Professional interfaces',
          popularity: 0.78,
          context: ['Corporate websites', 'B2B platforms', 'Financial services']
        },
        {
          palette: ['#E74C3C', '#F39C12', '#F1C40F', '#27AE60', '#3498DB'],
          usage: 'Call-to-action elements',
          popularity: 0.72,
          context: ['E-commerce', 'Marketing sites', 'Landing pages']
        },
        {
          palette: ['#9B59B6', '#8E44AD', '#E91E63', '#FF5722', '#795548'],
          usage: 'Creative and artistic',
          popularity: 0.65,
          context: ['Design portfolios', 'Creative agencies', 'Art platforms']
        }
      ];

      return colorTrends;
    } catch (error) {
      this.logger.error('Color trends analysis failed:', error);
      return [];
    }
  }

  /**
   * Analyse les tendances typographiques
   */
  async analyzeTypographyTrends(designSources: SearchResult[]): Promise<TypographyTrend[]> {
    try {
      this.logger.info('Analyzing typography trends...');

      const typographyTrends: TypographyTrend[] = [
        {
          fontFamily: 'Inter',
          style: 'Sans-serif, modern',
          usage: 'Body text and interfaces',
          popularity: 0.92
        },
        {
          fontFamily: 'Poppins',
          style: 'Sans-serif, friendly',
          usage: 'Headlines and branding',
          popularity: 0.88
        },
        {
          fontFamily: 'Roboto',
          style: 'Sans-serif, clean',
          usage: 'Technical documentation',
          popularity: 0.85
        },
        {
          fontFamily: 'Montserrat',
          style: 'Sans-serif, elegant',
          usage: 'Luxury and premium brands',
          popularity: 0.78
        },
        {
          fontFamily: 'Source Sans Pro',
          style: 'Sans-serif, readable',
          usage: 'Long-form content',
          popularity: 0.75
        },
        {
          fontFamily: 'Playfair Display',
          style: 'Serif, sophisticated',
          usage: 'Editorial and luxury',
          popularity: 0.68
        }
      ];

      return typographyTrends;
    } catch (error) {
      this.logger.error('Typography trends analysis failed:', error);
      return [];
    }
  }

  /**
   * Analyse les patterns d'interaction
   */
  async analyzeInteractionPatterns(designSources: SearchResult[]): Promise<InteractionTrend[]> {
    try {
      this.logger.info('Analyzing interaction patterns...');

      const interactionTrends: InteractionTrend[] = [
        {
          type: 'Micro-interactions',
          description: 'Subtle animations for user feedback',
          implementation: 'CSS transitions and JavaScript animations',
          popularity: 0.89
        },
        {
          type: 'Parallax Scrolling',
          description: 'Background moves slower than foreground',
          implementation: 'CSS transform and scroll events',
          popularity: 0.76
        },
        {
          type: 'Hover Effects',
          description: 'Interactive elements respond to mouse hover',
          implementation: 'CSS :hover pseudo-class',
          popularity: 0.94
        },
        {
          type: 'Loading Animations',
          description: 'Engaging loading states and skeleton screens',
          implementation: 'CSS animations and SVG',
          popularity: 0.82
        },
        {
          type: 'Gesture Navigation',
          description: 'Swipe and touch gestures for mobile',
          implementation: 'Touch event handlers',
          popularity: 0.71
        },
        {
          type: 'Voice Interface',
          description: 'Voice commands and speech recognition',
          implementation: 'Web Speech API',
          popularity: 0.45
        }
      ];

      return interactionTrends;
    } catch (error) {
      this.logger.error('Interaction patterns analysis failed:', error);
      return [];
    }
  }

  /**
   * Analyse la fréquence des mots-clés
   */
  private analyzeKeywordFrequency(results: SearchResult[]): Map<string, number> {
    const frequency = new Map<string, number>();

    for (const result of results) {
      const allKeywords = [
        ...result.keywords,
        ...this.extractKeywordsFromText(result.title),
        ...this.extractKeywordsFromText(result.snippet)
      ];

      for (const keyword of allKeywords) {
        const normalizedKeyword = keyword.toLowerCase().trim();
        if (normalizedKeyword.length > 2) {
          frequency.set(normalizedKeyword, (frequency.get(normalizedKeyword) || 0) + 1);
        }
      }
    }

    return frequency;
  }

  /**
   * Analyse les tendances temporelles
   */
  private analyzeTemporalTrends(results: SearchResult[]): Map<string, any> {
    const temporalData = new Map<string, any>();

    // Grouper par période (simulation)
    const periods = ['week1', 'week2', 'week3', 'week4'];
    
    for (const result of results) {
      const period = periods[Math.floor(Math.random() * periods.length)];
      
      for (const keyword of result.keywords) {
        if (!temporalData.has(keyword)) {
          temporalData.set(keyword, { week1: 0, week2: 0, week3: 0, week4: 0 });
        }
        temporalData.get(keyword)[period]++;
      }
    }

    return temporalData;
  }

  /**
   * Détermine la direction de la tendance
   */
  private determineTrendDirection(keyword: string, temporalTrends: Map<string, any>): 'rising' | 'stable' | 'declining' {
    const data = temporalTrends.get(keyword);
    if (!data) return 'stable';

    const values = [data.week1, data.week2, data.week3, data.week4];
    const trend = this.calculateLinearTrend(values);

    if (trend > 0.1) return 'rising';
    if (trend < -0.1) return 'declining';
    return 'stable';
  }

  /**
   * Calcule la croissance
   */
  private calculateGrowth(keyword: string, temporalTrends: Map<string, any>): number {
    const data = temporalTrends.get(keyword);
    if (!data) return 0;

    const firstWeek = data.week1 || 1;
    const lastWeek = data.week4 || 1;
    
    return (lastWeek - firstWeek) / firstWeek;
  }

  /**
   * Calcule la tendance linéaire
   */
  private calculateLinearTrend(values: number[]): number {
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, x) => sum + x * y, 0);
    const sumX2 = values.reduce((sum, _, x) => sum + x * x, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return slope;
  }

  /**
   * Trouve les termes liés
   */
  private findRelatedTerms(keyword: string, results: SearchResult[]): string[] {
    const relatedTerms = new Set<string>();

    for (const result of results) {
      if (result.keywords.includes(keyword)) {
        result.keywords.forEach(k => {
          if (k !== keyword && k.length > 2) {
            relatedTerms.add(k);
          }
        });
      }
    }

    return Array.from(relatedTerms).slice(0, 5);
  }

  /**
   * Obtient les sources pour un mot-clé
   */
  private getSourcesForKeyword(keyword: string, results: SearchResult[]): string[] {
    const sources = new Set<string>();

    for (const result of results) {
      if (result.keywords.includes(keyword)) {
        sources.add(result.domain);
      }
    }

    return Array.from(sources).slice(0, 3);
  }

  /**
   * Extrait les mots-clés d'un texte
   */
  private extractKeywordsFromText(text: string): string[] {
    if (!text) return [];

    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .filter(word => !this.isStopWord(word));
  }

  /**
   * Vérifie si un mot est un mot vide
   */
  private isStopWord(word: string): boolean {
    const stopWords = [
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
      'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
      'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
      'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
    ];
    
    return stopWords.includes(word);
  }
}
