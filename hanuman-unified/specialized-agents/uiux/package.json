{"name": "@retreat-and-be/agent-uiux", "version": "1.0.0", "description": "Agent UI/UX Design Thinking pour l'architecture Living AI", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "docker:build": "docker build -t agent-uiux .", "docker:run": "docker run -p 3005:3005 agent-uiux"}, "dependencies": {"@types/node": "^20.0.0", "axios": "^1.6.0", "express": "^4.18.0", "kafka-node": "^5.0.0", "redis": "^4.6.0", "winston": "^3.11.0", "dotenv": "^16.3.0", "puppeteer": "^21.0.0", "figma-api": "^1.11.0", "openai": "^4.20.0", "weaviate-ts-client": "^1.4.0", "uuid": "^9.0.0", "joi": "^17.11.0", "helmet": "^7.1.0", "cors": "^2.8.5"}, "devDependencies": {"@types/express": "^4.17.0", "@types/uuid": "^9.0.0", "@types/cors": "^2.8.0", "typescript": "^5.2.0", "ts-node": "^10.9.0", "jest": "^29.7.0", "@types/jest": "^29.5.0", "eslint": "^8.52.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0"}, "keywords": ["ai-agent", "uiux", "design-thinking", "retreat-and-be", "living-ai"], "author": "Retreat And Be Team", "license": "MIT"}