{"version": 3, "file": "UIUXAgent.js", "sourceRoot": "", "sources": ["../../src/core/UIUXAgent.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AActC,sEAAmE;AACnE,wEAAqE;AACrE,wEAAqE;AACrE,0EAAuE;AACvE,gEAA6D;AAI7D;;;;;GAKG;AACH,MAAa,SAAU,SAAQ,qBAAY;IAazC,YACE,MAAmB,EACnB,MAAc,EACd,MAAsB,EACtB,aAAiC;QAEjC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,0BAA0B;QAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACjE,IAAI,CAAC,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnE,IAAI,CAAC,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnE,IAAI,CAAC,oBAAoB,GAAG,IAAI,2CAAoB,CAAC,MAAM,CAAC,CAAC;QAC7D,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE3D,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,MAAM,CAAC,EAAE,aAAa,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAAC,YAAgC;QAC9D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2CAA2C,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;QAEhF,IAAI,CAAC;YACH,qDAAqD;YACrD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC/D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAC;YAE3E,wDAAwD;YACxD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAC1D,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAE7E,qCAAqC;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;YAEzF,sCAAsC;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACvD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,YAAY,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC;YAEvG,qCAAqC;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACvD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qCAAqC,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAE5F,iCAAiC;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACpD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEjF,4CAA4C;YAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACzD,MAAM,uBAAuB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;YAE/F,sCAAsC;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACvE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YAErF,mBAAmB;YACnB,MAAM,mBAAmB,GAA0B;gBACjD,YAAY;gBACZ,kBAAkB;gBAClB,QAAQ;gBACR,YAAY;gBACZ,UAAU;gBACV,gBAAgB;gBAChB,uBAAuB;gBACvB,gBAAgB;gBAChB,uBAAuB,EAAE,MAAM,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC;gBAC7E,mBAAmB,EAAE,MAAM,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,UAAU,CAAC;gBACnF,eAAe,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,UAAU,CAAC;aAC1E,CAAC;YAEF,yBAAyB;YACzB,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;YAE5E,6BAA6B;YAC7B,MAAM,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;YAEpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACpD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;YAEjD,OAAO,mBAAmB,CAAC;QAE7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACpF,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAChC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,4BAA4B,CAAC,YAAgC;QACjE,OAAO,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,YAAgC;QAC7D,OAAO,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC9B,YAA0B,EAC1B,kBAAsC;QAEtC,OAAO,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC9B,YAAgC,EAChC,QAAmB,EACnB,YAAgC;QAEhC,OAAO,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;IAC7F,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qCAAqC,CACzC,QAAmB,EACnB,YAAkC;QAElC,OAAO,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,UAAgC,EAChC,QAAmB;QAEnB,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,UAAgC,EAChC,gBAAkC;QAElC,OAAO,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,UAAgC,EAChC,YAAkC;QAElC,OAAO,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAAC,aAAqB,EAAE,cAAqC;QACvF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAEpD,0BAA0B;QAC1B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAEpE,mCAAmC;QACnC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QAExF,gCAAgC;QAChC,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAExF,OAAO;YACL,YAAY;YACZ,gBAAgB;YAChB,mBAAmB;YACnB,eAAe,EAAE,MAAM,IAAI,CAAC,qCAAqC,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;SACzG,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,mBAAmB,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEpF,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,OAAY;QAC5C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC1E,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,OAAY;QAChD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACnF,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,qDAAqD;IAC7C,KAAK,CAAC,6BAA6B,CAAC,UAAgC;QAC1E,OAAO,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IAC/D,CAAC;IAEO,KAAK,CAAC,yBAAyB,CACrC,YAAkC,EAClC,UAAgC;QAEhC,OAAO,IAAI,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACtF,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,QAAmB,EACnB,UAAgC;QAEhC,OAAO,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC1E,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,IAAY;QAC7C,gEAAgE;QAChE,OAAO,EAAE,QAAQ,EAAE,uBAAuB,EAAE,CAAC;IAC/C,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,YAAiB,EAAE,MAA6B;QAClF,0CAA0C;QAC1C,OAAO,EAAE,UAAU,EAAE,2BAA2B,EAAE,CAAC;IACrD,CAAC;IAEO,KAAK,CAAC,qCAAqC,CACjD,gBAAqB,EACrB,mBAAwB;QAExB,+CAA+C;QAC/C,OAAO,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;IAClD,CAAC;CACF;AAtRD,8BAsRC"}