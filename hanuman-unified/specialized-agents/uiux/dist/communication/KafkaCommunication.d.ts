import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { ComprehensiveUXDesign } from '../types';
/**
 * Système de Communication Synaptique via Kafka
 *
 * Gère la communication entre l'agent UI/UX et les autres agents
 * du système nerveux distribué.
 */
export declare class KafkaCommunication extends EventEmitter {
    private logger;
    private producer;
    private consumer;
    private agentId;
    private isConnected;
    private readonly topics;
    constructor(logger: Logger, agentId: string, kafkaBrokers?: string);
    /**
     * Initialise la connexion Kafka
     */
    private initializeKafka;
    /**
     * Configure le consumer Kafka
     */
    private setupConsumer;
    /**
     * Configure le producer Kafka
     */
    private setupProducer;
    /**
     * Publie la completion d'un design
     */
    publishDesignComplete(design: ComprehensiveUXDesign): Promise<void>;
    /**
     * Demande une validation d'implémentation
     */
    requestImplementationValidation(code: string, design: ComprehensiveUXDesign): Promise<string>;
    /**
     * Envoie un feedback de design
     */
    sendDesignFeedback(targetAgent: string, feedback: any): Promise<void>;
    /**
     * Publie une bibliothèque de composants
     */
    publishComponentLibrary(library: any): Promise<void>;
    /**
     * Envoie une réponse à une demande
     */
    sendResponse(correlationId: string, response: any): Promise<void>;
    /**
     * Envoie une erreur en réponse à une demande
     */
    sendError(correlationId: string, error: string): Promise<void>;
    /**
     * Envoie un heartbeat
     */
    private sendHeartbeat;
    /**
     * Envoie un message via Kafka
     */
    private sendMessage;
    /**
     * Simule l'écoute des messages (pour le développement)
     */
    private simulateMessageListening;
    /**
     * Gère les messages entrants
     */
    private handleIncomingMessage;
    /**
     * Génère un ID de message unique
     */
    private generateMessageId;
    /**
     * Génère un ID de corrélation unique
     */
    private generateCorrelationId;
    /**
     * Obtient la charge actuelle de l'agent
     */
    private getCurrentLoad;
    /**
     * Ferme les connexions Kafka
     */
    close(): Promise<void>;
    /**
     * Vérifie l'état de la connexion
     */
    isHealthy(): boolean;
    /**
     * Obtient les statistiques de communication
     */
    getStats(): any;
}
//# sourceMappingURL=KafkaCommunication.d.ts.map