import { Logger } from 'winston';
import { ConversionWireframes, Persona, UsabilityResults } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Testeur d'Utilisabilité Automatisé
 *
 * Simule des tests d'utilisabilité basés sur les personas
 * et génère des métriques de performance UX.
 */
export declare class UsabilityTester {
    private logger;
    private memory;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Simule des tests d'utilisabilité
     */
    simulateTests(wireframes: ConversionWireframes, personas: Persona[]): Promise<UsabilityResults>;
    /**
     * Crée une stratégie de test UX
     */
    createTestingStrategy(personas: Persona[], wireframes: ConversionWireframes): Promise<Record<string, any>>;
    private generateHeatmaps;
    private calculateClickThroughRates;
    private analyzeAttentionPatterns;
    private estimateConversionRates;
    private identifyDropOffPoints;
    private calculateErrorRates;
    private calculateCompletionRates;
    private measureTrustMetrics;
    private analyzeNavigationMetrics;
    private identifyErrorPatterns;
    private identifyConfusionPoints;
    private generateClickHeatmap;
    private generateScrollHeatmap;
    private generateAttentionHeatmap;
    private generateMouseMovementHeatmap;
    private calculateElementCTR;
    private calculateStepDropOff;
    private calculateOverallScore;
    private defineTestTypes;
    private createTestScenarios;
    private defineSuccessMetrics;
    private createTestingSchedule;
    private recommendTestingTools;
    private defineParticipantCriteria;
    private createAnalysisFramework;
    private defineReportingStructure;
    private predictFirstFixation;
    private calculateDwellTime;
    private analyzeScanningPatterns;
    private evaluateVisualHierarchy;
    private identifyDistractionPoints;
    private calculateFormErrorRate;
    private calculateNavigationErrorRate;
    private calculateSearchErrorRate;
    private calculateCheckoutErrorRate;
    private calculateFilterErrorRate;
    private calculateRegistrationCompletion;
    private calculateBookingCompletion;
    private calculateProfileSetupCompletion;
    private calculateSearchCompletion;
    private calculateReviewCompletion;
    private calculateCredibilityScore;
    private calculateSecurityPerception;
    private calculateBrandTrust;
    private calculateSocialProofEffectiveness;
    private calculateTransparencyScore;
    private analyzeMenuUsage;
    private analyzeSearchUsage;
    private analyzeBreadcrumbUsage;
    private analyzeBackButtonUsage;
    private calculateNavigationDepth;
    private calculateLostUserRate;
}
//# sourceMappingURL=UsabilityTester.d.ts.map