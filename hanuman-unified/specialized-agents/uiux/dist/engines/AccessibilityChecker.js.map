{"version": 3, "file": "AccessibilityChecker.js", "sourceRoot": "", "sources": ["../../src/engines/AccessibilityChecker.ts"], "names": [], "mappings": ";;;AAGA;;;;;GAKG;AACH,MAAa,oBAAoB;IAG/B,YAAY,MAAc;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,UAAgC;QACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QAEnE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG;gBACjB,QAAQ,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;gBAC9C,SAAS,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;gBAChD,aAAa,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;gBACxD,kBAAkB,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC;gBAClE,YAAY,EAAE,MAAM,IAAI,CAAC,8BAA8B,CAAC,UAAU,CAAC;gBACnE,aAAa,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;gBACxD,kBAAkB,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC;gBAClE,mBAAmB,EAAE,MAAM,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC;gBACpE,qBAAqB,EAAE,MAAM,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC;gBACxE,eAAe,EAAE,MAAM,IAAI,CAAC,oCAAoC,CAAC,UAAU,CAAC;aAC7E,CAAC;YAEF,OAAO,UAAU,CAAC;QAEpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAiD,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,aAAqB;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAE/D,IAAI,CAAC;YACH,MAAM,UAAU,GAAG;gBACjB,YAAY,EAAE,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC;gBAC5D,UAAU,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;gBACxD,QAAQ,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;gBACpD,gBAAgB,EAAE,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC;gBACpE,UAAU,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;gBACxD,eAAe,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC;gBAClE,eAAe,EAAE,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC;gBAClE,UAAU,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;gBACxD,KAAK,EAAE,MAAM,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC;gBAC5D,MAAM,EAAE,MAAM,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC;gBAC7D,KAAK,EAAE,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC;aAC3D,CAAC;YAEF,OAAO,UAAU,CAAC;QAEpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACpF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,gCAAgC;IAExB,KAAK,CAAC,aAAa,CAAC,UAAgC;QAC1D,qCAAqC;QACrC,OAAO;YACL,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE;gBACR,WAAW,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;gBACpD,QAAQ,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;gBAC9C,cAAc,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;gBAC1D,MAAM,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;aAC3C;YACD,KAAK,EAAE,IAAI,EAAE,sBAAsB;YACnC,MAAM,EAAE,EAAE;YACV,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,UAAgC;QAC3D,sCAAsC;QACtC,OAAO;YACL,KAAK,EAAE,KAAK;YACZ,QAAQ,EAAE;gBACR,WAAW,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;gBACvD,QAAQ,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;gBACjD,cAAc,EAAE,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC;gBAC7D,MAAM,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;aAC9C;YACD,KAAK,EAAE,IAAI,EAAE,0BAA0B;YACvC,MAAM,EAAE,EAAE;YACV,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAgC;QAC/D,qCAAqC;QACrC,OAAO;YACL,YAAY,EAAE;gBACZ,MAAM,EAAE,GAAG,EAAE,wBAAwB;gBACrC,KAAK,EAAE,GAAG,EAAG,iCAAiC;gBAC9C,GAAG,EAAE,GAAG,CAAK,yBAAyB;aACvC;YACD,UAAU,EAAE;gBACV,UAAU,EAAE,GAAG,EAAE,uCAAuC;gBACxD,KAAK,EAAE,GAAG,CAAO,8CAA8C;aAChE;YACD,MAAM,EAAE,EAAE;YACV,eAAe,EAAE;gBACf,4CAA4C;gBAC5C,mCAAmC;aACpC;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,UAAgC;QACpE,oCAAoC;QACpC,OAAO;YACL,QAAQ,EAAE,SAAS;YACnB,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,YAAY;YACvB,SAAS,EAAE,aAAa;YACxB,MAAM,EAAE,EAAE;YACV,eAAe,EAAE;gBACf,6DAA6D;gBAC7D,kDAAkD;aACnD;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,8BAA8B,CAAC,UAAgC;QAC3E,sDAAsD;QACtD,OAAO;YACL,iBAAiB,EAAE,MAAM;YACzB,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE,SAAS;YACpB,QAAQ,EAAE,cAAc;YACxB,QAAQ,EAAE,aAAa;YACvB,WAAW,EAAE,aAAa;YAC1B,MAAM,EAAE,EAAE;YACV,eAAe,EAAE;gBACf,oEAAoE;gBACpE,+DAA+D;aAChE;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAgC;QAC/D,+BAA+B;QAC/B,OAAO;YACL,UAAU,EAAE,UAAU;YACtB,OAAO,EAAE,MAAM;YACf,WAAW,EAAE,MAAM;YACnB,eAAe,EAAE,MAAM;YACvB,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,YAAY;YACrB,MAAM,EAAE,EAAE;YACV,eAAe,EAAE;gBACf,wCAAwC;gBACxC,mCAAmC;aACpC;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,UAAgC;QACpE,mCAAmC;QACnC,OAAO;YACL,UAAU,EAAE,UAAU,EAAE,2BAA2B;YACnD,OAAO,EAAE,YAAY;YACrB,QAAQ,EAAE,aAAa;YACvB,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,UAAU;YACnB,MAAM,EAAE,EAAE;YACV,eAAe,EAAE;gBACf,yCAAyC;gBACzC,+CAA+C;aAChD;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,UAAgC;QACrE,oCAAoC;QACpC,OAAO;YACL,cAAc,EAAE,WAAW;YAC3B,SAAS,EAAE,WAAW;YACtB,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,YAAY;YACpB,WAAW,EAAE,MAAM;YACnB,MAAM,EAAE,EAAE;YACV,eAAe,EAAE;gBACf,2CAA2C;gBAC3C,4CAA4C;aAC7C;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,UAAgC;QACvE,oCAAoC;QACpC,OAAO;YACL,QAAQ,EAAE,WAAW;YACrB,WAAW,EAAE,UAAU;YACvB,gBAAgB,EAAE,WAAW;YAC7B,YAAY,EAAE,aAAa;YAC3B,MAAM,EAAE,EAAE;YACV,eAAe,EAAE;gBACf,sCAAsC;gBACtC,yDAAyD;aAC1D;SACF,CAAC;IACJ,CAAC;IAED,iCAAiC;IAEzB,KAAK,CAAC,oBAAoB,CAAC,IAAY;QAC7C,4BAA4B;QAC5B,MAAM,gBAAgB,GAAG;YACvB,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS;YAC7C,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;SACtD,CAAC;QAEF,OAAO;YACL,KAAK,EAAE,GAAG;YACV,YAAY,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACpE,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;YAC7B,eAAe,EAAE;gBACf,gDAAgD;gBAChD,uDAAuD;aACxD;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAY;QAC3C,0BAA0B;QAC1B,OAAO;YACL,KAAK,EAAE,IAAI;YACX,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;YAC1C,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YAC9C,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;YACxC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;YACpC,MAAM,EAAE;gBACN,8CAA8C;gBAC9C,iDAAiD;aAClD;YACD,eAAe,EAAE;gBACf,yEAAyE;gBACzE,8DAA8D;aAC/D;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAY;QACzC,iCAAiC;QACjC,OAAO;YACL,KAAK,EAAE,GAAG;YACV,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,MAAM,IAAI,CAAC;YACvD,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,MAAM,IAAI,CAAC;YAC9D,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,MAAM,IAAI,CAAC;YACpD,eAAe,EAAE;gBACf,kEAAkE;gBAClE,sDAAsD;aACvD;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,IAAY;QACjD,kCAAkC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QAE9C,OAAO;YACL,KAAK,EAAE,GAAG;YACV,SAAS,EAAE,cAAc;YACzB,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,EAAE,EAAE;YACV,eAAe,EAAE;gBACf,6CAA6C;gBAC7C,mCAAmC;aACpC;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAY;QAC3C,mCAAmC;QACnC,OAAO;YACL,KAAK,EAAE,IAAI;YACX,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACtC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACpC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YACnC,eAAe,EAAE;gBACf,qDAAqD;gBACrD,gDAAgD;aACjD;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,IAAY;QAChD,8BAA8B;QAC9B,OAAO;YACL,KAAK,EAAE,GAAG;YACV,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YACnC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACrC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YACrC,eAAe,EAAE;gBACf,8CAA8C;gBAC9C,wCAAwC;aACzC;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,IAAY;QAChD,qCAAqC;QACrC,OAAO;YACL,KAAK,EAAE,IAAI;YACX,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI;YACjB,eAAe,EAAE;gBACf,uEAAuE;gBACvE,uDAAuD;aACxD;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAY;QAC3C,yBAAyB;QACzB,OAAO;YACL,KAAK,EAAE,GAAG;YACV,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC;YACtD,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YACpC,QAAQ,EAAE,aAAa;YACvB,eAAe,EAAE;gBACf,kCAAkC;gBAClC,oCAAoC;aACrC;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,IAAY;QACpD,2CAA2C;QAC3C,MAAM,MAAM,GAAG;YACb,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YACxD,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YACtD,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5D,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YACtD,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAC3D,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAC3D,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;SACvD,CAAC;QAEF,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IACvE,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,IAAY;QACpD,2CAA2C;QAC3C,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,IAAY;QAClD,2CAA2C;QAC3C,OAAO;YACL,qCAAqC;YACrC,mDAAmD;YACnD,mDAAmD;YACnD,qDAAqD;YACrD,iDAAiD;YACjD,uDAAuD;YACvD,+DAA+D;YAC/D,yCAAyC;SAC1C,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,oCAAoC,CAAC,UAAgC;QACjF,8CAA8C;QAC9C,OAAO;YACL,uDAAuD;YACvD,qEAAqE;YACrE,sDAAsD;YACtD,wDAAwD;YACxD,4DAA4D;YAC5D,0DAA0D;YAC1D,2DAA2D;YAC3D,sDAAsD;SACvD,CAAC;IACJ,CAAC;IAED,8CAA8C;IAEtC,KAAK,CAAC,gBAAgB,CAAC,UAAgC;QAC7D,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,UAAgC;QAC1D,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IAC1D,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,UAAgC;QAChE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,UAAgC;QACxD,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IAC1D,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,UAAgC;QAChE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,UAAgC;QAC7D,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IAC1D,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,UAAgC;QACnE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,UAAgC;QAC3D,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IACzD,CAAC;CACF;AArbD,oDAqbC"}