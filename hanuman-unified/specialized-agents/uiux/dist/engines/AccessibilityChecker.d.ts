import { Logger } from 'winston';
import { ConversionWireframes } from '../types';
/**
 * Vérificateur d'Accessibilité WCAG 2.1 AA/AAA
 *
 * Assure la conformité aux standards d'accessibilité web
 * et génère des recommandations d'amélioration.
 */
export declare class AccessibilityChecker {
    private logger;
    constructor(logger: Logger);
    /**
     * Vérifie la conformité d'accessibilité des wireframes
     */
    checkCompliance(wireframes: ConversionWireframes): Promise<Record<string, any>>;
    /**
     * Valide le code généré pour l'accessibilité
     */
    validateCode(generatedCode: string): Promise<any>;
    private checkWCAG21AA;
    private checkWCAG21AAA;
    private checkColorContrast;
    private checkKeyboardNavigation;
    private checkScreenReaderCompatibility;
    private checkCognitiveLoad;
    private checkMotorAccessibility;
    private checkVisualAccessibility;
    private checkAuditoryAccessibility;
    private validateSemanticHTML;
    private validateAriaLabels;
    private validateAltTexts;
    private validateHeadingStructure;
    private validateFormLabels;
    private validateFocusManagement;
    private validateColorDependency;
    private validateAnimations;
    private calculateAccessibilityScore;
    private identifyAccessibilityIssues;
    private suggestAccessibilityFixes;
    private generateAccessibilityRecommendations;
    private checkPerceivable;
    private checkOperable;
    private checkUnderstandable;
    private checkRobust;
    private checkPerceivableAAA;
    private checkOperableAAA;
    private checkUnderstandableAAA;
    private checkRobustAAA;
}
//# sourceMappingURL=AccessibilityChecker.d.ts.map