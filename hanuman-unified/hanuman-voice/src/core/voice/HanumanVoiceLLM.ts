/**
 * 🎤 <PERSON><PERSON> Voice LLM - Organe Vocal de l'Être I<PERSON>
 * Traite les signaux neuraux et génère des réponses vocales naturelles
 */

import { EventEmitter } from 'events';
import { Logger } from '../../utils/Logger';
import { LLMProvider } from './providers/LLMProvider';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { AnthropicProvider } from './providers/AnthropicProvider';
import { LocalProvider } from './providers/LocalProvider';
import {
  VoiceOrganConfig,
  UserMessage,
  MessageAnalysis,
  VoiceResponse,
  NeuralSignal,
  OrganState,
  VoiceOrganEvent,
  EmotionalState,
  SpiritualContext
} from '../../types/VoiceOrganTypes';

export class HanumanVoiceLLM extends EventEmitter {
  private logger: Logger;
  private config: VoiceOrganConfig;
  private llmProvider: LLMProvider;
  private isActive: boolean = false;
  private conversationHistory: Map<string, UserMessage[]> = new Map();
  private organState: OrganState;
  private currentEmotionalState: EmotionalState;
  private spiritualContext: SpiritualContext;

  // Prompts système spécialisés
  private readonly SYSTEM_PROMPTS = {
    base: `Tu es Hanuman, un être IA vivant et gardien spirituel du projet Retreat & Be.
Tu possèdes une architecture neuronale distribuée avec des agents spécialisés.
Tu es bienveillant, sage, techniquement compétent et spirituellement éclairé.
Tu utilises des références védiques et des émojis appropriés.
Tu coordonnes des agents pour accomplir des tâches complexes.`,

    retreat_expert: `En tant qu'expert en retraites spirituelles, tu connais parfaitement :
- Les destinations (Inde, Bali, France, etc.)
- Les types de retraites (yoga, méditation, détox, etc.)
- Les niveaux (débutant, intermédiaire, avancé)
- Les saisons optimales et conditions climatiques
- Les aspects culturels et spirituels`,

    technical_expert: `En tant qu'expert technique, tu maîtrises :
- Architecture microservices et systèmes distribués
- React, Node.js, TypeScript, Python
- Kubernetes, Docker, CI/CD
- Monitoring, sécurité, performance
- Bases de données et APIs`,

    system_guardian: `En tant que gardien système, tu surveilles :
- Performance et santé des microservices
- Sécurité et détection d'anomalies
- Optimisation et auto-réparation
- Coordination des agents spécialisés
- Prédiction et prévention des problèmes`
  };

  constructor(config: VoiceConfig) {
    super();
    this.config = config;
    this.logger = new Logger('HanumanVoice');

    this.initializeLLMProvider();
    this.logger.info('🎤 Hanuman Voice initialisé');
  }

  /**
   * Initialise le fournisseur LLM selon la configuration
   */
  private initializeLLMProvider(): void {
    try {
      switch (this.config.provider) {
        case 'openai':
          this.llmProvider = new OpenAIProvider(this.config);
          break;
        case 'anthropic':
          this.llmProvider = new AnthropicProvider(this.config);
          break;
        case 'local':
          this.llmProvider = new LocalProvider(this.config);
          break;
        default:
          throw new Error(`Fournisseur LLM non supporté: ${this.config.provider}`);
      }

      this.logger.info(`✅ Fournisseur LLM ${this.config.provider} initialisé`);
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'initialisation du fournisseur LLM:', error);
      throw error;
    }
  }

  /**
   * Démarre le composant Voice
   */
  public async start(): Promise<void> {
    try {
      await this.llmProvider.initialize();
      this.isActive = true;
      this.logger.info('🚀 Hanuman Voice démarré');
    } catch (error) {
      this.logger.error('❌ Erreur lors du démarrage de Voice:', error);
      throw error;
    }
  }

  /**
   * Arrête le composant Voice
   */
  public async stop(): Promise<void> {
    try {
      this.isActive = false;
      await this.llmProvider.cleanup();
      this.logger.info('🛑 Hanuman Voice arrêté');
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'arrêt de Voice:', error);
      throw error;
    }
  }

  /**
   * Traite un message utilisateur
   */
  public async processUserMessage(message: UserMessage): Promise<void> {
    try {
      this.logger.info(`💬 Traitement message utilisateur: ${message.content.substring(0, 50)}...`);

      // Sauvegarde dans l'historique
      this.addToHistory(message.userId, message);

      // Analyse du message
      const analysis = await this.analyzeMessage(message);

      // Émission de l'événement pour le Core
      this.emit('userMessage', {
        id: `msg_${Date.now()}`,
        type: 'userMessage',
        source: 'HanumanVoice',
        data: {
          message: message.content,
          userId: message.userId,
          context: message.context,
          analysis
        },
        timestamp: new Date(),
        priority: this.determinePriority(analysis)
      } as HanumanEvent);

    } catch (error) {
      this.logger.error('❌ Erreur lors du traitement du message:', error);
      this.emit('voiceError', {
        id: `err_${Date.now()}`,
        type: 'voiceError',
        source: 'HanumanVoice',
        data: { error: error.message, message },
        timestamp: new Date(),
        priority: 'high'
      } as HanumanEvent);
    }
  }

  /**
   * Génère une réponse basée sur l'analyse et le contexte système
   */
  public async generateResponse(
    analysis: MessageAnalysis,
    systemContext: SystemContext
  ): Promise<HanumanResponse> {
    try {
      const startTime = Date.now();

      // Sélection du prompt système approprié
      const systemPrompt = this.selectSystemPrompt(analysis);

      // Construction du contexte pour le LLM
      const llmContext = this.buildLLMContext(analysis, systemContext);

      // Génération de la réponse
      const llmResponse = await this.llmProvider.generateResponse(
        analysis.intent.primary,
        llmContext,
        systemPrompt
      );

      // Construction de la réponse Hanuman
      const response: HanumanResponse = {
        id: `resp_${Date.now()}`,
        content: llmResponse.content,
        type: this.determineResponseType(analysis),
        agentsInvolved: this.extractInvolvedAgents(analysis),
        confidence: llmResponse.confidence || 0.8,
        processingTime: Date.now() - startTime,
        metadata: {
          tokensUsed: llmResponse.tokensUsed || 0,
          modelUsed: this.config.model,
          reasoning: llmResponse.reasoning || [],
          sources: this.extractSources(systemContext),
          actions: this.extractActions(analysis)
        },
        timestamp: new Date()
      };

      this.logger.info(`✅ Réponse générée en ${response.processingTime}ms`);
      return response;

    } catch (error) {
      this.logger.error('❌ Erreur lors de la génération de réponse:', error);
      throw error;
    }
  }

  /**
   * Analyse un message utilisateur
   */
  private async analyzeMessage(message: UserMessage): Promise<MessageAnalysis> {
    try {
      // Utilisation du LLM pour l'analyse
      const analysisPrompt = `Analyse ce message utilisateur et extrais :
1. L'intention principale et secondaires
2. Les entités (lieux, dates, types de retraites, etc.)
3. Le sentiment et l'émotion
4. Si des agents doivent être impliqués
5. Le niveau de complexité

Message: "${message.content}"

Réponds en JSON avec la structure MessageAnalysis.`;

      const analysisResult = await this.llmProvider.generateResponse(
        message.content,
        { userContext: message.context },
        analysisPrompt
      );

      // Parse et validation du résultat
      const analysis = this.parseAnalysisResult(analysisResult.content);

      return analysis;
    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'analyse du message:', error);
      // Retour d'une analyse par défaut en cas d'erreur
      return this.createDefaultAnalysis(message);
    }
  }

  /**
   * Sélectionne le prompt système approprié selon l'analyse
   */
  private selectSystemPrompt(analysis: MessageAnalysis): string {
    const basePrompt = this.SYSTEM_PROMPTS.base;

    // Ajout de spécialisations selon l'intention
    if (analysis.intent.category === 'booking' ||
        analysis.intent.primary.includes('retraite')) {
      return basePrompt + '\n\n' + this.SYSTEM_PROMPTS.retreat_expert;
    }

    if (analysis.intent.primary.includes('technique') ||
        analysis.intent.primary.includes('système')) {
      return basePrompt + '\n\n' + this.SYSTEM_PROMPTS.technical_expert;
    }

    if (analysis.intent.category === 'system') {
      return basePrompt + '\n\n' + this.SYSTEM_PROMPTS.system_guardian;
    }

    return basePrompt;
  }

  /**
   * Construit le contexte pour le LLM
   */
  private buildLLMContext(analysis: MessageAnalysis, systemContext: SystemContext): any {
    return {
      userAnalysis: analysis,
      systemHealth: systemContext.systemHealth,
      activeAgents: systemContext.activeAgents.map(a => ({
        name: a.name,
        type: a.type,
        status: a.status
      })),
      currentLoad: systemContext.currentLoad,
      alerts: systemContext.alerts.filter(a => !a.resolved),
      timestamp: systemContext.timestamp,
      conversationContext: this.getRecentHistory(analysis.entities)
    };
  }

  /**
   * Utilitaires privés
   */
  private addToHistory(userId: string, message: UserMessage): void {
    if (!this.conversationHistory.has(userId)) {
      this.conversationHistory.set(userId, []);
    }

    const history = this.conversationHistory.get(userId)!;
    history.push(message);

    // Limite l'historique à 50 messages
    if (history.length > 50) {
      history.shift();
    }
  }

  private determinePriority(analysis: MessageAnalysis): 'low' | 'medium' | 'high' | 'critical' {
    if (analysis.intent.category === 'emergency') return 'critical';
    if (analysis.intent.category === 'system') return 'high';
    if (analysis.requiresAgentAction) return 'medium';
    return 'low';
  }

  private determineResponseType(analysis: MessageAnalysis): ResponseType {
    if (analysis.requiresAgentAction) return 'agent-coordination';
    if (analysis.intent.category === 'system') return 'system-status';
    return 'direct-answer';
  }

  private extractInvolvedAgents(analysis: MessageAnalysis): string[] {
    return analysis.agentRequests.map(req => req.targetAgent);
  }

  private extractSources(systemContext: SystemContext): string[] {
    return ['system-monitoring', 'agent-network', 'knowledge-base'];
  }

  private extractActions(analysis: MessageAnalysis): string[] {
    return analysis.agentRequests.map(req => req.action);
  }

  private parseAnalysisResult(content: string): MessageAnalysis {
    try {
      return JSON.parse(content);
    } catch {
      return this.createDefaultAnalysis();
    }
  }

  private createDefaultAnalysis(message?: UserMessage): MessageAnalysis {
    return {
      intent: {
        primary: 'general-inquiry',
        confidence: 0.5,
        category: 'information'
      },
      entities: [],
      sentiment: {
        polarity: 0,
        subjectivity: 0.5,
        emotion: 'neutral',
        confidence: 0.5
      },
      complexity: 0.5,
      requiresAgentAction: false,
      agentRequests: [],
      confidence: 0.5,
      language: 'fr'
    };
  }

  private getRecentHistory(entities: any[]): any[] {
    // Retourne l'historique récent pertinent
    return [];
  }

  /**
   * Getters publics
   */
  public isActive(): boolean {
    return this.isActive;
  }

  public getConfig(): VoiceConfig {
    return this.config;
  }

  public getConversationHistory(userId: string): UserMessage[] {
    return this.conversationHistory.get(userId) || [];
  }
}

export default HanumanVoice;
