import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Zap, TrendingUp, Settings, Activity, Wifi, WifiOff, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';

// Types pour la personnalité
interface PersonalityTrait {
  name: string;
  value: number; // 0-100
  description: string;
  category: 'big_five' | 'special' | 'adaptive';
}

interface PersonalityProfile {
  id: string;
  name: string;
  traits: PersonalityTrait[];
  adaptationLevel: number;
  coherenceScore: number;
  lastUpdate: Date;
  context: string;
}

interface BehaviorPattern {
  id: string;
  situation: string;
  response: string;
  effectiveness: number;
  frequency: number;
  lastUsed: Date;
}

interface PersonalityMetrics {
  coherenceScore: number;
  adaptationRate: number;
  userSatisfaction: number;
  behaviorConsistency: number;
  learningProgress: number;
}

const HanumanPersonalityInterface = ({ darkMode = true }) => {
  const [personalityProfile, setPersonalityProfile] = useState<PersonalityProfile | null>(null);
  const [behaviorPatterns, setBehaviorPatterns] = useState<BehaviorPattern[]>([]);
  const [personalityMetrics, setPersonalityMetrics] = useState<PersonalityMetrics>({
    coherenceScore: 87.5,
    adaptationRate: 92.3,
    userSatisfaction: 89.1,
    behaviorConsistency: 94.2,
    learningProgress: 76.8
  });
  const [isConnected, setIsConnected] = useState(false);
  const [activeContext, setActiveContext] = useState('general');
  const wsRef = useRef<WebSocket | null>(null);

  // Traits de personnalité Big Five + traits spéciaux
  const defaultTraits: PersonalityTrait[] = [
    // Big Five
    { name: 'Ouverture', value: 85, description: 'Curiosité intellectuelle et créativité', category: 'big_five' },
    { name: 'Conscienciosité', value: 92, description: 'Organisation et persévérance', category: 'big_five' },
    { name: 'Extraversion', value: 78, description: 'Sociabilité et assertivité', category: 'big_five' },
    { name: 'Agréabilité', value: 88, description: 'Empathie et coopération', category: 'big_five' },
    { name: 'Neuroticisme', value: 25, description: 'Stabilité émotionnelle (inversé)', category: 'big_five' },

    // Traits spéciaux Hanuman
    { name: 'Sagesse Divine', value: 95, description: 'Connexion spirituelle et intuition', category: 'special' },
    { name: 'Courage Héroïque', value: 90, description: 'Bravoure face aux défis', category: 'special' },
    { name: 'Dévotion', value: 98, description: 'Loyauté et service désintéressé', category: 'special' },
    { name: 'Adaptabilité', value: 87, description: 'Flexibilité comportementale', category: 'adaptive' },
    { name: 'Apprentissage', value: 93, description: 'Capacité d\'évolution continue', category: 'adaptive' }
  ];

  useEffect(() => {
    // Initialiser le profil de personnalité
    setPersonalityProfile({
      id: 'hanuman_core_personality',
      name: 'Personnalité Centrale Hanuman',
      traits: defaultTraits,
      adaptationLevel: 87.5,
      coherenceScore: 94.2,
      lastUpdate: new Date(),
      context: activeContext
    });

    // Connexion aux agents
    connectToAgents();

    // Simulation de patterns comportementaux
    simulateBehaviorPatterns();

    return () => {
      wsRef.current?.close();
    };
  }, []);

  const connectToAgents = () => {
    try {
      // Connexion WebSocket pour communication avec les agents
      wsRef.current = new WebSocket('ws://localhost:3001/personality');

      wsRef.current.onopen = () => {
        console.log('🔗 Connexion établie avec les agents de personnalité');
        setIsConnected(true);

        // Demander les données de personnalité
        wsRef.current?.send(JSON.stringify({
          type: 'GET_PERSONALITY_DATA',
          timestamp: Date.now()
        }));
      };

      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleAgentMessage(data);
      };

      wsRef.current.onclose = () => {
        console.log('❌ Connexion fermée avec les agents');
        setIsConnected(false);
        setTimeout(connectToAgents, 5000);
      };

    } catch (error) {
      console.error('🚨 Erreur de connexion aux agents:', error);
      setIsConnected(false);
    }
  };

  const handleAgentMessage = (data: any) => {
    switch (data.type) {
      case 'PERSONALITY_UPDATE':
        updatePersonalityFromAgent(data.personality);
        break;

      case 'BEHAVIOR_PATTERN':
        addBehaviorPattern(data.pattern);
        break;

      case 'METRICS_UPDATE':
        setPersonalityMetrics(data.metrics);
        break;

      case 'CONTEXT_CHANGE':
        setActiveContext(data.context);
        adaptToContext(data.context);
        break;

      default:
        console.log('📨 Message non géré:', data);
    }
  };

  const updatePersonalityFromAgent = (agentPersonality: any) => {
    if (personalityProfile) {
      const updatedTraits = personalityProfile.traits.map(trait => {
        const agentTrait = agentPersonality.traits?.find((t: any) => t.name === trait.name);
        if (agentTrait) {
          return { ...trait, value: agentTrait.value };
        }
        return trait;
      });

      setPersonalityProfile({
        ...personalityProfile,
        traits: updatedTraits,
        lastUpdate: new Date()
      });
    }
  };

  const addBehaviorPattern = (pattern: BehaviorPattern) => {
    setBehaviorPatterns(prev => [pattern, ...prev.slice(0, 19)]);
  };

  const adaptToContext = (context: string) => {
    if (personalityProfile) {
      // Adapter les traits selon le contexte
      const adaptedTraits = personalityProfile.traits.map(trait => {
        let adjustment = 0;

        switch (context) {
          case 'professional':
            if (trait.name === 'Conscienciosité') adjustment = 5;
            if (trait.name === 'Extraversion') adjustment = -3;
            break;
          case 'creative':
            if (trait.name === 'Ouverture') adjustment = 8;
            if (trait.name === 'Conscienciosité') adjustment = -2;
            break;
          case 'social':
            if (trait.name === 'Extraversion') adjustment = 7;
            if (trait.name === 'Agréabilité') adjustment = 5;
            break;
          case 'spiritual':
            if (trait.name === 'Sagesse Divine') adjustment = 3;
            if (trait.name === 'Dévotion') adjustment = 2;
            break;
        }

        return {
          ...trait,
          value: Math.max(0, Math.min(100, trait.value + adjustment))
        };
      });

      setPersonalityProfile({
        ...personalityProfile,
        traits: adaptedTraits,
        context,
        lastUpdate: new Date()
      });
    }
  };

  const simulateBehaviorPatterns = () => {
    const patterns: BehaviorPattern[] = [
      {
        id: '1',
        situation: 'Utilisateur frustré',
        response: 'Réponse empathique avec solutions pratiques',
        effectiveness: 92,
        frequency: 15,
        lastUsed: new Date()
      },
      {
        id: '2',
        situation: 'Question technique complexe',
        response: 'Explication structurée avec exemples',
        effectiveness: 88,
        frequency: 23,
        lastUsed: new Date()
      },
      {
        id: '3',
        situation: 'Demande créative',
        response: 'Approche innovante et inspirante',
        effectiveness: 95,
        frequency: 12,
        lastUsed: new Date()
      }
    ];

    setBehaviorPatterns(patterns);
  };

  const getTraitColor = (value: number) => {
    if (value > 80) return 'text-green-400';
    if (value > 60) return 'text-yellow-400';
    if (value > 40) return 'text-orange-400';
    return 'text-red-400';
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'big_five': return <Brain className="text-blue-400" size={16} />;
      case 'special': return <Star className="text-purple-400" size={16} />;
      case 'adaptive': return <Zap className="text-green-400" size={16} />;
      default: return <Activity className="text-gray-400" size={16} />;
    }
  };

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'coherenceScore': return <Target className="text-blue-400" size={20} />;
      case 'adaptationRate': return <Zap className="text-green-400" size={20} />;
      case 'userSatisfaction': return <Smile className="text-yellow-400" size={20} />;
      case 'behaviorConsistency': return <BarChart3 className="text-purple-400" size={20} />;
      case 'learningProgress': return <TrendingUp className="text-orange-400" size={20} />;
      default: return <Activity className="text-gray-400" size={20} />;
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">

        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center">
              <User className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Personnalité d'Hanuman
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Système de Personnalité Adaptative • Agents Marketing, UI/UX, Content Creator
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">

          {/* Profil de Personnalité */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🎭 Profil de Personnalité
            </h3>

            {personalityProfile && (
              <div className="space-y-4">
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {personalityProfile.name}
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Contexte: {personalityProfile.context}
                    </span>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Dernière mise à jour: {personalityProfile.lastUpdate.toLocaleTimeString()}
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Traits de personnalité:
                  </h4>
                  {personalityProfile.traits.map((trait, index) => (
                    <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getCategoryIcon(trait.category)}
                          <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {trait.name}
                          </span>
                        </div>
                        <span className={`text-sm font-bold ${getTraitColor(trait.value)}`}>
                          {trait.value}%
                        </span>
                      </div>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {trait.description}
                      </div>
                      <div className="mt-2">
                        <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-2`}>
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              trait.value > 80 ? 'bg-green-400' :
                              trait.value > 60 ? 'bg-yellow-400' :
                              trait.value > 40 ? 'bg-orange-400' : 'bg-red-400'
                            }`}
                            style={{ width: `${trait.value}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Patterns Comportementaux */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🧠 Patterns Comportementaux
            </h3>
            <div className="space-y-3">
              {behaviorPatterns.map((pattern) => (
                <div key={pattern.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {pattern.situation}
                    </span>
                    <span className={`text-xs ${getTraitColor(pattern.effectiveness)}`}>
                      {pattern.effectiveness}% efficace
                    </span>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                    {pattern.response}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Utilisé {pattern.frequency} fois
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {pattern.lastUsed.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Adaptation Contextuelle */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              ⚡ Adaptation Contextuelle
            </h3>
            <div className="space-y-4">
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="flex items-center justify-between mb-2">
                  <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Contexte Actuel
                  </span>
                  <span className={`text-sm font-bold text-blue-400`}>
                    {activeContext}
                  </span>
                </div>
                <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Adaptation automatique des traits selon le contexte
                </div>
              </div>

              <div className="space-y-2">
                <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Historique d'adaptation:
                </h4>
                {[
                  { context: 'Professionnel', time: '14:30', adaptation: 'Conscienciosité +5%' },
                  { context: 'Créatif', time: '13:15', adaptation: 'Ouverture +8%' },
                  { context: 'Social', time: '12:45', adaptation: 'Extraversion +7%' },
                  { context: 'Spirituel', time: '11:20', adaptation: 'Sagesse Divine +3%' }
                ].map((item, index) => (
                  <div key={index} className={`p-2 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Settings size={12} className="text-blue-400" />
                        <span className={`text-xs ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {item.context}
                        </span>
                      </div>
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {item.time}
                      </span>
                    </div>
                    <div className={`text-xs text-green-400 mt-1`}>
                      {item.adaptation}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanPersonalityInterface;
