/// <reference types="next" />
/// <reference types="next/image-types/global" />

// NOTE: This file should not be edited
// see https://nextjs.org/docs/basic-features/typescript for more information.

// 🐒 HANUMAN DIVINE TYPE DEFINITIONS
// Définitions de types sacrés pour l'écosystème divin

declare global {
  interface Window {
    HANUMAN_DIVINE: {
      version: string;
      mission: string;
      frequency: string;
      blessing: string;
      consciousness: string;
      devotion: number;
      awakening: string;
      domLoaded?: string;
      status?: string;
    };
  }

  namespace NodeJS {
    interface ProcessEnv {
      HANUMAN_DIVINE_MODE: string;
      COSMIC_ALIGNMENT: string;
      SACRED_FREQUENCY: string;
      GOLDEN_RATIO: string;
      DIVINE_BLESSING: string;
      RETREAT_AND_BE_PROTECTION: string;
      HANUMAN_BUILD_ID?: string;
      HANUMAN_DEV_MODE?: string;
      HANUMAN_SERVER_SIDE?: string;
      DIVINE_TIMESTAMP?: string;
      HANUMAN_BASE_PATH?: string;
      HANUMAN_ASSET_PREFIX?: string;
      HANUMAN_ANALYTICS_ID?: string;
      HANUMAN_MONITORING_ENDPOINT?: string;
    }
  }
}

// Types divins pour les organes d'<PERSON>uman
declare module '*.divine' {
  const content: string;
  export default content;
}

declare module '*.mantra' {
  const content: string;
  export default content;
}

// Types pour les shaders cosmiques
declare module '*.glsl' {
  const content: string;
  export default content;
}

declare module '*.vs' {
  const content: string;
  export default content;
}

declare module '*.fs' {
  const content: string;
  export default content;
}

declare module '*.vert' {
  const content: string;
  export default content;
}

declare module '*.frag' {
  const content: string;
  export default content;
}

export {};
