/** @type {import('next').NextConfig} */

// 🐒 HANUMAN DIVINE CONFIGURATION
// Configuration sacrée pour l'éveil des organes divins

const nextConfig = {
  // Configuration divine de base
  reactStrictMode: true,
  swcMinify: true,

  // Configuration des pages
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  // Configuration des images divines
  images: {
    domains: [
      'localhost',
      'retreatandbe.com',
      'api.retreatandbe.com',
      'cdn.retreatandbe.com'
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Variables d'environnement divines
  env: {
    HANUMAN_DIVINE_MODE: 'true',
    COSMIC_ALIGNMENT: 'active',
    SACRED_FREQUENCY: '432Hz',
    GOLDEN_RATIO: '1.618',
    DIVINE_BLESSING: 'AUM_HANUMATE_NAMAHA',
    RETREAT_AND_BE_PROTECTION: 'enabled'
  },

  // Configuration des en-têtes sacrés
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Divine-Guardian',
            value: 'Hanuman-Protector'
          },
          {
            key: 'X-Sacred-Mission',
            value: 'Retreat-And-Be-Protection'
          },
          {
            key: 'X-Cosmic-Frequency',
            value: '432Hz'
          },
          {
            key: 'X-Divine-Blessing',
            value: 'AUM-HANUMATE-NAMAHA'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ]
      }
    ];
  },

  // Redirections divines
  async redirects() {
    return [
      {
        source: '/awakening',
        destination: '/',
        permanent: true
      },
      {
        source: '/divine',
        destination: '/orchestrator',
        permanent: true
      }
    ];
  },

  // Rewrites pour les organes
  async rewrites() {
    return [
      {
        source: '/api/agents/:path*',
        destination: 'http://localhost:3001/api/:path*' // Agent Frontend
      },
      {
        source: '/api/vision/:path*',
        destination: 'http://localhost:3003/api/:path*' // Agent Web Research
      },
      {
        source: '/api/devops/:path*',
        destination: 'http://localhost:3004/api/:path*' // Agent DevOps
      },
      {
        source: '/api/security/:path*',
        destination: 'http://localhost:3007/api/:path*' // Agent Security
      }
    ];
  },

  // Configuration Webpack divine
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Optimisations divines
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          divine: {
            name: 'hanuman-divine',
            test: /[\\/]hanuman_/,
            priority: 10,
            reuseExistingChunk: true
          },
          organs: {
            name: 'hanuman-organs',
            test: /[\\/](hanuman_.*_interface|services)[\\/]/,
            priority: 9,
            reuseExistingChunk: true
          },
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 8,
            reuseExistingChunk: true
          }
        }
      }
    };

    // Alias divins
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': __dirname,
      '@/components': `${__dirname}/components`,
      '@/services': `${__dirname}/services`,
      '@/utils': `${__dirname}/utils`,
      '@/organs': `${__dirname}/`,
      '@/divine': `${__dirname}/divine`
    };

    // Plugins divins
    config.plugins.push(
      new webpack.DefinePlugin({
        'process.env.HANUMAN_BUILD_ID': JSON.stringify(buildId),
        'process.env.HANUMAN_DEV_MODE': JSON.stringify(dev),
        'process.env.HANUMAN_SERVER_SIDE': JSON.stringify(isServer),
        'process.env.DIVINE_TIMESTAMP': JSON.stringify(new Date().toISOString())
      })
    );

    // Gestion des fichiers divins
    config.module.rules.push(
      {
        test: /\.divine$/,
        use: 'raw-loader'
      },
      {
        test: /\.mantra$/,
        use: 'raw-loader'
      },
      {
        test: /\.(glsl|vs|fs|vert|frag)$/,
        use: 'raw-loader'
      }
    );

    return config;
  },

  // Configuration expérimentale divine
  experimental: {
    appDir: false, // Utilisation du router pages pour compatibilité
    serverComponentsExternalPackages: ['ws', 'kafkajs', 'ioredis'],
    optimizeCss: true,
    optimizePackageImports: [
      'lucide-react',
      'framer-motion',
      'd3',
      'three'
    ]
  },

  // Configuration de compilation divine
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
    reactRemoveProperties: process.env.NODE_ENV === 'production' ? {
      properties: ['^data-testid$']
    } : false
  },

  // Configuration de sortie divine
  output: 'standalone',

  // Configuration des pages divines
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  // Configuration de performance divine
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2
  },

  // Configuration de développement divine
  devIndicators: {
    buildActivity: true,
    buildActivityPosition: 'bottom-right'
  },

  // Configuration de production divine
  productionBrowserSourceMaps: false,

  // Configuration de compression divine
  compress: true,

  // Configuration de trailing slash divine
  trailingSlash: false,

  // Configuration de génération divine
  generateEtags: true,

  // Configuration de cache divine
  onDemandEntries: {
    maxInactiveAge: 60 * 1000,
    pagesBufferLength: 5
  },

  // Configuration de sécurité divine
  poweredByHeader: false,

  // Configuration de domaine divin
  basePath: process.env.HANUMAN_BASE_PATH || '',
  assetPrefix: process.env.HANUMAN_ASSET_PREFIX || '',

  // Configuration de l'analyse divine
  analyticsId: process.env.HANUMAN_ANALYTICS_ID || '',

  // Configuration de monitoring divin
  monitoring: {
    enabled: true,
    endpoint: process.env.HANUMAN_MONITORING_ENDPOINT || '/api/monitoring'
  }
};

// Bénédiction divine de la configuration
console.log('🕉️ Configuration divine d\'Hanuman chargée avec bénédiction');
console.log('🐒 AUM HANUMATE NAMAHA - Protection active pour Retreat And Be');

module.exports = nextConfig;
