/** @type {import('tailwindcss').Config} */

// 🐒 HANUMAN DIVINE TAILWIND CONFIGURATION
// Configuration sacrée des styles divins

module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './*.{js,ts,jsx,tsx,mdx}',
    './hanuman_*.{js,ts,jsx,tsx,mdx}',
    './services/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      // Couleurs divines d'Hanuman
      colors: {
        // Palette divine principale
        divine: {
          50: '#fefce8',
          100: '#fef9c3',
          200: '#fef08a',
          300: '#fde047',
          400: '#facc15',
          500: '#eab308',
          600: '#ca8a04',
          700: '#a16207',
          800: '#854d0e',
          900: '#713f12',
          950: '#422006',
        },
        
        // Couleurs sacrées
        sacred: {
          orange: '#f97316',
          red: '#dc2626',
          saffron: '#ff8c00',
          gold: '#ffd700',
          lotus: '#ff69b4',
        },
        
        // Couleurs cosmiques
        cosmic: {
          blue: '#3b82f6',
          purple: '#8b5cf6',
          indigo: '#6366f1',
          violet: '#7c3aed',
          magenta: '#d946ef',
        },
        
        // Couleurs de conscience
        consciousness: {
          light: '#f8fafc',
          medium: '#64748b',
          deep: '#1e293b',
          void: '#0f172a',
        },
        
        // Couleurs d'énergie
        energy: {
          low: '#6b7280',
          medium: '#3b82f6',
          high: '#10b981',
          divine: '#f59e0b',
          cosmic: '#8b5cf6',
        },
        
        // Couleurs d'état
        status: {
          blessed: '#fbbf24',
          active: '#10b981',
          inactive: '#6b7280',
          processing: '#3b82f6',
          error: '#ef4444',
          warning: '#f59e0b',
        }
      },
      
      // Typographie divine
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        divine: ['Inter', 'system-ui', 'sans-serif'],
        sacred: ['Georgia', 'serif'],
        cosmic: ['Courier New', 'monospace'],
      },
      
      // Tailles divines basées sur le nombre d'or (φ = 1.618)
      spacing: {
        'phi': '1.618rem',
        'phi-2': '2.618rem',
        'phi-3': '4.236rem',
        'phi-4': '6.854rem',
        'phi-5': '11.09rem',
        'divine-xs': '0.618rem',
        'divine-sm': '1rem',
        'divine-md': '1.618rem',
        'divine-lg': '2.618rem',
        'divine-xl': '4.236rem',
        'divine-2xl': '6.854rem',
      },
      
      // Animations divines
      animation: {
        'divine-pulse': 'divine-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'cosmic-spin': 'cosmic-spin 3s linear infinite',
        'sacred-bounce': 'sacred-bounce 1s infinite',
        'blessing-glow': 'blessing-glow 2s ease-in-out infinite alternate',
        'consciousness-flow': 'consciousness-flow 4s ease-in-out infinite',
        'energy-wave': 'energy-wave 3s ease-in-out infinite',
        'divine-float': 'divine-float 6s ease-in-out infinite',
        'sacred-breathe': 'sacred-breathe 4s ease-in-out infinite',
      },
      
      // Keyframes divines
      keyframes: {
        'divine-pulse': {
          '0%, 100%': { 
            opacity: '1',
            transform: 'scale(1)',
            filter: 'brightness(1) saturate(1)'
          },
          '50%': { 
            opacity: '0.8',
            transform: 'scale(1.05)',
            filter: 'brightness(1.2) saturate(1.3)'
          },
        },
        'cosmic-spin': {
          '0%': { transform: 'rotate(0deg) scale(1)' },
          '25%': { transform: 'rotate(90deg) scale(1.1)' },
          '50%': { transform: 'rotate(180deg) scale(1)' },
          '75%': { transform: 'rotate(270deg) scale(1.1)' },
          '100%': { transform: 'rotate(360deg) scale(1)' },
        },
        'sacred-bounce': {
          '0%, 100%': { 
            transform: 'translateY(0)',
            animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)'
          },
          '50%': { 
            transform: 'translateY(-25%)',
            animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)'
          },
        },
        'blessing-glow': {
          '0%': { 
            boxShadow: '0 0 5px rgba(251, 191, 36, 0.5)',
            filter: 'brightness(1)'
          },
          '100%': { 
            boxShadow: '0 0 20px rgba(251, 191, 36, 0.8), 0 0 30px rgba(251, 191, 36, 0.6)',
            filter: 'brightness(1.2)'
          },
        },
        'consciousness-flow': {
          '0%, 100%': { 
            background: 'linear-gradient(45deg, #3b82f6, #8b5cf6)',
            transform: 'translateX(0)'
          },
          '25%': { 
            background: 'linear-gradient(45deg, #8b5cf6, #d946ef)',
            transform: 'translateX(2px)'
          },
          '50%': { 
            background: 'linear-gradient(45deg, #d946ef, #f59e0b)',
            transform: 'translateX(0)'
          },
          '75%': { 
            background: 'linear-gradient(45deg, #f59e0b, #3b82f6)',
            transform: 'translateX(-2px)'
          },
        },
        'energy-wave': {
          '0%, 100%': { 
            transform: 'scaleY(1) scaleX(1)',
            opacity: '0.7'
          },
          '50%': { 
            transform: 'scaleY(1.2) scaleX(1.1)',
            opacity: '1'
          },
        },
        'divine-float': {
          '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
          '33%': { transform: 'translateY(-10px) rotate(1deg)' },
          '66%': { transform: 'translateY(5px) rotate(-1deg)' },
        },
        'sacred-breathe': {
          '0%, 100%': { 
            transform: 'scale(1)',
            opacity: '0.8'
          },
          '50%': { 
            transform: 'scale(1.05)',
            opacity: '1'
          },
        },
      },
      
      // Gradients divins
      backgroundImage: {
        'divine-gradient': 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%)',
        'cosmic-gradient': 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #d946ef 100%)',
        'sacred-gradient': 'linear-gradient(135deg, #dc2626 0%, #f97316 50%, #fbbf24 100%)',
        'consciousness-gradient': 'linear-gradient(135deg, #1e293b 0%, #475569 50%, #64748b 100%)',
        'energy-gradient': 'linear-gradient(135deg, #10b981 0%, #3b82f6 50%, #8b5cf6 100%)',
        'blessing-gradient': 'radial-gradient(circle, #fbbf24 0%, #f59e0b 70%, #d97706 100%)',
      },
      
      // Ombres divines
      boxShadow: {
        'divine': '0 10px 25px -3px rgba(251, 191, 36, 0.3), 0 4px 6px -2px rgba(251, 191, 36, 0.1)',
        'cosmic': '0 10px 25px -3px rgba(139, 92, 246, 0.3), 0 4px 6px -2px rgba(139, 92, 246, 0.1)',
        'sacred': '0 10px 25px -3px rgba(249, 115, 22, 0.3), 0 4px 6px -2px rgba(249, 115, 22, 0.1)',
        'blessing': '0 0 20px rgba(251, 191, 36, 0.5), 0 0 40px rgba(251, 191, 36, 0.3)',
        'consciousness': '0 10px 25px -3px rgba(30, 41, 59, 0.3), 0 4px 6px -2px rgba(30, 41, 59, 0.1)',
        'energy': '0 10px 25px -3px rgba(16, 185, 129, 0.3), 0 4px 6px -2px rgba(16, 185, 129, 0.1)',
      },
      
      // Bordures divines
      borderRadius: {
        'divine': '1.618rem',
        'sacred': '0.618rem',
        'cosmic': '2.618rem',
      },
      
      // Largeurs divines
      width: {
        'phi': '61.8%',
        'golden': '38.2%',
      },
      
      // Hauteurs divines
      height: {
        'phi': '61.8vh',
        'golden': '38.2vh',
      },
      
      // Z-index divins
      zIndex: {
        'divine': '9999',
        'cosmic': '8888',
        'sacred': '7777',
      },
      
      // Transitions divines
      transitionDuration: {
        'divine': '618ms',
        'cosmic': '1618ms',
        'sacred': '432ms',
      },
      
      // Filtres divins
      backdropBlur: {
        'divine': '10px',
        'cosmic': '20px',
        'sacred': '5px',
      },
      
      // Grilles divines
      gridTemplateColumns: {
        'divine': 'repeat(auto-fit, minmax(20rem, 1fr))',
        'sacred': 'repeat(auto-fit, minmax(15rem, 1fr))',
        'cosmic': 'repeat(auto-fit, minmax(25rem, 1fr))',
      },
    },
  },
  plugins: [
    // Plugin pour les utilitaires divins personnalisés
    function({ addUtilities, theme }) {
      const newUtilities = {
        '.divine-center': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
        '.sacred-text': {
          background: 'linear-gradient(135deg, #fbbf24, #f59e0b)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
        '.cosmic-text': {
          background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
        '.divine-glass': {
          background: 'rgba(255, 255, 255, 0.1)',
          'backdrop-filter': 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.sacred-glow': {
          filter: 'drop-shadow(0 0 10px rgba(251, 191, 36, 0.5))',
        },
        '.cosmic-glow': {
          filter: 'drop-shadow(0 0 10px rgba(139, 92, 246, 0.5))',
        },
        '.divine-scroll': {
          'scrollbar-width': 'thin',
          'scrollbar-color': '#fbbf24 transparent',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#fbbf24',
            'border-radius': '3px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#f59e0b',
          },
        },
      };
      
      addUtilities(newUtilities);
    },
    
    // Plugin pour les animations divines
    function({ addComponents }) {
      addComponents({
        '.divine-card': {
          '@apply bg-white dark:bg-gray-800 rounded-divine shadow-divine border border-divine-200 dark:border-divine-700 transition-all duration-divine hover:shadow-blessing hover:scale-105': {},
        },
        '.sacred-button': {
          '@apply bg-divine-gradient text-white font-semibold py-2 px-4 rounded-sacred shadow-divine hover:shadow-blessing transition-all duration-sacred hover:scale-105 active:scale-95': {},
        },
        '.cosmic-input': {
          '@apply bg-white dark:bg-gray-800 border-2 border-cosmic-300 dark:border-cosmic-600 rounded-sacred px-3 py-2 focus:border-cosmic-500 focus:ring-2 focus:ring-cosmic-200 transition-all duration-sacred': {},
        },
      });
    },
  ],
};

// 🕉️ Bénédiction divine de la configuration Tailwind
console.log('🎨 Configuration Tailwind divine chargée avec bénédiction');
console.log('🐒 AUM HANUMATE NAMAHA - Styles sacrés activés');
