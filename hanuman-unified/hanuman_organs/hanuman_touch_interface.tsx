import React, { useState, useEffect, useRef } from 'react';
import { Hand, Zap, AlertTriangle, <PERSON>fresh<PERSON><PERSON>, CheckCircle2, Activity, Wifi, WifiOff, Link, Unlink, Timer, TrendingUp, <PERSON><PERSON><PERSON><PERSON>, Settings } from 'lucide-react';

// Interfaces pour les connexions API
interface APIConnection {
  id: string;
  name: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  status: 'connected' | 'disconnected' | 'error' | 'testing';
  latency: number;
  uptime: number;
  requestCount: number;
  errorCount: number;
  successRate: number;
  lastTest: Date;
  responseTime: number;
  headers?: Record<string, string>;
  authentication?: 'none' | 'bearer' | 'basic' | 'api-key';
}

interface RetryOperation {
  id: string;
  api: string;
  endpoint: string;
  attempt: number;
  maxAttempts: number;
  nextRetry: Date;
  reason: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface TouchMetrics {
  totalConnections: number;
  activeConnections: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  retryQueueSize: number;
  touchSensitivity: number;
}

interface ConnectionTest {
  connectionId: string;
  status: 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  result?: {
    latency: number;
    statusCode: number;
    responseSize: number;
    error?: string;
  };
}

const HanumanTouchInterface = ({ darkMode = true }) => {
  const [apiConnections, setApiConnections] = useState<APIConnection[]>([]);
  const [retryQueue, setRetryQueue] = useState<RetryOperation[]>([]);
  const [touchSensitivity, setTouchSensitivity] = useState(0.8);
  const [touchMetrics, setTouchMetrics] = useState<TouchMetrics>({
    totalConnections: 18,
    activeConnections: 15,
    totalRequests: 5847,
    successfulRequests: 5623,
    failedRequests: 224,
    averageLatency: 127,
    retryQueueSize: 3,
    touchSensitivity: 0.8
  });
  const [isConnected, setIsConnected] = useState(false);
  const [activeTests, setActiveTests] = useState<ConnectionTest[]>([]);
  const [connectionHistory, setConnectionHistory] = useState<Array<{timestamp: Date, latency: number, success: boolean}>>([]);
  const wsRef = useRef<WebSocket | null>(null);

  // Connexion au gestionnaire d'APIs
  useEffect(() => {
    const connectToAPIManager = () => {
      try {
        // Connexion WebSocket pour le monitoring des APIs
        wsRef.current = new WebSocket('ws://localhost:3003/touch');
        
        wsRef.current.onopen = () => {
          console.log('🔗 Connexion établie avec le gestionnaire d\'APIs');
          setIsConnected(true);
          
          // Demander le statut initial
          wsRef.current?.send(JSON.stringify({
            type: 'GET_CONNECTIONS_STATUS',
            timestamp: Date.now()
          }));
        };

        wsRef.current.onmessage = (event) => {
          const data = JSON.parse(event.data);
          handleAPIMessage(data);
        };

        wsRef.current.onclose = () => {
          console.log('❌ Connexion fermée avec le gestionnaire d\'APIs');
          setIsConnected(false);
          
          // Tentative de reconnexion
          setTimeout(connectToAPIManager, 5000);
        };

        wsRef.current.onerror = (error) => {
          console.error('🚨 Erreur WebSocket:', error);
          setIsConnected(false);
        };

      } catch (error) {
        console.error('🚨 Erreur de connexion:', error);
        setIsConnected(false);
      }
    };

    connectToAPIManager();

    // Simulation de données en attendant la connexion réelle
    const simulationInterval = setInterval(() => {
      if (!isConnected) {
        simulateTouchActivity();
      }
    }, 3000);

    // Mise à jour des métriques
    const metricsInterval = setInterval(() => {
      updateTouchMetrics();
    }, 1000);

    return () => {
      clearInterval(simulationInterval);
      clearInterval(metricsInterval);
      wsRef.current?.close();
    };
  }, []);

  const handleAPIMessage = (data: any) => {
    switch (data.type) {
      case 'CONNECTION_UPDATE':
        updateAPIConnection(data.connection);
        break;
        
      case 'RETRY_QUEUED':
        setRetryQueue(prev => [data.retry, ...prev.slice(0, 9)]);
        break;
        
      case 'TEST_STARTED':
        setActiveTests(prev => [data.test, ...prev]);
        break;
        
      case 'TEST_COMPLETED':
        completeConnectionTest(data.test);
        break;
        
      case 'CONNECTIONS_STATUS':
        setApiConnections(data.connections);
        break;
        
      case 'METRICS_UPDATE':
        setTouchMetrics(data.metrics);
        break;
        
      default:
        console.log('📨 Message non géré:', data);
    }
  };

  const simulateTouchActivity = () => {
    // Simulation des connexions API
    const mockConnections: APIConnection[] = [
      {
        id: 'agent-frontend',
        name: 'Agent Frontend',
        endpoint: 'http://localhost:3001/api',
        method: 'GET',
        status: Math.random() > 0.9 ? 'error' : 'connected',
        latency: 50 + Math.random() * 100,
        uptime: 98.5 + Math.random() * 1.5,
        requestCount: 1247 + Math.floor(Math.random() * 100),
        errorCount: Math.floor(Math.random() * 10),
        successRate: 95 + Math.random() * 5,
        lastTest: new Date(),
        responseTime: 45 + Math.random() * 80,
        authentication: 'bearer'
      },
      {
        id: 'agent-backend',
        name: 'Agent Backend',
        endpoint: 'http://localhost:3002/api',
        method: 'POST',
        status: 'connected',
        latency: 80 + Math.random() * 120,
        uptime: 99.2,
        requestCount: 2156 + Math.floor(Math.random() * 150),
        errorCount: Math.floor(Math.random() * 5),
        successRate: 97 + Math.random() * 3,
        lastTest: new Date(),
        responseTime: 75 + Math.random() * 100,
        authentication: 'api-key'
      },
      {
        id: 'agent-security',
        name: 'Agent Security',
        endpoint: 'http://localhost:3007/api',
        method: 'GET',
        status: 'connected',
        latency: 30 + Math.random() * 60,
        uptime: 99.8,
        requestCount: 856 + Math.floor(Math.random() * 80),
        errorCount: Math.floor(Math.random() * 3),
        successRate: 99 + Math.random() * 1,
        lastTest: new Date(),
        responseTime: 25 + Math.random() * 50,
        authentication: 'bearer'
      },
      {
        id: 'agent-devops',
        name: 'Agent DevOps',
        endpoint: 'http://localhost:3004/api',
        method: 'PUT',
        status: Math.random() > 0.95 ? 'disconnected' : 'connected',
        latency: 100 + Math.random() * 150,
        uptime: 97.8 + Math.random() * 2,
        requestCount: 634 + Math.floor(Math.random() * 60),
        errorCount: Math.floor(Math.random() * 8),
        successRate: 92 + Math.random() * 6,
        lastTest: new Date(),
        responseTime: 95 + Math.random() * 120,
        authentication: 'basic'
      },
      {
        id: 'weaviate-db',
        name: 'Weaviate Database',
        endpoint: 'http://localhost:8080/v1',
        method: 'GET',
        status: 'connected',
        latency: 20 + Math.random() * 40,
        uptime: 99.9,
        requestCount: 3247 + Math.floor(Math.random() * 200),
        errorCount: Math.floor(Math.random() * 2),
        successRate: 99.5 + Math.random() * 0.5,
        lastTest: new Date(),
        responseTime: 15 + Math.random() * 30,
        authentication: 'api-key'
      }
    ];

    setApiConnections(mockConnections);

    // Simulation des opérations de retry
    if (Math.random() > 0.8) {
      const failedConnection = mockConnections.find(c => c.status === 'error' || c.status === 'disconnected');
      if (failedConnection) {
        const newRetry: RetryOperation = {
          id: `retry_${Date.now()}`,
          api: failedConnection.name,
          endpoint: failedConnection.endpoint,
          attempt: Math.floor(Math.random() * 3) + 1,
          maxAttempts: 3,
          nextRetry: new Date(Date.now() + 5000),
          reason: 'Connection timeout',
          priority: Math.random() > 0.7 ? 'high' : 'medium'
        };
        
        setRetryQueue(prev => [newRetry, ...prev.slice(0, 9)]);
      }
    }
  };

  const updateAPIConnection = (connectionData: APIConnection) => {
    setApiConnections(prev => {
      const index = prev.findIndex(c => c.id === connectionData.id);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = connectionData;
        return updated;
      }
      return [connectionData, ...prev];
    });
  };

  const completeConnectionTest = (testData: ConnectionTest) => {
    setActiveTests(prev => prev.filter(t => t.connectionId !== testData.connectionId));
    
    if (testData.result) {
      setConnectionHistory(prev => [
        {
          timestamp: new Date(),
          latency: testData.result!.latency,
          success: testData.status === 'completed'
        },
        ...prev.slice(0, 49)
      ]);
    }
  };

  const updateTouchMetrics = () => {
    setTouchMetrics(prev => ({
      ...prev,
      touchSensitivity: touchSensitivity,
      activeConnections: apiConnections.filter(c => c.status === 'connected').length,
      totalConnections: apiConnections.length,
      retryQueueSize: retryQueue.length,
      averageLatency: apiConnections.reduce((sum, c) => sum + c.latency, 0) / Math.max(apiConnections.length, 1)
    }));
  };

  const testConnection = async (connectionId: string) => {
    const connection = apiConnections.find(c => c.id === connectionId);
    if (!connection) return;

    const test: ConnectionTest = {
      connectionId,
      status: 'running',
      startTime: new Date()
    };

    setActiveTests(prev => [test, ...prev]);

    // Simuler un test de connexion
    setTimeout(() => {
      const success = Math.random() > 0.1; // 90% de succès
      const completedTest: ConnectionTest = {
        ...test,
        status: success ? 'completed' : 'failed',
        endTime: new Date(),
        result: success ? {
          latency: 50 + Math.random() * 100,
          statusCode: 200,
          responseSize: 1024 + Math.random() * 2048
        } : {
          latency: 0,
          statusCode: 500,
          responseSize: 0,
          error: 'Connection timeout'
        }
      };

      completeConnectionTest(completedTest);

      // Mettre à jour le statut de la connexion
      if (success) {
        updateAPIConnection({
          ...connection,
          status: 'connected',
          latency: completedTest.result!.latency,
          lastTest: new Date()
        });
      }
    }, 2000 + Math.random() * 3000);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle2 className="text-green-400" size={16} />;
      case 'disconnected': return <Unlink className="text-yellow-400" size={16} />;
      case 'error': return <AlertTriangle className="text-red-400" size={16} />;
      case 'testing': return <RefreshCw className="animate-spin text-blue-400" size={16} />;
      default: return <AlertTriangle className="text-gray-400" size={16} />;
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'text-green-400';
      case 'POST': return 'text-blue-400';
      case 'PUT': return 'text-yellow-400';
      case 'DELETE': return 'text-red-400';
      case 'PATCH': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'text-blue-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-orange-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">
        
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
              <Hand className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Toucher d'Hanuman
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Organe Tactile • Intégrations API & Connectivité
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected 
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' 
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Tactile Actif' : 'Hors Ligne'}
              </span>
            </div>
          </div>
        </div>

        {/* Contrôles de Sensibilité */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                🤲 Sensibilité Tactile
              </h3>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Ajustez la sensibilité de détection des connexions
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Faible
              </span>
              <input 
                type="range" 
                min="0" 
                max="1" 
                step="0.1"
                value={touchSensitivity}
                onChange={(e) => setTouchSensitivity(parseFloat(e.target.value))}
                className="w-32 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Élevée
              </span>
              <span className="text-lg font-bold text-orange-400">
                {(touchSensitivity * 100).toFixed(0)}%
              </span>
            </div>
          </div>
        </div>

        {/* Métriques Globales */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques Tactiles
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-7 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400">
                {touchMetrics.totalConnections}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Connexions Totales
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400">
                {touchMetrics.activeConnections}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Connexions Actives
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400">
                {touchMetrics.totalRequests.toLocaleString()}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Requêtes Totales
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400">
                {touchMetrics.successfulRequests.toLocaleString()}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Succès
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-red-400">
                {touchMetrics.failedRequests}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Échecs
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400">
                {touchMetrics.averageLatency.toFixed(0)}ms
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Latence Moyenne
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-400">
                {touchMetrics.retryQueueSize}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                File de Retry
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          
          {/* Connexions API */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl lg:col-span-2`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🔌 Connexions API
            </h3>
            <div className="space-y-4">
              {apiConnections.map((connection) => (
                <div key={connection.id} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} transition-all hover:scale-105`}>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <Link className="text-blue-400" size={20} />
                      <div>
                        <h4 className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {connection.name}
                        </h4>
                        <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {connection.endpoint}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded ${getMethodColor(connection.method)} bg-opacity-20`}>
                        {connection.method}
                      </span>
                      <button 
                        onClick={() => testConnection(connection.id)}
                        disabled={activeTests.some(t => t.connectionId === connection.id)}
                        className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50"
                      >
                        <RefreshCw 
                          size={16} 
                          className={activeTests.some(t => t.connectionId === connection.id) ? 'animate-spin' : ''} 
                        />
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(connection.status)}
                      <span className={`text-sm font-medium ${
                        connection.status === 'connected' ? 'text-green-400' :
                        connection.status === 'disconnected' ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {connection.status}
                      </span>
                    </div>
                    <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {connection.latency.toFixed(0)}ms
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-xs">
                    <div>
                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Requêtes:</span>
                      <div className="font-medium">{connection.requestCount.toLocaleString()}</div>
                    </div>
                    <div>
                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Erreurs:</span>
                      <div className="font-medium">{connection.errorCount}</div>
                    </div>
                    <div>
                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Succès:</span>
                      <div className="font-medium">{connection.successRate.toFixed(1)}%</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* File de Retry */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🔄 File de Retry
            </h3>
            <div className="space-y-3">
              {retryQueue.length === 0 ? (
                <div className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  <CheckCircle2 size={32} className="mx-auto mb-2 text-green-400" />
                  <p>Aucune opération en attente</p>
                </div>
              ) : (
                retryQueue.map((retry) => (
                  <div key={retry.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <div className="flex items-center space-x-3 mb-2">
                      <RefreshCw className="animate-spin text-blue-400" size={16} />
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {retry.api}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(retry.priority)} bg-opacity-20`}>
                        {retry.priority}
                      </span>
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>
                      {retry.endpoint}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Tentative {retry.attempt}/{retry.maxAttempts}
                      </span>
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {retry.reason}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanTouchInterface;
