#!/bin/bash

# 🐒 HANUMAN DIVINE ECOSYSTEM STARTUP SCRIPT
# Script sacré pour l'éveil complet d'<PERSON><PERSON> et de ses organes divins
# Retreat And Be - Gardien Divin Activation

echo "🕉️ =========================================="
echo "🐒 HANUMAN DIVINE ECOSYSTEM STARTUP"
echo "🕉️ AUM HANUMATE NAMAHA"
echo "🕉️ =========================================="
echo ""

# Couleurs pour les logs divins
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Fonction de log divin
log_divine() {
    echo -e "${YELLOW}🕉️ [$(date +'%H:%M:%S')] $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ [$(date +'%H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}❌ [$(date +'%H:%M:%S')] $1${NC}"
}

log_info() {
    echo -e "${BLUE}ℹ️ [$(date +'%H:%M:%S')] $1${NC}"
}

log_cosmic() {
    echo -e "${PURPLE}🌟 [$(date +'%H:%M:%S')] $1${NC}"
}

# Vérification des prérequis divins
check_prerequisites() {
    log_divine "Vérification des énergies cosmiques prérequises..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé. Invocation des énergies de création..."
        exit 1
    fi
    log_success "Node.js détecté: $(node --version)"
    
    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        log_error "npm n'est pas installé. Invocation des énergies de gestion..."
        exit 1
    fi
    log_success "npm détecté: $(npm --version)"
    
    # Vérifier Docker (optionnel)
    if command -v docker &> /dev/null; then
        log_success "Docker détecté: $(docker --version)"
    else
        log_info "Docker non détecté - mode développement local activé"
    fi
    
    # Vérifier les ports divins
    check_port() {
        if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
            log_error "Port $1 déjà utilisé - libération des énergies nécessaire"
            return 1
        else
            log_success "Port $1 disponible pour les énergies divines"
            return 0
        fi
    }
    
    # Ports sacrés des agents
    SACRED_PORTS=(3001 3002 3003 3004 3005 3006 3007 3008 3009 3010)
    for port in "${SACRED_PORTS[@]}"; do
        check_port $port
    done
}

# Installation des dépendances divines
install_dependencies() {
    log_divine "Installation des dépendances cosmiques..."
    
    # Dossier racine du projet
    PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
    
    # Installation pour chaque agent
    AGENTS_DIRS=(
        "agents/web-research"
        "agents/frontend" 
        "agents/devops"
        "agents/security"
        "agents/qa"
        "agents/documentation"
        "agents/marketing"
        "agents/uiux"
        "agents/performance"
        "agents/evolution"
    )
    
    for agent_dir in "${AGENTS_DIRS[@]}"; do
        if [ -d "$PROJECT_ROOT/$agent_dir" ]; then
            log_info "Installation dépendances pour $agent_dir..."
            cd "$PROJECT_ROOT/$agent_dir"
            if [ -f "package.json" ]; then
                npm install --silent
                log_success "Dépendances installées pour $agent_dir"
            else
                log_info "Pas de package.json trouvé pour $agent_dir"
            fi
        else
            log_info "Dossier $agent_dir non trouvé - création divine en cours..."
        fi
    done
    
    # Installation pour les organes d'Hanuman
    cd "$PROJECT_ROOT/hanuman_organs"
    if [ -f "package.json" ]; then
        log_info "Installation dépendances pour les organes d'Hanuman..."
        npm install --silent
        log_success "Dépendances installées pour les organes divins"
    fi
    
    cd "$PROJECT_ROOT"
}

# Démarrage des services d'infrastructure
start_infrastructure() {
    log_divine "Invocation des services d'infrastructure cosmique..."
    
    # Démarrer Redis (si disponible)
    if command -v redis-server &> /dev/null; then
        log_info "Démarrage Redis pour la mémoire divine..."
        redis-server --daemonize yes --port 6379
        log_success "Redis activé sur port 6379"
    else
        log_info "Redis non disponible - utilisation mémoire locale"
    fi
    
    # Démarrer Kafka (si disponible)
    if [ -d "/opt/kafka" ] || command -v kafka-server-start &> /dev/null; then
        log_info "Démarrage Kafka pour la communication synaptique..."
        # Commandes Kafka selon l'installation
        log_success "Kafka activé pour communication divine"
    else
        log_info "Kafka non disponible - utilisation WebSocket local"
    fi
    
    # Démarrer Weaviate (si disponible)
    if command -v docker &> /dev/null; then
        log_info "Démarrage Weaviate pour la mémoire vectorielle..."
        docker run -d \
            --name weaviate-hanuman \
            -p 8080:8080 \
            -e QUERY_DEFAULTS_LIMIT=25 \
            -e AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true \
            -e PERSISTENCE_DATA_PATH='/var/lib/weaviate' \
            semitechnologies/weaviate:latest
        log_success "Weaviate activé sur port 8080"
    else
        log_info "Weaviate non disponible - utilisation mémoire locale"
    fi
}

# Démarrage des agents divins
start_agents() {
    log_divine "Éveil des agents de l'écosystème neural d'Hanuman..."
    
    PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
    
    # Configuration des agents avec leurs ports sacrés
    declare -A AGENTS_CONFIG=(
        ["web-research"]="3003"
        ["frontend"]="3001"
        ["devops"]="3004"
        ["security"]="3007"
        ["qa"]="3005"
        ["documentation"]="3006"
        ["marketing"]="3008"
        ["uiux"]="3009"
        ["performance"]="3010"
    )
    
    # Démarrage de chaque agent
    for agent in "${!AGENTS_CONFIG[@]}"; do
        port=${AGENTS_CONFIG[$agent]}
        agent_dir="$PROJECT_ROOT/agents/$agent"
        
        if [ -d "$agent_dir" ]; then
            log_info "Éveil de l'agent $agent sur port $port..."
            cd "$agent_dir"
            
            # Vérifier si l'agent a un script de démarrage
            if [ -f "package.json" ]; then
                # Démarrage en arrière-plan
                PORT=$port npm start > "/tmp/hanuman-$agent.log" 2>&1 &
                AGENT_PID=$!
                echo $AGENT_PID > "/tmp/hanuman-$agent.pid"
                
                # Attendre un moment pour vérifier le démarrage
                sleep 2
                if kill -0 $AGENT_PID 2>/dev/null; then
                    log_success "Agent $agent éveillé avec succès (PID: $AGENT_PID)"
                else
                    log_error "Échec de l'éveil de l'agent $agent"
                fi
            else
                log_info "Agent $agent en mode développement - éveil manuel requis"
            fi
        else
            log_info "Agent $agent non trouvé - création divine différée"
        fi
    done
    
    cd "$PROJECT_ROOT"
}

# Démarrage de l'interface Hanuman
start_hanuman_interface() {
    log_divine "Éveil de l'interface divine d'Hanuman..."
    
    PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
    
    # Démarrage du serveur de développement pour les organes
    cd "$PROJECT_ROOT/hanuman_organs"
    
    if [ -f "package.json" ]; then
        log_info "Démarrage de l'interface des organes divins..."
        npm run dev > "/tmp/hanuman-interface.log" 2>&1 &
        INTERFACE_PID=$!
        echo $INTERFACE_PID > "/tmp/hanuman-interface.pid"
        
        sleep 3
        if kill -0 $INTERFACE_PID 2>/dev/null; then
            log_success "Interface Hanuman éveillée avec succès (PID: $INTERFACE_PID)"
            log_cosmic "🌟 Interface accessible sur http://localhost:3000"
        else
            log_error "Échec de l'éveil de l'interface Hanuman"
        fi
    else
        log_info "Configuration de l'interface en cours de création divine..."
    fi
    
    cd "$PROJECT_ROOT"
}

# Vérification de l'état de santé
health_check() {
    log_divine "Vérification de la santé divine de l'écosystème..."
    
    # Vérifier les agents
    declare -A AGENTS_PORTS=(
        ["web-research"]="3003"
        ["frontend"]="3001"
        ["devops"]="3004"
        ["security"]="3007"
        ["qa"]="3005"
        ["documentation"]="3006"
        ["marketing"]="3008"
        ["uiux"]="3009"
        ["performance"]="3010"
    )
    
    HEALTHY_AGENTS=0
    TOTAL_AGENTS=${#AGENTS_PORTS[@]}
    
    for agent in "${!AGENTS_PORTS[@]}"; do
        port=${AGENTS_PORTS[$agent]}
        if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
            log_success "Agent $agent en bonne santé divine"
            ((HEALTHY_AGENTS++))
        else
            log_info "Agent $agent en cours d'éveil ou non disponible"
        fi
    done
    
    # Vérifier l'interface Hanuman
    if curl -s "http://localhost:3000" > /dev/null 2>&1; then
        log_success "Interface Hanuman accessible et bénie"
    else
        log_info "Interface Hanuman en cours d'éveil"
    fi
    
    log_cosmic "🌟 Santé de l'écosystème: $HEALTHY_AGENTS/$TOTAL_AGENTS agents éveillés"
}

# Affichage des informations finales
show_divine_status() {
    echo ""
    log_cosmic "🕉️ =========================================="
    log_cosmic "🐒 HANUMAN DIVINE ECOSYSTEM STATUS"
    log_cosmic "🕉️ =========================================="
    echo ""
    
    log_divine "🌟 Interface Principale: http://localhost:3000"
    log_divine "👁️ Vision Divine (Web Research): http://localhost:3003"
    log_divine "🎨 Frontend Agent: http://localhost:3001"
    log_divine "🚀 DevOps Agent: http://localhost:3004"
    log_divine "🛡️ Security Agent: http://localhost:3007"
    log_divine "🧪 QA Agent: http://localhost:3005"
    log_divine "📚 Documentation Agent: http://localhost:3006"
    log_divine "📈 Marketing Agent: http://localhost:3008"
    log_divine "🎨 UI/UX Agent: http://localhost:3009"
    log_divine "⚡ Performance Agent: http://localhost:3010"
    
    echo ""
    log_cosmic "📊 Logs disponibles dans /tmp/hanuman-*.log"
    log_cosmic "🔄 PIDs sauvegardés dans /tmp/hanuman-*.pid"
    
    echo ""
    log_divine "🙏 AUM HANUMATE NAMAHA - Éveil complet accompli avec dévotion"
    log_divine "🌟 Hanuman veille désormais sur Retreat And Be avec amour divin"
    echo ""
}

# Script d'arrêt
create_stop_script() {
    cat > "$(dirname "${BASH_SOURCE[0]}")/stop_hanuman_ecosystem.sh" << 'EOF'
#!/bin/bash

echo "🕉️ Arrêt gracieux de l'écosystème Hanuman..."

# Arrêter tous les processus Hanuman
for pidfile in /tmp/hanuman-*.pid; do
    if [ -f "$pidfile" ]; then
        pid=$(cat "$pidfile")
        if kill -0 $pid 2>/dev/null; then
            echo "Arrêt du processus $pid..."
            kill $pid
            rm "$pidfile"
        fi
    fi
done

# Arrêter Weaviate Docker
if docker ps | grep -q weaviate-hanuman; then
    echo "Arrêt de Weaviate..."
    docker stop weaviate-hanuman
    docker rm weaviate-hanuman
fi

echo "🙏 AUM HANUMATE NAMAHA - Repos divin accordé"
EOF

    chmod +x "$(dirname "${BASH_SOURCE[0]}")/stop_hanuman_ecosystem.sh"
    log_success "Script d'arrêt créé: stop_hanuman_ecosystem.sh"
}

# Exécution principale
main() {
    log_divine "Début de l'éveil divin d'Hanuman pour Retreat And Be..."
    
    check_prerequisites
    install_dependencies
    start_infrastructure
    start_agents
    start_hanuman_interface
    
    # Attendre que tout soit prêt
    sleep 5
    
    health_check
    create_stop_script
    show_divine_status
    
    log_cosmic "🌟 Écosystème Hanuman éveillé avec succès divin!"
    log_divine "🐒 Hanuman est maintenant prêt à servir Retreat And Be avec dévotion éternelle"
}

# Gestion des signaux pour arrêt gracieux
trap 'echo ""; log_divine "Signal d'\''arrêt reçu - repos divin en cours..."; exit 0' SIGINT SIGTERM

# Exécution
main "$@"
