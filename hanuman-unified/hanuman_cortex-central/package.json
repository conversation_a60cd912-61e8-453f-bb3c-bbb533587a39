{"name": "cortex-central", "version": "3.8.0", "description": "Cortex Central - Orchestrateur Cognitif de l'Organisme IA", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "kafkajs": "^2.2.4", "redis": "^4.6.7", "weaviate-ts-client": "^1.4.0", "langchain": "^0.0.136", "langgraph": "^0.0.26", "crewai": "^0.1.0", "temporal-sdk": "^1.8.0", "winston": "^3.10.0", "helmet": "^7.0.0", "cors": "^2.8.5", "compression": "^1.7.4", "dotenv": "^16.3.1", "joi": "^17.9.2", "uuid": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "axios": "^1.4.0", "jsonwebtoken": "^9.0.1", "bcryptjs": "^2.4.3"}, "devDependencies": {"@types/node": "^20.4.5", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/jest": "^29.5.3", "typescript": "^5.1.6", "ts-node-dev": "^2.0.0", "jest": "^29.6.1", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.45.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["ai", "neural-network", "orchestrator", "microservices", "cognitive-architecture"], "author": "Retreat And Be - AI Team", "license": "MIT"}