import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
export interface NeuralSignal {
    id: string;
    type: NeuralSignalType;
    source: string;
    target?: string;
    payload: any;
    priority: SignalPriority;
    timestamp: Date;
    ttl?: number;
    route?: SignalRoute;
}
export declare enum NeuralSignalType {
    THOUGHT = "thought",
    DECISION = "decision",
    MEMORY_FORMATION = "memory_formation",
    LEARNING = "learning",
    SENSORY_INPUT = "sensory_input",
    PERCEPTION = "perception",
    ATTENTION = "attention",
    ACTION_INITIATION = "action_initiation",
    MOTOR_COMMAND = "motor_command",
    COORDINATION = "coordination",
    EMOTION = "emotion",
    MOTIVATION = "motivation",
    REWARD = "reward",
    HEALTH_CHECK = "health_check",
    SYNCHRONIZATION = "synchronization",
    ADAPTATION = "adaptation",
    EVOLUTION = "evolution"
}
export declare enum SignalPriority {
    CRITICAL = 0,// Signaux vitaux (sécurité, erreurs critiques)
    HIGH = 1,// Signaux importants (décisions, actions)
    NORMAL = 2,// Signaux standard (communication normale)
    LOW = 3,// Signaux de fond (monitoring, logs)
    BACKGROUND = 4
}
export interface SignalRoute {
    protocol: 'kafka' | 'nats' | 'redis' | 'direct';
    path: string[];
    latency: number;
    reliability: number;
}
export interface SynapticConnection {
    id: string;
    source: string;
    target: string;
    strength: number;
    type: ConnectionType;
    lastUsed: Date;
    usageCount: number;
    averageLatency: number;
    successRate: number;
    isActive: boolean;
}
export declare enum ConnectionType {
    EXCITATORY = "excitatory",// Connexion activatrice
    INHIBITORY = "inhibitory",// Connexion inhibitrice
    MODULATORY = "modulatory",// Connexion modulatrice
    FEEDBACK = "feedback",// Connexion de rétroaction
    FEEDFORWARD = "feedforward"
}
export interface SynapticNetworkConfig {
    memory: CentralMemory;
    kafkaConfig?: {
        brokers: string[];
        clientId: string;
    };
    natsConfig?: {
        servers: string[];
        reconnect: boolean;
    };
    redisConfig?: {
        host: string;
        port: number;
        cluster?: boolean;
    };
    adaptationEnabled?: boolean;
    pruningEnabled?: boolean;
    learningRate?: number;
}
export interface NetworkTopology {
    nodes: string[];
    connections: SynapticConnection[];
    clusters: NetworkCluster[];
    centralityScores: Map<string, number>;
    pathLengths: Map<string, Map<string, number>>;
}
export interface NetworkCluster {
    id: string;
    nodes: string[];
    density: number;
    function: string;
}
/**
 * Réseau Synaptique - Communication Neuronale Avancée
 *
 * Implémente un système de communication inspiré du cerveau humain
 * avec adaptation dynamique des connexions et routage intelligent.
 */
export declare class SynapticNetwork extends EventEmitter {
    private memory;
    private adaptationEnabled;
    private pruningEnabled;
    private learningRate;
    private isInitialized;
    private connections;
    private signalQueue;
    private routingTable;
    private networkTopology;
    private kafka;
    private nats;
    private redis;
    private networkMetrics;
    constructor(config: SynapticNetworkConfig);
    /**
     * Initialise le réseau synaptique
     */
    initialize(): Promise<void>;
    /**
     * Envoie un signal neural
     */
    sendNeuralSignal(signal: Partial<NeuralSignal>): Promise<void>;
    /**
     * Établit une connexion synaptique
     */
    establishConnection(source: string, target: string, type?: ConnectionType, initialStrength?: number): Promise<void>;
    /**
     * Détermine la route optimale pour un signal
     */
    private determineOptimalRoute;
    /**
     * Calcule la route optimale
     */
    private calculateOptimalRoute;
    /**
     * Trouve le chemin le plus court entre deux nœuds
     */
    private findShortestPath;
    /**
     * Obtient les voisins d'un nœud
     */
    private getNeighbors;
    /**
     * Vérifie si une route est valide
     */
    private isRouteValid;
    /**
     * Démarre le processeur de signaux
     */
    private startSignalProcessor;
    /**
     * Traite la file de signaux
     */
    private processSignalQueue;
    /**
     * Livre un signal à sa destination
     */
    private deliverSignal;
    /**
     * Livre un signal en direct
     */
    private deliverDirectSignal;
    /**
     * Livre un signal via Kafka
     */
    private deliverKafkaSignal;
    /**
     * Livre un signal via NATS
     */
    private deliverNATSSignal;
    /**
     * Livre un signal via Redis
     */
    private deliverRedisSignal;
    /**
     * Met à jour les métriques de connexion
     */
    private updateConnectionMetrics;
    /**
     * Démarre l'adaptation du réseau
     */
    private startNetworkAdaptation;
    /**
     * Adapte la force synaptique
     */
    private adaptSynapticStrength;
    /**
     * Analyse le pattern d'usage d'une connexion
     */
    private analyzeUsagePattern;
    /**
     * Démarre l'élagage des connexions
     */
    private startConnectionPruning;
    /**
     * Élague les connexions faibles
     */
    private pruneWeakConnections;
    /**
     * Initialise les protocoles de communication
     */
    private initializeCommunicationProtocols;
    /**
     * Met à jour la topologie du réseau
     */
    private updateNetworkTopology;
    /**
     * Détecte les clusters dans le réseau
     */
    private detectClusters;
    /**
     * Calcule les scores de centralité
     */
    private calculateCentralityScores;
    /**
     * Charge la topologie depuis la mémoire
     */
    private loadNetworkTopology;
    /**
     * Sauvegarde la topologie en mémoire
     */
    private saveNetworkTopology;
    /**
     * Génère un ID unique pour un signal
     */
    private generateSignalId;
    /**
     * Obtient les métriques du réseau
     */
    getNetworkMetrics(): {
        networkEfficiency: number;
        activeConnections: number;
        averageConnectionStrength: number;
        topologyStats: {
            nodes: number;
            connections: number;
            clusters: number;
        };
        totalSignals: number;
        successfulDeliveries: number;
        failedDeliveries: number;
        averageLatency: number;
        adaptationEvents: number;
    };
    /**
     * Calcule la force moyenne des connexions
     */
    private calculateAverageConnectionStrength;
    /**
     * Arrêt gracieux du réseau
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=SynapticNetwork.d.ts.map