import { EventEmitter } from 'events';
import { SynapticCommunication } from '../communication/SynapticCommunication';
import { CentralMemory } from '../memory/CentralMemory';
import { DecisionEngine } from '../decision/DecisionEngine';
export interface AgentInfo {
    id: string;
    type: string;
    capabilities: string[];
    status: 'online' | 'offline' | 'busy' | 'error';
    lastSeen: Date;
    performance: {
        successRate: number;
        averageResponseTime: number;
        tasksCompleted: number;
    };
    metadata: any;
}
export interface NeuralActivity {
    timestamp: Date;
    type: 'connection' | 'message' | 'decision' | 'learning';
    source: string;
    target?: string;
    data: any;
    intensity: number;
}
export interface NeuralNetworkConfig {
    communication: SynapticCommunication;
    memory: CentralMemory;
    decisionEngine: DecisionEngine;
}
/**
 * Gestionnaire du Réseau Neuronal
 *
 * Gère les connexions et interactions entre tous les agents
 * du système nerveux distribué
 */
export declare class NeuralNetworkManager extends EventEmitter {
    private communication;
    private memory;
    private decisionEngine;
    private connectedAgents;
    private neuralActivity;
    private synapticConnections;
    private isInitialized;
    private activityCleanupInterval;
    constructor(config: NeuralNetworkConfig);
    /**
     * Initialise le gestionnaire de réseau neuronal
     */
    initialize(): Promise<void>;
    /**
     * Configuration des événements de communication
     */
    private setupCommunicationEvents;
    /**
     * Gestion de la connexion d'un agent
     */
    private handleAgentConnection;
    /**
     * Gestion de la déconnexion d'un agent
     */
    private handleAgentDisconnection;
    /**
     * Gestion des mises à jour de statut d'agents
     */
    private handleAgentStatusUpdate;
    /**
     * Gestion des messages d'agents
     */
    private handleAgentMessage;
    /**
     * Gestion des signaux synaptiques
     */
    private handleSynapticSignal;
    /**
     * Établissement des connexions synaptiques pour un agent
     */
    private establishSynapticConnections;
    /**
     * Détermine si deux agents doivent être connectés
     */
    private shouldConnect;
    /**
     * Enregistrement de l'activité neuronale
     */
    private recordNeuralActivity;
    /**
     * Démarrage du monitoring des agents
     */
    private startAgentMonitoring;
    /**
     * Vérification de santé des agents
     */
    private performAgentHealthCheck;
    /**
     * Nettoyage périodique de l'activité neuronale
     */
    private startActivityCleanup;
    /**
     * Découverte des agents existants
     */
    private discoverExistingAgents;
    /**
     * Récupère les agents connectés
     */
    getConnectedAgents(): AgentInfo[];
    /**
     * Récupère un agent par ID
     */
    getAgent(agentId: string): AgentInfo | undefined;
    /**
     * Récupère les agents par type
     */
    getAgentsByType(type: string): AgentInfo[];
    /**
     * Récupère les agents par capacité
     */
    getAgentsByCapability(capability: string): AgentInfo[];
    /**
     * Récupère l'activité neuronale récente
     */
    getNeuralActivity(limit?: number): NeuralActivity[];
    /**
     * Récupère les connexions synaptiques
     */
    getSynapticConnections(): Map<string, Set<string>>;
    /**
     * Récupère le statut du réseau neuronal
     */
    getStatus(): any;
    /**
     * Calcule la distribution des agents par type
     */
    private getAgentDistribution;
    /**
     * Calcule la santé du réseau
     */
    private calculateNetworkHealth;
    /**
     * Arrêt du gestionnaire de réseau neuronal
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=NeuralNetworkManager.d.ts.map