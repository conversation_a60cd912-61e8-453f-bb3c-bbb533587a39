"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CortexCentral = void 0;
const events_1 = require("events");
const uuid_1 = require("uuid");
const logger_1 = require("../utils/logger");
const TaskOrchestrator_1 = require("./TaskOrchestrator");
const CognitiveProcessor_1 = require("./CognitiveProcessor");
const APIGateway_1 = require("../gateway/APIGateway");
const LearningSystem_1 = require("../learning/LearningSystem");
/**
 * Cortex Central - Orchestrateur Cognitif Principal
 *
 * Le Cortex Central est le cerveau principal de l'organisme IA.
 * Il analyse les requêtes, prend des décisions stratégiques,
 * et orchestre les agents spécialisés pour accomplir les tâches.
 */
class CortexCentral extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.activeTasks = new Map();
        this.isInitialized = false;
        this.neuralNetwork = config.neuralNetwork;
        this.memory = config.memory;
        this.decisionEngine = config.decisionEngine;
        this.communication = config.communication;
        this.io = config.io;
        this.startTime = new Date();
        // Initialisation des processeurs cognitifs
        this.taskOrchestrator = new TaskOrchestrator_1.TaskOrchestrator({
            neuralNetwork: this.neuralNetwork,
            memory: this.memory,
            communication: this.communication
        });
        this.cognitiveProcessor = new CognitiveProcessor_1.CognitiveProcessor({
            memory: this.memory,
            decisionEngine: this.decisionEngine
        });
        // Initialisation des nouveaux composants avancés
        this.apiGateway = new APIGateway_1.APIGateway({
            cortex: this,
            memory: this.memory,
            communication: this.communication
        });
        this.learningSystem = new LearningSystem_1.LearningSystem({
            memory: this.memory,
            decisionEngine: this.decisionEngine,
            neuralNetwork: this.neuralNetwork
        });
    }
    /**
     * Initialise le Cortex Central
     */
    async initialize() {
        try {
            logger_1.logger.info('🧠 Initialisation du Cortex Central...');
            // Initialisation des composants cognitifs
            await this.taskOrchestrator.initialize();
            await this.cognitiveProcessor.initialize();
            // Initialisation des nouveaux composants avancés
            await this.apiGateway.initialize();
            await this.learningSystem.initialize();
            // Configuration des événements de communication
            this.setupCommunicationEvents();
            // Configuration des événements neuronaux
            this.setupNeuralEvents();
            // Démarrage du monitoring cognitif
            this.startCognitiveMonitoring();
            this.isInitialized = true;
            logger_1.logger.info('✅ Cortex Central initialisé avec succès');
            this.emit('cortex-initialized', {
                timestamp: new Date(),
                status: 'active'
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation du Cortex Central:', error);
            throw error;
        }
    }
    /**
     * Traite des instructions reçues de l'utilisateur ou d'autres systèmes
     */
    async processInstructions(request) {
        const taskId = (0, uuid_1.v4)();
        logger_1.logger.info(`🎯 Nouvelle instruction reçue [${taskId}]:`, {
            instructions: request.instructions,
            priority: request.priority,
            requester: request.requester
        });
        // Création du statut de tâche initial
        const taskStatus = {
            taskId,
            status: 'analyzing',
            progress: 0,
            assignedAgents: [],
            startTime: new Date()
        };
        this.activeTasks.set(taskId, taskStatus);
        try {
            // Phase 1: Analyse cognitive des instructions
            this.updateTaskStatus(taskId, 'analyzing', 10);
            const cognitiveAnalysis = await this.cognitiveProcessor.analyzeInstructions(request.instructions);
            // Phase 2: Prise de décision stratégique
            this.updateTaskStatus(taskId, 'planning', 25);
            const strategicPlan = await this.decisionEngine.createStrategicPlan({
                analysis: cognitiveAnalysis,
                priority: request.priority,
                context: request.context
            });
            // Phase 3: Orchestration des agents
            this.updateTaskStatus(taskId, 'executing', 40);
            const orchestrationResult = await this.taskOrchestrator.orchestrateTask({
                taskId,
                plan: strategicPlan,
                priority: request.priority
            });
            // Mise à jour des agents assignés
            taskStatus.assignedAgents = orchestrationResult.assignedAgents;
            this.updateTaskStatus(taskId, 'executing', 60);
            // Stockage en mémoire centrale
            await this.memory.storeTaskExecution({
                taskId,
                instructions: request.instructions,
                analysis: cognitiveAnalysis,
                plan: strategicPlan,
                orchestration: orchestrationResult
            });
            // Émission d'événements neuronaux
            this.emit('neural-signal', {
                type: 'task-initiated',
                taskId,
                priority: request.priority,
                assignedAgents: orchestrationResult.assignedAgents,
                timestamp: new Date()
            });
            this.emit('decision-made', {
                type: 'strategic-plan',
                taskId,
                plan: strategicPlan,
                timestamp: new Date()
            });
            logger_1.logger.info(`✅ Tâche ${taskId} orchestrée avec succès`);
            return taskId;
        }
        catch (error) {
            logger_1.logger.error(`❌ Erreur lors du traitement de la tâche ${taskId}:`, error);
            this.updateTaskStatus(taskId, 'failed', 100, error.message);
            throw error;
        }
    }
    /**
     * Récupère le statut d'une tâche
     */
    async getTaskStatus(taskId) {
        const status = this.activeTasks.get(taskId);
        if (!status) {
            // Tentative de récupération depuis la mémoire
            const storedTask = await this.memory.getTaskStatus(taskId);
            return storedTask;
        }
        return status;
    }
    /**
     * Met à jour le statut d'une tâche
     */
    updateTaskStatus(taskId, status, progress, error) {
        const task = this.activeTasks.get(taskId);
        if (task) {
            task.status = status;
            task.progress = progress;
            if (error)
                task.error = error;
            // Estimation du temps de completion
            if (status === 'executing' && progress > 0) {
                const elapsed = Date.now() - task.startTime.getTime();
                const estimatedTotal = (elapsed / progress) * 100;
                task.estimatedCompletion = new Date(task.startTime.getTime() + estimatedTotal);
            }
            this.activeTasks.set(taskId, task);
            // Émission d'événement de mise à jour
            this.emit('task-updated', {
                taskId,
                status: task,
                timestamp: new Date()
            });
        }
    }
    /**
     * Configuration des événements de communication
     */
    setupCommunicationEvents() {
        this.communication.on('agent-message', (data) => {
            this.handleAgentMessage(data);
        });
        this.communication.on('agent-status-update', (data) => {
            this.handleAgentStatusUpdate(data);
        });
        this.communication.on('task-completion', (data) => {
            this.handleTaskCompletion(data);
        });
    }
    /**
     * Configuration des événements neuronaux
     */
    setupNeuralEvents() {
        this.neuralNetwork.on('agent-connected', (agentInfo) => {
            logger_1.logger.info(`🔗 Agent connecté: ${agentInfo.id} (${agentInfo.type})`);
            this.emit('neural-signal', {
                type: 'agent-connected',
                agent: agentInfo,
                timestamp: new Date()
            });
        });
        this.neuralNetwork.on('agent-disconnected', (agentInfo) => {
            logger_1.logger.warn(`❌ Agent déconnecté: ${agentInfo.id} (${agentInfo.type})`);
            this.emit('neural-signal', {
                type: 'agent-disconnected',
                agent: agentInfo,
                timestamp: new Date()
            });
        });
        this.neuralNetwork.on('synaptic-activity', (activity) => {
            this.emit('neural-signal', {
                type: 'synaptic-activity',
                activity,
                timestamp: new Date()
            });
        });
    }
    /**
     * Gestion des messages d'agents
     */
    async handleAgentMessage(data) {
        logger_1.logger.debug(`📨 Message reçu de l'agent ${data.agentId}:`, data.message);
        // Traitement cognitif du message
        const processedMessage = await this.cognitiveProcessor.processAgentMessage(data);
        // Mise à jour de la mémoire
        await this.memory.storeAgentInteraction(data.agentId, processedMessage);
        // Émission d'événement
        this.emit('agent-message-processed', {
            agentId: data.agentId,
            originalMessage: data.message,
            processedMessage,
            timestamp: new Date()
        });
    }
    /**
     * Gestion des mises à jour de statut d'agents
     */
    handleAgentStatusUpdate(data) {
        const { agentId, taskId, status, progress, result } = data;
        if (taskId && this.activeTasks.has(taskId)) {
            const task = this.activeTasks.get(taskId);
            // Mise à jour du progrès global basé sur les agents
            const agentProgress = progress || 0;
            const totalAgents = task.assignedAgents.length;
            const baseProgress = 60; // Progrès de base après orchestration
            const agentContribution = (40 / totalAgents) * (agentProgress / 100);
            this.updateTaskStatus(taskId, 'executing', baseProgress + agentContribution);
        }
        this.emit('neural-signal', {
            type: 'agent-status-update',
            agentId,
            taskId,
            status,
            progress,
            timestamp: new Date()
        });
    }
    /**
     * Gestion de la completion de tâches
     */
    async handleTaskCompletion(data) {
        const { taskId, agentId, result, success } = data;
        if (this.activeTasks.has(taskId)) {
            const task = this.activeTasks.get(taskId);
            if (success) {
                task.result = result;
                this.updateTaskStatus(taskId, 'completed', 100);
                // Stockage du résultat en mémoire
                await this.memory.storeTaskResult(taskId, result);
                logger_1.logger.info(`✅ Tâche ${taskId} complétée avec succès par l'agent ${agentId}`);
            }
            else {
                this.updateTaskStatus(taskId, 'failed', 100, result?.error || 'Échec de l\'agent');
                logger_1.logger.error(`❌ Tâche ${taskId} échouée sur l'agent ${agentId}:`, result?.error);
            }
            // Nettoyage après un délai
            setTimeout(() => {
                this.activeTasks.delete(taskId);
            }, 300000); // 5 minutes
        }
        this.emit('task-completion', {
            taskId,
            agentId,
            success,
            result,
            timestamp: new Date()
        });
    }
    /**
     * Démarrage du monitoring cognitif
     */
    startCognitiveMonitoring() {
        setInterval(() => {
            this.performCognitiveHealthCheck();
        }, 30000); // Toutes les 30 secondes
    }
    /**
     * Vérification de santé cognitive
     */
    async performCognitiveHealthCheck() {
        try {
            const healthMetrics = {
                activeTasks: this.activeTasks.size,
                connectedAgents: this.neuralNetwork.getConnectedAgents().length,
                memoryUsage: await this.memory.getUsageStats(),
                uptime: Date.now() - this.startTime.getTime(),
                timestamp: new Date()
            };
            this.emit('cognitive-health-check', healthMetrics);
            // Auto-optimisation si nécessaire
            if (healthMetrics.activeTasks > 100) {
                logger_1.logger.warn('⚠️ Nombre élevé de tâches actives, optimisation en cours...');
                await this.optimizeTaskLoad();
            }
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la vérification de santé cognitive:', error);
        }
    }
    /**
     * Optimisation de la charge de tâches
     */
    async optimizeTaskLoad() {
        // Implémentation de l'optimisation de charge
        // Priorisation des tâches, redistribution, etc.
        logger_1.logger.info('🔧 Optimisation de la charge de tâches en cours...');
    }
    /**
     * Récupère le statut du cortex
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            activeTasks: this.activeTasks.size,
            uptime: Date.now() - this.startTime.getTime(),
            startTime: this.startTime,
            status: this.isInitialized ? 'active' : 'initializing'
        };
    }
    /**
     * Arrêt gracieux du cortex
     */
    async shutdown() {
        logger_1.logger.info('🛑 Arrêt du Cortex Central...');
        // Arrêt des composants
        await this.taskOrchestrator.shutdown();
        await this.cognitiveProcessor.shutdown();
        // Sauvegarde des tâches actives
        for (const [taskId, task] of this.activeTasks) {
            await this.memory.storeTaskStatus(taskId, task);
        }
        this.isInitialized = false;
        logger_1.logger.info('✅ Cortex Central arrêté');
    }
}
exports.CortexCentral = CortexCentral;
//# sourceMappingURL=CortexCentral.js.map