{"version": 3, "file": "CognitiveProcessor.d.ts", "sourceRoot": "", "sources": ["../../src/core/CognitiveProcessor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAEtC,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAE/E,MAAM,WAAW,eAAe;IAC9B,MAAM,EAAE,aAAa,CAAC;IACtB,cAAc,EAAE,cAAc,CAAC;CAChC;AAED,MAAM,WAAW,mBAAmB;IAClC,UAAU,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IACnD,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,cAAc,EAAE,MAAM,EAAE,CAAC;IACzB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,GAAG,EAAE,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED;;;;;GAKG;AACH,qBAAa,kBAAmB,SAAQ,YAAY;IAClD,OAAO,CAAC,MAAM,CAAgB;IAC9B,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,aAAa,CAAkB;IAGvC,OAAO,CAAC,cAAc,CAAoC;IAC1D,OAAO,CAAC,aAAa,CAAoC;IACzD,OAAO,CAAC,oBAAoB,CAAkC;IAG9D,OAAO,CAAC,gBAAgB,CAKtB;gBAEU,MAAM,EAAE,eAAe;IASnC;;OAEG;IACU,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAmBxC;;OAEG;IACU,mBAAmB,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAyElF;;OAEG;IACU,mBAAmB,CAAC,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IA2BzD;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAiD1B;;OAEG;IACH,OAAO,CAAC,cAAc;IAQtB;;OAEG;YACW,eAAe;IAsC7B;;OAEG;YACW,YAAY;IAuB1B;;OAEG;YACW,cAAc;IA2B5B;;OAEG;YACW,qBAAqB;IA6BnC;;OAEG;YACW,kBAAkB;IAgChC;;OAEG;YACW,gBAAgB;IAwB9B;;OAEG;YACW,mBAAmB;IAwBjC;;OAEG;YACW,iBAAiB;IA2B/B;;OAEG;YACW,mBAAmB;IAqBjC;;OAEG;YACW,gBAAgB;IAkB9B;;OAEG;YACW,qBAAqB;IAmBnC;;OAEG;YACW,eAAe;IAuB7B;;OAEG;YACW,eAAe;IAoB7B;;OAEG;YACW,eAAe;IAM7B;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAW9B;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAW/B;;OAEG;YACW,sBAAsB;IAmBpC;;OAEG;YACW,qBAAqB;IAyBnC;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAmB9B;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAMjC;;OAEG;YACW,2BAA2B;IAqBzC;;OAEG;IACH,OAAO,CAAC,0BAA0B;IAclC;;OAEG;IACI,SAAS,IAAI,GAAG;IAiBvB;;OAEG;IACU,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAQvC"}