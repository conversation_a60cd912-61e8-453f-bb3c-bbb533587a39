"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const socket_io_1 = require("socket.io");
const http_1 = require("http");
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const dotenv_1 = __importDefault(require("dotenv"));
const CortexCentral_1 = require("./core/CortexCentral");
const NeuralNetworkManager_1 = require("./neural/NeuralNetworkManager");
const SynapticCommunication_1 = require("./communication/SynapticCommunication");
const CentralMemory_1 = require("./memory/CentralMemory");
const DecisionEngine_1 = require("./decision/DecisionEngine");
const HealthMonitor_1 = require("./monitoring/HealthMonitor");
const WorkflowOrchestrator_1 = require("./orchestration/WorkflowOrchestrator");
const IntelligenceEngine_1 = require("./intelligence/IntelligenceEngine");
const logger_1 = require("./utils/logger");
const config_1 = require("./config/config");
// Configuration de l'environnement
dotenv_1.default.config();
class CortexCentralServer {
    constructor() {
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.io = new socket_io_1.Server(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });
        this.initializeMiddleware();
        this.initializeComponents();
        this.initializeRoutes();
        this.initializeWebSocket();
    }
    initializeMiddleware() {
        this.app.use((0, helmet_1.default)());
        this.app.use((0, cors_1.default)());
        this.app.use((0, compression_1.default)());
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
    }
    async initializeComponents() {
        try {
            // Initialisation de la mémoire centrale
            this.centralMemory = new CentralMemory_1.CentralMemory({
                weaviateUrl: config_1.config.weaviate.url,
                redisUrl: config_1.config.redis.url
            });
            // Initialisation de la communication synaptique
            this.synapticComm = new SynapticCommunication_1.SynapticCommunication({
                kafkaBrokers: config_1.config.kafka.brokers,
                redisUrl: config_1.config.redis.url
            });
            // Initialisation du moteur de décision
            this.decisionEngine = new DecisionEngine_1.DecisionEngine({
                memory: this.centralMemory,
                communication: this.synapticComm
            });
            // Initialisation du réseau neuronal
            this.neuralNetwork = new NeuralNetworkManager_1.NeuralNetworkManager({
                communication: this.synapticComm,
                memory: this.centralMemory,
                decisionEngine: this.decisionEngine
            });
            // Initialisation de l'orchestrateur de workflows
            this.workflowOrchestrator = new WorkflowOrchestrator_1.WorkflowOrchestrator(this.synapticComm, this.centralMemory, this.decisionEngine, this.neuralNetwork);
            // Initialisation du moteur d'intelligence
            this.intelligenceEngine = new IntelligenceEngine_1.IntelligenceEngine(this.centralMemory, this.neuralNetwork, this.decisionEngine);
            // Initialisation du cortex central
            this.cortexCentral = new CortexCentral_1.CortexCentral({
                neuralNetwork: this.neuralNetwork,
                memory: this.centralMemory,
                decisionEngine: this.decisionEngine,
                communication: this.synapticComm,
                io: this.io
            });
            // Initialisation du monitoring de santé
            this.healthMonitor = new HealthMonitor_1.HealthMonitor({
                cortex: this.cortexCentral,
                neuralNetwork: this.neuralNetwork,
                memory: this.centralMemory
            });
            // Démarrage des composants
            await this.centralMemory.initialize();
            await this.synapticComm.initialize();
            await this.neuralNetwork.initialize();
            await this.intelligenceEngine.initialize();
            await this.cortexCentral.initialize();
            await this.healthMonitor.initialize();
            logger_1.logger.info('🧠 Cortex Central - Tous les composants initialisés avec succès');
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation des composants:', error);
            process.exit(1);
        }
    }
    initializeRoutes() {
        // Route de santé
        this.app.get('/health', (req, res) => {
            const healthStatus = this.healthMonitor.getHealthStatus();
            res.status(healthStatus.status === 'healthy' ? 200 : 503).json(healthStatus);
        });
        // Route d'information sur le cortex
        this.app.get('/cortex/info', (req, res) => {
            res.json({
                name: 'Cortex Central',
                version: '3.8.0',
                status: 'active',
                neuralNetworkMode: config_1.config.neuralNetwork.mode,
                decisionEngine: config_1.config.decisionEngine.enabled,
                connectedAgents: this.neuralNetwork.getConnectedAgents(),
                memoryStats: this.centralMemory.getStats(),
                uptime: process.uptime()
            });
        });
        // Route pour recevoir des instructions
        this.app.post('/cortex/instructions', async (req, res) => {
            try {
                const { instructions, priority = 'normal', requester } = req.body;
                const taskId = await this.cortexCentral.processInstructions({
                    instructions,
                    priority,
                    requester,
                    timestamp: new Date()
                });
                res.json({
                    success: true,
                    taskId,
                    message: 'Instructions reçues et en cours de traitement'
                });
            }
            catch (error) {
                logger_1.logger.error('Erreur lors du traitement des instructions:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erreur lors du traitement des instructions'
                });
            }
        });
        // Route pour obtenir le statut d'une tâche
        this.app.get('/cortex/task/:taskId', async (req, res) => {
            try {
                const { taskId } = req.params;
                const taskStatus = await this.cortexCentral.getTaskStatus(taskId);
                res.json(taskStatus);
            }
            catch (error) {
                logger_1.logger.error('Erreur lors de la récupération du statut de la tâche:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erreur lors de la récupération du statut'
                });
            }
        });
        // Route pour obtenir l'activité neuronale
        this.app.get('/cortex/neural-activity', (req, res) => {
            const activity = this.neuralNetwork.getNeuralActivity();
            res.json(activity);
        });
        // Route pour la visualisation en temps réel
        this.app.get('/cortex/dashboard', (req, res) => {
            res.json({
                cortexCentral: this.cortexCentral.getStatus(),
                neuralNetwork: this.neuralNetwork.getStatus(),
                memory: this.centralMemory.getStatus(),
                communication: this.synapticComm.getStatus(),
                health: this.healthMonitor.getHealthStatus(),
                workflows: this.workflowOrchestrator.getStatus(),
                intelligence: this.intelligenceEngine.getIntelligenceMetrics().slice(-1)[0]
            });
        });
        // Routes pour les workflows
        this.app.get('/cortex/workflows/templates', (req, res) => {
            res.json(this.workflowOrchestrator.getTemplates());
        });
        this.app.post('/cortex/workflows/create', async (req, res) => {
            try {
                const { templateId, context, createdBy } = req.body;
                const workflowId = await this.workflowOrchestrator.createWorkflowFromTemplate(templateId, context, createdBy || 'api');
                res.json({ success: true, workflowId });
            }
            catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        this.app.post('/cortex/workflows/:workflowId/start', async (req, res) => {
            try {
                const { workflowId } = req.params;
                await this.workflowOrchestrator.startWorkflow(workflowId);
                res.json({ success: true, message: 'Workflow démarré' });
            }
            catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        this.app.get('/cortex/workflows/:workflowId', (req, res) => {
            const { workflowId } = req.params;
            const workflow = this.workflowOrchestrator.getWorkflow(workflowId);
            if (workflow) {
                res.json(workflow);
            }
            else {
                res.status(404).json({ error: 'Workflow non trouvé' });
            }
        });
        this.app.get('/cortex/workflows', (req, res) => {
            res.json(this.workflowOrchestrator.getActiveWorkflows());
        });
        // Routes pour l'intelligence artificielle
        this.app.get('/cortex/intelligence/metrics', (req, res) => {
            res.json(this.intelligenceEngine.getIntelligenceMetrics());
        });
        this.app.get('/cortex/intelligence/patterns', (req, res) => {
            res.json(this.intelligenceEngine.getLearningPatterns());
        });
        this.app.get('/cortex/intelligence/optimizations', (req, res) => {
            res.json(this.intelligenceEngine.getOptimizationSuggestions());
        });
        this.app.post('/cortex/intelligence/learn', async (req, res) => {
            try {
                await this.intelligenceEngine.performLearningCycle();
                res.json({ success: true, message: 'Cycle d\'apprentissage terminé' });
            }
            catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        this.app.post('/cortex/intelligence/predict', async (req, res) => {
            try {
                const predictions = await this.intelligenceEngine.performPredictiveAnalysis();
                res.json({ success: true, predictions });
            }
            catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
    }
    initializeWebSocket() {
        this.io.on('connection', (socket) => {
            logger_1.logger.info(`🔗 Nouvelle connexion WebSocket: ${socket.id}`);
            // Inscription aux événements neuronaux
            socket.on('subscribe-neural-activity', () => {
                socket.join('neural-activity');
                logger_1.logger.info(`📡 Client ${socket.id} inscrit aux activités neuronales`);
            });
            // Inscription aux événements de décision
            socket.on('subscribe-decisions', () => {
                socket.join('decisions');
                logger_1.logger.info(`🎯 Client ${socket.id} inscrit aux décisions`);
            });
            // Inscription aux événements de mémoire
            socket.on('subscribe-memory', () => {
                socket.join('memory');
                logger_1.logger.info(`🧠 Client ${socket.id} inscrit aux événements mémoire`);
            });
            socket.on('disconnect', () => {
                logger_1.logger.info(`❌ Déconnexion WebSocket: ${socket.id}`);
            });
        });
        // Configuration des événements en temps réel
        this.cortexCentral.on('neural-signal', (data) => {
            this.io.to('neural-activity').emit('neural-signal', data);
        });
        this.cortexCentral.on('decision-made', (data) => {
            this.io.to('decisions').emit('decision-made', data);
        });
        this.cortexCentral.on('memory-updated', (data) => {
            this.io.to('memory').emit('memory-updated', data);
        });
    }
    async start() {
        const port = config_1.config.server.port || 8080;
        this.server.listen(port, () => {
            logger_1.logger.info(`🚀 Cortex Central démarré sur le port ${port}`);
            logger_1.logger.info(`🌐 Dashboard disponible sur http://localhost:${port}/cortex/dashboard`);
            logger_1.logger.info(`💡 Mode réseau neuronal: ${config_1.config.neuralNetwork.mode}`);
            logger_1.logger.info(`🎯 Moteur de décision: ${config_1.config.decisionEngine.enabled ? 'Activé' : 'Désactivé'}`);
        });
        // Gestion gracieuse de l'arrêt
        process.on('SIGTERM', () => this.gracefulShutdown());
        process.on('SIGINT', () => this.gracefulShutdown());
    }
    async gracefulShutdown() {
        logger_1.logger.info('🛑 Arrêt gracieux du Cortex Central...');
        try {
            await this.cortexCentral.shutdown();
            await this.intelligenceEngine.shutdown();
            await this.healthMonitor.shutdown();
            await this.neuralNetwork.shutdown();
            await this.synapticComm.shutdown();
            await this.centralMemory.shutdown();
            this.server.close(() => {
                logger_1.logger.info('✅ Cortex Central arrêté avec succès');
                process.exit(0);
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'arrêt:', error);
            process.exit(1);
        }
    }
}
// Démarrage du serveur
const cortexServer = new CortexCentralServer();
cortexServer.start().catch((error) => {
    logger_1.logger.error('❌ Erreur fatale lors du démarrage:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map