/**
 * DeploymentManager - Gestionnaire de déploiement pour l'évolution des agents
 * Orchestre le déploiement progressif et sécurisé des mises à jour
 */
import { EventEmitter } from 'events';
import { EvolutionPlan, TrainingResult, DeploymentResult } from './types';
export declare class DeploymentManager extends EventEmitter {
    private logger;
    private activeDeployments;
    private deploymentHistory;
    private rollbackHistory;
    constructor();
    /**
     * Initialise le gestionnaire de déploiement
     */
    initialize(): Promise<void>;
    /**
     * Déploie un plan d'évolution complet
     */
    deployPlan(plan: EvolutionPlan, trainingResults: TrainingResult[]): Promise<DeploymentResult[]>;
    /**
     * Déploie une phase spécifique
     */
    private deployPhase;
    /**
     * Déploie un agent spécifique
     */
    private deployAgent;
    /**
     * Effectue un rollback complet du plan
     */
    rollbackPlan(plan: EvolutionPlan): Promise<void>;
    /**
     * Méthodes de déploiement d'infrastructure
     */
    private stopAgent;
    private startAgent;
    private updateAgentImage;
    private performHealthCheck;
    private backupAgentState;
    private restoreAgentState;
    /**
     * Méthodes de validation
     */
    private validatePreDeployment;
    private validatePostDeployment;
    private validatePhase;
    private validateRollback;
    /**
     * Méthodes utilitaires
     */
    private executeCommand;
    private executeRollbackStep;
    private executeCriteriaValidation;
    private collectPerformanceMetrics;
    private shouldTriggerRollback;
    private waitForStabilization;
    private waitForAgentStop;
    private waitForAgentStart;
    private checkPhasePrerequisites;
    private checkSystemResources;
    private checkDependentServices;
    private validateSystemMetrics;
    /**
     * Méthodes de chargement et configuration
     */
    private loadDeploymentHistory;
    private setupDeploymentInfrastructure;
    /**
     * Getters
     */
    getActiveDeployments(): EvolutionPlan[];
    getDeploymentHistory(): DeploymentResult[];
    isDeploymentActive(planId: string): boolean;
}
//# sourceMappingURL=DeploymentManager.d.ts.map