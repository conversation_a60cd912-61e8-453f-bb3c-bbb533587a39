/**
 * TechnologyScanner - Scanner de technologies pour la détection des innovations
 * Surveille multiple sources pour identifier les nouvelles technologies pertinentes
 */
import { EventEmitter } from 'events';
import { Technology, TechnologySource } from './types';
export declare class TechnologyScanner extends EventEmitter {
    private logger;
    private sources;
    private cache;
    private lastScanResults;
    constructor(sources: TechnologySource[]);
    /**
     * Initialise le scanner
     */
    initialize(): Promise<void>;
    /**
     * Scan toutes les sources actives
     */
    scanAll(): Promise<Technology[]>;
    /**
     * Scan une source spécifique
     */
    private scanSource;
    /**
     * Scan GitHub pour les projets tendance
     */
    private scanGitHub;
    /**
     * Scan NPM pour les packages populaires
     */
    private scanNPM;
    /**
     * Scan StackOverflow pour les technologies émergentes
     */
    private scanStackOverflow;
    /**
     * Scan des blogs techniques
     */
    private scanTechBlogs;
    /**
     * Scan Reddit pour les discussions technologiques
     */
    private scanReddit;
    /**
     * Scan Hacker News
     */
    private scanHackerNews;
    /**
     * Validation d'une source
     */
    private validateSource;
    /**
     * Déduplication et filtrage des technologies
     */
    private deduplicateAndFilter;
    /**
     * Calcul des scores de pertinence
     */
    private calculateRelevanceScores;
    /**
     * Méthodes utilitaires
     */
    private categorizeFromLanguage;
    private categorizeFromTag;
    private assessMaturityFromGitHub;
    private assessMaturityFromNPM;
    private calculateAdoptionRate;
    private calculateNPMAdoptionRate;
    private isRelevantTag;
    private isRelevantTechnology;
    private extractTechnologiesFromText;
    /**
     * Getters
     */
    getLastScanResults(): Technology[];
    getCachedResults(sourceName: string): Technology[] | undefined;
    getActiveSources(): TechnologySource[];
}
//# sourceMappingURL=TechnologyScanner.d.ts.map