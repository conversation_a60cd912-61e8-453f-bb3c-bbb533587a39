"use strict";
/**
 * ValidationSystem - Système de validation pour l'évolution des agents
 * Valide les déploiements et déclenche les rollbacks si nécessaire
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationSystem = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
class ValidationSystem extends events_1.EventEmitter {
    constructor() {
        super();
        this.validationHistory = new Map();
        this.activeValidations = new Set();
        this.validationRules = new Map();
        this.logger = new logger_1.Logger('ValidationSystem');
    }
    /**
     * Initialise le système de validation
     */
    async initialize() {
        this.logger.info('Initializing ValidationSystem...');
        try {
            await this.loadValidationRules();
            await this.setupValidationInfrastructure();
            this.logger.info('ValidationSystem initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize ValidationSystem:', error);
            throw error;
        }
    }
    /**
     * Valide un plan d'évolution complet
     */
    async validatePlan(plan, deploymentResults) {
        const validationId = `validation_${plan.id}_${Date.now()}`;
        if (this.activeValidations.has(plan.id)) {
            throw new Error(`Validation already in progress for plan: ${plan.id}`);
        }
        this.logger.info(`Starting validation for plan: ${plan.name}`);
        this.activeValidations.add(plan.id);
        try {
            const allValidationResults = [];
            // Validation phase par phase
            for (let i = 0; i < plan.phases.length; i++) {
                const phase = plan.phases[i];
                const phaseDeploymentResult = deploymentResults[i];
                if (!phaseDeploymentResult || !phaseDeploymentResult.success) {
                    this.logger.warn(`Skipping validation for failed phase: ${phase.name}`);
                    continue;
                }
                this.logger.info(`Validating phase: ${phase.name}`);
                const phaseValidationResults = await this.validatePhase(plan, phase, phaseDeploymentResult);
                allValidationResults.push(...phaseValidationResults);
                // Vérification des échecs critiques
                const criticalFailures = phaseValidationResults.filter(vr => !vr.passed && vr.criteria.required);
                if (criticalFailures.length > 0) {
                    this.logger.error(`Critical validation failures in phase ${phase.name}`);
                    this.emit('validationFailed', {
                        planId: plan.id,
                        phaseId: phase.id,
                        failures: criticalFailures,
                        reason: 'Critical validation criteria not met'
                    });
                    // Arrêt de la validation en cas d'échec critique
                    break;
                }
            }
            // Validation globale du système
            const systemValidationResults = await this.validateSystemIntegrity(plan, deploymentResults);
            allValidationResults.push(...systemValidationResults);
            // Sauvegarde des résultats
            this.validationHistory.set(plan.id, allValidationResults);
            this.logger.info(`Validation completed for plan: ${plan.name} (${allValidationResults.length} checks)`);
            return allValidationResults;
        }
        catch (error) {
            this.logger.error(`Validation failed for plan ${plan.id}:`, error);
            throw error;
        }
        finally {
            this.activeValidations.delete(plan.id);
        }
    }
    /**
     * Valide une phase spécifique
     */
    async validatePhase(plan, phase, deploymentResult) {
        const results = [];
        // Validation des critères définis pour la phase
        for (const criteria of phase.validationCriteria) {
            try {
                const result = await this.validateCriteria(criteria, deploymentResult);
                results.push(result);
                this.logger.debug(`Validation result for ${criteria.name}: ${result.passed ? 'PASS' : 'FAIL'}`);
            }
            catch (error) {
                this.logger.error(`Validation error for criteria ${criteria.name}:`, error);
                results.push({
                    criteria,
                    passed: false,
                    actualValue: 0,
                    expectedValue: criteria.threshold,
                    message: `Validation error: ${error.message}`,
                    timestamp: new Date(),
                    details: { error: error.message }
                });
            }
        }
        // Validation des métriques de performance
        if (deploymentResult.performanceMetrics) {
            const performanceResults = await this.validatePerformanceMetrics(deploymentResult.performanceMetrics, phase);
            results.push(...performanceResults);
        }
        // Validation de la santé des agents
        const healthResults = await this.validateAgentHealth(deploymentResult.updated);
        results.push(...healthResults);
        return results;
    }
    /**
     * Valide un critère spécifique
     */
    async validateCriteria(criteria, deploymentResult) {
        this.logger.debug(`Validating criteria: ${criteria.name}`);
        try {
            let actualValue;
            // Exécution du test selon le type de critère
            switch (criteria.type) {
                case 'performance':
                    actualValue = await this.measurePerformance(criteria, deploymentResult);
                    break;
                case 'security':
                    actualValue = await this.measureSecurity(criteria, deploymentResult);
                    break;
                case 'functionality':
                    actualValue = await this.measureFunctionality(criteria, deploymentResult);
                    break;
                case 'compatibility':
                    actualValue = await this.measureCompatibility(criteria, deploymentResult);
                    break;
                case 'quality':
                    actualValue = await this.measureQuality(criteria, deploymentResult);
                    break;
                default:
                    throw new Error(`Unknown validation type: ${criteria.type}`);
            }
            // Évaluation du résultat
            const passed = this.evaluateCriteria(criteria, actualValue);
            const message = passed
                ? `${criteria.name} validation passed`
                : `${criteria.name} validation failed: ${actualValue} ${criteria.metric} (threshold: ${criteria.threshold})`;
            return {
                criteria,
                passed,
                actualValue,
                expectedValue: criteria.threshold,
                message,
                timestamp: new Date(),
                details: {
                    testCommand: criteria.testCommand,
                    timeout: criteria.timeout
                }
            };
        }
        catch (error) {
            throw new Error(`Criteria validation failed: ${error.message}`);
        }
    }
    /**
     * Mesure les performances
     */
    async measurePerformance(criteria, deploymentResult) {
        if (criteria.testCommand) {
            return await this.executePerformanceTest(criteria.testCommand, criteria.timeout || 30);
        }
        // Utilisation des métriques de performance du déploiement
        if (deploymentResult.performanceMetrics) {
            const metrics = deploymentResult.performanceMetrics;
            switch (criteria.metric.toLowerCase()) {
                case 'ms':
                case 'response_time':
                    return metrics.responseTime;
                case 'throughput':
                    return metrics.throughput;
                case '%':
                case 'error_rate':
                    return metrics.errorRate;
                case 'cpu_usage':
                    return metrics.cpuUsage;
                case 'memory_usage':
                    return metrics.memoryUsage;
                default:
                    throw new Error(`Unknown performance metric: ${criteria.metric}`);
            }
        }
        throw new Error('No performance data available');
    }
    /**
     * Mesure la sécurité
     */
    async measureSecurity(criteria, deploymentResult) {
        if (criteria.testCommand) {
            return await this.executeSecurityTest(criteria.testCommand, criteria.timeout || 300);
        }
        // Scan de sécurité par défaut
        return await this.performSecurityScan(deploymentResult.updated);
    }
    /**
     * Mesure la fonctionnalité
     */
    async measureFunctionality(criteria, deploymentResult) {
        if (criteria.testCommand) {
            return await this.executeFunctionalTest(criteria.testCommand, criteria.timeout || 600);
        }
        // Tests fonctionnels par défaut
        return await this.performFunctionalTests(deploymentResult.updated);
    }
    /**
     * Mesure la compatibilité
     */
    async measureCompatibility(criteria, deploymentResult) {
        return await this.performCompatibilityTests(deploymentResult.updated);
    }
    /**
     * Mesure la qualité
     */
    async measureQuality(criteria, deploymentResult) {
        return await this.performQualityTests(deploymentResult.updated);
    }
    /**
     * Valide les métriques de performance
     */
    async validatePerformanceMetrics(metrics, phase) {
        const results = [];
        // Validation du temps de réponse
        results.push(this.createMetricValidationResult('Response Time', 'performance', metrics.responseTime, 200, // seuil en ms
        'ms'));
        // Validation du taux d'erreur
        results.push(this.createMetricValidationResult('Error Rate', 'performance', metrics.errorRate, 5, // seuil en %
        '%'));
        // Validation de l'utilisation CPU
        results.push(this.createMetricValidationResult('CPU Usage', 'performance', metrics.cpuUsage, 80, // seuil en %
        '%'));
        // Validation de l'utilisation mémoire
        results.push(this.createMetricValidationResult('Memory Usage', 'performance', metrics.memoryUsage, 85, // seuil en %
        '%'));
        return results;
    }
    /**
     * Valide la santé des agents
     */
    async validateAgentHealth(agentIds) {
        const results = [];
        for (const agentId of agentIds) {
            try {
                const healthScore = await this.checkAgentHealth(agentId);
                results.push(this.createMetricValidationResult(`${agentId} Health`, 'functionality', healthScore, 90, // seuil de santé
                'score'));
            }
            catch (error) {
                results.push({
                    criteria: {
                        name: `${agentId} Health Check`,
                        type: 'functionality',
                        threshold: 90,
                        metric: 'score',
                        required: true
                    },
                    passed: false,
                    actualValue: 0,
                    expectedValue: 90,
                    message: `Health check failed for ${agentId}: ${error.message}`,
                    timestamp: new Date()
                });
            }
        }
        return results;
    }
    /**
     * Valide l'intégrité globale du système
     */
    async validateSystemIntegrity(plan, deploymentResults) {
        const results = [];
        // Validation de la communication inter-agents
        const communicationScore = await this.validateInterAgentCommunication(deploymentResults);
        results.push(this.createMetricValidationResult('Inter-Agent Communication', 'functionality', communicationScore, 95, 'score'));
        // Validation de la cohérence des données
        const dataConsistencyScore = await this.validateDataConsistency(deploymentResults);
        results.push(this.createMetricValidationResult('Data Consistency', 'functionality', dataConsistencyScore, 98, 'score'));
        // Validation de la sécurité globale
        const securityScore = await this.validateGlobalSecurity(deploymentResults);
        results.push(this.createMetricValidationResult('Global Security', 'security', securityScore, 90, 'score'));
        return results;
    }
    /**
     * Méthodes d'exécution des tests
     */
    async executePerformanceTest(command, timeout) {
        // Simulation de l'exécution d'un test de performance
        return new Promise((resolve) => {
            setTimeout(() => {
                // Simulation d'un temps de réponse aléatoire
                resolve(Math.random() * 300);
            }, Math.min(timeout * 100, 5000)); // Simulation rapide
        });
    }
    async executeSecurityTest(command, timeout) {
        // Simulation de l'exécution d'un test de sécurité
        return new Promise((resolve) => {
            setTimeout(() => {
                // Simulation du nombre de vulnérabilités trouvées
                resolve(Math.floor(Math.random() * 3));
            }, Math.min(timeout * 50, 10000));
        });
    }
    async executeFunctionalTest(command, timeout) {
        // Simulation de l'exécution d'un test fonctionnel
        return new Promise((resolve) => {
            setTimeout(() => {
                // Simulation du pourcentage de tests réussis
                resolve(Math.random() * 100);
            }, Math.min(timeout * 100, 30000));
        });
    }
    /**
     * Méthodes de test par défaut
     */
    async performSecurityScan(agentIds) {
        // Simulation d'un scan de sécurité
        return Math.floor(Math.random() * 2); // 0-1 vulnérabilités critiques
    }
    async performFunctionalTests(agentIds) {
        // Simulation de tests fonctionnels
        return 85 + Math.random() * 15; // 85-100% de réussite
    }
    async performCompatibilityTests(agentIds) {
        // Simulation de tests de compatibilité
        return 90 + Math.random() * 10; // 90-100% de compatibilité
    }
    async performQualityTests(agentIds) {
        // Simulation de tests de qualité
        return 80 + Math.random() * 20; // 80-100% de qualité
    }
    async checkAgentHealth(agentId) {
        // Simulation de vérification de santé d'agent
        return 85 + Math.random() * 15; // 85-100% de santé
    }
    async validateInterAgentCommunication(deploymentResults) {
        // Simulation de validation de communication inter-agents
        return 90 + Math.random() * 10;
    }
    async validateDataConsistency(deploymentResults) {
        // Simulation de validation de cohérence des données
        return 95 + Math.random() * 5;
    }
    async validateGlobalSecurity(deploymentResults) {
        // Simulation de validation de sécurité globale
        return 88 + Math.random() * 12;
    }
    /**
     * Méthodes utilitaires
     */
    evaluateCriteria(criteria, actualValue) {
        // Évaluation selon le type de métrique
        switch (criteria.metric.toLowerCase()) {
            case 'critical_vulnerabilities':
            case 'errors':
                return actualValue <= criteria.threshold;
            case '%':
            case 'score':
            case '% passed':
                return actualValue >= criteria.threshold;
            case 'ms':
            case 'response_time':
                return actualValue <= criteria.threshold;
            default:
                return actualValue <= criteria.threshold;
        }
    }
    createMetricValidationResult(name, type, actualValue, threshold, metric) {
        const criteria = {
            name,
            type,
            threshold,
            metric,
            required: true
        };
        const passed = this.evaluateCriteria(criteria, actualValue);
        return {
            criteria,
            passed,
            actualValue,
            expectedValue: threshold,
            message: passed
                ? `${name} validation passed`
                : `${name} validation failed: ${actualValue} ${metric} (threshold: ${threshold})`,
            timestamp: new Date()
        };
    }
    /**
     * Méthodes de chargement et configuration
     */
    async loadValidationRules() {
        // Chargement des règles de validation personnalisées
        this.validationRules.set('performance_baseline', {
            responseTime: 200,
            errorRate: 5,
            cpuUsage: 80,
            memoryUsage: 85
        });
        this.validationRules.set('security_baseline', {
            criticalVulnerabilities: 0,
            highVulnerabilities: 2,
            mediumVulnerabilities: 10
        });
    }
    async setupValidationInfrastructure() {
        // Configuration de l'infrastructure de validation
        this.logger.debug('Setting up validation infrastructure');
    }
    /**
     * Getters
     */
    getValidationHistory(planId) {
        return this.validationHistory.get(planId);
    }
    isValidationActive(planId) {
        return this.activeValidations.has(planId);
    }
    getValidationRules() {
        return new Map(this.validationRules);
    }
}
exports.ValidationSystem = ValidationSystem;
//# sourceMappingURL=ValidationSystem.js.map