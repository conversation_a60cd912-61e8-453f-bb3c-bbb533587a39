/**
 * ValidationSystem - Système de validation pour l'évolution des agents
 * Valide les déploiements et déclenche les rollbacks si nécessaire
 */
import { EventEmitter } from 'events';
import { EvolutionPlan, DeploymentResult, ValidationResult } from './types';
export declare class ValidationSystem extends EventEmitter {
    private logger;
    private validationHistory;
    private activeValidations;
    private validationRules;
    constructor();
    /**
     * Initialise le système de validation
     */
    initialize(): Promise<void>;
    /**
     * Valide un plan d'évolution complet
     */
    validatePlan(plan: EvolutionPlan, deploymentResults: DeploymentResult[]): Promise<ValidationResult[]>;
    /**
     * Valide une phase spécifique
     */
    private validatePhase;
    /**
     * Valide un critère spécifique
     */
    private validateCriteria;
    /**
     * Mesure les performances
     */
    private measurePerformance;
    /**
     * Mesure la sécurité
     */
    private measureSecurity;
    /**
     * Mesure la fonctionnalité
     */
    private measureFunctionality;
    /**
     * Mesure la compatibilité
     */
    private measureCompatibility;
    /**
     * Mesure la qualité
     */
    private measureQuality;
    /**
     * Valide les métriques de performance
     */
    private validatePerformanceMetrics;
    /**
     * Valide la santé des agents
     */
    private validateAgentHealth;
    /**
     * Valide l'intégrité globale du système
     */
    private validateSystemIntegrity;
    /**
     * Méthodes d'exécution des tests
     */
    private executePerformanceTest;
    private executeSecurityTest;
    private executeFunctionalTest;
    /**
     * Méthodes de test par défaut
     */
    private performSecurityScan;
    private performFunctionalTests;
    private performCompatibilityTests;
    private performQualityTests;
    private checkAgentHealth;
    private validateInterAgentCommunication;
    private validateDataConsistency;
    private validateGlobalSecurity;
    /**
     * Méthodes utilitaires
     */
    private evaluateCriteria;
    private createMetricValidationResult;
    /**
     * Méthodes de chargement et configuration
     */
    private loadValidationRules;
    private setupValidationInfrastructure;
    /**
     * Getters
     */
    getValidationHistory(planId: string): ValidationResult[] | undefined;
    isValidationActive(planId: string): boolean;
    getValidationRules(): Map<string, any>;
}
//# sourceMappingURL=ValidationSystem.d.ts.map