/**
 * EvolutionEngine - Système d'évolution continue pour l'adaptation automatique
 *
 * Ce module implémente un système complet d'évolution continue qui permet
 * au système d'agents IA de s'adapter automatiquement aux nouvelles technologies
 * et d'évoluer de manière autonome.
 *
 * Composants principaux :
 * - EvolutionEngine : Orchestrateur principal
 * - TechnologyScanner : Détection des nouvelles technologies
 * - ImpactAnalyzer : Analyse d'impact des changements
 * - EvolutionPlanner : Planification des évolutions
 * - AgentTrainer : Formation des agents
 * - DeploymentManager : Déploiement progressif
 * - ValidationSystem : Validation et rollback
 */
export { EvolutionEngine } from './EvolutionEngine';
export { TechnologyScanner } from './TechnologyScanner';
export { ImpactAnalyzer } from './ImpactAnalyzer';
export { EvolutionPlanner } from './EvolutionPlanner';
export { AgentTrainer } from './AgentTrainer';
export { DeploymentManager } from './DeploymentManager';
export { ValidationSystem } from './ValidationSystem';
export * from './types';
export declare const DEFAULT_EVOLUTION_CONFIG: {
    scanInterval: number;
    maxConcurrentEvolutions: number;
    autoApproveThreshold: number;
    rollbackTimeout: number;
    validationTimeout: number;
    enableAutomaticEvolution: boolean;
    enablePredictiveEvolution: boolean;
    technologySources: ({
        name: string;
        type: "github";
        url: string;
        lastScanned: Date;
        isActive: boolean;
        scanInterval: number;
        priority: number;
        filters: string[];
    } | {
        name: string;
        type: "npm";
        url: string;
        lastScanned: Date;
        isActive: boolean;
        scanInterval: number;
        priority: number;
        filters: string[];
    } | {
        name: string;
        type: "stackoverflow";
        url: string;
        lastScanned: Date;
        isActive: boolean;
        scanInterval: number;
        priority: number;
        filters?: undefined;
    } | {
        name: string;
        type: "reddit";
        url: string;
        lastScanned: Date;
        isActive: boolean;
        scanInterval: number;
        priority: number;
        filters?: undefined;
    } | {
        name: string;
        type: "hackernews";
        url: string;
        lastScanned: Date;
        isActive: boolean;
        scanInterval: number;
        priority: number;
        filters?: undefined;
    })[];
    excludedTechnologies: string[];
    priorityKeywords: string[];
    notificationChannels: string[];
    backupRetention: number;
    maxRollbackAttempts: number;
};
/**
 * Factory function pour créer une instance d'EvolutionEngine avec la configuration par défaut
 */
export declare function createEvolutionEngine(customConfig?: Partial<typeof DEFAULT_EVOLUTION_CONFIG>): any;
/**
 * Utilitaires pour la configuration
 */
export declare const EvolutionUtils: {
    /**
     * Valide une configuration d'évolution
     */
    validateConfig(config: any): boolean;
    /**
     * Crée une configuration pour un environnement de développement
     */
    createDevConfig(): {
        scanInterval: number;
        enableAutomaticEvolution: boolean;
        autoApproveThreshold: number;
        technologySources: ({
            scanInterval: number;
            name: string;
            type: "github";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            priority: number;
            filters: string[];
        } | {
            scanInterval: number;
            name: string;
            type: "npm";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            priority: number;
            filters: string[];
        } | {
            scanInterval: number;
            name: string;
            type: "stackoverflow";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            priority: number;
            filters?: undefined;
        } | {
            scanInterval: number;
            name: string;
            type: "reddit";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            priority: number;
            filters?: undefined;
        } | {
            scanInterval: number;
            name: string;
            type: "hackernews";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            priority: number;
            filters?: undefined;
        })[];
        maxConcurrentEvolutions: number;
        rollbackTimeout: number;
        validationTimeout: number;
        enablePredictiveEvolution: boolean;
        excludedTechnologies: string[];
        priorityKeywords: string[];
        notificationChannels: string[];
        backupRetention: number;
        maxRollbackAttempts: number;
    };
    /**
     * Crée une configuration pour un environnement de production
     */
    createProdConfig(): {
        scanInterval: number;
        enableAutomaticEvolution: boolean;
        autoApproveThreshold: number;
        maxConcurrentEvolutions: number;
        rollbackTimeout: number;
        validationTimeout: number;
        enablePredictiveEvolution: boolean;
        technologySources: ({
            name: string;
            type: "github";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            scanInterval: number;
            priority: number;
            filters: string[];
        } | {
            name: string;
            type: "npm";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            scanInterval: number;
            priority: number;
            filters: string[];
        } | {
            name: string;
            type: "stackoverflow";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            scanInterval: number;
            priority: number;
            filters?: undefined;
        } | {
            name: string;
            type: "reddit";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            scanInterval: number;
            priority: number;
            filters?: undefined;
        } | {
            name: string;
            type: "hackernews";
            url: string;
            lastScanned: Date;
            isActive: boolean;
            scanInterval: number;
            priority: number;
            filters?: undefined;
        })[];
        excludedTechnologies: string[];
        priorityKeywords: string[];
        notificationChannels: string[];
        backupRetention: number;
        maxRollbackAttempts: number;
    };
    /**
     * Crée une configuration pour les tests
     */
    createTestConfig(): {
        scanInterval: number;
        enableAutomaticEvolution: boolean;
        autoApproveThreshold: number;
        rollbackTimeout: number;
        validationTimeout: number;
        technologySources: never[];
        excludedTechnologies: never[];
        priorityKeywords: string[];
        maxConcurrentEvolutions: number;
        enablePredictiveEvolution: boolean;
        notificationChannels: string[];
        backupRetention: number;
        maxRollbackAttempts: number;
    };
};
/**
 * Constantes pour les événements d'évolution
 */
export declare const EVOLUTION_EVENTS: {
    readonly SCAN_STARTED: "scan_started";
    readonly TECHNOLOGY_DISCOVERED: "technology_discovered";
    readonly PLAN_CREATED: "plan_created";
    readonly EVOLUTION_STARTED: "evolution_started";
    readonly PHASE_COMPLETED: "phase_completed";
    readonly EVOLUTION_COMPLETED: "evolution_completed";
    readonly ROLLBACK_TRIGGERED: "rollback_triggered";
    readonly VALIDATION_FAILED: "validation_failed";
    readonly ERROR: "error";
};
/**
 * Métriques par défaut pour l'évolution
 */
export declare const DEFAULT_METRICS: {
    PERFORMANCE_THRESHOLDS: {
        responseTime: number;
        errorRate: number;
        cpuUsage: number;
        memoryUsage: number;
        diskUsage: number;
        networkLatency: number;
    };
    SECURITY_THRESHOLDS: {
        criticalVulnerabilities: number;
        highVulnerabilities: number;
        mediumVulnerabilities: number;
        lowVulnerabilities: number;
    };
    QUALITY_THRESHOLDS: {
        testCoverage: number;
        codeQuality: number;
        documentation: number;
        maintainability: number;
    };
};
/**
 * Templates de plans d'évolution
 */
export declare const EVOLUTION_TEMPLATES: {
    FRONTEND_UPDATE: {
        name: string;
        description: string;
        phases: {
            name: string;
            description: string;
            estimatedDuration: number;
        }[];
    };
    BACKEND_MIGRATION: {
        name: string;
        description: string;
        phases: {
            name: string;
            description: string;
            estimatedDuration: number;
        }[];
    };
    SECURITY_UPDATE: {
        name: string;
        description: string;
        phases: {
            name: string;
            description: string;
            estimatedDuration: number;
        }[];
    };
};
/**
 * Version de l'EvolutionEngine
 */
export declare const EVOLUTION_ENGINE_VERSION = "1.0.0";
/**
 * Informations sur l'EvolutionEngine
 */
export declare const EVOLUTION_ENGINE_INFO: {
    name: string;
    version: string;
    description: string;
    author: string;
    license: string;
    repository: string;
    documentation: string;
    support: string;
};
//# sourceMappingURL=index.d.ts.map