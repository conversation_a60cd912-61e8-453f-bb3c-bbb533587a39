/**
 * AgentTrainer - Formateur d'agents pour l'adaptation aux nouvelles technologies
 * Gère la formation et la mise à jour des capacités des agents
 */
import { EventEmitter } from 'events';
import { Technology, TrainingResult, AgentCapability } from './types';
export declare class AgentTrainer extends EventEmitter {
    private logger;
    private agentCapabilities;
    private trainingHistory;
    private trainingInProgress;
    constructor();
    /**
     * Initialise le formateur d'agents
     */
    initialize(): Promise<void>;
    /**
     * Forme un agent avec de nouvelles technologies
     */
    trainAgent(agentId: string, technologies: Technology[]): Promise<TrainingResult>;
    /**
     * Filtre les technologies pertinentes pour un agent
     */
    private filterRelevantTechnologies;
    /**
     * Forme un agent avec une technologie spécifique
     */
    private trainAgentWithTechnology;
    /**
     * Génère un module de formation pour une technologie
     */
    private generateTrainingModule;
    /**
     * Génère les étapes de formation
     */
    private generateTrainingSteps;
    /**
     * Exécute la formation
     */
    private executeTraining;
    /**
     * Exécute une étape de formation
     */
    private executeTrainingStep;
    /**
     * Évalue les résultats de formation
     */
    private evaluateTrainingResults;
    /**
     * Valide la formation
     */
    private validateTraining;
    /**
     * Met à jour les capacités de l'agent
     */
    private updateAgentCapabilities;
    /**
     * Méthodes utilitaires
     */
    private getAgentType;
    private isTechnologyRelevantForAgent;
    private agentHasCapability;
    private generateInstallCommand;
    private generateConfigCommand;
    private generateIntegrationCommand;
    private getAgentSpecificSteps;
    private generateNewCapabilities;
    private calculatePerformanceGain;
    private estimateTrainingDuration;
    private createSkippedResult;
    private getAgentModelVersion;
    private saveTrainingResult;
    private prepareTrainingEnvironment;
    private cleanupTrainingEnvironment;
    private generateRollbackProcedure;
    private generateValidationTests;
    /**
     * Méthodes de chargement des données
     */
    private loadAgentCapabilities;
    private loadTrainingHistory;
    /**
     * Getters
     */
    getAgentCapabilities(agentId: string): AgentCapability[];
    getTrainingHistory(agentId: string): TrainingResult[];
    isTrainingInProgress(agentId: string): boolean;
}
//# sourceMappingURL=AgentTrainer.d.ts.map