import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';
import { AgentRegistry } from '../agents/AgentRegistry';
export interface MCPTool {
    name: string;
    description: string;
    inputSchema: {
        type: string;
        properties: Record<string, any>;
        required?: string[];
    };
    handler: (params: any) => Promise<any>;
}
export interface MCPResource {
    uri: string;
    name: string;
    description: string;
    mimeType: string;
    handler: () => Promise<any>;
}
export interface MCPPrompt {
    name: string;
    description: string;
    arguments?: Record<string, any>;
    handler: (args: any) => Promise<string>;
}
export interface MCPRequest {
    id: string;
    method: string;
    params: any;
    timestamp: Date;
}
export interface MCPResponse {
    id: string;
    result?: any;
    error?: {
        code: number;
        message: string;
        data?: any;
    };
    timestamp: Date;
}
export interface MCPConnectorConfig {
    memory: CentralMemory;
    communication: SynapticCommunication;
    agentRegistry: AgentRegistry;
    serverPort?: number;
    enableLogging?: boolean;
    maxConcurrentRequests?: number;
}
export interface ExternalSystem {
    name: string;
    type: 'development' | 'collaboration' | 'project_management' | 'cloud_provider' | 'analytics';
    connectionInfo: {
        url?: string;
        apiKey?: string;
        token?: string;
        credentials?: any;
    };
    capabilities: string[];
    isConnected: boolean;
    lastSync: Date;
}
/**
 * Connecteur MCP - Agent Toucher (Interopérabilité Universelle)
 *
 * Expose les capacités de l'organisme IA via le protocole MCP
 * et se connecte aux systèmes externes pour une intégration complète.
 */
export declare class MCPConnector extends EventEmitter {
    private memory;
    private communication;
    private agentRegistry;
    private serverPort;
    private enableLogging;
    private maxConcurrentRequests;
    private isInitialized;
    private server;
    private tools;
    private resources;
    private prompts;
    private externalSystems;
    private activeRequests;
    constructor(config: MCPConnectorConfig);
    /**
     * Initialise le connecteur MCP
     */
    initialize(): Promise<void>;
    /**
     * Enregistre les outils MCP exposés
     */
    private registerMCPTools;
    /**
     * Enregistre les ressources MCP exposées
     */
    private registerMCPResources;
    /**
     * Enregistre les prompts MCP
     */
    private registerMCPPrompts;
    /**
     * Démarre le serveur MCP
     */
    private startMCPServer;
    /**
     * Connecte aux systèmes externes
     */
    private connectExternalSystems;
    /**
     * Teste la connexion à un système externe
     */
    private testExternalConnection;
    /**
     * Gestionnaire pour génération d'application complète
     */
    private handleGenerateFullApplication;
    /**
     * Gestionnaire pour audit de sécurité
     */
    private handleSecurityAudit;
    /**
     * Gestionnaire pour recherche marché
     */
    private handleMarketResearch;
    /**
     * Gestionnaire pour optimisation SEO
     */
    private handleSEOOptimization;
    /**
     * Gestionnaire pour migration de code
     */
    private handleCodeMigration;
    /**
     * Obtient le statut du système
     */
    private getSystemStatus;
    /**
     * Obtient les métriques de performance
     */
    private getPerformanceMetrics;
    /**
     * Obtient la documentation des agents
     */
    private getAgentDocumentation;
    /**
     * Génère un prompt d'architecture
     */
    private generateArchitecturePrompt;
    /**
     * Génère un prompt de stratégie marketing
     */
    private generateMarketingPrompt;
    /**
     * Configuration des gestionnaires d'événements
     */
    private setupEventHandlers;
    /**
     * Gestion des requêtes MCP
     */
    private handleMCPRequest;
    /**
     * Synchronise avec les systèmes externes
     */
    syncWithExternalSystems(): Promise<void>;
    /**
     * Synchronise un système spécifique
     */
    private syncSpecificSystem;
    /**
     * Obtient les statistiques du connecteur
     */
    getConnectorStats(): {
        tools: number;
        resources: number;
        prompts: number;
        externalSystems: number;
        connectedSystems: number;
        activeRequests: number;
        isInitialized: boolean;
    };
    /**
     * Arrêt gracieux du connecteur
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=MCPConnector.d.ts.map