import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
import { DecisionEngine } from '../decision/DecisionEngine';
export interface LearningPattern {
    id: string;
    type: 'workflow' | 'agent-behavior' | 'performance' | 'error-pattern';
    pattern: any;
    confidence: number;
    frequency: number;
    lastSeen: Date;
    effectiveness: number;
    metadata: any;
}
export interface IntelligenceMetrics {
    timestamp: Date;
    learningRate: number;
    patternsDiscovered: number;
    adaptationsApplied: number;
    performanceImprovement: number;
    predictionAccuracy: number;
    systemOptimizations: number;
}
export interface PredictionResult {
    type: 'performance' | 'failure' | 'optimization' | 'resource-need';
    confidence: number;
    timeframe: string;
    description: string;
    recommendations: string[];
    metadata: any;
}
export interface OptimizationSuggestion {
    id: string;
    category: 'performance' | 'resource' | 'workflow' | 'agent-allocation';
    priority: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    expectedImpact: number;
    implementationComplexity: 'low' | 'medium' | 'high';
    estimatedBenefit: string;
    risks: string[];
    implementation: any;
}
/**
 * Moteur d'Intelligence Artificielle
 *
 * Système d'apprentissage automatique et d'auto-amélioration
 * pour optimiser continuellement les performances du système
 */
export declare class IntelligenceEngine extends EventEmitter {
    private memory;
    private neuralNetwork;
    private decisionEngine;
    private learningPatterns;
    private metricsHistory;
    private optimizationSuggestions;
    private learningInterval;
    private analysisInterval;
    private isLearning;
    private learningRate;
    constructor(memory: CentralMemory, neuralNetwork: NeuralNetworkManager, decisionEngine: DecisionEngine);
    /**
     * Initialise le moteur d'intelligence
     */
    initialize(): Promise<void>;
    /**
     * Charge les patterns d'apprentissage existants
     */
    private loadExistingPatterns;
    /**
     * Démarre l'apprentissage continu
     */
    private startContinuousLearning;
    /**
     * Démarre l'analyse prédictive
     */
    private startPredictiveAnalysis;
    /**
     * Effectue un cycle d'apprentissage
     */
    performLearningCycle(): Promise<void>;
    /**
     * Analyse les performances des agents
     */
    private analyzeAgentPerformance;
    /**
     * Analyse les patterns de workflow
     */
    private analyzeWorkflowPatterns;
    /**
     * Détecte les anomalies dans le système
     */
    private detectAnomalies;
    /**
     * Optimise l'allocation des ressources
     */
    private optimizeResourceAllocation;
    /**
     * Met à jour les métriques d'intelligence
     */
    private updateIntelligenceMetrics;
    /**
     * Effectue une analyse prédictive
     */
    performPredictiveAnalysis(): Promise<PredictionResult[]>;
    /**
     * Prédit les tendances de performance
     */
    private predictPerformanceTrends;
    /**
     * Prédit les pannes potentielles
     */
    private predictPotentialFailures;
    /**
     * Prédit les besoins en ressources
     */
    private predictResourceNeeds;
    private extractCommonSteps;
    private analyzeAgentAllocation;
    private calculateTrend;
    private calculatePerformanceImprovement;
    private calculatePredictionAccuracy;
    private calculateCurrentSystemLoad;
    private createOptimizationSuggestion;
    /**
     * Récupère les métriques d'intelligence
     */
    getIntelligenceMetrics(): IntelligenceMetrics[];
    /**
     * Récupère les suggestions d'optimisation
     */
    getOptimizationSuggestions(): OptimizationSuggestion[];
    /**
     * Récupère les patterns d'apprentissage
     */
    getLearningPatterns(): LearningPattern[];
    /**
     * Arrêt du moteur d'intelligence
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=IntelligenceEngine.d.ts.map