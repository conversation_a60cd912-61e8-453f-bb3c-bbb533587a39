import { EventEmitter } from 'events';
import { Anomaly, HealingStrategy, HealingResult, AdaptationRule, AdaptationEngineConfig } from './types';
/**
 * Moteur d'Adaptation - Apprentissage et Évolution
 *
 * Responsable de l'apprentissage à partir des succès et échecs
 * de guérison pour améliorer les stratégies futures.
 */
export declare class AdaptationEngine extends EventEmitter {
    private memory;
    private learningRate;
    private confidenceThreshold;
    private maxRules;
    private enableAutoCreation;
    private isInitialized;
    private adaptationRules;
    private learningHistory;
    private learningStats;
    constructor(config: AdaptationEngineConfig);
    /**
     * Initialise le moteur d'adaptation
     */
    initialize(): Promise<void>;
    /**
     * Apprend d'un résultat de guérison
     */
    learn(anomaly: Anomaly, strategy: HealingStrategy, result: HealingResult): Promise<AdaptationRule | null>;
    /**
     * Applique une règle d'adaptation
     */
    apply(adaptationRule: AdaptationRule): Promise<void>;
    /**
     * Obtient les règles d'adaptation applicables à une situation
     */
    getApplicableRules(anomaly: Anomaly): AdaptationRule[];
    /**
     * Obtient les statistiques d'apprentissage
     */
    getStats(): {
        totalRules: number;
        highConfidenceRules: number;
        totalLearningEvents: number;
        rulesCreated: number;
        rulesUpdated: number;
        averageConfidence: number;
        lastLearning: Date | null;
    };
    /**
     * Apprend à partir d'un succès
     */
    private learnFromSuccess;
    /**
     * Apprend à partir d'un échec
     */
    private learnFromFailure;
    /**
     * Met à jour le taux de succès avec une moyenne mobile
     */
    private updateSuccessRate;
    /**
     * Vérifie si une règle est applicable à une anomalie
     */
    private isRuleApplicable;
    /**
     * Met à jour la confiance moyenne
     */
    private updateAverageConfidence;
    /**
     * Charge les règles d'adaptation depuis la mémoire
     */
    private loadAdaptationRules;
    /**
     * Sauvegarde les règles d'adaptation en mémoire
     */
    private saveAdaptationRules;
    /**
     * Charge l'historique d'apprentissage depuis la mémoire
     */
    private loadLearningHistory;
    /**
     * Sauvegarde l'historique d'apprentissage en mémoire
     */
    private saveLearningHistory;
}
//# sourceMappingURL=AdaptationEngine.d.ts.map