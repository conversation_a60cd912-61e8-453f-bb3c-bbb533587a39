"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIImmuneSystem = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
const AnomalyDetector_1 = require("./AnomalyDetector");
const AutoHealer_1 = require("./AutoHealer");
const AdaptationEngine_1 = require("./AdaptationEngine");
/**
 * Système Immunitaire IA - Auto-Réparation et Adaptation
 *
 * Surveille la santé du système, détecte les anomalies,
 * applique des stratégies de guérison et s'adapte pour éviter les récurrences.
 */
class AIImmuneSystem extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isInitialized = false;
        this.isMonitoring = false;
        // Métriques de santé
        this.currentMetrics = null;
        this.metricsHistory = [];
        this.maxHistorySize = 1000;
        // Anomalies actives
        this.activeAnomalies = new Map();
        this.healingInProgress = new Map();
        // Statistiques du système immunitaire
        this.immuneStats = {
            totalAnomaliesDetected: 0,
            successfulHealings: 0,
            adaptationsApplied: 0,
            averageDetectionTime: 0,
            averageHealingTime: 0,
            systemUptime: Date.now()
        };
        this.memory = config.memory;
        this.communication = config.communication;
        this.monitoringInterval = config.monitoringInterval || 30000; // 30 secondes
        this.healingEnabled = config.healingEnabled !== false;
        this.adaptationEnabled = config.adaptationEnabled !== false;
        // Initialisation des composants
        this.anomalyDetector = new AnomalyDetector_1.AnomalyDetector({
            memory: this.memory,
            sensitivityLevel: 0.7
        });
        this.autoHealer = new AutoHealer_1.AutoHealer({
            memory: this.memory,
            communication: this.communication,
            maxConcurrentHealings: 3
        });
        this.adaptationEngine = new AdaptationEngine_1.AdaptationEngine({
            memory: this.memory,
            learningRate: 0.1
        });
    }
    /**
     * Initialise le système immunitaire
     */
    async initialize() {
        try {
            logger_1.logger.info('🛡️ Initialisation du Système Immunitaire IA...');
            // Initialisation des composants
            await this.anomalyDetector.initialize();
            await this.autoHealer.initialize();
            await this.adaptationEngine.initialize();
            // Configuration des événements
            this.setupEventHandlers();
            // Chargement des stratégies de guérison depuis la mémoire
            await this.loadHealingStrategies();
            this.isInitialized = true;
            logger_1.logger.info('✅ Système Immunitaire IA initialisé');
            // Démarrage du monitoring
            await this.startMonitoring();
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation du Système Immunitaire:', error);
            throw error;
        }
    }
    /**
     * Démarre le monitoring de santé
     */
    async startMonitoring() {
        if (this.isMonitoring) {
            logger_1.logger.warn('⚠️ Monitoring déjà en cours');
            return;
        }
        this.isMonitoring = true;
        logger_1.logger.info('🔍 Démarrage du monitoring de santé...');
        // Boucle de monitoring principal
        const monitoringLoop = async () => {
            if (!this.isMonitoring)
                return;
            try {
                await this.monitorHealth();
            }
            catch (error) {
                logger_1.logger.error('❌ Erreur lors du monitoring:', error);
            }
            // Programmation du prochain cycle
            setTimeout(monitoringLoop, this.monitoringInterval);
        };
        // Démarrage immédiat
        monitoringLoop();
        this.emit('monitoring-started', {
            interval: this.monitoringInterval,
            timestamp: new Date()
        });
    }
    /**
     * Arrête le monitoring de santé
     */
    async stopMonitoring() {
        this.isMonitoring = false;
        logger_1.logger.info('🛑 Arrêt du monitoring de santé');
        this.emit('monitoring-stopped', {
            timestamp: new Date()
        });
    }
    /**
     * Surveille la santé du système
     */
    async monitorHealth() {
        try {
            // Collecte des métriques de santé
            const healthMetrics = await this.gatherMetrics();
            // Stockage des métriques
            this.currentMetrics = healthMetrics;
            this.metricsHistory.push(healthMetrics);
            // Limitation de l'historique
            if (this.metricsHistory.length > this.maxHistorySize) {
                this.metricsHistory.shift();
            }
            // Détection d'anomalies
            const anomalies = await this.anomalyDetector.detect(healthMetrics, this.metricsHistory);
            // Traitement des nouvelles anomalies
            for (const anomaly of anomalies) {
                await this.handleNewAnomaly(anomaly);
            }
            // Vérification des anomalies existantes
            await this.checkExistingAnomalies();
            // Émission d'événement de santé
            this.emit('health-check-completed', {
                metrics: healthMetrics,
                anomaliesCount: anomalies.length,
                activeAnomaliesCount: this.activeAnomalies.size,
                timestamp: new Date()
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors du monitoring de santé:', error);
        }
    }
    /**
     * Collecte les métriques de santé du système
     */
    async gatherMetrics() {
        const timestamp = new Date();
        // Métriques système
        const systemLoad = await this.getSystemLoad();
        const memoryUsage = await this.getMemoryUsage();
        const responseTime = await this.getAverageResponseTime();
        const errorRate = await this.getErrorRate();
        const throughput = await this.getThroughput();
        // Métriques des agents
        const agentHealth = await this.getAgentHealthMetrics();
        // Métriques synaptiques
        const synapticHealth = await this.getSynapticHealth();
        return {
            timestamp,
            systemLoad,
            memoryUsage,
            responseTime,
            errorRate,
            agentHealth,
            synapticHealth,
            throughput
        };
    }
    /**
     * Gère une nouvelle anomalie détectée
     */
    async handleNewAnomaly(anomaly) {
        logger_1.logger.warn(`🚨 Anomalie détectée: ${anomaly.type} - ${anomaly.description}`);
        // Enregistrement de l'anomalie
        this.activeAnomalies.set(anomaly.id, anomaly);
        this.immuneStats.totalAnomaliesDetected++;
        // Stockage en mémoire pour apprentissage
        await this.storeAnomalyInMemory(anomaly);
        // Application de la guérison si activée
        if (this.healingEnabled && anomaly.severity !== 'low') {
            await this.initiateHealing(anomaly);
        }
        // Émission d'événement
        this.emit('anomaly-detected', {
            anomaly,
            timestamp: new Date()
        });
    }
    /**
     * Initie le processus de guérison pour une anomalie
     */
    async initiateHealing(anomaly) {
        try {
            // Sélection de la stratégie de guérison
            const healingStrategy = await this.autoHealer.selectStrategy(anomaly);
            if (!healingStrategy) {
                logger_1.logger.warn(`⚠️ Aucune stratégie de guérison trouvée pour l'anomalie ${anomaly.id}`);
                return;
            }
            // Vérification des guérisons en cours
            if (this.healingInProgress.size >= 3) { // Limite de sécurité
                logger_1.logger.warn('⚠️ Trop de guérisons en cours, mise en attente');
                return;
            }
            logger_1.logger.info(`🔧 Initiation de la guérison: ${healingStrategy.name} pour ${anomaly.id}`);
            // Enregistrement de la guérison en cours
            this.healingInProgress.set(anomaly.id, healingStrategy);
            // Application de la stratégie
            const healingResult = await this.autoHealer.apply(healingStrategy, anomaly);
            if (healingResult.success) {
                logger_1.logger.info(`✅ Guérison réussie pour l'anomalie ${anomaly.id}`);
                // Suppression de l'anomalie active
                this.activeAnomalies.delete(anomaly.id);
                this.immuneStats.successfulHealings++;
                // Apprentissage pour éviter la récurrence
                if (this.adaptationEnabled) {
                    await this.learnFromHealing(anomaly, healingStrategy, healingResult);
                }
                this.emit('healing-successful', {
                    anomaly,
                    strategy: healingStrategy,
                    result: healingResult,
                    timestamp: new Date()
                });
            }
            else {
                logger_1.logger.error(`❌ Échec de la guérison pour l'anomalie ${anomaly.id}:`, healingResult.error);
                this.emit('healing-failed', {
                    anomaly,
                    strategy: healingStrategy,
                    result: healingResult,
                    timestamp: new Date()
                });
            }
            // Nettoyage
            this.healingInProgress.delete(anomaly.id);
        }
        catch (error) {
            logger_1.logger.error(`❌ Erreur lors de l'initiation de la guérison pour ${anomaly.id}:`, error);
            this.healingInProgress.delete(anomaly.id);
        }
    }
    /**
     * Apprend d'une guérison réussie pour éviter les récurrences
     */
    async learnFromHealing(anomaly, strategy, result) {
        try {
            // Création d'une adaptation basée sur le succès
            const adaptation = await this.adaptationEngine.learn(anomaly, strategy, result);
            if (adaptation) {
                // Application de l'adaptation
                await this.adaptationEngine.apply(adaptation);
                this.immuneStats.adaptationsApplied++;
                logger_1.logger.info(`🧠 Adaptation appliquée pour éviter la récurrence de: ${anomaly.type}`);
                this.emit('adaptation-applied', {
                    anomaly,
                    adaptation,
                    timestamp: new Date()
                });
            }
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'apprentissage de la guérison:', error);
        }
    }
    /**
     * Vérifie les anomalies existantes
     */
    async checkExistingAnomalies() {
        const now = Date.now();
        const maxAnomalyAge = 300000; // 5 minutes
        for (const [anomalyId, anomaly] of this.activeAnomalies) {
            const age = now - anomaly.timestamp.getTime();
            // Suppression des anomalies anciennes (potentiellement résolues)
            if (age > maxAnomalyAge) {
                logger_1.logger.info(`🔄 Suppression de l'anomalie expirée: ${anomalyId}`);
                this.activeAnomalies.delete(anomalyId);
                this.emit('anomaly-expired', {
                    anomaly,
                    age,
                    timestamp: new Date()
                });
            }
        }
    }
    /**
     * Stocke une anomalie en mémoire pour apprentissage
     */
    async storeAnomalyInMemory(anomaly) {
        try {
            await this.memory.storeGlobalPattern({
                id: `anomaly-${anomaly.id}`,
                content: {
                    type: 'anomaly',
                    anomaly,
                    systemState: this.currentMetrics
                },
                domain: 'immune-system',
                crossDomainRelevance: 0.8,
                timestamp: new Date(),
                version: '1.0',
                metadata: {
                    anomalyType: anomaly.type,
                    severity: anomaly.severity,
                    confidence: anomaly.confidence
                }
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors du stockage de l\'anomalie:', error);
        }
    }
    /**
     * Charge les stratégies de guérison depuis la mémoire
     */
    async loadHealingStrategies() {
        try {
            const strategies = await this.memory.queryAcrossDomains('healing-strategy', 50);
            for (const strategy of strategies) {
                await this.autoHealer.addStrategy(strategy.content);
            }
            logger_1.logger.info(`🔧 ${strategies.length} stratégies de guérison chargées`);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors du chargement des stratégies:', error);
        }
    }
    /**
     * Configuration des gestionnaires d'événements
     */
    setupEventHandlers() {
        // Événements de communication
        this.communication.on('agent-disconnected', (agentInfo) => {
            this.handleAgentDisconnection(agentInfo);
        });
        this.communication.on('synaptic-failure', (failure) => {
            this.handleSynapticFailure(failure);
        });
        // Événements de mémoire
        this.memory.on('memory-pressure', (pressure) => {
            this.handleMemoryPressure(pressure);
        });
    }
    /**
     * Gère la déconnexion d'un agent
     */
    async handleAgentDisconnection(agentInfo) {
        const anomaly = {
            id: `agent-disconnect-${agentInfo.id}-${Date.now()}`,
            type: 'agent',
            severity: 'medium',
            description: `Agent ${agentInfo.id} déconnecté`,
            metrics: { agentId: agentInfo.id, lastSeen: agentInfo.lastSeen },
            timestamp: new Date(),
            affectedComponents: [agentInfo.id],
            confidence: 0.9
        };
        await this.handleNewAnomaly(anomaly);
    }
    /**
     * Gère les échecs synaptiques
     */
    async handleSynapticFailure(failure) {
        const anomaly = {
            id: `synaptic-failure-${Date.now()}`,
            type: 'communication',
            severity: 'high',
            description: `Échec synaptique: ${failure.description}`,
            metrics: failure,
            timestamp: new Date(),
            affectedComponents: failure.affectedSynapses || [],
            confidence: 0.8
        };
        await this.handleNewAnomaly(anomaly);
    }
    /**
     * Gère la pression mémoire
     */
    async handleMemoryPressure(pressure) {
        const anomaly = {
            id: `memory-pressure-${Date.now()}`,
            type: 'resource',
            severity: pressure.level > 0.9 ? 'critical' : 'high',
            description: `Pression mémoire élevée: ${Math.round(pressure.level * 100)}%`,
            metrics: pressure,
            timestamp: new Date(),
            affectedComponents: ['memory-system'],
            confidence: 0.95
        };
        await this.handleNewAnomaly(anomaly);
    }
    // Méthodes de collecte de métriques (implémentations simplifiées)
    async getSystemLoad() {
        // Implémentation simplifiée - dans un vrai système, utiliser des métriques OS
        return Math.random() * 0.8; // 0-80% de charge
    }
    async getMemoryUsage() {
        const used = process.memoryUsage();
        const total = used.heapTotal;
        return used.heapUsed / total;
    }
    async getAverageResponseTime() {
        // Implémentation simplifiée
        return Math.random() * 200 + 50; // 50-250ms
    }
    async getErrorRate() {
        // Implémentation simplifiée
        return Math.random() * 0.05; // 0-5% d'erreurs
    }
    async getThroughput() {
        // Implémentation simplifiée
        return Math.random() * 1000 + 100; // 100-1100 req/min
    }
    async getAgentHealthMetrics() {
        const agentHealth = new Map();
        // Simulation de la santé des agents
        ['frontend', 'backend', 'devops', 'qa'].forEach(agentType => {
            agentHealth.set(agentType, Math.random() * 0.3 + 0.7); // 70-100% de santé
        });
        return agentHealth;
    }
    async getSynapticHealth() {
        // Implémentation simplifiée
        return Math.random() * 0.2 + 0.8; // 80-100% de santé synaptique
    }
    /**
     * Évolution de l'architecture basée sur l'usage
     */
    async evolveArchitecture() {
        try {
            logger_1.logger.info('🔄 Analyse de l\'évolution architecturale...');
            // Analyse des patterns d'usage
            const usagePatterns = await this.analyzeUsage();
            // Identification des goulots d'étranglement
            const bottlenecks = await this.identifyBottlenecks(usagePatterns);
            // Propositions d'évolution
            const evolutions = await this.proposeEvolutions(bottlenecks);
            // Application progressive des évolutions
            await this.applyEvolutions(evolutions);
            this.emit('architecture-evolved', {
                usagePatterns,
                bottlenecks,
                evolutions,
                timestamp: new Date()
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'évolution architecturale:', error);
        }
    }
    /**
     * Analyse les patterns d'usage
     */
    async analyzeUsage() {
        // Analyse des métriques historiques
        const recentMetrics = this.metricsHistory.slice(-100); // 100 dernières métriques
        return {
            averageLoad: recentMetrics.reduce((sum, m) => sum + m.systemLoad, 0) / recentMetrics.length,
            peakMemoryUsage: Math.max(...recentMetrics.map(m => m.memoryUsage)),
            averageResponseTime: recentMetrics.reduce((sum, m) => sum + m.responseTime, 0) / recentMetrics.length,
            throughputTrend: this.calculateTrend(recentMetrics.map(m => m.throughput))
        };
    }
    /**
     * Identifie les goulots d'étranglement
     */
    async identifyBottlenecks(usagePatterns) {
        const bottlenecks = [];
        if (usagePatterns.averageLoad > 0.7) {
            bottlenecks.push({
                type: 'cpu',
                severity: 'high',
                description: 'Charge CPU élevée'
            });
        }
        if (usagePatterns.peakMemoryUsage > 0.8) {
            bottlenecks.push({
                type: 'memory',
                severity: 'medium',
                description: 'Utilisation mémoire élevée'
            });
        }
        if (usagePatterns.averageResponseTime > 150) {
            bottlenecks.push({
                type: 'latency',
                severity: 'medium',
                description: 'Temps de réponse élevé'
            });
        }
        return bottlenecks;
    }
    /**
     * Propose des évolutions architecturales
     */
    async proposeEvolutions(bottlenecks) {
        const evolutions = [];
        for (const bottleneck of bottlenecks) {
            switch (bottleneck.type) {
                case 'cpu':
                    evolutions.push({
                        type: 'scale-out',
                        description: 'Ajout d\'instances d\'agents',
                        priority: 'high'
                    });
                    break;
                case 'memory':
                    evolutions.push({
                        type: 'memory-optimization',
                        description: 'Optimisation de la gestion mémoire',
                        priority: 'medium'
                    });
                    break;
                case 'latency':
                    evolutions.push({
                        type: 'caching-improvement',
                        description: 'Amélioration du système de cache',
                        priority: 'medium'
                    });
                    break;
            }
        }
        return evolutions;
    }
    /**
     * Applique les évolutions proposées
     */
    async applyEvolutions(evolutions) {
        for (const evolution of evolutions) {
            try {
                logger_1.logger.info(`🔧 Application de l'évolution: ${evolution.description}`);
                // Simulation de l'application d'évolution
                await new Promise(resolve => setTimeout(resolve, 1000));
                // Stockage de l'évolution en mémoire
                await this.memory.storeGlobalPattern({
                    id: `evolution-${Date.now()}`,
                    content: {
                        type: 'architectural-evolution',
                        evolution,
                        appliedAt: new Date()
                    },
                    domain: 'architecture',
                    crossDomainRelevance: 0.9,
                    timestamp: new Date(),
                    version: '1.0',
                    metadata: {
                        evolutionType: evolution.type,
                        priority: evolution.priority
                    }
                });
                logger_1.logger.info(`✅ Évolution appliquée: ${evolution.description}`);
            }
            catch (error) {
                logger_1.logger.error(`❌ Erreur lors de l'application de l'évolution ${evolution.description}:`, error);
            }
        }
    }
    /**
     * Calcule la tendance d'une série de valeurs
     */
    calculateTrend(values) {
        if (values.length < 2)
            return 'stable';
        const first = values.slice(0, Math.floor(values.length / 2));
        const second = values.slice(Math.floor(values.length / 2));
        const firstAvg = first.reduce((sum, v) => sum + v, 0) / first.length;
        const secondAvg = second.reduce((sum, v) => sum + v, 0) / second.length;
        const change = (secondAvg - firstAvg) / firstAvg;
        if (change > 0.1)
            return 'increasing';
        if (change < -0.1)
            return 'decreasing';
        return 'stable';
    }
    /**
     * Récupère le statut du système immunitaire
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isMonitoring: this.isMonitoring,
            healingEnabled: this.healingEnabled,
            adaptationEnabled: this.adaptationEnabled,
            activeAnomalies: this.activeAnomalies.size,
            healingInProgress: this.healingInProgress.size,
            currentMetrics: this.currentMetrics,
            stats: {
                ...this.immuneStats,
                uptime: Date.now() - this.immuneStats.systemUptime
            }
        };
    }
    /**
     * Arrêt gracieux du système immunitaire
     */
    async shutdown() {
        logger_1.logger.info('🛑 Arrêt du Système Immunitaire IA...');
        // Arrêt du monitoring
        await this.stopMonitoring();
        // Arrêt des composants
        await this.anomalyDetector.shutdown();
        await this.autoHealer.shutdown();
        await this.adaptationEngine.shutdown();
        logger_1.logger.info('✅ Système Immunitaire IA arrêté');
    }
}
exports.AIImmuneSystem = AIImmuneSystem;
//# sourceMappingURL=AIImmuneSystem.js.map