"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoHealer = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
const ServiceController_1 = require("./ServiceController");
const HealingStrategies_1 = require("./HealingStrategies");
const types_1 = require("./types");
/**
 * Auto-Healer - Système de Réparation Automatique
 *
 * Responsable de l'application automatique de stratégies de guérison
 * pour résoudre les anomalies détectées dans le système.
 */
class AutoHealer extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isInitialized = false;
        // État des guérisons en cours
        this.activeHealings = new Map();
        // Historique des guérisons
        this.healingHistory = [];
        this.maxHistorySize = 1000;
        // Statistiques
        this.stats = {
            totalHealings: 0,
            successfulHealings: 0,
            failedHealings: 0,
            rolledBackHealings: 0,
            averageHealingTime: 0,
            lastHealing: null
        };
        this.memory = config.memory;
        this.communication = config.communication;
        this.maxConcurrentHealings = config.maxConcurrentHealings || 3;
        this.defaultTimeout = config.defaultTimeout || 300000; // 5 minutes
        this.riskThreshold = config.riskThreshold || 0.7;
        this.enableLearning = config.enableLearning !== false;
        this.enableRollback = config.enableRollback !== false;
        // Initialisation des composants
        this.serviceController = new ServiceController_1.ServiceController({
            communication: this.communication,
            memory: this.memory
        });
        this.healingStrategies = new HealingStrategies_1.HealingStrategies({
            memory: this.memory,
            customStrategies: config.strategies
        });
    }
    /**
     * Initialise l'Auto-Healer
     */
    async initialize() {
        try {
            logger_1.logger.info('🔧 Initialisation de l\'Auto-Healer...');
            // Initialisation des composants
            await this.serviceController.initialize();
            await this.healingStrategies.initialize();
            // Chargement de l'historique depuis la mémoire
            await this.loadHealingHistory();
            // Configuration des événements
            this.setupEventHandlers();
            this.isInitialized = true;
            logger_1.logger.info('✅ Auto-Healer initialisé');
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation de l\'Auto-Healer:', error);
            throw error;
        }
    }
    /**
     * Sélectionne la meilleure stratégie de guérison pour une anomalie
     */
    async selectStrategy(anomaly) {
        try {
            // Récupération du contexte système
            const context = await this.buildHealingContext(anomaly);
            // Recherche des stratégies applicables
            const applicableStrategies = await this.healingStrategies.findApplicableStrategies(anomaly);
            if (applicableStrategies.length === 0) {
                logger_1.logger.warn(`Aucune stratégie trouvée pour l'anomalie ${anomaly.type}`);
                return null;
            }
            // Évaluation et classement des stratégies
            const evaluatedStrategies = await this.evaluateStrategies(applicableStrategies, context);
            // Sélection de la meilleure stratégie
            const bestStrategy = evaluatedStrategies[0];
            // Vérification du niveau de risque
            if (bestStrategy.riskLevel > this.riskThreshold) {
                logger_1.logger.warn(`Stratégie ${bestStrategy.name} rejetée: risque trop élevé (${bestStrategy.riskLevel})`);
                // Recherche d'une stratégie moins risquée
                const safeStrategy = evaluatedStrategies.find(s => s.riskLevel <= this.riskThreshold);
                if (safeStrategy) {
                    logger_1.logger.info(`Utilisation de la stratégie alternative: ${safeStrategy.name}`);
                    return safeStrategy;
                }
                return null;
            }
            logger_1.logger.info(`Stratégie sélectionnée: ${bestStrategy.name} (risque: ${bestStrategy.riskLevel})`);
            return bestStrategy;
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la sélection de stratégie:', error);
            return null;
        }
    }
    /**
     * Applique une stratégie de guérison
     */
    async apply(strategy, anomaly) {
        const healingId = `healing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const startTime = new Date();
        try {
            logger_1.logger.info(`🔧 Application de la stratégie ${strategy.name} pour l'anomalie ${anomaly.id}`);
            // Vérification des limites de concurrence
            if (this.activeHealings.size >= this.maxConcurrentHealings) {
                throw new Error('Limite de guérisons concurrentes atteinte');
            }
            // Construction du contexte
            const context = await this.buildHealingContext(anomaly);
            // Enregistrement de la guérison active
            this.activeHealings.set(healingId, {
                strategy,
                anomaly,
                startTime,
                status: types_1.HealingStatus.IN_PROGRESS,
                context
            });
            // Collecte des métriques avant guérison
            const beforeMetrics = await this.collectCurrentMetrics();
            // Exécution des actions de guérison
            const executedActions = [];
            let rollbackPerformed = false;
            try {
                for (const action of strategy.actions) {
                    logger_1.logger.debug(`Exécution de l'action: ${action.type} sur ${action.target}`);
                    await this.executeAction(action, context);
                    executedActions.push(action);
                    // Attente entre les actions si nécessaire
                    if (action.parameters?.delay) {
                        await this.sleep(action.parameters.delay);
                    }
                }
                // Vérification du succès
                const afterMetrics = await this.collectCurrentMetrics();
                const improvementScore = this.calculateImprovementScore(beforeMetrics, afterMetrics, anomaly);
                if (improvementScore < 0.3 && this.enableRollback) {
                    // La guérison n'a pas suffisamment amélioré la situation
                    logger_1.logger.warn('Guérison inefficace, rollback en cours...');
                    await this.performRollback(strategy, executedActions, context);
                    rollbackPerformed = true;
                }
                const result = {
                    success: !rollbackPerformed,
                    strategy,
                    anomaly,
                    executedActions,
                    startTime,
                    endTime: new Date(),
                    metrics: {
                        beforeHealing: beforeMetrics,
                        afterHealing: afterMetrics
                    },
                    rollbackPerformed,
                    improvementScore
                };
                // Mise à jour des statistiques
                this.updateStats(result);
                // Stockage en mémoire pour apprentissage
                if (this.enableLearning) {
                    await this.storeHealingResult(result);
                }
                return result;
            }
            catch (actionError) {
                logger_1.logger.error('❌ Erreur lors de l\'exécution des actions:', actionError);
                // Rollback en cas d'erreur
                if (this.enableRollback && executedActions.length > 0) {
                    try {
                        await this.performRollback(strategy, executedActions, context);
                        rollbackPerformed = true;
                    }
                    catch (rollbackError) {
                        logger_1.logger.error('❌ Erreur lors du rollback:', rollbackError);
                    }
                }
                const result = {
                    success: false,
                    strategy,
                    anomaly,
                    executedActions,
                    startTime,
                    endTime: new Date(),
                    error: actionError.message,
                    rollbackPerformed
                };
                this.updateStats(result);
                return result;
            }
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'application de la stratégie:', error);
            const result = {
                success: false,
                strategy,
                anomaly,
                executedActions: [],
                startTime,
                endTime: new Date(),
                error: error.message
            };
            this.updateStats(result);
            return result;
        }
        finally {
            // Nettoyage
            this.activeHealings.delete(healingId);
        }
    }
    /**
     * Construit le contexte de guérison
     */
    async buildHealingContext(anomaly) {
        // Récupération des composants affectés
        const affectedComponents = await this.getAffectedComponents(anomaly);
        // État actuel du système
        const systemState = await this.collectCurrentMetrics();
        // Guérisons récentes
        const recentHealings = this.healingHistory
            .filter(h => Date.now() - h.startTime.getTime() < 3600000) // Dernière heure
            .slice(-10);
        // Ressources disponibles
        const availableResources = await this.getAvailableResources();
        // Contraintes système
        const constraints = await this.getSystemConstraints();
        return {
            anomaly,
            affectedComponents,
            systemState,
            recentHealings,
            availableResources,
            constraints
        };
    }
    /**
     * Utilitaire pour attendre
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Configuration des gestionnaires d'événements
     */
    setupEventHandlers() {
        this.serviceController.on('service-restarted', (data) => {
            this.emit('action-completed', {
                type: 'restart',
                target: data.serviceName,
                success: data.success,
                timestamp: new Date()
            });
        });
        this.serviceController.on('service-scaled', (data) => {
            this.emit('action-completed', {
                type: 'scale',
                target: data.serviceName,
                success: data.success,
                timestamp: new Date()
            });
        });
    }
    /**
     * Évalue et classe les stratégies par pertinence
     */
    async evaluateStrategies(strategies, context) {
        const evaluatedStrategies = await Promise.all(strategies.map(async (strategy) => {
            let score = 0;
            // Score basé sur l'historique de succès
            const historicalSuccess = this.getHistoricalSuccessRate(strategy, context.anomaly);
            score += historicalSuccess * 0.4;
            // Score basé sur le temps de récupération estimé
            const timeScore = Math.max(0, 1 - (strategy.estimatedRecoveryTime / 600000)); // 10 min max
            score += timeScore * 0.2;
            // Score basé sur le niveau de risque (inversé)
            const riskScore = 1 - strategy.riskLevel;
            score += riskScore * 0.3;
            // Score basé sur la disponibilité des ressources
            const resourceScore = await this.calculateResourceScore(strategy, context);
            score += resourceScore * 0.1;
            return { ...strategy, evaluationScore: score };
        }));
        return evaluatedStrategies.sort((a, b) => b.evaluationScore - a.evaluationScore);
    }
    /**
     * Exécute une action de guérison
     */
    async executeAction(action, context) {
        const timeout = action.timeout || this.defaultTimeout;
        try {
            switch (action.type) {
                case 'restart':
                    await this.serviceController.restartService(action.target, timeout);
                    break;
                case 'scale':
                    const scaleParams = action.parameters || {};
                    await this.serviceController.scaleService(action.target, scaleParams.replicas || scaleParams.instances, timeout);
                    break;
                case 'redirect':
                    await this.serviceController.redirectTraffic(action.target, action.parameters.destination, timeout);
                    break;
                case 'optimize':
                    await this.serviceController.optimizeService(action.target, action.parameters);
                    break;
                case 'isolate':
                    await this.serviceController.isolateComponent(action.target, timeout);
                    break;
                case 'rollback':
                    await this.serviceController.rollbackDeployment(action.target, action.parameters);
                    break;
                case 'notify':
                    await this.sendNotification(action.parameters);
                    break;
                default:
                    throw new Error(`Action non supportée: ${action.type}`);
            }
            logger_1.logger.debug(`✅ Action ${action.type} exécutée avec succès sur ${action.target}`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Échec de l'action ${action.type} sur ${action.target}:`, error);
            throw error;
        }
    }
    /**
     * Effectue un rollback des actions exécutées
     */
    async performRollback(strategy, executedActions, context) {
        logger_1.logger.info('🔄 Début du rollback...');
        // Exécution des actions de rollback dans l'ordre inverse
        for (let i = executedActions.length - 1; i >= 0; i--) {
            const action = executedActions[i];
            try {
                if (action.rollbackAction) {
                    await this.executeAction(action.rollbackAction, context);
                }
                else if (strategy.rollbackActions) {
                    const rollbackAction = strategy.rollbackActions.find(ra => ra.target === action.target);
                    if (rollbackAction) {
                        await this.executeAction(rollbackAction, context);
                    }
                }
            }
            catch (error) {
                logger_1.logger.error(`❌ Erreur lors du rollback de l'action ${action.type}:`, error);
            }
        }
        logger_1.logger.info('✅ Rollback terminé');
    }
    /**
     * Calcule le score d'amélioration après guérison
     */
    calculateImprovementScore(beforeMetrics, afterMetrics, anomaly) {
        let improvementScore = 0;
        let factors = 0;
        // Amélioration de la charge système
        if (beforeMetrics.systemLoad > afterMetrics.systemLoad) {
            improvementScore += (beforeMetrics.systemLoad - afterMetrics.systemLoad) / beforeMetrics.systemLoad;
            factors++;
        }
        // Amélioration du temps de réponse
        if (beforeMetrics.responseTime > afterMetrics.responseTime) {
            improvementScore += (beforeMetrics.responseTime - afterMetrics.responseTime) / beforeMetrics.responseTime;
            factors++;
        }
        // Réduction du taux d'erreur
        if (beforeMetrics.errorRate > afterMetrics.errorRate) {
            improvementScore += (beforeMetrics.errorRate - afterMetrics.errorRate) / Math.max(beforeMetrics.errorRate, 0.01);
            factors++;
        }
        // Amélioration de l'utilisation mémoire
        if (beforeMetrics.memoryUsage > afterMetrics.memoryUsage) {
            improvementScore += (beforeMetrics.memoryUsage - afterMetrics.memoryUsage) / beforeMetrics.memoryUsage;
            factors++;
        }
        return factors > 0 ? improvementScore / factors : 0;
    }
    /**
     * Collecte les métriques actuelles du système
     */
    async collectCurrentMetrics() {
        // Cette méthode devrait être implémentée pour collecter les vraies métriques
        // Pour l'instant, on retourne des métriques simulées
        return {
            timestamp: new Date(),
            systemLoad: Math.random() * 100,
            memoryUsage: Math.random() * 100,
            responseTime: Math.random() * 1000,
            errorRate: Math.random() * 10,
            agentHealth: new Map(),
            synapticHealth: Math.random() * 100,
            throughput: Math.random() * 1000
        };
    }
    /**
     * Obtient les composants affectés par une anomalie
     */
    async getAffectedComponents(anomaly) {
        // Implémentation simplifiée - devrait interroger le système réel
        return anomaly.affectedComponents.map(componentId => ({
            id: componentId,
            name: componentId,
            type: 'service',
            status: 'error',
            health: {
                serviceName: componentId,
                status: 'unhealthy',
                lastCheck: new Date(),
                responseTime: 0,
                errorRate: 0,
                uptime: 0
            },
            metrics: {
                timestamp: new Date(),
                systemLoad: 0,
                memoryUsage: 0,
                responseTime: 0,
                errorRate: 0,
                agentHealth: new Map(),
                synapticHealth: 0,
                throughput: 0
            },
            dependencies: [],
            criticalityLevel: 'medium'
        }));
    }
    /**
     * Obtient les ressources disponibles
     */
    async getAvailableResources() {
        return {
            cpu: 80, // Pourcentage disponible
            memory: 70,
            disk: 90,
            network: 85
        };
    }
    /**
     * Obtient les contraintes système
     */
    async getSystemConstraints() {
        return {
            maxDowntime: 300000, // 5 minutes
            maxResourceUsage: 90, // 90%
            allowedActions: ['restart', 'scale', 'optimize'],
            restrictedComponents: ['critical-database']
        };
    }
    /**
     * Obtient le taux de succès historique d'une stratégie
     */
    getHistoricalSuccessRate(strategy, anomaly) {
        const relevantHealings = this.healingHistory.filter(h => h.strategy.id === strategy.id && h.anomaly.type === anomaly.type);
        if (relevantHealings.length === 0)
            return 0.5; // Score neutre par défaut
        const successfulHealings = relevantHealings.filter(h => h.success && !h.rollbackPerformed);
        return successfulHealings.length / relevantHealings.length;
    }
    /**
     * Calcule le score de ressources pour une stratégie
     */
    async calculateResourceScore(strategy, context) {
        // Analyse simplifiée des besoins en ressources
        let resourceNeed = 0;
        for (const action of strategy.actions) {
            switch (action.type) {
                case 'restart':
                    resourceNeed += 0.2;
                    break;
                case 'scale':
                    resourceNeed += 0.5;
                    break;
                case 'redirect':
                    resourceNeed += 0.1;
                    break;
                default:
                    resourceNeed += 0.3;
            }
        }
        const availableCapacity = Math.min(context.availableResources.cpu, context.availableResources.memory) / 100;
        return Math.max(0, availableCapacity - resourceNeed);
    }
    /**
     * Envoie une notification
     */
    async sendNotification(parameters) {
        // Implémentation de notification - pourrait utiliser Slack, email, etc.
        logger_1.logger.info(`📢 Notification: ${parameters.message}`);
        if (this.communication) {
            await this.communication.broadcast('healing-notification', {
                message: parameters.message,
                severity: parameters.severity || 'info',
                timestamp: new Date()
            });
        }
    }
    /**
     * Met à jour les statistiques
     */
    updateStats(result) {
        this.stats.totalHealings++;
        this.stats.lastHealing = result.endTime;
        if (result.success && !result.rollbackPerformed) {
            this.stats.successfulHealings++;
        }
        else if (result.rollbackPerformed) {
            this.stats.rolledBackHealings++;
        }
        else {
            this.stats.failedHealings++;
        }
        // Calcul de la moyenne du temps de guérison
        const healingTime = result.endTime.getTime() - result.startTime.getTime();
        this.stats.averageHealingTime = (this.stats.averageHealingTime * (this.stats.totalHealings - 1) + healingTime) / this.stats.totalHealings;
        // Ajout à l'historique
        this.healingHistory.push(result);
        if (this.healingHistory.length > this.maxHistorySize) {
            this.healingHistory.shift();
        }
    }
    /**
     * Charge l'historique des guérisons depuis la mémoire
     */
    async loadHealingHistory() {
        try {
            const history = await this.memory.retrieve('healing_history');
            if (history) {
                this.healingHistory = history;
                logger_1.logger.info(`📚 Historique de guérison chargé: ${this.healingHistory.length} entrées`);
            }
        }
        catch (error) {
            logger_1.logger.warn('⚠️ Impossible de charger l\'historique de guérison:', error);
        }
    }
    /**
     * Stocke un résultat de guérison en mémoire
     */
    async storeHealingResult(result) {
        try {
            await this.memory.store('healing_history', this.healingHistory);
            await this.memory.store(`healing_result_${result.startTime.getTime()}`, result);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors du stockage du résultat de guérison:', error);
        }
    }
    /**
     * Obtient les statistiques de l'Auto-Healer
     */
    getStats() {
        return {
            ...this.stats,
            activeHealings: this.activeHealings.size,
            totalStrategies: this.healingStrategies.getStrategiesCount()
        };
    }
}
exports.AutoHealer = AutoHealer;
//# sourceMappingURL=AutoHealer.js.map