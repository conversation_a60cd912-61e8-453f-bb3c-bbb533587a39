{"version": 3, "file": "AdaptationEngine.js", "sourceRoot": "", "sources": ["../../src/immune/AdaptationEngine.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,4CAAyC;AAUzC;;;;;GAKG;AACH,MAAa,gBAAiB,SAAQ,qBAAY;IAyBhD,YAAY,MAA8B;QACxC,KAAK,EAAE,CAAC;QAnBF,kBAAa,GAAY,KAAK,CAAC;QAC/B,oBAAe,GAAgC,IAAI,GAAG,EAAE,CAAC;QACzD,oBAAe,GAKlB,EAAE,CAAC;QAER,+BAA+B;QACvB,kBAAa,GAAG;YACtB,mBAAmB,EAAE,CAAC;YACtB,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;YACf,iBAAiB,EAAE,CAAC;YACpB,YAAY,EAAE,IAAmB;SAClC,CAAC;QAKA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,GAAG,CAAC;QAC/C,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,IAAI,GAAG,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,KAAK,KAAK,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAE5D,uDAAuD;YACvD,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEjC,6CAA6C;YAC7C,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,yCAAyC,IAAI,CAAC,eAAe,CAAC,IAAI,SAAS,CAAC,CAAC;QAE3F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6DAA6D,EAAE,KAAK,CAAC,CAAC;YACnF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,KAAK,CAChB,OAAgB,EAChB,QAAyB,EACzB,MAAqB;QAErB,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,0CAA0C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAE9F,mCAAmC;YACnC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;gBACxB,OAAO;gBACP,QAAQ;gBACR,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,6BAA6B;YAC7B,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC;gBACxC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC;YAED,+BAA+B;YAC/B,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC;YACzC,IAAI,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7C,IAAI,cAAc,GAA0B,IAAI,CAAC;YAEjD,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBAChD,qCAAqC;gBACrC,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC1E,CAAC;iBAAM,CAAC;gBACN,oCAAoC;gBACpC,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC1E,CAAC;YAED,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEjC,OAAO,cAAc,CAAC;QAExB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,KAAK,CAAC,cAA8B;QAC/C,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,4CAA4C,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;YAE/E,yCAAyC;YACzC,cAAc,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YAE5D,yCAAyC;YACzC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC9B,IAAI,EAAE,cAAc;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,aAAa;YACb,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAAgB;QACxC,MAAM,eAAe,GAAqB,EAAE,CAAC;QAE7C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC;YACjD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;gBACzC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAE5D,OAAO,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO;YACL,GAAG,IAAI,CAAC,aAAa;YACrB,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;YACrC,mBAAmB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;iBAC3D,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM;SACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,OAAgB,EAChB,QAAyB,EACzB,MAAqB;QAErB,MAAM,MAAM,GAAG,WAAW,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;QACxD,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE5C,IAAI,IAAI,EAAE,CAAC;YACT,oCAAoC;YACpC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAClE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;YACrE,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;QACpC,CAAC;aAAM,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChF,gCAAgC;YAChC,IAAI,GAAG;gBACL,EAAE,EAAE,MAAM;gBACV,IAAI,EAAE,UAAU,QAAQ,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,EAAE;gBACpD,WAAW,EAAE,aAAa,QAAQ,CAAC,IAAI,wCAAwC,OAAO,CAAC,IAAI,EAAE;gBAC7F,SAAS,EAAE,qBAAqB,OAAO,CAAC,IAAI,8BAA8B,OAAO,CAAC,QAAQ,GAAG;gBAC7F,MAAM,EAAE,oBAAoB,QAAQ,CAAC,EAAE,IAAI;gBAC3C,UAAU,EAAE,IAAI,CAAC,YAAY;gBAC7B,WAAW,EAAE,GAAG;gBAChB,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,WAAW,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE;aAC3C,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,eAAM,CAAC,KAAK,CAAC,yBAAyB,IAAI,CAAC,IAAI,gBAAgB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChG,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,OAAgB,EAChB,QAAyB,EACzB,MAAqB;QAErB,MAAM,MAAM,GAAG,WAAW,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;QACxD,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE5C,IAAI,IAAI,EAAE,CAAC;YACT,oCAAoC;YACpC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACnE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;YACrE,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;QACpC,CAAC;aAAM,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChF,4CAA4C;YAC5C,IAAI,GAAG;gBACL,EAAE,EAAE,MAAM;gBACV,IAAI,EAAE,UAAU,QAAQ,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,EAAE;gBACpD,WAAW,EAAE,aAAa,QAAQ,CAAC,IAAI,0CAA0C,OAAO,CAAC,IAAI,EAAE;gBAC/F,SAAS,EAAE,qBAAqB,OAAO,CAAC,IAAI,8BAA8B,OAAO,CAAC,QAAQ,GAAG;gBAC7F,MAAM,EAAE,mBAAmB,QAAQ,CAAC,EAAE,IAAI;gBAC1C,UAAU,EAAE,IAAI,CAAC,YAAY;gBAC7B,WAAW,EAAE,GAAG;gBAChB,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,WAAW,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE;aAC3C,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;QACpC,CAAC;QAED,0DAA0D;QAC1D,MAAM,aAAa,GAAG,WAAW,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;QAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC5D,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACjF,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,eAAM,CAAC,KAAK,CAAC,2CAA2C,IAAI,CAAC,IAAI,gBAAgB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,WAAmB,EAAE,OAAgB;QAC7D,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACrC,OAAO,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;IAC9E,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,IAAoB,EAAE,OAAgB;QAC7D,IAAI,CAAC;YACH,wCAAwC;YACxC,2FAA2F;YAC3F,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;iBAC7B,OAAO,CAAC,gBAAgB,EAAE,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC;iBAC9C,OAAO,CAAC,oBAAoB,EAAE,IAAI,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;YAE1D,sDAAsD;YACtD,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;gBAC3F,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,8CAA8C,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAC9E,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YAC7D,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAoB,EAAE,EAAE;oBACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBAC1C,CAAC,CAAC,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,MAAM,iDAAiD,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;YACxD,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YAC/D,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,oCAAoC;gBACjF,eAAM,CAAC,IAAI,CAAC,yCAAyC,IAAI,CAAC,eAAe,CAAC,MAAM,UAAU,CAAC,CAAC;YAC9F,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,kFAAkF;YAClF,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mEAAmE,EAAE,KAAK,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;CACF;AAjWD,4CAiWC"}