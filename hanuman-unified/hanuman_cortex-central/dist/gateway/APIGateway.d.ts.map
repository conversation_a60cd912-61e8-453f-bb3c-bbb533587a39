{"version": 3, "file": "APIGateway.d.ts", "sourceRoot": "", "sources": ["../../src/gateway/APIGateway.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAgB,EAAW,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAK5E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,qBAAqB,EAAE,MAAM,wCAAwC,CAAC;AAE/E,MAAM,WAAW,gBAAgB;IAC/B,MAAM,EAAE,GAAG,CAAC;IACZ,MAAM,EAAE,aAAa,CAAC;IACtB,aAAa,EAAE,qBAAqB,CAAC;IACrC,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B;AAED,MAAM,WAAW,QAAQ;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC;IACpD,OAAO,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACxD,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,IAAI,CAAC,CAAC;IAC9E,SAAS,CAAC,EAAE;QACV,QAAQ,EAAE,MAAM,CAAC;QACjB,GAAG,EAAE,MAAM,CAAC;KACb,CAAC;CACH;AAED,MAAM,WAAW,cAAc;IAC7B,aAAa,EAAE,MAAM,CAAC;IACtB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,cAAc,EAAE,MAAM,CAAC;IACvB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,iBAAiB,EAAE,MAAM,CAAC;IAC1B,aAAa,EAAE,MAAM,CAAC;IACtB,eAAe,EAAE,IAAI,CAAC;CACvB;AAED;;;;;;GAMG;AACH,qBAAa,UAAW,SAAQ,YAAY;IAC1C,OAAO,CAAC,GAAG,CAAU;IACrB,OAAO,CAAC,MAAM,CAAM;IACpB,OAAO,CAAC,MAAM,CAAgB;IAC9B,OAAO,CAAC,aAAa,CAAwB;IAC7C,OAAO,CAAC,MAAM,CAAmB;IACjC,OAAO,CAAC,MAAM,CAAM;IACpB,OAAO,CAAC,aAAa,CAAkB;IAGvC,OAAO,CAAC,OAAO,CAQb;IAGF,OAAO,CAAC,gBAAgB,CAAoC;IAG5D,OAAO,CAAC,aAAa,CAA+B;gBAExC,MAAM,EAAE,gBAAgB;IAapC;;OAEG;IACU,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IA8BxC;;OAEG;IACH,OAAO,CAAC,eAAe;IA2CvB;;OAEG;IACH,OAAO,CAAC,eAAe;IA8GvB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAqCzB;;OAEG;IACH,OAAO,CAAC,cAAc;IA0BtB;;OAEG;IACH,OAAO,CAAC,cAAc;IAMtB;;OAEG;YACW,WAAW;IAgBzB;;OAEG;YACW,gBAAgB;IAgB9B;;OAEG;IACH,OAAO,CAAC,wBAAwB;IAUhC;;OAEG;YACW,kBAAkB;IAKhC;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAM9B;;OAEG;IACU,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IAWtC;;OAEG;IACI,UAAU,IAAI,cAAc;IAInC;;OAEG;IACI,OAAO,IAAI,OAAO;CAG1B"}