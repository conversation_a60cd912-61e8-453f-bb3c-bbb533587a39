{"version": 3, "file": "DashboardManager.d.ts", "sourceRoot": "", "sources": ["../../src/dashboard/DashboardManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAEtC,OAAO,EAAE,MAAM,IAAI,UAAU,EAAE,MAAM,MAAM,CAAC;AAE5C,OAAO,EAAE,oBAAoB,EAAE,MAAM,gCAAgC,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,uCAAuC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,MAAM,WAAW,aAAa;IAC5B,SAAS,EAAE,IAAI,CAAC;IAChB,MAAM,EAAE;QACN,MAAM,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,CAAC;QAC3C,MAAM,EAAE,MAAM,CAAC;QACf,OAAO,EAAE,MAAM,CAAC;QAChB,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,MAAM,EAAE;QACN,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;QACf,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/B,WAAW,EAAE;YACX,kBAAkB,EAAE,MAAM,CAAC;YAC3B,mBAAmB,EAAE,MAAM,CAAC;YAC5B,mBAAmB,EAAE,MAAM,CAAC;SAC7B,CAAC;KACH,CAAC;IACF,SAAS,EAAE;QACT,MAAM,EAAE,MAAM,CAAC;QACf,SAAS,EAAE,MAAM,CAAC;QAClB,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;QACf,cAAc,EAAE,GAAG,EAAE,CAAC;KACvB,CAAC;IACF,MAAM,EAAE;QACN,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,MAAM,EAAE,MAAM,CAAC;QACf,aAAa,EAAE,GAAG,EAAE,CAAC;KACtB,CAAC;IACF,OAAO,EAAE;QACP,GAAG,EAAE,MAAM,CAAC;QACZ,MAAM,EAAE,MAAM,CAAC;QACf,OAAO,EAAE;YACP,OAAO,EAAE,MAAM,CAAC;YAChB,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,QAAQ,EAAE;YACR,KAAK,EAAE,MAAM,CAAC;YACd,IAAI,EAAE,MAAM,CAAC;SACd,CAAC;KACH,CAAC;CACH;AAED,MAAM,WAAW,SAAS;IACxB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,OAAO,GAAG,UAAU,CAAC;IAChD,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;IACf,YAAY,EAAE,OAAO,CAAC;IACtB,QAAQ,CAAC,EAAE,GAAG,CAAC;CAChB;AAED;;;;;GAKG;AACH,qBAAa,gBAAiB,SAAQ,YAAY;IAChD,OAAO,CAAC,EAAE,CAAiB;IAC3B,OAAO,CAAC,aAAa,CAAuB;IAC5C,OAAO,CAAC,oBAAoB,CAAuB;IACnD,OAAO,CAAC,MAAM,CAAgB;IAE9B,OAAO,CAAC,gBAAgB,CAA0B;IAClD,OAAO,CAAC,MAAM,CAAqC;IACnD,OAAO,CAAC,cAAc,CAAuB;IAC7C,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,SAAS,CAAoB;gBAGnC,UAAU,EAAE,UAAU,EACtB,aAAa,EAAE,oBAAoB,EACnC,oBAAoB,EAAE,oBAAoB,EAC1C,MAAM,EAAE,aAAa;IAsBvB;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAyE3B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAsC3B;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAc9B;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAwF/B;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAgB7B;;OAEG;IACH,OAAO,CAAC,WAAW;IAYnB;;OAEG;IACH,OAAO,CAAC,WAAW;IA8BnB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAQxB;;OAEG;IACH,OAAO,CAAC,UAAU;IAMlB;;OAEG;IACH,OAAO,CAAC,eAAe;IAKvB;;OAEG;IACH,iBAAiB,CAAC,KAAK,GAAE,MAAW,GAAG,aAAa,EAAE;IAItD;;OAEG;IACH,eAAe,IAAI,SAAS,EAAE;IAI9B;;OAEG;IACH,wBAAwB,IAAI,MAAM;IAIlC;;OAEG;IACH,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI;IAIzC;;OAEG;IACH,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI;IAI9D;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAkBhC"}