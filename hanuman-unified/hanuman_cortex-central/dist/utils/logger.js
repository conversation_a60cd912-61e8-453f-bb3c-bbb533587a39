"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceLogger = exports.synapticLogger = exports.memoryLogger = exports.decisionLogger = exports.neuralLogger = exports.logger = void 0;
exports.createLoggerMiddleware = createLoggerMiddleware;
exports.logPerformanceMetrics = logPerformanceMetrics;
exports.logNeuralActivity = logNeuralActivity;
exports.logDecision = logDecision;
exports.logMemoryOperation = logMemoryOperation;
exports.logSynapticCommunication = logSynapticCommunication;
exports.setupGlobalErrorHandling = setupGlobalErrorHandling;
const winston_1 = __importDefault(require("winston"));
const config_1 = require("../config/config");
// Configuration des formats de log
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaString = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level.toUpperCase()}] ${message} ${metaString}`;
}));
// Configuration des transports
const transports = [
    // Console transport
    new winston_1.default.transports.Console({
        level: config_1.config.logging.level,
        format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple(), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
            const emoji = getLogEmoji(level);
            const metaString = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
            return `${emoji} ${timestamp} [${level}] ${message}${metaString}`;
        }))
    })
];
// File transport si configuré
if (config_1.config.logging.file) {
    transports.push(new winston_1.default.transports.File({
        filename: config_1.config.logging.file,
        level: config_1.config.logging.level,
        format: logFormat,
        maxsize: parseSize(config_1.config.logging.maxSize),
        maxFiles: config_1.config.logging.maxFiles,
        tailable: true
    }));
}
// Création du logger principal
exports.logger = winston_1.default.createLogger({
    level: config_1.config.logging.level,
    format: logFormat,
    transports,
    exitOnError: false,
    // Gestion des exceptions non capturées
    exceptionHandlers: [
        new winston_1.default.transports.Console({
            format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple())
        })
    ],
    // Gestion des rejections non capturées
    rejectionHandlers: [
        new winston_1.default.transports.Console({
            format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple())
        })
    ]
});
// Logger spécialisé pour les activités neuronales
exports.neuralLogger = winston_1.default.createLogger({
    level: 'debug',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.label({ label: 'NEURAL' }), winston_1.default.format.json()),
    transports: [
        new winston_1.default.transports.File({
            filename: '/app/logs/neural-activity.log',
            maxsize: parseSize('50m'),
            maxFiles: 10,
            tailable: true
        })
    ]
});
// Logger spécialisé pour les décisions
exports.decisionLogger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.label({ label: 'DECISION' }), winston_1.default.format.json()),
    transports: [
        new winston_1.default.transports.File({
            filename: '/app/logs/decisions.log',
            maxsize: parseSize('100m'),
            maxFiles: 5,
            tailable: true
        })
    ]
});
// Logger spécialisé pour la mémoire
exports.memoryLogger = winston_1.default.createLogger({
    level: 'debug',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.label({ label: 'MEMORY' }), winston_1.default.format.json()),
    transports: [
        new winston_1.default.transports.File({
            filename: '/app/logs/memory.log',
            maxsize: parseSize('200m'),
            maxFiles: 3,
            tailable: true
        })
    ]
});
// Logger spécialisé pour la communication synaptique
exports.synapticLogger = winston_1.default.createLogger({
    level: 'debug',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.label({ label: 'SYNAPTIC' }), winston_1.default.format.json()),
    transports: [
        new winston_1.default.transports.File({
            filename: '/app/logs/synaptic.log',
            maxsize: parseSize('100m'),
            maxFiles: 5,
            tailable: true
        })
    ]
});
// Logger pour les performances
exports.performanceLogger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.label({ label: 'PERFORMANCE' }), winston_1.default.format.json()),
    transports: [
        new winston_1.default.transports.File({
            filename: '/app/logs/performance.log',
            maxsize: parseSize('50m'),
            maxFiles: 7,
            tailable: true
        })
    ]
});
// Fonctions utilitaires
function getLogEmoji(level) {
    const emojis = {
        error: '❌',
        warn: '⚠️',
        info: 'ℹ️',
        debug: '🔍',
        verbose: '📝'
    };
    return emojis[level] || '📋';
}
function parseSize(size) {
    const units = {
        b: 1,
        k: 1024,
        m: 1024 * 1024,
        g: 1024 * 1024 * 1024
    };
    const match = size.toLowerCase().match(/^(\d+)([bkmg]?)$/);
    if (!match)
        return 10 * 1024 * 1024; // 10MB par défaut
    const [, num, unit] = match;
    return parseInt(num) * (units[unit] || 1);
}
// Middleware de logging pour Express
function createLoggerMiddleware() {
    return (req, res, next) => {
        const start = Date.now();
        res.on('finish', () => {
            const duration = Date.now() - start;
            const logData = {
                method: req.method,
                url: req.url,
                status: res.statusCode,
                duration: `${duration}ms`,
                userAgent: req.get('User-Agent'),
                ip: req.ip
            };
            if (res.statusCode >= 400) {
                exports.logger.warn('HTTP Request', logData);
            }
            else {
                exports.logger.info('HTTP Request', logData);
            }
        });
        next();
    };
}
// Fonction pour logger les métriques de performance
function logPerformanceMetrics(component, metrics) {
    exports.performanceLogger.info('Performance Metrics', {
        component,
        metrics,
        timestamp: new Date().toISOString()
    });
}
// Fonction pour logger l'activité neuronale
function logNeuralActivity(activity) {
    exports.neuralLogger.debug('Neural Activity', {
        ...activity,
        timestamp: new Date().toISOString()
    });
}
// Fonction pour logger les décisions
function logDecision(decision) {
    exports.decisionLogger.info('Decision Made', {
        ...decision,
        timestamp: new Date().toISOString()
    });
}
// Fonction pour logger les opérations mémoire
function logMemoryOperation(operation) {
    exports.memoryLogger.debug('Memory Operation', {
        ...operation,
        timestamp: new Date().toISOString()
    });
}
// Fonction pour logger la communication synaptique
function logSynapticCommunication(communication) {
    exports.synapticLogger.debug('Synaptic Communication', {
        ...communication,
        timestamp: new Date().toISOString()
    });
}
// Gestionnaire d'erreurs global
function setupGlobalErrorHandling() {
    process.on('uncaughtException', (error) => {
        exports.logger.error('Uncaught Exception', {
            error: error.message,
            stack: error.stack
        });
        process.exit(1);
    });
    process.on('unhandledRejection', (reason, promise) => {
        exports.logger.error('Unhandled Rejection', {
            reason,
            promise
        });
    });
}
// Configuration du logger en mode développement
if (config_1.config.server.environment === 'development') {
    exports.logger.add(new winston_1.default.transports.Console({
        level: 'debug',
        format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple(), winston_1.default.format.printf(({ level, message, timestamp, ...meta }) => {
            const emoji = getLogEmoji(level);
            return `${emoji} ${timestamp} [${level}] ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
        }))
    }));
}
// Export du logger par défaut
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map