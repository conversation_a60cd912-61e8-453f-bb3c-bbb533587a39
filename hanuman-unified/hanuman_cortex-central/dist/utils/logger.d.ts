export declare const logger: any;
export declare const neuralLogger: any;
export declare const decisionLogger: any;
export declare const memoryLogger: any;
export declare const synapticLogger: any;
export declare const performanceLogger: any;
export declare function createLoggerMiddleware(): (req: any, res: any, next: any) => void;
export declare function logPerformanceMetrics(component: string, metrics: any): void;
export declare function logNeuralActivity(activity: any): void;
export declare function logDecision(decision: any): void;
export declare function logMemoryOperation(operation: any): void;
export declare function logSynapticCommunication(communication: any): void;
export declare function setupGlobalErrorHandling(): void;
export default logger;
//# sourceMappingURL=logger.d.ts.map