import winston from 'winston';
import { config } from '../config/config';

// Configuration des formats de log
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaString = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level.toUpperCase()}] ${message} ${metaString}`;
  })
);

// Configuration des transports
const transports: winston.transport[] = [
  // Console transport
  new winston.transports.Console({
    level: config.logging.level,
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        const emoji = getLogEmoji(level);
        const metaString = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
        return `${emoji} ${timestamp} [${level}] ${message}${metaString}`;
      })
    )
  })
];

// File transport si configuré
if (config.logging.file) {
  transports.push(
    new winston.transports.File({
      filename: config.logging.file,
      level: config.logging.level,
      format: logFormat,
      maxsize: parseSize(config.logging.maxSize),
      maxFiles: config.logging.maxFiles,
      tailable: true
    })
  );
}

// Création du logger principal
export const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  transports,
  exitOnError: false,
  // Gestion des exceptions non capturées
  exceptionHandlers: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ],
  // Gestion des rejections non capturées
  rejectionHandlers: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Logger spécialisé pour les activités neuronales
export const neuralLogger = winston.createLogger({
  level: 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.label({ label: 'NEURAL' }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: '/app/logs/neural-activity.log',
      maxsize: parseSize('50m'),
      maxFiles: 10,
      tailable: true
    })
  ]
});

// Logger spécialisé pour les décisions
export const decisionLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.label({ label: 'DECISION' }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: '/app/logs/decisions.log',
      maxsize: parseSize('100m'),
      maxFiles: 5,
      tailable: true
    })
  ]
});

// Logger spécialisé pour la mémoire
export const memoryLogger = winston.createLogger({
  level: 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.label({ label: 'MEMORY' }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: '/app/logs/memory.log',
      maxsize: parseSize('200m'),
      maxFiles: 3,
      tailable: true
    })
  ]
});

// Logger spécialisé pour la communication synaptique
export const synapticLogger = winston.createLogger({
  level: 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.label({ label: 'SYNAPTIC' }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: '/app/logs/synaptic.log',
      maxsize: parseSize('100m'),
      maxFiles: 5,
      tailable: true
    })
  ]
});

// Logger pour les performances
export const performanceLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.label({ label: 'PERFORMANCE' }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: '/app/logs/performance.log',
      maxsize: parseSize('50m'),
      maxFiles: 7,
      tailable: true
    })
  ]
});

// Fonctions utilitaires
function getLogEmoji(level: string): string {
  const emojis: { [key: string]: string } = {
    error: '❌',
    warn: '⚠️',
    info: 'ℹ️',
    debug: '🔍',
    verbose: '📝'
  };
  return emojis[level] || '📋';
}

function parseSize(size: string): number {
  const units: { [key: string]: number } = {
    b: 1,
    k: 1024,
    m: 1024 * 1024,
    g: 1024 * 1024 * 1024
  };
  
  const match = size.toLowerCase().match(/^(\d+)([bkmg]?)$/);
  if (!match) return 10 * 1024 * 1024; // 10MB par défaut
  
  const [, num, unit] = match;
  return parseInt(num) * (units[unit] || 1);
}

// Middleware de logging pour Express
export function createLoggerMiddleware() {
  return (req: any, res: any, next: any) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      const logData = {
        method: req.method,
        url: req.url,
        status: res.statusCode,
        duration: `${duration}ms`,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      };
      
      if (res.statusCode >= 400) {
        logger.warn('HTTP Request', logData);
      } else {
        logger.info('HTTP Request', logData);
      }
    });
    
    next();
  };
}

// Fonction pour logger les métriques de performance
export function logPerformanceMetrics(component: string, metrics: any): void {
  performanceLogger.info('Performance Metrics', {
    component,
    metrics,
    timestamp: new Date().toISOString()
  });
}

// Fonction pour logger l'activité neuronale
export function logNeuralActivity(activity: any): void {
  neuralLogger.debug('Neural Activity', {
    ...activity,
    timestamp: new Date().toISOString()
  });
}

// Fonction pour logger les décisions
export function logDecision(decision: any): void {
  decisionLogger.info('Decision Made', {
    ...decision,
    timestamp: new Date().toISOString()
  });
}

// Fonction pour logger les opérations mémoire
export function logMemoryOperation(operation: any): void {
  memoryLogger.debug('Memory Operation', {
    ...operation,
    timestamp: new Date().toISOString()
  });
}

// Fonction pour logger la communication synaptique
export function logSynapticCommunication(communication: any): void {
  synapticLogger.debug('Synaptic Communication', {
    ...communication,
    timestamp: new Date().toISOString()
  });
}

// Gestionnaire d'erreurs global
export function setupGlobalErrorHandling(): void {
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection', {
      reason,
      promise
    });
  });
}

// Configuration du logger en mode développement
if (config.server.environment === 'development') {
  logger.add(new winston.transports.Console({
    level: 'debug',
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ level, message, timestamp, ...meta }) => {
        const emoji = getLogEmoji(level);
        return `${emoji} ${timestamp} [${level}] ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
      })
    )
  }));
}

// Export du logger par défaut
export default logger;
