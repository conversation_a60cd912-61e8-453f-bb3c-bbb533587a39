FROM node:18-alpine

WORKDIR /app

# Installation des dépendances système
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    curl \
    git

# Copie des fichiers de configuration
COPY package*.json ./
COPY tsconfig.json ./

# Installation des dépendances
RUN npm ci --only=production

# Copie du code source
COPY src/ ./src/
COPY config/ ./config/

# Build de l'application
RUN npm run build

# Création des répertoires nécessaires
RUN mkdir -p /app/memory /app/logs /app/workspace

# Exposition du port
EXPOSE 8080

# Variables d'environnement par défaut
ENV NODE_ENV=production
ENV PORT=8080
ENV NEURAL_NETWORK_MODE=active
ENV DECISION_ENGINE=enabled

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Commande de démarrage
CMD ["npm", "start"]
