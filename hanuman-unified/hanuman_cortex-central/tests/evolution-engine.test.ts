/**
 * Tests pour l'EvolutionEngine
 * Tests unitaires et d'intégration pour le système d'évolution continue
 */

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import {
  EvolutionEngine,
  TechnologyScanner,
  ImpactAnalyzer,
  EvolutionPlanner,
  AgentTrainer,
  DeploymentManager,
  ValidationSystem,
  createEvolutionEngine,
  EvolutionUtils,
  DEFAULT_EVOLUTION_CONFIG
} from '../src/evolution';

describe('EvolutionEngine', () => {
  let evolutionEngine: EvolutionEngine;
  let mockConfig: any;

  beforeEach(() => {
    mockConfig = {
      ...DEFAULT_EVOLUTION_CONFIG,
      scanInterval: 0.1, // 6 minutes pour les tests
      enableAutomaticEvolution: false,
      technologySources: [],
      autoApproveThreshold: 50
    };
    
    evolutionEngine = createEvolutionEngine(mockConfig);
  });

  afterEach(async () => {
    if (evolutionEngine.isEvolutionRunning()) {
      await evolutionEngine.stop();
    }
  });

  describe('Initialisation', () => {
    test('devrait créer une instance avec la configuration par défaut', () => {
      const engine = createEvolutionEngine();
      expect(engine).toBeInstanceOf(EvolutionEngine);
      expect(engine.getConfig()).toMatchObject(DEFAULT_EVOLUTION_CONFIG);
    });

    test('devrait créer une instance avec une configuration personnalisée', () => {
      const customConfig = { ...mockConfig, scanInterval: 2 };
      const engine = createEvolutionEngine(customConfig);
      expect(engine.getConfig().scanInterval).toBe(2);
    });

    test('devrait démarrer et arrêter correctement', async () => {
      await expect(evolutionEngine.start()).resolves.not.toThrow();
      expect(evolutionEngine.isEvolutionRunning()).toBe(true);
      
      await expect(evolutionEngine.stop()).resolves.not.toThrow();
      expect(evolutionEngine.isEvolutionRunning()).toBe(false);
    });
  });

  describe('Cycle d\'évolution', () => {
    beforeEach(async () => {
      await evolutionEngine.start();
    });

    test('devrait exécuter un cycle d\'évolution complet', async () => {
      const report = await evolutionEngine.performEvolutionCycle();
      
      expect(report).toHaveProperty('id');
      expect(report).toHaveProperty('technologiesEvaluated');
      expect(report).toHaveProperty('agentsUpdated');
      expect(report).toHaveProperty('performanceGains');
      expect(report).toHaveProperty('rollbacks');
      expect(report).toHaveProperty('duration');
      expect(report).toHaveProperty('successRate');
      expect(report.duration).toBeGreaterThan(0);
    });

    test('devrait retourner un rapport vide si aucune technologie n\'est trouvée', async () => {
      const report = await evolutionEngine.performEvolutionCycle();
      
      expect(report.technologiesEvaluated).toBe(0);
      expect(report.agentsUpdated).toBe(0);
      expect(report.recommendations).toContain('No significant technologies found for evolution');
    });

    test('devrait émettre des événements pendant le cycle', async () => {
      const events: any[] = [];
      
      evolutionEngine.on('evolutionEvent', (event) => {
        events.push(event);
      });

      await evolutionEngine.performEvolutionCycle();
      
      expect(events.length).toBeGreaterThan(0);
      expect(events[0]).toHaveProperty('type');
      expect(events[0]).toHaveProperty('timestamp');
      expect(events[0]).toHaveProperty('message');
    });
  });

  describe('Métriques', () => {
    test('devrait retourner des métriques initiales', () => {
      const metrics = evolutionEngine.getMetrics();
      
      expect(metrics).toHaveProperty('totalEvolutions');
      expect(metrics).toHaveProperty('successfulEvolutions');
      expect(metrics).toHaveProperty('failedEvolutions');
      expect(metrics).toHaveProperty('averageDuration');
      expect(metrics).toHaveProperty('averagePerformanceGain');
      expect(metrics).toHaveProperty('totalRollbacks');
      expect(metrics).toHaveProperty('technologiesAdopted');
      expect(metrics).toHaveProperty('agentsEvolved');
      expect(metrics).toHaveProperty('lastEvolution');
      expect(metrics).toHaveProperty('nextScheduledEvolution');
      
      expect(metrics.totalEvolutions).toBe(0);
      expect(metrics.successfulEvolutions).toBe(0);
      expect(metrics.failedEvolutions).toBe(0);
    });

    test('devrait mettre à jour les métriques après un cycle', async () => {
      await evolutionEngine.start();
      
      const initialMetrics = evolutionEngine.getMetrics();
      await evolutionEngine.performEvolutionCycle();
      const finalMetrics = evolutionEngine.getMetrics();
      
      expect(finalMetrics.totalEvolutions).toBe(initialMetrics.totalEvolutions + 1);
      expect(finalMetrics.lastEvolution.getTime()).toBeGreaterThan(initialMetrics.lastEvolution.getTime());
    });
  });

  describe('Gestion des erreurs', () => {
    test('devrait gérer les erreurs de démarrage', async () => {
      // Configuration invalide
      const invalidEngine = new EvolutionEngine({
        ...mockConfig,
        scanInterval: -1 // Valeur invalide
      } as any);

      await expect(invalidEngine.start()).rejects.toThrow();
    });

    test('devrait gérer les erreurs pendant le cycle d\'évolution', async () => {
      await evolutionEngine.start();
      
      // Mock d'une erreur dans le scanner
      jest.spyOn(evolutionEngine as any, 'scanTechnologyLandscape')
        .mockRejectedValueOnce(new Error('Scanner error'));

      await expect(evolutionEngine.performEvolutionCycle()).rejects.toThrow('Scanner error');
    });
  });
});

describe('TechnologyScanner', () => {
  let scanner: TechnologyScanner;

  beforeEach(() => {
    scanner = new TechnologyScanner([]);
  });

  test('devrait s\'initialiser correctement', async () => {
    await expect(scanner.initialize()).resolves.not.toThrow();
  });

  test('devrait retourner une liste vide sans sources', async () => {
    await scanner.initialize();
    const technologies = await scanner.scanAll();
    expect(technologies).toEqual([]);
  });

  test('devrait filtrer les technologies par pertinence', async () => {
    const mockSources = [
      {
        name: 'Test Source',
        type: 'github' as const,
        url: 'https://test.com',
        lastScanned: new Date(),
        isActive: true,
        scanInterval: 24,
        priority: 5
      }
    ];

    const scannerWithSources = new TechnologyScanner(mockSources);
    await scannerWithSources.initialize();
    
    const technologies = await scannerWithSources.scanAll();
    expect(Array.isArray(technologies)).toBe(true);
  });
});

describe('ImpactAnalyzer', () => {
  let analyzer: ImpactAnalyzer;

  beforeEach(async () => {
    analyzer = new ImpactAnalyzer();
    await analyzer.initialize();
  });

  test('devrait analyser l\'impact d\'une technologie', async () => {
    const mockTechnology = {
      id: 'test-tech',
      name: 'TestTech',
      category: 'library' as const,
      version: '1.0.0',
      description: 'Test technology',
      maturity: 'stable' as const,
      adoptionRate: 0.8,
      discoveredAt: new Date(),
      source: 'test',
      relevanceScore: 85,
      tags: ['test']
    };

    const analysis = await analyzer.analyze(mockTechnology);
    
    expect(analysis).toHaveProperty('technology');
    expect(analysis).toHaveProperty('impactScore');
    expect(analysis).toHaveProperty('affectedAgents');
    expect(analysis).toHaveProperty('benefits');
    expect(analysis).toHaveProperty('risks');
    expect(analysis).toHaveProperty('migrationComplexity');
    expect(analysis).toHaveProperty('estimatedEffort');
    expect(analysis).toHaveProperty('businessValue');
    
    expect(analysis.impactScore).toBeGreaterThanOrEqual(0);
    expect(analysis.impactScore).toBeLessThanOrEqual(100);
    expect(Array.isArray(analysis.affectedAgents)).toBe(true);
    expect(Array.isArray(analysis.benefits)).toBe(true);
    expect(Array.isArray(analysis.risks)).toBe(true);
  });
});

describe('EvolutionPlanner', () => {
  let planner: EvolutionPlanner;

  beforeEach(async () => {
    planner = new EvolutionPlanner();
    await planner.initialize();
  });

  test('devrait créer un plan d\'évolution', async () => {
    const mockAnalyses = [
      {
        technology: {
          id: 'test-tech',
          name: 'TestTech',
          category: 'library' as const,
          version: '1.0.0',
          description: 'Test technology',
          maturity: 'stable' as const,
          adoptionRate: 0.8,
          discoveredAt: new Date(),
          source: 'test',
          relevanceScore: 85,
          tags: ['test']
        },
        impactScore: 80,
        affectedAgents: ['agent-frontend'],
        benefits: ['Improved performance'],
        risks: ['Learning curve'],
        migrationComplexity: 'medium' as const,
        estimatedEffort: 16,
        businessValue: 75,
        technicalDebt: 20,
        securityImplications: [],
        performanceImpact: 10,
        compatibilityIssues: [],
        dependencies: [],
        breakingChanges: [],
        learningCurve: 5,
        communitySupport: 8,
        ecosystemMaturity: 7
      }
    ];

    const plan = await planner.createPlan(mockAnalyses);
    
    expect(plan).toHaveProperty('id');
    expect(plan).toHaveProperty('name');
    expect(plan).toHaveProperty('description');
    expect(plan).toHaveProperty('technologies');
    expect(plan).toHaveProperty('phases');
    expect(plan).toHaveProperty('totalDuration');
    expect(plan).toHaveProperty('priority');
    expect(plan).toHaveProperty('status');
    expect(plan).toHaveProperty('rollbackPlan');
    
    expect(plan.phases.length).toBeGreaterThan(0);
    expect(plan.totalDuration).toBeGreaterThan(0);
    expect(plan.status).toBe('planned');
  });
});

describe('EvolutionUtils', () => {
  test('devrait valider une configuration correcte', () => {
    expect(() => EvolutionUtils.validateConfig(DEFAULT_EVOLUTION_CONFIG)).not.toThrow();
  });

  test('devrait rejeter une configuration invalide', () => {
    const invalidConfig = {
      ...DEFAULT_EVOLUTION_CONFIG,
      scanInterval: -1
    };
    
    expect(() => EvolutionUtils.validateConfig(invalidConfig)).toThrow();
  });

  test('devrait créer une configuration de développement', () => {
    const devConfig = EvolutionUtils.createDevConfig();
    
    expect(devConfig.scanInterval).toBe(1);
    expect(devConfig.enableAutomaticEvolution).toBe(false);
    expect(devConfig.autoApproveThreshold).toBe(90);
  });

  test('devrait créer une configuration de production', () => {
    const prodConfig = EvolutionUtils.createProdConfig();
    
    expect(prodConfig.scanInterval).toBe(48);
    expect(prodConfig.enableAutomaticEvolution).toBe(true);
    expect(prodConfig.maxConcurrentEvolutions).toBe(1);
  });

  test('devrait créer une configuration de test', () => {
    const testConfig = EvolutionUtils.createTestConfig();
    
    expect(testConfig.scanInterval).toBe(0.1);
    expect(testConfig.enableAutomaticEvolution).toBe(false);
    expect(testConfig.technologySources).toEqual([]);
  });
});

describe('Intégration complète', () => {
  test('devrait exécuter un cycle d\'évolution de bout en bout', async () => {
    const testConfig = EvolutionUtils.createTestConfig();
    const engine = createEvolutionEngine(testConfig);
    
    const events: any[] = [];
    engine.on('evolutionEvent', (event) => {
      events.push(event);
    });

    await engine.start();
    
    const initialMetrics = engine.getMetrics();
    const report = await engine.performEvolutionCycle();
    const finalMetrics = engine.getMetrics();
    
    await engine.stop();

    // Vérifications du rapport
    expect(report).toHaveProperty('id');
    expect(report.duration).toBeGreaterThan(0);
    expect(report.successRate).toBeGreaterThanOrEqual(0);
    expect(report.successRate).toBeLessThanOrEqual(100);

    // Vérifications des métriques
    expect(finalMetrics.totalEvolutions).toBe(initialMetrics.totalEvolutions + 1);

    // Vérifications des événements
    expect(events.length).toBeGreaterThan(0);
    expect(events.some(e => e.type === 'scan_started')).toBe(true);
  });

  test('devrait gérer les évolutions simultanées', async () => {
    const testConfig = {
      ...EvolutionUtils.createTestConfig(),
      maxConcurrentEvolutions: 2
    };
    const engine = createEvolutionEngine(testConfig);

    await engine.start();

    // Lancement de deux cycles simultanés
    const promise1 = engine.performEvolutionCycle();
    const promise2 = engine.performEvolutionCycle();

    const [report1, report2] = await Promise.all([promise1, promise2]);

    expect(report1).toHaveProperty('id');
    expect(report2).toHaveProperty('id');
    expect(report1.id).not.toBe(report2.id);

    await engine.stop();
  });
});
