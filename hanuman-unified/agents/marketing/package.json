{"name": "agent-marketing", "version": "1.0.0", "description": "Agent Marketing - Stratégie marketing, campagnes et optimisation conversion", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["marketing", "campaigns", "conversion-optimization", "ab-testing", "social-media", "analytics", "ai-agent"], "author": "Retreat & Be AI Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "winston": "^3.11.0", "kafkajs": "^2.2.4", "ioredis": "^5.3.2", "weaviate-ts-client": "^1.5.0", "axios": "^1.6.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.1", "uuid": "^9.0.1", "node-cron": "^3.0.3", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.5", "@types/bcrypt": "^5.0.2", "@types/uuid": "^9.0.7", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0"}, "engines": {"node": ">=18.0.0"}}