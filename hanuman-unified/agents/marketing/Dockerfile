# Agent Marketing - Dockerfile multi-stage optimisé
FROM node:18-alpine AS builder

# Métadonnées
LABEL maintainer="Retreat & Be AI Team"
LABEL description="Agent Marketing - Stratégie marketing et optimisation conversion"
LABEL version="1.0.0"

# Variables d'environnement de build
ENV NODE_ENV=production
ENV NPM_CONFIG_LOGLEVEL=warn

# Répertoire de travail
WORKDIR /app

# Copie des fichiers de dépendances
COPY package*.json ./
COPY tsconfig.json ./

# Installation des dépendances
RUN npm ci --only=production && npm cache clean --force

# Copie du code source
COPY src/ ./src/

# Build de l'application
RUN npm run build

# Stage de production
FROM node:18-alpine AS production

# Installation des outils système nécessaires
RUN apk add --no-cache \
    curl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Création d'un utilisateur non-root
RUN addgroup -g 1001 -S nodejs && \
    adduser -S marketing -u 1001

# Répertoire de travail
WORKDIR /app

# Copie des dépendances depuis le builder
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package*.json ./

# Changement de propriétaire
RUN chown -R marketing:nodejs /app

# Utilisateur non-root
USER marketing

# Port d'exposition
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Commande de démarrage
CMD ["node", "dist/index.js"]
