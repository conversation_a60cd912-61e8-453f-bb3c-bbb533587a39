import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { <PERSON><PERSON><PERSON>, Producer, Consumer, KafkaMessage } from 'kafkajs';
import { MarketingResponse, CommunicationMessage } from '../types';

/**
 * Système de communication pour l'Agent Marketing
 * Utilise Kafka pour la communication synaptique avec les autres agents
 */
export class MarketingCommunication extends EventEmitter {
  private kafka: Kafka;
  private producer: Producer;
  private consumer: Consumer;
  private logger: Logger;
  private isConnected: boolean = false;

  // Topics Kafka
  private readonly topics = {
    // Topics de sortie (publication)
    marketingStrategy: 'marketing.strategy.created',
    campaignLaunched: 'marketing.campaign.launched',
    conversionOptimized: 'marketing.conversion.optimized',
    analyticsGenerated: 'marketing.analytics.generated',
    socialMediaManaged: 'marketing.social.managed',
    
    // Topics d'entrée (consommation)
    seoInsights: 'seo.insights.generated',
    contentReady: 'content.creation.completed',
    uxFeedback: 'uiux.feedback.provided',
    translationCompleted: 'translation.completed',
    webResearchCompleted: 'web-research.completed',
    
    // Topics de requête
    requestSEO: 'request.seo.optimization',
    requestContent: 'request.content.creation',
    requestTranslation: 'request.translation',
    requestWebResearch: 'request.web-research',
    requestUXInsights: 'request.uiux.insights'
  };

  constructor(logger: Logger) {
    super();
    this.logger = logger;
    
    this.kafka = new Kafka({
      clientId: 'agent-marketing',
      brokers: [process.env.KAFKA_BROKER || 'localhost:9092'],
      retry: {
        initialRetryTime: 100,
        retries: 8
      }
    });

    this.producer = this.kafka.producer({
      maxInFlightRequests: 1,
      idempotent: true,
      transactionTimeout: 30000
    });

    this.consumer = this.kafka.consumer({
      groupId: 'agent-marketing-group',
      sessionTimeout: 30000,
      heartbeatInterval: 3000
    });
  }

  /**
   * Initialise la communication
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initialisation de la communication marketing...');

      // Connexion du producer
      await this.producer.connect();

      // Connexion du consumer
      await this.consumer.connect();

      // Souscription aux topics
      await this.subscribeToTopics();

      // Démarrage de l'écoute
      await this.startListening();

      this.isConnected = true;
      this.logger.info('Communication marketing initialisée avec succès');

    } catch (error) {
      this.logger.error('Erreur lors de l\'initialisation de la communication:', error);
      throw error;
    }
  }

  /**
   * Publie une réponse marketing
   */
  async publishResponse(response: MarketingResponse): Promise<void> {
    try {
      const message = {
        key: response.requestId,
        value: JSON.stringify(response),
        timestamp: Date.now().toString()
      };

      await this.producer.send({
        topic: this.getTopicForResponseType(response.type),
        messages: [message]
      });

      this.logger.info(`Réponse publiée: ${response.type}`, { requestId: response.requestId });

    } catch (error) {
      this.logger.error('Erreur lors de la publication de la réponse:', error);
      throw error;
    }
  }

  /**
   * Notifie l'agent SEO
   */
  async notifySEOAgent(payload: any): Promise<void> {
    try {
      const message = {
        key: payload.strategy?.id || 'seo-notification',
        value: JSON.stringify({
          from: 'agent-marketing',
          to: 'agent-seo',
          type: payload.type,
          payload: payload,
          timestamp: new Date().toISOString()
        })
      };

      await this.producer.send({
        topic: this.topics.requestSEO,
        messages: [message]
      });

      this.logger.info('Notification envoyée à l\'agent SEO', { type: payload.type });

    } catch (error) {
      this.logger.error('Erreur lors de la notification SEO:', error);
      throw error;
    }
  }

  /**
   * Notifie l'agent de traduction
   */
  async notifyTranslationAgent(payload: any): Promise<void> {
    try {
      const message = {
        key: payload.strategy?.id || 'translation-notification',
        value: JSON.stringify({
          from: 'agent-marketing',
          to: 'agent-translation',
          type: payload.type,
          payload: payload,
          timestamp: new Date().toISOString()
        })
      };

      await this.producer.send({
        topic: this.topics.requestTranslation,
        messages: [message]
      });

      this.logger.info('Notification envoyée à l\'agent de traduction', { type: payload.type });

    } catch (error) {
      this.logger.error('Erreur lors de la notification de traduction:', error);
      throw error;
    }
  }

  /**
   * Demande une recherche web
   */
  async requestWebResearch(payload: any): Promise<any> {
    try {
      const correlationId = `web-research-${Date.now()}`;
      
      const message = {
        key: correlationId,
        value: JSON.stringify({
          from: 'agent-marketing',
          to: 'agent-web-research',
          type: 'research_request',
          payload: payload,
          correlationId: correlationId,
          timestamp: new Date().toISOString()
        })
      };

      await this.producer.send({
        topic: this.topics.requestWebResearch,
        messages: [message]
      });

      this.logger.info('Demande de recherche web envoyée', { correlationId });

      // Retourner une promesse qui sera résolue quand la réponse arrive
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout: Pas de réponse de l\'agent web research'));
        }, 30000);

        const handler = (response: any) => {
          if (response.correlationId === correlationId) {
            clearTimeout(timeout);
            this.off('webResearchResponse', handler);
            resolve(response.payload);
          }
        };

        this.on('webResearchResponse', handler);
      });

    } catch (error) {
      this.logger.error('Erreur lors de la demande de recherche web:', error);
      throw error;
    }
  }

  /**
   * Demande des insights UX
   */
  async requestUXInsights(payload: any): Promise<any> {
    try {
      const correlationId = `ux-insights-${Date.now()}`;
      
      const message = {
        key: correlationId,
        value: JSON.stringify({
          from: 'agent-marketing',
          to: 'agent-uiux',
          type: 'insights_request',
          payload: payload,
          correlationId: correlationId,
          timestamp: new Date().toISOString()
        })
      };

      await this.producer.send({
        topic: this.topics.requestUXInsights,
        messages: [message]
      });

      this.logger.info('Demande d\'insights UX envoyée', { correlationId });

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout: Pas de réponse de l\'agent UX'));
        }, 30000);

        const handler = (response: any) => {
          if (response.correlationId === correlationId) {
            clearTimeout(timeout);
            this.off('uxInsightsResponse', handler);
            resolve(response.payload);
          }
        };

        this.on('uxInsightsResponse', handler);
      });

    } catch (error) {
      this.logger.error('Erreur lors de la demande d\'insights UX:', error);
      throw error;
    }
  }

  /**
   * Demande la création de contenu
   */
  async requestContentCreation(payload: any): Promise<any> {
    try {
      const correlationId = `content-creation-${Date.now()}`;
      
      const message = {
        key: correlationId,
        value: JSON.stringify({
          from: 'agent-marketing',
          to: 'agent-content-creator',
          type: 'content_request',
          payload: payload,
          correlationId: correlationId,
          timestamp: new Date().toISOString()
        })
      };

      await this.producer.send({
        topic: this.topics.requestContent,
        messages: [message]
      });

      this.logger.info('Demande de création de contenu envoyée', { correlationId });

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout: Pas de réponse de l\'agent content creator'));
        }, 30000);

        const handler = (response: any) => {
          if (response.correlationId === correlationId) {
            clearTimeout(timeout);
            this.off('contentCreationResponse', handler);
            resolve(response.payload);
          }
        };

        this.on('contentCreationResponse', handler);
      });

    } catch (error) {
      this.logger.error('Erreur lors de la demande de création de contenu:', error);
      throw error;
    }
  }

  /**
   * Souscrit aux topics Kafka
   */
  private async subscribeToTopics(): Promise<void> {
    const inputTopics = [
      this.topics.seoInsights,
      this.topics.contentReady,
      this.topics.uxFeedback,
      this.topics.translationCompleted,
      this.topics.webResearchCompleted
    ];

    for (const topic of inputTopics) {
      await this.consumer.subscribe({ topic, fromBeginning: false });
      this.logger.info(`Souscription au topic: ${topic}`);
    }
  }

  /**
   * Démarre l'écoute des messages
   */
  private async startListening(): Promise<void> {
    await this.consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        try {
          await this.handleMessage(topic, message);
        } catch (error) {
          this.logger.error('Erreur lors du traitement du message:', error);
        }
      }
    });
  }

  /**
   * Traite un message reçu
   */
  private async handleMessage(topic: string, message: KafkaMessage): Promise<void> {
    try {
      const messageValue = message.value?.toString();
      if (!messageValue) return;

      const parsedMessage: CommunicationMessage = JSON.parse(messageValue);
      
      this.logger.info(`Message reçu du topic ${topic}:`, {
        from: parsedMessage.from,
        type: parsedMessage.type
      });

      // Émettre l'événement approprié
      switch (topic) {
        case this.topics.seoInsights:
          this.emit('messageReceived', {
            from: 'agent-seo',
            type: 'seo_insights',
            payload: parsedMessage.payload
          });
          break;

        case this.topics.contentReady:
          this.emit('messageReceived', {
            from: 'agent-content-creator',
            type: 'content_ready',
            payload: parsedMessage.payload
          });
          
          // Si c'est une réponse à une demande, émettre l'événement spécifique
          if (parsedMessage.correlationId) {
            this.emit('contentCreationResponse', {
              correlationId: parsedMessage.correlationId,
              payload: parsedMessage.payload
            });
          }
          break;

        case this.topics.uxFeedback:
          this.emit('messageReceived', {
            from: 'agent-uiux',
            type: 'ux_feedback',
            payload: parsedMessage.payload
          });
          
          if (parsedMessage.correlationId) {
            this.emit('uxInsightsResponse', {
              correlationId: parsedMessage.correlationId,
              payload: parsedMessage.payload
            });
          }
          break;

        case this.topics.webResearchCompleted:
          if (parsedMessage.correlationId) {
            this.emit('webResearchResponse', {
              correlationId: parsedMessage.correlationId,
              payload: parsedMessage.payload
            });
          }
          break;

        default:
          this.logger.warn(`Topic non géré: ${topic}`);
      }

    } catch (error) {
      this.logger.error('Erreur lors du parsing du message:', error);
    }
  }

  /**
   * Détermine le topic pour un type de réponse
   */
  private getTopicForResponseType(responseType: string): string {
    switch (responseType) {
      case 'strategy':
        return this.topics.marketingStrategy;
      case 'campaign':
        return this.topics.campaignLaunched;
      case 'optimization':
        return this.topics.conversionOptimized;
      case 'analysis':
        return this.topics.analyticsGenerated;
      case 'content':
        return this.topics.socialMediaManaged;
      default:
        return 'marketing.response.general';
    }
  }

  /**
   * Déconnexion
   */
  async disconnect(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.consumer.disconnect();
        await this.producer.disconnect();
        this.isConnected = false;
        this.logger.info('Communication marketing déconnectée');
      }
    } catch (error) {
      this.logger.error('Erreur lors de la déconnexion:', error);
      throw error;
    }
  }
}
