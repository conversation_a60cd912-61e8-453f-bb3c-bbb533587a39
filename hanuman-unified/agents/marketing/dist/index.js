"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.marketingAgent = exports.app = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const winston_1 = require("winston");
const dotenv_1 = __importDefault(require("dotenv"));
const MarketingAgent_1 = require("./core/MarketingAgent");
const MarketingMemory_1 = require("./memory/MarketingMemory");
const MarketingCommunication_1 = require("./communication/MarketingCommunication");
// Configuration de l'environnement
dotenv_1.default.config();
// Configuration du logger
const logger = (0, winston_1.createLogger)({
    level: process.env.LOG_LEVEL || 'info',
    format: winston_1.format.combine(winston_1.format.timestamp(), winston_1.format.errors({ stack: true }), winston_1.format.json()),
    defaultMeta: { service: 'agent-marketing' },
    transports: [
        new winston_1.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston_1.transports.File({ filename: 'logs/combined.log' }),
        new winston_1.transports.Console({
            format: winston_1.format.combine(winston_1.format.colorize(), winston_1.format.simple())
        })
    ]
});
exports.logger = logger;
// Configuration de l'agent
const agentConfig = {
    id: 'agent-marketing-001',
    name: 'Agent Marketing',
    type: 'marketing',
    version: '1.0.0',
    capabilities: [
        'strategy_generation',
        'campaign_management',
        'conversion_optimization',
        'ab_testing',
        'social_media_management',
        'analytics_generation'
    ],
    endpoints: {
        health: '/health',
        strategy: '/api/strategy',
        campaign: '/api/campaign',
        analytics: '/api/analytics',
        optimization: '/api/optimization',
        social: '/api/social'
    },
    memory: {
        store: 'weaviate',
        collections: [
            'MarketingStrategy',
            'MarketingCampaign',
            'ABTest',
            'MarketingAnalytics',
            'MarketingResponse',
            'SocialMediaPost'
        ]
    },
    communication: {
        kafka: {
            topics: [
                'marketing.strategy.created',
                'marketing.campaign.launched',
                'marketing.conversion.optimized',
                'marketing.analytics.generated',
                'marketing.social.managed'
            ]
        }
    }
};
// Initialisation de l'application
const app = (0, express_1.default)();
exports.app = app;
const port = process.env.PORT || 3000;
// Middlewares de sécurité et performance
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
// Middleware de logging
app.use((req, res, next) => {
    logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });
    next();
});
// Variables globales
let marketingAgent;
let memory;
let communication;
/**
 * Initialise l'agent marketing
 */
async function initializeAgent() {
    try {
        logger.info('Initialisation de l\'Agent Marketing...');
        // Initialisation de la mémoire
        memory = new MarketingMemory_1.MarketingMemory(logger);
        await memory.initialize();
        // Initialisation de la communication
        communication = new MarketingCommunication_1.MarketingCommunication(logger);
        await communication.initialize();
        // Initialisation de l'agent principal
        exports.marketingAgent = marketingAgent = new MarketingAgent_1.MarketingAgent(agentConfig, logger, memory, communication);
        await marketingAgent.initialize();
        logger.info('Agent Marketing initialisé avec succès');
    }
    catch (error) {
        logger.error('Erreur lors de l\'initialisation de l\'agent:', error);
        process.exit(1);
    }
}
// Routes de santé
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        agent: {
            id: agentConfig.id,
            name: agentConfig.name,
            version: agentConfig.version
        },
        uptime: process.uptime()
    });
});
app.get('/status', (req, res) => {
    res.json({
        agent: agentConfig,
        memory: {
            connected: memory ? true : false,
            collections: agentConfig.memory.collections
        },
        communication: {
            connected: communication ? true : false,
            topics: agentConfig.communication.kafka.topics
        }
    });
});
// Routes API principales
/**
 * Création de stratégie marketing
 */
app.post('/api/strategy', async (req, res) => {
    try {
        const request = {
            id: `req-${Date.now()}`,
            type: 'strategy',
            description: 'Création de stratégie marketing',
            requirements: req.body,
            priority: 'medium',
            requestedBy: req.headers['x-user-id'] || 'anonymous',
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const response = await marketingAgent.processRequest(request);
        res.json({
            success: true,
            data: response,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger.error('Erreur lors de la création de stratégie:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});
/**
 * Gestion de campagnes
 */
app.post('/api/campaign', async (req, res) => {
    try {
        const request = {
            id: `req-${Date.now()}`,
            type: 'campaign',
            description: 'Création/gestion de campagne',
            requirements: req.body,
            priority: 'high',
            requestedBy: req.headers['x-user-id'] || 'anonymous',
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const response = await marketingAgent.processRequest(request);
        res.json({
            success: true,
            data: response,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger.error('Erreur lors de la gestion de campagne:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});
/**
 * Optimisation de conversion
 */
app.post('/api/optimization', async (req, res) => {
    try {
        const request = {
            id: `req-${Date.now()}`,
            type: 'optimization',
            description: 'Optimisation de conversion',
            requirements: req.body,
            priority: 'high',
            requestedBy: req.headers['x-user-id'] || 'anonymous',
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const response = await marketingAgent.processRequest(request);
        res.json({
            success: true,
            data: response,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger.error('Erreur lors de l\'optimisation:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});
/**
 * Génération d'analyses
 */
app.post('/api/analytics', async (req, res) => {
    try {
        const request = {
            id: `req-${Date.now()}`,
            type: 'analysis',
            description: 'Génération d\'analyses marketing',
            requirements: req.body,
            priority: 'medium',
            requestedBy: req.headers['x-user-id'] || 'anonymous',
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const response = await marketingAgent.processRequest(request);
        res.json({
            success: true,
            data: response,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger.error('Erreur lors de la génération d\'analyses:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});
/**
 * Gestion des réseaux sociaux
 */
app.post('/api/social', async (req, res) => {
    try {
        const request = {
            id: `req-${Date.now()}`,
            type: 'content',
            description: 'Gestion des réseaux sociaux',
            requirements: req.body,
            priority: 'medium',
            requestedBy: req.headers['x-user-id'] || 'anonymous',
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const response = await marketingAgent.processRequest(request);
        res.json({
            success: true,
            data: response,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger.error('Erreur lors de la gestion des réseaux sociaux:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});
// Middleware de gestion d'erreurs
app.use((error, req, res, next) => {
    logger.error('Erreur non gérée:', error);
    res.status(500).json({
        success: false,
        error: 'Erreur interne du serveur',
        timestamp: new Date().toISOString()
    });
});
// Gestion de l'arrêt gracieux
process.on('SIGTERM', async () => {
    logger.info('Signal SIGTERM reçu, arrêt gracieux...');
    if (marketingAgent) {
        await marketingAgent.shutdown();
    }
    process.exit(0);
});
process.on('SIGINT', async () => {
    logger.info('Signal SIGINT reçu, arrêt gracieux...');
    if (marketingAgent) {
        await marketingAgent.shutdown();
    }
    process.exit(0);
});
// Démarrage du serveur
async function startServer() {
    try {
        await initializeAgent();
        app.listen(port, () => {
            logger.info(`Agent Marketing démarré sur le port ${port}`);
            logger.info(`Health check: http://localhost:${port}/health`);
            logger.info(`Status: http://localhost:${port}/status`);
        });
    }
    catch (error) {
        logger.error('Erreur lors du démarrage du serveur:', error);
        process.exit(1);
    }
}
// Démarrage de l'application
if (require.main === module) {
    startServer();
}
//# sourceMappingURL=index.js.map