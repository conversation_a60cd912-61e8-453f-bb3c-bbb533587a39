import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { SocialMediaPost } from '../types';
import { MarketingMemory } from '../memory/MarketingMemory';
/**
 * Gestionnaire de réseaux sociaux
 * Responsable de la planification et gestion des posts sur les réseaux sociaux
 */
export declare class SocialMediaManager extends EventEmitter {
    private logger;
    private memory;
    private isInitialized;
    private readonly optimalTimes;
    constructor(logger: Logger, memory: MarketingMemory);
    initialize(): Promise<void>;
    schedulePosts(contentRequest: any): Promise<SocialMediaPost[]>;
    optimizeForEngagement(posts: SocialMediaPost[]): Promise<SocialMediaPost[]>;
    private createPostForPlatform;
    private adaptContentForPlatform;
    private generateHashtags;
    private getOptimalPostTime;
    private optimizeHashtags;
    private optimizeContentForPlatform;
    private initializeEngagement;
}
//# sourceMappingURL=SocialMediaManager.d.ts.map