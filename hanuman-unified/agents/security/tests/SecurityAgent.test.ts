import { SecurityAgent } from '../src/core/SecurityAgent';
import { SecurityConfig } from '../src/types';
import { Logger } from 'winston';
import { WeaviateMemory } from '../src/memory/WeaviateMemory';
import { KafkaCommunication } from '../src/communication/KafkaCommunication';

describe('SecurityAgent', () => {
  let securityAgent: SecurityAgent;
  let mockLogger: jest.Mocked<Logger>;
  let mockMemory: jest.Mocked<WeaviateMemory>;
  let mockCommunication: jest.Mocked<KafkaCommunication>;
  let config: SecurityConfig;

  beforeEach(() => {
    // Configuration de test
    config = {
      agent: {
        id: 'test-security-agent',
        name: 'Test Security Agent',
        version: '1.0.0'
      },
      scanning: {
        enabled: true,
        interval: 3600,
        targets: ['http://localhost:3000'],
        depth: 'medium',
        concurrent: 5
      },
      compliance: {
        frameworks: [
          {
            name: 'OWASP Top 10',
            version: '2021',
            enabled: true,
            controls: []
          }
        ],
        customPolicies: []
      },
      monitoring: {
        enabled: true,
        interval: 60,
        alertThresholds: {
          criticalVulnerabilities: 1,
          highVulnerabilities: 5,
          failedLogins: 10
        }
      },
      reporting: {
        enabled: true,
        schedule: 'daily',
        recipients: ['<EMAIL>'],
        formats: ['json', 'pdf']
      }
    };

    // Mocks
    mockLogger = {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn()
    } as any;

    mockMemory = {
      initialize: jest.fn(),
      storeVulnerability: jest.fn(),
      getVulnerabilities: jest.fn(),
      storeComplianceResults: jest.fn(),
      storeAuditLog: jest.fn(),
      getAuditLogs: jest.fn(),
      storeEncryptionKey: jest.fn(),
      getEncryptionKeys: jest.fn(),
      storeSecurityPolicy: jest.fn(),
      getSecurityPolicies: jest.fn(),
      shutdown: jest.fn()
    } as any;

    mockCommunication = {
      initialize: jest.fn(),
      sendMessage: jest.fn(),
      subscribe: jest.fn(),
      shutdown: jest.fn()
    } as any;

    securityAgent = new SecurityAgent(config, mockLogger, mockMemory, mockCommunication);
  });

  afterEach(async () => {
    if (securityAgent) {
      await securityAgent.shutdown();
    }
  });

  describe('Initialisation', () => {
    it('devrait initialiser correctement tous les composants', async () => {
      await securityAgent.initialize();

      expect(mockLogger.info).toHaveBeenCalledWith('🔐 Initialisation de l\'Agent Security...');
      expect(mockLogger.info).toHaveBeenCalledWith('✅ Agent Security initialisé et opérationnel');
    });

    it('devrait gérer les erreurs d\'initialisation', async () => {
      mockMemory.initialize.mockRejectedValue(new Error('Erreur de mémoire'));

      await expect(securityAgent.initialize()).rejects.toThrow('Erreur de mémoire');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('Scan de vulnérabilités', () => {
    beforeEach(async () => {
      await securityAgent.initialize();
    });

    it('devrait effectuer un scan de vulnérabilités', async () => {
      const target = 'http://localhost:3000';
      const scanOptions = { depth: 'medium', timeout: 30000 };

      const result = await securityAgent.scanVulnerabilities(target, scanOptions);

      expect(result).toBeDefined();
      expect(result.target).toBe(target);
      expect(result.vulnerabilities).toBeDefined();
    });

    it('devrait gérer les erreurs de scan', async () => {
      const invalidTarget = 'invalid-url';

      await expect(
        securityAgent.scanVulnerabilities(invalidTarget)
      ).rejects.toThrow();
    });
  });

  describe('Contrôle d\'accès', () => {
    beforeEach(async () => {
      await securityAgent.initialize();
    });

    it('devrait créer une session utilisateur', async () => {
      const userId = 'test-user';
      const userAttributes = {
        roles: ['user'],
        department: 'IT',
        ipAddress: '*************',
        userAgent: 'Test Agent'
      };
      const authMethod = 'password';

      const session = await securityAgent.createUserSession(userId, userAttributes, authMethod);

      expect(session).toBeDefined();
      expect(session.userId).toBe(userId);
      expect(session.isActive).toBe(true);
    });

    it('devrait vérifier l\'autorisation d\'accès', async () => {
      const userId = 'test-user';
      const resource = '/api/users';
      const action = 'read';

      // Créer d'abord une session
      await securityAgent.createUserSession(userId, {
        roles: ['user'],
        ipAddress: '*************',
        userAgent: 'Test Agent'
      }, 'password');

      const decision = await securityAgent.checkAccess(userId, resource, action);

      expect(decision).toBeDefined();
      expect(decision.allowed).toBeDefined();
      expect(decision.reason).toBeDefined();
    });

    it('devrait révoquer une session utilisateur', async () => {
      const userId = 'test-user';

      // Créer d'abord une session
      await securityAgent.createUserSession(userId, {
        roles: ['user'],
        ipAddress: '*************',
        userAgent: 'Test Agent'
      }, 'password');

      await securityAgent.revokeUserSession(userId, 'Test revocation');

      // Vérifier que l'accès est maintenant refusé
      const decision = await securityAgent.checkAccess(userId, '/api/test', 'read');
      expect(decision.allowed).toBe(false);
    });
  });

  describe('Chiffrement', () => {
    beforeEach(async () => {
      await securityAgent.initialize();
    });

    it('devrait chiffrer et déchiffrer des données', async () => {
      const originalData = 'Données sensibles à chiffrer';

      // Chiffrement
      const encryptionResult = await securityAgent.encryptData(originalData);
      expect(encryptionResult).toBeDefined();
      expect(encryptionResult.encryptedData).toBeDefined();
      expect(encryptionResult.keyId).toBeDefined();

      // Déchiffrement
      const decryptedData = await securityAgent.decryptData(
        encryptionResult.encryptedData,
        encryptionResult.keyId,
        encryptionResult.algorithm,
        encryptionResult.iv
      );

      expect(decryptedData).toBe(originalData);
    });

    it('devrait générer une nouvelle clé de chiffrement', async () => {
      const keyId = 'test-key';
      const algorithm = 'aes-256-gcm';

      const key = await securityAgent.generateEncryptionKey(keyId, algorithm);

      expect(key).toBeDefined();
      expect(key.id).toBe(keyId);
      expect(key.algorithm).toBe(algorithm);
      expect(key.isActive).toBe(true);
    });

    it('devrait effectuer la rotation d\'une clé', async () => {
      const keyId = 'test-key';

      // Générer d'abord une clé
      await securityAgent.generateEncryptionKey(keyId);

      // Effectuer la rotation
      const newKey = await securityAgent.rotateEncryptionKey(keyId);

      expect(newKey).toBeDefined();
      expect(newKey.id).toBe(keyId);
      expect(newKey.version).toBeGreaterThan(1);
    });
  });

  describe('Politiques de sécurité', () => {
    beforeEach(async () => {
      await securityAgent.initialize();
    });

    it('devrait créer une nouvelle politique de sécurité', async () => {
      const policyData = {
        name: 'Test Policy',
        description: 'Politique de test',
        category: 'access-control',
        severity: 'medium' as const,
        rules: [
          {
            id: 'test-rule',
            name: 'Test Rule',
            type: 'access-control' as const,
            conditions: { requireAuthentication: true },
            remediation: 'Authentifiez-vous'
          }
        ],
        enforcement: { action: 'warn' as const, immediate: false },
        scope: { actions: ['read'], resources: ['/api/test'] }
      };

      const policy = await securityAgent.createSecurityPolicy(policyData);

      expect(policy).toBeDefined();
      expect(policy.name).toBe(policyData.name);
      expect(policy.enabled).toBe(true);
    });

    it('devrait évaluer une action contre les politiques', async () => {
      // Créer d'abord une politique
      await securityAgent.createSecurityPolicy({
        name: 'Test Policy',
        description: 'Politique de test',
        category: 'access-control',
        severity: 'high' as const,
        rules: [
          {
            id: 'auth-required',
            name: 'Authentication Required',
            type: 'access-control' as const,
            conditions: { requireAuthentication: true },
            remediation: 'Authentifiez-vous'
          }
        ],
        enforcement: { action: 'block' as const, immediate: true },
        scope: { actions: ['write'], resources: ['/api/sensitive'] }
      });

      const action = {
        type: 'write',
        resource: '/api/sensitive',
        userId: 'test-user',
        timestamp: new Date(),
        metadata: { authenticated: false }
      };

      const result = await securityAgent.evaluateAction(action);

      expect(result).toBeDefined();
      expect(result.decision).toBeDefined();
      expect(result.violations).toBeDefined();
    });
  });

  describe('Audit', () => {
    beforeEach(async () => {
      await securityAgent.initialize();
    });

    it('devrait enregistrer un événement d\'audit', async () => {
      const auditEvent = {
        type: 'access',
        category: 'authentication',
        description: 'Tentative de connexion',
        userId: 'test-user',
        resource: '/login',
        action: 'authenticate',
        result: 'success',
        ipAddress: '*************'
      };

      await securityAgent.logAuditEvent(auditEvent);

      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('Événement d\'audit enregistré')
      );
    });

    it('devrait générer un rapport d\'audit', async () => {
      const reportType = 'daily';
      const period = {
        start: new Date(Date.now() - 24 * 60 * 60 * 1000),
        end: new Date()
      };

      const report = await securityAgent.generateAuditReport(reportType, period);

      expect(report).toBeDefined();
      expect(report.type).toBe(reportType);
      expect(report.period).toEqual(period);
      expect(report.summary).toBeDefined();
    });
  });

  describe('Métriques et statut', () => {
    beforeEach(async () => {
      await securityAgent.initialize();
    });

    it('devrait retourner le statut de l\'agent', async () => {
      const status = await securityAgent.getStatus();

      expect(status).toBeDefined();
      expect(status.isInitialized).toBe(true);
      expect(status.isRunning).toBe(true);
      expect(status.components).toBeDefined();
    });

    it('devrait retourner les métriques de sécurité', async () => {
      const metrics = await securityAgent.getSecurityMetrics();

      expect(metrics).toBeDefined();
    });
  });

  describe('Arrêt', () => {
    it('devrait arrêter correctement tous les composants', async () => {
      await securityAgent.initialize();
      await securityAgent.shutdown();

      expect(mockLogger.info).toHaveBeenCalledWith('🛑 Arrêt de l\'Agent Security...');
      expect(mockLogger.info).toHaveBeenCalledWith('✅ Agent Security arrêté');
    });
  });
});
