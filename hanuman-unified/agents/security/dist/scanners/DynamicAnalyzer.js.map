{"version": 3, "file": "DynamicAnalyzer.js", "sourceRoot": "", "sources": ["../../src/scanners/DynamicAnalyzer.ts"], "names": [], "mappings": ";;;AASA;;;;GAIG;AACH,MAAa,eAAe;IAK1B,YAAY,MAAc,EAAE,MAAsB;QAF1C,kBAAa,GAAY,KAAK,CAAC;QAGrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAEnE,iCAAiC;YACjC,iDAAiD;YAEjD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAEvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+DAA+D,EAAE,KAAK,CAAC,CAAC;YAC1F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,OAA4B;QACxC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,OAAO,CAAC,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC;QAE1E,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAC5D,eAAe,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;YAElC,mBAAmB;YACnB,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBAClC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;gBAC5D,eAAe,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;YACpC,CAAC;YAED,gCAAgC;YAChC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,CAAC;YACxE,eAAe,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;YAEnC,oCAAoC;YACpC,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,eAAe,CAAC,MAAM,0BAA0B,CAAC,CAAC;YAEpG,OAAO,eAAe,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,OAA4B;QAC/D,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,8BAA8B;QAC9B,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC5B,KAAK,EAAE,sCAAsC;gBAC7C,WAAW,EAAE,6EAA6E;gBAC1F,QAAQ,EAAE,MAA+B;gBACzC,QAAQ,EAAE,sBAA0C;gBACpD,GAAG,EAAE,QAAQ;gBACb,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAC5B,SAAS,EAAE,QAAQ;oBACnB,MAAM,EAAE,KAAK;iBACd;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,yCAAyC;oBAClD,QAAQ,EAAE,mCAAmC;oBAC7C,OAAO,EAAE,2BAA2B;iBACrC;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,iFAAiF;oBAC9F,MAAM,EAAE,QAAQ;oBAChB,QAAQ,EAAE,MAAM;iBACjB;gBACD,UAAU,EAAE;oBACV,8CAA8C;oBAC9C,gDAAgD;iBACjD;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,OAA4B;QAC/D,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,kDAAkD;QAClD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,sBAAsB,IAAI,CAAC,GAAG,EAAE,EAAE;gBACtC,KAAK,EAAE,yBAAyB;gBAChC,WAAW,EAAE,8CAA8C;gBAC3D,QAAQ,EAAE,QAAiC;gBAC3C,QAAQ,EAAE,eAAmC;gBAC7C,GAAG,EAAE,SAAS;gBACd,KAAK,EAAE,WAAW;gBAClB,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAC5B,QAAQ,EAAE,YAAY;oBACtB,MAAM,EAAE,KAAK;iBACd;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,gBAAgB;oBACzB,QAAQ,EAAE,2DAA2D;oBACrE,aAAa,EAAE,CAAC,eAAe,EAAE,aAAa,EAAE,aAAa,CAAC;iBAC/D;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,wEAAwE;oBACrF,MAAM,EAAE,QAAQ;oBAChB,QAAQ,EAAE,QAAQ;iBACnB;gBACD,UAAU,EAAE;oBACV,kGAAkG;iBACnG;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iCAAiC,CAAC,OAA4B;QAC1E,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,0DAA0D;QAC1D,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;YACzB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,kBAAkB,IAAI,CAAC,GAAG,EAAE,EAAE;gBAClC,KAAK,EAAE,+BAA+B;gBACtC,WAAW,EAAE,0DAA0D;gBACvE,QAAQ,EAAE,MAA+B;gBACzC,QAAQ,EAAE,gBAAoC;gBAC9C,GAAG,EAAE,SAAS;gBACd,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAC5B,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,MAAM;iBACf;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,aAAa;oBACtB,QAAQ,EAAE,wCAAwC;oBAClD,MAAM,EAAE,CAAC,oBAAoB,EAAE,wBAAwB,EAAE,QAAQ,CAAC;iBACnE;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,yEAAyE;oBACtF,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,MAAM;iBACjB;gBACD,UAAU,EAAE;oBACV,8EAA8E;iBAC/E;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,eAAgC;QACzE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG;gBACd,MAAM;gBACN,IAAI,EAAE,kBAAkB;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,eAAe,EAAE,eAAe,CAAC,MAAM;gBACvC,QAAQ,EAAE,eAAe;aAC1B,CAAC;YAEF,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,MAAM,EAAE,CAAC,CAAC;QAEnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;CACF;AAhOD,0CAgOC"}