import { Logger } from 'winston';
import { SecurityScanRequest, Vulnerability } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Scanner R<PERSON>eau
 *
 * Analyse les vulnérabilités réseau, ports ouverts et services exposés
 */
export declare class NetworkScanner {
    private logger;
    private memory;
    private isInitialized;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le scanner réseau
     */
    initialize(): Promise<void>;
    /**
     * Effectue un scan réseau
     */
    scan(request: SecurityScanRequest): Promise<Vulnerability[]>;
    /**
     * Scanne les ports ouverts
     */
    private scanOpenPorts;
    /**
     * Scanne les services exposés
     */
    private scanExposedServices;
    /**
     * Scanne les protocoles non sécurisés
     */
    private scanInsecureProtocols;
    /**
     * Détermine la sévérité d'un port ouvert
     */
    private getPortSeverity;
    /**
     * Obtient le nom du service pour un port
     */
    private getPortService;
    /**
     * Détermine l'effort de remédiation pour un port
     */
    private getPortRemediationEffort;
    /**
     * Stocke les résultats en mémoire
     */
    private storeResults;
    /**
     * Arrêt du scanner réseau
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=NetworkScanner.d.ts.map