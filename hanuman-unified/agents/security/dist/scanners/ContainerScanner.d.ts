import { Logger } from 'winston';
import { SecurityScanRequest, Vulnerability } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Scanner de Conteneurs
 *
 * Analyse les vulnérabilités dans les conteneurs Docker et les images
 */
export declare class ContainerScanner {
    private logger;
    private memory;
    private isInitialized;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le scanner de conteneurs
     */
    initialize(): Promise<void>;
    /**
     * Effectue un scan de conteneur
     */
    scan(request: SecurityScanRequest): Promise<Vulnerability[]>;
    /**
     * Scanne les vulnérabilités de l'image
     */
    private scanImageVulnerabilities;
    /**
     * Scanne la configuration du conteneur
     */
    private scanContainerConfiguration;
    /**
     * Scanne les secrets exposés
     */
    private scanExposedSecrets;
    /**
     * Stocke les résultats en mémoire
     */
    private storeResults;
    /**
     * Arrêt du scanner de conteneurs
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=ContainerScanner.d.ts.map