{"version": 3, "file": "APIScanner.js", "sourceRoot": "", "sources": ["../../src/scanners/APIScanner.ts"], "names": [], "mappings": ";;;AASA;;;;GAIG;AACH,MAAa,UAAU;IAKrB,YAAY,MAAc,EAAE,MAAsB;QAF1C,kBAAa,GAAY,KAAK,CAAC;QAGrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAExD,wCAAwC;YACxC,qDAAqD;YAErD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAE/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,OAA4B;QACrC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC;QAEjE,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,IAAI,CAAC;YACH,2CAA2C;YAC3C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvD,eAAe,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;YAElC,iCAAiC;YACjC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAC5D,eAAe,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;YAEnC,yBAAyB;YACzB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC5D,eAAe,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;YAEpC,yBAAyB;YACzB,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,eAAe,CAAC,MAAM,0BAA0B,CAAC,CAAC;YAE1F,OAAO,eAAe,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,OAA4B;QAC1D,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,gDAAgD;QAChD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,iBAAiB,IAAI,CAAC,GAAG,EAAE,EAAE;gBACjC,KAAK,EAAE,mCAAmC;gBAC1C,WAAW,EAAE,2EAA2E;gBACxF,QAAQ,EAAE,MAA+B;gBACzC,QAAQ,EAAE,uBAA2C;gBACrD,GAAG,EAAE,SAAS;gBACd,KAAK,EAAE,WAAW;gBAClB,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAC5B,QAAQ,EAAE,iBAAiB;oBAC3B,MAAM,EAAE,KAAK;iBACd;gBACD,QAAQ,EAAE;oBACR,QAAQ,EAAE,gBAAgB;oBAC1B,UAAU,EAAE,SAAS;oBACrB,kBAAkB,EAAE,IAAI;oBACxB,QAAQ,EAAE,oCAAoC;iBAC/C;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,2EAA2E;oBACxF,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,MAAM;iBACjB;gBACD,UAAU,EAAE;oBACV,yFAAyF;iBAC1F;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,OAA4B;QAC9D,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,iBAAiB,IAAI,CAAC,GAAG,EAAE,EAAE;gBACjC,KAAK,EAAE,yBAAyB;gBAChC,WAAW,EAAE,wDAAwD;gBACrE,QAAQ,EAAE,MAA+B;gBACzC,QAAQ,EAAE,gBAAoC;gBAC9C,GAAG,EAAE,SAAS;gBACd,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAC5B,QAAQ,EAAE,WAAW;oBACrB,MAAM,EAAE,MAAM;iBACf;gBACD,QAAQ,EAAE;oBACR,UAAU,EAAE,YAAY;oBACxB,WAAW,EAAE,OAAO;oBACpB,YAAY,EAAE,KAAK;oBACnB,oBAAoB,EAAE,KAAK;iBAC5B;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,kFAAkF;oBAC/F,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,MAAM;iBACjB;gBACD,UAAU,EAAE;oBACV,6EAA6E;iBAC9E;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,OAA4B;QAC7D,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,wCAAwC;QACxC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;YACzB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,kBAAkB,IAAI,CAAC,GAAG,EAAE,EAAE;gBAClC,KAAK,EAAE,gCAAgC;gBACvC,WAAW,EAAE,sDAAsD;gBACnE,QAAQ,EAAE,MAA+B;gBACzC,QAAQ,EAAE,eAAmC;gBAC7C,GAAG,EAAE,SAAS;gBACd,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAC5B,QAAQ,EAAE,cAAc;oBACxB,MAAM,EAAE,KAAK;iBACd;gBACD,QAAQ,EAAE;oBACR,QAAQ,EAAE,kBAAkB;oBAC5B,YAAY,EAAE,OAAO;oBACrB,WAAW,EAAE,MAAM;oBACnB,YAAY,EAAE,IAAI;iBACnB;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,yEAAyE;oBACtF,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,MAAM;iBACjB;gBACD,UAAU,EAAE;oBACV,6CAA6C;iBAC9C;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,eAAgC;QACzE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG;gBACd,MAAM;gBACN,IAAI,EAAE,UAAU;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,eAAe,EAAE,eAAe,CAAC,MAAM;gBACvC,QAAQ,EAAE,eAAe;aAC1B,CAAC;YAEF,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,MAAM,EAAE,CAAC,CAAC;QAEnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;CACF;AA9ND,gCA8NC"}