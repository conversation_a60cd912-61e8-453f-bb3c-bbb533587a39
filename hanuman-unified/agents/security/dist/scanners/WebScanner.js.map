{"version": 3, "file": "WebScanner.js", "sourceRoot": "", "sources": ["../../src/scanners/WebScanner.ts"], "names": [], "mappings": ";;;AASA;;;;GAIG;AACH,MAAa,UAAU;IAKrB,YAAY,MAAc,EAAE,MAAsB;QAF1C,kBAAa,GAAY,KAAK,CAAC;QAGrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAExD,wCAAwC;YACxC,gDAAgD;YAEhD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAE/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,OAA4B;QACrC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC;QAEjE,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACtD,eAAe,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;YAEpC,gCAAgC;YAChC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAC5D,eAAe,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;YAErC,mBAAmB;YACnB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC3D,eAAe,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;YAErC,yBAAyB;YACzB,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,eAAe,CAAC,MAAM,0BAA0B,CAAC,CAAC;YAE1F,OAAO,eAAe,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,OAA4B;QACvD,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,8BAA8B;QAC9B,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC5B,KAAK,EAAE,4BAA4B;gBACnC,WAAW,EAAE,sCAAsC;gBACnD,QAAQ,EAAE,MAA+B;gBACzC,QAAQ,EAAE,sBAA0C;gBACpD,GAAG,EAAE,QAAQ;gBACb,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAC5B,SAAS,EAAE,QAAQ;oBACnB,MAAM,EAAE,KAAK;iBACd;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,+BAA+B;oBACxC,QAAQ,EAAE,8BAA8B;oBACxC,OAAO,EAAE,cAAc;iBACxB;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,qEAAqE;oBAClF,MAAM,EAAE,QAAQ;oBAChB,QAAQ,EAAE,MAAM;iBACjB;gBACD,UAAU,EAAE;oBACV,8CAA8C;iBAC/C;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,+BAA+B;QAC/B,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC7B,KAAK,EAAE,mCAAmC;gBAC1C,WAAW,EAAE,iDAAiD;gBAC9D,QAAQ,EAAE,QAAiC;gBAC3C,QAAQ,EAAE,uBAA2C;gBACrD,GAAG,EAAE,SAAS;gBACd,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAE5B,MAAM,EAAE,MAAM;iBACf;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,KAAK;oBAChB,iBAAiB,EAAE,KAAK;oBACxB,YAAY,EAAE,KAAK;iBACpB;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,8DAA8D;oBAC3E,MAAM,EAAE,QAAQ;oBAChB,QAAQ,EAAE,QAAQ;iBACnB;gBACD,UAAU,EAAE;oBACV,8CAA8C;iBAC/C;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,OAA4B;QAC5D,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,+CAA+C;QAC/C,MAAM,cAAc,GAAG,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,2BAA2B,CAAC,CAAC;QACtH,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QAEjE,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,eAAe,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACvD,KAAK,EAAE,4BAA4B,MAAM,EAAE;gBAC3C,WAAW,EAAE,yBAAyB,MAAM,eAAe;gBAC3D,QAAQ,EAAE,KAA8B;gBACxC,QAAQ,EAAE,2BAA+C;gBACzD,GAAG,EAAE,SAAS;gBACd,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAC5B,MAAM,EAAE,MAAM;iBACf;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE,KAAK;oBACd,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;iBACrD;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,qBAAqB,MAAM,6BAA6B;oBACrE,MAAM,EAAE,KAAK;oBACb,QAAQ,EAAE,KAAK;iBAChB;gBACD,UAAU,EAAE;oBACV,+CAA+C;iBAChD;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,OAA4B;QAC3D,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,mDAAmD;QACnD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC;gBACnB,EAAE,EAAE,eAAe,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC/B,KAAK,EAAE,+BAA+B;gBACtC,WAAW,EAAE,sDAAsD;gBACnE,QAAQ,EAAE,QAAiC;gBAC3C,QAAQ,EAAE,2BAA+C;gBACzD,GAAG,EAAE,SAAS;gBACd,QAAQ,EAAE;oBACR,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;oBAC5B,MAAM,EAAE,WAAW;iBACpB;gBACD,QAAQ,EAAE;oBACR,UAAU,EAAE,WAAW;oBACvB,MAAM,EAAE,KAAK;oBACb,QAAQ,EAAE,KAAK;oBACf,QAAQ,EAAE,MAAM;iBACjB;gBACD,WAAW,EAAE;oBACX,WAAW,EAAE,wEAAwE;oBACrF,MAAM,EAAE,KAAK;oBACb,QAAQ,EAAE,QAAQ;iBACnB;gBACD,UAAU,EAAE;oBACV,gEAAgE;iBACjE;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,MAAc;QAC5C,MAAM,eAAe,GAA2B;YAC9C,iBAAiB,EAAE,oBAAoB;YACvC,wBAAwB,EAAE,SAAS;YACnC,kBAAkB,EAAE,eAAe;YACnC,2BAA2B,EAAE,qCAAqC;SACnE,CAAC;QAEF,OAAO,eAAe,CAAC,MAAM,CAAC,IAAI,kCAAkC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,eAAgC;QACzE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG;gBACd,MAAM;gBACN,IAAI,EAAE,UAAU;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,eAAe,EAAE,eAAe,CAAC,MAAM;gBACvC,QAAQ,EAAE,eAAe;aAC1B,CAAC;YAEF,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,MAAM,EAAE,CAAC,CAAC;QAEnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;CACF;AA7QD,gCA6QC"}