import { Logger } from 'winston';
import { SecurityScanRequest, Vulnerability } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Scanner d'Infrastructure
 *
 * Analyse les vulnérabilités d'infrastructure cloud et on-premise
 */
export declare class InfrastructureScanner {
    private logger;
    private memory;
    private isInitialized;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le scanner d'infrastructure
     */
    initialize(): Promise<void>;
    /**
     * Effectue un scan d'infrastructure
     */
    scan(request: SecurityScanRequest): Promise<Vulnerability[]>;
    /**
     * Scanne les configurations cloud
     */
    private scanCloudConfiguration;
    /**
     * Scanne les permissions IAM
     */
    private scanIAMPermissions;
    /**
     * Scanne les configurations réseau
     */
    private scanNetworkConfiguration;
    /**
     * Stocke les résultats en mémoire
     */
    private storeResults;
    /**
     * Arrêt du scanner d'infrastructure
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=InfrastructureScanner.d.ts.map