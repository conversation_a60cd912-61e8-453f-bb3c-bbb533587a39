import { Logger } from 'winston';
import { EventEmitter } from 'events';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { ThreatIndicator, ThreatReport } from '../types';
/**
 * Intelligence des Menaces
 *
 * Détecte, analyse et corrèle les menaces de sécurité
 */
export declare class ThreatIntelligence extends EventEmitter {
    private logger;
    private memory;
    private isInitialized;
    private threatFeeds;
    private indicators;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise l'intelligence des menaces
     */
    initialize(): Promise<void>;
    /**
     * Initialise les feeds de menaces
     */
    private initializeThreatFeeds;
    /**
     * Charge les indicateurs de menaces
     */
    private loadThreatIndicators;
    /**
     * Génère des indicateurs d'exemple
     */
    private generateSampleIndicators;
    /**
     * Analyse une menace
     */
    analyzeThreat(data: any): Promise<ThreatReport>;
    /**
     * Corrèle les données avec les indicateurs existants
     */
    private correlateWithIndicators;
    /**
     * Vérifie si les données correspondent à un indicateur
     */
    private matchesIndicator;
    /**
     * Évalue le niveau de menace
     */
    private assessThreatLevel;
    /**
     * Catégorise la menace
     */
    private categorizeThreat;
    /**
     * Génère une description de la menace
     */
    private generateThreatDescription;
    /**
     * Génère des recommandations
     */
    private generateRecommendations;
    /**
     * Calcule la confiance de l'analyse
     */
    private calculateConfidence;
    /**
     * Calcule le score de risque
     */
    private calculateRiskScore;
    /**
     * Stocke le rapport de menace
     */
    private storeThreatReport;
    /**
     * Ajoute un nouvel indicateur de menace
     */
    addThreatIndicator(indicator: ThreatIndicator): Promise<void>;
    /**
     * Obtient les statistiques des menaces
     */
    getThreatStatistics(): any;
    /**
     * Trouve des indicateurs correspondant à une vulnérabilité
     */
    findIndicators(vulnerability: any): Promise<ThreatIndicator[]>;
    /**
     * Vérifie si un indicateur correspond à une vulnérabilité
     */
    private matchesVulnerability;
    /**
     * Analyse une alerte de sécurité
     */
    analyzeAlert(alert: any): Promise<any>;
    /**
     * Arrêt de l'intelligence des menaces
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=ThreatIntelligence.d.ts.map