import { Logger } from 'winston';
import { SecurityReport, SecurityMetrics } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Générateur de Rapports de Sécurité
 *
 * Génère des rapports détaillés sur l'état de la sécurité
 */
export declare class ReportGenerator {
    private logger;
    private memory;
    private isInitialized;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le générateur de rapports
     */
    initialize(): Promise<void>;
    /**
     * Génère un rapport de vulnérabilités
     */
    generateVulnerabilityReport(period: {
        start: Date;
        end: Date;
    }): Promise<SecurityReport>;
    /**
     * Génère un rapport de compliance
     */
    generateComplianceReport(framework: string): Promise<SecurityReport>;
    /**
     * Génère un rapport d'intelligence des menaces
     */
    generateThreatIntelligenceReport(): Promise<SecurityReport>;
    /**
     * Génère un rapport générique
     */
    generateReport(type: string, period: any, scope: any, configuration: any): Promise<SecurityReport>;
    /**
     * <PERSON><PERSON><PERSON> les métriques de sécurité
     */
    generateSecurityMetrics(): Promise<SecurityMetrics>;
    /**
     * Génère les métriques (alias pour generateSecurityMetrics)
     */
    generateMetrics(): Promise<SecurityMetrics>;
    private generateVulnerabilitySummary;
    private generateComplianceSummary;
    private generateThreatSummary;
    private calculateRiskScore;
    private calculateVulnerabilityTrends;
    private calculateThreatTrends;
    private generateExecutiveSummary;
    private generateDetailedAnalysis;
    private generateTrendsAnalysis;
    private generateVulnerabilityRecommendations;
    private generateComplianceRecommendations;
    private generateThreatLandscape;
    private generateEmergingThreats;
    private generateThreatTrendsAnalysis;
    private generateNonCompliantControlsAnalysis;
    private getTopVulnerabilities;
    private getNonCompliantControls;
    private getRecentIndicators;
    /**
     * Arrêt du générateur de rapports
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=ReportGenerator.d.ts.map