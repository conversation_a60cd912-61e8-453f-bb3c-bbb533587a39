import { Logger } from 'winston';
import { EventEmitter } from 'events';
import * as crypto from 'crypto';
import { WeaviateMemory } from '../memory/WeaviateMemory';

/**
 * Gestionnaire de Chiffrement
 *
 * Gère tous les aspects du chiffrement et de la cryptographie
 */
export class EncryptionManager extends EventEmitter {
  private logger: Logger;
  private memory: WeaviateMemory;

  private encryptionKeys: Map<string, EncryptionKey> = new Map();
  private certificates: Map<string, Certificate> = new Map();
  private keyRotationSchedule: Map<string, NodeJS.Timeout> = new Map();

  private isInitialized: boolean = false;
  private defaultAlgorithm: string = 'aes-256-gcm';
  private keyDerivationIterations: number = 100000;

  constructor(
    logger: Logger,
    memory: WeaviateMemory
  ) {
    super();
    this.logger = logger;
    this.memory = memory;
  }

  /**
   * Initialise le gestionnaire de chiffrement
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('🔐 Initialisation du Encryption Manager...');

      // Chargement des clés existantes
      await this.loadExistingKeys();

      // Génération des clés par défaut si nécessaire
      await this.ensureDefaultKeys();

      // Configuration de la rotation automatique
      await this.setupKeyRotation();

      // Vérification des certificats
      await this.verifyCertificates();

      this.isInitialized = true;
      this.logger.info('✅ Encryption Manager initialisé');

    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'initialisation du Encryption Manager:', error);
      throw error;
    }
  }

  /**
   * Chiffre des données
   */
  async encrypt(
    data: string | Buffer,
    keyId?: string,
    algorithm?: string
  ): Promise<EncryptionResult> {
    const startTime = Date.now();

    try {
      const key = keyId ? this.encryptionKeys.get(keyId) : this.getDefaultKey();
      if (!key) {
        throw new Error(`Clé de chiffrement non trouvée: ${keyId || 'default'}`);
      }

      const alg = algorithm || this.defaultAlgorithm;
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(alg, key.value);

      const dataString = typeof data === 'string' ? data : data.toString('utf8');
      let encrypted = cipher.update(dataString, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      const result: EncryptionResult = {
        encryptedData: encrypted,
        algorithm: alg,
        keyId: key.id,
        iv: iv.toString('hex'),
        timestamp: new Date(),
        processingTime: Date.now() - startTime
      };

      this.logger.debug(`🔒 Données chiffrées avec ${alg} (${result.processingTime}ms)`);

      // Audit du chiffrement
      await this.auditEncryption('encrypt', key.id, alg, result.processingTime);

      return result;

    } catch (error) {
      this.logger.error('❌ Erreur lors du chiffrement:', error);
      throw error;
    }
  }

  /**
   * Déchiffre des données
   */
  async decrypt(
    encryptedData: string,
    keyId: string,
    algorithm: string,
    iv?: string
  ): Promise<string> {
    const startTime = Date.now();

    try {
      const key = this.encryptionKeys.get(keyId);
      if (!key) {
        throw new Error(`Clé de déchiffrement non trouvée: ${keyId}`);
      }

      const decipher = crypto.createDecipher(algorithm, key.value);

      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      const processingTime = Date.now() - startTime;
      this.logger.debug(`🔓 Données déchiffrées avec ${algorithm} (${processingTime}ms)`);

      // Audit du déchiffrement
      await this.auditEncryption('decrypt', keyId, algorithm, processingTime);

      return decrypted;

    } catch (error) {
      this.logger.error('❌ Erreur lors du déchiffrement:', error);
      throw error;
    }
  }

  /**
   * Génère une nouvelle clé de chiffrement
   */
  async generateKey(
    keyId: string,
    algorithm: string = this.defaultAlgorithm,
    keySize: number = 256
  ): Promise<EncryptionKey> {
    try {
      const keyValue = crypto.randomBytes(keySize / 8);
      const salt = crypto.randomBytes(32);

      const key: EncryptionKey = {
        id: keyId,
        algorithm,
        value: keyValue,
        salt,
        keySize,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + (365 * 24 * 60 * 60 * 1000)), // 1 an
        isActive: true,
        version: 1,
        metadata: {
          generator: 'EncryptionManager',
          purpose: 'general'
        }
      };

      this.encryptionKeys.set(keyId, key);
      // await this.memory.storeEncryptionKey(key);

      this.logger.info(`🔑 Nouvelle clé générée: ${keyId} (${algorithm})`);

      // Planification de la rotation
      this.scheduleKeyRotation(key);

      this.emit('key-generated', { keyId, algorithm });

      return key;

    } catch (error) {
      this.logger.error('❌ Erreur lors de la génération de clé:', error);
      throw error;
    }
  }

  /**
   * Effectue la rotation d'une clé
   */
  async rotateKey(keyId: string): Promise<EncryptionKey> {
    try {
      const oldKey = this.encryptionKeys.get(keyId);
      if (!oldKey) {
        throw new Error(`Clé non trouvée pour rotation: ${keyId}`);
      }

      this.logger.info(`🔄 Rotation de la clé: ${keyId}`);

      // Désactivation de l'ancienne clé
      oldKey.isActive = false;
      oldKey.rotatedAt = new Date();

      // Génération de la nouvelle clé
      const newKey = await this.generateKey(
        keyId,
        oldKey.algorithm,
        oldKey.keySize
      );
      newKey.version = oldKey.version + 1;
      newKey.previousVersion = oldKey.version;

      // Stockage de l'historique
      // await this.memory.storeKeyRotationHistory({
      //   keyId,
      //   oldVersion: oldKey.version,
      //   newVersion: newKey.version,
      //   rotatedAt: new Date(),
      //   reason: 'scheduled-rotation'
      // });

      this.emit('key-rotated', { keyId, oldVersion: oldKey.version, newVersion: newKey.version });

      return newKey;

    } catch (error) {
      this.logger.error('❌ Erreur lors de la rotation de clé:', error);
      throw error;
    }
  }

  /**
   * Génère un hash sécurisé
   */
  async generateHash(
    data: string,
    algorithm: string = 'sha256',
    salt?: string
  ): Promise<HashResult> {
    try {
      const saltValue = salt || crypto.randomBytes(32).toString('hex');
      const hash = crypto.createHash(algorithm);

      hash.update(data + saltValue);
      const hashValue = hash.digest('hex');

      const result: HashResult = {
        hash: hashValue,
        algorithm,
        salt: saltValue,
        timestamp: new Date()
      };

      this.logger.debug(`#️⃣ Hash généré avec ${algorithm}`);

      return result;

    } catch (error) {
      this.logger.error('❌ Erreur lors de la génération de hash:', error);
      throw error;
    }
  }

  /**
   * Vérifie un hash
   */
  async verifyHash(
    data: string,
    expectedHash: string,
    salt: string,
    algorithm: string = 'sha256'
  ): Promise<boolean> {
    try {
      const hashResult = await this.generateHash(data, algorithm, salt);
      return hashResult.hash === expectedHash;

    } catch (error) {
      this.logger.error('❌ Erreur lors de la vérification de hash:', error);
      return false;
    }
  }

  /**
   * Génère une signature numérique
   */
  async generateSignature(
    data: string,
    privateKeyId: string,
    algorithm: string = 'RSA-SHA256'
  ): Promise<SignatureResult> {
    try {
      const key = this.encryptionKeys.get(privateKeyId);
      if (!key || key.type !== 'private') {
        throw new Error(`Clé privée non trouvée: ${privateKeyId}`);
      }

      const sign = crypto.createSign(algorithm);
      sign.update(data);
      const signature = sign.sign(key.value, 'hex');

      const result: SignatureResult = {
        signature,
        algorithm,
        keyId: privateKeyId,
        timestamp: new Date()
      };

      this.logger.debug(`✍️ Signature générée avec ${algorithm}`);

      return result;

    } catch (error) {
      this.logger.error('❌ Erreur lors de la génération de signature:', error);
      throw error;
    }
  }

  /**
   * Vérifie une signature numérique
   */
  async verifySignature(
    data: string,
    signature: string,
    publicKeyId: string,
    algorithm: string = 'RSA-SHA256'
  ): Promise<boolean> {
    try {
      const key = this.encryptionKeys.get(publicKeyId);
      if (!key || key.type !== 'public') {
        throw new Error(`Clé publique non trouvée: ${publicKeyId}`);
      }

      const verify = crypto.createVerify(algorithm);
      verify.update(data);
      const isValid = verify.verify(key.value, signature, 'hex');

      this.logger.debug(`✅ Signature ${isValid ? 'valide' : 'invalide'}`);

      return isValid;

    } catch (error) {
      this.logger.error('❌ Erreur lors de la vérification de signature:', error);
      return false;
    }
  }

  /**
   * Génère une paire de clés RSA
   */
  async generateKeyPair(
    keyId: string,
    keySize: number = 2048
  ): Promise<{ publicKey: EncryptionKey; privateKey: EncryptionKey }> {
    try {
      const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
        modulusLength: keySize,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem'
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem'
        }
      });

      const publicKeyObj: EncryptionKey = {
        id: `${keyId}-public`,
        algorithm: 'RSA',
        type: 'public',
        value: Buffer.from(publicKey),
        keySize,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + (2 * 365 * 24 * 60 * 60 * 1000)), // 2 ans
        isActive: true,
        version: 1,
        metadata: { purpose: 'signature' }
      };

      const privateKeyObj: EncryptionKey = {
        id: `${keyId}-private`,
        algorithm: 'RSA',
        type: 'private',
        value: Buffer.from(privateKey),
        keySize,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + (2 * 365 * 24 * 60 * 60 * 1000)), // 2 ans
        isActive: true,
        version: 1,
        metadata: { purpose: 'signature' }
      };

      this.encryptionKeys.set(publicKeyObj.id, publicKeyObj);
      this.encryptionKeys.set(privateKeyObj.id, privateKeyObj);

      // await this.memory.storeEncryptionKey(publicKeyObj);
      // await this.memory.storeEncryptionKey(privateKeyObj);

      this.logger.info(`🔑 Paire de clés RSA générée: ${keyId} (${keySize} bits)`);

      this.emit('keypair-generated', { keyId, keySize });

      return { publicKey: publicKeyObj, privateKey: privateKeyObj };

    } catch (error) {
      this.logger.error('❌ Erreur lors de la génération de paire de clés:', error);
      throw error;
    }
  }

  /**
   * Charge les clés existantes
   */
  private async loadExistingKeys(): Promise<void> {
    try {
      // const keys = await this.memory.getEncryptionKeys();
    const keys: any[] = [];
      keys.forEach(key => {
        this.encryptionKeys.set(key.id, key);
      });

      this.logger.info(`🔑 ${keys.length} clés de chiffrement chargées`);
    } catch (error) {
      this.logger.warn('⚠️ Erreur lors du chargement des clés:', error);
    }
  }

  /**
   * Assure la présence des clés par défaut
   */
  private async ensureDefaultKeys(): Promise<void> {
    if (!this.encryptionKeys.has('default')) {
      await this.generateKey('default');
    }

    if (!this.encryptionKeys.has('system-public') || !this.encryptionKeys.has('system-private')) {
      await this.generateKeyPair('system');
    }
  }

  /**
   * Configure la rotation automatique des clés
   */
  private async setupKeyRotation(): Promise<void> {
    for (const [keyId, key] of Array.from(this.encryptionKeys)) {
      if (key.isActive) {
        this.scheduleKeyRotation(key);
      }
    }
  }

  /**
   * Planifie la rotation d'une clé
   */
  private scheduleKeyRotation(key: EncryptionKey): void {
    const rotationInterval = 90 * 24 * 60 * 60 * 1000; // 90 jours
    const timeUntilRotation = key.expiresAt.getTime() - Date.now() - rotationInterval;

    if (timeUntilRotation > 0) {
      const timeout = setTimeout(async () => {
        try {
          await this.rotateKey(key.id);
        } catch (error) {
          this.logger.error(`❌ Erreur lors de la rotation automatique de ${key.id}:`, error);
        }
      }, timeUntilRotation);

      this.keyRotationSchedule.set(key.id, timeout);
    }
  }

  /**
   * Vérifie les certificats
   */
  private async verifyCertificates(): Promise<void> {
    for (const [certId, cert] of Array.from(this.certificates)) {
      if (cert.expiresAt < new Date()) {
        this.logger.warn(`⚠️ Certificat expiré: ${certId}`);
        this.emit('certificate-expired', { certId });
      }
    }
  }

  /**
   * Récupère la clé par défaut
   */
  private getDefaultKey(): EncryptionKey | undefined {
    return this.encryptionKeys.get('default');
  }

  /**
   * Audit des opérations de chiffrement
   */
  private async auditEncryption(
    operation: string,
    keyId: string,
    algorithm: string,
    processingTime: number
  ): Promise<void> {
    const auditEvent = {
      type: 'encryption-operation',
      operation,
      keyId,
      algorithm,
      processingTime,
      timestamp: new Date()
    };

    this.emit('encryption-audit', auditEvent);
  }

  /**
   * Récupère les statistiques de chiffrement
   */
  getEncryptionStats(): EncryptionStats {
    const activeKeys = Array.from(this.encryptionKeys.values()).filter(k => k.isActive);
    const expiredKeys = Array.from(this.encryptionKeys.values()).filter(k => !k.isActive);

    return {
      totalKeys: this.encryptionKeys.size,
      activeKeys: activeKeys.length,
      expiredKeys: expiredKeys.length,
      keysByAlgorithm: this.getKeysByAlgorithm(),
      oldestKey: this.getOldestKey(),
      newestKey: this.getNewestKey()
    };
  }

  /**
   * Récupère les clés par algorithme
   */
  private getKeysByAlgorithm(): Record<string, number> {
    const counts: Record<string, number> = {};

    for (const key of Array.from(this.encryptionKeys.values())) {
      counts[key.algorithm] = (counts[key.algorithm] || 0) + 1;
    }

    return counts;
  }

  /**
   * Récupère la clé la plus ancienne
   */
  private getOldestKey(): Date | null {
    const keys = Array.from(this.encryptionKeys.values());
    if (keys.length === 0) return null;

    return new Date(Math.min(...keys.map(k => k.createdAt.getTime())));
  }

  /**
   * Récupère la clé la plus récente
   */
  private getNewestKey(): Date | null {
    const keys = Array.from(this.encryptionKeys.values());
    if (keys.length === 0) return null;

    return new Date(Math.max(...keys.map(k => k.createdAt.getTime())));
  }

  /**
   * Arrêt du gestionnaire de chiffrement
   */
  async shutdown(): Promise<void> {
    this.logger.info('🛑 Arrêt du Encryption Manager...');

    // Annulation des rotations planifiées
    for (const timeout of Array.from(this.keyRotationSchedule.values())) {
      clearTimeout(timeout);
    }
    this.keyRotationSchedule.clear();

    this.isInitialized = false;
  }
}

// Interfaces pour le chiffrement
interface EncryptionKey {
  id: string;
  algorithm: string;
  type?: 'symmetric' | 'public' | 'private';
  value: Buffer;
  salt?: Buffer;
  keySize: number;
  createdAt: Date;
  expiresAt: Date;
  rotatedAt?: Date;
  isActive: boolean;
  version: number;
  previousVersion?: number;
  metadata: any;
}

interface Certificate {
  id: string;
  subject: string;
  issuer: string;
  serialNumber: string;
  notBefore: Date;
  expiresAt: Date;
  fingerprint: string;
  publicKey: Buffer;
  certificate: Buffer;
}

interface EncryptionResult {
  encryptedData: string;
  algorithm: string;
  keyId: string;
  iv: string;
  timestamp: Date;
  processingTime: number;
}

interface HashResult {
  hash: string;
  algorithm: string;
  salt: string;
  timestamp: Date;
}

interface SignatureResult {
  signature: string;
  algorithm: string;
  keyId: string;
  timestamp: Date;
}

interface EncryptionStats {
  totalKeys: number;
  activeKeys: number;
  expiredKeys: number;
  keysByAlgorithm: Record<string, number>;
  oldestKey: Date | null;
  newestKey: Date | null;
}
