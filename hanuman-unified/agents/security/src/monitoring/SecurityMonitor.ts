import { EventEmitter } from 'events';
import { Logger } from 'winston';
import {
  SecurityMonitoringConfig,
  SecurityAlert,
  SecurityMetrics,
  ThreatIndicator,
  SecurityIncident
} from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';

/**
 * Moniteur de Sécurité
 *
 * Surveille les événements de sécurité en temps réel et génère des alertes
 */
export class SecurityMonitor extends EventEmitter {
  private logger: Logger;
  private memory: WeaviateMemory;
  private config: SecurityMonitoringConfig;
  private isRunning: boolean = false;
  private monitoringIntervals: NodeJS.Timeout[] = [];
  private alertQueue: SecurityAlert[] = [];
  private metrics: SecurityMetrics = this.initializeMetrics();

  constructor(config: SecurityMonitoringConfig, logger: Logger, memory: WeaviateMemory) {
    super();
    this.config = config;
    this.logger = logger;
    this.memory = memory;
  }

  /**
   * Initialise le monitoring de sécurité
   */
  async initialize(): Promise<void> {
    this.logger.info('📊 Initialisation du Monitoring de Sécurité...');
    // Initialisation déjà effectuée dans start()
    this.logger.info('✅ Monitoring de Sécurité initialisé');
  }

  /**
   * Démarre le monitoring de sécurité
   */
  async start(): Promise<void> {
    try {
      this.logger.info('📊 Démarrage du Monitoring de Sécurité...');

      // Démarrage du monitoring en temps réel
      if (this.config.realTime) {
        await this.startRealTimeMonitoring();
      }

      // Démarrage de la collecte de métriques
      if (this.config.metrics.collection) {
        this.startMetricsCollection();
      }

      // Démarrage du processeur d'alertes
      this.startAlertProcessor();

      // Démarrage des vérifications périodiques
      this.startPeriodicChecks();

      this.isRunning = true;
      this.logger.info('✅ Monitoring de Sécurité démarré');

    } catch (error) {
      this.logger.error('❌ Erreur lors du démarrage du monitoring:', error);
      throw error;
    }
  }

  /**
   * Démarre le monitoring en temps réel
   */
  private async startRealTimeMonitoring(): Promise<void> {
    this.logger.info('🔄 Démarrage du monitoring en temps réel...');

    // Monitoring des vulnérabilités
    const vulnMonitoring = setInterval(async () => {
      await this.checkForNewVulnerabilities();
    }, 30000); // Toutes les 30 secondes

    // Monitoring des menaces
    const threatMonitoring = setInterval(async () => {
      await this.checkForNewThreats();
    }, 60000); // Toutes les minutes

    // Monitoring des incidents
    const incidentMonitoring = setInterval(async () => {
      await this.checkForActiveIncidents();
    }, 45000); // Toutes les 45 secondes

    // Monitoring des métriques système
    const systemMonitoring = setInterval(async () => {
      await this.collectSystemMetrics();
    }, 120000); // Toutes les 2 minutes

    this.monitoringIntervals.push(vulnMonitoring, threatMonitoring, incidentMonitoring, systemMonitoring);
  }

  /**
   * Vérifie les nouvelles vulnérabilités
   */
  private async checkForNewVulnerabilities(): Promise<void> {
    try {
      const recentScans = await this.memory.getRecentScanResults(10);

      for (const scan of recentScans) {
        const criticalVulns = scan.vulnerabilities.filter(v => v.severity === 'critical');
        const highVulns = scan.vulnerabilities.filter(v => v.severity === 'high');

        if (criticalVulns.length > 0) {
          await this.generateAlert({
            id: `vuln-critical-${Date.now()}`,
            type: 'vulnerability-detection',
            title: `${criticalVulns.length} vulnérabilités critiques détectées`,
            description: `Scan ${scan.scanId} a révélé ${criticalVulns.length} vulnérabilités critiques`,
            severity: 'critical',
            source: 'SecurityMonitor',
            timestamp: new Date(),
            data: {
              scanId: scan.scanId,
              vulnerabilities: criticalVulns.length,
              critical: criticalVulns.length,
              high: highVulns.length
            }
          });

          this.emit('vulnerability-found', {
            scanId: scan.scanId,
            vulnerabilities: criticalVulns,
            severity: 'critical'
          });
        }
      }

    } catch (error) {
      this.logger.error('❌ Erreur lors de la vérification des vulnérabilités:', error);
    }
  }

  /**
   * Vérifie les nouvelles menaces
   */
  private async checkForNewThreats(): Promise<void> {
    try {
      const indicators = await this.memory.getThreatIndicators(50);
      const recentIndicators = indicators.filter(i =>
        Date.now() - i.lastSeen.getTime() < 300000 // 5 minutes
      );

      if (recentIndicators.length > 0) {
        const highThreatIndicators = recentIndicators.filter(i =>
          i.threatLevel === 'high' || i.threatLevel === 'critical'
        );

        if (highThreatIndicators.length > 0) {
          await this.generateAlert({
            id: `threat-${Date.now()}`,
            type: 'threat-detection',
            title: `${highThreatIndicators.length} nouvelles menaces détectées`,
            description: `${highThreatIndicators.length} indicateurs de menace de haute sévérité détectés`,
            severity: 'high',
            source: 'ThreatIntelligence',
            timestamp: new Date(),
            data: {
              indicators: highThreatIndicators.length,
              types: [...new Set(highThreatIndicators.map(i => i.type))]
            }
          });

          this.emit('threat-detected', {
            indicators: highThreatIndicators,
            count: highThreatIndicators.length
          });
        }
      }

    } catch (error) {
      this.logger.error('❌ Erreur lors de la vérification des menaces:', error);
    }
  }

  /**
   * Vérifie les incidents actifs
   */
  private async checkForActiveIncidents(): Promise<void> {
    try {
      const activeIncidents = await this.memory.getActiveIncidents();

      // Vérification des incidents critiques non résolus
      const criticalIncidents = activeIncidents.filter(i =>
        i.severity === 'critical' && i.status !== 'resolved'
      );

      if (criticalIncidents.length > 0) {
        for (const incident of criticalIncidents) {
          const timeSinceDetection = Date.now() - incident.detectedAt.getTime();

          // Alerte si incident critique non résolu depuis plus de 30 minutes
          if (timeSinceDetection > 1800000) { // 30 minutes
            await this.generateAlert({
              id: `incident-escalation-${incident.id}`,
              type: 'incident-escalation',
              title: `Incident critique non résolu: ${incident.title}`,
              description: `L'incident ${incident.id} est critique et non résolu depuis ${Math.round(timeSinceDetection / 60000)} minutes`,
              severity: 'critical',
              source: 'IncidentResponse',
              timestamp: new Date(),
              data: {
                incidentId: incident.id,
                timeSinceDetection: timeSinceDetection,
                status: incident.status
              }
            });
          }
        }
      }

    } catch (error) {
      this.logger.error('❌ Erreur lors de la vérification des incidents:', error);
    }
  }

  /**
   * Collecte les métriques système
   */
  private async collectSystemMetrics(): Promise<void> {
    try {
      const systemMetrics = {
        timestamp: new Date(),
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024)
        },
        uptime: process.uptime(),
        cpu: process.cpuUsage(),
        activeConnections: this.getActiveConnectionsCount(),
        queueSize: this.alertQueue.length
      };

      // Mise à jour des métriques
      this.updateMetrics(systemMetrics);

      // Vérification des seuils d'alerte
      await this.checkSystemThresholds(systemMetrics);

    } catch (error) {
      this.logger.error('❌ Erreur lors de la collecte des métriques:', error);
    }
  }

  /**
   * Génère une alerte de sécurité
   */
  private async generateAlert(alert: SecurityAlert): Promise<void> {
    try {
      // Ajout à la queue d'alertes
      this.alertQueue.push(alert);

      // Stockage en mémoire
      await this.memory.storeSecurityAlert(alert);

      // Émission d'événement
      this.emit('security-alert', alert);

      this.logger.warn(`🚨 Alerte générée: ${alert.title} (${alert.severity})`);

    } catch (error) {
      this.logger.error('❌ Erreur lors de la génération d\'alerte:', error);
    }
  }

  /**
   * Démarre le processeur d'alertes
   */
  private startAlertProcessor(): void {
    const processor = setInterval(async () => {
      if (this.alertQueue.length > 0) {
        const alert = this.alertQueue.shift();
        if (alert) {
          await this.processAlert(alert);
        }
      }
    }, 5000); // Toutes les 5 secondes

    this.monitoringIntervals.push(processor);
  }

  /**
   * Traite une alerte
   */
  private async processAlert(alert: SecurityAlert): Promise<void> {
    try {
      // Vérification des règles d'alerte
      const applicableRules = this.config.alerting.rules.filter(rule =>
        this.evaluateAlertRule(rule, alert)
      );

      for (const rule of applicableRules) {
        // Envoi via les canaux configurés
        for (const channelId of rule.channels) {
          const channel = this.config.alerting.channels.find(c => c.type === channelId);
          if (channel && channel.enabled) {
            await this.sendAlertToChannel(alert, channel);
          }
        }
      }

    } catch (error) {
      this.logger.error('❌ Erreur lors du traitement d\'alerte:', error);
    }
  }

  /**
   * Évalue une règle d'alerte
   */
  private evaluateAlertRule(rule: any, alert: SecurityAlert): boolean {
    // Logique d'évaluation simplifiée
    if (rule.severity && alert.severity !== rule.severity) {
      return false;
    }

    // Évaluation de la condition (simplifiée)
    return true;
  }

  /**
   * Envoie une alerte via un canal
   */
  private async sendAlertToChannel(alert: SecurityAlert, channel: any): Promise<void> {
    try {
      switch (channel.type) {
        case 'webhook':
          // Simulation d'envoi webhook
          this.logger.info(`📤 Alerte envoyée via webhook: ${alert.title}`);
          break;

        case 'email':
          // Simulation d'envoi email
          this.logger.info(`📧 Alerte envoyée par email: ${alert.title}`);
          break;

        default:
          this.logger.warn(`⚠️ Type de canal non supporté: ${channel.type}`);
      }

    } catch (error) {
      this.logger.error(`❌ Erreur lors de l'envoi via ${channel.type}:`, error);
    }
  }

  /**
   * Démarre les vérifications périodiques
   */
  private startPeriodicChecks(): void {
    // Vérification de la santé du système toutes les 5 minutes
    const healthCheck = setInterval(async () => {
      await this.performHealthCheck();
    }, 300000);

    this.monitoringIntervals.push(healthCheck);
  }

  /**
   * Effectue une vérification de santé
   */
  private async performHealthCheck(): Promise<void> {
    try {
      const health = {
        timestamp: new Date(),
        status: 'healthy',
        components: {
          memory: this.memory.getConnectionStatus(),
          monitoring: this.isRunning,
          alertQueue: this.alertQueue.length < 100,
          uptime: process.uptime() > 0
        }
      };

      const unhealthyComponents = Object.entries(health.components)
        .filter(([_, status]) => !status)
        .map(([name, _]) => name);

      if (unhealthyComponents.length > 0) {
        health.status = 'unhealthy';

        await this.generateAlert({
          id: `health-check-${Date.now()}`,
          type: 'system-health',
          title: 'Problème de santé système détecté',
          description: `Composants défaillants: ${unhealthyComponents.join(', ')}`,
          severity: 'medium',
          source: 'SecurityMonitor',
          timestamp: new Date(),
          data: { unhealthyComponents }
        });
      }

      this.logger.debug(`💓 Vérification de santé: ${health.status}`);

    } catch (error) {
      this.logger.error('❌ Erreur lors de la vérification de santé:', error);
    }
  }

  /**
   * Démarre la collecte de métriques
   */
  private startMetricsCollection(): void {
    const metricsCollector = setInterval(async () => {
      await this.collectAndExportMetrics();
    }, 60000); // Toutes les minutes

    this.monitoringIntervals.push(metricsCollector);
  }

  /**
   * Collecte et exporte les métriques
   */
  private async collectAndExportMetrics(): Promise<void> {
    try {
      const securityMetrics = await this.memory.getSecurityMetrics();

      // Mise à jour des métriques locales
      this.metrics = {
        ...this.metrics,
        ...securityMetrics,
        lastUpdate: new Date()
      };

      // Export vers les systèmes configurés
      for (const exportConfig of this.config.metrics.export) {
        if (exportConfig.enabled) {
          await this.exportMetrics(exportConfig, this.metrics);
        }
      }

    } catch (error) {
      this.logger.error('❌ Erreur lors de la collecte de métriques:', error);
    }
  }

  /**
   * Exporte les métriques
   */
  private async exportMetrics(exportConfig: any, metrics: SecurityMetrics): Promise<void> {
    try {
      switch (exportConfig.type) {
        case 'prometheus':
          // Simulation d'export Prometheus
          this.logger.debug('📊 Métriques exportées vers Prometheus');
          break;

        default:
          this.logger.warn(`⚠️ Type d'export non supporté: ${exportConfig.type}`);
      }

    } catch (error) {
      this.logger.error(`❌ Erreur lors de l'export ${exportConfig.type}:`, error);
    }
  }

  // Méthodes utilitaires

  private initializeMetrics(): SecurityMetrics {
    return {
      vulnerabilities: {
        total: 0,
        bySeverity: {} as any,
        byCategory: {} as any,
        meanTimeToDetection: 0,
        meanTimeToRemediation: 0,
        trends: []
      },
      compliance: {
        overallScore: 0,
        byFramework: {},
        controlsTotal: 0,
        controlsCompliant: 0,
        controlsNonCompliant: 0,
        trends: []
      },
      incidents: {
        total: 0,
        bySeverity: {},
        byCategory: {} as any,
        meanTimeToDetection: 0,
        meanTimeToContainment: 0,
        meanTimeToResolution: 0,
        trends: []
      },
      threats: {
        indicatorsTotal: 0,
        indicatorsByType: {},
        threatsBlocked: 0,
        threatsDetected: 0,
        falsePositives: 0,
        trends: []
      },
      coverage: {
        assetsScanned: 0,
        assetsTotal: 0,
        coveragePercentage: 0,
        scanFrequency: 0,
        lastScanAge: 0
      }
    };
  }

  private updateMetrics(systemMetrics: any): void {
    // Mise à jour des métriques avec les données système
    this.metrics.lastUpdate = new Date();
  }

  private async checkSystemThresholds(systemMetrics: any): Promise<void> {
    // Vérification des seuils système et génération d'alertes si nécessaire
    if (systemMetrics.memory.used > 1000) { // Plus de 1GB
      await this.generateAlert({
        id: `memory-threshold-${Date.now()}`,
        type: 'system-resource',
        title: 'Utilisation mémoire élevée',
        description: `Utilisation mémoire: ${systemMetrics.memory.used}MB`,
        severity: 'medium',
        source: 'SystemMonitor',
        timestamp: new Date(),
        data: systemMetrics.memory
      });
    }
  }

  private getActiveConnectionsCount(): number {
    // Simulation du nombre de connexions actives
    return Math.floor(Math.random() * 100);
  }

  /**
   * Obtient les métriques actuelles
   */
  getMetrics(): SecurityMetrics {
    return this.metrics;
  }

  /**
   * Obtient le statut du monitoring
   */
  getStatus(): any {
    return {
      isRunning: this.isRunning,
      alertQueueSize: this.alertQueue.length,
      activeIntervals: this.monitoringIntervals.length,
      uptime: process.uptime(),
      lastMetricsUpdate: this.metrics.lastUpdate
    };
  }

  /**
   * Envoie une alerte
   */
  async sendAlert(alert: any): Promise<void> {
    await this.generateAlert({
      id: alert.id || `alert-${Date.now()}`,
      type: alert.type,
      title: alert.title,
      description: alert.description,
      severity: alert.severity,
      source: alert.source || 'SecurityMonitor',
      timestamp: new Date(),
      data: alert.data
    });
  }

  /**
   * Arrête le monitoring
   */
  async stop(): Promise<void> {
    this.logger.info('🛑 Arrêt du Monitoring de Sécurité...');

    // Arrêt de tous les intervalles
    this.monitoringIntervals.forEach(interval => clearInterval(interval));
    this.monitoringIntervals = [];

    // Traitement des alertes restantes
    while (this.alertQueue.length > 0) {
      const alert = this.alertQueue.shift();
      if (alert) {
        await this.processAlert(alert);
      }
    }

    this.isRunning = false;
    this.logger.info('✅ Monitoring de Sécurité arrêté');
  }

  /**
   * Arrêt du monitoring (alias pour stop)
   */
  async shutdown(): Promise<void> {
    await this.stop();
  }
}
