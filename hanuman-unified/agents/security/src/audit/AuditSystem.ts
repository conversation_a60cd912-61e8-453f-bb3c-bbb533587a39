import { Logger } from 'winston';
import { EventEmitter } from 'events';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';

/**
 * Système d'Audit Complet
 * 
 * Enregistre, analyse et rapporte toutes les activités de sécurité
 */
export class AuditSystem extends EventEmitter {
  private logger: Logger;
  private memory: WeaviateMemory;
  private communication: KafkaCommunication;
  
  private auditLogs: Map<string, AuditLog> = new Map();
  private auditRules: Map<string, AuditRule> = new Map();
  private auditReports: Map<string, AuditReport> = new Map();
  private alertThresholds: Map<string, AlertThreshold> = new Map();
  
  private isInitialized: boolean = false;
  private isMonitoring: boolean = false;

  constructor(
    logger: Logger,
    memory: WeaviateMemory,
    communication: KafkaCommunication
  ) {
    super();
    this.logger = logger;
    this.memory = memory;
    this.communication = communication;
  }

  /**
   * Initialise le système d'audit
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('📋 Initialisation du Audit System...');

      // Chargement des règles d'audit
      await this.loadAuditRules();

      // Configuration des seuils d'alerte
      await this.setupAlertThresholds();

      // Chargement des logs existants
      await this.loadExistingLogs();

      // Démarrage du monitoring
      this.startAuditMonitoring();

      this.isInitialized = true;
      this.logger.info('✅ Audit System initialisé');

    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'initialisation du Audit System:', error);
      throw error;
    }
  }

  /**
   * Enregistre un événement d'audit
   */
  async logEvent(event: AuditEvent): Promise<void> {
    const auditLog: AuditLog = {
      id: this.generateAuditId(),
      timestamp: new Date(),
      event,
      source: event.source || 'security-agent',
      severity: this.calculateEventSeverity(event),
      category: event.category,
      metadata: {
        ...event.metadata,
        processingTime: Date.now()
      }
    };

    // Stockage du log
    this.auditLogs.set(auditLog.id, auditLog);
    await this.memory.storeAuditLog(auditLog);

    this.logger.info(`📝 Événement d'audit enregistré: ${event.type} (${auditLog.severity})`);

    // Vérification des règles d'audit
    await this.checkAuditRules(auditLog);

    // Notification
    this.emit('audit-logged', auditLog);

    // Vérification des seuils d'alerte
    await this.checkAlertThresholds(auditLog);
  }

  /**
   * Enregistre un accès système
   */
  async logAccess(accessEvent: AccessEvent): Promise<void> {
    const event: AuditEvent = {
      type: 'access',
      category: 'authentication',
      description: `Accès ${accessEvent.action} pour ${accessEvent.userId}`,
      userId: accessEvent.userId,
      resource: accessEvent.resource,
      action: accessEvent.action,
      result: accessEvent.result,
      ipAddress: accessEvent.ipAddress,
      userAgent: accessEvent.userAgent,
      metadata: accessEvent.metadata
    };

    await this.logEvent(event);
  }

  /**
   * Enregistre une modification de configuration
   */
  async logConfigurationChange(configEvent: ConfigurationEvent): Promise<void> {
    const event: AuditEvent = {
      type: 'configuration-change',
      category: 'system',
      description: `Configuration modifiée: ${configEvent.component}`,
      userId: configEvent.userId,
      resource: configEvent.component,
      action: 'modify',
      result: 'success',
      metadata: {
        oldValue: configEvent.oldValue,
        newValue: configEvent.newValue,
        changeReason: configEvent.reason
      }
    };

    await this.logEvent(event);
  }

  /**
   * Enregistre un incident de sécurité
   */
  async logSecurityIncident(incident: SecurityIncidentEvent): Promise<void> {
    const event: AuditEvent = {
      type: 'security-incident',
      category: 'security',
      description: `Incident de sécurité: ${incident.title}`,
      resource: incident.affectedResource,
      action: 'incident',
      result: 'detected',
      metadata: {
        incidentId: incident.incidentId,
        severity: incident.severity,
        category: incident.category,
        detectionMethod: incident.detectionMethod
      }
    };

    await this.logEvent(event);
  }

  /**
   * Génère un rapport d'audit
   */
  async generateAuditReport(
    reportType: AuditReportType,
    period: { start: Date; end: Date },
    filters?: AuditFilters
  ): Promise<AuditReport> {
    this.logger.info(`📊 Génération du rapport d'audit: ${reportType}`);

    const reportId = this.generateReportId();
    const logs = await this.getLogsForPeriod(period, filters);

    const report: AuditReport = {
      id: reportId,
      type: reportType,
      period,
      generatedAt: new Date(),
      summary: this.generateReportSummary(logs),
      findings: this.analyzeLogsForFindings(logs),
      recommendations: this.generateRecommendations(logs),
      statistics: this.calculateStatistics(logs),
      logs: logs.slice(0, 1000), // Limiter pour la performance
      metadata: {
        totalLogs: logs.length,
        filters: filters || {}
      }
    };

    // Stockage du rapport
    this.auditReports.set(reportId, report);
    await this.memory.storeAuditReport(report);

    this.emit('audit-report-generated', report);

    return report;
  }

  /**
   * Recherche dans les logs d'audit
   */
  async searchLogs(query: AuditSearchQuery): Promise<AuditLog[]> {
    const allLogs = Array.from(this.auditLogs.values());
    
    return allLogs.filter(log => {
      // Filtrage par période
      if (query.startDate && log.timestamp < query.startDate) return false;
      if (query.endDate && log.timestamp > query.endDate) return false;
      
      // Filtrage par utilisateur
      if (query.userId && log.event.userId !== query.userId) return false;
      
      // Filtrage par type d'événement
      if (query.eventType && log.event.type !== query.eventType) return false;
      
      // Filtrage par catégorie
      if (query.category && log.category !== query.category) return false;
      
      // Filtrage par sévérité
      if (query.severity && log.severity !== query.severity) return false;
      
      // Recherche textuelle
      if (query.searchText) {
        const searchLower = query.searchText.toLowerCase();
        const description = log.event.description?.toLowerCase() || '';
        const resource = log.event.resource?.toLowerCase() || '';
        
        if (!description.includes(searchLower) && !resource.includes(searchLower)) {
          return false;
        }
      }
      
      return true;
    }).slice(0, query.limit || 100);
  }

  /**
   * Vérifie les règles d'audit
   */
  private async checkAuditRules(auditLog: AuditLog): Promise<void> {
    for (const [ruleId, rule] of this.auditRules) {
      if (rule.enabled && this.matchesRule(auditLog, rule)) {
        await this.executeRuleAction(rule, auditLog);
      }
    }
  }

  /**
   * Vérifie si un log correspond à une règle
   */
  private matchesRule(auditLog: AuditLog, rule: AuditRule): boolean {
    // Vérification du type d'événement
    if (rule.conditions.eventTypes && 
        !rule.conditions.eventTypes.includes(auditLog.event.type)) {
      return false;
    }

    // Vérification de la sévérité
    if (rule.conditions.minSeverity) {
      const severityLevels = { low: 1, medium: 2, high: 3, critical: 4 };
      const logLevel = severityLevels[auditLog.severity as keyof typeof severityLevels] || 0;
      const minLevel = severityLevels[rule.conditions.minSeverity as keyof typeof severityLevels] || 0;
      
      if (logLevel < minLevel) return false;
    }

    // Vérification des patterns
    if (rule.conditions.patterns) {
      const description = auditLog.event.description || '';
      const hasMatch = rule.conditions.patterns.some(pattern => {
        const regex = new RegExp(pattern, 'i');
        return regex.test(description);
      });
      
      if (!hasMatch) return false;
    }

    return true;
  }

  /**
   * Exécute l'action d'une règle
   */
  private async executeRuleAction(rule: AuditRule, auditLog: AuditLog): Promise<void> {
    this.logger.info(`⚡ Exécution de la règle d'audit: ${rule.name}`);

    switch (rule.action.type) {
      case 'alert':
        await this.sendAlert(rule, auditLog);
        break;
      
      case 'escalate':
        await this.escalateIncident(rule, auditLog);
        break;
      
      case 'block':
        await this.blockAction(rule, auditLog);
        break;
      
      case 'notify':
        await this.sendNotification(rule, auditLog);
        break;
    }
  }

  /**
   * Envoie une alerte
   */
  private async sendAlert(rule: AuditRule, auditLog: AuditLog): Promise<void> {
    const alert = {
      type: 'audit-rule-triggered',
      title: `Règle d'audit déclenchée: ${rule.name}`,
      description: auditLog.event.description,
      severity: auditLog.severity,
      ruleId: rule.id,
      auditLogId: auditLog.id,
      timestamp: new Date()
    };

    await this.communication.sendMessage('security-alerts', alert);
    this.emit('audit-alert', alert);
  }

  /**
   * Calcule la sévérité d'un événement
   */
  private calculateEventSeverity(event: AuditEvent): string {
    // Logique de calcul de sévérité basée sur le type et le contenu
    if (event.type === 'security-incident') return 'critical';
    if (event.type === 'access' && event.result === 'failure') return 'high';
    if (event.type === 'configuration-change') return 'medium';
    
    return 'low';
  }

  /**
   * Charge les règles d'audit
   */
  private async loadAuditRules(): Promise<void> {
    // Règles par défaut
    this.auditRules.set('failed-login-attempts', {
      id: 'failed-login-attempts',
      name: 'Tentatives de connexion échouées',
      description: 'Détecte les tentatives de connexion multiples échouées',
      enabled: true,
      conditions: {
        eventTypes: ['access'],
        patterns: ['failure', 'failed'],
        minSeverity: 'medium'
      },
      action: {
        type: 'alert',
        parameters: { threshold: 5, timeWindow: 300 }
      }
    });

    this.auditRules.set('privilege-escalation', {
      id: 'privilege-escalation',
      name: 'Escalade de privilèges',
      description: 'Détecte les tentatives d\'escalade de privilèges',
      enabled: true,
      conditions: {
        eventTypes: ['access', 'configuration-change'],
        patterns: ['admin', 'root', 'privilege'],
        minSeverity: 'high'
      },
      action: {
        type: 'escalate',
        parameters: { severity: 'critical' }
      }
    });
  }

  /**
   * Configure les seuils d'alerte
   */
  private async setupAlertThresholds(): Promise<void> {
    this.alertThresholds.set('failed-logins', {
      id: 'failed-logins',
      name: 'Connexions échouées',
      eventType: 'access',
      threshold: 10,
      timeWindow: 300, // 5 minutes
      action: 'alert'
    });

    this.alertThresholds.set('security-incidents', {
      id: 'security-incidents',
      name: 'Incidents de sécurité',
      eventType: 'security-incident',
      threshold: 3,
      timeWindow: 3600, // 1 heure
      action: 'escalate'
    });
  }

  /**
   * Vérifie les seuils d'alerte
   */
  private async checkAlertThresholds(auditLog: AuditLog): Promise<void> {
    for (const [thresholdId, threshold] of this.alertThresholds) {
      if (auditLog.event.type === threshold.eventType) {
        const recentLogs = await this.getRecentLogsOfType(
          threshold.eventType,
          threshold.timeWindow
        );

        if (recentLogs.length >= threshold.threshold) {
          await this.triggerThresholdAlert(threshold, recentLogs);
        }
      }
    }
  }

  /**
   * Récupère les logs récents d'un type donné
   */
  private async getRecentLogsOfType(eventType: string, timeWindow: number): Promise<AuditLog[]> {
    const cutoffTime = new Date(Date.now() - (timeWindow * 1000));
    
    return Array.from(this.auditLogs.values()).filter(log => 
      log.event.type === eventType && log.timestamp > cutoffTime
    );
  }

  /**
   * Déclenche une alerte de seuil
   */
  private async triggerThresholdAlert(threshold: AlertThreshold, logs: AuditLog[]): Promise<void> {
    const alert = {
      type: 'threshold-exceeded',
      title: `Seuil dépassé: ${threshold.name}`,
      description: `${logs.length} événements de type ${threshold.eventType} en ${threshold.timeWindow}s`,
      severity: 'high',
      thresholdId: threshold.id,
      eventCount: logs.length,
      timestamp: new Date()
    };

    await this.communication.sendMessage('security-alerts', alert);
    this.emit('threshold-alert', alert);
  }

  /**
   * Charge les logs existants
   */
  private async loadExistingLogs(): Promise<void> {
    try {
      const existingLogs = await this.memory.getAuditLogs();
      existingLogs.forEach(log => {
        this.auditLogs.set(log.id, log);
      });
      
      this.logger.info(`📚 ${existingLogs.length} logs d'audit chargés`);
    } catch (error) {
      this.logger.warn('⚠️ Erreur lors du chargement des logs existants:', error);
    }
  }

  /**
   * Démarre le monitoring d'audit
   */
  private startAuditMonitoring(): void {
    this.isMonitoring = true;
    
    // Nettoyage périodique des anciens logs
    setInterval(() => {
      this.cleanupOldLogs();
    }, 60 * 60 * 1000); // Toutes les heures

    // Génération de rapports automatiques
    setInterval(() => {
      this.generateAutomaticReports();
    }, 24 * 60 * 60 * 1000); // Tous les jours
  }

  /**
   * Nettoie les anciens logs
   */
  private cleanupOldLogs(): void {
    const retentionDays = 90; // 3 mois
    const cutoffDate = new Date(Date.now() - (retentionDays * 24 * 60 * 60 * 1000));
    
    let cleanedCount = 0;
    for (const [logId, log] of this.auditLogs) {
      if (log.timestamp < cutoffDate) {
        this.auditLogs.delete(logId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.info(`🧹 ${cleanedCount} logs d'audit anciens nettoyés`);
    }
  }

  /**
   * Génère des rapports automatiques
   */
  private async generateAutomaticReports(): Promise<void> {
    const yesterday = new Date(Date.now() - (24 * 60 * 60 * 1000));
    const today = new Date();

    try {
      await this.generateAuditReport('daily', {
        start: yesterday,
        end: today
      });
    } catch (error) {
      this.logger.error('❌ Erreur lors de la génération du rapport automatique:', error);
    }
  }

  /**
   * Récupère les logs pour une période
   */
  private async getLogsForPeriod(
    period: { start: Date; end: Date },
    filters?: AuditFilters
  ): Promise<AuditLog[]> {
    return Array.from(this.auditLogs.values()).filter(log => {
      if (log.timestamp < period.start || log.timestamp > period.end) {
        return false;
      }
      
      if (filters?.eventTypes && !filters.eventTypes.includes(log.event.type)) {
        return false;
      }
      
      if (filters?.severity && log.severity !== filters.severity) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * Génère un résumé de rapport
   */
  private generateReportSummary(logs: AuditLog[]): AuditReportSummary {
    const eventTypes = new Map<string, number>();
    const severities = new Map<string, number>();
    
    logs.forEach(log => {
      eventTypes.set(log.event.type, (eventTypes.get(log.event.type) || 0) + 1);
      severities.set(log.severity, (severities.get(log.severity) || 0) + 1);
    });

    return {
      totalEvents: logs.length,
      eventsByType: Object.fromEntries(eventTypes),
      eventsBySeverity: Object.fromEntries(severities),
      timeRange: {
        start: logs.length > 0 ? Math.min(...logs.map(l => l.timestamp.getTime())) : 0,
        end: logs.length > 0 ? Math.max(...logs.map(l => l.timestamp.getTime())) : 0
      }
    };
  }

  /**
   * Analyse les logs pour trouver des découvertes
   */
  private analyzeLogsForFindings(logs: AuditLog[]): AuditFinding[] {
    const findings: AuditFinding[] = [];
    
    // Analyse des patterns suspects
    const failedLogins = logs.filter(l => 
      l.event.type === 'access' && l.event.result === 'failure'
    );
    
    if (failedLogins.length > 10) {
      findings.push({
        id: 'high-failed-logins',
        title: 'Nombre élevé de connexions échouées',
        description: `${failedLogins.length} tentatives de connexion échouées détectées`,
        severity: 'high',
        category: 'authentication',
        evidence: failedLogins.slice(0, 5).map(l => l.id)
      });
    }

    return findings;
  }

  /**
   * Génère des recommandations
   */
  private generateRecommendations(logs: AuditLog[]): AuditRecommendation[] {
    const recommendations: AuditRecommendation[] = [];
    
    // Recommandations basées sur l'analyse
    const securityIncidents = logs.filter(l => l.event.type === 'security-incident');
    
    if (securityIncidents.length > 0) {
      recommendations.push({
        id: 'review-security-incidents',
        title: 'Révision des incidents de sécurité',
        description: 'Réviser et analyser les incidents de sécurité récents',
        priority: 'high',
        category: 'security'
      });
    }

    return recommendations;
  }

  /**
   * Calcule les statistiques
   */
  private calculateStatistics(logs: AuditLog[]): AuditStatistics {
    return {
      totalEvents: logs.length,
      eventsPerDay: logs.length / 7, // Moyenne sur 7 jours
      topEventTypes: this.getTopEventTypes(logs),
      topUsers: this.getTopUsers(logs),
      riskScore: this.calculateRiskScore(logs)
    };
  }

  /**
   * Récupère les types d'événements les plus fréquents
   */
  private getTopEventTypes(logs: AuditLog[]): Array<{ type: string; count: number }> {
    const counts = new Map<string, number>();
    
    logs.forEach(log => {
      counts.set(log.event.type, (counts.get(log.event.type) || 0) + 1);
    });

    return Array.from(counts.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Récupère les utilisateurs les plus actifs
   */
  private getTopUsers(logs: AuditLog[]): Array<{ userId: string; count: number }> {
    const counts = new Map<string, number>();
    
    logs.forEach(log => {
      if (log.event.userId) {
        counts.set(log.event.userId, (counts.get(log.event.userId) || 0) + 1);
      }
    });

    return Array.from(counts.entries())
      .map(([userId, count]) => ({ userId, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Calcule le score de risque
   */
  private calculateRiskScore(logs: AuditLog[]): number {
    let score = 0;
    
    logs.forEach(log => {
      switch (log.severity) {
        case 'critical': score += 10; break;
        case 'high': score += 5; break;
        case 'medium': score += 2; break;
        case 'low': score += 1; break;
      }
    });

    return Math.min(100, score / logs.length * 10);
  }

  /**
   * Génère un ID d'audit
   */
  private generateAuditId(): string {
    return `audit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Génère un ID de rapport
   */
  private generateReportId(): string {
    return `report-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Méthodes de notification (à implémenter selon les besoins)
  private async escalateIncident(rule: AuditRule, auditLog: AuditLog): Promise<void> {
    // Implémentation de l'escalade d'incident
  }

  private async blockAction(rule: AuditRule, auditLog: AuditLog): Promise<void> {
    // Implémentation du blocage d'action
  }

  private async sendNotification(rule: AuditRule, auditLog: AuditLog): Promise<void> {
    // Implémentation de l'envoi de notification
  }

  /**
   * Arrêt du système d'audit
   */
  async shutdown(): Promise<void> {
    this.logger.info('🛑 Arrêt du Audit System...');
    this.isInitialized = false;
    this.isMonitoring = false;
  }
}

// Interfaces pour le système d'audit
interface AuditEvent {
  type: string;
  category: string;
  description: string;
  userId?: string;
  resource?: string;
  action?: string;
  result?: string;
  ipAddress?: string;
  userAgent?: string;
  source?: string;
  metadata?: any;
}

interface AuditLog {
  id: string;
  timestamp: Date;
  event: AuditEvent;
  source: string;
  severity: string;
  category: string;
  metadata: any;
}

interface AuditRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  conditions: {
    eventTypes?: string[];
    patterns?: string[];
    minSeverity?: string;
  };
  action: {
    type: 'alert' | 'escalate' | 'block' | 'notify';
    parameters: any;
  };
}

interface AlertThreshold {
  id: string;
  name: string;
  eventType: string;
  threshold: number;
  timeWindow: number; // seconds
  action: string;
}

interface AccessEvent {
  userId: string;
  resource: string;
  action: string;
  result: string;
  ipAddress: string;
  userAgent: string;
  metadata?: any;
}

interface ConfigurationEvent {
  userId: string;
  component: string;
  oldValue: any;
  newValue: any;
  reason: string;
}

interface SecurityIncidentEvent {
  incidentId: string;
  title: string;
  severity: string;
  category: string;
  affectedResource: string;
  detectionMethod: string;
}

type AuditReportType = 'daily' | 'weekly' | 'monthly' | 'custom' | 'compliance' | 'security';

interface AuditFilters {
  eventTypes?: string[];
  severity?: string;
  userId?: string;
}

interface AuditReport {
  id: string;
  type: AuditReportType;
  period: { start: Date; end: Date };
  generatedAt: Date;
  summary: AuditReportSummary;
  findings: AuditFinding[];
  recommendations: AuditRecommendation[];
  statistics: AuditStatistics;
  logs: AuditLog[];
  metadata: any;
}

interface AuditReportSummary {
  totalEvents: number;
  eventsByType: Record<string, number>;
  eventsBySeverity: Record<string, number>;
  timeRange: { start: number; end: number };
}

interface AuditFinding {
  id: string;
  title: string;
  description: string;
  severity: string;
  category: string;
  evidence: string[];
}

interface AuditRecommendation {
  id: string;
  title: string;
  description: string;
  priority: string;
  category: string;
}

interface AuditStatistics {
  totalEvents: number;
  eventsPerDay: number;
  topEventTypes: Array<{ type: string; count: number }>;
  topUsers: Array<{ userId: string; count: number }>;
  riskScore: number;
}

interface AuditSearchQuery {
  startDate?: Date;
  endDate?: Date;
  userId?: string;
  eventType?: string;
  category?: string;
  severity?: string;
  searchText?: string;
  limit?: number;
}
