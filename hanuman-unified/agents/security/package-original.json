{"name": "@retreat-and-be/agent-security", "version": "1.0.0", "description": "Agent Security - Advanced Security and Compliance for Distributed Nervous System", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "rm -rf dist coverage", "docker:build": "docker build -t agent-security .", "docker:run": "docker run -p 3007:3007 agent-security", "security:scan": "node scripts/security-scan.js", "compliance:check": "node scripts/compliance-check.js", "vulnerability:scan": "npm audit && snyk test", "penetration:test": "node scripts/penetration-test.js", "demo": "ts-node demo/security-demo.ts", "validate": "./scripts/run-security-validation.sh", "test:integration": "jest --testPathPattern=integration"}, "keywords": ["security", "compliance", "vulnerability-scanning", "penetration-testing", "threat-intelligence", "security-automation", "cybersecurity", "devsecops", "security-agent", "ai-security"], "author": "Retreat And Be", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "kafkajs": "^2.2.4", "ioredis": "^5.3.2", "weaviate-ts-client": "^1.5.0", "axios": "^1.6.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "crypto": "^1.0.1", "node-forge": "^1.3.1", "semver": "^7.5.4", "yaml": "^2.3.4", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "uuid": "^9.0.1", "moment": "^2.29.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "express-slow-down": "^2.0.1", "multer": "^1.4.5-lts.1", "archiver": "^6.0.1", "tar": "^6.2.0", "chalk": "^4.1.2", "ora": "^5.4.1", "boxen": "^5.1.2", "figlet": "^1.7.0", "gradient-string": "^2.0.2", "nanoid": "^3.3.7", "eventemitter3": "^5.0.1", "p-queue": "^6.6.2", "p-retry": "^4.6.2", "p-timeout": "^4.1.0", "async": "^3.2.5", "rxjs": "^7.8.1", "zod": "^3.22.4", "joi": "^17.11.0", "ajv": "^8.12.0", "semgrep": "^1.45.0", "snyk": "^1.1248.0", "retire": "^4.2.3", "eslint-plugin-security": "^1.7.1", "bandit": "^1.0.0", "safety": "^1.0.0", "gosec": "^2.18.2", "brakeman": "^6.0.1", "sonarjs": "^1.0.3", "owasp-dependency-check": "^8.4.2", "nmap": "^2.0.2", "nikto": "^2.5.0", "sqlmap": "^1.7.11", "burp-suite": "^2023.10.3", "zap-baseline": "^2.14.0", "nuclei": "^3.0.4", "trivy": "^0.47.0", "clair": "^4.7.1", "grype": "^0.73.4", "syft": "^0.95.0", "cosign": "^2.2.1", "sigstore": "^2.1.0", "notary": "^0.7.0", "falco": "^0.36.2", "osquery": "^5.10.2", "wazuh": "^4.6.0", "suricata": "^7.0.2", "zeek": "^6.0.3", "elastic-siem": "^8.11.0", "splunk": "^9.1.2", "mitre-attack": "^14.1.0", "cve-database": "^2023.11.0", "nvd-api": "^2.0.0", "virustotal": "^3.0.0", "shodan": "^1.28.0", "censys": "^2.2.5", "threatcrowd": "^1.0.0", "alienvault-otx": "^1.5.12", "ibm-xforce": "^1.0.0", "cisco-talos": "^1.0.0", "crowdstrike": "^1.0.0", "palo-alto-wildfire": "^1.0.0", "checkpoint-threat-cloud": "^1.0.0", "fortinet-fortiguard": "^1.0.0", "trend-micro-deep-security": "^1.0.0", "symantec-endpoint": "^1.0.0", "mcafee-epo": "^1.0.0", "kaspersky-security": "^1.0.0", "bitdefender-gravityzone": "^1.0.0", "sophos-central": "^1.0.0", "carbon-black": "^1.0.0", "cylance": "^1.0.0", "sentinel-one": "^1.0.0", "crowdstrike-falcon": "^1.0.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/multer": "^1.4.11", "@types/tar": "^6.1.10", "@types/js-yaml": "^4.0.9", "@types/figlet": "^1.5.8", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "tsx": "^4.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-plugin-security": "^1.7.1", "prettier": "^3.1.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/retreat-and-be/agent-security"}, "bugs": {"url": "https://github.com/retreat-and-be/agent-security/issues"}, "homepage": "https://github.com/retreat-and-be/agent-security#readme"}