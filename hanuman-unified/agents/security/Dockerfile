# Agent Security - Advanced Security & Compliance
# Multi-stage build pour optimiser la taille de l'image

# Stage 1: Build
FROM node:18-alpine AS builder

# Métadonnées
LABEL maintainer="Retreat And Be <<EMAIL>>"
LABEL description="Agent Security - Advanced Security & Compliance for Distributed Nervous System"
LABEL version="1.0.0"

# Installation des dépendances système pour la compilation
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl \
    wget

# Création du répertoire de travail
WORKDIR /app

# Copie des fichiers de configuration
COPY package*.json ./
COPY tsconfig.json ./

# Installation des dépendances
RUN npm ci --only=production && npm cache clean --force

# Copie du code source
COPY src/ ./src/

# Compilation TypeScript
RUN npm run build

# Stage 2: Security Tools Installation
FROM node:18-alpine AS security-tools

# Installation des outils de sécurité
RUN apk add --no-cache \
    # Outils système
    curl \
    wget \
    git \
    bash \
    jq \
    # Outils réseau
    nmap \
    netcat-openbsd \
    # Outils de développement
    python3 \
    py3-pip \
    openjdk11-jre \
    # Outils de sécurité
    openssl \
    ca-certificates

# Installation des outils de sécurité Python
RUN pip3 install --no-cache-dir \
    safety \
    bandit \
    semgrep \
    checkov

# Installation des outils Node.js de sécurité
RUN npm install -g \
    snyk \
    retire \
    eslint-plugin-security \
    audit-ci

# Installation de Trivy (scanner de vulnérabilités)
RUN wget -qO- https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh

# Installation de Grype (scanner de vulnérabilités)
RUN curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin

# Stage 3: Production
FROM node:18-alpine AS production

# Métadonnées finales
LABEL maintainer="Retreat And Be <<EMAIL>>"
LABEL description="Agent Security - Advanced Security & Compliance"
LABEL version="1.0.0"
LABEL org.opencontainers.image.title="Agent Security"
LABEL org.opencontainers.image.description="Advanced Security & Compliance Agent for Distributed Nervous System"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="Retreat And Be"
LABEL org.opencontainers.image.licenses="MIT"

# Installation des dépendances système minimales
RUN apk add --no-cache \
    curl \
    wget \
    bash \
    jq \
    python3 \
    py3-pip \
    openjdk11-jre \
    openssl \
    ca-certificates \
    nmap \
    netcat-openbsd \
    && rm -rf /var/cache/apk/*

# Copie des outils de sécurité depuis le stage précédent
COPY --from=security-tools /usr/local/bin/trivy /usr/local/bin/
COPY --from=security-tools /usr/local/bin/grype /usr/local/bin/
COPY --from=security-tools /usr/local/lib/python3.*/site-packages/ /usr/local/lib/python3.11/site-packages/
COPY --from=security-tools /usr/local/bin/safety /usr/local/bin/
COPY --from=security-tools /usr/local/bin/bandit /usr/local/bin/
COPY --from=security-tools /usr/local/bin/semgrep /usr/local/bin/

# Création d'un utilisateur non-root pour la sécurité
RUN addgroup -g 1001 -S agentsecurity && \
    adduser -S -D -H -u 1001 -s /sbin/nologin -G agentsecurity agentsecurity

# Création des répertoires nécessaires
WORKDIR /app

# Création des répertoires avec les bonnes permissions
RUN mkdir -p \
    /app/logs \
    /app/workspace \
    /app/reports \
    /app/temp \
    /app/config \
    /app/data \
    && chown -R agentsecurity:agentsecurity /app

# Copie des fichiers de l'application depuis le builder
COPY --from=builder --chown=agentsecurity:agentsecurity /app/node_modules ./node_modules
COPY --from=builder --chown=agentsecurity:agentsecurity /app/dist ./dist
COPY --from=builder --chown=agentsecurity:agentsecurity /app/package*.json ./

# Copie des fichiers de configuration
COPY --chown=agentsecurity:agentsecurity config/ ./config/
COPY --chown=agentsecurity:agentsecurity scripts/ ./scripts/

# Configuration des variables d'environnement
ENV NODE_ENV=production
ENV PORT=3007
ENV LOG_LEVEL=info
ENV AGENT_ID=agent-security-001

# Variables d'environnement pour les services externes
ENV KAFKA_BROKERS=kafka:9092
ENV REDIS_URL=redis://redis:6379
ENV WEAVIATE_URL=http://weaviate:8080
ENV CORTEX_CENTRAL_URL=http://cortex-central:8080

# Variables d'environnement pour les outils de sécurité
ENV TRIVY_CACHE_DIR=/app/temp/trivy
ENV GRYPE_DB_CACHE_DIR=/app/temp/grype
ENV SEMGREP_CACHE_DIR=/app/temp/semgrep

# Configuration des timeouts et limites
ENV REQUEST_TIMEOUT=30000
ENV SCAN_TIMEOUT=300000
ENV MAX_CONCURRENT_SCANS=3
ENV MAX_MEMORY_USAGE=2048

# Configuration de sécurité
ENV SECURITY_SCAN_DEPTH=comprehensive
ENV COMPLIANCE_FRAMEWORKS=owasp,cis,nist
ENV THREAT_INTELLIGENCE_ENABLED=true
ENV INCIDENT_RESPONSE_ENABLED=true

# Exposition du port
EXPOSE 3007

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3007/health || exit 1

# Volumes pour la persistance
VOLUME ["/app/logs", "/app/workspace", "/app/reports", "/app/data"]

# Changement vers l'utilisateur non-root
USER agentsecurity

# Script de démarrage
COPY --chown=agentsecurity:agentsecurity docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Point d'entrée
ENTRYPOINT ["docker-entrypoint.sh"]

# Commande par défaut
CMD ["node", "dist/index.js"]

# Métadonnées de build
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

LABEL org.opencontainers.image.created=$BUILD_DATE
LABEL org.opencontainers.image.revision=$VCS_REF
LABEL org.opencontainers.image.version=$VERSION
