import { Logger } from 'winston';
import { GeneratedFile, CodeGenerationRequest, ComprehensiveUXDesign, AdaptiveDesignSystem } from '../types';
/**
 * Générateur de Composants React/Vue
 *
 * Génère des composants UI optimisés basés sur le design system
 * et les spécifications d'accessibilité.
 */
export declare class ComponentGenerator {
    private logger;
    private framework;
    constructor(logger: Logger, framework: string);
    /**
     * Génère un composant de base
     */
    generateComponent(componentName: string, designSystem: AdaptiveDesignSystem, componentSpec: any, request: CodeGenerationRequest): Promise<GeneratedFile>;
    /**
     * Génère un composant spécialisé pour Retreat And Be
     */
    generateSpecializedComponent(componentName: string, design: ComprehensiveUXDesign, request: CodeGenerationRequest): Promise<GeneratedFile>;
    /**
     * Génère le contenu d'un composant de base
     */
    private generateComponentContent;
    /**
     * Génère un composant Button optimisé
     */
    private generateButtonComponent;
    /**
     * Génère un composant RetreatCard spécialisé
     */
    private generateRetreatCard;
    /**
     * Génère un composant BookingForm spécialisé
     */
    private generateBookingForm;
    private extractDependencies;
    private extractImports;
    private generateInputComponent;
    private generateCardComponent;
    private generateModalComponent;
    private generateNavigationComponent;
    private generateHeaderComponent;
    private generateFooterComponent;
    private generateLayoutComponent;
    private generateGenericComponentContent;
    private generateFilterPanel;
    private generateReviewComponent;
    private generatePartnerProfile;
    private generateSearchBar;
    private generatePricingCard;
    private generateTrustSignals;
    private generateGenericComponent;
}
//# sourceMappingURL=ComponentGenerator.d.ts.map