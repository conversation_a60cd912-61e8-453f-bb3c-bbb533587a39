{"version": 3, "file": "HookGenerator.js", "sourceRoot": "", "sources": ["../../src/generators/HookGenerator.ts"], "names": [], "mappings": ";;;AAOA;;;;;GAKG;AACH,MAAa,aAAa;IAIxB,YAAY,MAAc,EAAE,SAAiB;QAC3C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAChB,QAAgB,EAChB,MAA6B,EAC7B,OAA8B;QAE9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAElF,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QACnD,IAAI,OAAe,CAAC;QAEpB,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,SAAS;gBACZ,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,QAAQ;gBACX,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACrD,MAAM;YACR,KAAK,iBAAiB;gBACpB,OAAO,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,aAAa;gBAChB,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,yBAAyB;gBAC5B,OAAO,GAAG,MAAM,IAAI,CAAC,+BAA+B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACtE,MAAM;YACR,KAAK,kBAAkB;gBACrB,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC/D,MAAM;YACR,KAAK,YAAY;gBACf,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACzD,MAAM;YACR,KAAK,oBAAoB;gBACvB,OAAO,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACjE,MAAM;YACR;gBACE,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QACxE,CAAC;QAED,OAAO;YACL,IAAI,EAAE,aAAa,QAAQ,IAAI,SAAS,EAAE;YAC1C,OAAO;YACP,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY;YAC1D,IAAI,EAAE,OAAO,CAAC,MAAM;YACpB,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YAC/C,OAAO,EAAE,CAAC,QAAQ,CAAC;YACnB,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,MAA6B,EAAE,OAA8B;QACzF,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;QAExC,MAAM,eAAe,GAAG,YAAY,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsDzC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEJ,OAAO;;;EAGT,eAAe;;;mCAGkB,YAAY,CAAC,CAAC,CAAC,oCAAoC,CAAC,CAAC,CAAC,EAAE;;;2BAGhE,YAAY,CAAC,CAAC,CAAC,2CAA2C,CAAC,CAAC,CAAC,EAAE;8CAC5C,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAwCrC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,aAAa,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;gDAuBnE,YAAY,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDAuChC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;kDAkBvC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;gDAQhC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAwCtD,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,MAA6B,EAAE,OAA8B;QAClG,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;QAExC,MAAM,eAAe,GAAG,YAAY,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmDzC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEJ,OAAO;;;EAGT,eAAe;;;kDAGiC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;;;;;;;;;qCAShD,YAAY,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE;;;;WAIxE,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;aAC5B,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE;YACtC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;UAChC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDAgEW,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;WAyBtE,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;aAC5B,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE;UACxC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAqCP,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,MAA6B,EAAE,OAA8B;QACjG,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;QAExC,OAAO;;EAET,YAAY,CAAC,CAAC,CAAC;;CAEhB,CAAC,CAAC,CAAC,EAAE;;iCAE2B,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;OACnD,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gBACrB,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;GACtC,YAAY,CAAC,CAAC,CAAC,qCAAqC,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;kDAiBV,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;;;uCAGpC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCA6BtC,YAAY,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;kCAWtC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;gCAmBrC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,MAA6B,EAAE,OAA8B;QAC7F,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;QAExC,OAAO;;6BAEkB,YAAY,CAAC,CAAC,CAAC,qCAAqC,CAAC,CAAC,CAAC,EAAE;YAC1E,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;SAC5B,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;GACpC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;6BACC,YAAY,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAAE;;kDAExB,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;4BAY3D,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;;4BAG3B,CAAC;IAC3B,CAAC;IAED,uBAAuB;IACf,mBAAmB,CAAC,OAAe;QACzC,MAAM,WAAW,GAAG,gCAAgC,CAAC;QACrD,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjD,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IACpC,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,MAAM,WAAW,GAAG,sCAAsC,CAAC;QAC3D,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACb,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC9D,CAAC;iBAAM,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED,kEAAkE;IAC1D,KAAK,CAAC,cAAc,CAAC,MAA6B,EAAE,OAA8B;QACxF,OAAO,+BAA+B,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,+BAA+B,CAAC,MAA6B,EAAE,OAA8B;QACzG,OAAO,gDAAgD,CAAC;IAC1D,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,MAA6B,EAAE,OAA8B;QAC5F,OAAO,mCAAmC,CAAC;IAC7C,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,MAA6B,EAAE,OAA8B;QACpG,OAAO,2CAA2C,CAAC;IACrD,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,QAAgB,EAAE,MAA6B,EAAE,OAA8B;QAC/G,OAAO,cAAc,QAAQ,sBAAsB,CAAC;IACtD,CAAC;CACF;AA3rBD,sCA2rBC"}