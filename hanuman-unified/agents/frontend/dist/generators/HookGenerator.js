"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HookGenerator = void 0;
/**
 * Générateur de Hooks React
 *
 * Génère des hooks personnalisés optimisés pour la logique métier
 * et la gestion d'état de l'application.
 */
class HookGenerator {
    constructor(logger, framework) {
        this.logger = logger;
        this.framework = framework;
    }
    /**
     * Génère un hook personnalisé
     */
    async generateHook(hookName, design, request) {
        this.logger.info(`Génération du hook ${hookName}`, { framework: this.framework });
        const extension = request.typescript ? 'ts' : 'js';
        let content;
        switch (hookName) {
            case 'useAuth':
                content = await this.generateUseAuth(design, request);
                break;
            case 'useApi':
                content = await this.generateUseApi(design, request);
                break;
            case 'useLocalStorage':
                content = await this.generateUseLocalStorage(design, request);
                break;
            case 'useDebounce':
                content = await this.generateUseDebounce(design, request);
                break;
            case 'useIntersectionObserver':
                content = await this.generateUseIntersectionObserver(design, request);
                break;
            case 'useRetreatSearch':
                content = await this.generateUseRetreatSearch(design, request);
                break;
            case 'useBooking':
                content = await this.generateUseBooking(design, request);
                break;
            case 'useUserPreferences':
                content = await this.generateUseUserPreferences(design, request);
                break;
            default:
                content = await this.generateGenericHook(hookName, design, request);
        }
        return {
            path: `src/hooks/${hookName}.${extension}`,
            content,
            type: 'hook',
            language: request.typescript ? 'typescript' : 'javascript',
            size: content.length,
            dependencies: this.extractDependencies(content),
            exports: [hookName],
            imports: this.extractImports(content)
        };
    }
    /**
     * Génère le hook useAuth pour l'authentification
     */
    async generateUseAuth(design, request) {
        const isTypeScript = request.typescript;
        const typeDefinitions = isTypeScript ? `
interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  preferences: UserPreferences;
  subscription?: Subscription;
}

interface UserPreferences {
  language: string;
  currency: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy: {
    profileVisible: boolean;
    shareData: boolean;
  };
}

interface Subscription {
  plan: 'free' | 'premium' | 'pro';
  expiresAt: string;
  features: string[];
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
}` : '';
        return `import { useState, useEffect, useCallback, useContext, createContext } from 'react';
import { authService } from '../services/authService';
import { useLocalStorage } from './useLocalStorage';
${typeDefinitions}

// Context pour l'authentification
const AuthContext = createContext${isTypeScript ? '<(AuthState & AuthActions) | null>' : ''}(null);

// Provider d'authentification
export const AuthProvider${isTypeScript ? ': React.FC<{ children: React.ReactNode }>' : ''} = ({ children }) => {
  const [authState, setAuthState] = useState${isTypeScript ? '<AuthState>' : ''}({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null
  });

  const [storedToken, setStoredToken] = useLocalStorage('auth_token', null);

  // Initialisation de l'authentification
  useEffect(() => {
    const initAuth = async () => {
      if (storedToken) {
        try {
          const user = await authService.getCurrentUser();
          setAuthState({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        } catch (error) {
          // Token invalide, le supprimer
          setStoredToken(null);
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
        }
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    };

    initAuth();
  }, [storedToken, setStoredToken]);

  // Actions d'authentification
  const login = useCallback(async (email${isTypeScript ? ': string' : ''}, password${isTypeScript ? ': string' : ''}) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const { user, token } = await authService.login(email, password);
      setStoredToken(token);
      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });
    } catch (error) {
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error.message || 'Erreur de connexion'
      });
      throw error;
    }
  }, [setStoredToken]);

  const register = useCallback(async (userData${isTypeScript ? ': RegisterData' : ''}) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const { user, token } = await authService.register(userData);
      setStoredToken(token);
      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });
    } catch (error) {
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error.message || 'Erreur d\\'inscription'
      });
      throw error;
    }
  }, [setStoredToken]);

  const logout = useCallback(async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    } finally {
      setStoredToken(null);
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      });
    }
  }, [setStoredToken]);

  const updateProfile = useCallback(async (updates${isTypeScript ? ': Partial<User>' : ''}) => {
    if (!authState.user) return;

    try {
      const updatedUser = await authService.updateProfile(updates);
      setAuthState(prev => ({
        ...prev,
        user: updatedUser
      }));
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        error: error.message || 'Erreur de mise à jour'
      }));
      throw error;
    }
  }, [authState.user]);

  const resetPassword = useCallback(async (email${isTypeScript ? ': string' : ''}) => {
    try {
      await authService.resetPassword(email);
    } catch (error) {
      throw error;
    }
  }, []);

  const verifyEmail = useCallback(async (token${isTypeScript ? ': string' : ''}) => {
    try {
      await authService.verifyEmail(token);
      if (authState.user) {
        setAuthState(prev => ({
          ...prev,
          user: { ...prev.user!, emailVerified: true }
        }));
      }
    } catch (error) {
      throw error;
    }
  }, [authState.user]);

  const value = {
    ...authState,
    login,
    register,
    logout,
    updateProfile,
    resetPassword,
    verifyEmail
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook principal d'authentification
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default useAuth;`;
    }
    /**
     * Génère le hook useRetreatSearch pour la recherche de retraites
     */
    async generateUseRetreatSearch(design, request) {
        const isTypeScript = request.typescript;
        const typeDefinitions = isTypeScript ? `
interface SearchFilters {
  location?: string;
  priceRange?: [number, number];
  duration?: number[];
  type?: string[];
  rating?: number;
  dates?: [string, string];
  amenities?: string[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
}

interface Retreat {
  id: string;
  title: string;
  description: string;
  location: string;
  price: number;
  duration: number;
  rating: number;
  reviewCount: number;
  images: string[];
  tags: string[];
  partner: Partner;
  availability: Availability;
  amenities: string[];
  difficulty: string;
}

interface Partner {
  id: string;
  name: string;
  verified: boolean;
  rating: number;
  description: string;
}

interface Availability {
  startDate: string;
  endDate: string;
  spotsLeft: number;
  totalSpots: number;
}

interface SearchState {
  results: Retreat[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  hasMore: boolean;
  page: number;
}` : '';
        return `import { useState, useCallback, useRef } from 'react';
import { retreatService } from '../services/retreatService';
import { useDebounce } from './useDebounce';
${typeDefinitions}

export const useRetreatSearch = () => {
  const [searchState, setSearchState] = useState${isTypeScript ? '<SearchState>' : ''}({
    results: [],
    loading: false,
    error: null,
    totalCount: 0,
    hasMore: false,
    page: 1
  });

  const abortControllerRef = useRef${isTypeScript ? '<AbortController | null>' : ''}(null);

  // Recherche avec debounce pour éviter trop de requêtes
  const searchRetreats = useCallback(async (
    query${isTypeScript ? ': string' : ''}, 
    filters${isTypeScript ? ': SearchFilters' : ''} = {}, 
    sortBy${isTypeScript ? ': string' : ''} = 'relevance',
    page${isTypeScript ? ': number' : ''} = 1
  ) => {
    // Annuler la requête précédente si elle existe
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Créer un nouveau contrôleur d'annulation
    abortControllerRef.current = new AbortController();

    setSearchState(prev => ({
      ...prev,
      loading: true,
      error: null,
      ...(page === 1 && { results: [] }) // Reset results si nouvelle recherche
    }));

    try {
      const response = await retreatService.search({
        query,
        filters,
        sortBy,
        page,
        limit: 12
      }, abortControllerRef.current.signal);

      setSearchState(prev => ({
        ...prev,
        results: page === 1 ? response.results : [...prev.results, ...response.results],
        totalCount: response.totalCount,
        hasMore: response.hasMore,
        page: response.page,
        loading: false,
        error: null
      }));

      return response;
    } catch (error) {
      if (error.name !== 'AbortError') {
        setSearchState(prev => ({
          ...prev,
          loading: false,
          error: error.message || 'Erreur lors de la recherche'
        }));
      }
      throw error;
    }
  }, []);

  // Recherche avec debounce
  const debouncedSearch = useDebounce(searchRetreats, 300);

  // Charger plus de résultats
  const loadMore = useCallback(async () => {
    if (searchState.loading || !searchState.hasMore) return;

    try {
      await searchRetreats('', {}, 'relevance', searchState.page + 1);
    } catch (error) {
      console.error('Erreur lors du chargement de plus de résultats:', error);
    }
  }, [searchState.loading, searchState.hasMore, searchState.page, searchRetreats]);

  // Obtenir les suggestions de recherche
  const getSuggestions = useCallback(async (query${isTypeScript ? ': string' : ''}) => {
    if (!query.trim()) return [];

    try {
      const suggestions = await retreatService.getSuggestions(query);
      return suggestions;
    } catch (error) {
      console.error('Erreur lors de la récupération des suggestions:', error);
      return [];
    }
  }, []);

  // Obtenir les filtres populaires
  const getPopularFilters = useCallback(async () => {
    try {
      const filters = await retreatService.getPopularFilters();
      return filters;
    } catch (error) {
      console.error('Erreur lors de la récupération des filtres populaires:', error);
      return {};
    }
  }, []);

  // Sauvegarder une recherche
  const saveSearch = useCallback(async (
    query${isTypeScript ? ': string' : ''}, 
    filters${isTypeScript ? ': SearchFilters' : ''}, 
    name${isTypeScript ? ': string' : ''}
  ) => {
    try {
      await retreatService.saveSearch({ query, filters, name });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la recherche:', error);
      throw error;
    }
  }, []);

  // Nettoyer les ressources
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  return {
    // État
    results: searchState.results,
    loading: searchState.loading,
    error: searchState.error,
    totalCount: searchState.totalCount,
    hasMore: searchState.hasMore,
    page: searchState.page,

    // Actions
    searchRetreats,
    debouncedSearch,
    loadMore,
    getSuggestions,
    getPopularFilters,
    saveSearch,
    cleanup
  };
};

export default useRetreatSearch;`;
    }
    /**
     * Génère le hook useLocalStorage
     */
    async generateUseLocalStorage(design, request) {
        const isTypeScript = request.typescript;
        return `import { useState, useEffect, useCallback } from 'react';

${isTypeScript ? `
type SetValue<T> = T | ((val: T) => T);
` : ''}

export const useLocalStorage = ${isTypeScript ? '<T>' : ''}(
  key${isTypeScript ? ': string' : ''}, 
  initialValue${isTypeScript ? ': T' : ''}
)${isTypeScript ? ': [T, (value: SetValue<T>) => void]' : ''} => {
  // Fonction pour lire la valeur depuis localStorage
  const readValue = useCallback(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(\`Error reading localStorage key "\${key}":\`, error);
      return initialValue;
    }
  }, [initialValue, key]);

  // État avec lazy initial value
  const [storedValue, setStoredValue] = useState${isTypeScript ? '<T>' : ''}(readValue);

  // Fonction pour définir la valeur
  const setValue = useCallback((value${isTypeScript ? ': SetValue<T>' : ''}) => {
    if (typeof window === 'undefined') {
      console.warn('localStorage is not available in this environment');
      return;
    }

    try {
      // Permettre à la valeur d'être une fonction pour avoir la même API que useState
      const newValue = value instanceof Function ? value(storedValue) : value;

      // Sauvegarder dans localStorage
      window.localStorage.setItem(key, JSON.stringify(newValue));

      // Mettre à jour l'état
      setStoredValue(newValue);

      // Déclencher un événement personnalisé pour synchroniser entre onglets
      window.dispatchEvent(
        new CustomEvent('local-storage', {
          detail: { key, newValue }
        })
      );
    } catch (error) {
      console.warn(\`Error setting localStorage key "\${key}":\`, error);
    }
  }, [key, storedValue]);

  // Écouter les changements dans localStorage (pour la synchronisation entre onglets)
  useEffect(() => {
    const handleStorageChange = (e${isTypeScript ? ': StorageEvent' : ''}) => {
      if (e.key !== key) return;

      try {
        const newValue = e.newValue ? JSON.parse(e.newValue) : initialValue;
        setStoredValue(newValue);
      } catch (error) {
        console.warn(\`Error parsing localStorage value for key "\${key}":\`, error);
      }
    };

    const handleCustomEvent = (e${isTypeScript ? ': CustomEvent' : ''}) => {
      if (e.detail.key === key) {
        setStoredValue(e.detail.newValue);
      }
    };

    // Écouter les événements de storage natifs et personnalisés
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('local-storage', handleCustomEvent);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('local-storage', handleCustomEvent);
    };
  }, [key, initialValue]);

  return [storedValue, setValue];
};

export default useLocalStorage;`;
    }
    /**
     * Génère le hook useDebounce
     */
    async generateUseDebounce(design, request) {
        const isTypeScript = request.typescript;
        return `import { useCallback, useRef } from 'react';

export const useDebounce = ${isTypeScript ? '<T extends (...args: any[]) => any>' : ''}(
  callback${isTypeScript ? ': T' : ''}, 
  delay${isTypeScript ? ': number' : ''}
)${isTypeScript ? ': T' : ''} => {
  const timeoutRef = useRef${isTypeScript ? '<NodeJS.Timeout | null>' : ''}(null);

  const debouncedCallback = useCallback((...args${isTypeScript ? ': Parameters<T>' : ''}) => {
    // Annuler le timeout précédent
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Créer un nouveau timeout
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);

  return debouncedCallback${isTypeScript ? ' as T' : ''};
};

export default useDebounce;`;
    }
    // Méthodes utilitaires
    extractDependencies(content) {
        const importRegex = /import.*from ['"]([^'"]+)['"]/g;
        const dependencies = [];
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            const dep = match[1];
            if (!dep.startsWith('.') && !dep.startsWith('/')) {
                dependencies.push(dep);
            }
        }
        return [...new Set(dependencies)];
    }
    extractImports(content) {
        const importRegex = /import\s+(?:{([^}]+)}|(\w+))\s+from/g;
        const imports = [];
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            if (match[1]) {
                imports.push(...match[1].split(',').map(imp => imp.trim()));
            }
            else if (match[2]) {
                imports.push(match[2]);
            }
        }
        return [...new Set(imports)];
    }
    // Méthodes de génération simplifiées (à implémenter complètement)
    async generateUseApi(design, request) {
        return `// useApi hook implementation`;
    }
    async generateUseIntersectionObserver(design, request) {
        return `// useIntersectionObserver hook implementation`;
    }
    async generateUseBooking(design, request) {
        return `// useBooking hook implementation`;
    }
    async generateUseUserPreferences(design, request) {
        return `// useUserPreferences hook implementation`;
    }
    async generateGenericHook(hookName, design, request) {
        return `// Generic ${hookName} hook implementation`;
    }
}
exports.HookGenerator = HookGenerator;
//# sourceMappingURL=HookGenerator.js.map