import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { GeneratedCode, ValidationResult, ComprehensiveUXDesign } from '../types';
/**
 * Système de Communication Kafka pour l'Agent Frontend
 *
 * Gère la communication synaptique avec les autres agents
 * du système nerveux distribué via Kafka.
 */
export declare class KafkaCommunication extends EventEmitter {
    private logger;
    private kafka;
    private producer;
    private consumer;
    private isConnected;
    private agentId;
    private readonly topics;
    constructor(logger: Logger, kafkaBrokers?: string, agentId?: string);
    /**
     * Crée le client Kafka (simulation)
     */
    private createKafkaClient;
    /**
     * Initialise la connexion Kafka
     */
    private initializeConnection;
    /**
     * Gère les messages entrants
     */
    private handleIncomingMessage;
    /**
     * Notifie qu'un code a été généré
     */
    notifyCodeGenerated(generatedCode: GeneratedCode, originalDesign: ComprehensiveUXDesign, correlationId?: string): Promise<void>;
    /**
     * Envoie un feedback d'implémentation à l'Agent UI/UX
     */
    sendImplementationFeedback(validationResult: ValidationResult, originalDesign: ComprehensiveUXDesign, correlationId?: string): Promise<void>;
    /**
     * Notifie la completion d'une validation
     */
    notifyValidationComplete(validationResult: ValidationResult, correlationId?: string): Promise<void>;
    /**
     * Notifie la completion d'un déploiement
     */
    notifyDeploymentComplete(deploymentResult: any, correlationId?: string): Promise<void>;
    /**
     * Envoie une réponse à un message
     */
    sendResponse(correlationId: string, payload: any): Promise<void>;
    /**
     * Envoie une erreur en réponse à un message
     */
    sendError(correlationId: string, errorMessage: string): Promise<void>;
    /**
     * Envoie le statut de l'agent
     */
    sendAgentStatus(status: 'online' | 'offline' | 'busy' | 'error'): Promise<void>;
    /**
     * Envoie les métriques de l'agent
     */
    sendAgentMetrics(metrics: any): Promise<void>;
    /**
     * Envoie un message via Kafka
     */
    private sendMessage;
    /**
     * Simulation de messages entrants pour les tests
     */
    private simulateIncomingMessages;
    /**
     * Génère des suggestions d'amélioration
     */
    private generateImprovementSuggestions;
    /**
     * Génère un ID de message unique
     */
    private generateMessageId;
    /**
     * Vérifie l'état de la connexion
     */
    isConnected(): boolean;
    /**
     * Ferme les connexions Kafka
     */
    disconnect(): Promise<void>;
}
//# sourceMappingURL=KafkaCommunication.d.ts.map