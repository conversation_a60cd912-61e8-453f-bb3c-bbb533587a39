import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import winston from 'winston';
import dotenv from 'dotenv';
import { FrontendAgent } from './core/FrontendAgent';
import { WeaviateMemory } from './memory/WeaviateMemory';
import { KafkaCommunication } from './communication/KafkaCommunication';
import { AgentConfig } from './types';

// Charger les variables d'environnement
dotenv.config();

// Configuration du logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'agent-frontend' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Configuration de l'agent
const agentConfig: AgentConfig = {
  id: process.env.AGENT_ID || 'agent-frontend-001',
  name: 'Agent Frontend Code Generator',
  type: 'frontend',
  version: '1.0.0',
  capabilities: [
    'react-generation',
    'vue-generation',
    'code-validation',
    'performance-optimization',
    'accessibility-validation',
    'component-library-creation',
    'responsive-design',
    'seo-optimization'
  ],
  endpoints: {
    health: '/health',
    ready: '/ready',
    info: '/api/info',
    generate: '/api/generate',
    validate: '/api/validate',
    improve: '/api/improve',
    deploy: '/api/deploy'
  },
  memory: {
    store: 'weaviate',
    collections: [
      'GeneratedCode',
      'Template',
      'CodePattern',
      'ComponentLibrary',
      'OptimizationRule',
      'ValidationRule'
    ]
  },
  communication: {
    kafka: {
      topics: [
        'agent.frontend.code.generated',
        'agent.frontend.validation.complete',
        'agent.frontend.implementation.feedback',
        'agent.frontend.deployment.complete'
      ],
      groupId: 'agent-frontend-group'
    },
    redis: {
      channels: [
        'frontend:notifications',
        'frontend:requests'
      ]
    }
  },
  codeGeneration: {
    supportedFrameworks: ['react', 'vue', 'angular', 'svelte'],
    defaultFramework: 'react',
    outputDirectory: './generated',
    templatesDirectory: './templates'
  }
};

// Initialisation de l'application Express
const app = express();
const port = process.env.PORT || 3006;

// Middleware de sécurité
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  }
}));

// Middleware CORS
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Middleware de parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Middleware de logging des requêtes
app.use((req, res, next) => {
  logger.info('Requête reçue', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Variables globales pour les services
let frontendAgent: FrontendAgent;
let memory: WeaviateMemory;
let communication: KafkaCommunication;

/**
 * Initialisation des services
 */
async function initializeServices(): Promise<void> {
  try {
    logger.info('Initialisation des services de l\'agent frontend');

    // Initialiser la mémoire Weaviate
    memory = new WeaviateMemory(
      logger,
      process.env.WEAVIATE_URL || 'http://weaviate:8080'
    );

    // Initialiser la communication Kafka
    communication = new KafkaCommunication(
      logger,
      process.env.KAFKA_BROKERS || 'kafka:9092',
      agentConfig.id
    );

    // Initialiser l'agent frontend
    frontendAgent = new FrontendAgent(
      agentConfig,
      logger,
      memory,
      communication
    );

    logger.info('Services initialisés avec succès');

  } catch (error) {
    logger.error('Erreur lors de l\'initialisation des services', { error: error.message });
    throw error;
  }
}

// Routes de santé et statut

/**
 * Endpoint de santé
 */
app.get('/health', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    agent: {
      id: agentConfig.id,
      name: agentConfig.name,
      version: agentConfig.version
    },
    services: {
      memory: memory?.isConnected() || false,
      communication: communication?.isConnected() || false
    },
    uptime: process.uptime(),
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
    }
  };

  const statusCode = health.services.memory && health.services.communication ? 200 : 503;
  res.status(statusCode).json(health);
});

/**
 * Endpoint de disponibilité
 */
app.get('/ready', (req, res) => {
  const ready = frontendAgent && memory?.isConnected() && communication?.isConnected();
  res.status(ready ? 200 : 503).json({
    ready,
    timestamp: new Date().toISOString()
  });
});

/**
 * Informations sur l'agent
 */
app.get('/api/info', (req, res) => {
  res.json({
    agent: agentConfig,
    capabilities: agentConfig.capabilities,
    endpoints: agentConfig.endpoints,
    supportedFrameworks: agentConfig.codeGeneration.supportedFrameworks,
    version: agentConfig.version,
    timestamp: new Date().toISOString()
  });
});

// Routes API principales

/**
 * Génération de code à partir d'un design
 */
app.post('/api/generate', async (req, res) => {
  try {
    const { design, request } = req.body;

    if (!design || !request) {
      return res.status(400).json({
        error: 'Design et request sont requis',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de génération de code', { 
      framework: request.framework,
      features: request.features 
    });

    const generatedCode = await frontendAgent.generateCodeFromDesign(design, request);

    res.json({
      success: true,
      generatedCode,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la génération de code', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la génération de code',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Validation d'une implémentation
 */
app.post('/api/validate', async (req, res) => {
  try {
    const { code, design, framework } = req.body;

    if (!code || !design || !framework) {
      return res.status(400).json({
        error: 'Code, design et framework sont requis',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de validation', { framework });

    const validationResult = await frontendAgent.validateImplementation(code, design, framework);

    res.json({
      success: true,
      validationResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la validation', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la validation',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Génération d'améliorations
 */
app.post('/api/improve', async (req, res) => {
  try {
    const { code, design, framework } = req.body;

    if (!code || !design || !framework) {
      return res.status(400).json({
        error: 'Code, design et framework sont requis',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande d\'amélioration', { framework });

    const improvedCode = await frontendAgent.generateImprovements(code, design, framework);

    res.json({
      success: true,
      improvedCode,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la génération d\'améliorations', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la génération d\'améliorations',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Déploiement automatique
 */
app.post('/api/deploy', async (req, res) => {
  try {
    const { generatedCode, deploymentConfig } = req.body;

    if (!generatedCode || !deploymentConfig) {
      return res.status(400).json({
        error: 'GeneratedCode et deploymentConfig sont requis',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de déploiement', { 
      platform: deploymentConfig.platform,
      environment: deploymentConfig.environment 
    });

    const deploymentResult = await frontendAgent.deployCode(generatedCode, deploymentConfig);

    res.json({
      success: true,
      deploymentResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors du déploiement', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors du déploiement',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Middleware de gestion des erreurs
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Erreur non gérée', { 
    error: error.message, 
    stack: error.stack,
    url: req.url,
    method: req.method 
  });

  res.status(500).json({
    error: 'Erreur interne du serveur',
    timestamp: new Date().toISOString()
  });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint non trouvé',
    timestamp: new Date().toISOString()
  });
});

/**
 * Démarrage du serveur
 */
async function startServer(): Promise<void> {
  try {
    // Initialiser les services
    await initializeServices();

    // Démarrer le serveur HTTP
    const server = app.listen(port, () => {
      logger.info(`Agent Frontend démarré sur le port ${port}`, {
        agentId: agentConfig.id,
        version: agentConfig.version,
        environment: process.env.NODE_ENV || 'development'
      });
    });

    // Gestion gracieuse de l'arrêt
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Signal ${signal} reçu, arrêt gracieux en cours...`);
      
      server.close(async () => {
        try {
          if (communication) {
            await communication.disconnect();
          }
          if (memory) {
            await memory.close();
          }
          logger.info('Arrêt gracieux terminé');
          process.exit(0);
        } catch (error) {
          logger.error('Erreur lors de l\'arrêt gracieux', { error: error.message });
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Erreur lors du démarrage du serveur', { error: error.message });
    process.exit(1);
  }
}

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  logger.error('Exception non capturée', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Promesse rejetée non gérée', { reason, promise });
  process.exit(1);
});

// Démarrer l'application
startServer().catch((error) => {
  logger.error('Erreur fatale lors du démarrage', { error: error.message });
  process.exit(1);
});

export { app, frontendAgent, memory, communication };
