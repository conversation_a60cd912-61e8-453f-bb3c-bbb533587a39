# 🚀 Agent Frontend - Générateur de Code Automatique

L'**Agent Frontend** est un agent IA spécialisé dans la génération automatique de code frontend (React, Vue, Angular) à partir de designs UI/UX. Il fait partie de l'architecture "Living AI Organism" de Retreat And Be.

## 🎯 Fonctionnalités Principales

### 🔧 Génération de Code
- **React/Vue/Angular** : Support complet des frameworks modernes
- **TypeScript/JavaScript** : Génération avec ou sans TypeScript
- **Responsive Design** : Code adaptatif automatique
- **Accessibilité** : Conformité WCAG AA/AAA
- **Performance** : Optimisations automatiques (lazy loading, code splitting)
- **SEO** : Meta tags et données structurées

### 🎨 Intégration Design System
- **Couleurs** : Application automatique de la palette
- **Typographie** : Respect des règles typographiques
- **Espacement** : Utilisation de l'échelle définie
- **Composants** : Génération de bibliothèques de composants

### ✅ Validation et Optimisation
- **Validation syntaxique** : Vérification du code généré
- **Tests automatiques** : Génération de tests unitaires
- **Optimisation bundle** : Réduction de la taille
- **Audit accessibilité** : Vérification WCAG

### 🔄 Communication Synaptique
- **Kafka** : Communication avec les autres agents
- **Weaviate** : Mémoire vectorielle pour l'apprentissage
- **Redis** : Cache et sessions

## 🏗️ Architecture

```
agents/frontend/
├── src/
│   ├── core/                 # Agent principal
│   │   └── FrontendAgent.ts
│   ├── generators/           # Générateurs spécialisés
│   │   ├── ReactGenerator.ts
│   │   ├── VueGenerator.ts
│   │   ├── ComponentGenerator.ts
│   │   ├── PageGenerator.ts
│   │   ├── HookGenerator.ts
│   │   └── ServiceGenerator.ts
│   ├── validators/           # Validation du code
│   │   └── CodeValidator.ts
│   ├── optimization/         # Optimisation
│   │   └── OptimizationEngine.ts
│   ├── memory/              # Système de mémoire
│   │   └── WeaviateMemory.ts
│   ├── communication/       # Communication
│   │   └── KafkaCommunication.ts
│   ├── templates/           # Templates de code
│   ├── types/               # Types TypeScript
│   └── index.ts            # Point d'entrée
├── templates/               # Templates réutilisables
├── generated/              # Code généré
├── logs/                   # Logs de l'agent
├── monitoring/             # Configuration monitoring
├── Dockerfile
├── docker-compose.yml
└── package.json
```

## 🚀 Installation et Démarrage

### Prérequis
- Node.js 18+
- Docker et Docker Compose
- Git

### Installation

```bash
# Cloner le repository
git clone <repository-url>
cd agents/frontend

# Installer les dépendances
npm install

# Copier la configuration
cp .env.example .env

# Configurer les variables d'environnement
nano .env
```

### Démarrage avec Docker

```bash
# Démarrage complet (production)
docker-compose up -d

# Démarrage en mode développement
docker-compose --profile dev up -d

# Vérifier les services
docker-compose ps
```

### Démarrage en développement

```bash
# Démarrer les services externes
docker-compose up -d weaviate kafka redis

# Démarrer l'agent en mode dev
npm run dev

# Ou avec hot reload
npm run dev:watch
```

## 📡 API Endpoints

### Santé et Statut
- `GET /health` - État de santé de l'agent
- `GET /ready` - Disponibilité de l'agent
- `GET /api/info` - Informations sur l'agent

### Génération de Code
```bash
# Générer du code à partir d'un design
POST /api/generate
{
  "design": {
    "designSystem": { ... },
    "wireframes": { ... },
    "componentLibrary": { ... }
  },
  "request": {
    "framework": "react",
    "typescript": true,
    "styling": "tailwind",
    "features": {
      "routing": true,
      "responsive": true,
      "accessibility": true
    }
  }
}
```

### Validation
```bash
# Valider une implémentation
POST /api/validate
{
  "code": "...",
  "design": { ... },
  "framework": "react"
}
```

### Amélioration
```bash
# Générer des améliorations
POST /api/improve
{
  "code": "...",
  "design": { ... },
  "framework": "react"
}
```

### Déploiement
```bash
# Déployer automatiquement
POST /api/deploy
{
  "generatedCode": { ... },
  "deploymentConfig": {
    "platform": "vercel",
    "environment": "production"
  }
}
```

## 🔧 Configuration

### Variables d'Environnement

```bash
# Agent Identity
AGENT_ID=agent-frontend-001
AGENT_NAME="Agent Frontend Code Generator"

# Services
WEAVIATE_URL=http://weaviate:8080
KAFKA_BROKERS=kafka:9092
REDIS_URL=redis://redis:6379

# OpenAI (pour l'IA générative)
OPENAI_API_KEY=your_key_here

# Code Generation
DEFAULT_FRAMEWORK=react
OUTPUT_DIRECTORY=./generated
```

### Frameworks Supportés

- **React** : 18.x avec hooks, TypeScript, JSX
- **Vue** : 3.x avec Composition API, TypeScript
- **Angular** : 15+ avec TypeScript
- **Svelte** : 4.x avec TypeScript

### Outils de Styling

- **Tailwind CSS** : Classes utilitaires
- **Styled Components** : CSS-in-JS
- **SCSS/Sass** : Préprocesseur CSS
- **CSS Modules** : CSS scopé

## 🧪 Tests

```bash
# Tests unitaires
npm test

# Tests d'intégration
npm run test:integration

# Tests e2e
npm run test:e2e

# Coverage
npm run test:coverage
```

## 📊 Monitoring

### Métriques Disponibles
- **Performances** : Temps de génération, taille des bundles
- **Qualité** : Scores d'accessibilité, performance, SEO
- **Utilisation** : Frameworks utilisés, fonctionnalités demandées
- **Erreurs** : Taux d'erreur, types d'erreurs

### Dashboards
- **Grafana** : http://localhost:3001 (admin/admin)
- **Kafka UI** : http://localhost:8081
- **Prometheus** : http://localhost:9090

## 🔄 Communication avec les Autres Agents

### Topics Kafka

**Écoute :**
- `agent.uiux.design.complete` - Designs reçus de l'Agent UI/UX
- `agent.frontend.validation.request` - Demandes de validation
- `agent.frontend.improvement.request` - Demandes d'amélioration

**Publication :**
- `agent.frontend.code.generated` - Code généré
- `agent.frontend.validation.complete` - Validation terminée
- `agent.frontend.implementation.feedback` - Feedback d'implémentation

### Intégration avec l'Agent UI/UX

L'Agent Frontend reçoit les designs complets de l'Agent UI/UX et génère automatiquement :

1. **Structure du projet** : Arborescence et configuration
2. **Composants UI** : Basés sur le design system
3. **Pages** : Selon les wireframes
4. **Hooks personnalisés** : Logique métier
5. **Services** : Intégration API
6. **Tests** : Couverture automatique

## 🛠️ Développement

### Ajouter un Nouveau Framework

1. Créer un générateur dans `src/generators/`
2. Implémenter l'interface `FrameworkGenerator`
3. Ajouter les templates dans `templates/`
4. Mettre à jour la configuration

### Ajouter des Optimisations

1. Étendre `OptimizationEngine`
2. Ajouter les règles de validation
3. Créer les tests correspondants

### Contribuer

1. Fork le repository
2. Créer une branche feature
3. Implémenter les changements
4. Ajouter les tests
5. Créer une Pull Request

## 📚 Documentation Technique

### Types TypeScript
Tous les types sont définis dans `src/types/index.ts` :
- `GeneratedCode` : Structure du code généré
- `CodeGenerationRequest` : Paramètres de génération
- `ValidationResult` : Résultats de validation
- `ComprehensiveUXDesign` : Design reçu de l'Agent UI/UX

### Patterns de Code
L'agent utilise des patterns réutilisables stockés en mémoire vectorielle pour améliorer la qualité et la cohérence du code généré.

### Apprentissage Continu
L'agent apprend de chaque génération pour améliorer ses performances futures grâce à la mémoire Weaviate.

## 🔒 Sécurité

- **Validation d'entrée** : Sanitisation des données
- **Secrets** : Variables d'environnement sécurisées
- **CORS** : Configuration restrictive
- **Rate Limiting** : Protection contre les abus
- **Audit de sécurité** : Vérification du code généré

## 📈 Performance

- **Génération parallèle** : Traitement concurrent
- **Cache intelligent** : Réutilisation des patterns
- **Optimisation bundle** : Code splitting automatique
- **Lazy loading** : Chargement à la demande

## 🆘 Dépannage

### Problèmes Courants

**Agent ne démarre pas :**
```bash
# Vérifier les logs
docker-compose logs agent-frontend

# Vérifier les services
docker-compose ps
```

**Erreurs de génération :**
```bash
# Vérifier la mémoire Weaviate
curl http://localhost:8080/v1/meta

# Vérifier Kafka
docker-compose logs kafka
```

**Performance lente :**
- Vérifier la charge CPU/mémoire
- Optimiser les templates
- Ajuster la configuration de cache

## 📞 Support

- **Issues** : GitHub Issues
- **Documentation** : Wiki du projet
- **Chat** : Discord/Slack de l'équipe
- **Email** : <EMAIL>

---

🤖 **Agent Frontend** - Partie intégrante du système nerveux distribué de Retreat And Be
