import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import winston from 'winston';
import dotenv from 'dotenv';
import { QAAgent } from './core/QAAgent';
import { WeaviateMemory } from './memory/WeaviateMemory';
import { KafkaCommunication } from './communication/KafkaCommunication';
import { AgentConfig } from './types';

// Charger les variables d'environnement
dotenv.config();

// Configuration du logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'agent-qa' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Configuration de l'agent
const agentConfig: AgentConfig = {
  id: process.env.AGENT_ID || 'agent-qa-001',
  name: 'Agent QA Test Automation',
  type: 'qa',
  version: '1.0.0',
  capabilities: [
    'unit-testing',
    'integration-testing',
    'e2e-testing',
    'performance-testing',
    'accessibility-testing',
    'security-testing',
    'visual-testing',
    'api-testing',
    'load-testing',
    'code-quality-analysis',
    'test-generation',
    'report-generation'
  ],
  endpoints: {
    health: '/health',
    ready: '/ready',
    info: '/api/info',
    test: '/api/test',
    quality: '/api/quality',
    generate: '/api/generate',
    status: '/api/status',
    metrics: '/api/metrics',
    reports: '/api/reports'
  },
  memory: {
    store: 'weaviate',
    collections: [
      'TestResult',
      'TestPattern',
      'QualityMetric',
      'TestTemplate',
      'Issue',
      'Recommendation'
    ]
  },
  communication: {
    kafka: {
      topics: [
        'agent.qa.test.complete',
        'agent.qa.test.failed',
        'agent.qa.quality.report',
        'agent.qa.issue.detected'
      ],
      groupId: 'agent-qa-group'
    },
    redis: {
      channels: [
        'qa:notifications',
        'qa:requests'
      ]
    }
  },
  testing: {
    supportedTypes: [
      'unit',
      'integration',
      'e2e',
      'performance',
      'accessibility',
      'security',
      'visual',
      'api',
      'load',
      'smoke',
      'regression',
      'full-suite'
    ],
    defaultTimeout: 300000, // 5 minutes
    maxParallelTests: 3,
    browsers: ['chromium', 'firefox', 'webkit'],
    tools: [
      { name: 'jest', type: 'unit', version: '29.x', configuration: {} },
      { name: 'playwright', type: 'e2e', version: '1.x', configuration: {} },
      { name: 'lighthouse', type: 'performance', version: '11.x', configuration: {} },
      { name: 'axe-core', type: 'accessibility', version: '4.x', configuration: {} },
      { name: 'eslint', type: 'quality', version: '8.x', configuration: {} }
    ]
  }
};

// Initialisation de l'application Express
const app = express();
const port = process.env.PORT || 3008;

// Middleware de sécurité
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  }
}));

// Middleware CORS
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Middleware de parsing
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Middleware de logging des requêtes
app.use((req, res, next) => {
  logger.info('Requête reçue', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Variables globales pour les services
let qaAgent: QAAgent;
let memory: WeaviateMemory;
let communication: KafkaCommunication;

/**
 * Initialisation des services
 */
async function initializeServices(): Promise<void> {
  try {
    logger.info('Initialisation des services de l\'agent QA');

    // Initialiser la mémoire Weaviate
    memory = new WeaviateMemory(
      logger,
      process.env.WEAVIATE_URL || 'http://weaviate:8080'
    );

    // Initialiser la communication Kafka
    communication = new KafkaCommunication(
      logger,
      process.env.KAFKA_BROKERS || 'kafka:9092',
      agentConfig.id
    );

    // Initialiser l'agent QA
    qaAgent = new QAAgent(
      agentConfig,
      logger,
      memory,
      communication
    );

    logger.info('Services QA initialisés avec succès');

  } catch (error) {
    logger.error('Erreur lors de l\'initialisation des services QA', { error: error.message });
    throw error;
  }
}

// Routes de santé et statut

/**
 * Endpoint de santé
 */
app.get('/health', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    agent: {
      id: agentConfig.id,
      name: agentConfig.name,
      version: agentConfig.version
    },
    services: {
      memory: memory?.isConnected() || false,
      communication: communication?.isConnected() || false
    },
    testing: {
      supportedTypes: agentConfig.testing.supportedTypes,
      activeTests: qaAgent?.getStatus()?.activeTests || 0,
      queuedTests: qaAgent?.getStatus()?.queuedTests || 0
    },
    uptime: process.uptime(),
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
    }
  };

  const statusCode = health.services.memory && health.services.communication ? 200 : 503;
  res.status(statusCode).json(health);
});

/**
 * Endpoint de disponibilité
 */
app.get('/ready', (req, res) => {
  const ready = qaAgent && memory?.isConnected() && communication?.isConnected();
  res.status(ready ? 200 : 503).json({
    ready,
    timestamp: new Date().toISOString()
  });
});

/**
 * Informations sur l'agent
 */
app.get('/api/info', (req, res) => {
  res.json({
    agent: agentConfig,
    capabilities: agentConfig.capabilities,
    endpoints: agentConfig.endpoints,
    supportedTestTypes: agentConfig.testing.supportedTypes,
    testingTools: agentConfig.testing.tools,
    version: agentConfig.version,
    timestamp: new Date().toISOString()
  });
});

/**
 * Statut détaillé de l'agent
 */
app.get('/api/status', (req, res) => {
  try {
    const status = qaAgent?.getStatus() || {
      activeTests: 0,
      queuedTests: 0,
      isProcessingQueue: false,
      supportedTypes: agentConfig.testing.supportedTypes,
      uptime: process.uptime()
    };

    res.json({
      success: true,
      status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération du statut', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la récupération du statut',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Routes API principales

/**
 * Exécution de tests
 */
app.post('/api/test', async (req, res) => {
  try {
    const testRequest = req.body;

    if (!testRequest || !testRequest.type || !testRequest.source) {
      return res.status(400).json({
        error: 'Requête de test invalide',
        required: ['type', 'source'],
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de test', { 
      type: testRequest.type,
      source: testRequest.source.type 
    });

    const testResult = await qaAgent.runTests(testRequest);

    res.json({
      success: true,
      testResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de l\'exécution des tests', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de l\'exécution des tests',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Analyse de qualité de code
 */
app.post('/api/quality', async (req, res) => {
  try {
    const { code, framework } = req.body;

    if (!code || !framework) {
      return res.status(400).json({
        error: 'Code et framework requis',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande d\'analyse de qualité', { framework });

    const qualityAnalysis = await qaAgent.analyzeCodeQuality(code, framework);

    res.json({
      success: true,
      qualityAnalysis,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de l\'analyse de qualité', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de l\'analyse de qualité',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Génération de tests automatiques
 */
app.post('/api/generate', async (req, res) => {
  try {
    const testRequest = req.body;

    if (!testRequest || !testRequest.source) {
      return res.status(400).json({
        error: 'Source de code requise pour la génération',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de génération de tests', { 
      source: testRequest.source.type 
    });

    const generatedTests = await qaAgent.generateAutomaticTests(testRequest);

    res.json({
      success: true,
      generatedTests,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la génération de tests', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la génération de tests',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Métriques de test
 */
app.get('/api/metrics', async (req, res) => {
  try {
    const { testId, timeRange } = req.query;

    logger.info('Demande de métriques', { testId, timeRange });

    const metrics = await qaAgent.getTestMetrics(
      testId as string,
      timeRange as string
    );

    res.json({
      success: true,
      metrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des métriques', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la récupération des métriques',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Rapports de test
 */
app.get('/api/reports/:testId', async (req, res) => {
  try {
    const { testId } = req.params;

    // Cette route servirait à récupérer les rapports générés
    // Pour l'instant, on retourne une réponse simple
    res.json({
      success: true,
      message: 'Rapports disponibles',
      testId,
      reports: [
        { type: 'html', url: `/reports/${testId}-report.html` },
        { type: 'json', url: `/reports/${testId}-report.json` },
        { type: 'xml', url: `/reports/${testId}-junit.xml` }
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération des rapports', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la récupération des rapports',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Middleware de gestion des erreurs
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Erreur non gérée', { 
    error: error.message, 
    stack: error.stack,
    url: req.url,
    method: req.method 
  });

  res.status(500).json({
    error: 'Erreur interne du serveur',
    timestamp: new Date().toISOString()
  });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint non trouvé',
    timestamp: new Date().toISOString()
  });
});

/**
 * Démarrage du serveur
 */
async function startServer(): Promise<void> {
  try {
    // Initialiser les services
    await initializeServices();

    // Démarrer le serveur HTTP
    const server = app.listen(port, () => {
      logger.info(`Agent QA démarré sur le port ${port}`, {
        agentId: agentConfig.id,
        version: agentConfig.version,
        environment: process.env.NODE_ENV || 'development',
        supportedTestTypes: agentConfig.testing.supportedTypes
      });
    });

    // Gestion gracieuse de l'arrêt
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Signal ${signal} reçu, arrêt gracieux en cours...`);
      
      server.close(async () => {
        try {
          if (qaAgent) {
            await qaAgent.shutdown();
          }
          if (communication) {
            await communication.disconnect();
          }
          if (memory) {
            await memory.close();
          }
          logger.info('Arrêt gracieux terminé');
          process.exit(0);
        } catch (error) {
          logger.error('Erreur lors de l\'arrêt gracieux', { error: error.message });
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Erreur lors du démarrage du serveur QA', { error: error.message });
    process.exit(1);
  }
}

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  logger.error('Exception non capturée', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Promesse rejetée non gérée', { reason, promise });
  process.exit(1);
});

// Démarrer l'application
startServer().catch((error) => {
  logger.error('Erreur fatale lors du démarrage QA', { error: error.message });
  process.exit(1);
});

export { app, qaAgent, memory, communication };
