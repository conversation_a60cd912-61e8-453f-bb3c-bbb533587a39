import { Logger } from 'winston';
import { TestRequest, TestResult, TestStatus, BrowserConfig } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { chromium, firefox, webkit, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext } from 'playwright';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Runner de Tests End-to-End
 * 
 * Exécute les tests E2E avec Playwright sur différents navigateurs
 * et appareils pour valider l'expérience utilisateur complète.
 */
export class E2ETestRunner {
  private logger: Logger;
  private memory: WeaviateMemory;
  private workingDirectory: string;
  private browsers: Map<string, Browser> = new Map();

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    this.workingDirectory = process.env.E2E_WORKDIR || './e2e-workspace';
    
    // Créer le répertoire de travail
    fs.ensureDirSync(this.workingDirectory);
  }

  /**
   * Exécute les tests E2E
   */
  async run(request: TestRequest, generatedTests?: any): Promise<TestResult> {
    this.logger.info('Exécution des tests E2E', { id: request.id });

    const startTime = new Date();
    const testId = request.id;
    const resultsPath = path.join(this.workingDirectory, testId);

    try {
      // 1. Préparer l'environnement de test
      await this.prepareTestEnvironment(resultsPath);

      // 2. Initialiser les navigateurs
      await this.initializeBrowsers(request);

      // 3. Générer les tests E2E si nécessaire
      const testScenarios = await this.generateTestScenarios(request, generatedTests);

      // 4. Exécuter les tests sur tous les navigateurs configurés
      const testResults = await this.executeTestScenarios(request, testScenarios, resultsPath);

      // 5. Consolider les résultats
      const consolidatedResult = await this.consolidateResults(testResults, request, startTime);

      // 6. Générer les artefacts
      await this.generateArtifacts(resultsPath, consolidatedResult);

      // 7. Fermer les navigateurs
      await this.closeBrowsers();

      this.logger.info('Tests E2E terminés', { 
        id: testId,
        status: consolidatedResult.status,
        successRate: consolidatedResult.summary.successRate 
      });

      return consolidatedResult;

    } catch (error) {
      this.logger.error('Erreur lors des tests E2E', { 
        id: testId,
        error: error.message 
      });

      await this.closeBrowsers();
      await this.cleanup(resultsPath);
      throw error;
    }
  }

  /**
   * Prépare l'environnement de test
   */
  private async prepareTestEnvironment(resultsPath: string): Promise<void> {
    await fs.ensureDir(resultsPath);
    await fs.ensureDir(path.join(resultsPath, 'screenshots'));
    await fs.ensureDir(path.join(resultsPath, 'videos'));
    await fs.ensureDir(path.join(resultsPath, 'traces'));
    await fs.ensureDir(path.join(resultsPath, 'reports'));
  }

  /**
   * Initialise les navigateurs
   */
  private async initializeBrowsers(request: TestRequest): Promise<void> {
    const browsers = request.configuration.browsers || [
      { name: 'chromium', viewport: { width: 1920, height: 1080 } }
    ];

    for (const browserConfig of browsers) {
      try {
        let browser: Browser;
        
        switch (browserConfig.name) {
          case 'chromium':
          case 'chrome':
            browser = await chromium.launch({
              headless: request.configuration.headless !== false,
              args: ['--no-sandbox', '--disable-setuid-sandbox']
            });
            break;
          case 'firefox':
            browser = await firefox.launch({
              headless: request.configuration.headless !== false
            });
            break;
          case 'webkit':
            browser = await webkit.launch({
              headless: request.configuration.headless !== false
            });
            break;
          default:
            throw new Error(`Navigateur ${browserConfig.name} non supporté`);
        }

        this.browsers.set(browserConfig.name, browser);
        this.logger.info(`Navigateur ${browserConfig.name} initialisé`);
      } catch (error) {
        this.logger.error(`Erreur lors de l'initialisation du navigateur ${browserConfig.name}`, {
          error: error.message
        });
      }
    }
  }

  /**
   * Génère les scénarios de test E2E
   */
  private async generateTestScenarios(request: TestRequest, generatedTests?: any): Promise<any[]> {
    const scenarios = [];

    if (generatedTests?.e2eTests) {
      scenarios.push(...generatedTests.e2eTests);
    }

    // Générer des tests automatiques basés sur la source
    if (request.source.type === 'url') {
      scenarios.push(...await this.generateUrlBasedTests(request.source.url!));
    } else if (request.source.type === 'code') {
      scenarios.push(...await this.generateCodeBasedTests(request.source.code!));
    }

    // Ajouter des tests par défaut si aucun test n'est défini
    if (scenarios.length === 0) {
      scenarios.push(...this.getDefaultTestScenarios(request));
    }

    return scenarios;
  }

  /**
   * Génère des tests basés sur une URL
   */
  private async generateUrlBasedTests(url: string): Promise<any[]> {
    return [
      {
        name: 'Page Load Test',
        description: 'Vérifie que la page se charge correctement',
        steps: [
          { action: 'goto', url },
          { action: 'waitForLoadState', state: 'networkidle' },
          { action: 'screenshot', name: 'page-loaded' }
        ]
      },
      {
        name: 'Navigation Test',
        description: 'Teste la navigation de base',
        steps: [
          { action: 'goto', url },
          { action: 'click', selector: 'a[href]', optional: true },
          { action: 'waitForLoadState', state: 'networkidle' },
          { action: 'screenshot', name: 'navigation' }
        ]
      },
      {
        name: 'Form Interaction Test',
        description: 'Teste les interactions avec les formulaires',
        steps: [
          { action: 'goto', url },
          { action: 'fill', selector: 'input[type="text"]', value: 'test', optional: true },
          { action: 'fill', selector: 'input[type="email"]', value: '<EMAIL>', optional: true },
          { action: 'click', selector: 'button[type="submit"]', optional: true },
          { action: 'screenshot', name: 'form-interaction' }
        ]
      }
    ];
  }

  /**
   * Génère des tests basés sur le code
   */
  private async generateCodeBasedTests(code: any): Promise<any[]> {
    const tests = [];
    
    // Analyser le code pour détecter les composants et pages
    const components = this.extractComponents(code);
    const routes = this.extractRoutes(code);

    // Générer des tests pour chaque route
    for (const route of routes) {
      tests.push({
        name: `Route Test: ${route.path}`,
        description: `Teste la route ${route.path}`,
        steps: [
          { action: 'goto', url: `http://localhost:3000${route.path}` },
          { action: 'waitForLoadState', state: 'networkidle' },
          { action: 'screenshot', name: `route-${route.path.replace('/', '-')}` }
        ]
      });
    }

    // Générer des tests pour les composants interactifs
    for (const component of components) {
      if (component.interactive) {
        tests.push({
          name: `Component Test: ${component.name}`,
          description: `Teste le composant ${component.name}`,
          steps: [
            { action: 'goto', url: 'http://localhost:3000' },
            { action: 'click', selector: `[data-testid="${component.testId}"]`, optional: true },
            { action: 'screenshot', name: `component-${component.name}` }
          ]
        });
      }
    }

    return tests;
  }

  /**
   * Exécute les scénarios de test
   */
  private async executeTestScenarios(
    request: TestRequest,
    scenarios: any[],
    resultsPath: string
  ): Promise<TestResult[]> {
    const results: TestResult[] = [];

    for (const [browserName, browser] of this.browsers) {
      const browserConfig = request.configuration.browsers?.find(b => b.name === browserName);
      if (!browserConfig) continue;

      this.logger.info(`Exécution des tests sur ${browserName}`);

      const context = await browser.newContext({
        viewport: browserConfig.viewport,
        userAgent: browserConfig.userAgent,
        locale: browserConfig.locale,
        timezoneId: browserConfig.timezone,
        recordVideo: {
          dir: path.join(resultsPath, 'videos', browserName)
        }
      });

      // Activer le tracing
      await context.tracing.start({ screenshots: true, snapshots: true });

      const browserResult = await this.executeBrowserTests(
        context,
        scenarios,
        browserName,
        resultsPath,
        request
      );

      // Arrêter le tracing
      await context.tracing.stop({
        path: path.join(resultsPath, 'traces', `${browserName}-trace.zip`)
      });

      await context.close();
      results.push(browserResult);
    }

    return results;
  }

  /**
   * Exécute les tests pour un navigateur spécifique
   */
  private async executeBrowserTests(
    context: BrowserContext,
    scenarios: any[],
    browserName: string,
    resultsPath: string,
    request: TestRequest
  ): Promise<TestResult> {
    const startTime = new Date();
    const testCases = [];
    let passed = 0, failed = 0, skipped = 0;

    for (const scenario of scenarios) {
      const page = await context.newPage();
      
      try {
        this.logger.info(`Exécution du scénario: ${scenario.name} sur ${browserName}`);

        const testCase = await this.executeScenario(
          page,
          scenario,
          browserName,
          resultsPath,
          request
        );

        testCases.push(testCase);

        if (testCase.status === 'passed') {
          passed++;
        } else if (testCase.status === 'failed') {
          failed++;
        } else {
          skipped++;
        }

      } catch (error) {
        this.logger.error(`Erreur dans le scénario ${scenario.name}`, { error: error.message });
        
        testCases.push({
          name: scenario.name,
          status: 'failed' as TestStatus,
          duration: 0,
          steps: [],
          error: {
            message: error.message,
            type: error.constructor.name,
            stack: error.stack
          }
        });
        failed++;
      } finally {
        await page.close();
      }
    }

    const total = testCases.length;
    const successRate = total > 0 ? passed / total : 0;

    return {
      id: `${request.id}-${browserName}`,
      type: request.type,
      status: failed > 0 ? 'failed' : 'passed',
      summary: {
        total,
        passed,
        failed,
        skipped,
        errors: 0,
        successRate
      },
      details: {
        suites: [{
          name: `E2E Tests - ${browserName}`,
          status: failed > 0 ? 'failed' : 'passed',
          tests: testCases,
          duration: Date.now() - startTime.getTime()
        }],
        environment: {
          browser: {
            name: browserName,
            version: 'latest',
            userAgent: await this.getBrowserUserAgent(context),
            viewport: { width: 1920, height: 1080 }
          },
          os: process.platform,
          node: process.version,
          timestamp: new Date()
        },
        configuration: request.configuration
      },
      reports: [],
      artifacts: [],
      metrics: {},
      issues: [],
      recommendations: [],
      startedAt: startTime,
      completedAt: new Date(),
      duration: Date.now() - startTime.getTime()
    };
  }

  /**
   * Exécute un scénario de test
   */
  private async executeScenario(
    page: Page,
    scenario: any,
    browserName: string,
    resultsPath: string,
    request: TestRequest
  ): Promise<any> {
    const startTime = Date.now();
    const steps = [];

    for (const step of scenario.steps) {
      const stepStartTime = Date.now();
      
      try {
        await this.executeStep(page, step, browserName, resultsPath);
        
        steps.push({
          name: step.action,
          status: 'passed' as TestStatus,
          duration: Date.now() - stepStartTime,
          action: step.action,
          expected: step.expected || 'Step completed successfully',
          actual: 'Step completed successfully'
        });

      } catch (error) {
        if (step.optional) {
          steps.push({
            name: step.action,
            status: 'skipped' as TestStatus,
            duration: Date.now() - stepStartTime,
            action: step.action,
            expected: step.expected || 'Step completed successfully',
            actual: `Skipped: ${error.message}`
          });
        } else {
          steps.push({
            name: step.action,
            status: 'failed' as TestStatus,
            duration: Date.now() - stepStartTime,
            action: step.action,
            expected: step.expected || 'Step completed successfully',
            actual: error.message,
            error: {
              message: error.message,
              type: error.constructor.name,
              stack: error.stack
            }
          });
          throw error;
        }
      }
    }

    return {
      name: scenario.name,
      status: 'passed' as TestStatus,
      duration: Date.now() - startTime,
      steps
    };
  }

  /**
   * Exécute une étape de test
   */
  private async executeStep(
    page: Page,
    step: any,
    browserName: string,
    resultsPath: string
  ): Promise<void> {
    const timeout = step.timeout || 30000;

    switch (step.action) {
      case 'goto':
        await page.goto(step.url, { waitUntil: 'networkidle', timeout });
        break;

      case 'click':
        await page.click(step.selector, { timeout });
        break;

      case 'fill':
        await page.fill(step.selector, step.value, { timeout });
        break;

      case 'type':
        await page.type(step.selector, step.text, { timeout });
        break;

      case 'waitForSelector':
        await page.waitForSelector(step.selector, { timeout });
        break;

      case 'waitForLoadState':
        await page.waitForLoadState(step.state, { timeout });
        break;

      case 'screenshot':
        const screenshotPath = path.join(
          resultsPath,
          'screenshots',
          browserName,
          `${step.name || 'screenshot'}-${Date.now()}.png`
        );
        await fs.ensureDir(path.dirname(screenshotPath));
        await page.screenshot({ path: screenshotPath, fullPage: true });
        break;

      case 'scroll':
        await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
        break;

      case 'hover':
        await page.hover(step.selector, { timeout });
        break;

      case 'select':
        await page.selectOption(step.selector, step.value, { timeout });
        break;

      case 'check':
        await page.check(step.selector, { timeout });
        break;

      case 'uncheck':
        await page.uncheck(step.selector, { timeout });
        break;

      case 'wait':
        await page.waitForTimeout(step.duration || 1000);
        break;

      default:
        throw new Error(`Action ${step.action} non supportée`);
    }
  }

  /**
   * Consolide les résultats de tous les navigateurs
   */
  private async consolidateResults(
    results: TestResult[],
    request: TestRequest,
    startTime: Date
  ): Promise<TestResult> {
    const totalTests = results.reduce((sum, r) => sum + r.summary.total, 0);
    const totalPassed = results.reduce((sum, r) => sum + r.summary.passed, 0);
    const totalFailed = results.reduce((sum, r) => sum + r.summary.failed, 0);
    const totalSkipped = results.reduce((sum, r) => sum + r.summary.skipped, 0);

    return {
      id: request.id,
      type: request.type,
      status: totalFailed > 0 ? 'failed' : 'passed',
      summary: {
        total: totalTests,
        passed: totalPassed,
        failed: totalFailed,
        skipped: totalSkipped,
        errors: 0,
        successRate: totalTests > 0 ? totalPassed / totalTests : 0
      },
      details: {
        suites: results.flatMap(r => r.details.suites),
        environment: results[0]?.details.environment || {
          os: process.platform,
          node: process.version,
          timestamp: new Date()
        },
        configuration: request.configuration
      },
      reports: [],
      artifacts: [],
      metrics: {},
      issues: [],
      recommendations: this.generateRecommendations(results),
      startedAt: startTime,
      completedAt: new Date(),
      duration: Date.now() - startTime.getTime()
    };
  }

  /**
   * Génère les artefacts de test
   */
  private async generateArtifacts(resultsPath: string, testResult: TestResult): Promise<void> {
    const artifacts = [];

    // Ajouter les screenshots
    const screenshotsPath = path.join(resultsPath, 'screenshots');
    if (await fs.pathExists(screenshotsPath)) {
      artifacts.push({
        type: 'screenshot' as const,
        name: 'Screenshots',
        path: screenshotsPath,
        size: await this.getDirectorySize(screenshotsPath)
      });
    }

    // Ajouter les vidéos
    const videosPath = path.join(resultsPath, 'videos');
    if (await fs.pathExists(videosPath)) {
      artifacts.push({
        type: 'video' as const,
        name: 'Videos',
        path: videosPath,
        size: await this.getDirectorySize(videosPath)
      });
    }

    // Ajouter les traces
    const tracesPath = path.join(resultsPath, 'traces');
    if (await fs.pathExists(tracesPath)) {
      artifacts.push({
        type: 'trace' as const,
        name: 'Traces',
        path: tracesPath,
        size: await this.getDirectorySize(tracesPath)
      });
    }

    testResult.artifacts = artifacts;
  }

  /**
   * Ferme tous les navigateurs
   */
  private async closeBrowsers(): Promise<void> {
    for (const [name, browser] of this.browsers) {
      try {
        await browser.close();
        this.logger.info(`Navigateur ${name} fermé`);
      } catch (error) {
        this.logger.warn(`Erreur lors de la fermeture du navigateur ${name}`, {
          error: error.message
        });
      }
    }
    this.browsers.clear();
  }

  /**
   * Nettoie les fichiers temporaires
   */
  private async cleanup(resultsPath: string): Promise<void> {
    try {
      // Garder les résultats mais nettoyer les fichiers temporaires
      // await fs.remove(resultsPath);
    } catch (error) {
      this.logger.warn('Erreur lors du nettoyage E2E', { 
        resultsPath,
        error: error.message 
      });
    }
  }

  // Méthodes utilitaires

  private extractComponents(code: any): any[] {
    // Analyser le code pour extraire les composants
    // Cette méthode devrait parser le code React/Vue pour détecter les composants
    return [];
  }

  private extractRoutes(code: any): any[] {
    // Analyser le code pour extraire les routes
    // Cette méthode devrait parser le code pour détecter les routes
    return [{ path: '/' }];
  }

  private getDefaultTestScenarios(request: TestRequest): any[] {
    const baseUrl = request.source.url || 'http://localhost:3000';
    
    return [
      {
        name: 'Basic Page Load',
        description: 'Vérifie que la page principale se charge',
        steps: [
          { action: 'goto', url: baseUrl },
          { action: 'waitForLoadState', state: 'networkidle' },
          { action: 'screenshot', name: 'homepage' }
        ]
      }
    ];
  }

  private async getBrowserUserAgent(context: BrowserContext): Promise<string> {
    const page = await context.newPage();
    const userAgent = await page.evaluate(() => navigator.userAgent);
    await page.close();
    return userAgent;
  }

  private generateRecommendations(results: TestResult[]): string[] {
    const recommendations = [];
    
    const failedTests = results.filter(r => r.status === 'failed');
    if (failedTests.length > 0) {
      recommendations.push(`${failedTests.length} navigateur(s) avec des tests échoués`);
    }

    const lowSuccessRate = results.filter(r => r.summary.successRate < 0.8);
    if (lowSuccessRate.length > 0) {
      recommendations.push('Taux de succès faible sur certains navigateurs');
    }

    return recommendations;
  }

  private async getDirectorySize(dirPath: string): Promise<number> {
    try {
      const stats = await fs.stat(dirPath);
      if (stats.isFile()) {
        return stats.size;
      }
      
      let totalSize = 0;
      const files = await fs.readdir(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        totalSize += await this.getDirectorySize(filePath);
      }
      
      return totalSize;
    } catch (error) {
      return 0;
    }
  }
}
