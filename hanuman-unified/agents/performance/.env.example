# Configuration de l'Agent Performance

# Serveur
PORT=3007
NODE_ENV=production
LOG_LEVEL=info

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=performance-agent
<PERSON>AFKA_GROUP_ID=performance-agent-group
KAFKA_TOPIC_REQUESTS=performance.requests
KAFKA_TOPIC_RESULTS=performance.results
KAFKA_TOPIC_ALERTS=performance.alerts
KAFKA_TOPIC_RECOMMENDATIONS=performance.recommendations

# Weaviate Configuration
WEAVIATE_HOST=localhost:8080
WEAVIATE_SCHEME=http
WEAVIATE_API_KEY=
WEAVIATE_COLLECTION_OPTIMIZATIONS=PerformanceOptimizations
WEAVIATE_COLLECTION_BENCHMARKS=PerformanceBenchmarks
WEAVIATE_COLLECTION_RECOMMENDATIONS=PerformanceRecommendations
WEAVIATE_COLLECTION_METRICS=PerformanceMetrics

# Prometheus Configuration
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
PROMETHEUS_PATH=/metrics
PROMETHEUS_COLLECT_DEFAULT=true

# Lighthouse Configuration
LIGHTHOUSE_ENABLED=true
LIGHTHOUSE_CATEGORIES=performance,accessibility,best-practices,seo
LIGHTHOUSE_FORM_FACTOR=desktop
LIGHTHOUSE_THROTTLING=simulated3G

# Load Testing Configuration
LOAD_TESTING_ENABLED=true
LOAD_TESTING_TOOL=autocannon
LOAD_TESTING_DURATION=30s
LOAD_TESTING_CONNECTIONS=10
LOAD_TESTING_RPS=100

# Profiling Configuration
PROFILING_ENABLED=true
PROFILING_TOOL=clinic
PROFILING_DURATION=60

# Monitoring Configuration
MONITORING_INTERVAL=30000
THRESHOLD_RESPONSE_TIME=1000
THRESHOLD_CPU_USAGE=80
THRESHOLD_MEMORY_USAGE=85
THRESHOLD_ERROR_RATE=5

# Alerts Configuration
ALERTS_ENABLED=true
ALERT_CHANNELS=kafka,log

# Security Configuration
REQUIRE_AUTH=false
CORS_ORIGIN=*

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
