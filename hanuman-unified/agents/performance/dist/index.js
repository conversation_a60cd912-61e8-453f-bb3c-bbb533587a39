#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLogger = exports.loadConfig = exports.PerformanceServer = exports.PerformanceAgent = void 0;
exports.createTestAgent = createTestAgent;
exports.createTestServer = createTestServer;
const dotenv_1 = __importDefault(require("dotenv"));
const fs_1 = require("fs");
const PerformanceAgent_1 = require("./core/PerformanceAgent");
Object.defineProperty(exports, "PerformanceAgent", { enumerable: true, get: function () { return PerformanceAgent_1.PerformanceAgent; } });
const server_1 = require("./api/server");
Object.defineProperty(exports, "PerformanceServer", { enumerable: true, get: function () { return server_1.PerformanceServer; } });
const config_1 = require("./utils/config");
Object.defineProperty(exports, "loadConfig", { enumerable: true, get: function () { return config_1.loadConfig; } });
const logger_1 = require("./utils/logger");
Object.defineProperty(exports, "createLogger", { enumerable: true, get: function () { return logger_1.createLogger; } });
// Charger les variables d'environnement
dotenv_1.default.config();
const logger = (0, logger_1.createLogger)('Main');
/**
 * Fonction principale de démarrage
 */
async function main() {
    try {
        logger.info('🚀 Démarrage de l\'Agent Performance...');
        // Créer les répertoires nécessaires
        await ensureDirectories();
        // Charger la configuration
        const config = (0, config_1.loadConfig)();
        logger.info('Configuration chargée', {
            port: config.port,
            kafkaBrokers: config.kafka.brokers.length,
            weaviateHost: config.weaviate.host
        });
        // Initialiser l'agent
        const agent = new PerformanceAgent_1.PerformanceAgent(config);
        // Initialiser le serveur API
        const server = new server_1.PerformanceServer(config, logger, agent);
        // Gestionnaires de signaux pour un arrêt propre
        setupGracefulShutdown(agent, server);
        // Démarrer l'agent
        await agent.start();
        logger.info('✅ Agent Performance démarré');
        // Démarrer le serveur API
        await server.start();
        logger.info('✅ Serveur API démarré');
        // Afficher les informations de démarrage
        displayStartupInfo(config);
        // Enregistrer les gestionnaires d'événements
        setupEventHandlers(agent);
        logger.info('🎯 Agent Performance prêt à recevoir des requêtes');
    }
    catch (error) {
        logger.error('❌ Erreur lors du démarrage:', error);
        process.exit(1);
    }
}
/**
 * Créer les répertoires nécessaires
 */
async function ensureDirectories() {
    const directories = [
        'logs',
        'benchmarks',
        'reports',
        'temp'
    ];
    for (const dir of directories) {
        try {
            await fs_1.promises.access(dir);
        }
        catch {
            await fs_1.promises.mkdir(dir, { recursive: true });
            logger.info(`Répertoire créé: ${dir}`);
        }
    }
}
/**
 * Configuration de l'arrêt propre
 */
function setupGracefulShutdown(agent, server) {
    const shutdown = async (signal) => {
        logger.info(`Signal ${signal} reçu, arrêt en cours...`);
        try {
            // Arrêter le serveur API
            await server.stop();
            logger.info('Serveur API arrêté');
            // Arrêter l'agent
            await agent.stop();
            logger.info('Agent Performance arrêté');
            logger.info('Arrêt terminé avec succès');
            process.exit(0);
        }
        catch (error) {
            logger.error('Erreur lors de l\'arrêt:', error);
            process.exit(1);
        }
    };
    // Gestionnaires de signaux
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    // Gestionnaire d'erreurs non capturées
    process.on('uncaughtException', (error) => {
        logger.error('Exception non capturée:', error);
        shutdown('UNCAUGHT_EXCEPTION');
    });
    process.on('unhandledRejection', (reason, promise) => {
        logger.error('Promesse rejetée non gérée:', { reason, promise });
        shutdown('UNHANDLED_REJECTION');
    });
}
/**
 * Afficher les informations de démarrage
 */
function displayStartupInfo(config) {
    const info = `
╔══════════════════════════════════════════════════════════════╗
║                    🚀 AGENT PERFORMANCE                      ║
╠══════════════════════════════════════════════════════════════╣
║ Version: 1.0.0                                               ║
║ Port API: ${config.port.toString().padEnd(51)} ║
║ Kafka: ${config.kafka.brokers.join(', ').padEnd(53)} ║
║ Weaviate: ${config.weaviate.host.padEnd(50)} ║
║ Monitoring: ${(config.monitoring.interval / 1000 + 's').padEnd(48)} ║
╠══════════════════════════════════════════════════════════════╣
║ Endpoints disponibles:                                       ║
║ • GET  /health        - Health check                         ║
║ • GET  /metrics       - Métriques Prometheus                 ║
║ • POST /benchmark     - Lancer un benchmark                  ║
║ • POST /optimize      - Demander des optimisations           ║
║ • POST /monitor       - Configurer le monitoring             ║
║ • POST /analyze       - Analyser une application             ║
║ • GET  /reports       - Obtenir les rapports                 ║
║ • GET  /status        - Statut de l'agent                    ║
║ • GET  /api-docs      - Documentation API                    ║
╚══════════════════════════════════════════════════════════════╝
  `;
    console.log(info);
}
/**
 * Configuration des gestionnaires d'événements
 */
function setupEventHandlers(agent) {
    // Benchmark terminé
    agent.on('benchmarkCompleted', (result) => {
        logger.info(`📊 Benchmark terminé: ${result.id}`, {
            type: result.type,
            duration: result.duration,
            status: result.status,
            recommendations: result.recommendations.length
        });
    });
    // Optimisations générées
    agent.on('optimizationsGenerated', (recommendations) => {
        logger.info(`💡 ${recommendations.length} optimisations générées`, {
            priorities: recommendations.reduce((acc, rec) => {
                acc[rec.priority] = (acc[rec.priority] || 0) + 1;
                return acc;
            }, {})
        });
    });
    // Monitoring configuré
    agent.on('monitoringConfigured', (request) => {
        logger.info(`📈 Monitoring configuré: ${request.id}`, {
            targets: request.targets.length,
            metrics: request.metrics.length
        });
    });
    // Analyse terminée
    agent.on('analysisCompleted', (analysis) => {
        logger.info(`🔍 Analyse terminée: ${analysis.id}`, {
            type: analysis.type,
            findings: analysis.findings.length,
            recommendations: analysis.recommendations.length
        });
    });
    // Métrique reçue
    agent.on('metric', (metric) => {
        if (metric.value > 1000) { // Log seulement les métriques importantes
            logger.debug(`📊 Métrique: ${metric.name} = ${metric.value}${metric.unit}`);
        }
    });
    // Alerte générée
    agent.on('alert', (alert) => {
        const emoji = alert.severity === 'critical' ? '🚨' :
            alert.severity === 'error' ? '⚠️' :
                alert.severity === 'warning' ? '⚡' : 'ℹ️';
        logger.warn(`${emoji} Alerte ${alert.severity}: ${alert.title}`, {
            metric: alert.metric,
            value: alert.value,
            threshold: alert.threshold
        });
    });
    // Démarrage/arrêt
    agent.on('started', () => {
        logger.info('🟢 Agent Performance démarré');
    });
    agent.on('stopped', () => {
        logger.info('🔴 Agent Performance arrêté');
    });
    // Erreurs
    agent.on('error', (error) => {
        logger.error('❌ Erreur dans l\'agent:', error);
    });
}
/**
 * Fonction utilitaire pour les tests
 */
async function createTestAgent(config) {
    const testConfig = config || (0, config_1.loadConfig)();
    return new PerformanceAgent_1.PerformanceAgent(testConfig);
}
/**
 * Fonction utilitaire pour les tests du serveur
 */
async function createTestServer(agent, config) {
    const testConfig = config || (0, config_1.loadConfig)();
    const testLogger = (0, logger_1.createLogger)('Test');
    return new server_1.PerformanceServer(testConfig, testLogger, agent);
}
// Démarrer l'application si ce fichier est exécuté directement
if (require.main === module) {
    main().catch((error) => {
        logger.error('Erreur fatale:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map