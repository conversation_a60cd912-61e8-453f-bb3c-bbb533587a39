import { Logger } from 'winston';
import { BenchmarkResult, OptimizationRequest, AnalysisRequest, Recommendation } from '../types';
/**
 * Conseiller en optimisation intelligent
 * Génère des recommandations basées sur l'analyse des performances
 */
export declare class OptimizationAdvisor {
    private readonly logger;
    private readonly optimizationPatterns;
    constructor(logger: Logger);
    /**
     * Générer des recommandations basées sur les résultats de benchmark
     */
    generateRecommendations(benchmarkResult: BenchmarkResult): Promise<Recommendation[]>;
    /**
     * Générer des optimisations spécifiques
     */
    generateOptimizations(request: OptimizationRequest, context: any): Promise<Recommendation[]>;
    /**
     * Analyser une application
     */
    analyzeApplication(request: AnalysisRequest): Promise<any>;
    /**
     * Analyser les résultats Lighthouse
     */
    private analyzeLighthouseResults;
    /**
     * Analyser les résultats de test de charge
     */
    private analyzeLoadTestingResults;
    /**
     * Analyser les résultats de profiling
     */
    private analyzeProfilingResults;
    /**
     * Initialiser les patterns d'optimisation
     */
    private initializeOptimizationPatterns;
    /**
     * Vérifier si un pattern est applicable
     */
    private isPatternApplicable;
    /**
     * Créer une recommandation à partir d'un pattern
     */
    private createRecommendationFromPattern;
    /**
     * Filtrer les recommandations selon les contraintes
     */
    private filterByConstraints;
    /**
     * Analyser le code
     */
    private analyzeCode;
    /**
     * Analyser l'architecture
     */
    private analyzeArchitecture;
    /**
     * Analyser l'infrastructure
     */
    private analyzeInfrastructure;
    /**
     * Générer des recommandations à partir des findings
     */
    private generateRecommendationsFromFindings;
}
//# sourceMappingURL=OptimizationAdvisor.d.ts.map