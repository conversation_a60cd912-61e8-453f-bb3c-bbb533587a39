import { Logger } from 'winston';
import { KafkaConfig, KafkaMessage, BenchmarkResult, Recommendation } from '../types';
/**
 * Communication Kafka pour l'Agent Performance
 * Gère la communication synaptique avec les autres agents
 */
export declare class KafkaCommunication {
    private readonly config;
    private readonly logger;
    private readonly kafka;
    private producer;
    private consumer;
    private isConnected;
    private messageHandlers;
    constructor(config: KafkaConfig, logger: Logger);
    /**
     * Se connecter à Kafka
     */
    connect(): Promise<void>;
    /**
     * Se déconnecter de Kafka
     */
    disconnect(): Promise<void>;
    /**
     * S'abonner à un topic avec un handler
     */
    subscribe(topic: string, handler: (message: any) => Promise<void>): Promise<void>;
    /**
     * Publier un résultat de benchmark
     */
    publishResult(result: BenchmarkResult): Promise<void>;
    /**
     * Publier des recommandations
     */
    publishRecommendations(recommendations: Recommendation[]): Promise<void>;
    /**
     * Publier une alerte
     */
    publishAlert(alert: any): Promise<void>;
    /**
     * Publier un message générique
     */
    publishMessage(topic: string, message: KafkaMessage): Promise<void>;
    /**
     * Demander un benchmark à un autre agent
     */
    requestBenchmark(targetAgent: string, request: any): Promise<void>;
    /**
     * Demander une optimisation
     */
    requestOptimization(targetAgent: string, request: any): Promise<void>;
    /**
     * S'abonner aux topics configurés
     */
    private subscribeToTopics;
    /**
     * Démarrer la consommation des messages
     */
    private startConsuming;
    /**
     * Traiter un message reçu
     */
    private handleMessage;
    /**
     * Handler par défaut pour les messages
     */
    private handleDefaultMessage;
    /**
     * Traiter une requête de performance
     */
    private handlePerformanceRequest;
    /**
     * Traiter un résultat de performance
     */
    private handlePerformanceResult;
    /**
     * Traiter une alerte de performance
     */
    private handlePerformanceAlert;
    /**
     * Traiter des recommandations
     */
    private handleRecommendations;
    /**
     * Publier une erreur
     */
    private publishError;
    /**
     * Vérifier la santé de la connexion
     */
    isHealthy(): boolean;
    /**
     * Obtenir les statistiques de la communication
     */
    getStats(): any;
}
//# sourceMappingURL=KafkaCommunication.d.ts.map