import { Logger } from 'winston';
import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import * as path from 'path';
import lighthouse from 'lighthouse';
import * as chromeLauncher from 'chrome-launcher';
import {
  BenchmarkingConfig,
  BenchmarkRequest,
  BenchmarkResult,
  LighthouseResult,
  LoadTestingResult,
  ProfilingResult
} from '../types';

/**
 * Moteur de benchmarking avancé
 * Exécute différents types de tests de performance
 */
export class BenchmarkEngine {
  private readonly config: BenchmarkingConfig;
  private readonly logger: Logger;
  private readonly resultsDir: string;

  constructor(config: BenchmarkingConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.resultsDir = path.join(process.cwd(), 'benchmarks');
    this.ensureResultsDirectory();
  }

  /**
   * Exécuter un benchmark selon le type demandé
   */
  async execute(request: BenchmarkRequest): Promise<BenchmarkResult> {
    const startTime = Date.now();
    this.logger.info(`Exécution du benchmark ${request.id} de type ${request.type}`);

    try {
      const result: BenchmarkResult = {
        id: request.id,
        type: request.type,
        timestamp: new Date(),
        duration: 0,
        status: 'success',
        results: {},
        recommendations: [],
        metadata: request.metadata
      };

      // Exécuter les tests selon le type
      switch (request.type) {
        case 'lighthouse':
          if (this.config.lighthouse.enabled && request.target.url) {
            result.results.lighthouse = await this.runLighthouseTest(request);
          }
          break;

        case 'load':
          if (this.config.loadTesting.enabled && request.target.url) {
            result.results.loadTesting = await this.runLoadTest(request);
          }
          break;

        case 'profiling':
          if (this.config.profiling.enabled && request.target.service) {
            result.results.profiling = await this.runProfilingTest(request);
          }
          break;

        case 'full':
          // Exécuter tous les tests disponibles
          if (this.config.lighthouse.enabled && request.target.url) {
            result.results.lighthouse = await this.runLighthouseTest(request);
          }
          if (this.config.loadTesting.enabled && request.target.url) {
            result.results.loadTesting = await this.runLoadTest(request);
          }
          if (this.config.profiling.enabled && request.target.service) {
            result.results.profiling = await this.runProfilingTest(request);
          }
          break;

        default:
          throw new Error(`Type de benchmark non supporté: ${request.type}`);
      }

      result.duration = Date.now() - startTime;
      this.logger.info(`Benchmark ${request.id} terminé en ${result.duration}ms`);

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors du benchmark ${request.id}:`, error);
      return {
        id: request.id,
        type: request.type,
        timestamp: new Date(),
        duration: Date.now() - startTime,
        status: 'error',
        results: {},
        recommendations: [],
        metadata: { error: error.message }
      };
    }
  }

  /**
   * Exécuter un test Lighthouse
   */
  private async runLighthouseTest(request: BenchmarkRequest): Promise<LighthouseResult> {
    this.logger.info(`Démarrage du test Lighthouse pour ${request.target.url}`);

    const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });

    try {
      const options = {
        logLevel: 'info',
        output: 'json',
        onlyCategories: request.configuration.lighthouse?.categories || this.config.lighthouse.categories,
        formFactor: request.configuration.lighthouse?.formFactor || this.config.lighthouse.formFactor,
        throttling: request.configuration.lighthouse?.throttling || this.config.lighthouse.throttling,
        port: chrome.port,
      };

      const runnerResult = await lighthouse(request.target.url!, options);

      if (!runnerResult || !runnerResult.lhr) {
        throw new Error('Résultat Lighthouse invalide');
      }

      const lhr = runnerResult.lhr;

      // Extraire les métriques principales
      const result: LighthouseResult = {
        score: Math.round((lhr.categories.performance?.score || 0) * 100),
        categories: {
          performance: Math.round((lhr.categories.performance?.score || 0) * 100),
          accessibility: Math.round((lhr.categories.accessibility?.score || 0) * 100),
          bestPractices: Math.round((lhr.categories['best-practices']?.score || 0) * 100),
          seo: Math.round((lhr.categories.seo?.score || 0) * 100),
          pwa: lhr.categories.pwa ? Math.round(lhr.categories.pwa.score * 100) : undefined
        },
        metrics: {
          firstContentfulPaint: lhr.audits['first-contentful-paint']?.numericValue || 0,
          largestContentfulPaint: lhr.audits['largest-contentful-paint']?.numericValue || 0,
          firstInputDelay: lhr.audits['max-potential-fid']?.numericValue || 0,
          cumulativeLayoutShift: lhr.audits['cumulative-layout-shift']?.numericValue || 0,
          speedIndex: lhr.audits['speed-index']?.numericValue || 0,
          totalBlockingTime: lhr.audits['total-blocking-time']?.numericValue || 0
        },
        opportunities: this.extractOpportunities(lhr)
      };

      // Sauvegarder le rapport complet
      const reportPath = path.join(this.resultsDir, `lighthouse-${request.id}.json`);
      await fs.writeFile(reportPath, JSON.stringify(lhr, null, 2));

      this.logger.info(`Test Lighthouse terminé avec un score de ${result.score}`);
      return result;
    } finally {
      await chrome.kill();
    }
  }

  /**
   * Exécuter un test de charge
   */
  private async runLoadTest(request: BenchmarkRequest): Promise<LoadTestingResult> {
    this.logger.info(`Démarrage du test de charge pour ${request.target.url}`);

    const tool = this.config.loadTesting.tool;
    const options = request.configuration.loadTesting || {
      duration: this.config.loadTesting.duration,
      connections: this.config.loadTesting.connections,
      rps: this.config.loadTesting.rps
    };

    if (tool === 'autocannon') {
      return await this.runAutocannonTest(request.target.url!, options);
    } else if (tool === 'k6') {
      return await this.runK6Test(request.target.url!, options);
    } else {
      throw new Error(`Outil de test de charge non supporté: ${tool}`);
    }
  }

  /**
   * Exécuter un test avec Autocannon
   */
  private async runAutocannonTest(url: string, options: any): Promise<LoadTestingResult> {
    return new Promise((resolve, reject) => {
      const args = [
        '-c', options.connections.toString(),
        '-d', options.duration,
        '--json'
      ];

      if (options.rps) {
        args.push('-R', options.rps.toString());
      }

      args.push(url);

      const autocannon = spawn('autocannon', args);
      let output = '';

      autocannon.stdout.on('data', (data) => {
        output += data.toString();
      });

      autocannon.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output);
            resolve({
              requestsPerSecond: result.requests?.average || 0,
              averageLatency: result.latency?.average || 0,
              p95Latency: result.latency?.p95 || 0,
              p99Latency: result.latency?.p99 || 0,
              errorRate: result.requests?.total ? (result.errors / result.requests.total) * 100 : 0,
              totalRequests: result.requests?.total || 0,
              totalErrors: result.errors || 0,
              throughput: result.throughput?.average || 0,
              connectionErrors: result.connectionErrors || 0,
              timeouts: result.timeouts || 0
            });
          } catch (error) {
            reject(new Error(`Erreur lors du parsing des résultats Autocannon: ${error.message}`));
          }
        } else {
          reject(new Error(`Autocannon a échoué avec le code ${code}`));
        }
      });
    });
  }

  /**
   * Exécuter un test avec K6
   */
  private async runK6Test(url: string, options: any): Promise<LoadTestingResult> {
    // Créer un script K6 temporaire
    const script = `
      import http from 'k6/http';
      import { check } from 'k6';

      export let options = {
        duration: '${options.duration}',
        vus: ${options.connections},
        ${options.rps ? `rps: ${options.rps},` : ''}
      };

      export default function() {
        let response = http.get('${url}');
        check(response, {
          'status is 200': (r) => r.status === 200,
        });
      }
    `;

    const scriptPath = path.join(this.resultsDir, `k6-script-${Date.now()}.js`);
    await fs.writeFile(scriptPath, script);

    return new Promise((resolve, reject) => {
      const k6 = spawn('k6', ['run', '--out', 'json', scriptPath]);
      let output = '';

      k6.stdout.on('data', (data) => {
        output += data.toString();
      });

      k6.on('close', async (code) => {
        // Nettoyer le script temporaire
        await fs.unlink(scriptPath).catch(() => {});

        if (code === 0) {
          try {
            // Parser les résultats K6 (format JSON lines)
            const lines = output.trim().split('\n');
            const metrics = lines
              .filter(line => line.startsWith('{"type":"Point"'))
              .map(line => JSON.parse(line));

            // Calculer les statistiques
            const httpReqs = metrics.filter(m => m.metric === 'http_reqs');
            const httpReqDuration = metrics.filter(m => m.metric === 'http_req_duration');
            const httpReqFailed = metrics.filter(m => m.metric === 'http_req_failed');

            resolve({
              requestsPerSecond: httpReqs.length > 0 ? httpReqs[httpReqs.length - 1].data.value : 0,
              averageLatency: this.calculateAverage(httpReqDuration.map(m => m.data.value)),
              p95Latency: this.calculatePercentile(httpReqDuration.map(m => m.data.value), 95),
              p99Latency: this.calculatePercentile(httpReqDuration.map(m => m.data.value), 99),
              errorRate: this.calculateAverage(httpReqFailed.map(m => m.data.value)) * 100,
              totalRequests: httpReqs.length,
              totalErrors: httpReqFailed.filter(m => m.data.value > 0).length,
              throughput: 0, // À calculer selon les besoins
              connectionErrors: 0,
              timeouts: 0
            });
          } catch (error) {
            reject(new Error(`Erreur lors du parsing des résultats K6: ${error.message}`));
          }
        } else {
          reject(new Error(`K6 a échoué avec le code ${code}`));
        }
      });
    });
  }

  /**
   * Exécuter un test de profiling
   */
  private async runProfilingTest(request: BenchmarkRequest): Promise<ProfilingResult> {
    this.logger.info(`Démarrage du profiling pour ${request.target.service}`);

    // Simuler un profiling (à implémenter selon les besoins spécifiques)
    return {
      cpuProfile: {
        totalTime: 1000,
        idleTime: 200,
        userTime: 600,
        systemTime: 200,
        hotspots: [
          {
            function: 'processRequest',
            file: 'app.js',
            line: 42,
            selfTime: 150,
            totalTime: 300
          }
        ]
      },
      memoryProfile: {
        heapUsed: 50 * 1024 * 1024,
        heapTotal: 100 * 1024 * 1024,
        external: 5 * 1024 * 1024,
        rss: 120 * 1024 * 1024,
        leaks: []
      }
    };
  }

  /**
   * Extraire les opportunités d'optimisation de Lighthouse
   */
  private extractOpportunities(lhr: any): any[] {
    const opportunities = [];

    for (const [auditId, audit] of Object.entries(lhr.audits)) {
      if (audit.details && audit.details.type === 'opportunity' && audit.numericValue > 0) {
        opportunities.push({
          id: auditId,
          title: audit.title,
          description: audit.description,
          savings: audit.numericValue,
          impact: audit.numericValue > 1000 ? 'high' : audit.numericValue > 500 ? 'medium' : 'low'
        });
      }
    }

    return opportunities.sort((a, b) => b.savings - a.savings);
  }

  /**
   * Calculer la moyenne
   */
  private calculateAverage(values: number[]): number {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }

  /**
   * Calculer un percentile
   */
  private calculatePercentile(values: number[], percentile: number): number {
    if (values.length === 0) return 0;

    const sorted = values.sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }

  /**
   * S'assurer que le répertoire des résultats existe
   */
  private async ensureResultsDirectory(): Promise<void> {
    try {
      await fs.access(this.resultsDir);
    } catch {
      await fs.mkdir(this.resultsDir, { recursive: true });
    }
  }
}
