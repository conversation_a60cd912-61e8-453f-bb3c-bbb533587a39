{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/core/*": ["src/core/*"], "@/engines/*": ["src/engines/*"], "@/communication/*": ["src/communication/*"], "@/memory/*": ["src/memory/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}