{"name": "agent-migration", "version": "1.0.0", "description": "Agent spécialisé dans la migration de données et systèmes", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["migration", "data-migration", "system-migration", "database-migration", "legacy-migration", "etl"], "author": "Retreat And Be", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "kafkajs": "^2.2.4", "weaviate-ts-client": "^1.5.0", "redis": "^4.6.10", "dotenv": "^16.3.1", "joi": "^17.11.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "pg": "^8.11.3", "mysql2": "^3.6.5", "mongodb": "^6.3.0", "sqlite3": "^5.1.6", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "xlsx": "^0.18.5", "xml2js": "^0.6.2", "fast-xml-parser": "^4.3.2", "node-cron": "^3.0.3", "archiver": "^6.0.1", "unzipper": "^0.10.14"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "@types/pg": "^8.10.9", "@types/xml2js": "^0.4.14", "typescript": "^5.2.2", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}}