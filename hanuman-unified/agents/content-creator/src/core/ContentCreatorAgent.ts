import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

/**
 * Types pour l'Agent Content Creator
 */
export interface ContentRequest {
  id: string;
  type: 'blog_post' | 'social_media' | 'ad_copy' | 'email' | 'script' | 'product_description';
  topic: string;
  audience: AudienceProfile;
  tone: 'professional' | 'casual' | 'friendly' | 'authoritative' | 'humorous' | 'inspirational';
  length: 'short' | 'medium' | 'long';
  keywords?: string[];
  guidelines?: ContentGuidelines;
  brand?: BrandProfile;
  context?: string;
  deadline?: Date;
  createdAt: Date;
}

export interface AudienceProfile {
  demographics: {
    ageRange: [number, number];
    interests: string[];
    profession: string[];
    painPoints: string[];
  };
  psychographics: {
    values: string[];
    motivations: string[];
    challenges: string[];
  };
  behavior: {
    preferredChannels: string[];
    contentPreferences: string[];
    decisionFactors: string[];
  };
}

export interface ContentGuidelines {
  mustInclude: string[];
  mustAvoid: string[];
  callToAction?: string;
  structure?: string[];
  style?: string;
  references?: string[];
}

export interface BrandProfile {
  name: string;
  voice: string;
  personality: string[];
  values: string[];
  mission: string;
  uniqueSellingProposition: string;
  competitors: string[];
}

export interface GeneratedContent {
  id: string;
  requestId: string;
  type: string;
  title: string;
  content: string;
  metadata: ContentMetadata;
  seoOptimization: SEOOptimization;
  variations: ContentVariation[];
  qualityScore: number;
  readabilityScore: number;
  engagementPrediction: number;
  createdAt: Date;
}

export interface ContentMetadata {
  wordCount: number;
  characterCount: number;
  readingTime: number;
  complexity: 'beginner' | 'intermediate' | 'advanced';
  sentiment: 'positive' | 'neutral' | 'negative';
  topics: string[];
  entities: string[];
}

export interface SEOOptimization {
  primaryKeyword: string;
  secondaryKeywords: string[];
  keywordDensity: Record<string, number>;
  metaTitle: string;
  metaDescription: string;
  headings: string[];
  internalLinkSuggestions: string[];
}

export interface ContentVariation {
  id: string;
  name: string;
  content: string;
  purpose: string;
  targetSegment: string;
}

export interface ContentTemplate {
  id: string;
  name: string;
  type: string;
  structure: string[];
  placeholders: Record<string, string>;
  guidelines: string[];
  examples: string[];
}

export interface StyleGuide {
  id: string;
  brand: string;
  voice: string;
  tone: string[];
  vocabulary: {
    preferred: string[];
    avoid: string[];
  };
  formatting: {
    headings: string;
    paragraphs: string;
    lists: string;
  };
  examples: string[];
}

/**
 * Agent Content Creator Principal
 * Responsable de la génération de contenu avec Ollama
 */
export class ContentCreatorAgent extends EventEmitter {
  private logger: Logger;
  private isInitialized: boolean = false;
  private ollamaUrl: string;
  private defaultModel: string = 'llama2';

  // Templates de contenu par type
  private readonly contentTemplates = {
    'blog_post': {
      structure: ['introduction', 'main_points', 'examples', 'conclusion', 'call_to_action'],
      lengthGuide: { short: 500, medium: 1000, long: 2000 }
    },
    'social_media': {
      structure: ['hook', 'value', 'call_to_action'],
      lengthGuide: { short: 50, medium: 100, long: 200 }
    },
    'ad_copy': {
      structure: ['headline', 'problem', 'solution', 'benefits', 'call_to_action'],
      lengthGuide: { short: 50, medium: 100, long: 150 }
    },
    'email': {
      structure: ['subject', 'greeting', 'value_proposition', 'details', 'call_to_action', 'signature'],
      lengthGuide: { short: 100, medium: 300, long: 500 }
    },
    'script': {
      structure: ['hook', 'introduction', 'main_content', 'conclusion', 'call_to_action'],
      lengthGuide: { short: 200, medium: 500, long: 1000 }
    }
  };

  constructor(logger: Logger) {
    super();
    this.logger = logger;
    this.ollamaUrl = process.env.OLLAMA_URL || 'http://localhost:11434';
  }

  /**
   * Initialise l'agent content creator
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initialisation de l\'Agent Content Creator...');

      // Vérification de la connexion Ollama
      await this.checkOllamaConnection();

      // Chargement du modèle par défaut
      await this.loadModel(this.defaultModel);

      this.isInitialized = true;
      this.logger.info('Agent Content Creator initialisé avec succès');
      this.emit('initialized');

    } catch (error) {
      this.logger.error('Erreur lors de l\'initialisation de l\'Agent Content Creator:', error);
      throw error;
    }
  }

  /**
   * Génère du contenu basé sur une requête
   */
  async generateContent(request: ContentRequest): Promise<GeneratedContent> {
    if (!this.isInitialized) {
      throw new Error('Agent Content Creator non initialisé');
    }

    this.logger.info(`Génération de contenu: ${request.type} sur ${request.topic}`);

    try {
      const startTime = Date.now();

      // Préparation du prompt
      const prompt = this.buildContentPrompt(request);

      // Génération du contenu principal
      const content = await this.callOllama(prompt);

      // Génération du titre
      const title = await this.generateTitle(request, content);

      // Post-traitement
      const processedContent = await this.postProcessContent(content, request);

      // Optimisation SEO
      const seoOptimization = await this.optimizeForSEO(processedContent, request);

      // Génération de variations
      const variations = await this.generateVariations(request, processedContent);

      // Analyse de qualité
      const qualityScore = await this.analyzeQuality(processedContent, request);
      const readabilityScore = await this.analyzeReadability(processedContent);
      const engagementPrediction = await this.predictEngagement(processedContent, request);

      // Métadonnées
      const metadata = await this.extractMetadata(processedContent);

      const result: GeneratedContent = {
        id: uuidv4(),
        requestId: request.id,
        type: request.type,
        title,
        content: processedContent,
        metadata,
        seoOptimization,
        variations,
        qualityScore,
        readabilityScore,
        engagementPrediction,
        createdAt: new Date()
      };

      this.logger.info(`Contenu généré: ${result.id}, qualité: ${qualityScore}`);
      this.emit('contentGenerated', result);

      return result;

    } catch (error) {
      this.logger.error('Erreur lors de la génération de contenu:', error);
      throw error;
    }
  }

  /**
   * Génère du contenu pour les réseaux sociaux
   */
  async generateSocialMediaContent(
    topic: string,
    platform: string,
    audience: AudienceProfile,
    brand?: BrandProfile
  ): Promise<GeneratedContent[]> {
    this.logger.info(`Génération de contenu social media pour ${platform}`);

    try {
      const posts: GeneratedContent[] = [];
      const platformSpecs = this.getSocialMediaSpecs(platform);

      // Génération de plusieurs posts
      for (let i = 0; i < 3; i++) {
        const request: ContentRequest = {
          id: uuidv4(),
          type: 'social_media',
          topic,
          audience,
          tone: this.selectToneForPlatform(platform),
          length: platformSpecs.length,
          brand,
          context: `Platform: ${platform}`,
          createdAt: new Date()
        };

        const content = await this.generateContent(request);
        
        // Adaptation spécifique à la plateforme
        content.content = await this.adaptForPlatform(content.content, platform);
        
        posts.push(content);
      }

      this.logger.info(`${posts.length} posts générés pour ${platform}`);
      this.emit('socialMediaContentGenerated', { platform, posts });

      return posts;

    } catch (error) {
      this.logger.error('Erreur lors de la génération de contenu social media:', error);
      throw error;
    }
  }

  /**
   * Génère un article de blog optimisé SEO
   */
  async generateBlogPost(
    topic: string,
    keywords: string[],
    audience: AudienceProfile,
    length: 'short' | 'medium' | 'long' = 'medium'
  ): Promise<GeneratedContent> {
    this.logger.info(`Génération d'article de blog: ${topic}`);

    try {
      const request: ContentRequest = {
        id: uuidv4(),
        type: 'blog_post',
        topic,
        audience,
        tone: 'professional',
        length,
        keywords,
        guidelines: {
          mustInclude: keywords,
          mustAvoid: ['jargon technique excessif'],
          structure: ['introduction', 'développement', 'conclusion'],
          callToAction: 'Contactez-nous pour en savoir plus'
        },
        createdAt: new Date()
      };

      const content = await this.generateContent(request);

      // Optimisation supplémentaire pour le blog
      content.content = await this.optimizeForBlog(content.content, keywords);

      this.logger.info(`Article de blog généré: ${content.id}`);
      this.emit('blogPostGenerated', content);

      return content;

    } catch (error) {
      this.logger.error('Erreur lors de la génération d\'article de blog:', error);
      throw error;
    }
  }

  /**
   * Génère du copy publicitaire
   */
  async generateAdCopy(
    product: string,
    audience: AudienceProfile,
    objective: 'awareness' | 'consideration' | 'conversion',
    platform: string
  ): Promise<GeneratedContent[]> {
    this.logger.info(`Génération de copy publicitaire pour ${product}`);

    try {
      const copies: GeneratedContent[] = [];
      const variations = ['emotional', 'rational', 'urgency'];

      for (const variation of variations) {
        const request: ContentRequest = {
          id: uuidv4(),
          type: 'ad_copy',
          topic: product,
          audience,
          tone: this.getToneForVariation(variation),
          length: 'short',
          context: `Objective: ${objective}, Platform: ${platform}, Variation: ${variation}`,
          guidelines: {
            mustInclude: [product],
            callToAction: this.getCallToActionForObjective(objective)
          },
          createdAt: new Date()
        };

        const content = await this.generateContent(request);
        content.variations[0].name = variation;
        copies.push(content);
      }

      this.logger.info(`${copies.length} copies publicitaires générées`);
      this.emit('adCopyGenerated', { product, copies });

      return copies;

    } catch (error) {
      this.logger.error('Erreur lors de la génération de copy publicitaire:', error);
      throw error;
    }
  }

  /**
   * Vérifie la connexion Ollama
   */
  private async checkOllamaConnection(): Promise<void> {
    try {
      const response = await axios.get(`${this.ollamaUrl}/api/tags`);
      this.logger.info('Connexion Ollama établie');
    } catch (error) {
      throw new Error(`Impossible de se connecter à Ollama: ${error.message}`);
    }
  }

  /**
   * Charge un modèle Ollama
   */
  private async loadModel(model: string): Promise<void> {
    try {
      const response = await axios.get(`${this.ollamaUrl}/api/tags`);
      const models = response.data.models || [];
      
      const modelExists = models.some((m: any) => m.name.includes(model));
      
      if (!modelExists) {
        this.logger.warn(`Modèle ${model} non trouvé, utilisation du modèle par défaut`);
      }

      this.logger.info(`Modèle ${model} prêt`);

    } catch (error) {
      this.logger.error(`Erreur lors du chargement du modèle ${model}:`, error);
      throw error;
    }
  }

  /**
   * Construit le prompt de génération de contenu
   */
  private buildContentPrompt(request: ContentRequest): string {
    const template = this.contentTemplates[request.type];
    const targetLength = template?.lengthGuide[request.length] || 500;

    let prompt = `Create ${request.type.replace('_', ' ')} content about "${request.topic}".\n\n`;

    // Informations sur l'audience
    prompt += `Target Audience:\n`;
    prompt += `- Age: ${request.audience.demographics.ageRange[0]}-${request.audience.demographics.ageRange[1]}\n`;
    prompt += `- Interests: ${request.audience.demographics.interests.join(', ')}\n`;
    prompt += `- Pain Points: ${request.audience.demographics.painPoints.join(', ')}\n\n`;

    // Ton et style
    prompt += `Tone: ${request.tone}\n`;
    prompt += `Target Length: approximately ${targetLength} words\n\n`;

    // Mots-clés
    if (request.keywords && request.keywords.length > 0) {
      prompt += `Keywords to include: ${request.keywords.join(', ')}\n\n`;
    }

    // Guidelines
    if (request.guidelines) {
      if (request.guidelines.mustInclude.length > 0) {
        prompt += `Must include: ${request.guidelines.mustInclude.join(', ')}\n`;
      }
      if (request.guidelines.mustAvoid.length > 0) {
        prompt += `Must avoid: ${request.guidelines.mustAvoid.join(', ')}\n`;
      }
      if (request.guidelines.callToAction) {
        prompt += `Call to action: ${request.guidelines.callToAction}\n`;
      }
    }

    // Structure
    if (template?.structure) {
      prompt += `\nStructure the content with these sections: ${template.structure.join(', ')}\n`;
    }

    // Brand
    if (request.brand) {
      prompt += `\nBrand Voice: ${request.brand.voice}\n`;
      prompt += `Brand Values: ${request.brand.values.join(', ')}\n`;
    }

    prompt += `\nGenerate engaging, high-quality content that resonates with the target audience.`;

    return prompt;
  }

  /**
   * Appelle Ollama pour la génération
   */
  private async callOllama(prompt: string): Promise<string> {
    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.defaultModel,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.7, // Créativité modérée
          top_p: 0.9,
          top_k: 40,
          num_predict: 2000 // Limite de tokens
        }
      });

      return response.data.response.trim();

    } catch (error) {
      this.logger.error('Erreur lors de l\'appel Ollama:', error);
      throw new Error(`Erreur Ollama: ${error.message}`);
    }
  }

  /**
   * Génère un titre pour le contenu
   */
  private async generateTitle(request: ContentRequest, content: string): Promise<string> {
    const titlePrompt = `Generate a compelling title for this ${request.type.replace('_', ' ')} about "${request.topic}". The title should be engaging and optimized for ${request.tone} tone. Content preview: ${content.substring(0, 200)}...`;
    
    const title = await this.callOllama(titlePrompt);
    return title.replace(/['"]/g, '').trim();
  }

  /**
   * Post-traite le contenu généré
   */
  private async postProcessContent(content: string, request: ContentRequest): Promise<string> {
    let processedContent = content;

    // Nettoyage du contenu
    processedContent = processedContent.replace(/\n{3,}/g, '\n\n'); // Limite les sauts de ligne
    processedContent = processedContent.trim();

    // Ajout de formatage selon le type
    if (request.type === 'blog_post') {
      processedContent = this.formatBlogPost(processedContent);
    } else if (request.type === 'social_media') {
      processedContent = this.formatSocialMedia(processedContent);
    }

    return processedContent;
  }

  /**
   * Optimise le contenu pour le SEO
   */
  private async optimizeForSEO(content: string, request: ContentRequest): Promise<SEOOptimization> {
    const keywords = request.keywords || [];
    const primaryKeyword = keywords[0] || request.topic;

    return {
      primaryKeyword,
      secondaryKeywords: keywords.slice(1),
      keywordDensity: this.calculateKeywordDensity(content, keywords),
      metaTitle: await this.generateMetaTitle(request.topic, primaryKeyword),
      metaDescription: await this.generateMetaDescription(content, primaryKeyword),
      headings: this.extractHeadings(content),
      internalLinkSuggestions: await this.suggestInternalLinks(content, request.topic)
    };
  }

  /**
   * Génère des variations du contenu
   */
  private async generateVariations(request: ContentRequest, content: string): Promise<ContentVariation[]> {
    const variations: ContentVariation[] = [];

    // Variation pour différents segments d'audience
    const segments = ['decision_makers', 'technical_users', 'end_users'];

    for (const segment of segments) {
      const variationPrompt = `Adapt this content for ${segment}: ${content.substring(0, 500)}...`;
      const variationContent = await this.callOllama(variationPrompt);

      variations.push({
        id: uuidv4(),
        name: `${segment}_version`,
        content: variationContent,
        purpose: `Optimized for ${segment}`,
        targetSegment: segment
      });
    }

    return variations;
  }

  /**
   * Analyse la qualité du contenu
   */
  private async analyzeQuality(content: string, request: ContentRequest): Promise<number> {
    let score = 70; // Score de base

    // Facteurs de qualité
    const wordCount = content.split(' ').length;
    const targetLength = this.contentTemplates[request.type]?.lengthGuide[request.length] || 500;

    // Vérification de la longueur
    const lengthRatio = wordCount / targetLength;
    if (lengthRatio >= 0.8 && lengthRatio <= 1.2) {
      score += 10;
    }

    // Vérification des mots-clés
    if (request.keywords) {
      const keywordScore = this.calculateKeywordScore(content, request.keywords);
      score += keywordScore;
    }

    // Vérification de la structure
    if (this.hasGoodStructure(content, request.type)) {
      score += 10;
    }

    // Vérification de l'engagement
    if (this.hasEngagingElements(content)) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Analyse la lisibilité
   */
  private async analyzeReadability(content: string): Promise<number> {
    // Simulation d'analyse de lisibilité (Flesch-Kincaid, etc.)
    const sentences = content.split(/[.!?]+/).length;
    const words = content.split(' ').length;
    const avgWordsPerSentence = words / sentences;

    let score = 80;
    if (avgWordsPerSentence > 20) score -= 10;
    if (avgWordsPerSentence > 25) score -= 10;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Prédit l'engagement
   */
  private async predictEngagement(content: string, request: ContentRequest): Promise<number> {
    // Simulation de prédiction d'engagement
    let score = 60;

    // Facteurs d'engagement
    if (content.includes('?')) score += 5; // Questions engageantes
    if (content.includes('!')) score += 3; // Exclamations
    if (this.hasCallToAction(content)) score += 10;
    if (this.hasEmotionalWords(content)) score += 7;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Extrait les métadonnées du contenu
   */
  private async extractMetadata(content: string): Promise<ContentMetadata> {
    const words = content.split(' ');
    const readingTime = Math.ceil(words.length / 200); // 200 mots par minute

    return {
      wordCount: words.length,
      characterCount: content.length,
      readingTime,
      complexity: this.assessComplexity(content),
      sentiment: this.analyzeSentiment(content),
      topics: await this.extractTopics(content),
      entities: await this.extractEntities(content)
    };
  }

  // Méthodes utilitaires
  private getSocialMediaSpecs(platform: string): any {
    const specs = {
      'facebook': { length: 'medium', maxChars: 500 },
      'twitter': { length: 'short', maxChars: 280 },
      'linkedin': { length: 'long', maxChars: 700 },
      'instagram': { length: 'medium', maxChars: 300 }
    };
    return specs[platform] || specs['facebook'];
  }

  private selectToneForPlatform(platform: string): any {
    const tones = {
      'facebook': 'friendly',
      'twitter': 'casual',
      'linkedin': 'professional',
      'instagram': 'inspirational'
    };
    return tones[platform] || 'friendly';
  }

  private getToneForVariation(variation: string): any {
    const tones = {
      'emotional': 'inspirational',
      'rational': 'professional',
      'urgency': 'authoritative'
    };
    return tones[variation] || 'professional';
  }

  private getCallToActionForObjective(objective: string): string {
    const ctas = {
      'awareness': 'Learn more',
      'consideration': 'Get a free trial',
      'conversion': 'Buy now'
    };
    return ctas[objective] || 'Learn more';
  }

  private formatBlogPost(content: string): string {
    // Ajout de formatage Markdown pour les articles de blog
    return content;
  }

  private formatSocialMedia(content: string): string {
    // Formatage spécifique aux réseaux sociaux
    return content;
  }

  private calculateKeywordDensity(content: string, keywords: string[]): Record<string, number> {
    const density: Record<string, number> = {};
    const words = content.toLowerCase().split(' ');
    const totalWords = words.length;

    keywords.forEach(keyword => {
      const keywordWords = keyword.toLowerCase().split(' ');
      let count = 0;
      
      for (let i = 0; i <= words.length - keywordWords.length; i++) {
        const phrase = words.slice(i, i + keywordWords.length).join(' ');
        if (phrase === keyword.toLowerCase()) {
          count++;
        }
      }
      
      density[keyword] = (count / totalWords) * 100;
    });

    return density;
  }

  private async generateMetaTitle(topic: string, keyword: string): Promise<string> {
    return `${topic} - ${keyword} | Retreat & Be`;
  }

  private async generateMetaDescription(content: string, keyword: string): Promise<string> {
    const excerpt = content.substring(0, 150);
    return `${excerpt}... Découvrez plus sur ${keyword}.`;
  }

  private extractHeadings(content: string): string[] {
    // Extraction des titres du contenu
    const headings = content.match(/^#+\s+(.+)$/gm) || [];
    return headings.map(h => h.replace(/^#+\s+/, ''));
  }

  private async suggestInternalLinks(content: string, topic: string): Promise<string[]> {
    // Suggestions de liens internes basées sur le contenu
    return [`/blog/${topic.toLowerCase().replace(/\s+/g, '-')}`];
  }

  private calculateKeywordScore(content: string, keywords: string[]): number {
    let score = 0;
    keywords.forEach(keyword => {
      if (content.toLowerCase().includes(keyword.toLowerCase())) {
        score += 5;
      }
    });
    return Math.min(score, 20);
  }

  private hasGoodStructure(content: string, type: string): boolean {
    // Vérification de la structure selon le type de contenu
    return content.includes('\n') && content.length > 100;
  }

  private hasEngagingElements(content: string): boolean {
    return content.includes('?') || content.includes('!') || content.includes('vous');
  }

  private hasCallToAction(content: string): boolean {
    const ctas = ['contactez', 'découvrez', 'essayez', 'téléchargez', 'inscrivez'];
    return ctas.some(cta => content.toLowerCase().includes(cta));
  }

  private hasEmotionalWords(content: string): boolean {
    const emotional = ['incroyable', 'fantastique', 'révolutionnaire', 'unique', 'exceptionnel'];
    return emotional.some(word => content.toLowerCase().includes(word));
  }

  private assessComplexity(content: string): 'beginner' | 'intermediate' | 'advanced' {
    const avgWordLength = content.replace(/\s/g, '').length / content.split(' ').length;
    if (avgWordLength < 5) return 'beginner';
    if (avgWordLength < 7) return 'intermediate';
    return 'advanced';
  }

  private analyzeSentiment(content: string): 'positive' | 'neutral' | 'negative' {
    // Analyse de sentiment simplifiée
    const positive = ['excellent', 'fantastique', 'merveilleux', 'génial'];
    const negative = ['terrible', 'horrible', 'mauvais', 'problème'];
    
    const positiveCount = positive.filter(word => content.toLowerCase().includes(word)).length;
    const negativeCount = negative.filter(word => content.toLowerCase().includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private async extractTopics(content: string): Promise<string[]> {
    // Extraction de sujets du contenu
    return ['marketing', 'business', 'technology'];
  }

  private async extractEntities(content: string): Promise<string[]> {
    // Extraction d'entités nommées
    return ['Retreat & Be', 'AI', 'Marketing'];
  }

  private async adaptForPlatform(content: string, platform: string): Promise<string> {
    const specs = this.getSocialMediaSpecs(platform);
    
    if (content.length > specs.maxChars) {
      return content.substring(0, specs.maxChars - 3) + '...';
    }
    
    return content;
  }

  private async optimizeForBlog(content: string, keywords: string[]): Promise<string> {
    // Optimisation spécifique pour les articles de blog
    return content;
  }

  /**
   * Arrête l'agent
   */
  async shutdown(): Promise<void> {
    this.logger.info('Arrêt de l\'Agent Content Creator...');
    this.isInitialized = false;
    this.logger.info('Agent Content Creator arrêté avec succès');
    this.emit('shutdown');
  }
}
