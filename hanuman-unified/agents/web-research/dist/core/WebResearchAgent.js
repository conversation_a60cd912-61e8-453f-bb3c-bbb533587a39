"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebResearchAgent = void 0;
const events_1 = require("events");
const WebScrapingEngine_1 = require("../engines/WebScrapingEngine");
const CompetitiveAnalysisEngine_1 = require("../engines/CompetitiveAnalysisEngine");
const TrendDetectionEngine_1 = require("../engines/TrendDetectionEngine");
const MarketResearchEngine_1 = require("../engines/MarketResearchEngine");
const MonitoringEngine_1 = require("../engines/MonitoringEngine");
const WeaviateMemory_1 = require("../memory/WeaviateMemory");
const KafkaCommunication_1 = require("../communication/KafkaCommunication");
/**
 * Agent Web Research - Recherche web intelligente et veille concurrentielle
 */
class WebResearchAgent extends events_1.EventEmitter {
    constructor(config, logger) {
        super();
        this.isInitialized = false;
        this.config = config;
        this.logger = logger;
        // Initialiser les engines
        this.webScrapingEngine = new WebScrapingEngine_1.WebScrapingEngine(config, logger);
        this.competitiveAnalysisEngine = new CompetitiveAnalysisEngine_1.CompetitiveAnalysisEngine(config, logger);
        this.trendDetectionEngine = new TrendDetectionEngine_1.TrendDetectionEngine(config, logger);
        this.marketResearchEngine = new MarketResearchEngine_1.MarketResearchEngine(config, logger);
        this.monitoringEngine = new MonitoringEngine_1.MonitoringEngine(config, logger);
        // Initialiser la mémoire et communication
        this.memory = new WeaviateMemory_1.WeaviateMemory(config.weaviate, logger);
        this.communication = new KafkaCommunication_1.KafkaCommunication(config.kafka, logger);
    }
    /**
     * Initialise l'agent
     */
    async initialize() {
        try {
            this.logger.info('Initializing Web Research Agent...');
            // Initialiser les composants
            await this.memory.initialize();
            await this.communication.initialize();
            await this.webScrapingEngine.initialize();
            await this.competitiveAnalysisEngine.initialize();
            await this.trendDetectionEngine.initialize();
            await this.marketResearchEngine.initialize();
            await this.monitoringEngine.initialize();
            // Configurer les listeners
            this.setupEventListeners();
            this.isInitialized = true;
            this.logger.info('Web Research Agent initialized successfully');
            this.emit('initialized');
        }
        catch (error) {
            this.logger.error('Failed to initialize Web Research Agent:', error);
            throw error;
        }
    }
    /**
     * Effectue une recherche web intelligente
     */
    async conductResearch(request) {
        try {
            this.logger.info(`Conducting research for query: ${request.query}`);
            // Valider la requête
            this.validateResearchRequest(request);
            // Effectuer la recherche selon les sources
            const searchResults = await this.webScrapingEngine.search(request);
            // Analyser les résultats
            const analysis = await this.marketResearchEngine.analyzeResults(searchResults, request);
            // Détecter les tendances
            const trends = await this.trendDetectionEngine.detectTrends(searchResults, request.query);
            // Analyser la concurrence
            const competitors = await this.competitiveAnalysisEngine.analyzeCompetitors(searchResults);
            // Construire le résultat
            const result = {
                id: this.generateId(),
                query: request.query,
                results: searchResults,
                analysis,
                trends,
                competitors,
                metadata: {
                    timestamp: new Date(),
                    duration: Date.now() - Date.now(), // À calculer correctement
                    totalResults: searchResults.length,
                    sources: request.sources.map(s => s.type)
                }
            };
            // Sauvegarder en mémoire
            await this.memory.storeResearchResult(result);
            // Notifier les autres agents
            await this.communication.publishResearchResult(result);
            this.logger.info(`Research completed for query: ${request.query}`);
            return result;
        }
        catch (error) {
            this.logger.error('Research failed:', error);
            throw error;
        }
    }
    /**
     * Analyse les tendances design pour l'agent UI/UX
     */
    async analyzeDesignTrends(industry) {
        try {
            this.logger.info(`Analyzing design trends for industry: ${industry}`);
            // Rechercher les tendances design
            const designSources = await this.webScrapingEngine.searchDesignSources(industry);
            // Analyser les patterns UI
            const uiPatterns = await this.competitiveAnalysisEngine.analyzeUIPatterns(designSources);
            // Détecter les tendances couleurs
            const colorTrends = await this.trendDetectionEngine.analyzeColorTrends(designSources);
            // Analyser la typographie
            const typographyTrends = await this.trendDetectionEngine.analyzeTypographyTrends(designSources);
            // Analyser les interactions
            const interactionTrends = await this.trendDetectionEngine.analyzeInteractionPatterns(designSources);
            const trends = {
                colorTrends,
                typographyTrends,
                layoutTrends: uiPatterns.layouts,
                interactionTrends,
                industry,
                timeframe: 'last_6_months',
                confidence: 0.85
            };
            // Sauvegarder les tendances
            await this.memory.storeDesignTrends(trends);
            this.logger.info(`Design trends analysis completed for industry: ${industry}`);
            return trends;
        }
        catch (error) {
            this.logger.error('Design trends analysis failed:', error);
            throw error;
        }
    }
    /**
     * Analyse le comportement utilisateur
     */
    async analyzeUserBehavior(domain) {
        try {
            this.logger.info(`Analyzing user behavior for domain: ${domain}`);
            // Collecter les données comportementales
            const behaviorData = await this.webScrapingEngine.collectBehaviorData(domain);
            // Analyser les patterns
            const patterns = await this.marketResearchEngine.analyzeBehaviorPatterns(behaviorData);
            // Analyser les préférences
            const preferences = await this.marketResearchEngine.analyzeUserPreferences(behaviorData);
            // Analyser les démographiques
            const demographics = await this.marketResearchEngine.analyzeDemographics(behaviorData);
            // Analyser l'usage des devices
            const devices = await this.marketResearchEngine.analyzeDeviceUsage(behaviorData);
            const userBehavior = {
                patterns,
                preferences,
                demographics,
                devices,
                timeframe: 'last_30_days'
            };
            // Sauvegarder les données
            await this.memory.storeUserBehaviorData(userBehavior);
            this.logger.info(`User behavior analysis completed for domain: ${domain}`);
            return userBehavior;
        }
        catch (error) {
            this.logger.error('User behavior analysis failed:', error);
            throw error;
        }
    }
    /**
     * Effectue une analyse UX concurrentielle
     */
    async performCompetitorUXAnalysis(competitors) {
        try {
            this.logger.info(`Performing UX analysis for ${competitors.length} competitors`);
            const analyses = [];
            for (const competitor of competitors) {
                const analysis = await this.competitiveAnalysisEngine.analyzeCompetitorUX(competitor);
                analyses.push(analysis);
            }
            // Sauvegarder les analyses
            await this.memory.storeCompetitorUXAnalyses(analyses);
            this.logger.info(`Competitor UX analysis completed for ${competitors.length} competitors`);
            return analyses;
        }
        catch (error) {
            this.logger.error('Competitor UX analysis failed:', error);
            throw error;
        }
    }
    /**
     * Démarre le monitoring en temps réel
     */
    async startMonitoring(keywords, competitors) {
        try {
            this.logger.info('Starting real-time monitoring...');
            await this.monitoringEngine.startMonitoring(keywords, competitors);
            // Écouter les alertes
            this.monitoringEngine.on('alert', (alert) => {
                this.handleMonitoringAlert(alert);
            });
            this.logger.info('Real-time monitoring started successfully');
        }
        catch (error) {
            this.logger.error('Failed to start monitoring:', error);
            throw error;
        }
    }
    /**
     * Arrête le monitoring
     */
    async stopMonitoring() {
        try {
            await this.monitoringEngine.stopMonitoring();
            this.logger.info('Monitoring stopped');
        }
        catch (error) {
            this.logger.error('Failed to stop monitoring:', error);
            throw error;
        }
    }
    /**
     * Gère les alertes de monitoring
     */
    async handleMonitoringAlert(alert) {
        try {
            this.logger.info(`Received monitoring alert: ${alert.type} - ${alert.message}`);
            // Sauvegarder l'alerte
            await this.memory.storeAlert(alert);
            // Notifier les autres agents
            await this.communication.publishAlert(alert);
            this.emit('alert', alert);
        }
        catch (error) {
            this.logger.error('Failed to handle monitoring alert:', error);
        }
    }
    /**
     * Configure les listeners d'événements
     */
    setupEventListeners() {
        // Écouter les requêtes de recherche
        this.communication.on('research_request', async (request) => {
            try {
                const result = await this.conductResearch(request);
                await this.communication.publishResearchResult(result);
            }
            catch (error) {
                this.logger.error('Failed to handle research request:', error);
            }
        });
        // Écouter les demandes d'analyse de tendances design
        this.communication.on('design_trends_request', async (data) => {
            try {
                const trends = await this.analyzeDesignTrends(data.industry);
                await this.communication.publishDesignTrends(trends);
            }
            catch (error) {
                this.logger.error('Failed to handle design trends request:', error);
            }
        });
    }
    /**
     * Valide une requête de recherche
     */
    validateResearchRequest(request) {
        if (!request.query || request.query.trim().length === 0) {
            throw new Error('Query is required');
        }
        if (!request.sources || request.sources.length === 0) {
            throw new Error('At least one source is required');
        }
        if (!request.options) {
            throw new Error('Options are required');
        }
    }
    /**
     * Génère un ID unique
     */
    generateId() {
        return `research_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Arrête l'agent
     */
    async shutdown() {
        try {
            this.logger.info('Shutting down Web Research Agent...');
            await this.stopMonitoring();
            await this.communication.disconnect();
            await this.memory.disconnect();
            await this.webScrapingEngine.shutdown();
            this.isInitialized = false;
            this.logger.info('Web Research Agent shut down successfully');
        }
        catch (error) {
            this.logger.error('Error during shutdown:', error);
            throw error;
        }
    }
}
exports.WebResearchAgent = WebResearchAgent;
//# sourceMappingURL=WebResearchAgent.js.map