"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebScrapingEngine = void 0;
const puppeteer_1 = __importDefault(require("puppeteer"));
const axios_1 = __importDefault(require("axios"));
/**
 * Moteur de scraping web intelligent
 */
class WebScrapingEngine {
    constructor(config, logger) {
        this.browser = null;
        this.isInitialized = false;
        this.config = config;
        this.logger = logger;
    }
    /**
     * Initialise le moteur de scraping
     */
    async initialize() {
        try {
            this.logger.info('Initializing Web Scraping Engine...');
            // Lancer Puppeteer
            this.browser = await puppeteer_1.default.launch({
                headless: this.config.puppeteer.headless,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });
            this.isInitialized = true;
            this.logger.info('Web Scraping Engine initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize Web Scraping Engine:', error);
            throw error;
        }
    }
    /**
     * Effectue une recherche multi-sources
     */
    async search(request) {
        try {
            this.logger.info(`Searching for: ${request.query}`);
            const allResults = [];
            for (const source of request.sources) {
                let results = [];
                switch (source.type) {
                    case 'web':
                        results = await this.searchWeb(request.query, request.options);
                        break;
                    case 'news':
                        results = await this.searchNews(request.query, request.options);
                        break;
                    case 'academic':
                        results = await this.searchAcademic(request.query, request.options);
                        break;
                    case 'social':
                        results = await this.searchSocial(request.query, request.options);
                        break;
                    case 'competitor':
                        results = await this.searchCompetitors(request.query, source.domains || []);
                        break;
                }
                // Filtrer par domaines si spécifié
                if (source.domains && source.domains.length > 0) {
                    results = results.filter(r => source.domains.some(d => r.domain.includes(d)));
                }
                // Exclure les domaines si spécifié
                if (source.excludeDomains && source.excludeDomains.length > 0) {
                    results = results.filter(r => !source.excludeDomains.some(d => r.domain.includes(d)));
                }
                allResults.push(...results);
            }
            // Trier par score de pertinence
            allResults.sort((a, b) => b.relevanceScore - a.relevanceScore);
            // Limiter les résultats
            const limitedResults = allResults.slice(0, request.options.maxResults);
            this.logger.info(`Found ${limitedResults.length} results for: ${request.query}`);
            return limitedResults;
        }
        catch (error) {
            this.logger.error('Search failed:', error);
            throw error;
        }
    }
    /**
     * Recherche web générale
     */
    async searchWeb(query, options) {
        try {
            const results = [];
            // Utiliser l'API Google Search si configurée
            if (this.config.apis.googleSearch) {
                const googleResults = await this.searchGoogle(query, options);
                results.push(...googleResults);
            }
            // Utiliser l'API Bing Search si configurée
            if (this.config.apis.bingSearch) {
                const bingResults = await this.searchBing(query, options);
                results.push(...bingResults);
            }
            // Scraping direct de sites populaires
            const directResults = await this.searchDirect(query, options);
            results.push(...directResults);
            return results;
        }
        catch (error) {
            this.logger.error('Web search failed:', error);
            return [];
        }
    }
    /**
     * Recherche via Google Search API
     */
    async searchGoogle(query, options) {
        try {
            if (!this.config.apis.googleSearch) {
                return [];
            }
            const response = await axios_1.default.get('https://www.googleapis.com/customsearch/v1', {
                params: {
                    key: this.config.apis.googleSearch.apiKey,
                    cx: this.config.apis.googleSearch.searchEngineId,
                    q: query,
                    num: Math.min(options.maxResults || 10, 10),
                    lr: options.language ? `lang_${options.language}` : undefined,
                    gl: options.region || undefined
                }
            });
            const results = [];
            if (response.data.items) {
                for (const item of response.data.items) {
                    const result = {
                        id: this.generateId(),
                        title: item.title,
                        url: item.link,
                        snippet: item.snippet,
                        domain: new URL(item.link).hostname,
                        relevanceScore: 0.8,
                        credibilityScore: 0.7,
                        keywords: this.extractKeywords(item.title + ' ' + item.snippet),
                        entities: []
                    };
                    // Enrichir avec le contenu complet si nécessaire
                    if (options.depth === 'deep') {
                        result.content = await this.scrapePageContent(item.link);
                    }
                    results.push(result);
                }
            }
            return results;
        }
        catch (error) {
            this.logger.error('Google search failed:', error);
            return [];
        }
    }
    /**
     * Recherche via Bing Search API
     */
    async searchBing(query, options) {
        try {
            if (!this.config.apis.bingSearch) {
                return [];
            }
            const response = await axios_1.default.get('https://api.bing.microsoft.com/v7.0/search', {
                headers: {
                    'Ocp-Apim-Subscription-Key': this.config.apis.bingSearch.apiKey
                },
                params: {
                    q: query,
                    count: Math.min(options.maxResults || 10, 50),
                    mkt: options.region || 'en-US'
                }
            });
            const results = [];
            if (response.data.webPages && response.data.webPages.value) {
                for (const item of response.data.webPages.value) {
                    const result = {
                        id: this.generateId(),
                        title: item.name,
                        url: item.url,
                        snippet: item.snippet,
                        domain: new URL(item.url).hostname,
                        relevanceScore: 0.75,
                        credibilityScore: 0.7,
                        keywords: this.extractKeywords(item.name + ' ' + item.snippet),
                        entities: []
                    };
                    results.push(result);
                }
            }
            return results;
        }
        catch (error) {
            this.logger.error('Bing search failed:', error);
            return [];
        }
    }
    /**
     * Recherche directe sur des sites
     */
    async searchDirect(query, options) {
        try {
            const results = [];
            const searchSites = [
                'https://www.reddit.com/search',
                'https://stackoverflow.com/search',
                'https://medium.com/search'
            ];
            for (const site of searchSites) {
                try {
                    const siteResults = await this.scrapeSearchSite(site, query);
                    results.push(...siteResults);
                }
                catch (error) {
                    this.logger.warn(`Failed to search ${site}:`, error);
                }
            }
            return results;
        }
        catch (error) {
            this.logger.error('Direct search failed:', error);
            return [];
        }
    }
    /**
     * Scrape le contenu d'une page
     */
    async scrapePageContent(url) {
        try {
            if (!this.browser) {
                throw new Error('Browser not initialized');
            }
            const page = await this.browser.newPage();
            // Configurer la page
            await page.setUserAgent(this.config.puppeteer.userAgent);
            await page.setViewport({ width: 1920, height: 1080 });
            // Naviguer vers la page
            await page.goto(url, {
                waitUntil: 'networkidle2',
                timeout: this.config.puppeteer.timeout
            });
            // Extraire le contenu textuel
            const content = await page.evaluate(() => {
                // Supprimer les scripts et styles
                const scripts = document.querySelectorAll('script, style, nav, footer, aside');
                scripts.forEach(el => el.remove());
                // Extraire le texte principal
                const main = document.querySelector('main, article, .content, #content');
                if (main) {
                    return main.textContent?.trim() || '';
                }
                return document.body.textContent?.trim() || '';
            });
            await page.close();
            return content;
        }
        catch (error) {
            this.logger.error(`Failed to scrape content from ${url}:`, error);
            return '';
        }
    }
    /**
     * Recherche de sources design
     */
    async searchDesignSources(industry) {
        try {
            const designSites = [
                'dribbble.com',
                'behance.net',
                'awwwards.com',
                'designspiration.com',
                'pinterest.com'
            ];
            const query = `${industry} design trends 2024`;
            const results = [];
            for (const site of designSites) {
                const siteQuery = `site:${site} ${query}`;
                const siteResults = await this.searchWeb(siteQuery, { maxResults: 5 });
                results.push(...siteResults);
            }
            return results;
        }
        catch (error) {
            this.logger.error('Design sources search failed:', error);
            return [];
        }
    }
    /**
     * Collecte des données comportementales
     */
    async collectBehaviorData(domain) {
        try {
            // Simuler la collecte de données comportementales
            // En production, ceci se connecterait à des APIs d'analytics
            return {
                pageViews: Math.floor(Math.random() * 100000),
                sessions: Math.floor(Math.random() * 50000),
                bounceRate: Math.random() * 0.5 + 0.2,
                avgSessionDuration: Math.floor(Math.random() * 300) + 60,
                topPages: [
                    { page: '/', views: Math.floor(Math.random() * 10000) },
                    { page: '/products', views: Math.floor(Math.random() * 5000) },
                    { page: '/about', views: Math.floor(Math.random() * 3000) }
                ]
            };
        }
        catch (error) {
            this.logger.error('Behavior data collection failed:', error);
            return {};
        }
    }
    /**
     * Recherche d'actualités
     */
    async searchNews(query, options) {
        // Implémentation simplifiée - à étendre
        return [];
    }
    /**
     * Recherche académique
     */
    async searchAcademic(query, options) {
        // Implémentation simplifiée - à étendre
        return [];
    }
    /**
     * Recherche sur les réseaux sociaux
     */
    async searchSocial(query, options) {
        // Implémentation simplifiée - à étendre
        return [];
    }
    /**
     * Recherche chez les concurrents
     */
    async searchCompetitors(query, domains) {
        // Implémentation simplifiée - à étendre
        return [];
    }
    /**
     * Scrape un site de recherche
     */
    async scrapeSearchSite(site, query) {
        // Implémentation simplifiée - à étendre
        return [];
    }
    /**
     * Extrait les mots-clés d'un texte
     */
    extractKeywords(text) {
        const words = text.toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 3);
        return [...new Set(words)].slice(0, 10);
    }
    /**
     * Génère un ID unique
     */
    generateId() {
        return `result_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Arrête le moteur
     */
    async shutdown() {
        try {
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            this.isInitialized = false;
            this.logger.info('Web Scraping Engine shut down successfully');
        }
        catch (error) {
            this.logger.error('Error during Web Scraping Engine shutdown:', error);
        }
    }
}
exports.WebScrapingEngine = WebScrapingEngine;
//# sourceMappingURL=WebScrapingEngine.js.map