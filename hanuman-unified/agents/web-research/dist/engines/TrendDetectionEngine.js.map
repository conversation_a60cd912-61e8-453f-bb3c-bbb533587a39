{"version": 3, "file": "TrendDetectionEngine.js", "sourceRoot": "", "sources": ["../../src/engines/TrendDetectionEngine.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,oBAAoB;IAI/B,YAAY,MAAmB,EAAE,MAAc;QAC7C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,aAA6B,EAAE,KAAa;QAC7D,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAgB,EAAE,CAAC;YAE/B,mCAAmC;YACnC,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;YAErE,qCAAqC;YACrC,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAEjE,wBAAwB;YACxB,KAAK,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC9D,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC,gBAAgB;oBACpC,MAAM,KAAK,GAAc;wBACvB,OAAO;wBACP,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,cAAc,CAAC;wBAC5D,MAAM,EAAE,SAAS;wBACjB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,cAAc,CAAC;wBACrD,SAAS,EAAE,cAAc;wBACzB,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC;wBAC3D,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,aAAa,CAAC;qBAC3D,CAAC;oBACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,iCAAiC;YACjC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAEjF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM,SAAS,CAAC,CAAC;YACrD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,mBAAmB;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,aAA6B;QACpD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAE9C,wEAAwE;YACxE,MAAM,WAAW,GAAiB;gBAChC;oBACE,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;oBAChE,KAAK,EAAE,sBAAsB;oBAC7B,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC;iBAC5D;gBACD;oBACE,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;oBAChE,KAAK,EAAE,yBAAyB;oBAChC,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,CAAC,oBAAoB,EAAE,eAAe,EAAE,oBAAoB,CAAC;iBACvE;gBACD;oBACE,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;oBAChE,KAAK,EAAE,yBAAyB;oBAChC,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,eAAe,CAAC;iBAC5D;gBACD;oBACE,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;oBAChE,KAAK,EAAE,uBAAuB;oBAC9B,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,eAAe,CAAC;iBACrE;aACF,CAAC;YAEF,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,aAA6B;QACzD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAEnD,MAAM,gBAAgB,GAAsB;gBAC1C;oBACE,UAAU,EAAE,OAAO;oBACnB,KAAK,EAAE,oBAAoB;oBAC3B,KAAK,EAAE,0BAA0B;oBACjC,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,UAAU,EAAE,SAAS;oBACrB,KAAK,EAAE,sBAAsB;oBAC7B,KAAK,EAAE,wBAAwB;oBAC/B,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,UAAU,EAAE,QAAQ;oBACpB,KAAK,EAAE,mBAAmB;oBAC1B,KAAK,EAAE,yBAAyB;oBAChC,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,UAAU,EAAE,YAAY;oBACxB,KAAK,EAAE,qBAAqB;oBAC5B,KAAK,EAAE,2BAA2B;oBAClC,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,UAAU,EAAE,iBAAiB;oBAC7B,KAAK,EAAE,sBAAsB;oBAC7B,KAAK,EAAE,mBAAmB;oBAC1B,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,UAAU,EAAE,kBAAkB;oBAC9B,KAAK,EAAE,sBAAsB;oBAC7B,KAAK,EAAE,sBAAsB;oBAC7B,UAAU,EAAE,IAAI;iBACjB;aACF,CAAC;YAEF,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAAC,aAA6B;QAC5D,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAEtD,MAAM,iBAAiB,GAAuB;gBAC5C;oBACE,IAAI,EAAE,oBAAoB;oBAC1B,WAAW,EAAE,qCAAqC;oBAClD,cAAc,EAAE,2CAA2C;oBAC3D,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,oBAAoB;oBAC1B,WAAW,EAAE,yCAAyC;oBACtD,cAAc,EAAE,iCAAiC;oBACjD,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,eAAe;oBACrB,WAAW,EAAE,6CAA6C;oBAC1D,cAAc,EAAE,yBAAyB;oBACzC,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,oBAAoB;oBAC1B,WAAW,EAAE,8CAA8C;oBAC3D,cAAc,EAAE,wBAAwB;oBACxC,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,oBAAoB;oBAC1B,WAAW,EAAE,qCAAqC;oBAClD,cAAc,EAAE,sBAAsB;oBACtC,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,iBAAiB;oBACvB,WAAW,EAAE,uCAAuC;oBACpD,cAAc,EAAE,gBAAgB;oBAChC,UAAU,EAAE,IAAI;iBACjB;aACF,CAAC;YAEF,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,OAAuB;QACrD,MAAM,SAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE5C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,WAAW,GAAG;gBAClB,GAAG,MAAM,CAAC,QAAQ;gBAClB,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7C,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,OAAO,CAAC;aAChD,CAAC;YAEF,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;gBAClC,MAAM,iBAAiB,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;gBACvD,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACjC,SAAS,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,OAAuB;QACnD,MAAM,YAAY,GAAG,IAAI,GAAG,EAAe,CAAC;QAE5C,mCAAmC;QACnC,MAAM,OAAO,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAErD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YAEnE,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACtC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC/B,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;gBACxE,CAAC;gBACD,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,OAAe,EAAE,cAAgC;QAC/E,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI;YAAE,OAAO,QAAQ,CAAC;QAE3B,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAChE,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAEhD,IAAI,KAAK,GAAG,GAAG;YAAE,OAAO,QAAQ,CAAC;QACjC,IAAI,KAAK,GAAG,CAAC,GAAG;YAAE,OAAO,WAAW,CAAC;QACrC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAe,EAAE,cAAgC;QACvE,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI;YAAE,OAAO,CAAC,CAAC;QAEpB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QAEjC,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAAgB;QAC3C,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACxB,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/B,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3D,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3D,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QACpE,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAe,EAAE,OAAuB;QAC/D,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QAEvC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBAC1B,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAClC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAe,EAAE,OAAuB;QACnE,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAElC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,IAAY;QAC1C,IAAI,CAAC,IAAI;YAAE,OAAO,EAAE,CAAC;QAErB,OAAO,IAAI;aACR,WAAW,EAAE;aACb,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;aACxB,KAAK,CAAC,KAAK,CAAC;aACZ,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;aAC/B,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,IAAY;QAC7B,MAAM,SAAS,GAAG;YAChB,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;YACnE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM;YACpE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ;YACrE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;SAChE,CAAC;QAEF,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;CACF;AApWD,oDAoWC"}