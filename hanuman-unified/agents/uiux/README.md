# Agent UI/UX Design Thinking

Agent spécialisé dans la création automatisée de designs UX/UI optimisés pour la conversion, basé sur la recherche utilisateur et l'intelligence artificielle.

## 🎯 Capacités

### Recherche Utilisateur Automatique
- Collecte de données démographiques et comportementales
- Analyse concurrentielle automatisée
- Identification des pain points et motivations
- Évaluation des besoins d'accessibilité

### Génération de Personas Data-Driven
- Création de personas basés sur des données réelles
- Analyse psychographique approfondie
- Mapping des parcours utilisateur
- Identification des déclencheurs de conversion

### Design System Adaptatif
- Génération automatique de palettes de couleurs accessibles
- Système typographique optimisé
- Composants UI personnalisés
- Tokens de design pour les développeurs

### Wireframes Optimisés Conversion
- Parcours utilisateur optimisés
- Placement stratégique des CTA
- Intégration de signaux de confiance
- Optimisation mobile-first

### Tests d'Utilisabilité Simulés
- Simulation de tests basés sur les personas
- Génération de heatmaps prédictives
- Calcul de métriques de conversion
- Identification des points de friction

### Validation d'Accessibilité
- Conformité WCAG 2.1 AA/AAA
- Tests automatisés d'accessibilité
- Recommandations d'amélioration
- Validation du code généré

## 🚀 Installation

### Prérequis
- Node.js 18+
- Docker et Docker Compose
- Accès à Weaviate et Kafka

### Installation locale
```bash
cd agents/uiux
npm install
cp .env.example .env
# Configurer les variables d'environnement
npm run build
npm start
```

### Installation Docker
```bash
cd agents/uiux
docker build -t agent-uiux .
docker run -p 3005:3005 agent-uiux
```

## 📡 API Endpoints

### Santé et Status
- `GET /health` - État de santé de l'agent
- `GET /ready` - Vérification de disponibilité
- `GET /api/info` - Informations sur l'agent

### Design Complet
```bash
POST /api/design
{
  "requirements": {
    "industry": "wellness",
    "targetAudience": ["professionals", "wellness-seekers"],
    "brand": {
      "name": "Retreat And Be",
      "personality": ["authentic", "calming", "professional"],
      "values": ["wellness", "community", "growth"]
    },
    "competitors": ["competitor1.com", "competitor2.com"],
    "deviceTargets": ["mobile", "desktop"],
    "accessibilityLevel": "AA",
    "conversionGoals": ["booking", "newsletter-signup"]
  }
}
```

### Recherche Utilisateur
```bash
POST /api/research
{
  "requirements": {
    "industry": "wellness",
    "targetAudience": ["professionals"],
    "competitors": ["competitor1.com"]
  }
}
```

### Génération de Personas
```bash
POST /api/personas
{
  "userResearch": { /* données de recherche */ },
  "designIntelligence": { /* intelligence design */ }
}
```

### Design System
```bash
POST /api/design-system
{
  "requirements": { /* requirements */ },
  "personas": [ /* personas */ ],
  "intelligence": { /* design intelligence */ }
}
```

### Wireframes
```bash
POST /api/wireframes
{
  "personas": [ /* personas */ ],
  "designSystem": { /* design system */ }
}
```

### Bibliothèque de Composants
```bash
POST /api/components
{
  "wireframes": { /* wireframes */ },
  "designSystem": { /* design system */ }
}
```

### Validation d'Implémentation
```bash
POST /api/validate
{
  "code": "<!-- HTML/CSS/JS code -->",
  "design": { /* design original */ }
}
```

## 🔧 Configuration

### Variables d'Environnement
Voir `.env.example` pour la liste complète des variables.

### Communication Kafka
L'agent communique via Kafka avec les autres agents :
- **Topics de sortie** : `agent.uiux.design.complete`, `agent.uiux.components.ready`
- **Topics d'entrée** : `agent.uiux.design.request`, `agent.frontend.implementation.feedback`

### Mémoire Weaviate
Stockage vectoriel des données :
- Recherches utilisateur
- Personas générés
- Design systems
- Patterns de design réutilisables

## 🧠 Architecture

### Engines Spécialisés
- **UserResearchEngine** : Collecte et analyse des données utilisateur
- **DesignSystemManager** : Création de design systems adaptatifs
- **ConversionOptimizer** : Optimisation scientifique pour la conversion
- **AccessibilityChecker** : Validation de l'accessibilité WCAG
- **UsabilityTester** : Simulation de tests d'utilisabilité

### Communication Synaptique
- **KafkaCommunication** : Communication inter-agents
- **WeaviateMemory** : Mémoire vectorielle persistante

## 📊 Métriques et Monitoring

### Métriques de Performance
- Temps de génération de design
- Taux de réussite des validations
- Score d'accessibilité moyen
- Précision des prédictions de conversion

### Monitoring
- Health checks automatiques
- Logs structurés avec Winston
- Métriques Prometheus (optionnel)
- Intégration Sentry (optionnel)

## 🔬 Tests

```bash
# Tests unitaires
npm test

# Tests d'intégration
npm run test:integration

# Coverage
npm run test:coverage

# Linting
npm run lint
```

## 🚀 Déploiement

### Docker Compose
L'agent est inclus dans le `docker-compose.v3.8.yml` principal :

```yaml
agent-uiux:
  build: ./agents/uiux
  ports:
    - "3005:3005"
  environment:
    - WEAVIATE_URL=http://weaviate:8080
    - KAFKA_BROKERS=kafka:9092
  depends_on:
    - weaviate
    - kafka
```

### Kubernetes
Déploiement via les manifests dans `/k8s/agents/uiux/`

## 🤝 Intégration avec d'autres Agents

### Agent Frontend
- Reçoit les designs et composants générés
- Fournit des feedbacks d'implémentation
- Valide la conformité au design

### Agent Backend
- Fournit des données utilisateur réelles
- Intègre les métriques de conversion
- Alimente la recherche utilisateur

### Cortex Central
- Coordonne les demandes de design
- Orchestre les workflows complexes
- Agrège les résultats

## 📚 Documentation Technique

### Types TypeScript
Voir `src/types/index.ts` pour les interfaces complètes.

### Patterns de Design
L'agent maintient une bibliothèque de patterns réutilisables stockés dans Weaviate.

### Algorithmes d'Optimisation
- Optimisation de conversion basée sur la psychologie comportementale
- Algorithmes de placement optimal des éléments UI
- Calculs de métriques d'utilisabilité prédictives

## 🔒 Sécurité

- Validation des entrées avec Joi
- Rate limiting configurable
- Headers de sécurité avec Helmet
- Logs d'audit complets

## 🐛 Debugging

```bash
# Mode debug
DEBUG=agent-uiux:* npm run dev

# Logs détaillés
LOG_LEVEL=debug npm start

# Mock des services externes
MOCK_EXTERNAL_SERVICES=true npm run dev
```

## 📈 Roadmap

### Version 1.1
- [ ] Intégration API Figma
- [ ] Tests A/B automatisés
- [ ] Génération de code React/Vue

### Version 1.2
- [ ] IA générative pour les images
- [ ] Optimisation SEO automatique
- [ ] Analytics prédictives

### Version 2.0
- [ ] Apprentissage continu basé sur les résultats
- [ ] Personnalisation en temps réel
- [ ] Intégration avec les outils de design

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/amazing-feature`)
3. Commit les changements (`git commit -m 'Add amazing feature'`)
4. Push vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

## 📄 License

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

- Documentation : `/docs`
- Issues : GitHub Issues
- Discussions : GitHub Discussions
- Email : <EMAIL>
