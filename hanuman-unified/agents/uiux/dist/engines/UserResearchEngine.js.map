{"version": 3, "file": "UserResearchEngine.js", "sourceRoot": "", "sources": ["../../src/engines/UserResearchEngine.ts"], "names": [], "mappings": ";;;;;;AAEA,0DAAkC;AASlC;;;;;;;;GAQG;AACH,MAAa,kBAAkB;IAK7B,YAAY,MAAc,EAAE,MAAsB;QAChD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,YAAgC;QACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAElE,IAAI,CAAC;YACH,6CAA6C;YAC7C,IAAI,CAAC,OAAO,GAAG,MAAM,mBAAS,CAAC,MAAM,CAAC;gBACpC,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,CAAC,cAAc,EAAE,0BAA0B,CAAC;aACnD,CAAC,CAAC;YAEH,2CAA2C;YAC3C,MAAM,CACJ,YAAY,EACZ,gBAAgB,EAChB,kBAAkB,EAClB,eAAe,EACf,kBAAkB,EAClB,WAAW,EACX,cAAc,CACf,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC;gBAC5C,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;gBAC1C,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC;gBAC/C,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC;gBACzC,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC;gBAC3C,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;gBACrC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC;aACzC,CAAC,CAAC;YAEH,4CAA4C;YAC5C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC;gBACrD,gBAAgB;gBAChB,kBAAkB;gBAClB,eAAe;aAChB,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC;gBACpD,cAAc;gBACd,gBAAgB;gBAChB,kBAAkB;aACnB,CAAC,CAAC;YAEH,MAAM,YAAY,GAAiB;gBACjC,YAAY;gBACZ,gBAAgB;gBAChB,sBAAsB,EAAE,kBAAkB;gBAC1C,eAAe;gBACf,kBAAkB;gBAClB,mBAAmB,EAAE,WAAW;gBAChC,cAAc;gBACd,UAAU;gBACV,WAAW;aACZ,CAAC;YAEF,yBAAyB;YACzB,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAEzE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC/D,OAAO,YAAY,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACvF,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,YAAgC;QAC7D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAEpD,IAAI,CAAC;YACH,MAAM,CACJ,cAAc,EACd,kBAAkB,EAClB,YAAY,EACZ,SAAS,EACT,UAAU,CACX,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,2BAA2B,CAAC,YAAY,CAAC;gBAC9C,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC;gBAClD,IAAI,CAAC,yBAAyB,EAAE;gBAChC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;gBACnC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;aACrC,CAAC,CAAC;YAEH,OAAO;gBACL,cAAc;gBACd,kBAAkB;gBAClB,YAAY;gBACZ,UAAU,EAAE,SAAS;gBACrB,WAAW,EAAE,UAAU;aACxB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjG,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,YAA0B,EAC1B,kBAAsC;QAEtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAEvD,oDAAoD;QACpD,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;QAElF,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAEtD,mCAAmC;YACnC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAEvF,yBAAyB;YACzB,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;YAEnD,OAAO,iBAAiB,CAAC;QAE3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACxF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,+CAA+C;IAEvC,KAAK,CAAC,yBAAyB,CAAC,YAAgC;QACtE,0DAA0D;QAC1D,OAAO;YACL,QAAQ,EAAE,OAAO;YACjB,MAAM,EAAE,CAAC,KAAK,CAAC;YACf,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACpB,MAAM,EAAE,gBAAgB;YACxB,SAAS,EAAE,UAAU;SACtB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,YAAgC;QACpE,oDAAoD;QACpD,OAAO;YACL,WAAW,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;YACvD,SAAS,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE;YAC/D,kBAAkB,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,cAAc,CAAC;YAC9D,UAAU,EAAE,CAAC,cAAc,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;SACtE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,YAAgC;QACzE,yDAAyD;QACzD,MAAM,QAAQ,GAAG;YACf,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,EAAE;SAChB,CAAC;QAEF,KAAK,MAAM,UAAU,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;YAClD,IAAI,CAAC;gBACH,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;gBAC1E,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;gBACvD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;gBACvD,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAC/D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,UAAU,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACvG,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,YAAgC;QACnE,yCAAyC;QACzC,OAAO;YACL,kBAAkB,EAAE,GAAG,EAAE,WAAW;YACpC,UAAU,EAAE,IAAI;YAChB,cAAc,EAAE,KAAK;YACrB,qBAAqB,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC;SACtE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,YAAgC;QACrE,sCAAsC;QACtC,OAAO;YACL,iBAAiB,EAAE,IAAI;YACvB,gBAAgB,EAAE,IAAI;YACtB,oBAAoB,EAAE,IAAI;YAC1B,kBAAkB,EAAE,IAAI;SACzB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,YAAgC;QAC/D,iCAAiC;QACjC,OAAO;YACL,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,IAAI;SACf,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,YAAgC;QAClE,wCAAwC;QACxC,OAAO;YACL,SAAS,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,sBAAsB,EAAE,aAAa,CAAC;YACxE,MAAM,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,CAAC;YAClE,SAAS,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,kBAAkB,CAAC;YACzE,WAAW,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,YAAY,EAAE,QAAQ,CAAC;SAC1E,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,IAAS;QAC9C,2CAA2C;QAC3C,OAAO;YACL,sCAAsC;YACtC,sCAAsC;YACtC,2BAA2B;YAC3B,yBAAyB;YACzB,2BAA2B;SAC5B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,IAAS;QAC5C,uCAAuC;QACvC,OAAO;YACL,8BAA8B;YAC9B,iCAAiC;YACjC,oCAAoC;YACpC,2BAA2B;YAC3B,iCAAiC;SAClC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,UAAkB;QACtD,uCAAuC;QACvC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAE1C,IAAI,CAAC;YACH,+DAA+D;YAC/D,OAAO;gBACL,QAAQ,EAAE,CAAC,uBAAuB,EAAE,aAAa,CAAC;gBAClD,QAAQ,EAAE,CAAC,cAAc,EAAE,sBAAsB,CAAC;gBAClD,WAAW,EAAE,CAAC,kBAAkB,EAAE,2BAA2B,CAAC;aAC/D,CAAC;QACJ,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,YAAgC;QACxE,+CAA+C;QAC/C,OAAO;YACL,WAAW,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,eAAe,CAAC;YAC9D,gBAAgB,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;YAC5E,YAAY,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,cAAc,CAAC;YAC9D,iBAAiB,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,eAAe,CAAC;SAC/E,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,+BAA+B,CAAC,YAAgC;QAC5E,qCAAqC;QACrC,OAAO;YACL,SAAS,EAAE,CAAC,cAAc,EAAE,wBAAwB,CAAC;YACrD,UAAU,EAAE,CAAC,oBAAoB,EAAE,cAAc,CAAC;YAClD,aAAa,EAAE,CAAC,wBAAwB,EAAE,iBAAiB,CAAC;YAC5D,cAAc,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,mBAAmB,CAAC;SACvE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACrC,0CAA0C;QAC1C,OAAO;YACL,mBAAmB;YACnB,mBAAmB;YACnB,qBAAqB;YACrB,oBAAoB;YACpB,oBAAoB;SACrB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,YAAgC;QAC7D,6BAA6B;QAC7B,OAAO;YACL,0BAA0B;YAC1B,oBAAoB;YACpB,iBAAiB;YACjB,yBAAyB;SAC1B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,YAAgC;QAC9D,wCAAwC;QACxC,OAAO;YACL,wBAAwB;YACxB,mBAAmB;YACnB,yBAAyB;YACzB,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,YAA0B,EAAE,kBAAsC;QAC5F,OAAO;;;gCAGqB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;gCACzC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB,CAAC;+BAC9C,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,sBAAsB,CAAC;+BACnD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,kBAAkB,CAAC;6BACjD,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,cAAc,CAAC;;;;;;;;;;;;;KAazE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAc;QACzC,6CAA6C;QAC7C,uCAAuC;QACvC,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB;gBACE,EAAE,EAAE,WAAW;gBACf,IAAI,EAAE,YAAY;gBAClB,GAAG,EAAE,EAAE;gBACP,UAAU,EAAE,mBAAmB;gBAC/B,QAAQ,EAAE,mBAAmB;gBAC7B,KAAK,EAAE;oBACL,OAAO,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;oBACvD,SAAS,EAAE,CAAC,4BAA4B,EAAE,qBAAqB,CAAC;iBACjE;gBACD,YAAY,EAAE,CAAC,cAAc,EAAE,sBAAsB,EAAE,iBAAiB,CAAC;gBACzE,WAAW,EAAE;oBACX,SAAS,EAAE,CAAC,cAAc,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;oBAClE,aAAa,EAAE,CAAC,OAAO,EAAE,oBAAoB,CAAC;oBAC9C,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;iBAC/B;gBACD,kBAAkB,EAAE,EAAE;gBACtB,oBAAoB,EAAE,MAAM;gBAC5B,WAAW,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;gBAC/C,cAAc,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,CAAC;gBAC/D,UAAU,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;gBACjD,qBAAqB,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC;aACjD;SACF,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACnF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,QAAmB,EAAE,YAA0B;QACrF,iEAAiE;QACjE,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9B,GAAG,OAAO;YACV,0DAA0D;SAC3D,CAAC,CAAC,CAAC;IACN,CAAC;CACF;AAxYD,gDAwYC"}