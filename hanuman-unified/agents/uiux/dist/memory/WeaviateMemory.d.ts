import { Logger } from 'winston';
import { UserResearch, Persona, AdaptiveDesignSystem, ConversionWireframes, ComprehensiveUXDesign } from '../types';
/**
 * Système de Mémoire Vectorielle avec Weaviate
 *
 * Stocke et récupère les données de design, recherche utilisateur,
 * personas et patterns de design pour l'apprentissage continu.
 */
export declare class WeaviateMemory {
    private client;
    private logger;
    private isConnected;
    constructor(logger: Logger, weaviateUrl?: string);
    /**
     * Initialise le schéma Weaviate pour l'agent UI/UX
     */
    private initializeSchema;
    /**
     * Stocke une recherche utilisateur
     */
    storeUserResearch(industry: string, research: UserResearch): Promise<string>;
    /**
     * Stocke des personas
     */
    storePersonas(personas: Persona[]): Promise<string[]>;
    /**
     * Stocke un design system
     */
    storeDesignSystem(brandName: string, designSystem: AdaptiveDesignSystem): Promise<string>;
    /**
     * Stocke des wireframes
     */
    storeWireframes(type: string, wireframes: ConversionWireframes): Promise<string>;
    /**
     * Stocke un design complet
     */
    storeDesign(brandName: string, design: ComprehensiveUXDesign): Promise<string>;
    /**
     * Recherche des patterns de design similaires
     */
    searchSimilarDesigns(query: string, limit?: number): Promise<any[]>;
    /**
     * Recherche des personas similaires
     */
    searchSimilarPersonas(characteristics: string[], limit?: number): Promise<Persona[]>;
    /**
     * Récupère les tendances de design par industrie
     */
    getDesignTrendsByIndustry(industry: string): Promise<any[]>;
    /**
     * Stocke un pattern de design réutilisable
     */
    storeDesignPattern(pattern: any): Promise<string>;
    private createUserResearchSchema;
    private createPersonaSchema;
    private createDesignSystemSchema;
    private createWireframeSchema;
    private createDesignPatternSchema;
    private createComprehensiveDesignSchema;
    private createClassIfNotExists;
}
//# sourceMappingURL=WeaviateMemory.d.ts.map