import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { DesignRequirements, ComprehensiveUXDesign, UserResearch, DesignIntelligence, Persona, AdaptiveDesignSystem, ConversionWireframes, UsabilityResults, ConversionOptimizations, AgentConfig } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';
/**
 * Agent UI/UX Design Thinking - Cortex Créatif
 *
 * Cet agent implémente les capacités de design thinking automatisé,
 * recherche utilisateur, génération de personas, et optimisation de conversion.
 */
export declare class UIUXAgent extends EventEmitter {
    private logger;
    private config;
    private memory;
    private communication;
    private userResearchEngine;
    private designSystemManager;
    private conversionOptimizer;
    private accessibilityChecker;
    private usabilityTester;
    constructor(config: AgentConfig, logger: Logger, memory: WeaviateMemory, communication: KafkaCommunication);
    /**
     * Point d'entrée principal pour créer un design complet
     */
    createComprehensiveDesign(requirements: DesignRequirements): Promise<ComprehensiveUXDesign>;
    /**
     * Recherche utilisateur automatique via multiple sources
     */
    conductAutomatedUserResearch(requirements: DesignRequirements): Promise<UserResearch>;
    /**
     * Collecte d'intelligence design et analyse concurrentielle
     */
    gatherDesignIntelligence(requirements: DesignRequirements): Promise<DesignIntelligence>;
    /**
     * Génération de personas basée sur des données réelles
     */
    generateDataDrivenPersonas(userResearch: UserResearch, designIntelligence: DesignIntelligence): Promise<Persona[]>;
    /**
     * Création d'un design system adaptatif
     */
    createAdaptiveDesignSystem(requirements: DesignRequirements, personas: Persona[], intelligence: DesignIntelligence): Promise<AdaptiveDesignSystem>;
    /**
     * Génération de wireframes optimisés pour la conversion
     */
    generateConversionOptimizedWireframes(personas: Persona[], designSystem: AdaptiveDesignSystem): Promise<ConversionWireframes>;
    /**
     * Simulation de tests d'utilisabilité
     */
    simulateUsabilityTests(wireframes: ConversionWireframes, personas: Persona[]): Promise<UsabilityResults>;
    /**
     * Optimisation scientifique pour la conversion
     */
    optimizeForConversion(wireframes: ConversionWireframes, usabilityResults: UsabilityResults): Promise<ConversionOptimizations>;
    /**
     * Création d'une bibliothèque de composants
     */
    createComponentLibrary(wireframes: ConversionWireframes, designSystem: AdaptiveDesignSystem): Promise<Record<string, any>>;
    /**
     * Validation de l'implémentation par rapport au design
     */
    validateImplementation(generatedCode: string, originalDesign: ComprehensiveUXDesign): Promise<any>;
    /**
     * Configuration des gestionnaires d'événements
     */
    private setupEventHandlers;
    /**
     * Gestionnaire de demande de design
     */
    private handleDesignRequest;
    /**
     * Gestionnaire de demande de validation
     */
    private handleValidationRequest;
    private ensureAccessibilityCompliance;
    private createImplementationGuide;
    private createUXTestingStrategy;
    private analyzeGeneratedCode;
    private checkDesignCompliance;
    private generateImplementationRecommendations;
}
//# sourceMappingURL=UIUXAgent.d.ts.map