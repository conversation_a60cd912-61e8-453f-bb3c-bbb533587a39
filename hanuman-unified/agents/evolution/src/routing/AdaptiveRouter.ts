import { EventEmitter } from 'events';
import { NeuroplasticityEngine } from '../engines/NeuroplasticityEngine';
import { Logger } from '../utils/logger';

/**
 * Routeur Adaptatif pour Communications Synaptiques
 *
 * Implémente un système de routage intelligent qui s'adapte en temps réel
 * aux conditions du réseau pour optimiser les communications inter-agents.
 */
export class AdaptiveRouter extends EventEmitter {
  private engine: NeuroplasticityEngine;
  private logger: Logger;
  private routingTable: Map<string, RouteEntry[]> = new Map();
  private loadBalancer: LoadBalancer;
  private circuitBreaker: CircuitBreaker;
  private routingMetrics: RoutingMetrics;

  constructor(engine: NeuroplasticityEngine, logger: Logger) {
    super();
    this.engine = engine;
    this.logger = logger;
    this.loadBalancer = new LoadBalancer();
    this.circuitBreaker = new CircuitBreaker();
    this.routingMetrics = {
      totalRoutes: 0,
      successfulRoutes: 0,
      failedRoutes: 0,
      averageLatency: 0,
      adaptationCount: 0
    };

    this.setupEngineListeners();
  }

  /**
   * Initialise le routeur adaptatif
   */
  public async initialize(): Promise<void> {
    this.logger.info('🛣️ Initialisation du routeur adaptatif');

    await this.buildInitialRoutingTable();
    this.startPeriodicOptimization();

    this.logger.info('✅ Routeur adaptatif initialisé');
  }

  /**
   * Route un message de manière adaptative
   */
  public async routeMessage(message: RoutingMessage): Promise<RoutingResult> {
    const startTime = Date.now();
    this.routingMetrics.totalRoutes++;

    try {
      // 1. Trouver la meilleure route
      const route = await this.findOptimalRoute(message.from, message.to, message.priority);

      if (!route) {
        throw new Error(`Aucune route disponible de ${message.from} vers ${message.to}`);
      }

      // 2. Vérifier le circuit breaker
      if (this.circuitBreaker.isOpen(route.path.join('->'))) {
        throw new Error(`Circuit ouvert pour la route ${route.path.join('->')}`);
      }

      // 3. Appliquer l'équilibrage de charge
      const selectedRoute = this.loadBalancer.selectRoute([route], message);

      // 4. Exécuter le routage
      const result = await this.executeRoute(message, selectedRoute);

      // 5. Mettre à jour les métriques
      await this.updateRouteMetrics(selectedRoute, result, Date.now() - startTime);

      this.routingMetrics.successfulRoutes++;

      return result;

    } catch (error) {
      this.routingMetrics.failedRoutes++;
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      this.logger.error(`❌ Échec du routage: ${errorMessage}`);

      // Adapter le réseau en cas d'échec
      await this.handleRoutingFailure(message, error as Error);

      throw error;
    }
  }

  /**
   * Trouve la route optimale entre deux agents
   */
  private async findOptimalRoute(from: string, to: string, priority: MessagePriority): Promise<RouteEntry | null> {
    const routeKey = `${from}->${to}`;
    const availableRoutes = this.routingTable.get(routeKey) || [];

    if (availableRoutes.length === 0) {
      // Créer une nouvelle route directe
      const directRoute = await this.createDirectRoute(from, to);
      if (directRoute) {
        this.addRoute(routeKey, directRoute);
        return directRoute;
      }

      // Chercher des routes indirectes
      const indirectRoute = await this.findIndirectRoute(from, to);
      if (indirectRoute) {
        this.addRoute(routeKey, indirectRoute);
        return indirectRoute;
      }

      return null;
    }

    // Filtrer les routes selon la priorité et l'état
    const viableRoutes = availableRoutes.filter(route =>
      route.isActive &&
      route.priority >= priority &&
      !this.circuitBreaker.isOpen(route.path.join('->'))
    );

    if (viableRoutes.length === 0) {
      return null;
    }

    // Sélectionner la meilleure route selon les métriques
    return this.selectBestRoute(viableRoutes, priority);
  }

  /**
   * Sélectionne la meilleure route selon les critères
   */
  private selectBestRoute(routes: RouteEntry[], priority: MessagePriority): RouteEntry {
    return routes.reduce((best, current) => {
      const bestScore = this.calculateRouteScore(best, priority);
      const currentScore = this.calculateRouteScore(current, priority);

      return currentScore > bestScore ? current : best;
    });
  }

  /**
   * Calcule le score d'une route
   */
  private calculateRouteScore(route: RouteEntry, priority: MessagePriority): number {
    const latencyScore = Math.max(0, 1 - (route.averageLatency / 200));
    const reliabilityScore = route.successRate;
    const strengthScore = route.connectionStrength;
    const loadScore = Math.max(0, 1 - (route.currentLoad / route.maxLoad));

    // Pondération selon la priorité
    const weights = this.getPriorityWeights(priority);

    return (
      latencyScore * weights.latency +
      reliabilityScore * weights.reliability +
      strengthScore * weights.strength +
      loadScore * weights.load
    );
  }

  /**
   * Obtient les poids selon la priorité
   */
  private getPriorityWeights(priority: MessagePriority): RouteWeights {
    switch (priority) {
      case MessagePriority.CRITICAL:
        return { latency: 0.4, reliability: 0.4, strength: 0.1, load: 0.1 };
      case MessagePriority.HIGH:
        return { latency: 0.3, reliability: 0.3, strength: 0.2, load: 0.2 };
      case MessagePriority.NORMAL:
        return { latency: 0.25, reliability: 0.25, strength: 0.25, load: 0.25 };
      case MessagePriority.LOW:
        return { latency: 0.1, reliability: 0.2, strength: 0.3, load: 0.4 };
    }
  }

  /**
   * Crée une route directe
   */
  private async createDirectRoute(from: string, to: string): Promise<RouteEntry | null> {
    try {
      // Créer une connexion synaptique si elle n'existe pas
      await this.engine.createConnection(from, to, { routingRequest: true });

      const metrics = this.engine.getPlasticityMetrics();

      return {
        path: [from, to],
        averageLatency: 50, // Latence initiale estimée
        successRate: 1.0,
        connectionStrength: 0.5,
        currentLoad: 0,
        maxLoad: 100,
        isActive: true,
        priority: MessagePriority.NORMAL,
        lastUsed: new Date(),
        createdAt: new Date()
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      this.logger.warn(`⚠️ Impossible de créer une route directe ${from} -> ${to}: ${errorMessage}`);
      return null;
    }
  }

  /**
   * Trouve une route indirecte
   */
  private async findIndirectRoute(from: string, to: string): Promise<RouteEntry | null> {
    // Implémentation simplifiée d'un algorithme de recherche de chemin
    const patterns = this.engine.analyzeCommunicationPatterns();

    // Chercher des agents intermédiaires
    for (const pattern of patterns) {
      if (pattern.agents.includes(from) && pattern.agents.includes(to)) {
        const path = this.constructPath(from, to, pattern.agents);
        if (path.length > 2) {
          return {
            path,
            averageLatency: path.length * 30, // Estimation basée sur le nombre de sauts
            successRate: 0.8, // Réduite pour les routes indirectes
            connectionStrength: 0.4,
            currentLoad: 0,
            maxLoad: 50,
            isActive: true,
            priority: MessagePriority.LOW,
            lastUsed: new Date(),
            createdAt: new Date()
          };
        }
      }
    }

    return null;
  }

  /**
   * Construit un chemin optimal
   */
  private constructPath(from: string, to: string, agents: string[]): string[] {
    // Algorithme simplifié - dans une implémentation complète,
    // utiliser Dijkstra ou A*
    const fromIndex = agents.indexOf(from);
    const toIndex = agents.indexOf(to);

    if (fromIndex === -1 || toIndex === -1) {
      return [from, to];
    }

    if (Math.abs(fromIndex - toIndex) === 1) {
      return [from, to];
    }

    // Chemin via l'agent intermédiaire le plus proche
    const midIndex = Math.floor((fromIndex + toIndex) / 2);
    const midAgent = agents[midIndex];
    if (midAgent) {
      return [from, midAgent, to];
    }
    return [from, to];
  }

  /**
   * Exécute le routage
   */
  private async executeRoute(message: RoutingMessage, route: RouteEntry): Promise<RoutingResult> {
    const startTime = Date.now();

    // Simuler l'exécution du routage
    const latency = route.averageLatency + (Math.random() * 20 - 10); // Variation ±10ms
    const success = Math.random() < route.successRate;

    if (!success) {
      throw new Error('Échec de transmission du message');
    }

    // Mettre à jour la charge
    route.currentLoad++;
    route.lastUsed = new Date();

    const result: RoutingResult = {
      messageId: message.id,
      route: route.path,
      latency: Math.max(0, latency),
      success: true,
      timestamp: new Date(),
      hops: route.path.length - 1
    };

    // Adapter la connexion synaptique
    if (route.path.length === 2 && route.path[0] && route.path[1]) {
      await this.engine.strengthenConnection(route.path[0], route.path[1], {
        success: true,
        latency: result.latency
      });
    }

    return result;
  }

  /**
   * Met à jour les métriques de route
   */
  private async updateRouteMetrics(route: RouteEntry, result: RoutingResult, _executionTime: number): Promise<void> {
    // Mise à jour de la latence moyenne (moyenne mobile)
    const alpha = 0.1; // Facteur de lissage
    route.averageLatency = (1 - alpha) * route.averageLatency + alpha * result.latency;

    // Mise à jour du taux de succès
    const successWeight = result.success ? 1 : 0;
    route.successRate = (1 - alpha) * route.successRate + alpha * successWeight;

    // Réduire la charge
    route.currentLoad = Math.max(0, route.currentLoad - 1);

    // Mettre à jour les métriques globales
    this.routingMetrics.averageLatency =
      (this.routingMetrics.averageLatency * (this.routingMetrics.totalRoutes - 1) + result.latency) /
      this.routingMetrics.totalRoutes;
  }

  /**
   * Gère les échecs de routage
   */
  private async handleRoutingFailure(message: RoutingMessage, error: Error): Promise<void> {
    const routeKey = `${message.from}->${message.to}`;
    const routes = this.routingTable.get(routeKey) || [];

    // Marquer les routes défaillantes
    routes.forEach(route => {
      if (route.path.length === 2) {
        route.successRate *= 0.9; // Réduire le taux de succès

        // Ouvrir le circuit breaker si trop d'échecs
        if (route.successRate < 0.5) {
          this.circuitBreaker.open(route.path.join('->'));
        }
      }
    });

    // Affaiblir la connexion synaptique
    await this.engine.weakenConnection(message.from, message.to, 'routing_failure');

    // Déclencher une adaptation
    this.routingMetrics.adaptationCount++;
    this.emit('routing-failure', { message, error, timestamp: new Date() });
  }

  /**
   * Ajoute une route à la table
   */
  private addRoute(routeKey: string, route: RouteEntry): void {
    if (!this.routingTable.has(routeKey)) {
      this.routingTable.set(routeKey, []);
    }

    this.routingTable.get(routeKey)!.push(route);
  }

  /**
   * Construit la table de routage initiale
   */
  private async buildInitialRoutingTable(): Promise<void> {
    const patterns = this.engine.analyzeCommunicationPatterns();

    patterns.forEach(pattern => {
      for (let i = 0; i < pattern.agents.length; i++) {
        for (let j = i + 1; j < pattern.agents.length; j++) {
          const from = pattern.agents[i];
          const to = pattern.agents[j];
          const routeKey = `${from}->${to}`;

          const route: RouteEntry = {
            path: [from, to],
            averageLatency: pattern.averageLatency || 50,
            successRate: pattern.efficiency,
            connectionStrength: 0.5,
            currentLoad: 0,
            maxLoad: 100,
            isActive: true,
            priority: MessagePriority.NORMAL,
            lastUsed: new Date(),
            createdAt: new Date()
          };

          this.addRoute(routeKey, route);
        }
      }
    });
  }

  /**
   * Configure les listeners sur le moteur
   */
  private setupEngineListeners(): void {
    this.engine.on('connection-created', () => {
      this.rebuildRoutingTable();
    });

    this.engine.on('connection-pruned', () => {
      this.rebuildRoutingTable();
    });

    this.engine.on('paths-optimized', () => {
      this.rebuildRoutingTable();
    });
  }

  /**
   * Reconstruit la table de routage
   */
  private async rebuildRoutingTable(): Promise<void> {
    this.logger.debug('🔄 Reconstruction de la table de routage');
    this.routingTable.clear();
    await this.buildInitialRoutingTable();
  }

  /**
   * Démarre l'optimisation périodique
   */
  private startPeriodicOptimization(): void {
    setInterval(async () => {
      await this.optimizeRoutes();
    }, 60000); // Toutes les minutes
  }

  /**
   * Optimise les routes
   */
  private async optimizeRoutes(): Promise<void> {
    // Nettoyer les routes inactives
    this.routingTable.forEach((routes, key) => {
      const activeRoutes = routes.filter(route => {
        const timeSinceLastUse = Date.now() - route.lastUsed.getTime();
        return timeSinceLastUse < 300000; // 5 minutes
      });

      if (activeRoutes.length === 0) {
        this.routingTable.delete(key);
      } else {
        this.routingTable.set(key, activeRoutes);
      }
    });

    // Fermer les circuits breakers si approprié
    this.circuitBreaker.attemptReset();
  }

  /**
   * Obtient les métriques de routage
   */
  public getRoutingMetrics(): RoutingMetrics {
    return { ...this.routingMetrics };
  }

  /**
   * Obtient la table de routage
   */
  public getRoutingTable(): Map<string, RouteEntry[]> {
    return new Map(this.routingTable);
  }
}

// Classes auxiliaires
class LoadBalancer {
  selectRoute(routes: RouteEntry[], _message: RoutingMessage): RouteEntry {
    // Sélection basée sur la charge actuelle
    return routes.reduce((best, current) =>
      current.currentLoad < best.currentLoad ? current : best
    );
  }
}

class CircuitBreaker {
  private openCircuits = new Map<string, Date>();
  private readonly timeout = 30000; // 30 secondes

  isOpen(routeId: string): boolean {
    const openTime = this.openCircuits.get(routeId);
    if (!openTime) return false;

    return Date.now() - openTime.getTime() < this.timeout;
  }

  open(routeId: string): void {
    this.openCircuits.set(routeId, new Date());
  }

  attemptReset(): void {
    const now = Date.now();
    const toDelete: string[] = [];

    this.openCircuits.forEach((openTime, routeId) => {
      if (now - openTime.getTime() >= this.timeout) {
        toDelete.push(routeId);
      }
    });

    toDelete.forEach(routeId => {
      this.openCircuits.delete(routeId);
    });
  }
}

// Interfaces
interface RoutingMessage {
  id: string;
  from: string;
  to: string;
  payload: any;
  priority: MessagePriority;
  timestamp: Date;
}

interface RoutingResult {
  messageId: string;
  route: string[];
  latency: number;
  success: boolean;
  timestamp: Date;
  hops: number;
}

interface RouteEntry {
  path: string[];
  averageLatency: number;
  successRate: number;
  connectionStrength: number;
  currentLoad: number;
  maxLoad: number;
  isActive: boolean;
  priority: MessagePriority;
  lastUsed: Date;
  createdAt: Date;
}

interface RouteWeights {
  latency: number;
  reliability: number;
  strength: number;
  load: number;
}

interface RoutingMetrics {
  totalRoutes: number;
  successfulRoutes: number;
  failedRoutes: number;
  averageLatency: number;
  adaptationCount: number;
}

enum MessagePriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}
