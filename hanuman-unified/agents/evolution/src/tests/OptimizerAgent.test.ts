import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { Logger } from 'winston';
import { OptimizerAgent } from '../agents/OptimizerAgent';
import { EvolutionSolution } from '../types/evolution';

// Mock du logger
const mockLogger = {
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
} as unknown as <PERSON><PERSON>;

describe('OptimizerAgent', () => {
  let optimizerAgent: OptimizerAgent;

  beforeEach(() => {
    optimizerAgent = new OptimizerAgent(mockLogger);
    jest.clearAllMocks();
  });

  const mockSolution: EvolutionSolution = {
    id: 'solution-1',
    code: `
function bubbleSort(arr) {
  for (let i = 0; i < arr.length; i++) {
    for (let j = 0; j < arr.length - 1; j++) {
      if (arr[j] > arr[j + 1]) {
        let temp = arr[j];
        arr[j] = arr[j + 1];
        arr[j + 1] = temp;
      }
    }
  }
  return arr;
}`,
    description: 'Bubble sort implementation',
    approach: 'brute-force',
    fitness: {
      total: 0.4,
      performance: 0.3,
      correctness: 0.8,
      efficiency: 0.2,
      robustness: 0.6,
      maintainability: 0.5,
      innovation: 0.1
    },
    generation: 1,
    parentIds: [],
    mutations: [],
    performance: {
      executionTime: 1000,
      memoryUsage: 2048,
      cpuUsage: 0.8,
      complexity: {
        timeComplexity: 'O(n²)',
        spaceComplexity: 'O(1)',
        cyclomaticComplexity: 15,
        cognitiveComplexity: 12
      },
      scalability: 0.3
    }
  };

  const mockRequest = {
    problem: 'Sort array efficiently',
    domain: 'sorting',
    metrics: ['performance', 'efficiency'],
    constraints: { maxComplexity: 'O(n log n)' },
    config: {} as any
  };

  describe('optimizeSolution', () => {
    it('should optimize a solution and improve fitness', async () => {
      const optimizedSolution = await optimizerAgent.optimizeSolution(mockSolution, mockRequest);

      expect(optimizedSolution.id).toContain('optimizer-');
      expect(optimizedSolution.parentIds).toContain(mockSolution.id);
      expect(optimizedSolution.generation).toBe(mockSolution.generation + 1);
      expect(optimizedSolution.fitness.total).toBeGreaterThanOrEqual(mockSolution.fitness.total);
    });

    it('should emit solution-optimized event', async () => {
      const eventSpy = jest.fn();
      optimizerAgent.on('solution-optimized', eventSpy);

      await optimizerAgent.optimizeSolution(mockSolution, mockRequest);

      expect(eventSpy).toHaveBeenCalledWith({
        original: mockSolution.id,
        optimized: expect.any(String),
        improvements: expect.any(Number)
      });
    });

    it('should add optimization mutation to history', async () => {
      const optimizedSolution = await optimizerAgent.optimizeSolution(mockSolution, mockRequest);

      expect(optimizedSolution.mutations).toHaveLength(1);
      expect(optimizedSolution.mutations[0].type).toBe('optimization');
      expect(optimizedSolution.mutations[0].parentSolutionId).toBe(mockSolution.id);
    });

    it('should improve performance metrics', async () => {
      const optimizedSolution = await optimizerAgent.optimizeSolution(mockSolution, mockRequest);

      // Les métriques de performance devraient être améliorées
      expect(optimizedSolution.performance.complexity.cyclomaticComplexity)
        .toBeLessThanOrEqual(mockSolution.performance.complexity.cyclomaticComplexity);
      expect(optimizedSolution.performance.scalability)
        .toBeGreaterThanOrEqual(mockSolution.performance.scalability);
    });

    it('should preserve correctness while optimizing', async () => {
      const optimizedSolution = await optimizerAgent.optimizeSolution(mockSolution, mockRequest);

      // La correctness devrait être préservée
      expect(optimizedSolution.fitness.correctness).toBe(mockSolution.fitness.correctness);
    });
  });

  describe('inefficiency analysis', () => {
    it('should detect nested loops', async () => {
      const nestedLoopSolution = {
        ...mockSolution,
        code: `
function findPairs(arr) {
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (arr[i] + arr[j] === target) {
        return [i, j];
      }
    }
  }
}`
      };

      const optimizedSolution = await optimizerAgent.optimizeSolution(nestedLoopSolution, mockRequest);

      // Devrait détecter et optimiser les boucles imbriquées
      expect(optimizedSolution.fitness.efficiency).toBeGreaterThan(nestedLoopSolution.fitness.efficiency);
    });

    it('should detect code duplication', async () => {
      const duplicatedCodeSolution = {
        ...mockSolution,
        code: `
function process1(data) {
  let result = [];
  for (let i = 0; i < data.length; i++) {
    result.push(data[i] * 2);
  }
  return result;
}

function process2(data) {
  let result = [];
  for (let i = 0; i < data.length; i++) {
    result.push(data[i] * 3);
  }
  return result;
}`
      };

      const optimizedSolution = await optimizerAgent.optimizeSolution(duplicatedCodeSolution, mockRequest);

      // Devrait améliorer la maintenabilité en éliminant la duplication
      expect(optimizedSolution.fitness.maintainability)
        .toBeGreaterThan(duplicatedCodeSolution.fitness.maintainability);
    });

    it('should detect high cyclomatic complexity', async () => {
      const complexSolution = {
        ...mockSolution,
        performance: {
          ...mockSolution.performance,
          complexity: {
            ...mockSolution.performance.complexity,
            cyclomaticComplexity: 25 // Très élevé
          }
        }
      };

      const optimizedSolution = await optimizerAgent.optimizeSolution(complexSolution, mockRequest);

      // Devrait réduire la complexité cyclomatique
      expect(optimizedSolution.performance.complexity.cyclomaticComplexity)
        .toBeLessThan(complexSolution.performance.complexity.cyclomaticComplexity);
    });
  });

  describe('optimization strategies', () => {
    it('should apply refactoring optimizations', async () => {
      const messyCodeSolution = {
        ...mockSolution,
        code: `
function messyFunction(data) {
  if (data) {
    if (data.length > 0) {
      if (data[0]) {
        if (data[0].value) {
          return data[0].value;
        }
      }
    }
  }
  return null;
}`
      };

      const optimizedSolution = await optimizerAgent.optimizeSolution(messyCodeSolution, mockRequest);

      // Devrait améliorer la structure du code
      expect(optimizedSolution.code).toBeDefined();
      expect(optimizedSolution.fitness.maintainability)
        .toBeGreaterThan(messyCodeSolution.fitness.maintainability);
    });

    it('should apply algorithmic optimizations', async () => {
      const inefficientSolution = {
        ...mockSolution,
        code: `
function linearSearch(arr, target) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === target) {
      return i;
    }
  }
  return -1;
}`
      };

      const optimizedSolution = await optimizerAgent.optimizeSolution(inefficientSolution, mockRequest);

      // Devrait améliorer l'efficacité algorithmique
      expect(optimizedSolution.fitness.efficiency)
        .toBeGreaterThan(inefficientSolution.fitness.efficiency);
    });

    it('should apply performance optimizations', async () => {
      const slowSolution = {
        ...mockSolution,
        code: `
function processArray(arr) {
  let result = [];
  for (let i = 0; i < arr.length; i++) {
    result.push(arr[i].toString().toUpperCase());
  }
  return result;
}`
      };

      const optimizedSolution = await optimizerAgent.optimizeSolution(slowSolution, mockRequest);

      // Devrait améliorer les performances
      expect(optimizedSolution.fitness.performance)
        .toBeGreaterThanOrEqual(slowSolution.fitness.performance);
    });
  });

  describe('fitness estimation', () => {
    it('should estimate improved fitness scores', async () => {
      const optimizedSolution = await optimizerAgent.optimizeSolution(mockSolution, mockRequest);

      // Tous les scores de fitness devraient être dans la plage valide
      Object.values(optimizedSolution.fitness).forEach(score => {
        expect(score).toBeGreaterThanOrEqual(0);
        expect(score).toBeLessThanOrEqual(1);
      });

      // Le score total devrait être amélioré ou au moins égal
      expect(optimizedSolution.fitness.total).toBeGreaterThanOrEqual(mockSolution.fitness.total);
    });

    it('should balance different optimization aspects', async () => {
      const optimizedSolution = await optimizerAgent.optimizeSolution(mockSolution, mockRequest);

      // Vérifier que l'optimisation équilibre différents aspects
      const improvements = {
        performance: optimizedSolution.fitness.performance - mockSolution.fitness.performance,
        efficiency: optimizedSolution.fitness.efficiency - mockSolution.fitness.efficiency,
        maintainability: optimizedSolution.fitness.maintainability - mockSolution.fitness.maintainability
      };

      // Au moins un aspect devrait être amélioré
      const hasImprovement = Object.values(improvements).some(improvement => improvement > 0);
      expect(hasImprovement).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should handle solutions with empty code', async () => {
      const emptySolution = {
        ...mockSolution,
        code: ''
      };

      // Ne devrait pas lever d'exception
      const optimizedSolution = await optimizerAgent.optimizeSolution(emptySolution, mockRequest);
      expect(optimizedSolution).toBeDefined();
      expect(optimizedSolution.code).toBeDefined();
    });

    it('should handle invalid fitness scores', async () => {
      const invalidSolution = {
        ...mockSolution,
        fitness: {
          total: -1, // Score invalide
          performance: 2, // Score invalide
          correctness: 0.5,
          efficiency: 0.5,
          robustness: 0.5,
          maintainability: 0.5,
          innovation: 0.5
        }
      };

      const optimizedSolution = await optimizerAgent.optimizeSolution(invalidSolution, mockRequest);
      
      // Les scores devraient être normalisés
      expect(optimizedSolution.fitness.total).toBeGreaterThanOrEqual(0);
      expect(optimizedSolution.fitness.total).toBeLessThanOrEqual(1);
    });

    it('should handle optimization failures gracefully', async () => {
      const problematicSolution = {
        ...mockSolution,
        code: 'invalid javascript code {'
      };

      // Ne devrait pas lever d'exception
      const optimizedSolution = await optimizerAgent.optimizeSolution(problematicSolution, mockRequest);
      expect(optimizedSolution).toBeDefined();
    });
  });

  describe('optimization history', () => {
    it('should track optimization improvements', async () => {
      const solution1 = await optimizerAgent.optimizeSolution(mockSolution, mockRequest);
      const solution2 = await optimizerAgent.optimizeSolution(solution1, mockRequest);

      // Chaque optimisation devrait avoir un historique
      expect(solution1.mutations).toHaveLength(1);
      expect(solution2.mutations).toHaveLength(2); // Hérite des mutations précédentes + nouvelle
    });

    it('should preserve optimization lineage', async () => {
      const optimizedSolution = await optimizerAgent.optimizeSolution(mockSolution, mockRequest);

      expect(optimizedSolution.parentIds).toEqual([mockSolution.id]);
      expect(optimizedSolution.generation).toBe(mockSolution.generation + 1);
      expect(optimizedSolution.mutations[0].parentSolutionId).toBe(mockSolution.id);
    });
  });
});
