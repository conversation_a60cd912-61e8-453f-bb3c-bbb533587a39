import { NeuroplasticityEngine } from '../engines/NeuroplasticityEngine';
import { AdaptationType, NeuroplasticityRequest } from '../types/evolution';
import { Logger } from '../utils/logger';

// Mock du logger
const mockLogger: Logger = {
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

describe('NeuroplasticityEngine', () => {
  let engine: NeuroplasticityEngine;

  beforeEach(() => {
    engine = new NeuroplasticityEngine(mockLogger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      await engine.initialize();
      expect(mockLogger.info).toHaveBeenCalledWith('🧠 Initialisation du moteur de neuroplasticité');
      expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('✅ Neuroplasticité initialisée'));
    });

    test('should initialize learning rates for all agents', async () => {
      await engine.initialize();
      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBe(0);
      expect(metrics.averageStrength).toBe(0);
    });
  });

  describe('Synaptic Strengthening (LTP)', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should strengthen existing connection', async () => {
      // Créer d'abord une connexion
      await engine.createConnection('agent-a', 'agent-b', { latency: 50 });
      
      // Renforcer la connexion
      await engine.strengthenConnection('agent-a', 'agent-b', { 
        success: true, 
        latency: 30 
      });

      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBe(1);
      expect(metrics.averageStrength).toBeGreaterThan(0.5);
    });

    test('should create new connection if not exists', async () => {
      await engine.strengthenConnection('agent-x', 'agent-y', { 
        success: true, 
        latency: 40 
      });

      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBe(1);
    });

    test('should update learning rate with metaplasticity', async () => {
      await engine.createConnection('agent-a', 'agent-b', { latency: 50 });
      
      // Plusieurs renforcements successifs
      for (let i = 0; i < 5; i++) {
        await engine.strengthenConnection('agent-a', 'agent-b', { 
          success: true, 
          latency: 30 
        });
      }

      // Vérifier que la connexion s'est renforcée
      const metrics = engine.getPlasticityMetrics();
      expect(metrics.averageStrength).toBeGreaterThan(0.6);
    });
  });

  describe('Synaptic Weakening (LTD)', () => {
    beforeEach(async () => {
      await engine.initialize();
      await engine.createConnection('agent-a', 'agent-b', { latency: 50 });
    });

    test('should weaken existing connection', async () => {
      const initialMetrics = engine.getPlasticityMetrics();
      
      await engine.weakenConnection('agent-a', 'agent-b', 'communication_failure');

      const finalMetrics = engine.getPlasticityMetrics();
      expect(finalMetrics.averageStrength).toBeLessThan(initialMetrics.averageStrength);
    });

    test('should prune connection when strength becomes too low', async () => {
      // Affaiblir plusieurs fois pour déclencher l'élagage
      for (let i = 0; i < 10; i++) {
        await engine.weakenConnection('agent-a', 'agent-b', 'repeated_failure');
      }

      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBe(0);
    });
  });

  describe('Connection Management', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should create new connection with correct properties', async () => {
      const stimulus = { latency: 45, messageType: 'task-assignment' };
      
      await engine.createConnection('agent-frontend', 'agent-backend', stimulus);

      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBe(1);
      expect(metrics.averageStrength).toBe(0.5); // Force initiale
    });

    test('should emit connection-created event', async () => {
      const eventSpy = jest.fn();
      engine.on('connection-created', eventSpy);

      await engine.createConnection('agent-a', 'agent-b', {});

      expect(eventSpy).toHaveBeenCalledWith(expect.objectContaining({
        fromAgent: 'agent-a',
        toAgent: 'agent-b',
        strength: 0.5
      }));
    });

    test('should prune connection and emit event', async () => {
      await engine.createConnection('agent-a', 'agent-b', {});
      
      const eventSpy = jest.fn();
      engine.on('connection-pruned', eventSpy);

      const connectionId = engine.getConnectionId('agent-a', 'agent-b');
      await engine.pruneConnection(connectionId);

      expect(eventSpy).toHaveBeenCalledWith(expect.objectContaining({
        connectionId,
        reason: 'manual_pruning'
      }));

      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBe(0);
    });
  });

  describe('Adaptation Processing', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should process synaptic strengthening adaptation', async () => {
      const request: NeuroplasticityRequest = {
        agentId: 'agent-a',
        connectionId: 'agent-a->agent-b',
        adaptationType: AdaptationType.SYNAPTIC_STRENGTHENING,
        stimulus: { success: true },
        context: { taskType: 'optimization' }
      };

      const adaptation = await engine.processAdaptation(request);

      expect(adaptation).toMatchObject({
        fromAgent: 'agent-a',
        toAgent: 'agent-b',
        adaptationType: AdaptationType.SYNAPTIC_STRENGTHENING
      });

      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBe(1);
    });

    test('should process connection pruning adaptation', async () => {
      // Créer d'abord une connexion
      await engine.createConnection('agent-a', 'agent-b', {});

      const request: NeuroplasticityRequest = {
        agentId: 'agent-a',
        connectionId: 'agent-a->agent-b',
        adaptationType: AdaptationType.CONNECTION_PRUNING,
        stimulus: {},
        context: {}
      };

      await engine.processAdaptation(request);

      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBe(0);
    });
  });

  describe('Metrics and Monitoring', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should provide accurate plasticity metrics', async () => {
      // Créer plusieurs connexions avec différentes forces
      await engine.createConnection('agent-a', 'agent-b', {});
      await engine.createConnection('agent-b', 'agent-c', {});
      await engine.strengthenConnection('agent-a', 'agent-b', { success: true });

      const metrics = engine.getPlasticityMetrics();

      expect(metrics.totalConnections).toBe(2);
      expect(metrics.averageStrength).toBeGreaterThan(0.5);
      expect(metrics.strongConnections).toBeGreaterThanOrEqual(0);
      expect(metrics.weakConnections).toBeGreaterThanOrEqual(0);
    });

    test('should track adaptation history', async () => {
      const request: NeuroplasticityRequest = {
        agentId: 'agent-a',
        connectionId: 'agent-a->agent-b',
        adaptationType: AdaptationType.NEW_CONNECTION,
        stimulus: {},
        context: {}
      };

      await engine.processAdaptation(request);

      const history = engine.getAdaptationHistory();
      expect(history).toHaveLength(1);
      expect(history[0]).toMatchObject({
        fromAgent: 'agent-a',
        toAgent: 'agent-b',
        adaptationType: AdaptationType.NEW_CONNECTION
      });
    });
  });

  describe('Communication Pattern Analysis', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    test('should analyze communication patterns', async () => {
      // Créer un réseau de connexions
      await engine.createConnection('agent-frontend', 'agent-backend', {});
      await engine.createConnection('agent-backend', 'agent-database', {});
      await engine.createConnection('agent-frontend', 'agent-cache', {});

      const patterns = engine.analyzeCommunicationPatterns();

      expect(patterns).toBeDefined();
      expect(Array.isArray(patterns)).toBe(true);
    });
  });
});
