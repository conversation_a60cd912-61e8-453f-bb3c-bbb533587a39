import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { <PERSON>gger } from 'winston';
import { AlphaEvolveEngine } from '../core/AlphaEvolveEngine';
import { EvolutionConfig, SelectionStrategy } from '../types/evolution';

// Mock du logger
const mockLogger = {
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
} as unknown as <PERSON><PERSON>;

describe('AlphaEvolveEngine Integration', () => {
  let alphaEvolveEngine: AlphaEvolveEngine;
  let evolutionConfig: EvolutionConfig;

  beforeEach(() => {
    evolutionConfig = {
      populationSize: 10,
      eliteRatio: 0.3,
      mutationRate: 0.2,
      crossoverRate: 0.7,
      maxGenerations: 5, // Réduit pour les tests
      convergenceThreshold: 0.001,
      fitnessWeights: {
        performance: 0.3,
        correctness: 0.3,
        efficiency: 0.2,
        robustness: 0.1,
        maintainability: 0.05,
        innovation: 0.05
      },
      selectionStrategy: SelectionStrategy.TOURNAMENT
    };

    alphaEvolveEngine = new AlphaEvolveEngine(evolutionConfig, mockLogger);
    jest.clearAllMocks();
  });

  describe('complete evolution cycle', () => {
    it('should complete a full evolution cycle', async () => {
      const request = {
        problem: 'Sort an array of integers',
        domain: 'sorting',
        metrics: ['performance', 'correctness'],
        constraints: {
          maxExecutionTime: 1000,
          maxMemoryUsage: 1024
        },
        config: evolutionConfig
      };

      const result = await alphaEvolveEngine.evolve(request);

      expect(result).toBeDefined();
      expect(result.solutions).toHaveLength(evolutionConfig.populationSize);
      expect(result.bestSolution).toBeDefined();
      expect(result.generationCount).toBeGreaterThan(0);
      expect(result.generationCount).toBeLessThanOrEqual(evolutionConfig.maxGenerations);
      expect(result.executionTime).toBeGreaterThan(0);
    });

    it('should improve fitness over generations', async () => {
      const request = {
        problem: 'Find maximum element in array',
        domain: 'searching',
        metrics: ['performance', 'correctness'],
        constraints: {},
        config: evolutionConfig
      };

      const eventSpy = jest.fn();
      alphaEvolveEngine.on('generation-complete', eventSpy);

      const result = await alphaEvolveEngine.evolve(request);

      // Vérifier que des événements de génération ont été émis
      expect(eventSpy).toHaveBeenCalled();
      
      // Vérifier l'amélioration progressive
      const generationEvents = eventSpy.mock.calls.map(call => call[0]);
      if (generationEvents.length > 1) {
        const firstGeneration = generationEvents[0];
        const lastGeneration = generationEvents[generationEvents.length - 1];
        
        expect(lastGeneration.bestFitness).toBeGreaterThanOrEqual(firstGeneration.bestFitness);
      }

      expect(result.bestSolution.fitness.total).toBeGreaterThan(0);
    });

    it('should handle convergence correctly', async () => {
      const fastConvergenceConfig = {
        ...evolutionConfig,
        convergenceThreshold: 0.5, // Seuil élevé pour convergence rapide
        maxGenerations: 20
      };

      const engine = new AlphaEvolveEngine(fastConvergenceConfig, mockLogger);
      
      const request = {
        problem: 'Simple addition function',
        domain: 'arithmetic',
        metrics: ['correctness'],
        constraints: {},
        config: fastConvergenceConfig
      };

      const result = await engine.evolve(request);

      // Devrait converger avant d'atteindre le maximum de générations
      expect(result.generationCount).toBeLessThanOrEqual(fastConvergenceConfig.maxGenerations);
    });

    it('should respect population size constraints', async () => {
      const request = {
        problem: 'Binary search implementation',
        domain: 'searching',
        metrics: ['performance', 'correctness'],
        constraints: {},
        config: evolutionConfig
      };

      const result = await alphaEvolveEngine.evolve(request);

      expect(result.solutions).toHaveLength(evolutionConfig.populationSize);
      
      // Vérifier que chaque solution a les propriétés requises
      result.solutions.forEach(solution => {
        expect(solution.id).toBeDefined();
        expect(solution.code).toBeDefined();
        expect(solution.fitness).toBeDefined();
        expect(solution.generation).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('agent collaboration', () => {
    it('should coordinate Explorer, Optimizer, and Evaluator agents', async () => {
      const request = {
        problem: 'Implement quicksort algorithm',
        domain: 'sorting',
        metrics: ['performance', 'correctness', 'efficiency'],
        constraints: {
          timeComplexity: 'O(n log n)',
          spaceComplexity: 'O(log n)'
        },
        config: evolutionConfig
      };

      const result = await alphaEvolveEngine.evolve(request);

      // Vérifier que les agents ont collaboré efficacement
      expect(result.bestSolution.fitness.total).toBeGreaterThan(0);
      expect(result.bestSolution.fitness.correctness).toBeGreaterThan(0);
      expect(result.bestSolution.fitness.performance).toBeGreaterThan(0);
      
      // Vérifier la diversité des solutions
      const approaches = result.solutions.map(s => s.approach);
      const uniqueApproaches = new Set(approaches);
      expect(uniqueApproaches.size).toBeGreaterThan(1);
    });

    it('should apply optimizations from OptimizerAgent', async () => {
      const request = {
        problem: 'Optimize bubble sort',
        domain: 'sorting',
        metrics: ['efficiency', 'performance'],
        constraints: {},
        config: evolutionConfig
      };

      const result = await alphaEvolveEngine.evolve(request);

      // Vérifier que des optimisations ont été appliquées
      const optimizedSolutions = result.solutions.filter(s => 
        s.mutations.some(m => m.type === 'optimization')
      );
      
      expect(optimizedSolutions.length).toBeGreaterThan(0);
    });

    it('should evaluate solutions with EvaluatorAgent', async () => {
      const request = {
        problem: 'Linear search with early termination',
        domain: 'searching',
        metrics: ['correctness', 'performance'],
        constraints: {},
        config: evolutionConfig
      };

      const result = await alphaEvolveEngine.evolve(request);

      // Toutes les solutions devraient avoir été évaluées
      result.solutions.forEach(solution => {
        expect(solution.fitness.total).toBeGreaterThan(0);
        expect(solution.fitness.correctness).toBeGreaterThanOrEqual(0);
        expect(solution.fitness.performance).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('selection strategies', () => {
    it('should work with tournament selection', async () => {
      const config = {
        ...evolutionConfig,
        selectionStrategy: SelectionStrategy.TOURNAMENT
      };

      const engine = new AlphaEvolveEngine(config, mockLogger);
      
      const request = {
        problem: 'Tournament selection test',
        domain: 'test',
        metrics: ['performance'],
        constraints: {},
        config
      };

      const result = await engine.evolve(request);
      expect(result.bestSolution).toBeDefined();
    });

    it('should work with elitist selection', async () => {
      const config = {
        ...evolutionConfig,
        selectionStrategy: SelectionStrategy.ELITIST
      };

      const engine = new AlphaEvolveEngine(config, mockLogger);
      
      const request = {
        problem: 'Elitist selection test',
        domain: 'test',
        metrics: ['correctness'],
        constraints: {},
        config
      };

      const result = await engine.evolve(request);
      expect(result.bestSolution).toBeDefined();
    });
  });

  describe('diversity preservation', () => {
    it('should maintain genetic diversity', async () => {
      const request = {
        problem: 'Diversity preservation test',
        domain: 'general',
        metrics: ['innovation', 'performance'],
        constraints: {},
        config: evolutionConfig
      };

      const result = await alphaEvolveEngine.evolve(request);

      // Vérifier la diversité des approches
      const approaches = result.solutions.map(s => s.approach);
      const uniqueApproaches = new Set(approaches);
      
      expect(uniqueApproaches.size).toBeGreaterThan(1);
      expect(uniqueApproaches.size / approaches.length).toBeGreaterThan(0.3); // Au moins 30% de diversité
    });

    it('should balance exploration and exploitation', async () => {
      const request = {
        problem: 'Balance test',
        domain: 'optimization',
        metrics: ['performance', 'innovation'],
        constraints: {},
        config: evolutionConfig
      };

      const result = await alphaEvolveEngine.evolve(request);

      // Vérifier qu'il y a un équilibre entre performance et innovation
      const avgPerformance = result.solutions.reduce((sum, s) => sum + s.fitness.performance, 0) / result.solutions.length;
      const avgInnovation = result.solutions.reduce((sum, s) => sum + s.fitness.innovation, 0) / result.solutions.length;
      
      expect(avgPerformance).toBeGreaterThan(0);
      expect(avgInnovation).toBeGreaterThan(0);
    });
  });

  describe('error handling and robustness', () => {
    it('should handle invalid requests gracefully', async () => {
      const invalidRequest = {
        problem: '',
        domain: '',
        metrics: [],
        constraints: null,
        config: evolutionConfig
      };

      // Ne devrait pas lever d'exception
      const result = await alphaEvolveEngine.evolve(invalidRequest);
      expect(result).toBeDefined();
    });

    it('should handle stop requests', async () => {
      const longRunningConfig = {
        ...evolutionConfig,
        maxGenerations: 100
      };

      const engine = new AlphaEvolveEngine(longRunningConfig, mockLogger);
      
      const request = {
        problem: 'Long running test',
        domain: 'test',
        metrics: ['performance'],
        constraints: {},
        config: longRunningConfig
      };

      // Démarrer l'évolution
      const evolutionPromise = engine.evolve(request);
      
      // Arrêter après un court délai
      setTimeout(() => {
        engine.stop();
      }, 100);

      const result = await evolutionPromise;
      
      // Devrait s'arrêter prématurément
      expect(result.generationCount).toBeLessThan(longRunningConfig.maxGenerations);
    });

    it('should recover from agent failures', async () => {
      const request = {
        problem: 'Error recovery test',
        domain: 'test',
        metrics: ['performance'],
        constraints: {},
        config: evolutionConfig
      };

      // Même si des erreurs internes se produisent, l'évolution devrait continuer
      const result = await alphaEvolveEngine.evolve(request);
      
      expect(result).toBeDefined();
      expect(result.solutions.length).toBeGreaterThan(0);
    });
  });

  describe('performance and scalability', () => {
    it('should complete evolution within reasonable time', async () => {
      const startTime = Date.now();
      
      const request = {
        problem: 'Performance test',
        domain: 'test',
        metrics: ['performance'],
        constraints: {},
        config: evolutionConfig
      };

      const result = await alphaEvolveEngine.evolve(request);
      const executionTime = Date.now() - startTime;

      expect(executionTime).toBeLessThan(30000); // Moins de 30 secondes
      expect(result.executionTime).toBeGreaterThan(0);
      expect(result.executionTime).toBeLessThan(executionTime);
    });

    it('should scale with population size', async () => {
      const smallConfig = { ...evolutionConfig, populationSize: 5 };
      const largeConfig = { ...evolutionConfig, populationSize: 20 };

      const smallEngine = new AlphaEvolveEngine(smallConfig, mockLogger);
      const largeEngine = new AlphaEvolveEngine(largeConfig, mockLogger);

      const request = {
        problem: 'Scalability test',
        domain: 'test',
        metrics: ['performance'],
        constraints: {},
        config: evolutionConfig
      };

      const smallResult = await smallEngine.evolve({ ...request, config: smallConfig });
      const largeResult = await largeEngine.evolve({ ...request, config: largeConfig });

      expect(smallResult.solutions).toHaveLength(5);
      expect(largeResult.solutions).toHaveLength(20);
      
      // Le temps d'exécution devrait augmenter avec la taille de la population
      expect(largeResult.executionTime).toBeGreaterThan(smallResult.executionTime);
    });
  });
});
