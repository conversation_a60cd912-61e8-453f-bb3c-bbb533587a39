import { GeneticMemoryEngine } from '../engines/GeneticMemoryEngine';
import { GeneType, GeneticMemoryRequest, EvolutionSolution } from '../types/evolution';
import { mockLogger } from './setup';

describe('GeneticMemoryEngine', () => {
  let engine: GeneticMemoryEngine;

  beforeEach(async () => {
    engine = new GeneticMemoryEngine(mockLogger);
    await engine.initialize();
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      const newEngine = new GeneticMemoryEngine(mockLogger);
      await newEngine.initialize();
      
      expect(mockLogger.info).toHaveBeenCalledWith('🧬 Initialisation de la mémoire génétique');
      expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('✅ Mémoire génétique initialisée'));
    });

    test('should initialize with fundamental genes', async () => {
      const newEngine = new GeneticMemoryEngine(mockLogger);
      await newEngine.initialize();
      
      // Récupérer les gènes fondamentaux
      const algorithmGenes = await newEngine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.ALGORITHM
      });
      
      const patternGenes = await newEngine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.PATTERN
      });
      
      expect(algorithmGenes.length).toBeGreaterThan(0);
      expect(patternGenes.length).toBeGreaterThan(0);
    });
  });

  describe('Gene Storage', () => {
    test('should store a new gene successfully', async () => {
      const request: GeneticMemoryRequest = {
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: 'function quickSort(arr) { return arr.sort(); }',
        context: { domain: 'sorting', performance: 0.8 }
      };

      const gene = await engine.storeGene(request);

      expect(gene).toBeDefined();
      expect(gene.id).toBeDefined();
      expect(gene.sequence).toBe(request.pattern);
      expect(gene.type).toBe(request.geneType);
      expect(gene.fitness).toBe(0.5); // Fitness initiale
      expect(gene.age).toBe(0);
      expect(gene.usageCount).toBe(0);
    });

    test('should emit gene-stored event', async () => {
      const eventSpy = jest.fn();
      engine.on('gene-stored', eventSpy);

      const request: GeneticMemoryRequest = {
        operation: 'store',
        geneType: GeneType.BEHAVIOR,
        pattern: 'function validate(input) { return input !== null; }',
        context: { validation: true }
      };

      await engine.storeGene(request);

      expect(eventSpy).toHaveBeenCalledTimes(1);
      expect(eventSpy).toHaveBeenCalledWith(expect.objectContaining({
        type: GeneType.BEHAVIOR,
        sequence: request.pattern
      }));
    });

    test('should handle multiple genes of same type', async () => {
      const requests = [
        {
          operation: 'store' as const,
          geneType: GeneType.ALGORITHM,
          pattern: 'function bubbleSort(arr) { /* implementation */ }',
          context: { sorting: true }
        },
        {
          operation: 'store' as const,
          geneType: GeneType.ALGORITHM,
          pattern: 'function mergeSort(arr) { /* implementation */ }',
          context: { sorting: true }
        }
      ];

      const genes = await Promise.all(requests.map(req => engine.storeGene(req)));

      expect(genes).toHaveLength(2);
      expect(genes[0].id).not.toBe(genes[1].id);
      expect(genes.every(gene => gene.type === GeneType.ALGORITHM)).toBe(true);
    });
  });

  describe('Gene Retrieval', () => {
    beforeEach(async () => {
      // Stocker quelques gènes de test
      await engine.storeGene({
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: 'function sort(arr) { return arr.sort(); }',
        context: { domain: 'sorting' }
      });

      await engine.storeGene({
        operation: 'store',
        geneType: GeneType.PATTERN,
        pattern: 'const cache = new Map();',
        context: { domain: 'caching' }
      });

      await engine.storeGene({
        operation: 'store',
        geneType: GeneType.OPTIMIZATION,
        pattern: 'if (memo.has(key)) return memo.get(key);',
        context: { domain: 'memoization' }
      });
    });

    test('should retrieve genes by type', async () => {
      const algorithmGenes = await engine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.ALGORITHM
      });

      expect(algorithmGenes.length).toBeGreaterThan(0);
      expect(algorithmGenes.every(gene => gene.type === GeneType.ALGORITHM)).toBe(true);
    });

    test('should retrieve genes by pattern', async () => {
      const sortGenes = await engine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.ALGORITHM,
        pattern: 'sort'
      });

      expect(sortGenes.length).toBeGreaterThan(0);
      expect(sortGenes.some(gene => gene.sequence.includes('sort'))).toBe(true);
    });

    test('should filter by fitness threshold', async () => {
      // Mettre à jour la fitness d'un gène
      const genes = await engine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.ALGORITHM
      });

      if (genes.length > 0) {
        await engine.updateGeneFitness(genes[0].id, 0.9);
      }

      const highFitnessGenes = await engine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.ALGORITHM
      });

      // Tous les gènes retournés doivent avoir une fitness >= seuil
      expect(highFitnessGenes.every(gene => gene.fitness >= 0.7)).toBe(true);
    });

    test('should sort genes by fitness and recency', async () => {
      const genes = await engine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.ALGORITHM
      });

      if (genes.length > 1) {
        // Vérifier que les gènes sont triés par fitness décroissante
        for (let i = 0; i < genes.length - 1; i++) {
          expect(genes[i].fitness).toBeGreaterThanOrEqual(genes[i + 1].fitness);
        }
      }
    });

    test('should update usage statistics on retrieval', async () => {
      const genesBefore = await engine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.ALGORITHM
      });

      if (genesBefore.length > 0) {
        const initialUsageCount = genesBefore[0].usageCount;
        const initialLastUsed = genesBefore[0].lastUsed;

        // Attendre un peu pour voir la différence de timestamp
        await new Promise(resolve => setTimeout(resolve, 10));

        const genesAfter = await engine.retrieveGenes({
          operation: 'retrieve',
          geneType: GeneType.ALGORITHM
        });

        expect(genesAfter[0].usageCount).toBe(initialUsageCount + 1);
        expect(genesAfter[0].lastUsed.getTime()).toBeGreaterThan(initialLastUsed.getTime());
      }
    });
  });

  describe('Gene Evolution', () => {
    beforeEach(async () => {
      // Stocker des gènes parents avec fitness élevée
      const parentGenes = [
        {
          operation: 'store' as const,
          geneType: GeneType.ALGORITHM,
          pattern: 'function quickSort(arr) { /* implementation */ }',
          context: { performance: 0.9 }
        },
        {
          operation: 'store' as const,
          geneType: GeneType.ALGORITHM,
          pattern: 'function mergeSort(arr) { /* implementation */ }',
          context: { performance: 0.85 }
        }
      ];

      for (const geneData of parentGenes) {
        const gene = await engine.storeGene(geneData);
        await engine.updateGeneFitness(gene.id, 0.8); // Fitness élevée
      }
    });

    test('should evolve genes through recombination', async () => {
      const evolvedGenes = await engine.evolveGenes({
        operation: 'evolve',
        geneType: GeneType.ALGORITHM
      });

      expect(evolvedGenes.length).toBeGreaterThan(0);
      expect(evolvedGenes.every(gene => gene.type === GeneType.ALGORITHM)).toBe(true);
      expect(evolvedGenes.every(gene => gene.id.includes('recomb') || gene.id.includes('mut'))).toBe(true);
    });

    test('should handle insufficient parent genes', async () => {
      // Tester avec un type qui n'a pas assez de gènes
      const evolvedGenes = await engine.evolveGenes({
        operation: 'evolve',
        geneType: GeneType.INTERFACE
      });

      expect(evolvedGenes).toHaveLength(0);
      expect(mockLogger.warn).toHaveBeenCalledWith('Pas assez de gènes parents pour l\'évolution');
    });

    test('should create lineage for evolved genes', async () => {
      const evolvedGenes = await engine.evolveGenes({
        operation: 'evolve',
        geneType: GeneType.ALGORITHM
      });

      expect(evolvedGenes.length).toBeGreaterThan(0);
      // Les gènes évolués devraient avoir des IDs indiquant leur origine
      expect(evolvedGenes.some(gene => gene.id.includes('recomb'))).toBe(true);
    });
  });

  describe('Gene Extraction from Solutions', () => {
    test('should extract genes from high-fitness solution', async () => {
      const solution: EvolutionSolution = {
        id: 'solution-1',
        code: `
          function optimizedSort(arr) {
            for (let i = 0; i < arr.length; i++) {
              // Bubble sort implementation
            }
            return arr;
          }
        `,
        description: 'Optimized sorting algorithm',
        approach: 'iterative',
        fitness: {
          total: 0.9,
          performance: 0.9,
          correctness: 0.95,
          efficiency: 0.85,
          robustness: 0.8,
          maintainability: 0.9,
          innovation: 0.7
        },
        generation: 5,
        parentIds: [],
        mutations: [],
        performance: {
          executionTime: 100,
          memoryUsage: 1024,
          cpuUsage: 50,
          complexity: {
            timeComplexity: 'O(n²)',
            spaceComplexity: 'O(1)',
            cyclomaticComplexity: 3,
            cognitiveComplexity: 2
          },
          scalability: 0.8
        }
      };

      const extractedGenes = await engine.extractGenesFromSolution(solution);

      expect(extractedGenes.length).toBeGreaterThan(0);
      expect(extractedGenes.every(gene => gene.fitness === solution.fitness.total)).toBe(true);
      expect(extractedGenes.some(gene => gene.type === GeneType.ALGORITHM)).toBe(true);
    });

    test('should not auto-store low-fitness genes', async () => {
      const lowFitnessSolution: EvolutionSolution = {
        id: 'solution-low',
        code: 'function badSort(arr) { return arr; }',
        description: 'Poor sorting algorithm',
        approach: 'naive',
        fitness: {
          total: 0.3, // Fitness faible
          performance: 0.3,
          correctness: 0.2,
          efficiency: 0.4,
          robustness: 0.3,
          maintainability: 0.3,
          innovation: 0.2
        },
        generation: 1,
        parentIds: [],
        mutations: [],
        performance: {
          executionTime: 1000,
          memoryUsage: 2048,
          cpuUsage: 90,
          complexity: {
            timeComplexity: 'O(n³)',
            spaceComplexity: 'O(n)',
            cyclomaticComplexity: 10,
            cognitiveComplexity: 8
          },
          scalability: 0.2
        }
      };

      const extractedGenes = await engine.extractGenesFromSolution(lowFitnessSolution);

      // Les gènes sont extraits mais pas automatiquement stockés
      expect(extractedGenes.length).toBeGreaterThan(0);
      expect(extractedGenes.every(gene => gene.fitness < 0.7)).toBe(true);
    });
  });

  describe('Fitness Updates', () => {
    test('should update gene fitness progressively', async () => {
      const gene = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.ALGORITHM,
        pattern: 'function test() { return true; }',
        context: {}
      });

      const initialFitness = gene.fitness;
      await engine.updateGeneFitness(gene.id, 0.9);

      // La fitness devrait être mise à jour progressivement (moyenne pondérée)
      const updatedGenes = await engine.retrieveGenes({
        operation: 'retrieve',
        geneType: GeneType.ALGORITHM
      });

      const updatedGene = updatedGenes.find(g => g.id === gene.id);
      expect(updatedGene).toBeDefined();
      expect(updatedGene!.fitness).toBeGreaterThan(initialFitness);
      expect(updatedGene!.fitness).toBeLessThan(0.9); // Pas exactement 0.9 à cause du lissage
      expect(updatedGene!.age).toBe(1);
    });

    test('should emit fitness update event', async () => {
      const eventSpy = jest.fn();
      engine.on('gene-fitness-updated', eventSpy);

      const gene = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.PATTERN,
        pattern: 'const x = 1;',
        context: {}
      });

      await engine.updateGeneFitness(gene.id, 0.8, { test: true });

      expect(eventSpy).toHaveBeenCalledTimes(1);
      expect(eventSpy).toHaveBeenCalledWith(expect.objectContaining({
        gene: expect.objectContaining({ id: gene.id }),
        newFitness: 0.8,
        context: { test: true }
      }));
    });
  });

  describe('Process Request Interface', () => {
    test('should handle store operation', async () => {
      const request: GeneticMemoryRequest = {
        operation: 'store',
        geneType: GeneType.STRUCTURE,
        pattern: 'class TestClass {}',
        context: { oop: true }
      };

      const result = await engine.processRequest(request);

      expect(result).toBeDefined();
      expect(result.type).toBe(GeneType.STRUCTURE);
      expect(result.sequence).toBe(request.pattern);
    });

    test('should handle retrieve operation', async () => {
      // Stocker d'abord un gène
      await engine.storeGene({
        operation: 'store',
        geneType: GeneType.BEHAVIOR,
        pattern: 'function behavior() {}',
        context: {}
      });

      const request: GeneticMemoryRequest = {
        operation: 'retrieve',
        geneType: GeneType.BEHAVIOR
      };

      const result = await engine.processRequest(request);

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
    });

    test('should handle evolve operation', async () => {
      // Stocker des gènes parents
      await engine.storeGene({
        operation: 'store',
        geneType: GeneType.OPTIMIZATION,
        pattern: 'function optimize1() {}',
        context: {}
      });

      const gene = await engine.storeGene({
        operation: 'store',
        geneType: GeneType.OPTIMIZATION,
        pattern: 'function optimize2() {}',
        context: {}
      });

      await engine.updateGeneFitness(gene.id, 0.8);

      const request: GeneticMemoryRequest = {
        operation: 'evolve',
        geneType: GeneType.OPTIMIZATION
      };

      const result = await engine.processRequest(request);

      expect(Array.isArray(result)).toBe(true);
    });

    test('should throw error for unsupported operation', async () => {
      const request = {
        operation: 'invalid' as any,
        geneType: GeneType.ALGORITHM
      };

      await expect(engine.processRequest(request)).rejects.toThrow('Opération non supportée: invalid');
    });
  });
});
