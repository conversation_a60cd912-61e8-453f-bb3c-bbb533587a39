/**
 * Interface pour le système de logging
 */
export interface Logger {
  info(message: string, ...args: any[]): void;
  debug(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;
}

/**
 * Implémentation simple du logger pour les tests et le développement
 */
export class SimpleLogger implements Logger {
  private logLevel: LogLevel;

  constructor(logLevel: LogLevel = LogLevel.INFO) {
    this.logLevel = logLevel;
  }

  info(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.INFO) {
      console.log(`[INFO] ${new Date().toISOString()} - ${message}`, ...args);
    }
  }

  debug(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      console.log(`[DEBUG] ${new Date().toISOString()} - ${message}`, ...args);
    }
  }

  warn(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.WARN) {
      console.warn(`[WARN] ${new Date().toISOString()} - ${message}`, ...args);
    }
  }

  error(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.ERROR) {
      console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, ...args);
    }
  }
}

/**
 * Logger silencieux pour les tests
 */
export class SilentLogger implements Logger {
  info(_message: string, ..._args: any[]): void {
    // Ne fait rien
  }

  debug(_message: string, ..._args: any[]): void {
    // Ne fait rien
  }

  warn(_message: string, ..._args: any[]): void {
    // Ne fait rien
  }

  error(_message: string, ..._args: any[]): void {
    // Ne fait rien
  }
}

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

// Instance par défaut
export const logger = new SimpleLogger(LogLevel.INFO);
