{"name": "agent-evolution", "version": "1.0.0", "description": "Agent spécialisé dans l'évolution continue, le tech radar et l'auto-déploiement", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "demo:genetic": "ts-node src/demo/GeneticMemoryDemo.ts", "demo:sprint3": "npm run demo:genetic", "demo:integration": "ts-node src/demo/Sprint4Demo.ts", "demo:sprint4": "npm run demo:integration", "demo:final": "ts-node src/demo/Sprint5FinalDemo.ts", "demo:sprint5": "npm run demo:final", "demo:all": "npm run demo:sprint3 && npm run demo:sprint4 && npm run demo:sprint5", "test:integration": "jest src/tests/CortexIntegration.test.ts", "test:biomimetic": "jest src/tests/BiomimeticValidation.test.ts", "test:load": "jest src/tests/LoadStressTesting.test.ts", "test:sprint5": "npm run test:integration && npm run test:biomimetic && npm run test:load"}, "keywords": ["evolution", "tech-radar", "auto-deployment", "continuous-learning", "trend-tracking", "system-optimization"], "author": "Retreat And Be", "license": "MIT", "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "dockerode": "^4.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "moment": "^2.29.4", "node-cron": "^3.0.3", "npm-registry-fetch": "^16.1.0", "octokit": "^3.1.2", "redis": "^4.6.10", "semver": "^7.5.4", "tar": "^6.2.0", "ts-node": "^10.9.2", "uuid": "^9.0.1", "weaviate-ts-client": "^1.5.0", "winston": "^3.11.0", "yaml": "^2.3.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/node": "^20.9.0", "@types/semver": "^7.5.6", "@types/tar": "^6.1.8", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}