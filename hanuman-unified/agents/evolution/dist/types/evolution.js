"use strict";
/**
 * Types pour l'Agent d'Évolution AlphaEvolve
 * Inspiré du biomimétisme et de l'évolution naturelle
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Priority = exports.SelectionStrategy = exports.AdaptationType = exports.GeneType = exports.MutationType = exports.EvolutionType = void 0;
// Enums
var EvolutionType;
(function (EvolutionType) {
    EvolutionType["ALGORITHM_OPTIMIZATION"] = "algorithm_optimization";
    EvolutionType["CODE_GENERATION"] = "code_generation";
    EvolutionType["ARCHITECTURE_EVOLUTION"] = "architecture_evolution";
    EvolutionType["PERFORMANCE_TUNING"] = "performance_tuning";
    EvolutionType["BUG_FIXING"] = "bug_fixing";
    EvolutionType["FEATURE_ENHANCEMENT"] = "feature_enhancement";
    EvolutionType["SECURITY_HARDENING"] = "security_hardening";
    EvolutionType["NEURAL_ADAPTATION"] = "neural_adaptation";
})(EvolutionType || (exports.EvolutionType = EvolutionType = {}));
var MutationType;
(function (MutationType) {
    MutationType["SIMPLE"] = "simple";
    MutationType["CROSSOVER"] = "crossover";
    MutationType["CREATIVE"] = "creative";
    MutationType["OPTIMIZATION"] = "optimization";
    MutationType["REFACTORING"] = "refactoring";
    MutationType["HYBRIDIZATION"] = "hybridization";
})(MutationType || (exports.MutationType = MutationType = {}));
var GeneType;
(function (GeneType) {
    GeneType["ALGORITHM"] = "algorithm";
    GeneType["PATTERN"] = "pattern";
    GeneType["OPTIMIZATION"] = "optimization";
    GeneType["STRUCTURE"] = "structure";
    GeneType["BEHAVIOR"] = "behavior";
    GeneType["INTERFACE"] = "interface";
})(GeneType || (exports.GeneType = GeneType = {}));
var AdaptationType;
(function (AdaptationType) {
    AdaptationType["SYNAPTIC_STRENGTHENING"] = "synaptic_strengthening";
    AdaptationType["SYNAPTIC_WEAKENING"] = "synaptic_weakening";
    AdaptationType["NEW_CONNECTION"] = "new_connection";
    AdaptationType["CONNECTION_PRUNING"] = "connection_pruning";
    AdaptationType["PATHWAY_OPTIMIZATION"] = "pathway_optimization";
})(AdaptationType || (exports.AdaptationType = AdaptationType = {}));
var SelectionStrategy;
(function (SelectionStrategy) {
    SelectionStrategy["TOURNAMENT"] = "tournament";
    SelectionStrategy["ROULETTE"] = "roulette";
    SelectionStrategy["RANK_BASED"] = "rank_based";
    SelectionStrategy["ELITIST"] = "elitist";
    SelectionStrategy["DIVERSITY_PRESERVING"] = "diversity_preserving";
})(SelectionStrategy || (exports.SelectionStrategy = SelectionStrategy = {}));
var Priority;
(function (Priority) {
    Priority["LOW"] = "low";
    Priority["MEDIUM"] = "medium";
    Priority["HIGH"] = "high";
    Priority["CRITICAL"] = "critical";
})(Priority || (exports.Priority = Priority = {}));
//# sourceMappingURL=evolution.js.map