/**
 * Types pour l'Agent Evolution
 */
export interface AgentConfig {
    port: number;
    kafka: {
        brokers: string[];
        clientId: string;
        groupId: string;
    };
    weaviate: {
        scheme: string;
        host: string;
        port: number;
    };
    redis: {
        host: string;
        port: number;
        password?: string;
    };
    docker: {
        host: string;
        port: number;
        registry: string;
    };
    github: {
        token?: string;
        organization?: string;
    };
}
export interface TechRadarItem {
    id: string;
    name: string;
    description: string;
    category: TechCategory;
    quadrant: TechQuadrant;
    ring: TechRing;
    status: TechStatus;
    firstAppeared: Date;
    lastUpdated: Date;
    trend: TechTrend;
    impact: TechImpact;
    adoptionLevel: number;
    maturityLevel: number;
    riskLevel: number;
    sources: TechSource[];
    relatedTechnologies: string[];
    useCases: UseCase[];
    pros: string[];
    cons: string[];
    recommendations: TechRecommendation[];
    metadata: TechMetadata;
}
export type TechCategory = 'programming_languages' | 'frameworks' | 'libraries' | 'tools' | 'platforms' | 'databases' | 'infrastructure' | 'methodologies' | 'ai_ml' | 'security' | 'devops' | 'frontend' | 'backend' | 'mobile' | 'cloud' | 'iot' | 'blockchain';
export type TechQuadrant = 'techniques' | 'tools' | 'platforms' | 'languages_frameworks';
export type TechRing = 'adopt' | 'trial' | 'assess' | 'hold';
export type TechStatus = 'new' | 'rising' | 'stable' | 'declining' | 'deprecated';
export type TechTrend = 'emerging' | 'growing' | 'mainstream' | 'mature' | 'legacy';
export type TechImpact = 'low' | 'medium' | 'high' | 'transformative';
export interface TechSource {
    type: 'github' | 'npm' | 'stackoverflow' | 'reddit' | 'twitter' | 'blog' | 'conference' | 'survey';
    url: string;
    title: string;
    date: Date;
    credibility: number;
    relevance: number;
}
export interface UseCase {
    title: string;
    description: string;
    industry: string;
    complexity: 'low' | 'medium' | 'high';
    benefits: string[];
    challenges: string[];
}
export interface TechRecommendation {
    type: 'adopt' | 'evaluate' | 'avoid' | 'migrate_from';
    reason: string;
    timeline: string;
    effort: 'low' | 'medium' | 'high';
    risk: 'low' | 'medium' | 'high';
    alternatives?: string[];
}
export interface TechMetadata {
    githubStars?: number;
    npmDownloads?: number;
    stackOverflowQuestions?: number;
    jobPostings?: number;
    communitySize?: number;
    lastRelease?: Date;
    license?: string;
    maintainers?: number;
    contributors?: number;
    issues?: number;
    pullRequests?: number;
}
export interface EvolutionPlan {
    id: string;
    name: string;
    description: string;
    version: string;
    targetDate: Date;
    status: EvolutionStatus;
    priority: Priority;
    type: EvolutionType;
    scope: EvolutionScope;
    changes: EvolutionChange[];
    dependencies: EvolutionDependency[];
    risks: EvolutionRisk[];
    rollbackPlan: RollbackPlan;
    testing: TestingPlan;
    deployment: DeploymentPlan;
    monitoring: MonitoringPlan;
    approval: ApprovalProcess;
    metadata: EvolutionMetadata;
}
export type EvolutionStatus = 'planned' | 'approved' | 'in_progress' | 'testing' | 'deployed' | 'completed' | 'failed' | 'rolled_back';
export type Priority = 'low' | 'medium' | 'high' | 'critical';
export type EvolutionType = 'feature_addition' | 'performance_improvement' | 'security_update' | 'bug_fix' | 'dependency_update' | 'architecture_change' | 'migration' | 'deprecation';
export type EvolutionScope = 'agent' | 'service' | 'system' | 'infrastructure' | 'configuration';
export interface EvolutionChange {
    id: string;
    type: ChangeType;
    component: string;
    description: string;
    impact: ChangeImpact;
    effort: number;
    files: string[];
    tests: string[];
    documentation: string[];
}
export type ChangeType = 'addition' | 'modification' | 'removal' | 'refactoring' | 'configuration';
export interface ChangeImpact {
    performance: 'positive' | 'negative' | 'neutral';
    security: 'improved' | 'degraded' | 'neutral';
    usability: 'improved' | 'degraded' | 'neutral';
    maintainability: 'improved' | 'degraded' | 'neutral';
    compatibility: 'breaking' | 'compatible' | 'enhanced';
}
export interface EvolutionDependency {
    id: string;
    type: 'requires' | 'blocks' | 'enhances';
    target: string;
    description: string;
    critical: boolean;
}
export interface EvolutionRisk {
    id: string;
    description: string;
    probability: number;
    impact: number;
    riskScore: number;
    mitigation: string[];
    contingency: string[];
    owner: string;
}
export interface RollbackPlan {
    strategy: 'automatic' | 'manual' | 'hybrid';
    triggers: RollbackTrigger[];
    steps: RollbackStep[];
    timeLimit: number;
    dataBackup: boolean;
    verificationSteps: string[];
}
export interface RollbackTrigger {
    type: 'error_rate' | 'performance' | 'manual' | 'timeout';
    threshold: number;
    duration: number;
    description: string;
}
export interface RollbackStep {
    order: number;
    description: string;
    command: string;
    timeout: number;
    verification: string;
}
export interface TestingPlan {
    phases: TestPhase[];
    coverage: TestCoverage;
    automation: TestAutomation;
    environments: TestEnvironment[];
    criteria: AcceptanceCriteria[];
}
export interface TestPhase {
    name: string;
    type: 'unit' | 'integration' | 'system' | 'acceptance' | 'performance' | 'security';
    duration: number;
    parallel: boolean;
    dependencies: string[];
    tests: TestCase[];
}
export interface TestCase {
    id: string;
    name: string;
    description: string;
    type: string;
    priority: Priority;
    automated: boolean;
    steps: TestStep[];
    expectedResult: string;
    actualResult?: string;
    status?: 'pending' | 'running' | 'passed' | 'failed' | 'skipped';
}
export interface TestStep {
    order: number;
    action: string;
    data?: any;
    expected: string;
}
export interface TestCoverage {
    unit: number;
    integration: number;
    system: number;
    target: number;
}
export interface TestAutomation {
    percentage: number;
    tools: string[];
    cicd: boolean;
    reporting: boolean;
}
export interface TestEnvironment {
    name: string;
    type: 'development' | 'staging' | 'production' | 'testing';
    configuration: Record<string, any>;
    dataSet: string;
    isolated: boolean;
}
export interface AcceptanceCriteria {
    id: string;
    description: string;
    type: 'functional' | 'performance' | 'security' | 'usability';
    measurable: boolean;
    threshold?: number;
    unit?: string;
}
export interface DeploymentPlan {
    strategy: DeploymentStrategy;
    phases: DeploymentPhase[];
    schedule: DeploymentSchedule;
    infrastructure: InfrastructureRequirements;
    communication: CommunicationPlan;
    verification: VerificationPlan;
}
export type DeploymentStrategy = 'blue_green' | 'rolling' | 'canary' | 'recreate' | 'a_b_testing';
export interface DeploymentPhase {
    name: string;
    order: number;
    type: 'preparation' | 'deployment' | 'verification' | 'cleanup';
    duration: number;
    parallel: boolean;
    steps: DeploymentStep[];
    rollbackPoint: boolean;
}
export interface DeploymentStep {
    order: number;
    name: string;
    type: 'script' | 'manual' | 'api_call' | 'verification';
    command?: string;
    timeout: number;
    retries: number;
    verification: string;
    rollbackCommand?: string;
}
export interface DeploymentSchedule {
    startTime: Date;
    endTime: Date;
    timezone: string;
    maintenanceWindow: boolean;
    notifications: NotificationSchedule[];
}
export interface NotificationSchedule {
    type: 'start' | 'progress' | 'completion' | 'failure';
    recipients: string[];
    channels: string[];
    template: string;
    timing: string;
}
export interface InfrastructureRequirements {
    resources: ResourceRequirement[];
    dependencies: string[];
    scaling: ScalingRequirements;
    monitoring: string[];
}
export interface ResourceRequirement {
    type: 'cpu' | 'memory' | 'storage' | 'network';
    amount: number;
    unit: string;
    duration: string;
}
export interface ScalingRequirements {
    horizontal: boolean;
    vertical: boolean;
    automatic: boolean;
    triggers: ScalingTrigger[];
}
export interface ScalingTrigger {
    metric: string;
    threshold: number;
    duration: number;
    action: 'scale_up' | 'scale_down';
}
export interface CommunicationPlan {
    stakeholders: Stakeholder[];
    channels: CommunicationChannel[];
    schedule: CommunicationSchedule[];
    templates: CommunicationTemplate[];
}
export interface Stakeholder {
    id: string;
    name: string;
    role: string;
    email: string;
    notificationLevel: 'all' | 'critical' | 'summary';
    timezone: string;
}
export interface CommunicationChannel {
    type: 'email' | 'slack' | 'teams' | 'sms' | 'dashboard';
    config: Record<string, any>;
    priority: Priority;
    fallback?: string;
}
export interface CommunicationSchedule {
    phase: string;
    timing: string;
    recipients: string[];
    channels: string[];
    message: string;
}
export interface CommunicationTemplate {
    id: string;
    name: string;
    type: string;
    subject: string;
    body: string;
    variables: string[];
}
export interface VerificationPlan {
    checks: VerificationCheck[];
    monitoring: VerificationMonitoring;
    alerts: VerificationAlert[];
    reporting: VerificationReporting;
}
export interface VerificationCheck {
    id: string;
    name: string;
    type: 'health' | 'performance' | 'functionality' | 'security';
    frequency: string;
    timeout: number;
    retries: number;
    threshold: number;
    command: string;
    expectedResult: any;
}
export interface VerificationMonitoring {
    duration: number;
    metrics: string[];
    dashboards: string[];
    alerts: boolean;
}
export interface VerificationAlert {
    condition: string;
    severity: 'info' | 'warning' | 'error' | 'critical';
    recipients: string[];
    channels: string[];
    escalation: EscalationRule[];
}
export interface EscalationRule {
    level: number;
    delay: number;
    recipients: string[];
    channels: string[];
}
export interface VerificationReporting {
    frequency: string;
    recipients: string[];
    format: 'email' | 'dashboard' | 'api';
    metrics: string[];
    charts: boolean;
}
export interface MonitoringPlan {
    metrics: MonitoringMetric[];
    alerts: MonitoringAlert[];
    dashboards: MonitoringDashboard[];
    retention: DataRetention;
}
export interface MonitoringMetric {
    name: string;
    type: 'counter' | 'gauge' | 'histogram' | 'summary';
    description: string;
    unit: string;
    labels: string[];
    collection: MetricCollection;
}
export interface MetricCollection {
    frequency: string;
    method: 'push' | 'pull';
    endpoint?: string;
    query?: string;
}
export interface MonitoringAlert {
    name: string;
    condition: string;
    severity: 'info' | 'warning' | 'error' | 'critical';
    duration: string;
    recipients: string[];
    channels: string[];
    runbook?: string;
}
export interface MonitoringDashboard {
    name: string;
    description: string;
    panels: DashboardPanel[];
    refresh: string;
    timeRange: string;
}
export interface DashboardPanel {
    title: string;
    type: 'graph' | 'table' | 'stat' | 'gauge';
    query: string;
    visualization: Record<string, any>;
}
export interface DataRetention {
    raw: string;
    aggregated: string;
    alerts: string;
    logs: string;
}
export interface ApprovalProcess {
    required: boolean;
    approvers: Approver[];
    stages: ApprovalStage[];
    criteria: ApprovalCriteria[];
    timeout: number;
}
export interface Approver {
    id: string;
    name: string;
    role: string;
    email: string;
    level: number;
    backup?: string;
}
export interface ApprovalStage {
    name: string;
    order: number;
    approvers: string[];
    required: number;
    timeout: number;
    criteria: string[];
}
export interface ApprovalCriteria {
    id: string;
    description: string;
    type: 'technical' | 'business' | 'security' | 'compliance';
    mandatory: boolean;
    evidence: string[];
}
export interface EvolutionMetadata {
    createdBy: string;
    createdAt: Date;
    updatedBy: string;
    updatedAt: Date;
    version: string;
    tags: string[];
    customFields: Record<string, any>;
}
export interface UXTrend {
    id: string;
    name: string;
    description: string;
    category: UXCategory;
    type: UXTrendType;
    status: TrendStatus;
    impact: UXImpact;
    adoptionRate: number;
    maturityLevel: number;
    sources: UXSource[];
    examples: UXExample[];
    implementation: UXImplementation;
    metrics: UXMetrics;
    recommendations: UXRecommendation[];
    relatedTrends: string[];
    timeline: UXTimeline;
}
export type UXCategory = 'visual_design' | 'interaction_design' | 'information_architecture' | 'user_research' | 'accessibility' | 'mobile_ux' | 'web_ux' | 'voice_ui' | 'ar_vr' | 'ai_ux';
export type UXTrendType = 'design_pattern' | 'interaction_method' | 'visual_style' | 'technology' | 'methodology' | 'tool';
export type TrendStatus = 'emerging' | 'growing' | 'mainstream' | 'declining' | 'obsolete';
export type UXImpact = 'revolutionary' | 'significant' | 'moderate' | 'minimal';
export interface UXSource {
    type: 'dribbble' | 'behance' | 'awwwards' | 'medium' | 'conference' | 'research';
    url: string;
    title: string;
    author: string;
    date: Date;
    credibility: number;
}
export interface UXExample {
    title: string;
    description: string;
    company: string;
    url?: string;
    screenshot?: string;
    category: string;
    effectiveness: number;
}
export interface UXImplementation {
    difficulty: 'easy' | 'medium' | 'hard';
    timeToImplement: string;
    resources: string[];
    tools: string[];
    skills: string[];
    cost: 'low' | 'medium' | 'high';
}
export interface UXMetrics {
    userSatisfaction?: number;
    conversionRate?: number;
    taskCompletionRate?: number;
    timeOnTask?: number;
    errorRate?: number;
    adoptionRate?: number;
}
export interface UXRecommendation {
    type: 'implement' | 'experiment' | 'monitor' | 'avoid';
    reason: string;
    context: string[];
    timeline: string;
    priority: Priority;
    risks: string[];
    benefits: string[];
}
export interface UXTimeline {
    firstSeen: Date;
    peakAdoption?: Date;
    decline?: Date;
    predictions: UXPrediction[];
}
export interface UXPrediction {
    timeframe: string;
    prediction: string;
    confidence: number;
    factors: string[];
}
export interface AutoDeployment {
    id: string;
    name: string;
    description: string;
    trigger: DeploymentTrigger;
    target: DeploymentTarget;
    pipeline: DeploymentPipeline;
    conditions: DeploymentCondition[];
    notifications: DeploymentNotification[];
    rollback: AutoRollback;
    monitoring: DeploymentMonitoring;
    history: DeploymentHistory[];
}
export interface DeploymentTrigger {
    type: 'schedule' | 'event' | 'manual' | 'webhook';
    config: Record<string, any>;
    conditions: string[];
}
export interface DeploymentTarget {
    environment: string;
    services: string[];
    infrastructure: string[];
    configuration: Record<string, any>;
}
export interface DeploymentPipeline {
    stages: PipelineStage[];
    parallelism: number;
    timeout: number;
    retries: number;
}
export interface PipelineStage {
    name: string;
    order: number;
    type: 'build' | 'test' | 'deploy' | 'verify';
    steps: PipelineStep[];
    conditions: string[];
    timeout: number;
}
export interface PipelineStep {
    name: string;
    command: string;
    timeout: number;
    retries: number;
    continueOnError: boolean;
    environment: Record<string, string>;
}
export interface DeploymentCondition {
    type: 'health_check' | 'test_results' | 'approval' | 'schedule';
    config: Record<string, any>;
    required: boolean;
}
export interface DeploymentNotification {
    event: 'start' | 'success' | 'failure' | 'rollback';
    recipients: string[];
    channels: string[];
    template: string;
}
export interface AutoRollback {
    enabled: boolean;
    triggers: RollbackTrigger[];
    strategy: 'immediate' | 'gradual' | 'manual_approval';
    timeout: number;
}
export interface DeploymentMonitoring {
    duration: number;
    metrics: string[];
    thresholds: Record<string, number>;
    alerts: string[];
}
export interface DeploymentHistory {
    id: string;
    timestamp: Date;
    version: string;
    status: 'success' | 'failure' | 'rollback';
    duration: number;
    changes: string[];
    metrics: Record<string, number>;
    logs: string[];
}
//# sourceMappingURL=index.d.ts.map