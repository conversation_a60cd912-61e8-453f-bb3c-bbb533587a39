/**
 * Types pour l'Agent d'Évolution AlphaEvolve
 * Inspiré du biomimétisme et de l'évolution naturelle
 */
export interface EvolutionRequest {
    id: string;
    type: EvolutionType;
    target: EvolutionTarget;
    constraints: EvolutionConstraints;
    objectives: EvolutionObjective[];
    priority: Priority;
    context?: any;
}
export interface EvolutionResult {
    id: string;
    requestId: string;
    solutions: EvolutionSolution[];
    bestSolution: EvolutionSolution;
    generationCount: number;
    convergenceScore: number;
    executionTime: number;
    metadata: EvolutionMetadata;
}
export interface EvolutionSolution {
    id: string;
    code: string;
    description: string;
    approach: string;
    fitness: FitnessScore;
    generation: number;
    parentIds: string[];
    mutations: Mutation[];
    performance: PerformanceMetrics;
}
export interface FitnessScore {
    total: number;
    performance: number;
    correctness: number;
    efficiency: number;
    robustness: number;
    maintainability: number;
    innovation: number;
}
export interface PerformanceMetrics {
    executionTime: number;
    memoryUsage: number;
    cpuUsage: number;
    complexity: ComplexityMetrics;
    scalability: number;
}
export interface ComplexityMetrics {
    timeComplexity: string;
    spaceComplexity: string;
    cyclomaticComplexity: number;
    cognitiveComplexity: number;
}
export interface Mutation {
    id: string;
    type: MutationType;
    description: string;
    impact: number;
    success: boolean;
    parentSolutionId: string;
}
export interface GeneticCode {
    id: string;
    sequence: string;
    type: GeneType;
    expression: string;
    fitness: number;
    age: number;
    usageCount: number;
    lastUsed: Date;
}
export interface NeuralAdaptation {
    id: string;
    fromAgent: string;
    toAgent: string;
    synapticStrength: number;
    adaptationType: AdaptationType;
    learningRate: number;
    timestamp: Date;
}
export interface EvolutionPopulation {
    id: string;
    generation: number;
    solutions: EvolutionSolution[];
    elites: EvolutionSolution[];
    diversity: number;
    averageFitness: number;
    bestFitness: number;
}
export interface EvolutionConfig {
    populationSize: number;
    eliteRatio: number;
    mutationRate: number;
    crossoverRate: number;
    maxGenerations: number;
    convergenceThreshold: number;
    fitnessWeights: FitnessWeights;
    selectionStrategy: SelectionStrategy;
}
export interface FitnessWeights {
    performance: number;
    correctness: number;
    efficiency: number;
    robustness: number;
    maintainability: number;
    innovation: number;
}
export interface EvolutionTarget {
    domain: string;
    problem: string;
    inputFormat: string;
    outputFormat: string;
    testCases: TestCase[];
    benchmarks: Benchmark[];
}
export interface EvolutionConstraints {
    maxExecutionTime: number;
    maxMemoryUsage: number;
    maxCodeLines: number;
    requiredInterfaces: string[];
    forbiddenPatterns: string[];
    complianceRules: string[];
}
export interface EvolutionObjective {
    name: string;
    weight: number;
    target: number;
    metric: string;
    direction: 'minimize' | 'maximize';
}
export interface TestCase {
    id: string;
    input: any;
    expectedOutput: any;
    weight: number;
    timeout: number;
}
export interface Benchmark {
    id: string;
    name: string;
    dataset: string;
    metric: string;
    baseline: number;
    target: number;
}
export interface EvolutionMetadata {
    startTime: Date;
    endTime: Date;
    totalEvaluations: number;
    successfulMutations: number;
    failedMutations: number;
    diversityHistory: number[];
    fitnessHistory: number[];
    convergencePoint?: number;
}
export declare enum EvolutionType {
    ALGORITHM_OPTIMIZATION = "algorithm_optimization",
    CODE_GENERATION = "code_generation",
    ARCHITECTURE_EVOLUTION = "architecture_evolution",
    PERFORMANCE_TUNING = "performance_tuning",
    BUG_FIXING = "bug_fixing",
    FEATURE_ENHANCEMENT = "feature_enhancement",
    SECURITY_HARDENING = "security_hardening",
    NEURAL_ADAPTATION = "neural_adaptation"
}
export declare enum MutationType {
    SIMPLE = "simple",
    CROSSOVER = "crossover",
    CREATIVE = "creative",
    OPTIMIZATION = "optimization",
    REFACTORING = "refactoring",
    HYBRIDIZATION = "hybridization"
}
export declare enum GeneType {
    ALGORITHM = "algorithm",
    PATTERN = "pattern",
    OPTIMIZATION = "optimization",
    STRUCTURE = "structure",
    BEHAVIOR = "behavior",
    INTERFACE = "interface"
}
export declare enum AdaptationType {
    SYNAPTIC_STRENGTHENING = "synaptic_strengthening",
    SYNAPTIC_WEAKENING = "synaptic_weakening",
    NEW_CONNECTION = "new_connection",
    CONNECTION_PRUNING = "connection_pruning",
    PATHWAY_OPTIMIZATION = "pathway_optimization"
}
export declare enum SelectionStrategy {
    TOURNAMENT = "tournament",
    ROULETTE = "roulette",
    RANK_BASED = "rank_based",
    ELITIST = "elitist",
    DIVERSITY_PRESERVING = "diversity_preserving"
}
export declare enum Priority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export interface AlphaEvolveRequest {
    problem: string;
    domain: string;
    metrics: string[];
    constraints: any;
    config: EvolutionConfig;
}
export interface NeuroplasticityRequest {
    agentId: string;
    connectionId: string;
    adaptationType: AdaptationType;
    stimulus: any;
    context: any;
}
export interface GeneticMemoryRequest {
    operation: 'store' | 'retrieve' | 'evolve';
    geneType: GeneType;
    pattern?: string;
    context?: any;
}
//# sourceMappingURL=evolution.d.ts.map