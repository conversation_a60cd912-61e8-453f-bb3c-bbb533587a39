/**
 * Configuration globale pour les tests Jest
 * Agent Évolution AlphaEvolve
 */
declare global {
    namespace jest {
        interface Matchers<R> {
            toBeValidFitnessScore(): R;
            toBeValidEvolutionSolution(): R;
            toHaveImprovedFitness(original: any): R;
        }
    }
    var testConfig: {
        evolution: {
            populationSize: number;
            maxGenerations: number;
            convergenceThreshold: number;
            timeout: number;
        };
    };
    var createMockSolution: (overrides?: any) => any;
    var createMockRequest: (overrides?: any) => any;
}
export {};
//# sourceMappingURL=setup.d.ts.map