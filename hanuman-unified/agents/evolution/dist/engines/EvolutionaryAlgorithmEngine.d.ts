import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { EvolutionSolution, MutationType, SelectionStrategy } from '../types/evolution';
/**
 * Moteur d'Algorithmes Évolutionnaires
 *
 * Implémente les opérateurs génétiques classiques :
 * - Sélection (tournament, roulette, rank-based)
 * - Croisement (crossover)
 * - Mutation (simple, créative, optimisation)
 * - Élitisme et préservation de la diversité
 */
export declare class EvolutionaryAlgorithmEngine extends EventEmitter {
    private logger;
    private geneticPool;
    private mutationHistory;
    constructor(logger: Logger);
    /**
     * Initialise le moteur évolutionnaire
     */
    initialize(): Promise<void>;
    /**
     * Sélection des parents selon la stratégie choisie
     */
    selectParents(population: EvolutionSolution[], strategy: SelectionStrategy, count: number): EvolutionSolution[];
    /**
     * Sélection par tournoi
     */
    private tournamentSelection;
    /**
     * Sélection par roulette (proportionnelle au fitness)
     */
    private rouletteSelection;
    /**
     * Sélection basée sur le rang
     */
    private rankBasedSelection;
    /**
     * Sélection élitiste
     */
    private elitistSelection;
    /**
     * Sélection préservant la diversité
     */
    private diversityPreservingSelection;
    /**
     * Croisement de deux solutions parents
     */
    crossover(parent1: EvolutionSolution, parent2: EvolutionSolution): EvolutionSolution[];
    /**
     * Mutation d'une solution
     */
    mutate(solution: EvolutionSolution, type: MutationType, intensity?: number): EvolutionSolution;
    /**
     * Extraction des blocs de code fonctionnels
     */
    private extractCodeBlocks;
    /**
     * Combinaison de blocs de code
     */
    private combineCodeBlocks;
    /**
     * Mutations spécialisées
     */
    private simpleMutation;
    private optimizationMutation;
    private refactoringMutation;
    private creativeMutation;
    /**
     * Calcul du score de diversité
     */
    private calculateDiversityScore;
    /**
     * Calcul de la distance entre deux codes
     */
    private calculateCodeDistance;
    /**
     * Chargement du pool génétique
     */
    private loadGeneticPool;
    /**
     * Initialisation des gènes de base
     */
    private initializeBasicGenes;
}
//# sourceMappingURL=EvolutionaryAlgorithmEngine.d.ts.map