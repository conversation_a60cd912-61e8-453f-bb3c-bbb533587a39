import { EventEmitter } from 'events';
import { Logger } from '../utils/logger';
import { NeuralAdaptation, NeuroplasticityRequest } from '../types/evolution';
/**
 * Moteur de Neuroplasticité
 *
 * Implémente l'adaptation continue des connexions synaptiques entre agents,
 * inspiré de la neuroplasticité du cerveau humain :
 * - Renforcement synaptique (LTP - Long Term Potentiation)
 * - Affaiblissement synaptique (LTD - Long Term Depression)
 * - Formation de nouvelles connexions
 * - Élagage des connexions inutiles
 * - Optimisation des voies de communication
 */
export declare class NeuroplasticityEngine extends EventEmitter {
    private logger;
    private synapticConnections;
    private adaptationHistory;
    private learningRates;
    private plasticityThresholds;
    constructor(logger: Logger);
    /**
     * Initialise le moteur de neuroplasticité
     */
    initialize(): Promise<void>;
    /**
     * Traite une demande d'adaptation neuroplastique
     */
    processAdaptation(request: NeuroplasticityRequest): Promise<NeuralAdaptation>;
    /**
     * Renforce une connexion synaptique (LTP)
     */
    strengthenConnection(fromAgent: string, toAgent: string, stimulus: any): Promise<void>;
    /**
     * Affaiblit une connexion synaptique (LTD)
     */
    weakenConnection(fromAgent: string, toAgent: string, reason: string): Promise<void>;
    /**
     * Crée une nouvelle connexion synaptique
     */
    createConnection(fromAgent: string, toAgent: string, stimulus: any): Promise<void>;
    /**
     * Élague une connexion synaptique inutile
     */
    pruneConnection(connectionId: string): Promise<void>;
    /**
     * Optimise les voies de communication
     */
    optimizePathways(): Promise<void>;
    /**
     * Démarre le processus d'adaptation continue
     */
    private startContinuousAdaptation;
    /**
     * Adaptation périodique automatique
     */
    private performPeriodicAdaptation;
    /**
     * Nettoyage des connexions obsolètes
     */
    private cleanupObsoleteConnections;
    /**
     * Crée une adaptation neuroplastique
     */
    private createAdaptation;
    /**
     * Applique une adaptation
     */
    private applyAdaptation;
    /**
     * Calcule l'augmentation de force synaptique
     */
    private calculateStrengthIncrease;
    /**
     * Met à jour la latence moyenne
     */
    private updateAverageLatency;
    /**
     * Met à jour le taux d'apprentissage (métaplasticité)
     */
    private updateLearningRate;
    /**
     * Calcule la nouvelle force synaptique
     */
    private calculateNewStrength;
    /**
     * Analyse les patterns de communication
     */
    analyzeCommunicationPatterns(): CommunicationPattern[];
    /**
     * Identifie le type de pattern de communication
     */
    private identifyPattern;
    /**
     * Identifie les voies sous-optimales
     */
    identifySuboptimalPaths(): CommunicationPath[];
    /**
     * Identifie le type de goulot d'étranglement
     */
    private identifyBottleneckType;
    /**
     * Calcule le potentiel d'optimisation
     */
    private calculateOptimizationPotential;
    /**
     * Optimise automatiquement les voies de communication
     */
    optimizeCommunicationPaths(): Promise<OptimizationResult>;
    /**
     * Optimise une voie spécifique
     */
    private optimizePath;
    /**
     * Optimise la latence d'une connexion
     */
    private optimizeLatency;
    /**
     * Promeut une connexion sous-utilisée
     */
    private promoteConnection;
    /**
     * Calcule l'amélioration entre avant et après
     */
    private calculateImprovement;
    /**
     * Calcule l'amélioration moyenne
     */
    private calculateAverageImprovement;
    /**
     * Calcule le gain d'efficacité du réseau
     */
    private calculateNetworkEfficiencyGain;
    /**
     * Charge les connexions synaptiques existantes
     */
    private loadSynapticConnections;
    /**
     * Initialise les taux d'apprentissage
     */
    private initializeLearningRates;
    /**
     * Obtient les métriques de plasticité
     */
    getPlasticityMetrics(): PlasticityMetrics;
    /**
     * Obtient l'historique des adaptations
     */
    getAdaptationHistory(): NeuralAdaptation[];
    /**
     * Obtient l'ID de connexion
     */
    getConnectionId(fromAgent: string, toAgent: string): string;
}
interface CommunicationPattern {
    agents: string[];
    frequency: number;
    efficiency: number;
    averageLatency: number;
    connectionCount: number;
    pattern: string;
}
interface PlasticityMetrics {
    totalConnections: number;
    averageStrength: number;
    strongConnections: number;
    weakConnections: number;
    averageLatency: number;
    adaptationRate: number;
    networkEfficiency: number;
}
interface CommunicationPath {
    from: string;
    to: string;
    currentLatency: number;
    currentStrength: number;
    usageCount: number;
    bottleneckType: string;
    optimizationPotential: number;
}
interface OptimizationResult {
    timestamp: Date;
    pathsAnalyzed: number;
    pathsOptimized: number;
    optimizations: PathOptimization[];
    averageImprovement: number;
    networkEfficiencyGain: number;
}
interface PathOptimization {
    path: CommunicationPath;
    action: string;
    beforeMetrics: {
        latency: number;
        strength: number;
    };
    afterMetrics: {
        latency: number;
        strength: number;
    };
    improvement: number;
}
export {};
//# sourceMappingURL=NeuroplasticityEngine.d.ts.map