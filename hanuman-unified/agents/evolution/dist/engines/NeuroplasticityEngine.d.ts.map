{"version": 3, "file": "NeuroplasticityEngine.d.ts", "sourceRoot": "", "sources": ["../../src/engines/NeuroplasticityEngine.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EACL,gBAAgB,EAEhB,sBAAsB,EACvB,MAAM,oBAAoB,CAAC;AAE5B;;;;;;;;;;GAUG;AACH,qBAAa,qBAAsB,SAAQ,YAAY;IACrD,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,mBAAmB,CAA8C;IACzE,OAAO,CAAC,iBAAiB,CAA0B;IACnD,OAAO,CAAC,aAAa,CAAkC;IACvD,OAAO,CAAC,oBAAoB,CAAuB;gBAEvC,MAAM,EAAE,MAAM;IAW1B;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAejC;;OAEG;IACG,iBAAiB,CAAC,OAAO,EAAE,sBAAsB,GAAG,OAAO,CAAC,gBAAgB,CAAC;IAiBnF;;OAEG;IACG,oBAAoB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAuB5F;;OAEG;IACG,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAuBzF;;OAEG;IACG,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IA2BxF;;OAEG;IACG,eAAe,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAa1D;;OAEG;IACG,gBAAgB,IAAI,OAAO,CAAC,IAAI,CAAC;IAiBvC;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAYjC;;OAEG;YACW,yBAAyB;IAqBvC;;OAEG;YACW,0BAA0B;IAgBxC;;OAEG;YACW,gBAAgB;IAc9B;;OAEG;YACW,eAAe;IAqB7B;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAQjC;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAK5B;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAQ1B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAK5B;;OAEG;IACI,4BAA4B,IAAI,oBAAoB,EAAE;IAmC7D;;OAEG;IACH,OAAO,CAAC,eAAe;IAOvB;;OAEG;IACI,uBAAuB,IAAI,iBAAiB,EAAE;IA0BrD;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAO9B;;OAEG;IACH,OAAO,CAAC,8BAA8B;IAQtC;;OAEG;IACU,0BAA0B,IAAI,OAAO,CAAC,kBAAkB,CAAC;IA0BtE;;OAEG;YACW,YAAY;IAgD1B;;OAEG;YACW,eAAe;IAM7B;;OAEG;YACW,iBAAiB;IAK/B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAO5B;;OAEG;IACH,OAAO,CAAC,2BAA2B;IAOnC;;OAEG;YACW,8BAA8B;IAO5C;;OAEG;YACW,uBAAuB;IAIrC;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAS/B;;OAEG;IACI,oBAAoB,IAAI,iBAAiB;IAoChD;;OAEG;IACI,oBAAoB,IAAI,gBAAgB,EAAE;IAIjD;;OAEG;IACI,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM;CAGnE;AAuBD,UAAU,oBAAoB;IAC5B,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,cAAc,EAAE,MAAM,CAAC;IACvB,eAAe,EAAE,MAAM,CAAC;IACxB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,UAAU,iBAAiB;IACzB,gBAAgB,EAAE,MAAM,CAAC;IACzB,eAAe,EAAE,MAAM,CAAC;IACxB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,eAAe,EAAE,MAAM,CAAC;IACxB,cAAc,EAAE,MAAM,CAAC;IACvB,cAAc,EAAE,MAAM,CAAC;IACvB,iBAAiB,EAAE,MAAM,CAAC;CAC3B;AAED,UAAU,iBAAiB;IACzB,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,EAAE,MAAM,CAAC;IACX,cAAc,EAAE,MAAM,CAAC;IACvB,eAAe,EAAE,MAAM,CAAC;IACxB,UAAU,EAAE,MAAM,CAAC;IACnB,cAAc,EAAE,MAAM,CAAC;IACvB,qBAAqB,EAAE,MAAM,CAAC;CAC/B;AAED,UAAU,kBAAkB;IAC1B,SAAS,EAAE,IAAI,CAAC;IAChB,aAAa,EAAE,MAAM,CAAC;IACtB,cAAc,EAAE,MAAM,CAAC;IACvB,aAAa,EAAE,gBAAgB,EAAE,CAAC;IAClC,kBAAkB,EAAE,MAAM,CAAC;IAC3B,qBAAqB,EAAE,MAAM,CAAC;CAC/B;AAED,UAAU,gBAAgB;IACxB,IAAI,EAAE,iBAAiB,CAAC;IACxB,MAAM,EAAE,MAAM,CAAC;IACf,aAAa,EAAE;QACb,OAAO,EAAE,MAAM,CAAC;QAChB,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;IACF,YAAY,EAAE;QACZ,OAAO,EAAE,MAAM,CAAC;QAChB,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;IACF,WAAW,EAAE,MAAM,CAAC;CACrB"}