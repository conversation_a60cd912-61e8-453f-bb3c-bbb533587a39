import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { EvolutionSolution, MutationType, AlphaEvolveRequest } from '../types/evolution';
/**
 * Explorer Agent - Génération Rapide de Variantes (Breadth)
 *
 * Inspiré du framework AlphaEvolve, cet agent se spécialise dans :
 * - Exploration large de l'espace des solutions
 * - Génération rapide de nombreuses variantes
 * - Diversification créative des approches
 * - Mutations innovantes et hybridations
 */
export declare class ExplorerAgent extends EventEmitter {
    private logger;
    private generationCount;
    private explorationPatterns;
    constructor(logger: Logger);
    /**
     * Génère des variantes algorithmiques pour la population initiale
     */
    generateVariants(request: VariantGenerationRequest): Promise<EvolutionSolution[]>;
    /**
     * Génère une mutation créative d'une solution parent
     */
    generateMutation(parent: EvolutionSolution, type: MutationType, request: AlphaEvolveRequest): Promise<EvolutionSolution>;
    /**
     * Sélectionne les approches d'exploration appropriées
     */
    private selectExplorationApproaches;
    /**
     * Génère une variante de solution selon une approche donnée
     */
    private generateSolutionVariant;
    /**
     * Construit un prompt optimisé pour l'exploration
     */
    private buildExplorationPrompt;
    /**
     * Génère du code via LLM (simulation pour l'instant)
     */
    private generateCodeWithLLM;
    /**
     * Applique une mutation à un code existant
     */
    private applyMutation;
    /**
     * Mutations structurelles
     */
    private applyStructuralMutation;
    /**
     * Mutations algorithmiques
     */
    private applyAlgorithmicMutation;
    /**
     * Mutations créatives
     */
    private applyCreativeMutation;
    /**
     * Mutations d'optimisation
     */
    private applyOptimizationMutation;
    /**
     * Mutation aléatoire
     */
    private applyRandomMutation;
    /**
     * Estime la fitness initiale d'une solution
     */
    private estimateInitialFitness;
    /**
     * Estime les métriques de performance
     */
    private estimatePerformance;
    /**
     * Méthodes d'estimation de complexité
     */
    private estimateComplexity;
    private estimateReadability;
    private estimateTimeComplexity;
    private estimateSpaceComplexity;
    private estimateCyclomaticComplexity;
    private estimateCognitiveComplexity;
    private calculateNestingLevel;
    /**
     * Initialise les patterns d'exploration
     */
    private initializeExplorationPatterns;
    /**
     * Méthodes utilitaires pour la génération
     */
    private getApproachesForDomain;
    private getApproachesForComplexity;
    private getCreativeApproaches;
    private selectMutationStrategy;
    private getCodeTemplates;
    private adaptTemplate;
    private injectCreativePattern;
}
interface VariantGenerationRequest {
    problem: string;
    domain: string;
    count: number;
    constraints: any;
}
export {};
//# sourceMappingURL=ExplorerAgent.d.ts.map