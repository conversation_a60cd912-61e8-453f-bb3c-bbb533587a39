/**
 * Types pour l'Agent Data Analyst
 */
export interface AgentConfig {
    port: number;
    kafka: {
        brokers: string[];
        clientId: string;
        groupId: string;
    };
    weaviate: {
        scheme: string;
        host: string;
        port: number;
    };
    redis: {
        host: string;
        port: number;
        password?: string;
    };
}
export interface AnalysisRequest {
    id: string;
    type: AnalysisType;
    data: DataSource[];
    parameters: AnalysisParameters;
    metadata?: Record<string, any>;
}
export type AnalysisType = 'descriptive' | 'predictive' | 'prescriptive' | 'diagnostic' | 'comparative' | 'trend' | 'correlation' | 'segmentation' | 'forecasting' | 'anomaly_detection';
export interface DataSource {
    id: string;
    name: string;
    type: 'csv' | 'json' | 'database' | 'api' | 'stream';
    source: string;
    schema?: DataSchema;
    filters?: DataFilter[];
    transformations?: DataTransformation[];
}
export interface DataSchema {
    fields: DataField[];
    primaryKey?: string;
    relationships?: DataRelationship[];
}
export interface DataField {
    name: string;
    type: 'string' | 'number' | 'date' | 'boolean' | 'array' | 'object';
    nullable: boolean;
    description?: string;
    constraints?: FieldConstraints;
}
export interface FieldConstraints {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
    unique?: boolean;
}
export interface DataRelationship {
    type: 'one-to-one' | 'one-to-many' | 'many-to-many';
    targetTable: string;
    foreignKey: string;
    targetKey: string;
}
export interface DataFilter {
    field: string;
    operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'regex';
    value: any;
}
export interface DataTransformation {
    type: 'aggregate' | 'join' | 'pivot' | 'normalize' | 'clean' | 'derive';
    parameters: Record<string, any>;
}
export interface AnalysisParameters {
    timeRange?: TimeRange;
    granularity?: 'minute' | 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year';
    metrics?: string[];
    dimensions?: string[];
    filters?: AnalysisFilter[];
    options?: AnalysisOptions;
}
export interface TimeRange {
    start: Date;
    end: Date;
    timezone?: string;
}
export interface AnalysisFilter {
    dimension: string;
    operator: string;
    value: any;
}
export interface AnalysisOptions {
    confidence?: number;
    significance?: number;
    sampleSize?: number;
    algorithm?: string;
    parameters?: Record<string, any>;
}
export interface AnalysisResult {
    id: string;
    requestId: string;
    type: AnalysisType;
    status: 'completed' | 'failed' | 'partial';
    results: AnalysisData;
    insights: Insight[];
    recommendations: Recommendation[];
    visualizations: Visualization[];
    metadata: AnalysisMetadata;
}
export interface AnalysisData {
    summary: DataSummary;
    metrics: Metric[];
    trends: TrendAnalysis[];
    correlations: Correlation[];
    segments: Segment[];
    predictions: Prediction[];
    anomalies: Anomaly[];
}
export interface DataSummary {
    totalRecords: number;
    timeRange: TimeRange;
    dataQuality: DataQuality;
    keyMetrics: KeyMetric[];
}
export interface DataQuality {
    completeness: number;
    accuracy: number;
    consistency: number;
    timeliness: number;
    validity: number;
    issues: DataIssue[];
}
export interface DataIssue {
    type: 'missing' | 'duplicate' | 'outlier' | 'inconsistent' | 'invalid';
    field: string;
    count: number;
    percentage: number;
    examples?: any[];
}
export interface KeyMetric {
    name: string;
    value: number;
    unit?: string;
    change?: number;
    changeType?: 'absolute' | 'percentage';
    trend?: 'up' | 'down' | 'stable';
    significance?: 'high' | 'medium' | 'low';
}
export interface Metric {
    name: string;
    value: number;
    unit?: string;
    description?: string;
    category?: string;
    tags?: string[];
    timestamp?: Date;
    confidence?: number;
}
export interface TrendAnalysis {
    metric: string;
    direction: 'increasing' | 'decreasing' | 'stable' | 'volatile';
    strength: number;
    seasonality?: SeasonalityPattern;
    changePoints: ChangePoint[];
    forecast?: ForecastData;
}
export interface SeasonalityPattern {
    type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    strength: number;
    peaks: Date[];
    troughs: Date[];
}
export interface ChangePoint {
    date: Date;
    type: 'level' | 'trend' | 'variance';
    magnitude: number;
    confidence: number;
    description?: string;
}
export interface ForecastData {
    values: ForecastPoint[];
    confidence: number;
    method: string;
    accuracy: ForecastAccuracy;
}
export interface ForecastPoint {
    date: Date;
    value: number;
    lower: number;
    upper: number;
    confidence: number;
}
export interface ForecastAccuracy {
    mae: number;
    mape: number;
    rmse: number;
    r2: number;
}
export interface Correlation {
    variable1: string;
    variable2: string;
    coefficient: number;
    type: 'pearson' | 'spearman' | 'kendall';
    significance: number;
    strength: 'weak' | 'moderate' | 'strong';
    direction: 'positive' | 'negative';
}
export interface Segment {
    id: string;
    name: string;
    description: string;
    size: number;
    percentage: number;
    characteristics: SegmentCharacteristic[];
    metrics: Metric[];
    trends: TrendAnalysis[];
}
export interface SegmentCharacteristic {
    dimension: string;
    value: any;
    importance: number;
    description?: string;
}
export interface Prediction {
    target: string;
    value: number;
    confidence: number;
    probability?: number;
    factors: PredictionFactor[];
    method: string;
    accuracy: ModelAccuracy;
}
export interface PredictionFactor {
    feature: string;
    importance: number;
    impact: 'positive' | 'negative';
    value?: any;
}
export interface ModelAccuracy {
    precision: number;
    recall: number;
    f1Score: number;
    accuracy: number;
    auc?: number;
}
export interface Anomaly {
    id: string;
    timestamp: Date;
    metric: string;
    value: number;
    expectedValue: number;
    deviation: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    type: 'point' | 'contextual' | 'collective';
    description: string;
    possibleCauses: string[];
}
export interface Insight {
    id: string;
    type: 'trend' | 'pattern' | 'anomaly' | 'correlation' | 'opportunity' | 'risk';
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    confidence: number;
    evidence: Evidence[];
    relatedMetrics: string[];
    tags?: string[];
}
export interface Evidence {
    type: 'statistical' | 'visual' | 'comparative' | 'temporal';
    description: string;
    value?: any;
    source?: string;
}
export interface Recommendation {
    id: string;
    type: 'action' | 'investigation' | 'monitoring' | 'optimization';
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    effort: 'high' | 'medium' | 'low';
    impact: 'high' | 'medium' | 'low';
    timeframe: string;
    steps: ActionStep[];
    expectedOutcome: string;
    metrics: string[];
    risks?: string[];
}
export interface ActionStep {
    order: number;
    description: string;
    owner?: string;
    duration?: string;
    dependencies?: string[];
}
export interface Visualization {
    id: string;
    type: 'chart' | 'table' | 'map' | 'dashboard' | 'report';
    title: string;
    description?: string;
    chartType?: ChartType;
    data: VisualizationData;
    config: VisualizationConfig;
    exportFormats: ExportFormat[];
}
export type ChartType = 'line' | 'bar' | 'pie' | 'scatter' | 'heatmap' | 'histogram' | 'box' | 'area' | 'bubble' | 'treemap' | 'sankey' | 'funnel';
export interface VisualizationData {
    labels: string[];
    datasets: Dataset[];
    metadata?: Record<string, any>;
}
export interface Dataset {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    fill?: boolean;
    tension?: number;
}
export interface VisualizationConfig {
    responsive: boolean;
    maintainAspectRatio: boolean;
    plugins?: Record<string, any>;
    scales?: Record<string, any>;
    interaction?: Record<string, any>;
    animation?: Record<string, any>;
}
export type ExportFormat = 'png' | 'jpg' | 'pdf' | 'svg' | 'csv' | 'xlsx' | 'json';
export interface AnalysisMetadata {
    timestamp: Date;
    duration: number;
    dataSize: number;
    algorithm: string;
    version: string;
    parameters: Record<string, any>;
    quality: AnalysisQuality;
}
export interface AnalysisQuality {
    dataCompleteness: number;
    modelAccuracy: number;
    confidence: number;
    reliability: number;
    warnings: string[];
}
export interface KPIDefinition {
    id: string;
    name: string;
    description: string;
    formula: string;
    unit: string;
    category: string;
    target?: KPITarget;
    thresholds: KPIThreshold[];
    frequency: 'real-time' | 'hourly' | 'daily' | 'weekly' | 'monthly';
    dataSources: string[];
    dependencies: string[];
}
export interface KPITarget {
    value: number;
    type: 'minimum' | 'maximum' | 'exact' | 'range';
    timeframe: string;
}
export interface KPIThreshold {
    level: 'critical' | 'warning' | 'good' | 'excellent';
    operator: 'lt' | 'lte' | 'gt' | 'gte' | 'eq' | 'between';
    value: number | [number, number];
    color?: string;
}
export interface Dashboard {
    id: string;
    name: string;
    description?: string;
    widgets: DashboardWidget[];
    layout: DashboardLayout;
    filters: DashboardFilter[];
    refreshInterval?: number;
    permissions: DashboardPermission[];
}
export interface DashboardWidget {
    id: string;
    type: 'metric' | 'chart' | 'table' | 'text' | 'image';
    title: string;
    position: WidgetPosition;
    size: WidgetSize;
    config: WidgetConfig;
    dataSources: string[];
    refreshInterval?: number;
}
export interface WidgetPosition {
    x: number;
    y: number;
}
export interface WidgetSize {
    width: number;
    height: number;
}
export interface WidgetConfig {
    visualization?: Visualization;
    kpi?: string;
    query?: string;
    parameters?: Record<string, any>;
    styling?: Record<string, any>;
}
export interface DashboardLayout {
    type: 'grid' | 'flex' | 'absolute';
    columns?: number;
    gap?: number;
    padding?: number;
}
export interface DashboardFilter {
    id: string;
    name: string;
    type: 'select' | 'multiselect' | 'date' | 'daterange' | 'text' | 'number';
    field: string;
    options?: FilterOption[];
    defaultValue?: any;
}
export interface FilterOption {
    label: string;
    value: any;
}
export interface DashboardPermission {
    userId: string;
    role: 'viewer' | 'editor' | 'admin';
    restrictions?: string[];
}
export interface Report {
    id: string;
    name: string;
    description?: string;
    type: 'scheduled' | 'adhoc' | 'automated';
    template: ReportTemplate;
    schedule?: ReportSchedule;
    recipients: ReportRecipient[];
    format: ExportFormat[];
    parameters: Record<string, any>;
}
export interface ReportTemplate {
    sections: ReportSection[];
    styling: ReportStyling;
    header?: ReportHeader;
    footer?: ReportFooter;
}
export interface ReportSection {
    id: string;
    type: 'text' | 'chart' | 'table' | 'metrics' | 'insights';
    title?: string;
    content: any;
    config?: Record<string, any>;
}
export interface ReportStyling {
    theme: string;
    colors: string[];
    fonts: ReportFonts;
    layout: ReportLayout;
}
export interface ReportFonts {
    title: string;
    heading: string;
    body: string;
    caption: string;
}
export interface ReportLayout {
    pageSize: 'A4' | 'A3' | 'letter' | 'legal';
    orientation: 'portrait' | 'landscape';
    margins: ReportMargins;
}
export interface ReportMargins {
    top: number;
    right: number;
    bottom: number;
    left: number;
}
export interface ReportHeader {
    logo?: string;
    title: string;
    subtitle?: string;
    date: boolean;
    pageNumbers: boolean;
}
export interface ReportFooter {
    text?: string;
    pageNumbers: boolean;
    timestamp: boolean;
}
export interface ReportSchedule {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
    time: string;
    timezone: string;
    startDate: Date;
    endDate?: Date;
}
export interface ReportRecipient {
    email: string;
    name?: string;
    role?: string;
}
export interface DataAlert {
    id: string;
    name: string;
    description?: string;
    condition: AlertCondition;
    actions: AlertAction[];
    schedule: AlertSchedule;
    enabled: boolean;
    lastTriggered?: Date;
    triggerCount: number;
}
export interface AlertCondition {
    metric: string;
    operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'ne' | 'change_gt' | 'change_lt';
    value: number;
    timeWindow?: string;
    aggregation?: 'sum' | 'avg' | 'min' | 'max' | 'count';
}
export interface AlertAction {
    type: 'email' | 'webhook' | 'slack' | 'sms';
    config: Record<string, any>;
    template?: string;
}
export interface AlertSchedule {
    frequency: 'real-time' | 'hourly' | 'daily';
    quietHours?: QuietHours;
    maxFrequency?: string;
}
export interface QuietHours {
    start: string;
    end: string;
    timezone: string;
    days?: string[];
}
//# sourceMappingURL=index.d.ts.map