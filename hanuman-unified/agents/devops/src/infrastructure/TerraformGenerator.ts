import { Logger } from 'winston';
import { InfrastructureRequest, TerraformConfig } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Générateur Terraform
 * 
 * Génère des configurations Terraform pour l'infrastructure as code
 */
export class TerraformGenerator {
  private logger: Logger;
  private memory: WeaviateMemory;
  private templatesPath: string;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    this.templatesPath = path.join(__dirname, '../templates/terraform');
  }

  /**
   * Génère une configuration Terraform complète
   */
  async generateInfrastructure(request: InfrastructureRequest): Promise<TerraformConfig> {
    this.logger.info('Génération de l\'infrastructure Terraform', { 
      provider: request.provider,
      environment: request.environment 
    });

    const config: TerraformConfig = {
      provider: request.provider,
      version: '1.0',
      modules: [],
      variables: {},
      outputs: {},
      resources: []
    };

    try {
      // 1. Configuration du provider
      await this.generateProviderConfig(config, request);

      // 2. Variables Terraform
      await this.generateVariables(config, request);

      // 3. Ressources principales
      await this.generateMainResources(config, request);

      // 4. Modules réutilisables
      await this.generateModules(config, request);

      // 5. Outputs
      await this.generateOutputs(config, request);

      // 6. Écrire les fichiers Terraform
      await this.writeTerraformFiles(config, request);

      this.logger.info('Configuration Terraform générée avec succès', { 
        provider: request.provider,
        modules: config.modules.length,
        resources: config.resources.length 
      });

      return config;

    } catch (error) {
      this.logger.error('Erreur lors de la génération Terraform', { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Génère la configuration du provider
   */
  private async generateProviderConfig(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    switch (request.provider) {
      case 'aws':
        config.providerConfig = {
          terraform: {
            required_version: '>= 1.0',
            required_providers: {
              aws: {
                source: 'hashicorp/aws',
                version: '~> 5.0'
              }
            }
          },
          provider: {
            aws: {
              region: request.region || 'us-east-1',
              default_tags: {
                tags: {
                  Environment: request.environment,
                  Project: request.projectName,
                  ManagedBy: 'agent-devops'
                }
              }
            }
          }
        };
        break;

      case 'gcp':
        config.providerConfig = {
          terraform: {
            required_version: '>= 1.0',
            required_providers: {
              google: {
                source: 'hashicorp/google',
                version: '~> 4.0'
              }
            }
          },
          provider: {
            google: {
              project: request.projectId,
              region: request.region || 'us-central1',
              zone: request.zone || 'us-central1-a'
            }
          }
        };
        break;

      case 'azure':
        config.providerConfig = {
          terraform: {
            required_version: '>= 1.0',
            required_providers: {
              azurerm: {
                source: 'hashicorp/azurerm',
                version: '~> 3.0'
              }
            }
          },
          provider: {
            azurerm: {
              features: {}
            }
          }
        };
        break;
    }
  }

  /**
   * Génère les variables Terraform
   */
  private async generateVariables(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    config.variables = {
      environment: {
        description: 'Environment name',
        type: 'string',
        default: request.environment
      },
      project_name: {
        description: 'Project name',
        type: 'string',
        default: request.projectName
      },
      region: {
        description: 'Cloud provider region',
        type: 'string',
        default: request.region
      }
    };

    // Variables spécifiques aux composants
    if (request.components.includes('kubernetes')) {
      config.variables.k8s_version = {
        description: 'Kubernetes version',
        type: 'string',
        default: '1.28'
      };
      config.variables.node_count = {
        description: 'Number of worker nodes',
        type: 'number',
        default: 3
      };
    }

    if (request.components.includes('database')) {
      config.variables.db_instance_class = {
        description: 'Database instance class',
        type: 'string',
        default: 'db.t3.micro'
      };
    }
  }

  /**
   * Génère les ressources principales
   */
  private async generateMainResources(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    // Réseau de base
    if (request.components.includes('networking')) {
      await this.generateNetworkingResources(config, request);
    }

    // Cluster Kubernetes
    if (request.components.includes('kubernetes')) {
      await this.generateKubernetesResources(config, request);
    }

    // Base de données
    if (request.components.includes('database')) {
      await this.generateDatabaseResources(config, request);
    }

    // Stockage
    if (request.components.includes('storage')) {
      await this.generateStorageResources(config, request);
    }

    // Monitoring
    if (request.components.includes('monitoring')) {
      await this.generateMonitoringResources(config, request);
    }
  }

  /**
   * Génère les ressources réseau
   */
  private async generateNetworkingResources(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    switch (request.provider) {
      case 'aws':
        config.resources.push(
          {
            type: 'aws_vpc',
            name: 'main',
            config: {
              cidr_block: '10.0.0.0/16',
              enable_dns_hostnames: true,
              enable_dns_support: true,
              tags: {
                Name: '${var.project_name}-vpc'
              }
            }
          },
          {
            type: 'aws_subnet',
            name: 'public',
            config: {
              count: 2,
              vpc_id: '${aws_vpc.main.id}',
              cidr_block: '10.0.${count.index + 1}.0/24',
              availability_zone: '${data.aws_availability_zones.available.names[count.index]}',
              map_public_ip_on_launch: true,
              tags: {
                Name: '${var.project_name}-public-${count.index + 1}'
              }
            }
          },
          {
            type: 'aws_internet_gateway',
            name: 'main',
            config: {
              vpc_id: '${aws_vpc.main.id}',
              tags: {
                Name: '${var.project_name}-igw'
              }
            }
          }
        );
        break;

      case 'gcp':
        config.resources.push(
          {
            type: 'google_compute_network',
            name: 'main',
            config: {
              name: '${var.project_name}-network',
              auto_create_subnetworks: false
            }
          },
          {
            type: 'google_compute_subnetwork',
            name: 'main',
            config: {
              name: '${var.project_name}-subnet',
              ip_cidr_range: '10.0.0.0/16',
              region: '${var.region}',
              network: '${google_compute_network.main.id}'
            }
          }
        );
        break;
    }
  }

  /**
   * Génère les ressources Kubernetes
   */
  private async generateKubernetesResources(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    switch (request.provider) {
      case 'aws':
        config.resources.push(
          {
            type: 'aws_eks_cluster',
            name: 'main',
            config: {
              name: '${var.project_name}-cluster',
              role_arn: '${aws_iam_role.eks_cluster.arn}',
              version: '${var.k8s_version}',
              vpc_config: {
                subnet_ids: '${aws_subnet.public[*].id}'
              }
            }
          },
          {
            type: 'aws_eks_node_group',
            name: 'main',
            config: {
              cluster_name: '${aws_eks_cluster.main.name}',
              node_group_name: '${var.project_name}-nodes',
              node_role_arn: '${aws_iam_role.eks_node_group.arn}',
              subnet_ids: '${aws_subnet.public[*].id}',
              scaling_config: {
                desired_size: '${var.node_count}',
                max_size: '${var.node_count + 2}',
                min_size: 1
              },
              instance_types: ['t3.medium']
            }
          }
        );
        break;

      case 'gcp':
        config.resources.push(
          {
            type: 'google_container_cluster',
            name: 'main',
            config: {
              name: '${var.project_name}-cluster',
              location: '${var.region}',
              remove_default_node_pool: true,
              initial_node_count: 1,
              network: '${google_compute_network.main.name}',
              subnetwork: '${google_compute_subnetwork.main.name}'
            }
          },
          {
            type: 'google_container_node_pool',
            name: 'main',
            config: {
              name: '${var.project_name}-nodes',
              location: '${var.region}',
              cluster: '${google_container_cluster.main.name}',
              node_count: '${var.node_count}',
              node_config: {
                preemptible: true,
                machine_type: 'e2-medium'
              }
            }
          }
        );
        break;
    }
  }

  /**
   * Génère les ressources de base de données
   */
  private async generateDatabaseResources(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    switch (request.provider) {
      case 'aws':
        config.resources.push(
          {
            type: 'aws_db_instance',
            name: 'main',
            config: {
              identifier: '${var.project_name}-db',
              engine: 'postgres',
              engine_version: '15.4',
              instance_class: '${var.db_instance_class}',
              allocated_storage: 20,
              storage_encrypted: true,
              db_name: '${replace(var.project_name, "-", "_")}',
              username: 'admin',
              password: '${random_password.db_password.result}',
              vpc_security_group_ids: ['${aws_security_group.db.id}'],
              skip_final_snapshot: true
            }
          },
          {
            type: 'random_password',
            name: 'db_password',
            config: {
              length: 16,
              special: true
            }
          }
        );
        break;

      case 'gcp':
        config.resources.push(
          {
            type: 'google_sql_database_instance',
            name: 'main',
            config: {
              name: '${var.project_name}-db',
              database_version: 'POSTGRES_15',
              region: '${var.region}',
              settings: {
                tier: 'db-f1-micro',
                disk_size: 20,
                disk_type: 'PD_SSD'
              }
            }
          }
        );
        break;
    }
  }

  /**
   * Génère les ressources de stockage
   */
  private async generateStorageResources(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    switch (request.provider) {
      case 'aws':
        config.resources.push(
          {
            type: 'aws_s3_bucket',
            name: 'main',
            config: {
              bucket: '${var.project_name}-${var.environment}-storage'
            }
          },
          {
            type: 'aws_s3_bucket_versioning',
            name: 'main',
            config: {
              bucket: '${aws_s3_bucket.main.id}',
              versioning_configuration: {
                status: 'Enabled'
              }
            }
          }
        );
        break;

      case 'gcp':
        config.resources.push(
          {
            type: 'google_storage_bucket',
            name: 'main',
            config: {
              name: '${var.project_name}-${var.environment}-storage',
              location: '${var.region}',
              versioning: {
                enabled: true
              }
            }
          }
        );
        break;
    }
  }

  /**
   * Génère les ressources de monitoring
   */
  private async generateMonitoringResources(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    // Ajouter des ressources de monitoring spécifiques au provider
    config.resources.push(
      {
        type: 'helm_release',
        name: 'prometheus',
        config: {
          name: 'prometheus',
          repository: 'https://prometheus-community.github.io/helm-charts',
          chart: 'kube-prometheus-stack',
          namespace: 'monitoring',
          create_namespace: true,
          values: [
            '${file("${path.module}/values/prometheus.yaml")}'
          ]
        }
      }
    );
  }

  /**
   * Génère les modules réutilisables
   */
  private async generateModules(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    // Module de sécurité
    config.modules.push({
      name: 'security',
      source: './modules/security',
      variables: {
        project_name: '${var.project_name}',
        environment: '${var.environment}'
      }
    });

    // Module de monitoring
    if (request.components.includes('monitoring')) {
      config.modules.push({
        name: 'monitoring',
        source: './modules/monitoring',
        variables: {
          cluster_name: '${aws_eks_cluster.main.name}',
          environment: '${var.environment}'
        }
      });
    }
  }

  /**
   * Génère les outputs
   */
  private async generateOutputs(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    config.outputs = {
      vpc_id: {
        description: 'VPC ID',
        value: request.provider === 'aws' ? '${aws_vpc.main.id}' : '${google_compute_network.main.id}'
      },
      environment: {
        description: 'Environment name',
        value: '${var.environment}'
      }
    };

    if (request.components.includes('kubernetes')) {
      config.outputs.cluster_endpoint = {
        description: 'Kubernetes cluster endpoint',
        value: request.provider === 'aws' 
          ? '${aws_eks_cluster.main.endpoint}'
          : '${google_container_cluster.main.endpoint}'
      };
    }

    if (request.components.includes('database')) {
      config.outputs.database_endpoint = {
        description: 'Database endpoint',
        value: request.provider === 'aws'
          ? '${aws_db_instance.main.endpoint}'
          : '${google_sql_database_instance.main.connection_name}',
        sensitive: true
      };
    }
  }

  /**
   * Écrit les fichiers Terraform
   */
  private async writeTerraformFiles(config: TerraformConfig, request: InfrastructureRequest): Promise<void> {
    const outputDir = path.join(process.cwd(), 'terraform', request.projectName);
    await fs.ensureDir(outputDir);

    // main.tf
    const mainTf = this.generateMainTf(config);
    await fs.writeFile(path.join(outputDir, 'main.tf'), mainTf);

    // variables.tf
    const variablesTf = this.generateVariablesTf(config);
    await fs.writeFile(path.join(outputDir, 'variables.tf'), variablesTf);

    // outputs.tf
    const outputsTf = this.generateOutputsTf(config);
    await fs.writeFile(path.join(outputDir, 'outputs.tf'), outputsTf);

    // terraform.tfvars.example
    const tfvarsExample = this.generateTfvarsExample(config);
    await fs.writeFile(path.join(outputDir, 'terraform.tfvars.example'), tfvarsExample);

    this.logger.info('Fichiers Terraform écrits', { outputDir });
  }

  // Méthodes de génération de fichiers

  private generateMainTf(config: TerraformConfig): string {
    let content = '';

    // Provider configuration
    if (config.providerConfig) {
      content += this.objectToHcl(config.providerConfig) + '\n\n';
    }

    // Data sources
    content += 'data "aws_availability_zones" "available" {\n  state = "available"\n}\n\n';

    // Resources
    for (const resource of config.resources) {
      content += `resource "${resource.type}" "${resource.name}" {\n`;
      content += this.objectToHcl(resource.config, 1);
      content += '}\n\n';
    }

    // Modules
    for (const module of config.modules) {
      content += `module "${module.name}" {\n`;
      content += `  source = "${module.source}"\n`;
      content += this.objectToHcl(module.variables, 1);
      content += '}\n\n';
    }

    return content;
  }

  private generateVariablesTf(config: TerraformConfig): string {
    let content = '';

    for (const [name, variable] of Object.entries(config.variables)) {
      content += `variable "${name}" {\n`;
      content += this.objectToHcl(variable, 1);
      content += '}\n\n';
    }

    return content;
  }

  private generateOutputsTf(config: TerraformConfig): string {
    let content = '';

    for (const [name, output] of Object.entries(config.outputs)) {
      content += `output "${name}" {\n`;
      content += this.objectToHcl(output, 1);
      content += '}\n\n';
    }

    return content;
  }

  private generateTfvarsExample(config: TerraformConfig): string {
    let content = '# Example terraform.tfvars file\n\n';

    for (const [name, variable] of Object.entries(config.variables)) {
      content += `# ${variable.description}\n`;
      content += `${name} = ${JSON.stringify(variable.default)}\n\n`;
    }

    return content;
  }

  private objectToHcl(obj: any, indent: number = 0): string {
    const spaces = '  '.repeat(indent);
    let result = '';

    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        result += `${spaces}${key} {\n`;
        result += this.objectToHcl(value, indent + 1);
        result += `${spaces}}\n`;
      } else if (Array.isArray(value)) {
        result += `${spaces}${key} = ${JSON.stringify(value)}\n`;
      } else if (typeof value === 'string' && value.startsWith('${')) {
        result += `${spaces}${key} = ${value}\n`;
      } else {
        result += `${spaces}${key} = ${JSON.stringify(value)}\n`;
      }
    }

    return result;
  }
}
