import { Logger } from 'winston';
import { DeploymentMetrics, SystemMetrics, ApplicationMetrics } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { register, collectDefaultMetrics, Gauge, Counter, Histogram } from 'prom-client';
import axios from 'axios';

/**
 * Collecteur de Métriques
 * 
 * Collecte et agrège les métriques de déploiement, système et application
 */
export class MetricsCollector {
  private logger: Logger;
  private memory: WeaviateMemory;
  private metricsRegistry: typeof register;

  // Métriques Prometheus
  private deploymentGauge: Gauge<string>;
  private requestCounter: Counter<string>;
  private responseTimeHistogram: Histogram<string>;
  private errorCounter: Counter<string>;
  private resourceGauge: Gauge<string>;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
    this.metricsRegistry = register;

    // Initialiser les métriques par défaut
    collectDefaultMetrics({ register: this.metricsRegistry });

    // Initialiser les métriques personnalisées
    this.initializeCustomMetrics();

    // Démarrer la collecte périodique
    this.startPeriodicCollection();
  }

  /**
   * Initialise les métriques personnalisées
   */
  private initializeCustomMetrics(): void {
    this.deploymentGauge = new Gauge({
      name: 'devops_deployments_total',
      help: 'Total number of deployments',
      labelNames: ['status', 'environment', 'application']
    });

    this.requestCounter = new Counter({
      name: 'devops_requests_total',
      help: 'Total number of requests',
      labelNames: ['method', 'endpoint', 'status']
    });

    this.responseTimeHistogram = new Histogram({
      name: 'devops_response_time_seconds',
      help: 'Response time in seconds',
      labelNames: ['method', 'endpoint'],
      buckets: [0.1, 0.5, 1, 2, 5, 10]
    });

    this.errorCounter = new Counter({
      name: 'devops_errors_total',
      help: 'Total number of errors',
      labelNames: ['type', 'application', 'environment']
    });

    this.resourceGauge = new Gauge({
      name: 'devops_resource_usage',
      help: 'Resource usage metrics',
      labelNames: ['resource_type', 'application', 'environment']
    });
  }

  /**
   * Collecte les métriques de déploiement
   */
  async collectDeploymentMetrics(
    deploymentId: string,
    applicationName: string,
    environment: string
  ): Promise<DeploymentMetrics> {
    this.logger.info('Collecte des métriques de déploiement', { 
      deploymentId,
      applicationName,
      environment 
    });

    try {
      // Métriques de base
      const baseMetrics = await this.collectBaseMetrics(applicationName, environment);

      // Métriques Kubernetes si applicable
      const k8sMetrics = await this.collectKubernetesMetrics(applicationName, environment);

      // Métriques Docker si applicable
      const dockerMetrics = await this.collectDockerMetrics(applicationName, environment);

      // Métriques d'application
      const appMetrics = await this.collectApplicationMetrics(applicationName, environment);

      const deploymentMetrics: DeploymentMetrics = {
        deploymentId,
        applicationName,
        environment,
        timestamp: new Date(),
        system: baseMetrics,
        kubernetes: k8sMetrics,
        docker: dockerMetrics,
        application: appMetrics,
        health: await this.assessHealth(baseMetrics, appMetrics)
      };

      // Mettre à jour les métriques Prometheus
      this.updatePrometheusMetrics(deploymentMetrics);

      // Stocker dans la mémoire
      await this.storeMetrics(deploymentMetrics);

      return deploymentMetrics;

    } catch (error) {
      this.logger.error('Erreur lors de la collecte des métriques', { 
        deploymentId,
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Collecte les métriques système de base
   */
  private async collectBaseMetrics(applicationName: string, environment: string): Promise<SystemMetrics> {
    const metrics: SystemMetrics = {
      cpu: {
        current: 0,
        average: 0,
        peak: 0,
        unit: 'cores'
      },
      memory: {
        current: 0,
        average: 0,
        peak: 0,
        unit: 'MB'
      },
      network: {
        bytesIn: 0,
        bytesOut: 0,
        connectionsActive: 0,
        connectionsTotal: 0
      },
      disk: {
        used: 0,
        available: 0,
        total: 0,
        unit: 'GB'
      }
    };

    try {
      // Utiliser les métriques système Node.js
      const memUsage = process.memoryUsage();
      metrics.memory.current = Math.round(memUsage.heapUsed / 1024 / 1024);
      metrics.memory.peak = Math.round(memUsage.heapTotal / 1024 / 1024);

      // CPU usage (simulation - en production, utiliser des outils comme pidusage)
      metrics.cpu.current = Math.random() * 2; // 0-2 cores
      metrics.cpu.average = metrics.cpu.current * 0.8;
      metrics.cpu.peak = metrics.cpu.current * 1.2;

      // Métriques réseau (simulation)
      metrics.network.bytesIn = Math.floor(Math.random() * 1000000);
      metrics.network.bytesOut = Math.floor(Math.random() * 1000000);
      metrics.network.connectionsActive = Math.floor(Math.random() * 100);

      // Métriques disque (simulation)
      metrics.disk.used = Math.floor(Math.random() * 50);
      metrics.disk.available = 100 - metrics.disk.used;
      metrics.disk.total = 100;

    } catch (error) {
      this.logger.warn('Erreur lors de la collecte des métriques système', { 
        error: error.message 
      });
    }

    return metrics;
  }

  /**
   * Collecte les métriques Kubernetes
   */
  private async collectKubernetesMetrics(applicationName: string, environment: string): Promise<any> {
    try {
      // Simuler des métriques Kubernetes
      // En production, utiliser l'API Kubernetes ou Prometheus
      return {
        pods: {
          running: Math.floor(Math.random() * 5) + 1,
          pending: 0,
          failed: 0,
          total: Math.floor(Math.random() * 5) + 1
        },
        services: {
          active: 1,
          endpoints: Math.floor(Math.random() * 3) + 1
        },
        ingress: {
          active: 1,
          rules: 1
        },
        resources: {
          cpuRequests: '500m',
          cpuLimits: '1000m',
          memoryRequests: '512Mi',
          memoryLimits: '1Gi'
        }
      };
    } catch (error) {
      this.logger.warn('Erreur lors de la collecte des métriques Kubernetes', { 
        error: error.message 
      });
      return null;
    }
  }

  /**
   * Collecte les métriques Docker
   */
  private async collectDockerMetrics(applicationName: string, environment: string): Promise<any> {
    try {
      // Simuler des métriques Docker
      // En production, utiliser l'API Docker
      return {
        containers: {
          running: Math.floor(Math.random() * 3) + 1,
          stopped: 0,
          total: Math.floor(Math.random() * 3) + 1
        },
        images: {
          total: Math.floor(Math.random() * 5) + 1,
          size: Math.floor(Math.random() * 1000) + 100 // MB
        },
        volumes: {
          total: Math.floor(Math.random() * 3),
          size: Math.floor(Math.random() * 500) + 50 // MB
        }
      };
    } catch (error) {
      this.logger.warn('Erreur lors de la collecte des métriques Docker', { 
        error: error.message 
      });
      return null;
    }
  }

  /**
   * Collecte les métriques d'application
   */
  private async collectApplicationMetrics(applicationName: string, environment: string): Promise<ApplicationMetrics> {
    const metrics: ApplicationMetrics = {
      requests: {
        total: 0,
        rate: 0,
        latencyP50: 0,
        latencyP95: 0,
        latencyP99: 0
      },
      errors: {
        total: 0,
        rate: 0,
        types: {}
      },
      availability: {
        uptime: 0,
        downtime: 0,
        percentage: 0
      }
    };

    try {
      // Simuler des métriques d'application
      // En production, récupérer depuis l'application ou Prometheus
      metrics.requests.total = Math.floor(Math.random() * 10000);
      metrics.requests.rate = Math.floor(Math.random() * 100);
      metrics.requests.latencyP50 = Math.floor(Math.random() * 100) + 50;
      metrics.requests.latencyP95 = metrics.requests.latencyP50 * 2;
      metrics.requests.latencyP99 = metrics.requests.latencyP50 * 3;

      metrics.errors.total = Math.floor(Math.random() * 10);
      metrics.errors.rate = metrics.requests.total > 0 ? metrics.errors.total / metrics.requests.total : 0;
      metrics.errors.types = {
        '4xx': Math.floor(metrics.errors.total * 0.7),
        '5xx': Math.floor(metrics.errors.total * 0.3)
      };

      metrics.availability.uptime = Math.floor(Math.random() * 86400); // secondes
      metrics.availability.downtime = Math.floor(Math.random() * 100);
      metrics.availability.percentage = 99.9 - (Math.random() * 0.5);

    } catch (error) {
      this.logger.warn('Erreur lors de la collecte des métriques d\'application', { 
        error: error.message 
      });
    }

    return metrics;
  }

  /**
   * Évalue la santé globale
   */
  private async assessHealth(systemMetrics: SystemMetrics, appMetrics: ApplicationMetrics): Promise<string> {
    let score = 100;

    // Pénalités basées sur les métriques
    if (systemMetrics.cpu.current > 1.5) score -= 20;
    if (systemMetrics.memory.current > 1000) score -= 15;
    if (appMetrics.errors.rate > 0.05) score -= 25;
    if (appMetrics.availability.percentage < 99.5) score -= 30;
    if (appMetrics.requests.latencyP95 > 1000) score -= 10;

    if (score >= 90) return 'excellent';
    if (score >= 75) return 'good';
    if (score >= 60) return 'fair';
    if (score >= 40) return 'poor';
    return 'critical';
  }

  /**
   * Met à jour les métriques Prometheus
   */
  private updatePrometheusMetrics(metrics: DeploymentMetrics): void {
    const labels = {
      application: metrics.applicationName,
      environment: metrics.environment
    };

    // Métriques de ressources
    this.resourceGauge.set(
      { ...labels, resource_type: 'cpu' },
      metrics.system.cpu.current
    );
    this.resourceGauge.set(
      { ...labels, resource_type: 'memory' },
      metrics.system.memory.current
    );

    // Métriques d'application
    this.requestCounter.inc(
      { method: 'GET', endpoint: '/api', status: '200' },
      metrics.application.requests.total
    );
    this.errorCounter.inc(
      { type: 'http', ...labels },
      metrics.application.errors.total
    );
  }

  /**
   * Stocke les métriques dans la mémoire
   */
  private async storeMetrics(metrics: DeploymentMetrics): Promise<void> {
    try {
      // Stocker dans Weaviate pour l'analyse historique
      await this.memory.storeDeploymentMetrics(metrics);
    } catch (error) {
      this.logger.warn('Erreur lors du stockage des métriques', { 
        error: error.message 
      });
    }
  }

  /**
   * Démarre la collecte périodique
   */
  private startPeriodicCollection(): void {
    // Collecter les métriques toutes les 30 secondes
    setInterval(async () => {
      try {
        await this.collectSystemMetrics();
      } catch (error) {
        this.logger.error('Erreur lors de la collecte périodique', { 
          error: error.message 
        });
      }
    }, 30000);
  }

  /**
   * Collecte les métriques système périodiques
   */
  private async collectSystemMetrics(): Promise<void> {
    const memUsage = process.memoryUsage();
    
    this.resourceGauge.set(
      { resource_type: 'heap_used', application: 'agent-devops', environment: 'system' },
      memUsage.heapUsed / 1024 / 1024
    );
    
    this.resourceGauge.set(
      { resource_type: 'heap_total', application: 'agent-devops', environment: 'system' },
      memUsage.heapTotal / 1024 / 1024
    );
  }

  /**
   * Récupère les métriques au format Prometheus
   */
  async getPrometheusMetrics(): Promise<string> {
    return this.metricsRegistry.metrics();
  }

  /**
   * Récupère les métriques historiques
   */
  async getHistoricalMetrics(
    applicationName: string,
    environment: string,
    timeRange: string
  ): Promise<DeploymentMetrics[]> {
    try {
      return await this.memory.getDeploymentMetrics(applicationName, environment, timeRange);
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des métriques historiques', { 
        error: error.message 
      });
      return [];
    }
  }

  /**
   * Génère un rapport de métriques
   */
  async generateMetricsReport(
    applicationName: string,
    environment: string,
    timeRange: string = '24h'
  ): Promise<any> {
    const metrics = await this.getHistoricalMetrics(applicationName, environment, timeRange);
    
    if (metrics.length === 0) {
      return {
        applicationName,
        environment,
        timeRange,
        summary: 'Aucune métrique disponible',
        recommendations: ['Vérifier la collecte de métriques']
      };
    }

    const latest = metrics[metrics.length - 1];
    const avgCpu = metrics.reduce((sum, m) => sum + m.system.cpu.current, 0) / metrics.length;
    const avgMemory = metrics.reduce((sum, m) => sum + m.system.memory.current, 0) / metrics.length;
    const totalRequests = metrics.reduce((sum, m) => sum + m.application.requests.total, 0);
    const totalErrors = metrics.reduce((sum, m) => sum + m.application.errors.total, 0);

    return {
      applicationName,
      environment,
      timeRange,
      summary: {
        currentHealth: latest.health,
        averageCpu: Math.round(avgCpu * 100) / 100,
        averageMemory: Math.round(avgMemory),
        totalRequests,
        totalErrors,
        errorRate: totalRequests > 0 ? (totalErrors / totalRequests * 100).toFixed(2) + '%' : '0%',
        availability: latest.application.availability.percentage.toFixed(2) + '%'
      },
      trends: {
        cpuTrend: this.calculateTrend(metrics.map(m => m.system.cpu.current)),
        memoryTrend: this.calculateTrend(metrics.map(m => m.system.memory.current)),
        errorTrend: this.calculateTrend(metrics.map(m => m.application.errors.rate))
      },
      recommendations: this.generateRecommendations(latest)
    };
  }

  /**
   * Calcule la tendance d'une série de valeurs
   */
  private calculateTrend(values: number[]): string {
    if (values.length < 2) return 'stable';
    
    const first = values[0];
    const last = values[values.length - 1];
    const change = ((last - first) / first) * 100;
    
    if (change > 10) return 'increasing';
    if (change < -10) return 'decreasing';
    return 'stable';
  }

  /**
   * Génère des recommandations basées sur les métriques
   */
  private generateRecommendations(metrics: DeploymentMetrics): string[] {
    const recommendations = [];

    if (metrics.system.cpu.current > 1.5) {
      recommendations.push('CPU élevé détecté - considérer l\'augmentation des ressources');
    }

    if (metrics.system.memory.current > 1000) {
      recommendations.push('Utilisation mémoire élevée - vérifier les fuites mémoire');
    }

    if (metrics.application.errors.rate > 0.05) {
      recommendations.push('Taux d\'erreur élevé - investiguer les erreurs applicatives');
    }

    if (metrics.application.requests.latencyP95 > 1000) {
      recommendations.push('Latence élevée détectée - optimiser les performances');
    }

    if (metrics.application.availability.percentage < 99.5) {
      recommendations.push('Disponibilité faible - améliorer la robustesse');
    }

    if (recommendations.length === 0) {
      recommendations.push('Toutes les métriques sont dans les seuils acceptables');
    }

    return recommendations;
  }
}
