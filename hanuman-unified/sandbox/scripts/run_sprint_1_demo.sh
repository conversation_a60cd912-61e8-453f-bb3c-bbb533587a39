#!/bin/bash

# ========================================
# SCRIPT DE LANCEMENT DÉMONSTRATION SPRINT 1
# Orchestrateur IDE Enhanced avec Vimana
# ========================================

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction d'affichage avec couleurs
print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Vérification des prérequis
check_prerequisites() {
    print_header "VÉRIFICATION DES PRÉREQUIS"
    
    # Vérifier Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js installé : $NODE_VERSION"
    else
        print_error "Node.js non installé"
        exit 1
    fi
    
    # Vérifier TypeScript
    if command -v tsc &> /dev/null; then
        TS_VERSION=$(tsc --version)
        print_success "TypeScript installé : $TS_VERSION"
    else
        print_warning "TypeScript non installé globalement"
        print_info "Installation via : npm install -g typescript"
    fi
    
    # Vérifier ts-node
    if command -v ts-node &> /dev/null; then
        print_success "ts-node disponible"
    else
        print_warning "ts-node non installé globalement"
        print_info "Installation via : npm install -g ts-node"
    fi
    
    # Vérifier le répertoire
    if [ ! -f "package.json" ]; then
        print_error "Exécutez ce script depuis le répertoire hanuman-unified/sandbox"
        exit 1
    fi
    
    print_success "Prérequis vérifiés"
}

# Installation des dépendances
install_dependencies() {
    print_header "INSTALLATION DES DÉPENDANCES"
    
    if [ ! -d "node_modules" ]; then
        print_info "Installation des dépendances npm..."
        npm install
        if [ $? -eq 0 ]; then
            print_success "Dépendances installées"
        else
            print_error "Échec installation dépendances"
            exit 1
        fi
    else
        print_success "Dépendances déjà installées"
    fi
}

# Menu principal
show_menu() {
    print_header "🚀 DÉMONSTRATION SPRINT 1 - IDE ORCHESTRATOR ENHANCED"
    echo -e "${CYAN}Premier orchestrateur IDE spirituellement conscient au monde !${NC}"
    echo ""
    echo "Choisissez une démonstration :"
    echo ""
    echo -e "${GREEN}1.${NC} 🎬 Démonstration complète Sprint 1"
    echo -e "${GREEN}2.${NC} 🚁 Intégration Vimana seulement"
    echo -e "${GREEN}3.${NC} 🧠 Tests Orchestrateur IDE"
    echo -e "${GREEN}4.${NC} 🌐 Tests API Server Enhanced"
    echo -e "${GREEN}5.${NC} 📊 Rapport de progrès Sprint 1"
    echo -e "${GREEN}6.${NC} 🔧 Vérification système"
    echo -e "${GREEN}7.${NC} 🚪 Quitter"
    echo ""
}

# Exécution des démonstrations
run_complete_demo() {
    print_header "🎬 DÉMONSTRATION COMPLÈTE SPRINT 1"
    print_info "Lancement de la démonstration complète..."
    
    if command -v ts-node &> /dev/null; then
        ts-node demo_sprint_1.ts
    else
        print_warning "ts-node non disponible, compilation TypeScript..."
        npx tsc demo_sprint_1.ts --target es2020 --module commonjs --esModuleInterop
        node demo_sprint_1.js
    fi
}

run_vimana_demo() {
    print_header "🚁 DÉMONSTRATION VIMANA"
    print_info "Lancement de la démonstration Vimana..."
    
    cd vimana-integration
    if command -v ts-node &> /dev/null; then
        ts-node demo_vimana_integration.ts
    else
        npx tsc demo_vimana_integration.ts --target es2020 --module commonjs --esModuleInterop
        node demo_vimana_integration.js
    fi
    cd ..
}

run_orchestrator_tests() {
    print_header "🧠 TESTS ORCHESTRATEUR IDE"
    print_info "Lancement des tests orchestrateur..."
    
    cd orchestration
    if command -v ts-node &> /dev/null; then
        ts-node test_ide_orchestrator.ts
    else
        npx tsc test_ide_orchestrator.ts --target es2020 --module commonjs --esModuleInterop
        node test_ide_orchestrator.js
    fi
    cd ..
}

run_api_tests() {
    print_header "🌐 TESTS API SERVER ENHANCED"
    print_info "Vérification du serveur API..."
    
    # Vérifier si le serveur est en cours d'exécution
    if curl -s http://localhost:8085/health > /dev/null 2>&1; then
        print_success "Serveur API détecté sur le port 8085"
        cd api
        if command -v ts-node &> /dev/null; then
            ts-node test_api_server_enhanced.ts
        else
            npx tsc test_api_server_enhanced.ts --target es2020 --module commonjs --esModuleInterop
            node test_api_server_enhanced.js
        fi
        cd ..
    else
        print_warning "Serveur API non démarré"
        print_info "Pour démarrer le serveur :"
        print_info "  1. cd hanuman-unified/sandbox"
        print_info "  2. npm run start:api"
        print_info "  3. Relancer ce script"
    fi
}

show_progress_report() {
    print_header "📊 RAPPORT DE PROGRÈS SPRINT 1"
    
    if [ -f "SPRINT_1_PROGRESS.md" ]; then
        if command -v bat &> /dev/null; then
            bat SPRINT_1_PROGRESS.md
        elif command -v less &> /dev/null; then
            less SPRINT_1_PROGRESS.md
        else
            cat SPRINT_1_PROGRESS.md
        fi
    else
        print_error "Fichier SPRINT_1_PROGRESS.md non trouvé"
    fi
}

check_system() {
    print_header "🔧 VÉRIFICATION SYSTÈME"
    
    print_info "Vérification de l'architecture Sprint 1..."
    
    # Vérifier les fichiers clés
    files=(
        "orchestration/ide_agent_orchestrator.ts"
        "api/sandbox_api_server_enhanced.ts"
        "vimana-integration/vimana_integration_bridge.ts"
        "vimana-integration/divine_code_generator.ts"
        "orchestration/test_ide_orchestrator.ts"
        "api/test_api_server_enhanced.ts"
        "demo_sprint_1.ts"
        "SPRINT_1_PROGRESS.md"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            print_success "Fichier présent : $file"
        else
            print_error "Fichier manquant : $file"
        fi
    done
    
    # Vérifier les dépendances clés
    print_info "Vérification des dépendances..."
    
    if [ -f "package.json" ]; then
        if grep -q "express" package.json; then
            print_success "Express.js configuré"
        fi
        if grep -q "socket.io" package.json; then
            print_success "Socket.IO configuré"
        fi
        if grep -q "typescript" package.json; then
            print_success "TypeScript configuré"
        fi
    fi
    
    print_success "Vérification système terminée"
}

# Boucle principale
main() {
    # Vérifications initiales
    check_prerequisites
    install_dependencies
    
    while true; do
        show_menu
        read -p "Votre choix (1-7): " choice
        
        case $choice in
            1)
                run_complete_demo
                ;;
            2)
                run_vimana_demo
                ;;
            3)
                run_orchestrator_tests
                ;;
            4)
                run_api_tests
                ;;
            5)
                show_progress_report
                ;;
            6)
                check_system
                ;;
            7)
                print_header "🙏 AUM HANUMAN VIMANA IDE ORCHESTRATOR NAMAHA"
                echo -e "${CYAN}Merci d'avoir exploré le Sprint 1 !${NC}"
                echo -e "${PURPLE}✨ Que cette technologie divine serve l'évolution de l'humanité ✨${NC}"
                exit 0
                ;;
            *)
                print_error "Option invalide. Choisissez entre 1 et 7."
                ;;
        esac
        
        echo ""
        read -p "Appuyez sur Entrée pour continuer..."
        clear
    done
}

# Gestion des signaux
trap 'echo -e "\n${YELLOW}Démonstration interrompue${NC}"; exit 1' INT TERM

# Lancement du script
clear
main
