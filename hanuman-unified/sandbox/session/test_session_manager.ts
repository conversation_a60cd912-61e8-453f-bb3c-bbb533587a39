#!/usr/bin/env ts-node

// ========================================
// TESTS SESSION MANAGER ENHANCED
// Tests pour le gestionnaire de sessions avec support Vimana
// ========================================

import { SessionManager } from './session_manager';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * Tests pour le Session Manager Enhanced
 */
export class SessionManagerTests {
  private sessionManager: SessionManager;
  private testDir: string;

  constructor() {
    this.testDir = './test-sessions';
    this.sessionManager = new SessionManager({
      persistenceDir: this.testDir,
      vimanaEnabled: true
    });
  }

  /**
   * Exécute tous les tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 TESTS SESSION MANAGER ENHANCED');
    console.log('='.repeat(50));

    try {
      await this.setupTests();
      await this.testSessionCreation();
      await this.testSpiritualContext();
      await this.testCommandRecording();
      await this.testDivineGeneration();
      await this.testSessionPersistence();
      await this.testGlobalStats();
      await this.testSessionClosure();
      await this.cleanupTests();

      console.log('\n✅ TOUS LES TESTS SESSION MANAGER PASSÉS !');
      console.log('🎉 Le Session Manager Enhanced fonctionne parfaitement');

    } catch (error) {
      console.error('❌ ÉCHEC DES TESTS SESSION MANAGER:', error);
      throw error;
    }
  }

  /**
   * Configuration des tests
   */
  private async setupTests(): Promise<void> {
    console.log('\n🔧 Test 1: Configuration et initialisation');
    
    // Nettoyer le répertoire de test
    try {
      await fs.rm(this.testDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore si le répertoire n'existe pas
    }

    // Créer le répertoire de test
    await fs.mkdir(this.testDir, { recursive: true });
    
    console.log('✅ Configuration terminée');
  }

  /**
   * Test de création de session
   */
  private async testSessionCreation(): Promise<void> {
    console.log('\n📋 Test 2: Création de sessions');
    
    // Créer une session pour agent frontend
    const frontendSession = await this.sessionManager.createSession('agent-frontend-001', {
      currentProject: 'test-project',
      preferences: { theme: 'dark', language: 'fr' }
    });

    if (!frontendSession) {
      throw new Error('Session frontend non créée');
    }

    if (frontendSession.agentId !== 'agent-frontend-001') {
      throw new Error('Agent ID incorrect');
    }

    if (!frontendSession.isActive) {
      throw new Error('Session non active');
    }

    if (!frontendSession.spiritualContext) {
      throw new Error('Contexte spirituel manquant');
    }

    console.log('✅ Session frontend créée');

    // Créer une session pour agent backend
    const backendSession = await this.sessionManager.createSession('agent-backend-001', {
      currentProject: 'test-project'
    });

    if (!backendSession || backendSession.agentId !== 'agent-backend-001') {
      throw new Error('Session backend non créée correctement');
    }

    console.log('✅ Session backend créée');

    // Vérifier les sessions actives
    const activeSessions = this.sessionManager.getActiveSessions();
    if (activeSessions.length !== 2) {
      throw new Error(`Nombre de sessions actives incorrect: ${activeSessions.length}`);
    }

    console.log('✅ Sessions actives vérifiées');
  }

  /**
   * Test du contexte spirituel
   */
  private async testSpiritualContext(): Promise<void> {
    console.log('\n🕉️ Test 3: Contexte spirituel');
    
    const agentId = 'agent-frontend-001';
    const session = this.sessionManager.getSessionState(agentId);
    
    if (!session || !session.spiritualContext) {
      throw new Error('Session ou contexte spirituel manquant');
    }

    // Vérifier les valeurs par défaut
    if (session.spiritualContext.cosmicAlignment !== 85) {
      throw new Error('Alignement cosmique incorrect');
    }

    if (session.spiritualContext.spiritualQuality !== 80) {
      throw new Error('Qualité spirituelle incorrecte');
    }

    // Test invocation bénédiction
    await this.sessionManager.invokeSessionBlessing(agentId, 'AUM GANESHA NAMAHA');
    
    const updatedSession = this.sessionManager.getSessionState(agentId);
    if (!updatedSession?.spiritualContext?.lastBlessing) {
      throw new Error('Bénédiction non enregistrée');
    }

    if (updatedSession.spiritualContext.cosmicAlignment <= 85) {
      throw new Error('Alignement cosmique non amélioré');
    }

    console.log('✅ Contexte spirituel fonctionnel');

    // Test mise à jour contexte spirituel
    await this.sessionManager.updateSpiritualContext(agentId, {
      spiritualQuality: 95,
      divineAgent: 'brahma'
    });

    const finalSession = this.sessionManager.getSessionState(agentId);
    if (finalSession?.spiritualContext?.spiritualQuality !== 95) {
      throw new Error('Mise à jour contexte spirituel échouée');
    }

    console.log('✅ Mise à jour contexte spirituel OK');
  }

  /**
   * Test d'enregistrement de commandes
   */
  private async testCommandRecording(): Promise<void> {
    console.log('\n📝 Test 4: Enregistrement de commandes');
    
    const agentId = 'agent-frontend-001';
    
    // Enregistrer des commandes classiques
    await this.sessionManager.recordCommand(agentId, 'créer un composant React', false);
    await this.sessionManager.recordCommand(agentId, 'ouvrir le fichier App.tsx', false);
    
    // Enregistrer des commandes spirituelles
    await this.sessionManager.recordCommand(agentId, 'créer un agent divin avec bénédiction', true);
    await this.sessionManager.recordCommand(agentId, 'valider la géométrie sacrée', true);

    const session = this.sessionManager.getSessionState(agentId);
    if (!session) {
      throw new Error('Session non trouvée');
    }

    if (session.context.commandHistory.length !== 4) {
      throw new Error(`Nombre de commandes incorrect: ${session.context.commandHistory.length}`);
    }

    if (session.metadata.totalCommands !== 4) {
      throw new Error('Compteur de commandes incorrect');
    }

    if (session.metadata.spiritualActions !== 2) {
      throw new Error('Compteur d\'actions spirituelles incorrect');
    }

    console.log('✅ Enregistrement de commandes OK');
  }

  /**
   * Test d'enregistrement de génération divine
   */
  private async testDivineGeneration(): Promise<void> {
    console.log('\n🚁 Test 5: Génération divine');
    
    const agentId = 'agent-frontend-001';
    
    // Simuler une génération divine
    const divineResult = {
      code: '// AUM BRAHMAYE NAMAHA\nexport class DivineComponent {}',
      spiritualQuality: 92,
      blessings: ['Que ce composant soit béni']
    };

    await this.sessionManager.recordDivineGeneration(agentId, 'brahma', divineResult);

    const session = this.sessionManager.getSessionState(agentId);
    if (!session) {
      throw new Error('Session non trouvée');
    }

    if (session.metadata.divineGenerations !== 1) {
      throw new Error('Génération divine non enregistrée');
    }

    if (session.spiritualContext?.divineAgent !== 'brahma') {
      throw new Error('Agent divin non mis à jour');
    }

    if (session.spiritualContext?.cosmicCycles.creation !== 1) {
      throw new Error('Cycle cosmique création non incrémenté');
    }

    console.log('✅ Génération divine enregistrée');

    // Test avec Vishnu
    await this.sessionManager.recordDivineGeneration(agentId, 'vishnu', {
      spiritualQuality: 88
    });

    const updatedSession = this.sessionManager.getSessionState(agentId);
    if (updatedSession?.spiritualContext?.cosmicCycles.preservation !== 1) {
      throw new Error('Cycle cosmique préservation non incrémenté');
    }

    console.log('✅ Cycles cosmiques fonctionnels');
  }

  /**
   * Test de persistance des sessions
   */
  private async testSessionPersistence(): Promise<void> {
    console.log('\n💾 Test 6: Persistance des sessions');
    
    const agentId = 'agent-backend-001';
    
    // Mettre à jour le contexte
    await this.sessionManager.updateSessionContext(agentId, {
      openFiles: ['server.ts', 'database.ts'],
      currentProject: 'api-project'
    });

    // Vérifier que le fichier de persistance sera créé
    // (la persistance automatique se fait toutes les minutes)
    
    const session = this.sessionManager.getSessionState(agentId);
    if (!session) {
      throw new Error('Session non trouvée');
    }

    if (session.context.openFiles?.length !== 2) {
      throw new Error('Fichiers ouverts non mis à jour');
    }

    if (session.context.currentProject !== 'api-project') {
      throw new Error('Projet courant non mis à jour');
    }

    console.log('✅ Contexte de session mis à jour');
    console.log('✅ Persistance configurée (auto-sauvegarde toutes les minutes)');
  }

  /**
   * Test des statistiques globales
   */
  private async testGlobalStats(): Promise<void> {
    console.log('\n📊 Test 7: Statistiques globales');
    
    const stats = this.sessionManager.getGlobalStats();
    
    if (stats.totalSessions !== 2) {
      throw new Error(`Nombre total de sessions incorrect: ${stats.totalSessions}`);
    }

    if (stats.activeSessions !== 2) {
      throw new Error(`Nombre de sessions actives incorrect: ${stats.activeSessions}`);
    }

    if (stats.totalCommands < 4) {
      throw new Error('Nombre total de commandes incorrect');
    }

    if (stats.spiritualStats.spiritualActions < 2) {
      throw new Error('Actions spirituelles non comptées');
    }

    if (stats.spiritualStats.divineGenerations < 2) {
      throw new Error('Générations divines non comptées');
    }

    if (stats.spiritualStats.blessingsInvoked < 1) {
      throw new Error('Bénédictions non comptées');
    }

    if (stats.spiritualStats.averageCosmicAlignment <= 0) {
      throw new Error('Alignement cosmique moyen incorrect');
    }

    console.log('✅ Statistiques globales correctes');
    console.log(`   Sessions: ${stats.activeSessions}/${stats.totalSessions}`);
    console.log(`   Commandes: ${stats.totalCommands}`);
    console.log(`   Actions spirituelles: ${stats.spiritualStats.spiritualActions}`);
    console.log(`   Générations divines: ${stats.spiritualStats.divineGenerations}`);
    console.log(`   Alignement cosmique moyen: ${stats.spiritualStats.averageCosmicAlignment.toFixed(1)}`);
  }

  /**
   * Test de fermeture de sessions
   */
  private async testSessionClosure(): Promise<void> {
    console.log('\n🔒 Test 8: Fermeture de sessions');
    
    const agentId = 'agent-frontend-001';
    
    // Fermer une session
    await this.sessionManager.closeSession(agentId);
    
    // Vérifier que la session n'est plus active
    const session = this.sessionManager.getSessionState(agentId);
    if (session) {
      throw new Error('Session encore présente après fermeture');
    }

    const activeSessions = this.sessionManager.getActiveSessions();
    if (activeSessions.length !== 1) {
      throw new Error(`Nombre de sessions actives incorrect après fermeture: ${activeSessions.length}`);
    }

    console.log('✅ Session fermée correctement');

    // Test arrêt complet
    await this.sessionManager.shutdown();
    
    const finalActiveSessions = this.sessionManager.getActiveSessions();
    if (finalActiveSessions.length !== 0) {
      throw new Error('Sessions encore actives après shutdown');
    }

    console.log('✅ Arrêt complet réussi');
  }

  /**
   * Nettoyage des tests
   */
  private async cleanupTests(): Promise<void> {
    console.log('\n🧹 Nettoyage des tests');
    
    try {
      await fs.rm(this.testDir, { recursive: true, force: true });
      console.log('✅ Nettoyage terminé');
    } catch (error) {
      console.warn('⚠️ Erreur nettoyage:', error);
    }
  }
}

/**
 * Fonction utilitaire pour exécuter les tests
 */
export async function runSessionManagerTests(): Promise<void> {
  const tests = new SessionManagerTests();
  await tests.runAllTests();
}

// Exécuter si appelé directement
if (require.main === module) {
  runSessionManagerTests().catch(console.error);
}
