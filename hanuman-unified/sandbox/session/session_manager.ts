// ========================================
// SESSION MANAGER ENHANCED
// Gestionnaire de sessions multi-agents avec contexte spirituel
// ========================================

import { EventEmitter } from 'events';
import * as fs from 'fs/promises';
import * as path from 'path';

// Types
interface AgentSession {
  agentId: string;
  sessionId: string;
  startTime: Date;
  lastActivity: Date;
  isActive: boolean;
  context: SessionContext;
  spiritualContext?: SpiritualSessionContext;
  metadata: SessionMetadata;
}

interface SessionContext {
  workspaceDir: string;
  currentProject?: string;
  openFiles: string[];
  commandHistory: string[];
  preferences: Record<string, any>;
}

interface SpiritualSessionContext {
  vimanaEnabled: boolean;
  cosmicAlignment: number;
  spiritualQuality: number;
  divineAgent?: string;
  mantrasActive: string[];
  lastBlessing?: string;
  blessedAt?: Date;
  trigunaBalance: {
    sattva: number;
    rajas: number;
    tamas: number;
    harmony: number;
  };
  cosmicCycles: {
    creation: number;
    preservation: number;
    transformation: number;
  };
}

interface SessionMetadata {
  totalCommands: number;
  totalFiles: number;
  totalProjects: number;
  spiritualActions?: number;
  divineGenerations?: number;
  blessingsInvoked?: number;
}

interface SessionPersistence {
  sessionId: string;
  agentId: string;
  timestamp: Date;
  context: SessionContext;
  spiritualContext?: SpiritualSessionContext;
  metadata: SessionMetadata;
}

/**
 * Gestionnaire de sessions enhanced avec support spirituel
 */
export class SessionManager extends EventEmitter {
  private sessions: Map<string, AgentSession> = new Map();
  private persistenceDir: string;
  private vimanaEnabled: boolean = true;
  private autoSaveInterval: NodeJS.Timeout | null = null;

  constructor(config: any = {}) {
    super();
    this.persistenceDir = config.persistenceDir || './sessions';
    this.vimanaEnabled = config.vimanaEnabled !== false;

    this.initializeSessionManager();
  }

  /**
   * Initialise le gestionnaire de sessions
   */
  private async initializeSessionManager(): Promise<void> {
    console.log('📋 Initialisation Session Manager Enhanced...');

    try {
      // Créer le répertoire de persistance
      await fs.mkdir(this.persistenceDir, { recursive: true });

      // Charger les sessions existantes
      await this.loadPersistedSessions();

      // Démarrer l'auto-sauvegarde
      this.startAutoSave();

      console.log('✅ Session Manager initialisé');

    } catch (error) {
      console.error('❌ Erreur initialisation Session Manager:', error);
    }
  }

  /**
   * Crée une nouvelle session pour un agent
   */
  async createSession(agentId: string, config: any = {}): Promise<AgentSession> {
    const sessionId = this.generateSessionId(agentId);

    console.log(`📋 Création session pour agent: ${agentId} (${sessionId})`);

    // Fermer session existante si présente
    if (this.sessions.has(agentId)) {
      await this.closeSession(agentId);
    }

    const workspaceDir = config.workspaceDir || path.join('./workspace', agentId);
    await fs.mkdir(workspaceDir, { recursive: true });

    const session: AgentSession = {
      agentId,
      sessionId,
      startTime: new Date(),
      lastActivity: new Date(),
      isActive: true,
      context: {
        workspaceDir,
        currentProject: config.currentProject,
        openFiles: [],
        commandHistory: [],
        preferences: config.preferences || {}
      },
      metadata: {
        totalCommands: 0,
        totalFiles: 0,
        totalProjects: 0,
        spiritualActions: 0,
        divineGenerations: 0,
        blessingsInvoked: 0
      }
    };

    // Contexte spirituel si Vimana activé
    if (this.vimanaEnabled) {
      session.spiritualContext = this.createSpiritualContext(agentId);
    }

    this.sessions.set(agentId, session);

    // Bénédiction d'ouverture
    if (this.vimanaEnabled) {
      await this.invokeSessionBlessing(agentId, 'AUM GANESHA NAMAHA - Session bénie et protégée');
    }

    this.emit('session_created', { agentId, sessionId });
    console.log(`✅ Session créée pour ${agentId}`);

    return session;
  }

  /**
   * Met à jour le contexte d'une session
   */
  async updateSessionContext(agentId: string, updates: Partial<SessionContext>): Promise<void> {
    const session = this.sessions.get(agentId);
    if (!session) {
      throw new Error(`Session non trouvée pour agent: ${agentId}`);
    }

    // Mettre à jour le contexte
    session.context = { ...session.context, ...updates };
    session.lastActivity = new Date();

    // Mettre à jour les métadonnées
    if (updates.openFiles) {
      session.metadata.totalFiles = Math.max(session.metadata.totalFiles, updates.openFiles.length);
    }

    this.emit('session_updated', { agentId, updates });
  }

  /**
   * Met à jour le contexte spirituel
   */
  async updateSpiritualContext(agentId: string, updates: Partial<SpiritualSessionContext>): Promise<void> {
    const session = this.sessions.get(agentId);
    if (!session || !session.spiritualContext) {
      throw new Error(`Session spirituelle non trouvée pour agent: ${agentId}`);
    }

    // Mettre à jour le contexte spirituel
    session.spiritualContext = { ...session.spiritualContext, ...updates };
    session.lastActivity = new Date();

    this.emit('spiritual_context_updated', { agentId, updates });
  }

  /**
   * Enregistre une commande dans l'historique
   */
  async recordCommand(agentId: string, command: string, isSpiritual: boolean = false): Promise<void> {
    const session = this.sessions.get(agentId);
    if (!session) {
      throw new Error(`Session non trouvée pour agent: ${agentId}`);
    }

    // Ajouter à l'historique
    session.context.commandHistory.push(command);
    session.metadata.totalCommands++;
    session.lastActivity = new Date();

    // Compteur spirituel
    if (isSpiritual && session.spiritualContext) {
      session.metadata.spiritualActions = (session.metadata.spiritualActions || 0) + 1;
    }

    // Limiter l'historique
    if (session.context.commandHistory.length > 100) {
      session.context.commandHistory = session.context.commandHistory.slice(-100);
    }

    this.emit('command_recorded', { agentId, command, isSpiritual });
  }

  /**
   * Invoque une bénédiction pour la session
   */
  async invokeSessionBlessing(agentId: string, mantra: string): Promise<void> {
    const session = this.sessions.get(agentId);
    if (!session || !session.spiritualContext) {
      return;
    }

    console.log(`🙏 Bénédiction session ${agentId}: ${mantra}`);

    // Mettre à jour le contexte spirituel
    session.spiritualContext.lastBlessing = mantra;
    session.spiritualContext.blessedAt = new Date();
    session.spiritualContext.mantrasActive.push(mantra);
    session.spiritualContext.cosmicAlignment = Math.min(100, session.spiritualContext.cosmicAlignment + 5);

    // Métadonnées
    session.metadata.blessingsInvoked = (session.metadata.blessingsInvoked || 0) + 1;
    session.lastActivity = new Date();

    this.emit('blessing_invoked', { agentId, mantra });
  }

  /**
   * Enregistre une génération divine
   */
  async recordDivineGeneration(agentId: string, divineAgent: string, result: any): Promise<void> {
    const session = this.sessions.get(agentId);
    if (!session) {
      return;
    }

    // Métadonnées
    session.metadata.divineGenerations = (session.metadata.divineGenerations || 0) + 1;
    session.lastActivity = new Date();

    // Contexte spirituel
    if (session.spiritualContext) {
      session.spiritualContext.divineAgent = divineAgent;
      session.spiritualContext.spiritualQuality = result.spiritualQuality || session.spiritualContext.spiritualQuality;

      // Incrémenter le cycle cosmique approprié
      switch (divineAgent) {
        case 'brahma':
          session.spiritualContext.cosmicCycles.creation++;
          break;
        case 'vishnu':
          session.spiritualContext.cosmicCycles.preservation++;
          break;
        case 'shiva':
          session.spiritualContext.cosmicCycles.transformation++;
          break;
      }
    }

    this.emit('divine_generation_recorded', { agentId, divineAgent, result });
  }

  /**
   * Obtient l'état d'une session
   */
  getSessionState(agentId: string): AgentSession | null {
    return this.sessions.get(agentId) || null;
  }

  /**
   * Obtient toutes les sessions actives
   */
  getActiveSessions(): AgentSession[] {
    return Array.from(this.sessions.values()).filter(session => session.isActive);
  }

  /**
   * Obtient les statistiques globales
   */
  getGlobalStats(): any {
    const activeSessions = this.getActiveSessions();

    const stats = {
      totalSessions: this.sessions.size,
      activeSessions: activeSessions.length,
      totalCommands: 0,
      totalFiles: 0,
      totalProjects: 0,
      spiritualStats: {
        spiritualActions: 0,
        divineGenerations: 0,
        blessingsInvoked: 0,
        averageCosmicAlignment: 0,
        averageSpiritualQuality: 0
      }
    };

    let cosmicAlignmentSum = 0;
    let spiritualQualitySum = 0;
    let spiritualSessions = 0;

    for (const session of activeSessions) {
      stats.totalCommands += session.metadata.totalCommands;
      stats.totalFiles += session.metadata.totalFiles;
      stats.totalProjects += session.metadata.totalProjects;

      if (session.spiritualContext) {
        stats.spiritualStats.spiritualActions += session.metadata.spiritualActions || 0;
        stats.spiritualStats.divineGenerations += session.metadata.divineGenerations || 0;
        stats.spiritualStats.blessingsInvoked += session.metadata.blessingsInvoked || 0;

        cosmicAlignmentSum += session.spiritualContext.cosmicAlignment;
        spiritualQualitySum += session.spiritualContext.spiritualQuality;
        spiritualSessions++;
      }
    }

    if (spiritualSessions > 0) {
      stats.spiritualStats.averageCosmicAlignment = cosmicAlignmentSum / spiritualSessions;
      stats.spiritualStats.averageSpiritualQuality = spiritualQualitySum / spiritualSessions;
    }

    return stats;
  }

  /**
   * Ferme une session
   */
  async closeSession(agentId: string): Promise<void> {
    const session = this.sessions.get(agentId);
    if (!session) {
      return;
    }

    console.log(`🔒 Fermeture session pour ${agentId}`);

    // Bénédiction de fermeture
    if (this.vimanaEnabled && session.spiritualContext) {
      await this.invokeSessionBlessing(agentId, 'AUM SHANTI SHANTI SHANTI - Session fermée en paix');
    }

    // Sauvegarder avant fermeture
    await this.persistSession(session);

    // Marquer comme inactive
    session.isActive = false;
    this.sessions.delete(agentId);

    this.emit('session_closed', { agentId });
    console.log(`✅ Session fermée pour ${agentId}`);
  }

  /**
   * Active/désactive Vimana
   */
  setVimanaEnabled(enabled: boolean): void {
    this.vimanaEnabled = enabled;
    console.log(`🚁 Vimana ${enabled ? 'activé' : 'désactivé'} pour Session Manager`);

    // Mettre à jour toutes les sessions actives
    for (const session of this.sessions.values()) {
      if (enabled && !session.spiritualContext) {
        session.spiritualContext = this.createSpiritualContext(session.agentId);
      } else if (!enabled && session.spiritualContext) {
        delete session.spiritualContext;
      }
    }
  }

  /**
   * Arrêt propre du gestionnaire
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Arrêt du Session Manager...');

    // Arrêter l'auto-sauvegarde
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }

    // Fermer toutes les sessions
    const activeAgents = Array.from(this.sessions.keys());
    for (const agentId of activeAgents) {
      await this.closeSession(agentId);
    }

    console.log('✅ Session Manager arrêté');
  }

  // ========================================
  // MÉTHODES UTILITAIRES PRIVÉES
  // ========================================

  /**
   * Crée un contexte spirituel pour une session
   */
  private createSpiritualContext(agentId: string): SpiritualSessionContext {
    return {
      vimanaEnabled: true,
      cosmicAlignment: 85,
      spiritualQuality: 80,
      mantrasActive: [],
      trigunaBalance: {
        sattva: 40,
        rajas: 35,
        tamas: 25,
        harmony: 85
      },
      cosmicCycles: {
        creation: 0,
        preservation: 0,
        transformation: 0
      }
    };
  }

  /**
   * Génère un ID de session unique
   */
  private generateSessionId(agentId: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${agentId}-${timestamp}-${random}`;
  }

  /**
   * Démarre l'auto-sauvegarde périodique
   */
  private startAutoSave(): void {
    this.autoSaveInterval = setInterval(async () => {
      try {
        await this.saveAllSessions();
      } catch (error) {
        console.error('❌ Erreur auto-sauvegarde:', error);
      }
    }, 60000); // Toutes les minutes
  }

  /**
   * Sauvegarde toutes les sessions actives
   */
  private async saveAllSessions(): Promise<void> {
    const activeSessions = this.getActiveSessions();

    for (const session of activeSessions) {
      try {
        await this.persistSession(session);
      } catch (error) {
        console.error(`❌ Erreur sauvegarde session ${session.agentId}:`, error);
      }
    }
  }

  /**
   * Persiste une session sur disque
   */
  private async persistSession(session: AgentSession): Promise<void> {
    const persistence: SessionPersistence = {
      sessionId: session.sessionId,
      agentId: session.agentId,
      timestamp: new Date(),
      context: session.context,
      spiritualContext: session.spiritualContext,
      metadata: session.metadata
    };

    const filePath = path.join(this.persistenceDir, `${session.agentId}.json`);
    await fs.writeFile(filePath, JSON.stringify(persistence, null, 2), 'utf8');
  }

  /**
   * Charge les sessions persistées
   */
  private async loadPersistedSessions(): Promise<void> {
    try {
      const files = await fs.readdir(this.persistenceDir);
      const sessionFiles = files.filter(file => file.endsWith('.json'));

      for (const file of sessionFiles) {
        try {
          const filePath = path.join(this.persistenceDir, file);
          const content = await fs.readFile(filePath, 'utf8');
          const persistence: SessionPersistence = JSON.parse(content);

          console.log(`📋 Session persistée trouvée: ${persistence.agentId}`);

        } catch (error) {
          console.error(`❌ Erreur chargement session ${file}:`, error);
        }
      }

    } catch (error) {
      console.warn('⚠️ Aucune session persistée trouvée');
    }
  }
}
