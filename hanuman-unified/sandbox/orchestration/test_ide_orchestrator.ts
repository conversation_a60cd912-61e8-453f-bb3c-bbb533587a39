// ========================================
// TEST IDE AGENT ORCHESTRATOR ENHANCED
// Tests pour l'orchestrateur avec intégration Vimana
// ========================================

import { IDEAgentOrchestrator } from './ide_agent_orchestrator';

/**
 * Tests pour l'orchestrateur IDE avec Vimana
 */
export class IDEOrchestratorTests {
  private orchestrator: IDEAgentOrchestrator;

  constructor() {
    // Mock du HanumanOrganOrchestrator
    const mockHanumanOrchestrator = {
      getAgentContext: (agentId: string) => ({
        agentId,
        projectType: 'agent',
        architecture: 'hanuman-biomimetic-enhanced'
      })
    };

    this.orchestrator = new IDEAgentOrchestrator(mockHanumanOrchestrator, {
      logLevel: 'debug',
      enableMetrics: true
    });
  }

  /**
   * Exécute tous les tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 TESTS IDE ORCHESTRATOR ENHANCED');
    console.log('='.repeat(50));

    try {
      await this.testBasicInitialization();
      await this.testClassicCommands();
      await this.testVimanaCommands();
      await this.testSpiritualCommands();
      await this.testMixedWorkflow();

      console.log('\n✅ TOUS LES TESTS PASSÉS !');
      console.log('🎉 L\'orchestrateur IDE Enhanced fonctionne parfaitement');

    } catch (error) {
      console.error('❌ ÉCHEC DES TESTS:', error);
      throw error;
    } finally {
      await this.orchestrator.shutdown();
    }
  }

  /**
   * Test d'initialisation de base
   */
  private async testBasicInitialization(): Promise<void> {
    console.log('\n🔧 Test 1: Initialisation de base');
    
    // Attendre l'initialisation
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const state = this.orchestrator.getState();
    
    if (state.status !== 'running') {
      throw new Error(`État incorrect: ${state.status}`);
    }
    
    const vimanaState = this.orchestrator.getVimanaState();
    if (!vimanaState) {
      throw new Error('Vimana non initialisé');
    }
    
    console.log('✅ Orchestrateur initialisé avec succès');
    console.log(`   État: ${state.status}`);
    console.log(`   Vimana: ${vimanaState.status}`);
  }

  /**
   * Test des commandes classiques
   */
  private async testClassicCommands(): Promise<void> {
    console.log('\n🗣️ Test 2: Commandes classiques');
    
    const commands = [
      'créer un agent frontend moderne',
      'créer une interface utilisateur',
      'setup projet complet'
    ];

    for (const command of commands) {
      console.log(`   Test: "${command}"`);
      
      const results = await this.orchestrator.processNaturalLanguageCommand(
        'test-agent-001',
        command
      );
      
      if (results.length === 0) {
        throw new Error(`Aucun résultat pour: ${command}`);
      }
      
      console.log(`   ✅ ${results.length} actions exécutées`);
    }
  }

  /**
   * Test des commandes Vimana
   */
  private async testVimanaCommands(): Promise<void> {
    console.log('\n🚁 Test 3: Commandes Vimana');
    
    const vimanaCommands = [
      'créer un agent frontend divin avec bénédiction',
      'générer du code sacré avec mantras',
      'valider la géométrie sacrée du code',
      'équilibrer les tâches selon Tri-Guna'
    ];

    for (const command of vimanaCommands) {
      console.log(`   Test: "${command}"`);
      
      const results = await this.orchestrator.processNaturalLanguageCommand(
        'vimana-agent-001',
        command
      );
      
      if (results.length === 0) {
        throw new Error(`Aucun résultat pour: ${command}`);
      }
      
      // Vérifier que Vimana a été utilisé
      const hasVimanaAction = results.some(r => 
        r.actionId.includes('divine') || 
        r.actionId.includes('sacred') || 
        r.actionId.includes('triguna')
      );
      
      if (!hasVimanaAction) {
        throw new Error(`Vimana non utilisé pour: ${command}`);
      }
      
      console.log(`   ✅ ${results.length} actions divines exécutées`);
    }
  }

  /**
   * Test des commandes spirituelles avancées
   */
  private async testSpiritualCommands(): Promise<void> {
    console.log('\n🕉️ Test 4: Commandes spirituelles avancées');
    
    const spiritualCommands = [
      'créer un agent avec Brahma et géométrie sacrée',
      'transformer le code avec Shiva et mantras',
      'préserver la stabilité avec Vishnu et Fibonacci',
      'harmoniser le projet selon le nombre d\'or phi'
    ];

    for (const command of spiritualCommands) {
      console.log(`   Test: "${command}"`);
      
      const results = await this.orchestrator.processNaturalLanguageCommand(
        'spiritual-agent-001',
        command
      );
      
      if (results.length === 0) {
        throw new Error(`Aucun résultat pour: ${command}`);
      }
      
      // Vérifier la qualité spirituelle
      const divineResults = results.filter(r => r.metadata?.spiritualQuality);
      if (divineResults.length > 0) {
        const avgQuality = divineResults.reduce((sum, r) => 
          sum + (r.metadata.spiritualQuality || 0), 0) / divineResults.length;
        
        if (avgQuality < 60) {
          throw new Error(`Qualité spirituelle trop faible: ${avgQuality}`);
        }
        
        console.log(`   ✨ Qualité spirituelle moyenne: ${avgQuality.toFixed(1)}/108`);
      }
      
      console.log(`   ✅ ${results.length} actions spirituelles exécutées`);
    }
  }

  /**
   * Test d'un workflow mixte
   */
  private async testMixedWorkflow(): Promise<void> {
    console.log('\n🌟 Test 5: Workflow mixte');
    
    const workflow = [
      'créer un agent backend divin avec mantras',
      'valider la géométrie sacrée',
      'équilibrer selon Tri-Guna',
      'ouvrir le fichier généré'
    ];

    console.log('   Exécution workflow complet...');
    
    for (let i = 0; i < workflow.length; i++) {
      const command = workflow[i];
      console.log(`   Étape ${i + 1}: "${command}"`);
      
      const results = await this.orchestrator.processNaturalLanguageCommand(
        'workflow-agent-001',
        command
      );
      
      if (results.length === 0) {
        throw new Error(`Échec étape ${i + 1}: ${command}`);
      }
      
      console.log(`   ✅ Étape ${i + 1} complétée (${results.length} actions)`);
    }
    
    // Vérifier les métriques finales
    const metrics = this.orchestrator.getMetrics();
    console.log(`   📊 Métriques finales:`);
    console.log(`      Commandes traitées: ${metrics.commandsProcessed}`);
    console.log(`      Taux de succès: ${metrics.successRate.toFixed(1)}%`);
    console.log(`      Temps moyen: ${metrics.averageProcessingTime.toFixed(0)}ms`);
    
    if (metrics.successRate < 80) {
      throw new Error(`Taux de succès trop faible: ${metrics.successRate}%`);
    }
  }
}

/**
 * Fonction utilitaire pour exécuter les tests
 */
export async function runIDEOrchestratorTests(): Promise<void> {
  const tests = new IDEOrchestratorTests();
  await tests.runAllTests();
}

// Exécuter si appelé directement
if (require.main === module) {
  runIDEOrchestratorTests().catch(console.error);
}
