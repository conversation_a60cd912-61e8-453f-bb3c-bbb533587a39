// ========================================
// TEST INTÉGRATION VIMANA
// Tests rapides pour vérifier l'intégration
// ========================================

import { HanumanVimanaIntegrationBridge } from './vimana_integration_bridge';
import { DivineCodeGenerator } from './divine_code_generator';
import {
  VimanaBridgeConfig,
  DivineGenerationRequest,
  SpiritualContext
} from './vimana_types';

/**
 * Tests d'intégration rapides
 */
export class VimanaIntegrationTests {
  private bridge: HanumanVimanaIntegrationBridge;
  private generator: DivineCodeGenerator;

  constructor() {
    const config: VimanaBridgeConfig = {
      vimanaPath: '../../../vimana',
      enableSpiritualEnhancement: true,
      defaultDivineAgent: 'brahma',
      cosmicValidation: true,
      mantrasRequired: true,
      goldenRatioThreshold: 70,
      spiritualQualityThreshold: 80,
      trigunaBalanceRequired: true,
      sacredGeometryValidation: true,
      cosmicTimingOptimization: true
    };

    this.bridge = new HanumanVimanaIntegrationBridge(config);
  }

  /**
   * Exécute tous les tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 TESTS D\'INTÉGRATION VIMANA');
    console.log('='.repeat(40));

    try {
      await this.testBridgeInitialization();
      await this.testDivineGeneration();
      await this.testSacredGeometry();
      await this.testTriGunaBalance();
      await this.testTemplateGeneration();

      console.log('\n✅ TOUS LES TESTS PASSÉS !');
      console.log('🎉 L\'intégration Vimana fonctionne parfaitement');

    } catch (error) {
      console.error('❌ ÉCHEC DES TESTS:', error);
      throw error;
    } finally {
      await this.bridge.shutdown();
    }
  }

  /**
   * Test d'initialisation du pont
   */
  private async testBridgeInitialization(): Promise<void> {
    console.log('\n🔧 Test 1: Initialisation du pont');
    
    await this.bridge.initialize();
    
    const state = this.bridge.getSystemState();
    
    // Vérifications
    if (state.status !== 'blessed') {
      throw new Error(`État incorrect: ${state.status}`);
    }
    
    if (state.cosmicAlignment < 70) {
      throw new Error(`Alignement cosmique trop faible: ${state.cosmicAlignment}`);
    }
    
    if (!state.divineAgents.brahma.active) {
      throw new Error('Agent Brahma non activé');
    }
    
    console.log('✅ Pont initialisé avec succès');
    console.log(`   État: ${state.status}`);
    console.log(`   Alignement: ${state.cosmicAlignment}%`);
  }

  /**
   * Test de génération divine
   */
  private async testDivineGeneration(): Promise<void> {
    console.log('\n🕉️ Test 2: Génération divine');
    
    const request: DivineGenerationRequest = {
      agentId: 'test-agent-001',
      command: 'créer un agent de test divin',
      divineAgent: 'brahma',
      context: {
        type: 'test',
        agentId: 'test-agent-001',
        environment: 'test',
        metadata: {
          purpose: 'Test d\'intégration'
        }
      },
      cosmicPrinciples: {
        goldenRatio: true,
        mantras: true,
        sacredGeometry: true,
        fibonacciStructure: true,
        cosmicFrequency: true
      }
    };

    const result = await this.bridge.generateDivineCode(request);
    
    // Vérifications
    if (!result.success) {
      throw new Error('Génération divine échouée');
    }
    
    if (result.spiritualQuality < 60) {
      throw new Error(`Qualité spirituelle trop faible: ${result.spiritualQuality}`);
    }
    
    if (result.mantras.length === 0) {
      throw new Error('Aucun mantra généré');
    }
    
    if (!result.code.includes('AUM')) {
      throw new Error('Code ne contient pas de mantras');
    }
    
    console.log('✅ Génération divine réussie');
    console.log(`   Qualité spirituelle: ${result.spiritualQuality}/108`);
    console.log(`   Mantras: ${result.mantras.length}`);
    console.log(`   Longueur code: ${result.code.length} caractères`);
  }

  /**
   * Test de validation géométrie sacrée
   */
  private async testSacredGeometry(): Promise<void> {
    console.log('\n📐 Test 3: Géométrie sacrée');
    
    const testCode = `// AUM BRAHMAYE NAMAHA
export class TestClass {
  private readonly phi = 1.618033988749895;
  private readonly cosmic = 432;
  
  constructor() {
    this.init();
  }
  
  private init(): void {
    console.log('Test');
  }
  
  async process(): Promise<void> {
    console.log('Processing');
  }
}`;

    const validation = await this.bridge.validateSacredGeometry(testCode);
    
    // Vérifications
    if (validation.spiritualScore < 50) {
      throw new Error(`Score spirituel trop faible: ${validation.spiritualScore}`);
    }
    
    if (validation.goldenRatioCompliance < 0) {
      throw new Error('Conformité nombre d\'or négative');
    }
    
    if (validation.cosmicResonance.frequency < 400) {
      throw new Error('Fréquence cosmique trop faible');
    }
    
    console.log('✅ Validation géométrie sacrée réussie');
    console.log(`   Score spirituel: ${validation.spiritualScore}/108`);
    console.log(`   Conformité φ: ${validation.goldenRatioCompliance.toFixed(1)}%`);
    console.log(`   Fréquence: ${validation.cosmicResonance.frequency.toFixed(1)}Hz`);
  }

  /**
   * Test d'équilibrage Tri-Guna
   */
  private async testTriGunaBalance(): Promise<void> {
    console.log('\n⚖️ Test 4: Équilibrage Tri-Guna');
    
    const workload = [
      { type: 'create', name: 'Nouveau composant' },
      { type: 'maintain', name: 'Maintenance' },
      { type: 'refactor', name: 'Refactoring' }
    ];

    const balance = await this.bridge.balanceTriGuna(workload);
    
    // Vérifications
    const total = balance.sattva + balance.rajas + balance.tamas;
    if (Math.abs(total - 100) > 1) {
      throw new Error(`Balance Tri-Guna incorrecte: ${total}%`);
    }
    
    if (balance.harmony < 0 || balance.harmony > 100) {
      throw new Error(`Score d'harmonie invalide: ${balance.harmony}`);
    }
    
    if (!balance.recommendation) {
      throw new Error('Aucune recommandation générée');
    }
    
    console.log('✅ Équilibrage Tri-Guna réussi');
    console.log(`   Sattva: ${balance.sattva}%`);
    console.log(`   Rajas: ${balance.rajas}%`);
    console.log(`   Tamas: ${balance.tamas}%`);
    console.log(`   Harmonie: ${balance.harmony}/100`);
  }

  /**
   * Test de génération de templates
   */
  private async testTemplateGeneration(): Promise<void> {
    console.log('\n🎭 Test 5: Génération de templates');
    
    // Initialiser le générateur
    const vimanaConfig = (this.bridge as any).vimanaConfig;
    this.generator = new DivineCodeGenerator(vimanaConfig);
    
    const agentTypes = ['frontend', 'backend', 'devops'];
    
    for (const type of agentTypes) {
      const template = this.generator.generateHanumanAgentTemplate(type, `test-${type}-001`);
      
      // Vérifications
      if (!template.id) {
        throw new Error(`Template ${type} sans ID`);
      }
      
      if (!template.template.includes('AUM')) {
        throw new Error(`Template ${type} sans mantras`);
      }
      
      if (!template.template.includes('export class')) {
        throw new Error(`Template ${type} sans classe`);
      }
      
      if (template.blessings.length === 0) {
        throw new Error(`Template ${type} sans bénédictions`);
      }
      
      console.log(`   ✅ Template ${type}: ${template.name}`);
    }
    
    console.log('✅ Génération de templates réussie');
  }
}

/**
 * Fonction utilitaire pour exécuter les tests
 */
export async function runVimanaTests(): Promise<void> {
  const tests = new VimanaIntegrationTests();
  await tests.runAllTests();
}

// Exécuter si appelé directement
if (require.main === module) {
  runVimanaTests().catch(console.error);
}
