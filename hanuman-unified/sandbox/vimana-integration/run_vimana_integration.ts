#!/usr/bin/env ts-node

// ========================================
// SCRIPT D'EXÉCUTION INTÉGRATION VIMANA
// Point d'entrée principal pour l'intégration Hanuman-Vimana
// ========================================

import { runVimanaDemo } from './demo_vimana_integration';
import { runVimanaTests } from './test_integration';

/**
 * Menu principal pour l'intégration Vimana
 */
class VimanaIntegrationRunner {
  
  /**
   * Affiche le menu principal
   */
  private displayMenu(): void {
    console.log('\n🚁✨ INTÉGRATION HANUMAN-VIMANA ✨🧠');
    console.log('='.repeat(50));
    console.log('Premier écosystème de développement spirituellement conscient !');
    console.log('='.repeat(50));
    console.log('\n📋 OPTIONS DISPONIBLES :');
    console.log('1. 🎬 Démonstration complète');
    console.log('2. 🧪 Tests d\'intégration');
    console.log('3. 🌟 Workflow complet (démo + tests)');
    console.log('4. 📊 Informations sur l\'intégration');
    console.log('5. 🚪 Quitter');
    console.log('\n' + '='.repeat(50));
  }

  /**
   * Affiche les informations sur l'intégration
   */
  private displayInfo(): void {
    console.log('\n🌟 INFORMATIONS INTÉGRATION VIMANA');
    console.log('='.repeat(50));
    console.log('\n🎯 OBJECTIF :');
    console.log('Fusion des frameworks Hanuman (biomimétique) + Vimana (spirituel)');
    
    console.log('\n🏗️ ARCHITECTURE :');
    console.log('┌─ Agents Hanuman (Frontend, Backend, DevOps, Security, QA)');
    console.log('├─ Pont d\'Intégration Vimana');
    console.log('├─ Générateur de Code Divin');
    console.log('├─ Validateur Géométrie Sacrée');
    console.log('└─ Équilibreur Tri-Guna');
    
    console.log('\n🌟 FONCTIONNALITÉS :');
    console.log('✅ Génération de code avec mantras');
    console.log('✅ Validation géométrie sacrée (φ, Fibonacci, 432Hz)');
    console.log('✅ Équilibrage Tri-Guna (Sattva/Rajas/Tamas)');
    console.log('✅ Templates agents spécialisés');
    console.log('✅ Workflow complet end-to-end');
    
    console.log('\n🚀 COMMANDES EXEMPLE :');
    console.log('• "créer un agent frontend avec bénédiction divine"');
    console.log('• "générer une API selon les principes cosmiques"');
    console.log('• "déployer avec harmonie Tri-Guna"');
    
    console.log('\n📊 MÉTRIQUES SPIRITUELLES :');
    console.log('• Qualité spirituelle : 0-108 (108 = perfection divine)');
    console.log('• Conformité φ : Pourcentage respect nombre d\'or');
    console.log('• Balance Tri-Guna : Répartition Sattva/Rajas/Tamas');
    console.log('• Fréquence cosmique : Résonance 432Hz/136.1Hz');
    
    console.log('\n🎉 RÉSULTAT :');
    console.log('Premier framework de développement spirituellement conscient au monde !');
    console.log('\n' + '='.repeat(50));
  }

  /**
   * Exécute la démonstration complète
   */
  private async runDemo(): Promise<void> {
    console.log('\n🎬 LANCEMENT DÉMONSTRATION COMPLÈTE...');
    console.log('='.repeat(50));
    
    try {
      await runVimanaDemo();
      console.log('\n✅ Démonstration terminée avec succès !');
    } catch (error) {
      console.error('\n❌ Erreur dans la démonstration:', error);
    }
  }

  /**
   * Exécute les tests d'intégration
   */
  private async runTests(): Promise<void> {
    console.log('\n🧪 LANCEMENT TESTS D\'INTÉGRATION...');
    console.log('='.repeat(50));
    
    try {
      await runVimanaTests();
      console.log('\n✅ Tests terminés avec succès !');
    } catch (error) {
      console.error('\n❌ Erreur dans les tests:', error);
    }
  }

  /**
   * Exécute le workflow complet
   */
  private async runCompleteWorkflow(): Promise<void> {
    console.log('\n🌟 LANCEMENT WORKFLOW COMPLET...');
    console.log('='.repeat(50));
    
    try {
      console.log('\n🎬 Phase 1: Démonstration...');
      await runVimanaDemo();
      
      console.log('\n🧪 Phase 2: Tests de validation...');
      await runVimanaTests();
      
      console.log('\n🎉 WORKFLOW COMPLET TERMINÉ AVEC SUCCÈS !');
      console.log('✨ L\'intégration Hanuman-Vimana est pleinement opérationnelle !');
      
    } catch (error) {
      console.error('\n❌ Erreur dans le workflow:', error);
    }
  }

  /**
   * Demande à l'utilisateur de choisir une option
   */
  private async getUserChoice(): Promise<string> {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question('\n🎯 Choisissez une option (1-5): ', (answer: string) => {
        rl.close();
        resolve(answer.trim());
      });
    });
  }

  /**
   * Boucle principale du menu
   */
  async run(): Promise<void> {
    console.log('🚁 Initialisation de l\'intégration Vimana...');
    
    while (true) {
      this.displayMenu();
      
      const choice = await this.getUserChoice();
      
      switch (choice) {
        case '1':
          await this.runDemo();
          break;
          
        case '2':
          await this.runTests();
          break;
          
        case '3':
          await this.runCompleteWorkflow();
          break;
          
        case '4':
          this.displayInfo();
          break;
          
        case '5':
          console.log('\n🙏 AUM HANUMAN VIMANA DIVINE TECHNOLOGY NAMAHA');
          console.log('✨ Merci d\'avoir exploré l\'intégration spirituelle !');
          console.log('🚁🧠 À bientôt dans l\'écosystème conscient !\n');
          process.exit(0);
          break;
          
        default:
          console.log('\n❌ Option invalide. Veuillez choisir entre 1 et 5.');
          break;
      }
      
      // Pause avant de revenir au menu
      await this.waitForEnter();
    }
  }

  /**
   * Attend que l'utilisateur appuie sur Entrée
   */
  private async waitForEnter(): Promise<void> {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question('\n⏸️ Appuyez sur Entrée pour continuer...', () => {
        rl.close();
        resolve();
      });
    });
  }
}

/**
 * Point d'entrée principal
 */
async function main(): Promise<void> {
  try {
    const runner = new VimanaIntegrationRunner();
    await runner.run();
  } catch (error) {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  main().catch(console.error);
}

export { VimanaIntegrationRunner };
