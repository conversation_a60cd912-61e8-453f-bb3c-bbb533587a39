// ========================================
// HANUMAN SANDBOX ENHANCED - API SERVER
// API REST et WebSocket pour contrôle IDE
// ========================================

import express, { Express, Request, Response, NextFunction } from 'express';
import { createServer, Server } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import {
  SandboxAPIResponse,
  NaturalLanguageCommand,
  IDEAction,
  IDEActionResult,
  HanumanContext,
  OrchestrationError
} from '../orchestration/types';
import { IDEAgentOrchestrator } from '../orchestration/ide_agent_orchestrator';

/**
 * Configuration pour l'API Server Enhanced
 */
interface APIServerConfig {
  port: number;
  enableCors: boolean;
  enableRateLimit: boolean;
  enableWebSocket: boolean;
  enableSwagger: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  maxRequestSize: string;
  requestTimeout: number;
}

/**
 * Middleware d'authentification pour les agents
 */
interface AuthenticatedRequest extends Request {
  agentId?: string;
  organId?: string;
  context?: HanumanContext;
}

/**
 * API Server Enhanced pour la sandbox Hanuman
 * Expose les fonctionnalités d'orchestration IDE via REST et WebSocket
 */
export class SandboxAPIServerEnhanced {
  private app: Express;
  private server: Server;
  private io?: SocketIOServer;
  private config: APIServerConfig;
  private orchestrator: IDEAgentOrchestrator;
  private isRunning = false;

  constructor(orchestrator: IDEAgentOrchestrator, config?: Partial<APIServerConfig>) {
    this.orchestrator = orchestrator;

    this.config = {
      port: 8085,
      enableCors: true,
      enableRateLimit: true,
      enableWebSocket: true,
      enableSwagger: true,
      logLevel: 'info',
      maxRequestSize: '10mb',
      requestTimeout: 30000,
      ...config
    };

    this.app = express();
    this.server = createServer(this.app);

    this.setupMiddleware();
    this.setupRoutes();

    if (this.config.enableWebSocket) {
      this.setupWebSocket();
    }
  }

  /**
   * Configuration des middlewares
   */
  private setupMiddleware(): void {
    // Sécurité
    this.app.use(helmet());

    // CORS
    if (this.config.enableCors) {
      this.app.use(cors({
        origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
        credentials: true
      }));
    }

    // Rate limiting
    if (this.config.enableRateLimit) {
      const limiter = rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // limite chaque IP à 100 requêtes par fenêtre
        message: 'Trop de requêtes depuis cette IP'
      });
      this.app.use('/api/', limiter);
    }

    // Parsing
    this.app.use(express.json({ limit: this.config.maxRequestSize }));
    this.app.use(express.urlencoded({ extended: true, limit: this.config.maxRequestSize }));

    // Timeout
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      req.setTimeout(this.config.requestTimeout);
      next();
    });

    // Logging
    this.app.use(this.requestLogger.bind(this));

    // Authentification
    this.app.use('/api/', this.authenticateAgent.bind(this));
  }

  /**
   * Configuration des routes REST
   */
  private setupRoutes(): void {
    // Health check
    this.app.get('/health', this.handleHealthCheck.bind(this));
    this.app.get('/status', this.handleStatus.bind(this));

    // Routes principales IDE
    this.app.post('/api/ide/command', this.handleNaturalLanguageCommand.bind(this));
    this.app.post('/api/ide/action', this.handleDirectAction.bind(this));
    this.app.get('/api/ide/sessions', this.handleGetSessions.bind(this));
    this.app.get('/api/ide/history/:agentId', this.handleGetHistory.bind(this));

    // Routes VS Code
    this.app.post('/api/vscode/open', this.handleVSCodeOpen.bind(this));
    this.app.post('/api/vscode/command', this.handleVSCodeCommand.bind(this));
    this.app.post('/api/vscode/generate', this.handleVSCodeGenerate.bind(this));

    // Routes Roo Code
    this.app.post('/api/roo/generate', this.handleRooGenerate.bind(this));
    this.app.get('/api/roo/templates', this.handleRooTemplates.bind(this));
    this.app.post('/api/roo/template/:templateId', this.handleRooTemplate.bind(this));

    // Routes fichiers
    this.app.post('/api/files/read', this.handleFileRead.bind(this));
    this.app.post('/api/files/write', this.handleFileWrite.bind(this));
    this.app.post('/api/files/create', this.handleFileCreate.bind(this));

    // Routes projets
    this.app.post('/api/projects/create', this.handleProjectCreate.bind(this));
    this.app.get('/api/projects/list', this.handleProjectList.bind(this));

    // Routes métriques
    this.app.get('/api/metrics', this.handleMetrics.bind(this));
    this.app.get('/api/metrics/orchestrator', this.handleOrchestratorMetrics.bind(this));

    // ========================================
    // NOUVELLES ROUTES VIMANA
    // ========================================

    // Routes génération divine
    this.app.post('/api/vimana/divine-generation', this.handleDivineGeneration.bind(this));
    this.app.post('/api/vimana/divine-enhancement', this.handleDivineEnhancement.bind(this));
    this.app.get('/api/vimana/divine-agents', this.handleGetDivineAgents.bind(this));

    // Routes validation sacrée
    this.app.post('/api/vimana/sacred-validation', this.handleSacredValidation.bind(this));
    this.app.post('/api/vimana/sacred-geometry', this.handleSacredGeometry.bind(this));
    this.app.get('/api/vimana/cosmic-principles', this.handleGetCosmicPrinciples.bind(this));

    // Routes équilibrage Tri-Guna
    this.app.get('/api/vimana/triguna-balance', this.handleGetTriGunaBalance.bind(this));
    this.app.post('/api/vimana/triguna-balance', this.handleSetTriGunaBalance.bind(this));
    this.app.post('/api/vimana/triguna-optimize', this.handleOptimizeTriGuna.bind(this));

    // Routes mantras et bénédictions
    this.app.post('/api/vimana/invoke-blessing', this.handleInvokeBlessing.bind(this));
    this.app.get('/api/vimana/mantras', this.handleGetMantras.bind(this));
    this.app.post('/api/vimana/enhance-mantras', this.handleEnhanceMantras.bind(this));

    // Routes état spirituel
    this.app.get('/api/vimana/spiritual-state', this.handleGetSpiritualState.bind(this));
    this.app.get('/api/vimana/cosmic-alignment', this.handleGetCosmicAlignment.bind(this));
    this.app.post('/api/vimana/spiritual-command', this.handleSpiritualCommand.bind(this));

    // Routes templates divins
    this.app.get('/api/vimana/divine-templates', this.handleGetDivineTemplates.bind(this));
    this.app.post('/api/vimana/generate-template', this.handleGenerateDivineTemplate.bind(this));

    // Gestion des erreurs
    this.app.use(this.errorHandler.bind(this));
  }

  /**
   * Configuration WebSocket
   */
  private setupWebSocket(): void {
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
        credentials: true
      }
    });

    this.io.on('connection', (socket) => {
      this.log('info', `🔌 Nouvelle connexion WebSocket: ${socket.id}`);

      // Authentification WebSocket
      socket.on('authenticate', async (data) => {
        try {
          const { agentId, token } = data;
          // TODO: Valider le token
          socket.data.agentId = agentId;
          socket.join(`agent:${agentId}`);
          socket.emit('authenticated', { success: true, agentId });
          this.log('info', `✅ Agent ${agentId} authentifié via WebSocket`);
        } catch (error) {
          socket.emit('authentication_error', { error: error.message });
        }
      });

      // Commandes en temps réel
      socket.on('ide_command', async (data) => {
        try {
          const { command, sessionId } = data;
          const agentId = socket.data.agentId;

          if (!agentId) {
            socket.emit('error', { message: 'Agent non authentifié' });
            return;
          }

          const results = await this.orchestrator.processNaturalLanguageCommand(
            agentId,
            command,
            sessionId
          );

          socket.emit('command_result', { success: true, results });

          // Diffuser aux autres clients de l'agent
          socket.to(`agent:${agentId}`).emit('agent_activity', {
            type: 'command_executed',
            command,
            results
          });

        } catch (error) {
          socket.emit('command_error', { error: error.message });
        }
      });

      // Monitoring en temps réel
      socket.on('subscribe_metrics', () => {
        socket.join('metrics');
        this.log('debug', `📊 Client ${socket.id} abonné aux métriques`);
      });

      // ========================================
      // ÉVÉNEMENTS VIMANA WEBSOCKET
      // ========================================

      // Abonnement aux événements spirituels
      socket.on('subscribe_spiritual', () => {
        socket.join('spiritual');
        this.log('debug', `🕉️ Client ${socket.id} abonné aux événements spirituels`);
      });

      // Commandes divines en temps réel
      socket.on('divine_command', async (data) => {
        try {
          const { command, divineAgent, cosmicPrinciples } = data;
          const agentId = socket.data.agentId;

          if (!agentId) {
            socket.emit('error', { message: 'Agent non authentifié' });
            return;
          }

          this.log('info', `🕉️ Commande divine reçue: ${divineAgent} - "${command}"`);

          const results = await this.orchestrator.processNaturalLanguageCommand(
            agentId,
            command
          );

          socket.emit('divine_command_result', {
            success: true,
            results,
            divineAgent,
            cosmicPrinciples
          });

          // Diffuser aux autres clients spirituels
          socket.to('spiritual').emit('divine_activity', {
            type: 'divine_command_executed',
            agentId,
            command,
            divineAgent,
            results
          });

        } catch (error) {
          socket.emit('divine_command_error', { error: error.message });
        }
      });

      // Invocation de bénédictions
      socket.on('invoke_blessing', async (data) => {
        try {
          const { mantra, intention } = data;
          const agentId = socket.data.agentId;

          if (!agentId) {
            socket.emit('error', { message: 'Agent non authentifié' });
            return;
          }

          await this.orchestrator.invokeBlessing(mantra);

          const blessing = {
            mantra,
            intention,
            agentId,
            timestamp: new Date(),
            spiritualPower: 95
          };

          socket.emit('blessing_invoked', blessing);

          // Diffuser la bénédiction à tous les clients spirituels
          socket.to('spiritual').emit('blessing_shared', blessing);

        } catch (error) {
          socket.emit('blessing_error', { error: error.message });
        }
      });

      // Demande d'état spirituel en temps réel
      socket.on('get_spiritual_state', async () => {
        try {
          const vimanaState = this.orchestrator.getVimanaState();
          socket.emit('spiritual_state_update', vimanaState);
        } catch (error) {
          socket.emit('spiritual_state_error', { error: error.message });
        }
      });

      // Validation sacrée en temps réel
      socket.on('sacred_validation', async (data) => {
        try {
          const { code, validationType } = data;
          const agentId = socket.data.agentId;

          if (!agentId) {
            socket.emit('error', { message: 'Agent non authentifié' });
            return;
          }

          // Simuler la validation (TODO: implémenter via orchestrateur)
          const validation = {
            spiritualScore: 88,
            goldenRatioCompliance: 85.7,
            fibonacciAlignment: true,
            cosmicFrequency: 432.1,
            passed: true
          };

          socket.emit('sacred_validation_result', validation);

        } catch (error) {
          socket.emit('sacred_validation_error', { error: error.message });
        }
      });

      socket.on('disconnect', () => {
        this.log('info', `🔌 Déconnexion WebSocket: ${socket.id}`);
      });
    });

    // Diffusion périodique des métriques
    setInterval(() => {
      if (this.io) {
        const metrics = this.orchestrator.getMetrics();
        this.io.to('metrics').emit('metrics_update', metrics);
      }
    }, 5000);

    // Diffusion périodique des métriques spirituelles
    setInterval(() => {
      if (this.io) {
        const vimanaState = this.orchestrator.getVimanaState();
        if (vimanaState) {
          this.io.to('spiritual').emit('spiritual_metrics_update', {
            cosmicAlignment: vimanaState.cosmicAlignment,
            spiritualQuality: vimanaState.spiritualQuality,
            trigunaBalance: vimanaState.trigunaBalance,
            mantrasActive: vimanaState.mantrasActive,
            divineAgents: vimanaState.divineAgents,
            timestamp: new Date()
          });
        }
      }
    }, 10000); // Toutes les 10 secondes pour les métriques spirituelles
  }

  /**
   * Handlers des routes REST
   */

  private async handleHealthCheck(req: Request, res: Response): Promise<void> {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      orchestrator: this.orchestrator.getState().status,
      uptime: process.uptime()
    };

    res.json(this.createResponse(health));
  }

  private async handleStatus(req: Request, res: Response): Promise<void> {
    const status = {
      server: {
        running: this.isRunning,
        port: this.config.port,
        websocket: this.config.enableWebSocket
      },
      orchestrator: this.orchestrator.getState(),
      metrics: this.orchestrator.getMetrics()
    };

    res.json(this.createResponse(status));
  }

  private async handleNaturalLanguageCommand(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { command, sessionId } = req.body;
      const agentId = req.agentId!;

      this.log('info', `🗣️ Commande reçue de ${agentId}: "${command}"`);

      const results = await this.orchestrator.processNaturalLanguageCommand(
        agentId,
        command,
        sessionId
      );

      // Notifier via WebSocket si disponible
      if (this.io) {
        this.io.to(`agent:${agentId}`).emit('command_completed', {
          command,
          results,
          timestamp: new Date()
        });
      }

      res.json(this.createResponse(results));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleDirectAction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const action: IDEAction = req.body;
      const agentId = req.agentId!;

      this.log('info', `⚡ Action directe de ${agentId}: ${action.type}`);

      // TODO: Exécuter l'action directement via le contrôleur
      const result: IDEActionResult = {
        actionId: `direct_${Date.now()}`,
        success: true,
        result: `Action ${action.type} simulée`,
        duration: 1000,
        timestamp: new Date()
      };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleGetSessions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const sessions = this.orchestrator.getState().activeSessions;
      const sessionArray = Array.from(sessions.values());

      res.json(this.createResponse(sessionArray));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleGetHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { agentId } = req.params;
      const history = this.orchestrator.getCommandHistory(agentId);

      res.json(this.createResponse(history));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleVSCodeOpen(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { filePath, focus } = req.body;

      // TODO: Implémenter ouverture VS Code
      const result = { opened: true, filePath, focus };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleVSCodeCommand(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { command, params } = req.body;

      // TODO: Implémenter commande VS Code
      const result = { executed: true, command, params };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleVSCodeGenerate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { template, variables, outputPath } = req.body;

      // TODO: Implémenter génération VS Code
      const result = { generated: true, template, outputPath };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleRooGenerate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { prompt, context, template } = req.body;

      // TODO: Implémenter génération Roo Code
      const result = {
        generated: true,
        code: `// Code généré pour: ${prompt}`,
        length: 100
      };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleRooTemplates(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // TODO: Récupérer les templates Roo Code
      const templates = [
        { id: 'hanuman-agent', name: 'Agent Hanuman', category: 'agent' },
        { id: 'hanuman-interface', name: 'Interface React', category: 'interface' },
        { id: 'hanuman-service', name: 'Service API', category: 'service' }
      ];

      res.json(this.createResponse({ templates, count: templates.length }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleRooTemplate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { templateId } = req.params;
      const variables = req.body;

      // TODO: Utiliser template spécifique
      const result = {
        templateId,
        variables,
        generated: true
      };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleFileRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { path } = req.body;

      // TODO: Lire fichier via système sécurisé
      const content = `// Contenu simulé du fichier: ${path}`;

      res.json(this.createResponse({ path, content, size: content.length }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleFileWrite(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { path, content } = req.body;

      // TODO: Écrire fichier via système sécurisé
      const result = { path, written: true, size: content.length };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleFileCreate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { path, content, type } = req.body;

      // TODO: Créer fichier
      const result = { path, created: true, type };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleProjectCreate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { projectName, projectType, technologies } = req.body;

      // TODO: Créer projet via orchestrateur
      const result = {
        projectName,
        projectType,
        technologies,
        created: true
      };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleProjectList(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // TODO: Lister projets
      const projects = [
        { name: 'SampleAgent', type: 'agent', status: 'active' },
        { name: 'TestInterface', type: 'interface', status: 'development' }
      ];

      res.json(this.createResponse({ projects, count: projects.length }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const metrics = {
        orchestrator: this.orchestrator.getMetrics(),
        server: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          connections: this.io?.engine.clientsCount || 0
        }
      };

      res.json(this.createResponse(metrics));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleOrchestratorMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const metrics = this.orchestrator.getMetrics();
      res.json(this.createResponse(metrics));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  // ========================================
  // HANDLERS VIMANA
  // ========================================

  /**
   * Génération divine de code
   */
  private async handleDivineGeneration(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { agentId, command, divineAgent, cosmicPrinciples, context } = req.body;

      this.log('info', `🕉️ Génération divine demandée: ${divineAgent} pour "${command}"`);

      // Utiliser l'orchestrateur pour la génération divine
      const results = await this.orchestrator.processNaturalLanguageCommand(
        agentId || req.agentId!,
        command
      );

      // Filtrer les résultats divins
      const divineResults = results.filter(r =>
        r.metadata?.spiritualQuality ||
        r.actionId.includes('divine')
      );

      // Notifier via WebSocket
      if (this.io) {
        this.io.to(`agent:${agentId || req.agentId!}`).emit('divine_generation_completed', {
          command,
          results: divineResults,
          timestamp: new Date()
        });
      }

      res.json(this.createResponse({
        success: true,
        results: divineResults,
        totalActions: results.length,
        divineActions: divineResults.length
      }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Enhancement divin de code existant
   */
  private async handleDivineEnhancement(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { code, enhancementType, spiritualContext } = req.body;

      this.log('info', `✨ Enhancement divin demandé: ${enhancementType}`);

      // Obtenir l'état Vimana
      const vimanaState = this.orchestrator.getVimanaState();
      if (!vimanaState) {
        throw new Error('Vimana non disponible');
      }

      // Simuler l'enhancement (TODO: implémenter via orchestrateur)
      const enhancedCode = `// AUM NAMAHA SHIVAYA - Code Enhanced\n${code}\n// Bénédiction divine appliquée`;

      const result = {
        originalCode: code,
        enhancedCode,
        enhancementType,
        spiritualQuality: 85,
        mantrasAdded: ['AUM NAMAHA SHIVAYA'],
        blessings: ['Que ce code soit béni et fonctionnel']
      };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Liste des agents divins disponibles
   */
  private async handleGetDivineAgents(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const divineAgents = [
        {
          id: 'brahma',
          name: 'Brahma le Créateur',
          mantra: 'AUM BRAHMAYE NAMAHA',
          focus: ['création', 'innovation', 'architecture'],
          cosmicPhase: 'creation',
          active: true
        },
        {
          id: 'vishnu',
          name: 'Vishnu le Préservateur',
          mantra: 'AUM VISHNAVE NAMAHA',
          focus: ['maintenance', 'stabilité', 'optimisation'],
          cosmicPhase: 'preservation',
          active: true
        },
        {
          id: 'shiva',
          name: 'Shiva le Transformateur',
          mantra: 'AUM SHIVAYA NAMAHA',
          focus: ['refactoring', 'transformation', 'destruction créative'],
          cosmicPhase: 'transformation',
          active: true
        }
      ];

      res.json(this.createResponse({ agents: divineAgents, count: divineAgents.length }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Validation sacrée de code
   */
  private async handleSacredValidation(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { code, validationType = 'full', spiritualQualityThreshold = 80 } = req.body;

      this.log('info', `📐 Validation sacrée demandée: ${validationType}`);

      // Simuler la validation (TODO: implémenter via orchestrateur)
      const validation = {
        spiritualScore: 88,
        goldenRatioCompliance: 85.7,
        fibonacciAlignment: true,
        cosmicFrequency: 432.1,
        cosmicResonance: {
          frequency: 432.1,
          harmony: 90,
          omAlignment: true,
          sacredAlignment: true
        },
        geometricPatterns: {
          spirals: 3,
          symmetries: 5,
          fractals: 2
        },
        passed: true,
        recommendations: [
          'Ajouter plus de commentaires spirituels',
          'Optimiser la structure Fibonacci'
        ]
      };

      // Notifier via WebSocket
      if (this.io) {
        this.io.to(`agent:${req.agentId!}`).emit('sacred_validation_completed', validation);
      }

      res.json(this.createResponse(validation));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Analyse géométrie sacrée
   */
  private async handleSacredGeometry(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { code } = req.body;

      this.log('info', '📐 Analyse géométrie sacrée demandée');

      // Simuler l'analyse (TODO: implémenter via orchestrateur)
      const geometry = {
        goldenRatio: {
          compliance: 85.7,
          idealRatio: 1.618033988749895,
          actualRatio: 1.62,
          deviation: 0.002
        },
        fibonacci: {
          alignment: true,
          sequences: [1, 1, 2, 3, 5, 8, 13, 21],
          foundInCode: [1, 2, 3, 5, 8]
        },
        sacredNumbers: {
          found: [432, 136.1, 1.618],
          missing: [108, 528, 741],
          score: 75
        },
        cosmicFrequency: 432.1,
        spiritualScore: 88
      };

      res.json(this.createResponse(geometry));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Principes cosmiques disponibles
   */
  private async handleGetCosmicPrinciples(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const principles = {
        goldenRatio: {
          value: 1.618033988749895,
          symbol: 'φ',
          description: 'Proportion divine pour harmonie parfaite'
        },
        sacredFrequencies: {
          cosmic: 432,
          om: 136.1,
          healing: 528,
          transformation: 741,
          intuition: 852,
          spiritual: 963
        },
        fibonacciSequence: [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144],
        sacredNumbers: [108, 432, 136.1, 528, 741, 852, 963],
        trigunaBalance: {
          sattva: { ideal: 40, description: 'Pureté, stabilité, harmonie' },
          rajas: { ideal: 35, description: 'Passion, action, création' },
          tamas: { ideal: 25, description: 'Transformation, destruction nécessaire' }
        }
      };

      res.json(this.createResponse(principles));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Obtenir l'équilibrage Tri-Guna actuel
   */
  private async handleGetTriGunaBalance(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      this.log('info', '⚖️ État Tri-Guna demandé');

      const vimanaState = this.orchestrator.getVimanaState();
      const balance = vimanaState?.trigunaBalance || {
        sattva: 40,
        rajas: 35,
        tamas: 25,
        harmony: 85,
        recommendation: 'Équilibre harmonieux maintenu'
      };

      res.json(this.createResponse(balance));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Définir un nouvel équilibrage Tri-Guna
   */
  private async handleSetTriGunaBalance(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { workload, targetBalance } = req.body;

      this.log('info', '⚖️ Équilibrage Tri-Guna demandé');

      // Simuler l'équilibrage (TODO: implémenter via orchestrateur)
      const newBalance = {
        sattva: targetBalance?.sattva || 40,
        rajas: targetBalance?.rajas || 35,
        tamas: targetBalance?.tamas || 25,
        harmony: 90,
        recommendation: 'Nouvel équilibre appliqué avec succès',
        workloadAnalysis: {
          totalTasks: workload?.length || 0,
          distribution: 'Optimisée selon principes cosmiques'
        }
      };

      // Notifier via WebSocket
      if (this.io) {
        this.io.to(`agent:${req.agentId!}`).emit('triguna_balance_updated', newBalance);
      }

      res.json(this.createResponse(newBalance));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Optimiser automatiquement Tri-Guna
   */
  private async handleOptimizeTriGuna(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { currentWorkload, preferences } = req.body;

      this.log('info', '🎯 Optimisation Tri-Guna demandée');

      // Simuler l'optimisation
      const optimization = {
        before: { sattva: 30, rajas: 50, tamas: 20, harmony: 70 },
        after: { sattva: 40, rajas: 35, tamas: 25, harmony: 92 },
        improvements: [
          'Réduction de Rajas pour plus de stabilité',
          'Augmentation de Tamas pour transformation nécessaire',
          'Équilibrage optimal atteint'
        ],
        recommendations: [
          'Maintenir cet équilibre pendant 7 jours',
          'Surveiller les métriques de performance',
          'Ajuster selon les phases lunaires'
        ]
      };

      res.json(this.createResponse(optimization));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Invoquer une bénédiction
   */
  private async handleInvokeBlessing(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { mantra, intention, duration } = req.body;

      this.log('info', `🙏 Bénédiction invoquée: ${mantra}`);

      // Invoquer via orchestrateur
      await this.orchestrator.invokeBlessing(mantra);

      const blessing = {
        mantra,
        intention: intention || 'Bénédiction générale',
        duration: duration || 'Permanent',
        invocationTime: new Date(),
        spiritualPower: 95,
        cosmicAlignment: '+5%',
        effects: [
          'Augmentation de la qualité spirituelle',
          'Amélioration de l\'harmonie cosmique',
          'Protection divine activée'
        ]
      };

      // Notifier via WebSocket
      if (this.io) {
        this.io.to(`agent:${req.agentId!}`).emit('blessing_invoked', blessing);
      }

      res.json(this.createResponse(blessing));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Obtenir la liste des mantras disponibles
   */
  private async handleGetMantras(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const mantras = {
        creation: [
          { mantra: 'AUM BRAHMAYE NAMAHA', purpose: 'Création divine', agent: 'brahma' },
          { mantra: 'AUM GANESHA NAMAHA', purpose: 'Remover obstacles', agent: 'ganesha' },
          { mantra: 'AUM SARASWATI NAMAHA', purpose: 'Sagesse et connaissance', agent: 'saraswati' }
        ],
        preservation: [
          { mantra: 'AUM VISHNAVE NAMAHA', purpose: 'Préservation divine', agent: 'vishnu' },
          { mantra: 'AUM LAKSHMI NAMAHA', purpose: 'Prospérité', agent: 'lakshmi' },
          { mantra: 'AUM NARAYANA NAMAHA', purpose: 'Protection', agent: 'narayana' }
        ],
        transformation: [
          { mantra: 'AUM SHIVAYA NAMAHA', purpose: 'Transformation divine', agent: 'shiva' },
          { mantra: 'AUM RUDRA NAMAHA', purpose: 'Destruction créative', agent: 'rudra' },
          { mantra: 'AUM MAHADEV NAMAHA', purpose: 'Évolution spirituelle', agent: 'mahadev' }
        ],
        universal: [
          { mantra: 'AUM', purpose: 'Son primordial', agent: 'universal' },
          { mantra: 'AUM MANI PADME HUM', purpose: 'Compassion universelle', agent: 'avalokiteshvara' },
          { mantra: 'GATE GATE PARAGATE PARASAMGATE BODHI SVAHA', purpose: 'Éveil', agent: 'buddha' }
        ]
      };

      res.json(this.createResponse(mantras));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Améliorer le code avec des mantras
   */
  private async handleEnhanceMantras(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { code, mantrasType, spiritualLevel } = req.body;

      this.log('info', `🕉️ Enhancement mantras demandé: ${mantrasType}`);

      // Simuler l'enhancement
      const enhancement = {
        originalCode: code,
        enhancedCode: `// AUM NAMAHA SHIVAYA\n${code}\n// Bénédiction divine intégrée`,
        mantrasAdded: ['AUM NAMAHA SHIVAYA', 'AUM GANESHA NAMAHA'],
        spiritualComments: [
          '// Intention spirituelle: Élévation du code',
          '// Phase cosmique: Création divine',
          '// Chakra focus: Cœur (harmonie)'
        ],
        spiritualQuality: {
          before: 60,
          after: 88,
          improvement: '+28 points'
        },
        blessings: [
          'Que ce code soit stable et harmonieux',
          'Que les bugs soient transformés en sagesse',
          'Que les utilisateurs trouvent la paix'
        ]
      };

      res.json(this.createResponse(enhancement));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Obtenir l'état spirituel global
   */
  private async handleGetSpiritualState(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const vimanaState = this.orchestrator.getVimanaState();

      const spiritualState = {
        status: vimanaState?.status || 'active',
        cosmicAlignment: vimanaState?.cosmicAlignment || 85,
        spiritualQuality: vimanaState?.spiritualQuality || 80,
        divineAgents: vimanaState?.divineAgents || {
          brahma: { active: true, invocations: 15 },
          vishnu: { active: true, invocations: 12 },
          shiva: { active: true, invocations: 8 }
        },
        mantrasActive: vimanaState?.mantrasActive || ['AUM NAMAHA SHIVAYA'],
        lastBlessing: vimanaState?.lastBlessing || new Date(),
        cosmicCycles: vimanaState?.cosmicCycles || {
          creation: 15,
          preservation: 12,
          transformation: 8
        },
        recommendations: [
          'Maintenir l\'équilibre Tri-Guna',
          'Invoquer des bénédictions régulières',
          'Surveiller l\'alignement cosmique'
        ]
      };

      res.json(this.createResponse(spiritualState));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Obtenir l'alignement cosmique
   */
  private async handleGetCosmicAlignment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const vimanaState = this.orchestrator.getVimanaState();

      const alignment = {
        current: vimanaState?.cosmicAlignment || 85,
        target: 100,
        factors: {
          codeQuality: 88,
          spiritualPractices: 82,
          teamHarmony: 90,
          cosmicTiming: 85
        },
        improvements: [
          { factor: 'codeQuality', suggestion: 'Ajouter plus de géométrie sacrée' },
          { factor: 'spiritualPractices', suggestion: 'Invoquer plus de mantras' },
          { factor: 'cosmicTiming', suggestion: 'Synchroniser avec phases lunaires' }
        ],
        nextOptimalTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Demain
        lunarPhase: 'Croissante',
        vedicTiming: 'Auspicieux'
      };

      res.json(this.createResponse(alignment));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Traiter une commande spirituelle spécialisée
   */
  private async handleSpiritualCommand(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { command, spiritualContext, divineAgent } = req.body;

      this.log('info', `🕉️ Commande spirituelle: "${command}"`);

      // Traiter via orchestrateur avec contexte spirituel
      const results = await this.orchestrator.processNaturalLanguageCommand(
        req.agentId!,
        command
      );

      // Enrichir avec contexte spirituel
      const spiritualResults = results.map(r => ({
        ...r,
        spiritualContext,
        divineAgent,
        cosmicTimestamp: new Date(),
        blessings: ['Que cette action soit bénie et fructueuse']
      }));

      res.json(this.createResponse({
        command,
        results: spiritualResults,
        spiritualPower: 95,
        cosmicAlignment: '+3%'
      }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Obtenir les templates divins disponibles
   */
  private async handleGetDivineTemplates(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const templates = [
        {
          id: 'brahma-creation',
          name: 'Template Création Brahma',
          divineAgent: 'brahma',
          category: 'creation',
          spiritualLevel: 'advanced',
          cosmicPrinciples: ['goldenRatio', 'sacredGeometry', 'mantras']
        },
        {
          id: 'vishnu-preservation',
          name: 'Template Préservation Vishnu',
          divineAgent: 'vishnu',
          category: 'preservation',
          spiritualLevel: 'intermediate',
          cosmicPrinciples: ['fibonacci', 'stability', 'harmony']
        },
        {
          id: 'shiva-transformation',
          name: 'Template Transformation Shiva',
          divineAgent: 'shiva',
          category: 'transformation',
          spiritualLevel: 'expert',
          cosmicPrinciples: ['transformation', 'renewal', 'power']
        }
      ];

      res.json(this.createResponse({ templates, count: templates.length }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Générer un template divin personnalisé
   */
  private async handleGenerateDivineTemplate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { templateType, agentType, spiritualLevel, cosmicPrinciples } = req.body;

      this.log('info', `🎭 Génération template divin: ${templateType} pour ${agentType}`);

      // Simuler la génération (TODO: implémenter via orchestrateur)
      const template = {
        id: `${templateType}-${agentType}-${Date.now()}`,
        name: `Template ${templateType} pour ${agentType}`,
        code: `// AUM BRAHMAYE NAMAHA - Template Divin
export class Divine${agentType}Template {
  private readonly goldenRatio = 1.618033988749895;
  private readonly cosmicFrequency = 432;

  constructor() {
    this.invokeBlessing();
  }

  private invokeBlessing(): void {
    console.log('🙏 Template béni et activé');
  }
}`,
        spiritualQuality: 92,
        cosmicPrinciples,
        blessings: ['Que ce template soit source de création divine'],
        generatedAt: new Date()
      };

      res.json(this.createResponse(template));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  // Méthodes utilitaires

  private async authenticateAgent(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      const agentId = req.headers['x-agent-id'] as string;

      if (!agentId) {
        res.status(401).json(this.createResponse(null, 'Agent ID requis', 'MISSING_AGENT_ID'));
        return;
      }

      // TODO: Valider le token d'authentification
      // if (!authHeader || !this.validateToken(authHeader)) {
      //   res.status(401).json(this.createResponse(null, 'Token invalide', 'INVALID_TOKEN'));
      //   return;
      // }

      req.agentId = agentId;
      req.context = await this.getAgentContext(agentId);

      next();

    } catch (error) {
      res.status(401).json(this.createResponse(null, 'Erreur authentification', 'AUTH_ERROR'));
    }
  }

  private async getAgentContext(agentId: string): Promise<HanumanContext> {
    // TODO: Récupérer contexte depuis l'orchestrateur
    return {
      agentId,
      projectType: 'agent',
      architecture: 'hanuman-biomimetic-enhanced',
      workspaceDir: '/workspace',
      developmentPhase: 'development',
      technologies: ['typescript', 'react', 'node.js'],
      requirements: []
    };
  }

  private requestLogger(req: Request, res: Response, next: NextFunction): void {
    const start = Date.now();

    res.on('finish', () => {
      const duration = Date.now() - start;
      this.log('info', `${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`);
    });

    next();
  }

  private errorHandler(error: Error, req: Request, res: Response, next: NextFunction): void {
    this.log('error', 'Erreur API:', error);

    if (error instanceof OrchestrationError) {
      res.status(400).json(this.createResponse(null, error.message, error.code));
    } else {
      res.status(500).json(this.createResponse(null, 'Erreur interne du serveur', 'INTERNAL_ERROR'));
    }
  }

  private handleError(error: any, res: Response): void {
    this.log('error', 'Erreur handler:', error);

    if (error instanceof OrchestrationError) {
      res.status(400).json(this.createResponse(null, error.message, error.code));
    } else {
      res.status(500).json(this.createResponse(null, error.message || 'Erreur interne', 'HANDLER_ERROR'));
    }
  }

  private createResponse<T>(data: T, error?: string, code?: string): SandboxAPIResponse<T> {
    return {
      success: !error,
      data: error ? undefined : data,
      error: error ? { code: code || 'UNKNOWN_ERROR', message: error } : undefined,
      metadata: {
        requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        processingTime: 0 // TODO: Calculer le temps réel
      }
    };
  }

  private log(level: string, message: string, data?: any): void {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    if (levels[level] >= levels[this.config.logLevel]) {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] [API] [${level.toUpperCase()}] ${message}`, data || '');
    }
  }

  // Méthodes publiques

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server.listen(this.config.port, () => {
          this.isRunning = true;
          this.log('info', `🚀 API Server Enhanced démarré sur le port ${this.config.port}`);

          if (this.config.enableWebSocket) {
            this.log('info', `🔌 WebSocket activé sur le port ${this.config.port}`);
          }

          resolve();
        });

        this.server.on('error', (error) => {
          this.log('error', 'Erreur serveur:', error);
          reject(error);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.io) {
        this.io.close();
      }

      this.server.close(() => {
        this.isRunning = false;
        this.log('info', '🛑 API Server Enhanced arrêté');
        resolve();
      });
    });
  }

  getConfig(): APIServerConfig {
    return { ...this.config };
  }

  isServerRunning(): boolean {
    return this.isRunning;
  }
}
