// ========================================
// TEST API SERVER ENHANCED AVEC VIMANA
// Tests pour l'API server avec nouvelles routes spirituelles
// ========================================

import axios from 'axios';
import { io, Socket } from 'socket.io-client';

/**
 * Tests pour l'API Server Enhanced avec Vimana
 */
export class APIServerEnhancedTests {
  private baseURL: string;
  private socket: Socket | null = null;
  private agentId = 'test-agent-vimana-001';

  constructor(port: number = 8085) {
    this.baseURL = `http://localhost:${port}`;
  }

  /**
   * Exécute tous les tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 TESTS API SERVER ENHANCED AVEC VIMANA');
    console.log('='.repeat(50));

    try {
      await this.testHealthAndStatus();
      await this.testVimanaRoutes();
      await this.testWebSocketVimana();
      await this.testSpiritualWorkflow();

      console.log('\n✅ TOUS LES TESTS API PASSÉS !');
      console.log('🎉 L\'API Server Enhanced avec Vimana fonctionne parfaitement');

    } catch (error) {
      console.error('❌ ÉCHEC DES TESTS API:', error);
      throw error;
    } finally {
      if (this.socket) {
        this.socket.disconnect();
      }
    }
  }

  /**
   * Test de santé et statut
   */
  private async testHealthAndStatus(): Promise<void> {
    console.log('\n🔧 Test 1: Health check et statut');
    
    // Test health check
    const healthResponse = await axios.get(`${this.baseURL}/health`);
    if (healthResponse.status !== 200) {
      throw new Error(`Health check échoué: ${healthResponse.status}`);
    }
    
    console.log('✅ Health check OK');
    
    // Test status
    const statusResponse = await axios.get(`${this.baseURL}/status`);
    if (statusResponse.status !== 200) {
      throw new Error(`Status check échoué: ${statusResponse.status}`);
    }
    
    console.log('✅ Status check OK');
    console.log(`   Orchestrateur: ${statusResponse.data.data.orchestrator.status}`);
  }

  /**
   * Test des routes Vimana
   */
  private async testVimanaRoutes(): Promise<void> {
    console.log('\n🚁 Test 2: Routes Vimana');
    
    const headers = {
      'x-agent-id': this.agentId,
      'Content-Type': 'application/json'
    };

    // Test génération divine
    console.log('   Test génération divine...');
    const divineGenResponse = await axios.post(
      `${this.baseURL}/api/vimana/divine-generation`,
      {
        agentId: this.agentId,
        command: 'créer un agent frontend divin avec bénédiction',
        divineAgent: 'brahma',
        cosmicPrinciples: {
          goldenRatio: true,
          mantras: true,
          sacredGeometry: true
        }
      },
      { headers }
    );
    
    if (!divineGenResponse.data.success) {
      throw new Error('Génération divine échouée');
    }
    console.log('   ✅ Génération divine OK');

    // Test agents divins
    console.log('   Test liste agents divins...');
    const agentsResponse = await axios.get(
      `${this.baseURL}/api/vimana/divine-agents`,
      { headers }
    );
    
    if (agentsResponse.data.data.count !== 3) {
      throw new Error(`Nombre d'agents divins incorrect: ${agentsResponse.data.data.count}`);
    }
    console.log('   ✅ Agents divins OK (3 agents)');

    // Test validation sacrée
    console.log('   Test validation sacrée...');
    const validationResponse = await axios.post(
      `${this.baseURL}/api/vimana/sacred-validation`,
      {
        code: 'export class TestClass { private phi = 1.618; }',
        validationType: 'full'
      },
      { headers }
    );
    
    if (!validationResponse.data.data.passed) {
      throw new Error('Validation sacrée échouée');
    }
    console.log('   ✅ Validation sacrée OK');

    // Test Tri-Guna balance
    console.log('   Test équilibrage Tri-Guna...');
    const balanceResponse = await axios.get(
      `${this.baseURL}/api/vimana/triguna-balance`,
      { headers }
    );
    
    const balance = balanceResponse.data.data;
    if (balance.sattva + balance.rajas + balance.tamas !== 100) {
      throw new Error('Balance Tri-Guna incorrecte');
    }
    console.log('   ✅ Tri-Guna balance OK');

    // Test mantras
    console.log('   Test mantras...');
    const mantrasResponse = await axios.get(
      `${this.baseURL}/api/vimana/mantras`,
      { headers }
    );
    
    const mantras = mantrasResponse.data.data;
    if (!mantras.creation || !mantras.preservation || !mantras.transformation) {
      throw new Error('Mantras incomplets');
    }
    console.log('   ✅ Mantras OK');

    // Test invocation bénédiction
    console.log('   Test invocation bénédiction...');
    const blessingResponse = await axios.post(
      `${this.baseURL}/api/vimana/invoke-blessing`,
      {
        mantra: 'AUM NAMAHA SHIVAYA',
        intention: 'Test de bénédiction',
        duration: 'Temporaire'
      },
      { headers }
    );
    
    if (blessingResponse.data.data.spiritualPower < 90) {
      throw new Error('Puissance spirituelle insuffisante');
    }
    console.log('   ✅ Bénédiction OK');

    // Test état spirituel
    console.log('   Test état spirituel...');
    const spiritualResponse = await axios.get(
      `${this.baseURL}/api/vimana/spiritual-state`,
      { headers }
    );
    
    const spiritual = spiritualResponse.data.data;
    if (!spiritual.divineAgents || spiritual.cosmicAlignment < 50) {
      throw new Error('État spirituel insuffisant');
    }
    console.log('   ✅ État spirituel OK');
  }

  /**
   * Test WebSocket avec Vimana
   */
  private async testWebSocketVimana(): Promise<void> {
    console.log('\n🔌 Test 3: WebSocket Vimana');
    
    return new Promise((resolve, reject) => {
      this.socket = io(this.baseURL);
      let testsCompleted = 0;
      const totalTests = 4;

      this.socket.on('connect', () => {
        console.log('   Connexion WebSocket établie');
        
        // Authentification
        this.socket!.emit('authenticate', {
          agentId: this.agentId,
          token: 'test-token'
        });
      });

      this.socket.on('authenticated', () => {
        console.log('   ✅ Authentification WebSocket OK');
        
        // S'abonner aux événements spirituels
        this.socket!.emit('subscribe_spiritual');
        
        // Test commande divine
        this.socket!.emit('divine_command', {
          command: 'créer un composant divin',
          divineAgent: 'brahma',
          cosmicPrinciples: { goldenRatio: true }
        });
      });

      this.socket.on('divine_command_result', (data) => {
        if (data.success) {
          console.log('   ✅ Commande divine WebSocket OK');
          testsCompleted++;
        }
        
        // Test invocation bénédiction
        this.socket!.emit('invoke_blessing', {
          mantra: 'AUM GANESHA NAMAHA',
          intention: 'Test WebSocket'
        });
      });

      this.socket.on('blessing_invoked', (data) => {
        if (data.spiritualPower > 90) {
          console.log('   ✅ Bénédiction WebSocket OK');
          testsCompleted++;
        }
        
        // Test état spirituel
        this.socket!.emit('get_spiritual_state');
      });

      this.socket.on('spiritual_state_update', (data) => {
        if (data && data.cosmicAlignment) {
          console.log('   ✅ État spirituel WebSocket OK');
          testsCompleted++;
        }
        
        // Test validation sacrée
        this.socket!.emit('sacred_validation', {
          code: 'const phi = 1.618;',
          validationType: 'quick'
        });
      });

      this.socket.on('sacred_validation_result', (data) => {
        if (data.spiritualScore > 80) {
          console.log('   ✅ Validation sacrée WebSocket OK');
          testsCompleted++;
        }
        
        if (testsCompleted === totalTests) {
          resolve();
        }
      });

      this.socket.on('error', (error) => {
        reject(new Error(`Erreur WebSocket: ${error.message}`));
      });

      // Timeout de sécurité
      setTimeout(() => {
        if (testsCompleted < totalTests) {
          reject(new Error(`Tests WebSocket incomplets: ${testsCompleted}/${totalTests}`));
        }
      }, 10000);
    });
  }

  /**
   * Test d'un workflow spirituel complet
   */
  private async testSpiritualWorkflow(): Promise<void> {
    console.log('\n🌟 Test 4: Workflow spirituel complet');
    
    const headers = {
      'x-agent-id': this.agentId,
      'Content-Type': 'application/json'
    };

    // Étape 1: Obtenir les principes cosmiques
    console.log('   Étape 1: Principes cosmiques...');
    const principlesResponse = await axios.get(
      `${this.baseURL}/api/vimana/cosmic-principles`,
      { headers }
    );
    
    if (!principlesResponse.data.data.goldenRatio) {
      throw new Error('Principes cosmiques manquants');
    }

    // Étape 2: Générer un template divin
    console.log('   Étape 2: Template divin...');
    const templateResponse = await axios.post(
      `${this.baseURL}/api/vimana/generate-template`,
      {
        templateType: 'brahma-creation',
        agentType: 'Frontend',
        spiritualLevel: 'advanced',
        cosmicPrinciples: ['goldenRatio', 'mantras', 'sacredGeometry']
      },
      { headers }
    );
    
    if (templateResponse.data.data.spiritualQuality < 90) {
      throw new Error('Qualité spirituelle du template insuffisante');
    }

    // Étape 3: Valider le code généré
    console.log('   Étape 3: Validation du code...');
    const codeValidationResponse = await axios.post(
      `${this.baseURL}/api/vimana/sacred-geometry`,
      {
        code: templateResponse.data.data.code
      },
      { headers }
    );
    
    if (codeValidationResponse.data.data.spiritualScore < 80) {
      throw new Error('Score spirituel insuffisant');
    }

    // Étape 4: Optimiser Tri-Guna
    console.log('   Étape 4: Optimisation Tri-Guna...');
    const optimizeResponse = await axios.post(
      `${this.baseURL}/api/vimana/triguna-optimize`,
      {
        currentWorkload: [
          { type: 'create', name: 'Template généré' },
          { type: 'validate', name: 'Validation sacrée' }
        ],
        preferences: { harmony: 'maximum' }
      },
      { headers }
    );
    
    if (optimizeResponse.data.data.after.harmony < 90) {
      throw new Error('Optimisation Tri-Guna insuffisante');
    }

    // Étape 5: Bénédiction finale
    console.log('   Étape 5: Bénédiction finale...');
    const finalBlessingResponse = await axios.post(
      `${this.baseURL}/api/vimana/invoke-blessing`,
      {
        mantra: 'AUM HANUMAN VIMANA DIVINE TECHNOLOGY NAMAHA',
        intention: 'Finalisation du workflow spirituel',
        duration: 'Permanent'
      },
      { headers }
    );
    
    if (finalBlessingResponse.data.data.spiritualPower < 95) {
      throw new Error('Puissance spirituelle finale insuffisante');
    }

    console.log('   ✅ Workflow spirituel complet réussi !');
    console.log('   🌟 Toutes les étapes validées avec succès');
  }
}

/**
 * Fonction utilitaire pour exécuter les tests
 */
export async function runAPIServerEnhancedTests(port?: number): Promise<void> {
  const tests = new APIServerEnhancedTests(port);
  await tests.runAllTests();
}

// Exécuter si appelé directement
if (require.main === module) {
  const port = process.argv[2] ? parseInt(process.argv[2]) : 8085;
  runAPIServerEnhancedTests(port).catch(console.error);
}
