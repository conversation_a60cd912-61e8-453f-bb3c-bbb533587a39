import React, { useState, useEffect, useCallback } from 'react';
import { AutomatedTestingFramework, TestExecution, TestSuite, TestResult } from '../testing/automated_testing_framework';
import { PerformanceTestingSystem, PerformanceTest, PerformanceTestResult } from '../testing/performance_testing';
import { QualityMetricsSystem, QualityMetrics, QualityAlert, QualityReport } from '../testing/quality_metrics';
import { TestGenerator } from '../testing/test_generator';
import { LoadSimulator } from '../testing/load_simulator';

/**
 * Interface Principale du Laboratoire de Test Hanuman
 * Dashboard central pour l'exécution, monitoring et analyse des tests
 */

interface TestLabProps {
  testingFramework: AutomatedTestingFramework;
  performanceSystem: PerformanceTestingSystem;
  qualitySystem: QualityMetricsSystem;
  testGenerator: TestGenerator;
  loadSimulator: LoadSimulator;
  projectId: string;
}

interface TestLabState {
  activeTab: 'overview' | 'execution' | 'performance' | 'quality' | 'generator' | 'reports';
  executions: TestExecution[];
  performanceResults: PerformanceTestResult[];
  qualityMetrics: QualityMetrics | null;
  qualityAlerts: QualityAlert[];
  isLoading: boolean;
  selectedExecution: TestExecution | null;
  selectedPerformanceTest: PerformanceTest | null;
  autoRefresh: boolean;
  refreshInterval: number;
}

export const TestLabInterface: React.FC<TestLabProps> = ({
  testingFramework,
  performanceSystem,
  qualitySystem,
  testGenerator,
  loadSimulator,
  projectId
}) => {
  const [state, setState] = useState<TestLabState>({
    activeTab: 'overview',
    executions: [],
    performanceResults: [],
    qualityMetrics: null,
    qualityAlerts: [],
    isLoading: false,
    selectedExecution: null,
    selectedPerformanceTest: null,
    autoRefresh: true,
    refreshInterval: 30000 // 30 secondes
  });

  /**
   * Charge les données initiales
   */
  const loadData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const [executions, performanceResults, qualityMetrics, alerts] = await Promise.all([
        testingFramework.getRecentExecutions(projectId, 20),
        performanceSystem.getRecentResults(projectId, 10),
        qualitySystem.collectMetrics(projectId),
        qualitySystem.getActiveAlerts()
      ]);

      setState(prev => ({
        ...prev,
        executions,
        performanceResults,
        qualityMetrics,
        qualityAlerts: alerts,
        isLoading: false
      }));
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [testingFramework, performanceSystem, qualitySystem, projectId]);

  /**
   * Auto-refresh des données
   */
  useEffect(() => {
    loadData();

    if (state.autoRefresh) {
      const interval = setInterval(loadData, state.refreshInterval);
      return () => clearInterval(interval);
    }
  }, [loadData, state.autoRefresh, state.refreshInterval]);

  /**
   * Exécute une suite de tests
   */
  const executeTestSuite = async (suiteId: string, options: any) => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const execution = await testingFramework.executeTestSuite(suiteId, {
        ...options,
        triggeredBy: 'test-lab-interface'
      });

      setState(prev => ({
        ...prev,
        executions: [execution, ...prev.executions],
        selectedExecution: execution,
        isLoading: false
      }));
    } catch (error) {
      console.error('Erreur lors de l\'exécution des tests:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Exécute un test de performance
   */
  const executePerformanceTest = async (test: PerformanceTest) => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const result = await performanceSystem.executePerformanceTest(test);

      setState(prev => ({
        ...prev,
        performanceResults: [result, ...prev.performanceResults],
        isLoading: false
      }));
    } catch (error) {
      console.error('Erreur lors du test de performance:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Génère un rapport de qualité
   */
  const generateQualityReport = async () => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const report = await qualitySystem.generateQualityReport(projectId);
      // Ici on pourrait ouvrir le rapport dans un modal ou télécharger
      console.log('Rapport généré:', report);
      setState(prev => ({ ...prev, isLoading: false }));
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Rendu de l'onglet Overview
   */
  const renderOverviewTab = () => (
    <div className="overview-tab">
      <div className="metrics-grid">
        {/* Métriques de qualité globales */}
        <div className="metric-card quality-score">
          <h3>Score de Qualité Global</h3>
          <div className="score-display">
            <span className="score-value">{state.qualityMetrics?.score.overall || 0}</span>
            <span className="score-grade">Grade {state.qualityMetrics?.score.grade || 'N/A'}</span>
          </div>
          <div className="score-trend">
            {state.qualityMetrics?.score.trend === 'improving' && '📈 En amélioration'}
            {state.qualityMetrics?.score.trend === 'declining' && '📉 En dégradation'}
            {state.qualityMetrics?.score.trend === 'stable' && '➡️ Stable'}
          </div>
        </div>

        {/* Couverture de tests */}
        <div className="metric-card test-coverage">
          <h3>Couverture de Tests</h3>
          <div className="coverage-display">
            <div className="coverage-bar">
              <div
                className="coverage-fill"
                style={{ width: `${state.qualityMetrics?.testMetrics.coverage.overall || 0}%` }}
              />
            </div>
            <span className="coverage-percentage">
              {state.qualityMetrics?.testMetrics.coverage.overall || 0}%
            </span>
          </div>
          <div className="coverage-details">
            <span>Seuil: {state.qualityMetrics?.testMetrics.coverage.threshold || 0}%</span>
            <span className={`status ${state.qualityMetrics?.testMetrics.coverage.status || 'unknown'}`}>
              {state.qualityMetrics?.testMetrics.coverage.status || 'Unknown'}
            </span>
          </div>
        </div>

        {/* Exécutions récentes */}
        <div className="metric-card recent-executions">
          <h3>Exécutions Récentes</h3>
          <div className="executions-summary">
            <div className="execution-stat">
              <span className="stat-label">Total:</span>
              <span className="stat-value">{state.executions.length}</span>
            </div>
            <div className="execution-stat">
              <span className="stat-label">Réussies:</span>
              <span className="stat-value success">
                {state.executions.filter(e => e.status === 'completed').length}
              </span>
            </div>
            <div className="execution-stat">
              <span className="stat-label">Échouées:</span>
              <span className="stat-value error">
                {state.executions.filter(e => e.status === 'failed').length}
              </span>
            </div>
          </div>
        </div>

        {/* Performance */}
        <div className="metric-card performance-summary">
          <h3>Performance</h3>
          <div className="performance-metrics">
            <div className="perf-metric">
              <span className="metric-label">Score:</span>
              <span className="metric-value">
                {state.qualityMetrics?.testMetrics.performance.score || 0}/100
              </span>
            </div>
            <div className="perf-metric">
              <span className="metric-label">Temps de réponse:</span>
              <span className="metric-value">
                {state.qualityMetrics?.testMetrics.performance.responseTime.avg || 0}ms
              </span>
            </div>
            <div className="perf-metric">
              <span className="metric-label">Débit:</span>
              <span className="metric-value">
                {state.qualityMetrics?.testMetrics.performance.throughput.current || 0} req/s
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Alertes actives */}
      {state.qualityAlerts.length > 0 && (
        <div className="alerts-section">
          <h3>Alertes Actives</h3>
          <div className="alerts-list">
            {state.qualityAlerts.slice(0, 5).map(alert => (
              <div key={alert.id} className={`alert alert-${alert.severity}`}>
                <div className="alert-header">
                  <span className="alert-type">{alert.severity.toUpperCase()}</span>
                  <span className="alert-time">{alert.timestamp.toLocaleTimeString()}</span>
                </div>
                <div className="alert-message">{alert.message}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Actions rapides */}
      <div className="quick-actions">
        <h3>Actions Rapides</h3>
        <div className="action-buttons">
          <button
            className="action-btn primary"
            onClick={() => executeTestSuite('all-tests', { parallel: true })}
            disabled={state.isLoading}
          >
            🧪 Lancer Tous les Tests
          </button>
          <button
            className="action-btn secondary"
            onClick={() => setState(prev => ({ ...prev, activeTab: 'performance' }))}
          >
            ⚡ Tests de Performance
          </button>
          <button
            className="action-btn secondary"
            onClick={generateQualityReport}
            disabled={state.isLoading}
          >
            📊 Générer Rapport
          </button>
          <button
            className="action-btn secondary"
            onClick={() => setState(prev => ({ ...prev, activeTab: 'generator' }))}
          >
            🤖 Générateur de Tests
          </button>
        </div>
      </div>
    </div>
  );

  /**
   * Rendu de l'onglet Execution
   */
  const renderExecutionTab = () => (
    <div className="execution-tab">
      <div className="execution-controls">
        <h3>Contrôles d'Exécution</h3>
        <div className="control-panel">
          <div className="suite-selector">
            <label>Suite de Tests:</label>
            <select onChange={(e) => {
              if (e.target.value) {
                executeTestSuite(e.target.value, { parallel: true });
              }
            }}>
              <option value="">Sélectionner une suite...</option>
              <option value="infrastructure-tests">Tests Infrastructure</option>
              <option value="security-tests">Tests Sécurité</option>
              <option value="performance-tests">Tests Performance</option>
              <option value="integration-tests">Tests Intégration</option>
              <option value="regression-tests">Tests Régression</option>
            </select>
          </div>

          <div className="execution-options">
            <label>
              <input type="checkbox" defaultChecked />
              Exécution parallèle
            </label>
            <label>
              <input type="checkbox" />
              Mode verbose
            </label>
            <label>
              <input type="checkbox" />
              Arrêter au premier échec
            </label>
          </div>
        </div>
      </div>

      <div className="executions-history">
        <h3>Historique des Exécutions</h3>
        <div className="executions-table">
          <div className="table-header">
            <span>ID</span>
            <span>Suite</span>
            <span>Statut</span>
            <span>Durée</span>
            <span>Tests</span>
            <span>Couverture</span>
            <span>Actions</span>
          </div>
          {state.executions.map(execution => (
            <div key={execution.id} className="table-row">
              <span className="execution-id">{execution.id.slice(-8)}</span>
              <span className="suite-name">{execution.suiteId}</span>
              <span className={`status status-${execution.status}`}>
                {execution.status}
              </span>
              <span className="duration">
                {execution.duration ? `${Math.round(execution.duration / 1000)}s` : '-'}
              </span>
              <span className="test-summary">
                {execution.summary.passed}/{execution.summary.total}
              </span>
              <span className="coverage">
                {execution.summary.coverage}%
              </span>
              <span className="actions">
                <button
                  className="btn-small"
                  onClick={() => setState(prev => ({ ...prev, selectedExecution: execution }))}
                >
                  Détails
                </button>
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Détails de l'exécution sélectionnée */}
      {state.selectedExecution && (
        <div className="execution-details">
          <h3>Détails de l'Exécution</h3>
          <div className="details-content">
            <div className="execution-info">
              <div className="info-item">
                <label>ID:</label>
                <span>{state.selectedExecution.id}</span>
              </div>
              <div className="info-item">
                <label>Suite:</label>
                <span>{state.selectedExecution.suiteId}</span>
              </div>
              <div className="info-item">
                <label>Statut:</label>
                <span className={`status status-${state.selectedExecution.status}`}>
                  {state.selectedExecution.status}
                </span>
              </div>
              <div className="info-item">
                <label>Démarré:</label>
                <span>{state.selectedExecution.startTime.toLocaleString()}</span>
              </div>
              <div className="info-item">
                <label>Durée:</label>
                <span>
                  {state.selectedExecution.duration ?
                    `${Math.round(state.selectedExecution.duration / 1000)}s` :
                    'En cours...'
                  }
                </span>
              </div>
            </div>

            <div className="test-results">
              <h4>Résultats des Tests</h4>
              <div className="results-summary">
                <div className="summary-item success">
                  <span className="count">{state.selectedExecution.summary.passed}</span>
                  <span className="label">Réussis</span>
                </div>
                <div className="summary-item error">
                  <span className="count">{state.selectedExecution.summary.failed}</span>
                  <span className="label">Échoués</span>
                </div>
                <div className="summary-item warning">
                  <span className="count">{state.selectedExecution.summary.skipped}</span>
                  <span className="label">Ignorés</span>
                </div>
                <div className="summary-item info">
                  <span className="count">{state.selectedExecution.summary.coverage}%</span>
                  <span className="label">Couverture</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  /**
   * Rendu de l'onglet Performance
   */
  const renderPerformanceTab = () => (
    <div className="performance-tab">
      <div className="performance-controls">
        <h3>Tests de Performance</h3>
        <div className="perf-control-panel">
          <div className="test-type-selector">
            <label>Type de Test:</label>
            <select onChange={(e) => {
              if (e.target.value) {
                const testType = e.target.value as any;
                const testConfig = {
                  id: `perf-${Date.now()}`,
                  name: `Test ${testType}`,
                  description: `Test de ${testType} automatique`,
                  type: testType,
                  configuration: {
                    duration: 300,
                    rampUpTime: 60,
                    rampDownTime: 60,
                    virtualUsers: testType === 'load' ? 100 : testType === 'stress' ? 500 : 50,
                    requestsPerSecond: 100
                  },
                  target: { endpoint: '/api/test' },
                  thresholds: {
                    maxResponseTime: 200,
                    minThroughput: 50,
                    maxErrorRate: 5,
                    maxCpuUsage: 80,
                    maxMemoryUsage: 80
                  },
                  scenarios: []
                };
                executePerformanceTest(testConfig);
              }
            }}>
              <option value="">Sélectionner un type...</option>
              <option value="load">Test de Charge</option>
              <option value="stress">Test de Stress</option>
              <option value="spike">Test de Pic</option>
              <option value="volume">Test de Volume</option>
              <option value="endurance">Test d'Endurance</option>
            </select>
          </div>

          <div className="perf-options">
            <div className="option-group">
              <label>Utilisateurs virtuels:</label>
              <input type="number" defaultValue={100} min={1} max={1000} />
            </div>
            <div className="option-group">
              <label>Durée (s):</label>
              <input type="number" defaultValue={300} min={10} max={3600} />
            </div>
            <div className="option-group">
              <label>Req/s cible:</label>
              <input type="number" defaultValue={100} min={1} max={1000} />
            </div>
          </div>
        </div>
      </div>

      <div className="performance-results">
        <h3>Résultats de Performance</h3>
        <div className="results-grid">
          {state.performanceResults.map(result => (
            <div key={result.testId} className="perf-result-card">
              <div className="result-header">
                <h4>{result.testId.slice(-8)}</h4>
                <span className={`status status-${result.status}`}>
                  {result.status}
                </span>
              </div>

              <div className="result-metrics">
                <div className="metric-item">
                  <label>Temps de réponse moyen:</label>
                  <span>{result.metrics.responseTime.avg}ms</span>
                </div>
                <div className="metric-item">
                  <label>P95:</label>
                  <span>{result.metrics.responseTime.p95}ms</span>
                </div>
                <div className="metric-item">
                  <label>Débit:</label>
                  <span>{result.metrics.throughput.requestsPerSecond} req/s</span>
                </div>
                <div className="metric-item">
                  <label>CPU:</label>
                  <span>{result.metrics.resources.cpu.usage}%</span>
                </div>
                <div className="metric-item">
                  <label>Mémoire:</label>
                  <span>{result.metrics.resources.memory.usage}%</span>
                </div>
                <div className="metric-item">
                  <label>Erreurs:</label>
                  <span>{result.metrics.errors.rate}%</span>
                </div>
              </div>

              {result.thresholdViolations.length > 0 && (
                <div className="threshold-violations">
                  <h5>Violations de Seuils:</h5>
                  {result.thresholdViolations.map((violation, index) => (
                    <div key={index} className={`violation violation-${violation.severity}`}>
                      <span>{violation.threshold}: {violation.actual} > {violation.expected}</span>
                    </div>
                  ))}
                </div>
              )}

              <div className="result-actions">
                <button className="btn-small">Détails</button>
                <button className="btn-small">Comparer</button>
                <button className="btn-small">Exporter</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  /**
   * Rendu de l'onglet Qualité
   */
  const renderQualityTab = () => (
    <div className="quality-tab">
      <div className="quality-overview">
        <h3>Métriques de Qualité</h3>

        {state.qualityMetrics && (
          <div className="quality-dashboard">
            <div className="quality-score-section">
              <div className="main-score">
                <div className="score-circle">
                  <span className="score-number">{state.qualityMetrics.score.overall}</span>
                  <span className="score-label">Score Global</span>
                </div>
                <div className="grade-display">
                  <span className={`grade grade-${state.qualityMetrics.score.grade.toLowerCase()}`}>
                    {state.qualityMetrics.score.grade}
                  </span>
                </div>
              </div>

              <div className="category-scores">
                {Object.entries(state.qualityMetrics.score.categories).map(([category, score]) => (
                  <div key={category} className="category-score">
                    <label>{category.charAt(0).toUpperCase() + category.slice(1)}:</label>
                    <div className="score-bar">
                      <div
                        className="score-fill"
                        style={{ width: `${score}%` }}
                      />
                    </div>
                    <span className="score-value">{score}/100</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="metrics-details">
              <div className="metric-section">
                <h4>Tests</h4>
                <div className="metric-grid">
                  <div className="metric-item">
                    <label>Couverture:</label>
                    <span>{state.qualityMetrics.testMetrics.coverage.overall}%</span>
                  </div>
                  <div className="metric-item">
                    <label>Taux de réussite:</label>
                    <span>{state.qualityMetrics.testMetrics.reliability.testPassRate}%</span>
                  </div>
                  <div className="metric-item">
                    <label>Tests instables:</label>
                    <span>{state.qualityMetrics.testMetrics.reliability.flakyTests}</span>
                  </div>
                </div>
              </div>

              <div className="metric-section">
                <h4>Performance</h4>
                <div className="metric-grid">
                  <div className="metric-item">
                    <label>Score:</label>
                    <span>{state.qualityMetrics.testMetrics.performance.score}/100</span>
                  </div>
                  <div className="metric-item">
                    <label>Temps de réponse:</label>
                    <span>{state.qualityMetrics.testMetrics.performance.responseTime.avg}ms</span>
                  </div>
                  <div className="metric-item">
                    <label>Débit:</label>
                    <span>{state.qualityMetrics.testMetrics.performance.throughput.current} req/s</span>
                  </div>
                </div>
              </div>

              <div className="metric-section">
                <h4>Sécurité</h4>
                <div className="metric-grid">
                  <div className="metric-item">
                    <label>Risque:</label>
                    <span>{state.qualityMetrics.testMetrics.security.riskScore}</span>
                  </div>
                  <div className="metric-item">
                    <label>Vulnérabilités critiques:</label>
                    <span className="critical">{state.qualityMetrics.testMetrics.security.vulnerabilities.critical}</span>
                  </div>
                  <div className="metric-item">
                    <label>Conformité:</label>
                    <span>{state.qualityMetrics.testMetrics.security.complianceScore}%</span>
                  </div>
                </div>
              </div>

              <div className="metric-section">
                <h4>Maintenabilité</h4>
                <div className="metric-grid">
                  <div className="metric-item">
                    <label>Complexité cyclomatique:</label>
                    <span>{state.qualityMetrics.codeMetrics.complexity.cyclomaticComplexity}</span>
                  </div>
                  <div className="metric-item">
                    <label>Dette technique:</label>
                    <span>{state.qualityMetrics.codeMetrics.complexity.technicalDebt}h</span>
                  </div>
                  <div className="metric-item">
                    <label>Code dupliqué:</label>
                    <span>{state.qualityMetrics.codeMetrics.duplication.duplicationRatio.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </div>

            {state.qualityMetrics.recommendations.length > 0 && (
              <div className="recommendations-section">
                <h4>Recommandations</h4>
                <div className="recommendations-list">
                  {state.qualityMetrics.recommendations.map(rec => (
                    <div key={rec.id} className={`recommendation recommendation-${rec.priority}`}>
                      <div className="rec-header">
                        <h5>{rec.title}</h5>
                        <span className={`priority priority-${rec.priority}`}>
                          {rec.priority.toUpperCase()}
                        </span>
                      </div>
                      <p className="rec-description">{rec.description}</p>
                      <div className="rec-details">
                        <span className="rec-impact">Impact: {rec.impact}</span>
                        <span className="rec-effort">Effort: {rec.effort}</span>
                        <span className="rec-time">Temps estimé: {rec.estimatedTime}h</span>
                      </div>
                      <div className="rec-actions">
                        <h6>Actions:</h6>
                        <ul>
                          {rec.actions.map((action, index) => (
                            <li key={index}>{action}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  /**
   * Rendu de l'onglet Générateur
   */
  const renderGeneratorTab = () => (
    <div className="generator-tab">
      <div className="generator-controls">
        <h3>Générateur de Tests Intelligent</h3>
        <div className="generator-options">
          <div className="generation-type">
            <label>Type de génération:</label>
            <select>
              <option value="unit">Tests Unitaires</option>
              <option value="integration">Tests d'Intégration</option>
              <option value="e2e">Tests End-to-End</option>
              <option value="performance">Tests de Performance</option>
              <option value="security">Tests de Sécurité</option>
            </select>
          </div>

          <div className="target-selection">
            <label>Cible:</label>
            <select>
              <option value="">Sélectionner un composant...</option>
              <option value="agent">Agent Hanuman</option>
              <option value="organ">Organe</option>
              <option value="interface">Interface</option>
              <option value="service">Service</option>
              <option value="api">API Endpoint</option>
            </select>
          </div>

          <div className="generation-params">
            <div className="param-group">
              <label>Couverture cible (%):</label>
              <input type="number" defaultValue={90} min={50} max={100} />
            </div>
            <div className="param-group">
              <label>Complexité:</label>
              <select>
                <option value="basic">Basique</option>
                <option value="intermediate">Intermédiaire</option>
                <option value="advanced">Avancé</option>
              </select>
            </div>
            <div className="param-group">
              <label>
                <input type="checkbox" defaultChecked />
                Inclure les mocks
              </label>
            </div>
            <div className="param-group">
              <label>
                <input type="checkbox" />
                Tests de régression
              </label>
            </div>
          </div>

          <div className="generation-actions">
            <button className="btn-primary">
              🤖 Générer Tests
            </button>
            <button className="btn-secondary">
              📋 Prévisualiser
            </button>
            <button className="btn-secondary">
              💾 Sauvegarder Template
            </button>
          </div>
        </div>
      </div>

      <div className="generated-tests">
        <h3>Tests Générés</h3>
        <div className="tests-preview">
          <div className="preview-header">
            <span>Aucun test généré</span>
            <div className="preview-actions">
              <button className="btn-small">Tout sélectionner</button>
              <button className="btn-small">Appliquer</button>
              <button className="btn-small">Exporter</button>
            </div>
          </div>

          <div className="preview-content">
            <div className="empty-state">
              <div className="empty-icon">🤖</div>
              <h4>Générateur de Tests IA</h4>
              <p>Utilisez les contrôles ci-dessus pour générer automatiquement des tests intelligents basés sur l'analyse de votre code.</p>
              <div className="features-list">
                <div className="feature">✅ Génération automatique basée sur l'IA</div>
                <div className="feature">✅ Couverture optimisée</div>
                <div className="feature">✅ Mocks intelligents</div>
                <div className="feature">✅ Tests de régression</div>
                <div className="feature">✅ Templates personnalisables</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="templates-section">
        <h3>Templates de Tests</h3>
        <div className="templates-grid">
          <div className="template-card">
            <h4>Agent Standard</h4>
            <p>Template pour tester les agents Hanuman</p>
            <div className="template-stats">
              <span>15 tests</span>
              <span>95% couverture</span>
            </div>
            <button className="btn-small">Utiliser</button>
          </div>

          <div className="template-card">
            <h4>Interface React</h4>
            <p>Template pour les composants React</p>
            <div className="template-stats">
              <span>12 tests</span>
              <span>90% couverture</span>
            </div>
            <button className="btn-small">Utiliser</button>
          </div>

          <div className="template-card">
            <h4>API Endpoints</h4>
            <p>Template pour les endpoints REST</p>
            <div className="template-stats">
              <span>20 tests</span>
              <span>100% couverture</span>
            </div>
            <button className="btn-small">Utiliser</button>
          </div>
        </div>
      </div>
    </div>
  );

  /**
   * Rendu de l'onglet Rapports
   */
  const renderReportsTab = () => (
    <div className="reports-tab">
      <div className="reports-controls">
        <h3>Rapports et Exports</h3>
        <div className="report-options">
          <div className="report-type">
            <label>Type de rapport:</label>
            <select>
              <option value="quality">Rapport de Qualité</option>
              <option value="performance">Rapport de Performance</option>
              <option value="security">Rapport de Sécurité</option>
              <option value="coverage">Rapport de Couverture</option>
              <option value="trends">Rapport de Tendances</option>
              <option value="complete">Rapport Complet</option>
            </select>
          </div>

          <div className="report-period">
            <label>Période:</label>
            <select>
              <option value="last24h">Dernières 24h</option>
              <option value="last7d">7 derniers jours</option>
              <option value="last30d">30 derniers jours</option>
              <option value="last90d">90 derniers jours</option>
              <option value="custom">Période personnalisée</option>
            </select>
          </div>

          <div className="export-format">
            <label>Format d'export:</label>
            <div className="format-options">
              <label>
                <input type="checkbox" defaultChecked />
                HTML
              </label>
              <label>
                <input type="checkbox" defaultChecked />
                JSON
              </label>
              <label>
                <input type="checkbox" />
                PDF
              </label>
              <label>
                <input type="checkbox" />
                CSV
              </label>
            </div>
          </div>

          <div className="report-actions">
            <button
              className="btn-primary"
              onClick={generateQualityReport}
              disabled={state.isLoading}
            >
              📊 Générer Rapport
            </button>
            <button className="btn-secondary">
              📧 Programmer Envoi
            </button>
            <button className="btn-secondary">
              ⚙️ Configurer
            </button>
          </div>
        </div>
      </div>

      <div className="reports-history">
        <h3>Historique des Rapports</h3>
        <div className="reports-table">
          <div className="table-header">
            <span>Date</span>
            <span>Type</span>
            <span>Période</span>
            <span>Format</span>
            <span>Taille</span>
            <span>Actions</span>
          </div>

          {/* Simulation de rapports existants */}
          {[
            { date: new Date(), type: 'Qualité', period: '7 jours', format: 'HTML', size: '2.3 MB' },
            { date: new Date(Date.now() - 86400000), type: 'Performance', period: '24h', format: 'JSON', size: '1.1 MB' },
            { date: new Date(Date.now() - 172800000), type: 'Complet', period: '30 jours', format: 'PDF', size: '5.7 MB' }
          ].map((report, index) => (
            <div key={index} className="table-row">
              <span>{report.date.toLocaleDateString()}</span>
              <span>{report.type}</span>
              <span>{report.period}</span>
              <span>{report.format}</span>
              <span>{report.size}</span>
              <span className="actions">
                <button className="btn-small">Télécharger</button>
                <button className="btn-small">Partager</button>
                <button className="btn-small">Supprimer</button>
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="report-preview">
        <h3>Aperçu du Rapport</h3>
        <div className="preview-container">
          {state.qualityMetrics ? (
            <div className="report-summary">
              <div className="summary-header">
                <h4>Rapport de Qualité - {new Date().toLocaleDateString()}</h4>
                <span className="report-status">✅ Prêt</span>
              </div>

              <div className="summary-content">
                <div className="summary-metric">
                  <label>Score Global:</label>
                  <span className="metric-value">{state.qualityMetrics.score.overall}/100</span>
                </div>
                <div className="summary-metric">
                  <label>Grade:</label>
                  <span className={`grade grade-${state.qualityMetrics.score.grade.toLowerCase()}`}>
                    {state.qualityMetrics.score.grade}
                  </span>
                </div>
                <div className="summary-metric">
                  <label>Tendance:</label>
                  <span className="trend">
                    {state.qualityMetrics.score.trend === 'improving' && '📈 En amélioration'}
                    {state.qualityMetrics.score.trend === 'declining' && '📉 En dégradation'}
                    {state.qualityMetrics.score.trend === 'stable' && '➡️ Stable'}
                  </span>
                </div>
                <div className="summary-metric">
                  <label>Alertes:</label>
                  <span className="alerts-count">{state.qualityAlerts.length}</span>
                </div>
              </div>

              <div className="summary-actions">
                <button className="btn-primary">Voir Rapport Complet</button>
                <button className="btn-secondary">Exporter</button>
              </div>
            </div>
          ) : (
            <div className="empty-preview">
              <div className="empty-icon">📊</div>
              <p>Aucun rapport disponible. Générez un rapport pour voir l'aperçu.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="test-lab-interface">
      {/* Header */}
      <div className="lab-header">
        <h1>🧪 Laboratoire de Test Hanuman</h1>
        <div className="header-controls">
          <div className="refresh-controls">
            <label>
              <input
                type="checkbox"
                checked={state.autoRefresh}
                onChange={(e) => setState(prev => ({ ...prev, autoRefresh: e.target.checked }))}
              />
              Auto-refresh
            </label>
            <select
              value={state.refreshInterval}
              onChange={(e) => setState(prev => ({ ...prev, refreshInterval: parseInt(e.target.value) }))}
            >
              <option value={10000}>10s</option>
              <option value={30000}>30s</option>
              <option value={60000}>1min</option>
              <option value={300000}>5min</option>
            </select>
          </div>
          <button
            className="refresh-btn"
            onClick={loadData}
            disabled={state.isLoading}
          >
            {state.isLoading ? '⏳' : '🔄'} Actualiser
          </button>
        </div>
      </div>

      {/* Navigation */}
      <div className="lab-navigation">
        <div className="nav-tabs">
          {[
            { id: 'overview', label: '📊 Vue d\'ensemble', icon: '📊' },
            { id: 'execution', label: '🧪 Exécution', icon: '🧪' },
            { id: 'performance', label: '⚡ Performance', icon: '⚡' },
            { id: 'quality', label: '🎯 Qualité', icon: '🎯' },
            { id: 'generator', label: '🤖 Générateur', icon: '🤖' },
            { id: 'reports', label: '📋 Rapports', icon: '📋' }
          ].map(tab => (
            <button
              key={tab.id}
              className={`nav-tab ${state.activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setState(prev => ({ ...prev, activeTab: tab.id as any }))}
            >
              <span className="tab-icon">{tab.icon}</span>
              <span className="tab-label">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Contenu principal */}
      <div className="lab-content">
        {state.activeTab === 'overview' && renderOverviewTab()}
        {state.activeTab === 'execution' && renderExecutionTab()}
        {state.activeTab === 'performance' && renderPerformanceTab()}
        {state.activeTab === 'quality' && renderQualityTab()}
        {state.activeTab === 'generator' && renderGeneratorTab()}
        {state.activeTab === 'reports' && renderReportsTab()}
      </div>

      {/* Loading overlay */}
      {state.isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner">
            <div className="spinner"></div>
            <span>Chargement...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default TestLabInterface;
