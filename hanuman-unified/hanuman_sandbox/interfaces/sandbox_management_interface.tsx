import React, { useState, useEffect, useCallback } from 'react';
import { SandboxInfrastructure, SandboxContainer } from '../infrastructure/sandbox_infrastructure';
import { SandboxSecurity, SecurityIncident, SecurityMetrics } from '../security/sandbox_security';
import EnvironmentManager from '../environments/environment_manager';

// Types pour l'interface de gestion
export interface DashboardStats {
  totalContainers: number;
  runningContainers: number;
  stoppedContainers: number;
  errorContainers: number;
  totalEnvironments: number;
  securityScore: number;
  openIncidents: number;
  resourceUsage: {
    cpu: number;
    memory: number;
    storage: number;
  };
}

export interface SystemAlert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  dismissed: boolean;
}

interface SandboxManagementInterfaceProps {
  infrastructure: SandboxInfrastructure;
  security: SandboxSecurity;
  onError?: (error: Error) => void;
}

/**
 * Interface de Gestion Principale de la Sandbox Hanuman
 * Dashboard de contrôle, monitoring et gestion des environnements
 */
export const SandboxManagementInterface: React.FC<SandboxManagementInterfaceProps> = ({
  infrastructure,
  security,
  onError
}) => {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'environments' | 'security' | 'monitoring'>('dashboard');
  const [stats, setStats] = useState<DashboardStats>({
    totalContainers: 0,
    runningContainers: 0,
    stoppedContainers: 0,
    errorContainers: 0,
    totalEnvironments: 0,
    securityScore: 100,
    openIncidents: 0,
    resourceUsage: { cpu: 0, memory: 0, storage: 0 }
  });
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [containers, setContainers] = useState<SandboxContainer[]>([]);
  const [incidents, setIncidents] = useState<SecurityIncident[]>([]);
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Charger les données initiales
  useEffect(() => {
    loadDashboardData();
    
    // Écouter les événements
    infrastructure.on('container:created', handleContainerEvent);
    infrastructure.on('container:destroyed', handleContainerEvent);
    infrastructure.on('monitoring:resources', handleResourceUpdate);
    
    security.on('security:incident-created', handleSecurityIncident);
    security.on('security:incident-resolved', handleSecurityIncident);
    security.on('security:scan-completed', handleSecurityScan);

    // Actualiser les données toutes les 30 secondes
    const interval = setInterval(loadDashboardData, 30000);

    return () => {
      infrastructure.off('container:created', handleContainerEvent);
      infrastructure.off('container:destroyed', handleContainerEvent);
      infrastructure.off('monitoring:resources', handleResourceUpdate);
      
      security.off('security:incident-created', handleSecurityIncident);
      security.off('security:incident-resolved', handleSecurityIncident);
      security.off('security:scan-completed', handleSecurityScan);
      
      clearInterval(interval);
    };
  }, [infrastructure, security]);

  const loadDashboardData = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Charger les conteneurs
      const containerList = infrastructure.getContainers();
      setContainers(containerList);
      
      // Charger les incidents de sécurité
      const incidentList = security.getIncidents();
      setIncidents(incidentList);
      
      // Charger les métriques de sécurité
      const metrics = security.getSecurityMetrics();
      setSecurityMetrics(metrics);
      
      // Calculer les statistiques
      const infraStats = infrastructure.getInfrastructureStats();
      const openIncidents = security.getOpenIncidents().length;
      
      const newStats: DashboardStats = {
        totalContainers: infraStats.containers.total,
        runningContainers: infraStats.containers.running,
        stoppedContainers: infraStats.containers.stopped,
        errorContainers: infraStats.containers.error,
        totalEnvironments: infraStats.namespaces,
        securityScore: metrics.securityScore,
        openIncidents,
        resourceUsage: {
          cpu: containerList.reduce((sum, c) => sum + c.resources.cpu, 0),
          memory: containerList.reduce((sum, c) => sum + c.resources.memory, 0),
          storage: containerList.reduce((sum, c) => sum + c.resources.storage, 0)
        }
      };
      
      setStats(newStats);
      
      // Générer des alertes si nécessaire
      generateSystemAlerts(newStats, metrics);
      
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      onError?.(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [infrastructure, security, onError]);

  const handleContainerEvent = useCallback(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const handleResourceUpdate = useCallback((data: any) => {
    // Mettre à jour les métriques de ressources en temps réel
    setStats(prev => ({
      ...prev,
      resourceUsage: {
        ...prev.resourceUsage,
        // Ici on pourrait utiliser les vraies données de monitoring
      }
    }));
  }, []);

  const handleSecurityIncident = useCallback((incident: SecurityIncident) => {
    loadDashboardData();
    
    // Créer une alerte pour les incidents critiques
    if (incident.severity === 'critical' || incident.severity === 'high') {
      const alert: SystemAlert = {
        id: `alert_${Date.now()}`,
        type: incident.severity === 'critical' ? 'error' : 'warning',
        title: 'Incident de Sécurité',
        message: incident.description,
        timestamp: new Date(),
        dismissed: false
      };
      setAlerts(prev => [alert, ...prev.slice(0, 9)]); // Garder max 10 alertes
    }
  }, [loadDashboardData]);

  const handleSecurityScan = useCallback((metrics: SecurityMetrics) => {
    setSecurityMetrics(metrics);
  }, []);

  const generateSystemAlerts = (stats: DashboardStats, metrics: SecurityMetrics) => {
    const newAlerts: SystemAlert[] = [];

    // Alerte pour score de sécurité bas
    if (metrics.securityScore < 70) {
      newAlerts.push({
        id: `security_score_${Date.now()}`,
        type: 'warning',
        title: 'Score de Sécurité Bas',
        message: `Score de sécurité: ${metrics.securityScore}%. Vérifiez les incidents ouverts.`,
        timestamp: new Date(),
        dismissed: false
      });
    }

    // Alerte pour utilisation élevée des ressources
    if (stats.resourceUsage.cpu > 80) {
      newAlerts.push({
        id: `cpu_high_${Date.now()}`,
        type: 'warning',
        title: 'Utilisation CPU Élevée',
        message: `Utilisation CPU: ${stats.resourceUsage.cpu} cores. Considérez l'optimisation.`,
        timestamp: new Date(),
        dismissed: false
      });
    }

    // Alerte pour conteneurs en erreur
    if (stats.errorContainers > 0) {
      newAlerts.push({
        id: `containers_error_${Date.now()}`,
        type: 'error',
        title: 'Conteneurs en Erreur',
        message: `${stats.errorContainers} conteneur(s) en erreur nécessitent votre attention.`,
        timestamp: new Date(),
        dismissed: false
      });
    }

    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev.filter(a => !a.dismissed).slice(0, 7)]);
    }
  };

  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, dismissed: true } : alert
    ));
  };

  const getStatusColor = (status: string) => {
    const colors = {
      running: 'text-green-600 bg-green-100',
      stopped: 'text-gray-600 bg-gray-100',
      error: 'text-red-600 bg-red-100',
      creating: 'text-yellow-600 bg-yellow-100'
    };
    return colors[status as keyof typeof colors] || 'text-gray-600 bg-gray-100';
  };

  const getAlertColor = (type: SystemAlert['type']) => {
    const colors = {
      info: 'border-blue-200 bg-blue-50 text-blue-800',
      warning: 'border-yellow-200 bg-yellow-50 text-yellow-800',
      error: 'border-red-200 bg-red-50 text-red-800',
      success: 'border-green-200 bg-green-50 text-green-800'
    };
    return colors[type];
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-2xl">📦</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Conteneurs Totaux</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalContainers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-2xl">🚀</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">En Fonctionnement</p>
              <p className="text-2xl font-bold text-green-600">{stats.runningContainers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <span className="text-2xl">🛡️</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Score Sécurité</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.securityScore}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <span className="text-2xl">🚨</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Incidents Ouverts</p>
              <p className="text-2xl font-bold text-red-600">{stats.openIncidents}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Alertes système */}
      {alerts.filter(a => !a.dismissed).length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800">🔔 Alertes Système</h3>
          {alerts.filter(a => !a.dismissed).slice(0, 5).map(alert => (
            <div key={alert.id} className={`p-4 rounded-lg border ${getAlertColor(alert.type)}`}>
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium">{alert.title}</h4>
                  <p className="text-sm mt-1">{alert.message}</p>
                  <p className="text-xs mt-2 opacity-75">
                    {alert.timestamp.toLocaleString()}
                  </p>
                </div>
                <button
                  onClick={() => dismissAlert(alert.id)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Utilisation des ressources */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">📊 Utilisation des Ressources</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600">CPU</span>
              <span className="text-sm text-gray-900">{stats.resourceUsage.cpu} cores</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${Math.min(100, (stats.resourceUsage.cpu / 16) * 100)}%` }}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600">Mémoire</span>
              <span className="text-sm text-gray-900">{(stats.resourceUsage.memory / 1024).toFixed(1)} GB</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full" 
                style={{ width: `${Math.min(100, (stats.resourceUsage.memory / 32768) * 100)}%` }}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600">Stockage</span>
              <span className="text-sm text-gray-900">{(stats.resourceUsage.storage / 1000).toFixed(1)} GB</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-purple-600 h-2 rounded-full" 
                style={{ width: `${Math.min(100, (stats.resourceUsage.storage / 500000) * 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Liste des conteneurs récents */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">📦 Conteneurs Récents</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Nom
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ressources
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Créé
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {containers.slice(0, 5).map(container => (
                <tr key={container.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {container.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {container.type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(container.status)}`}>
                      {container.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {container.resources.cpu}c / {container.resources.memory}MB
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {container.createdAt.toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderSecurity = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">🛡️ Tableau de Bord Sécurité</h3>
        
        {securityMetrics && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{securityMetrics.securityScore}%</div>
              <div className="text-sm text-gray-600">Score de Sécurité</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">{securityMetrics.totalIncidents}</div>
              <div className="text-sm text-gray-600">Incidents Totaux</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{securityMetrics.threatsBlocked}</div>
              <div className="text-sm text-gray-600">Menaces Bloquées</div>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Gravité
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {incidents.slice(0, 10).map(incident => (
                <tr key={incident.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {incident.type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      incident.severity === 'critical' ? 'text-red-800 bg-red-100' :
                      incident.severity === 'high' ? 'text-orange-800 bg-orange-100' :
                      incident.severity === 'medium' ? 'text-yellow-800 bg-yellow-100' :
                      'text-green-800 bg-green-100'
                    }`}>
                      {incident.severity}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {incident.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {incident.status}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {incident.timestamp.toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement de la sandbox...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                🏗️ Sandbox Hanuman - Gestion Centrale
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Environnement de développement et test sécurisé pour les agents
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                stats.securityScore >= 80 ? 'bg-green-100 text-green-800' :
                stats.securityScore >= 60 ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                Sécurité: {stats.securityScore}%
              </div>
              <div className="text-sm text-gray-500">
                {stats.runningContainers}/{stats.totalContainers} conteneurs actifs
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation par onglets */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'dashboard', name: 'Tableau de Bord', icon: '📊' },
              { id: 'environments', name: 'Environnements', icon: '🧪' },
              { id: 'security', name: 'Sécurité', icon: '🛡️' },
              { id: 'monitoring', name: 'Monitoring', icon: '📈' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Contenu des onglets */}
        {activeTab === 'dashboard' && renderDashboard()}
        {activeTab === 'environments' && (
          <EnvironmentManager 
            infrastructure={infrastructure} 
            onError={onError}
          />
        )}
        {activeTab === 'security' && renderSecurity()}
        {activeTab === 'monitoring' && (
          <div className="text-center py-12 text-gray-500">
            <p className="text-lg">Monitoring avancé</p>
            <p className="text-sm">Fonctionnalité à implémenter dans les prochains sprints</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SandboxManagementInterface;
