import { EventEmitter } from 'events';
import { HanumanOrganOrchestrator, OrganState } from '../../../hanuman-working/services/HanumanOrganOrchestrator';

// Types pour l'infrastructure de la sandbox
export interface SandboxContainer {
  id: string;
  name: string;
  type: 'development' | 'testing' | 'security' | 'qa' | 'staging';
  status: 'creating' | 'running' | 'stopped' | 'error' | 'destroyed';
  resources: {
    cpu: number;
    memory: number;
    storage: number;
    network: string;
  };
  isolation: {
    networkIsolated: boolean;
    storageEncrypted: boolean;
    processIsolated: boolean;
    securityLevel: 'low' | 'medium' | 'high' | 'maximum';
  };
  createdAt: Date;
  lastActivity: Date;
  agentId?: string;
  organId?: string;
}

export interface SandboxNamespace {
  id: string;
  name: string;
  containers: Map<string, SandboxContainer>;
  resourceLimits: {
    maxCpu: number;
    maxMemory: number;
    maxStorage: number;
    maxContainers: number;
  };
  securityPolicies: string[];
  createdAt: Date;
  owner: string;
}

export interface SandboxNetwork {
  id: string;
  name: string;
  type: 'isolated' | 'restricted' | 'monitored';
  subnet: string;
  allowedConnections: string[];
  blockedConnections: string[];
  monitoring: boolean;
}

export interface SandboxStorage {
  id: string;
  type: 'temporary' | 'persistent' | 'encrypted';
  size: number;
  encryption: {
    enabled: boolean;
    algorithm: string;
    keyId: string;
  };
  autoCleanup: boolean;
  retentionDays: number;
}

/**
 * Infrastructure de base de la Sandbox Hanuman
 * Gère la conteneurisation, l'isolation et les ressources
 */
export class SandboxInfrastructure extends EventEmitter {
  private containers: Map<string, SandboxContainer> = new Map();
  private namespaces: Map<string, SandboxNamespace> = new Map();
  private networks: Map<string, SandboxNetwork> = new Map();
  private storages: Map<string, SandboxStorage> = new Map();
  private orchestrator: HanumanOrganOrchestrator;
  private isInitialized = false;

  constructor(orchestrator: HanumanOrganOrchestrator) {
    super();
    this.orchestrator = orchestrator;
    this.initializeInfrastructure();
  }

  /**
   * Initialise l'infrastructure de base
   */
  private async initializeInfrastructure(): Promise<void> {
    try {
      // Créer les réseaux de base
      await this.createBaseNetworks();
      
      // Créer les espaces de noms par défaut
      await this.createDefaultNamespaces();
      
      // Configurer le monitoring
      await this.setupMonitoring();
      
      this.isInitialized = true;
      this.emit('infrastructure:initialized');
      
      console.log('🏗️ Infrastructure Sandbox Hanuman initialisée');
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation de l\'infrastructure:', error);
      this.emit('infrastructure:error', error);
    }
  }

  /**
   * Crée les réseaux de base pour la sandbox
   */
  private async createBaseNetworks(): Promise<void> {
    const baseNetworks = [
      {
        id: 'sandbox-isolated',
        name: 'Réseau Isolé Sandbox',
        type: 'isolated' as const,
        subnet: '172.20.0.0/16',
        allowedConnections: [],
        blockedConnections: ['*'],
        monitoring: true
      },
      {
        id: 'sandbox-restricted',
        name: 'Réseau Restreint Sandbox',
        type: 'restricted' as const,
        subnet: '172.21.0.0/16',
        allowedConnections: ['hanuman-organs', 'sandbox-services'],
        blockedConnections: ['external'],
        monitoring: true
      },
      {
        id: 'sandbox-monitored',
        name: 'Réseau Surveillé Sandbox',
        type: 'monitored' as const,
        subnet: '172.22.0.0/16',
        allowedConnections: ['*'],
        blockedConnections: [],
        monitoring: true
      }
    ];

    for (const networkConfig of baseNetworks) {
      const network: SandboxNetwork = {
        ...networkConfig
      };
      
      this.networks.set(network.id, network);
      this.emit('network:created', network);
    }
  }

  /**
   * Crée les espaces de noms par défaut
   */
  private async createDefaultNamespaces(): Promise<void> {
    const defaultNamespaces = [
      {
        name: 'development',
        resourceLimits: {
          maxCpu: 4,
          maxMemory: 8192,
          maxStorage: 50000,
          maxContainers: 10
        },
        securityPolicies: ['basic-isolation', 'resource-limits']
      },
      {
        name: 'testing',
        resourceLimits: {
          maxCpu: 8,
          maxMemory: 16384,
          maxStorage: 100000,
          maxContainers: 20
        },
        securityPolicies: ['enhanced-isolation', 'test-monitoring']
      },
      {
        name: 'security',
        resourceLimits: {
          maxCpu: 2,
          maxMemory: 4096,
          maxStorage: 25000,
          maxContainers: 5
        },
        securityPolicies: ['maximum-isolation', 'security-scanning', 'audit-logging']
      }
    ];

    for (const nsConfig of defaultNamespaces) {
      const namespace: SandboxNamespace = {
        id: `sandbox-${nsConfig.name}`,
        name: nsConfig.name,
        containers: new Map(),
        resourceLimits: nsConfig.resourceLimits,
        securityPolicies: nsConfig.securityPolicies,
        createdAt: new Date(),
        owner: 'hanuman-system'
      };
      
      this.namespaces.set(namespace.id, namespace);
      this.emit('namespace:created', namespace);
    }
  }

  /**
   * Configure le monitoring de l'infrastructure
   */
  private async setupMonitoring(): Promise<void> {
    // Monitoring des ressources
    setInterval(() => {
      this.monitorResources();
    }, 30000); // Toutes les 30 secondes

    // Monitoring de la sécurité
    setInterval(() => {
      this.monitorSecurity();
    }, 60000); // Toutes les minutes

    // Nettoyage automatique
    setInterval(() => {
      this.cleanupResources();
    }, 300000); // Toutes les 5 minutes
  }

  /**
   * Crée un nouveau conteneur dans la sandbox
   */
  async createContainer(config: {
    name: string;
    type: SandboxContainer['type'];
    namespace: string;
    agentId?: string;
    organId?: string;
    securityLevel?: SandboxContainer['isolation']['securityLevel'];
  }): Promise<SandboxContainer> {
    const namespace = this.namespaces.get(`sandbox-${config.namespace}`);
    if (!namespace) {
      throw new Error(`Namespace ${config.namespace} non trouvé`);
    }

    // Vérifier les limites de ressources
    if (namespace.containers.size >= namespace.resourceLimits.maxContainers) {
      throw new Error(`Limite de conteneurs atteinte pour le namespace ${config.namespace}`);
    }

    const container: SandboxContainer = {
      id: `container_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: config.name,
      type: config.type,
      status: 'creating',
      resources: {
        cpu: this.calculateCpuAllocation(config.type),
        memory: this.calculateMemoryAllocation(config.type),
        storage: this.calculateStorageAllocation(config.type),
        network: this.selectNetwork(config.type)
      },
      isolation: {
        networkIsolated: true,
        storageEncrypted: config.securityLevel === 'high' || config.securityLevel === 'maximum',
        processIsolated: true,
        securityLevel: config.securityLevel || 'medium'
      },
      createdAt: new Date(),
      lastActivity: new Date(),
      agentId: config.agentId,
      organId: config.organId
    };

    // Créer le stockage chiffré si nécessaire
    if (container.isolation.storageEncrypted) {
      await this.createEncryptedStorage(container.id);
    }

    this.containers.set(container.id, container);
    namespace.containers.set(container.id, container);

    // Démarrer le conteneur
    await this.startContainer(container.id);

    this.emit('container:created', container);
    
    // Notifier l'orchestrateur si un agent/organe est associé
    if (config.agentId || config.organId) {
      this.orchestrator.emit('sandbox:container-ready', {
        containerId: container.id,
        agentId: config.agentId,
        organId: config.organId
      });
    }

    return container;
  }

  /**
   * Démarre un conteneur
   */
  private async startContainer(containerId: string): Promise<void> {
    const container = this.containers.get(containerId);
    if (!container) {
      throw new Error(`Conteneur ${containerId} non trouvé`);
    }

    try {
      container.status = 'running';
      container.lastActivity = new Date();
      
      this.containers.set(containerId, container);
      this.emit('container:started', container);
      
      console.log(`🚀 Conteneur ${container.name} démarré avec succès`);
    } catch (error) {
      container.status = 'error';
      this.containers.set(containerId, container);
      this.emit('container:error', { container, error });
      throw error;
    }
  }

  /**
   * Calcule l'allocation CPU basée sur le type de conteneur
   */
  private calculateCpuAllocation(type: SandboxContainer['type']): number {
    const allocations = {
      development: 1,
      testing: 2,
      security: 1,
      qa: 2,
      staging: 3
    };
    return allocations[type];
  }

  /**
   * Calcule l'allocation mémoire basée sur le type de conteneur
   */
  private calculateMemoryAllocation(type: SandboxContainer['type']): number {
    const allocations = {
      development: 1024,
      testing: 2048,
      security: 512,
      qa: 2048,
      staging: 4096
    };
    return allocations[type];
  }

  /**
   * Calcule l'allocation stockage basée sur le type de conteneur
   */
  private calculateStorageAllocation(type: SandboxContainer['type']): number {
    const allocations = {
      development: 5000,
      testing: 10000,
      security: 2500,
      qa: 10000,
      staging: 20000
    };
    return allocations[type];
  }

  /**
   * Sélectionne le réseau approprié pour le type de conteneur
   */
  private selectNetwork(type: SandboxContainer['type']): string {
    const networkMappings = {
      development: 'sandbox-monitored',
      testing: 'sandbox-restricted',
      security: 'sandbox-isolated',
      qa: 'sandbox-restricted',
      staging: 'sandbox-monitored'
    };
    return networkMappings[type];
  }

  /**
   * Crée un stockage chiffré pour un conteneur
   */
  private async createEncryptedStorage(containerId: string): Promise<void> {
    const storage: SandboxStorage = {
      id: `storage_${containerId}`,
      type: 'encrypted',
      size: 10000,
      encryption: {
        enabled: true,
        algorithm: 'AES-256-GCM',
        keyId: `key_${containerId}`
      },
      autoCleanup: true,
      retentionDays: 7
    };

    this.storages.set(storage.id, storage);
    this.emit('storage:created', storage);
  }

  /**
   * Surveille les ressources système
   */
  private monitorResources(): void {
    const totalContainers = this.containers.size;
    const runningContainers = Array.from(this.containers.values())
      .filter(c => c.status === 'running').length;

    this.emit('monitoring:resources', {
      totalContainers,
      runningContainers,
      timestamp: new Date()
    });
  }

  /**
   * Surveille la sécurité
   */
  private monitorSecurity(): void {
    // Vérifier les conteneurs suspects
    const suspiciousContainers = Array.from(this.containers.values())
      .filter(c => {
        const inactiveTime = Date.now() - c.lastActivity.getTime();
        return inactiveTime > 3600000; // Plus d'1 heure d'inactivité
      });

    if (suspiciousContainers.length > 0) {
      this.emit('security:suspicious-activity', suspiciousContainers);
    }
  }

  /**
   * Nettoie les ressources inutilisées
   */
  private cleanupResources(): void {
    const now = Date.now();
    const cleanupThreshold = 24 * 60 * 60 * 1000; // 24 heures

    // Nettoyer les conteneurs anciens
    for (const [id, container] of this.containers) {
      if (container.status === 'stopped' && 
          now - container.lastActivity.getTime() > cleanupThreshold) {
        this.destroyContainer(id);
      }
    }

    // Nettoyer les stockages temporaires
    for (const [id, storage] of this.storages) {
      if (storage.autoCleanup && storage.type === 'temporary') {
        const retentionMs = storage.retentionDays * 24 * 60 * 60 * 1000;
        // Note: Nous aurions besoin d'une date de création pour le stockage
        // Pour l'instant, on suppose qu'il faut le nettoyer
        this.cleanupStorage(id);
      }
    }
  }

  /**
   * Détruit un conteneur
   */
  async destroyContainer(containerId: string): Promise<void> {
    const container = this.containers.get(containerId);
    if (!container) return;

    container.status = 'destroyed';
    this.containers.delete(containerId);

    // Supprimer du namespace
    for (const namespace of this.namespaces.values()) {
      namespace.containers.delete(containerId);
    }

    // Nettoyer le stockage associé
    const storageId = `storage_${containerId}`;
    if (this.storages.has(storageId)) {
      this.cleanupStorage(storageId);
    }

    this.emit('container:destroyed', container);
  }

  /**
   * Nettoie un stockage
   */
  private cleanupStorage(storageId: string): void {
    const storage = this.storages.get(storageId);
    if (storage) {
      this.storages.delete(storageId);
      this.emit('storage:cleaned', storage);
    }
  }

  /**
   * Obtient les statistiques de l'infrastructure
   */
  getInfrastructureStats() {
    return {
      containers: {
        total: this.containers.size,
        running: Array.from(this.containers.values()).filter(c => c.status === 'running').length,
        stopped: Array.from(this.containers.values()).filter(c => c.status === 'stopped').length,
        error: Array.from(this.containers.values()).filter(c => c.status === 'error').length
      },
      namespaces: this.namespaces.size,
      networks: this.networks.size,
      storages: this.storages.size,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Obtient la liste des conteneurs
   */
  getContainers(): SandboxContainer[] {
    return Array.from(this.containers.values());
  }

  /**
   * Obtient un conteneur par ID
   */
  getContainer(containerId: string): SandboxContainer | undefined {
    return this.containers.get(containerId);
  }

  /**
   * Obtient les espaces de noms
   */
  getNamespaces(): SandboxNamespace[] {
    return Array.from(this.namespaces.values());
  }
}
