#!/usr/bin/env ts-node

import { SecurityTestRunner, SecurityTestSuite } from '../tests/security_tests';
import { SecurityAgent } from '../../../agents/security/src/core/SecurityAgent';
import { VulnerabilityScanner } from '../security/vulnerability_scanner';
import { SecurityPolicies } from '../security/security_policies';
import { SandboxSecurity } from '../security/sandbox_security';
import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';

/**
 * Script d'exécution des tests de sécurité pour le Sprint 4
 * Valide le bon fonctionnement de tous les composants de validation sécurité
 */

class SecurityTestExecutor {
  private testRunner: SecurityTestRunner | null = null;

  /**
   * Initialise et exécute tous les tests de sécurité
   */
  async executeSecurityTests(): Promise<void> {
    console.log('🚀 DÉMARRAGE DES TESTS DE SÉCURITÉ - SPRINT 4');
    console.log('==============================================');
    console.log('🛡️ Validation des composants de sécurité <PERSON>uman Sandbox');
    console.log('📅 Sprint 4: Interface de Validation Sécurité');
    console.log('');

    try {
      // Étape 1: Initialisation des composants
      console.log('📋 Étape 1: Initialisation des composants de sécurité...');
      const components = await this.initializeSecurityComponents();
      console.log('✅ Composants initialisés avec succès');

      // Étape 2: Création du runner de tests
      console.log('📋 Étape 2: Création du runner de tests...');
      this.testRunner = new SecurityTestRunner(
        components.securityAgent,
        components.vulnerabilityScanner,
        components.securityPolicies,
        components.sandboxSecurity
      );
      console.log('✅ Runner de tests créé');

      // Étape 3: Exécution des tests
      console.log('📋 Étape 3: Exécution des tests de sécurité...');
      const testSuite = await this.testRunner.runAllSecurityTests();

      // Étape 4: Analyse des résultats
      console.log('📋 Étape 4: Analyse des résultats...');
      await this.analyzeTestResults(testSuite);

      // Étape 5: Génération du rapport
      console.log('📋 Étape 5: Génération du rapport...');
      await this.generateTestReport(testSuite);

      console.log('🎉 Tests de sécurité terminés avec succès!');

    } catch (error) {
      console.error('❌ Erreur lors de l\'exécution des tests:', error);
      process.exit(1);
    }
  }

  /**
   * Initialise tous les composants de sécurité nécessaires
   */
  private async initializeSecurityComponents(): Promise<{
    securityAgent: SecurityAgent;
    vulnerabilityScanner: VulnerabilityScanner;
    securityPolicies: SecurityPolicies;
    sandboxSecurity: SandboxSecurity;
  }> {
    // Configuration de base pour les tests
    const testConfig = {
      security: {
        level: 'high',
        scanners: {
          enabled: true,
          timeout: 30000
        },
        policies: {
          strict: true,
          compliance: ['owasp', 'cis']
        }
      }
    };

    // Initialiser l'infrastructure sandbox
    const infrastructure = new SandboxInfrastructure();
    await infrastructure.initialize();

    // Initialiser l'agent de sécurité
    const securityAgent = new SecurityAgent(testConfig.security);
    await securityAgent.initialize();

    // Initialiser le scanner de vulnérabilités
    const vulnerabilityScanner = new VulnerabilityScanner();
    await vulnerabilityScanner.initialize();

    // Initialiser les politiques de sécurité
    const securityPolicies = new SecurityPolicies();
    await securityPolicies.initialize();

    // Initialiser la sécurité sandbox
    const sandboxSecurity = new SandboxSecurity(infrastructure);
    await sandboxSecurity.initialize();

    return {
      securityAgent,
      vulnerabilityScanner,
      securityPolicies,
      sandboxSecurity
    };
  }

  /**
   * Analyse les résultats des tests
   */
  private async analyzeTestResults(testSuite: SecurityTestSuite): Promise<void> {
    console.log('\n🔍 ANALYSE DES RÉSULTATS');
    console.log('========================');

    // Calcul des métriques
    const successRate = (testSuite.passedTests / testSuite.totalTests) * 100;
    const averageTestDuration = testSuite.totalDuration / testSuite.totalTests;

    console.log(`📊 Taux de réussite: ${successRate.toFixed(1)}%`);
    console.log(`⏱️ Durée moyenne par test: ${averageTestDuration.toFixed(0)}ms`);
    console.log(`🎯 Objectif Sprint 4: ${testSuite.success ? 'ATTEINT' : 'NON ATTEINT'}`);

    // Analyse des échecs
    if (testSuite.failedTests > 0) {
      console.log('\n⚠️ TESTS ÉCHOUÉS:');
      testSuite.tests
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`❌ ${test.testName}: ${test.details}`);
          if (test.errors) {
            test.errors.forEach(error => console.log(`   • ${error}`));
          }
        });
    }

    // Recommandations
    console.log('\n💡 RECOMMANDATIONS:');
    if (successRate >= 90) {
      console.log('✅ Excellente couverture de tests - Sprint 4 validé');
    } else if (successRate >= 75) {
      console.log('⚠️ Couverture acceptable - Quelques améliorations nécessaires');
    } else {
      console.log('❌ Couverture insuffisante - Révision des composants requise');
    }

    // Validation des critères Sprint 4
    this.validateSprint4Criteria(testSuite);
  }

  /**
   * Valide les critères spécifiques du Sprint 4
   */
  private validateSprint4Criteria(testSuite: SecurityTestSuite): void {
    console.log('\n🎯 VALIDATION CRITÈRES SPRINT 4');
    console.log('===============================');

    const criteria = [
      {
        name: 'Agent Validateur Sécurité',
        tests: ['Initialisation Agent Validateur', 'Demande de Validation', 'Workflow de Validation'],
        required: true
      },
      {
        name: 'Scanner de Vulnérabilités',
        tests: ['Scanner de Vulnérabilités', 'Détection Vulnérabilités Code', 'Détection Vulnérabilités Conteneur'],
        required: true
      },
      {
        name: 'Gestionnaire de Politiques',
        tests: ['Chargement Politiques', 'Détection Violations', 'Validation Conformité'],
        required: true
      },
      {
        name: 'Tests de Sécurité',
        tests: ['Tests de Pénétration', 'Intégration Sécurité', 'Audit Trail'],
        required: false
      }
    ];

    criteria.forEach(criterion => {
      const criterionTests = testSuite.tests.filter(test => 
        criterion.tests.includes(test.testName)
      );
      
      const passed = criterionTests.every(test => test.passed);
      const status = passed ? '✅' : '❌';
      const requirement = criterion.required ? '(REQUIS)' : '(OPTIONNEL)';
      
      console.log(`${status} ${criterion.name} ${requirement}`);
      
      if (!passed && criterion.required) {
        console.log(`   ⚠️ Critère requis non satisfait`);
      }
    });
  }

  /**
   * Génère un rapport de test détaillé
   */
  private async generateTestReport(testSuite: SecurityTestSuite): Promise<void> {
    const timestamp = new Date().toISOString();
    const report = {
      sprint: 'Sprint 4 - Validation Sécurité',
      timestamp,
      summary: {
        totalTests: testSuite.totalTests,
        passedTests: testSuite.passedTests,
        failedTests: testSuite.failedTests,
        successRate: (testSuite.passedTests / testSuite.totalTests) * 100,
        totalDuration: testSuite.totalDuration,
        success: testSuite.success
      },
      tests: testSuite.tests,
      recommendations: this.generateRecommendations(testSuite)
    };

    // Sauvegarder le rapport (simulation)
    console.log('\n📄 RAPPORT GÉNÉRÉ');
    console.log('=================');
    console.log(`📁 Fichier: security_test_report_${timestamp.split('T')[0]}.json`);
    console.log(`📊 Résumé: ${report.summary.passedTests}/${report.summary.totalTests} tests réussis`);
    console.log(`🎯 Statut: ${report.summary.success ? 'SUCCÈS' : 'ÉCHEC'}`);

    // Afficher les recommandations
    if (report.recommendations.length > 0) {
      console.log('\n📝 Recommandations:');
      report.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
  }

  /**
   * Génère des recommandations basées sur les résultats
   */
  private generateRecommendations(testSuite: SecurityTestSuite): string[] {
    const recommendations: string[] = [];

    if (testSuite.failedTests > 0) {
      recommendations.push('Corriger les tests échoués avant de passer au Sprint 5');
    }

    if (testSuite.totalDuration > 10000) {
      recommendations.push('Optimiser les performances des tests de sécurité');
    }

    const successRate = (testSuite.passedTests / testSuite.totalTests) * 100;
    if (successRate < 90) {
      recommendations.push('Améliorer la couverture de tests pour atteindre 90%');
    }

    if (testSuite.success) {
      recommendations.push('Sprint 4 validé - Prêt pour le Sprint 5 (Centre de Validation QA)');
    }

    return recommendations;
  }
}

/**
 * Point d'entrée principal
 */
async function main(): Promise<void> {
  const executor = new SecurityTestExecutor();
  await executor.executeSecurityTests();
}

// Exécution si appelé directement
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Erreur fatale:', error);
    process.exit(1);
  });
}

export { SecurityTestExecutor };
export default main;
