import { EventEmitter } from 'events';
import { VSCodeInstance } from './vscode_server_manager';

// Types pour l'intégration Roo Coder
export interface RooCoderTemplate {
  id: string;
  name: string;
  description: string;
  category: 'agent' | 'organ' | 'interface' | 'service' | 'test' | 'utility';
  language: 'typescript' | 'javascript' | 'react' | 'python' | 'markdown';
  template: string;
  variables: RooCoderVariable[];
  dependencies?: string[];
  examples?: string[];
}

export interface RooCoderVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  defaultValue?: any;
  validation?: string;
}

export interface RooCoderPrompt {
  id: string;
  name: string;
  description: string;
  context: 'hanuman' | 'agent' | 'organ' | 'general';
  prompt: string;
  parameters: string[];
  examples: RooCoderExample[];
}

export interface RooCoderExample {
  input: string;
  output: string;
  explanation: string;
}

export interface RooCoderConfig {
  apiKey?: string;
  model: 'gpt-4' | 'claude-3' | 'codellama' | 'custom';
  temperature: number;
  maxTokens: number;
  contextWindow: number;
  customEndpoint?: string;
  hanumanContext: {
    architecture: string;
    patterns: string[];
    conventions: string[];
    bestPractices: string[];
  };
}

/**
 * Intégration Roo Coder spécialisée pour les agents Hanuman
 * Fournit des templates, prompts et configurations optimisés
 */
export class RooCoderIntegration extends EventEmitter {
  private templates: Map<string, RooCoderTemplate> = new Map();
  private prompts: Map<string, RooCoderPrompt> = new Map();
  private config: RooCoderConfig;

  constructor() {
    super();
    this.initializeConfig();
    this.loadHanumanTemplates();
    this.loadHanumanPrompts();
  }

  /**
   * Initialise la configuration Roo Coder pour Hanuman
   */
  private initializeConfig(): void {
    this.config = {
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 4000,
      contextWindow: 8000,
      hanumanContext: {
        architecture: 'microservices-organs-agents',
        patterns: [
          'Event-driven architecture',
          'Organ-based modularity',
          'Agent autonomy',
          'Neural signal communication',
          'Sandbox isolation',
          'Security-first design'
        ],
        conventions: [
          'TypeScript strict mode',
          'React functional components',
          'Event emitter patterns',
          'Async/await for promises',
          'Descriptive naming',
          'Comprehensive error handling'
        ],
        bestPractices: [
          'Test-driven development',
          'Security by design',
          'Performance optimization',
          'Accessibility compliance',
          'Documentation completeness',
          'Code reusability'
        ]
      }
    };
  }

  /**
   * Charge les templates spécialisés Hanuman
   */
  private loadHanumanTemplates(): void {
    const templates: RooCoderTemplate[] = [
      {
        id: 'hanuman-agent',
        name: 'Agent Hanuman',
        description: 'Template pour créer un nouvel agent Hanuman',
        category: 'agent',
        language: 'typescript',
        template: `
import { EventEmitter } from 'events';
import { HanumanOrganOrchestrator } from '../services/HanumanOrganOrchestrator';

export interface {{agentName}}Config {
  // Configuration pour {{agentName}}
  {{#each configFields}}
  {{name}}: {{type}};
  {{/each}}
}

export interface {{agentName}}State {
  // État de l'agent {{agentName}}
  isActive: boolean;
  lastActivity: Date;
  {{#each stateFields}}
  {{name}}: {{type}};
  {{/each}}
}

/**
 * Agent {{agentName}} - {{description}}
 * Responsabilités: {{responsibilities}}
 */
export class {{agentName}} extends EventEmitter {
  private config: {{agentName}}Config;
  private state: {{agentName}}State;
  private orchestrator: HanumanOrganOrchestrator;

  constructor(orchestrator: HanumanOrganOrchestrator, config: {{agentName}}Config) {
    super();
    this.orchestrator = orchestrator;
    this.config = config;
    this.initializeState();
    this.setupEventHandlers();
  }

  private initializeState(): void {
    this.state = {
      isActive: false,
      lastActivity: new Date(),
      {{#each stateFields}}
      {{name}}: {{defaultValue}},
      {{/each}}
    };
  }

  private setupEventHandlers(): void {
    this.orchestrator.on('neural:signal-received', this.handleNeuralSignal.bind(this));
  }

  private handleNeuralSignal(signal: any): void {
    // Traitement des signaux neuraux
    this.state.lastActivity = new Date();
    this.emit('agent:signal-processed', { agent: '{{agentName}}', signal });
  }

  async activate(): Promise<void> {
    this.state.isActive = true;
    this.emit('agent:activated', { agent: '{{agentName}}' });
    console.log('🤖 Agent {{agentName}} activé');
  }

  async deactivate(): Promise<void> {
    this.state.isActive = false;
    this.emit('agent:deactivated', { agent: '{{agentName}}' });
    console.log('😴 Agent {{agentName}} désactivé');
  }

  getState(): {{agentName}}State {
    return { ...this.state };
  }

  updateConfig(newConfig: Partial<{{agentName}}Config>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('agent:config-updated', { agent: '{{agentName}}', config: this.config });
  }
}

export default {{agentName}};
        `,
        variables: [
          {
            name: 'agentName',
            type: 'string',
            description: 'Nom de l\'agent (PascalCase)',
            required: true
          },
          {
            name: 'description',
            type: 'string',
            description: 'Description de l\'agent',
            required: true
          },
          {
            name: 'responsibilities',
            type: 'string',
            description: 'Responsabilités de l\'agent',
            required: true
          },
          {
            name: 'configFields',
            type: 'array',
            description: 'Champs de configuration',
            required: false,
            defaultValue: []
          },
          {
            name: 'stateFields',
            type: 'array',
            description: 'Champs d\'état',
            required: false,
            defaultValue: []
          }
        ],
        dependencies: ['events', '../services/HanumanOrganOrchestrator'],
        examples: [
          'Agent Frontend pour interfaces utilisateur',
          'Agent Backend pour services API',
          'Agent Security pour validation sécurité'
        ]
      },
      {
        id: 'hanuman-organ',
        name: 'Organe Hanuman',
        description: 'Template pour créer un nouvel organe Hanuman',
        category: 'organ',
        language: 'typescript',
        template: `
import { EventEmitter } from 'events';

export interface {{organName}}Capabilities {
  // Capacités de l'organe {{organName}}
  {{#each capabilities}}
  {{name}}: {{type}};
  {{/each}}
}

export interface {{organName}}Memory {
  // Mémoire de l'organe {{organName}}
  shortTerm: Map<string, any>;
  longTerm: Map<string, any>;
  working: any[];
}

/**
 * Organe {{organName}} - {{description}}
 * Type: {{organType}}
 * Fonction: {{function}}
 */
export class {{organName}} extends EventEmitter {
  private capabilities: {{organName}}Capabilities;
  private memory: {{organName}}Memory;
  private isActive = false;

  constructor(capabilities: {{organName}}Capabilities) {
    super();
    this.capabilities = capabilities;
    this.initializeMemory();
    this.activate();
  }

  private initializeMemory(): void {
    this.memory = {
      shortTerm: new Map(),
      longTerm: new Map(),
      working: []
    };
  }

  async activate(): Promise<void> {
    this.isActive = true;
    this.emit('organ:activated', { organ: '{{organName}}' });
    console.log('🧠 Organe {{organName}} activé');
  }

  async process(input: any): Promise<any> {
    if (!this.isActive) {
      throw new Error('Organe {{organName}} non actif');
    }

    // Traitement spécialisé de l'organe
    this.memory.working.push(input);
    
    const result = await this.performProcessing(input);
    
    this.memory.shortTerm.set(Date.now().toString(), result);
    this.emit('organ:processed', { organ: '{{organName}}', input, result });
    
    return result;
  }

  private async performProcessing(input: any): Promise<any> {
    // Implémentation spécifique du traitement
    return input; // Placeholder
  }

  storeInLongTermMemory(key: string, value: any): void {
    this.memory.longTerm.set(key, value);
    this.emit('organ:memory-stored', { organ: '{{organName}}', key, value });
  }

  retrieveFromMemory(key: string): any {
    return this.memory.longTerm.get(key) || this.memory.shortTerm.get(key);
  }

  getCapabilities(): {{organName}}Capabilities {
    return { ...this.capabilities };
  }

  getMemoryStats() {
    return {
      shortTermSize: this.memory.shortTerm.size,
      longTermSize: this.memory.longTerm.size,
      workingSize: this.memory.working.length
    };
  }
}

export default {{organName}};
        `,
        variables: [
          {
            name: 'organName',
            type: 'string',
            description: 'Nom de l\'organe (PascalCase)',
            required: true
          },
          {
            name: 'description',
            type: 'string',
            description: 'Description de l\'organe',
            required: true
          },
          {
            name: 'organType',
            type: 'string',
            description: 'Type d\'organe (cortex, système, etc.)',
            required: true
          },
          {
            name: 'function',
            type: 'string',
            description: 'Fonction principale de l\'organe',
            required: true
          },
          {
            name: 'capabilities',
            type: 'array',
            description: 'Capacités de l\'organe',
            required: false,
            defaultValue: []
          }
        ]
      },
      {
        id: 'hanuman-interface',
        name: 'Interface React Hanuman',
        description: 'Template pour créer une interface React pour Hanuman',
        category: 'interface',
        language: 'react',
        template: `
import React, { useState, useEffect, useCallback } from 'react';

// Types pour l'interface {{componentName}}
export interface {{componentName}}Props {
  {{#each props}}
  {{name}}: {{type}};
  {{/each}}
  onAction?: (action: string, data: any) => void;
  onError?: (error: Error) => void;
}

export interface {{componentName}}State {
  {{#each stateFields}}
  {{name}}: {{type}};
  {{/each}}
  isLoading: boolean;
  error: string | null;
}

/**
 * Interface {{componentName}} - {{description}}
 * Composant React pour {{purpose}}
 */
export const {{componentName}}: React.FC<{{componentName}}Props> = ({
  {{#each props}}
  {{name}},
  {{/each}}
  onAction,
  onError
}) => {
  const [state, setState] = useState<{{componentName}}State>({
    {{#each stateFields}}
    {{name}}: {{defaultValue}},
    {{/each}}
    isLoading: false,
    error: null
  });

  useEffect(() => {
    initializeComponent();
  }, []);

  const initializeComponent = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      // Initialisation du composant
      await performInitialization();
      
      setState(prev => ({ ...prev, isLoading: false }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Erreur inconnue' 
      }));
      onError?.(error as Error);
    }
  }, []);

  const performInitialization = async (): Promise<void> => {
    // Logique d'initialisation
  };

  const handleAction = useCallback((action: string, data?: any) => {
    try {
      // Traitement de l'action
      onAction?.(action, data);
    } catch (error) {
      onError?.(error as Error);
    }
  }, [onAction, onError]);

  if (state.isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Chargement...</span>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <span className="text-red-600 text-lg">⚠️</span>
          <div className="ml-3">
            <h3 className="text-red-800 font-medium">Erreur</h3>
            <p className="text-red-600 text-sm">{state.error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="hanuman-interface {{kebabCase componentName}}">
      <div className="bg-white rounded-lg shadow border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">
            {{displayName}}
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            {{description}}
          </p>
        </div>
        
        <div className="p-6">
          {/* Contenu principal de l'interface */}
          <div className="space-y-4">
            {{#each sections}}
            <div className="{{sectionClass}}">
              <h3 className="text-lg font-medium text-gray-700 mb-3">
                {{title}}
              </h3>
              {/* Contenu de la section {{title}} */}
            </div>
            {{/each}}
          </div>
        </div>
        
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => handleAction('cancel')}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Annuler
            </button>
            <button
              onClick={() => handleAction('submit')}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Valider
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default {{componentName}};
        `,
        variables: [
          {
            name: 'componentName',
            type: 'string',
            description: 'Nom du composant (PascalCase)',
            required: true
          },
          {
            name: 'description',
            type: 'string',
            description: 'Description du composant',
            required: true
          },
          {
            name: 'purpose',
            type: 'string',
            description: 'Objectif du composant',
            required: true
          },
          {
            name: 'displayName',
            type: 'string',
            description: 'Nom affiché dans l\'interface',
            required: true
          },
          {
            name: 'props',
            type: 'array',
            description: 'Props du composant',
            required: false,
            defaultValue: []
          },
          {
            name: 'stateFields',
            type: 'array',
            description: 'Champs d\'état',
            required: false,
            defaultValue: []
          },
          {
            name: 'sections',
            type: 'array',
            description: 'Sections de l\'interface',
            required: false,
            defaultValue: []
          }
        ]
      }
    ];

    templates.forEach(template => {
      this.templates.set(template.id, template);
    });

    console.log(`📝 ${templates.length} templates Hanuman chargés`);
  }

  /**
   * Charge les prompts spécialisés Hanuman
   */
  private loadHanumanPrompts(): void {
    const prompts: RooCoderPrompt[] = [
      {
        id: 'create-agent',
        name: 'Créer un Agent Hanuman',
        description: 'Génère un nouvel agent avec les capacités spécifiées',
        context: 'agent',
        prompt: `
Créer un nouvel agent Hanuman avec les spécifications suivantes :

Nom: {agentName}
Responsabilités: {responsibilities}
Capacités: {capabilities}
Intégrations: {integrations}

L'agent doit :
1. Hériter de EventEmitter pour la communication
2. S'intégrer avec HanumanOrganOrchestrator
3. Implémenter les patterns de l'architecture Hanuman
4. Inclure une gestion d'état robuste
5. Fournir des méthodes d'activation/désactivation
6. Émettre des événements appropriés
7. Gérer les erreurs de manière élégante
8. Être entièrement typé avec TypeScript

Respecter les conventions de nommage et les bonnes pratiques Hanuman.
        `,
        parameters: ['agentName', 'responsibilities', 'capabilities', 'integrations'],
        examples: [
          {
            input: 'agentName: "SecurityValidator", responsibilities: "Validation sécurité", capabilities: "scan, audit, report"',
            output: 'Agent complet avec validation sécurité',
            explanation: 'Génère un agent spécialisé dans la validation sécurité'
          }
        ]
      },
      {
        id: 'enhance-organ',
        name: 'Améliorer un Organe',
        description: 'Améliore un organe existant avec de nouvelles fonctionnalités',
        context: 'organ',
        prompt: `
Améliorer l'organe Hanuman existant avec les nouvelles fonctionnalités :

Organe: {organName}
Nouvelles capacités: {newCapabilities}
Améliorations: {improvements}
Compatibilité: {compatibility}

L'amélioration doit :
1. Maintenir la compatibilité avec l'existant
2. Ajouter les nouvelles capacités de manière élégante
3. Respecter l'architecture des organes Hanuman
4. Inclure des tests pour les nouvelles fonctionnalités
5. Mettre à jour la documentation
6. Optimiser les performances si possible
7. Gérer la migration des données si nécessaire

Fournir le code modifié et les instructions de migration.
        `,
        parameters: ['organName', 'newCapabilities', 'improvements', 'compatibility'],
        examples: [
          {
            input: 'organName: "CortexCreatif", newCapabilities: "generation_images", improvements: "performance"',
            output: 'Organe amélioré avec génération d\'images',
            explanation: 'Ajoute la génération d\'images au cortex créatif'
          }
        ]
      }
    ];

    prompts.forEach(prompt => {
      this.prompts.set(prompt.id, prompt);
    });

    console.log(`💬 ${prompts.length} prompts Hanuman chargés`);
  }

  /**
   * Configure Roo Coder pour une instance VS Code
   */
  async configureForInstance(instance: VSCodeInstance): Promise<void> {
    const agentContext = this.buildAgentContext(instance);
    
    const config = {
      ...this.config,
      instanceId: instance.id,
      agentContext,
      templates: Array.from(this.templates.values()),
      prompts: Array.from(this.prompts.values())
    };

    // Appliquer la configuration à l'instance
    instance.config.settings['rooCoder.hanumanConfig'] = config;
    
    this.emit('roo-coder:configured', { instance, config });
    console.log(`🤖 Roo Coder configuré pour ${instance.agentId || instance.organId}`);
  }

  /**
   * Construit le contexte spécifique à l'agent
   */
  private buildAgentContext(instance: VSCodeInstance): any {
    return {
      agentId: instance.agentId,
      organId: instance.organId,
      projectType: 'hanuman-ecosystem',
      architecture: this.config.hanumanContext.architecture,
      availableTemplates: Array.from(this.templates.keys()),
      availablePrompts: Array.from(this.prompts.keys()),
      workspaceStructure: {
        src: 'Code source principal',
        tests: 'Tests automatisés',
        docs: 'Documentation',
        config: 'Configuration',
        types: 'Définitions TypeScript'
      }
    };
  }

  /**
   * Obtient un template par ID
   */
  getTemplate(templateId: string): RooCoderTemplate | undefined {
    return this.templates.get(templateId);
  }

  /**
   * Obtient tous les templates
   */
  getTemplates(): RooCoderTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Obtient les templates par catégorie
   */
  getTemplatesByCategory(category: RooCoderTemplate['category']): RooCoderTemplate[] {
    return Array.from(this.templates.values())
      .filter(t => t.category === category);
  }

  /**
   * Obtient un prompt par ID
   */
  getPrompt(promptId: string): RooCoderPrompt | undefined {
    return this.prompts.get(promptId);
  }

  /**
   * Obtient tous les prompts
   */
  getPrompts(): RooCoderPrompt[] {
    return Array.from(this.prompts.values());
  }

  /**
   * Ajoute un template personnalisé
   */
  addCustomTemplate(template: RooCoderTemplate): void {
    this.templates.set(template.id, template);
    this.emit('template:added', template);
  }

  /**
   * Ajoute un prompt personnalisé
   */
  addCustomPrompt(prompt: RooCoderPrompt): void {
    this.prompts.set(prompt.id, prompt);
    this.emit('prompt:added', prompt);
  }

  /**
   * Met à jour la configuration
   */
  updateConfig(newConfig: Partial<RooCoderConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config:updated', this.config);
  }

  /**
   * Obtient la configuration actuelle
   */
  getConfig(): RooCoderConfig {
    return { ...this.config };
  }
}

export default RooCoderIntegration;
