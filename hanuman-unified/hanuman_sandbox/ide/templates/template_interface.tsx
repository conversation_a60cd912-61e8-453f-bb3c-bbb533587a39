import React, { useState, useEffect, useCallback } from 'react';
import { TemplateManager, ProjectTemplate, TemplateGenerationOptions } from './template_manager';

// Types pour l'interface de templates
export interface TemplateInterfaceProps {
  templateManager: TemplateManager;
  onProjectGenerated?: (projectPath: string, files: string[]) => void;
  onError?: (error: Error) => void;
}

export interface TemplateFormData {
  templateId: string;
  projectName: string;
  outputPath: string;
  variables: Record<string, any>;
  overwrite: boolean;
}

export interface TemplatePreview {
  template: ProjectTemplate;
  estimatedFiles: number;
  estimatedSize: string;
  generationTime: string;
}

/**
 * Interface de Gestion des Templates Hanuman
 * Permet de sélectionner, configurer et générer des projets à partir de templates
 */
export const TemplateInterface: React.FC<TemplateInterfaceProps> = ({
  templateManager,
  onProjectGenerated,
  onError
}) => {
  const [templates, setTemplates] = useState<ProjectTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ProjectTemplate | null>(null);
  const [formData, setFormData] = useState<TemplateFormData>({
    templateId: '',
    projectName: '',
    outputPath: '/workspace/projects',
    variables: {},
    overwrite: false
  });
  const [preview, setPreview] = useState<TemplatePreview | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState<'browse' | 'configure' | 'preview' | 'generate'>('browse');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  useEffect(() => {
    loadTemplates();
    
    templateManager.on('template:added', loadTemplates);
    templateManager.on('generation:started', handleGenerationStarted);
    templateManager.on('generation:completed', handleGenerationCompleted);
    templateManager.on('generation:error', handleGenerationError);

    return () => {
      templateManager.off('template:added', loadTemplates);
      templateManager.off('generation:started', handleGenerationStarted);
      templateManager.off('generation:completed', handleGenerationCompleted);
      templateManager.off('generation:error', handleGenerationError);
    };
  }, [templateManager]);

  const loadTemplates = useCallback(() => {
    const allTemplates = templateManager.getTemplates();
    setTemplates(allTemplates);
  }, [templateManager]);

  const handleGenerationStarted = useCallback(() => {
    setIsGenerating(true);
  }, []);

  const handleGenerationCompleted = useCallback((data: any) => {
    setIsGenerating(false);
    onProjectGenerated?.(data.options.outputPath, data.generatedFiles);
  }, [onProjectGenerated]);

  const handleGenerationError = useCallback((data: any) => {
    setIsGenerating(false);
    onError?.(data.error);
  }, [onError]);

  const handleTemplateSelect = (template: ProjectTemplate) => {
    setSelectedTemplate(template);
    setFormData(prev => ({
      ...prev,
      templateId: template.id,
      projectName: `nouveau-${template.category}`,
      variables: {}
    }));
    setActiveTab('configure');
    generatePreview(template);
  };

  const generatePreview = (template: ProjectTemplate) => {
    const estimatedFiles = template.files.length + template.structure.rootFiles.length;
    const estimatedSize = `${(estimatedFiles * 2.5).toFixed(1)} KB`;
    const generationTime = `${Math.max(1, Math.ceil(estimatedFiles / 10))} secondes`;

    setPreview({
      template,
      estimatedFiles,
      estimatedSize,
      generationTime
    });
  };

  const handleVariableChange = (variableName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      variables: {
        ...prev.variables,
        [variableName]: value
      }
    }));
  };

  const validateForm = (): string[] => {
    const errors: string[] = [];

    if (!formData.projectName.trim()) {
      errors.push('Le nom du projet est requis');
    }

    if (!formData.outputPath.trim()) {
      errors.push('Le chemin de sortie est requis');
    }

    if (selectedTemplate) {
      const templateErrors = templateManager.validateTemplateVariables(
        selectedTemplate.id,
        formData.variables
      );
      errors.push(...templateErrors);
    }

    return errors;
  };

  const handleGenerate = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      onError?.(new Error(`Erreurs de validation: ${errors.join(', ')}`));
      return;
    }

    try {
      const options: TemplateGenerationOptions = {
        templateId: formData.templateId,
        projectName: formData.projectName,
        outputPath: formData.outputPath,
        variables: formData.variables,
        overwrite: formData.overwrite,
        dryRun: false
      };

      await templateManager.generateProject(options);
    } catch (error) {
      onError?.(error as Error);
    }
  };

  const getFilteredTemplates = () => {
    if (categoryFilter === 'all') {
      return templates;
    }
    return templates.filter(t => t.category === categoryFilter);
  };

  const getCategories = () => {
    const categories = new Set(templates.map(t => t.category));
    return Array.from(categories);
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      agent: '🤖',
      organ: '🧠',
      interface: '🖥️',
      service: '⚙️',
      microservice: '🔧',
      'full-project': '📦'
    };
    return icons[category as keyof typeof icons] || '📄';
  };

  const renderBrowseTab = () => (
    <div className="space-y-6">
      {/* Filtres */}
      <div className="flex items-center space-x-4">
        <label className="text-sm font-medium text-gray-700">Catégorie:</label>
        <select
          value={categoryFilter}
          onChange={(e) => setCategoryFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">Toutes les catégories</option>
          {getCategories().map(category => (
            <option key={category} value={category}>
              {getCategoryIcon(category)} {category}
            </option>
          ))}
        </select>
      </div>

      {/* Liste des templates */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {getFilteredTemplates().map(template => (
          <div
            key={template.id}
            className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleTemplateSelect(template)}
          >
            <div className="flex items-center mb-3">
              <span className="text-2xl mr-3">{getCategoryIcon(template.category)}</span>
              <div>
                <h3 className="font-semibold text-gray-800">{template.name}</h3>
                <p className="text-sm text-gray-500">v{template.version}</p>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 mb-3">{template.description}</p>
            
            <div className="flex flex-wrap gap-1 mb-3">
              {template.tags.slice(0, 3).map(tag => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                >
                  {tag}
                </span>
              ))}
              {template.tags.length > 3 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                  +{template.tags.length - 3}
                </span>
              )}
            </div>
            
            <div className="text-xs text-gray-500">
              <p>📁 {template.files.length} fichiers</p>
              <p>👤 {template.author}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderConfigureTab = () => {
    if (!selectedTemplate) {
      return (
        <div className="text-center py-12 text-gray-500">
          <p>Sélectionnez d'abord un template</p>
        </div>
      );
    }

    const requiredVariables = templateManager['extractRequiredVariables'](selectedTemplate);

    return (
      <div className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">
            {getCategoryIcon(selectedTemplate.category)} {selectedTemplate.name}
          </h3>
          <p className="text-blue-600 text-sm">{selectedTemplate.description}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom du projet *
            </label>
            <input
              type="text"
              value={formData.projectName}
              onChange={(e) => setFormData(prev => ({ ...prev, projectName: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="mon-nouveau-projet"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Chemin de sortie *
            </label>
            <input
              type="text"
              value={formData.outputPath}
              onChange={(e) => setFormData(prev => ({ ...prev, outputPath: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="/workspace/projects"
            />
          </div>
        </div>

        {/* Variables du template */}
        {requiredVariables.length > 0 && (
          <div>
            <h4 className="text-lg font-semibold text-gray-800 mb-4">Variables du template</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {requiredVariables.map(variable => (
                <div key={variable}>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {variable} *
                  </label>
                  <input
                    type="text"
                    value={formData.variables[variable] || ''}
                    onChange={(e) => handleVariableChange(variable, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={`Valeur pour ${variable}`}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex items-center">
          <input
            type="checkbox"
            id="overwrite"
            checked={formData.overwrite}
            onChange={(e) => setFormData(prev => ({ ...prev, overwrite: e.target.checked }))}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="overwrite" className="ml-2 text-sm text-gray-700">
            Écraser les fichiers existants
          </label>
        </div>
      </div>
    );
  };

  const renderPreviewTab = () => {
    if (!preview) {
      return (
        <div className="text-center py-12 text-gray-500">
          <p>Aucun aperçu disponible</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Aperçu de la génération</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{preview.estimatedFiles}</div>
              <div className="text-sm text-gray-600">Fichiers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{preview.estimatedSize}</div>
              <div className="text-sm text-gray-600">Taille estimée</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{preview.generationTime}</div>
              <div className="text-sm text-gray-600">Temps estimé</div>
            </div>
          </div>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-gray-800 mb-3">Structure du projet</h4>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
            <div>📁 {formData.projectName}/</div>
            {preview.template.structure.directories.map(dir => (
              <div key={dir} className="ml-4">📁 {dir}/</div>
            ))}
            {preview.template.structure.rootFiles.map(file => (
              <div key={file} className="ml-4">📄 {file}</div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="text-lg font-semibold text-gray-800 mb-3">Dépendances</h4>
          <div className="flex flex-wrap gap-2">
            {preview.template.dependencies.map(dep => (
              <span
                key={dep}
                className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm"
              >
                📦 {dep}
              </span>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderGenerateTab = () => {
    const errors = validateForm();

    return (
      <div className="space-y-6">
        {errors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-medium text-red-800 mb-2">Erreurs de validation</h4>
            <ul className="list-disc list-inside text-red-600 text-sm">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-green-800 mb-4">Prêt à générer</h3>
          
          <div className="space-y-3 text-sm">
            <div><strong>Template:</strong> {selectedTemplate?.name}</div>
            <div><strong>Projet:</strong> {formData.projectName}</div>
            <div><strong>Destination:</strong> {formData.outputPath}</div>
            <div><strong>Fichiers:</strong> ~{preview?.estimatedFiles} fichiers</div>
          </div>

          <div className="mt-6">
            <button
              onClick={handleGenerate}
              disabled={isGenerating || errors.length > 0}
              className="w-full px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGenerating ? (
                <span className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Génération en cours...
                </span>
              ) : (
                '🚀 Générer le projet'
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow border">
      <div className="border-b border-gray-200">
        <div className="px-6 py-4">
          <h2 className="text-2xl font-bold text-gray-800">
            📋 Templates de Projet Hanuman
          </h2>
          <p className="text-gray-600 mt-1">
            Créez rapidement de nouveaux projets avec nos templates optimisés
          </p>
        </div>

        {/* Navigation par onglets */}
        <div className="px-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'browse', name: 'Parcourir', icon: '🔍' },
              { id: 'configure', name: 'Configurer', icon: '⚙️' },
              { id: 'preview', name: 'Aperçu', icon: '👁️' },
              { id: 'generate', name: 'Générer', icon: '🚀' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      <div className="p-6">
        {activeTab === 'browse' && renderBrowseTab()}
        {activeTab === 'configure' && renderConfigureTab()}
        {activeTab === 'preview' && renderPreviewTab()}
        {activeTab === 'generate' && renderGenerateTab()}
      </div>
    </div>
  );
};

export default TemplateInterface;
