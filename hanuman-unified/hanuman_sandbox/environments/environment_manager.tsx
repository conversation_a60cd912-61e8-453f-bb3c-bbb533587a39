import React, { useState, useEffect, useCallback } from 'react';
import { SandboxInfrastructure, SandboxContainer, SandboxNamespace } from '../infrastructure/sandbox_infrastructure';

// Types pour la gestion des environnements
export interface Environment {
  id: string;
  name: string;
  type: 'development' | 'testing' | 'security' | 'qa' | 'staging';
  status: 'creating' | 'ready' | 'running' | 'stopped' | 'error' | 'destroying';
  containers: SandboxContainer[];
  resources: {
    cpuUsage: number;
    memoryUsage: number;
    storageUsage: number;
    networkTraffic: number;
  };
  performance: {
    responseTime: number;
    throughput: number;
    errorRate: number;
    uptime: number;
  };
  createdAt: Date;
  lastActivity: Date;
  agentId?: string;
  organId?: string;
  config: EnvironmentConfig;
}

export interface EnvironmentConfig {
  autoScale: boolean;
  maxContainers: number;
  resourceLimits: {
    cpu: number;
    memory: number;
    storage: number;
  };
  securityLevel: 'low' | 'medium' | 'high' | 'maximum';
  monitoring: {
    enabled: boolean;
    alertThresholds: {
      cpu: number;
      memory: number;
      errorRate: number;
    };
  };
  autoCleanup: {
    enabled: boolean;
    inactivityThreshold: number; // en minutes
  };
}

export interface EnvironmentTemplate {
  id: string;
  name: string;
  description: string;
  type: Environment['type'];
  defaultConfig: EnvironmentConfig;
  requiredResources: {
    minCpu: number;
    minMemory: number;
    minStorage: number;
  };
}

interface EnvironmentManagerProps {
  infrastructure: SandboxInfrastructure;
  onEnvironmentChange?: (environment: Environment) => void;
  onError?: (error: Error) => void;
}

/**
 * Gestionnaire d'Environnements pour la Sandbox Hanuman
 * Interface React pour créer et gérer les environnements de développement
 */
export const EnvironmentManager: React.FC<EnvironmentManagerProps> = ({
  infrastructure,
  onEnvironmentChange,
  onError
}) => {
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const [templates, setTemplates] = useState<EnvironmentTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [isCreating, setIsCreating] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newEnvironmentName, setNewEnvironmentName] = useState('');
  const [selectedAgentId, setSelectedAgentId] = useState('');
  const [selectedOrganId, setSelectedOrganId] = useState('');

  // Templates d'environnements prédéfinis
  const defaultTemplates: EnvironmentTemplate[] = [
    {
      id: 'dev-basic',
      name: 'Développement Basique',
      description: 'Environnement de développement standard pour les agents',
      type: 'development',
      defaultConfig: {
        autoScale: false,
        maxContainers: 3,
        resourceLimits: { cpu: 2, memory: 4096, storage: 10000 },
        securityLevel: 'medium',
        monitoring: {
          enabled: true,
          alertThresholds: { cpu: 80, memory: 85, errorRate: 5 }
        },
        autoCleanup: { enabled: true, inactivityThreshold: 60 }
      },
      requiredResources: { minCpu: 1, minMemory: 1024, minStorage: 5000 }
    },
    {
      id: 'test-comprehensive',
      name: 'Tests Complets',
      description: 'Environnement de test avec monitoring avancé',
      type: 'testing',
      defaultConfig: {
        autoScale: true,
        maxContainers: 10,
        resourceLimits: { cpu: 8, memory: 16384, storage: 50000 },
        securityLevel: 'high',
        monitoring: {
          enabled: true,
          alertThresholds: { cpu: 70, memory: 80, errorRate: 2 }
        },
        autoCleanup: { enabled: true, inactivityThreshold: 30 }
      },
      requiredResources: { minCpu: 2, minMemory: 2048, minStorage: 10000 }
    },
    {
      id: 'security-hardened',
      name: 'Sécurité Renforcée',
      description: 'Environnement ultra-sécurisé pour validation sécurité',
      type: 'security',
      defaultConfig: {
        autoScale: false,
        maxContainers: 2,
        resourceLimits: { cpu: 2, memory: 4096, storage: 15000 },
        securityLevel: 'maximum',
        monitoring: {
          enabled: true,
          alertThresholds: { cpu: 60, memory: 70, errorRate: 1 }
        },
        autoCleanup: { enabled: true, inactivityThreshold: 15 }
      },
      requiredResources: { minCpu: 1, minMemory: 1024, minStorage: 5000 }
    },
    {
      id: 'qa-performance',
      name: 'QA Performance',
      description: 'Environnement QA avec focus sur les performances',
      type: 'qa',
      defaultConfig: {
        autoScale: true,
        maxContainers: 8,
        resourceLimits: { cpu: 6, memory: 12288, storage: 30000 },
        securityLevel: 'high',
        monitoring: {
          enabled: true,
          alertThresholds: { cpu: 75, memory: 85, errorRate: 3 }
        },
        autoCleanup: { enabled: true, inactivityThreshold: 45 }
      },
      requiredResources: { minCpu: 2, minMemory: 2048, minStorage: 10000 }
    }
  ];

  useEffect(() => {
    setTemplates(defaultTemplates);
    loadEnvironments();
    
    // Écouter les événements de l'infrastructure
    infrastructure.on('container:created', handleContainerCreated);
    infrastructure.on('container:destroyed', handleContainerDestroyed);
    infrastructure.on('monitoring:resources', handleResourceUpdate);

    return () => {
      infrastructure.off('container:created', handleContainerCreated);
      infrastructure.off('container:destroyed', handleContainerDestroyed);
      infrastructure.off('monitoring:resources', handleResourceUpdate);
    };
  }, [infrastructure]);

  const loadEnvironments = useCallback(() => {
    // Charger les environnements existants depuis l'infrastructure
    const containers = infrastructure.getContainers();
    const environmentMap = new Map<string, Environment>();

    containers.forEach(container => {
      const envId = container.agentId || container.organId || 'default';
      
      if (!environmentMap.has(envId)) {
        const environment: Environment = {
          id: envId,
          name: `Environnement ${container.name}`,
          type: container.type,
          status: container.status === 'running' ? 'ready' : 'stopped',
          containers: [],
          resources: {
            cpuUsage: 0,
            memoryUsage: 0,
            storageUsage: 0,
            networkTraffic: 0
          },
          performance: {
            responseTime: 0,
            throughput: 0,
            errorRate: 0,
            uptime: 100
          },
          createdAt: container.createdAt,
          lastActivity: container.lastActivity,
          agentId: container.agentId,
          organId: container.organId,
          config: defaultTemplates.find(t => t.type === container.type)?.defaultConfig || defaultTemplates[0].defaultConfig
        };
        environmentMap.set(envId, environment);
      }

      const env = environmentMap.get(envId)!;
      env.containers.push(container);
      
      // Calculer l'utilisation des ressources
      env.resources.cpuUsage += container.resources.cpu;
      env.resources.memoryUsage += container.resources.memory;
      env.resources.storageUsage += container.resources.storage;
    });

    setEnvironments(Array.from(environmentMap.values()));
  }, [infrastructure]);

  const handleContainerCreated = useCallback((container: SandboxContainer) => {
    loadEnvironments();
  }, [loadEnvironments]);

  const handleContainerDestroyed = useCallback((container: SandboxContainer) => {
    loadEnvironments();
  }, [loadEnvironments]);

  const handleResourceUpdate = useCallback((data: any) => {
    // Mettre à jour les métriques de performance
    setEnvironments(prev => prev.map(env => ({
      ...env,
      performance: {
        ...env.performance,
        uptime: Math.random() * 100, // Simulation - à remplacer par de vraies métriques
        responseTime: Math.random() * 1000,
        throughput: Math.random() * 100
      }
    })));
  }, []);

  const createEnvironment = async () => {
    if (!selectedTemplate || !newEnvironmentName) {
      onError?.(new Error('Veuillez sélectionner un template et saisir un nom'));
      return;
    }

    setIsCreating(true);
    try {
      const template = templates.find(t => t.id === selectedTemplate);
      if (!template) throw new Error('Template non trouvé');

      // Créer le conteneur principal
      const container = await infrastructure.createContainer({
        name: newEnvironmentName,
        type: template.type,
        namespace: template.type,
        agentId: selectedAgentId || undefined,
        organId: selectedOrganId || undefined,
        securityLevel: template.defaultConfig.securityLevel
      });

      // Créer l'environnement
      const environment: Environment = {
        id: container.id,
        name: newEnvironmentName,
        type: template.type,
        status: 'ready',
        containers: [container],
        resources: {
          cpuUsage: container.resources.cpu,
          memoryUsage: container.resources.memory,
          storageUsage: container.resources.storage,
          networkTraffic: 0
        },
        performance: {
          responseTime: 0,
          throughput: 0,
          errorRate: 0,
          uptime: 100
        },
        createdAt: new Date(),
        lastActivity: new Date(),
        agentId: selectedAgentId || undefined,
        organId: selectedOrganId || undefined,
        config: template.defaultConfig
      };

      setEnvironments(prev => [...prev, environment]);
      onEnvironmentChange?.(environment);

      // Réinitialiser le formulaire
      setNewEnvironmentName('');
      setSelectedTemplate('');
      setSelectedAgentId('');
      setSelectedOrganId('');
      setShowCreateForm(false);

      console.log(`✅ Environnement "${newEnvironmentName}" créé avec succès`);
    } catch (error) {
      console.error('❌ Erreur lors de la création de l\'environnement:', error);
      onError?.(error as Error);
    } finally {
      setIsCreating(false);
    }
  };

  const destroyEnvironment = async (environmentId: string) => {
    const environment = environments.find(e => e.id === environmentId);
    if (!environment) return;

    try {
      // Détruire tous les conteneurs de l'environnement
      for (const container of environment.containers) {
        await infrastructure.destroyContainer(container.id);
      }

      setEnvironments(prev => prev.filter(e => e.id !== environmentId));
      console.log(`🗑️ Environnement "${environment.name}" détruit`);
    } catch (error) {
      console.error('❌ Erreur lors de la destruction de l\'environnement:', error);
      onError?.(error as Error);
    }
  };

  const getStatusColor = (status: Environment['status']) => {
    const colors = {
      creating: 'text-yellow-600',
      ready: 'text-green-600',
      running: 'text-blue-600',
      stopped: 'text-gray-600',
      error: 'text-red-600',
      destroying: 'text-orange-600'
    };
    return colors[status] || 'text-gray-600';
  };

  const getTypeIcon = (type: Environment['type']) => {
    const icons = {
      development: '🧪',
      testing: '🔬',
      security: '🛡️',
      qa: '✅',
      staging: '🚀'
    };
    return icons[type] || '📦';
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          🏗️ Gestionnaire d'Environnements Sandbox
        </h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          ➕ Nouvel Environnement
        </button>
      </div>

      {/* Formulaire de création */}
      {showCreateForm && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Créer un Nouvel Environnement</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nom de l'environnement
              </label>
              <input
                type="text"
                value={newEnvironmentName}
                onChange={(e) => setNewEnvironmentName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Mon environnement de dev"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Template
              </label>
              <select
                value={selectedTemplate}
                onChange={(e) => setSelectedTemplate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Sélectionner un template</option>
                {templates.map(template => (
                  <option key={template.id} value={template.id}>
                    {getTypeIcon(template.type)} {template.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Agent ID (optionnel)
              </label>
              <input
                type="text"
                value={selectedAgentId}
                onChange={(e) => setSelectedAgentId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="agent-frontend"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Organe ID (optionnel)
              </label>
              <input
                type="text"
                value={selectedOrganId}
                onChange={(e) => setSelectedOrganId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="cortex-creatif"
              />
            </div>
          </div>

          {selectedTemplate && (
            <div className="mt-4 p-3 bg-blue-50 rounded-md">
              <h4 className="font-medium text-blue-800">
                {templates.find(t => t.id === selectedTemplate)?.name}
              </h4>
              <p className="text-sm text-blue-600">
                {templates.find(t => t.id === selectedTemplate)?.description}
              </p>
            </div>
          )}

          <div className="flex justify-end space-x-3 mt-4">
            <button
              onClick={() => setShowCreateForm(false)}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Annuler
            </button>
            <button
              onClick={createEnvironment}
              disabled={isCreating || !selectedTemplate || !newEnvironmentName}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCreating ? '⏳ Création...' : '✅ Créer'}
            </button>
          </div>
        </div>
      )}

      {/* Liste des environnements */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {environments.map(environment => (
          <div key={environment.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-semibold text-gray-800 flex items-center">
                  {getTypeIcon(environment.type)} {environment.name}
                </h3>
                <p className={`text-sm ${getStatusColor(environment.status)}`}>
                  {environment.status}
                </p>
              </div>
              <button
                onClick={() => destroyEnvironment(environment.id)}
                className="text-red-500 hover:text-red-700 text-sm"
                title="Détruire l'environnement"
              >
                🗑️
              </button>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Conteneurs:</span>
                <span className="font-medium">{environment.containers.length}</span>
              </div>
              <div className="flex justify-between">
                <span>CPU:</span>
                <span className="font-medium">{environment.resources.cpuUsage} cores</span>
              </div>
              <div className="flex justify-between">
                <span>Mémoire:</span>
                <span className="font-medium">{environment.resources.memoryUsage} MB</span>
              </div>
              <div className="flex justify-between">
                <span>Uptime:</span>
                <span className="font-medium">{environment.performance.uptime.toFixed(1)}%</span>
              </div>
            </div>

            {(environment.agentId || environment.organId) && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600">
                  {environment.agentId && `Agent: ${environment.agentId}`}
                  {environment.agentId && environment.organId && ' • '}
                  {environment.organId && `Organe: ${environment.organId}`}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>

      {environments.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <p className="text-lg">Aucun environnement créé</p>
          <p className="text-sm">Cliquez sur "Nouvel Environnement" pour commencer</p>
        </div>
      )}
    </div>
  );
};

export default EnvironmentManager;
