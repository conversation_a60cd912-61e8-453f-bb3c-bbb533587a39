import { EventEmitter } from 'events';
import { RoadmapValidatorAgent } from './roadmap_validator_agent';
import { RoadmapTracker } from './roadmap_tracker';
import { RoadmapGenerator } from './roadmap_generator';

/**
 * Gardien du Pipeline de Déploiement
 * Bloque automatiquement les déploiements si la roadmap n'est pas complète et validée
 */

// Types pour le contrôle de déploiement
export interface DeploymentRequest {
  id: string;
  projectId: string;
  roadmapId?: string;
  environment: 'development' | 'staging' | 'production';
  requestedBy: string;
  requestedAt: Date;
  changes: DeploymentChange[];
  metadata: any;
}

export interface DeploymentChange {
  type: 'feature' | 'bugfix' | 'enhancement' | 'security' | 'configuration';
  description: string;
  files: string[];
  impact: 'low' | 'medium' | 'high' | 'critical';
  tested: boolean;
  reviewed: boolean;
}

export interface DeploymentGate {
  id: string;
  name: string;
  description: string;
  type: 'roadmap_required' | 'validation_required' | 'progress_required' | 'approval_required' | 'security_required';
  environment: string[];
  mandatory: boolean;
  checker: (request: DeploymentRequest) => Promise<GateResult>;
}

export interface GateResult {
  passed: boolean;
  message: string;
  details: string;
  blockers: string[];
  warnings: string[];
  recommendations: string[];
  canOverride: boolean;
  overrideRequiredRole?: string;
}

export interface DeploymentDecision {
  requestId: string;
  decision: 'approved' | 'rejected' | 'conditional';
  gateResults: Map<string, GateResult>;
  overallScore: number;
  blockers: string[];
  warnings: string[];
  recommendations: string[];
  approvedBy?: string;
  approvedAt?: Date;
  conditions?: string[];
  expiresAt?: Date;
}

export interface GateKeeperConfig {
  enableStrictMode: boolean;
  allowEmergencyOverride: boolean;
  emergencyOverrideRoles: string[];
  requireRoadmapForProduction: boolean;
  requireValidationForStaging: boolean;
  requireProgressTracking: boolean;
  autoCreateRoadmapForMissingProjects: boolean;
  notificationChannels: string[];
}

export class DeploymentGateKeeper extends EventEmitter {
  private gates: Map<string, DeploymentGate> = new Map();
  private decisions: Map<string, DeploymentDecision> = new Map();
  private config: GateKeeperConfig;
  private roadmapValidator: RoadmapValidatorAgent;
  private roadmapTracker: RoadmapTracker;
  private roadmapGenerator: RoadmapGenerator;

  constructor(
    config: GateKeeperConfig,
    roadmapValidator: RoadmapValidatorAgent,
    roadmapTracker: RoadmapTracker,
    roadmapGenerator: RoadmapGenerator
  ) {
    super();
    this.config = config;
    this.roadmapValidator = roadmapValidator;
    this.roadmapTracker = roadmapTracker;
    this.roadmapGenerator = roadmapGenerator;
    this.initializeGates();
  }

  /**
   * Initialise les portes de contrôle
   */
  private initializeGates(): void {
    const gates: DeploymentGate[] = [
      {
        id: 'roadmap_exists',
        name: 'Roadmap Obligatoire',
        description: 'Vérifier qu\'une roadmap existe pour le projet',
        type: 'roadmap_required',
        environment: ['staging', 'production'],
        mandatory: true,
        checker: async (request: DeploymentRequest) => {
          const projects = this.roadmapGenerator.getProjects();
          const hasRoadmap = projects.some(p => 
            p.id === request.roadmapId || 
            p.name.toLowerCase().includes(request.projectId.toLowerCase())
          );

          if (!hasRoadmap && this.config.autoCreateRoadmapForMissingProjects) {
            // Créer automatiquement une roadmap basique
            try {
              await this.createEmergencyRoadmap(request);
              return {
                passed: true,
                message: 'Roadmap d\'urgence créée automatiquement',
                details: 'Une roadmap basique a été générée pour ce déploiement',
                blockers: [],
                warnings: ['Roadmap créée automatiquement - À compléter après déploiement'],
                recommendations: ['Compléter la roadmap avec les détails du projet'],
                canOverride: false
              };
            } catch (error) {
              return {
                passed: false,
                message: 'Aucune roadmap trouvée et création automatique échouée',
                details: `Projet ${request.projectId} sans roadmap définie`,
                blockers: ['Roadmap manquante', 'Création automatique échouée'],
                warnings: [],
                recommendations: ['Créer une roadmap avant le déploiement'],
                canOverride: this.config.allowEmergencyOverride,
                overrideRequiredRole: 'deployment-manager'
              };
            }
          }

          return {
            passed: hasRoadmap,
            message: hasRoadmap ? 'Roadmap trouvée' : 'Aucune roadmap trouvée',
            details: hasRoadmap ? 'Le projet a une roadmap définie' : `Projet ${request.projectId} sans roadmap`,
            blockers: hasRoadmap ? [] : ['Roadmap manquante'],
            warnings: [],
            recommendations: hasRoadmap ? [] : ['Créer une roadmap avant le déploiement'],
            canOverride: this.config.allowEmergencyOverride,
            overrideRequiredRole: 'deployment-manager'
          };
        }
      },
      {
        id: 'roadmap_validated',
        name: 'Roadmap Validée',
        description: 'Vérifier que la roadmap est validée et approuvée',
        type: 'validation_required',
        environment: ['staging', 'production'],
        mandatory: true,
        checker: async (request: DeploymentRequest) => {
          if (!request.roadmapId) {
            return {
              passed: false,
              message: 'ID de roadmap manquant',
              details: 'Impossible de vérifier la validation sans ID de roadmap',
              blockers: ['ID de roadmap manquant'],
              warnings: [],
              recommendations: ['Associer le déploiement à une roadmap'],
              canOverride: false
            };
          }

          const canDeployResult = this.roadmapValidator.canDeploy(request.roadmapId);
          
          return {
            passed: canDeployResult.canDeploy,
            message: canDeployResult.canDeploy ? 'Roadmap validée pour déploiement' : 'Roadmap non validée',
            details: canDeployResult.canDeploy ? 'Toutes les validations sont passées' : 'Validations en échec',
            blockers: canDeployResult.canDeploy ? [] : canDeployResult.reasons,
            warnings: [],
            recommendations: canDeployResult.canDeploy ? [] : [
              'Résoudre les problèmes de validation',
              'Obtenir les approbations manquantes'
            ],
            canOverride: this.config.allowEmergencyOverride && request.environment !== 'production',
            overrideRequiredRole: 'security-officer'
          };
        }
      },
      {
        id: 'progress_tracking',
        name: 'Suivi de Progrès',
        description: 'Vérifier que le progrès du projet est suivi',
        type: 'progress_required',
        environment: ['production'],
        mandatory: this.config.requireProgressTracking,
        checker: async (request: DeploymentRequest) => {
          if (!request.roadmapId) {
            return {
              passed: !this.config.requireProgressTracking,
              message: 'Suivi de progrès non disponible',
              details: 'ID de roadmap manquant pour vérifier le suivi',
              blockers: this.config.requireProgressTracking ? ['Suivi de progrès requis'] : [],
              warnings: ['Suivi de progrès recommandé'],
              recommendations: ['Activer le suivi de progrès'],
              canOverride: true,
              overrideRequiredRole: 'project-manager'
            };
          }

          const progress = this.roadmapTracker.getProgress(request.roadmapId);
          const hasTracking = progress !== undefined;
          const isCompleted = progress ? progress.overallProgress >= 90 : false;

          return {
            passed: hasTracking && isCompleted,
            message: hasTracking ? 
              (isCompleted ? 'Projet complété et suivi' : 'Projet en cours de suivi') : 
              'Aucun suivi de progrès',
            details: hasTracking ? 
              `Progrès: ${Math.round(progress!.overallProgress)}%` : 
              'Le projet n\'est pas suivi',
            blockers: (!hasTracking && this.config.requireProgressTracking) ? ['Suivi de progrès manquant'] : [],
            warnings: !isCompleted ? ['Projet non complété'] : [],
            recommendations: hasTracking ? 
              (isCompleted ? [] : ['Compléter le projet avant déploiement']) : 
              ['Démarrer le suivi de progrès'],
            canOverride: true,
            overrideRequiredRole: 'project-manager'
          };
        }
      },
      {
        id: 'security_clearance',
        name: 'Autorisation Sécurité',
        description: 'Vérifier les aspects sécurité du déploiement',
        type: 'security_required',
        environment: ['production'],
        mandatory: true,
        checker: async (request: DeploymentRequest) => {
          const hasSecurityChanges = request.changes.some(change => 
            change.type === 'security' || 
            change.impact === 'critical' ||
            change.description.toLowerCase().includes('security') ||
            change.description.toLowerCase().includes('sécurité')
          );

          const allChangesReviewed = request.changes.every(change => change.reviewed);
          const allChangesTested = request.changes.every(change => change.tested);

          const blockers: string[] = [];
          const warnings: string[] = [];

          if (hasSecurityChanges && !allChangesReviewed) {
            blockers.push('Changements sécurité non reviewés');
          }

          if (!allChangesTested) {
            warnings.push('Certains changements non testés');
          }

          const passed = blockers.length === 0;

          return {
            passed,
            message: passed ? 'Autorisation sécurité accordée' : 'Problèmes de sécurité détectés',
            details: `${request.changes.length} changements analysés`,
            blockers,
            warnings,
            recommendations: [
              'Effectuer une review sécurité complète',
              'Tester tous les changements',
              'Valider avec l\'équipe sécurité'
            ],
            canOverride: this.config.allowEmergencyOverride,
            overrideRequiredRole: 'security-officer'
          };
        }
      },
      {
        id: 'change_approval',
        name: 'Approbation des Changements',
        description: 'Vérifier que tous les changements sont approuvés',
        type: 'approval_required',
        environment: ['production'],
        mandatory: true,
        checker: async (request: DeploymentRequest) => {
          const criticalChanges = request.changes.filter(change => change.impact === 'critical');
          const highImpactChanges = request.changes.filter(change => change.impact === 'high');
          
          const needsApproval = criticalChanges.length > 0 || highImpactChanges.length > 2;
          
          // Simuler la vérification d'approbation
          const hasApproval = request.metadata?.approved === true;

          return {
            passed: !needsApproval || hasApproval,
            message: needsApproval ? 
              (hasApproval ? 'Changements approuvés' : 'Approbation requise') : 
              'Aucune approbation nécessaire',
            details: `${criticalChanges.length} changements critiques, ${highImpactChanges.length} changements à fort impact`,
            blockers: (needsApproval && !hasApproval) ? ['Approbation manquante'] : [],
            warnings: [],
            recommendations: needsApproval ? ['Obtenir l\'approbation des changements'] : [],
            canOverride: false
          };
        }
      }
    ];

    gates.forEach(gate => {
      this.gates.set(gate.id, gate);
    });
  }

  /**
   * Évalue une demande de déploiement
   */
  async evaluateDeployment(request: DeploymentRequest): Promise<DeploymentDecision> {
    this.emit('evaluation:started', request);

    const decision: DeploymentDecision = {
      requestId: request.id,
      decision: 'approved',
      gateResults: new Map(),
      overallScore: 100,
      blockers: [],
      warnings: [],
      recommendations: []
    };

    try {
      // Évaluer chaque porte applicable
      for (const gate of this.gates.values()) {
        if (this.shouldApplyGate(gate, request)) {
          const result = await gate.checker(request);
          decision.gateResults.set(gate.id, result);

          // Collecter les blockers, warnings et recommandations
          decision.blockers.push(...result.blockers);
          decision.warnings.push(...result.warnings);
          decision.recommendations.push(...result.recommendations);

          // Ajuster le score
          if (!result.passed) {
            decision.overallScore -= gate.mandatory ? 50 : 20;
          }
        }
      }

      // Déterminer la décision finale
      if (decision.blockers.length > 0) {
        decision.decision = 'rejected';
      } else if (decision.warnings.length > 0) {
        decision.decision = 'conditional';
        decision.conditions = decision.warnings;
      }

      // Sauvegarder la décision
      this.decisions.set(request.id, decision);

      this.emit('evaluation:completed', { request, decision });

      return decision;

    } catch (error) {
      decision.decision = 'rejected';
      decision.blockers.push('Erreur lors de l\'évaluation');
      
      this.emit('evaluation:error', { request, decision, error });
      
      return decision;
    }
  }

  /**
   * Crée une roadmap d'urgence pour un projet sans roadmap
   */
  private async createEmergencyRoadmap(request: DeploymentRequest): Promise<void> {
    const emergencyInput = {
      name: `Roadmap d'urgence - ${request.projectId}`,
      description: `Roadmap créée automatiquement pour le déploiement ${request.id}`,
      type: 'enhancement',
      scope: ['deployment', 'emergency'],
      technicalStack: ['existing'],
      integrations: [],
      requirements: [
        'Déploiement d\'urgence autorisé',
        'Roadmap à compléter post-déploiement'
      ],
      constraints: [
        'Roadmap d\'urgence - documentation limitée',
        'Validation post-déploiement requise'
      ],
      team: [request.requestedBy]
    };

    const roadmap = await this.roadmapGenerator.generateRoadmap(emergencyInput);
    
    // Validation automatique en mode urgence
    const validation = await this.roadmapValidator.validateRoadmap(roadmap, 'pre_deployment');
    
    // Approuver automatiquement si c'est une urgence
    if (this.config.allowEmergencyOverride) {
      for (const approval of validation.stakeholderApprovals) {
        if (approval.required) {
          await this.roadmapValidator.approveValidation(
            validation.id, 
            approval.stakeholder, 
            'Approbation automatique - déploiement d\'urgence'
          );
        }
      }
    }

    // Associer la roadmap à la demande
    request.roadmapId = roadmap.id;
  }

  /**
   * Vérifie si une porte doit être appliquée
   */
  private shouldApplyGate(gate: DeploymentGate, request: DeploymentRequest): boolean {
    return gate.environment.includes(request.environment);
  }

  /**
   * Approuve un déploiement avec override
   */
  async overrideDeployment(
    requestId: string, 
    overrideBy: string, 
    reason: string,
    userRole: string
  ): Promise<DeploymentDecision> {
    const decision = this.decisions.get(requestId);
    if (!decision) {
      throw new Error(`Décision de déploiement non trouvée: ${requestId}`);
    }

    // Vérifier les permissions d'override
    const canOverride = this.canUserOverride(decision, userRole);
    if (!canOverride) {
      throw new Error(`Utilisateur ${overrideBy} non autorisé à faire un override`);
    }

    decision.decision = 'approved';
    decision.approvedBy = overrideBy;
    decision.approvedAt = new Date();
    decision.conditions = [`Override par ${overrideBy}: ${reason}`];
    decision.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24h

    this.emit('deployment:overridden', { requestId, overrideBy, reason, decision });

    return decision;
  }

  /**
   * Vérifie si un utilisateur peut faire un override
   */
  private canUserOverride(decision: DeploymentDecision, userRole: string): boolean {
    if (!this.config.allowEmergencyOverride) {
      return false;
    }

    if (this.config.emergencyOverrideRoles.includes(userRole)) {
      return true;
    }

    // Vérifier les rôles spécifiques aux portes
    for (const result of decision.gateResults.values()) {
      if (result.canOverride && result.overrideRequiredRole === userRole) {
        return true;
      }
    }

    return false;
  }

  /**
   * Génère un rapport de conformité
   */
  generateComplianceReport(timeRange: { start: Date; end: Date }): ComplianceReport {
    const decisions = Array.from(this.decisions.values()).filter(d => 
      d.approvedAt && 
      d.approvedAt >= timeRange.start && 
      d.approvedAt <= timeRange.end
    );

    const totalDeployments = decisions.length;
    const approvedDeployments = decisions.filter(d => d.decision === 'approved').length;
    const rejectedDeployments = decisions.filter(d => d.decision === 'rejected').length;
    const overriddenDeployments = decisions.filter(d => d.conditions?.some(c => c.includes('Override'))).length;

    return {
      period: timeRange,
      totalDeployments,
      approvedDeployments,
      rejectedDeployments,
      overriddenDeployments,
      complianceRate: totalDeployments > 0 ? (approvedDeployments / totalDeployments) * 100 : 100,
      commonBlockers: this.getCommonBlockers(decisions),
      recommendations: this.generateComplianceRecommendations(decisions)
    };
  }

  private getCommonBlockers(decisions: DeploymentDecision[]): { blocker: string; count: number }[] {
    const blockerCounts = new Map<string, number>();
    
    decisions.forEach(decision => {
      decision.blockers.forEach(blocker => {
        blockerCounts.set(blocker, (blockerCounts.get(blocker) || 0) + 1);
      });
    });

    return Array.from(blockerCounts.entries())
      .map(([blocker, count]) => ({ blocker, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  private generateComplianceRecommendations(decisions: DeploymentDecision[]): string[] {
    const recommendations: string[] = [];
    
    const overrideRate = decisions.filter(d => d.conditions?.some(c => c.includes('Override'))).length / decisions.length;
    if (overrideRate > 0.1) {
      recommendations.push('Taux d\'override élevé - revoir les processus de validation');
    }

    const rejectionRate = decisions.filter(d => d.decision === 'rejected').length / decisions.length;
    if (rejectionRate > 0.2) {
      recommendations.push('Taux de rejet élevé - améliorer la formation des équipes');
    }

    return recommendations;
  }

  // Getters
  getDecision(requestId: string): DeploymentDecision | undefined {
    return this.decisions.get(requestId);
  }

  getGates(): DeploymentGate[] {
    return Array.from(this.gates.values());
  }

  getRecentDecisions(limit: number = 50): DeploymentDecision[] {
    return Array.from(this.decisions.values())
      .sort((a, b) => (b.approvedAt?.getTime() || 0) - (a.approvedAt?.getTime() || 0))
      .slice(0, limit);
  }
}

// Interfaces supplémentaires
export interface ComplianceReport {
  period: { start: Date; end: Date };
  totalDeployments: number;
  approvedDeployments: number;
  rejectedDeployments: number;
  overriddenDeployments: number;
  complianceRate: number;
  commonBlockers: { blocker: string; count: number }[];
  recommendations: string[];
}
