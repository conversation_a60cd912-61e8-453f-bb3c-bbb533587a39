{"testLab": {"name": "Laboratoire de Test Hanuman", "version": "1.0.0", "description": "Système complet de tests automatisés, métriques de qualité et analyse de performance", "sprint": "Sprint 3 - <PERSON><PERSON><PERSON><PERSON>", "status": "Production Ready"}, "environments": {"test-unit": {"type": "testing", "autoScale": false, "maxContainers": 2, "resourceLimits": {"cpu": 1, "memory": 2048, "storage": 5000}, "securityLevel": "medium", "monitoring": {"enabled": true, "alertThresholds": {"cpu": 80, "memory": 80, "errorRate": 5}}, "autoCleanup": {"enabled": true, "inactivityThreshold": 30}}, "test-integration": {"type": "testing", "autoScale": true, "maxContainers": 5, "resourceLimits": {"cpu": 4, "memory": 8192, "storage": 20000}, "securityLevel": "high", "monitoring": {"enabled": true, "alertThresholds": {"cpu": 70, "memory": 70, "errorRate": 3}}, "autoCleanup": {"enabled": true, "inactivityThreshold": 60}}, "test-performance": {"type": "testing", "autoScale": true, "maxContainers": 10, "resourceLimits": {"cpu": 8, "memory": 16384, "storage": 50000}, "securityLevel": "medium", "monitoring": {"enabled": true, "alertThresholds": {"cpu": 90, "memory": 85, "errorRate": 1}}, "autoCleanup": {"enabled": true, "inactivityThreshold": 120}}}, "qualityThresholds": {"coverage": {"overall": 80, "statements": 80, "branches": 75, "functions": 85, "lines": 80}, "performance": {"responseTime": 200, "throughput": 100, "errorRate": 5, "cpuUsage": 80, "memoryUsage": 80}, "security": {"riskScore": 20, "criticalVulnerabilities": 0, "highVulnerabilities": 2, "complianceScore": 90}, "maintainability": {"cyclomaticComplexity": 10, "cognitiveComplexity": 15, "technicalDebt": 30, "maintainabilityIndex": 70, "duplicationRatio": 5}, "documentation": {"coverage": 80, "quality": 75, "outdatedThreshold": 10}}, "testSuites": {"infrastructure-tests": {"name": "Tests Infrastructure", "description": "Tests de l'infrastructure sandbox", "parallel": true, "maxConcurrency": 3, "timeout": 30000, "retries": 2, "tags": ["infrastructure", "core"], "environment": "test-integration"}, "security-tests": {"name": "Tests Sécurité", "description": "Tests de sécurité et vulnérabilités", "parallel": false, "maxConcurrency": 1, "timeout": 60000, "retries": 1, "tags": ["security", "critical"], "environment": "test-integration"}, "performance-tests": {"name": "Tests Performance", "description": "Tests de performance et charge", "parallel": true, "maxConcurrency": 2, "timeout": 300000, "retries": 1, "tags": ["performance", "load"], "environment": "test-performance"}, "integration-tests": {"name": "Tests Intégration", "description": "Tests d'intégration entre composants", "parallel": false, "maxConcurrency": 1, "timeout": 120000, "retries": 2, "tags": ["integration", "e2e"], "environment": "test-integration"}, "regression-tests": {"name": "Tests Régression", "description": "Tests de non-régression", "parallel": true, "maxConcurrency": 4, "timeout": 45000, "retries": 1, "tags": ["regression", "stability"], "environment": "test-unit"}}, "performanceTestTemplates": {"load": {"name": "Test de Charge", "description": "Test de charge standard", "defaultConfig": {"duration": 300, "rampUpTime": 60, "rampDownTime": 60, "virtualUsers": 100, "requestsPerSecond": 50}, "thresholds": {"maxResponseTime": 200, "minThroughput": 40, "maxErrorRate": 5, "maxCpuUsage": 80, "maxMemoryUsage": 80}}, "stress": {"name": "Test de Stress", "description": "Test de stress pour limites système", "defaultConfig": {"duration": 600, "rampUpTime": 120, "rampDownTime": 120, "virtualUsers": 500, "requestsPerSecond": 200}, "thresholds": {"maxResponseTime": 500, "minThroughput": 150, "maxErrorRate": 10, "maxCpuUsage": 95, "maxMemoryUsage": 90}}, "spike": {"name": "Test de Pic", "description": "Test de pic de trafic soudain", "defaultConfig": {"duration": 180, "rampUpTime": 10, "rampDownTime": 30, "virtualUsers": 300, "requestsPerSecond": 150}, "thresholds": {"maxResponseTime": 300, "minThroughput": 100, "maxErrorRate": 8, "maxCpuUsage": 85, "maxMemoryUsage": 85}}, "endurance": {"name": "Test d'Endurance", "description": "Test de longue durée", "defaultConfig": {"duration": 3600, "rampUpTime": 300, "rampDownTime": 300, "virtualUsers": 50, "requestsPerSecond": 25}, "thresholds": {"maxResponseTime": 250, "minThroughput": 20, "maxErrorRate": 3, "maxCpuUsage": 70, "maxMemoryUsage": 75}}}, "alerting": {"enabled": true, "channels": ["console", "log", "webhook"], "severityLevels": {"info": {"color": "blue", "icon": "ℹ️", "retention": 24}, "warning": {"color": "yellow", "icon": "⚠️", "retention": 72}, "error": {"color": "orange", "icon": "❌", "retention": 168}, "critical": {"color": "red", "icon": "🔴", "retention": 720}}, "rules": [{"name": "Low Test Coverage", "condition": "coverage.overall < thresholds.coverage.overall", "severity": "warning", "message": "Couverture de tests en dessous du seuil"}, {"name": "High Response Time", "condition": "performance.responseTime.avg > thresholds.performance.responseTime", "severity": "error", "message": "Temps de réponse trop élevé"}, {"name": "Critical Vulnerabilities", "condition": "security.vulnerabilities.critical > 0", "severity": "critical", "message": "Vulnérabilités critiques détectées"}, {"name": "High Error Rate", "condition": "performance.errorRate > thresholds.performance.errorRate", "severity": "error", "message": "Taux d'erreur élev<PERSON>"}]}, "reporting": {"formats": ["html", "json", "csv", "pdf"], "defaultFormat": "html", "autoGenerate": true, "schedule": {"daily": true, "weekly": true, "monthly": true}, "retention": {"daily": 30, "weekly": 12, "monthly": 24}, "templates": {"quality": {"name": "Rapport de Qualité", "sections": ["overview", "metrics", "trends", "recommendations"], "charts": true, "detailed": true}, "performance": {"name": "Rapport de Performance", "sections": ["summary", "metrics", "violations", "recommendations"], "charts": true, "detailed": true}, "security": {"name": "Rapport de Sécurité", "sections": ["vulnerabilities", "compliance", "recommendations"], "charts": false, "detailed": true}}}, "ui": {"theme": "hanuman", "autoRefresh": {"enabled": true, "interval": 30000, "options": [10000, 30000, 60000, 300000]}, "dashboard": {"defaultTab": "overview", "tabs": [{"id": "overview", "name": "Vue d'ensemble", "icon": "📊", "enabled": true}, {"id": "execution", "name": "Exécution", "icon": "🧪", "enabled": true}, {"id": "performance", "name": "Performance", "icon": "⚡", "enabled": true}, {"id": "quality", "name": "Qualité", "icon": "🎯", "enabled": true}, {"id": "generator", "name": "Générateur", "icon": "🤖", "enabled": true}, {"id": "reports", "name": "Rapports", "icon": "📋", "enabled": true}]}, "notifications": {"enabled": true, "position": "top-right", "duration": 5000, "maxVisible": 3}}, "integration": {"hanuman": {"enabled": true, "orchestrator": "HanumanOrganOrchestrator", "events": ["test.started", "test.completed", "alert.triggered", "report.generated"]}, "external": {"webhook": {"enabled": false, "url": "", "events": ["alert.critical", "test.failed"]}, "slack": {"enabled": false, "webhook": "", "channels": ["#quality", "#alerts"]}, "email": {"enabled": false, "smtp": {}, "recipients": []}}}, "metadata": {"created": "2024-01-15T10:00:00Z", "lastUpdated": "2024-01-15T15:30:00Z", "version": "1.0.0", "author": "Hanuman Development Team", "sprint": "Sprint 3", "status": "Completed", "nextSprint": "Sprint 4 - Validation Sécurité"}}