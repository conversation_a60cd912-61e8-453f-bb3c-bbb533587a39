apiVersion: v1
kind: ConfigMap
metadata:
  name: agent-uiux-config
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
data:
  # Configuration de l'agent
  agent.json: |
    {
      "id": "agent-uiux-001",
      "name": "Agent UI/UX Design Thinking",
      "type": "uiux",
      "version": "1.0.0",
      "capabilities": [
        "user_research",
        "persona_generation",
        "design_system_creation",
        "wireframe_generation",
        "conversion_optimization",
        "accessibility_validation",
        "usability_testing",
        "component_library_creation"
      ],
      "endpoints": {
        "health": "/health",
        "design": "/api/design",
        "validate": "/api/validate",
        "research": "/api/research",
        "personas": "/api/personas",
        "designSystem": "/api/design-system",
        "wireframes": "/api/wireframes",
        "components": "/api/components"
      },
      "memory": {
        "store": "weaviate",
        "collections": [
          "UserResearch",
          "Persona",
          "DesignSystem",
          "Wireframe",
          "DesignPattern",
          "ComprehensiveDesign"
        ]
      },
      "communication": {
        "kafka": {
          "topics": [
            "agent.uiux.design.complete",
            "agent.uiux.validation.request",
            "agent.uiux.design.feedback",
            "agent.uiux.components.ready"
          ],
          "groupId": "agent-uiux-group"
        },
        "redis": {
          "channels": [
            "uiux:notifications",
            "uiux:requests"
          ]
        }
      }
    }

  # Configuration des logs
  logging.json: |
    {
      "level": "info",
      "format": "json",
      "transports": [
        {
          "type": "console",
          "colorize": true
        },
        {
          "type": "file",
          "filename": "/app/logs/agent-uiux.log",
          "maxsize": "10MB",
          "maxFiles": 5
        },
        {
          "type": "file",
          "filename": "/app/logs/error.log",
          "level": "error",
          "maxsize": "10MB",
          "maxFiles": 5
        }
      ]
    }

  # Configuration des métriques
  metrics.json: |
    {
      "enabled": true,
      "port": 3005,
      "path": "/metrics",
      "defaultMetrics": true,
      "customMetrics": [
        {
          "name": "design_requests_total",
          "help": "Total number of design requests",
          "type": "counter"
        },
        {
          "name": "design_generation_duration_seconds",
          "help": "Duration of design generation in seconds",
          "type": "histogram"
        },
        {
          "name": "user_research_requests_total",
          "help": "Total number of user research requests",
          "type": "counter"
        },
        {
          "name": "validation_requests_total",
          "help": "Total number of validation requests",
          "type": "counter"
        },
        {
          "name": "accessibility_score",
          "help": "Accessibility score of generated designs",
          "type": "gauge"
        }
      ]
    }

  # Configuration de la sécurité
  security.json: |
    {
      "cors": {
        "origin": ["http://localhost:3000", "https://retreatandbe.com"],
        "credentials": true,
        "optionsSuccessStatus": 200
      },
      "helmet": {
        "contentSecurityPolicy": {
          "directives": {
            "defaultSrc": ["'self'"],
            "styleSrc": ["'self'", "'unsafe-inline'"],
            "scriptSrc": ["'self'"],
            "imgSrc": ["'self'", "data:", "https:"],
            "connectSrc": ["'self'", "ws:", "wss:"]
          }
        },
        "hsts": {
          "maxAge": 31536000,
          "includeSubDomains": true,
          "preload": true
        }
      },
      "rateLimit": {
        "windowMs": 900000,
        "max": 100,
        "message": "Too many requests from this IP"
      }
    }

  # Configuration des features
  features.json: |
    {
      "aiGeneration": {
        "enabled": true,
        "provider": "openai",
        "model": "gpt-4",
        "maxTokens": 2000,
        "temperature": 0.7
      },
      "externalApis": {
        "enabled": false,
        "figma": {
          "enabled": false,
          "baseUrl": "https://api.figma.com/v1"
        },
        "analytics": {
          "enabled": false,
          "providers": ["google-analytics", "hotjar"]
        }
      },
      "caching": {
        "enabled": true,
        "ttl": 3600,
        "maxSize": 1000
      },
      "monitoring": {
        "enabled": true,
        "healthCheck": {
          "interval": 30000,
          "timeout": 5000
        },
        "metrics": {
          "enabled": true,
          "interval": 60000
        }
      }
    }
