apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-uiux
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
    version: v1.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: agent-uiux
  template:
    metadata:
      labels:
        app: agent-uiux
        component: ai-agent
        tier: application
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3005"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: agent-uiux
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: agent-uiux
        image: retreat-and-be/agent-uiux:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 3005
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3005"
        - name: AGENT_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: LOG_LEVEL
          value: "info"
        - name: WEAVIATE_URL
          value: "http://weaviate:8080"
        - name: KAFKA_BROKERS
          value: "kafka:9092"
        - name: KAFKA_GROUP_ID
          value: "agent-uiux-group"
        - name: REDIS_URL
          value: "redis://redis:6379"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: agent-uiux-secrets
              key: openai-api-key
              optional: true
        - name: ENABLE_AI_GENERATION
          value: "true"
        - name: MOCK_EXTERNAL_SERVICES
          value: "false"
        - name: PROMETHEUS_ENABLED
          value: "true"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: agent-uiux-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: design-assets
          mountPath: /app/assets
        - name: user-research-data
          mountPath: /app/research
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: logs
        emptyDir: {}
      - name: design-assets
        persistentVolumeClaim:
          claimName: design-assets-pvc
      - name: user-research-data
        persistentVolumeClaim:
          claimName: user-research-data-pvc
      - name: config
        configMap:
          name: agent-uiux-config
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "ai-workload"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - agent-uiux
              topologyKey: kubernetes.io/hostname
