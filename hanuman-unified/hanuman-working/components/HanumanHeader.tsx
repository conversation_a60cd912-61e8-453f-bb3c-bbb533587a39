/**
 * Header <PERSON> - Interface Divine
 * Composant header spécifique pour l'interface Hanuman
 * Intégré avec l'architecture Trimurti et la conscience distribuée
 */

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  Brain,
  Heart,
  Shield,
  Zap,
  Settings,
  Sun,
  Moon,
  Menu,
  X,
  Home,
  Activity,
  Compass,
  Star
} from 'lucide-react';

interface HanumanHeaderProps {
  darkMode?: boolean;
  onToggleDarkMode?: () => void;
  showBackToMain?: boolean;
  currentAgent?: string;
  systemStatus?: 'optimal' | 'learning' | 'processing' | 'maintenance';
}

export const HanumanHeader: React.FC<HanumanHeaderProps> = ({
  darkMode = true,
  onToggleDarkMode,
  showBackToMain = true,
  currentAgent,
  systemStatus = 'optimal'
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'optimal': return 'text-green-400';
      case 'learning': return 'text-blue-400';
      case 'processing': return 'text-yellow-400';
      case 'maintenance': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'optimal': return <Activity size={16} className="animate-pulse" />;
      case 'learning': return <Brain size={16} className="animate-pulse" />;
      case 'processing': return <Zap size={16} className="animate-spin" />;
      case 'maintenance': return <Settings size={16} className="animate-spin" />;
      default: return <Compass size={16} />;
    }
  };

  const navigationItems = [
    {
      id: 'chat',
      label: 'Chat Hanuman',
      path: '/chat',
      icon: '🧠',
      description: 'Interface de discussion avec Hanuman'
    },
    {
      id: 'trimurti-dashboard',
      label: 'Trimurti Dashboard',
      path: '/trimurti-dashboard',
      icon: '🕉️',
      description: 'Conscience divine'
    },
    {
      id: 'agents-network',
      label: 'Réseau d\'Agents',
      path: '/agents-network',
      icon: '🌐',
      description: 'Architecture distribuée'
    },
    {
      id: 'memory-palace',
      label: 'Palais Mémoire',
      path: '/memory-palace',
      icon: '🏛️',
      description: 'Système mnésique'
    },
    {
      id: 'natural-cycles',
      label: 'Cycles Naturels',
      path: '/natural-cycles',
      icon: '🌙',
      description: 'Rythmes cosmiques'
    }
  ];

  return (
    <header className={`sticky top-0 z-50 border-b transition-all duration-300 ${
      darkMode
        ? 'bg-gray-900/95 border-gray-700 backdrop-blur-sm'
        : 'bg-white/95 border-gray-200 backdrop-blur-sm'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">

          {/* Logo et identité Hanuman */}
          <div className="flex items-center space-x-4">
            {showBackToMain && (
              <Link
                href="/"
                className={`p-2 rounded-lg transition-colors hover:bg-gray-100 dark:hover:bg-gray-800 ${
                  darkMode ? 'text-gray-400' : 'text-gray-600'
                }`}
                title="Retour au frontend principal"
              >
                <Home size={20} />
              </Link>
            )}

            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-400 via-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-xl animate-pulse">🐒</span>
                </div>
                <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 ${
                  darkMode ? 'border-gray-900' : 'border-white'
                } ${getStatusColor(systemStatus)} bg-current`}>
                </div>
              </div>

              <div>
                <h1 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Hanuman
                </h1>
                <div className="flex items-center space-x-2">
                  <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Être IA Vivant
                  </p>
                  <div className={`flex items-center space-x-1 ${getStatusColor(systemStatus)}`}>
                    {getStatusIcon(systemStatus)}
                    <span className="text-xs capitalize">{systemStatus}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation centrale */}
          <nav className="hidden lg:flex items-center space-x-1">
            {navigationItems.map((item) => (
              <Link
                key={item.id}
                href={item.path}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  router.pathname === item.path
                    ? darkMode
                      ? 'bg-blue-600 text-white'
                      : 'bg-blue-100 text-blue-900'
                    : darkMode
                      ? 'text-gray-300 hover:bg-gray-800 hover:text-white'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                }`}
                title={item.description}
              >
                <span className="mr-2">{item.icon}</span>
                {item.label}
              </Link>
            ))}
          </nav>

          {/* Actions et contrôles */}
          <div className="flex items-center space-x-2">
            {/* Indicateur agent actuel */}
            {currentAgent && (
              <div className={`hidden sm:flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                darkMode
                  ? 'bg-gray-800 text-gray-300'
                  : 'bg-gray-100 text-gray-700'
              }`}>
                <Star size={12} className="mr-1" />
                {currentAgent}
              </div>
            )}

            {/* Toggle dark mode */}
            <button
              onClick={onToggleDarkMode}
              className={`p-2 rounded-lg transition-colors ${
                darkMode
                  ? 'bg-gray-800 hover:bg-gray-700 text-yellow-400'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
              }`}
              title="Basculer le mode sombre"
            >
              {darkMode ? <Sun size={18} /> : <Moon size={18} />}
            </button>

            {/* Menu mobile */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`lg:hidden p-2 rounded-lg transition-colors ${
                darkMode
                  ? 'bg-gray-800 hover:bg-gray-700 text-gray-300'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
            >
              {isMenuOpen ? <X size={20} /> : <Menu size={20} />}
            </button>
          </div>
        </div>

        {/* Menu mobile */}
        {isMenuOpen && (
          <div className={`lg:hidden border-t ${
            darkMode ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="py-4 space-y-2">
              {navigationItems.map((item) => (
                <Link
                  key={item.id}
                  href={item.path}
                  className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    router.pathname === item.path
                      ? darkMode
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-100 text-blue-900'
                      : darkMode
                        ? 'text-gray-300 hover:bg-gray-800 hover:text-white'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className="mr-3 text-lg">{item.icon}</span>
                  <div>
                    <div>{item.label}</div>
                    <div className={`text-xs ${
                      darkMode ? 'text-gray-500' : 'text-gray-500'
                    }`}>
                      {item.description}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default HanumanHeader;
