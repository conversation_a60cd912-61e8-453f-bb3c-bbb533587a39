/**
 * Service de connexion entre l'interface Cycles Naturels et les agents existants
 * Intègre les agents web-research, evolution, et monitoring pour la synchronisation
 */

import { EventEmitter } from 'events';

// Types pour la communication avec les agents
interface AgentMessage {
  type: string;
  agent: string;
  data: any;
  timestamp: Date;
}

interface WeatherRequest {
  location: string;
  includeAstronomy: boolean;
  includeForecast: boolean;
}

interface EvolutionOptimizationRequest {
  context: {
    season: string;
    lunar: string;
    circadian: string;
    weather?: any;
  };
  objectives: string[];
  timeframe: string;
}

interface AgentResponse {
  success: boolean;
  data: any;
  error?: string;
  timestamp: Date;
}

export class NaturalCyclesAgentConnector extends EventEmitter {
  private wsConnections: Map<string, WebSocket> = new Map();
  private agentEndpoints = {
    'web-research': 'ws://localhost:8081/web-research',
    'evolution': 'ws://localhost:8082/evolution',
    'monitoring': 'ws://localhost:8083/monitoring',
    'cortex-central': 'ws://localhost:8080/cortex-central'
  };
  private reconnectAttempts = new Map<string, number>();
  private maxReconnectAttempts = 5;
  private reconnectDelay = 3000;

  constructor() {
    super();
    this.initializeConnections();
  }

  /**
   * Initialise les connexions WebSocket avec tous les agents
   */
  private async initializeConnections(): Promise<void> {
    for (const [agentName, endpoint] of Object.entries(this.agentEndpoints)) {
      await this.connectToAgent(agentName, endpoint);
    }
  }

  /**
   * Établit une connexion WebSocket avec un agent spécifique
   */
  private async connectToAgent(agentName: string, endpoint: string): Promise<void> {
    try {
      const ws = new WebSocket(endpoint);
      
      ws.onopen = () => {
        console.log(`✅ Connexion établie avec l'agent ${agentName}`);
        this.wsConnections.set(agentName, ws);
        this.reconnectAttempts.set(agentName, 0);
        this.emit('agent-connected', { agent: agentName });
        
        // Envoyer un message d'identification
        this.sendToAgent(agentName, {
          type: 'identify',
          source: 'natural-cycles-interface',
          capabilities: ['weather-monitoring', 'energy-optimization', 'circadian-analysis']
        });
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleAgentMessage(agentName, message);
        } catch (error) {
          console.error(`Erreur parsing message de ${agentName}:`, error);
        }
      };

      ws.onclose = () => {
        console.log(`🔌 Connexion fermée avec l'agent ${agentName}`);
        this.wsConnections.delete(agentName);
        this.emit('agent-disconnected', { agent: agentName });
        this.scheduleReconnect(agentName, endpoint);
      };

      ws.onerror = (error) => {
        console.error(`❌ Erreur connexion avec l'agent ${agentName}:`, error);
        this.emit('agent-error', { agent: agentName, error });
      };

    } catch (error) {
      console.error(`Impossible de se connecter à l'agent ${agentName}:`, error);
      this.scheduleReconnect(agentName, endpoint);
    }
  }

  /**
   * Programme une reconnexion automatique
   */
  private scheduleReconnect(agentName: string, endpoint: string): void {
    const attempts = this.reconnectAttempts.get(agentName) || 0;
    
    if (attempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        console.log(`🔄 Tentative de reconnexion ${attempts + 1}/${this.maxReconnectAttempts} pour ${agentName}`);
        this.reconnectAttempts.set(agentName, attempts + 1);
        this.connectToAgent(agentName, endpoint);
      }, this.reconnectDelay * (attempts + 1));
    } else {
      console.error(`❌ Échec de reconnexion à l'agent ${agentName} après ${this.maxReconnectAttempts} tentatives`);
      this.emit('agent-connection-failed', { agent: agentName });
    }
  }

  /**
   * Traite les messages reçus des agents
   */
  private handleAgentMessage(agentName: string, message: any): void {
    console.log(`📨 Message reçu de ${agentName}:`, message.type);
    
    switch (message.type) {
      case 'weather-data':
        this.emit('weather-update', {
          agent: agentName,
          data: message.data,
          timestamp: new Date()
        });
        break;
        
      case 'optimization-result':
        this.emit('optimization-update', {
          agent: agentName,
          data: message.data,
          timestamp: new Date()
        });
        break;
        
      case 'seasonal-insight':
        this.emit('insight-generated', {
          agent: agentName,
          insight: message.data,
          timestamp: new Date()
        });
        break;
        
      case 'circadian-adjustment':
        this.emit('circadian-update', {
          agent: agentName,
          data: message.data,
          timestamp: new Date()
        });
        break;
        
      case 'monitoring-alert':
        this.emit('monitoring-alert', {
          agent: agentName,
          alert: message.data,
          timestamp: new Date()
        });
        break;
        
      default:
        this.emit('agent-message', {
          agent: agentName,
          message,
          timestamp: new Date()
        });
    }
  }

  /**
   * Envoie un message à un agent spécifique
   */
  private sendToAgent(agentName: string, data: any): boolean {
    const ws = this.wsConnections.get(agentName);
    
    if (ws && ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify({
          ...data,
          timestamp: new Date().toISOString(),
          source: 'natural-cycles-interface'
        }));
        return true;
      } catch (error) {
        console.error(`Erreur envoi message à ${agentName}:`, error);
        return false;
      }
    } else {
      console.warn(`Agent ${agentName} non connecté`);
      return false;
    }
  }

  /**
   * Demande des données météorologiques à l'agent web-research
   */
  public async requestWeatherData(location: string = 'current'): Promise<void> {
    const request: WeatherRequest = {
      location,
      includeAstronomy: true,
      includeForecast: true
    };

    this.sendToAgent('web-research', {
      type: 'weather-request',
      query: `weather conditions ${location} astronomy lunar phases`,
      sources: [
        { type: 'web', priority: 'high' },
        { type: 'api', priority: 'high' }
      ],
      options: {
        maxResults: 10,
        realTime: true,
        includeAstronomy: true
      },
      data: request
    });
  }

  /**
   * Demande une optimisation énergétique à l'agent evolution
   */
  public async requestEnergyOptimization(context: any): Promise<void> {
    const request: EvolutionOptimizationRequest = {
      context,
      objectives: [
        'maximize-energy-efficiency',
        'optimize-circadian-alignment',
        'enhance-seasonal-adaptation'
      ],
      timeframe: '24h'
    };

    this.sendToAgent('evolution', {
      type: 'optimization-request',
      target: {
        problem: 'natural-cycles-energy-optimization',
        domain: 'temporal-optimization'
      },
      objectives: request.objectives.map(obj => ({
        metric: obj,
        weight: 1.0,
        direction: 'maximize'
      })),
      constraints: {
        seasonal: context.season,
        lunar: context.lunar,
        circadian: context.circadian
      },
      data: request
    });
  }

  /**
   * Demande une analyse des tendances saisonnières
   */
  public async requestSeasonalTrends(): Promise<void> {
    this.sendToAgent('web-research', {
      type: 'trend-analysis-request',
      query: 'seasonal energy patterns circadian rhythms productivity optimization',
      sources: [
        { type: 'academic', priority: 'high' },
        { type: 'web', priority: 'medium' }
      ],
      options: {
        timeFrame: 'last_year',
        depth: 'deep',
        includeScientific: true
      }
    });
  }

  /**
   * Demande des données astronomiques en temps réel
   */
  public async requestAstronomicalData(): Promise<void> {
    this.sendToAgent('web-research', {
      type: 'astronomy-request',
      query: 'lunar phases planetary positions astronomical events',
      sources: [
        { type: 'api', priority: 'high', domains: ['astronomy.com', 'timeanddate.com'] }
      ],
      options: {
        realTime: true,
        includeCalculations: true
      }
    });
  }

  /**
   * Active le monitoring continu des cycles naturels
   */
  public async startContinuousMonitoring(): Promise<void> {
    // Démarrer le monitoring météorologique
    this.sendToAgent('monitoring', {
      type: 'start-monitoring',
      targets: ['weather', 'astronomy', 'energy-patterns'],
      interval: 300000, // 5 minutes
      alerts: {
        weather: { threshold: 'significant-change' },
        astronomy: { threshold: 'phase-change' },
        energy: { threshold: 'optimization-opportunity' }
      }
    });

    // Programmer des requêtes périodiques
    setInterval(() => {
      this.requestWeatherData();
    }, 600000); // 10 minutes

    setInterval(() => {
      this.requestAstronomicalData();
    }, 3600000); // 1 heure

    setInterval(() => {
      this.requestSeasonalTrends();
    }, 86400000); // 24 heures
  }

  /**
   * Arrête le monitoring continu
   */
  public async stopContinuousMonitoring(): Promise<void> {
    this.sendToAgent('monitoring', {
      type: 'stop-monitoring',
      targets: ['weather', 'astronomy', 'energy-patterns']
    });
  }

  /**
   * Ferme toutes les connexions
   */
  public disconnect(): void {
    for (const [agentName, ws] of this.wsConnections) {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    }
    this.wsConnections.clear();
    this.removeAllListeners();
  }

  /**
   * Vérifie l'état des connexions
   */
  public getConnectionStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {};
    
    for (const agentName of Object.keys(this.agentEndpoints)) {
      const ws = this.wsConnections.get(agentName);
      status[agentName] = ws ? ws.readyState === WebSocket.OPEN : false;
    }
    
    return status;
  }

  /**
   * Envoie une requête de synchronisation globale
   */
  public async synchronizeWithAllAgents(currentState: any): Promise<void> {
    const syncMessage = {
      type: 'sync-request',
      state: currentState,
      requestedData: [
        'current-weather',
        'astronomical-positions',
        'energy-optimization',
        'seasonal-insights'
      ]
    };

    for (const agentName of Object.keys(this.agentEndpoints)) {
      this.sendToAgent(agentName, syncMessage);
    }
  }
}

export default NaturalCyclesAgentConnector;
