import React, { useState, useEffect, useRef } from 'react';
import { Database, Brain, Layers, Search, Archive, Zap, HardDrive, Cloud, Activity } from 'lucide-react';

// Types pour l'interface de mémoire distribuée
interface MemoryNode {
  id: string;
  type: 'central' | 'specialized' | 'working' | 'archive';
  name: string;
  status: 'active' | 'syncing' | 'offline' | 'error';
  capacity: number;
  used: number;
  connections: number;
  lastSync: Date;
  location: 'weaviate' | 'pinecone' | 'redis' | 'local';
}

interface MemoryVector {
  id: string;
  content: string;
  embedding: number[];
  metadata: {
    agent: string;
    timestamp: Date;
    category: string;
    importance: number;
    accessCount: number;
  };
  similarity?: number;
}

interface MemoryMetrics {
  totalVectors: number;
  activeQueries: number;
  averageLatency: number;
  hitRate: number;
  syncStatus: number;
  storageEfficiency: number;
}

interface SearchQuery {
  id: string;
  query: string;
  timestamp: Date;
  results: number;
  latency: number;
  agent: string;
}

const HanumanMemoryInterface = ({ darkMode = true }) => {
  const [memoryNodes, setMemoryNodes] = useState<MemoryNode[]>([]);
  const [recentVectors, setRecentVectors] = useState<MemoryVector[]>([]);
  const [metrics, setMetrics] = useState<MemoryMetrics>({
    totalVectors: 15847,
    activeQueries: 23,
    averageLatency: 45,
    hitRate: 0.87,
    syncStatus: 0.94,
    storageEfficiency: 0.82
  });
  const [recentQueries, setRecentQueries] = useState<SearchQuery[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<MemoryVector[]>([]);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // Simulation des nœuds de mémoire
  useEffect(() => {
    const mockNodes: MemoryNode[] = [
      {
        id: 'central-memory',
        type: 'central',
        name: 'Mémoire Centrale Weaviate',
        status: 'active',
        capacity: 100000,
        used: 15847,
        connections: 12,
        lastSync: new Date(),
        location: 'weaviate'
      },
      {
        id: 'specialized-frontend',
        type: 'specialized',
        name: 'Mémoire Frontend',
        status: 'active',
        capacity: 25000,
        used: 3421,
        connections: 3,
        lastSync: new Date(Date.now() - 30000),
        location: 'pinecone'
      },
      {
        id: 'specialized-backend',
        type: 'specialized',
        name: 'Mémoire Backend',
        status: 'syncing',
        capacity: 25000,
        used: 4567,
        connections: 4,
        lastSync: new Date(Date.now() - 45000),
        location: 'pinecone'
      },
      {
        id: 'working-memory',
        type: 'working',
        name: 'Mémoire de Travail',
        status: 'active',
        capacity: 5000,
        used: 1234,
        connections: 8,
        lastSync: new Date(Date.now() - 5000),
        location: 'redis'
      },
      {
        id: 'archive-memory',
        type: 'archive',
        name: 'Archive Historique',
        status: 'active',
        capacity: 500000,
        used: 89456,
        connections: 2,
        lastSync: new Date(Date.now() - 120000),
        location: 'local'
      }
    ];
    setMemoryNodes(mockNodes);

    const mockVectors: MemoryVector[] = [
      {
        id: 'vec-001',
        content: 'Successful deployment pattern for React components',
        embedding: [0.1, 0.2, 0.3, 0.4, 0.5],
        metadata: {
          agent: 'agent-frontend',
          timestamp: new Date(),
          category: 'deployment',
          importance: 0.9,
          accessCount: 15
        }
      },
      {
        id: 'vec-002',
        content: 'Security vulnerability detection in API endpoints',
        embedding: [0.2, 0.3, 0.4, 0.5, 0.6],
        metadata: {
          agent: 'agent-security',
          timestamp: new Date(Date.now() - 60000),
          category: 'security',
          importance: 0.95,
          accessCount: 8
        }
      },
      {
        id: 'vec-003',
        content: 'Database optimization strategies for high load',
        embedding: [0.3, 0.4, 0.5, 0.6, 0.7],
        metadata: {
          agent: 'agent-backend',
          timestamp: new Date(Date.now() - 120000),
          category: 'performance',
          importance: 0.85,
          accessCount: 23
        }
      }
    ];
    setRecentVectors(mockVectors);

    const mockQueries: SearchQuery[] = [
      {
        id: 'query-001',
        query: 'React component optimization',
        timestamp: new Date(),
        results: 12,
        latency: 34,
        agent: 'agent-frontend'
      },
      {
        id: 'query-002',
        query: 'Security best practices',
        timestamp: new Date(Date.now() - 30000),
        results: 8,
        latency: 67,
        agent: 'agent-security'
      },
      {
        id: 'query-003',
        query: 'Database performance tuning',
        timestamp: new Date(Date.now() - 60000),
        results: 15,
        latency: 45,
        agent: 'agent-backend'
      }
    ];
    setRecentQueries(mockQueries);
  }, []);

  // Connexion WebSocket pour les mises à jour en temps réel
  useEffect(() => {
    // Simulation de connexion WebSocket avec les systèmes de mémoire
    wsRef.current = new WebSocket('ws://localhost:8080/memory');
    
    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'vector_added') {
        setRecentVectors(prev => [data.vector, ...prev.slice(0, 9)]);
      } else if (data.type === 'query_executed') {
        setRecentQueries(prev => [data.query, ...prev.slice(0, 9)]);
      } else if (data.type === 'metrics_update') {
        setMetrics(data.metrics);
      }
    };

    return () => {
      wsRef.current?.close();
    };
  }, []);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    // Simulation de recherche vectorielle
    const mockResults: MemoryVector[] = [
      {
        id: 'search-001',
        content: `Result for "${searchQuery}" - Advanced implementation pattern`,
        embedding: [0.1, 0.2, 0.3],
        metadata: {
          agent: 'agent-evolution',
          timestamp: new Date(),
          category: 'pattern',
          importance: 0.88,
          accessCount: 5
        },
        similarity: 0.92
      },
      {
        id: 'search-002',
        content: `Related to "${searchQuery}" - Best practices documentation`,
        embedding: [0.2, 0.3, 0.4],
        metadata: {
          agent: 'agent-documentation',
          timestamp: new Date(Date.now() - 30000),
          category: 'documentation',
          importance: 0.75,
          accessCount: 12
        },
        similarity: 0.85
      }
    ];

    setSearchResults(mockResults);

    // Envoyer la requête aux systèmes de mémoire
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'search_query',
        query: searchQuery,
        agent: 'hanuman-interface'
      }));
    }
  };

  const getNodeStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'syncing': return 'text-yellow-400';
      case 'offline': return 'text-gray-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getNodeIcon = (type: string) => {
    switch (type) {
      case 'central': return <Database className="w-5 h-5" />;
      case 'specialized': return <Brain className="w-5 h-5" />;
      case 'working': return <Zap className="w-5 h-5" />;
      case 'archive': return <Archive className="w-5 h-5" />;
      default: return <HardDrive className="w-5 h-5" />;
    }
  };

  const getLocationIcon = (location: string) => {
    switch (location) {
      case 'weaviate': return <Cloud className="w-4 h-4 text-blue-400" />;
      case 'pinecone': return <Cloud className="w-4 h-4 text-green-400" />;
      case 'redis': return <Zap className="w-4 h-4 text-red-400" />;
      case 'local': return <HardDrive className="w-4 h-4 text-gray-400" />;
      default: return <Database className="w-4 h-4" />;
    }
  };

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* En-tête */}
      <div className="mb-8">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl">
            <Database className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              Mémoire Distribuée Hanuman
            </h1>
            <p className="text-gray-400 mt-1">
              Système de mémoire vectorielle multi-niveaux
            </p>
          </div>
        </div>
      </div>

      {/* Métriques globales */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Vecteurs Totaux</p>
              <p className="text-2xl font-bold text-blue-400">{metrics.totalVectors.toLocaleString()}</p>
            </div>
            <Layers className="w-8 h-8 text-blue-400" />
          </div>
        </div>
        
        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Requêtes Actives</p>
              <p className="text-2xl font-bold text-green-400">{metrics.activeQueries}</p>
            </div>
            <Activity className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Latence Moyenne</p>
              <p className="text-2xl font-bold text-purple-400">{metrics.averageLatency}ms</p>
            </div>
            <Zap className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Taux de Succès</p>
              <p className="text-2xl font-bold text-yellow-400">{(metrics.hitRate * 100).toFixed(0)}%</p>
            </div>
            <Search className="w-8 h-8 text-yellow-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Sync Status</p>
              <p className="text-2xl font-bold text-cyan-400">{(metrics.syncStatus * 100).toFixed(0)}%</p>
            </div>
            <Cloud className="w-8 h-8 text-cyan-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Efficacité</p>
              <p className="text-2xl font-bold text-pink-400">{(metrics.storageEfficiency * 100).toFixed(0)}%</p>
            </div>
            <HardDrive className="w-8 h-8 text-pink-400" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Nœuds de mémoire */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <Database className="w-5 h-5 mr-2 text-blue-400" />
            Nœuds de Mémoire
          </h3>
          
          <div className="space-y-3">
            {memoryNodes.map((node) => (
              <div
                key={node.id}
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedNode === node.id
                    ? 'border-blue-500 bg-blue-500/10'
                    : darkMode ? 'border-gray-600 bg-gray-700 hover:bg-gray-600' : 'border-gray-300 bg-gray-100 hover:bg-gray-200'
                }`}
                onClick={() => setSelectedNode(selectedNode === node.id ? null : node.id)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <div className={getNodeStatusColor(node.status)}>
                      {getNodeIcon(node.type)}
                    </div>
                    <div>
                      <h4 className="font-medium">{node.name}</h4>
                      <p className="text-sm text-gray-400">{node.type}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getLocationIcon(node.location)}
                    <span className={`text-sm ${getNodeStatusColor(node.status)}`}>
                      {node.status}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-400">
                  <span>Utilisé: {((node.used / node.capacity) * 100).toFixed(1)}%</span>
                  <span>{node.connections} connexions</span>
                </div>
                
                <div className="mt-2">
                  <div className="w-full bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(node.used / node.capacity) * 100}%` }}
                    />
                  </div>
                </div>
                
                {selectedNode === node.id && (
                  <div className="mt-3 pt-3 border-t border-gray-600">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Capacité:</span>
                        <span className="ml-2">{node.capacity.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Utilisé:</span>
                        <span className="ml-2">{node.used.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Dernière sync:</span>
                        <span className="ml-2">{node.lastSync.toLocaleTimeString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Localisation:</span>
                        <span className="ml-2 capitalize">{node.location}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Recherche vectorielle */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <Search className="w-5 h-5 mr-2 text-green-400" />
            Recherche Vectorielle
          </h3>
          
          <div className="mb-4">
            <div className="flex space-x-2">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Rechercher dans la mémoire..."
                className={`flex-1 px-4 py-2 rounded-lg border ${
                  darkMode 
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <button
                onClick={handleSearch}
                className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                <Search className="w-4 h-4" />
              </button>
            </div>
          </div>

          {searchResults.length > 0 && (
            <div className="space-y-3 mb-4">
              <h4 className="font-medium text-gray-400">Résultats de recherche:</h4>
              {searchResults.map((result) => (
                <div
                  key={result.id}
                  className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-green-400">
                      Similarité: {(result.similarity! * 100).toFixed(1)}%
                    </span>
                    <span className="text-xs text-gray-400">
                      {result.metadata.agent}
                    </span>
                  </div>
                  <p className="text-sm mb-2">{result.content}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-400">
                    <span>Importance: {(result.metadata.importance * 100).toFixed(0)}%</span>
                    <span>Accès: {result.metadata.accessCount}</span>
                    <span>{result.metadata.category}</span>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="space-y-3">
            <h4 className="font-medium text-gray-400">Requêtes récentes:</h4>
            {recentQueries.map((query) => (
              <div
                key={query.id}
                className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="font-medium">{query.query}</span>
                  <span className="text-xs text-gray-400">
                    {query.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <div className="flex items-center space-x-4 text-xs text-gray-400">
                  <span>{query.results} résultats</span>
                  <span>{query.latency}ms</span>
                  <span>{query.agent}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Vecteurs récents */}
      <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
        <h3 className="text-xl font-semibold mb-4 flex items-center">
          <Brain className="w-5 h-5 mr-2 text-purple-400" />
          Vecteurs Récemment Ajoutés
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {recentVectors.map((vector) => (
            <div
              key={vector.id}
              className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-purple-400">
                  {vector.metadata.category}
                </span>
                <span className="text-xs text-gray-400">
                  {vector.metadata.timestamp.toLocaleTimeString()}
                </span>
              </div>
              <p className="text-sm mb-3">{vector.content}</p>
              <div className="flex items-center justify-between text-xs text-gray-400">
                <span>{vector.metadata.agent}</span>
                <div className="flex items-center space-x-2">
                  <span>Importance: {(vector.metadata.importance * 100).toFixed(0)}%</span>
                  <span>Accès: {vector.metadata.accessCount}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HanumanMemoryInterface;
