# 🐒 Head<PERSON> - Interface Divine

## Vue d'ensemble

Le Header <PERSON> est un composant spécialement conçu pour l'interface Hanuman-working, distinct du header du frontend principal Retreat & Be. Il offre une navigation dédiée à l'écosystème d'agents IA et à l'architecture Trimurti.

## Fonctionnalités

### 🎨 Design Unique
- **Identité visuelle distincte** : Logo <PERSON> avec indicateur de statut en temps réel
- **Thème sombre par défaut** : Optimisé pour l'interface d'agents IA
- **Animations fluides** : Transitions et effets visuels pour une expérience immersive
- **Responsive design** : Adaptation mobile et desktop

### 🧭 Navigation Spécialisée
- **Hub Central** : Orchestrateur principal des agents
- **Trimurti Dashboard** : Interface de conscience divine (<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>)
- **Réseau d'Agents** : Architecture distribuée des agents IA
- **Palais Mémoire** : Système de mémoire distribuée
- **Cycles Naturels** : Rythmes cosmiques et synchronisation

### 🔧 Fonctionnalités Techniques
- **Indicateur de statut système** : Optimal, Learning, Processing, Maintenance
- **Agent actuel** : Affichage de l'agent en cours d'utilisation
- **Retour au frontend principal** : Bouton de navigation vers Retreat & Be
- **Toggle dark/light mode** : Basculement des thèmes
- **Menu mobile responsive** : Navigation adaptée aux petits écrans

## Intégration avec le Frontend Principal

### Bouton d'accès dans UnifiedNavigation

Le header du frontend principal (Retreat & Be) a été modifié pour inclure un bouton "Hanuman IA" qui :

1. **Ouvre l'interface Hanuman** dans un nouvel onglet
2. **Pointe vers localhost:3001** (port dédié à Hanuman)
3. **Badge IA distinctif** avec couleur orange
4. **Description explicite** : "Interface avec l'être IA vivant Hanuman"

### Configuration

```typescript
{
  id: 'hanuman',
  label: 'Hanuman IA',
  path: '/hanuman',
  icon: '🐒',
  description: 'Interface avec l\'être IA vivant Hanuman',
  badge: 'IA',
}
```

## Utilisation

### Démarrage de l'interface Hanuman

```bash
# Option 1: Script automatique
./hanuman-working/start-hanuman-interface.sh

# Option 2: Commandes manuelles
cd hanuman-working
npm install
npm run dev
```

L'interface sera accessible sur `http://localhost:3001`

### Accès depuis le frontend principal

1. Démarrer le frontend principal Retreat & Be
2. Naviguer dans la sidebar
3. Cliquer sur "Hanuman IA" 🐒
4. L'interface s'ouvre dans un nouvel onglet

## Structure des Composants

```
hanuman-working/
├── components/
│   └── HanumanHeader.tsx          # Header principal
├── pages/
│   └── index.tsx                  # Page d'accueil avec header
├── start-hanuman-interface.sh     # Script de démarrage
└── package.json                   # Configuration port 3001
```

## Props du HanumanHeader

```typescript
interface HanumanHeaderProps {
  darkMode?: boolean;                // Mode sombre (défaut: true)
  onToggleDarkMode?: () => void;     // Fonction toggle thème
  showBackToMain?: boolean;          // Afficher bouton retour (défaut: true)
  currentAgent?: string;             // Agent actuellement actif
  systemStatus?: 'optimal' | 'learning' | 'processing' | 'maintenance';
}
```

## Statuts Système

- **🟢 Optimal** : Système fonctionnel, agents actifs
- **🔵 Learning** : Apprentissage en cours, neuroplasticité active
- **🟡 Processing** : Traitement intensif, calculs en cours
- **🔴 Maintenance** : Maintenance système, auto-réparation

## Personnalisation

### Couleurs et Thèmes
- **Gradient Hanuman** : Orange → Rouge → Rose
- **Statuts colorés** : Vert, Bleu, Jaune, Rouge
- **Mode sombre optimisé** : Gris 900/800/700
- **Accents** : Bleu pour navigation active

### Navigation
- **Liens internes** : Navigation Next.js
- **Lien externe** : Retour vers frontend principal
- **Menu mobile** : Collapse/expand avec animations

## Intégration Future

Le header est conçu pour s'intégrer avec :
- **Système de notifications** des agents
- **Métriques en temps réel** du système
- **Chat inter-agents** 
- **Monitoring de performance**
- **Logs d'activité divine** 🕉️

---

*AUM HANUMATE NAMAHA* 🙏
*Retreat And Be - Protection Divine Active* ✨
