/**
 * 🐒 Démonstration du Header Hanuman
 * Test et présentation des fonctionnalités du header
 */

import React, { useState } from 'react';
import HanumanHeader from './components/HanumanHeader';

const HeaderDemo: React.FC = () => {
  const [darkMode, setDarkMode] = useState(true);
  const [currentAgent, setCurrentAgent] = useState('Cortex Central');
  const [systemStatus, setSystemStatus] = useState<'optimal' | 'learning' | 'processing' | 'maintenance'>('optimal');

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const agents = [
    'Cortex Central',
    'Système Limbique', 
    'Cortex Créatif',
    'Cortex Logique',
    'Système Immunitaire',
    'Neuroplasticité'
  ];

  const statuses: Array<'optimal' | 'learning' | 'processing' | 'maintenance'> = [
    'optimal',
    'learning', 
    'processing',
    'maintenance'
  ];

  return (
    <div className={`min-h-screen ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <HanumanHeader
        darkMode={darkMode}
        onToggleDarkMode={toggleDarkMode}
        showBackToMain={true}
        currentAgent={currentAgent}
        systemStatus={systemStatus}
      />
      
      <main className="pt-20 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className={`text-4xl font-bold mb-8 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            🐒 Démonstration Header Hanuman
          </h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Contrôles de test */}
            <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
              <h2 className={`text-2xl font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                🎛️ Contrôles de Test
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Agent Actuel
                  </label>
                  <select
                    value={currentAgent}
                    onChange={(e) => setCurrentAgent(e.target.value)}
                    className={`w-full p-2 rounded border ${
                      darkMode 
                        ? 'bg-gray-700 border-gray-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    {agents.map(agent => (
                      <option key={agent} value={agent}>{agent}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Statut Système
                  </label>
                  <select
                    value={systemStatus}
                    onChange={(e) => setSystemStatus(e.target.value as any)}
                    className={`w-full p-2 rounded border ${
                      darkMode 
                        ? 'bg-gray-700 border-gray-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    {statuses.map(status => (
                      <option key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
                
                <button
                  onClick={toggleDarkMode}
                  className={`w-full p-3 rounded-lg font-medium transition-colors ${
                    darkMode
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-blue-500 hover:bg-blue-600 text-white'
                  }`}
                >
                  {darkMode ? '☀️ Mode Clair' : '🌙 Mode Sombre'}
                </button>
              </div>
            </div>
            
            {/* Informations */}
            <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
              <h2 className={`text-2xl font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                ℹ️ Informations
              </h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Fonctionnalités testées :
                  </h3>
                  <ul className={`mt-2 space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    <li>✅ Navigation responsive</li>
                    <li>✅ Toggle dark/light mode</li>
                    <li>✅ Indicateur de statut système</li>
                    <li>✅ Affichage agent actuel</li>
                    <li>✅ Bouton retour frontend principal</li>
                    <li>✅ Menu mobile</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Navigation disponible :
                  </h3>
                  <ul className={`mt-2 space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    <li>🧠 Hub Central</li>
                    <li>🕉️ Trimurti Dashboard</li>
                    <li>🌐 Réseau d'Agents</li>
                    <li>🏛️ Palais Mémoire</li>
                    <li>🌙 Cycles Naturels</li>
                  </ul>
                </div>
                
                <div className={`p-3 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    💡 Astuce : Testez le menu mobile en réduisant la largeur de la fenêtre
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Statut actuel */}
          <div className={`mt-8 p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
            <h2 className={`text-2xl font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              📊 État Actuel
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className={`p-4 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Mode Thème
                </h3>
                <p className={`text-lg ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {darkMode ? '🌙 Sombre' : '☀️ Clair'}
                </p>
              </div>
              
              <div className={`p-4 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Agent Actuel
                </h3>
                <p className={`text-lg ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  🤖 {currentAgent}
                </p>
              </div>
              
              <div className={`p-4 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <h3 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Statut Système
                </h3>
                <p className={`text-lg ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {systemStatus === 'optimal' && '🟢 Optimal'}
                  {systemStatus === 'learning' && '🔵 Learning'}
                  {systemStatus === 'processing' && '🟡 Processing'}
                  {systemStatus === 'maintenance' && '🔴 Maintenance'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default HeaderDemo;
