/**
 * 🐒 Page d'accueil Hanuman - Redirection vers Chat
 * Point d'entrée qui redirige vers l'interface de discussion
 */

import { useEffect } from 'react';
import { useRouter } from 'next/router';

export default function HanumanHomePage() {
  const router = useRouter();

  useEffect(() => {
    // Redirection automatique vers la page de chat
    router.push('/chat');
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-3xl animate-pulse">🐒</span>
        </div>
        <h1 className="text-2xl font-bold text-white mb-2">Hanuman s'éveille...</h1>
        <p className="text-gray-400">Redirection vers l'interface de discussion</p>
      </div>
    </div>
  );
}
