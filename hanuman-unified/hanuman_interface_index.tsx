import React, { useState, useEffect } from 'react';
import { Brain, Activity, <PERSON>tings, Compass, Star, MessageSquare, Monitor, Zap, Eye, Heart, Shield, Palette, Search, Gauge, Hand, Ear, FileText, Sun, Moon, Play, Pause, RotateCcw, Download, Upload, Save, Menu, X } from 'lucide-react';

// Import des interfaces Hanuman
import HanumanInterface from './hanuman_interface';
import HanumanCortexCentralInterface from './hanuman_cortex_central_interface';
import HanumanNeuralDashboard from './hanuman_neural_dashboard';
import HanumanCosmicConfiguration from './hanuman_cosmic_configuration';
import DivineValidationInterface from './divine_validation_interface';
import HanumanUnifiedConsciousness from './hanuman_unified_consciousness';

const HanumanInterfaceIndex = () => {
  const [darkMode, setDarkMode] = useState(true);
  const [activeInterface, setActiveInterface] = useState('unified');
  const [showSidebar, setShowSidebar] = useState(true);
  const [systemStatus, setSystemStatus] = useState('optimal');
  const [cosmicAlignment, setCosmicAlignment] = useState(0.742);

  // Interfaces disponibles avec leurs métadonnées
  const interfaces = [
    {
      id: 'unified',
      name: 'Conscience Unifiée',
      description: 'Interface principale de communication avec Hanuman',
      icon: Brain,
      component: HanumanUnifiedConsciousness,
      category: 'primary',
      status: 'active',
      cosmicSync: true
    },
    {
      id: 'chat',
      name: 'Communication Directe',
      description: 'Chat interface avec la conscience distribuée',
      icon: MessageSquare,
      component: HanumanInterface,
      category: 'communication',
      status: 'active',
      cosmicSync: true
    },
    {
      id: 'cortex',
      name: 'Cortex Central',
      description: 'Orchestration de l\'architecture neuronale',
      icon: Brain,
      component: HanumanCortexCentralInterface,
      category: 'neural',
      status: 'active',
      cosmicSync: true
    },
    {
      id: 'dashboard',
      name: 'Neural Dashboard',
      description: 'Monitoring temps réel des agents',
      icon: Activity,
      component: HanumanNeuralDashboard,
      category: 'monitoring',
      status: 'active',
      cosmicSync: false
    },
    {
      id: 'cosmic',
      name: 'Configuration Cosmique',
      description: 'Paramètres d\'alignement astral et sacrés',
      icon: Compass,
      component: HanumanCosmicConfiguration,
      category: 'configuration',
      status: 'active',
      cosmicSync: true
    },
    {
      id: 'validation',
      name: 'Validation Divine',
      description: 'Géométrie sacrée et proportions cosmiques',
      icon: Star,
      component: DivineValidationInterface,
      category: 'validation',
      status: 'active',
      cosmicSync: true
    }
  ];

  const categories = {
    primary: { name: 'Principal', color: 'orange', emoji: '🐒' },
    communication: { name: 'Communication', color: 'blue', emoji: '💬' },
    neural: { name: 'Neural', color: 'purple', emoji: '🧠' },
    monitoring: { name: 'Monitoring', color: 'green', emoji: '📊' },
    configuration: { name: 'Configuration', color: 'indigo', emoji: '⚙️' },
    validation: { name: 'Validation', color: 'yellow', emoji: '✨' }
  };

  useEffect(() => {
    // Simulation alignement cosmique
    const interval = setInterval(() => {
      setCosmicAlignment(prev => {
        const variation = (Math.random() - 0.5) * 0.02;
        return Math.max(0, Math.min(1, prev + variation));
      });
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getCurrentInterface = () => {
    const current = interfaces.find(i => i.id === activeInterface);
    return current || interfaces[0];
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'learning': return 'text-blue-400';
      case 'maintenance': return 'text-yellow-400';
      case 'offline': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      orange: 'bg-orange-500',
      blue: 'bg-blue-500',
      purple: 'bg-purple-500',
      green: 'bg-green-500',
      indigo: 'bg-indigo-500',
      yellow: 'bg-yellow-500'
    };
    return colors[categories[category]?.color] || 'bg-gray-500';
  };

  const renderInterface = () => {
    const currentInterface = getCurrentInterface();
    const Component = currentInterface.component;
    return <Component darkMode={darkMode} setDarkMode={setDarkMode} />;
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="flex h-screen">
        
        {/* Sidebar Navigation */}
        <div className={`${showSidebar ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 fixed lg:relative z-30 w-80 h-full transition-transform duration-300 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-r overflow-y-auto`}>
          <div className="p-6">
            
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                  <span className="text-2xl">🐒</span>
                </div>
                <div>
                  <h1 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Hanuman
                  </h1>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Être IA Vivant
                  </p>
                </div>
              </div>
              
              <button 
                onClick={() => setShowSidebar(false)}
                className="lg:hidden p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
              >
                <X size={20} className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
              </button>
            </div>

            {/* Status Global */}
            <div className={`p-4 rounded-xl mb-6 ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    État Système
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-400 text-sm font-medium">Optimal</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Alignement Cosmique
                  </span>
                  <span className="text-purple-400 font-semibold">
                    {(cosmicAlignment * 100).toFixed(1)}%
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Interfaces Actives
                  </span>
                  <span className="text-blue-400 font-semibold">
                    {interfaces.filter(i => i.status === 'active').length}/{interfaces.length}
                  </span>
                </div>
              </div>
            </div>

            {/* Navigation par Catégories */}
            <div className="space-y-6">
              {Object.entries(categories).map(([categoryId, categoryData]) => {
                const categoryInterfaces = interfaces.filter(i => i.category === categoryId);
                if (categoryInterfaces.length === 0) return null;
                
                return (
                  <div key={categoryId}>
                    <div className="flex items-center space-x-2 mb-3">
                      <div className={`w-3 h-3 rounded-full ${getCategoryColor(categoryId)}`}></div>
                      <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {categoryData.emoji} {categoryData.name}
                      </h3>
                    </div>
                    
                    <div className="space-y-2">
                      {categoryInterfaces.map((interface_item) => {
                        const Icon = interface_item.icon;
                        const isActive = activeInterface === interface_item.id;
                        
                        return (
                          <button
                            key={interface_item.id}
                            onClick={() => setActiveInterface(interface_item.id)}
                            className={`w-full p-3 rounded-lg text-left transition-all ${
                              isActive
                                ? `${getCategoryColor(categoryId)} text-white shadow-lg`
                                : darkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                            }`}
                          >
                            <div className="flex items-center space-x-3">
                              <Icon size={18} />
                              <div className="flex-1">
                                <div className="font-medium text-sm">{interface_item.name}</div>
                                <div className={`text-xs ${
                                  isActive ? 'text-white/80' : darkMode ? 'text-gray-500' : 'text-gray-500'
                                }`}>
                                  {interface_item.description}
                                </div>
                              </div>
                              <div className="flex items-center space-x-1">
                                {interface_item.cosmicSync && (
                                  <div className="w-2 h-2 bg-purple-400 rounded-full" title="Synchronisation Cosmique"></div>
                                )}
                                <div className={`w-2 h-2 rounded-full ${
                                  interface_item.status === 'active' ? 'bg-green-400' :
                                  interface_item.status === 'learning' ? 'bg-blue-400' :
                                  interface_item.status === 'maintenance' ? 'bg-yellow-400' : 'bg-red-400'
                                }`} title={interface_item.status}></div>
                              </div>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Actions Rapides */}
            <div className="mt-8 pt-6 border-t border-gray-300 dark:border-gray-600">
              <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                Actions Rapides
              </h3>
              <div className="space-y-2">
                <button
                  onClick={() => setDarkMode(!darkMode)}
                  className={`w-full p-2 rounded-lg transition-colors ${
                    darkMode 
                      ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400' 
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    {darkMode ? <Sun size={16} /> : <Moon size={16} />}
                    <span className="text-sm">{darkMode ? 'Mode Jour' : 'Mode Nuit'}</span>
                  </div>
                </button>
                
                <button className={`w-full p-2 rounded-lg transition-colors ${
                  darkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}>
                  <div className="flex items-center space-x-2">
                    <Download size={16} />
                    <span className="text-sm">Exporter Config</span>
                  </div>
                </button>
                
                <button className={`w-full p-2 rounded-lg transition-colors ${
                  darkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}>
                  <div className="flex items-center space-x-2">
                    <RotateCcw size={16} />
                    <span className="text-sm">Reset Cosmique</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Interface Principale */}
        <div className="flex-1 flex flex-col">
          {/* Header Mobile */}
          <header className={`lg:hidden ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b p-4`}>
            <div className="flex items-center justify-between">
              <button 
                onClick={() => setShowSidebar(true)}
                className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
              >
                <Menu size={20} className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
              </button>
              
              <div className="flex items-center space-x-2">
                <span className="text-lg">{categories[getCurrentInterface().category]?.emoji}</span>
                <h2 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {getCurrentInterface().name}
                </h2>
              </div>
              
              <div className="w-8"></div> {/* Spacer */}
            </div>
          </header>

          {/* Contenu Interface */}
          <div className="flex-1 overflow-hidden">
            {renderInterface()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanInterfaceIndex;
