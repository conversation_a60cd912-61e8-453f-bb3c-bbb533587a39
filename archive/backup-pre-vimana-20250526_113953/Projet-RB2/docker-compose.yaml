version: '3.8'

services:
  agent-rb:
    build:
      context: ./Agent-RB
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - ENVIRONMENT=development
      - SUPERAGENT_SERVICE_URL=http://superagent:5001
      - AGENT_IA_SERVICE_URL=http://agent-ia:5002
      - TRACING_ENABLED=true
      - JAEGER_HOST=jaeger
      - JAEGER_PORT=6831
      - SERVICE_NAME=agent-rb
      - ALERTING_ENABLED=true
      - EMAIL_ALERTS_ENABLED=false
      - SLACK_ALERTS_ENABLED=false
    volumes:
      - ./Agent-RB:/app
    depends_on:
      - superagent
      - agent-ia
      - jaeger
    networks:
      - retreat-network

  superagent:
    build:
      context: ./superagent
      dockerfile: Dockerfile
    ports:
      - "5001:8000"
    environment:
      - ENVIRONMENT=development
      - AGENT_RB_SERVICE_URL=http://agent-rb:5000
      - AGENT_IA_SERVICE_URL=http://agent-ia:5002
      - TRACING_ENABLED=true
      - JAEGER_HOST=jaeger
      - JAEGER_PORT=6831
      - SERVICE_NAME=superagent
    volumes:
      - ./superagent:/app
    depends_on:
      - jaeger
    networks:
      - retreat-network

  agent-ia:
    build:
      context: ./Agent\ IA
      dockerfile: Dockerfile.api
    ports:
      - "5002:5002"
    environment:
      - NODE_ENV=development
      - PORT=5002
      - AGENT_RB_SERVICE_URL=http://agent-rb:5000
      - SUPERAGENT_SERVICE_URL=http://superagent:5001
      - TRACING_ENABLED=true
      - JAEGER_HOST=jaeger
      - JAEGER_PORT=6831
      - SERVICE_NAME=agent-ia
    volumes:
      - ./Agent\ IA:/app
    depends_on:
      - jaeger
    networks:
      - retreat-network

  # Service de tracing distribué
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"  # UI
      - "14268:14268"
      - "14250:14250"
      - "9411:9411"
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=9411
    networks:
      - retreat-network

  # Service de base de données partagée
  postgres:
    image: postgres:14
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=retreat
      - POSTGRES_PASSWORD=retreat_password
      - POSTGRES_DB=retreat_db
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - retreat-network

  # Service Redis pour le cache et les files d'attente
  redis:
    image: redis:6
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - retreat-network

networks:
  retreat-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
