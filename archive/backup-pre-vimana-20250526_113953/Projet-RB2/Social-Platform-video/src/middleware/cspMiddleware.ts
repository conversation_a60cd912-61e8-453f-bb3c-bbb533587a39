import securityService from '../services/securityService';

/**
 * Type de requête (pour le typage TypeScript)
 */
interface Request {
  path: string;
}

/**
 * Type de réponse (pour le typage TypeScript)
 */
interface Response {
  setHeader: (name: string, value: string) => void;
}

/**
 * Type de fonction next (pour le typage TypeScript)
 */
type NextFunction = () => void;

/**
 * Configuration CSP par défaut
 */
const defaultCSP = {
  directives: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'"],
    styleSrc: ["'self'", "'unsafe-inline'"],
    imgSrc: ["'self'", "data:", "https://storage.retreatandbe.com"],
    connectSrc: ["'self'", "https://api.retreatandbe.com"],
    fontSrc: ["'self'", "https://fonts.gstatic.com"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'", "https://storage.retreatandbe.com"],
    frameSrc: ["'self'"],
    upgradeInsecureRequests: []
  }
};

/**
 * Configuration CSP pour les vidéos
 */
const videoCSP = {
  directives: {
    ...defaultCSP.directives,
    mediaSrc: ["'self'", "https://storage.retreatandbe.com", "blob:"],
    connectSrc: ["'self'", "https://api.retreatandbe.com", "wss://live.retreatandbe.com"],
    childSrc: ["blob:"],
    workerSrc: ["blob:"]
  }
};

/**
 * Configuration CSP pour les livestreams
 */
const livestreamCSP = {
  directives: {
    ...defaultCSP.directives,
    mediaSrc: ["'self'", "https://storage.retreatandbe.com", "blob:"],
    connectSrc: ["'self'", "https://api.retreatandbe.com", "wss://live.retreatandbe.com", "wss://*.webrtc.retreatandbe.com"],
    childSrc: ["blob:"],
    workerSrc: ["blob:"]
  }
};

/**
 * Convertit un objet de directives CSP en chaîne de caractères
 * @param directives Directives CSP
 */
function cspObjectToString(directives: Record<string, string[]>): string {
  return Object.entries(directives)
    .map(([key, values]) => {
      // Gestion spéciale pour upgradeInsecureRequests qui n'a pas de valeur
      if (key === 'upgradeInsecureRequests' && values.length === 0) {
        return 'upgrade-insecure-requests';
      }
      
      // Convertir camelCase en kebab-case
      const kebabKey = key.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
      
      return `${kebabKey} ${values.join(' ')}`;
    })
    .join('; ');
}

/**
 * Détermine le type de contenu en fonction du chemin de la requête
 * @param path Chemin de la requête
 */
function getContentType(path: string): string {
  if (path.includes('/videos') || path.includes('/watch')) {
    return 'video';
  }
  
  if (path.includes('/livestreams') || path.includes('/live')) {
    return 'livestream';
  }
  
  return 'default';
}

/**
 * Middleware pour configurer les en-têtes CSP
 */
export const cspMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Déterminer le type de contenu
    const contentType = getContentType(req.path);
    
    // Obtenir la configuration CSP du service de sécurité
    let cspConfig;
    try {
      cspConfig = await securityService.getCSPConfig(contentType);
    } catch (error) {
      // Utiliser la configuration locale en cas d'erreur
      console.warn('Impossible d\'obtenir la configuration CSP du service de sécurité, utilisation de la configuration locale');
      
      switch (contentType) {
        case 'video':
          cspConfig = videoCSP;
          break;
        case 'livestream':
          cspConfig = livestreamCSP;
          break;
        default:
          cspConfig = defaultCSP;
      }
    }
    
    // Convertir la configuration en chaîne de caractères
    const cspString = cspObjectToString(cspConfig.directives);
    
    // Définir l'en-tête CSP
    res.setHeader('Content-Security-Policy', cspString);
    
    next();
  } catch (error) {
    console.error('Erreur lors de la configuration des en-têtes CSP:', error);
    next(); // Continue même en cas d'erreur
  }
};

export default cspMiddleware;
