const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

/**
 * Middleware d'authentification
 * @param {Object} req Requête Express
 * @param {Object} res Réponse Express
 * @param {Function} next Fonction suivante
 */
function authMiddleware(req, res, next) {
  try {
    // Récupérer le token d'authentification
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Authentication required' });
    }
    
    const token = authHeader.split(' ')[1];
    
    // Vérifier le token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret-key');
    
    // Ajouter les informations de l'utilisateur à la requête
    req.user = decoded;
    
    next();
  } catch (error) {
    logger.error(`Authentication error: ${error.message}`, error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired' });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token' });
    }
    
    res.status(500).json({ message: 'Authentication error' });
  }
}

module.exports = authMiddleware;
