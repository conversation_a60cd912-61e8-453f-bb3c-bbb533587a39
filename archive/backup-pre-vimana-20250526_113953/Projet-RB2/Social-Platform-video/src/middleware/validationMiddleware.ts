import { z } from 'zod';
import securityService from '../services/securityService';

/**
 * Type de requête (pour le typage TypeScript)
 */
interface Request {
  body: any;
  query: any;
  params: any;
}

/**
 * Type de réponse (pour le typage TypeScript)
 */
interface Response {
  status: (code: number) => Response;
  json: (data: any) => void;
}

/**
 * Type de fonction next (pour le typage TypeScript)
 */
type NextFunction = () => void;

/**
 * Middleware de validation avec Zod
 * @param schema Schéma Zod pour la validation
 * @param source Source des données à valider (body, query, params)
 */
export const validate = (schema: z.ZodType<any>, source: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Valider les données avec le schéma Zod
      const result = schema.safeParse(req[source]);
      
      if (!result.success) {
        // Formater les erreurs de validation
        const errors = result.error.errors.map(error => ({
          path: error.path.join('.'),
          message: error.message
        }));
        
        return res.status(400).json({
          success: false,
          errors
        });
      }
      
      // Remplacer les données par les données validées et transformées
      req[source] = result.data;
      next();
    } catch (error) {
      console.error('Erreur de validation:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur interne lors de la validation des données'
      });
    }
  };
};

/**
 * Middleware de validation avec le service de sécurité
 * @param schemaName Nom du schéma de validation dans le microservice Security
 * @param source Source des données à valider (body, query, params)
 */
export const validateWithSecurityService = (schemaName: string, source: 'body' | 'query' | 'params' = 'body') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Valider les données avec le service de sécurité
      const result = await securityService.validateInput(req[source], schemaName);
      
      if (!result.valid) {
        return res.status(400).json({
          success: false,
          errors: result.errors || ['Données invalides']
        });
      }
      
      next();
    } catch (error) {
      console.error('Erreur de validation avec le service de sécurité:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur interne lors de la validation des données'
      });
    }
  };
};

/**
 * Middleware de sanitization des entrées
 * @param sources Sources des données à sanitizer (body, query, params)
 */
export const sanitize = (sources: ('body' | 'query' | 'params')[] = ['body']) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Sanitizer chaque source de données spécifiée
      for (const source of sources) {
        if (req[source] && typeof req[source] === 'object') {
          req[source] = await sanitizeObject(req[source]);
        }
      }
      
      next();
    } catch (error) {
      console.error('Erreur de sanitization:', error);
      next(); // Continue même en cas d'erreur de sanitization
    }
  };
};

/**
 * Sanitize récursivement un objet
 * @param obj Objet à sanitizer
 */
async function sanitizeObject(obj: any): Promise<any> {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return Promise.all(obj.map(item => sanitizeObject(item)));
  }
  
  if (typeof obj === 'object') {
    const result: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      result[key] = await sanitizeObject(value);
    }
    
    return result;
  }
  
  if (typeof obj === 'string') {
    return await securityService.sanitizeInput(obj, 'text');
  }
  
  return obj;
}

/**
 * Middleware de sanitization HTML
 * @param fields Champs à sanitizer en tant que HTML
 * @param source Source des données (body, query, params)
 */
export const sanitizeHtml = (fields: string[], source: 'body' | 'query' | 'params' = 'body') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req[source]) {
        return next();
      }
      
      // Sanitizer chaque champ spécifié
      for (const field of fields) {
        if (req[source][field] && typeof req[source][field] === 'string') {
          req[source][field] = await securityService.sanitizeInput(req[source][field], 'html');
        }
      }
      
      next();
    } catch (error) {
      console.error('Erreur de sanitization HTML:', error);
      next(); // Continue même en cas d'erreur de sanitization
    }
  };
};
