import { useState, useEffect } from 'react';
import { Calendar, Clock, Repeat, AlertCircle, Info } from 'lucide-react';
import { format, addDays, addWeeks, addMonths, parseISO } from 'date-fns';
import { RecurrencePattern, RecurrenceSettings } from '../../api/contentApi';

interface RecurrenceSettingsFormProps {
  initialSettings?: RecurrenceSettings;
  onChange: (settings: RecurrenceSettings | undefined) => void;
  className?: string;
}

export function RecurrenceSettingsForm({
  initialSettings,
  onChange,
  className = ''
}: RecurrenceSettingsFormProps) {
  const [enableRecurrence, setEnableRecurrence] = useState(
    initialSettings?.pattern !== 'none' && initialSettings?.pattern !== undefined
  );
  
  const [pattern, setPattern] = useState<RecurrencePattern>(
    initialSettings?.pattern || 'none'
  );
  
  const [interval, setInterval] = useState<number>(
    initialSettings?.interval || 1
  );
  
  const [daysOfWeek, setDaysOfWeek] = useState<number[]>(
    initialSettings?.daysOfWeek || []
  );
  
  const [dayOfMonth, setDayOfMonth] = useState<number>(
    initialSettings?.dayOfMonth || 1
  );
  
  const [endType, setEndType] = useState<'never' | 'after' | 'on'>(
    initialSettings?.endDate ? 'on' : 
    initialSettings?.occurrences ? 'after' : 'never'
  );
  
  const [occurrences, setOccurrences] = useState<number>(
    initialSettings?.occurrences || 10
  );
  
  const [endDate, setEndDate] = useState<string>(
    initialSettings?.endDate || format(addMonths(new Date(), 3), 'yyyy-MM-dd')
  );
  
  // Update parent component when settings change
  useEffect(() => {
    if (!enableRecurrence) {
      onChange(undefined);
      return;
    }
    
    const settings: RecurrenceSettings = {
      pattern,
      interval,
      ...(pattern === 'weekly' && { daysOfWeek }),
      ...(pattern === 'monthly' && { dayOfMonth }),
      ...(endType === 'after' && { occurrences }),
      ...(endType === 'on' && { endDate })
    };
    
    onChange(settings);
  }, [
    enableRecurrence, 
    pattern, 
    interval, 
    daysOfWeek, 
    dayOfMonth, 
    endType, 
    occurrences, 
    endDate, 
    onChange
  ]);
  
  // Get next occurrences preview
  const getNextOccurrences = (count: number = 3): string[] => {
    if (!enableRecurrence || pattern === 'none') {
      return [];
    }
    
    const startDate = new Date();
    const occurrences: Date[] = [];
    
    for (let i = 0; i < count; i++) {
      let nextDate: Date;
      
      if (i === 0) {
        nextDate = new Date(startDate);
      } else {
        const prevDate = occurrences[i - 1];
        
        switch (pattern) {
          case 'daily':
            nextDate = addDays(prevDate, interval);
            break;
          case 'weekly':
            // For simplicity in this preview, just add weeks
            nextDate = addWeeks(prevDate, interval);
            break;
          case 'monthly':
            // For simplicity in this preview, just add months
            nextDate = addMonths(prevDate, interval);
            break;
          default:
            nextDate = addDays(prevDate, 1);
        }
      }
      
      occurrences.push(nextDate);
    }
    
    return occurrences.map(date => format(date, 'PPP'));
  };
  
  // Handle toggle recurrence
  const handleToggleRecurrence = () => {
    setEnableRecurrence(!enableRecurrence);
  };
  
  // Get day name
  const getDayName = (dayIndex: number): string => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[dayIndex];
  };
  
  // Toggle day of week
  const toggleDayOfWeek = (dayIndex: number) => {
    if (daysOfWeek.includes(dayIndex)) {
      setDaysOfWeek(daysOfWeek.filter(day => day !== dayIndex));
    } else {
      setDaysOfWeek([...daysOfWeek, dayIndex].sort());
    }
  };
  
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Repeat size={18} className="text-blue-500 mr-2" />
          <h3 className="text-sm font-medium text-gray-900">Recurrence</h3>
        </div>
        
        <label className="inline-flex items-center cursor-pointer">
          <span className="mr-2 text-sm text-gray-500">
            {enableRecurrence ? 'Enabled' : 'Disabled'}
          </span>
          <div className="relative">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={enableRecurrence}
              onChange={handleToggleRecurrence}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </div>
        </label>
      </div>
      
      {enableRecurrence && (
        <div className="space-y-4 p-4 bg-gray-50 rounded-md border border-gray-200">
          {/* Recurrence Pattern */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Repeat
            </label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <select
                  value={pattern}
                  onChange={(e) => setPattern(e.target.value as RecurrencePattern)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
              
              <div className="flex items-center">
                <span className="mr-2">Every</span>
                <input
                  type="number"
                  min="1"
                  max="30"
                  value={interval}
                  onChange={(e) => setInterval(parseInt(e.target.value) || 1)}
                  className="w-16 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="ml-2">
                  {pattern === 'daily' && (interval === 1 ? 'day' : 'days')}
                  {pattern === 'weekly' && (interval === 1 ? 'week' : 'weeks')}
                  {pattern === 'monthly' && (interval === 1 ? 'month' : 'months')}
                </span>
              </div>
            </div>
          </div>
          
          {/* Weekly Options */}
          {pattern === 'weekly' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                On these days
              </label>
              <div className="flex flex-wrap gap-2">
                {[0, 1, 2, 3, 4, 5, 6].map((day) => (
                  <button
                    key={day}
                    type="button"
                    onClick={() => toggleDayOfWeek(day)}
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                      daysOfWeek.includes(day)
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {getDayName(day)}
                  </button>
                ))}
              </div>
              
              {daysOfWeek.length === 0 && (
                <div className="mt-2 text-xs text-red-500 flex items-center">
                  <AlertCircle size={12} className="mr-1" />
                  Please select at least one day of the week
                </div>
              )}
            </div>
          )}
          
          {/* Monthly Options */}
          {pattern === 'monthly' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                On day of month
              </label>
              <input
                type="number"
                min="1"
                max="31"
                value={dayOfMonth}
                onChange={(e) => setDayOfMonth(parseInt(e.target.value) || 1)}
                className="w-16 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <span className="ml-2 text-sm text-gray-500">
                of each month
              </span>
            </div>
          )}
          
          {/* End Recurrence */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End
            </label>
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  type="radio"
                  id="end-never"
                  name="end-type"
                  checked={endType === 'never'}
                  onChange={() => setEndType('never')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label htmlFor="end-never" className="ml-2 text-sm text-gray-700">
                  Never
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="radio"
                  id="end-after"
                  name="end-type"
                  checked={endType === 'after'}
                  onChange={() => setEndType('after')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label htmlFor="end-after" className="ml-2 text-sm text-gray-700">
                  After
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={occurrences}
                  onChange={(e) => setOccurrences(parseInt(e.target.value) || 1)}
                  disabled={endType !== 'after'}
                  className="ml-2 w-16 p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="ml-2 text-sm text-gray-700">
                  occurrences
                </span>
              </div>
              
              <div className="flex items-center">
                <input
                  type="radio"
                  id="end-on"
                  name="end-type"
                  checked={endType === 'on'}
                  onChange={() => setEndType('on')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label htmlFor="end-on" className="ml-2 text-sm text-gray-700">
                  On
                </label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  disabled={endType !== 'on'}
                  className="ml-2 p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
          
          {/* Preview */}
          <div className="mt-4 p-3 bg-blue-50 rounded-md">
            <div className="flex items-start">
              <Info size={16} className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <h4 className="text-xs font-medium text-blue-700 mb-1">
                  Recurrence Preview
                </h4>
                <div className="text-xs text-blue-600">
                  {getNextOccurrences().length > 0 ? (
                    <>
                      <p>This content will be published on:</p>
                      <ul className="mt-1 list-disc list-inside">
                        {getNextOccurrences().map((date, index) => (
                          <li key={index}>{date}</li>
                        ))}
                      </ul>
                      <p className="mt-1 italic">
                        {endType === 'never' 
                          ? 'And will continue indefinitely.' 
                          : endType === 'after'
                            ? `And will continue for ${occurrences} occurrences.`
                            : `And will continue until ${format(parseISO(endDate), 'PPP')}.`
                        }
                      </p>
                    </>
                  ) : (
                    <p>Please configure the recurrence settings.</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
