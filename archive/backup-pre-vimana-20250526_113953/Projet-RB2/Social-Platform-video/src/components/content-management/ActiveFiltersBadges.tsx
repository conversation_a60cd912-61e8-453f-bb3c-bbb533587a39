import { X, Video, FileText, Image, Radio, Clock, AlertCircle, Repeat, Calendar } from 'lucide-react';
import { CalendarFilters } from './CalendarFilters';
import { format } from 'date-fns';

interface ActiveFiltersBadgesProps {
  filters: CalendarFilters;
  onRemoveFilter: (filterType: string, filterValue: string) => void;
  className?: string;
}

export function ActiveFiltersBadges({
  filters,
  onRemoveFilter,
  className = ''
}: ActiveFiltersBadgesProps) {
  // Check if all filters of a certain type are selected
  const areAllSelected = (filterType: 'contentTypes' | 'status' | 'recurrence'): boolean => {
    return Object.values(filters[filterType]).every(value => value);
  };
  
  // Get active content type filters
  const getActiveContentTypeFilters = () => {
    if (areAllSelected('contentTypes')) {
      return [];
    }
    
    const activeFilters: { key: string; label: string; icon: JSX.Element }[] = [];
    
    if (!filters.contentTypes.video) {
      activeFilters.push({
        key: 'video',
        label: 'Vidéos exclues',
        icon: <Video size={14} />
      });
    }
    
    if (!filters.contentTypes.post) {
      activeFilters.push({
        key: 'post',
        label: 'Posts exclus',
        icon: <Image size={14} />
      });
    }
    
    if (!filters.contentTypes.livestream) {
      activeFilters.push({
        key: 'livestream',
        label: 'Livestreams exclus',
        icon: <Radio size={14} />
      });
    }
    
    if (!filters.contentTypes.blog) {
      activeFilters.push({
        key: 'blog',
        label: 'Blogs exclus',
        icon: <FileText size={14} />
      });
    }
    
    return activeFilters;
  };
  
  // Get active status filters
  const getActiveStatusFilters = () => {
    if (areAllSelected('status')) {
      return [];
    }
    
    const activeFilters: { key: string; label: string; icon: JSX.Element }[] = [];
    
    if (!filters.status.scheduled) {
      activeFilters.push({
        key: 'scheduled',
        label: 'Planifiés exclus',
        icon: <Clock size={14} />
      });
    }
    
    if (!filters.status.processing) {
      activeFilters.push({
        key: 'processing',
        label: 'En traitement exclus',
        icon: <Clock size={14} />
      });
    }
    
    if (!filters.status.error) {
      activeFilters.push({
        key: 'error',
        label: 'Erreurs exclues',
        icon: <AlertCircle size={14} />
      });
    }
    
    return activeFilters;
  };
  
  // Get active recurrence filters
  const getActiveRecurrenceFilters = () => {
    if (areAllSelected('recurrence')) {
      return [];
    }
    
    const activeFilters: { key: string; label: string; icon: JSX.Element }[] = [];
    
    if (!filters.recurrence.oneTime) {
      activeFilters.push({
        key: 'oneTime',
        label: 'Ponctuels exclus',
        icon: <Calendar size={14} />
      });
    }
    
    if (!filters.recurrence.recurring) {
      activeFilters.push({
        key: 'recurring',
        label: 'Récurrents exclus',
        icon: <Repeat size={14} />
      });
    }
    
    return activeFilters;
  };
  
  // Get active date range filters
  const getActiveDateRangeFilters = () => {
    const activeFilters: { key: string; label: string; icon: JSX.Element }[] = [];
    
    if (filters.dateRange.start) {
      activeFilters.push({
        key: 'start',
        label: `Après le ${format(new Date(filters.dateRange.start), 'dd/MM/yyyy')}`,
        icon: <Calendar size={14} />
      });
    }
    
    if (filters.dateRange.end) {
      activeFilters.push({
        key: 'end',
        label: `Avant le ${format(new Date(filters.dateRange.end), 'dd/MM/yyyy')}`,
        icon: <Calendar size={14} />
      });
    }
    
    return activeFilters;
  };
  
  // Combine all active filters
  const allActiveFilters = [
    ...getActiveContentTypeFilters(),
    ...getActiveStatusFilters(),
    ...getActiveRecurrenceFilters(),
    ...getActiveDateRangeFilters()
  ];
  
  if (allActiveFilters.length === 0) {
    return null;
  }
  
  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {allActiveFilters.map((filter) => (
        <div
          key={`${filter.key}-${filter.label}`}
          className="flex items-center px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs"
        >
          <span className="mr-1">{filter.icon}</span>
          <span>{filter.label}</span>
          <button
            type="button"
            onClick={() => {
              // Determine filter type based on key
              let filterType = 'contentTypes';
              if (['scheduled', 'processing', 'error'].includes(filter.key)) {
                filterType = 'status';
              } else if (['oneTime', 'recurring'].includes(filter.key)) {
                filterType = 'recurrence';
              } else if (['start', 'end'].includes(filter.key)) {
                filterType = 'dateRange';
              }
              
              onRemoveFilter(filterType, filter.key);
            }}
            className="ml-1 p-0.5 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-full"
            title="Supprimer ce filtre"
          >
            <X size={12} />
          </button>
        </div>
      ))}
    </div>
  );
}
