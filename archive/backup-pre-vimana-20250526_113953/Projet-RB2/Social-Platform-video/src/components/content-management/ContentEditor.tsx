import { useState, useEffect, useRef } from 'react';
import { 
  Edit, 
  Save, 
  X, 
  Tag, 
  Globe, 
  Lock, 
  Users, 
  Image as ImageIcon, 
  Loader2, 
  AlertCircle,
  Check,
  Plus,
  Trash2
} from 'lucide-react';

export interface ContentDetails {
  id: string;
  title: string;
  description: string;
  tags: string[];
  thumbnailUrl?: string;
  privacy: 'public' | 'private' | 'unlisted' | 'followers';
  createdAt: string;
  updatedAt?: string;
  type: 'video' | 'post' | 'livestream' | 'blog';
}

interface ContentEditorProps {
  content: ContentDetails;
  onSave: (updatedContent: ContentDetails) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  readOnly?: boolean;
  className?: string;
  suggestedTags?: string[];
}

export function ContentEditor({
  content,
  onSave,
  onCancel,
  isLoading = false,
  readOnly = false,
  className = '',
  suggestedTags = []
}: ContentEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState<ContentDetails>({ ...content });
  const [error, setError] = useState<string | null>(null);
  const [newTag, setNewTag] = useState('');
  const [showSuggestedTags, setShowSuggestedTags] = useState(false);
  const [filteredSuggestedTags, setFilteredSuggestedTags] = useState<string[]>([]);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const suggestedTagsRef = useRef<HTMLDivElement>(null);
  
  // Update edited content when props change
  useEffect(() => {
    if (!isEditing) {
      setEditedContent({ ...content });
    }
  }, [content, isEditing]);
  
  // Filter suggested tags based on input
  useEffect(() => {
    if (newTag.trim()) {
      const filtered = suggestedTags.filter(tag => 
        tag.toLowerCase().includes(newTag.toLowerCase()) && 
        !editedContent.tags.includes(tag)
      );
      setFilteredSuggestedTags(filtered);
    } else {
      setFilteredSuggestedTags([]);
    }
  }, [newTag, suggestedTags, editedContent.tags]);
  
  // Handle click outside of suggested tags
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestedTagsRef.current && 
        !suggestedTagsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestedTags(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Start editing
  const handleEdit = () => {
    if (readOnly) return;
    setIsEditing(true);
    setError(null);
  };
  
  // Cancel editing
  const handleCancel = () => {
    setIsEditing(false);
    setEditedContent({ ...content });
    setError(null);
    if (onCancel) {
      onCancel();
    }
  };
  
  // Save changes
  const handleSave = async () => {
    // Validate
    if (!editedContent.title.trim()) {
      setError('Title is required');
      return;
    }
    
    setError(null);
    
    try {
      await onSave(editedContent);
      setIsEditing(false);
    } catch (err) {
      setError('Failed to save changes. Please try again.');
      console.error('Error saving content:', err);
    }
  };
  
  // Update title
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditedContent({ ...editedContent, title: e.target.value });
  };
  
  // Update description
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedContent({ ...editedContent, description: e.target.value });
  };
  
  // Update privacy
  const handlePrivacyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setEditedContent({ 
      ...editedContent, 
      privacy: e.target.value as ContentDetails['privacy'] 
    });
  };
  
  // Add tag
  const handleAddTag = () => {
    if (!newTag.trim()) return;
    
    // Check if tag already exists
    if (editedContent.tags.includes(newTag.trim())) {
      setNewTag('');
      return;
    }
    
    setEditedContent({
      ...editedContent,
      tags: [...editedContent.tags, newTag.trim()]
    });
    
    setNewTag('');
  };
  
  // Add suggested tag
  const handleAddSuggestedTag = (tag: string) => {
    if (editedContent.tags.includes(tag)) return;
    
    setEditedContent({
      ...editedContent,
      tags: [...editedContent.tags, tag]
    });
    
    setNewTag('');
    setShowSuggestedTags(false);
  };
  
  // Remove tag
  const handleRemoveTag = (tagToRemove: string) => {
    setEditedContent({
      ...editedContent,
      tags: editedContent.tags.filter(tag => tag !== tagToRemove)
    });
  };
  
  // Handle thumbnail upload
  const handleThumbnailUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }
    
    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image must be less than 5MB');
      return;
    }
    
    // Create object URL for preview
    const objectUrl = URL.createObjectURL(file);
    
    setEditedContent({
      ...editedContent,
      thumbnailUrl: objectUrl
    });
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Get privacy icon
  const getPrivacyIcon = (privacy: ContentDetails['privacy']) => {
    switch (privacy) {
      case 'public':
        return <Globe size={16} className="text-green-500" />;
      case 'private':
        return <Lock size={16} className="text-red-500" />;
      case 'unlisted':
        return <Link size={16} className="text-blue-500" />;
      case 'followers':
        return <Users size={16} className="text-purple-500" />;
      default:
        return <Globe size={16} />;
    }
  };
  
  // Get privacy label
  const getPrivacyLabel = (privacy: ContentDetails['privacy']) => {
    switch (privacy) {
      case 'public':
        return 'Public';
      case 'private':
        return 'Private';
      case 'unlisted':
        return 'Unlisted';
      case 'followers':
        return 'Followers Only';
      default:
        return 'Unknown';
    }
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="font-semibold">Content Details</h3>
        
        {!isEditing && !readOnly && (
          <button
            type="button"
            onClick={handleEdit}
            className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100"
            aria-label="Edit content details"
          >
            <Edit size={16} />
          </button>
        )}
      </div>
      
      <div className="p-4">
        {isEditing ? (
          <div className="space-y-4">
            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title
              </label>
              <input
                type="text"
                value={editedContent.title}
                onChange={handleTitleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Enter title"
                disabled={isLoading}
              />
            </div>
            
            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={editedContent.description}
                onChange={handleDescriptionChange}
                rows={4}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                placeholder="Enter description"
                disabled={isLoading}
              />
            </div>
            
            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tags
              </label>
              
              <div className="mb-2 flex flex-wrap gap-2">
                {editedContent.tags.map(tag => (
                  <div 
                    key={tag}
                    className="flex items-center bg-blue-100 text-blue-800 rounded-full px-2 py-1 text-sm"
                  >
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                      disabled={isLoading}
                    >
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>
              
              <div className="relative">
                <div className="flex">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => {
                      setNewTag(e.target.value);
                      setShowSuggestedTags(true);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddTag();
                      }
                    }}
                    className="flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Add a tag"
                    disabled={isLoading}
                  />
                  
                  <button
                    type="button"
                    onClick={handleAddTag}
                    className="px-3 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                    disabled={!newTag.trim() || isLoading}
                  >
                    <Plus size={16} />
                  </button>
                </div>
                
                {/* Suggested tags */}
                {showSuggestedTags && filteredSuggestedTags.length > 0 && (
                  <div 
                    ref={suggestedTagsRef}
                    className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto"
                  >
                    {filteredSuggestedTags.map(tag => (
                      <button
                        key={tag}
                        type="button"
                        onClick={() => handleAddSuggestedTag(tag)}
                        className="w-full text-left px-3 py-2 hover:bg-gray-100 flex items-center"
                      >
                        <Tag size={14} className="mr-2 text-gray-500" />
                        {tag}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            {/* Privacy */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Privacy
              </label>
              <select
                value={editedContent.privacy}
                onChange={handlePrivacyChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                disabled={isLoading}
              >
                <option value="public">Public</option>
                <option value="private">Private</option>
                <option value="unlisted">Unlisted</option>
                <option value="followers">Followers Only</option>
              </select>
            </div>
            
            {/* Thumbnail */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Thumbnail
              </label>
              
              <div className="flex items-start space-x-4">
                <div className="w-32 h-24 bg-gray-100 rounded-md overflow-hidden flex-shrink-0">
                  {editedContent.thumbnailUrl ? (
                    <img
                      src={editedContent.thumbnailUrl}
                      alt="Thumbnail"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      <ImageIcon size={24} />
                    </div>
                  )}
                </div>
                
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleThumbnailUpload}
                    className="hidden"
                  />
                  
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
                    disabled={isLoading}
                  >
                    <ImageIcon size={16} className="mr-2" />
                    Change Thumbnail
                  </button>
                  
                  <p className="text-xs text-gray-500 mt-1">
                    Recommended size: 1280x720 pixels
                  </p>
                </div>
              </div>
            </div>
            
            {/* Error message */}
            {error && (
              <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
                <AlertCircle size={16} className="mr-2 flex-shrink-0" />
                {error}
              </div>
            )}
            
            {/* Action buttons */}
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={handleCancel}
                className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                disabled={isLoading}
              >
                <X size={16} className="mr-2" />
                Cancel
              </button>
              
              <button
                type="button"
                onClick={handleSave}
                className="px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                disabled={isLoading || !editedContent.title.trim()}
              >
                {isLoading ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save size={16} className="mr-2" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Title */}
            <div>
              <h4 className="text-lg font-medium text-gray-900">{content.title}</h4>
            </div>
            
            {/* Description */}
            <div>
              <p className="text-gray-700 whitespace-pre-wrap">
                {content.description || <span className="text-gray-400 italic">No description</span>}
              </p>
            </div>
            
            {/* Tags */}
            <div>
              <div className="flex items-center text-sm text-gray-500 mb-2">
                <Tag size={16} className="mr-1" />
                Tags
              </div>
              
              <div className="flex flex-wrap gap-2">
                {content.tags.length > 0 ? (
                  content.tags.map(tag => (
                    <div 
                      key={tag}
                      className="bg-blue-100 text-blue-800 rounded-full px-2 py-1 text-sm"
                    >
                      {tag}
                    </div>
                  ))
                ) : (
                  <span className="text-gray-400 italic">No tags</span>
                )}
              </div>
            </div>
            
            {/* Privacy */}
            <div>
              <div className="flex items-center text-sm text-gray-500 mb-2">
                {getPrivacyIcon(content.privacy)}
                <span className="ml-1">Privacy</span>
              </div>
              
              <div className="text-gray-700">
                {getPrivacyLabel(content.privacy)}
              </div>
            </div>
            
            {/* Thumbnail */}
            {content.thumbnailUrl && (
              <div>
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <ImageIcon size={16} className="mr-1" />
                  Thumbnail
                </div>
                
                <div className="w-32 h-24 bg-gray-100 rounded-md overflow-hidden">
                  <img
                    src={content.thumbnailUrl}
                    alt="Thumbnail"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            )}
            
            {/* Dates */}
            <div className="pt-2 border-t border-gray-200 text-xs text-gray-500">
              <div>
                Created: {new Date(content.createdAt).toLocaleString()}
              </div>
              
              {content.updatedAt && (
                <div>
                  Last updated: {new Date(content.updatedAt).toLocaleString()}
                </div>
              )}
            </div>
            
            {!readOnly && (
              <button
                type="button"
                onClick={handleEdit}
                className="mt-2 text-sm text-blue-600 hover:text-blue-800 flex items-center"
              >
                <Edit size={14} className="mr-1" />
                Edit Details
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
