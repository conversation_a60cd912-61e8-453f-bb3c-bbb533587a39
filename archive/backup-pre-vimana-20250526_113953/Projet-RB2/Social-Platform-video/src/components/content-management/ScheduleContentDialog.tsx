import { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  X,
  Check,
  Loader2,
  AlertCircle,
  Info,
  Repeat
} from 'lucide-react';
import { format, addHours, isBefore, startOfDay, endOfDay, addDays } from 'date-fns';
import { RecurrenceSettings } from '../../api/contentApi';
import { RecurrenceSettingsForm } from './RecurrenceSettingsForm';

export interface ContentItem {
  id: string;
  title: string;
  description?: string;
  thumbnailUrl?: string;
  createdAt: string;
  type: 'video' | 'post' | 'livestream' | 'blog';
}

interface ScheduleContentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSchedule: (itemIds: string[], scheduledDate: string, recurrence?: RecurrenceSettings) => Promise<void>;
  selectedItems: ContentItem[];
  initialDate?: string;
  title?: string;
  description?: string;
  className?: string;
}

export function ScheduleContentDialog({
  isOpen,
  onClose,
  onSchedule,
  selectedItems,
  initialDate,
  title = 'Schedule Content',
  description = 'Select when you want to publish the selected content. The content will be automatically published at the specified date and time.',
  className = ''
}: ScheduleContentDialogProps) {
  const [scheduledDate, setScheduledDate] = useState<Date | null>(null);
  const [scheduledTime, setScheduledTime] = useState<string>('');
  const [recurrenceSettings, setRecurrenceSettings] = useState<RecurrenceSettings | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Set default date and time on open
  useEffect(() => {
    if (isOpen) {
      if (initialDate) {
        // Use the provided initial date
        const date = new Date(initialDate);
        setScheduledDate(date);
        setScheduledTime(format(date, 'HH:mm'));
      } else {
        // Default to tomorrow at current hour
        const tomorrow = addDays(new Date(), 1);
        setScheduledDate(tomorrow);

        // Default to current hour, rounded to next hour
        const now = new Date();
        const nextHour = addHours(new Date(now.setMinutes(0, 0, 0)), 1);
        setScheduledTime(format(nextHour, 'HH:mm'));
      }

      // Reset states
      setError(null);
      setSuccess(null);
      setRecurrenceSettings(undefined);
    }
  }, [isOpen, initialDate]);

  // Handle date change
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const date = new Date(e.target.value);
    setScheduledDate(date);
  };

  // Handle time change
  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setScheduledTime(e.target.value);
  };

  // Handle schedule
  const handleSchedule = async () => {
    if (!scheduledDate || !scheduledTime) {
      setError('Please select both date and time');
      return;
    }

    // Combine date and time
    const [hours, minutes] = scheduledTime.split(':').map(Number);
    const combinedDate = new Date(scheduledDate);
    combinedDate.setHours(hours, minutes, 0, 0);

    // Validate date is in the future
    if (isBefore(combinedDate, new Date())) {
      setError('Scheduled date and time must be in the future');
      return;
    }

    // Validate recurrence settings if enabled
    if (recurrenceSettings) {
      if (recurrenceSettings.pattern === 'weekly' &&
          (!recurrenceSettings.daysOfWeek || recurrenceSettings.daysOfWeek.length === 0)) {
        setError('Please select at least one day of the week for weekly recurrence');
        return;
      }
    }

    setIsLoading(true);
    setError(null);

    try {
      await onSchedule(
        selectedItems.map(item => item.id),
        combinedDate.toISOString(),
        recurrenceSettings
      );

      const recurrenceText = recurrenceSettings
        ? ` with ${recurrenceSettings.pattern} recurrence`
        : '';

      setSuccess(`Successfully scheduled ${selectedItems.length} item(s) for ${format(combinedDate, 'PPP p')}${recurrenceText}`);

      // Close dialog after success
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      setError('Failed to schedule content. Please try again.');
      console.error('Error scheduling content:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Get min date (today)
  const getMinDate = () => {
    return format(new Date(), 'yyyy-MM-dd');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
          aria-hidden="true"
        ></div>

        {/* Modal */}
        <div className="inline-block overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto bg-blue-100 rounded-full sm:mx-0 sm:h-10 sm:w-10">
                <Calendar size={24} className="text-blue-600" />
              </div>

              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 className="text-lg font-medium leading-6 text-gray-900">
                  {title}
                </h3>

                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    {description}
                  </p>
                </div>

                <div className="mt-4">
                  {selectedItems.length > 0 ? (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Selected Content ({selectedItems.length})</h4>
                      <div className="max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2">
                        {selectedItems.map(item => (
                          <div key={item.id} className="py-1 border-b border-gray-100 last:border-b-0">
                            <div className="flex items-center">
                              {item.thumbnailUrl && (
                                <img
                                  src={item.thumbnailUrl}
                                  alt={item.title}
                                  className="w-8 h-8 object-cover rounded mr-2"
                                />
                              )}
                              <div className="truncate text-sm">{item.title}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : selectedItems.length === 0 && initialDate ? (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Content Details</h4>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Title
                          </label>
                          <input
                            type="text"
                            placeholder="Enter content title"
                            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Content Type
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="video">Video</option>
                            <option value="post">Post</option>
                            <option value="livestream">Livestream</option>
                            <option value="blog">Blog Article</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  ) : null}

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Date
                      </label>
                      <div className="relative">
                        <input
                          type="date"
                          value={scheduledDate ? format(scheduledDate, 'yyyy-MM-dd') : ''}
                          onChange={handleDateChange}
                          min={getMinDate()}
                          className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          disabled={isLoading}
                          required
                        />
                        <Calendar size={16} className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Time
                      </label>
                      <div className="relative">
                        <input
                          type="time"
                          value={scheduledTime}
                          onChange={handleTimeChange}
                          className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          disabled={isLoading}
                          required
                        />
                        <Clock size={16} className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  {/* Recurrence Settings */}
                  <RecurrenceSettingsForm
                    onChange={setRecurrenceSettings}
                    className="mt-4"
                  />

                  <div className="mt-4 p-3 bg-blue-50 rounded-md flex items-start">
                    <Info size={16} className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                    <div className="text-xs text-blue-700">
                      Scheduled content will be automatically published at the specified time. You can view and manage your scheduled content in the "Scheduled" tab.
                    </div>
                  </div>
                </div>

                {error && (
                  <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
                    <AlertCircle size={16} className="mr-2 flex-shrink-0" />
                    {error}
                  </div>
                )}

                {success && (
                  <div className="mt-4 p-3 bg-green-50 text-green-700 rounded-md text-sm flex items-center">
                    <Check size={16} className="mr-2 flex-shrink-0" />
                    {success}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="px-4 py-3 bg-gray-50 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={handleSchedule}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
              disabled={isLoading || !scheduledDate || !scheduledTime}
            >
              {isLoading ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Scheduling...
                </>
              ) : (
                <>
                  <Calendar size={16} className="mr-2" />
                  Schedule
                </>
              )}
            </button>

            <button
              type="button"
              onClick={onClose}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
              disabled={isLoading}
            >
              <X size={16} className="mr-2" />
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
