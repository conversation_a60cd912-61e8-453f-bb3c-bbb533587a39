import { useState } from 'react';
import { 
  Trash2, 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  <PERSON>, 
  <PERSON>, 
  Loader2, 
  AlertCircle,
  Archive
} from 'lucide-react';

interface DeleteConfirmationProps {
  title: string;
  message?: string;
  itemName?: string;
  itemType?: string;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  onArchiveInstead?: () => Promise<void>;
  showArchiveOption?: boolean;
  confirmButtonText?: string;
  cancelButtonText?: string;
  archiveButtonText?: string;
  dangerLevel?: 'low' | 'medium' | 'high';
  requireTypeName?: boolean;
}

export function DeleteConfirmation({
  title,
  message,
  itemName = 'this item',
  itemType = 'item',
  isOpen,
  onClose,
  onConfirm,
  onArchiveInstead,
  showArchiveOption = true,
  confirmButtonText = 'Delete',
  cancelButtonText = 'Cancel',
  archiveButtonText = 'Archive Instead',
  dangerLevel = 'medium',
  requireTypeName = false
}: DeleteConfirmationProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confirmText, setConfirmText] = useState('');
  
  // Handle confirm
  const handleConfirm = async () => {
    if (requireTypeName && confirmText !== itemType) {
      setError(`Please type "${itemType}" to confirm deletion`);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      await onConfirm();
      onClose();
    } catch (err) {
      setError('Failed to delete. Please try again.');
      console.error('Error deleting:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle archive
  const handleArchive = async () => {
    if (!onArchiveInstead) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await onArchiveInstead();
      onClose();
    } catch (err) {
      setError('Failed to archive. Please try again.');
      console.error('Error archiving:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Get danger level styles
  const getDangerStyles = () => {
    switch (dangerLevel) {
      case 'low':
        return 'bg-orange-50 border-orange-200';
      case 'high':
        return 'bg-red-50 border-red-200';
      case 'medium':
      default:
        return 'bg-red-50 border-red-200';
    }
  };
  
  // Get danger icon color
  const getDangerIconColor = () => {
    switch (dangerLevel) {
      case 'low':
        return 'text-orange-500';
      case 'high':
        return 'text-red-600';
      case 'medium':
      default:
        return 'text-red-500';
    }
  };
  
  // Get confirm button styles
  const getConfirmButtonStyles = () => {
    switch (dangerLevel) {
      case 'low':
        return 'bg-orange-500 hover:bg-orange-600';
      case 'high':
        return 'bg-red-600 hover:bg-red-700';
      case 'medium':
      default:
        return 'bg-red-500 hover:bg-red-600';
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" 
          onClick={onClose}
          aria-hidden="true"
        ></div>
        
        {/* Modal */}
        <div className="inline-block overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className={`p-6 ${getDangerStyles()}`}>
            <div className="sm:flex sm:items-start">
              <div className={`flex-shrink-0 flex items-center justify-center w-12 h-12 mx-auto rounded-full sm:mx-0 sm:h-10 sm:w-10 ${getDangerIconColor()} bg-white`}>
                <AlertTriangle size={24} />
              </div>
              
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 className="text-lg font-medium leading-6 text-gray-900">
                  {title}
                </h3>
                
                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    {message || `Are you sure you want to delete ${itemName}? This action cannot be undone.`}
                  </p>
                </div>
                
                {requireTypeName && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type "{itemType}" to confirm deletion
                    </label>
                    <input
                      type="text"
                      value={confirmText}
                      onChange={(e) => setConfirmText(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      placeholder={itemType}
                      disabled={isLoading}
                    />
                  </div>
                )}
                
                {error && (
                  <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
                    <AlertCircle size={16} className="mr-2 flex-shrink-0" />
                    {error}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="px-4 py-3 bg-gray-50 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={handleConfirm}
              className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm ${getConfirmButtonStyles()} disabled:opacity-50`}
              disabled={isLoading || (requireTypeName && confirmText !== itemType)}
            >
              {isLoading ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Trash2 size={16} className="mr-2" />
                  {confirmButtonText}
                </>
              )}
            </button>
            
            {showArchiveOption && onArchiveInstead && (
              <button
                type="button"
                onClick={handleArchive}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
                disabled={isLoading}
              >
                <Archive size={16} className="mr-2" />
                {archiveButtonText}
              </button>
            )}
            
            <button
              type="button"
              onClick={onClose}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
              disabled={isLoading}
            >
              <X size={16} className="mr-2" />
              {cancelButtonText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
