import { useState, useEffect } from 'react';
import {
  Calendar,
  RefreshCw,
  Search,
  X,
  CheckSquare,
  Square,
  Trash2,
  Filter,
  ChevronDown,
  ChevronUp,
  Loader2,
  AlertCircle,
  Clock,
  Eye,
  ThumbsUp,
  MessageSquare,
  Edit,
  MoreHorizontal,
  Play
} from 'lucide-react';
import { format, formatDistanceToNow, isBefore, parseISO } from 'date-fns';

export interface ScheduledContentItem {
  id: string;
  title: string;
  description?: string;
  thumbnailUrl?: string;
  createdAt: string;
  scheduledDate: string;
  type: 'video' | 'post' | 'livestream' | 'blog';
  status: 'scheduled' | 'processing' | 'error';
  errorMessage?: string;
  recurrence?: {
    pattern: 'daily' | 'weekly' | 'monthly';
    interval: number;
    daysOfWeek?: number[];
    dayOfMonth?: number;
    endDate?: string;
    occurrences?: number;
  };
  nextOccurrence?: string;
  occurrenceCount?: number;
  parentScheduleId?: string;
  stats?: {
    views?: number;
    likes?: number;
    comments?: number;
  };
}

interface ScheduledContentManagerProps {
  scheduledItems: ScheduledContentItem[];
  onPublishNow: (itemIds: string[]) => Promise<void>;
  onReschedule: (itemId: string) => void;
  onCancel: (itemIds: string[]) => Promise<void>;
  onRefresh: () => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

export function ScheduledContentManager({
  scheduledItems,
  onPublishNow,
  onReschedule,
  onCancel,
  onRefresh,
  isLoading = false,
  className = ''
}: ScheduledContentManagerProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredItems, setFilteredItems] = useState<ScheduledContentItem[]>([]);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    type: 'all' as 'all' | 'video' | 'post' | 'livestream' | 'blog',
    status: 'all' as 'all' | 'scheduled' | 'processing' | 'error',
    timeframe: 'all' as 'all' | 'today' | 'tomorrow' | 'week' | 'month',
    recurrence: 'all' as 'all' | 'recurring' | 'one-time'
  });
  const [activeItemMenu, setActiveItemMenu] = useState<string | null>(null);

  // Filter and sort items
  useEffect(() => {
    let filtered = [...scheduledItems];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(query) ||
        (item.description && item.description.toLowerCase().includes(query))
      );
    }

    // Apply type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(item => item.type === filters.type);
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(item => item.status === filters.status);
    }

    // Apply recurrence filter
    if (filters.recurrence !== 'all') {
      if (filters.recurrence === 'recurring') {
        filtered = filtered.filter(item => !!item.recurrence);
      } else if (filters.recurrence === 'one-time') {
        filtered = filtered.filter(item => !item.recurrence);
      }
    }

    // Apply timeframe filter
    if (filters.timeframe !== 'all') {
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const nextWeek = new Date(now);
      nextWeek.setDate(nextWeek.getDate() + 7);
      const nextMonth = new Date(now);
      nextMonth.setMonth(nextMonth.getMonth() + 1);

      filtered = filtered.filter(item => {
        const scheduledDate = parseISO(item.scheduledDate);

        switch (filters.timeframe) {
          case 'today':
            return (
              scheduledDate.getDate() === now.getDate() &&
              scheduledDate.getMonth() === now.getMonth() &&
              scheduledDate.getFullYear() === now.getFullYear()
            );
          case 'tomorrow':
            return (
              scheduledDate.getDate() === tomorrow.getDate() &&
              scheduledDate.getMonth() === tomorrow.getMonth() &&
              scheduledDate.getFullYear() === tomorrow.getFullYear()
            );
          case 'week':
            return scheduledDate <= nextWeek;
          case 'month':
            return scheduledDate <= nextMonth;
          default:
            return true;
        }
      });
    }

    // Sort by scheduled date (earliest first)
    filtered.sort((a, b) => {
      return new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime();
    });

    setFilteredItems(filtered);
  }, [scheduledItems, searchQuery, filters]);

  // Handle refresh
  const handleRefresh = async () => {
    setError(null);

    try {
      await onRefresh();
    } catch (err) {
      setError('Failed to refresh scheduled items');
      console.error('Error refreshing scheduled items:', err);
    }
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedItems.length === filteredItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems.map(item => item.id));
    }
  };

  // Handle select item
  const handleSelectItem = (itemId: string) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  // Handle publish now
  const handlePublishNow = async () => {
    if (selectedItems.length === 0) return;

    if (!window.confirm(`Are you sure you want to publish ${selectedItems.length} item(s) now?`)) {
      return;
    }

    setIsActionLoading(true);
    setError(null);

    try {
      await onPublishNow(selectedItems);
      setSelectedItems([]);
    } catch (err) {
      setError('Failed to publish selected items');
      console.error('Error publishing items:', err);
    } finally {
      setIsActionLoading(false);
    }
  };

  // Handle cancel
  const handleCancel = async () => {
    if (selectedItems.length === 0) return;

    if (!window.confirm(`Are you sure you want to cancel the scheduled publication of ${selectedItems.length} item(s)?`)) {
      return;
    }

    setIsActionLoading(true);
    setError(null);

    try {
      await onCancel(selectedItems);
      setSelectedItems([]);
    } catch (err) {
      setError('Failed to cancel scheduled items');
      console.error('Error canceling items:', err);
    } finally {
      setIsActionLoading(false);
    }
  };

  // Toggle filters
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Toggle item menu
  const toggleItemMenu = (itemId: string) => {
    if (activeItemMenu === itemId) {
      setActiveItemMenu(null);
    } else {
      setActiveItemMenu(itemId);
    }
  };

  // Format view count
  const formatCount = (count: number = 0) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Get content type label
  const getContentTypeLabel = (type: ScheduledContentItem['type']) => {
    switch (type) {
      case 'video':
        return 'Video';
      case 'post':
        return 'Post';
      case 'livestream':
        return 'Livestream';
      case 'blog':
        return 'Blog';
      default:
        return 'Content';
    }
  };

  // Get content type color
  const getContentTypeColor = (type: ScheduledContentItem['type']) => {
    switch (type) {
      case 'video':
        return 'bg-blue-100 text-blue-800';
      case 'post':
        return 'bg-green-100 text-green-800';
      case 'livestream':
        return 'bg-red-100 text-red-800';
      case 'blog':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status color
  const getStatusColor = (status: ScheduledContentItem['status']) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get time remaining
  const getTimeRemaining = (scheduledDate: string) => {
    const now = new Date();
    const scheduled = parseISO(scheduledDate);

    if (isBefore(scheduled, now)) {
      return 'Publishing soon';
    }

    return formatDistanceToNow(scheduled, { addSuffix: true });
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="font-semibold flex items-center">
          <Calendar size={18} className="mr-2 text-gray-500" />
          Scheduled Content
        </h3>

        <button
          type="button"
          onClick={handleRefresh}
          className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
          disabled={isLoading}
          title="Refresh"
        >
          <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
        </button>
      </div>

      <div className="p-4">
        {/* Search and filters */}
        <div className="mb-4 space-y-3">
          <div className="flex">
            <div className="relative flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search scheduled content..."
                className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isLoading || isActionLoading}
              />
              <Search size={16} className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400" />

              {searchQuery && (
                <button
                  type="button"
                  onClick={() => setSearchQuery('')}
                  className="absolute right-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  disabled={isLoading || isActionLoading}
                >
                  <X size={16} />
                </button>
              )}
            </div>

            <button
              type="button"
              onClick={toggleFilters}
              className="ml-2 px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center"
              disabled={isLoading || isActionLoading}
            >
              <Filter size={16} className="mr-1" />
              Filters
              {showFilters ? <ChevronUp size={16} className="ml-1" /> : <ChevronDown size={16} className="ml-1" />}
            </button>
          </div>

          {showFilters && (
            <div className="p-3 border border-gray-200 rounded-md bg-gray-50">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Content Type
                  </label>
                  <select
                    value={filters.type}
                    onChange={(e) => setFilters({ ...filters, type: e.target.value as any })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isLoading || isActionLoading}
                  >
                    <option value="all">All Types</option>
                    <option value="video">Videos</option>
                    <option value="post">Posts</option>
                    <option value="livestream">Livestreams</option>
                    <option value="blog">Blog Articles</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters({ ...filters, status: e.target.value as any })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isLoading || isActionLoading}
                  >
                    <option value="all">All Statuses</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="processing">Processing</option>
                    <option value="error">Error</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Timeframe
                  </label>
                  <select
                    value={filters.timeframe}
                    onChange={(e) => setFilters({ ...filters, timeframe: e.target.value as any })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isLoading || isActionLoading}
                  >
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="tomorrow">Tomorrow</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Recurrence
                  </label>
                  <select
                    value={filters.recurrence}
                    onChange={(e) => setFilters({ ...filters, recurrence: e.target.value as any })}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isLoading || isActionLoading}
                  >
                    <option value="all">All</option>
                    <option value="one-time">One-time</option>
                    <option value="recurring">Recurring</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Bulk actions */}
        {selectedItems.length > 0 && (
          <div className="mb-4 p-3 bg-blue-50 rounded-md flex items-center justify-between">
            <div className="text-sm text-blue-800">
              {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''} selected
            </div>

            <div className="flex space-x-2">
              <button
                type="button"
                onClick={handlePublishNow}
                className="px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center"
                disabled={isActionLoading}
              >
                {isActionLoading ? (
                  <Loader2 size={16} className="mr-1 animate-spin" />
                ) : (
                  <Play size={16} className="mr-1" />
                )}
                Publish Now
              </button>

              <button
                type="button"
                onClick={handleCancel}
                className="px-3 py-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 flex items-center"
                disabled={isActionLoading}
              >
                <X size={16} className="mr-1" />
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
            <AlertCircle size={16} className="mr-2 flex-shrink-0" />
            {error}
          </div>
        )}

        {/* Content list */}
        {isLoading ? (
          <div className="py-8 flex flex-col items-center justify-center text-gray-500">
            <Loader2 size={32} className="animate-spin mb-2" />
            <p>Loading scheduled content...</p>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="py-8 flex flex-col items-center justify-center text-gray-500">
            <Calendar size={32} className="mb-2" />
            <p className="mb-1">No scheduled content found</p>
            <p className="text-sm text-gray-400">
              {searchQuery ? 'Try a different search term' : 'Content you schedule will appear here'}
            </p>
          </div>
        ) : (
          <div className="space-y-1">
            {/* Header */}
            <div className="flex items-center p-2 bg-gray-50 rounded-md text-sm font-medium text-gray-700">
              <div className="w-8 flex-shrink-0">
                <button
                  type="button"
                  onClick={handleSelectAll}
                  className="text-gray-500 hover:text-gray-700"
                  disabled={isActionLoading}
                >
                  {selectedItems.length === filteredItems.length ? (
                    <CheckSquare size={18} className="text-blue-500" />
                  ) : (
                    <Square size={18} />
                  )}
                </button>
              </div>
              <div className="flex-1 min-w-0">Content</div>
              <div className="w-32 text-center">Status</div>
              <div className="w-40 text-right">Scheduled For</div>
              <div className="w-8"></div> {/* Actions column */}
            </div>

            {/* Items */}
            {filteredItems.map(item => (
              <div
                key={item.id}
                className={`flex items-center p-2 hover:bg-gray-50 rounded-md ${
                  selectedItems.includes(item.id) ? 'bg-blue-50' : ''
                }`}
              >
                <div className="w-8 flex-shrink-0">
                  <button
                    type="button"
                    onClick={() => handleSelectItem(item.id)}
                    className="text-gray-500 hover:text-gray-700"
                    disabled={isActionLoading}
                  >
                    {selectedItems.includes(item.id) ? (
                      <CheckSquare size={18} className="text-blue-500" />
                    ) : (
                      <Square size={18} />
                    )}
                  </button>
                </div>

                <div className="flex-1 min-w-0 flex items-center">
                  {item.thumbnailUrl && (
                    <div className="w-12 h-12 rounded overflow-hidden mr-3 flex-shrink-0">
                      <img
                        src={item.thumbnailUrl}
                        alt={item.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}

                  <div className="min-w-0">
                    <div className="font-medium text-gray-900 truncate">{item.title}</div>
                    <div className="flex items-center text-xs text-gray-500">
                      <span className={`px-1.5 py-0.5 rounded-full text-xs mr-2 ${getContentTypeColor(item.type)}`}>
                        {getContentTypeLabel(item.type)}
                      </span>
                      <span>
                        Created {formatDistanceToNow(parseISO(item.createdAt), { addSuffix: true })}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="w-32 text-center">
                  <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(item.status)}`}>
                    {item.status === 'scheduled' && 'Scheduled'}
                    {item.status === 'processing' && 'Processing'}
                    {item.status === 'error' && 'Failed'}
                  </span>

                  {item.status === 'error' && item.errorMessage && (
                    <div className="text-xs text-red-500 mt-1">
                      {item.errorMessage}
                    </div>
                  )}
                </div>

                <div className="w-40 text-right text-xs text-gray-500">
                  <div className="flex flex-col items-end">
                    <div className="font-medium">
                      {format(parseISO(item.scheduledDate), 'MMM d, yyyy')}
                    </div>
                    <div>
                      {format(parseISO(item.scheduledDate), 'h:mm a')}
                    </div>
                    <div className="text-blue-500">
                      {getTimeRemaining(item.scheduledDate)}
                    </div>

                    {item.recurrence && (
                      <div className="mt-1 flex items-center justify-end text-purple-600">
                        <Repeat size={12} className="mr-1" />
                        <span>
                          {item.recurrence.pattern === 'daily' &&
                            `Every ${item.recurrence.interval > 1 ? item.recurrence.interval : ''} day${item.recurrence.interval > 1 ? 's' : ''}`}
                          {item.recurrence.pattern === 'weekly' &&
                            `Every ${item.recurrence.interval > 1 ? item.recurrence.interval : ''} week${item.recurrence.interval > 1 ? 's' : ''}`}
                          {item.recurrence.pattern === 'monthly' &&
                            `Every ${item.recurrence.interval > 1 ? item.recurrence.interval : ''} month${item.recurrence.interval > 1 ? 's' : ''}`}
                        </span>
                      </div>
                    )}

                    {item.occurrenceCount !== undefined && item.occurrenceCount > 0 && (
                      <div className="text-gray-400 text-xs">
                        {item.occurrenceCount} published
                      </div>
                    )}
                  </div>
                </div>

                <div className="w-8 relative">
                  <button
                    type="button"
                    onClick={() => toggleItemMenu(item.id)}
                    className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
                    disabled={isActionLoading}
                  >
                    <MoreHorizontal size={16} />
                  </button>

                  {activeItemMenu === item.id && (
                    <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                      <div className="py-1">
                        <button
                          type="button"
                          onClick={() => {
                            onReschedule(item.id);
                            setActiveItemMenu(null);
                          }}
                          className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        >
                          <Edit size={16} className="mr-2 text-gray-500" />
                          Reschedule
                        </button>

                        <button
                          type="button"
                          onClick={async () => {
                            if (window.confirm('Are you sure you want to publish this content now?')) {
                              setIsActionLoading(true);
                              try {
                                await onPublishNow([item.id]);
                              } catch (err) {
                                console.error('Error publishing item:', err);
                              } finally {
                                setIsActionLoading(false);
                              }
                            }
                            setActiveItemMenu(null);
                          }}
                          className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        >
                          <Play size={16} className="mr-2 text-green-500" />
                          Publish Now
                        </button>

                        <button
                          type="button"
                          onClick={async () => {
                            if (window.confirm('Are you sure you want to cancel this scheduled publication?')) {
                              setIsActionLoading(true);
                              try {
                                await onCancel([item.id]);
                              } catch (err) {
                                console.error('Error canceling item:', err);
                              } finally {
                                setIsActionLoading(false);
                              }
                            }
                            setActiveItemMenu(null);
                          }}
                          className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex items-center"
                        >
                          <X size={16} className="mr-2" />
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
