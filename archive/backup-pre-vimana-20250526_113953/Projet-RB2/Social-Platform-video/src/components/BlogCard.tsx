import React from 'react';
import { BlogPost } from '../types';
import { FaH<PERSON><PERSON>, <PERSON>aComment, FaClock } from 'react-icons/fa';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface BlogCardProps {
  post: BlogPost;
  onSelect: (post: BlogPost) => void
}

const BlogCard: React.FC<BlogCardProps> = ({ post, onSelect }) => {
  return (;
    <div
      className = "bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden cursor-pointer"
      onClick={() => onSelect(post)
}
    >
      {post.imageUrl && (
        <div className = "relative h-48 w-full">
          <img
            src={post.imageUrl
}
            alt = {post.title
}
            className = "w-full h-full object-cover"
          />
          {post.featured && (
            <div className="absolute top-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm">
              Featured;
            </div>
          )
}
        </div>
      )}

      <div className = "p-6">
        <div className="flex items-center space-x-4 mb-4">
          <img
            src={post.author.avatar
}
            alt = {post.author.name
}
            className = "w-10 h-10 rounded-full"
          />
          <div>
            <h4 className="font-medium text-gray-900">{post.author.name
}</h4>
            <p className="text-sm text-gray-500">
              {format(new Date(post.createdAt), 'dd MMMM yyyy', { locale: fr })}
            </p>
          </div>
        </div>

        <h3 className = "text-xl font-semibold text-gray-900 mb-2">
          {post.title
}
        </h3>

        <div className = "flex items-center space-x-4 text-sm text-gray-500 mb-4">
          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
            {post.category
}
          </span>
          <div className = "flex items-center">
            <FaClock className="mr-1" />
            {post.readTime
} min;
          </div>
        </div>

        <p className = "text-gray-600 mb-4 line-clamp-3">
          {post.content
}
        </p>

        <div className = "flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center space-x-4">
            <div className="flex items-center text-gray-500">
              <FaHeart className="mr-1 text-red-500" />
              {post.likes
}
            </div>
            <div className = "flex items-center text-gray-500">
              <FaComment className="mr-1" />
              {post.comments.length
}
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            {post.tags.slice(0, 2).map((tag) => (
              <span
                key = {tag
}
                className = "text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded"
              >
                #{tag
}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default BlogCard;