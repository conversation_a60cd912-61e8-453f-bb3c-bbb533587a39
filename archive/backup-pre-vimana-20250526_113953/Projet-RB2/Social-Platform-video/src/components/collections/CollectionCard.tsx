import { Link } from 'react-router-dom';
import { MoreHorizontal, Lock, Users, Grid, ExternalLink } from 'lucide-react';
import { Collection } from '../../api/collectionsApi';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

interface CollectionCardProps {
  collection: Collection;
  isOwner?: boolean;
  onEdit?: (collection: Collection) => void;
  onDelete?: (collection: Collection) => void;
  onShare?: (collection: Collection) => void;
}

export function CollectionCard({
  collection,
  isOwner = false,
  onEdit,
  onDelete,
  onShare,
}: CollectionCardProps) {
  const {
    id,
    name,
    description,
    coverUrl,
    itemCount,
    isPublic,
    isCollaborative,
    createdAt,
    updatedAt,
  } = collection;
  
  // Format the date
  const formattedDate = formatDistanceToNow(new Date(updatedAt || createdAt), {
    addSuffix: true,
  });
  
  // Default cover image if none is provided
  const defaultCoverUrl = 'https://images.unsplash.com/photo-1508739773434-c26b3d09e071?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80';
  
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 hover:shadow-md transition-shadow">
      <Link to={`/collections/${id}`} className="block">
        <div className="relative h-40 bg-gray-100">
          <img
            src={coverUrl || defaultCoverUrl}
            alt={name}
            className="w-full h-full object-cover"
          />
          <div className="absolute top-2 right-2 flex space-x-1">
            {!isPublic && (
              <span className="bg-gray-800 bg-opacity-70 text-white p-1 rounded-full">
                <Lock size={14} />
              </span>
            )}
            {isCollaborative && (
              <span className="bg-gray-800 bg-opacity-70 text-white p-1 rounded-full">
                <Users size={14} />
              </span>
            )}
          </div>
        </div>
      </Link>
      
      <div className="p-4">
        <div className="flex justify-between items-start">
          <Link to={`/collections/${id}`} className="block">
            <h3 className="font-semibold text-lg line-clamp-1">{name}</h3>
          </Link>
          
          {isOwner && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal size={16} />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit?.(collection)}>
                  Edit collection
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onShare?.(collection)}>
                  Share collection
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete?.(collection)}
                  className="text-red-600"
                >
                  Delete collection
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        
        {description && (
          <p className="text-gray-600 text-sm mt-1 line-clamp-2">{description}</p>
        )}
        
        <div className="flex justify-between items-center mt-3 text-sm text-gray-500">
          <div className="flex items-center">
            <Grid size={14} className="mr-1" />
            <span>{itemCount} {itemCount === 1 ? 'item' : 'items'}</span>
          </div>
          <span>{formattedDate}</span>
        </div>
        
        {isPublic && (
          <div className="mt-3 flex justify-end">
            <Button
              variant="ghost"
              size="sm"
              className="text-xs text-gray-500 hover:text-gray-700"
              onClick={(e) => {
                e.preventDefault();
                onShare?.(collection);
              }}
            >
              <ExternalLink size={14} className="mr-1" />
              Share
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
