import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCollectionsStore } from '../../store/collections';
import { Collection, CollectionItem } from '../../api/collectionsApi';
import { Button } from '../ui/button';
import { Avatar } from '../ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Pagination } from '../ui/pagination';
import { PostCard } from '../post-card';
import { VideoCard } from '../video-card';
import { LivestreamCard } from '../livestream/LivestreamCard';
import { EditCollectionDialog } from './EditCollectionDialog';
import { ShareCollectionDialog } from './ShareCollectionDialog';
import { DeleteCollectionDialog } from './DeleteCollectionDialog';
import { AddToCollectionDialog } from './AddToCollectionDialog';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Edit, Share, Trash2, Lock, Users, Grid, ArrowLeft, GripVertical } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface CollectionDetailProps {
  collectionId: string;
  isOwner?: boolean;
}

// Sortable item wrapper
function SortableItem({ item, isOwner }: { item: CollectionItem; isOwner: boolean }) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: item.id });
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  
  return (
    <div ref={setNodeRef} style={style} className="relative">
      {isOwner && (
        <div
          className="absolute left-0 top-1/2 -translate-y-1/2 -ml-8 cursor-grab active:cursor-grabbing text-gray-400"
          {...attributes}
          {...listeners}
        >
          <GripVertical size={20} />
        </div>
      )}
      
      {item.contentType === 'video' && (
        <VideoCard video={item.content} className="mb-4" />
      )}
      
      {item.contentType === 'post' && (
        <PostCard post={item.content} className="mb-4" />
      )}
      
      {item.contentType === 'livestream' && (
        <LivestreamCard livestream={item.content} className="mb-4" />
      )}
    </div>
  );
}

export function CollectionDetail({ collectionId, isOwner = false }: CollectionDetailProps) {
  const navigate = useNavigate();
  const {
    fetchCollection,
    fetchCollectionItems,
    fetchCollaborators,
    reorderItems,
    currentCollection,
    collectionItems,
    collaborators,
    totalItems,
    currentPage,
    totalPages,
    isLoading,
    error,
  } = useCollectionsStore();
  
  const [activeTab, setActiveTab] = useState<'items' | 'collaborators'>('items');
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  
  // DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  
  // Fetch collection data
  useEffect(() => {
    fetchCollection(collectionId);
    fetchCollectionItems(collectionId);
    fetchCollaborators(collectionId);
  }, [collectionId, fetchCollection, fetchCollectionItems, fetchCollaborators]);
  
  // Handle page change
  const handlePageChange = (page: number) => {
    fetchCollectionItems(collectionId, page);
  };
  
  // Handle DnD end
  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    
    if (active.id !== over.id) {
      // Get the current order of items
      const oldIndex = collectionItems.findIndex((item) => item.id === active.id);
      const newIndex = collectionItems.findIndex((item) => item.id === over.id);
      
      // Reorder the items locally
      const newItems = [...collectionItems];
      const [removed] = newItems.splice(oldIndex, 1);
      newItems.splice(newIndex, 0, removed);
      
      // Get the new order of item IDs
      const newOrder = newItems.map((item) => item.id);
      
      // Update the order on the server
      reorderItems(collectionId, newOrder);
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  };
  
  if (isLoading && !currentCollection) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
      </div>
    );
  }
  
  if (error || !currentCollection) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Collection not found</h3>
        <p className="text-gray-500 mb-4">
          {error || 'The collection you are looking for does not exist or has been removed.'}
        </p>
        <Button onClick={() => navigate('/collections')}>
          <ArrowLeft size={16} className="mr-2" />
          Back to Collections
        </Button>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <Button
        variant="ghost"
        size="sm"
        className="mb-4"
        onClick={() => navigate('/collections')}
      >
        <ArrowLeft size={16} className="mr-2" />
        Back to Collections
      </Button>
      
      <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 mb-6">
        <div className="relative h-48 bg-gray-100">
          {currentCollection.coverUrl && (
            <img
              src={currentCollection.coverUrl}
              alt={currentCollection.name}
              className="w-full h-full object-cover"
            />
          )}
          
          <div className="absolute top-4 right-4 flex space-x-2">
            {isOwner && (
              <>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setIsEditDialogOpen(true)}
                >
                  <Edit size={16} className="mr-2" />
                  Edit
                </Button>
                
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setIsShareDialogOpen(true)}
                >
                  <Share size={16} className="mr-2" />
                  Share
                </Button>
                
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setIsDeleteDialogOpen(true)}
                  className="bg-red-500 hover:bg-red-600 text-white"
                >
                  <Trash2 size={16} className="mr-2" />
                  Delete
                </Button>
              </>
            )}
          </div>
        </div>
        
        <div className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold">{currentCollection.name}</h1>
              
              <div className="flex items-center mt-2 text-sm text-gray-500">
                <div className="flex items-center mr-4">
                  <Grid size={16} className="mr-1" />
                  <span>{currentCollection.itemCount} {currentCollection.itemCount === 1 ? 'item' : 'items'}</span>
                </div>
                
                <div className="flex items-center mr-4">
                  {!currentCollection.isPublic ? (
                    <>
                      <Lock size={16} className="mr-1" />
                      <span>Private</span>
                    </>
                  ) : (
                    <>
                      <Share size={16} className="mr-1" />
                      <span>Public</span>
                    </>
                  )}
                </div>
                
                {currentCollection.isCollaborative && (
                  <div className="flex items-center">
                    <Users size={16} className="mr-1" />
                    <span>Collaborative</span>
                  </div>
                )}
              </div>
              
              {currentCollection.description && (
                <p className="mt-4 text-gray-600">{currentCollection.description}</p>
              )}
              
              <p className="mt-2 text-sm text-gray-500">
                Updated {formatDate(currentCollection.updatedAt || currentCollection.createdAt)}
              </p>
            </div>
            
            {isOwner && (
              <Button onClick={() => setIsAddDialogOpen(true)}>
                Add Content
              </Button>
            )}
          </div>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'items' | 'collaborators')}>
        <TabsList className="mb-6">
          <TabsTrigger value="items">Items</TabsTrigger>
          {currentCollection.isCollaborative && (
            <TabsTrigger value="collaborators">Collaborators</TabsTrigger>
          )}
        </TabsList>
        
        <TabsContent value="items">
          {collectionItems.length === 0 ? (
            <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No items in this collection</h3>
              <p className="text-gray-500 mb-4">
                {isOwner
                  ? 'Start adding content to your collection.'
                  : 'This collection is empty.'}
              </p>
              {isOwner && (
                <Button onClick={() => setIsAddDialogOpen(true)}>
                  Add Content
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={collectionItems.map((item) => item.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="pl-8">
                    {collectionItems.map((item) => (
                      <SortableItem key={item.id} item={item} isOwner={isOwner} />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
              
              {totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              )}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="collaborators">
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold mb-4">Collaborators</h3>
            
            {collaborators.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">
                  {isOwner
                    ? 'No collaborators yet. Share your collection to add collaborators.'
                    : 'No collaborators yet.'}
                </p>
                {isOwner && (
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => setIsShareDialogOpen(true)}
                  >
                    <Share size={16} className="mr-2" />
                    Share Collection
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {collaborators.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 border rounded-md"
                  >
                    <div className="flex items-center space-x-3">
                      <Avatar src={user.avatar} alt={user.name} />
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-gray-500">@{user.username}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Dialogs */}
      {currentCollection && (
        <>
          <EditCollectionDialog
            open={isEditDialogOpen}
            onOpenChange={setIsEditDialogOpen}
            collection={currentCollection}
          />
          
          <ShareCollectionDialog
            open={isShareDialogOpen}
            onOpenChange={setIsShareDialogOpen}
            collection={currentCollection}
          />
          
          <DeleteCollectionDialog
            open={isDeleteDialogOpen}
            onOpenChange={setIsDeleteDialogOpen}
            collection={currentCollection}
          />
          
          <AddToCollectionDialog
            open={isAddDialogOpen}
            onOpenChange={setIsAddDialogOpen}
            collectionId={currentCollection.id}
          />
        </>
      )}
    </div>
  );
}
