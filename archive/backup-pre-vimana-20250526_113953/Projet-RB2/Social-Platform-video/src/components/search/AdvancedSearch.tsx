import { useState, useEffect } from 'react';
import { Search, Filter, X, Tag, MapPin, Calendar, Clock } from 'lucide-react';
import { CategoryFilter } from './CategoryFilter';
import { SortOptions } from './SortOptions';
import { useSearchStore } from '../../store/search';
import { SearchFilters } from '../../api/searchApi';

interface AdvancedSearchProps {
  initialFilters?: Partial<SearchFilters>;
  onSearch: (filters: SearchFilters) => void;
}

export function AdvancedSearch({ initialFilters = {}, onSearch }: AdvancedSearchProps) {
  const { categories, popularTags, fetchCategories, fetchPopularTags } = useSearchStore();
  
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState(initialFilters.query || '');
  const [selectedCategories, setSelectedCategories] = useState<string[]>(initialFilters.categories || []);
  const [selectedTags, setSelectedTags] = useState<string[]>(initialFilters.tags || []);
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'trending'>(
    initialFilters.sortBy || 'recent'
  );
  const [durationRange, setDurationRange] = useState<{ min: number; max: number }>({
    min: initialFilters.duration?.min || 0,
    max: initialFilters.duration?.max || 3600, // 1 hour default max
  });
  
  // Fetch categories and tags on mount
  useEffect(() => {
    fetchCategories();
    fetchPopularTags();
  }, []);
  
  const handleSearch = () => {
    const filters: SearchFilters = {
      query: searchQuery || undefined,
      categories: selectedCategories.length > 0 ? selectedCategories : undefined,
      tags: selectedTags.length > 0 ? selectedTags : undefined,
      sortBy,
      duration: {
        min: durationRange.min,
        max: durationRange.max,
      },
    };
    
    onSearch(filters);
  };
  
  const handleClearFilters = () => {
    setSearchQuery('');
    setSelectedCategories([]);
    setSelectedTags([]);
    setSortBy('recent');
    setDurationRange({ min: 0, max: 3600 });
  };
  
  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter((t) => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };
  
  // Format duration in MM:SS format
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      {/* Search Bar */}
      <div className="flex mb-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search videos, posts, users..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearch();
              }
            }}
          />
        </div>
        
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`ml-2 px-3 py-2 border rounded-md flex items-center ${
            showFilters
              ? 'bg-green-50 text-green-700 border-green-300'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`}
        >
          <Filter size={18} className="mr-1" />
          <span className="hidden sm:inline">Filters</span>
          {(selectedCategories.length > 0 || selectedTags.length > 0) && (
            <span className="ml-1 text-xs bg-green-100 text-green-800 rounded-full px-2 py-0.5">
              {selectedCategories.length + selectedTags.length}
            </span>
          )}
        </button>
        
        <button
          onClick={handleSearch}
          className="ml-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Search
        </button>
      </div>
      
      {/* Filters */}
      {showFilters && (
        <div className="mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Advanced Filters</h3>
            <button
              onClick={handleClearFilters}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Clear all filters
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Categories */}
            <div>
              <CategoryFilter
                categories={categories}
                selectedCategories={selectedCategories}
                onSelectCategories={setSelectedCategories}
              />
            </div>
            
            {/* Sort Options */}
            <div>
              <div className="mb-2 text-sm font-medium">Sort By</div>
              <SortOptions selectedSort={sortBy} onSelectSort={(sort) => setSortBy(sort as any)} />
            </div>
            
            {/* Duration Range */}
            <div>
              <div className="mb-2 text-sm font-medium flex items-center">
                <Clock size={16} className="mr-1" />
                Duration Range
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-500">{formatDuration(durationRange.min)}</span>
                <input
                  type="range"
                  min="0"
                  max="3600"
                  step="30"
                  value={durationRange.min}
                  onChange={(e) => {
                    const min = parseInt(e.target.value);
                    setDurationRange((prev) => ({
                      min,
                      max: Math.max(min, prev.max),
                    }));
                  }}
                  className="flex-1 h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
                />
                <span className="text-xs text-gray-500">{formatDuration(durationRange.max)}</span>
              </div>
              <div className="mt-2 flex items-center space-x-2">
                <span className="text-xs text-gray-500">Min</span>
                <input
                  type="range"
                  min="0"
                  max="7200"
                  step="30"
                  value={durationRange.max}
                  onChange={(e) => {
                    const max = parseInt(e.target.value);
                    setDurationRange((prev) => ({
                      min: Math.min(prev.min, max),
                      max,
                    }));
                  }}
                  className="flex-1 h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
                />
                <span className="text-xs text-gray-500">Max</span>
              </div>
            </div>
            
            {/* Popular Tags */}
            <div>
              <div className="mb-2 text-sm font-medium flex items-center">
                <Tag size={16} className="mr-1" />
                Popular Tags
              </div>
              <div className="flex flex-wrap gap-2">
                {popularTags.slice(0, 10).map((tag) => (
                  <button
                    key={tag.tag}
                    onClick={() => toggleTag(tag.tag)}
                    className={`flex items-center px-2 py-1 rounded-full text-xs ${
                      selectedTags.includes(tag.tag)
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                  >
                    {tag.tag}
                    {selectedTags.includes(tag.tag) ? (
                      <X size={12} className="ml-1" />
                    ) : (
                      <span className="ml-1 text-gray-500">({tag.count})</span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
          
          {/* Selected Filters Summary */}
          {(selectedCategories.length > 0 || selectedTags.length > 0) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="text-sm font-medium mb-2">Applied Filters:</div>
              <div className="flex flex-wrap gap-2">
                {selectedCategories.map((categoryId) => {
                  const category = categories.find((c) => c.id === categoryId);
                  return (
                    <div
                      key={categoryId}
                      className="flex items-center bg-green-100 text-green-800 rounded-full px-2 py-1 text-xs"
                    >
                      <span>Category: {category?.name || categoryId}</span>
                      <button
                        onClick={() => setSelectedCategories(selectedCategories.filter((id) => id !== categoryId))}
                        className="ml-1 text-green-600 hover:text-green-800"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  );
                })}
                
                {selectedTags.map((tag) => (
                  <div
                    key={tag}
                    className="flex items-center bg-green-100 text-green-800 rounded-full px-2 py-1 text-xs"
                  >
                    <span>Tag: {tag}</span>
                    <button
                      onClick={() => setSelectedTags(selectedTags.filter((t) => t !== tag))}
                      className="ml-1 text-green-600 hover:text-green-800"
                    >
                      <X size={12} />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
