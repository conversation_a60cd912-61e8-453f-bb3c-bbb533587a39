import { useState, useEffect } from 'react';
import {
  Tag,
  Plus,
  X,
  Check,
  ChevronDown,
  ChevronUp,
  Search,
  Loader2,
  Edit,
  Trash2
} from 'lucide-react';

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  color?: string;
  icon?: string;
  count?: number;
  isCustom?: boolean;
}

interface CategoryFilterProps {
  categories: Category[];
  selectedCategories: string[];
  onCategoryChange: (categoryIds: string[]) => void;
  onCreateCategory?: (category: Omit<Category, 'id'>) => Promise<Category>;
  onDeleteCategory?: (categoryId: string) => Promise<void>;
  allowMultiple?: boolean;
  allowCustomCategories?: boolean;
  showCounts?: boolean;
  maxHeight?: string;
  isLoading?: boolean;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
}

export function CategoryFilter({
  categories,
  selectedCategories,
  onCategoryChange,
  onCreateCategory,
  onDeleteCategory,
  allowMultiple = true,
  allowCustomCategories = false,
  showCounts = true,
  maxHeight = '300px',
  isLoading = false,
  isExpanded = true,
  onToggleExpand
}: CategoryFilterProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreatingCategory, setIsCreatingCategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryDescription, setNewCategoryDescription] = useState('');
  const [newCategoryColor, setNewCategoryColor] = useState('#10b981'); // Default green color
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAllCategories, setShowAllCategories] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [visibleCategories, setVisibleCategories] = useState<Category[]>([]);

  // Filter and sort categories
  useEffect(() => {
    // Filter categories based on search query
    const filtered = categories.filter(
      category =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (category.description && category.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    // Sort categories: non-custom first, then by count (descending), then alphabetically
    const sorted = [...filtered].sort((a, b) => {
      if ((a.isCustom && b.isCustom) || (!a.isCustom && !b.isCustom)) {
        // If counts are available, sort by count first
        if (a.count !== undefined && b.count !== undefined) {
          if (a.count !== b.count) {
            return b.count - a.count;
          }
        }
        // Then sort alphabetically
        return a.name.localeCompare(b.name);
      }
      // Non-custom categories first
      return a.isCustom ? 1 : -1;
    });

    setVisibleCategories(sorted);
  }, [categories, searchQuery]);

  // Get displayed categories (limited or all)
  const displayedCategories = showAllCategories
    ? visibleCategories
    : visibleCategories.slice(0, 10);

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    if (allowMultiple) {
      if (selectedCategories.includes(categoryId)) {
        onCategoryChange(selectedCategories.filter(id => id !== categoryId));
      } else {
        onCategoryChange([...selectedCategories, categoryId]);
      }
    } else {
      onCategoryChange([categoryId]);
    }
  };

  // Clear all selected categories
  const clearSelection = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    onCategoryChange([]);
  };

  // Select all visible categories
  const selectAllVisible = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    const visibleIds = visibleCategories.map(category => category.id);
    onCategoryChange(visibleIds);
  };

  // Handle category creation
  const handleCreateCategory = async () => {
    if (!newCategoryName.trim() || !onCreateCategory) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const newCategory = await onCreateCategory({
        name: newCategoryName.trim(),
        slug: newCategoryName.trim().toLowerCase().replace(/\s+/g, '-'),
        description: newCategoryDescription.trim() || undefined,
        color: newCategoryColor
      });

      // Select the newly created category
      handleCategorySelect(newCategory.id);

      // Reset form
      setNewCategoryName('');
      setNewCategoryDescription('');
      setIsCreatingCategory(false);
    } catch (err) {
      setError('Failed to create category. Please try again.');
      console.error('Error creating category:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle category deletion
  const handleDeleteCategory = async (categoryId: string, event: React.MouseEvent) => {
    event.stopPropagation();

    if (!onDeleteCategory) return;

    try {
      await onDeleteCategory(categoryId);

      // Remove from selected categories if it was selected
      if (selectedCategories.includes(categoryId)) {
        onCategoryChange(selectedCategories.filter(id => id !== categoryId));
      }
    } catch (err) {
      setError('Failed to delete category. Please try again.');
      console.error('Error deleting category:', err);
    }
  };

  // Get category color with fallback
  const getCategoryColor = (category: Category) => {
    return category.color || '#e5e7eb'; // Default gray color
  };

  // Toggle expanded state
  const toggleExpanded = () => {
    if (onToggleExpand) {
      onToggleExpand();
    }
  };

  return (
    <div className="w-full bg-white rounded-lg shadow-sm border border-gray-200">
      <div
        className="p-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
        onClick={toggleExpanded}
      >
        <h3 className="font-semibold flex items-center">
          <Tag size={18} className="mr-2 text-gray-500" />
          Categories
        </h3>
        <div className="flex items-center">
          {selectedCategories.length > 0 && (
            <span className="text-xs bg-green-100 text-green-800 rounded-full px-2 py-0.5 mr-2">
              {selectedCategories.length}
            </span>
          )}
          {isExpanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </div>
      </div>

      {isExpanded && (
        <div className="p-4">
          {/* Search input */}
          <div className="relative mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search categories..."
              className="w-full p-2 pl-9 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />

            {searchQuery && (
              <button
                type="button"
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-between mb-3">
            <button
              onClick={clearSelection}
              className="text-xs text-gray-500 hover:text-gray-700"
              disabled={selectedCategories.length === 0}
            >
              Clear all
            </button>

            <button
              onClick={selectAllVisible}
              className="text-xs text-green-500 hover:text-green-700"
              disabled={visibleCategories.length === 0}
            >
              Select all visible
            </button>
          </div>

          {/* Categories list */}
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 size={24} className="animate-spin text-green-500" />
            </div>
          ) : visibleCategories.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              {searchQuery ? (
                <p>No categories found matching "{searchQuery}"</p>
              ) : (
                <p>No categories available</p>
              )}
            </div>
          ) : (
            <div
              className="space-y-2 overflow-y-auto"
              style={{ maxHeight }}
            >
              {displayedCategories.map(category => (
                <div
                  key={category.id}
                  onClick={() => handleCategorySelect(category.id)}
                  className={`
                    flex items-center p-2 rounded-md cursor-pointer
                    ${selectedCategories.includes(category.id)
                      ? 'bg-green-50 border border-green-200'
                      : 'hover:bg-gray-50 border border-transparent'}
                  `}
                >
                  <div className="flex-shrink-0 mr-2">
                    <div
                      className={`w-5 h-5 rounded border flex items-center justify-center ${
                        selectedCategories.includes(category.id)
                          ? 'bg-green-500 border-green-500'
                          : 'border-gray-300'
                      }`}
                    >
                      {selectedCategories.includes(category.id) && (
                        <Check size={14} className="text-white" />
                      )}
                    </div>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center">
                      {category.color && (
                        <div
                          className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                          style={{ backgroundColor: getCategoryColor(category) }}
                        />
                      )}
                      <span className="font-medium text-gray-900 truncate">
                        {category.name}
                      </span>
                      {category.isCustom && (
                        <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded">
                          Custom
                        </span>
                      )}
                    </div>

                    {category.description && (
                      <p className="text-xs text-gray-500 truncate">
                        {category.description}
                      </p>
                    )}
                  </div>

                  {showCounts && category.count !== undefined && (
                    <span className="text-xs text-gray-500 bg-gray-100 rounded-full px-2 py-1 ml-2">
                      {category.count}
                    </span>
                  )}

                  {category.isCustom && onDeleteCategory && (
                    <button
                      type="button"
                      onClick={(e) => handleDeleteCategory(category.id, e)}
                      className="ml-2 text-gray-400 hover:text-red-500 p-1 rounded-full hover:bg-gray-100"
                      title="Delete category"
                    >
                      <Trash2 size={14} />
                    </button>
                  )}
                </div>
              ))}

              {/* Show more/less button */}
              {visibleCategories.length > 10 && (
                <button
                  type="button"
                  onClick={() => setShowAllCategories(!showAllCategories)}
                  className="w-full text-sm text-blue-600 hover:text-blue-800 py-2 flex items-center justify-center"
                >
                  {showAllCategories ? (
                    <>
                      <ChevronUp size={16} className="mr-1" />
                      Show less
                    </>
                  ) : (
                    <>
                      <ChevronDown size={16} className="mr-1" />
                      Show all ({visibleCategories.length})
                    </>
                  )}
                </button>
              )}
            </div>
          )}

          {/* Selected categories */}
          {selectedCategories.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="text-xs text-gray-500 mb-2">Selected categories:</div>
              <div className="flex flex-wrap gap-2">
                {selectedCategories.map((categoryId) => {
                  const category = categories.find((c) => c.id === categoryId);
                  return (
                    <div
                      key={categoryId}
                      className="flex items-center bg-green-100 text-green-800 rounded-full px-2 py-1 text-xs"
                    >
                      <span>{category?.name || categoryId}</span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCategorySelect(categoryId);
                        }}
                        className="ml-1 text-green-600 hover:text-green-800"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Create custom category */}
          {allowCustomCategories && onCreateCategory && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              {isCreatingCategory ? (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Create Custom Category</h4>

                  <div>
                    <input
                      type="text"
                      value={newCategoryName}
                      onChange={(e) => setNewCategoryName(e.target.value)}
                      placeholder="Category name"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      maxLength={50}
                    />
                    <p className="mt-1 text-xs text-gray-500 text-right">
                      {newCategoryName.length}/50
                    </p>
                  </div>

                  <div>
                    <input
                      type="text"
                      value={newCategoryDescription}
                      onChange={(e) => setNewCategoryDescription(e.target.value)}
                      placeholder="Description (optional)"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      maxLength={100}
                    />
                    <p className="mt-1 text-xs text-gray-500 text-right">
                      {newCategoryDescription.length}/100
                    </p>
                  </div>

                  <div>
                    <label className="block text-xs text-gray-500 mb-1">
                      Color
                    </label>
                    <input
                      type="color"
                      value={newCategoryColor}
                      onChange={(e) => setNewCategoryColor(e.target.value)}
                      className="w-full h-8 p-0 border border-gray-300 rounded-md"
                    />
                  </div>

                  {error && (
                    <p className="text-sm text-red-500">{error}</p>
                  )}

                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={() => setIsCreatingCategory(false)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={handleCreateCategory}
                      disabled={!newCategoryName.trim() || isSubmitting}
                      className="flex-1 px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 flex items-center justify-center"
                    >
                      {isSubmitting ? (
                        <Loader2 size={16} className="animate-spin" />
                      ) : (
                        'Create'
                      )}
                    </button>
                  </div>
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => setIsCreatingCategory(true)}
                  className="w-full px-3 py-2 border border-dashed border-gray-300 rounded-md text-gray-600 hover:bg-gray-50 flex items-center justify-center"
                >
                  <Plus size={16} className="mr-2" />
                  Create Custom Category
                </button>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
