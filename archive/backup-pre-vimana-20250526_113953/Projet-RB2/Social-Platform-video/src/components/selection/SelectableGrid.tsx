import React, { ReactNode } from 'react';
import { SelectableItem } from './SelectableItem';
import { useSelection } from './SelectionProvider';

// Props pour la grille d'éléments sélectionnables
interface SelectableGridProps<T> {
  // Éléments à afficher dans la grille
  items: T[];
  // Fonction pour extraire l'ID unique de chaque élément
  keyExtractor: (item: T) => string;
  // Fonction pour rendre chaque élément
  renderItem: (item: T, isSelected: boolean) => ReactNode;
  // Nombre de colonnes selon la taille de l'écran
  columns?: {
    sm?: number; // Mobile
    md?: number; // Tablette
    lg?: number; // Desktop
    xl?: number; // Large Desktop
  };
  // Espacement entre les éléments
  gap?: string;
  // Classe CSS pour la grille
  className?: string;
  // Classe CSS pour chaque élément de la grille
  itemClassName?: string;
  // Classe CSS pour les éléments sélectionnés
  selectedItemClassName?: string;
  // Callback lorsqu'un élément est sélectionné/désélectionné
  onSelectionChange?: (id: string, isSelected: boolean, item: T) => void;
  // Est-ce que la grille a une hauteur fixe
  fixedHeight?: boolean | string;
}

// Composant pour la grille d'éléments sélectionnables
export function SelectableGrid<T>({
  items,
  keyExtractor,
  renderItem,
  columns = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = '1rem',
  className = '',
  itemClassName = '',
  selectedItemClassName = 'ring-2 ring-blue-500 bg-blue-50',
  onSelectionChange,
  fixedHeight,
}: SelectableGridProps<T>) {
  // Utiliser le contexte de sélection
  const { isSelected } = useSelection<T>();

  // Calculer les classes CSS pour la grille responsive
  const gridTemplateColumns = `
    grid-template-columns: repeat(${columns.sm || 1}, 1fr);
    @media (min-width: 640px) {
      grid-template-columns: repeat(${columns.sm || 1}, 1fr);
    }
    @media (min-width: 768px) {
      grid-template-columns: repeat(${columns.md || 2}, 1fr);
    }
    @media (min-width: 1024px) {
      grid-template-columns: repeat(${columns.lg || 3}, 1fr);
    }
    @media (min-width: 1280px) {
      grid-template-columns: repeat(${columns.xl || 4}, 1fr);
    }
  `;

  // Style pour la grille avec les colonnes et les espaces
  const gridStyle = {
    display: 'grid',
    gap,
    height: fixedHeight === true ? '100%' : fixedHeight || 'auto',
    ...Object.fromEntries(
      gridTemplateColumns
        .split(';')
        .filter(Boolean)
        .map(rule => {
          const [property, value] = rule.split(':').map(s => s.trim());
          return [property, value];
        })
    ),
  };

  // Gérer le changement de sélection
  const handleSelectionChange = (isItemSelected: boolean, item: T) => {
    if (onSelectionChange) {
      onSelectionChange(keyExtractor(item), isItemSelected, item);
    }
  };

  return (
    <div className={className} style={gridStyle}>
      {items.map(item => {
        const id = keyExtractor(item);
        const selected = isSelected(id);
        
        return (
          <SelectableItem<T>
            key={id}
            id={id}
            item={item}
            className={itemClassName}
            selectedClassName={selectedItemClassName}
            onSelectionChange={(isItemSelected) => handleSelectionChange(isItemSelected, item)}
          >
            {renderItem(item, selected)}
          </SelectableItem>
        );
      })}
    </div>
  );
} 