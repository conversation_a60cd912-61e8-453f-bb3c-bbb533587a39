import React from 'react';
import { useSelection } from './SelectionProvider';
import { CheckSquare, Square } from 'lucide-react';

// Props pour le bouton de bascule de sélection
interface SelectionToggleProps {
  // Texte à afficher lorsque le mode de sélection est actif
  activeText?: string;
  // Texte à afficher lorsque le mode de sélection est inactif
  inactiveText?: string;
  // Classe CSS pour le bouton
  className?: string;
  // Couleur du bouton lorsqu'il est actif
  activeColor?: string;
  // Type de mode de sélection à activer (simple ou multiple)
  mode?: 'single' | 'multiple';
  // Icône personnalisée lorsque le mode est actif
  activeIcon?: React.ReactNode;
  // Icône personnalisée lorsque le mode est inactif
  inactiveIcon?: React.ReactNode;
  // Fonction exécutée lorsque l'état de sélection change
  onChange?: (isActive: boolean, mode: 'single' | 'multiple' | 'none') => void;
}

// Composant bouton pour activer/désactiver le mode de sélection
export function SelectionToggle({
  activeText = 'Terminé',
  inactiveText = 'Sélectionner',
  className = '',
  activeColor = 'bg-blue-500 text-white',
  mode = 'multiple',
  activeIcon,
  inactiveIcon,
  onChange,
}: SelectionToggleProps) {
  // Utiliser le contexte de sélection
  const { selectionMode, toggleSelectionMode, clearSelection } = useSelection();
  
  // Déterminer si le mode de sélection est actif
  const isActive = selectionMode !== 'none';
  
  // Déterminer le texte et l'icône à afficher
  const buttonText = isActive ? activeText : inactiveText;
  const icon = isActive 
    ? activeIcon || <CheckSquare size={18} /> 
    : inactiveIcon || <Square size={18} />;
  
  // Gérer le clic sur le bouton
  const handleClick = () => {
    // Si le mode est actif, le désactiver (passer à 'none')
    // Si le mode est inactif, l'activer avec le mode spécifié
    const newMode = isActive ? 'none' : mode;
    toggleSelectionMode(newMode);
    
    // Si le mode est désactivé, effacer la sélection
    if (isActive) {
      clearSelection();
    }
    
    // Appeler le callback si fourni
    if (onChange) {
      onChange(!isActive, newMode);
    }
  };
  
  return (
    <button
      onClick={handleClick}
      className={`
        inline-flex items-center px-4 py-2 rounded-md 
        transition-colors duration-200
        ${isActive ? activeColor : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}
        ${className}
      `}
    >
      <span className="mr-2">{icon}</span>
      <span>{buttonText}</span>
    </button>
  );
} 