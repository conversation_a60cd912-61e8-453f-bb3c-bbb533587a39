import React, { ReactNode, useCallback } from 'react';
import { useSelection } from './SelectionProvider';
import { CheckCircle2 } from 'lucide-react';

// Props pour l'élément sélectionnable
interface SelectableItemProps<T> {
  // ID unique de l'élément
  id: string;
  // Données complètes de l'élément
  item: T;
  // Contenu à afficher
  children: ReactNode;
  // Classes CSS conditionnelles
  className?: string;
  // Classes CSS à appliquer lorsque l'élément est sélectionné
  selectedClassName?: string;
  // Désactiver la sélection pour cet élément
  disabled?: boolean;
  // Function de rendu personnalisé pour l'indicateur de sélection
  renderSelectionIndicator?: (isSelected: boolean) => ReactNode;
  // Callback appelé lorsque l'élément est sélectionné/désélectionné
  onSelectionChange?: (isSelected: boolean, item: T) => void;
}

// Composant pour rendre un élément sélectionnable
export function SelectableItem<T>({
  id,
  item,
  children,
  className = '',
  selectedClassName = 'ring-2 ring-blue-500 bg-blue-50',
  disabled = false,
  renderSelectionIndicator,
  onSelectionChange,
}: SelectableItemProps<T>) {
  // Utiliser le contexte de sélection
  const { 
    toggleSelection, 
    isSelected, 
    selectionMode 
  } = useSelection<T>();

  // Vérifier si l'élément est actuellement sélectionné
  const selected = isSelected(id);
  
  // Gérer le clic sur l'élément
  const handleClick = useCallback(() => {
    if (disabled || selectionMode === 'none') return;
    
    // Basculer la sélection
    toggleSelection(id, item);
    
    // Appeler le callback si fourni
    if (onSelectionChange) {
      // L'état actuel est l'inverse de "selected" puisque nous venons de le basculer
      onSelectionChange(!selected, item);
    }
  }, [id, item, toggleSelection, disabled, selectionMode, selected, onSelectionChange]);

  // Indicateur de sélection par défaut
  const defaultSelectionIndicator = useCallback(() => {
    if (selectionMode === 'none' || !selected) return null;
    
    return (
      <div className="absolute top-2 right-2 z-10">
        <CheckCircle2 className="text-blue-500 bg-white rounded-full h-6 w-6" />
      </div>
    );
  }, [selected, selectionMode]);

  // Utiliser l'indicateur personnalisé ou celui par défaut
  const selectionIndicator = renderSelectionIndicator 
    ? renderSelectionIndicator(selected) 
    : defaultSelectionIndicator();

  // Classes conditionnelles basées sur l'état de sélection
  const combinedClassName = `
    relative 
    transition-all 
    duration-200 
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} 
    ${selectionMode !== 'none' ? 'hover:bg-gray-50' : ''} 
    ${selected ? selectedClassName : ''} 
    ${className}
  `;

  return (
    <div className={combinedClassName} onClick={handleClick}>
      {selectionIndicator}
      {children}
    </div>
  );
}

// Pour une utilisation plus simple dans les listes
export function SelectableList<T>({
  items,
  renderItem,
  keyExtractor,
  className = '',
  itemClassName = '',
  selectedItemClassName = '',
}: {
  items: T[];
  renderItem: (item: T) => ReactNode;
  keyExtractor: (item: T) => string;
  className?: string;
  itemClassName?: string;
  selectedItemClassName?: string;
}) {
  return (
    <div className={`space-y-2 ${className}`}>
      {items.map(item => {
        const id = keyExtractor(item);
        return (
          <SelectableItem<T>
            key={id}
            id={id}
            item={item}
            className={itemClassName}
            selectedClassName={selectedItemClassName}
          >
            {renderItem(item)}
          </SelectableItem>
        );
      })}
    </div>
  );
} 