import { useState, useEffect } from 'react';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { Calendar, ChevronDown, Download, RefreshCw } from 'lucide-react';
import { ViewsData, EngagementData, RevenueData } from '../../api/analyticsApi';

interface PerformanceChartProps {
  title: string;
  subtitle?: string;
  type: 'views' | 'engagement' | 'revenue';
  data: ViewsData[] | EngagementData[] | RevenueData[];
  timeRange: 'day' | 'week' | 'month' | 'year';
  granularity: 'hour' | 'day' | 'week' | 'month';
  onTimeRangeChange?: (timeRange: 'day' | 'week' | 'month' | 'year') => void;
  onGranularityChange?: (granularity: 'hour' | 'day' | 'week' | 'month') => void;
  onRefresh?: () => void;
  onExport?: (format: 'csv' | 'json' | 'pdf') => void;
  isLoading?: boolean;
  chartType?: 'line' | 'area' | 'bar';
  className?: string;
}

export function PerformanceChart({
  title,
  subtitle,
  type,
  data,
  timeRange,
  granularity,
  onTimeRangeChange,
  onGranularityChange,
  onRefresh,
  onExport,
  isLoading = false,
  chartType = 'line',
  className = '',
}: PerformanceChartProps) {
  const [showTimeRangeDropdown, setShowTimeRangeDropdown] = useState(false);
  const [showGranularityDropdown, setShowGranularityDropdown] = useState(false);
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  
  // Format date based on granularity
  const formatDate = (date: string) => {
    const dateObj = new Date(date);
    
    if (granularity === 'hour') {
      return dateObj.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (granularity === 'day') {
      return dateObj.toLocaleDateString([], { month: 'short', day: 'numeric' });
    } else if (granularity === 'week') {
      return `Week ${Math.ceil(dateObj.getDate() / 7)} ${dateObj.toLocaleDateString([], { month: 'short' })}`;
    } else {
      return dateObj.toLocaleDateString([], { month: 'short', year: 'numeric' });
    }
  };
  
  // Format number with K/M suffix
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };
  
  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };
  
  // Get chart colors based on type
  const getChartColors = () => {
    switch (type) {
      case 'views':
        return ['#10B981']; // Green
      case 'engagement':
        return ['#3B82F6', '#EC4899', '#F59E0B']; // Blue, Pink, Amber
      case 'revenue':
        return ['#8B5CF6']; // Purple
      default:
        return ['#10B981'];
    }
  };
  
  // Get chart data keys based on type
  const getDataKeys = () => {
    switch (type) {
      case 'views':
        return ['views'];
      case 'engagement':
        return ['likes', 'comments', 'shares'];
      case 'revenue':
        return ['amount'];
      default:
        return ['views'];
    }
  };
  
  // Render the appropriate chart based on chartType
  const renderChart = () => {
    const colors = getChartColors();
    const dataKeys = getDataKeys();
    
    if (chartType === 'line') {
      return (
        <LineChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis 
            dataKey="date" 
            tickFormatter={formatDate} 
            tick={{ fontSize: 12 }} 
            axisLine={false}
            tickLine={false}
          />
          <YAxis 
            tickFormatter={(value) => type === 'revenue' ? formatCurrency(value).split('.')[0] : formatNumber(value)} 
            tick={{ fontSize: 12 }} 
            axisLine={false}
            tickLine={false}
          />
          <Tooltip 
            formatter={(value: number, name: string) => {
              if (type === 'revenue') {
                return [formatCurrency(value), name];
              }
              return [value.toLocaleString(), name.charAt(0).toUpperCase() + name.slice(1)];
            }}
            labelFormatter={(label) => formatDate(label)}
          />
          <Legend />
          {dataKeys.map((key, index) => (
            <Line 
              key={key} 
              type="monotone" 
              dataKey={key} 
              stroke={colors[index % colors.length]} 
              strokeWidth={2}
              dot={{ r: 3 }}
              activeDot={{ r: 5 }}
            />
          ))}
        </LineChart>
      );
    } else if (chartType === 'area') {
      return (
        <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis 
            dataKey="date" 
            tickFormatter={formatDate} 
            tick={{ fontSize: 12 }} 
            axisLine={false}
            tickLine={false}
          />
          <YAxis 
            tickFormatter={(value) => type === 'revenue' ? formatCurrency(value).split('.')[0] : formatNumber(value)} 
            tick={{ fontSize: 12 }} 
            axisLine={false}
            tickLine={false}
          />
          <Tooltip 
            formatter={(value: number, name: string) => {
              if (type === 'revenue') {
                return [formatCurrency(value), name];
              }
              return [value.toLocaleString(), name.charAt(0).toUpperCase() + name.slice(1)];
            }}
            labelFormatter={(label) => formatDate(label)}
          />
          <Legend />
          {dataKeys.map((key, index) => (
            <Area 
              key={key} 
              type="monotone" 
              dataKey={key} 
              stroke={colors[index % colors.length]} 
              fill={colors[index % colors.length]} 
              fillOpacity={0.2}
            />
          ))}
        </AreaChart>
      );
    } else {
      return (
        <BarChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis 
            dataKey="date" 
            tickFormatter={formatDate} 
            tick={{ fontSize: 12 }} 
            axisLine={false}
            tickLine={false}
          />
          <YAxis 
            tickFormatter={(value) => type === 'revenue' ? formatCurrency(value).split('.')[0] : formatNumber(value)} 
            tick={{ fontSize: 12 }} 
            axisLine={false}
            tickLine={false}
          />
          <Tooltip 
            formatter={(value: number, name: string) => {
              if (type === 'revenue') {
                return [formatCurrency(value), name];
              }
              return [value.toLocaleString(), name.charAt(0).toUpperCase() + name.slice(1)];
            }}
            labelFormatter={(label) => formatDate(label)}
          />
          <Legend />
          {dataKeys.map((key, index) => (
            <Bar 
              key={key} 
              dataKey={key} 
              fill={colors[index % colors.length]} 
              radius={[4, 4, 0, 0]}
            />
          ))}
        </BarChart>
      );
    }
  };
  
  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowTimeRangeDropdown(false);
      setShowGranularityDropdown(false);
      setShowExportDropdown(false);
    };
    
    document.addEventListener('click', handleClickOutside);
    
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">{title}</h3>
            {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
          </div>
          
          <div className="flex space-x-2">
            {/* Time Range Dropdown */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowTimeRangeDropdown(!showTimeRangeDropdown);
                  setShowGranularityDropdown(false);
                  setShowExportDropdown(false);
                }}
                className="px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none flex items-center"
              >
                <Calendar size={14} className="mr-1" />
                {timeRange === 'day' ? 'Today' : 
                 timeRange === 'week' ? 'This Week' : 
                 timeRange === 'month' ? 'This Month' : 'This Year'}
                <ChevronDown size={14} className="ml-1" />
              </button>
              
              {showTimeRangeDropdown && (
                <div className="absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  <div className="py-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onTimeRangeChange) onTimeRangeChange('day');
                        setShowTimeRangeDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm ${
                        timeRange === 'day' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      Today
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onTimeRangeChange) onTimeRangeChange('week');
                        setShowTimeRangeDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm ${
                        timeRange === 'week' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      This Week
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onTimeRangeChange) onTimeRangeChange('month');
                        setShowTimeRangeDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm ${
                        timeRange === 'month' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      This Month
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onTimeRangeChange) onTimeRangeChange('year');
                        setShowTimeRangeDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm ${
                        timeRange === 'year' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      This Year
                    </button>
                  </div>
                </div>
              )}
            </div>
            
            {/* Granularity Dropdown */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowGranularityDropdown(!showGranularityDropdown);
                  setShowTimeRangeDropdown(false);
                  setShowExportDropdown(false);
                }}
                className="px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none flex items-center"
              >
                {granularity === 'hour' ? 'Hourly' : 
                 granularity === 'day' ? 'Daily' : 
                 granularity === 'week' ? 'Weekly' : 'Monthly'}
                <ChevronDown size={14} className="ml-1" />
              </button>
              
              {showGranularityDropdown && (
                <div className="absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  <div className="py-1">
                    {timeRange !== 'year' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onGranularityChange) onGranularityChange('hour');
                          setShowGranularityDropdown(false);
                        }}
                        className={`w-full text-left px-4 py-2 text-sm ${
                          granularity === 'hour' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        Hourly
                      </button>
                    )}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onGranularityChange) onGranularityChange('day');
                        setShowGranularityDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm ${
                        granularity === 'day' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      Daily
                    </button>
                    {timeRange !== 'day' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onGranularityChange) onGranularityChange('week');
                          setShowGranularityDropdown(false);
                        }}
                        className={`w-full text-left px-4 py-2 text-sm ${
                          granularity === 'week' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        Weekly
                      </button>
                    )}
                    {timeRange === 'year' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onGranularityChange) onGranularityChange('month');
                          setShowGranularityDropdown(false);
                        }}
                        className={`w-full text-left px-4 py-2 text-sm ${
                          granularity === 'month' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        Monthly
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Refresh Button */}
            {onRefresh && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  if (onRefresh) onRefresh();
                }}
                className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
                title="Refresh data"
              >
                <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
              </button>
            )}
            
            {/* Export Dropdown */}
            {onExport && (
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowExportDropdown(!showExportDropdown);
                    setShowTimeRangeDropdown(false);
                    setShowGranularityDropdown(false);
                  }}
                  className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
                  title="Export data"
                >
                  <Download size={16} />
                </button>
                
                {showExportDropdown && (
                  <div className="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    <div className="py-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onExport) onExport('csv');
                          setShowExportDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Export CSV
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onExport) onExport('json');
                          setShowExportDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Export JSON
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onExport) onExport('pdf');
                          setShowExportDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Export PDF
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="p-4">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500"></div>
          </div>
        ) : data.length === 0 ? (
          <div className="flex justify-center items-center h-64 text-gray-500">
            <p>No data available for the selected time range.</p>
          </div>
        ) : (
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              {renderChart()}
            </ResponsiveContainer>
          </div>
        )}
      </div>
    </div>
  );
}
