import { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  Tooltip, 
  Legend,
  BarChart,
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid
} from 'recharts';
import { Download, RefreshCw, Users } from 'lucide-react';
import { AudienceDemographics } from '../../api/analyticsApi';

interface AudienceInsightsProps {
  data: AudienceDemographics | null;
  onRefresh?: () => void;
  onExport?: (format: 'csv' | 'json' | 'pdf') => void;
  isLoading?: boolean;
  className?: string;
}

export function AudienceInsights({
  data,
  onRefresh,
  onExport,
  isLoading = false,
  className = '',
}: AudienceInsightsProps) {
  const [activeTab, setActiveTab] = useState<'age' | 'gender' | 'location' | 'interests'>('age');
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  
  // Colors for charts
  const COLORS = ['#10B981', '#3B82F6', '#EC4899', '#F59E0B', '#8B5CF6', '#EF4444', '#14B8A6', '#F97316'];
  
  // Get chart data based on active tab
  const getChartData = () => {
    if (!data) return [];
    
    switch (activeTab) {
      case 'age':
        return data.ageGroups;
      case 'gender':
        return data.genders;
      case 'location':
        return data.locations;
      case 'interests':
        return data.interests;
      default:
        return [];
    }
  };
  
  // Get chart label based on active tab
  const getChartLabel = () => {
    switch (activeTab) {
      case 'age':
        return 'group';
      case 'gender':
        return 'gender';
      case 'location':
        return 'country';
      case 'interests':
        return 'interest';
      default:
        return '';
    }
  };
  
  // Render pie chart
  const renderPieChart = () => {
    const chartData = getChartData();
    const dataKey = getChartLabel();
    
    return (
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="percentage"
            nameKey={dataKey}
            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [`${(value * 100).toFixed(1)}%`, 'Percentage']}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    );
  };
  
  // Render bar chart (for interests and locations)
  const renderBarChart = () => {
    const chartData = getChartData();
    const dataKey = getChartLabel();
    
    // Sort data by percentage (descending)
    const sortedData = [...chartData].sort((a, b) => b.percentage - a.percentage);
    
    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={sortedData}
          layout="vertical"
          margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
          <XAxis
            type="number"
            tickFormatter={(value) => `${(value * 100).toFixed(0)}%`}
            domain={[0, Math.max(...sortedData.map(item => item.percentage)) * 1.1]}
          />
          <YAxis
            type="category"
            dataKey={dataKey}
            tick={{ fontSize: 12 }}
            width={80}
          />
          <Tooltip
            formatter={(value: number) => [`${(value * 100).toFixed(1)}%`, 'Percentage']}
          />
          <Bar dataKey="percentage" fill="#10B981" radius={[0, 4, 4, 0]} />
        </BarChart>
      </ResponsiveContainer>
    );
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Users size={18} className="text-green-500 mr-2" />
            <h3 className="text-lg font-semibold">Audience Insights</h3>
          </div>
          
          <div className="flex space-x-2">
            {/* Refresh Button */}
            {onRefresh && (
              <button
                onClick={onRefresh}
                className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
                title="Refresh data"
              >
                <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
              </button>
            )}
            
            {/* Export Dropdown */}
            {onExport && (
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowExportDropdown(!showExportDropdown);
                  }}
                  className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
                  title="Export data"
                >
                  <Download size={16} />
                </button>
                
                {showExportDropdown && (
                  <div className="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    <div className="py-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onExport) onExport('csv');
                          setShowExportDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Export CSV
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onExport) onExport('json');
                          setShowExportDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Export JSON
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onExport) onExport('pdf');
                          setShowExportDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Export PDF
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('age')}
          className={`flex-1 py-2 text-sm font-medium ${
            activeTab === 'age'
              ? 'text-green-500 border-b-2 border-green-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Age
        </button>
        <button
          onClick={() => setActiveTab('gender')}
          className={`flex-1 py-2 text-sm font-medium ${
            activeTab === 'gender'
              ? 'text-green-500 border-b-2 border-green-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Gender
        </button>
        <button
          onClick={() => setActiveTab('location')}
          className={`flex-1 py-2 text-sm font-medium ${
            activeTab === 'location'
              ? 'text-green-500 border-b-2 border-green-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Location
        </button>
        <button
          onClick={() => setActiveTab('interests')}
          className={`flex-1 py-2 text-sm font-medium ${
            activeTab === 'interests'
              ? 'text-green-500 border-b-2 border-green-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Interests
        </button>
      </div>
      
      <div className="p-4">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500"></div>
          </div>
        ) : !data ? (
          <div className="flex justify-center items-center h-64 text-gray-500">
            <p>No audience data available.</p>
          </div>
        ) : (
          <div className="h-64">
            {activeTab === 'age' || activeTab === 'gender'
              ? renderPieChart()
              : renderBarChart()}
          </div>
        )}
      </div>
    </div>
  );
}
