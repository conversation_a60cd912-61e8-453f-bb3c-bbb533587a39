import { useState } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown, Download, Eye, Heart, MessageCircle, RefreshCw, Share2, Video, FileText, Clock } from 'lucide-react';
import { ContentPerformanceItem } from '../../api/analyticsApi';

interface ContentPerformanceProps {
  data: ContentPerformanceItem[];
  onRefresh?: () => void;
  onExport?: (format: 'csv' | 'json' | 'pdf') => void;
  onContentTypeChange?: (contentType: 'all' | 'video' | 'post' | 'story') => void;
  onSortByChange?: (sortBy: 'views' | 'likes' | 'comments' | 'shares' | 'completionRate') => void;
  contentTypeFilter: 'all' | 'video' | 'post' | 'story';
  sortBy: 'views' | 'likes' | 'comments' | 'shares' | 'completionRate';
  isLoading?: boolean;
  onContentSelect?: (contentId: string, contentType: 'video' | 'post' | 'story') => void;
  className?: string;
}

export function ContentPerformance({
  data,
  onRefresh,
  onExport,
  onContentTypeChange,
  onSortByChange,
  contentTypeFilter,
  sortBy,
  isLoading = false,
  onContentSelect,
  className = '',
}: ContentPerformanceProps) {
  const [showContentTypeDropdown, setShowContentTypeDropdown] = useState(false);
  const [showSortByDropdown, setShowSortByDropdown] = useState(false);
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };
  
  // Format number with K/M suffix
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };
  
  // Format time in MM:SS format
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Get content type icon
  const getContentTypeIcon = (type: 'video' | 'post' | 'story') => {
    switch (type) {
      case 'video':
        return <Video size={16} className="text-blue-500" />;
      case 'post':
        return <FileText size={16} className="text-green-500" />;
      case 'story':
        return <Clock size={16} className="text-purple-500" />;
      default:
        return null;
    }
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Content Performance</h3>
          
          <div className="flex space-x-2">
            {/* Content Type Filter */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowContentTypeDropdown(!showContentTypeDropdown);
                  setShowSortByDropdown(false);
                  setShowExportDropdown(false);
                }}
                className="px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none flex items-center"
              >
                {contentTypeFilter === 'all' ? 'All Content' : 
                 contentTypeFilter === 'video' ? 'Videos' : 
                 contentTypeFilter === 'post' ? 'Posts' : 'Stories'}
                <ChevronDown size={14} className="ml-1" />
              </button>
              
              {showContentTypeDropdown && (
                <div className="absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  <div className="py-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onContentTypeChange) onContentTypeChange('all');
                        setShowContentTypeDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        contentTypeFilter === 'all' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      All Content
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onContentTypeChange) onContentTypeChange('video');
                        setShowContentTypeDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        contentTypeFilter === 'video' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Video size={16} className="mr-2" />
                      Videos
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onContentTypeChange) onContentTypeChange('post');
                        setShowContentTypeDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        contentTypeFilter === 'post' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <FileText size={16} className="mr-2" />
                      Posts
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onContentTypeChange) onContentTypeChange('story');
                        setShowContentTypeDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        contentTypeFilter === 'story' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Clock size={16} className="mr-2" />
                      Stories
                    </button>
                  </div>
                </div>
              )}
            </div>
            
            {/* Sort By Dropdown */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowSortByDropdown(!showSortByDropdown);
                  setShowContentTypeDropdown(false);
                  setShowExportDropdown(false);
                }}
                className="px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none flex items-center"
              >
                Sort by: {sortBy === 'views' ? 'Views' : 
                         sortBy === 'likes' ? 'Likes' : 
                         sortBy === 'comments' ? 'Comments' : 
                         sortBy === 'shares' ? 'Shares' : 'Completion Rate'}
                <ChevronDown size={14} className="ml-1" />
              </button>
              
              {showSortByDropdown && (
                <div className="absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  <div className="py-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onSortByChange) onSortByChange('views');
                        setShowSortByDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        sortBy === 'views' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Eye size={16} className="mr-2" />
                      Views
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onSortByChange) onSortByChange('likes');
                        setShowSortByDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        sortBy === 'likes' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Heart size={16} className="mr-2" />
                      Likes
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onSortByChange) onSortByChange('comments');
                        setShowSortByDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        sortBy === 'comments' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <MessageCircle size={16} className="mr-2" />
                      Comments
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onSortByChange) onSortByChange('shares');
                        setShowSortByDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        sortBy === 'shares' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Share2 size={16} className="mr-2" />
                      Shares
                    </button>
                    {(contentTypeFilter === 'all' || contentTypeFilter === 'video') && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onSortByChange) onSortByChange('completionRate');
                          setShowSortByDropdown(false);
                        }}
                        className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                          sortBy === 'completionRate' ? 'bg-green-50 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Clock size={16} className="mr-2" />
                        Completion Rate
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Refresh Button */}
            {onRefresh && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  if (onRefresh) onRefresh();
                }}
                className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
                title="Refresh data"
              >
                <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
              </button>
            )}
            
            {/* Export Dropdown */}
            {onExport && (
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowExportDropdown(!showExportDropdown);
                    setShowContentTypeDropdown(false);
                    setShowSortByDropdown(false);
                  }}
                  className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
                  title="Export data"
                >
                  <Download size={16} />
                </button>
                
                {showExportDropdown && (
                  <div className="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    <div className="py-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onExport) onExport('csv');
                          setShowExportDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Export CSV
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onExport) onExport('json');
                          setShowExportDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Export JSON
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onExport) onExport('pdf');
                          setShowExportDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Export PDF
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500"></div>
          </div>
        ) : data.length === 0 ? (
          <div className="flex justify-center items-center h-64 text-gray-500">
            <p>No content data available for the selected filters.</p>
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Content
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <Eye size={14} className="inline mr-1" /> Views
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <Heart size={14} className="inline mr-1" /> Likes
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <MessageCircle size={14} className="inline mr-1" /> Comments
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <Share2 size={14} className="inline mr-1" /> Shares
                </th>
                {(contentTypeFilter === 'all' || contentTypeFilter === 'video') && (
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Completion
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.map((item) => (
                <tr 
                  key={item.id} 
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onContentSelect && onContentSelect(item.id, item.type)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-16 flex-shrink-0 bg-gray-100 rounded overflow-hidden">
                        <img 
                          src={item.thumbnailUrl} 
                          alt={item.title} 
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 line-clamp-1">
                          {item.title}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getContentTypeIcon(item.type)}
                      <span className="ml-1 text-sm text-gray-500 capitalize">
                        {item.type}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(item.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {formatNumber(item.stats.views)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {formatNumber(item.stats.likes)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {formatNumber(item.stats.comments)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {formatNumber(item.stats.shares)}
                  </td>
                  {(contentTypeFilter === 'all' || contentTypeFilter === 'video') && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                      {item.stats.completionRate !== undefined ? (
                        <span 
                          className={`inline-block px-2 py-1 rounded-full ${
                            item.stats.completionRate >= 0.7 ? 'bg-green-100 text-green-800' :
                            item.stats.completionRate >= 0.4 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}
                        >
                          {Math.round(item.stats.completionRate * 100)}%
                        </span>
                      ) : (
                        <span className="text-gray-400">N/A</span>
                      )}
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}
