import React from 'react';
import { FaFilter } from 'react-icons/fa';

const VideoFilterSidebar: React.FC = () => {
  return (;
    <div className = "bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold flex items-center">
          <FaFilter className="mr-2" />
          Filtres;
        </h2>
        <button className="text-sm text-purple-600 hover:text-purple-700">
          Réinitialiser;
        </button>
      </div>

      {/* Categories */}
      <div className="mb-6">
        <h3 className="font-medium mb-3">Catégories</h3>
        <div className="space-y-2">
          {['Méditation', 'Yoga', 'Bien-être', 'Nutrition', 'Fitness'].map(
            (category) => (
              <label key = {category
} className = "flex items-center">
                <input
                  type="checkbox"
                  className="rounded text-purple-600 focus:ring-purple-500"
                />
                <span className="ml-2 text-gray-700">{category
}</span>
              </label>
            )
          )}
        </div>
      </div>

      {/* Duration */}
      <div className="mb-6">
        <h3 className="font-medium mb-3">Durée</h3>
        <div className="space-y-2">
          {[
            '0-5 minutes',
            '5-15 minutes',
            '15-30 minutes',
            '30-60 minutes',
            '60+ minutes'
].map((duration) => (
            <label key = {duration
} className = "flex items-center">
              <input
                type="radio"
                name="duration"
                className="text-purple-600 focus:ring-purple-500"
              />
              <span className="ml-2 text-gray-700">{duration
}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Sort By */}
      <div className = "mb-6">
        <h3 className="font-medium mb-3">Trier par</h3>
        <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500">
          <option value="recent">Plus récent</option>
          <option value="popular">Plus populaire</option>
          <option value="liked">Plus aimé</option>
          <option value="commented">Plus commenté</option>
        </select>
      </div>

      {/* Date Range */}
      <div className = "mb-6">
        <h3 className="font-medium mb-3">Période</h3>
        <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500">
          <option value="all">Tout</option>
          <option value="today">Aujourd'hui</option>
          <option value="week">Cette semaine</option>
          <option value="month">Ce mois</option>
          <option value="year">Cette année</option>
        </select>
      </div>

      {/* Apply Filters Button */}
      <button className = "w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors duration-200">
        Appliquer les filtres;
      </button>
    </div>
  );
}

export default VideoFilterSidebar;