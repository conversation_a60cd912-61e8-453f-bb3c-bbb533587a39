import React from 'react';
import { Link } from 'react-router-dom';

// Temporary interface - replace with actual Video type
interface VideoCardProps {
  video: any; // Using 'any' temporarily
  className?: string;
  compact?: boolean;
}

export function VideoCard({ video, className, compact }: VideoCardProps) {
  const videoId = video?.id || 'unknown-id';
  const videoTitle = video?.title || 'Video Title Placeholder';

  return (
    <div className={`p-2 border rounded shadow-sm ${className || ''}`}>
      <Link to={`/videos/${videoId}`}>
        <h4 className="font-semibold">{videoTitle}</h4>
        <p className="text-xs text-gray-500">Video ID: {videoId}</p>
        <p className="text-sm mt-1"> (This is a placeholder VideoCard)</p>
      </Link>
    </div>
  );
}
