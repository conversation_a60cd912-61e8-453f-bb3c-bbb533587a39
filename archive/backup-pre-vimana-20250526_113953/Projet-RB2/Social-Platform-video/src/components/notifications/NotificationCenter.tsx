import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Bell, 
  X, 
  Check, 
  Trash2, 
  Filter, 
  Settings, 
  ChevronDown, 
  Loader2,
  Heart,
  MessageCircle,
  UserPlus,
  Share2,
  Users,
  DollarSign,
  Award,
  Video
} from 'lucide-react';
import { useNotificationsStore } from '../../store/notifications';
import { NotificationItem } from './NotificationItem';
import { NotificationType } from '../../api/notificationsApi';

interface NotificationCenterProps {
  className?: string;
}

export function NotificationCenter({ className = '' }: NotificationCenterProps) {
  const navigate = useNavigate();
  const {
    notifications,
    unreadCount,
    totalCount,
    isLoading,
    error,
    activeFilter,
    fetchNotifications,
    markAllAsRead,
    removeAllNotifications,
    setActiveFilter,
  } = useNotificationsStore();
  
  const [isOpen, setIsOpen] = useState(false);
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  const notificationCenterRef = useRef<HTMLDivElement>(null);
  const notificationsContainerRef = useRef<HTMLDivElement>(null);
  
  // Fetch notifications on mount
  useEffect(() => {
    if (isOpen) {
      fetchNotifications(1, 20, activeFilter === 'all' ? undefined : activeFilter);
    }
  }, [isOpen, activeFilter, fetchNotifications]);
  
  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        notificationCenterRef.current &&
        !notificationCenterRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setShowFilterDropdown(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Handle scroll to load more
  useEffect(() => {
    const handleScroll = () => {
      if (!notificationsContainerRef.current) return;
      
      const { scrollTop, scrollHeight, clientHeight } = notificationsContainerRef.current;
      
      // Load more when scrolled to bottom (with a 50px threshold)
      if (scrollTop + clientHeight >= scrollHeight - 50 && !isLoadingMore) {
        loadMoreNotifications();
      }
    };
    
    const container = notificationsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }
    
    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [page, isLoadingMore]);
  
  // Load more notifications
  const loadMoreNotifications = async () => {
    if (isLoading || isLoadingMore) return;
    
    const nextPage = page + 1;
    setIsLoadingMore(true);
    
    try {
      await fetchNotifications(nextPage, 20, activeFilter === 'all' ? undefined : activeFilter);
      setPage(nextPage);
    } catch (error) {
      console.error('Error loading more notifications:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };
  
  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };
  
  // Handle clear all notifications
  const handleClearAll = async () => {
    try {
      await removeAllNotifications();
    } catch (error) {
      console.error('Error clearing all notifications:', error);
    }
  };
  
  // Handle filter change
  const handleFilterChange = (filter: NotificationType | 'all' | 'unread') => {
    setActiveFilter(filter);
    setShowFilterDropdown(false);
    setPage(1);
  };
  
  // Get filter display name
  const getFilterDisplayName = (filter: NotificationType | 'all' | 'unread') => {
    switch (filter) {
      case 'all':
        return 'All Notifications';
      case 'unread':
        return 'Unread';
      case 'like':
        return 'Likes';
      case 'comment':
        return 'Comments';
      case 'follow':
        return 'Follows';
      case 'mention':
        return 'Mentions';
      case 'share':
        return 'Shares';
      case 'collaboration_request':
      case 'collaboration_accepted':
      case 'collaboration_declined':
        return 'Collaborations';
      case 'subscription':
        return 'Subscriptions';
      case 'tip':
      case 'payment':
      case 'payout':
        return 'Payments';
      case 'message':
        return 'Messages';
      case 'content_published':
      case 'content_trending':
        return 'Content';
      case 'achievement':
        return 'Achievements';
      case 'system':
        return 'System';
      default:
        return 'Notifications';
    }
  };
  
  // Get filter icon
  const getFilterIcon = (filter: NotificationType | 'all' | 'unread') => {
    switch (filter) {
      case 'like':
        return <Heart size={16} className="text-red-500" />;
      case 'comment':
      case 'mention':
      case 'message':
        return <MessageCircle size={16} className="text-blue-500" />;
      case 'follow':
        return <UserPlus size={16} className="text-green-500" />;
      case 'share':
        return <Share2 size={16} className="text-blue-500" />;
      case 'collaboration_request':
      case 'collaboration_accepted':
      case 'collaboration_declined':
      case 'subscription':
        return <Users size={16} className="text-indigo-500" />;
      case 'tip':
      case 'payment':
      case 'payout':
        return <DollarSign size={16} className="text-green-500" />;
      case 'content_published':
      case 'content_trending':
        return <Video size={16} className="text-red-500" />;
      case 'achievement':
        return <Award size={16} className="text-yellow-500" />;
      case 'unread':
        return <Bell size={16} className="text-blue-500" />;
      case 'system':
      case 'all':
      default:
        return <Bell size={16} className="text-gray-500" />;
    }
  };
  
  return (
    <div className={`relative ${className}`} ref={notificationCenterRef}>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 focus:outline-none"
      >
        <Bell size={20} />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 h-5 w-5 text-xs flex items-center justify-center bg-red-500 text-white rounded-full">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>
      
      {/* Notification Panel */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 sm:w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Header */}
          <div className="p-3 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold">Notifications</h3>
            <div className="flex space-x-1">
              <button
                onClick={() => navigate('/notifications')}
                className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
                title="View all notifications"
              >
                <Settings size={16} />
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
                title="Close"
              >
                <X size={16} />
              </button>
            </div>
          </div>
          
          {/* Filters */}
          <div className="p-2 border-b border-gray-200 flex justify-between items-center">
            <div className="relative">
              <button
                onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                className="px-2 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none flex items-center"
              >
                {getFilterIcon(activeFilter)}
                <span className="mx-1">{getFilterDisplayName(activeFilter)}</span>
                <ChevronDown size={14} />
              </button>
              
              {showFilterDropdown && (
                <div className="absolute left-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handleFilterChange('all')}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        activeFilter === 'all' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Bell size={16} className="mr-2 text-gray-500" />
                      All Notifications
                    </button>
                    <button
                      onClick={() => handleFilterChange('unread')}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        activeFilter === 'unread' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Bell size={16} className="mr-2 text-blue-500" />
                      Unread
                    </button>
                    <button
                      onClick={() => handleFilterChange('like')}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        activeFilter === 'like' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Heart size={16} className="mr-2 text-red-500" />
                      Likes
                    </button>
                    <button
                      onClick={() => handleFilterChange('comment')}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        activeFilter === 'comment' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <MessageCircle size={16} className="mr-2 text-blue-500" />
                      Comments
                    </button>
                    <button
                      onClick={() => handleFilterChange('follow')}
                      className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                        activeFilter === 'follow' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <UserPlus size={16} className="mr-2 text-green-500" />
                      Follows
                    </button>
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex space-x-1">
              <button
                onClick={handleMarkAllAsRead}
                disabled={unreadCount === 0}
                className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                title="Mark all as read"
              >
                <Check size={12} className="mr-1" />
                Read All
              </button>
              <button
                onClick={handleClearAll}
                disabled={totalCount === 0}
                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                title="Clear all notifications"
              >
                <Trash2 size={12} className="mr-1" />
                Clear All
              </button>
            </div>
          </div>
          
          {/* Notifications List */}
          <div
            ref={notificationsContainerRef}
            className="max-h-96 overflow-y-auto"
          >
            {isLoading && notifications.length === 0 ? (
              <div className="flex justify-center items-center h-32">
                <Loader2 size={24} className="animate-spin text-blue-500" />
              </div>
            ) : error ? (
              <div className="p-4 text-center text-red-500">
                <p>Error: {error}</p>
                <button
                  onClick={() => fetchNotifications()}
                  className="mt-2 text-sm underline"
                >
                  Try Again
                </button>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <Bell size={24} className="mx-auto mb-2 text-gray-400" />
                <p>No notifications</p>
                {activeFilter !== 'all' && (
                  <button
                    onClick={() => handleFilterChange('all')}
                    className="mt-2 text-sm text-blue-500 hover:underline"
                  >
                    View all notifications
                  </button>
                )}
              </div>
            ) : (
              <>
                {notifications.map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onClick={() => setIsOpen(false)}
                  />
                ))}
                
                {isLoadingMore && (
                  <div className="flex justify-center items-center py-4">
                    <Loader2 size={20} className="animate-spin text-blue-500" />
                  </div>
                )}
              </>
            )}
          </div>
          
          {/* Footer */}
          <div className="p-2 border-t border-gray-200 text-center">
            <button
              onClick={() => {
                navigate('/notifications');
                setIsOpen(false);
              }}
              className="text-sm text-blue-500 hover:underline"
            >
              View All Notifications
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
