import { useState, useEffect } from 'react';
import { CreditCard, Trash2, Plus, Check, X, Loader2, DollarSign } from 'lucide-react';
import { useMonetizationStore } from '../../store/monetization';
import { PaymentMethod } from '../../api/monetizationApi';

interface PaymentMethodsProps {
  className?: string;
}

export function PaymentMethods({ className = '' }: PaymentMethodsProps) {
  const {
    paymentMethods,
    isLoading,
    error,
    fetchPaymentMethods,
    addPaymentMethod,
    removePaymentMethod,
    setDefaultPayment,
    isAddingPaymentMethod,
    setAddingPaymentMethod,
  } = useMonetizationStore();
  
  const [newPaymentMethod, setNewPaymentMethod] = useState({
    type: 'card' as 'card' | 'paypal' | 'bank_account',
    cardNumber: '',
    cardholderName: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    email: '',
    accountName: '',
    accountNumber: '',
    routingNumber: '',
    bankName: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Fetch payment methods on mount
  useEffect(() => {
    fetchPaymentMethods();
  }, [fetchPaymentMethods]);
  
  // Reset form when adding payment method state changes
  useEffect(() => {
    if (!isAddingPaymentMethod) {
      setNewPaymentMethod({
        type: 'card',
        cardNumber: '',
        cardholderName: '',
        expiryMonth: '',
        expiryYear: '',
        cvv: '',
        email: '',
        accountName: '',
        accountNumber: '',
        routingNumber: '',
        bankName: '',
      });
      setErrors({});
    }
  }, [isAddingPaymentMethod]);
  
  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (newPaymentMethod.type === 'card') {
      if (!newPaymentMethod.cardNumber.trim()) {
        newErrors.cardNumber = 'Card number is required';
      } else if (!/^\d{16}$/.test(newPaymentMethod.cardNumber.replace(/\s/g, ''))) {
        newErrors.cardNumber = 'Invalid card number';
      }
      
      if (!newPaymentMethod.cardholderName.trim()) {
        newErrors.cardholderName = 'Cardholder name is required';
      }
      
      if (!newPaymentMethod.expiryMonth.trim()) {
        newErrors.expiryMonth = 'Expiry month is required';
      } else if (!/^(0[1-9]|1[0-2])$/.test(newPaymentMethod.expiryMonth)) {
        newErrors.expiryMonth = 'Invalid month';
      }
      
      if (!newPaymentMethod.expiryYear.trim()) {
        newErrors.expiryYear = 'Expiry year is required';
      } else if (!/^\d{4}$/.test(newPaymentMethod.expiryYear)) {
        newErrors.expiryYear = 'Invalid year';
      }
      
      if (!newPaymentMethod.cvv.trim()) {
        newErrors.cvv = 'CVV is required';
      } else if (!/^\d{3,4}$/.test(newPaymentMethod.cvv)) {
        newErrors.cvv = 'Invalid CVV';
      }
    } else if (newPaymentMethod.type === 'paypal') {
      if (!newPaymentMethod.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newPaymentMethod.email)) {
        newErrors.email = 'Invalid email address';
      }
    } else if (newPaymentMethod.type === 'bank_account') {
      if (!newPaymentMethod.accountName.trim()) {
        newErrors.accountName = 'Account name is required';
      }
      
      if (!newPaymentMethod.accountNumber.trim()) {
        newErrors.accountNumber = 'Account number is required';
      } else if (!/^\d{8,17}$/.test(newPaymentMethod.accountNumber)) {
        newErrors.accountNumber = 'Invalid account number';
      }
      
      if (!newPaymentMethod.routingNumber.trim()) {
        newErrors.routingNumber = 'Routing number is required';
      } else if (!/^\d{9}$/.test(newPaymentMethod.routingNumber)) {
        newErrors.routingNumber = 'Invalid routing number';
      }
      
      if (!newPaymentMethod.bankName.trim()) {
        newErrors.bankName = 'Bank name is required';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle adding a new payment method
  const handleAddPaymentMethod = () => {
    if (!validateForm()) return;
    
    let details: any = {};
    
    if (newPaymentMethod.type === 'card') {
      details = {
        cardNumber: newPaymentMethod.cardNumber.replace(/\s/g, ''),
        cardholderName: newPaymentMethod.cardholderName,
        expiryMonth: newPaymentMethod.expiryMonth,
        expiryYear: newPaymentMethod.expiryYear,
        cvv: newPaymentMethod.cvv,
      };
    } else if (newPaymentMethod.type === 'paypal') {
      details = {
        email: newPaymentMethod.email,
      };
    } else if (newPaymentMethod.type === 'bank_account') {
      details = {
        accountName: newPaymentMethod.accountName,
        accountNumber: newPaymentMethod.accountNumber,
        routingNumber: newPaymentMethod.routingNumber,
        bankName: newPaymentMethod.bankName,
      };
    }
    
    addPaymentMethod(newPaymentMethod.type, details);
  };
  
  // Format card number with spaces
  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || '';
    const parts = [];
    
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    
    if (parts.length) {
      return parts.join(' ');
    } else {
      return value;
    }
  };
  
  // Get card type based on number
  const getCardType = (number: string) => {
    const re = {
      visa: /^4/,
      mastercard: /^5[1-5]/,
      amex: /^3[47]/,
      discover: /^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)/,
    };
    
    if (re.visa.test(number)) return 'Visa';
    if (re.mastercard.test(number)) return 'Mastercard';
    if (re.amex.test(number)) return 'American Express';
    if (re.discover.test(number)) return 'Discover';
    
    return 'Unknown';
  };
  
  // Get payment method display info
  const getPaymentMethodInfo = (method: PaymentMethod) => {
    switch (method.type) {
      case 'card':
        return {
          icon: <CreditCard size={20} className="text-blue-500" />,
          title: `${method.brand} •••• ${method.lastFour}`,
          subtitle: `Expires ${method.expiryDate}`,
        };
      case 'paypal':
        return {
          icon: <span className="text-blue-500 font-bold text-lg">P</span>,
          title: 'PayPal',
          subtitle: method.email,
        };
      case 'bank_account':
        return {
          icon: <DollarSign size={20} className="text-green-500" />,
          title: method.bankName || 'Bank Account',
          subtitle: `Account ending in ${method.accountNumber?.slice(-4) || '****'}`,
        };
      default:
        return {
          icon: <CreditCard size={20} />,
          title: 'Payment Method',
          subtitle: '',
        };
    }
  };
  
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Payment Methods</h2>
        
        <button
          onClick={() => setAddingPaymentMethod(!isAddingPaymentMethod)}
          className="px-3 py-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
        >
          {isAddingPaymentMethod ? (
            <>
              <X size={16} className="mr-1" />
              Cancel
            </>
          ) : (
            <>
              <Plus size={16} className="mr-1" />
              Add Method
            </>
          )}
        </button>
      </div>
      
      {/* Add Payment Method Form */}
      {isAddingPaymentMethod && (
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-4">
          <h3 className="text-lg font-medium mb-4">Add Payment Method</h3>
          
          <div className="space-y-4">
            {/* Payment Method Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Method Type
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={newPaymentMethod.type === 'card'}
                    onChange={() => setNewPaymentMethod({ ...newPaymentMethod, type: 'card' })}
                    className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Credit/Debit Card</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={newPaymentMethod.type === 'paypal'}
                    onChange={() => setNewPaymentMethod({ ...newPaymentMethod, type: 'paypal' })}
                    className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">PayPal</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={newPaymentMethod.type === 'bank_account'}
                    onChange={() => setNewPaymentMethod({ ...newPaymentMethod, type: 'bank_account' })}
                    className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Bank Account</span>
                </label>
              </div>
            </div>
            
            {/* Credit Card Form */}
            {newPaymentMethod.type === 'card' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Number
                  </label>
                  <input
                    type="text"
                    value={newPaymentMethod.cardNumber}
                    onChange={(e) => setNewPaymentMethod({
                      ...newPaymentMethod,
                      cardNumber: formatCardNumber(e.target.value),
                    })}
                    className={`w-full px-3 py-2 border ${
                      errors.cardNumber ? 'border-red-300' : 'border-gray-300'
                    } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    placeholder="1234 5678 9012 3456"
                    maxLength={19}
                  />
                  {errors.cardNumber && (
                    <p className="mt-1 text-xs text-red-500">{errors.cardNumber}</p>
                  )}
                  {newPaymentMethod.cardNumber && (
                    <p className="mt-1 text-xs text-gray-500">
                      Card Type: {getCardType(newPaymentMethod.cardNumber)}
                    </p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cardholder Name
                  </label>
                  <input
                    type="text"
                    value={newPaymentMethod.cardholderName}
                    onChange={(e) => setNewPaymentMethod({
                      ...newPaymentMethod,
                      cardholderName: e.target.value,
                    })}
                    className={`w-full px-3 py-2 border ${
                      errors.cardholderName ? 'border-red-300' : 'border-gray-300'
                    } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    placeholder="John Doe"
                  />
                  {errors.cardholderName && (
                    <p className="mt-1 text-xs text-red-500">{errors.cardholderName}</p>
                  )}
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Expiry Month
                    </label>
                    <select
                      value={newPaymentMethod.expiryMonth}
                      onChange={(e) => setNewPaymentMethod({
                        ...newPaymentMethod,
                        expiryMonth: e.target.value,
                      })}
                      className={`w-full px-3 py-2 border ${
                        errors.expiryMonth ? 'border-red-300' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    >
                      <option value="">Month</option>
                      {Array.from({ length: 12 }, (_, i) => {
                        const month = (i + 1).toString().padStart(2, '0');
                        return (
                          <option key={month} value={month}>
                            {month}
                          </option>
                        );
                      })}
                    </select>
                    {errors.expiryMonth && (
                      <p className="mt-1 text-xs text-red-500">{errors.expiryMonth}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Expiry Year
                    </label>
                    <select
                      value={newPaymentMethod.expiryYear}
                      onChange={(e) => setNewPaymentMethod({
                        ...newPaymentMethod,
                        expiryYear: e.target.value,
                      })}
                      className={`w-full px-3 py-2 border ${
                        errors.expiryYear ? 'border-red-300' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    >
                      <option value="">Year</option>
                      {Array.from({ length: 10 }, (_, i) => {
                        const year = (new Date().getFullYear() + i).toString();
                        return (
                          <option key={year} value={year}>
                            {year}
                          </option>
                        );
                      })}
                    </select>
                    {errors.expiryYear && (
                      <p className="mt-1 text-xs text-red-500">{errors.expiryYear}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      CVV
                    </label>
                    <input
                      type="password"
                      value={newPaymentMethod.cvv}
                      onChange={(e) => setNewPaymentMethod({
                        ...newPaymentMethod,
                        cvv: e.target.value.replace(/\D/g, ''),
                      })}
                      className={`w-full px-3 py-2 border ${
                        errors.cvv ? 'border-red-300' : 'border-gray-300'
                      } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder="123"
                      maxLength={4}
                    />
                    {errors.cvv && (
                      <p className="mt-1 text-xs text-red-500">{errors.cvv}</p>
                    )}
                  </div>
                </div>
              </>
            )}
            
            {/* PayPal Form */}
            {newPaymentMethod.type === 'paypal' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  PayPal Email
                </label>
                <input
                  type="email"
                  value={newPaymentMethod.email}
                  onChange={(e) => setNewPaymentMethod({
                    ...newPaymentMethod,
                    email: e.target.value,
                  })}
                  className={`w-full px-3 py-2 border ${
                    errors.email ? 'border-red-300' : 'border-gray-300'
                  } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="mt-1 text-xs text-red-500">{errors.email}</p>
                )}
              </div>
            )}
            
            {/* Bank Account Form */}
            {newPaymentMethod.type === 'bank_account' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account Holder Name
                  </label>
                  <input
                    type="text"
                    value={newPaymentMethod.accountName}
                    onChange={(e) => setNewPaymentMethod({
                      ...newPaymentMethod,
                      accountName: e.target.value,
                    })}
                    className={`w-full px-3 py-2 border ${
                      errors.accountName ? 'border-red-300' : 'border-gray-300'
                    } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    placeholder="John Doe"
                  />
                  {errors.accountName && (
                    <p className="mt-1 text-xs text-red-500">{errors.accountName}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bank Name
                  </label>
                  <input
                    type="text"
                    value={newPaymentMethod.bankName}
                    onChange={(e) => setNewPaymentMethod({
                      ...newPaymentMethod,
                      bankName: e.target.value,
                    })}
                    className={`w-full px-3 py-2 border ${
                      errors.bankName ? 'border-red-300' : 'border-gray-300'
                    } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    placeholder="Bank of America"
                  />
                  {errors.bankName && (
                    <p className="mt-1 text-xs text-red-500">{errors.bankName}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Routing Number
                  </label>
                  <input
                    type="text"
                    value={newPaymentMethod.routingNumber}
                    onChange={(e) => setNewPaymentMethod({
                      ...newPaymentMethod,
                      routingNumber: e.target.value.replace(/\D/g, ''),
                    })}
                    className={`w-full px-3 py-2 border ${
                      errors.routingNumber ? 'border-red-300' : 'border-gray-300'
                    } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    placeholder="*********"
                    maxLength={9}
                  />
                  {errors.routingNumber && (
                    <p className="mt-1 text-xs text-red-500">{errors.routingNumber}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account Number
                  </label>
                  <input
                    type="text"
                    value={newPaymentMethod.accountNumber}
                    onChange={(e) => setNewPaymentMethod({
                      ...newPaymentMethod,
                      accountNumber: e.target.value.replace(/\D/g, ''),
                    })}
                    className={`w-full px-3 py-2 border ${
                      errors.accountNumber ? 'border-red-300' : 'border-gray-300'
                    } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    placeholder="**************"
                    maxLength={17}
                  />
                  {errors.accountNumber && (
                    <p className="mt-1 text-xs text-red-500">{errors.accountNumber}</p>
                  )}
                </div>
              </>
            )}
            
            <div className="flex justify-end">
              <button
                onClick={() => setAddingPaymentMethod(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md mr-2 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Cancel
              </button>
              
              <button
                onClick={handleAddPaymentMethod}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
              >
                <Plus size={16} className="mr-1" />
                Add Payment Method
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Payment Methods List */}
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 size={24} className="animate-spin text-blue-500" />
        </div>
      ) : error ? (
        <div className="p-4 text-center text-red-500">
          <p>Error: {error}</p>
          <button
            onClick={fetchPaymentMethods}
            className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            Try Again
          </button>
        </div>
      ) : paymentMethods.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-gray-500">You haven't added any payment methods yet.</p>
          {!isAddingPaymentMethod && (
            <button
              onClick={() => setAddingPaymentMethod(true)}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Add Your First Payment Method
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {paymentMethods.map((method) => {
            const { icon, title, subtitle } = getPaymentMethodInfo(method);
            
            return (
              <div
                key={method.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 flex items-center"
              >
                <div className="flex-shrink-0 mr-4">
                  <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    {icon}
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center">
                    <h3 className="text-sm font-medium text-gray-900">{title}</h3>
                    {method.isDefault && (
                      <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
                        Default
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">{subtitle}</p>
                </div>
                
                <div className="flex-shrink-0 flex space-x-2">
                  {!method.isDefault && (
                    <button
                      onClick={() => setDefaultPayment(method.id)}
                      className="p-1.5 text-gray-500 hover:text-blue-500 rounded-md hover:bg-gray-100 focus:outline-none"
                      title="Set as default"
                    >
                      <Check size={16} />
                    </button>
                  )}
                  
                  <button
                    onClick={() => removePaymentMethod(method.id)}
                    className="p-1.5 text-gray-500 hover:text-red-500 rounded-md hover:bg-gray-100 focus:outline-none"
                    title="Remove"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
