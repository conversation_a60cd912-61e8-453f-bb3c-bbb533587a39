import React, { useState } from 'react';
import { 
  SelectionToolbar, 
  useSelection 
} from '../selection';
import { 
  DollarSign, 
  PieChart, 
  Globe, 
  Gift, 
  CreditCard, 
  Crown 
} from 'lucide-react';
import { Video, Post } from '../../types';
import SelectionIntegrationService, { MonetizationResponse } from '../../services/selectionIntegrationService';

// Props pour la barre d'outils de monétisation
interface SelectionMonetizationToolbarProps<T extends Video | Post> {
  // Type de contenu
  contentType: 'video' | 'post' | 'livestream';
  // Callback exécuté lorsqu'une action de monétisation est terminée
  onMonetizationComplete?: (response: MonetizationResponse) => void;
  // Position de la barre d'outils
  position?: 'top' | 'bottom';
  // Est-ce que la barre d'outils est fixe
  fixed?: boolean;
  // Classe CSS pour la barre d'outils
  className?: string;
}

// Composant de modal pour la configuration des prix du contenu premium
const PremiumConfigModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (price: number) => void;
}> = ({ isOpen, onClose, onSubmit }) => {
  const [price, setPrice] = useState<number>(4.99);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <h2 className="text-xl font-bold mb-4">Configuration du contenu premium</h2>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Prix (€)
          </label>
          <input
            type="number"
            min="0.99"
            step="0.50"
            value={price}
            onChange={(e) => setPrice(parseFloat(e.target.value))}
            className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
          >
            Annuler
          </button>
          <button
            onClick={() => onSubmit(price)}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
          >
            Confirmer
          </button>
        </div>
      </div>
    </div>
  );
};

// Composant de modal pour la configuration des dons
const DonationConfigModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (settings: {
    minAmount?: number;
    suggestedAmounts: number[];
    thanksMessage: string;
  }) => void;
}> = ({ isOpen, onClose, onSubmit }) => {
  const [minAmount, setMinAmount] = useState<number>(1);
  const [suggestedAmounts, setSuggestedAmounts] = useState<string>("2, 5, 10, 20");
  const [thanksMessage, setThanksMessage] = useState<string>("Merci pour votre soutien !");

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <h2 className="text-xl font-bold mb-4">Configuration des dons</h2>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Montant minimum (€)
          </label>
          <input
            type="number"
            min="0"
            step="0.50"
            value={minAmount}
            onChange={(e) => setMinAmount(parseFloat(e.target.value))}
            className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Montants suggérés (séparés par des virgules)
          </label>
          <input
            type="text"
            value={suggestedAmounts}
            onChange={(e) => setSuggestedAmounts(e.target.value)}
            className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Message de remerciement
          </label>
          <textarea
            value={thanksMessage}
            onChange={(e) => setThanksMessage(e.target.value)}
            className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            rows={3}
          />
        </div>
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
          >
            Annuler
          </button>
          <button
            onClick={() => onSubmit({
              minAmount,
              suggestedAmounts: suggestedAmounts.split(',').map(a => parseFloat(a.trim())).filter(a => !isNaN(a)),
              thanksMessage
            })}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
          >
            Confirmer
          </button>
        </div>
      </div>
    </div>
  );
};

// Composant pour la barre d'outils de monétisation
export function SelectionMonetizationToolbar<T extends Video | Post>({
  contentType,
  onMonetizationComplete,
  position = 'bottom',
  fixed = true,
  className = '',
}: SelectionMonetizationToolbarProps<T>) {
  // Utiliser le contexte de sélection
  const { selectedIds, selectedItemsList } = useSelection<T>();
  
  // États pour les modals
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  const [showDonationModal, setShowDonationModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);

  // Gérer la conversion en contenu premium
  const handlePremiumSubmit = async (price: number) => {
    setIsLoading(true);
    setStatusMessage("Conversion en contenu premium...");
    
    try {
      const response = await SelectionIntegrationService.convertToPremiumContent(
        selectedIds,
        contentType,
        price
      );
      
      if (response.success) {
        setStatusMessage("Conversion en contenu premium réussie !");
      } else {
        setStatusMessage(`Erreur: ${response.message}`);
      }
      
      if (onMonetizationComplete) {
        onMonetizationComplete(response);
      }
    } catch (error) {
      setStatusMessage("Une erreur s'est produite lors de la conversion en contenu premium.");
      console.error(error);
    } finally {
      setIsLoading(false);
      setShowPremiumModal(false);
      // Masquer le message après 3 secondes
      setTimeout(() => setStatusMessage(null), 3000);
    }
  };

  // Gérer la configuration des dons
  const handleDonationSubmit = async (settings: {
    minAmount?: number;
    suggestedAmounts: number[];
    thanksMessage: string;
  }) => {
    setIsLoading(true);
    setStatusMessage("Configuration des dons...");
    
    try {
      const response = await SelectionIntegrationService.setupDonations(
        selectedIds,
        contentType,
        {
          minAmount: settings.minAmount,
          suggestedAmounts: settings.suggestedAmounts,
          thanksMessage: settings.thanksMessage,
        }
      );
      
      if (response.success) {
        setStatusMessage("Configuration des dons réussie !");
      } else {
        setStatusMessage(`Erreur: ${response.message}`);
      }
      
      if (onMonetizationComplete) {
        onMonetizationComplete(response);
      }
    } catch (error) {
      setStatusMessage("Une erreur s'est produite lors de la configuration des dons.");
      console.error(error);
    } finally {
      setIsLoading(false);
      setShowDonationModal(false);
      // Masquer le message après 3 secondes
      setTimeout(() => setStatusMessage(null), 3000);
    }
  };

  // Actions de monétisation pour la barre d'outils
  const monetizationActions = [
    {
      icon: <Crown size={18} />,
      label: 'Contenu Premium',
      onClick: () => setShowPremiumModal(true),
      show: (count: number) => count > 0,
    },
    {
      icon: <Gift size={18} />,
      label: 'Activer les dons',
      onClick: () => setShowDonationModal(true),
      show: (count: number) => count > 0,
    },
    {
      icon: <Globe size={18} />,
      label: 'Promouvoir',
      onClick: async () => {
        setIsLoading(true);
        setStatusMessage("Préparation de la promotion...");
        
        try {
          // Rediriger vers la page de promotion avec les ID sélectionnés
          window.location.href = `/promotion?ids=${selectedIds.join(',')}&type=${contentType}`;
        } catch (error) {
          setStatusMessage("Une erreur s'est produite lors de la préparation de la promotion.");
          console.error(error);
          setIsLoading(false);
        }
      },
      show: (count: number) => count > 0,
    },
    {
      icon: <PieChart size={18} />,
      label: 'Statistiques',
      onClick: async () => {
        setIsLoading(true);
        setStatusMessage("Récupération des statistiques...");
        
        try {
          // Rediriger vers la page de statistiques avec les ID sélectionnés
          window.location.href = `/revenue-stats?ids=${selectedIds.join(',')}&type=${contentType}`;
        } catch (error) {
          setStatusMessage("Une erreur s'est produite lors de la récupération des statistiques.");
          console.error(error);
          setIsLoading(false);
        }
      },
      show: (count: number) => count > 0,
    },
  ];

  // Titre personnalisé pour la barre d'outils
  const customTitle = (count: number) => (
    <div className="flex items-center">
      <DollarSign size={16} className="mr-2 text-green-500" />
      <span>
        {count} {count > 1 ? 'éléments' : 'élément'} sélectionné{count > 1 ? 's' : ''} pour monétisation
      </span>
    </div>
  );

  return (
    <>
      {/* Barre d'outils de sélection avec actions de monétisation */}
      <SelectionToolbar<T>
        actions={monetizationActions}
        title={customTitle}
        position={position}
        fixed={fixed}
        className={`${className} ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
      />
      
      {/* Modals de configuration */}
      <PremiumConfigModal
        isOpen={showPremiumModal}
        onClose={() => setShowPremiumModal(false)}
        onSubmit={handlePremiumSubmit}
      />
      
      <DonationConfigModal
        isOpen={showDonationModal}
        onClose={() => setShowDonationModal(false)}
        onSubmit={handleDonationSubmit}
      />
      
      {/* Notification de statut */}
      {statusMessage && (
        <div className="fixed bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 text-white px-4 py-2 rounded-lg shadow-lg z-50">
          {statusMessage}
        </div>
      )}
    </>
  );
} 