import { useState, useEffect } from 'react';
import { Heart, X, DollarSign, CreditCard, Loader2 } from 'lucide-react';
import { useMonetizationStore } from '../../store/monetization';
import { PaymentMethod } from '../../api/monetizationApi';

interface TipButtonProps {
  creatorId: string;
  creatorName: string;
  contentId?: string;
  contentType?: 'video' | 'post' | 'livestream';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function TipButton({
  creatorId,
  creatorName,
  contentId,
  contentType,
  size = 'md',
  className = '',
}: TipButtonProps) {
  const {
    paymentMethods,
    isLoading,
    error,
    fetchPaymentMethods,
    sendTip,
    selectedPaymentMethodId,
    setSelectedPaymentMethod,
  } = useMonetizationStore();
  
  const [showTipModal, setShowTipModal] = useState(false);
  const [tipAmount, setTipAmount] = useState(5);
  const [tipMessage, setTipMessage] = useState('');
  const [tipCurrency, setTipCurrency] = useState('USD');
  const [isSending, setIsSending] = useState(false);
  const [tipSuccess, setTipSuccess] = useState(false);
  
  // Predefined tip amounts
  const tipAmounts = [1, 5, 10, 20, 50, 100];
  
  // Button size classes
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base',
  };
  
  // Fetch payment methods when modal is opened
  useEffect(() => {
    if (showTipModal) {
      fetchPaymentMethods();
    }
  }, [showTipModal, fetchPaymentMethods]);
  
  // Reset state when modal is closed
  useEffect(() => {
    if (!showTipModal) {
      setTipSuccess(false);
      setIsSending(false);
    }
  }, [showTipModal]);
  
  // Handle sending a tip
  const handleSendTip = async () => {
    if (!selectedPaymentMethodId) return;
    
    setIsSending(true);
    
    try {
      await sendTip(
        creatorId,
        tipAmount,
        tipCurrency,
        selectedPaymentMethodId,
        tipMessage,
        contentId,
        contentType
      );
      
      setTipSuccess(true);
      setTipMessage('');
      
      // Close modal after a delay
      setTimeout(() => {
        setShowTipModal(false);
      }, 2000);
    } catch (error) {
      console.error('Error sending tip:', error);
    } finally {
      setIsSending(false);
    }
  };
  
  // Format currency
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };
  
  // Get payment method display info
  const getPaymentMethodInfo = (method: PaymentMethod) => {
    switch (method.type) {
      case 'card':
        return {
          icon: <CreditCard size={16} className="mr-2" />,
          label: `${method.brand} •••• ${method.lastFour}`,
          expires: method.expiryDate ? `Expires ${method.expiryDate}` : '',
        };
      case 'paypal':
        return {
          icon: <span className="mr-2 text-blue-500 font-bold">P</span>,
          label: `PayPal - ${method.email}`,
          expires: '',
        };
      case 'bank_account':
        return {
          icon: <DollarSign size={16} className="mr-2" />,
          label: `Bank Account - ${method.accountName}`,
          expires: '',
        };
      default:
        return {
          icon: <CreditCard size={16} className="mr-2" />,
          label: 'Payment Method',
          expires: '',
        };
    }
  };
  
  return (
    <>
      <button
        onClick={() => setShowTipModal(true)}
        className={`bg-pink-500 text-white rounded-full hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 flex items-center ${
          sizeClasses[size]
        } ${className}`}
      >
        <Heart size={size === 'sm' ? 14 : size === 'md' ? 16 : 18} className="mr-1" />
        Tip
      </button>
      
      {/* Tip Modal */}
      {showTipModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-semibold">Send a Tip to {creatorName}</h3>
              <button
                onClick={() => setShowTipModal(false)}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X size={20} />
              </button>
            </div>
            
            {/* Modal Content */}
            <div className="p-4">
              {tipSuccess ? (
                <div className="text-center py-8">
                  <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <Heart size={32} className="text-pink-500" />
                  </div>
                  <h4 className="text-xl font-semibold mb-2">Thank You!</h4>
                  <p className="text-gray-600">
                    Your tip of {formatCurrency(tipAmount, tipCurrency)} has been sent to {creatorName}.
                  </p>
                </div>
              ) : (
                <>
                  {/* Tip Amount Selection */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Tip Amount
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      {tipAmounts.map((amount) => (
                        <button
                          key={amount}
                          onClick={() => setTipAmount(amount)}
                          className={`py-2 rounded-md ${
                            tipAmount === amount
                              ? 'bg-pink-500 text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {formatCurrency(amount, tipCurrency)}
                        </button>
                      ))}
                    </div>
                    
                    <div className="mt-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Custom Amount
                      </label>
                      <div className="flex">
                        <div className="flex-shrink-0 inline-flex items-center px-3 py-2 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 rounded-l-md">
                          {tipCurrency === 'USD' ? '$' : tipCurrency === 'EUR' ? '€' : '£'}
                        </div>
                        <input
                          type="number"
                          value={tipAmount}
                          onChange={(e) => setTipAmount(Math.max(1, parseFloat(e.target.value) || 0))}
                          className="flex-1 min-w-0 block w-full px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-pink-500 focus:border-pink-500"
                          min="1"
                          step="1"
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* Currency Selection */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Currency
                    </label>
                    <select
                      value={tipCurrency}
                      onChange={(e) => setTipCurrency(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-pink-500 focus:border-pink-500"
                    >
                      <option value="USD">USD - US Dollar</option>
                      <option value="EUR">EUR - Euro</option>
                      <option value="GBP">GBP - British Pound</option>
                      <option value="CAD">CAD - Canadian Dollar</option>
                      <option value="AUD">AUD - Australian Dollar</option>
                    </select>
                  </div>
                  
                  {/* Message */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Add a Message (Optional)
                    </label>
                    <textarea
                      value={tipMessage}
                      onChange={(e) => setTipMessage(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-pink-500 focus:border-pink-500"
                      rows={3}
                      placeholder="Say something nice..."
                    />
                  </div>
                  
                  {/* Payment Methods */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Method
                    </label>
                    
                    {isLoading ? (
                      <div className="flex justify-center py-4">
                        <Loader2 size={24} className="animate-spin text-pink-500" />
                      </div>
                    ) : error ? (
                      <div className="text-center py-4 text-red-500">
                        <p>Error loading payment methods.</p>
                        <button
                          onClick={fetchPaymentMethods}
                          className="mt-2 text-sm text-pink-500 hover:text-pink-600"
                        >
                          Try Again
                        </button>
                      </div>
                    ) : paymentMethods.length === 0 ? (
                      <div className="text-center py-4 text-gray-500">
                        <p>No payment methods available.</p>
                        <p className="text-sm mt-1">
                          Please add a payment method in your account settings.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {paymentMethods.map((method) => {
                          const { icon, label, expires } = getPaymentMethodInfo(method);
                          
                          return (
                            <div
                              key={method.id}
                              onClick={() => setSelectedPaymentMethod(method.id)}
                              className={`flex items-center p-3 border rounded-md cursor-pointer ${
                                selectedPaymentMethodId === method.id
                                  ? 'border-pink-500 bg-pink-50'
                                  : 'border-gray-300 hover:border-pink-300'
                              }`}
                            >
                              <div className="flex-shrink-0">{icon}</div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900">{label}</p>
                                {expires && (
                                  <p className="text-xs text-gray-500">{expires}</p>
                                )}
                              </div>
                              <div className="flex-shrink-0">
                                <div
                                  className={`w-4 h-4 rounded-full border ${
                                    selectedPaymentMethodId === method.id
                                      ? 'border-pink-500 bg-pink-500'
                                      : 'border-gray-300'
                                  }`}
                                >
                                  {selectedPaymentMethodId === method.id && (
                                    <div className="w-2 h-2 mx-auto mt-1 bg-white rounded-full" />
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
            
            {/* Modal Footer */}
            {!tipSuccess && (
              <div className="p-4 border-t flex justify-end">
                <button
                  onClick={() => setShowTipModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md mr-2 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                
                <button
                  onClick={handleSendTip}
                  disabled={!selectedPaymentMethodId || isSending}
                  className="px-4 py-2 bg-pink-500 text-white rounded-md hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                >
                  {isSending ? (
                    <>
                      <Loader2 size={16} className="animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Heart size={16} className="mr-2" />
                      Send {formatCurrency(tipAmount, tipCurrency)} Tip
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
