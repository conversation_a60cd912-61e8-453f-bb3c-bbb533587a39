import React, { useState, useEffect } from 'react';
import { SelectionProvider } from '../selection';
import { Video } from '../../types';
import { VideoGridWithSelection } from '../video/VideoGridWithSelection';
import { SelectionMonetizationToolbar } from './SelectionMonetizationToolbar';
import SelectionIntegrationService, { ContentRevenueStats, MonetizationResponse } from '../../services/selectionIntegrationService';
import { Loader2, AlertTriangle, CheckCircle } from 'lucide-react';

// Props pour la grille de vidéos avec monétisation
interface VideoGridWithMonetizationProps {
  // Liste des vidéos à afficher
  videos: Video[];
  // Classe CSS pour la grille
  className?: string;
  // Nombre de colonnes par taille d'écran
  columns?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  // Fonction appelée lorsqu'une vidéo est cliquée
  onVideoClick?: (video: Video) => void;
  // Callback lorsqu'une action de monétisation est effectuée
  onMonetizationAction?: (action: string, contentIds: string[], response: MonetizationResponse) => void;
  // Afficher les statistiques de revenus sur les cartes de vidéo
  showRevenueStats?: boolean;
}

// Composant pour la grille de vidéos avec monétisation
export function VideoGridWithMonetization({
  videos,
  className = '',
  columns = { sm: 1, md: 2, lg: 3, xl: 4 },
  onVideoClick,
  onMonetizationAction,
  showRevenueStats = false,
}: VideoGridWithMonetizationProps) {
  // États pour les données et le statut
  const [videosWithStats, setVideosWithStats] = useState<(Video & { revenueStats?: ContentRevenueStats })[]>(videos);
  const [monetizationStatus, setMonetizationStatus] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Charger les statistiques de revenus si nécessaire
  useEffect(() => {
    if (showRevenueStats) {
      loadRevenueStats();
    }
    
    // Charger le statut de monétisation pour toutes les vidéos
    loadMonetizationStatus();
  }, [videos, showRevenueStats]);

  // Charger les statistiques de revenus
  const loadRevenueStats = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const videoIds = videos.map(video => video.id);
      const stats = await SelectionIntegrationService.getContentRevenueStats(videoIds, 'video');
      
      // Fusionner les statistiques avec les vidéos
      const updatedVideos = videos.map(video => {
        const videoStats = stats.find(stat => stat.contentId === video.id);
        return {
          ...video,
          revenueStats: videoStats,
        };
      });
      
      setVideosWithStats(updatedVideos);
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques de revenus:', error);
      setError('Erreur lors du chargement des statistiques de revenus');
    } finally {
      setIsLoading(false);
    }
  };

  // Charger le statut de monétisation
  const loadMonetizationStatus = async () => {
    try {
      const videoIds = videos.map(video => video.id);
      const status = await SelectionIntegrationService.getMonetizationStatus(videoIds, 'video');
      setMonetizationStatus(status);
    } catch (error) {
      console.error('Erreur lors du chargement du statut de monétisation:', error);
    }
  };

  // Gérer la complétion d'une action de monétisation
  const handleMonetizationComplete = (response: MonetizationResponse) => {
    if (response.success) {
      setSuccessMessage('Action de monétisation réussie !');
      // Recharger les statistiques et le statut après une action réussie
      if (showRevenueStats) {
        loadRevenueStats();
      }
      loadMonetizationStatus();
      
      // Appeler le callback si fourni
      if (onMonetizationAction && response.monetizationIds) {
        onMonetizationAction('monetize', response.monetizationIds, response);
      }
    } else {
      setError(`Échec de l'action de monétisation : ${response.message}`);
    }
    
    // Masquer les messages après 3 secondes
    setTimeout(() => {
      setSuccessMessage(null);
      setError(null);
    }, 3000);
  };

  // Rendre des badges de statut sur les cartes de vidéo
  const renderVideoWithStats = (video: Video & { revenueStats?: ContentRevenueStats }, isSelected: boolean) => {
    const videoStatus = monetizationStatus[video.id];
    
    // Déterminer les badges à afficher
    const badges = [];
    
    // Badge de statut de monétisation
    if (videoStatus?.isMonetized) {
      badges.push(
        <div key="monetized" className="absolute top-2 left-2 z-10 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
          {videoStatus.type === 'premium' ? 'Premium' : videoStatus.type === 'donation' ? 'Dons' : 'Monétisé'}
        </div>
      );
    }
    
    // Badge de revenus si disponible
    if (video.revenueStats && video.revenueStats.totalRevenue && video.revenueStats.totalRevenue > 0) {
      badges.push(
        <div key="revenue" className="absolute top-10 left-2 z-10 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
          {`${video.revenueStats.totalRevenue} ${video.revenueStats.currency || 'EUR'}`}
        </div>
      );
    }
    
    return (
      <div className="relative">
        {/* Afficher les badges */}
        {badges}
        
        {/* Utiliser le composant de carte de vidéo standard */}
        <div className="relative rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200">
          {/* Vignette de la vidéo */}
          <div className="relative aspect-video">
            <img 
              src={video.thumbnailUrl} 
              alt={video.title} 
              className="w-full h-full object-cover"
            />
            {/* Durée de la vidéo */}
            <span className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded">
              {video.duration}
            </span>
          </div>
          
          {/* Informations sur la vidéo */}
          <div className="p-3">
            <h3 className="font-medium text-gray-800 line-clamp-2">
              {video.title}
            </h3>
            
            <div className="flex items-center mt-2">
              <img 
                src={video.author.avatar} 
                alt={video.author.name} 
                className="w-7 h-7 rounded-full mr-2"
              />
              <span className="text-sm text-gray-600">
                {video.author.name}
              </span>
            </div>
            
            <div className="flex items-center text-xs text-gray-500 mt-2">
              <span>{video.views} vues</span>
              <span className="mx-1">•</span>
              <span>{new Date(video.createdAt).toLocaleDateString()}</span>
            </div>
            
            {/* Statistiques de revenus si disponibles */}
            {video.revenueStats && (
              <div className="mt-2 border-t pt-2 border-gray-100">
                <div className="text-xs text-gray-600 flex justify-between">
                  <span>Revenus: {video.revenueStats.totalRevenue} {video.revenueStats.currency}</span>
                  <span>Transactions: {video.revenueStats.transactionCount}</span>
                </div>
                {video.revenueStats.bySource && Object.keys(video.revenueStats.bySource).length > 0 && (
                  <div className="mt-1 text-xs text-gray-500">
                    {Object.entries(video.revenueStats.bySource).map(([source, amount]) => (
                      <span key={source} className="mr-2">
                        {source}: {amount}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <SelectionProvider<Video> initialMode="none">
      <div className="relative">
        {/* Messages de statut */}
        {isLoading && (
          <div className="absolute top-0 left-0 right-0 bg-blue-100 text-blue-800 p-2 rounded-md mb-4 flex items-center justify-center">
            <Loader2 className="animate-spin mr-2" size={16} />
            <span>Chargement des données...</span>
          </div>
        )}
        
        {error && (
          <div className="absolute top-0 left-0 right-0 bg-red-100 text-red-800 p-2 rounded-md mb-4 flex items-center justify-center">
            <AlertTriangle className="mr-2" size={16} />
            <span>{error}</span>
          </div>
        )}
        
        {successMessage && (
          <div className="absolute top-0 left-0 right-0 bg-green-100 text-green-800 p-2 rounded-md mb-4 flex items-center justify-center">
            <CheckCircle className="mr-2" size={16} />
            <span>{successMessage}</span>
          </div>
        )}
        
        {/* Grille de vidéos avec sélection */}
        <div className="pt-10">
          <VideoGridWithSelection
            videos={videosWithStats}
            className={className}
            columns={columns}
            onVideoClick={onVideoClick}
            selectionActions={[]} // Actions gérées par SelectionMonetizationToolbar
          />
        </div>
        
        {/* Barre d'outils de monétisation */}
        <SelectionMonetizationToolbar<Video>
          contentType="video"
          onMonetizationComplete={handleMonetizationComplete}
          position="bottom"
          fixed={true}
        />
      </div>
    </SelectionProvider>
  );
} 