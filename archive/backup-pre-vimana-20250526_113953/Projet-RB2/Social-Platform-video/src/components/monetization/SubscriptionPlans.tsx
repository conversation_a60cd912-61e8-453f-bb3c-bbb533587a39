import { useState, useEffect } from 'react';
import { Check, Edit, Trash2, Plus, Loader2, X, Save } from 'lucide-react';
import { useMonetizationStore } from '../../store/monetization';
import { SubscriptionPlan } from '../../api/monetizationApi';

interface SubscriptionPlansProps {
  creatorId?: string;
  isCreator?: boolean;
  onSubscribe?: (planId: string) => void;
  className?: string;
}

export function SubscriptionPlans({
  creatorId,
  isCreator = false,
  onSubscribe,
  className = '',
}: SubscriptionPlansProps) {
  const {
    subscriptionPlans,
    creatorSubscriptionPlans,
    isLoading,
    error,
    fetchSubscriptionPlans,
    createPlan,
    updatePlan,
    deletePlan,
    isCreatingPlan,
    isEditingPlan,
    setCreatingPlan,
    setEditingPlan,
  } = useMonetizationStore();
  
  const [newPlan, setNewPlan] = useState<Partial<SubscriptionPlan>>({
    name: '',
    description: '',
    price: 4.99,
    currency: 'USD',
    interval: 'month',
    features: ['Full access to content', 'Exclusive updates'],
    isActive: true,
  });
  
  const [editedPlan, setEditedPlan] = useState<Partial<SubscriptionPlan>>({});
  const [newFeature, setNewFeature] = useState('');
  const [editedNewFeature, setEditedNewFeature] = useState('');
  
  // Fetch subscription plans on mount
  useEffect(() => {
    fetchSubscriptionPlans(creatorId);
  }, [creatorId, fetchSubscriptionPlans]);
  
  // Set edited plan when isEditingPlan changes
  useEffect(() => {
    if (isEditingPlan) {
      setEditedPlan({ ...isEditingPlan });
    } else {
      setEditedPlan({});
    }
  }, [isEditingPlan]);
  
  // Get the appropriate plans based on whether we're viewing as creator or subscriber
  const plans = creatorId ? creatorSubscriptionPlans : subscriptionPlans;
  
  // Handle adding a new feature to the plan
  const handleAddFeature = () => {
    if (newFeature.trim()) {
      setNewPlan({
        ...newPlan,
        features: [...(newPlan.features || []), newFeature.trim()],
      });
      setNewFeature('');
    }
  };
  
  // Handle adding a new feature to the edited plan
  const handleAddEditedFeature = () => {
    if (editedNewFeature.trim()) {
      setEditedPlan({
        ...editedPlan,
        features: [...(editedPlan.features || []), editedNewFeature.trim()],
      });
      setEditedNewFeature('');
    }
  };
  
  // Handle removing a feature from the plan
  const handleRemoveFeature = (index: number) => {
    const updatedFeatures = [...(newPlan.features || [])];
    updatedFeatures.splice(index, 1);
    setNewPlan({
      ...newPlan,
      features: updatedFeatures,
    });
  };
  
  // Handle removing a feature from the edited plan
  const handleRemoveEditedFeature = (index: number) => {
    const updatedFeatures = [...(editedPlan.features || [])];
    updatedFeatures.splice(index, 1);
    setEditedPlan({
      ...editedPlan,
      features: updatedFeatures,
    });
  };
  
  // Handle creating a new plan
  const handleCreatePlan = () => {
    if (
      newPlan.name &&
      newPlan.description &&
      newPlan.price !== undefined &&
      newPlan.currency &&
      newPlan.interval
    ) {
      createPlan(newPlan as any);
      setNewPlan({
        name: '',
        description: '',
        price: 4.99,
        currency: 'USD',
        interval: 'month',
        features: ['Full access to content', 'Exclusive updates'],
        isActive: true,
      });
    }
  };
  
  // Handle updating a plan
  const handleUpdatePlan = () => {
    if (isEditingPlan && editedPlan.id) {
      updatePlan(editedPlan.id, editedPlan);
    }
  };
  
  // Format price with currency symbol
  const formatPrice = (price: number, currency: string) => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    });
    
    return formatter.format(price);
  };
  
  if (isLoading && plans.length === 0) {
    return (
      <div className={`flex justify-center items-center h-40 ${className}`}>
        <Loader2 size={24} className="animate-spin text-green-500" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={`p-4 text-center text-red-500 ${className}`}>
        <p>Error: {error}</p>
        <button
          onClick={() => fetchSubscriptionPlans(creatorId)}
          className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          {isCreator ? 'Your Subscription Plans' : 'Subscription Plans'}
        </h2>
        
        {isCreator && (
          <button
            onClick={() => setCreatingPlan(!isCreatingPlan)}
            className="px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center"
          >
            {isCreatingPlan ? (
              <>
                <X size={16} className="mr-1" />
                Cancel
              </>
            ) : (
              <>
                <Plus size={16} className="mr-1" />
                New Plan
              </>
            )}
          </button>
        )}
      </div>
      
      {/* Create New Plan Form */}
      {isCreator && isCreatingPlan && (
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-4">
          <h3 className="text-lg font-medium mb-4">Create New Subscription Plan</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Plan Name</label>
              <input
                type="text"
                value={newPlan.name}
                onChange={(e) => setNewPlan({ ...newPlan, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="e.g. Basic, Premium, VIP"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={newPlan.description}
                onChange={(e) => setNewPlan({ ...newPlan, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Describe what subscribers will get"
                rows={3}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
                <input
                  type="number"
                  value={newPlan.price}
                  onChange={(e) => setNewPlan({ ...newPlan, price: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  min="0.99"
                  step="0.01"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                <select
                  value={newPlan.currency}
                  onChange={(e) => setNewPlan({ ...newPlan, currency: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="CAD">CAD</option>
                  <option value="AUD">AUD</option>
                </select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Billing Interval</label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={newPlan.interval === 'month'}
                    onChange={() => setNewPlan({ ...newPlan, interval: 'month' })}
                    className="h-4 w-4 text-green-500 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Monthly</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={newPlan.interval === 'year'}
                    onChange={() => setNewPlan({ ...newPlan, interval: 'year' })}
                    className="h-4 w-4 text-green-500 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Yearly</span>
                </label>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Features</label>
              <div className="space-y-2">
                {newPlan.features?.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <Check size={16} className="text-green-500 mr-2 flex-shrink-0" />
                    <span className="text-sm flex-1">{feature}</span>
                    <button
                      onClick={() => handleRemoveFeature(index)}
                      className="text-gray-400 hover:text-red-500"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))}
                
                <div className="flex mt-2">
                  <input
                    type="text"
                    value={newFeature}
                    onChange={(e) => setNewFeature(e.target.value)}
                    className="flex-1 px-3 py-1.5 text-sm border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="Add a feature"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddFeature();
                      }
                    }}
                  />
                  <button
                    onClick={handleAddFeature}
                    className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-r-md border border-gray-300 border-l-0 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <Plus size={16} />
                  </button>
                </div>
              </div>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={newPlan.isActive}
                onChange={(e) => setNewPlan({ ...newPlan, isActive: e.target.checked })}
                className="h-4 w-4 text-green-500 focus:ring-green-500 rounded"
              />
              <label className="ml-2 text-sm text-gray-700">
                Make this plan active and available for subscription
              </label>
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={() => setCreatingPlan(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md mr-2 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Cancel
              </button>
              
              <button
                onClick={handleCreatePlan}
                disabled={!newPlan.name || !newPlan.description || newPlan.price === undefined}
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
              >
                <Save size={16} className="mr-1" />
                Create Plan
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Edit Plan Form */}
      {isCreator && isEditingPlan && (
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-4">
          <h3 className="text-lg font-medium mb-4">Edit Subscription Plan</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Plan Name</label>
              <input
                type="text"
                value={editedPlan.name || ''}
                onChange={(e) => setEditedPlan({ ...editedPlan, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={editedPlan.description || ''}
                onChange={(e) => setEditedPlan({ ...editedPlan, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                rows={3}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
                <input
                  type="number"
                  value={editedPlan.price || 0}
                  onChange={(e) => setEditedPlan({ ...editedPlan, price: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  min="0.99"
                  step="0.01"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                <select
                  value={editedPlan.currency || 'USD'}
                  onChange={(e) => setEditedPlan({ ...editedPlan, currency: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="CAD">CAD</option>
                  <option value="AUD">AUD</option>
                </select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Billing Interval</label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={editedPlan.interval === 'month'}
                    onChange={() => setEditedPlan({ ...editedPlan, interval: 'month' })}
                    className="h-4 w-4 text-green-500 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Monthly</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={editedPlan.interval === 'year'}
                    onChange={() => setEditedPlan({ ...editedPlan, interval: 'year' })}
                    className="h-4 w-4 text-green-500 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Yearly</span>
                </label>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Features</label>
              <div className="space-y-2">
                {editedPlan.features?.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <Check size={16} className="text-green-500 mr-2 flex-shrink-0" />
                    <span className="text-sm flex-1">{feature}</span>
                    <button
                      onClick={() => handleRemoveEditedFeature(index)}
                      className="text-gray-400 hover:text-red-500"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))}
                
                <div className="flex mt-2">
                  <input
                    type="text"
                    value={editedNewFeature}
                    onChange={(e) => setEditedNewFeature(e.target.value)}
                    className="flex-1 px-3 py-1.5 text-sm border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="Add a feature"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddEditedFeature();
                      }
                    }}
                  />
                  <button
                    onClick={handleAddEditedFeature}
                    className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-r-md border border-gray-300 border-l-0 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <Plus size={16} />
                  </button>
                </div>
              </div>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={editedPlan.isActive}
                onChange={(e) => setEditedPlan({ ...editedPlan, isActive: e.target.checked })}
                className="h-4 w-4 text-green-500 focus:ring-green-500 rounded"
              />
              <label className="ml-2 text-sm text-gray-700">
                Make this plan active and available for subscription
              </label>
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={() => setEditingPlan(null)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md mr-2 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Cancel
              </button>
              
              <button
                onClick={handleUpdatePlan}
                disabled={!editedPlan.name || !editedPlan.description || editedPlan.price === undefined}
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
              >
                <Save size={16} className="mr-1" />
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Subscription Plans List */}
      {plans.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-gray-500">
            {isCreator
              ? 'You haven\'t created any subscription plans yet.'
              : 'No subscription plans available.'}
          </p>
          {isCreator && !isCreatingPlan && (
            <button
              onClick={() => setCreatingPlan(true)}
              className="mt-4 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Create Your First Plan
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`bg-white rounded-lg shadow-md border ${
                plan.isActive ? 'border-green-200' : 'border-gray-200'
              } overflow-hidden`}
            >
              <div className="p-6">
                <div className="flex justify-between items-start">
                  <h3 className="text-lg font-semibold">{plan.name}</h3>
                  {!plan.isActive && (
                    <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">
                      Inactive
                    </span>
                  )}
                </div>
                
                <div className="mt-2 text-sm text-gray-600">{plan.description}</div>
                
                <div className="mt-4">
                  <span className="text-2xl font-bold">
                    {formatPrice(plan.price, plan.currency)}
                  </span>
                  <span className="text-gray-500 ml-1">
                    /{plan.interval === 'month' ? 'mo' : 'yr'}
                  </span>
                </div>
                
                <div className="mt-4 space-y-2">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-start">
                      <Check size={16} className="text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
                
                <div className="mt-6">
                  {isCreator ? (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingPlan(plan)}
                        className="flex-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center justify-center"
                      >
                        <Edit size={16} className="mr-1" />
                        Edit
                      </button>
                      
                      <button
                        onClick={() => deletePlan(plan.id)}
                        className="flex-1 px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center justify-center"
                      >
                        <Trash2 size={16} className="mr-1" />
                        Delete
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => onSubscribe && onSubscribe(plan.id)}
                      disabled={!plan.isActive}
                      className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Subscribe
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
