import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>hart, 
  Line, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>ltip, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { 
  DollarSign, 
  Users, 
  Calendar, 
  ArrowUp, 
  ArrowDown, 
  Download, 
  RefreshCw, 
  CreditCard, 
  ChevronDown,
  Loader2
} from 'lucide-react';
import { useMonetizationStore } from '../../store/monetization';

interface CreatorDashboardProps {
  className?: string;
}

export function CreatorDashboard({ className = '' }: CreatorDashboardProps) {
  const {
    earningsSummary,
    subscribers,
    subscribersCount,
    transactions,
    isLoading,
    error,
    fetchEarningsSummary,
    fetchSubscribers,
    fetchTransactions,
    requestPayout,
  } = useMonetizationStore();
  
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year' | 'all'>('month');
  const [showTimeRangeDropdown, setShowTimeRangeDropdown] = useState(false);
  const [showPayoutModal, setShowPayoutModal] = useState(false);
  const [payoutAmount, setPayoutAmount] = useState(0);
  const [payoutCurrency, setPayoutCurrency] = useState('USD');
  const [payoutMethodId, setPayoutMethodId] = useState('');
  const [isRequestingPayout, setIsRequestingPayout] = useState(false);
  
  // Fetch data on mount and when time range changes
  useEffect(() => {
    fetchEarningsSummary(timeRange);
    fetchSubscribers();
    fetchTransactions();
  }, [timeRange, fetchEarningsSummary, fetchSubscribers, fetchTransactions]);
  
  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };
  
  // Handle requesting a payout
  const handleRequestPayout = async () => {
    if (!payoutMethodId || payoutAmount <= 0) return;
    
    setIsRequestingPayout(true);
    
    try {
      await requestPayout(payoutAmount, payoutCurrency, payoutMethodId);
      setShowPayoutModal(false);
    } catch (error) {
      console.error('Error requesting payout:', error);
    } finally {
      setIsRequestingPayout(false);
    }
  };
  
  // Colors for charts
  const COLORS = ['#10B981', '#3B82F6', '#EC4899', '#F59E0B', '#8B5CF6'];
  
  // Prepare data for earnings by source pie chart
  const getEarningsBySourceData = () => {
    if (!earningsSummary) return [];
    
    return [
      { name: 'Subscriptions', value: earningsSummary.subscriptionEarnings },
      { name: 'Tips', value: earningsSummary.tipEarnings },
      { name: 'Content Purchases', value: earningsSummary.contentPurchaseEarnings },
    ].filter((item) => item.value > 0);
  };
  
  if (isLoading && !earningsSummary) {
    return (
      <div className={`flex justify-center items-center h-64 ${className}`}>
        <Loader2 size={32} className="animate-spin text-blue-500" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={`p-8 text-center text-red-500 ${className}`}>
        <p>Error: {error}</p>
        <button
          onClick={() => {
            fetchEarningsSummary(timeRange);
            fetchSubscribers();
            fetchTransactions();
          }}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Time Range Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Creator Dashboard</h2>
        
        <div className="flex space-x-2">
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowTimeRangeDropdown(!showTimeRangeDropdown);
              }}
              className="px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none flex items-center"
            >
              <Calendar size={14} className="mr-1" />
              {timeRange === 'week' ? 'This Week' : 
               timeRange === 'month' ? 'This Month' : 
               timeRange === 'year' ? 'This Year' : 'All Time'}
              <ChevronDown size={14} className="ml-1" />
            </button>
            
            {showTimeRangeDropdown && (
              <div className="absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <div className="py-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setTimeRange('week');
                      setShowTimeRangeDropdown(false);
                    }}
                    className={`w-full text-left px-4 py-2 text-sm ${
                      timeRange === 'week' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    This Week
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setTimeRange('month');
                      setShowTimeRangeDropdown(false);
                    }}
                    className={`w-full text-left px-4 py-2 text-sm ${
                      timeRange === 'month' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    This Month
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setTimeRange('year');
                      setShowTimeRangeDropdown(false);
                    }}
                    className={`w-full text-left px-4 py-2 text-sm ${
                      timeRange === 'year' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    This Year
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setTimeRange('all');
                      setShowTimeRangeDropdown(false);
                    }}
                    className={`w-full text-left px-4 py-2 text-sm ${
                      timeRange === 'all' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    All Time
                  </button>
                </div>
              </div>
            )}
          </div>
          
          <button
            onClick={() => fetchEarningsSummary(timeRange)}
            className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
            title="Refresh data"
          >
            <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
          </button>
          
          <button
            onClick={() => {
              // Download earnings data as CSV
              if (!earningsSummary) return;
              
              const headers = ['Month', 'Earnings'];
              const rows = earningsSummary.earningsByMonth.map((item) => [
                item.month,
                item.earnings.toString(),
              ]);
              
              const csvContent = [
                headers.join(','),
                ...rows.map((row) => row.join(',')),
              ].join('\n');
              
              const blob = new Blob([csvContent], { type: 'text/csv' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `earnings_${timeRange}.csv`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            }}
            className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
            title="Download data"
          >
            <Download size={16} />
          </button>
        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Earnings */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 flex items-center">
                <DollarSign size={16} className="mr-1" />
                Total Earnings
              </p>
              <h3 className="text-2xl font-bold mt-1">
                {earningsSummary ? formatCurrency(earningsSummary.totalEarnings, earningsSummary.currency) : '-'}
              </h3>
            </div>
          </div>
        </div>
        
        {/* Pending Payout */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 flex items-center">
                <CreditCard size={16} className="mr-1" />
                Pending Payout
              </p>
              <h3 className="text-2xl font-bold mt-1">
                {earningsSummary ? formatCurrency(earningsSummary.pendingPayout, earningsSummary.currency) : '-'}
              </h3>
            </div>
          </div>
          {earningsSummary && earningsSummary.pendingPayout > 0 && (
            <button
              onClick={() => {
                setPayoutAmount(earningsSummary.pendingPayout);
                setPayoutCurrency(earningsSummary.currency);
                setShowPayoutModal(true);
              }}
              className="mt-2 px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Request Payout
            </button>
          )}
        </div>
        
        {/* Last Payout */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 flex items-center">
                <Calendar size={16} className="mr-1" />
                Last Payout
              </p>
              <h3 className="text-2xl font-bold mt-1">
                {earningsSummary ? formatCurrency(earningsSummary.lastPayout, earningsSummary.currency) : '-'}
              </h3>
            </div>
          </div>
          {earningsSummary && earningsSummary.lastPayoutDate && (
            <p className="text-xs text-gray-500 mt-1">
              {formatDate(earningsSummary.lastPayoutDate)}
            </p>
          )}
        </div>
        
        {/* Subscribers */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 flex items-center">
                <Users size={16} className="mr-1" />
                Subscribers
              </p>
              <h3 className="text-2xl font-bold mt-1">
                {subscribersCount || 0}
              </h3>
            </div>
          </div>
        </div>
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Earnings Over Time */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-semibold mb-4">Earnings Over Time</h3>
          
          {earningsSummary && earningsSummary.earningsByMonth.length > 0 ? (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={earningsSummary.earningsByMonth}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis 
                    dataKey="month" 
                    tick={{ fontSize: 12 }} 
                    axisLine={false}
                    tickLine={false}
                  />
                  <YAxis 
                    tickFormatter={(value) => formatCurrency(value, earningsSummary.currency).split('.')[0]} 
                    tick={{ fontSize: 12 }} 
                    axisLine={false}
                    tickLine={false}
                  />
                  <Tooltip 
                    formatter={(value: number) => [formatCurrency(value, earningsSummary.currency), 'Earnings']}
                  />
                  <Bar 
                    dataKey="earnings" 
                    fill="#10B981" 
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="flex justify-center items-center h-64 text-gray-500">
              <p>No earnings data available for the selected time range.</p>
            </div>
          )}
        </div>
        
        {/* Earnings by Source */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-semibold mb-4">Earnings by Source</h3>
          
          {earningsSummary && getEarningsBySourceData().length > 0 ? (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={getEarningsBySourceData()}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {getEarningsBySourceData().map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: number) => [formatCurrency(value, earningsSummary.currency), 'Earnings']}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="flex justify-center items-center h-64 text-gray-500">
              <p>No earnings data available for the selected time range.</p>
            </div>
          )}
        </div>
      </div>
      
      {/* Recent Transactions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 className="text-lg font-semibold mb-4">Recent Transactions</h3>
        
        {transactions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No transactions available.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    From/To
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.slice(0, 5).map((transaction) => (
                  <tr key={transaction.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(transaction.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize">
                      {transaction.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {transaction.description}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transaction.type === 'payout' ? 'To You' : 
                       transaction.fromUserName ? `From ${transaction.fromUserName}` : 
                       transaction.toUserName ? `To ${transaction.toUserName}` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                      <span className={transaction.type === 'payout' ? 'text-red-500' : 'text-green-500'}>
                        {transaction.type === 'payout' ? '-' : '+'}
                        {formatCurrency(transaction.amount, transaction.currency)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        transaction.status === 'completed' ? 'bg-green-100 text-green-800' :
                        transaction.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        transaction.status === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {transaction.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* Payout Modal */}
      {showPayoutModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            <div className="p-4 border-b">
              <h3 className="text-lg font-semibold">Request Payout</h3>
            </div>
            
            <div className="p-4">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payout Amount
                </label>
                <div className="flex">
                  <div className="flex-shrink-0 inline-flex items-center px-3 py-2 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 rounded-l-md">
                    {payoutCurrency === 'USD' ? '$' : payoutCurrency === 'EUR' ? '€' : '£'}
                  </div>
                  <input
                    type="number"
                    value={payoutAmount}
                    onChange={(e) => setPayoutAmount(Math.max(0, parseFloat(e.target.value) || 0))}
                    className="flex-1 min-w-0 block w-full px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    min="0"
                    step="0.01"
                    max={earningsSummary?.pendingPayout || 0}
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Available: {earningsSummary ? formatCurrency(earningsSummary.pendingPayout, earningsSummary.currency) : '-'}
                </p>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payout Method
                </label>
                <select
                  value={payoutMethodId}
                  onChange={(e) => setPayoutMethodId(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a payout method</option>
                  <option value="method1">Bank Account (ending in 1234)</option>
                  <option value="method2">PayPal (<EMAIL>)</option>
                </select>
              </div>
            </div>
            
            <div className="p-4 border-t flex justify-end">
              <button
                onClick={() => setShowPayoutModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md mr-2 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Cancel
              </button>
              
              <button
                onClick={handleRequestPayout}
                disabled={!payoutMethodId || payoutAmount <= 0 || payoutAmount > (earningsSummary?.pendingPayout || 0) || isRequestingPayout}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
              >
                {isRequestingPayout ? (
                  <>
                    <Loader2 size={16} className="animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <DollarSign size={16} className="mr-2" />
                    Request Payout
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
