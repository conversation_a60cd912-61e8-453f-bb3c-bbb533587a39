import { useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';
import { StoryThumbnail } from './StoryThumbnail';
import { useStoriesStore } from '../../store/stories';

interface StoriesCarouselProps {
  className?: string;
  showCreateButton?: boolean;
  onCreateClick?: () => void;
}

export function StoriesCarousel({
  className = '',
  showCreateButton = true,
  onCreateClick,
}: StoriesCarouselProps) {
  const { storiesFeed, isLoading, error, fetchStoriesFeed } = useStoriesStore();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    fetchStoriesFeed();
  }, [fetchStoriesFeed]);
  
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -200,
        behavior: 'smooth',
      });
    }
  };
  
  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 200,
        behavior: 'smooth',
      });
    }
  };
  
  if (isLoading && storiesFeed.length === 0) {
    return (
      <div className={`flex justify-center items-center h-28 ${className}`}>
        <Loader2 size={24} className="animate-spin text-green-500" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={`p-4 text-center text-red-500 ${className}`}>
        <p>Error: {error}</p>
        <button
          onClick={() => fetchStoriesFeed()}
          className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  if (storiesFeed.length === 0 && !showCreateButton) {
    return null;
  }
  
  return (
    <div className={`relative ${className}`}>
      {/* Scroll buttons */}
      {storiesFeed.length > 0 && (
        <>
          <button
            onClick={scrollLeft}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-1 shadow-md text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Scroll left"
          >
            <ChevronLeft size={20} />
          </button>
          
          <button
            onClick={scrollRight}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-1 shadow-md text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            aria-label="Scroll right"
          >
            <ChevronRight size={20} />
          </button>
        </>
      )}
      
      {/* Stories carousel */}
      <div
        ref={scrollContainerRef}
        className="flex space-x-4 overflow-x-auto py-2 px-2 scrollbar-hide"
      >
        {/* Create story button */}
        {showCreateButton && (
          <div className="flex-shrink-0">
            <StoryThumbnail isCreateButton onCreateClick={onCreateClick} />
          </div>
        )}
        
        {/* Story thumbnails */}
        {storiesFeed.map((story) => (
          <div key={story.id} className="flex-shrink-0">
            <StoryThumbnail story={story} />
          </div>
        ))}
      </div>
    </div>
  );
}
