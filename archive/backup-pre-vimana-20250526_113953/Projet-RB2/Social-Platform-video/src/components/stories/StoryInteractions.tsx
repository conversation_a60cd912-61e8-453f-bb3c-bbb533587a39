import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, MessageCircle, Send, ThumbsUp, ThumbsDown, HelpCircle, Vote } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Progress } from '../ui/progress';
import { Avatar } from '../ui/avatar';

interface PollOption {
  id: string;
  text: string;
  votes: number;
}

interface Poll {
  id: string;
  question: string;
  options: PollOption[];
  totalVotes: number;
  expiresAt: string;
  hasVoted: boolean;
}

interface Question {
  id: string;
  text: string;
  responses: Array<{
    id: string;
    userId: string;
    userName: string;
    userAvatar: string;
    text: string;
    createdAt: string;
  }>;
}

interface StoryInteractionsProps {
  storyId: string;
  storyItemId: string;
  onClose?: () => void;
}

export function StoryInteractions({ storyId, storyItemId, onClose }: StoryInteractionsProps) {
  const [activeTab, setActiveTab] = useState<'poll' | 'question' | 'reactions'>('poll');
  const [poll, setPoll] = useState<Poll | null>(null);
  const [question, setQuestion] = useState<Question | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [responseText, setResponseText] = useState('');
  const [showResponses, setShowResponses] = useState(false);
  
  // Fetch poll and question data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // In a real app, you would fetch this data from the API
        // For now, we'll use mock data
        
        // Mock poll data
        const mockPoll: Poll = {
          id: 'poll1',
          question: 'What wellness practice do you prefer?',
          options: [
            { id: 'option1', text: 'Yoga', votes: 42 },
            { id: 'option2', text: 'Meditation', votes: 28 },
            { id: 'option3', text: 'Breathwork', votes: 15 },
            { id: 'option4', text: 'Journaling', votes: 10 },
          ],
          totalVotes: 95,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
          hasVoted: false,
        };
        
        // Mock question data
        const mockQuestion: Question = {
          id: 'question1',
          text: 'What\'s your favorite morning routine for wellness?',
          responses: [
            {
              id: 'response1',
              userId: 'user1',
              userName: 'Emma Wilson',
              userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
              text: 'I love starting with 10 minutes of meditation followed by a short yoga flow!',
              createdAt: new Date(Date.now() - 35 * 60 * 1000).toISOString(), // 35 minutes ago
            },
            {
              id: 'response2',
              userId: 'user2',
              userName: 'Michael Chen',
              userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
              text: 'Cold shower and 5 minutes of breathwork changed my life!',
              createdAt: new Date(Date.now() - 22 * 60 * 1000).toISOString(), // 22 minutes ago
            },
            {
              id: 'response3',
              userId: 'user3',
              userName: 'Sophia Rodriguez',
              userAvatar: 'https://randomuser.me/api/portraits/women/68.jpg',
              text: 'Journaling and a cup of herbal tea before checking my phone',
              createdAt: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago
            },
          ],
        };
        
        setPoll(mockPoll);
        setQuestion(mockQuestion);
      } catch (error) {
        console.error('Error fetching story interactions:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [storyId, storyItemId]);
  
  // Handle voting on a poll
  const handleVote = (optionId: string) => {
    if (!poll || poll.hasVoted) return;
    
    // In a real app, you would send this to the API
    const updatedOptions = poll.options.map(option => {
      if (option.id === optionId) {
        return { ...option, votes: option.votes + 1 };
      }
      return option;
    });
    
    setPoll({
      ...poll,
      options: updatedOptions,
      totalVotes: poll.totalVotes + 1,
      hasVoted: true,
    });
    
    setSelectedOption(optionId);
  };
  
  // Handle submitting a response to a question
  const handleSubmitResponse = () => {
    if (!question || !responseText.trim()) return;
    
    // In a real app, you would send this to the API
    const newResponse = {
      id: `response-${Date.now()}`,
      userId: 'current-user', // This would be the actual user ID
      userName: 'You', // This would be the actual user name
      userAvatar: 'https://randomuser.me/api/portraits/lego/1.jpg', // This would be the actual user avatar
      text: responseText,
      createdAt: new Date().toISOString(),
    };
    
    setQuestion({
      ...question,
      responses: [...question.responses, newResponse],
    });
    
    setResponseText('');
    setShowResponses(true);
  };
  
  // Format time
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffMinutes = Math.floor(diffTime / (1000 * 60));
    
    if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else if (diffMinutes < 1440) {
      return `${Math.floor(diffMinutes / 60)}h ago`;
    } else {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
      }).format(date);
    }
  };
  
  // Calculate poll percentage
  const calculatePercentage = (votes: number) => {
    if (!poll || poll.totalVotes === 0) return 0;
    return Math.round((votes / poll.totalVotes) * 100);
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
      </div>
    );
  }
  
  return (
    <div className="bg-white rounded-lg shadow-lg max-w-md w-full mx-auto overflow-hidden">
      {/* Tabs */}
      <div className="flex border-b">
        <button
          onClick={() => setActiveTab('poll')}
          className={`flex-1 py-3 text-sm font-medium flex items-center justify-center ${
            activeTab === 'poll'
              ? 'text-green-500 border-b-2 border-green-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <BarChart size={16} className="mr-2" />
          Poll
        </button>
        
        <button
          onClick={() => setActiveTab('question')}
          className={`flex-1 py-3 text-sm font-medium flex items-center justify-center ${
            activeTab === 'question'
              ? 'text-green-500 border-b-2 border-green-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <HelpCircle size={16} className="mr-2" />
          Question
        </button>
        
        <button
          onClick={() => setActiveTab('reactions')}
          className={`flex-1 py-3 text-sm font-medium flex items-center justify-center ${
            activeTab === 'reactions'
              ? 'text-green-500 border-b-2 border-green-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <ThumbsUp size={16} className="mr-2" />
          Reactions
        </button>
      </div>
      
      {/* Content */}
      <div className="p-4">
        {activeTab === 'poll' && poll && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{poll.question}</h3>
            
            <div className="space-y-3">
              {poll.options.map((option) => (
                <div key={option.id} className="space-y-1">
                  <button
                    onClick={() => handleVote(option.id)}
                    disabled={poll.hasVoted}
                    className={`w-full p-3 rounded-md text-left flex justify-between items-center ${
                      poll.hasVoted
                        ? 'bg-gray-100'
                        : 'bg-gray-50 hover:bg-gray-100'
                    } ${
                      selectedOption === option.id
                        ? 'ring-2 ring-green-500'
                        : ''
                    }`}
                  >
                    <span>{option.text}</span>
                    {poll.hasVoted && (
                      <span className="text-sm font-medium">
                        {calculatePercentage(option.votes)}%
                      </span>
                    )}
                  </button>
                  
                  {poll.hasVoted && (
                    <Progress value={calculatePercentage(option.votes)} className="h-1" />
                  )}
                </div>
              ))}
            </div>
            
            <div className="text-sm text-gray-500 flex justify-between">
              <span>{poll.totalVotes} votes</span>
              <span>Expires in {formatTime(poll.expiresAt)}</span>
            </div>
          </div>
        )}
        
        {activeTab === 'question' && question && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{question.text}</h3>
            
            {!showResponses ? (
              <div className="space-y-3">
                <div className="relative">
                  <Input
                    value={responseText}
                    onChange={(e) => setResponseText(e.target.value)}
                    placeholder="Type your answer..."
                    className="pr-10"
                  />
                  <button
                    onClick={handleSubmitResponse}
                    disabled={!responseText.trim()}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-green-500 disabled:text-gray-300"
                  >
                    <Send size={16} />
                  </button>
                </div>
                
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowResponses(true)}
                    className="text-xs"
                  >
                    See {question.responses.length} responses
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onClose && onClose()}
                    className="text-xs"
                  >
                    Skip
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="max-h-60 overflow-y-auto space-y-3">
                  {question.responses.map((response) => (
                    <div key={response.id} className="flex space-x-3">
                      <Avatar src={response.userAvatar} alt={response.userName} className="w-8 h-8" />
                      <div className="flex-1">
                        <div className="flex items-center">
                          <span className="font-medium text-sm">{response.userName}</span>
                          <span className="text-xs text-gray-500 ml-2">
                            {formatTime(response.createdAt)}
                          </span>
                        </div>
                        <p className="text-sm">{response.text}</p>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="relative">
                  <Input
                    value={responseText}
                    onChange={(e) => setResponseText(e.target.value)}
                    placeholder="Add another response..."
                    className="pr-10"
                  />
                  <button
                    onClick={handleSubmitResponse}
                    disabled={!responseText.trim()}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-green-500 disabled:text-gray-300"
                  >
                    <Send size={16} />
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'reactions' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">React to this story</h3>
            
            <div className="grid grid-cols-4 gap-2">
              {['❤️', '😂', '😮', '👏', '🔥', '💯', '🙏', '😍'].map((emoji) => (
                <button
                  key={emoji}
                  onClick={() => {
                    // In a real app, you would send this to the API
                    console.log('Reacted with:', emoji);
                    onClose && onClose();
                  }}
                  className="p-3 text-2xl bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                >
                  {emoji}
                </button>
              ))}
            </div>
            
            <div className="flex justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // In a real app, you would open the direct message
                  console.log('Open direct message');
                }}
                className="text-xs flex items-center"
              >
                <MessageCircle size={14} className="mr-1" />
                Send message
              </Button>
              
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // In a real app, you would send this to the API
                    console.log('Disliked story');
                    onClose && onClose();
                  }}
                  className="text-xs"
                >
                  <ThumbsDown size={14} />
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // In a real app, you would send this to the API
                    console.log('Liked story');
                    onClose && onClose();
                  }}
                  className="text-xs"
                >
                  <ThumbsUp size={14} />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
