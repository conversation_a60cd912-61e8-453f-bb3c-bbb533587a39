import { useState, useEffect, useRef } from 'react';
import { X, Pause, Play, ChevronLeft, ChevronRight, Heart, MessageCircle, Send, MoreHorizontal, BarChart, HelpCircle, Eye } from 'lucide-react';
import { useStoriesStore } from '../../store/stories';
import { Avatar } from '../ui/avatar';
import { StoryInteractions } from './StoryInteractions';

interface StoryViewerProps {
  onClose?: () => void;
}

export function StoryViewer({ onClose }: StoryViewerProps) {
  const {
    currentStory,
    currentItemIndex,
    isViewerOpen,
    isPaused,
    closeStory,
    nextItem,
    prevItem,
    nextStory,
    prevStory,
    pauseStory,
    resumeStory,
    markAsSeen,
  } = useStoriesStore();

  const [progress, setProgress] = useState(0);
  const [inputText, setInputText] = useState('');
  const [showReactions, setShowReactions] = useState(false);
  const [showInteractions, setShowInteractions] = useState<'poll' | 'question' | 'reactions' | null>(null);
  const [viewCount, setViewCount] = useState(0);
  const [showViewers, setShowViewers] = useState(false);

  const videoRef = useRef<HTMLVideoElement>(null);
  const progressIntervalRef = useRef<number | null>(null);
  const progressTimeoutRef = useRef<number | null>(null);

  const currentItem = currentStory?.items[currentItemIndex];
  const isVideo = currentItem?.type === 'video';
  const duration = isVideo ? currentItem?.duration || 10 : 5; // Default 5 seconds for images, 10 for videos

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffMinutes = Math.floor(diffTime / (1000 * 60));

    if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else if (diffMinutes < 1440) {
      return `${Math.floor(diffMinutes / 60)}h ago`;
    } else {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
      }).format(date);
    }
  };

  // Handle story progress
  useEffect(() => {
    if (!isViewerOpen || !currentItem || isPaused) return;

    // Reset progress when changing items
    setProgress(0);

    // Clear any existing intervals and timeouts
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    if (progressTimeoutRef.current) {
      clearTimeout(progressTimeoutRef.current);
    }

    // For videos, progress is handled by the video element
    if (isVideo && videoRef.current) {
      videoRef.current.currentTime = 0;
      videoRef.current.play();

      // Update progress based on video currentTime
      progressIntervalRef.current = window.setInterval(() => {
        if (videoRef.current) {
          const progress = (videoRef.current.currentTime / duration) * 100;
          setProgress(progress);
        }
      }, 100);
    } else {
      // For images, use a timer
      const interval = 100; // Update progress every 100ms
      const steps = duration * 1000 / interval;
      let step = 0;

      progressIntervalRef.current = window.setInterval(() => {
        step++;
        const newProgress = (step / steps) * 100;
        setProgress(newProgress);
      }, interval);

      // Move to next item after duration
      progressTimeoutRef.current = window.setTimeout(() => {
        nextItem();
      }, duration * 1000);
    }

    // Mark the current item as seen
    if (currentItem && !currentItem.seen) {
      markAsSeen(currentItem.id);
    }

    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }

      if (progressTimeoutRef.current) {
        clearTimeout(progressTimeoutRef.current);
      }
    };
  }, [currentItem, isViewerOpen, isPaused, isVideo, duration]);

  // Simulate view count
  useEffect(() => {
    if (!currentItem) return;

    // In a real app, you would fetch the view count from the API
    // For now, we'll simulate a random view count
    const baseCount = Math.floor(Math.random() * 50) + 10;
    setViewCount(baseCount);

    // Simulate view count increasing over time
    const interval = setInterval(() => {
      setViewCount(prev => prev + Math.floor(Math.random() * 3));
    }, 5000);

    return () => clearInterval(interval);
  }, [currentItem]);

  // Handle video ended event
  const handleVideoEnded = () => {
    nextItem();
  };

  // Handle click on the left side to go to previous item/story
  const handleLeftClick = (e: React.MouseEvent) => {
    // Only handle clicks on the left third of the screen
    const { clientX, currentTarget } = e;
    const { left, width } = currentTarget.getBoundingClientRect();
    const relativeX = clientX - left;

    if (relativeX < width / 3) {
      prevItem();
    }
  };

  // Handle click on the right side to go to next item/story
  const handleRightClick = (e: React.MouseEvent) => {
    // Only handle clicks on the right third of the screen
    const { clientX, currentTarget } = e;
    const { left, width } = currentTarget.getBoundingClientRect();
    const relativeX = clientX - left;

    if (relativeX > (width * 2) / 3) {
      nextItem();
    }
  };

  // Handle pause/resume on click in the middle
  const handleMiddleClick = (e: React.MouseEvent) => {
    // Only handle clicks in the middle third of the screen
    const { clientX, currentTarget } = e;
    const { left, width } = currentTarget.getBoundingClientRect();
    const relativeX = clientX - left;

    if (relativeX >= width / 3 && relativeX <= (width * 2) / 3) {
      if (isPaused) {
        resumeStory();
        if (isVideo && videoRef.current) {
          videoRef.current.play();
        }
      } else {
        pauseStory();
        if (isVideo && videoRef.current) {
          videoRef.current.pause();
        }
      }
    }
  };

  // Handle sending a message
  const handleSendMessage = () => {
    if (inputText.trim()) {
      // In a real app, you would send the message to the API
      console.log('Sending message:', inputText);
      setInputText('');
    }
  };

  // Handle adding a reaction
  const handleAddReaction = (reaction: string) => {
    if (currentItem) {
      // In a real app, you would send the reaction to the API
      console.log('Adding reaction:', reaction, 'to story:', currentItem.id);
      setShowReactions(false);
    }
  };

  // Handle opening poll
  const handleOpenPoll = () => {
    pauseStory();
    setShowInteractions('poll');
  };

  // Handle opening question
  const handleOpenQuestion = () => {
    pauseStory();
    setShowInteractions('question');
  };

  // Handle opening reactions
  const handleOpenReactions = () => {
    pauseStory();
    setShowInteractions('reactions');
  };

  // Handle closing interactions
  const handleCloseInteractions = () => {
    setShowInteractions(null);
    resumeStory();
  };

  if (!isViewerOpen || !currentStory || !currentItem) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 bg-black flex items-center justify-center">
      {/* Close button */}
      <button
        onClick={() => {
          if (onClose) {
            onClose();
          } else {
            closeStory();
          }
        }}
        className="absolute top-4 right-4 z-10 text-white p-2 rounded-full hover:bg-gray-800 focus:outline-none"
        aria-label="Close"
      >
        <X size={24} />
      </button>

      {/* Story content */}
      <div
        className="relative w-full h-full max-w-md mx-auto"
        onClick={(e) => {
          handleLeftClick(e);
          handleMiddleClick(e);
          handleRightClick(e);
        }}
      >
        {/* Progress bars */}
        <div className="absolute top-4 left-4 right-4 z-10 flex space-x-1">
          {currentStory.items.map((item, index) => (
            <div
              key={index}
              className="h-1 bg-gray-600 rounded-full flex-1 overflow-hidden"
            >
              <div
                className={`h-full bg-white ${
                  index < currentItemIndex
                    ? 'w-full'
                    : index === currentItemIndex
                    ? ''
                    : 'w-0'
                }`}
                style={{
                  width: index === currentItemIndex ? `${progress}%` : undefined,
                }}
              />
            </div>
          ))}
        </div>

        {/* User info */}
        <div className="absolute top-8 left-4 right-4 z-10 flex items-center justify-between">
          <div className="flex items-center">
            <Avatar
              src={currentStory.user.avatar}
              alt={currentStory.user.name}
              className="w-8 h-8 border border-white"
            />
            <div className="ml-2">
              <p className="text-white font-medium text-sm">{currentStory.user.name}</p>
              <div className="flex items-center">
                <p className="text-gray-300 text-xs mr-3">
                  {formatDate(currentItem.createdAt)}
                </p>
                <button
                  className="flex items-center text-gray-300 text-xs hover:text-white"
                  onClick={() => setShowViewers(!showViewers)}
                >
                  <Eye size={12} className="mr-1" />
                  {viewCount}
                </button>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={handleOpenPoll}
              className="text-white p-1 rounded-full hover:bg-gray-800 focus:outline-none"
              aria-label="Poll"
            >
              <BarChart size={18} />
            </button>

            <button
              onClick={handleOpenQuestion}
              className="text-white p-1 rounded-full hover:bg-gray-800 focus:outline-none"
              aria-label="Question"
            >
              <HelpCircle size={18} />
            </button>

            <button
              className="text-white p-1 rounded-full hover:bg-gray-800 focus:outline-none"
              aria-label="More options"
            >
              <MoreHorizontal size={18} />
            </button>
          </div>
        </div>

        {/* Story media */}
        <div className="w-full h-full flex items-center justify-center">
          {isVideo ? (
            <video
              ref={videoRef}
              src={currentItem.url}
              className="max-w-full max-h-full object-contain"
              playsInline
              muted={false}
              controls={false}
              onEnded={handleVideoEnded}
            />
          ) : (
            <img
              src={currentItem.url}
              alt="Story"
              className="max-w-full max-h-full object-contain"
            />
          )}
        </div>

        {/* Pause/play indicator */}
        {isPaused && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-black bg-opacity-50 rounded-full p-4">
              <Play size={32} className="text-white" />
            </div>
          </div>
        )}

        {/* Navigation buttons for larger screens */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            prevStory();
          }}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 text-white p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 focus:outline-none hidden md:block"
          aria-label="Previous story"
        >
          <ChevronLeft size={24} />
        </button>

        <button
          onClick={(e) => {
            e.stopPropagation();
            nextStory();
          }}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 text-white p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 focus:outline-none hidden md:block"
          aria-label="Next story"
        >
          <ChevronRight size={24} />
        </button>

        {/* Reply input */}
        <div className="absolute bottom-4 left-4 right-4 z-10 flex items-center space-x-2">
          <div className="relative flex-1">
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Send a message..."
              className="w-full px-4 py-2 pr-10 bg-white bg-opacity-20 backdrop-blur-sm text-white rounded-full focus:outline-none focus:ring-2 focus:ring-green-500 placeholder-gray-300"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSendMessage();
                }
              }}
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputText.trim()}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white disabled:text-gray-400 focus:outline-none"
            >
              <Send size={18} />
            </button>
          </div>

          <div className="relative">
            <button
              onClick={handleOpenReactions}
              className="p-2 bg-white bg-opacity-20 backdrop-blur-sm text-white rounded-full hover:bg-opacity-30 focus:outline-none"
            >
              <Heart size={18} />
            </button>

            {showReactions && (
              <div className="absolute bottom-12 right-0 bg-white rounded-full p-2 shadow-lg flex space-x-2">
                {['❤️', '😂', '😮', '😢', '👍'].map((reaction) => (
                  <button
                    key={reaction}
                    onClick={() => handleAddReaction(reaction)}
                    className="text-xl hover:scale-125 transition-transform focus:outline-none"
                  >
                    {reaction}
                  </button>
                ))}
              </div>
            )}
          </div>

          <button
            className="p-2 bg-white bg-opacity-20 backdrop-blur-sm text-white rounded-full hover:bg-opacity-30 focus:outline-none"
            aria-label="Direct message"
          >
            <MessageCircle size={18} />
          </button>
        </div>

        {/* Story Interactions */}
        {showInteractions && (
          <div className="absolute inset-0 flex items-center justify-center z-20 bg-black bg-opacity-50">
            <div className="relative max-w-md w-full mx-4">
              <button
                onClick={handleCloseInteractions}
                className="absolute -top-10 right-0 text-white p-2 rounded-full hover:bg-gray-800 focus:outline-none"
                aria-label="Close interactions"
              >
                <X size={20} />
              </button>

              <StoryInteractions
                storyId={currentStory.id}
                storyItemId={currentItem.id}
                onClose={handleCloseInteractions}
              />
            </div>
          </div>
        )}

        {/* Viewers list */}
        {showViewers && (
          <div className="absolute inset-0 flex items-center justify-center z-20 bg-black bg-opacity-50">
            <div className="bg-white rounded-lg max-w-md w-full mx-4 overflow-hidden">
              <div className="p-4 border-b flex justify-between items-center">
                <h3 className="font-semibold">Viewers</h3>
                <button
                  onClick={() => setShowViewers(false)}
                  className="text-gray-500 hover:text-gray-700 focus:outline-none"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="max-h-80 overflow-y-auto p-4">
                {/* In a real app, you would fetch and display actual viewers */}
                {Array.from({ length: viewCount }).map((_, index) => (
                  <div key={index} className="flex items-center py-2">
                    <Avatar
                      src={`https://randomuser.me/api/portraits/${index % 2 === 0 ? 'women' : 'men'}/${(index % 70) + 1}.jpg`}
                      alt={`Viewer ${index + 1}`}
                      className="w-8 h-8 mr-3"
                    />
                    <div>
                      <p className="font-medium text-sm">User {index + 1}</p>
                      <p className="text-xs text-gray-500">{formatTime(new Date(Date.now() - Math.random() * 3600000).toISOString())}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
