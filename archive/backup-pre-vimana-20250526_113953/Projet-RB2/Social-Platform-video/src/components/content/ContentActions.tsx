import { useState } from 'react';
import { 
  MoreHorizontal, 
  Archive, 
  Trash2, 
  Edit, 
  Flag, 
  Share, 
  Link, 
  AlertTriangle,
  X,
  Check
} from 'lucide-react';

interface ContentActionsProps {
  postId: string;
  isOwner: boolean;
  onArchive?: (postId: string) => Promise<void>;
  onDelete?: (postId: string) => Promise<void>;
  onEdit?: (postId: string) => void;
  onReport?: (postId: string, reason: string) => Promise<void>;
  onShare?: (postId: string) => void;
  onCopyLink?: (postId: string) => void;
}

export function ContentActions({
  postId,
  isOwner,
  onArchive,
  onDelete,
  onEdit,
  onReport,
  onShare,
  onCopyLink
}: ContentActionsProps) {
  const [showMenu, setShowMenu] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportReason, setReportReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);
  
  const handleArchive = async () => {
    if (onArchive) {
      setIsProcessing(true);
      try {
        await onArchive(postId);
      } catch (error) {
        console.error('Failed to archive post:', error);
      } finally {
        setIsProcessing(false);
        setShowMenu(false);
      }
    }
  };
  
  const handleDelete = async () => {
    if (onDelete) {
      setIsProcessing(true);
      try {
        await onDelete(postId);
        setShowDeleteConfirm(false);
      } catch (error) {
        console.error('Failed to delete post:', error);
      } finally {
        setIsProcessing(false);
        setShowMenu(false);
      }
    }
  };
  
  const handleEdit = () => {
    if (onEdit) {
      onEdit(postId);
      setShowMenu(false);
    }
  };
  
  const handleReport = async () => {
    if (onReport && reportReason) {
      setIsProcessing(true);
      try {
        await onReport(postId, reportReason);
        setShowReportDialog(false);
      } catch (error) {
        console.error('Failed to report post:', error);
      } finally {
        setIsProcessing(false);
        setShowMenu(false);
      }
    }
  };
  
  const handleShare = () => {
    if (onShare) {
      onShare(postId);
      setShowMenu(false);
    }
  };
  
  const handleCopyLink = () => {
    if (onCopyLink) {
      onCopyLink(postId);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
      setShowMenu(false);
    }
  };
  
  // Report reasons
  const reportReasons = [
    'Inappropriate content',
    'Spam',
    'Harassment',
    'False information',
    'Intellectual property violation',
    'Violence',
    'Self-harm',
    'Hate speech',
    'Other'
  ];
  
  return (
    <div className="relative">
      <button
        onClick={() => setShowMenu(!showMenu)}
        className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
      >
        <MoreHorizontal size={20} />
      </button>
      
      {/* Actions Menu */}
      {showMenu && !showDeleteConfirm && !showReportDialog && (
        <div className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
          <div className="py-1" role="menu" aria-orientation="vertical">
            {isOwner ? (
              <>
                {onEdit && (
                  <button
                    onClick={handleEdit}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    role="menuitem"
                  >
                    <Edit size={16} className="mr-2" />
                    Edit
                  </button>
                )}
                
                {onArchive && (
                  <button
                    onClick={handleArchive}
                    disabled={isProcessing}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center disabled:opacity-50"
                    role="menuitem"
                  >
                    <Archive size={16} className="mr-2" />
                    Archive
                  </button>
                )}
                
                {onDelete && (
                  <button
                    onClick={() => {
                      setShowMenu(false);
                      setShowDeleteConfirm(true);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
                    role="menuitem"
                  >
                    <Trash2 size={16} className="mr-2" />
                    Delete
                  </button>
                )}
              </>
            ) : (
              <>
                {onReport && (
                  <button
                    onClick={() => {
                      setShowMenu(false);
                      setShowReportDialog(true);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    role="menuitem"
                  >
                    <Flag size={16} className="mr-2" />
                    Report
                  </button>
                )}
              </>
            )}
            
            <div className="border-t border-gray-100 my-1"></div>
            
            {onShare && (
              <button
                onClick={handleShare}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                role="menuitem"
              >
                <Share size={16} className="mr-2" />
                Share
              </button>
            )}
            
            {onCopyLink && (
              <button
                onClick={handleCopyLink}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                role="menuitem"
              >
                <Link size={16} className="mr-2" />
                {linkCopied ? 'Link copied!' : 'Copy link'}
              </button>
            )}
          </div>
        </div>
      )}
      
      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center text-red-600 mb-4">
              <AlertTriangle size={24} className="mr-2" />
              <h3 className="text-lg font-medium">Delete Post</h3>
            </div>
            
            <p className="mb-6 text-gray-600">
              Are you sure you want to delete this post? This action cannot be undone.
            </p>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Cancel
              </button>
              
              <button
                onClick={handleDelete}
                disabled={isProcessing}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
              >
                {isProcessing ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Report Dialog */}
      {showReportDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">Report Post</h3>
              <button
                onClick={() => setShowReportDialog(false)}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
              >
                <X size={20} />
              </button>
            </div>
            
            <p className="mb-4 text-gray-600">
              Please select a reason for reporting this post:
            </p>
            
            <div className="mb-6 space-y-2">
              {reportReasons.map((reason) => (
                <label key={reason} className="flex items-center">
                  <input
                    type="radio"
                    name="reportReason"
                    value={reason}
                    checked={reportReason === reason}
                    onChange={() => setReportReason(reason)}
                    className="h-4 w-4 text-green-500 focus:ring-green-500 border-gray-300"
                  />
                  <span className="ml-2 text-gray-700">{reason}</span>
                </label>
              ))}
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowReportDialog(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Cancel
              </button>
              
              <button
                onClick={handleReport}
                disabled={isProcessing || !reportReason}
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
              >
                {isProcessing ? (
                  'Submitting...'
                ) : (
                  <>
                    <Check size={16} className="mr-2" />
                    Submit Report
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
