import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Card } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Checkbox } from '../ui/checkbox';
import { Label } from '../ui/label';
import { AlertCircle, CheckCircle, Smartphone } from 'lucide-react';
import securityService from '../../services/securityService';

interface TwoFactorSetupProps {
  onComplete?: (success: boolean) => void;
}

export const TwoFactorSetup: React.FC<TwoFactorSetupProps> = ({ onComplete }) => {
  const [activeTab, setActiveTab] = useState('setup');
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [secret, setSecret] = useState<string | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showRecoveryCodes, setShowRecoveryCodes] = useState(false);
  const [recoveryCodes, setRecoveryCodes] = useState<string[]>([]);
  const [recoveryCodesConfirmed, setRecoveryCodesConfirmed] = useState(false);

  useEffect(() => {
    // Générer le QR code lors du chargement du composant
    generateQrCode();
  }, []);

  const generateQrCode = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await securityService.generate2FAQrCode();
      setQrCode(response.qrCodeUrl);
      setSecret(response.secret);
    } catch (err) {
      setError('Erreur lors de la génération du QR code. Veuillez réessayer.');
      console.error('Erreur 2FA:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerify = async () => {
    if (!verificationCode.trim()) {
      setError('Veuillez entrer le code de vérification');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await securityService.verify2FASetup(verificationCode);
      
      if (result) {
        setSuccess(true);
        // Générer des codes de récupération
        // Dans un cas réel, ces codes viendraient du backend
        const codes = [
          'ABCDE-12345', 'FGHIJ-67890', 'KLMNO-13579',
          'PQRST-24680', 'UVWXY-97531', 'ZABCD-86420',
          'EFGHI-75319', 'JKLMN-02468', 'OPQRS-19283',
          'TUVWX-74650'
        ];
        setRecoveryCodes(codes);
        setActiveTab('recovery');
        
        if (onComplete) {
          onComplete(true);
        }
      } else {
        setError('Code de vérification incorrect. Veuillez réessayer.');
      }
    } catch (err) {
      setError('Erreur lors de la vérification. Veuillez réessayer.');
      console.error('Erreur 2FA:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRecoveryCodesConfirmed = () => {
    setRecoveryCodesConfirmed(true);
    setActiveTab('complete');
  };

  return (
    <Card className="p-6 max-w-md mx-auto">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="setup" disabled={activeTab !== 'setup'}>
            Configuration
          </TabsTrigger>
          <TabsTrigger value="recovery" disabled={!success || recoveryCodesConfirmed}>
            Codes de récupération
          </TabsTrigger>
          <TabsTrigger value="complete" disabled={!recoveryCodesConfirmed}>
            Terminé
          </TabsTrigger>
        </TabsList>

        <TabsContent value="setup">
          <div className="space-y-4">
            <h2 className="text-xl font-bold">Configurer l'authentification à deux facteurs</h2>
            <p className="text-gray-600">
              L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à votre compte.
              Suivez ces étapes pour la configurer:
            </p>

            <ol className="list-decimal list-inside space-y-2 text-gray-600">
              <li>Téléchargez une application d'authentification comme Google Authenticator ou Authy</li>
              <li>Scannez le QR code ci-dessous avec l'application</li>
              <li>Entrez le code à 6 chiffres généré par l'application</li>
            </ol>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                {error}
              </div>
            )}

            <div className="flex flex-col items-center justify-center p-4 border rounded bg-gray-50">
              {isLoading ? (
                <div className="text-center py-8">Chargement...</div>
              ) : qrCode ? (
                <>
                  <img src={qrCode} alt="QR Code pour 2FA" className="mb-4" />
                  {secret && (
                    <div className="text-sm text-gray-500 text-center">
                      <p>Si vous ne pouvez pas scanner le QR code, entrez ce code manuellement:</p>
                      <code className="bg-gray-100 px-2 py-1 rounded">{secret}</code>
                    </div>
                  )}
                </>
              ) : (
                <Button onClick={generateQrCode}>Générer QR Code</Button>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="verification-code">Code de vérification</Label>
              <Input
                id="verification-code"
                placeholder="Entrez le code à 6 chiffres"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                maxLength={6}
              />
            </div>

            <Button 
              onClick={handleVerify} 
              disabled={isLoading || !qrCode || verificationCode.length !== 6}
              className="w-full"
            >
              Vérifier et activer
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="recovery">
          <div className="space-y-4">
            <h2 className="text-xl font-bold">Codes de récupération</h2>
            <p className="text-gray-600">
              Conservez ces codes de récupération dans un endroit sûr. Si vous perdez accès à votre application d'authentification,
              vous pourrez utiliser l'un de ces codes pour vous connecter.
            </p>

            <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
              <p className="font-bold">Important:</p>
              <ul className="list-disc list-inside">
                <li>Chaque code ne peut être utilisé qu'une seule fois</li>
                <li>Conservez ces codes dans un gestionnaire de mots de passe ou imprimez-les</li>
                <li>Traitez ces codes comme des mots de passe</li>
              </ul>
            </div>

            <div className="grid grid-cols-2 gap-2 p-4 border rounded bg-gray-50 font-mono text-sm">
              {recoveryCodes.map((code, index) => (
                <div key={index} className="p-2 bg-white border rounded">{code}</div>
              ))}
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox 
                id="confirm-saved" 
                checked={showRecoveryCodes}
                onCheckedChange={(checked) => setShowRecoveryCodes(checked as boolean)}
              />
              <Label htmlFor="confirm-saved">
                J'ai sauvegardé mes codes de récupération
              </Label>
            </div>

            <Button 
              onClick={handleRecoveryCodesConfirmed} 
              disabled={!showRecoveryCodes}
              className="w-full"
            >
              Continuer
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="complete">
          <div className="space-y-4 text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
            <h2 className="text-xl font-bold">Configuration terminée!</h2>
            <p className="text-gray-600">
              L'authentification à deux facteurs est maintenant activée pour votre compte.
              Vous devrez entrer un code de vérification à chaque connexion.
            </p>
            <Button className="w-full">Retour au profil</Button>
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
};

interface TwoFactorVerifyProps {
  userId: string;
  onVerify: (success: boolean) => void;
  onCancel: () => void;
}

export const TwoFactorVerify: React.FC<TwoFactorVerifyProps> = ({ userId, onVerify, onCancel }) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [useRecoveryCode, setUseRecoveryCode] = useState(false);

  const handleVerify = async () => {
    if (!verificationCode.trim()) {
      setError('Veuillez entrer le code de vérification');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await securityService.verify2FALogin(verificationCode, userId);
      
      if (result) {
        onVerify(true);
      } else {
        setError('Code de vérification incorrect. Veuillez réessayer.');
      }
    } catch (err) {
      setError('Erreur lors de la vérification. Veuillez réessayer.');
      console.error('Erreur 2FA:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="p-6 max-w-md mx-auto">
      <div className="space-y-4">
        <div className="flex items-center justify-center mb-4">
          <Smartphone className="h-12 w-12 text-primary" />
        </div>

        <h2 className="text-xl font-bold text-center">Vérification en deux étapes</h2>
        <p className="text-gray-600 text-center">
          {useRecoveryCode 
            ? "Entrez l'un de vos codes de récupération" 
            : "Entrez le code à 6 chiffres généré par votre application d'authentification"}
        </p>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="verification-code">
            {useRecoveryCode ? "Code de récupération" : "Code de vérification"}
          </Label>
          <Input
            id="verification-code"
            placeholder={useRecoveryCode ? "XXXXX-XXXXX" : "Entrez le code à 6 chiffres"}
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            maxLength={useRecoveryCode ? 11 : 6}
          />
        </div>

        <Button 
          onClick={handleVerify} 
          disabled={isLoading || (!useRecoveryCode && verificationCode.length !== 6)}
          className="w-full"
        >
          {isLoading ? "Vérification..." : "Vérifier"}
        </Button>

        <div className="text-center">
          <button 
            type="button"
            onClick={() => setUseRecoveryCode(!useRecoveryCode)}
            className="text-sm text-primary hover:underline"
          >
            {useRecoveryCode 
              ? "Utiliser l'application d'authentification à la place" 
              : "Utiliser un code de récupération à la place"}
          </button>
        </div>

        <div className="text-center">
          <button 
            type="button"
            onClick={onCancel}
            className="text-sm text-gray-500 hover:underline"
          >
            Annuler et retourner à la connexion
          </button>
        </div>
      </div>
    </Card>
  );
};

export default { TwoFactorSetup, TwoFactorVerify };
