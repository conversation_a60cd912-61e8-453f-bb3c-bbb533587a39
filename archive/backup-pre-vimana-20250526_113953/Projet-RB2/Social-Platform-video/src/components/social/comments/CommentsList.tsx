import { useState, useEffect } from 'react';
import { 
  Message<PERSON><PERSON><PERSON>, 
  ChevronDown, 
  ChevronUp, 
  Filter, 
  Loader2,
  <PERSON>ertCircle,
  RefreshCw
} from 'lucide-react';
import { CommentItem, Comment } from './CommentItem';
import { CommentEditor } from './CommentEditor';

interface CommentsListProps {
  entityId: string;
  entityType: 'video' | 'livestream' | 'blog';
  comments: Comment[];
  totalCount: number;
  isLoading: boolean;
  error: string | null;
  onAddComment: (content: string, attachments?: File[]) => Promise<void>;
  onLikeComment: (commentId: string) => Promise<void>;
  onReplyToComment: (commentId: string, content: string, attachments?: File[]) => Promise<void>;
  onEditComment: (commentId: string, content: string, attachments?: File[]) => Promise<void>;
  onDeleteComment: (commentId: string) => Promise<void>;
  onReportComment: (commentId: string, reason: string) => Promise<void>;
  onLoadMoreComments: () => Promise<void>;
  onRefreshComments: () => Promise<void>;
  hasMoreComments: boolean;
}

type SortOption = 'newest' | 'oldest' | 'most_liked';

export function CommentsList({
  entityId,
  entityType,
  comments,
  totalCount,
  isLoading,
  error,
  onAddComment,
  onLikeComment,
  onReplyToComment,
  onEditComment,
  onDeleteComment,
  onReportComment,
  onLoadMoreComments,
  onRefreshComments,
  hasMoreComments
}: CommentsListProps) {
  const [isCommentsOpen, setIsCommentsOpen] = useState(true);
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  // Toggle comments section
  const toggleComments = () => {
    setIsCommentsOpen(!isCommentsOpen);
  };
  
  // Handle sort change
  const handleSortChange = (option: SortOption) => {
    setSortBy(option);
  };
  
  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefreshComments();
    } catch (error) {
      console.error('Error refreshing comments:', error);
    } finally {
      setIsRefreshing(false);
    }
  };
  
  // Handle load more
  const handleLoadMore = async () => {
    setIsLoadingMore(true);
    try {
      await onLoadMoreComments();
    } catch (error) {
      console.error('Error loading more comments:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };
  
  // Sort comments based on selected option
  const sortedComments = [...comments].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'oldest':
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case 'most_liked':
        return b.likes - a.likes;
      default:
        return 0;
    }
  });
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Comments header */}
      <div className="p-4 border-b border-gray-200">
        <button
          type="button"
          onClick={toggleComments}
          className="flex items-center font-semibold"
        >
          <MessageCircle size={20} className="mr-2" />
          Comments ({totalCount})
          {isCommentsOpen ? (
            <ChevronUp size={20} className="ml-2" />
          ) : (
            <ChevronDown size={20} className="ml-2" />
          )}
        </button>
      </div>
      
      {/* Comments content */}
      {isCommentsOpen && (
        <div className="p-4">
          {/* Comment editor */}
          <div className="mb-6">
            <CommentEditor
              onSubmit={onAddComment}
              placeholder="Add a comment..."
              showAttachments={true}
            />
          </div>
          
          {/* Sort options */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Filter size={16} className="mr-2 text-gray-500" />
              <span className="text-sm text-gray-600 mr-2">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value as SortOption)}
                className="text-sm border-none bg-transparent focus:outline-none focus:ring-0 text-gray-700"
              >
                <option value="newest">Newest</option>
                <option value="oldest">Oldest</option>
                <option value="most_liked">Most liked</option>
              </select>
            </div>
            
            {/* Refresh button */}
            <button
              type="button"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
            >
              <RefreshCw size={14} className={`mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
          
          {/* Loading state */}
          {isLoading && comments.length === 0 && (
            <div className="py-8 flex flex-col items-center justify-center">
              <Loader2 size={32} className="text-gray-400 animate-spin mb-4" />
              <p className="text-gray-500">Loading comments...</p>
            </div>
          )}
          
          {/* Error state */}
          {error && (
            <div className="py-6 flex flex-col items-center justify-center">
              <AlertCircle size={32} className="text-red-500 mb-4" />
              <p className="text-red-500 mb-2">{error}</p>
              <button
                type="button"
                onClick={handleRefresh}
                className="text-blue-600 hover:text-blue-800"
              >
                Try again
              </button>
            </div>
          )}
          
          {/* Empty state */}
          {!isLoading && !error && comments.length === 0 && (
            <div className="py-8 flex flex-col items-center justify-center">
              <MessageCircle size={32} className="text-gray-400 mb-4" />
              <p className="text-gray-500 mb-2">No comments yet</p>
              <p className="text-gray-400 text-sm">Be the first to comment</p>
            </div>
          )}
          
          {/* Comments list */}
          {!isLoading && !error && comments.length > 0 && (
            <div className="space-y-4">
              {sortedComments.map((comment) => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  onLike={onLikeComment}
                  onReply={onReplyToComment}
                  onEdit={onEditComment}
                  onDelete={onDeleteComment}
                  onReport={onReportComment}
                />
              ))}
              
              {/* Load more button */}
              {hasMoreComments && (
                <div className="pt-4 flex justify-center">
                  <button
                    type="button"
                    onClick={handleLoadMore}
                    disabled={isLoadingMore}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 flex items-center"
                  >
                    {isLoadingMore ? (
                      <>
                        <Loader2 size={16} className="mr-2 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      'Load more comments'
                    )}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
