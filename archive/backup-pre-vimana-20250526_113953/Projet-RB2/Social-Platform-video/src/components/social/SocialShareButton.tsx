import { useState, useEffect } from 'react';
import { 
  Share2, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin, 
  Youtube, 
  X, 
  Calendar, 
  Hash, 
  Link2, 
  Loader2,
  Check,
  AlertCircle
} from 'lucide-react';
import { useSocialSharingStore } from '../../store/socialSharing';
import { SocialPlatform } from '../../api/socialSharingApi';

interface SocialShareButtonProps {
  contentId: string;
  contentType: 'video' | 'post' | 'story' | 'livestream';
  contentTitle?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function SocialShareButton({
  contentId,
  contentType,
  contentTitle = '',
  size = 'md',
  className = '',
}: SocialShareButtonProps) {
  const {
    socialConnections,
    crossPostSettings,
    isSharing,
    shareModalOpen,
    selectedPlatforms,
    fetchSocialConnections,
    fetchCrossPostSettings,
    shareToMultiplePlatforms,
    toggleSelectedPlatform,
    openShareModal,
    closeShareModal,
  } = useSocialSharingStore();
  
  const [caption, setCaption] = useState('');
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [newHashtag, setNewHashtag] = useState('');
  const [includeLink, setIncludeLink] = useState(true);
  const [schedulePost, setSchedulePost] = useState(false);
  const [scheduleTime, setScheduleTime] = useState('');
  const [platformSpecificOptions, setPlatformSpecificOptions] = useState<{
    [key in SocialPlatform]?: {
      caption?: string;
      hashtags?: string[];
      scheduleTime?: string;
      includeLink?: boolean;
    };
  }>({});
  const [showPlatformSpecific, setShowPlatformSpecific] = useState<SocialPlatform | null>(null);
  const [shareResults, setShareResults] = useState<{
    [key in SocialPlatform]?: { success: boolean; error?: string };
  }>({});
  const [isShared, setIsShared] = useState(false);
  
  // Button size classes
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base',
  };
  
  // Fetch social connections and cross-post settings on mount
  useEffect(() => {
    if (shareModalOpen) {
      fetchSocialConnections();
      fetchCrossPostSettings();
    }
  }, [shareModalOpen, fetchSocialConnections, fetchCrossPostSettings]);
  
  // Set default caption and hashtags from cross-post settings
  useEffect(() => {
    if (crossPostSettings) {
      setCaption(crossPostSettings.defaultCaption);
      setHashtags(crossPostSettings.defaultHashtags);
    }
  }, [crossPostSettings]);
  
  // Get platform icon
  const getPlatformIcon = (platform: SocialPlatform, size: number = 16) => {
    switch (platform) {
      case 'facebook':
        return <Facebook size={size} className="text-blue-600" />;
      case 'twitter':
        return <Twitter size={size} className="text-blue-400" />;
      case 'instagram':
        return <Instagram size={size} className="text-pink-500" />;
      case 'tiktok':
        return <span className="text-black font-bold">TT</span>;
      case 'youtube':
        return <Youtube size={size} className="text-red-600" />;
      case 'linkedin':
        return <Linkedin size={size} className="text-blue-700" />;
      case 'pinterest':
        return <span className="text-red-600 font-bold">P</span>;
      case 'reddit':
        return <span className="text-orange-600 font-bold">R</span>;
      default:
        return <Share2 size={size} />;
    }
  };
  
  // Handle adding a hashtag
  const handleAddHashtag = () => {
    if (newHashtag.trim() && !hashtags.includes(newHashtag.trim())) {
      setHashtags([...hashtags, newHashtag.trim()]);
      setNewHashtag('');
    }
  };
  
  // Handle removing a hashtag
  const handleRemoveHashtag = (tag: string) => {
    setHashtags(hashtags.filter((t) => t !== tag));
  };
  
  // Handle platform-specific option changes
  const handlePlatformSpecificChange = (
    platform: SocialPlatform,
    field: 'caption' | 'hashtags' | 'scheduleTime' | 'includeLink',
    value: any
  ) => {
    setPlatformSpecificOptions((prev) => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        [field]: value,
      },
    }));
  };
  
  // Handle sharing content
  const handleShareContent = async () => {
    // Prepare options
    const options = {
      caption,
      hashtags,
      includeLink,
      ...(schedulePost && scheduleTime ? { scheduleTime } : {}),
      platformSpecificOptions,
    };
    
    try {
      const results = await shareToMultiplePlatforms(
        selectedPlatforms,
        contentId,
        contentType,
        options
      );
      
      // Process results
      const processedResults: { [key in SocialPlatform]?: { success: boolean; error?: string } } = {};
      
      for (const platform of selectedPlatforms) {
        const result = results[platform];
        processedResults[platform] = {
          success: result?.success || false,
          error: result?.error,
        };
      }
      
      setShareResults(processedResults);
      setIsShared(true);
      
      // Close modal after a delay if all shares were successful
      const allSuccessful = selectedPlatforms.every(
        (platform) => processedResults[platform]?.success
      );
      
      if (allSuccessful) {
        setTimeout(() => {
          closeShareModal();
          setIsShared(false);
          setShareResults({});
        }, 2000);
      }
    } catch (error) {
      console.error('Error sharing content:', error);
    }
  };
  
  return (
    <>
      <button
        onClick={openShareModal}
        className={`bg-blue-500 text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center ${
          sizeClasses[size]
        } ${className}`}
      >
        <Share2 size={size === 'sm' ? 14 : size === 'md' ? 16 : 18} className="mr-1" />
        Share
      </button>
      
      {/* Share Modal */}
      {shareModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-semibold">Share to Social Media</h3>
              <button
                onClick={closeShareModal}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X size={20} />
              </button>
            </div>
            
            {/* Modal Content */}
            <div className="p-4">
              {isShared ? (
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-center">Share Results</h4>
                  
                  <div className="space-y-2">
                    {selectedPlatforms.map((platform) => (
                      <div
                        key={platform}
                        className={`flex items-center p-3 rounded-md ${
                          shareResults[platform]?.success
                            ? 'bg-green-50 border border-green-200'
                            : 'bg-red-50 border border-red-200'
                        }`}
                      >
                        <div className="mr-3">
                          {getPlatformIcon(platform)}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium capitalize">{platform}</p>
                        </div>
                        <div>
                          {shareResults[platform]?.success ? (
                            <Check size={18} className="text-green-500" />
                          ) : (
                            <AlertCircle size={18} className="text-red-500" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {selectedPlatforms.some((platform) => !shareResults[platform]?.success) && (
                    <div className="mt-4">
                      <p className="text-sm text-red-500">
                        Some shares failed. Please check your connections and try again.
                      </p>
                    </div>
                  )}
                  
                  <div className="flex justify-end mt-4">
                    <button
                      onClick={() => {
                        setIsShared(false);
                        setShareResults({});
                      }}
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Done
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  {/* Platform Selection */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Platforms
                    </label>
                    <div className="grid grid-cols-4 gap-2">
                      {socialConnections
                        .filter((conn) => conn.isConnected)
                        .map((connection) => (
                          <button
                            key={connection.platform}
                            onClick={() => toggleSelectedPlatform(connection.platform)}
                            className={`p-3 rounded-md flex flex-col items-center justify-center ${
                              selectedPlatforms.includes(connection.platform)
                                ? 'bg-blue-50 border-2 border-blue-500'
                                : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                            }`}
                          >
                            {getPlatformIcon(connection.platform, 24)}
                            <span className="text-xs mt-1 capitalize">
                              {connection.platform}
                            </span>
                          </button>
                        ))}
                    </div>
                    
                    {socialConnections.filter((conn) => conn.isConnected).length === 0 && (
                      <p className="text-sm text-gray-500 mt-2">
                        No connected social platforms. Please connect your accounts in settings.
                      </p>
                    )}
                  </div>
                  
                  {selectedPlatforms.length > 0 && (
                    <>
                      {/* Caption */}
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Caption
                        </label>
                        <textarea
                          value={caption}
                          onChange={(e) => setCaption(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          rows={3}
                          placeholder={`Share ${contentTitle || contentType}...`}
                        />
                      </div>
                      
                      {/* Hashtags */}
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                          <Hash size={14} className="mr-1" />
                          Hashtags
                        </label>
                        <div className="flex flex-wrap gap-2 mb-2">
                          {hashtags.map((tag) => (
                            <div
                              key={tag}
                              className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm flex items-center"
                            >
                              #{tag}
                              <button
                                onClick={() => handleRemoveHashtag(tag)}
                                className="ml-1 text-gray-500 hover:text-gray-700"
                              >
                                <X size={14} />
                              </button>
                            </div>
                          ))}
                        </div>
                        <div className="flex">
                          <input
                            type="text"
                            value={newHashtag}
                            onChange={(e) => setNewHashtag(e.target.value)}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Add hashtag"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                handleAddHashtag();
                              }
                            }}
                          />
                          <button
                            onClick={handleAddHashtag}
                            className="px-3 py-2 bg-gray-100 text-gray-700 rounded-r-md border border-gray-300 border-l-0 hover:bg-gray-200 focus:outline-none"
                          >
                            Add
                          </button>
                        </div>
                      </div>
                      
                      {/* Options */}
                      <div className="mb-4 space-y-2">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="includeLink"
                            checked={includeLink}
                            onChange={(e) => setIncludeLink(e.target.checked)}
                            className="h-4 w-4 text-blue-500 focus:ring-blue-500 rounded"
                          />
                          <label htmlFor="includeLink" className="ml-2 text-sm text-gray-700 flex items-center">
                            <Link2 size={14} className="mr-1" />
                            Include link to content
                          </label>
                        </div>
                        
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="schedulePost"
                            checked={schedulePost}
                            onChange={(e) => setSchedulePost(e.target.checked)}
                            className="h-4 w-4 text-blue-500 focus:ring-blue-500 rounded"
                          />
                          <label htmlFor="schedulePost" className="ml-2 text-sm text-gray-700 flex items-center">
                            <Calendar size={14} className="mr-1" />
                            Schedule post
                          </label>
                        </div>
                        
                        {schedulePost && (
                          <div className="ml-6 mt-2">
                            <input
                              type="datetime-local"
                              value={scheduleTime}
                              onChange={(e) => setScheduleTime(e.target.value)}
                              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              min={new Date().toISOString().slice(0, 16)}
                            />
                          </div>
                        )}
                      </div>
                      
                      {/* Platform-specific options */}
                      {selectedPlatforms.length > 1 && (
                        <div className="mb-4">
                          <div className="flex justify-between items-center mb-2">
                            <label className="block text-sm font-medium text-gray-700">
                              Platform-specific Options
                            </label>
                            <select
                              value={showPlatformSpecific || ''}
                              onChange={(e) => setShowPlatformSpecific(e.target.value as SocialPlatform || null)}
                              className="text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                              <option value="">Select platform</option>
                              {selectedPlatforms.map((platform) => (
                                <option key={platform} value={platform} className="capitalize">
                                  {platform}
                                </option>
                              ))}
                            </select>
                          </div>
                          
                          {showPlatformSpecific && (
                            <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                              <div className="flex items-center mb-2">
                                {getPlatformIcon(showPlatformSpecific)}
                                <h4 className="text-sm font-medium ml-1 capitalize">
                                  {showPlatformSpecific} Options
                                </h4>
                              </div>
                              
                              <div className="space-y-3">
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">
                                    Custom Caption
                                  </label>
                                  <textarea
                                    value={platformSpecificOptions[showPlatformSpecific]?.caption || ''}
                                    onChange={(e) => handlePlatformSpecificChange(
                                      showPlatformSpecific,
                                      'caption',
                                      e.target.value
                                    )}
                                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    rows={2}
                                    placeholder="Leave empty to use the default caption"
                                  />
                                </div>
                                
                                <div className="flex items-center">
                                  <input
                                    type="checkbox"
                                    id={`${showPlatformSpecific}-includeLink`}
                                    checked={platformSpecificOptions[showPlatformSpecific]?.includeLink ?? includeLink}
                                    onChange={(e) => handlePlatformSpecificChange(
                                      showPlatformSpecific,
                                      'includeLink',
                                      e.target.checked
                                    )}
                                    className="h-4 w-4 text-blue-500 focus:ring-blue-500 rounded"
                                  />
                                  <label
                                    htmlFor={`${showPlatformSpecific}-includeLink`}
                                    className="ml-2 text-xs text-gray-700"
                                  >
                                    Include link
                                  </label>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </>
              )}
            </div>
            
            {/* Modal Footer */}
            {!isShared && (
              <div className="p-4 border-t flex justify-end">
                <button
                  onClick={closeShareModal}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md mr-2 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                
                <button
                  onClick={handleShareContent}
                  disabled={selectedPlatforms.length === 0 || isSharing || (schedulePost && !scheduleTime)}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                >
                  {isSharing ? (
                    <>
                      <Loader2 size={16} className="animate-spin mr-2" />
                      Sharing...
                    </>
                  ) : (
                    <>
                      <Share2 size={16} className="mr-2" />
                      Share Now
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
