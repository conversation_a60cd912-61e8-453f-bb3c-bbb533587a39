import { useEffect } from 'react';
import { 
  BarChart2, 
  Eye, 
  Heart, 
  MessageCircle, 
  Share2, 
  RefreshCw, 
  ExternalLink, 
  Loader2,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Youtube
} from 'lucide-react';
import { useSocialSharingStore } from '../../store/socialSharing';
import { SocialPlatform, SocialMetrics } from '../../api/socialSharingApi';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';

interface SocialMetricsCardProps {
  contentId: string;
  contentType: 'video' | 'post' | 'story' | 'livestream';
  className?: string;
}

export function SocialMetricsCard({
  contentId,
  contentType,
  className = '',
}: SocialMetricsCardProps) {
  const {
    socialMetrics,
    isLoading,
    error,
    fetchSocialMetrics,
  } = useSocialSharingStore();
  
  // Fetch social metrics on mount
  useEffect(() => {
    fetchSocialMetrics(contentId, contentType);
  }, [contentId, contentType, fetchSocialMetrics]);
  
  // Get platform icon
  const getPlatformIcon = (platform: SocialPlatform, size: number = 16) => {
    switch (platform) {
      case 'facebook':
        return <Facebook size={size} className="text-blue-600" />;
      case 'twitter':
        return <Twitter size={size} className="text-blue-400" />;
      case 'instagram':
        return <Instagram size={size} className="text-pink-500" />;
      case 'tiktok':
        return <span className="text-black font-bold">TT</span>;
      case 'youtube':
        return <Youtube size={size} className="text-red-600" />;
      case 'linkedin':
        return <Linkedin size={size} className="text-blue-700" />;
      case 'pinterest':
        return <span className="text-red-600 font-bold">P</span>;
      case 'reddit':
        return <span className="text-orange-600 font-bold">R</span>;
      default:
        return null;
    }
  };
  
  // Format number with K/M suffix
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    }).format(date);
  };
  
  // Calculate total metrics across all platforms
  const calculateTotalMetrics = () => {
    return socialMetrics.reduce(
      (totals, metric) => {
        totals.views += metric.metrics.views;
        totals.likes += metric.metrics.likes;
        totals.comments += metric.metrics.comments;
        totals.shares += metric.metrics.shares;
        totals.clicks += metric.metrics.clicks;
        totals.reach += metric.metrics.reach;
        return totals;
      },
      { views: 0, likes: 0, comments: 0, shares: 0, clicks: 0, reach: 0 }
    );
  };
  
  // Prepare data for engagement by platform chart
  const prepareEngagementData = () => {
    return socialMetrics.map((metric) => ({
      name: metric.platform,
      likes: metric.metrics.likes,
      comments: metric.metrics.comments,
      shares: metric.metrics.shares,
    }));
  };
  
  // Prepare data for reach by platform pie chart
  const prepareReachData = () => {
    return socialMetrics.map((metric) => ({
      name: metric.platform,
      value: metric.metrics.reach,
    }));
  };
  
  // Colors for charts
  const COLORS = ['#4267B2', '#1DA1F2', '#E1306C', '#0077B5', '#FF0000', '#BD081C', '#FF4500'];
  
  // Total metrics
  const totalMetrics = calculateTotalMetrics();
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium flex items-center">
          <BarChart2 size={18} className="text-blue-500 mr-2" />
          Social Media Performance
        </h3>
        
        <button
          onClick={() => fetchSocialMetrics(contentId, contentType)}
          disabled={isLoading}
          className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
          title="Refresh metrics"
        >
          <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
        </button>
      </div>
      
      {isLoading && socialMetrics.length === 0 ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 size={24} className="animate-spin text-blue-500" />
        </div>
      ) : error ? (
        <div className="p-4 text-center text-red-500">
          <p>Error: {error}</p>
          <button
            onClick={() => fetchSocialMetrics(contentId, contentType)}
            className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            Try Again
          </button>
        </div>
      ) : socialMetrics.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No social media metrics available for this content.</p>
          <p className="text-sm mt-1">
            Share your content on social media to start tracking performance.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Overview Stats */}
          <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
            {/* Views */}
            <div className="bg-gray-50 p-3 rounded-md text-center">
              <div className="text-sm text-gray-500 flex items-center justify-center">
                <Eye size={14} className="mr-1" />
                Views
              </div>
              <div className="text-lg font-semibold">
                {formatNumber(totalMetrics.views)}
              </div>
            </div>
            
            {/* Likes */}
            <div className="bg-gray-50 p-3 rounded-md text-center">
              <div className="text-sm text-gray-500 flex items-center justify-center">
                <Heart size={14} className="mr-1" />
                Likes
              </div>
              <div className="text-lg font-semibold">
                {formatNumber(totalMetrics.likes)}
              </div>
            </div>
            
            {/* Comments */}
            <div className="bg-gray-50 p-3 rounded-md text-center">
              <div className="text-sm text-gray-500 flex items-center justify-center">
                <MessageCircle size={14} className="mr-1" />
                Comments
              </div>
              <div className="text-lg font-semibold">
                {formatNumber(totalMetrics.comments)}
              </div>
            </div>
            
            {/* Shares */}
            <div className="bg-gray-50 p-3 rounded-md text-center">
              <div className="text-sm text-gray-500 flex items-center justify-center">
                <Share2 size={14} className="mr-1" />
                Shares
              </div>
              <div className="text-lg font-semibold">
                {formatNumber(totalMetrics.shares)}
              </div>
            </div>
            
            {/* Clicks */}
            <div className="bg-gray-50 p-3 rounded-md text-center">
              <div className="text-sm text-gray-500 flex items-center justify-center">
                <ExternalLink size={14} className="mr-1" />
                Clicks
              </div>
              <div className="text-lg font-semibold">
                {formatNumber(totalMetrics.clicks)}
              </div>
            </div>
            
            {/* Reach */}
            <div className="bg-gray-50 p-3 rounded-md text-center">
              <div className="text-sm text-gray-500 flex items-center justify-center">
                <Users size={14} className="mr-1" />
                Reach
              </div>
              <div className="text-lg font-semibold">
                {formatNumber(totalMetrics.reach)}
              </div>
            </div>
          </div>
          
          {/* Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Engagement by Platform */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Engagement by Platform</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={prepareEngagementData()}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis 
                      dataKey="name" 
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => value.charAt(0).toUpperCase() + value.slice(1, 3)}
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip
                      formatter={(value: number) => [formatNumber(value), '']}
                      labelFormatter={(label) => label.charAt(0).toUpperCase() + label.slice(1)}
                    />
                    <Legend />
                    <Bar dataKey="likes" name="Likes" fill="#10B981" />
                    <Bar dataKey="comments" name="Comments" fill="#3B82F6" />
                    <Bar dataKey="shares" name="Shares" fill="#8B5CF6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
            
            {/* Reach by Platform */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Reach by Platform</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={prepareReachData()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => 
                        `${name.charAt(0).toUpperCase() + name.slice(1, 3)}: ${(percent * 100).toFixed(0)}%`
                      }
                    >
                      {prepareReachData().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: number) => [formatNumber(value), 'Reach']}
                      labelFormatter={(label) => label.charAt(0).toUpperCase() + label.slice(1)}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
          
          {/* Platform Details */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Platform Details</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Platform
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Views
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Likes
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Comments
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Shares
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Engagement
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Updated
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {socialMetrics.map((metric) => (
                    <tr key={metric.platform}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getPlatformIcon(metric.platform)}
                          <span className="ml-2 text-sm font-medium text-gray-900 capitalize">
                            {metric.platform}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatNumber(metric.metrics.views)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatNumber(metric.metrics.likes)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatNumber(metric.metrics.comments)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatNumber(metric.metrics.shares)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {(metric.metrics.engagement * 100).toFixed(2)}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(metric.lastUpdated)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
