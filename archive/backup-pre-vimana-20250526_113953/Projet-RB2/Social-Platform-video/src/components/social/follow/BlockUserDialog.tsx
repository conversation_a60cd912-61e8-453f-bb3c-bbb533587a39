import { useState } from 'react';
import { 
  Shield<PERSON>lert, 
  UserX, 
  AlertTriangle, 
  Check, 
  Loader2,
  X
} from 'lucide-react';

interface BlockUserDialogProps {
  userId: string;
  userName: string;
  userAvatar?: string;
  isOpen: boolean;
  onClose: () => void;
  onBlock: (userId: string, reason?: string) => Promise<void>;
  onUnblock?: (userId: string) => Promise<void>;
  isBlocked?: boolean;
}

export function BlockUserDialog({
  userId,
  userName,
  userAvatar,
  isOpen,
  onClose,
  onBlock,
  onUnblock,
  isBlocked = false,
}: BlockUserDialogProps) {
  const [reason, setReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  if (!isOpen) return null;
  
  // Handle block
  const handleBlock = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      await onBlock(userId, reason);
      setSuccess(`You have blocked ${userName}`);
      
      // Close dialog after a delay
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      setError('Failed to block user. Please try again.');
      console.error('Error blocking user:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle unblock
  const handleUnblock = async () => {
    if (!onUnblock) return;
    
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      await onUnblock(userId);
      setSuccess(`You have unblocked ${userName}`);
      
      // Close dialog after a delay
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      setError('Failed to unblock user. Please try again.');
      console.error('Error unblocking user:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6 relative">
        {/* Close button */}
        <button
          type="button"
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          aria-label="Close"
        >
          <X size={20} />
        </button>
        
        <div className="text-center mb-6">
          <div className="mx-auto w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
            {isBlocked ? (
              <UserX size={24} className="text-red-600" />
            ) : (
              <ShieldAlert size={24} className="text-red-600" />
            )}
          </div>
          
          <h3 className="text-xl font-bold mb-2">
            {isBlocked ? 'Unblock User' : 'Block User'}
          </h3>
          
          <p className="text-gray-600">
            {isBlocked
              ? `Are you sure you want to unblock ${userName}? They will be able to follow you, view your content, and interact with you again.`
              : `When you block ${userName}, they won't be able to follow you, view your content, or interact with you.`
            }
          </p>
        </div>
        
        {/* User info */}
        <div className="flex items-center p-3 bg-gray-50 rounded-md mb-6">
          {/* Avatar */}
          <div className="mr-3 flex-shrink-0">
            {userAvatar ? (
              <img
                src={userAvatar}
                alt={userName}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                {userName.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          
          {/* User name */}
          <div>
            <p className="font-medium">{userName}</p>
            <p className="text-sm text-gray-500">@{userName.toLowerCase().replace(/\s+/g, '')}</p>
          </div>
        </div>
        
        {/* Reason input (only for blocking) */}
        {!isBlocked && (
          <div className="mb-6">
            <label htmlFor="block-reason" className="block text-sm font-medium text-gray-700 mb-1">
              Reason for blocking (optional)
            </label>
            <select
              id="block-reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              disabled={isLoading}
            >
              <option value="">Select a reason</option>
              <option value="harassment">Harassment or bullying</option>
              <option value="spam">Spam or scam</option>
              <option value="inappropriate">Inappropriate content</option>
              <option value="impersonation">Impersonation</option>
              <option value="other">Other</option>
            </select>
          </div>
        )}
        
        {/* Warning */}
        {!isBlocked && (
          <div className="flex items-start p-3 bg-yellow-50 rounded-md mb-6 text-sm">
            <AlertTriangle size={16} className="text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
            <p className="text-yellow-800">
              Blocking is different from unfollowing or muting. The blocked user will not be notified that you've blocked them, but they will no longer be able to:
              <ul className="list-disc pl-5 mt-1 space-y-1">
                <li>Follow you or view your content</li>
                <li>Tag you in posts or comments</li>
                <li>Message you or interact with your content</li>
                <li>Find your profile in search results</li>
              </ul>
            </p>
          </div>
        )}
        
        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
            <AlertTriangle size={16} className="mr-2 flex-shrink-0" />
            {error}
          </div>
        )}
        
        {/* Success message */}
        {success && (
          <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md text-sm flex items-center">
            <Check size={16} className="mr-2 flex-shrink-0" />
            {success}
          </div>
        )}
        
        {/* Action buttons */}
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            disabled={isLoading}
          >
            Cancel
          </button>
          
          <button
            type="button"
            onClick={isBlocked ? handleUnblock : handleBlock}
            className={`flex-1 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 flex items-center justify-center ${
              isBlocked
                ? 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
                : 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
            }`}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 size={16} className="animate-spin mr-2" />
            ) : isBlocked ? (
              <UserX size={16} className="mr-2" />
            ) : (
              <ShieldAlert size={16} className="mr-2" />
            )}
            {isBlocked ? 'Unblock' : 'Block'}
          </button>
        </div>
      </div>
    </div>
  );
}
