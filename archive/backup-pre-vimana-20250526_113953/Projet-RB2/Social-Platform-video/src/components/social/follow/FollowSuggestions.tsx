import { useState, useEffect } from 'react';
import { 
  Users, 
  RefreshCw, 
  Loader2, 
  AlertCircle, 
  ChevronRight,
  X
} from 'lucide-react';
import { FollowButton } from './FollowButton';

export interface User {
  id: string;
  name: string;
  username: string;
  avatar?: string;
  bio?: string;
  isFollowing: boolean;
  isVerified?: boolean;
  followerCount?: number;
  mutualFollowers?: number;
  mutualFollowerNames?: string[];
}

interface FollowSuggestionsProps {
  title?: string;
  subtitle?: string;
  users: User[];
  isLoading: boolean;
  error: string | null;
  onFollow: (userId: string) => Promise<void>;
  onUnfollow: (userId: string) => Promise<void>;
  onRefresh: () => Promise<void>;
  onDismiss?: (userId: string) => Promise<void>;
  onViewMore?: () => void;
  maxUsers?: number;
  showDismiss?: boolean;
  showMutualFollowers?: boolean;
  variant?: 'card' | 'inline' | 'compact';
  className?: string;
}

export function FollowSuggestions({
  title = 'Suggested for you',
  subtitle = 'Based on your interests and activity',
  users,
  isLoading,
  error,
  onFollow,
  onUnfollow,
  onRefresh,
  onDismiss,
  onViewMore,
  maxUsers = 5,
  showDismiss = true,
  showMutualFollowers = true,
  variant = 'card',
  className = '',
}: FollowSuggestionsProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [displayedUsers, setDisplayedUsers] = useState<User[]>([]);
  
  // Update displayed users when users prop changes
  useEffect(() => {
    setDisplayedUsers(users.slice(0, maxUsers));
  }, [users, maxUsers]);
  
  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefresh();
    } catch (error) {
      console.error('Error refreshing suggestions:', error);
    } finally {
      setIsRefreshing(false);
    }
  };
  
  // Handle dismiss
  const handleDismiss = async (userId: string) => {
    if (!onDismiss) return;
    
    try {
      await onDismiss(userId);
      setDisplayedUsers(displayedUsers.filter(user => user.id !== userId));
    } catch (error) {
      console.error('Error dismissing suggestion:', error);
    }
  };
  
  // Render mutual followers text
  const renderMutualFollowers = (user: User) => {
    if (!showMutualFollowers || !user.mutualFollowers || user.mutualFollowers === 0) {
      return null;
    }
    
    if (user.mutualFollowerNames && user.mutualFollowerNames.length > 0) {
      const firstFollower = user.mutualFollowerNames[0];
      
      if (user.mutualFollowers === 1) {
        return (
          <span className="text-xs text-gray-500">
            Followed by {firstFollower}
          </span>
        );
      } else {
        const othersCount = user.mutualFollowers - 1;
        return (
          <span className="text-xs text-gray-500">
            Followed by {firstFollower} and {othersCount} {othersCount === 1 ? 'other' : 'others'}
          </span>
        );
      }
    } else {
      return (
        <span className="text-xs text-gray-500">
          {user.mutualFollowers} mutual {user.mutualFollowers === 1 ? 'follower' : 'followers'}
        </span>
      );
    }
  };
  
  // Card variant
  if (variant === 'card') {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <div>
            <h3 className="font-semibold flex items-center">
              <Users size={18} className="mr-2 text-gray-500" />
              {title}
            </h3>
            {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
          </div>
          
          <button
            type="button"
            onClick={handleRefresh}
            disabled={isRefreshing || isLoading}
            className="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
            aria-label="Refresh suggestions"
          >
            <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-4">
          {/* Loading state */}
          {isLoading && displayedUsers.length === 0 && (
            <div className="py-8 flex flex-col items-center justify-center">
              <Loader2 size={32} className="text-gray-400 animate-spin mb-4" />
              <p className="text-gray-500">Loading suggestions...</p>
            </div>
          )}
          
          {/* Error state */}
          {error && (
            <div className="py-6 flex flex-col items-center justify-center">
              <AlertCircle size={32} className="text-red-500 mb-4" />
              <p className="text-red-500 mb-2">{error}</p>
              <button
                type="button"
                onClick={handleRefresh}
                className="text-blue-600 hover:text-blue-800"
              >
                Try again
              </button>
            </div>
          )}
          
          {/* Empty state */}
          {!isLoading && !error && displayedUsers.length === 0 && (
            <div className="py-8 flex flex-col items-center justify-center">
              <Users size={32} className="text-gray-400 mb-4" />
              <p className="text-gray-500 mb-2">No suggestions available</p>
              <button
                type="button"
                onClick={handleRefresh}
                className="text-blue-600 hover:text-blue-800"
              >
                Refresh
              </button>
            </div>
          )}
          
          {/* User list */}
          {!isLoading && !error && displayedUsers.length > 0 && (
            <div className="space-y-4">
              {displayedUsers.map(user => (
                <div key={user.id} className="flex items-center">
                  {/* Avatar */}
                  <div className="mr-3 flex-shrink-0">
                    {user.avatar ? (
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                        {user.name.charAt(0).toUpperCase()}
                      </div>
                    )}
                  </div>
                  
                  {/* User info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center">
                      <p className="font-medium text-gray-900 truncate">
                        {user.name}
                      </p>
                      {user.isVerified && (
                        <span className="ml-1 text-blue-500">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                            <path fillRule="evenodd" d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                          </svg>
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-500 truncate">@{user.username}</p>
                    {renderMutualFollowers(user)}
                  </div>
                  
                  {/* Follow button */}
                  <div className="ml-4 flex items-center">
                    <FollowButton
                      userId={user.id}
                      userName={user.name}
                      isFollowing={user.isFollowing}
                      size="sm"
                      variant="outline"
                      onFollow={onFollow}
                      onUnfollow={onUnfollow}
                    />
                    
                    {/* Dismiss button */}
                    {showDismiss && onDismiss && (
                      <button
                        type="button"
                        onClick={() => handleDismiss(user.id)}
                        className="ml-2 text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                        aria-label={`Dismiss suggestion for ${user.name}`}
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* View more button */}
          {!isLoading && !error && displayedUsers.length > 0 && onViewMore && (
            <div className="mt-4 text-center">
              <button
                type="button"
                onClick={onViewMore}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center justify-center mx-auto"
              >
                View more suggestions
                <ChevronRight size={16} className="ml-1" />
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }
  
  // Inline variant
  if (variant === 'inline') {
    return (
      <div className={`${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold flex items-center">
            <Users size={18} className="mr-2 text-gray-500" />
            {title}
          </h3>
          
          <button
            type="button"
            onClick={handleRefresh}
            disabled={isRefreshing || isLoading}
            className="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
            aria-label="Refresh suggestions"
          >
            <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
          </button>
        </div>
        
        {/* User list */}
        <div className="flex overflow-x-auto space-x-4 pb-4">
          {isLoading && displayedUsers.length === 0 ? (
            <div className="flex-1 py-8 flex flex-col items-center justify-center">
              <Loader2 size={24} className="text-gray-400 animate-spin mb-2" />
              <p className="text-sm text-gray-500">Loading...</p>
            </div>
          ) : error ? (
            <div className="flex-1 py-6 flex flex-col items-center justify-center">
              <AlertCircle size={24} className="text-red-500 mb-2" />
              <p className="text-sm text-red-500">{error}</p>
            </div>
          ) : displayedUsers.length === 0 ? (
            <div className="flex-1 py-8 flex flex-col items-center justify-center">
              <Users size={24} className="text-gray-400 mb-2" />
              <p className="text-sm text-gray-500">No suggestions</p>
            </div>
          ) : (
            displayedUsers.map(user => (
              <div key={user.id} className="flex-shrink-0 w-48 bg-white rounded-lg border border-gray-200 p-4">
                {/* Avatar */}
                <div className="flex justify-center mb-3">
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-xl">
                      {user.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                
                {/* User info */}
                <div className="text-center mb-3">
                  <div className="flex items-center justify-center">
                    <p className="font-medium text-gray-900 truncate">
                      {user.name}
                    </p>
                    {user.isVerified && (
                      <span className="ml-1 text-blue-500">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                          <path fillRule="evenodd" d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                        </svg>
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 truncate">@{user.username}</p>
                  {renderMutualFollowers(user)}
                </div>
                
                {/* Follow button */}
                <div className="flex justify-center">
                  <FollowButton
                    userId={user.id}
                    userName={user.name}
                    isFollowing={user.isFollowing}
                    size="sm"
                    variant="primary"
                    onFollow={onFollow}
                    onUnfollow={onUnfollow}
                    className="w-full"
                  />
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    );
  }
  
  // Compact variant
  return (
    <div className={`${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-gray-700">{title}</h3>
        
        {onViewMore && (
          <button
            type="button"
            onClick={onViewMore}
            className="text-blue-600 hover:text-blue-800 text-xs font-medium"
          >
            See all
          </button>
        )}
      </div>
      
      {/* User list */}
      <div className="space-y-2">
        {isLoading && displayedUsers.length === 0 ? (
          <div className="py-4 flex items-center justify-center">
            <Loader2 size={16} className="text-gray-400 animate-spin" />
          </div>
        ) : error ? (
          <div className="py-2 text-xs text-red-500">{error}</div>
        ) : displayedUsers.length === 0 ? (
          <div className="py-2 text-xs text-gray-500">No suggestions available</div>
        ) : (
          displayedUsers.map(user => (
            <div key={user.id} className="flex items-center py-1">
              {/* Avatar */}
              <div className="mr-2 flex-shrink-0">
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              
              {/* User info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user.name}
                  {user.isVerified && (
                    <span className="ml-1 text-blue-500">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="inline w-3 h-3">
                        <path fillRule="evenodd" d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                      </svg>
                    </span>
                  )}
                </p>
                <p className="text-xs text-gray-500 truncate">@{user.username}</p>
              </div>
              
              {/* Follow button */}
              <FollowButton
                userId={user.id}
                userName={user.name}
                isFollowing={user.isFollowing}
                size="sm"
                variant="minimal"
                onFollow={onFollow}
                onUnfollow={onUnfollow}
              />
            </div>
          ))
        )}
      </div>
    </div>
  );
}
