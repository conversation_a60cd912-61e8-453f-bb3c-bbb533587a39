import { useState, useEffect, useRef } from 'react';
import { 
  Edit, 
  Check, 
  X, 
  <PERSON><PERSON>les, 
  AlertCircle, 
  Loader2, 
  Info,
  Lightbulb
} from 'lucide-react';

interface BioEditorProps {
  initialBio: string;
  maxLength?: number;
  onSave: (bio: string) => Promise<void>;
  placeholder?: string;
  showTemplates?: boolean;
  readOnly?: boolean;
  className?: string;
}

// Bio templates for different types of users
const bioTemplates = [
  {
    id: 'yoga-instructor',
    title: 'Yoga Instructor',
    content: 'Certified yoga instructor with X years of experience. Specializing in [style] yoga. Helping people find balance, strength, and peace through mindful practice.'
  },
  {
    id: 'wellness-coach',
    title: 'Wellness Coach',
    content: 'Holistic wellness coach passionate about helping others achieve optimal health. Specializing in [area]. Certified in [certifications]. Let\'s transform your wellbeing together!'
  },
  {
    id: 'meditation-teacher',
    title: 'Meditation Teacher',
    content: 'Meditation teacher and mindfulness practitioner. Guiding others on their journey to inner peace for X years. Certified in [practice]. Join me to discover the power of presence.'
  },
  {
    id: 'retreat-host',
    title: 'Retreat Host',
    content: 'Creating transformative retreat experiences in [location]. Specializing in [type] retreats. Join me for a journey of self-discovery, healing, and connection with like-minded souls.'
  },
  {
    id: 'nutritionist',
    title: 'Nutritionist',
    content: 'Certified nutritionist passionate about whole foods and balanced eating. Helping clients achieve their health goals through personalized nutrition plans. Specializing in [specialty].'
  }
];

export function BioEditor({
  initialBio,
  maxLength = 500,
  onSave,
  placeholder = 'Write something about yourself...',
  showTemplates = true,
  readOnly = false,
  className = ''
}: BioEditorProps) {
  const [bio, setBio] = useState(initialBio);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [showTemplateInfo, setShowTemplateInfo] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const templateSelectorRef = useRef<HTMLDivElement>(null);
  
  // Auto-resize textarea as content grows
  useEffect(() => {
    if (textareaRef.current && isEditing) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [bio, isEditing]);
  
  // Auto-focus textarea when editing starts
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isEditing]);
  
  // Handle click outside of template selector
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        templateSelectorRef.current && 
        !templateSelectorRef.current.contains(event.target as Node)
      ) {
        setShowTemplateSelector(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Start editing
  const handleEdit = () => {
    if (readOnly) return;
    setIsEditing(true);
  };
  
  // Cancel editing
  const handleCancel = () => {
    setIsEditing(false);
    setBio(initialBio);
    setError(null);
  };
  
  // Save bio
  const handleSave = async () => {
    if (bio.length > maxLength) {
      setError(`Bio is too long. Maximum ${maxLength} characters allowed.`);
      return;
    }
    
    setIsSaving(true);
    setError(null);
    
    try {
      await onSave(bio);
      setIsEditing(false);
    } catch (err) {
      setError('Failed to save bio. Please try again.');
      console.error('Error saving bio:', err);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Apply template
  const applyTemplate = (templateContent: string) => {
    setBio(templateContent);
    setShowTemplateSelector(false);
    
    // Focus textarea after applying template
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };
  
  // Toggle template selector
  const toggleTemplateSelector = () => {
    setShowTemplateSelector(!showTemplateSelector);
  };
  
  // Toggle template info
  const toggleTemplateInfo = () => {
    setShowTemplateInfo(!showTemplateInfo);
  };
  
  // Calculate remaining characters
  const remainingChars = maxLength - bio.length;
  const isNearLimit = remainingChars <= maxLength * 0.1; // 10% of max length
  const isOverLimit = remainingChars < 0;
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="font-semibold">Bio</h3>
        
        {!isEditing && !readOnly && (
          <button
            type="button"
            onClick={handleEdit}
            className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100"
            aria-label="Edit bio"
          >
            <Edit size={16} />
          </button>
        )}
      </div>
      
      <div className="p-4">
        {isEditing ? (
          <div className="space-y-3">
            <div className="relative">
              <textarea
                ref={textareaRef}
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                placeholder={placeholder}
                className={`w-full min-h-[120px] p-3 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  isOverLimit ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={isSaving}
              />
              
              {/* Character counter */}
              <div className={`text-xs mt-1 text-right ${
                isOverLimit ? 'text-red-500 font-medium' : 
                isNearLimit ? 'text-orange-500' : 
                'text-gray-500'
              }`}>
                {remainingChars} characters remaining
              </div>
            </div>
            
            {/* Template selector button */}
            {showTemplates && (
              <div className="relative">
                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={toggleTemplateSelector}
                    className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                    disabled={isSaving}
                  >
                    <Sparkles size={14} className="mr-1" />
                    Use a template
                  </button>
                  
                  <button
                    type="button"
                    onClick={toggleTemplateInfo}
                    className="ml-2 text-gray-400 hover:text-gray-600"
                    aria-label="Template information"
                  >
                    <Info size={14} />
                  </button>
                </div>
                
                {/* Template info tooltip */}
                {showTemplateInfo && (
                  <div className="mt-2 p-3 bg-blue-50 text-blue-800 text-sm rounded-md flex items-start">
                    <Lightbulb size={16} className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                    <div>
                      Templates help you create a professional bio quickly. Customize the template by replacing placeholder text in [brackets] with your own information.
                    </div>
                  </div>
                )}
                
                {/* Template selector dropdown */}
                {showTemplateSelector && (
                  <div 
                    ref={templateSelectorRef}
                    className="absolute z-10 mt-2 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto"
                  >
                    <div className="p-2 border-b border-gray-200">
                      <h4 className="text-sm font-medium text-gray-700">Select a template</h4>
                    </div>
                    
                    <div className="p-2 space-y-2">
                      {bioTemplates.map(template => (
                        <button
                          key={template.id}
                          type="button"
                          onClick={() => applyTemplate(template.content)}
                          className="w-full text-left p-2 hover:bg-gray-50 rounded-md"
                        >
                          <div className="font-medium text-sm">{template.title}</div>
                          <div className="text-xs text-gray-500 line-clamp-2">{template.content}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {/* Error message */}
            {error && (
              <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
                <AlertCircle size={16} className="mr-2 flex-shrink-0" />
                {error}
              </div>
            )}
            
            {/* Action buttons */}
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={handleCancel}
                className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                disabled={isSaving}
              >
                Cancel
              </button>
              
              <button
                type="button"
                onClick={handleSave}
                className="px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                disabled={isSaving || isOverLimit}
              >
                {isSaving ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Check size={16} className="mr-2" />
                    Save
                  </>
                )}
              </button>
            </div>
          </div>
        ) : (
          <div>
            {bio ? (
              <p className="text-gray-800 whitespace-pre-wrap">{bio}</p>
            ) : (
              <p className="text-gray-400 italic">
                {readOnly ? 'No bio available' : 'Add a bio to tell people about yourself'}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
