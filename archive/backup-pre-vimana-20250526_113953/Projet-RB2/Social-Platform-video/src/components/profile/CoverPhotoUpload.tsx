import { useState, useRef, useEffect } from 'react';
import { 
  Upload, 
  Camera, 
  Trash2, 
  Crop, 
  Move, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  RotateCw, 
  Check, 
  X, 
  Loader2, 
  Image as ImageIcon,
  AlertCircle,
  Info
} from 'lucide-react';

interface CoverPhotoUploadProps {
  currentImageUrl?: string;
  onSave: (imageFile: File) => Promise<void>;
  onRemove?: () => Promise<void>;
  aspectRatio?: number; // width/height
  recommendedSize?: string;
  className?: string;
}

export function CoverPhotoUpload({
  currentImageUrl,
  onSave,
  onRemove,
  aspectRatio = 3, // 3:1 aspect ratio by default
  recommendedSize = '1500x500 pixels',
  className = ''
}: CoverPhotoUploadProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showInfo, setShowInfo] = useState(false);
  
  // Cropper state
  const [isCropping, setIsCropping] = useState(false);
  const [cropPosition, setCropPosition] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cropperRef = useRef<HTMLDivElement>(null);
  const cropperImageRef = useRef<HTMLImageElement>(null);
  
  // Clean up preview URL when component unmounts
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);
  
  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('Image must be less than 10MB');
      return;
    }
    
    setSelectedFile(file);
    
    // Create preview URL
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);
    
    // Reset cropper state
    setCropPosition({ x: 0, y: 0 });
    setZoom(1);
    setRotation(0);
    
    // Start cropping
    setIsCropping(true);
    setError(null);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Handle save
  const handleSave = async () => {
    if (!selectedFile) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real implementation, you would apply cropping, zoom, and rotation here
      // For this example, we'll just use the original file
      await onSave(selectedFile);
      
      // Reset state
      setIsEditing(false);
      setIsCropping(false);
      setSelectedFile(null);
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    } catch (err) {
      setError('Failed to save cover photo');
      console.error('Error saving cover photo:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle remove
  const handleRemove = async () => {
    if (!onRemove) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await onRemove();
      
      // Reset state
      setIsEditing(false);
      setSelectedFile(null);
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    } catch (err) {
      setError('Failed to remove cover photo');
      console.error('Error removing cover photo:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle cancel
  const handleCancel = () => {
    setIsEditing(false);
    setIsCropping(false);
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    setError(null);
  };
  
  // Cropper controls
  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 3));
  };
  
  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  };
  
  const handleRotateLeft = () => {
    setRotation(prev => prev - 90);
  };
  
  const handleRotateRight = () => {
    setRotation(prev => prev + 90);
  };
  
  // Apply crop
  const handleApplyCrop = () => {
    setIsCropping(false);
    // In a real implementation, you would apply the crop here
    // For this example, we'll just proceed to save
  };
  
  // Toggle info
  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="font-semibold">Cover Photo</h3>
        
        <button
          type="button"
          onClick={toggleInfo}
          className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
          aria-label="Cover photo information"
        >
          <Info size={16} />
        </button>
      </div>
      
      {showInfo && (
        <div className="p-4 bg-blue-50 border-b border-blue-100">
          <div className="flex items-start text-sm text-blue-800">
            <Info size={16} className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <p className="mb-1">
                Your cover photo is displayed at the top of your profile and helps create a professional impression.
              </p>
              <p>
                <span className="font-medium">Recommended size:</span> {recommendedSize}
              </p>
              <p>
                <span className="font-medium">Aspect ratio:</span> {aspectRatio}:1
              </p>
            </div>
          </div>
        </div>
      )}
      
      <div className="p-4">
        {isEditing ? (
          <div className="space-y-4">
            {isCropping && previewUrl ? (
              <div className="space-y-4">
                {/* Cropper */}
                <div 
                  ref={cropperRef}
                  className="relative mx-auto overflow-hidden"
                  style={{ 
                    width: '100%', 
                    height: '200px',
                    aspectRatio: `${aspectRatio}/1` 
                  }}
                >
                  <img
                    ref={cropperImageRef}
                    src={previewUrl}
                    alt="Crop preview"
                    className="absolute"
                    style={{
                      transform: `translate(${cropPosition.x}px, ${cropPosition.y}px) scale(${zoom}) rotate(${rotation}deg)`,
                      transformOrigin: 'center',
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                </div>
                
                {/* Cropper controls */}
                <div className="flex justify-center space-x-2">
                  <button
                    type="button"
                    onClick={handleZoomIn}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                    title="Zoom in"
                  >
                    <ZoomIn size={18} />
                  </button>
                  <button
                    type="button"
                    onClick={handleZoomOut}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                    title="Zoom out"
                  >
                    <ZoomOut size={18} />
                  </button>
                  <button
                    type="button"
                    onClick={handleRotateLeft}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                    title="Rotate left"
                  >
                    <RotateCcw size={18} />
                  </button>
                  <button
                    type="button"
                    onClick={handleRotateRight}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                    title="Rotate right"
                  >
                    <RotateCw size={18} />
                  </button>
                </div>
                
                {/* Crop actions */}
                <div className="flex justify-center space-x-2">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleApplyCrop}
                    className="px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center"
                  >
                    <Check size={16} className="mr-1" />
                    Apply
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Upload options */}
                <div className="flex flex-col items-center">
                  <div className="mb-4">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleFileSelect}
                      className="hidden"
                    />
                    
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
                    >
                      <Upload size={16} className="mr-2" />
                      Upload Cover Photo
                    </button>
                  </div>
                </div>
                
                {/* Error message */}
                {error && (
                  <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
                    <AlertCircle size={16} className="mr-2 flex-shrink-0" />
                    {error}
                  </div>
                )}
                
                {/* Actions */}
                <div className="flex justify-center space-x-2">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    disabled={isLoading}
                  >
                    Cancel
                  </button>
                  
                  {onRemove && currentImageUrl && (
                    <button
                      type="button"
                      onClick={handleRemove}
                      className="px-3 py-1.5 border border-red-300 text-red-700 rounded-md hover:bg-red-50 flex items-center"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <Loader2 size={16} className="mr-1 animate-spin" />
                      ) : (
                        <Trash2 size={16} className="mr-1" />
                      )}
                      Remove
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center">
            {/* Cover photo display */}
            <div 
              className="relative w-full mb-4 rounded-md overflow-hidden"
              style={{ 
                height: '200px',
                aspectRatio: `${aspectRatio}/1` 
              }}
            >
              {currentImageUrl ? (
                <img
                  src={currentImageUrl}
                  alt="Cover"
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500">
                  <ImageIcon size={48} />
                </div>
              )}
              
              <button
                type="button"
                onClick={() => setIsEditing(true)}
                className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
              >
                <Camera size={24} className="text-white" />
              </button>
            </div>
            
            <button
              type="button"
              onClick={() => setIsEditing(true)}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
            >
              <ImageIcon size={14} className="mr-1" />
              {currentImageUrl ? 'Change Cover Photo' : 'Add Cover Photo'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
