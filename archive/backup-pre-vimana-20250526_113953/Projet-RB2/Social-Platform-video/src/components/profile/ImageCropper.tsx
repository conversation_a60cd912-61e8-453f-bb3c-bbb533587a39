import { useState, useRef, useEffect } from 'react';
import { X, Check, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';

interface ImageCropperProps {
  image: string;
  onCrop: (croppedImage: string) => void;
  onCancel: () => void;
  aspectRatio?: number;
  circular?: boolean;
}

export function ImageCropper({
  image,
  onCrop,
  onCancel,
  aspectRatio = 1,
  circular = false,
}: ImageCropperProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  
  // Reset position when image changes
  useEffect(() => {
    setPosition({ x: 0, y: 0 });
    setScale(1);
    setRotation(0);
  }, [image]);
  
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    });
  };
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };
  
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    setDragStart({
      x: e.touches[0].clientX - position.x,
      y: e.touches[0].clientY - position.y,
    });
  };
  
  const handleTouchMove = (e: React.TouchEvent) => {
    if (isDragging) {
      setPosition({
        x: e.touches[0].clientX - dragStart.x,
        y: e.touches[0].clientY - dragStart.y,
      });
    }
  };
  
  const handleTouchEnd = () => {
    setIsDragging(false);
  };
  
  const zoomIn = () => {
    setScale(Math.min(scale + 0.1, 3));
  };
  
  const zoomOut = () => {
    setScale(Math.max(scale - 0.1, 0.5));
  };
  
  const rotate = () => {
    setRotation((rotation + 90) % 360);
  };
  
  const cropImage = () => {
    if (!imageRef.current || !containerRef.current) return;
    
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Get container dimensions
    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;
    
    // Set canvas size to match container (or desired output size)
    canvas.width = containerWidth;
    canvas.height = containerHeight;
    
    // If circular, create a circular clip path
    if (circular) {
      ctx.beginPath();
      ctx.arc(
        containerWidth / 2,
        containerHeight / 2,
        Math.min(containerWidth, containerHeight) / 2,
        0,
        Math.PI * 2
      );
      ctx.clip();
    }
    
    // Draw the image with transformations
    ctx.save();
    ctx.translate(containerWidth / 2, containerHeight / 2);
    ctx.rotate((rotation * Math.PI) / 180);
    ctx.scale(scale, scale);
    ctx.translate(-containerWidth / 2 + position.x / scale, -containerHeight / 2 + position.y / scale);
    
    // Draw the image
    ctx.drawImage(
      imageRef.current,
      0,
      0,
      imageRef.current.naturalWidth,
      imageRef.current.naturalHeight
    );
    
    ctx.restore();
    
    // Convert to data URL and pass to onCrop
    const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
    onCrop(dataUrl);
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="text-lg font-medium">Crop Image</h3>
          <button
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <X size={24} />
          </button>
        </div>
        
        <div className="p-4">
          <div 
            ref={containerRef}
            className={`relative overflow-hidden mx-auto ${
              circular ? 'rounded-full' : 'rounded-lg'
            }`}
            style={{
              width: '300px',
              height: circular ? '300px' : `${300 / aspectRatio}px`,
              cursor: isDragging ? 'grabbing' : 'grab',
            }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <img
              ref={imageRef}
              src={image}
              alt="Crop preview"
              className="absolute"
              style={{
                transform: `translate(${position.x}px, ${position.y}px) scale(${scale}) rotate(${rotation}deg)`,
                transformOrigin: 'center',
                maxWidth: 'none',
              }}
              draggable={false}
            />
          </div>
          
          <div className="flex justify-center space-x-4 mt-4">
            <button
              onClick={zoomOut}
              className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <ZoomOut size={20} />
            </button>
            <button
              onClick={zoomIn}
              className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <ZoomIn size={20} />
            </button>
            <button
              onClick={rotate}
              className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <RotateCcw size={20} />
            </button>
          </div>
          
          <div className="text-center text-sm text-gray-500 mt-2">
            <p>Drag to reposition • Pinch or use buttons to zoom</p>
          </div>
        </div>
        
        <div className="p-4 border-t flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Cancel
          </button>
          <button
            onClick={cropImage}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center"
          >
            <Check size={18} className="mr-2" />
            Apply
          </button>
        </div>
      </div>
    </div>
  );
}
