import { useState, useRef, useEffect } from 'react';
import { 
  Upload, 
  Camera, 
  Trash2, 
  Crop, 
  Move, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  RotateCw, 
  Check, 
  X, 
  Loader2, 
  User,
  Image as ImageIcon,
  AlertCircle
} from 'lucide-react';

interface ProfilePictureUploadProps {
  currentImageUrl?: string;
  onSave: (imageFile: File) => Promise<void>;
  onRemove?: () => Promise<void>;
  size?: 'sm' | 'md' | 'lg';
  showDefaultAvatars?: boolean;
  className?: string;
}

// Default avatars
const defaultAvatars = [
  '/avatars/default-1.png',
  '/avatars/default-2.png',
  '/avatars/default-3.png',
  '/avatars/default-4.png',
  '/avatars/default-5.png',
  '/avatars/default-6.png',
];

export function ProfilePictureUpload({
  currentImageUrl,
  onSave,
  onRemove,
  size = 'md',
  showDefaultAvatars = true,
  className = ''
}: ProfilePictureUploadProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDefaultAvatarSelector, setShowDefaultAvatarSelector] = useState(false);
  
  // Cropper state
  const [isCropping, setIsCropping] = useState(false);
  const [cropPosition, setCropPosition] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cropperRef = useRef<HTMLDivElement>(null);
  const cropperImageRef = useRef<HTMLImageElement>(null);
  
  // Size classes
  const sizeClasses = {
    sm: 'w-24 h-24',
    md: 'w-32 h-32',
    lg: 'w-40 h-40'
  };
  
  // Clean up preview URL when component unmounts
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);
  
  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }
    
    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image must be less than 5MB');
      return;
    }
    
    setSelectedFile(file);
    
    // Create preview URL
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);
    
    // Reset cropper state
    setCropPosition({ x: 0, y: 0 });
    setZoom(1);
    setRotation(0);
    
    // Start cropping
    setIsCropping(true);
    setError(null);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Handle default avatar selection
  const handleDefaultAvatarSelect = async (avatarUrl: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch the image and convert to File object
      const response = await fetch(avatarUrl);
      const blob = await response.blob();
      const file = new File([blob], 'default-avatar.png', { type: 'image/png' });
      
      await onSave(file);
      setShowDefaultAvatarSelector(false);
    } catch (err) {
      setError('Failed to set default avatar');
      console.error('Error setting default avatar:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle save
  const handleSave = async () => {
    if (!selectedFile) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real implementation, you would apply cropping, zoom, and rotation here
      // For this example, we'll just use the original file
      await onSave(selectedFile);
      
      // Reset state
      setIsEditing(false);
      setIsCropping(false);
      setSelectedFile(null);
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    } catch (err) {
      setError('Failed to save profile picture');
      console.error('Error saving profile picture:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle remove
  const handleRemove = async () => {
    if (!onRemove) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await onRemove();
      
      // Reset state
      setIsEditing(false);
      setSelectedFile(null);
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    } catch (err) {
      setError('Failed to remove profile picture');
      console.error('Error removing profile picture:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle cancel
  const handleCancel = () => {
    setIsEditing(false);
    setIsCropping(false);
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    setError(null);
  };
  
  // Cropper controls
  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 3));
  };
  
  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  };
  
  const handleRotateLeft = () => {
    setRotation(prev => prev - 90);
  };
  
  const handleRotateRight = () => {
    setRotation(prev => prev + 90);
  };
  
  // Apply crop
  const handleApplyCrop = () => {
    setIsCropping(false);
    // In a real implementation, you would apply the crop here
    // For this example, we'll just proceed to save
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold">Profile Picture</h3>
      </div>
      
      <div className="p-4">
        {isEditing ? (
          <div className="space-y-4">
            {isCropping && previewUrl ? (
              <div className="space-y-4">
                {/* Cropper */}
                <div 
                  ref={cropperRef}
                  className={`relative mx-auto overflow-hidden rounded-full ${sizeClasses[size]}`}
                >
                  <img
                    ref={cropperImageRef}
                    src={previewUrl}
                    alt="Crop preview"
                    className="absolute"
                    style={{
                      transform: `translate(${cropPosition.x}px, ${cropPosition.y}px) scale(${zoom}) rotate(${rotation}deg)`,
                      transformOrigin: 'center',
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                </div>
                
                {/* Cropper controls */}
                <div className="flex justify-center space-x-2">
                  <button
                    type="button"
                    onClick={handleZoomIn}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                    title="Zoom in"
                  >
                    <ZoomIn size={18} />
                  </button>
                  <button
                    type="button"
                    onClick={handleZoomOut}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                    title="Zoom out"
                  >
                    <ZoomOut size={18} />
                  </button>
                  <button
                    type="button"
                    onClick={handleRotateLeft}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                    title="Rotate left"
                  >
                    <RotateCcw size={18} />
                  </button>
                  <button
                    type="button"
                    onClick={handleRotateRight}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                    title="Rotate right"
                  >
                    <RotateCw size={18} />
                  </button>
                </div>
                
                {/* Crop actions */}
                <div className="flex justify-center space-x-2">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleApplyCrop}
                    className="px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center"
                  >
                    <Check size={16} className="mr-1" />
                    Apply
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Upload options */}
                <div className="flex flex-col items-center">
                  <div className="mb-4">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleFileSelect}
                      className="hidden"
                    />
                    
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
                      >
                        <Upload size={16} className="mr-2" />
                        Upload Photo
                      </button>
                      
                      {showDefaultAvatars && (
                        <button
                          type="button"
                          onClick={() => setShowDefaultAvatarSelector(!showDefaultAvatarSelector)}
                          className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center"
                        >
                          <User size={16} className="mr-2" />
                          Default Avatars
                        </button>
                      )}
                    </div>
                  </div>
                  
                  {/* Default avatar selector */}
                  {showDefaultAvatarSelector && (
                    <div className="grid grid-cols-3 gap-2 mb-4">
                      {defaultAvatars.map((avatar, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleDefaultAvatarSelect(avatar)}
                          className="p-1 border border-gray-200 rounded-md hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <img
                            src={avatar}
                            alt={`Default avatar ${index + 1}`}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* Error message */}
                {error && (
                  <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
                    <AlertCircle size={16} className="mr-2 flex-shrink-0" />
                    {error}
                  </div>
                )}
                
                {/* Actions */}
                <div className="flex justify-center space-x-2">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    disabled={isLoading}
                  >
                    Cancel
                  </button>
                  
                  {onRemove && currentImageUrl && (
                    <button
                      type="button"
                      onClick={handleRemove}
                      className="px-3 py-1.5 border border-red-300 text-red-700 rounded-md hover:bg-red-50 flex items-center"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <Loader2 size={16} className="mr-1 animate-spin" />
                      ) : (
                        <Trash2 size={16} className="mr-1" />
                      )}
                      Remove
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center">
            {/* Profile picture display */}
            <div className={`relative rounded-full overflow-hidden mb-4 ${sizeClasses[size]}`}>
              {currentImageUrl ? (
                <img
                  src={currentImageUrl}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500">
                  <User size={size === 'sm' ? 32 : size === 'md' ? 48 : 64} />
                </div>
              )}
              
              <button
                type="button"
                onClick={() => setIsEditing(true)}
                className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
              >
                <Camera size={24} className="text-white" />
              </button>
            </div>
            
            <button
              type="button"
              onClick={() => setIsEditing(true)}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
            >
              <ImageIcon size={14} className="mr-1" />
              {currentImageUrl ? 'Change Photo' : 'Add Photo'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
