import { useState } from 'react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { User, Eye, Clock, Calendar, MessageSquare } from 'lucide-react';
import { cn } from '@/lib/utils';
// import type { Livestream } from '@/types'; // Temporarily comment out

interface LivestreamCardProps {
  livestream: any; // Changed type to any temporarily
  className?: string;
  onClick?: () => void;
  compact?: boolean; // Added compact prop
}

export function LivestreamCard({ livestream, className, onClick, compact }: LivestreamCardProps) { // Added compact to destructuring
  const [isHovered, setIsHovered] = useState(false);
  
  const isLive = livestream.status === 'live';
  
  return (
    <Card 
      className={cn(
        "overflow-hidden transition-all duration-300 h-full flex flex-col", 
        isHovered && "shadow-md", 
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      {/* Thumbnail */}
      <div className="relative aspect-video overflow-hidden">
        <img 
          src={livestream.thumbnailUrl || `https://api.thumbalizr.com/?url=${encodeURIComponent(livestream.streamUrl)}&width=720`}
          alt={livestream.title}
          className="w-full h-full object-cover transition-transform duration-300"
          style={{ transform: isHovered ? 'scale(1.05)' : 'scale(1)' }}
        />
        
        {/* Live badge */}
        {isLive && (
          <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
            <span className="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>
            LIVE
          </div>
        )}
        
        {/* Scheduled badge */}
        {livestream.status === 'scheduled' && livestream.scheduledFor && (
          <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
            <Calendar className="w-3 h-3 mr-1" />
            {new Date(livestream.scheduledFor).toLocaleDateString()}
          </div>
        )}
        
        {/* Duration or viewer count */}
        <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center">
          {isLive ? (
            <>
              <Eye className="w-3 h-3 mr-1" />
              {livestream.viewerCount || 0} watching
            </>
          ) : (
            <>
              <Clock className="w-3 h-3 mr-1" />
              {livestream.duration || '00:00'}
            </>
          )}
        </div>
        
        {/* Play button overlay on hover */}
        {isHovered && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/30">
            <Button 
              variant="ghost" 
              size="icon" 
              className="rounded-full bg-white/20 backdrop-blur-sm hover:bg-white/30"
            >
              {isLive ? (
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  viewBox="0 0 24 24" 
                  fill="white" 
                  className="w-10 h-10"
                >
                  <path d="M8 5v14l11-7z" />
                </svg>
              ) : (
                <Calendar className="w-6 h-6 text-white" />
              )}
            </Button>
          </div>
        )}
      </div>
      
      {/* Content */}
      <div className="p-4 flex-1 flex flex-col">
        <h3 className="font-semibold text-lg mb-1 line-clamp-2">{livestream.title}</h3>
        
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <User className="w-3 h-3 mr-1" />
          <span>{livestream.user?.name || 'Unknown creator'}</span>
          
          {isLive && (
            <>
              <span className="mx-2">•</span>
              <span className="text-red-500 font-medium">Live now</span>
            </>
          )}
          
          {livestream.status === 'scheduled' && livestream.scheduledFor && (
            <>
              <span className="mx-2">•</span>
              <span>Starts {new Date(livestream.scheduledFor).toLocaleString()}</span>
            </>
          )}
          
          {livestream.status === 'ended' && livestream.endedAt && (
            <>
              <span className="mx-2">•</span>
              <span>Streamed on {new Date(livestream.endedAt).toLocaleDateString()}</span>
            </>
          )}
        </div>
        
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{livestream.description}</p>
        
        {/* Stats */}
        <div className="mt-auto flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
          <div className="flex items-center mr-3">
            <Eye className="w-3 h-3 mr-1" />
            <span>{isLive ? `${livestream.viewerCount || 0} watching` : `${livestream.totalViews || 0} views`}</span>
          </div>
          <div className="flex items-center">
            <MessageSquare className="w-3 h-3 mr-1" />
            <span>{livestream.comments || 0}</span>
          </div>
        </div>
      </div>
    </Card>
  );
}
