import { Heart, MessageCircle, Share2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PostActionsProps {
  postId: string;
  likes: number;
  comments: number;
  hasLiked: boolean;
  onLike: (postId: string) => void
}

export function PostActions({ postId, likes, comments, hasLiked, onLike }: PostActionsProps) {
  return (
    <div className="absolute right-4 bottom-24 flex flex-col items-center gap-6">
      <button
        onClick={(e) => {
          e.stopPropagation();
          onLike(postId)
        }}
        className="flex flex-col items-center gap-1"
      >
        <div
          className={cn(
            'p-2 rounded-full transition-colors',
            hasLiked ? 'bg-red-500/50' : 'bg-black/50'
          )}
        >
          <Heart
            className={cn('w-6 h-6', hasLiked && 'fill-current text-white')}
          />
        </div>
        <span className="text-white text-sm font-medium">
          {likes.toLocaleString()}
        </span>
      </button>

      <button className="flex flex-col items-center gap-1">
        <div className="p-2 bg-black/50 rounded-full">
          <MessageCircle className="w-6 h-6 text-white" />
        </div>
        <span className="text-white text-sm font-medium">
          {comments.toLocaleString()}
        </span>
      </button>

      <button className="flex flex-col items-center gap-1">
        <div className="p-2 bg-black/50 rounded-full">
          <Share2 className="w-6 h-6 text-white" />
        </div>
        <span className="text-white text-sm font-medium">Share</span>
      </button>
    </div>
  );
}