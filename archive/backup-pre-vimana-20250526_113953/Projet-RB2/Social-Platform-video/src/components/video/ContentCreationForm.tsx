import { useState, useEffect } from 'react';
import { 
  Video, 
  Edit, 
  Image, 
  Upload, 
  Save, 
  Eye, 
  ArrowRight, 
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { VideoEditor } from './VideoEditor';
import { ThumbnailGenerator } from './ThumbnailGenerator';
import { CaptionHashtagEditor } from './CaptionHashtagEditor';
import { LocationTagging } from './LocationTagging';
import { DraftSaving } from './DraftSaving';

interface Hashtag {
  id: string;
  text: string;
}

interface Mention {
  id: string;
  username: string;
  displayName: string;
}

interface Location {
  id: string;
  name: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  type: 'city' | 'place' | 'custom';
  popularity?: number;
}

interface Draft {
  id: string;
  title: string;
  thumbnailUrl: string;
  lastSaved: Date;
  scheduledPublishDate?: Date;
  videoId: string;
  metadata: Record<string, any>;
}

interface ContentCreationFormProps {
  videoId: string;
  initialData?: {
    title?: string;
    description?: string;
    thumbnailUrl?: string;
    caption?: string;
    hashtags?: Hashtag[];
    mentions?: Mention[];
    location?: Location;
  };
  onPublish: (data: Record<string, any>) => Promise<void>;
  onSaveDraft: (data: Record<string, any>) => Promise<Draft>;
  onLoadDraft: (draftId: string) => Promise<Record<string, any>>;
  onDeleteDraft: (draftId: string) => Promise<void>;
  onSchedulePublish: (date: Date, data: Record<string, any>) => Promise<void>;
  existingDrafts?: Draft[];
  suggestedHashtags?: Hashtag[];
  suggestedMentions?: Mention[];
  popularLocations?: Location[];
}

export function ContentCreationForm({
  videoId,
  initialData = {},
  onPublish,
  onSaveDraft,
  onLoadDraft,
  onDeleteDraft,
  onSchedulePublish,
  existingDrafts = [],
  suggestedHashtags = [],
  suggestedMentions = [],
  popularLocations = []
}: ContentCreationFormProps) {
  // Form state
  const [title, setTitle] = useState(initialData.title || '');
  const [description, setDescription] = useState(initialData.description || '');
  const [thumbnailUrl, setThumbnailUrl] = useState(initialData.thumbnailUrl || '');
  const [caption, setCaption] = useState(initialData.caption || '');
  const [hashtags, setHashtags] = useState<Hashtag[]>(initialData.hashtags || []);
  const [mentions, setMentions] = useState<Mention[]>(initialData.mentions || []);
  const [location, setLocation] = useState<Location | null>(initialData.location || null);
  
  // UI state
  const [activeTab, setActiveTab] = useState<'edit' | 'details'>('edit');
  const [isEditing, setIsEditing] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Handle video editor save
  const handleEditorSave = () => {
    setIsEditing(false);
  };
  
  // Handle thumbnail selection
  const handleThumbnailSelect = (url: string) => {
    setThumbnailUrl(url);
  };
  
  // Handle caption and hashtags change
  const handleCaptionHashtagsChange = (data: { 
    caption: string; 
    hashtags: Hashtag[]; 
    mentions: Mention[] 
  }) => {
    setCaption(data.caption);
    setHashtags(data.hashtags);
    setMentions(data.mentions);
  };
  
  // Handle location selection
  const handleLocationSelect = (selectedLocation: Location | null) => {
    setLocation(selectedLocation);
  };
  
  // Handle draft saving
  const handleSaveDraft = async () => {
    const draftData = {
      videoId,
      title,
      description,
      thumbnailUrl,
      caption,
      hashtags,
      mentions,
      location,
    };
    
    return await onSaveDraft(draftData);
  };
  
  // Handle draft loading
  const handleLoadDraft = async (draftId: string) => {
    try {
      const draftData = await onLoadDraft(draftId);
      
      // Update form state with draft data
      setTitle(draftData.title || '');
      setDescription(draftData.description || '');
      setThumbnailUrl(draftData.thumbnailUrl || '');
      setCaption(draftData.caption || '');
      setHashtags(draftData.hashtags || []);
      setMentions(draftData.mentions || []);
      setLocation(draftData.location || null);
      
      return draftData;
    } catch (error) {
      throw error;
    }
  };
  
  // Handle publishing
  const handlePublish = async () => {
    if (!title.trim()) {
      setError('Please enter a title for your video');
      return;
    }
    
    setIsPublishing(true);
    setError(null);
    
    try {
      const publishData = {
        videoId,
        title,
        description,
        thumbnailUrl,
        caption,
        hashtags,
        mentions,
        location,
      };
      
      await onPublish(publishData);
      
      setSuccess('Video published successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError('Failed to publish video');
    } finally {
      setIsPublishing(false);
    }
  };
  
  // Handle scheduled publishing
  const handleSchedulePublish = async (date: Date) => {
    if (!title.trim()) {
      throw new Error('Please enter a title for your video');
    }
    
    const publishData = {
      videoId,
      title,
      description,
      thumbnailUrl,
      caption,
      hashtags,
      mentions,
      location,
    };
    
    await onSchedulePublish(date, publishData);
  };
  
  return (
    <div className="w-full max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Create Content</h1>
        <p className="text-gray-600">
          Edit your video and add details before publishing
        </p>
      </div>
      
      {/* Tabs */}
      <div className="mb-6 border-b border-gray-200">
        <div className="flex space-x-4">
          <button
            type="button"
            onClick={() => setActiveTab('edit')}
            className={`py-3 px-4 font-medium text-sm flex items-center ${
              activeTab === 'edit'
                ? 'text-green-600 border-b-2 border-green-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Edit size={16} className="mr-2" />
            Edit Video
          </button>
          
          <button
            type="button"
            onClick={() => setActiveTab('details')}
            className={`py-3 px-4 font-medium text-sm flex items-center ${
              activeTab === 'details'
                ? 'text-green-600 border-b-2 border-green-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Video size={16} className="mr-2" />
            Details & Publishing
          </button>
        </div>
      </div>
      
      {/* Tab content */}
      <div className="mb-8">
        {activeTab === 'edit' ? (
          <div>
            {isEditing ? (
              <VideoEditor
                videoId={videoId}
                onSave={handleEditorSave}
                onCancel={() => setIsEditing(false)}
              />
            ) : (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                <Video size={48} className="mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">Edit Your Video</h3>
                <p className="text-gray-500 mb-4">
                  Trim, add filters, text overlays, and more
                </p>
                <button
                  type="button"
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                >
                  Start Editing
                </button>
              </div>
            )}
            
            <div className="mt-6">
              <ThumbnailGenerator
                videoId={videoId}
                videoUrl={`/api/videos/${videoId}/stream`}
                onThumbnailSelect={handleThumbnailSelect}
              />
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              {/* Basic details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-4 border-b border-gray-200">
                  <h3 className="font-semibold">Basic Details</h3>
                  <p className="text-sm text-gray-500">
                    Add title and description
                  </p>
                </div>
                
                <div className="p-4 space-y-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      Title <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="Add a title that describes your video"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      maxLength={100}
                    />
                    <p className="mt-1 text-xs text-gray-500 text-right">
                      {title.length}/100
                    </p>
                  </div>
                  
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      id="description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="Tell viewers about your video"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent min-h-[100px]"
                      maxLength={5000}
                    />
                    <p className="mt-1 text-xs text-gray-500 text-right">
                      {description.length}/5000
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Caption and hashtags */}
              <CaptionHashtagEditor
                initialCaption={caption}
                initialHashtags={hashtags}
                initialMentions={mentions}
                suggestedHashtags={suggestedHashtags}
                suggestedMentions={suggestedMentions}
                onChange={handleCaptionHashtagsChange}
              />
              
              {/* Location tagging */}
              <LocationTagging
                initialLocation={location}
                popularLocations={popularLocations}
                onLocationSelect={handleLocationSelect}
              />
            </div>
            
            <div className="space-y-6">
              {/* Preview */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-4 border-b border-gray-200">
                  <h3 className="font-semibold">Preview</h3>
                </div>
                
                <div className="p-4">
                  <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-md overflow-hidden mb-3">
                    {thumbnailUrl ? (
                      <img
                        src={thumbnailUrl}
                        alt="Video thumbnail"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Image size={32} className="text-gray-400" />
                      </div>
                    )}
                  </div>
                  
                  <div className="font-medium truncate mb-1">
                    {title || 'Untitled Video'}
                  </div>
                  
                  <div className="text-sm text-gray-500 line-clamp-2 mb-3">
                    {description || 'No description'}
                  </div>
                  
                  <div className="flex flex-wrap gap-1 mb-3">
                    {hashtags.slice(0, 5).map(tag => (
                      <span
                        key={tag.id}
                        className="text-xs text-blue-600"
                      >
                        #{tag.text}
                      </span>
                    ))}
                    {hashtags.length > 5 && (
                      <span className="text-xs text-gray-500">
                        +{hashtags.length - 5} more
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Draft saving */}
              <DraftSaving
                videoId={videoId}
                videoTitle={title || 'Untitled Video'}
                thumbnailUrl={thumbnailUrl}
                metadata={{
                  title,
                  description,
                  caption,
                  hashtags,
                  mentions,
                  location,
                }}
                onSaveDraft={handleSaveDraft}
                onLoadDraft={handleLoadDraft}
                onDeleteDraft={onDeleteDraft}
                onSchedulePublish={handleSchedulePublish}
                existingDrafts={existingDrafts}
              />
              
              {/* Publish button */}
              <button
                type="button"
                onClick={handlePublish}
                disabled={isPublishing || !title.trim()}
                className="w-full px-4 py-3 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center justify-center"
              >
                {isPublishing ? (
                  <>
                    <Loader2 size={18} className="mr-2 animate-spin" />
                    Publishing...
                  </>
                ) : (
                  <>
                    <Upload size={18} className="mr-2" />
                    Publish Now
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* Status messages */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md flex items-center">
          <AlertCircle size={20} className="mr-2 flex-shrink-0" />
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md flex items-center">
          <CheckCircle size={20} className="mr-2 flex-shrink-0" />
          {success}
        </div>
      )}
      
      {/* Navigation buttons */}
      <div className="flex justify-between">
        <button
          type="button"
          onClick={() => setActiveTab(activeTab === 'edit' ? 'details' : 'edit')}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          {activeTab === 'edit' ? (
            <>
              Next: Details
              <ArrowRight size={16} className="ml-2 inline" />
            </>
          ) : (
            <>
              Back to Editing
            </>
          )}
        </button>
      </div>
    </div>
  );
}
