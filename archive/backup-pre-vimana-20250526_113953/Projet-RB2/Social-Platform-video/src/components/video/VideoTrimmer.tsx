import { useState, useRef, useEffect } from 'react';
import { Scissors } from 'lucide-react';

interface VideoTrimmerProps {
  videoUrl: string;
  duration: number;
  initialStart?: number;
  initialEnd?: number;
  onChange: (start: number, end: number) => void;
  onPreview?: (time: number) => void;
  thumbnails?: string[];
}

export function VideoTrimmer({
  videoUrl,
  duration,
  initialStart = 0,
  initialEnd,
  onChange,
  onPreview,
  thumbnails = [],
}: VideoTrimmerProps) {
  const [start, setStart] = useState(initialStart);
  const [end, setEnd] = useState(initialEnd || duration);
  const [isDraggingStart, setIsDraggingStart] = useState(false);
  const [isDraggingEnd, setIsDraggingEnd] = useState(false);
  const [isDraggingRange, setIsDraggingRange] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartValues, setDragStartValues] = useState({ start: 0, end: 0 });
  const [previewTime, setPreviewTime] = useState<number | null>(null);
  
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Format time in MM:SS format
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Convert position to time
  const positionToTime = (position: number) => {
    if (!containerRef.current) return 0;
    
    const containerWidth = containerRef.current.clientWidth;
    const percentage = Math.max(0, Math.min(1, position / containerWidth));
    return percentage * duration;
  };
  
  // Convert time to position
  const timeToPosition = (time: number) => {
    if (!containerRef.current) return 0;
    
    const containerWidth = containerRef.current.clientWidth;
    const percentage = time / duration;
    return percentage * containerWidth;
  };
  
  // Handle mouse down on start handle
  const handleStartHandleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDraggingStart(true);
    setDragStartX(e.clientX);
    setDragStartValues({ start, end });
  };
  
  // Handle mouse down on end handle
  const handleEndHandleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDraggingEnd(true);
    setDragStartX(e.clientX);
    setDragStartValues({ start, end });
  };
  
  // Handle mouse down on range
  const handleRangeMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDraggingRange(true);
    setDragStartX(e.clientX);
    setDragStartValues({ start, end });
  };
  
  // Handle mouse move
  const handleMouseMove = (e: MouseEvent) => {
    if (!containerRef.current) return;
    
    if (isDraggingStart) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const position = e.clientX - containerRect.left;
      const newStart = positionToTime(position);
      
      // Ensure start is not greater than end - 1 second and not less than 0
      const clampedStart = Math.max(0, Math.min(end - 1, newStart));
      setStart(clampedStart);
      onChange(clampedStart, end);
    } else if (isDraggingEnd) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const position = e.clientX - containerRect.left;
      const newEnd = positionToTime(position);
      
      // Ensure end is not less than start + 1 second and not greater than duration
      const clampedEnd = Math.min(duration, Math.max(start + 1, newEnd));
      setEnd(clampedEnd);
      onChange(start, clampedEnd);
    } else if (isDraggingRange) {
      const deltaX = e.clientX - dragStartX;
      const containerWidth = containerRef.current.clientWidth;
      const deltaTime = (deltaX / containerWidth) * duration;
      
      let newStart = dragStartValues.start + deltaTime;
      let newEnd = dragStartValues.end + deltaTime;
      
      // Ensure the range stays within bounds
      if (newStart < 0) {
        const shift = -newStart;
        newStart = 0;
        newEnd = Math.min(duration, dragStartValues.end + deltaTime + shift);
      }
      
      if (newEnd > duration) {
        const shift = newEnd - duration;
        newEnd = duration;
        newStart = Math.max(0, dragStartValues.start + deltaTime - shift);
      }
      
      setStart(newStart);
      setEnd(newEnd);
      onChange(newStart, newEnd);
    }
  };
  
  // Handle mouse up
  const handleMouseUp = () => {
    setIsDraggingStart(false);
    setIsDraggingEnd(false);
    setIsDraggingRange(false);
  };
  
  // Handle timeline click for preview
  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!containerRef.current || isDraggingStart || isDraggingEnd || isDraggingRange) return;
    
    const containerRect = containerRef.current.getBoundingClientRect();
    const position = e.clientX - containerRect.left;
    const time = positionToTime(position);
    
    setPreviewTime(time);
    if (onPreview) {
      onPreview(time);
    }
  };
  
  // Add and remove event listeners
  useEffect(() => {
    if (isDraggingStart || isDraggingEnd || isDraggingRange) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDraggingStart, isDraggingEnd, isDraggingRange]);
  
  // Update when initialStart or initialEnd change
  useEffect(() => {
    if (initialStart !== undefined && initialStart !== start) {
      setStart(initialStart);
    }
    if (initialEnd !== undefined && initialEnd !== end) {
      setEnd(initialEnd);
    }
  }, [initialStart, initialEnd]);
  
  return (
    <div className="w-full">
      <div className="flex justify-between mb-2 text-sm text-gray-500">
        <div>Start: {formatTime(start)}</div>
        <div>Duration: {formatTime(end - start)}</div>
        <div>End: {formatTime(end)}</div>
      </div>
      
      <div
        ref={containerRef}
        className="relative h-16 bg-gray-200 rounded-lg cursor-pointer overflow-hidden"
        onClick={handleTimelineClick}
      >
        {/* Thumbnails */}
        {thumbnails.length > 0 && (
          <div className="absolute inset-0 flex">
            {thumbnails.map((thumbnail, index) => (
              <div
                key={index}
                className="h-full flex-1"
                style={{
                  backgroundImage: `url(${thumbnail})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                }}
              />
            ))}
          </div>
        )}
        
        {/* Video preview frame */}
        {!thumbnails.length && (
          <div className="absolute inset-0 flex items-center justify-center text-gray-400">
            <Scissors size={24} />
            <span className="ml-2">Trim your video</span>
          </div>
        )}
        
        {/* Selected range */}
        <div
          className="absolute h-full bg-green-500 bg-opacity-30 border-l-2 border-r-2 border-green-500"
          style={{
            left: `${(start / duration) * 100}%`,
            width: `${((end - start) / duration) * 100}%`,
          }}
          onMouseDown={handleRangeMouseDown}
        />
        
        {/* Start handle */}
        <div
          className="absolute top-0 bottom-0 w-4 bg-green-500 cursor-ew-resize flex items-center justify-center"
          style={{
            left: `calc(${(start / duration) * 100}% - 8px)`,
          }}
          onMouseDown={handleStartHandleMouseDown}
        >
          <div className="h-8 w-1 bg-white rounded-full" />
        </div>
        
        {/* End handle */}
        <div
          className="absolute top-0 bottom-0 w-4 bg-green-500 cursor-ew-resize flex items-center justify-center"
          style={{
            left: `calc(${(end / duration) * 100}% - 8px)`,
          }}
          onMouseDown={handleEndHandleMouseDown}
        >
          <div className="h-8 w-1 bg-white rounded-full" />
        </div>
        
        {/* Preview marker */}
        {previewTime !== null && (
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-blue-500 pointer-events-none"
            style={{
              left: `${(previewTime / duration) * 100}%`,
            }}
          />
        )}
        
        {/* Time markers */}
        <div className="absolute bottom-0 left-0 right-0 h-4 flex justify-between px-2 text-xs text-gray-600 pointer-events-none">
          {Array.from({ length: 5 }).map((_, i) => {
            const time = (i / 4) * duration;
            return (
              <div key={i} className="flex flex-col items-center">
                <div className="h-1 w-0.5 bg-gray-400 mb-1" />
                {formatTime(time)}
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Controls */}
      <div className="flex justify-between mt-4">
        <button
          onClick={() => {
            setStart(0);
            setEnd(duration);
            onChange(0, duration);
          }}
          className="px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
        >
          Reset
        </button>
        
        <div className="space-x-2">
          <button
            onClick={() => {
              if (onPreview) onPreview(start);
              setPreviewTime(start);
            }}
            className="px-3 py-1 text-xs bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Preview Start
          </button>
          
          <button
            onClick={() => {
              if (onPreview) onPreview(end);
              setPreviewTime(end);
            }}
            className="px-3 py-1 text-xs bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Preview End
          </button>
        </div>
      </div>
    </div>
  );
}
