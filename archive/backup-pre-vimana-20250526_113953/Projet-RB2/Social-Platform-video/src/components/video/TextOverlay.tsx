import { useState, useRef, useEffect } from 'react';
import { X, Move, Type, Clock, Plus } from 'lucide-react';

interface TextOverlayItem {
  id: string;
  text: string;
  position: { x: number; y: number };
  fontSize: number;
  color: string;
  startTime?: number;
  endTime?: number;
}

interface TextOverlayProps {
  overlays: TextOverlayItem[];
  videoDuration: number;
  onAdd: (text: string) => void;
  onUpdate: (id: string, updates: Partial<Omit<TextOverlayItem, 'id'>>) => void;
  onRemove: (id: string) => void;
  previewContainerRef: React.RefObject<HTMLDivElement>;
}

export function TextOverlay({
  overlays,
  videoDuration,
  onAdd,
  onUpdate,
  onRemove,
  previewContainerRef,
}: TextOverlayProps) {
  const [newText, setNewText] = useState('');
  const [selectedOverlay, setSelectedOverlay] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 });
  const [dragStartOverlayPos, setDragStartOverlayPos] = useState({ x: 0, y: 0 });
  
  const formatTime = (seconds: number | undefined) => {
    if (seconds === undefined) return '--:--';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  const handleAddText = () => {
    if (newText.trim()) {
      onAdd(newText.trim());
      setNewText('');
    }
  };
  
  const handleOverlayMouseDown = (
    e: React.MouseEvent,
    overlayId: string,
    position: { x: number; y: number }
  ) => {
    e.preventDefault();
    e.stopPropagation();
    
    setSelectedOverlay(overlayId);
    setIsDragging(true);
    setDragStartPos({ x: e.clientX, y: e.clientY });
    setDragStartOverlayPos(position);
  };
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !selectedOverlay || !previewContainerRef.current) return;
    
    const overlay = overlays.find((o) => o.id === selectedOverlay);
    if (!overlay) return;
    
    const containerRect = previewContainerRef.current.getBoundingClientRect();
    
    // Calculate new position as percentage of container dimensions
    const deltaX = e.clientX - dragStartPos.x;
    const deltaY = e.clientY - dragStartPos.y;
    
    const newX = Math.max(0, Math.min(100, dragStartOverlayPos.x + (deltaX / containerRect.width) * 100));
    const newY = Math.max(0, Math.min(100, dragStartOverlayPos.y + (deltaY / containerRect.height) * 100));
    
    onUpdate(selectedOverlay, {
      position: { x: newX, y: newY },
    });
  };
  
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  // Add and remove event listeners
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, selectedOverlay]);
  
  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">Text Overlays</h3>
        
        <div className="flex mb-3">
          <input
            type="text"
            value={newText}
            onChange={(e) => setNewText(e.target.value)}
            placeholder="Enter text to add"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500"
          />
          <button
            onClick={handleAddText}
            disabled={!newText.trim()}
            className="px-3 py-2 bg-green-500 text-white rounded-r-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
          >
            <Plus size={16} className="mr-1" />
            Add Text
          </button>
        </div>
        
        {overlays.length === 0 ? (
          <div className="text-center py-6 bg-gray-100 rounded-lg text-gray-500">
            <Type size={24} className="mx-auto mb-2" />
            <p>No text overlays added yet.</p>
            <p className="text-sm">Add text above to overlay on your video.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {overlays.map((overlay) => (
              <div
                key={overlay.id}
                className={`p-3 border rounded-lg ${
                  selectedOverlay === overlay.id ? 'border-green-500 bg-green-50' : 'border-gray-300'
                }`}
                onClick={() => setSelectedOverlay(overlay.id)}
              >
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium truncate flex-1">{overlay.text}</div>
                  <div className="flex space-x-1">
                    <button
                      className="p-1 text-gray-500 hover:text-gray-700 rounded"
                      title="Drag to position"
                      onMouseDown={(e) => handleOverlayMouseDown(e, overlay.id, overlay.position)}
                    >
                      <Move size={16} />
                    </button>
                    <button
                      className="p-1 text-red-500 hover:text-red-700 rounded"
                      title="Remove"
                      onClick={() => onRemove(overlay.id)}
                    >
                      <X size={16} />
                    </button>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-2 mb-2">
                  {/* Font Size */}
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Font Size</label>
                    <input
                      type="range"
                      min="12"
                      max="72"
                      value={overlay.fontSize}
                      onChange={(e) => onUpdate(overlay.id, { fontSize: parseInt(e.target.value) })}
                      className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="text-xs text-right mt-1">{overlay.fontSize}px</div>
                  </div>
                  
                  {/* Color */}
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Color</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        value={overlay.color}
                        onChange={(e) => onUpdate(overlay.id, { color: e.target.value })}
                        className="w-8 h-8 rounded cursor-pointer border border-gray-300"
                      />
                      <input
                        type="text"
                        value={overlay.color}
                        onChange={(e) => onUpdate(overlay.id, { color: e.target.value })}
                        className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  {/* Start Time */}
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">
                      <Clock size={12} className="inline mr-1" />
                      Start Time
                    </label>
                    <div className="flex items-center">
                      <input
                        type="range"
                        min="0"
                        max={videoDuration}
                        step="0.1"
                        value={overlay.startTime || 0}
                        onChange={(e) => onUpdate(overlay.id, { startTime: parseFloat(e.target.value) })}
                        className="flex-1 h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
                      />
                      <span className="ml-2 text-xs">{formatTime(overlay.startTime)}</span>
                    </div>
                  </div>
                  
                  {/* End Time */}
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">
                      <Clock size={12} className="inline mr-1" />
                      End Time
                    </label>
                    <div className="flex items-center">
                      <input
                        type="range"
                        min="0"
                        max={videoDuration}
                        step="0.1"
                        value={overlay.endTime || videoDuration}
                        onChange={(e) => onUpdate(overlay.id, { endTime: parseFloat(e.target.value) })}
                        className="flex-1 h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
                      />
                      <span className="ml-2 text-xs">{formatTime(overlay.endTime)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="mt-2 text-xs text-gray-500">
                  Position: {Math.round(overlay.position.x)}% horizontal, {Math.round(overlay.position.y)}% vertical
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="mt-4 text-center text-sm text-gray-500">
        <p>Drag text overlays on the video preview to position them.</p>
        <p>Set timing to control when text appears and disappears.</p>
      </div>
    </div>
  );
}
