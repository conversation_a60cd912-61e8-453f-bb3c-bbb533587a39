import { useState, useEffect } from 'react';
import { 
  createThumbnailJob, 
  getThumbnailJob, 
  selectThumbnail, 
  uploadCustomThumbnail,
  ThumbnailJobStatus,
  ThumbnailMetadata
} from '../../services/video-optimization/thumbnail-generation';
import { Button } from '../ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { Slider } from '../ui/slider';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { 
  Upload, 
  Image, 
  RefreshCw, 
  Check, 
  X, 
  ChevronLeft, 
  ChevronRight,
  Loader2
} from 'lucide-react';

interface ThumbnailGeneratorProps {
  videoId: string;
  videoUrl: string;
  onThumbnailSelect?: (thumbnailUrl: string) => void;
  className?: string;
}

export function ThumbnailGenerator({
  videoId,
  videoUrl,
  onThumbnailSelect,
  className = '',
}: ThumbnailGeneratorProps) {
  const [activeTab, setActiveTab] = useState<'auto' | 'custom'>('auto');
  const [jobId, setJobId] = useState<string | null>(null);
  const [thumbnails, setThumbnails] = useState<ThumbnailMetadata[]>([]);
  const [selectedThumbnailIndex, setSelectedThumbnailIndex] = useState<number | null>(null);
  const [jobStatus, setJobStatus] = useState<ThumbnailJobStatus | null>(null);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [customFile, setCustomFile] = useState<File | null>(null);
  const [customThumbnailUrl, setCustomThumbnailUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [thumbnailsPerPage] = useState(3);
  
  // Options for thumbnail generation
  const [options, setOptions] = useState({
    count: 9,
    faceDetection: true,
    autoSelect: true,
  });
  
  // Generate thumbnails
  const generateThumbnails = () => {
    setError(null);
    setThumbnails([]);
    setSelectedThumbnailIndex(null);
    
    try {
      const job = createThumbnailJob(videoId, videoUrl, {
        count: options.count,
        faceDetection: options.faceDetection,
        autoSelect: options.autoSelect,
      });
      
      setJobId(job.id);
      setJobStatus(job.status);
      setProgress(job.progress);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate thumbnails');
    }
  };
  
  // Poll for job status
  useEffect(() => {
    if (!jobId || jobStatus === ThumbnailJobStatus.COMPLETED || jobStatus === ThumbnailJobStatus.FAILED) {
      return;
    }
    
    const intervalId = setInterval(() => {
      const job = getThumbnailJob(jobId);
      
      if (!job) {
        clearInterval(intervalId);
        setError('Thumbnail job not found');
        return;
      }
      
      setJobStatus(job.status);
      setProgress(job.progress);
      
      if (job.status === ThumbnailJobStatus.COMPLETED) {
        setThumbnails(job.thumbnails);
        setSelectedThumbnailIndex(job.selectedThumbnailIndex || null);
        
        if (job.selectedThumbnailIndex !== undefined && onThumbnailSelect) {
          onThumbnailSelect(job.thumbnails[job.selectedThumbnailIndex].url);
        }
      } else if (job.status === ThumbnailJobStatus.FAILED) {
        setError(job.error || 'Failed to generate thumbnails');
      }
    }, 500);
    
    return () => {
      clearInterval(intervalId);
    };
  }, [jobId, jobStatus]);
  
  // Handle thumbnail selection
  const handleThumbnailSelect = (index: number) => {
    if (!jobId) return;
    
    setSelectedThumbnailIndex(index);
    selectThumbnail(jobId, index);
    
    if (onThumbnailSelect && thumbnails[index]) {
      onThumbnailSelect(thumbnails[index].url);
    }
  };
  
  // Handle custom thumbnail upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    setCustomFile(file);
    setCustomThumbnailUrl(URL.createObjectURL(file));
  };
  
  // Upload custom thumbnail
  const uploadThumbnail = async () => {
    if (!customFile) return;
    
    setIsUploading(true);
    setError(null);
    
    try {
      const url = await uploadCustomThumbnail(videoId, customFile);
      setCustomThumbnailUrl(url);
      
      if (onThumbnailSelect) {
        onThumbnailSelect(url);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload thumbnail');
    } finally {
      setIsUploading(false);
    }
  };
  
  // Handle pagination
  const totalPages = Math.ceil(thumbnails.length / thumbnailsPerPage);
  const paginatedThumbnails = thumbnails.slice(
    currentPage * thumbnailsPerPage,
    (currentPage + 1) * thumbnailsPerPage
  );
  
  const nextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  const prevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold">Video Thumbnail</h3>
        <p className="text-sm text-gray-500">
          Choose a thumbnail for your video
        </p>
      </div>
      
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'auto' | 'custom')}>
        <TabsList className="w-full">
          <TabsTrigger value="auto" className="flex-1">Auto-Generate</TabsTrigger>
          <TabsTrigger value="custom" className="flex-1">Upload Custom</TabsTrigger>
        </TabsList>
        
        <TabsContent value="auto" className="p-4">
          {/* Options */}
          <div className="mb-4 space-y-4">
            <div>
              <Label htmlFor="thumbnail-count">Number of thumbnails</Label>
              <div className="flex items-center space-x-2">
                <Slider
                  id="thumbnail-count"
                  value={[options.count]}
                  min={3}
                  max={15}
                  step={3}
                  onValueChange={(value) => setOptions({ ...options, count: value[0] })}
                  className="flex-1"
                />
                <span className="w-8 text-center">{options.count}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="face-detection"
                checked={options.faceDetection}
                onCheckedChange={(checked) => 
                  setOptions({ ...options, faceDetection: checked === true })
                }
              />
              <Label htmlFor="face-detection">Use face detection</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="auto-select"
                checked={options.autoSelect}
                onCheckedChange={(checked) => 
                  setOptions({ ...options, autoSelect: checked === true })
                }
              />
              <Label htmlFor="auto-select">Auto-select best thumbnail</Label>
            </div>
            
            <Button 
              onClick={generateThumbnails}
              disabled={jobStatus === ThumbnailJobStatus.PROCESSING}
              className="w-full"
            >
              {jobStatus === ThumbnailJobStatus.PROCESSING ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating ({progress}%)
                </>
              ) : jobId ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Regenerate Thumbnails
                </>
              ) : (
                <>
                  <Image className="mr-2 h-4 w-4" />
                  Generate Thumbnails
                </>
              )}
            </Button>
          </div>
          
          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
              {error}
            </div>
          )}
          
          {/* Thumbnails */}
          {thumbnails.length > 0 && (
            <div className="space-y-4">
              <h4 className="font-medium">Select a thumbnail</h4>
              
              <div className="grid grid-cols-1 gap-4">
                {paginatedThumbnails.map((thumbnail, index) => {
                  const actualIndex = currentPage * thumbnailsPerPage + index;
                  return (
                    <div
                      key={thumbnail.url}
                      className={`relative border-2 rounded-md overflow-hidden cursor-pointer transition-all ${
                        selectedThumbnailIndex === actualIndex
                          ? 'border-green-500 shadow-md'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleThumbnailSelect(actualIndex)}
                    >
                      <img
                        src={thumbnail.url}
                        alt={`Thumbnail ${actualIndex + 1}`}
                        className="w-full h-auto"
                      />
                      
                      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-20 transition-opacity">
                        {selectedThumbnailIndex === actualIndex && (
                          <div className="bg-green-500 text-white p-2 rounded-full">
                            <Check size={20} />
                          </div>
                        )}
                      </div>
                      
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1">
                        {thumbnail.timestamp.toFixed(1)}s
                        {thumbnail.hasfaces && (
                          <span className="ml-2 px-1 bg-blue-500 rounded">Face</span>
                        )}
                        {thumbnail.isAutoSelected && (
                          <span className="ml-2 px-1 bg-green-500 rounded">Auto</span>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2 mt-4">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={prevPage}
                    disabled={currentPage === 0}
                  >
                    <ChevronLeft size={16} />
                  </Button>
                  
                  <span className="text-sm">
                    {currentPage + 1} / {totalPages}
                  </span>
                  
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={nextPage}
                    disabled={currentPage === totalPages - 1}
                  >
                    <ChevronRight size={16} />
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="custom" className="p-4">
          <div className="space-y-4">
            {/* Upload area */}
            <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
              {customThumbnailUrl ? (
                <div className="space-y-4">
                  <img
                    src={customThumbnailUrl}
                    alt="Custom thumbnail"
                    className="max-h-48 mx-auto"
                  />
                  
                  <div className="flex justify-center space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setCustomFile(null);
                        setCustomThumbnailUrl(null);
                      }}
                    >
                      <X className="mr-2 h-4 w-4" />
                      Remove
                    </Button>
                    
                    {customFile && (
                      <Button
                        onClick={uploadThumbnail}
                        disabled={isUploading}
                      >
                        {isUploading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Upload className="mr-2 h-4 w-4" />
                            Upload
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="text-sm text-gray-500">
                    Drag and drop an image, or click to browse
                  </p>
                  <p className="text-xs text-gray-400">
                    Recommended: 1280×720px, JPG or PNG
                  </p>
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                    id="thumbnail-upload"
                  />
                  <Label htmlFor="thumbnail-upload">
                    <Button variant="outline" className="mt-2" asChild>
                      <span>
                        <Upload className="mr-2 h-4 w-4" />
                        Browse Files
                      </span>
                    </Button>
                  </Label>
                </div>
              )}
            </div>
            
            {/* Error message */}
            {error && (
              <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm">
                {error}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
