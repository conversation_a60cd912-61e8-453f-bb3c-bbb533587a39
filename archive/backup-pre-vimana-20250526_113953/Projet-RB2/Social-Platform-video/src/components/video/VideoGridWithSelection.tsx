import React, { useState } from 'react';
import { 
  SelectionProvider, 
  SelectableGrid, 
  SelectionToggle, 
  SelectionToolbar 
} from '../selection';
import { Video } from '../../types';
import { Trash2, Share2, FolderPlus, Star } from 'lucide-react';

// Props pour la grille de vidéos avec sélection
interface VideoGridWithSelectionProps {
  // Liste des vidéos à afficher
  videos: Video[];
  // Classe CSS pour la grille
  className?: string;
  // Nombre de colonnes par taille d'écran
  columns?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  // Fonction appelée lorsqu'une vidéo est cliquée
  onVideoClick?: (video: Video) => void;
  // Actions personnalisées pour la barre d'outils de sélection
  selectionActions?: Array<{
    icon: React.ReactNode;
    label: string;
    onClick: (selectedVideos: Video[]) => void;
    disabled?: boolean;
    show?: (count: number) => boolean;
    textColor?: string;
  }>;
}

// Composant pour la grille de vidéos avec sélection
export function VideoGridWithSelection({
  videos,
  className = '',
  columns = { sm: 1, md: 2, lg: 3, xl: 4 },
  onVideoClick,
  selectionActions,
}: VideoGridWithSelectionProps) {
  // État pour stocker l'ID de la vidéo survolée
  const [hoveredVideoId, setHoveredVideoId] = useState<string | null>(null);

  // Actions par défaut pour la sélection de vidéos
  const defaultActions = [
    {
      icon: <Trash2 size={18} />,
      label: 'Supprimer',
      onClick: (selectedVideos: Video[]) => {
        console.log('Supprimer vidéos:', selectedVideos);
        // Implémenter la logique de suppression ici
      },
      textColor: 'text-red-500',
    },
    {
      icon: <Share2 size={18} />,
      label: 'Partager',
      onClick: (selectedVideos: Video[]) => {
        console.log('Partager vidéos:', selectedVideos);
        // Implémenter la logique de partage ici
      },
    },
    {
      icon: <FolderPlus size={18} />,
      label: 'Ajouter à la collection',
      onClick: (selectedVideos: Video[]) => {
        console.log('Ajouter à la collection:', selectedVideos);
        // Implémenter la logique d'ajout à la collection ici
      },
    },
    {
      icon: <Star size={18} />,
      label: 'Favoris',
      onClick: (selectedVideos: Video[]) => {
        console.log('Ajouter aux favoris:', selectedVideos);
        // Implémenter la logique d'ajout aux favoris ici
      },
      show: (count: number) => count > 0,
    },
  ];

  // Fonction pour gérer le clic sur une vidéo
  const handleVideoClick = (video: Video, isSelected: boolean) => {
    // Si on est en mode de sélection, ne pas naviguer vers la vidéo
    if (isSelected) return;
    
    // Sinon, appeler le callback onVideoClick
    if (onVideoClick) {
      onVideoClick(video);
    }
  };

  // Fonction pour rendre une carte de vidéo
  const renderVideoCard = (video: Video, isSelected: boolean) => {
    const isHovered = hoveredVideoId === video.id;
    
    return (
      <div 
        className={`
          relative rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200
          ${isHovered ? 'scale-[1.02]' : 'scale-100'}
        `}
        onMouseEnter={() => setHoveredVideoId(video.id)}
        onMouseLeave={() => setHoveredVideoId(null)}
        onClick={() => handleVideoClick(video, isSelected)}
      >
        {/* Vignette de la vidéo */}
        <div className="relative aspect-video">
          <img 
            src={video.thumbnailUrl} 
            alt={video.title} 
            className="w-full h-full object-cover"
          />
          {/* Durée de la vidéo */}
          <span className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded">
            {video.duration}
          </span>
        </div>
        
        {/* Informations sur la vidéo */}
        <div className="p-3">
          <h3 className="font-medium text-gray-800 line-clamp-2">
            {video.title}
          </h3>
          
          <div className="flex items-center mt-2">
            <img 
              src={video.author.avatar} 
              alt={video.author.name} 
              className="w-7 h-7 rounded-full mr-2"
            />
            <span className="text-sm text-gray-600">
              {video.author.name}
            </span>
          </div>
          
          <div className="flex items-center text-xs text-gray-500 mt-2">
            <span>{video.views} vues</span>
            <span className="mx-1">•</span>
            <span>{new Date(video.createdAt).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <SelectionProvider<Video> initialMode="none">
      <div className="relative">
        {/* Bouton pour activer/désactiver la sélection */}
        <div className="mb-4 flex justify-end">
          <SelectionToggle />
        </div>
        
        {/* Grille de vidéos sélectionnables */}
        <SelectableGrid<Video>
          items={videos}
          keyExtractor={(video) => video.id}
          renderItem={renderVideoCard}
          columns={columns}
          className={className}
          itemClassName="relative"
          selectedItemClassName="ring-2 ring-blue-500 bg-blue-50"
          gap="1.5rem"
        />
        
        {/* Barre d'outils de sélection */}
        <SelectionToolbar<Video>
          actions={selectionActions || defaultActions}
          position="bottom"
          fixed={true}
          className="border-t border-gray-200"
        />
      </div>
    </SelectionProvider>
  );
} 