import { useState, useRef, useEffect } from 'react';
import { 
  Scissors, 
  Sliders, 
  Type, 
  Music, 
  Play, 
  Pause, 
  RotateCcw, 
  Save, 
  Image, 
  Loader2 
} from 'lucide-react';
import { VideoTrimmer } from './VideoTrimmer';
import { VideoFilters } from './VideoFilters';
import { TextOverlay } from './TextOverlay';
import { useVideoEditorStore } from '../../store/videoEditor';

interface VideoEditorProps {
  videoId: string;
  onSave: () => void;
  onCancel: () => void;
}

export function VideoEditor({ videoId, onSave, onCancel }: VideoEditorProps) {
  const {
    videoMetadata,
    videoUrl,
    trimRange,
    selectedFilter,
    customFilterValues,
    textOverlays,
    selectedAudioTrack,
    availableFilters,
    availableAudioTracks,
    currentTab,
    previewWithEdits,
    isLoading,
    isProcessing,
    processingProgress,
    error,
    
    loadVideo,
    setTrimRange,
    setSelectedFilter,
    updateCustomFilterValues,
    addTextOverlay,
    updateTextOverlay,
    removeTextOverlay,
    setSelectedAudioTrack,
    updateAudioTrackSettings,
    setCurrentTab,
    togglePreviewWithEdits,
    applyEdits,
    generateThumbnail,
    resetEditor,
  } = useVideoEditorStore();
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [thumbnails, setThumbnails] = useState<string[]>([]);
  const [generatingThumbnails, setGeneratingThumbnails] = useState(false);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const videoContainerRef = useRef<HTMLDivElement>(null);
  
  // Load video on mount
  useEffect(() => {
    loadVideo(videoId);
    
    return () => {
      resetEditor();
    };
  }, [videoId]);
  
  // Update video playback when trim range changes
  useEffect(() => {
    if (videoRef.current && trimRange) {
      if (currentTime < trimRange.start || currentTime > trimRange.end) {
        videoRef.current.currentTime = trimRange.start;
        setCurrentTime(trimRange.start);
      }
    }
  }, [trimRange]);
  
  // Handle play/pause
  const togglePlay = () => {
    if (!videoRef.current) return;
    
    if (isPlaying) {
      videoRef.current.pause();
    } else {
      // If at the end of trim range, go back to start
      if (trimRange && currentTime >= trimRange.end) {
        videoRef.current.currentTime = trimRange.start;
      }
      videoRef.current.play();
    }
    
    setIsPlaying(!isPlaying);
  };
  
  // Handle time update
  const handleTimeUpdate = () => {
    if (!videoRef.current) return;
    
    const time = videoRef.current.currentTime;
    setCurrentTime(time);
    
    // If we're past the trim end, pause the video
    if (trimRange && time >= trimRange.end) {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };
  
  // Handle seeking
  const handleSeek = (time: number) => {
    if (!videoRef.current) return;
    
    videoRef.current.currentTime = time;
    setCurrentTime(time);
  };
  
  // Generate thumbnails for the trimmer
  const generateThumbnails = async () => {
    if (!videoMetadata || !videoMetadata.duration) return;
    
    setGeneratingThumbnails(true);
    
    try {
      // Generate 5 thumbnails evenly spaced throughout the video
      const thumbnailCount = 5;
      const thumbnailPromises = [];
      
      for (let i = 0; i < thumbnailCount; i++) {
        const time = (i / (thumbnailCount - 1)) * videoMetadata.duration;
        thumbnailPromises.push(generateThumbnail(time));
      }
      
      const thumbnailUrls = await Promise.all(thumbnailPromises);
      setThumbnails(thumbnailUrls);
    } catch (error) {
      console.error('Failed to generate thumbnails:', error);
    } finally {
      setGeneratingThumbnails(false);
    }
  };
  
  // Generate thumbnails when video is loaded
  useEffect(() => {
    if (videoMetadata && videoUrl && !thumbnails.length && !generatingThumbnails) {
      generateThumbnails();
    }
  }, [videoMetadata, videoUrl]);
  
  // Get CSS filter string based on current filter settings
  const getFilterStyle = () => {
    if (!previewWithEdits) return {};
    
    const filterValues = selectedFilter?.values || customFilterValues;
    const {
      brightness = 100,
      contrast = 100,
      saturation = 100,
      blur = 0,
      sepia = 0,
    } = filterValues;
    
    return {
      filter: `
        brightness(${brightness}%)
        contrast(${contrast}%)
        saturate(${saturation}%)
        blur(${blur / 10}px)
        sepia(${sepia}%)
      `,
    };
  };
  
  // Format time in MM:SS format
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 size={32} className="animate-spin text-green-500 mb-4" />
        <p className="text-gray-500">Loading video...</p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-red-500">
        <p className="mb-4">Error: {error}</p>
        <button
          onClick={() => loadVideo(videoId)}
          className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  if (isProcessing) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="w-48 h-2 bg-gray-200 rounded-full mb-4 overflow-hidden">
          <div
            className="h-full bg-green-500 transition-all duration-300"
            style={{ width: `${processingProgress}%` }}
          />
        </div>
        <p className="text-gray-500 mb-2">Processing video... {Math.round(processingProgress)}%</p>
        <p className="text-sm text-gray-400">This may take a few minutes.</p>
      </div>
    );
  }
  
  if (!videoMetadata || !videoUrl) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <p>No video loaded.</p>
      </div>
    );
  }
  
  return (
    <div className="w-full">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">Edit Video</h2>
        <p className="text-gray-500">{videoMetadata.title || 'Untitled Video'}</p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Preview */}
        <div className="lg:col-span-2">
          <div className="bg-black rounded-lg overflow-hidden relative" ref={videoContainerRef}>
            <video
              ref={videoRef}
              src={videoUrl}
              className="w-full h-auto"
              style={getFilterStyle()}
              onTimeUpdate={handleTimeUpdate}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              onEnded={() => setIsPlaying(false)}
            />
            
            {/* Text Overlays */}
            {previewWithEdits && textOverlays.map((overlay) => (
              <div
                key={overlay.id}
                className="absolute pointer-events-none select-none"
                style={{
                  left: `${overlay.position.x}%`,
                  top: `${overlay.position.y}%`,
                  transform: 'translate(-50%, -50%)',
                  fontSize: `${overlay.fontSize}px`,
                  color: overlay.color,
                  textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)',
                  display:
                    (overlay.startTime === undefined || currentTime >= overlay.startTime) &&
                    (overlay.endTime === undefined || currentTime <= overlay.endTime)
                      ? 'block'
                      : 'none',
                }}
              >
                {overlay.text}
              </div>
            ))}
          </div>
          
          {/* Video Controls */}
          <div className="mt-2 flex items-center space-x-2">
            <button
              onClick={togglePlay}
              className="p-2 rounded-full bg-green-500 text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              {isPlaying ? <Pause size={20} /> : <Play size={20} />}
            </button>
            
            <div className="flex-1 flex items-center space-x-2">
              <span className="text-sm text-gray-500">{formatTime(currentTime)}</span>
              <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500"
                  style={{ width: `${(currentTime / videoMetadata.duration) * 100}%` }}
                />
              </div>
              <span className="text-sm text-gray-500">{formatTime(videoMetadata.duration)}</span>
            </div>
            
            <button
              onClick={togglePreviewWithEdits}
              className={`p-2 rounded-full ${
                previewWithEdits
                  ? 'bg-blue-500 text-white hover:bg-blue-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
              title={previewWithEdits ? 'Hide edits preview' : 'Show edits preview'}
            >
              <Image size={20} />
            </button>
          </div>
        </div>
        
        {/* Editing Tools */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            {/* Tabs */}
            <div className="flex border-b">
              <button
                onClick={() => setCurrentTab('trim')}
                className={`flex-1 py-3 px-4 text-sm font-medium flex items-center justify-center ${
                  currentTab === 'trim'
                    ? 'text-green-500 border-b-2 border-green-500'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Scissors size={16} className="mr-2" />
                Trim
              </button>
              
              <button
                onClick={() => setCurrentTab('filters')}
                className={`flex-1 py-3 px-4 text-sm font-medium flex items-center justify-center ${
                  currentTab === 'filters'
                    ? 'text-green-500 border-b-2 border-green-500'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Sliders size={16} className="mr-2" />
                Filters
              </button>
              
              <button
                onClick={() => setCurrentTab('text')}
                className={`flex-1 py-3 px-4 text-sm font-medium flex items-center justify-center ${
                  currentTab === 'text'
                    ? 'text-green-500 border-b-2 border-green-500'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Type size={16} className="mr-2" />
                Text
              </button>
              
              <button
                onClick={() => setCurrentTab('audio')}
                className={`flex-1 py-3 px-4 text-sm font-medium flex items-center justify-center ${
                  currentTab === 'audio'
                    ? 'text-green-500 border-b-2 border-green-500'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Music size={16} className="mr-2" />
                Audio
              </button>
            </div>
            
            {/* Tab Content */}
            <div className="p-4">
              {currentTab === 'trim' && (
                <VideoTrimmer
                  videoUrl={videoUrl}
                  duration={videoMetadata.duration}
                  initialStart={trimRange?.start}
                  initialEnd={trimRange?.end}
                  onChange={setTrimRange}
                  onPreview={handleSeek}
                  thumbnails={thumbnails}
                />
              )}
              
              {currentTab === 'filters' && (
                <VideoFilters
                  filters={availableFilters}
                  selectedFilter={selectedFilter}
                  customValues={customFilterValues}
                  onSelectFilter={setSelectedFilter}
                  onCustomValuesChange={updateCustomFilterValues}
                  onApplyFilter={() => {}} // This is handled by the save button
                />
              )}
              
              {currentTab === 'text' && (
                <TextOverlay
                  overlays={textOverlays}
                  videoDuration={videoMetadata.duration}
                  onAdd={addTextOverlay}
                  onUpdate={updateTextOverlay}
                  onRemove={removeTextOverlay}
                  previewContainerRef={videoContainerRef}
                />
              )}
              
              {currentTab === 'audio' && (
                <div className="text-center py-8 text-gray-500">
                  <Music size={32} className="mx-auto mb-2" />
                  <p>Audio editing coming soon!</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Action Buttons */}
      <div className="flex justify-end space-x-4 mt-8">
        <button
          onClick={resetEditor}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center"
        >
          <RotateCcw size={18} className="mr-2" />
          Reset All
        </button>
        
        <button
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          Cancel
        </button>
        
        <button
          onClick={async () => {
            await applyEdits();
            onSave();
          }}
          className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center"
        >
          <Save size={18} className="mr-2" />
          Save Changes
        </button>
      </div>
    </div>
  );
}
