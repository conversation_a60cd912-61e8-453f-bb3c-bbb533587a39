import { useState } from 'react';
import { Sliders } from 'lucide-react';

interface FilterOption {
  id: string;
  name: string;
  preview: string;
  values: {
    brightness?: number;
    contrast?: number;
    saturation?: number;
    blur?: number;
    sepia?: number;
  };
}

interface VideoFiltersProps {
  filters: FilterOption[];
  selectedFilter: FilterOption | null;
  customValues: {
    brightness: number;
    contrast: number;
    saturation: number;
    blur: number;
    sepia: number;
  };
  onSelectFilter: (filterId: string | null) => void;
  onCustomValuesChange: (values: Partial<VideoFiltersProps['customValues']>) => void;
  onApplyFilter: () => void;
}

export function VideoFilters({
  filters,
  selectedFilter,
  customValues,
  onSelectFilter,
  onCustomValuesChange,
  onApplyFilter,
}: VideoFiltersProps) {
  const [showCustomControls, setShowCustomControls] = useState(false);
  
  const handleFilterClick = (filterId: string) => {
    if (selectedFilter?.id === filterId) {
      onSelectFilter(null);
    } else {
      onSelectFilter(filterId);
    }
  };
  
  const handleSliderChange = (
    property: keyof VideoFiltersProps['customValues'],
    value: number
  ) => {
    onCustomValuesChange({ [property]: value });
  };
  
  // Get CSS filter string for previews
  const getFilterStyle = (values: Partial<VideoFiltersProps['customValues']>) => {
    const {
      brightness = 100,
      contrast = 100,
      saturation = 100,
      blur = 0,
      sepia = 0,
    } = values;
    
    return {
      filter: `
        brightness(${brightness}%)
        contrast(${contrast}%)
        saturate(${saturation}%)
        blur(${blur / 10}px)
        sepia(${sepia}%)
      `,
    };
  };
  
  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">Preset Filters</h3>
        <div className="grid grid-cols-4 gap-3">
          {/* Original/No filter option */}
          <div
            className={`relative cursor-pointer rounded-lg overflow-hidden ${
              !selectedFilter ? 'ring-2 ring-green-500' : ''
            }`}
            onClick={() => onSelectFilter(null)}
          >
            <div className="aspect-w-1 aspect-h-1 bg-gray-200">
              <img
                src="https://via.placeholder.com/100"
                alt="Original"
                className="object-cover"
              />
            </div>
            <div className="p-1 text-center text-xs font-medium">Original</div>
          </div>
          
          {/* Filter options */}
          {filters.map((filter) => (
            <div
              key={filter.id}
              className={`relative cursor-pointer rounded-lg overflow-hidden ${
                selectedFilter?.id === filter.id ? 'ring-2 ring-green-500' : ''
              }`}
              onClick={() => handleFilterClick(filter.id)}
            >
              <div className="aspect-w-1 aspect-h-1 bg-gray-200">
                <img
                  src={filter.preview}
                  alt={filter.name}
                  className="object-cover"
                  style={getFilterStyle(filter.values)}
                />
              </div>
              <div className="p-1 text-center text-xs font-medium">{filter.name}</div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium">Custom Adjustments</h3>
          <button
            onClick={() => setShowCustomControls(!showCustomControls)}
            className="text-sm text-green-500 flex items-center"
          >
            <Sliders size={16} className="mr-1" />
            {showCustomControls ? 'Hide Controls' : 'Show Controls'}
          </button>
        </div>
        
        {showCustomControls && (
          <div className="space-y-4 p-4 bg-gray-100 rounded-lg">
            {/* Brightness */}
            <div>
              <div className="flex justify-between mb-1">
                <label className="text-sm font-medium">Brightness</label>
                <span className="text-sm text-gray-500">{customValues.brightness}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="200"
                value={customValues.brightness}
                onChange={(e) => handleSliderChange('brightness', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
            </div>
            
            {/* Contrast */}
            <div>
              <div className="flex justify-between mb-1">
                <label className="text-sm font-medium">Contrast</label>
                <span className="text-sm text-gray-500">{customValues.contrast}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="200"
                value={customValues.contrast}
                onChange={(e) => handleSliderChange('contrast', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
            </div>
            
            {/* Saturation */}
            <div>
              <div className="flex justify-between mb-1">
                <label className="text-sm font-medium">Saturation</label>
                <span className="text-sm text-gray-500">{customValues.saturation}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="200"
                value={customValues.saturation}
                onChange={(e) => handleSliderChange('saturation', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
            </div>
            
            {/* Blur */}
            <div>
              <div className="flex justify-between mb-1">
                <label className="text-sm font-medium">Blur</label>
                <span className="text-sm text-gray-500">{customValues.blur / 10}px</span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={customValues.blur}
                onChange={(e) => handleSliderChange('blur', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
            </div>
            
            {/* Sepia */}
            <div>
              <div className="flex justify-between mb-1">
                <label className="text-sm font-medium">Sepia</label>
                <span className="text-sm text-gray-500">{customValues.sepia}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={customValues.sepia}
                onChange={(e) => handleSliderChange('sepia', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer"
              />
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={() => {
                  onCustomValuesChange({
                    brightness: 100,
                    contrast: 100,
                    saturation: 100,
                    blur: 0,
                    sepia: 0,
                  });
                }}
                className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 mr-2"
              >
                Reset
              </button>
              
              <button
                onClick={onApplyFilter}
                className="px-3 py-1 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                Apply
              </button>
            </div>
          </div>
        )}
      </div>
      
      <div className="mt-6">
        <div className="text-center text-sm text-gray-500">
          <p>Preview your changes in real-time on the video player.</p>
          <p>Click "Apply" to save your filter settings.</p>
        </div>
      </div>
    </div>
  );
}
