import { useState } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { Heart, MessageCircle, MoreHorizontal, ChevronDown, ChevronUp } from 'lucide-react';
import { Avatar } from '../ui/avatar';
import { CommentForm } from './CommentForm';

export interface Comment {
  id: string;
  content: string;
  createdAt: string;
  author: {
    id: string;
    name: string;
    avatar: string;
  };
  likes: number;
  isLiked: boolean;
  replies?: Comment[];
}

interface CommentItemProps {
  comment: Comment;
  onLike: (commentId: string) => void;
  onReply: (content: { content: string }, parentId: string) => void;
  depth?: number;
  maxDepth?: number;
}

export function CommentItem({ 
  comment, 
  onLike, 
  onReply, 
  depth = 0, 
  maxDepth = 3 
}: CommentItemProps) {
  const [showReplies, setShowReplies] = useState(depth === 0);
  const [isReplying, setIsReplying] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  
  const hasReplies = comment.replies && comment.replies.length > 0;
  const canNest = depth < maxDepth;
  
  const handleReply = (data: { content: string }) => {
    onReply(data, comment.id);
    setIsReplying(false);
  };

  return (
    <div className={`${depth > 0 ? 'ml-6 mt-3 border-l-2 border-gray-200 pl-3' : 'mt-4'}`}>
      <div className="flex items-start space-x-3">
        <Avatar 
          src={comment.author.avatar} 
          alt={comment.author.name} 
          className="w-8 h-8"
        />
        
        <div className="flex-1">
          <div className="bg-gray-100 rounded-lg p-3">
            <div className="flex justify-between items-start">
              <span className="font-medium text-sm">{comment.author.name}</span>
              <div className="relative">
                <button 
                  onClick={() => setShowOptions(!showOptions)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <MoreHorizontal size={16} />
                </button>
                
                {showOptions && (
                  <div className="absolute right-0 mt-1 bg-white border border-gray-200 rounded shadow-lg z-10 w-32">
                    <button className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100">
                      Report
                    </button>
                    <button className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100">
                      Copy text
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            <p className="text-sm mt-1">{comment.content}</p>
          </div>
          
          <div className="flex items-center mt-1 space-x-4 text-xs text-gray-500">
            <span>{formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}</span>
            
            <button 
              onClick={() => onLike(comment.id)}
              className={`flex items-center space-x-1 ${comment.isLiked ? 'text-red-500' : ''}`}
            >
              <Heart size={14} fill={comment.isLiked ? 'currentColor' : 'none'} />
              <span>{comment.likes}</span>
            </button>
            
            <button 
              onClick={() => setIsReplying(!isReplying)}
              className="flex items-center space-x-1"
            >
              <MessageCircle size={14} />
              <span>Reply</span>
            </button>
          </div>
          
          {isReplying && canNest && (
            <div className="mt-3">
              <CommentForm 
                postId={comment.id} 
                parentId={comment.id}
                onCommentSubmit={handleReply}
                placeholder="Write a reply..."
              />
            </div>
          )}
          
          {hasReplies && (
            <div className="mt-2">
              <button 
                onClick={() => setShowReplies(!showReplies)}
                className="flex items-center text-xs text-gray-500 hover:text-gray-700"
              >
                {showReplies ? (
                  <>
                    <ChevronUp size={14} className="mr-1" />
                    Hide replies ({comment.replies?.length})
                  </>
                ) : (
                  <>
                    <ChevronDown size={14} className="mr-1" />
                    Show replies ({comment.replies?.length})
                  </>
                )}
              </button>
              
              {showReplies && comment.replies?.map((reply) => (
                <CommentItem
                  key={reply.id}
                  comment={reply}
                  onLike={onLike}
                  onReply={onReply}
                  depth={depth + 1}
                  maxDepth={maxDepth}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
