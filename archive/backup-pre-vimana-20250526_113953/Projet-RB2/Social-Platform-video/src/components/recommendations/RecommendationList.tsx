import { useEffect, useState } from 'react';
import { 
  Chevron<PERSON>eft, 
  ChevronRight, 
  Loader2, 
  Refresh<PERSON>w, 
  <PERSON>ert<PERSON>ircle, 
  Filter, 
  X 
} from 'lucide-react';
import { useRecommendationsStore } from '@/store/recommendations';
import { RecommendationCard } from './RecommendationCard';
import { SearchResult } from '@/api/searchApi';

interface RecommendationListProps {
  title: string;
  type: 'feed' | 'similar' | 'interestBased' | 'topic';
  contentId?: string;
  contentType?: 'video' | 'post' | 'livestream';
  topicId?: string;
  interests?: string[];
  limit?: number;
  showControls?: boolean;
  showFilters?: boolean;
  layout?: 'grid' | 'carousel' | 'list';
  cardSize?: 'sm' | 'md' | 'lg';
  className?: string;
  onItemSelect?: (item: SearchResult) => void;
}

export function RecommendationList({
  title,
  type,
  contentId,
  contentType,
  topicId,
  interests,
  limit = 10,
  showControls = true,
  showFilters = false,
  layout = 'carousel',
  cardSize = 'md',
  className = '',
  onItemSelect,
}: RecommendationListProps) {
  const {
    feedItems,
    similarContent,
    interestBasedContent,
    topicContent,
    isLoading,
    error,
    fetchPersonalizedFeed,
    fetchSimilarContent,
    fetchInterestBasedContent,
    fetchTopicContent,
    loadMoreFeedItems,
    loadMoreInterestBasedContent,
    loadMoreTopicContent,
  } = useRecommendationsStore();
  
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [activeFilters, setActiveFilters] = useState<{
    contentType?: 'video' | 'post' | 'livestream';
    duration?: 'short' | 'medium' | 'long';
    sortBy?: 'recent' | 'popular' | 'trending';
  }>({});
  
  // Get the appropriate content based on type
  const getContent = (): SearchResult[] => {
    switch (type) {
      case 'feed':
        return feedItems;
      case 'similar':
        return similarContent;
      case 'interestBased':
        return interestBasedContent;
      case 'topic':
        return topicContent;
      default:
        return [];
    }
  };
  
  const content = getContent();
  
  // Fetch content on mount
  useEffect(() => {
    const fetchContent = async () => {
      switch (type) {
        case 'feed':
          await fetchPersonalizedFeed(1, limit);
          break;
        case 'similar':
          if (contentId && contentType) {
            await fetchSimilarContent(contentId, contentType, limit);
          }
          break;
        case 'interestBased':
          if (interests && interests.length > 0) {
            await fetchInterestBasedContent(interests, 1, limit);
          }
          break;
        case 'topic':
          if (topicId) {
            await fetchTopicContent(topicId, 1, limit);
          }
          break;
      }
    };
    
    fetchContent();
  }, [
    type,
    contentId,
    contentType,
    topicId,
    interests,
    limit,
    fetchPersonalizedFeed,
    fetchSimilarContent,
    fetchInterestBasedContent,
    fetchTopicContent,
  ]);
  
  // Load more content
  const handleLoadMore = async () => {
    switch (type) {
      case 'feed':
        await loadMoreFeedItems();
        break;
      case 'interestBased':
        await loadMoreInterestBasedContent();
        break;
      case 'topic':
        await loadMoreTopicContent();
        break;
    }
  };
  
  // Refresh content
  const handleRefresh = async () => {
    switch (type) {
      case 'feed':
        await fetchPersonalizedFeed(1, limit);
        break;
      case 'similar':
        if (contentId && contentType) {
          await fetchSimilarContent(contentId, contentType, limit);
        }
        break;
      case 'interestBased':
        if (interests && interests.length > 0) {
          await fetchInterestBasedContent(interests, 1, limit);
        }
        break;
      case 'topic':
        if (topicId) {
          await fetchTopicContent(topicId, 1, limit);
        }
        break;
    }
  };
  
  // Scroll carousel
  const scrollCarousel = (direction: 'left' | 'right') => {
    const carousel = document.getElementById(`carousel-${title.replace(/\s+/g, '-').toLowerCase()}`);
    if (carousel) {
      const scrollAmount = direction === 'left' ? -carousel.clientWidth : carousel.clientWidth;
      carousel.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };
  
  // Apply filters
  const applyFilters = (filters: typeof activeFilters) => {
    setActiveFilters(filters);
    setShowFilterMenu(false);
    
    // Re-fetch content with filters
    // This would need to be implemented in the actual API calls
    handleRefresh();
  };
  
  // Reset filters
  const resetFilters = () => {
    setActiveFilters({});
    setShowFilterMenu(false);
    handleRefresh();
  };
  
  // Get layout classes
  const getLayoutClasses = () => {
    switch (layout) {
      case 'grid':
        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4';
      case 'list':
        return 'flex flex-col space-y-4';
      case 'carousel':
      default:
        return 'flex space-x-4 overflow-x-auto pb-4 hide-scrollbar';
    }
  };
  
  return (
    <div className={`${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">{title}</h2>
        
        {showControls && (
          <div className="flex items-center space-x-2">
            {showFilters && (
              <div className="relative">
                <button
                  onClick={() => setShowFilterMenu(!showFilterMenu)}
                  className={`p-1.5 rounded-md ${
                    Object.keys(activeFilters).length > 0
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-gray-100 text-gray-700'
                  } hover:bg-gray-200 focus:outline-none`}
                  title="Filter"
                >
                  <Filter size={16} />
                </button>
                
                {showFilterMenu && (
                  <div className="absolute right-0 mt-1 w-64 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                    <div className="p-3">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-sm font-medium">Filters</h3>
                        <button
                          onClick={() => setShowFilterMenu(false)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <X size={16} />
                        </button>
                      </div>
                      
                      {/* Content Type Filter */}
                      <div className="mb-3">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Content Type
                        </label>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => applyFilters({ ...activeFilters, contentType: 'video' })}
                            className={`px-2 py-1 text-xs rounded-md ${
                              activeFilters.contentType === 'video'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Videos
                          </button>
                          <button
                            onClick={() => applyFilters({ ...activeFilters, contentType: 'post' })}
                            className={`px-2 py-1 text-xs rounded-md ${
                              activeFilters.contentType === 'post'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Posts
                          </button>
                          <button
                            onClick={() => applyFilters({ ...activeFilters, contentType: 'livestream' })}
                            className={`px-2 py-1 text-xs rounded-md ${
                              activeFilters.contentType === 'livestream'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Livestreams
                          </button>
                        </div>
                      </div>
                      
                      {/* Duration Filter */}
                      <div className="mb-3">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Duration
                        </label>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => applyFilters({ ...activeFilters, duration: 'short' })}
                            className={`px-2 py-1 text-xs rounded-md ${
                              activeFilters.duration === 'short'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Short
                          </button>
                          <button
                            onClick={() => applyFilters({ ...activeFilters, duration: 'medium' })}
                            className={`px-2 py-1 text-xs rounded-md ${
                              activeFilters.duration === 'medium'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Medium
                          </button>
                          <button
                            onClick={() => applyFilters({ ...activeFilters, duration: 'long' })}
                            className={`px-2 py-1 text-xs rounded-md ${
                              activeFilters.duration === 'long'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Long
                          </button>
                        </div>
                      </div>
                      
                      {/* Sort By Filter */}
                      <div className="mb-3">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Sort By
                        </label>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => applyFilters({ ...activeFilters, sortBy: 'recent' })}
                            className={`px-2 py-1 text-xs rounded-md ${
                              activeFilters.sortBy === 'recent'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Recent
                          </button>
                          <button
                            onClick={() => applyFilters({ ...activeFilters, sortBy: 'popular' })}
                            className={`px-2 py-1 text-xs rounded-md ${
                              activeFilters.sortBy === 'popular'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Popular
                          </button>
                          <button
                            onClick={() => applyFilters({ ...activeFilters, sortBy: 'trending' })}
                            className={`px-2 py-1 text-xs rounded-md ${
                              activeFilters.sortBy === 'trending'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Trending
                          </button>
                        </div>
                      </div>
                      
                      {/* Action Buttons */}
                      <div className="flex justify-between">
                        <button
                          onClick={resetFilters}
                          className="px-2 py-1 text-xs text-gray-700 hover:text-gray-900"
                        >
                          Reset
                        </button>
                        <button
                          onClick={() => setShowFilterMenu(false)}
                          className="px-2 py-1 text-xs bg-blue-500 text-white rounded-md hover:bg-blue-600"
                        >
                          Apply
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none disabled:opacity-50"
              title="Refresh"
            >
              <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            </button>
            
            {layout === 'carousel' && (
              <>
                <button
                  onClick={() => scrollCarousel('left')}
                  className="p-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
                  title="Scroll left"
                >
                  <ChevronLeft size={16} />
                </button>
                <button
                  onClick={() => scrollCarousel('right')}
                  className="p-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
                  title="Scroll right"
                >
                  <ChevronRight size={16} />
                </button>
              </>
            )}
          </div>
        )}
      </div>
      
      {/* Active Filters */}
      {Object.keys(activeFilters).length > 0 && (
        <div className="flex items-center mb-4 space-x-2">
          <span className="text-xs text-gray-500">Filters:</span>
          
          {activeFilters.contentType && (
            <div className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full flex items-center">
              {activeFilters.contentType === 'video' ? 'Videos' : 
               activeFilters.contentType === 'post' ? 'Posts' : 'Livestreams'}
              <button
                onClick={() => applyFilters({ ...activeFilters, contentType: undefined })}
                className="ml-1 text-blue-500 hover:text-blue-700"
              >
                <X size={12} />
              </button>
            </div>
          )}
          
          {activeFilters.duration && (
            <div className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full flex items-center">
              {activeFilters.duration === 'short' ? 'Short' : 
               activeFilters.duration === 'medium' ? 'Medium' : 'Long'} Duration
              <button
                onClick={() => applyFilters({ ...activeFilters, duration: undefined })}
                className="ml-1 text-blue-500 hover:text-blue-700"
              >
                <X size={12} />
              </button>
            </div>
          )}
          
          {activeFilters.sortBy && (
            <div className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full flex items-center">
              Sort: {activeFilters.sortBy === 'recent' ? 'Recent' : 
                    activeFilters.sortBy === 'popular' ? 'Popular' : 'Trending'}
              <button
                onClick={() => applyFilters({ ...activeFilters, sortBy: undefined })}
                className="ml-1 text-blue-500 hover:text-blue-700"
              >
                <X size={12} />
              </button>
            </div>
          )}
          
          <button
            onClick={resetFilters}
            className="text-xs text-blue-500 hover:text-blue-700"
          >
            Clear all
          </button>
        </div>
      )}
      
      {/* Content */}
      {isLoading && content.length === 0 ? (
        <div className="flex justify-center items-center h-48">
          <Loader2 size={24} className="animate-spin text-blue-500" />
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center h-48 text-red-500">
          <AlertCircle size={24} className="mb-2" />
          <p className="text-sm">{error}</p>
          <button
            onClick={handleRefresh}
            className="mt-2 px-3 py-1 text-xs bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none"
          >
            Try Again
          </button>
        </div>
      ) : content.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-48 text-gray-500">
          <p className="text-sm">No content available</p>
        </div>
      ) : (
        <div 
          id={`carousel-${title.replace(/\s+/g, '-').toLowerCase()}`}
          className={getLayoutClasses()}
        >
          {content.map((item) => (
            <RecommendationCard
              key={item.id}
              item={item}
              showReason={true}
              size={cardSize}
              onSelect={onItemSelect}
            />
          ))}
        </div>
      )}
      
      {/* Load More Button (for grid and list layouts) */}
      {layout !== 'carousel' && content.length > 0 && (
        <div className="mt-4 text-center">
          <button
            onClick={handleLoadMore}
            disabled={isLoading}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <Loader2 size={16} className="inline-block animate-spin mr-2" />
                Loading...
              </>
            ) : (
              'Load More'
            )}
          </button>
        </div>
      )}
    </div>
  );
}
