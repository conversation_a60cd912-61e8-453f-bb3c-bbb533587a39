import { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  Heart, 
  MessageCircle, 
  Share2, 
  Clock, 
  Play, 
  MoreHorizontal, 
  ThumbsUp, 
  ThumbsDown, 
  X, 
  User, 
  Eye, 
  Flag, 
  Bookmark, 
  CheckCircle 
} from 'lucide-react';
import { useRecommendationsStore } from '@/store/recommendations';
import { SearchResult } from '@/api/searchApi';
import { formatRelativeTime } from '@/lib/utils';

interface RecommendationCardProps {
  item: SearchResult;
  showReason?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onSelect?: (item: SearchResult) => void;
}

export function RecommendationCard({
  item,
  showReason = false,
  size = 'md',
  className = '',
  onSelect,
}: RecommendationCardProps) {
  const { provideFeedback } = useRecommendationsStore();
  
  const [showFeedbackMenu, setShowFeedbackMenu] = useState(false);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState<string | null>(null);
  
  // Handle feedback submission
  const handleFeedback = async (feedback: 'like' | 'dislike' | 'not_interested' | 'hide_creator' | 'report') => {
    try {
      await provideFeedback(item.id, feedback);
      setFeedbackSubmitted(feedback);
      setShowFeedbackMenu(false);
      
      // Reset feedback status after 3 seconds
      setTimeout(() => {
        setFeedbackSubmitted(null);
      }, 3000);
    } catch (error) {
      console.error('Error providing feedback:', error);
    }
  };
  
  // Format duration
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes < 60) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    return `${hours}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Get size-specific classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          card: 'w-48',
          thumbnail: 'h-28',
          title: 'text-sm',
          meta: 'text-xs',
        };
      case 'lg':
        return {
          card: 'w-80',
          thumbnail: 'h-44',
          title: 'text-lg',
          meta: 'text-sm',
        };
      case 'md':
      default:
        return {
          card: 'w-64',
          thumbnail: 'h-36',
          title: 'text-base',
          meta: 'text-xs',
        };
    }
  };
  
  const sizeClasses = getSizeClasses();
  
  // Handle card click
  const handleCardClick = () => {
    if (onSelect) {
      onSelect(item);
    }
  };
  
  return (
    <div 
      className={`bg-white rounded-lg shadow-sm overflow-hidden ${sizeClasses.card} ${className} ${
        feedbackSubmitted === 'not_interested' || feedbackSubmitted === 'hide_creator' 
          ? 'opacity-50 pointer-events-none' 
          : ''
      }`}
    >
      {/* Thumbnail */}
      <div className="relative">
        <Link 
          to={`/${item.type}s/${item.id}`} 
          className="block"
          onClick={handleCardClick}
        >
          <div className={`${sizeClasses.thumbnail} bg-gray-200 relative overflow-hidden`}>
            {item.thumbnailUrl ? (
              <img 
                src={item.thumbnailUrl} 
                alt={item.title} 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-300">
                {item.type === 'video' ? (
                  <Play size={24} className="text-gray-500" />
                ) : (
                  <User size={24} className="text-gray-500" />
                )}
              </div>
            )}
            
            {/* Duration badge for videos */}
            {item.type === 'video' && item.duration && (
              <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded">
                {formatDuration(item.duration)}
              </div>
            )}
            
            {/* Live badge for livestreams */}
            {item.type === 'livestream' && (
              <div className="absolute top-1 left-1 bg-red-500 text-white text-xs px-1 py-0.5 rounded flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>
                LIVE
              </div>
            )}
            
            {/* New badge */}
            {item.isNew && (
              <div className="absolute top-1 right-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded">
                NEW
              </div>
            )}
            
            {/* Trending badge */}
            {item.isTrending && !item.isNew && (
              <div className="absolute top-1 right-1 bg-orange-500 text-white text-xs px-1 py-0.5 rounded flex items-center">
                <span className="mr-0.5">↑</span>
                TRENDING
              </div>
            )}
          </div>
        </Link>
        
        {/* Feedback button */}
        <div className="absolute top-1 right-1">
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowFeedbackMenu(!showFeedbackMenu);
              }}
              className="p-1 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 focus:outline-none"
            >
              <MoreHorizontal size={16} />
            </button>
            
            {/* Feedback menu */}
            {showFeedbackMenu && (
              <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                <div className="py-1">
                  <button
                    onClick={() => handleFeedback('like')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <ThumbsUp size={14} className="mr-2 text-green-500" />
                    Like this recommendation
                  </button>
                  <button
                    onClick={() => handleFeedback('dislike')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <ThumbsDown size={14} className="mr-2 text-red-500" />
                    Dislike this recommendation
                  </button>
                  <button
                    onClick={() => handleFeedback('not_interested')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <X size={14} className="mr-2 text-gray-500" />
                    Not interested
                  </button>
                  <button
                    onClick={() => handleFeedback('hide_creator')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <User size={14} className="mr-2 text-gray-500" />
                    Don't recommend this creator
                  </button>
                  <button
                    onClick={() => handleFeedback('report')}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  >
                    <Flag size={14} className="mr-2 text-orange-500" />
                    Report
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Content */}
      <div className="p-3">
        <Link 
          to={`/${item.type}s/${item.id}`}
          className="block"
          onClick={handleCardClick}
        >
          <h3 className={`${sizeClasses.title} font-medium line-clamp-2 mb-1`}>
            {item.title}
          </h3>
        </Link>
        
        <div className="flex items-center justify-between mb-2">
          <Link 
            to={`/profile/${item.creator.id}`} 
            className={`${sizeClasses.meta} text-gray-600 hover:text-gray-900 flex items-center`}
          >
            {item.creator.name}
            {item.creator.isVerified && (
              <CheckCircle size={12} className="ml-1 text-blue-500" />
            )}
          </Link>
          
          <div className={`${sizeClasses.meta} text-gray-500 flex items-center`}>
            <Eye size={12} className="mr-1" />
            {item.views > 1000 
              ? `${(item.views / 1000).toFixed(1)}K` 
              : item.views}
          </div>
        </div>
        
        {/* Stats */}
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <span className={`${sizeClasses.meta} text-gray-500 flex items-center`}>
              <Heart size={12} className="mr-1" />
              {item.likes}
            </span>
            
            <span className={`${sizeClasses.meta} text-gray-500 flex items-center`}>
              <MessageCircle size={12} className="mr-1" />
              {item.comments}
            </span>
          </div>
          
          <span className={`${sizeClasses.meta} text-gray-500 flex items-center`}>
            <Clock size={12} className="mr-1" />
            {formatRelativeTime(new Date(item.createdAt))}
          </span>
        </div>
        
        {/* Recommendation reason */}
        {showReason && item.recommendationReason && (
          <div className="mt-2 pt-2 border-t border-gray-100">
            <p className={`${sizeClasses.meta} text-gray-500 italic`}>
              {item.recommendationReason}
            </p>
          </div>
        )}
      </div>
      
      {/* Feedback confirmation */}
      {feedbackSubmitted && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-3 rounded-md text-center">
            {feedbackSubmitted === 'like' && (
              <div className="flex flex-col items-center">
                <ThumbsUp size={24} className="text-green-500 mb-2" />
                <p className="text-sm">Thanks for your feedback!</p>
              </div>
            )}
            
            {feedbackSubmitted === 'dislike' && (
              <div className="flex flex-col items-center">
                <ThumbsDown size={24} className="text-red-500 mb-2" />
                <p className="text-sm">We'll improve your recommendations</p>
              </div>
            )}
            
            {feedbackSubmitted === 'not_interested' && (
              <div className="flex flex-col items-center">
                <X size={24} className="text-gray-500 mb-2" />
                <p className="text-sm">You won't see this again</p>
              </div>
            )}
            
            {feedbackSubmitted === 'hide_creator' && (
              <div className="flex flex-col items-center">
                <User size={24} className="text-gray-500 mb-2" />
                <p className="text-sm">Creator hidden from recommendations</p>
              </div>
            )}
            
            {feedbackSubmitted === 'report' && (
              <div className="flex flex-col items-center">
                <Flag size={24} className="text-orange-500 mb-2" />
                <p className="text-sm">Report submitted</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
