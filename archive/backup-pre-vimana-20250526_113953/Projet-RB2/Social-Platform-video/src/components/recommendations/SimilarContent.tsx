import { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Loader2, Clock, Eye, MessageSquare, ThumbsUp } from 'lucide-react';
import { useRecommendationsStore } from '../../store/recommendations';
import { Avatar } from '../ui/avatar';

interface SimilarContentProps {
  contentId: string;
  contentType: 'video' | 'post' | 'livestream';
  limit?: number;
  className?: string;
  layout?: 'grid' | 'list';
}

export function SimilarContent({
  contentId,
  contentType,
  limit = 6,
  className = '',
  layout = 'grid',
}: SimilarContentProps) {
  const { 
    similarContent, 
    isLoading, 
    error, 
    fetchSimilarContent 
  } = useRecommendationsStore();
  
  useEffect(() => {
    fetchSimilarContent(contentId, contentType, limit);
  }, [contentId, contentType, limit]);
  
  // Format duration in MM:SS format
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else if (diffDays < 30) {
      return `${Math.floor(diffDays / 7)} weeks ago`;
    } else {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
      }).format(date);
    }
  };
  
  // Format number with K/M suffix
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };
  
  if (isLoading && similarContent.length === 0) {
    return (
      <div className={`flex justify-center items-center h-40 ${className}`}>
        <Loader2 size={24} className="animate-spin text-green-500" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={`p-4 text-center text-red-500 bg-red-50 rounded-lg ${className}`}>
        <p>Error: {error}</p>
        <button
          onClick={() => fetchSimilarContent(contentId, contentType, limit)}
          className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  if (similarContent.length === 0) {
    return (
      <div className={`p-6 text-center text-gray-500 bg-gray-50 rounded-lg ${className}`}>
        <p>No similar content found.</p>
      </div>
    );
  }
  
  return (
    <div className={className}>
      <h2 className="text-lg font-semibold mb-4">More Like This</h2>
      
      {layout === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {similarContent.map((item) => (
            <Link
              key={item.id}
              to={`/${item.type}s/${item.id}`}
              className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow"
            >
              <div className="relative aspect-video bg-gray-100">
                <img
                  src={item.thumbnailUrl}
                  alt={item.title}
                  className="w-full h-full object-cover"
                />
                {item.type === 'video' && item.duration && (
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 rounded">
                    {formatDuration(item.duration)}
                  </div>
                )}
                {item.isLive && (
                  <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center">
                    <span className="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>
                    LIVE
                  </div>
                )}
              </div>
              <div className="p-3">
                <h3 className="font-medium text-sm line-clamp-2">{item.title}</h3>
                <p className="text-xs text-gray-500 mt-1">{item.creator.name}</p>
                <div className="flex items-center text-xs text-gray-500 mt-1">
                  <span>{formatNumber(item.stats.views)} views</span>
                  <span className="mx-1">•</span>
                  <span>{formatDate(item.createdAt)}</span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="space-y-3">
          {similarContent.map((item) => (
            <Link
              key={item.id}
              to={`/${item.type}s/${item.id}`}
              className="flex bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow"
            >
              <div className="relative w-40 h-24 flex-shrink-0 bg-gray-100">
                <img
                  src={item.thumbnailUrl}
                  alt={item.title}
                  className="w-full h-full object-cover"
                />
                {item.type === 'video' && item.duration && (
                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded">
                    {formatDuration(item.duration)}
                  </div>
                )}
              </div>
              <div className="p-3 flex-1">
                <h3 className="font-medium text-sm line-clamp-2">{item.title}</h3>
                <p className="text-xs text-gray-500 mt-1">{item.creator.name}</p>
                <div className="flex items-center text-xs text-gray-500 mt-1">
                  <div className="flex items-center mr-2">
                    <Eye size={12} className="mr-1" />
                    {formatNumber(item.stats.views)}
                  </div>
                  <div className="flex items-center mr-2">
                    <ThumbsUp size={12} className="mr-1" />
                    {formatNumber(item.stats.likes)}
                  </div>
                  <div className="flex items-center">
                    <Clock size={12} className="mr-1" />
                    {formatDate(item.createdAt)}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
}
