import { useEffect, useState } from 'react';
import { 
  Setting<PERSON>, 
  Save, 
  Loader2, 
  AlertCircle, 
  CheckCircle, 
  X, 
  Plus, 
  Clock, 
  Globe, 
  Sparkles, 
  Users 
} from 'lucide-react';
import { useRecommendationsStore } from '@/store/recommendations';

interface RecommendationPreferencesProps {
  className?: string;
}

export function RecommendationPreferences({ className = '' }: RecommendationPreferencesProps) {
  const {
    preferences,
    isLoading,
    isUpdatingPreferences,
    error,
    fetchPreferences,
    updatePreferences,
  } = useRecommendationsStore();
  
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [excludedTags, setExcludedTags] = useState<string[]>([]);
  const [excludedCreators, setExcludedCreators] = useState<string[]>([]);
  const [preferredDuration, setPreferredDuration] = useState<'short' | 'medium' | 'long' | undefined>(undefined);
  const [preferredLanguages, setPreferredLanguages] = useState<string[]>([]);
  const [preferNewContent, setPreferNewContent] = useState<boolean>(false);
  const [preferFollowingContent, setPreferFollowingContent] = useState<boolean>(false);
  
  const [newTag, setNewTag] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  
  // Available categories and languages (would come from an API in a real implementation)
  const availableCategories = [
    'Yoga', 'Meditation', 'Fitness', 'Nutrition', 'Wellness', 
    'Mindfulness', 'Travel', 'Nature', 'Spirituality', 'Health',
    'Cooking', 'Art', 'Music', 'Dance', 'Photography'
  ];
  
  const availableLanguages = [
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'French' },
    { code: 'es', name: 'Spanish' },
    { code: 'de', name: 'German' },
    { code: 'it', name: 'Italian' },
    { code: 'pt', name: 'Portuguese' },
    { code: 'ja', name: 'Japanese' },
    { code: 'zh', name: 'Chinese' },
    { code: 'ru', name: 'Russian' },
    { code: 'ar', name: 'Arabic' }
  ];
  
  // Fetch preferences on mount
  useEffect(() => {
    fetchPreferences();
  }, [fetchPreferences]);
  
  // Update local state when preferences are loaded
  useEffect(() => {
    if (preferences) {
      setSelectedCategories(preferences.categories);
      setExcludedTags(preferences.excludedTags);
      setExcludedCreators(preferences.excludedCreators);
      setPreferredDuration(preferences.contentPreferences.preferredDuration);
      setPreferredLanguages(preferences.contentPreferences.preferredLanguages || []);
      setPreferNewContent(preferences.contentPreferences.preferNewContent || false);
      setPreferFollowingContent(preferences.contentPreferences.preferFollowingContent || false);
    }
  }, [preferences]);
  
  // Handle category toggle
  const handleCategoryToggle = (category: string) => {
    if (selectedCategories.includes(category)) {
      setSelectedCategories(selectedCategories.filter((c) => c !== category));
    } else {
      setSelectedCategories([...selectedCategories, category]);
    }
  };
  
  // Handle language toggle
  const handleLanguageToggle = (languageCode: string) => {
    if (preferredLanguages.includes(languageCode)) {
      setPreferredLanguages(preferredLanguages.filter((l) => l !== languageCode));
    } else {
      setPreferredLanguages([...preferredLanguages, languageCode]);
    }
  };
  
  // Handle add tag
  const handleAddTag = () => {
    if (newTag.trim() && !excludedTags.includes(newTag.trim())) {
      setExcludedTags([...excludedTags, newTag.trim()]);
      setNewTag('');
    }
  };
  
  // Handle remove tag
  const handleRemoveTag = (tag: string) => {
    setExcludedTags(excludedTags.filter((t) => t !== tag));
  };
  
  // Handle remove creator
  const handleRemoveCreator = (creatorId: string) => {
    setExcludedCreators(excludedCreators.filter((c) => c !== creatorId));
  };
  
  // Handle save preferences
  const handleSavePreferences = async () => {
    try {
      await updatePreferences({
        categories: selectedCategories,
        excludedTags,
        excludedCreators,
        contentPreferences: {
          preferredDuration,
          preferredLanguages,
          preferNewContent,
          preferFollowingContent,
        },
      });
      
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving preferences:', error);
    }
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold flex items-center">
          <Settings size={18} className="mr-2 text-gray-500" />
          Recommendation Preferences
        </h2>
        
        <button
          onClick={handleSavePreferences}
          disabled={isUpdatingPreferences}
          className="px-3 py-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
        >
          {isUpdatingPreferences ? (
            <>
              <Loader2 size={16} className="mr-1.5 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save size={16} className="mr-1.5" />
              Save Preferences
            </>
          )}
        </button>
      </div>
      
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-start">
          <CheckCircle size={18} className="text-green-500 mr-2 flex-shrink-0 mt-0.5" />
          <div>
            <p className="text-sm font-medium text-green-800">Preferences saved successfully!</p>
            <p className="text-xs text-green-700 mt-1">
              Your recommendation preferences have been updated. It may take a few minutes for changes to take effect.
            </p>
          </div>
        </div>
      )}
      
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
          <AlertCircle size={18} className="text-red-500 mr-2 flex-shrink-0 mt-0.5" />
          <div>
            <p className="text-sm font-medium text-red-800">Error</p>
            <p className="text-xs text-red-700 mt-1">{error}</p>
          </div>
        </div>
      )}
      
      {isLoading && !preferences ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 size={24} className="animate-spin text-blue-500" />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Categories */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
              <Sparkles size={16} className="mr-1.5 text-blue-500" />
              Content Categories
            </h3>
            <p className="text-xs text-gray-500 mb-3">
              Select categories you're interested in to improve your recommendations
            </p>
            
            <div className="flex flex-wrap gap-2">
              {availableCategories.map((category) => (
                <button
                  key={category}
                  onClick={() => handleCategoryToggle(category)}
                  className={`px-3 py-1.5 text-sm rounded-full ${
                    selectedCategories.includes(category)
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
          
          {/* Excluded Tags */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">
              Excluded Tags
            </h3>
            <p className="text-xs text-gray-500 mb-3">
              Add tags for content you don't want to see in your recommendations
            </p>
            
            <div className="flex mb-2">
              <input
                type="text"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag to exclude"
                className="flex-1 px-3 py-1.5 text-sm border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleAddTag();
                  }
                }}
              />
              <button
                onClick={handleAddTag}
                className="px-3 py-1.5 bg-blue-500 text-white text-sm rounded-r-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <Plus size={16} />
              </button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {excludedTags.map((tag) => (
                <div
                  key={tag}
                  className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-full flex items-center"
                >
                  {tag}
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1.5 text-red-500 hover:text-red-700"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))}
              
              {excludedTags.length === 0 && (
                <p className="text-xs text-gray-500 italic">No excluded tags</p>
              )}
            </div>
          </div>
          
          {/* Excluded Creators */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">
              Excluded Creators
            </h3>
            <p className="text-xs text-gray-500 mb-3">
              Creators whose content you've chosen not to see
            </p>
            
            <div className="space-y-2">
              {excludedCreators.length === 0 ? (
                <p className="text-xs text-gray-500 italic">No excluded creators</p>
              ) : (
                excludedCreators.map((creatorId) => (
                  <div
                    key={creatorId}
                    className="p-2 border border-gray-200 rounded-md flex items-center justify-between"
                  >
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-gray-200 rounded-full mr-2"></div>
                      <div>
                        <p className="text-sm font-medium">Creator {creatorId}</p>
                        <p className="text-xs text-gray-500">@username</p>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => handleRemoveCreator(creatorId)}
                      className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))
              )}
            </div>
          </div>
          
          {/* Content Preferences */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
              <Settings size={16} className="mr-1.5 text-gray-500" />
              Content Preferences
            </h3>
            
            {/* Duration Preference */}
            <div className="mb-4">
              <label className="block text-xs font-medium text-gray-700 mb-1 flex items-center">
                <Clock size={14} className="mr-1 text-gray-500" />
                Preferred Duration
              </label>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => setPreferredDuration('short')}
                  className={`px-3 py-1.5 text-xs rounded-md ${
                    preferredDuration === 'short'
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  Short (&lt;5 min)
                </button>
                <button
                  onClick={() => setPreferredDuration('medium')}
                  className={`px-3 py-1.5 text-xs rounded-md ${
                    preferredDuration === 'medium'
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  Medium (5-20 min)
                </button>
                <button
                  onClick={() => setPreferredDuration('long')}
                  className={`px-3 py-1.5 text-xs rounded-md ${
                    preferredDuration === 'long'
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  Long (&gt;20 min)
                </button>
                <button
                  onClick={() => setPreferredDuration(undefined)}
                  className={`px-3 py-1.5 text-xs rounded-md ${
                    preferredDuration === undefined
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  No Preference
                </button>
              </div>
            </div>
            
            {/* Language Preference */}
            <div className="mb-4">
              <label className="block text-xs font-medium text-gray-700 mb-1 flex items-center">
                <Globe size={14} className="mr-1 text-gray-500" />
                Preferred Languages
              </label>
              
              <div className="flex flex-wrap gap-2">
                {availableLanguages.map((language) => (
                  <button
                    key={language.code}
                    onClick={() => handleLanguageToggle(language.code)}
                    className={`px-3 py-1 text-xs rounded-md ${
                      preferredLanguages.includes(language.code)
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                    }`}
                  >
                    {language.name}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Other Preferences */}
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={preferNewContent}
                  onChange={() => setPreferNewContent(!preferNewContent)}
                  className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Prioritize new content</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={preferFollowingContent}
                  onChange={() => setPreferFollowingContent(!preferFollowingContent)}
                  className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 flex items-center">
                  <Users size={14} className="mr-1 text-gray-500" />
                  Prioritize content from creators I follow
                </span>
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
