import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Sparkles, 
  TrendingUp, 
  ChevronRight, 
  Loader2, 
  AlertCircle 
} from 'lucide-react';
import { useRecommendationsStore } from '@/store/recommendations';
import { RecommendationCard } from './RecommendationCard';

interface HomePageRecommendationsProps {
  className?: string;
}

export function HomePageRecommendations({ className = '' }: HomePageRecommendationsProps) {
  const {
    feedItems,
    trendingTopics,
    isLoading,
    error,
    fetchPersonalizedFeed,
    fetchTrendingTopics,
  } = useRecommendationsStore();
  
  // Fetch recommendations on mount
  useEffect(() => {
    fetchPersonalizedFeed(10);
    fetchTrendingTopics(10);
  }, [fetchPersonalizedFeed, fetchTrendingTopics]);
  
  return (
    <div className={`space-y-8 ${className}`}>
      {/* For You Section */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center">
            <Sparkles size={20} className="text-blue-500 mr-2" />
            For You
          </h2>
          
          <Link 
            to="/recommendations?tab=forYou" 
            className="text-sm text-blue-500 hover:text-blue-700 flex items-center"
          >
            View All
            <ChevronRight size={16} className="ml-1" />
          </Link>
        </div>
        
        {isLoading && feedItems.length === 0 ? (
          <div className="flex justify-center items-center h-48">
            <Loader2 size={24} className="animate-spin text-blue-500" />
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-48 text-red-500">
            <AlertCircle size={24} className="mb-2" />
            <p className="text-sm">{error}</p>
            <button
              onClick={() => fetchPersonalizedFeed(10)}
              className="mt-2 px-3 py-1 text-xs bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none"
            >
              Try Again
            </button>
          </div>
        ) : feedItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-48 text-gray-500">
            <p className="text-sm">No recommendations available</p>
            <Link
              to="/recommendations?tab=preferences"
              className="mt-2 text-sm text-blue-500 hover:underline"
            >
              Update your preferences
            </Link>
          </div>
        ) : (
          <div className="flex space-x-4 overflow-x-auto pb-4 hide-scrollbar">
            {feedItems.map((item) => (
              <RecommendationCard
                key={item.id}
                item={item}
                size="md"
                showReason={false}
              />
            ))}
          </div>
        )}
      </section>
      
      {/* Trending Section */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center">
            <TrendingUp size={20} className="text-orange-500 mr-2" />
            Trending Now
          </h2>
          
          <Link 
            to="/recommendations?tab=trending" 
            className="text-sm text-blue-500 hover:text-blue-700 flex items-center"
          >
            View All
            <ChevronRight size={16} className="ml-1" />
          </Link>
        </div>
        
        {isLoading && trendingTopics.length === 0 ? (
          <div className="flex justify-center items-center h-48">
            <Loader2 size={24} className="animate-spin text-blue-500" />
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-48 text-red-500">
            <AlertCircle size={24} className="mb-2" />
            <p className="text-sm">{error}</p>
            <button
              onClick={() => fetchTrendingTopics(10)}
              className="mt-2 px-3 py-1 text-xs bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none"
            >
              Try Again
            </button>
          </div>
        ) : trendingTopics.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-48 text-gray-500">
            <p className="text-sm">No trending content available</p>
            <p className="text-xs text-gray-400">Check back later for the latest trends</p>
          </div>
        ) : (
          <div className="flex space-x-4 overflow-x-auto pb-4 hide-scrollbar">
            {trendingTopics.map((topic) => (
              <div key={topic.id} className="p-2 border rounded shadow-sm w-64 text-sm">
                <p className="font-semibold">{topic.name}</p>
                <p className="text-xs text-gray-400">Count: {topic.count}, Trend: {topic.trend.toFixed(2)}</p>
                <p className="text-xs mt-1">(Placeholder for Trending Topic)</p>
              </div>
            ))}
          </div>
        )}
      </section>
      
      {/* Call to Action */}
      <section className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="mb-4 md:mb-0">
            <h3 className="text-xl font-bold mb-2">Discover More Content</h3>
            <p className="text-blue-100">
              Get personalized recommendations based on your interests and viewing history
            </p>
          </div>
          
          <Link
            to="/recommendations"
            className="px-6 py-2 bg-white text-blue-600 font-medium rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-500"
          >
            Explore Recommendations
          </Link>
        </div>
      </section>
    </div>
  );
}
