import { create } from 'zustand';
import { 
  archivePost, 
  restorePost, 
  deletePost, 
  getArchivedPosts, 
  reportPost, 
  updatePost, 
  getPostDetails,
  likePost,
  savePost,
  sharePost
} from '../api/contentApi';

interface ContentState {
  posts: any[];
  archivedPosts: any[];
  currentPost: any | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  archivePost: (postId: string) => Promise<void>;
  restorePost: (postId: string) => Promise<void>;
  deletePost: (postId: string) => Promise<void>;
  fetchArchivedPosts: (page?: number, limit?: number) => Promise<void>;
  reportPost: (postId: string, reason: string) => Promise<void>;
  updatePost: (postId: string, data: any) => Promise<void>;
  fetchPostDetails: (postId: string) => Promise<void>;
  likePost: (postId: string) => Promise<void>;
  savePost: (postId: string) => Promise<void>;
  sharePost: (postId: string, platform?: string) => Promise<void>;
}

export const useContentStore = create<ContentState>((set, get) => ({
  posts: [],
  archivedPosts: [],
  currentPost: null,
  isLoading: false,
  error: null,
  
  archivePost: async (postId: string) => {
    set({ isLoading: true, error: null });
    try {
      await archivePost(postId);
      
      // Update posts list
      set((state) => ({
        posts: state.posts.filter((post) => post.id !== postId),
        isLoading: false,
      }));
      
      // Refresh archived posts
      get().fetchArchivedPosts();
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to archive post' 
      });
    }
  },
  
  restorePost: async (postId: string) => {
    set({ isLoading: true, error: null });
    try {
      await restorePost(postId);
      
      // Update archived posts list
      set((state) => ({
        archivedPosts: state.archivedPosts.filter((post) => post.id !== postId),
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to restore post' 
      });
    }
  },
  
  deletePost: async (postId: string) => {
    set({ isLoading: true, error: null });
    try {
      await deletePost(postId);
      
      // Update posts and archived posts lists
      set((state) => ({
        posts: state.posts.filter((post) => post.id !== postId),
        archivedPosts: state.archivedPosts.filter((post) => post.id !== postId),
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to delete post' 
      });
    }
  },
  
  fetchArchivedPosts: async (page = 1, limit = 10) => {
    set({ isLoading: true, error: null });
    try {
      const archivedPosts = await getArchivedPosts(page, limit);
      set({ archivedPosts, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch archived posts' 
      });
    }
  },
  
  reportPost: async (postId: string, reason: string) => {
    set({ isLoading: true, error: null });
    try {
      await reportPost(postId, reason);
      set({ isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to report post' 
      });
    }
  },
  
  updatePost: async (postId: string, data: any) => {
    set({ isLoading: true, error: null });
    try {
      const updatedPost = await updatePost(postId, data);
      
      // Update posts list
      set((state) => ({
        posts: state.posts.map((post) =>
          post.id === postId ? updatedPost : post
        ),
        currentPost: state.currentPost?.id === postId ? updatedPost : state.currentPost,
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to update post' 
      });
    }
  },
  
  fetchPostDetails: async (postId: string) => {
    set({ isLoading: true, error: null });
    try {
      const post = await getPostDetails(postId);
      set({ currentPost: post, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch post details' 
      });
    }
  },
  
  likePost: async (postId: string) => {
    try {
      const { likes, isLiked } = await likePost(postId);
      
      // Update posts list
      set((state) => ({
        posts: state.posts.map((post) =>
          post.id === postId ? { ...post, likes, isLiked } : post
        ),
        currentPost: state.currentPost?.id === postId 
          ? { ...state.currentPost, likes, isLiked } 
          : state.currentPost,
      }));
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to like post' 
      });
    }
  },
  
  savePost: async (postId: string) => {
    try {
      const { isSaved } = await savePost(postId);
      
      // Update posts list
      set((state) => ({
        posts: state.posts.map((post) =>
          post.id === postId ? { ...post, isSaved } : post
        ),
        currentPost: state.currentPost?.id === postId 
          ? { ...state.currentPost, isSaved } 
          : state.currentPost,
      }));
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to save post' 
      });
    }
  },
  
  sharePost: async (postId: string, platform?: string) => {
    try {
      const { shares } = await sharePost(postId, platform);
      
      // Update posts list
      set((state) => ({
        posts: state.posts.map((post) =>
          post.id === postId ? { ...post, shares } : post
        ),
        currentPost: state.currentPost?.id === postId 
          ? { ...state.currentPost, shares } 
          : state.currentPost,
      }));
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to share post' 
      });
    }
  },
}));
