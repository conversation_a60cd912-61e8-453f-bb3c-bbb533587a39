import { create } from 'zustand';
import {
  getSocialConnections,
  connectSocialPlatform,
  disconnectSocialPlatform,
  shareToSocialPlatform,
  shareToMultiplePlatforms,
  getSocialMetrics,
  getCrossPostSettings,
  updateCrossPostSettings,
  getSocialAuthUrl,
  syncSocialAccount,
  getSocialPlatformInfo,
  getSocialSharingHistory,
  getPopularHashtags,
  SocialPlatform,
  SocialConnection,
  SocialShareResult,
  SocialMetrics,
  CrossPostSettings,
} from '../api/socialSharingApi';

interface SocialSharingState {
  // Social connections
  socialConnections: SocialConnection[];
  
  // Social metrics
  socialMetrics: SocialMetrics[];
  
  // Cross-post settings
  crossPostSettings: CrossPostSettings | null;
  
  // Platform info
  platformInfo: {
    [key in SocialPlatform]?: {
      maxVideoLength?: number;
      maxFileSize?: number;
      supportedFormats?: string[];
      maxCaptionLength?: number;
      maxHashtags?: number;
      schedulingSupported: boolean;
      analyticsSupported: boolean;
      directPostingSupported: boolean;
    };
  };
  
  // Sharing history
  sharingHistory: Array<{
    id: string;
    platform: SocialPlatform;
    contentId: string;
    contentType: 'video' | 'post' | 'story' | 'livestream';
    contentTitle: string;
    thumbnailUrl?: string;
    postUrl?: string;
    caption?: string;
    hashtags?: string[];
    status: 'pending' | 'published' | 'failed';
    scheduledFor?: string;
    publishedAt?: string;
    error?: string;
    metrics?: {
      likes: number;
      comments: number;
      shares: number;
      views: number;
    };
  }>;
  
  // Popular hashtags
  popularHashtags: {
    [key in SocialPlatform]?: Array<{
      hashtag: string;
      postCount: number;
      trending: boolean;
    }>;
  };
  
  // UI state
  isConnecting: boolean;
  isSharing: boolean;
  isSyncing: boolean;
  selectedPlatforms: SocialPlatform[];
  shareModalOpen: boolean;
  connectModalOpen: boolean;
  
  // Status
  isLoading: boolean;
  error: string | null;
  
  // Actions - Social Connections
  fetchSocialConnections: () => Promise<void>;
  connectPlatform: (platform: SocialPlatform, authCode: string) => Promise<void>;
  disconnectPlatform: (platform: SocialPlatform) => Promise<void>;
  syncPlatform: (platform: SocialPlatform) => Promise<void>;
  
  // Actions - Sharing
  shareContent: (
    platform: SocialPlatform,
    contentId: string,
    contentType: 'video' | 'post' | 'story' | 'livestream',
    options?: {
      caption?: string;
      hashtags?: string[];
      scheduleTime?: string;
      includeLink?: boolean;
    }
  ) => Promise<SocialShareResult>;
  
  shareToMultiplePlatforms: (
    platforms: SocialPlatform[],
    contentId: string,
    contentType: 'video' | 'post' | 'story' | 'livestream',
    options?: {
      caption?: string;
      hashtags?: string[];
      scheduleTime?: string;
      includeLink?: boolean;
      platformSpecificOptions?: {
        [key in SocialPlatform]?: {
          caption?: string;
          hashtags?: string[];
          scheduleTime?: string;
          includeLink?: boolean;
        };
      };
    }
  ) => Promise<{ [key in SocialPlatform]?: SocialShareResult }>;
  
  // Actions - Metrics
  fetchSocialMetrics: (
    contentId: string,
    contentType: 'video' | 'post' | 'story' | 'livestream'
  ) => Promise<void>;
  
  // Actions - Settings
  fetchCrossPostSettings: () => Promise<void>;
  updateCrossPostSettings: (settings: Partial<CrossPostSettings>) => Promise<void>;
  
  // Actions - Platform Info
  fetchPlatformInfo: (platform: SocialPlatform) => Promise<void>;
  
  // Actions - Sharing History
  fetchSharingHistory: (limit?: number, offset?: number) => Promise<void>;
  
  // Actions - Hashtags
  fetchPopularHashtags: (platform: SocialPlatform, category?: string) => Promise<void>;
  
  // Actions - Auth
  getAuthUrl: (platform: SocialPlatform, redirectUri: string) => Promise<string>;
  
  // UI Actions
  setSelectedPlatforms: (platforms: SocialPlatform[]) => void;
  toggleSelectedPlatform: (platform: SocialPlatform) => void;
  openShareModal: () => void;
  closeShareModal: () => void;
  openConnectModal: () => void;
  closeConnectModal: () => void;
}

export const useSocialSharingStore = create<SocialSharingState>((set, get) => ({
  // Social connections
  socialConnections: [],
  
  // Social metrics
  socialMetrics: [],
  
  // Cross-post settings
  crossPostSettings: null,
  
  // Platform info
  platformInfo: {},
  
  // Sharing history
  sharingHistory: [],
  
  // Popular hashtags
  popularHashtags: {},
  
  // UI state
  isConnecting: false,
  isSharing: false,
  isSyncing: false,
  selectedPlatforms: [],
  shareModalOpen: false,
  connectModalOpen: false,
  
  // Status
  isLoading: false,
  error: null,
  
  // Actions - Social Connections
  fetchSocialConnections: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const connections = await getSocialConnections();
      set({ socialConnections: connections, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch social connections',
      });
    }
  },
  
  connectPlatform: async (platform, authCode) => {
    set({ isConnecting: true, error: null });
    
    try {
      const connection = await connectSocialPlatform(platform, authCode);
      
      set((state) => ({
        socialConnections: [
          ...state.socialConnections.filter((conn) => conn.platform !== platform),
          connection,
        ],
        isConnecting: false,
      }));
    } catch (error) {
      set({
        isConnecting: false,
        error: error instanceof Error ? error.message : `Failed to connect to ${platform}`,
      });
    }
  },
  
  disconnectPlatform: async (platform) => {
    set({ isLoading: true, error: null });
    
    try {
      await disconnectSocialPlatform(platform);
      
      set((state) => ({
        socialConnections: state.socialConnections.map((conn) =>
          conn.platform === platform ? { ...conn, isConnected: false } : conn
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to disconnect from ${platform}`,
      });
    }
  },
  
  syncPlatform: async (platform) => {
    set({ isSyncing: true, error: null });
    
    try {
      const updatedConnection = await syncSocialAccount(platform);
      
      set((state) => ({
        socialConnections: state.socialConnections.map((conn) =>
          conn.platform === platform ? updatedConnection : conn
        ),
        isSyncing: false,
      }));
    } catch (error) {
      set({
        isSyncing: false,
        error: error instanceof Error ? error.message : `Failed to sync ${platform} account`,
      });
    }
  },
  
  // Actions - Sharing
  shareContent: async (platform, contentId, contentType, options) => {
    set({ isSharing: true, error: null });
    
    try {
      const result = await shareToSocialPlatform(platform, contentId, contentType, options);
      set({ isSharing: false });
      return result;
    } catch (error) {
      set({
        isSharing: false,
        error: error instanceof Error ? error.message : `Failed to share to ${platform}`,
      });
      throw error;
    }
  },
  
  shareToMultiplePlatforms: async (platforms, contentId, contentType, options) => {
    set({ isSharing: true, error: null });
    
    try {
      const results = await shareToMultiplePlatforms(platforms, contentId, contentType, options);
      set({ isSharing: false });
      return results;
    } catch (error) {
      set({
        isSharing: false,
        error: error instanceof Error ? error.message : 'Failed to share to multiple platforms',
      });
      throw error;
    }
  },
  
  // Actions - Metrics
  fetchSocialMetrics: async (contentId, contentType) => {
    set({ isLoading: true, error: null });
    
    try {
      const metrics = await getSocialMetrics(contentId, contentType);
      set({ socialMetrics: metrics, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch social metrics',
      });
    }
  },
  
  // Actions - Settings
  fetchCrossPostSettings: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const settings = await getCrossPostSettings();
      set({ crossPostSettings: settings, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch cross-post settings',
      });
    }
  },
  
  updateCrossPostSettings: async (settings) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedSettings = await updateCrossPostSettings(settings);
      set({ crossPostSettings: updatedSettings, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update cross-post settings',
      });
    }
  },
  
  // Actions - Platform Info
  fetchPlatformInfo: async (platform) => {
    set({ isLoading: true, error: null });
    
    try {
      const info = await getSocialPlatformInfo(platform);
      
      set((state) => ({
        platformInfo: {
          ...state.platformInfo,
          [platform]: info,
        },
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to fetch info for ${platform}`,
      });
    }
  },
  
  // Actions - Sharing History
  fetchSharingHistory: async (limit = 20, offset = 0) => {
    set({ isLoading: true, error: null });
    
    try {
      const history = await getSocialSharingHistory(limit, offset);
      set({ sharingHistory: history, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch sharing history',
      });
    }
  },
  
  // Actions - Hashtags
  fetchPopularHashtags: async (platform, category) => {
    set({ isLoading: true, error: null });
    
    try {
      const hashtags = await getPopularHashtags(platform, category);
      
      set((state) => ({
        popularHashtags: {
          ...state.popularHashtags,
          [platform]: hashtags,
        },
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to fetch hashtags for ${platform}`,
      });
    }
  },
  
  // Actions - Auth
  getAuthUrl: async (platform, redirectUri) => {
    set({ isLoading: true, error: null });
    
    try {
      const { authUrl } = await getSocialAuthUrl(platform, redirectUri);
      set({ isLoading: false });
      return authUrl;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to get auth URL for ${platform}`,
      });
      throw error;
    }
  },
  
  // UI Actions
  setSelectedPlatforms: (platforms) => {
    set({ selectedPlatforms: platforms });
  },
  
  toggleSelectedPlatform: (platform) => {
    set((state) => {
      const isSelected = state.selectedPlatforms.includes(platform);
      
      return {
        selectedPlatforms: isSelected
          ? state.selectedPlatforms.filter((p) => p !== platform)
          : [...state.selectedPlatforms, platform],
      };
    });
  },
  
  openShareModal: () => {
    set({ shareModalOpen: true });
  },
  
  closeShareModal: () => {
    set({ shareModalOpen: false });
  },
  
  openConnectModal: () => {
    set({ connectModalOpen: true });
  },
  
  closeConnectModal: () => {
    set({ connectModalOpen: false });
  },
}));
