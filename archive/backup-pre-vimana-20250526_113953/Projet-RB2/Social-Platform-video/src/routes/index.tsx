import { Routes, Route, Navigate } from 'react-router-dom';
import { Home } from '@/pages/home';
import { Explore } from '@/pages/explore';
import { Profile } from '@/pages/profile';
import { Notifications } from '@/pages/notifications';
import { FollowersPage } from '@/pages/FollowersPage';
import { FollowingPage } from '@/pages/FollowingPage';
import { ArchivedPostsPage } from '@/pages/ArchivedPostsPage';
import { VideoEditPage } from '@/pages/VideoEditPage';
import { StoryPage } from '@/pages/StoryPage';
import { CreateStoryPage } from '@/pages/CreateStoryPage';
import { AnalyticsPage } from '@/pages/AnalyticsPage';
import { MonetizationSettingsPage } from '@/pages/MonetizationSettingsPage';
import { CollaborationPage } from '@/pages/CollaborationPage';
import { SocialIntegrationsPage } from '@/pages/SocialIntegrationsPage';
import { RecommendationsPage } from '@/pages/RecommendationsPage';
import CollectionsPage from '@/pages/collections';
import CollectionDetailPage from '@/pages/collection-detail';
import VideoOptimizationDemo from '@/pages/VideoOptimizationDemo';
import ScheduledContentPage from '@/pages/ScheduledContentPage';
import ContentCalendarPage from '@/pages/ContentCalendarPage';

export function AppRoutes() {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/explore" element={<Explore />} />
      <Route path="/notifications" element={<Notifications />} />

      {/* Profile Routes */}
      <Route path="/profile" element={<Profile />} />
      <Route path="/profile/:userId" element={<Profile />} />
      <Route path="/profile/:userId/followers" element={<FollowersPage />} />
      <Route path="/profile/:userId/following" element={<FollowingPage />} />
      <Route path="/profile/archived" element={<ArchivedPostsPage />} />

      {/* Video Routes */}
      <Route path="/videos/:videoId" element={<Home />} /> {/* Placeholder - should be a video detail page */}
      <Route path="/videos/:videoId/edit" element={<VideoEditPage />} />
      <Route path="/video-optimization" element={<VideoOptimizationDemo />} />

      {/* Post Routes */}
      <Route path="/posts/:postId" element={<Home />} /> {/* Placeholder - should be a post detail page */}

      {/* Livestream Routes */}
      <Route path="/livestreams/:livestreamId" element={<Home />} /> {/* Placeholder - should be a livestream page */}

      {/* Story Routes */}
      <Route path="/stories/:storyId" element={<StoryPage />} />
      <Route path="/stories/:storyId/items/:itemId" element={<StoryPage />} />
      <Route path="/stories/create" element={<CreateStoryPage />} />

      {/* Analytics Routes */}
      <Route path="/analytics" element={<AnalyticsPage />} />
      <Route path="/analytics/:contentType/:contentId" element={<AnalyticsPage />} />

      {/* Monetization Routes */}
      <Route path="/monetization" element={<MonetizationSettingsPage />} />

      {/* Collaboration Routes */}
      <Route path="/collaboration" element={<CollaborationPage />} />
      <Route path="/collaboration/:contentType/:contentId" element={<CollaborationPage />} />

      {/* Social Media Integration Routes */}
      <Route path="/social-integrations" element={<SocialIntegrationsPage />} />
      <Route path="/social-auth-callback" element={<SocialIntegrationsPage />} />

      {/* Recommendations Routes */}
      <Route path="/recommendations" element={<RecommendationsPage />} />

      {/* Collections Routes */}
      <Route path="/collections" element={<CollectionsPage />} />
      <Route path="/collections/:id" element={<CollectionDetailPage />} />

      {/* Content Management Routes */}
      <Route path="/content/scheduled" element={<ScheduledContentPage />} />
      <Route path="/content/calendar" element={<ContentCalendarPage />} />

      {/* Catch-all Route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}