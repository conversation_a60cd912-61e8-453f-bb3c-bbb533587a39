const express = require('express');
const router = express.Router();
const BlogController = require('../controllers/BlogController');
const { 
  blogCacheMiddleware, 
  invalidateBlogCacheMiddleware 
} = require('../middleware/cacheMiddleware');
const authMiddleware = require('../middleware/authMiddleware');

// Routes publiques (avec cache)
router.get('/', blogCacheMiddleware, BlogController.getBlogPosts);
router.get('/:id', blogCacheMiddleware, BlogController.getBlogPostById);
router.get('/:id/comments', blogCacheMiddleware, BlogController.getBlogPostComments);

// Routes protégées (avec invalidation de cache)
router.post('/', 
  authMiddleware, 
  invalidateBlogCacheMiddleware, 
  BlogController.createBlogPost
);

router.put('/:id', 
  authMiddleware, 
  invalidateBlogCacheMiddleware, 
  BlogController.updateBlogPost
);

router.delete('/:id', 
  authMiddleware, 
  invalidateBlogCacheMiddleware, 
  BlogController.deleteBlogPost
);

router.post('/:id/comments', 
  authMiddleware, 
  invalidateBlogCacheMiddleware, 
  BlogController.addBlogPostComment
);

router.post('/:id/like', 
  authMiddleware, 
  invalidateBlogCacheMiddleware, 
  BlogController.likeBlogPost
);

router.post('/:id/unlike', 
  authMiddleware, 
  invalidateBlogCacheMiddleware, 
  BlogController.unlikeBlogPost
);

router.post('/:id/share', 
  authMiddleware, 
  BlogController.shareBlogPost
);

module.exports = router;
