const express = require('express');
const router = express.Router();
const LivestreamController = require('../controllers/LivestreamController');
const { 
  livestreamCacheMiddleware, 
  invalidateLivestreamCacheMiddleware 
} = require('../middleware/cacheMiddleware');
const authMiddleware = require('../middleware/authMiddleware');

// Routes publiques (avec cache)
router.get('/', livestreamCacheMiddleware, LivestreamController.getLivestreams);
router.get('/:id', livestreamCacheMiddleware, LivestreamController.getLivestreamById);
router.get('/:id/messages', livestreamCacheMiddleware, LivestreamController.getLivestreamMessages);

// Routes protégées (avec invalidation de cache)
router.post('/', 
  authMiddleware, 
  invalidateLivestreamCacheMiddleware, 
  LivestreamController.createLivestream
);

router.put('/:id', 
  authMiddleware, 
  invalidateLivestreamCacheMiddleware, 
  LivestreamController.updateLivestream
);

router.post('/:id/start', 
  authMiddleware, 
  invalidateLivestreamCacheMiddleware, 
  LivestreamController.startLivestream
);

router.post('/:id/end', 
  authMiddleware, 
  invalidateLivestreamCacheMiddleware, 
  LivestreamController.endLivestream
);

router.post('/:id/messages', 
  authMiddleware, 
  invalidateLivestreamCacheMiddleware, 
  LivestreamController.sendLivestreamMessage
);

module.exports = router;
