import { CalendarFilters } from '../components/content-management/CalendarFilters';

// Storage key for saved filters
const STORAGE_KEY = 'social_platform_saved_filters';

export interface SavedFilter {
  id: string;
  name: string;
  description?: string;
  filters: CalendarFilters;
  createdAt: string;
  updatedAt: string;
  isDefault?: boolean;
}

/**
 * Generate a unique ID for a saved filter
 */
function generateFilterId(): string {
  return 'filter-' + Math.random().toString(36).substring(2, 11);
}

/**
 * Get all saved filters from local storage
 */
export function getSavedFilters(): SavedFilter[] {
  try {
    const savedFiltersJson = localStorage.getItem(STORAGE_KEY);
    if (!savedFiltersJson) {
      return [];
    }
    
    return JSON.parse(savedFiltersJson);
  } catch (error) {
    console.error('Error loading saved filters:', error);
    return [];
  }
}

/**
 * Save a new filter to local storage
 */
export function saveFilter(name: string, filters: CalendarFilters, description?: string): SavedFilter {
  const savedFilters = getSavedFilters();
  
  // Check if a filter with this name already exists
  const existingFilterIndex = savedFilters.findIndex(filter => filter.name === name);
  
  const now = new Date().toISOString();
  
  if (existingFilterIndex >= 0) {
    // Update existing filter
    const updatedFilter: SavedFilter = {
      ...savedFilters[existingFilterIndex],
      filters,
      description,
      updatedAt: now
    };
    
    savedFilters[existingFilterIndex] = updatedFilter;
    localStorage.setItem(STORAGE_KEY, JSON.stringify(savedFilters));
    
    return updatedFilter;
  } else {
    // Create new filter
    const newFilter: SavedFilter = {
      id: generateFilterId(),
      name,
      description,
      filters,
      createdAt: now,
      updatedAt: now
    };
    
    savedFilters.push(newFilter);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(savedFilters));
    
    return newFilter;
  }
}

/**
 * Update an existing filter
 */
export function updateFilter(id: string, updates: Partial<SavedFilter>): SavedFilter | null {
  const savedFilters = getSavedFilters();
  const filterIndex = savedFilters.findIndex(filter => filter.id === id);
  
  if (filterIndex === -1) {
    return null;
  }
  
  const updatedFilter: SavedFilter = {
    ...savedFilters[filterIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  savedFilters[filterIndex] = updatedFilter;
  localStorage.setItem(STORAGE_KEY, JSON.stringify(savedFilters));
  
  return updatedFilter;
}

/**
 * Delete a saved filter
 */
export function deleteFilter(id: string): boolean {
  const savedFilters = getSavedFilters();
  const filterIndex = savedFilters.findIndex(filter => filter.id === id);
  
  if (filterIndex === -1) {
    return false;
  }
  
  // Don't delete default filters
  if (savedFilters[filterIndex].isDefault) {
    return false;
  }
  
  savedFilters.splice(filterIndex, 1);
  localStorage.setItem(STORAGE_KEY, JSON.stringify(savedFilters));
  
  return true;
}

/**
 * Set a filter as the default
 */
export function setDefaultFilter(id: string): SavedFilter | null {
  const savedFilters = getSavedFilters();
  
  // First, remove default flag from all filters
  savedFilters.forEach(filter => {
    filter.isDefault = false;
  });
  
  // Then set the new default
  const filterIndex = savedFilters.findIndex(filter => filter.id === id);
  if (filterIndex === -1) {
    return null;
  }
  
  savedFilters[filterIndex].isDefault = true;
  localStorage.setItem(STORAGE_KEY, JSON.stringify(savedFilters));
  
  return savedFilters[filterIndex];
}

/**
 * Get the default filter if one exists
 */
export function getDefaultFilter(): SavedFilter | null {
  const savedFilters = getSavedFilters();
  return savedFilters.find(filter => filter.isDefault) || null;
}

/**
 * Initialize default filters if none exist
 */
export function initializeDefaultFilters(): void {
  const savedFilters = getSavedFilters();
  
  if (savedFilters.length === 0) {
    // Create some default filters
    const now = new Date().toISOString();
    
    const defaultFilters: SavedFilter[] = [
      {
        id: generateFilterId(),
        name: 'Tous les contenus',
        description: 'Affiche tous les types de contenu',
        filters: {
          contentTypes: {
            video: true,
            post: true,
            livestream: true,
            blog: true
          },
          status: {
            scheduled: true,
            processing: true,
            error: true
          },
          recurrence: {
            oneTime: true,
            recurring: true
          },
          dateRange: {
            start: null,
            end: null
          }
        },
        createdAt: now,
        updatedAt: now,
        isDefault: true
      },
      {
        id: generateFilterId(),
        name: 'Vidéos uniquement',
        description: 'Affiche uniquement les vidéos',
        filters: {
          contentTypes: {
            video: true,
            post: false,
            livestream: false,
            blog: false
          },
          status: {
            scheduled: true,
            processing: true,
            error: true
          },
          recurrence: {
            oneTime: true,
            recurring: true
          },
          dateRange: {
            start: null,
            end: null
          }
        },
        createdAt: now,
        updatedAt: now,
        isDefault: false
      },
      {
        id: generateFilterId(),
        name: 'Contenu récurrent',
        description: 'Affiche uniquement le contenu récurrent',
        filters: {
          contentTypes: {
            video: true,
            post: true,
            livestream: true,
            blog: true
          },
          status: {
            scheduled: true,
            processing: true,
            error: true
          },
          recurrence: {
            oneTime: false,
            recurring: true
          },
          dateRange: {
            start: null,
            end: null
          }
        },
        createdAt: now,
        updatedAt: now,
        isDefault: false
      }
    ];
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultFilters));
  }
}
