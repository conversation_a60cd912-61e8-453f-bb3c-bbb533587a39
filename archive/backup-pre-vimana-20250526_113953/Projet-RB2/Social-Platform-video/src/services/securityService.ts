import axios from 'axios';

/**
 * Configuration du service de sécurité
 */
interface SecurityServiceConfig {
  baseUrl: string;
  apiKey?: string;
}

/**
 * Service d'interface avec le microservice Security
 * Fournit des méthodes pour accéder aux fonctionnalités de sécurité centralisées
 */
export class SecurityService {
  private baseUrl: string;
  private apiKey?: string;

  constructor(config?: SecurityServiceConfig) {
    this.baseUrl = config?.baseUrl || process.env.SECURITY_SERVICE_URL || 'http://security-service:3001/api';
    this.apiKey = config?.apiKey || process.env.SECURITY_API_KEY;
  }

  /**
   * Obtient les en-têtes d'authentification pour les requêtes au service de sécurité
   */
  private getHeaders() {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.apiKey) {
      headers['X-API-Key'] = this.apiKey;
    }

    // Ajouter le token d'authentification s'il est disponible
    const token = localStorage.getItem('auth_token');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Valide les données d'entrée selon un schéma spécifié
   * @param data Données à valider
   * @param schemaName Nom du schéma de validation
   */
  async validateInput(data: any, schemaName: string): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/validation/${schemaName}`,
        { data },
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la validation des données:', error);
      return { valid: false, errors: ['Erreur de service lors de la validation'] };
    }
  }

  /**
   * Sanitize les données d'entrée
   * @param data Données à sanitizer
   * @param type Type de données (html, text, etc.)
   */
  async sanitizeInput(data: any, type: 'html' | 'text' | 'url' = 'text'): Promise<any> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/sanitization/${type}`,
        { data },
        { headers: this.getHeaders() }
      );
      return response.data.sanitized;
    } catch (error) {
      console.error('Erreur lors de la sanitization des données:', error);
      return data; // Retourne les données originales en cas d'erreur
    }
  }

  /**
   * Obtient la configuration CSP pour un type de contenu spécifique
   * @param contentType Type de contenu (video, social, etc.)
   */
  async getCSPConfig(contentType: string): Promise<Record<string, any>> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/csp/config/${contentType}`,
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de la configuration CSP:', error);
      return {}; // Retourne une configuration vide en cas d'erreur
    }
  }

  /**
   * Chiffre un message avec le chiffrement de bout en bout
   * @param message Message à chiffrer
   * @param recipientPublicKey Clé publique du destinataire
   */
  async encryptE2E(message: string, recipientPublicKey: string): Promise<string> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/encryption/e2e/encrypt`,
        { message, recipientPublicKey },
        { headers: this.getHeaders() }
      );
      return response.data.encryptedMessage;
    } catch (error) {
      console.error('Erreur lors du chiffrement du message:', error);
      throw new Error('Échec du chiffrement du message');
    }
  }

  /**
   * Déchiffre un message chiffré de bout en bout
   * @param encryptedMessage Message chiffré
   */
  async decryptE2E(encryptedMessage: string): Promise<string> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/encryption/e2e/decrypt`,
        { encryptedMessage },
        { headers: this.getHeaders() }
      );
      return response.data.decryptedMessage;
    } catch (error) {
      console.error('Erreur lors du déchiffrement du message:', error);
      throw new Error('Échec du déchiffrement du message');
    }
  }

  /**
   * Génère un QR code pour la configuration 2FA
   */
  async generate2FAQrCode(): Promise<{ qrCodeUrl: string; secret: string }> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/2fa/setup`,
        {},
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la génération du QR code 2FA:', error);
      throw new Error('Échec de la génération du QR code 2FA');
    }
  }

  /**
   * Vérifie un code 2FA lors de la configuration
   * @param verificationCode Code de vérification
   */
  async verify2FASetup(verificationCode: string): Promise<boolean> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/2fa/verify-setup`,
        { verificationCode },
        { headers: this.getHeaders() }
      );
      return response.data.success;
    } catch (error) {
      console.error('Erreur lors de la vérification du code 2FA:', error);
      throw new Error('Code de vérification 2FA invalide');
    }
  }

  /**
   * Vérifie un code 2FA lors de l'authentification
   * @param verificationCode Code de vérification
   * @param userId ID de l'utilisateur
   */
  async verify2FALogin(verificationCode: string, userId: string): Promise<boolean> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/2fa/verify-login`,
        { verificationCode, userId },
        { headers: this.getHeaders() }
      );
      return response.data.success;
    } catch (error) {
      console.error('Erreur lors de la vérification du code 2FA:', error);
      throw new Error('Code de vérification 2FA invalide');
    }
  }

  /**
   * Vérifie les permissions d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param resource Ressource à accéder
   * @param action Action à effectuer
   */
  async checkPermission(userId: string, resource: string, action: string): Promise<boolean> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/rbac/check-permission`,
        { userId, resource, action },
        { headers: this.getHeaders() }
      );
      return response.data.allowed;
    } catch (error) {
      console.error('Erreur lors de la vérification des permissions:', error);
      return false; // Par défaut, refuser l'accès en cas d'erreur
    }
  }

  /**
   * Obtient les politiques de rétention des données
   */
  async getDataRetentionPolicies(): Promise<any[]> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/compliance/data-retention-policies`,
        { headers: this.getHeaders() }
      );
      return response.data.policies;
    } catch (error) {
      console.error('Erreur lors de la récupération des politiques de rétention:', error);
      return []; // Retourne une liste vide en cas d'erreur
    }
  }

  /**
   * Analyse un fichier pour détecter des virus/malwares
   * @param file Fichier à analyser
   */
  async scanFile(file: File): Promise<{ clean: boolean; threats: string[] }> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post(
        `${this.baseUrl}/antivirus/scan`,
        formData,
        { 
          headers: {
            ...this.getHeaders(),
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'analyse du fichier:', error);
      throw new Error('Échec de l\'analyse du fichier');
    }
  }
}

// Exporte une instance par défaut du service
export default new SecurityService();
