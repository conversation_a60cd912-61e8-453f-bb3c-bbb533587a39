import axios from 'axios';
import { Video, Post } from '../types';

// Configuration de base pour les requêtes API
const apiClient = axios.create({
  baseURL: process.env.BACKEND_API_URL || 'http://localhost:3000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Fonction pour ajouter le token d'authentification aux requêtes
export const setAuthToken = (token: string) => {
  if (token) {
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    delete apiClient.defaults.headers.common['Authorization'];
  }
};

// Types d'intégration financière
export interface MonetizationRequest {
  contentIds: string[];
  contentType: 'video' | 'post' | 'livestream';
  monetizationType: 'premium' | 'subscription' | 'oneTime' | 'donation';
  price?: number;
  currency?: string;
  description?: string;
}

export interface MonetizationResponse {
  success: boolean;
  transactionIds?: string[];
  monetizationIds?: string[];
  message?: string;
  status: 'pending' | 'active' | 'rejected' | 'completed';
}

export interface ContentRevenueStats {
  contentId: string;
  totalRevenue: number;
  currency: string;
  transactionCount: number;
  lastUpdated: string;
  bySource: {
    premium?: number;
    subscription?: number;
    oneTime?: number;
    donation?: number;
    sponsorship?: number;
  };
}

// Service pour l'intégration de la sélection avec le système financier
export const SelectionIntegrationService = {
  // Monétiser une sélection de contenu
  async monetizeContent(request: MonetizationRequest): Promise<MonetizationResponse> {
    try {
      const response = await apiClient.post('/financial/monetize', request);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la monétisation du contenu:', error);
      return {
        success: false,
        message: 'Échec de la monétisation du contenu',
        status: 'rejected',
      };
    }
  },

  // Obtenir les statistiques de revenus pour une sélection de contenu
  async getContentRevenueStats(contentIds: string[], contentType: 'video' | 'post' | 'livestream'): Promise<ContentRevenueStats[]> {
    try {
      const response = await apiClient.get('/financial/revenue-stats', {
        params: { contentIds: contentIds.join(','), contentType },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques de revenus:', error);
      return [];
    }
  },

  // Convertir un contenu standard en contenu premium
  async convertToPremiumContent(contentIds: string[], contentType: 'video' | 'post' | 'livestream', price: number): Promise<MonetizationResponse> {
    try {
      const response = await apiClient.post('/financial/convert-to-premium', {
        contentIds,
        contentType,
        price,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la conversion en contenu premium:', error);
      return {
        success: false,
        message: 'Échec de la conversion en contenu premium',
        status: 'rejected',
      };
    }
  },

  // Promouvoir une sélection de contenu (sponsoring ou mise en avant)
  async promoteContent(contentIds: string[], contentType: 'video' | 'post' | 'livestream', budget: number, duration: number): Promise<MonetizationResponse> {
    try {
      const response = await apiClient.post('/financial/promote', {
        contentIds,
        contentType,
        budget,
        duration,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la promotion du contenu:', error);
      return {
        success: false,
        message: 'Échec de la promotion du contenu',
        status: 'rejected',
      };
    }
  },

  // Définir des paramètres de dons pour une sélection de contenu
  async setupDonations(contentIds: string[], contentType: 'video' | 'post' | 'livestream', settings: { 
    minAmount?: number;
    suggestedAmounts?: number[];
    thanksMessage?: string;
    goals?: { amount: number; description: string }[];
  }): Promise<MonetizationResponse> {
    try {
      const response = await apiClient.post('/financial/setup-donations', {
        contentIds,
        contentType,
        settings,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la configuration des dons:', error);
      return {
        success: false,
        message: 'Échec de la configuration des dons',
        status: 'rejected',
      };
    }
  },

  // Obtenir le statut de monétisation pour une sélection de contenu
  async getMonetizationStatus(contentIds: string[], contentType: 'video' | 'post' | 'livestream'): Promise<Record<string, {
    isMonetized: boolean;
    type?: 'premium' | 'subscription' | 'oneTime' | 'donation';
    price?: number;
    status: 'pending' | 'active' | 'rejected' | 'completed';
  }>> {
    try {
      const response = await apiClient.get('/financial/monetization-status', {
        params: { contentIds: contentIds.join(','), contentType },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du statut de monétisation:', error);
      return {};
    }
  }
};

export default SelectionIntegrationService; 