import { cacheService } from './CacheService';
import { CacheTier } from './CacheConfig';

/**
 * Exemples d'utilisation du service de cache
 * Ce fichier sert uniquement de documentation et de référence
 */

// Exemple 1: Stockage et récupération simple
function basicCacheExample() {
  // Stocker une valeur dans le cache
  cacheService.set('user:123', { id: 123, name: '<PERSON>' });
  
  // Récupérer une valeur du cache
  const user = cacheService.get<{ id: number, name: string }>('user:123');
  console.log('User from cache:', user);
  
  // Vérifier si une clé existe dans le cache
  const exists = cacheService.has('user:123');
  console.log('User exists in cache:', exists);
  
  // Supprimer une entrée du cache
  cacheService.delete('user:123');
}

// Exemple 2: Utilisation avec des options
function cacheWithOptionsExample() {
  // Stocker avec un TTL personnalisé (10 minutes)
  cacheService.set('video:456', { id: 456, title: 'Mon super tutoriel' }, { ttl: 600 });
  
  // Stocker avec un niveau de cache prédéfini
  cacheService.set('video:789', { id: 789, title: 'Vidéo populaire' }, { tier: CacheTier.FREQUENT });
  
  // Stocker avec des tags pour l'invalidation groupée
  cacheService.set('video:101', { id: 101, title: 'Tutoriel avancé' }, { 
    tags: ['video', 'tutorial', 'user:123'] 
  });
  
  // Stocker dans un espace de noms spécifique
  cacheService.set('profile', { theme: 'dark' }, { namespace: 'user:123:preferences' });
}

// Exemple 3: Récupération avec fallback
async function getOrSetExample() {
  // Récupérer du cache ou via une fonction de récupération
  const videoData = await cacheService.getOrSet(
    'video:456',
    async () => {
      console.log('Fetching video data from API...');
      // Simuler un appel API
      await new Promise(resolve => setTimeout(resolve, 500));
      return { id: 456, title: 'Mon super tutoriel', views: 1250 };
    },
    { tier: CacheTier.STANDARD, tags: ['video'] }
  );
  
  console.log('Video data:', videoData);
}

// Exemple 4: Invalidation par tag
function invalidationExample() {
  // Stocker plusieurs entrées avec le même tag
  cacheService.set('video:1', { id: 1, title: 'Vidéo 1' }, { tags: ['user:123', 'videos'] });
  cacheService.set('video:2', { id: 2, title: 'Vidéo 2' }, { tags: ['user:123', 'videos'] });
  cacheService.set('video:3', { id: 3, title: 'Vidéo 3' }, { tags: ['user:456', 'videos'] });
  
  // Invalider toutes les vidéos d'un utilisateur spécifique
  const invalidatedCount = cacheService.invalidateByTag('user:123');
  console.log(`Invalidated ${invalidatedCount} cache entries`);
  
  // Invalider toutes les vidéos
  const totalInvalidated = cacheService.invalidateByTag('videos');
  console.log(`Invalidated ${totalInvalidated} video entries`);
}

// Exemple 5: Statistiques de cache
function cacheStatsExample() {
  // Effectuer quelques opérations
  cacheService.set('test:1', { value: 1 });
  cacheService.get('test:1');
  cacheService.get('test:2'); // Miss
  
  // Obtenir les statistiques
  const stats = cacheService.getStats();
  console.log('Cache statistics:', stats);
  console.log(`Hit rate: ${(stats.hitRate * 100).toFixed(2)}%`);
}

// Exemple 6: Configuration du cache
function cacheConfigExample() {
  // Configurer les TTL personnalisés
  cacheService.setTTLValues({
    [CacheTier.MICRO]: 5,           // 5 secondes
    [CacheTier.FREQUENT]: 2 * 60,   // 2 minutes
  });
  
  // Configurer la taille maximale du cache
  cacheService.setMaxSize(2000);
}

// Exemple 7: Utilisation dans un service API
class VideoApiService {
  private apiBaseUrl = 'https://api.example.com/videos';
  
  async getVideoById(id: string): Promise<any> {
    return cacheService.getOrSet(
      `video:${id}`,
      async () => {
        // Simuler un appel API
        const response = await fetch(`${this.apiBaseUrl}/${id}`);
        return response.json();
      },
      { 
        tier: CacheTier.STANDARD,
        tags: ['video', `video:${id}`]
      }
    );
  }
  
  async getRecommendedVideos(userId: string): Promise<any[]> {
    return cacheService.getOrSet(
      `recommendations:${userId}`,
      async () => {
        // Simuler un appel API
        const response = await fetch(`${this.apiBaseUrl}/recommended?userId=${userId}`);
        return response.json();
      },
      { 
        tier: CacheTier.FREQUENT,
        tags: ['recommendations', `user:${userId}`]
      }
    );
  }
  
  // Invalider le cache après une mise à jour
  async updateVideo(id: string, data: any): Promise<void> {
    // Appel API pour mettre à jour la vidéo
    await fetch(`${this.apiBaseUrl}/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
      headers: { 'Content-Type': 'application/json' }
    });
    
    // Invalider les entrées de cache associées
    cacheService.invalidateByTags([`video:${id}`, 'recommendations']);
  }
}
