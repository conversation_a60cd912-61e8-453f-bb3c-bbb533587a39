import { cacheService } from '../CacheService';
import { CacheTier } from '../CacheConfig';

// Créer le répertoire de tests si nécessaire
describe('CacheService', () => {
  beforeEach(() => {
    // Nettoyer le cache avant chaque test
    cacheService.clear();
  });

  describe('Opérations de base', () => {
    test('devrait stocker et récupérer une valeur', () => {
      const testKey = 'test:key';
      const testValue = { id: 1, name: 'Test Value' };
      
      cacheService.set(testKey, testValue);
      const retrievedValue = cacheService.get(testKey);
      
      expect(retrievedValue).toEqual(testValue);
    });

    test('devrait retourner null pour une clé inexistante', () => {
      const result = cacheService.get('nonexistent:key');
      expect(result).toBeNull();
    });

    test('devrait vérifier correctement si une clé existe', () => {
      const testKey = 'test:exists';
      cacheService.set(testKey, 'value');
      
      expect(cacheService.has(testKey)).toBe(true);
      expect(cacheService.has('nonexistent:key')).toBe(false);
    });

    test('devrait supprimer une entrée du cache', () => {
      const testKey = 'test:delete';
      cacheService.set(testKey, 'value');
      
      expect(cacheService.has(testKey)).toBe(true);
      
      cacheService.delete(testKey);
      expect(cacheService.has(testKey)).toBe(false);
    });
  });

  describe('Options avancées', () => {
    test('devrait respecter les espaces de noms', () => {
      const key = 'user:profile';
      const value1 = { name: 'User 1' };
      const value2 = { name: 'User 2' };
      
      cacheService.set(key, value1, { namespace: 'user1' });
      cacheService.set(key, value2, { namespace: 'user2' });
      
      const result1 = cacheService.get(key, { namespace: 'user1' });
      const result2 = cacheService.get(key, { namespace: 'user2' });
      
      expect(result1).toEqual(value1);
      expect(result2).toEqual(value2);
    });

    test('devrait gérer les tags pour l\'invalidation', () => {
      cacheService.set('video:1', { title: 'Video 1' }, { tags: ['videos', 'user:1'] });
      cacheService.set('video:2', { title: 'Video 2' }, { tags: ['videos', 'user:1'] });
      cacheService.set('video:3', { title: 'Video 3' }, { tags: ['videos', 'user:2'] });
      
      // Invalider les vidéos d'un utilisateur spécifique
      const invalidatedCount = cacheService.invalidateByTag('user:1');
      
      expect(invalidatedCount).toBe(2);
      expect(cacheService.has('video:1')).toBe(false);
      expect(cacheService.has('video:2')).toBe(false);
      expect(cacheService.has('video:3')).toBe(true);
    });
  });

  describe('getOrSet', () => {
    test('devrait récupérer du cache si disponible', async () => {
      const testKey = 'test:getOrSet';
      const testValue = { data: 'cached value' };
      
      cacheService.set(testKey, testValue);
      
      const fetchFn = jest.fn().mockResolvedValue({ data: 'fetched value' });
      
      const result = await cacheService.getOrSet(testKey, fetchFn);
      
      expect(result).toEqual(testValue);
      expect(fetchFn).not.toHaveBeenCalled();
    });

    test('devrait appeler la fonction de récupération si non disponible', async () => {
      const testKey = 'test:getOrSet:missing';
      const fetchedValue = { data: 'fetched value' };
      
      const fetchFn = jest.fn().mockResolvedValue(fetchedValue);
      
      const result = await cacheService.getOrSet(testKey, fetchFn);
      
      expect(result).toEqual(fetchedValue);
      expect(fetchFn).toHaveBeenCalledTimes(1);
      expect(cacheService.get(testKey)).toEqual(fetchedValue);
    });
  });

  describe('Statistiques', () => {
    test('devrait suivre les statistiques d\'utilisation', () => {
      // Effectuer quelques opérations
      cacheService.set('stats:1', 'value1');
      cacheService.set('stats:2', 'value2');
      
      cacheService.get('stats:1'); // Hit
      cacheService.get('stats:1'); // Hit
      cacheService.get('stats:2'); // Hit
      cacheService.get('stats:3'); // Miss
      
      const stats = cacheService.getStats();
      
      expect(stats.hits).toBe(3);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBeCloseTo(0.75);
      expect(stats.setOperations).toBe(2);
      expect(stats.getOperations).toBe(4);
    });
  });
});
