import { CacheTier, CacheOptions, CacheStats, DEFAULT_TTL_VALUES } from './CacheConfig';

/**
 * Interface pour une entrée de cache
 */
interface CacheEntry<T = any> {
  value: T;
  expiry: number;
  tags?: string[];
  size?: number;
  hits: number;
  lastAccessed: number;
}

/**
 * Service de gestion de cache avec support multi-niveaux
 * - Cache en mémoire pour les accès rapides
 * - Support pour l'invalidation par tags
 * - Statistiques de performance
 * - Gestion intelligente de l'expiration
 */
class CacheService {
  private static instance: CacheService;
  private cache: Map<string, CacheEntry>;
  private tagMap: Map<string, Set<string>>;
  private stats: CacheStats;
  private ttlValues: Record<CacheTier, number>;
  private compressionThreshold: number;
  private maxSize: number;
  private cleanupInterval: NodeJS.Timeout | null;

  /**
   * Constructeur privé (singleton)
   */
  private constructor() {
    this.cache = new Map();
    this.tagMap = new Map();
    this.ttlValues = { ...DEFAULT_TTL_VALUES };
    this.compressionThreshold = 10 * 1024; // 10KB
    this.maxSize = 1000; // Nombre maximum d'entrées
    this.cleanupInterval = null;
    
    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      setOperations: 0,
      getOperations: 0,
      invalidations: 0,
      averageLookupTime: 0,
    };
    
    // Démarrer le nettoyage périodique
    this.startCleanupInterval();
  }

  /**
   * Obtenir l'instance singleton du service de cache
   */
  public static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  /**
   * Démarrer l'intervalle de nettoyage du cache
   */
  private startCleanupInterval(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    // Nettoyer le cache toutes les 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Nettoyer les entrées expirées du cache
   */
  private cleanup(): void {
    const now = Date.now();
    let expiredCount = 0;
    
    // Supprimer les entrées expirées
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiry < now) {
        this.cache.delete(key);
        expiredCount++;
      }
    }
    
    // Si le cache est trop grand, supprimer les entrées les moins utilisées
    if (this.cache.size > this.maxSize) {
      this.evictLeastRecentlyUsed(this.cache.size - this.maxSize);
    }
    
    console.log(`Cache cleanup: removed ${expiredCount} expired entries. Current size: ${this.cache.size}`);
  }

  /**
   * Supprimer les entrées les moins récemment utilisées
   */
  private evictLeastRecentlyUsed(count: number): void {
    // Trier les entrées par date de dernier accès
    const entries = Array.from(this.cache.entries())
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
    
    // Supprimer les entrées les moins récemment utilisées
    for (let i = 0; i < Math.min(count, entries.length); i++) {
      this.cache.delete(entries[i][0]);
    }
  }

  /**
   * Générer une clé de cache complète
   */
  private generateCacheKey(key: string, options?: CacheOptions): string {
    const namespace = options?.namespace || 'default';
    return `${namespace}:${key}`;
  }

  /**
   * Récupérer une valeur du cache
   */
  get<T>(key: string, options?: CacheOptions): T | null {
    const fullKey = this.generateCacheKey(key, options);
    const startTime = Date.now();
    this.stats.getOperations++;
    
    const entry = this.cache.get(fullKey);
    
    if (!entry || entry.expiry < Date.now()) {
      if (entry) {
        // Entrée expirée
        this.cache.delete(fullKey);
      }
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
    
    // Mettre à jour les statistiques d'accès
    entry.hits++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;
    this.updateHitRate();
    
    // Mettre à jour le temps moyen de recherche
    const lookupTime = Date.now() - startTime;
    this.stats.averageLookupTime = 
      (this.stats.averageLookupTime * (this.stats.hits + this.stats.misses - 1) + lookupTime) / 
      (this.stats.hits + this.stats.misses);
    
    return entry.value as T;
  }

  /**
   * Stocker une valeur dans le cache
   */
  set<T>(key: string, value: T, options?: CacheOptions): void {
    const fullKey = this.generateCacheKey(key, options);
    this.stats.setOperations++;
    
    // Déterminer la durée de vie
    const defaultTier = CacheTier.STANDARD;
    const ttl = options?.ttl ?? this.ttlValues[options?.tier || defaultTier];
    
    // Créer l'entrée de cache
    const entry: CacheEntry<T> = {
      value,
      expiry: Date.now() + ttl * 1000,
      tags: options?.tags,
      hits: 0,
      lastAccessed: Date.now(),
    };
    
    // Stocker dans le cache
    this.cache.set(fullKey, entry);
    
    // Associer les tags si fournis
    if (options?.tags && options.tags.length > 0) {
      this.associateKeyWithTags(fullKey, options.tags);
    }
    
    // Si le cache est trop grand, supprimer les entrées les moins utilisées
    if (this.cache.size > this.maxSize) {
      this.evictLeastRecentlyUsed(1);
    }
  }

  /**
   * Associer une clé à des tags pour l'invalidation groupée
   */
  private associateKeyWithTags(key: string, tags: string[]): void {
    for (const tag of tags) {
      if (!this.tagMap.has(tag)) {
        this.tagMap.set(tag, new Set());
      }
      this.tagMap.get(tag)?.add(key);
    }
  }

  /**
   * Récupérer une valeur du cache ou l'obtenir via une fonction de récupération
   */
  async getOrSet<T>(
    key: string, 
    fetchFn: () => Promise<T>, 
    options?: CacheOptions
  ): Promise<T> {
    // Essayer de récupérer du cache
    const cachedValue = this.get<T>(key, options);
    
    if (cachedValue !== null) {
      return cachedValue;
    }
    
    // Récupérer la valeur via la fonction fournie
    try {
      const value = await fetchFn();
      this.set(key, value, options);
      return value;
    } catch (error) {
      console.error(`Error fetching value for cache key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Vérifier si une clé existe dans le cache et n'est pas expirée
   */
  has(key: string, options?: CacheOptions): boolean {
    const fullKey = this.generateCacheKey(key, options);
    const entry = this.cache.get(fullKey);
    
    return !!entry && entry.expiry > Date.now();
  }

  /**
   * Supprimer une entrée du cache
   */
  delete(key: string, options?: CacheOptions): boolean {
    const fullKey = this.generateCacheKey(key, options);
    return this.cache.delete(fullKey);
  }

  /**
   * Invalider toutes les entrées associées à un tag
   */
  invalidateByTag(tag: string): number {
    this.stats.invalidations++;
    
    const keys = this.tagMap.get(tag);
    if (!keys) {
      return 0;
    }
    
    let count = 0;
    for (const key of keys) {
      if (this.cache.delete(key)) {
        count++;
      }
    }
    
    // Nettoyer le tag
    this.tagMap.delete(tag);
    
    return count;
  }

  /**
   * Invalider toutes les entrées associées à plusieurs tags (opération OR)
   */
  invalidateByTags(tags: string[]): number {
    let totalCount = 0;
    
    for (const tag of tags) {
      totalCount += this.invalidateByTag(tag);
    }
    
    return totalCount;
  }

  /**
   * Vider complètement le cache
   */
  clear(): void {
    this.cache.clear();
    this.tagMap.clear();
    console.log('Cache cleared');
  }

  /**
   * Mettre à jour le taux de succès du cache
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }

  /**
   * Obtenir les statistiques du cache
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Configurer les valeurs TTL pour les différents niveaux
   */
  setTTLValues(values: Partial<Record<CacheTier, number>>): void {
    this.ttlValues = { ...this.ttlValues, ...values };
  }

  /**
   * Configurer la taille maximale du cache
   */
  setMaxSize(size: number): void {
    this.maxSize = size;
    
    // Si le cache est déjà trop grand, nettoyer immédiatement
    if (this.cache.size > this.maxSize) {
      this.evictLeastRecentlyUsed(this.cache.size - this.maxSize);
    }
  }
}

// Exporter l'instance singleton
export const cacheService = CacheService.getInstance();
