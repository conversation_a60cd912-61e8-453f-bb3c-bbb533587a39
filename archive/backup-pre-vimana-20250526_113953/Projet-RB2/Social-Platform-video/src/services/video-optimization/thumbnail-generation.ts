/**
 * Thumbnail Generation Service
 * 
 * This service handles video thumbnail generation, including:
 * - Automatic frame selection
 * - Custom thumbnail upload
 * - Thumbnail preview grid
 */

// Thumbnail options
export interface ThumbnailOptions {
  width: number;
  height: number;
  quality: number; // 0-100
  format: 'jpeg' | 'png' | 'webp';
  timestamp?: number; // Specific timestamp in seconds
  count?: number; // Number of thumbnails to generate
  interval?: number; // Interval between thumbnails in seconds
  autoSelect?: boolean; // Whether to automatically select the best thumbnail
  cropToAspectRatio?: boolean; // Whether to crop to target aspect ratio
  faceDetection?: boolean; // Whether to use face detection for better thumbnails
  blur?: number; // Blur amount (0 = no blur)
  brightness?: number; // Brightness adjustment (-100 to 100)
  contrast?: number; // Contrast adjustment (-100 to 100)
  saturation?: number; // Saturation adjustment (-100 to 100)
}

// Default thumbnail options
export const DEFAULT_THUMBNAIL_OPTIONS: ThumbnailOptions = {
  width: 1280,
  height: 720,
  quality: 85,
  format: 'jpeg',
  count: 9,
  autoSelect: true,
  cropToAspectRatio: true,
  faceDetection: true,
};

// Thumbnail metadata
export interface ThumbnailMetadata {
  url: string;
  width: number;
  height: number;
  timestamp: number; // in seconds
  fileSize: number; // in bytes
  format: string;
  score?: number; // Quality score (0-100)
  hasfaces?: boolean;
  isAutoSelected?: boolean;
}

// Thumbnail generation job status
export enum ThumbnailJobStatus {
  QUEUED = 'queued',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// Thumbnail generation job
export interface ThumbnailJob {
  id: string;
  videoId: string;
  videoUrl: string;
  options: ThumbnailOptions;
  status: ThumbnailJobStatus;
  progress: number; // 0-100
  thumbnails: ThumbnailMetadata[];
  selectedThumbnailIndex?: number;
  startTime?: number;
  endTime?: number;
  error?: string;
}

// Thumbnail job queue
const thumbnailQueue: ThumbnailJob[] = [];
let isProcessing = false;

/**
 * Create a thumbnail generation job
 * @param videoId Video ID
 * @param videoUrl Video URL
 * @param options Thumbnail options
 * @returns Thumbnail job
 */
export function createThumbnailJob(
  videoId: string,
  videoUrl: string,
  options: Partial<ThumbnailOptions> = {}
): ThumbnailJob {
  const jobId = `thumb_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  const job: ThumbnailJob = {
    id: jobId,
    videoId,
    videoUrl,
    options: { ...DEFAULT_THUMBNAIL_OPTIONS, ...options },
    status: ThumbnailJobStatus.QUEUED,
    progress: 0,
    thumbnails: [],
  };
  
  thumbnailQueue.push(job);
  
  // Start processing if not already running
  if (!isProcessing) {
    processNextThumbnailJob();
  }
  
  return job;
}

/**
 * Get a thumbnail job by ID
 * @param jobId Job ID
 * @returns Thumbnail job or undefined if not found
 */
export function getThumbnailJob(jobId: string): ThumbnailJob | undefined {
  return thumbnailQueue.find((job) => job.id === jobId);
}

/**
 * Process the next thumbnail job in the queue
 */
async function processNextThumbnailJob(): Promise<void> {
  // Find the next queued job
  const nextJob = thumbnailQueue.find(
    (job) => job.status === ThumbnailJobStatus.QUEUED
  );
  
  if (!nextJob) {
    isProcessing = false;
    return;
  }
  
  isProcessing = true;
  nextJob.status = ThumbnailJobStatus.PROCESSING;
  nextJob.startTime = Date.now();
  
  try {
    // In a real implementation, this would call a backend service or Web Worker
    // For now, we'll simulate the thumbnail generation process
    await simulateThumbnailGeneration(nextJob);
    
    nextJob.status = ThumbnailJobStatus.COMPLETED;
    nextJob.progress = 100;
    nextJob.endTime = Date.now();
    
    // Process the next job
    processNextThumbnailJob();
  } catch (error) {
    nextJob.status = ThumbnailJobStatus.FAILED;
    nextJob.error = error instanceof Error ? error.message : 'Unknown error';
    nextJob.endTime = Date.now();
    
    // Process the next job
    processNextThumbnailJob();
  }
}

/**
 * Simulate thumbnail generation (for demo purposes)
 * @param job Thumbnail job
 */
async function simulateThumbnailGeneration(job: ThumbnailJob): Promise<void> {
  const { count = 9, interval } = job.options;
  const videoDuration = 180; // Assume 3-minute video
  
  // Calculate interval if not specified
  const actualInterval = interval || (videoDuration / (count + 1));
  
  // Generate thumbnails
  for (let i = 0; i < count; i++) {
    // Simulate processing time
    await new Promise((resolve) => setTimeout(resolve, 200));
    
    // Calculate timestamp
    const timestamp = (i + 1) * actualInterval;
    
    // Generate random quality score (higher for thumbnails in the middle of the video)
    const normalizedPosition = i / (count - 1); // 0 to 1
    const distanceFromMiddle = Math.abs(normalizedPosition - 0.5) * 2; // 0 to 1, 0 is middle
    const baseScore = 70 + Math.random() * 20; // 70-90
    const score = Math.round(baseScore * (1 - distanceFromMiddle * 0.5)); // Reduce score for thumbnails far from middle
    
    // Random face detection
    const hasFaces = Math.random() > 0.3; // 70% chance of having faces
    
    // Create thumbnail metadata
    const thumbnail: ThumbnailMetadata = {
      url: `https://example.com/thumbnails/${job.videoId}_${i}.${job.options.format}`,
      width: job.options.width,
      height: job.options.height,
      timestamp,
      fileSize: Math.floor(Math.random() * 100000) + 50000, // 50-150KB
      format: job.options.format,
      score,
      hasfaces: hasFaces,
    };
    
    job.thumbnails.push(thumbnail);
    job.progress = Math.floor(((i + 1) / count) * 100);
  }
  
  // Auto-select the best thumbnail if enabled
  if (job.options.autoSelect && job.thumbnails.length > 0) {
    // Find the thumbnail with the highest score
    let bestIndex = 0;
    let bestScore = 0;
    
    job.thumbnails.forEach((thumbnail, index) => {
      // Prioritize thumbnails with faces if face detection is enabled
      const faceBonus = job.options.faceDetection && thumbnail.hasfaces ? 10 : 0;
      const totalScore = (thumbnail.score || 0) + faceBonus;
      
      if (totalScore > bestScore) {
        bestScore = totalScore;
        bestIndex = index;
      }
    });
    
    job.selectedThumbnailIndex = bestIndex;
    job.thumbnails[bestIndex].isAutoSelected = true;
  }
}

/**
 * Select a thumbnail from a job
 * @param jobId Job ID
 * @param thumbnailIndex Thumbnail index
 * @returns Whether the thumbnail was successfully selected
 */
export function selectThumbnail(
  jobId: string,
  thumbnailIndex: number
): boolean {
  const job = getThumbnailJob(jobId);
  
  if (!job || job.status !== ThumbnailJobStatus.COMPLETED) {
    return false;
  }
  
  if (thumbnailIndex < 0 || thumbnailIndex >= job.thumbnails.length) {
    return false;
  }
  
  job.selectedThumbnailIndex = thumbnailIndex;
  
  // Reset auto-selected flag
  job.thumbnails.forEach((thumbnail, index) => {
    thumbnail.isAutoSelected = false;
  });
  
  return true;
}

/**
 * Upload a custom thumbnail
 * @param videoId Video ID
 * @param file File object
 * @returns Promise resolving to the thumbnail URL
 */
export async function uploadCustomThumbnail(
  videoId: string,
  file: File
): Promise<string> {
  // In a real implementation, this would upload the file to a server
  // For now, we'll simulate the upload process
  
  // Check file type
  if (!file.type.startsWith('image/')) {
    throw new Error('File must be an image');
  }
  
  // Check file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    throw new Error('File size must be less than 5MB');
  }
  
  // Simulate upload delay
  await new Promise((resolve) => setTimeout(resolve, 1000));
  
  // Generate a fake URL
  const thumbnailUrl = `https://example.com/thumbnails/${videoId}_custom_${Date.now()}.jpg`;
  
  return thumbnailUrl;
}

/**
 * Generate a sprite sheet for video scrubbing
 * @param videoId Video ID
 * @param videoUrl Video URL
 * @param options Options for sprite sheet generation
 * @returns Promise resolving to the sprite sheet URL and metadata
 */
export async function generateSpriteSheet(
  videoId: string,
  videoUrl: string,
  options: {
    width: number;
    height: number;
    columns: number;
    rows: number;
    interval: number; // in seconds
  }
): Promise<{
  url: string;
  width: number;
  height: number;
  tileWidth: number;
  tileHeight: number;
  columns: number;
  rows: number;
  count: number;
  interval: number;
}> {
  // In a real implementation, this would generate a sprite sheet
  // For now, we'll simulate the process
  
  const { width, height, columns, rows, interval } = options;
  const tileWidth = Math.floor(width / columns);
  const tileHeight = Math.floor(height / rows);
  const count = columns * rows;
  
  // Simulate processing delay
  await new Promise((resolve) => setTimeout(resolve, 2000));
  
  return {
    url: `https://example.com/sprites/${videoId}_sprite.jpg`,
    width,
    height,
    tileWidth,
    tileHeight,
    columns,
    rows,
    count,
    interval,
  };
}

/**
 * Extract a frame from a video at a specific timestamp
 * @param videoElement Video element
 * @param timestamp Timestamp in seconds
 * @param options Options for frame extraction
 * @returns Promise resolving to a data URL of the extracted frame
 */
export async function extractVideoFrame(
  videoElement: HTMLVideoElement,
  timestamp: number,
  options: {
    width?: number;
    height?: number;
    format?: 'jpeg' | 'png';
    quality?: number;
  } = {}
): Promise<string> {
  const { width, height, format = 'jpeg', quality = 0.9 } = options;
  
  // Set video to the specified timestamp
  videoElement.currentTime = timestamp;
  
  // Wait for the video to seek to the timestamp
  await new Promise<void>((resolve, reject) => {
    const seeked = () => {
      videoElement.removeEventListener('seeked', seeked);
      resolve();
    };
    
    const error = (e: Event) => {
      videoElement.removeEventListener('error', error);
      reject(new Error('Error seeking video'));
    };
    
    videoElement.addEventListener('seeked', seeked);
    videoElement.addEventListener('error', error);
  });
  
  // Create a canvas to draw the video frame
  const canvas = document.createElement('canvas');
  canvas.width = width || videoElement.videoWidth;
  canvas.height = height || videoElement.videoHeight;
  
  // Draw the video frame on the canvas
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('Could not get canvas context');
  }
  
  ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
  
  // Convert the canvas to a data URL
  const dataUrl = canvas.toDataURL(`image/${format}`, quality);
  
  return dataUrl;
}
