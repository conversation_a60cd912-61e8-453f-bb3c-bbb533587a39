const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

// Simulation d'une base de données pour les articles de blog
const blogPosts = [];
const blogComments = {};
const blogLikes = {};

/**
 * Service pour la gestion des articles de blog
 */
class BlogService {
  /**
   * Récupère tous les articles de blog avec filtres optionnels
   * @param {Object} filters Filtres à appliquer
   * @returns {Promise<Array>} Liste des articles de blog
   */
  async getBlogPosts(filters = {}) {
    try {
      let filteredPosts = [...blogPosts];
      
      // Appliquer les filtres
      if (filters.authorId) {
        filteredPosts = filteredPosts.filter(post => post.authorId === filters.authorId);
      }
      
      if (filters.category) {
        filteredPosts = filteredPosts.filter(post => post.category === filters.category);
      }
      
      if (filters.tag) {
        filteredPosts = filteredPosts.filter(post => post.tags && post.tags.includes(filters.tag));
      }
      
      // Tri par date de publication (du plus récent au plus ancien)
      filteredPosts.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt));
      
      return filteredPosts;
    } catch (error) {
      logger.error(`Error in getBlogPosts: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Récupère un article de blog par son ID
   * @param {string} id ID de l'article de blog
   * @returns {Promise<Object|null>} Article de blog trouvé ou null
   */
  async getBlogPostById(id) {
    try {
      const post = blogPosts.find(post => post.id === id) || null;
      
      if (post) {
        // Ajouter le nombre de commentaires et de likes
        post.commentCount = (blogComments[id] || []).length;
        post.likeCount = Object.keys(blogLikes[id] || {}).length;
      }
      
      return post;
    } catch (error) {
      logger.error(`Error in getBlogPostById: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Crée un nouvel article de blog
   * @param {Object} blogPostData Données de l'article de blog
   * @returns {Promise<Object>} Article de blog créé
   */
  async createBlogPost(blogPostData) {
    try {
      const newBlogPost = {
        id: uuidv4(),
        ...blogPostData,
        publishedAt: blogPostData.publishedAt || new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      blogPosts.push(newBlogPost);
      blogComments[newBlogPost.id] = [];
      blogLikes[newBlogPost.id] = {};
      
      return newBlogPost;
    } catch (error) {
      logger.error(`Error in createBlogPost: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Met à jour un article de blog existant
   * @param {string} id ID de l'article de blog
   * @param {Object} updateData Données à mettre à jour
   * @returns {Promise<Object|null>} Article de blog mis à jour ou null
   */
  async updateBlogPost(id, updateData) {
    try {
      const index = blogPosts.findIndex(post => post.id === id);
      
      if (index === -1) {
        return null;
      }
      
      // Empêcher la modification de certains champs
      const { id: _, createdAt, authorId, authorName, ...allowedUpdates } = updateData;
      
      // Mettre à jour l'article de blog
      blogPosts[index] = {
        ...blogPosts[index],
        ...allowedUpdates,
        updatedAt: new Date().toISOString(),
      };
      
      return blogPosts[index];
    } catch (error) {
      logger.error(`Error in updateBlogPost: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Supprime un article de blog
   * @param {string} id ID de l'article de blog
   * @returns {Promise<boolean>} Succès de la suppression
   */
  async deleteBlogPost(id) {
    try {
      const index = blogPosts.findIndex(post => post.id === id);
      
      if (index === -1) {
        return false;
      }
      
      // Supprimer l'article de blog
      blogPosts.splice(index, 1);
      
      // Supprimer les commentaires et les likes associés
      delete blogComments[id];
      delete blogLikes[id];
      
      return true;
    } catch (error) {
      logger.error(`Error in deleteBlogPost: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Récupère les commentaires d'un article de blog
   * @param {string} id ID de l'article de blog
   * @returns {Promise<Array>} Liste des commentaires
   */
  async getBlogPostComments(id) {
    try {
      return blogComments[id] || [];
    } catch (error) {
      logger.error(`Error in getBlogPostComments: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Ajoute un commentaire à un article de blog
   * @param {string} id ID de l'article de blog
   * @param {Object} commentData Données du commentaire
   * @returns {Promise<Object>} Commentaire créé
   */
  async addBlogPostComment(id, commentData) {
    try {
      // Vérifier que l'article de blog existe
      const blogPost = await this.getBlogPostById(id);
      
      if (!blogPost) {
        throw new Error('Blog post not found');
      }
      
      // Créer le commentaire
      const newComment = {
        id: uuidv4(),
        ...commentData,
        blogPostId: id,
        createdAt: new Date().toISOString(),
      };
      
      // Ajouter le commentaire
      if (!blogComments[id]) {
        blogComments[id] = [];
      }
      
      blogComments[id].push(newComment);
      
      return newComment;
    } catch (error) {
      logger.error(`Error in addBlogPostComment: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Ajoute ou retire un like d'un article de blog
   * @param {string} id ID de l'article de blog
   * @param {string} userId ID de l'utilisateur
   * @param {string} action Action à effectuer ('like' ou 'unlike')
   * @returns {Promise<Object>} Résultat de l'opération
   */
  async toggleBlogPostLike(id, userId, action = 'like') {
    try {
      // Vérifier que l'article de blog existe
      const blogPost = await this.getBlogPostById(id);
      
      if (!blogPost) {
        throw new Error('Blog post not found');
      }
      
      // Initialiser la structure si nécessaire
      if (!blogLikes[id]) {
        blogLikes[id] = {};
      }
      
      if (action === 'like') {
        // Ajouter le like
        blogLikes[id][userId] = true;
      } else {
        // Retirer le like
        delete blogLikes[id][userId];
      }
      
      // Compter les likes
      const likeCount = Object.keys(blogLikes[id]).length;
      
      return {
        blogPostId: id,
        likeCount,
        liked: action === 'like',
      };
    } catch (error) {
      logger.error(`Error in toggleBlogPostLike: ${error.message}`, error);
      throw error;
    }
  }
}

module.exports = new BlogService();
