import { useState, useEffect } from 'react';
import { usePostsStore } from '@/store/posts';
import { PostCard } from '@/components/post-card';
import { RecommendedContent } from '@/components/recommendations/RecommendedContent';
import { HomePageRecommendations } from '@/components/recommendations/HomePageRecommendations';
import { Loader2, Home as HomeIcon, Compass, Flame as Fire, Sparkles, ChevronDown, ChevronUp } from 'lucide-react';
import { useRecommendationsStore } from '@/store/recommendations';
import { StoriesCarousel } from '@/components/stories/StoriesCarousel';
import { StoryViewer } from '@/components/stories/StoryViewer';
import { useStoriesStore } from '@/store/stories';
import { useNavigate } from 'react-router-dom';

export function Home() {
  const { posts, isLoading: postsLoading, error: postsError, fetchPosts } = usePostsStore();
  const {
    feedItems,
    trendingTopics,
    isLoading: recommendationsLoading,
    error: recommendationsError,
    fetchPersonalizedFeed,
    fetchTrendingTopics
  } = useRecommendationsStore();
  const {
    isViewerOpen
  } = useStoriesStore();

  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'foryou' | 'following' | 'trending'>('foryou');
  const [showTopics, setShowTopics] = useState(false);

  useEffect(() => {
    fetchPosts();
    fetchPersonalizedFeed();
    fetchTrendingTopics();
  }, []);

  const isLoading = (activeTab === 'following' && postsLoading) ||
                    ((activeTab === 'foryou' || activeTab === 'trending') && recommendationsLoading);

  const error = (activeTab === 'following' && postsError) ||
                ((activeTab === 'foryou' || activeTab === 'trending') && recommendationsError);

  const handleCreateStory = () => {
    navigate('/stories/create');
  };

  if (isLoading && posts.length === 0 && feedItems.length === 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 size={32} className="animate-spin text-green-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={() => {
              if (activeTab === 'following') {
                fetchPosts();
              } else {
                fetchPersonalizedFeed();
              }
            }}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen pb-16 ${activeTab === 'following' ? 'bg-white' : 'bg-gray-50'}`}>
      {/* Common Tabs */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="flex justify-center">
          <div className="flex">
            <button
              onClick={() => setActiveTab('foryou')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'foryou'
                  ? 'text-green-500 border-b-2 border-green-500'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Sparkles size={16} className="inline mr-2" />
              For You
            </button>

            <button
              onClick={() => setActiveTab('following')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'following'
                  ? 'text-green-500 border-b-2 border-green-500'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <HomeIcon size={16} className="inline mr-2" />
              Following
            </button>

            <button
              onClick={() => setActiveTab('trending')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'trending'
                  ? 'text-green-500 border-b-2 border-green-500'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Fire size={16} className="inline mr-2" />
              Trending
            </button>
          </div>
        </div>
      </div>

      {/* Common Stories Carousel */}
      <div className="bg-white border-b py-2">
        <StoriesCarousel onCreateClick={handleCreateStory} />
      </div>

      {/* Conditional Content */}
      {activeTab === 'following' ? (
        <main className="fixed inset-0 top-28 bg-black"> {/* top-28 accounts for Tabs and Stories height */}
          <div className="h-full snap-y snap-mandatory overflow-y-scroll">
            {posts.map((post) => (
              <PostCard key={post.id} post={post} />
            ))}
          </div>
        </main>
      ) : ( // 'foryou' or 'trending'
        <>
          {/* Topics Bar (only for 'trending') */}
          {activeTab === 'trending' && trendingTopics.length > 0 && (
            <div className="bg-white border-b sticky top-12 z-10"> {/* top-12 to stick below main tabs */}
              <div className="container mx-auto px-4">
                <div className="flex items-center py-2">
                  <div className="flex-1 overflow-x-auto whitespace-nowrap scrollbar-hide">
                    <div className="flex space-x-2">
                      {trendingTopics.slice(0, 10).map((topic) => (
                        <button
                          key={topic.id}
                          className="px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex-shrink-0"
                        >
                          {topic.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  <button
                    onClick={() => setShowTopics(!showTopics)}
                    className="ml-2 p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 focus:outline-none"
                  >
                    {showTopics ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                  </button>
                </div>

                {showTopics && (
                  <div className="py-3 border-t">
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                      {trendingTopics.map((topic) => (
                        <button
                          key={topic.id}
                          className="px-3 py-2 text-sm bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center justify-between"
                        >
                          <span>{topic.name}</span>
                          <span className="text-xs text-gray-500">{topic.count.toLocaleString()}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="container mx-auto px-4 py-6">
            {activeTab === 'foryou' && (
              <>
                {/* Use the new HomePageRecommendations component */}
                <HomePageRecommendations className="mb-8" />

                <div className="mb-8">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-semibold flex items-center">
                      <Compass size={18} className="mr-2 text-green-500" />
                      Discover New Creators
                    </h2>
                    <a href="/explore" className="text-sm text-green-500 hover:text-green-600">
                      View All
                    </a>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Placeholder for recommended creators */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                        <div>
                          <div className="h-4 w-24 bg-gray-200 rounded"></div>
                          <div className="h-3 w-16 bg-gray-100 rounded mt-2"></div>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                        <div>
                          <div className="h-4 w-24 bg-gray-200 rounded"></div>
                          <div className="h-3 w-16 bg-gray-100 rounded mt-2"></div>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                        <div>
                          <div className="h-4 w-24 bg-gray-200 rounded"></div>
                          <div className="h-3 w-16 bg-gray-100 rounded mt-2"></div>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                        <div>
                          <div className="h-4 w-24 bg-gray-200 rounded"></div>
                          <div className="h-3 w-16 bg-gray-100 rounded mt-2"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}

            {activeTab === 'trending' && (
              <>
                <div className="mb-8">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-semibold flex items-center">
                      <Fire size={18} className="mr-2 text-red-500" />
                      Trending Now
                    </h2>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {feedItems.slice(0, 6).map((item, index) => (
                      <div key={item.id} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
                        <div className="relative aspect-video bg-gray-100">
                          <img
                            src={item.thumbnailUrl}
                            alt={item.title}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                            #{index + 1}
                          </div>
                        </div>
                        <div className="p-3">
                          <h3 className="font-medium text-sm line-clamp-2">{item.title}</h3>
                          <p className="text-xs text-gray-500 mt-1">{item.creator.name}</p>
                          <div className="flex items-center text-xs text-gray-500 mt-1">
                            <span>{item.stats.views.toLocaleString()} views</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <RecommendedContent
                  title="Trending in Wellness"
                  subtitle="Popular content in wellness and mindfulness"
                  limit={3}
                  className="mb-8"
                />

                <RecommendedContent
                  title="Trending in Fitness"
                  subtitle="Popular workouts and fitness tips"
                  limit={3}
                  className="mb-8"
                />
              </>
            )}
          </div>
        </>
      )}

      {/* Common Story Viewer */}
      {isViewerOpen && <StoryViewer />}
    </div>
  );
}