import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Settings, Grid, Bookmark, Heart, Loader2, Edit, Users, UserPlus, Calendar } from 'lucide-react';
import { Avatar } from '@/components/ui/avatar';
import { ProfileEditor } from '@/components/profile/ProfileEditor';
import { useProfileStore } from '@/store/profile';
import { SocialLink } from '@/api/profileApi';

const TABS = [
  { id: 'posts', icon: Grid, label: 'Posts' },
  { id: 'saved', icon: Bookmark, label: 'Saved' },
  { id: 'liked', icon: Heart, label: 'Liked' }
];

// Mock data for when API is not available
const MOCK_PROFILE = {
  id: '1',
  username: 'yogamaster',
  name: '<PERSON>',
  avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400',
  coverPhoto: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?w=900',
  bio: '🧘‍♀️ Yoga Instructor & Wellness Coach\n✨ Sharing daily wellness tips\n🌿 Living mindfully',
  website: 'https://sarahwilson.yoga',
  location: 'Bali, Indonesia',
  socialLinks: [
    { platform: 'Instagram', url: 'https://instagram.com/sarahwilson' },
    { platform: 'YouTube', url: 'https://youtube.com/sarahwilson' }
  ],
  stats: {
    posts: 156,
    followers: 12500,
    following: 891
  }
};

export function Profile() {
  const navigate = useNavigate();
  const { userId = 'me' } = useParams<{ userId?: string }>();
  const {
    profile,
    isLoading,
    error,
    isEditing,
    fetchProfile,
    updateUserProfile,
    setIsEditing
  } = useProfileStore();

  const [activeTab, setActiveTab] = useState('posts');

  // Use mock data if API is not available
  const profileData = profile || MOCK_PROFILE;
  const isCurrentUser = userId === 'me' || userId === profileData.id;

  useEffect(() => {
    fetchProfile(userId);
  }, [userId, fetchProfile]);

  const handleEditProfile = () => {
    setIsEditing(true);
  };

  const handleSaveProfile = async (data: {
    name: string;
    username: string;
    bio?: string;
    website?: string;
    location?: string;
    avatar: string;
    coverPhoto: string;
    socialLinks: SocialLink[];
  }) => {
    await updateUserProfile(profileData.id, data);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (isLoading && !profile) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <Loader2 size={32} className="animate-spin text-green-500" />
      </div>
    );
  }

  if (error && !profile) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center p-4">
        <p className="text-red-500 mb-4">Error loading profile: {error}</p>
        <button
          onClick={() => fetchProfile(userId)}
          className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (isEditing) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <ProfileEditor
          initialData={{
            name: profileData.name,
            username: profileData.username,
            bio: profileData.bio,
            website: profileData.website,
            location: profileData.location,
            avatar: profileData.avatar,
            coverPhoto: profileData.coverPhoto,
            socialLinks: profileData.socialLinks,
          }}
          onSave={handleSaveProfile}
          onCancel={() => setIsEditing(false)}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white pb-16">
      {/* Header */}
      <div className="sticky top-0 bg-white p-4 border-b border-gray-200 z-10">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold">@{profileData.username}</h1>
          {isCurrentUser && (
            <button
              className="p-2 hover:bg-gray-100 rounded-full"
              onClick={handleEditProfile}
            >
              <Settings className="w-6 h-6" />
            </button>
          )}
        </div>
      </div>

      {/* Cover Photo */}
      <div
        className="h-40 bg-cover bg-center"
        style={{ backgroundImage: `url(${profileData.coverPhoto})` }}
      />

      {/* Profile Info */}
      <div className="p-4">
        <div className="flex items-start gap-4 mb-6 relative">
          <Avatar
            src={profileData.avatar}
            alt={profileData.name}
            className="w-20 h-20 ring-4 ring-white shadow-lg -mt-10"
          />
          <div className="flex-1 pt-2">
            <div className="flex justify-between items-start">
              <div>
                <h2 className="text-xl font-semibold">{profileData.name}</h2>
                <p className="text-gray-500 text-sm">@{profileData.username}</p>
              </div>

              {isCurrentUser ? (
                <div className="flex space-x-2">
                  <button
                    onClick={() => navigate('/content/scheduled')}
                    className="px-4 py-1.5 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 flex items-center"
                  >
                    <Calendar size={16} className="mr-1" />
                    Scheduled
                  </button>
                  <button
                    onClick={handleEditProfile}
                    className="px-4 py-1.5 bg-gray-100 text-gray-800 rounded-full hover:bg-gray-200 flex items-center"
                  >
                    <Edit size={16} className="mr-1" />
                    Edit Profile
                  </button>
                </div>
              ) : (
                <button className="px-4 py-1.5 bg-green-500 text-white rounded-full hover:bg-green-600 flex items-center">
                  <UserPlus size={16} className="mr-1" />
                  Follow
                </button>
              )}
            </div>

            {profileData.bio && (
              <p className="text-gray-600 whitespace-pre-line text-sm mt-3">
                {profileData.bio}
              </p>
            )}

            <div className="flex flex-wrap gap-x-4 gap-y-2 mt-3 text-sm text-gray-500">
              {profileData.location && (
                <span>📍 {profileData.location}</span>
              )}

              {profileData.website && (
                <a
                  href={profileData.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  🔗 {profileData.website.replace(/^https?:\/\//, '')}
                </a>
              )}
            </div>

            {profileData.socialLinks && profileData.socialLinks.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {profileData.socialLinks.map((link) => (
                  <a
                    key={link.platform}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs px-2 py-1 bg-gray-100 rounded-full text-gray-700 hover:bg-gray-200"
                  >
                    {link.platform}
                  </a>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="flex justify-around mb-6 py-3 border-y border-gray-100">
          <div className="text-center">
            <div className="font-semibold">{formatNumber(profileData.stats.posts)}</div>
            <div className="text-sm text-gray-500">Posts</div>
          </div>

          <Link
            to={`/profile/${profileData.id}/followers`}
            className="text-center"
          >
            <div className="font-semibold">{formatNumber(profileData.stats.followers)}</div>
            <div className="text-sm text-gray-500 flex items-center justify-center">
              <Users size={14} className="mr-1" />
              Followers
            </div>
          </Link>

          <Link
            to={`/profile/${profileData.id}/following`}
            className="text-center"
          >
            <div className="font-semibold">{formatNumber(profileData.stats.following)}</div>
            <div className="text-sm text-gray-500">Following</div>
          </Link>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          {TABS.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 flex flex-col items-center gap-1 py-3 text-sm font-medium ${
                activeTab === tab.id
                  ? 'text-green-500 border-b-2 border-green-500'
                  : 'text-gray-500 hover:text-gray-900'
              }`}
            >
              <tab.icon className="w-5 h-5" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="pt-4 text-center text-gray-500">
          {activeTab === 'posts' && <p>No posts yet</p>}
          {activeTab === 'saved' && <p>No saved posts</p>}
          {activeTab === 'liked' && <p>No liked posts</p>}
        </div>
      </div>
    </div>
  );
}