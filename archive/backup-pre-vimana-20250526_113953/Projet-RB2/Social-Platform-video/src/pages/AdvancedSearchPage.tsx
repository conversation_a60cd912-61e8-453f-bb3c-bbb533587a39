import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Search,
  Filter,
  X,
  ChevronDown,
  ChevronUp,
  Loader2,
  Clock,
  Eye,
  ThumbsUp,
  MessageSquare,
  RefreshCw,
  AlertCircle,
  SlidersHorizontal,
  MapPin
} from 'lucide-react';
import { CategoryFilter, Category } from '../components/search/CategoryFilter';
import { SortOptions, videoSortOptions } from '../components/search/SortOptions';
import { LocationFilter, Location } from '../components/search/LocationFilter';
import { DateRangeFilter, DateRange } from '../components/search/DateRangeFilter';
import { useAdvancedSearch, mockVideoSearch } from '../hooks/useAdvancedSearch';
import { format, formatDistanceToNow } from 'date-fns';

// Mock categories
const mockCategories: Category[] = [
  { id: 'yoga', name: 'Yoga', slug: 'yoga', count: 120, color: '#4ade80' },
  { id: 'meditation', name: 'Meditation', slug: 'meditation', count: 85, color: '#60a5fa' },
  { id: 'fitness', name: 'Fitness', slug: 'fitness', count: 210, color: '#f97316' },
  { id: 'wellness', name: 'Wellness', slug: 'wellness', count: 95, color: '#8b5cf6' },
  { id: 'nutrition', name: 'Nutrition', slug: 'nutrition', count: 75, color: '#ec4899' },
  { id: 'mindfulness', name: 'Mindfulness', slug: 'mindfulness', count: 65, color: '#14b8a6' },
  { id: 'spirituality', name: 'Spirituality', slug: 'spirituality', count: 45, color: '#f59e0b' },
  { id: 'nature', name: 'Nature', slug: 'nature', count: 55, color: '#10b981' },
  { id: 'travel', name: 'Travel', slug: 'travel', count: 110, color: '#6366f1' },
  { id: 'healing', name: 'Healing', slug: 'healing', count: 40, color: '#ef4444' },
  { id: 'self-care', name: 'Self Care', slug: 'self-care', count: 80, color: '#0ea5e9' },
  { id: 'personal-growth', name: 'Personal Growth', slug: 'personal-growth', count: 60, color: '#84cc16' }
];

// Mock popular locations
const mockPopularLocations: Location[] = [
  {
    id: 'bali',
    name: 'Bali',
    country: 'Indonesia',
    type: 'region',
    coordinates: { latitude: -8.4095, longitude: 115.1889 }
  },
  {
    id: 'tulum',
    name: 'Tulum',
    country: 'Mexico',
    type: 'city',
    coordinates: { latitude: 20.2114, longitude: -87.4654 }
  },
  {
    id: 'costa-rica',
    name: 'Costa Rica',
    country: 'Costa Rica',
    type: 'country',
    coordinates: { latitude: 9.7489, longitude: -83.7534 }
  },
  {
    id: 'sedona',
    name: 'Sedona',
    region: 'Arizona',
    country: 'USA',
    type: 'city',
    coordinates: { latitude: 34.8697, longitude: -111.7601 }
  }
];

// Video interface
interface Video {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: number;
  views: number;
  likes: number;
  comments: number;
  createdAt: string;
  creator: {
    id: string;
    name: string;
    avatarUrl: string;
  };
  categories: string[];
}

export function AdvancedSearchPage() {
  const navigate = useNavigate();
  const location = useLocation();

  // Parse query parameters
  const queryParams = new URLSearchParams(location.search);
  const initialQuery = queryParams.get('q') || '';
  const initialCategories = queryParams.get('categories')?.split(',') || [];

  // State for filter visibility on mobile
  const [showFilters, setShowFilters] = useState(false);

  // State for expanded filters
  const [expandedFilters, setExpandedFilters] = useState({
    categories: true,
    sort: true,
    location: true,
    dateRange: true
  });

  // Initialize search hook
  const {
    filters,
    results,
    updateQuery,
    updateCategories,
    updateLocation,
    updateRadius,
    updateDateRange,
    updateSortBy,
    loadNextPage,
    loadPreviousPage,
    resetFilters,
    executeSearch
  } = useAdvancedSearch<Video>({
    initialFilters: {
      query: initialQuery,
      categories: initialCategories,
      sortBy: 'trending'
    },
    searchFunction: mockVideoSearch,
    autoSearch: true
  });

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();

    if (filters.query) {
      params.set('q', filters.query);
    }

    if (filters.categories.length > 0) {
      params.set('categories', filters.categories.join(','));
    }

    if (filters.sortBy && filters.sortBy !== 'trending') {
      params.set('sort', filters.sortBy);
    }

    if (filters.location) {
      params.set('location', filters.location.id);
    }

    if (filters.dateRange.startDate) {
      params.set('from', filters.dateRange.startDate.toISOString());
    }

    if (filters.dateRange.endDate) {
      params.set('to', filters.dateRange.endDate.toISOString());
    }

    const newSearch = params.toString();
    if (newSearch !== location.search.substring(1)) {
      navigate(`/search?${newSearch}`, { replace: true });
    }
  }, [filters, navigate, location.search]);

  // Toggle filter visibility on mobile
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Toggle expanded state for a filter
  const toggleExpandedFilter = (filterName: keyof typeof expandedFilters) => {
    setExpandedFilters(prev => ({
      ...prev,
      [filterName]: !prev[filterName]
    }));
  };

  // Format duration
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Format view count
  const formatViewCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Get category name by ID
  const getCategoryName = (categoryId: string) => {
    const category = mockCategories.find(cat => cat.id === categoryId);
    return category?.name || categoryId;
  };

  // Get category color by ID
  const getCategoryColor = (categoryId: string) => {
    const category = mockCategories.find(cat => cat.id === categoryId);
    return category?.color || '#e5e7eb';
  };

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    executeSearch();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Advanced Search</h1>
          <p className="text-gray-600">
            Find videos, retreats, and professionals with powerful filtering options
          </p>
        </div>

        {/* Search form */}
        <form onSubmit={handleSearchSubmit} className="mb-6">
          <div className="flex">
            <div className="relative flex-1">
              <input
                type="text"
                value={filters.query}
                onChange={(e) => updateQuery(e.target.value)}
                placeholder="Search for videos, retreats, or professionals..."
                className="w-full p-3 pl-10 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />

              {filters.query && (
                <button
                  type="button"
                  onClick={() => updateQuery('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X size={16} />
                </button>
              )}
            </div>

            <button
              type="submit"
              className="px-6 py-3 bg-green-500 text-white rounded-r-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Search
            </button>
          </div>
        </form>

        {/* Mobile filter toggle */}
        <div className="lg:hidden mb-4">
          <button
            type="button"
            onClick={toggleFilters}
            className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50"
          >
            <Filter size={16} className="mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
            {showFilters ? <ChevronUp size={16} className="ml-2" /> : <ChevronDown size={16} className="ml-2" />}
          </button>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters sidebar */}
          <div
            className={`w-full lg:w-64 flex-shrink-0 space-y-4 ${
              showFilters ? 'block' : 'hidden lg:block'
            }`}
          >
            {/* Active filters summary */}
            {(filters.categories.length > 0 || filters.location ||
              filters.dateRange.startDate || filters.dateRange.endDate) && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold flex items-center">
                    <Filter size={18} className="mr-2 text-gray-500" />
                    Active Filters
                  </h3>
                  <button
                    type="button"
                    onClick={resetFilters}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    Clear All
                  </button>
                </div>

                <div className="space-y-2">
                  {filters.categories.length > 0 && (
                    <div>
                      <span className="text-xs text-gray-500">Categories:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {filters.categories.map(categoryId => (
                          <div
                            key={categoryId}
                            className="flex items-center text-xs rounded-full px-2 py-1"
                            style={{ backgroundColor: `${getCategoryColor(categoryId)}20`, color: getCategoryColor(categoryId) }}
                          >
                            <span>{getCategoryName(categoryId)}</span>
                            <button
                              type="button"
                              onClick={() => updateCategories(filters.categories.filter(id => id !== categoryId))}
                              className="ml-1 hover:text-gray-800"
                            >
                              <X size={12} />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {filters.location && (
                    <div>
                      <span className="text-xs text-gray-500">Location:</span>
                      <div className="flex items-center mt-1 text-sm">
                        <MapPin size={14} className="mr-1 text-gray-500" />
                        {filters.location.name}
                        <button
                          type="button"
                          onClick={() => updateLocation(null)}
                          className="ml-1 text-gray-500 hover:text-gray-700"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    </div>
                  )}

                  {(filters.dateRange.startDate || filters.dateRange.endDate) && (
                    <div>
                      <span className="text-xs text-gray-500">Date Range:</span>
                      <div className="flex items-center mt-1 text-sm">
                        <Clock size={14} className="mr-1 text-gray-500" />
                        {filters.dateRange.startDate && filters.dateRange.endDate ? (
                          `${format(filters.dateRange.startDate, 'MMM d, yyyy')} - ${format(filters.dateRange.endDate, 'MMM d, yyyy')}`
                        ) : filters.dateRange.startDate ? (
                          `From ${format(filters.dateRange.startDate, 'MMM d, yyyy')}`
                        ) : (
                          `Until ${format(filters.dateRange.endDate!, 'MMM d, yyyy')}`
                        )}
                        <button
                          type="button"
                          onClick={() => updateDateRange({ startDate: null, endDate: null })}
                          className="ml-1 text-gray-500 hover:text-gray-700"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Category filter */}
            <CategoryFilter
              categories={mockCategories}
              selectedCategories={filters.categories}
              onCategoryChange={updateCategories}
              isExpanded={expandedFilters.categories}
              onToggleExpand={() => toggleExpandedFilter('categories')}
            />

            {/* Sort options */}
            <SortOptions
              options={videoSortOptions}
              selectedOption={filters.sortBy}
              onOptionChange={updateSortBy}
              isExpanded={expandedFilters.sort}
              onToggleExpand={() => toggleExpandedFilter('sort')}
              variant="card"
            />

            {/* Location filter */}
            <LocationFilter
              selectedLocation={filters.location}
              onLocationChange={updateLocation}
              onRadiusChange={updateRadius}
              radius={filters.radius}
              popularLocations={mockPopularLocations}
              isExpanded={expandedFilters.location}
              onToggleExpand={() => toggleExpandedFilter('location')}
            />

            {/* Date range filter */}
            <DateRangeFilter
              selectedRange={filters.dateRange}
              onRangeChange={updateDateRange}
              isExpanded={expandedFilters.dateRange}
              onToggleExpand={() => toggleExpandedFilter('dateRange')}
            />
          </div>

          {/* Search results */}
          <div className="flex-1">
            {/* Results header */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-lg font-semibold">
                    {results.isLoading ? (
                      'Searching...'
                    ) : results.error ? (
                      'Search Error'
                    ) : results.totalCount === 0 ? (
                      'No Results Found'
                    ) : (
                      `${results.totalCount} Results`
                    )}
                  </h2>
                  {!results.isLoading && !results.error && results.totalCount > 0 && (
                    <p className="text-sm text-gray-500">
                      Showing {(results.page - 1) * filters.limit + 1} - {Math.min(results.page * filters.limit, results.totalCount)} of {results.totalCount} results
                    </p>
                  )}
                </div>

                <div className="flex items-center">
                  <SortOptions
                    options={videoSortOptions}
                    selectedOption={filters.sortBy}
                    onOptionChange={updateSortBy}
                    variant="dropdown"
                  />

                  <button
                    type="button"
                    onClick={executeSearch}
                    className="ml-2 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
                    title="Refresh results"
                  >
                    <RefreshCw size={18} />
                  </button>
                </div>
              </div>
            </div>

            {/* Results content */}
            {results.isLoading && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 flex flex-col items-center justify-center">
                <Loader2 size={48} className="text-green-500 animate-spin mb-4" />
                <p className="text-gray-600">Searching for results...</p>
              </div>
            )}

            {results.error && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 flex flex-col items-center justify-center">
                <AlertCircle size={48} className="text-red-500 mb-4" />
                <p className="text-red-600 font-medium mb-2">{results.error}</p>
                <button
                  type="button"
                  onClick={executeSearch}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 mt-2"
                >
                  Try Again
                </button>
              </div>
            )}

            {!results.isLoading && !results.error && results.items.length === 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 flex flex-col items-center justify-center">
                <SlidersHorizontal size={48} className="text-gray-400 mb-4" />
                <p className="text-gray-600 font-medium mb-2">No results found</p>
                <p className="text-gray-500 text-center mb-4">
                  Try adjusting your search terms or filters to find what you're looking for.
                </p>
                <button
                  type="button"
                  onClick={resetFilters}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Clear All Filters
                </button>
              </div>
            )}

            {!results.isLoading && !results.error && results.items.length > 0 && (
              <div className="space-y-4">
                {results.items.map(video => (
                  <div key={video.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div className="flex flex-col sm:flex-row">
                      {/* Thumbnail */}
                      <div className="sm:w-64 flex-shrink-0 relative">
                        <img
                          src={video.thumbnailUrl}
                          alt={video.title}
                          className="w-full h-48 sm:h-full object-cover"
                        />
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 rounded">
                          {formatDuration(video.duration)}
                        </div>
                      </div>

                      {/* Content */}
                      <div className="p-4 flex-1">
                        <h3 className="text-lg font-semibold mb-1 line-clamp-2">
                          {video.title}
                        </h3>

                        <div className="flex items-center mb-2">
                          <img
                            src={video.creator.avatarUrl}
                            alt={video.creator.name}
                            className="w-6 h-6 rounded-full mr-2"
                          />
                          <span className="text-sm text-gray-700">{video.creator.name}</span>
                        </div>

                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                          {video.description}
                        </p>

                        <div className="flex flex-wrap gap-1 mb-3">
                          {video.categories.slice(0, 3).map(categoryId => (
                            <span
                              key={categoryId}
                              className="text-xs rounded-full px-2 py-0.5"
                              style={{ backgroundColor: `${getCategoryColor(categoryId)}20`, color: getCategoryColor(categoryId) }}
                            >
                              {getCategoryName(categoryId)}
                            </span>
                          ))}
                          {video.categories.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{video.categories.length - 3} more
                            </span>
                          )}
                        </div>

                        <div className="flex items-center text-xs text-gray-500 space-x-3">
                          <span className="flex items-center">
                            <Eye size={14} className="mr-1" />
                            {formatViewCount(video.views)} views
                          </span>
                          <span className="flex items-center">
                            <ThumbsUp size={14} className="mr-1" />
                            {formatViewCount(video.likes)}
                          </span>
                          <span className="flex items-center">
                            <MessageSquare size={14} className="mr-1" />
                            {formatViewCount(video.comments)}
                          </span>
                          <span className="flex items-center">
                            <Clock size={14} className="mr-1" />
                            {formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Pagination */}
                {results.totalPages > 1 && (
                  <div className="flex justify-between items-center mt-6">
                    <button
                      type="button"
                      onClick={loadPreviousPage}
                      disabled={results.page === 1}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>

                    <span className="text-sm text-gray-600">
                      Page {results.page} of {results.totalPages}
                    </span>

                    <button
                      type="button"
                      onClick={loadNextPage}
                      disabled={results.page === results.totalPages}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
