import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Loader2, AlertCircle } from 'lucide-react';
import { ContentCreationForm } from '../components/video/ContentCreationForm';
import { 
  getVideoDetails, 
  publishVideo, 
  saveDraft, 
  loadDraft, 
  deleteDraft, 
  schedulePublish,
  getSuggestedHashtags,
  getSuggestedMentions,
  getPopularLocations,
  getDrafts
} from '../api/videoApi';

interface VideoMetadata {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: number;
  videoUrl: string;
  status: string;
}

export function ContentCreationPage() {
  const { videoId } = useParams<{ videoId: string }>();
  const navigate = useNavigate();
  
  const [videoMetadata, setVideoMetadata] = useState<VideoMetadata | null>(null);
  const [initialData, setInitialData] = useState<Record<string, any>>({});
  const [drafts, setDrafts] = useState<any[]>([]);
  const [suggestedHashtags, setSuggestedHashtags] = useState<any[]>([]);
  const [suggestedMentions, setSuggestedMentions] = useState<any[]>([]);
  const [popularLocations, setPopularLocations] = useState<any[]>([]);
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Load video data
  useEffect(() => {
    const loadVideoData = async () => {
      if (!videoId) {
        setError('Video ID is required');
        setIsLoading(false);
        return;
      }
      
      try {
        // Load video metadata
        const metadata = await getVideoDetails(videoId);
        setVideoMetadata(metadata);
        
        // Load initial data (most recent draft or empty)
        const draftsData = await getDrafts(videoId);
        setDrafts(draftsData);
        
        if (draftsData.length > 0) {
          // Use the most recent draft as initial data
          const mostRecentDraft = draftsData.sort(
            (a, b) => new Date(b.lastSaved).getTime() - new Date(a.lastSaved).getTime()
          )[0];
          
          const draftData = await loadDraft(mostRecentDraft.id);
          setInitialData(draftData);
        } else {
          // Use video metadata as initial data
          setInitialData({
            title: metadata.title,
            description: metadata.description,
            thumbnailUrl: metadata.thumbnailUrl,
          });
        }
        
        // Load suggested hashtags, mentions, and locations
        const [hashtags, mentions, locations] = await Promise.all([
          getSuggestedHashtags(videoId),
          getSuggestedMentions(),
          getPopularLocations()
        ]);
        
        setSuggestedHashtags(hashtags);
        setSuggestedMentions(mentions);
        setPopularLocations(locations);
      } catch (err) {
        setError('Failed to load video data');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadVideoData();
  }, [videoId]);
  
  // Handle publish
  const handlePublish = async (data: Record<string, any>) => {
    if (!videoId) return;
    
    try {
      await publishVideo(videoId, data);
      
      // Navigate to video page or dashboard
      navigate(`/videos/${videoId}`);
    } catch (err) {
      throw err;
    }
  };
  
  // Handle save draft
  const handleSaveDraft = async (data: Record<string, any>) => {
    if (!videoId) throw new Error('Video ID is required');
    
    try {
      const draft = await saveDraft(videoId, data);
      
      // Update drafts list
      setDrafts(prevDrafts => {
        const existingIndex = prevDrafts.findIndex(d => d.id === draft.id);
        
        if (existingIndex >= 0) {
          // Update existing draft
          const updatedDrafts = [...prevDrafts];
          updatedDrafts[existingIndex] = draft;
          return updatedDrafts;
        } else {
          // Add new draft
          return [...prevDrafts, draft];
        }
      });
      
      return draft;
    } catch (err) {
      throw err;
    }
  };
  
  // Handle load draft
  const handleLoadDraft = async (draftId: string) => {
    try {
      const draftData = await loadDraft(draftId);
      return draftData;
    } catch (err) {
      throw err;
    }
  };
  
  // Handle delete draft
  const handleDeleteDraft = async (draftId: string) => {
    try {
      await deleteDraft(draftId);
      
      // Update drafts list
      setDrafts(prevDrafts => prevDrafts.filter(d => d.id !== draftId));
    } catch (err) {
      throw err;
    }
  };
  
  // Handle schedule publish
  const handleSchedulePublish = async (date: Date, data: Record<string, any>) => {
    if (!videoId) throw new Error('Video ID is required');
    
    try {
      await schedulePublish(videoId, date, data);
    } catch (err) {
      throw err;
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Loader2 size={48} className="animate-spin text-green-500 mb-4" />
        <p className="text-gray-600">Loading video data...</p>
      </div>
    );
  }
  
  if (error || !videoId) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <AlertCircle size={48} className="text-red-500 mb-4" />
        <h2 className="text-xl font-bold mb-2">Error</h2>
        <p className="text-gray-600 mb-4">{error || 'Video ID is required'}</p>
        <button
          type="button"
          onClick={() => navigate('/videos')}
          className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Go to Videos
        </button>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <ContentCreationForm
        videoId={videoId}
        initialData={initialData}
        onPublish={handlePublish}
        onSaveDraft={handleSaveDraft}
        onLoadDraft={handleLoadDraft}
        onDeleteDraft={handleDeleteDraft}
        onSchedulePublish={handleSchedulePublish}
        existingDrafts={drafts}
        suggestedHashtags={suggestedHashtags}
        suggestedMentions={suggestedMentions}
        popularLocations={popularLocations}
      />
    </div>
  );
}
