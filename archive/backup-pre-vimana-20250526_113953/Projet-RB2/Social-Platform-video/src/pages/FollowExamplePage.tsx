import { useState } from 'react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '../components/ui/tabs';
import { FollowButton } from '../components/social/follow/FollowButton';
import { FollowSuggestions } from '../components/social/follow/FollowSuggestions';
import { FollowList } from '../components/social/follow/FollowList';
import { useFollow } from '../hooks/useFollow';

export function FollowExamplePage() {
  const [activeTab, setActiveTab] = useState('suggestions');
  
  const {
    followers,
    following,
    suggestions,
    followerCount,
    followingCount,
    isLoadingFollowers,
    isLoadingFollowing,
    isLoadingSuggestions,
    errorFollowers,
    errorFollowing,
    errorSuggestions,
    follow,
    unfollow,
    toggleNotifications,
    dismissSuggestion,
    loadMoreFollowers,
    loadMoreFollowing,
    refreshFollowers,
    refreshFollowing,
    refreshSuggestions,
    searchFollowers,
    searchFollowing,
    hasMoreFollowers,
    hasMoreFollowing,
  } = useFollow();
  
  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Follow System Example</h1>
      
      {/* User profile example */}
      <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-start">
          {/* Avatar */}
          <div className="mr-6">
            <img
              src="https://i.pravatar.cc/150?u=example-user"
              alt="Example User"
              className="w-24 h-24 rounded-full object-cover"
            />
          </div>
          
          {/* User info */}
          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h2 className="text-xl font-bold flex items-center">
                  Example User
                  <span className="ml-1 text-blue-500">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                      <path fillRule="evenodd" d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                    </svg>
                  </span>
                </h2>
                <p className="text-gray-600">@exampleuser</p>
              </div>
              
              <FollowButton
                userId="example-user"
                userName="Example User"
                isFollowing={false}
                onFollow={follow}
                onUnfollow={unfollow}
                showNotificationToggle={true}
                onToggleNotifications={toggleNotifications}
              />
            </div>
            
            <p className="text-gray-700 mb-4">
              This is an example user profile to demonstrate the follow system.
              You can follow/unfollow this user and explore the followers, following, and suggestions tabs below.
            </p>
            
            <div className="flex space-x-6 text-sm">
              <div>
                <span className="font-semibold">{followerCount}</span> followers
              </div>
              <div>
                <span className="font-semibold">{followingCount}</span> following
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Tabs */}
      <Tabs
        defaultValue="suggestions"
        value={activeTab}
        onValueChange={setActiveTab}
        className="mb-8"
      >
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
          <TabsTrigger value="followers">Followers</TabsTrigger>
          <TabsTrigger value="following">Following</TabsTrigger>
        </TabsList>
        
        <TabsContent value="suggestions">
          <div className="space-y-6">
            {/* Card variant */}
            <div>
              <h3 className="text-lg font-medium mb-4">Card Variant</h3>
              <FollowSuggestions
                title="Suggested for you"
                subtitle="Based on your interests and activity"
                users={suggestions}
                isLoading={isLoadingSuggestions}
                error={errorSuggestions}
                onFollow={follow}
                onUnfollow={unfollow}
                onRefresh={refreshSuggestions}
                onDismiss={dismissSuggestion}
                onViewMore={() => setActiveTab('followers')}
                variant="card"
              />
            </div>
            
            {/* Inline variant */}
            <div>
              <h3 className="text-lg font-medium mb-4">Inline Variant</h3>
              <FollowSuggestions
                title="People you might know"
                users={suggestions}
                isLoading={isLoadingSuggestions}
                error={errorSuggestions}
                onFollow={follow}
                onUnfollow={unfollow}
                onRefresh={refreshSuggestions}
                onDismiss={dismissSuggestion}
                variant="inline"
              />
            </div>
            
            {/* Compact variant */}
            <div>
              <h3 className="text-lg font-medium mb-4">Compact Variant</h3>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <FollowSuggestions
                  title="Suggested accounts"
                  users={suggestions.slice(0, 3)}
                  isLoading={isLoadingSuggestions}
                  error={errorSuggestions}
                  onFollow={follow}
                  onUnfollow={unfollow}
                  onRefresh={refreshSuggestions}
                  onViewMore={() => setActiveTab('followers')}
                  maxUsers={3}
                  showDismiss={false}
                  variant="compact"
                />
              </div>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="followers">
          <FollowList
            title={`Followers (${followerCount})`}
            users={followers}
            isLoading={isLoadingFollowers}
            error={errorFollowers}
            onFollow={follow}
            onUnfollow={unfollow}
            onRefresh={refreshFollowers}
            onSearch={searchFollowers}
            onLoadMore={loadMoreFollowers}
            hasMore={hasMoreFollowers}
            type="followers"
            emptyStateMessage="This user doesn't have any followers yet"
          />
        </TabsContent>
        
        <TabsContent value="following">
          <FollowList
            title={`Following (${followingCount})`}
            users={following}
            isLoading={isLoadingFollowing}
            error={errorFollowing}
            onFollow={follow}
            onUnfollow={unfollow}
            onRefresh={refreshFollowing}
            onSearch={searchFollowing}
            onLoadMore={loadMoreFollowing}
            hasMore={hasMoreFollowing}
            type="following"
            emptyStateMessage="This user isn't following anyone yet"
          />
        </TabsContent>
      </Tabs>
      
      {/* Documentation */}
      <div className="mt-12 bg-gray-50 rounded-lg p-6 border border-gray-200">
        <h2 className="text-xl font-semibold mb-4">Follow System Documentation</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-lg">Features</h3>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Follow/unfollow users with a single click</li>
              <li>Toggle notifications for followed users</li>
              <li>View followers and following lists</li>
              <li>Search and filter followers/following</li>
              <li>Personalized user suggestions</li>
              <li>Dismiss unwanted suggestions</li>
              <li>View mutual followers</li>
              <li>Pagination with "Load more" functionality</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-lg">Components</h3>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li><code>FollowButton</code> - Button to follow/unfollow users</li>
              <li><code>FollowSuggestions</code> - Display suggested users to follow</li>
              <li><code>FollowList</code> - Display followers or following lists</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-lg">Hooks</h3>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li><code>useFollow</code> - Custom hook for managing follow state and API calls</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-lg">Integration</h3>
            <p className="mt-2">
              To integrate the follow system into your application:
            </p>
            <ol className="list-decimal pl-5 mt-2 space-y-1">
              <li>Import the necessary components (<code>FollowButton</code>, <code>FollowSuggestions</code>, <code>FollowList</code>)</li>
              <li>Use the <code>useFollow</code> hook to manage follow state</li>
              <li>Pass the hook's return values to the components</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
