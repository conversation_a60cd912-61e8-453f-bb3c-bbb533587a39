import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CollaborativeEditor } from '@/components/collaboration/CollaborativeEditor';
import { CollaborationRequests } from '@/components/collaboration/CollaborationRequests';
import { useCollaborationStore } from '@/store/collaboration';
import { 
  Users, 
  Plus, 
  Video, 
  FileText, 
  Radio, 
  ChevronLeft, 
  Loader2, 
  HelpCircle,
  Bell,
  Grid,
  List,
  Filter,
  Search
} from 'lucide-react';

export function CollaborationPage() {
  const { contentId, contentType } = useParams<{ contentId?: string; contentType?: 'videos' | 'posts' | 'livestreams' }>();
  const navigate = useNavigate();
  
  const {
    userContent,
    isLoading,
    error,
    fetchUserContent,
    createContent,
    isCreatingContent,
    setCreatingContent,
    resetCollaborationState,
  } = useCollaborationStore();
  
  const [activeTab, setActiveTab] = useState<'content' | 'requests'>('content');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [statusFilter, setStatusFilter] = useState<'all' | 'draft' | 'published' | 'archived'>('all');
  const [typeFilter, setTypeFilter] = useState<'all' | 'video' | 'post' | 'livestream'>('all');
  const [roleFilter, setRoleFilter] = useState<'all' | 'owner' | 'editor' | 'viewer'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  
  const [newContent, setNewContent] = useState({
    type: 'video' as 'video' | 'post' | 'livestream',
    title: '',
    description: '',
    visibility: 'private' as 'public' | 'private' | 'unlisted',
  });
  
  // Reset collaboration state when navigating away
  useEffect(() => {
    return () => {
      resetCollaborationState();
    };
  }, [resetCollaborationState]);
  
  // Fetch user content on mount
  useEffect(() => {
    if (!contentId) {
      fetchUserContent();
    }
  }, [contentId, fetchUserContent]);
  
  // Filter content based on filters and search query
  const filteredContent = userContent.filter((content) => {
    // Status filter
    if (statusFilter !== 'all' && content.status !== statusFilter) {
      return false;
    }
    
    // Type filter
    if (typeFilter !== 'all' && content.type !== typeFilter) {
      return false;
    }
    
    // Role filter
    if (roleFilter !== 'all') {
      if (roleFilter === 'owner' && content.owner.id !== 'current-user-id') { // Replace with actual user ID
        return false;
      } else if (roleFilter !== 'owner') {
        const userCollaborator = content.collaborators.find(
          (collaborator) => collaborator.userId === 'current-user-id' // Replace with actual user ID
        );
        if (!userCollaborator || userCollaborator.role !== roleFilter) {
          return false;
        }
      }
    }
    
    // Search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        content.title.toLowerCase().includes(query) ||
        content.description.toLowerCase().includes(query)
      );
    }
    
    return true;
  });
  
  // Handle creating new content
  const handleCreateContent = async () => {
    if (!newContent.title || !newContent.description) return;
    
    await createContent(
      newContent.type,
      newContent.title,
      newContent.description,
      undefined,
      newContent.visibility
    );
    
    // Reset form
    setNewContent({
      type: 'video',
      title: '',
      description: '',
      visibility: 'private',
    });
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };
  
  // Get content type icon
  const getContentTypeIcon = (type: 'video' | 'post' | 'livestream') => {
    switch (type) {
      case 'video':
        return <Video size={16} className="text-blue-500" />;
      case 'post':
        return <FileText size={16} className="text-green-500" />;
      case 'livestream':
        return <Radio size={16} className="text-red-500" />;
      default:
        return null;
    }
  };
  
  // If viewing a specific content
  if (contentId && contentType) {
    const normalizedContentType = contentType.endsWith('s')
      ? contentType.slice(0, -1) as 'video' | 'post' | 'livestream'
      : (contentType as 'video' | 'post' | 'livestream');
    
    return (
      <div className="min-h-screen bg-gray-50 pb-16">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white border-b">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/collaboration')}
                className="mr-4 text-gray-500 hover:text-gray-700 focus:outline-none"
              >
                <ChevronLeft size={24} />
              </button>
              <h1 className="text-xl font-bold flex items-center">
                <Users size={24} className="text-blue-500 mr-2" />
                Collaborative Content
              </h1>
            </div>
            
            <button
              onClick={() => setShowHelp(!showHelp)}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 focus:outline-none"
              title="Help"
            >
              <HelpCircle size={20} />
            </button>
          </div>
        </div>
        
        {/* Help Panel */}
        {showHelp && (
          <div className="container mx-auto px-4 py-4 bg-blue-50 border-b border-blue-200">
            <div className="flex items-start">
              <div className="flex-1">
                <h3 className="text-lg font-medium text-blue-800 mb-2">Collaboration Help</h3>
                <ul className="list-disc pl-5 text-sm text-blue-700 space-y-1">
                  <li>Edit content collaboratively with your team.</li>
                  <li>Add comments to discuss specific aspects of the content.</li>
                  <li>Manage collaborators and their permissions.</li>
                  <li>Track changes and activity in real-time.</li>
                  <li>Publish or share your content when it's ready.</li>
                </ul>
              </div>
              <button
                onClick={() => setShowHelp(false)}
                className="text-blue-500 hover:text-blue-700 focus:outline-none"
              >
                &times;
              </button>
            </div>
          </div>
        )}
        
        {/* Main Content */}
        <div className="container mx-auto px-4 py-6">
          <CollaborativeEditor
            contentId={contentId}
            contentType={normalizedContentType}
          />
        </div>
      </div>
    );
  }
  
  // Collaboration dashboard
  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <h1 className="text-xl font-bold flex items-center">
            <Users size={24} className="text-blue-500 mr-2" />
            Collaboration
          </h1>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => navigate('/notifications')}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 focus:outline-none"
              title="Notifications"
            >
              <Bell size={20} />
            </button>
            
            <button
              onClick={() => setShowHelp(!showHelp)}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 focus:outline-none"
              title="Help"
            >
              <HelpCircle size={20} />
            </button>
          </div>
        </div>
      </div>
      
      {/* Help Panel */}
      {showHelp && (
        <div className="container mx-auto px-4 py-4 bg-blue-50 border-b border-blue-200">
          <div className="flex items-start">
            <div className="flex-1">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Collaboration Help</h3>
              <ul className="list-disc pl-5 text-sm text-blue-700 space-y-1">
                <li>Create new collaborative content or manage existing ones.</li>
                <li>Invite others to collaborate on your content.</li>
                <li>Respond to collaboration requests from others.</li>
                <li>Filter content by status, type, or your role.</li>
                <li>Switch between grid and list views for better organization.</li>
              </ul>
            </div>
            <button
              onClick={() => setShowHelp(false)}
              className="text-blue-500 hover:text-blue-700 focus:outline-none"
            >
              &times;
            </button>
          </div>
        </div>
      )}
      
      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="content" value={activeTab} onValueChange={(value) => setActiveTab(value as 'content' | 'requests')}>
          <TabsList className="grid grid-cols-2 mb-8">
            <TabsTrigger value="content" className="flex items-center">
              <FileText size={16} className="mr-2" />
              My Content
            </TabsTrigger>
            <TabsTrigger value="requests" className="flex items-center">
              <Bell size={16} className="mr-2" />
              Requests
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="content">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={16} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Search content..."
                  />
                </div>
                
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
                  title="Filters"
                >
                  <Filter size={20} />
                </button>
                
                <div className="flex border border-gray-300 rounded-md overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 ${
                      viewMode === 'grid'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white text-gray-500 hover:bg-gray-100'
                    }`}
                    title="Grid view"
                  >
                    <Grid size={20} />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 ${
                      viewMode === 'list'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white text-gray-500 hover:bg-gray-100'
                    }`}
                    title="List view"
                  >
                    <List size={20} />
                  </button>
                </div>
              </div>
              
              <button
                onClick={() => setCreatingContent(!isCreatingContent)}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
              >
                {isCreatingContent ? (
                  <>
                    <ChevronLeft size={16} className="mr-1" />
                    Cancel
                  </>
                ) : (
                  <>
                    <Plus size={16} className="mr-1" />
                    Create New
                  </>
                )}
              </button>
            </div>
            
            {/* Filters */}
            {showFilters && (
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value as any)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="all">All Statuses</option>
                      <option value="draft">Drafts</option>
                      <option value="published">Published</option>
                      <option value="archived">Archived</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Content Type
                    </label>
                    <select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value as any)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="all">All Types</option>
                      <option value="video">Videos</option>
                      <option value="post">Posts</option>
                      <option value="livestream">Livestreams</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Your Role
                    </label>
                    <select
                      value={roleFilter}
                      onChange={(e) => setRoleFilter(e.target.value as any)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="all">All Roles</option>
                      <option value="owner">Owner</option>
                      <option value="editor">Editor</option>
                      <option value="viewer">Viewer</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
            
            {/* Create Content Form */}
            {isCreatingContent && (
              <div className="bg-white rounded-lg shadow-md border border-gray-200 p-4 mb-6">
                <h2 className="text-lg font-semibold mb-4">Create New Collaborative Content</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Content Type
                    </label>
                    <div className="flex space-x-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          checked={newContent.type === 'video'}
                          onChange={() => setNewContent({ ...newContent, type: 'video' })}
                          className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 flex items-center">
                          <Video size={16} className="text-blue-500 mr-1" />
                          Video
                        </span>
                      </label>
                      
                      <label className="flex items-center">
                        <input
                          type="radio"
                          checked={newContent.type === 'post'}
                          onChange={() => setNewContent({ ...newContent, type: 'post' })}
                          className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 flex items-center">
                          <FileText size={16} className="text-green-500 mr-1" />
                          Post
                        </span>
                      </label>
                      
                      <label className="flex items-center">
                        <input
                          type="radio"
                          checked={newContent.type === 'livestream'}
                          onChange={() => setNewContent({ ...newContent, type: 'livestream' })}
                          className="h-4 w-4 text-blue-500 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 flex items-center">
                          <Radio size={16} className="text-red-500 mr-1" />
                          Livestream
                        </span>
                      </label>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Title
                    </label>
                    <input
                      type="text"
                      value={newContent.title}
                      onChange={(e) => setNewContent({ ...newContent, title: e.target.value })}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter a title for your content"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={newContent.description}
                      onChange={(e) => setNewContent({ ...newContent, description: e.target.value })}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={4}
                      placeholder="Describe your content"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Visibility
                    </label>
                    <select
                      value={newContent.visibility}
                      onChange={(e) => setNewContent({ ...newContent, visibility: e.target.value as any })}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="private">Private (Only you and collaborators)</option>
                      <option value="unlisted">Unlisted (Anyone with the link)</option>
                      <option value="public">Public (Anyone can find and view)</option>
                    </select>
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      onClick={() => setCreatingContent(false)}
                      className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md mr-2 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                    >
                      Cancel
                    </button>
                    
                    <button
                      onClick={handleCreateContent}
                      disabled={!newContent.title || !newContent.description}
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                    >
                      Create Content
                    </button>
                  </div>
                </div>
              </div>
            )}
            
            {/* Content List */}
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 size={32} className="animate-spin text-blue-500" />
              </div>
            ) : error ? (
              <div className="p-8 text-center text-red-500">
                <p>Error: {error}</p>
                <button
                  onClick={() => fetchUserContent()}
                  className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Try Again
                </button>
              </div>
            ) : filteredContent.length === 0 ? (
              <div className="text-center py-16 bg-gray-50 rounded-lg border border-gray-200">
                <h3 className="text-lg font-medium text-gray-700 mb-2">No content found</h3>
                <p className="text-gray-500 mb-6">
                  {searchQuery
                    ? `No results matching "${searchQuery}"`
                    : "You don't have any collaborative content yet."}
                </p>
                {!isCreatingContent && (
                  <button
                    onClick={() => setCreatingContent(true)}
                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Create Your First Content
                  </button>
                )}
              </div>
            ) : (
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
                {filteredContent.map((content) => (
                  viewMode === 'grid' ? (
                    <div
                      key={content.id}
                      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => navigate(`/collaboration/${content.type}s/${content.id}`)}
                    >
                      <div className="h-40 bg-gray-200 relative">
                        {content.thumbnailUrl ? (
                          <img
                            src={content.thumbnailUrl}
                            alt={content.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-gray-100">
                            {getContentTypeIcon(content.type)}
                            <span className="text-gray-400 text-sm ml-1 capitalize">{content.type}</span>
                          </div>
                        )}
                        <div className="absolute top-2 right-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            content.status === 'draft'
                              ? 'bg-yellow-100 text-yellow-800'
                              : content.status === 'published'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {content.status}
                          </span>
                        </div>
                      </div>
                      
                      <div className="p-4">
                        <h3 className="text-lg font-medium mb-1 line-clamp-1">{content.title}</h3>
                        <p className="text-sm text-gray-500 mb-2 line-clamp-2">{content.description}</p>
                        
                        <div className="flex justify-between items-center text-xs text-gray-500">
                          <div className="flex items-center">
                            <Users size={14} className="mr-1" />
                            {content.collaborators.length + 1} {/* +1 for owner */}
                          </div>
                          <div>{formatDate(content.updatedAt)}</div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div
                      key={content.id}
                      className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => navigate(`/collaboration/${content.type}s/${content.id}`)}
                    >
                      <div className="flex items-start">
                        <div className="h-16 w-16 bg-gray-200 rounded-md overflow-hidden flex-shrink-0 mr-4">
                          {content.thumbnailUrl ? (
                            <img
                              src={content.thumbnailUrl}
                              alt={content.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gray-100">
                              {getContentTypeIcon(content.type)}
                            </div>
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start">
                            <h3 className="text-lg font-medium mb-1">{content.title}</h3>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              content.status === 'draft'
                                ? 'bg-yellow-100 text-yellow-800'
                                : content.status === 'published'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {content.status}
                            </span>
                          </div>
                          
                          <p className="text-sm text-gray-500 mb-2 line-clamp-1">{content.description}</p>
                          
                          <div className="flex justify-between items-center text-xs text-gray-500">
                            <div className="flex items-center space-x-2">
                              <div className="flex items-center">
                                {getContentTypeIcon(content.type)}
                                <span className="ml-1 capitalize">{content.type}</span>
                              </div>
                              <div className="flex items-center">
                                <Users size={14} className="mr-1" />
                                {content.collaborators.length + 1} {/* +1 for owner */}
                              </div>
                            </div>
                            <div>{formatDate(content.updatedAt)}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                ))}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="requests">
            <CollaborationRequests />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
