import React from 'react';
import { Link } from 'react-router-dom';
import { FaPlay, FaUsers, FaVideo, FaChalkboardTeacher } from 'react-icons/fa';

const VideoLandingPage: React.FC = () => {
  return (;
    <div className = "min-h-screen">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-purple-600 to-blue-600 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
                  <span className="block">Partagez votre expérience</span>
                  <span className="block text-blue-200">en vidéo</span>
                </h1>
                <p className="mt-3 text-base text-blue-100 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                  Rejoignez notre communauté de passionnés et partagez vos moments de bien-être, vos conseils et vos expériences à travers des vidéos inspirantes.
                </p>
                <div className = "mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <Link
                      to="/videos/explore"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-purple-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"
                    >
                      Explorer les vidéos;
                    </Link>
                  </div>
                  <div className="mt-3 sm:mt-0 sm:ml-3">
                    <Link
                      to="/register"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-500 hover:bg-purple-600 md:py-4 md:text-lg md:px-10"
                    >
                      Commencer à partager;
                    </Link>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <img
            className="h-56 w-full object-cover sm:h-72 md:h-96 lg:w-full lg:h-full"
            src="/images/video-community.jpg"
            alt="Communauté vidéo"
          />
        </div>
      </div>

      {/* Features Section */}
      <div className = "py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-purple-600 font-semibold tracking-wide uppercase">
              Fonctionnalités;
            </h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Une plateforme complète pour vos vidéos;
            </p>
          </div>

          <div className="mt-10">
            <div className="grid grid-cols-1 gap-10 sm:grid-cols-2 lg:grid-cols-3">
              {/* Live Streaming */}
              <div className="relative">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white mb-4">
                  <FaPlay className="h-6 w-6" />
                </div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Live Streaming;
                </h3>
                <p className="mt-2 text-base text-gray-500">
                  Diffusez en direct vos sessions de méditation, cours de yoga ou ateliers de bien-être.
                </p>
              </div>

              {/* Community Features */}
              <div className="relative">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white mb-4">
                  <FaUsers className="h-6 w-6" />
                </div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Communauté Active;
                </h3>
                <p className="mt-2 text-base text-gray-500">
                  Interagissez avec d'autres passionnés, commentez et partagez vos expériences.
                </p>
              </div>

              {/* Video Creation */}
              <div className="relative">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white mb-4">
                  <FaVideo className="h-6 w-6" />
                </div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Création de Contenu;
                </h3>
                <p className="mt-2 text-base text-gray-500">
                  Des outils simples pour créer, éditer et partager vos vidéos de qualité.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Videos Section */}
      <div className = "bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900">
              Vidéos à la une;
            </h2>
            <p className="mt-4 text-lg text-gray-500">
              Découvrez les contenus les plus populaires de notre communauté
            </p>
          </div>

          <div className="mt-10 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {/* Featured Video Cards would go here */}
            {/* This is just a placeholder structure */}
            {[1, 2, 3].map((index) => (
              <div
                key = {index
}
                className = "bg-white rounded-lg shadow-sm overflow-hidden"
              >
                <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                  {/* Video thumbnail would go here */}
                </div>
                <div className = "p-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Titre de la vidéo {index
}
                  </h3>
                  <p className = "mt-2 text-sm text-gray-500">
                    Description courte de la vidéo...
                  </p>
                </div>
              </div>
            ))
}
          </div>

          <div className = "mt-10 text-center">
            <Link
              to="/videos/explore"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
            >
              Voir plus de vidéos;
            </Link>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className = "bg-purple-700">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
          <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
            <span className="block">Prêt à partager vos vidéos ?</span>
            <span className="block text-purple-200">
              Rejoignez notre communauté dès aujourd'hui.
            </span>
          </h2>
          <div className="mt-8 flex lg:mt-0 lg:flex-shrink-0">
            <div className="inline-flex rounded-md shadow">
              <Link
                to="/register"
                className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-purple-600 bg-white hover:bg-purple-50"
              >
                Commencer;
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default VideoLandingPage;