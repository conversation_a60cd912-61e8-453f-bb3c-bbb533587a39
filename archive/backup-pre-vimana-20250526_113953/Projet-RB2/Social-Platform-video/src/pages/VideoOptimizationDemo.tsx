import { useState, useEffect } from 'react';
import { AdaptiveVideoPlayer } from '../components/video/AdaptiveVideoPlayer';
import { ThumbnailGenerator } from '../components/video/ThumbnailGenerator';
import { 
  VideoQuality, 
  VideoCodec, 
  VideoFormat, 
  VideoManifest 
} from '../services/video-optimization/adaptive-streaming';
import {
  createCompressionJob,
  getCompressionJob,
  CompressionQuality,
  CompressionSpeed,
  CompressionJobStatus
} from '../services/video-optimization/video-compression';
import { 
  getBandwidthEstimate, 
  getBandwidthStats, 
  testBandwidth 
} from '../services/video-optimization/bandwidth-detection';
import { Button } from '../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Slider } from '../components/ui/slider';
import { Progress } from '../components/ui/progress';
import { Loader2, Zap, BarChart, Image, RefreshCw } from 'lucide-react';

// Mock video manifest for demo
const mockVideoManifest: VideoManifest = {
  id: 'demo-video-123',
  format: VideoFormat.HLS,
  duration: 180, // 3 minutes
  defaultQuality: VideoQuality.AUTO,
  streams: [
    {
      url: 'https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      quality: VideoQuality.LOW,
      bitrate: 800,
      resolution: { width: 640, height: 360 },
      codec: VideoCodec.H264,
      size: 5000000, // 5MB
    },
    {
      url: 'https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      quality: VideoQuality.MEDIUM,
      bitrate: 2500,
      resolution: { width: 1280, height: 720 },
      codec: VideoCodec.H264,
      size: 15000000, // 15MB
    },
    {
      url: 'https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      quality: VideoQuality.HIGH,
      bitrate: 5000,
      resolution: { width: 1920, height: 1080 },
      codec: VideoCodec.H264,
      size: 30000000, // 30MB
    },
  ],
  thumbnails: [
    {
      url: 'https://storage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg',
      width: 1280,
      height: 720,
      timestamp: 0,
    },
  ],
};

export default function VideoOptimizationDemo() {
  const [activeTab, setActiveTab] = useState<'adaptive' | 'compression' | 'thumbnails'>('adaptive');
  const [selectedQuality, setSelectedQuality] = useState<VideoQuality>(VideoQuality.AUTO);
  const [bandwidthStats, setBandwidthStats] = useState({
    current: 0,
    average: 0,
    min: 0,
    max: 0,
  });
  const [isTesting, setIsTesting] = useState(false);
  
  // Compression state
  const [compressionOptions, setCompressionOptions] = useState({
    codec: VideoCodec.H264,
    quality: CompressionQuality.MEDIUM,
    speed: CompressionSpeed.MEDIUM,
    twoPass: false,
  });
  const [compressionJobId, setCompressionJobId] = useState<string | null>(null);
  const [compressionStatus, setCompressionStatus] = useState<CompressionJobStatus | null>(null);
  const [compressionProgress, setCompressionProgress] = useState(0);
  const [compressionError, setCompressionError] = useState<string | null>(null);
  const [compressionResult, setCompressionResult] = useState<{
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    outputUrl: string | null;
  } | null>(null);
  
  // Thumbnail state
  const [selectedThumbnail, setSelectedThumbnail] = useState<string | null>(null);
  
  // Update bandwidth stats periodically
  useEffect(() => {
    const intervalId = setInterval(() => {
      const stats = getBandwidthStats();
      setBandwidthStats({
        current: stats.current,
        average: stats.average,
        min: stats.min,
        max: stats.max,
      });
    }, 2000);
    
    return () => {
      clearInterval(intervalId);
    };
  }, []);
  
  // Poll for compression job status
  useEffect(() => {
    if (!compressionJobId || compressionStatus === CompressionJobStatus.COMPLETED || compressionStatus === CompressionJobStatus.FAILED) {
      return;
    }
    
    const intervalId = setInterval(() => {
      const job = getCompressionJob(compressionJobId);
      
      if (!job) {
        clearInterval(intervalId);
        setCompressionError('Compression job not found');
        return;
      }
      
      setCompressionStatus(job.status);
      setCompressionProgress(job.progress);
      
      if (job.status === CompressionJobStatus.COMPLETED) {
        if (job.size && job.outputUrl) {
          setCompressionResult({
            originalSize: job.size.original,
            compressedSize: job.size.compressed,
            compressionRatio: job.size.compressed / job.size.original,
            outputUrl: job.outputUrl,
          });
        }
      } else if (job.status === CompressionJobStatus.FAILED) {
        setCompressionError(job.error || 'Compression failed');
      }
    }, 500);
    
    return () => {
      clearInterval(intervalId);
    };
  }, [compressionJobId, compressionStatus]);
  
  // Handle quality change
  const handleQualityChange = (quality: VideoQuality) => {
    setSelectedQuality(quality);
  };
  
  // Test bandwidth
  const handleTestBandwidth = async () => {
    setIsTesting(true);
    
    try {
      await testBandwidth();
      const stats = getBandwidthStats();
      setBandwidthStats({
        current: stats.current,
        average: stats.average,
        min: stats.min,
        max: stats.max,
      });
    } catch (error) {
      console.error('Bandwidth test failed:', error);
    } finally {
      setIsTesting(false);
    }
  };
  
  // Start compression
  const handleStartCompression = () => {
    setCompressionError(null);
    setCompressionResult(null);
    
    try {
      const job = createCompressionJob('demo-video-123', mockVideoManifest.streams[0].url, {
        codec: compressionOptions.codec,
        quality: compressionOptions.quality,
        speed: compressionOptions.speed,
        twoPass: compressionOptions.twoPass,
      });
      
      setCompressionJobId(job.id);
      setCompressionStatus(job.status);
      setCompressionProgress(job.progress);
    } catch (err) {
      setCompressionError(err instanceof Error ? err.message : 'Failed to start compression');
    }
  };
  
  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else if (bytes < 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    } else {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Video Optimization Demo</h1>
      
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'adaptive' | 'compression' | 'thumbnails')}>
        <TabsList className="mb-6">
          <TabsTrigger value="adaptive">Adaptive Streaming</TabsTrigger>
          <TabsTrigger value="compression">Video Compression</TabsTrigger>
          <TabsTrigger value="thumbnails">Thumbnail Generation</TabsTrigger>
        </TabsList>
        
        <TabsContent value="adaptive">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Adaptive Video Player</CardTitle>
                  <CardDescription>
                    This player automatically adjusts video quality based on your network conditions.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <AdaptiveVideoPlayer
                    manifest={mockVideoManifest}
                    onQualityChange={handleQualityChange}
                    className="aspect-video rounded-md overflow-hidden"
                  />
                </CardContent>
              </Card>
            </div>
            
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Network Stats</CardTitle>
                  <CardDescription>
                    Current network bandwidth and video quality
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Current Quality</Label>
                    <div className="text-2xl font-bold">{selectedQuality}</div>
                  </div>
                  
                  <div>
                    <Label>Current Bandwidth</Label>
                    <div className="text-2xl font-bold">{bandwidthStats.current.toFixed(1)} Kbps</div>
                  </div>
                  
                  <div>
                    <Label>Average Bandwidth</Label>
                    <div>{bandwidthStats.average.toFixed(1)} Kbps</div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Min</Label>
                      <div>{bandwidthStats.min.toFixed(1)} Kbps</div>
                    </div>
                    <div>
                      <Label>Max</Label>
                      <div>{bandwidthStats.max.toFixed(1)} Kbps</div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={handleTestBandwidth} disabled={isTesting} className="w-full">
                    {isTesting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <Zap className="mr-2 h-4 w-4" />
                        Test Bandwidth
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="compression">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Video Compression</CardTitle>
                  <CardDescription>
                    Compress your video to reduce file size while maintaining quality
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="codec">Codec</Label>
                      <Select
                        value={compressionOptions.codec}
                        onValueChange={(value) => 
                          setCompressionOptions({ ...compressionOptions, codec: value as VideoCodec })
                        }
                      >
                        <SelectTrigger id="codec">
                          <SelectValue placeholder="Select codec" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={VideoCodec.H264}>H.264 (AVC)</SelectItem>
                          <SelectItem value={VideoCodec.H265}>H.265 (HEVC)</SelectItem>
                          <SelectItem value={VideoCodec.VP9}>VP9</SelectItem>
                          <SelectItem value={VideoCodec.AV1}>AV1</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="quality">Quality</Label>
                      <Select
                        value={compressionOptions.quality}
                        onValueChange={(value) => 
                          setCompressionOptions({ ...compressionOptions, quality: value as CompressionQuality })
                        }
                      >
                        <SelectTrigger id="quality">
                          <SelectValue placeholder="Select quality" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={CompressionQuality.LOWEST}>Lowest</SelectItem>
                          <SelectItem value={CompressionQuality.LOW}>Low</SelectItem>
                          <SelectItem value={CompressionQuality.MEDIUM}>Medium</SelectItem>
                          <SelectItem value={CompressionQuality.HIGH}>High</SelectItem>
                          <SelectItem value={CompressionQuality.HIGHEST}>Highest</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="speed">Encoding Speed</Label>
                      <Select
                        value={compressionOptions.speed}
                        onValueChange={(value) => 
                          setCompressionOptions({ ...compressionOptions, speed: value as CompressionSpeed })
                        }
                      >
                        <SelectTrigger id="speed">
                          <SelectValue placeholder="Select speed" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={CompressionSpeed.FASTEST}>Fastest</SelectItem>
                          <SelectItem value={CompressionSpeed.FAST}>Fast</SelectItem>
                          <SelectItem value={CompressionSpeed.MEDIUM}>Medium</SelectItem>
                          <SelectItem value={CompressionSpeed.SLOW}>Slow</SelectItem>
                          <SelectItem value={CompressionSpeed.SLOWEST}>Slowest</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="two-pass"
                        checked={compressionOptions.twoPass}
                        onChange={(e) => 
                          setCompressionOptions({ ...compressionOptions, twoPass: e.target.checked })
                        }
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor="two-pass">Two-pass encoding (better quality)</Label>
                    </div>
                  </div>
                  
                  {compressionStatus === CompressionJobStatus.PROCESSING && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Compressing...</span>
                        <span>{compressionProgress}%</span>
                      </div>
                      <Progress value={compressionProgress} />
                    </div>
                  )}
                  
                  {compressionError && (
                    <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm">
                      {compressionError}
                    </div>
                  )}
                  
                  {compressionResult && (
                    <div className="p-4 border rounded-md bg-green-50">
                      <h4 className="font-medium text-green-800 mb-2">Compression Complete</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Original Size</p>
                          <p className="font-medium">{formatFileSize(compressionResult.originalSize)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Compressed Size</p>
                          <p className="font-medium">{formatFileSize(compressionResult.compressedSize)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Compression Ratio</p>
                          <p className="font-medium">{(compressionResult.compressionRatio * 100).toFixed(1)}%</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Space Saved</p>
                          <p className="font-medium">
                            {formatFileSize(compressionResult.originalSize - compressionResult.compressedSize)}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
                <CardFooter>
                  <Button 
                    onClick={handleStartCompression}
                    disabled={compressionStatus === CompressionJobStatus.PROCESSING}
                    className="w-full"
                  >
                    {compressionStatus === CompressionJobStatus.PROCESSING ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Compressing...
                      </>
                    ) : compressionResult ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Compress Again
                      </>
                    ) : (
                      <>
                        <BarChart className="mr-2 h-4 w-4" />
                        Start Compression
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </div>
            
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Compression Info</CardTitle>
                  <CardDescription>
                    Learn about video compression options
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 text-sm">
                  <div>
                    <h4 className="font-medium">Codecs</h4>
                    <ul className="list-disc pl-5 space-y-1 mt-1">
                      <li><strong>H.264 (AVC)</strong>: Widely supported, good balance of quality and compatibility</li>
                      <li><strong>H.265 (HEVC)</strong>: Better compression than H.264, but less compatible</li>
                      <li><strong>VP9</strong>: Open source alternative to H.265, used by YouTube</li>
                      <li><strong>AV1</strong>: Newest codec with best compression, but slower encoding</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium">Quality Settings</h4>
                    <ul className="list-disc pl-5 space-y-1 mt-1">
                      <li><strong>Lowest/Low</strong>: Smallest file size, visible quality loss</li>
                      <li><strong>Medium</strong>: Good balance of quality and file size</li>
                      <li><strong>High/Highest</strong>: Best quality, larger file size</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium">Encoding Speed</h4>
                    <ul className="list-disc pl-5 space-y-1 mt-1">
                      <li><strong>Faster</strong>: Quicker encoding, less efficient compression</li>
                      <li><strong>Slower</strong>: Better compression, but takes longer to encode</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium">Two-Pass Encoding</h4>
                    <p className="mt-1">
                      Analyzes the video in the first pass to optimize compression in the second pass.
                      Results in better quality at the same file size, but takes twice as long.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="thumbnails">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <ThumbnailGenerator
                videoId="demo-video-123"
                videoUrl={mockVideoManifest.streams[0].url}
                onThumbnailSelect={setSelectedThumbnail}
              />
            </div>
            
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Selected Thumbnail</CardTitle>
                  <CardDescription>
                    Preview of your selected thumbnail
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {selectedThumbnail ? (
                    <img
                      src={selectedThumbnail}
                      alt="Selected thumbnail"
                      className="w-full h-auto rounded-md"
                    />
                  ) : (
                    <div className="aspect-video bg-gray-100 rounded-md flex items-center justify-center">
                      <p className="text-gray-500">No thumbnail selected</p>
                    </div>
                  )}
                </CardContent>
                <CardFooter>
                  <p className="text-sm text-gray-500">
                    Select a thumbnail from the generator or upload your own custom image.
                  </p>
                </CardFooter>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
