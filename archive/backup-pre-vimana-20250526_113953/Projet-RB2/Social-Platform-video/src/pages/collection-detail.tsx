import { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useCollectionsStore } from '../store/collections';
import { CollectionDetail } from '../components/collections/CollectionDetail';
import { Button } from '../components/ui/button';
import { ArrowLeft, Loader2 } from 'lucide-react';

export default function CollectionDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { fetchCollection, clearCurrentCollection, currentCollection, isLoading, error } = useCollectionsStore();
  
  // Fetch collection when the component mounts
  useEffect(() => {
    if (id) {
      fetchCollection(id);
    }
    
    // Clear current collection when the component unmounts
    return () => {
      clearCurrentCollection();
    };
  }, [id, fetchCollection, clearCurrentCollection]);
  
  // Handle back button
  const handleBack = () => {
    navigate('/collections');
  };
  
  if (isLoading && !currentCollection) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Button
          variant="ghost"
          size="sm"
          className="mb-4"
          onClick={handleBack}
        >
          <ArrowLeft size={16} className="mr-2" />
          Back to Collections
        </Button>
        
        <div className="flex justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    );
  }
  
  if (error || !id) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Button
          variant="ghost"
          size="sm"
          className="mb-4"
          onClick={handleBack}
        >
          <ArrowLeft size={16} className="mr-2" />
          Back to Collections
        </Button>
        
        <div className="text-center py-12 bg-red-50 rounded-lg">
          <p className="text-red-500">
            {error || 'Collection ID is missing. Please go back and try again.'}
          </p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={handleBack}
          >
            Back to Collections
          </Button>
        </div>
      </div>
    );
  }
  
  // Determine if the current user is the owner of the collection
  // In a real app, you would compare the collection's userId with the current user's ID
  const isOwner = true; // For demo purposes
  
  return <CollectionDetail collectionId={id} isOwner={isOwner} />;
}
