import { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { StoryViewer } from '../components/stories/StoryViewer';
import { useStoriesStore } from '../store/stories';

export function StoryPage() {
  const { storyId, itemId } = useParams<{ storyId: string; itemId?: string }>();
  const navigate = useNavigate();
  const { 
    storiesFeed, 
    isLoading, 
    error, 
    fetchStoriesFeed, 
    openStory, 
    closeStory 
  } = useStoriesStore();
  
  useEffect(() => {
    // Fetch stories if not already loaded
    if (storiesFeed.length === 0 && !isLoading) {
      fetchStoriesFeed();
    }
  }, [fetchStoriesFeed, storiesFeed.length, isLoading]);
  
  useEffect(() => {
    // Open the story when the component mounts and stories are loaded
    if (storyId && storiesFeed.length > 0) {
      const story = storiesFeed.find((s) => s.id === storyId);
      
      if (story) {
        // If itemId is provided, find its index
        if (itemId) {
          const itemIndex = story.items.findIndex((item) => item.id === itemId);
          if (itemIndex !== -1) {
            openStory(storyId, itemIndex);
          } else {
            openStory(storyId);
          }
        } else {
          openStory(storyId);
        }
      } else {
        // Story not found, navigate back to home
        navigate('/');
      }
    }
  }, [storyId, itemId, storiesFeed, openStory, navigate]);
  
  const handleClose = () => {
    closeStory();
    navigate('/');
  };
  
  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="fixed inset-0 bg-black flex flex-col items-center justify-center text-white">
        <p className="text-red-500 mb-4">Error: {error}</p>
        <button
          onClick={() => fetchStoriesFeed()}
          className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  return <StoryViewer onClose={handleClose} />;
}
