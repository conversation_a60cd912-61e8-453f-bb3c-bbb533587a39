import { useState } from 'react';
import { Bar<PERSON>hart2, ChevronLeft, HelpCircle } from 'lucide-react';
import { AnalyticsDashboard } from '../components/analytics/AnalyticsDashboard';
import { useAnalyticsStore } from '../store/analytics';

export function AnalyticsPage() {
  const { selectedContentId, selectedContentType, contentItemAnalytics } = useAnalyticsStore();
  const [showHelp, setShowHelp] = useState(false);
  
  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center">
            {selectedContentId ? (
              <button
                onClick={() => window.history.back()}
                className="mr-4 text-gray-500 hover:text-gray-700 focus:outline-none"
              >
                <ChevronLeft size={24} />
              </button>
            ) : null}
            <h1 className="text-xl font-bold flex items-center">
              <BarChart2 size={24} className="text-green-500 mr-2" />
              {selectedContentId
                ? `Content Analytics: ${selectedContentType?.charAt(0).toUpperCase()}${selectedContentType?.slice(1)}`
                : 'Analytics Dashboard'}
            </h1>
          </div>
          
          <button
            onClick={() => setShowHelp(!showHelp)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 focus:outline-none"
            title="Help"
          >
            <HelpCircle size={20} />
          </button>
        </div>
      </div>
      
      {/* Help Panel */}
      {showHelp && (
        <div className="container mx-auto px-4 py-4 bg-blue-50 border-b border-blue-200">
          <div className="flex items-start">
            <div className="flex-1">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Analytics Dashboard Help</h3>
              <ul className="list-disc pl-5 text-sm text-blue-700 space-y-1">
                <li>Use the time range selector to view data for different periods.</li>
                <li>Change the granularity to see data by hour, day, week, or month.</li>
                <li>Filter content performance by type (videos, posts, stories).</li>
                <li>Sort content by views, likes, comments, shares, or completion rate.</li>
                <li>Click on any content item to view detailed analytics.</li>
                <li>Export data in CSV, JSON, or PDF format for further analysis.</li>
              </ul>
            </div>
            <button
              onClick={() => setShowHelp(false)}
              className="text-blue-500 hover:text-blue-700 focus:outline-none"
            >
              &times;
            </button>
          </div>
        </div>
      )}
      
      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        {selectedContentId && contentItemAnalytics ? (
          <div className="space-y-6">
            {/* Content Item Analytics */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold mb-4">Content Details</h2>
              
              {/* Content details would go here */}
              <div className="text-center py-8 text-gray-500">
                <p>Detailed content analytics coming soon!</p>
                <p className="text-sm mt-2">This feature is under development.</p>
              </div>
            </div>
          </div>
        ) : (
          <AnalyticsDashboard />
        )}
      </div>
    </div>
  );
}
