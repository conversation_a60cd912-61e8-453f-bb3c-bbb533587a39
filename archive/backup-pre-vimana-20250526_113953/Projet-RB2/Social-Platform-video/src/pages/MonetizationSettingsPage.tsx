import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { SubscriptionPlans } from '@/components/monetization/SubscriptionPlans';
import { PaymentMethods } from '@/components/monetization/PaymentMethods';
import { CreatorDashboard } from '@/components/monetization/CreatorDashboard';
import { DollarSign, CreditCard, Users, BarChart2, HelpCircle } from 'lucide-react';

export function MonetizationSettingsPage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showHelp, setShowHelp] = useState(false);
  
  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <h1 className="text-xl font-bold flex items-center">
            <DollarSign size={24} className="text-green-500 mr-2" />
            Creator Monetization
          </h1>
          
          <button
            onClick={() => setShowHelp(!showHelp)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 focus:outline-none"
            title="Help"
          >
            <HelpCircle size={20} />
          </button>
        </div>
      </div>
      
      {/* Help Panel */}
      {showHelp && (
        <div className="container mx-auto px-4 py-4 bg-blue-50 border-b border-blue-200">
          <div className="flex items-start">
            <div className="flex-1">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Monetization Help</h3>
              <ul className="list-disc pl-5 text-sm text-blue-700 space-y-1">
                <li>Create subscription plans to offer premium content to your followers.</li>
                <li>Manage your payment methods for receiving tips and subscription payments.</li>
                <li>View your earnings and request payouts from your dashboard.</li>
                <li>Track subscriber growth and engagement with your premium content.</li>
                <li>Set up different tiers with unique benefits to maximize your earnings.</li>
              </ul>
            </div>
            <button
              onClick={() => setShowHelp(false)}
              className="text-blue-500 hover:text-blue-700 focus:outline-none"
            >
              &times;
            </button>
          </div>
        </div>
      )}
      
      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="dashboard" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-8">
            <TabsTrigger value="dashboard" className="flex items-center">
              <BarChart2 size={16} className="mr-2" />
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="subscriptions" className="flex items-center">
              <Users size={16} className="mr-2" />
              Subscription Plans
            </TabsTrigger>
            <TabsTrigger value="payments" className="flex items-center">
              <CreditCard size={16} className="mr-2" />
              Payment Methods
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="dashboard">
            <CreatorDashboard />
          </TabsContent>
          
          <TabsContent value="subscriptions">
            <SubscriptionPlans isCreator={true} />
          </TabsContent>
          
          <TabsContent value="payments">
            <PaymentMethods />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
