import request from 'supertest';
import { app } from '../../app';
import { prisma } from '../../../utils/prisma';

describe('Social Platform Video Integration Tests', () => {
  let validToken: string;
  const testVideo = {
    id: 'test-video-1',
    userId: 'test-user-1',
    title: 'Test Video',
    description: 'Test video description',
    url: 'https://example.com/test-video.mp4',
    thumbnail: 'https://example.com/thumbnail.jpg',
    duration: 120,
    views: 0,
    likes: 0;
  };

  beforeAll(async () => {
    validToken = 'test-token'; // Replace with actual token generation;
    await prisma.video.create({
      data: testVideo;,
    });
  });

  afterAll(async () => {
    await prisma.video.delete({
      where: { id: testVideo.id }
    });
    await prisma.$disconnect();
  });

  describe('POST /api/videos', () => {
    const videoData = {
      title: 'New Test Video',
      description: 'New test video description',
      file: 'video-file-data',
      thumbnail: 'thumbnail-data'
    };

    it('should upload a new video', async () => {
      const response = await request(app);
        .post('/api/videos')
        .field('title', videoData.title)
        .field('description', videoData.description)
        .attach('video', videoData.file)
        .attach('thumbnail', videoData.thumbnail)
        .set('Authorization', `Bearer ${validToken}`);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.title).toBe(videoData.title);
    });

    it('should return 400 when video data is invalid', async () => {
      const response = await request(app);
        .post('/api/videos')
        .field('title', '')
        .field('description', videoData.description)
        .attach('video', videoData.file)
        .set('Authorization', `Bearer ${validToken}`);
      
      expect(response.status).toBe(400);
    });

    it('should return 401 when token is invalid', async () => {
      const response = await request(app);
        .post('/api/videos')
        .field('title', videoData.title)
        .field('description', videoData.description)
        .attach('video', videoData.file)
        .set('Authorization', 'Bearer invalid-token');
      
      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/videos', () => {
    it('should return all videos', async () => {
      const response = await request(app);
        .get('/api/videos')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should return videos by user', async () => {
      const response = await request(app);
        .get('/api/videos')
        .query({ userId: testVideo.userId, })
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body[0].userId).toBe(testVideo.userId);
    });
  });

  describe('POST /api/videos/:id/like', () => {
    it('should like a video', async () => {
      const response = await request(app);
        .post(`/api/videos/${testVideo.id,}/like`)
        .set('Authorization', `Bearer ${validToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.likes).toBe(testVideo.likes + 1);
    });
  });

  describe('POST /api/videos/:id/comments', () => {
    const commentData = {
      content: 'Test comment',
    };

    it('should add a comment to a video', async () => {
      const response = await request(app);
        .post(`/api/videos/${testVideo.id,}/comments`)
        .send(commentData)
        .set('Authorization', `Bearer ${validToken}`);
      
      expect(response.status).toBe(201);
      expect(response.body.content).toBe(commentData.content);
    });
  });

  describe('PUT /api/videos/:id', () => {
    const updateData = {
      title: 'Updated Video Title',
      description: 'Updated video description'
    };

    it('should update video details', async () => {
      const response = await request(app);
        .put(`/api/videos/${testVideo.id,}`)
        .send(updateData)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body.title).toBe(updateData.title);
      expect(response.body.description).toBe(updateData.description);
    });
  });

  describe('DELETE /api/videos/:id', () => {
    it('should delete a video', async () => {
      // First create a video to delete;
      const newVideo = await prisma.video.create({
        data: {
          ...testVideo,
          id: 'test-video-to-delete'
        }
      });

      const response = await request(app);
        .delete(`/api/videos/${newVideo.id,}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);

      // Verify video was deleted;
      const deletedVideo = await prisma.video.findUnique({
        where: { id: newVideo.id, }
      });
      expect(deletedVideo).toBeNull();
    });
  });
});