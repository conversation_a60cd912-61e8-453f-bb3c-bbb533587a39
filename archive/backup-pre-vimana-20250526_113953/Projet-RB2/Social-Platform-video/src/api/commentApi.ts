import axios from 'axios';
import { Comment } from '../components/comments/CommentItem';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface CommentCreateData {
  content: string;
  postId: string;
  parentId?: string;
}

export interface CommentResponse {
  id: string;
  content: string;
  createdAt: string;
  author: {
    id: string;
    name: string;
    avatar: string;
  };
  likes: number;
  isLiked: boolean;
  replies?: Comment[];
}

// Get comments for a post
export const getComments = async (postId: string): Promise<Comment[]> => {
  try {
    const response = await axios.get(`${API_URL}/posts/${postId}/comments`);
    return response.data;
  } catch (error) {
    console.error('Error fetching comments:', error);
    throw error;
  }
};

// Create a new comment
export const createComment = async (data: CommentCreateData): Promise<CommentResponse> => {
  try {
    const response = await axios.post(`${API_URL}/comments`, data);
    return response.data;
  } catch (error) {
    console.error('Error creating comment:', error);
    throw error;
  }
};

// Like or unlike a comment
export const toggleCommentLike = async (commentId: string): Promise<{ likes: number; isLiked: boolean }> => {
  try {
    const response = await axios.post(`${API_URL}/comments/${commentId}/like`);
    return response.data;
  } catch (error) {
    console.error('Error toggling comment like:', error);
    throw error;
  }
};

// Delete a comment
export const deleteComment = async (commentId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/comments/${commentId}`);
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw error;
  }
};

// Report a comment
export const reportComment = async (commentId: string, reason: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/comments/${commentId}/report`, { reason });
  } catch (error) {
    console.error('Error reporting comment:', error);
    throw error;
  }
};

// Get replies for a comment
export const getCommentReplies = async (commentId: string): Promise<Comment[]> => {
  try {
    const response = await axios.get(`${API_URL}/comments/${commentId}/replies`);
    return response.data;
  } catch (error) {
    console.error('Error fetching comment replies:', error);
    throw error;
  }
};
