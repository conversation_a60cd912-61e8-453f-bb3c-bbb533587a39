import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Subscriber {
  id: string;
  userId: string;
  name: string;
  username: string;
  avatar: string;
  subscriptionPlanId: string;
  subscriptionPlanName: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
  autoRenew: boolean;
}

export interface Transaction {
  id: string;
  type: 'subscription' | 'tip' | 'purchase' | 'payout';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  description: string;
  fromUserId?: string;
  fromUserName?: string;
  toUserId?: string;
  toUserName?: string;
  contentId?: string;
  contentType?: 'video' | 'post' | 'livestream';
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'bank_account';
  isDefault: boolean;
  lastFour?: string;
  expiryDate?: string;
  brand?: string;
  accountName?: string;
  email?: string;
  createdAt: string;
}

export interface PayoutMethod {
  id: string;
  type: 'bank_account' | 'paypal';
  isDefault: boolean;
  accountName?: string;
  accountNumber?: string;
  routingNumber?: string;
  bankName?: string;
  email?: string;
  country: string;
  currency: string;
  createdAt: string;
}

export interface EarningsSummary {
  totalEarnings: number;
  pendingPayout: number;
  lastPayout: number;
  lastPayoutDate: string;
  currency: string;
  subscriptionEarnings: number;
  tipEarnings: number;
  contentPurchaseEarnings: number;
  earningsByMonth: Array<{
    month: string;
    earnings: number;
  }>;
}

// Get creator's subscription plans
export const getSubscriptionPlans = async (creatorId?: string): Promise<SubscriptionPlan[]> => {
  try {
    const response = await axios.get(`${API_URL}/monetization/subscription-plans`, {
      params: { creatorId },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching subscription plans:', error);
    throw error;
  }
};

// Create a subscription plan
export const createSubscriptionPlan = async (plan: Omit<SubscriptionPlan, 'id' | 'createdAt' | 'updatedAt'>): Promise<SubscriptionPlan> => {
  try {
    const response = await axios.post(`${API_URL}/monetization/subscription-plans`, plan);
    return response.data;
  } catch (error) {
    console.error('Error creating subscription plan:', error);
    throw error;
  }
};

// Update a subscription plan
export const updateSubscriptionPlan = async (planId: string, updates: Partial<Omit<SubscriptionPlan, 'id' | 'createdAt' | 'updatedAt'>>): Promise<SubscriptionPlan> => {
  try {
    const response = await axios.put(`${API_URL}/monetization/subscription-plans/${planId}`, updates);
    return response.data;
  } catch (error) {
    console.error('Error updating subscription plan:', error);
    throw error;
  }
};

// Delete a subscription plan
export const deleteSubscriptionPlan = async (planId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/monetization/subscription-plans/${planId}`);
  } catch (error) {
    console.error('Error deleting subscription plan:', error);
    throw error;
  }
};

// Get creator's subscribers
export const getSubscribers = async (page = 1, limit = 20): Promise<{
  subscribers: Subscriber[];
  totalCount: number;
  page: number;
  totalPages: number;
}> => {
  try {
    const response = await axios.get(`${API_URL}/monetization/subscribers`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching subscribers:', error);
    throw error;
  }
};

// Subscribe to a creator
export const subscribeToCreator = async (
  creatorId: string,
  planId: string,
  paymentMethodId: string
): Promise<{ subscriptionId: string; status: string }> => {
  try {
    const response = await axios.post(`${API_URL}/monetization/subscribe`, {
      creatorId,
      planId,
      paymentMethodId,
    });
    return response.data;
  } catch (error) {
    console.error('Error subscribing to creator:', error);
    throw error;
  }
};

// Cancel a subscription
export const cancelSubscription = async (
  subscriptionId: string,
  reason?: string
): Promise<{ status: string }> => {
  try {
    const response = await axios.post(`${API_URL}/monetization/subscriptions/${subscriptionId}/cancel`, {
      reason,
    });
    return response.data;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    throw error;
  }
};

// Send a tip to a creator
export const sendTip = async (
  creatorId: string,
  amount: number,
  currency: string,
  paymentMethodId: string,
  message?: string,
  contentId?: string,
  contentType?: 'video' | 'post' | 'livestream'
): Promise<Transaction> => {
  try {
    const response = await axios.post(`${API_URL}/monetization/tip`, {
      creatorId,
      amount,
      currency,
      paymentMethodId,
      message,
      contentId,
      contentType,
    });
    return response.data;
  } catch (error) {
    console.error('Error sending tip:', error);
    throw error;
  }
};

// Get user's transactions
export const getTransactions = async (
  type?: 'subscription' | 'tip' | 'purchase' | 'payout',
  status?: 'pending' | 'completed' | 'failed' | 'refunded',
  page = 1,
  limit = 20
): Promise<{
  transactions: Transaction[];
  totalCount: number;
  page: number;
  totalPages: number;
}> => {
  try {
    const response = await axios.get(`${API_URL}/monetization/transactions`, {
      params: { type, status, page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching transactions:', error);
    throw error;
  }
};

// Get user's payment methods
export const getPaymentMethods = async (): Promise<PaymentMethod[]> => {
  try {
    const response = await axios.get(`${API_URL}/monetization/payment-methods`);
    return response.data;
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    throw error;
  }
};

// Add a payment method
export const addPaymentMethod = async (
  type: 'card' | 'paypal' | 'bank_account',
  details: any
): Promise<PaymentMethod> => {
  try {
    const response = await axios.post(`${API_URL}/monetization/payment-methods`, {
      type,
      ...details,
    });
    return response.data;
  } catch (error) {
    console.error('Error adding payment method:', error);
    throw error;
  }
};

// Delete a payment method
export const deletePaymentMethod = async (paymentMethodId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/monetization/payment-methods/${paymentMethodId}`);
  } catch (error) {
    console.error('Error deleting payment method:', error);
    throw error;
  }
};

// Set default payment method
export const setDefaultPaymentMethod = async (paymentMethodId: string): Promise<PaymentMethod> => {
  try {
    const response = await axios.put(`${API_URL}/monetization/payment-methods/${paymentMethodId}/default`);
    return response.data;
  } catch (error) {
    console.error('Error setting default payment method:', error);
    throw error;
  }
};

// Get user's payout methods
export const getPayoutMethods = async (): Promise<PayoutMethod[]> => {
  try {
    const response = await axios.get(`${API_URL}/monetization/payout-methods`);
    return response.data;
  } catch (error) {
    console.error('Error fetching payout methods:', error);
    throw error;
  }
};

// Add a payout method
export const addPayoutMethod = async (
  type: 'bank_account' | 'paypal',
  details: any
): Promise<PayoutMethod> => {
  try {
    const response = await axios.post(`${API_URL}/monetization/payout-methods`, {
      type,
      ...details,
    });
    return response.data;
  } catch (error) {
    console.error('Error adding payout method:', error);
    throw error;
  }
};

// Delete a payout method
export const deletePayoutMethod = async (payoutMethodId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/monetization/payout-methods/${payoutMethodId}`);
  } catch (error) {
    console.error('Error deleting payout method:', error);
    throw error;
  }
};

// Set default payout method
export const setDefaultPayoutMethod = async (payoutMethodId: string): Promise<PayoutMethod> => {
  try {
    const response = await axios.put(`${API_URL}/monetization/payout-methods/${payoutMethodId}/default`);
    return response.data;
  } catch (error) {
    console.error('Error setting default payout method:', error);
    throw error;
  }
};

// Request a payout
export const requestPayout = async (
  amount: number,
  currency: string,
  payoutMethodId: string
): Promise<Transaction> => {
  try {
    const response = await axios.post(`${API_URL}/monetization/payout-request`, {
      amount,
      currency,
      payoutMethodId,
    });
    return response.data;
  } catch (error) {
    console.error('Error requesting payout:', error);
    throw error;
  }
};

// Get creator's earnings summary
export const getEarningsSummary = async (
  timeRange: 'week' | 'month' | 'year' | 'all' = 'month'
): Promise<EarningsSummary> => {
  try {
    const response = await axios.get(`${API_URL}/monetization/earnings-summary`, {
      params: { timeRange },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching earnings summary:', error);
    throw error;
  }
};
