import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface SearchFilters {
  query?: string;
  categories?: string[];
  sortBy?: 'recent' | 'popular' | 'trending';
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // in kilometers
  };
  dateRange?: {
    start: string;
    end: string;
  };
  duration?: {
    min: number; // in seconds
    max: number; // in seconds
  };
  tags?: string[];
  creatorId?: string;
}

export interface SearchResult {
  id: string;
  type: 'video' | 'post' | 'user' | 'livestream';
  title: string;
  description?: string;
  thumbnailUrl: string;
  createdAt: string;
  creator: {
    id: string;
    name: string;
    username: string;
    avatar: string;
  };
  stats: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
  };
  duration?: number; // for videos
  isLive?: boolean; // for livestreams
  tags?: string[];
  category?: string;
}

export interface SearchResponse {
  results: SearchResult[];
  totalResults: number;
  page: number;
  totalPages: number;
}

// Search content with filters
export const searchContent = async (
  filters: SearchFilters,
  page = 1,
  limit = 20
): Promise<SearchResponse> => {
  try {
    const response = await axios.get(`${API_URL}/search`, {
      params: {
        ...filters,
        page,
        limit,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error searching content:', error);
    throw error;
  }
};

// Get trending content
export const getTrendingContent = async (
  type?: 'video' | 'post' | 'livestream',
  category?: string,
  limit = 10
): Promise<SearchResult[]> => {
  try {
    const response = await axios.get(`${API_URL}/trending`, {
      params: {
        type,
        category,
        limit,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching trending content:', error);
    throw error;
  }
};

// Get categories
export const getCategories = async (): Promise<Array<{ id: string; name: string; count: number }>> => {
  try {
    const response = await axios.get(`${API_URL}/categories`);
    return response.data;
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
};

// Get popular tags
export const getPopularTags = async (limit = 20): Promise<Array<{ tag: string; count: number }>> => {
  try {
    const response = await axios.get(`${API_URL}/tags/popular`, {
      params: { limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching popular tags:', error);
    throw error;
  }
};

// Get related content
export const getRelatedContent = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  limit = 10
): Promise<SearchResult[]> => {
  try {
    const response = await axios.get(`${API_URL}/${contentType}s/${contentId}/related`, {
      params: { limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching related content:', error);
    throw error;
  }
};

// Search users
export const searchUsers = async (
  query: string,
  page = 1,
  limit = 20
): Promise<{
  users: Array<{
    id: string;
    name: string;
    username: string;
    avatar: string;
    bio?: string;
    isFollowing: boolean;
    stats: {
      followers: number;
      following: number;
      posts: number;
    };
  }>;
  totalResults: number;
  page: number;
  totalPages: number;
}> => {
  try {
    const response = await axios.get(`${API_URL}/users/search`, {
      params: {
        query,
        page,
        limit,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error searching users:', error);
    throw error;
  }
};
