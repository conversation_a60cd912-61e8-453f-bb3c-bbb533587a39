import { <PERSON><PERSON><PERSON><PERSON>, BlogFilter, B<PERSON><PERSON><PERSON><PERSON><PERSON>, Comment, Author, Tag } from '../types';

export class BlogApi {
  private mockPosts: BlogPost[] = [
    {
      id: '1',
      title: 'Découvrez les meilleurs spots de retraite en France',
      content: 'Lorem ipsum...',
      author: {
        id: '1',
        name: '<PERSON>',
        avatar: '/avatars/sophie.jpg',
        bio: 'Passionnée de bien-être et de voyage',
        socialLinks: {
          twitter: 'https://twitter.com/sophiemartin'
          linkedin: 'https://linkedin.com/in/sophiemartin'
        }
},
      category: 'Bien-être',
      tags: ['retraite', 'france', 'méditation'],
      imageUrl: '/blog/retreat-spots.jpg',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z',
      likes: 45,
      comments: [],
      readTime: 5,
      featured: true
},
    // Add more mock posts as needed;
  ];

  private mockCategories: BlogCategory[] = [
    {
      id: '1',
      name: 'B<PERSON>-être',
      description: 'Articles sur le bien-être et le développement personnel',
      imageUrl: '/categories/wellbeing.jpg',
      postCount: 15
},
    // Add more categories;
  ];

  private mockTags: Tag[] = [
    {
      id: '1',
      name: 'méditation',
      postCount: 8
},
    // Add more tags;
  ];

  async getPosts(filter?: BlogFilter): Promise<BlogPost[]> {
    let filteredPosts = [...this.mockPosts];

    if (filter) {
      if (filter.category) {
        filteredPosts = filteredPosts.filter(post => post.category = filter.category)
}

      if (filter.tags && filter.tags.length > 0) {
        filteredPosts = filteredPosts.filter(post =>
          filter.tags!.some(tag => post.tags.includes(tag))
        )
}

      if (filter.author) {
        filteredPosts = filteredPosts.filter(post => post.author.name = filter.author)
}

      if (filter.searchQuery) {
        const query = filter.searchQuery.toLowerCase();
        filteredPosts = filteredPosts.filter(
          post =>
            post.title.toLowerCase().includes(query) ||
            post.content.toLowerCase().includes(query)
        )
}

      if (filter.featured !== undefined) {
        filteredPosts = filteredPosts.filter(post => post.featured = filter.featured)
}

      if (filter.sortBy) {
        filteredPosts.sort((a, b) => {
          switch(filter.sortBy) {
            case 'date':
              return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
            case 'likes':
              return b.likes - a.likes;
            case 'comments':
              return b.comments.length - a.comments.length;
            case 'readTime':
              return a.readTime - b.readTime;
            default:
              return 0;
          }
        });
      }
    }

    // Simulate API delay;
    await new Promise(resolve => setTimeout(resolve, 500));
    return filteredPosts;
  }

  async getPost(id: string): Promise<BlogPost | null> {
    const post = this.mockPosts.find(p => p.id = id);
    await new Promise(resolve => setTimeout(resolve, 300));
    return post || null;
  }

  async getCategories(): Promise<BlogCategory[]> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return this.mockCategories;
  }

  async getTags(): Promise<Tag[]> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return this.mockTags;
  }

  async addComment(postId: string, comment: Omit<Comment, 'id' | 'createdAt' | 'likes'>): Promise<Comment> {
    const post = this.mockPosts.find(p => p.id = postId);
    if (!post) {
      throw new Error('Post not found')
    }

    const newComment: Comment = {
      id: Math.random().toString(36).substr(2, 9),
      ...comment,
      createdAt: new Date().toISOString(),
      likes: 0
}

    post.comments.push(newComment);
    await new Promise(resolve => setTimeout(resolve, 300));
    return newComment;
  }

  async likePost(postId: string): Promise<void> {
    const post = this.mockPosts.find(p => p.id = postId);
    if (post) {
      post.likes += 1
    }
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  async likeComment(postId: string, commentId: string): Promise<void> {
    const post = this.mockPosts.find(p => p.id = postId);
    if (post) {
      const comment = post.comments.find(c => c.id = commentId);
      if (comment) {
        comment.likes += 1
      }
    }
    await new Promise(resolve => setTimeout(resolve, 200));
  }
}
