import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface SocialLink {
  platform: string;
  url: string;
}

export interface ProfileData {
  id: string;
  name: string;
  username: string;
  bio?: string;
  website?: string;
  location?: string;
  avatar: string;
  coverPhoto: string;
  socialLinks: SocialLink[];
  stats: {
    posts: number;
    followers: number;
    following: number;
  };
}

export interface ProfileUpdateData {
  name: string;
  username: string;
  bio?: string;
  website?: string;
  location?: string;
  avatar: string;
  coverPhoto: string;
  socialLinks: SocialLink[];
}

// Get user profile
export const getProfile = async (userId: string): Promise<ProfileData> => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/profile`);
    return response.data;
  } catch (error) {
    console.error('Error fetching profile:', error);
    throw error;
  }
};

// Update user profile
export const updateProfile = async (userId: string, data: ProfileUpdateData): Promise<ProfileData> => {
  try {
    const response = await axios.put(`${API_URL}/users/${userId}/profile`, data);
    return response.data;
  } catch (error) {
    console.error('Error updating profile:', error);
    throw error;
  }
};

// Upload profile image (avatar or cover photo)
export const uploadProfileImage = async (
  userId: string,
  imageType: 'avatar' | 'cover',
  imageData: string
): Promise<{ url: string }> => {
  try {
    // Convert base64 to blob
    const base64Response = await fetch(imageData);
    const blob = await base64Response.blob();
    
    // Create form data
    const formData = new FormData();
    formData.append('image', blob, `${imageType}.jpg`);
    formData.append('type', imageType);
    
    const response = await axios.post(
      `${API_URL}/users/${userId}/images`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error uploading profile image:', error);
    throw error;
  }
};

// Get user posts
export const getUserPosts = async (userId: string, page = 1, limit = 10): Promise<any[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/posts`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching user posts:', error);
    throw error;
  }
};

// Get user saved posts
export const getUserSavedPosts = async (page = 1, limit = 10): Promise<any[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/me/saved`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching saved posts:', error);
    throw error;
  }
};

// Get user liked posts
export const getUserLikedPosts = async (page = 1, limit = 10): Promise<any[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/me/liked`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching liked posts:', error);
    throw error;
  }
};
