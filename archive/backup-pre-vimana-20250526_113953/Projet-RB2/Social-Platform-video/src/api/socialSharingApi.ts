import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export type SocialPlatform = 'facebook' | 'twitter' | 'instagram' | 'tiktok' | 'youtube' | 'linkedin' | 'pinterest' | 'reddit';

export interface SocialConnection {
  id: string;
  platform: SocialPlatform;
  username: string;
  profileUrl: string;
  avatarUrl: string;
  isConnected: boolean;
  lastSyncedAt: string;
  followerCount: number;
  followingCount: number;
  postCount: number;
}

export interface SocialShareResult {
  success: boolean;
  postId?: string;
  postUrl?: string;
  error?: string;
}

export interface SocialMetrics {
  platform: SocialPlatform;
  metrics: {
    shares: number;
    likes: number;
    comments: number;
    views: number;
    clicks: number;
    reach: number;
    engagement: number;
  };
  lastUpdated: string;
}

export interface CrossPostSettings {
  platforms: {
    [key in SocialPlatform]?: {
      enabled: boolean;
      customCaption?: boolean;
      schedulePost?: boolean;
      includeLink?: boolean;
      includeHashtags?: boolean;
    };
  };
  defaultHashtags: string[];
  defaultCaption: string;
}

// Get user's social connections
export const getSocialConnections = async (): Promise<SocialConnection[]> => {
  try {
    const response = await axios.get(`${API_URL}/social/connections`);
    return response.data;
  } catch (error) {
    console.error('Error fetching social connections:', error);
    throw error;
  }
};

// Connect to a social platform
export const connectSocialPlatform = async (
  platform: SocialPlatform,
  authCode: string
): Promise<SocialConnection> => {
  try {
    const response = await axios.post(`${API_URL}/social/connect`, {
      platform,
      authCode,
    });
    return response.data;
  } catch (error) {
    console.error(`Error connecting to ${platform}:`, error);
    throw error;
  }
};

// Disconnect from a social platform
export const disconnectSocialPlatform = async (
  platform: SocialPlatform
): Promise<{ success: boolean }> => {
  try {
    const response = await axios.post(`${API_URL}/social/disconnect`, {
      platform,
    });
    return response.data;
  } catch (error) {
    console.error(`Error disconnecting from ${platform}:`, error);
    throw error;
  }
};

// Share content to a social platform
export const shareToSocialPlatform = async (
  platform: SocialPlatform,
  contentId: string,
  contentType: 'video' | 'post' | 'story' | 'livestream',
  options?: {
    caption?: string;
    hashtags?: string[];
    scheduleTime?: string;
    includeLink?: boolean;
  }
): Promise<SocialShareResult> => {
  try {
    const response = await axios.post(`${API_URL}/social/share`, {
      platform,
      contentId,
      contentType,
      ...options,
    });
    return response.data;
  } catch (error) {
    console.error(`Error sharing to ${platform}:`, error);
    throw error;
  }
};

// Share content to multiple social platforms
export const shareToMultiplePlatforms = async (
  platforms: SocialPlatform[],
  contentId: string,
  contentType: 'video' | 'post' | 'story' | 'livestream',
  options?: {
    caption?: string;
    hashtags?: string[];
    scheduleTime?: string;
    includeLink?: boolean;
    platformSpecificOptions?: {
      [key in SocialPlatform]?: {
        caption?: string;
        hashtags?: string[];
        scheduleTime?: string;
        includeLink?: boolean;
      };
    };
  }
): Promise<{ [key in SocialPlatform]?: SocialShareResult }> => {
  try {
    const response = await axios.post(`${API_URL}/social/share/multiple`, {
      platforms,
      contentId,
      contentType,
      ...options,
    });
    return response.data;
  } catch (error) {
    console.error('Error sharing to multiple platforms:', error);
    throw error;
  }
};

// Get social metrics for content
export const getSocialMetrics = async (
  contentId: string,
  contentType: 'video' | 'post' | 'story' | 'livestream'
): Promise<SocialMetrics[]> => {
  try {
    const response = await axios.get(`${API_URL}/social/metrics`, {
      params: { contentId, contentType },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching social metrics:', error);
    throw error;
  }
};

// Get cross-post settings
export const getCrossPostSettings = async (): Promise<CrossPostSettings> => {
  try {
    const response = await axios.get(`${API_URL}/social/cross-post-settings`);
    return response.data;
  } catch (error) {
    console.error('Error fetching cross-post settings:', error);
    throw error;
  }
};

// Update cross-post settings
export const updateCrossPostSettings = async (
  settings: Partial<CrossPostSettings>
): Promise<CrossPostSettings> => {
  try {
    const response = await axios.put(`${API_URL}/social/cross-post-settings`, settings);
    return response.data;
  } catch (error) {
    console.error('Error updating cross-post settings:', error);
    throw error;
  }
};

// Get auth URL for a social platform
export const getSocialAuthUrl = async (
  platform: SocialPlatform,
  redirectUri: string
): Promise<{ authUrl: string }> => {
  try {
    const response = await axios.get(`${API_URL}/social/auth-url`, {
      params: { platform, redirectUri },
    });
    return response.data;
  } catch (error) {
    console.error(`Error getting auth URL for ${platform}:`, error);
    throw error;
  }
};

// Sync social account data (followers, etc.)
export const syncSocialAccount = async (
  platform: SocialPlatform
): Promise<SocialConnection> => {
  try {
    const response = await axios.post(`${API_URL}/social/sync`, {
      platform,
    });
    return response.data;
  } catch (error) {
    console.error(`Error syncing ${platform} account:`, error);
    throw error;
  }
};

// Get social platform features and limitations
export const getSocialPlatformInfo = async (
  platform: SocialPlatform
): Promise<{
  maxVideoLength?: number;
  maxFileSize?: number;
  supportedFormats?: string[];
  maxCaptionLength?: number;
  maxHashtags?: number;
  schedulingSupported: boolean;
  analyticsSupported: boolean;
  directPostingSupported: boolean;
}> => {
  try {
    const response = await axios.get(`${API_URL}/social/platform-info`, {
      params: { platform },
    });
    return response.data;
  } catch (error) {
    console.error(`Error getting info for ${platform}:`, error);
    throw error;
  }
};

// Get social sharing history
export const getSocialSharingHistory = async (
  limit: number = 20,
  offset: number = 0
): Promise<Array<{
  id: string;
  platform: SocialPlatform;
  contentId: string;
  contentType: 'video' | 'post' | 'story' | 'livestream';
  contentTitle: string;
  thumbnailUrl?: string;
  postUrl?: string;
  caption?: string;
  hashtags?: string[];
  status: 'pending' | 'published' | 'failed';
  scheduledFor?: string;
  publishedAt?: string;
  error?: string;
  metrics?: {
    likes: number;
    comments: number;
    shares: number;
    views: number;
  };
}>> => {
  try {
    const response = await axios.get(`${API_URL}/social/sharing-history`, {
      params: { limit, offset },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching social sharing history:', error);
    throw error;
  }
};

// Get popular hashtags for a platform
export const getPopularHashtags = async (
  platform: SocialPlatform,
  category?: string
): Promise<Array<{
  hashtag: string;
  postCount: number;
  trending: boolean;
}>> => {
  try {
    const response = await axios.get(`${API_URL}/social/popular-hashtags`, {
      params: { platform, category },
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching popular hashtags for ${platform}:`, error);
    throw error;
  }
};
