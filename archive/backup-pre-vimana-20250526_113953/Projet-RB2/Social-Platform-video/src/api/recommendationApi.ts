import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface RecommendationItem {
  id: string;
  type: 'video' | 'post' | 'story' | 'livestream' | 'profile';
  title: string;
  description?: string;
  thumbnailUrl?: string;
  duration?: number;
  createdAt: string;
  views?: number;
  likes?: number;
  creator: {
    id: string;
    name: string;
    username: string;
    avatarUrl?: string;
    isVerified?: boolean;
  };
  tags?: string[];
  category?: string;
  relevanceScore?: number;
  isNew?: boolean;
  isTrending?: boolean;
  isSponsored?: boolean;
}

export interface RecommendationResponse {
  recommendations: RecommendationItem[];
  totalCount: number;
  nextCursor?: string;
  recommendationType: RecommendationType;
  context?: {
    sourceId?: string;
    sourceType?: string;
    query?: string;
    filters?: Record<string, any>;
  };
}

export type RecommendationType = 
  | 'forYou'
  | 'trending'
  | 'similar'
  | 'basedOnHistory'
  | 'basedOnLikes'
  | 'fromCreatorsYouFollow'
  | 'newToYou'
  | 'recommended'
  | 'sponsored';

export interface RecommendationFilter {
  category?: string;
  tags?: string[];
  duration?: 'short' | 'medium' | 'long';
  createdAfter?: string;
  createdBefore?: string;
  minViews?: number;
  minLikes?: number;
  excludeWatched?: boolean;
  excludeCreators?: string[];
  onlyFromFollowing?: boolean;
}

/**
 * Get personalized recommendations for the current user
 */
export const getPersonalizedRecommendations = async (
  limit: number = 20,
  cursor?: string,
  filters?: RecommendationFilter
): Promise<RecommendationResponse> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/for-you`, {
      params: {
        limit,
        cursor,
        ...filters,
      },
    });
    return {
      ...response.data,
      recommendationType: 'forYou',
    };
  } catch (error) {
    console.error('Error fetching personalized recommendations:', error);
    throw error;
  }
};

/**
 * Get trending content recommendations
 */
export const getTrendingRecommendations = async (
  limit: number = 20,
  cursor?: string,
  filters?: RecommendationFilter
): Promise<RecommendationResponse> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/trending`, {
      params: {
        limit,
        cursor,
        ...filters,
      },
    });
    return {
      ...response.data,
      recommendationType: 'trending',
    };
  } catch (error) {
    console.error('Error fetching trending recommendations:', error);
    throw error;
  }
};

/**
 * Get recommendations similar to a specific content item
 */
export const getSimilarContent = async (
  contentId: string,
  contentType: 'video' | 'post' | 'story' | 'livestream',
  limit: number = 20,
  cursor?: string,
  filters?: RecommendationFilter
): Promise<RecommendationResponse> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/similar`, {
      params: {
        contentId,
        contentType,
        limit,
        cursor,
        ...filters,
      },
    });
    return {
      ...response.data,
      recommendationType: 'similar',
      context: {
        sourceId: contentId,
        sourceType: contentType,
      },
    };
  } catch (error) {
    console.error('Error fetching similar content recommendations:', error);
    throw error;
  }
};

/**
 * Get recommendations based on user's watch history
 */
export const getRecommendationsFromHistory = async (
  limit: number = 20,
  cursor?: string,
  filters?: RecommendationFilter
): Promise<RecommendationResponse> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/from-history`, {
      params: {
        limit,
        cursor,
        ...filters,
      },
    });
    return {
      ...response.data,
      recommendationType: 'basedOnHistory',
    };
  } catch (error) {
    console.error('Error fetching recommendations from history:', error);
    throw error;
  }
};

/**
 * Get recommendations based on user's likes
 */
export const getRecommendationsFromLikes = async (
  limit: number = 20,
  cursor?: string,
  filters?: RecommendationFilter
): Promise<RecommendationResponse> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/from-likes`, {
      params: {
        limit,
        cursor,
        ...filters,
      },
    });
    return {
      ...response.data,
      recommendationType: 'basedOnLikes',
    };
  } catch (error) {
    console.error('Error fetching recommendations from likes:', error);
    throw error;
  }
};

/**
 * Get recommendations from creators the user follows
 */
export const getRecommendationsFromFollowing = async (
  limit: number = 20,
  cursor?: string,
  filters?: RecommendationFilter
): Promise<RecommendationResponse> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/from-following`, {
      params: {
        limit,
        cursor,
        ...filters,
      },
    });
    return {
      ...response.data,
      recommendationType: 'fromCreatorsYouFollow',
    };
  } catch (error) {
    console.error('Error fetching recommendations from following:', error);
    throw error;
  }
};

/**
 * Get new content recommendations that the user hasn't seen
 */
export const getNewContentRecommendations = async (
  limit: number = 20,
  cursor?: string,
  filters?: RecommendationFilter
): Promise<RecommendationResponse> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/new`, {
      params: {
        limit,
        cursor,
        ...filters,
      },
    });
    return {
      ...response.data,
      recommendationType: 'newToYou',
    };
  } catch (error) {
    console.error('Error fetching new content recommendations:', error);
    throw error;
  }
};

/**
 * Get recommendations based on a search query
 */
export const getSearchBasedRecommendations = async (
  query: string,
  limit: number = 20,
  cursor?: string,
  filters?: RecommendationFilter
): Promise<RecommendationResponse> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/search`, {
      params: {
        query,
        limit,
        cursor,
        ...filters,
      },
    });
    return {
      ...response.data,
      recommendationType: 'recommended',
      context: {
        query,
      },
    };
  } catch (error) {
    console.error('Error fetching search-based recommendations:', error);
    throw error;
  }
};

/**
 * Provide feedback on a recommendation to improve future recommendations
 */
export const provideRecommendationFeedback = async (
  recommendationId: string,
  feedback: 'like' | 'dislike' | 'not_interested' | 'hide_creator' | 'report'
): Promise<{ success: boolean }> => {
  try {
    const response = await axios.post(`${API_URL}/recommendations/feedback`, {
      recommendationId,
      feedback,
    });
    return response.data;
  } catch (error) {
    console.error('Error providing recommendation feedback:', error);
    throw error;
  }
};

/**
 * Get user's recommendation preferences
 */
export const getRecommendationPreferences = async (): Promise<{
  categories: string[];
  excludedTags: string[];
  excludedCreators: string[];
  contentPreferences: {
    preferredDuration?: 'short' | 'medium' | 'long';
    preferredLanguages?: string[];
    preferNewContent?: boolean;
    preferFollowingContent?: boolean;
  };
}> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/preferences`);
    return response.data;
  } catch (error) {
    console.error('Error fetching recommendation preferences:', error);
    throw error;
  }
};

/**
 * Update user's recommendation preferences
 */
export const updateRecommendationPreferences = async (
  preferences: Partial<{
    categories: string[];
    excludedTags: string[];
    excludedCreators: string[];
    contentPreferences: {
      preferredDuration?: 'short' | 'medium' | 'long';
      preferredLanguages?: string[];
      preferNewContent?: boolean;
      preferFollowingContent?: boolean;
    };
  }>
): Promise<{ success: boolean }> => {
  try {
    const response = await axios.put(`${API_URL}/recommendations/preferences`, preferences);
    return response.data;
  } catch (error) {
    console.error('Error updating recommendation preferences:', error);
    throw error;
  }
};
