import axios from 'axios';
import { SearchResult } from './searchApi';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

// Types
export interface Collection {
  id: string;
  name: string;
  description: string;
  coverUrl: string | null;
  itemCount: number;
  isPublic: boolean;
  isCollaborative: boolean;
  createdAt: string;
  updatedAt: string;
  userId: string;
  collaborators?: User[];
  items?: CollectionItem[];
}

export interface CollectionItem {
  id: string;
  collectionId: string;
  contentId: string;
  contentType: 'video' | 'post' | 'livestream';
  addedAt: string;
  content: SearchResult;
  notes?: string;
}

export interface User {
  id: string;
  name: string;
  username: string;
  avatar: string;
}

export interface CreateCollectionData {
  name: string;
  description?: string;
  isPublic?: boolean;
  isCollaborative?: boolean;
  coverUrl?: string;
}

export interface UpdateCollectionData {
  name?: string;
  description?: string;
  isPublic?: boolean;
  isCollaborative?: boolean;
  coverUrl?: string;
}

// Get all collections for the current user
export const getUserCollections = async (): Promise<Collection[]> => {
  try {
    const response = await axios.get(`${API_URL}/collections`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user collections:', error);
    throw error;
  }
};

// Get a specific collection by ID
export const getCollection = async (collectionId: string): Promise<Collection> => {
  try {
    const response = await axios.get(`${API_URL}/collections/${collectionId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching collection ${collectionId}:`, error);
    throw error;
  }
};

// Create a new collection
export const createCollection = async (data: CreateCollectionData): Promise<Collection> => {
  try {
    const response = await axios.post(`${API_URL}/collections`, data);
    return response.data;
  } catch (error) {
    console.error('Error creating collection:', error);
    throw error;
  }
};

// Update a collection
export const updateCollection = async (
  collectionId: string,
  data: UpdateCollectionData
): Promise<Collection> => {
  try {
    const response = await axios.put(`${API_URL}/collections/${collectionId}`, data);
    return response.data;
  } catch (error) {
    console.error(`Error updating collection ${collectionId}:`, error);
    throw error;
  }
};

// Delete a collection
export const deleteCollection = async (collectionId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/collections/${collectionId}`);
  } catch (error) {
    console.error(`Error deleting collection ${collectionId}:`, error);
    throw error;
  }
};

// Add an item to a collection
export const addToCollection = async (
  collectionId: string,
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  notes?: string
): Promise<CollectionItem> => {
  try {
    const response = await axios.post(`${API_URL}/collections/${collectionId}/items`, {
      contentId,
      contentType,
      notes,
    });
    return response.data;
  } catch (error) {
    console.error(`Error adding item to collection ${collectionId}:`, error);
    throw error;
  }
};

// Remove an item from a collection
export const removeFromCollection = async (
  collectionId: string,
  itemId: string
): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/collections/${collectionId}/items/${itemId}`);
  } catch (error) {
    console.error(`Error removing item ${itemId} from collection ${collectionId}:`, error);
    throw error;
  }
};

// Get all items in a collection
export const getCollectionItems = async (
  collectionId: string,
  page = 1,
  limit = 20
): Promise<{
  items: CollectionItem[];
  totalItems: number;
  page: number;
  totalPages: number;
}> => {
  try {
    const response = await axios.get(`${API_URL}/collections/${collectionId}/items`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching items for collection ${collectionId}:`, error);
    throw error;
  }
};

// Add a collaborator to a collection
export const addCollaborator = async (
  collectionId: string,
  userId: string
): Promise<User> => {
  try {
    const response = await axios.post(`${API_URL}/collections/${collectionId}/collaborators`, {
      userId,
    });
    return response.data;
  } catch (error) {
    console.error(`Error adding collaborator to collection ${collectionId}:`, error);
    throw error;
  }
};

// Remove a collaborator from a collection
export const removeCollaborator = async (
  collectionId: string,
  userId: string
): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/collections/${collectionId}/collaborators/${userId}`);
  } catch (error) {
    console.error(`Error removing collaborator from collection ${collectionId}:`, error);
    throw error;
  }
};

// Get all collaborators for a collection
export const getCollaborators = async (collectionId: string): Promise<User[]> => {
  try {
    const response = await axios.get(`${API_URL}/collections/${collectionId}/collaborators`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching collaborators for collection ${collectionId}:`, error);
    throw error;
  }
};

// Get a shareable link for a collection
export const getShareableLink = async (collectionId: string): Promise<string> => {
  try {
    const response = await axios.get(`${API_URL}/collections/${collectionId}/share`);
    return response.data.shareUrl;
  } catch (error) {
    console.error(`Error getting shareable link for collection ${collectionId}:`, error);
    throw error;
  }
};

// Get public collections
export const getPublicCollections = async (
  page = 1,
  limit = 20
): Promise<{
  collections: Collection[];
  totalCollections: number;
  page: number;
  totalPages: number;
}> => {
  try {
    const response = await axios.get(`${API_URL}/collections/public`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching public collections:', error);
    throw error;
  }
};

// Reorder items in a collection
export const reorderCollectionItems = async (
  collectionId: string,
  itemIds: string[]
): Promise<void> => {
  try {
    await axios.put(`${API_URL}/collections/${collectionId}/items/reorder`, {
      itemIds,
    });
  } catch (error) {
    console.error(`Error reordering items in collection ${collectionId}:`, error);
    throw error;
  }
};
