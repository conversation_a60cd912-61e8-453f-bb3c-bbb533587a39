import axios from 'axios';
import { SearchResult } from './searchApi';
import { generateRecommendations, mockRecommendationPreferences } from '../mocks/recommendationsMock';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';
const USE_MOCK_DATA = true; // Set to false to use real API

// Get personalized feed for the current user
export const getPersonalizedFeed = async (
  page = 1,
  limit = 10
): Promise<{
  results: SearchResult[];
  totalResults: number;
  page: number;
  totalPages: number;
}> => {
  if (USE_MOCK_DATA) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Generate mock recommendations
    const mockResults = generateRecommendations(limit) as unknown as SearchResult[];

    // Simulate pagination
    const totalResults = 100;
    const totalPages = Math.ceil(totalResults / limit);

    return {
      results: mockResults,
      totalResults,
      page,
      totalPages,
    };
  }

  try {
    const response = await axios.get(`${API_URL}/recommendations/feed`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching personalized feed:', error);
    throw error;
  }
};

// Get similar content recommendations
export const getSimilarContent = async (
  contentId: string,
  contentType: 'video' | 'post' | 'livestream',
  limit = 10
): Promise<SearchResult[]> => {
  if (USE_MOCK_DATA) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Generate mock recommendations
    const mockResults = generateRecommendations(limit) as unknown as SearchResult[];

    // Add a reason related to the content
    mockResults.forEach(item => {
      item.recommendationReason = `Because you watched ${contentType} "${contentId.substring(0, 8)}"`;
    });

    return mockResults;
  }

  try {
    const response = await axios.get(`${API_URL}/recommendations/similar`, {
      params: {
        contentId,
        contentType,
        limit,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching similar content:', error);
    throw error;
  }
};

// Get recommended users to follow
export const getRecommendedUsers = async (
  limit = 10
): Promise<Array<{
  id: string;
  name: string;
  username: string;
  avatar: string;
  bio?: string;
  isFollowing: boolean;
  stats: {
    followers: number;
    following: number;
    posts: number;
  };
  matchScore: number;
  matchReason: string;
}>> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/users`, {
      params: { limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching recommended users:', error);
    throw error;
  }
};

// Get content based on user interests
export const getInterestBasedContent = async (
  interests: string[],
  page = 1,
  limit = 10
): Promise<{
  results: SearchResult[];
  totalResults: number;
  page: number;
  totalPages: number;
}> => {
  if (USE_MOCK_DATA) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Generate mock recommendations
    const mockResults = generateRecommendations(limit) as unknown as SearchResult[];

    // Add interest-based reasons
    mockResults.forEach(item => {
      const randomInterest = interests[Math.floor(Math.random() * interests.length)];
      item.recommendationReason = `Based on your interest in ${randomInterest}`;

      // Add the interest as a tag if it's not already there
      if (item.tags && !item.tags.includes(randomInterest)) {
        item.tags.push(randomInterest);
      }
    });

    // Simulate pagination
    const totalResults = 100;
    const totalPages = Math.ceil(totalResults / limit);

    return {
      results: mockResults,
      totalResults,
      page,
      totalPages,
    };
  }

  try {
    const response = await axios.get(`${API_URL}/recommendations/interests`, {
      params: {
        interests: interests.join(','),
        page,
        limit,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching interest-based content:', error);
    throw error;
  }
};

// Get user interests
export const getUserInterests = async (): Promise<Array<{ id: string; name: string; score: number }>> => {
  if (USE_MOCK_DATA) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Return mock interests
    return [
      { id: 'yoga', name: 'Yoga', score: 0.9 },
      { id: 'meditation', name: 'Meditation', score: 0.85 },
      { id: 'wellness', name: 'Wellness', score: 0.8 },
      { id: 'nutrition', name: 'Nutrition', score: 0.7 },
      { id: 'mindfulness', name: 'Mindfulness', score: 0.65 },
      { id: 'fitness', name: 'Fitness', score: 0.6 },
      { id: 'nature', name: 'Nature', score: 0.55 },
    ];
  }

  try {
    const response = await axios.get(`${API_URL}/users/me/interests`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user interests:', error);
    throw error;
  }
};

// Update user interests
export const updateUserInterests = async (
  interests: Array<{ id: string; score: number }>
): Promise<Array<{ id: string; name: string; score: number }>> => {
  try {
    const response = await axios.put(`${API_URL}/users/me/interests`, { interests });
    return response.data;
  } catch (error) {
    console.error('Error updating user interests:', error);
    throw error;
  }
};

// Get trending topics
export const getTrendingTopics = async (
  limit = 10
): Promise<Array<{ id: string; name: string; count: number; trend: number }>> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/trending-topics`, {
      params: { limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching trending topics:', error);
    throw error;
  }
};

// Get content for a specific topic
export const getTopicContent = async (
  topicId: string,
  page = 1,
  limit = 10
): Promise<{
  results: SearchResult[];
  totalResults: number;
  page: number;
  totalPages: number;
}> => {
  try {
    const response = await axios.get(`${API_URL}/recommendations/topics/${topicId}`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching topic content:', error);
    throw error;
  }
};

// Get recommendation preferences
export const getRecommendationPreferences = async (): Promise<{
  contentTypes: string[];
  categories: string[];
  excludedTags: string[];
  excludedCreators: string[];
  preferredLanguages: string[];
  preferredDuration?: 'short' | 'medium' | 'long';
  preferNewContent: boolean;
  preferFollowingContent: boolean;
}> => {
  if (USE_MOCK_DATA) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Return mock preferences
    return {
      contentTypes: ['video', 'post'],
      categories: mockRecommendationPreferences.categories,
      excludedTags: mockRecommendationPreferences.excludedTags,
      excludedCreators: mockRecommendationPreferences.excludedCreators,
      preferredLanguages: mockRecommendationPreferences.contentPreferences.preferredLanguages || ['en'],
      preferredDuration: mockRecommendationPreferences.contentPreferences.preferredDuration,
      preferNewContent: mockRecommendationPreferences.contentPreferences.preferNewContent || false,
      preferFollowingContent: mockRecommendationPreferences.contentPreferences.preferFollowingContent || false,
    };
  }

  try {
    const response = await axios.get(`${API_URL}/recommendations/preferences`);
    return response.data;
  } catch (error) {
    console.error('Error fetching recommendation preferences:', error);
    throw error;
  }
};

// Update recommendation preferences
export const updateRecommendationPreferences = async (
  preferences: Partial<{
    contentTypes: string[];
    categories: string[];
    excludedTags: string[];
    excludedCreators: string[];
    preferredLanguages: string[];
    preferredDuration?: 'short' | 'medium' | 'long';
    preferNewContent: boolean;
    preferFollowingContent: boolean;
  }>
): Promise<{
  contentTypes: string[];
  categories: string[];
  excludedTags: string[];
  excludedCreators: string[];
  preferredLanguages: string[];
  preferredDuration?: 'short' | 'medium' | 'long';
  preferNewContent: boolean;
  preferFollowingContent: boolean;
}> => {
  if (USE_MOCK_DATA) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Return updated mock preferences
    return {
      contentTypes: preferences.contentTypes || ['video', 'post'],
      categories: preferences.categories || mockRecommendationPreferences.categories,
      excludedTags: preferences.excludedTags || mockRecommendationPreferences.excludedTags,
      excludedCreators: preferences.excludedCreators || mockRecommendationPreferences.excludedCreators,
      preferredLanguages: preferences.preferredLanguages || mockRecommendationPreferences.contentPreferences.preferredLanguages || ['en'],
      preferredDuration: preferences.preferredDuration || mockRecommendationPreferences.contentPreferences.preferredDuration,
      preferNewContent: preferences.preferNewContent !== undefined ? preferences.preferNewContent : mockRecommendationPreferences.contentPreferences.preferNewContent || false,
      preferFollowingContent: preferences.preferFollowingContent !== undefined ? preferences.preferFollowingContent : mockRecommendationPreferences.contentPreferences.preferFollowingContent || false,
    };
  }

  try {
    const response = await axios.put(`${API_URL}/recommendations/preferences`, preferences);
    return response.data;
  } catch (error) {
    console.error('Error updating recommendation preferences:', error);
    throw error;
  }
};
