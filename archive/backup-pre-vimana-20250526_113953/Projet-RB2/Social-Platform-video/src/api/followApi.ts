import axios from 'axios';
import { User } from '../components/follow/FollowersList';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

// Get followers for a user
export const getFollowers = async (userId: string): Promise<User[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/followers`);
    return response.data;
  } catch (error) {
    console.error('Error fetching followers:', error);
    throw error;
  }
};

// Get users that a user is following
export const getFollowing = async (userId: string): Promise<User[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/${userId}/following`);
    return response.data;
  } catch (error) {
    console.error('Error fetching following:', error);
    throw error;
  }
};

// Follow a user
export const followUser = async (userId: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/users/${userId}/follow`);
  } catch (error) {
    console.error('Error following user:', error);
    throw error;
  }
};

// Unfollow a user
export const unfollowUser = async (userId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/users/${userId}/follow`);
  } catch (error) {
    console.error('Error unfollowing user:', error);
    throw error;
  }
};

// Get follow suggestions
export const getFollowSuggestions = async (): Promise<User[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/suggestions`);
    return response.data;
  } catch (error) {
    console.error('Error fetching follow suggestions:', error);
    throw error;
  }
};

// Block a user
export const blockUser = async (userId: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/users/${userId}/block`);
  } catch (error) {
    console.error('Error blocking user:', error);
    throw error;
  }
};

// Unblock a user
export const unblockUser = async (userId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/users/${userId}/block`);
  } catch (error) {
    console.error('Error unblocking user:', error);
    throw error;
  }
};

// Get blocked users
export const getBlockedUsers = async (): Promise<User[]> => {
  try {
    const response = await axios.get(`${API_URL}/users/blocked`);
    return response.data;
  } catch (error) {
    console.error('Error fetching blocked users:', error);
    throw error;
  }
};

// Report a user
export const reportUser = async (userId: string, reason: string): Promise<void> => {
  try {
    await axios.post(`${API_URL}/users/${userId}/report`, { reason });
  } catch (error) {
    console.error('Error reporting user:', error);
    throw error;
  }
};
