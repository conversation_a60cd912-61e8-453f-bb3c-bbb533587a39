const LivestreamService = require('../services/CachedLivestreamService');
const MessagingIntegrationService = require('../services/MessagingIntegrationService');
const logger = require('../utils/logger');

/**
 * Contrôleur pour la gestion des livestreams
 */
class LivestreamController {
  /**
   * Récupère tous les livestreams avec filtres optionnels
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async getLivestreams(req, res) {
    try {
      const filters = req.query;
      const livestreams = await LivestreamService.getLivestreams(filters);
      res.status(200).json(livestreams);
    } catch (error) {
      logger.error(`Error in getLivestreams: ${error.message}`, error);
      res.status(500).json({ message: 'Error fetching livestreams' });
    }
  }

  /**
   * Récupère un livestream par son ID
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async getLivestreamById(req, res) {
    try {
      const { id } = req.params;
      const livestream = await LivestreamService.getLivestreamById(id);

      if (!livestream) {
        return res.status(404).json({ message: 'Livestream not found' });
      }

      res.status(200).json(livestream);
    } catch (error) {
      logger.error(`Error in getLivestreamById: ${error.message}`, error);
      res.status(500).json({ message: 'Error fetching livestream' });
    }
  }

  /**
   * Crée un nouveau livestream
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async createLivestream(req, res) {
    try {
      const livestreamData = req.body;
      const userId = req.user.id;

      // Ajouter l'ID de l'utilisateur comme hôte
      livestreamData.hostId = userId;

      const createdLivestream = await LivestreamService.createLivestream(livestreamData);

      // Envoyer une notification via le service de messagerie
      await MessagingIntegrationService.sendLivestreamNotification(
        createdLivestream.id,
        userId, // Envoyer à l'hôte
        'created',
        `Votre livestream "${createdLivestream.title}" a été créé avec succès.`
      );

      res.status(201).json(createdLivestream);
    } catch (error) {
      logger.error(`Error in createLivestream: ${error.message}`, error);
      res.status(500).json({ message: 'Error creating livestream' });
    }
  }

  /**
   * Met à jour un livestream existant
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async updateLivestream(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const userId = req.user.id;

      // Vérifier que l'utilisateur est l'hôte du livestream
      const livestream = await LivestreamService.getLivestreamById(id);

      if (!livestream) {
        return res.status(404).json({ message: 'Livestream not found' });
      }

      if (livestream.hostId !== userId && req.user.role !== 'ADMIN') {
        return res.status(403).json({ message: 'You are not authorized to update this livestream' });
      }

      const updatedLivestream = await LivestreamService.updateLivestream(id, updateData);
      res.status(200).json(updatedLivestream);
    } catch (error) {
      logger.error(`Error in updateLivestream: ${error.message}`, error);
      res.status(500).json({ message: 'Error updating livestream' });
    }
  }

  /**
   * Démarre un livestream
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async startLivestream(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      // Vérifier que l'utilisateur est l'hôte du livestream
      const livestream = await LivestreamService.getLivestreamById(id);

      if (!livestream) {
        return res.status(404).json({ message: 'Livestream not found' });
      }

      if (livestream.hostId !== userId && req.user.role !== 'ADMIN') {
        return res.status(403).json({ message: 'You are not authorized to start this livestream' });
      }

      const result = await LivestreamService.startLivestream(id);

      // Envoyer une notification via le service de messagerie
      await MessagingIntegrationService.sendLivestreamNotification(
        id,
        null, // Envoyer à tous les abonnés
        'started',
        `Le livestream "${livestream.title}" vient de commencer!`
      );

      res.status(200).json(result);
    } catch (error) {
      logger.error(`Error in startLivestream: ${error.message}`, error);
      res.status(500).json({ message: 'Error starting livestream' });
    }
  }

  /**
   * Termine un livestream
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async endLivestream(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      // Vérifier que l'utilisateur est l'hôte du livestream
      const livestream = await LivestreamService.getLivestreamById(id);

      if (!livestream) {
        return res.status(404).json({ message: 'Livestream not found' });
      }

      if (livestream.hostId !== userId && req.user.role !== 'ADMIN') {
        return res.status(403).json({ message: 'You are not authorized to end this livestream' });
      }

      const result = await LivestreamService.endLivestream(id);

      // Envoyer une notification via le service de messagerie
      await MessagingIntegrationService.sendLivestreamNotification(
        id,
        null, // Envoyer à tous les abonnés
        'ended',
        `Le livestream "${livestream.title}" est terminé.`
      );

      res.status(200).json(result);
    } catch (error) {
      logger.error(`Error in endLivestream: ${error.message}`, error);
      res.status(500).json({ message: 'Error ending livestream' });
    }
  }

  /**
   * Récupère les messages d'un livestream
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async getLivestreamMessages(req, res) {
    try {
      const { id } = req.params;
      const messages = await LivestreamService.getLivestreamMessages(id);
      res.status(200).json(messages);
    } catch (error) {
      logger.error(`Error in getLivestreamMessages: ${error.message}`, error);
      res.status(500).json({ message: 'Error fetching livestream messages' });
    }
  }

  /**
   * Envoie un message dans un livestream
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async sendLivestreamMessage(req, res) {
    try {
      const { id } = req.params;
      const { content, type = 'text' } = req.body;
      const userId = req.user.id;
      const userName = `${req.user.firstName} ${req.user.lastName}`;

      const message = await LivestreamService.sendLivestreamMessage(id, {
        userId,
        userName,
        content,
        type,
      });

      // Relayer le message via le service de messagerie
      await MessagingIntegrationService.relayLivestreamMessage(
        id,
        userId,
        userName,
        content
      );

      res.status(201).json(message);
    } catch (error) {
      logger.error(`Error in sendLivestreamMessage: ${error.message}`, error);
      res.status(500).json({ message: 'Error sending livestream message' });
    }
  }
}

module.exports = new LivestreamController();
