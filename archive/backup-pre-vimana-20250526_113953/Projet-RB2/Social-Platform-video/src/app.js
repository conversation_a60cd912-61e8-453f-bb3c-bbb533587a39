const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { initializeCache } = require('./services/cache/initCache');
const logger = require('./utils/logger');

// Routes
const livestreamRoutes = require('./routes/livestreamRoutes');
const blogRoutes = require('./routes/blogRoutes');

// Initialiser l'application Express
const app = express();

// Middleware
app.use(helmet()); // Sécurité
app.use(cors()); // CORS
app.use(express.json()); // Parser JSON
app.use(morgan('combined')); // Logging HTTP

// Routes API
app.use('/api/livestreams', livestreamRoutes);
app.use('/api/blog', blogRoutes);

// Route de santé
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Middleware de gestion des erreurs
app.use((err, req, res, next) => {
  logger.error(`Unhandled error: ${err.message}`, err);
  res.status(500).json({ message: 'Internal server error' });
});

// Initialiser le cache au démarrage
(async () => {
  try {
    await initializeCache();
    logger.info('Cache initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize cache', error);
  }
})();

// Exporter l'application
module.exports = app;
