import { useState, useEffect, useCallback } from 'react';
import { User } from '../components/social/follow/FollowSuggestions';

interface UseFollowOptions {
  userId?: string;
  initialFollowers?: User[];
  initialFollowing?: User[];
  initialSuggestions?: User[];
  pageSize?: number;
}

interface UseFollowResult {
  followers: User[];
  following: User[];
  suggestions: User[];
  followerCount: number;
  followingCount: number;
  isLoadingFollowers: boolean;
  isLoadingFollowing: boolean;
  isLoadingSuggestions: boolean;
  errorFollowers: string | null;
  errorFollowing: string | null;
  errorSuggestions: string | null;
  follow: (userId: string) => Promise<void>;
  unfollow: (userId: string) => Promise<void>;
  toggleNotifications: (userId: string, enabled: boolean) => Promise<void>;
  dismissSuggestion: (userId: string) => Promise<void>;
  loadMoreFollowers: () => Promise<void>;
  loadMoreFollowing: () => Promise<void>;
  refreshFollowers: () => Promise<void>;
  refreshFollowing: () => Promise<void>;
  refreshSuggestions: () => Promise<void>;
  searchFollowers: (query: string) => void;
  searchFollowing: (query: string) => void;
  hasMoreFollowers: boolean;
  hasMoreFollowing: boolean;
}

// Mock API functions (replace with actual API calls)
const api = {
  getFollowers: async (
    userId: string,
    page: number,
    pageSize: number,
    query?: string
  ): Promise<{ users: User[]; totalCount: number }> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    const mockUsers: User[] = Array.from({ length: pageSize }).map((_, index) => ({
      id: `follower-${page}-${index}`,
      name: `Follower ${page * pageSize + index + 1}`,
      username: `follower${page * pageSize + index + 1}`,
      avatar: index % 3 === 0 ? `https://i.pravatar.cc/150?u=follower-${index}` : undefined,
      bio: index % 2 === 0 ? `This is the bio for follower ${page * pageSize + index + 1}` : undefined,
      isFollowing: Math.random() > 0.5,
      isVerified: Math.random() > 0.8,
      followerCount: Math.floor(Math.random() * 1000),
      mutualFollowers: Math.floor(Math.random() * 5),
      mutualFollowerNames: ['John Doe', 'Jane Smith'],
    }));
    
    // Filter by query if provided
    const filteredUsers = query
      ? mockUsers.filter(
          user =>
            user.name.toLowerCase().includes(query.toLowerCase()) ||
            user.username.toLowerCase().includes(query.toLowerCase())
        )
      : mockUsers;
    
    return {
      users: filteredUsers,
      totalCount: 120, // Mock total count
    };
  },
  
  getFollowing: async (
    userId: string,
    page: number,
    pageSize: number,
    query?: string
  ): Promise<{ users: User[]; totalCount: number }> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    const mockUsers: User[] = Array.from({ length: pageSize }).map((_, index) => ({
      id: `following-${page}-${index}`,
      name: `Following ${page * pageSize + index + 1}`,
      username: `following${page * pageSize + index + 1}`,
      avatar: index % 3 === 0 ? `https://i.pravatar.cc/150?u=following-${index}` : undefined,
      bio: index % 2 === 0 ? `This is the bio for following ${page * pageSize + index + 1}` : undefined,
      isFollowing: true, // Always true for following list
      isVerified: Math.random() > 0.8,
      followerCount: Math.floor(Math.random() * 1000),
      mutualFollowers: Math.floor(Math.random() * 5),
      mutualFollowerNames: ['John Doe', 'Jane Smith'],
    }));
    
    // Filter by query if provided
    const filteredUsers = query
      ? mockUsers.filter(
          user =>
            user.name.toLowerCase().includes(query.toLowerCase()) ||
            user.username.toLowerCase().includes(query.toLowerCase())
        )
      : mockUsers;
    
    return {
      users: filteredUsers,
      totalCount: 85, // Mock total count
    };
  },
  
  getSuggestions: async (userId: string): Promise<User[]> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    return Array.from({ length: 5 }).map((_, index) => ({
      id: `suggestion-${index}`,
      name: `Suggested User ${index + 1}`,
      username: `suggested${index + 1}`,
      avatar: index % 3 === 0 ? `https://i.pravatar.cc/150?u=suggestion-${index}` : undefined,
      bio: index % 2 === 0 ? `This is the bio for suggested user ${index + 1}` : undefined,
      isFollowing: false, // Always false for suggestions
      isVerified: Math.random() > 0.8,
      followerCount: Math.floor(Math.random() * 1000),
      mutualFollowers: Math.floor(Math.random() * 5),
      mutualFollowerNames: ['John Doe', 'Jane Smith'],
    }));
  },
  
  follow: async (userId: string): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
  },
  
  unfollow: async (userId: string): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
  },
  
  toggleNotifications: async (userId: string, enabled: boolean): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
  },
  
  dismissSuggestion: async (userId: string): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
  },
};

export function useFollow({
  userId = 'current-user',
  initialFollowers = [],
  initialFollowing = [],
  initialSuggestions = [],
  pageSize = 20,
}: UseFollowOptions = {}): UseFollowResult {
  // State for followers
  const [followers, setFollowers] = useState<User[]>(initialFollowers);
  const [followerCount, setFollowerCount] = useState(0);
  const [isLoadingFollowers, setIsLoadingFollowers] = useState(true);
  const [errorFollowers, setErrorFollowers] = useState<string | null>(null);
  const [followerPage, setFollowerPage] = useState(1);
  const [hasMoreFollowers, setHasMoreFollowers] = useState(true);
  const [followerQuery, setFollowerQuery] = useState('');
  
  // State for following
  const [following, setFollowing] = useState<User[]>(initialFollowing);
  const [followingCount, setFollowingCount] = useState(0);
  const [isLoadingFollowing, setIsLoadingFollowing] = useState(true);
  const [errorFollowing, setErrorFollowing] = useState<string | null>(null);
  const [followingPage, setFollowingPage] = useState(1);
  const [hasMoreFollowing, setHasMoreFollowing] = useState(true);
  const [followingQuery, setFollowingQuery] = useState('');
  
  // State for suggestions
  const [suggestions, setSuggestions] = useState<User[]>(initialSuggestions);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(true);
  const [errorSuggestions, setErrorSuggestions] = useState<string | null>(null);
  
  // Fetch followers
  const fetchFollowers = useCallback(async (
    page: number,
    replace: boolean = false,
    query: string = ''
  ) => {
    setIsLoadingFollowers(true);
    setErrorFollowers(null);
    
    try {
      const { users: newFollowers, totalCount } = await api.getFollowers(
        userId,
        page,
        pageSize,
        query
      );
      
      setFollowerCount(totalCount);
      
      if (replace) {
        setFollowers(newFollowers);
      } else {
        setFollowers(prevFollowers => [...prevFollowers, ...newFollowers]);
      }
      
      setHasMoreFollowers(newFollowers.length === pageSize);
    } catch (err) {
      setErrorFollowers('Failed to load followers. Please try again.');
      console.error('Error fetching followers:', err);
    } finally {
      setIsLoadingFollowers(false);
    }
  }, [userId, pageSize]);
  
  // Fetch following
  const fetchFollowing = useCallback(async (
    page: number,
    replace: boolean = false,
    query: string = ''
  ) => {
    setIsLoadingFollowing(true);
    setErrorFollowing(null);
    
    try {
      const { users: newFollowing, totalCount } = await api.getFollowing(
        userId,
        page,
        pageSize,
        query
      );
      
      setFollowingCount(totalCount);
      
      if (replace) {
        setFollowing(newFollowing);
      } else {
        setFollowing(prevFollowing => [...prevFollowing, ...newFollowing]);
      }
      
      setHasMoreFollowing(newFollowing.length === pageSize);
    } catch (err) {
      setErrorFollowing('Failed to load following. Please try again.');
      console.error('Error fetching following:', err);
    } finally {
      setIsLoadingFollowing(false);
    }
  }, [userId, pageSize]);
  
  // Fetch suggestions
  const fetchSuggestions = useCallback(async () => {
    setIsLoadingSuggestions(true);
    setErrorSuggestions(null);
    
    try {
      const newSuggestions = await api.getSuggestions(userId);
      setSuggestions(newSuggestions);
    } catch (err) {
      setErrorSuggestions('Failed to load suggestions. Please try again.');
      console.error('Error fetching suggestions:', err);
    } finally {
      setIsLoadingSuggestions(false);
    }
  }, [userId]);
  
  // Initial fetch
  useEffect(() => {
    if (initialFollowers.length === 0) {
      fetchFollowers(1, true);
    } else {
      setIsLoadingFollowers(false);
    }
    
    if (initialFollowing.length === 0) {
      fetchFollowing(1, true);
    } else {
      setIsLoadingFollowing(false);
    }
    
    if (initialSuggestions.length === 0) {
      fetchSuggestions();
    } else {
      setIsLoadingSuggestions(false);
    }
  }, [fetchFollowers, fetchFollowing, fetchSuggestions, initialFollowers.length, initialFollowing.length, initialSuggestions.length]);
  
  // Follow a user
  const follow = async (targetUserId: string) => {
    try {
      await api.follow(targetUserId);
      
      // Update following list
      setFollowing(prevFollowing => {
        const userExists = prevFollowing.some(user => user.id === targetUserId);
        
        if (!userExists) {
          // Find user in suggestions or followers
          const userToAdd = [...suggestions, ...followers].find(user => user.id === targetUserId);
          
          if (userToAdd) {
            return [...prevFollowing, { ...userToAdd, isFollowing: true }];
          }
        }
        
        return prevFollowing;
      });
      
      // Update followers list
      setFollowers(prevFollowers =>
        prevFollowers.map(user =>
          user.id === targetUserId ? { ...user, isFollowing: true } : user
        )
      );
      
      // Update suggestions list
      setSuggestions(prevSuggestions =>
        prevSuggestions.map(user =>
          user.id === targetUserId ? { ...user, isFollowing: true } : user
        )
      );
    } catch (err) {
      console.error('Error following user:', err);
      throw err;
    }
  };
  
  // Unfollow a user
  const unfollow = async (targetUserId: string) => {
    try {
      await api.unfollow(targetUserId);
      
      // Update following list
      setFollowing(prevFollowing =>
        prevFollowing.filter(user => user.id !== targetUserId)
      );
      
      // Update followers list
      setFollowers(prevFollowers =>
        prevFollowers.map(user =>
          user.id === targetUserId ? { ...user, isFollowing: false } : user
        )
      );
      
      // Update suggestions list
      setSuggestions(prevSuggestions =>
        prevSuggestions.map(user =>
          user.id === targetUserId ? { ...user, isFollowing: false } : user
        )
      );
    } catch (err) {
      console.error('Error unfollowing user:', err);
      throw err;
    }
  };
  
  // Toggle notifications for a user
  const toggleNotifications = async (targetUserId: string, enabled: boolean) => {
    try {
      await api.toggleNotifications(targetUserId, enabled);
    } catch (err) {
      console.error('Error toggling notifications:', err);
      throw err;
    }
  };
  
  // Dismiss a suggestion
  const dismissSuggestion = async (targetUserId: string) => {
    try {
      await api.dismissSuggestion(targetUserId);
      
      // Remove from suggestions list
      setSuggestions(prevSuggestions =>
        prevSuggestions.filter(user => user.id !== targetUserId)
      );
    } catch (err) {
      console.error('Error dismissing suggestion:', err);
      throw err;
    }
  };
  
  // Load more followers
  const loadMoreFollowers = async () => {
    if (!hasMoreFollowers || isLoadingFollowers) return;
    
    const nextPage = followerPage + 1;
    await fetchFollowers(nextPage, false, followerQuery);
    setFollowerPage(nextPage);
  };
  
  // Load more following
  const loadMoreFollowing = async () => {
    if (!hasMoreFollowing || isLoadingFollowing) return;
    
    const nextPage = followingPage + 1;
    await fetchFollowing(nextPage, false, followingQuery);
    setFollowingPage(nextPage);
  };
  
  // Refresh followers
  const refreshFollowers = async () => {
    setFollowerPage(1);
    await fetchFollowers(1, true, followerQuery);
  };
  
  // Refresh following
  const refreshFollowing = async () => {
    setFollowingPage(1);
    await fetchFollowing(1, true, followingQuery);
  };
  
  // Refresh suggestions
  const refreshSuggestions = async () => {
    await fetchSuggestions();
  };
  
  // Search followers
  const searchFollowers = (query: string) => {
    setFollowerQuery(query);
    setFollowerPage(1);
    fetchFollowers(1, true, query);
  };
  
  // Search following
  const searchFollowing = (query: string) => {
    setFollowingQuery(query);
    setFollowingPage(1);
    fetchFollowing(1, true, query);
  };
  
  return {
    followers,
    following,
    suggestions,
    followerCount,
    followingCount,
    isLoadingFollowers,
    isLoadingFollowing,
    isLoadingSuggestions,
    errorFollowers,
    errorFollowing,
    errorSuggestions,
    follow,
    unfollow,
    toggleNotifications,
    dismissSuggestion,
    loadMoreFollowers,
    loadMoreFollowing,
    refreshFollowers,
    refreshFollowing,
    refreshSuggestions,
    searchFollowers,
    searchFollowing,
    hasMoreFollowers,
    hasMoreFollowing,
  };
}
