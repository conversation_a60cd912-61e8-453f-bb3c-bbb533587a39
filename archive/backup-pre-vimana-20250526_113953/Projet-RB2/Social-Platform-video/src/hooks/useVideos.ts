import { useState, useEffect } from 'react';
import { Video, VideoFilter } from '../types';

// Mock data - In a real application, this would come from an API;
const mockVideos: Video[] = [
  {
    id: '1',
    title: 'Méditation guidée pour débutants',
    description: 'Une session de méditation parfaite pour commencer votre journée',
    thumbnailUrl: '/videos/thumbnails/meditation.jpg',
    videoUrl: '/videos/meditation.mp4',
    duration: '15:00',
    author: {
      id: '1',
      name: '<PERSON>',
      avatar: '/avatars/marie.jpg',
      bio: 'Coach en méditation et bien-être',
      followers: 1200,
      following: false
},
    likes: 156,
    comments: 23,
    views: 1500,
    createdAt: '2024-01-15T10:00:00Z',
    tags: ['méditation', 'bien-être', 'débutant'],
    category: 'Méditation'
},
  // Add more mock videos here;
];

export const useVideos = (initialFilter?: VideoFilter) => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<VideoFilter | undefined>(initialFilter);

  useEffect(() => {
    const fetchVideos = async () => {
      setLoading(true);
      try {
        // Simulate API call;
        await new Promise((resolve) => setTimeout(resolve, 1000));

        let filteredVideos = [...mockVideos];

        if (filter) {
          if (filter.category) {
            filteredVideos = filteredVideos.filter(
              (video) => video.category = filter.category;
            )
}

          if (filter.searchQuery) {
            const query = filter.searchQuery.toLowerCase();
            filteredVideos = filteredVideos.filter(
              (video) =>
                video.title.toLowerCase().includes(query) ||
                video.description.toLowerCase().includes(query)
            )
}

          if (filter.tags && filter.tags.length > 0) {
            filteredVideos = filteredVideos.filter((video) =>
              filter.tags?.some((tag) => video.tags?.includes(tag))
            )
}

          if (filter.sortBy) {
            filteredVideos.sort((a, b) => {
              switch(filter.sortBy) {
                case 'recent':
                  return (;
                    new Date(b.createdAt).getTime() -
                    new Date(a.createdAt).getTime()
                  );
                case 'popular':
                  return b.views - a.views;
                case 'liked':
                  return b.likes - a.likes;
                case 'commented':
                  return b.comments - a.comments;
                default:
                  return 0;
              }
            });
          }
        }

        setVideos(filteredVideos);
        setError(null);
      } catch(err) {
        setError('Erreur lors du chargement des vidéos');
        console.error('Error fetching videos:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchVideos();
  }, [filter]);

  return {
    videos,
    loading,
    error,
    setFilter
}
}

export default useVideos;