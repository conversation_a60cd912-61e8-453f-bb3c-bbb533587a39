import { useState, useEffect, useCallback } from 'react';
import { Comment } from '../components/social/comments/CommentItem';

interface UseCommentsOptions {
  entityId: string;
  entityType: 'video' | 'livestream' | 'blog';
  pageSize?: number;
  initialComments?: Comment[];
}

interface UseCommentsResult {
  comments: Comment[];
  totalCount: number;
  isLoading: boolean;
  error: string | null;
  addComment: (content: string, attachments?: File[]) => Promise<void>;
  likeComment: (commentId: string) => Promise<void>;
  replyToComment: (commentId: string, content: string, attachments?: File[]) => Promise<void>;
  editComment: (commentId: string, content: string, attachments?: File[]) => Promise<void>;
  deleteComment: (commentId: string) => Promise<void>;
  reportComment: (commentId: string, reason: string) => Promise<void>;
  loadMoreComments: () => Promise<void>;
  refreshComments: () => Promise<void>;
  hasMoreComments: boolean;
}

// Mock API functions (replace with actual API calls)
const api = {
  getComments: async (
    entityId: string,
    entityType: string,
    page: number,
    pageSize: number
  ): Promise<{ comments: Comment[]; totalCount: number }> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    const mockComments: Comment[] = Array.from({ length: pageSize }).map((_, index) => ({
      id: `comment-${page}-${index}`,
      content: `This is a mock comment ${page * pageSize + index + 1}`,
      userId: `user-${index % 5}`,
      userName: `User ${index % 5}`,
      userAvatar: index % 3 === 0 ? `https://i.pravatar.cc/150?u=user-${index % 5}` : undefined,
      createdAt: new Date(Date.now() - Math.random() * 1000000000).toISOString(),
      likes: Math.floor(Math.random() * 50),
      liked: Math.random() > 0.7,
      replyCount: Math.floor(Math.random() * 5),
      isEdited: Math.random() > 0.8,
      isDeleted: Math.random() > 0.95,
      isAuthor: Math.random() > 0.7,
      replies: index % 4 === 0 ? [
        {
          id: `reply-${page}-${index}-1`,
          content: `This is a reply to comment ${page * pageSize + index + 1}`,
          userId: `user-${(index + 1) % 5}`,
          userName: `User ${(index + 1) % 5}`,
          userAvatar: (index + 1) % 3 === 0 ? `https://i.pravatar.cc/150?u=user-${(index + 1) % 5}` : undefined,
          createdAt: new Date(Date.now() - Math.random() * 500000000).toISOString(),
          likes: Math.floor(Math.random() * 10),
          liked: Math.random() > 0.7,
          replyCount: 0,
          isEdited: Math.random() > 0.8,
          isDeleted: Math.random() > 0.95,
          isAuthor: Math.random() > 0.7,
        }
      ] : undefined,
    }));
    
    return {
      comments: mockComments,
      totalCount: 100, // Mock total count
    };
  },
  
  addComment: async (
    entityId: string,
    entityType: string,
    content: string,
    attachments?: File[]
  ): Promise<Comment> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock response
    return {
      id: `comment-new-${Date.now()}`,
      content,
      userId: 'current-user',
      userName: 'Current User',
      userAvatar: 'https://i.pravatar.cc/150?u=current-user',
      createdAt: new Date().toISOString(),
      likes: 0,
      liked: false,
      replyCount: 0,
      isEdited: false,
      isDeleted: false,
      isAuthor: true,
    };
  },
  
  likeComment: async (commentId: string): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
  },
  
  replyToComment: async (
    commentId: string,
    content: string,
    attachments?: File[]
  ): Promise<Comment> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock response
    return {
      id: `reply-new-${Date.now()}`,
      content,
      userId: 'current-user',
      userName: 'Current User',
      userAvatar: 'https://i.pravatar.cc/150?u=current-user',
      createdAt: new Date().toISOString(),
      likes: 0,
      liked: false,
      replyCount: 0,
      isEdited: false,
      isDeleted: false,
      isAuthor: true,
    };
  },
  
  editComment: async (
    commentId: string,
    content: string,
    attachments?: File[]
  ): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
  },
  
  deleteComment: async (commentId: string): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
  },
  
  reportComment: async (commentId: string, reason: string): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
  },
};

export function useComments({
  entityId,
  entityType,
  pageSize = 10,
  initialComments = [],
}: UseCommentsOptions): UseCommentsResult {
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  
  // Fetch comments
  const fetchComments = useCallback(async (pageNum: number, replace: boolean = false) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { comments: newComments, totalCount: newTotalCount } = await api.getComments(
        entityId,
        entityType,
        pageNum,
        pageSize
      );
      
      setTotalCount(newTotalCount);
      
      if (replace) {
        setComments(newComments);
      } else {
        setComments(prevComments => [...prevComments, ...newComments]);
      }
      
      setHasMore(newComments.length === pageSize);
    } catch (err) {
      setError('Failed to load comments. Please try again.');
      console.error('Error fetching comments:', err);
    } finally {
      setIsLoading(false);
    }
  }, [entityId, entityType, pageSize]);
  
  // Initial fetch
  useEffect(() => {
    if (initialComments.length === 0) {
      fetchComments(1, true);
    } else {
      setIsLoading(false);
    }
  }, [fetchComments, initialComments.length]);
  
  // Add comment
  const addComment = async (content: string, attachments?: File[]) => {
    try {
      const newComment = await api.addComment(entityId, entityType, content, attachments);
      
      setComments(prevComments => [newComment, ...prevComments]);
      setTotalCount(prevCount => prevCount + 1);
    } catch (err) {
      console.error('Error adding comment:', err);
      throw err;
    }
  };
  
  // Like comment
  const likeComment = async (commentId: string) => {
    try {
      await api.likeComment(commentId);
      
      setComments(prevComments => 
        prevComments.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              liked: !comment.liked,
              likes: comment.liked ? comment.likes - 1 : comment.likes + 1,
            };
          }
          
          // Check in replies
          if (comment.replies) {
            return {
              ...comment,
              replies: comment.replies.map(reply => {
                if (reply.id === commentId) {
                  return {
                    ...reply,
                    liked: !reply.liked,
                    likes: reply.liked ? reply.likes - 1 : reply.likes + 1,
                  };
                }
                return reply;
              }),
            };
          }
          
          return comment;
        })
      );
    } catch (err) {
      console.error('Error liking comment:', err);
      throw err;
    }
  };
  
  // Reply to comment
  const replyToComment = async (commentId: string, content: string, attachments?: File[]) => {
    try {
      const newReply = await api.replyToComment(commentId, content, attachments);
      
      setComments(prevComments => 
        prevComments.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              replies: comment.replies ? [...comment.replies, newReply] : [newReply],
              replyCount: comment.replyCount + 1,
            };
          }
          return comment;
        })
      );
    } catch (err) {
      console.error('Error replying to comment:', err);
      throw err;
    }
  };
  
  // Edit comment
  const editComment = async (commentId: string, content: string, attachments?: File[]) => {
    try {
      await api.editComment(commentId, content, attachments);
      
      setComments(prevComments => 
        prevComments.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              content,
              isEdited: true,
            };
          }
          
          // Check in replies
          if (comment.replies) {
            return {
              ...comment,
              replies: comment.replies.map(reply => {
                if (reply.id === commentId) {
                  return {
                    ...reply,
                    content,
                    isEdited: true,
                  };
                }
                return reply;
              }),
            };
          }
          
          return comment;
        })
      );
    } catch (err) {
      console.error('Error editing comment:', err);
      throw err;
    }
  };
  
  // Delete comment
  const deleteComment = async (commentId: string) => {
    try {
      await api.deleteComment(commentId);
      
      setComments(prevComments => 
        prevComments.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              content: '',
              isDeleted: true,
            };
          }
          
          // Check in replies
          if (comment.replies) {
            return {
              ...comment,
              replies: comment.replies.map(reply => {
                if (reply.id === commentId) {
                  return {
                    ...reply,
                    content: '',
                    isDeleted: true,
                  };
                }
                return reply;
              }),
            };
          }
          
          return comment;
        })
      );
    } catch (err) {
      console.error('Error deleting comment:', err);
      throw err;
    }
  };
  
  // Report comment
  const reportComment = async (commentId: string, reason: string) => {
    try {
      await api.reportComment(commentId, reason);
    } catch (err) {
      console.error('Error reporting comment:', err);
      throw err;
    }
  };
  
  // Load more comments
  const loadMoreComments = async () => {
    if (!hasMore || isLoading) return;
    
    const nextPage = page + 1;
    await fetchComments(nextPage);
    setPage(nextPage);
  };
  
  // Refresh comments
  const refreshComments = async () => {
    setPage(1);
    await fetchComments(1, true);
  };
  
  return {
    comments,
    totalCount,
    isLoading,
    error,
    addComment,
    likeComment,
    replyToComment,
    editComment,
    deleteComment,
    reportComment,
    loadMoreComments,
    refreshComments,
    hasMoreComments: hasMore,
  };
}
