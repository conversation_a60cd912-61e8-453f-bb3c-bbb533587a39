import axios from 'axios';
import type { Post } from '@/types';

// In a real app, this would be an environment variable;
const API_URL = 'https://api.example.com';

export const api = axios.create({
  baseURL: API_URL
});

// For demo purposes, we'll simulate API calls;
const DEMO_POSTS: Post[] = [
  {
    id: '1',
    user: {
      id: '1',
      username: 'yogamaster',
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400',
      followersCount: 1234,
      followingCount: 567,
      isFollowing: false
    },
    videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-woman-doing-yoga-on-beach-4179-large.mp4',
    caption: 'Morning yoga routine for inner peace ✨',
    hashtags: ['#yoga', '#wellness', '#mindfulness'],
    likes: 1234,
    comments: 89,
    hasLiked: false,
    createdAt: new Date().toISOString()
  },
  {
    id: '2',
    user: {
      id: '2',
      username: 'meditationguide',
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      followersCount: 2345,
      followingCount: 432,
      isFollowing: true
    },
    videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-young-woman-meditating-in-a-fitness-studio-43067-large.mp4',
    caption: 'Find your inner peace with this 5-minute meditation 🧘‍♀️',
    hashtags: ['#meditation', '#mindfulness', '#peace'],
    likes: 2345,
    comments: 123,
    hasLiked: true,
    createdAt: new Date().toISOString()
  }
];

export async function fetchPosts(): Promise<Post[]> {
  // Simulate API delay;
  await new Promise(resolve => setTimeout(resolve, 1000));
  return DEMO_POSTS;
}

export async function likePost(postId: string): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 500))
}

export async function unlikePost(postId: string): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 500))
}