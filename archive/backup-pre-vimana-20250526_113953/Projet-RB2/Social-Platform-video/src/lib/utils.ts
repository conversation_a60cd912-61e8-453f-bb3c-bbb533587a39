import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { formatDistanceToNowStrict } from 'date-fns';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatRelativeTime(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
      // console.error("Invalid date passed to formatRelativeTime:", date);
      return "Invalid date";
    }
    return formatDistanceToNowStrict(dateObj, { addSuffix: true });
  } catch (error) {
    // console.error("Error in formatRelativeTime:", error);
    return "Date error";
  }
}
