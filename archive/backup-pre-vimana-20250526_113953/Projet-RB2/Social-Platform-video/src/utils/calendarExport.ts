import { ScheduledPost } from '../api/contentApi';
import { format } from 'date-fns';

/**
 * Generate a unique ID for iCalendar events
 */
function generateUID(): string {
  return 'event-' + Math.random().toString(36).substring(2, 11);
}

/**
 * Format a date for iCalendar (YYYYMMDDTHHmmssZ)
 */
function formatICalDate(date: Date): string {
  return format(date, "yyyyMMdd'T'HHmmss'Z'");
}

/**
 * Escape special characters in iCalendar text
 */
function escapeICalText(text: string): string {
  return text
    .replace(/\\/g, '\\\\')
    .replace(/;/g, '\\;')
    .replace(/,/g, '\\,')
    .replace(/\n/g, '\\n');
}

/**
 * Generate iCalendar content for scheduled posts
 */
export function generateICalendar(scheduledItems: ScheduledPost[]): string {
  const now = new Date();
  const calendarStart = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Retreat And Be//Social Platform Video//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:PUBLISH',
    `X-WR-CALNAME:Retreat And Be Content Calendar`,
    `X-WR-CALDESC:Scheduled content from Retreat And Be platform`,
  ].join('\r\n');

  const calendarEnd = 'END:VCALENDAR';

  const events = scheduledItems.map(item => {
    const startDate = new Date(item.scheduledDate);
    // End date is 30 minutes after start for display purposes
    const endDate = new Date(startDate.getTime() + 30 * 60 * 1000);
    
    const recurrenceRule = item.recurrence ? generateRecurrenceRule(item.recurrence) : '';
    
    return [
      'BEGIN:VEVENT',
      `UID:${generateUID()}`,
      `DTSTAMP:${formatICalDate(now)}`,
      `DTSTART:${formatICalDate(startDate)}`,
      `DTEND:${formatICalDate(endDate)}`,
      `SUMMARY:${escapeICalText(item.title)}`,
      item.description ? `DESCRIPTION:${escapeICalText(item.description)}` : '',
      `CATEGORIES:${item.type.toUpperCase()}`,
      recurrenceRule,
      'END:VEVENT'
    ].filter(Boolean).join('\r\n');
  });

  return [calendarStart, ...events, calendarEnd].join('\r\n');
}

/**
 * Generate recurrence rule for iCalendar
 */
function generateRecurrenceRule(recurrence: any): string {
  if (!recurrence || !recurrence.pattern) {
    return '';
  }

  let rrule = 'RRULE:FREQ=';

  switch (recurrence.pattern) {
    case 'daily':
      rrule += 'DAILY';
      break;
    case 'weekly':
      rrule += 'WEEKLY';
      if (recurrence.daysOfWeek && recurrence.daysOfWeek.length > 0) {
        const days = recurrence.daysOfWeek.map((day: number) => {
          const dayNames = ['SU', 'MO', 'TU', 'WE', 'TH', 'FR', 'SA'];
          return dayNames[day];
        });
        rrule += `;BYDAY=${days.join(',')}`;
      }
      break;
    case 'monthly':
      rrule += 'MONTHLY';
      if (recurrence.dayOfMonth) {
        rrule += `;BYMONTHDAY=${recurrence.dayOfMonth}`;
      }
      break;
    default:
      return '';
  }

  if (recurrence.interval && recurrence.interval > 1) {
    rrule += `;INTERVAL=${recurrence.interval}`;
  }

  if (recurrence.occurrences) {
    rrule += `;COUNT=${recurrence.occurrences}`;
  } else if (recurrence.endDate) {
    const endDate = new Date(recurrence.endDate);
    rrule += `;UNTIL=${formatICalDate(endDate)}`;
  }

  return rrule;
}

/**
 * Download iCalendar file
 */
export function downloadICalendar(scheduledItems: ScheduledPost[]): void {
  const icalContent = generateICalendar(scheduledItems);
  const blob = new Blob([icalContent], { type: 'text/calendar;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', 'content-calendar.ics');
  document.body.appendChild(link);
  link.click();
  
  // Clean up
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
