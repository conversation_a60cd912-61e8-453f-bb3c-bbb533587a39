# Service de Gestion du Cache

Le microservice Social-Platform-Video intègre un système de cache avancé pour optimiser les performances et réduire la charge sur les services backend.

## Architecture du Cache

Le service de cache est conçu avec une architecture à plusieurs niveaux:

1. **Cache en mémoire**: Pour un accès ultra-rapide aux données fréquemment utilisées
2. **Invalidation par tags**: Pour une gestion précise du cycle de vie des données en cache
3. **Niveaux de TTL**: Différentes durées de vie selon le type de données
4. **Statistiques de performance**: Pour surveiller l'efficacité du cache

## Niveaux de Cache

Le service définit plusieurs niveaux de cache avec des durées de vie (TTL) différentes:

| Niveau | Description | TTL par défaut |
|--------|-------------|----------------|
| MICRO | Données très volatiles | 10 secondes |
| FREQUENT | Données qui changent fréquemment | 5 minutes |
| STANDARD | Données avec une durée de vie moyenne | 1 heure |
| STABLE | Données qui changent rarement | 6 heures |
| REFERENCE | Données de référence | 24 heures |

## Intégration avec les Services

Le cache est intégré avec les services suivants:

### LivestreamService

- Liste des livestreams (avec filtres)
- Détails des livestreams individuels
- Messages des livestreams

### BlogService

- Liste des articles de blog (avec filtres)
- Détails des articles individuels
- Commentaires des articles

## Middleware API

Le service fournit également un middleware Express pour la mise en cache automatique des réponses API:

```javascript
// Exemple d'utilisation du middleware de cache
app.use('/api/livestreams', livestreamCacheMiddleware, livestreamRoutes);
app.use('/api/blog', blogCacheMiddleware, blogRoutes);
```

## Configuration

Le service de cache peut être configuré via des variables d'environnement:

```
# Taille maximale du cache (nombre d'entrées)
CACHE_MAX_SIZE=1000

# Durées de vie par niveau (en secondes)
CACHE_TTL_MICRO=10
CACHE_TTL_FREQUENT=300
CACHE_TTL_STANDARD=3600
CACHE_TTL_STABLE=21600
CACHE_TTL_REFERENCE=86400
```

## Utilisation dans le Code

### Exemple d'utilisation simple

```javascript
const { cacheService } = require('./services/cache');

// Stocker une valeur dans le cache
cacheService.set('user:123', userData, { ttl: 3600 });

// Récupérer une valeur du cache
const user = cacheService.get('user:123');

// Récupérer ou calculer une valeur
const data = await cacheService.getOrSet(
  'api:endpoint',
  async () => {
    // Fonction coûteuse à mettre en cache
    return await fetchDataFromApi();
  },
  { tier: CacheTier.STANDARD }
);
```

### Utilisation avec l'adaptateur de service

```javascript
const CacheAdapter = require('./services/cache/CacheAdapter');

// Wrapper une méthode de service avec mise en cache
const cachedMethod = CacheAdapter.wrapServiceMethod(
  originalMethod,
  {
    keyPrefix: 'service:method',
    tier: CacheTier.STANDARD,
    tags: ['service', 'method']
  }
);
```

## Statistiques et Monitoring

Le service de cache fournit des statistiques en temps réel:

- Taux de succès (hit rate)
- Nombre d'opérations de lecture/écriture
- Temps moyen d'accès
- Nombre d'invalidations

Ces statistiques peuvent être utilisées pour surveiller les performances du cache et ajuster sa configuration.
