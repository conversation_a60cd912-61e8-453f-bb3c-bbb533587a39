apiVersion: apps/v1
kind: Deployment
metadata:
  name: social-platform-video-service
  labels:
    app: social-platform-video
spec:
  replicas: 2
  selector:
    matchLabels:
      app: social-platform-video
  template:
    metadata:
      labels:
        app: social-platform-video
    spec:
      containers:
      - name: social-platform-video
        image: social-platform-video-service:latest
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: "1"
            memory: 512Mi
          requests:
            cpu: "0.5"
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
