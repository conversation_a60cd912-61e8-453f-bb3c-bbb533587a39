# Plan d'optimisation SEO

Ce document présente le plan d'optimisation SEO pour la fusion des frontends original (RandBeFrontend) et nouveau (frontend-refonte).

## Objectifs

- Améliorer la visibilité du site dans les moteurs de recherche
- Augmenter le trafic organique
- Améliorer l'expérience utilisateur pour les visiteurs provenant des moteurs de recherche
- Optimiser le contenu pour les mots-clés pertinents
- Améliorer les métriques Core Web Vitals

## Stratégies d'optimisation

### 1. Optimisation technique

#### Métadonnées

Mettre en place des métadonnées appropriées pour chaque page.

```jsx
// Utilisation de React Helmet pour gérer les métadonnées
import { Helmet } from 'react-helmet-async';

const RetreatPage = ({ retreat }) => {
  return (
    <>
      <Helmet>
        <title>{retreat.title} | Retreat And Be</title>
        <meta name="description" content={retreat.shortDescription} />
        <meta name="keywords" content={`retraite, yoga, méditation, ${retreat.tags.join(', ')}`} />
        <link rel="canonical" href={`https://retreatandbe.com/retreats/${retreat.slug}`} />
        <meta property="og:title" content={`${retreat.title} | Retreat And Be`} />
        <meta property="og:description" content={retreat.shortDescription} />
        <meta property="og:image" content={retreat.coverImage} />
        <meta property="og:url" content={`https://retreatandbe.com/retreats/${retreat.slug}`} />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>
      {/* Contenu de la page */}
    </>
  );
};
```

#### Structure des URL

Créer une structure d'URL claire et descriptive.

```
// Structure d'URL recommandée
https://retreatandbe.com/retreats/categories/yoga
https://retreatandbe.com/retreats/location/france/provence
https://retreatandbe.com/blog/meditation-techniques-for-beginners
```

#### Plan du site

Générer un sitemap.xml pour aider les moteurs de recherche à indexer le site.

```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://retreatandbe.com/</loc>
    <lastmod>2023-05-15</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://retreatandbe.com/retreats</loc>
    <lastmod>2023-05-15</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
  <!-- Autres URLs -->
</urlset>
```

#### Fichier robots.txt

Créer un fichier robots.txt pour guider les robots des moteurs de recherche.

```
User-agent: *
Allow: /

Disallow: /admin/
Disallow: /private/
Disallow: /api/

Sitemap: https://retreatandbe.com/sitemap.xml
```

### 2. Optimisation du contenu

#### Balises de titre

Utiliser des balises de titre hiérarchiques et descriptives.

```jsx
// Structure hiérarchique des titres
const RetreatPage = ({ retreat }) => {
  return (
    <div>
      <h1>{retreat.title}</h1>
      <section>
        <h2>À propos de cette retraite</h2>
        <p>{retreat.description}</p>
      </section>
      <section>
        <h2>Programme</h2>
        {retreat.program.map((day, index) => (
          <div key={index}>
            <h3>Jour {day.day}: {day.title}</h3>
            <p>{day.description}</p>
          </div>
        ))}
      </section>
      <section>
        <h2>Hébergement</h2>
        <p>{retreat.accommodation}</p>
      </section>
    </div>
  );
};
```

#### Contenu riche

Utiliser des données structurées pour améliorer la présentation dans les résultats de recherche.

```jsx
// Données structurées pour une retraite
import { Helmet } from 'react-helmet-async';

const RetreatPage = ({ retreat }) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Event",
    "name": retreat.title,
    "description": retreat.description,
    "image": retreat.coverImage,
    "startDate": retreat.startDate,
    "endDate": retreat.endDate,
    "location": {
      "@type": "Place",
      "name": retreat.location.name,
      "address": {
        "@type": "PostalAddress",
        "addressLocality": retreat.location.city,
        "addressRegion": retreat.location.region,
        "addressCountry": retreat.location.country
      }
    },
    "organizer": {
      "@type": "Organization",
      "name": retreat.organizer.name,
      "url": retreat.organizer.website
    },
    "offers": {
      "@type": "Offer",
      "price": retreat.price,
      "priceCurrency": "EUR",
      "availability": retreat.availability,
      "url": `https://retreatandbe.com/retreats/${retreat.slug}`
    }
  };

  return (
    <>
      <Helmet>
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>
      {/* Contenu de la page */}
    </>
  );
};
```

#### Optimisation des images

Optimiser les images pour le SEO.

```jsx
// Images optimisées pour le SEO
const OptimizedImage = ({ src, alt, width, height }) => {
  return (
    <img 
      src={src} 
      alt={alt} 
      width={width} 
      height={height} 
      loading="lazy" 
    />
  );
};

// Utilisation
<OptimizedImage 
  src="/images/yoga-retreat.jpg" 
  alt="Retraite de yoga dans les montagnes de Provence" 
  width={800} 
  height={600} 
/>
```

### 3. Optimisation mobile

#### Responsive design

S'assurer que le site est parfaitement adapté aux appareils mobiles.

```css
/* Styles responsive */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
  
  .hero-section {
    height: auto;
    min-height: 50vh;
  }
}
```

#### AMP (Accelerated Mobile Pages)

Envisager l'utilisation d'AMP pour les pages de contenu.

```jsx
// Exemple de page AMP
const AMPBlogPost = ({ post }) => {
  return (
    <html amp lang="fr">
      <head>
        <meta charSet="utf-8" />
        <script async src="https://cdn.ampproject.org/v0.js"></script>
        <title>{post.title}</title>
        <link rel="canonical" href={`https://retreatandbe.com/blog/${post.slug}`} />
        <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1" />
        <style amp-boilerplate>{`body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`}</style>
        <noscript>
          <style amp-boilerplate>{`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}</style>
        </noscript>
        <style amp-custom>{`
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 1rem;
          }
          h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
          }
          img {
            max-width: 100%;
            height: auto;
          }
        `}</style>
      </head>
      <body>
        <h1>{post.title}</h1>
        <p>{post.date}</p>
        <div dangerouslySetInnerHTML={{ __html: post.content }} />
      </body>
    </html>
  );
};
```

### 4. Optimisation des performances

#### Core Web Vitals

Optimiser les Core Web Vitals pour améliorer le classement dans les moteurs de recherche.

```jsx
// Chargement différé des images
import { useEffect, useRef, useState } from 'react';

const LazyImage = ({ src, alt, width, height }) => {
  const imgRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setIsVisible(true);
        observer.disconnect();
      }
    });
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => {
      observer.disconnect();
    };
  }, []);
  
  return (
    <div 
      ref={imgRef} 
      style={{ width, height, background: '#f0f0f0' }}
    >
      {isVisible && (
        <img 
          src={src} 
          alt={alt} 
          width={width} 
          height={height} 
          loading="lazy" 
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
      )}
    </div>
  );
};
```

#### Préchargement des ressources critiques

Précharger les ressources critiques pour améliorer les performances.

```jsx
// Préchargement des ressources critiques
import { Helmet } from 'react-helmet-async';

const HomePage = () => {
  return (
    <>
      <Helmet>
        <link rel="preload" href="/fonts/main-font.woff2" as="font" type="font/woff2" crossorigin />
        <link rel="preload" href="/images/hero-image.webp" as="image" />
        <link rel="preconnect" href="https://api.retreatandbe.com" />
      </Helmet>
      {/* Contenu de la page */}
    </>
  );
};
```

### 5. Optimisation locale

#### SEO local

Optimiser le site pour les recherches locales.

```jsx
// Données structurées pour une entreprise locale
import { Helmet } from 'react-helmet-async';

const ContactPage = () => {
  const localBusinessData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Retreat And Be",
    "image": "https://retreatandbe.com/images/logo.png",
    "url": "https://retreatandbe.com",
    "telephone": "+***********",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Rue de la Paix",
      "addressLocality": "Paris",
      "postalCode": "75001",
      "addressCountry": "FR"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 48.8566,
      "longitude": 2.3522
    },
    "openingHoursSpecification": [
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        "opens": "09:00",
        "closes": "18:00"
      }
    ]
  };

  return (
    <>
      <Helmet>
        <script type="application/ld+json">
          {JSON.stringify(localBusinessData)}
        </script>
      </Helmet>
      {/* Contenu de la page */}
    </>
  );
};
```

## Plan d'implémentation

1. **Audit initial**
   - Exécuter un audit SEO complet (Lighthouse, SEMrush, Ahrefs)
   - Identifier les problèmes SEO existants
   - Analyser les mots-clés pertinents

2. **Optimisation technique**
   - Mettre en place les métadonnées pour toutes les pages
   - Optimiser la structure des URL
   - Générer un sitemap.xml
   - Créer un fichier robots.txt

3. **Optimisation du contenu**
   - Optimiser les balises de titre
   - Mettre en place des données structurées
   - Optimiser les images pour le SEO

4. **Optimisation mobile**
   - Vérifier et améliorer le responsive design
   - Envisager l'utilisation d'AMP pour les pages de contenu

5. **Optimisation des performances**
   - Optimiser les Core Web Vitals
   - Mettre en place le préchargement des ressources critiques

6. **Optimisation locale**
   - Mettre en place le SEO local pour les pages pertinentes

7. **Mesure et itération**
   - Mesurer l'impact des optimisations
   - Itérer et améliorer continuellement

## Outils de mesure

- **Google Search Console** : Pour surveiller la présence dans Google
- **Google Analytics** : Pour analyser le trafic
- **Lighthouse** : Pour mesurer les performances et le SEO
- **SEMrush / Ahrefs** : Pour l'analyse des mots-clés et le suivi des positions

## Conclusion

Ce plan d'optimisation SEO permettra d'améliorer la visibilité du site dans les moteurs de recherche et d'augmenter le trafic organique. Les optimisations seront mises en œuvre progressivement et mesurées à chaque étape pour s'assurer qu'elles ont l'impact souhaité.
