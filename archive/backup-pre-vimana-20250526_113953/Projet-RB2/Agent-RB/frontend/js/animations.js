/**
 * Fonctions pour gérer les animations.
 */

/**
 * Initialise les animations au défilement.
 * 
 * @param {string} selector - Sélecteur CSS pour les éléments à animer
 * @param {string} visibleClass - Classe à ajouter lorsque l'élément est visible
 * @param {number} threshold - Seuil de visibilité (0 à 1)
 * @param {number} rootMargin - Marge autour de la zone de visibilité (ex: "0px 0px -100px 0px")
 */
export function initScrollAnimations(
  selector = '.scroll-fade-in, .scroll-slide-in-left, .scroll-slide-in-right, .scroll-zoom-in, .stagger-fade-in',
  visibleClass = 'visible',
  threshold = 0.1,
  rootMargin = '0px 0px -100px 0px'
) {
  // Vérifier si l'API IntersectionObserver est disponible
  if (!('IntersectionObserver' in window)) {
    // Fallback pour les navigateurs qui ne supportent pas IntersectionObserver
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      element.classList.add(visibleClass);
    });
    return;
  }
  
  // Créer l'observateur
  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add(visibleClass);
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold,
    rootMargin
  });
  
  // Observer les éléments
  const elements = document.querySelectorAll(selector);
  elements.forEach(element => {
    observer.observe(element);
  });
}

/**
 * Ajoute une animation à un élément.
 * 
 * @param {HTMLElement} element - Élément à animer
 * @param {string} animationClass - Classe d'animation à ajouter
 * @param {boolean} removeAfter - Si l'animation doit être supprimée après son exécution
 * @param {Function} callback - Fonction à exécuter après l'animation
 */
export function addAnimation(element, animationClass, removeAfter = true, callback = null) {
  // Supprimer les animations existantes
  element.classList.remove(
    'animate-fade-in',
    'animate-fade-out',
    'animate-slide-in-left',
    'animate-slide-in-right',
    'animate-slide-in-up',
    'animate-slide-in-down',
    'animate-slide-out-left',
    'animate-slide-out-right',
    'animate-slide-out-up',
    'animate-slide-out-down',
    'animate-zoom-in',
    'animate-zoom-out',
    'animate-pulse',
    'animate-shake',
    'animate-bounce',
    'animate-spin'
  );
  
  // Forcer un reflow pour réinitialiser l'animation
  void element.offsetWidth;
  
  // Ajouter la nouvelle animation
  element.classList.add(animationClass);
  
  // Supprimer l'animation après son exécution
  if (removeAfter) {
    const animationDuration = getComputedStyle(element).animationDuration;
    const duration = parseFloat(animationDuration) * 1000;
    
    setTimeout(() => {
      element.classList.remove(animationClass);
      
      if (callback) {
        callback();
      }
    }, duration);
  }
}

/**
 * Initialise les animations de page.
 * 
 * @param {string} selector - Sélecteur CSS pour les éléments à animer
 * @param {string} activeClass - Classe à ajouter lorsque la page est active
 */
export function initPageTransitions(selector = '.page-transition-fade, .page-transition-slide, .page-transition-zoom', activeClass = 'active') {
  const elements = document.querySelectorAll(selector);
  
  // Ajouter la classe active après un court délai
  setTimeout(() => {
    elements.forEach(element => {
      element.classList.add(activeClass);
    });
  }, 100);
  
  // Gérer les transitions de page lors de la navigation
  document.addEventListener('click', event => {
    const link = event.target.closest('a');
    
    if (link && link.getAttribute('href').startsWith('/') && !link.hasAttribute('data-no-transition')) {
      event.preventDefault();
      
      // Supprimer la classe active
      elements.forEach(element => {
        element.classList.remove(activeClass);
      });
      
      // Attendre la fin de la transition
      setTimeout(() => {
        window.location.href = link.getAttribute('href');
      }, 500);
    }
  });
}

/**
 * Initialise les animations au survol.
 * 
 * @param {string} selector - Sélecteur CSS pour les éléments à animer
 */
export function initHoverAnimations(selector = '.hover-scale, .hover-shadow, .hover-lift, .hover-rotate, .hover-brightness') {
  const elements = document.querySelectorAll(selector);
  
  elements.forEach(element => {
    // Ajouter la transition
    element.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease, filter 0.3s ease';
  });
}

/**
 * Crée une animation de chargement.
 * 
 * @param {string} type - Type d'animation (spinner, dots, pulse, bar)
 * @param {HTMLElement} container - Conteneur où ajouter l'animation
 * @returns {HTMLElement} - Élément d'animation
 */
export function createLoadingAnimation(type = 'spinner', container = null) {
  let loadingElement;
  
  switch (type) {
    case 'spinner':
      loadingElement = document.createElement('div');
      loadingElement.className = 'loading-spinner';
      break;
    
    case 'dots':
      loadingElement = document.createElement('div');
      loadingElement.className = 'loading-dots';
      loadingElement.textContent = 'Chargement';
      break;
    
    case 'pulse':
      loadingElement = document.createElement('div');
      loadingElement.className = 'loading-pulse';
      break;
    
    case 'bar':
      loadingElement = document.createElement('div');
      loadingElement.className = 'loading-bar';
      break;
    
    default:
      loadingElement = document.createElement('div');
      loadingElement.className = 'loading-spinner';
      break;
  }
  
  // Ajouter l'animation au conteneur
  if (container) {
    container.appendChild(loadingElement);
  }
  
  return loadingElement;
}

/**
 * Initialise les animations au chargement de la page.
 */
export function initAnimations() {
  // Initialiser les animations au défilement
  initScrollAnimations();
  
  // Initialiser les animations de page
  initPageTransitions();
  
  // Initialiser les animations au survol
  initHoverAnimations();
  
  // Ajouter une animation de fade-in au body
  document.body.classList.add('animate-fade-in');
}

// Initialiser les animations au chargement de la page
document.addEventListener('DOMContentLoaded', initAnimations);
