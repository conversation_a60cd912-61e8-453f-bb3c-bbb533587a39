/**
 * Module d'internationalisation pour le frontend.
 */

// Langue par défaut
let currentLanguage = 'fr';

// Traductions
let translations = {};

/**
 * Initialise le module d'internationalisation.
 * 
 * @param {string} language - Langue à utiliser
 * @returns {Promise} - Promise résolue lorsque les traductions sont chargées
 */
export async function init(language = 'fr') {
  // Définir la langue
  currentLanguage = language;
  
  // Charger les traductions
  try {
    const response = await fetch(`/api/i18n/translations/${language}`);
    const data = await response.json();
    
    if (data.status === 'success') {
      translations = data.translations;
      return true;
    } else {
      console.error('Erreur lors du chargement des traductions:', data.message);
      return false;
    }
  } catch (error) {
    console.error('Erreur lors du chargement des traductions:', error);
    return false;
  }
}

/**
 * Définit la langue à utiliser.
 * 
 * @param {string} language - Langue à utiliser
 * @returns {Promise} - Promise résolue lorsque les traductions sont chargées
 */
export async function setLanguage(language) {
  // Stocker la langue dans le localStorage
  localStorage.setItem('language', language);
  
  // Initialiser le module
  return init(language);
}

/**
 * Récupère la langue actuelle.
 * 
 * @returns {string} - Langue actuelle
 */
export function getLanguage() {
  return currentLanguage;
}

/**
 * Traduit une clé dans la langue actuelle.
 * 
 * @param {string} key - Clé de traduction
 * @param {Object} params - Paramètres de formatage (optionnel)
 * @returns {string} - Texte traduit
 */
export function t(key, params = {}) {
  // Récupérer la traduction
  let translation = translations[key];
  
  // Si la traduction n'existe pas, utiliser la clé
  if (!translation) {
    console.warn(`Traduction non trouvée pour la clé: ${key}`);
    return key;
  }
  
  // Formater la traduction avec les paramètres
  if (params && Object.keys(params).length > 0) {
    Object.keys(params).forEach(param => {
      translation = translation.replace(new RegExp(`{${param}}`, 'g'), params[param]);
    });
  }
  
  return translation;
}

/**
 * Applique les traductions aux éléments HTML avec l'attribut data-i18n.
 */
export function applyTranslations() {
  // Parcourir tous les éléments avec l'attribut data-i18n
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.getAttribute('data-i18n');
    const translation = t(key);
    
    // Appliquer la traduction
    element.textContent = translation;
  });
  
  // Parcourir tous les éléments avec l'attribut data-i18n-placeholder
  document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
    const key = element.getAttribute('data-i18n-placeholder');
    const translation = t(key);
    
    // Appliquer la traduction au placeholder
    element.setAttribute('placeholder', translation);
  });
  
  // Parcourir tous les éléments avec l'attribut data-i18n-title
  document.querySelectorAll('[data-i18n-title]').forEach(element => {
    const key = element.getAttribute('data-i18n-title');
    const translation = t(key);
    
    // Appliquer la traduction au title
    element.setAttribute('title', translation);
  });
  
  // Parcourir tous les éléments avec l'attribut data-i18n-value
  document.querySelectorAll('[data-i18n-value]').forEach(element => {
    const key = element.getAttribute('data-i18n-value');
    const translation = t(key);
    
    // Appliquer la traduction à la valeur
    element.setAttribute('value', translation);
  });
}

/**
 * Initialise le sélecteur de langue.
 * 
 * @param {string} selectorId - ID du sélecteur de langue
 */
export function initLanguageSelector(selectorId = 'language-selector') {
  // Récupérer le sélecteur de langue
  const selector = document.getElementById(selectorId);
  
  if (!selector) {
    console.warn(`Sélecteur de langue non trouvé: ${selectorId}`);
    return;
  }
  
  // Définir la valeur du sélecteur
  selector.value = currentLanguage;
  
  // Ajouter l'événement de changement de langue
  selector.addEventListener('change', async (event) => {
    const language = event.target.value;
    
    // Changer la langue
    await setLanguage(language);
    
    // Appliquer les traductions
    applyTranslations();
  });
}

/**
 * Récupère la liste des langues supportées.
 * 
 * @returns {Promise<Array>} - Promise résolue avec la liste des langues
 */
export async function getSupportedLanguages() {
  try {
    const response = await fetch('/api/i18n/languages');
    const data = await response.json();
    
    if (data.status === 'success') {
      return data.languages;
    } else {
      console.error('Erreur lors de la récupération des langues:', data.message);
      return [];
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des langues:', error);
    return [];
  }
}

/**
 * Initialise le module d'internationalisation au chargement de la page.
 */
document.addEventListener('DOMContentLoaded', async () => {
  // Récupérer la langue depuis le localStorage ou utiliser la langue par défaut
  const language = localStorage.getItem('language') || 'fr';
  
  // Initialiser le module
  await init(language);
  
  // Appliquer les traductions
  applyTranslations();
  
  // Initialiser le sélecteur de langue
  initLanguageSelector();
});
