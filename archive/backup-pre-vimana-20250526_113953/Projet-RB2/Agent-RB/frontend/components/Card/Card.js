/**
 * Composant Card réutilisable.
 */

/**
 * Crée une carte.
 * 
 * @param {Object} options - Options de la carte
 * @param {string} options.id - ID de la carte
 * @param {string} options.title - Titre de la carte
 * @param {string} options.content - Contenu de la carte
 * @param {string} options.footer - Pied de la carte
 * @param {string} options.image - URL de l'image
 * @param {string} options.imageAlt - Texte alternatif de l'image
 * @param {string} options.type - Type de la carte (default, primary, secondary, etc.)
 * @param {boolean} options.shadow - Si la carte a une ombre
 * @param {boolean} options.hover - Si la carte a un effet au survol
 * @param {Function} options.onClick - Fonction à exécuter au clic
 * @param {string} options.i18nTitleKey - Clé de traduction pour le titre
 * @param {string} options.i18nContentKey - Clé de traduction pour le contenu
 * @param {string} options.i18nFooterKey - Clé de traduction pour le pied
 * @returns {HTMLDivElement} - Élément div de la carte
 */
export function createCard({
  id,
  title,
  content,
  footer,
  image,
  imageAlt = '',
  type = 'default',
  shadow = true,
  hover = false,
  onClick = null,
  i18nTitleKey = null,
  i18nContentKey = null,
  i18nFooterKey = null
}) {
  // Créer la carte
  const card = document.createElement('div');
  
  // Définir l'ID
  if (id) {
    card.id = id;
  }
  
  // Définir les classes
  card.className = `card card-${type}`;
  
  if (shadow) {
    card.classList.add('card-shadow');
  }
  
  if (hover) {
    card.classList.add('card-hover');
  }
  
  // Ajouter l'image si spécifiée
  if (image) {
    const imageContainer = document.createElement('div');
    imageContainer.className = 'card-image';
    
    const img = document.createElement('img');
    img.src = image;
    img.alt = imageAlt;
    
    imageContainer.appendChild(img);
    card.appendChild(imageContainer);
  }
  
  // Ajouter le titre si spécifié
  if (title) {
    const titleElement = document.createElement('div');
    titleElement.className = 'card-title';
    
    if (i18nTitleKey) {
      titleElement.setAttribute('data-i18n', i18nTitleKey);
      titleElement.textContent = title;
    } else {
      titleElement.textContent = title;
    }
    
    card.appendChild(titleElement);
  }
  
  // Ajouter le contenu si spécifié
  if (content) {
    const contentElement = document.createElement('div');
    contentElement.className = 'card-content';
    
    if (i18nContentKey) {
      contentElement.setAttribute('data-i18n', i18nContentKey);
      contentElement.textContent = content;
    } else {
      contentElement.textContent = content;
    }
    
    card.appendChild(contentElement);
  }
  
  // Ajouter le pied si spécifié
  if (footer) {
    const footerElement = document.createElement('div');
    footerElement.className = 'card-footer';
    
    if (i18nFooterKey) {
      footerElement.setAttribute('data-i18n', i18nFooterKey);
      footerElement.textContent = footer;
    } else {
      footerElement.textContent = footer;
    }
    
    card.appendChild(footerElement);
  }
  
  // Ajouter l'événement de clic
  if (onClick) {
    card.addEventListener('click', onClick);
    card.style.cursor = 'pointer';
  }
  
  return card;
}

/**
 * Crée une grille de cartes.
 * 
 * @param {Object} options - Options de la grille
 * @param {string} options.id - ID de la grille
 * @param {Array} options.cards - Liste des options de cartes
 * @param {number} options.columns - Nombre de colonnes
 * @param {string} options.gap - Espacement entre les cartes
 * @returns {HTMLDivElement} - Élément div de la grille
 */
export function createCardGrid({
  id,
  cards = [],
  columns = 3,
  gap = '1rem'
}) {
  // Créer la grille
  const grid = document.createElement('div');
  
  // Définir l'ID
  if (id) {
    grid.id = id;
  }
  
  // Définir les classes et le style
  grid.className = 'card-grid';
  grid.style.display = 'grid';
  grid.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
  grid.style.gap = gap;
  
  // Ajouter les cartes
  cards.forEach(cardOptions => {
    const card = createCard(cardOptions);
    grid.appendChild(card);
  });
  
  return grid;
}

export default {
  createCard,
  createCardGrid
};
