/**
 * Composant Modal réutilisable.
 */

/**
 * Crée une modale.
 * 
 * @param {Object} options - Options de la modale
 * @param {string} options.id - ID de la modale
 * @param {string} options.title - Titre de la modale
 * @param {string|HTMLElement} options.content - Contenu de la modale
 * @param {Array} options.buttons - Boutons de la modale
 * @param {string} options.size - Taille de la modale (small, medium, large)
 * @param {boolean} options.closeOnEscape - Si la modale se ferme avec la touche Escape
 * @param {boolean} options.closeOnClickOutside - Si la modale se ferme en cliquant à l'extérieur
 * @param {boolean} options.showCloseButton - Si la modale affiche un bouton de fermeture
 * @param {Function} options.onOpen - Fonction à exécuter à l'ouverture
 * @param {Function} options.onClose - Fonction à exécuter à la fermeture
 * @param {string} options.i18nTitleKey - Clé de traduction pour le titre
 * @returns {Object} - Objet contenant la modale et les méthodes pour la manipuler
 */
export function createModal({
  id,
  title,
  content,
  buttons = [],
  size = 'medium',
  closeOnEscape = true,
  closeOnClickOutside = true,
  showCloseButton = true,
  onOpen = null,
  onClose = null,
  i18nTitleKey = null
}) {
  // Créer l'overlay
  const overlay = document.createElement('div');
  overlay.className = 'modal-overlay';
  
  // Créer la modale
  const modal = document.createElement('div');
  modal.id = id;
  modal.className = `modal modal-${size}`;
  
  // Créer l'en-tête
  const header = document.createElement('div');
  header.className = 'modal-header';
  
  // Créer le titre
  if (title) {
    const titleElement = document.createElement('h2');
    titleElement.className = 'modal-title';
    
    if (i18nTitleKey) {
      titleElement.setAttribute('data-i18n', i18nTitleKey);
      titleElement.textContent = title;
    } else {
      titleElement.textContent = title;
    }
    
    header.appendChild(titleElement);
  }
  
  // Créer le bouton de fermeture
  if (showCloseButton) {
    const closeButton = document.createElement('button');
    closeButton.className = 'modal-close';
    closeButton.innerHTML = '&times;';
    closeButton.setAttribute('aria-label', 'Close');
    
    closeButton.addEventListener('click', () => {
      close();
    });
    
    header.appendChild(closeButton);
  }
  
  modal.appendChild(header);
  
  // Créer le contenu
  const contentContainer = document.createElement('div');
  contentContainer.className = 'modal-content';
  
  if (typeof content === 'string') {
    contentContainer.textContent = content;
  } else {
    contentContainer.appendChild(content);
  }
  
  modal.appendChild(contentContainer);
  
  // Créer le pied
  if (buttons.length > 0) {
    const footer = document.createElement('div');
    footer.className = 'modal-footer';
    
    buttons.forEach(buttonOptions => {
      const button = document.createElement('button');
      button.type = 'button';
      button.className = `btn btn-${buttonOptions.type || 'primary'}`;
      
      if (buttonOptions.i18nKey) {
        button.setAttribute('data-i18n', buttonOptions.i18nKey);
        button.textContent = buttonOptions.text;
      } else {
        button.textContent = buttonOptions.text;
      }
      
      if (buttonOptions.onClick) {
        button.addEventListener('click', (event) => {
          buttonOptions.onClick(event, { close });
        });
      }
      
      footer.appendChild(button);
    });
    
    modal.appendChild(footer);
  }
  
  overlay.appendChild(modal);
  
  // Fonction pour ouvrir la modale
  function open() {
    document.body.appendChild(overlay);
    
    // Ajouter la classe pour l'animation
    setTimeout(() => {
      overlay.classList.add('modal-overlay-visible');
      modal.classList.add('modal-visible');
    }, 10);
    
    // Désactiver le défilement du body
    document.body.style.overflow = 'hidden';
    
    // Exécuter la fonction d'ouverture
    if (onOpen) {
      onOpen();
    }
    
    // Ajouter les événements
    if (closeOnEscape) {
      document.addEventListener('keydown', handleKeyDown);
    }
    
    if (closeOnClickOutside) {
      overlay.addEventListener('click', handleOverlayClick);
    }
  }
  
  // Fonction pour fermer la modale
  function close() {
    // Ajouter la classe pour l'animation
    overlay.classList.remove('modal-overlay-visible');
    modal.classList.remove('modal-visible');
    
    // Attendre la fin de l'animation
    setTimeout(() => {
      // Supprimer la modale
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
      
      // Réactiver le défilement du body
      document.body.style.overflow = '';
      
      // Exécuter la fonction de fermeture
      if (onClose) {
        onClose();
      }
    }, 300);
    
    // Supprimer les événements
    if (closeOnEscape) {
      document.removeEventListener('keydown', handleKeyDown);
    }
    
    if (closeOnClickOutside) {
      overlay.removeEventListener('click', handleOverlayClick);
    }
  }
  
  // Fonction pour gérer l'appui sur la touche Escape
  function handleKeyDown(event) {
    if (event.key === 'Escape') {
      close();
    }
  }
  
  // Fonction pour gérer le clic sur l'overlay
  function handleOverlayClick(event) {
    if (event.target === overlay) {
      close();
    }
  }
  
  // Fonction pour mettre à jour le contenu
  function setContent(newContent) {
    contentContainer.innerHTML = '';
    
    if (typeof newContent === 'string') {
      contentContainer.textContent = newContent;
    } else {
      contentContainer.appendChild(newContent);
    }
  }
  
  // Fonction pour mettre à jour le titre
  function setTitle(newTitle, newI18nTitleKey = null) {
    const titleElement = header.querySelector('.modal-title');
    
    if (titleElement) {
      if (newI18nTitleKey) {
        titleElement.setAttribute('data-i18n', newI18nTitleKey);
        titleElement.textContent = newTitle;
      } else {
        titleElement.removeAttribute('data-i18n');
        titleElement.textContent = newTitle;
      }
    }
  }
  
  return {
    modal,
    overlay,
    open,
    close,
    setContent,
    setTitle
  };
}

/**
 * Crée une modale de confirmation.
 * 
 * @param {Object} options - Options de la modale
 * @param {string} options.id - ID de la modale
 * @param {string} options.title - Titre de la modale
 * @param {string} options.message - Message de la modale
 * @param {string} options.confirmText - Texte du bouton de confirmation
 * @param {string} options.cancelText - Texte du bouton d'annulation
 * @param {string} options.confirmType - Type du bouton de confirmation
 * @param {Function} options.onConfirm - Fonction à exécuter à la confirmation
 * @param {Function} options.onCancel - Fonction à exécuter à l'annulation
 * @param {string} options.i18nTitleKey - Clé de traduction pour le titre
 * @param {string} options.i18nMessageKey - Clé de traduction pour le message
 * @param {string} options.i18nConfirmKey - Clé de traduction pour le bouton de confirmation
 * @param {string} options.i18nCancelKey - Clé de traduction pour le bouton d'annulation
 * @returns {Object} - Objet contenant la modale et les méthodes pour la manipuler
 */
export function createConfirmModal({
  id,
  title = 'Confirmation',
  message = 'Êtes-vous sûr de vouloir effectuer cette action ?',
  confirmText = 'Confirmer',
  cancelText = 'Annuler',
  confirmType = 'primary',
  onConfirm = null,
  onCancel = null,
  i18nTitleKey = 'common.confirm',
  i18nMessageKey = null,
  i18nConfirmKey = 'common.confirm',
  i18nCancelKey = 'common.cancel'
}) {
  // Créer le contenu
  const content = document.createElement('p');
  
  if (i18nMessageKey) {
    content.setAttribute('data-i18n', i18nMessageKey);
    content.textContent = message;
  } else {
    content.textContent = message;
  }
  
  // Créer les boutons
  const buttons = [
    {
      text: cancelText,
      type: 'secondary',
      i18nKey: i18nCancelKey,
      onClick: (event, { close }) => {
        close();
        
        if (onCancel) {
          onCancel();
        }
      }
    },
    {
      text: confirmText,
      type: confirmType,
      i18nKey: i18nConfirmKey,
      onClick: (event, { close }) => {
        close();
        
        if (onConfirm) {
          onConfirm();
        }
      }
    }
  ];
  
  // Créer la modale
  return createModal({
    id,
    title,
    content,
    buttons,
    size: 'small',
    i18nTitleKey
  });
}

/**
 * Crée une modale d'alerte.
 * 
 * @param {Object} options - Options de la modale
 * @param {string} options.id - ID de la modale
 * @param {string} options.title - Titre de la modale
 * @param {string} options.message - Message de la modale
 * @param {string} options.buttonText - Texte du bouton
 * @param {Function} options.onClose - Fonction à exécuter à la fermeture
 * @param {string} options.i18nTitleKey - Clé de traduction pour le titre
 * @param {string} options.i18nMessageKey - Clé de traduction pour le message
 * @param {string} options.i18nButtonKey - Clé de traduction pour le bouton
 * @returns {Object} - Objet contenant la modale et les méthodes pour la manipuler
 */
export function createAlertModal({
  id,
  title = 'Alerte',
  message = 'Une erreur est survenue.',
  buttonText = 'OK',
  onClose = null,
  i18nTitleKey = 'common.alert',
  i18nMessageKey = null,
  i18nButtonKey = 'common.ok'
}) {
  // Créer le contenu
  const content = document.createElement('p');
  
  if (i18nMessageKey) {
    content.setAttribute('data-i18n', i18nMessageKey);
    content.textContent = message;
  } else {
    content.textContent = message;
  }
  
  // Créer les boutons
  const buttons = [
    {
      text: buttonText,
      type: 'primary',
      i18nKey: i18nButtonKey,
      onClick: (event, { close }) => {
        close();
      }
    }
  ];
  
  // Créer la modale
  return createModal({
    id,
    title,
    content,
    buttons,
    size: 'small',
    onClose,
    i18nTitleKey
  });
}

export default {
  createModal,
  createConfirmModal,
  createAlertModal
};
