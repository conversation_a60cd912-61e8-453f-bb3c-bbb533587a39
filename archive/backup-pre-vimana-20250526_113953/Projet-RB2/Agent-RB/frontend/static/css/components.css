/**
 * Styles pour les composants réutilisables.
 */

/* Variables */
:root {
  /* Couleurs */
  --color-primary: #4a90e2;
  --color-primary-light: #6ba5e7;
  --color-primary-dark: #3a7bc8;
  --color-secondary: #6c757d;
  --color-secondary-light: #868e96;
  --color-secondary-dark: #495057;
  --color-success: #28a745;
  --color-success-light: #48c664;
  --color-success-dark: #1e7e34;
  --color-danger: #dc3545;
  --color-danger-light: #e25663;
  --color-danger-dark: #bd2130;
  --color-warning: #ffc107;
  --color-warning-light: #ffcd39;
  --color-warning-dark: #d39e00;
  --color-info: #17a2b8;
  --color-info-light: #1fc8e3;
  --color-info-dark: #117a8b;
  --color-light: #f8f9fa;
  --color-light-dark: #e9ecef;
  --color-dark: #343a40;
  --color-dark-light: #495057;
  --color-white: #ffffff;
  --color-black: #000000;
  --color-gray: #6c757d;
  --color-gray-light: #adb5bd;
  --color-gray-dark: #495057;
  
  /* Espacement */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  /* Typographie */
  --font-family-base: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-heading: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-base: 1rem;
  --font-size-sm: 0.875rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-xxl: 2rem;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-bold: 700;
  --line-height-base: 1.5;
  --line-height-sm: 1.25;
  --line-height-lg: 1.75;
  
  /* Bordures */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 1rem;
  --border-width: 1px;
  --border-color: #dee2e6;
  
  /* Ombres */
  --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --box-shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  /* Transitions */
  --transition-base: all 0.2s ease-in-out;
  --transition-slow: all 0.3s ease-in-out;
  --transition-fast: all 0.1s ease-in-out;
  
  /* Z-index */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-base);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: var(--border-width) solid transparent;
  border-radius: var(--border-radius-sm);
  transition: var(--transition-base);
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.btn:disabled,
.btn.btn-disabled {
  opacity: 0.65;
  pointer-events: none;
}

.btn-with-icon {
  display: inline-flex;
  align-items: center;
}

.btn-with-icon .icon {
  margin-right: var(--spacing-sm);
}

.btn-primary {
  color: var(--color-white);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  color: var(--color-white);
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.btn-secondary {
  color: var(--color-white);
  background-color: var(--color-secondary);
  border-color: var(--color-secondary);
}

.btn-secondary:hover {
  color: var(--color-white);
  background-color: var(--color-secondary-dark);
  border-color: var(--color-secondary-dark);
}

.btn-success {
  color: var(--color-white);
  background-color: var(--color-success);
  border-color: var(--color-success);
}

.btn-success:hover {
  color: var(--color-white);
  background-color: var(--color-success-dark);
  border-color: var(--color-success-dark);
}

.btn-danger {
  color: var(--color-white);
  background-color: var(--color-danger);
  border-color: var(--color-danger);
}

.btn-danger:hover {
  color: var(--color-white);
  background-color: var(--color-danger-dark);
  border-color: var(--color-danger-dark);
}

.btn-warning {
  color: var(--color-dark);
  background-color: var(--color-warning);
  border-color: var(--color-warning);
}

.btn-warning:hover {
  color: var(--color-dark);
  background-color: var(--color-warning-dark);
  border-color: var(--color-warning-dark);
}

.btn-info {
  color: var(--color-white);
  background-color: var(--color-info);
  border-color: var(--color-info);
}

.btn-info:hover {
  color: var(--color-white);
  background-color: var(--color-info-dark);
  border-color: var(--color-info-dark);
}

.btn-light {
  color: var(--color-dark);
  background-color: var(--color-light);
  border-color: var(--color-light);
}

.btn-light:hover {
  color: var(--color-dark);
  background-color: var(--color-light-dark);
  border-color: var(--color-light-dark);
}

.btn-dark {
  color: var(--color-white);
  background-color: var(--color-dark);
  border-color: var(--color-dark);
}

.btn-dark:hover {
  color: var(--color-white);
  background-color: var(--color-dark-light);
  border-color: var(--color-dark-light);
}

.btn-outline {
  background-color: transparent;
}

.btn-outline.btn-primary {
  color: var(--color-primary);
}

.btn-outline.btn-primary:hover {
  color: var(--color-white);
  background-color: var(--color-primary);
}

.btn-outline.btn-secondary {
  color: var(--color-secondary);
}

.btn-outline.btn-secondary:hover {
  color: var(--color-white);
  background-color: var(--color-secondary);
}

.btn-outline.btn-success {
  color: var(--color-success);
}

.btn-outline.btn-success:hover {
  color: var(--color-white);
  background-color: var(--color-success);
}

.btn-outline.btn-danger {
  color: var(--color-danger);
}

.btn-outline.btn-danger:hover {
  color: var(--color-white);
  background-color: var(--color-danger);
}

.btn-outline.btn-warning {
  color: var(--color-warning);
}

.btn-outline.btn-warning:hover {
  color: var(--color-dark);
  background-color: var(--color-warning);
}

.btn-outline.btn-info {
  color: var(--color-info);
}

.btn-outline.btn-info:hover {
  color: var(--color-white);
  background-color: var(--color-info);
}

.btn-outline.btn-light {
  color: var(--color-light);
}

.btn-outline.btn-light:hover {
  color: var(--color-dark);
  background-color: var(--color-light);
}

.btn-outline.btn-dark {
  color: var(--color-dark);
}

.btn-outline.btn-dark:hover {
  color: var(--color-white);
  background-color: var(--color-dark);
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
}

.btn-group {
  display: inline-flex;
  position: relative;
}

.btn-group-horizontal {
  flex-direction: row;
}

.btn-group-horizontal .btn:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group-horizontal .btn:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group-vertical {
  flex-direction: column;
}

.btn-group-vertical .btn:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.btn-group-vertical .btn:not(:last-child) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-loading .icon-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Cartes */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--color-white);
  background-clip: border-box;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-md);
}

.card-shadow {
  box-shadow: var(--box-shadow-sm);
}

.card-hover:hover {
  box-shadow: var(--box-shadow-md);
  transform: translateY(-2px);
  transition: var(--transition-base);
}

.card-image {
  width: 100%;
  overflow: hidden;
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md);
}

.card-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.card-title {
  padding: var(--spacing-md) var(--spacing-md) 0;
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

.card-content {
  padding: var(--spacing-md);
  flex: 1 1 auto;
}

.card-footer {
  padding: var(--spacing-md);
  background-color: var(--color-light);
  border-top: var(--border-width) solid var(--border-color);
  border-bottom-left-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

/* Formulaires */
.form {
  margin-bottom: var(--spacing-lg);
}

.form-field {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-bold);
}

.form-label.required {
  position: relative;
}

.form-label .required-mark {
  color: var(--color-danger);
  margin-left: var(--spacing-xs);
}

.form-input,
.form-select,
.form-textarea {
  display: block;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--color-dark);
  background-color: var(--color-white);
  background-clip: padding-box;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-base);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: var(--color-primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background-color: var(--color-light);
  opacity: 0.65;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
}

.form-field-checkbox {
  display: flex;
  align-items: flex-start;
}

.checkbox-container {
  display: flex;
  align-items: center;
}

.form-checkbox {
  margin-right: var(--spacing-sm);
}

.checkbox-label {
  margin-bottom: 0;
}

.form-error {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-danger);
}

.form-help {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-gray);
}

.form-field-error .form-input,
.form-field-error .form-select,
.form-field-error .form-textarea {
  border-color: var(--color-danger);
}

.form-field-error .form-input:focus,
.form-field-error .form-select:focus,
.form-field-error .form-textarea:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.form-invalid .form-field-error {
  animation: shake 0.5s;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Modales */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal-backdrop);
  opacity: 0;
  transition: var(--transition-base);
}

.modal-overlay-visible {
  opacity: 1;
}

.modal {
  position: relative;
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-lg);
  max-width: 90%;
  max-height: 90%;
  overflow: auto;
  z-index: var(--z-index-modal);
  transform: scale(0.9);
  opacity: 0;
  transition: var(--transition-base);
}

.modal-visible {
  transform: scale(1);
  opacity: 1;
}

.modal-small {
  width: 300px;
}

.modal-medium {
  width: 500px;
}

.modal-large {
  width: 800px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: var(--border-width) solid var(--border-color);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

.modal-close {
  background: transparent;
  border: 0;
  font-size: var(--font-size-xl);
  line-height: 1;
  color: var(--color-gray);
  cursor: pointer;
  transition: var(--transition-base);
}

.modal-close:hover {
  color: var(--color-dark);
}

.modal-content {
  padding: var(--spacing-md);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-top: var(--border-width) solid var(--border-color);
}

/* Media Queries */
@media (max-width: 576px) {
  .modal-small,
  .modal-medium,
  .modal-large {
    width: 95%;
  }
  
  .card-grid {
    grid-template-columns: 1fr;
  }
  
  .form-buttons {
    flex-direction: column;
  }
  
  .form-buttons .btn {
    width: 100%;
  }
}

@media (min-width: 576px) and (max-width: 768px) {
  .modal-large {
    width: 95%;
  }
  
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 992px) {
  .modal-large {
    width: 80%;
  }
}

/* Utilitaires */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-end {
  justify-content: flex-end;
}

.align-items-center {
  align-items: center;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.m-0 {
  margin: 0;
}

.mt-1 {
  margin-top: var(--spacing-sm);
}

.mt-2 {
  margin-top: var(--spacing-md);
}

.mt-3 {
  margin-top: var(--spacing-lg);
}

.mb-1 {
  margin-bottom: var(--spacing-sm);
}

.mb-2 {
  margin-bottom: var(--spacing-md);
}

.mb-3 {
  margin-bottom: var(--spacing-lg);
}

.ml-1 {
  margin-left: var(--spacing-sm);
}

.ml-2 {
  margin-left: var(--spacing-md);
}

.ml-3 {
  margin-left: var(--spacing-lg);
}

.mr-1 {
  margin-right: var(--spacing-sm);
}

.mr-2 {
  margin-right: var(--spacing-md);
}

.mr-3 {
  margin-right: var(--spacing-lg);
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: var(--spacing-sm);
}

.p-2 {
  padding: var(--spacing-md);
}

.p-3 {
  padding: var(--spacing-lg);
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.rounded {
  border-radius: var(--border-radius-sm);
}

.rounded-circle {
  border-radius: 50%;
}

.shadow {
  box-shadow: var(--box-shadow-sm);
}

.shadow-lg {
  box-shadow: var(--box-shadow-lg);
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.text-success {
  color: var(--color-success);
}

.text-danger {
  color: var(--color-danger);
}

.text-warning {
  color: var(--color-warning);
}

.text-info {
  color: var(--color-info);
}

.text-light {
  color: var(--color-light);
}

.text-dark {
  color: var(--color-dark);
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-secondary {
  background-color: var(--color-secondary);
}

.bg-success {
  background-color: var(--color-success);
}

.bg-danger {
  background-color: var(--color-danger);
}

.bg-warning {
  background-color: var(--color-warning);
}

.bg-info {
  background-color: var(--color-info);
}

.bg-light {
  background-color: var(--color-light);
}

.bg-dark {
  background-color: var(--color-dark);
}

.font-weight-light {
  font-weight: var(--font-weight-light);
}

.font-weight-normal {
  font-weight: var(--font-weight-normal);
}

.font-weight-bold {
  font-weight: var(--font-weight-bold);
}

.font-size-sm {
  font-size: var(--font-size-sm);
}

.font-size-lg {
  font-size: var(--font-size-lg);
}

.font-size-xl {
  font-size: var(--font-size-xl);
}

.font-size-xxl {
  font-size: var(--font-size-xxl);
}
