"""
Module de surveillance pour collecter des métriques sur les appels entre microservices.
"""
import time
import logging
import functools
import threading
from typing import Dict, Any, Callable, List, Optional
from collections import deque

# Configuration du logging
logger = logging.getLogger(__name__)

class MetricsCollector:
    """
    Collecteur de métriques pour les appels entre microservices.
    
    Attributes:
        metrics: Dictionnaire contenant les métriques collectées
        request_times: Dictionnaire contenant les temps de réponse des requêtes
        error_counts: Dictionnaire contenant le nombre d'erreurs par service
        _lock: Verrou pour protéger l'accès concurrent aux métriques
    """
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls) -> 'MetricsCollector':
        """
        Récupère l'instance unique du collecteur de métriques.
        
        Returns:
            Instance du collecteur de métriques
        """
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = MetricsCollector()
        return cls._instance
    
    def __init__(self):
        """Initialise le collecteur de métriques."""
        self.metrics = {
            'request_count': 0,
            'success_count': 0,
            'error_count': 0,
            'services': {}
        }
        self.request_times = {}
        self.error_counts = {}
        self._lock = threading.Lock()
    
    def record_request(self, service: str, endpoint: str, method: str, start_time: float, 
                      end_time: float, success: bool, error: Optional[str] = None) -> None:
        """
        Enregistre une requête.
        
        Args:
            service: Nom du service appelé
            endpoint: Endpoint appelé
            method: Méthode HTTP utilisée
            start_time: Timestamp de début de la requête
            end_time: Timestamp de fin de la requête
            success: Si la requête a réussi
            error: Message d'erreur si la requête a échoué
        """
        with self._lock:
            # Mettre à jour les compteurs globaux
            self.metrics['request_count'] += 1
            if success:
                self.metrics['success_count'] += 1
            else:
                self.metrics['error_count'] += 1
            
            # Mettre à jour les métriques par service
            if service not in self.metrics['services']:
                self.metrics['services'][service] = {
                    'request_count': 0,
                    'success_count': 0,
                    'error_count': 0,
                    'endpoints': {}
                }
            
            service_metrics = self.metrics['services'][service]
            service_metrics['request_count'] += 1
            if success:
                service_metrics['success_count'] += 1
            else:
                service_metrics['error_count'] += 1
            
            # Mettre à jour les métriques par endpoint
            endpoint_key = f"{method}:{endpoint}"
            if endpoint_key not in service_metrics['endpoints']:
                service_metrics['endpoints'][endpoint_key] = {
                    'request_count': 0,
                    'success_count': 0,
                    'error_count': 0,
                    'avg_response_time': 0,
                    'min_response_time': float('inf'),
                    'max_response_time': 0
                }
            
            endpoint_metrics = service_metrics['endpoints'][endpoint_key]
            endpoint_metrics['request_count'] += 1
            if success:
                endpoint_metrics['success_count'] += 1
            else:
                endpoint_metrics['error_count'] += 1
            
            # Calculer le temps de réponse
            response_time = end_time - start_time
            
            # Mettre à jour les temps de réponse
            if service not in self.request_times:
                self.request_times[service] = {}
            
            if endpoint_key not in self.request_times[service]:
                self.request_times[service][endpoint_key] = deque(maxlen=100)  # Garder les 100 derniers temps
            
            self.request_times[service][endpoint_key].append(response_time)
            
            # Mettre à jour les statistiques de temps de réponse
            endpoint_metrics['avg_response_time'] = sum(self.request_times[service][endpoint_key]) / len(self.request_times[service][endpoint_key])
            endpoint_metrics['min_response_time'] = min(endpoint_metrics['min_response_time'], response_time)
            endpoint_metrics['max_response_time'] = max(endpoint_metrics['max_response_time'], response_time)
            
            # Enregistrer les erreurs
            if not success:
                if service not in self.error_counts:
                    self.error_counts[service] = {}
                
                if endpoint_key not in self.error_counts[service]:
                    self.error_counts[service][endpoint_key] = {}
                
                error_key = error or 'unknown'
                self.error_counts[service][endpoint_key][error_key] = self.error_counts[service][endpoint_key].get(error_key, 0) + 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Récupère les métriques collectées.
        
        Returns:
            Métriques collectées
        """
        with self._lock:
            return self.metrics.copy()
    
    def get_service_metrics(self, service: str) -> Dict[str, Any]:
        """
        Récupère les métriques pour un service spécifique.
        
        Args:
            service: Nom du service
            
        Returns:
            Métriques du service
        """
        with self._lock:
            return self.metrics['services'].get(service, {}).copy()
    
    def get_endpoint_metrics(self, service: str, endpoint: str, method: str) -> Dict[str, Any]:
        """
        Récupère les métriques pour un endpoint spécifique.
        
        Args:
            service: Nom du service
            endpoint: Endpoint
            method: Méthode HTTP
            
        Returns:
            Métriques de l'endpoint
        """
        with self._lock:
            endpoint_key = f"{method}:{endpoint}"
            service_metrics = self.metrics['services'].get(service, {})
            return service_metrics.get('endpoints', {}).get(endpoint_key, {}).copy()
    
    def get_error_counts(self, service: Optional[str] = None, endpoint: Optional[str] = None, 
                        method: Optional[str] = None) -> Dict[str, Any]:
        """
        Récupère le nombre d'erreurs.
        
        Args:
            service: Nom du service (optionnel)
            endpoint: Endpoint (optionnel)
            method: Méthode HTTP (optionnel)
            
        Returns:
            Nombre d'erreurs
        """
        with self._lock:
            if service is None:
                return self.error_counts.copy()
            
            if endpoint is None or method is None:
                return self.error_counts.get(service, {}).copy()
            
            endpoint_key = f"{method}:{endpoint}"
            return self.error_counts.get(service, {}).get(endpoint_key, {}).copy()
    
    def reset(self) -> None:
        """Réinitialise les métriques."""
        with self._lock:
            self.metrics = {
                'request_count': 0,
                'success_count': 0,
                'error_count': 0,
                'services': {}
            }
            self.request_times = {}
            self.error_counts = {}

def monitor_request(service: str):
    """
    Décorateur pour surveiller les appels à un service.
    
    Args:
        service: Nom du service appelé
        
    Returns:
        Décorateur
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extraire l'endpoint et la méthode des arguments
            endpoint = kwargs.get('endpoint', args[1] if len(args) > 1 else 'unknown')
            method = kwargs.get('method', args[2] if len(args) > 2 else 'GET')
            
            # Récupérer le collecteur de métriques
            metrics_collector = MetricsCollector.get_instance()
            
            # Enregistrer le début de la requête
            start_time = time.time()
            
            try:
                # Appeler la fonction
                result = func(*args, **kwargs)
                
                # Enregistrer la fin de la requête (succès)
                end_time = time.time()
                metrics_collector.record_request(
                    service=service,
                    endpoint=endpoint,
                    method=method,
                    start_time=start_time,
                    end_time=end_time,
                    success=True
                )
                
                return result
            
            except Exception as e:
                # Enregistrer la fin de la requête (échec)
                end_time = time.time()
                metrics_collector.record_request(
                    service=service,
                    endpoint=endpoint,
                    method=method,
                    start_time=start_time,
                    end_time=end_time,
                    success=False,
                    error=str(e)
                )
                
                # Propager l'exception
                raise
        
        return wrapper
    
    return decorator
