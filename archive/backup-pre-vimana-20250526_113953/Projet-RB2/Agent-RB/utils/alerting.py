"""
Module d'alertes pour surveiller les microservices.

Ce module permet de définir des règles d'alerte et d'envoyer des notifications
lorsque des conditions spécifiques sont remplies.
"""
import os
import logging
import time
import threading
import json
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from typing import Dict, Any, List, Callable, Optional, Union

# Configuration du logging
logger = logging.getLogger(__name__)

# Configuration des alertes
ALERTING_ENABLED = os.environ.get('ALERTING_ENABLED', 'false').lower() == 'true'
ALERT_CHECK_INTERVAL = int(os.environ.get('ALERT_CHECK_INTERVAL', '60'))  # Secondes

# Configuration des notifications
EMAIL_ENABLED = os.environ.get('EMAIL_ALERTS_ENABLED', 'false').lower() == 'true'
EMAIL_SERVER = os.environ.get('EMAIL_SERVER', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USERNAME = os.environ.get('EMAIL_USERNAME', '')
EMAIL_PASSWORD = os.environ.get('EMAIL_PASSWORD', '')
EMAIL_FROM = os.environ.get('EMAIL_FROM', '<EMAIL>')
EMAIL_TO = os.environ.get('EMAIL_TO', '<EMAIL>').split(',')

SLACK_ENABLED = os.environ.get('SLACK_ALERTS_ENABLED', 'false').lower() == 'true'
SLACK_WEBHOOK_URL = os.environ.get('SLACK_WEBHOOK_URL', '')

class AlertRule:
    """
    Règle d'alerte pour surveiller une métrique.
    
    Attributes:
        name: Nom de la règle
        description: Description de la règle
        check_function: Fonction qui vérifie si la condition d'alerte est remplie
        severity: Sévérité de l'alerte (info, warning, error, critical)
        enabled: Si la règle est activée
        last_triggered: Timestamp de la dernière fois que l'alerte a été déclenchée
        cooldown: Temps en secondes avant de pouvoir déclencher à nouveau l'alerte
    """
    
    def __init__(self, name: str, description: str, check_function: Callable[[], bool], 
                severity: str = 'warning', enabled: bool = True, cooldown: int = 300):
        """
        Initialise une règle d'alerte.
        
        Args:
            name: Nom de la règle
            description: Description de la règle
            check_function: Fonction qui vérifie si la condition d'alerte est remplie
            severity: Sévérité de l'alerte (info, warning, error, critical)
            enabled: Si la règle est activée
            cooldown: Temps en secondes avant de pouvoir déclencher à nouveau l'alerte
        """
        self.name = name
        self.description = description
        self.check_function = check_function
        self.severity = severity
        self.enabled = enabled
        self.last_triggered = 0
        self.cooldown = cooldown
    
    def check(self) -> bool:
        """
        Vérifie si la condition d'alerte est remplie.
        
        Returns:
            True si la condition est remplie, False sinon
        """
        if not self.enabled:
            return False
        
        # Vérifier si l'alerte est en cooldown
        if time.time() - self.last_triggered < self.cooldown:
            return False
        
        try:
            # Vérifier la condition
            if self.check_function():
                self.last_triggered = time.time()
                return True
        except Exception as e:
            logger.error(f"Erreur lors de la vérification de l'alerte {self.name}: {str(e)}")
        
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit la règle d'alerte en dictionnaire.
        
        Returns:
            Dictionnaire représentant la règle d'alerte
        """
        return {
            'name': self.name,
            'description': self.description,
            'severity': self.severity,
            'enabled': self.enabled,
            'last_triggered': self.last_triggered,
            'cooldown': self.cooldown
        }

class AlertManager:
    """
    Gestionnaire d'alertes pour surveiller les microservices.
    
    Attributes:
        rules: Liste des règles d'alerte
        _instance: Instance unique du gestionnaire d'alertes
        _lock: Verrou pour protéger l'accès concurrent aux règles
        _thread: Thread qui vérifie périodiquement les règles
        _running: Si le thread est en cours d'exécution
    """
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls) -> 'AlertManager':
        """
        Récupère l'instance unique du gestionnaire d'alertes.
        
        Returns:
            Instance du gestionnaire d'alertes
        """
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = AlertManager()
        return cls._instance
    
    def __init__(self):
        """Initialise le gestionnaire d'alertes."""
        self.rules = []
        self._thread = None
        self._running = False
    
    def add_rule(self, rule: AlertRule) -> None:
        """
        Ajoute une règle d'alerte.
        
        Args:
            rule: Règle d'alerte à ajouter
        """
        with self._lock:
            self.rules.append(rule)
    
    def remove_rule(self, name: str) -> bool:
        """
        Supprime une règle d'alerte.
        
        Args:
            name: Nom de la règle à supprimer
            
        Returns:
            True si la règle a été supprimée, False sinon
        """
        with self._lock:
            for i, rule in enumerate(self.rules):
                if rule.name == name:
                    del self.rules[i]
                    return True
        return False
    
    def get_rule(self, name: str) -> Optional[AlertRule]:
        """
        Récupère une règle d'alerte.
        
        Args:
            name: Nom de la règle
            
        Returns:
            Règle d'alerte ou None si elle n'existe pas
        """
        with self._lock:
            for rule in self.rules:
                if rule.name == name:
                    return rule
        return None
    
    def get_rules(self) -> List[Dict[str, Any]]:
        """
        Récupère toutes les règles d'alerte.
        
        Returns:
            Liste des règles d'alerte
        """
        with self._lock:
            return [rule.to_dict() for rule in self.rules]
    
    def enable_rule(self, name: str) -> bool:
        """
        Active une règle d'alerte.
        
        Args:
            name: Nom de la règle
            
        Returns:
            True si la règle a été activée, False sinon
        """
        rule = self.get_rule(name)
        if rule:
            rule.enabled = True
            return True
        return False
    
    def disable_rule(self, name: str) -> bool:
        """
        Désactive une règle d'alerte.
        
        Args:
            name: Nom de la règle
            
        Returns:
            True si la règle a été désactivée, False sinon
        """
        rule = self.get_rule(name)
        if rule:
            rule.enabled = False
            return True
        return False
    
    def check_rules(self) -> List[Dict[str, Any]]:
        """
        Vérifie toutes les règles d'alerte.
        
        Returns:
            Liste des alertes déclenchées
        """
        triggered_alerts = []
        
        with self._lock:
            for rule in self.rules:
                if rule.check():
                    alert = rule.to_dict()
                    alert['triggered_at'] = time.time()
                    
                    # Envoyer des notifications
                    self._send_notifications(alert)
                    
                    triggered_alerts.append(alert)
        
        return triggered_alerts
    
    def _send_notifications(self, alert: Dict[str, Any]) -> None:
        """
        Envoie des notifications pour une alerte.
        
        Args:
            alert: Alerte déclenchée
        """
        # Envoyer un email
        if EMAIL_ENABLED:
            try:
                self._send_email_notification(alert)
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de l'email: {str(e)}")
        
        # Envoyer une notification Slack
        if SLACK_ENABLED:
            try:
                self._send_slack_notification(alert)
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de la notification Slack: {str(e)}")
    
    def _send_email_notification(self, alert: Dict[str, Any]) -> None:
        """
        Envoie une notification par email.
        
        Args:
            alert: Alerte déclenchée
        """
        # Créer le message
        msg = MIMEMultipart()
        msg['From'] = EMAIL_FROM
        msg['To'] = ', '.join(EMAIL_TO)
        msg['Subject'] = f"[{alert['severity'].upper()}] {alert['name']}"
        
        # Corps du message
        body = f"""
        <html>
        <body>
            <h2>Alerte: {alert['name']}</h2>
            <p><strong>Sévérité:</strong> {alert['severity']}</p>
            <p><strong>Description:</strong> {alert['description']}</p>
            <p><strong>Déclenchée à:</strong> {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(alert['triggered_at']))}</p>
        </body>
        </html>
        """
        msg.attach(MIMEText(body, 'html'))
        
        # Envoyer l'email
        with smtplib.SMTP(EMAIL_SERVER, EMAIL_PORT) as server:
            server.starttls()
            if EMAIL_USERNAME and EMAIL_PASSWORD:
                server.login(EMAIL_USERNAME, EMAIL_PASSWORD)
            server.send_message(msg)
    
    def _send_slack_notification(self, alert: Dict[str, Any]) -> None:
        """
        Envoie une notification Slack.
        
        Args:
            alert: Alerte déclenchée
        """
        # Définir la couleur en fonction de la sévérité
        color = {
            'info': '#36a64f',
            'warning': '#ffcc00',
            'error': '#ff9900',
            'critical': '#ff0000'
        }.get(alert['severity'], '#36a64f')
        
        # Créer le message
        message = {
            'attachments': [
                {
                    'fallback': f"[{alert['severity'].upper()}] {alert['name']}",
                    'color': color,
                    'title': f"Alerte: {alert['name']}",
                    'fields': [
                        {
                            'title': 'Sévérité',
                            'value': alert['severity'],
                            'short': True
                        },
                        {
                            'title': 'Déclenchée à',
                            'value': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(alert['triggered_at'])),
                            'short': True
                        },
                        {
                            'title': 'Description',
                            'value': alert['description'],
                            'short': False
                        }
                    ]
                }
            ]
        }
        
        # Envoyer la notification
        requests.post(SLACK_WEBHOOK_URL, json=message)
    
    def start(self) -> None:
        """Démarre le thread de vérification des règles."""
        if not ALERTING_ENABLED:
            logger.info("Alerting is disabled")
            return
        
        if self._thread is not None and self._thread.is_alive():
            logger.warning("Alert manager is already running")
            return
        
        self._running = True
        self._thread = threading.Thread(target=self._check_loop)
        self._thread.daemon = True
        self._thread.start()
        
        logger.info("Alert manager started")
    
    def stop(self) -> None:
        """Arrête le thread de vérification des règles."""
        self._running = False
        if self._thread is not None:
            self._thread.join(timeout=5)
        
        logger.info("Alert manager stopped")
    
    def _check_loop(self) -> None:
        """Boucle de vérification des règles."""
        while self._running:
            try:
                self.check_rules()
            except Exception as e:
                logger.error(f"Erreur lors de la vérification des règles: {str(e)}")
            
            # Attendre avant la prochaine vérification
            time.sleep(ALERT_CHECK_INTERVAL)

# Initialiser le gestionnaire d'alertes
alert_manager = AlertManager.get_instance()

# Démarrer le gestionnaire d'alertes si activé
if ALERTING_ENABLED:
    alert_manager.start()
