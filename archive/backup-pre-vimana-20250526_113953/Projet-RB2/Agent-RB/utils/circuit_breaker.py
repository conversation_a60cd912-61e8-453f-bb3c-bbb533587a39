"""
Module de circuit breaker pour améliorer la résilience des appels entre microservices.

Un circuit breaker permet d'éviter les appels répétés à un service défaillant,
ce qui peut entraîner une cascade d'échecs dans l'ensemble du système.
"""
import time
import logging
import functools
from enum import Enum
from typing import Callable, Any, Dict, Optional

# Configuration du logging
logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """États possibles du circuit breaker."""
    CLOSED = 'closed'  # Circuit fermé, les appels sont autorisés
    OPEN = 'open'      # Circuit ouvert, les appels sont bloqués
    HALF_OPEN = 'half_open'  # Circuit semi-ouvert, un appel est autorisé pour tester

class CircuitBreaker:
    """
    Implémentation d'un circuit breaker pour améliorer la résilience des appels entre microservices.
    
    Attributes:
        name: Nom du circuit breaker
        failure_threshold: Nombre d'échecs consécutifs avant d'ouvrir le circuit
        recovery_timeout: Temps en secondes avant de passer à l'état semi-ouvert
        expected_exceptions: Exceptions qui sont considérées comme des échecs
        state: État actuel du circuit
        failure_count: Nombre d'échecs consécutifs
        last_failure_time: Timestamp du dernier échec
    """
    
    _instances: Dict[str, 'CircuitBreaker'] = {}
    
    @classmethod
    def get_instance(cls, name: str) -> 'CircuitBreaker':
        """
        Récupère une instance de circuit breaker par son nom.
        
        Args:
            name: Nom du circuit breaker
            
        Returns:
            Instance du circuit breaker
        """
        if name not in cls._instances:
            cls._instances[name] = CircuitBreaker(name)
        return cls._instances[name]
    
    def __init__(self, name: str, failure_threshold: int = 3, recovery_timeout: int = 30,
                expected_exceptions: tuple = (Exception,)):
        """
        Initialise un circuit breaker.
        
        Args:
            name: Nom du circuit breaker
            failure_threshold: Nombre d'échecs consécutifs avant d'ouvrir le circuit
            recovery_timeout: Temps en secondes avant de passer à l'état semi-ouvert
            expected_exceptions: Exceptions qui sont considérées comme des échecs
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exceptions = expected_exceptions
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0
    
    def __call__(self, func: Callable) -> Callable:
        """
        Décorateur pour protéger une fonction avec un circuit breaker.
        
        Args:
            func: Fonction à protéger
            
        Returns:
            Fonction protégée
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return self.call(func, *args, **kwargs)
        return wrapper
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Appelle une fonction en respectant l'état du circuit breaker.
        
        Args:
            func: Fonction à appeler
            *args: Arguments positionnels
            **kwargs: Arguments nommés
            
        Returns:
            Résultat de la fonction
            
        Raises:
            CircuitBreakerError: Si le circuit est ouvert
            Exception: Si la fonction échoue
        """
        if self.state == CircuitState.OPEN:
            if time.time() > self.last_failure_time + self.recovery_timeout:
                logger.info(f"Circuit breaker '{self.name}' passe à l'état semi-ouvert")
                self.state = CircuitState.HALF_OPEN
            else:
                logger.warning(f"Circuit breaker '{self.name}' est ouvert, appel bloqué")
                raise CircuitBreakerError(f"Circuit breaker '{self.name}' est ouvert")
        
        try:
            result = func(*args, **kwargs)
            
            # Si l'appel réussit et que le circuit est semi-ouvert, fermer le circuit
            if self.state == CircuitState.HALF_OPEN:
                logger.info(f"Circuit breaker '{self.name}' passe à l'état fermé")
                self.state = CircuitState.CLOSED
                self.failure_count = 0
            
            return result
        
        except self.expected_exceptions as e:
            # Incrémenter le compteur d'échecs
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            # Si le seuil d'échecs est atteint, ouvrir le circuit
            if self.state == CircuitState.CLOSED and self.failure_count >= self.failure_threshold:
                logger.warning(f"Circuit breaker '{self.name}' passe à l'état ouvert après {self.failure_count} échecs")
                self.state = CircuitState.OPEN
            
            # Si le circuit est semi-ouvert et que l'appel échoue, ouvrir le circuit
            if self.state == CircuitState.HALF_OPEN:
                logger.warning(f"Circuit breaker '{self.name}' passe à l'état ouvert après un échec en état semi-ouvert")
                self.state = CircuitState.OPEN
            
            # Propager l'exception
            raise e
    
    def reset(self) -> None:
        """Réinitialise le circuit breaker à son état initial."""
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0
        logger.info(f"Circuit breaker '{self.name}' réinitialisé")
    
    def get_state(self) -> CircuitState:
        """
        Récupère l'état actuel du circuit breaker.
        
        Returns:
            État actuel du circuit breaker
        """
        return self.state
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Récupère les métriques du circuit breaker.
        
        Returns:
            Métriques du circuit breaker
        """
        return {
            'name': self.name,
            'state': self.state.value,
            'failure_count': self.failure_count,
            'last_failure_time': self.last_failure_time,
            'failure_threshold': self.failure_threshold,
            'recovery_timeout': self.recovery_timeout
        }

class CircuitBreakerError(Exception):
    """Exception levée lorsqu'un appel est bloqué par un circuit breaker ouvert."""
    pass
