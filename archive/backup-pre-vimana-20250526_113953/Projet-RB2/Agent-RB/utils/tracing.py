"""
Module de tracing distribué avec OpenTelemetry.

Ce module permet de tracer les requêtes à travers les différents microservices
pour faciliter le débogage et l'analyse des performances.
"""
import os
import logging
import functools
from typing import Callable, Dict, Any, Optional

# Configuration du logging
logger = logging.getLogger(__name__)

# Vérifier si OpenTelemetry est disponible
try:
    from opentelemetry import trace
    from opentelemetry.exporter.jaeger.thrift import JaegerExporter
    from opentelemetry.sdk.resources import SERVICE_NAME, Resource
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
    from opentelemetry.trace.status import Status, StatusCode
    
    OPENTELEMETRY_AVAILABLE = True
except ImportError:
    logger.warning("OpenTelemetry n'est pas disponible. Le tracing sera désactivé.")
    OPENTELEMETRY_AVAILABLE = False

# Configuration du tracing
TRACING_ENABLED = os.environ.get('TRACING_ENABLED', 'false').lower() == 'true'
JAEGER_HOST = os.environ.get('JAEGER_HOST', 'localhost')
JAEGER_PORT = int(os.environ.get('JAEGER_PORT', '6831'))
SERVICE_NAME_VALUE = os.environ.get('SERVICE_NAME', 'agent-rb')

# Initialisation du tracing
if TRACING_ENABLED and OPENTELEMETRY_AVAILABLE:
    try:
        # Créer un resource avec le nom du service
        resource = Resource(attributes={
            SERVICE_NAME: SERVICE_NAME_VALUE
        })
        
        # Créer un provider de tracing
        tracer_provider = TracerProvider(resource=resource)
        
        # Créer un exporter Jaeger
        jaeger_exporter = JaegerExporter(
            agent_host_name=JAEGER_HOST,
            agent_port=JAEGER_PORT,
        )
        
        # Ajouter l'exporter au provider
        tracer_provider.add_span_processor(BatchSpanProcessor(jaeger_exporter))
        
        # Définir le provider global
        trace.set_tracer_provider(tracer_provider)
        
        # Créer un tracer
        tracer = trace.get_tracer(__name__)
        
        # Créer un propagateur pour les en-têtes HTTP
        propagator = TraceContextTextMapPropagator()
        
        logger.info(f"Tracing initialisé avec succès pour le service {SERVICE_NAME_VALUE}")
        logger.info(f"Jaeger configuré sur {JAEGER_HOST}:{JAEGER_PORT}")
    except Exception as e:
        logger.error(f"Erreur lors de l'initialisation du tracing: {str(e)}")
        TRACING_ENABLED = False
else:
    logger.info("Tracing désactivé")

def trace_request(name: str):
    """
    Décorateur pour tracer une requête.
    
    Args:
        name: Nom de la span
        
    Returns:
        Décorateur
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Si le tracing n'est pas activé, appeler directement la fonction
            if not TRACING_ENABLED or not OPENTELEMETRY_AVAILABLE:
                return func(*args, **kwargs)
            
            # Extraire les informations de la requête
            endpoint = kwargs.get('endpoint', args[1] if len(args) > 1 else 'unknown')
            method = kwargs.get('method', args[2] if len(args) > 2 else 'GET')
            
            # Créer une span
            with tracer.start_as_current_span(name) as span:
                # Ajouter des attributs à la span
                span.set_attribute("endpoint", endpoint)
                span.set_attribute("method", method)
                
                # Ajouter des attributs supplémentaires
                if 'data' in kwargs and kwargs['data']:
                    span.set_attribute("request.data", str(kwargs['data']))
                if 'params' in kwargs and kwargs['params']:
                    span.set_attribute("request.params", str(kwargs['params']))
                
                try:
                    # Appeler la fonction
                    result = func(*args, **kwargs)
                    
                    # Marquer la span comme réussie
                    span.set_status(Status(StatusCode.OK))
                    
                    # Ajouter le résultat à la span
                    if result:
                        span.set_attribute("response.status", "success")
                    
                    return result
                
                except Exception as e:
                    # Marquer la span comme échouée
                    span.set_status(Status(StatusCode.ERROR, str(e)))
                    span.record_exception(e)
                    
                    # Propager l'exception
                    raise
        
        return wrapper
    
    return decorator

def get_current_span_context():
    """
    Récupère le contexte de la span courante pour le propager.
    
    Returns:
        Contexte de la span courante ou None si le tracing n'est pas activé
    """
    if not TRACING_ENABLED or not OPENTELEMETRY_AVAILABLE:
        return None
    
    return trace.get_current_span().get_span_context()

def inject_span_context(headers: Dict[str, str]):
    """
    Injecte le contexte de la span courante dans les en-têtes HTTP.
    
    Args:
        headers: En-têtes HTTP à modifier
        
    Returns:
        En-têtes HTTP avec le contexte de la span
    """
    if not TRACING_ENABLED or not OPENTELEMETRY_AVAILABLE:
        return headers
    
    carrier = {}
    propagator.inject(carrier=carrier)
    headers.update(carrier)
    
    return headers

def extract_span_context(headers: Dict[str, str]):
    """
    Extrait le contexte de la span des en-têtes HTTP.
    
    Args:
        headers: En-têtes HTTP
        
    Returns:
        Contexte de la span ou None si le tracing n'est pas activé
    """
    if not TRACING_ENABLED or not OPENTELEMETRY_AVAILABLE:
        return None
    
    return propagator.extract(carrier=headers)
