"""
Researcher node implementation.
The researcher is responsible for gathering information and context.
"""

from src.graph.types import State
import logging

logger = logging.getLogger(__name__)

async def researcher_node(state: State) -> State:
    """
    Researcher node implementation.
    
    The researcher is responsible for:
    - Gathering information from various sources
    - Analyzing and synthesizing information
    - Providing context for other agents
    
    Args:
        state: The current workflow state
        
    Returns:
        Updated workflow state
    """
    logger.info("Researcher node processing...")
    
    # Record this step in history
    state.history.append({
        "agent": "researcher",
        "action": "gather_information",
        "timestamp": "timestamp_placeholder"  # In a real implementation, use actual timestamp
    })
    
    # Simulate research process
    research_results = {
        "sources": [
            {"name": "Source 1", "relevance": 0.85, "content": "Sample content from source 1"},
            {"name": "Source 2", "relevance": 0.72, "content": "Sample content from source 2"}
        ],
        "key_findings": [
            "Finding 1: Important information about the task",
            "Finding 2: Additional context that might be useful"
        ],
        "summary": "This is a summary of the research findings"
    }
    
    # Update the state with research results
    state.results["research"] = research_results
    
    # Update the plan
    plan = state.context.get("plan", {})
    steps = plan.get("steps", [])
    
    # Mark the current step as completed
    for step in steps:
        if step["id"] == plan.get("current_step"):
            step["status"] = "completed"
            # Find the next step
            for next_step in steps:
                if step["id"] in next_step.get("depends_on", []):
                    plan["current_step"] = next_step["id"]
                    break
            break
    
    # Set the next agent to be the supervisor
    state.next = "supervisor"
    
    logger.info(f"Researcher completed. Next agent: {state.next}")
    return state
