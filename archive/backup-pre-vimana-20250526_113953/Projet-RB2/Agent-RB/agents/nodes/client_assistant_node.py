"""
Agent assistant client.
Cet agent est responsable d'aider les clients à trouver des retraites adaptées à leurs besoins
et de répondre à leurs questions.
"""

import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.graph.types import State
from src.utils.time_utils import get_timestamp
from src.utils.error_utils import handle_error, ErrorType
from src.config import load_config
from src.models.client import Client, ClientPreference, ClientStatus

logger = logging.getLogger(__name__)

async def client_assistant_node(state: State) -> State:
    """
    Agent assistant client.
    
    Cet agent est responsable de:
    - Analyser les préférences et besoins des clients
    - Recommander des retraites adaptées
    - Répondre aux questions des clients
    - Aider à la réservation
    
    Args:
        state: L'état actuel du workflow
        
    Returns:
        État mis à jour du workflow
    """
    logger.info("Agent assistant client en cours de traitement...")
    
    try:
        # Charger la configuration
        config = load_config()
        agent_config = config.get_agent_config("client_assistant")
        
        # Enregistrer cette étape dans l'historique
        state.history.append({
            "agent": "client_assistant",
            "action": "assist_client",
            "timestamp": get_timestamp()
        })
        
        # Récupérer les informations de la tâche
        task = state.task
        if not task:
            raise ValueError("Aucune tâche définie pour l'assistance client")
        
        # Déterminer le type d'assistance demandée
        assistance_type = task.get("assistance_type", "recommendation")
        
        if assistance_type == "recommendation":
            # Recommander des retraites
            recommendations = _recommend_retreats(task)
            state.results["recommendations"] = recommendations
            logger.info(f"Recommandations générées: {len(recommendations)} retraites")
            
        elif assistance_type == "question_answering":
            # Répondre à une question
            answer = _answer_client_question(task)
            state.results["answer"] = answer
            logger.info("Réponse générée pour la question du client")
            
        elif assistance_type == "booking_assistance":
            # Aider à la réservation
            booking_info = _provide_booking_assistance(task)
            state.results["booking_assistance"] = booking_info
            logger.info("Assistance à la réservation fournie")
            
        else:
            logger.warning(f"Type d'assistance non reconnu: {assistance_type}")
            state.results["error"] = f"Type d'assistance non reconnu: {assistance_type}"
        
        # Définir l'agent suivant
        state.next = "supervisor"
        
        logger.info(f"Assistance client terminée. Agent suivant: {state.next}")
        
    except Exception as e:
        logger.error(f"Erreur dans l'agent assistant client: {str(e)}")
        state.error = handle_error(e, context={"agent": "client_assistant"}, error_type=ErrorType.EXECUTION_ERROR)
        state.next = "supervisor"  # Laisser le superviseur gérer l'erreur
    
    return state

def _recommend_retreats(task: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Recommander des retraites adaptées aux préférences du client.
    
    Dans un système réel, cette fonction interrogerait une base de données.
    Ici, nous simulons des résultats.
    
    Args:
        task: Informations de la tâche
        
    Returns:
        Liste des retraites recommandées
    """
    # Extraire les préférences du client
    client_data = task.get("client", {})
    preferences = client_data.get("preferences", {})
    
    # Simuler une recherche de retraites
    recommendations = []
    
    # Créer quelques retraites fictives adaptées aux préférences
    retreat_types = preferences.get("retreat_types", ["yoga", "meditation", "wellness"])
    locations = preferences.get("locations", ["France", "Espagne", "Italie"])
    activities = preferences.get("activities", ["yoga", "meditation", "hiking"])
    price_range = preferences.get("price_range", {"min": 500, "max": 2000})
    
    # Générer 3-5 recommandations
    for i in range(1, 6):
        # Choisir un type de retraite parmi les préférences
        retreat_type = retreat_types[i % len(retreat_types)]
        
        # Choisir une localisation parmi les préférences
        location_country = locations[i % len(locations)]
        
        # Créer une retraite
        retreat = {
            "id": f"retreat_{uuid.uuid4().hex[:8]}",
            "title": f"Retraite de {retreat_type.capitalize()} en {location_country}",
            "description": f"Une retraite de {retreat_type} pour se ressourcer et se reconnecter à soi-même.",
            "retreat_type": retreat_type,
            "start_date": (datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).isoformat()),
            "end_date": (datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).isoformat()),
            "duration": 7,  # jours
            "location": {
                "country": location_country,
                "region": f"Région {i}"
            },
            "activities": [activities[j % len(activities)] for j in range(i, i+3)],
            "price": price_range["min"] + (i * 200),
            "capacity": 10 + i,
            "rating": 4.0 + (i * 0.2) % 1.0,  # Note entre 4.0 et 4.8
            "match_score": 0.7 + (i * 0.05) % 0.3,  # Score entre 0.7 et 0.95
            "match_reasons": [
                f"Correspond à votre préférence pour {retreat_type}",
                f"Se déroule en {location_country}, une destination que vous avez mentionnée",
                f"Propose des activités que vous appréciez"
            ]
        }
        
        recommendations.append(retreat)
    
    # Trier par score de correspondance
    recommendations.sort(key=lambda r: r.get("match_score", 0), reverse=True)
    
    return recommendations

def _answer_client_question(task: Dict[str, Any]) -> Dict[str, Any]:
    """
    Répondre à une question d'un client.
    
    Args:
        task: Informations de la tâche
        
    Returns:
        Réponse à la question
    """
    # Extraire la question
    question = task.get("question", "")
    
    # Simuler une analyse de la question et une génération de réponse
    question_lower = question.lower()
    
    # Réponses prédéfinies pour certains types de questions
    if "prix" in question_lower or "tarif" in question_lower or "coût" in question_lower:
        answer = {
            "text": "Les prix de nos retraites varient généralement entre 500€ et 2500€ selon la durée, le lieu et les prestations incluses. Chaque retraite affiche clairement son prix sur sa page de détails. Nous proposons également des options de paiement échelonné et des réductions pour réservation anticipée.",
            "type": "pricing",
            "links": [
                {"text": "Voir nos tarifs", "url": "/pricing"},
                {"text": "Options de paiement", "url": "/payment-options"}
            ]
        }
    
    elif "annulation" in question_lower or "remboursement" in question_lower:
        answer = {
            "text": "Notre politique d'annulation permet un remboursement complet jusqu'à 30 jours avant le début de la retraite. Entre 30 et 14 jours, 50% du montant est remboursable. Moins de 14 jours avant, aucun remboursement n'est possible, mais vous pouvez reporter votre participation à une date ultérieure moyennant des frais administratifs de 50€.",
            "type": "cancellation",
            "links": [
                {"text": "Politique d'annulation complète", "url": "/cancellation-policy"}
            ]
        }
    
    elif "hébergement" in question_lower or "logement" in question_lower or "chambre" in question_lower:
        answer = {
            "text": "Nos retraites proposent différentes options d'hébergement, allant de chambres partagées à des chambres individuelles ou des suites. Tous nos hébergements sont soigneusement sélectionnés pour leur confort, leur propreté et leur harmonie avec l'environnement. Les détails spécifiques sont indiqués sur la page de chaque retraite.",
            "type": "accommodation",
            "links": [
                {"text": "Types d'hébergement", "url": "/accommodation-types"}
            ]
        }
    
    elif "transport" in question_lower or "arrivée" in question_lower or "accès" in question_lower:
        answer = {
            "text": "Des informations détaillées sur l'accès au lieu de retraite sont fournies après la réservation. Pour certaines destinations, nous proposons des transferts depuis les aéroports ou gares les plus proches. Vous pouvez également organiser votre propre transport, et nous serons heureux de vous conseiller sur les meilleures options.",
            "type": "transport",
            "links": [
                {"text": "Options de transport", "url": "/transport-options"}
            ]
        }
    
    else:
        answer = {
            "text": "Merci pour votre question. Nous serions ravis de vous aider davantage. Pour obtenir une réponse plus précise, pourriez-vous nous donner plus de détails ou reformuler votre question? Vous pouvez également contacter notre service client par téléphone au +33 1 23 45 67 89 ou par email à <EMAIL>.",
            "type": "general",
            "links": [
                {"text": "FAQ", "url": "/faq"},
                {"text": "Contactez-nous", "url": "/contact"}
            ]
        }
    
    # Ajouter des métadonnées
    answer["question"] = question
    answer["timestamp"] = get_timestamp()
    
    return answer

def _provide_booking_assistance(task: Dict[str, Any]) -> Dict[str, Any]:
    """
    Fournir une assistance à la réservation.
    
    Args:
        task: Informations de la tâche
        
    Returns:
        Informations d'assistance à la réservation
    """
    # Extraire les informations de réservation
    booking_info = task.get("booking_info", {})
    retreat_id = booking_info.get("retreat_id")
    client_id = booking_info.get("client_id")
    
    # Simuler une assistance à la réservation
    assistance = {
        "booking_steps": [
            {
                "step": 1,
                "title": "Sélection de la retraite",
                "description": "Vous avez sélectionné une retraite. Vérifiez les dates et les détails avant de continuer.",
                "status": "completed" if retreat_id else "pending"
            },
            {
                "step": 2,
                "title": "Choix de l'hébergement",
                "description": "Sélectionnez le type d'hébergement qui vous convient (chambre partagée, individuelle, etc.).",
                "status": "pending"
            },
            {
                "step": 3,
                "title": "Informations personnelles",
                "description": "Renseignez vos informations personnelles et celles des autres participants si nécessaire.",
                "status": "pending"
            },
            {
                "step": 4,
                "title": "Options supplémentaires",
                "description": "Ajoutez des options supplémentaires comme des massages, des excursions, etc.",
                "status": "pending"
            },
            {
                "step": 5,
                "title": "Paiement",
                "description": "Procédez au paiement de l'acompte ou du montant total.",
                "status": "pending"
            }
        ],
        "available_accommodations": [
            {
                "type": "shared",
                "name": "Chambre partagée (2-3 personnes)",
                "price": 0,  # Prix de base inclus
                "availability": "disponible"
            },
            {
                "type": "double",
                "name": "Chambre double (occupation simple)",
                "price": 300,  # Supplément
                "availability": "disponible"
            },
            {
                "type": "single",
                "name": "Chambre individuelle",
                "price": 500,  # Supplément
                "availability": "limitée"
            }
        ],
        "payment_options": [
            {
                "type": "deposit",
                "name": "Acompte de 30%",
                "description": "Payez 30% maintenant et le reste 30 jours avant la retraite."
            },
            {
                "type": "full",
                "name": "Paiement complet",
                "description": "Payez la totalité maintenant et bénéficiez d'une réduction de 5%."
            },
            {
                "type": "installments",
                "name": "Paiement en 3 fois",
                "description": "Payez en 3 versements égaux (disponible uniquement pour les réservations à plus de 60 jours)."
            }
        ],
        "special_offers": [
            {
                "code": "EARLY2023",
                "description": "10% de réduction pour toute réservation effectuée 90 jours à l'avance.",
                "eligible": True
            },
            {
                "code": "FRIEND2023",
                "description": "5% de réduction pour chaque ami que vous invitez (jusqu'à 3 amis).",
                "eligible": True
            }
        ],
        "next_steps": "Pour continuer votre réservation, veuillez sélectionner un type d'hébergement et passer à l'étape suivante.",
        "support_contact": {
            "email": "<EMAIL>",
            "phone": "+33 1 23 45 67 89",
            "hours": "Lun-Ven, 9h-18h (CET)"
        }
    }
    
    return assistance
