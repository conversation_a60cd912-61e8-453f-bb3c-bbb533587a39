"""
Agent de planification de retraites.
Cet agent est responsable de la planification et de l'organisation des retraites de bien-être.
"""

import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from src.graph.types import State
from src.utils.time_utils import get_timestamp
from src.utils.error_utils import handle_error, ErrorType
from src.config import load_config
from src.models.retreat import Retreat, RetreatType, RetreatStatus, RetreatActivity, RetreatScheduleItem, RetreatPricing

logger = logging.getLogger(__name__)

async def retreat_planner_node(state: State) -> State:
    """
    Agent de planification de retraites.
    
    Cet agent est responsable de:
    - Analyser les besoins et préférences pour une retraite
    - Créer un programme détaillé pour la retraite
    - Définir les activités et le calendrier
    - Estimer les coûts et établir la tarification
    
    Args:
        state: L'état actuel du workflow
        
    Returns:
        État mis à jour du workflow
    """
    logger.info("Agent de planification de retraites en cours de traitement...")
    
    try:
        # Charger la configuration
        config = load_config()
        agent_config = config.get_agent_config("retreat_planner")
        
        # Enregistrer cette étape dans l'historique
        state.history.append({
            "agent": "retreat_planner",
            "action": "plan_retreat",
            "timestamp": get_timestamp()
        })
        
        # Récupérer les informations de la tâche
        task = state.task
        if not task:
            raise ValueError("Aucune tâche définie pour la planification de retraite")
        
        # Vérifier si nous avons déjà une retraite en cours de planification
        retreat = state.results.get("retreat")
        
        # Si nous n'avons pas encore de retraite, en créer une nouvelle
        if not retreat:
            retreat = _create_new_retreat(task)
            logger.info(f"Nouvelle retraite créée: {retreat['title']}")
        else:
            logger.info(f"Continuation de la planification pour la retraite: {retreat['title']}")
        
        # Planifier les activités de la retraite
        retreat = _plan_retreat_activities(retreat, task)
        
        # Établir la tarification
        retreat = _set_retreat_pricing(retreat, task)
        
        # Mettre à jour l'état avec la retraite planifiée
        state.results["retreat"] = retreat
        
        # Définir l'agent suivant
        state.next = "supervisor"
        
        logger.info(f"Planification de retraite terminée. Agent suivant: {state.next}")
        
    except Exception as e:
        logger.error(f"Erreur dans l'agent de planification de retraites: {str(e)}")
        state.error = handle_error(e, context={"agent": "retreat_planner"}, error_type=ErrorType.EXECUTION_ERROR)
        state.next = "supervisor"  # Laisser le superviseur gérer l'erreur
    
    return state

def _create_new_retreat(task: Dict[str, Any]) -> Dict[str, Any]:
    """
    Créer une nouvelle retraite basée sur les informations de la tâche.
    
    Args:
        task: Informations de la tâche
        
    Returns:
        Dictionnaire représentant la retraite
    """
    # Extraire les paramètres de la tâche
    params = task.get("parameters", {})
    retreat_type_str = params.get("retreat_type", "yoga")
    
    try:
        retreat_type = RetreatType(retreat_type_str)
    except ValueError:
        retreat_type = RetreatType.YOGA
    
    # Créer une nouvelle retraite
    start_date = datetime.now() + timedelta(days=30)  # Par défaut, dans 30 jours
    if params.get("start_date"):
        try:
            start_date = datetime.fromisoformat(params.get("start_date"))
        except ValueError:
            pass
    
    duration = params.get("duration", 7)  # Durée par défaut: 7 jours
    end_date = start_date + timedelta(days=duration)
    
    # Créer l'objet retraite
    retreat = Retreat(
        id=f"retreat_{uuid.uuid4().hex[:8]}",
        title=params.get("title", f"Retraite de {retreat_type.value}"),
        description=params.get("description", f"Une retraite de {retreat_type.value} pour se ressourcer et se reconnecter à soi-même."),
        retreat_type=retreat_type,
        status=RetreatStatus.DRAFT,
        start_date=start_date,
        end_date=end_date,
        location=params.get("location", {"country": "France", "region": "Provence"}),
        capacity=params.get("capacity", 10),
        language=params.get("language", "fr")
    )
    
    return retreat.to_dict()

def _plan_retreat_activities(retreat: Dict[str, Any], task: Dict[str, Any]) -> Dict[str, Any]:
    """
    Planifier les activités de la retraite.
    
    Args:
        retreat: Dictionnaire représentant la retraite
        task: Informations de la tâche
        
    Returns:
        Dictionnaire mis à jour de la retraite
    """
    # Extraire les paramètres de la tâche
    params = task.get("parameters", {})
    
    # Déterminer les activités en fonction du type de retraite
    retreat_type = retreat.get("retreat_type", "yoga")
    activities = []
    
    if retreat_type == "yoga":
        activities = [RetreatActivity.YOGA, RetreatActivity.MEDITATION, RetreatActivity.RELAXATION]
    elif retreat_type == "meditation":
        activities = [RetreatActivity.MEDITATION, RetreatActivity.SILENCE, RetreatActivity.YOGA]
    elif retreat_type == "wellness":
        activities = [RetreatActivity.YOGA, RetreatActivity.MASSAGE, RetreatActivity.RELAXATION]
    elif retreat_type == "fitness":
        activities = [RetreatActivity.FITNESS, RetreatActivity.HIKING, RetreatActivity.YOGA]
    elif retreat_type == "detox":
        activities = [RetreatActivity.YOGA, RetreatActivity.COOKING, RetreatActivity.MEDITATION]
    else:
        activities = [RetreatActivity.YOGA, RetreatActivity.MEDITATION, RetreatActivity.RELAXATION]
    
    # Ajouter des activités supplémentaires si spécifiées
    additional_activities = params.get("activities", [])
    for activity in additional_activities:
        try:
            activities.append(RetreatActivity(activity))
        except ValueError:
            logger.warning(f"Activité non reconnue: {activity}")
    
    # Mettre à jour les activités de la retraite
    retreat["activities"] = [activity.value for activity in activities]
    
    # Créer un programme pour la retraite
    schedule = []
    
    # Calculer la durée de la retraite
    start_date = datetime.fromisoformat(retreat.get("start_date"))
    end_date = datetime.fromisoformat(retreat.get("end_date"))
    duration = (end_date - start_date).days
    
    # Créer un programme pour chaque jour
    for day in range(1, duration + 1):
        # Matin: Yoga ou méditation
        schedule.append({
            "day": day,
            "start_time": "07:00",
            "end_time": "08:30",
            "activity": RetreatActivity.YOGA.value if RetreatActivity.YOGA.value in retreat["activities"] else RetreatActivity.MEDITATION.value,
            "title": "Yoga matinal" if RetreatActivity.YOGA.value in retreat["activities"] else "Méditation matinale",
            "description": "Commencez votre journée avec une séance revitalisante.",
            "location": "Salle principale",
            "is_optional": False
        })
        
        # Petit-déjeuner
        schedule.append({
            "day": day,
            "start_time": "08:30",
            "end_time": "09:30",
            "activity": "other",
            "title": "Petit-déjeuner",
            "description": "Petit-déjeuner sain et équilibré.",
            "location": "Restaurant",
            "is_optional": False
        })
        
        # Activité principale du matin
        main_morning_activity = RetreatActivity.WORKSHOP.value
        if RetreatActivity.HIKING.value in retreat["activities"] and day % 2 == 0:
            main_morning_activity = RetreatActivity.HIKING.value
        elif RetreatActivity.COOKING.value in retreat["activities"] and day % 3 == 0:
            main_morning_activity = RetreatActivity.COOKING.value
        
        schedule.append({
            "day": day,
            "start_time": "10:00",
            "end_time": "12:00",
            "activity": main_morning_activity,
            "title": f"Atelier du matin: {main_morning_activity.capitalize()}",
            "description": "Activité principale de la matinée.",
            "location": "Selon l'activité",
            "is_optional": False
        })
        
        # Déjeuner
        schedule.append({
            "day": day,
            "start_time": "12:30",
            "end_time": "14:00",
            "activity": "other",
            "title": "Déjeuner",
            "description": "Repas sain et équilibré.",
            "location": "Restaurant",
            "is_optional": False
        })
        
        # Temps libre
        schedule.append({
            "day": day,
            "start_time": "14:00",
            "end_time": "16:00",
            "activity": "other",
            "title": "Temps libre",
            "description": "Temps libre pour se détendre ou explorer les environs.",
            "location": "Au choix",
            "is_optional": True
        })
        
        # Activité de l'après-midi
        afternoon_activity = RetreatActivity.RELAXATION.value
        if RetreatActivity.MASSAGE.value in retreat["activities"] and day % 2 == 1:
            afternoon_activity = RetreatActivity.MASSAGE.value
        elif RetreatActivity.MEDITATION.value in retreat["activities"]:
            afternoon_activity = RetreatActivity.MEDITATION.value
        
        schedule.append({
            "day": day,
            "start_time": "16:00",
            "end_time": "17:30",
            "activity": afternoon_activity,
            "title": f"Session de l'après-midi: {afternoon_activity.capitalize()}",
            "description": "Activité de l'après-midi pour se ressourcer.",
            "location": "Salle de détente",
            "is_optional": False
        })
        
        # Dîner
        schedule.append({
            "day": day,
            "start_time": "19:00",
            "end_time": "20:30",
            "activity": "other",
            "title": "Dîner",
            "description": "Repas du soir sain et équilibré.",
            "location": "Restaurant",
            "is_optional": False
        })
        
        # Activité du soir
        evening_activity = RetreatActivity.MEDITATION.value
        if day == 1:
            evening_activity = "other"
            evening_title = "Cercle d'ouverture"
            evening_description = "Présentation et introduction à la retraite."
        elif day == duration:
            evening_activity = "other"
            evening_title = "Cercle de clôture"
            evening_description = "Partage d'expériences et clôture de la retraite."
        else:
            evening_title = "Méditation du soir"
            evening_description = "Session de méditation pour terminer la journée en douceur."
        
        schedule.append({
            "day": day,
            "start_time": "20:30",
            "end_time": "21:30",
            "activity": evening_activity,
            "title": evening_title,
            "description": evening_description,
            "location": "Salle principale",
            "is_optional": day not in [1, duration]  # Obligatoire le premier et dernier jour
        })
    
    # Mettre à jour le programme de la retraite
    retreat["schedule"] = schedule
    
    return retreat

def _set_retreat_pricing(retreat: Dict[str, Any], task: Dict[str, Any]) -> Dict[str, Any]:
    """
    Établir la tarification de la retraite.
    
    Args:
        retreat: Dictionnaire représentant la retraite
        task: Informations de la tâche
        
    Returns:
        Dictionnaire mis à jour de la retraite
    """
    # Extraire les paramètres de la tâche
    params = task.get("parameters", {})
    
    # Calculer la durée de la retraite
    start_date = datetime.fromisoformat(retreat.get("start_date"))
    end_date = datetime.fromisoformat(retreat.get("end_date"))
    duration = (end_date - start_date).days
    
    # Prix de base par jour selon le type de retraite
    base_price_per_day = 100  # Prix par défaut
    retreat_type = retreat.get("retreat_type")
    
    if retreat_type == "yoga":
        base_price_per_day = 120
    elif retreat_type == "meditation":
        base_price_per_day = 110
    elif retreat_type == "wellness":
        base_price_per_day = 150
    elif retreat_type == "fitness":
        base_price_per_day = 130
    elif retreat_type == "detox":
        base_price_per_day = 140
    
    # Ajuster le prix en fonction des paramètres
    if params.get("luxury_level") == "premium":
        base_price_per_day *= 1.5
    elif params.get("luxury_level") == "budget":
        base_price_per_day *= 0.8
    
    # Calculer le prix total
    base_price = base_price_per_day * duration
    
    # Créer la structure de tarification
    pricing = {
        "currency": params.get("currency", "EUR"),
        "base_price": base_price,
        "early_bird_price": base_price * 0.9,  # 10% de réduction pour réservation anticipée
        "early_bird_deadline": (start_date - timedelta(days=30)).isoformat(),  # 30 jours avant le début
        "deposit_amount": base_price * 0.3,  # 30% d'acompte
        "payment_deadline": (start_date - timedelta(days=7)).isoformat(),  # 7 jours avant le début
        "includes": [
            "Hébergement",
            "Repas",
            "Activités guidées",
            "Accès aux installations"
        ],
        "excludes": [
            "Transport vers le lieu de retraite",
            "Massages et soins supplémentaires",
            "Excursions optionnelles"
        ],
        "options": {
            "chambre_individuelle": base_price * 0.3,  # Supplément pour chambre individuelle
            "massage": 80,  # Prix par massage
            "excursion": 50  # Prix par excursion
        }
    }
    
    # Mettre à jour la tarification de la retraite
    retreat["pricing"] = pricing
    
    return retreat
