"""
Coordinator node implementation.
The coordinator is responsible for initializing the workflow and coordinating between agents.
"""

import logging
import uuid
from typing import Dict, Any

from src.graph.types import State
from src.utils.time_utils import get_timestamp
from src.utils.error_utils import handle_error, ErrorType
from src.config import load_config

logger = logging.getLogger(__name__)

async def coordinator_node(state: State) -> State:
    """
    Coordinator node implementation.

    The coordinator is responsible for:
    - Initializing the workflow
    - Setting up the initial task
    - Directing the workflow to the planner

    Args:
        state: The current workflow state

    Returns:
        Updated workflow state
    """
    logger.info("Coordinator node processing...")

    try:
        # Load configuration
        config = load_config()
        agent_config = config.get_agent_config("coordinator")

        # Record this step in history
        state.history.append({
            "agent": "coordinator",
            "action": "initialize_workflow",
            "timestamp": get_timestamp()
        })

        # Set the next agent to be the planner
        state.next = "planner"

        # Initialize task if not already set
        if not state.task:
            state.task = {
                "id": f"task_{uuid.uuid4().hex[:8]}",
                "type": "default",
                "status": "initialized",
                "priority": agent_config.parameters.get("priority", "normal") if agent_config else "normal"
            }

        logger.info(f"Coordinator completed. Next agent: {state.next}")

    except Exception as e:
        logger.error(f"Error in coordinator node: {str(e)}")
        state.error = handle_error(e, context={"agent": "coordinator"}, error_type=ErrorType.EXECUTION_ERROR)
        state.next = "supervisor"  # Let supervisor handle the error

    return state
