"""
Example script demonstrating how to use the agent workflow.
"""

import asyncio
import logging
from src.graph.workflow import run_workflow
from src.graph.types import State

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def main():
    """
    Run an example workflow.
    """
    print("Starting example workflow...")
    
    # Create an initial state with a specific task
    initial_state = State(
        task={
            "id": "example_task_001",
            "type": "code_analysis",
            "description": "Analyze a Python codebase for security vulnerabilities",
            "priority": "high"
        },
        context={
            "user": {
                "id": "user_123",
                "name": "Example User"
            },
            "project": {
                "id": "project_456",
                "name": "Security Analysis Project"
            }
        }
    )
    
    # Run the workflow
    final_state = await run_workflow(initial_state)
    
    # Print the results
    print("\nWorkflow completed!")
    print(f"Final state: next={final_state.next}")
    print("\nHistory:")
    for entry in final_state.history:
        print(f"  - {entry['agent']}: {entry['action']}")
    
    print("\nReport:")
    report = final_state.results.get("report", {})
    if report:
        print(f"  Title: {report.get('title')}")
        print(f"  Summary: {report.get('summary')}")
        print("  Sections:")
        for section in report.get("sections", []):
            print(f"    - {section.get('title')}")
        print(f"  Conclusion: {report.get('conclusion')}")
    else:
        print("  No report generated")

if __name__ == "__main__":
    asyncio.run(main())
