#!/bin/bash
# Example script demonstrating how to run workflows using the CLI

# Run with default configuration
echo "Running with default configuration..."
python -m src.cli

# Run with a specific task
echo -e "\nRunning with a specific task..."
python -m src.cli --task src/examples/tasks/code_analysis_task.yaml

# Run with custom configuration and task
echo -e "\nRunning with custom configuration and task..."
python -m src.cli --config src/config/default_config.yaml --task src/examples/tasks/web_research_task.yaml

# Save results to a file
echo -e "\nSaving results to a file..."
python -m src.cli --task src/examples/tasks/web_research_task.yaml --output results.json

# Run with debug logging
echo -e "\nRunning with debug logging..."
python -m src.cli --log-level DEBUG --task src/examples/tasks/code_analysis_task.yaml
