#!/usr/bin/env python3
"""
Script pour tester les fonctionnalités de résilience.
"""
import logging
import sys
import json
import time
import random
from services.communication_enhanced import CommunicationService
from utils.circuit_breaker import CircuitBreaker, CircuitBreakerError

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_circuit_breaker():
    """Teste le fonctionnement du circuit breaker."""
    logger.info("Test du circuit breaker...")
    
    # Récupérer l'instance du circuit breaker
    superagent_cb = CircuitBreaker.get_instance('superagent')
    
    # Réinitialiser le circuit breaker
    superagent_cb.reset()
    
    # Vérifier l'état initial
    logger.info(f"État initial du circuit breaker: {superagent_cb.get_state().value}")
    
    # Simuler des échecs consécutifs
    logger.info("Simulation de 5 échecs consécutifs...")
    
    for i in range(5):
        try:
            @superagent_cb
            def failing_function():
                raise Exception(f"Échec simulé #{i+1}")
            
            failing_function()
        except Exception as e:
            logger.info(f"Échec #{i+1}: {str(e)}")
            logger.info(f"État du circuit breaker: {superagent_cb.get_state().value}")
            logger.info(f"Nombre d'échecs: {superagent_cb.failure_count}")
    
    # Vérifier que le circuit est ouvert
    logger.info(f"État final du circuit breaker: {superagent_cb.get_state().value}")
    
    # Essayer d'appeler une fonction avec le circuit ouvert
    logger.info("Tentative d'appel avec le circuit ouvert...")
    
    try:
        @superagent_cb
        def another_function():
            return "Cette fonction ne devrait pas être appelée"
        
        another_function()
    except CircuitBreakerError as e:
        logger.info(f"Appel bloqué comme prévu: {str(e)}")
    
    # Attendre que le circuit passe à l'état semi-ouvert
    logger.info(f"Attente de {superagent_cb.recovery_timeout} secondes pour que le circuit passe à l'état semi-ouvert...")
    time.sleep(superagent_cb.recovery_timeout + 1)
    
    # Vérifier que le circuit est semi-ouvert
    logger.info(f"État du circuit breaker après attente: {superagent_cb.get_state().value}")
    
    # Essayer un appel réussi pour fermer le circuit
    logger.info("Tentative d'appel réussi pour fermer le circuit...")
    
    try:
        @superagent_cb
        def successful_function():
            return "Appel réussi"
        
        result = successful_function()
        logger.info(f"Résultat: {result}")
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
    
    # Vérifier que le circuit est fermé
    logger.info(f"État final du circuit breaker: {superagent_cb.get_state().value}")

def test_retry_mechanism():
    """Teste le mécanisme de retry."""
    logger.info("Test du mécanisme de retry...")
    
    # Créer une fonction qui échoue les 2 premières fois puis réussit
    failure_count = [0]
    
    def mock_request(*args, **kwargs):
        failure_count[0] += 1
        if failure_count[0] <= 2:
            logger.info(f"Échec simulé #{failure_count[0]}")
            raise requests.exceptions.RequestException(f"Échec simulé #{failure_count[0]}")
        logger.info("Succès simulé")
        return {"status": "success"}
    
    # Remplacer temporairement la méthode requests.request
    original_request = requests.request
    requests.request = mock_request
    
    try:
        # Appeler le service avec retry
        logger.info("Appel du service avec retry...")
        result = CommunicationService.call_superagent("test", method="GET", retry=True)
        logger.info(f"Résultat: {result}")
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
    finally:
        # Restaurer la méthode originale
        requests.request = original_request
    
    # Vérifier le nombre d'appels
    logger.info(f"Nombre total d'appels: {failure_count[0]}")

def main():
    """Fonction principale."""
    logger.info("Démarrage des tests de résilience...")
    
    # Tester le circuit breaker
    test_circuit_breaker()
    
    # Tester le mécanisme de retry
    test_retry_mechanism()
    
    logger.info("Tests de résilience terminés.")

if __name__ == "__main__":
    main()
