#!/usr/bin/env python3
"""
Exemple d'utilisation du service de communication.
"""
import logging
import sys
import json
from services.communication import CommunicationService

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_services_health():
    """Vérifie la santé des services."""
    logger.info("Vérification de la santé des services...")
    
    superagent_health = CommunicationService.check_superagent_health()
    agent_ia_health = CommunicationService.check_agent_ia_health()
    
    logger.info(f"Santé de superagent: {'OK' if superagent_health else 'KO'}")
    logger.info(f"Santé de Agent IA: {'OK' if agent_ia_health else 'KO'}")
    
    return superagent_health and agent_ia_health

def start_workflow_example():
    """Exemple de démarrage d'un workflow."""
    logger.info("Démarrage d'un workflow...")
    
    workflow_data = {
        "name": "retreat_planning",
        "input": {
            "location": "Provence, France",
            "duration": 7,
            "type": "yoga",
            "participants": 10
        }
    }
    
    try:
        result = CommunicationService.start_workflow(workflow_data)
        logger.info(f"Workflow démarré avec succès: {json.dumps(result, indent=2)}")
        return result.get("workflow_id")
    except Exception as e:
        logger.error(f"Erreur lors du démarrage du workflow: {str(e)}")
        return None

def analyze_text_example():
    """Exemple d'analyse de texte."""
    logger.info("Analyse de texte...")
    
    text = "Je cherche une retraite de yoga en Provence pour 7 jours en juillet pour 10 personnes."
    
    try:
        result = CommunicationService.analyze_text(text)
        logger.info(f"Analyse de texte réussie: {json.dumps(result, indent=2)}")
        return result
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse de texte: {str(e)}")
        return None

def get_recommendations_example():
    """Exemple de récupération de recommandations."""
    logger.info("Récupération de recommandations...")
    
    preferences = {
        "type": "yoga",
        "location": "France",
        "duration": 7,
        "maxPrice": 1500
    }
    
    try:
        result = CommunicationService.get_recommendations(preferences)
        logger.info(f"Recommandations récupérées avec succès: {json.dumps(result, indent=2)}")
        return result
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des recommandations: {str(e)}")
        return None

def main():
    """Fonction principale."""
    logger.info("Démarrage de l'exemple de communication...")
    
    # Vérification de la santé des services
    if not check_services_health():
        logger.error("Les services ne sont pas en bonne santé. Arrêt de l'exemple.")
        sys.exit(1)
    
    # Démarrage d'un workflow
    workflow_id = start_workflow_example()
    if workflow_id:
        logger.info(f"Workflow démarré avec l'ID: {workflow_id}")
    
    # Analyse de texte
    analysis = analyze_text_example()
    if analysis:
        logger.info(f"Entités détectées: {analysis.get('data', {}).get('entities', {})}")
    
    # Récupération de recommandations
    recommendations = get_recommendations_example()
    if recommendations:
        for recommendation in recommendations.get('data', []):
            logger.info(f"Recommandation: {recommendation.get('name')} - Score: {recommendation.get('match_score')}")
    
    logger.info("Exemple de communication terminé.")

if __name__ == "__main__":
    main()
