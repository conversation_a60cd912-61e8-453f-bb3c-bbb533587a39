"""
Routes pour les workflows.
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Body, status

from src.graph.types import State
from src.graph.retreat_workflow import run_retreat_workflow
from src.graph.partner_workflow import run_partner_workflow
from src.graph.client_workflow import run_client_workflow

# Configuration du logging
logger = logging.getLogger(__name__)

# Créer le router
router = APIRouter()

@router.post("/retreat-planning", status_code=status.HTTP_200_OK)
async def run_retreat_planning_workflow(
    task: Dict[str, Any] = Body(..., description="Tâche de planification de retraite")
):
    """
    Exécute le workflow de planification de retraites.
    """
    try:
        # Créer un état initial avec la tâche
        initial_state = State(
            task=task,
            context={
                "workflow_type": "retreat_planning"
            }
        )
        
        # Exécuter le workflow
        final_state = await run_retreat_workflow(initial_state)
        
        # Extraire les résultats
        results = final_state.results
        
        # Retourner les résultats
        return {
            "status": "success",
            "results": results,
            "history": final_state.history
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de l'exécution du workflow de planification de retraites: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de l'exécution du workflow: {str(e)}"
        )

@router.post("/partner-matching", status_code=status.HTTP_200_OK)
async def run_partner_matching_workflow(
    task: Dict[str, Any] = Body(..., description="Tâche de mise en relation de partenaires")
):
    """
    Exécute le workflow de mise en relation de partenaires.
    """
    try:
        # Créer un état initial avec la tâche
        initial_state = State(
            task=task,
            context={
                "workflow_type": "partner_matching"
            }
        )
        
        # Exécuter le workflow
        final_state = await run_partner_workflow(initial_state)
        
        # Extraire les résultats
        results = final_state.results
        
        # Retourner les résultats
        return {
            "status": "success",
            "results": results,
            "history": final_state.history
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de l'exécution du workflow de mise en relation de partenaires: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de l'exécution du workflow: {str(e)}"
        )

@router.post("/client-assistance", status_code=status.HTTP_200_OK)
async def run_client_assistance_workflow(
    task: Dict[str, Any] = Body(..., description="Tâche d'assistance client")
):
    """
    Exécute le workflow d'assistance client.
    """
    try:
        # Créer un état initial avec la tâche
        initial_state = State(
            task=task,
            context={
                "workflow_type": "client_assistance"
            }
        )
        
        # Exécuter le workflow
        final_state = await run_client_workflow(initial_state)
        
        # Extraire les résultats
        results = final_state.results
        
        # Retourner les résultats
        return {
            "status": "success",
            "results": results,
            "history": final_state.history
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de l'exécution du workflow d'assistance client: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de l'exécution du workflow: {str(e)}"
        )
