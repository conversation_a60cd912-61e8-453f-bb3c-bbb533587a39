"""
Routes pour les intégrations avec des services tiers.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Depends, Query, Path, Body, status
from datetime import datetime

from src.auth.dependencies import get_current_active_user
from src.auth.models import User
from src.integrations.calendar import GoogleCalendarService, ICalendarService
from src.integrations.maps import GoogleMapsService
from src.database.repositories.retreat_repository import RetreatRepository

# Configuration du logging
logger = logging.getLogger(__name__)

# Créer le router
router = APIRouter()

# Créer les services
google_calendar = GoogleCalendarService()
icalendar = ICalendarService()
google_maps = GoogleMapsService()

# Créer le repository des retraites
retreat_repo = RetreatRepository()

@router.post("/calendar/event", status_code=status.HTTP_200_OK)
async def create_calendar_event(
    retreat_id: str = Body(..., description="ID de la retraite"),
    calendar_id: Optional[str] = Body(None, description="ID du calendrier (optionnel)"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Crée un événement dans le calendrier pour une retraite.
    """
    try:
        # Récupérer la retraite
        retreat = retreat_repo.get_by_id(retreat_id)
        if not retreat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Retraite non trouvée: {retreat_id}"
            )
        
        # Créer l'événement
        event = await google_calendar.create_event(
            title=retreat.title,
            start_time=datetime.fromisoformat(retreat.start_date),
            end_time=datetime.fromisoformat(retreat.end_date),
            description=retreat.description,
            location=retreat.location.get("formatted_address", str(retreat.location)),
            calendar_id=calendar_id
        )
        
        return {
            "status": "success",
            "message": "Événement créé avec succès",
            "event": event
        }
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de la création de l'événement: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la création de l'événement"
        )

@router.get("/calendar/ical/{retreat_id}", status_code=status.HTTP_200_OK)
async def generate_ical(
    retreat_id: str = Path(..., description="ID de la retraite"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Génère un fichier iCalendar pour une retraite.
    """
    try:
        # Récupérer la retraite
        retreat = retreat_repo.get_by_id(retreat_id)
        if not retreat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Retraite non trouvée: {retreat_id}"
            )
        
        # Créer l'événement iCalendar
        event = await icalendar.create_event(
            title=retreat.title,
            start_time=datetime.fromisoformat(retreat.start_date),
            end_time=datetime.fromisoformat(retreat.end_date),
            description=retreat.description,
            location=retreat.location.get("formatted_address", str(retreat.location))
        )
        
        # Générer le fichier iCalendar
        ical_content = await icalendar.generate_ical(event["id"])
        
        return {
            "status": "success",
            "message": "Fichier iCalendar généré avec succès",
            "ical_content": ical_content,
            "file_path": event["file_path"]
        }
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de la génération du fichier iCalendar: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la génération du fichier iCalendar"
        )

@router.get("/maps/geocode", status_code=status.HTTP_200_OK)
async def geocode_address(
    address: str = Query(..., description="Adresse à géocoder"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Convertit une adresse en coordonnées géographiques.
    """
    try:
        # Géocoder l'adresse
        location = await google_maps.geocode(address)
        
        return {
            "status": "success",
            "message": "Adresse géocodée avec succès",
            "location": location
        }
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(f"Erreur lors du géocodage de l'adresse: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors du géocodage de l'adresse"
        )

@router.get("/maps/places", status_code=status.HTTP_200_OK)
async def find_places_nearby(
    location: str = Query(..., description="Adresse ou coordonnées"),
    radius: int = Query(1000, description="Rayon de recherche en mètres"),
    type: Optional[str] = Query(None, description="Type de lieu"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Recherche des lieux à proximité d'une adresse.
    """
    try:
        # Rechercher des lieux à proximité
        places = await google_maps.get_places_nearby(location, radius, type)
        
        return {
            "status": "success",
            "message": "Lieux trouvés avec succès",
            "places": places
        }
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de la recherche de lieux à proximité: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la recherche de lieux à proximité"
        )

@router.get("/maps/static", status_code=status.HTTP_200_OK)
async def generate_static_map(
    center: str = Query(..., description="Centre de la carte (adresse ou coordonnées)"),
    zoom: int = Query(14, description="Niveau de zoom"),
    width: int = Query(600, description="Largeur de la carte en pixels"),
    height: int = Query(400, description="Hauteur de la carte en pixels"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Génère une URL pour une carte statique.
    """
    try:
        # Générer l'URL de la carte statique
        url = google_maps.generate_static_map(center, zoom, (width, height))
        
        return {
            "status": "success",
            "message": "URL de carte statique générée avec succès",
            "url": url
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de la génération de l'URL de carte statique: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la génération de l'URL de carte statique"
        )

@router.get("/maps/directions", status_code=status.HTTP_200_OK)
async def generate_directions(
    origin: str = Query(..., description="Adresse d'origine"),
    destination: str = Query(..., description="Adresse de destination"),
    mode: Optional[str] = Query(None, description="Mode de transport"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Génère une URL pour un itinéraire.
    """
    try:
        # Générer l'URL de l'itinéraire
        url = google_maps.generate_directions_url(origin, destination, mode)
        
        # Calculer la distance et le temps de trajet
        distance = await google_maps.get_distance(origin, destination, mode)
        
        return {
            "status": "success",
            "message": "URL d'itinéraire générée avec succès",
            "url": url,
            "distance": distance
        }
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de la génération de l'URL d'itinéraire: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la génération de l'URL d'itinéraire"
        )
