"""
Routes pour l'API REST.
"""

from .partner_routes import router as partner_router
from .retreat_routes import router as retreat_router
from .client_routes import router as client_router
from .booking_routes import router as booking_router
from .workflow_routes import router as workflow_router
from .auth_routes import router as auth_router
from .payment_routes import router as payment_router
from .integration_routes import router as integration_router
from .analytics_routes import router as analytics_router
from .i18n_routes import router as i18n_router

__all__ = [
    "partner_router",
    "retreat_router",
    "client_router",
    "booking_router",
    "workflow_router",
    "auth_router",
    "payment_router",
    "integration_router",
    "analytics_router",
    "i18n_router"
]
