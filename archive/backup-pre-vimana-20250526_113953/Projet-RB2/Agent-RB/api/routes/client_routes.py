"""
Routes pour les clients.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query, Path, Body, status

from src.models.client import Client, ClientStatus, ClientPreference
from src.database.repositories import ClientRepository

# Configuration du logging
logger = logging.getLogger(__name__)

# Créer le router
router = APIRouter()

# Créer le repository
client_repo = ClientRepository()

@router.get("/", response_model=List[Client])
async def get_clients(
    status: Optional[str] = Query(None, description="Statut du client"),
    email: Optional[str] = Query(None, description="Email du client"),
    limit: int = Query(100, description="Nombre maximum de résultats"),
    offset: int = Query(0, description="Décalage pour la pagination")
):
    """
    Récupère la liste des clients avec filtrage optionnel.
    """
    try:
        # Appliquer les filtres
        if status:
            try:
                status_enum = ClientStatus(status)
                clients = client_repo.find_by_status(status_enum, limit, offset)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Statut invalide: {status}"
                )
        elif email:
            client = client_repo.find_by_email(email)
            clients = [client] if client else []
        else:
            clients = client_repo.get_all(limit, offset)
        
        return clients
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des clients: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des clients"
        )

@router.get("/{client_id}", response_model=Client)
async def get_client(
    client_id: str = Path(..., description="ID du client")
):
    """
    Récupère un client par son ID.
    """
    try:
        client = client_repo.get_by_id(client_id)
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Client non trouvé: {client_id}"
            )
        
        return client
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la récupération du client {client_id}"
        )

@router.post("/", response_model=Client, status_code=status.HTTP_201_CREATED)
async def create_client(
    client: Client = Body(..., description="Client à créer")
):
    """
    Crée un nouveau client.
    """
    try:
        # Vérifier si un client avec le même email existe déjà
        existing_client = client_repo.find_by_email(client.email)
        if existing_client:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Un client avec l'email {client.email} existe déjà"
            )
        
        # Créer le client
        created_client = client_repo.create(client)
        return created_client
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la création du client: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la création du client"
        )

@router.put("/{client_id}", response_model=Client)
async def update_client(
    client_id: str = Path(..., description="ID du client"),
    client: Client = Body(..., description="Client mis à jour")
):
    """
    Met à jour un client existant.
    """
    try:
        # Vérifier si le client existe
        existing_client = client_repo.get_by_id(client_id)
        if not existing_client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Client non trouvé: {client_id}"
            )
        
        # Vérifier que l'ID correspond
        if client.id != client_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="L'ID du client ne correspond pas à l'URL"
            )
        
        # Mettre à jour le client
        updated_client = client_repo.update(client)
        return updated_client
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour du client {client_id}"
        )

@router.delete("/{client_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_client(
    client_id: str = Path(..., description="ID du client")
):
    """
    Supprime un client.
    """
    try:
        # Vérifier si le client existe
        existing_client = client_repo.get_by_id(client_id)
        if not existing_client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Client non trouvé: {client_id}"
            )
        
        # Supprimer le client
        client_repo.delete(client_id)
        return None
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la suppression du client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la suppression du client {client_id}"
        )

@router.put("/{client_id}/preferences", response_model=Client)
async def update_client_preferences(
    client_id: str = Path(..., description="ID du client"),
    preferences: Dict[str, Any] = Body(..., description="Préférences mises à jour")
):
    """
    Met à jour les préférences d'un client.
    """
    try:
        # Mettre à jour les préférences
        updated_client = client_repo.update_preferences(client_id, preferences)
        if not updated_client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Client non trouvé: {client_id}"
            )
        
        return updated_client
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des préférences du client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour des préférences du client {client_id}"
        )

@router.post("/{client_id}/wishlist/{retreat_id}", response_model=Client)
async def add_to_wishlist(
    client_id: str = Path(..., description="ID du client"),
    retreat_id: str = Path(..., description="ID de la retraite")
):
    """
    Ajoute une retraite à la liste de souhaits d'un client.
    """
    try:
        # Vérifier si la retraite existe
        from src.database.repositories import RetreatRepository
        retreat_repo = RetreatRepository()
        retreat = retreat_repo.get_by_id(retreat_id)
        if not retreat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Retraite non trouvée: {retreat_id}"
            )
        
        # Ajouter à la liste de souhaits
        updated_client = client_repo.add_to_wishlist(client_id, retreat_id)
        if not updated_client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Client non trouvé: {client_id}"
            )
        
        return updated_client
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de l'ajout à la liste de souhaits du client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de l'ajout à la liste de souhaits du client {client_id}"
        )

@router.delete("/{client_id}/wishlist/{retreat_id}", response_model=Client)
async def remove_from_wishlist(
    client_id: str = Path(..., description="ID du client"),
    retreat_id: str = Path(..., description="ID de la retraite")
):
    """
    Retire une retraite de la liste de souhaits d'un client.
    """
    try:
        # Retirer de la liste de souhaits
        updated_client = client_repo.remove_from_wishlist(client_id, retreat_id)
        if not updated_client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Client non trouvé: {client_id}"
            )
        
        return updated_client
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors du retrait de la liste de souhaits du client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors du retrait de la liste de souhaits du client {client_id}"
        )
