"""
Routes pour les paiements.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Depends, Query, Path, Body, status

from src.auth.dependencies import get_current_active_user, has_permission
from src.auth.models import User, Permission
from src.payments.payment_service import PaymentService
from src.payments.stripe_provider import StripeProvider
from src.payments.payment_status import PaymentStatus, PaymentMethod, PaymentType
from src.database.repositories.booking_repository import BookingRepository

# Configuration du logging
logger = logging.getLogger(__name__)

# Créer le router
router = APIRouter()

# Créer le service de paiement
payment_provider = StripeProvider()
payment_service = PaymentService(payment_provider)

# Créer le repository des réservations
booking_repo = BookingRepository()

@router.post("/intent", status_code=status.HTTP_200_OK)
async def create_payment_intent(
    booking_id: str = Body(..., description="ID de la réservation"),
    amount: float = Body(..., description="Montant du paiement"),
    payment_type: PaymentType = Body(..., description="Type de paiement"),
    payment_methods: Optional[List[PaymentMethod]] = Body(None, description="Méthodes de paiement acceptées"),
    metadata: Optional[Dict[str, Any]] = Body(None, description="Métadonnées supplémentaires"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Crée une intention de paiement pour une réservation.
    """
    try:
        # Récupérer la réservation
        booking = booking_repo.get_by_id(booking_id)
        if not booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        # Vérifier que l'utilisateur est autorisé à effectuer le paiement
        if booking.client_id != current_user.id and current_user.role not in ["admin", "staff"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Vous n'êtes pas autorisé à effectuer ce paiement"
            )
        
        # Créer les informations sur le client
        customer_info = {
            "email": current_user.email,
            "name": current_user.full_name,
            "metadata": {
                "user_id": current_user.id
            }
        }
        
        # Créer l'intention de paiement
        intent_result = await payment_service.create_payment_intent(
            booking_id=booking_id,
            amount=amount,
            payment_type=payment_type,
            customer_info=customer_info,
            payment_methods=payment_methods,
            metadata=metadata
        )
        
        return intent_result
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de la création de l'intention de paiement: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la création de l'intention de paiement"
        )

@router.post("/process", status_code=status.HTTP_200_OK)
async def process_payment(
    booking_id: str = Body(..., description="ID de la réservation"),
    amount: float = Body(..., description="Montant du paiement"),
    payment_type: PaymentType = Body(..., description="Type de paiement"),
    payment_method: PaymentMethod = Body(..., description="Méthode de paiement"),
    metadata: Optional[Dict[str, Any]] = Body(None, description="Métadonnées supplémentaires"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Traite un paiement pour une réservation.
    """
    try:
        # Récupérer la réservation
        booking = booking_repo.get_by_id(booking_id)
        if not booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        # Vérifier que l'utilisateur est autorisé à effectuer le paiement
        if booking.client_id != current_user.id and current_user.role not in ["admin", "staff"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Vous n'êtes pas autorisé à effectuer ce paiement"
            )
        
        # Créer les informations sur le client
        customer_info = {
            "email": current_user.email,
            "name": current_user.full_name,
            "metadata": {
                "user_id": current_user.id
            }
        }
        
        # Traiter le paiement
        payment_result = await payment_service.process_payment(
            booking_id=booking_id,
            amount=amount,
            payment_type=payment_type,
            payment_method=payment_method,
            customer_info=customer_info,
            metadata=metadata
        )
        
        return payment_result
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(f"Erreur lors du traitement du paiement: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors du traitement du paiement"
        )

@router.post("/refund", status_code=status.HTTP_200_OK)
async def refund_payment(
    payment_id: str = Body(..., description="ID du paiement"),
    booking_id: str = Body(..., description="ID de la réservation"),
    amount: Optional[float] = Body(None, description="Montant à rembourser"),
    reason: Optional[str] = Body(None, description="Raison du remboursement"),
    current_user: User = Depends(has_permission(Permission.WRITE_BOOKINGS))
):
    """
    Rembourse un paiement.
    """
    try:
        # Récupérer la réservation
        booking = booking_repo.get_by_id(booking_id)
        if not booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        # Vérifier que l'utilisateur est autorisé à effectuer le remboursement
        if booking.client_id != current_user.id and current_user.role not in ["admin", "staff"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Vous n'êtes pas autorisé à effectuer ce remboursement"
            )
        
        # Effectuer le remboursement
        refund_result = await payment_service.refund_payment(
            payment_id=payment_id,
            booking_id=booking_id,
            amount=amount,
            reason=reason
        )
        
        return refund_result
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(f"Erreur lors du remboursement du paiement: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors du remboursement du paiement"
        )

@router.get("/status/{payment_id}", status_code=status.HTTP_200_OK)
async def get_payment_status(
    payment_id: str = Path(..., description="ID du paiement"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Récupère le statut d'un paiement.
    """
    try:
        # Récupérer le statut du paiement
        payment_status = await payment_service.get_payment_status(payment_id)
        
        return {
            "payment_id": payment_id,
            "status": payment_status.value
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du statut du paiement: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération du statut du paiement"
        )

@router.get("/booking/{booking_id}", status_code=status.HTTP_200_OK)
async def get_booking_payments(
    booking_id: str = Path(..., description="ID de la réservation"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Récupère les paiements d'une réservation.
    """
    try:
        # Récupérer la réservation
        booking = booking_repo.get_by_id(booking_id)
        if not booking:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Réservation non trouvée: {booking_id}"
            )
        
        # Vérifier que l'utilisateur est autorisé à voir les paiements
        if booking.client_id != current_user.id and current_user.role not in ["admin", "staff"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Vous n'êtes pas autorisé à voir ces paiements"
            )
        
        # Récupérer les paiements
        payments = booking.payments
        
        return {
            "booking_id": booking_id,
            "payments": payments
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des paiements de la réservation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des paiements de la réservation"
        )
