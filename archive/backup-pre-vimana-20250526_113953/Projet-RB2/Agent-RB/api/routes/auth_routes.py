"""
Routes pour l'authentification.
"""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import OAuth2PasswordRequestForm

from src.auth.models import User, User<PERSON><PERSON>, <PERSON>r<PERSON>og<PERSON>, Token, User<PERSON><PERSON>
from src.auth.jwt_auth import create_access_token, ACCESS_TOKEN_EXPIRE_MINUTES
from src.auth.dependencies import get_current_user, get_current_admin_user
from src.database.repositories.user_repository import UserRepository

# Configuration du logging
logger = logging.getLogger(__name__)

# Créer le router
router = APIRouter()

# Créer le repository des utilisateurs
user_repo = UserRepository()

@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    Authentifie un utilisateur et génère un token JWT.
    """
    # Authentifier l'utilisateur
    user = user_repo.authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Email ou mot de passe incorrect",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    # Vérifier que l'utilisateur est actif
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Utilisateur inactif"
        )
    
    # Créer le token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={
            "sub": user.id,
            "email": user.email,
            "role": user.role.value
        },
        expires_delta=access_token_expires
    )
    
    # Calculer la date d'expiration
    expires_at = int((datetime.utcnow() + access_token_expires).timestamp())
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_at=expires_at,
        user=user
    )

@router.post("/register", response_model=User, status_code=status.HTTP_201_CREATED)
async def register_user(user_data: UserCreate):
    """
    Enregistre un nouvel utilisateur.
    """
    try:
        # Créer l'utilisateur
        user = user_repo.create_user(
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name,
            role=user_data.role
        )
        
        return user
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de l'enregistrement de l'utilisateur: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de l'enregistrement de l'utilisateur"
        )

@router.get("/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """
    Récupère les informations de l'utilisateur actuel.
    """
    return current_user

@router.post("/change-password", response_model=User)
async def change_password(
    old_password: str,
    new_password: str,
    current_user: User = Depends(get_current_user)
):
    """
    Change le mot de passe de l'utilisateur actuel.
    """
    # Vérifier l'ancien mot de passe
    user = user_repo.authenticate_user(current_user.email, old_password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Mot de passe incorrect"
        )
    
    # Mettre à jour le mot de passe
    success = user_repo.update_password(current_user.id, new_password)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la mise à jour du mot de passe"
        )
    
    return current_user

@router.post("/users/{user_id}/change-role", response_model=User)
async def change_user_role(
    user_id: str,
    new_role: UserRole,
    current_user: User = Depends(get_current_admin_user)
):
    """
    Change le rôle d'un utilisateur (réservé aux administrateurs).
    """
    # Mettre à jour le rôle
    updated_user = user_repo.update_role(user_id, new_role)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Utilisateur non trouvé: {user_id}"
        )
    
    return updated_user

@router.post("/users/{user_id}/deactivate", response_model=User)
async def deactivate_user(
    user_id: str,
    current_user: User = Depends(get_current_admin_user)
):
    """
    Désactive un utilisateur (réservé aux administrateurs).
    """
    # Vérifier que l'utilisateur n'est pas en train de se désactiver lui-même
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Vous ne pouvez pas désactiver votre propre compte"
        )
    
    # Désactiver l'utilisateur
    updated_user = user_repo.deactivate_user(user_id)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Utilisateur non trouvé: {user_id}"
        )
    
    return updated_user

@router.post("/users/{user_id}/activate", response_model=User)
async def activate_user(
    user_id: str,
    current_user: User = Depends(get_current_admin_user)
):
    """
    Active un utilisateur (réservé aux administrateurs).
    """
    # Activer l'utilisateur
    updated_user = user_repo.activate_user(user_id)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Utilisateur non trouvé: {user_id}"
        )
    
    return updated_user
