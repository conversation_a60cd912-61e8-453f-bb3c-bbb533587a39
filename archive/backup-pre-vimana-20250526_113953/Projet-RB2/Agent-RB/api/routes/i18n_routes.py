"""
Routes pour l'internationalisation.
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Depends, Query, Path, Body, status

from src.auth.dependencies import get_current_active_user
from src.auth.models import User
from src.i18n import I18nService

# Configuration du logging
logger = logging.getLogger(__name__)

# C<PERSON>er le router
router = APIRouter()

# Créer le service d'internationalisation
i18n_service = I18nService()

@router.get("/languages", status_code=status.HTTP_200_OK)
async def get_languages():
    """
    Récupère la liste des langues supportées.
    """
    try:
        # Récupérer les langues
        languages = i18n_service.get_supported_languages()
        
        return {
            "status": "success",
            "message": "Langues récupérées avec succès",
            "languages": languages
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des langues: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des langues"
        )

@router.get("/translations/{language}", status_code=status.HTTP_200_OK)
async def get_translations(
    language: str = Path(..., description="Code de la langue")
):
    """
    Récupère les traductions pour une langue.
    """
    try:
        # Récupérer les traductions
        translations = i18n_service.get_translations(language)
        
        return {
            "status": "success",
            "message": "Traductions récupérées avec succès",
            "language": language,
            "translations": translations
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des traductions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des traductions"
        )

@router.get("/translate", status_code=status.HTTP_200_OK)
async def translate(
    key: str = Query(..., description="Clé de traduction"),
    language: str = Query(..., description="Code de la langue"),
    params: Optional[Dict[str, Any]] = Query(None, description="Paramètres de formatage")
):
    """
    Traduit une clé dans la langue spécifiée.
    """
    try:
        # Traduire la clé
        translation = i18n_service.translate(key, language, params)
        
        return {
            "status": "success",
            "message": "Traduction réussie",
            "key": key,
            "language": language,
            "translation": translation
        }
    
    except Exception as e:
        logger.error(f"Erreur lors de la traduction: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la traduction"
        )
