"""
Intégration avec Google Calendar.
"""

import os
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build

from .calendar_service import CalendarService

# Configuration du logging
logger = logging.getLogger(__name__)

class GoogleCalendarService(CalendarService):
    """
    Service d'intégration avec Google Calendar.
    """
    
    # Portées d'accès à l'API Google Calendar
    SCOPES = ['https://www.googleapis.com/auth/calendar']
    
    def __init__(self, credentials_file: Optional[str] = None, token_file: Optional[str] = None):
        """
        Initialise le service Google Calendar.
        
        Args:
            credentials_file: Chemin vers le fichier de credentials Google (optionnel)
            token_file: Chemin vers le fichier de token Google (optionnel)
        """
        self.credentials_file = credentials_file or os.environ.get("GOOGLE_CREDENTIALS_FILE", "credentials.json")
        self.token_file = token_file or os.environ.get("GOOGLE_TOKEN_FILE", "token.json")
        self.service = None
        
        # En mode développement, ne pas effectuer d'appels réels à l'API
        self.dev_mode = os.environ.get("ENVIRONMENT", "development") == "development"
    
    def _get_service(self):
        """
        Récupère le service Google Calendar.
        
        Returns:
            Service Google Calendar
        """
        if self.dev_mode:
            logger.info("[DEV MODE] Utilisation du service Google Calendar simulé")
            return None
        
        if self.service:
            return self.service
        
        creds = None
        
        # Charger les credentials depuis le fichier token.json s'il existe
        if os.path.exists(self.token_file):
            creds = Credentials.from_authorized_user_info(
                json.load(open(self.token_file)), self.SCOPES)
        
        # Si les credentials n'existent pas ou ne sont plus valides
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    self.credentials_file, self.SCOPES)
                creds = flow.run_local_server(port=0)
            
            # Sauvegarder les credentials pour la prochaine exécution
            with open(self.token_file, 'w') as token:
                token.write(creds.to_json())
        
        # Construire le service
        self.service = build('calendar', 'v3', credentials=creds)
        
        return self.service
    
    async def create_event(self, 
                         title: str, 
                         start_time: datetime, 
                         end_time: datetime, 
                         description: Optional[str] = None,
                         location: Optional[str] = None,
                         attendees: Optional[List[str]] = None,
                         calendar_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Crée un événement dans Google Calendar.
        
        Args:
            title: Titre de l'événement
            start_time: Date et heure de début
            end_time: Date et heure de fin
            description: Description de l'événement (optionnel)
            location: Lieu de l'événement (optionnel)
            attendees: Liste des adresses email des participants (optionnel)
            calendar_id: ID du calendrier (optionnel, utilise le calendrier principal si non spécifié)
            
        Returns:
            Informations sur l'événement créé
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Création d'un événement Google Calendar simulé: {title}")
            return {
                "id": f"event_{uuid.uuid4().hex[:8]}",
                "title": title,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "description": description,
                "location": location,
                "attendees": attendees,
                "calendar_id": calendar_id or "primary",
                "html_link": "https://calendar.google.com/calendar/event?eid=123456789"
            }
        
        try:
            # Préparer l'événement
            event = {
                'summary': title,
                'start': {
                    'dateTime': start_time.isoformat(),
                    'timeZone': 'Europe/Paris',
                },
                'end': {
                    'dateTime': end_time.isoformat(),
                    'timeZone': 'Europe/Paris',
                }
            }
            
            # Ajouter les champs optionnels
            if description:
                event['description'] = description
            
            if location:
                event['location'] = location
            
            if attendees:
                event['attendees'] = [{'email': email} for email in attendees]
            
            # Créer l'événement
            service = self._get_service()
            created_event = service.events().insert(
                calendarId=calendar_id or 'primary',
                body=event
            ).execute()
            
            # Formater la réponse
            return {
                "id": created_event['id'],
                "title": created_event['summary'],
                "start_time": created_event['start']['dateTime'],
                "end_time": created_event['end']['dateTime'],
                "description": created_event.get('description'),
                "location": created_event.get('location'),
                "attendees": [attendee['email'] for attendee in created_event.get('attendees', [])],
                "calendar_id": calendar_id or 'primary',
                "html_link": created_event['htmlLink']
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de la création de l'événement Google Calendar: {str(e)}")
            raise
    
    async def update_event(self, 
                         event_id: str, 
                         title: Optional[str] = None,
                         start_time: Optional[datetime] = None, 
                         end_time: Optional[datetime] = None, 
                         description: Optional[str] = None,
                         location: Optional[str] = None,
                         attendees: Optional[List[str]] = None,
                         calendar_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Met à jour un événement dans Google Calendar.
        
        Args:
            event_id: ID de l'événement
            title: Nouveau titre de l'événement (optionnel)
            start_time: Nouvelle date et heure de début (optionnel)
            end_time: Nouvelle date et heure de fin (optionnel)
            description: Nouvelle description de l'événement (optionnel)
            location: Nouveau lieu de l'événement (optionnel)
            attendees: Nouvelle liste des adresses email des participants (optionnel)
            calendar_id: ID du calendrier (optionnel, utilise le calendrier principal si non spécifié)
            
        Returns:
            Informations sur l'événement mis à jour
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Mise à jour d'un événement Google Calendar simulé: {event_id}")
            return {
                "id": event_id,
                "title": title or "Événement mis à jour",
                "start_time": start_time.isoformat() if start_time else "2023-01-01T10:00:00",
                "end_time": end_time.isoformat() if end_time else "2023-01-01T12:00:00",
                "description": description,
                "location": location,
                "attendees": attendees,
                "calendar_id": calendar_id or "primary",
                "html_link": "https://calendar.google.com/calendar/event?eid=123456789"
            }
        
        try:
            # Récupérer l'événement existant
            service = self._get_service()
            event = service.events().get(
                calendarId=calendar_id or 'primary',
                eventId=event_id
            ).execute()
            
            # Mettre à jour les champs
            if title:
                event['summary'] = title
            
            if start_time:
                event['start'] = {
                    'dateTime': start_time.isoformat(),
                    'timeZone': 'Europe/Paris',
                }
            
            if end_time:
                event['end'] = {
                    'dateTime': end_time.isoformat(),
                    'timeZone': 'Europe/Paris',
                }
            
            if description is not None:
                event['description'] = description
            
            if location is not None:
                event['location'] = location
            
            if attendees is not None:
                event['attendees'] = [{'email': email} for email in attendees]
            
            # Mettre à jour l'événement
            updated_event = service.events().update(
                calendarId=calendar_id or 'primary',
                eventId=event_id,
                body=event
            ).execute()
            
            # Formater la réponse
            return {
                "id": updated_event['id'],
                "title": updated_event['summary'],
                "start_time": updated_event['start']['dateTime'],
                "end_time": updated_event['end']['dateTime'],
                "description": updated_event.get('description'),
                "location": updated_event.get('location'),
                "attendees": [attendee['email'] for attendee in updated_event.get('attendees', [])],
                "calendar_id": calendar_id or 'primary',
                "html_link": updated_event['htmlLink']
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour de l'événement Google Calendar: {str(e)}")
            raise
    
    async def delete_event(self, 
                         event_id: str, 
                         calendar_id: Optional[str] = None) -> bool:
        """
        Supprime un événement de Google Calendar.
        
        Args:
            event_id: ID de l'événement
            calendar_id: ID du calendrier (optionnel, utilise le calendrier principal si non spécifié)
            
        Returns:
            True si l'événement a été supprimé, False sinon
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Suppression d'un événement Google Calendar simulé: {event_id}")
            return True
        
        try:
            # Supprimer l'événement
            service = self._get_service()
            service.events().delete(
                calendarId=calendar_id or 'primary',
                eventId=event_id
            ).execute()
            
            return True
        
        except Exception as e:
            logger.error(f"Erreur lors de la suppression de l'événement Google Calendar: {str(e)}")
            return False
    
    async def get_event(self, 
                      event_id: str, 
                      calendar_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Récupère les informations d'un événement Google Calendar.
        
        Args:
            event_id: ID de l'événement
            calendar_id: ID du calendrier (optionnel, utilise le calendrier principal si non spécifié)
            
        Returns:
            Informations sur l'événement
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Récupération d'un événement Google Calendar simulé: {event_id}")
            return {
                "id": event_id,
                "title": "Événement simulé",
                "start_time": "2023-01-01T10:00:00",
                "end_time": "2023-01-01T12:00:00",
                "description": "Description de l'événement simulé",
                "location": "Lieu de l'événement simulé",
                "attendees": ["<EMAIL>", "<EMAIL>"],
                "calendar_id": calendar_id or "primary",
                "html_link": "https://calendar.google.com/calendar/event?eid=123456789"
            }
        
        try:
            # Récupérer l'événement
            service = self._get_service()
            event = service.events().get(
                calendarId=calendar_id or 'primary',
                eventId=event_id
            ).execute()
            
            # Formater la réponse
            return {
                "id": event['id'],
                "title": event['summary'],
                "start_time": event['start']['dateTime'],
                "end_time": event['end']['dateTime'],
                "description": event.get('description'),
                "location": event.get('location'),
                "attendees": [attendee['email'] for attendee in event.get('attendees', [])],
                "calendar_id": calendar_id or 'primary',
                "html_link": event['htmlLink']
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de la récupération de l'événement Google Calendar: {str(e)}")
            raise
    
    async def list_events(self, 
                        start_time: Optional[datetime] = None, 
                        end_time: Optional[datetime] = None,
                        calendar_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Liste les événements dans une plage de dates dans Google Calendar.
        
        Args:
            start_time: Date et heure de début (optionnel)
            end_time: Date et heure de fin (optionnel)
            calendar_id: ID du calendrier (optionnel, utilise le calendrier principal si non spécifié)
            
        Returns:
            Liste des événements
        """
        if self.dev_mode:
            logger.info("[DEV MODE] Récupération des événements Google Calendar simulés")
            return [
                {
                    "id": f"event_{i}",
                    "title": f"Événement simulé {i}",
                    "start_time": "2023-01-01T10:00:00",
                    "end_time": "2023-01-01T12:00:00",
                    "description": f"Description de l'événement simulé {i}",
                    "location": f"Lieu de l'événement simulé {i}",
                    "attendees": ["<EMAIL>", "<EMAIL>"],
                    "calendar_id": calendar_id or "primary",
                    "html_link": f"https://calendar.google.com/calendar/event?eid={i}"
                }
                for i in range(1, 6)
            ]
        
        try:
            # Préparer les paramètres
            params = {
                'calendarId': calendar_id or 'primary',
                'maxResults': 100,
                'singleEvents': True,
                'orderBy': 'startTime'
            }
            
            if start_time:
                params['timeMin'] = start_time.isoformat() + 'Z'
            
            if end_time:
                params['timeMax'] = end_time.isoformat() + 'Z'
            
            # Récupérer les événements
            service = self._get_service()
            events_result = service.events().list(**params).execute()
            events = events_result.get('items', [])
            
            # Formater la réponse
            return [
                {
                    "id": event['id'],
                    "title": event['summary'],
                    "start_time": event['start'].get('dateTime', event['start'].get('date')),
                    "end_time": event['end'].get('dateTime', event['end'].get('date')),
                    "description": event.get('description'),
                    "location": event.get('location'),
                    "attendees": [attendee['email'] for attendee in event.get('attendees', [])],
                    "calendar_id": calendar_id or 'primary',
                    "html_link": event['htmlLink']
                }
                for event in events
            ]
        
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des événements Google Calendar: {str(e)}")
            raise
    
    async def generate_ical(self, 
                          event_id: str, 
                          calendar_id: Optional[str] = None) -> str:
        """
        Génère un fichier iCalendar pour un événement Google Calendar.
        
        Args:
            event_id: ID de l'événement
            calendar_id: ID du calendrier (optionnel, utilise le calendrier principal si non spécifié)
            
        Returns:
            Contenu du fichier iCalendar
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Génération d'un fichier iCalendar simulé pour l'événement: {event_id}")
            return """BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Retreat & Be//Calendar//FR
BEGIN:VEVENT
UID:<EMAIL>
DTSTAMP:20230101T100000Z
DTSTART:20230101T100000Z
DTEND:20230101T120000Z
SUMMARY:Événement simulé
DESCRIPTION:Description de l'événement simulé
LOCATION:Lieu de l'événement simulé
END:VEVENT
END:VCALENDAR"""
        
        try:
            # Récupérer l'événement
            event = await self.get_event(event_id, calendar_id)
            
            # Générer le fichier iCalendar
            from icalendar import Calendar, Event
            from datetime import datetime
            
            cal = Calendar()
            cal.add('prodid', '-//Retreat & Be//Calendar//FR')
            cal.add('version', '2.0')
            
            event_ical = Event()
            event_ical.add('summary', event['title'])
            event_ical.add('dtstart', datetime.fromisoformat(event['start_time'].replace('Z', '+00:00')))
            event_ical.add('dtend', datetime.fromisoformat(event['end_time'].replace('Z', '+00:00')))
            event_ical.add('dtstamp', datetime.now())
            event_ical.add('uid', f"{event['id']}@retreatandbe.com")
            
            if event.get('description'):
                event_ical.add('description', event['description'])
            
            if event.get('location'):
                event_ical.add('location', event['location'])
            
            cal.add_component(event_ical)
            
            return cal.to_ical().decode('utf-8')
        
        except Exception as e:
            logger.error(f"Erreur lors de la génération du fichier iCalendar: {str(e)}")
            raise
