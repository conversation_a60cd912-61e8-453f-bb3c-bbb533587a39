"""
Interface pour les services de cartographie.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple

class MapsService(ABC):
    """
    Interface pour les services de cartographie.
    """
    
    @abstractmethod
    async def geocode(self, address: str) -> Dict[str, Any]:
        """
        Convertit une adresse en coordonnées géographiques.
        
        Args:
            address: Adresse à géocoder
            
        Returns:
            Informations sur la localisation
        """
        pass
    
    @abstractmethod
    async def reverse_geocode(self, latitude: float, longitude: float) -> Dict[str, Any]:
        """
        Convertit des coordonnées géographiques en adresse.
        
        Args:
            latitude: Latitude
            longitude: Longitude
            
        Returns:
            Informations sur l'adresse
        """
        pass
    
    @abstractmethod
    async def get_distance(self, 
                         origin: str, 
                         destination: str, 
                         mode: Optional[str] = None) -> Dict[str, Any]:
        """
        Calcule la distance et le temps de trajet entre deux adresses.
        
        Args:
            origin: Adresse d'origine
            destination: Adresse de destination
            mode: Mode de transport (optionnel)
            
        Returns:
            Informations sur la distance et le temps de trajet
        """
        pass
    
    @abstractmethod
    async def get_places_nearby(self, 
                              location: str, 
                              radius: int = 1000, 
                              type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Recherche des lieux à proximité d'une adresse.
        
        Args:
            location: Adresse ou coordonnées
            radius: Rayon de recherche en mètres
            type: Type de lieu (optionnel)
            
        Returns:
            Liste des lieux à proximité
        """
        pass
    
    @abstractmethod
    def generate_static_map_url(self, 
                              center: str, 
                              zoom: int = 14, 
                              size: Tuple[int, int] = (600, 400),
                              markers: Optional[List[Dict[str, Any]]] = None) -> str:
        """
        Génère une URL pour une carte statique.
        
        Args:
            center: Centre de la carte (adresse ou coordonnées)
            zoom: Niveau de zoom
            size: Taille de la carte en pixels
            markers: Liste des marqueurs à afficher sur la carte (optionnel)
            
        Returns:
            URL de la carte statique
        """
        pass
    
    @abstractmethod
    def generate_directions_url(self, 
                              origin: str, 
                              destination: str, 
                              mode: Optional[str] = None) -> str:
        """
        Génère une URL pour un itinéraire.
        
        Args:
            origin: Adresse d'origine
            destination: Adresse de destination
            mode: Mode de transport (optionnel)
            
        Returns:
            URL de l'itinéraire
        """
        pass
