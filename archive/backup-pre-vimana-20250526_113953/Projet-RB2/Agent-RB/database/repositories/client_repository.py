"""
Repository pour l'accès aux données des clients.
"""

import logging
from typing import List, Dict, Any, Optional

from src.models.client import Client, ClientStatus
from src.database.connection import db_transaction
from .base_repository import BaseRepository

# Configuration du logging
logger = logging.getLogger(__name__)

class ClientRepository(BaseRepository[Client]):
    """
    Repository pour l'accès aux données des clients.
    """
    
    def __init__(self):
        """
        Initialise le repository des clients.
        """
        super().__init__('clients', Client)
    
    def find_by_email(self, email: str) -> Optional[Client]:
        """
        Recherche un client par son email.
        
        Args:
            email: Email du client
            
        Returns:
            Client correspondant ou None si non trouvé
        """
        clients = self.find_by({'email': email}, limit=1)
        return clients[0] if clients else None
    
    def find_by_status(self, status: ClientStatus, limit: int = 100, offset: int = 0) -> List[Client]:
        """
        Recherche des clients par statut.
        
        Args:
            status: Statut du client
            limit: Nombre maximum de clients à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des clients correspondants
        """
        return self.find_by({'status': status.value}, limit, offset)
    
    def find_by_retreat_interest(self, retreat_id: str, limit: int = 100, offset: int = 0) -> List[Client]:
        """
        Recherche des clients intéressés par une retraite.
        
        Args:
            retreat_id: ID de la retraite
            limit: Nombre maximum de clients à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des clients correspondants
        """
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE 
            wishlist LIKE ? OR
            upcoming_retreats LIKE ?
        LIMIT ? OFFSET ?
        """
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (
                f'%"{retreat_id}"%',
                f'%"{retreat_id}"%',
                limit, offset
            ))
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def find_by_preference(self, preference_key: str, preference_value: Any, limit: int = 100, offset: int = 0) -> List[Client]:
        """
        Recherche des clients par préférence.
        
        Args:
            preference_key: Clé de la préférence
            preference_value: Valeur de la préférence
            limit: Nombre maximum de clients à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des clients correspondants
        """
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE preferences LIKE ?
        LIMIT ? OFFSET ?
        """
        
        # Construire le motif de recherche
        if isinstance(preference_value, str):
            search_pattern = f'%"{preference_key}":"{preference_value}"%'
        elif isinstance(preference_value, (list, tuple)):
            search_pattern = f'%"{preference_key}":[%"{preference_value[0]}"%'
        else:
            search_pattern = f'%"{preference_key}":{preference_value}%'
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (search_pattern, limit, offset))
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def update_preferences(self, client_id: str, preferences: Dict[str, Any]) -> Optional[Client]:
        """
        Met à jour les préférences d'un client.
        
        Args:
            client_id: ID du client
            preferences: Nouvelles préférences
            
        Returns:
            Client mis à jour ou None si non trouvé
        """
        client = self.get_by_id(client_id)
        if not client:
            return None
        
        # Mettre à jour les préférences
        client.preferences = preferences
        
        # Sauvegarder les modifications
        return self.update(client)
    
    def add_to_wishlist(self, client_id: str, retreat_id: str) -> Optional[Client]:
        """
        Ajoute une retraite à la liste de souhaits d'un client.
        
        Args:
            client_id: ID du client
            retreat_id: ID de la retraite
            
        Returns:
            Client mis à jour ou None si non trouvé
        """
        client = self.get_by_id(client_id)
        if not client:
            return None
        
        # Ajouter la retraite à la liste de souhaits si elle n'y est pas déjà
        if retreat_id not in client.wishlist:
            client.wishlist.append(retreat_id)
            
            # Sauvegarder les modifications
            return self.update(client)
        
        return client
    
    def remove_from_wishlist(self, client_id: str, retreat_id: str) -> Optional[Client]:
        """
        Retire une retraite de la liste de souhaits d'un client.
        
        Args:
            client_id: ID du client
            retreat_id: ID de la retraite
            
        Returns:
            Client mis à jour ou None si non trouvé
        """
        client = self.get_by_id(client_id)
        if not client:
            return None
        
        # Retirer la retraite de la liste de souhaits si elle y est
        if retreat_id in client.wishlist:
            client.wishlist.remove(retreat_id)
            
            # Sauvegarder les modifications
            return self.update(client)
        
        return client
