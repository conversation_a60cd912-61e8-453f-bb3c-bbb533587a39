"""
Repository pour l'accès aux données des réservations.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from src.models.booking import Booking, BookingStatus, PaymentStatus
from src.database.connection import db_transaction
from .base_repository import BaseRepository

# Configuration du logging
logger = logging.getLogger(__name__)

class BookingRepository(BaseRepository[Booking]):
    """
    Repository pour l'accès aux données des réservations.
    """
    
    def __init__(self):
        """
        Initialise le repository des réservations.
        """
        super().__init__('bookings', Booking)
    
    def find_by_client(self, client_id: str, limit: int = 100, offset: int = 0) -> List[Booking]:
        """
        Recherche des réservations par client.
        
        Args:
            client_id: ID du client
            limit: Nombre maximum de réservations à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des réservations correspondantes
        """
        return self.find_by({'client_id': client_id}, limit, offset)
    
    def find_by_retreat(self, retreat_id: str, limit: int = 100, offset: int = 0) -> List[Booking]:
        """
        Recherche des réservations par retraite.
        
        Args:
            retreat_id: ID de la retraite
            limit: Nombre maximum de réservations à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des réservations correspondantes
        """
        return self.find_by({'retreat_id': retreat_id}, limit, offset)
    
    def find_by_status(self, status: BookingStatus, limit: int = 100, offset: int = 0) -> List[Booking]:
        """
        Recherche des réservations par statut.
        
        Args:
            status: Statut de la réservation
            limit: Nombre maximum de réservations à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des réservations correspondantes
        """
        return self.find_by({'status': status.value}, limit, offset)
    
    def find_by_payment_status(self, payment_status: PaymentStatus, limit: int = 100, offset: int = 0) -> List[Booking]:
        """
        Recherche des réservations par statut de paiement.
        
        Args:
            payment_status: Statut de paiement
            limit: Nombre maximum de réservations à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des réservations correspondantes
        """
        return self.find_by({'payment_status': payment_status.value}, limit, offset)
    
    def find_by_date_range(self, start_date: datetime, end_date: datetime, limit: int = 100, offset: int = 0) -> List[Booking]:
        """
        Recherche des réservations dans une plage de dates.
        
        Args:
            start_date: Date de début
            end_date: Date de fin
            limit: Nombre maximum de réservations à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des réservations correspondantes
        """
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE booking_date >= ? AND booking_date <= ?
        ORDER BY booking_date DESC
        LIMIT ? OFFSET ?
        """
        
        start_str = start_date.isoformat()
        end_str = end_date.isoformat()
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (start_str, end_str, limit, offset))
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def update_status(self, booking_id: str, status: BookingStatus) -> Optional[Booking]:
        """
        Met à jour le statut d'une réservation.
        
        Args:
            booking_id: ID de la réservation
            status: Nouveau statut
            
        Returns:
            Réservation mise à jour ou None si non trouvée
        """
        booking = self.get_by_id(booking_id)
        if not booking:
            return None
        
        # Mettre à jour le statut
        booking.status = status
        booking.updated_at = datetime.now()
        
        # Sauvegarder les modifications
        return self.update(booking)
    
    def update_payment_status(self, booking_id: str, payment_status: PaymentStatus) -> Optional[Booking]:
        """
        Met à jour le statut de paiement d'une réservation.
        
        Args:
            booking_id: ID de la réservation
            payment_status: Nouveau statut de paiement
            
        Returns:
            Réservation mise à jour ou None si non trouvée
        """
        booking = self.get_by_id(booking_id)
        if not booking:
            return None
        
        # Mettre à jour le statut de paiement
        booking.payment_status = payment_status
        booking.updated_at = datetime.now()
        
        # Sauvegarder les modifications
        return self.update(booking)
    
    def add_payment(self, booking_id: str, payment: Dict[str, Any]) -> Optional[Booking]:
        """
        Ajoute un paiement à une réservation.
        
        Args:
            booking_id: ID de la réservation
            payment: Informations sur le paiement
            
        Returns:
            Réservation mise à jour ou None si non trouvée
        """
        booking = self.get_by_id(booking_id)
        if not booking:
            return None
        
        # Ajouter le paiement
        booking.payments.append(payment)
        booking.updated_at = datetime.now()
        
        # Mettre à jour le statut de paiement si nécessaire
        total_paid = sum(p.get('amount', 0) for p in booking.payments)
        if total_paid >= booking.total_price:
            booking.payment_status = PaymentStatus.PAID
        elif total_paid > 0:
            booking.payment_status = PaymentStatus.PARTIAL
        
        # Sauvegarder les modifications
        return self.update(booking)
    
    def cancel_booking(self, booking_id: str, reason: str) -> Optional[Booking]:
        """
        Annule une réservation.
        
        Args:
            booking_id: ID de la réservation
            reason: Raison de l'annulation
            
        Returns:
            Réservation mise à jour ou None si non trouvée
        """
        booking = self.get_by_id(booking_id)
        if not booking:
            return None
        
        # Mettre à jour le statut et les informations d'annulation
        booking.status = BookingStatus.CANCELLED
        booking.cancellation_reason = reason
        booking.cancellation_date = datetime.now()
        booking.updated_at = datetime.now()
        
        # Sauvegarder les modifications
        return self.update(booking)
