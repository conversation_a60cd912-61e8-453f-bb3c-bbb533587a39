"""
Repository pour l'accès aux données des partenaires.
"""

import logging
from typing import List, Dict, Any, Optional

from src.models.partner import Partner, PartnerType, PartnerSpecialty
from src.database.connection import db_transaction
from .base_repository import BaseRepository

# Configuration du logging
logger = logging.getLogger(__name__)

class PartnerRepository(BaseRepository[Partner]):
    """
    Repository pour l'accès aux données des partenaires.
    """
    
    def __init__(self):
        """
        Initialise le repository des partenaires.
        """
        super().__init__('partners', Partner)
    
    def find_by_type(self, partner_type: PartnerType, limit: int = 100, offset: int = 0) -> List[Partner]:
        """
        Recherche des partenaires par type.
        
        Args:
            partner_type: Type de partenaire
            limit: Nombre maximum de partenaires à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des partenaires correspondants
        """
        return self.find_by({'partner_type': partner_type.value}, limit, offset)
    
    def find_by_specialty(self, specialty: PartnerSpecialty, limit: int = 100, offset: int = 0) -> List[Partner]:
        """
        Recherche des partenaires par spécialité.
        
        Args:
            specialty: Spécialité recherchée
            limit: Nombre maximum de partenaires à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des partenaires correspondants
        """
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE specialties LIKE ?
        LIMIT ? OFFSET ?
        """
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (f'%"{specialty.value}"%', limit, offset))
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def find_by_location(self, country: str, region: Optional[str] = None, limit: int = 100, offset: int = 0) -> List[Partner]:
        """
        Recherche des partenaires par localisation.
        
        Args:
            country: Pays
            region: Région (optionnel)
            limit: Nombre maximum de partenaires à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des partenaires correspondants
        """
        if region:
            query = f"""
            SELECT * FROM {self.table_name}
            WHERE location LIKE ? AND location LIKE ?
            LIMIT ? OFFSET ?
            """
            params = (f'%"country":"{country}"%', f'%"region":"{region}"%', limit, offset)
        else:
            query = f"""
            SELECT * FROM {self.table_name}
            WHERE location LIKE ?
            LIMIT ? OFFSET ?
            """
            params = (f'%"country":"{country}"%', limit, offset)
        
        with db_transaction() as conn:
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def find_by_rating(self, min_rating: float, limit: int = 100, offset: int = 0) -> List[Partner]:
        """
        Recherche des partenaires par note minimale.
        
        Args:
            min_rating: Note minimale
            limit: Nombre maximum de partenaires à récupérer
            offset: Décalage pour la pagination
            
        Returns:
            Liste des partenaires correspondants
        """
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE rating >= ?
        ORDER BY rating DESC
        LIMIT ? OFFSET ?
        """
        
        with db_transaction() as conn:
            cursor = conn.execute(query, (min_rating, limit, offset))
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
    
    def find_for_retreat(self, retreat_type: str, location: Dict[str, Any], activities: List[str], limit: int = 100) -> List[Partner]:
        """
        Recherche des partenaires adaptés pour une retraite.
        
        Args:
            retreat_type: Type de retraite
            location: Localisation de la retraite
            activities: Activités de la retraite
            limit: Nombre maximum de partenaires à récupérer
            
        Returns:
            Liste des partenaires correspondants
        """
        country = location.get('country', '')
        region = location.get('region', '')
        
        # Construire la requête pour trouver des partenaires adaptés
        query = f"""
        SELECT * FROM {self.table_name}
        WHERE 
            (partner_type = 'retreat_organizer' OR partner_type = 'wellness_expert') AND
            (specialties LIKE ? OR specialties LIKE ?) AND
            location LIKE ? AND
            status = 'active'
        ORDER BY rating DESC
        LIMIT ?
        """
        
        # Préparer les paramètres
        specialty_params = []
        for activity in activities:
            specialty_params.append(f'%"{activity}"%')
        
        # Si nous n'avons pas d'activités, utiliser le type de retraite
        if not specialty_params:
            specialty_params = [f'%"{retreat_type}"%']
        
        # Limiter à 2 paramètres pour la requête
        while len(specialty_params) < 2:
            specialty_params.append(specialty_params[0] if specialty_params else '%')
        
        params = (
            specialty_params[0],
            specialty_params[1],
            f'%"country":"{country}"%',
            limit
        )
        
        with db_transaction() as conn:
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
        
        return [self._deserialize(dict(row)) for row in rows]
