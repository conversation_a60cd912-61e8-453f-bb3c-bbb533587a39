"""
Configuration classes for workflow settings.
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field

@dataclass
class AgentConfig:
    """Configuration for an individual agent."""
    name: str
    enabled: bool = True
    timeout_seconds: int = 60
    max_retries: int = 3
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "name": self.name,
            "enabled": self.enabled,
            "timeout_seconds": self.timeout_seconds,
            "max_retries": self.max_retries,
            "parameters": self.parameters
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentConfig':
        """Create from dictionary."""
        return cls(
            name=data["name"],
            enabled=data.get("enabled", True),
            timeout_seconds=data.get("timeout_seconds", 60),
            max_retries=data.get("max_retries", 3),
            parameters=data.get("parameters", {})
        )

@dataclass
class WorkflowConfig:
    """Configuration for the entire workflow."""
    name: str
    description: Optional[str] = None
    version: str = "1.0.0"
    agents: List[AgentConfig] = field(default_factory=list)
    max_steps: int = 100
    logging_level: str = "INFO"
    enable_tracing: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "agents": [agent.to_dict() for agent in self.agents],
            "max_steps": self.max_steps,
            "logging_level": self.logging_level,
            "enable_tracing": self.enable_tracing
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowConfig':
        """Create from dictionary."""
        agents = [AgentConfig.from_dict(agent_data) for agent_data in data.get("agents", [])]
        
        return cls(
            name=data["name"],
            description=data.get("description"),
            version=data.get("version", "1.0.0"),
            agents=agents,
            max_steps=data.get("max_steps", 100),
            logging_level=data.get("logging_level", "INFO"),
            enable_tracing=data.get("enable_tracing", False)
        )
    
    def get_agent_config(self, agent_name: str) -> Optional[AgentConfig]:
        """Get configuration for a specific agent."""
        for agent in self.agents:
            if agent.name == agent_name:
                return agent
        return None
