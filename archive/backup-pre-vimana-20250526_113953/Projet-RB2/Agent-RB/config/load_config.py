"""
Functions for loading configuration from files.
"""

import os
import json
import yaml
import logging
from typing import Dict, Any, Optional

from .workflow_config import WorkflowConfig

logger = logging.getLogger(__name__)

def load_config(config_path: Optional[str] = None) -> WorkflowConfig:
    """
    Load configuration from a file.
    
    Args:
        config_path: Path to the configuration file (JSON or YAML)
                    If None, default configuration is returned
    
    Returns:
        WorkflowConfig object
    """
    if config_path is None:
        logger.info("No config path provided, using default configuration")
        return _get_default_config()
    
    if not os.path.exists(config_path):
        logger.warning(f"Config file not found: {config_path}, using default configuration")
        return _get_default_config()
    
    try:
        file_ext = os.path.splitext(config_path)[1].lower()
        
        if file_ext == '.json':
            with open(config_path, 'r') as f:
                config_data = json.load(f)
        elif file_ext in ['.yaml', '.yml']:
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f)
        else:
            logger.warning(f"Unsupported config file format: {file_ext}, using default configuration")
            return _get_default_config()
        
        logger.info(f"Loaded configuration from {config_path}")
        return WorkflowConfig.from_dict(config_data)
    
    except Exception as e:
        logger.error(f"Error loading config from {config_path}: {str(e)}")
        logger.info("Using default configuration")
        return _get_default_config()

def _get_default_config() -> WorkflowConfig:
    """
    Get the default configuration.
    
    Returns:
        Default WorkflowConfig object
    """
    from .workflow_config import AgentConfig
    
    # Create default agent configurations
    coordinator_config = AgentConfig(
        name="coordinator",
        timeout_seconds=30,
        parameters={"priority": "normal"}
    )
    
    planner_config = AgentConfig(
        name="planner",
        timeout_seconds=60,
        parameters={"max_plan_steps": 10}
    )
    
    supervisor_config = AgentConfig(
        name="supervisor",
        timeout_seconds=30,
        parameters={"check_interval": 5}
    )
    
    researcher_config = AgentConfig(
        name="researcher",
        timeout_seconds=120,
        parameters={"max_sources": 5}
    )
    
    coder_config = AgentConfig(
        name="coder",
        timeout_seconds=180,
        parameters={"languages": ["python", "javascript"]}
    )
    
    browser_config = AgentConfig(
        name="browser",
        timeout_seconds=120,
        parameters={"max_pages": 5}
    )
    
    reporter_config = AgentConfig(
        name="reporter",
        timeout_seconds=60,
        parameters={"format": "markdown"}
    )
    
    # Create default workflow configuration
    default_config = WorkflowConfig(
        name="default_workflow",
        description="Default workflow configuration",
        agents=[
            coordinator_config,
            planner_config,
            supervisor_config,
            researcher_config,
            coder_config,
            browser_config,
            reporter_config
        ]
    )
    
    return default_config
