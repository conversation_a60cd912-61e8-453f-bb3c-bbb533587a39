"""
Workflow spécifique pour les retraites de bien-être.
Ce module définit un workflow adapté à la planification et à l'organisation de retraites.
"""

from langgraph.graph import StateGraph, START
from src.graph.types import State
from src.agents.nodes import (
    coordinator_node,
    supervisor_node,
    retreat_planner_node,
    partner_matcher_node,
    reporter_node
)

def build_retreat_workflow():
    """
    Construire et retourner un workflow pour la planification de retraites.
    
    Ce workflow est spécialisé pour la planification et l'organisation de retraites de bien-être,
    en utilisant des agents spécifiques au domaine.
    
    Returns:
        Un StateGraph compilé qui peut être exécuté
    """
    builder = StateGraph(State)

    # Définir les nœuds
    builder.add_node("coordinator", coordinator_node)
    builder.add_node("supervisor", supervisor_node)
    builder.add_node("retreat_planner", retreat_planner_node)
    builder.add_node("partner_matcher", partner_matcher_node)
    builder.add_node("reporter", reporter_node)

    # Définir les arêtes
    builder.add_edge(START, "coordinator")
    builder.add_edge("coordinator", "retreat_planner")  # Coordinator -> Retreat Planner
    builder.add_edge("retreat_planner", "supervisor")   # Retreat Planner -> Supervisor
    
    # Arêtes conditionnelles depuis le superviseur vers d'autres agents
    builder.add_edge(
        "supervisor", 
        "retreat_planner", 
        condition=lambda state: state['next'] == "retreat_planner"
    )
    builder.add_edge(
        "supervisor", 
        "partner_matcher", 
        condition=lambda state: state['next'] == "partner_matcher"
    )
    builder.add_edge(
        "supervisor", 
        "reporter", 
        condition=lambda state: state['next'] == "reporter"
    )
    builder.add_edge(
        "supervisor", 
        "__end__", 
        condition=lambda state: state['next'] == "__end__"
    )
    
    # Tous les agents retournent au superviseur après leur exécution
    builder.add_edge("partner_matcher", "supervisor")
    builder.add_edge("reporter", "supervisor")

    # Définir le point d'entrée et le point de sortie
    builder.set_entry_point("coordinator")
    builder.add_end_point("__end__")

    return builder.compile()

async def run_retreat_workflow(initial_state=None):
    """
    Exécuter le workflow de retraite avec un état initial optionnel.
    
    Args:
        initial_state: État initial optionnel pour le workflow
        
    Returns:
        L'état final après l'exécution du workflow
    """
    graph = build_retreat_workflow()
    
    # Créer un état initial si non fourni
    if initial_state is None:
        initial_state = State()
    
    # Exécuter le workflow
    result = await graph.arun(initial_state)
    
    return result
