"""
Module d'authentification et d'autorisation.
"""

from .jwt_auth import create_access_token, decode_access_token, get_password_hash, verify_password
from .dependencies import get_current_user, get_current_active_user, get_current_admin_user
from .models import User, UserRole, UserCreate, UserLogin, Token, TokenData

__all__ = [
    "create_access_token",
    "decode_access_token",
    "get_password_hash",
    "verify_password",
    "get_current_user",
    "get_current_active_user",
    "get_current_admin_user",
    "User",
    "UserRole",
    "UserCreate",
    "UserLogin",
    "Token",
    "TokenData"
]
