"""
Modèles pour l'authentification et l'autorisation.
"""

from enum import Enum
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field

class UserRole(str, Enum):
    """Rôles des utilisateurs."""
    ADMIN = "admin"
    PARTNER = "partner"
    CLIENT = "client"
    STAFF = "staff"

class UserBase(BaseModel):
    """Modèle de base pour les utilisateurs."""
    email: EmailStr
    full_name: Optional[str] = None
    is_active: bool = True

class UserCreate(UserBase):
    """Modèle pour la création d'un utilisateur."""
    password: str
    role: UserRole = UserRole.CLIENT

class UserLogin(BaseModel):
    """Modèle pour la connexion d'un utilisateur."""
    email: EmailStr
    password: str

class User(UserBase):
    """Modèle complet d'un utilisateur."""
    id: str
    role: UserRole
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        orm_mode = True

class Token(BaseModel):
    """Modèle pour un token d'authentification."""
    access_token: str
    token_type: str = "bearer"
    expires_at: int  # Timestamp d'expiration
    user: User

class TokenData(BaseModel):
    """Données contenues dans un token."""
    sub: str  # ID de l'utilisateur
    email: EmailStr
    role: UserRole
    exp: int  # Timestamp d'expiration

class Permission(str, Enum):
    """Permissions possibles."""
    READ_RETREATS = "read:retreats"
    WRITE_RETREATS = "write:retreats"
    READ_PARTNERS = "read:partners"
    WRITE_PARTNERS = "write:partners"
    READ_CLIENTS = "read:clients"
    WRITE_CLIENTS = "write:clients"
    READ_BOOKINGS = "read:bookings"
    WRITE_BOOKINGS = "write:bookings"
    ADMIN = "admin"

# Définition des permissions par rôle
ROLE_PERMISSIONS = {
    UserRole.ADMIN: [
        Permission.READ_RETREATS,
        Permission.WRITE_RETREATS,
        Permission.READ_PARTNERS,
        Permission.WRITE_PARTNERS,
        Permission.READ_CLIENTS,
        Permission.WRITE_CLIENTS,
        Permission.READ_BOOKINGS,
        Permission.WRITE_BOOKINGS,
        Permission.ADMIN
    ],
    UserRole.PARTNER: [
        Permission.READ_RETREATS,
        Permission.WRITE_RETREATS,  # Uniquement ses propres retraites
        Permission.READ_PARTNERS,   # Uniquement son propre profil
        Permission.WRITE_PARTNERS,  # Uniquement son propre profil
        Permission.READ_BOOKINGS    # Uniquement ses propres réservations
    ],
    UserRole.CLIENT: [
        Permission.READ_RETREATS,
        Permission.READ_PARTNERS,
        Permission.READ_CLIENTS,    # Uniquement son propre profil
        Permission.WRITE_CLIENTS,   # Uniquement son propre profil
        Permission.READ_BOOKINGS,   # Uniquement ses propres réservations
        Permission.WRITE_BOOKINGS   # Uniquement ses propres réservations
    ],
    UserRole.STAFF: [
        Permission.READ_RETREATS,
        Permission.WRITE_RETREATS,
        Permission.READ_PARTNERS,
        Permission.READ_CLIENTS,
        Permission.READ_BOOKINGS,
        Permission.WRITE_BOOKINGS
    ]
}
