"""
Application FastAPI pour l'interface d'administration.
"""

import logging
from fastapi import <PERSON><PERSON><PERSON>, Depends, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
import os
from pathlib import Path

from src.auth.dependencies import get_current_admin_user
from src.auth.models import User
from .routes import (
    dashboard_router,
    retreats_router,
    partners_router,
    clients_router,
    bookings_router,
    users_router
)

# Configuration du logging
logger = logging.getLogger(__name__)

# Chemin vers les templates
templates_path = Path(__file__).parent / "templates"
static_path = Path(__file__).parent / "static"

# Créer les répertoires s'ils n'existent pas
templates_path.mkdir(exist_ok=True)
static_path.mkdir(exist_ok=True)

# Créer les templates
templates = Jinja2Templates(directory=str(templates_path))

def create_admin_app() -> FastAPI:
    """
    Crée et configure l'application FastAPI pour l'interface d'administration.
    
    Returns:
        Application FastAPI configurée
    """
    # Créer l'application FastAPI
    app = FastAPI(
        title="Administration Retraites de Bien-être",
        description="Interface d'administration pour le système de retraites de bien-être",
        version="1.0.0"
    )
    
    # Configurer CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # À remplacer par les origines autorisées en production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Monter les fichiers statiques
    app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
    
    # Ajouter les routes
    app.include_router(dashboard_router, prefix="/admin", tags=["admin"])
    app.include_router(retreats_router, prefix="/admin/retreats", tags=["admin"])
    app.include_router(partners_router, prefix="/admin/partners", tags=["admin"])
    app.include_router(clients_router, prefix="/admin/clients", tags=["admin"])
    app.include_router(bookings_router, prefix="/admin/bookings", tags=["admin"])
    app.include_router(users_router, prefix="/admin/users", tags=["admin"])
    
    @app.get("/", response_class=HTMLResponse)
    async def root():
        """
        Redirige vers l'interface d'administration.
        """
        return RedirectResponse(url="/admin")
    
    @app.get("/admin", response_class=HTMLResponse)
    async def admin_root(request: Request, current_user: User = Depends(get_current_admin_user)):
        """
        Page d'accueil de l'interface d'administration.
        """
        return templates.TemplateResponse(
            "dashboard.html",
            {
                "request": request,
                "user": current_user,
                "title": "Tableau de bord"
            }
        )
    
    return app
