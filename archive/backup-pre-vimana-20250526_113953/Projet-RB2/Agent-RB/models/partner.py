"""
Modèle de données pour les partenaires professionnels.
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime

class PartnerType(str, Enum):
    """Types de partenaires professionnels."""
    RETREAT_ORGANIZER = "retreat_organizer"  # Organisateur de retraites
    WELLNESS_EXPERT = "wellness_expert"      # Expert en bien-être (yoga, méditation, etc.)
    ACCOMMODATION = "accommodation"          # Hébergement (hôtel, villa, etc.)
    CATERING = "catering"                    # Service de restauration
    TRANSPORT = "transport"                  # Service de transport
    ACTIVITY_PROVIDER = "activity_provider"  # Fournisseur d'activités
    GUIDE = "guide"                          # Guide local
    OTHER = "other"                          # Autre type de partenaire

class PartnerStatus(str, Enum):
    """Statuts possibles pour un partenaire."""
    PENDING = "pending"           # En attente de validation
    ACTIVE = "active"             # Partenaire actif
    INACTIVE = "inactive"         # Partenaire inactif
    SUSPENDED = "suspended"       # Partenaire suspendu
    VERIFIED = "verified"         # Partenaire vérifié
    PREMIUM = "premium"           # Partenaire premium

class PartnerSpecialty(str, Enum):
    """Spécialités des partenaires."""
    YOGA = "yoga"
    MEDITATION = "meditation"
    NUTRITION = "nutrition"
    FITNESS = "fitness"
    MASSAGE = "massage"
    COACHING = "coaching"
    HIKING = "hiking"
    COOKING = "cooking"
    MINDFULNESS = "mindfulness"
    DANCE = "dance"
    ART_THERAPY = "art_therapy"
    MUSIC_THERAPY = "music_therapy"
    NATURE_THERAPY = "nature_therapy"
    SPIRITUAL = "spiritual"
    ADVENTURE = "adventure"
    CULTURAL = "cultural"
    DETOX = "detox"
    WELLNESS = "wellness"
    OTHER = "other"

@dataclass
class PartnerAvailability:
    """Disponibilité d'un partenaire."""
    start_date: datetime
    end_date: datetime
    status: str = "available"  # available, tentative, unavailable
    notes: Optional[str] = None

@dataclass
class PartnerReview:
    """Avis sur un partenaire."""
    client_id: str
    rating: float  # 1-5
    comment: Optional[str] = None
    date: datetime = field(default_factory=datetime.now)
    retreat_id: Optional[str] = None

@dataclass
class Partner:
    """
    Modèle de données pour un partenaire professionnel.
    """
    id: str
    name: str
    email: str
    phone: Optional[str] = None
    partner_type: PartnerType = PartnerType.OTHER
    status: PartnerStatus = PartnerStatus.PENDING
    specialties: List[PartnerSpecialty] = field(default_factory=list)
    description: Optional[str] = None
    bio: Optional[str] = None
    website: Optional[str] = None
    social_media: Dict[str, str] = field(default_factory=dict)
    address: Optional[Dict[str, Any]] = None
    location: Optional[Dict[str, float]] = None  # {latitude: float, longitude: float}
    languages: List[str] = field(default_factory=list)
    certifications: List[Dict[str, Any]] = field(default_factory=list)
    availability: List[PartnerAvailability] = field(default_factory=list)
    pricing: Dict[str, Any] = field(default_factory=dict)
    reviews: List[PartnerReview] = field(default_factory=list)
    rating: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire."""
        return {
            "id": self.id,
            "name": self.name,
            "email": self.email,
            "phone": self.phone,
            "partner_type": self.partner_type.value,
            "status": self.status.value,
            "specialties": [specialty.value for specialty in self.specialties],
            "description": self.description,
            "bio": self.bio,
            "website": self.website,
            "social_media": self.social_media,
            "address": self.address,
            "location": self.location,
            "languages": self.languages,
            "certifications": self.certifications,
            "availability": [
                {
                    "start_date": avail.start_date.isoformat(),
                    "end_date": avail.end_date.isoformat(),
                    "status": avail.status,
                    "notes": avail.notes
                }
                for avail in self.availability
            ],
            "pricing": self.pricing,
            "reviews": [
                {
                    "client_id": review.client_id,
                    "rating": review.rating,
                    "comment": review.comment,
                    "date": review.date.isoformat(),
                    "retreat_id": review.retreat_id
                }
                for review in self.reviews
            ],
            "rating": self.rating,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Partner':
        """Créer à partir d'un dictionnaire."""
        # Convertir les types énumérés
        partner_type = PartnerType(data.get("partner_type", PartnerType.OTHER.value))
        status = PartnerStatus(data.get("status", PartnerStatus.PENDING.value))
        specialties = [PartnerSpecialty(s) for s in data.get("specialties", [])]
        
        # Convertir les dates
        created_at = datetime.fromisoformat(data.get("created_at")) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data.get("updated_at")) if data.get("updated_at") else datetime.now()
        
        # Convertir les disponibilités
        availability = []
        for avail_data in data.get("availability", []):
            availability.append(PartnerAvailability(
                start_date=datetime.fromisoformat(avail_data.get("start_date")),
                end_date=datetime.fromisoformat(avail_data.get("end_date")),
                status=avail_data.get("status", "available"),
                notes=avail_data.get("notes")
            ))
        
        # Convertir les avis
        reviews = []
        for review_data in data.get("reviews", []):
            reviews.append(PartnerReview(
                client_id=review_data.get("client_id"),
                rating=review_data.get("rating", 0.0),
                comment=review_data.get("comment"),
                date=datetime.fromisoformat(review_data.get("date")) if review_data.get("date") else datetime.now(),
                retreat_id=review_data.get("retreat_id")
            ))
        
        return cls(
            id=data.get("id"),
            name=data.get("name"),
            email=data.get("email"),
            phone=data.get("phone"),
            partner_type=partner_type,
            status=status,
            specialties=specialties,
            description=data.get("description"),
            bio=data.get("bio"),
            website=data.get("website"),
            social_media=data.get("social_media", {}),
            address=data.get("address"),
            location=data.get("location"),
            languages=data.get("languages", []),
            certifications=data.get("certifications", []),
            availability=availability,
            pricing=data.get("pricing", {}),
            reviews=reviews,
            rating=data.get("rating", 0.0),
            created_at=created_at,
            updated_at=updated_at
        )
