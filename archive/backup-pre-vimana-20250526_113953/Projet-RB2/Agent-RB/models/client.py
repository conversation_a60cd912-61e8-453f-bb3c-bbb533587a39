"""
Modèle de données pour les clients.
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime

class ClientStatus(str, Enum):
    """Statuts possibles pour un client."""
    REGISTERED = "registered"     # Client inscrit
    ACTIVE = "active"             # Client actif
    INACTIVE = "inactive"         # Client inactif
    VERIFIED = "verified"         # Client vérifié
    PREMIUM = "premium"           # Client premium

@dataclass
class ClientPreference:
    """Préférences d'un client."""
    retreat_types: List[str] = field(default_factory=list)
    activities: List[str] = field(default_factory=list)
    locations: List[str] = field(default_factory=list)
    price_range: Dict[str, float] = field(default_factory=dict)  # {min: float, max: float}
    duration_range: Dict[str, int] = field(default_factory=dict)  # {min: int, max: int} en jours
    accommodation_types: List[str] = field(default_factory=list)
    dietary_restrictions: List[str] = field(default_factory=list)
    accessibility_needs: List[str] = field(default_factory=list)
    languages: List[str] = field(default_factory=list)
    travel_preferences: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Client:
    """
    Modèle de données pour un client.
    """
    id: str
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    status: ClientStatus = ClientStatus.REGISTERED
    preferences: ClientPreference = field(default_factory=ClientPreference)
    profile_picture: Optional[str] = None
    bio: Optional[str] = None
    date_of_birth: Optional[datetime] = None
    address: Optional[Dict[str, Any]] = None
    emergency_contact: Optional[Dict[str, str]] = None
    health_info: Optional[Dict[str, Any]] = None
    past_retreats: List[str] = field(default_factory=list)
    upcoming_retreats: List[str] = field(default_factory=list)
    wishlist: List[str] = field(default_factory=list)
    notifications_settings: Dict[str, bool] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    last_login: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire."""
        return {
            "id": self.id,
            "email": self.email,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "phone": self.phone,
            "status": self.status.value,
            "preferences": {
                "retreat_types": self.preferences.retreat_types,
                "activities": self.preferences.activities,
                "locations": self.preferences.locations,
                "price_range": self.preferences.price_range,
                "duration_range": self.preferences.duration_range,
                "accommodation_types": self.preferences.accommodation_types,
                "dietary_restrictions": self.preferences.dietary_restrictions,
                "accessibility_needs": self.preferences.accessibility_needs,
                "languages": self.preferences.languages,
                "travel_preferences": self.preferences.travel_preferences
            },
            "profile_picture": self.profile_picture,
            "bio": self.bio,
            "date_of_birth": self.date_of_birth.isoformat() if self.date_of_birth else None,
            "address": self.address,
            "emergency_contact": self.emergency_contact,
            "health_info": self.health_info,
            "past_retreats": self.past_retreats,
            "upcoming_retreats": self.upcoming_retreats,
            "wishlist": self.wishlist,
            "notifications_settings": self.notifications_settings,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_login": self.last_login.isoformat() if self.last_login else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Client':
        """Créer à partir d'un dictionnaire."""
        # Convertir les types énumérés
        status = ClientStatus(data.get("status", ClientStatus.REGISTERED.value))
        
        # Convertir les dates
        created_at = datetime.fromisoformat(data.get("created_at")) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data.get("updated_at")) if data.get("updated_at") else datetime.now()
        date_of_birth = datetime.fromisoformat(data.get("date_of_birth")) if data.get("date_of_birth") else None
        last_login = datetime.fromisoformat(data.get("last_login")) if data.get("last_login") else None
        
        # Convertir les préférences
        preferences_data = data.get("preferences", {})
        preferences = ClientPreference(
            retreat_types=preferences_data.get("retreat_types", []),
            activities=preferences_data.get("activities", []),
            locations=preferences_data.get("locations", []),
            price_range=preferences_data.get("price_range", {}),
            duration_range=preferences_data.get("duration_range", {}),
            accommodation_types=preferences_data.get("accommodation_types", []),
            dietary_restrictions=preferences_data.get("dietary_restrictions", []),
            accessibility_needs=preferences_data.get("accessibility_needs", []),
            languages=preferences_data.get("languages", []),
            travel_preferences=preferences_data.get("travel_preferences", {})
        )
        
        return cls(
            id=data.get("id", ""),
            email=data.get("email", ""),
            first_name=data.get("first_name"),
            last_name=data.get("last_name"),
            phone=data.get("phone"),
            status=status,
            preferences=preferences,
            profile_picture=data.get("profile_picture"),
            bio=data.get("bio"),
            date_of_birth=date_of_birth,
            address=data.get("address"),
            emergency_contact=data.get("emergency_contact"),
            health_info=data.get("health_info"),
            past_retreats=data.get("past_retreats", []),
            upcoming_retreats=data.get("upcoming_retreats", []),
            wishlist=data.get("wishlist", []),
            notifications_settings=data.get("notifications_settings", {}),
            created_at=created_at,
            updated_at=updated_at,
            last_login=last_login
        )
