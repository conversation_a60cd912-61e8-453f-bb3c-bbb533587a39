"""
Modèle de données pour les réservations.
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime

class BookingStatus(str, Enum):
    """Statuts possibles pour une réservation."""
    PENDING = "pending"           # En attente
    CONFIRMED = "confirmed"       # Confirmée
    CANCELLED = "cancelled"       # Annulée
    COMPLETED = "completed"       # Terminée
    REFUNDED = "refunded"         # Remboursée
    WAITING_LIST = "waiting_list" # Liste d'attente

class PaymentStatus(str, Enum):
    """Statuts possibles pour un paiement."""
    PENDING = "pending"           # En attente
    PARTIAL = "partial"           # Partiel
    PAID = "paid"                 # Payé
    FAILED = "failed"             # Échoué
    REFUNDED = "refunded"         # Remboursé
    CANCELLED = "cancelled"       # Annulé

@dataclass
class BookingParticipant:
    """Participant à une réservation."""
    first_name: str
    last_name: str
    email: str
    phone: Optional[str] = None
    date_of_birth: Optional[datetime] = None
    dietary_restrictions: List[str] = field(default_factory=list)
    accessibility_needs: List[str] = field(default_factory=list)
    emergency_contact: Optional[Dict[str, str]] = None
    health_info: Optional[Dict[str, Any]] = None
    is_primary: bool = False

@dataclass
class BookingPayment:
    """Paiement associé à une réservation."""
    id: str
    amount: float
    currency: str
    status: PaymentStatus
    payment_method: str
    transaction_id: Optional[str] = None
    payment_date: datetime = field(default_factory=datetime.now)
    notes: Optional[str] = None

@dataclass
class Booking:
    """
    Modèle de données pour une réservation.
    """
    id: str
    client_id: str
    retreat_id: str
    status: BookingStatus = BookingStatus.PENDING
    booking_date: datetime = field(default_factory=datetime.now)
    participants: List[BookingParticipant] = field(default_factory=list)
    accommodation_type: Optional[str] = None
    room_preference: Optional[str] = None
    total_price: float = 0.0
    currency: str = "EUR"
    payments: List[BookingPayment] = field(default_factory=list)
    payment_status: PaymentStatus = PaymentStatus.PENDING
    special_requests: Optional[str] = None
    arrival_details: Optional[Dict[str, Any]] = None
    departure_details: Optional[Dict[str, Any]] = None
    add_ons: List[Dict[str, Any]] = field(default_factory=list)
    cancellation_policy: Optional[Dict[str, Any]] = None
    cancellation_reason: Optional[str] = None
    cancellation_date: Optional[datetime] = None
    notes: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire."""
        return {
            "id": self.id,
            "client_id": self.client_id,
            "retreat_id": self.retreat_id,
            "status": self.status.value,
            "booking_date": self.booking_date.isoformat(),
            "participants": [
                {
                    "first_name": participant.first_name,
                    "last_name": participant.last_name,
                    "email": participant.email,
                    "phone": participant.phone,
                    "date_of_birth": participant.date_of_birth.isoformat() if participant.date_of_birth else None,
                    "dietary_restrictions": participant.dietary_restrictions,
                    "accessibility_needs": participant.accessibility_needs,
                    "emergency_contact": participant.emergency_contact,
                    "health_info": participant.health_info,
                    "is_primary": participant.is_primary
                }
                for participant in self.participants
            ],
            "accommodation_type": self.accommodation_type,
            "room_preference": self.room_preference,
            "total_price": self.total_price,
            "currency": self.currency,
            "payments": [
                {
                    "id": payment.id,
                    "amount": payment.amount,
                    "currency": payment.currency,
                    "status": payment.status.value,
                    "payment_method": payment.payment_method,
                    "transaction_id": payment.transaction_id,
                    "payment_date": payment.payment_date.isoformat(),
                    "notes": payment.notes
                }
                for payment in self.payments
            ],
            "payment_status": self.payment_status.value,
            "special_requests": self.special_requests,
            "arrival_details": self.arrival_details,
            "departure_details": self.departure_details,
            "add_ons": self.add_ons,
            "cancellation_policy": self.cancellation_policy,
            "cancellation_reason": self.cancellation_reason,
            "cancellation_date": self.cancellation_date.isoformat() if self.cancellation_date else None,
            "notes": self.notes,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Booking':
        """Créer à partir d'un dictionnaire."""
        # Convertir les types énumérés
        status = BookingStatus(data.get("status", BookingStatus.PENDING.value))
        payment_status = PaymentStatus(data.get("payment_status", PaymentStatus.PENDING.value))
        
        # Convertir les dates
        booking_date = datetime.fromisoformat(data.get("booking_date")) if data.get("booking_date") else datetime.now()
        created_at = datetime.fromisoformat(data.get("created_at")) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data.get("updated_at")) if data.get("updated_at") else datetime.now()
        cancellation_date = datetime.fromisoformat(data.get("cancellation_date")) if data.get("cancellation_date") else None
        
        # Convertir les participants
        participants = []
        for participant_data in data.get("participants", []):
            date_of_birth = datetime.fromisoformat(participant_data.get("date_of_birth")) if participant_data.get("date_of_birth") else None
            participants.append(BookingParticipant(
                first_name=participant_data.get("first_name", ""),
                last_name=participant_data.get("last_name", ""),
                email=participant_data.get("email", ""),
                phone=participant_data.get("phone"),
                date_of_birth=date_of_birth,
                dietary_restrictions=participant_data.get("dietary_restrictions", []),
                accessibility_needs=participant_data.get("accessibility_needs", []),
                emergency_contact=participant_data.get("emergency_contact"),
                health_info=participant_data.get("health_info"),
                is_primary=participant_data.get("is_primary", False)
            ))
        
        # Convertir les paiements
        payments = []
        for payment_data in data.get("payments", []):
            payment_date = datetime.fromisoformat(payment_data.get("payment_date")) if payment_data.get("payment_date") else datetime.now()
            payments.append(BookingPayment(
                id=payment_data.get("id", ""),
                amount=payment_data.get("amount", 0.0),
                currency=payment_data.get("currency", "EUR"),
                status=PaymentStatus(payment_data.get("status", PaymentStatus.PENDING.value)),
                payment_method=payment_data.get("payment_method", ""),
                transaction_id=payment_data.get("transaction_id"),
                payment_date=payment_date,
                notes=payment_data.get("notes")
            ))
        
        return cls(
            id=data.get("id", ""),
            client_id=data.get("client_id", ""),
            retreat_id=data.get("retreat_id", ""),
            status=status,
            booking_date=booking_date,
            participants=participants,
            accommodation_type=data.get("accommodation_type"),
            room_preference=data.get("room_preference"),
            total_price=data.get("total_price", 0.0),
            currency=data.get("currency", "EUR"),
            payments=payments,
            payment_status=payment_status,
            special_requests=data.get("special_requests"),
            arrival_details=data.get("arrival_details"),
            departure_details=data.get("departure_details"),
            add_ons=data.get("add_ons", []),
            cancellation_policy=data.get("cancellation_policy"),
            cancellation_reason=data.get("cancellation_reason"),
            cancellation_date=cancellation_date,
            notes=data.get("notes"),
            created_at=created_at,
            updated_at=updated_at
        )
