import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { catchError, firstValueFrom, map, timeout } from 'rxjs';
import { AxiosError } from 'axios';

/**
 * Service d'intégration avec le système de recommandation
 */
@Injectable()
export class RecommendationIntegrationService {
  private readonly logger = new Logger(RecommendationIntegrationService.name);
  private readonly baseUrl: string;
  private readonly timeoutMs: number = 30000; // 30 secondes par défaut

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get<string>('RECOMMENDATION_SERVICE_URL');
    
    if (!this.baseUrl) {
      this.logger.error('RECOMMENDATION_SERVICE_URL is not defined in environment variables');
      throw new Error('RECOMMENDATION_SERVICE_URL is not defined');
    }
    
    const configTimeout = this.configService.get<number>('RECOMMENDATION_TIMEOUT_MS');
    if (configTimeout) {
      this.timeoutMs = configTimeout;
    }
    
    this.logger.log(`RecommendationIntegrationService initialized with baseUrl: ${this.baseUrl}`);
  }

  /**
   * Récupère des recommandations personnalisées pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type d'élément à recommander (RETREAT, PARTNER, COURSE)
   * @param options Options de recommandation
   * @returns Recommandations personnalisées
   */
  async getPersonalizedRecommendations(
    userId: string,
    type: string = 'RETREAT',
    options: any = {},
  ): Promise<any> {
    this.logger.debug(`Getting personalized recommendations for user ${userId}, type ${type}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/v1/recommendations`, {
          params: {
            type,
            ...options,
          },
          headers: {
            'Authorization': `Bearer ${this.generateUserToken(userId)}`,
          },
        }).pipe(
          timeout(this.timeoutMs),
          map(res => res.data),
          catchError(this.handleError('getPersonalizedRecommendations')),
        ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error getting personalized recommendations: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère des recommandations tendance
   * @param type Type d'élément à recommander (RETREAT, PARTNER, COURSE)
   * @param options Options de recommandation
   * @returns Recommandations tendance
   */
  async getTrendingRecommendations(
    type: string = 'RETREAT',
    options: any = {},
  ): Promise<any> {
    this.logger.debug(`Getting trending recommendations for type ${type}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/v1/recommendations/trending`, {
          params: {
            type,
            ...options,
          },
          headers: {
            'Authorization': `Bearer ${this.generateServiceToken()}`,
          },
        }).pipe(
          timeout(this.timeoutMs),
          map(res => res.data),
          catchError(this.handleError('getTrendingRecommendations')),
        ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error getting trending recommendations: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère des éléments similaires à un élément spécifié
   * @param itemId ID de l'élément de référence
   * @param type Type d'élément (RETREAT, PARTNER, COURSE)
   * @param userId ID de l'utilisateur (optionnel)
   * @param options Options de recommandation
   * @returns Éléments similaires
   */
  async getSimilarItems(
    itemId: string,
    type: string = 'RETREAT',
    userId?: string,
    options: any = {},
  ): Promise<any> {
    this.logger.debug(`Getting similar items for ${type} ${itemId}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/v1/recommendations/similar/${type}/${itemId}`, {
          params: options,
          headers: {
            'Authorization': `Bearer ${userId ? this.generateUserToken(userId) : this.generateServiceToken()}`,
          },
        }).pipe(
          timeout(this.timeoutMs),
          map(res => res.data),
          catchError(this.handleError('getSimilarItems')),
        ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error getting similar items: ${error.message}`);
      throw error;
    }
  }

  /**
   * Enregistre une interaction utilisateur avec un élément
   * @param userId ID de l'utilisateur
   * @param itemId ID de l'élément
   * @param type Type d'élément (RETREAT, PARTNER, COURSE)
   * @param interactionType Type d'interaction (VIEW, LIKE, ENROLL, etc.)
   * @param metadata Métadonnées additionnelles de l'interaction
   * @returns Résultat de l'opération
   */
  async recordInteraction(
    userId: string,
    itemId: string,
    type: string,
    interactionType: string,
    metadata?: any,
  ): Promise<any> {
    this.logger.debug(`Recording interaction for user ${userId} with ${type} ${itemId}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/api/v1/recommendations/interactions`,
          {
            itemId,
            type,
            interactionType,
            metadata,
          },
          {
            headers: {
              'Authorization': `Bearer ${this.generateUserToken(userId)}`,
            },
          },
        ).pipe(
          timeout(this.timeoutMs),
          map(res => res.data),
          catchError(this.handleError('recordInteraction')),
        ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error recording interaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour les préférences de recommandation de l'utilisateur
   * @param userId ID de l'utilisateur
   * @param preferences Nouvelles préférences
   * @returns Préférences mises à jour
   */
  async updatePreferences(
    userId: string,
    preferences: any,
  ): Promise<any> {
    this.logger.debug(`Updating preferences for user ${userId}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/api/v1/recommendations/preferences`,
          preferences,
          {
            headers: {
              'Authorization': `Bearer ${this.generateUserToken(userId)}`,
            },
          },
        ).pipe(
          timeout(this.timeoutMs),
          map(res => res.data),
          catchError(this.handleError('updatePreferences')),
        ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error updating preferences: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un token JWT pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Token JWT
   */
  private generateUserToken(userId: string): string {
    // Dans un environnement réel, cette méthode devrait générer un vrai token JWT
    // Pour cet exemple, nous retournons un token factice
    return `user_token_${userId}`;
  }

  /**
   * Génère un token JWT pour le service
   * @returns Token JWT
   */
  private generateServiceToken(): string {
    // Dans un environnement réel, cette méthode devrait générer un vrai token JWT
    // Pour cet exemple, nous retournons un token factice
    return 'service_token';
  }

  /**
   * Gère les erreurs des requêtes HTTP
   * @param operation Nom de l'opération
   * @returns Fonction de gestion d'erreur
   */
  private handleError(operation: string) {
    return (error: AxiosError) => {
      this.logger.error(`${operation} failed: ${error.message}`);
      
      if (error.response) {
        this.logger.error(`Response status: ${error.response.status}`);
        this.logger.error(`Response data: ${JSON.stringify(error.response.data)}`);
      }
      
      throw error;
    };
  }
}
