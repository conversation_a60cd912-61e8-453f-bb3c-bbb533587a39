import { registerAs } from '@nestjs/config';

/**
 * Configuration pour l'intégration avec le système de recommandation
 */
export default registerAs('recommendation', () => ({
  /**
   * URL du service de recommandation
   */
  serviceUrl: process.env.RECOMMENDATION_SERVICE_URL || 'http://localhost:3001',
  
  /**
   * Timeout pour les requêtes au service de recommandation (en millisecondes)
   */
  timeoutMs: parseInt(process.env.RECOMMENDATION_TIMEOUT_MS || '30000', 10),
  
  /**
   * Nombre maximum de tentatives pour les requêtes au service de recommandation
   */
  maxRetries: parseInt(process.env.RECOMMENDATION_MAX_RETRIES || '3', 10),
  
  /**
   * Intervalle entre les tentatives (en millisecondes)
   */
  retryInterval: parseInt(process.env.RECOMMENDATION_RETRY_INTERVAL || '1000', 10),
  
  /**
   * Activer le cache pour les recommandations
   */
  enableCache: process.env.RECOMMENDATION_ENABLE_CACHE === 'true',
  
  /**
   * Durée de vie du cache (en secondes)
   */
  cacheTtl: parseInt(process.env.RECOMMENDATION_CACHE_TTL || '300', 10),
  
  /**
   * Nombre maximum d'éléments dans le cache
   */
  cacheMaxItems: parseInt(process.env.RECOMMENDATION_CACHE_MAX_ITEMS || '1000', 10),
  
  /**
   * Stratégie de recommandation par défaut
   */
  defaultStrategy: process.env.RECOMMENDATION_DEFAULT_STRATEGY || 'HYBRID',
  
  /**
   * Méthode hybride par défaut
   */
  defaultHybridMethod: process.env.RECOMMENDATION_DEFAULT_HYBRID_METHOD || 'WEIGHTED',
  
  /**
   * Nombre d'éléments par page par défaut
   */
  defaultLimit: parseInt(process.env.RECOMMENDATION_DEFAULT_LIMIT || '10', 10),
  
  /**
   * Types d'interactions à enregistrer automatiquement
   */
  autoTrackInteractions: (process.env.RECOMMENDATION_AUTO_TRACK_INTERACTIONS || 'VIEW,LIKE,ENROLL').split(','),
}));
