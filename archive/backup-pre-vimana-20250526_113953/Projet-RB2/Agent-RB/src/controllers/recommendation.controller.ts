import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { RecommendationIntegrationService } from '../services/recommendation-integration.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { Request } from 'express';

/**
 * Contrôleur pour les recommandations dans Agent-RB
 */
@Controller('api/recommendations')
export class RecommendationController {
  private readonly logger = new Logger(RecommendationController.name);

  constructor(
    private readonly recommendationService: RecommendationIntegrationService,
  ) {}

  /**
   * Récupère des recommandations personnalisées pour l'utilisateur connecté
   */
  @UseGuards(JwtAuthGuard)
  @Get('personalized')
  async getPersonalizedRecommendations(
    @Req() req: Request,
    @Query('type') type: string = 'RETREAT',
    @Query('limit') limit: number = 10,
    @Query('page') page: number = 1,
    @Query('filters') filtersStr?: string,
  ) {
    try {
      const userId = req.user['id'];
      
      // Convertir les filtres de string JSON en objet
      let filters = {};
      if (filtersStr) {
        try {
          filters = JSON.parse(filtersStr);
        } catch (error) {
          throw new HttpException('Invalid filters format', HttpStatus.BAD_REQUEST);
        }
      }
      
      const options = {
        limit,
        page,
        filters,
        includeMetadata: true,
      };
      
      return await this.recommendationService.getPersonalizedRecommendations(
        userId,
        type,
        options,
      );
    } catch (error) {
      this.logger.error(`Error getting personalized recommendations: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Error getting personalized recommendations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère des recommandations tendance
   */
  @Get('trending')
  async getTrendingRecommendations(
    @Query('type') type: string = 'RETREAT',
    @Query('limit') limit: number = 10,
    @Query('page') page: number = 1,
    @Query('filters') filtersStr?: string,
  ) {
    try {
      // Convertir les filtres de string JSON en objet
      let filters = {};
      if (filtersStr) {
        try {
          filters = JSON.parse(filtersStr);
        } catch (error) {
          throw new HttpException('Invalid filters format', HttpStatus.BAD_REQUEST);
        }
      }
      
      const options = {
        limit,
        page,
        filters,
        includeMetadata: true,
      };
      
      return await this.recommendationService.getTrendingRecommendations(
        type,
        options,
      );
    } catch (error) {
      this.logger.error(`Error getting trending recommendations: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Error getting trending recommendations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère des éléments similaires à un élément spécifié
   */
  @Get('similar/:type/:itemId')
  async getSimilarItems(
    @Param('type') type: string,
    @Param('itemId') itemId: string,
    @Query('limit') limit: number = 10,
    @Query('page') page: number = 1,
    @Query('filters') filtersStr?: string,
    @Req() req?: Request,
  ) {
    try {
      // Convertir les filtres de string JSON en objet
      let filters = {};
      if (filtersStr) {
        try {
          filters = JSON.parse(filtersStr);
        } catch (error) {
          throw new HttpException('Invalid filters format', HttpStatus.BAD_REQUEST);
        }
      }
      
      const options = {
        limit,
        page,
        filters,
        includeMetadata: true,
      };
      
      // Récupérer l'ID utilisateur si l'utilisateur est authentifié
      const userId = req.user ? req.user['id'] : undefined;
      
      return await this.recommendationService.getSimilarItems(
        itemId,
        type,
        userId,
        options,
      );
    } catch (error) {
      this.logger.error(`Error getting similar items: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Error getting similar items',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enregistre une interaction utilisateur avec un élément
   */
  @UseGuards(JwtAuthGuard)
  @Post('interactions')
  async recordInteraction(
    @Req() req: Request,
    @Body() body: {
      itemId: string;
      type: string;
      interactionType: string;
      metadata?: any;
    },
  ) {
    try {
      const userId = req.user['id'];
      
      // Valider les données d'entrée
      if (!body.itemId || !body.type || !body.interactionType) {
        throw new HttpException('Missing required fields', HttpStatus.BAD_REQUEST);
      }
      
      return await this.recommendationService.recordInteraction(
        userId,
        body.itemId,
        body.type,
        body.interactionType,
        body.metadata,
      );
    } catch (error) {
      this.logger.error(`Error recording interaction: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Error recording interaction',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Met à jour les préférences de recommandation de l'utilisateur
   */
  @UseGuards(JwtAuthGuard)
  @Post('preferences')
  async updatePreferences(
    @Req() req: Request,
    @Body() preferences: any,
  ) {
    try {
      const userId = req.user['id'];
      
      return await this.recommendationService.updatePreferences(
        userId,
        preferences,
      );
    } catch (error) {
      this.logger.error(`Error updating preferences: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Error updating preferences',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
