"""
Service d'envoi de notifications push.
"""

import logging
import os
import json
from typing import Dict, Any, Optional, List

from firebase_admin import messaging, initialize_app, credentials

# Configuration du logging
logger = logging.getLogger(__name__)

class PushSender:
    """
    Service d'envoi de notifications push.
    """
    
    def __init__(self, 
                credentials_file: Optional[str] = None,
                app_name: Optional[str] = None):
        """
        Initialise le service d'envoi de notifications push.
        
        Args:
            credentials_file: Chemin vers le fichier de credentials Firebase (optionnel)
            app_name: Nom de l'application Firebase (optionnel)
        """
        self.credentials_file = credentials_file or os.environ.get("FIREBASE_CREDENTIALS_FILE")
        self.app_name = app_name or "retreat-and-be"
        self.app = None
        
        # En mode développement, ne pas envoyer de notifications réelles
        self.dev_mode = os.environ.get("ENVIRONMENT", "development") == "development"
        
        # Initialiser Firebase si les credentials sont disponibles
        if not self.dev_mode and self.credentials_file and os.path.exists(self.credentials_file):
            cred = credentials.Certificate(self.credentials_file)
            self.app = initialize_app(cred, name=self.app_name)
    
    async def send_push(self, 
                      token: str, 
                      title: str, 
                      body: str, 
                      data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Envoie une notification push à un appareil.
        
        Args:
            token: Token de l'appareil
            title: Titre de la notification
            body: Corps de la notification
            data: Données supplémentaires (optionnel)
            
        Returns:
            Résultat de l'envoi
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Notification push qui serait envoyée à {token}: {title}")
            logger.debug(f"[DEV MODE] Corps de la notification: {body}")
            logger.debug(f"[DEV MODE] Données: {data}")
            return {
                "success": True,
                "message": "Notification push simulée en mode développement",
                "token": token,
                "title": title
            }
        
        try:
            # Vérifier que Firebase est initialisé
            if not self.app:
                raise ValueError("Firebase non initialisé")
            
            # Créer le message
            message = messaging.Message(
                notification=messaging.Notification(
                    title=title,
                    body=body
                ),
                data=data or {},
                token=token
            )
            
            # Envoyer le message
            response = messaging.send(message, app=self.app)
            
            logger.info(f"Notification push envoyée à {token}: {title}")
            
            return {
                "success": True,
                "message": "Notification push envoyée avec succès",
                "token": token,
                "title": title,
                "response": response
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de la notification push à {token}: {str(e)}")
            
            return {
                "success": False,
                "message": f"Erreur lors de l'envoi de la notification push: {str(e)}",
                "token": token,
                "title": title,
                "error": str(e)
            }
    
    async def send_push_to_topic(self, 
                               topic: str, 
                               title: str, 
                               body: str, 
                               data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Envoie une notification push à un sujet.
        
        Args:
            topic: Sujet
            title: Titre de la notification
            body: Corps de la notification
            data: Données supplémentaires (optionnel)
            
        Returns:
            Résultat de l'envoi
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Notification push qui serait envoyée au sujet {topic}: {title}")
            logger.debug(f"[DEV MODE] Corps de la notification: {body}")
            logger.debug(f"[DEV MODE] Données: {data}")
            return {
                "success": True,
                "message": "Notification push simulée en mode développement",
                "topic": topic,
                "title": title
            }
        
        try:
            # Vérifier que Firebase est initialisé
            if not self.app:
                raise ValueError("Firebase non initialisé")
            
            # Créer le message
            message = messaging.Message(
                notification=messaging.Notification(
                    title=title,
                    body=body
                ),
                data=data or {},
                topic=topic
            )
            
            # Envoyer le message
            response = messaging.send(message, app=self.app)
            
            logger.info(f"Notification push envoyée au sujet {topic}: {title}")
            
            return {
                "success": True,
                "message": "Notification push envoyée avec succès",
                "topic": topic,
                "title": title,
                "response": response
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de la notification push au sujet {topic}: {str(e)}")
            
            return {
                "success": False,
                "message": f"Erreur lors de l'envoi de la notification push: {str(e)}",
                "topic": topic,
                "title": title,
                "error": str(e)
            }
    
    async def send_push_to_multiple_tokens(self, 
                                         tokens: List[str], 
                                         title: str, 
                                         body: str, 
                                         data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Envoie une notification push à plusieurs appareils.
        
        Args:
            tokens: Liste des tokens des appareils
            title: Titre de la notification
            body: Corps de la notification
            data: Données supplémentaires (optionnel)
            
        Returns:
            Résultat de l'envoi
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Notification push qui serait envoyée à {len(tokens)} appareils: {title}")
            logger.debug(f"[DEV MODE] Corps de la notification: {body}")
            logger.debug(f"[DEV MODE] Données: {data}")
            return {
                "success": True,
                "message": "Notification push simulée en mode développement",
                "tokens": tokens,
                "title": title,
                "success_count": len(tokens),
                "failure_count": 0
            }
        
        try:
            # Vérifier que Firebase est initialisé
            if not self.app:
                raise ValueError("Firebase non initialisé")
            
            # Créer le message
            message = messaging.MulticastMessage(
                notification=messaging.Notification(
                    title=title,
                    body=body
                ),
                data=data or {},
                tokens=tokens
            )
            
            # Envoyer le message
            response = messaging.send_multicast(message, app=self.app)
            
            logger.info(f"Notification push envoyée à {len(tokens)} appareils: {title}")
            logger.info(f"Succès: {response.success_count}, Échecs: {response.failure_count}")
            
            return {
                "success": True,
                "message": "Notification push envoyée avec succès",
                "tokens": tokens,
                "title": title,
                "success_count": response.success_count,
                "failure_count": response.failure_count
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de la notification push à {len(tokens)} appareils: {str(e)}")
            
            return {
                "success": False,
                "message": f"Erreur lors de l'envoi de la notification push: {str(e)}",
                "tokens": tokens,
                "title": title,
                "error": str(e)
            }
    
    async def subscribe_to_topic(self, token: str, topic: str) -> Dict[str, Any]:
        """
        Abonne un appareil à un sujet.
        
        Args:
            token: Token de l'appareil
            topic: Sujet
            
        Returns:
            Résultat de l'abonnement
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Abonnement simulé de {token} au sujet {topic}")
            return {
                "success": True,
                "message": "Abonnement simulé en mode développement",
                "token": token,
                "topic": topic
            }
        
        try:
            # Vérifier que Firebase est initialisé
            if not self.app:
                raise ValueError("Firebase non initialisé")
            
            # Abonner l'appareil au sujet
            response = messaging.subscribe_to_topic(token, topic, app=self.app)
            
            logger.info(f"Appareil {token} abonné au sujet {topic}")
            
            return {
                "success": True,
                "message": "Appareil abonné avec succès",
                "token": token,
                "topic": topic,
                "response": response
            }
        
        except Exception as e:
            logger.error(f"Erreur lors de l'abonnement de l'appareil {token} au sujet {topic}: {str(e)}")
            
            return {
                "success": False,
                "message": f"Erreur lors de l'abonnement: {str(e)}",
                "token": token,
                "topic": topic,
                "error": str(e)
            }
    
    async def unsubscribe_from_topic(self, token: str, topic: str) -> Dict[str, Any]:
        """
        Désabonne un appareil d'un sujet.
        
        Args:
            token: Token de l'appareil
            topic: Sujet
            
        Returns:
            Résultat du désabonnement
        """
        if self.dev_mode:
            logger.info(f"[DEV MODE] Désabonnement simulé de {token} du sujet {topic}")
            return {
                "success": True,
                "message": "Désabonnement simulé en mode développement",
                "token": token,
                "topic": topic
            }
        
        try:
            # Vérifier que Firebase est initialisé
            if not self.app:
                raise ValueError("Firebase non initialisé")
            
            # Désabonner l'appareil du sujet
            response = messaging.unsubscribe_from_topic(token, topic, app=self.app)
            
            logger.info(f"Appareil {token} désabonné du sujet {topic}")
            
            return {
                "success": True,
                "message": "Appareil désabonné avec succès",
                "token": token,
                "topic": topic,
                "response": response
            }
        
        except Exception as e:
            logger.error(f"Erreur lors du désabonnement de l'appareil {token} du sujet {topic}: {str(e)}")
            
            return {
                "success": False,
                "message": f"Erreur lors du désabonnement: {str(e)}",
                "token": token,
                "topic": topic,
                "error": str(e)
            }
