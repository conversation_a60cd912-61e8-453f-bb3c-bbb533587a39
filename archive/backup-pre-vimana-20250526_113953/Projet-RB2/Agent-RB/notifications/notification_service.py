"""
Service de notification.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from .notification_types import NotificationType
from .email_sender import EmailSender
from .sms_sender import SMSSender
from .push_sender import PushSender

# Configuration du logging
logger = logging.getLogger(__name__)

class NotificationService:
    """
    Service de notification pour envoyer des notifications aux utilisateurs.
    """

    def __init__(self,
                 email_sender: Optional[EmailSender] = None,
                 sms_sender: Optional[SMSSender] = None,
                 push_sender: Optional[PushSender] = None):
        """
        Initialise le service de notification.

        Args:
            email_sender: Service d'envoi d'emails
            sms_sender: Service d'envoi de SMS
            push_sender: Service d'envoi de notifications push
        """
        self.email_sender = email_sender or EmailSender()
        self.sms_sender = sms_sender or SMSSender()
        self.push_sender = push_sender or PushSender()

    async def send_notification(self,
                               recipient: Dict[str, Any],
                               notification_type: NotificationType,
                               data: Dict[str, Any],
                               channels: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Envoie une notification à un destinataire.

        Args:
            recipient: Informations sur le destinataire
            notification_type: Type de notification
            data: Données de la notification
            channels: Canaux de notification (email, sms, push, etc.)

        Returns:
            Résultat de l'envoi de la notification
        """
        channels = channels or ["email"]
        results = {}

        # Préparer le contenu de la notification
        subject, content = self._prepare_content(notification_type, data)

        # Envoyer la notification par les canaux spécifiés
        if "email" in channels and recipient.get("email"):
            email_result = await self.email_sender.send_email(
                recipient["email"],
                subject,
                content
            )
            results["email"] = email_result

        if "sms" in channels and recipient.get("phone"):
            sms_result = await self.sms_sender.send_sms(
                recipient["phone"],
                self._prepare_sms_content(notification_type, data)
            )
            results["sms"] = sms_result

        if "push" in channels and recipient.get("device_token"):
            push_result = await self.push_sender.send_push(
                recipient["device_token"],
                subject,
                self._prepare_push_content(notification_type, data),
                data
            )
            results["push"] = push_result

        # Enregistrer la notification
        notification = {
            "recipient_id": recipient.get("id"),
            "notification_type": notification_type.value,
            "data": data,
            "channels": channels,
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"Notification envoyée: {notification_type.value} à {recipient.get('email')}")

        return notification

    async def send_bulk_notifications(self,
                                     recipients: List[Dict[str, Any]],
                                     notification_type: NotificationType,
                                     data: Dict[str, Any],
                                     channels: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Envoie une notification à plusieurs destinataires.

        Args:
            recipients: Liste des destinataires
            notification_type: Type de notification
            data: Données de la notification
            channels: Canaux de notification (email, sms, etc.)

        Returns:
            Résultats de l'envoi des notifications
        """
        results = []

        for recipient in recipients:
            result = await self.send_notification(recipient, notification_type, data, channels)
            results.append(result)

        return results

    def _prepare_content(self, notification_type: NotificationType, data: Dict[str, Any]) -> tuple:
        """
        Prépare le contenu de la notification.

        Args:
            notification_type: Type de notification
            data: Données de la notification

        Returns:
            Tuple (sujet, contenu)
        """
        templates = {
            NotificationType.RETREAT_CREATED: (
                "Nouvelle retraite créée: {retreat_title}",
                "Bonjour {recipient_name},\n\nUne nouvelle retraite a été créée: {retreat_title}.\n\nDates: {start_date} - {end_date}\nLieu: {location}\n\nPour plus d'informations, consultez notre site web.\n\nCordialement,\nL'équipe Retreat & Be"
            ),
            NotificationType.BOOKING_CREATED: (
                "Confirmation de votre réservation pour {retreat_title}",
                "Bonjour {recipient_name},\n\nNous avons bien reçu votre réservation pour la retraite: {retreat_title}.\n\nDates: {start_date} - {end_date}\nMontant: {total_price} {currency}\n\nVous recevrez bientôt un email avec tous les détails.\n\nCordialement,\nL'équipe Retreat & Be"
            ),
            NotificationType.PAYMENT_RECEIVED: (
                "Paiement reçu pour {retreat_title}",
                "Bonjour {recipient_name},\n\nNous avons bien reçu votre paiement de {amount} {currency} pour la retraite: {retreat_title}.\n\nMerci pour votre confiance.\n\nCordialement,\nL'équipe Retreat & Be"
            ),
            NotificationType.RETREAT_REMINDER: (
                "Rappel: Votre retraite {retreat_title} approche",
                "Bonjour {recipient_name},\n\nNous vous rappelons que votre retraite {retreat_title} commence bientôt.\n\nDates: {start_date} - {end_date}\nLieu: {location}\n\nN'hésitez pas à nous contacter si vous avez des questions.\n\nCordialement,\nL'équipe Retreat & Be"
            ),
            NotificationType.PARTNER_MATCHED: (
                "Nouveau partenaire trouvé pour {retreat_title}",
                "Bonjour {recipient_name},\n\nNous avons trouvé un nouveau partenaire pour votre retraite {retreat_title}:\n\nNom: {partner_name}\nType: {partner_type}\nSpécialité: {partner_specialty}\n\nVous pouvez le contacter à {partner_email}.\n\nCordialement,\nL'équipe Retreat & Be"
            ),
            NotificationType.WELCOME: (
                "Bienvenue sur Retreat & Be",
                "Bonjour {recipient_name},\n\nBienvenue sur Retreat & Be! Nous sommes ravis de vous compter parmi nos utilisateurs.\n\nVous pouvez dès maintenant explorer notre catalogue de retraites et trouver celle qui vous correspond.\n\nCordialement,\nL'équipe Retreat & Be"
            )
        }

        # Récupérer le template ou utiliser un template par défaut
        subject_template, content_template = templates.get(
            notification_type,
            ("Notification de Retreat & Be", "Bonjour {recipient_name},\n\n{message}\n\nCordialement,\nL'équipe Retreat & Be")
        )

        # Préparer les données pour le formatage
        format_data = {
            "recipient_name": data.get("recipient_name", ""),
            "message": data.get("message", ""),
            **data
        }

        # Formater le sujet et le contenu
        try:
            subject = subject_template.format(**format_data)
            content = content_template.format(**format_data)
        except KeyError as e:
            logger.warning(f"Données manquantes pour le formatage de la notification: {str(e)}")
            subject = subject_template
            content = f"Notification de Retreat & Be\n\n{data.get('message', '')}"

        return subject, content

    def _prepare_sms_content(self, notification_type: NotificationType, data: Dict[str, Any]) -> str:
        """
        Prépare le contenu SMS de la notification.

        Args:
            notification_type: Type de notification
            data: Données de la notification

        Returns:
            Contenu SMS
        """
        templates = {
            NotificationType.BOOKING_CREATED: "Retreat & Be: Réservation confirmée pour {retreat_title}. Montant: {total_price} {currency}.",
            NotificationType.PAYMENT_RECEIVED: "Retreat & Be: Paiement de {amount} {currency} reçu pour {retreat_title}.",
            NotificationType.RETREAT_REMINDER: "Retreat & Be: Rappel - Votre retraite {retreat_title} commence le {start_date}.",
            NotificationType.BOOKING_CANCELLED: "Retreat & Be: Votre réservation pour {retreat_title} a été annulée."
        }

        # Récupérer le template ou utiliser un template par défaut
        template = templates.get(
            notification_type,
            "Retreat & Be: {message}"
        )

        # Préparer les données pour le formatage
        format_data = {
            "message": data.get("message", ""),
            **data
        }

        # Formater le contenu
        try:
            content = template.format(**format_data)
        except KeyError as e:
            logger.warning(f"Données manquantes pour le formatage du SMS: {str(e)}")
            content = f"Retreat & Be: {data.get('message', '')}"

        # Limiter la longueur du SMS
        if len(content) > 160:
            content = content[:157] + "..."

        return content

    def _prepare_push_content(self, notification_type: NotificationType, data: Dict[str, Any]) -> str:
        """
        Prépare le contenu push de la notification.

        Args:
            notification_type: Type de notification
            data: Données de la notification

        Returns:
            Contenu push
        """
        templates = {
            NotificationType.BOOKING_CREATED: "Réservation confirmée pour {retreat_title}",
            NotificationType.PAYMENT_RECEIVED: "Paiement de {amount} {currency} reçu",
            NotificationType.RETREAT_REMINDER: "Votre retraite {retreat_title} commence bientôt",
            NotificationType.BOOKING_CANCELLED: "Réservation annulée pour {retreat_title}",
            NotificationType.RETREAT_CREATED: "Nouvelle retraite disponible: {retreat_title}",
            NotificationType.PARTNER_MATCHED: "Nouveau partenaire trouvé pour {retreat_title}"
        }

        # Récupérer le template ou utiliser un template par défaut
        template = templates.get(
            notification_type,
            "{message}"
        )

        # Préparer les données pour le formatage
        format_data = {
            "message": data.get("message", ""),
            **data
        }

        # Formater le contenu
        try:
            content = template.format(**format_data)
        except KeyError as e:
            logger.warning(f"Données manquantes pour le formatage de la notification push: {str(e)}")
            content = data.get("message", "Notification de Retreat & Be")

        return content
