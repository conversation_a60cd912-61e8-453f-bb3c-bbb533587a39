"""
Statuts et types de paiement.
"""

from enum import Enum

class PaymentStatus(str, Enum):
    """Statuts possibles pour un paiement."""
    PENDING = "pending"           # En attente
    PROCESSING = "processing"     # En cours de traitement
    COMPLETED = "completed"       # Terminé avec succès
    FAILED = "failed"             # Échoué
    REFUNDED = "refunded"         # Remboursé
    PARTIALLY_REFUNDED = "partially_refunded"  # Partiellement remboursé
    CANCELLED = "cancelled"       # Annulé

class PaymentMethod(str, Enum):
    """Méthodes de paiement possibles."""
    CREDIT_CARD = "credit_card"   # Carte de crédit
    BANK_TRANSFER = "bank_transfer"  # Virement bancaire
    PAYPAL = "paypal"             # PayPal
    APPLE_PAY = "apple_pay"       # Apple Pay
    GOOGLE_PAY = "google_pay"     # Google Pay
    CASH = "cash"                 # Espèces
    OTHER = "other"               # Autre

class PaymentType(str, Enum):
    """Types de paiement possibles."""
    DEPOSIT = "deposit"           # Acompte
    FULL_PAYMENT = "full_payment"  # Paiement complet
    INSTALLMENT = "installment"   # Paiement échelonné
    REFUND = "refund"             # Remboursement
