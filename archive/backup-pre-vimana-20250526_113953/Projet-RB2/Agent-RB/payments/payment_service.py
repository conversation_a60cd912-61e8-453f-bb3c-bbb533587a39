"""
Service de paiement.
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime

from .payment_provider import PaymentProvider
from .payment_status import PaymentStatus, PaymentMethod, PaymentType
from src.database.repositories.booking_repository import BookingRepository

# Configuration du logging
logger = logging.getLogger(__name__)

class PaymentService:
    """
    Service de paiement pour le système de retraites de bien-être.
    """
    
    def __init__(self, payment_provider: PaymentProvider):
        """
        Initialise le service de paiement.
        
        Args:
            payment_provider: Fournisseur de paiement
        """
        self.payment_provider = payment_provider
        self.booking_repo = BookingRepository()
    
    async def process_payment(self, 
                            booking_id: str, 
                            amount: float,
                            payment_type: PaymentType,
                            payment_method: PaymentMethod,
                            customer_info: Dict[str, Any],
                            metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Traite un paiement pour une réservation.
        
        Args:
            booking_id: ID de la réservation
            amount: Montant du paiement
            payment_type: Type de paiement
            payment_method: Méthode de paiement
            customer_info: Informations sur le client
            metadata: Métadonnées supplémentaires
            
        Returns:
            Informations sur le paiement
        """
        try:
            # Récupérer la réservation
            booking = self.booking_repo.get_by_id(booking_id)
            if not booking:
                raise ValueError(f"Réservation non trouvée: {booking_id}")
            
            # Vérifier que le montant est valide
            if amount <= 0:
                raise ValueError("Le montant du paiement doit être supérieur à 0")
            
            # Vérifier que le montant ne dépasse pas le montant total de la réservation
            if payment_type == PaymentType.FULL_PAYMENT and amount > booking.total_price:
                raise ValueError(f"Le montant du paiement ({amount}) dépasse le montant total de la réservation ({booking.total_price})")
            
            # Créer les métadonnées
            payment_metadata = {
                "booking_id": booking_id,
                "retreat_id": booking.retreat_id,
                "client_id": booking.client_id,
                "payment_type": payment_type.value,
                **(metadata or {})
            }
            
            # Créer le paiement
            payment_result = await self.payment_provider.create_payment(
                amount=amount,
                currency=booking.currency,
                description=f"Paiement pour la réservation {booking_id}",
                customer_info=customer_info,
                payment_type=payment_type,
                metadata=payment_metadata
            )
            
            # Créer l'objet de paiement
            payment = {
                "id": payment_result.get("id"),
                "amount": amount,
                "currency": booking.currency,
                "payment_method": payment_method.value,
                "payment_type": payment_type.value,
                "status": payment_result.get("status"),
                "transaction_id": payment_result.get("id"),
                "payment_date": datetime.now().isoformat(),
                "metadata": payment_metadata
            }
            
            # Ajouter le paiement à la réservation
            updated_booking = self.booking_repo.add_payment(booking_id, payment)
            
            # Mettre à jour le statut de la réservation si nécessaire
            if payment_result.get("status") == PaymentStatus.COMPLETED.value:
                if payment_type == PaymentType.FULL_PAYMENT:
                    self.booking_repo.update_status(booking_id, "confirmed")
                elif payment_type == PaymentType.DEPOSIT:
                    self.booking_repo.update_status(booking_id, "confirmed")
            
            return {
                "payment": payment,
                "booking": updated_booking.to_dict() if updated_booking else None
            }
        
        except Exception as e:
            logger.error(f"Erreur lors du traitement du paiement: {str(e)}")
            raise
    
    async def create_payment_intent(self, 
                                  booking_id: str, 
                                  amount: float,
                                  payment_type: PaymentType,
                                  customer_info: Dict[str, Any],
                                  payment_methods: Optional[List[PaymentMethod]] = None,
                                  metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Crée une intention de paiement pour une réservation.
        
        Args:
            booking_id: ID de la réservation
            amount: Montant du paiement
            payment_type: Type de paiement
            customer_info: Informations sur le client
            payment_methods: Méthodes de paiement acceptées
            metadata: Métadonnées supplémentaires
            
        Returns:
            Informations sur l'intention de paiement
        """
        try:
            # Récupérer la réservation
            booking = self.booking_repo.get_by_id(booking_id)
            if not booking:
                raise ValueError(f"Réservation non trouvée: {booking_id}")
            
            # Vérifier que le montant est valide
            if amount <= 0:
                raise ValueError("Le montant du paiement doit être supérieur à 0")
            
            # Créer les métadonnées
            payment_metadata = {
                "booking_id": booking_id,
                "retreat_id": booking.retreat_id,
                "client_id": booking.client_id,
                "payment_type": payment_type.value,
                **(metadata or {})
            }
            
            # Créer l'intention de paiement
            intent_result = await self.payment_provider.create_payment_intent(
                amount=amount,
                currency=booking.currency,
                description=f"Paiement pour la réservation {booking_id}",
                customer_info=customer_info,
                payment_methods=payment_methods,
                metadata=payment_metadata
            )
            
            return intent_result
        
        except Exception as e:
            logger.error(f"Erreur lors de la création de l'intention de paiement: {str(e)}")
            raise
    
    async def refund_payment(self, 
                           payment_id: str, 
                           booking_id: str,
                           amount: Optional[float] = None,
                           reason: Optional[str] = None) -> Dict[str, Any]:
        """
        Rembourse un paiement.
        
        Args:
            payment_id: ID du paiement
            booking_id: ID de la réservation
            amount: Montant à rembourser (si None, rembourse le montant total)
            reason: Raison du remboursement
            
        Returns:
            Informations sur le remboursement
        """
        try:
            # Récupérer la réservation
            booking = self.booking_repo.get_by_id(booking_id)
            if not booking:
                raise ValueError(f"Réservation non trouvée: {booking_id}")
            
            # Vérifier que le paiement existe
            payment_found = False
            for payment in booking.payments:
                if payment.get("id") == payment_id or payment.get("transaction_id") == payment_id:
                    payment_found = True
                    break
            
            if not payment_found:
                raise ValueError(f"Paiement non trouvé: {payment_id}")
            
            # Effectuer le remboursement
            refund_result = await self.payment_provider.refund_payment(
                payment_id=payment_id,
                amount=amount,
                reason=reason
            )
            
            # Créer l'objet de remboursement
            refund = {
                "id": refund_result.get("id"),
                "amount": refund_result.get("amount"),
                "currency": booking.currency,
                "payment_method": PaymentMethod.OTHER.value,
                "payment_type": PaymentType.REFUND.value,
                "status": refund_result.get("status"),
                "transaction_id": refund_result.get("id"),
                "payment_date": datetime.now().isoformat(),
                "related_payment_id": payment_id,
                "reason": reason
            }
            
            # Ajouter le remboursement à la réservation
            updated_booking = self.booking_repo.add_payment(booking_id, refund)
            
            # Mettre à jour le statut de la réservation si nécessaire
            if refund_result.get("status") == PaymentStatus.REFUNDED.value:
                self.booking_repo.update_status(booking_id, "refunded")
            
            return {
                "refund": refund,
                "booking": updated_booking.to_dict() if updated_booking else None
            }
        
        except Exception as e:
            logger.error(f"Erreur lors du remboursement du paiement: {str(e)}")
            raise
    
    async def get_payment_status(self, payment_id: str) -> PaymentStatus:
        """
        Récupère le statut d'un paiement.
        
        Args:
            payment_id: ID du paiement
            
        Returns:
            Statut du paiement
        """
        try:
            return await self.payment_provider.get_payment_status(payment_id)
        
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du statut du paiement: {str(e)}")
            raise
