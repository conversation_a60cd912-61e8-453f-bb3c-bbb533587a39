# Guide d'intégration avec les microservices

Ce document décrit comment Front-Audrey-V1-Main-main s'intègre avec les différents microservices du projet Retreat And Be.

## Architecture globale

Front-Audrey-V1-Main-main est conçu pour fonctionner avec l'architecture microservices de Retreat And Be. Voici comment il s'intègre avec les différents microservices :

```
                                  +-------------------+
                                  |                   |
                                  |  Ingress/Gateway  |
                                  |                   |
                                  +--------+----------+
                                           |
                                           v
+-------------------+            +-------------------+
|                   |            |                   |
|  Audrey Frontend  +<---------->+  Backend-NestJS   |
|                   |            |                   |
+--------+----------+            +--------+----------+
         |                                |
         |                                |
         v                                v
+-------------------+            +-------------------+
|                   |            |                   |
|  Security Service |<---------->+  Other Services   |
|                   |            |                   |
+-------------------+            +-------------------+
```

## Configuration des endpoints

### Backend-NestJS

Le frontend communique principalement avec Backend-NestJS pour les opérations CRUD. Voici les principaux endpoints utilisés :

- **Authentication**: `/api/auth/*`
- **Users**: `/api/users/*`
- **Retreats**: `/api/retreats/*`
- **Bookings**: `/api/bookings/*`
- **Profiles**: `/api/profiles/*`

La configuration de l'URL de l'API se fait via la variable d'environnement `REACT_APP_API_URL`.

### Security Service

Le frontend communique avec le Security Service pour les opérations liées à la sécurité :

- **Encryption**: `/api/security/encrypt`
- **Decryption**: `/api/security/decrypt`
- **File Validation**: `/api/security/validate-file`
- **Certificate Management**: `/api/security/certificates/*`

La configuration de l'URL du Security Service se fait via la variable d'environnement `REACT_APP_SECURITY_URL`.

### Agent IA

Le frontend communique avec le service Agent IA pour les fonctionnalités d'intelligence artificielle :

- **Chatbot**: `/api/agent-ia/chat`
- **Recommendations**: `/api/agent-ia/recommend`
- **Content Generation**: `/api/agent-ia/generate`

La configuration de l'URL du service Agent IA se fait via la variable d'environnement `REACT_APP_AGENT_IA_URL`.

### Social-Platform-Video

Le frontend communique avec le service Social-Platform-Video pour les fonctionnalités sociales et vidéo :

- **Video Streaming**: `/api/social/video/*`
- **Comments**: `/api/social/comments/*`
- **Likes**: `/api/social/likes/*`
- **Shares**: `/api/social/shares/*`

La configuration de l'URL du service Social-Platform-Video se fait via la variable d'environnement `REACT_APP_SOCIAL_URL`.

### Financial-Management

Le frontend communique avec le service Financial-Management pour les opérations financières :

- **Payments**: `/api/financial/payments/*`
- **Subscriptions**: `/api/financial/subscriptions/*`
- **Invoices**: `/api/financial/invoices/*`
- **Refunds**: `/api/financial/refunds/*`

La configuration de l'URL du service Financial-Management se fait via la variable d'environnement `REACT_APP_FINANCIAL_URL`.

## Configuration dans Kubernetes

### Service Discovery

Dans Kubernetes, les services sont découverts via DNS. Les noms de service suivants sont utilisés :

- **Backend-NestJS**: `backend-nestjs.retreat-and-be.svc.cluster.local`
- **Security Service**: `security-service.retreat-and-be.svc.cluster.local`
- **Agent IA**: `agent-ia.retreat-and-be.svc.cluster.local`
- **Social-Platform-Video**: `social-platform-video.retreat-and-be.svc.cluster.local`
- **Financial-Management**: `financial-management.retreat-and-be.svc.cluster.local`

### Configuration des variables d'environnement

Les variables d'environnement sont configurées dans le ConfigMap Kubernetes :

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: audrey-frontend-config
data:
  REACT_APP_API_URL: "https://api.retreat-and-be.com"
  REACT_APP_SECURITY_URL: "https://api.retreat-and-be.com/security"
  REACT_APP_AGENT_IA_URL: "https://api.retreat-and-be.com/agent-ia"
  REACT_APP_SOCIAL_URL: "https://api.retreat-and-be.com/social"
  REACT_APP_FINANCIAL_URL: "https://api.retreat-and-be.com/financial"
```

### Gestion des secrets

Les secrets, comme les clés API, sont gérés via Kubernetes Secrets :

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: audrey-frontend-secrets
type: Opaque
data:
  REACT_APP_API_KEY: "base64-encoded-api-key"
  REACT_APP_ENCRYPTION_KEY: "base64-encoded-encryption-key"
```

## Authentification et autorisation

### Flux d'authentification

1. L'utilisateur se connecte via le frontend
2. Le frontend envoie les identifiants au Backend-NestJS
3. Le Backend-NestJS vérifie les identifiants et génère un JWT
4. Le frontend stocke le JWT dans le localStorage
5. Le frontend inclut le JWT dans toutes les requêtes API

### Autorisation

Les rôles et permissions sont gérés par le Backend-NestJS. Le frontend adapte son interface en fonction des rôles de l'utilisateur.

## Gestion des erreurs

### Erreurs API

Les erreurs API sont gérées de manière centralisée dans le frontend. Les codes d'erreur standard sont :

- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **500**: Internal Server Error

### Retry et Circuit Breaker

Le frontend implémente des mécanismes de retry et de circuit breaker pour gérer les erreurs temporaires :

- **Retry**: 3 tentatives avec backoff exponentiel
- **Circuit Breaker**: Ouverture après 5 erreurs consécutives, fermeture après 30 secondes

## Monitoring et observabilité

### Métriques

Le frontend expose des métriques pour Prometheus à l'endpoint `/metrics`. Les principales métriques sont :

- **Temps de réponse API**: `api_response_time_seconds`
- **Erreurs API**: `api_errors_total`
- **Temps de chargement des pages**: `page_load_time_seconds`
- **Erreurs JavaScript**: `js_errors_total`

### Logging

Les logs du frontend sont envoyés à Elasticsearch via Fluentd. Les logs incluent :

- **Erreurs API**
- **Erreurs JavaScript**
- **Actions utilisateur**
- **Performances**

## Tests d'intégration

Des tests d'intégration sont disponibles pour vérifier la communication entre le frontend et les microservices :

```bash
# Exécuter les tests d'intégration
npm run test:integration
```

Les tests couvrent les scénarios suivants :

- **Authentification**
- **Opérations CRUD**
- **Gestion des erreurs**
- **Performance**
