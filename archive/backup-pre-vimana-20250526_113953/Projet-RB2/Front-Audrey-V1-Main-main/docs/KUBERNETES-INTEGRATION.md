# Kubernetes Integration Guide for Audrey Frontend

This guide explains how the Audrey Frontend application is integrated with Kubernetes.

## Architecture Overview

The Audrey Frontend application is deployed as a stateless application in Kubernetes. It consists of the following components:

- **Deployment**: Manages the frontend application pods
- **Service**: Exposes the frontend application to other services
- **Ingress**: Exposes the frontend application to external users
- **ConfigMap**: Stores configuration data
- **HorizontalPodAutoscaler**: Automatically scales the application based on CPU usage
- **NetworkPolicy**: Restricts traffic to and from the application

## Prerequisites

- Kubernetes cluster (version 1.19+)
- kubectl CLI tool
- Helm (version 3+)
- Docker registry access
- Nginx Ingress Controller
- Cert-Manager (for TLS certificates)

## Deployment Options

### Option 1: Using kubectl

1. Apply the Kubernetes manifests:

```bash
kubectl apply -f kubernetes/configmap.yaml -n retreat-and-be
kubectl apply -f kubernetes/deployment.yaml -n retreat-and-be
kubectl apply -f kubernetes/service.yaml -n retreat-and-be
kubectl apply -f kubernetes/ingress.yaml -n retreat-and-be
kubectl apply -f kubernetes/hpa.yaml -n retreat-and-be
kubectl apply -f kubernetes/networkpolicy.yaml -n retreat-and-be
```

2. Verify the deployment:

```bash
kubectl get pods -n retreat-and-be
kubectl get services -n retreat-and-be
kubectl get ingress -n retreat-and-be
```

### Option 2: Using Helm

1. Install the Helm chart:

```bash
helm install audrey-frontend ./helm -n retreat-and-be
```

2. Customize the installation using values:

```bash
helm install audrey-frontend ./helm -f custom-values.yaml -n retreat-and-be
```

3. Upgrade the installation:

```bash
helm upgrade audrey-frontend ./helm -n retreat-and-be
```

## Configuration

### Environment Variables

The following environment variables can be configured:

| Variable | Description | Default |
|----------|-------------|---------|
| NODE_ENV | Environment mode | production |
| REACT_APP_API_URL | Backend API URL | https://api.retreat-and-be.com |

### Resource Allocation

The default resource allocation is:

- **Requests**:
  - CPU: 100m
  - Memory: 128Mi
- **Limits**:
  - CPU: 300m
  - Memory: 256Mi

Adjust these values based on your application's requirements.

## Scaling

The application is configured to scale automatically based on CPU usage:

- **Minimum replicas**: 2
- **Maximum replicas**: 10
- **Target CPU utilization**: 70%

To manually scale the application:

```bash
kubectl scale deployment audrey-frontend --replicas=5 -n retreat-and-be
```

## Networking

### Service

The application is exposed internally through a ClusterIP service on port 80.

### Ingress

The application is exposed externally through an Ingress resource:

- **Host**: app.retreat-and-be.com
- **Path**: /
- **TLS**: Enabled with Let's Encrypt

### Network Policies

Network policies restrict traffic to and from the application:

- **Ingress**: Only allow traffic from the Ingress controller
- **Egress**: Only allow traffic to the backend service and DNS

## Monitoring

### Prometheus Integration

The application exposes metrics at the `/metrics` endpoint, which is scraped by Prometheus.

### Grafana Dashboards

A Grafana dashboard is available for monitoring the application:

- **Dashboard ID**: audrey-frontend
- **Metrics**: HTTP requests, response times, error rates

## Logging

Logs are collected by Fluentd and sent to Elasticsearch. The logs can be viewed in Kibana:

- **Index**: audrey-frontend-*
- **Log format**: JSON

## Security

### Pod Security

The application runs as a non-root user with minimal permissions.

### Network Security

Traffic is encrypted using TLS, and network policies restrict communication.

### Secret Management

Sensitive information is stored in Kubernetes Secrets.

## Troubleshooting

### Common Issues

1. **Pods not starting**:
   - Check pod events: `kubectl describe pod <pod-name> -n retreat-and-be`
   - Check pod logs: `kubectl logs <pod-name> -n retreat-and-be`

2. **Service not accessible**:
   - Check service endpoints: `kubectl get endpoints audrey-frontend -n retreat-and-be`
   - Check service selector: `kubectl describe service audrey-frontend -n retreat-and-be`

3. **Ingress not working**:
   - Check ingress status: `kubectl describe ingress audrey-frontend-ingress -n retreat-and-be`
   - Check ingress controller logs: `kubectl logs -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx`

### Debugging Commands

```bash
# Check pod status
kubectl get pods -n retreat-and-be -l app=audrey-frontend

# Check pod logs
kubectl logs -n retreat-and-be -l app=audrey-frontend

# Check pod events
kubectl get events -n retreat-and-be --sort-by='.lastTimestamp'

# Check service endpoints
kubectl get endpoints -n retreat-and-be audrey-frontend

# Check ingress status
kubectl get ingress -n retreat-and-be audrey-frontend-ingress
```
