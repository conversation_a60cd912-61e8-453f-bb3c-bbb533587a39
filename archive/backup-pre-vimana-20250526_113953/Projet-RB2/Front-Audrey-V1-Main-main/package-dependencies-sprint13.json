{"name": "retreat-and-be-frontend-unified", "version": "2.0.0", "description": "Frontend unifié pour Retreat And Be - Sprint 13 Implementation", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.0", "typescript": "^5.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "zustand": "^4.3.8", "immer": "^10.0.2", "class-variance-authority": "^0.6.0", "clsx": "^1.2.1", "tailwind-merge": "^1.12.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "framer-motion": "^10.12.16", "react-query": "^3.39.3", "@tanstack/react-query": "^4.29.7", "react-hook-form": "^7.44.3", "@hookform/resolvers": "^3.1.0", "zod": "^3.21.4", "date-fns": "^2.30.0", "react-datepicker": "^4.11.0", "react-hot-toast": "^2.4.1", "sonner": "^0.6.2", "lucide-react": "^0.244.0", "react-icons": "^4.9.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.0.0", "vite": "^4.3.9", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3", "vitest": "^0.32.0", "jsdom": "^22.1.0", "cypress": "^12.14.0", "@cypress/react": "^7.0.3", "storybook": "^7.0.20", "@storybook/react": "^7.0.20", "@storybook/addon-essentials": "^7.0.20", "@storybook/addon-interactions": "^7.0.20", "@storybook/testing-library": "^0.1.0", "eslint": "^8.42.0", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.3.0", "husky": "^8.0.3", "lint-staged": "^13.2.2", "@types/node": "^20.3.1"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "cypress:open": "cypress open", "cypress:run": "cypress run", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "prepare": "husky install"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/retreat-and-be/frontend"}, "keywords": ["react", "typescript", "tailwindcss", "vite", "retreat", "wellness", "spa"], "author": "Retreat And Be Team", "license": "MIT"}