#!/bin/bash

# Script de nettoyage pour Front-Audrey-V1-Main-main
# Ce script permet de nettoyer les ressources créées par l'application

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
  log "Vérification des prérequis..."
  
  # Vérifier si kubectl est installé
  if ! command -v kubectl &> /dev/null; then
    error "kubectl n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si Helm est installé
  if ! command -v helm &> /dev/null; then
    error "Helm n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  success "Tous les prérequis sont installés."
}

# Configurer les variables d'environnement
setup_environment() {
  log "Configuration des variables d'environnement..."
  
  # Demander le namespace Kubernetes
  read -p "Entrez le namespace Kubernetes (par défaut: retreat-and-be): " NAMESPACE
  NAMESPACE=${NAMESPACE:-retreat-and-be}
  
  success "Variables d'environnement configurées."
}

# Supprimer le déploiement Helm
remove_helm_deployment() {
  log "Suppression du déploiement Helm..."
  
  # Supprimer le déploiement Helm
  helm uninstall audrey-frontend -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    warn "Erreur lors de la suppression du déploiement Helm. Tentative de suppression manuelle des ressources..."
  else
    success "Déploiement Helm supprimé avec succès."
  fi
}

# Supprimer les ressources Kubernetes manuellement
remove_kubernetes_resources() {
  log "Suppression manuelle des ressources Kubernetes..."
  
  # Supprimer le déploiement
  kubectl delete deployment audrey-frontend -n ${NAMESPACE} --ignore-not-found
  
  # Supprimer le service
  kubectl delete service audrey-frontend -n ${NAMESPACE} --ignore-not-found
  
  # Supprimer l'ingress
  kubectl delete ingress audrey-frontend-ingress -n ${NAMESPACE} --ignore-not-found
  
  # Supprimer le HPA
  kubectl delete hpa audrey-frontend-hpa -n ${NAMESPACE} --ignore-not-found
  
  # Supprimer le ConfigMap
  kubectl delete configmap audrey-frontend-config -n ${NAMESPACE} --ignore-not-found
  
  # Supprimer le NetworkPolicy
  kubectl delete networkpolicy audrey-frontend-network-policy -n ${NAMESPACE} --ignore-not-found
  
  # Supprimer le ServiceMonitor
  kubectl delete servicemonitor audrey-frontend-monitor -n ${NAMESPACE} --ignore-not-found
  
  success "Ressources Kubernetes supprimées avec succès."
}

# Supprimer les images Docker
remove_docker_images() {
  log "Suppression des images Docker locales..."
  
  # Demander le registre Docker
  read -p "Entrez l'URL du registre Docker (par défaut: registry.retreat-and-be.com): " DOCKER_REGISTRY
  DOCKER_REGISTRY=${DOCKER_REGISTRY:-registry.retreat-and-be.com}
  
  # Supprimer les images Docker locales
  docker images | grep "${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend" | awk '{print $3}' | xargs -r docker rmi -f
  
  success "Images Docker locales supprimées avec succès."
}

# Supprimer les sauvegardes
remove_backups() {
  log "Suppression des sauvegardes..."
  
  # Demander confirmation
  read -p "Voulez-vous supprimer les sauvegardes? Cette action est irréversible. (o/n): " CONFIRM
  if [[ $CONFIRM != "o" && $CONFIRM != "O" ]]; then
    log "Suppression des sauvegardes annulée."
    return
  fi
  
  # Supprimer la planification de sauvegarde
  kubectl delete schedule audrey-frontend-daily-backup -n velero --ignore-not-found
  
  # Supprimer les sauvegardes existantes
  kubectl get backup -n velero | grep "audrey-frontend" | awk '{print $1}' | xargs -r kubectl delete backup -n velero
  
  success "Sauvegardes supprimées avec succès."
}

# Menu principal
main() {
  echo "================================================"
  echo "  Nettoyage de Front-Audrey-V1-Main-main"
  echo "================================================"
  
  # Vérifier les prérequis
  check_prerequisites
  
  # Configurer les variables d'environnement
  setup_environment
  
  # Demander confirmation
  echo
  echo "ATTENTION: Cette action va supprimer toutes les ressources associées à Front-Audrey-V1-Main-main."
  echo "Cette action est irréversible."
  echo
  read -p "Voulez-vous continuer? (o/n): " CONFIRM
  if [[ $CONFIRM != "o" && $CONFIRM != "O" ]]; then
    log "Nettoyage annulé."
    exit 0
  fi
  
  # Supprimer le déploiement Helm
  remove_helm_deployment
  
  # Supprimer les ressources Kubernetes manuellement
  remove_kubernetes_resources
  
  # Demander si l'utilisateur veut supprimer les images Docker
  read -p "Voulez-vous supprimer les images Docker locales? (o/n): " CONFIRM
  if [[ $CONFIRM == "o" || $CONFIRM == "O" ]]; then
    remove_docker_images
  fi
  
  # Demander si l'utilisateur veut supprimer les sauvegardes
  read -p "Voulez-vous supprimer les sauvegardes? (o/n): " CONFIRM
  if [[ $CONFIRM == "o" || $CONFIRM == "O" ]]; then
    remove_backups
  fi
  
  success "Nettoyage terminé avec succès!"
}

# Exécuter le script
main
