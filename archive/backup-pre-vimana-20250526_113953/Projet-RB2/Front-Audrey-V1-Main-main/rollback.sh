#!/bin/bash

# Script de rollback pour Front-Audrey-V1-Main-main
# Ce script permet de revenir à une version précédente de l'application

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
  log "Vérification des prérequis..."
  
  # Vérifier si kubectl est installé
  if ! command -v kubectl &> /dev/null; then
    error "kubectl n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  success "Tous les prérequis sont installés."
}

# Configurer les variables d'environnement
setup_environment() {
  log "Configuration des variables d'environnement..."
  
  # Demander le namespace Kubernetes
  read -p "Entrez le namespace Kubernetes (par défaut: retreat-and-be): " NAMESPACE
  NAMESPACE=${NAMESPACE:-retreat-and-be}
  
  success "Variables d'environnement configurées."
}

# Afficher l'historique des révisions
show_revision_history() {
  log "Affichage de l'historique des révisions..."
  
  # Afficher l'historique des révisions
  kubectl rollout history deployment/audrey-frontend -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de l'affichage de l'historique des révisions."
    exit 1
  fi
}

# Effectuer le rollback
perform_rollback() {
  log "Exécution du rollback..."
  
  # Demander la révision cible
  read -p "Entrez le numéro de révision pour le rollback (laissez vide pour revenir à la révision précédente): " REVISION
  
  if [ -z "$REVISION" ]; then
    # Rollback à la révision précédente
    kubectl rollout undo deployment/audrey-frontend -n ${NAMESPACE}
  else
    # Rollback à une révision spécifique
    kubectl rollout undo deployment/audrey-frontend --to-revision=${REVISION} -n ${NAMESPACE}
  fi
  
  if [ $? -ne 0 ]; then
    error "Erreur lors du rollback."
    exit 1
  fi
  
  success "Rollback exécuté avec succès."
}

# Vérifier le rollback
verify_rollback() {
  log "Vérification du rollback..."
  
  # Attendre que le déploiement soit prêt
  kubectl rollout status deployment/audrey-frontend -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la vérification du rollback."
    exit 1
  fi
  
  # Afficher les informations du déploiement
  kubectl get deployment audrey-frontend -n ${NAMESPACE}
  
  success "Rollback vérifié avec succès."
}

# Menu principal
main() {
  echo "================================================"
  echo "  Rollback de Front-Audrey-V1-Main-main"
  echo "================================================"
  
  # Vérifier les prérequis
  check_prerequisites
  
  # Configurer les variables d'environnement
  setup_environment
  
  # Afficher l'historique des révisions
  show_revision_history
  
  # Demander confirmation
  echo
  read -p "Voulez-vous effectuer un rollback? (o/n): " CONFIRM
  if [[ $CONFIRM != "o" && $CONFIRM != "O" ]]; then
    log "Rollback annulé."
    exit 0
  fi
  
  # Effectuer le rollback
  perform_rollback
  
  # Vérifier le rollback
  verify_rollback
  
  success "Rollback terminé avec succès!"
}

# Exécuter le script
main
