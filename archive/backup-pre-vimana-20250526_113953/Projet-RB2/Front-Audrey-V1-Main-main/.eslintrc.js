module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    jest: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:jsx-a11y/recommended',
    // 'plugin:security/recommended', // Temporairement commenté pour résoudre l'erreur de config
    // La configuration prettier doit être la dernière pour surcharger les autres règles de formatage
    'prettier', // Désactive les règles ESLint conflictuelles avec Prettier (via eslint-config-prettier)
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: './tsconfig.json', // Important pour les règles TypeScript qui nécessitent des informations de type
  },
  plugins: [
    'react',
    'react-hooks',
    '@typescript-eslint',
    'jsx-a11y',
    'security', // Le plugin est toujours là, seules les recommandations étendues sont commentées
    'prettier', // Active eslint-plugin-prettier
  ],
  rules: {
    'prettier/prettier': 'warn', // Affiche les problèmes Prettier comme des avertissements ESLint
    'react/react-in-jsx-scope': 'off', // Plus nécessaire avec React 17+ et le nouveau JSX transform
    'react/prop-types': 'off', // Désactivé car nous utilisons TypeScript pour la vérification des types
    '@typescript-eslint/explicit-module-boundary-types': 'off', // Peut être activé pour plus de rigueur
    '@typescript-eslint/no-unused-vars': ['error', { 'argsIgnorePattern': '^_', 'varsIgnorePattern': '^_' }], // Ignore les variables commençant par un underscore
    // Les règles de eslint-plugin-security peuvent être très strictes.
    // Désactivez ou ajustez celles qui ne sont pas pertinentes pour un frontend
    // ou qui génèrent trop de faux positifs après évaluation.
    // Exemple :
    // 'security/detect-object-injection': 'off',
  },
  settings: {
    react: {
      version: 'detect', // Détecte automatiquement la version de React
    },
  },
  ignorePatterns: ['.eslintrc.js', 'node_modules/', 'build/', 'dist/', 'public/'], // Ajout de patterns à ignorer
};