import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from '../hooks/useTranslation';
import { useAuthContext } from '../hooks/useAuthContext';
import { 
  CriterionWeight, 
  MultiCriteriaOptions, 
  OptimizationMethod,
  UserMultiCriteriaPreferences,
  multiCriteriaRecommendationService
} from '../services/api/multiCriteriaRecommendationService';
import CriteriaSelector from '../components/multicriteria/CriteriaSelector';
import OptimizationMethodSelector from '../components/multicriteria/OptimizationMethodSelector';
import MultiCriteriaRecommendationList from '../components/multicriteria/MultiCriteriaRecommendationList';
import Footer from '../components/ui/Footer';

/**
 * Page de recommandations multi-critères
 */
const MultiCriteriaRecommendationPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const location = useLocation();
  const navigate = useNavigate();
  
  // Récupérer les critères et la méthode d'optimisation depuis l'état de navigation
  const initialCriteria = location.state?.criteria || [
    { criterionId: 'relevance', weight: 0.6, direction: 'maximize' },
    { criterionId: 'rating', weight: 0.3, direction: 'maximize' },
    { criterionId: 'price', weight: 0.1, direction: 'minimize' },
  ];
  
  const initialMethod = location.state?.optimizationMethod || OptimizationMethod.WEIGHTED_SUM;
  
  const [criteria, setCriteria] = useState<CriterionWeight[]>(initialCriteria);
  const [optimizationMethod, setOptimizationMethod] = useState<OptimizationMethod>(initialMethod);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [preferencesChanged, setPreferencesChanged] = useState<boolean>(false);
  
  // Charger les préférences utilisateur et les recommandations
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Récupérer les préférences utilisateur si elles ne sont pas déjà définies dans l'état de navigation
        if (user && !location.state) {
          try {
            const preferences = await multiCriteriaRecommendationService.getUserPreferences();
            if (preferences && preferences.criteriaWeights && preferences.criteriaWeights.length > 0) {
              setCriteria(preferences.criteriaWeights);
            }
            
            if (preferences && preferences.preferredMethod) {
              setOptimizationMethod(preferences.preferredMethod);
            }
          } catch (error) {
            console.error('Erreur lors de la récupération des préférences:', error);
            // Continuer avec les valeurs par défaut
          }
        }
        
        // Récupérer les recommandations
        await loadRecommendations();
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        setError(t('multiCriteria.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [user]);
  
  // Charger les recommandations
  const loadRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const options: MultiCriteriaOptions = {
        criteria,
        optimizationMethod,
        normalizeScores: true,
      };
      
      const multiCriteriaRecommendations = await multiCriteriaRecommendationService.getMultiCriteriaRecommendations(
        options,
        'RETREAT',
        12
      );
      
      setRecommendations(multiCriteriaRecommendations);
    } catch (error) {
      console.error('Erreur lors du chargement des recommandations:', error);
      setError(t('multiCriteria.loadError'));
    } finally {
      setLoading(false);
    }
  };
  
  // Gérer le changement de critères
  const handleCriteriaChange = (newCriteria: CriterionWeight[]) => {
    setCriteria(newCriteria);
    setPreferencesChanged(true);
  };
  
  // Gérer le changement de méthode d'optimisation
  const handleMethodChange = (newMethod: OptimizationMethod) => {
    setOptimizationMethod(newMethod);
    setPreferencesChanged(true);
  };
  
  // Appliquer les changements
  const handleApplyChanges = async () => {
    await loadRecommendations();
    setPreferencesChanged(false);
  };
  
  // Sauvegarder les préférences
  const handleSavePreferences = async () => {
    try {
      setLoading(true);
      
      const preferences: UserMultiCriteriaPreferences = {
        criteriaWeights: criteria,
        preferredMethod: optimizationMethod,
      };
      
      await multiCriteriaRecommendationService.updateUserPreferences(preferences);
      
      // Afficher un message de succès
      alert(t('multiCriteria.preferencesSaved'));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des préférences:', error);
      setError(t('multiCriteria.savePreferencesError'));
    } finally {
      setLoading(false);
    }
  };
  
  // Gérer le clic sur "Voir les détails"
  const handleViewDetails = (recommendation: any) => {
    navigate(`/retreats/${recommendation.id}`, {
      state: {
        from: 'multi-criteria',
        multiCriteriaScore: recommendation.multiCriteriaScore,
      },
    });
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-grow">
        <div className="bg-retreat-green-light py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl font-bold text-center mb-4">
              {t('multiCriteria.pageTitle')}
            </h1>
            <p className="text-center text-gray-700 max-w-2xl mx-auto">
              {t('multiCriteria.pageDescription')}
            </p>
          </div>
        </div>
        
        <div className="container mx-auto px-4 py-8">
          {error && (
            <div className="bg-red-100 text-red-700 p-4 rounded-md mb-6">
              {error}
            </div>
          )}
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <div className="lg:col-span-1">
              <div className="space-y-6">
                <CriteriaSelector
                  selectedCriteria={criteria}
                  onChange={handleCriteriaChange}
                />
                
                <OptimizationMethodSelector
                  selectedMethod={optimizationMethod}
                  onChange={handleMethodChange}
                />
                
                {preferencesChanged && (
                  <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <p className="text-yellow-700 mb-3">
                      {t('multiCriteria.preferencesChangedWarning')}
                    </p>
                    <button
                      onClick={handleApplyChanges}
                      className="w-full px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                    >
                      {t('multiCriteria.applyChanges')}
                    </button>
                  </div>
                )}
                
                {user && (
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <p className="text-gray-700 mb-3">
                      {t('multiCriteria.savePreferencesDescription')}
                    </p>
                    <button
                      onClick={handleSavePreferences}
                      className="w-full px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                      disabled={loading}
                    >
                      {t('multiCriteria.savePreferences')}
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            <div className="lg:col-span-2">
              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-retreat-green"></div>
                </div>
              ) : (
                <MultiCriteriaRecommendationList
                  recommendations={recommendations}
                  optimizationMethod={optimizationMethod}
                  onViewDetails={handleViewDetails}
                />
              )}
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default MultiCriteriaRecommendationPage;
