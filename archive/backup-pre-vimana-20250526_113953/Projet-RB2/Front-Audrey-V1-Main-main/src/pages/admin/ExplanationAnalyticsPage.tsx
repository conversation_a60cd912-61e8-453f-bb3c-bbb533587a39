import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';
import { 
  explanationAnalyticsService,
  ExplanationMetrics,
  ExplanationTrends,
  ExplanationImpact,
  AnalyticsFilterParams,
} from '../../services/api/explanationAnalyticsService';
import { t } from '../../services/i18n/i18nService';

/**
 * Page d'administration pour l'analyse des explications
 */
const ExplanationAnalyticsPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [metrics, setMetrics] = useState<ExplanationMetrics | null>(null);
  const [trends, setTrends] = useState<ExplanationTrends | null>(null);
  const [impact, setImpact] = useState<ExplanationImpact | null>(null);
  const [activeTab, setActiveTab] = useState<'metrics' | 'trends' | 'impact'>('metrics');
  const [period, setPeriod] = useState<'day' | 'week' | 'month'>('week');
  const [filters, setFilters] = useState<AnalyticsFilterParams>({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 jours
    endDate: new Date(),
    factorTypes: [],
    userSegments: [],
    languages: [],
    explanationTypes: [],
    explanationStyles: [],
  });
  
  // Vérifier si l'utilisateur est administrateur
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);
  
  // Charger les données
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Charger les métriques
        const metricsData = await explanationAnalyticsService.getMetrics(filters);
        setMetrics(metricsData);
        
        // Charger les tendances
        const trendsData = await explanationAnalyticsService.getTrends(period, filters);
        setTrends(trendsData);
        
        // Charger l'impact
        const impactData = await explanationAnalyticsService.getImpact(filters);
        setImpact(impactData);
      } catch (error) {
        console.error('Erreur lors du chargement des données d\'analyse:', error);
        toast.error('Erreur lors du chargement des données d\'analyse');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && user.role === 'ADMIN') {
      loadData();
    }
  }, [user, period, filters]);
  
  // Gérer le changement de période
  const handlePeriodChange = (newPeriod: 'day' | 'week' | 'month') => {
    setPeriod(newPeriod);
  };
  
  // Gérer le changement de filtre
  const handleFilterChange = (name: keyof AnalyticsFilterParams, value: any) => {
    setFilters({
      ...filters,
      [name]: value,
    });
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Helmet>
          <title>Analyse des Explications | Retreat And Be</title>
          <meta
            name="description"
            content="Analyse des explications pour les administrateurs de Retreat And Be."
          />
        </Helmet>
        
        <AdminNavBar />
        
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
            </div>
          </div>
        </main>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Analyse des Explications | Retreat And Be</title>
        <meta
          name="description"
          content="Analyse des explications pour les administrateurs de Retreat And Be."
        />
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Analyse des Explications</h1>
            <p className="text-gray-600">Visualisez et analysez les performances des explications</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div className="p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Filtres</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date de début
                  </label>
                  <input
                    type="date"
                    value={filters.startDate?.toISOString().split('T')[0]}
                    onChange={(e) => handleFilterChange('startDate', new Date(e.target.value))}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date de fin
                  </label>
                  <input
                    type="date"
                    value={filters.endDate?.toISOString().split('T')[0]}
                    onChange={(e) => handleFilterChange('endDate', new Date(e.target.value))}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Types de facteur
                  </label>
                  <select
                    multiple
                    value={filters.factorTypes || []}
                    onChange={(e) => {
                      const options = Array.from(e.target.selectedOptions, option => option.value);
                      handleFilterChange('factorTypes', options);
                    }}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                  >
                    {explanationAnalyticsService.getFactorTypes().map((type) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  <button
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'metrics'
                        ? 'border-retreat-green text-retreat-green'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                    onClick={() => setActiveTab('metrics')}
                  >
                    Métriques
                  </button>
                  <button
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'trends'
                        ? 'border-retreat-green text-retreat-green'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                    onClick={() => setActiveTab('trends')}
                  >
                    Tendances
                  </button>
                  <button
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'impact'
                        ? 'border-retreat-green text-retreat-green'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                    onClick={() => setActiveTab('impact')}
                  >
                    Impact
                  </button>
                </nav>
              </div>
              
              <div className="mt-6">
                {activeTab === 'metrics' && metrics && (
                  <div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="bg-gray-50 p-4 rounded-md">
                        <div className="text-sm text-gray-500">Explications</div>
                        <div className="text-xl font-semibold">{metrics.totalExplanations}</div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-md">
                        <div className="text-sm text-gray-500">Taux de consultation</div>
                        <div className="text-xl font-semibold">{explanationAnalyticsService.formatPercent(metrics.viewRate)}</div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-md">
                        <div className="text-sm text-gray-500">Taux de clics</div>
                        <div className="text-xl font-semibold">{explanationAnalyticsService.formatPercent(metrics.clickThroughRate)}</div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-md">
                        <div className="text-sm text-gray-500">Taux de conversion</div>
                        <div className="text-xl font-semibold">{explanationAnalyticsService.formatPercent(metrics.conversionRate)}</div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-3">Métriques par type de facteur</h3>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Type de facteur
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Nombre
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Taux de clics
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Taux de conversion
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {Object.entries(metrics.metricsByFactorType).map(([factorType, data]) => (
                                <tr key={factorType}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {factorType}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {data.count}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {explanationAnalyticsService.formatPercent(data.clickThroughRate)}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {explanationAnalyticsService.formatPercent(data.conversionRate)}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-3">Métriques par segment utilisateur</h3>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Segment
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Nombre
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Taux de clics
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Satisfaction
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {Object.entries(metrics.metricsByUserSegment).map(([segment, data]) => (
                                <tr key={segment}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {segment}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {data.count}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {explanationAnalyticsService.formatPercent(data.clickThroughRate)}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {data.averageSatisfactionScore.toFixed(1)}/5
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                
                {activeTab === 'trends' && trends && (
                  <div>
                    <div className="mb-6">
                      <div className="flex space-x-4">
                        <button
                          className={`px-4 py-2 rounded-md ${
                            period === 'day'
                              ? 'bg-retreat-green text-white'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                          }`}
                          onClick={() => handlePeriodChange('day')}
                        >
                          Jour
                        </button>
                        <button
                          className={`px-4 py-2 rounded-md ${
                            period === 'week'
                              ? 'bg-retreat-green text-white'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                          }`}
                          onClick={() => handlePeriodChange('week')}
                        >
                          Semaine
                        </button>
                        <button
                          className={`px-4 py-2 rounded-md ${
                            period === 'month'
                              ? 'bg-retreat-green text-white'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                          }`}
                          onClick={() => handlePeriodChange('month')}
                        >
                          Mois
                        </button>
                      </div>
                    </div>
                    
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Explications
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Taux de consultation
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Taux de clics
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Taux de conversion
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {trends.data.map((item, index) => (
                            <tr key={index}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {new Date(item.date).toLocaleDateString()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {item.explanationCount}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {explanationAnalyticsService.formatPercent(item.viewRate)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {explanationAnalyticsService.formatPercent(item.clickThroughRate)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {explanationAnalyticsService.formatPercent(item.conversionRate)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
                
                {activeTab === 'impact' && impact && (
                  <div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                          <h3 className="text-lg font-medium text-gray-900">Engagement utilisateur</h3>
                        </div>
                        <div className="p-6">
                          <div className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Temps passé</span>
                              <span className={`font-medium ${impact.userEngagement.timeSpentChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {explanationAnalyticsService.formatChange(impact.userEngagement.timeSpentChange)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Pages vues</span>
                              <span className={`font-medium ${impact.userEngagement.pageViewsChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {explanationAnalyticsService.formatChange(impact.userEngagement.pageViewsChange)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Taux de rebond</span>
                              <span className={`font-medium ${impact.userEngagement.bounceRateChange <= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {explanationAnalyticsService.formatChange(impact.userEngagement.bounceRateChange)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                          <h3 className="text-lg font-medium text-gray-900">Conversions</h3>
                        </div>
                        <div className="p-6">
                          <div className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Taux de conversion</span>
                              <span className={`font-medium ${impact.conversions.conversionRateChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {explanationAnalyticsService.formatChange(impact.conversions.conversionRateChange)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Panier moyen</span>
                              <span className={`font-medium ${impact.conversions.averageOrderValueChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {explanationAnalyticsService.formatChange(impact.conversions.averageOrderValueChange)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Abandon de panier</span>
                              <span className={`font-medium ${impact.conversions.cartAbandonmentRateChange <= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {explanationAnalyticsService.formatChange(impact.conversions.cartAbandonmentRateChange)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                          <h3 className="text-lg font-medium text-gray-900">Rétention</h3>
                        </div>
                        <div className="p-6">
                          <div className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Taux de rétention</span>
                              <span className={`font-medium ${impact.retention.retentionRateChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {explanationAnalyticsService.formatChange(impact.retention.retentionRateChange)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Fréquence des visites</span>
                              <span className={`font-medium ${impact.retention.visitFrequencyChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {explanationAnalyticsService.formatChange(impact.retention.visitFrequencyChange)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Taux de désabonnement</span>
                              <span className={`font-medium ${impact.retention.churnRateChange <= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {explanationAnalyticsService.formatChange(impact.retention.churnRateChange)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-6 p-4 bg-blue-50 border-l-4 border-blue-500 rounded-md">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-blue-700">
                            Ces métriques montrent l'impact des explications sur l'engagement, les conversions et la rétention des utilisateurs.
                            Les valeurs positives indiquent une amélioration par rapport à la période précédente.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ExplanationAnalyticsPage;
