import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';
import { 
  reinforcementLearningService, 
  RLAgent, 
  RLAgentType, 
  RLAgentState,
  ExplorationStrategy,
  CreateRLAgentRequest,
  UpdateRLAgentRequest,
} from '../../services/api/reinforcementLearningService';
import { t } from '../../services/i18n/i18nService';

/**
 * Page d'administration pour l'apprentissage par renforcement
 */
const ReinforcementLearningPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [agents, setAgents] = useState<RLAgent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<RLAgent | null>(null);
  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);
  const [isEditingAgent, setIsEditingAgent] = useState<boolean>(false);
  const [actionInProgress, setActionInProgress] = useState<boolean>(false);
  
  // Vérifier si l'utilisateur est administrateur
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);
  
  // Charger les agents
  useEffect(() => {
    const loadAgents = async () => {
      try {
        setLoading(true);
        const agentsData = await reinforcementLearningService.getAllAgents();
        setAgents(agentsData);
      } catch (error) {
        console.error('Erreur lors du chargement des agents:', error);
        toast.error(t('reinforcementLearning.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (user && user.role === 'ADMIN') {
      loadAgents();
    }
  }, [user]);
  
  // Gérer la création d'un agent
  const handleCreateAgent = async (agentData: CreateRLAgentRequest) => {
    try {
      setActionInProgress(true);
      const newAgent = await reinforcementLearningService.createAgent(agentData);
      
      setAgents([...agents, newAgent]);
      setIsCreatingAgent(false);
      toast.success(t('reinforcementLearning.createSuccess'));
    } catch (error) {
      console.error('Erreur lors de la création de l\'agent:', error);
      toast.error(t('reinforcementLearning.createError'));
    } finally {
      setActionInProgress(false);
    }
  };
  
  // Gérer la mise à jour d'un agent
  const handleUpdateAgent = async (agentId: string, agentData: UpdateRLAgentRequest) => {
    try {
      setActionInProgress(true);
      const updatedAgent = await reinforcementLearningService.updateAgent(agentId, agentData);
      
      setAgents(agents.map(agent => agent.id === agentId ? updatedAgent : agent));
      setSelectedAgent(updatedAgent);
      setIsEditingAgent(false);
      toast.success(t('reinforcementLearning.updateSuccess'));
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de l'agent ${agentId}:`, error);
      toast.error(t('reinforcementLearning.updateError'));
    } finally {
      setActionInProgress(false);
    }
  };
  
  // Gérer la suppression d'un agent
  const handleDeleteAgent = async (agentId: string) => {
    if (!window.confirm(t('reinforcementLearning.deleteConfirm'))) {
      return;
    }
    
    try {
      setActionInProgress(true);
      await reinforcementLearningService.deleteAgent(agentId);
      
      setAgents(agents.filter(agent => agent.id !== agentId));
      if (selectedAgent && selectedAgent.id === agentId) {
        setSelectedAgent(null);
      }
      toast.success(t('reinforcementLearning.deleteSuccess'));
    } catch (error) {
      console.error(`Erreur lors de la suppression de l'agent ${agentId}:`, error);
      toast.error(t('reinforcementLearning.deleteError'));
    } finally {
      setActionInProgress(false);
    }
  };
  
  // Gérer le démarrage de l'apprentissage
  const handleStartLearning = async (agentId: string) => {
    try {
      setActionInProgress(true);
      const updatedAgent = await reinforcementLearningService.startLearning(agentId);
      
      setAgents(agents.map(agent => agent.id === agentId ? updatedAgent : agent));
      if (selectedAgent && selectedAgent.id === agentId) {
        setSelectedAgent(updatedAgent);
      }
      toast.success(t('reinforcementLearning.startSuccess'));
    } catch (error) {
      console.error(`Erreur lors du démarrage de l'apprentissage de l'agent ${agentId}:`, error);
      toast.error(t('reinforcementLearning.startError'));
    } finally {
      setActionInProgress(false);
    }
  };
  
  // Gérer la mise en pause de l'apprentissage
  const handlePauseLearning = async (agentId: string) => {
    try {
      setActionInProgress(true);
      const updatedAgent = await reinforcementLearningService.pauseLearning(agentId);
      
      setAgents(agents.map(agent => agent.id === agentId ? updatedAgent : agent));
      if (selectedAgent && selectedAgent.id === agentId) {
        setSelectedAgent(updatedAgent);
      }
      toast.success(t('reinforcementLearning.pauseSuccess'));
    } catch (error) {
      console.error(`Erreur lors de la mise en pause de l'apprentissage de l'agent ${agentId}:`, error);
      toast.error(t('reinforcementLearning.pauseError'));
    } finally {
      setActionInProgress(false);
    }
  };
  
  // Gérer l'arrêt de l'apprentissage
  const handleStopLearning = async (agentId: string) => {
    try {
      setActionInProgress(true);
      const updatedAgent = await reinforcementLearningService.stopLearning(agentId);
      
      setAgents(agents.map(agent => agent.id === agentId ? updatedAgent : agent));
      if (selectedAgent && selectedAgent.id === agentId) {
        setSelectedAgent(updatedAgent);
      }
      toast.success(t('reinforcementLearning.stopSuccess'));
    } catch (error) {
      console.error(`Erreur lors de l'arrêt de l'apprentissage de l'agent ${agentId}:`, error);
      toast.error(t('reinforcementLearning.stopError'));
    } finally {
      setActionInProgress(false);
    }
  };
  
  // Obtenir la couleur de l'état de l'agent
  const getStateColor = (state: RLAgentState): string => {
    switch (state) {
      case RLAgentState.LEARNING:
        return 'bg-green-100 text-green-800 border-green-300';
      case RLAgentState.PAUSED:
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case RLAgentState.ERROR:
        return 'bg-red-100 text-red-800 border-red-300';
      case RLAgentState.INITIALIZING:
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case RLAgentState.EXPLOITING:
        return 'bg-purple-100 text-purple-800 border-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };
  
  // Formater un nombre
  const formatNumber = (value: number, decimals: number = 2): string => {
    return value.toFixed(decimals);
  };
  
  // Formater un pourcentage
  const formatPercent = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Helmet>
          <title>Apprentissage par Renforcement | Retreat And Be</title>
          <meta
            name="description"
            content="Gestion de l'apprentissage par renforcement pour les administrateurs de Retreat And Be."
          />
        </Helmet>
        
        <AdminNavBar />
        
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
            </div>
          </div>
        </main>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Apprentissage par Renforcement | Retreat And Be</title>
        <meta
          name="description"
          content="Gestion de l'apprentissage par renforcement pour les administrateurs de Retreat And Be."
        />
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Apprentissage par Renforcement</h1>
            <p className="text-gray-600">Gérer les agents d'apprentissage par renforcement pour optimiser les explications</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Liste des agents */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">Agents</h2>
                  <button
                    onClick={() => setIsCreatingAgent(true)}
                    className="px-3 py-1 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                    disabled={actionInProgress}
                  >
                    Créer un agent
                  </button>
                </div>
                
                {agents.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    Aucun agent disponible
                  </div>
                ) : (
                  <div className="space-y-4">
                    {agents.map((agent) => (
                      <div
                        key={agent.id}
                        className={`p-4 border rounded-md cursor-pointer transition-colors ${
                          selectedAgent?.id === agent.id
                            ? 'border-retreat-green bg-retreat-green-50'
                            : 'border-gray-200 hover:bg-gray-50'
                        }`}
                        onClick={() => setSelectedAgent(agent)}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium text-gray-900">{agent.name}</h3>
                            <p className="text-sm text-gray-500">{agent.description}</p>
                          </div>
                          <span className={`text-xs px-2 py-1 rounded-full border ${getStateColor(agent.state)}`}>
                            {agent.state}
                          </span>
                        </div>
                        <div className="mt-2 text-xs text-gray-500">
                          <span className="mr-4">Type: {agent.agentType}</span>
                          <span>Épisodes: {agent.stats.totalEpisodes}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            {/* Détails de l'agent sélectionné */}
            <div className="lg:col-span-2">
              {selectedAgent ? (
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-6">
                      <div>
                        <h2 className="text-xl font-semibold text-gray-900">{selectedAgent.name}</h2>
                        <p className="text-gray-600">{selectedAgent.description}</p>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setIsEditingAgent(true)}
                          className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
                          disabled={actionInProgress}
                        >
                          Modifier
                        </button>
                        <button
                          onClick={() => handleDeleteAgent(selectedAgent.id)}
                          className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                          disabled={actionInProgress}
                        >
                          Supprimer
                        </button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Informations de base */}
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-3">Informations</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Type:</span>
                            <span className="font-medium">{selectedAgent.agentType}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">État:</span>
                            <span className={`font-medium ${getStateColor(selectedAgent.state)} px-2 py-0.5 rounded-full text-xs`}>
                              {selectedAgent.state}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Créé le:</span>
                            <span>{new Date(selectedAgent.createdAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Mis à jour le:</span>
                            <span>{new Date(selectedAgent.updatedAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* Configuration */}
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-3">Configuration</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Taux d'apprentissage:</span>
                            <span>{formatNumber(selectedAgent.config.learningRate)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Facteur d'actualisation:</span>
                            <span>{formatNumber(selectedAgent.config.discountFactor)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Taux d'exploration:</span>
                            <span>{formatPercent(selectedAgent.config.explorationRate)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Stratégie d'exploration:</span>
                            <span>{selectedAgent.config.explorationStrategy}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Taille de la mémoire:</span>
                            <span>{selectedAgent.config.experienceMemorySize}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Taille du lot:</span>
                            <span>{selectedAgent.config.batchSize}</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* Statistiques */}
                      <div className="md:col-span-2">
                        <h3 className="text-lg font-medium text-gray-900 mb-3">Statistiques</h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="bg-gray-50 p-4 rounded-md">
                            <div className="text-sm text-gray-500">Épisodes</div>
                            <div className="text-xl font-semibold">{selectedAgent.stats.totalEpisodes}</div>
                          </div>
                          <div className="bg-gray-50 p-4 rounded-md">
                            <div className="text-sm text-gray-500">Étapes</div>
                            <div className="text-xl font-semibold">{selectedAgent.stats.totalSteps}</div>
                          </div>
                          <div className="bg-gray-50 p-4 rounded-md">
                            <div className="text-sm text-gray-500">Récompense moyenne</div>
                            <div className="text-xl font-semibold">{formatNumber(selectedAgent.stats.averageRewardPerEpisode)}</div>
                          </div>
                          <div className="bg-gray-50 p-4 rounded-md">
                            <div className="text-sm text-gray-500">Taux de convergence</div>
                            <div className="text-xl font-semibold">{formatPercent(selectedAgent.stats.convergenceRate)}</div>
                          </div>
                        </div>
                      </div>
                      
                      {/* Actions */}
                      <div className="md:col-span-2">
                        <h3 className="text-lg font-medium text-gray-900 mb-3">Actions</h3>
                        <div className="flex space-x-3">
                          <button
                            onClick={() => handleStartLearning(selectedAgent.id)}
                            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors disabled:bg-gray-300 disabled:text-gray-500"
                            disabled={actionInProgress || selectedAgent.state === RLAgentState.LEARNING}
                          >
                            Démarrer
                          </button>
                          <button
                            onClick={() => handlePauseLearning(selectedAgent.id)}
                            className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors disabled:bg-gray-300 disabled:text-gray-500"
                            disabled={actionInProgress || selectedAgent.state !== RLAgentState.LEARNING}
                          >
                            Pause
                          </button>
                          <button
                            onClick={() => handleStopLearning(selectedAgent.id)}
                            className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors disabled:bg-gray-300 disabled:text-gray-500"
                            disabled={actionInProgress || (selectedAgent.state !== RLAgentState.LEARNING && selectedAgent.state !== RLAgentState.PAUSED)}
                          >
                            Arrêter
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="p-6 text-center py-16">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Sélectionnez un agent</h3>
                    <p className="mt-2 text-gray-500">Sélectionnez un agent dans la liste pour voir ses détails et le gérer</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ReinforcementLearningPage;
