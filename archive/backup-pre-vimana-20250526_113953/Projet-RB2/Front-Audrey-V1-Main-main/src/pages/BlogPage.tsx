import React from 'react';
import { Helmet } from 'react-helmet-async';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import BlogSidebar from '../components/Blog/BlogSidebar';
import FeaturedPosts from '../components/Blog/FeaturedPosts';
import RecentPosts from '../components/Blog/RecentPosts';
import NewsletterBlog from '../components/Blog/NewsletterBlog';
import ScrollToTop from '../components/ui/ScrollToTop';
const BlogPage: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Blog - Retreat And Be</title>
        <meta
          name='description'
          content='Découvrez nos articles sur le bien-être, le yoga, la méditation et les retraites du monde entier.'
        />
      </Helmet>

      <div className='min-h-screen bg-gray-50'>
        <NavBarClient />

        <main className='pt-24 pb-16'>
          <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
            <div className='grid grid-cols-1 lg:grid-cols-12 gap-8'>
              {/* Main Content */}
              <div className='lg:col-span-8'>
                <FeaturedPosts />
                <RecentPosts />
              </div>

              {/* Sidebar */}
              <div className='lg:col-span-4'>
                <BlogSidebar />
                <NewsletterBlog />
              </div>
            </div>
          </div>
        </main>
        <footer>
          <Footer />
        </footer>
        <ScrollToTop />
      </div>
    </>
  );
};

export default BlogPage;
