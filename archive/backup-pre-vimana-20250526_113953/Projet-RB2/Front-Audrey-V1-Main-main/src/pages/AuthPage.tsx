import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import {
  UserCircleIcon,
  EnvelopeIcon,
  LockClosedIcon,
  ArrowRightIcon,
} from '@heroicons/react/24/outline';

const AuthPage: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const navigate = useNavigate();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Ici, ajoutez la logique d'authentification
    navigate('/compte');
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      <NavBarClient />

      <main className='pt-24 pb-16'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='max-w-md mx-auto'>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className='bg-white rounded-xl shadow-lg overflow-hidden'
            >
              {/* En-tête */}
              <div className='p-6 bg-retreat-green text-center'>
                <UserCircleIcon className='h-16 w-16 mx-auto text-white' />
                <h1 className='mt-4 text-2xl font-bold text-white'>
                  {isLogin ? 'Connexion' : 'Inscription'}
                </h1>
              </div>

              {/* Formulaire */}
              <div className='p-6'>
                <div className='flex justify-center space-x-4 mb-8'>
                  <button
                    onClick={() => setIsLogin(true)}
                    className={`px-4 py-2 rounded-full transition-colors ${
                      isLogin ? 'bg-retreat-green text-white' : 'text-gray-500 hover:bg-gray-100'
                    }`}
                  >
                    Se connecter
                  </button>
                  <button
                    onClick={() => setIsLogin(false)}
                    className={`px-4 py-2 rounded-full transition-colors ${
                      !isLogin ? 'bg-retreat-green text-white' : 'text-gray-500 hover:bg-gray-100'
                    }`}
                  >
                    S&apos;inscrire
                  </button>
                </div>

                <form onSubmit={handleSubmit} className='space-y-6'>
                  {!isLogin && (
                    <div className='grid grid-cols-2 gap-4'>
                      <div>
                        <label
                          htmlFor='firstNameInputAuth'
                          className='block text-sm font-medium text-gray-700'
                        >
                          Prénom
                        </label>
                        <input
                          id='firstNameInputAuth'
                          type='text'
                          required
                          className='mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                        />
                      </div>
                      <div>
                        <label
                          htmlFor='lastNameInputAuth'
                          className='block text-sm font-medium text-gray-700'
                        >
                          Nom
                        </label>
                        <input
                          id='lastNameInputAuth'
                          type='text'
                          required
                          className='mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                        />
                      </div>
                    </div>
                  )}

                  <div>
                    <label
                      htmlFor='emailInputAuth'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Email
                    </label>
                    <div className='mt-1 relative'>
                      <input
                        id='emailInputAuth'
                        type='email'
                        required
                        className='block w-full rounded-lg border-gray-300 pl-10 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                      />
                      <EnvelopeIcon className='h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2' />
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor='passwordInputAuth'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Mot de passe
                    </label>
                    <div className='mt-1 relative'>
                      <input
                        id='passwordInputAuth'
                        type='password'
                        required
                        className='block w-full rounded-lg border-gray-300 pl-10 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                      />
                      <LockClosedIcon className='h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2' />
                    </div>
                  </div>

                  {!isLogin && (
                    <div>
                      <label
                        htmlFor='confirmPasswordInputAuth'
                        className='block text-sm font-medium text-gray-700'
                      >
                        Confirmer le mot de passe
                      </label>
                      <div className='mt-1 relative'>
                        <input
                          id='confirmPasswordInputAuth'
                          type='password'
                          required
                          className='block w-full rounded-lg border-gray-300 pl-10 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                        />
                        <LockClosedIcon className='h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2' />
                      </div>
                    </div>
                  )}

                  {isLogin && (
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center'>
                        <input
                          id='rememberMeAuthCheckbox'
                          type='checkbox'
                          className='h-4 w-4 rounded border-gray-300 text-retreat-green focus:ring-retreat-green'
                        />
                        <label
                          htmlFor='rememberMeAuthCheckbox'
                          className='ml-2 block text-sm text-gray-700'
                        >
                          Se souvenir de moi
                        </label>
                      </div>
                      <Link
                        to='/reset-password'
                        className='text-sm text-retreat-green hover:text-retreat-green-dark'
                      >
                        Mot de passe oublié ?
                      </Link>
                    </div>
                  )}

                  <button
                    type='submit'
                    className='w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green'
                  >
                    <span>{isLogin ? 'Se connecter' : "S'inscrire"}</span>
                    <ArrowRightIcon className='ml-2 h-5 w-5' />
                  </button>
                </form>

                {/* Séparateur */}
                <div className='mt-6'>
                  <div className='relative'>
                    <div className='absolute inset-0 flex items-center'>
                      <div className='w-full border-t border-gray-300' />
                    </div>
                    <div className='relative flex justify-center text-sm'>
                      <span className='px-2 bg-white text-gray-500'>Ou continuer avec</span>
                    </div>
                  </div>

                  {/* Boutons sociaux */}
                  <div className='mt-6'>
                    <button className='w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50'>
                      <img className='h-5 w-5' src='/images/google-icon.svg' alt='Google' />
                      <span className='ml-2'>Google</span>
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Message légal */}
            <p className='mt-4 text-center text-sm text-gray-600'>
              En continuant, vous acceptez nos{' '}
              <Link to='/terms' className='text-retreat-green hover:text-retreat-green-dark'>
                Conditions d&apos;utilisation
              </Link>{' '}
              et notre{' '}
              <Link to='/privacy' className='text-retreat-green hover:text-retreat-green-dark'>
                Politique de confidentialité
              </Link>
              .
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default AuthPage;
