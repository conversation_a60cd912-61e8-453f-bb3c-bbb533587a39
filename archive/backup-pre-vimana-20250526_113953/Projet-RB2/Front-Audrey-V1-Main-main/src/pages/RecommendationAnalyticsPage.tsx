import React from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuthContext } from '../hooks/useAuthContext';
import { PageLayout } from '../layouts/PageLayout';
import { RecommendationAnalyticsDashboard } from '../components/recommendation/RecommendationAnalyticsDashboard';
import { Alert } from '../components/ui/Alert';

const RecommendationAnalyticsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  
  const isAdmin = user?.roles?.includes('admin');

  if (!user) {
    return (
      <PageLayout>
        <div className="container mx-auto px-4 py-8">
          <Alert
            variant="warning"
            title={t('common.unauthorized')}
            message={t('analytics.unauthorized')}
          />
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">{t('analytics.recommendation.pageTitle')}</h1>
          <p className="text-gray-500 mt-2">{t('analytics.recommendation.pageDescription')}</p>
        </div>
        
        <RecommendationAnalyticsDashboard isAdmin={isAdmin} />
      </div>
    </PageLayout>
  );
};

export default RecommendationAnalyticsPage;
