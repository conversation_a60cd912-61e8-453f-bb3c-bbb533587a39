import React from 'react';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import styled from 'styled-components';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Title = styled.h1`
  color: #4a148c;
  margin-bottom: 2rem;
`;

const Section = styled.section`
  margin-bottom: 2rem;
`;

const LegalNotice = () => {
  return (
    <>
      <NavBarClient />
      <Container>
        <Title>Mentions Légales</Title>
        <Section>
          <h2>Éditeur du site</h2>
          <p>Retreat and Be</p>
          <p>Adresse : [Votre adresse]</p>
          <p>Email : <EMAIL></p>
        </Section>
        {/* Ajoutez d'autres sections selon vos besoins */}
      </Container>
      <Footer />
    </>
  );
};

export default LegalNotice;
