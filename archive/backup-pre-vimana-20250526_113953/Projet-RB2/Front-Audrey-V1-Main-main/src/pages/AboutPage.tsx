/**
 * Page À Propos - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Page de présentation de l'entreprise et de la mission.
 */

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Container,
  Grid,
  Card,
  CardContent,
  Button,
  Badge
} from '../components/ui/design-system';

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
      {/* Navigation simple */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-neutral-200">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-2">
              <div className="text-2xl">🧘‍♀️</div>
              <span className="text-xl font-bold text-primary-900">Retreat & Be</span>
            </div>
            <Button variant="outline" onClick={() => window.history.back()}>
              Retour
            </Button>
          </div>
        </Container>
      </nav>

      <Container className="py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Hero */}
          <div className="text-center mb-16">
            <Badge variant="info" className="mb-4">À propos de nous</Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Notre mission : démocratiser le bien-être
            </h1>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Retreat & Be est née de la conviction que chacun mérite d'accéder à des outils 
              et des expériences qui favorisent son épanouissement personnel.
            </p>
          </div>

          {/* Valeurs */}
          <Grid cols={3} gap="lg" className="mb-16">
            <Card>
              <CardContent className="text-center p-8">
                <div className="text-4xl mb-4">🌱</div>
                <h3 className="text-xl font-semibold mb-3">Authenticité</h3>
                <p className="text-neutral-600">
                  Nous privilégions des approches authentiques et éprouvées, 
                  loin des tendances éphémères.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="text-center p-8">
                <div className="text-4xl mb-4">🤝</div>
                <h3 className="text-xl font-semibold mb-3">Bienveillance</h3>
                <p className="text-neutral-600">
                  Notre communauté est fondée sur l'entraide, le respect 
                  et la bienveillance mutuelle.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="text-center p-8">
                <div className="text-4xl mb-4">✨</div>
                <h3 className="text-xl font-semibold mb-3">Excellence</h3>
                <p className="text-neutral-600">
                  Nous sélectionnons rigoureusement nos partenaires pour 
                  garantir la qualité de chaque expérience.
                </p>
              </CardContent>
            </Card>
          </Grid>

          {/* Histoire */}
          <Card className="mb-16">
            <CardContent className="p-12">
              <div className="max-w-4xl mx-auto">
                <h2 className="text-3xl font-bold text-center mb-8">Notre histoire</h2>
                <div className="prose prose-lg mx-auto text-neutral-600">
                  <p>
                    Retreat & Be a été fondée en 2024 par une équipe passionnée de professionnels 
                    du bien-être et de la technologie. Constatant la difficulté pour beaucoup 
                    de trouver des ressources fiables et accessibles pour leur développement personnel, 
                    nous avons décidé de créer une plateforme qui connecte les chercheurs de bien-être 
                    avec les meilleurs professionnels du domaine.
                  </p>
                  <p>
                    Aujourd'hui, nous sommes fiers de compter plus de 10 000 membres actifs et 
                    200 professionnels certifiés qui partagent notre vision d'un monde où le 
                    bien-être est accessible à tous.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Équipe */}
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-8">Une équipe dédiée</h2>
            <p className="text-xl text-neutral-600 mb-8 max-w-2xl mx-auto">
              Notre équipe multidisciplinaire travaille chaque jour pour améliorer 
              votre expérience et vous accompagner dans votre parcours de bien-être.
            </p>
            <Button variant="primary" size="lg">
              Rejoindre l'aventure
            </Button>
          </div>
        </motion.div>
      </Container>
    </div>
  );
};

export default AboutPage;
