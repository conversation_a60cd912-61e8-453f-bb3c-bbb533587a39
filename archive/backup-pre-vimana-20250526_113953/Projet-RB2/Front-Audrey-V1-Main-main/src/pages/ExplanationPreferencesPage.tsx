import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../hooks/useAuthContext';
import NavBar from '../components/organisms/NavBar/NavBar';
import Footer from '../components/organisms/Footer/Footer';
import ExplanationPreferencesForm from '../components/explanation/ExplanationPreferencesForm';
import { t } from '../services/i18n/i18nService';

/**
 * Page de préférences d'explication
 */
const ExplanationPreferencesPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
  React.useEffect(() => {
    if (!user) {
      navigate('/login', { state: { from: '/explanation-preferences' } });
    }
  }, [user, navigate]);
  
  if (!user) {
    return null;
  }
  
  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      <Helmet>
        <title>{t('explanation.preferences.pageTitle')} | Retreat And Be</title>
        <meta
          name="description"
          content={t('explanation.preferences.pageDescription')}
        />
      </Helmet>
      
      <NavBar />
      
      <main className="flex-grow pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">{t('explanation.preferences.pageTitle')}</h1>
            <p className="text-gray-600">{t('explanation.preferences.pageSubtitle')}</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <ExplanationPreferencesForm />
            </div>
            
            <div>
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('explanation.preferences.helpTitle')}</h2>
                  
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{t('explanation.preferences.styleTitle')}</h3>
                      <p className="text-gray-600">{t('explanation.preferences.styleDescription')}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{t('explanation.preferences.detailTitle')}</h3>
                      <p className="text-gray-600">{t('explanation.preferences.detailDescription')}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{t('explanation.preferences.formatTitle')}</h3>
                      <p className="text-gray-600">{t('explanation.preferences.formatDescription')}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{t('explanation.preferences.languageTitle')}</h3>
                      <p className="text-gray-600">{t('explanation.preferences.languageDescription')}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow-md overflow-hidden mt-6">
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('explanation.preferences.exampleTitle')}</h2>
                  
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{t('explanation.preferences.exampleHeader')}</h3>
                    <p className="text-gray-600 mb-4">{t('explanation.preferences.exampleDescription')}</p>
                    
                    <div className="bg-white p-4 rounded-md border border-gray-200">
                      <div className="text-sm text-gray-500 mb-1">{t('explanation.preferences.exampleFactors')}</div>
                      <ul className="list-disc list-inside text-gray-700 mb-3">
                        <li>{t('explanation.preferences.exampleFactor1')}</li>
                        <li>{t('explanation.preferences.exampleFactor2')}</li>
                        <li>{t('explanation.preferences.exampleFactor3')}</li>
                      </ul>
                      
                      <div className="text-sm text-gray-500 mb-1">{t('explanation.preferences.exampleSummary')}</div>
                      <p className="text-gray-700">{t('explanation.preferences.exampleText')}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ExplanationPreferencesPage;
