import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../hooks/useAuthContext';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import LoadingSpinner from '../components/atoms/LoadingSpinner/LoadingSpinner';
import ConfirmationDialog from '../components/ui/ConfirmationDialog';
import { socialVideoService, Post } from '../services/api/socialVideoService';

const ScheduledContentPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  const [scheduledPosts, setScheduledPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedPostId, setSelectedPostId] = useState<string | null>(null);

  useEffect(() => {
    fetchScheduledPosts();
  }, [user]);

  const fetchScheduledPosts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const posts = await socialVideoService.getScheduledVideos(user?.id);
      setScheduledPosts(posts);
    } catch (err) {
      console.error('Error fetching scheduled posts:', err);
      setError('Une erreur est survenue lors du chargement des publications programmées');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelScheduled = (postId: string) => {
    setSelectedPostId(postId);
    setConfirmDialogOpen(true);
  };

  const handleConfirmCancel = async () => {
    if (!selectedPostId) return;

    try {
      setIsLoading(true);
      await socialVideoService.cancelScheduledVideo(selectedPostId);
      
      setSuccess('Publication programmée annulée avec succès');
      setConfirmDialogOpen(false);
      
      // Refresh the list
      fetchScheduledPosts();
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('Error cancelling scheduled post:', err);
      setError('Une erreur est survenue lors de l\'annulation de la publication programmée');
      setConfirmDialogOpen(false);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getTimeRemaining = (dateString: string) => {
    const now = new Date();
    const scheduledDate = new Date(dateString);
    const diffMs = scheduledDate.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'En cours de publication';
    
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffDays > 0) {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else if (diffHours > 0) {
      return `${diffHours} heure${diffHours > 1 ? 's' : ''} ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    } else {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    }
  };

  return (
    <>
      <Helmet>
        <title>Contenu programmé - Retreat And Be</title>
        <meta
          name="description"
          content="Gérez vos publications programmées sur la plateforme Retreat And Be."
        />
      </Helmet>

      <Navbar />

      <main className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Contenu programmé</h1>
            <p className="mt-2 text-gray-600">
              Gérez vos publications planifiées pour une diffusion future
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => navigate('/content-management')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Retour à la gestion
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 bg-green-50 border border-green-200 text-green-800 rounded-md p-4">
            {success}
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner />
          </div>
        ) : scheduledPosts.length > 0 ? (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {scheduledPosts.map((post) => (
                <li key={post.id}>
                  <div className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-16 w-24 bg-gray-100 rounded overflow-hidden">
                        {post.thumbnailUrl ? (
                          <img
                            src={post.thumbnailUrl}
                            alt={post.title}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center text-gray-400">
                            <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                        )}
                      </div>
                      <div className="ml-4 flex-1">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-retreat-green truncate">{post.title}</p>
                          <div className="ml-2 flex-shrink-0 flex">
                            <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                              Programmé
                            </p>
                          </div>
                        </div>
                        <div className="mt-1 text-sm text-gray-500 line-clamp-2">
                          {post.description}
                        </div>
                        <div className="mt-2 flex items-center justify-between">
                          <div className="flex items-center text-xs text-gray-500">
                            <span className="font-medium">Publication prévue :</span>
                            <span className="ml-1">{post.scheduledDate && formatDateTime(post.scheduledDate)}</span>
                            <span className="mx-1">•</span>
                            <span className="font-medium">Temps restant :</span>
                            <span className="ml-1">{post.scheduledDate && getTimeRemaining(post.scheduledDate)}</span>
                          </div>
                          <button
                            onClick={() => handleCancelScheduled(post.id)}
                            className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                          >
                            Annuler
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-12 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun contenu programmé</h3>
            <p className="mt-1 text-sm text-gray-500">
              Vous n'avez pas encore programmé de publications.
            </p>
            <div className="mt-6">
              <button
                onClick={() => navigate('/content-management')}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Programmer du contenu
              </button>
            </div>
          </div>
        )}
      </main>

      <Footer />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        title="Annuler la publication programmée"
        message="Êtes-vous sûr de vouloir annuler cette publication programmée ? Cette action ne peut pas être annulée."
        confirmButtonText="Annuler la publication"
        cancelButtonText="Retour"
        onConfirm={handleConfirmCancel}
        onCancel={() => setConfirmDialogOpen(false)}
      />
    </>
  );
};

export default ScheduledContentPage;
