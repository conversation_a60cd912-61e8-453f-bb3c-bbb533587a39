import React from 'react';
import { useParams } from 'react-router-dom';
import { ResetPasswordForm } from '../../components/auth/ResetPasswordForm';
import MainLayout from '../../components/templates/MainLayout/MainLayout';

const ResetPasswordPage: React.FC = () => {
  const { token } = useParams<{ token: string }>();

  if (!token) {
    // This case should ideally be handled by routing or a redirect
    // if the token is missing, but as a fallback:
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto text-center">
            <h1 className="text-2xl font-bold text-red-600">Erreur</h1>
            <p className="text-gray-700 dark:text-gray-300">Jeton de réinitialisation de mot de passe manquant ou invalide.</p>
            {/* TODO: Add a link to forgot password page or login */}
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <ResetPasswordForm token={token} />
        </div>
      </div>
    </MainLayout>
  );
};

export default ResetPasswordPage; 