import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  CalendarIcon,
  UserIcon,
  CreditCardIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import ScrollToTop from '../components/ui/ScrollToTop';
import Wallet from '../components/wallet/Wallet';

interface BookingData {
  retreatId: string;
  startDate: Date;
  endDate: Date;
  participants: {
    adults: number;
    children: number;
  };
  totalPrice: number;
}

const Booking: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [bookingData, setBookingData] = useState<BookingData>({
    retreatId: '',
    startDate: new Date(),
    endDate: new Date(),
    participants: {
      adults: 1,
      children: 0,
    },
    totalPrice: 0,
  });

  const steps = [
    {
      id: 1,
      title: 'Vérification des dates',
      icon: CalendarIcon,
      description: 'Confirmez vos dates de séjour',
    },
    {
      id: 2,
      title: 'Participants',
      icon: UserIcon,
      description: 'Indiquez le nombre de participants',
    },
    {
      id: 3,
      title: 'Paiement',
      icon: CreditCardIcon,
      description: 'Effectuez votre paiement',
    },
    {
      id: 4,
      title: 'Confirmation',
      icon: CheckCircleIcon,
      description: 'Confirmation de votre réservation',
    },
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className='space-y-6'>
            <h3 className='text-xl font-semibold'>Dates de séjour</h3>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700'>Date d'arrivée</label>
                <input
                  type='date'
                  className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  value={bookingData.startDate.toISOString().split('T')[0]}
                  onChange={(e) =>
                    setBookingData({
                      ...bookingData,
                      startDate: new Date(e.target.value),
                    })
                  }
                />
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700'>Date de départ</label>
                <input
                  type='date'
                  className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  value={bookingData.endDate.toISOString().split('T')[0]}
                  onChange={(e) =>
                    setBookingData({
                      ...bookingData,
                      endDate: new Date(e.target.value),
                    })
                  }
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className='space-y-6'>
            <h3 className='text-xl font-semibold'>Nombre de participants</h3>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700'>Adultes</label>
                <input
                  type='number'
                  min='1'
                  className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  value={bookingData.participants.adults}
                  onChange={(e) =>
                    setBookingData({
                      ...bookingData,
                      participants: {
                        ...bookingData.participants,
                        adults: parseInt(e.target.value),
                      },
                    })
                  }
                />
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700'>Enfants</label>
                <input
                  type='number'
                  min='0'
                  className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  value={bookingData.participants.children}
                  onChange={(e) =>
                    setBookingData({
                      ...bookingData,
                      participants: {
                        ...bookingData.participants,
                        children: parseInt(e.target.value),
                      },
                    })
                  }
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className='space-y-6'>
            <h3 className='text-xl font-semibold'>Paiement</h3>
            <div className='bg-gray-50 p-6 rounded-lg'>
              <h4 className='text-lg font-medium mb-4'>Récapitulatif</h4>
              <div className='space-y-2'>
                <p>
                  Dates : {bookingData.startDate.toLocaleDateString()} -{' '}
                  {bookingData.endDate.toLocaleDateString()}
                </p>
                <p>
                  Participants : {bookingData.participants.adults} adultes,{' '}
                  {bookingData.participants.children} enfants
                </p>
                <p className='text-lg font-semibold mt-4'>Total : {bookingData.totalPrice}€</p>
                <p className='text-sm text-gray-600'>ou {bookingData.totalPrice * 0.0001} RandB</p>
              </div>
            </div>
            <div className='mt-6'>
              <h4 className='text-lg font-medium mb-4'>Choisissez votre mode de paiement</h4>
              <div className='space-y-4'>
                <div className='flex items-center space-x-4'>
                  <input
                    type='radio'
                    id='card'
                    name='paymentMethod'
                    value='card'
                    className='h-4 w-4 text-retreat-green focus:ring-retreat-green'
                  />
                  <label htmlFor='card' className='text-gray-700'>
                    Carte bancaire
                  </label>
                </div>
                <div className='flex items-center space-x-4'>
                  <input
                    type='radio'
                    id='randb'
                    name='paymentMethod'
                    value='randb'
                    className='h-4 w-4 text-retreat-green focus:ring-retreat-green'
                  />
                  <label htmlFor='randb' className='text-gray-700'>
                    RandB (Crypto)
                  </label>
                </div>
              </div>

              {/* Formulaire carte bancaire */}
              <div className='mt-6 space-y-4'>
                <input
                  type='text'
                  placeholder='Numéro de carte'
                  className='block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                />
                <div className='grid grid-cols-2 gap-4'>
                  <input
                    type='text'
                    placeholder='MM/AA'
                    className='block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  />
                  <input
                    type='text'
                    placeholder='CVV'
                    className='block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                  />
                </div>
              </div>

              {/* Wallet RandB */}
              <div className='mt-6'>
                <Wallet
                  requiredAmount={bookingData.totalPrice * 0.0001}
                  onTransactionComplete={(amount) => {
                    console.log('Transaction complétée:', amount);
                    setCurrentStep(4);
                  }}
                />
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className='text-center space-y-6'>
            <CheckCircleIcon className='w-16 h-16 text-retreat-green mx-auto' />
            <h3 className='text-2xl font-semibold'>Réservation confirmée !</h3>
            <p className='text-gray-600'>
              Votre réservation a été confirmée. Vous recevrez un email de confirmation avec tous
              les détails.
            </p>
            <button
              onClick={() => navigate('/')}
              className='mt-6 px-6 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark'
            >
              Retour à l'accueil
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className='min-h-screen bg-gray-100'>
      <header className='fixed top-0 left-0 right-0 bg-white shadow-sm z-50'>
        <NavBarClient />
      </header>

      <main className='pt-20 pb-12'>
        <div className='max-w-4xl mx-auto px-4'>
          {/* Progress bar */}
          <div className='mb-8'>
            <div className='flex justify-between mb-2'>
              {steps.map((step) => (
                <div
                  key={step.id}
                  className={`flex items-center ${
                    step.id === currentStep ? 'text-retreat-green' : 'text-gray-400'
                  }`}
                >
                  <step.icon className='w-6 h-6' />
                </div>
              ))}
            </div>
            <div className='h-2 bg-gray-200 rounded-full'>
              <motion.div
                className='h-full bg-retreat-green rounded-full'
                initial={{ width: '0%' }}
                animate={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>

          {/* Current step content */}
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className='bg-white rounded-2xl shadow-lg p-8'
          >
            <div className='mb-8'>
              <h2 className='text-2xl font-bold text-gray-900'>{steps[currentStep - 1].title}</h2>
              <p className='mt-2 text-gray-600'>{steps[currentStep - 1].description}</p>
            </div>

            {renderStepContent()}

            {/* Navigation buttons */}
            {currentStep < steps.length && (
              <div className='mt-8 flex justify-between'>
                {currentStep > 1 && (
                  <button
                    onClick={() => setCurrentStep(currentStep - 1)}
                    className='px-6 py-3 text-retreat-green hover:text-retreat-green-dark'
                  >
                    Retour
                  </button>
                )}
                <button
                  onClick={() => setCurrentStep(currentStep + 1)}
                  className='px-6 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark'
                >
                  {currentStep === steps.length - 1 ? 'Confirmer' : 'Continuer'}
                </button>
              </div>
            )}
          </motion.div>
        </div>
      </main>

      <footer>
        <Footer />
      </footer>
      <ScrollToTop />
    </div>
  );
};

export default Booking;
