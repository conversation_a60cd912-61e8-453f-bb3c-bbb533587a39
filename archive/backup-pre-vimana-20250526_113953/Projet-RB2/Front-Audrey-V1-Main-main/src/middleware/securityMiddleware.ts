import { NextFunction, Request, Response } from 'express';
import { securityService } from '../services/api/securityService';

/**
 * Middleware pour ajouter les en-têtes de sécurité à toutes les réponses
 */
export const securityHeadersMiddleware = (_req: Request, res: Response, next: NextFunction) => {
  // Content Security Policy (CSP)
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://api.mapbox.com https://events.mapbox.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://api.mapbox.com https://fonts.googleapis.com; img-src 'self' data: blob: https://*.tile.openstreetmap.org https://api.mapbox.com https://* https://www.google-analytics.com; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' https://api.mapbox.com https://events.mapbox.com https://api.retreat-and-be.com https://www.google-analytics.com; frame-src 'self'; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none';"
  );

  // X-Content-Type-Options
  res.setHeader('X-Content-Type-Options', 'nosniff');

  // X-Frame-Options
  res.setHeader('X-Frame-Options', 'DENY');

  // X-XSS-Protection
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // Referrer-Policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Strict-Transport-Security (HSTS)
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

  // Feature-Policy / Permissions-Policy
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=(self), payment=()');

  next();
};

/**
 * Middleware pour la validation des entrées
 */
export const inputValidationMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Fonction pour nettoyer les entrées
  const sanitizeInput = (input: any): any => {
    if (typeof input === 'string') {
      // Nettoyer les chaînes de caractères
      return input
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');
    } else if (Array.isArray(input)) {
      // Nettoyer les tableaux récursivement
      return input.map((item) => sanitizeInput(item));
    } else if (input && typeof input === 'object') {
      // Nettoyer les objets récursivement
      const sanitizedObj: Record<string, any> = {};
      for (const key in input) {
        if (Object.prototype.hasOwnProperty.call(input, key)) {
          sanitizedObj[key] = sanitizeInput(input[key]);
        }
      }
      return sanitizedObj;
    }
    // Retourner les autres types tels quels
    return input;
  };

  // Nettoyer les paramètres de requête, le corps et les paramètres d'URL
  if (req.query) {
    req.query = sanitizeInput(req.query);
  }
  if (req.body) {
    req.body = sanitizeInput(req.body);
  }
  if (req.params) {
    req.params = sanitizeInput(req.params);
  }

  next();
};

/**
 * Middleware pour la limitation de taux (rate limiting)
 */
export const rateLimitMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Utiliser l'adresse IP comme identifiant
  const identifier = req.ip || 'unknown';

  // Vérifier si l'IP est limitée
  const isLimited = false; // À remplacer par une vérification réelle

  if (isLimited) {
    return res.status(429).json({
      error: 'Too many requests, please try again later',
    });
  }

  next();
};

/**
 * Middleware pour la journalisation des événements de sécurité
 */
export const securityLoggingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Enregistrer l'heure de début
  const startTime = Date.now();

  // Capturer la réponse d'origine
  const originalSend = res.send;

  // Remplacer la méthode send pour capturer le statut et le corps
  res.send = function (body) {
    // Restaurer la méthode d'origine
    res.send = originalSend;

    // Calculer le temps de réponse
    const responseTime = Date.now() - startTime;

    // Journaliser l'événement pour les réponses d'erreur
    if (res.statusCode >= 400) {
      const logData = {
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        responseTime,
      };

      // Journaliser l'événement de sécurité
      console.log('Security event:', logData);

      // Dans un environnement de production, on enverrait cet événement au backend
      // securityService.logSecurityEvent(logData);
    }

    // Appeler la méthode d'origine
    return originalSend.call(this, body);
  };

  next();
};

/**
 * Exporter tous les middlewares de sécurité
 */
export const securityMiddleware = {
  securityHeaders: securityHeadersMiddleware,
  inputValidation: inputValidationMiddleware,
  rateLimit: rateLimitMiddleware,
  securityLogging: securityLoggingMiddleware,
};
