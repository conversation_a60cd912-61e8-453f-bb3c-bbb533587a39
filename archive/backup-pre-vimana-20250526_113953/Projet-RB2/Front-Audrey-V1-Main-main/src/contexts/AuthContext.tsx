import React, { createContext, useState, useEffect, use<PERSON><PERSON>back, ReactNode } from 'react';
import {
  authService,
  LoginCredentials,
  RegistrationData,
  AuthResponse,
  RegistrationResponse,
} from '../services/api/authService';
import { userService, User } from '../services/api/userService';

interface PlaceholderChangePasswordRequest {
  oldPassword?: string;
  newPassword?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (userData: RegistrationData) => Promise<void>;
  logout: () => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  requestVerificationEmail: (email: string) => Promise<void>;
  changePassword: (passwordData: PlaceholderChangePasswordRequest) => Promise<void>;
  clearError: () => void;
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  error: null,
  isAuthenticated: false,
  login: async () => { throw new Error('Login function not ready'); },
  register: async () => { throw new Error('Register function not ready'); },
  logout: async () => { throw new Error('Logout function not ready'); },
  requestPasswordReset: async () => { throw new Error('requestPasswordReset function not ready'); },
  resetPassword: async () => { throw new Error('resetPassword function not ready'); },
  verifyEmail: async () => { throw new Error('verifyEmail function not ready'); },
  requestVerificationEmail: async () => { throw new Error('requestVerificationEmail function not ready'); },
  changePassword: async () => { throw new Error('changePassword function not ready'); },
  clearError: () => {},
});

// Hook pour utiliser le contexte d'authentification
export const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  useEffect(() => {
    const checkAuth = async () => {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      if (token) {
        try {
          const userData = await userService.getProfile();
          setUser(userData);
          setIsAuthenticated(true);
        } catch (e) {
          console.error('Auth check failed, clearing tokens:', e);
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          setUser(null);
          setIsAuthenticated(false);
        }
      }
      setIsLoading(false);
    };
    checkAuth();
  }, []);

  const login = useCallback(async (credentials: LoginCredentials): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      const response: AuthResponse = await authService.login(credentials);
      localStorage.setItem('token', response.token);
      localStorage.setItem('refreshToken', response.refreshToken);
      setUser(response.user);
      setIsAuthenticated(true);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue lors de la connexion');
      } else {
        setError('Une erreur inconnue est survenue lors de la connexion');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (userData: RegistrationData): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      const response: RegistrationResponse = await authService.register(userData);
      if (response.token && response.refreshToken) {
        localStorage.setItem('token', response.token);
        localStorage.setItem('refreshToken', response.refreshToken);
        setUser(response.user);
        setIsAuthenticated(true);
      } else {
        console.log(response.message || 'Registration successful, awaiting next steps.');
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || "Une erreur est survenue lors de l'inscription");
      } else {
        setError("Une erreur inconnue est survenue lors de l'inscription");
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await authService.logout();
    } catch (err: unknown) {
      console.error('Server-side logout error:', err);
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue lors de la déconnexion (serveur).');
      } else {
        setError('Une erreur inconnue est survenue lors de la déconnexion (serveur).');
      }
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      setUser(null);
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  }, []);

  const requestPasswordReset = useCallback(async (email: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await authService.requestPasswordReset(email);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue lors de la demande de réinitialisation');
      } else {
        setError('Une erreur inconnue est survenue lors de la demande de réinitialisation');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const resetPassword = useCallback(async (token: string, newPassword: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await authService.resetPassword(token, newPassword);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(
          err.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe'
        );
      } else {
        setError('Une erreur inconnue est survenue lors de la réinitialisation du mot de passe');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const verifyEmail = useCallback(async (token: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await authService.verifyEmail(token);
      if (response.user) {
        setUser(response.user);
      } else {
        const updatedUser = await userService.getProfile();
        setUser(updatedUser);
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue lors de la vérification de l\'e-mail');
      } else {
        setError('Une erreur inconnue est survenue lors de la vérification de l\'e-mail');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const requestVerificationEmail = useCallback(async (email: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await authService.requestVerificationEmail(email);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue lors de la demande de l\'e-mail de vérification');
      } else {
        setError('Une erreur inconnue est survenue lors de la demande de l\'e-mail de vérification');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const changePassword = useCallback(async (passwordData: PlaceholderChangePasswordRequest): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      console.warn('authService.changePassword not fully implemented yet.');
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue lors du changement de mot de passe');
      } else {
        setError('Une erreur inconnue est survenue lors du changement de mot de passe');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const value: AuthContextType = {
    user,
    isLoading,
    error,
    isAuthenticated,
    login,
    register,
    logout,
    requestPasswordReset,
    resetPassword,
    verifyEmail,
    requestVerificationEmail,
    changePassword,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
