import React, { createContext, useState, useCallback, ReactNode } from 'react';
import Toast, { ToastType } from '../components/common/Toast';

// Interface pour une notification
interface Notification {
  id: string;
  type: ToastType;
  message: string;
  duration?: number;
}

// Interface pour le contexte de notification
interface NotificationContextType {
  notifications: Notification[];
  addNotification: (type: ToastType, message: string, duration?: number) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

// Créer le contexte
export const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  addNotification: () => {},
  removeNotification: () => {},
  clearNotifications: () => {},
});

// Hook pour utiliser le contexte de notification
export const useNotification = () => {
  const context = React.useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

/**
 * Fournisseur de contexte pour les notifications
 */
export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  /**
   * Supprimer une notification par son ID
   * @param id ID de la notification à supprimer
   */
  const removeNotification = useCallback((id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id));
  }, []);

  /**
   * Ajouter une notification
   * @param type Type de notification
   * @param message Message à afficher
   * @param duration Durée d'affichage en ms (0 pour une durée infinie)
   */
  const addNotification = useCallback(
    (type: ToastType, message: string, duration: number = 5000) => {
      const id = Date.now().toString();

      setNotifications((prev) => [...prev, { id, type, message, duration }]);

      // Si la durée n'est pas infinie, supprimer automatiquement la notification après la durée spécifiée
      if (duration > 0) {
        setTimeout(() => {
          removeNotification(id);
        }, duration);
      }
    },
    [removeNotification]
  );

  /**
   * Supprimer toutes les notifications
   */
  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Valeur du contexte
  const value = {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}

      {/* Afficher les notifications */}
      <div className='notification-container'>
        {notifications.map((notification, index) => (
          <Toast
            key={notification.id}
            type={notification.type}
            message={notification.message}
            duration={notification.duration}
            onClose={() => removeNotification(notification.id)}
            position='top-right'
            // Décaler les notifications pour éviter qu'elles ne se superposent
            // @ts-expect-error - Ajouter un style personnalisé
            style={{ top: `${index * 4 + 1}rem` }}
          />
        ))}
      </div>
    </NotificationContext.Provider>
  );
};
