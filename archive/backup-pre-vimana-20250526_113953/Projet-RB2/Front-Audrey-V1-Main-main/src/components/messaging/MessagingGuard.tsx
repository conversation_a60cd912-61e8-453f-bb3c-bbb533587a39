import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { MessagingProvider } from '../../contexts/MessagingContext';

interface MessagingGuardProps {
  children: React.ReactNode;
}

/**
 * Composant de garde pour protéger les routes de messagerie
 * - Vérifie que l'utilisateur est authentifié
 * - Fournit le contexte de messagerie aux composants enfants
 */
const MessagingGuard: React.FC<MessagingGuardProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Si l'authentification est en cours de chargement, afficher un indicateur de chargement
  if (isLoading) {
    return (
      <div className='flex justify-center items-center h-screen'>
        <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-green-500'></div>
      </div>
    );
  }

  // Si l'utilisateur n'est pas authentifié, rediriger vers la page de connexion
  if (!isAuthenticated) {
    return <Navigate to='/login' state={{ from: location }} replace />;
  }

  // Si l'utilisateur est authentifié, fournir le contexte de messagerie et afficher les composants enfants
  return <MessagingProvider>{children}</MessagingProvider>;
};

export default MessagingGuard;
