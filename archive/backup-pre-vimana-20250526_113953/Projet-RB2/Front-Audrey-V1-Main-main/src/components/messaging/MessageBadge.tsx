import React from 'react';
import { Link } from 'react-router-dom';
import { ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import { useUnreadMessages } from '../../hooks/useUnreadMessages';
import { useAuth } from '../../contexts/AuthContext';

interface MessageBadgeProps {
  className?: string;
  iconClassName?: string;
  badgeClassName?: string;
}

/**
 * Composant affichant une icône de messagerie avec un badge indiquant le nombre de messages non lus
 */
const MessageBadge: React.FC<MessageBadgeProps> = ({
  className = 'text-gray-600 hover:text-retreat-green relative',
  iconClassName = 'h-6 w-6',
  badgeClassName = 'absolute -top-1 -right-1 bg-retreat-green text-white text-xs rounded-full w-4 h-4 flex items-center justify-center',
}) => {
  const { unreadCount } = useUnreadMessages();
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return null;
  }

  return (
    <Link to='/messaging' className={className}>
      <ChatBubbleLeftRightIcon className={iconClassName} />
      {unreadCount > 0 && (
        <span className={badgeClassName}>{unreadCount > 9 ? '9+' : unreadCount}</span>
      )}
    </Link>
  );
};

export default MessageBadge;
