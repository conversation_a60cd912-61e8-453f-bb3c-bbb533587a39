import { useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { messagingNotificationService } from '../../services/messagingNotificationService';
import { useNotification } from '../../contexts/NotificationContext';

/**
 * Composant qui initialise le service de notification de messagerie
 * Ce composant ne rend rien, il se contente d'initialiser le service
 */
const MessagingNotificationInitializer = () => {
  const { isAuthenticated } = useAuth();
  const { addNotification: showNotification } = useNotification();

  useEffect(() => {
    // Initialiser le service de notification uniquement si l'utilisateur est authentifié
    if (isAuthenticated) {
      messagingNotificationService.init();

      // S'abonner aux notifications de nouveaux messages
      const unsubscribeNewMessage = messagingNotificationService.onNewMessage((message) => {
        // Afficher une notification système
        messagingNotificationService.showNotification(message);

        // Afficher une notification dans l'application
        const notificationTitle = 'Nouveau message';
        const notificationMessage = `${message.sender?.firstName || "Quelqu'un"} vous a envoyé un message`;
        showNotification('info', `${notificationTitle}: ${notificationMessage}`, 5000);
        // TODO: Handle action (onClick to navigate) if Toast component is enhanced
      });

      // Nettoyer lors du démontage du composant
      return () => {
        unsubscribeNewMessage();
        messagingNotificationService.disconnect();
      };
    }
  }, [isAuthenticated, showNotification]);

  // Ce composant ne rend rien
  return null;
};

export default MessagingNotificationInitializer;
