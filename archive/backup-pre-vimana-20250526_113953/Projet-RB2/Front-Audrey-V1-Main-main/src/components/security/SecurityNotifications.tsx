import React, { useState, useEffect } from 'react';
import { useSecurity } from '../../contexts/SecurityContext';
import { Link } from 'react-router-dom';

interface SecurityNotification {
  id: string;
  title: string;
  message: string;
  severity: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
  type: string;
  read: boolean;
  createdAt: string;
  readAt?: string;
  actionRequired: boolean;
  actionUrl?: string;
  actionText?: string;
}

interface SecurityNotificationsProps {
  limit?: number;
  showAll?: boolean;
  onNotificationClick?: (notification: SecurityNotification) => void;
}

const SecurityNotifications: React.FC<SecurityNotificationsProps> = ({
  limit = 5,
  showAll = false,
  onNotificationClick,
}) => {
  const {
    isLoading,
    error,
    getSecurityNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
  } = useSecurity();
  const [notifications, setNotifications] = useState<SecurityNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  // Fetch notifications
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const response = await getSecurityNotifications({
          limit: showAll ? 20 : limit,
          page: 1,
        });

        setNotifications(response.notifications);
        setUnreadCount(response.unreadCount);
        setTotalCount(response.total);
      } catch (err) {
        console.error('Error fetching security notifications:', err);
      }
    };

    fetchNotifications();

    // Set up polling for new notifications
    const intervalId = setInterval(fetchNotifications, 60000); // Poll every minute

    return () => clearInterval(intervalId);
  }, [getSecurityNotifications, limit, showAll]);

  const handleNotificationClick = async (notification: SecurityNotification) => {
    if (!notification.read) {
      try {
        await markNotificationAsRead(notification.id);

        // Update local state
        setNotifications((prev) =>
          prev.map((n) => (n.id === notification.id ? { ...n, read: true } : n))
        );
        setUnreadCount((prev) => Math.max(0, prev - 1));
      } catch (err) {
        console.error('Error marking notification as read:', err);
      }
    }

    if (onNotificationClick) {
      onNotificationClick(notification);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsAsRead();

      // Update local state
      setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
      setUnreadCount(0);
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-100 border-red-500 text-red-800';
      case 'ERROR':
        return 'bg-orange-100 border-orange-500 text-orange-800';
      case 'WARNING':
        return 'bg-yellow-100 border-yellow-500 text-yellow-800';
      case 'INFO':
      default:
        return 'bg-blue-100 border-blue-500 text-blue-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  if (isLoading && notifications.length === 0) {
    return (
      <div className='animate-pulse'>
        <div className='h-4 bg-gray-200 rounded w-3/4 mb-2.5'></div>
        <div className='h-4 bg-gray-200 rounded w-1/2 mb-2.5'></div>
        <div className='h-4 bg-gray-200 rounded w-5/6 mb-2.5'></div>
        <div className='h-4 bg-gray-200 rounded w-2/3 mb-2.5'></div>
        <div className='h-4 bg-gray-200 rounded w-3/4 mb-2.5'></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='bg-red-100 border-l-4 border-red-500 text-red-700 p-4'>
        <p className='font-bold'>Erreur</p>
        <p>{error}</p>
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className='text-center py-4 text-gray-500'>
        <svg
          className='mx-auto h-12 w-12 text-gray-400'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9'
          />
        </svg>
        <p className='mt-2'>Aucune notification de sécurité</p>
      </div>
    );
  }

  return (
    <div className='bg-white rounded-lg shadow overflow-hidden'>
      <div className='flex justify-between items-center p-4 border-b'>
        <h3 className='text-lg font-medium text-gray-900'>
          Notifications de sécurité
          {unreadCount > 0 && (
            <span className='ml-2 bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded'>
              {unreadCount} non lues
            </span>
          )}
        </h3>
        {unreadCount > 0 && (
          <button
            onClick={handleMarkAllAsRead}
            className='text-sm text-blue-600 hover:text-blue-800'
          >
            Tout marquer comme lu
          </button>
        )}
      </div>

      <ul className='divide-y divide-gray-200'>
        {notifications.map((notification) => (
          <li
            key={notification.id}
            role='button'
            tabIndex={0}
            className={`p-4 hover:bg-gray-50 transition-colors duration-150 ${
              !notification.read ? 'bg-gray-50' : ''
            }`}
            onClick={() => handleNotificationClick(notification)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleNotificationClick(notification);
              }
            }}
          >
            <div className='flex items-start'>
              <div className='flex-shrink-0'>
                {!notification.read && (
                  <span className='inline-block h-2 w-2 rounded-full bg-blue-600'></span>
                )}
              </div>
              <div className='ml-3 flex-1'>
                <div className='flex items-center justify-between'>
                  <p className='text-sm font-medium text-gray-900'>{notification.title}</p>
                  <p className='text-xs text-gray-500'>{formatDate(notification.createdAt)}</p>
                </div>
                <div
                  className={`mt-1 text-sm ${
                    notification.read ? 'text-gray-500' : 'text-gray-900'
                  }`}
                >
                  {notification.message}
                </div>
                {notification.actionRequired && notification.actionUrl && (
                  <div className='mt-2'>
                    <Link
                      to={notification.actionUrl}
                      className='inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                    >
                      {notification.actionText || 'Voir les détails'}
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </li>
        ))}
      </ul>

      {!showAll && totalCount > limit && (
        <div className='p-4 border-t text-center'>
          <Link to='/security/notifications' className='text-sm text-blue-600 hover:text-blue-800'>
            Voir toutes les notifications ({totalCount})
          </Link>
        </div>
      )}
    </div>
  );
};

export default SecurityNotifications;
