import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { socialVideoService } from '../../services/api/socialVideoService';

interface UploadContentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const UploadContentDialog: React.FC<UploadContentDialogProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [privacy, setPrivacy] = useState<'public' | 'friends' | 'private'>('public');
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const selectedFile = acceptedFiles[0];
      setFile(selectedFile);

      // Create preview for image or video
      if (selectedFile.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = () => {
          setPreview(reader.result as string);
        };
        reader.readAsDataURL(selectedFile);
      } else if (selectedFile.type.startsWith('video/')) {
        const videoElement = document.createElement('video');
        videoElement.preload = 'metadata';
        videoElement.onloadedmetadata = () => {
          URL.revokeObjectURL(videoElement.src);
          
          // Create a canvas to capture the thumbnail
          const canvas = document.createElement('canvas');
          canvas.width = videoElement.videoWidth;
          canvas.height = videoElement.videoHeight;
          const ctx = canvas.getContext('2d');
          ctx?.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
          setPreview(canvas.toDataURL('image/jpeg'));
        };
        videoElement.src = URL.createObjectURL(selectedFile);
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': [],
      'video/*': []
    },
    maxFiles: 1,
    maxSize: 100 * 1024 * 1024 // 100MB max size
  });

  const handleAddTag = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      if (!tags.includes(tagInput.trim())) {
        setTags([...tags, tagInput.trim()]);
      }
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!file) {
      setError('Veuillez sélectionner un fichier à télécharger');
      return;
    }

    if (!title.trim()) {
      setError('Veuillez ajouter un titre');
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          if (newProgress >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return newProgress;
        });
      }, 500);

      // Upload the content
      await socialVideoService.uploadVideo({
        title,
        description,
        file,
        tags,
        privacy
      });

      clearInterval(progressInterval);
      setUploadProgress(100);
      
      // Reset form
      setTitle('');
      setDescription('');
      setTags([]);
      setFile(null);
      setPreview(null);
      setPrivacy('public');
      
      onSuccess();
      onClose();
    } catch (err) {
      console.error('Error uploading content:', err);
      setError('Une erreur est survenue lors du téléchargement. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Télécharger du contenu
                  </h3>
                  
                  {error && (
                    <div className="mb-4 bg-red-50 border border-red-200 text-red-800 rounded-md p-3">
                      {error}
                    </div>
                  )}
                  
                  <div className="mt-2 space-y-4">
                    {/* File upload area */}
                    <div 
                      {...getRootProps()} 
                      className={`border-2 border-dashed rounded-md p-6 text-center cursor-pointer ${
                        isDragActive ? 'border-retreat-green bg-green-50' : 'border-gray-300 hover:border-retreat-green'
                      }`}
                    >
                      <input {...getInputProps()} />
                      
                      {preview ? (
                        <div className="flex flex-col items-center">
                          <img 
                            src={preview} 
                            alt="Preview" 
                            className="max-h-40 max-w-full mb-2 rounded"
                          />
                          <p className="text-sm text-gray-500">
                            {file?.name} ({(file?.size ? file.size / 1024 / 1024 : 0).toFixed(2)} MB)
                          </p>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              setFile(null);
                              setPreview(null);
                            }}
                            className="mt-2 text-sm text-red-600 hover:text-red-800"
                          >
                            Supprimer
                          </button>
                        </div>
                      ) : (
                        <>
                          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          <p className="mt-1 text-sm text-gray-600">
                            Glissez-déposez un fichier ici, ou cliquez pour sélectionner
                          </p>
                          <p className="mt-1 text-xs text-gray-500">
                            Images ou vidéos (max 100MB)
                          </p>
                        </>
                      )}
                    </div>
                    
                    {/* Title */}
                    <div>
                      <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                        Titre <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="title"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm"
                        required
                      />
                    </div>
                    
                    {/* Description */}
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                        Description
                      </label>
                      <textarea
                        id="description"
                        rows={3}
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm"
                      />
                    </div>
                    
                    {/* Tags */}
                    <div>
                      <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
                        Tags
                      </label>
                      <div className="mt-1 flex flex-wrap gap-2 mb-2">
                        {tags.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() => handleRemoveTag(tag)}
                              className="ml-1.5 inline-flex text-green-400 hover:text-green-600 focus:outline-none"
                            >
                              <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </span>
                        ))}
                      </div>
                      <input
                        type="text"
                        id="tags"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyDown={handleAddTag}
                        placeholder="Ajouter un tag (appuyez sur Entrée)"
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm"
                      />
                    </div>
                    
                    {/* Privacy */}
                    <div>
                      <label htmlFor="privacy" className="block text-sm font-medium text-gray-700">
                        Visibilité
                      </label>
                      <select
                        id="privacy"
                        value={privacy}
                        onChange={(e) => setPrivacy(e.target.value as 'public' | 'friends' | 'private')}
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm rounded-md"
                      >
                        <option value="public">Public</option>
                        <option value="friends">Amis</option>
                        <option value="private">Privé</option>
                      </select>
                    </div>
                    
                    {/* Upload progress */}
                    {isLoading && (
                      <div className="mt-4">
                        <div className="relative pt-1">
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-retreat-green bg-green-200">
                                Téléchargement
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-retreat-green">
                                {uploadProgress}%
                              </span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-green-200">
                            <div 
                              style={{ width: `${uploadProgress}%` }} 
                              className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-retreat-green"
                            ></div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-retreat-green text-base font-medium text-white hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green sm:ml-3 sm:w-auto sm:text-sm ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isLoading ? 'Téléchargement...' : 'Télécharger'}
              </button>
              <button
                type="button"
                disabled={isLoading}
                onClick={onClose}
                className={`mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Annuler
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UploadContentDialog;
