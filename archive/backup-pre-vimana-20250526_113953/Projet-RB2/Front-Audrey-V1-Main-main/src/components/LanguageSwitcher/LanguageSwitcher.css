.language-switcher {
  position: relative;
  display: inline-block;
  font-size: 14px;
}

/* Style pour la variante dropdown */
.language-switcher--dropdown .language-switcher__select {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  outline: none;
  min-width: 120px;
}

.language-switcher--dropdown .language-switcher__select:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Style pour la variante buttons */
.language-switcher--buttons {
  display: flex;
  gap: 8px;
}

.language-switcher--buttons .language-switcher__button {
  padding: 6px 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.language-switcher--buttons .language-switcher__button:hover {
  background-color: #f5f5f5;
}

.language-switcher--buttons .language-switcher__button--active {
  background-color: #4caf50;
  color: white;
  border-color: #4caf50;
}

/* Style pour la variante select */
.language-switcher--select {
  position: relative;
}

.language-switcher--select .language-switcher__current {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  min-width: 120px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.language-switcher--select .language-switcher__current::after {
  content: '▼';
  font-size: 10px;
  margin-left: 8px;
}

.language-switcher--select .language-switcher__options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 4px 4px;
  z-index: 10;
  display: none;
}

.language-switcher--select:hover .language-switcher__options {
  display: block;
}

.language-switcher--select .language-switcher__option {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.language-switcher--select .language-switcher__option:hover {
  background-color: #f5f5f5;
}

.language-switcher--select .language-switcher__option--active {
  background-color: #e8f5e9;
  color: #4caf50;
}
