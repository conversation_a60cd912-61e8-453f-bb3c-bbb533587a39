import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { diversityFairnessService } from '../../services/api/diversityFairnessService';
import { useAuthContext } from '../../hooks/useAuthContext';
import { toast } from 'react-toastify';
import { t } from '../../services/i18n/i18nService';
import RetreatCard from '../retreats/RetreatCard';
import LoadingSpinner from '../common/LoadingSpinner';

/**
 * Interface pour les propriétés du composant
 */
interface DiscoverSectionProps {
  /** Nombre de recommandations à afficher */
  count?: number;
  
  /** Titre de la section */
  title?: string;
  
  /** Description de la section */
  description?: string;
  
  /** Classe CSS supplémentaire */
  className?: string;
}

/**
 * Composant pour la section "Découvrir"
 */
const DiscoverSection: React.FC<DiscoverSectionProps> = ({
  count = 3,
  title = t('discover.title'),
  description = t('discover.description'),
  className = '',
}) => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  
  // Charger les recommandations surprenantes
  useEffect(() => {
    const loadDiscoverRecommendations = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const discoverRecommendations = await diversityFairnessService.getDiscoverRecommendations(count);
        setRecommendations(discoverRecommendations);
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations surprenantes:', error);
        setError(t('discover.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (user) {
      loadDiscoverRecommendations();
    }
  }, [user, count]);
  
  // Gérer le clic sur "Découvrir plus"
  const handleDiscoverMore = () => {
    navigate('/discover');
  };
  
  // Gérer le rafraîchissement des recommandations
  const handleRefresh = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const discoverRecommendations = await diversityFairnessService.getDiscoverRecommendations(count);
      setRecommendations(discoverRecommendations);
      
      toast.success(t('discover.refreshSuccess'));
    } catch (error) {
      console.error('Erreur lors du rafraîchissement des recommandations surprenantes:', error);
      setError(t('discover.refreshError'));
      toast.error(t('discover.refreshError'));
    } finally {
      setLoading(false);
    }
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600 mb-6">{description}</p>
        
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }
  
  // Afficher un message d'erreur
  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600 mb-6">{description}</p>
        
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
        
        <div className="flex justify-center">
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
          >
            {t('discover.tryAgain')}
          </button>
        </div>
      </div>
    );
  }
  
  // Afficher les recommandations
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
          <p className="text-gray-600">{description}</p>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={handleRefresh}
            className="p-2 text-gray-500 hover:text-retreat-green transition-colors"
            title={t('discover.refresh')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
      
      {recommendations.length === 0 ? (
        <div className="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('discover.noRecommendations')}</h3>
          <p className="text-gray-500 mb-6">{t('discover.tryAgainLater')}</p>
          
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
          >
            {t('discover.refresh')}
          </button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            {recommendations.map((recommendation) => (
              <RetreatCard
                key={recommendation.id}
                retreat={recommendation}
                isSurprising={recommendation.isSurprising}
              />
            ))}
          </div>
          
          <div className="flex justify-center">
            <button
              onClick={handleDiscoverMore}
              className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
            >
              {t('discover.discoverMore')}
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default DiscoverSection;
