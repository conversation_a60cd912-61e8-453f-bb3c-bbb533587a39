import React from 'react';
import { Link } from 'react-router-dom';
import {
  HeartIcon,
  SparklesIcon,
  SunIcon,
  MoonIcon,
  FireIcon,
  BookOpenIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

interface Category {
  id: string;
  name: string;
  icon: React.ComponentType<{ className: string }>;
  count: number;
  color: string;
}

const categories: Category[] = [
  {
    id: 'bien-etre',
    name: 'Bien-être',
    icon: HeartIcon as React.ComponentType<{ className: string }>,
    count: 42,
    color: 'text-pink-500',
  },
  {
    id: 'meditation',
    name: 'Méditation',
    icon: MoonIcon as React.ComponentType<{ className: string }>,
    count: 35,
    color: 'text-indigo-500',
  },
  {
    id: 'yoga',
    name: 'Yoga',
    icon: SparklesIcon as React.ComponentType<{ className: string }>,
    count: 38,
    color: 'text-purple-500',
  },
  {
    id: 'spiritualite',
    name: 'Spiritualité',
    icon: SunIcon as React.ComponentType<{ className: string }>,
    count: 24,
    color: 'text-amber-500',
  },
  {
    id: 'developpement-personnel',
    name: 'Développement Personnel',
    icon: BookOpenIcon as React.ComponentType<{ className: string }>,
    count: 31,
    color: 'text-blue-500',
  },
  {
    id: 'nutrition',
    name: 'Nutrition',
    icon: FireIcon as React.ComponentType<{ className: string }>,
    count: 27,
    color: 'text-green-500',
  },
  {
    id: 'activites',
    name: 'Activités',
    icon: FireIcon as React.ComponentType<{ className: string }>,
    count: 19,
    color: 'text-orange-500',
  },
  {
    id: 'temoignages',
    name: 'Témoignages',
    icon: UserGroupIcon as React.ComponentType<{ className: string }>,
    count: 23,
    color: 'text-teal-500',
  },
];

const PopularCategories: React.FC = () => {
  return (
    <div className='bg-white rounded-lg shadow-sm p-6'>
      <h3 className='text-lg font-semibold text-gray-900 mb-4'>Catégories populaires</h3>
      <div className='space-y-3'>
        {categories.map((category) => (
          <Link
            key={category.id}
            to={`/blog/categorie/${category.id}`}
            className='flex items-center justify-between group p-2 rounded-lg hover:bg-gray-50 transition-colors'
          >
            <div className='flex items-center space-x-3'>
              <div className={`${category.color} group-hover:scale-110 transition-transform`}>
                <category.icon className='w-5 h-5' />
              </div>
              <span className='text-gray-700 group-hover:text-retreat-green transition-colors'>
                {category.name}
              </span>
            </div>
            <div className='flex items-center'>
              <span className='text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full'>
                {category.count}
              </span>
              <svg
                className='w-4 h-4 text-gray-400 ml-2 group-hover:translate-x-1 transition-transform'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M9 5l7 7-7 7'
                />
              </svg>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default PopularCategories;
