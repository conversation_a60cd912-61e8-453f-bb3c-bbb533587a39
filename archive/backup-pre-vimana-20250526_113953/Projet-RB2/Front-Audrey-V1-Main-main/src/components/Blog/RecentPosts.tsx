import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const RecentPosts: React.FC = () => {
  return (
    <section>
      <h2 className='text-2xl font-bold text-gray-900 mb-6'>Articles récents</h2>
      <div className='space-y-6'>
        {recentPosts.map((post) => (
          <motion.article
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className='bg-white rounded-lg shadow-sm p-6'
          >
            <div className='flex items-start space-x-4'>
              <img src={post.image} alt={post.title} className='w-24 h-24 object-cover rounded' />
              <div>
                <div className='flex items-center mb-2'>
                  <span className='text-sm text-retreat-green'>{post.category}</span>
                  <span className='mx-2'>•</span>
                  <span className='text-sm text-gray-500'>{post.date}</span>
                </div>
                <Link to={post.slug}>
                  <h3 className='text-lg font-semibold text-gray-900 mb-2'>{post.title}</h3>
                </Link>
                <p className='text-gray-600 text-sm'>{post.excerpt}</p>
              </div>
            </div>
          </motion.article>
        ))}
      </div>
    </section>
  );
};

const recentPosts = [
  {
    id: 1,
    title: '5 exercices de yoga pour débutants',
    slug: '/blog/yoga-debutants',
    excerpt: 'Commencez votre pratique du yoga avec ces postures simples et efficaces.',
    image: '/images/blog/yoga-beginners.jpg',
    category: 'Yoga',
    date: '15 Mars 2024',
  },
  // Ajoutez d'autres articles récents
];

export default RecentPosts;
