import React from 'react';
import { Link } from 'react-router-dom';
import PopularCategories from './PopularCategories';
import NewsletterBlog from './NewsletterBlog';

const BlogSidebar: React.FC = () => {
  return (
    <div className='space-y-8'>
      {/* Search */}
      <div className='bg-white rounded-lg shadow-sm p-6'>
        <h3 className='text-lg font-semibold text-gray-900 mb-4'>Rechercher</h3>
        <div className='relative'>
          <input
            type='text'
            placeholder='Rechercher un article...'
            className='w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-retreat-green'
          />
          <button className='absolute right-3 top-1/2 transform -translate-y-1/2'>🔍</button>
        </div>
      </div>

      {/* Popular Categories */}
      <PopularCategories />

      <NewsletterBlog />

      {/* Popular Tags */}
      <div className='bg-white rounded-lg shadow-sm p-6'>
        <h3 className='text-lg font-semibold text-gray-900 mb-4'>Tags populaires</h3>
        <div className='flex flex-wrap gap-2'>
          {popularTags.map((tag) => (
            <Link
              key={tag}
              to={`/blog/tag/${tag.toLowerCase()}`}
              className='px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm hover:bg-retreat-green hover:text-white transition-colors'
            >
              {tag}
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

const popularTags = [
  'Méditation',
  'Yoga',
  'Bien-être',
  'Mindfulness',
  'Nature',
  'Développement personnel',
  'Nutrition',
  'Détox',
  'Spiritualité',
];

export default BlogSidebar;
