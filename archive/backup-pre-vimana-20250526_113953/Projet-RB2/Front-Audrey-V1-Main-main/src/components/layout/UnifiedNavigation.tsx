/**
 * Navigation Unifiée - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Composant de navigation principal qui unifie l'accès
 * à tous les modules de l'application.
 */

import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '../../utils/cn';
import { Button } from '../ui/design-system/Button';
import { ThemeToggle } from '../ui/design-system/hooks/useTheme';

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ReactNode;
  description?: string;
  badge?: string;
  children?: NavigationItem[];
}

interface UnifiedNavigationProps {
  user?: {
    name: string;
    email: string;
    avatar?: string;
    role: string;
  };
  onLogout?: () => void;
}

// Configuration des modules de navigation
const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Tableau de bord',
    path: '/dashboard',
    icon: '🏠',
    description: 'Vue d\'ensemble de votre activité',
  },
  {
    id: 'retreats',
    label: 'Retraites',
    path: '/retreats',
    icon: '🧘‍♀️',
    description: 'Découvrir et réserver des retraites',
    children: [
      {
        id: 'retreats-search',
        label: 'Rechercher',
        path: '/retreats/search',
        icon: '🔍',
      },
      {
        id: 'retreats-favorites',
        label: 'Favoris',
        path: '/retreats/favorites',
        icon: '❤️',
      },
      {
        id: 'retreats-bookings',
        label: 'Mes réservations',
        path: '/retreats/bookings',
        icon: '📅',
      },
    ],
  },
  {
    id: 'professionals',
    label: 'Professionnels',
    path: '/professionals',
    icon: '👨‍⚕️',
    description: 'Trouver des professionnels du bien-être',
    children: [
      {
        id: 'professionals-search',
        label: 'Rechercher',
        path: '/professionals/search',
        icon: '🔍',
      },
      {
        id: 'professionals-matcher',
        label: 'Matching IA',
        path: '/professionals/matcher',
        icon: '🤖',
        badge: 'IA',
      },
    ],
  },
  {
    id: 'content',
    label: 'Contenu',
    path: '/content',
    icon: '📚',
    description: 'Contenus éducatifs et ressources',
    children: [
      {
        id: 'content-library',
        label: 'Bibliothèque',
        path: '/content/library',
        icon: '📖',
      },
      {
        id: 'content-courses',
        label: 'Cours',
        path: '/content/courses',
        icon: '🎓',
      },
      {
        id: 'content-videos',
        label: 'Vidéos',
        path: '/content/videos',
        icon: '🎥',
      },
    ],
  },
  {
    id: 'social',
    label: 'Communauté',
    path: '/social',
    icon: '👥',
    description: 'Connectez-vous avec la communauté',
    children: [
      {
        id: 'social-feed',
        label: 'Fil d\'actualité',
        path: '/social/feed',
        icon: '📰',
      },
      {
        id: 'social-groups',
        label: 'Groupes',
        path: '/social/groups',
        icon: '👥',
      },
      {
        id: 'social-events',
        label: 'Événements',
        path: '/social/events',
        icon: '🎉',
      },
    ],
  },
  {
    id: 'analytics',
    label: 'Analytics',
    path: '/analytics',
    icon: '📊',
    description: 'Analyses et statistiques',
    badge: 'Pro',
  },
];

// Éléments de navigation pour les créateurs
const creatorNavigationItems: NavigationItem[] = [
  {
    id: 'creator-dashboard',
    label: 'Créateur',
    path: '/creator',
    icon: '🎨',
    description: 'Espace créateur de contenu',
    children: [
      {
        id: 'creator-content',
        label: 'Mon contenu',
        path: '/creator/content',
        icon: '📝',
      },
      {
        id: 'creator-analytics',
        label: 'Analytics',
        path: '/creator/analytics',
        icon: '📈',
      },
      {
        id: 'creator-monetization',
        label: 'Monétisation',
        path: '/creator/monetization',
        icon: '💰',
      },
    ],
  },
];

export const UnifiedNavigation: React.FC<UnifiedNavigationProps> = ({
  user,
  onLogout,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const location = useLocation();
  const navigate = useNavigate();

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);
    const active = isActive(item.path);

    return (
      <div key={item.id} className="space-y-1">
        <div
          className={cn(
            'flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors',
            level > 0 && 'ml-4',
            active
              ? 'bg-primary-100 text-primary-900'
              : 'text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900'
          )}
        >
          <Link
            to={item.path}
            className="flex flex-1 items-center space-x-3"
            onClick={() => setIsOpen(false)}
          >
            <span className="text-lg">{item.icon}</span>
            <span>{item.label}</span>
            {item.badge && (
              <span className="rounded-full bg-primary-100 px-2 py-0.5 text-xs font-medium text-primary-800">
                {item.badge}
              </span>
            )}
          </Link>
          {hasChildren && (
            <button
              onClick={() => toggleExpanded(item.id)}
              className="rounded p-1 hover:bg-neutral-200"
            >
              <span className={cn('transition-transform', isExpanded && 'rotate-90')}>
                ▶
              </span>
            </button>
          )}
        </div>
        {hasChildren && isExpanded && (
          <div className="space-y-1">
            {item.children!.map((child) => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Navigation mobile */}
      <div className="lg:hidden">
        <div className="flex items-center justify-between border-b bg-white px-4 py-3">
          <Link to="/" className="flex items-center space-x-2">
            <span className="text-2xl">🧘‍♀️</span>
            <span className="text-xl font-bold text-primary-600">Retreat & Be</span>
          </Link>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsOpen(!isOpen)}
          >
            <span className="text-xl">{isOpen ? '✕' : '☰'}</span>
          </Button>
        </div>
        
        {isOpen && (
          <div className="fixed inset-0 z-50 bg-white">
            <div className="flex h-full flex-col">
              <div className="flex items-center justify-between border-b px-4 py-3">
                <span className="text-lg font-semibold">Navigation</span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                >
                  <span className="text-xl">✕</span>
                </Button>
              </div>
              <div className="flex-1 overflow-y-auto p-4">
                <nav className="space-y-2">
                  {navigationItems.map((item) => renderNavigationItem(item))}
                  {user?.role === 'creator' && (
                    <>
                      <div className="my-4 border-t" />
                      {creatorNavigationItems.map((item) => renderNavigationItem(item))}
                    </>
                  )}
                </nav>
              </div>
              <div className="border-t p-4">
                <div className="flex items-center justify-between">
                  <ThemeToggle />
                  {user && (
                    <Button variant="outline" onClick={onLogout}>
                      Déconnexion
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Navigation desktop */}
      <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
        <div className="flex flex-col flex-1 min-h-0 bg-white border-r">
          <div className="flex items-center h-16 px-4 border-b">
            <Link to="/" className="flex items-center space-x-2">
              <span className="text-2xl">🧘‍♀️</span>
              <span className="text-xl font-bold text-primary-600">Retreat & Be</span>
            </Link>
          </div>
          
          <div className="flex-1 flex flex-col overflow-y-auto">
            <nav className="flex-1 px-4 py-4 space-y-2">
              {navigationItems.map((item) => renderNavigationItem(item))}
              {user?.role === 'creator' && (
                <>
                  <div className="my-4 border-t" />
                  {creatorNavigationItems.map((item) => renderNavigationItem(item))}
                </>
              )}
            </nav>
          </div>
          
          <div className="flex-shrink-0 border-t p-4">
            <div className="space-y-3">
              <ThemeToggle />
              {user && (
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {user.avatar ? (
                      <img
                        className="h-8 w-8 rounded-full"
                        src={user.avatar}
                        alt={user.name}
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                        <span className="text-sm font-medium text-primary-600">
                          {user.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-neutral-900 truncate">
                      {user.name}
                    </p>
                    <p className="text-xs text-neutral-500 truncate">
                      {user.email}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onLogout}
                    className="flex-shrink-0"
                  >
                    ↗
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
