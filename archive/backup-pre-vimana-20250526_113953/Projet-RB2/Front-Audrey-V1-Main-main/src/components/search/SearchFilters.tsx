import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  MapPinIcon,
  CalendarIcon,
  CurrencyEuroIcon,
  UsersIcon,
  TagIcon,
  HandRaisedIcon,
  SparklesIcon,
  HomeIcon,
  PhoneXMarkIcon,
} from '@heroicons/react/24/outline';

interface FilterOption {
  label: string;
  value: string;
  icon?: React.ComponentType<{ className: string }>;
  description?: string;
}

interface PresetFilter {
  label: string;
  icon: React.ComponentType<{ className: string }>;
  description: string;
  filters: {
    location?: string;
    duration?: string;
    price?: string;
    experience?: string;
    theme?: string[];
    dietaryRestrictions?: string[];
    wellnessPreferences?: string[];
    adults?: number;
    children?: number;
  };
}

const SearchFilters: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [adults, setAdults] = useState(2);
  const [children, setChildren] = useState(0);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);

  const presetFilters: PresetFilter[] = [
    {
      label: 'Détox & Bien-être',
      icon: SparklesIcon as React.ComponentType<{ className: string }>,
      description: 'Séjours axés sur la purification et le ressourcement',
      filters: {
        theme: ['detox', 'wellness'],
        dietaryRestrictions: ['gluten-free', 'vegan'],
        duration: 'week',
        wellnessPreferences: ['massage', 'spa'],
      },
    },
    {
      label: 'Retraite Familiale',
      icon: HomeIcon as React.ComponentType<{ className: string }>,
      description: 'Activités pour toute la famille',
      filters: {
        theme: ['yoga', 'nature'],
        adults: 2,
        children: 2,
        dietaryRestrictions: ['family-friendly'],
      },
    },
    {
      label: 'Digital Detox',
      icon: PhoneXMarkIcon as React.ComponentType<{ className: string }>,
      description: 'Déconnexion totale en pleine nature',
      filters: {
        location: 'nature',
        theme: ['meditation', 'nature'],
        wellnessPreferences: ['silence', 'isolation'],
      },
    },
    {
      label: 'Thérapies Holistiques',
      icon: SparklesIcon as React.ComponentType<{ className: string }>,
      description: 'Approche globale corps-esprit',
      filters: {
        theme: ['sophrology', 'sound-therapy', 'naturopathy'],
        wellnessPreferences: ['massage', 'aromatherapy'],
        duration: 'week',
      },
    },
    {
      label: 'Expression & Mouvement',
      icon: HandRaisedIcon as React.ComponentType<{ className: string }>,
      description: 'Libération par le mouvement et la danse',
      filters: {
        theme: ['contemporary-dance', 'biodanza', 'meditative-dance'],
        wellnessPreferences: ['sound-therapy'],
        duration: 'weekend',
      },
    },
    {
      label: 'Thérapies Naturelles',
      icon: SparklesIcon as React.ComponentType<{ className: string }>,
      description: 'Guérison par les éléments naturels',
      filters: {
        theme: ['forest-therapy', 'herbal-therapy', 'aromatherapy'],
        location: 'nature',
        wellnessPreferences: ['silence', 'outdoor-activities'],
      },
    },
  ];

  const locations: FilterOption[] = [
    { label: 'France', value: 'france' },
    { label: 'Espagne', value: 'spain' },
    { label: 'Italie', value: 'italy' },
    { label: 'Portugal', value: 'portugal' },
    { label: 'Bali', value: 'bali' },
    { label: 'Thaïlande', value: 'thailand' },
  ];

  const durations: FilterOption[] = [
    { label: 'Weekend (2-3 jours)', value: 'weekend' },
    { label: 'Court séjour (4-5 jours)', value: 'short' },
    { label: '1 semaine', value: 'week' },
    { label: '2 semaines', value: '2-weeks' },
    { label: '1 mois', value: 'month' },
  ];

  const prices: FilterOption[] = [
    { label: '< 500€', value: '0-500' },
    { label: '500€ - 1000€', value: '500-1000' },
    { label: '1000€ - 2000€', value: '1000-2000' },
    { label: '2000€ - 3000€', value: '2000-3000' },
    { label: '> 3000€', value: '3000+' },
  ];

  const experiences: FilterOption[] = [
    { label: 'Débutant', value: 'beginner' },
    { label: 'Intermédiaire', value: 'intermediate' },
    { label: 'Avancé', value: 'advanced' },
    { label: 'Tous niveaux', value: 'all-levels' },
  ];

  const themes: FilterOption[] = [
    // Pratiques traditionnelles
    { label: 'Yoga', value: 'yoga' },
    { label: 'Méditation', value: 'meditation' },
    { label: 'Bien-être', value: 'wellness' },
    { label: 'Detox', value: 'detox' },
    { label: 'Sport', value: 'sport' },
    { label: 'Spirituel', value: 'spiritual' },
    { label: 'Nature', value: 'nature' },

    // Thérapies alternatives
    { label: 'Sophrologie', value: 'sophrology' },
    { label: 'Sonothérapie', value: 'sound-therapy' },
    { label: 'Hypnothérapie', value: 'hypnotherapy' },
    { label: 'Lithothérapie', value: 'crystal-therapy' },
    { label: 'Réflexologie', value: 'reflexology' },
    { label: 'Naturopathie', value: 'naturopathy' },
    { label: 'Acupuncture', value: 'acupuncture' },
    { label: 'Ostéopathie', value: 'osteopathy' },
    { label: 'Shiatsu', value: 'shiatsu' },

    // Expression corporelle
    { label: 'Danse contemporaine', value: 'contemporary-dance' },
    { label: 'Danse méditative', value: 'meditative-dance' },
    { label: 'Biodanza', value: 'biodanza' },
    { label: 'Qi Gong', value: 'qigong' },
    { label: 'Tai Chi', value: 'taichi' },

    // Thérapies créatives
    { label: 'Musicothérapie', value: 'music-therapy' },
    { label: 'Art-thérapie', value: 'art-therapy' },
    { label: 'Théâtre-thérapie', value: 'drama-therapy' },
    { label: 'Expression vocale', value: 'vocal-expression' },

    // Thérapies naturelles
    { label: 'Sylvothérapie', value: 'forest-therapy' },
    { label: 'Hydrothérapie', value: 'hydrotherapy' },
    { label: 'Aromathérapie', value: 'aromatherapy' },
    { label: 'Phytothérapie', value: 'herbal-therapy' },

    // Développement personnel
    { label: 'Constellation familiale', value: 'family-constellation' },
    { label: 'PNL', value: 'nlp' },
    { label: 'Analyse transactionnelle', value: 'transactional-analysis' },
    { label: 'Ennéagramme', value: 'enneagram' },
  ];

  const dietaryRestrictions: FilterOption[] = [
    { label: 'Sans Gluten', value: 'gluten-free' },
    { label: 'Sans Lactose', value: 'lactose-free' },
    { label: 'Végétarien', value: 'vegetarian' },
    { label: 'Végétalien', value: 'vegan' },
    { label: 'Cru', value: 'raw' },
    { label: 'Jeûne Intermittent', value: 'intermittent-fasting' },
  ];

  const wellnessPreferences: FilterOption[] = [
    { label: 'Massages', value: 'massage' },
    { label: 'Spa & Balnéo', value: 'spa' },
    { label: 'Soins Ayurvédiques', value: 'ayurveda' },
    { label: 'Thérapie Sonore', value: 'sound-therapy' },
    { label: 'Sylvothérapie', value: 'forest-therapy' },
    { label: 'Aromathérapie', value: 'aromatherapy' },
    { label: 'Acupuncture', value: 'acupuncture' },
    { label: 'Reiki', value: 'reiki' },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className='bg-white py-6 shadow-sm'
    >
      {/* Preset Filters */}
      <div className='max-w-7xl mx-auto px-4 mb-6'>
        <div className='flex gap-4 overflow-x-auto pb-4 hide-scrollbar'>
          {presetFilters.map((preset) => (
            <motion.button
              key={preset.label}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setSelectedPreset(preset.label)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-full border ${
                selectedPreset === preset.label
                  ? 'border-retreat-green bg-retreat-green/10'
                  : 'border-gray-200 hover:border-retreat-green'
              }`}
            >
              <preset.icon className='w-5 h-5 text-retreat-green' />
              <span>{preset.label}</span>
            </motion.button>
          ))}
        </div>
      </div>

      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex flex-wrap gap-4 justify-center'>
          {/* Localisation */}
          <div className='relative'>
            <button
              onClick={() => setActiveFilter(activeFilter === 'location' ? null : 'location')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-full border ${
                activeFilter === 'location'
                  ? 'border-retreat-green bg-retreat-green/10'
                  : 'border-gray-200 hover:border-retreat-green'
              } transition-all duration-200`}
            >
              <MapPinIcon className='w-5 h-5 text-retreat-green' />
              <span>Destination</span>
            </button>
            {activeFilter === 'location' && (
              <FilterDropdown options={locations} onClose={() => setActiveFilter(null)} />
            )}
          </div>

          {/* Durée */}
          <div className='relative'>
            <button
              onClick={() => setActiveFilter(activeFilter === 'duration' ? null : 'duration')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-full border ${
                activeFilter === 'duration'
                  ? 'border-retreat-green bg-retreat-green/10'
                  : 'border-gray-200 hover:border-retreat-green'
              } transition-all duration-200`}
            >
              <CalendarIcon className='w-5 h-5 text-retreat-green' />
              <span>Durée</span>
            </button>
            {activeFilter === 'duration' && (
              <FilterDropdown options={durations} onClose={() => setActiveFilter(null)} />
            )}
          </div>

          {/* Prix */}
          <div className='relative'>
            <button
              onClick={() => setActiveFilter(activeFilter === 'price' ? null : 'price')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-full border ${
                activeFilter === 'price'
                  ? 'border-retreat-green bg-retreat-green/10'
                  : 'border-gray-200 hover:border-retreat-green'
              } transition-all duration-200`}
            >
              <CurrencyEuroIcon className='w-5 h-5 text-retreat-green' />
              <span>Budget</span>
            </button>
            {activeFilter === 'price' && (
              <FilterDropdown options={prices} onClose={() => setActiveFilter(null)} />
            )}
          </div>

          {/* Niveau */}
          <div className='relative'>
            <button
              onClick={() => setActiveFilter(activeFilter === 'experience' ? null : 'experience')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-full border ${
                activeFilter === 'experience'
                  ? 'border-retreat-green bg-retreat-green/10'
                  : 'border-gray-200 hover:border-retreat-green'
              } transition-all duration-200`}
            >
              <UsersIcon className='w-5 h-5 text-retreat-green' />
              <span>Niveau</span>
            </button>
            {activeFilter === 'experience' && (
              <FilterDropdown options={experiences} onClose={() => setActiveFilter(null)} />
            )}
          </div>

          {/* Thème */}
          <div className='relative'>
            <button
              onClick={() => setActiveFilter(activeFilter === 'theme' ? null : 'theme')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-full border ${
                activeFilter === 'theme'
                  ? 'border-retreat-green bg-retreat-green/10'
                  : 'border-gray-200 hover:border-retreat-green'
              } transition-all duration-200`}
            >
              <TagIcon className='w-5 h-5 text-retreat-green' />
              <span>Thème</span>
            </button>
            {activeFilter === 'theme' && (
              <FilterDropdown options={themes} onClose={() => setActiveFilter(null)} />
            )}
          </div>

          {/* New People Selection */}
          <div className='relative'>
            <button
              onClick={() => setActiveFilter(activeFilter === 'people' ? null : 'people')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-full border ${
                activeFilter === 'people'
                  ? 'border-retreat-green bg-retreat-green/10'
                  : 'border-gray-200 hover:border-retreat-green'
              }`}
            >
              <UsersIcon className='w-5 h-5 text-retreat-green' />
              <span>{`${adults + children} Personne${adults + children > 1 ? 's' : ''}`}</span>
            </button>
            {activeFilter === 'people' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className='absolute z-50 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-100 p-4'
              >
                <div className='space-y-4'>
                  <div className='flex justify-between items-center'>
                    <span>Adultes</span>
                    <div className='flex items-center space-x-2'>
                      <button
                        onClick={() => setAdults(Math.max(1, adults - 1))}
                        className='p-1 rounded-full hover:bg-gray-100'
                      >
                        -
                      </button>
                      <span>{adults}</span>
                      <button
                        onClick={() => setAdults(adults + 1)}
                        className='p-1 rounded-full hover:bg-gray-100'
                      >
                        +
                      </button>
                    </div>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span>Enfants</span>
                    <div className='flex items-center space-x-2'>
                      <button
                        onClick={() => setChildren(Math.max(0, children - 1))}
                        className='p-1 rounded-full hover:bg-gray-100'
                      >
                        -
                      </button>
                      <span>{children}</span>
                      <button
                        onClick={() => setChildren(children + 1)}
                        className='p-1 rounded-full hover:bg-gray-100'
                      >
                        +
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

interface FilterDropdownProps {
  options: FilterOption[];
  onClose: () => void;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({ options, onClose }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      className='absolute z-50 top-full mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-100 py-2'
    >
      {options.map((option) => (
        <button
          key={option.value}
          className='w-full text-left px-4 py-2 hover:bg-gray-50 text-sm text-gray-700 hover:text-retreat-green transition-colors duration-200'
          onClick={() => {
            // Implémenter la logique de sélection ici
            onClose();
          }}
        >
          {option.label}
        </button>
      ))}
    </motion.div>
  );
};

export default SearchFilters;
