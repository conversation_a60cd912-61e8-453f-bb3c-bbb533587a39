import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { 
  Criterion, 
  CriterionWeight, 
  OptimizationDirection,
  multiCriteriaRecommendationService
} from '../../services/api/multiCriteriaRecommendationService';

interface CriteriaSelectorProps {
  selectedCriteria: CriterionWeight[];
  onChange: (criteria: CriterionWeight[]) => void;
  maxCriteria?: number;
  className?: string;
}

/**
 * Composant pour sélectionner et pondérer les critères de recommandation
 */
const CriteriaSelector: React.FC<CriteriaSelectorProps> = ({
  selectedCriteria,
  onChange,
  maxCriteria = 5,
  className = '',
}) => {
  const { t } = useTranslation();
  
  const [availableCriteria, setAvailableCriteria] = useState<Criterion[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Charger les critères disponibles
  useEffect(() => {
    const loadCriteria = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const criteria = await multiCriteriaRecommendationService.getCriteria();
        setAvailableCriteria(criteria);
      } catch (error) {
        console.error('Erreur lors du chargement des critères:', error);
        setError(t('multiCriteria.loadCriteriaError'));
      } finally {
        setLoading(false);
      }
    };
    
    loadCriteria();
  }, []);
  
  // Ajouter un critère
  const handleAddCriterion = (criterionId: string) => {
    if (selectedCriteria.length >= maxCriteria) {
      return;
    }
    
    // Vérifier si le critère est déjà sélectionné
    if (selectedCriteria.some(c => c.criterionId === criterionId)) {
      return;
    }
    
    const criterion = availableCriteria.find(c => c.id === criterionId);
    if (!criterion) {
      return;
    }
    
    // Déterminer la direction d'optimisation par défaut
    let defaultDirection = OptimizationDirection.MAXIMIZE;
    if (criterion.id === 'price' || criterion.id === 'distance') {
      defaultDirection = OptimizationDirection.MINIMIZE;
    }
    
    const newCriteria = [
      ...selectedCriteria,
      {
        criterionId,
        weight: 1.0 / (selectedCriteria.length + 1),
        direction: defaultDirection,
      },
    ];
    
    // Normaliser les poids pour qu'ils somment à 1
    const normalizedCriteria = normalizeCriteriaWeights(newCriteria);
    
    onChange(normalizedCriteria);
  };
  
  // Supprimer un critère
  const handleRemoveCriterion = (criterionId: string) => {
    const newCriteria = selectedCriteria.filter(c => c.criterionId !== criterionId);
    
    // Normaliser les poids pour qu'ils somment à 1
    const normalizedCriteria = normalizeCriteriaWeights(newCriteria);
    
    onChange(normalizedCriteria);
  };
  
  // Mettre à jour le poids d'un critère
  const handleWeightChange = (criterionId: string, weight: number) => {
    const newCriteria = selectedCriteria.map(c => {
      if (c.criterionId === criterionId) {
        return {
          ...c,
          weight,
        };
      }
      return c;
    });
    
    // Normaliser les poids pour qu'ils somment à 1
    const normalizedCriteria = normalizeCriteriaWeights(newCriteria);
    
    onChange(normalizedCriteria);
  };
  
  // Changer la direction d'optimisation d'un critère
  const handleDirectionChange = (criterionId: string, direction: OptimizationDirection) => {
    const newCriteria = selectedCriteria.map(c => {
      if (c.criterionId === criterionId) {
        return {
          ...c,
          direction,
        };
      }
      return c;
    });
    
    onChange(newCriteria);
  };
  
  // Normaliser les poids des critères pour qu'ils somment à 1
  const normalizeCriteriaWeights = (criteria: CriterionWeight[]): CriterionWeight[] => {
    if (criteria.length === 0) {
      return [];
    }
    
    const totalWeight = criteria.reduce((sum, c) => sum + c.weight, 0);
    
    return criteria.map(c => ({
      ...c,
      weight: c.weight / totalWeight,
    }));
  };
  
  // Obtenir le nom d'un critère
  const getCriterionName = (criterionId: string): string => {
    const criterion = availableCriteria.find(c => c.id === criterionId);
    return criterion ? criterion.name : criterionId;
  };
  
  // Obtenir la description d'un critère
  const getCriterionDescription = (criterionId: string): string => {
    const criterion = availableCriteria.find(c => c.id === criterionId);
    return criterion ? criterion.description : '';
  };
  
  // Obtenir l'unité d'un critère
  const getCriterionUnit = (criterionId: string): string => {
    const criterion = availableCriteria.find(c => c.id === criterionId);
    return criterion && criterion.unit ? criterion.unit : '';
  };
  
  return (
    <div className={`bg-white rounded-lg shadow p-4 ${className}`}>
      <h3 className="text-lg font-semibold mb-4">{t('multiCriteria.criteriaSelector')}</h3>
      
      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
          {error}
        </div>
      )}
      
      {loading ? (
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-retreat-green"></div>
        </div>
      ) : (
        <>
          {/* Liste des critères sélectionnés */}
          <div className="mb-6">
            <h4 className="text-md font-medium mb-2">{t('multiCriteria.selectedCriteria')}</h4>
            
            {selectedCriteria.length === 0 ? (
              <p className="text-gray-500 italic">{t('multiCriteria.noCriteriaSelected')}</p>
            ) : (
              <div className="space-y-4">
                {selectedCriteria.map((criterion) => (
                  <div key={criterion.criterionId} className="border rounded-md p-3">
                    <div className="flex justify-between items-center mb-2">
                      <div>
                        <span className="font-medium">{getCriterionName(criterion.criterionId)}</span>
                        {getCriterionUnit(criterion.criterionId) && (
                          <span className="text-gray-500 ml-1">({getCriterionUnit(criterion.criterionId)})</span>
                        )}
                      </div>
                      <button
                        onClick={() => handleRemoveCriterion(criterion.criterionId)}
                        className="text-red-500 hover:text-red-700"
                        aria-label={t('multiCriteria.removeCriterion')}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{getCriterionDescription(criterion.criterionId)}</p>
                    
                    <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                      {/* Contrôle du poids */}
                      <div className="flex-1">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('multiCriteria.weight')}: {Math.round(criterion.weight * 100)}%
                        </label>
                        <input
                          type="range"
                          min="1"
                          max="100"
                          value={Math.round(criterion.weight * 100)}
                          onChange={(e) => handleWeightChange(criterion.criterionId, parseInt(e.target.value) / 100)}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                      </div>
                      
                      {/* Contrôle de la direction */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('multiCriteria.direction')}
                        </label>
                        <div className="flex">
                          <button
                            onClick={() => handleDirectionChange(criterion.criterionId, OptimizationDirection.MAXIMIZE)}
                            className={`px-3 py-1 text-sm rounded-l-md ${
                              criterion.direction === OptimizationDirection.MAXIMIZE
                                ? 'bg-retreat-green text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                          >
                            {t('multiCriteria.maximize')}
                          </button>
                          <button
                            onClick={() => handleDirectionChange(criterion.criterionId, OptimizationDirection.MINIMIZE)}
                            className={`px-3 py-1 text-sm rounded-r-md ${
                              criterion.direction === OptimizationDirection.MINIMIZE
                                ? 'bg-retreat-green text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                          >
                            {t('multiCriteria.minimize')}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* Sélecteur de critères disponibles */}
          {selectedCriteria.length < maxCriteria && (
            <div>
              <h4 className="text-md font-medium mb-2">{t('multiCriteria.availableCriteria')}</h4>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {availableCriteria
                  .filter(c => !selectedCriteria.some(sc => sc.criterionId === c.id))
                  .map((criterion) => (
                    <button
                      key={criterion.id}
                      onClick={() => handleAddCriterion(criterion.id)}
                      className="text-left p-2 border rounded hover:bg-gray-50 transition-colors"
                    >
                      <div className="font-medium">{criterion.name}</div>
                      <div className="text-sm text-gray-600 truncate">{criterion.description}</div>
                    </button>
                  ))}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default CriteriaSelector;
