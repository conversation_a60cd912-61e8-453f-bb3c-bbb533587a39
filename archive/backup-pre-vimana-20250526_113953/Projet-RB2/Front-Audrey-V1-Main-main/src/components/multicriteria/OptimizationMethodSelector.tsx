import React from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { OptimizationMethod } from '../../services/api/multiCriteriaRecommendationService';

interface OptimizationMethodSelectorProps {
  selectedMethod: OptimizationMethod;
  onChange: (method: OptimizationMethod) => void;
  className?: string;
}

/**
 * Composant pour sélectionner la méthode d'optimisation multi-critères
 */
const OptimizationMethodSelector: React.FC<OptimizationMethodSelectorProps> = ({
  selectedMethod,
  onChange,
  className = '',
}) => {
  const { t } = useTranslation();
  
  // Informations sur les méthodes d'optimisation
  const methodInfo = {
    [OptimizationMethod.WEIGHTED_SUM]: {
      name: t('multiCriteria.methods.weightedSum'),
      description: t('multiCriteria.methodDescriptions.weightedSum'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
    },
    [OptimizationMethod.PARETO]: {
      name: t('multiCriteria.methods.pareto'),
      description: t('multiCriteria.methodDescriptions.pareto'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
    },
    [OptimizationMethod.LEXICOGRAPHIC]: {
      name: t('multiCriteria.methods.lexicographic'),
      description: t('multiCriteria.methodDescriptions.lexicographic'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
        </svg>
      ),
    },
    [OptimizationMethod.TOPSIS]: {
      name: t('multiCriteria.methods.topsis'),
      description: t('multiCriteria.methodDescriptions.topsis'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
        </svg>
      ),
    },
  };
  
  return (
    <div className={`bg-white rounded-lg shadow p-4 ${className}`}>
      <h3 className="text-lg font-semibold mb-4">{t('multiCriteria.optimizationMethod')}</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.values(OptimizationMethod).map((method) => (
          <div
            key={method}
            onClick={() => onChange(method)}
            className={`
              border rounded-lg p-4 cursor-pointer transition-colors
              ${selectedMethod === method
                ? 'border-retreat-green bg-retreat-green bg-opacity-10'
                : 'border-gray-200 hover:bg-gray-50'
              }
            `}
          >
            <div className="flex items-center mb-2">
              <div className={`mr-3 ${selectedMethod === method ? 'text-retreat-green' : 'text-gray-500'}`}>
                {methodInfo[method].icon}
              </div>
              <h4 className="text-md font-medium">{methodInfo[method].name}</h4>
            </div>
            
            <p className="text-sm text-gray-600">{methodInfo[method].description}</p>
          </div>
        ))}
      </div>
      
      {/* Explication détaillée de la méthode sélectionnée */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-md font-medium mb-2">{t('multiCriteria.aboutSelectedMethod')}</h4>
        
        <div className="text-sm">
          {selectedMethod === OptimizationMethod.WEIGHTED_SUM && (
            <div>
              <p className="mb-2">{t('multiCriteria.methodDetails.weightedSum.p1')}</p>
              <p className="mb-2">{t('multiCriteria.methodDetails.weightedSum.p2')}</p>
              <div className="bg-gray-100 p-2 rounded my-2 font-mono text-sm">
                score_global = Σ (poids_i * score_i)
              </div>
              <p>{t('multiCriteria.methodDetails.weightedSum.p3')}</p>
            </div>
          )}
          
          {selectedMethod === OptimizationMethod.PARETO && (
            <div>
              <p className="mb-2">{t('multiCriteria.methodDetails.pareto.p1')}</p>
              <p className="mb-2">{t('multiCriteria.methodDetails.pareto.p2')}</p>
              <p>{t('multiCriteria.methodDetails.pareto.p3')}</p>
            </div>
          )}
          
          {selectedMethod === OptimizationMethod.LEXICOGRAPHIC && (
            <div>
              <p className="mb-2">{t('multiCriteria.methodDetails.lexicographic.p1')}</p>
              <p className="mb-2">{t('multiCriteria.methodDetails.lexicographic.p2')}</p>
              <p>{t('multiCriteria.methodDetails.lexicographic.p3')}</p>
            </div>
          )}
          
          {selectedMethod === OptimizationMethod.TOPSIS && (
            <div>
              <p className="mb-2">{t('multiCriteria.methodDetails.topsis.p1')}</p>
              <p className="mb-2">{t('multiCriteria.methodDetails.topsis.p2')}</p>
              <p>{t('multiCriteria.methodDetails.topsis.p3')}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OptimizationMethodSelector;
