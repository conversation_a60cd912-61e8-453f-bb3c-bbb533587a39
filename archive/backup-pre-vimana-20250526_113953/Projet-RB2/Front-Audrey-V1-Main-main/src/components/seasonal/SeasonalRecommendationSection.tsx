import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { contextualRecommendationService } from '../../services/api/contextualRecommendationService';
import { useAuthContext } from '../../hooks/useAuthContext';
import { toast } from 'react-toastify';
import { t } from '../../services/i18n/i18nService';
import RetreatCard from '../retreats/RetreatCard';
import LoadingSpinner from '../common/LoadingSpinner';

/**
 * Interface pour les propriétés du composant
 */
interface SeasonalRecommendationSectionProps {
  /** Nombre de recommandations à afficher */
  count?: number;
  
  /** Titre de la section */
  title?: string;
  
  /** Description de la section */
  description?: string;
  
  /** Afficher les recommandations pour la saison suivante */
  showNextSeason?: boolean;
  
  /** Classe CSS supplémentaire */
  className?: string;
}

/**
 * Composant pour afficher les recommandations saisonnières
 */
const SeasonalRecommendationSection: React.FC<SeasonalRecommendationSectionProps> = ({
  count = 3,
  title,
  description,
  showNextSeason = false,
  className = '',
}) => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [userContext, setUserContext] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Charger le contexte utilisateur et les recommandations saisonnières
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Récupérer le contexte utilisateur
        const context = await contextualRecommendationService.getMyContext();
        setUserContext(context);
        
        // Récupérer les recommandations saisonnières
        let seasonalRecommendations;
        if (showNextSeason) {
          seasonalRecommendations = await contextualRecommendationService.getNextSeasonRecommendations(count);
        } else {
          seasonalRecommendations = await contextualRecommendationService.getCurrentSeasonRecommendations(count);
        }
        
        setRecommendations(seasonalRecommendations);
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations saisonnières:', error);
        setError(t('seasonal.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (user) {
      loadData();
    }
  }, [user, count, showNextSeason]);
  
  // Générer le titre et la description en fonction de la saison
  const getSeasonalTitle = () => {
    if (!userContext || !userContext.seasonData) {
      return title || t('seasonal.title');
    }
    
    const season = showNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason;
    const seasonName = contextualRecommendationService.getSeasonName(season);
    
    return title || (showNextSeason
      ? t('seasonal.nextSeasonTitle', { season: seasonName })
      : t('seasonal.currentSeasonTitle', { season: seasonName }));
  };
  
  const getSeasonalDescription = () => {
    if (!userContext || !userContext.seasonData) {
      return description || t('seasonal.description');
    }
    
    const season = showNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason;
    const seasonName = contextualRecommendationService.getSeasonName(season);
    
    return description || (showNextSeason
      ? t('seasonal.nextSeasonDescription', { season: seasonName })
      : t('seasonal.currentSeasonDescription', { season: seasonName }));
  };
  
  // Récupérer la classe CSS de fond en fonction de la saison
  const getSeasonalBackgroundClass = () => {
    if (!userContext || !userContext.seasonData) {
      return 'bg-white';
    }
    
    const season = showNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason;
    return contextualRecommendationService.getSeasonBackgroundClass(season);
  };
  
  // Gérer le clic sur "Voir plus"
  const handleSeeMore = () => {
    navigate(showNextSeason ? '/next-season' : '/current-season');
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className={`rounded-lg shadow-md p-6 ${className} bg-white`}>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{getSeasonalTitle()}</h2>
        <p className="text-gray-600 mb-6">{getSeasonalDescription()}</p>
        
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }
  
  // Afficher un message d'erreur
  if (error) {
    return (
      <div className={`rounded-lg shadow-md p-6 ${className} bg-white`}>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{getSeasonalTitle()}</h2>
        <p className="text-gray-600 mb-6">{getSeasonalDescription()}</p>
        
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Afficher les recommandations
  return (
    <div className={`rounded-lg shadow-md p-6 ${className} ${getSeasonalBackgroundClass()}`}>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{getSeasonalTitle()}</h2>
          <p className="text-gray-600">{getSeasonalDescription()}</p>
        </div>
        
        {userContext && userContext.seasonData && (
          <div className="flex items-center">
            <span className={`text-sm font-medium ${contextualRecommendationService.getSeasonColorClass(
              showNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason
            )}`}>
              {contextualRecommendationService.getSeasonName(
                showNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason
              )}
            </span>
          </div>
        )}
      </div>
      
      {recommendations.length === 0 ? (
        <div className="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {showNextSeason
              ? t('seasonal.noNextSeasonRecommendations')
              : t('seasonal.noCurrentSeasonRecommendations')}
          </h3>
          <p className="text-gray-500 mb-6">
            {showNextSeason
              ? t('seasonal.checkBackLater')
              : t('seasonal.tryDifferentFilters')}
          </p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            {recommendations.map((recommendation) => (
              <RetreatCard
                key={recommendation.id}
                retreat={recommendation}
                isSeasonal={true}
                seasonalBadge={contextualRecommendationService.getSeasonName(
                  showNextSeason ? userContext.seasonData.nextSeason : userContext.seasonData.currentSeason
                )}
              />
            ))}
          </div>
          
          <div className="flex justify-center">
            <button
              onClick={handleSeeMore}
              className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
            >
              {t('seasonal.seeMore')}
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default SeasonalRecommendationSection;
