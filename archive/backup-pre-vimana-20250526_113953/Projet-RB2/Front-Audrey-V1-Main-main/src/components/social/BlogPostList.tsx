import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { blogService } from '../../services/api/blogService';
import { socialAnalyticsService } from '../../services/api/socialAnalyticsService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface BlogPostListProps {
  tags?: string[];
  authorId?: string;
  status?: 'draft' | 'published' | 'archived';
  limit?: number;
  showCreateButton?: boolean;
  onCreateClick?: () => void;
}

const BlogPostList: React.FC<BlogPostListProps> = ({
  tags,
  authorId,
  status = 'published',
  limit = 6,
  showCreateButton = false,
  onCreateClick,
}) => {
  const [blogPosts, setBlogPosts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const filters: Record<string, any> = { limit };
        if (tags) filters.tags = tags;
        if (authorId) filters.authorId = authorId;
        if (status) filters.status = status;
        
        const data = await blogService.getBlogPosts(filters);
        setBlogPosts(data);
      } catch (error) {
        console.error('Error fetching blog posts:', error);
        setError('Une erreur est survenue lors du chargement des articles de blog.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogPosts();
  }, [tags, authorId, status, limit]);

  const handleBlogPostClick = (blogPostId: string) => {
    // Enregistrer l'événement de vue
    socialAnalyticsService.trackView(blogPostId, 'blog');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'PPP', { locale: fr });
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 p-4">
        <p>{error}</p>
        <button
          className="mt-2 text-retreat-green hover:text-retreat-green-dark"
          onClick={() => window.location.reload()}
        >
          Réessayer
        </button>
      </div>
    );
  }

  if (blogPosts.length === 0) {
    return (
      <div className="text-center p-6">
        <p className="text-gray-500">Aucun article de blog disponible.</p>
        {showCreateButton && onCreateClick && (
          <button
            onClick={onCreateClick}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Créer un article
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">
          {status === 'draft' ? 'Brouillons' : 
           status === 'published' ? 'Articles publiés' : 
           status === 'archived' ? 'Articles archivés' : 
           'Articles de blog'}
        </h2>
        {showCreateButton && onCreateClick && (
          <button
            onClick={onCreateClick}
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Créer un article
          </button>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {blogPosts.map((post) => (
          <Link
            key={post.id}
            to={`/blog/${post.id}`}
            className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
            onClick={() => handleBlogPostClick(post.id)}
          >
            <div className="h-48 bg-gray-200">
              {post.imageUrl ? (
                <img
                  src={post.imageUrl}
                  alt={post.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-retreat-green-light to-retreat-green">
                  <svg className="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-5M8 12h8M8 16h4" />
                  </svg>
                </div>
              )}
            </div>
            
            <div className="p-4">
              <h3 className="text-lg font-medium text-gray-900 truncate">{post.title}</h3>
              
              <div className="mt-2 flex items-center text-sm text-gray-500">
                <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>{post.authorName}</span>
              </div>
              
              <div className="mt-1 flex items-center text-sm text-gray-500">
                <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>{formatDate(post.publishDate || post.createdAt)}</span>
              </div>
              
              <p className="mt-3 text-sm text-gray-600 line-clamp-3">
                {truncateText(post.content.replace(/<[^>]*>?/gm, ''), 150)}
              </p>
              
              {post.tags && post.tags.length > 0 && (
                <div className="mt-3 flex flex-wrap gap-1">
                  {post.tags.slice(0, 3).map((tag: string) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-retreat-green-light text-retreat-green"
                    >
                      {tag}
                    </span>
                  ))}
                  {post.tags.length > 3 && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                      +{post.tags.length - 3}
                    </span>
                  )}
                </div>
              )}
            </div>
          </Link>
        ))}
      </div>
      
      {blogPosts.length > 0 && (
        <div className="text-center mt-6">
          <Link
            to={`/blog${status ? `?status=${status}` : ''}`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            Voir tous les articles
            <svg className="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      )}
    </div>
  );
};

export default BlogPostList;
