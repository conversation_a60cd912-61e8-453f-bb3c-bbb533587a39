import React, { useState, useEffect } from 'react';
import { socialAnalyticsService } from '../../services/api/socialAnalyticsService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';
import { Bar, Line, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Enregistrer les composants Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface SocialAnalyticsDashboardProps {
  period?: string;
  userId?: string;
  isAdmin?: boolean;
}

const SocialAnalyticsDashboard: React.FC<SocialAnalyticsDashboardProps> = ({
  period = 'month',
  userId,
  isAdmin = false,
}) => {
  const [analytics, setAnalytics] = useState<any>(null);
  const [popularContent, setPopularContent] = useState<any[]>([]);
  const [engagementTrends, setEngagementTrends] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<string>(period);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        let analyticsData;
        
        if (userId) {
          // Récupérer les statistiques de l'utilisateur
          analyticsData = await socialAnalyticsService.getUserAnalytics(userId);
        } else if (isAdmin) {
          // Récupérer les statistiques globales (admin uniquement)
          analyticsData = await socialAnalyticsService.getSocialAnalytics(selectedPeriod);
        } else {
          // Cas par défaut : aucune donnée
          analyticsData = null;
        }
        
        setAnalytics(analyticsData);
        
        // Récupérer le contenu populaire
        const popular = await socialAnalyticsService.getPopularContent(undefined, 5, selectedPeriod);
        setPopularContent(popular);
        
        // Récupérer les tendances d'engagement (admin uniquement)
        if (isAdmin) {
          const trends = await socialAnalyticsService.getEngagementTrends(selectedPeriod);
          setEngagementTrends(trends);
        }
      } catch (error) {
        console.error('Error fetching analytics:', error);
        setError('Une erreur est survenue lors du chargement des statistiques.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, [userId, isAdmin, selectedPeriod]);

  const handlePeriodChange = (newPeriod: string) => {
    setSelectedPeriod(newPeriod);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 p-4">
        <p>{error}</p>
        <button
          className="mt-2 text-retreat-green hover:text-retreat-green-dark"
          onClick={() => window.location.reload()}
        >
          Réessayer
        </button>
      </div>
    );
  }

  if (!analytics && !isAdmin && !userId) {
    return (
      <div className="text-center p-6">
        <p className="text-gray-500">Aucune donnée d'analyse disponible.</p>
      </div>
    );
  }

  // Préparer les données pour les graphiques
  const prepareContentTypeData = () => {
    if (!analytics || !analytics.contentStats) return null;
    
    const data = {
      labels: ['Livestreams', 'Articles de blog', 'Vidéos'],
      datasets: [
        {
          label: 'Nombre de vues',
          data: [
            analytics.contentStats.livestream?.views || 0,
            analytics.contentStats.blog?.views || 0,
            analytics.contentStats.video?.views || 0,
          ],
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
    
    return data;
  };

  const prepareEngagementData = () => {
    if (!engagementTrends || !engagementTrends.timeline) return null;
    
    const labels = engagementTrends.timeline.map((item: any) => item.date);
    
    const data = {
      labels,
      datasets: [
        {
          label: 'Vues',
          data: engagementTrends.timeline.map((item: any) => item.views),
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.4,
        },
        {
          label: 'Likes',
          data: engagementTrends.timeline.map((item: any) => item.likes),
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.2)',
          tension: 0.4,
        },
        {
          label: 'Commentaires',
          data: engagementTrends.timeline.map((item: any) => item.comments),
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          tension: 0.4,
        },
      ],
    };
    
    return data;
  };

  return (
    <div className="space-y-8">
      {/* Sélecteur de période */}
      <div className="flex justify-end">
        <div className="inline-flex rounded-md shadow-sm" role="group">
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium rounded-l-lg ${
              selectedPeriod === 'day'
                ? 'bg-retreat-green text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => handlePeriodChange('day')}
          >
            Jour
          </button>
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium ${
              selectedPeriod === 'week'
                ? 'bg-retreat-green text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => handlePeriodChange('week')}
          >
            Semaine
          </button>
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium ${
              selectedPeriod === 'month'
                ? 'bg-retreat-green text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => handlePeriodChange('month')}
          >
            Mois
          </button>
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium rounded-r-lg ${
              selectedPeriod === 'year'
                ? 'bg-retreat-green text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => handlePeriodChange('year')}
          >
            Année
          </button>
        </div>
      </div>

      {/* Statistiques générales */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-sm font-medium text-gray-500">Total des vues</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">{analytics.totalViews || 0}</p>
            {analytics.viewsChange !== undefined && (
              <p className={`mt-2 text-sm ${analytics.viewsChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {analytics.viewsChange >= 0 ? '+' : ''}{analytics.viewsChange}% par rapport à la période précédente
              </p>
            )}
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-sm font-medium text-gray-500">Engagement</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">{analytics.totalEngagements || 0}</p>
            {analytics.engagementChange !== undefined && (
              <p className={`mt-2 text-sm ${analytics.engagementChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {analytics.engagementChange >= 0 ? '+' : ''}{analytics.engagementChange}% par rapport à la période précédente
              </p>
            )}
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-sm font-medium text-gray-500">Contenu créé</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">{analytics.totalContent || 0}</p>
            {analytics.contentChange !== undefined && (
              <p className={`mt-2 text-sm ${analytics.contentChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {analytics.contentChange >= 0 ? '+' : ''}{analytics.contentChange}% par rapport à la période précédente
              </p>
            )}
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-sm font-medium text-gray-500">Taux d'engagement</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">{analytics.engagementRate || 0}%</p>
            {analytics.engagementRateChange !== undefined && (
              <p className={`mt-2 text-sm ${analytics.engagementRateChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {analytics.engagementRateChange >= 0 ? '+' : ''}{analytics.engagementRateChange}% par rapport à la période précédente
              </p>
            )}
          </div>
        </div>
      )}

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Répartition par type de contenu */}
        {analytics && prepareContentTypeData() && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Répartition par type de contenu</h3>
            <div className="h-64">
              <Pie data={prepareContentTypeData()} options={{ maintainAspectRatio: false }} />
            </div>
          </div>
        )}
        
        {/* Tendances d'engagement */}
        {engagementTrends && prepareEngagementData() && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tendances d'engagement</h3>
            <div className="h-64">
              <Line data={prepareEngagementData()} options={{ maintainAspectRatio: false }} />
            </div>
          </div>
        )}
      </div>

      {/* Contenu populaire */}
      {popularContent.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Contenu populaire</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Titre
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Vues
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Engagement
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {popularContent.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.type === 'livestream' ? 'Livestream' : 
                       item.type === 'blog' ? 'Article de blog' : 
                       item.type === 'video' ? 'Vidéo' : 
                       item.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.views}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.engagement}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default SocialAnalyticsDashboard;
