import React from 'react';
import { motion } from 'framer-motion';
import { MapPinIcon, UserGroupIcon, StarIcon } from '@heroicons/react/24/outline';

interface Space {
  id: string;
  name: string;
  location: string;
  capacity: number;
  rating: number;
  images: string[];
  description: string;
}

const spaces: Space[] = [
  {
    id: '1',
    name: 'Le Domaine des Alpes',
    location: 'Alpes, France',
    capacity: 20,
    rating: 4.9,
    images: ['/images/spaces/alpes-1.jpg', '/images/spaces/alpes-2.jpg'],
    description:
      'Un espace idyllique au cœur des Alpes, parfait pour les retraites de yoga et de méditation.',
  },
  {
    id: '2',
    name: 'La Villa Méditerranée',
    location: 'Provence, France',
    capacity: 15,
    rating: 4.8,
    images: ['/images/spaces/provence-1.jpg', '/images/spaces/provence-2.jpg'],
    description:
      'Une villa contemporaine avec vue sur la mer, idéale pour les retraites bien-être.',
  },
  {
    id: '3',
    name: 'Le Manoir Breton',
    location: 'Bretagne, France',
    capacity: 25,
    rating: 4.7,
    images: ['/images/spaces/bretagne-1.jpg', '/images/spaces/bretagne-2.jpg'],
    description:
      'Un manoir historique entouré de jardins, parfait pour les retraites en pleine nature.',
  },
];

const SpaceGallery: React.FC = () => {
  return (
    <div className='bg-white rounded-2xl shadow-lg p-8'>
      <h2 className='text-2xl font-bold text-gray-900 mb-6'>Découvrez nos espaces</h2>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
        {spaces.map((space, index) => (
          <motion.div
            key={space.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className='bg-gray-50 rounded-lg overflow-hidden'
          >
            <div className='relative h-48'>
              <img src={space.images[0]} alt={space.name} className='w-full h-full object-cover' />
              <div className='absolute top-4 right-4 bg-white px-2 py-1 rounded-full flex items-center gap-1'>
                <StarIcon className='w-4 h-4 text-yellow-400' />
                <span className='text-sm font-medium'>{space.rating}</span>
              </div>
            </div>
            <div className='p-6'>
              <h3 className='text-xl font-semibold mb-2'>{space.name}</h3>
              <div className='flex items-center gap-4 mb-4'>
                <div className='flex items-center gap-1 text-gray-600'>
                  <MapPinIcon className='w-5 h-5' />
                  <span>{space.location}</span>
                </div>
                <div className='flex items-center gap-1 text-gray-600'>
                  <UserGroupIcon className='w-5 h-5' />
                  <span>{space.capacity} personnes</span>
                </div>
              </div>
              <p className='text-gray-600 mb-4'>{space.description}</p>
              <button className='w-full px-4 py-2 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark transition-colors'>
                Voir les détails
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default SpaceGallery;
