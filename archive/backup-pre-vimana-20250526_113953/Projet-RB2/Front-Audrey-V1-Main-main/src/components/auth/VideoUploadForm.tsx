'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { UploadCloud, FileVideo, XCircle } from 'lucide-react';
// TODO: Setup ShadCN/UI and uncomment these imports
// import { Button } from '../ui/button'; 
// import { Progress } from '../ui/progress'; 
// import { Label } from '../ui/label';     
import { fileService } from '../../services/api/fileService'; // Corrected path
import { toast } from 'react-toastify';

// Assuming AxiosProgressEvent or a similar structure. 
// If not using Axios, this type might need adjustment.
interface CustomProgressEvent {
  loaded: number;
  total?: number;
}

interface VideoUploadFormProps {
  onUploadSuccess?: (response: any) => void;
  onUploadError?: (error: any) => void;
}

const VideoUploadForm: React.FC<VideoUploadFormProps> = ({ onUploadSuccess, onUploadError }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [simulatedProgressIntervalId, setSimulatedProgressIntervalId] = useState<NodeJS.Timeout | null>(null);

  const clearSimulatedProgressInterval = () => {
    if (simulatedProgressIntervalId) {
      clearInterval(simulatedProgressIntervalId);
      setSimulatedProgressIntervalId(null);
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      if (file.type.startsWith('video/')) {
        setSelectedFile(file);
        setError(null);
      } else {
        setError('Invalid file type. Please upload a video file.');
        toast.error('Invalid file type. Please upload a video file.');
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'video/*': ['.mp4', '.mov', '.avi', '.mkv'] },
    multiple: false,
  });

  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a file to upload.');
      toast.warn('Please select a file to upload.');
      return;
    }

    setIsUploading(true);
    setError(null);
    setUploadProgress(0);

    let currentProgress = 0;
    const intervalId = setInterval(() => {
      currentProgress += 10;
      if (currentProgress <= 90) { 
        setUploadProgress(currentProgress);
      } else {
        clearSimulatedProgressInterval();
      }
    }, 200);
    setSimulatedProgressIntervalId(intervalId);

    try {
      const response = await fileService.uploadVideoFile(
        selectedFile,
        (event: CustomProgressEvent) => { // Typed the event parameter
          clearSimulatedProgressInterval(); 
          if (event.total) {
            const percentCompleted = Math.round((event.loaded * 100) / event.total);
            setUploadProgress(percentCompleted);
          }
        }
      );
      setUploadProgress(100);
      toast.success('Video uploaded successfully!');
      if (onUploadSuccess) {
        onUploadSuccess(response);
      }
    } catch (err: any) {
      clearSimulatedProgressInterval();
      setError(err.message || 'An unknown error occurred during upload.');
      toast.error(err.message || 'Upload failed.');
      if (onUploadError) {
        onUploadError(err);
      }
      setUploadProgress(0); 
    } finally {
      setIsUploading(false);
      clearSimulatedProgressInterval(); 
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setUploadProgress(0);
    setError(null);
    clearSimulatedProgressInterval();
  };
  
  useEffect(() => {
    return () => {
      clearSimulatedProgressInterval();
    };
  }, [simulatedProgressIntervalId]);

  return (
    <div className="p-4 border rounded-lg shadow-sm bg-white">
      {/* <Label htmlFor="video-upload" className="text-lg font-semibold mb-2 block">Upload Video</Label> */}
      <label htmlFor="video-upload" className="text-lg font-semibold mb-2 block">Upload Video</label>
      
      <div
        {...getRootProps()}
        className={`p-6 border-2 border-dashed rounded-md cursor-pointer hover:border-blue-500 transition-colors
          ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
          ${error ? 'border-red-500' : ''}`}
      >
        <input {...getInputProps()} id="video-upload" />
        <div className="flex flex-col items-center justify-center text-center">
          <UploadCloud size={48} className={`mb-2 ${isDragActive ? 'text-blue-600' : 'text-gray-500'}`} />
          {isDragActive ? (
            <p className="text-blue-600">Drop the video file here ...</p>
          ) : (
            <p className="text-gray-600">Drag & drop a video file here, or click to select file</p>
          )}
          <p className="text-xs text-gray-500 mt-1">MP4, MOV, AVI, MKV (Max 500MB)</p>
        </div>
      </div>

      {error && <p className="text-red-500 text-sm mt-2">{error}</p>}

      {selectedFile && (
        <div className="mt-4 p-3 border rounded-md bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FileVideo size={24} className="text-blue-500 mr-2" />
              <span className="text-sm font-medium text-gray-700">{selectedFile.name}</span>
              <span className="text-xs text-gray-500 ml-2">({(selectedFile.size / (1024 * 1024)).toFixed(2)} MB)</span>
            </div>
            {/* <Button variant="ghost" size="sm" onClick={handleRemoveFile} disabled={isUploading}> */}
            <button onClick={handleRemoveFile} disabled={isUploading} className="p-1 text-red-500 hover:text-red-700">
              <XCircle size={18} />
            </button>
            {/* </Button> */}
          </div>
          {isUploading && (
            <div className="mt-2">
              {/* <Progress value={uploadProgress} className="w-full" /> */}
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${uploadProgress}%` }}></div>
              </div>
              <p className="text-xs text-gray-500 text-right mt-1">{uploadProgress}%</p>
            </div>
          )}
        </div>
      )}

      <div className="mt-6 flex justify-end">
        {/* <Button
          onClick={handleUpload}
          disabled={!selectedFile || isUploading}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isUploading ? 'Uploading...' : 'Upload Video'}
        </Button> */}
        <button 
          onClick={handleUpload} 
          disabled={!selectedFile || isUploading}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-md disabled:opacity-50"
        >
           {isUploading ? 'Uploading...' : 'Upload Video'}
        </button>
      </div>
    </div>
  );
};

export default VideoUploadForm; 