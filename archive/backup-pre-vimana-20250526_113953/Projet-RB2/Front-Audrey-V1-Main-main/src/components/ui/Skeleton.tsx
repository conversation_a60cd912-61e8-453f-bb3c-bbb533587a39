import React from 'react';

interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  borderRadius?: string | number;
  className?: string;
  animate?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  borderRadius = '0.25rem',
  className = '',
  animate = true,
}) => {
  // Convertir les valeurs numériques en pixels
  const widthStyle = typeof width === 'number' ? `${width}px` : width;
  const heightStyle = typeof height === 'number' ? `${height}px` : height;
  const borderRadiusStyle = typeof borderRadius === 'number' ? `${borderRadius}px` : borderRadius;

  return (
    <div
      className={`bg-gray-200 ${animate ? 'animate-pulse' : ''} ${className}`}
      style={{
        width: widthStyle,
        height: heightStyle,
        borderRadius: borderRadiusStyle,
      }}
      aria-hidden="true"
    />
  );
};

export const SkeletonText: React.FC<{
  lines?: number;
  className?: string;
  lineHeight?: string | number;
  lineSpacing?: string | number;
}> = ({
  lines = 3,
  className = '',
  lineHeight = '1rem',
  lineSpacing = '0.5rem',
}) => {
  // Convertir les valeurs numériques en pixels
  const lineHeightStyle = typeof lineHeight === 'number' ? `${lineHeight}px` : lineHeight;
  const lineSpacingStyle = typeof lineSpacing === 'number' ? `${lineSpacing}px` : lineSpacing;

  return (
    <div className={className}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          height={lineHeightStyle}
          width={index === lines - 1 && lines > 1 ? '80%' : '100%'}
          className={index < lines - 1 ? `mb-${lineSpacingStyle}` : ''}
        />
      ))}
    </div>
  );
};

export const SkeletonCard: React.FC<{
  className?: string;
  height?: string | number;
}> = ({
  className = '',
  height = '12rem',
}) => {
  // Convertir les valeurs numériques en pixels
  const heightStyle = typeof height === 'number' ? `${height}px` : height;

  return (
    <div
      className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}
      style={{ height: heightStyle }}
    >
      <Skeleton height="40%" borderRadius={0} />
      <div className="p-4">
        <Skeleton width="60%" className="mb-2" />
        <SkeletonText lines={2} lineHeight="0.75rem" lineSpacing="0.5rem" />
      </div>
    </div>
  );
};

export const SkeletonAvatar: React.FC<{
  size?: string | number;
  className?: string;
}> = ({
  size = '3rem',
  className = '',
}) => {
  // Convertir les valeurs numériques en pixels
  const sizeStyle = typeof size === 'number' ? `${size}px` : size;

  return (
    <Skeleton
      width={sizeStyle}
      height={sizeStyle}
      borderRadius="50%"
      className={className}
    />
  );
};

export const SkeletonButton: React.FC<{
  width?: string | number;
  height?: string | number;
  className?: string;
}> = ({
  width = '6rem',
  height = '2.5rem',
  className = '',
}) => {
  return (
    <Skeleton
      width={width}
      height={height}
      borderRadius="0.375rem"
      className={className}
    />
  );
};

export const SkeletonChart: React.FC<{
  width?: string | number;
  height?: string | number;
  className?: string;
}> = ({
  width = '100%',
  height = '15rem',
  className = '',
}) => {
  return (
    <div className={`${className}`}>
      <Skeleton width="50%" height="1.5rem" className="mb-4" />
      <Skeleton
        width={width}
        height={height}
        borderRadius="0.375rem"
        animate={false}
        className="opacity-50"
      />
    </div>
  );
};

export default Skeleton;
