import React, { useState, useEffect, useRef } from 'react';

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  formatValue?: (value: number) => string;
  className?: string;
}

export const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  value,
  duration = 1000,
  formatValue = (val) => val.toLocaleString(),
  className = '',
}) => {
  const [displayValue, setDisplayValue] = useState(0);
  const previousValueRef = useRef(0);
  const animationRef = useRef<number | null>(null);
  const startTimeRef = useRef<number | null>(null);

  useEffect(() => {
    // Si la valeur n'a pas changé, ne rien faire
    if (previousValueRef.current === value) return;

    // Annuler l'animation précédente si elle existe
    if (animationRef.current !== null) {
      cancelAnimationFrame(animationRef.current);
    }

    // Initialiser les références
    previousValueRef.current = displayValue;
    startTimeRef.current = null;

    // Fonction d'animation
    const animate = (timestamp: number) => {
      if (startTimeRef.current === null) {
        startTimeRef.current = timestamp;
      }

      const elapsed = timestamp - startTimeRef.current;
      const progress = Math.min(elapsed / duration, 1);
      
      // Fonction d'easing pour une animation plus naturelle
      const easeOutQuart = (x: number): number => 1 - Math.pow(1 - x, 4);
      const easedProgress = easeOutQuart(progress);
      
      // Calculer la valeur actuelle
      const currentValue = previousValueRef.current + (value - previousValueRef.current) * easedProgress;
      setDisplayValue(Math.round(currentValue));

      // Continuer l'animation si elle n'est pas terminée
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        animationRef.current = null;
      }
    };

    // Démarrer l'animation
    animationRef.current = requestAnimationFrame(animate);

    // Nettoyer l'animation lors du démontage du composant
    return () => {
      if (animationRef.current !== null) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [value, duration]);

  return <span className={className}>{formatValue(displayValue)}</span>;
};

export default AnimatedCounter;
