/**
 * Tests de Composants Cypress - Card - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Tests de composants avec Cypress pour validation
 * des interactions et du rendu en conditions réelles.
 */

import { Card, CardHeader, CardTitle, CardContent, CardFooter, RetreatCard, ProfessionalCard, StatsCard } from './Card';

describe('Card Component Tests', () => {
  describe('Basic Card Component', () => {
    it('should render basic card correctly', () => {
      cy.mount(
        <Card>
          <CardHeader>
            <CardTitle>Test Card</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This is test content</p>
          </CardContent>
        </Card>
      );

      cy.get('[data-testid="card"]').should('be.visible');
      cy.contains('Test Card').should('be.visible');
      cy.contains('This is test content').should('be.visible');
    });

    it('should apply variant classes correctly', () => {
      const variants = ['default', 'outlined', 'elevated', 'interactive', 'success', 'warning', 'error', 'info'];
      
      variants.forEach(variant => {
        cy.mount(
          <Card variant={variant as any}>
            <CardContent>Variant: {variant}</CardContent>
          </Card>
        );
        
        cy.get('[data-testid="card"]').should('be.visible');
        cy.contains(`Variant: ${variant}`).should('be.visible');
      });
    });

    it('should apply size classes correctly', () => {
      const sizes = ['sm', 'md', 'lg'];
      
      sizes.forEach(size => {
        cy.mount(
          <Card size={size as any}>
            <CardContent>Size: {size}</CardContent>
          </Card>
        );
        
        cy.get('[data-testid="card"]').should('be.visible');
        cy.contains(`Size: ${size}`).should('be.visible');
      });
    });

    it('should handle interactive variant with hover effects', () => {
      cy.mount(
        <Card variant="interactive">
          <CardContent>Interactive Card</CardContent>
        </Card>
      );

      cy.get('[data-testid="card"]').should('be.visible');
      cy.get('[data-testid="card"]').should('have.class', 'cursor-pointer');
      
      // Test hover effect
      cy.get('[data-testid="card"]').trigger('mouseover');
      cy.get('[data-testid="card"]').should('have.class', 'hover:shadow-medium');
    });
  });

  describe('RetreatCard Component', () => {
    const mockRetreat = {
      id: '1',
      title: 'Test Yoga Retreat',
      description: 'A wonderful yoga retreat in the mountains',
      image: 'https://example.com/retreat.jpg',
      price: 299,
      duration: '3 days',
      location: 'Provence, France',
      rating: 4.8,
      category: 'Yoga',
    };

    it('should render retreat card with all information', () => {
      cy.mount(<RetreatCard retreat={mockRetreat} />);

      cy.contains('Test Yoga Retreat').should('be.visible');
      cy.contains('A wonderful yoga retreat in the mountains').should('be.visible');
      cy.contains('299€').should('be.visible');
      cy.contains('3 days').should('be.visible');
      cy.contains('Provence, France').should('be.visible');
      cy.contains('4.8').should('be.visible');
      cy.contains('Yoga').should('be.visible');
      cy.get('img[alt="Test Yoga Retreat"]').should('be.visible');
    });

    it('should handle book button click', () => {
      const onBook = cy.stub();
      
      cy.mount(<RetreatCard retreat={mockRetreat} onBook={onBook} />);

      cy.contains('Réserver').click();
      cy.wrap(onBook).should('have.been.calledWith', '1');
    });

    it('should handle favorite button click', () => {
      const onFavorite = cy.stub();
      
      cy.mount(<RetreatCard retreat={mockRetreat} onFavorite={onFavorite} />);

      cy.get('[data-testid="favorite-button"]').click();
      cy.wrap(onFavorite).should('have.been.calledWith', '1');
    });

    it('should show favorited state correctly', () => {
      cy.mount(<RetreatCard retreat={mockRetreat} isFavorite={true} />);

      cy.get('[data-testid="favorite-button"]').should('contain.text', '❤️');
    });

    it('should show unfavorited state correctly', () => {
      cy.mount(<RetreatCard retreat={mockRetreat} isFavorite={false} />);

      cy.get('[data-testid="favorite-button"]').should('contain.text', '🤍');
    });

    it('should be accessible', () => {
      cy.mount(<RetreatCard retreat={mockRetreat} />);

      // Vérifier les attributs d'accessibilité
      cy.get('img').should('have.attr', 'alt', 'Test Yoga Retreat');
      cy.get('button').each(($btn) => {
        cy.wrap($btn).should('be.visible');
      });
      
      // Test de navigation au clavier
      cy.get('body').tab();
      cy.focused().should('be.visible');
    });
  });

  describe('ProfessionalCard Component', () => {
    const mockProfessional = {
      id: '1',
      name: 'Dr. Smith',
      title: 'Wellness Coach',
      avatar: 'https://example.com/avatar.jpg',
      specialties: ['Stress Management', 'Nutrition', 'Mindfulness'],
      rating: 4.9,
      reviewCount: 127,
      price: 75,
      available: true,
    };

    it('should render professional card with all information', () => {
      cy.mount(<ProfessionalCard professional={mockProfessional} />);

      cy.contains('Dr. Smith').should('be.visible');
      cy.contains('Wellness Coach').should('be.visible');
      cy.contains('4.9').should('be.visible');
      cy.contains('(127)').should('be.visible');
      cy.contains('75€/session').should('be.visible');
      cy.contains('Disponible').should('be.visible');
      cy.contains('Stress Management').should('be.visible');
      cy.contains('Nutrition').should('be.visible');
      cy.contains('Mindfulness').should('be.visible');
    });

    it('should handle contact button click', () => {
      const onContact = cy.stub();
      
      cy.mount(<ProfessionalCard professional={mockProfessional} onContact={onContact} />);

      cy.contains('Contacter').click();
      cy.wrap(onContact).should('have.been.calledWith', '1');
    });

    it('should handle view profile button click', () => {
      const onViewProfile = cy.stub();
      
      cy.mount(<ProfessionalCard professional={mockProfessional} onViewProfile={onViewProfile} />);

      cy.contains('Voir le profil').click();
      cy.wrap(onViewProfile).should('have.been.calledWith', '1');
    });

    it('should show unavailable state correctly', () => {
      const unavailableProfessional = { ...mockProfessional, available: false };
      
      cy.mount(<ProfessionalCard professional={unavailableProfessional} />);

      cy.contains('Occupé').should('be.visible');
      cy.contains('Contacter').should('be.disabled');
    });

    it('should display availability indicator correctly', () => {
      cy.mount(<ProfessionalCard professional={mockProfessional} />);

      cy.get('[data-testid="availability-indicator"]').should('have.class', 'bg-success-500');
    });
  });

  describe('StatsCard Component', () => {
    it('should render stats card with basic information', () => {
      cy.mount(
        <StatsCard
          title="Total Users"
          value="1,234"
          icon="👥"
        />
      );

      cy.contains('Total Users').should('be.visible');
      cy.contains('1,234').should('be.visible');
      cy.contains('👥').should('be.visible');
    });

    it('should render stats card with change indicator', () => {
      cy.mount(
        <StatsCard
          title="Revenue"
          value="€45,678"
          change={{ value: 12, type: 'increase' }}
          description="this month"
        />
      );

      cy.contains('Revenue').should('be.visible');
      cy.contains('€45,678').should('be.visible');
      cy.contains('↗ 12%').should('be.visible');
      cy.contains('this month').should('be.visible');
    });

    it('should show decrease indicator correctly', () => {
      cy.mount(
        <StatsCard
          title="Bounce Rate"
          value="23%"
          change={{ value: 5, type: 'decrease' }}
        />
      );

      cy.contains('↘ 5%').should('be.visible');
      cy.get('[data-testid="change-indicator"]').should('have.class', 'text-error-600');
    });

    it('should show increase indicator correctly', () => {
      cy.mount(
        <StatsCard
          title="Conversions"
          value="456"
          change={{ value: 8, type: 'increase' }}
        />
      );

      cy.contains('↗ 8%').should('be.visible');
      cy.get('[data-testid="change-indicator"]').should('have.class', 'text-success-600');
    });
  });

  describe('Card Responsive Behavior', () => {
    it('should adapt to mobile viewport', () => {
      cy.viewport('iphone-x');
      
      cy.mount(
        <Card>
          <CardHeader>
            <CardTitle>Mobile Card</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This card should adapt to mobile</p>
          </CardContent>
        </Card>
      );

      cy.get('[data-testid="card"]').should('be.visible');
      cy.contains('Mobile Card').should('be.visible');
    });

    it('should adapt to tablet viewport', () => {
      cy.viewport('ipad-2');
      
      cy.mount(
        <Card>
          <CardHeader>
            <CardTitle>Tablet Card</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This card should adapt to tablet</p>
          </CardContent>
        </Card>
      );

      cy.get('[data-testid="card"]').should('be.visible');
      cy.contains('Tablet Card').should('be.visible');
    });
  });

  describe('Card Interactions', () => {
    it('should handle click events on interactive cards', () => {
      const onClick = cy.stub();
      
      cy.mount(
        <Card variant="interactive" onClick={onClick}>
          <CardContent>Clickable Card</CardContent>
        </Card>
      );

      cy.get('[data-testid="card"]').click();
      cy.wrap(onClick).should('have.been.called');
    });

    it('should handle keyboard navigation', () => {
      cy.mount(
        <Card variant="interactive" tabIndex={0}>
          <CardContent>
            <button>Button 1</button>
            <button>Button 2</button>
          </CardContent>
        </Card>
      );

      // Test navigation avec Tab
      cy.get('body').tab();
      cy.focused().should('contain.text', 'Button 1');
      
      cy.focused().tab();
      cy.focused().should('contain.text', 'Button 2');
    });

    it('should handle focus states correctly', () => {
      cy.mount(
        <Card variant="interactive" tabIndex={0}>
          <CardContent>Focusable Card</CardContent>
        </Card>
      );

      cy.get('[data-testid="card"]').focus();
      cy.get('[data-testid="card"]').should('have.focus');
    });
  });

  describe('Card Error Handling', () => {
    it('should handle missing image gracefully', () => {
      const retreatWithBrokenImage = {
        id: '1',
        title: 'Test Retreat',
        description: 'Test description',
        image: 'https://broken-url.com/image.jpg',
        price: 299,
        duration: '3 days',
        location: 'Test Location',
        rating: 4.5,
        category: 'Test',
      };

      cy.mount(<RetreatCard retreat={retreatWithBrokenImage} />);

      // Vérifier que la carte s'affiche même avec une image cassée
      cy.contains('Test Retreat').should('be.visible');
      cy.get('img').should('exist');
    });

    it('should handle long text content gracefully', () => {
      const retreatWithLongText = {
        id: '1',
        title: 'This is a very long title that should be truncated properly to avoid layout issues',
        description: 'This is a very long description that should also be handled gracefully and truncated if necessary to maintain the card layout and visual consistency across different screen sizes and devices.',
        image: 'https://example.com/image.jpg',
        price: 299,
        duration: '3 days',
        location: 'Very Long Location Name That Should Be Handled',
        rating: 4.5,
        category: 'Test',
      };

      cy.mount(<RetreatCard retreat={retreatWithLongText} />);

      cy.get('[data-testid="card"]').should('be.visible');
      cy.contains('This is a very long title').should('be.visible');
    });

    it('should handle missing data gracefully', () => {
      const incompleteRetreat = {
        id: '1',
        title: 'Test Retreat',
        description: '',
        image: '',
        price: 0,
        duration: '',
        location: '',
        rating: 0,
        category: '',
      };

      cy.mount(<RetreatCard retreat={incompleteRetreat} />);

      // Vérifier que la carte s'affiche même avec des données manquantes
      cy.contains('Test Retreat').should('be.visible');
      cy.get('[data-testid="card"]').should('be.visible');
    });
  });
});
