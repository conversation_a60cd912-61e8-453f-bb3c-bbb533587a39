import React from 'react';
import Icon from '../Icon/Icon';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

interface MenuButtonProps {
  isOpen: boolean;
  onClick: () => void;
  className?: string;
  ariaLabel?: string;
}

const MenuButton: React.FC<MenuButtonProps> = ({
  isOpen,
  onClick,
  className = '',
  ariaLabel = isOpen ? 'Fermer le menu' : 'Ouvrir le menu',
}) => {
  const IconComponent = isOpen ? XMarkIcon : Bars3Icon;

  return (
    <button
      onClick={onClick}
      className={`p-2 rounded-md hover:bg-gray-100 transition-colors duration-200 ${className}`}
      aria-label={ariaLabel}
      aria-expanded={isOpen}
    >
      <Icon icon={IconComponent} size='md' color='text-gray-600' />
    </button>
  );
};

export default MenuButton;
