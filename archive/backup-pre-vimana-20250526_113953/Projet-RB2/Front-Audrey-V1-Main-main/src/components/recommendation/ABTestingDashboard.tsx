import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useAuthContext } from '../../hooks/useAuthContext';
import { abTestingService } from '../../services/api/abTestingService';
import { FadeIn } from '../ui/FadeIn';
import { MetricsChart } from '../analytics/MetricsChart';
import { ExportDataButton } from '../analytics/ExportDataButton';
import { Tabs, TabList, Tab, TabPanel } from '../ui/Tabs';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { Spinner } from '../ui/Spinner';
import { Alert } from '../ui/Alert';

interface ABTest {
  id: string;
  name: string;
  description: string;
  strategies: string[];
  weights: number[];
  startDate: string;
  endDate: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ABTestMetrics {
  testId: string;
  strategy: string;
  impressions: number;
  clicks: number;
  conversions: number;
  revenue: number;
  updatedAt: string;
}

interface ConversionRates {
  [strategy: string]: {
    ctr: number;
    cvr: number;
    revenue: number;
  };
}

interface ABTestingDashboardProps {
  onCreateTest?: () => void;
}

export const ABTestingDashboard: React.FC<ABTestingDashboardProps> = ({ onCreateTest }) => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const [tests, setTests] = useState<ABTest[]>([]);
  const [selectedTest, setSelectedTest] = useState<ABTest | null>(null);
  const [metrics, setMetrics] = useState<ABTestMetrics[]>([]);
  const [conversionRates, setConversionRates] = useState<ConversionRates>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showEndTestModal, setShowEndTestModal] = useState<boolean>(false);
  const [showWinnerModal, setShowWinnerModal] = useState<boolean>(false);
  const [winner, setWinner] = useState<string | null>(null);

  useEffect(() => {
    fetchTests();
  }, []);

  useEffect(() => {
    if (selectedTest) {
      fetchTestMetrics(selectedTest.id);
    }
  }, [selectedTest]);

  const fetchTests = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await abTestingService.getActiveTests();
      setTests(response);
      
      if (response.length > 0) {
        setSelectedTest(response[0]);
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching A/B tests:', error);
      setError(t('abTesting.errors.fetchFailed'));
      setLoading(false);
    }
  };

  const fetchTestMetrics = async (testId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await abTestingService.getTestMetrics(testId);
      setMetrics(response.metrics);
      setConversionRates(response.conversionRates);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching test metrics:', error);
      setError(t('abTesting.errors.metricsFetchFailed'));
      setLoading(false);
    }
  };

  const handleEndTest = async () => {
    if (!selectedTest) return;
    
    try {
      setLoading(true);
      setError(null);
      await abTestingService.endTest(selectedTest.id);
      setShowEndTestModal(false);
      await fetchTests();
      setLoading(false);
    } catch (error) {
      console.error('Error ending test:', error);
      setError(t('abTesting.errors.endTestFailed'));
      setLoading(false);
    }
  };

  const handleDetermineWinner = async () => {
    if (!selectedTest) return;
    
    try {
      setLoading(true);
      setError(null);
      const response = await abTestingService.determineWinner(selectedTest.id);
      setWinner(response.winner);
      setShowWinnerModal(true);
      setLoading(false);
    } catch (error) {
      console.error('Error determining winner:', error);
      setError(t('abTesting.errors.determineWinnerFailed'));
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const getStatusClass = (isActive: boolean) => {
    return isActive
      ? 'bg-green-100 text-green-800'
      : 'bg-gray-100 text-gray-800';
  };

  const getStatusText = (isActive: boolean) => {
    return isActive
      ? t('abTesting.status.active')
      : t('abTesting.status.inactive');
  };

  const getMetricsForStrategy = (strategy: string) => {
    return metrics.find(m => m.strategy === strategy) || {
      impressions: 0,
      clicks: 0,
      conversions: 0,
      revenue: 0,
    };
  };

  const getConversionRatesForStrategy = (strategy: string) => {
    return conversionRates[strategy] || {
      ctr: 0,
      cvr: 0,
      revenue: 0,
    };
  };

  const prepareChartData = () => {
    if (!selectedTest) return [];
    
    return selectedTest.strategies.map(strategy => {
      const metricsData = getMetricsForStrategy(strategy);
      const ratesData = getConversionRatesForStrategy(strategy);
      
      return {
        name: strategy,
        impressions: metricsData.impressions,
        clicks: metricsData.clicks,
        conversions: metricsData.conversions,
        revenue: metricsData.revenue,
        ctr: ratesData.ctr,
        cvr: ratesData.cvr,
      };
    });
  };

  const prepareExportData = () => {
    if (!selectedTest) return {};
    
    return {
      test: selectedTest,
      metrics: metrics,
      conversionRates: conversionRates,
      chartData: prepareChartData(),
    };
  };

  if (loading && tests.length === 0) {
    return (
      <div className="flex justify-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error && tests.length === 0) {
    return (
      <Alert variant="error" title={t('common.error')} message={error} />
    );
  }

  if (tests.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <h2 className="text-xl font-semibold mb-4">{t('abTesting.noTests')}</h2>
        {user?.roles.includes('admin') && (
          <Button onClick={onCreateTest} variant="primary">
            {t('abTesting.createTest')}
          </Button>
        )}
      </div>
    );
  }

  return (
    <FadeIn>
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <h2 className="text-xl font-semibold">{t('abTesting.title')}</h2>
        
        <div className="mt-4 md:mt-0 flex space-x-2">
          {user?.roles.includes('admin') && (
            <Button onClick={onCreateTest} variant="primary" size="sm">
              {t('abTesting.createTest')}
            </Button>
          )}
          
          {selectedTest && (
            <ExportDataButton
              data={prepareExportData()}
              filename={`ab_test_${selectedTest.id}_${new Date().toISOString().split('T')[0]}`}
              title="A/B Testing Data"
            />
          )}
        </div>
      </div>
      
      {error && <Alert variant="error" title={t('common.error')} message={error} className="mb-4" />}
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div className="p-4 border-b">
          <h3 className="text-lg font-medium">{t('abTesting.selectTest')}</h3>
          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {tests.map(test => (
              <div
                key={test.id}
                className={`p-4 border rounded-md cursor-pointer hover:bg-gray-50 ${
                  selectedTest?.id === test.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
                onClick={() => setSelectedTest(test)}
              >
                <div className="flex justify-between items-start">
                  <h4 className="font-medium">{test.name}</h4>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusClass(test.isActive)}`}>
                    {getStatusText(test.isActive)}
                  </span>
                </div>
                <p className="text-sm text-gray-500 mt-1">{test.description}</p>
                <div className="mt-2 text-xs text-gray-500">
                  {t('abTesting.startDate')}: {formatDate(test.startDate)}
                  {test.endDate && ` • ${t('abTesting.endDate')}: ${formatDate(test.endDate)}`}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {selectedTest && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="p-4 border-b flex justify-between items-center">
            <h3 className="text-lg font-medium">{selectedTest.name}</h3>
            <div className="flex space-x-2">
              <Button
                onClick={handleDetermineWinner}
                variant="secondary"
                size="sm"
                disabled={loading}
              >
                {t('abTesting.determineWinner')}
              </Button>
              
              {user?.roles.includes('admin') && selectedTest.isActive && (
                <Button
                  onClick={() => setShowEndTestModal(true)}
                  variant="danger"
                  size="sm"
                  disabled={loading}
                >
                  {t('abTesting.endTest')}
                </Button>
              )}
            </div>
          </div>
          
          <div className="p-4">
            <p className="mb-4">{selectedTest.description}</p>
            
            <div className="mb-6">
              <h4 className="font-medium mb-2">{t('abTesting.strategies')}</h4>
              <div className="flex flex-wrap gap-2">
                {selectedTest.strategies.map((strategy, index) => (
                  <div
                    key={strategy}
                    className="px-3 py-1 bg-gray-100 rounded-full text-sm flex items-center"
                  >
                    <span>{strategy}</span>
                    <span className="ml-2 text-xs text-gray-500">
                      {(selectedTest.weights[index] * 100).toFixed(0)}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
            
            {loading ? (
              <div className="flex justify-center py-8">
                <Spinner size="md" />
              </div>
            ) : (
              <Tabs>
                <TabList>
                  <Tab>{t('abTesting.tabs.overview')}</Tab>
                  <Tab>{t('abTesting.tabs.metrics')}</Tab>
                  <Tab>{t('abTesting.tabs.conversionRates')}</Tab>
                  <Tab>{t('abTesting.tabs.revenue')}</Tab>
                </TabList>
                
                <TabPanel>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-gray-500 mb-1">{t('abTesting.metrics.totalImpressions')}</h5>
                      <p className="text-2xl font-bold">
                        {metrics.reduce((sum, m) => sum + m.impressions, 0).toLocaleString()}
                      </p>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-gray-500 mb-1">{t('abTesting.metrics.totalClicks')}</h5>
                      <p className="text-2xl font-bold">
                        {metrics.reduce((sum, m) => sum + m.clicks, 0).toLocaleString()}
                      </p>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-gray-500 mb-1">{t('abTesting.metrics.totalConversions')}</h5>
                      <p className="text-2xl font-bold">
                        {metrics.reduce((sum, m) => sum + m.conversions, 0).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <h4 className="font-medium mb-2">{t('abTesting.metrics.byStrategy')}</h4>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('abTesting.strategy')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('abTesting.metrics.impressions')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('abTesting.metrics.clicks')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('abTesting.metrics.conversions')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('abTesting.metrics.ctr')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('abTesting.metrics.cvr')}
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t('abTesting.metrics.revenue')}
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {selectedTest.strategies.map(strategy => {
                            const metricsData = getMetricsForStrategy(strategy);
                            const ratesData = getConversionRatesForStrategy(strategy);
                            
                            return (
                              <tr key={strategy}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {strategy}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {metricsData.impressions.toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {metricsData.clicks.toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {metricsData.conversions.toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {ratesData.ctr.toFixed(2)}%
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {ratesData.cvr.toFixed(2)}%
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  ${metricsData.revenue.toLocaleString()}
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </TabPanel>
                
                <TabPanel>
                  <div className="mb-6">
                    <h4 className="font-medium mb-2">{t('abTesting.metrics.impressionsClicks')}</h4>
                    <div className="h-80">
                      <MetricsChart
                        data={prepareChartData()}
                        dataKeys={['impressions', 'clicks', 'conversions']}
                        xAxisDataKey="name"
                        height={300}
                        type="bar"
                      />
                    </div>
                  </div>
                </TabPanel>
                
                <TabPanel>
                  <div className="mb-6">
                    <h4 className="font-medium mb-2">{t('abTesting.metrics.conversionRates')}</h4>
                    <div className="h-80">
                      <MetricsChart
                        data={prepareChartData()}
                        dataKeys={['ctr', 'cvr']}
                        xAxisDataKey="name"
                        height={300}
                        type="bar"
                      />
                    </div>
                  </div>
                </TabPanel>
                
                <TabPanel>
                  <div className="mb-6">
                    <h4 className="font-medium mb-2">{t('abTesting.metrics.revenue')}</h4>
                    <div className="h-80">
                      <MetricsChart
                        data={prepareChartData()}
                        dataKeys={['revenue']}
                        xAxisDataKey="name"
                        height={300}
                        type="bar"
                      />
                    </div>
                  </div>
                </TabPanel>
              </Tabs>
            )}
          </div>
        </div>
      )}
      
      {/* Modal de confirmation pour terminer le test */}
      <Modal
        isOpen={showEndTestModal}
        onClose={() => setShowEndTestModal(false)}
        title={t('abTesting.endTestConfirmation.title')}
      >
        <div className="p-4">
          <p className="mb-4">{t('abTesting.endTestConfirmation.message')}</p>
          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => setShowEndTestModal(false)}
              variant="secondary"
              disabled={loading}
            >
              {t('common.cancel')}
            </Button>
            <Button
              onClick={handleEndTest}
              variant="danger"
              disabled={loading}
            >
              {loading ? <Spinner size="sm" /> : t('abTesting.endTest')}
            </Button>
          </div>
        </div>
      </Modal>
      
      {/* Modal pour afficher le gagnant */}
      <Modal
        isOpen={showWinnerModal}
        onClose={() => setShowWinnerModal(false)}
        title={t('abTesting.winner.title')}
      >
        <div className="p-4">
          {winner ? (
            <div className="text-center">
              <div className="mb-4 text-green-500">
                <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">{t('abTesting.winner.strategy')}</h3>
              <p className="text-2xl font-bold text-blue-600 mb-4">{winner}</p>
              <p className="mb-4">{t('abTesting.winner.message')}</p>
            </div>
          ) : (
            <p className="text-center">{t('abTesting.winner.noWinner')}</p>
          )}
          <div className="flex justify-center">
            <Button
              onClick={() => setShowWinnerModal(false)}
              variant="primary"
            >
              {t('common.close')}
            </Button>
          </div>
        </div>
      </Modal>
    </FadeIn>
  );
};

export default ABTestingDashboard;
