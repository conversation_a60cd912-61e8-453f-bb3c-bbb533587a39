import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useAuthContext } from '../../hooks/useAuthContext';
import { recommendationTestingService } from '../../services/api/recommendationTestingService';
import { FadeIn } from '../ui/FadeIn';
import { Ta<PERSON>, TabList, Tab, TabPanel } from '../ui/Tabs';
import { Button } from '../ui/Button';
import { Spinner } from '../ui/Spinner';
import { Alert } from '../ui/Alert';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Modal } from '../ui/Modal';
import { MetricsChart } from '../analytics/MetricsChart';
import { MetricsTable } from '../analytics/MetricsTable';
import { CreateTestForm } from './CreateTestForm';

interface TestConfig {
  id: string;
  name: string;
  description: string;
  strategies: string[];
  userSegments: string[];
  metrics: string[];
  sampleSize: number;
  testDuration: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
}

interface TestResult {
  testId: string;
  strategy: string;
  userSegment: string;
  impressions: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
  averageRelevanceScore: number;
  averageSatisfactionScore: number;
  diversityScore: number;
  noveltyScore: number;
  coverageScore: number;
  createdAt: string;
}

interface TestRun {
  id: string;
  testId: string;
  startedAt: string;
  completedAt?: string;
  status: 'RUNNING' | 'COMPLETED' | 'FAILED';
  error?: string;
}

export const RecommendationTestingDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const [tests, setTests] = useState<TestConfig[]>([]);
  const [selectedTest, setSelectedTest] = useState<TestConfig | null>(null);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testRuns, setTestRuns] = useState<TestRun[]>([]);
  const [runningTest, setRunningTest] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchTests();
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, []);

  useEffect(() => {
    if (selectedTest) {
      fetchTestResults(selectedTest.id);
      fetchTestRuns(selectedTest.id);
    }
  }, [selectedTest]);

  useEffect(() => {
    if (runningTest) {
      // Mettre en place un intervalle pour rafraîchir le statut du test en cours d'exécution
      const interval = setInterval(() => {
        fetchTestStatus(runningTest);
      }, 5000); // Rafraîchir toutes les 5 secondes
      
      setRefreshInterval(interval);
      
      return () => {
        clearInterval(interval);
      };
    } else if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }
  }, [runningTest]);

  const fetchTests = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const testsData = await recommendationTestingService.getAllTests();
      setTests(testsData);
      
      if (testsData.length > 0) {
        setSelectedTest(testsData[0]);
      }
      
      // Vérifier si un test est en cours d'exécution
      for (const test of testsData) {
        const status = await recommendationTestingService.getTestRunStatus(test.id);
        if (status.status === 'RUNNING') {
          setRunningTest(test.id);
          break;
        }
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching tests:', error);
      setError(t('testing.errors.fetchFailed'));
      setLoading(false);
    }
  };

  const fetchTestResults = async (testId: string) => {
    try {
      const results = await recommendationTestingService.getTestResults(testId);
      setTestResults(results);
    } catch (error) {
      console.error(`Error fetching test results for ${testId}:`, error);
    }
  };

  const fetchTestRuns = async (testId: string) => {
    try {
      const runs = await recommendationTestingService.getTestRuns(testId);
      setTestRuns(runs);
    } catch (error) {
      console.error(`Error fetching test runs for ${testId}:`, error);
    }
  };

  const fetchTestStatus = async (testId: string) => {
    try {
      const status = await recommendationTestingService.getTestRunStatus(testId);
      
      if (status.status !== 'RUNNING') {
        // Le test est terminé, rafraîchir les données
        setRunningTest(null);
        fetchTests();
        
        if (selectedTest && selectedTest.id === testId) {
          fetchTestResults(testId);
          fetchTestRuns(testId);
        }
      }
    } catch (error) {
      console.error(`Error fetching test status for ${testId}:`, error);
    }
  };

  const handleCreateTest = async (test: any) => {
    try {
      setLoading(true);
      setError(null);
      
      await recommendationTestingService.createTest(test);
      setShowCreateModal(false);
      await fetchTests();
      
      setLoading(false);
    } catch (error) {
      console.error('Error creating test:', error);
      setError(t('testing.errors.createFailed'));
      setLoading(false);
    }
  };

  const handleUpdateTest = async (testId: string, updates: any) => {
    try {
      setLoading(true);
      setError(null);
      
      await recommendationTestingService.updateTest(testId, updates);
      await fetchTests();
      
      setLoading(false);
    } catch (error) {
      console.error('Error updating test:', error);
      setError(t('testing.errors.updateFailed'));
      setLoading(false);
    }
  };

  const handleDeleteTest = async () => {
    if (!selectedTest) return;
    
    try {
      setLoading(true);
      setError(null);
      
      await recommendationTestingService.deleteTest(selectedTest.id);
      setShowDeleteModal(false);
      await fetchTests();
      
      setLoading(false);
    } catch (error) {
      console.error('Error deleting test:', error);
      setError(t('testing.errors.deleteFailed'));
      setLoading(false);
    }
  };

  const handleRunTest = async (testId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      await recommendationTestingService.runTest(testId);
      setRunningTest(testId);
      
      setLoading(false);
    } catch (error) {
      console.error('Error running test:', error);
      setError(t('testing.errors.runFailed'));
      setLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getStatusBadge = (test: TestConfig) => {
    if (runningTest === test.id) {
      return <Badge color="blue">{t('testing.status.running')}</Badge>;
    }
    
    if (!test.isActive) {
      return <Badge color="gray">{t('testing.status.inactive')}</Badge>;
    }
    
    if (test.completedAt) {
      return <Badge color="green">{t('testing.status.completed')}</Badge>;
    }
    
    if (test.startedAt) {
      return <Badge color="yellow">{t('testing.status.started')}</Badge>;
    }
    
    return <Badge color="purple">{t('testing.status.pending')}</Badge>;
  };

  const prepareChartData = () => {
    if (!testResults.length) return [];
    
    // Regrouper les résultats par stratégie
    const strategyResults: Record<string, any> = {};
    
    testResults.forEach(result => {
      if (!strategyResults[result.strategy]) {
        strategyResults[result.strategy] = {
          strategy: result.strategy,
          clickThroughRate: 0,
          conversionRate: 0,
          averageRelevanceScore: 0,
          averageSatisfactionScore: 0,
          diversityScore: 0,
          noveltyScore: 0,
          coverageScore: 0,
          count: 0,
        };
      }
      
      const sr = strategyResults[result.strategy];
      sr.clickThroughRate += result.clickThroughRate;
      sr.conversionRate += result.conversionRate;
      sr.averageRelevanceScore += result.averageRelevanceScore;
      sr.averageSatisfactionScore += result.averageSatisfactionScore;
      sr.diversityScore += result.diversityScore;
      sr.noveltyScore += result.noveltyScore;
      sr.coverageScore += result.coverageScore;
      sr.count += 1;
    });
    
    // Calculer les moyennes
    return Object.values(strategyResults).map(sr => ({
      strategy: sr.strategy,
      clickThroughRate: sr.clickThroughRate / sr.count,
      conversionRate: sr.conversionRate / sr.count,
      averageRelevanceScore: sr.averageRelevanceScore / sr.count,
      averageSatisfactionScore: sr.averageSatisfactionScore / sr.count,
      diversityScore: sr.diversityScore / sr.count,
      noveltyScore: sr.noveltyScore / sr.count,
      coverageScore: sr.coverageScore / sr.count,
    }));
  };

  if (loading && tests.length === 0) {
    return (
      <div className="flex justify-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error && tests.length === 0) {
    return (
      <Alert variant="error" title={t('common.error')} message={error} />
    );
  }

  return (
    <FadeIn>
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <h2 className="text-xl font-semibold">{t('testing.title')}</h2>
        
        <div className="mt-4 md:mt-0">
          <Button
            variant="primary"
            onClick={() => setShowCreateModal(true)}
          >
            {t('testing.createTest')}
          </Button>
        </div>
      </div>
      
      {error && (
        <Alert variant="error" title={t('common.error')} message={error} className="mb-4" />
      )}
      
      {runningTest && (
        <Alert
          variant="info"
          title={t('testing.runningTest.title')}
          message={t('testing.runningTest.message')}
          className="mb-4"
        />
      )}
      
      {tests.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <h3 className="text-lg font-medium mb-2">{t('testing.noTests.title')}</h3>
          <p className="text-gray-500 mb-4">{t('testing.noTests.description')}</p>
          <Button
            variant="primary"
            onClick={() => setShowCreateModal(true)}
          >
            {t('testing.createTest')}
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-4 border-b">
                <h3 className="text-lg font-medium">{t('testing.testsList')}</h3>
              </div>
              
              <div className="divide-y">
                {tests.map(test => (
                  <div
                    key={test.id}
                    className={`p-4 cursor-pointer hover:bg-gray-50 ${
                      selectedTest?.id === test.id ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => setSelectedTest(test)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{test.name}</h4>
                        <p className="text-sm text-gray-500 mt-1">{test.description}</p>
                      </div>
                      {getStatusBadge(test)}
                    </div>
                    
                    <div className="mt-2 flex flex-wrap gap-1">
                      {test.strategies.map(strategy => (
                        <Badge key={strategy} color="green" size="sm">{strategy}</Badge>
                      ))}
                    </div>
                    
                    <div className="mt-2 text-xs text-gray-500">
                      {t('testing.created')}: {formatDate(test.createdAt)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <div className="md:col-span-2">
            {selectedTest ? (
              <div className="space-y-6">
                <div className="bg-white rounded-lg shadow-md p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-semibold">{selectedTest.name}</h3>
                      <p className="text-gray-500 mt-1">{selectedTest.description}</p>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => handleRunTest(selectedTest.id)}
                        disabled={!!runningTest || !selectedTest.isActive}
                      >
                        {runningTest === selectedTest.id ? (
                          <Spinner size="sm" />
                        ) : (
                          t('testing.runTest')
                        )}
                      </Button>
                      
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => handleUpdateTest(selectedTest.id, { isActive: !selectedTest.isActive })}
                        disabled={!!runningTest}
                      >
                        {selectedTest.isActive ? t('testing.deactivate') : t('testing.activate')}
                      </Button>
                      
                      <Button
                        variant="danger"
                        size="sm"
                        onClick={() => setShowDeleteModal(true)}
                        disabled={!!runningTest}
                      >
                        {t('common.delete')}
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">{t('testing.details.strategies')}</h4>
                      <div className="flex flex-wrap gap-1">
                        {selectedTest.strategies.map(strategy => (
                          <Badge key={strategy} color="green">{strategy}</Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">{t('testing.details.userSegments')}</h4>
                      <div className="flex flex-wrap gap-1">
                        {selectedTest.userSegments.map(segment => (
                          <Badge key={segment} color="blue">{segment}</Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">{t('testing.details.metrics')}</h4>
                      <div className="flex flex-wrap gap-1">
                        {selectedTest.metrics.map(metric => (
                          <Badge key={metric} color="purple">{metric}</Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">{t('testing.details.sampleSize')}</h4>
                      <p>{selectedTest.sampleSize} {t('testing.details.users')}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">{t('testing.details.testDuration')}</h4>
                      <p>{selectedTest.testDuration} {t('testing.details.days')}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">{t('testing.details.status')}</h4>
                      <div className="flex items-center">
                        {getStatusBadge(selectedTest)}
                        {selectedTest.startedAt && (
                          <span className="ml-2 text-sm text-gray-500">
                            {formatDate(selectedTest.startedAt)}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                <Tabs>
                  <TabList>
                    <Tab>{t('testing.tabs.results')}</Tab>
                    <Tab>{t('testing.tabs.comparison')}</Tab>
                    <Tab>{t('testing.tabs.history')}</Tab>
                  </TabList>
                  
                  <TabPanel>
                    {testResults.length > 0 ? (
                      <div className="bg-white rounded-lg shadow-md overflow-hidden">
                        <div className="p-4 border-b">
                          <h3 className="text-lg font-medium">{t('testing.results.title')}</h3>
                        </div>
                        
                        <MetricsTable
                          data={testResults}
                          columns={[
                            {
                              id: 'strategy',
                              header: t('testing.results.columns.strategy'),
                              cell: (row) => row.strategy,
                            },
                            {
                              id: 'userSegment',
                              header: t('testing.results.columns.segment'),
                              cell: (row) => row.userSegment,
                            },
                            {
                              id: 'clickThroughRate',
                              header: t('testing.results.columns.ctr'),
                              cell: (row) => `${(row.clickThroughRate * 100).toFixed(2)}%`,
                            },
                            {
                              id: 'conversionRate',
                              header: t('testing.results.columns.cvr'),
                              cell: (row) => `${(row.conversionRate * 100).toFixed(2)}%`,
                            },
                            {
                              id: 'averageRelevanceScore',
                              header: t('testing.results.columns.relevance'),
                              cell: (row) => `${(row.averageRelevanceScore * 100).toFixed(2)}%`,
                            },
                            {
                              id: 'diversityScore',
                              header: t('testing.results.columns.diversity'),
                              cell: (row) => `${(row.diversityScore * 100).toFixed(2)}%`,
                            },
                            {
                              id: 'noveltyScore',
                              header: t('testing.results.columns.novelty'),
                              cell: (row) => `${(row.noveltyScore * 100).toFixed(2)}%`,
                            },
                          ]}
                          initialSortColumn="strategy"
                        />
                      </div>
                    ) : (
                      <div className="bg-white rounded-lg shadow-md p-6 text-center">
                        <p className="text-gray-500">{t('testing.results.noResults')}</p>
                      </div>
                    )}
                  </TabPanel>
                  
                  <TabPanel>
                    {testResults.length > 0 ? (
                      <div className="space-y-6">
                        <div className="bg-white rounded-lg shadow-md p-4">
                          <h3 className="text-lg font-medium mb-4">{t('testing.comparison.performance')}</h3>
                          <div className="h-80">
                            <MetricsChart
                              data={prepareChartData()}
                              dataKeys={['clickThroughRate', 'conversionRate']}
                              xAxisDataKey="strategy"
                              height={300}
                              type="bar"
                              yAxisFormatter={(value) => `${(value * 100).toFixed(0)}%`}
                            />
                          </div>
                        </div>
                        
                        <div className="bg-white rounded-lg shadow-md p-4">
                          <h3 className="text-lg font-medium mb-4">{t('testing.comparison.quality')}</h3>
                          <div className="h-80">
                            <MetricsChart
                              data={prepareChartData()}
                              dataKeys={['averageRelevanceScore', 'averageSatisfactionScore']}
                              xAxisDataKey="strategy"
                              height={300}
                              type="bar"
                              yAxisFormatter={(value) => `${(value * 100).toFixed(0)}%`}
                            />
                          </div>
                        </div>
                        
                        <div className="bg-white rounded-lg shadow-md p-4">
                          <h3 className="text-lg font-medium mb-4">{t('testing.comparison.diversity')}</h3>
                          <div className="h-80">
                            <MetricsChart
                              data={prepareChartData()}
                              dataKeys={['diversityScore', 'noveltyScore', 'coverageScore']}
                              xAxisDataKey="strategy"
                              height={300}
                              type="bar"
                              yAxisFormatter={(value) => `${(value * 100).toFixed(0)}%`}
                            />
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-white rounded-lg shadow-md p-6 text-center">
                        <p className="text-gray-500">{t('testing.comparison.noResults')}</p>
                      </div>
                    )}
                  </TabPanel>
                  
                  <TabPanel>
                    {testRuns.length > 0 ? (
                      <div className="bg-white rounded-lg shadow-md overflow-hidden">
                        <div className="p-4 border-b">
                          <h3 className="text-lg font-medium">{t('testing.history.title')}</h3>
                        </div>
                        
                        <MetricsTable
                          data={testRuns}
                          columns={[
                            {
                              id: 'id',
                              header: t('testing.history.columns.id'),
                              cell: (row) => row.id.substring(0, 8) + '...',
                            },
                            {
                              id: 'startedAt',
                              header: t('testing.history.columns.startedAt'),
                              cell: (row) => formatDate(row.startedAt),
                            },
                            {
                              id: 'completedAt',
                              header: t('testing.history.columns.completedAt'),
                              cell: (row) => formatDate(row.completedAt),
                            },
                            {
                              id: 'status',
                              header: t('testing.history.columns.status'),
                              cell: (row) => (
                                <Badge
                                  color={
                                    row.status === 'COMPLETED' ? 'green' :
                                    row.status === 'RUNNING' ? 'blue' :
                                    'red'
                                  }
                                >
                                  {row.status}
                                </Badge>
                              ),
                            },
                            {
                              id: 'error',
                              header: t('testing.history.columns.error'),
                              cell: (row) => row.error || '-',
                            },
                          ]}
                          initialSortColumn="startedAt"
                          initialSortDirection="desc"
                        />
                      </div>
                    ) : (
                      <div className="bg-white rounded-lg shadow-md p-6 text-center">
                        <p className="text-gray-500">{t('testing.history.noRuns')}</p>
                      </div>
                    )}
                  </TabPanel>
                </Tabs>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <p className="text-gray-500">{t('testing.selectTest')}</p>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Modal de création de test */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={t('testing.createTest')}
      >
        <CreateTestForm
          onSubmit={handleCreateTest}
          onCancel={() => setShowCreateModal(false)}
        />
      </Modal>
      
      {/* Modal de confirmation de suppression */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('testing.deleteTest.title')}
      >
        <div className="p-4">
          <p className="mb-4">{t('testing.deleteTest.confirmation')}</p>
          <div className="flex justify-end space-x-2">
            <Button
              variant="secondary"
              onClick={() => setShowDeleteModal(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteTest}
            >
              {t('common.delete')}
            </Button>
          </div>
        </div>
      </Modal>
    </FadeIn>
  );
};

export default RecommendationTestingDashboard;
