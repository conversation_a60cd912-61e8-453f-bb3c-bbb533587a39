import React, { useState } from 'react';
import { Card, Typography, Space, Divider, Button, Tag, Image, Collapse, Skeleton } from 'antd';
import { 
  InfoCircleOutlined, 
  EnvironmentOutlined, 
  CalendarOutlined, 
  TeamOutlined,
  ExpandOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import EnhancedExplanation from './EnhancedExplanation';
import FeedbackButtons from './FeedbackButtons';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

/**
 * Composant pour afficher une carte de recommandation avec explication et feedback
 */
const RecommendationCard = ({ 
  recommendation, 
  type, 
  loading = false, 
  onFeedbackSubmitted,
  language = 'fr',
  detailLevel = 'STANDARD'
}) => {
  const [showExplanation, setShowExplanation] = useState(false);

  if (loading) {
    return (
      <Card style={{ marginBottom: 16 }}>
        <Skeleton active avatar paragraph={{ rows: 4 }} />
      </Card>
    );
  }

  if (!recommendation) {
    return null;
  }

  /**
   * Formatte la date selon la locale
   */
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(language === 'fr' ? 'fr-FR' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  /**
   * Gère la soumission d'un feedback
   */
  const handleFeedbackSubmitted = (feedbackType, data) => {
    if (onFeedbackSubmitted) {
      onFeedbackSubmitted(recommendation.id, type, feedbackType, data);
    }
  };

  return (
    <Card 
      style={{ marginBottom: 16 }}
      cover={
        recommendation.imageUrl && (
          <div style={{ height: 200, overflow: 'hidden' }}>
            <Image
              alt={recommendation.title || recommendation.name}
              src={recommendation.imageUrl}
              style={{ width: '100%', objectFit: 'cover' }}
              fallback="data:image/png;base64,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"
            />
          </div>
        )
      }
      actions={[
        <Button 
          type="link" 
          icon={<QuestionCircleOutlined />} 
          onClick={() => setShowExplanation(!showExplanation)}
        >
          {showExplanation ? "Masquer l'explication" : "Pourquoi cette recommandation ?"}
        </Button>,
        <Button 
          type="link" 
          icon={<ExpandOutlined />} 
          onClick={() => window.open(`/recommendations/${type.toLowerCase()}/${recommendation.id}`, '_blank')}
        >
          Voir les détails
        </Button>
      ]}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Title level={4}>{recommendation.title || recommendation.name}</Title>
        
        <Space wrap>
          {recommendation.categories && recommendation.categories.map((category, index) => (
            <Tag key={index} color="blue">{category}</Tag>
          ))}
          
          {recommendation.tags && recommendation.tags.map((tag, index) => (
            <Tag key={index}>{tag}</Tag>
          ))}
        </Space>
        
        <Space direction="vertical" style={{ width: '100%' }}>
          {recommendation.location && (
            <Space>
              <EnvironmentOutlined />
              <Text>{recommendation.location}</Text>
            </Space>
          )}
          
          {recommendation.date && (
            <Space>
              <CalendarOutlined />
              <Text>{formatDate(recommendation.date)}</Text>
            </Space>
          )}
          
          {recommendation.participants && (
            <Space>
              <TeamOutlined />
              <Text>{recommendation.participants} participants</Text>
            </Space>
          )}
        </Space>
        
        <Paragraph ellipsis={{ rows: 3, expandable: true, symbol: 'Voir plus' }}>
          {recommendation.description}
        </Paragraph>
        
        <Divider />
        
        <FeedbackButtons 
          recommendationId={recommendation.id} 
          recommendationType={type}
          onFeedbackSubmitted={handleFeedbackSubmitted}
        />
        
        {showExplanation && (
          <>
            <Divider />
            <EnhancedExplanation 
              recommendationId={recommendation.id}
              recommendationType={type}
              language={language}
              detailLevel={detailLevel}
            />
          </>
        )}
      </Space>
    </Card>
  );
};

export default RecommendationCard;
