import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { matchingBookingService } from '../../services/api/matchingBookingService';
import { matchingAnalyticsService } from '../../services/api/matchingAnalyticsService';
import { MatchingResult } from '../../services/api/matchingService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface MatchingBookingFormProps {
  matchingResult: MatchingResult;
  onSuccess?: (bookingId: string) => void;
}

const MatchingBookingForm: React.FC<MatchingBookingFormProps> = ({
  matchingResult,
  onSuccess,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isCheckingBooking, setIsCheckingBooking] = useState<boolean>(true);
  const [existingBooking, setExistingBooking] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    startDate: '',
    endDate: '',
    notes: '',
    participants: 1,
    specialRequirements: '',
    services: [] as string[],
  });
  const navigate = useNavigate();

  // Vérifier si une réservation existe déjà
  useEffect(() => {
    const checkExistingBooking = async () => {
      try {
        setIsCheckingBooking(true);
        const result = await matchingBookingService.checkExistingBooking(
          matchingResult.partnerId,
          matchingResult.retreatId,
        );
        
        if (result.exists && result.booking) {
          setExistingBooking(result.booking);
        }
      } catch (error) {
        console.error('Erreur lors de la vérification de la réservation existante:', error);
      } finally {
        setIsCheckingBooking(false);
      }
    };

    checkExistingBooking();
  }, [matchingResult.partnerId, matchingResult.retreatId]);

  // Initialiser les dates par défaut
  useEffect(() => {
    if (matchingResult.retreat?.startDate && matchingResult.retreat?.endDate) {
      setFormData(prev => ({
        ...prev,
        startDate: new Date(matchingResult.retreat?.startDate || '').toISOString().split('T')[0],
        endDate: new Date(matchingResult.retreat?.endDate || '').toISOString().split('T')[0],
      }));
    }
  }, [matchingResult.retreat]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseInt(value) || 1,
    }));
  };

  const handleServiceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      services: checked
        ? [...prev.services, value]
        : prev.services.filter(service => service !== value),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Créer la réservation
      const result = await matchingBookingService.createBookingFromMatching({
        partnerId: matchingResult.partnerId,
        retreatId: matchingResult.retreatId,
        startDate: new Date(formData.startDate),
        endDate: new Date(formData.endDate),
        notes: formData.notes,
        participants: formData.participants,
        specialRequirements: formData.specialRequirements,
        services: formData.services,
      });
      
      // Enregistrer l'événement d'analyse
      await matchingAnalyticsService.recordMatchingConversion(
        matchingResult,
        'booking',
        { 
          bookingId: result.booking.id,
          amount: result.booking.totalAmount,
          currency: result.booking.currency || 'EUR',
        },
      );
      
      toast.success('Réservation créée avec succès');
      
      // Appeler le callback de succès si fourni
      if (onSuccess && result.booking.id) {
        onSuccess(result.booking.id);
      } else {
        // Rediriger vers la page de la réservation
        navigate(`/bookings/${result.booking.id}`);
      }
    } catch (error: any) {
      console.error('Erreur lors de la création de la réservation:', error);
      setError(error.response?.data?.message || 'Une erreur est survenue lors de la création de la réservation');
      toast.error('Erreur lors de la création de la réservation');
    } finally {
      setIsLoading(false);
    }
  };

  if (isCheckingBooking) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-center items-center h-32">
          <LoadingSpinner />
          <span className="ml-2 text-gray-600">Vérification des réservations existantes...</span>
        </div>
      </div>
    );
  }

  if (existingBooking) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="mt-2 text-lg font-medium text-gray-900">Réservation déjà existante</h3>
          <div className="mt-3 text-sm text-gray-500">
            <p>Une réservation existe déjà pour ce partenaire et cette retraite.</p>
            <p className="mt-1">
              <span className="font-medium">Statut :</span> {existingBooking.status}
            </p>
            <p className="mt-1">
              <span className="font-medium">Dates :</span> Du {new Date(existingBooking.startDate).toLocaleDateString('fr-FR')} au {new Date(existingBooking.endDate).toLocaleDateString('fr-FR')}
            </p>
            {existingBooking.totalAmount && (
              <p className="mt-1">
                <span className="font-medium">Montant :</span> {existingBooking.totalAmount} {existingBooking.currency || 'EUR'}
              </p>
            )}
          </div>
          <div className="mt-4">
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
              onClick={() => navigate(`/bookings/${existingBooking.id}`)}
            >
              Voir la réservation
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Réserver ce partenaire
      </h3>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
              Date de début
            </label>
            <input
              type="date"
              id="startDate"
              name="startDate"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
              value={formData.startDate}
              onChange={handleInputChange}
              required
            />
          </div>
          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
              Date de fin
            </label>
            <input
              type="date"
              id="endDate"
              name="endDate"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
              value={formData.endDate}
              onChange={handleInputChange}
              required
            />
          </div>
        </div>
        
        <div className="mb-4">
          <label htmlFor="participants" className="block text-sm font-medium text-gray-700 mb-1">
            Nombre de participants
          </label>
          <input
            type="number"
            id="participants"
            name="participants"
            min="1"
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
            value={formData.participants}
            onChange={handleNumberChange}
            required
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Services requis
          </label>
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="service-yoga"
                name="services"
                value="yoga"
                className="h-4 w-4 text-retreat-green focus:ring-retreat-green border-gray-300 rounded"
                checked={formData.services.includes('yoga')}
                onChange={handleServiceChange}
              />
              <label htmlFor="service-yoga" className="ml-2 block text-sm text-gray-700">
                Yoga
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="service-meditation"
                name="services"
                value="meditation"
                className="h-4 w-4 text-retreat-green focus:ring-retreat-green border-gray-300 rounded"
                checked={formData.services.includes('meditation')}
                onChange={handleServiceChange}
              />
              <label htmlFor="service-meditation" className="ml-2 block text-sm text-gray-700">
                Méditation
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="service-massage"
                name="services"
                value="massage"
                className="h-4 w-4 text-retreat-green focus:ring-retreat-green border-gray-300 rounded"
                checked={formData.services.includes('massage')}
                onChange={handleServiceChange}
              />
              <label htmlFor="service-massage" className="ml-2 block text-sm text-gray-700">
                Massage
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="service-nutrition"
                name="services"
                value="nutrition"
                className="h-4 w-4 text-retreat-green focus:ring-retreat-green border-gray-300 rounded"
                checked={formData.services.includes('nutrition')}
                onChange={handleServiceChange}
              />
              <label htmlFor="service-nutrition" className="ml-2 block text-sm text-gray-700">
                Nutrition
              </label>
            </div>
          </div>
        </div>
        
        <div className="mb-4">
          <label htmlFor="specialRequirements" className="block text-sm font-medium text-gray-700 mb-1">
            Exigences particulières
          </label>
          <textarea
            id="specialRequirements"
            name="specialRequirements"
            rows={3}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
            value={formData.specialRequirements}
            onChange={handleInputChange}
          />
        </div>
        
        <div className="mb-4">
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
            Notes
          </label>
          <textarea
            id="notes"
            name="notes"
            rows={3}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
            value={formData.notes}
            onChange={handleInputChange}
          />
        </div>
        
        {error && (
          <div className="mb-4 text-sm text-red-600">
            {error}
          </div>
        )}
        
        <div className="flex justify-end">
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <LoadingSpinner size="small" color="white" />
                <span className="ml-2">Création en cours...</span>
              </>
            ) : (
              'Créer la réservation'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default MatchingBookingForm;
