import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { matchingVideoService } from '../../services/api/matchingVideoService';
import { matchingAnalyticsService } from '../../services/api/matchingAnalyticsService';
import { MatchingResult } from '../../services/api/matchingService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface MatchingVideoRoomFormProps {
  matchingResult: MatchingResult;
  onSuccess?: (videoRoom: any) => void;
  onCancel?: () => void;
}

const MatchingVideoRoomForm: React.FC<MatchingVideoRoomFormProps> = ({
  matchingResult,
  onSuccess,
  onCancel,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    title: `Discussion: ${matchingResult.partner?.companyName || 'Partenaire'} - ${matchingResult.retreat?.title || 'Retraite'}`,
    description: `Vidéoconférence pour discuter de la collaboration entre ${matchingResult.partner?.companyName || 'le partenaire'} et la retraite "${matchingResult.retreat?.title || 'Retraite'}"`,
    scheduledFor: new Date(Date.now() + 30 * 60 * 1000).toISOString().slice(0, 16), // 30 minutes à partir de maintenant
    duration: 60,
    isPrivate: true,
    password: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked,
    }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseInt(value) || 30,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Créer la salle de vidéoconférence
      const result = await matchingVideoService.createVideoRoom({
        partnerId: matchingResult.partnerId,
        retreatId: matchingResult.retreatId,
        title: formData.title,
        description: formData.description,
        scheduledFor: new Date(formData.scheduledFor),
        duration: formData.duration,
        isPrivate: formData.isPrivate,
        password: formData.password || undefined,
      });
      
      // Enregistrer l'événement d'analyse
      await matchingAnalyticsService.recordMatchingInteraction(
        matchingResult,
        'VIDEO_ROOM_CREATED',
        { 
          roomId: result.videoRoom.id,
          scheduledFor: formData.scheduledFor,
        },
      );
      
      toast.success('Vidéoconférence créée avec succès');
      
      // Appeler le callback de succès si fourni
      if (onSuccess) {
        onSuccess(result.videoRoom);
      }
    } catch (error: any) {
      console.error('Erreur lors de la création de la vidéoconférence:', error);
      setError(error.response?.data?.message || 'Une erreur est survenue lors de la création de la vidéoconférence');
      toast.error('Erreur lors de la création de la vidéoconférence');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Programmer une vidéoconférence
      </h3>
      
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
            Titre
          </label>
          <input
            type="text"
            id="title"
            name="title"
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
            value={formData.title}
            onChange={handleInputChange}
            required
          />
        </div>
        
        <div className="mb-4">
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            rows={3}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
            value={formData.description}
            onChange={handleInputChange}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="scheduledFor" className="block text-sm font-medium text-gray-700 mb-1">
              Date et heure
            </label>
            <input
              type="datetime-local"
              id="scheduledFor"
              name="scheduledFor"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
              value={formData.scheduledFor}
              onChange={handleInputChange}
              required
            />
          </div>
          <div>
            <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
              Durée (minutes)
            </label>
            <input
              type="number"
              id="duration"
              name="duration"
              min="15"
              max="240"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
              value={formData.duration}
              onChange={handleNumberChange}
              required
            />
          </div>
        </div>
        
        <div className="mb-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isPrivate"
              name="isPrivate"
              className="h-4 w-4 text-retreat-green focus:ring-retreat-green border-gray-300 rounded"
              checked={formData.isPrivate}
              onChange={handleCheckboxChange}
            />
            <label htmlFor="isPrivate" className="ml-2 block text-sm text-gray-700">
              Salle privée (accessible uniquement aux participants invités)
            </label>
          </div>
        </div>
        
        {formData.isPrivate && (
          <div className="mb-4">
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Mot de passe (optionnel)
            </label>
            <input
              type="password"
              id="password"
              name="password"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Laisser vide pour générer automatiquement"
            />
          </div>
        )}
        
        {error && (
          <div className="mb-4 text-sm text-red-600">
            {error}
          </div>
        )}
        
        <div className="flex justify-end space-x-3">
          {onCancel && (
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
              onClick={onCancel}
            >
              Annuler
            </button>
          )}
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <LoadingSpinner size="small" color="white" />
                <span className="ml-2">Création en cours...</span>
              </>
            ) : (
              'Créer la vidéoconférence'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default MatchingVideoRoomForm;
