import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Icon from '../../atoms/Icon/Icon';
import Badge from '../../atoms/Badge/Badge';
import { StarIcon, MapPinIcon, CalendarIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface RetreatCardProps {
  id: string;
  title: string;
  image: string;
  location: string;
  price: number;
  duration: string;
  maxParticipants: number;
  rating: number;
  tags: string[];
  host: {
    name: string;
    avatar: string;
  };
  className?: string;
}

const RetreatCard: React.FC<RetreatCardProps> = ({
  id,
  title,
  image,
  location,
  price,
  duration,
  maxParticipants,
  rating,
  tags,
  host,
  className = '',
}) => {
  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className={`bg-white rounded-lg shadow-sm overflow-hidden ${className}`}
    >
      <Link to={`/retreats/${id}`} className='block'>
        {/* Image */}
        <div className='relative aspect-[4/3]'>
          <img src={image} alt={title} className='w-full h-full object-cover' />
          <div className='absolute top-2 right-2 flex space-x-1'>
            {tags.map((tag) => (
              <Badge key={tag} variant='default' size='sm' className='bg-white/90 backdrop-blur-sm'>
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className='p-4'>
          {/* Host */}
          <div className='flex items-center mb-3'>
            <img src={host.avatar} alt={host.name} className='w-6 h-6 rounded-full mr-2' />
            <span className='text-sm text-gray-600'>{host.name}</span>
          </div>

          {/* Title */}
          <h3 className='text-lg font-semibold text-gray-900 mb-2 line-clamp-2'>{title}</h3>

          {/* Location & Rating */}
          <div className='flex items-center justify-between mb-3'>
            <div className='flex items-center text-sm text-gray-600'>
              <Icon icon={MapPinIcon} size='sm' className='mr-1' />
              {location}
            </div>
            <div className='flex items-center'>
              <Icon
                icon={rating >= 4.5 ? StarIconSolid : StarIcon}
                size='sm'
                className='text-yellow-400 mr-1'
              />
              <span className='text-sm font-medium'>{rating}</span>
            </div>
          </div>

          {/* Details */}
          <div className='flex items-center space-x-4 text-sm text-gray-600 mb-4'>
            <div className='flex items-center'>
              <Icon icon={CalendarIcon} size='sm' className='mr-1' />
              {duration}
            </div>
            <div className='flex items-center'>
              <Icon icon={UserGroupIcon} size='sm' className='mr-1' />
              Max {maxParticipants}
            </div>
          </div>

          {/* Price */}
          <div className='flex items-center justify-between'>
            <div>
              <span className='text-lg font-semibold text-retreat-green'>
                {price.toLocaleString('fr-FR')} €
              </span>
              <span className='text-sm text-gray-600'> /personne</span>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default RetreatCard;
