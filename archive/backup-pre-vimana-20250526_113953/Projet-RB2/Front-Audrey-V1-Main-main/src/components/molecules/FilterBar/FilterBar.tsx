import React, { useState, useEffect } from 'react';
import { FaSearch, FaFilter, FaTimes } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { retreatService } from '../../../services/api/retreatService';

// Wrapper components for React Icons to fix TypeScript JSX issues
// @ts-ignore - React icons v5 types incompatibility with current @types/react
const SearchIcon = () => <FaSearch className='text-gray-400' />;
// @ts-ignore - React icons v5 types incompatibility with current @types/react
const FilterIcon = () => <FaFilter className='mr-2' />;
// @ts-ignore - React icons v5 types incompatibility with current @types/react
const TimesIcon = () => <FaTimes className='mr-2' />;

interface FilterBarProps {
  onFilterChange: (filters: any) => void;
  className?: string;
}

const FilterBar: React.FC<FilterBarProps> = ({ onFilterChange, className = '' }) => {
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    minPrice: '',
    maxPrice: '',
    location: '',
    startDate: '',
    endDate: '',
  });

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await retreatService.getCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Apply filters with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      onFilterChange(filters);
    }, 500);

    return () => clearTimeout(timer);
  }, [filters, onFilterChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onFilterChange(filters);
  };

  const handleReset = () => {
    setFilters({
      search: '',
      category: '',
      minPrice: '',
      maxPrice: '',
      location: '',
      startDate: '',
      endDate: '',
    });
    setShowAdvanced(false);
    onFilterChange({
      search: '',
      category: '',
      minPrice: '',
      maxPrice: '',
      location: '',
      startDate: '',
      endDate: '',
    });
  };

  const toggleAdvanced = () => {
    setShowAdvanced((prev) => !prev);
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      <form onSubmit={handleSubmit}>
        <div className='flex flex-col md:flex-row gap-3'>
          {/* Search input */}
          <div className='flex-grow relative'>
            <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
              <SearchIcon />
            </div>
            <input
              type='text'
              name='search'
              value={filters.search}
              onChange={handleInputChange}
              placeholder='Search retreats...'
              className='block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-color-retreat-green-dark focus:border-color-retreat-green-dark'
            />
          </div>

          {/* Category select */}
          <div className='md:w-1/4'>
            <select
              name='category'
              value={filters.category}
              onChange={handleInputChange}
              className='block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-color-retreat-green-dark focus:border-color-retreat-green-dark'
            >
              <option value=''>All Categories</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* Toggle advanced filters */}
          <button
            type='button'
            onClick={toggleAdvanced}
            className='inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-color-retreat-green-dark'
          >
            {showAdvanced ? <TimesIcon /> : <FilterIcon />}
            {showAdvanced ? 'Hide Filters' : 'More Filters'}
          </button>

          {/* Reset button */}
          {(filters.search ||
            filters.category ||
            filters.minPrice ||
            filters.maxPrice ||
            filters.location ||
            filters.startDate ||
            filters.endDate) && (
            <button
              type='button'
              onClick={handleReset}
              className='inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'
            >
              <TimesIcon />
              Reset
            </button>
          )}
        </div>

        {/* Advanced filters */}
        <AnimatePresence>
          {showAdvanced && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className='overflow-hidden'
            >
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4'>
                {/* Price range */}
                <div>
                  <label
                    htmlFor='minPrice'
                    className='block text-sm font-medium text-gray-700 mb-1'
                  >
                    Min Price
                  </label>
                  <input
                    type='number'
                    name='minPrice'
                    id='minPrice'
                    value={filters.minPrice}
                    onChange={handleInputChange}
                    placeholder='Min $'
                    min='0'
                    className='block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-color-retreat-green-dark focus:border-color-retreat-green-dark'
                  />
                </div>

                <div>
                  <label
                    htmlFor='maxPrice'
                    className='block text-sm font-medium text-gray-700 mb-1'
                  >
                    Max Price
                  </label>
                  <input
                    type='number'
                    name='maxPrice'
                    id='maxPrice'
                    value={filters.maxPrice}
                    onChange={handleInputChange}
                    placeholder='Max $'
                    min='0'
                    className='block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-color-retreat-green-dark focus:border-color-retreat-green-dark'
                  />
                </div>

                {/* Location */}
                <div>
                  <label
                    htmlFor='location'
                    className='block text-sm font-medium text-gray-700 mb-1'
                  >
                    Location
                  </label>
                  <input
                    type='text'
                    name='location'
                    id='location'
                    value={filters.location}
                    onChange={handleInputChange}
                    placeholder='Any location'
                    className='block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-color-retreat-green-dark focus:border-color-retreat-green-dark'
                  />
                </div>

                {/* Date range */}
                <div>
                  <label
                    htmlFor='startDate'
                    className='block text-sm font-medium text-gray-700 mb-1'
                  >
                    Start Date
                  </label>
                  <input
                    type='date'
                    name='startDate'
                    id='startDate'
                    value={filters.startDate}
                    onChange={handleInputChange}
                    className='block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-color-retreat-green-dark focus:border-color-retreat-green-dark'
                  />
                </div>

                <div>
                  <label htmlFor='endDate' className='block text-sm font-medium text-gray-700 mb-1'>
                    End Date
                  </label>
                  <input
                    type='date'
                    name='endDate'
                    id='endDate'
                    value={filters.endDate}
                    onChange={handleInputChange}
                    className='block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-color-retreat-green-dark focus:border-color-retreat-green-dark'
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </form>
    </div>
  );
};

export default FilterBar;
