import React, { useState } from 'react';
import { HandThumbUpIcon, HandThumbDownIcon } from '@heroicons/react/24/outline';
import {
  HandThumbUpIcon as HandThumbUpSolidIcon,
  HandThumbDownIcon as HandThumbDownSolidIcon,
} from '@heroicons/react/24/solid';

interface MessageFeedbackProps {
  messageId: string;
  onFeedback: (messageId: string, isPositive: boolean) => void;
}

/**
 * Component to render feedback buttons for a message
 */
const MessageFeedback: React.FC<MessageFeedbackProps> = ({ messageId, onFeedback }) => {
  const [feedback, setFeedback] = useState<'positive' | 'negative' | null>(null);

  const handlePositiveFeedback = () => {
    setFeedback('positive');
    onFeedback(messageId, true);
  };

  const handleNegativeFeedback = () => {
    setFeedback('negative');
    onFeedback(messageId, false);
  };

  return (
    <div className='flex justify-end mt-1 space-x-2'>
      <button
        onClick={handlePositiveFeedback}
        className={`p-1 rounded-full transition-colors ${
          feedback === 'positive' ? 'text-green-500' : 'text-gray-400 hover:text-green-500'
        }`}
        aria-label='Réponse utile'
        title='Réponse utile'
      >
        {feedback === 'positive' ? (
          <HandThumbUpSolidIcon className='h-4 w-4' />
        ) : (
          <HandThumbUpIcon className='h-4 w-4' />
        )}
      </button>

      <button
        onClick={handleNegativeFeedback}
        className={`p-1 rounded-full transition-colors ${
          feedback === 'negative' ? 'text-red-500' : 'text-gray-400 hover:text-red-500'
        }`}
        aria-label='Réponse non utile'
        title='Réponse non utile'
      >
        {feedback === 'negative' ? (
          <HandThumbDownSolidIcon className='h-4 w-4' />
        ) : (
          <HandThumbDownIcon className='h-4 w-4' />
        )}
      </button>
    </div>
  );
};

export default MessageFeedback;
