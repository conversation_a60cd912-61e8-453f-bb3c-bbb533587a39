# Molécules (Molecules)

Les molécules sont des composants composés d'atomes qui forment une unité fonctionnelle simple. Elles représentent des groupes d'atomes qui travaillent ensemble pour accomplir une fonction spécifique.

## Structure des dossiers

- `SearchBar/` : Barres de recherche

  - `SearchBar.tsx` : Barre de recherche complète (Input + IconButton)

- `FilterButton/` : Boutons de filtre

  - `FilterButton.tsx` : Bouton de filtre (Button + Icon)

- `Notification/` : Notifications

  - `Notification.tsx` : Notification (Icon + Text + Button)

- `Card/` : Cartes

  - `Card.tsx` : Carte de base (Image + Text + Button)

- `FormField/` : Champs de formulaire

  - `FormField.tsx` : Champ de formulaire (Label + Input)

- `SocialShare/` : Partage social
  - `SocialShare.tsx` : Partage social (Icon + Button)

## Règles d'utilisation

1. Les molécules doivent :

   - Être composées uniquement d'atomes
   - Avoir une fonction unique et claire
   - Être réutilisables dans différents contextes
   - Maintenir une cohérence visuelle

2. Chaque molécule doit :

   - Avoir ses propres tests
   - Être documentée avec des props typées
   - Inclure des exemples d'utilisation
   - Gérer son propre état si nécessaire
   - Suivre les conventions de nommage

3. Bonnes pratiques :
   - Éviter la duplication de code
   - Utiliser les atomes existants
   - Maintenir une séparation claire des responsabilités
   - Documenter les cas d'utilisation
