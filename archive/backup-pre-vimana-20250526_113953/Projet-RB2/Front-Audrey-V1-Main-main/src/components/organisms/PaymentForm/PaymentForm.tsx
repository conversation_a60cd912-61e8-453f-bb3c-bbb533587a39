import React, { useState, useEffect } from 'react';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { paymentService } from '../../../services/api/paymentService';
import Button from '../../atoms/Button/Button';
import Spinner from '../../atoms/Spinner/Spinner';
import Alert from '../../common/Alert';
import { motion } from 'framer-motion';

interface PaymentFormProps {
  bookingId: string;
  amount: number;
  currency: string;
  onSuccess: (paymentId: string) => void;
  onCancel: () => void;
  className?: string;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  bookingId,
  amount,
  currency,
  onSuccess,
  onCancel,
  className = '',
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentMethods, setPaymentMethods] = useState<any[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [isNewCard, setIsNewCard] = useState(true);

  // Fetch client secret and saved payment methods
  useEffect(() => {
    const fetchPaymentData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Create payment intent
        const paymentIntent = await paymentService.createPaymentIntent(bookingId);
        setClientSecret(paymentIntent.clientSecret);

        // Fetch saved payment methods
        const methods = await paymentService.getPaymentMethods();
        setPaymentMethods(methods);

        // If there are saved methods, default to using the first one
        if (methods.length > 0) {
          setSelectedPaymentMethod(methods[0].id);
          setIsNewCard(false);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to initialize payment');
        console.error('Payment initialization error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPaymentData();
  }, [bookingId]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      let paymentMethodId;

      if (isNewCard) {
        // Create a new payment method with the card element
        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
          throw new Error('Card element not found');
        }

        const { error, paymentMethod } = await stripe.createPaymentMethod({
          type: 'card',
          card: cardElement,
        });

        if (error) {
          throw new Error(error.message);
        }

        paymentMethodId = paymentMethod?.id;
      } else {
        // Use existing payment method
        paymentMethodId = selectedPaymentMethod;
      }

      if (!paymentMethodId) {
        throw new Error('No payment method selected');
      }

      // Confirm the payment
      const result = await paymentService.confirmPayment(clientSecret, paymentMethodId);

      // Handle success
      onSuccess(result.id);
    } catch (err: any) {
      setError(err.message || 'Payment failed');
      console.error('Payment error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentMethodChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (value === 'new') {
      setIsNewCard(true);
      setSelectedPaymentMethod(null);
    } else {
      setIsNewCard(false);
      setSelectedPaymentMethod(value);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#9e2146',
      },
    },
  };

  return (
    <motion.div
      className={`bg-white rounded-lg shadow-md p-6 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <h2 className='text-2xl font-semibold mb-6'>Payment Details</h2>

      {error && (
        <Alert type='error' message={error} className='mb-4' onClose={() => setError(null)} />
      )}

      <div className='mb-6'>
        <p className='text-lg font-medium'>
          Amount: {new Intl.NumberFormat('fr-FR', { style: 'currency', currency }).format(amount)}
        </p>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Payment Method Selection */}
        {paymentMethods.length > 0 && (
          <div className='mb-6'>
            <h3 className='text-lg font-medium mb-3'>Payment Method</h3>
            <div className='space-y-2'>
              {paymentMethods.map((method) => (
                <label key={method.id} className='flex items-center space-x-3'>
                  <input
                    type='radio'
                    name='paymentMethod'
                    value={method.id}
                    checked={selectedPaymentMethod === method.id}
                    onChange={handlePaymentMethodChange}
                    className='h-4 w-4 text-retreat-green focus:ring-retreat-green-dark'
                  />
                  <span>
                    {method.card.brand.toUpperCase()} •••• {method.card.last4} (expires{' '}
                    {method.card.exp_month}/{method.card.exp_year})
                  </span>
                </label>
              ))}
              <label className='flex items-center space-x-3'>
                <input
                  type='radio'
                  name='paymentMethod'
                  value='new'
                  checked={isNewCard}
                  onChange={handlePaymentMethodChange}
                  className='h-4 w-4 text-retreat-green focus:ring-retreat-green-dark'
                />
                <span>Use a new card</span>
              </label>
            </div>
          </div>
        )}

        {/* Card Element */}
        {isNewCard && (
          <div className='mb-6'>
            <h3 className='text-lg font-medium mb-3'>Card Details</h3>
            <div className='border border-gray-300 rounded-md p-3'>
              <CardElement options={cardElementOptions} />
            </div>
          </div>
        )}

        {/* Save Card Checkbox */}
        {isNewCard && (
          <div className='mb-6'>
            <label className='flex items-center space-x-3'>
              <input
                type='checkbox'
                name='saveCard'
                className='h-4 w-4 text-retreat-green focus:ring-retreat-green-dark'
              />
              <span>Save this card for future payments</span>
            </label>
          </div>
        )}

        {/* Action Buttons */}
        <div className='flex justify-end space-x-4'>
          <Button variant='outline' onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            type='submit'
            variant='primary'
            disabled={!stripe || !elements || isLoading || !clientSecret}
          >
            {isLoading ? <Spinner size='sm' className='mr-2' /> : null}
            Pay {new Intl.NumberFormat('fr-FR', { style: 'currency', currency }).format(amount)}
          </Button>
        </div>
      </form>
    </motion.div>
  );
};

export default PaymentForm;
