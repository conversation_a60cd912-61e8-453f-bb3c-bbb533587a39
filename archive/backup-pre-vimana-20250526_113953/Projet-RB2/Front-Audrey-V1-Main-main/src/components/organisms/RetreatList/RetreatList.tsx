import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { retreatService } from '../../../services/api/retreatService';
import RetreatCard from '../../molecules/RetreatCard/RetreatCard';
import Pagination from '../../molecules/Pagination/Pagination';
import Spinner from '../../atoms/Spinner/Spinner';
import FilterBar from '../../molecules/FilterBar/FilterBar';
import { motion } from 'framer-motion';

interface RetreatListProps {
  featured?: boolean;
  upcoming?: boolean;
  limit?: number;
  showFilters?: boolean;
  showPagination?: boolean;
  className?: string;
}

const RetreatList: React.FC<RetreatListProps> = ({
  featured = false,
  upcoming = false,
  limit = 6,
  showFilters = true,
  showPagination = true,
  className = '',
}) => {
  const [retreats, setRetreats] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    minPrice: '',
    maxPrice: '',
    location: '',
    startDate: '',
    endDate: '',
  });

  const navigate = useNavigate();

  useEffect(() => {
    const fetchRetreats = async () => {
      setLoading(true);
      setError(null);

      try {
        let response;

        if (featured) {
          response = await retreatService.getFeaturedRetreats();
          setRetreats(response);
        } else if (upcoming) {
          response = await retreatService.getUpcomingRetreats();
          setRetreats(response);
        } else {
          // Apply filters
          const filterParams: any = {
            page,
            limit,
          };

          if (filters.search) filterParams.search = filters.search;
          if (filters.category) filterParams.category = filters.category;
          if (filters.minPrice) filterParams.minPrice = Number(filters.minPrice);
          if (filters.maxPrice) filterParams.maxPrice = Number(filters.maxPrice);
          if (filters.location) filterParams.location = filters.location;
          if (filters.startDate) filterParams.startDate = filters.startDate;
          if (filters.endDate) filterParams.endDate = filters.endDate;

          response = await retreatService.getRetreats(filterParams);
          setRetreats(response.data);
          setTotalPages(Math.ceil(response.total / limit));
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch retreats');
        console.error('Error fetching retreats:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchRetreats();
  }, [featured, upcoming, page, limit, filters]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
  };

  const handleRetreatClick = (retreatId: string) => {
    navigate(`/retreats/${retreatId}`);
  };

  if (loading && page === 1) {
    return (
      <div className='flex justify-center items-center h-64'>
        <Spinner size='lg' />
      </div>
    );
  }

  if (error) {
    return (
      <div
        className='bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative'
        role='alert'
      >
        <strong className='font-bold'>Error: </strong>
        <span className='block sm:inline'>{error}</span>
      </div>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {showFilters && !featured && !upcoming && <FilterBar onFilterChange={handleFilterChange} />}

      {retreats.length === 0 ? (
        <div className='text-center py-12 bg-gray-50 rounded-lg'>
          <h3 className='text-xl font-medium text-gray-600'>No retreats found</h3>
          <p className='text-gray-500 mt-2'>
            Try adjusting your filters or check back later for new retreats.
          </p>
        </div>
      ) : (
        <motion.div
          className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {retreats.map((retreat) => (
            <motion.div
              key={retreat.id}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <RetreatCard
                id={retreat.id}
                title={retreat.title}
                image={
                  retreat.images && retreat.images.length > 0
                    ? retreat.images[0]
                    : '/images/placeholder-retreat.jpg'
                }
                location={retreat.location}
                price={retreat.price}
                duration={`${new Date(retreat.startDate).toLocaleDateString()} - ${new Date(retreat.endDate).toLocaleDateString()}`}
                maxParticipants={retreat.capacity || 10}
                rating={4.5} // Default rating if not available
                tags={retreat.categories || []}
                host={{
                  name: retreat.host
                    ? `${retreat.host.firstName} ${retreat.host.lastName}`
                    : 'Host Name',
                  avatar:
                    retreat.host && retreat.host.image
                      ? retreat.host.image
                      : '/images/placeholder-avatar.jpg',
                }}
              />
            </motion.div>
          ))}
        </motion.div>
      )}

      {showPagination && !featured && !upcoming && totalPages > 1 && (
        <div className='mt-8 flex justify-center'>
          <Pagination currentPage={page} totalPages={totalPages} onPageChange={handlePageChange} />
        </div>
      )}
    </div>
  );
};

export default RetreatList;
