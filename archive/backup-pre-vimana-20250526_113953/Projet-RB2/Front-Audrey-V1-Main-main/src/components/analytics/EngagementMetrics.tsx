import React from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { MetricsChart } from './MetricsChart';

export interface EngagementMetricsProps {
  metrics: {
    summary: {
      totalViews: number;
      totalLikes: number;
      totalComments: number;
      totalShares: number;
      totalBookmarks: number;
      totalClickThroughs: number;
      engagementRate: number;
      clickThroughRate: number;
    };
    timeSeries: {
      date: string;
      views: number;
      likes: number;
      comments: number;
      shares: number;
      bookmarks: number;
      clickThroughs: number;
    }[];
    period: {
      startDate: string | Date;
      endDate: string | Date;
    };
  };
  compact?: boolean;
}

export const EngagementMetrics: React.FC<EngagementMetricsProps> = ({ metrics, compact = false }) => {
  const { t } = useTranslation();

  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(2)}%`;
  };

  const getChangeClass = (value: number) => {
    if (value > 0) return 'text-green-600';
    if (value < 0) return 'text-red-600';
    return 'text-gray-500';
  };

  if (compact) {
    return (
      <div>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <h4 className="text-sm font-medium text-gray-500">{t('analytics.engagement.views')}</h4>
            <p className="text-xl font-bold">{formatNumber(metrics.summary.totalViews)}</p>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-500">{t('analytics.engagement.engagementRate')}</h4>
            <p className="text-xl font-bold">{formatPercentage(metrics.summary.engagementRate)}</p>
          </div>
        </div>
        
        <MetricsChart
          data={metrics.timeSeries}
          dataKeys={['views']}
          xAxisDataKey="date"
          height={200}
        />
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">{t('analytics.engagement.title')}</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.engagement.views')}</h3>
          <p className="text-2xl font-bold mt-2">{formatNumber(metrics.summary.totalViews)}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.engagement.likes')}</h3>
          <p className="text-2xl font-bold mt-2">{formatNumber(metrics.summary.totalLikes)}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.engagement.comments')}</h3>
          <p className="text-2xl font-bold mt-2">{formatNumber(metrics.summary.totalComments)}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.engagement.shares')}</h3>
          <p className="text-2xl font-bold mt-2">{formatNumber(metrics.summary.totalShares)}</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.engagement.engagementRate')}</h3>
          <p className="text-2xl font-bold mt-2">{formatPercentage(metrics.summary.engagementRate)}</p>
          <p className="text-xs text-gray-500 mt-1">
            {t('analytics.engagement.engagementRateDescription')}
          </p>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.engagement.clickThroughRate')}</h3>
          <p className="text-2xl font-bold mt-2">{formatPercentage(metrics.summary.clickThroughRate)}</p>
          <p className="text-xs text-gray-500 mt-1">
            {t('analytics.engagement.clickThroughRateDescription')}
          </p>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-medium mb-4">{t('analytics.engagement.viewsOverTime')}</h3>
        <MetricsChart
          data={metrics.timeSeries}
          dataKeys={['views']}
          xAxisDataKey="date"
          height={300}
        />
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-medium mb-4">{t('analytics.engagement.engagementOverTime')}</h3>
        <MetricsChart
          data={metrics.timeSeries}
          dataKeys={['likes', 'comments', 'shares']}
          xAxisDataKey="date"
          height={300}
        />
      </div>
    </div>
  );
};

export default EngagementMetrics;
