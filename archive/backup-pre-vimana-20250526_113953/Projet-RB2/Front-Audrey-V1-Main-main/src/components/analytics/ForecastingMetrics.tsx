import React, { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { MetricsChart } from './MetricsChart';
import { Forecast } from '../../services/api/analyticsService';
import { FadeIn } from '../ui/FadeIn';

export interface ForecastingMetricsProps {
  forecast: Forecast;
  compact?: boolean;
}

export const ForecastingMetrics: React.FC<ForecastingMetricsProps> = ({ 
  forecast, 
  compact = false 
}) => {
  const { t } = useTranslation();
  const [forecastPeriod, setForecastPeriod] = useState<'1m' | '3m' | '6m'>('1m');
  
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };
  
  const formatPercentage = (num: number) => {
    return `${num.toFixed(2)}%`;
  };
  
  const getChangeClass = (value: number) => {
    if (value > 0) return 'text-green-600';
    if (value < 0) return 'text-red-600';
    return 'text-gray-500';
  };
  
  const filterForecastData = (data: any[]) => {
    const now = new Date();
    let endDate = new Date();
    
    switch (forecastPeriod) {
      case '1m':
        endDate.setMonth(now.getMonth() + 1);
        break;
      case '3m':
        endDate.setMonth(now.getMonth() + 3);
        break;
      case '6m':
        endDate.setMonth(now.getMonth() + 6);
        break;
    }
    
    return data.filter(item => {
      const itemDate = new Date(item.date);
      return itemDate >= now && itemDate <= endDate;
    });
  };
  
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.5) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };
  
  const getNextMonthValue = (data: any[]) => {
    if (!data || data.length === 0) return { value: 0, confidence: 0 };
    
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    
    // Trouver la prévision la plus proche du mois prochain
    const nextMonthData = data.reduce((closest, current) => {
      const currentDate = new Date(current.date);
      const closestDate = closest ? new Date(closest.date) : null;
      
      if (!closestDate) return current;
      
      const currentDiff = Math.abs(currentDate.getTime() - nextMonth.getTime());
      const closestDiff = Math.abs(closestDate.getTime() - nextMonth.getTime());
      
      return currentDiff < closestDiff ? current : closest;
    }, null);
    
    return nextMonthData || { value: 0, confidence: 0 };
  };
  
  if (compact) {
    const viewsForecast = getNextMonthValue(forecast.engagement.views);
    const followersForecast = getNextMonthValue(forecast.audience.followers);
    const revenueForecast = getNextMonthValue(forecast.revenue.amount);
    
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <div>
            <h4 className="text-sm font-medium text-gray-500">{t('analytics.forecasting.engagement')}</h4>
            <p className="text-xl font-bold">{formatNumber(viewsForecast.value)} {t('analytics.engagement.views')}</p>
            <div className={`text-xs ${getConfidenceColor(viewsForecast.confidence)} px-2 py-0.5 rounded-full inline-block mt-1`}>
              {formatPercentage(viewsForecast.confidence * 100)} {t('analytics.forecasting.confidenceLevel')}
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-500">{t('analytics.forecasting.audience')}</h4>
            <p className="text-xl font-bold">{formatNumber(followersForecast.value)} {t('analytics.audience.totalFollowers')}</p>
            <div className={`text-xs ${getConfidenceColor(followersForecast.confidence)} px-2 py-0.5 rounded-full inline-block mt-1`}>
              {formatPercentage(followersForecast.confidence * 100)} {t('analytics.forecasting.confidenceLevel')}
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-500">{t('analytics.forecasting.revenue')}</h4>
            <p className="text-xl font-bold">{formatNumber(revenueForecast.value)} €</p>
            <div className={`text-xs ${getConfidenceColor(revenueForecast.confidence)} px-2 py-0.5 rounded-full inline-block mt-1`}>
              {formatPercentage(revenueForecast.confidence * 100)} {t('analytics.forecasting.confidenceLevel')}
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <FadeIn>
      <h2 className="text-xl font-semibold mb-6">{t('analytics.forecasting.title')}</h2>
      
      <div className="mb-6 flex justify-between items-center">
        <div className="text-sm font-medium text-gray-500">
          {t('analytics.forecasting.nextMonth')}
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => setForecastPeriod('1m')}
            className={`px-3 py-1 text-sm rounded-md ${
              forecastPeriod === '1m'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {t('analytics.forecasting.nextMonth')}
          </button>
          
          <button
            onClick={() => setForecastPeriod('3m')}
            className={`px-3 py-1 text-sm rounded-md ${
              forecastPeriod === '3m'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {t('analytics.forecasting.next3Months')}
          </button>
          
          <button
            onClick={() => setForecastPeriod('6m')}
            className={`px-3 py-1 text-sm rounded-md ${
              forecastPeriod === '6m'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {t('analytics.forecasting.next6Months')}
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-lg font-medium mb-4">{t('analytics.forecasting.engagement')}</h3>
          <MetricsChart
            data={filterForecastData(forecast.engagement.views)}
            dataKeys={['value']}
            xAxisDataKey="date"
            height={200}
          />
          <div className="mt-4 text-sm text-gray-500">
            {t('analytics.forecasting.confidenceLevel')}: {formatPercentage(getNextMonthValue(forecast.engagement.views).confidence * 100)}
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-lg font-medium mb-4">{t('analytics.forecasting.audience')}</h3>
          <MetricsChart
            data={filterForecastData(forecast.audience.followers)}
            dataKeys={['value']}
            xAxisDataKey="date"
            height={200}
          />
          <div className="mt-4 text-sm text-gray-500">
            {t('analytics.forecasting.confidenceLevel')}: {formatPercentage(getNextMonthValue(forecast.audience.followers).confidence * 100)}
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-lg font-medium mb-4">{t('analytics.forecasting.revenue')}</h3>
          <MetricsChart
            data={filterForecastData(forecast.revenue.amount)}
            dataKeys={['value']}
            xAxisDataKey="date"
            height={200}
          />
          <div className="mt-4 text-sm text-gray-500">
            {t('analytics.forecasting.confidenceLevel')}: {formatPercentage(getNextMonthValue(forecast.revenue.amount).confidence * 100)}
          </div>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-medium mb-4">{t('analytics.forecasting.engagement')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-2">{t('analytics.engagement.views')}</h4>
            <MetricsChart
              data={filterForecastData(forecast.engagement.views)}
              dataKeys={['value']}
              xAxisDataKey="date"
              height={250}
            />
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-2">{t('analytics.engagement.engagementRate')}</h4>
            <MetricsChart
              data={filterForecastData(forecast.engagement.engagementRate)}
              dataKeys={['value']}
              xAxisDataKey="date"
              height={250}
            />
          </div>
        </div>
      </div>
    </FadeIn>
  );
};

export default ForecastingMetrics;
