import React from 'react';
import { Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Rectangle, Layer } from 'recharts';

interface SankeyNode {
  name: string;
}

interface SankeyLink {
  source: number;
  target: number;
  value: number;
}

interface SankeyDiagramProps {
  data: {
    source: string;
    target: string;
    value: number;
  }[];
  height?: number;
  colors?: string[];
  nodeWidth?: number;
  nodePadding?: number;
}

export const SankeyDiagram: React.FC<SankeyDiagramProps> = ({
  data,
  height = 400,
  colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#6366F1', '#14B8A6'],
  nodeWidth = 10,
  nodePadding = 10,
}) => {
  // Transform data for Recharts Sankey
  const { nodes, links } = React.useMemo(() => {
    const nodeMap = new Map<string, number>();
    const nodeList: SankeyNode[] = [];
    
    // First pass: collect all unique nodes
    data.forEach(item => {
      if (!nodeMap.has(item.source)) {
        nodeMap.set(item.source, nodeList.length);
        nodeList.push({ name: item.source });
      }
      
      if (!nodeMap.has(item.target)) {
        nodeMap.set(item.target, nodeList.length);
        nodeList.push({ name: item.target });
      }
    });
    
    // Second pass: create links
    const linkList: SankeyLink[] = data.map(item => ({
      source: nodeMap.get(item.source) as number,
      target: nodeMap.get(item.target) as number,
      value: item.value,
    }));
    
    return {
      nodes: nodeList,
      links: linkList,
    };
  }, [data]);

  if (!data || data.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  // Custom link shape with gradient
  const CustomLink = (props: any) => {
    const { sourceX, sourceY, sourceControlX, targetX, targetY, targetControlX, linkWidth, index } = props;
    
    const gradientId = `linkGradient${index}`;
    const startColor = colors[index % colors.length];
    const endColor = colors[(index + 1) % colors.length];
    
    return (
      <Layer key={`CustomLink${index}`}>
        <defs>
          <linearGradient id={gradientId} x1="0" y1="0" x2="1" y2="0">
            <stop offset="0%" stopColor={startColor} stopOpacity={0.6} />
            <stop offset="100%" stopColor={endColor} stopOpacity={0.6} />
          </linearGradient>
        </defs>
        <path
          d={`
            M${sourceX},${sourceY}
            C${sourceControlX},${sourceY} ${targetControlX},${targetY} ${targetX},${targetY}
            L${targetX},${targetY + linkWidth}
            C${targetControlX},${targetY + linkWidth} ${sourceControlX},${sourceY + linkWidth} ${sourceX},${sourceY + linkWidth}
            Z
          `}
          fill={`url(#${gradientId})`}
          strokeWidth="0"
          onMouseEnter={props.onMouseEnter}
          onMouseLeave={props.onMouseLeave}
        />
      </Layer>
    );
  };

  // Custom node shape
  const CustomNode = (props: any) => {
    const { x, y, width, height, index, payload } = props;
    const color = colors[index % colors.length];
    
    return (
      <Rectangle
        x={x}
        y={y}
        width={width}
        height={height}
        fill={color}
        fillOpacity={0.9}
        onMouseEnter={props.onMouseEnter}
        onMouseLeave={props.onMouseLeave}
      />
    );
  };

  // Custom tooltip
  const CustomTooltip = (props: any) => {
    const { active, payload } = props;
    
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      
      return (
        <div className="bg-white p-2 border border-gray-200 shadow-md rounded-md">
          {data.source !== undefined && data.target !== undefined ? (
            // Link tooltip
            <div>
              <p className="font-medium">{`${nodes[data.source].name} → ${nodes[data.target].name}`}</p>
              <p className="text-gray-600">{`Value: ${data.value.toLocaleString()}`}</p>
            </div>
          ) : (
            // Node tooltip
            <div>
              <p className="font-medium">{data.name}</p>
              <p className="text-gray-600">{`Value: ${data.value?.toLocaleString() || 'N/A'}`}</p>
            </div>
          )}
        </div>
      );
    }
    
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <Sankey
        data={{ nodes, links }}
        nodeWidth={nodeWidth}
        nodePadding={nodePadding}
        link={<CustomLink />}
        node={<CustomNode />}
      >
        <Tooltip content={<CustomTooltip />} />
      </Sankey>
    </ResponsiveContainer>
  );
};

export default SankeyDiagram;
