import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AnalyticsDashboard } from '../AnalyticsDashboard';
import { analyticsService } from '../../../services/api/analyticsService';

// Mock des dépendances
jest.mock('../../../hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      if (params) {
        return `${key} ${JSON.stringify(params)}`;
      }
      return key;
    },
  }),
}));

jest.mock('../../../services/api/analyticsService');
jest.mock('../EngagementMetrics', () => ({
  EngagementMetrics: ({ metrics, compact }) => (
    <div data-testid="engagement-metrics" data-compact={compact}>
      Engagement Metrics Component
    </div>
  ),
}));
jest.mock('../AudienceMetrics', () => ({
  AudienceMetrics: ({ metrics, compact }) => (
    <div data-testid="audience-metrics" data-compact={compact}>
      Audience Metrics Component
    </div>
  ),
}));
jest.mock('../RevenueMetrics', () => ({
  RevenueMetrics: ({ metrics, compact }) => (
    <div data-testid="revenue-metrics" data-compact={compact}>
      Revenue Metrics Component
    </div>
  ),
}));
jest.mock('../ContentPerformance', () => ({
  ContentPerformance: ({ content, compact, creatorId }) => (
    <div data-testid="content-performance" data-compact={compact}>
      Content Performance Component
    </div>
  ),
}));
jest.mock('../CustomDashboard', () => ({
  CustomDashboard: ({ creatorId }) => (
    <div data-testid="custom-dashboard">
      Custom Dashboard Component
    </div>
  ),
}));

describe('AnalyticsDashboard', () => {
  const mockCreatorId = '123e4567-e89b-12d3-a456-426614174000';
  const mockMetrics = {
    engagement: {
      summary: {
        totalViews: 1000,
        totalLikes: 500,
        totalComments: 100,
        totalShares: 50,
        totalBookmarks: 200,
        totalClickThroughs: 300,
        engagementRate: 65,
        clickThroughRate: 30,
      },
      timeSeries: [],
      period: {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      },
    },
    audience: {
      totalFollowers: 5000,
      newFollowers: 100,
      lostFollowers: 20,
      activeFollowers: 3000,
      demographics: {},
      period: {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      },
    },
    revenue: {
      totalRevenue: 10000,
      bySources: [],
      timeSeries: [],
      period: {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      },
    },
    topContent: [],
    period: {
      startDate: '2023-01-01',
      endDate: '2023-01-31',
    },
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    (analyticsService.getCreatorMetrics as jest.Mock).mockResolvedValue(mockMetrics);
  });

  it('renders the dashboard with overview tab active by default', async () => {
    render(<AnalyticsDashboard creatorId={mockCreatorId} />);
    
    // Vérifier que le titre est affiché
    expect(screen.getByText('analytics.dashboard.title')).toBeInTheDocument();
    
    // Vérifier que l'onglet Overview est actif par défaut
    expect(screen.getByText('analytics.dashboard.tabs.overview')).toHaveClass('text-blue-600');
    
    // Vérifier que les filtres sont affichés
    expect(screen.getByText('analytics.filters.allContentTypes')).toBeInTheDocument();
    expect(screen.getByText('analytics.filters.allTime')).toBeInTheDocument();
    
    // Vérifier que le service a été appelé
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalledWith(
        mockCreatorId,
        expect.any(Object)
      );
    });
    
    // Vérifier que les composants de l'aperçu sont affichés
    await waitFor(() => {
      expect(screen.getByTestId('engagement-metrics')).toBeInTheDocument();
      expect(screen.getByTestId('audience-metrics')).toBeInTheDocument();
      expect(screen.getByTestId('revenue-metrics')).toBeInTheDocument();
      expect(screen.getByTestId('content-performance')).toBeInTheDocument();
    });
  });

  it('switches to engagement tab when clicked', async () => {
    render(<AnalyticsDashboard creatorId={mockCreatorId} />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalled();
    });
    
    // Cliquer sur l'onglet Engagement
    fireEvent.click(screen.getByText('analytics.dashboard.tabs.engagement'));
    
    // Vérifier que l'onglet Engagement est actif
    expect(screen.getByText('analytics.dashboard.tabs.engagement')).toHaveClass('text-blue-600');
    
    // Vérifier que le composant EngagementMetrics est affiché avec compact=false
    await waitFor(() => {
      const engagementMetrics = screen.getByTestId('engagement-metrics');
      expect(engagementMetrics).toBeInTheDocument();
      expect(engagementMetrics.getAttribute('data-compact')).toBe('false');
    });
  });

  it('filters data by content type', async () => {
    render(<AnalyticsDashboard creatorId={mockCreatorId} />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalled();
    });
    
    // Sélectionner un type de contenu
    fireEvent.change(screen.getByRole('combobox'), {
      target: { value: 'RETREAT' },
    });
    
    // Vérifier que le service a été appelé avec le bon filtre
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalledWith(
        mockCreatorId,
        expect.objectContaining({
          contentType: 'RETREAT',
        })
      );
    });
  });

  it('filters data by date range', async () => {
    render(<AnalyticsDashboard creatorId={mockCreatorId} />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalled();
    });
    
    // Cliquer sur le filtre des 30 derniers jours
    fireEvent.click(screen.getByText('analytics.filters.last30Days'));
    
    // Vérifier que le service a été appelé avec le bon filtre
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalledWith(
        mockCreatorId,
        expect.objectContaining({
          startDate: expect.any(String),
          endDate: expect.any(String),
        })
      );
    });
  });

  it('displays loading state while fetching data', async () => {
    // Configurer le mock pour retarder la résolution
    (analyticsService.getCreatorMetrics as jest.Mock).mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(mockMetrics), 100))
    );
    
    render(<AnalyticsDashboard creatorId={mockCreatorId} />);
    
    // Vérifier que l'indicateur de chargement est affiché
    expect(screen.getByRole('status')).toBeInTheDocument();
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(screen.queryByRole('status')).not.toBeInTheDocument();
    });
  });

  it('displays custom dashboard when custom tab is clicked', async () => {
    render(<AnalyticsDashboard creatorId={mockCreatorId} />);
    
    // Attendre que les données soient chargées
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalled();
    });
    
    // Cliquer sur l'onglet Custom
    fireEvent.click(screen.getByText('analytics.dashboard.tabs.custom'));
    
    // Vérifier que l'onglet Custom est actif
    expect(screen.getByText('analytics.dashboard.tabs.custom')).toHaveClass('text-blue-600');
    
    // Vérifier que le composant CustomDashboard est affiché
    await waitFor(() => {
      expect(screen.getByTestId('custom-dashboard')).toBeInTheDocument();
    });
  });
});
