import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AnalyticsDashboard } from '../../AnalyticsDashboard';
import { ContentPerformance } from '../../ContentPerformance';
import { analyticsService } from '../../../../services/api/analyticsService';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import ContentDetailAnalyticsPage from '../../../../pages/ContentDetailAnalyticsPage';

// Mock des dépendances
jest.mock('../../../../hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      if (params) {
        return `${key} ${JSON.stringify(params)}`;
      }
      return key;
    },
  }),
}));

jest.mock('../../../../hooks/useAuthContext', () => ({
  useAuthContext: () => ({
    user: { id: 'test-user-id', roles: ['creator'] },
  }),
}));

jest.mock('../../../../services/api/analyticsService');
jest.mock('../../EngagementMetrics', () => ({
  EngagementMetrics: ({ metrics, compact }: { metrics: any; compact: boolean }) => (
    <div data-testid="engagement-metrics" data-compact={compact}>
      Engagement Metrics Component
    </div>
  ),
}));
jest.mock('../../AudienceMetrics', () => ({
  AudienceMetrics: ({ metrics, compact }: { metrics: any; compact: boolean }) => (
    <div data-testid="audience-metrics" data-compact={compact}>
      Audience Metrics Component
    </div>
  ),
}));
jest.mock('../../RevenueMetrics', () => ({
  RevenueMetrics: ({ metrics, compact }: { metrics: any; compact: boolean }) => (
    <div data-testid="revenue-metrics" data-compact={compact}>
      Revenue Metrics Component
    </div>
  ),
}));
jest.mock('../../ForecastingMetrics', () => ({
  ForecastingMetrics: ({ forecast }: { forecast: any }) => (
    <div data-testid="forecasting-metrics">
      Forecasting Metrics Component
    </div>
  ),
}));
jest.mock('../../BenchmarkMetrics', () => ({
  BenchmarkMetrics: ({ benchmark, categories, onCategoryChange }: { benchmark: any; categories: string[]; onCategoryChange: any }) => (
    <div data-testid="benchmark-metrics" data-categories={categories.join(',')}>
      Benchmark Metrics Component
    </div>
  ),
}));

describe('Analytics Flow Integration Tests', () => {
  const mockCreatorId = 'test-creator-id';
  const mockMetrics = {
    engagement: {
      summary: {
        totalViews: 1000,
        totalLikes: 500,
        totalComments: 100,
        totalShares: 50,
        totalBookmarks: 200,
        totalClickThroughs: 300,
        engagementRate: 65,
        clickThroughRate: 30,
      },
      timeSeries: [],
      period: {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      },
    },
    audience: {
      totalFollowers: 5000,
      newFollowers: 100,
      lostFollowers: 20,
      activeFollowers: 3000,
      demographics: {},
      period: {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      },
    },
    revenue: {
      totalRevenue: 10000,
      bySources: [],
      timeSeries: [],
      period: {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      },
    },
    topContent: [
      {
        contentId: 'content-1',
        contentType: 'RETREAT',
        views: 500,
        likes: 200,
        comments: 50,
        shares: 30,
        bookmarks: 100,
        clickThroughs: 150,
        engagement: 280,
        engagementRate: 56,
      },
      {
        contentId: 'content-2',
        contentType: 'POST',
        views: 300,
        likes: 150,
        comments: 30,
        shares: 20,
        bookmarks: 50,
        clickThroughs: 100,
        engagement: 200,
        engagementRate: 66.67,
      },
    ],
    period: {
      startDate: '2023-01-01',
      endDate: '2023-01-31',
    },
  };
  
  const mockForecast = {
    engagement: {
      views: [
        { date: '2023-02-01', value: 1100, confidence: 0.9 },
        { date: '2023-03-01', value: 1200, confidence: 0.8 },
        { date: '2023-04-01', value: 1300, confidence: 0.7 },
      ],
      engagementRate: [
        { date: '2023-02-01', value: 66, confidence: 0.9 },
        { date: '2023-03-01', value: 67, confidence: 0.8 },
        { date: '2023-04-01', value: 68, confidence: 0.7 },
      ],
    },
    audience: {
      followers: [
        { date: '2023-02-01', value: 5100, confidence: 0.9 },
        { date: '2023-03-01', value: 5200, confidence: 0.8 },
        { date: '2023-04-01', value: 5300, confidence: 0.7 },
      ],
    },
    revenue: {
      amount: [
        { date: '2023-02-01', value: 10500, confidence: 0.9 },
        { date: '2023-03-01', value: 11000, confidence: 0.8 },
        { date: '2023-04-01', value: 11500, confidence: 0.7 },
      ],
    },
  };
  
  const mockBenchmark = {
    engagement: {
      views: {
        value: 1000,
        percentile: 75,
        categoryAverage: 800,
      },
      engagementRate: {
        value: 65,
        percentile: 80,
        categoryAverage: 50,
      },
    },
    audience: {
      followers: {
        value: 5000,
        percentile: 70,
        categoryAverage: 4000,
      },
      growth: {
        value: 2,
        percentile: 60,
        categoryAverage: 1.5,
      },
    },
    revenue: {
      amount: {
        value: 10000,
        percentile: 85,
        categoryAverage: 8000,
      },
    },
    category: 'all',
  };
  
  const mockContentMetrics = {
    engagement: {
      summary: {
        totalViews: 500,
        totalLikes: 200,
        totalComments: 50,
        totalShares: 30,
        totalBookmarks: 100,
        totalClickThroughs: 150,
        engagementRate: 56,
        clickThroughRate: 30,
      },
      timeSeries: [],
      period: {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      },
    },
    revenue: {
      totalRevenue: 2000,
      bySources: [],
      timeSeries: [],
      period: {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      },
    },
    period: {
      startDate: '2023-01-01',
      endDate: '2023-01-31',
    },
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock des réponses des services
    (analyticsService.getCreatorMetrics as jest.Mock).mockResolvedValue(mockMetrics);
    (analyticsService.getCreatorForecasts as jest.Mock).mockResolvedValue(mockForecast);
    (analyticsService.getCreatorBenchmarks as jest.Mock).mockResolvedValue(mockBenchmark);
    (analyticsService.getContentMetrics as jest.Mock).mockResolvedValue(mockContentMetrics);
  });

  it('should navigate between analytics dashboard tabs', async () => {
    render(<AnalyticsDashboard creatorId={mockCreatorId} />);
    
    // Vérifier que l'onglet Overview est actif par défaut
    expect(screen.getByText('analytics.dashboard.tabs.overview')).toHaveClass('text-blue-600');
    
    // Attendre que les métriques soient chargées
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalledWith(
        mockCreatorId,
        expect.any(Object)
      );
    });
    
    // Cliquer sur l'onglet Engagement
    fireEvent.click(screen.getByText('analytics.dashboard.tabs.engagement'));
    
    // Vérifier que l'onglet Engagement est actif
    expect(screen.getByText('analytics.dashboard.tabs.engagement')).toHaveClass('text-blue-600');
    expect(screen.getByTestId('engagement-metrics')).toBeInTheDocument();
    
    // Cliquer sur l'onglet Forecasting
    fireEvent.click(screen.getByText('analytics.forecasting.title'));
    
    // Vérifier que l'onglet Forecasting est actif et que les prévisions sont chargées
    await waitFor(() => {
      expect(screen.getByText('analytics.forecasting.title')).toHaveClass('text-blue-600');
      expect(analyticsService.getCreatorForecasts).toHaveBeenCalledWith(mockCreatorId);
      expect(screen.getByTestId('forecasting-metrics')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Benchmarks
    fireEvent.click(screen.getByText('analytics.benchmarks.title'));
    
    // Vérifier que l'onglet Benchmarks est actif et que les benchmarks sont chargés
    await waitFor(() => {
      expect(screen.getByText('analytics.benchmarks.title')).toHaveClass('text-blue-600');
      expect(analyticsService.getCreatorBenchmarks).toHaveBeenCalledWith(mockCreatorId, 'all');
      expect(screen.getByTestId('benchmark-metrics')).toBeInTheDocument();
    });
  });

  it('should filter analytics data by content type', async () => {
    render(<AnalyticsDashboard creatorId={mockCreatorId} />);
    
    // Attendre que les métriques soient chargées
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalled();
    });
    
    // Sélectionner un type de contenu
    fireEvent.change(screen.getByRole('combobox'), {
      target: { value: 'RETREAT' },
    });
    
    // Vérifier que le service a été appelé avec le bon filtre
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalledWith(
        mockCreatorId,
        expect.objectContaining({
          contentType: 'RETREAT',
        })
      );
    });
  });

  it('should filter analytics data by date range', async () => {
    render(<AnalyticsDashboard creatorId={mockCreatorId} />);
    
    // Attendre que les métriques soient chargées
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalled();
    });
    
    // Cliquer sur le filtre des 30 derniers jours
    fireEvent.click(screen.getByText('analytics.filters.last30Days'));
    
    // Vérifier que le service a été appelé avec le bon filtre
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalledWith(
        mockCreatorId,
        expect.objectContaining({
          startDate: expect.any(String),
          endDate: expect.any(String),
        })
      );
    });
  });

  it('should navigate to content detail page when clicking on content', async () => {
    // Utiliser MemoryRouter pour simuler la navigation
    render(
      <MemoryRouter initialEntries={[`/analytics/${mockCreatorId}`]}>
        <Routes>
          <Route path="/analytics/:creatorId" element={<AnalyticsDashboard creatorId={mockCreatorId} />} />
          <Route path="/analytics/:creatorId/content/:contentId" element={<ContentDetailAnalyticsPage />} />
        </Routes>
      </MemoryRouter>
    );
    
    // Attendre que les métriques soient chargées
    await waitFor(() => {
      expect(analyticsService.getCreatorMetrics).toHaveBeenCalled();
    });
    
    // Cliquer sur l'onglet Content
    fireEvent.click(screen.getByText('analytics.dashboard.tabs.content'));
    
    // Attendre que le contenu soit affiché
    await waitFor(() => {
      expect(screen.getByText('analytics.content.table.content')).toBeInTheDocument();
    });
    
    // Cliquer sur "View Details" pour le premier contenu
    // Note: Comme ContentPerformance est mocké, nous ne pouvons pas vraiment cliquer sur le lien
    // Nous allons donc vérifier que le lien est correctement configuré
    expect(ContentPerformance).toHaveBeenCalledWith(
      expect.objectContaining({
        content: mockMetrics.topContent,
        creatorId: mockCreatorId,
      }),
      expect.anything()
    );
  });
});
