/**
 * Panel de Contrôle des Workflows
 * Gestion et orchestration des workflows multi-agents
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Grid,
  Badge,
  Input,
  Select,
  Alert,
  Modal,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>ooter
} from '../ui/design-system';
import { useToast } from '../ui/design-system/Toast';
import { workflowService } from '../../services/api/workflowService';

interface Workflow {
  id: string;
  name: string;
  description: string;
  status: 'running' | 'completed' | 'failed' | 'paused' | 'pending';
  progress: number;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  steps: WorkflowStep[];
  agents: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  creator: string;
}

interface WorkflowStep {
  id: string;
  name: string;
  agent: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  input?: any;
  output?: any;
  error?: string;
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  steps: Omit<WorkflowStep, 'id' | 'status' | 'startTime' | 'endTime' | 'duration'>[];
  estimatedDuration: number;
}

export const WorkflowControlPanel: React.FC = () => {
  const { toast } = useToast();
  
  // États
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newWorkflow, setNewWorkflow] = useState({
    name: '',
    description: '',
    template: '',
    priority: 'medium' as const,
    parameters: {}
  });

  // Charger les workflows
  const loadWorkflows = async () => {
    try {
      setIsLoading(true);
      const [workflowsResponse, templatesResponse] = await Promise.all([
        workflowService.getWorkflows(),
        workflowService.getTemplates()
      ]);
      
      setWorkflows(workflowsResponse.data);
      setTemplates(templatesResponse.data);
    } catch (error) {
      console.error('Erreur lors du chargement des workflows:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les workflows',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadWorkflows();
    
    // Rafraîchissement automatique toutes les 10 secondes
    const interval = setInterval(loadWorkflows, 10000);
    return () => clearInterval(interval);
  }, []);

  // Actions sur les workflows
  const handleWorkflowAction = async (workflowId: string, action: 'pause' | 'resume' | 'cancel' | 'restart') => {
    try {
      await workflowService.controlWorkflow(workflowId, action);
      toast({
        title: 'Succès',
        description: `Workflow ${action} avec succès`,
        variant: 'default'
      });
      loadWorkflows();
    } catch (error) {
      console.error('Erreur lors de l\'action:', error);
      toast({
        title: 'Erreur',
        description: `Impossible d'exécuter l'action ${action}`,
        variant: 'destructive'
      });
    }
  };

  // Créer un nouveau workflow
  const handleCreateWorkflow = async () => {
    try {
      await workflowService.createWorkflow(newWorkflow);
      toast({
        title: 'Succès',
        description: 'Workflow créé avec succès',
        variant: 'default'
      });
      setShowCreateModal(false);
      setNewWorkflow({
        name: '',
        description: '',
        template: '',
        priority: 'medium',
        parameters: {}
      });
      loadWorkflows();
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de créer le workflow',
        variant: 'destructive'
      });
    }
  };

  // Formater la durée
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) return `${hours}h ${minutes}m`;
    if (minutes > 0) return `${minutes}m ${secs}s`;
    return `${secs}s`;
  };

  // Obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-blue-500';
      case 'completed': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      case 'paused': return 'bg-yellow-500';
      case 'pending': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  // Obtenir la couleur de la priorité
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'destructive';
      case 'high': return 'warning';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  const selectedWorkflowData = workflows.find(w => w.id === selectedWorkflow);

  return (
    <div className="space-y-6">
      {/* Actions rapides */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Contrôle des Workflows</h2>
          <p className="text-gray-600">Gestion et orchestration des workflows multi-agents</p>
        </div>
        
        <div className="flex space-x-3">
          <Button onClick={loadWorkflows} variant="outline" leftIcon="🔄">
            Actualiser
          </Button>
          <Button onClick={() => setShowCreateModal(true)} leftIcon="➕">
            Nouveau Workflow
          </Button>
        </div>
      </div>

      {/* Statistiques */}
      <Grid cols={4} gap="md">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-blue-600">
              {workflows.filter(w => w.status === 'running').length}
            </div>
            <p className="text-sm text-gray-500">En cours</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">
              {workflows.filter(w => w.status === 'completed').length}
            </div>
            <p className="text-sm text-gray-500">Terminés</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-red-600">
              {workflows.filter(w => w.status === 'failed').length}
            </div>
            <p className="text-sm text-gray-500">Échoués</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-gray-600">
              {workflows.filter(w => w.status === 'pending').length}
            </div>
            <p className="text-sm text-gray-500">En attente</p>
          </CardContent>
        </Card>
      </Grid>

      {/* Liste des workflows */}
      <Grid cols={selectedWorkflow ? 2 : 1} gap="lg">
        <Card>
          <CardHeader>
            <CardTitle>Workflows actifs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {workflows.map((workflow) => (
                <motion.div
                  key={workflow.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedWorkflow === workflow.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedWorkflow(workflow.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(workflow.status)}`} />
                      <h3 className="font-medium">{workflow.name}</h3>
                      <Badge variant={getPriorityColor(workflow.priority)}>
                        {workflow.priority}
                      </Badge>
                    </div>
                    
                    <div className="flex space-x-1">
                      {workflow.status === 'running' && (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleWorkflowAction(workflow.id, 'pause');
                            }}
                          >
                            ⏸️
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleWorkflowAction(workflow.id, 'cancel');
                            }}
                          >
                            ⏹️
                          </Button>
                        </>
                      )}
                      
                      {workflow.status === 'paused' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleWorkflowAction(workflow.id, 'resume');
                          }}
                        >
                          ▶️
                        </Button>
                      )}
                      
                      {(workflow.status === 'failed' || workflow.status === 'completed') && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleWorkflowAction(workflow.id, 'restart');
                          }}
                        >
                          🔄
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{workflow.description}</p>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">
                      {workflow.agents.length} agent(s) • {workflow.steps.length} étapes
                    </span>
                    <span className="text-gray-500">
                      {workflow.duration ? formatDuration(workflow.duration) : 'En cours...'}
                    </span>
                  </div>
                  
                  {workflow.status === 'running' && (
                    <div className="mt-2">
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>Progression</span>
                        <span>{workflow.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${workflow.progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}
              
              {workflows.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  Aucun workflow en cours
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Détails du workflow sélectionné */}
        {selectedWorkflow && selectedWorkflowData && (
          <Card>
            <CardHeader>
              <CardTitle>Détails du workflow</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">{selectedWorkflowData.name}</h3>
                  <p className="text-sm text-gray-600">{selectedWorkflowData.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Statut:</span>
                    <Badge variant={getPriorityColor(selectedWorkflowData.priority)} className="ml-2">
                      {selectedWorkflowData.status}
                    </Badge>
                  </div>
                  <div>
                    <span className="text-gray-500">Priorité:</span>
                    <span className="ml-2">{selectedWorkflowData.priority}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Début:</span>
                    <span className="ml-2">{new Date(selectedWorkflowData.startTime).toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Créateur:</span>
                    <span className="ml-2">{selectedWorkflowData.creator}</span>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Étapes du workflow</h4>
                  <div className="space-y-2">
                    {selectedWorkflowData.steps.map((step, index) => (
                      <div key={step.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                        <div className={`w-2 h-2 rounded-full ${getStatusColor(step.status)}`} />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{step.name}</span>
                            <span className="text-xs text-gray-500">{step.agent}</span>
                          </div>
                          {step.duration && (
                            <span className="text-xs text-gray-500">
                              Durée: {formatDuration(step.duration)}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </Grid>

      {/* Modal de création de workflow */}
      <Modal open={showCreateModal} onOpenChange={setShowCreateModal}>
        <ModalContent>
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">Créer un nouveau workflow</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Nom</label>
                <Input
                  value={newWorkflow.name}
                  onChange={(e) => setNewWorkflow({...newWorkflow, name: e.target.value})}
                  placeholder="Nom du workflow"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <Input
                  value={newWorkflow.description}
                  onChange={(e) => setNewWorkflow({...newWorkflow, description: e.target.value})}
                  placeholder="Description du workflow"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Template</label>
                <Select
                  value={newWorkflow.template}
                  onValueChange={(value) => setNewWorkflow({...newWorkflow, template: value})}
                >
                  <option value="">Sélectionner un template</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name} - {template.category}
                    </option>
                  ))}
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Priorité</label>
                <Select
                  value={newWorkflow.priority}
                  onValueChange={(value) => setNewWorkflow({...newWorkflow, priority: value as any})}
                >
                  <option value="low">Basse</option>
                  <option value="medium">Moyenne</option>
                  <option value="high">Haute</option>
                  <option value="critical">Critique</option>
                </Select>
              </div>
            </div>
          </div>
          
          <ModalFooter>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Annuler
            </Button>
            <Button onClick={handleCreateWorkflow} disabled={!newWorkflow.name || !newWorkflow.template}>
              Créer
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
