/**
 * Gestion des Alertes
 * Interface d'administration des alertes système
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Grid,
  Badge,
  Input,
  Select,
  Alert,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Switch
} from '../ui/design-system';
import { useToast } from '../ui/design-system/Toast';
import { alertsService } from '../../services/api/alertsService';

interface AlertItem {
  id: string;
  title: string;
  description: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  status: 'active' | 'acknowledged' | 'resolved';
  source: string;
  timestamp: Date;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  metadata?: Record<string, any>;
  actions?: AlertAction[];
}

interface AlertAction {
  id: string;
  label: string;
  type: 'button' | 'link';
  action: string;
  variant?: 'default' | 'destructive' | 'outline';
}

interface AlertRule {
  id: string;
  name: string;
  description: string;
  metric: string;
  condition: 'greater_than' | 'less_than' | 'equals' | 'not_equals';
  threshold: number;
  duration: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  enabled: boolean;
  channels: string[];
}

export const AlertsManagement: React.FC = () => {
  const { toast } = useToast();
  
  // États
  const [alerts, setAlerts] = useState<AlertItem[]>([]);
  const [alertRules, setAlertRules] = useState<AlertRule[]>([]);
  const [filteredAlerts, setFilteredAlerts] = useState<AlertItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAlert, setSelectedAlert] = useState<string | null>(null);
  const [showRulesModal, setShowRulesModal] = useState(false);
  const [showCreateRuleModal, setShowCreateRuleModal] = useState(false);
  
  // Filtres
  const [filters, setFilters] = useState({
    severity: 'all',
    status: 'all',
    source: 'all',
    search: ''
  });

  // Nouvelle règle
  const [newRule, setNewRule] = useState<Partial<AlertRule>>({
    name: '',
    description: '',
    metric: '',
    condition: 'greater_than',
    threshold: 0,
    duration: 300,
    severity: 'warning',
    enabled: true,
    channels: ['email']
  });

  // Charger les alertes
  const loadAlerts = async () => {
    try {
      setIsLoading(true);
      const [alertsResponse, rulesResponse] = await Promise.all([
        alertsService.getAlerts(),
        alertsService.getAlertRules()
      ]);
      
      setAlerts(alertsResponse.data);
      setAlertRules(rulesResponse.data);
    } catch (error) {
      console.error('Erreur lors du chargement des alertes:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les alertes',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Filtrer les alertes
  useEffect(() => {
    let filtered = alerts;

    if (filters.severity !== 'all') {
      filtered = filtered.filter(alert => alert.severity === filters.severity);
    }

    if (filters.status !== 'all') {
      filtered = filtered.filter(alert => alert.status === filters.status);
    }

    if (filters.source !== 'all') {
      filtered = filtered.filter(alert => alert.source === filters.source);
    }

    if (filters.search) {
      filtered = filtered.filter(alert => 
        alert.title.toLowerCase().includes(filters.search.toLowerCase()) ||
        alert.description.toLowerCase().includes(filters.search.toLowerCase())
      );
    }

    setFilteredAlerts(filtered);
  }, [alerts, filters]);

  useEffect(() => {
    loadAlerts();
    
    // Rafraîchissement automatique toutes les 30 secondes
    const interval = setInterval(loadAlerts, 30000);
    return () => clearInterval(interval);
  }, []);

  // Actions sur les alertes
  const handleAlertAction = async (alertId: string, action: 'acknowledge' | 'resolve') => {
    try {
      await alertsService.updateAlert(alertId, { action });
      toast({
        title: 'Succès',
        description: `Alerte ${action === 'acknowledge' ? 'acquittée' : 'résolue'}`,
        variant: 'default'
      });
      loadAlerts();
    } catch (error) {
      console.error('Erreur lors de l\'action:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible d\'exécuter l\'action',
        variant: 'destructive'
      });
    }
  };

  // Créer une règle d'alerte
  const handleCreateRule = async () => {
    try {
      await alertsService.createAlertRule(newRule as AlertRule);
      toast({
        title: 'Succès',
        description: 'Règle d\'alerte créée',
        variant: 'default'
      });
      setShowCreateRuleModal(false);
      setNewRule({
        name: '',
        description: '',
        metric: '',
        condition: 'greater_than',
        threshold: 0,
        duration: 300,
        severity: 'warning',
        enabled: true,
        channels: ['email']
      });
      loadAlerts();
    } catch (error) {
      console.error('Erreur lors de la création:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de créer la règle',
        variant: 'destructive'
      });
    }
  };

  // Obtenir la couleur de la sévérité
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'error': return 'destructive';
      case 'warning': return 'warning';
      case 'info': return 'default';
      default: return 'secondary';
    }
  };

  // Obtenir l'icône de la sévérité
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return '🚨';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '📋';
    }
  };

  // Obtenir les sources uniques
  const uniqueSources = [...new Set(alerts.map(alert => alert.source))];

  const selectedAlertData = alerts.find(a => a.id === selectedAlert);

  return (
    <div className="space-y-6">
      {/* En-tête et actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Gestion des Alertes</h2>
          <p className="text-gray-600">Administration des alertes et règles de notification</p>
        </div>
        
        <div className="flex space-x-3">
          <Button onClick={() => setShowRulesModal(true)} variant="outline" leftIcon="⚙️">
            Règles
          </Button>
          <Button onClick={loadAlerts} variant="outline" leftIcon="🔄">
            Actualiser
          </Button>
          <Button onClick={() => setShowCreateRuleModal(true)} leftIcon="➕">
            Nouvelle Règle
          </Button>
        </div>
      </div>

      {/* Statistiques */}
      <Grid cols={4} gap="md">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-red-600">
              {alerts.filter(a => a.severity === 'critical' && a.status === 'active').length}
            </div>
            <p className="text-sm text-gray-500">Critiques actives</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-yellow-600">
              {alerts.filter(a => a.severity === 'warning' && a.status === 'active').length}
            </div>
            <p className="text-sm text-gray-500">Avertissements</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-blue-600">
              {alerts.filter(a => a.status === 'acknowledged').length}
            </div>
            <p className="text-sm text-gray-500">Acquittées</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">
              {alerts.filter(a => a.status === 'resolved').length}
            </div>
            <p className="text-sm text-gray-500">Résolues</p>
          </CardContent>
        </Card>
      </Grid>

      {/* Filtres */}
      <Card>
        <CardHeader>
          <CardTitle>Filtres</CardTitle>
        </CardHeader>
        <CardContent>
          <Grid cols={4} gap="md">
            <div>
              <label className="block text-sm font-medium mb-1">Recherche</label>
              <Input
                placeholder="Rechercher..."
                value={filters.search}
                onChange={(e) => setFilters({...filters, search: e.target.value})}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Sévérité</label>
              <Select
                value={filters.severity}
                onValueChange={(value) => setFilters({...filters, severity: value})}
              >
                <option value="all">Toutes</option>
                <option value="critical">Critique</option>
                <option value="error">Erreur</option>
                <option value="warning">Avertissement</option>
                <option value="info">Information</option>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Statut</label>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters({...filters, status: value})}
              >
                <option value="all">Tous</option>
                <option value="active">Actives</option>
                <option value="acknowledged">Acquittées</option>
                <option value="resolved">Résolues</option>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Source</label>
              <Select
                value={filters.source}
                onValueChange={(value) => setFilters({...filters, source: value})}
              >
                <option value="all">Toutes</option>
                {uniqueSources.map(source => (
                  <option key={source} value={source}>{source}</option>
                ))}
              </Select>
            </div>
          </Grid>
        </CardContent>
      </Card>

      {/* Liste des alertes */}
      <Grid cols={selectedAlert ? 2 : 1} gap="lg">
        <Card>
          <CardHeader>
            <CardTitle>
              Alertes ({filteredAlerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredAlerts.map((alert) => (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedAlert === alert.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedAlert(alert.id)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{getSeverityIcon(alert.severity)}</span>
                      <div>
                        <h3 className="font-medium">{alert.title}</h3>
                        <p className="text-sm text-gray-600">{alert.description}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Badge variant={getSeverityColor(alert.severity)}>
                        {alert.severity}
                      </Badge>
                      <Badge variant={alert.status === 'active' ? 'destructive' : 'default'}>
                        {alert.status}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{alert.source}</span>
                    <span>{new Date(alert.timestamp).toLocaleString()}</span>
                  </div>
                  
                  {alert.status === 'active' && (
                    <div className="flex space-x-2 mt-3">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAlertAction(alert.id, 'acknowledge');
                        }}
                      >
                        Acquitter
                      </Button>
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAlertAction(alert.id, 'resolve');
                        }}
                      >
                        Résoudre
                      </Button>
                    </div>
                  )}
                </motion.div>
              ))}
              
              {filteredAlerts.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  Aucune alerte trouvée
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Détails de l'alerte sélectionnée */}
        {selectedAlert && selectedAlertData && (
          <Card>
            <CardHeader>
              <CardTitle>Détails de l'alerte</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2 flex items-center space-x-2">
                    <span>{getSeverityIcon(selectedAlertData.severity)}</span>
                    <span>{selectedAlertData.title}</span>
                  </h3>
                  <p className="text-gray-600">{selectedAlertData.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Sévérité:</span>
                    <Badge variant={getSeverityColor(selectedAlertData.severity)} className="ml-2">
                      {selectedAlertData.severity}
                    </Badge>
                  </div>
                  <div>
                    <span className="text-gray-500">Statut:</span>
                    <Badge variant={selectedAlertData.status === 'active' ? 'destructive' : 'default'} className="ml-2">
                      {selectedAlertData.status}
                    </Badge>
                  </div>
                  <div>
                    <span className="text-gray-500">Source:</span>
                    <span className="ml-2">{selectedAlertData.source}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Timestamp:</span>
                    <span className="ml-2">{new Date(selectedAlertData.timestamp).toLocaleString()}</span>
                  </div>
                </div>
                
                {selectedAlertData.acknowledgedBy && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm">
                      <strong>Acquittée par:</strong> {selectedAlertData.acknowledgedBy}
                    </p>
                    <p className="text-sm text-gray-500">
                      Le {new Date(selectedAlertData.acknowledgedAt!).toLocaleString()}
                    </p>
                  </div>
                )}
                
                {selectedAlertData.resolvedAt && (
                  <div className="p-3 bg-green-50 rounded-lg">
                    <p className="text-sm">
                      <strong>Résolue le:</strong> {new Date(selectedAlertData.resolvedAt).toLocaleString()}
                    </p>
                  </div>
                )}
                
                {selectedAlertData.metadata && (
                  <div>
                    <h4 className="font-medium mb-2">Métadonnées</h4>
                    <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                      {JSON.stringify(selectedAlertData.metadata, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </Grid>

      {/* Modal des règles d'alerte */}
      <Modal open={showRulesModal} onOpenChange={setShowRulesModal}>
        <ModalContent className="max-w-4xl">
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">Règles d'alerte</h2>
            
            <div className="space-y-3">
              {alertRules.map((rule) => (
                <div key={rule.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <h3 className="font-medium">{rule.name}</h3>
                    <p className="text-sm text-gray-600">{rule.description}</p>
                    <p className="text-xs text-gray-500">
                      {rule.metric} {rule.condition.replace('_', ' ')} {rule.threshold} pendant {rule.duration}s
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Badge variant={getSeverityColor(rule.severity)}>
                      {rule.severity}
                    </Badge>
                    <Switch checked={rule.enabled} />
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <ModalFooter>
            <Button variant="outline" onClick={() => setShowRulesModal(false)}>
              Fermer
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Modal de création de règle */}
      <Modal open={showCreateRuleModal} onOpenChange={setShowCreateRuleModal}>
        <ModalContent>
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">Créer une règle d'alerte</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Nom</label>
                <Input
                  value={newRule.name}
                  onChange={(e) => setNewRule({...newRule, name: e.target.value})}
                  placeholder="Nom de la règle"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <Input
                  value={newRule.description}
                  onChange={(e) => setNewRule({...newRule, description: e.target.value})}
                  placeholder="Description de la règle"
                />
              </div>
              
              <Grid cols={2} gap="md">
                <div>
                  <label className="block text-sm font-medium mb-1">Métrique</label>
                  <Select
                    value={newRule.metric}
                    onValueChange={(value) => setNewRule({...newRule, metric: value})}
                  >
                    <option value="">Sélectionner une métrique</option>
                    <option value="cpu_usage">Utilisation CPU</option>
                    <option value="memory_usage">Utilisation mémoire</option>
                    <option value="response_time">Temps de réponse</option>
                    <option value="error_rate">Taux d'erreur</option>
                  </Select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Condition</label>
                  <Select
                    value={newRule.condition}
                    onValueChange={(value) => setNewRule({...newRule, condition: value as any})}
                  >
                    <option value="greater_than">Supérieur à</option>
                    <option value="less_than">Inférieur à</option>
                    <option value="equals">Égal à</option>
                    <option value="not_equals">Différent de</option>
                  </Select>
                </div>
              </Grid>
              
              <Grid cols={2} gap="md">
                <div>
                  <label className="block text-sm font-medium mb-1">Seuil</label>
                  <Input
                    type="number"
                    value={newRule.threshold}
                    onChange={(e) => setNewRule({...newRule, threshold: Number(e.target.value)})}
                    placeholder="Valeur seuil"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Durée (secondes)</label>
                  <Input
                    type="number"
                    value={newRule.duration}
                    onChange={(e) => setNewRule({...newRule, duration: Number(e.target.value)})}
                    placeholder="Durée en secondes"
                  />
                </div>
              </Grid>
              
              <div>
                <label className="block text-sm font-medium mb-1">Sévérité</label>
                <Select
                  value={newRule.severity}
                  onValueChange={(value) => setNewRule({...newRule, severity: value as any})}
                >
                  <option value="info">Information</option>
                  <option value="warning">Avertissement</option>
                  <option value="error">Erreur</option>
                  <option value="critical">Critique</option>
                </Select>
              </div>
            </div>
          </div>
          
          <ModalFooter>
            <Button variant="outline" onClick={() => setShowCreateRuleModal(false)}>
              Annuler
            </Button>
            <Button onClick={handleCreateRule} disabled={!newRule.name || !newRule.metric}>
              Créer
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
