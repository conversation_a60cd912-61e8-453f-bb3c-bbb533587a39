import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { moderationService } from '../../services/api/moderationService';
import { ReportDetails } from './ReportDetails';

export interface Report {
  id: string;
  contentType: string;
  contentId: string;
  reporterId: string;
  reason: string;
  description?: string;
  status: 'PENDING' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED' | 'ESCALATED';
  createdAt: string;
  updatedAt: string;
  moderationActions: ModerationAction[];
}

export interface ModerationAction {
  id: string;
  reportId: string;
  moderatorId: string;
  action: string;
  comment?: string;
  createdAt: string;
}

export interface ReportListProps {
  userRole: 'admin' | 'moderator' | 'user';
}

export const ReportList: React.FC<ReportListProps> = ({ userRole }) => {
  const { t } = useTranslation();
  const [reports, setReports] = useState<Report[]>([]);
  const [totalReports, setTotalReports] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [filters, setFilters] = useState({
    status: '',
    contentType: '',
  });

  const reportsPerPage = 10;

  useEffect(() => {
    fetchReports();
  }, [currentPage, filters]);

  const fetchReports = async () => {
    try {
      setIsLoading(true);
      const response = await moderationService.getReports({
        status: filters.status || undefined,
        contentType: filters.contentType || undefined,
        skip: (currentPage - 1) * reportsPerPage,
        take: reportsPerPage,
      });
      
      setReports(response.reports);
      setTotalReports(response.total);
    } catch (error) {
      console.error('Error fetching reports:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
    setCurrentPage(1);
  };

  const handleReportClick = (report: Report) => {
    setSelectedReport(report);
  };

  const handleCloseDetails = () => {
    setSelectedReport(null);
    fetchReports(); // Refresh the list after closing details
  };

  const totalPages = Math.ceil(totalReports / reportsPerPage);

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'IN_REVIEW':
        return 'bg-blue-100 text-blue-800';
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      case 'ESCALATED':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (selectedReport) {
    return <ReportDetails report={selectedReport} onClose={handleCloseDetails} userRole={userRole} />;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">{t('moderation.reports.title')}</h2>
        
        <div className="flex space-x-4">
          <select
            name="status"
            value={filters.status}
            onChange={handleFilterChange}
            className="border rounded-md px-3 py-2 text-sm"
          >
            <option value="">{t('moderation.reports.filters.allStatuses')}</option>
            <option value="PENDING">{t('moderation.reports.filters.pending')}</option>
            <option value="IN_REVIEW">{t('moderation.reports.filters.inReview')}</option>
            <option value="APPROVED">{t('moderation.reports.filters.approved')}</option>
            <option value="REJECTED">{t('moderation.reports.filters.rejected')}</option>
            <option value="ESCALATED">{t('moderation.reports.filters.escalated')}</option>
          </select>
          
          <select
            name="contentType"
            value={filters.contentType}
            onChange={handleFilterChange}
            className="border rounded-md px-3 py-2 text-sm"
          >
            <option value="">{t('moderation.reports.filters.allContentTypes')}</option>
            <option value="TEXT">{t('moderation.reports.filters.text')}</option>
            <option value="IMAGE">{t('moderation.reports.filters.image')}</option>
            <option value="VIDEO">{t('moderation.reports.filters.video')}</option>
            <option value="COMMENT">{t('moderation.reports.filters.comment')}</option>
            <option value="POST">{t('moderation.reports.filters.post')}</option>
          </select>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : reports.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {t('moderation.reports.noReports')}
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('moderation.reports.table.id')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('moderation.reports.table.contentType')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('moderation.reports.table.reason')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('moderation.reports.table.status')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('moderation.reports.table.date')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('moderation.reports.table.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reports.map((report) => (
                  <tr key={report.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {report.id.substring(0, 8)}...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {report.contentType}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {report.reason.length > 50 ? `${report.reason.substring(0, 50)}...` : report.reason}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(report.status)}`}>
                        {report.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(report.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => handleReportClick(report)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        {t('moderation.reports.table.view')}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <nav className="flex items-center">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded-md ${
                    currentPage === 1
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {t('common.previous')}
                </button>
                
                <span className="mx-4 text-sm text-gray-700">
                  {t('common.pagination', { current: currentPage, total: totalPages })}
                </span>
                
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded-md ${
                    currentPage === totalPages
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {t('common.next')}
                </button>
              </nav>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ReportList;
