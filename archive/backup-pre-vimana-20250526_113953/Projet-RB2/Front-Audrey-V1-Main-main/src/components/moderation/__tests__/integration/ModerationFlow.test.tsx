import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ModerationDashboard } from '../../ModerationDashboard';
import { ContentModerationForm } from '../../ContentModerationForm';
import { moderationService } from '../../../../services/api/moderationService';
import { aiModerationService } from '../../../../services/api/aiModerationService';

// Mock des dépendances
jest.mock('../../../../hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => key, // Retourne simplement la clé pour faciliter les tests
  }),
}));

jest.mock('../../../../services/api/moderationService');
jest.mock('../../../../services/api/aiModerationService');
jest.mock('../../ReportList', () => ({
  ReportList: ({ userRole }: { userRole: string }) => (
    <div data-testid="report-list" data-user-role={userRole}>Report List Component</div>
  ),
}));
jest.mock('../../ModerationRules', () => ({
  ModerationRules: ({ userRole }: { userRole: string }) => (
    <div data-testid="moderation-rules" data-user-role={userRole}>Moderation Rules Component</div>
  ),
}));
jest.mock('../../ModerationStats', () => ({
  ModerationStats: ({ stats, isLoading }: { stats: any; isLoading: boolean }) => (
    <div data-testid="moderation-stats" data-is-loading={isLoading}>
      Moderation Stats Component
      <pre>{JSON.stringify(stats, null, 2)}</pre>
    </div>
  ),
}));

describe('Moderation Flow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock des réponses des services
    (moderationService.getStats as jest.Mock).mockResolvedValue({
      total: 100,
      pending: 20,
      inReview: 10,
      approved: 40,
      rejected: 25,
      escalated: 5,
    });
    
    (moderationService.moderateContent as jest.Mock).mockResolvedValue({
      isInappropriate: false,
      severity: null,
      matchedRules: [],
      confidence: 0.1,
    });
    
    (aiModerationService.moderateContent as jest.Mock).mockResolvedValue({
      isInappropriate: true,
      severity: 'MEDIUM',
      matchedRules: [
        { id: '1', name: 'Test Rule', severity: 'MEDIUM' }
      ],
      confidence: 0.85,
      explanation: 'This content contains inappropriate language.',
    });
  });

  it('should navigate between dashboard tabs', async () => {
    render(<ModerationDashboard userRole="admin" />);
    
    // Vérifier que l'onglet Reports est actif par défaut
    expect(screen.getByText('moderation.dashboard.tabs.reports')).toHaveClass('text-blue-600');
    expect(screen.getByTestId('report-list')).toBeInTheDocument();
    
    // Cliquer sur l'onglet Rules
    fireEvent.click(screen.getByText('moderation.dashboard.tabs.rules'));
    
    // Vérifier que l'onglet Rules est actif
    await waitFor(() => {
      expect(screen.getByText('moderation.dashboard.tabs.rules')).toHaveClass('text-blue-600');
      expect(screen.getByTestId('moderation-rules')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Stats
    fireEvent.click(screen.getByText('moderation.dashboard.tabs.stats'));
    
    // Vérifier que l'onglet Stats est actif
    await waitFor(() => {
      expect(screen.getByText('moderation.dashboard.tabs.stats')).toHaveClass('text-blue-600');
      expect(screen.getByTestId('moderation-stats')).toBeInTheDocument();
    });
  });

  it('should moderate text content using AI service', async () => {
    render(<ContentModerationForm />);
    
    // Remplir le formulaire
    fireEvent.change(screen.getByLabelText('moderation.contentForm.text'), {
      target: { value: 'This is a test content' },
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('moderation.contentForm.checkContent'));
    
    // Vérifier que le service AI a été appelé
    await waitFor(() => {
      expect(aiModerationService.moderateContent).toHaveBeenCalledWith(
        { text: 'This is a test content' },
        'TEXT'
      );
    });
    
    // Vérifier que le résultat est affiché
    await waitFor(() => {
      expect(screen.getByText('moderation.contentForm.result.title')).toBeInTheDocument();
      expect(screen.getByText('moderation.contentForm.result.inappropriate')).toBeInTheDocument();
      expect(screen.getByText('MEDIUM')).toBeInTheDocument();
      expect(screen.getByText('Test Rule')).toBeInTheDocument();
      expect(screen.getByText('This content contains inappropriate language.')).toBeInTheDocument();
    });
  });

  it('should fallback to standard moderation when AI service fails', async () => {
    // Configurer le mock pour simuler une erreur du service AI
    (aiModerationService.moderateContent as jest.Mock).mockRejectedValue(new Error('AI service error'));
    
    render(<ContentModerationForm />);
    
    // Remplir le formulaire
    fireEvent.change(screen.getByLabelText('moderation.contentForm.text'), {
      target: { value: 'This is a test content' },
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('moderation.contentForm.checkContent'));
    
    // Vérifier que le service AI a été appelé et a échoué
    await waitFor(() => {
      expect(aiModerationService.moderateContent).toHaveBeenCalled();
    });
    
    // Vérifier que le service standard a été appelé comme fallback
    await waitFor(() => {
      expect(moderationService.moderateContent).toHaveBeenCalledWith(
        { text: 'This is a test content' },
        'TEXT'
      );
    });
    
    // Vérifier que le résultat est affiché
    await waitFor(() => {
      expect(screen.getByText('moderation.contentForm.result.title')).toBeInTheDocument();
      expect(screen.getByText('moderation.contentForm.result.appropriate')).toBeInTheDocument();
    });
  });

  it('should handle image moderation', async () => {
    render(<ContentModerationForm />);
    
    // Changer le type de contenu à IMAGE
    fireEvent.change(screen.getByLabelText('moderation.contentForm.contentType'), {
      target: { value: 'IMAGE' },
    });
    
    // Remplir l'URL de l'image
    fireEvent.change(screen.getByLabelText('moderation.contentForm.imageUrl'), {
      target: { value: 'https://example.com/image.jpg' },
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('moderation.contentForm.checkContent'));
    
    // Vérifier que le service AI a été appelé
    await waitFor(() => {
      expect(aiModerationService.moderateContent).toHaveBeenCalledWith(
        { imageUrl: 'https://example.com/image.jpg', base64Image: '' },
        'IMAGE'
      );
    });
    
    // Vérifier que le résultat est affiché
    await waitFor(() => {
      expect(screen.getByText('moderation.contentForm.result.title')).toBeInTheDocument();
    });
  });

  it('should reset form when clicking on new check button', async () => {
    render(<ContentModerationForm />);
    
    // Remplir le formulaire
    fireEvent.change(screen.getByLabelText('moderation.contentForm.text'), {
      target: { value: 'This is a test content' },
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('moderation.contentForm.checkContent'));
    
    // Attendre que le résultat soit affiché
    await waitFor(() => {
      expect(screen.getByText('moderation.contentForm.result.title')).toBeInTheDocument();
    });
    
    // Cliquer sur le bouton pour une nouvelle vérification
    fireEvent.click(screen.getByText('moderation.contentForm.newCheck'));
    
    // Vérifier que le formulaire est réinitialisé
    await waitFor(() => {
      expect(screen.getByLabelText('moderation.contentForm.text')).toBeInTheDocument();
      expect(screen.getByLabelText('moderation.contentForm.text')).toHaveValue('');
    });
  });
});
