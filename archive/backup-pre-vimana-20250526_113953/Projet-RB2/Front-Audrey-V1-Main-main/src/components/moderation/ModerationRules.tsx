import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { moderationService, TextModerationRule, ImageModerationRule } from '../../services/api/moderationService';
import { FadeIn } from '../ui/FadeIn';

export interface ModerationRulesProps {
  userRole: string;
}

export const ModerationRules: React.FC<ModerationRulesProps> = ({ userRole }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'text' | 'image'>('text');
  const [textRules, setTextRules] = useState<TextModerationRule[]>([]);
  const [imageRules, setImageRules] = useState<ImageModerationRule[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState<boolean>(false);
  const [editingRule, setEditingRule] = useState<TextModerationRule | ImageModerationRule | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    id: '',
    name: '',
    description: '',
    pattern: '',
    category: '',
    threshold: 0.5,
    severity: 'MEDIUM',
    isActive: true,
  });
  
  useEffect(() => {
    fetchRules();
  }, [activeTab]);
  
  const fetchRules = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      if (activeTab === 'text') {
        const rules = await moderationService.getTextRules();
        setTextRules(rules);
      } else {
        const rules = await moderationService.getImageRules();
        setImageRules(rules);
      }
    } catch (error) {
      console.error(`Error fetching ${activeTab} rules:`, error);
      setError(t('moderation.rules.errors.fetchFailed'));
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'threshold') {
      setFormData(prev => ({ ...prev, [name]: parseFloat(value) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };
  
  const resetForm = () => {
    setFormData({
      id: '',
      name: '',
      description: '',
      pattern: '',
      category: '',
      threshold: 0.5,
      severity: 'MEDIUM',
      isActive: true,
    });
    setEditingRule(null);
    setShowForm(false);
  };
  
  const handleEditRule = (rule: TextModerationRule | ImageModerationRule) => {
    setEditingRule(rule);
    setFormData({
      id: rule.id,
      name: rule.name,
      description: rule.description || '',
      pattern: 'pattern' in rule ? rule.pattern : '',
      category: 'category' in rule ? rule.category : '',
      threshold: 'threshold' in rule ? rule.threshold : 0.5,
      severity: rule.severity,
      isActive: rule.isActive,
    });
    setShowForm(true);
  };
  
  const validateForm = () => {
    if (!formData.name.trim()) {
      setError(t('moderation.rules.errors.nameRequired'));
      return false;
    }
    
    if (activeTab === 'text' && !formData.pattern.trim()) {
      setError(t('moderation.rules.errors.patternRequired'));
      return false;
    }
    
    if (activeTab === 'image') {
      if (!formData.category.trim()) {
        setError(t('moderation.rules.errors.categoryRequired'));
        return false;
      }
      
      if (formData.threshold < 0 || formData.threshold > 1) {
        setError(t('moderation.rules.errors.thresholdRequired'));
        return false;
      }
    }
    
    if (!formData.severity) {
      setError(t('moderation.rules.errors.severityRequired'));
      return false;
    }
    
    return true;
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      if (activeTab === 'text') {
        const ruleData = {
          name: formData.name,
          description: formData.description,
          pattern: formData.pattern,
          severity: formData.severity,
          isActive: formData.isActive,
        };
        
        if (editingRule) {
          await moderationService.updateTextRule(formData.id, ruleData);
        } else {
          await moderationService.createTextRule(ruleData);
        }
      } else {
        const ruleData = {
          name: formData.name,
          description: formData.description,
          category: formData.category,
          threshold: formData.threshold,
          severity: formData.severity,
          isActive: formData.isActive,
        };
        
        if (editingRule) {
          await moderationService.updateImageRule(formData.id, ruleData);
        } else {
          await moderationService.createImageRule(ruleData);
        }
      }
      
      // Rafraîchir la liste des règles
      await fetchRules();
      
      // Réinitialiser le formulaire
      resetForm();
    } catch (error) {
      console.error('Error saving rule:', error);
      setError(t('moderation.rules.errors.saveFailed'));
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleDeleteRule = async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      if (activeTab === 'text') {
        await moderationService.deleteTextRule(id);
      } else {
        await moderationService.deleteImageRule(id);
      }
      
      // Rafraîchir la liste des règles
      await fetchRules();
      
      // Fermer la confirmation de suppression
      setShowDeleteConfirm(null);
    } catch (error) {
      console.error('Error deleting rule:', error);
      setError(t('moderation.rules.errors.deleteFailed'));
    } finally {
      setIsLoading(false);
    }
  };
  
  const getSeverityClass = (severity: string) => {
    switch (severity) {
      case 'LOW':
        return 'bg-yellow-100 text-yellow-800';
      case 'MEDIUM':
        return 'bg-orange-100 text-orange-800';
      case 'HIGH':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const renderRuleForm = () => {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 className="text-lg font-medium mb-4">
          {editingRule ? t('moderation.rules.editRule') : t('moderation.rules.addRule')}
        </h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              {t('moderation.rules.form.name')}
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder={t('moderation.rules.form.namePlaceholder')}
            />
          </div>
          
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              {t('moderation.rules.form.description')}
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder={t('moderation.rules.form.descriptionPlaceholder')}
            />
          </div>
          
          {activeTab === 'text' && (
            <div>
              <label htmlFor="pattern" className="block text-sm font-medium text-gray-700">
                {t('moderation.rules.form.pattern')}
              </label>
              <input
                type="text"
                id="pattern"
                name="pattern"
                value={formData.pattern}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder={t('moderation.rules.form.patternPlaceholder')}
              />
            </div>
          )}
          
          {activeTab === 'image' && (
            <>
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  {t('moderation.rules.form.category')}
                </label>
                <input
                  type="text"
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder={t('moderation.rules.form.categoryPlaceholder')}
                />
              </div>
              
              <div>
                <label htmlFor="threshold" className="block text-sm font-medium text-gray-700">
                  {t('moderation.rules.form.threshold')}
                </label>
                <input
                  type="range"
                  id="threshold"
                  name="threshold"
                  min="0"
                  max="1"
                  step="0.01"
                  value={formData.threshold}
                  onChange={handleInputChange}
                  className="mt-1 block w-full"
                />
                <div className="text-sm text-gray-500 mt-1">{formData.threshold}</div>
              </div>
            </>
          )}
          
          <div>
            <label htmlFor="severity" className="block text-sm font-medium text-gray-700">
              {t('moderation.rules.form.severity')}
            </label>
            <select
              id="severity"
              name="severity"
              value={formData.severity}
              onChange={handleInputChange}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
            >
              <option value="LOW">LOW</option>
              <option value="MEDIUM">MEDIUM</option>
              <option value="HIGH">HIGH</option>
            </select>
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              name="isActive"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
              {t('moderation.rules.form.isActive')}
            </label>
          </div>
          
          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">{error}</h3>
                </div>
              </div>
            </div>
          )}
          
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={resetForm}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {t('moderation.rules.form.cancel')}
            </button>
            
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {isLoading ? (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : null}
              {t('moderation.rules.form.submit')}
            </button>
          </div>
        </form>
      </div>
    );
  };
  
  return (
    <FadeIn>
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <h2 className="text-xl font-semibold">{t('moderation.rules.title')}</h2>
        
        <div className="mt-4 md:mt-0 flex space-x-2">
          <button
            className={`px-4 py-2 text-sm rounded-md ${
              activeTab === 'text'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            onClick={() => setActiveTab('text')}
          >
            {t('moderation.rules.tabs.text')}
          </button>
          
          <button
            className={`px-4 py-2 text-sm rounded-md ${
              activeTab === 'image'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            onClick={() => setActiveTab('image')}
          >
            {t('moderation.rules.tabs.image')}
          </button>
          
          {userRole === 'admin' && (
            <button
              className="ml-4 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              onClick={() => setShowForm(true)}
            >
              {t('moderation.rules.addRule')}
            </button>
          )}
        </div>
      </div>
      
      {showForm && renderRuleForm()}
      
      {isLoading && !showForm ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {activeTab === 'text' && textRules.length === 0 && (
            <div className="text-center py-12 text-gray-500">
              {t('moderation.rules.noRules')}
            </div>
          )}
          
          {activeTab === 'image' && imageRules.length === 0 && (
            <div className="text-center py-12 text-gray-500">
              {t('moderation.rules.noRules')}
            </div>
          )}
          
          {activeTab === 'text' && textRules.length > 0 && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('moderation.rules.table.name')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('moderation.rules.table.pattern')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('moderation.rules.table.severity')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('moderation.rules.table.status')}
                    </th>
                    {userRole === 'admin' && (
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('moderation.rules.table.actions')}
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {textRules.map((rule) => (
                    <tr key={rule.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{rule.name}</div>
                        {rule.description && (
                          <div className="text-sm text-gray-500">{rule.description}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-mono">{rule.pattern}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getSeverityClass(rule.severity)}`}>
                          {rule.severity}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          rule.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {rule.isActive ? t('common.active') : t('common.inactive')}
                        </span>
                      </td>
                      {userRole === 'admin' && (
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleEditRule(rule)}
                            className="text-blue-600 hover:text-blue-900 mr-4"
                          >
                            {t('moderation.rules.editRule')}
                          </button>
                          <button
                            onClick={() => setShowDeleteConfirm(rule.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            {t('moderation.rules.deleteRule')}
                          </button>
                          
                          {showDeleteConfirm === rule.id && (
                            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
                              <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">
                                  {t('moderation.rules.deleteConfirm')}
                                </h3>
                                <div className="flex justify-end space-x-3">
                                  <button
                                    onClick={() => setShowDeleteConfirm(null)}
                                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                  >
                                    {t('common.cancel')}
                                  </button>
                                  <button
                                    onClick={() => handleDeleteRule(rule.id)}
                                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                  >
                                    {t('common.delete')}
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {activeTab === 'image' && imageRules.length > 0 && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('moderation.rules.table.name')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('moderation.rules.table.category')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('moderation.rules.table.threshold')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('moderation.rules.table.severity')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('moderation.rules.table.status')}
                    </th>
                    {userRole === 'admin' && (
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('moderation.rules.table.actions')}
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {imageRules.map((rule) => (
                    <tr key={rule.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{rule.name}</div>
                        {rule.description && (
                          <div className="text-sm text-gray-500">{rule.description}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{rule.category}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{rule.threshold}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getSeverityClass(rule.severity)}`}>
                          {rule.severity}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          rule.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {rule.isActive ? t('common.active') : t('common.inactive')}
                        </span>
                      </td>
                      {userRole === 'admin' && (
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleEditRule(rule)}
                            className="text-blue-600 hover:text-blue-900 mr-4"
                          >
                            {t('moderation.rules.editRule')}
                          </button>
                          <button
                            onClick={() => setShowDeleteConfirm(rule.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            {t('moderation.rules.deleteRule')}
                          </button>
                          
                          {showDeleteConfirm === rule.id && (
                            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
                              <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">
                                  {t('moderation.rules.deleteConfirm')}
                                </h3>
                                <div className="flex justify-end space-x-3">
                                  <button
                                    onClick={() => setShowDeleteConfirm(null)}
                                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                  >
                                    {t('common.cancel')}
                                  </button>
                                  <button
                                    onClick={() => handleDeleteRule(rule.id)}
                                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                  >
                                    {t('common.delete')}
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </FadeIn>
  );
};

export default ModerationRules;
