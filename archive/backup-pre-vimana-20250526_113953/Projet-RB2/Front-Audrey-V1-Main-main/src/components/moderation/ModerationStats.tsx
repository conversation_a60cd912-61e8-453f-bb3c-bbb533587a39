import React, { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { ModerationStats as ModerationStatsType } from '../../services/api/moderationService';
import { MetricsChart } from '../analytics/MetricsChart';
import { FadeIn } from '../ui/FadeIn';
import { AnimatedCounter } from '../ui/AnimatedCounter';
import { ExportDataButton } from '../analytics/ExportDataButton';

export interface ModerationStatsProps {
  stats: ModerationStatsType;
  isLoading: boolean;
  timeSeriesData?: {
    byDate: {
      date: string;
      total: number;
      pending: number;
      inReview: number;
      approved: number;
      rejected: number;
      escalated: number;
    }[];
    byContentType: {
      contentType: string;
      count: number;
    }[];
    averageResolutionTime: number;
  };
}

export const ModerationStats: React.FC<ModerationStatsProps> = ({
  stats,
  isLoading,
  timeSeriesData,
}) => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  
  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  if (!stats) {
    return (
      <div className="text-center py-12 text-gray-500">
        {t('moderation.stats.noData')}
      </div>
    );
  }
  
  // Données pour le graphique par statut
  const statusChartData = [
    { name: t('moderation.reports.filters.pending'), value: stats.pending },
    { name: t('moderation.reports.filters.inReview'), value: stats.inReview },
    { name: t('moderation.reports.filters.approved'), value: stats.approved },
    { name: t('moderation.reports.filters.rejected'), value: stats.rejected },
    { name: t('moderation.reports.filters.escalated'), value: stats.escalated },
  ];
  
  // Données pour le graphique par type de contenu (si disponible)
  const contentTypeChartData = timeSeriesData?.byContentType || [
    { contentType: 'TEXT', count: 35 },
    { contentType: 'IMAGE', count: 25 },
    { contentType: 'VIDEO', count: 15 },
    { contentType: 'COMMENT', count: 20 },
    { contentType: 'POST', count: 5 },
  ];
  
  // Données pour le graphique par date (si disponible)
  const dateChartData = timeSeriesData?.byDate || [
    { date: '2023-01-01', total: 10, pending: 2, inReview: 1, approved: 5, rejected: 2, escalated: 0 },
    { date: '2023-01-02', total: 12, pending: 3, inReview: 2, approved: 4, rejected: 2, escalated: 1 },
    { date: '2023-01-03', total: 15, pending: 4, inReview: 2, approved: 6, rejected: 3, escalated: 0 },
    { date: '2023-01-04', total: 8, pending: 1, inReview: 1, approved: 4, rejected: 2, escalated: 0 },
    { date: '2023-01-05', total: 14, pending: 3, inReview: 2, approved: 5, rejected: 3, escalated: 1 },
    { date: '2023-01-06', total: 11, pending: 2, inReview: 1, approved: 5, rejected: 2, escalated: 1 },
    { date: '2023-01-07', total: 9, pending: 1, inReview: 1, approved: 4, rejected: 3, escalated: 0 },
  ];
  
  // Filtrer les données par plage de temps
  const filterDateData = (data: any[]) => {
    if (timeRange === 'all') return data;
    
    const now = new Date();
    const cutoffDate = new Date();
    
    switch (timeRange) {
      case '7d':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        cutoffDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        cutoffDate.setDate(now.getDate() - 90);
        break;
    }
    
    return data.filter(item => {
      const itemDate = new Date(item.date);
      return itemDate >= cutoffDate;
    });
  };
  
  // Calculer le temps moyen de traitement
  const averageResolutionTime = timeSeriesData?.averageResolutionTime || 24; // Heures par défaut
  
  // Formater le temps moyen de traitement
  const formatResolutionTime = (hours: number) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)} ${t('common.minutes')}`;
    }
    if (hours < 24) {
      return `${Math.round(hours)} ${t('common.hours')}`;
    }
    return `${Math.round(hours / 24)} ${t('common.days')}`;
  };
  
  return (
    <FadeIn>
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <h2 className="text-xl font-semibold">{t('moderation.stats.title')}</h2>
        
        <div className="mt-4 md:mt-0 flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
          <div className="flex space-x-2">
            <button
              onClick={() => setTimeRange('7d')}
              className={`px-3 py-2 text-sm rounded-md ${
                timeRange === '7d'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              7 {t('common.days')}
            </button>
            
            <button
              onClick={() => setTimeRange('30d')}
              className={`px-3 py-2 text-sm rounded-md ${
                timeRange === '30d'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              30 {t('common.days')}
            </button>
            
            <button
              onClick={() => setTimeRange('90d')}
              className={`px-3 py-2 text-sm rounded-md ${
                timeRange === '90d'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              90 {t('common.days')}
            </button>
            
            <button
              onClick={() => setTimeRange('all')}
              className={`px-3 py-2 text-sm rounded-md ${
                timeRange === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {t('common.all')}
            </button>
          </div>
          
          <ExportDataButton
            data={{
              summary: {
                total: stats.total,
                pending: stats.pending,
                inReview: stats.inReview,
                approved: stats.approved,
                rejected: stats.rejected,
                escalated: stats.escalated,
                averageResolutionTime,
              },
              byContentType: contentTypeChartData,
              byDate: filterDateData(dateChartData),
            }}
            filename={`moderation_stats_${new Date().toISOString().split('T')[0]}`}
            title="Moderation Statistics"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('moderation.stats.total')}</h3>
          <div className="flex items-center">
            <div className="text-3xl font-bold">
              <AnimatedCounter value={stats.total} />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('moderation.stats.averageTime')}</h3>
          <div className="flex items-center">
            <div className="text-3xl font-bold">
              {formatResolutionTime(averageResolutionTime)}
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6 lg:col-span-1">
          <h3 className="text-lg font-medium mb-4">{t('moderation.stats.byStatus')}</h3>
          <MetricsChart
            data={statusChartData}
            dataKeys={['value']}
            xAxisDataKey="name"
            height={200}
            type="pie"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('moderation.stats.byContentType')}</h3>
          <MetricsChart
            data={contentTypeChartData.map(item => ({
              name: item.contentType,
              value: item.count,
            }))}
            dataKeys={['value']}
            xAxisDataKey="name"
            height={300}
            type="bar"
          />
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('moderation.stats.byDate')}</h3>
          <MetricsChart
            data={filterDateData(dateChartData)}
            dataKeys={['total']}
            xAxisDataKey="date"
            height={300}
          />
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-medium mb-4">{t('moderation.stats.byDate')}</h3>
        <MetricsChart
          data={filterDateData(dateChartData)}
          dataKeys={['pending', 'inReview', 'approved', 'rejected', 'escalated']}
          xAxisDataKey="date"
          height={400}
          type="bar"
          stacked={true}
        />
      </div>
    </FadeIn>
  );
};

export default ModerationStats;
