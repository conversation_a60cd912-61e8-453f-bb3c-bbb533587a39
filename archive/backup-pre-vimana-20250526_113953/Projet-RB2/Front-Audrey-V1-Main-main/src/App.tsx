import React, { lazy, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import HomePage from './pages/HomePage';
import ClientHomePage from './pages/ClientHomePage';
import ProfessionalHomePage from './pages/ProfessionalHomePage';
import StarBorderPage from './pages/StarBorderPage';
import RetreatFinderPage from './pages/RetreatFinderPage';
import SearchResultsPage from './pages/SearchResultsPage';
import HelpPage from './pages/HelpPage';
import AccountPage from './pages/AccountPage';
import AuthPage from './pages/AuthPage';
import ResetPasswordPage from './pages/auth/ResetPasswordPage';
import BlogPage from './pages/BlogPage';
import UserPreferencesPage from './pages/UserPreferencesPage';
import RecommendationAnalyticsPage from './pages/RecommendationAnalyticsPage';
import RecommendationTestingPage from './pages/RecommendationTestingPage';
import BlogPostPage from './pages/BlogPostPage';
import LivestreamPage from './pages/LivestreamPage';
import SocialAnalyticsPage from './pages/SocialAnalyticsPage';
import ContentManagementPage from './pages/ContentManagementPage';
import ContentAnalyticsPage from './pages/ContentAnalyticsPage';
import ContentSearchPage from './pages/ContentSearchPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ForgotPasswordPage from './pages/auth/ForgotPasswordPage';
import VerifyEmailPage from './pages/auth/VerifyEmailPage';
import RequestVerificationEmailPage from './pages/auth/RequestVerificationEmailPage';
import MessagingPage from './pages/MessagingPage';
import SecurityPage from './pages/SecurityPage';
import SecurityTrainingPage from './pages/SecurityTrainingPage';
import SecurityNotificationsPage from './pages/SecurityNotificationsPage';
import PartnerRegistrationPage from './pages/PartnerRegistrationPage';
import PartnerStatusPage from './pages/PartnerStatusPage';
import PartnerMatchingPage from './pages/PartnerMatchingPage';
import MatchingDetailsPage from './pages/MatchingDetailsPage';
import PartnerDashboardPage from './pages/PartnerDashboardPage';
import MatchingAnalyticsPage from './pages/admin/MatchingAnalyticsPage';
import MultiCriteriaRecommendationPage from './pages/MultiCriteriaRecommendationPage';
import ExplanationPreferencesPage from './pages/ExplanationPreferencesPage';
import AgentsDashboard from './pages/AgentsDashboard';

// Lazy-loaded admin pages
const PartnerListPage = lazy(() => import('./pages/admin/PartnerListPage'));
const PartnerManagementPage = lazy(() => import('./pages/admin/PartnerManagementPage'));
const ExplanationLearningPage = lazy(() => import('./pages/admin/ExplanationLearningPage'));
const ABTestingPage = lazy(() => import('./pages/admin/ABTestingPage'));
const ABTestResultsPage = lazy(() => import('./pages/admin/ABTestResultsPage'));
const ExplanationDashboardPage = lazy(() => import('./pages/admin/ExplanationDashboardPage'));
const AlertConfigPage = lazy(() => import('./pages/admin/AlertConfigPage'));
const ReinforcementLearningPage = lazy(() => import('./pages/admin/ReinforcementLearningPage'));
const ExplanationTranslationPage = lazy(() => import('./pages/admin/ExplanationTranslationPage'));
const ExplanationAnalyticsPage = lazy(() => import('./pages/admin/ExplanationAnalyticsPage'));
const FairnessDashboardPage = lazy(() => import('./pages/admin/FairnessDashboardPage'));
const DiscoverPage = lazy(() => import('./pages/DiscoverPage'));
const ContextualRecommendationPage = lazy(() => import('./pages/ContextualRecommendationPage'));
const SeasonalRecommendationPage = lazy(() => import('./pages/SeasonalRecommendationPage'));
const SocialRecommendationPage = lazy(() => import('./pages/SocialRecommendationPage'));
import MessagingGuard from './components/messaging/MessagingGuard';
import MessagingNotificationInitializer from './components/messaging/MessagingNotificationInitializer';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { SecurityProvider } from './contexts/SecurityContext';
import TranslationProvider from './providers/TranslationProvider';
import ProtectedRoute from './components/auth/ProtectedRoute';
import CSPViolationMonitor from './components/security/CSPViolationMonitor';
import './styles/globals.css';

const App: React.FC = () => {
  return (
    <HelmetProvider context={{}}>
      <NotificationProvider>
        <AuthProvider>
          <SecurityProvider>
            <TranslationProvider>
              <Router>
                <Routes>
                  {/* Routes publiques */}
                  <Route path='/' element={<HomePage />} />
                  <Route path='/login' element={<LoginPage />} />
                  <Route path='/register' element={<RegisterPage />} />
                  <Route path='/forgot-password' element={<ForgotPasswordPage />} />
                  <Route path='/reset-password/:token' element={<ResetPasswordPage />} />
                  <Route path='/verify-email/:token' element={<VerifyEmailPage />} />
                  <Route path='/request-verification-email' element={<RequestVerificationEmailPage />} />
                  <Route path='/auth' element={<AuthPage />} />
                  <Route path='/aide' element={<HelpPage />} />
                  <Route path='/blog' element={<BlogPage />} />
                  <Route path='/blog/:category' element={<BlogPage />} />
                  <Route path='/blog/post/:id' element={<BlogPostPage />} />
                  <Route path='/multi-criteria' element={<MultiCriteriaRecommendationPage />} />
                  <Route path='/livestream/:id' element={<LivestreamPage />} />
                  <Route path='/social/analytics' element={<SocialAnalyticsPage />} />
                  <Route path='/content-management' element={<ContentManagementPage />} />
                  <Route path='/content-analytics' element={<ContentAnalyticsPage />} />
                  <Route path='/content-search' element={<ContentSearchPage />} />
                  <Route path='/become-partner' element={<PartnerRegistrationPage />} />
                  <Route path='/partner-status/:id' element={<PartnerStatusPage />} />
                  <Route path='/partners/matching' element={<PartnerMatchingPage />} />
                  <Route path='/partners/matching/:retreatId' element={<PartnerMatchingPage />} />
                  <Route path='/matching/details/:partnerId/:retreatId' element={<MatchingDetailsPage />} />

                  {/* Routes d'administration */}
                  <Route path='/admin/partners' element={
                    <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                      <PartnerListPage />
                    </Suspense>
                  } />
                  <Route path='/admin/partners/:id' element={
                    <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                      <PartnerManagementPage />
                    </Suspense>
                  } />

                  {/* Routes protégées */}
                  <Route
                    path='/client'
                    element={
                      <ProtectedRoute>
                        <ClientHomePage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/professional'
                    element={
                      <ProtectedRoute requiredRole='professional'>
                        <ProfessionalHomePage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/explanation-preferences'
                    element={
                      <ProtectedRoute>
                        <ExplanationPreferencesPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/preferences'
                    element={
                      <ProtectedRoute>
                        <UserPreferencesPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/analytics/recommendations'
                    element={
                      <ProtectedRoute>
                        <RecommendationAnalyticsPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/testing/recommendations'
                    element={
                      <ProtectedRoute>
                        <RecommendationTestingPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/partner/dashboard'
                    element={
                      <ProtectedRoute requiredRole='professional'>
                        <PartnerDashboardPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/star-border'
                    element={
                      <ProtectedRoute>
                        <StarBorderPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/find-Ideal-Retreat'
                    element={
                      <ProtectedRoute>
                        <RetreatFinderPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/results'
                    element={
                      <ProtectedRoute>
                        <SearchResultsPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/discover'
                    element={
                      <ProtectedRoute>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <DiscoverPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/contextual'
                    element={
                      <ProtectedRoute>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <ContextualRecommendationPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/seasonal/:season'
                    element={
                      <ProtectedRoute>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <SeasonalRecommendationPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/current-season'
                    element={
                      <ProtectedRoute>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <SeasonalRecommendationPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/next-season'
                    element={
                      <Navigate to="/seasonal/next" replace />
                    }
                  />
                  <Route
                    path='/social'
                    element={
                      <ProtectedRoute>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <SocialRecommendationPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/compte'
                    element={
                      <ProtectedRoute>
                        <AccountPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/messaging'
                    element={
                      <MessagingGuard>
                        <MessagingPage />
                      </MessagingGuard>
                    }
                  />
                  <Route
                    path='/messaging/:conversationId'
                    element={
                      <MessagingGuard>
                        <MessagingPage />
                      </MessagingGuard>
                    }
                  />
                  <Route
                    path='/admin/security'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <SecurityPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/matching-analytics'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <MatchingAnalyticsPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/explanation-learning'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <ExplanationLearningPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/ab-testing'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <ABTestingPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/ab-testing/:id/results'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <ABTestResultsPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/explanation-dashboard'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <ExplanationDashboardPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/alert-config'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <AlertConfigPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/reinforcement-learning'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <ReinforcementLearningPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/explanation-translation'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <ExplanationTranslationPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/explanation-analytics'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <ExplanationAnalyticsPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/fairness-dashboard'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <Suspense fallback={<div className="flex justify-center items-center h-screen">Chargement...</div>}>
                          <FairnessDashboardPage />
                        </Suspense>
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/admin/agents'
                    element={
                      <ProtectedRoute requiredRole='admin'>
                        <AgentsDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/security/training'
                    element={
                      <ProtectedRoute>
                        <SecurityTrainingPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path='/security/notifications'
                    element={
                      <ProtectedRoute>
                        <SecurityNotificationsPage />
                      </ProtectedRoute>
                    }
                  />
                </Routes>

                {/* Moniteur des violations CSP (visible uniquement en développement) */}
                <CSPViolationMonitor />

                {/* Initialisation des notifications de messagerie */}
                <MessagingNotificationInitializer />
              </Router>
            </TranslationProvider>
          </SecurityProvider>
        </AuthProvider>
      </NotificationProvider>
    </HelmetProvider>
  );
};

export default App;
