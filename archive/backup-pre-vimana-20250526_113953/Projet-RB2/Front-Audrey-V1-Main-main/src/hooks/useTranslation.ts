import { useState, useEffect, useCallback } from 'react';
import { i18nService, SupportedLanguage, t } from '../services/i18n/i18nService';

/**
 * Hook pour utiliser les traductions dans les composants React
 * @returns Fonctions et données pour l'internationalisation
 */
export const useTranslation = () => {
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(
    i18nService.getLanguage()
  );
  const [isReady, setIsReady] = useState<boolean>(i18nService.isReady());

  // Mettre à jour l'état local lorsque la langue change
  useEffect(() => {
    const unsubscribe = i18nService.addListener((language) => {
      setCurrentLanguage(language);
    });

    // Vérifier périodiquement si le service est initialisé
    if (!isReady) {
      const interval = setInterval(() => {
        if (i18nService.isReady()) {
          setIsReady(true);
          clearInterval(interval);
        }
      }, 100);

      return () => {
        clearInterval(interval);
        unsubscribe();
      };
    }

    return unsubscribe;
  }, [isReady]);

  /**
   * Changer la langue
   * @param language Nouvelle langue
   */
  const changeLanguage = useCallback((language: SupportedLanguage) => {
    i18nService.setLanguage(language);
  }, []);

  /**
   * Formater une date selon la langue actuelle
   * @param date Date à formater
   * @param options Options de formatage
   * @returns Date formatée
   */
  const formatDate = useCallback(
    (date: Date, options?: Intl.DateTimeFormatOptions) => {
      return new Intl.DateTimeFormat(currentLanguage, options).format(date);
    },
    [currentLanguage]
  );

  /**
   * Formater un nombre selon la langue actuelle
   * @param number Nombre à formater
   * @param options Options de formatage
   * @returns Nombre formaté
   */
  const formatNumber = useCallback(
    (number: number, options?: Intl.NumberFormatOptions) => {
      return new Intl.NumberFormat(currentLanguage, options).format(number);
    },
    [currentLanguage]
  );

  /**
   * Formater une devise selon la langue actuelle
   * @param amount Montant à formater
   * @param currency Code de la devise (ex: EUR, USD)
   * @param options Options de formatage
   * @returns Montant formaté
   */
  const formatCurrency = useCallback(
    (amount: number, currency: string, options?: Intl.NumberFormatOptions) => {
      return new Intl.NumberFormat(currentLanguage, {
        style: 'currency',
        currency,
        ...options,
      }).format(amount);
    },
    [currentLanguage]
  );

  /**
   * Formater une durée relative selon la langue actuelle
   * @param value Valeur de la durée
   * @param unit Unité de la durée
   * @returns Durée formatée
   */
  const formatRelativeTime = useCallback(
    (value: number, unit: Intl.RelativeTimeFormatUnit) => {
      const rtf = new Intl.RelativeTimeFormat(currentLanguage, { numeric: 'auto' });
      return rtf.format(value, unit);
    },
    [currentLanguage]
  );

  return {
    t, // Fonction de traduction
    isReady, // Indique si le service est initialisé
    currentLanguage, // Langue actuelle
    changeLanguage, // Fonction pour changer la langue
    formatDate, // Fonction pour formater une date
    formatNumber, // Fonction pour formater un nombre
    formatCurrency, // Fonction pour formater une devise
    formatRelativeTime, // Fonction pour formater une durée relative
  };
};

export default useTranslation;
