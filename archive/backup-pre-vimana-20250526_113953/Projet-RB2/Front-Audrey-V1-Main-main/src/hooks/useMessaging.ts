import { useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { API_URL } from '../services/api/apiConfig';
import { getAuthToken } from '../utils/authUtils';
import * as messagingService from '../services/api/messagingService';
import {
  IConversation,
  IMessage,
  CreateMessageDto,
  UpdateMessageDto,
} from '../services/api/messagingService';

interface SocketResponse<T = undefined> {
  success: boolean;
  message?: T;
  error?: string;
}

interface MessagingHook {
  conversations: IConversation[];
  currentConversation: IConversation | null;
  messages: IMessage[];
  loading: boolean;
  error: string | null;
  unreadCount: number;
  isConnected: boolean;
  typingUsers: Record<string, string[]>;
  loadConversations: () => Promise<IConversation[]>;
  loadMessages: (
    conversationId: string,
    options?: { limit?: number; before?: string }
  ) => Promise<IMessage[]>;
  sendMessage: (data: CreateMessageDto) => Promise<IMessage | null>;
  updateMessage: (id: string, data: UpdateMessageDto) => Promise<IMessage | null>;
  deleteMessage: (id: string) => Promise<boolean>;
  markAsRead: (conversationId: string) => Promise<void>;
  createConversation: (
    data: messagingService.CreateConversationDto
  ) => Promise<IConversation | null>;
  addParticipant: (conversationId: string, participantId: string) => Promise<IConversation | null>;
  removeParticipant: (
    conversationId: string,
    participantId: string
  ) => Promise<IConversation | null>;
  addReaction: (messageId: string, emoji: string) => Promise<IMessage | null>;
  setTyping: (conversationId: string, isTyping: boolean) => void;
  searchMessages: (query: string) => Promise<IMessage[]>;
}

export const useMessaging = (): MessagingHook => {
  const [conversations, setConversations] = useState<IConversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<IConversation | null>(null);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [typingUsers, setTypingUsers] = useState<Record<string, string[]>>({});

  const socketRef = useRef<Socket | null>(null);

  // Load overall unread count
  const loadUnreadCount = useCallback(async () => {
    try {
      const count = await messagingService.getUnreadCount();
      setUnreadCount(count);
    } catch (err: unknown) {
      // Silently fail for now, or add specific logging
      console.error('Failed to load unread count:', err);
    }
  }, []);

  // Helper function to update a conversation with a new message
  const updateConversationWithMessage = useCallback((message: IMessage) => {
    setConversations((prev) => {
      const existingConv = prev.find((c) => c.id === message.conversationId);
      let newUnreadCount = existingConv?.unreadCount || 0;
      // This logic might need adjustment based on where user ID is available.
      if (existingConv && message.sender && message.sender.id !== getAuthToken()) {
        // Placeholder for actual user ID check
        newUnreadCount = (existingConv.unreadCount || 0) + 1;
      }

      const updatedConversations = prev.map((conv) => {
        if (conv.id === message.conversationId) {
          return {
            ...conv,
            messages: [message], // Should ideally be just the latest message snippet
            lastMessage: message.sentAt,
            unreadCount: newUnreadCount,
          };
        }
        return conv;
      });

      // Sort conversations by last message date
      return updatedConversations.sort((a, b) => {
        const dateA = a.lastMessage ? new Date(a.lastMessage).getTime() : 0;
        const dateB = b.lastMessage ? new Date(b.lastMessage).getTime() : 0;
        return dateB - dateA;
      });
    });
  }, []);

  // Mark a conversation as read
  const markAsRead = useCallback(
    async (conversationId: string) => {
      try {
        await messagingService.markConversationAsRead(conversationId);
        // Optimistically update unread count and conversation state
        setConversations((prev) =>
          prev.map((conv) => (conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv))
        );
        if (currentConversation?.id === conversationId) {
          setCurrentConversation((prev) => (prev ? { ...prev, unreadCount: 0 } : null));
        }
        loadUnreadCount(); // Reload overall unread count
      } catch (err: unknown) {
        if (err instanceof Error) {
          console.error('Failed to mark conversation as read:', err.message);
        } else {
          console.error('An unknown error occurred while marking conversation as read');
        }
      }
    },
    [loadUnreadCount, currentConversation?.id]
  );

  // Initialize socket connection
  useEffect(() => {
    const token = getAuthToken();
    if (!token) return;

    socketRef.current = io(`${API_URL}/messaging`, {
      auth: {
        token,
      },
    });

    socketRef.current.on('connect', () => {
      setIsConnected(true);
    });

    socketRef.current.on('disconnect', () => {
      setIsConnected(false);
    });

    socketRef.current.on('message:created', (message: IMessage) => {
      // Add message to the current conversation if it belongs there
      if (currentConversation && message.conversationId === currentConversation.id) {
        setMessages((prev) => [...prev, message]);

        // Mark as read if we're currently viewing this conversation
        markAsRead(message.conversationId);
      }

      // Update conversation list to show the latest message
      updateConversationWithMessage(message);
    });

    socketRef.current.on('message:updated', (message: IMessage) => {
      // Update message in the current conversation if it belongs there
      if (currentConversation && message.conversationId === currentConversation.id) {
        setMessages((prev) => prev.map((m) => (m.id === message.id ? message : m)));
      }
    });

    socketRef.current.on('message:deleted', ({ id }: { id: string }) => {
      // Remove message from the current conversation
      setMessages((prev) => prev.filter((m) => m.id !== id));
    });

    socketRef.current.on(
      'message:read',
      ({ conversationId, userId }: { conversationId: string; userId: string }) => {
        // Update read status for messages in this conversation
        if (currentConversation && conversationId === currentConversation.id) {
          setMessages((prev) =>
            prev.map((m) => {
              if (m.senderId === userId && m.status !== 'READ') {
                return { ...m, status: 'READ', readAt: new Date().toISOString() };
              }
              return m;
            })
          );
        }

        // Update unread count
        loadUnreadCount();
      }
    );

    socketRef.current.on(
      'conversation:typing',
      ({
        conversationId,
        userId,
        isTyping,
      }: {
        conversationId: string;
        userId: string;
        isTyping: boolean;
      }) => {
        setTypingUsers((prev) => {
          const conversationTypers = prev[conversationId] || [];

          if (isTyping && !conversationTypers.includes(userId)) {
            return {
              ...prev,
              [conversationId]: [...conversationTypers, userId],
            };
          } else if (!isTyping && conversationTypers.includes(userId)) {
            return {
              ...prev,
              [conversationId]: conversationTypers.filter((id) => id !== userId),
            };
          }

          return prev;
        });
      }
    );

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [currentConversation, loadUnreadCount, markAsRead, updateConversationWithMessage]);

  // Load unread count on mount
  useEffect(() => {
    loadUnreadCount();
  }, [loadUnreadCount]);

  // Load all conversations
  const loadConversations = useCallback(async (): Promise<IConversation[]> => {
    try {
      setLoading(true);
      const data = await messagingService.getRecentConversations();
      setConversations(data);
      return data;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Failed to load conversations');
      } else {
        setError('An unknown error occurred while loading conversations');
      }
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Load messages for a conversation
  const loadMessages = useCallback(
    async (
      conversationId: string,
      options?: { limit?: number; before?: string }
    ): Promise<IMessage[]> => {
      try {
        setLoading(true);
        const conversation = await messagingService.getConversation(conversationId);
        setCurrentConversation(conversation);
        const messagesData = await messagingService.getMessages(conversationId, options);
        setMessages(messagesData);
        if (socketRef.current) {
          socketRef.current.emit('conversation:join', { conversationId });
        }
        await markAsRead(conversationId);
        return messagesData;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message || 'Failed to load messages');
        } else {
          setError('An unknown error occurred while loading messages');
        }
        return [];
      } finally {
        setLoading(false);
      }
    },
    [markAsRead]
  );

  // Send a message
  const sendMessage = useCallback(
    async (data: CreateMessageDto): Promise<IMessage | null> => {
      try {
        if (socketRef.current && isConnected) {
          return new Promise((resolve) => {
            socketRef.current!.emit(
              'message:create',
              data,
              (response: SocketResponse<IMessage>) => {
                if (response.success && response.message) {
                  resolve(response.message);
                } else {
                  setError(response.error || 'Failed to send message via socket');
                  resolve(null);
                }
              }
            );
          });
        } else {
          const message = await messagingService.createMessage(data);
          if (currentConversation && message.conversationId === currentConversation.id) {
            setMessages((prev) => [...prev, message]);
          }
          updateConversationWithMessage(message);
          return message;
        }
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message || 'Failed to send message');
        } else {
          setError('An unknown error occurred while sending the message');
        }
        return null;
      }
    },
    [isConnected, currentConversation, updateConversationWithMessage]
  );

  // Update a message
  const updateMessage = useCallback(
    async (id: string, data: UpdateMessageDto): Promise<IMessage | null> => {
      try {
        if (socketRef.current && isConnected) {
          return new Promise((resolve) => {
            socketRef.current!.emit(
              'message:update',
              { id, dto: data },
              (response: SocketResponse<IMessage>) => {
                if (response.success && response.message) {
                  resolve(response.message);
                } else {
                  setError(response.error || 'Failed to update message via socket');
                  resolve(null);
                }
              }
            );
          });
        } else {
          const message = await messagingService.updateMessage(id, data);
          if (currentConversation && message.conversationId === currentConversation.id) {
            setMessages((prev) => prev.map((m) => (m.id === message.id ? message : m)));
          }
          // Optionally update conversation list if last message changed
          return message;
        }
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message || 'Failed to update message');
        } else {
          setError('An unknown error occurred while updating the message');
        }
        return null;
      }
    },
    [isConnected, currentConversation]
  );

  // Delete a message
  const deleteMessage = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        if (socketRef.current && isConnected) {
          return new Promise((resolve) => {
            socketRef.current!.emit('message:delete', { id }, (response: SocketResponse) => {
              if (response.success) {
                resolve(true);
              } else {
                setError(response.error || 'Failed to delete message via socket');
                resolve(false);
              }
            });
          });
        } else {
          await messagingService.deleteMessage(id);
          setMessages((prev) => prev.filter((m) => m.id !== id));
          // Optionally update conversation list if last message changed
          return true;
        }
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message || 'Failed to delete message');
        } else {
          setError('An unknown error occurred while deleting the message');
        }
        return false;
      }
    },
    [isConnected]
  );

  // Create a new conversation
  const createConversation = useCallback(
    async (data: messagingService.CreateConversationDto): Promise<IConversation | null> => {
      try {
        setLoading(true);
        const conversation = await messagingService.createConversation(data);
        setConversations((prev) =>
          [conversation, ...prev].sort((a, b) => {
            const dateA = a.lastMessage ? new Date(a.lastMessage).getTime() : 0;
            const dateB = b.lastMessage ? new Date(b.lastMessage).getTime() : 0;
            return dateB - dateA;
          })
        );
        setCurrentConversation(conversation);
        return conversation;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message || 'Failed to create conversation');
        } else {
          setError('An unknown error occurred while creating the conversation');
        }
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Add participant to a conversation
  const addParticipant = useCallback(
    async (conversationId: string, participantId: string): Promise<IConversation | null> => {
      try {
        const conversation = await messagingService.addParticipant(conversationId, participantId);
        setConversations((prev) =>
          prev
            .map((c) => (c.id === conversationId ? conversation : c))
            .sort((a, b) => {
              const dateA = a.lastMessage ? new Date(a.lastMessage).getTime() : 0;
              const dateB = b.lastMessage ? new Date(b.lastMessage).getTime() : 0;
              return dateB - dateA;
            })
        );
        if (currentConversation && currentConversation.id === conversationId) {
          setCurrentConversation(conversation);
        }
        return conversation;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message || 'Failed to add participant');
        } else {
          setError('An unknown error occurred while adding participant');
        }
        return null;
      }
    },
    [currentConversation]
  );

  // Remove participant from a conversation
  const removeParticipant = useCallback(
    async (conversationId: string, participantId: string): Promise<IConversation | null> => {
      try {
        const conversation = await messagingService.removeParticipant(
          conversationId,
          participantId
        );
        setConversations((prev) =>
          prev
            .map((c) => (c.id === conversationId ? conversation : c))
            .sort((a, b) => {
              const dateA = a.lastMessage ? new Date(a.lastMessage).getTime() : 0;
              const dateB = b.lastMessage ? new Date(b.lastMessage).getTime() : 0;
              return dateB - dateA;
            })
        );
        if (currentConversation && currentConversation.id === conversationId) {
          // If current user is removed, currentConversation might need to be nulled
          const amIRemoved = !conversation.participants.some((p) => p.id === getAuthToken()); // Placeholder
          if (amIRemoved) {
            setCurrentConversation(null);
            // Navigate away or show a message
          } else {
            setCurrentConversation(conversation);
          }
        }
        return conversation;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message || 'Failed to remove participant');
        } else {
          setError('An unknown error occurred while removing participant');
        }
        return null;
      }
    },
    [currentConversation]
  );

  // Add reaction to a message
  const addReaction = useCallback(
    async (messageId: string, emoji: string): Promise<IMessage | null> => {
      try {
        if (socketRef.current && isConnected) {
          return new Promise((resolve) => {
            // Assuming 'message:react' is the event the server listens for
            // and 'message:reacted' or similar is what it emits back, handled by general listeners.
            socketRef.current!.emit(
              'message:react',
              { messageId, emoji },
              (response: SocketResponse<IMessage>) => {
                if (response.success && response.message) {
                  // Optimistically update UI or rely on broadcast event 'message:updated'
                  setMessages((prev) =>
                    prev.map((m) => (m.id === messageId ? response.message! : m))
                  );
                  resolve(response.message);
                } else {
                  setError(response.error || 'Failed to add reaction via socket');
                  resolve(null);
                }
              }
            );
          });
        } else {
          // Fallback to REST API
          const updatedMessage = await messagingService.addReaction(messageId, emoji); // This service returns 'any' currently
          // Cast or ensure service returns IMessage
          const returnedMessage = updatedMessage as IMessage;

          setMessages((prev) => prev.map((m) => (m.id === messageId ? returnedMessage : m)));
          return returnedMessage;
        }
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message || 'Failed to add reaction');
        } else {
          setError('An unknown error occurred while adding reaction');
        }
        return null;
      }
    },
    [isConnected]
  );

  // Set typing status
  const setTyping = useCallback(
    (conversationId: string, isTyping: boolean) => {
      if (socketRef.current && isConnected) {
        socketRef.current.emit('conversation:typing', { conversationId, isTyping });
      }
    },
    [isConnected]
  );

  // Search messages
  const searchMessages = useCallback(async (query: string): Promise<IMessage[]> => {
    try {
      setLoading(true);
      const results = await messagingService.searchMessages(query);
      return results;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Failed to search messages');
      } else {
        setError('An unknown error occurred while searching messages');
      }
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    conversations,
    currentConversation,
    messages,
    loading,
    error,
    unreadCount,
    isConnected,
    typingUsers,
    loadConversations,
    loadMessages,
    sendMessage,
    updateMessage,
    deleteMessage,
    markAsRead,
    createConversation,
    addParticipant,
    removeParticipant,
    addReaction,
    setTyping,
    searchMessages,
  };
};
