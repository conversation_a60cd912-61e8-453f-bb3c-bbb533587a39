import { useState, useCallback, useEffect } from 'react';
import { userService, User, UserProfile, UpdateProfileRequest } from '../../services/api';

interface UseUserReturn {
  profile: User | null;
  detailedProfile: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  getProfile: () => Promise<User>;
  getDetailedProfile: () => Promise<UserProfile>;
  updateProfile: (profileData: UpdateProfileRequest) => Promise<User>;
  uploadProfilePicture: (file: File) => Promise<string>;
  getUserById: (userId: string) => Promise<User>;
  getUserProfileById: (userId: string) => Promise<UserProfile>;
}

export const useUser = (): UseUserReturn => {
  const [profile, setProfile] = useState<User | null>(null);
  const [detailedProfile, setDetailedProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const getProfile = useCallback(async (): Promise<User> => {
    setIsLoading(true);
    setError(null);
    try {
      const userData = await userService.getProfile();
      setProfile(userData);
      return userData;
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la récupération du profil');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getDetailedProfile = useCallback(async (): Promise<UserProfile> => {
    setIsLoading(true);
    setError(null);
    try {
      const profileData = await userService.getDetailedProfile();
      setDetailedProfile(profileData);
      return profileData;
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la récupération du profil détaillé');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateProfile = useCallback(async (profileData: UpdateProfileRequest): Promise<User> => {
    setIsLoading(true);
    setError(null);
    try {
      const updatedProfile = await userService.updateProfile(profileData);
      setProfile(updatedProfile);
      return updatedProfile;
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la mise à jour du profil');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const uploadProfilePicture = useCallback(
    async (file: File): Promise<string> => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await userService.uploadProfilePicture(file);

        // Mettre à jour le profil avec la nouvelle URL d'image
        if (profile) {
          setProfile({
            ...profile,
            profilePicture: response.url,
          });
        }

        return response.url;
      } catch (err: any) {
        setError(
          err.message || "Une erreur est survenue lors du téléchargement de l'image de profil"
        );
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [profile]
  );

  const getUserById = useCallback(async (userId: string): Promise<User> => {
    setIsLoading(true);
    setError(null);
    try {
      return await userService.getUserById(userId);
    } catch (err: any) {
      setError(err.message || "Une erreur est survenue lors de la récupération de l'utilisateur");
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getUserProfileById = useCallback(async (userId: string): Promise<UserProfile> => {
    setIsLoading(true);
    setError(null);
    try {
      return await userService.getUserProfileById(userId);
    } catch (err: any) {
      setError(
        err.message || "Une erreur est survenue lors de la récupération du profil de l'utilisateur"
      );
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Charger le profil au montage du composant
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token && !profile) {
      getProfile().catch((err) => {
        console.error('Erreur lors du chargement du profil:', err);
      });
    }
  }, [getProfile, profile]);

  return {
    profile,
    detailedProfile,
    isLoading,
    error,
    getProfile,
    getDetailedProfile,
    updateProfile,
    uploadProfilePicture,
    getUserById,
    getUserProfileById,
  };
};
