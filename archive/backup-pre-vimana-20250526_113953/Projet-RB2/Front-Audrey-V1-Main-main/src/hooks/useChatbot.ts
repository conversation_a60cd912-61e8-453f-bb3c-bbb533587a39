import { useState, useCallback, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useAuth } from '../contexts/AuthContext';
import * as chatbotService from '../services/api/chatbotService';
import * as chatbotStorage from '../services/chatbotStorageService';
import {
  ChatbotError,
  ChatbotErrorType,
  MessageType,
  Button,
} from '../services/api/chatbotService';

// Message interface
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  messageType?: MessageType;
  buttons?: Button[];
  metadata?: Record<string, unknown>;
}

// Conversation interface
export interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

// Analytics event types
export enum AnalyticsEventType {
  MESSAGE_SENT = 'message_sent',
  MESSAGE_RECEIVED = 'message_received',
  CONVERSATION_STARTED = 'conversation_started',
  CONVERSATION_ENDED = 'conversation_ended',
  BUTTON_CLICKED = 'button_clicked',
  ERROR_OCCURRED = 'error_occurred',
  FEEDBACK_GIVEN = 'feedback_given',
}

// Analytics event interface
export interface AnalyticsEvent {
  type: AnalyticsEventType;
  timestamp: Date;
  data: Record<string, unknown>;
}

// Chatbot hook return type
interface UseChatbotReturn {
  messages: ChatMessage[];
  conversations: Conversation[];
  currentConversation: Conversation | null;
  isLoading: boolean;
  error: string | null;
  isAvailable: boolean;
  isSpeechRecognitionAvailable: boolean;
  isListening: boolean;
  sendMessage: (content: string) => Promise<void>;
  sendVoiceMessage: () => void;
  stopVoiceMessage: () => void;
  clearMessages: () => void;
  startNewConversation: (title?: string) => void;
  loadConversation: (conversationId: string) => void;
  deleteConversation: (conversationId: string) => void;
  handleButtonClick: (button: Button) => void;
  giveFeedback: (messageId: string, isPositive: boolean) => void;
}

/**
 * Custom hook for interacting with the chatbot
 *
 * @returns Chatbot state and methods
 */
export const useChatbot = (): UseChatbotReturn => {
  // Initial welcome message
  const initialMessages: ChatMessage[] = [
    {
      id: uuidv4(),
      role: 'assistant',
      content:
        "Bonjour ! Je suis l'assistant Retreat And Be. Comment puis-je vous aider aujourd'hui ?",
      timestamp: new Date(),
      messageType: MessageType.TEXT,
    },
  ];

  // State
  const [messages, setMessages] = useState<ChatMessage[]>(initialMessages);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isAvailable, setIsAvailable] = useState<boolean>(true);
  const [conversationId, setConversationId] = useState<string | undefined>(undefined);

  // Voice recognition
  const [isListening, setIsListening] = useState<boolean>(false);
  const [isSpeechRecognitionAvailable, setIsSpeechRecognitionAvailable] = useState<boolean>(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const transcriptRef = useRef<string>('');

  // Auth
  const { user } = useAuth();
  const userId = user?.id || 'anonymous';

  // Analytics
  const trackEvent = useCallback(
    (type: AnalyticsEventType, data: Record<string, unknown> = {}) => {
      const event: AnalyticsEvent = {
        type,
        timestamp: new Date(),
        data: {
          ...data,
          userId,
          conversationId,
        },
      };

      // In a real implementation, this would send the event to an analytics service
      console.log('Analytics event:', event);

      // You could implement a real analytics service call here
      // analyticsService.trackEvent(event);
    },
    [userId, conversationId]
  );

  // Load conversations from storage on mount
  useEffect(() => {
    const loadStoredConversations = () => {
      const storedConversations = chatbotStorage.getStoredConversations();

      if (storedConversations.length > 0) {
        const conversationsResult: Conversation[] = storedConversations.map(
          (stored: chatbotStorage.StoredConversation) => ({
            id: stored.id,
            title: stored.title,
            messages: stored.messages.map(
              (msg: chatbotStorage.StoredMessage): ChatMessage => ({
                id: msg.id,
                role: msg.role,
                content: msg.content,
                timestamp: new Date(msg.timestamp),
                messageType: msg.messageType as MessageType,
                buttons: undefined,
                metadata: msg.metadata,
              })
            ),
            createdAt: new Date(stored.createdAt),
            updatedAt: new Date(stored.updatedAt),
          })
        );

        setConversations(conversationsResult);

        // Load current conversation if available
        const currentId = chatbotStorage.getCurrentConversationId();
        if (currentId) {
          const current = conversationsResult.find((conv) => conv.id === currentId);
          if (current) {
            setCurrentConversation(current);
            setMessages(current.messages);
            setConversationId(current.id);
          }
        }
      } else {
        // Create a new conversation if none exists
        startNewConversation('Nouvelle conversation');
      }
    };

    loadStoredConversations();

    // Check if speech recognition is available
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      setIsSpeechRecognitionAvailable(true);
    }
  }, []);

  // Check if the chatbot service is available
  useEffect(() => {
    const checkHealth = async () => {
      try {
        const isHealthy = await chatbotService.checkChatbotHealth();
        setIsAvailable(isHealthy);
        if (!isHealthy) {
          setError('Le service de chatbot est actuellement indisponible.');
        }
      } catch (_err) {
        setIsAvailable(false);
        setError('Impossible de vérifier l&apos;état du service de chatbot.');
      }
    };

    checkHealth();
  }, []);

  // Start a new conversation
  const startNewConversation = useCallback(
    (title: string = 'Nouvelle conversation') => {
      const storedConversation = chatbotStorage.createConversation(title, initialMessages);

      const newConversation: Conversation = {
        id: storedConversation.id,
        title: storedConversation.title,
        messages: initialMessages,
        createdAt: new Date(storedConversation.createdAt),
        updatedAt: new Date(storedConversation.updatedAt),
      };

      setConversations((prev) => [newConversation, ...prev]);
      setCurrentConversation(newConversation);
      setMessages(initialMessages);
      setConversationId(newConversation.id);
      setError(null);

      trackEvent(AnalyticsEventType.CONVERSATION_STARTED, { conversationId: newConversation.id });

      return newConversation;
    },
    [initialMessages, trackEvent]
  );

  // Load an existing conversation
  const loadConversation = useCallback((conversationId: string) => {
    const storedConversation = chatbotStorage.getStoredConversation(conversationId);

    if (!storedConversation) {
      setError('Conversation introuvable');
      return;
    }

    const conversation: Conversation = {
      id: storedConversation.id,
      title: storedConversation.title,
      messages: storedConversation.messages.map((msg) => ({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        timestamp: new Date(msg.timestamp),
        messageType: msg.messageType as MessageType,
        metadata: msg.metadata,
      })),
      createdAt: new Date(storedConversation.createdAt),
      updatedAt: new Date(storedConversation.updatedAt),
    };

    setCurrentConversation(conversation);
    setMessages(conversation.messages);
    setConversationId(conversation.id);
    chatbotStorage.setCurrentConversationId(conversation.id);
    setError(null);
  }, []);

  // Delete a conversation
  const deleteConversation = useCallback(
    (conversationId: string) => {
      const success = chatbotStorage.deleteConversation(conversationId);

      if (success) {
        setConversations((prev) => prev.filter((conv) => conv.id !== conversationId));

        if (currentConversation?.id === conversationId) {
          // If we deleted the current conversation, start a new one
          startNewConversation();
        }

        trackEvent(AnalyticsEventType.CONVERSATION_ENDED, { conversationId });
      }

      return success;
    },
    [currentConversation, startNewConversation, trackEvent]
  );

  // Send a message to the chatbot
  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim()) return;

      // Add user message to the chat
      const userMessage: ChatMessage = {
        id: uuidv4(),
        role: 'user',
        content,
        timestamp: new Date(),
        messageType: MessageType.TEXT,
      };

      setMessages((prev) => [...prev, userMessage]);
      setIsLoading(true);
      setError(null);

      // Store the message
      if (conversationId) {
        chatbotStorage.addMessageToConversation(conversationId, {
          role: userMessage.role,
          content: userMessage.content,
          timestamp: userMessage.timestamp,
          messageType: userMessage.messageType,
        });
      }

      trackEvent(AnalyticsEventType.MESSAGE_SENT, {
        messageId: userMessage.id,
        content: userMessage.content,
      });

      try {
        // Convert messages to the format expected by the API
        const apiMessages = messages
          .slice(-5) // Only send the last 5 messages for context
          .map((msg) => ({
            role: msg.role,
            content: msg.content,
            messageType: msg.messageType,
          }));

        // Add the new user message
        apiMessages.push({
          role: 'user',
          content,
          messageType: MessageType.TEXT,
        });

        // Send the message to the chatbot service
        const response = await chatbotService.sendChatMessage(apiMessages, userId, conversationId);

        // If we get a conversation ID back, save it for future messages
        if (response.conversation_id && !conversationId) {
          setConversationId(response.conversation_id);

          // If we don't have a current conversation, create one
          if (!currentConversation) {
            const newConversation = startNewConversation();
            setConversationId(newConversation.id);
          }
        }

        // Add the assistant's response to the chat
        const assistantMessage: ChatMessage = {
          id: uuidv4(),
          role: 'assistant',
          content: response.content,
          timestamp: new Date(),
          messageType: response.messageType || MessageType.TEXT,
          buttons: response.buttons,
          metadata: response.metadata as Record<string, unknown> | undefined,
        };

        setMessages((prev) => [...prev, assistantMessage]);

        // Store the response
        if (conversationId) {
          chatbotStorage.addMessageToConversation(conversationId, {
            role: assistantMessage.role,
            content: assistantMessage.content,
            timestamp: assistantMessage.timestamp,
            messageType: assistantMessage.messageType,
            metadata: assistantMessage.metadata as Record<string, unknown> | undefined,
          });

          // Update conversation list
          setConversations((prev) => {
            const updated = [...prev];
            const index = updated.findIndex((conv) => conv.id === conversationId);

            if (index !== -1) {
              updated[index] = {
                ...updated[index],
                messages: [...updated[index].messages, userMessage, assistantMessage],
                updatedAt: new Date(),
              };
            }

            return updated;
          });
        }

        trackEvent(AnalyticsEventType.MESSAGE_RECEIVED, {
          messageId: assistantMessage.id,
          content: assistantMessage.content,
          intent: response.intent,
          entities: response.entities,
        });
      } catch (err) {
        if (err instanceof ChatbotError) {
          setError(err.message);

          trackEvent(AnalyticsEventType.ERROR_OCCURRED, {
            errorType: err.type,
            errorMessage: err.message,
            statusCode: err.statusCode,
          });

          // Handle specific error types
          if (err.type === ChatbotErrorType.AUTH_ERROR) {
            // Handle auth errors
          }
        } else if (err instanceof Error) {
          setError(err.message);

          trackEvent(AnalyticsEventType.ERROR_OCCURRED, {
            errorType: 'unknown',
            errorMessage: err.message,
          });
        } else {
          setError("Une erreur est survenue lors de l'envoi du message.");

          trackEvent(AnalyticsEventType.ERROR_OCCURRED, {
            errorType: 'unknown',
            errorMessage: "Une erreur est survenue lors de l'envoi du message.",
          });
        }
      } finally {
        setIsLoading(false);
      }
    },
    [messages, userId, conversationId, currentConversation, startNewConversation, trackEvent]
  );

  // Handle button click
  const handleButtonClick = useCallback(
    (button: Button) => {
      if (button.type === 'url' && button.url) {
        window.open(button.url, '_blank');
      } else {
        // For postback buttons, send the value as a message
        sendMessage(button.value);
      }

      trackEvent(AnalyticsEventType.BUTTON_CLICKED, {
        buttonText: button.text,
        buttonValue: button.value,
        buttonType: button.type,
      });
    },
    [sendMessage, trackEvent]
  );

  // Give feedback on a message
  const giveFeedback = useCallback(
    (messageId: string, isPositive: boolean) => {
      // In a real implementation, this would send feedback to the server
      trackEvent(AnalyticsEventType.FEEDBACK_GIVEN, {
        messageId,
        isPositive,
      });

      // You could implement a real feedback service call here
      // feedbackService.sendFeedback(messageId, isPositive);
    },
    [trackEvent]
  );

  // Voice recognition
  const initSpeechRecognition = useCallback(() => {
    if (!isSpeechRecognitionAvailable) return;

    if (recognitionRef.current) {
      recognitionRef.current.abort();
    }

    const SpeechRecognitionImpl = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognitionImpl) {
      // Handle browsers that do not support SpeechRecognition
      console.warn('SpeechRecognition API is not available in this browser.');
      setIsSpeechRecognitionAvailable(false); // Make sure this state is handled elsewhere if needed
      return;
    }
    recognitionRef.current = new SpeechRecognitionImpl();
    // It's good practice to ensure recognitionRef.current is not null before accessing properties
    if (recognitionRef.current) {
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'fr-FR';

      recognitionRef.current.onresult = (event) => {
        const transcript = Array.from(event.results)
          .map((result) => result[0].transcript)
          .join('');

        transcriptRef.current = transcript;
      };

      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error', event.error);
        setIsListening(false);
        setError(`Erreur de reconnaissance vocale: ${event.error}`);
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }
  }, [isSpeechRecognitionAvailable]);

  // Start voice recognition
  const sendVoiceMessage = useCallback(() => {
    if (!isSpeechRecognitionAvailable) {
      setError("La reconnaissance vocale n'est pas disponible sur votre navigateur.");
      return;
    }

    if (!recognitionRef.current) {
      initSpeechRecognition();
    }

    try {
      transcriptRef.current = '';
      recognitionRef.current?.start();
      setIsListening(true);
      setError(null);
    } catch (err) {
      console.error('Failed to start speech recognition', err);
      setError('Impossible de démarrer la reconnaissance vocale.');
    }
  }, [isSpeechRecognitionAvailable, initSpeechRecognition]);

  // Stop voice recognition and send the message
  const stopVoiceMessage = useCallback(() => {
    if (!recognitionRef.current || !isListening) return;

    recognitionRef.current.stop();
    setIsListening(false);

    const transcript = transcriptRef.current.trim();
    if (transcript) {
      sendMessage(transcript);
    }
  }, [isListening, sendMessage]);

  // Clear all messages and start a new conversation
  const clearMessages = useCallback(() => {
    startNewConversation();
  }, [startNewConversation]);

  return {
    messages,
    conversations,
    currentConversation,
    isLoading,
    error,
    isAvailable,
    isSpeechRecognitionAvailable,
    isListening,
    sendMessage,
    sendVoiceMessage,
    stopVoiceMessage,
    clearMessages,
    startNewConversation,
    loadConversation,
    deleteConversation,
    handleButtonClick,
    giveFeedback,
  };
};
