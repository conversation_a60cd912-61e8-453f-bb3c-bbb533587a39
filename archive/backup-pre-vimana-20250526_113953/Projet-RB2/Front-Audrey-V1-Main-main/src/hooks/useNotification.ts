import { useContext } from 'react';
import { NotificationContext } from '../contexts/NotificationContext';
import { ToastType } from '../components/common/Toast';

/**
 * Hook pour utiliser le contexte de notification
 * @returns Méthodes pour gérer les notifications
 */
export const useNotification = () => {
  const context = useContext(NotificationContext);

  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }

  const { addNotification, removeNotification, clearNotifications } = context;

  /**
   * Afficher une notification de succès
   * @param message Message à afficher
   * @param duration Durée d'affichage en ms (0 pour une durée infinie)
   */
  const success = (message: string, duration?: number) => {
    addNotification('success', message, duration);
  };

  /**
   * Afficher une notification d'erreur
   * @param message Message à afficher
   * @param duration Durée d'affichage en ms (0 pour une durée infinie)
   */
  const error = (message: string, duration?: number) => {
    addNotification('error', message, duration);
  };

  /**
   * Afficher une notification d'avertissement
   * @param message Message à afficher
   * @param duration Durée d'affichage en ms (0 pour une durée infinie)
   */
  const warning = (message: string, duration?: number) => {
    addNotification('warning', message, duration);
  };

  /**
   * Afficher une notification d'information
   * @param message Message à afficher
   * @param duration Durée d'affichage en ms (0 pour une durée infinie)
   */
  const info = (message: string, duration?: number) => {
    addNotification('info', message, duration);
  };

  /**
   * Afficher une notification personnalisée
   * @param type Type de notification
   * @param message Message à afficher
   * @param duration Durée d'affichage en ms (0 pour une durée infinie)
   */
  const notify = (type: ToastType, message: string, duration?: number) => {
    addNotification(type, message, duration);
  };

  return {
    success,
    error,
    warning,
    info,
    notify,
    remove: removeNotification,
    clear: clearNotifications,
  };
};
