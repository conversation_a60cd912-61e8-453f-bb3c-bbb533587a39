/**
 * Router Unifié - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Configuration du routing unifié pour tous les modules
 * avec lazy loading et gestion des erreurs.
 */

import React, { Suspense, lazy } from 'react';
import { createBrowserRouter, RouterProvider, Navigate } from 'react-router-dom';
import { AppLayout, AuthLayout, ErrorLayout } from '../components/layout/AppLayout';
import { FullPageLoading } from '../components/ui/design-system/Spinner';

// Lazy loading des modules principaux
const Dashboard = lazy(() => import('../pages/Dashboard'));
const RetreatsModule = lazy(() => import('../modules/retreats/RetreatsModule'));
const ProfessionalsModule = lazy(() => import('../modules/professionals/ProfessionalsModule'));
const ContentModule = lazy(() => import('../modules/content/ContentModule'));
const SocialModule = lazy(() => import('../modules/social/SocialModule'));
const AnalyticsModule = lazy(() => import('../modules/analytics/AnalyticsModule'));
const CreatorModule = lazy(() => import('../modules/creator/CreatorModule'));

// Pages d'authentification
const LoginPage = lazy(() => import('../pages/auth/LoginPage'));
const RegisterPage = lazy(() => import('../pages/auth/RegisterPage'));
const ForgotPasswordPage = lazy(() => import('../pages/auth/ForgotPasswordPage'));

// Pages spéciales
const LandingPage = lazy(() => import('../pages/LandingPage'));
const NotFoundPage = lazy(() => import('../pages/NotFoundPage'));

// Composant de protection des routes
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  user?: {
    name: string;
    email: string;
    avatar?: string;
    role: string;
  };
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  user,
}) => {
  // Vérifier si l'utilisateur est connecté
  if (!user) {
    return <Navigate to="/auth/login" replace />;
  }

  // Vérifier le rôle si requis
  if (requiredRole && user.role !== requiredRole) {
    return (
      <ErrorLayout
        error={{
          status: 403,
          title: 'Accès refusé',
          message: 'Vous n\'avez pas les permissions nécessaires pour accéder à cette page.',
        }}
        onGoHome={() => window.location.href = '/dashboard'}
      />
    );
  }

  return <>{children}</>;
};

// Configuration du router
export const createUnifiedRouter = (user?: ProtectedRouteProps['user'], onLogout?: () => void) => {
  return createBrowserRouter([
    // Page d'accueil publique
    {
      path: '/',
      element: (
        <Suspense fallback={<FullPageLoading />}>
          <LandingPage />
        </Suspense>
      ),
    },

    // Routes d'authentification
    {
      path: '/auth',
      children: [
        {
          path: 'login',
          element: (
            <Suspense fallback={<FullPageLoading />}>
              <AuthLayout title="Connexion" subtitle="Connectez-vous à votre compte">
                <LoginPage />
              </AuthLayout>
            </Suspense>
          ),
        },
        {
          path: 'register',
          element: (
            <Suspense fallback={<FullPageLoading />}>
              <AuthLayout title="Inscription" subtitle="Créez votre compte">
                <RegisterPage />
              </AuthLayout>
            </Suspense>
          ),
        },
        {
          path: 'forgot-password',
          element: (
            <Suspense fallback={<FullPageLoading />}>
              <AuthLayout title="Mot de passe oublié" subtitle="Récupérez votre mot de passe">
                <ForgotPasswordPage />
              </AuthLayout>
            </Suspense>
          ),
        },
      ],
    },

    // Routes protégées de l'application
    {
      path: '/app',
      element: (
        <ProtectedRoute user={user}>
          <AppLayout user={user} onLogout={onLogout} />
        </ProtectedRoute>
      ),
      children: [
        // Redirection par défaut vers le dashboard
        {
          index: true,
          element: <Navigate to="/app/dashboard" replace />,
        },

        // Dashboard principal
        {
          path: 'dashboard',
          element: (
            <Suspense fallback={<FullPageLoading />}>
              <Dashboard />
            </Suspense>
          ),
        },

        // Module Retraites
        {
          path: 'retreats/*',
          element: (
            <Suspense fallback={<FullPageLoading />}>
              <RetreatsModule />
            </Suspense>
          ),
        },

        // Module Professionnels
        {
          path: 'professionals/*',
          element: (
            <Suspense fallback={<FullPageLoading />}>
              <ProfessionalsModule />
            </Suspense>
          ),
        },

        // Module Contenu
        {
          path: 'content/*',
          element: (
            <Suspense fallback={<FullPageLoading />}>
              <ContentModule />
            </Suspense>
          ),
        },

        // Module Social
        {
          path: 'social/*',
          element: (
            <Suspense fallback={<FullPageLoading />}>
              <SocialModule />
            </Suspense>
          ),
        },

        // Module Analytics (Pro uniquement)
        {
          path: 'analytics/*',
          element: (
            <ProtectedRoute user={user} requiredRole="pro">
              <Suspense fallback={<FullPageLoading />}>
                <AnalyticsModule />
              </Suspense>
            </ProtectedRoute>
          ),
        },

        // Module Créateur (Créateurs uniquement)
        {
          path: 'creator/*',
          element: (
            <ProtectedRoute user={user} requiredRole="creator">
              <Suspense fallback={<FullPageLoading />}>
                <CreatorModule />
              </Suspense>
            </ProtectedRoute>
          ),
        },
      ],
    },

    // Redirections pour compatibilité
    {
      path: '/dashboard',
      element: <Navigate to="/app/dashboard" replace />,
    },
    {
      path: '/retreats',
      element: <Navigate to="/app/retreats" replace />,
    },
    {
      path: '/professionals',
      element: <Navigate to="/app/professionals" replace />,
    },
    {
      path: '/content',
      element: <Navigate to="/app/content" replace />,
    },
    {
      path: '/social',
      element: <Navigate to="/app/social" replace />,
    },
    {
      path: '/analytics',
      element: <Navigate to="/app/analytics" replace />,
    },
    {
      path: '/creator',
      element: <Navigate to="/app/creator" replace />,
    },

    // Page 404
    {
      path: '*',
      element: (
        <Suspense fallback={<FullPageLoading />}>
          <NotFoundPage />
        </Suspense>
      ),
    },
  ]);
};

// Composant principal du router
interface UnifiedRouterProps {
  user?: ProtectedRouteProps['user'];
  onLogout?: () => void;
}

export const UnifiedRouter: React.FC<UnifiedRouterProps> = ({ user, onLogout }) => {
  const router = createUnifiedRouter(user, onLogout);
  
  return <RouterProvider router={router} />;
};

// Hook pour la navigation programmatique
export const useUnifiedNavigation = () => {
  const navigate = (path: string, options?: { replace?: boolean }) => {
    // Assurer que les chemins commencent par /app pour les routes protégées
    const protectedPaths = [
      '/dashboard',
      '/retreats',
      '/professionals',
      '/content',
      '/social',
      '/analytics',
      '/creator',
    ];

    let finalPath = path;
    if (protectedPaths.some(p => path.startsWith(p))) {
      finalPath = `/app${path}`;
    }

    window.history.pushState(null, '', finalPath);
    if (options?.replace) {
      window.history.replaceState(null, '', finalPath);
    }
  };

  const goBack = () => {
    window.history.back();
  };

  const goForward = () => {
    window.history.forward();
  };

  const reload = () => {
    window.location.reload();
  };

  return {
    navigate,
    goBack,
    goForward,
    reload,
  };
};
