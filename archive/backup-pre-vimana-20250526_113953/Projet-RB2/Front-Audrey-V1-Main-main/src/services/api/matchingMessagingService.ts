import { apiClient } from './apiClient';

// Define a generic response type for message sending operations
interface MessageSentResponse {
  success: boolean;
  messageId?: string;
  status?: string;
  detail?: string;
}

class MatchingMessagingService {
  /**
   * Envoie un message du partenaire à l'organisateur de la retraite
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @param message Message personnalisé (optionnel)
   * @returns Résultat de l'envoi
   */
  async sendPartnerToOrganizerMessage(
    partnerId: string,
    retreatId: string,
    message?: string,
  ): Promise<MessageSentResponse> {
    try {
      const response = await apiClient.post<MessageSentResponse>('/matching/messaging/partner-to-organizer', {
        partnerId,
        retreatId,
        message,
      });
      return response;
    } catch (error) {
      console.error('Error sending message from partner to organizer:', error);
      throw error;
    }
  }

  /**
   * Envoie un message de l'organisateur au partenaire
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @param message Message personnalisé (optionnel)
   * @returns Résultat de l'envoi
   */
  async sendOrganizerToPartnerMessage(
    partnerId: string,
    retreatId: string,
    message?: string,
  ): Promise<MessageSentResponse> {
    try {
      const response = await apiClient.post<MessageSentResponse>('/matching/messaging/organizer-to-partner', {
        partnerId,
        retreatId,
        message,
      });
      return response;
    } catch (error) {
      console.error('Error sending message from organizer to partner:', error);
      throw error;
    }
  }

  /**
   * Envoie un message de suivi dans une conversation existante
   * @param conversationId ID de la conversation
   * @param message Message personnalisé (optionnel)
   * @returns Résultat de l'envoi
   */
  async sendFollowUpMessage(
    conversationId: string,
    message?: string,
  ): Promise<MessageSentResponse> {
    try {
      const response = await apiClient.post<MessageSentResponse>(`/matching/messaging/follow-up/${conversationId}`, {
        message,
      });
      return response;
    } catch (error) {
      console.error('Error sending follow-up message:', error);
      throw error;
    }
  }

  /**
   * Initie un contact depuis la page de détails de matching
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @param message Message personnalisé (optionnel)
   * @returns Résultat de l'envoi
   */
  async contactFromMatching(
    partnerId: string,
    retreatId: string,
    message?: string,
  ): Promise<MessageSentResponse> {
    try {
      const response = await apiClient.post<MessageSentResponse>(`/matching/messaging/contact-from-matching/${partnerId}/${retreatId}`, {
        message,
      });
      return response;
    } catch (error) {
      console.error('Error initiating contact from matching:', error);
      throw error;
    }
  }
}

export const matchingMessagingService = new MatchingMessagingService();
