import { apiClient } from './apiClient';

/**
 * Direction d'optimisation
 */
export enum OptimizationDirection {
  /** Maximiser */
  MAXIMIZE = 'maximize',
  
  /** Minimiser */
  MINIMIZE = 'minimize',
}

/**
 * Méthodes d'optimisation multi-critères
 */
export enum OptimizationMethod {
  /** Somme pondérée */
  WEIGHTED_SUM = 'weighted_sum',
  
  /** Optimisation de Pareto */
  PARETO = 'pareto',
  
  /** Méthode lexicographique */
  LEXICOGRAPHIC = 'lexicographic',
  
  /** Méthode TOPSIS */
  TOPSIS = 'topsis',
}

/**
 * Types de critères
 */
export enum CriterionType {
  /** Numérique */
  NUMERIC = 'numeric',
  
  /** Catégoriel */
  CATEGORICAL = 'categorical',
  
  /** Booléen */
  BOOLEAN = 'boolean',
  
  /** Personnalisé */
  CUSTOM = 'custom',
}

/**
 * Interface pour un critère et son poids
 */
export interface CriterionWeight {
  /** Identifiant du critère */
  criterionId: string;
  
  /** Poids du critère (entre 0 et 1) */
  weight: number;
  
  /** Direction d'optimisation */
  direction: OptimizationDirection;
}

/**
 * Interface pour un critère d'évaluation
 */
export interface Criterion {
  /** Identifiant du critère */
  id: string;
  
  /** Nom du critère */
  name: string;
  
  /** Description du critère */
  description: string;
  
  /** Type de critère */
  type: CriterionType;
  
  /** Unité de mesure (si applicable) */
  unit?: string;
  
  /** Valeur minimale (si applicable) */
  minValue?: number;
  
  /** Valeur maximale (si applicable) */
  maxValue?: number;
  
  /** Valeurs possibles (pour les critères catégoriels) */
  possibleValues?: string[];
}

/**
 * Interface pour les options de recommandation multi-critères
 */
export interface MultiCriteriaOptions {
  /** Critères et leurs poids */
  criteria: CriterionWeight[];
  
  /** Méthode d'optimisation */
  optimizationMethod: OptimizationMethod;
  
  /** Seuil de dominance pour les solutions Pareto-optimales */
  paretoDominanceThreshold?: number;
  
  /** Normaliser les scores de chaque critère */
  normalizeScores?: boolean;
  
  /** Paramètres spécifiques à la méthode d'optimisation */
  methodParams?: Record<string, any>;
}

/**
 * Interface pour un score multi-critères
 */
export interface MultiCriteriaScore {
  /** Scores par critère */
  criteriaScores: {
    /** Identifiant du critère */
    criterionId: string;
    
    /** Score pour ce critère */
    score: number;
    
    /** Score normalisé (entre 0 et 1) */
    normalizedScore: number;
    
    /** Poids appliqué */
    appliedWeight: number;
  }[];
  
  /** Score global */
  globalScore: number;
  
  /** Est une solution Pareto-optimale */
  isParetoOptimal: boolean;
  
  /** Rang dans l'ensemble des solutions */
  rank?: number;
}

/**
 * Interface pour les préférences multi-critères d'un utilisateur
 */
export interface UserMultiCriteriaPreferences {
  /** Critères et leurs poids */
  criteriaWeights: CriterionWeight[];
  
  /** Méthode d'optimisation préférée */
  preferredMethod?: OptimizationMethod;
  
  /** Paramètres personnalisés pour les méthodes d'optimisation */
  customMethodParams?: Record<string, any>;
}

/**
 * Service pour les recommandations multi-critères
 */
class MultiCriteriaRecommendationService {
  /**
   * Récupère les critères disponibles
   * @returns Liste des critères disponibles
   */
  async getCriteria(): Promise<Criterion[]> {
    try {
      const response = await apiClient.get('/recommendations/multi-criteria/criteria');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des critères:', error);
      throw error;
    }
  }

  /**
   * Récupère un critère par son ID
   * @param criterionId ID du critère
   * @returns Critère
   */
  async getCriterionById(criterionId: string): Promise<Criterion> {
    try {
      const response = await apiClient.get(`/recommendations/multi-criteria/criteria/${criterionId}`);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération du critère ${criterionId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les préférences multi-critères de l'utilisateur
   * @returns Préférences multi-critères
   */
  async getUserPreferences(): Promise<UserMultiCriteriaPreferences> {
    try {
      const response = await apiClient.get('/recommendations/multi-criteria/preferences');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des préférences multi-critères:', error);
      throw error;
    }
  }

  /**
   * Met à jour les préférences multi-critères de l'utilisateur
   * @param preferences Préférences multi-critères
   * @returns Préférences mises à jour
   */
  async updateUserPreferences(preferences: UserMultiCriteriaPreferences): Promise<UserMultiCriteriaPreferences> {
    try {
      const response = await apiClient.put('/recommendations/multi-criteria/preferences', preferences);
      return response;
    } catch (error) {
      console.error('Erreur lors de la mise à jour des préférences multi-critères:', error);
      throw error;
    }
  }

  /**
   * Récupère des recommandations multi-critères
   * @param options Options multi-critères
   * @param type Type de recommandation
   * @param limit Nombre maximum de recommandations
   * @param page Numéro de page
   * @returns Recommandations multi-critères
   */
  async getMultiCriteriaRecommendations(
    options: MultiCriteriaOptions,
    type: string = 'RETREAT',
    limit: number = 10,
    page: number = 1,
  ): Promise<any[]> {
    try {
      const params = {
        type,
        limit,
        page,
      };

      const response = await apiClient.post('/recommendations/multi-criteria', options, { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations multi-critères:', error);
      throw error;
    }
  }
}

export const multiCriteriaRecommendationService = new MultiCriteriaRecommendationService();
