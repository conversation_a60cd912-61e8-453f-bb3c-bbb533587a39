/**
 * Service API pour la gestion des agents
 * Communication avec les agents du système distribué
 */

import { apiClient } from './apiClient';

export interface AgentStatus {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
  uptime: number;
  lastHeartbeat: Date;
  activeJobs: number;
  totalJobs: number;
  errorRate: number;
  responseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  version: string;
  endpoint: string;
}

export interface SystemMetrics {
  totalAgents: number;
  onlineAgents: number;
  totalJobs: number;
  activeJobs: number;
  completedJobs: number;
  failedJobs: number;
  averageResponseTime: number;
  systemUptime: number;
  alertsCount: number;
  criticalAlertsCount: number;
}

export interface AgentConfig {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  autoRestart: boolean;
  maxJobs: number;
  timeout: number;
  retryAttempts: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  resources: {
    cpu: number;
    memory: number;
    disk: number;
  };
  environment: Record<string, string>;
  dependencies: string[];
  healthCheck: {
    enabled: boolean;
    interval: number;
    timeout: number;
    retries: number;
  };
  scaling: {
    enabled: boolean;
    minInstances: number;
    maxInstances: number;
    targetCpu: number;
    targetMemory: number;
  };
}

class AgentsService {
  private readonly baseUrl = '/api/agents';

  /**
   * Obtenir le statut de tous les agents
   */
  async getAgentsStatus(): Promise<{ data: AgentStatus[] }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/status`);
      return {
        data: response.data.map((agent: any) => ({
          ...agent,
          lastHeartbeat: new Date(agent.lastHeartbeat)
        }))
      };
    } catch (error) {
      console.error('Erreur lors de la récupération du statut des agents:', error);
      // Données de simulation en cas d'erreur
      return {
        data: [
          {
            id: 'agent-performance',
            name: 'Agent Performance',
            status: 'online',
            uptime: 86400,
            lastHeartbeat: new Date(),
            activeJobs: 3,
            totalJobs: 127,
            errorRate: 2.1,
            responseTime: 245,
            memoryUsage: 68,
            cpuUsage: 45,
            version: '1.0.0',
            endpoint: 'http://localhost:3007'
          },
          {
            id: 'agent-qa',
            name: 'Agent QA',
            status: 'online',
            uptime: 82800,
            lastHeartbeat: new Date(),
            activeJobs: 1,
            totalJobs: 89,
            errorRate: 1.2,
            responseTime: 189,
            memoryUsage: 52,
            cpuUsage: 32,
            version: '1.0.0',
            endpoint: 'http://localhost:3008'
          },
          {
            id: 'agent-security',
            name: 'Agent Security',
            status: 'online',
            uptime: 79200,
            lastHeartbeat: new Date(),
            activeJobs: 2,
            totalJobs: 156,
            errorRate: 0.8,
            responseTime: 312,
            memoryUsage: 74,
            cpuUsage: 58,
            version: '1.0.0',
            endpoint: 'http://localhost:3009'
          },
          {
            id: 'agent-devops',
            name: 'Agent DevOps',
            status: 'maintenance',
            uptime: 3600,
            lastHeartbeat: new Date(Date.now() - 300000),
            activeJobs: 0,
            totalJobs: 234,
            errorRate: 3.4,
            responseTime: 567,
            memoryUsage: 23,
            cpuUsage: 12,
            version: '1.0.0',
            endpoint: 'http://localhost:3010'
          },
          {
            id: 'agent-uiux',
            name: 'Agent UI/UX',
            status: 'online',
            uptime: 75600,
            lastHeartbeat: new Date(),
            activeJobs: 1,
            totalJobs: 67,
            errorRate: 1.8,
            responseTime: 423,
            memoryUsage: 61,
            cpuUsage: 38,
            version: '1.0.0',
            endpoint: 'http://localhost:3011'
          },
          {
            id: 'agent-frontend',
            name: 'Agent Frontend',
            status: 'error',
            uptime: 1800,
            lastHeartbeat: new Date(Date.now() - 600000),
            activeJobs: 0,
            totalJobs: 45,
            errorRate: 12.3,
            responseTime: 1234,
            memoryUsage: 89,
            cpuUsage: 95,
            version: '1.0.0',
            endpoint: 'http://localhost:3012'
          }
        ]
      };
    }
  }

  /**
   * Obtenir les métriques système globales
   */
  async getSystemMetrics(): Promise<{ data: SystemMetrics }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/metrics`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques système:', error);
      // Données de simulation
      return {
        data: {
          totalAgents: 6,
          onlineAgents: 4,
          totalJobs: 718,
          activeJobs: 7,
          completedJobs: 698,
          failedJobs: 13,
          averageResponseTime: 328,
          systemUptime: 86400,
          alertsCount: 3,
          criticalAlertsCount: 1
        }
      };
    }
  }

  /**
   * Obtenir les détails d'un agent spécifique
   */
  async getAgentDetails(agentId: string): Promise<{ data: any }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${agentId}/details`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des détails de l\'agent:', error);
      // Données de simulation
      return {
        data: {
          endpoint: `http://localhost:300${Math.floor(Math.random() * 10) + 7}`,
          lastHeartbeat: new Date(),
          errorRate: Math.random() * 5,
          requestsPerMinute: Math.floor(Math.random() * 100) + 10,
          successRate: 95 + Math.random() * 5,
          p95Latency: Math.floor(Math.random() * 500) + 100
        }
      };
    }
  }

  /**
   * Contrôler un agent (start, stop, restart)
   */
  async controlAgent(agentId: string, action: 'start' | 'stop' | 'restart'): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/${agentId}/control`, { action });
    } catch (error) {
      console.error(`Erreur lors du contrôle de l'agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir les configurations des agents
   */
  async getAgentConfigs(): Promise<{ data: AgentConfig[] }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/configs`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des configurations:', error);
      // Données de simulation
      return {
        data: [
          {
            id: 'agent-performance',
            name: 'Agent Performance',
            description: 'Agent spécialisé dans l\'optimisation des performances',
            enabled: true,
            autoRestart: true,
            maxJobs: 10,
            timeout: 300,
            retryAttempts: 3,
            logLevel: 'info',
            resources: {
              cpu: 2,
              memory: 2048,
              disk: 10
            },
            environment: {
              NODE_ENV: 'production',
              LOG_LEVEL: 'info'
            },
            dependencies: ['kafka', 'weaviate', 'redis'],
            healthCheck: {
              enabled: true,
              interval: 30,
              timeout: 10,
              retries: 3
            },
            scaling: {
              enabled: true,
              minInstances: 1,
              maxInstances: 3,
              targetCpu: 70,
              targetMemory: 80
            }
          },
          {
            id: 'agent-qa',
            name: 'Agent QA',
            description: 'Agent de tests et assurance qualité',
            enabled: true,
            autoRestart: true,
            maxJobs: 5,
            timeout: 600,
            retryAttempts: 2,
            logLevel: 'info',
            resources: {
              cpu: 1.5,
              memory: 1536,
              disk: 8
            },
            environment: {
              NODE_ENV: 'production',
              TEST_TIMEOUT: '600'
            },
            dependencies: ['kafka', 'selenium'],
            healthCheck: {
              enabled: true,
              interval: 45,
              timeout: 15,
              retries: 2
            },
            scaling: {
              enabled: false,
              minInstances: 1,
              maxInstances: 1,
              targetCpu: 80,
              targetMemory: 85
            }
          }
        ]
      };
    }
  }

  /**
   * Mettre à jour la configuration d'un agent
   */
  async updateAgentConfig(agentId: string, config: AgentConfig): Promise<void> {
    try {
      await apiClient.put(`${this.baseUrl}/${agentId}/config`, config);
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de la configuration de l'agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir les logs d'un agent
   */
  async getAgentLogs(agentId: string, options?: {
    level?: string;
    limit?: number;
    since?: Date;
  }): Promise<{ data: any[] }> {
    try {
      const params = new URLSearchParams();
      if (options?.level) params.append('level', options.level);
      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.since) params.append('since', options.since.toISOString());

      const response = await apiClient.get(`${this.baseUrl}/${agentId}/logs?${params}`);
      return { data: response.data };
    } catch (error) {
      console.error(`Erreur lors de la récupération des logs de l'agent ${agentId}:`, error);
      return { data: [] };
    }
  }

  /**
   * Obtenir les métriques d'un agent spécifique
   */
  async getAgentMetrics(agentId: string, timeRange: string = '1h'): Promise<{ data: any }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${agentId}/metrics?range=${timeRange}`);
      return { data: response.data };
    } catch (error) {
      console.error(`Erreur lors de la récupération des métriques de l'agent ${agentId}:`, error);
      return { data: {} };
    }
  }

  /**
   * Exécuter une action personnalisée sur un agent
   */
  async executeAgentAction(agentId: string, action: string, parameters?: any): Promise<{ data: any }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${agentId}/actions/${action}`, parameters);
      return { data: response.data };
    } catch (error) {
      console.error(`Erreur lors de l'exécution de l'action ${action} sur l'agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir l'historique des performances d'un agent
   */
  async getAgentPerformanceHistory(agentId: string, period: string = '24h'): Promise<{ data: any[] }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${agentId}/performance?period=${period}`);
      return { data: response.data };
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'historique de performance de l'agent ${agentId}:`, error);
      return { data: [] };
    }
  }
}

export const agentsService = new AgentsService();
