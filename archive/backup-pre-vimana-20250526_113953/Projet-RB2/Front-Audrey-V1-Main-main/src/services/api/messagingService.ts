import axios from 'axios';
import { API_URL } from './apiConfig';
import { getAuthToken } from '../../utils/authUtils';

const API_ENDPOINT = `${API_URL}/messaging`;

export interface ParticipantLike {
  id: string;
  firstName?: string;
  lastName?: string;
  image?: string;
  // Allow other properties, as the exact shape isn't fully defined globally
  [key: string]: unknown;
}

export interface IConversation {
  id: string;
  title?: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  lastMessage?: string;
  participants: ParticipantLike[];
  messages: IMessage[];
  unreadCount?: number;
}

export interface IMessage {
  id: string;
  content: string;
  conversationId: string;
  senderId: string;
  receiverId?: string;
  type: string;
  status: string;
  sentAt: string;
  deliveredAt?: string;
  readAt?: string;
  metadata?: Record<string, unknown>;
  replyToId?: string;
  isEncrypted: boolean;
  sender: ParticipantLike;
  receiver?: ParticipantLike;
  replyTo?: IMessage;
  attachments?: IAttachment[];
  reactions?: IReaction[];
}

export interface IAttachment {
  id: string;
  messageId: string;
  type: string;
  url: string;
  name: string;
  size: number;
  mimeType: string;
  metadata?: Record<string, unknown>;
}

export interface IReaction {
  id: string;
  messageId: string;
  userId: string;
  emoji: string;
}

export interface CreateConversationDto {
  title?: string;
  type: string;
  participantIds: string[];
  isEncrypted?: boolean;
  metadata?: Record<string, unknown>;
}

export interface CreateMessageDto {
  content: string;
  conversationId: string;
  receiverId?: string;
  type?: string;
  replyToId?: string;
  isEncrypted?: boolean;
  metadata?: Record<string, unknown>;
}

export interface UpdateMessageDto {
  content?: string;
  status?: string;
  metadata?: Record<string, unknown>;
}

export interface CreateAttachmentDto {
  messageId: string;
  type: string;
  url: string;
  name: string;
  size: number;
  mimeType: string;
  metadata?: Record<string, unknown>;
}

const getHeaders = () => {
  const token = getAuthToken();
  return {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };
};

// Conversations
export const getConversations = async (): Promise<IConversation[]> => {
  const response = await axios.get(`${API_ENDPOINT}/conversations`, getHeaders());
  return response.data;
};

export const getRecentConversations = async (limit?: number): Promise<IConversation[]> => {
  const url = limit
    ? `${API_ENDPOINT}/conversations/recent?limit=${limit}`
    : `${API_ENDPOINT}/conversations/recent`;
  const response = await axios.get(url, getHeaders());
  return response.data;
};

export const getConversation = async (id: string): Promise<IConversation> => {
  const response = await axios.get(`${API_ENDPOINT}/conversations/${id}`, getHeaders());
  return response.data;
};

export const createConversation = async (data: CreateConversationDto): Promise<IConversation> => {
  const response = await axios.post(`${API_ENDPOINT}/conversations`, data, getHeaders());
  return response.data;
};

export const addParticipant = async (
  conversationId: string,
  participantId: string
): Promise<IConversation> => {
  const response = await axios.post(
    `${API_ENDPOINT}/conversations/${conversationId}/participants/${participantId}`,
    {},
    getHeaders()
  );
  return response.data;
};

export const removeParticipant = async (
  conversationId: string,
  participantId: string
): Promise<IConversation> => {
  const response = await axios.delete(
    `${API_ENDPOINT}/conversations/${conversationId}/participants/${participantId}`,
    getHeaders()
  );
  return response.data;
};

export const markConversationAsRead = async (conversationId: string): Promise<void> => {
  await axios.post(`${API_ENDPOINT}/conversations/${conversationId}/read`, {}, getHeaders());
};

// Messages
export const getMessages = async (
  conversationId: string,
  options?: { limit?: number; before?: string }
): Promise<IMessage[]> => {
  let url = `${API_ENDPOINT}/conversations/${conversationId}/messages`;

  if (options) {
    const params = new URLSearchParams();
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.before) params.append('before', options.before);
    if (params.toString()) url += `?${params.toString()}`;
  }

  const response = await axios.get(url, getHeaders());
  return response.data;
};

export const getMessage = async (id: string): Promise<IMessage> => {
  const response = await axios.get(`${API_ENDPOINT}/messages/${id}`, getHeaders());
  return response.data;
};

export const createMessage = async (data: CreateMessageDto): Promise<IMessage> => {
  const response = await axios.post(`${API_ENDPOINT}/messages`, data, getHeaders());
  return response.data;
};

export const updateMessage = async (id: string, data: UpdateMessageDto): Promise<IMessage> => {
  const response = await axios.patch(`${API_ENDPOINT}/messages/${id}`, data, getHeaders());
  return response.data;
};

export const deleteMessage = async (id: string): Promise<void> => {
  await axios.delete(`${API_ENDPOINT}/messages/${id}`, getHeaders());
};

export const markMessageAsRead = async (id: string): Promise<void> => {
  await axios.post(`${API_ENDPOINT}/messages/${id}/read`, {}, getHeaders());
};

export const addReaction = async (messageId: string, emoji: string): Promise<IMessage> => {
  const response = await axios.post(
    `${API_ENDPOINT}/messages/${messageId}/reactions`,
    { emoji },
    getHeaders()
  );
  return response.data;
};

// Attachments
export const getAttachments = async (messageId: string): Promise<IAttachment[]> => {
  const response = await axios.get(
    `${API_ENDPOINT}/messages/${messageId}/attachments`,
    getHeaders()
  );
  return response.data;
};

export const getAttachment = async (id: string): Promise<IAttachment> => {
  const response = await axios.get(`${API_ENDPOINT}/attachments/${id}`, getHeaders());
  return response.data;
};

export const createAttachment = async (data: CreateAttachmentDto): Promise<IAttachment> => {
  const response = await axios.post(`${API_ENDPOINT}/attachments`, data, getHeaders());
  return response.data;
};

export const deleteAttachment = async (id: string): Promise<void> => {
  await axios.delete(`${API_ENDPOINT}/attachments/${id}`, getHeaders());
};

// Stats
export const getMessagingStats = async (): Promise<Record<string, unknown>> => {
  const response = await axios.get(`${API_ENDPOINT}/stats`, getHeaders());
  return response.data;
};

export const getUnreadCount = async (): Promise<number> => {
  const response = await axios.get(`${API_ENDPOINT}/unread`, getHeaders());
  return response.data;
};

export const searchMessages = async (query: string, limit?: number): Promise<IMessage[]> => {
  const url = limit
    ? `${API_ENDPOINT}/search?query=${encodeURIComponent(query)}&limit=${limit}`
    : `${API_ENDPOINT}/search?query=${encodeURIComponent(query)}`;

  const response = await axios.get(url, getHeaders());
  return response.data;
};
