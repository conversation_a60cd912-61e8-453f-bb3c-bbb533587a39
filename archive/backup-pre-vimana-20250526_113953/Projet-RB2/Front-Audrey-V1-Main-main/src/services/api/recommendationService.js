import axios from 'axios';
import { API_URL } from '../../config';
import { getAuthToken } from '../authService';

/**
 * Service pour interagir avec les API de recommandation
 */

/**
 * Récupère les recommandations pour un utilisateur
 * @param {Object} options - Options de recommandation
 * @returns {Promise<Object>} - Réponse de l'API
 */
export const getRecommendations = async (options = {}) => {
  try {
    const token = getAuthToken();
    const response = await axios.get(`${API_URL}/recommendations`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: options,
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des recommandations:', error);
    throw error;
  }
};

/**
 * Récupère une explication améliorée pour une recommandation
 * @param {string} recommendationId - ID de la recommandation
 * @param {string} recommendationType - Type de recommandation
 * @param {Object} options - Options d'explication
 * @returns {Promise<Object>} - Réponse de l'API
 */
export const getEnhancedExplanation = async (recommendationId, recommendationType, options = {}) => {
  try {
    const token = getAuthToken();
    const response = await axios.get(
      `${API_URL}/recommendations/explanations/${recommendationType}/${recommendationId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: options,
      }
    );
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'explication:', error);
    throw error;
  }
};

/**
 * Enregistre un feedback utilisateur pour une recommandation
 * @param {string} recommendationId - ID de la recommandation
 * @param {string} recommendationType - Type de recommandation
 * @param {string} feedbackType - Type de feedback
 * @param {Object} data - Données supplémentaires
 * @returns {Promise<Object>} - Réponse de l'API
 */
export const recordFeedback = async (recommendationId, recommendationType, feedbackType, data = {}) => {
  try {
    const token = getAuthToken();
    const response = await axios.post(
      `${API_URL}/recommendations/feedback`,
      {
        recommendationId,
        recommendationType,
        feedbackType,
        ...data,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du feedback:', error);
    throw error;
  }
};

/**
 * Récupère les feedbacks d'un utilisateur pour une recommandation
 * @param {string} recommendationId - ID de la recommandation
 * @param {string} recommendationType - Type de recommandation
 * @returns {Promise<Object>} - Réponse de l'API
 */
export const getUserFeedbackForRecommendation = async (recommendationId, recommendationType) => {
  try {
    const token = getAuthToken();
    const response = await axios.get(
      `${API_URL}/recommendations/feedback/${recommendationType}/${recommendationId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des feedbacks:', error);
    throw error;
  }
};

/**
 * Signale une recommandation inappropriée
 * @param {string} recommendationId - ID de la recommandation
 * @param {string} recommendationType - Type de recommandation
 * @param {string} reason - Raison du signalement
 * @returns {Promise<Object>} - Réponse de l'API
 */
export const reportRecommendation = async (recommendationId, recommendationType, reason) => {
  try {
    const token = getAuthToken();
    const response = await axios.post(
      `${API_URL}/recommendations/moderation/report/${recommendationType}/${recommendationId}`,
      {
        reason,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Erreur lors du signalement de la recommandation:', error);
    throw error;
  }
};

/**
 * Vérifie si une recommandation est autorisée
 * @param {string} recommendationId - ID de la recommandation
 * @param {string} recommendationType - Type de recommandation
 * @returns {Promise<Object>} - Réponse de l'API
 */
export const checkRecommendation = async (recommendationId, recommendationType) => {
  try {
    const token = getAuthToken();
    const response = await axios.post(
      `${API_URL}/recommendations/moderation/check/${recommendationType}/${recommendationId}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la vérification de la recommandation:', error);
    throw error;
  }
};
