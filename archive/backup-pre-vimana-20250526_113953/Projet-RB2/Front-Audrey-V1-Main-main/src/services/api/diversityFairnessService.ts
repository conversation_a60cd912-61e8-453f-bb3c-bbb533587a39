import { apiClient } from './apiClient';

/**
 * Interface pour les métriques de diversité
 */
export interface DiversityMetrics {
  /** Score de diversité des catégories (0-1) */
  categoryDiversity: number;
  
  /** Score de diversité des prix (0-1) */
  priceDiversity: number;
  
  /** Score de diversité des durées (0-1) */
  durationDiversity: number;
  
  /** Score de diversité des localisations (0-1) */
  locationDiversity: number;
  
  /** Score de diversité des thèmes (0-1) */
  themeDiversity: number;
  
  /** Score de diversité global (0-1) */
  overallDiversity: number;
  
  /** Nombre de catégories uniques */
  uniqueCategories: number;
  
  /** Nombre de plages de prix uniques */
  uniquePriceRanges: number;
  
  /** Nombre de plages de durée uniques */
  uniqueDurationRanges: number;
  
  /** Nombre de localisations uniques */
  uniqueLocations: number;
  
  /** Nombre de thèmes uniques */
  uniqueThemes: number;
}

/**
 * Interface pour les métriques d'équité
 */
export interface FairnessMetrics {
  /** Score d'équité global (0-1) */
  overallFairnessScore: number;
  
  /** Score d'équité de représentation des catégories (0-1) */
  categoryRepresentationScore: number;
  
  /** Score d'équité de représentation des prix (0-1) */
  priceRepresentationScore: number;
  
  /** Score d'équité de représentation des localisations (0-1) */
  locationRepresentationScore: number;
  
  /** Score d'équité de représentation des fournisseurs (0-1) */
  providerRepresentationScore: number;
  
  /** Score d'équité de représentation des thèmes (0-1) */
  themeRepresentationScore: number;
  
  /** Distribution des catégories */
  categoryDistribution: Record<string, number>;
  
  /** Distribution des plages de prix */
  priceDistribution: Record<string, number>;
  
  /** Distribution des localisations */
  locationDistribution: Record<string, number>;
  
  /** Distribution des fournisseurs */
  providerDistribution: Record<string, number>;
  
  /** Distribution des thèmes */
  themeDistribution: Record<string, number>;
  
  /** Biais détectés */
  detectedBiases: Array<{
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>;
}

/**
 * Interface pour les options de diversification
 */
export interface DiversificationOptions {
  /** Facteur d'équilibre entre pertinence et diversité (0-1) */
  diversityFactor?: number;
  
  /** Taille maximale de la liste de recommandations */
  maxRecommendations?: number;
  
  /** Inclure des recommandations surprenantes */
  includeSurprising?: boolean;
  
  /** Nombre de recommandations surprenantes à inclure */
  surprisingCount?: number;
}

/**
 * Interface pour les options d'équité
 */
export interface FairnessOptions {
  /** Facteur d'équilibre entre pertinence et équité (0-1) */
  fairnessFactor?: number;
  
  /** Taille maximale de la liste de recommandations */
  maxRecommendations?: number;
  
  /** Appliquer une correction d'équité */
  applyFairnessCorrection?: boolean;
}

/**
 * Service pour la diversité et l'équité des recommandations
 */
class DiversityFairnessService {
  /**
   * Récupère les métriques de diversité pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Métriques de diversité
   */
  async getDiversityMetrics(userId: string): Promise<DiversityMetrics> {
    try {
      const response = await apiClient.get(`/recommendation/diversity-fairness/diversity-metrics/${userId}`);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération des métriques de diversité pour l'utilisateur ${userId}:`, error);
      throw error;
    }
  }
  
  /**
   * Récupère les métriques d'équité pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Métriques d'équité
   */
  async getFairnessMetrics(userId: string): Promise<FairnessMetrics> {
    try {
      const response = await apiClient.get(`/recommendation/diversity-fairness/fairness-metrics/${userId}`);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération des métriques d'équité pour l'utilisateur ${userId}:`, error);
      throw error;
    }
  }
  
  /**
   * Applique la diversification aux recommandations d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de diversification
   * @returns Recommandations diversifiées
   */
  async diversifyRecommendations(userId: string, options?: DiversificationOptions): Promise<any[]> {
    try {
      const response = await apiClient.post(`/recommendation/diversity-fairness/diversify/${userId}`, options || {});
      return response;
    } catch (error) {
      console.error(`Erreur lors de la diversification des recommandations pour l'utilisateur ${userId}:`, error);
      throw error;
    }
  }
  
  /**
   * Applique l'équité aux recommandations d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options d'équité
   * @returns Recommandations équitables
   */
  async applyFairness(userId: string, options?: FairnessOptions): Promise<any[]> {
    try {
      const response = await apiClient.post(`/recommendation/diversity-fairness/apply-fairness/${userId}`, options || {});
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'application de l'équité aux recommandations pour l'utilisateur ${userId}:`, error);
      throw error;
    }
  }
  
  /**
   * Récupère des recommandations diversifiées et équitables
   * @param diversityFactor Facteur de diversité
   * @param fairnessFactor Facteur d'équité
   * @param includeSurprising Inclure des recommandations surprenantes
   * @returns Recommandations diversifiées et équitables
   */
  async getDiverseFairRecommendations(
    diversityFactor?: number,
    fairnessFactor?: number,
    includeSurprising?: boolean,
  ): Promise<any[]> {
    try {
      const params: Record<string, any> = {};
      
      if (diversityFactor !== undefined) {
        params.diversityFactor = diversityFactor;
      }
      
      if (fairnessFactor !== undefined) {
        params.fairnessFactor = fairnessFactor;
      }
      
      if (includeSurprising !== undefined) {
        params.includeSurprising = includeSurprising;
      }
      
      const response = await apiClient.get('/recommendation/diversity-fairness/recommendations', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations diversifiées et équitables:', error);
      throw error;
    }
  }
  
  /**
   * Récupère des recommandations surprenantes
   * @param count Nombre de recommandations à récupérer
   * @returns Recommandations surprenantes
   */
  async getDiscoverRecommendations(count?: number): Promise<any[]> {
    try {
      const params: Record<string, any> = {};
      
      if (count !== undefined) {
        params.count = count;
      }
      
      const response = await apiClient.get('/recommendation/diversity-fairness/discover', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations surprenantes:', error);
      throw error;
    }
  }
  
  /**
   * Formate un pourcentage
   * @param value Valeur (0-1)
   * @returns Pourcentage formaté
   */
  formatPercent(value: number): string {
    return `${(value * 100).toFixed(1)}%`;
  }
  
  /**
   * Obtient la couleur pour un score
   * @param score Score (0-1)
   * @returns Classe CSS de couleur
   */
  getScoreColorClass(score: number): string {
    if (score >= 0.8) {
      return 'text-green-600';
    } else if (score >= 0.6) {
      return 'text-yellow-600';
    } else {
      return 'text-red-600';
    }
  }
  
  /**
   * Obtient la couleur pour une sévérité de biais
   * @param severity Sévérité du biais
   * @returns Classe CSS de couleur
   */
  getBiasSeverityColorClass(severity: 'low' | 'medium' | 'high'): string {
    switch (severity) {
      case 'low':
        return 'text-yellow-600';
      case 'medium':
        return 'text-orange-600';
      case 'high':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  }
}

export const diversityFairnessService = new DiversityFairnessService();
