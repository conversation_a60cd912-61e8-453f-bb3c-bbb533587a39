interface ConversionRate {
  eurToRandB: number;
  randBToEur: number;
  lastUpdate: Date;
}

class ConversionService {
  private static instance: ConversionService;
  private currentRate: ConversionRate = {
    eurToRandB: 0.0001, // 1 EUR = 0.0001 RandB
    randBToEur: 10000, // 1 RandB = 10000 EUR
    lastUpdate: new Date(),
  };

  private constructor() {
    // Initialisation privée pour le singleton
  }

  public static getInstance(): ConversionService {
    if (!ConversionService.instance) {
      ConversionService.instance = new ConversionService();
    }
    return ConversionService.instance;
  }

  public async updateRates(): Promise<void> {
    try {
      // Simulation d'un appel API pour obtenir les taux en temps réel
      // Dans une vraie application, cela serait un appel à une API de taux de change
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Simulation de variation aléatoire des taux
      const variation = 0.95 + Math.random() * 0.1; // Variation entre -5% et +5%
      this.currentRate = {
        eurToRandB: this.currentRate.eurToRandB * variation,
        randBToEur: this.currentRate.randBToEur / variation,
        lastUpdate: new Date(),
      };
    } catch (error) {
      console.error('Erreur lors de la mise à jour des taux:', error);
      throw error;
    }
  }

  public getCurrentRates(): ConversionRate {
    return { ...this.currentRate };
  }

  public convertEurToRandB(eurAmount: number): number {
    return eurAmount * this.currentRate.eurToRandB;
  }

  public convertRandBToEur(randBAmount: number): number {
    return randBAmount * this.currentRate.randBToEur;
  }

  public getLastUpdate(): Date {
    return this.currentRate.lastUpdate;
  }
}

export default ConversionService;
