// Types de langues supportées
export type SupportedLanguage = 'fr' | 'en' | 'es' | 'de';

// Interface pour les traductions
export interface Translations {
  [key: string]: string | Translations;
}

/**
 * Service d'internationalisation
 * Gère les traductions et le changement de langue
 */
class I18nService {
  private currentLanguage: SupportedLanguage = 'fr';
  private translations: Record<SupportedLanguage, Translations> = {
    fr: {},
    en: {},
    es: {},
    de: {},
  };
  private listeners: Array<(language: SupportedLanguage) => void> = [];
  private isInitialized: boolean = false;

  constructor() {
    // Initialiser la langue à partir du localStorage ou de la langue du navigateur
    this.initLanguage();
    // Charger les traductions
    this.loadAllTranslations();
  }

  /**
   * Initialiser la langue
   */
  private initLanguage(): void {
    // Essayer de récupérer la langue depuis le localStorage
    const storedLanguage = localStorage.getItem('language') as SupportedLanguage | null;

    if (storedLanguage && this.isLanguageSupported(storedLanguage)) {
      this.currentLanguage = storedLanguage;
      return;
    }

    // Sinon, utiliser la langue du navigateur
    const browserLanguage = navigator.language.split('-')[0] as SupportedLanguage;

    if (this.isLanguageSupported(browserLanguage)) {
      this.currentLanguage = browserLanguage;
    }
    // Sinon, conserver la langue par défaut (fr)
  }

  /**
   * Vérifier si une langue est supportée
   * @param language Langue à vérifier
   * @returns true si la langue est supportée
   */
  private isLanguageSupported(language: string): language is SupportedLanguage {
    return ['fr', 'en', 'es', 'de'].includes(language);
  }

  /**
   * Obtenir la langue actuelle
   * @returns Langue actuelle
   */
  public getLanguage(): SupportedLanguage {
    return this.currentLanguage;
  }

  /**
   * Changer la langue
   * @param language Nouvelle langue
   */
  public setLanguage(language: SupportedLanguage): void {
    if (this.currentLanguage === language) return;

    this.currentLanguage = language;

    // Sauvegarder la langue dans le localStorage
    localStorage.setItem('language', language);

    // Notifier les écouteurs
    this.notifyListeners();
  }

  /**
   * Ajouter un écouteur pour les changements de langue
   * @param listener Fonction à appeler lors d'un changement de langue
   * @returns Fonction pour supprimer l'écouteur
   */
  public addListener(listener: (language: SupportedLanguage) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  /**
   * Notifier les écouteurs d'un changement de langue
   */
  private notifyListeners(): void {
    this.listeners.forEach((listener) => listener(this.currentLanguage));
  }

  /**
   * Charger des traductions pour une langue
   * @param language Langue cible
   * @param translations Traductions à charger
   */
  public loadTranslations(language: SupportedLanguage, translations: Translations): void {
    this.translations[language] = {
      ...this.translations[language],
      ...translations,
    };
  }

  /**
   * Charger toutes les traductions depuis les fichiers
   */
  private async loadAllTranslations(): Promise<void> {
    try {
      // Charger les traductions pour chaque langue supportée
      await Promise.all([
        this.loadTranslationsFromFiles('fr'),
        this.loadTranslationsFromFiles('en'),
        // Ajouter d'autres langues au besoin
      ]);

      this.isInitialized = true;
      this.notifyListeners();
    } catch (error) {
      console.error('Erreur lors du chargement des traductions:', error);
    }
  }

  /**
   * Charger les traductions depuis les fichiers pour une langue spécifique
   * @param language Langue cible
   */
  private async loadTranslationsFromFiles(language: SupportedLanguage): Promise<void> {
    try {
      // Charger les traductions communes
      const commonResponse = await fetch(`/locales/${language}/common.json`);
      if (commonResponse.ok) {
        const commonTranslations = await commonResponse.json();
        this.loadTranslations(language, commonTranslations);
      }

      // Charger les traductions spécifiques
      const translationResponse = await fetch(`/locales/${language}/translation.json`);
      if (translationResponse.ok) {
        const translations = await translationResponse.json();
        this.loadTranslations(language, translations);
      }
    } catch (error) {
      console.error(`Erreur lors du chargement des traductions pour ${language}:`, error);
    }
  }

  /**
   * Vérifier si le service est initialisé
   * @returns true si le service est initialisé
   */
  public isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Traduire une clé
   * @param key Clé de traduction
   * @param params Paramètres à insérer dans la traduction
   * @returns Traduction
   */
  public translate(key: string, params: Record<string, string | number> = {}): string {
    // Récupérer la traduction
    const translation = this.getTranslation(key);

    // Si la traduction n'existe pas, retourner la clé
    if (!translation) return key;

    // Remplacer les paramètres
    return this.replaceParams(translation, params);
  }

  /**
   * Obtenir une traduction
   * @param key Clé de traduction
   * @returns Traduction ou null si non trouvée
   */
  private getTranslation(key: string): string | null {
    // Diviser la clé en parties (pour les clés imbriquées)
    const parts = key.split('.');

    // Récupérer les traductions pour la langue actuelle
    let translations = this.translations[this.currentLanguage];

    // Parcourir les parties de la clé
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];

      // Si c'est la dernière partie, retourner la traduction
      if (i === parts.length - 1) {
        const translation = translations[part];

        // Vérifier que la traduction est une chaîne
        if (typeof translation === 'string') {
          return translation;
        }

        return null;
      }

      // Sinon, continuer à parcourir les traductions
      const nestedTranslations = translations[part];

      // Vérifier que les traductions imbriquées existent
      if (typeof nestedTranslations !== 'object' || nestedTranslations === null) {
        return null;
      }

      translations = nestedTranslations as Translations;
    }

    return null;
  }

  /**
   * Remplacer les paramètres dans une traduction
   * @param translation Traduction
   * @param params Paramètres à insérer
   * @returns Traduction avec les paramètres insérés
   */
  private replaceParams(translation: string, params: Record<string, string | number>): string {
    let result = translation;

    // Remplacer chaque paramètre
    for (const [key, value] of Object.entries(params)) {
      result = result.replace(new RegExp(`{${key}}`, 'g'), String(value));
    }

    return result;
  }
}

// Exporter une instance unique du service d'internationalisation
export const i18nService = new I18nService();

// Fonction raccourcie pour traduire
export const t = (key: string, params: Record<string, string | number> = {}): string => {
  return i18nService.translate(key, params);
};
