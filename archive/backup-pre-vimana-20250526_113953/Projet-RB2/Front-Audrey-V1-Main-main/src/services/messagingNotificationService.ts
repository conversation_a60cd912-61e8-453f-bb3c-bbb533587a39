import { io, Socket } from 'socket.io-client';
import { API_URL } from './api/apiConfig';
import { getAuthToken } from '../utils/authUtils';
import { IMessage } from './api/messagingService';

/**
 * Service de notification pour la messagerie
 * Gère les notifications en temps réel pour les nouveaux messages
 */
class MessagingNotificationService {
  private socket: Socket | null = null;
  private notificationCallbacks: Array<(data: IMessage) => void> = [];
  private unreadCountCallbacks: Array<(count: number) => void> = [];

  /**
   * Initialise la connexion WebSocket
   */
  public init(): void {
    const token = getAuthToken();
    if (!token) return;

    // Fermer la connexion existante si elle existe
    this.disconnect();

    // Créer une nouvelle connexion
    this.socket = io(`${API_URL}/messaging`, {
      auth: {
        token,
      },
    });

    this.socket.on('connect', () => {
      console.log('Connexion WebSocket établie pour les notifications de messagerie');
    });

    this.socket.on('disconnect', () => {
      console.log('Connexion WebSocket fermée pour les notifications de messagerie');
    });

    // Écouter les nouveaux messages
    this.socket.on('message:created', (data: IMessage) => {
      // Vérifier si le message est destiné à l'utilisateur actuel
      const currentUserId = localStorage.getItem('userId');
      // Notify if the current user is not the sender AND
      // (it's a direct message to them OR it implies a group chat context where they might be a participant)
      if (
        data.senderId !== currentUserId &&
        (data.receiverId === currentUserId || !data.receiverId)
      ) {
        // Déclencher les callbacks de notification
        this.notificationCallbacks.forEach((callback) => callback(data));

        // Mettre à jour le compteur de messages non lus
        this.updateUnreadCount();
      }
    });

    // Écouter les mises à jour du statut de lecture
    this.socket.on('message:read', () => {
      this.updateUnreadCount();
    });
  }

  /**
   * Ferme la connexion WebSocket
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Ajoute un callback pour les notifications de nouveaux messages
   * @param callback Fonction à appeler lors de la réception d'un nouveau message
   * @returns Fonction pour supprimer le callback
   */
  public onNewMessage(callback: (data: IMessage) => void): () => void {
    this.notificationCallbacks.push(callback);
    return () => {
      this.notificationCallbacks = this.notificationCallbacks.filter((cb) => cb !== callback);
    };
  }

  /**
   * Ajoute un callback pour les mises à jour du compteur de messages non lus
   * @param callback Fonction à appeler lors de la mise à jour du compteur
   * @returns Fonction pour supprimer le callback
   */
  public onUnreadCountUpdate(callback: (count: number) => void): () => void {
    this.unreadCountCallbacks.push(callback);
    return () => {
      this.unreadCountCallbacks = this.unreadCountCallbacks.filter((cb) => cb !== callback);
    };
  }

  /**
   * Met à jour le compteur de messages non lus
   */
  private async updateUnreadCount(): Promise<void> {
    try {
      // Appeler l'API pour obtenir le nombre de messages non lus
      const response = await fetch(`${API_URL}/messaging/unread`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
        },
      });

      if (response.ok) {
        const count = await response.json();
        // Déclencher les callbacks de mise à jour du compteur
        this.unreadCountCallbacks.forEach((callback) => callback(count));
      }
    } catch (error) {
      console.error('Erreur lors de la récupération du nombre de messages non lus:', error);
    }
  }

  /**
   * Affiche une notification système pour un nouveau message
   * @param message Données du message
   */
  public showNotification(message: IMessage): void {
    // Vérifier si les notifications sont supportées et autorisées
    if (!('Notification' in window)) {
      console.log('Ce navigateur ne prend pas en charge les notifications de bureau');
      return;
    }

    if (Notification.permission === 'granted') {
      this.createNotification(message);
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then((permission) => {
        if (permission === 'granted') {
          this.createNotification(message);
        }
      });
    }
  }

  /**
   * Crée et affiche une notification système
   * @param message Données du message
   */
  private createNotification(message: IMessage): void {
    const sender = message.sender?.firstName
      ? `${message.sender.firstName} ${message.sender.lastName || ''}`
      : "Quelqu'un";

    const title = `Nouveau message de ${sender}`;
    const options = {
      body: message.content,
      icon: '/images/logo.svg', // Ensure this path is correct
    };

    const notification = new Notification(title, options);

    // Rediriger vers la conversation lorsque l'utilisateur clique sur la notification
    notification.onclick = () => {
      window.focus();
      window.location.href = `/messaging/${message.conversationId}`;
      notification.close();
    };
  }
}

// Exporter une instance unique du service
export const messagingNotificationService = new MessagingNotificationService();
