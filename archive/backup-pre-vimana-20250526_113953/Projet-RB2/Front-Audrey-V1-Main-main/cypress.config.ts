/**
 * Configuration Cypress - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Configuration complète pour les tests E2E et de composants
 * avec environnements multiples et optimisations.
 */

import { defineConfig } from 'cypress';

export default defineConfig({
  e2e: {
    // Configuration de base
    baseUrl: 'http://localhost:3000',
    supportFile: 'cypress/support/e2e.ts',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    
    // Viewport par défaut
    viewportWidth: 1280,
    viewportHeight: 720,
    
    // Configuration vidéo et screenshots
    video: true,
    videosFolder: 'cypress/videos',
    screenshotOnRunFailure: true,
    screenshotsFolder: 'cypress/screenshots',
    
    // Timeouts
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    pageLoadTimeout: 30000,
    
    // Retry configuration
    retries: {
      runMode: 2,
      openMode: 0,
    },
    
    // Variables d'environnement
    env: {
      // URLs des environnements
      apiUrl: 'http://localhost:3001/api',
      stagingUrl: 'https://staging.retreatandbe.com',
      prodUrl: 'https://app.retreatandbe.com',
      
      // Utilisateurs de test
      testUser: {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        name: 'Test User'
      },
      adminUser: {
        email: '<EMAIL>',
        password: 'AdminPassword123!',
        name: 'Admin User'
      },
      creatorUser: {
        email: '<EMAIL>',
        password: 'CreatorPassword123!',
        name: 'Creator User'
      },
      
      // Configuration des tests
      coverage: true,
      lighthouse: true,
      accessibility: true,
      
      // Données de test
      testRetreat: {
        id: 'test-retreat-1',
        name: 'Test Yoga Retreat',
        price: 299,
        duration: '3 days'
      },
      testProfessional: {
        id: 'test-professional-1',
        name: 'Test Yoga Teacher',
        price: 75
      }
    },
    
    // Configuration des tâches
    setupNodeEvents(on, config) {
      // Plugin de couverture de code
      require('@cypress/code-coverage/task')(on, config);
      
      // Plugin Lighthouse
      on('task', {
        lighthouse: require('cypress-lighthouse/task'),
      });
      
      // Plugin pour les logs
      on('task', {
        log(message) {
          console.log(message);
          return null;
        },
        table(message) {
          console.table(message);
          return null;
        }
      });
      
      // Configuration dynamique selon l'environnement
      if (config.env.environment === 'staging') {
        config.baseUrl = config.env.stagingUrl;
      } else if (config.env.environment === 'production') {
        config.baseUrl = config.env.prodUrl;
      }
      
      return config;
    },
  },
  
  component: {
    // Configuration pour les tests de composants
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
    supportFile: 'cypress/support/component.ts',
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    indexHtmlFile: 'cypress/support/component-index.html',
    
    // Viewport pour les tests de composants
    viewportWidth: 1000,
    viewportHeight: 660,
    
    // Variables d'environnement pour les composants
    env: {
      coverage: true,
    },
    
    setupNodeEvents(on, config) {
      require('@cypress/code-coverage/task')(on, config);
      return config;
    },
  },
  
  // Configuration globale
  chromeWebSecurity: false,
  modifyObstructiveCode: false,
  experimentalStudio: true,
  experimentalWebKitSupport: true,
  
  // Exclusions
  excludeSpecPattern: [
    '**/examples/*',
    '**/node_modules/*'
  ],
});
