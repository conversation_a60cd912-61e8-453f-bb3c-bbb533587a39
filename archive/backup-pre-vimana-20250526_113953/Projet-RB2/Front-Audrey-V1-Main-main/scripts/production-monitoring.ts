/**
 * Monitoring Production - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Script de monitoring en temps réel pour surveiller
 * la santé et les performances de l'application en production.
 */

import fs from 'fs/promises';
import { execSync } from 'child_process';

interface MetricData {
  timestamp: string;
  value: number;
  status: 'healthy' | 'warning' | 'critical';
}

interface HealthMetrics {
  uptime: MetricData;
  responseTime: MetricData;
  errorRate: MetricData;
  activeUsers: MetricData;
  memoryUsage: MetricData;
  cpuUsage: MetricData;
}

interface Alert {
  id: string;
  type: 'performance' | 'error' | 'security' | 'business';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  resolved: boolean;
}

class ProductionMonitor {
  private metrics: HealthMetrics[] = [];
  private alerts: Alert[] = [];
  private isRunning = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  async startMonitoring(): Promise<void> {
    console.log('📊 DÉMARRAGE DU MONITORING PRODUCTION - RETREAT AND BE');
    console.log('=' .repeat(70));
    console.log(`🕐 Démarré à: ${new Date().toLocaleString('fr-FR')}`);
    console.log('=' .repeat(70));

    this.isRunning = true;
    
    // Monitoring principal toutes les 30 secondes
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, 30000);

    // Monitoring initial
    await this.collectMetrics();
    
    // Affichage du dashboard
    this.startDashboard();

    // Gestion de l'arrêt propre
    process.on('SIGINT', () => {
      this.stopMonitoring();
    });

    console.log('✅ Monitoring démarré avec succès');
    console.log('📈 Dashboard disponible en temps réel');
    console.log('🛑 Appuyez sur Ctrl+C pour arrêter\n');
  }

  private async collectMetrics(): Promise<void> {
    const timestamp = new Date().toISOString();
    
    try {
      const metrics: HealthMetrics = {
        uptime: await this.checkUptime(),
        responseTime: await this.checkResponseTime(),
        errorRate: await this.checkErrorRate(),
        activeUsers: await this.checkActiveUsers(),
        memoryUsage: await this.checkMemoryUsage(),
        cpuUsage: await this.checkCpuUsage(),
      };

      // Ajouter timestamp à toutes les métriques
      Object.values(metrics).forEach(metric => {
        metric.timestamp = timestamp;
      });

      this.metrics.push(metrics);
      
      // Garder seulement les 100 dernières métriques
      if (this.metrics.length > 100) {
        this.metrics = this.metrics.slice(-100);
      }

      // Analyser les métriques pour détecter des problèmes
      await this.analyzeMetrics(metrics);
      
      // Sauvegarder les métriques
      await this.saveMetrics();

    } catch (error) {
      console.error('❌ Erreur lors de la collecte des métriques:', error);
      
      this.createAlert({
        type: 'error',
        severity: 'high',
        message: `Erreur de collecte des métriques: ${error}`,
      });
    }
  }

  private async checkUptime(): Promise<MetricData> {
    try {
      // Simuler une vérification d'uptime
      const isUp = Math.random() > 0.01; // 99% uptime
      
      return {
        timestamp: '',
        value: isUp ? 100 : 0,
        status: isUp ? 'healthy' : 'critical',
      };
    } catch {
      return {
        timestamp: '',
        value: 0,
        status: 'critical',
      };
    }
  }

  private async checkResponseTime(): Promise<MetricData> {
    try {
      // Simuler une mesure de temps de réponse
      const responseTime = Math.random() * 2000 + 200; // 200-2200ms
      
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (responseTime > 1500) status = 'critical';
      else if (responseTime > 1000) status = 'warning';
      
      return {
        timestamp: '',
        value: Math.round(responseTime),
        status,
      };
    } catch {
      return {
        timestamp: '',
        value: 0,
        status: 'critical',
      };
    }
  }

  private async checkErrorRate(): Promise<MetricData> {
    try {
      // Simuler un taux d'erreur
      const errorRate = Math.random() * 5; // 0-5%
      
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (errorRate > 3) status = 'critical';
      else if (errorRate > 1) status = 'warning';
      
      return {
        timestamp: '',
        value: Math.round(errorRate * 100) / 100,
        status,
      };
    } catch {
      return {
        timestamp: '',
        value: 100,
        status: 'critical',
      };
    }
  }

  private async checkActiveUsers(): Promise<MetricData> {
    try {
      // Simuler le nombre d'utilisateurs actifs
      const baseUsers = 1500;
      const variation = Math.sin(Date.now() / 3600000) * 500; // Variation horaire
      const activeUsers = Math.max(0, Math.round(baseUsers + variation + (Math.random() - 0.5) * 200));
      
      return {
        timestamp: '',
        value: activeUsers,
        status: 'healthy',
      };
    } catch {
      return {
        timestamp: '',
        value: 0,
        status: 'warning',
      };
    }
  }

  private async checkMemoryUsage(): Promise<MetricData> {
    try {
      // Simuler l'utilisation mémoire
      const memoryUsage = Math.random() * 80 + 10; // 10-90%
      
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (memoryUsage > 85) status = 'critical';
      else if (memoryUsage > 70) status = 'warning';
      
      return {
        timestamp: '',
        value: Math.round(memoryUsage),
        status,
      };
    } catch {
      return {
        timestamp: '',
        value: 100,
        status: 'critical',
      };
    }
  }

  private async checkCpuUsage(): Promise<MetricData> {
    try {
      // Simuler l'utilisation CPU
      const cpuUsage = Math.random() * 70 + 5; // 5-75%
      
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (cpuUsage > 80) status = 'critical';
      else if (cpuUsage > 60) status = 'warning';
      
      return {
        timestamp: '',
        value: Math.round(cpuUsage),
        status,
      };
    } catch {
      return {
        timestamp: '',
        value: 100,
        status: 'critical',
      };
    }
  }

  private async analyzeMetrics(metrics: HealthMetrics): Promise<void> {
    // Analyser l'uptime
    if (metrics.uptime.status === 'critical') {
      this.createAlert({
        type: 'error',
        severity: 'critical',
        message: 'Application indisponible !',
      });
    }

    // Analyser le temps de réponse
    if (metrics.responseTime.status === 'critical') {
      this.createAlert({
        type: 'performance',
        severity: 'high',
        message: `Temps de réponse élevé: ${metrics.responseTime.value}ms`,
      });
    }

    // Analyser le taux d'erreur
    if (metrics.errorRate.status === 'critical') {
      this.createAlert({
        type: 'error',
        severity: 'critical',
        message: `Taux d'erreur élevé: ${metrics.errorRate.value}%`,
      });
    }

    // Analyser l'utilisation des ressources
    if (metrics.memoryUsage.status === 'critical' || metrics.cpuUsage.status === 'critical') {
      this.createAlert({
        type: 'performance',
        severity: 'high',
        message: `Ressources critiques - CPU: ${metrics.cpuUsage.value}%, RAM: ${metrics.memoryUsage.value}%`,
      });
    }

    // Analyser les tendances
    if (this.metrics.length >= 5) {
      await this.analyzeTrends();
    }
  }

  private async analyzeTrends(): Promise<void> {
    const recent = this.metrics.slice(-5);
    
    // Tendance du temps de réponse
    const responseTimes = recent.map(m => m.responseTime.value);
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    
    if (avgResponseTime > 1200) {
      this.createAlert({
        type: 'performance',
        severity: 'medium',
        message: `Tendance de dégradation des performances: ${Math.round(avgResponseTime)}ms en moyenne`,
      });
    }

    // Tendance des utilisateurs actifs
    const userCounts = recent.map(m => m.activeUsers.value);
    const userTrend = userCounts[userCounts.length - 1] - userCounts[0];
    
    if (userTrend > 500) {
      this.createAlert({
        type: 'business',
        severity: 'low',
        message: `Pic d'activité détecté: +${userTrend} utilisateurs`,
      });
    }
  }

  private createAlert(alert: Omit<Alert, 'id' | 'timestamp' | 'resolved'>): void {
    const newAlert: Alert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      resolved: false,
      ...alert,
    };

    this.alerts.unshift(newAlert);
    
    // Garder seulement les 50 dernières alertes
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(0, 50);
    }

    // Afficher l'alerte
    const icon = this.getAlertIcon(newAlert.severity);
    console.log(`\n${icon} ALERTE ${newAlert.severity.toUpperCase()}: ${newAlert.message}`);
    console.log(`   Type: ${newAlert.type} | ID: ${newAlert.id}`);
  }

  private getAlertIcon(severity: string): string {
    switch (severity) {
      case 'critical': return '🚨';
      case 'high': return '⚠️';
      case 'medium': return '🟡';
      case 'low': return 'ℹ️';
      default: return '📢';
    }
  }

  private startDashboard(): void {
    // Afficher le dashboard toutes les 10 secondes
    setInterval(() => {
      if (this.isRunning && this.metrics.length > 0) {
        this.displayDashboard();
      }
    }, 10000);
  }

  private displayDashboard(): void {
    const latest = this.metrics[this.metrics.length - 1];
    if (!latest) return;

    // Effacer l'écran (optionnel)
    // console.clear();

    console.log('\n📊 DASHBOARD TEMPS RÉEL - RETREAT AND BE');
    console.log('=' .repeat(70));
    console.log(`🕐 Dernière mise à jour: ${new Date(latest.uptime.timestamp).toLocaleString('fr-FR')}`);
    console.log('');

    // Métriques principales
    console.log('📈 MÉTRIQUES PRINCIPALES:');
    console.log(`   ${this.getStatusIcon(latest.uptime.status)} Uptime: ${latest.uptime.value}%`);
    console.log(`   ${this.getStatusIcon(latest.responseTime.status)} Temps de réponse: ${latest.responseTime.value}ms`);
    console.log(`   ${this.getStatusIcon(latest.errorRate.status)} Taux d'erreur: ${latest.errorRate.value}%`);
    console.log(`   👥 Utilisateurs actifs: ${latest.activeUsers.value}`);
    console.log('');

    // Ressources système
    console.log('💻 RESSOURCES SYSTÈME:');
    console.log(`   ${this.getStatusIcon(latest.cpuUsage.status)} CPU: ${latest.cpuUsage.value}%`);
    console.log(`   ${this.getStatusIcon(latest.memoryUsage.status)} Mémoire: ${latest.memoryUsage.value}%`);
    console.log('');

    // Alertes récentes
    const recentAlerts = this.alerts.filter(a => !a.resolved).slice(0, 3);
    if (recentAlerts.length > 0) {
      console.log('🚨 ALERTES ACTIVES:');
      recentAlerts.forEach(alert => {
        console.log(`   ${this.getAlertIcon(alert.severity)} ${alert.message}`);
      });
      console.log('');
    }

    // Statut global
    const globalStatus = this.calculateGlobalStatus(latest);
    console.log(`🌍 STATUT GLOBAL: ${this.getStatusIcon(globalStatus)} ${globalStatus.toUpperCase()}`);
    console.log('=' .repeat(70));
  }

  private getStatusIcon(status: string): string {
    switch (status) {
      case 'healthy': return '✅';
      case 'warning': return '⚠️';
      case 'critical': return '❌';
      default: return '⚪';
    }
  }

  private calculateGlobalStatus(metrics: HealthMetrics): 'healthy' | 'warning' | 'critical' {
    const statuses = Object.values(metrics).map(m => m.status);
    
    if (statuses.includes('critical')) return 'critical';
    if (statuses.includes('warning')) return 'warning';
    return 'healthy';
  }

  private async saveMetrics(): Promise<void> {
    try {
      const data = {
        timestamp: new Date().toISOString(),
        metrics: this.metrics.slice(-10), // Dernières 10 métriques
        alerts: this.alerts.slice(0, 10), // 10 dernières alertes
      };

      await fs.mkdir('./monitoring-data', { recursive: true });
      await fs.writeFile(
        './monitoring-data/current-metrics.json',
        JSON.stringify(data, null, 2)
      );

      // Sauvegarder un historique quotidien
      const date = new Date().toISOString().split('T')[0];
      await fs.writeFile(
        `./monitoring-data/metrics-${date}.json`,
        JSON.stringify(this.metrics, null, 2)
      );

    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde des métriques:', error);
    }
  }

  private stopMonitoring(): void {
    console.log('\n🛑 Arrêt du monitoring...');
    
    this.isRunning = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    // Sauvegarder les données finales
    this.saveMetrics().then(() => {
      console.log('💾 Données sauvegardées');
      console.log('👋 Monitoring arrêté');
      process.exit(0);
    });
  }

  async generateReport(): Promise<void> {
    if (this.metrics.length === 0) {
      console.log('❌ Aucune donnée disponible pour générer un rapport');
      return;
    }

    const report = {
      period: {
        start: this.metrics[0].uptime.timestamp,
        end: this.metrics[this.metrics.length - 1].uptime.timestamp,
        duration: this.metrics.length * 30, // secondes
      },
      summary: {
        averageResponseTime: Math.round(
          this.metrics.reduce((sum, m) => sum + m.responseTime.value, 0) / this.metrics.length
        ),
        averageErrorRate: Math.round(
          this.metrics.reduce((sum, m) => sum + m.errorRate.value, 0) / this.metrics.length * 100
        ) / 100,
        maxActiveUsers: Math.max(...this.metrics.map(m => m.activeUsers.value)),
        uptime: Math.round(
          this.metrics.filter(m => m.uptime.value > 0).length / this.metrics.length * 100
        ),
      },
      alerts: {
        total: this.alerts.length,
        critical: this.alerts.filter(a => a.severity === 'critical').length,
        high: this.alerts.filter(a => a.severity === 'high').length,
        medium: this.alerts.filter(a => a.severity === 'medium').length,
        low: this.alerts.filter(a => a.severity === 'low').length,
      },
    };

    await fs.writeFile(
      './monitoring-report.json',
      JSON.stringify(report, null, 2)
    );

    console.log('📊 Rapport de monitoring généré: monitoring-report.json');
  }
}

// Exécution du script
if (require.main === module) {
  const monitor = new ProductionMonitor();
  
  // Gestion des arguments de ligne de commande
  const command = process.argv[2];
  
  if (command === 'report') {
    monitor.generateReport();
  } else {
    monitor.startMonitoring().catch(console.error);
  }
}

export { ProductionMonitor };
