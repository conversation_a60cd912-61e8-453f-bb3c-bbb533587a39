#!/bin/bash

# Script de déploiement Kubernetes pour le frontend Audrey

# Variables
NAMESPACE="retreat-and-be"
DOCKER_REGISTRY="registry.retreat-and-be.com"
IMAGE_NAME="retreat-and-be/audrey-frontend"
IMAGE_TAG=${1:-latest}

# Vérifier si kubectl est installé
if ! command -v kubectl &> /dev/null; then
    echo "kubectl n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Vérifier si le namespace existe, sinon le créer
if ! kubectl get namespace $NAMESPACE &> /dev/null; then
    echo "Création du namespace $NAMESPACE..."
    kubectl create namespace $NAMESPACE
fi

# Construire l'image Docker
echo "Construction de l'image Docker..."
docker build -t $DOCKER_REGISTRY/$IMAGE_NAME:$IMAGE_TAG ..

# Pousser l'image vers le registre
echo "Envoi de l'image vers le registre Docker..."
docker push $DOCKER_REGISTRY/$IMAGE_NAME:$IMAGE_TAG

# Remplacer les variables dans les fichiers Kubernetes
echo "Préparation des fichiers de déploiement..."
sed -i "s|\${DOCKER_REGISTRY}|$DOCKER_REGISTRY|g" deployment.yaml
sed -i "s|\${IMAGE_TAG}|$IMAGE_TAG|g" deployment.yaml

# Appliquer les fichiers Kubernetes
echo "Déploiement des ressources Kubernetes..."
kubectl apply -f configmap.yaml -n $NAMESPACE
kubectl apply -f deployment.yaml -n $NAMESPACE
kubectl apply -f service.yaml -n $NAMESPACE
kubectl apply -f ingress.yaml -n $NAMESPACE
kubectl apply -f hpa.yaml -n $NAMESPACE
kubectl apply -f networkpolicy.yaml -n $NAMESPACE

# Vérifier le déploiement
echo "Vérification du déploiement..."
kubectl rollout status deployment/audrey-frontend -n $NAMESPACE

# Afficher les informations du service
echo "Informations du service:"
kubectl get service audrey-frontend -n $NAMESPACE

# Afficher l'URL d'accès
echo "URL d'accès: https://app.retreat-and-be.com"

echo "Déploiement terminé avec succès!"
