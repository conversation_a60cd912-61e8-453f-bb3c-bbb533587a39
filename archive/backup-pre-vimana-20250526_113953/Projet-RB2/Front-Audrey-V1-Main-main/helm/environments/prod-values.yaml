# Valeurs pour l'environnement de production
replicaCount: 3

image:
  repository: registry.retreat-and-be.com/retreat-and-be/audrey-frontend
  tag: prod
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: nginx
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload";
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";
  hosts:
    - host: app.retreat-and-be.com
      paths:
        - path: /(.*)
          pathType: Prefix
  tls:
    - secretName: audrey-frontend-prod-tls
      hosts:
        - app.retreat-and-be.com

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 200m
    memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

nodeSelector:
  tier: frontend

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - audrey-frontend
        topologyKey: kubernetes.io/hostname

tolerations:
  - key: "node-role.kubernetes.io/frontend"
    operator: "Exists"
    effect: "NoSchedule"

env:
  NODE_ENV: production
  API_URL: https://api.retreat-and-be.com
  SECURITY_URL: https://api.retreat-and-be.com/security
  AGENT_IA_URL: https://api.retreat-and-be.com/agent-ia
  SOCIAL_URL: https://api.retreat-and-be.com/social
  FINANCIAL_URL: https://api.retreat-and-be.com/financial
  ENABLE_LOGS: "true"
  ENABLE_DEBUG: "false"

# Configuration pour la production
production:
  enableCaching: true
  cacheTTL: 86400
  enableRateLimiting: true
  rateLimit: 200
  enableCDN: true
  cdnUrl: https://cdn.retreat-and-be.com
  enableCompression: true
  compressionLevel: 6
