name: CI/CD Pipeline - Retreat And Be

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  CYPRESS_CACHE_FOLDER: ~/.cache/Cypress

jobs:
  # Job 1: Tests unitaires et linting
  unit-tests:
    name: Tests Unitaires & Linting
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run ESLint
        run: npm run lint
        
      - name: Run TypeScript check
        run: npm run type-check
        
      - name: Run unit tests
        run: npm run test:coverage
        
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: unit-test-results
          path: |
            coverage/
            test-results/

  # Job 2: Build et validation
  build:
    name: Build & Validation
    runs-on: ubuntu-latest
    needs: unit-tests
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        
      - name: Check bundle size
        run: |
          npm run build:analyze
          ls -la dist/
          
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: dist/
          retention-days: 7

  # Job 3: Tests End-to-End avec Cypress
  e2e-tests:
    name: Tests E2E (Cypress)
    runs-on: ubuntu-latest
    needs: build
    
    strategy:
      matrix:
        browser: [chrome, firefox, edge]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: dist/
          
      - name: Start application
        run: |
          npm run preview &
          npx wait-on http://localhost:3000 --timeout 60000
          
      - name: Run Cypress E2E tests
        uses: cypress-io/github-action@v6
        with:
          browser: ${{ matrix.browser }}
          record: true
          parallel: true
          group: 'E2E Tests - ${{ matrix.browser }}'
          spec: 'cypress/e2e/**/*.cy.ts'
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Upload Cypress screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots-${{ matrix.browser }}
          path: cypress/screenshots/
          
      - name: Upload Cypress videos
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-videos-${{ matrix.browser }}
          path: cypress/videos/

  # Job 4: Tests de composants Cypress
  component-tests:
    name: Tests de Composants (Cypress)
    runs-on: ubuntu-latest
    needs: unit-tests
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run Cypress component tests
        uses: cypress-io/github-action@v6
        with:
          component: true
          record: true
          group: 'Component Tests'
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Job 5: Tests d'accessibilité
  accessibility-tests:
    name: Tests d'Accessibilité
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: dist/
          
      - name: Start application
        run: |
          npm run preview &
          npx wait-on http://localhost:3000 --timeout 60000
          
      - name: Run accessibility tests
        run: npm run test:a11y
        
      - name: Upload accessibility report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: accessibility-report
          path: accessibility-report/

  # Job 6: Tests de performance
  performance-tests:
    name: Tests de Performance
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: dist/
          
      - name: Start application
        run: |
          npm run preview &
          npx wait-on http://localhost:3000 --timeout 60000
          
      - name: Install K6
        run: |
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
          
      - name: Run performance tests
        run: k6 run tests/performance/load-test.js
        env:
          BASE_URL: http://localhost:3000
          
      - name: Upload performance report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-report
          path: performance-report/

  # Job 7: Security scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: unit-tests
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Run npm audit
        run: npm audit --audit-level=moderate
        
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # Job 8: Lighthouse CI
  lighthouse:
    name: Lighthouse CI
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: dist/
          
      - name: Start application
        run: |
          npm run preview &
          npx wait-on http://localhost:3000 --timeout 60000
          
      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Job 9: Deploy (seulement sur main)
  deploy:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [e2e-tests, component-tests, accessibility-tests, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          path: dist/
          
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Ici, ajouter les commandes de déploiement
          # Par exemple: rsync, scp, ou API calls vers le service de déploiement
          
      - name: Notify deployment
        if: success()
        run: |
          echo "Deployment successful!"
          # Ici, ajouter les notifications (Slack, Discord, etc.)

  # Job 10: Notification finale
  notify:
    name: Notification
    runs-on: ubuntu-latest
    needs: [deploy]
    if: always()
    
    steps:
      - name: Notify team
        run: |
          if [ "${{ needs.deploy.result }}" == "success" ]; then
            echo "✅ Pipeline completed successfully!"
          else
            echo "❌ Pipeline failed!"
          fi
          # Ici, ajouter les notifications vers Slack, Discord, etc.
