# Audrey Frontend

This is the frontend application for the Retreat And Be platform, built with React and TypeScript.

## Overview

The Audrey Frontend provides a modern, responsive user interface for the Retreat And Be platform. It is designed to be deployed as a containerized application in a Kubernetes cluster.

## Features

- Modern React application with TypeScript
- Responsive design for all devices
- Integration with backend APIs
- Authentication and authorization
- Search and filtering capabilities
- Interactive maps and visualizations
- Booking and payment processing
- Comprehensive content management system
- Content analytics and performance tracking
- Advanced content search with filtering

## Getting Started

### Prerequisites

- Node.js 16.x or higher
- npm 8.x or higher
- Docker (for containerized deployment)
- Kubernetes cluster (for production deployment)

### Development

1. Clone the repository:

```bash
git clone https://github.com/retreat-and-be/projet-rb2.git
cd projet-rb2/Front-Audrey-V1-Main-main
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm start
```

The application will be available at http://localhost:3000.

### Building for Production

```bash
npm run build
```

This will create a production-ready build in the `build` folder.

## Docker Deployment

### Building the Docker Image

```bash
docker build -t audrey-frontend:latest .
```

### Running the Docker Container

```bash
docker run -p 3000:80 audrey-frontend:latest
```

### Using Docker Compose

```bash
docker-compose up -d
```

## Kubernetes Deployment

### Using kubectl

```bash
cd kubernetes
./deploy.sh [tag]
```

### Using Helm

```bash
helm install audrey-frontend ./helm
```

### Multi-Environment Deployment

For deploying to different environments (development, staging, production):

```bash
./deploy-env.sh [options] <environment>
```

Example:
```bash
./deploy-env.sh --tag v1.0.0 prod
```

### Multi-Region Deployment

For deploying to multiple regions (EU, US, AP):

```bash
./deploy-multi-region.sh [options] <regions>
```

Example:
```bash
./deploy-multi-region.sh --tag v1.0.0 eu us ap
```

## Documentation

- [Deployment Guide](./DEPLOYMENT.md)
- [Kubernetes Integration](./docs/KUBERNETES-INTEGRATION.md)
- [Microservices Integration](./docs/MICROSERVICES-INTEGRATION.md)
- [Production Readiness Checklist](./docs/PRODUCTION-READINESS.md)
- [Disaster Recovery Plan](./docs/DISASTER-RECOVERY.md)
- [Troubleshooting Guide](./docs/TROUBLESHOOTING.md)

## CI/CD

The application is configured with GitHub Actions for continuous integration and deployment:

- Automated builds on push to main branch
- Security scanning with Trivy
- Deployment to Kubernetes cluster

## Monitoring and Observability

- Prometheus metrics exposed at `/metrics`
- Grafana dashboards for monitoring
- Centralized logging with ELK stack
- Blackbox exporter for endpoint monitoring
- Alerting with Prometheus Alertmanager
- Multi-region health checks
- CDN performance monitoring

## Security

- TLS encryption
- Content Security Policy
- Network policies
- Regular security scanning
- Multi-region deployment for high availability
- CDN integration for improved security and performance
- Automated certificate management
- Security headers configuration

## Implémentations de Sécurité

Ce projet intègre plusieurs fonctionnalités de sécurité avancées pour protéger l'application web et ses utilisateurs.

### Content Security Policy (CSP)

La Content Security Policy est mise en œuvre avec :

- Un mode **report-only** actuellement actif via meta tag (pour évaluation)
- Une configuration prête pour le mode de production dans `security-headers.conf`
- Un système de reporting qui :
  - Enregistre les violations en console durant le développement
  - Offre un moniteur visuel des violations en mode développement
  - Est configuré pour envoyer les violations à un endpoint backend en production

### Protection CSRF

La protection contre les attaques Cross-Site Request Forgery (CSRF) est implémentée avec :

- Génération de tokens CSRF cryptographiquement sécurisés
- Injection automatique dans les formulaires HTML
- Interception des requêtes AJAX pour l'ajout des tokens CSRF
- Fonctionnement compatible avec fetch et axios

### En-têtes de Sécurité HTTP

Les en-têtes de sécurité suivants sont configurés :

- **HSTS** (HTTP Strict Transport Security) : Force l'utilisation de HTTPS
- **X-Frame-Options** : Prévient le clickjacking
- **X-Content-Type-Options** : Empêche le MIME-sniffing
- **Referrer-Policy** : Contrôle les informations de référent
- **Permissions-Policy** : Restreint l'utilisation de fonctionnalités sensibles du navigateur
- **X-XSS-Protection** : Couche supplémentaire contre les attaques XSS pour les anciens navigateurs

## Configuration en Développement

```bash
# Installation des dépendances
npm install

# Démarrage du serveur de développement
npm start
```

## Déploiement en Production

1. Construire l'application :
   ```bash
   npm run build
   ```

2. Configurer le serveur web avec les en-têtes de sécurité appropriés :
   - Pour Nginx : inclure `security-headers.conf` dans la configuration du serveur
   - Pour Apache : adapter les directives pour le format .htaccess

3. Configurer un endpoint backend pour recevoir les rapports CSP :
   ```javascript
   // Dans le code frontend, avant déploiement
   import { configureCSPReporting } from './api/cspReport';

   configureCSPReporting({
     productionEndpoint: 'https://api.votre-domaine.com/security/csp-report'
   });
   ```

## Considérations pour les Développeurs

- Les tokens CSP et CSRF sont initialisés automatiquement au démarrage de l'application
- En développement, un moniteur de violations CSP apparaît automatiquement lorsque des violations sont détectées
- Le mode CSP report-only permet de tester les règles sans bloquer la fonctionnalité
- Avant de passer en mode CSP d'application stricte, vérifier qu'aucune violation légitime n'est signalée

## Système de Gestion de Contenu

Le frontend intègre un système complet de gestion de contenu pour les partenaires et administrateurs de la plateforme.

### Fonctionnalités Principales

- **Gestion de Contenu**
  - Publication, archivage et suppression de contenu
  - Édition des métadonnées (titre, description, tags)
  - Gestion des paramètres de confidentialité
  - Restauration de contenu archivé ou supprimé
  - Sélection et actions par lots sur plusieurs contenus

- **Téléchargement de Contenu**
  - Interface de glisser-déposer pour les fichiers
  - Prévisualisation des images et vidéos
  - Ajout de métadonnées et de tags
  - Gestion des paramètres de confidentialité
  - Barre de progression pour les téléchargements

- **Analyses de Contenu**
  - Statistiques de performance (vues, likes, commentaires)
  - Graphiques d'engagement au fil du temps
  - Répartition du contenu par statut
  - Classement du contenu le plus performant
  - Filtrage par période (semaine, mois, année)

- **Recherche de Contenu**
  - Recherche par mots-clés
  - Filtrage par type de contenu, date et statut
  - Sélection par tags
  - Tri par pertinence ou date
  - Prévisualisation des résultats

### Accès aux Fonctionnalités

Les fonctionnalités de gestion de contenu sont accessibles via les routes suivantes :

- `/content-management` - Gestion principale du contenu
- `/content-analytics` - Analyses et statistiques de contenu
- `/content-search` - Recherche avancée de contenu

### Intégration

Le système de gestion de contenu est intégré avec :

- Le système d'authentification pour la gestion des permissions
- Les services d'analyse pour le suivi des performances
- Les services de stockage pour les fichiers multimédias
- Les services sociaux pour l'engagement des utilisateurs

## Ressources

- [Documentation complète](./docs/SECURITY-HEADERS.md)
- [MDN Content Security Policy](https://developer.mozilla.org/fr/docs/Web/HTTP/CSP)
- [OWASP CSRF Prevention](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html)
- [Documentation de la Gestion de Contenu](./docs/CONTENT-MANAGEMENT.md)

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
