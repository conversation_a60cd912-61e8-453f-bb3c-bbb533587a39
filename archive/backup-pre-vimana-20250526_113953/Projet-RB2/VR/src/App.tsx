import React from 'react';
import { Header } from '@/components/layout/Header';
import { RetreatList } from '@/features/retreats/components/RetreatList';
import { SAMPLE_RETREATS } from '@/features/retreats/data/sample-retreats';

function App() {
  const handleJoinRetreat = (retreatId: string) => {
    console.log(`Joining retreat: ${retreatId
}`);
    // TODO: Implement join logic;
  }

  return (;
    <div className = "min-h-screen bg-gray-100">
      <Header />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <RetreatList
          retreats={SAMPLE_RETREATS
}
          onJoinRetreat = {handleJoinRetreat
}
        />
      </main>
    </div>
  );
}

export default App;