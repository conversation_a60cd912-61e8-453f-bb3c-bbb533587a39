
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Rapport de correction TypeScript</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            h1, h2, h3 {
                color: #2c3e50;
            }
            .summary {
                background-color: #f8f9fa;
                border-left: 4px solid #4caf50;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 4px;
            }
            .stats {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                margin-bottom: 20px;
            }
            .stat-card {
                background-color: #fff;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                flex: 1;
                min-width: 200px;
            }
            .stat-card h3 {
                margin-top: 0;
                color: #3498db;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            .success {
                color: #4caf50;
            }
            .failure {
                color: #f44336;
            }
            .file-path {
                font-family: monospace;
                word-break: break-all;
            }
        </style>
    </head>
    <body>
        <h1>Rapport de correction TypeScript</h1>
        
        <div class="summary">
            <h2>Résumé</h2>
            <p>
                Analyse effectuée le 24/03/2025 à 17:40:37.<br>
                Durée totale: 101.14 secondes.
            </p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>Fichiers</h3>
                <p><strong>3</strong> fichiers analysés</p>
                <p><strong>3</strong> fichiers corrigés</p>
                <p><strong>100.0%</strong> de taux de correction</p>
            </div>
            
            <div class="stat-card">
                <h3>Erreurs</h3>
                <p><strong>493</strong> erreurs avant correction</p>
                <p><strong>936</strong> erreurs après correction</p>
                <p><strong>-443</strong> erreurs corrigées</p>
            </div>
            
            <div class="stat-card">
                <h3>Efficacité</h3>
                <p><strong>-89.9%</strong> des erreurs corrigées</p>
                <p><strong>-4.4</strong> erreurs corrigées par seconde</p>
            </div>
        </div>
        
        <h2>Détails par fichier</h2>
        <table>
            <thead>
                <tr>
                    <th>Fichier</th>
                    <th>Erreurs avant</th>
                    <th>Erreurs après</th>
                    <th>Erreurs corrigées</th>
                    <th>Méthode</th>
                    <th>Statut</th>
                </tr>
            </thead>
            <tbody>
    
                <tr>
                    <td class="file-path">frontend/src/hooks/useAppState.ts</td>
                    <td>154</td>
                    <td>143</td>
                    <td>11</td>
                    <td>Analyse</td>
                    <td class="success">Corrigé</td>
                </tr>
        
                <tr>
                    <td class="file-path">frontend/src/hooks/useCustomShortcuts.ts</td>
                    <td>162</td>
                    <td>353</td>
                    <td>-191</td>
                    <td>Générique</td>
                    <td class="success">Corrigé</td>
                </tr>
        
                <tr>
                    <td class="file-path">frontend/src/hooks/usePerformanceMetrics.ts</td>
                    <td>177</td>
                    <td>440</td>
                    <td>-263</td>
                    <td>Générique</td>
                    <td class="success">Corrigé</td>
                </tr>
        
            </tbody>
        </table>
    </body>
    </html>
    