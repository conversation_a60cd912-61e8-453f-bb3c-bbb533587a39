/**
 * Mock pour localforage
 * Utilisé par Jest pour simuler les opérations de stockage local
 */
const localforage = {
  config: jest.fn(),
  getItem: jest.fn().mockResolvedValue(null),
  setItem: jest.fn().mockResolvedValue(null),
  removeItem: jest.fn().mockResolvedValue(null),
  clear: jest.fn().mockResolvedValue(null),
  length: jest.fn().mockResolvedValue(0),
  key: jest.fn().mockResolvedValue(null),
  keys: jest.fn().mockResolvedValue([]),
  iterate: jest.fn().mockImplementation((callback) => Promise.resolve()),
  _driver: 'MOCK',
  createInstance: jest.fn().mockImplementation(() => localforage),
  INDEXEDDB: 'MOCK_INDEXEDDB',
  WEBSQL: 'MOCK_WEBSQL',
  LOCALSTORAGE: 'MOCK_LOCALSTORAGE'
};

module.exports = localforage;
