[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: mark a test as a unit test
    integration: mark a test as an integration test
    api: mark a test as an API test
    slow: mark a test as slow
    asyncio: mark a test as an asyncio test
    i18n: mark a test as an internationalization test
    calendar: mark a test as a calendar integration test
    maps: mark a test as a maps integration test
    analytics: mark a test as an analytics integration test
    notifications: mark a test as a notifications test
addopts = -v
