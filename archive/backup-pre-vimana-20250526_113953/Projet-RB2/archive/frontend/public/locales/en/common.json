{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "search": "Search", "filter": "Filter", "refresh": "Refresh", "create": "Create", "view": "View", "details": "Details", "noData": "No data available"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password"}, "booking": {"title": "Book Your Session", "selectDate": "Select Date", "selectTime": "Select Time", "participants": "Number of Participants", "specialRequests": "Special Requests", "bookNow": "Book Now", "confirmBooking": "Confirm Booking", "bookingSuccess": "Booking Successful!", "bookingError": "Booking Failed"}, "accessibility": {"skipToMain": "Skip to main content", "menuButton": "Toggle menu", "closeButton": "Close", "expandButton": "Expand", "collapseButton": "Collapse", "nextPage": "Next page", "previousPage": "Previous page"}, "dates": {"today": "Today", "tomorrow": "Tomorrow", "thisWeek": "This Week", "nextWeek": "Next Week"}, "errors": {"required": "This field is required", "invalidEmail": "Invalid email address", "passwordMismatch": "Passwords do not match", "serverError": "Server error occurred", "networkError": "Network error occurred"}, "analysis": {"title": "Analyses", "newAnalysis": "New Analysis", "detail": "Analysis Details", "name": "Name", "type": "Type", "status": "Status", "priority": "Priority", "createdAt": "Creation Date", "completedAt": "Completion Date", "result": "Result", "score": "Score", "issues": "Issues", "logs": "Logs", "actions": "Actions", "rerun": "<PERSON><PERSON>", "cancelAnalysis": "Cancel Analysis", "filters": "Filters", "searchPlaceholder": "Search for an analysis...", "noAnalysisFound": "No analysis found", "loading": "Loading analyses...", "createSuccess": "Analysis created successfully", "cancelSuccess": "Analysis cancelled successfully", "rerunSuccess": "Analysis rerun successfully", "error": "An error occurred while processing the analysis", "statusPending": "Pending", "statusInProgress": "In Progress", "statusCompleted": "Completed", "statusFailed": "Failed", "priorityLow": "Low", "priorityMedium": "Medium", "priorityHigh": "High", "priorityCritical": "Critical", "typeCodeQuality": "Code Quality", "typeSecurity": "Security", "typePerformance": "Performance", "typeArchitecture": "Architecture", "typeCustom": "Custom"}}