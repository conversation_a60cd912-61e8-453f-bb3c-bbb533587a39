<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Module</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #f0f0f0;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #38C283;
    }
    #result {
      margin-top: 1rem;
      padding: 1rem;
      background-color: #f8f8f8;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Test Module</h1>
    <p>Testing JavaScript module loading</p>
    <div id="result">Loading...</div>
  </div>

  <script type="module">
    try {
      // Import the module
      import { say<PERSON>ello } from '/test.js';
      
      // Call the function
      const message = sayHello();
      
      // Display the result
      document.getElementById('result').textContent = message;
    } catch (error) {
      console.error('Error loading module:', error);
      document.getElementById('result').textContent = 'Error: ' + error.message;
    }
  </script>
</body>
</html>
