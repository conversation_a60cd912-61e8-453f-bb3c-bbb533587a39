module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: [
    '<rootDir>/src/tests/setup/mockExportDependencies.ts',
    '<rootDir>/src/tests/setup/mockDOMExport.ts'
  ],
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/src/test/__mocks__/fileMock.js'
  },
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      isolatedModules: true,
      useESM: true,
      tsconfig: {
        jsx: 'react-jsx',
        allowJs: true,
        esModuleInterop: true,
        module: 'ESNext'
      }
    }]
  },
  transformIgnorePatterns: [
    'node_modules/(?!(jspdf|jspdf-autotable|@reduxjs)/)'
  ],
  testMatch: [
    '**/src/tests/services/exportService.test.ts'
  ],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/services/exportService.ts'
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node']
}; 