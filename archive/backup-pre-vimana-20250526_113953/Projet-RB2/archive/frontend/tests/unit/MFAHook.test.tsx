import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react-hooks';
import { useMFA } from '../../src/hooks/useMFA';
import { MFAService } from '../../src/services/auth/mfa.service';

// Mock the MFA service
vi.mock('../../src/services/auth/mfa.service');

describe('useMFA Hook', () => {
  let mockMFAService: MFAService;
  
  beforeEach(() => {
    // Create a new mock for each test
    mockMFAService = new MFAService('http://localhost:3000/api') as jest.Mocked<MFAService>;
    
    // Reset mocks
    vi.resetAllMocks();
    
    // Setup mock implementations
    (mockMFAService.setupTwoFactor as any).mockResolvedValue({
      secret: 'TEST_SECRET',
      qrCodeUrl: 'data:image/png;base64,test-qr-code',
    });
    
    (mockMFAService.enableTwoFactor as any).mockResolvedValue({
      message: 'Two-factor authentication enabled successfully',
    });
    
    (mockMFAService.verifyTwoFactor as any).mockResolvedValue({
      message: 'Two-factor authentication verified successfully',
    });
    
    (mockMFAService.disableTwoFactor as any).mockResolvedValue({
      message: 'Two-factor authentication disabled successfully',
    });
    
    (mockMFAService.checkTwoFactorStatus as any).mockResolvedValue({
      enabled: true,
      verified: false,
    });
    
    // Mock the constructor to return our mock instance
    (MFAService as any).mockImplementation(() => mockMFAService);
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  describe('setupTwoFactor', () => {
    it('should set loading state during setup', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      expect(result.current.isLoading).toBe(false);
      
      act(() => {
        result.current.setupTwoFactor();
      });
      
      expect(result.current.isLoading).toBe(true);
      
      await waitForNextUpdate();
      
      expect(result.current.isLoading).toBe(false);
    });
    
    it('should update setup data on successful setup', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.setupTwoFactor();
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.setupTwoFactor).toHaveBeenCalled();
      expect(result.current.setupData).toEqual({
        secret: 'TEST_SECRET',
        qrCodeUrl: 'data:image/png;base64,test-qr-code',
      });
      expect(result.current.error).toBeNull();
    });
    
    it('should set error state on setup failure', async () => {
      const errorMessage = 'Failed to set up two-factor authentication';
      (mockMFAService.setupTwoFactor as any).mockRejectedValue(new Error(errorMessage));
      
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.setupTwoFactor();
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.setupTwoFactor).toHaveBeenCalled();
      expect(result.current.setupData).toBeNull();
      expect(result.current.error).toBe(errorMessage);
    });
  });
  
  describe('enableTwoFactor', () => {
    it('should set loading state during enable', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      expect(result.current.isLoading).toBe(false);
      
      act(() => {
        result.current.enableTwoFactor('TEST_SECRET', '123456');
      });
      
      expect(result.current.isLoading).toBe(true);
      
      await waitForNextUpdate();
      
      expect(result.current.isLoading).toBe(false);
    });
    
    it('should call enableTwoFactor with correct params', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.enableTwoFactor('TEST_SECRET', '123456');
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.enableTwoFactor).toHaveBeenCalledWith('TEST_SECRET', '123456');
      expect(result.current.error).toBeNull();
    });
    
    it('should set error state on enable failure', async () => {
      const errorMessage = 'Invalid verification code';
      (mockMFAService.enableTwoFactor as any).mockRejectedValue(new Error(errorMessage));
      
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.enableTwoFactor('TEST_SECRET', '111111');
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.enableTwoFactor).toHaveBeenCalledWith('TEST_SECRET', '111111');
      expect(result.current.error).toBe(errorMessage);
    });
  });
  
  describe('verifyTwoFactor', () => {
    it('should set loading state during verification', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      expect(result.current.isLoading).toBe(false);
      
      act(() => {
        result.current.verifyTwoFactor('123456');
      });
      
      expect(result.current.isLoading).toBe(true);
      
      await waitForNextUpdate();
      
      expect(result.current.isLoading).toBe(false);
    });
    
    it('should call verifyTwoFactor with correct params', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.verifyTwoFactor('123456');
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.verifyTwoFactor).toHaveBeenCalledWith('123456');
      expect(result.current.error).toBeNull();
    });
    
    it('should set error state on verification failure', async () => {
      const errorMessage = 'Invalid verification code';
      (mockMFAService.verifyTwoFactor as any).mockRejectedValue(new Error(errorMessage));
      
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.verifyTwoFactor('111111');
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.verifyTwoFactor).toHaveBeenCalledWith('111111');
      expect(result.current.error).toBe(errorMessage);
    });
  });
  
  describe('disableTwoFactor', () => {
    it('should set loading state during disable', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      expect(result.current.isLoading).toBe(false);
      
      act(() => {
        result.current.disableTwoFactor('123456');
      });
      
      expect(result.current.isLoading).toBe(true);
      
      await waitForNextUpdate();
      
      expect(result.current.isLoading).toBe(false);
    });
    
    it('should call disableTwoFactor with correct params', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.disableTwoFactor('123456');
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.disableTwoFactor).toHaveBeenCalledWith('123456');
      expect(result.current.error).toBeNull();
    });
    
    it('should set error state on disable failure', async () => {
      const errorMessage = 'Invalid verification code';
      (mockMFAService.disableTwoFactor as any).mockRejectedValue(new Error(errorMessage));
      
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.disableTwoFactor('111111');
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.disableTwoFactor).toHaveBeenCalledWith('111111');
      expect(result.current.error).toBe(errorMessage);
    });
  });
  
  describe('checkTwoFactorStatus', () => {
    it('should set loading state during status check', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      expect(result.current.isLoading).toBe(false);
      
      act(() => {
        result.current.checkTwoFactorStatus();
      });
      
      expect(result.current.isLoading).toBe(true);
      
      await waitForNextUpdate();
      
      expect(result.current.isLoading).toBe(false);
    });
    
    it('should update status on successful check', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.checkTwoFactorStatus();
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.checkTwoFactorStatus).toHaveBeenCalled();
      expect(result.current.twoFactorStatus).toEqual({
        enabled: true,
        verified: false,
      });
      expect(result.current.error).toBeNull();
    });
    
    it('should set error state on status check failure', async () => {
      const errorMessage = 'Failed to check two-factor authentication status';
      (mockMFAService.checkTwoFactorStatus as any).mockRejectedValue(new Error(errorMessage));
      
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.checkTwoFactorStatus();
      });
      
      await waitForNextUpdate();
      
      expect(mockMFAService.checkTwoFactorStatus).toHaveBeenCalled();
      expect(result.current.twoFactorStatus).toBeNull();
      expect(result.current.error).toBe(errorMessage);
    });
  });
  
  describe('resetError', () => {
    it('should reset error state', async () => {
      const errorMessage = 'Test error';
      (mockMFAService.setupTwoFactor as any).mockRejectedValue(new Error(errorMessage));
      
      const { result, waitForNextUpdate } = renderHook(() => useMFA());
      
      act(() => {
        result.current.setupTwoFactor();
      });
      
      await waitForNextUpdate();
      
      expect(result.current.error).toBe(errorMessage);
      
      act(() => {
        result.current.resetError();
      });
      
      expect(result.current.error).toBeNull();
    });
  });
}); 