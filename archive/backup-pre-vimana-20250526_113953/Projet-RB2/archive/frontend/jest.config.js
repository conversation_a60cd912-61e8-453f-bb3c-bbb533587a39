/**
 * Configuration Jest pour les tests React
 */

export default {
  // Environnement de test
  testEnvironment: 'jsdom',
  
  // Extensions de fichiers à traiter
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  
  // Transformations pour les fichiers
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.json',
    }],
    '^.+\\.(js|jsx)$': 'babel-jest',
  },
  
  // Ignorer les fichiers de node_modules sauf ceux spécifiés
  transformIgnorePatterns: [
    '/node_modules/(?!(@testing-library|react-router|react-router-dom)/)',
  ],
  
  // Chemins pour les imports
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/src/__mocks__/fileMock.js',
  },
  
  // Configuration de la couverture de code
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/main.tsx',
    '!src/vite-env.d.ts',
  ],
  
  // Répertoires pour les tests
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}',
  ],
  
  // Configuration de setupFiles
  setupFiles: ['<rootDir>/src/setupTests.js'],
  
  // Configuration de setupFilesAfterEnv
  setupFilesAfterEnv: ['<rootDir>/src/setupTestsAfterEnv.js'],
  
  // Autres options
  testPathIgnorePatterns: ['<rootDir>/node_modules/'],
  watchPathIgnorePatterns: ['<rootDir>/node_modules/'],
  
  // Verbose output
  verbose: true,
};
