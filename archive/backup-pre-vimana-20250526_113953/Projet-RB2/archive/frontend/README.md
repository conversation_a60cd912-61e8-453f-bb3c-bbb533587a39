# Retreat & Be Frontend

## Description
Retreat & Be est une plateforme complète dédiée aux retraites bien-être et au développement personnel. Elle offre une expérience utilisateur moderne et intuitive, intégrant des fonctionnalités avancées comme la réalité virtuelle, les NFTs, et un système de communauté robuste.

## Intégration de Front-Audrey-V1-Main-main

Ce projet intègre désormais les fonctionnalités de Front-Audrey-V1-Main-main. Cette intégration apporte de nouvelles fonctionnalités et améliore l'expérience utilisateur globale de l'application.

### Fonctionnalités ajoutées
- Interface utilisateur améliorée avec des composants Atomic Design
- Nouvelles pages et templates pour les retraites et le blog
- Système de recherche avancé pour les retraites
- Formulaire de retour utilisateur
- Intégration avec tous les microservices du projet

### Documentation de l'intégration
- [Guide de démarrage rapide](./docs/QUICKSTART.md)
- [Documentation technique](./docs/AUDREY-INTEGRATION.md)
- [Guide de déploiement](./docs/DEPLOYMENT.md)
- [Guide de formation](./docs/TRAINING.md)
- [Guide de contribution](./docs/CONTRIBUTING.md)
- [Roadmap des futures fonctionnalités](./docs/ROADMAP-FUTURE.md)

## Fonctionnalités Principales

### Navigation et Interface
- Header responsive avec navigation carrousel
- Design moderne et épuré
- Support multi-langues
- Mode sombre/clair
- Navigation mobile optimisée

### Authentification et Sécurité
- Système d'authentification complet
  - Inscription et connexion sécurisées
  - Authentification à deux facteurs (2FA)
  - Gestion des codes de secours
  - Protection contre les attaques CSRF
- Intégration TON Wallet
- Gestion des sessions avec JWT
- Protection des routes sensibles
- Refresh token automatique

### Modules Principaux
- **Retraites**: Exploration et réservation de retraites
- **Formation**: Accès aux cours et formations
- **Marketplace**: Achat et vente de NFTs
- **Communauté**: Réseau social et messagerie
- **Services**: Location de voitures, assurance, stockage, etc.

## Prérequis Techniques
- Node.js >= 16.x
- npm >= 8.x
- Vite
- React 18+
- TypeScript 4.x+

## Installation

1. Cloner le repository :
```bash
git clone [URL_DU_REPO]
cd frontend
```

2. Installer les dépendances :
```bash
npm install
```

3. Configurer les variables d'environnement :
```bash
cp .env.example .env
```

4. Lancer le serveur de développement :
```bash
npm run dev
```

## Tests

Le projet utilise Jest et React Testing Library pour les tests unitaires et d'intégration.

### Commandes de Test
```bash
# Lancer tous les tests
npm test

# Mode watch pour le développement
npm run test:watch

# Générer un rapport de couverture
npm run test:coverage

# Tests d'intégration
npm run test:e2e
```

### Structure des Tests
```
src/
├── components/
│   ├── auth/
│   │   ├── __tests__/
│   │   │   ├── LoginPage.test.tsx
│   │   │   └── RegisterPage.test.tsx
│   └── security/
│       └── __tests__/
│           ├── SecuritySettings.test.tsx
│           └── TwoFactorSetup.test.tsx
└── services/
    └── __tests__/
        └── authService.test.ts
```

### Couverture des Tests
Les tests couvrent :
- Service d'authentification
- Composants de connexion et d'inscription
- Configuration et gestion 2FA
- Protection des routes
- Gestion des tokens
- Validation des formulaires

## Structure du Projet

```
frontend/
├── public/                 # Assets statiques
├── src/
│   ├── components/         # Composants réutilisables
│   │   ├── auth/          # Composants d'authentification
│   │   ├── common/        # Composants communs
│   │   ├── security/      # Composants de sécurité (2FA, etc.)
│   │   ├── layout/        # Layouts et templates
│   │   └── randbefrontend/ # Composants migrés d'Audrey-V1
│   │       ├── atoms/     # Composants atomiques
│   │       ├── molecules/ # Composants moléculaires
│   │       ├── organisms/ # Composants organismes
│   │       ├── templates/ # Templates
│   │       └── ui/        # Composants UI génériques
│   ├── contexts/          # Contextes React
│   │   ├── AuthContext.tsx
│   │   └── SnackbarContext.tsx
│   ├── services/          # Services
│   │   └── authService.ts
│   ├── hooks/             # Hooks personnalisés
│   ├── utils/             # Utilitaires
│   ├── pages/             # Pages de l'application
│   │   └── randbefrontend/ # Pages migrées d'Audrey-V1
│   ├── styles/            # Styles globaux
│   │   ├── audrey-styles.css     # Styles spécifiques à Audrey-V1
│   │   └── audrey-integration.css # Adaptations pour l'intégration
│   ├── config/            # Configuration
│   │   └── audrey-integration.ts # Configuration pour l'intégration
│   ├── routes/            # Routes de l'application
│   │   ├── audreyRoutes.tsx      # Routes d'Audrey-V1
│   │   └── combinedRoutes.tsx    # Combinaison des routes
│   └── App.tsx            # Composant racine
├── scripts/               # Scripts utilitaires
│   ├── audrey-migration.js       # Script de migration
│   ├── deploy-staging.js         # Script de déploiement staging
│   ├── deploy-production.js      # Script de déploiement production
│   ├── verify-integration.js     # Script de vérification
│   └── run-audrey-tests.js       # Script d'exécution des tests
├── docs/                  # Documentation
│   ├── AUDREY-INTEGRATION.md     # Documentation technique
│   ├── DEPLOYMENT.md             # Guide de déploiement
│   ├── TRAINING.md               # Guide de formation
│   ├── CONTRIBUTING.md           # Guide de contribution
│   ├── QUICKSTART.md             # Guide de démarrage rapide
│   └── ROADMAP-FUTURE.md         # Roadmap des futures fonctionnalités
```

## Scripts Disponibles

```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "cypress run",
    "test:audrey": "node --experimental-modules scripts/run-audrey-tests.js",
    "migrate:audrey": "node --experimental-modules scripts/audrey-migration.js",
    "verify:audrey": "node --experimental-modules scripts/verify-integration.js",
    "deploy:staging": "node --experimental-modules scripts/deploy-staging.js",
    "deploy:prod": "node --experimental-modules scripts/deploy-production.js",
    "lint": "eslint src --ext ts,tsx",
    "format": "prettier --write \"src/**/*.{ts,tsx,css,scss}\""
  }
}
```

## Guide d'Authentification

### Configuration 2FA
1. Accédez aux paramètres de sécurité
2. Activez l'authentification à deux facteurs
3. Scannez le QR code avec une application d'authentification
4. Entrez le code de vérification
5. Sauvegardez les codes de secours en lieu sûr

### Utilisation
- Lors de la connexion, un code 2FA sera demandé si activé
- Utilisez votre application d'authentification pour générer le code
- En cas de perte d'accès, utilisez un code de secours

## Contribution
1. Fork le projet
2. Créez votre branche (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

### Standards de Code
- ESLint pour le linting
- Prettier pour le formatage
- Husky pour les pre-commit hooks
- Tests requis pour les nouvelles fonctionnalités

## License
Ce projet est sous licence MIT.

## Système de Gestion Adaptative de la Bande Passante

Notre application intègre un système sophistiqué de gestion adaptative de la bande passante qui s'adapte dynamiquement aux conditions réseau de l'utilisateur. Ce système permet d'optimiser l'expérience utilisateur dans des environnements à connectivité variable ou limitée.

### Fonctionnalités principales

- **Détection automatique de la qualité réseau** - Utilise l'API Network Information et d'autres métriques pour évaluer la connexion de l'utilisateur.
- **Stratégies adaptatives de chargement** - Ajuste le chargement du contenu et sa présentation en fonction des conditions réseau.
- **Mode hors-ligne** - Bascule automatiquement vers un mode hors-ligne lorsque la connexion est perdue.
- **Priorisation du contenu** - Charge le contenu essentiel en premier, et ajuste le chargement du contenu secondaire selon les conditions.
- **Optimisation des médias** - Ajuste la qualité des images et la présence d'animations en fonction de la bande passante disponible.
- **Mode économie de données** - S'adapte automatiquement lorsque le mode d'économie de données est activé sur l'appareil.

### Utilisation dans les composants

```jsx
import { useAdaptiveBandwidth } from '../hooks/useAdaptiveBandwidth';

function MyComponent() {
  const {
    networkStats,
    strategy,
    shouldLoadContent,
    getImageQuality,
    shouldEnableAnimations,
    getPageSize
  } = useAdaptiveBandwidth();

  // Conditionner le chargement du contenu en fonction de la priorité
  return (
    <div>
      {shouldLoadContent(1) && <EssentialContent />}

      {shouldLoadContent(2) && (
        <img
          src={`/images/example-${getImageQuality()}.jpg`}
          alt="Exemple"
        />
      )}

      {shouldEnableAnimations() && <AnimatedElement />}
    </div>
  );
}
```

### Composants d'interface

- **NetworkQualityIndicator** - Affiche la qualité actuelle de la connexion réseau.
- **NetworkStatsMonitor** - Moniteur avancé montrant les statistiques détaillées du réseau.

### Documentation complète

Pour plus de détails, consultez [la documentation complète](./src/docs/AdaptiveBandwidthManagement.md) du système de gestion adaptative de la bande passante.
