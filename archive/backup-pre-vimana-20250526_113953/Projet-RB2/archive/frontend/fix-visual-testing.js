const fs = require("fs"); fs.writeFileSync("cypress/support/visual-testing.d.ts", `/// <reference types="cypress" />
/// <reference types="@percy/cypress" />

declare namespace Cypress {
  interface Chainable<Subject> {
    /**
     * Custom command to take a Percy snapshot
     * @example cy.percySnapshot("Homepage")
     */
    percySnapshot(name: string, options?: object): Chainable<null>;
    
    /**
     * Custom command to match an image snapshot
     * @example cy.get(".element").matchImageSnapshot("element-name")
     */
    matchImageSnapshot(name: string, options?: object): Chainable<Subject>;
  }
}`, "utf8");
