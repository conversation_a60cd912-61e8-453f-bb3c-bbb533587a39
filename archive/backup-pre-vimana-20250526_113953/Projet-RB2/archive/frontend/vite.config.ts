import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import fs from 'fs';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    {
      name: 'fix-module-mime-type',
      configureServer(server) {
        server.middlewares.use((req, res, next) => {
          // Définir le type MIME correct pour les modules JavaScript
          if (req.url && (req.url.endsWith('.js') || req.url.endsWith('.mjs') || req.url.includes('.js?') || req.url.includes('.mjs?'))) {
            res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
          }
          next();
        });
      }
    },
    {
      name: 'html-fallback',
      configureServer(server) {
        server.middlewares.use((req, res, next) => {
          // Si c'est une requête pour un fichier spécifique, continuer
          if (req.url && /\.[^\/]+$/.test(req.url)) {
            next();
            return;
          }

          // Pour les routes SPA, renvoyer index.html
          const indexPath = path.resolve(__dirname, 'index.html');
          if (fs.existsSync(indexPath)) {
            const content = fs.readFileSync(indexPath, 'utf-8');
            res.setHeader('Content-Type', 'text/html; charset=utf-8');
            res.end(content);
          } else {
            next();
          }
        });
      }
    }
  ],
  server: {
    port: 3000,
    hmr: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'X-Content-Type-Options': 'nosniff'
    },
    proxy: {
      '/api/security': {
        target: 'http://localhost:3002',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      },
      '/socket.io': {
        target: 'http://localhost:3002',
        ws: true,
        changeOrigin: true
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      'src': path.resolve(__dirname, './src'),
      'components': path.resolve(__dirname, './src/components'),
      'pages': path.resolve(__dirname, './src/pages'),
      'store': path.resolve(__dirname, './src/store'),
      'services': path.resolve(__dirname, './src/services'),
      'types': path.resolve(__dirname, './src/types'),
      'utils': path.resolve(__dirname, './src/utils'),
      'assets': path.resolve(__dirname, './src/assets'),
      'hooks': path.resolve(__dirname, './src/hooks')
    }
  },
  optimizeDeps: {
    include: ['react', 'react-dom']
  },
  build: {
    sourcemap: true,
    minify: 'terser',
    cssCodeSplit: true,
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'vendor-react': ['react', 'react-dom', 'react-router-dom'],
          'vendor-ui': ['framer-motion', '@headlessui/react'],
          'vendor-utils': ['date-fns', 'lodash', 'axios'],
          'vendor-forms': ['react-hook-form', 'yup', 'zod'],
          'vendor-charts': ['recharts', 'chart.js'],

          // Feature chunks
          'feature-auth': ['./src/services/authService.ts', './src/hooks/useAuth.ts'],
          'feature-profile': ['./src/services/api/profileService.ts', './src/hooks/useProfile.ts'],
          'feature-retreats': ['./src/services/api/retreatService.ts'],
          'feature-bookings': ['./src/services/api/bookingService.ts'],

          // Page chunks will be automatically created by dynamic imports
        },
        // Ensure chunk filenames include content hash for better caching
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
