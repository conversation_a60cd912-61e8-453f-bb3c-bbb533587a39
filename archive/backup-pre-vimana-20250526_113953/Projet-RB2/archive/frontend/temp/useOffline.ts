import { useState, useEffect } from "react";

export interface UseOfflineOptions {
  syncUrl?: string;
  syncOnLoad?: boolean;
  autoSync?: boolean;
  syncInterval?: number;
  customValidation?: any;
  // Identifiant pour le logging
  loggingContext?: string;
}

export interface UseOfflineResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  syncing: boolean;
  saveData: (data: T) => Promise<{ success: boolean; error?: Error }>;
  syncData: () => Promise<void>;
  clearData: () => Promise<void>;
}

export function useOffline<T>(key: string, options: UseOfflineOptions = {}): UseOfflineResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [syncing, setSyncing] = useState(false);

  useEffect(() => {
    // Simulation du chargement des données
    const loadData = async (): Promise<void> => {
      setLoading(true);
      try {
        // Simuler une requête
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Dans une implémentation réelle, charger depuis localStorage ou IndexedDB
        const storedData = localStorage.getItem(key);
        if (storedData) {
          setData(JSON.parse(storedData) as T);
        }
        
        setLoading(false);
        
        // Si syncOnLoad est activé, synchroniser avec le serveur
        if (options.syncOnLoad) {
          syncData().catch(console.error);
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Erreur inconnue'));
        setLoading(false);
      }
    };
    
    loadData();
    
    // Mettre en place la synchronisation automatique si demandée
    let syncInterval: number | undefined;
    if (options.autoSync && options.syncInterval) {
      syncInterval = window.setInterval(() => {
        syncData().catch(console.error);
      }, options.syncInterval);
    }
    
    return () => {
      if (syncInterval) {
        clearInterval(syncInterval);
      }
    };
  }, [key, options.syncOnLoad, options.autoSync, options.syncInterval]);
  
  // Sauvegarder les données localement
  const saveData = async (newData: T): Promise<{ success: boolean; error?: Error }> => {
    try {
      // Valider les données si une validation est fournie
      if (options.customValidation) {
        options.customValidation.parse(newData);
      }
      
      // Dans une implémentation réelle, sauvegarder dans localStorage ou IndexedDB
      localStorage.setItem(key, JSON.stringify(newData));
      
      setData(newData);
      return { success: true };
    } catch (err) {
      return { 
        success: false, 
        error: err instanceof Error ? err : new Error('Erreur de sauvegarde')
      };
    }
  };
  
  // Synchroniser avec le serveur
  const syncData = async (): Promise<void> => {
    if (!navigator.onLine || !options.syncUrl) {
      return;
    }
    
    setSyncing(true);
    
    try {
      // Simuler une requête de synchronisation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Dans une implémentation réelle, envoyer les données au serveur
      // const response = await fetch(options.syncUrl, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(data)
      // });
      
      // if (!response.ok) {
      //   throw new Error(`Erreur de synchronisation: ${response.status}`);
      // }
      
      setSyncing(false);
    } catch (err) {
      console.error('Erreur de synchronisation:', err);
      setSyncing(false);
      throw err;
    }
  };
  
  // Effacer les données
  const clearData = async (): Promise<void> => {
    try {
      localStorage.removeItem(key);
      setData(null);
      setError(null);
    } catch (err) {
      console.error('Erreur lors de la suppression des données:', err);
    }
  };
  
  return {
    data,
    loading,
    error,
    syncing,
    saveData,
    syncData,
    clearData
  };
}
