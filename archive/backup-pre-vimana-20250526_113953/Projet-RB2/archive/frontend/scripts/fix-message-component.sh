#!/bin/bash

echo "Fixing TypeScript errors in MessageList component..."

# Correction des imports
sed -i '' 's/import { Message } from "\.\.\/\.\.\/types\/message";/import type { Message } from "..\/..\/types\/message";/' src/components/messaging/MessageList.tsx

# Correction du typage des attachments
sed -i '' 's/attachments\?\.\map((attachment: any,/attachments?.map((attachment: { type: string; url: string; name: string },/' src/components/messaging/MessageList.tsx

# Ajout des types manquants
cat > src/types/message.ts << EOL
export interface Message {
  id: string;
  content: string;
  senderId: string;
  timestamp: string | number | Date;
  status: 'sent' | 'delivered' | 'read';
  attachments?: Array<{
    type: string;
    url: string;
    name: string;
  }>;
}
EOL

echo "TypeScript fixes completed!"