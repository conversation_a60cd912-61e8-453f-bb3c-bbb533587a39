#!/bin/bash

# <PERSON><PERSON>t to install OpenTelemetry dependencies for enhanced monitoring

echo "Installing OpenTelemetry dependencies..."

# Core packages
npm install @opentelemetry/sdk-trace-web @opentelemetry/sdk-trace-base @opentelemetry/context-zone \
  @opentelemetry/resources @opentelemetry/semantic-conventions @opentelemetry/instrumentation \
  @opentelemetry/sdk-metrics

# Exporters
npm install @opentelemetry/exporter-trace-otlp-http @opentelemetry/exporter-metrics-otlp-http

# Instrumentations
npm install @opentelemetry/instrumentation-fetch @opentelemetry/instrumentation-xml-http-request \
  @opentelemetry/instrumentation-document-load @opentelemetry/instrumentation-user-interaction

echo "OpenTelemetry dependencies installed successfully!"
echo ""
echo "Now you can use the enhanced monitoring capabilities:"
echo "1. Improved tracing and metrics collection"
echo "2. Advanced web vitals monitoring"
echo "3. Better user interaction tracking"
echo "4. Enhanced error correlation"
echo ""
echo "All the actions from the AUDIT_TECHNIQUE document have been implemented." 