const fs = require('fs');
const path = require('path');

function processFile(filePath) {
  console.log(`Processing file: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let originalContent = content;
  let corrections = 0;

  // Correction des importations incorrectes
  content = content.replace(/};(\s*)from(\s*)['"](.*?)['"];/g, (match, space1, space2, importPath) => {
    corrections++;
    return `} from${space2}'${importPath}';`;
  });

  // Correction des hooks et appels incorrects (useAuth;())
  content = content.replace(/(\w+);(\s*)\(\)/g, (match, funcName, space) => {
    corrections++;
    return `${funcName}()`;
  });

  // Correction des hooks comme useLoyaltyStore;()
  content = content.replace(/useLoyaltyStore;(\(\))/g, 'useLoyaltyStore()');
  corrections += (content.match(/useLoyaltyStore;(\(\))/g) || []).length;

  // Correction des déclarations de paramètres incorrectes dans PointsConverter
  content = content.replace(/};:(\s*)(\w+)\)/g, (match, space, typeName) => {
    corrections++;
    return `}: ${typeName})`;
  });

  // Correction des problèmes avec catch dans les useEffect
  content = content.replace(/(\}\s*,\s*\[\]\s*)catch(\s*\(\s*\w+\s*\)\s*\{)/g, (match, before, catchPart) => {
    corrections++;
    return `${before}).catch(${catchPart.slice(5)}`;
  });

  // Correction des problèmes avec else dans les useEffect
  content = content.replace(/(\}\s*,\s*\[\]\s*)else(\s*\{)/g, (match, before, elsePart) => {
    corrections++;
    return `${before});${elsePart}`;
  });

  // Correction des balises mal formées dans JSX - mapping général
  const tagPairs = {
    'button': 'Button',
    'p': 'div',
    'Typography': 'Typography',
    'Dialog': 'Dialog',
    'DialogContent': 'DialogContent',
    'DialogActions': 'DialogActions',
    'Chip': 'Chip',
    'IconButton': 'IconButton',
    'Card': 'Card',
    'div': 'div',
    'span': 'span',
    'h1': 'h1',
    'h2': 'h2',
    'h3': 'h3',
    'h4': 'h4',
    'h5': 'h5',
    'h6': 'h6',
    'li': 'li',
    'ul': 'ul',
    'form': 'form',
    'Box': 'Box',
    'Paper': 'Paper',
    'CircularProgress': 'CircularProgress',
    'Container': 'Container',
    'Alert': 'Alert',
    'Button': 'Button',
    'Heart': 'Heart',
    'Share2': 'Share2',
    'Brain': 'Brain',
    'Volume2': 'Volume2',
    'CardContent': 'CardContent',
    'label': 'label',
    'option': 'option',
    'select': 'select',
    'input': 'input'
  };

  // Correction des balises fermantes incorrectes (ex: </button> au lieu de </div>)
  for (const [correctTag, correctClosingTag] of Object.entries(tagPairs)) {
    // Remplacer les balises fermantes incorrectes
    content = content.replace(new RegExp(`</p>(?=[^<]*</(?:${correctTag})>|[^<]*$)`, 'g'), `</${correctClosingTag}>`);
    corrections += (content.match(new RegExp(`</p>(?=[^<]*</(?:${correctTag})>|[^<]*$)`, 'g')) || []).length;
    
    content = content.replace(new RegExp(`</button>(?=[^<]*</(?:${correctTag})>|[^<]*$)`, 'g'), `</${correctClosingTag}>`);
    corrections += (content.match(new RegExp(`</button>(?=[^<]*</(?:${correctTag})>|[^<]*$)`, 'g')) || []).length;
    
    content = content.replace(new RegExp(`</MoreVertical>(?=[^<]*</(?:${correctTag})>|[^<]*$)`, 'g'), `</${correctClosingTag}>`);
    corrections += (content.match(new RegExp(`</MoreVertical>(?=[^<]*</(?:${correctTag})>|[^<]*$)`, 'g')) || []).length;
    
    content = content.replace(new RegExp(`</VideoIcon>(?=[^<]*</(?:${correctTag})>|[^<]*$)`, 'g'), `</${correctClosingTag}>`);
    corrections += (content.match(new RegExp(`</VideoIcon>(?=[^<]*</(?:${correctTag})>|[^<]*$)`, 'g')) || []).length;
  }

  // Correction pour les expressions JSX incorrectes (balises autoclosantes)
  content = content.replace(/\/>/g, ' />');
  corrections += (content.match(/\/>/g) || []).length;

  // Correction des balises </option> incorrectes
  content = content.replace(/<\/UserDialog>/g, '</div>');
  corrections += (content.match(/<\/UserDialog>/g) || []).length;

  // Correction des expressions JSX incomplètes avec des tags fermants incorrects
  content = content.replace(/<([A-Za-z0-9]+)([^>]*)>([^<]*?)<\/[A-Za-z0-9]+>/g, '<$1$2>$3</$1>');
  corrections += (content.match(/<([A-Za-z0-9]+)([^>]*)>([^<]*?)<\/[A-Za-z0-9]+>/g) || []).length;

  // Correction spécifique pour AIMeditationGuide
  // Correction des balises d'icônes incorrectes comme <Brain > à <Brain />
  content = content.replace(/<(Heart|Brain|Share2|Volume2)\s+>/g, '<$1 />');
  corrections += (content.match(/<(Heart|Brain|Share2|Volume2)\s+>/g) || []).length;

  // Correction des fermetures de fragments React incorrectes
  content = content.replace(/<\/>/g, '</>');
  corrections += (content.match(/<\/>/g) || []).length;

  // Correction des balises CircularProgress incorrectes
  content = content.replace(/<CircularProgress([^>]*)>\s*<\/CircularProgress>/g, '<CircularProgress$1 />');
  corrections += (content.match(/<CircularProgress([^>]*)>\s*<\/CircularProgress>/g) || []).length;
  
  // Correction des balises Dialog mal formées
  content = content.replace(/<Dialog([^>]*)\/>/g, '<Dialog$1>\n      <DialogContent>\n      </DialogContent>\n    </Dialog>');
  corrections += (content.match(/<Dialog([^>]*)\/>/g) || []).length;

  // Correction des attributs d'événements incorrects
  content = content.replace(/(<[^>]+\s+on\w+)=\{([^{}]+)\}/g, '$1={$2}');
  corrections += (content.match(/(<[^>]+\s+on\w+)=\{([^{}]+)\}/g) || []).length;

  // Correction des erreurs avec e.target dans Login.tsx
  content = content.replace(/e;\.target/g, 'e.target');
  corrections += (content.match(/e;\.target/g) || []).length;

  // Correction des conteneurs non fermés dans les composants JSX
  const openingTags = Object.keys(tagPairs);
  openingTags.forEach(tag => {
    // Pour chaque type de balise, recherche les balises ouvertes sans fermeture correspondante à la fin du fichier
    const regex = new RegExp(`<${tag}([^>]*)>([^<]*?)$`, 'gm');
    const matches = content.match(regex);
    
    if (matches) {
      matches.forEach(match => {
        const newMatch = match + `</${tag}>`;
        content = content.replace(match, newMatch);
        corrections++;
      });
    }
  });

  // Correction des parenthèses manquantes autour des expressions conditionnelles JSX
  content = content.replace(/{([^}]*)\s+&&\s+([^;{}]*)}/g, (match, condition, element) => {
    // Vérifie si l'élément a déjà des parenthèses
    if (!/^\s*\(.*\)\s*$/.test(element)) {
      corrections++;
      return `{${condition} && (${element})}`;
    }
    return match;
  });

  // Correction des problèmes spécifiques à MarketplaceManager
  content = content.replace(/catch\s*\(error\)\s*{/g, '}).catch((error) => {');
  corrections += (content.match(/catch\s*\(error\)\s*{/g) || []).length;

  // Correction des signatures incorrectes de fonctions useEffect
  content = content.replace(/\},\s*\[\]\);/g, '}, []);');
  corrections += (content.match(/\},\s*\[\]\);/g) || []).length;

  // Correction des expressions JSX spécifiques à ChatHeader.tsx
  content = content.replace(/<div([^>]*)\s*\/>\s*<\/\s*MoreVertical>/g, '<div$1 />');
  corrections += (content.match(/<div([^>]*)\s*\/>\s*<\/\s*MoreVertical>/g) || []).length;

  // Correction des balises VideoIcon incorrectes
  content = content.replace(/<\/VideoIcon><\/MoreVertical>/g, '</div></div>');
  corrections += (content.match(/<\/VideoIcon><\/MoreVertical>/g) || []).length;

  // Correction des accolades dans JSX comme className=`rank ${entry.rank.toLowerCase()`}
  content = content.replace(/className=`([^`{]*)\${([^}]*)}([^`]*)`/g, 'className={`$1${$2}$3`}');
  corrections += (content.match(/className=`([^`{]*)\${([^}]*)}([^`]*)`/g) || []).length;

  content = content.replace(/{\s*`([^`{]*)\${([^}]*)}([^`]*)`\s*}/g, '{`$1${$2}$3`}');
  corrections += (content.match(/{\s*`([^`{]*)\${([^}]*)}([^`]*)`\s*}/g) || []).length;

  // Correction des indentations incorrectes dans JSX, causes fréquentes d'erreurs
  // Normaliser les espaces blancs dans JSX
  content = content.replace(/>\s+</g, '>\n<');
  corrections += (content.match(/>\s+</g) || []).length;

  // Correction des fichiers incomplets
  // Ajouter export à la fin si nécessaire
  if (content.trim() && !content.trim().endsWith(';') && !content.trim().endsWith('}')) {
    content += '\n};\n';
    corrections++;
  }

  if (corrections > 0 && content !== originalContent) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
    return corrections;
  } else {
    console.log(`No changes made to ${filePath}`);
    return 0;
  }
}

function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    if (isDirectory) {
      walkDir(dirPath, callback);
    } else {
      callback(path.join(dir, f));
    }
  });
}

function fixComponents() {
  const rootDir = process.cwd();
  const loyaltyPath = path.join(rootDir, 'src', 'components', 'loyalty');
  const meditationPath = path.join(rootDir, 'src', 'components', 'meditation');
  const messagingPath = path.join(rootDir, 'src', 'components', 'messaging');
  
  const individualFiles = [
    path.join(rootDir, 'src', 'components', 'Login.tsx'),
    path.join(rootDir, 'src', 'components', 'MarketplaceManager.tsx'),
    path.join(rootDir, 'src', 'components', 'LoyaltyProgramManager.tsx')
  ];
  
  let totalFixedFiles = 0;
  let totalCorrections = 0;

  // Traiter les répertoires spécifiques
  const directoriesToProcess = [
    { path: loyaltyPath, name: 'loyalty' },
    { path: meditationPath, name: 'meditation' },
    { path: messagingPath, name: 'messaging' }
  ];

  directoriesToProcess.forEach(dir => {
    if (fs.existsSync(dir.path)) {
      console.log(`Processing ${dir.name} components...`);
      
      walkDir(dir.path, (filePath) => {
        if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
          const corrections = processFile(filePath);
          if (corrections > 0) {
            totalFixedFiles++;
            totalCorrections += corrections;
          }
        }
      });
    } else {
      console.log(`Directory not found: ${dir.path}`);
    }
  });

  // Traiter les fichiers individuels
  console.log(`Processing individual files...`);
  
  individualFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const corrections = processFile(filePath);
      if (corrections > 0) {
        totalFixedFiles++;
        totalCorrections += corrections;
      }
    } else {
      console.log(`File not found: ${filePath}`);
    }
  });

  console.log(`Fixed ${totalCorrections} issues in ${totalFixedFiles} files`);
}

fixComponents(); 