const fs = require('fs');
const path = require('path');

function processFile(filePath) {
  console.log(`Processing file: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let corrections = 0;

  // Correction des imports avec from incorrects
  content = content.replace(/};(\s*)from(\s*)['"](.*?)['"];/g, (match, space1, space2, importPath) => {
    corrections++;
    return `} from${space2}'${importPath}';`;
  });

  // Correction des balises de fermeture incorrectes (span, button, CarCard, etc.)
  content = content.replace(/<\/span>/g, '</div>');
  corrections += (content.match(/<\/span>/g) || []).length;
  
  content = content.replace(/<\/button>/g, '</div>');
  corrections += (content.match(/<\/button>/g) || []).length;
  
  content = content.replace(/<\/CarCard>/g, '</div>');
  corrections += (content.match(/<\/CarCard>/g) || []).length;

  // Corriger les balises self-closing avec mauvais formatage
  content = content.replace(/(\s*)\/>(\s*)/g, ' />$2');
  corrections += (content.match(/(\s*)\/>(\s*)/g) || []).length;

  // Correction des balises incorrectes pour les éléments HTML
  const htmlElements = ['div', 'p', 'h3', 'h1', 'h2', 'label'];
  
  htmlElements.forEach(element => {
    content = content.replace(new RegExp(`</${element}>`, 'g'), `</${element}>`);
    const replacements = content.match(new RegExp(`</${element}>`, 'g'));
    if (replacements) {
      corrections += replacements.length;
    }
  });

  // Corriger les balises fermantes pour les éléments Mui courants
  const muiComponents = [
    'Typography', 'Box', 'Container', 'Paper', 'Grid', 'Tab', 'Tabs', 'TabPanel',
    'Dialog', 'DialogTitle', 'MenuItem', 'Select', 'FormControl', 'InputLabel',
    'TableCell', 'TableRow', 'TableHead', 'TableBody', 'Table', 'TableContainer',
    'Button', 'TextField', 'CircularProgress', 'IconButton', 'Snackbar', 'DialogContent',
    'DialogActions', 'FormControl', 'MenuItem', 'DateTimePicker'
  ];
  
  muiComponents.forEach(component => {
    content = content.replace(new RegExp(`</${component}>`, 'g'), `</${component}>`);
    const replacements = content.match(new RegExp(`</${component}>`, 'g'));
    if (replacements) {
      corrections += replacements.length;
    }
  });

  // Corriger les balises fermantes pour les éléments custom
  const customComponents = [
    'FaCalendar', 'AddIcon', 'CloseIcon'
  ];
  
  customComponents.forEach(component => {
    content = content.replace(new RegExp(`</${component}>`, 'g'), `</${component}>`);
    const replacements = content.match(new RegExp(`</${component}>`, 'g'));
    if (replacements) {
      corrections += replacements.length;
    }
  });

  // Corriger useTranslation;()
  content = content.replace(/useTranslation;(\(\))/g, 'useTranslation()');
  corrections += (content.match(/useTranslation;(\(\))/g) || []).length;

  // Correction des try/catch malformés
  content = content.replace(/\}, \[\]\) catch/g, '}).catch');
  corrections += (content.match(/\}, \[\]\) catch/g) || []).length;

  // Corrige le problème des backticks
  content = content.replace(/`\}/g, '}');
  corrections += (content.match(/`\}/g) || []).length;

  // Corriger les problèmes de destructuration d'objet
  content = content.replace(/const \{ ([^}]+) \} = ([^;]+);\.([^;]+);/g, 'const { $1 } = $2.$3;');
  corrections += (content.match(/const \{ ([^}]+) \} = ([^;]+);\.([^;]+);/g) || []).length;

  // Correction pour CircularProgress > : null}
  content = content.replace(/<CircularProgress([^>]+)>\s*:/g, '<CircularProgress$1 />\s*:');
  corrections += (content.match(/<CircularProgress([^>]+)>\s*:/g) || []).length;

  // Correction pour les balises auto-fermantes
  content = content.replace(/<([^>]+)\s*=\s*\/>/g, '<$1 />');
  corrections += (content.match(/<([^>]+)\s*=\s*\/>/g) || []).length;

  // Corriger les fragments JSX malformés
  content = content.replace(/<\s*\/\s*>/g, '</>');
  corrections += (content.match(/<\s*\/\s*>/g) || []).length;

  // Corriger l'utilisation de e;.target
  content = content.replace(/e;\.target/g, 'e.target');
  corrections += (content.match(/e;\.target/g) || []).length;

  if (corrections > 0) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
    return corrections;
  } else {
    console.log(`No issues found in ${filePath}`);
    return 0;
  }
}

function fixComponents() {
  const calendarPath = path.join(process.cwd(), 'src', 'components', 'Calendar');
  const carRentalPath = path.join(process.cwd(), 'src', 'components', 'car-rental');
  
  let totalFixedFiles = 0;
  let totalCorrections = 0;

  // Vérifier si le répertoire Calendar existe
  if (fs.existsSync(calendarPath)) {
    const calendarFiles = fs.readdirSync(calendarPath)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.jsx'));
    
    console.log(`Found ${calendarFiles.length} Calendar component files`);
    
    calendarFiles.forEach(file => {
      const filePath = path.join(calendarPath, file);
      const corrections = processFile(filePath);
      if (corrections > 0) {
        totalFixedFiles++;
        totalCorrections += corrections;
      }
    });
  } else {
    console.log(`Directory not found: ${calendarPath}`);
  }

  // Vérifier si le répertoire car-rental existe
  if (fs.existsSync(carRentalPath)) {
    const carRentalFiles = fs.readdirSync(carRentalPath)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.jsx'));
    
    console.log(`Found ${carRentalFiles.length} car-rental component files`);
    
    carRentalFiles.forEach(file => {
      const filePath = path.join(carRentalPath, file);
      const corrections = processFile(filePath);
      if (corrections > 0) {
        totalFixedFiles++;
        totalCorrections += corrections;
      }
    });
  } else {
    console.log(`Directory not found: ${carRentalPath}`);
  }

  console.log(`Fixed ${totalCorrections} issues in ${totalFixedFiles} files`);
}

fixComponents(); 