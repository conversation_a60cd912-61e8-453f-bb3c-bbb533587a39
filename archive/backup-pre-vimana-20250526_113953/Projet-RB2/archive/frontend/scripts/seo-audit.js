#!/usr/bin/env node

/**
 * SEO Audit Script
 * 
 * This script performs a comprehensive SEO audit on the frontend pages.
 * It checks for:
 * - Proper meta tags
 * - Heading structure
 * - Image alt attributes
 * - Performance metrics
 * - Accessibility issues
 * - Mobile responsiveness
 * - Structured data
 * 
 * Usage: node scripts/seo-audit.js [--page=url] [--all] [--report=path]
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const { program } = require('commander');

// Configure command line options
program
  .option('--page <url>', 'URL of the page to audit')
  .option('--all', 'Audit all pages')
  .option('--report <path>', 'Path to save the report', './seo-audit-report.json')
  .option('--verbose', 'Show detailed output')
  .parse(process.argv);

const options = program.opts();

// Default pages to audit
const defaultPages = [
  '/',
  '/login',
  '/register',
  '/retreats',
  '/blog',
  '/about',
  '/contact',
  '/professional-dashboard',
  '/client-dashboard'
];

// SEO requirements
const seoRequirements = {
  metaTags: [
    'title',
    'description',
    'viewport',
    'og:title',
    'og:description',
    'og:image',
    'og:url',
    'og:type',
    'twitter:card',
    'twitter:title',
    'twitter:description',
    'twitter:image'
  ],
  headingStructure: {
    h1: { min: 1, max: 1 },
    h2: { min: 1, max: 10 },
    h3: { min: 0, max: 20 }
  },
  performance: {
    minScore: 80,
    maxLCP: 2500, // ms
    maxFID: 100, // ms
    maxCLS: 0.1
  },
  accessibility: {
    minScore: 90
  }
};

/**
 * Run Lighthouse audit on a page
 * @param {string} url - URL to audit
 * @returns {Promise<Object>} - Lighthouse results
 */
async function runLighthouse(url) {
  console.log(`Running Lighthouse audit on ${url}...`);
  
  const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });
  const options = {
    logLevel: 'info',
    output: 'json',
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
    port: chrome.port
  };
  
  const runnerResult = await lighthouse(url, options);
  await chrome.kill();
  
  return runnerResult.lhr;
}

/**
 * Check meta tags on a page
 * @param {Object} lhr - Lighthouse results
 * @returns {Object} - Meta tag audit results
 */
function checkMetaTags(lhr) {
  const metaTagResults = {
    present: [],
    missing: []
  };
  
  // Extract meta tags from Lighthouse results
  const metaElements = lhr.audits['meta-description'].details?.items || [];
  const titleElement = lhr.audits['document-title'].details?.items || [];
  
  // Check for title
  if (titleElement.length > 0 && titleElement[0].node) {
    metaTagResults.present.push('title');
  } else {
    metaTagResults.missing.push('title');
  }
  
  // Check for other meta tags
  metaElements.forEach(item => {
    const name = item.node.attributes.name || item.node.attributes.property;
    if (name && seoRequirements.metaTags.includes(name)) {
      metaTagResults.present.push(name);
    }
  });
  
  // Find missing tags
  seoRequirements.metaTags.forEach(tag => {
    if (!metaTagResults.present.includes(tag)) {
      metaTagResults.missing.push(tag);
    }
  });
  
  return metaTagResults;
}

/**
 * Check heading structure on a page
 * @param {Object} lhr - Lighthouse results
 * @returns {Object} - Heading structure audit results
 */
function checkHeadingStructure(lhr) {
  const headingResults = {
    counts: { h1: 0, h2: 0, h3: 0, h4: 0, h5: 0, h6: 0 },
    issues: []
  };
  
  // Extract headings from Lighthouse results
  const headings = lhr.audits['heading-order'].details?.items || [];
  
  // Count headings by level
  headings.forEach(item => {
    const level = item.node.attributes.role || item.node.tagName.toLowerCase();
    if (level.match(/^h[1-6]$/)) {
      headingResults.counts[level]++;
    }
  });
  
  // Check for issues
  Object.entries(seoRequirements.headingStructure).forEach(([level, { min, max }]) => {
    const count = headingResults.counts[level];
    if (count < min) {
      headingResults.issues.push(`Too few ${level} headings: ${count} (min: ${min})`);
    }
    if (count > max) {
      headingResults.issues.push(`Too many ${level} headings: ${count} (max: ${max})`);
    }
  });
  
  return headingResults;
}

/**
 * Check image alt attributes on a page
 * @param {Object} lhr - Lighthouse results
 * @returns {Object} - Image alt audit results
 */
function checkImageAlt(lhr) {
  const imageResults = {
    total: 0,
    withAlt: 0,
    withoutAlt: 0,
    issues: []
  };
  
  // Extract images from Lighthouse results
  const images = lhr.audits['image-alt'].details?.items || [];
  
  imageResults.total = images.length;
  
  // Check each image
  images.forEach(item => {
    if (item.node.attributes.alt) {
      imageResults.withAlt++;
    } else {
      imageResults.withoutAlt++;
      imageResults.issues.push(`Image without alt text: ${item.node.snippet}`);
    }
  });
  
  return imageResults;
}

/**
 * Check performance metrics on a page
 * @param {Object} lhr - Lighthouse results
 * @returns {Object} - Performance audit results
 */
function checkPerformance(lhr) {
  const performanceResults = {
    score: Math.round(lhr.categories.performance.score * 100),
    metrics: {
      LCP: lhr.audits['largest-contentful-paint'].numericValue,
      FID: lhr.audits['max-potential-fid'].numericValue,
      CLS: lhr.audits['cumulative-layout-shift'].numericValue
    },
    issues: []
  };
  
  // Check for issues
  if (performanceResults.score < seoRequirements.performance.minScore) {
    performanceResults.issues.push(`Performance score too low: ${performanceResults.score} (min: ${seoRequirements.performance.minScore})`);
  }
  
  if (performanceResults.metrics.LCP > seoRequirements.performance.maxLCP) {
    performanceResults.issues.push(`LCP too high: ${Math.round(performanceResults.metrics.LCP)}ms (max: ${seoRequirements.performance.maxLCP}ms)`);
  }
  
  if (performanceResults.metrics.FID > seoRequirements.performance.maxFID) {
    performanceResults.issues.push(`FID too high: ${Math.round(performanceResults.metrics.FID)}ms (max: ${seoRequirements.performance.maxFID}ms)`);
  }
  
  if (performanceResults.metrics.CLS > seoRequirements.performance.maxCLS) {
    performanceResults.issues.push(`CLS too high: ${performanceResults.metrics.CLS.toFixed(2)} (max: ${seoRequirements.performance.maxCLS})`);
  }
  
  return performanceResults;
}

/**
 * Check accessibility on a page
 * @param {Object} lhr - Lighthouse results
 * @returns {Object} - Accessibility audit results
 */
function checkAccessibility(lhr) {
  const accessibilityResults = {
    score: Math.round(lhr.categories.accessibility.score * 100),
    issues: []
  };
  
  // Extract accessibility issues
  Object.values(lhr.audits).forEach(audit => {
    if (audit.details && audit.details.items && audit.details.items.length > 0 && audit.scoreDisplayMode !== 'notApplicable') {
      if (audit.score !== 1 && audit.score !== null) {
        accessibilityResults.issues.push({
          id: audit.id,
          title: audit.title,
          description: audit.description,
          score: audit.score
        });
      }
    }
  });
  
  return accessibilityResults;
}

/**
 * Check structured data on a page
 * @param {Object} lhr - Lighthouse results
 * @returns {Object} - Structured data audit results
 */
function checkStructuredData(lhr) {
  const structuredDataResults = {
    present: false,
    type: null,
    issues: []
  };
  
  // Check for structured data
  const structuredData = lhr.audits['structured-data'];
  
  if (structuredData && structuredData.details && structuredData.details.items) {
    structuredDataResults.present = structuredData.details.items.length > 0;
    
    if (structuredDataResults.present) {
      structuredDataResults.type = structuredData.details.items[0].type;
    } else {
      structuredDataResults.issues.push('No structured data found');
    }
  } else {
    structuredDataResults.issues.push('Could not check structured data');
  }
  
  return structuredDataResults;
}

/**
 * Run a complete SEO audit on a page
 * @param {string} url - URL to audit
 * @returns {Promise<Object>} - Audit results
 */
async function auditPage(url) {
  try {
    const lhr = await runLighthouse(url);
    
    const results = {
      url,
      timestamp: new Date().toISOString(),
      metaTags: checkMetaTags(lhr),
      headingStructure: checkHeadingStructure(lhr),
      imageAlt: checkImageAlt(lhr),
      performance: checkPerformance(lhr),
      accessibility: checkAccessibility(lhr),
      structuredData: checkStructuredData(lhr),
      overallScore: Math.round((
        lhr.categories.performance.score +
        lhr.categories.accessibility.score +
        lhr.categories.seo.score
      ) * 100 / 3)
    };
    
    return results;
  } catch (error) {
    console.error(`Error auditing ${url}:`, error);
    return {
      url,
      error: error.message
    };
  }
}

/**
 * Generate a summary of audit results
 * @param {Array} results - Array of audit results
 * @returns {Object} - Summary of results
 */
function generateSummary(results) {
  const summary = {
    totalPages: results.length,
    averageScore: 0,
    pagesWithIssues: 0,
    commonIssues: {
      metaTags: {},
      performance: 0,
      accessibility: 0,
      imageAlt: 0,
      headingStructure: 0,
      structuredData: 0
    }
  };
  
  let totalScore = 0;
  
  results.forEach(result => {
    if (result.error) {
      summary.pagesWithIssues++;
      return;
    }
    
    totalScore += result.overallScore;
    
    // Count meta tag issues
    result.metaTags.missing.forEach(tag => {
      summary.commonIssues.metaTags[tag] = (summary.commonIssues.metaTags[tag] || 0) + 1;
    });
    
    // Count other issues
    if (result.performance.issues.length > 0) summary.commonIssues.performance++;
    if (result.accessibility.issues.length > 0) summary.commonIssues.accessibility++;
    if (result.imageAlt.issues.length > 0) summary.commonIssues.imageAlt++;
    if (result.headingStructure.issues.length > 0) summary.commonIssues.headingStructure++;
    if (result.structuredData.issues.length > 0) summary.commonIssues.structuredData++;
    
    if (result.metaTags.missing.length > 0 ||
        result.performance.issues.length > 0 ||
        result.accessibility.issues.length > 0 ||
        result.imageAlt.issues.length > 0 ||
        result.headingStructure.issues.length > 0 ||
        result.structuredData.issues.length > 0) {
      summary.pagesWithIssues++;
    }
  });
  
  summary.averageScore = Math.round(totalScore / results.length);
  
  return summary;
}

/**
 * Save audit results to a file
 * @param {Object} data - Audit results
 * @param {string} filePath - Path to save the file
 */
function saveReport(data, filePath) {
  const dir = path.dirname(filePath);
  
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  console.log(`Report saved to ${filePath}`);
}

/**
 * Print a summary of the audit results
 * @param {Object} summary - Summary of results
 */
function printSummary(summary) {
  console.log('\n=== SEO Audit Summary ===');
  console.log(`Total pages audited: ${summary.totalPages}`);
  console.log(`Average score: ${summary.averageScore}/100`);
  console.log(`Pages with issues: ${summary.pagesWithIssues}/${summary.totalPages}`);
  
  console.log('\nCommon issues:');
  
  // Meta tag issues
  const metaTagIssues = Object.entries(summary.commonIssues.metaTags);
  if (metaTagIssues.length > 0) {
    console.log('  Missing meta tags:');
    metaTagIssues.forEach(([tag, count]) => {
      console.log(`    - ${tag}: ${count} pages`);
    });
  }
  
  // Other issues
  if (summary.commonIssues.performance > 0) {
    console.log(`  Performance issues: ${summary.commonIssues.performance} pages`);
  }
  
  if (summary.commonIssues.accessibility > 0) {
    console.log(`  Accessibility issues: ${summary.commonIssues.accessibility} pages`);
  }
  
  if (summary.commonIssues.imageAlt > 0) {
    console.log(`  Image alt text issues: ${summary.commonIssues.imageAlt} pages`);
  }
  
  if (summary.commonIssues.headingStructure > 0) {
    console.log(`  Heading structure issues: ${summary.commonIssues.headingStructure} pages`);
  }
  
  if (summary.commonIssues.structuredData > 0) {
    console.log(`  Structured data issues: ${summary.commonIssues.structuredData} pages`);
  }
  
  console.log('\nFor detailed results, see the generated report.');
}

/**
 * Main function
 */
async function main() {
  console.log('Starting SEO audit...');
  
  // Determine pages to audit
  let pagesToAudit = [];
  
  if (options.page) {
    pagesToAudit = [options.page];
  } else if (options.all) {
    // Get all routes from the application
    try {
      const routes = JSON.parse(execSync('npm run routes --silent').toString());
      pagesToAudit = routes.map(route => `http://localhost:3000${route}`);
    } catch (error) {
      console.warn('Could not get routes from application, using default pages');
      pagesToAudit = defaultPages.map(page => `http://localhost:3000${page}`);
    }
  } else {
    pagesToAudit = defaultPages.map(page => `http://localhost:3000${page}`);
  }
  
  console.log(`Auditing ${pagesToAudit.length} pages...`);
  
  // Run audits
  const results = [];
  for (const url of pagesToAudit) {
    const result = await auditPage(url);
    results.push(result);
    
    if (options.verbose) {
      console.log(`\nResults for ${url}:`);
      console.log(`  Overall score: ${result.overallScore}/100`);
      console.log(`  Missing meta tags: ${result.metaTags.missing.join(', ') || 'None'}`);
      console.log(`  Performance issues: ${result.performance.issues.length}`);
      console.log(`  Accessibility issues: ${result.accessibility.issues.length}`);
      console.log(`  Image alt issues: ${result.imageAlt.issues.length}`);
      console.log(`  Heading structure issues: ${result.headingStructure.issues.length}`);
      console.log(`  Structured data issues: ${result.structuredData.issues.length}`);
    }
  }
  
  // Generate summary
  const summary = generateSummary(results);
  
  // Save report
  const reportData = {
    summary,
    results,
    timestamp: new Date().toISOString()
  };
  
  saveReport(reportData, options.report);
  
  // Print summary
  printSummary(summary);
}

// Run the script
main().catch(error => {
  console.error('Error running SEO audit:', error);
  process.exit(1);
});
