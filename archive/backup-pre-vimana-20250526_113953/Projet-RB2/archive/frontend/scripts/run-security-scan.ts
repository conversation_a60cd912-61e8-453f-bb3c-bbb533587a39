#!/usr/bin/env node

import { SecurityAnalyzer } from './security/SecurityAnalyzer.js';
import { ReportGenerator } from './utils/reportGenerator.js';
import { SecurityNotifications } from './utils/notifications.js';
import { SecurityReport } from './security/types.js';

/**
 * Risk level severity map (higher numbers are more severe)
 */
const RISK_LEVEL_SEVERITY: Record<string, number> = {
  'LOW': 0,
  'MEDIUM': 1,
  'HIGH': 2,
  'CRITICAL': 3
};

async function runSecurityScan(): Promise<SecurityReport> {
  const analyzer = new SecurityAnalyzer();
  
  try {
    console.log('Starting security analysis...');
    
    const results = await analyzer.analyze('http://localhost:3000');
    
    // Generate HTML report
    await ReportGenerator.generateHTML(
      results,
      './reports/security-scan-report.html'
    );

    // Generate JSON report
    await ReportGenerator.generateJSON(
      results,
      './reports/security-scan-report.json'
    );

    // Check if risk level is higher than MEDIUM
    if (RISK_LEVEL_SEVERITY[results.riskLevel] > RISK_LEVEL_SEVERITY['MEDIUM']) {
      await SecurityNotifications.sendSlackNotification(
        results,
        process.env.SLACK_WEBHOOK_URL
      );
    }

    console.log('Security analysis completed');
    console.log(`Risk Level: ${results.riskLevel}`);
    console.log(`Vulnerabilities found: ${results.issues.length}`);
    
    return results;
  } catch (error) {
    console.error('Security analysis failed:', error);
    throw error;
  }
}

if (require.main === module) {
  runSecurityScan()
    .then(results => {
      process.exit(results.riskLevel === 'LOW' ? 0 : 1);
    })
    .catch(() => process.exit(1));
}

export { runSecurityScan }; 
