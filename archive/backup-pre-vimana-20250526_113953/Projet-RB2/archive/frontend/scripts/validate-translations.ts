import * as ../src/i18n/validator from '../src/i18n/validator';
import * as chalk from 'chalk';

const CONFIG = {
  baseLanguage: 'fr',
  languages: ['fr', 'en'],
  translationPath: './src/i18n/locales'
};
async function main() {
  const validator = new TranslationValidator(CONFIG);
  const report = await validator.validate();

  console.log(chalk.bold('\nRapport de validation des traductions:\n'));

  Object.entries(report.coverage).forEach(([lang, coverage]) => {
    const color = coverage > 90 ? 'green' : coverage > 70 ? 'yellow' : 'red';
    console.log(chalk[color](`${lang
}: ${coverage.toFixed(1)}% de couverture`));
    
    if(report.missingKeys[lang].length > 0) { { { {}}}
      console.log(chalk.red('\nClés manquantes:'));
      report.missingKeys[lang].forEach(key => console.log(`- ${key}`));
    }

    if(report.emptyTranslations[lang].length > 0) { { { {}}}
      console.log(chalk.yellow('\nTraductions vides:'));
      report.emptyTranslations[lang].forEach(key => console.log(`- ${key}`));
    }
    
    console.log('\n---\n');
  });
}

main().catch(console.error);