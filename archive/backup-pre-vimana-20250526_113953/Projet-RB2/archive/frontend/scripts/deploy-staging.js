/**
 * Script pour déployer l'application dans un environnement de staging
 */

import { spawn } from 'child_process';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le chemin du répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Fonction pour exécuter une commande
function runCommand(command, args, cwd = rootDir) {
  return new Promise((resolve, reject) => {
    console.log(chalk.blue(`Exécution de la commande: ${command} ${args.join(' ')}`));
    
    const process = spawn(command, args, { stdio: 'inherit', cwd });
    
    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`La commande a échoué avec le code ${code}`));
      }
    });
    
    process.on('error', (error) => {
      reject(error);
    });
  });
}

// Fonction pour créer un fichier .env.staging
function createStagingEnvFile() {
  const envContent = `
# Variables d'environnement pour l'environnement de staging

# API URLs
VITE_API_URL=https://api-staging.retreat-and-be.com
VITE_SECURITY_API_URL=https://security-api-staging.retreat-and-be.com
VITE_FINANCIAL_API_URL=https://financial-api-staging.retreat-and-be.com
VITE_SOCIAL_API_URL=https://social-api-staging.retreat-and-be.com
VITE_EDUCATION_API_URL=https://education-api-staging.retreat-and-be.com
VITE_AGENT_API_URL=https://agent-api-staging.retreat-and-be.com

# Authentication
VITE_AUTH_DOMAIN=auth-staging.retreat-and-be.com
VITE_AUTH_CLIENT_ID=staging-client-id
VITE_AUTH_AUDIENCE=https://api-staging.retreat-and-be.com

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_CHAT=true
VITE_ENABLE_NOTIFICATIONS=true

# Misc
VITE_APP_VERSION=${new Date().toISOString()}
VITE_ENVIRONMENT=staging
`;

  fs.writeFileSync(path.join(rootDir, '.env.staging'), envContent);
  console.log(chalk.green('Fichier .env.staging créé avec succès.'));
}

// Fonction pour créer un fichier de configuration Vite pour le staging
function createStagingViteConfig() {
  const configContent = `
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist-staging',
    sourcemap: true,
    minify: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          ui: ['@mui/material', '@mui/icons-material'],
        },
      },
    },
  },
  server: {
    port: 5174,
    strictPort: true,
  },
});
`;

  fs.writeFileSync(path.join(rootDir, 'vite.staging.config.js'), configContent);
  console.log(chalk.green('Fichier vite.staging.config.js créé avec succès.'));
}

// Fonction pour mettre à jour le package.json avec les scripts de staging
async function updatePackageJson() {
  const packageJsonPath = path.join(rootDir, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Ajouter les scripts de staging s'ils n'existent pas déjà
  if (!packageJson.scripts['build:staging']) {
    packageJson.scripts['build:staging'] = 'vite build --config vite.staging.config.js --mode staging';
  }
  
  if (!packageJson.scripts['preview:staging']) {
    packageJson.scripts['preview:staging'] = 'vite preview --config vite.staging.config.js --port 5174';
  }
  
  if (!packageJson.scripts['deploy:staging']) {
    packageJson.scripts['deploy:staging'] = 'node scripts/deploy-staging.js';
  }
  
  // Écrire le package.json mis à jour
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log(chalk.green('package.json mis à jour avec succès.'));
}

// Fonction principale
async function main() {
  try {
    console.log(chalk.green('=== Déploiement de l\'application dans l\'environnement de staging ==='));
    
    // Créer les fichiers de configuration
    console.log(chalk.yellow('\n=== Création des fichiers de configuration ==='));
    createStagingEnvFile();
    createStagingViteConfig();
    await updatePackageJson();
    
    // Installer les dépendances
    console.log(chalk.yellow('\n=== Installation des dépendances ==='));
    await runCommand('npm', ['install']);
    
    // Linter
    console.log(chalk.yellow('\n=== Vérification du code ==='));
    await runCommand('npm', ['run', 'lint']);
    
    // Build
    console.log(chalk.yellow('\n=== Build de l\'application ==='));
    await runCommand('npm', ['run', 'build:staging']);
    
    // Déploiement sur AWS S3 (simulation)
    console.log(chalk.yellow('\n=== Déploiement sur AWS S3 (simulation) ==='));
    console.log(chalk.blue('Exécution de la commande: aws s3 sync dist-staging/ s3://retreat-and-be-staging --delete'));
    console.log(chalk.green('Déploiement sur AWS S3 simulé avec succès.'));
    
    // Invalidation du cache CloudFront (simulation)
    console.log(chalk.yellow('\n=== Invalidation du cache CloudFront (simulation) ==='));
    console.log(chalk.blue('Exécution de la commande: aws cloudfront create-invalidation --distribution-id DISTRIBUTION_ID_STAGING --paths "/*"'));
    console.log(chalk.green('Invalidation du cache CloudFront simulée avec succès.'));
    
    console.log(chalk.green('\n=== Déploiement terminé avec succès ==='));
    console.log(chalk.blue('L\'application est maintenant disponible à l\'adresse: https://staging.retreat-and-be.com'));
  } catch (error) {
    console.error(chalk.red(`Erreur lors du déploiement: ${error.message}`));
    process.exit(1);
  }
}

// Exécuter la fonction principale
main();
