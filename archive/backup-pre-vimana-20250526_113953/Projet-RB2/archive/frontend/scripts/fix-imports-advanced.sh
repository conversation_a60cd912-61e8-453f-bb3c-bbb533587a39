#!/bin/bash

echo "Applying advanced import fixes to TypeScript files..."

# Fonction pour trouver et remplacer dans tous les fichiers .ts et .tsx du répertoire src
find_and_replace() {
    find src -type f \( -name "*.ts" -o -name "*.tsx" \) -print0 | xargs -0 sed -i '' "$1"
}

# 1. Corriger les guillemets qui ne sont pas fermés correctement
find_and_replace 's/from "socket\.io-client'\''";/from "socket.io-client";/g'
find_and_replace 's/from "web3\.storage'\''";/from "web3.storage";/g'
find_and_replace 's/from "axios\/index'\''";/from "axios";/g'
find_and_replace 's/from "rxjs\/operators'\''";/from "rxjs/operators";/g'
find_and_replace 's/from "viem\/chains'\''";/from "viem/chains";/g'
find_and_replace 's/from "react-ga4'\''";/from "react-ga4";/g'
find_and_replace 's/from "msw\/node'\''";/from "msw/node";/g'
find_and_replace 's/from "zustand\/middleware'\''";/from "zustand/middleware";/g'
find_and_replace 's/from "redux-persist\/lib\/storage'\''";/from "redux-persist/lib/storage";/g'
find_and_replace 's/from "react-dom\/test-utils'\''";/from "react-dom/test-utils";/g'

# 2. Corriger les imports avec des points dans le nom du module
find_and_replace 's/import \* as socket\.io-client from/import * as socketIo from/g'
find_and_replace 's/import \* as web3\.storage from/import * as web3Storage from/g'

# 3. Corriger les imports avec des slashs dans le nom du module
find_and_replace 's/import \* as axios\/index from/import axios from/g'
find_and_replace 's/import \* as rxjs\/operators from/import * as operators from/g'
find_and_replace 's/import \* as redux-persist\/lib\/storage from/import storage from/g'
find_and_replace 's/import \* as zustand\/middleware from/import * as middleware from/g'
find_and_replace 's/import \* as msw\/node from/import * as mswNode from/g'
find_and_replace 's/import ReactDOM\/test-utils from/import * as ReactTestUtils from/g'
find_and_replace 's/import \* as viem\/chains from/import * as chains from/g'

# 4. Corriger les imports avec ".."
find_and_replace 's/import \* as \.\. from "\.\.'\''";/import * as parent from "..";/g'
find_and_replace 's/import \* as \.\. from "\.\.";/import * as parent from "..";/g'

# 5. Corriger les doubles guillemets à la fin des imports
find_and_replace 's/from "\([^"]*\)"";/from "\1";/g'

# 6. Corriger les imports avec des traits d'union dans le nom du module
find_and_replace 's/import \* as react-ga4 from/import * as ReactGA from/g'
find_and_replace 's/import \* as react-icons\/fa from/import * as FaIcons from/g'

# 7. Correction plus directe pour les erreurs fréquentes
echo "Fixing specific import patterns..."

# Correction pour socket.io-client
grep -l "import \* as socket.io-client" $(find src -name "*.ts" -o -name "*.tsx") | xargs -I{} sed -i '' 's/import \* as socket\.io-client from "socket\.io-client.*";/import * as socketIo from "socket.io-client";/g' {}

# Correction pour les doubles guillemets
grep -l "from \".*\"\";" $(find src -name "*.ts" -o -name "*.tsx") | xargs -I{} sed -i '' 's/from "\(.*\)"";/from "\1";/g' {}

# Correction pour les guillemets non fermés
grep -l "from \".*'\";" $(find src -name "*.ts" -o -name "*.tsx") | xargs -I{} sed -i '' 's/from "\(.*\)'";/from "\1";/g' {}

echo "Advanced import fixes completed!"

echo "All advanced import fixes have been applied." 