#!/usr/bin/env node

/**
 * Ce script analyse le codebase pour identifier les dépendances inutilisées
 * ou redondantes et propose des optimisations.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const chalk = require('chalk');

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const PACKAGE_JSON_PATH = path.join(ROOT_DIR, 'package.json');
const SRC_DIR = path.join(ROOT_DIR, 'src');
const IGNORE_PACKAGES = [
  // Packages qui sont utilisés indirectement et ne doivent pas être supprimés
  '@opentelemetry/context-zone', 
  '@types/jest',
  '@types/node',
  'jest-environment-jsdom'
];

// Couleurs pour la sortie console
const colors = {
  info: chalk.blue,
  success: chalk.green,
  warning: chalk.yellow,
  error: chalk.red,
  highlight: chalk.cyan
};

console.log(colors.info('🧹 Analyse des dépendances du projet...'));

// Lire package.json
const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, 'utf8'));
const allDependencies = {
  ...packageJson.dependencies,
  ...packageJson.devDependencies
};

const dependencyKeys = Object.keys(allDependencies);

console.log(colors.info(`📦 ${dependencyKeys.length} dépendances trouvées.`));

// Récupérer tous les fichiers .js, .jsx, .ts, .tsx
function getAllJsFiles(dir) {
  let files = [];
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory() && entry.name !== 'node_modules' && !entry.name.startsWith('.')) {
      files = files.concat(getAllJsFiles(fullPath));
    } else if (/\.(js|jsx|ts|tsx)$/.test(entry.name)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

const jsFiles = getAllJsFiles(SRC_DIR);
console.log(colors.info(`🔍 Analyse de ${jsFiles.length} fichiers...`));

// Fonction pour vérifier si un paquet est utilisé dans le code
function isDependencyUsed(dependency) {
  if (IGNORE_PACKAGES.includes(dependency)) {
    return true;
  }
  
  // Cas spéciaux pour certains types de packages
  if (dependency.startsWith('@types/')) {
    const typedPackage = dependency.replace('@types/', '');
    if (dependencyKeys.includes(typedPackage)) {
      return true; // Les @types/* sont valides si le paquet correspondant est utilisé
    }
  }
  
  const packageNamePattern = new RegExp(`['"]${dependency}(?:/[^'"]*)?['"]`, 'g');
  
  for (const file of jsFiles) {
    const content = fs.readFileSync(file, 'utf8');
    if (packageNamePattern.test(content)) {
      return true;
    }
  }
  
  // Vérifier les imports dynamiques et autres modèles
  const altImportPatterns = [
    new RegExp(`import\\(['"](${dependency})['"]\\)`, 'g'),
    new RegExp(`require\\(['"](${dependency})['"]\\)`, 'g'),
    new RegExp(`loadable\\(\\s*\\(\\)\\s*=>\\s*import\\(['"](${dependency})['"]\\)`, 'g')
  ];
  
  for (const file of jsFiles) {
    const content = fs.readFileSync(file, 'utf8');
    for (const pattern of altImportPatterns) {
      if (pattern.test(content)) {
        return true;
      }
    }
  }
  
  return false;
}

// Trouver les dépendances non utilisées
const unusedDependencies = dependencyKeys.filter(dep => !isDependencyUsed(dep));

console.log('\n' + colors.highlight('📊 Résultats de l\'analyse:'));
console.log(colors.success(`✓ ${dependencyKeys.length - unusedDependencies.length} dépendances sont utilisées.`));

if (unusedDependencies.length > 0) {
  console.log(colors.warning(`⚠️ ${unusedDependencies.length} dépendances potentiellement inutilisées:`));
  
  // Séparer par type (dev vs regular)
  const unusedRegular = unusedDependencies.filter(dep => packageJson.dependencies && dep in packageJson.dependencies);
  const unusedDev = unusedDependencies.filter(dep => packageJson.devDependencies && dep in packageJson.devDependencies);
  
  if (unusedRegular.length > 0) {
    console.log(colors.warning('\nDépendances standard:'));
    unusedRegular.forEach(dep => console.log(`  - ${dep}`));
  }
  
  if (unusedDev.length > 0) {
    console.log(colors.warning('\nDépendances de développement:'));
    unusedDev.forEach(dep => console.log(`  - ${dep}`));
  }
  
  console.log('\n' + colors.highlight('💡 Pour supprimer ces dépendances, exécuter:'));
  if (unusedRegular.length > 0) {
    console.log(`npm uninstall ${unusedRegular.join(' ')}`);
  }
  if (unusedDev.length > 0) {
    console.log(`npm uninstall --save-dev ${unusedDev.join(' ')}`);
  }
} else {
  console.log(colors.success('✓ Toutes les dépendances sont utilisées.'));
}

// Vérifier les versions dupliquées avec npm ls
console.log('\n' + colors.info('🔍 Recherche de doublons et de conflits de versions...'));

try {
  const duplicates = execSync('npm ls --parseable --all | grep -E "node_modules/[^/]+/node_modules/"', { 
    encoding: 'utf8',
    stdio: ['pipe', 'pipe', 'ignore']
  });
  
  const duplicateLines = duplicates.split('\n').filter(line => line.trim() !== '');
  
  if (duplicateLines.length > 0) {
    console.log(colors.warning(`⚠️ ${duplicateLines.length} dépendances ont des versions dupliquées.`));
    console.log(colors.highlight('\n💡 Conseil: Exécutez `npm dedupe` pour optimiser les dépendances.'));
  } else {
    console.log(colors.success('✓ Aucune dépendance dupliquée trouvée.'));
  }
} catch (error) {
  // Si grep ne trouve pas de correspondance, il quitte avec un code d'erreur
  console.log(colors.success('✓ Aucune dépendance dupliquée trouvée.'));
}

// Vérifier les vulnérabilités
console.log('\n' + colors.info('🔍 Vérification des vulnérabilités...'));

try {
  const audit = execSync('npm audit --json', { encoding: 'utf8' });
  const auditData = JSON.parse(audit);
  
  const vulnerabilities = auditData.metadata.vulnerabilities;
  const totalVulnerabilities = Object.values(vulnerabilities).reduce((sum, val) => sum + val, 0);
  
  if (totalVulnerabilities > 0) {
    console.log(colors.warning(`⚠️ ${totalVulnerabilities} vulnérabilités trouvées:`));
    
    Object.entries(vulnerabilities).forEach(([severity, count]) => {
      if (count > 0) {
        const colorFn = severity === 'critical' || severity === 'high' 
          ? colors.error 
          : severity === 'moderate' 
            ? colors.warning 
            : colors.info;
            
        console.log(colorFn(`  - ${severity}: ${count}`));
      }
    });
    
    console.log('\n' + colors.highlight('💡 Conseil: Exécutez `npm audit fix` pour tenter de résoudre ces problèmes.'));
  } else {
    console.log(colors.success('✓ Aucune vulnérabilité trouvée.'));
  }
} catch (error) {
  console.log(colors.error('❌ Erreur lors de la vérification des vulnérabilités:'), error.message);
}

console.log('\n' + colors.success('✅ Analyse terminée!')); 