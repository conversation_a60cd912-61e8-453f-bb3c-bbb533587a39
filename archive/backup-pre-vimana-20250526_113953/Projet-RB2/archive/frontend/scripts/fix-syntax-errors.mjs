import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { glob } from 'glob';

// Obtenir le chemin du répertoire actuel en ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.resolve(__dirname, '..');

const PATTERNS = [
  // Corriger les "===" qui devraient être "="
  { regex: /data-testid===["']([^"']+)["']/g, replacement: 'data-testid="$1"' },
  { regex: /className\s*===\s*["']([^"']+)["']/g, replacement: 'className="$1"' },
  { regex: /id\s*===\s*["']([^"']+)["']/g, replacement: 'id="$1"' },
  { regex: /scope\s*===\s*["']([^"']+)["']/g, replacement: 'scope="$1"' },
  
  // Autres attributs avec "==="
  { regex: /(\w+)\s*===\s*["']([^"']+)["']/g, replacement: '$1="$2"' },
  
  // Corriger les points-virgules à la fin des commentaires
  { regex: /(\/\/\s*[^;\n]+);(\s*$)/gm, replacement: '$1$2' },
  
  // Corriger les balises JSX mal fermées
  { regex: /<\/([a-zA-Z0-9]+)\/>/g, replacement: '</$1>' },
  
  // Corriger les points-virgules à l'intérieur des objets JSON dans les tests
  { regex: /(pageSize|page|total):\s*(\d+);/g, replacement: '$1: $2' },
  
  // Corriger les références à filename === "quelque chose"
  { regex: /filename\s*===\s*["']([^"']+)["']/g, replacement: 'filename="$1"' },
  
  // Corriger les style={{ p: 2, }} avec une virgule superflue
  { regex: /(\{\s*p:\s*\d+),(\s*\})/g, replacement: '$1$2' }
];

// Les fichiers à corriger
const FILES_TO_CHECK = [
  'cypress/e2e/**/*.{ts,tsx}',
  'src/components/**/*.{ts,tsx}',
  'src/pages/**/*.{ts,tsx}'
];

function processFile(filePath) {
  console.log(`Processing ${filePath}...`);
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  
  // Appliquer toutes les regex de correction
  PATTERNS.forEach(pattern => {
    const newContent = content.replace(pattern.regex, pattern.replacement);
    if (newContent !== content) {
      hasChanges = true;
      content = newContent;
    }
  });
  
  // Sauvegarder le fichier si des changements ont été effectués
  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ Fixed issues in ${filePath}`);
    return true;
  }
  
  console.log(`  ✓ No issues found in ${filePath}`);
  return false;
}

async function run() {
  let totalFixed = 0;
  let totalFiles = 0;
  
  // Traiter tous les fichiers
  for (const pattern of FILES_TO_CHECK) {
    const files = await glob(pattern, { cwd: ROOT_DIR });
    for (const file of files) {
      totalFiles++;
      const fullPath = path.join(ROOT_DIR, file);
      if (processFile(fullPath)) {
        totalFixed++;
      }
    }
  }
  
  console.log(`\nTerminé! ${totalFixed} fichiers corrigés sur ${totalFiles} fichiers vérifiés.`);
}

run(); 