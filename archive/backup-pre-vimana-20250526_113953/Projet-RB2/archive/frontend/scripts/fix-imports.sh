#!/bin/bash

echo "Fixing common TypeScript import errors..."

# Fix unterminated string literals in import statements
# This replaces single quotes with double quotes for import paths that end with a trailing single quote
# Example: from '../utils/logger.ts; to from '../utils/logger.ts';
find src -type f -name "*.ts" -o -name "*.tsx" | xargs sed -i '' -E "s/from ['\"](.*)['\";]/from \"\1\";/g"

# Fix imports with react-icons and similar
# This replaces incorrect imports like: import * as react-icons/fa from 'react-icons/fa'
# with correct ones like: import * as FaIcons from 'react-icons/fa'
find src -type f -name "*.ts" -o -name "*.tsx" | xargs sed -i '' -E "s/import \* as react-icons\/fa from/import * as FaIcons from/g"
find src -type f -name "*.ts" -o -name "*.tsx" | xargs sed -i '' -E "s/import \* as react-i18next from/import { useTranslation } from/g"
find src -type f -name "*.ts" -o -name "*.tsx" | xargs sed -i '' -E "s/import \* as date-fns\/locale from/import * as dateFnsLocale from/g"

# Fix imports with regular expressions
# This replaces incorrect imports like: import { } from /Button;
# with correct ones like: import { } from './Button';
find src -type f -name "*.ts" -o -name "*.tsx" | xargs sed -i '' -E "s/from \/(.*);/from '.\/\1';/g"

echo "Import fixes completed!" 