# Instructions pour résoudre le problème "Cannot GET /test"

Nous avons rencontré un problème de routage avec l'erreur "Cannot GET /test". Cela est probablement dû à l'un des problèmes suivants:

1. Le serveur de développement Vite n'est pas correctement configuré pour les applications SPA
2. Des conflits dans les configurations de routage React
3. Des problèmes de build qui empêchent l'application de fonctionner correctement

## Étapes à suivre:

### 1. Vérifier que vous utilisez la dernière version de @vitejs/plugin-react

```bash
cd frontend
npm install @vitejs/plugin-react@latest
```

### 2. Ajouter une configuration pour le SPA routing dans le fichier public/_redirects

Créez un fichier `frontend/public/_redirects` avec le contenu suivant:

```
/* /index.html 200
```

### 3. Ajouter un fichier index.html à la racine du dossier public

Si ce fichier n'existe pas déjà, créez-le avec:

```html
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Mon Application</title>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>
</html>
```

### 4. Redémarrer le serveur de développement

```bash
cd frontend
npm run dev
```

### 5. Testez les routes

- http://localhost:3000/test
- http://localhost:3000/login 
- http://localhost:3000/dashboard

### 6. Si le problème persiste:

1. Essayez d'utiliser un serveur de développement différent comme `npm run preview` après avoir fait un build:
   ```bash
   npm run build
   npm run preview
   ```

2. Vérifiez si le problème existe aussi en mode production.

3. Examinez les logs du serveur pour voir s'il y a des erreurs spécifiques.

## Si les erreurs TypeScript bloquent le démarrage

Si les erreurs TypeScript dans `ConfigurationPanel.tsx` bloquent le démarrage de l'application, vous pouvez temporairement renommer ce fichier:

```bash
mv src/monitoring/ConfigurationPanel.tsx src/monitoring/ConfigurationPanel.tsx.bak
```

Ou créez un fichier minimal pour remplacer celui qui pose problème:

```tsx
// src/monitoring/ConfigurationPanel.tsx
import React from 'react';
export const ConfigurationPanel = () => <div>Configuration Panel (temporairement désactivé)</div>;
export default ConfigurationPanel;
``` 