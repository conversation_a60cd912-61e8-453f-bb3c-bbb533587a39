/// <reference types="cypress" />

// Import other commands files
import './commands';

// Déclarations globales pour les variables de test
declare global {
  namespace Cypress {
    // Ajoutez vos commandes personnalisées ici
    interface Chainable {
      /**
       * Custom command to log in with given email and password;
       */
      login(email: string, password: string): Chainable<void>;

      /**
       * Custom command to select DOM element by data-testid attribute;
       */
      getByTestId(testId: string): Chainable<Element>;

      /**
       * Custom command to log out the current user;
       */
      logout(): Chainable<void>;

      /**
       * Custom command to check accessibility;
       */
      checkA11y(context?: any, options?: any): Chainable<void>;

      /**
       * Custom command to connect wallet;
       */
      connectWallet(walletType: string): Chainable<void>;

      /**
       * Custom command for interception
       */
      intercept(method: string, url: string, response?: any): Chainable<void>;
      
      /**
       * Viewport command with preset
       */
      viewport(preset: string): Chainable<void>;
      viewport(width: number, height: number): Chainable<void>;
    }
  }
} 