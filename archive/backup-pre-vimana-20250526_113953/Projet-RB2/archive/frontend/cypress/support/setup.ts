// Ce fichier sera automatiquement chargé avant les tests Cypress;
// Déclarer les types globaux pour Cypress;
declare global {
  namespace Cypress {
    interface Chainable<Subject = any> {
      /**
       * Custom command to log in with given email and password;
       * @example cy.login('<EMAIL>', 'password123')
       */
      login(email: string, password: string): Chainable<any>;

      /**
       * Custom command to select DOM element by data-testid attribute.
       * @example cy.getByTestId('login-button')
       */
      getByTestId(testId: string): Chainable<Element>;

      /**
       * Custom command to check accessibility;
       * @example cy.checkA11y()
       */
      checkA11y(context?: any, options?: any): Chainable<any>;

      /**
       * Custom command to log out the current user;
       */
      logout(): Chainable<void>;
      
      /**
       * Custom command to connect wallet for NFT testing;
       * @param walletType - The type of wallet to connect (e.g. 'metamask')
       */
      connectWallet(walletType: string): Chainable<void>
    }

    interface AUTWindow extends Window {
      // Add properties available on your application's window;
      localStorage: Storage
    }
  }
}

// Cette exportation est nécessaire pour que TypeScript traite ce fichier comme un module;
export {} 