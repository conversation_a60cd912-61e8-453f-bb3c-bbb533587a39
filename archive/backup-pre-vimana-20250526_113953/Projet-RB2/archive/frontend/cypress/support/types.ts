/// <reference types="cypress" />

export {}

// Type défini pour les données de réservation
export interface BookingData {
  hotelId: string
  checkIn: string
  checkOut: string
  guests: number
  roomType: string
  specialRequests?: string
  price: number
}

declare global {
  namespace Cypress {
    interface Chainable<Subject> {
      /**
       * Custom command to login with email and password
       * @param email - The user's email
       * @param password - The user's password
       * @example cy.login('<EMAIL>', 'password123')
       */
      login(email: string, password: string): Chainable<void>

      /**
       * Custom command to inject axe-core for accessibility testing
       * @example cy.injectAxe()
       */
      injectAxe(): Chainable<void>

      /**
       * Custom command to check accessibility
       * @example cy.checkA11y()
       */
      checkA11y(): Chainable<void>

      /**
       * Custom command to focus next focusable element
       * @example cy.tab()
       */
      tab(): Chainable<JQuery<HTMLElement>>

      /**
       * Custom command to check page accessibility
       * @param path - The page path to check
       * @example cy.checkPageA11y('/dashboard')
       */
      checkPageA11y(path: string): Chainable<void>

      /**
       * Custom command to check element accessibility
       * @param selector - The element selector to check
       * @example cy.checkElementA11y('.button')
       */
      checkElementA11y(selector: string): Chainable<void>

      /**
       * Custom command to check accessibility with specific rules
       * @param rules - Array of accessibility rules to check
       * @example cy.checkA11yWithRules(['color-contrast'])
       */
      checkA11yWithRules(rules: string[]): Chainable<void>

      /**
       * Custom command to check accessibility excluding elements
       * @param excludeSelector - Selector for elements to exclude
       * @example cy.checkA11yExclude('.skip-a11y')
       */
      checkA11yExclude(excludeSelector: string): Chainable<void>

      /**
       * Custom command to check color contrast accessibility
       * @param selector - The element selector to check
       * @example cy.checkColorContrast('.text-element')
       */
      checkColorContrast(selector: string): Chainable<void>

      /**
       * Custom command to check keyboard navigation accessibility
       * @example cy.checkKeyboardNavigation()
       */
      checkKeyboardNavigation(): Chainable<void>

      /**
       * Custom command to check focus management
       * @param selector - The element selector to check
       * @example cy.checkFocusManagement('.interactive-element')
       */
      checkFocusManagement(selector: string): Chainable<void>

      /**
       * Custom command to check form accessibility
       * @param formSelector - The form selector to check
       * @example cy.checkFormA11y('form')
       */
      checkFormA11y(formSelector: string): Chainable<void>

      /**
       * Connecte un utilisateur avec les identifiants fournis
       * @example cy.login('<EMAIL>', 'password123')
       */
      login(email: string, password: string): Chainable<Element>
      
      /**
       * Attache un fichier à un input
       * @example cy.get('[data-testid="file-upload"]').attachFile('test.json')
       */
      attachFile(filePath: string): Chainable<Element>
    }
  }
}

/**
 * Rôles utilisateur dans l'application
 */
export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  MANAGER = 'MANAGER',
  GUEST = 'GUEST'
}

/**
 * Interface utilisateur pour les tests
 */
export interface User {
  id: string
  email: string
  name: string
  role: UserRole
}

/**
 * Status des analyses
 */
export enum AnalysisStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

/**
 * Priorité des analyses
 */
export enum AnalysisPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH'
}

/**
 * Interface pour les analyses
 */
export interface Analysis {
  id: string
  status: AnalysisStatus
  priority: AnalysisPriority
  createdAt: string
  completedAt?: string
  results?: any
}

/**
 * Interface pour les événements d'audit
 */
export interface AuditEvent {
  id: string
  userId: string
  action: string
  resource: string
  timestamp: string
  details?: any
}

/**
 * Interface pour les incidents
 */
export interface Incident {
  id: string
  title: string
  description: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED'
  assignedTo?: string
  createdAt: string
  updatedAt: string
}

// Type pour les raccourcis clavier
export interface Shortcut {
  key: string
  description: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
}

// Autres types utiles pour les tests
export interface UserData {
  email: string
  password: string
  name?: string
}