/// <reference types="cypress" />
/// <reference types="mocha" />

// @ts-nocheck
// ***********************************************
// This example commands.ts shows you how to
// ***********************************************

// Import des types Cypress
import 'cypress-axe';
import 'cypress-file-upload';
import { UserData } from './types';

// Extension des types Cypress
declare global {
  namespace Cypress {
    interface Chainable {
      // Commande personnalisée pour se connecter
      // @param email - Email de l'utilisateur
      // @param password - Mot de passe de l'utilisateur
      // @example cy.login('<EMAIL>', 'password')
      login(email: string, password: string): Chainable<Element>

      // Commande personnalisée pour vérifier une réponse API
      // @param endpoint - Endpoint API à vérifier
      // @param expectedStatus - Code de statut HTTP attendu (défaut: 200)
      // @example cy.verifyApiResponse('/api/users', 200)
      verifyApiResponse(endpoint: string, expectedStatus?: number): Chainable<Element>

      // Custom command to get by test id
      getByTestId(testId: string): Chainable<Element>

      // Custom command for logout
      logout(): Chainable<Element>

      // Custom command for accessibility check
      checkA11y(context?: any, options?: any): Chainable<void>

      // Custom command for connecting to a wallet
      connectWallet(walletType: string): Chainable<void>
    }
  }
}

// Custom command to get by test id
Cypress.Commands.add('getByTestId', (testId: string) => {
  return cy.get(`[data-testid="${testId}"]`)
})

// Custom command for login
Cypress.Commands.add('login', (email: string, password: string) => {
  cy.visit('/login')
  cy.get('[data-testid="email-input"]').type(email)
  cy.get('[data-testid="password-input"]').type(password)
  cy.get('[data-testid="login-button"]').click()
  cy.url().should('include', '/dashboard')
})

Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="logout-button"]').click()
  cy.url().should('include', '/login')
})

Cypress.Commands.add('checkA11y', (context, options) => {
  cy.injectAxe()
  cy.checkA11y(context, options)
})

Cypress.Commands.add('connectWallet', (walletType: string) => {
  cy.get('[data-testid="connect-wallet-button"]').click()
  if (walletType === 'metamask') {
    cy.get('[data-testid="metamask-option"]').click()
    cy.get('[data-testid="wallet-address"]').should('be.visible')
  } else {
    cy.get(`[data-testid="${walletType}-option"]`).click()
  }
})

// Commande de vérification d'API
Cypress.Commands.add('verifyApiResponse', (endpoint: string, expectedStatus = 200) => {
  cy.request({
    url: `${Cypress.env('apiUrl')}${endpoint}`,
    failOnStatusCode: false
  }).then((response) => {
    expect(response.status).to.eq(expectedStatus)
  })
})

export { }