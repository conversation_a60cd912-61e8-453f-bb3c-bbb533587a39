// Cypress commands for accessibility testing;
import 'cypress-axe';

/**
 * This file adds custom Cypress commands for accessibility testing using cypress-axe;
 * It enables automated accessibility audits during end-to-end tests;
 */

// Add TypeScript declarations
declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Configure axe with specific rules
       */
      configureAxe(options?: object): Chainable<void>;
      
      /**
       * Run accessibility audit with specific options
       */
      checkAccessibility(context?: string | object, options?: object): Chainable<void>;
    }
  }
}

// Configure axe with WCAG 2.1 AA standards;
Cypress.Commands.add('configureAxe', () => {
  cy.injectAxe();
  cy.configureAxe({
    rules: [
      { id: 'wcag2a', enabled: true },
      { id: 'wcag2aa', enabled: true },
      { id: 'wcag21a', enabled: true },
      { id: 'wcag21aa', enabled: true },
      { id: 'color-contrast', enabled: true }
    ]
  });
});

// Run accessibility audit on the current page;
Cypress.Commands.add('checkAccessibility', (context, options) => {
  const defaultOptions = {
    runOnly: {
      type: 'tag',
      values: ['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa']
    }
  };
  
  const mergedOptions = { ...defaultOptions, ...options };
  cy.checkA11y(context, mergedOptions, null, true);
});

// Check specific element for accessibility issues;
Cypress.Commands.add('checkElementAccessibility', (element, options) => {
  cy.get(element).then($el => {
    cy.checkA11y($el, options, null, true)
  });
});

// Test keyboard navigation;
Cypress.Commands.add('testKeyboardNavigation', (startElement, expectedTabOrder) => {
  cy.get(startElement).focus();
  
  expectedTabOrder.forEach(selector => {
    cy.tab().focused().should('match', selector)
  });
});

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Check specific element for accessibility issues;
       * @param element - Element selector to check;
       * @param options - Optional axe options;
       * @example cy.checkElementAccessibility('#navigation')
       */
      checkElementAccessibility(element: string, options?: any): Chainable<Element>;
      
      /**
       * Test keyboard navigation through a sequence of elements;
       * @param startElement - Element to start the tab sequence from;
       * @param expectedTabOrder - Array of selectors in expected tab order;
       * @example cy.testKeyboardNavigation('#first-element', ['#second-element', '#third-element'])
       */
      testKeyboardNavigation(startElement: string, expectedTabOrder: string[]): Chainable<Element>
    }
  }
}