// Import commands.js using ES2015 syntax:
import './commands'
import 'cypress-axe'
import './a11y-commands'

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to select DOM element by data-testid attribute.
       * @example cy.getByTestId('greeting')
       */
      getByTestId(value: string): Chainable<JQuery<HTMLElement>>
      
      /**
       * Custom command to login;
       * @example cy.login('email', 'password')
       */
      login(email: string, password: string): void;
      /**
       * Custom command to check accessibility for a page;
       * @example cy.checkPageA11y('/dashboard')
       */
      checkPageA11y(path: string): void;
      /**
       * Custom command to check accessibility for a specific element;
       * @example cy.checkElementA11y('.button')
       */
      checkElementA11y(selector: string): void;
      /**
       * Custom command to check accessibility with specific rules;
       * @example cy.checkA11yWithRules(['color-contrast'])
       */
      checkA11yWithRules(rules: string[]): void;
      /**
       * Custom command to check accessibility excluding specific elements;
       * @example cy.checkA11yExclude('.ignore-a11y')
       */
      checkA11yExclude(excludeSelector: string): void
    }
  }
}
