import 'cypress-axe';

describe('Visual Regression Tests - Critical Components', () => {
  beforeEach(() => {
    cy.visit('/');
    cy.injectAxe()
  });

  describe('Header Component', () => {
    it('should maintain visual consistency', () => {
      cy.get('[data-testid="header"]').should('be.visible');
      cy.checkElementA11y('[data-testid="header"]');
      cy.get('[data-testid="header"]').matchImageSnapshot('header-component')
    });

    it('should be responsive on mobile viewport', () => {
      cy.viewport('iphone-x');
      cy.get('[data-testid="header"]').should('be.visible');
      cy.checkElementA11y('[data-testid="header"]');
      cy.get('[data-testid="header"]').matchImageSnapshot('header-component-mobile')
    });
  });

  describe('Navigation Menu', () => {
    it('should display correctly in desktop view', () => {
      cy.get('[data-testid="nav-menu"]').should('be.visible');
      cy.checkElementA11y('[data-testid="nav-menu"]');
      cy.get('[data-testid="nav-menu"]').matchImageSnapshot('nav-menu-desktop')
    });

    it('should collapse into hamburger menu on mobile', () => {
      cy.viewport('iphone-x');
      cy.get('[data-testid="hamburger-menu"]').should('be.visible');
      cy.checkElementA11y('[data-testid="hamburger-menu"]');
      cy.get('[data-testid="hamburger-menu"]').matchImageSnapshot('nav-menu-mobile')
    });
  });

  describe('Forms', () => {
    it('should maintain consistent styling for input fields', () => {
      cy.visit('/login');
      cy.get('[data-testid="login-form"]').should('be.visible');
      cy.checkElementA11y('[data-testid="login-form"]');
      cy.get('[data-testid="login-form"]').matchImageSnapshot('login-form');
    });

    it('should show proper validation states', () => {
      cy.visit('/login');
      cy.get('[data-testid="email-input"]').type('invalid-email');
      cy.get('[data-testid="submit-button"]').click();
      cy.get('[data-testid="error-state"]').should('be.visible');
      cy.checkElementA11y('[data-testid="error-state"]');
      cy.get('[data-testid="error-state"]').matchImageSnapshot('form-error-state');
    });
  });

  describe('Modal Components', () => {
    it('should render modals with proper overlay', () => {
      cy.get('[data-testid="open-modal"]').click();
      cy.get('[data-testid="modal"]').should('be.visible');
      cy.checkElementA11y('[data-testid="modal"]');
      cy.get('[data-testid="modal"]').matchImageSnapshot('modal-component')
    });
  });

  describe('Loading States', () => {
    it('should display loading spinners consistently', () => {
      cy.intercept('GET', '/api/data', (req) => {
        req.reply({ delay: 1000, fixture: 'data.json' });
      });
      cy.visit('/data');
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      cy.get('[data-testid="loading-spinner"]').matchImageSnapshot('loading-state');
    });
  });
});