import '@percy/cypress';

describe('Specialized Pages Tests', () => {
  beforeEach(() => {
    // Suppress uncaught exceptions related to React hydration
    Cypress.on('uncaught:exception', (err) => {
      if (err.message.includes('hydration') || err.message.includes('Hydration')) {
        return false;
      }
      return true;
    });
  });

  describe('TravelAgenciesPage', () => {
    it('should load the travel agencies page', () => {
      cy.visit('/travel-agencies');
      cy.contains('Partner with RetreatAndBe').should('be.visible');
      cy.contains('Global Network').should('be.visible');
      cy.contains('Register Your Agency').should('be.visible');
    });

    it('should have a working registration form', () => {
      cy.visit('/travel-agencies');
      cy.get('input[type="text"]').first().type('Test Agency');
      cy.get('input[type="email"]').type('<EMAIL>');
      cy.get('select').select('1-3 years');
      cy.get('input[placeholder="e.g., Europe, Asia"]').type('Europe');
      cy.get('textarea').type('This is a test agency description');
      // Don't actually submit the form in tests
      cy.get('button[type="submit"]').should('be.visible');
    });

    it('should match visual snapshot', () => {
      cy.visit('/travel-agencies');
      cy.percySnapshot('Travel Agencies Page');
    });
  });

  describe('CaterersPage', () => {
    it('should load the caterers page', () => {
      cy.visit('/caterers');
      cy.contains('Cater for Wellness Retreats').should('be.visible');
      cy.contains('Benefits for Caterers').should('be.visible');
      cy.contains('Register as a Caterer').should('be.visible');
    });

    it('should have a working registration form', () => {
      cy.visit('/caterers');
      cy.get('input[type="text"]').first().type('Test Catering Service');
      cy.get('input[type="email"]').type('<EMAIL>');
      cy.get('select').select('Vegan & Plant-Based');
      cy.get('input[placeholder="e.g., Paris, France"]').type('Paris, France');
      cy.get('textarea').type('We specialize in plant-based cuisine for wellness retreats');
      // Don't actually submit the form in tests
      cy.get('button[type="submit"]').should('be.visible');
    });

    it('should match visual snapshot', () => {
      cy.visit('/caterers');
      cy.percySnapshot('Caterers Page');
    });
  });

  describe('InsurancePage', () => {
    it('should load the insurance page', () => {
      cy.visit('/insurance');
      cy.contains('Travel Insurance').should('be.visible');
      // This test might need to be adjusted based on the actual implementation
      // and whether the page makes API calls that need to be mocked
    });

    it('should match visual snapshot', () => {
      cy.visit('/insurance');
      cy.percySnapshot('Insurance Page');
    });
  });

  describe('TokenPage', () => {
    it('should load the token page', () => {
      cy.visit('/token');
      // Check for components that should be visible
      // This will depend on the actual implementation of the TokenInfo component
    });

    it('should match visual snapshot', () => {
      cy.visit('/token');
      cy.percySnapshot('Token Page');
    });
  });

  describe('NFTGalleryPage', () => {
    it('should load the NFT gallery page', () => {
      cy.visit('/nft-gallery');
      cy.contains('NFT Gallery').should('be.visible');
      cy.contains('Your NFTs').should('be.visible');
      cy.contains('Exclusive Collections').should('be.visible');
    });

    it('should match visual snapshot', () => {
      cy.visit('/nft-gallery');
      cy.percySnapshot('NFT Gallery Page');
    });
  });

  describe('WellnessPage', () => {
    it('should load the wellness page', () => {
      cy.visit('/wellness');
      cy.contains('Votre Espace Bien-être').should('be.visible');
      cy.contains('Réservation de Retraites').should('be.visible');
      cy.contains('Méditation Guidée').should('be.visible');
    });

    it('should switch between tabs', () => {
      cy.visit('/wellness');
      cy.contains('Méditation Guidée').click();
      // Check that the meditation tab is active
      // This will depend on the actual implementation
      
      cy.contains('Réservation de Retraites').click();
      // Check that the booking tab is active
    });

    it('should match visual snapshot', () => {
      cy.visit('/wellness');
      cy.percySnapshot('Wellness Page');
    });
  });
});
