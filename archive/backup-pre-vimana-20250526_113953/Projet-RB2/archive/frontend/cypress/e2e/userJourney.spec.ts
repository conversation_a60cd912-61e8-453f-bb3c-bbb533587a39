describe('User Journey E2E Tests', () => {
  beforeEach(() => {
    cy.intercept('GET', '/api/recommendations', { fixture: 'recommendations.json' });
    cy.intercept('POST', '/api/analytics', { statusCode: 200 });
  });

  describe('Authentication Flow', () => {
    it('should complete the sign-up process', () => {
      cy.visit('/signup');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('SecurePass123!');
      cy.get('[data-testid="signup-button"]').click();
      cy.url().should('include', '/dashboard')
    });

    it('should handle failed authentication', () => {
      cy.intercept('POST', '/api/auth/login', {
        statusCode: 401,
        body: { error: 'Invalid credentials' }
      });

      cy.visit('/login');
      cy.get('[data-testid="login-form"]').submit();
      cy.get('[data-testid="error-message"]').should('be.visible');
    });
  });

  describe('Recommendation Flow', () => {
    beforeEach(() => {
      cy.login(); // Custom command
    });

    it('should display personalized recommendations', () => {
      cy.visit('/recommendations');
      cy.get('[data-testid="recommendation-card"]').should('have.length.gt', 0);
      cy.get('[data-testid="recommendation-score"]').should('be.visible')
    });

    it('should update recommendations in real-time', () => {
      cy.visit('/recommendations');
      cy.intercept('GET', '/api/recommendations/realtime', {
        body: { newRecommendations: [] }
      });
      cy.get('[data-testid="real-time-toggle"]').click();
      cy.get('[data-testid="loading-indicator"]').should('not.exist');
    });
  });

  describe('Performance Checks', () => {
    it('should load main components within performance budget', () => {
      cy.visit('/', {
        onBeforeLoad: (win) => {
          cy.spy(win.performance, 'mark').as('performanceMark')
        }
});

      cy.get('@performanceMark').then((mark) => {
        expect(mark).to.be.called;
        // Vérifier les marqueurs de performance spécifiques
      });
    });
  });
});