import '@percy/cypress';

describe('Visual Regression Tests', () => {
  beforeEach(() => {
    cy.visit('/')
  });

  describe('Homepage Visual Tests', () => {
    it('should match homepage snapshot', () => {
      cy.percySnapshot('Homepage')
    });

    it('should match mobile homepage snapshot', () => {
      cy.viewport('iphone-x');
      cy.percySnapshot('Homepage - Mobile')
    });
  });

  describe('Booking Flow Visual Tests', () => {
    it('should match booking form snapshot', () => {
      cy.get('[data-testid="booking-button"]').click();
      cy.percySnapshot('Booking Form')
    });

    it('should match booking confirmation snapshot', () => {
      cy.get('[data-testid="booking-button"]').click();
      cy.get('[data-testid="date-picker"]').click();
      cy.get('.available-date').first().click();
      cy.get('[data-testid="guests-input"]').type('2');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="submit-booking"]').click();
      cy.percySnapshot('Booking Confirmation')
    });
  });

  describe('Search Results Visual Tests', () => {
    it('should match search results snapshot', () => {
      cy.get('[data-testid="search-input"]').type('test location');
      cy.get('[data-testid="search-button"]').click();
      cy.percySnapshot('Search Results')
    });

    it('should match empty search results snapshot', () => {
      cy.get('[data-testid="search-input"]').type('nonexistent location');
      cy.get('[data-testid="search-button"]').click();
      cy.percySnapshot('Empty Search Results')
    });
  });

  describe('Responsive Design Visual Tests', () => {
    const viewports = [
      ['macbook-15', 'Desktop'],
      ['ipad-2', 'Tablet'],
      ['iphone-x', 'Mobile']
    ];

    viewports.forEach(([device, label]) => {
      it(`should match ${label} layout`, () => {
        cy.viewport(device);
        cy.percySnapshot(`Layout - ${label}`);
      });
    });
  });
});