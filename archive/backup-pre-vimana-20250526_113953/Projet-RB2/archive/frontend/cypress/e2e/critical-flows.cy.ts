/// <reference types="cypress" />
/// <reference types="mocha" />
// @ts-nocheck
// Ce fichier est délibérément exclu de la vérification TypeScript car il y a des problèmes de compatibilité 
// entre les définitions de types de Cypress, Mocha et Jest.

describe('Critical User Flows', () => {
  describe('Authentication', () => {
    it('should successfully login', () => {
      cy.visit('/login')
      cy.get('[data-testid="email-input"]').type('<EMAIL>')
      cy.get('[data-testid="password-input"]').type('password123')
      cy.get('[data-testid="login-button"]').click()
      cy.url().should('include', '/dashboard')
    })
  })

  describe('Business Features', () => {
    beforeEach(() => cy.login("<EMAIL>", "password123"))
    
    it('should create new content', () => {
      cy.visit('/content/new')
      cy.get('[data-testid="title-input"]').type('New Content')
      cy.get('[data-testid="save-button"]').click()
      cy.get('[data-testid="success-message"]').should('be.visible')
    })
  })
})
