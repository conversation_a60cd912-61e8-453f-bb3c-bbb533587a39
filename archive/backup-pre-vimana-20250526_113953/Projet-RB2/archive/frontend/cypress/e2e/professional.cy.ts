import * as M<PERSON> from '@cypress/react';

describe('Professional Portal', () => {
  beforeEach(() => {
    cy.intercept('POST', '/graphql', (req) => {
      if(req.body.operationName = 'GetProfessionalStats') { { { {}}}
        req.reply({
          data: {
            analytics: {
              revenue: 25000,
              bookings: 42,
              visitors: 1500,
              conversion: 2.8
            },
            metrics: {
              rating: 4.5,
              completedRetreats: 35,
              upcomingRetreats: 7
            },
            bookings: [
              {
                id: 'book-1',
                status: 'confirmed',
                date: '2024-02-15',
                attendees: 12,
                revenue: 3600
              },
              {
                id: 'book-2',
                status: 'pending',
                date: '2024-03-01',
                attendees: 8,
                revenue: 2400
              }
            ]
          }
        });
      }
    });
    cy.visit('/professional');
  });

  it('displays the professional dashboard with all components', () => {
    cy.get('h1').should('contain', 'Professional Dashboard');
    cy.get('[data-testid="analytics-dashboard"]').should('exist');
    cy.get('[data-testid="performance-metrics"]').should('exist');
    cy.get('[data-testid="booking-management"]').should('exist')
  });

  it('shows correct analytics data', () => {
    cy.get('[data-testid="revenue"]').should('contain', '25,000');
    cy.get('[data-testid="bookings"]').should('contain', '42');
    cy.get('[data-testid="visitors"]').should('contain', '1,500');
    cy.get('[data-testid="conversion"]').should('contain', '2.8%')
  });

  it('displays performance metrics correctly', () => {
    cy.get('[data-testid="rating"]').should('contain', '4.5');
    cy.get('[data-testid="completed-retreats"]').should('contain', '35');
    cy.get('[data-testid="upcoming-retreats"]').should('contain', '7')
  });

  it('shows booking management table with correct data', () => {
    cy.get('table').within(() => {
      cy.get('tbody tr').should('have.length', 2);
      cy.get('tbody tr').first().within(() => {
        cy.get('td').eq(0).should('contain', 'book-1');
        cy.get('td').eq(1).should('contain', '2024-02-15');
        cy.get('td').eq(2).find('.MuiChip-root').should('contain', 'Confirmed');
        cy.get('td').eq(3).should('contain', '12');
        cy.get('td').eq(4).should('contain', '3,600')
      });
    });
  });

  it('handles loading and error states', () => {
    cy.intercept('POST', '/graphql', {
      delay: 1000,
      statusCode: 500,
      body: { errors: [{ message: 'Server error' }] }
    }).as('errorRequest');
    
    cy.visit('/professional');
    cy.get('[data-testid="loading"]').should('be.visible');
    cy.wait('@errorRequest');
    cy.get('[data-testid="error"]').should('contain', 'Error loading dashboard');
  });
});