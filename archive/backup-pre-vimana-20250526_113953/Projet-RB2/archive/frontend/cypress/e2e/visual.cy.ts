describe('Visual Regression Tests', () => {
  beforeEach(() => {
    cy.visit('/');
    // Attendre que l'application soit complètement chargée
    cy.get('[data-testid="app-root"]').should('exist')
  });

  it('should match homepage snapshot', () => {
    cy.wait(2000); // Attendre les animations
    cy.percySnapshot('Homepage')
  });

  it('should match NFT grid snapshot', () => {
    cy.get('[data-testid="nft-grid"]').should('exist');
    cy.percySnapshot('NFT Grid')
  });

  it('should match NFT detail modal snapshot', () => {
    cy.get('[data-testid="nft-card"]').first().click();
    cy.get('[data-testid="nft-modal"]').should('be.visible');
    cy.percySnapshot('NFT Detail Modal')
  });

  it('should match mobile layout snapshots', () => {
    cy.viewport('iphone-x');
    cy.wait(1000); // Attendre le responsive
    cy.percySnapshot('Homepage Mobile');

    // Vérifier le menu mobile
    cy.get('[data-testid="mobile-menu-button"]').click();
    cy.percySnapshot('Mobile Menu Open')
  });

  it('should match dark mode snapshots', () => {
    cy.get('[data-testid="theme-toggle"]').click();
    cy.wait(500); // Attendre la transition
    cy.percySnapshot('Homepage Dark Mode')
  });

  it('should match form validation states', () => {
    cy.get('[data-testid="contact-form"]').within(() => {
      // État initial
      cy.percySnapshot('Form Initial State');

      // État avec erreurs
      cy.get('button[type="submit"]').click();
      cy.percySnapshot('Form Error State');

      // État de succès
      cy.get('input[name="email"]').type('<EMAIL>');
      cy.get('textarea[name="message"]').type('Test message');
      cy.get('button[type="submit"]').click();
      cy.percySnapshot('Form Success State')
    });
  });

  it('should match loading states', () => {
    // Simuler une connexion lente
    cy.intercept('GET', '/api/nfts', (req) => {
      req.on('response', (res) => {
        res.setDelay(2000)
      });
    });

    cy.visit('/marketplace');
    cy.percySnapshot('Loading State');
  });

  it('should match error states', () => {
    // Simuler une erreur
    cy.intercept('GET', '/api/nfts', {
      statusCode: 500,
      body: { error: 'Server Error' }
    });

    cy.visit('/marketplace');
    cy.percySnapshot('Error State');
  });

  it('should match responsive breakpoints', () => {
    const breakpoints = [
      'iphone-6',
      'ipad-2',
      [1024, 768],
      [1920, 1080]
    ];

    breakpoints.forEach(breakpoint => {
      if (Array.isArray(breakpoint)) {
        cy.viewport(breakpoint[0], breakpoint[1]);
      } else {
        cy.viewport(breakpoint)
      }
      cy.wait(500);
      cy.percySnapshot(`Responsive - ${Array.isArray(breakpoint) ? breakpoint.join('x') : breakpoint}`);
    });
  });
});
