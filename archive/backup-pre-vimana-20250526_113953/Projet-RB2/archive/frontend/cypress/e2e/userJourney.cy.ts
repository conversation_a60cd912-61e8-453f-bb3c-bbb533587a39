/// <reference types="cypress" />

// @ts-nocheck
// Test du parcours utilisateur complet
describe('User Journey', () => {
  beforeEach(() => {
    // Intercepter la requête d'authentification et renvoyer une réponse simulée
    cy.intercept('POST', '/api/auth/login', {
      statusCode: 200,
      body: {
        user: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>'
        },
        token: 'fake-jwt-token'
      }
    }).as('loginRequest');

    // Intercepter les requêtes API pour les projets
    cy.intercept('GET', '/api/projects', {
      statusCode: 200,
      body: [
        { 
          id: '1', 
          title: 'Projet Test 1', 
          description: 'Description du projet 1',
          status: 'En cours'
        },
        { 
          id: '2', 
          title: 'Projet Test 2', 
          description: 'Description du projet 2',
          status: 'Terminé'
        }
      ]
    }).as('getProjects');

    // Initialiser le localStorage pour éviter les problèmes de CORS
    cy.window().then((win: Cypress.AUTWindow) => {
      win.localStorage.clear()
    });

    // Visiter la page d'accueil
    cy.visit('/');
  });

  it('should complete a full user journey', () => {
    // 1. Connexion
    cy.get('[data-testid="login-button"]').click();
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('password123');
    cy.get('[data-testid="login-submit"]').click();

    // Vérifier que la requête d'authentification a été effectuée
    cy.wait('@loginRequest');

    // Vérifier que l'utilisateur est redirigé vers le tableau de bord
    cy.url().should('include', '/dashboard');
    cy.get('[data-testid="welcome-message"]').should('contain', 'Bienvenue, Test User');

    // 2. Navigation vers la liste des projets
    cy.get('[data-testid="nav-projects"]').click();
    cy.url().should('include', '/projects');
    
    // Attendre que les projets soient chargés
    cy.wait('@getProjects');
    
    // Vérifier que les projets sont affichés
    cy.get('[data-testid="project-card"]').should('have.length', 2);
    cy.get('[data-testid="project-card"]').first().should('contain', 'Projet Test 1');

    // 3. Créer un nouveau projet
    cy.get('[data-testid="create-project-button"]').click();
    cy.url().should('include', '/projects/new');

    // Remplir le formulaire
    cy.get('[data-testid="project-title-input"]').type('Nouveau Projet Test');
    cy.get('[data-testid="project-description-input"]').type('Description du nouveau projet');
    cy.get('[data-testid="project-status-select"]').select('En cours');

    // Intercepter la requête POST pour la création du projet
    cy.intercept('POST', '/api/projects', {
      statusCode: 201,
      body: {
        id: '3',
        title: 'Nouveau Projet Test',
        description: 'Description du nouveau projet',
        status: 'En cours'
      }
    }).as('createProject');

    // Soumettre le formulaire
    cy.get('[data-testid="project-submit-button"]').click();
    cy.wait('@createProject');

    // Vérifier la redirection et le message de succès
    cy.url().should('include', '/projects');
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="success-message"]').should('contain', 'Projet créé avec succès');

    // 4. Rechercher un projet
    cy.get('[data-testid="search-input"]').type('Test 1');
    cy.get('[data-testid="project-card"]').should('have.length', 1);
    cy.get('[data-testid="project-card"]').first().should('contain', 'Projet Test 1');

    // 5. Voir les détails d'un projet
    cy.get('[data-testid="project-card"]').first().click();
    cy.url().should('include', '/projects/1');
    cy.get('[data-testid="project-title"]').should('contain', 'Projet Test 1');
    cy.get('[data-testid="project-description"]').should('contain', 'Description du projet 1');

    // 6. Modifier un projet
    cy.get('[data-testid="edit-project-button"]').click();
    cy.get('[data-testid="project-title-input"]').clear().type('Projet Test 1 Modifié');

    // Intercepter la requête PUT pour la modification du projet
    cy.intercept('PUT', '/api/projects/1', {
      statusCode: 200,
      body: {
        id: '1',
        title: 'Projet Test 1 Modifié',
        description: 'Description du projet 1',
        status: 'En cours'
      }
    }).as('updateProject');

    cy.get('[data-testid="project-submit-button"]').click();
    cy.wait('@updateProject');

    // Vérifier la redirection et le message de succès
    cy.url().should('include', '/projects/1');
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="success-message"]').should('contain', 'Projet mis à jour avec succès');

    // 7. Se déconnecter
    cy.get('[data-testid="user-menu"]').click();
    cy.get('[data-testid="logout-button"]').click();

    // Vérifier la redirection vers la page de connexion
    cy.url().should('include', '/login');
    cy.get('[data-testid="login-button"]').should('be.visible');
  });

  it('should handle form validation', () => {
    // Tester la validation du formulaire de connexion
    cy.get('[data-testid="login-button"]').click();
    cy.get('[data-testid="login-submit"]').click();

    // Vérifier les messages d'erreur
    cy.get('[data-testid="email-error"]').should('be.visible');
    cy.get('[data-testid="password-error"]').should('be.visible');

    // Tester la correction des erreurs
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="email-error"]').should('not.exist');

    cy.get('[data-testid="password-input"]').type('password123');
    cy.get('[data-testid="password-error"]').should('not.exist')
  });

  it('should handle failed login', () => {
    // Intercepter la requête d'authentification pour simuler un échec
    cy.intercept('POST', '/api/auth/login', {
      statusCode: 401,
      body: {
        message: 'Identifiants invalides'
      }
    }).as('failedLoginRequest');

    // Tenter de se connecter
    cy.get('[data-testid="login-button"]').click();
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('wrongpassword');
    cy.get('[data-testid="login-submit"]').click();

    // Vérifier la gestion de l'erreur
    cy.wait('@failedLoginRequest');
    cy.get('[data-testid="login-error"]').should('be.visible');
    cy.get('[data-testid="login-error"]').should('contain', 'Identifiants invalides');
  });

  it('should handle server errors gracefully', () => {
    // Intercepter la requête pour simuler une erreur serveur
    cy.intercept('GET', '/api/projects', {
      statusCode: 500,
      body: {
        message: 'Erreur interne du serveur'
      }
    }).as('serverError');

    // Connecter l'utilisateur (utiliser la fonction personnalisée si disponible)
    cy.get('[data-testid="login-button"]').click();
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('password123');
    cy.get('[data-testid="login-submit"]').click();

    // Naviguer vers la page des projets
    cy.get('[data-testid="nav-projects"]').click();
    
    // Vérifier la gestion de l'erreur
    cy.wait('@serverError');
    cy.get('[data-testid="error-message"]').should('be.visible');
    cy.get('[data-testid="error-message"]').should('contain', 'Erreur lors du chargement des projets');
    
    // Vérifier la présence d'un bouton pour réessayer
    cy.get('[data-testid="retry-button"]').should('be.visible');
    
    // Intercepter la nouvelle requête et simuler un succès
    cy.intercept('GET', '/api/projects', {
      statusCode: 200,
      body: [{ id: '1', title: 'Projet Test 1' }]
    }).as('retryRequest');
    
    // Cliquer sur le bouton pour réessayer
    cy.get('[data-testid="retry-button"]').click();
    cy.wait('@retryRequest');
    
    // Vérifier que les données sont affichées
    cy.get('[data-testid="project-card"]').should('have.length', 1);
  });
}); 