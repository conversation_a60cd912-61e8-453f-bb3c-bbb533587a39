/// <reference types="cypress" />
/// <reference types="cypress-axe" />

import 'cypress-axe'

describe('Booking Flow', () => {
  beforeEach(() => {
    cy.visit('/booking')
    cy.injectAxe()
  })

  it('should have no accessibility violations', () => {
    cy.checkA11y()
  })

  it('should successfully complete a booking', () => {
    // Select date
    cy.getByTestId('calendar-day-15').click()

    // Fill in booking details
    cy.getByTestId('booking-name').type('<PERSON>')
    cy.getByTestId('booking-email').type('<EMAIL>')
    cy.getByTestId('booking-phone').type('1234567890')

    // Submit booking
    cy.getByTestId('submit-booking').click()

    // Verify success message
    cy.getByTestId('success-message')
      .should('be.visible')
      .and('contain', 'Booking confirmed')

    // Check accessibility after booking completion
    cy.checkA11y()
  })

  it('should show validation errors for invalid inputs', () => {
    // Submit without filling required fields
    cy.getByTestId('submit-booking').click()

    // Verify error messages
    cy.getByTestId('name-error')
      .should('be.visible')
      .and('contain', 'Name is required')

    cy.getByTestId('email-error')
      .should('be.visible')
      .and('contain', 'Email is required')

    // Check accessibility with error states
    cy.checkA11y()
  })

  it('displays validation errors', () => {
    // Try to continue without selecting anything
    cy.contains('Continue to Review').click()

    // Verify validation messages
    cy.contains('Please select a service').should('be.visible')
    cy.contains('Please select a time slot').should('be.visible')
  })

  it('handles server errors', () => {
    // Intercept booking API call and force an error
    cy.intercept('POST', '/api/bookings', {
      statusCode: 500,
      body: {
        message: 'Server error'
      }
    })

    // Complete booking form
    cy.get('[data-testid="calendar-day-15"]').click()
    cy.contains('Yoga Session').click()
    cy.contains('09:00').click()
    cy.get('input[type="number"]').clear().type('2')
    cy.contains('Continue to Review').click()
    cy.contains('Confirm Booking').click()

    // Verify error message
    cy.contains('Server error').should('be.visible')
  })

  it('allows booking modification', () => {
    // Complete initial booking form
    cy.get('[data-testid="calendar-day-15"]').click()
    cy.contains('Yoga Session').click()
    cy.contains('09:00').click()
    cy.get('input[type="number"]').clear().type('2')
    cy.contains('Continue to Review').click()

    // Go back to edit
    cy.contains('Edit Details').click()

    // Modify booking
    cy.contains('Meditation Class').click()
    cy.contains('10:00').click()
    cy.get('input[type="number"]').clear().type('3')
    cy.contains('Continue to Review').click()

    // Verify updated summary
    cy.contains('Meditation Class').should('be.visible')
    cy.contains('10:00').should('be.visible')
    cy.contains('3 participants').should('be.visible')
  })

  it('checks accessibility', () => {
    // Run accessibility audit
    cy.injectAxe()
    cy.checkA11y()

    // Complete form using keyboard navigation
    cy.get('[data-testid="calendar-day-15"]').focus().type('{enter}')
    cy.contains('Yoga Session').focus().type('{enter}')
    cy.contains('09:00').focus().type('{enter}')
    cy.get('input[type="number"]').type('2')
    cy.get('textarea').type('Need yoga mats')
    cy.contains('Continue to Review').focus().type('{enter}')

    // Check accessibility on review page
    cy.checkA11y()
  })
})
