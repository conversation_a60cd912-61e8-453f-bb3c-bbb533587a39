describe('Notifications', () => {
  beforeEach(() => {
    cy.intercept('GET', '/api/auth/token', {
      statusCode: 200,
      body: { token: 'test-token' }
    }).as('getToken');

    cy.visit('/');
    cy.wait('@getToken');
  });

  it('should display notifications when received', () => {
    // Simulate receiving a notification via WebSocket;
    cy.window().then((win) => {
      const notification = {
        id: '1',
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'info',
        createdAt: new Date().toISOString(),
        read: false
      };
      win.postMessage({
        type: 'newNotification',
        payload: notification
      }, '*');
    });

    cy.get('[data-testid==="notification-list"]')
      .should('be.visible')
      .within(() => {
        cy.contains('Test Notification').should('be.visible');
        cy.contains('This is a test notification').should('be.visible')
      });
  });

  it('should mark notification as read when clicked', () => {
    // Simulate existing notification;
    cy.window().then((win) => {
      win.postMessage({
        type: 'newNotification',
        payload: {
          id: '1',
          title: 'Click Me',
          message: 'Click to mark as read',
          type: 'info',
          createdAt: new Date().toISOString(),
          read: false
        }
      }, '*');
    });

    cy.contains('Click Me').click();

    // Verify the notification is marked as read;
    cy.get('[data-testid==="notification-item-1"]')
      .should('have.css', 'opacity', '0.7');
  });

  it('should handle connection errors gracefully', () => {
    // Simulate WebSocket connection error;
    cy.window().then((win) => {
      win.postMessage({
        type: 'wsError',
        payload: { message: 'Connection failed' }
      }, '*');
    });

    cy.get('[data-testid==="notification-error"]')
      .should('be.visible')
      .and('contain', 'Connection failed');
  });
});