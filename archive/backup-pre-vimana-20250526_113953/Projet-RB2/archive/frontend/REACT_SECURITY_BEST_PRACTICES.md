# Bonnes pratiques de sécurité pour les applications React

Ce document présente les bonnes pratiques et recommandations pour sécuriser vos applications React contre les vulnérabilités les plus courantes. Ces principes sont illustrés dans nos tests de sécurité et devraient être appliqués lors du développement de nouvelles fonctionnalités.

## Table des matières

1. [Protection contre les attaques XSS](#protection-contre-les-attaques-xss)
2. [Sécurisation de l'authentification](#sécurisation-de-lauthentification)
3. [Gestion sécurisée des données sensibles](#gestion-sécurisée-des-données-sensibles)
4. [Sécurité des API et des requêtes HTTP](#sécurité-des-api-et-des-requêtes-http)
5. [Téléchargement sécurisé de fichiers](#téléchargement-sécurisé-de-fichiers)
6. [Gestion d'état sécurisée](#gestion-détat-sécurisée)
7. [En-têtes de sécurité HTTP](#en-têtes-de-sécurité-http)
8. [Considérations pour le déploiement](#considérations-pour-le-déploiement)

## Protection contre les attaques XSS

Les attaques Cross-Site Scripting (XSS) permettent à des attaquants d'injecter et d'exécuter du code malveillant dans le navigateur des utilisateurs.

### Bonnes pratiques :

1. **Échapper toutes les sorties de données** :
   ```jsx
   // ❌ Dangereux - permet l'injection de code
   <div>{userInput}</div>
   
   // ✅ Sécurisé - React échappe par défaut le contenu des expressions
   <div>{userInput}</div>
   ```

   Attention : Dans certains cas, React n'échappe pas automatiquement :
   ```jsx
   // ❌ Dangereux - contourne l'échappement automatique de React
   <div dangerouslySetInnerHTML={{ __html: userInput }} />
   
   // ✅ Sécurisé - utiliser une bibliothèque de sanitization
   import DOMPurify from 'dompurify';
   <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(userInput) }} />
   ```

2. **Configurer une Content Security Policy (CSP)** :
   
   Dans le fichier `index.html` :
   ```html
   <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' https://trusted-cdn.com; style-src 'self';">
   ```
   
   Ou mieux, via les en-têtes HTTP du serveur :
   ```
   Content-Security-Policy: default-src 'self'; script-src 'self' https://trusted-cdn.com;
   ```

3. **Valider et sanitiser toutes les entrées utilisateur** :
   ```jsx
   // ✅ Validation des entrées avec une bibliothèque comme Yup
   import * as Yup from 'yup';
   
   const schema = Yup.object().shape({
     email: Yup.string().email().required(),
     comment: Yup.string().max(500).matches(/^[a-zA-Z0-9\s.,!?]*$/)
   });
   
   const validate = async (data) => {
     try {
       await schema.validate(data);
       return true;
     } catch (error) {
       return false;
     }
   };
   ```

## Sécurisation de l'authentification

### Bonnes pratiques :

1. **Stockage sécurisé des tokens JWT** :
   ```jsx
   // ❌ Non sécurisé - localStorage est accessible par JavaScript
   localStorage.setItem('token', jwt);
   
   // ✅ Plus sécurisé - utiliser un cookie HttpOnly
   // Note: Ceci est géré côté serveur, pas directement en JavaScript
   // Set-Cookie: authToken=xyz; HttpOnly; Secure; SameSite=Strict; Path=/;
   
   // Si vous devez stocker côté client, créez un service pour encapsuler la logique
   class TokenService {
     setToken(token) {
       // Logique de stockage avec validation et expiration
     }
     
     getToken() {
       // Logique de récupération avec vérification
     }
     
     clearToken() {
       // Nettoyage complet du token
     }
   }
   ```

2. **Expiration et rafraîchissement des tokens** :
   ```jsx
   // Service d'authentification avec gestion des expirations
   class AuthService {
     async login(credentials) {
       // Obtenir les tokens (accès et rafraîchissement)
     }
     
     isTokenExpired(token) {
       // Vérifier l'expiration
       const decoded = jwtDecode(token);
       return decoded.exp < Date.now() / 1000;
     }
     
     async refreshTokenIfNeeded() {
       const token = tokenService.getToken();
       if (this.isTokenExpired(token)) {
         // Utiliser le refreshToken pour obtenir un nouveau token
       }
     }
   }
   ```

3. **Protection contre les attaques par force brute** :
   ```jsx
   // Composant de connexion avec limitation des tentatives
   function LoginForm() {
     const [attempts, setAttempts] = useState(0);
     const [locked, setLocked] = useState(false);
     const [lockTime, setLockTime] = useState(null);
     
     const handleLogin = async (credentials) => {
       // Vérifier si le compte est verrouillé
       if (locked) {
         const timeRemaining = calculateTimeRemaining(lockTime);
         setError(`Compte verrouillé. Réessayez dans ${timeRemaining} minutes.`);
         return;
       }
       
       try {
         await authService.login(credentials);
         // Réinitialiser les tentatives en cas de succès
         setAttempts(0);
       } catch (error) {
         // Incrémenter le compteur de tentatives
         const newAttempts = attempts + 1;
         setAttempts(newAttempts);
         
         // Verrouiller le compte après 5 tentatives
         if (newAttempts >= 5) {
           setLocked(true);
           setLockTime(new Date(Date.now() + 15 * 60 * 1000)); // 15 minutes
           setError('Compte verrouillé. Réessayez dans 15 minutes.');
         }
       }
     };
     
     // Reste du composant...
   }
   ```

## Gestion sécurisée des données sensibles

### Bonnes pratiques :

1. **Ne jamais exposer de données sensibles dans le code client** :
   ```jsx
   // ❌ Dangereux - exposer des secrets dans le code client
   const API_KEY = 'your-secret-api-key';
   
   // ✅ Sécurisé - utiliser des variables d'environnement côté serveur
   // Ou pour les clés publiques dans une SPA, utiliser des fichiers .env avec préfixe VITE_
   const API_KEY = import.meta.env.VITE_PUBLIC_API_KEY;
   ```

2. **Masquer les données sensibles dans l'UI** :
   ```jsx
   // ✅ Masquer les données sensibles
   function PaymentForm() {
     const [cardNumber, setCardNumber] = useState('');
     
     // Pour l'affichage, ne montrer que les derniers chiffres
     const maskedCardNumber = cardNumber.length > 4 
       ? `**** **** **** ${cardNumber.slice(-4)}`
       : cardNumber;
     
     return (
       <div>
         <label>Numéro de carte</label>
         <input
           type="text"
           value={cardNumber}
           onChange={(e) => setCardNumber(e.target.value)}
           // Ne pas stocker en autocomplete
           autoComplete="off"
         />
         <div>Carte: {maskedCardNumber}</div>
       </div>
     );
   }
   ```

3. **Nettoyage des données sensibles après utilisation** :
   ```jsx
   function CheckoutPage() {
     // Après traitement du paiement
     const handlePaymentComplete = () => {
       // Nettoyer les données sensibles
       setCardNumber('');
       setCvv('');
       
       // Rediriger vers la page de confirmation
       navigate('/confirmation');
     };
   }
   ```

## Sécurité des API et des requêtes HTTP

### Bonnes pratiques :

1. **Validation et assainissement des paramètres d'URL** :
   ```jsx
   // ✅ Encoder les paramètres de requête
   const searchParams = new URLSearchParams();
   searchParams.append('query', userInput);
   
   // L'URL sera correctement encodée
   const url = `/api/search?${searchParams.toString()}`;
   ```

2. **Protection CSRF pour les requêtes modifiant l'état** :
   ```jsx
   // Service API avec protection CSRF
   class ApiService {
     constructor() {
       this.axios = axios.create({
         baseURL: '/api'
       });
       
       // Intercepteur pour ajouter le token CSRF à toutes les requêtes
       this.axios.interceptors.request.use(config => {
         // Ajouter le token CSRF pour les requêtes qui modifient l'état
         if (['post', 'put', 'delete', 'patch'].includes(config.method)) {
           const csrfToken = getCsrfToken(); // Récupérer le token depuis un cookie ou localStorage
           config.headers['X-CSRF-Token'] = csrfToken;
         }
         return config;
       });
     }
     
     // Méthodes API...
   }
   ```

3. **Validation des réponses API** :
   ```jsx
   // Valider les données reçues de l'API
   const fetchUserData = async (userId) => {
     try {
       const response = await api.get(`/users/${userId}`);
       
       // Valider la structure des données reçues
       const schema = Yup.object().shape({
         id: Yup.number().required(),
         name: Yup.string().required(),
         email: Yup.string().email().required(),
         role: Yup.string().oneOf(['user', 'admin']).required()
       });
       
       // Valider avant utilisation
       try {
         await schema.validate(response.data);
         return response.data;
       } catch (validationError) {
         console.error('Invalid API response format:', validationError);
         throw new Error('Invalid response format');
       }
     } catch (error) {
       handleApiError(error);
     }
   };
   ```

## Téléchargement sécurisé de fichiers

### Bonnes pratiques :

1. **Validation des types de fichiers** :
   ```jsx
   function FileUpload({ allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'] }) {
     const validateFile = (file) => {
       // Vérifier le type MIME
       if (!allowedTypes.includes(file.type)) {
         return {
           valid: false,
           error: `Type de fichier non autorisé. Autorisés: ${allowedTypes.join(', ')}`
         };
       }
       
       // Vérifier l'extension
       const extension = file.name.split('.').pop().toLowerCase();
       const expectedExtension = getExtensionForMimeType(file.type);
       if (extension !== expectedExtension) {
         return {
           valid: false,
           error: 'Extension de fichier incohérente avec le type MIME'
         };
       }
       
       return { valid: true };
     };
     
     const handleFileChange = (e) => {
       const file = e.target.files[0];
       const validation = validateFile(file);
       
       if (!validation.valid) {
         setError(validation.error);
         return;
       }
       
       // Continuer le processus de téléchargement...
     };
     
     // Reste du composant...
   }
   ```

2. **Limitation de la taille des fichiers** :
   ```jsx
   function FileUpload({ maxSizeInBytes = 5 * 1024 * 1024 }) { // 5MB
     const validateFileSize = (file) => {
       if (file.size > maxSizeInBytes) {
         return {
           valid: false,
           error: `Fichier trop volumineux. Maximum: ${formatFileSize(maxSizeInBytes)}`
         };
       }
       return { valid: true };
     };
     
     const formatFileSize = (bytes) => {
       if (bytes < 1024) return `${bytes} B`;
       if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
       return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
     };
     
     // Logique de validation...
   }
   ```

3. **Sanitisation des noms de fichiers** :
   ```jsx
   function sanitizeFileName(fileName) {
     // Supprimer les caractères spéciaux et les chemins relatifs
     return fileName
       .replace(/[\\/:*?"<>|]/g, '') // Supprimer les caractères interdits
       .replace(/\.\./g, '')         // Supprimer les tentatives de directory traversal
       .replace(/^\s+|\s+$/g, '')    // Supprimer les espaces en début et fin
       .replace(/\s+/g, '_');        // Remplacer les espaces par des underscores
   }
   
   function FileUpload() {
     const handleFileSubmit = async (file) => {
       const sanitizedName = sanitizeFileName(file.name);
       
       // Créer un nouvel objet File avec le nom sanitisé
       const sanitizedFile = new File([file], sanitizedName, { type: file.type });
       
       // Uploader le fichier sanitisé...
     };
   }
   ```

## Gestion d'état sécurisée

### Bonnes pratiques :

1. **Ne jamais stocker de données sensibles dans l'état global** :
   ```jsx
   // ❌ Dangereux - stocker des identifiants dans Redux
   const loginSlice = createSlice({
     name: 'auth',
     initialState: {
       user: null,
       credentials: null, // Stockera email et mot de passe
     },
     reducers: {
       loginStart: (state, action) => {
         state.credentials = action.payload; // ❌ Ne jamais stocker les mots de passe!
       }
     }
   });
   
   // ✅ Sécurisé - Ne pas stocker les identifiants
   const secureLoginSlice = createSlice({
     name: 'auth',
     initialState: {
       user: null,
       loading: false,
       error: null
     },
     reducers: {
       loginStart: (state) => {
         state.loading = true;
         state.error = null;
       },
       loginSuccess: (state, action) => {
         state.user = action.payload;
         state.loading = false;
       }
     }
   });
   ```

2. **Nettoyer l'état lors de la déconnexion** :
   ```jsx
   const authSlice = createSlice({
     name: 'auth',
     initialState,
     reducers: {
       // Autres reducers...
       
       logout: () => {
         // Retourner entièrement l'état initial pour nettoyer toutes les données
         return initialState;
       }
     }
   });
   ```

3. **Utiliser un middleware de validation pour l'état** :
   ```jsx
   // Middleware Redux pour valider les actions et l'état
   const validateStateMiddleware = store => next => action => {
     // Laisser passer l'action
     const result = next(action);
     
     // Vérifier l'état après modification
     const state = store.getState();
     
     // Vérifier des règles spécifiques, par exemple:
     // 1. Pas de mot de passe dans l'état
     if (state.auth?.credentials?.password) {
       console.error('Security issue: Password found in state');
       // Dispatch d'une action pour nettoyer
       store.dispatch(cleanSensitiveData());
     }
     
     return result;
   };
   
   // Configurer le store avec le middleware
   const store = configureStore({
     reducer: rootReducer,
     middleware: (getDefaultMiddleware) => 
       getDefaultMiddleware().concat(validateStateMiddleware)
   });
   ```

## En-têtes de sécurité HTTP

Ces en-têtes sont généralement configurés côté serveur, mais il est important de comprendre leur rôle :

1. **Content-Security-Policy (CSP)** - Empêche l'exécution de scripts non autorisés :
   ```
   Content-Security-Policy: default-src 'self'; script-src 'self' https://trusted-domain.com;
   ```

2. **X-XSS-Protection** - Protection supplémentaire contre XSS :
   ```
   X-XSS-Protection: 1; mode=block
   ```

3. **X-Content-Type-Options** - Empêche le MIME sniffing :
   ```
   X-Content-Type-Options: nosniff
   ```

4. **X-Frame-Options** - Empêche le clickjacking :
   ```
   X-Frame-Options: DENY
   ```

5. **Strict-Transport-Security (HSTS)** - Force HTTPS :
   ```
   Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
   ```

6. **Referrer-Policy** - Contrôle les informations de référence :
   ```
   Referrer-Policy: strict-origin-when-cross-origin
   ```

## Considérations pour le déploiement

1. **Implémentation de la CI/CD sécurisée** :
   - Scans de sécurité automatisés (SAST, DAST, SCA)
   - Vérification des dépendances vulnérables

2. **Configuration HTTPS** :
   - Redirection HTTP vers HTTPS
   - Configuration des certificats TLS

3. **Protection des informations sensibles en production** :
   - Utilisation des variables d'environnement
   - Pas de secrets dans le code déployé
   - Utilisation de services de gestion de secrets (comme AWS Secrets Manager)

---

Ce guide couvre les bases de la sécurité dans les applications React. Pour plus d'informations, consultez notre [Guide des Tests de Sécurité](./SECURITY_TESTING_GUIDE.md) et les ressources OWASP. 