# API Configuration
VITE_API_URL=http://localhost:3000/api
VITE_WS_URL=ws://localhost:3000

# Messaging Service
VITE_MESSAGING_SERVICE_URL=http://localhost:3001
VITE_MESSAGING_WS_URL=http://localhost:3001

# Feature Flags
VITE_USE_MOCK_DATA=true
VITE_ENABLE_LIVESTREAM=true
VITE_ENABLE_NFT=true
VITE_ENABLE_SOCIAL=true
VITE_ENABLE_AI=true

# Web3 Configuration
VITE_IPFS_GATEWAY=https://ipfs.io/ipfs/
VITE_CHAIN_ID=1
VITE_CONTRACT_ADDRESS=your_contract_address
VITE_WALLETCONNECT_PROJECT_ID=your_project_id

# Analytics and Monitoring
VITE_SENTRY_DSN=your_sentry_dsn
VITE_GA_MEASUREMENT_ID=your_ga_id