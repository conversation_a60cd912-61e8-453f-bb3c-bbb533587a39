apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Release.Name }}-nginx-cache
  labels:
    {{- include "frontend.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.persistence.size | default "1Gi" }}
  storageClassName: {{ .Values.persistence.storageClass | default "standard" }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-nginx-config
  labels:
    {{- include "frontend.labels" . | nindent 4 }}
data:
  nginx.conf: |
    worker_processes auto;
    worker_rlimit_nofile 65535;
    
    events {
      worker_connections 65535;
      multi_accept on;
    }
    
    http {
      charset utf-8;
      sendfile on;
      tcp_nopush on;
      tcp_nodelay on;
      server_tokens off;
      log_not_found off;
      types_hash_max_size 2048;
      client_max_body_size 16M;
      
      # MIME
      include mime.types;
      default_type application/octet-stream;
      
      # Logging
      access_log /var/log/nginx/access.log combined buffer=512k flush=1m;
      error_log /var/log/nginx/error.log warn;
      
      # SSL
      ssl_session_timeout 1d;
      ssl_session_cache shared:SSL:50m;
      ssl_session_tickets off;
      
      # Modern configuration
      ssl_protocols TLSv1.2 TLSv1.3;
      ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
      
      # OCSP Stapling
      ssl_stapling on;
      ssl_stapling_verify on;
      resolver ******* ******* valid=60s;
      resolver_timeout 2s;
      
      # Load configs
      include /etc/nginx/conf.d/*.conf;
    }