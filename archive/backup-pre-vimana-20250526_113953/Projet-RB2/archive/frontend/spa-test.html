<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test SPA Routing</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #1976d2;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }
    .routes {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 5px;
      margin: 20px 0;
    }
    .routes a {
      display: block;
      margin: 10px 0;
      color: #1976d2;
      text-decoration: none;
    }
    .routes a:hover {
      text-decoration: underline;
    }
    .test-button {
      background-color: #1976d2;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    .error {
      background-color: #ffebee;
      color: #c62828;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>Test de Routage SPA</h1>
  
  <div class="routes">
    <h2>Routes à tester</h2>
    <a href="/test">/test</a>
    <a href="/login">/login</a>
    <a href="/dashboard">/dashboard</a>
    <a href="/this-route-does-not-exist">/this-route-does-not-exist</a>
  </div>
  
  <div>
    <h2>Actions</h2>
    <button class="test-button" onclick="testRoute('/test')">Tester /test</button>
    <button class="test-button" onclick="testRoute('/login')">Tester /login</button>
    <button class="test-button" onclick="testRoute('/dashboard')">Tester /dashboard</button>
    <button class="test-button" onclick="testRoute('/invalid-route')">Tester route invalide</button>
  </div>
  
  <div id="results">
    <h2>Résultats</h2>
    <div id="result-content">
      Cliquez sur une route pour voir les résultats
    </div>
  </div>
  
  <script>
    async function testRoute(route) {
      const resultElement = document.getElementById('result-content');
      resultElement.innerHTML = `<p>Test de la route: ${route}</p><p>Chargement...</p>`;
      
      try {
        const response = await fetch(route);
        const text = await response.text();
        const status = response.status;
        
        resultElement.innerHTML = `
          <p>Test de la route: ${route}</p>
          <p>Statut: ${status}</p>
          <p>Contenu reçu (premiers 200 caractères):</p>
          <pre>${text.substring(0, 200)}...</pre>
        `;
        
        // Vérifier si la réponse contient du contenu SPA valide
        if (text.includes('<div id="root"></div>') || text.includes("Page de test fonctionnelle")) {
          resultElement.innerHTML += `<p style="color: green">✅ La route SPA fonctionne correctement!</p>`;
        } else {
          resultElement.innerHTML += `<p class="error">❌ La route ne retourne pas le contenu SPA attendu.</p>`;
        }
      } catch (error) {
        resultElement.innerHTML = `
          <p>Test de la route: ${route}</p>
          <div class="error">
            <p>Erreur lors du test:</p>
            <pre>${error.message}</pre>
          </div>
        `;
      }
    }
  </script>
</body>
</html> 