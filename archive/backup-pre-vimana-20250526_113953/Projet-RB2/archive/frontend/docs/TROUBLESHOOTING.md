# Guide de résolution des problèmes courants

Ce guide présente les solutions aux problèmes courants que vous pourriez rencontrer lors du développement du projet Retreat And Be.

## 1. Problèmes d'installation

### npm install échoue avec des erreurs de dépendances

**Problème** : L'installation des dépendances échoue avec des erreurs de conflit de versions.

**Solution** :
1. Supprimer le dossier `node_modules` et le fichier `package-lock.json` :
```bash
rm -rf node_modules
rm package-lock.json
```

2. Vider le cache npm :
```bash
npm cache clean --force
```

3. Réinstaller les dépendances :
```bash
npm install
```

### Erreur "Module not found" après installation

**Problème** : Après l'installation, vous obtenez des erreurs "Module not found" lors du démarrage de l'application.

**Solution** :
1. Vérifier que toutes les dépendances sont correctement installées :
```bash
npm ls
```

2. R<PERSON><PERSON><PERSON><PERSON> les dépendances manquantes :
```bash
npm install <nom-du-module-manquant>
```

3. Si le problème persiste, essayer de réinstaller toutes les dépendances :
```bash
rm -rf node_modules
rm package-lock.json
npm install
```

## 2. Problèmes de développement

### Hot Reload ne fonctionne pas

**Problème** : Les modifications de code ne sont pas automatiquement reflétées dans le navigateur.

**Solution** :
1. Vérifier que le serveur de développement est en cours d'exécution :
```bash
npm run dev
```

2. Vérifier que vous n'avez pas désactivé le hot reload dans la configuration Vite :
```bash
# vite.config.ts
export default defineConfig({
  server: {
    hmr: true, // Assurez-vous que cette option est activée
  },
});
```

3. Essayer de redémarrer le serveur de développement :
```bash
npm run dev
```

### Erreurs TypeScript

**Problème** : Vous obtenez des erreurs TypeScript lors de la compilation.

**Solution** :
1. Vérifier les erreurs TypeScript dans le terminal ou dans l'IDE.

2. Corriger les erreurs de type en ajoutant les types manquants ou en corrigeant les types incorrects.

3. Si vous utilisez une bibliothèque sans types, ajouter les types manuellement ou installer les types depuis DefinitelyTyped :
```bash
npm install --save-dev @types/<nom-de-la-bibliothèque>
```

4. Si vous ne pouvez pas résoudre l'erreur immédiatement, vous pouvez utiliser `// @ts-ignore` ou `// @ts-expect-error` pour ignorer temporairement l'erreur, mais essayez d'éviter cette pratique.

### Problèmes de style

**Problème** : Les styles ne sont pas appliqués correctement.

**Solution** :
1. Vérifier que les fichiers CSS sont correctement importés dans les composants.

2. Vérifier que les classes CSS sont correctement appliquées aux éléments.

3. Vérifier qu'il n'y a pas de conflit de noms de classe.

4. Utiliser les outils de développement du navigateur pour inspecter les styles appliqués.

5. Si vous utilisez CSS Modules, vérifier que vous importez les styles correctement :
```tsx
import styles from './ComponentName.module.css';

// Utilisation
<div className={styles.container}>...</div>
```

## 3. Problèmes de test

### Les tests échouent

**Problème** : Les tests échouent lors de l'exécution.

**Solution** :
1. Vérifier les erreurs dans la sortie des tests.

2. Vérifier que les composants testés sont correctement importés.

3. Vérifier que les mocks sont correctement configurés.

4. Vérifier que les assertions sont correctes.

5. Exécuter les tests en mode watch pour voir les changements en temps réel :
```bash
npm run test:watch
```

### Problèmes avec Jest

**Problème** : Jest ne trouve pas les tests ou ne les exécute pas correctement.

**Solution** :
1. Vérifier la configuration Jest dans `jest.config.js`.

2. Vérifier que les fichiers de test suivent la convention de nommage (*.test.tsx, *.spec.tsx).

3. Vérifier que les tests sont dans les répertoires corrects.

4. Essayer de nettoyer le cache Jest :
```bash
npx jest --clearCache
```

5. Exécuter Jest avec l'option `--verbose` pour obtenir plus d'informations :
```bash
npx jest --verbose
```

## 4. Problèmes de build

### La build échoue

**Problème** : La build échoue avec des erreurs.

**Solution** :
1. Vérifier les erreurs dans la sortie de la build.

2. Vérifier que toutes les dépendances sont correctement installées.

3. Vérifier qu'il n'y a pas d'erreurs TypeScript.

4. Essayer de nettoyer le cache et de reconstruire :
```bash
npm run clean
npm run build
```

### La build est lente

**Problème** : La build prend beaucoup de temps.

**Solution** :
1. Utiliser le mode production uniquement pour les builds finales :
```bash
npm run build:dev # Pour le développement
npm run build # Pour la production
```

2. Utiliser le code splitting pour réduire la taille du bundle :
```tsx
const MyComponent = React.lazy(() => import('./MyComponent'));
```

3. Optimiser les imports pour éviter d'importer des bibliothèques entières :
```tsx
// Mauvais
import * as MaterialUI from '@mui/material';

// Bon
import { Button, Typography } from '@mui/material';
```

## 5. Problèmes de déploiement

### Le déploiement échoue

**Problème** : Le déploiement échoue avec des erreurs.

**Solution** :
1. Vérifier les erreurs dans la sortie du déploiement.

2. Vérifier que la build est réussie localement :
```bash
npm run build
```

3. Vérifier que les variables d'environnement sont correctement configurées.

4. Vérifier que les services dépendants (backend, microservices) sont accessibles.

5. Essayer de déployer en mode debug pour obtenir plus d'informations :
```bash
npm run deploy:staging -- --debug
```

### L'application ne fonctionne pas après le déploiement

**Problème** : L'application est déployée mais ne fonctionne pas correctement.

**Solution** :
1. Vérifier les logs du serveur pour identifier les erreurs.

2. Vérifier que les variables d'environnement sont correctement configurées.

3. Vérifier que les services dépendants (backend, microservices) sont accessibles.

4. Vérifier que les fichiers statiques sont correctement servis.

5. Vérifier que les routes sont correctement configurées.

## 6. Problèmes d'intégration avec les microservices

### Les appels API échouent

**Problème** : Les appels API vers les microservices échouent.

**Solution** :
1. Vérifier que les microservices sont en cours d'exécution.

2. Vérifier que les URLs des microservices sont correctement configurées.

3. Vérifier que les tokens d'authentification sont correctement envoyés.

4. Vérifier les logs des microservices pour identifier les erreurs.

5. Utiliser les outils de développement du navigateur pour inspecter les requêtes et les réponses.

### Problèmes CORS

**Problème** : Vous obtenez des erreurs CORS lors des appels API.

**Solution** :
1. Vérifier que les microservices sont configurés pour accepter les requêtes de votre domaine.

2. Vérifier que les en-têtes CORS sont correctement configurés dans les microservices.

3. Utiliser un proxy de développement pour contourner les problèmes CORS en développement :
```js
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
    },
  },
});
```

## 7. Problèmes spécifiques à l'intégration d'Audrey-V1

### Les composants d'Audrey-V1 ne s'affichent pas correctement

**Problème** : Les composants migrés d'Audrey-V1 ne s'affichent pas correctement.

**Solution** :
1. Vérifier que les styles d'Audrey-V1 sont correctement importés :
```tsx
import '../styles/audrey-styles.css';
```

2. Vérifier que les classes CSS sont correctement préfixées avec `audrey-v1-`.

3. Vérifier que les composants sont correctement importés :
```tsx
import Button from '../components/randbefrontend/atoms/Button/Button';
```

4. Vérifier que les props sont correctement passées aux composants.

### Les routes d'Audrey-V1 ne fonctionnent pas

**Problème** : Les routes migrées d'Audrey-V1 ne fonctionnent pas.

**Solution** :
1. Vérifier que les routes sont correctement configurées dans `audreyRoutes.tsx`.

2. Vérifier que les routes sont correctement importées dans `combinedRoutes.tsx`.

3. Vérifier que les composants de page sont correctement importés.

4. Vérifier que le routeur est correctement configuré dans `App.tsx`.

## 8. Ressources

- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Vite Documentation](https://vitejs.dev/guide/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Material UI Documentation](https://mui.com/material-ui/getting-started/overview/)

## 9. Contact

Si vous ne parvenez pas à résoudre un problème, n'hésitez pas à contacter :

- **Équipe de développement** : <EMAIL>
- **Lead Developer** : <EMAIL>
- **Support technique** : <EMAIL>
