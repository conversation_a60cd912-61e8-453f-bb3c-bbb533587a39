# Guide de démarrage rapide

Ce guide vous aidera à démarrer rapidement avec le projet Retreat And Be après l'intégration de Front-Audrey-V1-Main-main.

## Prérequis

- Node.js 16.x ou supérieur
- npm 8.x ou supérieur
- Git

## Installation

1. <PERSON><PERSON><PERSON> le dépôt :

```bash
git clone https://github.com/retreat-and-be/projet-rb2.git
cd projet-rb2
```

2. Installer les dépendances :

```bash
cd frontend
npm install
```

3. Lancer l'application en mode développement :

```bash
npm run dev
```

L'application sera accessible à l'adresse [http://localhost:5173](http://localhost:5173).

## Structure du projet

L'intégration de Front-Audrey-V1-Main-main suit la structure suivante :

```
frontend/
├── src/
│   ├── components/
│   │   └── randbefrontend/       # Composants migrés d'Audrey-V1
│   │       ├── atoms/            # Composants atomiques
│   │       ├── molecules/        # Composants moléculaires
│   │       ├── organisms/        # Composants organismes
│   │       ├── templates/        # Templates
│   │       └── ui/               # Composants UI génériques
│   ├── pages/
│   │   └── randbefrontend/       # Pages migrées d'Audrey-V1
│   ├── styles/
│   │   ├── audrey-styles.css     # Styles spécifiques à Audrey-V1
│   │   └── audrey-integration.css # Adaptations pour l'intégration
│   ├── config/
│   │   └── audrey-integration.ts # Configuration pour l'intégration
│   └── routes/
│       ├── audreyRoutes.tsx      # Routes d'Audrey-V1
│       └── combinedRoutes.tsx    # Combinaison des routes
└── scripts/
    └── audrey-migration.js       # Script de migration
```

## Commandes principales

- `npm run dev` : Lancer l'application en mode développement
- `npm run build` : Construire l'application pour la production
- `npm run preview` : Prévisualiser l'application construite
- `npm run lint` : Vérifier le code avec ESLint
- `npm run test` : Exécuter les tests
- `npm run test:audrey` : Exécuter les tests spécifiques à l'intégration d'Audrey-V1
- `npm run migrate:audrey` : Exécuter le script de migration d'Audrey-V1
- `npm run deploy:staging` : Déployer l'application dans l'environnement de staging
- `npm run deploy:prod` : Déployer l'application dans l'environnement de production

## Utilisation des composants

### Composants atomiques

Les composants atomiques sont les briques de base de l'interface utilisateur. Ils sont simples et réutilisables.

```tsx
import Button from '../components/randbefrontend/atoms/Button/Button';

function MyComponent() {
  return (
    <Button onClick={() => console.log('Clicked!')} variant="primary">
      Click me
    </Button>
  );
}
```

### Composants moléculaires

Les composants moléculaires sont composés de plusieurs composants atomiques. Ils représentent des éléments d'interface plus complexes.

```tsx
import RetreatCard from '../components/randbefrontend/molecules/RetreatCard/RetreatCard';

function MyComponent() {
  const retreat = {
    id: '1',
    title: 'Retreat Title',
    location: 'Retreat Location',
    price: 100,
    rating: 4.5,
    image: 'retreat-image.jpg',
  };

  return <RetreatCard retreat={retreat} />;
}
```

### Composants organismes

Les composants organismes sont composés de plusieurs composants moléculaires. Ils représentent des sections complètes de l'interface utilisateur.

```tsx
import NavBarClient from '../components/randbefrontend/organisms/NavBarClient/NavBarClient';

function MyComponent() {
  return <NavBarClient />;
}
```

### Templates

Les templates sont des composants qui définissent la structure globale d'une page.

```tsx
import MainLayout from '../components/randbefrontend/templates/MainLayout/MainLayout';

function MyComponent() {
  return (
    <MainLayout>
      <h1>Page Content</h1>
      <p>This is the content of the page.</p>
    </MainLayout>
  );
}
```

## Intégration avec le Backend-NestJS

Les services API sont configurés dans `config/audrey-integration.ts`.

```tsx
import { API_CONFIG } from '../config/audrey-integration';

async function fetchRetreats() {
  const response = await fetch(API_CONFIG.endpoints.retreats);
  const data = await response.json();
  return data;
}
```

## Intégration avec les microservices

Le `MicroserviceConnector` est configuré dans `config/audrey-integration.ts`.

```tsx
import { MICROSERVICE_CONFIG } from '../config/audrey-integration';
import { MicroserviceConnector } from '../services/MicroserviceConnector';

async function authenticateUser(email, password) {
  const response = await MicroserviceConnector.post(
    MICROSERVICE_CONFIG.security.authEndpoint,
    { email, password }
  );
  return response.data;
}
```

## Tests

Les tests sont organisés en trois catégories :

- Tests unitaires : Tests des composants individuels
- Tests d'intégration : Tests des interactions entre les composants
- Tests end-to-end : Tests des parcours utilisateur complets

```bash
# Exécuter tous les tests
npm run test

# Exécuter les tests d'Audrey-V1
npm run test:audrey
```

## Déploiement

L'application peut être déployée dans différents environnements :

```bash
# Déployer dans l'environnement de staging
npm run deploy:staging

# Déployer dans l'environnement de production
npm run deploy:prod
```

## Documentation

Pour plus d'informations, consultez les documents suivants :

- [Documentation technique](./AUDREY-INTEGRATION.md)
- [Guide de déploiement](./DEPLOYMENT.md)
- [Guide de formation](./TRAINING.md)
- [Guide de contribution](./CONTRIBUTING.md)
- [Roadmap des futures fonctionnalités](./ROADMAP-FUTURE.md)

## Support

En cas de problème, contactez l'équipe de développement à l'adresse suivante : <EMAIL>
