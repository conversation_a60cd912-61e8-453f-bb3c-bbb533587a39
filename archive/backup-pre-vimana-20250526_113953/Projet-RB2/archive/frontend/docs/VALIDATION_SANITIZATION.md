# Guide de Validation et Sanitization des Données

## Introduction

Ce document décrit le système de validation et de sanitization des données implémenté dans notre application, qui utilise Zod pour la validation des schémas et DOMPurify pour la sanitization.

## Principes Fondamentaux

1. **Validation** : Vérifier que les données respectent un format attendu
2. **Sanitization** : Nettoyer les données pour éviter les injections XSS et autres attaques
3. **Typage** : Utiliser les types TypeScript dérivés des schémas Zod

## Architecture du Système

### Structure de Dossiers

```
frontend/
  ├── src/
  │   ├── schemas/            # Schémas de validation Zod
  │   ├── utils/              # Utilitaires de validation/sanitization
  │   ├── hooks/              # Hooks personnalisés (ex: useOffline)
  │   └── components/         # Composants utilisant les validations
  └── __tests__/
      └── schemas/            # Tests des schémas de validation
```

### Bibliothèques Utilisées

- **Zod** : Bibliothèque de validation de schémas avec inférence de types
- **DOMPurify** : Bibliothèque de sanitization HTML
- **localforage** : Stockage local pour les données offline

## Schémas de Validation

### Schémas de Base

Nous utilisons des schémas de base qui sont étendus pour des cas d'utilisation spécifiques :

```typescript
// Schéma de base pour toutes les données mises en cache
export const baseOfflineDataSchema = z.object({
  id: z.string().uuid().or(z.string().min(1)),
  timestamp: z.number().int().positive(),
  lastModified: z.number().int().positive(),
});
```

### Schémas Spécifiques

Des schémas spécifiques sont définis pour chaque type de données :

```typescript
// Exemple : Schéma pour les profils utilisateurs
export const userProfileSchema = baseOfflineDataSchema.extend({
  type: z.literal('userProfile'),
  data: z.object({
    firstName: z.string().min(2).max(50),
    lastName: z.string().min(2).max(50),
    email: z.string().email(),
    phone: z.string().regex(/^(\+\d{1,3})?\s?\(?\d{1,4}\)?[\s.-]?\d{1,4}[\s.-]?\d{1,9}$/).optional(),
    // ...
  }),
});
```

### Unions Discriminées

Nous utilisons des unions discriminées pour gérer différents types de données avec un seul schéma :

```typescript
export const offlineDataSchema = z.discriminatedUnion('type', [
  reservationSchema,
  userProfileSchema,
  appSettingsSchema,
]);
```

## Fonctions de Validation et Sanitization

### validateAndSanitize

Cette fonction centrale combine validation et sanitization :

```typescript
export const validateAndSanitize = <T extends z.ZodType>(
  schema: T,
  data: unknown
): { success: boolean; data?: z.infer<T>; errors?: z.ZodError } => {
  try {
    // Validation avec Zod
    const validatedData = schema.parse(data);
    
    // Sanitization des chaînes de caractères
    const sanitizedData = Object.entries(validatedData).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [key]: typeof value === 'string' ? DOMPurify.sanitize(value) : value,
      }),
      {}
    );
    
    return { success: true, data: sanitizedData as z.infer<T> };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error };
    }
    throw error;
  }
};
```

## Hook useOffline

Le hook `useOffline` intègre validation et sanitization pour les données stockées en local :

```typescript
const { 
  data: profile, 
  loading, 
  error, 
  syncing, 
  saveData, 
  syncData,
  clearData 
} = useOffline(`userProfile_${userId}`, {
  syncUrl: '/api/sync/profile',
  syncOnLoad: true,
  autoSync: true,
  syncInterval: 5 * 60 * 1000, // 5 minutes
  customValidation: userProfileSchema,
});
```

### Options du Hook

- `syncUrl` : URL pour la synchronisation avec le serveur
- `syncOnLoad` : Synchronisation au chargement
- `autoSync` : Synchronisation automatique
- `syncInterval` : Intervalle de synchronisation automatique
- `customValidation` : Schéma de validation personnalisé

## Utilisation dans les Formulaires

### Validation en Temps Réel

```typescript
// Validation en temps réel
try {
  const schema = userProfileSchema.shape.data.shape[name as keyof typeof userProfileSchema.shape.data.shape];
  if (schema) {
    schema.parse(value);
    // Si pas d'erreur, supprimer l'erreur existante
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[name];
      return newErrors;
    });
  }
} catch (err) {
  if (err instanceof z.ZodError) {
    setValidationErrors(prev => ({
      ...prev,
      [name]: err.errors[0].message
    }));
  }
}
```

### Validation Complète lors de la Soumission

```typescript
try {
  // Validation complète avant soumission
  userProfileSchema.shape.data.parse(formData);
  
  // Si valide, procéder à la sauvegarde
  // ...
} catch (err) {
  if (err instanceof z.ZodError) {
    // Afficher les erreurs
    const errorMap: Record<string, string> = {};
    err.errors.forEach(e => {
      const field = e.path[e.path.length - 1];
      errorMap[field.toString()] = e.message;
    });
    setValidationErrors(errorMap);
  }
}
```

## Best Practices

### 1. Validation des Entrées

- Toujours valider les données avant de les stocker ou de les envoyer au serveur
- Utiliser des schémas Zod pour définir la structure attendue
- Ajouter des contraintes spécifiques (min/max, regex, etc.)

### 2. Sanitization

- Toujours sanitizer les entrées utilisateur avant affichage ou stockage
- Utiliser DOMPurify pour nettoyer les chaînes contenant du HTML
- Appliquer la sanitization après la validation

### 3. Gestion des Erreurs

- Afficher des messages d'erreur clairs et précis
- Localiser les messages d'erreur (i18n)
- Enregistrer les erreurs de validation dans les logs pour analyse

### 4. Tests

- Tester tous les schémas avec des données valides et invalides
- Tester les cas limites (valeurs minimales/maximales)
- Vérifier que la sanitization n'altère pas les données valides

## Exemples d'Implémentation

### Exemple 1 : Validation Simple

```typescript
import { z } from 'zod';
import { validateAndSanitize } from '../utils/validation';

const emailSchema = z.string().email();

const validateEmail = (email: string) => {
  const result = validateAndSanitize(emailSchema, email);
  return result.success;
};
```

### Exemple 2 : Formulaire avec Validation

```typescript
import { userProfileSchema } from '../schemas/offlineDataSchema';

// Dans un composant React
const [formData, setFormData] = useState({...});
const [errors, setErrors] = useState({});

const handleSubmit = (e) => {
  e.preventDefault();
  
  try {
    userProfileSchema.shape.data.parse(formData);
    // Soumission du formulaire
  } catch (err) {
    if (err instanceof z.ZodError) {
      // Afficher les erreurs
    }
  }
};
```

## Conclusion

Ce système de validation et sanitization assure que toutes les données manipulées par l'application sont valides et sécurisées. Il combine la puissance de Zod pour la validation et le typage avec DOMPurify pour la sécurité, offrant une solution robuste pour la gestion des données.

## Ressources

- [Documentation Zod](https://github.com/colinhacks/zod)
- [Documentation DOMPurify](https://github.com/cure53/DOMPurify)
- [OWASP XSS Prevention Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html) 