# Guide de déploiement de Front-Audrey-V1-Main-main

Ce document décrit les processus de déploiement de l'application Front-Audrey-V1-Main-main intégrée au projet principal.

## Prérequis

- Node.js 16.x ou supérieur
- npm 8.x ou supérieur
- Accès aux environnements de déploiement (développement, staging, production)
- Accès aux services AWS (S3, CloudFront, Route53)
- Accès aux services Docker et Kubernetes

## Environnements de déploiement

L'application est déployée dans trois environnements distincts :

1. **Développement** : Utilisé pour les tests en cours de développement
2. **Staging** : Utilisé pour les tests avant la mise en production
3. **Production** : Environnement de production accessible aux utilisateurs

## Processus de build

### 1. Installation des dépendances

```bash
# Installer les dépendances
npm install
```

### 2. Vérification du code

```bash
# Linter
npm run lint

# Tests
npm run test
npm run test:audrey
```

### 3. Build de l'application

```bash
# Build pour le développement
npm run build:dev

# Build pour le staging
npm run build:staging

# Build pour la production
npm run build:prod
```

Les fichiers de build sont générés dans le répertoire `dist`.

## Déploiement manuel

### 1. Déploiement sur AWS S3

```bash
# Déploiement sur le bucket de développement
aws s3 sync dist/ s3://retreat-and-be-dev --delete

# Déploiement sur le bucket de staging
aws s3 sync dist/ s3://retreat-and-be-staging --delete

# Déploiement sur le bucket de production
aws s3 sync dist/ s3://retreat-and-be-prod --delete
```

### 2. Invalidation du cache CloudFront

```bash
# Invalidation du cache de développement
aws cloudfront create-invalidation --distribution-id DISTRIBUTION_ID_DEV --paths "/*"

# Invalidation du cache de staging
aws cloudfront create-invalidation --distribution-id DISTRIBUTION_ID_STAGING --paths "/*"

# Invalidation du cache de production
aws cloudfront create-invalidation --distribution-id DISTRIBUTION_ID_PROD --paths "/*"
```

## Déploiement avec Docker

### 1. Construction de l'image Docker

```bash
# Construction de l'image pour le développement
docker build -t retreat-and-be-frontend:dev -f Dockerfile.dev .

# Construction de l'image pour le staging
docker build -t retreat-and-be-frontend:staging -f Dockerfile.staging .

# Construction de l'image pour la production
docker build -t retreat-and-be-frontend:prod -f Dockerfile.prod .
```

### 2. Publication de l'image Docker

```bash
# Publication de l'image pour le développement
docker tag retreat-and-be-frontend:dev REGISTRY_URL/retreat-and-be-frontend:dev
docker push REGISTRY_URL/retreat-and-be-frontend:dev

# Publication de l'image pour le staging
docker tag retreat-and-be-frontend:staging REGISTRY_URL/retreat-and-be-frontend:staging
docker push REGISTRY_URL/retreat-and-be-frontend:staging

# Publication de l'image pour la production
docker tag retreat-and-be-frontend:prod REGISTRY_URL/retreat-and-be-frontend:prod
docker push REGISTRY_URL/retreat-and-be-frontend:prod
```

### 3. Déploiement sur Kubernetes

```bash
# Déploiement sur l'environnement de développement
kubectl apply -f kubernetes/frontend-dev.yaml

# Déploiement sur l'environnement de staging
kubectl apply -f kubernetes/frontend-staging.yaml

# Déploiement sur l'environnement de production
kubectl apply -f kubernetes/frontend-prod.yaml
```

## Déploiement automatisé avec CI/CD

Le déploiement automatisé est configuré avec GitHub Actions. Les workflows sont définis dans le répertoire `.github/workflows`.

### 1. Déploiement en développement

Le déploiement en développement est déclenché automatiquement à chaque push sur la branche `develop`.

### 2. Déploiement en staging

Le déploiement en staging est déclenché automatiquement à chaque push sur la branche `staging`.

### 3. Déploiement en production

Le déploiement en production est déclenché automatiquement à chaque push sur la branche `main`.

## Configuration des variables d'environnement

Les variables d'environnement sont configurées dans les fichiers suivants :

- `.env.development` : Variables d'environnement pour le développement
- `.env.staging` : Variables d'environnement pour le staging
- `.env.production` : Variables d'environnement pour la production

Ces fichiers ne sont pas versionnés. Des exemples sont fournis dans le fichier `.env.example`.

## Surveillance et monitoring

### 1. Logs

Les logs de l'application sont envoyés à CloudWatch Logs.

```bash
# Affichage des logs de développement
aws logs get-log-events --log-group-name /aws/lambda/retreat-and-be-frontend-dev

# Affichage des logs de staging
aws logs get-log-events --log-group-name /aws/lambda/retreat-and-be-frontend-staging

# Affichage des logs de production
aws logs get-log-events --log-group-name /aws/lambda/retreat-and-be-frontend-prod
```

### 2. Métriques

Les métriques de l'application sont envoyées à CloudWatch Metrics.

### 3. Alertes

Les alertes sont configurées dans CloudWatch Alarms.

## Rollback

En cas de problème après un déploiement, il est possible de revenir à la version précédente.

### 1. Rollback manuel

```bash
# Rollback vers la version précédente
aws s3 sync s3://retreat-and-be-backup/VERSION_ID/ s3://retreat-and-be-prod --delete
aws cloudfront create-invalidation --distribution-id DISTRIBUTION_ID_PROD --paths "/*"
```

### 2. Rollback avec Kubernetes

```bash
# Rollback vers la version précédente
kubectl rollout undo deployment/retreat-and-be-frontend
```

## Sauvegarde

Les fichiers de build sont sauvegardés dans un bucket S3 dédié avant chaque déploiement.

```bash
# Sauvegarde des fichiers de build
aws s3 sync dist/ s3://retreat-and-be-backup/$(date +%Y%m%d%H%M%S)/
```

## Contacts

En cas de problème lors du déploiement, contacter :

- **Équipe DevOps** : <EMAIL>
- **Équipe Frontend** : <EMAIL>
- **Responsable technique** : <EMAIL>
