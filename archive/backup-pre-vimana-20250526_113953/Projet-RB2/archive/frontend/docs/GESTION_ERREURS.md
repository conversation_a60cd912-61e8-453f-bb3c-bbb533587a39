# Système de Gestion des Erreurs

Ce document décrit le système centralisé de gestion des erreurs implémenté dans notre application. Ce système permet de capturer, normaliser, journaliser et afficher les erreurs de manière cohérente et conviviale pour les utilisateurs.

## Objectifs

- **Uniformiser la gestion des erreurs** à travers l'application
- **Améliorer l'expérience utilisateur** face aux erreurs
- **Faciliter le débogage** en fournissant des informations détaillées
- **Centraliser la journalisation** des erreurs
- **Intégrer avec notre système de validation** existant

## Architecture du système

Le système de gestion d'erreurs est constitué de plusieurs composants interconnectés :

1. **Service d'erreurs** : Normalise les erreurs et fournit des utilitaires
2. **Hooks React** : Intègrent la gestion d'erreurs aux composants fonctionnels
3. **Composants UI** : Affichent les erreurs de manière conviviale
4. **Error Boundary** : Capture les erreurs non gérées dans les composants React

### Diagramme de flux

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ Source Erreur │────▶│  ErrorService │────▶│  Logger       │
└───────────────┘     └───────┬───────┘     └───────────────┘
                             │
                             ▼
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ ErrorBoundary │◀────│   AppError    │────▶│ ErrorDisplay  │
└───────────────┘     └───────────────┘     └───────────────┘
                             ▲
                             │
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│ Composant     │────▶│useErrorHandler│────▶│useAxiosErrors │
└───────────────┘     └───────────────┘     └───────────────┘
```

## Composants principaux

### 1. Service d'erreurs (`ErrorService.ts`)

Classe centrale pour la normalisation et la gestion des erreurs.

**Fonctionnalités principales :**
- Définition des types d'erreurs (réseau, serveur, validation, etc.)
- Niveaux de sévérité (critique, élevé, moyen, faible)
- Création d'erreurs contextualisées
- Conversion d'erreurs brutes en erreurs normalisées (`AppError`)
- Journalisation automatique des erreurs

**Exemple d'utilisation :**

```typescript
import { handleError, ErrorType } from '../services/errors/ErrorService';

try {
  // Opération qui peut échouer
} catch (err) {
  const appError = handleError(err, "MonModule", {
    errorType: ErrorType.VALIDATION,
    userMessage: "Les données saisies sont invalides"
  });
  
  // Faire quelque chose avec l'erreur normalisée
}
```

### 2. Hooks de gestion d'erreurs

#### `useErrorHandler`

Hook générique pour gérer les erreurs dans les composants fonctionnels.

**Fonctionnalités :**
- Stockage de l'erreur courante dans l'état React
- Traitement et normalisation des erreurs
- Méthodes utilitaires (`clearError`, `safeAsync`, etc.)

**Exemple d'utilisation :**

```tsx
const { error, handleError, clearError } = useErrorHandler({
  context: 'UserProfileComponent'
});

// Utilisation dans un effet
useEffect(() => {
  const fetchData = async () => {
    try {
      // Opération asynchrone
    } catch (err) {
      handleError(err, { 
        userMessage: "Impossible de charger les données" 
      });
    }
  };
  
  fetchData();
}, []);

// Affichage de l'erreur
if (error) {
  return <ErrorDisplay error={error} onReset={clearError} />;
}
```

#### `useAxiosErrorHandler`

Hook spécialisé pour les erreurs d'API avec Axios.

**Fonctionnalités :**
- Conversion des codes HTTP en types d'erreurs
- Messages utilisateur adaptés par code d'erreur
- Wrapper pour fonctions asynchrones avec Axios

**Exemple d'utilisation :**

```tsx
const { error, handleAxiosError, withAxiosErrorHandling } = useAxiosErrorHandler({
  context: 'UserAPI'
});

// Méthode 1: Utilisation directe
const fetchUser = async (id) => {
  try {
    const response = await axios.get(`/api/users/${id}`);
    return response.data;
  } catch (err) {
    handleAxiosError(err);
    return null;
  }
};

// Méthode 2: Utilisation du wrapper
const fetchUser = withAxiosErrorHandling(async (id) => {
  const response = await axios.get(`/api/users/${id}`);
  return response.data;
});
```

### 3. Composants UI

#### `ErrorBoundary`

Composant React qui capture les erreurs non gérées dans ses enfants.

**Fonctionnalités :**
- Capture des erreurs d'exécution dans les composants React
- Conversion en `AppError` normalisée
- Support pour des fallbacks personnalisés

**Exemple d'utilisation :**

```tsx
<ErrorBoundary context="ProfilePage">
  <UserProfile userId={userId} />
</ErrorBoundary>

// Avec fallback personnalisé
<ErrorBoundary 
  context="PaymentForm"
  fallback={(error, reset) => (
    <div>
      <h3>Erreur de paiement</h3>
      <p>{error.userMessage}</p>
      <button onClick={reset}>Réessayer</button>
    </div>
  )}
>
  <PaymentForm />
</ErrorBoundary>
```

#### `ErrorDisplay`

Composant pour afficher les erreurs de manière conviviale aux utilisateurs.

**Fonctionnalités :**
- Affichage adapté selon le type et la sévérité de l'erreur
- Support pour les messages personnalisés
- Versions détaillée et compacte
- Suggestions d'actions pour l'utilisateur

**Exemple d'utilisation :**

```tsx
// Version détaillée
<ErrorDisplay error={appError} onReset={handleReset} />

// Version compacte
<ErrorDisplay 
  error={validationError} 
  variant="compact" 
  className="form-error" 
/>
```

## Intégration avec la validation Zod

Notre système d'erreurs s'intègre parfaitement avec le schéma de validation Zod :

```typescript
const userSchema = z.object({
  name: z.string().min(2),
  email: z.string().email()
});

try {
  const validationResult = userSchema.safeParse(formData);
  
  if (!validationResult.success) {
    throw handleError(new Error("Validation failed"), "UserForm", {
      errorType: ErrorType.VALIDATION,
      data: validationResult.error.format(),
      userMessage: "Veuillez corriger les erreurs dans le formulaire"
    });
  }
  
  // Continuer avec les données valides
} catch (err) {
  // L'erreur est déjà traitée
}
```

## Exemples d'utilisation

### Exemple 1 : Composant avec ErrorBoundary

Voir le fichier `UserProfileWithErrorHandling.tsx` pour un exemple complet de composant qui utilise :

- `ErrorBoundary` pour la capture des erreurs non gérées
- `useErrorHandler` pour gérer les erreurs de manière contrôlée
- Validation des données avec Zod
- Affichage des erreurs avec `ErrorDisplay`

### Exemple 2 : Gestion d'erreurs API

```tsx
const UserList = () => {
  const { error, withAxiosErrorHandling, clearError } = useAxiosErrorHandler({
    context: 'UserListComponent'
  });
  
  const [users, setUsers] = useState([]);
  
  const fetchUsers = withAxiosErrorHandling(async () => {
    const response = await axios.get('/api/users');
    const validUsers = z.array(userSchema).parse(response.data);
    setUsers(validUsers);
  });
  
  useEffect(() => {
    fetchUsers();
  }, []);
  
  if (error) {
    return (
      <ErrorDisplay 
        error={error} 
        onReset={() => {
          clearError();
          fetchUsers();
        }} 
      />
    );
  }
  
  return (
    <ul>
      {users.map(user => (
        <li key={user.id}>{user.name}</li>
      ))}
    </ul>
  );
};
```

## Bonnes pratiques

1. **Toujours spécifier un contexte** pour faciliter le débogage
2. **Utiliser les types d'erreurs appropriés** pour une classification cohérente
3. **Fournir des messages utilisateur clairs** pour améliorer l'expérience utilisateur
4. **Envelopper les composants critiques** dans des `ErrorBoundary`
5. **Combiner avec la validation Zod** pour une gestion cohérente des erreurs de validation
6. **Proposer des actions correctives** lorsque possible via `suggestedAction`

## Prochaines étapes

1. Intégration avec un service de monitoring externe (Sentry)
2. Ajout de rapports d'erreurs anonymisés
3. Extensions pour gérer les erreurs de formulaires complexes
4. Amélioration des messages d'erreur par défaut 