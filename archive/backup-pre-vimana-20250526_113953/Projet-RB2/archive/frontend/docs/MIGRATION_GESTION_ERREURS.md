# Guide de Migration vers le Système de Gestion d'Erreurs

Ce document fournit des instructions étape par étape pour migrer les composants existants vers notre nouveau système centralisé de gestion des erreurs.

## Objectifs de la migration

- Remplacer les différentes approches ad hoc de gestion d'erreur par notre système unifié
- Améliorer la cohérence de l'expérience utilisateur face aux erreurs
- Renforcer la sécurité en normalisant la façon dont les erreurs sont traitées
- Faciliter le suivi et le débogage des erreurs

## Étapes de migration

### 1. Identifier les différents types de composants à migrer

Commencez par identifier les types de composants qui nécessitent une migration :

- Composants React avec leur propre logique de gestion d'erreurs
- Pages ou écrans qui effectuent des appels API
- Formulaires qui gèrent des erreurs de validation
- Services ou hooks personnalisés qui peuvent générer des erreurs

### 2. Migration des composants React avec gestion d'erreurs interne

#### Avant la migration

```tsx
// Approche typique avant la migration
const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setError(null);
        const response = await fetch(`/api/users/${userId}`);
        
        if (!response.ok) {
          throw new Error(`Erreur: ${response.status}`);
        }
        
        const data = await response.json();
        setUser(data);
      } catch (err) {
        console.error("Erreur lors du chargement de l'utilisateur", err);
        setError("Impossible de charger les données de l'utilisateur");
      }
    };
    
    fetchUser();
  }, [userId]);
  
  if (error) {
    return <div className="error-message">{error}</div>;
  }
  
  // Rendu du composant...
};
```

#### Après la migration

```tsx
// Approche avec le nouveau système
import { useErrorHandler } from '../../hooks/useErrorHandler';
import { ErrorDisplay } from '../errors/ErrorDisplay';
import { ErrorType } from '../../services/errors/ErrorService';

const UserProfile = ({ userId }) => {
  const { error, handleError, clearError } = useErrorHandler({
    context: 'UserProfile'
  });
  
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    const fetchUser = async () => {
      try {
        clearError();
        const response = await fetch(`/api/users/${userId}`);
        
        if (!response.ok) {
          throw handleError(new Error(`Erreur HTTP: ${response.status}`), {
            errorType: response.status === 404 ? ErrorType.RESOURCE_NOT_FOUND 
                      : response.status >= 500 ? ErrorType.SERVER
                      : ErrorType.CLIENT,
            userMessage: "Impossible de charger les données de l'utilisateur",
            data: { statusCode: response.status }
          });
        }
        
        const data = await response.json();
        setUser(data);
      } catch (err) {
        // Si l'erreur a déjà été traitée par handleError plus haut,
        // cette ligne ne fera rien de plus
        handleError(err);
      }
    };
    
    fetchUser();
  }, [userId, handleError, clearError]);
  
  if (error) {
    return (
      <ErrorDisplay 
        error={error} 
        onReset={() => {
          clearError();
          fetchUser();
        }}
      />
    );
  }
  
  // Rendu du composant...
};

// Pour une meilleure protection, envelopper avec ErrorBoundary
import { ErrorBoundary } from '../errors/ErrorBoundary';

export const UserProfileWithErrorHandling = (props) => (
  <ErrorBoundary context="UserProfile">
    <UserProfile {...props} />
  </ErrorBoundary>
);
```

### 3. Migration des appels API avec Axios

#### Avant la migration

```tsx
const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [error, setError] = useState(null);
  
  const fetchProducts = async () => {
    try {
      setError(null);
      const response = await axios.get('/api/products');
      setProducts(response.data);
    } catch (err) {
      console.error("Erreur lors du chargement des produits", err);
      setError(
        err.response?.status === 403 
          ? "Vous n'avez pas accès à cette ressource" 
          : "Impossible de charger les produits"
      );
    }
  };
  
  // Utilisation et gestion des erreurs...
};
```

#### Après la migration

```tsx
import { useAxiosErrorHandler } from '../../hooks/useAxiosErrorHandler';

const ProductList = () => {
  const { 
    error, 
    withAxiosErrorHandling, 
    clearError 
  } = useAxiosErrorHandler({ 
    context: 'ProductList' 
  });
  
  const [products, setProducts] = useState([]);
  
  // Version 1: Utiliser le wrapper
  const fetchProducts = withAxiosErrorHandling(async () => {
    const response = await axios.get('/api/products');
    setProducts(response.data);
  });
  
  // Version 2: Utilisation manuelle plus détaillée
  const fetchProductsDetailed = async () => {
    try {
      clearError();
      const response = await axios.get('/api/products');
      setProducts(response.data);
    } catch (err) {
      // Le hook gère automatiquement la conversion des codes d'erreur HTTP
      // en types d'erreurs appropriés et messages utilisateur
      handleAxiosError(err);
    }
  };
  
  // Gestion de l'affichage des erreurs...
};
```

### 4. Migration des formulaires avec validation

#### Avant la migration

```tsx
const SignupForm = () => {
  const [formData, setFormData] = useState({ name: '', email: '', password: '' });
  const [errors, setErrors] = useState({});
  
  const validate = () => {
    const newErrors = {};
    
    if (!formData.name) newErrors.name = "Le nom est requis";
    if (!formData.email) newErrors.email = "L'email est requis";
    if (!formData.email.includes('@')) newErrors.email = "L'email est invalide";
    if (formData.password.length < 8) {
      newErrors.password = "Le mot de passe doit contenir au moins 8 caractères";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validate()) {
      return;
    }
    
    try {
      await api.signup(formData);
      // Gérer le succès...
    } catch (err) {
      console.error("Erreur d'inscription", err);
      setErrors({ form: "L'inscription a échoué. Veuillez réessayer." });
    }
  };
  
  // Rendu du formulaire avec la gestion des erreurs...
};
```

#### Après la migration

```tsx
import { z } from 'zod';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import { ErrorType } from '../../services/errors/ErrorService';
import { ErrorDisplay } from '../errors/ErrorDisplay';
import { ValidatedForm } from '../form/ValidatedForm';

// Définir le schéma de validation avec Zod
const signupSchema = z.object({
  name: z.string().min(1, "Le nom est requis"),
  email: z.string().min(1, "L'email est requis").email("L'email est invalide"),
  password: z.string().min(8, "Le mot de passe doit contenir au moins 8 caractères")
});

const SignupForm = () => {
  const { error, handleError, clearError } = useErrorHandler({
    context: 'SignupForm'
  });
  
  const handleSubmit = async (formData) => {
    try {
      clearError();
      await api.signup(formData);
      // Gérer le succès...
    } catch (err) {
      handleError(err, {
        errorType: ErrorType.BUSINESS_LOGIC,
        userMessage: "L'inscription a échoué. Veuillez réessayer."
      });
    }
  };
  
  const handleValidationError = (validationError) => {
    handleError(new Error("Validation failed"), {
      errorType: ErrorType.VALIDATION,
      userMessage: "Veuillez corriger les erreurs dans le formulaire",
      data: { zodErrors: validationError.format() }
    });
  };
  
  if (error && error.type !== ErrorType.VALIDATION) {
    return (
      <ErrorDisplay 
        error={error} 
        onReset={clearError}
        variant="compact"
      />
    );
  }
  
  return (
    <ValidatedForm
      schema={signupSchema}
      onSubmit={handleSubmit}
      onValidationError={handleValidationError}
    >
      {/* Rendu des champs du formulaire... */}
    </ValidatedForm>
  );
};
```

### 5. Gestion des erreurs asynchrones dans les hooks personnalisés

#### Avant la migration

```tsx
const useProductSearch = (initialQuery = '') => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [query, setQuery] = useState(initialQuery);
  
  useEffect(() => {
    const searchProducts = async () => {
      if (!query) {
        setProducts([]);
        return;
      }
      
      try {
        setLoading(true);
        setError(null);
        
        const response = await axios.get(`/api/products/search?q=${query}`);
        setProducts(response.data);
      } catch (err) {
        console.error("Erreur de recherche", err);
        setError("La recherche a échoué. Veuillez réessayer.");
      } finally {
        setLoading(false);
      }
    };
    
    searchProducts();
  }, [query]);
  
  return { products, loading, error, setQuery };
};
```

#### Après la migration

```tsx
import { useAxiosErrorHandler } from '../../hooks/useAxiosErrorHandler';

const useProductSearch = (initialQuery = '') => {
  const { 
    error, 
    clearError,
    safeAsync,
    handleAxiosError
  } = useAxiosErrorHandler({ 
    context: 'ProductSearch' 
  });
  
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState(initialQuery);
  
  // Fonction de recherche avec gestion d'erreurs
  const searchProducts = useCallback(async (searchQuery) => {
    if (!searchQuery) {
      setProducts([]);
      return;
    }
    
    try {
      setLoading(true);
      clearError();
      
      const response = await axios.get(`/api/products/search?q=${searchQuery}`);
      setProducts(response.data);
    } catch (err) {
      handleAxiosError(err);
    } finally {
      setLoading(false);
    }
  }, [clearError, handleAxiosError]);
  
  // Méthode alternative utilisant safeAsync
  const performSearch = useCallback(() => {
    return safeAsync(async () => {
      if (!query) {
        setProducts([]);
        return;
      }
      
      setLoading(true);
      try {
        const response = await axios.get(`/api/products/search?q=${query}`);
        setProducts(response.data);
        return response.data;
      } finally {
        setLoading(false);
      }
    });
  }, [query, safeAsync]);
  
  useEffect(() => {
    searchProducts(query);
    // Ou: performSearch();
  }, [query, searchProducts]);
  
  return { 
    products, 
    loading, 
    error, 
    setQuery,
    clearError,
    retry: () => searchProducts(query)
  };
};
```

## Recommandations pour la migration

1. **Migration progressive** : Migrez les composants un par un, en commençant par les plus critiques ou ceux avec le plus d'erreurs
2. **Tests unitaires** : Ajoutez des tests pour vérifier que les erreurs sont correctement capturées et traitées
3. **Documentation des erreurs** : Documentez les types d'erreurs spécifiques à chaque composant
4. **Revue de code** : Effectuez des revues de code pour s'assurer que la migration est correcte

## Checklist de migration

Pour chaque composant migré, vérifiez :

- [ ] Les erreurs sont capturées et traitées par notre système centralisé
- [ ] Les messages d'erreur sont adaptés aux utilisateurs finaux
- [ ] Les erreurs techniques sont masquées mais journalisées
- [ ] Les erreurs incluent des informations de contexte appropriées
- [ ] Le composant propose une action de récupération lorsque c'est possible
- [ ] Les composants qui peuvent générer des erreurs non gérées sont enveloppés dans un `ErrorBoundary`

## Support de migration

Pour toute question ou assistance concernant la migration vers le nouveau système de gestion d'erreurs, consultez :

- La documentation complète dans [`GESTION_ERREURS.md`](./GESTION_ERREURS.md)
- Les exemples de code dans `/frontend/src/components/examples`
- Contactez l'équipe de sécurité pour des cas spécifiques

## Prochaines étapes après la migration

1. **Audit des erreurs** : Analysez les erreurs captées pour identifier des modèles récurrents
2. **Amélioration des messages** : Affinez les messages d'erreur utilisateur en fonction des retours
3. **Extension du monitoring** : Configurez des alertes pour les erreurs critiques et récurrentes 