# Nouvelles Fonctionnalités de Sécurité

Ce document présente un aperçu des nouvelles fonctionnalités de sécurité implémentées dans l'application et comment les utiliser.

## 1. Formulaires Validés

### Description

Le composant `ValidatedForm` offre une solution complète pour la validation, la sanitization et la journalisation des formulaires. Il utilise Zod pour la validation et DOMPurify pour la sanitization.

### Utilisation

```tsx
import { ValidatedForm } from '../components/form/ValidatedForm';
import { z } from 'zod';

// Définir un schéma de validation
const loginSchema = z.object({
  email: z.string().email({ message: 'Email invalide' }),
  password: z.string().min(8, { message: 'Mot de passe trop court' })
});

// Utiliser le formulaire
function LoginForm() {
  return (
    <ValidatedForm
      schema={loginSchema}
      onSubmit={async (data) => {
        // Les données sont déjà validées et sanitizées
        await loginUser(data);
      }}
      loggingContext="LoginForm"
    >
      {({ values, setValue, getFieldError, isTouched, isSubmitting }) => (
        <>
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={values.email || ''}
              onChange={(e) => setValue('email', e.target.value)}
            />
            {getFieldError('email') && isTouched('email') && (
              <div className="error">{getFieldError('email')}</div>
            )}
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Mot de passe</label>
            <input
              type="password"
              id="password"
              value={values.password || ''}
              onChange={(e) => setValue('password', e.target.value)}
            />
            {getFieldError('password') && isTouched('password') && (
              <div className="error">{getFieldError('password')}</div>
            )}
          </div>
          
          <button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Connexion...' : 'Se connecter'}
          </button>
        </>
      )}
    </ValidatedForm>
  );
}
```

### Fonctionnalités

- Validation en temps réel
- Sanitization des entrées
- Journalisation sécurisée des erreurs
- Masquage des données sensibles
- Gestion d'état intégrée
- Types TypeScript générés automatiquement

## 2. Hook useOffline Amélioré

### Description

Le hook `useOffline` a été amélioré pour inclure la validation, la sanitization et la journalisation sécurisée des données stockées hors ligne.

### Utilisation

```tsx
import { useOffline } from '../hooks/useOffline';
import { userProfileSchema } from '../schemas/offlineDataSchema';

function ProfileComponent({ userId }) {
  const { 
    data: profile,
    loading,
    error,
    saveData,
    syncData
  } = useOffline(`userProfile_${userId}`, {
    syncUrl: '/api/sync/profile',
    customValidation: userProfileSchema,
    loggingContext: `ProfileComponent:${userId}`
  });
  
  // Les données sont automatiquement validées, sanitizées et journalisées
  
  return (
    <div>
      {loading && <p>Chargement...</p>}
      {error && <p>Erreur: {error.message}</p>}
      {profile && (
        <div>
          <h1>{profile.data.firstName} {profile.data.lastName}</h1>
          <button onClick={() => syncData()}>Synchroniser</button>
        </div>
      )}
    </div>
  );
}
```

### Fonctionnalités

- Validation des données avant stockage
- Sanitization des chaînes de caractères
- Vérification des données du serveur
- Journalisation détaillée
- Synchronisation sécurisée
- Support pour les schémas personnalisés

## 3. Système de Journalisation Sécurisé

### Description

Le système de journalisation fournit une solution complète pour journaliser les événements de l'application tout en protégeant les données sensibles.

### Utilisation

```tsx
import { logger, logValidationError } from '../services/logger/logger';

// Journalisation d'informations
logger.info('Utilisateur connecté', { userId: '123', role: 'admin' });

// Journalisation d'erreurs
try {
  // Code qui peut échouer
} catch (err) {
  logger.error('Erreur lors de l\'opération', err, { 
    operationId: '456',
    context: 'PagePaiement'
  });
}

// Journalisation d'erreurs de validation
try {
  const result = mySchema.parse(data);
} catch (err) {
  if (err instanceof z.ZodError) {
    logValidationError(err, 'FormulairePaiement', { 
      formId: 'payment-form'
    });
  }
}
```

### Fonctionnalités

- Masquage automatique des données sensibles
- Niveaux de journalisation configurables
- Intégration avec Sentry en production
- Contextualisation des logs
- Format standardisé des erreurs
- Support des objets imbriqués

## 4. Schémas de Validation des Données Offline

### Description

Des schémas de validation ont été définis pour toutes les données stockées en mode hors ligne, garantissant leur intégrité et leur sécurité.

### Utilisation

```tsx
import { 
  offlineDataSchema,
  reservationSchema, 
  userProfileSchema 
} from '../schemas/offlineDataSchema';

// Valider des données de réservation
const validationResult = reservationSchema.safeParse(myReservationData);
if (validationResult.success) {
  // Données valides
  const validData = validationResult.data;
} else {
  // Gestion des erreurs
  const errors = validationResult.error;
}

// Utiliser les types dérivés des schémas
import type { UserProfile, Reservation } from '../schemas/offlineDataSchema';

const myFunction = (profile: UserProfile) => {
  // TypeScript fournit un typage complet
};
```

### Fonctionnalités

- Schémas pour différents types de données
- Unions discriminées
- Types TypeScript générés automatiquement
- Validation stricte
- Messages d'erreur personnalisés

## 5. Intégration dans le Projet

Ces nouvelles fonctionnalités s'intègrent parfaitement dans le flux de travail existant. Pour utiliser ces outils dans votre code :

1. Pour les formulaires, remplacez les implémentations existantes par `ValidatedForm`
2. Pour le stockage hors ligne, utilisez le hook `useOffline` amélioré
3. Pour la journalisation, remplacez les appels à `console.log` par `logger.info`, etc.
4. Pour les validations ad hoc, utilisez les schémas Zod définis

## Exemples Concrets

### Formulaire de Profil Utilisateur

Voir le fichier `frontend/src/components/form/UserProfileForm.tsx` pour un exemple complet d'utilisation du système de formulaire validé.

### Composant Hors Ligne

Voir le fichier `frontend/src/components/offline/OfflineProfileEditor.tsx` pour un exemple de composant utilisant le hook `useOffline` amélioré.

## Prochaines Étapes

1. Migrer tous les formulaires existants vers `ValidatedForm`
2. Ajouter des schémas de validation pour toutes les entités de l'application
3. Intégrer la journalisation sécurisée dans tous les composants sensibles
4. Développer des tests pour valider le comportement des nouveaux composants 