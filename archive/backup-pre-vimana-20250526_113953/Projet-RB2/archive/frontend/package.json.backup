{"name": "retreat-and-be-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"start": "vite", "dev": "vite", "generate-sitemap": "ts-node scripts/generate-sitemap.ts", "build": "tsc && vite build", "lint": "eslint . --fix", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,scss}\"", "preview": "vite preview", "cypress:open": "cypress open", "cypress:run": "cypress run", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:a11y": "jest --testMatch=\"**/*.a11y.test.{ts,tsx}\"", "test:perf": "jest --testMatch=\"**/*.perf.test.{ts,tsx}\"", "test:load": "jest --testMatch=\"**/performance/Component*.perf.test.{ts,tsx}\"", "test:api": "jest --testMatch=\"**/*.msw.test.{ts,tsx}\"", "test:edge": "jest --testMatch=\"**/edge-cases/**/*.test.{ts,tsx}\"", "test:security": "jest --testMatch=\"**/security/**/*.test.{ts,tsx}\"", "security:scan": "ts-node scripts/run-security-scan.ts", "security:scan:ci": "CI=true ts-node scripts/run-security-scan.ts", "security:dast": "ts-node scripts/run-dast-scan.ts", "security:dast:baseline": "DAST_SCAN_TYPE=baseline ts-node scripts/run-dast-scan.ts", "security:dast:full": "DAST_SCAN_TYPE=full ts-node scripts/run-dast-scan.ts", "security:dast:api": "DAST_SCAN_TYPE=api ts-node scripts/run-dast-scan.ts", "test:all": "npm run test && npm run test:a11y && npm run test:perf && npm run test:api && npm run test:edge && npm run test:security", "test:ci": "npm run lint:ts && npm run test:all && npm run security:scan:ci", "lint:ts": "tsc --noEmit", "validate": "npm-run-all --parallel lint test build", "prepare": "husky install", "lint-staged": "lint-staged", "test:simple": "jest --config=jest.simple.config.js", "serve": "npx serve -s build -l 3000"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^4.1.3", "@mui/icons-material": "^5.15.6", "@mui/lab": "^5.0.0-alpha.160", "@mui/material": "^5.15.6", "@mui/x-date-pickers": "^6.19.2", "@reduxjs/toolkit": "^2.0.1", "@swc/jest": "^0.2.37", "@tanstack/react-query": "^5.17.19", "axios": "^1.6.7", "chart.js": "^4.4.8", "date-fns": "^3.3.1", "dompurify": "^3.2.4", "ethers": "^6.13.5", "formik": "^2.4.5", "jest-axe": "^9.0.0", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "node-html-parser": "^7.0.1", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-big-calendar": "^1.17.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.1", "react-icons": "^4.12.0", "react-leaflet": "^4.2.1", "react-redux": "^9.2.0", "react-router-dom": "^6.21.3", "react-toastify": "^10.0.4", "react-use-websocket": "^4.13.0", "redux-mock-store": "^1.5.5", "redux-persist": "^6.0.0", "uuid": "^9.0.1", "yup": "^1.3.3", "zustand": "^4.5.0"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@reduxjs/toolkit": "^2.5.1", "@sentry/react": "^9.1.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/cypress": "^1.1.3", "@types/cypress-axe": "^0.8.0", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.11", "@types/leaflet": "^1.9.8", "@types/node": "^20.17.24", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-lazy-load-image-component": "^1.6.4", "@types/redux-mock-store": "^1.5.0", "@types/testing-library__react-hooks": "^3.4.1", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "axios-mock-adapter": "^1.22.0", "babel-jest": "^29.7.0", "cypress": "^12.12.0", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-esm-transformer": "^1.0.0", "jest-watch-typeahead": "^2.2.2", "lighthouse": "^12.3.0", "lint-staged": "^15.2.1", "msw": "^2.0.11", "npm-run-all": "^4.1.5", "postcss": "^8.5.3", "prettier": "^3.2.4", "react-hook-form": "^7.54.2", "redux-thunk": "^3.1.0", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3", "typescript-eslint": "^8.24.1", "vite": "^5.4.14", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.21.1"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "moduleNameMapper": {"\\.(css|less|scss|sass)$": "identity-obj-proxy", "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/__mocks__/fileMock.js"}, "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx", "!src/serviceWorker.ts"]}}