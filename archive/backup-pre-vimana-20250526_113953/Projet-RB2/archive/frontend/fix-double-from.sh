#!/bin/bash

# Script pour corriger les erreurs d'importation avec double "from"

# Couleurs pour les messages
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
NC="\033[0m" # No Color

echo "${YELLOW}Correction des importations avec double 'from'...${NC}"

# Trouver tous les fichiers TypeScript et TSX
FILES=$(find ./src -type f \( -name "*.ts" -o -name "*.tsx" \))

# Pour chaque fichier
for FILE in $FILES; do
  # Recherche et correction des imports avec double "from"
  if grep -q "from .* from" "$FILE"; then
    echo "${GREEN}Correction du fichier: $FILE${NC}"
    
    # Correction des imports avec pattern "import { api } from 'path/to/file from 'path/to/file'"
    sed -i '' 's/import { \([^}]*\) } from '\''\([^'\'']*\) from '\''\([^'\'']*\)'\''/'\'import { \1 } from '\''\3'\''/g' "$FILE"
    
    # Correction des imports avec pattern "import { something } from './Something from './Something'"
    sed -i '' 's/import { \([^}]*\) } from '\''\([^'\'']*\) from '\''\([^'\'']*\)'\''/'\'import { \1 } from '\''\3'\''/g' "$FILE"
  fi
done

echo "${YELLOW}Correction terminée!${NC}"
