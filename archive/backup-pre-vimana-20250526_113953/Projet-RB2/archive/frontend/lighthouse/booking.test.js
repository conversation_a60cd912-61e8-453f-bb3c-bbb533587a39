const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const { expect } = require('chai');

const launchChromeAndRunLighthouse = async (url, opts = {}, config = null) => {
  const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });
  const options = { logLevel: 'info', output: 'json', port: chrome.port, ...opts };
  const runnerResult = await lighthouse(url, options, config);
  await chrome.kill();
  return runnerResult.lhr;
};

describe('Performance Tests', () => {
  const BASE_URL = 'http://localhost:3000';
  const THRESHOLD = {
    performance: 0.8,
    accessibility: 0.9,
    'best-practices': 0.9,
    seo: 0.9,
    pwa: 0.8,
  };

  it('booking page meets performance standards', async () => {
    const result = await launchChromeAndRunLighthouse(`${BASE_URL}/booking`);

    // Performance metrics
    expect(result.categories.performance.score).to.be.above(THRESHOLD.performance);
    expect(result.categories.accessibility.score).to.be.above(THRESHOLD.accessibility);
    expect(result.categories['best-practices'].score).to.be.above(THRESHOLD['best-practices']);
    expect(result.categories.seo.score).to.be.above(THRESHOLD.seo);
    expect(result.categories.pwa.score).to.be.above(THRESHOLD.pwa);

    // Specific metrics
    const metrics = result.audits.metrics.details.items[0];
    expect(metrics['first-contentful-paint']).to.be.below(2000); // 2s
    expect(metrics['largest-contentful-paint']).to.be.below(2500); // 2.5s
    expect(metrics['total-blocking-time']).to.be.below(300); // 300ms
    expect(metrics['cumulative-layout-shift']).to.be.below(0.1); // 0.1
  });

  it('booking form loads quickly under poor network conditions', async () => {
    const result = await launchChromeAndRunLighthouse(`${BASE_URL}/booking`, {
      throttling: {
        rttMs: 150,
        throughputKbps: 1600,
        cpuSlowdownMultiplier: 2,
      },
    });

    expect(result.audits['server-response-time'].score).to.be.above(0.8);
    expect(result.audits['total-blocking-time'].score).to.be.above(0.7);
  });

  it('booking page is optimized for mobile', async () => {
    const result = await launchChromeAndRunLighthouse(`${BASE_URL}/booking`, {
      emulatedFormFactor: 'mobile',
    });

    expect(result.categories.performance.score).to.be.above(THRESHOLD.performance);
    expect(result.audits['content-width'].score).to.equal(1);
    expect(result.audits['tap-targets'].score).to.be.above(0.8);
  });
});
