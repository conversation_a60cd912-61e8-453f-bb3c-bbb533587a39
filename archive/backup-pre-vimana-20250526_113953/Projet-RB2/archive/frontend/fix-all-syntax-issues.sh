#!/bin/bash

# Script pour corriger de nombreux problèmes de syntaxe TypeScript courants

echo "Correction des problèmes de syntaxe..."

# 1. Correction des imports React pour éviter les problèmes esModuleInterop
find ./src -type f \( -name "*.tsx" -o -name "*.ts" \) -exec sed -i '' 's/import React,/import * as React from "react";\nconst {/g' {} \;
find ./src -type f \( -name "*.tsx" -o -name "*.ts" \) -exec sed -i '' 's/{ useState, useEffect } from "react";/useState, useEffect } = React;/g' {} \;

# 2. Correction des balises img
find ./src -type f -name "*.tsx" -exec sed -i '' 's/alt="Avatar" \/>/alt="Avatar" /g' {} \;
find ./src -type f -name "*.tsx" -exec sed -i '' 's/alt="Avatar" /> className="avatar"/alt="Avatar" className="avatar"/g' {} \;

# 3. Correction des balises option
find ./src -type f -name "*.tsx" -exec sed -i '' 's/<option value="/<option value="/g' {} \;
find ./src -type f -name "*.tsx" -exec sed -i '' 's/">{t(/"> {t(/g' {} \;

# 4. Correction des balises Typography
find ./src -type f -name "*.tsx" -exec sed -i '' 's/<Typography variant="body1">/<Typography variant="body1">/g' {} \;

# 5. Correction des problèmes de guillemets dans les imports
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/"";/";/g' {} \;

# 6. Correction d'autres problèmes syntaxiques
find ./src -type f -name "WorkerSecurity.test.ts" -exec sed -i '' 's/});/})/g' {} \;
find ./src -type f -name "axe-setup.ts" -exec sed -i '' 's/}));/}))/g' {} \;

# 7. Correction de la syntaxe JSX
find ./src -type f -name "SecureBookingForm.tsx" -exec sed -i '' 's/<SecureForm/<div><SecureForm/g' {} \;
find ./src -type f -name "SecureBookingForm.tsx" -exec sed -i '' 's/<\/SecureForm>/<\/SecureForm><\/div>/g' {} \;

# 8. Remplacement global des imports qui utilisent des tirets
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as [a-zA-Z]*-[a-zA-Z]*/import * as ImportAlias/g' {} \;

echo "Correction terminée!"
