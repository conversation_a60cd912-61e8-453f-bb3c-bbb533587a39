#!/bin/bash

# Script pour corriger les imports malformés avec des guillemets
echo "Correction des guillemets dans les imports..."

# Remplace les patterns "module'" par "module"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/from "\([^"]*\)'\''/from "\1"/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as \([^ ]*\) from "\([^"]*\)'\''/import * as \1 from "\2"/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import { \([^}]*\) } from "\([^"]*\)'\''/import { \1 } from "\2"/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \([^ ]*\) from "\([^"]*\)'\''/import \1 from "\2"/g' {} \;

echo "Correction terminée!"
