import { execSync } from 'child_process';
import path from 'path';

try {
  console.log('Running TypeScript compiler with correct options...');
  execSync('npx tsc --jsx react --esModuleInterop --skipLibCheck --noEmit frontend/src/pages/RetreatDetailPage.tsx', {
    stdio: 'inherit'
  });
  console.log('TypeScript check completed successfully!');
} catch (error) {
  console.error('Error running TypeScript check:', error.message);
  process.exit(1);
}
