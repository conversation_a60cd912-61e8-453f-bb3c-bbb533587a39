<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1743703014519" clover="3.2.0">
  <project timestamp="1743703014519" name="All files">
    <metrics statements="784" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="416" coveredmethods="0" elements="1221" coveredelements="0" complexity="0" loc="784" ncloc="784" packages="184" files="363" classes="363"/>
    <package name="src">
      <metrics statements="31" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="7" coveredmethods="0"/>
      <file name="api.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/api.ts">
        <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="index.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/index.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
      </file>
      <file name="main.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/main.tsx">
        <metrics statements="9" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
      </file>
      <file name="setupTests.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/setupTests.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.__tests__">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="setup.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/setup.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="test-utils.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/test-utils.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.__tests__.components">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="Button.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/components/Button.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.__tests__.hooks">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="usePerformance.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/hooks/usePerformance.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.__tests__.schemas">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="offlineDataSchema.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/schemas/offlineDataSchema.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.assets.icons">
      <metrics statements="6" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="index.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/assets/icons/index.tsx">
        <metrics statements="6" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="5" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.atomic">
      <metrics statements="20" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="20" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="lazyComponents.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/lazyComponents.ts">
        <metrics statements="20" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="20" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.atomic.__tests__.templates">
      <metrics statements="28" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="20" coveredmethods="0"/>
      <file name="ComponentTestTemplate.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/__tests__/templates/ComponentTestTemplate.tsx">
        <metrics statements="28" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="20" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="21" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.atomic.atoms">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Badge">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Badge.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Badge/Badge.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Badge/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Button">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Button.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Button/Button.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Button/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Checkbox">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Checkbox.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Checkbox/Checkbox.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Checkbox/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Icon">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Icon.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Icon/Icon.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Icon/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Image">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Image.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Image/Image.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Image/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Input">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Input.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Input/Input.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Input/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Label">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Label.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Label/Label.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Label/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Link">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Link.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Link/Link.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Link/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Radio">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Radio.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Radio/Radio.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Radio/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Select">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Select.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Select/Select.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Select/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Spinner">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Spinner.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Spinner/Spinner.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Spinner/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.Typography">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Typography.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Typography/Typography.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Typography/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.atoms.form">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="form.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/form/form.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/form/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.examples">
      <metrics statements="8" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="App.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/examples/App.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.atomic.molecules">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Alert">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Alert.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Alert/Alert.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Alert/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Avatar">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Avatar.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Avatar/Avatar.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Avatar/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Breadcrumb">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Breadcrumb.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Breadcrumb/Breadcrumb.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Breadcrumb/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Card">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Card.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Card/Card.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Card/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Container">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Container.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Container/Container.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Container/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.DatePicker">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="DatePicker.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/DatePicker/DatePicker.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/DatePicker/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Dropdown">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Dropdown.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Dropdown/Dropdown.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Dropdown/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.FileUpload">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="FileUpload.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FileUpload/FileUpload.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FileUpload/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.FormField">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="FormField.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FormField/FormField.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FormField/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.IconLink">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="IconLink.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/IconLink/IconLink.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/IconLink/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.NavigationCarousel">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="NavigationCarousel.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/NavigationCarousel/NavigationCarousel.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/NavigationCarousel/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Pagination">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Pagination.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Pagination/Pagination.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Pagination/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.PasswordInput">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="PasswordInput.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/PasswordInput/PasswordInput.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/PasswordInput/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Price">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Price.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Price/Price.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Price/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Progress">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Progress.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Progress/Progress.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Progress/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.SearchField">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="SearchField.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/SearchField/SearchField.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/SearchField/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Skeleton">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Skeleton.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Skeleton/Skeleton.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Skeleton/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.TabGroup">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="TabGroup.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TabGroup/TabGroup.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TabGroup/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.TimePicker">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="TimePicker.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TimePicker/TimePicker.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TimePicker/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Toast">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Toast.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Toast/Toast.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Toast/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.molecules.Tooltip">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Tooltip.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Tooltip/Tooltip.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Tooltip/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="organisms.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/organisms.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.atomic.organisms.BookingForm">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="BookingForm.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/BookingForm/BookingForm.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/BookingForm/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.Footer">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Footer.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Footer/Footer.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Footer/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.Header">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Header.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Header/Header.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Header/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.Hero">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Hero.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Hero/Hero.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Hero/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.LoginForm">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="LoginForm.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/LoginForm/LoginForm.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/LoginForm/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.Navigation">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Navigation.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Navigation/Navigation.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Navigation/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.NotificationCenter">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="NotificationCenter.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/NotificationCenter/NotificationCenter.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/NotificationCenter/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.PaymentForm">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="PaymentForm.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/PaymentForm/PaymentForm.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/PaymentForm/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.RetreatCard">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="RetreatCard.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatCard/RetreatCard.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatCard/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.RetreatFilters">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="RetreatFilters.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatFilters/RetreatFilters.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatFilters/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.ReviewForm">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="ReviewForm.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/ReviewForm/ReviewForm.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/ReviewForm/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.SearchBar">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="SearchBar.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchBar/SearchBar.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchBar/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.SearchSection">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="SearchSection.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchSection/SearchSection.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchSection/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.Sidebar">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="Sidebar.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Sidebar/Sidebar.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Sidebar/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.organisms.UserProfile">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="UserProfile.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/UserProfile/UserProfile.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/UserProfile/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.pages">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.pages.CheckoutPage">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="CheckoutPage.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/CheckoutPage/CheckoutPage.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/CheckoutPage/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.pages.HomePage">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="HomePage.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/HomePage/HomePage.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/HomePage/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.pages.LoginPage">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="LoginPage.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/LoginPage/LoginPage.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/LoginPage/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.pages.RetreatDetailPage">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="RetreatDetailPage.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatDetailPage/RetreatDetailPage.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatDetailPage/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.pages.RetreatListPage">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="RetreatListPage.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatListPage/RetreatListPage.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatListPage/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.pages.UserDashboardPage">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="UserDashboardPage.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/UserDashboardPage/UserDashboardPage.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/UserDashboardPage/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.routes">
      <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="11" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/routes/index.ts">
        <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.atomic.templates">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.templates.AuthLayout">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="AuthLayout.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/AuthLayout/AuthLayout.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/AuthLayout/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.templates.DashboardLayout">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="DashboardLayout.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/DashboardLayout/DashboardLayout.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/DashboardLayout/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.templates.MainLayout">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="MainLayout.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MainLayout/MainLayout.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MainLayout/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.templates.MarketplaceLayout">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="MarketplaceLayout.styles.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MarketplaceLayout/MarketplaceLayout.styles.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MarketplaceLayout/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.atomic.theme">
      <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="animations.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/theme/animations.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/theme/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="tokens.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/theme/tokens.ts">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components">
      <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="AdminSidebar.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/AdminSidebar.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="App.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/App.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="TaskList.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/TaskList.tsx">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.Admin">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="AdminBookingList.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Admin/AdminBookingList.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.Button">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="Button.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Button/Button.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.Calendar.__tests__">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="EventCalendar.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Calendar/__tests__/EventCalendar.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="EventForm.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Calendar/__tests__/EventForm.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.Header">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Header/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.Notification">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Notification/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.SecurityAlert">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="SecurityAlert.a11y.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/SecurityAlert/SecurityAlert.a11y.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="Button.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/__tests__/Button.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.a11y">
      <metrics statements="6" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="Announcer.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/a11y/Announcer.tsx">
        <metrics statements="6" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/a11y/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.a11y.Dialog">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="Dialog.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/a11y/Dialog/Dialog.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.accessibility">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/accessibility/index.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.accessible">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/accessible/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.analytics">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/analytics/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.atoms">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.atoms.Button">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="AccessibleButton.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Button/AccessibleButton.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="Button.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Button/Button.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.Input">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="Input.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Input/Input.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Input/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.auth">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.auth.__tests__">
      <metrics statements="21" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="14" coveredmethods="0"/>
      <file name="AuthComponent.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/AuthComponent.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="LoginForm.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/LoginForm.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="LoginPage.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/LoginPage.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="PrivateRoute.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/PrivateRoute.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="RegisterPage.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/RegisterPage.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="ResetPasswordForm.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/ResetPasswordForm.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="Verify2FAForm.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/Verify2FAForm.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.booking">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="GroupBooking.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/booking/GroupBooking.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.common">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="AccessibilityTest.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/AccessibilityTest.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.common.Form.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="Input.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/Form/__tests__/Input.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.common.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="accessibility.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/__tests__/accessibility.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.community.__tests__">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="CreatePost.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/community/__tests__/CreatePost.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="Post.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/community/__tests__/Post.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.dashboard.__tests__">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="AnalyticsCard.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/dashboard/__tests__/AnalyticsCard.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="AnalyticsDashboard.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/dashboard/__tests__/AnalyticsDashboard.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.invoice">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="InvoiceGenerator.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/invoice/InvoiceGenerator.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.layout">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.layout.SearchBar.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="SearchBar.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/SearchBar/__tests__/SearchBar.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.layout.__tests__">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="Layout.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/__tests__/Layout.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="Sidebar.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/__tests__/Sidebar.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.messaging.__tests__">
      <metrics statements="42" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="12" coveredmethods="0"/>
      <file name="MessageInput.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/messaging/__tests__/MessageInput.test.tsx">
        <metrics statements="42" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.Navigation">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="AccessibleNavigation.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/molecules/Navigation/AccessibleNavigation.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.Table">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="AccessibleTable.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/molecules/Table/AccessibleTable.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.navigation.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="NavItem.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/navigation/__tests__/NavItem.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.nft.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="NFTGallery.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/nft/__tests__/NFTGallery.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.profile.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="ProfileSettings.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/profile/__tests__/ProfileSettings.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.pwa">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/pwa/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.retreats.__tests__">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="RetreatFilters.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/retreats/__tests__/RetreatFilters.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="RetreatList.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/retreats/__tests__/RetreatList.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.routing.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="NavigationTransition.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/routing/__tests__/NavigationTransition.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.security.__tests__">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="SecuritySettings.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/security/__tests__/SecuritySettings.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="TwoFactorSetup.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/security/__tests__/TwoFactorSetup.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.settings">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="AppearanceSettings.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/settings/AppearanceSettings.tsx">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.social.Poll.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="PollComponent.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/social/Poll/__tests__/PollComponent.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.social.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="SocialFeed.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/social/__tests__/SocialFeed.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.ui">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="demo.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/ui/demo.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.ui.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="OptimizedImage.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/ui/__tests__/OptimizedImage.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.config">
      <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="monitoring.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/config/monitoring.ts">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
      <file name="performance.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/config/performance.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.containers.Financial.pages">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="DashboardPage.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/containers/Financial/pages/DashboardPage.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="PaymentMethodsPage.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/containers/Financial/pages/PaymentMethodsPage.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
      <file name="TransactionHistoryPage.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/containers/Financial/pages/TransactionHistoryPage.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.contexts">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/contexts/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.contracts.test">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="RetreatBooking.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/contracts/test/RetreatBooking.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.core.api.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="apiClient.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/core/api/__tests__/apiClient.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.analyses.components.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="AnalysisTable.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/analyses/components/__tests__/AnalysisTable.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.auth">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/auth/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.features.auth.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="AuthContext.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/auth/__tests__/AuthContext.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.features.auth.components">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/auth/components/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.features.marketplace">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/marketplace/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.features.marketplace.components">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/marketplace/components/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.features.security">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="SecurityDashboard.integration.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/security/SecurityDashboard.integration.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="SecurityDashboard.perf.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/security/SecurityDashboard.perf.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.hooks">
      <metrics statements="9" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="useAdaptiveCache.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAdaptiveCache.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="useAuth.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAuth.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="useAuth.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAuth.ts">
        <metrics statements="5" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
      </file>
      <file name="useNotificationSystem.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useNotificationSystem.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.hooks.__tests__">
      <metrics statements="30" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="20" coveredmethods="0"/>
      <file name="useActivity.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useActivity.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="useBreakpoints.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useBreakpoints.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="useCommunityEngagement.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useCommunityEngagement.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="useCommunityInteractions.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useCommunityInteractions.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
      <file name="useForm.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useForm.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
      <file name="useLayoutState.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useLayoutState.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
      <file name="useNotifications.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useNotifications.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="usePoll.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/usePoll.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="useScrollPosition.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useScrollPosition.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="useSocialFeed.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useSocialFeed.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.hooks.synchronization">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/synchronization/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.i18n">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="i18n.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/i18n/i18n.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="react-query.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/lib/react-query.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.middleware">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/middleware/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.middleware.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="authMiddleware.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/middleware/__tests__/authMiddleware.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.monitoring.dashboard">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="config.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/monitoring/dashboard/config.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.monitoring.tests">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="MonitoringSystem.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/monitoring/tests/MonitoringSystem.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.pages.NftGallery.nft">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/NftGallery/nft/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.pages.Public">
      <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="HomePage.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/HomePage.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="LoginPage.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/LoginPage.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="UnauthorizedPage.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/UnauthorizedPage.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.pages.admin">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="AdminPage.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/admin/AdminPage.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.pages.auth">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/auth/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.pages.error">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/error/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.pages.error.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="Error404Page.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/error/__tests__/Error404Page.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.pages.nft">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/nft/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.pages.partner">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/partner/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.pages.services">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/services/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.pages.social">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/social/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.routes.__tests__">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="ProtectedRoute.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/routes/__tests__/ProtectedRoute.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.schemas.validation">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/schemas/validation/index.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.security">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="WorkerSecurityValidator.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/WorkerSecurityValidator.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.security.__tests__">
      <metrics statements="15" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="AuditTests.spec.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/AuditTests.spec.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="AuditTests.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/AuditTests.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="WorkerSecurity.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/WorkerSecurity.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="XSSValidator.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/XSSValidator.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="utils.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/utils.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.security.constants">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="security.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/constants/security.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="queryClient.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/queryClient.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services.SecurityAnalytics">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="SecurityAnalytics.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/SecurityAnalytics/SecurityAnalytics.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services.__tests__">
      <metrics statements="36" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="24" coveredmethods="0"/>
      <file name="AlertManager.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/AlertManager.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="CircuitBreaker.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/CircuitBreaker.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="FeatureFlags.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/FeatureFlags.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="LoginPage.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/LoginPage.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="NotificationClient.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/NotificationClient.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="RegisterPage.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/RegisterPage.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="SecuritySettings.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/SecuritySettings.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="TwoFactorSetup.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/TwoFactorSetup.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="Web3Service.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/Web3Service.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="authService.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/authService.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="exportService.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/exportService.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="fileService.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/fileService.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services.ai">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="AIAssistantService.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/ai/AIAssistantService.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.services.offline">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/offline/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.shared">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/shared/index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.simple-tests">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="useAuth.fixed.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/simple-tests/useAuth.fixed.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.store">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="messageStore.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/store/messageStore.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.store.selectors.__tests__">
      <metrics statements="33" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="userSelectors.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/store/selectors/__tests__/userSelectors.test.ts">
        <metrics statements="33" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.styles">
      <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="theme.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/styles/theme.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests">
      <metrics statements="18" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="AdaptiveBandwidth.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/AdaptiveBandwidth.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
      </file>
      <file name="sample.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/sample.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="setupAccessibilityTests.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setupAccessibilityTests.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
      <file name="setupAxe.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setupAxe.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="setupTests.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setupTests.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="simple-component.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/simple-component.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.accessibility">
      <metrics statements="35" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="22" coveredmethods="0"/>
      <file name="AccessibilityTests.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/AccessibilityTests.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="BookingAccessibility.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/BookingAccessibility.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="Button.a11y.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/Button.a11y.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="CalendarAccessibility.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/CalendarAccessibility.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="ComponentAccessibility.template.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/ComponentAccessibility.template.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="FormAccessibility.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/FormAccessibility.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="NavigationAccessibility.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/NavigationAccessibility.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="a11y.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/a11y.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="axe-setup.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/axe-setup.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
      <file name="axe.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/axe.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="button.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/button.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="form.a11y.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/form.a11y.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.components">
      <metrics statements="15" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="BookingForm.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/BookingForm.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="Button.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/Button.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="LoginForm.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/LoginForm.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="ProductList.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/ProductList.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="UserProfile.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/UserProfile.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.config">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="loadTestConfig.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/config/loadTestConfig.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.dashboard.__tests__">
      <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="TestDashboard.load.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.load.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="TestDashboard.perf.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.perf.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="TestDashboard.security.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.security.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="TestDashboard.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.e2e">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="critical-paths.cy.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/e2e/critical-paths.cy.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.edge-cases">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="FormValidation.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/edge-cases/FormValidation.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="validation.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/edge-cases/validation.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.hooks">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="useAuth.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/hooks/useAuth.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.integration">
      <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="Auth.integration.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/integration/Auth.integration.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="BookingFlow.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/integration/BookingFlow.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="CalendarFlow.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/integration/CalendarFlow.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.load">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="LoadTests.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/load/LoadTests.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.monitoring">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="TestMonitor.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/monitoring/TestMonitor.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.performance">
      <metrics statements="15" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="ComponentLoadTest.perf.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/ComponentLoadTest.perf.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="PerformanceTests.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/PerformanceTests.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="PerformanceTests.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/PerformanceTests.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="list.perf.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/list.perf.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="metrics.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/metrics.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.recommendation">
      <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="CollaborativeFilteringModel.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/recommendation/CollaborativeFilteringModel.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="ContentBasedModel.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/recommendation/ContentBasedModel.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="RecommendationService.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/recommendation/RecommendationService.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.security">
      <metrics statements="39" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="26" coveredmethods="0"/>
      <file name="DependencyVulnerabilityTests.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/DependencyVulnerabilityTests.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="DoSProtectionTests.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/DoSProtectionTests.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="JWTSecurity.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/JWTSecurity.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="LoggingMonitoringTests.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/LoggingMonitoringTests.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="SSRFTests.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SSRFTests.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="SecurityAnalyzer.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityAnalyzer.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="SecurityAnalyzer.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityAnalyzer.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="SecurityHeaders.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityHeaders.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="SecurityTests.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityTests.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="StorageSecurity.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/StorageSecurity.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="UserEnumerationTests.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/UserEnumerationTests.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="VulnerabilityDetector.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/VulnerabilityDetector.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="validation.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/validation.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.security.examples">
      <metrics statements="18" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="12" coveredmethods="0"/>
      <file name="APISecurityTests.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/APISecurityTests.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="AuthorizationSecurity.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/AuthorizationSecurity.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="FileUploadSecurity.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/FileUploadSecurity.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="LoginFormSecurity.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/LoginFormSecurity.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="OpenRedirectSecurity.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/OpenRedirectSecurity.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="StateManagementSecurity.test.tsx" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/StateManagementSecurity.test.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.services">
      <metrics statements="15" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="authService.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/authService.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="exportService.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/exportService.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="testData.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/testData.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="userService.msw.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/userService.msw.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="userService.test.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/userService.test.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.setup">
      <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="setupAccessibilityTests.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setup/setupAccessibilityTests.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="setupApiMocks.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setup/setupApiMocks.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="setupPerformanceTests.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setup/setupPerformanceTests.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tests.utils">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="a11y-helpers.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/utils/a11y-helpers.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="accessibility-helpers.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/utils/accessibility-helpers.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.theme">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/theme/index.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="themeConfig.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/theme/themeConfig.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.utils">
      <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="config.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/utils/config.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
      </file>
      <file name="pdfUtils.ts" path="/Users/<USER>/Desktop/Projet-RB2/frontend/src/utils/pdfUtils.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
  </project>
</coverage>
