
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/atomic/lazyComponents.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/atomic</a> lazyComponents.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/40</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/20</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/20</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import { lazyLoad } from './utils/lazyLoad';
&nbsp;
// Organismes - ces composants sont plus complexes et bénéficient du lazy loading
export const LazyRetreatCard = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/RetreatCard/RetreatCard'))</span>;</span>
export const LazyRetreatFilters = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/RetreatFilters/RetreatFilters'))</span>;</span>
export const LazyBookingForm = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/BookingForm/BookingForm'))</span>;</span>
export const LazyPaymentForm = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/PaymentForm/PaymentForm'))</span>;</span>
export const LazyUserProfile = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/UserProfile/UserProfile'))</span>;</span>
export const LazyHeader = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/Header/Header'))</span>;</span>
export const LazyFooter = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/Footer/Footer'))</span>;</span>
export const LazySidebar = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/Sidebar/Sidebar'))</span>;</span>
export const LazyNotificationCenter = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/NotificationCenter/NotificationCenter'))</span>;</span>
export const LazyReviewForm = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./organisms/ReviewForm/ReviewForm'))</span>;</span>
&nbsp;
// Templates - ces composants définissent la structure de la page
export const LazyMainLayout = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./templates/MainLayout/MainLayout'))</span>;</span>
export const LazyAuthLayout = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./templates/AuthLayout/AuthLayout'))</span>;</span>
export const LazyDashboardLayout = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./templates/DashboardLayout/DashboardLayout'))</span>;</span>
export const LazyMarketplaceLayout = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./templates/MarketplaceLayout/MarketplaceLayout'))</span>;</span>
&nbsp;
// Pages - ces composants représentent des pages complètes
export const LazyHomepage = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./pages/Homepage/Homepage'))</span>;</span>
export const LazyRetreatListPage = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./pages/RetreatListPage/RetreatListPage'))</span>;</span>
export const LazyRetreatDetailPage = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./pages/RetreatDetailPage/RetreatDetailPage'))</span>;</span>
export const LazyCheckoutPage = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./pages/CheckoutPage/CheckoutPage'))</span>;</span>
export const LazyUserDashboardPage = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./pages/UserDashboardPage/UserDashboardPage'))</span>;</span>
export const LazyLoginPage = <span class="cstat-no" title="statement not covered" >lazyLoad(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >import('./pages/LoginPage/LoginPage'))</span>; </span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-04-03T17:56:53.967Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    