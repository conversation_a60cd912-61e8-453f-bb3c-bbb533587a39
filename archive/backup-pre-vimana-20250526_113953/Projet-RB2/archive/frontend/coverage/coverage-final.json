{"/Users/<USER>/Desktop/Projet-RB2/frontend/src/api.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/api.ts", "statementMap": {"0": {"start": {"line": 3, "column": 12}, "end": {"line": 8, "column": 2}}, "1": {"start": {"line": 10, "column": 26}, "end": {"line": 19, "column": 1}}, "2": {"start": {"line": 11, "column": 19}, "end": {"line": 11, "column": 33}}, "3": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 32}}, "4": {"start": {"line": 13, "column": 19}, "end": {"line": 17, "column": 2}}, "5": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 23}}, "6": {"start": {"line": 21, "column": 28}, "end": {"line": 29, "column": 1}}, "7": {"start": {"line": 22, "column": 19}, "end": {"line": 27, "column": 2}}, "8": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 23}}, "9": {"start": {"line": 31, "column": 26}, "end": {"line": 39, "column": 1}}, "10": {"start": {"line": 32, "column": 19}, "end": {"line": 37, "column": 2}}, "11": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 23}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 26}, "end": {"line": 10, "column": 27}}, "loc": {"start": {"line": 10, "column": 49}, "end": {"line": 19, "column": 1}}, "line": 10}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 21, "column": 28}, "end": {"line": 21, "column": 29}}, "loc": {"start": {"line": 21, "column": 53}, "end": {"line": 29, "column": 1}}, "line": 21}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 31, "column": 26}, "end": {"line": 31, "column": 27}}, "loc": {"start": {"line": 31, "column": 51}, "end": {"line": 39, "column": 1}}, "line": 31}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/index.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/index.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 18}, "end": {"line": 11, "column": 49}}, "1": {"start": {"line": 13, "column": 0}, "end": {"line": 15, "column": 1}}, "2": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 71}}, "3": {"start": {"line": 18, "column": 13}, "end": {"line": 18, "column": 34}}, "4": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 55}}, "5": {"start": {"line": 24, "column": 0}, "end": {"line": 34, "column": 2}}, "6": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 26}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 0}, "end": {"line": 15, "column": 1}}, "type": "if", "locations": [{"start": {"line": 13, "column": 0}, "end": {"line": 15, "column": 1}}, {"start": {}, "end": {}}], "line": 13}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {}, "b": {"0": [0, 0]}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/main.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/main.tsx", "statementMap": {"0": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 27}}, "1": {"start": {"line": 23, "column": 20}, "end": {"line": 23, "column": 51}}, "2": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 69}}, "3": {"start": {"line": 24, "column": 18}, "end": {"line": 24, "column": 69}}, "4": {"start": {"line": 26, "column": 0}, "end": {"line": 35, "column": 2}}, "5": {"start": {"line": 38, "column": 0}, "end": {"line": 49, "column": 3}}, "6": {"start": {"line": 40, "column": 2}, "end": {"line": 48, "column": 3}}, "7": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 39}}, "8": {"start": {"line": 43, "column": 4}, "end": {"line": 47, "column": 70}}, "9": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": 68}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 38, "column": 16}, "end": {"line": 38, "column": 17}}, "loc": {"start": {"line": 38, "column": 26}, "end": {"line": 49, "column": 1}}, "line": 38}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 47, "column": 13}, "end": {"line": 47, "column": 14}}, "loc": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": 68}}, "line": 47}}, "branchMap": {"0": {"loc": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 69}}, "type": "if", "locations": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 69}}, {"start": {}, "end": {}}], "line": 24}, "1": {"loc": {"start": {"line": 40, "column": 2}, "end": {"line": 48, "column": 3}}, "type": "if", "locations": [{"start": {"line": 40, "column": 2}, "end": {"line": 48, "column": 3}}, {"start": {}, "end": {}}], "line": 40}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/setupTests.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/setupTests.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/setup.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/setup.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/test-utils.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/test-utils.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/components/Button.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/components/Button.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/hooks/usePerformance.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/hooks/usePerformance.test.ts", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 11, "column": 3}}, "1": {"start": {"line": 7, "column": 2}, "end": {"line": 10, "column": 5}}, "2": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 22}, "end": {"line": 4, "column": 23}}, "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 11, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 30}}, "loc": {"start": {"line": 7, "column": 35}, "end": {"line": 10, "column": 3}}, "line": 7}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/schemas/offlineDataSchema.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/__tests__/schemas/offlineDataSchema.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/assets/icons/index.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/assets/icons/index.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 24}, "end": {"line": 24, "column": 1}}, "1": {"start": {"line": 5, "column": 2}, "end": {"line": 23, "column": 4}}, "2": {"start": {"line": 27, "column": 21}, "end": {"line": 45, "column": 1}}, "3": {"start": {"line": 28, "column": 2}, "end": {"line": 44, "column": 4}}, "4": {"start": {"line": 48, "column": 21}, "end": {"line": 65, "column": 1}}, "5": {"start": {"line": 49, "column": 2}, "end": {"line": 64, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 25}}, "loc": {"start": {"line": 4, "column": 77}, "end": {"line": 24, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 21}, "end": {"line": 27, "column": 22}}, "loc": {"start": {"line": 27, "column": 74}, "end": {"line": 45, "column": 1}}, "line": 27}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 48, "column": 21}, "end": {"line": 48, "column": 22}}, "loc": {"start": {"line": 48, "column": 74}, "end": {"line": 65, "column": 1}}, "line": 48}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 36}}, "type": "default-arg", "locations": [{"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 36}}], "line": 4}, "1": {"loc": {"start": {"line": 4, "column": 38}, "end": {"line": 4, "column": 60}}, "type": "default-arg", "locations": [{"start": {"line": 4, "column": 46}, "end": {"line": 4, "column": 60}}], "line": 4}, "2": {"loc": {"start": {"line": 27, "column": 24}, "end": {"line": 27, "column": 33}}, "type": "default-arg", "locations": [{"start": {"line": 27, "column": 31}, "end": {"line": 27, "column": 33}}], "line": 27}, "3": {"loc": {"start": {"line": 27, "column": 35}, "end": {"line": 27, "column": 57}}, "type": "default-arg", "locations": [{"start": {"line": 27, "column": 43}, "end": {"line": 27, "column": 57}}], "line": 27}, "4": {"loc": {"start": {"line": 48, "column": 24}, "end": {"line": 48, "column": 33}}, "type": "default-arg", "locations": [{"start": {"line": 48, "column": 31}, "end": {"line": 48, "column": 33}}], "line": 48}, "5": {"loc": {"start": {"line": 48, "column": 35}, "end": {"line": 48, "column": 57}}, "type": "default-arg", "locations": [{"start": {"line": 48, "column": 43}, "end": {"line": 48, "column": 57}}], "line": 48}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0]}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/lazyComponents.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/lazyComponents.ts", "statementMap": {"0": {"start": {"line": 4, "column": 31}, "end": {"line": 4, "column": 92}}, "1": {"start": {"line": 4, "column": 46}, "end": {"line": 4, "column": 91}}, "2": {"start": {"line": 5, "column": 34}, "end": {"line": 5, "column": 101}}, "3": {"start": {"line": 5, "column": 49}, "end": {"line": 5, "column": 100}}, "4": {"start": {"line": 6, "column": 31}, "end": {"line": 6, "column": 92}}, "5": {"start": {"line": 6, "column": 46}, "end": {"line": 6, "column": 91}}, "6": {"start": {"line": 7, "column": 31}, "end": {"line": 7, "column": 92}}, "7": {"start": {"line": 7, "column": 46}, "end": {"line": 7, "column": 91}}, "8": {"start": {"line": 8, "column": 31}, "end": {"line": 8, "column": 92}}, "9": {"start": {"line": 8, "column": 46}, "end": {"line": 8, "column": 91}}, "10": {"start": {"line": 9, "column": 26}, "end": {"line": 9, "column": 77}}, "11": {"start": {"line": 9, "column": 41}, "end": {"line": 9, "column": 76}}, "12": {"start": {"line": 10, "column": 26}, "end": {"line": 10, "column": 77}}, "13": {"start": {"line": 10, "column": 41}, "end": {"line": 10, "column": 76}}, "14": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 80}}, "15": {"start": {"line": 11, "column": 42}, "end": {"line": 11, "column": 79}}, "16": {"start": {"line": 12, "column": 38}, "end": {"line": 12, "column": 113}}, "17": {"start": {"line": 12, "column": 53}, "end": {"line": 12, "column": 112}}, "18": {"start": {"line": 13, "column": 30}, "end": {"line": 13, "column": 89}}, "19": {"start": {"line": 13, "column": 45}, "end": {"line": 13, "column": 88}}, "20": {"start": {"line": 16, "column": 30}, "end": {"line": 16, "column": 89}}, "21": {"start": {"line": 16, "column": 45}, "end": {"line": 16, "column": 88}}, "22": {"start": {"line": 17, "column": 30}, "end": {"line": 17, "column": 89}}, "23": {"start": {"line": 17, "column": 45}, "end": {"line": 17, "column": 88}}, "24": {"start": {"line": 18, "column": 35}, "end": {"line": 18, "column": 104}}, "25": {"start": {"line": 18, "column": 50}, "end": {"line": 18, "column": 103}}, "26": {"start": {"line": 19, "column": 37}, "end": {"line": 19, "column": 110}}, "27": {"start": {"line": 19, "column": 52}, "end": {"line": 19, "column": 109}}, "28": {"start": {"line": 22, "column": 28}, "end": {"line": 22, "column": 79}}, "29": {"start": {"line": 22, "column": 43}, "end": {"line": 22, "column": 78}}, "30": {"start": {"line": 23, "column": 35}, "end": {"line": 23, "column": 100}}, "31": {"start": {"line": 23, "column": 50}, "end": {"line": 23, "column": 99}}, "32": {"start": {"line": 24, "column": 37}, "end": {"line": 24, "column": 106}}, "33": {"start": {"line": 24, "column": 52}, "end": {"line": 24, "column": 105}}, "34": {"start": {"line": 25, "column": 32}, "end": {"line": 25, "column": 91}}, "35": {"start": {"line": 25, "column": 47}, "end": {"line": 25, "column": 90}}, "36": {"start": {"line": 26, "column": 37}, "end": {"line": 26, "column": 106}}, "37": {"start": {"line": 26, "column": 52}, "end": {"line": 26, "column": 105}}, "38": {"start": {"line": 27, "column": 29}, "end": {"line": 27, "column": 82}}, "39": {"start": {"line": 27, "column": 44}, "end": {"line": 27, "column": 81}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 40}, "end": {"line": 4, "column": 41}}, "loc": {"start": {"line": 4, "column": 46}, "end": {"line": 4, "column": 91}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 5, "column": 43}, "end": {"line": 5, "column": 44}}, "loc": {"start": {"line": 5, "column": 49}, "end": {"line": 5, "column": 100}}, "line": 5}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 6, "column": 40}, "end": {"line": 6, "column": 41}}, "loc": {"start": {"line": 6, "column": 46}, "end": {"line": 6, "column": 91}}, "line": 6}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 7, "column": 40}, "end": {"line": 7, "column": 41}}, "loc": {"start": {"line": 7, "column": 46}, "end": {"line": 7, "column": 91}}, "line": 7}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 8, "column": 40}, "end": {"line": 8, "column": 41}}, "loc": {"start": {"line": 8, "column": 46}, "end": {"line": 8, "column": 91}}, "line": 8}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 9, "column": 35}, "end": {"line": 9, "column": 36}}, "loc": {"start": {"line": 9, "column": 41}, "end": {"line": 9, "column": 76}}, "line": 9}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 10, "column": 35}, "end": {"line": 10, "column": 36}}, "loc": {"start": {"line": 10, "column": 41}, "end": {"line": 10, "column": 76}}, "line": 10}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 11, "column": 36}, "end": {"line": 11, "column": 37}}, "loc": {"start": {"line": 11, "column": 42}, "end": {"line": 11, "column": 79}}, "line": 11}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 12, "column": 47}, "end": {"line": 12, "column": 48}}, "loc": {"start": {"line": 12, "column": 53}, "end": {"line": 12, "column": 112}}, "line": 12}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 13, "column": 39}, "end": {"line": 13, "column": 40}}, "loc": {"start": {"line": 13, "column": 45}, "end": {"line": 13, "column": 88}}, "line": 13}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 16, "column": 39}, "end": {"line": 16, "column": 40}}, "loc": {"start": {"line": 16, "column": 45}, "end": {"line": 16, "column": 88}}, "line": 16}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 17, "column": 39}, "end": {"line": 17, "column": 40}}, "loc": {"start": {"line": 17, "column": 45}, "end": {"line": 17, "column": 88}}, "line": 17}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 18, "column": 44}, "end": {"line": 18, "column": 45}}, "loc": {"start": {"line": 18, "column": 50}, "end": {"line": 18, "column": 103}}, "line": 18}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 19, "column": 46}, "end": {"line": 19, "column": 47}}, "loc": {"start": {"line": 19, "column": 52}, "end": {"line": 19, "column": 109}}, "line": 19}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 22, "column": 37}, "end": {"line": 22, "column": 38}}, "loc": {"start": {"line": 22, "column": 43}, "end": {"line": 22, "column": 78}}, "line": 22}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 23, "column": 44}, "end": {"line": 23, "column": 45}}, "loc": {"start": {"line": 23, "column": 50}, "end": {"line": 23, "column": 99}}, "line": 23}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 24, "column": 46}, "end": {"line": 24, "column": 47}}, "loc": {"start": {"line": 24, "column": 52}, "end": {"line": 24, "column": 105}}, "line": 24}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 25, "column": 41}, "end": {"line": 25, "column": 42}}, "loc": {"start": {"line": 25, "column": 47}, "end": {"line": 25, "column": 90}}, "line": 25}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 26, "column": 46}, "end": {"line": 26, "column": 47}}, "loc": {"start": {"line": 26, "column": 52}, "end": {"line": 26, "column": 105}}, "line": 26}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 27, "column": 38}, "end": {"line": 27, "column": 39}}, "loc": {"start": {"line": 27, "column": 44}, "end": {"line": 27, "column": 81}}, "line": 27}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/__tests__/templates/ComponentTestTemplate.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/__tests__/templates/ComponentTestTemplate.tsx", "statementMap": {"0": {"start": {"line": 18, "column": 0}, "end": {"line": 130, "column": 3}}, "1": {"start": {"line": 20, "column": 26}, "end": {"line": 27, "column": 3}}, "2": {"start": {"line": 21, "column": 4}, "end": {"line": 26, "column": 6}}, "3": {"start": {"line": 30, "column": 2}, "end": {"line": 41, "column": 5}}, "4": {"start": {"line": 31, "column": 4}, "end": {"line": 34, "column": 7}}, "5": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 24}}, "6": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 68}}, "7": {"start": {"line": 36, "column": 4}, "end": {"line": 40, "column": 7}}, "8": {"start": {"line": 37, "column": 28}, "end": {"line": 37, "column": 45}}, "9": {"start": {"line": 38, "column": 22}, "end": {"line": 38, "column": 42}}, "10": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 43}}, "11": {"start": {"line": 44, "column": 2}, "end": {"line": 61, "column": 5}}, "12": {"start": {"line": 45, "column": 4}, "end": {"line": 48, "column": 7}}, "13": {"start": {"line": 50, "column": 4}, "end": {"line": 54, "column": 7}}, "14": {"start": {"line": 56, "column": 4}, "end": {"line": 60, "column": 7}}, "15": {"start": {"line": 64, "column": 2}, "end": {"line": 81, "column": 5}}, "16": {"start": {"line": 65, "column": 4}, "end": {"line": 71, "column": 7}}, "17": {"start": {"line": 73, "column": 4}, "end": {"line": 80, "column": 7}}, "18": {"start": {"line": 84, "column": 2}, "end": {"line": 111, "column": 5}}, "19": {"start": {"line": 85, "column": 4}, "end": {"line": 94, "column": 7}}, "20": {"start": {"line": 96, "column": 4}, "end": {"line": 103, "column": 7}}, "21": {"start": {"line": 105, "column": 4}, "end": {"line": 110, "column": 7}}, "22": {"start": {"line": 114, "column": 2}, "end": {"line": 129, "column": 5}}, "23": {"start": {"line": 115, "column": 4}, "end": {"line": 118, "column": 7}}, "24": {"start": {"line": 116, "column": 28}, "end": {"line": 116, "column": 45}}, "25": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 42}}, "26": {"start": {"line": 120, "column": 4}, "end": {"line": 123, "column": 7}}, "27": {"start": {"line": 125, "column": 4}, "end": {"line": 128, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 26}, "end": {"line": 18, "column": 27}}, "loc": {"start": {"line": 18, "column": 32}, "end": {"line": 130, "column": 1}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 26}, "end": {"line": 20, "column": 27}}, "loc": {"start": {"line": 20, "column": 42}, "end": {"line": 27, "column": 3}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 28}, "end": {"line": 30, "column": 29}}, "loc": {"start": {"line": 30, "column": 34}, "end": {"line": 41, "column": 3}}, "line": 30}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 31, "column": 40}, "end": {"line": 31, "column": 41}}, "loc": {"start": {"line": 31, "column": 46}, "end": {"line": 34, "column": 5}}, "line": 31}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 36, "column": 52}, "end": {"line": 36, "column": 53}}, "loc": {"start": {"line": 36, "column": 64}, "end": {"line": 40, "column": 5}}, "line": 36}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 44, "column": 20}, "end": {"line": 44, "column": 21}}, "loc": {"start": {"line": 44, "column": 26}, "end": {"line": 61, "column": 3}}, "line": 44}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 45, "column": 77}, "end": {"line": 45, "column": 78}}, "loc": {"start": {"line": 45, "column": 83}, "end": {"line": 48, "column": 5}}, "line": 45}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 50, "column": 83}, "end": {"line": 50, "column": 84}}, "loc": {"start": {"line": 50, "column": 89}, "end": {"line": 54, "column": 5}}, "line": 50}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 56, "column": 67}, "end": {"line": 56, "column": 68}}, "loc": {"start": {"line": 56, "column": 73}, "end": {"line": 60, "column": 5}}, "line": 56}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 64, "column": 25}, "end": {"line": 64, "column": 26}}, "loc": {"start": {"line": 64, "column": 31}, "end": {"line": 81, "column": 3}}, "line": 64}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 65, "column": 57}, "end": {"line": 65, "column": 58}}, "loc": {"start": {"line": 65, "column": 63}, "end": {"line": 71, "column": 5}}, "line": 65}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 73, "column": 67}, "end": {"line": 73, "column": 68}}, "loc": {"start": {"line": 73, "column": 73}, "end": {"line": 80, "column": 5}}, "line": 73}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 84, "column": 34}, "end": {"line": 84, "column": 35}}, "loc": {"start": {"line": 84, "column": 40}, "end": {"line": 111, "column": 3}}, "line": 84}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 85, "column": 65}, "end": {"line": 85, "column": 66}}, "loc": {"start": {"line": 85, "column": 77}, "end": {"line": 94, "column": 5}}, "line": 85}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 96, "column": 74}, "end": {"line": 96, "column": 75}}, "loc": {"start": {"line": 96, "column": 86}, "end": {"line": 103, "column": 5}}, "line": 96}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 105, "column": 66}, "end": {"line": 105, "column": 67}}, "loc": {"start": {"line": 105, "column": 72}, "end": {"line": 110, "column": 5}}, "line": 105}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 114, "column": 24}, "end": {"line": 114, "column": 25}}, "loc": {"start": {"line": 114, "column": 30}, "end": {"line": 129, "column": 3}}, "line": 114}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 115, "column": 68}, "end": {"line": 115, "column": 69}}, "loc": {"start": {"line": 115, "column": 74}, "end": {"line": 118, "column": 5}}, "line": 115}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 120, "column": 69}, "end": {"line": 120, "column": 70}}, "loc": {"start": {"line": 120, "column": 75}, "end": {"line": 123, "column": 5}}, "line": 120}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 125, "column": 66}, "end": {"line": 125, "column": 67}}, "loc": {"start": {"line": 125, "column": 72}, "end": {"line": 128, "column": 5}}, "line": 125}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 27}, "end": {"line": 20, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 20, "column": 35}, "end": {"line": 20, "column": 37}}], "line": 20}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "b": {"0": [0]}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Badge/Badge.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Badge/Badge.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Badge/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Badge/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Button/Button.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Button/Button.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 28}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Button/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Button/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Checkbox/Checkbox.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Checkbox/Checkbox.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Checkbox/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Checkbox/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Icon/Icon.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Icon/Icon.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 26}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Icon/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Icon/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Image/Image.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Image/Image.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Image/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Image/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Input/Input.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Input/Input.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Input/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Input/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Label/Label.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Label/Label.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Label/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Label/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Link/Link.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Link/Link.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 26}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Link/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Link/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Radio/Radio.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Radio/Radio.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Radio/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Radio/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Select/Select.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Select/Select.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 28}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Select/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Select/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Spinner/Spinner.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Spinner/Spinner.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 29}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Spinner/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Spinner/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Typography/Typography.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Typography/Typography.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Typography/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/Typography/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/form/form.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/form/form.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 26}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/form/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/atoms/form/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/examples/App.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/examples/App.tsx", "statementMap": {"0": {"start": {"line": 19, "column": 16}, "end": {"line": 24, "column": 1}}, "1": {"start": {"line": 20, "column": 2}, "end": {"line": 23, "column": 8}}, "2": {"start": {"line": 27, "column": 16}, "end": {"line": 44, "column": 1}}, "3": {"start": {"line": 28, "column": 2}, "end": {"line": 43, "column": 4}}, "4": {"start": {"line": 47, "column": 27}, "end": {"line": 68, "column": 1}}, "5": {"start": {"line": 48, "column": 38}, "end": {"line": 48, "column": 53}}, "6": {"start": {"line": 50, "column": 2}, "end": {"line": 67, "column": 4}}, "7": {"start": {"line": 55, "column": 23}, "end": {"line": 55, "column": 51}}, "8": {"start": {"line": 55, "column": 45}, "end": {"line": 55, "column": 50}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 19, "column": 16}, "end": {"line": 19, "column": 17}}, "loc": {"start": {"line": 20, "column": 2}, "end": {"line": 23, "column": 8}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 16}, "end": {"line": 27, "column": 17}}, "loc": {"start": {"line": 27, "column": 22}, "end": {"line": 44, "column": 1}}, "line": 27}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 47, "column": 27}, "end": {"line": 47, "column": 28}}, "loc": {"start": {"line": 47, "column": 33}, "end": {"line": 68, "column": 1}}, "line": 47}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 55, "column": 17}, "end": {"line": 55, "column": 18}}, "loc": {"start": {"line": 55, "column": 23}, "end": {"line": 55, "column": 51}}, "line": 55}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 55, "column": 37}, "end": {"line": 55, "column": 38}}, "loc": {"start": {"line": 55, "column": 45}, "end": {"line": 55, "column": 50}}, "line": 55}}, "branchMap": {"0": {"loc": {"start": {"line": 58, "column": 9}, "end": {"line": 58, "column": 68}}, "type": "cond-expr", "locations": [{"start": {"line": 58, "column": 22}, "end": {"line": 58, "column": 43}}, {"start": {"line": 58, "column": 46}, "end": {"line": 58, "column": 68}}], "line": 58}, "1": {"loc": {"start": {"line": 61, "column": 7}, "end": {"line": 65, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 61, "column": 7}, "end": {"line": 61, "column": 17}}, {"start": {"line": 62, "column": 8}, "end": {"line": 64, "column": 19}}], "line": 61}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Alert/Alert.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Alert/Alert.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Alert/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Alert/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Avatar/Avatar.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Avatar/Avatar.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 28}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Avatar/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Avatar/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Breadcrumb/Breadcrumb.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Breadcrumb/Breadcrumb.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Breadcrumb/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Breadcrumb/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Card/Card.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Card/Card.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 26}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Card/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Card/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Container/Container.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Container/Container.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 31}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Container/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Container/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/DatePicker/DatePicker.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/DatePicker/DatePicker.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/DatePicker/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/DatePicker/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Dropdown/Dropdown.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Dropdown/Dropdown.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Dropdown/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Dropdown/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FileUpload/FileUpload.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FileUpload/FileUpload.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FileUpload/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FileUpload/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FormField/FormField.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FormField/FormField.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 31}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FormField/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/FormField/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/IconLink/IconLink.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/IconLink/IconLink.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/IconLink/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/IconLink/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/NavigationCarousel/NavigationCarousel.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/NavigationCarousel/NavigationCarousel.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 40}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/NavigationCarousel/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/NavigationCarousel/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Pagination/Pagination.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Pagination/Pagination.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Pagination/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Pagination/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/PasswordInput/PasswordInput.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/PasswordInput/PasswordInput.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 35}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/PasswordInput/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/PasswordInput/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Price/Price.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Price/Price.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Price/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Price/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Progress/Progress.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Progress/Progress.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Progress/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Progress/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/SearchField/SearchField.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/SearchField/SearchField.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 33}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/SearchField/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/SearchField/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Skeleton/Skeleton.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Skeleton/Skeleton.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Skeleton/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Skeleton/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TabGroup/TabGroup.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TabGroup/TabGroup.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TabGroup/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TabGroup/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TimePicker/TimePicker.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TimePicker/TimePicker.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TimePicker/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/TimePicker/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Toast/Toast.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Toast/Toast.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Toast/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Toast/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Tooltip/Tooltip.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Tooltip/Tooltip.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 29}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Tooltip/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/molecules/Tooltip/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/organisms.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/organisms.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 31}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/BookingForm/BookingForm.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/BookingForm/BookingForm.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 33}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/BookingForm/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/BookingForm/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Footer/Footer.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Footer/Footer.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 28}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Footer/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Footer/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Header/Header.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Header/Header.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 28}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Header/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Header/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Hero/Hero.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Hero/Hero.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 26}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Hero/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Hero/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/LoginForm/LoginForm.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/LoginForm/LoginForm.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 31}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/LoginForm/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/LoginForm/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Navigation/Navigation.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Navigation/Navigation.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Navigation/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Navigation/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/NotificationCenter/NotificationCenter.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/NotificationCenter/NotificationCenter.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 40}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/NotificationCenter/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/NotificationCenter/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/PaymentForm/PaymentForm.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/PaymentForm/PaymentForm.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 33}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/PaymentForm/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/PaymentForm/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatCard/RetreatCard.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatCard/RetreatCard.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 33}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatCard/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatCard/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatFilters/RetreatFilters.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatFilters/RetreatFilters.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 36}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatFilters/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/RetreatFilters/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/ReviewForm/ReviewForm.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/ReviewForm/ReviewForm.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/ReviewForm/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/ReviewForm/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchBar/SearchBar.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchBar/SearchBar.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 31}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchBar/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchBar/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchSection/SearchSection.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchSection/SearchSection.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 35}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchSection/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/SearchSection/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Sidebar/Sidebar.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Sidebar/Sidebar.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 29}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Sidebar/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/Sidebar/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/UserProfile/UserProfile.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/UserProfile/UserProfile.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 33}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/UserProfile/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/organisms/UserProfile/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/CheckoutPage/CheckoutPage.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/CheckoutPage/CheckoutPage.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 34}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/CheckoutPage/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/CheckoutPage/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/HomePage/HomePage.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/HomePage/HomePage.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/HomePage/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/HomePage/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/LoginPage/LoginPage.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/LoginPage/LoginPage.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 31}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/LoginPage/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/LoginPage/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatDetailPage/RetreatDetailPage.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatDetailPage/RetreatDetailPage.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 39}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatDetailPage/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatDetailPage/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatListPage/RetreatListPage.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatListPage/RetreatListPage.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 37}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatListPage/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/RetreatListPage/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/UserDashboardPage/UserDashboardPage.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/UserDashboardPage/UserDashboardPage.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 39}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/UserDashboardPage/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/pages/UserDashboardPage/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/routes/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/routes/index.ts", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 66, "column": 1}}, "1": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 68}}, "2": {"start": {"line": 17, "column": 28}, "end": {"line": 17, "column": 68}}, "3": {"start": {"line": 23, "column": 28}, "end": {"line": 23, "column": 67}}, "4": {"start": {"line": 27, "column": 28}, "end": {"line": 27, "column": 70}}, "5": {"start": {"line": 33, "column": 28}, "end": {"line": 33, "column": 78}}, "6": {"start": {"line": 37, "column": 28}, "end": {"line": 37, "column": 77}}, "7": {"start": {"line": 43, "column": 28}, "end": {"line": 43, "column": 76}}, "8": {"start": {"line": 47, "column": 28}, "end": {"line": 47, "column": 70}}, "9": {"start": {"line": 51, "column": 28}, "end": {"line": 51, "column": 69}}, "10": {"start": {"line": 57, "column": 28}, "end": {"line": 57, "column": 68}}, "11": {"start": {"line": 63, "column": 28}, "end": {"line": 63, "column": 71}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 23}}, "loc": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 68}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 23}}, "loc": {"start": {"line": 17, "column": 28}, "end": {"line": 17, "column": 68}}, "line": 17}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 23, "column": 22}, "end": {"line": 23, "column": 23}}, "loc": {"start": {"line": 23, "column": 28}, "end": {"line": 23, "column": 67}}, "line": 23}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 27, "column": 22}, "end": {"line": 27, "column": 23}}, "loc": {"start": {"line": 27, "column": 28}, "end": {"line": 27, "column": 70}}, "line": 27}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 33, "column": 22}, "end": {"line": 33, "column": 23}}, "loc": {"start": {"line": 33, "column": 28}, "end": {"line": 33, "column": 78}}, "line": 33}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 37, "column": 22}, "end": {"line": 37, "column": 23}}, "loc": {"start": {"line": 37, "column": 28}, "end": {"line": 37, "column": 77}}, "line": 37}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 43, "column": 22}, "end": {"line": 43, "column": 23}}, "loc": {"start": {"line": 43, "column": 28}, "end": {"line": 43, "column": 76}}, "line": 43}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 47, "column": 22}, "end": {"line": 47, "column": 23}}, "loc": {"start": {"line": 47, "column": 28}, "end": {"line": 47, "column": 70}}, "line": 47}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 51, "column": 22}, "end": {"line": 51, "column": 23}}, "loc": {"start": {"line": 51, "column": 28}, "end": {"line": 51, "column": 69}}, "line": 51}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 57, "column": 22}, "end": {"line": 57, "column": 23}}, "loc": {"start": {"line": 57, "column": 28}, "end": {"line": 57, "column": 68}}, "line": 57}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 63, "column": 22}, "end": {"line": 63, "column": 23}}, "loc": {"start": {"line": 63, "column": 28}, "end": {"line": 63, "column": 71}}, "line": 63}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/AuthLayout/AuthLayout.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/AuthLayout/AuthLayout.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/AuthLayout/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/AuthLayout/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/DashboardLayout/DashboardLayout.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/DashboardLayout/DashboardLayout.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 37}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/DashboardLayout/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/DashboardLayout/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MainLayout/MainLayout.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MainLayout/MainLayout.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 32}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MainLayout/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MainLayout/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MarketplaceLayout/MarketplaceLayout.styles.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MarketplaceLayout/MarketplaceLayout.styles.ts", "statementMap": {"0": {"start": {"line": 4, "column": 39}, "end": {"line": 6, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MarketplaceLayout/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/templates/MarketplaceLayout/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/theme/animations.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/theme/animations.ts", "statementMap": {"0": {"start": {"line": 3, "column": 25}, "end": {"line": 61, "column": 1}}, "1": {"start": {"line": 63, "column": 26}, "end": {"line": 114, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/theme/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/theme/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/theme/tokens.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/atomic/theme/tokens.ts", "statementMap": {"0": {"start": {"line": 1, "column": 22}, "end": {"line": 58, "column": 1}}, "1": {"start": {"line": 60, "column": 23}, "end": {"line": 79, "column": 1}}, "2": {"start": {"line": 81, "column": 26}, "end": {"line": 113, "column": 1}}, "3": {"start": {"line": 115, "column": 27}, "end": {"line": 122, "column": 1}}, "4": {"start": {"line": 124, "column": 23}, "end": {"line": 130, "column": 1}}, "5": {"start": {"line": 132, "column": 22}, "end": {"line": 148, "column": 1}}, "6": {"start": {"line": 150, "column": 28}, "end": {"line": 159, "column": 1}}, "7": {"start": {"line": 161, "column": 27}, "end": {"line": 178, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/AdminSidebar.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/AdminSidebar.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 2}, "end": {"line": 3, "column": 34}}}, "fnMap": {"0": {"name": "AdminSidebar", "decl": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 36}}, "loc": {"start": {"line": 2, "column": 39}, "end": {"line": 4, "column": 1}}, "line": 2}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/App.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/App.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/TaskList.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/TaskList.tsx", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Admin/AdminBookingList.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Admin/AdminBookingList.tsx", "statementMap": {"0": {"start": {"line": 7, "column": 32}, "end": {"line": 41, "column": 1}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 40, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 32}, "end": {"line": 7, "column": 33}}, "loc": {"start": {"line": 7, "column": 38}, "end": {"line": 41, "column": 1}}, "line": 7}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Button/Button.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Button/Button.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Calendar/__tests__/EventCalendar.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Calendar/__tests__/EventCalendar.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Calendar/__tests__/EventForm.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Calendar/__tests__/EventForm.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Header/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Header/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Notification/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/Notification/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/SecurityAlert/SecurityAlert.a11y.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/SecurityAlert/SecurityAlert.a11y.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/__tests__/Button.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/__tests__/Button.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/a11y/Announcer.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/a11y/Announcer.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 57}}, "1": {"start": {"line": 6, "column": 19}, "end": {"line": 6, "column": 32}}, "2": {"start": {"line": 8, "column": 2}, "end": {"line": 12, "column": 9}}, "3": {"start": {"line": 9, "column": 4}, "end": {"line": 11, "column": 5}}, "4": {"start": {"line": 10, "column": 6}, "end": {"line": 10, "column": 44}}, "5": {"start": {"line": 14, "column": 2}, "end": {"line": 22, "column": 4}}}, "fnMap": {"0": {"name": "Announcer", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 25}}, "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 23, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 18}, "end": {"line": 8, "column": 19}}, "loc": {"start": {"line": 8, "column": 24}, "end": {"line": 12, "column": 3}}, "line": 8}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 4}, "end": {"line": 11, "column": 5}}, "type": "if", "locations": [{"start": {"line": 9, "column": 4}, "end": {"line": 11, "column": 5}}, {"start": {}, "end": {}}], "line": 9}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/a11y/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/a11y/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/a11y/Dialog/Dialog.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/a11y/Dialog/Dialog.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/accessibility/index.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/accessibility/index.tsx", "statementMap": {"0": {"start": {"line": 7, "column": 0}, "end": {"line": 12, "column": 2}}, "1": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 27}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/accessible/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/accessible/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/analytics/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/analytics/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Button/AccessibleButton.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Button/AccessibleButton.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Button/Button.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Button/Button.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Input/Input.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Input/Input.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Input/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/atoms/Input/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/AuthComponent.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/AuthComponent.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/LoginForm.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/LoginForm.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/LoginPage.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/LoginPage.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/PrivateRoute.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/PrivateRoute.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/RegisterPage.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/RegisterPage.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/ResetPasswordForm.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/ResetPasswordForm.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/Verify2FAForm.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/auth/__tests__/Verify2FAForm.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/booking/GroupBooking.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/booking/GroupBooking.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 206, "column": 4}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/AccessibilityTest.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/AccessibilityTest.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/Form/__tests__/Input.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/Form/__tests__/Input.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/__tests__/accessibility.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/common/__tests__/accessibility.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/community/__tests__/CreatePost.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/community/__tests__/CreatePost.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/community/__tests__/Post.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/community/__tests__/Post.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/dashboard/__tests__/AnalyticsCard.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/dashboard/__tests__/AnalyticsCard.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/dashboard/__tests__/AnalyticsDashboard.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/dashboard/__tests__/AnalyticsDashboard.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/invoice/InvoiceGenerator.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/invoice/InvoiceGenerator.tsx", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 157, "column": 4}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/SearchBar/__tests__/SearchBar.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/SearchBar/__tests__/SearchBar.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/__tests__/Layout.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/__tests__/Layout.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/__tests__/Sidebar.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/layout/__tests__/Sidebar.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/messaging/__tests__/MessageInput.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/messaging/__tests__/MessageInput.test.tsx", "statementMap": {"0": {"start": {"line": 7, "column": 0}, "end": {"line": 12, "column": 4}}, "1": {"start": {"line": 7, "column": 41}, "end": {"line": 12, "column": 1}}, "2": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 36}}, "3": {"start": {"line": 9, "column": 19}, "end": {"line": 9, "column": 44}}, "4": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 36}}, "5": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 34}}, "6": {"start": {"line": 14, "column": 0}, "end": {"line": 80, "column": 3}}, "7": {"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 30}}, "8": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 5}}, "9": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 27}}, "10": {"start": {"line": 21, "column": 2}, "end": {"line": 24, "column": 5}}, "11": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 49}}, "12": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 68}}, "13": {"start": {"line": 26, "column": 2}, "end": {"line": 37, "column": 5}}, "14": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 49}}, "15": {"start": {"line": 29, "column": 18}, "end": {"line": 29, "column": 53}}, "16": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 47}}, "17": {"start": {"line": 32, "column": 23}, "end": {"line": 32, "column": 56}}, "18": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 32}}, "19": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 63}}, "20": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 34}}, "21": {"start": {"line": 39, "column": 2}, "end": {"line": 46, "column": 5}}, "22": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 49}}, "23": {"start": {"line": 42, "column": 23}, "end": {"line": 42, "column": 56}}, "24": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 32}}, "25": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 46}}, "26": {"start": {"line": 48, "column": 2}, "end": {"line": 60, "column": 5}}, "27": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 49}}, "28": {"start": {"line": 51, "column": 17}, "end": {"line": 51, "column": 72}}, "29": {"start": {"line": 52, "column": 18}, "end": {"line": 52, "column": 50}}, "30": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 40}}, "31": {"start": {"line": 56, "column": 23}, "end": {"line": 56, "column": 56}}, "32": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 32}}, "33": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 56}}, "34": {"start": {"line": 62, "column": 2}, "end": {"line": 79, "column": 5}}, "35": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 49}}, "36": {"start": {"line": 65, "column": 17}, "end": {"line": 65, "column": 72}}, "37": {"start": {"line": 66, "column": 18}, "end": {"line": 66, "column": 50}}, "38": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 40}}, "39": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 62}}, "40": {"start": {"line": 74, "column": 25}, "end": {"line": 74, "column": 75}}, "41": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 34}}, "42": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 68}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 34}, "end": {"line": 7, "column": 35}}, "loc": {"start": {"line": 7, "column": 41}, "end": {"line": 12, "column": 1}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 9}, "end": {"line": 8, "column": 10}}, "loc": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 36}}, "line": 8}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": 14}}, "loc": {"start": {"line": 9, "column": 19}, "end": {"line": 9, "column": 44}}, "line": 9}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 10, "column": 9}, "end": {"line": 10, "column": 10}}, "loc": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 36}}, "line": 10}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 11, "column": 8}, "end": {"line": 11, "column": 9}}, "loc": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 34}}, "line": 11}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 14, "column": 25}, "end": {"line": 14, "column": 26}}, "loc": {"start": {"line": 14, "column": 31}, "end": {"line": 80, "column": 1}}, "line": 14}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 17, "column": 13}, "end": {"line": 17, "column": 14}}, "loc": {"start": {"line": 17, "column": 19}, "end": {"line": 19, "column": 3}}, "line": 17}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 21, "column": 53}, "end": {"line": 21, "column": 54}}, "loc": {"start": {"line": 21, "column": 59}, "end": {"line": 24, "column": 3}}, "line": 21}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 26, "column": 78}, "end": {"line": 26, "column": 79}}, "loc": {"start": {"line": 26, "column": 90}, "end": {"line": 37, "column": 3}}, "line": 26}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 39, "column": 61}, "end": {"line": 39, "column": 62}}, "loc": {"start": {"line": 39, "column": 67}, "end": {"line": 46, "column": 3}}, "line": 39}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 48, "column": 41}, "end": {"line": 48, "column": 42}}, "loc": {"start": {"line": 48, "column": 53}, "end": {"line": 60, "column": 3}}, "line": 48}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 62, "column": 56}, "end": {"line": 62, "column": 57}}, "loc": {"start": {"line": 62, "column": 68}, "end": {"line": 79, "column": 3}}, "line": 62}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/molecules/Navigation/AccessibleNavigation.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/molecules/Navigation/AccessibleNavigation.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/molecules/Table/AccessibleTable.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/molecules/Table/AccessibleTable.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/navigation/__tests__/NavItem.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/navigation/__tests__/NavItem.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/nft/__tests__/NFTGallery.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/nft/__tests__/NFTGallery.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/profile/__tests__/ProfileSettings.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/profile/__tests__/ProfileSettings.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/pwa/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/pwa/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/retreats/__tests__/RetreatFilters.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/retreats/__tests__/RetreatFilters.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/retreats/__tests__/RetreatList.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/retreats/__tests__/RetreatList.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/routing/__tests__/NavigationTransition.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/routing/__tests__/NavigationTransition.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/security/__tests__/SecuritySettings.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/security/__tests__/SecuritySettings.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/security/__tests__/TwoFactorSetup.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/security/__tests__/TwoFactorSetup.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/settings/AppearanceSettings.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/settings/AppearanceSettings.tsx", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/social/Poll/__tests__/PollComponent.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/social/Poll/__tests__/PollComponent.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/social/__tests__/SocialFeed.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/social/__tests__/SocialFeed.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/ui/demo.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/ui/demo.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 4}, "end": {"line": 5, "column": 57}}}, "fnMap": {"0": {"name": "DemoBackgroundPaths", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 35}}, "loc": {"start": {"line": 4, "column": 38}, "end": {"line": 6, "column": 1}}, "line": 4}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/ui/__tests__/OptimizedImage.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/components/ui/__tests__/OptimizedImage.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/config/monitoring.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/config/monitoring.ts", "statementMap": {"0": {"start": {"line": 5, "column": 30}, "end": {"line": 14, "column": 1}}, "1": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 15}}, "2": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 11}}, "3": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 30}}, "4": {"start": {"line": 16, "column": 32}, "end": {"line": 51, "column": 1}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": 31}}, "loc": {"start": {"line": 5, "column": 36}, "end": {"line": 14, "column": 1}}, "line": 5}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/config/performance.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/config/performance.ts", "statementMap": {"0": {"start": {"line": 1, "column": 37}, "end": {"line": 25, "column": 1}}, "1": {"start": {"line": 27, "column": 30}, "end": {"line": 50, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/containers/Financial/pages/DashboardPage.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/containers/Financial/pages/DashboardPage.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 22}, "end": {"line": 5, "column": 1}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 30}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": 23}}, "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 5, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/containers/Financial/pages/PaymentMethodsPage.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/containers/Financial/pages/PaymentMethodsPage.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 27}, "end": {"line": 5, "column": 1}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 36}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 28}}, "loc": {"start": {"line": 3, "column": 33}, "end": {"line": 5, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/containers/Financial/pages/TransactionHistoryPage.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/containers/Financial/pages/TransactionHistoryPage.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 31}, "end": {"line": 5, "column": 1}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 40}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 32}}, "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 5, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/contexts/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/contexts/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/contracts/test/RetreatBooking.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/contracts/test/RetreatBooking.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/core/api/__tests__/apiClient.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/core/api/__tests__/apiClient.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/analyses/components/__tests__/AnalysisTable.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/analyses/components/__tests__/AnalysisTable.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/auth/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/auth/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/auth/__tests__/AuthContext.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/auth/__tests__/AuthContext.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/auth/components/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/auth/components/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/marketplace/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/marketplace/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/marketplace/components/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/marketplace/components/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/security/SecurityDashboard.integration.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/security/SecurityDashboard.integration.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/security/SecurityDashboard.perf.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/features/security/SecurityDashboard.perf.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAdaptiveCache.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAdaptiveCache.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAuth.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAuth.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAuth.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useAuth.ts", "statementMap": {"0": {"start": {"line": 5, "column": 23}, "end": {"line": 13, "column": 1}}, "1": {"start": {"line": 6, "column": 18}, "end": {"line": 6, "column": 41}}, "2": {"start": {"line": 8, "column": 2}, "end": {"line": 10, "column": 3}}, "3": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 67}}, "4": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 17}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 24}}, "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 13, "column": 1}}, "line": 5}}, "branchMap": {"0": {"loc": {"start": {"line": 8, "column": 2}, "end": {"line": 10, "column": 3}}, "type": "if", "locations": [{"start": {"line": 8, "column": 2}, "end": {"line": 10, "column": 3}}, {"start": {}, "end": {}}], "line": 8}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useNotificationSystem.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/useNotificationSystem.ts", "statementMap": {"0": {"start": {"line": 4, "column": 37}, "end": {"line": 4, "column": 52}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useActivity.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useActivity.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useBreakpoints.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useBreakpoints.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useCommunityEngagement.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useCommunityEngagement.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useCommunityInteractions.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useCommunityInteractions.test.ts", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 11, "column": 3}}, "1": {"start": {"line": 7, "column": 2}, "end": {"line": 10, "column": 5}}, "2": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 22}, "end": {"line": 4, "column": 23}}, "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 11, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 30}}, "loc": {"start": {"line": 7, "column": 35}, "end": {"line": 10, "column": 3}}, "line": 7}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useForm.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useForm.test.ts", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 11, "column": 3}}, "1": {"start": {"line": 7, "column": 2}, "end": {"line": 10, "column": 5}}, "2": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 22}, "end": {"line": 4, "column": 23}}, "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 11, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 30}}, "loc": {"start": {"line": 7, "column": 35}, "end": {"line": 10, "column": 3}}, "line": 7}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useLayoutState.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useLayoutState.test.ts", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 11, "column": 3}}, "1": {"start": {"line": 7, "column": 2}, "end": {"line": 10, "column": 5}}, "2": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 22}, "end": {"line": 4, "column": 23}}, "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 11, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 30}}, "loc": {"start": {"line": 7, "column": 35}, "end": {"line": 10, "column": 3}}, "line": 7}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useNotifications.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useNotifications.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/usePoll.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/usePoll.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useScrollPosition.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useScrollPosition.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useSocialFeed.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/__tests__/useSocialFeed.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/synchronization/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/hooks/synchronization/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/i18n/i18n.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/i18n/i18n.ts", "statementMap": {"0": {"start": {"line": 7, "column": 34}, "end": {"line": 7, "column": 46}}, "1": {"start": {"line": 10, "column": 0}, "end": {"line": 39, "column": 5}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/lib/react-query.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/lib/react-query.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 13, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/middleware/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/middleware/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/middleware/__tests__/authMiddleware.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/middleware/__tests__/authMiddleware.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/monitoring/dashboard/config.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/monitoring/dashboard/config.ts", "statementMap": {"0": {"start": {"line": 1, "column": 31}, "end": {"line": 163, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/monitoring/tests/MonitoringSystem.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/monitoring/tests/MonitoringSystem.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/NftGallery/nft/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/NftGallery/nft/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/HomePage.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/HomePage.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/LoginPage.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/LoginPage.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 2}, "end": {"line": 3, "column": 36}}}, "fnMap": {"0": {"name": "LoginPage", "decl": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 33}}, "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 4, "column": 1}}, "line": 2}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/UnauthorizedPage.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/UnauthorizedPage.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 2}, "end": {"line": 3, "column": 43}}}, "fnMap": {"0": {"name": "UnauthorizedPage", "decl": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 40}}, "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 4, "column": 1}}, "line": 2}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/Public/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/admin/AdminPage.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/admin/AdminPage.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 2}, "end": {"line": 3, "column": 34}}}, "fnMap": {"0": {"name": "AdminPage", "decl": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 33}}, "loc": {"start": {"line": 2, "column": 36}, "end": {"line": 4, "column": 1}}, "line": 2}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/auth/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/auth/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/error/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/error/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/error/__tests__/Error404Page.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/error/__tests__/Error404Page.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/nft/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/nft/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/partner/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/partner/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/services/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/services/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/social/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/pages/social/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/routes/__tests__/ProtectedRoute.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/routes/__tests__/ProtectedRoute.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/schemas/validation/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/schemas/validation/index.ts", "statementMap": {"0": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 99}}, "1": {"start": {"line": 5, "column": 30}, "end": {"line": 10, "column": 87}}, "2": {"start": {"line": 12, "column": 27}, "end": {"line": 14, "column": 69}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/WorkerSecurityValidator.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/WorkerSecurityValidator.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/AuditTests.spec.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/AuditTests.spec.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/AuditTests.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/AuditTests.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/WorkerSecurity.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/WorkerSecurity.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 28}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/XSSValidator.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/XSSValidator.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/utils.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/__tests__/utils.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/constants/security.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/security/constants/security.ts", "statementMap": {"0": {"start": {"line": 5, "column": 40}, "end": {"line": 16, "column": 1}}, "1": {"start": {"line": 18, "column": 31}, "end": {"line": 24, "column": 1}}, "2": {"start": {"line": 26, "column": 33}, "end": {"line": 37, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/queryClient.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/queryClient.ts", "statementMap": {"0": {"start": {"line": 3, "column": 27}, "end": {"line": 13, "column": 2}}, "1": {"start": {"line": 9, "column": 34}, "end": {"line": 9, "column": 75}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 18}, "end": {"line": 9, "column": 19}}, "loc": {"start": {"line": 9, "column": 34}, "end": {"line": 9, "column": 75}}, "line": 9}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/SecurityAnalytics/SecurityAnalytics.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/SecurityAnalytics/SecurityAnalytics.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/AlertManager.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/AlertManager.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/CircuitBreaker.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/CircuitBreaker.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/FeatureFlags.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/FeatureFlags.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/LoginPage.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/LoginPage.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/NotificationClient.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/NotificationClient.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/RegisterPage.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/RegisterPage.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/SecuritySettings.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/SecuritySettings.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/TwoFactorSetup.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/TwoFactorSetup.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/Web3Service.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/Web3Service.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/authService.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/authService.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/exportService.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/exportService.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/fileService.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/__tests__/fileService.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/ai/AIAssistantService.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/ai/AIAssistantService.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/offline/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/services/offline/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/shared/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/shared/index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/simple-tests/useAuth.fixed.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/simple-tests/useAuth.fixed.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/store/messageStore.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/store/messageStore.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/store/selectors/__tests__/userSelectors.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/store/selectors/__tests__/userSelectors.test.ts", "statementMap": {"0": {"start": {"line": 10, "column": 0}, "end": {"line": 104, "column": 3}}, "1": {"start": {"line": 12, "column": 20}, "end": {"line": 42, "column": 3}}, "2": {"start": {"line": 44, "column": 2}, "end": {"line": 47, "column": 5}}, "3": {"start": {"line": 45, "column": 19}, "end": {"line": 45, "column": 47}}, "4": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 51}}, "5": {"start": {"line": 49, "column": 2}, "end": {"line": 52, "column": 5}}, "6": {"start": {"line": 50, "column": 19}, "end": {"line": 50, "column": 51}}, "7": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 30}}, "8": {"start": {"line": 54, "column": 2}, "end": {"line": 57, "column": 5}}, "9": {"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 47}}, "10": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 31}}, "11": {"start": {"line": 59, "column": 2}, "end": {"line": 62, "column": 5}}, "12": {"start": {"line": 60, "column": 19}, "end": {"line": 60, "column": 44}}, "13": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 33}}, "14": {"start": {"line": 64, "column": 2}, "end": {"line": 67, "column": 5}}, "15": {"start": {"line": 65, "column": 19}, "end": {"line": 65, "column": 51}}, "16": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 63}}, "17": {"start": {"line": 69, "column": 2}, "end": {"line": 73, "column": 5}}, "18": {"start": {"line": 70, "column": 26}, "end": {"line": 70, "column": 59}}, "19": {"start": {"line": 71, "column": 19}, "end": {"line": 71, "column": 43}}, "20": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 32}}, "21": {"start": {"line": 75, "column": 2}, "end": {"line": 85, "column": 5}}, "22": {"start": {"line": 76, "column": 32}, "end": {"line": 82, "column": 5}}, "23": {"start": {"line": 83, "column": 19}, "end": {"line": 83, "column": 54}}, "24": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 33}}, "25": {"start": {"line": 87, "column": 2}, "end": {"line": 97, "column": 5}}, "26": {"start": {"line": 88, "column": 32}, "end": {"line": 94, "column": 5}}, "27": {"start": {"line": 95, "column": 19}, "end": {"line": 95, "column": 61}}, "28": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 31}}, "29": {"start": {"line": 99, "column": 2}, "end": {"line": 103, "column": 5}}, "30": {"start": {"line": 100, "column": 32}, "end": {"line": 100, "column": 71}}, "31": {"start": {"line": 101, "column": 19}, "end": {"line": 101, "column": 49}}, "32": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 35}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 28}}, "loc": {"start": {"line": 10, "column": 33}, "end": {"line": 104, "column": 1}}, "line": 10}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 44, "column": 35}, "end": {"line": 44, "column": 36}}, "loc": {"start": {"line": 44, "column": 41}, "end": {"line": 47, "column": 3}}, "line": 44}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 49, "column": 44}, "end": {"line": 49, "column": 45}}, "loc": {"start": {"line": 49, "column": 50}, "end": {"line": 52, "column": 3}}, "line": 49}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 54, "column": 37}, "end": {"line": 54, "column": 38}}, "loc": {"start": {"line": 54, "column": 43}, "end": {"line": 57, "column": 3}}, "line": 54}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 59, "column": 32}, "end": {"line": 59, "column": 33}}, "loc": {"start": {"line": 59, "column": 38}, "end": {"line": 62, "column": 3}}, "line": 59}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 64, "column": 39}, "end": {"line": 64, "column": 40}}, "loc": {"start": {"line": 64, "column": 45}, "end": {"line": 67, "column": 3}}, "line": 64}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 69, "column": 44}, "end": {"line": 69, "column": 45}}, "loc": {"start": {"line": 69, "column": 50}, "end": {"line": 73, "column": 3}}, "line": 69}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 75, "column": 60}, "end": {"line": 75, "column": 61}}, "loc": {"start": {"line": 75, "column": 66}, "end": {"line": 85, "column": 3}}, "line": 75}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 87, "column": 76}, "end": {"line": 87, "column": 77}}, "loc": {"start": {"line": 87, "column": 82}, "end": {"line": 97, "column": 3}}, "line": 87}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 99, "column": 60}, "end": {"line": 99, "column": 61}}, "loc": {"start": {"line": 99, "column": 66}, "end": {"line": 103, "column": 3}}, "line": 99}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/styles/theme.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/styles/theme.ts", "statementMap": {"0": {"start": {"line": 5, "column": 16}, "end": {"line": 68, "column": 1}}, "1": {"start": {"line": 71, "column": 19}, "end": {"line": 140, "column": 1}}, "2": {"start": {"line": 143, "column": 14}, "end": {"line": 172, "column": 2}}, "3": {"start": {"line": 175, "column": 33}, "end": {"line": 201, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/AdaptiveBandwidth.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/AdaptiveBandwidth.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 10, "column": 3}}, "1": {"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 5}}, "2": {"start": {"line": 8, "column": 4}, "end": {"line": 8, "column": 28}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 38}}, "loc": {"start": {"line": 5, "column": 43}, "end": {"line": 10, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 26}}, "loc": {"start": {"line": 7, "column": 31}, "end": {"line": 9, "column": 3}}, "line": 7}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/sample.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/sample.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setupAccessibilityTests.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setupAccessibilityTests.ts", "statementMap": {"0": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 34}}, "1": {"start": {"line": 9, "column": 0}, "end": {"line": 11, "column": 3}}, "2": {"start": {"line": 14, "column": 18}, "end": {"line": 32, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setupAxe.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setupAxe.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setupTests.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setupTests.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/simple-component.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/simple-component.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/AccessibilityTests.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/AccessibilityTests.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/BookingAccessibility.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/BookingAccessibility.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/Button.a11y.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/Button.a11y.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/CalendarAccessibility.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/CalendarAccessibility.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/ComponentAccessibility.template.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/ComponentAccessibility.template.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/FormAccessibility.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/FormAccessibility.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/NavigationAccessibility.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/NavigationAccessibility.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/a11y.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/a11y.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/axe-setup.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/axe-setup.ts", "statementMap": {"0": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 34}}, "1": {"start": {"line": 9, "column": 25}, "end": {"line": 33, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/axe.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/axe.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/button.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/button.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/form.a11y.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/accessibility/form.a11y.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/BookingForm.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/BookingForm.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/Button.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/Button.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 7, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 28}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 30}}, "loc": {"start": {"line": 5, "column": 35}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 26}}, "loc": {"start": {"line": 7, "column": 31}, "end": {"line": 11, "column": 3}}, "line": 7}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/LoginForm.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/LoginForm.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/ProductList.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/ProductList.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/UserProfile.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/components/UserProfile.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/config/loadTestConfig.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/config/loadTestConfig.ts", "statementMap": {"0": {"start": {"line": 1, "column": 23}, "end": {"line": 36, "column": 1}}, "1": {"start": {"line": 38, "column": 25}, "end": {"line": 89, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.load.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.load.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.perf.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.perf.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.security.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.security.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/dashboard/__tests__/TestDashboard.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/e2e/critical-paths.cy.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/e2e/critical-paths.cy.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/edge-cases/FormValidation.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/edge-cases/FormValidation.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/edge-cases/validation.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/edge-cases/validation.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/hooks/useAuth.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/hooks/useAuth.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 23}}, "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 30}}, "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/integration/Auth.integration.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/integration/Auth.integration.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/integration/BookingFlow.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/integration/BookingFlow.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/integration/CalendarFlow.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/integration/CalendarFlow.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/load/LoadTests.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/load/LoadTests.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/monitoring/TestMonitor.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/monitoring/TestMonitor.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/ComponentLoadTest.perf.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/ComponentLoadTest.perf.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/PerformanceTests.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/PerformanceTests.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/PerformanceTests.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/PerformanceTests.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/list.perf.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/list.perf.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/metrics.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/performance/metrics.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/recommendation/CollaborativeFilteringModel.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/recommendation/CollaborativeFilteringModel.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/recommendation/ContentBasedModel.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/recommendation/ContentBasedModel.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/recommendation/RecommendationService.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/recommendation/RecommendationService.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/DependencyVulnerabilityTests.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/DependencyVulnerabilityTests.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/DoSProtectionTests.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/DoSProtectionTests.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/JWTSecurity.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/JWTSecurity.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/LoggingMonitoringTests.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/LoggingMonitoringTests.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SSRFTests.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SSRFTests.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityAnalyzer.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityAnalyzer.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityAnalyzer.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityAnalyzer.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityHeaders.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityHeaders.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityTests.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/SecurityTests.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/StorageSecurity.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/StorageSecurity.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/UserEnumerationTests.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/UserEnumerationTests.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/VulnerabilityDetector.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/VulnerabilityDetector.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/validation.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/validation.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/APISecurityTests.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/APISecurityTests.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/AuthorizationSecurity.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/AuthorizationSecurity.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/FileUploadSecurity.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/FileUploadSecurity.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/LoginFormSecurity.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/LoginFormSecurity.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/OpenRedirectSecurity.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/OpenRedirectSecurity.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/StateManagementSecurity.test.tsx": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/security/examples/StateManagementSecurity.test.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/authService.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/authService.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/exportService.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/exportService.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/testData.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/testData.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/userService.msw.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/userService.msw.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/userService.test.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/services/userService.test.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setup/setupAccessibilityTests.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setup/setupAccessibilityTests.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setup/setupApiMocks.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setup/setupApiMocks.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setup/setupPerformanceTests.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/setup/setupPerformanceTests.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/utils/a11y-helpers.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/utils/a11y-helpers.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/utils/accessibility-helpers.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/tests/utils/accessibility-helpers.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 5, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 33}, "end": {"line": 8, "column": 34}}, "loc": {"start": {"line": 8, "column": 39}, "end": {"line": 11, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/theme/index.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/theme/index.ts", "statementMap": {"0": {"start": {"line": 3, "column": 21}, "end": {"line": 36, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/theme/themeConfig.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/theme/themeConfig.ts", "statementMap": {"0": {"start": {"line": 3, "column": 26}, "end": {"line": 25, "column": 2}}, "1": {"start": {"line": 27, "column": 25}, "end": {"line": 49, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/utils/config.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/utils/config.ts", "statementMap": {"0": {"start": {"line": 7, "column": 0}, "end": {"line": 21, "column": 3}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/Users/<USER>/Desktop/Projet-RB2/frontend/src/utils/pdfUtils.ts": {"path": "/Users/<USER>/Desktop/Projet-RB2/frontend/src/utils/pdfUtils.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}}