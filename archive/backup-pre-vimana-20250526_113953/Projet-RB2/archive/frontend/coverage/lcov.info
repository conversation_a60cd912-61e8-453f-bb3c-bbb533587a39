TN:
SF:src/api.ts
FN:10,(anonymous_0)
FN:21,(anonymous_1)
FN:31,(anonymous_2)
FNF:3
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
DA:3,0
DA:10,0
DA:11,0
DA:12,0
DA:13,0
DA:18,0
DA:21,0
DA:22,0
DA:28,0
DA:31,0
DA:32,0
DA:38,0
LF:12
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/index.tsx
FNF:0
FNH:0
DA:11,0
DA:13,0
DA:14,0
DA:18,0
DA:21,0
DA:24,0
DA:37,0
LF:7
LH:0
BRDA:13,0,0,0
BRDA:13,0,1,0
BRF:2
BRH:0
end_of_record
TN:
SF:src/main.tsx
FN:38,(anonymous_0)
FN:47,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:21,0
DA:23,0
DA:24,0
DA:26,0
DA:38,0
DA:40,0
DA:41,0
DA:43,0
DA:47,0
LF:9
LH:0
BRDA:24,0,0,0
BRDA:24,0,1,0
BRDA:40,1,0,0
BRDA:40,1,1,0
BRF:4
BRH:0
end_of_record
TN:
SF:src/setupTests.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/__tests__/setup.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/__tests__/test-utils.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/__tests__/components/Button.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/__tests__/hooks/usePerformance.test.ts
FN:4,(anonymous_0)
FN:7,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:4,0
DA:7,0
DA:9,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/__tests__/schemas/offlineDataSchema.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/assets/icons/index.tsx
FN:4,(anonymous_0)
FN:27,(anonymous_1)
FN:48,(anonymous_2)
FNF:3
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
DA:4,0
DA:5,0
DA:27,0
DA:28,0
DA:48,0
DA:49,0
LF:6
LH:0
BRDA:4,0,0,0
BRDA:4,1,0,0
BRDA:27,2,0,0
BRDA:27,3,0,0
BRDA:48,4,0,0
BRDA:48,5,0,0
BRF:6
BRH:0
end_of_record
TN:
SF:src/atomic/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/lazyComponents.ts
FN:4,(anonymous_0)
FN:5,(anonymous_1)
FN:6,(anonymous_2)
FN:7,(anonymous_3)
FN:8,(anonymous_4)
FN:9,(anonymous_5)
FN:10,(anonymous_6)
FN:11,(anonymous_7)
FN:12,(anonymous_8)
FN:13,(anonymous_9)
FN:16,(anonymous_10)
FN:17,(anonymous_11)
FN:18,(anonymous_12)
FN:19,(anonymous_13)
FN:22,(anonymous_14)
FN:23,(anonymous_15)
FN:24,(anonymous_16)
FN:25,(anonymous_17)
FN:26,(anonymous_18)
FN:27,(anonymous_19)
FNF:20
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
FNDA:0,(anonymous_13)
FNDA:0,(anonymous_14)
FNDA:0,(anonymous_15)
FNDA:0,(anonymous_16)
FNDA:0,(anonymous_17)
FNDA:0,(anonymous_18)
FNDA:0,(anonymous_19)
DA:4,0
DA:5,0
DA:6,0
DA:7,0
DA:8,0
DA:9,0
DA:10,0
DA:11,0
DA:12,0
DA:13,0
DA:16,0
DA:17,0
DA:18,0
DA:19,0
DA:22,0
DA:23,0
DA:24,0
DA:25,0
DA:26,0
DA:27,0
LF:20
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/__tests__/templates/ComponentTestTemplate.tsx
FN:18,(anonymous_0)
FN:20,(anonymous_1)
FN:30,(anonymous_2)
FN:31,(anonymous_3)
FN:36,(anonymous_4)
FN:44,(anonymous_5)
FN:45,(anonymous_6)
FN:50,(anonymous_7)
FN:56,(anonymous_8)
FN:64,(anonymous_9)
FN:65,(anonymous_10)
FN:73,(anonymous_11)
FN:84,(anonymous_12)
FN:85,(anonymous_13)
FN:96,(anonymous_14)
FN:105,(anonymous_15)
FN:114,(anonymous_16)
FN:115,(anonymous_17)
FN:120,(anonymous_18)
FN:125,(anonymous_19)
FNF:20
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
FNDA:0,(anonymous_13)
FNDA:0,(anonymous_14)
FNDA:0,(anonymous_15)
FNDA:0,(anonymous_16)
FNDA:0,(anonymous_17)
FNDA:0,(anonymous_18)
FNDA:0,(anonymous_19)
DA:18,0
DA:20,0
DA:21,0
DA:30,0
DA:31,0
DA:32,0
DA:33,0
DA:36,0
DA:37,0
DA:38,0
DA:39,0
DA:44,0
DA:45,0
DA:50,0
DA:56,0
DA:64,0
DA:65,0
DA:73,0
DA:84,0
DA:85,0
DA:96,0
DA:105,0
DA:114,0
DA:115,0
DA:116,0
DA:117,0
DA:120,0
DA:125,0
LF:28
LH:0
BRDA:20,0,0,0
BRF:1
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Badge/Badge.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Badge/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Button/Button.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Button/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Checkbox/Checkbox.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Checkbox/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Icon/Icon.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Icon/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Image/Image.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Image/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Input/Input.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Input/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Label/Label.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Label/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Link/Link.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Link/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Radio/Radio.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Radio/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Select/Select.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Select/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Spinner/Spinner.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Spinner/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Typography/Typography.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/Typography/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/form/form.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/atoms/form/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/examples/App.tsx
FN:19,(anonymous_0)
FN:27,(anonymous_1)
FN:47,(anonymous_2)
FN:55,(anonymous_3)
FN:55,(anonymous_4)
FNF:5
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
DA:19,0
DA:20,0
DA:27,0
DA:28,0
DA:47,0
DA:48,0
DA:50,0
DA:55,0
LF:8
LH:0
BRDA:58,0,0,0
BRDA:58,0,1,0
BRDA:61,1,0,0
BRDA:61,1,1,0
BRF:4
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Alert/Alert.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Alert/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Avatar/Avatar.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Avatar/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Breadcrumb/Breadcrumb.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Breadcrumb/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Card/Card.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Card/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Container/Container.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Container/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/DatePicker/DatePicker.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/DatePicker/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Dropdown/Dropdown.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Dropdown/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/FileUpload/FileUpload.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/FileUpload/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/FormField/FormField.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/FormField/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/IconLink/IconLink.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/IconLink/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/NavigationCarousel/NavigationCarousel.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/NavigationCarousel/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Pagination/Pagination.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Pagination/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/PasswordInput/PasswordInput.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/PasswordInput/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Price/Price.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Price/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Progress/Progress.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Progress/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/SearchField/SearchField.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/SearchField/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Skeleton/Skeleton.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Skeleton/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/TabGroup/TabGroup.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/TabGroup/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/TimePicker/TimePicker.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/TimePicker/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Toast/Toast.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Toast/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Tooltip/Tooltip.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/molecules/Tooltip/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/organisms.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/BookingForm/BookingForm.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/BookingForm/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Footer/Footer.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Footer/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Header/Header.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Header/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Hero/Hero.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Hero/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/LoginForm/LoginForm.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/LoginForm/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Navigation/Navigation.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Navigation/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/NotificationCenter/NotificationCenter.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/NotificationCenter/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/PaymentForm/PaymentForm.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/PaymentForm/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/RetreatCard/RetreatCard.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/RetreatCard/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/RetreatFilters/RetreatFilters.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/RetreatFilters/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/ReviewForm/ReviewForm.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/ReviewForm/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/SearchBar/SearchBar.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/SearchBar/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/SearchSection/SearchSection.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/SearchSection/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Sidebar/Sidebar.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/Sidebar/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/UserProfile/UserProfile.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/organisms/UserProfile/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/CheckoutPage/CheckoutPage.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/CheckoutPage/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/HomePage/HomePage.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/HomePage/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/LoginPage/LoginPage.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/LoginPage/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/RetreatDetailPage/RetreatDetailPage.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/RetreatDetailPage/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/RetreatListPage/RetreatListPage.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/RetreatListPage/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/UserDashboardPage/UserDashboardPage.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/pages/UserDashboardPage/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/routes/index.ts
FN:9,(anonymous_0)
FN:17,(anonymous_1)
FN:23,(anonymous_2)
FN:27,(anonymous_3)
FN:33,(anonymous_4)
FN:37,(anonymous_5)
FN:43,(anonymous_6)
FN:47,(anonymous_7)
FN:51,(anonymous_8)
FN:57,(anonymous_9)
FN:63,(anonymous_10)
FNF:11
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
DA:5,0
DA:9,0
DA:17,0
DA:23,0
DA:27,0
DA:33,0
DA:37,0
DA:43,0
DA:47,0
DA:51,0
DA:57,0
DA:63,0
LF:12
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/templates/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/templates/AuthLayout/AuthLayout.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/templates/AuthLayout/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/templates/DashboardLayout/DashboardLayout.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/templates/DashboardLayout/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/templates/MainLayout/MainLayout.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/templates/MainLayout/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/templates/MarketplaceLayout/MarketplaceLayout.styles.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/templates/MarketplaceLayout/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/theme/animations.ts
FNF:0
FNH:0
DA:3,0
DA:63,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/theme/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/atomic/theme/tokens.ts
FNF:0
FNH:0
DA:1,0
DA:60,0
DA:81,0
DA:115,0
DA:124,0
DA:132,0
DA:150,0
DA:161,0
LF:8
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/AdminSidebar.tsx
FN:2,AdminSidebar
FNF:1
FNH:0
FNDA:0,AdminSidebar
DA:3,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/App.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/TaskList.tsx
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/Admin/AdminBookingList.tsx
FN:7,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:7,0
DA:8,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/Button/Button.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/Calendar/__tests__/EventCalendar.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/Calendar/__tests__/EventForm.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/Header/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/Notification/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/SecurityAlert/SecurityAlert.a11y.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/__tests__/Button.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/a11y/Announcer.tsx
FN:4,Announcer
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,Announcer
FNDA:0,(anonymous_1)
DA:5,0
DA:6,0
DA:8,0
DA:9,0
DA:10,0
DA:14,0
LF:6
LH:0
BRDA:9,0,0,0
BRDA:9,0,1,0
BRF:2
BRH:0
end_of_record
TN:
SF:src/components/a11y/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/a11y/Dialog/Dialog.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/accessibility/index.tsx
FNF:0
FNH:0
DA:7,0
DA:14,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/accessible/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/analytics/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/atoms/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/atoms/Button/AccessibleButton.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/atoms/Button/Button.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/atoms/Input/Input.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/atoms/Input/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/auth/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/auth/__tests__/AuthComponent.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/auth/__tests__/LoginForm.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/auth/__tests__/LoginPage.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/auth/__tests__/PrivateRoute.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/auth/__tests__/RegisterPage.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/auth/__tests__/ResetPasswordForm.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/auth/__tests__/Verify2FAForm.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/booking/GroupBooking.tsx
FNF:0
FNH:0
DA:1,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/common/AccessibilityTest.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/common/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/common/Form/__tests__/Input.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/common/__tests__/accessibility.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/community/__tests__/CreatePost.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/community/__tests__/Post.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/dashboard/__tests__/AnalyticsCard.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/dashboard/__tests__/AnalyticsDashboard.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/invoice/InvoiceGenerator.tsx
FNF:0
FNH:0
DA:1,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/layout/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/layout/SearchBar/__tests__/SearchBar.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/layout/__tests__/Layout.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/layout/__tests__/Sidebar.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/messaging/__tests__/MessageInput.test.tsx
FN:7,(anonymous_0)
FN:8,(anonymous_1)
FN:9,(anonymous_2)
FN:10,(anonymous_3)
FN:11,(anonymous_4)
FN:14,(anonymous_5)
FN:17,(anonymous_6)
FN:21,(anonymous_7)
FN:26,(anonymous_8)
FN:39,(anonymous_9)
FN:48,(anonymous_10)
FN:62,(anonymous_11)
FNF:12
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
FNDA:0,(anonymous_11)
DA:7,0
DA:8,0
DA:9,0
DA:10,0
DA:11,0
DA:14,0
DA:15,0
DA:17,0
DA:18,0
DA:21,0
DA:22,0
DA:23,0
DA:26,0
DA:27,0
DA:29,0
DA:30,0
DA:32,0
DA:33,0
DA:35,0
DA:36,0
DA:39,0
DA:40,0
DA:42,0
DA:43,0
DA:45,0
DA:48,0
DA:49,0
DA:51,0
DA:52,0
DA:54,0
DA:56,0
DA:57,0
DA:59,0
DA:62,0
DA:63,0
DA:65,0
DA:66,0
DA:68,0
DA:71,0
DA:74,0
DA:75,0
DA:78,0
LF:42
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/molecules/Navigation/AccessibleNavigation.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/molecules/Table/AccessibleTable.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/navigation/__tests__/NavItem.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/nft/__tests__/NFTGallery.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/profile/__tests__/ProfileSettings.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/pwa/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/retreats/__tests__/RetreatFilters.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/retreats/__tests__/RetreatList.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/routing/__tests__/NavigationTransition.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/security/__tests__/SecuritySettings.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/security/__tests__/TwoFactorSetup.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/settings/AppearanceSettings.tsx
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/social/Poll/__tests__/PollComponent.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/social/__tests__/SocialFeed.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/ui/demo.tsx
FN:4,DemoBackgroundPaths
FNF:1
FNH:0
FNDA:0,DemoBackgroundPaths
DA:5,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/components/ui/__tests__/OptimizedImage.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/config/monitoring.ts
FN:5,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:5,0
DA:7,0
DA:10,0
DA:13,0
DA:16,0
LF:5
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/config/performance.ts
FNF:0
FNH:0
DA:1,0
DA:27,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/containers/Financial/pages/DashboardPage.tsx
FN:3,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:3,0
DA:4,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/containers/Financial/pages/PaymentMethodsPage.tsx
FN:3,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:3,0
DA:4,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/containers/Financial/pages/TransactionHistoryPage.tsx
FN:3,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:3,0
DA:4,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/contexts/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/contracts/test/RetreatBooking.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/core/api/__tests__/apiClient.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/features/analyses/components/__tests__/AnalysisTable.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/features/auth/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/features/auth/__tests__/AuthContext.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/features/auth/components/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/features/marketplace/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/features/marketplace/components/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/features/security/SecurityDashboard.integration.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/features/security/SecurityDashboard.perf.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/useAdaptiveCache.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/useAuth.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/useAuth.ts
FN:5,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:5,0
DA:6,0
DA:8,0
DA:9,0
DA:12,0
LF:5
LH:0
BRDA:8,0,0,0
BRDA:8,0,1,0
BRF:2
BRH:0
end_of_record
TN:
SF:src/hooks/useNotificationSystem.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/useActivity.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/useBreakpoints.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/useCommunityEngagement.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/useCommunityInteractions.test.ts
FN:4,(anonymous_0)
FN:7,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:4,0
DA:7,0
DA:9,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/useForm.test.ts
FN:4,(anonymous_0)
FN:7,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:4,0
DA:7,0
DA:9,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/useLayoutState.test.ts
FN:4,(anonymous_0)
FN:7,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:4,0
DA:7,0
DA:9,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/useNotifications.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/usePoll.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/useScrollPosition.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/__tests__/useSocialFeed.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/hooks/synchronization/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/i18n/i18n.ts
FNF:0
FNH:0
DA:7,0
DA:10,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/lib/react-query.ts
FNF:0
FNH:0
DA:4,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/middleware/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/middleware/__tests__/authMiddleware.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/monitoring/dashboard/config.ts
FNF:0
FNH:0
DA:1,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/monitoring/tests/MonitoringSystem.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/NftGallery/nft/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/Public/HomePage.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/Public/LoginPage.tsx
FN:2,LoginPage
FNF:1
FNH:0
FNDA:0,LoginPage
DA:3,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/Public/UnauthorizedPage.tsx
FN:2,UnauthorizedPage
FNF:1
FNH:0
FNDA:0,UnauthorizedPage
DA:3,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/Public/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/admin/AdminPage.tsx
FN:2,AdminPage
FNF:1
FNH:0
FNDA:0,AdminPage
DA:3,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/auth/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/error/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/error/__tests__/Error404Page.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/nft/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/partner/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/services/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/pages/social/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/routes/__tests__/ProtectedRoute.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/schemas/validation/index.ts
FNF:0
FNH:0
DA:4,0
DA:5,0
DA:12,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/security/WorkerSecurityValidator.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/security/__tests__/AuditTests.spec.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/security/__tests__/AuditTests.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/security/__tests__/WorkerSecurity.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/security/__tests__/XSSValidator.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/security/__tests__/utils.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/security/constants/security.ts
FNF:0
FNH:0
DA:5,0
DA:18,0
DA:26,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/queryClient.ts
FN:9,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:3,0
DA:9,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/SecurityAnalytics/SecurityAnalytics.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/AlertManager.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/CircuitBreaker.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/FeatureFlags.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/LoginPage.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/NotificationClient.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/RegisterPage.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/SecuritySettings.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/TwoFactorSetup.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/Web3Service.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/authService.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/exportService.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/__tests__/fileService.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/ai/AIAssistantService.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/services/offline/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/shared/index.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/simple-tests/useAuth.fixed.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/store/messageStore.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/store/selectors/__tests__/userSelectors.test.ts
FN:10,(anonymous_0)
FN:44,(anonymous_1)
FN:49,(anonymous_2)
FN:54,(anonymous_3)
FN:59,(anonymous_4)
FN:64,(anonymous_5)
FN:69,(anonymous_6)
FN:75,(anonymous_7)
FN:87,(anonymous_8)
FN:99,(anonymous_9)
FNF:10
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
DA:10,0
DA:12,0
DA:44,0
DA:45,0
DA:46,0
DA:49,0
DA:50,0
DA:51,0
DA:54,0
DA:55,0
DA:56,0
DA:59,0
DA:60,0
DA:61,0
DA:64,0
DA:65,0
DA:66,0
DA:69,0
DA:70,0
DA:71,0
DA:72,0
DA:75,0
DA:76,0
DA:83,0
DA:84,0
DA:87,0
DA:88,0
DA:95,0
DA:96,0
DA:99,0
DA:100,0
DA:101,0
DA:102,0
LF:33
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/styles/theme.ts
FNF:0
FNH:0
DA:5,0
DA:71,0
DA:143,0
DA:175,0
LF:4
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/AdaptiveBandwidth.test.tsx
FN:5,(anonymous_0)
FN:7,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:7,0
DA:8,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/sample.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/setupAccessibilityTests.ts
FNF:0
FNH:0
DA:6,0
DA:9,0
DA:14,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/setupAxe.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/setupTests.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/simple-component.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/AccessibilityTests.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/BookingAccessibility.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/Button.a11y.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/CalendarAccessibility.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/ComponentAccessibility.template.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/FormAccessibility.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/NavigationAccessibility.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/a11y.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/axe-setup.ts
FNF:0
FNH:0
DA:6,0
DA:9,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/axe.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/button.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/accessibility/form.a11y.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/components/BookingForm.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/components/Button.test.tsx
FN:5,(anonymous_0)
FN:7,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:7,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/components/LoginForm.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/components/ProductList.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/components/UserProfile.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/config/loadTestConfig.ts
FNF:0
FNH:0
DA:1,0
DA:38,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/dashboard/__tests__/TestDashboard.load.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/dashboard/__tests__/TestDashboard.perf.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/dashboard/__tests__/TestDashboard.security.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/dashboard/__tests__/TestDashboard.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/e2e/critical-paths.cy.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/edge-cases/FormValidation.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/edge-cases/validation.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/hooks/useAuth.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/integration/Auth.integration.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/integration/BookingFlow.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/integration/CalendarFlow.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/load/LoadTests.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/monitoring/TestMonitor.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/performance/ComponentLoadTest.perf.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/performance/PerformanceTests.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/performance/PerformanceTests.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/performance/list.perf.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/performance/metrics.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/recommendation/CollaborativeFilteringModel.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/recommendation/ContentBasedModel.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/recommendation/RecommendationService.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/DependencyVulnerabilityTests.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/DoSProtectionTests.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/JWTSecurity.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/LoggingMonitoringTests.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/SSRFTests.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/SecurityAnalyzer.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/SecurityAnalyzer.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/SecurityHeaders.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/SecurityTests.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/StorageSecurity.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/UserEnumerationTests.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/VulnerabilityDetector.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/validation.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/examples/APISecurityTests.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/examples/AuthorizationSecurity.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/examples/FileUploadSecurity.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/examples/LoginFormSecurity.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/examples/OpenRedirectSecurity.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/security/examples/StateManagementSecurity.test.tsx
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/services/authService.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/services/exportService.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/services/testData.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/services/userService.msw.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/services/userService.test.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/setup/setupAccessibilityTests.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/setup/setupApiMocks.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/setup/setupPerformanceTests.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/utils/a11y-helpers.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/tests/utils/accessibility-helpers.ts
FN:5,(anonymous_0)
FN:8,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:5,0
DA:8,0
DA:10,0
LF:3
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/theme/index.ts
FNF:0
FNH:0
DA:3,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/theme/themeConfig.ts
FNF:0
FNH:0
DA:3,0
DA:27,0
LF:2
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/utils/config.ts
FNF:0
FNH:0
DA:7,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/utils/pdfUtils.ts
FNF:0
FNH:0
LF:0
LH:0
BRF:0
BRH:0
end_of_record
