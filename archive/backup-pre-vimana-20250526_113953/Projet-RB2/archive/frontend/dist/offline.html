<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="theme-color" content="#4f46e5">
  <title>Offline - Retreat Pro</title>
  <link rel="manifest" href="/manifest.json">
  <link rel="icon" href="/favicon.ico">
  <style>
    body {
      font-family: 'Roboto', sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background-color: #f9fafb;
      color: #111827;
      text-align: center;
    }
    .container {
      max-width: 600px;
      padding: 2rem;
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    h1 {
      color: #4f46e5;
      margin-bottom: 1rem;
    }
    p {
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }
    .icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: #4f46e5;
    }
    .button {
      display: inline-block;
      background-color: #4f46e5;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.2s;
    }
    .button:hover {
      background-color: #4338ca;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📶</div>
    <h1>You're Offline</h1>
    <p>It looks like you've lost your internet connection. Some features may be unavailable until you're back online.</p>
    <p>Don't worry! You can still access previously loaded content and perform certain actions that will sync once you're connected again.</p>
    <a href="/" class="button">Try Again</a>
  </div>
  <script>
    // Check if we're back online and reload the page
    window.addEventListener('online', () => {
      window.location.reload();
    });
    
    // Add event listener for the Try Again button
    document.querySelector('.button').addEventListener('click', (e) => {
      e.preventDefault();
      window.location.reload();
    });
  </script>
</body>
</html>
