// Service Worker with Workbox
importScripts('https://storage.googleapis.com/workbox-cdn/releases/6.5.4/workbox-sw.js');

// Configuration
const APP_VERSION = '1.1.0';
const DEBUG = false;
const CACHE_PREFIX = 'retreat-pro';

// Set workbox config
workbox.setConfig({
  debug: DEBUG
});

// Custom log function
const log = DEBUG ? console.log.bind(console) : () => {};

// Cache names
const CACHE_NAMES = {
  static: `${CACHE_PREFIX}-static-${APP_VERSION}`,
  images: `${CACHE_PREFIX}-images-${APP_VERSION}`,
  fonts: `${CACHE_PREFIX}-fonts-${APP_VERSION}`,
  api: `${CACHE_PREFIX}-api-${APP_VERSION}`,
  pages: `${CACHE_PREFIX}-pages-${APP_VERSION}`,
  offline: `${CACHE_PREFIX}-offline-${APP_VERSION}`,
  documents: `${CACHE_PREFIX}-documents-${APP_VERSION}`,
  media: `${CACHE_PREFIX}-media-${APP_VERSION}`
};

// Queue names for background sync
const SYNC_QUEUES = {
  apiRequests: 'api-requests-queue',
  userActions: 'user-actions-queue',
  mediaUploads: 'media-uploads-queue',
  formSubmissions: 'form-submissions-queue'
};

// Precache static assets
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/offline.html',
  '/manifest.json',
  '/favicon.ico',
  '/static/css/main.css',
  '/static/js/main.js',
  '/static/js/bundle.js',
  '/static/js/vendors.js'
];

// Precache offline page
const OFFLINE_PAGE = '/offline.html';

// Skip waiting and claim clients
self.addEventListener('install', event => {
  log('Service Worker: Installed');
  event.waitUntil(
    Promise.all([
      caches.open(CACHE_NAMES.offline).then(cache => {
        log('Service Worker: Caching offline page');
        return cache.add(OFFLINE_PAGE);
      }),
      // Precache static assets
      caches.open(CACHE_NAMES.static).then(cache => {
        log('Service Worker: Precaching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      self.skipWaiting()
    ])
  );
});

self.addEventListener('activate', event => {
  log('Service Worker: Activated');
  
  // Clean up old caches
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames
          .filter(cacheName => cacheName.startsWith(CACHE_PREFIX) && 
                              !Object.values(CACHE_NAMES).includes(cacheName))
          .map(cacheName => {
            log(`Service Worker: Deleting old cache ${cacheName}`);
            return caches.delete(cacheName);
          })
      );
    }).then(() => self.clients.claim())
  );
});

// Workbox routing

// Cache the Google Fonts stylesheets with a stale-while-revalidate strategy
workbox.routing.registerRoute(
  /^https:\/\/fonts\.googleapis\.com/,
  new workbox.strategies.StaleWhileRevalidate({
    cacheName: CACHE_NAMES.fonts,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 30,
        maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
      }),
    ],
  })
);

// Cache the Google Fonts webfont files with a cache-first strategy for 1 year
workbox.routing.registerRoute(
  /^https:\/\/fonts\.gstatic\.com/,
  new workbox.strategies.CacheFirst({
    cacheName: CACHE_NAMES.fonts,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 30,
        maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
      }),
    ],
  })
);

// Cache images with a cache-first strategy
workbox.routing.registerRoute(
  /\.(?:png|jpg|jpeg|svg|gif|webp|avif)$/,
  new workbox.strategies.CacheFirst({
    cacheName: CACHE_NAMES.images,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 30 * 24 * 60 * 60, // 30 days
      }),
      new workbox.cacheableResponse.CacheableResponsePlugin({
        statuses: [0, 200]
      }),
    ],
  })
);

// Cache media files with a cache-first strategy
workbox.routing.registerRoute(
  /\.(?:mp4|webm|mp3|wav|ogg)$/,
  new workbox.strategies.CacheFirst({
    cacheName: CACHE_NAMES.media,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 14 * 24 * 60 * 60, // 14 days
      }),
      new workbox.cacheableResponse.CacheableResponsePlugin({
        statuses: [0, 200]
      }),
      new workbox.rangeRequests.RangeRequestsPlugin(),
    ],
  })
);

// Cache document files with a cache-first strategy
workbox.routing.registerRoute(
  /\.(?:pdf|docx|xlsx|pptx|txt)$/,
  new workbox.strategies.CacheFirst({
    cacheName: CACHE_NAMES.documents,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
      }),
      new workbox.cacheableResponse.CacheableResponsePlugin({
        statuses: [0, 200]
      }),
    ],
  })
);

// Cache CSS, JS, and Web Worker files with a stale-while-revalidate strategy
workbox.routing.registerRoute(
  /\.(?:js|css|mjs|wasm|worker\.js)$/,
  new workbox.strategies.StaleWhileRevalidate({
    cacheName: CACHE_NAMES.static,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
      }),
      new workbox.cacheableResponse.CacheableResponsePlugin({
        statuses: [0, 200]
      }),
    ],
  })
);

// Cache API requests with different strategies based on the endpoint
workbox.routing.registerRoute(
  /\/api\/static\//,
  new workbox.strategies.CacheFirst({
    cacheName: CACHE_NAMES.api,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
      }),
      new workbox.cacheableResponse.CacheableResponsePlugin({
        statuses: [0, 200]
      }),
    ],
  })
);

workbox.routing.registerRoute(
  /\/api\/data\//,
  new workbox.strategies.NetworkFirst({
    cacheName: CACHE_NAMES.api,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 100,
        maxAgeSeconds: 24 * 60 * 60, // 24 hours
      }),
      new workbox.cacheableResponse.CacheableResponsePlugin({
        statuses: [0, 200]
      }),
    ],
    networkTimeoutSeconds: 10
  })
);

workbox.routing.registerRoute(
  /\/api\/(?!static|data).*/,
  new workbox.strategies.StaleWhileRevalidate({
    cacheName: CACHE_NAMES.api,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 200,
        maxAgeSeconds: 12 * 60 * 60, // 12 hours
      }),
      new workbox.cacheableResponse.CacheableResponsePlugin({
        statuses: [0, 200]
      }),
    ],
  })
);

// Cache pages with a network-first strategy
workbox.routing.registerRoute(
  ({ request }) => request.mode === 'navigate',
  new workbox.strategies.NetworkFirst({
    cacheName: CACHE_NAMES.pages,
    plugins: [
      new workbox.expiration.ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 24 * 60 * 60, // 24 hours
      }),
      new workbox.cacheableResponse.CacheableResponsePlugin({
        statuses: [0, 200]
      }),
    ],
    networkTimeoutSeconds: 5
  })
);

// Fallback to offline page when network is unavailable
workbox.routing.setCatchHandler(({ event }) => {
  log('Service Worker: Fallback to offline page', event.request.url);
  
  if (event.request.mode === 'navigate') {
    return caches.match(OFFLINE_PAGE);
  }
  
  // Return appropriate error response based on request type
  if (event.request.destination === 'image') {
    return new Response(
      '<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg"><rect width="400" height="300" fill="#eee"/><text x="200" y="150" text-anchor="middle" dominant-baseline="middle" font-family="sans-serif" font-size="18" fill="#888">Image Offline</text></svg>',
      { headers: { 'Content-Type': 'image/svg+xml' } }
    );
  }
  
  return Response.error();
});

// Background sync queue configurations
const apiRequestsQueue = new workbox.backgroundSync.Queue(SYNC_QUEUES.apiRequests, {
  maxRetentionTime: 24 * 60, // Retry for up to 24 hours (specified in minutes)
  onSync: async ({ queue }) => {
    try {
      log('Syncing API requests queue');
      await queue.replayRequests();
      
      // Notify clients about successful sync
      self.clients.matchAll().then(clients => {
        for (const client of clients) {
          client.postMessage({
            type: 'SYNC_COMPLETED',
            queue: SYNC_QUEUES.apiRequests,
            timestamp: Date.now()
          });
        }
      });
    } catch (error) {
      log('Error syncing API requests:', error);
      // Retry will happen automatically based on maxRetentionTime
    }
  }
});

const userActionsQueue = new workbox.backgroundSync.Queue(SYNC_QUEUES.userActions, {
  maxRetentionTime: 48 * 60, // Retry for up to 48 hours
  onSync: async ({ queue }) => {
    try {
      log('Syncing user actions queue');
      await queue.replayRequests();
      
      // Notify clients
      self.clients.matchAll().then(clients => {
        for (const client of clients) {
          client.postMessage({
            type: 'SYNC_COMPLETED',
            queue: SYNC_QUEUES.userActions,
            timestamp: Date.now()
          });
        }
      });
    } catch (error) {
      log('Error syncing user actions:', error);
    }
  }
});

const mediaUploadsQueue = new workbox.backgroundSync.Queue(SYNC_QUEUES.mediaUploads, {
  maxRetentionTime: 72 * 60, // Retry for up to 72 hours
  onSync: async ({ queue }) => {
    try {
      log('Syncing media uploads queue');
      await queue.replayRequests();
      
      // Notify clients
      self.clients.matchAll().then(clients => {
        for (const client of clients) {
          client.postMessage({
            type: 'SYNC_COMPLETED',
            queue: SYNC_QUEUES.mediaUploads,
            timestamp: Date.now()
          });
        }
      });
    } catch (error) {
      log('Error syncing media uploads:', error);
    }
  }
});

const formSubmissionsQueue = new workbox.backgroundSync.Queue(SYNC_QUEUES.formSubmissions, {
  maxRetentionTime: 24 * 60, // Retry for up to 24 hours
  onSync: async ({ queue }) => {
    try {
      log('Syncing form submissions queue');
      await queue.replayRequests();
      
      // Notify clients
      self.clients.matchAll().then(clients => {
        for (const client of clients) {
          client.postMessage({
            type: 'SYNC_COMPLETED',
            queue: SYNC_QUEUES.formSubmissions,
            timestamp: Date.now()
          });
        }
      });
    } catch (error) {
      log('Error syncing form submissions:', error);
    }
  }
});

// Register route handlers for different API endpoints with appropriate queue assignment
workbox.routing.registerRoute(
  ({url}) => url.pathname.startsWith('/api/actions/'),
  new workbox.strategies.NetworkOnly({
    plugins: [userActionsQueue.createPlugin()]
  }),
  'POST'
);

workbox.routing.registerRoute(
  ({url}) => url.pathname.startsWith('/api/forms/'),
  new workbox.strategies.NetworkOnly({
    plugins: [formSubmissionsQueue.createPlugin()]
  }),
  'POST'
);

workbox.routing.registerRoute(
  ({url}) => url.pathname.startsWith('/api/media/'),
  new workbox.strategies.NetworkOnly({
    plugins: [mediaUploadsQueue.createPlugin()]
  }),
  'POST'
);

// Default API requests queue for all other API endpoints
workbox.routing.registerRoute(
  ({url}) => url.pathname.startsWith('/api/'),
  new workbox.strategies.NetworkOnly({
    plugins: [apiRequestsQueue.createPlugin()]
  }),
  'POST'
);

// Also handle PUT and DELETE methods
workbox.routing.registerRoute(
  ({url}) => url.pathname.startsWith('/api/'),
  new workbox.strategies.NetworkOnly({
    plugins: [apiRequestsQueue.createPlugin()]
  }),
  'PUT'
);

workbox.routing.registerRoute(
  ({url}) => url.pathname.startsWith('/api/'),
  new workbox.strategies.NetworkOnly({
    plugins: [apiRequestsQueue.createPlugin()]
  }),
  'DELETE'
);

// Manual sync trigger handler (responds to messages from clients)
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SYNC_NOW') {
    log('Manual sync triggered');
    
    if (event.data.queue) {
      // Sync specific queue
      const queueName = event.data.queue;
      
      switch (queueName) {
        case SYNC_QUEUES.apiRequests:
          apiRequestsQueue.replayRequests();
          break;
        case SYNC_QUEUES.userActions:
          userActionsQueue.replayRequests();
          break;
        case SYNC_QUEUES.mediaUploads:
          mediaUploadsQueue.replayRequests();
          break;
        case SYNC_QUEUES.formSubmissions:
          formSubmissionsQueue.replayRequests();
          break;
        default:
          log('Unknown queue:', queueName);
      }
    } else {
      // Sync all queues
      Promise.all([
        apiRequestsQueue.replayRequests(),
        userActionsQueue.replayRequests(),
        mediaUploadsQueue.replayRequests(),
        formSubmissionsQueue.replayRequests()
      ]).then(() => {
        log('All queues synced');
        
        // Notify clients
        self.clients.matchAll().then(clients => {
          for (const client of clients) {
            client.postMessage({
              type: 'ALL_SYNC_COMPLETED',
              timestamp: Date.now()
            });
          }
        });
      }).catch(error => {
        log('Error syncing all queues:', error);
      });
    }
  }
  
  // Handle existing message types
  if (event.data && event.data.type === 'SKIP_WAITING') {
    log('Service Worker: Skip waiting command received');
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CHECK_VERSION') {
    log('Service Worker: Version check requested');
    event.ports[0].postMessage({
      type: 'VERSION_INFO',
      version: APP_VERSION
    });
  }
});

// Handle push notifications
self.addEventListener('push', event => {
  log('Service Worker: Push received');
  
  let notification = {};
  
  try {
    notification = event.data.json();
  } catch (e) {
    notification = {
      title: 'Retreat Pro',
      body: event.data ? event.data.text() : 'New notification',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png'
    };
  }
  
  const title = notification.title || 'Retreat Pro';
  const options = {
    body: notification.body || '',
    icon: notification.icon || '/icons/icon-192x192.png',
    badge: notification.badge || '/icons/icon-72x72.png',
    vibrate: notification.vibrate || [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: notification.id || 1,
      url: notification.url || '/'
    },
    actions: notification.actions || [
      {
        action: 'view',
        title: 'View',
        icon: '/icons/view-action-icon.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification(title, options)
  );
});

// Handle notification click
self.addEventListener('notificationclick', event => {
  log('Service Worker: Notification clicked', event.notification.data);
  
  event.notification.close();
  
  const urlToOpen = event.notification.data && event.notification.data.url
    ? event.notification.data.url
    : '/';
  
  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then(windowClients => {
      // Check if there is already a window/tab open with the target URL
      const matchingClient = windowClients.find(client => {
        return new URL(client.url).pathname === new URL(urlToOpen, self.location.origin).pathname;
      });
      
      // If so, focus it
      if (matchingClient) {
        return matchingClient.focus();
      }
      
      // If not, open a new window/tab
      return clients.openWindow(urlToOpen);
    })
  );
});

// Handle sync event
self.addEventListener('sync', event => {
  log('Service Worker: Sync event received', event.tag);
  
  if (event.tag === 'sync-data') {
    event.waitUntil(syncData());
  } else if (event.tag.startsWith('sync-')) {
    // Handle other specific sync tags
    const syncType = event.tag.replace('sync-', '');
    event.waitUntil(syncSpecificData(syncType));
  }
});

// Periodic sync for content updates
self.addEventListener('periodicsync', event => {
  log('Service Worker: Periodic sync event received', event.tag);
  
  if (event.tag === 'content-update') {
    event.waitUntil(updateContent());
  } else if (event.tag === 'daily-sync') {
    event.waitUntil(performDailySync());
  }
});

// Function to sync all data
async function syncData() {
  log('Service Worker: Syncing all data');
  
  // This would be implemented to process all sync queues
  // For now, we'll just log it
  return Promise.resolve();
}

// Function to sync specific data type
async function syncSpecificData(type) {
  log(`Service Worker: Syncing ${type} data`);
  
  // This would be implemented to process a specific sync queue
  // For now, we'll just log it
  return Promise.resolve();
}

// Function to update content
async function updateContent() {
  log('Service Worker: Updating content');
  
  try {
    // Fetch fresh content from the server
    const response = await fetch('/api/content/latest');
    
    if (!response.ok) {
      throw new Error('Failed to fetch latest content');
    }
    
    const data = await response.json();
    
    // Update caches with fresh content
    const cache = await caches.open(CACHE_NAMES.static);
    
    // Cache new assets
    if (data.assets && Array.isArray(data.assets)) {
      await Promise.all(
        data.assets.map(asset => cache.add(asset))
      );
    }
    
    return true;
  } catch (error) {
    log('Service Worker: Error updating content', error);
    return false;
  }
}

// Function for daily sync tasks
async function performDailySync() {
  log('Service Worker: Performing daily sync');
  
  try {
    // Clean up old cached data
    await cleanupOldCaches();
    
    // Sync user preferences
    await syncUserPreferences();
    
    return true;
  } catch (error) {
    log('Service Worker: Error performing daily sync', error);
    return false;
  }
}

// Function to clean up old caches
async function cleanupOldCaches() {
  const maxAge = {
    [CACHE_NAMES.images]: 30 * 24 * 60 * 60 * 1000, // 30 days
    [CACHE_NAMES.api]: 7 * 24 * 60 * 60 * 1000,     // 7 days
    [CACHE_NAMES.pages]: 3 * 24 * 60 * 60 * 1000,   // 3 days
  };
  
  const now = Date.now();
  
  for (const [cacheName, maxAgeMs] of Object.entries(maxAge)) {
    const cache = await caches.open(cacheName);
    const requests = await cache.keys();
    
    for (const request of requests) {
      const response = await cache.match(request);
      
      if (!response) continue;
      
      const dateHeader = response.headers.get('date');
      if (!dateHeader) continue;
      
      const cacheDate = new Date(dateHeader).getTime();
      
      if (now - cacheDate > maxAgeMs) {
        await cache.delete(request);
        log(`Service Worker: Deleted old cache entry for ${request.url}`);
      }
    }
  }
}

// Function to sync user preferences
async function syncUserPreferences() {
  // This would be implemented to sync user preferences
  // For now, we'll just log it
  log('Service Worker: Synced user preferences');
  return Promise.resolve();
}

// Message handling from clients
self.addEventListener('message', event => {
  log('Service Worker: Message received', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  } else if (event.data && event.data.type === 'CACHE_URLS') {
    // Cache URLs sent from the client
    event.waitUntil(
      caches.open(CACHE_NAMES.static).then(cache => {
        return cache.addAll(event.data.urls);
      })
    );
  }
});
