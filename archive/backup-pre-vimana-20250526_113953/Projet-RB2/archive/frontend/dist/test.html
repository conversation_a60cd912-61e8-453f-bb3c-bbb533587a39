<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Page de test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    p {
      color: #666;
      line-height: 1.6;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 20px;
    }
    .button:hover {
      background-color: #45a049;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Page de test</h1>
    <p>Cette page est un test pour vérifier si le serveur peut servir correctement des fichiers HTML statiques.</p>
    <p>Si vous voyez cette page, cela signifie que le serveur fonctionne correctement.</p>
    <button class="button" id="testButton">Cliquez ici pour tester JavaScript</button>
  </div>

  <script>
    document.getElementById('testButton').addEventListener('click', function() {
      alert('JavaScript fonctionne correctement !');
    });
  </script>
</body>
</html>
