<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Retreat & Be - Simple</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      h1 {
        color: #333;
      }
      p {
        color: #666;
        line-height: 1.6;
      }
      .button {
        display: inline-block;
        background-color: #4CAF50;
        color: white;
        padding: 10px 20px;
        text-align: center;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 20px;
      }
      .button:hover {
        background-color: #45a049;
      }
    </style>
    <link rel="stylesheet" href="/assets/index-C_gEJRHN.css">
  </head>
  <body>
    <div id="root">
      <div class="container">
        <h1>Retreat & Be - Version simplifiée</h1>
        <p>Cette page est une version simplifiée de l'application.</p>
        <p>Si vous voyez cette page, cela signifie que le serveur peut servir des fichiers HTML correctement.</p>
        <p>Cependant, l'application React ne se charge pas correctement.</p>
        <button class="button" id="loadApp">Essayer de charger l'application</button>
      </div>
    </div>

    <script>
      document.getElementById('loadApp').addEventListener('click', function() {
        try {
          // Charger les scripts manuellement
          const vendorScript = document.createElement('script');
          vendorScript.src = '/assets/vendor-DeqkGhWy.js';
          vendorScript.type = 'module';
          document.head.appendChild(vendorScript);

          const uiScript = document.createElement('script');
          uiScript.src = '/assets/ui-khMqCZVx.js';
          uiScript.type = 'module';
          document.head.appendChild(uiScript);

          const indexScript = document.createElement('script');
          indexScript.src = '/assets/index-BDEf-ic6.js';
          indexScript.type = 'module';
          document.head.appendChild(indexScript);

          alert('Scripts chargés. Vérifiez la console pour les erreurs.');
        } catch (error) {
          alert('Erreur lors du chargement des scripts: ' + error.message);
          console.error('Erreur lors du chargement des scripts:', error);
        }
      });
    </script>
  </body>
</html>
