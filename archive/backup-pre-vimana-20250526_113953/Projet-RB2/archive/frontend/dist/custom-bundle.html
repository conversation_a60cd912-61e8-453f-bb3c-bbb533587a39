<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Retreat & Be - Custom Bundle</title>
  <link rel="stylesheet" href="/assets/index-C_gEJRHN.css">
  <style>
    .fallback-container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    p {
      color: #666;
      line-height: 1.6;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 20px;
      border: none;
    }
    .button:hover {
      background-color: #45a049;
    }
  </style>
</head>
<body>
  <div id="root">
    <div class="fallback-container">
      <h1>Retreat & Be</h1>
      <p>Bienvenue sur notre plateforme de retraites de bien-être.</p>
      <p>Cette version utilise un bundle personnalisé pour contourner les problèmes de chargement.</p>
      
      <div id="app-content">
        <!-- Le contenu de l'application sera inséré ici -->
        <p>Chargement de l'application...</p>
      </div>
      
      <button class="button" id="loadApp">Charger l'application</button>
    </div>
  </div>

  <!-- Charger React et ReactDOM depuis CDN -->
  <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

  <script>
    // Application React simplifiée
    document.getElementById('loadApp').addEventListener('click', function() {
      const appContent = document.getElementById('app-content');
      appContent.innerHTML = '';
      
      // Créer un élément React simple
      const App = React.createElement('div', { className: 'fallback-container' },
        React.createElement('h1', null, 'Application React chargée avec succès'),
        React.createElement('p', null, 'Cette application React simplifiée a été chargée avec succès.'),
        React.createElement('p', null, 'Cela confirme que React fonctionne correctement sur votre navigateur.'),
        React.createElement('button', { 
          className: 'button',
          onClick: () => alert('React fonctionne correctement!')
        }, 'Tester React')
      );
      
      // Rendre l'application
      const root = ReactDOM.createRoot(appContent);
      root.render(App);
    });
  </script>
</body>
</html>
