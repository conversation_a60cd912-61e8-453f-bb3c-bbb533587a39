<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Retreat & Be - Debug</title>
    <link rel="stylesheet" href="/assets/index-C_gEJRHN.css">
    <style>
      .debug-container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .debug-log {
        background-color: #f1f1f1;
        border: 1px solid #ddd;
        padding: 10px;
        margin-top: 10px;
        max-height: 200px;
        overflow-y: auto;
        font-family: monospace;
      }
      .debug-button {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 10px 15px;
        margin: 5px;
        border-radius: 4px;
        cursor: pointer;
      }
      .debug-button:hover {
        background-color: #45a049;
      }
      .error {
        color: red;
      }
      .success {
        color: green;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    
    <div class="debug-container">
      <h2>Débogage de l'application</h2>
      <p>Cette page est conçue pour déboguer l'application React qui ne s'affiche pas correctement.</p>
      
      <div>
        <button class="debug-button" id="loadVendor">1. Charger Vendor</button>
        <button class="debug-button" id="loadUI">2. Charger UI</button>
        <button class="debug-button" id="loadIndex">3. Charger Index</button>
        <button class="debug-button" id="checkDOM">4. Vérifier DOM</button>
        <button class="debug-button" id="clearLog">Effacer Log</button>
      </div>
      
      <div class="debug-log" id="debugLog"></div>
    </div>

    <script>
      // Fonction pour ajouter des messages au log
      function log(message, type = 'info') {
        const logElement = document.getElementById('debugLog');
        const logEntry = document.createElement('div');
        logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        logEntry.className = type;
        logElement.appendChild(logEntry);
        logElement.scrollTop = logElement.scrollHeight;
      }

      // Fonction pour charger un script
      function loadScript(src) {
        return new Promise((resolve, reject) => {
          try {
            log(`Chargement du script: ${src}`);
            const script = document.createElement('script');
            script.type = 'module';
            script.src = src;
            script.onload = () => {
              log(`Script chargé avec succès: ${src}`, 'success');
              resolve();
            };
            script.onerror = (error) => {
              log(`Erreur lors du chargement du script: ${src}`, 'error');
              reject(error);
            };
            document.head.appendChild(script);
          } catch (error) {
            log(`Exception lors du chargement du script: ${error.message}`, 'error');
            reject(error);
          }
        });
      }

      // Gestionnaires d'événements pour les boutons
      document.getElementById('loadVendor').addEventListener('click', async () => {
        try {
          await loadScript('/assets/vendor-DeqkGhWy.js');
        } catch (error) {
          log(`Erreur: ${error.message}`, 'error');
        }
      });

      document.getElementById('loadUI').addEventListener('click', async () => {
        try {
          await loadScript('/assets/ui-khMqCZVx.js');
        } catch (error) {
          log(`Erreur: ${error.message}`, 'error');
        }
      });

      document.getElementById('loadIndex').addEventListener('click', async () => {
        try {
          await loadScript('/assets/index-BDEf-ic6.js');
        } catch (error) {
          log(`Erreur: ${error.message}`, 'error');
        }
      });

      document.getElementById('checkDOM').addEventListener('click', () => {
        const root = document.getElementById('root');
        log(`État du DOM root: ${root.innerHTML ? 'Non vide' : 'Vide'}`);
        log(`Nombre d'enfants dans root: ${root.childNodes.length}`);
        log(`Classes sur root: ${root.className || 'Aucune'}`);
        
        // Vérifier les variables globales React
        if (window.React) {
          log('React est disponible globalement', 'success');
        } else {
          log('React n\'est pas disponible globalement', 'error');
        }
        
        if (window.ReactDOM) {
          log('ReactDOM est disponible globalement', 'success');
        } else {
          log('ReactDOM n\'est pas disponible globalement', 'error');
        }
      });

      document.getElementById('clearLog').addEventListener('click', () => {
        document.getElementById('debugLog').innerHTML = '';
      });

      // Log initial
      log('Page de débogage chargée');
      log(`User Agent: ${navigator.userAgent}`);
    </script>
  </body>
</html>
