<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Retreat & Be - Version intermédiaire</title>
  <link rel="stylesheet" href="/assets/index-C_gEJRHN.css">
  <style>
    .fallback-container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    p {
      color: #666;
      line-height: 1.6;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 20px;
      border: none;
    }
    .button:hover {
      background-color: #45a049;
    }
    .log {
      background-color: #f1f1f1;
      border: 1px solid #ddd;
      padding: 10px;
      margin-top: 10px;
      max-height: 200px;
      overflow-y: auto;
      font-family: monospace;
    }
    .error {
      color: red;
    }
    .success {
      color: green;
    }
  </style>
</head>
<body>
  <div id="root">
    <div class="fallback-container">
      <h1>Retreat & Be - Version intermédiaire</h1>
      <p>Cette version utilise React depuis un CDN mais tente de charger vos composants réels.</p>
      
      <div id="app-content">
        <!-- Le contenu de l'application sera inséré ici -->
        <p>Chargement de l'application...</p>
      </div>
      
      <div class="log" id="log"></div>
      
      <button class="button" id="loadApp">Charger l'application</button>
    </div>
  </div>

  <!-- Charger React et ReactDOM depuis CDN -->
  <script src="https://unpkg.com/react@18/umd/react.development.min.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.min.js"></script>
  
  <!-- Charger d'autres dépendances courantes -->
  <script src="https://unpkg.com/react-router-dom@7.4.0/dist/umd/react-router-dom.production.min.js"></script>
  <script src="https://unpkg.com/@mui/material@6.4.8/umd/material-ui.production.min.js"></script>

  <script>
    // Fonction pour ajouter des messages au log
    function log(message, type = 'info') {
      const logElement = document.getElementById('log');
      const logEntry = document.createElement('div');
      logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logEntry.className = type;
      logElement.appendChild(logEntry);
      logElement.scrollTop = logElement.scrollHeight;
    }

    // Fonction pour charger un script
    function loadScript(src) {
      return new Promise((resolve, reject) => {
        try {
          log(`Chargement du script: ${src}`);
          const script = document.createElement('script');
          script.src = src;
          script.onload = () => {
            log(`Script chargé avec succès: ${src}`, 'success');
            resolve();
          };
          script.onerror = (error) => {
            log(`Erreur lors du chargement du script: ${src}`, 'error');
            reject(error);
          };
          document.head.appendChild(script);
        } catch (error) {
          log(`Exception lors du chargement du script: ${error.message}`, 'error');
          reject(error);
        }
      });
    }

    // Définir des variables globales pour les dépendances
    window.React = React;
    window.ReactDOM = ReactDOM;
    window.ReactRouterDOM = ReactRouterDOM;
    window.MaterialUI = MaterialUI;

    // Application React simplifiée
    document.getElementById('loadApp').addEventListener('click', async function() {
      try {
        // Charger les scripts de l'application
        await loadScript('/assets/vendor-DeqkGhWy.js');
        await loadScript('/assets/ui-khMqCZVx.js');
        
        // Créer un élément React simple
        const App = React.createElement('div', { className: 'fallback-container' },
          React.createElement('h1', null, 'Application React chargée avec succès'),
          React.createElement('p', null, 'Les scripts de l\'application ont été chargés.'),
          React.createElement('p', null, 'Vérifiez la console pour les erreurs éventuelles.'),
          React.createElement('button', { 
            className: 'button',
            onClick: () => {
              try {
                // Essayer de charger le script principal
                loadScript('/assets/index-BDEf-ic6.js')
                  .then(() => {
                    log('Script principal chargé avec succès', 'success');
                  })
                  .catch(error => {
                    log(`Erreur lors du chargement du script principal: ${error.message}`, 'error');
                  });
              } catch (error) {
                log(`Exception: ${error.message}`, 'error');
              }
            }
          }, 'Charger le script principal')
        );
        
        // Rendre l'application
        const appContent = document.getElementById('app-content');
        appContent.innerHTML = '';
        const root = ReactDOM.createRoot(appContent);
        root.render(App);
        
        log('Application React de base chargée avec succès', 'success');
      } catch (error) {
        log(`Erreur: ${error.message}`, 'error');
        console.error('Erreur détaillée:', error);
      }
    });
    
    // Log initial
    log('Page intermédiaire chargée');
  </script>
</body>
</html>
