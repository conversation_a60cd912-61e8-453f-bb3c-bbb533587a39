// Script pour configurer le déploiement avec support SPA
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Obtenir le répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Configuration du déploiement avec support SPA...');

// Créer/vérifier le dossier public
const publicDir = path.join(__dirname, 'public');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
  console.log('✅ Dossier public créé');
} else {
  console.log('✅ Dossier public existe déjà');
}

// Créer/vérifier le fichier _redirects
const redirectsPath = path.join(publicDir, '_redirects');
if (!fs.existsSync(redirectsPath)) {
  fs.writeFileSync(redirectsPath, '/* /index.html 200\n');
  console.log('✅ Fichier _redirects créé');
} else {
  console.log('✅ Fichier _redirects existe déjà');
}

// Créer/vérifier le fichier vercel.json
const vercelPath = path.join(__dirname, 'vercel.json');
if (!fs.existsSync(vercelPath)) {
  const vercelConfig = {
    "rewrites": [
      { "source": "/(.*)", "destination": "/index.html" }
    ],
    "headers": [
      {
        "source": "/(.*)",
        "headers": [
          { "key": "Cache-Control", "value": "s-maxage=1, stale-while-revalidate=59" }
        ]
      },
      {
        "source": "/assets/(.*)",
        "headers": [
          { "key": "Cache-Control", "value": "public, max-age=31536000, immutable" }
        ]
      }
    ]
  };
  
  fs.writeFileSync(vercelPath, JSON.stringify(vercelConfig, null, 2));
  console.log('✅ Fichier vercel.json créé');
} else {
  console.log('✅ Fichier vercel.json existe déjà');
}

// Créer le script de serveur Express pour le SPA
const expressServerPath = path.join(__dirname, 'express-server.js');
if (!fs.existsSync(expressServerPath)) {
  console.log('✅ Création du serveur Express pour le SPA...');
  // Le contenu est déjà créé
} else {
  console.log('✅ Serveur Express pour le SPA existe déjà');
}

// Exécuter la commande build
console.log('\n📦 Construction de l\'application...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build terminée avec succès');
} catch (error) {
  console.error('❌ Erreur lors de la build:', error.message);
  process.exit(1);
}

console.log('\n🚀 Configuration terminée! Vous pouvez maintenant:');
console.log('1. Tester le serveur SPA avec: npm run serve:spa');
console.log('2. Déployer votre application sur un hébergeur comme Netlify ou Vercel');
console.log('   qui prendra en charge automatiquement le SPA routing'); 