{"name": "retreat-and-be-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:browser": "vite & node open-browser.js", "dev:basic": "node basic-server.js", "build": "vite build", "preview": "vite preview", "test": "jest", "test:audrey": "node --experimental-modules scripts/run-audrey-tests.js", "migrate:audrey": "node --experimental-modules scripts/audrey-migration.js", "verify:audrey": "node --experimental-modules scripts/verify-integration.js", "deploy:staging": "node --experimental-modules scripts/deploy-staging.js", "deploy:prod": "node --experimental-modules scripts/deploy-production.js", "start": "vite", "dev:standard": "vite", "dev:spa": "node vite-server.js", "generate-sitemap": "ts-node scripts/generate-sitemap.ts", "demo": "vite --config vite.demo.config.ts", "setup:deploy": "node setup-deploy.js", "serve:cjs": "node express-server-cjs.js", "serve:simple": "node simple-server.js", "test:watch": "jest --watch", "type-check": "tsc --noEmit", "type-check:secure": "tsc --noEmit --skipLib<PERSON>heck --jsx react --esModuleInterop src/components/booking/SecureBookingForm.tsx src/pages/partner/OnboardingPage.tsx src/tests/accessibility/axe-setup.ts", "test:coverage": "jest --coverage", "test:security": "jest --testMatch=\"**/security/**/*.test.{ts,tsx}\"", "test:a11y": "jest --testMatch=\"**/*.a11y.test.{ts,tsx}\"", "test:perf": "jest --testMatch=\"**/*.perf.test.{ts,tsx}\"", "test:api": "jest --testMatch=\"**/*.api.test.{ts,tsx}\"", "test:edge": "jest --testMatch=\"**/*.edge.test.{ts,tsx}\"", "test:pages": "jest --testMatch=\"**/pages/**/__tests__/**/*.test.{ts,tsx}\"", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:e2e:specialized": "cypress run --spec \"cypress/e2e/specializedPages.cy.ts\"", "test:all": "npm run test && npm run test:a11y && npm run test:perf && npm run test:api && npm run test:edge && npm run test:security && npm run test:integration && npm run test:pages", "test:integration": "bash src/tests/run-integration-tests.sh", "optimize:production": "node ../scripts/optimize-for-production.js", "lint": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,scss}\"", "serve:spa": "npm run build && node prod-server.js", "install:otel": "scripts/install-otel-deps.sh", "generate": "plop", "generate:atom": "plop atomic -- --atomicType=atoms", "generate:molecule": "plop atomic -- --atomicType=molecules", "generate:organism": "plop atomic -- --atomicType=organisms", "generate:template": "plop atomic -- --atomicType=templates", "migrate": "plop migrate", "migration:track": "node ../scripts/migration-tracker.js", "atomic:audit": "node src/atomic/utils/component-audit.js", "atomic:audit:fix": "node src/atomic/utils/component-audit.js --fix", "atomic:audit:dashboard": "node src/atomic/utils/component-audit.js --dashboard", "atomic:create": "node src/atomic/utils/template-generator.js", "atomic:create:atom": "node src/atomic/utils/template-generator.js atom", "atomic:create:molecule": "node src/atomic/utils/template-generator.js molecule", "atomic:create:organism": "node src/atomic/utils/template-generator.js organism", "atomic:create:template": "node src/atomic/utils/template-generator.js template", "atomic:create:page": "node src/atomic/utils/template-generator.js page", "atomic:migrate": "node src/atomic/utils/migration-assistant.js", "atomic:migrate:analyze": "node src/atomic/utils/migration-assistant.js --analyze-only", "atomic:migrate:batch": "node src/atomic/utils/batch-migration.js", "atomic:update-imports": "node src/atomic/utils/update-imports.js", "atomic:generate-demo": "node src/atomic/utils/generate-demo.js", "atomic:ci-validate": "node src/atomic/utils/ci-validate.js", "atomic:deploy:status": "node src/atomic/utils/deployment-tracker.js status", "atomic:deploy:update": "node src/atomic/utils/deployment-tracker.js update", "atomic:deploy:report": "node src/atomic/utils/deployment-tracker.js report", "atomic:deploy:readiness": "node src/atomic/utils/readiness-check.js", "atomic:deploy:readiness:report": "node src/atomic/utils/readiness-check.js --report", "atomic:deploy:dashboard": "node src/atomic/utils/migration-dashboard-generator.js && open src/atomic/docs/MIGRATION_DASHBOARD_INTERACTIVE.html", "atomic:deploy:check": "node src/atomic/utils/pre-deployment-check.js", "atomic:deploy:check:report": "node src/atomic/utils/pre-deployment-check.js --html", "optimize:dependencies": "node scripts/clean-dependencies.js", "optimize:bundle": "npm run build && rollup-plugin-visualizer", "optimize:audit": "npm run optimize:dependencies && npm audit fix && npm dedupe", "test:ci": "react-scripts test --ci --coverage", "test:redux": "TS_NODE_PROJECT=tsconfig.test.json react-scripts test --testMatch=\"**/__tests__/**/*.(test|spec).(ts|tsx)\"", "eject": "react-scripts eject"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^4.1.3", "@mui/icons-material": "^6.4.8", "@mui/lab": "^5.0.0-alpha.160", "@mui/material": "^6.4.8", "@mui/x-date-pickers": "^6.19.2", "@opentelemetry/context-zone": "^2.0.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.200.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/instrumentation": "^0.200.0", "@opentelemetry/instrumentation-document-load": "^0.45.0", "@opentelemetry/instrumentation-fetch": "^0.200.0", "@opentelemetry/instrumentation-user-interaction": "^0.45.0", "@opentelemetry/instrumentation-xml-http-request": "^0.200.0", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-metrics": "^2.0.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-web": "^2.0.0", "@opentelemetry/semantic-conventions": "^1.30.0", "@radix-ui/react-tabs": "^1.1.4", "@reduxjs/toolkit": "^2.6.1", "@tanstack/react-query": "^5.69.0", "antd": "^5.24.6", "axios": "^1.8.4", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "2.30.0", "dompurify": "^3.2.4", "ethers": "^5.7.2", "focus-trap-react": "^11.0.3", "formik": "^2.4.6", "framer-motion": "^12.5.0", "jest-axe": "^10.0.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^3.1.2", "leaflet": "^1.9.4", "lucide-react": "^0.483.0", "next": "^15.2.4", "notistack": "^3.0.2", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-datepicker": "^8.2.1", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-ga4": "^2.1.0", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-lazy-load-image-component": "^1.6.3", "react-leaflet": "^4.2.1", "react-popper": "^2.3.0", "react-redux": "^9.2.0", "react-router-dom": "^7.4.0", "react-scripts": "5.0.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "react-use-websocket": "^4.13.0", "recharts": "^2.15.1", "redux": "^5.0.1", "redux-mock-store": "^1.5.5", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "semver": "^7.7.1", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "styled-components": "^6.1.16", "tailwind-merge": "^3.2.0", "typescript": "^4.9.5", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "workbox-cacheable-response": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "yup": "^1.6.1", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.25.9", "@jest/types": "^29.6.3", "@sentry/react": "^9.7.0", "@storybook/react": "^7.0.0", "@storybook/react-vite": "^8.6.12", "@storybook/types": "^7.0.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.0.17", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.6.1", "@types/cypress": "^0.1.6", "@types/glob": "^8.1.0", "@types/jest": "^29.5.14", "@types/mocha": "^10.0.10", "@types/node": "^20.4.2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-helmet-async": "^1.0.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "chalk": "^5.4.1", "cypress": "^14.2.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "express": "^4.21.2", "glob": "^11.0.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.7.3", "prettier": "^3.5.3", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "tailwindcss": "^4.0.17", "vite": "^6.2.3", "vite-plugin-svgr": "^4.2.0", "vitest": "^3.1.1"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}