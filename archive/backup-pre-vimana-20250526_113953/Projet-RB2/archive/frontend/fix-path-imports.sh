#!/bin/bash

# Script pour corriger spécifiquement les imports contenant des chemins dans les accolades

echo "Correction des imports avec chemins dans les accolades..."

# Remplace les imports du type import { ./api } from './api' par import { api } from './api'
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import { \.\//import { /g" {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import { \.\.\//import { /g" {} \;

# Correction pour les imports du type import {  } from ./path
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import {  } from \.\//import { Component } from '/g" {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import {  } from \.\.\//import { Component } from '..//g" {} \;

# Correción pour carRental et autres imports API
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import { \.\/api } from '\.\/api'/import { api } from '.\/api'/g" {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import { \.\./import { api } from '\.\./g" {} \;

echo "Corrections terminées!"
