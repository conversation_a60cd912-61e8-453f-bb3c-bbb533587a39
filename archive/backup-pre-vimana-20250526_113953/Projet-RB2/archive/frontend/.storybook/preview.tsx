import React from 'react';
import * as <PERSON><PERSON> from '@emotion/react';
import * as ../src/styles/theme from '../src/styles/theme';
import * as M<PERSON> from '@storybook/react';

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/
}
}
},
  decorators: [
    (Story) => (
      <ThemeProvider theme = {lightTheme}>
        <Story />
      </ThemeProvider>
    )
],
  globalTypes: {
    theme: {
      name: 'Theme',
      description: 'Global theme for components',
      defaultValue: 'light',
      toolbar: {
        icon: 'circlehollow',
        items: ['light', 'dark'],
        showName: true
}
}
}
}

export default preview;
