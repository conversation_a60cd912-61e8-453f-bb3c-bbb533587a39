# Meilleures pratiques pour éviter les erreurs TypeScript dans les tests Redux

Ce document présente les meilleures pratiques pour écrire des tests Redux avec TypeScript sans rencontrer d'erreurs de typage.

## Configuration TypeScript pour les tests

Nous avons créé un fichier `tsconfig.test.json` spécifique pour les tests avec les options suivantes:

```json
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "target": "es2018",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext",
      "es2018.promise"
    ],
    "noImplicitAny": false,
    "strictNullChecks": true
  }
}
```

Les points clés sont:
- L'inclusion de `es2018.promise` pour les tests asynchrones
- La désactivation de `noImplicitAny` pour les tests (bien que nous recommandions d'ajouter des types explicites)
- L'activation de `strictNullChecks` pour éviter les erreurs liées aux valeurs null/undefined

## Types explicites pour les paramètres de fonction

Une erreur TypeScript courante est `Parameter 'X' implicitly has an 'any' type`. Pour l'éviter:

```typescript
// ÉVITER
const selectUser = (state) => state.user;

// PRÉFÉRER
interface RootState {
  user: UserState;
}
const selectUser = (state: RootState) => state.user;
```

## Types pour les tests de sélecteurs

```typescript
// Définir l'interface pour votre état
interface AppState {
  items: string[];
}

// Typer le sélecteur
const selectItems = (state: AppState) => state.items;

// Typer le sélecteur mémorisé
const selectItemCount = createSelector(
  [selectItems],
  (items: string[]) => items.length
);

// Typer l'état dans les tests
it('should select items', () => {
  const state: AppState = { items: ['a', 'b', 'c'] };
  expect(selectItems(state)).toEqual(['a', 'b', 'c']);
});
```

## Types pour les réducteurs

```typescript
import { AnyAction } from '@reduxjs/toolkit';

interface MyState {
  value: number;
}

// Typer correctement les paramètres du reducer
const reducer = (state: MyState = initialState, action: AnyAction): MyState => {
  // ...
};
```

## Types pour les mocks de Redux Toolkit

Lorsque vous mockez Redux Toolkit, assurez-vous d'inclure les types appropriés:

```typescript
jest.mock('@reduxjs/toolkit', () => ({
  createSlice: (options: {
    name: string;
    initialState: any;
    reducers: Record<string, (state: any, action: any) => void>;
  }) => {
    // Implémentation du mock...
  },
  // Autres mocks...
}));
```

## Types pour les composants de test React-Redux

```typescript
interface Props {
  onViewChange: (view: 'day' | 'week' | 'month') => void;
  onEventSelect: (event: CalendarEvent | null) => void;
}

// Typer les props et les paramètres dans les fonctions
const CalendarComponent = ({ onViewChange, onEventSelect }: Props) => {
  const { useSelector, useDispatch } = require('react-redux');
  const dispatch = useDispatch();
  
  // Définir l'interface pour l'état
  interface RootState {
    calendar: {
      events: CalendarEvent[];
      view: 'day' | 'week' | 'month';
      selectedEvent: CalendarEvent | null;
    };
  }
  
  // Typer l'état dans useSelector
  const events = useSelector((state: RootState) => state.calendar.events);
  const view = useSelector((state: RootState) => state.calendar.view);
  
  // Typer les paramètres des fonctions
  const changeView = (newView: 'day' | 'week' | 'month') => {
    dispatch(setView(newView));
    onViewChange(newView);
  };
  
  // ...
};
```

## Correction des opérations arithmétiques sur les dates

TypeScript signale une erreur lors de l'utilisation d'opérations arithmétiques sur les objets Date. La solution est de convertir les dates en timestamps:

```typescript
// ÉVITER
const diff = new Date(a.start) - new Date(b.start); // Erreur TypeScript

// PRÉFÉRER
const diff = new Date(a.start).getTime() - new Date(b.start).getTime();
```

## Cas particuliers

### Tests asynchrones

Pour les tests asynchrones, assurez-vous d'utiliser correctement `async/await`:

```typescript
// Ajouter un retour de type Promise<void> pour les fonctions de test async
it('should fetch data asynchronously', async (): Promise<void> => {
  const result = await fetchData();
  expect(result).toHaveProperty('id');
});
```

### Types pour les actions

Pour typer correctement les actions Redux:

```typescript
import { PayloadAction } from '@reduxjs/toolkit';

// Dans les réducteurs
reducers: {
  increment: (state) => {
    state.value += 1;
  },
  setValue: (state, action: PayloadAction<number>) => {
    state.value = action.payload;
  }
}
```

## Conclusion

En suivant ces pratiques, vous pouvez éviter la plupart des erreurs TypeScript dans vos tests Redux. L'ajout de types explicites non seulement évite les erreurs, mais améliore également la maintenabilité et l'auto-documentation de votre code. 