import { exec } from 'child_process';
import os from 'os';

// Function to determine the correct command to open a URL based on the operating system
const getOpenCommand = () => {
  switch (os.platform()) {
    case 'darwin': // macOS
      return 'open';
    case 'win32': // Windows
      return 'start';
    default: // Linux and others
      return 'xdg-open';
  }
};

// The URL of your development server
const url = 'http://localhost:5173';

// Open the URL in the default browser
console.log(`Opening ${url} in your default browser...`);
exec(`${getOpenCommand()} ${url}`);

console.log(`
✨ Development server is running at: ${url}

Try these routes:
- http://localhost:5173/
- http://localhost:5173/test
- http://localhost:5173/login
- http://localhost:5173/dashboard

If you encounter any issues, try:
1. Clearing your browser cache
2. Using incognito/private mode
3. Disabling browser extensions that might interfere with the app
`);
