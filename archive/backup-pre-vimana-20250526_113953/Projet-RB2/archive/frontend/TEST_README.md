# Documentation des Tests

## Vue d'ensemble

Ce document fournit des informations sur la stratégie de test, la structure et les bonnes pratiques pour le projet Retreat & Be. L'objectif est de maintenir une couverture de test complète et d'assurer la qualité du code.

## Structure des Tests

Les tests sont organisés en plusieurs catégories pour couvrir différents aspects de l'application :

```
src/
├── tests/                   # Tests généraux
│   ├── accessibility/       # Tests d'accessibilité  
│   ├── components/          # Tests de composants spécifiques
│   ├── edge-cases/          # Tests de cas limites
│   ├── integration/         # Tests d'intégration
│   ├── performance/         # Tests de performance
│   ├── security/            # Tests de sécurité
│   └── services/            # Tests de services
├── components/              # Chaque composant a ses propres tests
│   ├── Component/
│   │   ├── Component.tsx
│   │   └── Component.test.tsx
├── hooks/                   # Tests pour les hooks React
│   ├── useHook.ts
│   └── __tests__/
│       └── useHook.test.ts
└── utils/                   # Tests pour les utilitaires
    ├── util.ts
    └── __tests__/
        └── util.test.ts
```

## Types de Tests

### Tests Unitaires

Les tests unitaires se concentrent sur des fonctions ou composants individuels et sont placés à côté du code qu'ils testent.

```typescript
// Exemple de test unitaire pour un composant Button
import { render, screen } from '@testing-library/react';
import { Button } from "./Button';

describe('Button Component', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });
});
```

### Tests d'Intégration

Les tests d'intégration vérifient les interactions entre différents composants ou modules.

```typescript
// Exemple de test d'intégration pour le flux d'authentification
describe('Authentication Flow', () => {
  it('should allow a user to login and view dashboard', async () => {
    // Configuration
    // ...
    
    // Test du processus de login
    // ...
    
    // Vérification de la redirection vers le dashboard
    // ...
  });
});
```

### Tests d'Accessibilité

Les tests d'accessibilité utilisent jest-axe pour vérifier la conformité aux normes WCAG.

```typescript
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';
import { Component } from "./Component';

describe('Component Accessibility', () => {
  it('should not have any accessibility violations', async () => {
    const { container } = render(<Component />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

### Tests de Performance

Les tests de performance mesurent le temps de rendu et d'autres métriques.

```typescript
describe('Performance', () => {
  it('renders within performance budget', () => {
    const start = performance.now();
    render(<Component />);
    const end = performance.now();
    expect(end - start).toBeLessThan(100);
  });
});
```

## Bonnes Pratiques

### Structure des Tests

- Utilisez le pattern AAA (Arrange, Act, Assert) pour structurer vos tests
- Isolez chaque test pour qu'il puisse s'exécuter indépendamment
- Nommez clairement vos tests pour décrire ce qu'ils testent

```typescript
it('should display error message when login fails', async () => {
  // Arrange
  mockAuthService.login.mockRejectedValue(new Error('Invalid credentials'));
  
  // Act
  render(<LoginForm />);
  fireEvent.click(screen.getByRole('button', { name: /login/i }));
  
  // Assert
  await waitFor(() => {
    expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
  });
});
```

### Mocks

Standardisez l'utilisation des mocks pour simuler les dépendances externes.

```typescript
// Exemple de mock pour un service
jest.mock('../../services/authService', () => ({
  login: jest.fn(),
  logout: jest.fn(),
  getUserData: jest.fn()
}));

// Réinitialisation des mocks avant chaque test
beforeEach(() => {
  jest.clearAllMocks();
});
```

### Tests Asynchrones

Utilisez les fonctions async/await et waitFor pour les tests asynchrones.

```typescript
it('loads user data on mount', async () => {
  // Arrange
  mockAuthService.getUserData.mockResolvedValue({ name: 'Test User' });
  
  // Act
  render(<UserProfile />);
  
  // Assert
  await waitFor(() => {
    expect(screen.getByText('Test User')).toBeInTheDocument();
  });
});
```

## Exécution des Tests

### Commandes Principales

- `npm test` : Exécute tous les tests
- `npm test -- --watch` : Exécute les tests en mode watch
- `npm test -- --coverage` : Génère un rapport de couverture
- `npm test -- --testPathPattern=src/components` : Exécute uniquement les tests des composants
- `npm test -- --testPathPattern=accessibility` : Exécute uniquement les tests d'accessibilité

### Filtrage des Tests

Vous pouvez filtrer les tests par nom ou chemin :

```bash
# Exécuter un test spécifique
npm test -- -t "renders correctly"

# Exécuter tous les tests dans un fichier
npm test -- path/to/file.test.ts

# Exécuter tous les tests dans un répertoire
npm test -- path/to/directory
```

## Couverture de Code

L'objectif est de maintenir une couverture de code d'au moins 90% pour les composants critiques et 80% pour l'ensemble du code.

Après avoir exécuté les tests avec couverture (`npm test -- --coverage`), un rapport est généré dans le répertoire `coverage/`.

## Résolution des Problèmes Courants

### Tests qui échouent aléatoirement

Si un test échoue parfois mais pas toujours, il peut s'agir d'un problème de "flaky test". Causes possibles :

- Dépendance à l'état des tests précédents
- Dépendance au timing ou aux animations
- Utilisation incorrecte des mocks

Solution : isolez le test et utilisez des waitFor ou des timers simulés.

### Tests lents

Si vos tests sont lents :

- Utilisez des mocks pour les API externes
- Évitez d'initialiser des objets complexes inutilement
- Utilisez `--runInBand` pour les tests d'intégration volumineux

## Ressources

- [Documentation de Jest](https://jestjs.io/docs/getting-started)
- [Documentation de React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Documentation de jest-axe](https://github.com/nickcolley/jest-axe)

## Contribution

Lors de l'ajout de nouvelles fonctionnalités, suivez ces étapes pour maintenir la qualité des tests :

1. Écrivez d'abord les tests (TDD)
2. Vérifiez la couverture de code
3. Testez les cas limites
4. Incluez des tests d'accessibilité pour les composants UI
5. Vérifiez la performance pour les composants critiques 