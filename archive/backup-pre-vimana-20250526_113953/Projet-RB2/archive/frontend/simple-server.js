import express from 'express';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3000;

// Logging middleware
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  next();
});

// Désactiver la CSP pour le débogage
app.use((req, res, next) => {
  // Supprimer les en-têtes de sécurité qui pourraient bloquer les scripts
  res.removeHeader('Content-Security-Policy');
  res.removeHeader('X-Content-Security-Policy');
  res.removeHeader('X-WebKit-CSP');

  // Ajouter des en-têtes permissifs
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  next();
});

// Définir les types MIME
app.use((req, res, next) => {
  if (req.url.endsWith('.js') || req.url.endsWith('.mjs')) {
    res.setHeader('Content-Type', 'application/javascript');
    console.log(`Setting Content-Type: application/javascript for ${req.url}`);
  } else if (req.url.endsWith('.css')) {
    res.setHeader('Content-Type', 'text/css');
    console.log(`Setting Content-Type: text/css for ${req.url}`);
  } else if (req.url.endsWith('.json')) {
    res.setHeader('Content-Type', 'application/json');
    console.log(`Setting Content-Type: application/json for ${req.url}`);
  }
  next();
});

// Vérifier si le répertoire dist existe
const distPath = path.join(__dirname, 'dist');
if (fs.existsSync(distPath)) {
  console.log(`Serving static files from: ${distPath}`);
  // Liste les fichiers dans le répertoire dist
  const files = fs.readdirSync(distPath);
  console.log('Files in dist directory:', files);
} else {
  console.error(`Error: Directory ${distPath} does not exist!`);
}

// Servir les fichiers statiques
app.use(express.static(distPath));

// Rediriger toutes les requêtes vers index.html (SPA)
app.get('*', (req, res) => {
  const indexPath = path.join(__dirname, 'dist', 'index.html');
  if (fs.existsSync(indexPath)) {
    console.log(`Serving index.html for path: ${req.url}`);
    res.sendFile(indexPath);
  } else {
    console.error(`Error: File ${indexPath} does not exist!`);
    res.status(404).send('index.html not found');
  }
});

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`Serveur démarré sur http://localhost:${PORT}`);
});
