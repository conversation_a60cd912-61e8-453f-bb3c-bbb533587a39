#!/bin/bash

# Script pour corriger les imports avec des tirets dans les noms de modules

echo "Correction des imports avec des tirets..."

# Corrige les imports avec des tirets dans le nom
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as react-chartjs-2/import * as ReactChartJS2/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as color-contrast-checker/import * as ColorContrastChecker/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as react-popper/import * as ReactPopper/g' {} \;

# Correction des guillemets restants
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as React from "react"";/import * as React from "react";/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/"";/"";/g' {} \;

echo "Corrections terminées!"
