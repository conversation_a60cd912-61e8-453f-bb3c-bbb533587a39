const React = require('react');
const { render, screen, fireEvent } = require('@testing-library/react');

// Un composant Button très simple (en JS pur, pas de JSX)
function SimpleButton(props) {
  return React.createElement('button', {
    onClick: props.onClick,
    disabled: props.disabled || false,
    'data-testid': 'simple-button'
  }, props.children);
}

describe('SimpleButton component', () => {
  test('renders with the correct text', () => {
    render(React.createElement(SimpleButton, {}, 'Click me'));
    const button = screen.getByTestId('simple-button');
    expect(button.textContent).toBe('Click me');
  });

  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(React.createElement(SimpleButton, { onClick: handleClick }, 'Click me'));
    
    fireEvent.click(screen.getByTestId('simple-button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('does not call onClick when disabled', () => {
    const handleClick = jest.fn();
    render(React.createElement(SimpleButton, { onClick: handleClick, disabled: true }, 'Click me'));
    
    fireEvent.click(screen.getByTestId('simple-button'));
    expect(handleClick).not.toHaveBeenCalled();
  });
}); 