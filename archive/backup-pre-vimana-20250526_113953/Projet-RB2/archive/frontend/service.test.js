// Un service simple à tester
const userService = {
  fetchUser: async (id) => {
    // Dans un cas réel, ce serait un appel API
    return { id, name: 'Test User', email: '<EMAIL>' };
  },

  saveUser: async (user) => {
    // Dans un cas réel, ce serait un appel API POST
    return { success: true, user };
  }
};

// Les tests
describe('userService', () => {
  // Test d'une fonction asynchrone
  test('fetchUser returns user data', async () => {
    const user = await userService.fetchUser(1);
    expect(user.id).toBe(1);
    expect(user.name).toBe('Test User');
    expect(user.email).toBe('<EMAIL>');
  });

  // Test avec un mock de fonction
  test('saveUser calls API correctly', async () => {
    // Mock de la fonction saveUser
    const originalSaveUser = userService.saveUser;
    userService.saveUser = jest.fn().mockResolvedValue({ success: true });

    // Appel de la fonction mockée
    const userData = { id: 1, name: 'New User' };
    const result = await userService.saveUser(userData);

    // Vérifications
    expect(result.success).toBe(true);
    expect(userService.saveUser).toHaveBeenCalledWith(userData);
    expect(userService.saveUser).toHaveBeenCalledTimes(1);

    // Restauration de la fonction originale
    userService.saveUser = originalSaveUser;
  });
}); 