#!/bin/bash

# Script pour corriger les problèmes de balises JSX dans les fichiers

echo "Correction des balises JSX..."

# Correction des balises MenuItem
find ./src -type f -name "SecurityEventsList.tsx" -exec sed -i '' 's/<MenuItem value=">/<MenuItem value=""/g' {} \;

# Correction des doubles guillemets 
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/""/""/g' {} \;

# Autres corrections spécifiques
find ./src -type f -name "setupTests.ts" -exec sed -i '' 's/import \* as React from "react""; \/\/ Import nécessaire pour JSX/import * as React from "react"; \/\/ Import nécessaire pour JSX/g' {} \;

echo "Corrections terminées!"
