// Mock pour QueueService
class QueueServiceMock {
  constructor() {
    this.queues = new Map();
    this.workers = new Map();
  }

  createQueue(options) {
    const queue = {
      name: options.name,
      add: jest.fn().mockResolvedValue({ id: 'mock-job-id' }),
      getJob: jest.fn().mockResolvedValue({ id: 'mock-job-id', data: {} }),
      pause: jest.fn().mockResolvedValue(undefined),
      resume: jest.fn().mockResolvedValue(undefined),
      clean: jest.fn().mockResolvedValue(undefined),
      close: jest.fn().mockResolvedValue(undefined),
      getJobCounts: jest.fn().mockResolvedValue({
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        waiting: 0
      }),
      client: Promise.resolve({
        ping: jest.fn().mockResolvedValue('PONG')
      })
    };
    this.queues.set(options.name, queue);
    return queue;
  }

  async addJob(queueName, jobId, data) {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }
    const job = await queue.add(jobId, data);
    return job?.id || jobId;
  }

  async getJob(queueName, jobId) {
    const queue = this.queues.get(queueName);
    if (!queue) {
      return null;
    }
    return queue.getJob(jobId);
  }

  async removeJob(queueName, jobId) {
    const queue = this.queues.get(queueName);
    if (!queue) {
      return;
    }
    const job = await queue.getJob(jobId);
    if (job) {
      await job.remove();
    }
  }

  async pause(queueName) {
    const queue = this.queues.get(queueName);
    if (queue) {
      await queue.pause();
    }
  }

  async resume(queueName) {
    const queue = this.queues.get(queueName);
    if (queue) {
      await queue.resume();
    }
  }

  async clean(queueName, grace) {
    const queue = this.queues.get(queueName);
    if (queue) {
      await queue.clean(grace, 50, 'completed');
      await queue.clean(grace, 50, 'failed');
    }
  }

  async checkHealth() {
    return {
      isHealthy: true,
      details: `All ${this.queues.size} queues are healthy`
    };
  }

  getQueueOrThrow(name) {
    const queue = this.queues.get(name);
    if (!queue) {
      throw new Error(`Queue ${name} not found`);
    }
    return queue;
  }

  async cleanup() {
    for (const queue of this.queues.values()) {
      await queue.close();
    }
    for (const worker of this.workers.values()) {
      await worker.close();
    }
    this.queues.clear();
    this.workers.clear();
  }

  // Singleton pattern
  static getInstance() {
    if (!QueueServiceMock.instance) {
      QueueServiceMock.instance = new QueueServiceMock();
    }
    return QueueServiceMock.instance;
  }
}

// Initialiser l'instance pour le pattern singleton
QueueServiceMock.instance = null;

const QueueService = {
  QueueService: QueueServiceMock
};

module.exports = QueueService;
module.exports.default = module.exports; 