// Mock complet pour node-cache
const EventEmitter = require('events');

class NodeCacheMock extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      stdTTL: options.stdTTL || 0,
      checkperiod: options.checkperiod || 0,
      useClones: options.useClones !== false,
      deleteOnExpire: options.deleteOnExpire !== false,
      maxKeys: options.maxKeys || -1,
      ...options
    };
    this.data = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      keys: 0,
      ksize: 0,
      vsize: 0
    };
    this.ttlData = new Map();
    this.maxListeners = 100;
  }

  set(key, value, ttl = this.options.stdTTL) {
    this.data.set(key, value);
    if (ttl > 0) {
      this.ttlData.set(key, Date.now() + (ttl * 1000));
    }
    this.stats.keys = this.data.size;
    this.emit('set', key, value);
    return true;
  }

  get(key) {
    if (this.data.has(key)) {
      // Vérifier si la clé est expirée
      if (this.ttlData.has(key) && this.ttlData.get(key) < Date.now()) {
        this.del(key);
        this.stats.misses++;
        return undefined;
      }
      this.stats.hits++;
      return this.data.get(key);
    }
    this.stats.misses++;
    return undefined;
  }

  del(key) {
    const deleted = this.data.delete(key);
    this.ttlData.delete(key);
    this.stats.keys = this.data.size;
    if (deleted) {
      this.emit('del', key);
      return 1;
    }
    return 0;
  }

  take(key) {
    const value = this.get(key);
    if (value !== undefined) {
      this.del(key);
    }
    return value;
  }

  getTtl(key) {
    if (!this.data.has(key)) return undefined;
    if (!this.ttlData.has(key)) return 0;
    return Math.max(0, Math.floor((this.ttlData.get(key) - Date.now()) / 1000));
  }

  setTtl(key, ttl) {
    if (!this.data.has(key)) return false;
    if (ttl > 0) {
      this.ttlData.set(key, Date.now() + (ttl * 1000));
    } else {
      this.ttlData.delete(key);
    }
    return true;
  }

  keys() {
    return Array.from(this.data.keys());
  }

  has(key) {
    if (this.data.has(key)) {
      // Vérifier si la clé est expirée
      if (this.ttlData.has(key) && this.ttlData.get(key) < Date.now()) {
        this.del(key);
        return false;
      }
      return true;
    }
    return false;
  }

  flushAll() {
    this.data.clear();
    this.ttlData.clear();
    this.stats.keys = 0;
    this.emit('flush');
    return true;
  }

  flush() {
    return this.flushAll();
  }

  getStats() {
    return { ...this.stats };
  }

  close() {
    this.emit('close');
    this.removeAllListeners();
    this.data.clear();
    this.ttlData.clear();
    return true;
  }

  mset(keyValueSet, ttl = this.options.stdTTL) {
    if (!Array.isArray(keyValueSet)) {
      throw new Error('keyValueSet must be an array');
    }
    
    for (const { key, val } of keyValueSet) {
      this.set(key, val, ttl);
    }
    return true;
  }

  mget(keys) {
    if (!Array.isArray(keys)) {
      throw new Error('keys must be an array');
    }
    
    const result = {};
    for (const key of keys) {
      const value = this.get(key);
      if (value !== undefined) {
        result[key] = value;
      }
    }
    return result;
  }

  mdel(keys) {
    if (!Array.isArray(keys)) {
      throw new Error('keys must be an array');
    }
    
    let count = 0;
    for (const key of keys) {
      count += this.del(key);
    }
    return count;
  }
}

// Création d'une fonction factory qui retourne une instance de NodeCacheMock
const NodeCache = jest.fn().mockImplementation((options) => {
  return new NodeCacheMock(options);
});

module.exports = NodeCache; 