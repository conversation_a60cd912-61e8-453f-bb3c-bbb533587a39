import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Grid,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Tooltip,
  Card,
  CardContent,
  CardActions
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import ShareIcon from '@mui/icons-material/Share';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import PercentIcon from '@mui/icons-material/Percent';
import MoneyOffIcon from '@mui/icons-material/MoneyOff';

// Types pour le système de parrainage
enum ReferralStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

enum RewardStatus {
  PENDING = 'PENDING',
  USED = 'USED',
  EXPIRED = 'EXPIRED'
}

enum RewardType {
  FREE_MONTHS = 'FREE_MONTHS',
  PERCENTAGE_DISCOUNT = 'PERCENTAGE_DISCOUNT',
  FIXED_AMOUNT = 'FIXED_AMOUNT'
}

interface ReferralCode {
  id: string;
  code: string;
  description: string;
  isActive: boolean;
  expiresAt?: Date;
  createdAt: Date;
}

interface Referral {
  id: string;
  status: ReferralStatus;
  referredPartner: {
    id: string;
    businessName: string;
  };
  completedAt?: Date;
  createdAt: Date;
}

interface Reward {
  id: string;
  type: RewardType;
  value: number;
  status: RewardStatus;
  expiresAt: Date;
  usedAt?: Date;
  referral: {
    referredPartner: {
      businessName: string;
    };
  };
}

interface ReferralProgramProps {
  partnerId: string;
}

const ReferralProgram: React.FC<ReferralProgramProps> = ({ partnerId }) => {
  const [referralCodes, setReferralCodes] = useState<ReferralCode[]>([]);
  const [referrals, setReferrals] = useState<Referral[]>([]);
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [applyDialogOpen, setApplyDialogOpen] = useState(false);
  const [codeDescription, setCodeDescription] = useState('');
  const [referralCode, setReferralCode] = useState('');
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Simuler le chargement des données
  useEffect(() => {
    const fetchReferralData = async () => {
      setLoading(true);
      try {
        // Simuler un délai de chargement
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Données de démonstration
        const mockReferralCodes: ReferralCode[] = [
          {
            id: '1',
            code: 'RETREAT20',
            description: 'Code de parrainage standard',
            isActive: true,
            createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        ];
        
        const mockReferrals: Referral[] = [
          {
            id: '1',
            status: ReferralStatus.COMPLETED,
            referredPartner: {
              id: 'partner_456',
              businessName: 'Wellness Center Paris'
            },
            completedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
            createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000)
          },
          {
            id: '2',
            status: ReferralStatus.PENDING,
            referredPartner: {
              id: 'partner_789',
              businessName: 'Zen Meditation Studio'
            },
            createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
          }
        ];
        
        const mockRewards: Reward[] = [
          {
            id: '1',
            type: RewardType.FREE_MONTHS,
            value: 1,
            status: RewardStatus.PENDING,
            expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
            referral: {
              referredPartner: {
                businessName: 'Wellness Center Paris'
              }
            }
          }
        ];
        
        setReferralCodes(mockReferralCodes);
        setReferrals(mockReferrals);
        setRewards(mockRewards);
      } catch (error) {
        console.error('Error fetching referral data:', error);
        setErrorMessage('Erreur lors du chargement des données de parrainage');
      } finally {
        setLoading(false);
      }
    };

    fetchReferralData();
  }, [partnerId]);

  const handleCreateReferralCode = async () => {
    setLoading(true);
    try {
      // Simuler un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Créer un nouveau code de parrainage
      const newCode: ReferralCode = {
        id: `${Date.now()}`,
        code: `RETREAT${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        description: codeDescription || 'Code de parrainage',
        isActive: true,
        createdAt: new Date()
      };
      
      setReferralCodes([...referralCodes, newCode]);
      setCreateDialogOpen(false);
      setCodeDescription('');
      setSuccessMessage('Code de parrainage créé avec succès');
      
      // Effacer le message après 3 secondes
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error creating referral code:', error);
      setErrorMessage('Erreur lors de la création du code de parrainage');
      
      // Effacer le message après 3 secondes
      setTimeout(() => setErrorMessage(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  const handleApplyReferralCode = async () => {
    setLoading(true);
    try {
      // Simuler un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Vérifier si le code existe
      if (referralCode === 'PARTNER123') {
        // Simuler l'application d'un code de parrainage
        const newReferral: Referral = {
          id: `${Date.now()}`,
          status: ReferralStatus.PENDING,
          referredPartner: {
            id: partnerId,
            businessName: 'Votre entreprise'
          },
          createdAt: new Date()
        };
        
        setReferrals([...referrals, newReferral]);
        setApplyDialogOpen(false);
        setReferralCode('');
        setSuccessMessage('Code de parrainage appliqué avec succès');
      } else {
        setErrorMessage('Code de parrainage invalide');
      }
      
      // Effacer le message après 3 secondes
      setTimeout(() => {
        setSuccessMessage(null);
        setErrorMessage(null);
      }, 3000);
    } catch (error) {
      console.error('Error applying referral code:', error);
      setErrorMessage('Erreur lors de l\'application du code de parrainage');
      
      // Effacer le message après 3 secondes
      setTimeout(() => setErrorMessage(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    setSuccessMessage('Code copié dans le presse-papiers');
    
    // Effacer le message après 3 secondes
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  const handleShareCode = (code: string) => {
    // Simuler le partage du code
    const shareUrl = `https://retreatandbe.com/referral?code=${code}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Rejoignez Retreat And Be',
        text: 'Utilisez mon code de parrainage pour rejoindre Retreat And Be',
        url: shareUrl
      }).catch(error => console.error('Error sharing:', error));
    } else {
      navigator.clipboard.writeText(shareUrl);
      setSuccessMessage('Lien de parrainage copié dans le presse-papiers');
      
      // Effacer le message après 3 secondes
      setTimeout(() => setSuccessMessage(null), 3000);
    }
  };

  const getRewardIcon = (type: RewardType) => {
    switch (type) {
      case RewardType.FREE_MONTHS:
        return <CalendarMonthIcon />;
      case RewardType.PERCENTAGE_DISCOUNT:
        return <PercentIcon />;
      case RewardType.FIXED_AMOUNT:
        return <MoneyOffIcon />;
      default:
        return <CardGiftcardIcon />;
    }
  };

  const getRewardText = (reward: Reward) => {
    switch (reward.type) {
      case RewardType.FREE_MONTHS:
        return `${reward.value} mois gratuit${reward.value > 1 ? 's' : ''}`;
      case RewardType.PERCENTAGE_DISCOUNT:
        return `${reward.value}% de réduction`;
      case RewardType.FIXED_AMOUNT:
        return `${reward.value}€ de réduction`;
      default:
        return 'Récompense';
    }
  };

  if (loading && !referralCodes.length && !referrals.length && !rewards.length) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        Programme de parrainage
      </Typography>
      
      {successMessage && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {successMessage}
        </Alert>
      )}
      
      {errorMessage && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errorMessage}
        </Alert>
      )}
      
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" gutterBottom>
          Comment ça marche
        </Typography>
        <Typography variant="body2" paragraph>
          Parrainez d'autres partenaires et recevez des récompenses lorsqu'ils s'abonnent à notre plateforme.
          Pour chaque parrainage réussi, vous recevez 1 mois d'abonnement gratuit.
        </Typography>
        
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ 
                    bgcolor: 'primary.main', 
                    color: 'white', 
                    width: 32, 
                    height: 32, 
                    borderRadius: '50%', 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    mr: 1
                  }}>
                    1
                  </Box>
                  <Typography variant="subtitle1">Créez votre code</Typography>
                </Box>
                <Typography variant="body2">
                  Créez votre code de parrainage personnalisé et partagez-le avec d'autres professionnels.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ 
                    bgcolor: 'primary.main', 
                    color: 'white', 
                    width: 32, 
                    height: 32, 
                    borderRadius: '50%', 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    mr: 1
                  }}>
                    2
                  </Box>
                  <Typography variant="subtitle1">Ils s'inscrivent</Typography>
                </Box>
                <Typography variant="body2">
                  Vos filleuls s'inscrivent en utilisant votre code de parrainage et souscrivent à un abonnement.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ 
                    bgcolor: 'primary.main', 
                    color: 'white', 
                    width: 32, 
                    height: 32, 
                    borderRadius: '50%', 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    mr: 1
                  }}>
                    3
                  </Box>
                  <Typography variant="subtitle1">Recevez des récompenses</Typography>
                </Box>
                <Typography variant="body2">
                  Pour chaque parrainage validé, vous recevez 1 mois d'abonnement gratuit à utiliser sur votre compte.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
      
      <Divider sx={{ my: 3 }} />
      
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1">
                Vos codes de parrainage
              </Typography>
              <Button 
                variant="outlined" 
                startIcon={<PersonAddIcon />}
                onClick={() => setCreateDialogOpen(true)}
              >
                Créer un code
              </Button>
            </Box>
            
            {referralCodes.length === 0 ? (
              <Typography variant="body2" color="text.secondary">
                Vous n'avez pas encore créé de code de parrainage.
              </Typography>
            ) : (
              <List>
                {referralCodes.map((code) => (
                  <Paper key={code.id} variant="outlined" sx={{ mb: 2, p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2">
                        {code.code}
                      </Typography>
                      <Box>
                        <Tooltip title="Copier le code">
                          <IconButton size="small" onClick={() => handleCopyCode(code.code)}>
                            <ContentCopyIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Partager le code">
                          <IconButton size="small" onClick={() => handleShareCode(code.code)}>
                            <ShareIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {code.description}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Chip 
                        label={code.isActive ? 'Actif' : 'Inactif'} 
                        size="small" 
                        color={code.isActive ? 'success' : 'default'}
                        sx={{ mr: 1 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        Créé le {code.createdAt.toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Paper>
                ))}
              </List>
            )}
            
            <Box sx={{ mt: 3 }}>
              <Button 
                variant="text" 
                onClick={() => setApplyDialogOpen(true)}
              >
                Vous avez un code de parrainage ?
              </Button>
            </Box>
          </Box>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Vos parrainages
            </Typography>
            
            {referrals.length === 0 ? (
              <Typography variant="body2" color="text.secondary">
                Vous n'avez pas encore de parrainages.
              </Typography>
            ) : (
              <List>
                {referrals.map((referral) => (
                  <ListItem 
                    key={referral.id}
                    secondaryAction={
                      <Chip 
                        icon={referral.status === ReferralStatus.COMPLETED ? <CheckCircleIcon /> : <PendingIcon />}
                        label={referral.status === ReferralStatus.COMPLETED ? 'Validé' : 'En attente'} 
                        color={referral.status === ReferralStatus.COMPLETED ? 'success' : 'default'}
                        size="small"
                      />
                    }
                    sx={{ 
                      bgcolor: 'background.paper', 
                      mb: 1, 
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'divider'
                    }}
                  >
                    <ListItemText
                      primary={referral.referredPartner.businessName}
                      secondary={`Parrainé le ${referral.createdAt.toLocaleDateString()}`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Box>
          
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Vos récompenses
            </Typography>
            
            {rewards.length === 0 ? (
              <Typography variant="body2" color="text.secondary">
                Vous n'avez pas encore de récompenses.
              </Typography>
            ) : (
              <List>
                {rewards.map((reward) => (
                  <Paper key={reward.id} variant="outlined" sx={{ mb: 2, p: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Box sx={{ mr: 2 }}>
                        {getRewardIcon(reward.type)}
                      </Box>
                      <Box>
                        <Typography variant="subtitle2">
                          {getRewardText(reward)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Pour avoir parrainé {reward.referral.referredPartner.businessName}
                        </Typography>
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Chip 
                        label={
                          reward.status === RewardStatus.PENDING 
                            ? 'Disponible' 
                            : reward.status === RewardStatus.USED 
                              ? 'Utilisé' 
                              : 'Expiré'
                        } 
                        size="small" 
                        color={
                          reward.status === RewardStatus.PENDING 
                            ? 'success' 
                            : reward.status === RewardStatus.USED 
                              ? 'primary' 
                              : 'default'
                        }
                        sx={{ mr: 1 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        {reward.status === RewardStatus.PENDING 
                          ? `Expire le ${reward.expiresAt.toLocaleDateString()}` 
                          : reward.status === RewardStatus.USED 
                            ? `Utilisé le ${reward.usedAt?.toLocaleDateString()}` 
                            : `Expiré le ${reward.expiresAt.toLocaleDateString()}`
                        }
                      </Typography>
                    </Box>
                    
                    {reward.status === RewardStatus.PENDING && (
                      <Button 
                        variant="outlined" 
                        size="small" 
                        sx={{ mt: 2 }}
                        onClick={() => setSuccessMessage('Cette fonctionnalité sera disponible prochainement')}
                      >
                        Utiliser
                      </Button>
                    )}
                  </Paper>
                ))}
              </List>
            )}
          </Box>
        </Grid>
      </Grid>
      
      {/* Dialog pour créer un code de parrainage */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)}>
        <DialogTitle>Créer un code de parrainage</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Description du code"
            fullWidth
            variant="outlined"
            value={codeDescription}
            onChange={(e) => setCodeDescription(e.target.value)}
            placeholder="Ex: Pour mes collègues thérapeutes"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Annuler</Button>
          <Button 
            onClick={handleCreateReferralCode} 
            variant="contained" 
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Dialog pour appliquer un code de parrainage */}
      <Dialog open={applyDialogOpen} onClose={() => setApplyDialogOpen(false)}>
        <DialogTitle>Appliquer un code de parrainage</DialogTitle>
        <DialogContent>
          <Typography variant="body2" paragraph>
            Si vous avez été parrainé par un autre partenaire, entrez son code de parrainage ici.
          </Typography>
          <TextField
            autoFocus
            margin="dense"
            label="Code de parrainage"
            fullWidth
            variant="outlined"
            value={referralCode}
            onChange={(e) => setReferralCode(e.target.value)}
            placeholder="Ex: PARTNER123"
          />
          <Typography variant="caption" color="text.secondary">
            Pour la démonstration, utilisez le code "PARTNER123"
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApplyDialogOpen(false)}>Annuler</Button>
          <Button 
            onClick={handleApplyReferralCode} 
            variant="contained" 
            disabled={loading || !referralCode}
          >
            {loading ? <CircularProgress size={24} /> : 'Appliquer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default ReferralProgram;
