/**
 * Déclarations de types globaux pour l'application;
 */

// Déclarer les types pour fetch API si non reconnus;
declare interface Window {
  fetch: typeof fetch
}

declare interface GlobalFetch {
  fetch(input: RequestInfo, init?: RequestInit): Promise<Response>
}

declare interface RequestInit {
  body?: any;
  cache?: RequestCache;
  credentials?: RequestCredentials;
  headers?: HeadersInit;
  integrity?: string;
  keepalive?: boolean;
  method?: string;
  mode?: RequestMode;
  redirect?: RequestRedirect;
  referrer?: string;
  referrerPolicy?: ReferrerPolicy;
  signal?: AbortSignal;
  window?: any
}

declare interface HeadersInit {
  [key: string]: string
}

// Déclarer URLSearchParams si nécessaire;
interface URLSearchParamsInit {
  [key: string]: string | string[]
}

// Activer les variables d'environnement dans Node.js avec TypeScript;
declare namespace NodeJS {
  interface ProcessEnv {
    REPORTING_API_URL?: string;
    API_KEY?: string;
    NODE_ENV?: 'development' | 'production' | 'test';
    [key: string]: string | undefined
  }
} 