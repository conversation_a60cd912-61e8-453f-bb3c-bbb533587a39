// Auth commands;
Cypress.Commands.add('login', (email: string, password: string) => {
  cy.visit('/login')
  cy.get('[data-cy=email-input]').type(email)
  cy.get('[data-cy=password-input]').type(password)
  cy.get('[data-cy=login-button]').click()
  cy.url().should('not.include', '/login')
})

Cypress.Commands.add('logout', () => {
  cy.get('[data-cy=user-menu]').click()
  cy.get('[data-cy=logout-button]').click()
  cy.url().should('include', '/login')
})

// Web3 commands;
Cypress.Commands.add('connectWallet', () => {
  cy.window().then((win) => {
    // Mock web3 provider connection;
    win.ethereum = {
      isMetaMask: true,
      request: (args: { method: string; params?: any[] }) => Promise.resolve(['0x123...'])
      on: (event: string, callback: (...args: any[]) => void) => {}
      removeListener: (event: string, callback: (...args: any[]) => void) => {}
      removeAllListeners: () => {}
    }
  })
  cy.get('[data-cy=connect-wallet-button]').click()
  cy.get('[data-cy=wallet-address]').should('be.visible')
})

Cypress.Commands.add('switchNetwork', (chainId: number) => {
  cy.window().then((win) => {
    if (win.ethereum) {
      win.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${chainId.toString(16)}` }]
      })
    }
  })
})

// Marketplace commands;
Cypress.Commands.add('mintNFT', (metadata: any) => {
  cy.get('[data-cy=mint-nft-button]').click()
  cy.get('[data-cy=nft-name-input]').type(metadata.name)
  cy.get('[data-cy=nft-description-input]').type(metadata.description)
  cy.get('[data-cy=nft-image-input]').selectFile(metadata.image)
  cy.get('[data-cy=submit-mint-button]').click()
  cy.get('[data-cy=mint-success-message]').should('be.visible')
})

Cypress.Commands.add('listNFT', (tokenId: number, price: number) => {
  cy.get(`[data-cy=nft-${tokenId}]`).click()
  cy.get('[data-cy=list-nft-button]').click()
  cy.get('[data-cy=nft-price-input]').type(price.toString())
  cy.get('[data-cy=confirm-listing-button]').click()
  cy.get('[data-cy=listing-success-message]').should('be.visible')
})

// Data setup commands;
Cypress.Commands.add('setupTestData', () => {
  cy.request('POST', `${Cypress.env('apiUrl')}/test/setup`)
})

Cypress.Commands.add('cleanupTestData', () => {
  cy.request('POST', `${Cypress.env('apiUrl')}/test/cleanup`)
}) 