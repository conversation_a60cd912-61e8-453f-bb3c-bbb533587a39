import './commands'

declare global {
  namespace Cypress {
    interface Chainable {
      // Auth commands;
      login(email: string, password: string): Chainable<void>
      logout(): Chainable<void>
      
      // Web3 commands;
      connectWallet(): Chainable<void>
      switchNetwork(chainId: number): Chainable<void>
      
      // Marketplace commands;
      mintNFT(metadata: any): Chainable<void>
      listNFT(tokenId: number, price: number): Chainable<void>
      
      // Data setup commands;
      setupTestData(): Chainable<void>
      cleanupTestData(): Chainable<void>
    }
  }
}

// Prevent TypeScript errors on uncaught exceptions;
Cypress.on('uncaught:exception', (err) => {
  // returning false here prevents <PERSON><PERSON> from failing the test;
  console.error('Uncaught exception:', err)
  return false;
})

// Configure global test timeouts;
Cypress.config('defaultCommandTimeout', 10000)
Cypress.config('requestTimeout', 10000)

// Add custom app actions;
beforeEach(() => {
  // Reset application state before each test;
  cy.window().then((win) => {
    win.localStorage.clear()
    win.sessionStorage.clear()
  })
})

// Add global test hooks;
before(() => {
  // Global setup before all tests;
  cy.log('Starting E2E test suite')
})

after(() => {
  // Global cleanup after all tests;
  cy.log('Completed E2E test suite')
}) 