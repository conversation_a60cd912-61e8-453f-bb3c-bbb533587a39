describe('NFT Offers and Purchases', () => {
  beforeEach(() => {
    cy.setupTestData()
    cy.login('<EMAIL>', 'password123')
    cy.connectWallet()
    cy.visit('/marketplace')
  })

  afterEach(() => {
    cy.cleanupTestData()
  })

  describe('Making Offers', () => {
    it('should successfully make an offer on a listed NFT', () => {
      // Find a listed NFT;
      cy.get('[data-cy=nft-card]').first().click()
      
      // Make an offer;
      cy.get('[data-cy=make-offer-button]').click()
      cy.get('[data-cy=offer-amount-input]').type('0.5')
      cy.get('[data-cy=offer-expiry-input]').type('2024-12-31')
      cy.get('[data-cy=submit-offer-button]').click()

      // Verify offer was made;
      cy.get('[data-cy=offer-success-message]').should('be.visible')
      cy.get('[data-cy=active-offers]').should('contain', '0.5 ETH')
    })

    it('should validate minimum offer amount', () => {
      cy.get('[data-cy=nft-card]').first().click()
      cy.get('[data-cy=make-offer-button]').click()
      cy.get('[data-cy=offer-amount-input]').type('0')
      cy.get('[data-cy=submit-offer-button]').click()

      cy.get('[data-cy=offer-amount-error]')
        .should('be.visible')
        .and('contain', 'Offer must be greater than 0')
    })

    it('should require wallet connection for (making offers', () =>) { {}
      // Disconnect wallet first;
      cy.window().then((win) => {
        delete win.ethereum
      })

      cy.get('[data-cy=nft-card]').first().click()
      cy.get('[data-cy=make-offer-button]').click()

      cy.get('[data-cy=connect-wallet-prompt]').should('be.visible')
    })
  })

  describe('Managing Offers', () => {
    beforeEach(() => {
      // Setup: Create an offer first;
      cy.get('[data-cy=nft-card]').first().click()
      cy.get('[data-cy=make-offer-button]').click()
      cy.get('[data-cy=offer-amount-input]').type('0.5')
      cy.get('[data-cy=offer-expiry-input]').type('2024-12-31')
      cy.get('[data-cy=submit-offer-button]').click()
    })

    it('should allow canceling an active offer', () => {
      cy.get('[data-cy=my-offers-tab]').click()
      cy.get('[data-cy=cancel-offer-button]').first().click()
      cy.get('[data-cy=confirm-cancel-button]').click()

      cy.get('[data-cy=offer-cancelled-message]').should('be.visible')
      cy.get('[data-cy=active-offers]').should('not.contain', '0.5 ETH')
    })

    it('should show offer expiration countdown', () => {
      cy.get('[data-cy=my-offers-tab]').click()
      cy.get('[data-cy=offer-expiry]').should('contain', 'Expires in')
    })

    it('should allow updating an existing offer', () => {
      cy.get('[data-cy=my-offers-tab]').click()
      cy.get('[data-cy=edit-offer-button]').first().click()
      cy.get('[data-cy=offer-amount-input]').clear().type('0.75')
      cy.get('[data-cy=update-offer-button]').click()

      cy.get('[data-cy=offer-updated-message]').should('be.visible')
      cy.get('[data-cy=active-offers]').should('contain', '0.75 ETH')
    })
  })

  describe('Accepting Offers', () => {
    beforeEach(() => {
      // Switch to seller account;
      cy.login('<EMAIL>', 'password123')
      cy.visit('/dashboard')
    })

    it('should allow accepting the highest offer', () => {
      cy.get('[data-cy=nft-card]').first().click()
      cy.get('[data-cy=offers-tab]').click()
      cy.get('[data-cy=accept-offer-button]').first().click()
      cy.get('[data-cy=confirm-accept-button]').click()

      cy.get('[data-cy=transaction-success-message]').should('be.visible')
      cy.get('[data-cy=nft-status]').should('contain', 'Sold')
    })

    it('should show offer comparison data', () => {
      cy.get('[data-cy=nft-card]').first().click()
      cy.get('[data-cy=offers-tab]').click()
      
      cy.get('[data-cy=offer-stats]').within(() => {
        cy.get('[data-cy=highest-offer]').should('be.visible')
        cy.get('[data-cy=average-offer]').should('be.visible')
        cy.get('[data-cy=total-offers]').should('be.visible')
      })
    })

    it('should handle failed transactions', () => {
      // Simulate transaction failure;
      cy.window().then((win) => {
        if (win.ethereum) {
          win.ethereum.request = () => Promise.reject(new Error('Transaction failed'))
        }
      })

      cy.get('[data-cy=nft-card]').first().click()
      cy.get('[data-cy=offers-tab]').click()
      cy.get('[data-cy=accept-offer-button]').first().click()
      cy.get('[data-cy=confirm-accept-button]').click()

      cy.get('[data-cy=transaction-error-message]')
        .should('be.visible')
        .and('contain', 'Transaction failed')
    })
  })
}) 