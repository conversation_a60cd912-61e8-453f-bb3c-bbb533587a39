describe('Authentication Flow', () => {
  beforeEach(() => {
    cy.visit('/login')
  })

  it('should successfully login with valid credentials', () => {
    cy.login('<EMAIL>', 'password123')
    cy.url().should('eq', `${Cypress.config().baseUrl}/dashboard`)
    cy.get('[data-cy=user-profile]').should('be.visible')
  })

  it('should show error with invalid credentials', () => {
    cy.login('<EMAIL>', 'wrongpassword')
    cy.get('[data-cy=login-error]')
      .should('be.visible')
      .and('contain', 'Invalid email or password')
    cy.url().should('include', '/login')
  })

  it('should successfully logout', () => {
    cy.login('<EMAIL>', 'password123')
    cy.logout()
    cy.url().should('include', '/login')
    cy.visit('/dashboard')
    cy.url().should('include', '/login') // Should redirect to login
  })

  it('should validate required fields', () => {
    cy.get('[data-cy=login-button]').click()
    cy.get('[data-cy=email-error]').should('be.visible')
    cy.get('[data-cy=password-error]').should('be.visible')
  })

  it('should validate email format', () => {
    cy.get('[data-cy=email-input]').type('invalidemailformat')
    cy.get('[data-cy=password-input]').type('password123')
    cy.get('[data-cy=login-button]').click()
    cy.get('[data-cy=email-error]')
      .should('be.visible')
      .and('contain', 'Invalid email format')
  })

  it('should maintain session after page refresh', () => {
    cy.login('<EMAIL>', 'password123')
    cy.reload()
    cy.url().should('not.include', '/login')
    cy.get('[data-cy=user-profile]').should('be.visible')
  })
}) 