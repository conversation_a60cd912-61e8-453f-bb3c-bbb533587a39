# Build stage for React frontend
FROM node:18-alpine as frontend-build

WORKDIR /frontend

# Copy package files
COPY client/package*.json ./

# Install dependencies
RUN npm install

# Copy client source code
COPY client/ ./

# Build frontend
RUN npm run build

# Backend stage
FROM node:18-alpine

WORKDIR /app

# Copy backend package files
COPY package*.json ./

# Install backend dependencies
RUN npm install

# Copy backend files
COPY . .

# Remove existing client/build directory if it exists
RUN rm -rf /app/client/build

# Copy frontend build from build stage
COPY --from=frontend-build /frontend/build /app/client/build

EXPOSE 3001

CMD ["npm", "start"]
