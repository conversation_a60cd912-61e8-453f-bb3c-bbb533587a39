apiVersion: apps/v1
kind: Deployment
metadata:
  name: search-transport-deployment
  labels:
    app: search-transport
spec:
  replicas: 2
  selector:
    matchLabels:
      app: search-transport
  template:
    metadata:
      labels:
        app: search-transport
    spec:
      containers:
      - name: search-transport-container
        image: search-transport-service:latest
        ports:
        - containerPort: 8080
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
