import React from 'react';
import useReservation from '../hooks/useReservation.js';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Stack,
  Chip,
  CircularProgress,
  Alert,
  Grid,
} from '@mui/material';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

const getStatusColor = (status) => {
  switch (status) {
    case 'confirmed':
      return 'success';
    case 'pending':
      return 'warning';
    case 'cancelled':
      return 'error';
    default:
      return 'default';
  }
};

const ReservationCard = ({ reservation, onCancel }) => {
  const formattedDate = format(new Date(reservation.pickupDateTime), 'PPPp', { locale: fr });

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Stack spacing={2}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" component="div">
              Réservation #{reservation.id}
            </Typography>
            <Chip
              label={reservation.status}
              color={getStatusColor(reservation.status)}
              size="small"
            />
          </Box>

          <Typography variant="body2" color="text.secondary">
            <strong>Date et heure :</strong> {formattedDate}
          </Typography>

          <Typography variant="body2" color="text.secondary">
            <strong>Départ :</strong> {reservation.pickupLocation}
          </Typography>

          <Typography variant="body2" color="text.secondary">
            <strong>Destination :</strong> {reservation.dropoffLocation}
          </Typography>

          <Typography variant="body2" color="text.secondary">
            <strong>Passagers :</strong> {reservation.passengers}
          </Typography>

          {reservation.specialRequests && (
            <Typography variant="body2" color="text.secondary">
              <strong>Demandes spéciales :</strong> {reservation.specialRequests}
            </Typography>
          )}

          {reservation.status !== 'cancelled' && (
            <Button
              variant="outlined"
              color="error"
              onClick={() => onCancel(reservation.id)}
              size="small"
            >
              Annuler la réservation
            </Button>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
};

const ReservationList = ({ userId }) => {
  const { reservations, loading, error, cancelReservation, getUserReservations } = useReservation();

  React.useEffect(() => {
    if (userId) {
      getUserReservations(userId);
    }
  }, [userId]);

  const handleCancelReservation = async (reservationId) => {
    if (window.confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')) {
      await cancelReservation(reservationId);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (reservations.length === 0) {
    return (
      <Alert severity="info" sx={{ mt: 2, mb: 2 }}>
        Vous n'avez aucune réservation pour le moment.
      </Alert>
    );
  }

  return (
    <Grid container spacing={3}>
      {reservations.map((reservation) => (
        <Grid item xs={12} md={6} lg={4} key={reservation.id}>
          <ReservationCard
            reservation={reservation}
            onCancel={handleCancelReservation}
          />
        </Grid>
      ))}
    </Grid>
  );
};

export default ReservationList;
