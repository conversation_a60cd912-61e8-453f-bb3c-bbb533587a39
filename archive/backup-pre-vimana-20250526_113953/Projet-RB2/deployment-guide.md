# Guide de Déploiement - Plateforme RB2

Ce guide explique comment déployer correctement la plateforme RB2 sur Kubernetes, en tenant compte des leçons apprises lors des déploiements précédents.

## Prérequis

- Cluster Kubernetes fonctionnel (version 1.19+)
- kubectl configuré pour communiquer avec votre cluster
- Helm 3 installé
- Docker pour créer et pousser les images

## 1. Préparation du Cluster

### Création du Namespace

```bash
kubectl create namespace retreat-and-be
```

### Installation des CRDs Nécessaires

#### CRDs Istio

```bash
# Télécharger et installer Istio
curl -L https://istio.io/downloadIstio | ISTIO_VERSION=1.18.2 sh -
cd istio-1.18.2
export PATH=$PWD/bin:$PATH

# Installer Istio avec les CRDs
istioctl install --set profile=demo -y
```

#### CRDs Flagger (pour les déploiements Canary)

```bash
# Installer les CRDs Flagger
kubectl apply -f https://raw.githubusercontent.com/fluxcd/flagger/main/artifacts/flagger/crd.yaml
```

## 2. Préparation des Images Docker

Créez et publiez les images Docker pour tous vos services. Pour chaque service, suivez ces étapes :

```bash
# Dans le répertoire de chaque service
docker build -t votre-registry/rb2/service-name:version .
docker push votre-registry/rb2/service-name:version
```

Assurez-vous de mettre à jour les fichiers `values.yaml` de chaque chart Helm pour référencer ces images :

```yaml
image:
  repository: votre-registry/rb2/service-name
  tag: version
  pullPolicy: Always
```

## 3. Configuration des Charts Helm

### Vérification des Helpers Helm

Assurez-vous que chaque chart possède les fichiers d'helpers nécessaires, en particulier :

- `charts/monitoring/templates/_helpers.tpl` (pour la fonction "service")
- `charts/istio/templates/_helpers.tpl` (pour la fonction "istio.labels")
- `charts/analyzer/templates/_helpers.tpl` (pour la fonction "analyzer.labels")

### Correction des Templates Problématiques

1. **Monitoring** : Corrigez le template `grafana-dashboards.yaml`

```yaml
apiVersion: monitoring.grafana.com/v1alpha1
kind: GrafanaDashboard
metadata:
  name: microservices-overview
  labels:
    grafana_dashboard: "true"
spec:
  json: |
    {
      "title": "Microservices Metrics Overview",
      "panels": [
        {
          "targets": [{
            "expr": "...",
            "legendFormat": "{{ '{{service}}' }}"  # Notez l'échappement correct
          }]
        }
      ]
    }
```

2. **Analyzer** : Corrigez le template `grafana-dashboard.yaml`

```yaml
apiVersion: monitoring.grafana.com/v1alpha1
kind: GrafanaDashboard
metadata:
  name: {{ .Release.Name }}-analyzer-metrics
  labels:
    {{- include "analyzer.labels" . | nindent 4 }}
spec:
  json: |
    {
      "panels": [
        {
          "targets": [{
            "expr": "...",
            "legendFormat": "{{ '{{path}}' }}"  # Notez l'échappement correct
          }]
        }
      ]
    }
```

## 4. Déploiement des Services

### Services d'Infrastructure

```bash
helm upgrade --install monitoring ./charts/monitoring --namespace retreat-and-be
helm upgrade --install istio ./charts/istio --namespace retreat-and-be
```

### Services Core

```bash
helm upgrade --install backend ./charts/Backend --namespace retreat-and-be
helm upgrade --install frontend ./charts/Frontend --namespace retreat-and-be
helm upgrade --install analyzer ./charts/Analyzer --namespace retreat-and-be
helm upgrade --install decentralized-storage ./charts/Decentralized-Storage --namespace retreat-and-be
```

### Services de Sécurité

```bash
helm upgrade --install security-service ./charts/Security-Service --namespace retreat-and-be
helm upgrade --install keycloak ./charts/keycloak --namespace retreat-and-be
```

### Autres Services

Déployez les services restants dans l'ordre approprié selon les dépendances.

## 5. Vérification du Déploiement

### Vérifier l'État des Pods

```bash
kubectl get pods -n retreat-and-be
```

### Déboguer les Pods Problématiques

Pour les pods en état d'erreur :

```bash
kubectl describe pod <pod-name> -n retreat-and-be
kubectl logs <pod-name> -n retreat-and-be
```

### Corriger les Problèmes d'Images

Si vous avez des pods en état `ImagePullBackOff`, utilisez cette commande pour remplacer l'image par une image accessible :

```bash
kubectl -n retreat-and-be patch deployment <deployment-name> -p '{"spec":{"template":{"spec":{"containers":[{"name":"<container-name>","image":"nginx:alpine"}]}}}}'
```

## 6. Accès aux Services

### Configuration des Ingress

Assurez-vous que les Ingress sont correctement configurés pour accéder à vos services depuis l'extérieur du cluster.

### Portforwarding pour les Tests

Pour tester un service spécifique sans passer par l'Ingress :

```bash
kubectl port-forward svc/<service-name> <local-port>:<service-port> -n retreat-and-be
```

## Résolution des Problèmes Courants

### Problèmes de CRDs

```bash
# Vérifier si un CRD est installé
kubectl get crd | grep <nom-du-crd>

# Installer manuellement un CRD
kubectl apply -f <fichier-crd.yaml>
```

### Problèmes de Ressources

```bash
# Vérifier l'utilisation des ressources
kubectl top nodes
kubectl top pods -n retreat-and-be
```

### Problèmes de Registry

```bash
# Créer un secret pour accéder à un registry privé
kubectl create secret docker-registry regcred \
  --docker-server=<server> \
  --docker-username=<username> \
  --docker-password=<password> \
  --docker-email=<email> \
  -n retreat-and-be
```

### Redémarrer un Déploiement

```bash
kubectl rollout restart deployment/<deployment-name> -n retreat-and-be
```

## Maintenance

Consultez régulièrement les logs et surveillez l'état des pods :

```bash
# Surveiller les pods en temps réel
kubectl get pods -n retreat-and-be -w

# Afficher les logs d'un service
kubectl logs -f -l app=<service-name> -n retreat-and-be
``` 