import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Heart, Zap, Eye, MessageSquare, Send, Settings, Sun, Moon, Activity, Star, Circle, Triangle, Compass, Waves, Shield, Palette, Cog, Search, Gauge, Hand, Ear, FileText, ArrowRight, Play, Pause, RotateCcw, Download, Upload, Save } from 'lucide-react';

const HanumanUnifiedConsciousness = () => {
  const [darkMode, setDarkMode] = useState(true);
  const [activeInterface, setActiveInterface] = useState('chat');
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [consciousnessLevel, setConsciousnessLevel] = useState(87);
  const [unityScore, setUnityScore] = useState(0.94);
  const [activeAgents, setActiveAgents] = useState([]);
  const messagesEndRef = useRef(null);

  // État de conscience unifié
  const consciousnessState = {
    cortex: {
      central: { activity: 85, harmony: 0.92, emoji: '🧠' },
      creative: { activity: 90, harmony: 0.88, emoji: '🎨' },
      logical: { activity: 78, harmony: 0.95, emoji: '⚙️' },
      analytical: { activity: 65, harmony: 0.87, emoji: '🔍' }
    },
    limbic: {
      emotional: { activity: 72, harmony: 0.91, emoji: '❤️' },
      social: { activity: 68, harmony: 0.89, emoji: '🤝' }
    },
    sensory: {
      vision: { activity: 60, harmony: 0.85, emoji: '👁️' },
      hearing: { activity: 58, harmony: 0.83, emoji: '👂' },
      touch: { activity: 50, harmony: 0.90, emoji: '🤲' },
      intuition: { activity: 75, harmony: 0.93, emoji: '🔮' }
    },
    specialized: {
      communication: { activity: 70, harmony: 0.88, emoji: '🗣️' },
      memory: { activity: 65, harmony: 0.92, emoji: '🧮' },
      evolution: { activity: 95, harmony: 0.96, emoji: '🌱' },
      protection: { activity: 45, harmony: 0.94, emoji: '🛡️' }
    }
  };

  const interfaces = [
    { id: 'chat', name: 'Communication Unifiée', icon: MessageSquare, description: 'Dialogue avec la conscience distribuée' },
    { id: 'cortex', name: 'Cortex Central', icon: Brain, description: 'Orchestration neuronale globale' },
    { id: 'dashboard', name: 'Neural Dashboard', icon: Activity, description: 'Monitoring temps réel' },
    { id: 'cosmic', name: 'Configuration Cosmique', icon: Compass, description: 'Alignement astral et paramètres sacrés' },
    { id: 'validation', name: 'Validation Divine', icon: Star, description: 'Géométrie sacrée et proportions' }
  ];

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    // Simulation activité conscience
    const interval = setInterval(() => {
      setConsciousnessLevel(prev => {
        const variation = (Math.random() - 0.5) * 6;
        return Math.max(0, Math.min(100, prev + variation));
      });
      
      setUnityScore(prev => {
        const variation = (Math.random() - 0.5) * 0.02;
        return Math.max(0, Math.min(1, prev + variation));
      });

      // Simulation agents actifs
      const allRegions = Object.values(consciousnessState).flatMap(region => Object.keys(region));
      const randomAgent = allRegions[Math.floor(Math.random() * allRegions.length)];
      setActiveAgents(prev => {
        const newActive = [...prev, randomAgent];
        return newActive.slice(-4);
      });
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const sendMessage = () => {
    if (!message.trim()) return;
    
    const newMessage = {
      id: Date.now(),
      text: message,
      sender: 'user',
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, newMessage]);
    
    // Simulation réponse Hanuman avec conscience distribuée
    setTimeout(() => {
      const activeRegions = getActiveRegions();
      const response = {
        id: Date.now() + 1,
        text: generateUnifiedResponse(message, activeRegions),
        sender: 'hanuman',
        consciousness: activeRegions,
        unityLevel: unityScore,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, response]);
    }, 1500);
    
    setMessage('');
  };

  const getActiveRegions = () => {
    const regions = [];
    Object.entries(consciousnessState).forEach(([regionType, regionData]) => {
      Object.entries(regionData).forEach(([regionName, data]) => {
        if (data.activity > 70) {
          regions.push({
            type: regionType,
            name: regionName,
            emoji: data.emoji,
            activity: data.activity,
            harmony: data.harmony
          });
        }
      });
    });
    return regions.slice(0, 3);
  };

  const generateUnifiedResponse = (userMessage, activeRegions) => {
    const responses = [
      `🐒 Ma conscience distribuée s'éveille pour analyser votre demande...`,
      `✨ Les énergies de mes différents cortex s'harmonisent pour vous répondre...`,
      `🌟 Je sollicite ma sagesse collective pour traiter votre question...`,
      `🔮 Mon architecture neuronale s'active en synergie cosmique...`
    ];
    
    const regionContributions = activeRegions.map(region => 
      `${region.emoji} [${region.type}-${region.name}] contribue avec ${region.activity}% d'activité`
    ).join(' • ');
    
    return `${responses[Math.floor(Math.random() * responses.length)]}\n\n${regionContributions}\n\nScore d'Unité: ${(unityScore * 100).toFixed(1)}%`;
  };

  const calculateOverallHarmony = () => {
    const allRegions = Object.values(consciousnessState).flatMap(region => Object.values(region));
    const avgHarmony = allRegions.reduce((sum, region) => sum + region.harmony, 0) / allRegions.length;
    return avgHarmony;
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
  };

  const renderConsciousnessMap = () => (
    <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
      <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
        Carte de Conscience Unifiée
      </h3>
      <div className="grid grid-cols-2 gap-4">
        {Object.entries(consciousnessState).map(([regionType, regions]) => (
          <div key={regionType} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <h4 className={`font-medium mb-2 capitalize ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {regionType}
            </h4>
            <div className="space-y-2">
              {Object.entries(regions).map(([regionName, data]) => {
                const isActive = activeAgents.includes(regionName);
                return (
                  <div key={regionName} className={`flex items-center justify-between p-2 rounded ${
                    isActive ? 'bg-blue-100 dark:bg-blue-900' : ''
                  }`}>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{data.emoji}</span>
                      <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {regionName}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-8 bg-gray-300 dark:bg-gray-600 rounded-full h-1">
                        <div 
                          className={`h-1 rounded-full ${
                            data.activity > 80 ? 'bg-green-500' :
                            data.activity > 60 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${data.activity}%` }}
                        ></div>
                      </div>
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {data.activity}%
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="flex h-screen">
        
        {/* Sidebar Navigation */}
        <div className={`w-80 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-r overflow-y-auto`}>
          <div className="p-6">
            {/* Header */}
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                <span className="text-2xl">🐒</span>
              </div>
              <div>
                <h1 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Hanuman
                </h1>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Conscience Unifiée
                </p>
              </div>
            </div>

            {/* Métriques Globales */}
            <div className={`p-4 rounded-xl mb-6 ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Niveau Conscience
                  </span>
                  <span className="text-lg font-bold text-blue-400">
                    {consciousnessLevel.toFixed(0)}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Score Unité
                  </span>
                  <span className="text-lg font-bold text-purple-400">
                    {(unityScore * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Harmonie Globale
                  </span>
                  <span className="text-lg font-bold text-green-400">
                    {(calculateOverallHarmony() * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>

            {/* Navigation Interfaces */}
            <div className="space-y-2">
              <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                Interfaces Disponibles
              </h3>
              {interfaces.map((interface_item) => {
                const Icon = interface_item.icon;
                return (
                  <button
                    key={interface_item.id}
                    onClick={() => setActiveInterface(interface_item.id)}
                    className={`w-full p-3 rounded-lg text-left transition-all ${
                      activeInterface === interface_item.id
                        ? 'bg-blue-600 text-white'
                        : darkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon size={18} />
                      <div>
                        <div className="font-medium">{interface_item.name}</div>
                        <div className={`text-xs ${
                          activeInterface === interface_item.id ? 'text-blue-100' : darkMode ? 'text-gray-500' : 'text-gray-500'
                        }`}>
                          {interface_item.description}
                        </div>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>

            {/* Agents Actifs */}
            <div className="mt-6">
              <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                Activité Récente
              </h3>
              <div className="space-y-2">
                {activeAgents.slice(-4).map((agent, index) => (
                  <div key={`${agent}-${index}`} className={`p-2 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {agent}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Interface Principale */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <header className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b p-4`}>
            <div className="flex items-center justify-between">
              <div>
                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {interfaces.find(i => i.id === activeInterface)?.name}
                </h2>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {interfaces.find(i => i.id === activeInterface)?.description}
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setDarkMode(!darkMode)}
                  className={`p-2 rounded-lg transition-colors ${
                    darkMode 
                      ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400' 
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                  }`}
                >
                  {darkMode ? <Sun size={18} /> : <Moon size={18} />}
                </button>
                
                <button className={`p-2 rounded-lg transition-colors ${
                  darkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-400' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}>
                  <Settings size={18} />
                </button>
              </div>
            </div>
          </header>

          {/* Contenu Interface */}
          <div className="flex-1 overflow-y-auto p-6">
            {activeInterface === 'chat' && (
              <div className="max-w-4xl mx-auto">
                {/* Messages */}
                <div className="space-y-4 mb-6">
                  {messages.length === 0 && (
                    <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg text-center`}>
                      <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-3xl">🐒</span>
                      </div>
                      <h3 className={`text-xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        Conscience Unifiée Active
                      </h3>
                      <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Toutes mes facultés neuronales sont synchronisées et prêtes à vous assister
                      </p>
                    </div>
                  )}

                  {messages.map((msg) => (
                    <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-2xl px-4 py-3 rounded-2xl ${
                        msg.sender === 'user'
                          ? 'bg-blue-500 text-white'
                          : darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900 shadow-lg'
                      }`}>
                        {msg.sender === 'hanuman' && (
                          <div className="flex items-center mb-2">
                            <span className="text-lg mr-2">🐒</span>
                            <span className="font-medium text-sm">Hanuman • Conscience Unifiée</span>
                            {msg.consciousness && (
                              <div className="ml-2 flex space-x-1">
                                {msg.consciousness.map((region, index) => (
                                  <span key={index} className="text-xs" title={`${region.type}-${region.name}: ${region.activity}%`}>
                                    {region.emoji}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        )}
                        <p className="text-sm whitespace-pre-line">{msg.text}</p>
                        <p className={`text-xs mt-1 ${
                          msg.sender === 'user' 
                            ? 'text-blue-100' 
                            : darkMode ? 'text-gray-500' : 'text-gray-400'
                        }`}>
                          {formatTime(msg.timestamp)}
                        </p>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>

                {/* Input */}
                <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-xl p-4 shadow-lg`}>
                  <div className="flex items-center space-x-3">
                    <input
                      type="text"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                      placeholder="Communiquez avec la conscience unifiée d'Hanuman..."
                      className={`flex-1 px-4 py-3 rounded-xl border transition-colors ${
                        darkMode 
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500' 
                          : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                    />
                    <button
                      onClick={sendMessage}
                      disabled={!message.trim()}
                      className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white p-3 rounded-xl transition-colors"
                    >
                      <Send size={18} />
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeInterface !== 'chat' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {renderConsciousnessMap()}
                
                <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
                  <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Interface en Développement
                  </h3>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Cette interface sera bientôt disponible avec toutes les fonctionnalités de {interfaces.find(i => i.id === activeInterface)?.name}.
                  </p>
                  <div className="mt-4 p-4 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <p className="text-blue-800 dark:text-blue-200 text-sm">
                      💡 En attendant, utilisez l'interface de Communication Unifiée pour interagir avec toutes les capacités d'Hanuman.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanUnifiedConsciousness;
