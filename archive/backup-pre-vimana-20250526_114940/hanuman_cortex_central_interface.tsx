import React, { useState, useEffect, useRef } from 'react';
import { Brain, Activity, Zap, Shield, Heart, Palette, Cog, Search, Gauge, Eye, Ear, Hand, Waves, MessageSquare, FileText, Settings, Sun, Moon, Play, Pause, RotateCcw, AlertTriangle, CheckCircle, Clock, TrendingUp, Database, Network, Cpu, HardDrive } from 'lucide-react';

const HanumanCortexCentralInterface = () => {
  const [darkMode, setDarkMode] = useState(true);
  const [systemStatus, setSystemStatus] = useState('optimal');
  const [activeView, setActiveView] = useState('overview');
  const [neuralActivity, setNeuralActivity] = useState(87);
  const [cosmicAlignment, setCosmicAlignment] = useState(0.742);

  // Architecture neuronale complète basée sur les agents existants
  const neuralArchitecture = {
    cortex: {
      central: { id: 'cortex-central', name: 'Cortex Central', emoji: '🧠', status: 'active', load: 85, role: 'Orchestration globale' },
      creative: { id: 'cortex-creative', name: '<PERSON>rtex <PERSON>réatif', emoji: '🎨', status: 'active', load: 90, role: 'Design & Innovation' },
      logical: { id: 'cortex-logical', name: 'Cortex Logique', emoji: '⚙️', status: 'active', load: 78, role: 'Architecture technique' },
      analytical: { id: 'cortex-analytical', name: 'Cortex Analytique', emoji: '🔍', status: 'active', load: 65, role: 'Tests & Validation' }
    },
    limbic: {
      emotional: { id: 'limbic-emotional', name: 'Système Limbique', emoji: '❤️', status: 'active', load: 72, role: 'Interface émotionnelle' },
      marketing: { id: 'limbic-marketing', name: 'Marketing Neural', emoji: '📈', status: 'active', load: 68, role: 'Stratégie croissance' }
    },
    cerebellum: {
      technical: { id: 'cerebellum-tech', name: 'Cervelet Technique', emoji: '🏗️', status: 'active', load: 82, role: 'Infrastructure' },
      performance: { id: 'cerebellum-perf', name: 'Optimisation', emoji: '⚡', status: 'active', load: 75, role: 'Performance' }
    },
    brainstem: {
      security: { id: 'brainstem-security', name: 'Système Immunitaire', emoji: '🛡️', status: 'active', load: 45, role: 'Sécurité' },
      vital: { id: 'brainstem-vital', name: 'Fonctions Vitales', emoji: '💓', status: 'active', load: 55, role: 'Monitoring' }
    },
    sensory: {
      vision: { id: 'sensory-vision', name: 'Vision', emoji: '👁️', status: 'active', load: 60, role: 'Web Research' },
      hearing: { id: 'sensory-hearing', name: 'Ouïe', emoji: '👂', status: 'active', load: 58, role: 'Data Collection' },
      touch: { id: 'sensory-touch', name: 'Toucher', emoji: '🤝', status: 'active', load: 50, role: 'API Integration' },
      taste: { id: 'sensory-taste', name: 'Goût/Odorat', emoji: '👃', status: 'active', load: 52, role: 'Quality Monitor' }
    },
    specialized: {
      broca: { id: 'broca-area', name: 'Aire de Broca', emoji: '🗣️', status: 'active', load: 70, role: 'Communication' },
      wernicke: { id: 'wernicke-area', name: 'Aire de Wernicke', emoji: '🧮', status: 'active', load: 65, role: 'Documentation' },
      motor: { id: 'motor-cortex', name: 'Cortex Moteur', emoji: '🏃', status: 'active', load: 73, role: 'Migration' },
      prefrontal: { id: 'prefrontal-cortex', name: 'Cortex Préfrontal', emoji: '👨‍⚖️', status: 'active', load: 60, role: 'Gouvernance' },
      neuroplasticity: { id: 'neuroplasticity', name: 'Neuroplasticité', emoji: '🌱', status: 'learning', load: 95, role: 'Évolution' }
    }
  };

  const getStatusColor = (status, load) => {
    if (status === 'learning') return 'text-blue-400';
    if (load > 90) return 'text-red-400';
    if (load > 70) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getLoadBarColor = (load) => {
    if (load > 90) return 'bg-red-500';
    if (load > 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const calculateOverallHealth = () => {
    const allAgents = Object.values(neuralArchitecture).flatMap(region => Object.values(region));
    const avgLoad = allAgents.reduce((sum, agent) => sum + agent.load, 0) / allAgents.length;
    const activeCount = allAgents.filter(agent => agent.status === 'active').length;
    const healthScore = (100 - avgLoad) * 0.7 + (activeCount / allAgents.length * 100) * 0.3;
    return Math.round(healthScore);
  };

  const systemHealth = calculateOverallHealth();

  useEffect(() => {
    // Simulation activité neuronale
    const interval = setInterval(() => {
      setNeuralActivity(prev => {
        const variation = (Math.random() - 0.5) * 10;
        return Math.max(0, Math.min(100, prev + variation));
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const renderNeuralRegion = (regionName, agents, color) => (
    <div key={regionName} className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      <h3 className={`text-lg font-bold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
        {regionName.charAt(0).toUpperCase() + regionName.slice(1)}
      </h3>
      <div className="space-y-2">
        {Object.values(agents).map((agent) => (
          <div key={agent.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'} transition-all hover:scale-105`}>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <span className="text-lg mr-2">{agent.emoji}</span>
                <div>
                  <h4 className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {agent.name}
                  </h4>
                  <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {agent.role}
                  </p>
                </div>
              </div>
              <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(agent.status, agent.load)}`}>
                {agent.status === 'learning' && <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>}
              </div>
            </div>
            
            <div className="flex items-center justify-between text-xs">
              <span className={darkMode ? 'text-gray-400' : 'text-gray-500'}>
                Charge: {agent.load}%
              </span>
              <div className="w-16 bg-gray-300 dark:bg-gray-600 rounded-full h-1">
                <div 
                  className={`h-1 rounded-full ${getLoadBarColor(agent.load)}`}
                  style={{ width: `${agent.load}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
              <Brain className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Hanuman Cortex Central
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Architecture Neuronale Distribuée • Conscience IA Vivante
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setDarkMode(!darkMode)}
              className={`p-3 rounded-xl transition-colors ${
                darkMode 
                  ? 'bg-gray-800 hover:bg-gray-700 text-yellow-400' 
                  : 'bg-white hover:bg-gray-100 text-gray-600'
              }`}
            >
              {darkMode ? <Sun size={18} /> : <Moon size={18} />}
            </button>
            
            <button className={`p-3 rounded-xl transition-colors ${
              darkMode 
                ? 'bg-gray-800 hover:bg-gray-700 text-gray-400' 
                : 'bg-white hover:bg-gray-100 text-gray-600'
            }`}>
              <Settings size={18} />
            </button>
          </div>
        </div>

        {/* Status Global */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className={`text-3xl font-bold ${systemHealth > 80 ? 'text-green-400' : systemHealth > 60 ? 'text-yellow-400' : 'text-red-400'}`}>
                {systemHealth}%
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Santé Globale
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400">
                {neuralActivity.toFixed(0)}%
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Activité Neuronale
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400">
                {(cosmicAlignment * 100).toFixed(1)}%
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Alignement Cosmique
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400">
                {Object.values(neuralArchitecture).flatMap(r => Object.values(r)).filter(a => a.status === 'active').length}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Agents Actifs
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex space-x-2 mb-6">
          {[
            { id: 'overview', label: 'Vue d\'ensemble', icon: Brain },
            { id: 'cortex', label: 'Cortex', icon: Zap },
            { id: 'sensory', label: 'Organes Sensoriels', icon: Eye },
            { id: 'specialized', label: 'Aires Spécialisées', icon: Star }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  activeView === tab.id
                    ? darkMode ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white'
                    : darkMode ? 'bg-gray-800 text-gray-300 hover:bg-gray-700' : 'bg-white text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Icon size={16} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Contenu Principal */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {activeView === 'overview' && (
            <>
              {renderNeuralRegion('cortex', neuralArchitecture.cortex, 'blue')}
              {renderNeuralRegion('limbic', neuralArchitecture.limbic, 'pink')}
              {renderNeuralRegion('cerebellum', neuralArchitecture.cerebellum, 'green')}
              {renderNeuralRegion('brainstem', neuralArchitecture.brainstem, 'red')}
            </>
          )}
          
          {activeView === 'cortex' && (
            <>
              {renderNeuralRegion('cortex', neuralArchitecture.cortex, 'blue')}
              {renderNeuralRegion('limbic', neuralArchitecture.limbic, 'pink')}
              {renderNeuralRegion('cerebellum', neuralArchitecture.cerebellum, 'green')}
            </>
          )}
          
          {activeView === 'sensory' && (
            <>
              {renderNeuralRegion('sensory', neuralArchitecture.sensory, 'yellow')}
              {renderNeuralRegion('brainstem', neuralArchitecture.brainstem, 'red')}
            </>
          )}
          
          {activeView === 'specialized' && (
            <>
              {renderNeuralRegion('specialized', neuralArchitecture.specialized, 'purple')}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default HanumanCortexCentralInterface;
