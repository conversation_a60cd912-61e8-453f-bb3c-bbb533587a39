# Performance MCP Tools (2025)

## PerformanceAnalysis MCP
*Installation: `npx -y @modelcontextprotocol/server-performance-analysis@2025`*

Fournit des capacités d'analyse de performance complètes:

### Outils d'analyse

- **code_performance_profile**: Analyse statique des performances du code
  ```javascript
  const results = await mcp.call("code_performance_profile", {
    codebase: "/path/to/project",
    languages: ["javascript", "typescript", "python"],
    focusAreas: ["algorithms", "memory", "async"],
    threshold: 0.7
  });
  ```

- **database_query_analyzer**: Optimise les requêtes de base de données
  ```javascript
  const results = await mcp.call("database_query_analyzer", {
    projectRoot: "./",
    dbType: "postgresql", // ou mysql, mongodb, etc.
    queryPatterns: ["SELECT", "JOIN", "WHERE"],
    suggestIndexes: true
  });
  ```

- **frontend_performance_audit**: Analyse les performances frontend
  ```javascript
  const results = await mcp.call("frontend_performance_audit", {
    projectRoot: "./",
    framework: "react", // ou vue, angular, etc.
    metrics: ["FCP", "LCP", "CLS", "TTI"],
    generateReport: true
  });
  ```