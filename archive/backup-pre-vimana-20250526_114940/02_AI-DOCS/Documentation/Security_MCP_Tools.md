# Security MCP Tools (2025)

This document outlines the specialized Model Context Protocol (MCP) tools available for security analysis in the 2025 edition of the Agentic Coding Framework.

## Core Security MCP Servers

### SecurityAnalysis MCP
*Installation: `npx -y @modelcontextprotocol/server-security-analysis@2025`*

Provides comprehensive security analysis capabilities:

#### Vulnerability Analysis Tools

- **static_vulnerability_scan**: Performs static code analysis for security vulnerabilities
  ```javascript
  const results = await mcp.call("static_vulnerability_scan", {
    codebase: "/path/to/project",
    languages: ["javascript", "typescript", "python"],
    rulesets: ["owasp-top-10", "cwe-top-25", "sans-top-25"],
    confidence_threshold: 0.7
  });
  ```

- **dynamic_vulnerability_scan**: Executes dynamic analysis to find runtime vulnerabilities
  ```javascript
  const results = await mcp.call("dynamic_vulnerability_scan", {
    target_url: "https://staging-app.example.com",
    authentication: {
      method: "form",
      credentials: { username: "test_user", password: "test_password" }
    },
    scan_depth: "comprehensive"
  });
  ```

- **interactive_vulnerability_scan**: Combines static and dynamic analysis for higher accuracy
  ```javascript
  const results = await mcp.call("interactive_vulnerability_scan", {
    codebase: "/path/to/project",
    target_url: "https://staging-app.example.com",
    correlation_threshold: 0.8
  });
  ```

#### Threat Modeling Tools

- **automated_threat_modeling**: Generates comprehensive threat models
  ```