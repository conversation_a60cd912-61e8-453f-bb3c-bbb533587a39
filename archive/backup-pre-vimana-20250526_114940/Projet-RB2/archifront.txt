Directory structure:
└── frontend/
    ├── README.md
    ├── Dockerfile
    ├── OPTIMIZATION.md
    ├── cypress.config.ts
    ├── index.html
    ├── jest.config.ts
    ├── jest.setup.ts
    ├── nginx.conf
    ├── package.json
    ├── postcss.config.cjs
    ├── postcss.config.js
    ├── tailwind.config.js
    ├── tsconfig.jest.json
    ├── tsconfig.json
    ├── tsconfig.node.json
    ├── tsconfig.paths.json
    ├── vite.config.ts
    ├── .babelrc
    ├── .dockerignore
    ├── .env.example
    ├── .env.local
    ├── .eslintrc.js
    ├── .eslintrc.json
    ├── .gitlab-ci.yml
    ├── .lintstagedrc
    ├── .prettierrc
    ├── .swcrc
    ├── __mocks__/
    │   ├── lucide-react.js
    │   └── react-big-calendar.tsx
    ├── charts/
    │   └── financial-management/
    │       └── values.yaml
    ├── coverage/
    │   ├── coverage-final.json
    │   ├── lcov.info
    │   └── lcov-report/
    │       ├── base.css
    │       ├── block-navigation.js
    │       ├── index.html
    │       ├── prettify.css
    │       ├── prettify.js
    │       ├── sorter.js
    │       └── src/
    │           ├── App.js.html
    │           ├── App.tsx.html
    │           ├── config.ts.html
    │           ├── index.html
    │           ├── serviceWorker.ts.html
    │           ├── theme.js.html
    │           ├── theme.ts.html
    │           ├── api/
    │           │   ├── api.ts.html
    │           │   ├── axiosConfig.ts.html
    │           │   ├── carRental.ts.html
    │           │   ├── community.ts.html
    │           │   ├── index.html
    │           │   ├── profile.ts.html
    │           │   ├── retreats.ts.html
    │           │   └── web3.ts.html
    │           ├── components/
    │           │   ├── AIRecommendations.tsx.html
    │           │   ├── AllyProvider.tsx.html
    │           │   ├── BookingModal.tsx.html
    │           │   ├── CommunitySection.tsx.html
    │           │   ├── FeaturedRetreats.tsx.html
    │           │   ├── Features.tsx.html
    │           │   ├── Footer.tsx.html
    │           │   ├── Foundation.tsx.html
    │           │   ├── Hero.tsx.html
    │           │   ├── LiveRetreats.tsx.html
    │           │   ├── LivestreamSection.tsx.html
    │           │   ├── NFTGallery.tsx.html
    │           │   ├── Navbar.tsx.html
    │           │   ├── Partners.tsx.html
    │           │   ├── RetreatCalendar.tsx.html
    │           │   ├── RetreatCard.tsx.html
    │           │   ├── RetreatCategories.tsx.html
    │           │   ├── RetreatFilters.tsx.html
    │           │   ├── RetreatHighlights.tsx.html
    │           │   ├── RetreatList.tsx.html
    │           │   ├── RetreatListHeader.tsx.html
    │           │   ├── RetreatMap.tsx.html
    │           │   ├── RetreatSearch.tsx.html
    │           │   ├── RetreatSort.tsx.html
    │           │   ├── RetreatStats.tsx.html
    │           │   ├── ScrollToTop.tsx.html
    │           │   ├── Testimonials.tsx.html
    │           │   ├── TokenDistribution.tsx.html
    │           │   ├── TokenEcosystem.tsx.html
    │           │   ├── TokenMetrics.tsx.html
    │           │   ├── WalletConnect.tsx.html
    │           │   ├── index.html
    │           │   ├── index.ts.html
    │           │   ├── Admin/
    │           │   │   ├── AdminAnalytics.tsx.html
    │           │   │   ├── AdminBookingList.tsx.html
    │           │   │   ├── AdminDashboard.tsx.html
    │           │   │   ├── AdvancedAnalytics.tsx.html
    │           │   │   ├── AutomatedReports.tsx.html
    │           │   │   ├── BackupManagement.tsx.html
    │           │   │   ├── BookingFilters.tsx.html
    │           │   │   ├── BookingReports.tsx.html
    │           │   │   ├── GeoRestrictions.tsx.html
    │           │   │   ├── NotificationCenter.tsx.html
    │           │   │   ├── PasswordManager.tsx.html
    │           │   │   ├── RefundManagement.tsx.html
    │           │   │   ├── ReportVisualizations.tsx.html
    │           │   │   ├── SecurityEventsList.tsx.html
    │           │   │   ├── SecurityNotifications.tsx.html
    │           │   │   ├── SecurityRules.tsx.html
    │           │   │   ├── SessionManager.tsx.html
    │           │   │   ├── SmartAlerts.tsx.html
    │           │   │   ├── SystemHealth.tsx.html
    │           │   │   ├── TwoFactorAuth.tsx.html
    │           │   │   ├── UserManagement.tsx.html
    │           │   │   └── index.html
    │           │   ├── Calendar/
    │           │   │   ├── DraggableEvent.tsx.html
    │           │   │   ├── EventCalendar.tsx.html
    │           │   │   ├── EventDetailsDialog.tsx.html
    │           │   │   ├── EventFilters.tsx.html
    │           │   │   ├── EventForm.tsx.html
    │           │   │   ├── EventNotifications.tsx.html
    │           │   │   ├── EventReminderDialog.tsx.html
    │           │   │   └── index.html
    │           │   ├── Chat/
    │           │   │   ├── ChatWindow.tsx.html
    │           │   │   └── index.html
    │           │   ├── FileList/
    │           │   │   ├── Filelist.tsx.html
    │           │   │   └── index.html
    │           │   ├── FileUploader/
    │           │   │   ├── FileUploader.tsx.html
    │           │   │   ├── index.html
    │           │   │   └── index.tsx.html
    │           │   ├── Header/
    │           │   │   ├── Header.tsx.html
    │           │   │   ├── HeaderCarousel.tsx.html
    │           │   │   ├── NavigationLinks.ts.html
    │           │   │   ├── NavigationLinks.tsx.html
    │           │   │   ├── index.html
    │           │   │   └── index.ts.html
    │           │   ├── PrivateRoute/
    │           │   │   ├── PrivateRoute.tsx.html
    │           │   │   └── index.html
    │           │   ├── SEO/
    │           │   │   ├── Breadcrumbs.tsx.html
    │           │   │   ├── CommunitySEO.tsx.html
    │           │   │   ├── DefaultSEO.tsx.html
    │           │   │   ├── EventSEO.tsx.html
    │           │   │   ├── GoogleSearchConsole.tsx.html
    │           │   │   ├── MetaTags.tsx.html
    │           │   │   ├── PageMetadata.tsx.html
    │           │   │   ├── PagePerformance.tsx.html
    │           │   │   ├── PageSEO.tsx.html
    │           │   │   ├── ProfileSEO.tsx.html
    │           │   │   ├── SEO.tsx.html
    │           │   │   ├── SEODashboard.tsx.html
    │           │   │   ├── SearchSEO.tsx.html
    │           │   │   ├── index.html
    │           │   │   ├── Dashboard/
    │           │   │   │   ├── SEODashboard.tsx.html
    │           │   │   │   └── index.html
    │           │   │   └── Suggestions/
    │           │   │       ├── SEOFilters.tsx.html
    │           │   │       ├── SEOStats.tsx.html
    │           │   │       ├── SEOSuggestions.tsx.html
    │           │   │       └── index.html
    │           │   ├── VirtualEvents/
    │           │   │   ├── EventChat.tsx.html
    │           │   │   ├── InteractiveWorkshop.tsx.html
    │           │   │   ├── LivestreamComponent.tsx.html
    │           │   │   ├── ParticipantsList.tsx.html
    │           │   │   ├── VirtualEventRoom.tsx.html
    │           │   │   ├── index.html
    │           │   │   ├── Activities/
    │           │   │   │   ├── ActivityCard.tsx.html
    │           │   │   │   ├── ActivityFilters.tsx.html
    │           │   │   │   ├── ActivityManager.tsx.html
    │           │   │   │   ├── BreakoutRoomActivity.tsx.html
    │           │   │   │   ├── CreateActivityDialog.tsx.html
    │           │   │   │   ├── ExerciseActivity.tsx.html
    │           │   │   │   ├── MeditationActivity.tsx.html
    │           │   │   │   ├── PollActivity.tsx.html
    │           │   │   │   ├── QuizActivity.tsx.html
    │           │   │   │   ├── ThreeDActivity.tsx.html
    │           │   │   │   ├── WhiteboardActivity.tsx.html
    │           │   │   │   ├── index.html
    │           │   │   │   └── Mobile/
    │           │   │   │       ├── MobileActivityBase.tsx.html
    │           │   │   │       ├── MobileBreakoutRoom.tsx.html
    │           │   │   │       ├── MobileChat.tsx.html
    │           │   │   │       ├── MobileEventViewer.tsx.html
    │           │   │   │       ├── MobileNetworking.tsx.html
    │           │   │   │       ├── MobilePoll.tsx.html
    │           │   │   │       ├── MobileQA.tsx.html
    │           │   │   │       ├── MobileVirtualExpo.tsx.html
    │           │   │   │       ├── MobileWhiteboard.tsx.html
    │           │   │   │       ├── MobileWorkshop.tsx.html
    │           │   │   │       └── index.html
    │           │   │   ├── ScheduleBuilder/
    │           │   │   │   ├── AIRecommendations.tsx.html
    │           │   │   │   ├── ScheduleBuilder.tsx.html
    │           │   │   │   └── index.html
    │           │   │   └── VideoPlayer/
    │           │   │       ├── MobileVideoPlayer.tsx.html
    │           │   │       └── index.html
    │           │   ├── accessibility/
    │           │   │   ├── AccessibleComponents.tsx.html
    │           │   │   ├── KeyboardShortcuts.tsx.html
    │           │   │   └── index.html
    │           │   ├── ai/
    │           │   │   ├── MatchingResults.tsx.html
    │           │   │   ├── PartnerMatchingAI.tsx.html
    │           │   │   └── index.html
    │           │   ├── analytics/
    │           │   │   ├── PartnerAnalytics.tsx.html
    │           │   │   ├── RetreatAnalytics.tsx.html
    │           │   │   └── index.html
    │           │   ├── atoms/
    │           │   │   ├── Button/
    │           │   │   │   ├── Button.stories.tsx.html
    │           │   │   │   ├── Button.tsx.html
    │           │   │   │   └── index.html
    │           │   │   └── Skeleton/
    │           │   │       ├── Skeleton.tsx.html
    │           │   │       └── index.html
    │           │   ├── auth/
    │           │   │   ├── AuthButtons.tsx.html
    │           │   │   ├── AuthMiddleware.tsx.html
    │           │   │   ├── LoginForm.tsx.html
    │           │   │   ├── LoginPage.tsx.html
    │           │   │   ├── PrivateRoute.js.html
    │           │   │   ├── PrivateRoute.tsx.html
    │           │   │   ├── ProtectedRoute.tsx.html
    │           │   │   ├── RegisterForm.tsx.html
    │           │   │   ├── RequestPasswordResetForm.tsx.html
    │           │   │   ├── ResetPasswordForm.tsx.html
    │           │   │   ├── RoleBasedRoute.tsx.html
    │           │   │   ├── TwoFactorVerification.tsx.html
    │           │   │   ├── UnauthorizedPage.tsx.html
    │           │   │   ├── VerifyEmailForm.tsx.html
    │           │   │   └── index.html
    │           │   ├── booking/
    │           │   │   ├── GroupBooking.tsx.html
    │           │   │   ├── RetreatBooking.tsx.html
    │           │   │   ├── SecureBookingForm.tsx.html
    │           │   │   ├── SmartBooking.tsx.html
    │           │   │   └── index.html
    │           │   ├── car-rental/
    │           │   │   ├── CarCard.tsx.html
    │           │   │   ├── CarList.tsx.html
    │           │   │   ├── CarSearchForm.tsx.html
    │           │   │   └── index.html
    │           │   ├── chatbot/
    │           │   │   ├── Chatbot.tsx.html
    │           │   │   └── index.html
    │           │   ├── common/
    │           │   │   ├── ConfirmDialog.js.html
    │           │   │   ├── ConfirmDialog.tsx.html
    │           │   │   ├── CustomSnackbar.js.html
    │           │   │   ├── CustomSnackbar.tsx.html
    │           │   │   ├── ErrorBoundary.js.html
    │           │   │   ├── ErrorBoundary.tsx.html
    │           │   │   ├── ErrorFeedback.tsx.html
    │           │   │   ├── FormField.js.html
    │           │   │   ├── FormField.tsx.html
    │           │   │   ├── Image0ptimizer.tsx.html
    │           │   │   ├── LanguageSelector.tsx.html
    │           │   │   ├── LanguageSwitcher.tsx.html
    │           │   │   ├── Layout.tsx.html
    │           │   │   ├── LazyImage.tsx.html
    │           │   │   ├── LazyRoute.tsx.html
    │           │   │   ├── LoadingButton.tsx.html
    │           │   │   ├── LoadingFallback.tsx.html
    │           │   │   ├── LoadingScreen.tsx.html
    │           │   │   ├── LoadingSpinner.js.html
    │           │   │   ├── LoadingSpinner.tsx.html
    │           │   │   ├── LoadingView.tsx.html
    │           │   │   ├── Modal.tsx.html
    │           │   │   ├── Notification.tsx.html
    │           │   │   ├── NotificationItem.tsx.html
    │           │   │   ├── NotificationProvider.tsx.html
    │           │   │   ├── OptimizedImage.tsx.html
    │           │   │   ├── OptimizedPage.tsx.html
    │           │   │   ├── PageHeader.js.html
    │           │   │   ├── PageHeader.tsx.html
    │           │   │   ├── PageHelmet.tsx.html
    │           │   │   ├── PageTransition.tsx.html
    │           │   │   ├── Pagination.tsx.html
    │           │   │   ├── PrivateRoute.tsx.html
    │           │   │   ├── RetreatCard.tsx.html
    │           │   │   ├── RoleBasedRoute.tsx.html
    │           │   │   ├── SEO.tsx.html
    │           │   │   ├── ScrollToTop.tsx.html
    │           │   │   ├── SearchAndFilter.js.html
    │           │   │   ├── SearchAndFilter.tsx.html
    │           │   │   ├── SecureForm.tsx.html
    │           │   │   ├── SecureInput.tsx.html
    │           │   │   ├── Sidebar.tsx.html
    │           │   │   ├── Skeleton.tsx.html
    │           │   │   ├── SkeletonLoader.tsx.html
    │           │   │   ├── SkeletonPatterns.tsx.html
    │           │   │   ├── ThemeToggle.tsx.html
    │           │   │   ├── Toast.tsx.html
    │           │   │   ├── VirtualScroll.tsx.html
    │           │   │   ├── WalletButton.tsx.html
    │           │   │   ├── index.html
    │           │   │   ├── index.ts.html
    │           │   │   ├── withImageOptimization.tsx.html
    │           │   │   ├── withLazyLoading.tsx.html
    │           │   │   ├── Form/
    │           │   │   │   ├── Input.tsx.html
    │           │   │   │   └── index.html
    │           │   │   └── Table/
    │           │   │       ├── Table.tsx.html
    │           │   │       └── index.html
    │           │   ├── community/
    │           │   │   ├── CommunityFeed.tsx.html
    │           │   │   ├── CommunityFilters.tsx.html
    │           │   │   ├── CommunityMemberCard.tsx.html
    │           │   │   ├── CommunityMemberList.tsx.html
    │           │   │   ├── CommunitySearch.tsx.html
    │           │   │   ├── CreatePost.js.html
    │           │   │   ├── CreatePost.tsx.html
    │           │   │   ├── GroupVirtualEvents.tsx.html
    │           │   │   ├── Post.js.html
    │           │   │   ├── Post.tsx.html
    │           │   │   ├── PostFeed.js.html
    │           │   │   ├── PostFeed.tsx.html
    │           │   │   ├── PostList.tsx.html
    │           │   │   ├── ThematicGroups.tsx.html
    │           │   │   ├── VirtualizedMemberList.tsx.html
    │           │   │   └── index.html
    │           │   ├── contact/
    │           │   │   ├── SecureContactForm.tsx.html
    │           │   │   └── index.html
    │           │   ├── dashboard/
    │           │   │   ├── AnalyticsCard.tsx.html
    │           │   │   ├── AnalyticsDashboard.tsx.html
    │           │   │   ├── DashboardMessages.tsx.html
    │           │   │   ├── PartnerDashboard.tsx.html
    │           │   │   └── index.html
    │           │   ├── education/
    │           │   │   ├── CourseCard.tsx.html
    │           │   │   ├── CourseFilters.tsx.html
    │           │   │   ├── CourseGrid.tsx.html
    │           │   │   └── index.html
    │           │   ├── error/
    │           │   │   ├── ErrorBoundary.tsx.html
    │           │   │   ├── RouteErrorBoundary.tsx.html
    │           │   │   └── index.html
    │           │   ├── errors/
    │           │   │   ├── ErrorFallback.tsx.html
    │           │   │   └── index.html
    │           │   ├── events/
    │           │   │   ├── EventCalendar.tsx.html
    │           │   │   └── index.html
    │           │   ├── filters/
    │           │   │   ├── AdvancedFilters.tsx.html
    │           │   │   └── index.html
    │           │   ├── flights/
    │           │   │   ├── AirportSearch.tsx.html
    │           │   │   ├── FlightDatePicker.tsx.html
    │           │   │   ├── FlightResults.tsx.html
    │           │   │   ├── FlightSearch.tsx.html
    │           │   │   ├── PassengerSelector.tsx.html
    │           │   │   └── index.html
    │           │   ├── gamification/
    │           │   │   ├── AchievementSystem.tsx.html
    │           │   │   ├── Achievements.tsx.html
    │           │   │   └── index.html
    │           │   ├── gdpr/
    │           │   │   ├── ConsentManager.tsx.html
    │           │   │   ├── SubjectRequestForm.tsx.html
    │           │   │   └── index.html
    │           │   ├── gift-cards/
    │           │   │   ├── GiftCard.tsx.html
    │           │   │   ├── GiftCardAdmin.tsx.html
    │           │   │   ├── GiftCardList.tsx.html
    │           │   │   └── index.html
    │           │   ├── groups/
    │           │   │   ├── CreateGroupModal.tsx.html
    │           │   │   └── index.html
    │           │   ├── insurance/
    │           │   │   ├── InsuranceComparison.tsx.html
    │           │   │   ├── InsuranceQuoteForm.tsx.html
    │           │   │   ├── PolicyDetails.tsx.html
    │           │   │   └── index.html
    │           │   ├── invoice/
    │           │   │   ├── InvoiceActions.tsx.html
    │           │   │   ├── InvoiceGenerator.tsx.html
    │           │   │   ├── InvoiceTemplate.tsx.html
    │           │   │   └── index.html
    │           │   ├── layout/
    │           │   │   ├── ErrorBoundary.tsx.html
    │           │   │   ├── Footer.tsx.html
    │           │   │   ├── Header.tsx.html
    │           │   │   ├── Layout.tsx.html
    │           │   │   ├── MainLayout.tsx.html
    │           │   │   ├── MobileOptimizedLayout.tsx.html
    │           │   │   ├── Navbar.tsx.html
    │           │   │   ├── PrivateLayout.tsx.html
    │           │   │   ├── PublicLayout.tsx.html
    │           │   │   ├── PublicNavbar.tsx.html
    │           │   │   ├── ResponsiveLayout.tsx.html
    │           │   │   ├── Sidebar.tsx.html
    │           │   │   ├── TopBar.tsx.html
    │           │   │   ├── index.html
    │           │   │   ├── index.js.html
    │           │   │   ├── index.ts.html
    │           │   │   ├── index.tsx.html
    │           │   │   ├── types.ts.html
    │           │   │   ├── MobileDrawer/
    │           │   │   │   ├── MobileDrawer.tsx.html
    │           │   │   │   └── index.html
    │           │   │   ├── ProfileMenu/
    │           │   │   │   ├── ProfileMenu.tsx.html
    │           │   │   │   └── index.html
    │           │   │   └── SearchBar/
    │           │   │       ├── SearchBar.tsx.html
    │           │   │       └── index.html
    │           │   ├── layouts/
    │           │   │   ├── AdminLayout.tsx.html
    │           │   │   ├── MainLayout.tsx.html
    │           │   │   └── index.html
    │           │   ├── livestream/
    │           │   │   ├── LiveChat.tsx.html
    │           │   │   ├── LivestreamRetreat.tsx.html
    │           │   │   ├── ParticipantsList.tsx.html
    │           │   │   ├── StreamControls.tsx.html
    │           │   │   └── index.html
    │           │   ├── loyalty/
    │           │   │   ├── LoyaltyCard.tsx.html
    │           │   │   ├── LoyaltyTierCard.tsx.html
    │           │   │   ├── PointsConverter.tsx.html
    │           │   │   ├── PointsHistory.tsx.html
    │           │   │   └── index.html
    │           │   ├── marketplace/
    │           │   │   ├── CategoryFilter.tsx.html
    │           │   │   ├── ProductCard.tsx.html
    │           │   │   ├── ProductGrid.tsx.html
    │           │   │   ├── SearchBar.tsx.html
    │           │   │   ├── ShoppingCart.tsx.html
    │           │   │   └── index.html
    │           │   ├── meditation/
    │           │   │   ├── AIMeditationGuide.tsx.html
    │           │   │   └── index.html
    │           │   ├── messaging/
    │           │   │   ├── ChatHeader.tsx.html
    │           │   │   ├── ChatWindow.tsx.html
    │           │   │   ├── ConversationList.tsx.html
    │           │   │   ├── MessageInput.tsx.html
    │           │   │   ├── MessageList.tsx.html
    │           │   │   ├── MessagingPanel.tsx.html
    │           │   │   └── index.html
    │           │   ├── monitoring/
    │           │   │   ├── AlertRuleForm.tsx.html
    │           │   │   ├── AlertsManager.tsx.html
    │           │   │   ├── Dashboard.tsx.html
    │           │   │   ├── MetricCard.tsx.html
    │           │   │   ├── PerformanceChart.tsx.html
    │           │   │   ├── PerformanceMetrics.tsx.html
    │           │   │   ├── ReportsManager.tsx.html
    │           │   │   ├── ScheduledReportForm.tsx.html
    │           │   │   ├── TestDetails.tsx.html
    │           │   │   ├── index.html
    │           │   │   ├── charts/
    │           │   │   │   ├── ReportChart.tsx.html
    │           │   │   │   └── index.html
    │           │   │   ├── exports/
    │           │   │   │   ├── ReportExport.tsx.html
    │           │   │   │   └── index.html
    │           │   │   └── filters/
    │           │   │       ├── ReportFilters.tsx.html
    │           │   │       └── index.html
    │           │   ├── navigation/
    │           │   │   ├── ContextualNavigation.tsx.html
    │           │   │   ├── CustomShortcutsManager.tsx.html
    │           │   │   ├── Footer.tsx.html
    │           │   │   ├── GestureVisualizer.tsx.html
    │           │   │   ├── KeyboardShortcuts.tsx.html
    │           │   │   ├── NavItem.tsx.html
    │           │   │   ├── Navbar.tsx.html
    │           │   │   ├── NavigationHistory.tsx.html
    │           │   │   ├── NavigationPreferences.tsx.html
    │           │   │   ├── Sidebar.tsx.html
    │           │   │   ├── VoiceNavigation.tsx.html
    │           │   │   └── index.html
    │           │   ├── nft/
    │           │   │   ├── NFTCard.tsx.html
    │           │   │   ├── NFTCollection.tsx.html
    │           │   │   ├── NFTGallery.tsx.html
    │           │   │   ├── NFTGrid.tsx.html
    │           │   │   ├── NFTMinter.tsx.html
    │           │   │   ├── OptimizedNFTGrid.tsx.html
    │           │   │   └── index.html
    │           │   ├── notifications/
    │           │   │   ├── ABTestForm.tsx.html
    │           │   │   ├── ABTestResults.tsx.html
    │           │   │   ├── ABTestingDashboard.tsx.html
    │           │   │   ├── ConnectionStatus.tsx.html
    │           │   │   ├── NotificationAnalytics.tsx.html
    │           │   │   ├── NotificationBadge.tsx.html
    │           │   │   ├── NotificationCenter.tsx.html
    │           │   │   ├── NotificationDashboard.tsx.html
    │           │   │   ├── NotificationDropdown.tsx.html
    │           │   │   ├── NotificationHistory.tsx.html
    │           │   │   ├── NotificationManager.tsx.html
    │           │   │   ├── NotificationPreferences.tsx.html
    │           │   │   ├── NotificationScheduler.tsx.html
    │           │   │   ├── NotificationSystem.tsx.html
    │           │   │   ├── NotificationTemplates.tsx.html
    │           │   │   ├── RealTimeNotifications.tsx.html
    │           │   │   └── index.html
    │           │   ├── optimizations/
    │           │   │   ├── MediaResource.tsx.html
    │           │   │   ├── OptimizedImage.tsx.html
    │           │   │   └── index.html
    │           │   ├── partner/
    │           │   │   ├── PartnerHeader.tsx.html
    │           │   │   ├── PartnerStats.tsx.html
    │           │   │   ├── PartnerTierBadge.tsx.html
    │           │   │   ├── PartnerTypeIcon.tsx.html
    │           │   │   ├── StatCard.tsx.html
    │           │   │   ├── UpgradeBanner.tsx.html
    │           │   │   └── index.html
    │           │   ├── payment/
    │           │   │   ├── SecurePaymentForm.tsx.html
    │           │   │   └── index.html
    │           │   ├── performance/
    │           │   │   ├── PerformanceMonitor.tsx.html
    │           │   │   └── index.html
    │           │   ├── profile/
    │           │   │   ├── AchievementsDisplay.tsx.html
    │           │   │   ├── ActiveSessions.tsx.html
    │           │   │   ├── ActivityFeed.tsx.html
    │           │   │   ├── AppPermissions.tsx.html
    │           │   │   ├── ConnectedApps.tsx.html
    │           │   │   ├── ConnectionsGrid.tsx.html
    │           │   │   ├── EditProfileDialog.tsx.html
    │           │   │   ├── LoginHistory.tsx.html
    │           │   │   ├── NotificationCenter.tsx.html
    │           │   │   ├── PrivacyPreferences.tsx.html
    │           │   │   ├── ProfileAnalytics.tsx.html
    │           │   │   ├── ProfileCard.tsx.html
    │           │   │   ├── ProfileDashboard.tsx.html
    │           │   │   ├── ProfileSettings.tsx.html
    │           │   │   ├── RetreatHistory.tsx.html
    │           │   │   ├── SecureProfileForm.tsx.html
    │           │   │   ├── SecurityActivityHistory.tsx.html
    │           │   │   ├── SecurityAlerts.tsx.html
    │           │   │   ├── SecurityAudit.tsx.html
    │           │   │   ├── SecurityKeys.tsx.html
    │           │   │   ├── SecurityNavigation.tsx.html
    │           │   │   ├── SecurityNotifications.tsx.html
    │           │   │   ├── SecuritySettings.tsx.html
    │           │   │   ├── TrustedDevices.tsx.html
    │           │   │   ├── UserProfile.tsx.html
    │           │   │   ├── WellnessPreferences.tsx.html
    │           │   │   └── index.html
    │           │   ├── referral/
    │           │   │   ├── ReferralProgram.tsx.html
    │           │   │   └── index.html
    │           │   ├── retreats/
    │           │   │   ├── BookingModal.tsx.html
    │           │   │   ├── PaymentDetails.tsx.html
    │           │   │   ├── RetreatCard.js.html
    │           │   │   ├── RetreatCard.tsx.html
    │           │   │   ├── RetreatDetail.tsx.html
    │           │   │   ├── RetreatDetails.tsx.html
    │           │   │   ├── RetreatFilters.tsx.html
    │           │   │   ├── RetreatGrid.js.html
    │           │   │   ├── RetreatGrid.tsx.html
    │           │   │   ├── RetreatList.tsx.html
    │           │   │   ├── ReviewSection.tsx.html
    │           │   │   └── index.html
    │           │   ├── reviews/
    │           │   │   ├── ReviewSystem.tsx.html
    │           │   │   ├── SecureReviewForm.tsx.html
    │           │   │   └── index.html
    │           │   ├── rewards/
    │           │   │   ├── RewardsCatalog.tsx.html
    │           │   │   └── index.html
    │           │   ├── routing/
    │           │   │   ├── AdminRoute.tsx.html
    │           │   │   ├── PrivateRoute.tsx.html
    │           │   │   ├── PublicRoute.tsx.html
    │           │   │   ├── RoleBasedRedirect.tsx.html
    │           │   │   └── index.html
    │           │   ├── search/
    │           │   │   ├── GlobalSearch.tsx.html
    │           │   │   ├── SearchBar.tsx.html
    │           │   │   ├── SearchResults.tsx.html
    │           │   │   └── index.html
    │           │   ├── security/
    │           │   │   ├── AuditLog.tsx.html
    │           │   │   ├── DeviceManager.tsx.html
    │           │   │   ├── InactivityMonitor.tsx.html
    │           │   │   ├── PasswordManager.tsx.html
    │           │   │   ├── ProtectedRoute.tsx.html
    │           │   │   ├── RecentActivityList.tsx.html
    │           │   │   ├── SecurityAlertsList.tsx.html
    │           │   │   ├── SecurityChart.tsx.html
    │           │   │   ├── SecurityDashboard.tsx.html
    │           │   │   ├── SecurityProvider.tsx.html
    │           │   │   ├── SessionManager.tsx.html
    │           │   │   ├── TwoFactorSetup.tsx.html
    │           │   │   ├── TwoFactorVerify.tsx.html
    │           │   │   ├── index.html
    │           │   │   └── withXSSProtection.tsx.html
    │           │   ├── settings/
    │           │   │   ├── SecureSettingsForm.tsx.html
    │           │   │   ├── TwoFactorSettings.tsx.html
    │           │   │   └── index.html
    │           │   ├── social/
    │           │   │   ├── CreatePost.tsx.html
    │           │   │   ├── LiveStream.tsx.html
    │           │   │   ├── PostCard.tsx.html
    │           │   │   ├── SocialFeed.tsx.html
    │           │   │   ├── SocialSidebar.tsx.html
    │           │   │   ├── index.html
    │           │   │   ├── Feed/
    │           │   │   │   ├── CreatePost.tsx.html
    │           │   │   │   ├── FeedFilters.tsx.html
    │           │   │   │   ├── FeedItem.tsx.html
    │           │   │   │   ├── FeedList.tsx.html
    │           │   │   │   ├── RecommendedPosts.tsx.html
    │           │   │   │   ├── SocialFeedContainer.tsx.html
    │           │   │   │   ├── TrendingPosts.tsx.html
    │           │   │   │   └── index.html
    │           │   │   └── Poll/
    │           │   │       ├── CreatePollDialog.tsx.html
    │           │   │       ├── PollComponent.tsx.html
    │           │   │       └── index.html
    │           │   ├── transitions/
    │           │   │   ├── PageTransition.tsx.html
    │           │   │   └── index.html
    │           │   ├── ui/
    │           │   │   ├── Badge.tsx.html
    │           │   │   ├── LoadingSpinner.tsx.html
    │           │   │   ├── OptimizedImage.tsx.html
    │           │   │   ├── Skeletons.tsx.html
    │           │   │   ├── Transitions.tsx.html
    │           │   │   ├── button.tsx.html
    │           │   │   ├── card.tsx.html
    │           │   │   ├── index.html
    │           │   │   └── input.tsx.html
    │           │   ├── video/
    │           │   │   ├── GroupVideoCall.tsx.html
    │           │   │   ├── VideoCall.tsx.html
    │           │   │   └── index.html
    │           │   ├── web3/
    │           │   │   ├── Web3Test.tsx.html
    │           │   │   └── index.html
    │           │   └── wellness/
    │           │       ├── WellnessTracker.tsx.html
    │           │       └── index.html
    │           ├── config/
    │           │   ├── adminRoutes.ts.html
    │           │   ├── api.ts.html
    │           │   ├── appConfig.ts.html
    │           │   ├── axios.ts.html
    │           │   ├── chains.ts.html
    │           │   ├── env.ts.html
    │           │   ├── index.html
    │           │   ├── index.js.html
    │           │   ├── index.ts.html
    │           │   ├── monitoring.ts.html
    │           │   ├── routeConfig.ts.html
    │           │   ├── routeMetadata.ts.html
    │           │   └── routes.ts.html
    │           ├── containers/
    │           │   ├── education/
    │           │   │   ├── EducationContainer.tsx.html
    │           │   │   └── index.html
    │           │   ├── financial/
    │           │   │   ├── FinancialContainer.tsx.html
    │           │   │   └── index.html
    │           │   ├── insurance/
    │           │   │   ├── InsuranceContainer.tsx.html
    │           │   │   └── index.html
    │           │   ├── loyalty/
    │           │   │   ├── LoyaltyContainer.tsx.html
    │           │   │   └── index.html
    │           │   ├── marketplace/
    │           │   │   ├── MarketplaceContainer.tsx.html
    │           │   │   └── index.html
    │           │   ├── messaging/
    │           │   │   ├── MessagingContainer.tsx.html
    │           │   │   └── index.html
    │           │   ├── partners/
    │           │   │   ├── PartnerRegistrationContainer.tsx.html
    │           │   │   └── index.html
    │           │   ├── security/
    │           │   │   ├── SecurityContainer.tsx.html
    │           │   │   └── index.html
    │           │   └── social/
    │           │       ├── SocialContainer.tsx.html
    │           │       └── index.html
    │           ├── contexts/
    │           │   ├── AuthContext.js.html
    │           │   ├── AuthContext.tsx.html
    │           │   ├── BookingContext.tsx.html
    │           │   ├── NotificationContext.tsx.html
    │           │   ├── SearchContext.tsx.html
    │           │   ├── SnackbarContext.tsx.html
    │           │   ├── ThemeContext.tsx.html
    │           │   ├── UIContext.tsx.html
    │           │   ├── index.html
    │           │   └── index.ts.html
    │           ├── features/
    │           │   ├── auth/
    │           │   │   ├── index.html
    │           │   │   ├── index.ts.html
    │           │   │   ├── components/
    │           │   │   │   ├── Login.js.html
    │           │   │   │   ├── Login.tsx.html
    │           │   │   │   ├── Register.js.html
    │           │   │   │   ├── Register.tsx.html
    │           │   │   │   ├── ResetPassword.tsx.html
    │           │   │   │   ├── VerifyEmail.tsx.html
    │           │   │   │   ├── index.html
    │           │   │   │   └── index.ts.html
    │           │   │   ├── contexts/
    │           │   │   │   ├── AuthContext.tsx.html
    │           │   │   │   └── index.html
    │           │   │   ├── services/
    │           │   │   │   ├── authService.ts.html
    │           │   │   │   └── index.html
    │           │   │   └── types/
    │           │   │       ├── index.html
    │           │   │       └── index.ts.html
    │           │   ├── marketplace/
    │           │   │   ├── index.html
    │           │   │   ├── index.ts.html
    │           │   │   ├── types.ts.html
    │           │   │   ├── components/
    │           │   │   │   ├── CategoryFilter.tsx.html
    │           │   │   │   ├── Marketplace.js.html
    │           │   │   │   ├── Marketplace.tsx.html
    │           │   │   │   ├── ProductCard.tsx.html
    │           │   │   │   ├── ProductGrid.tsx.html
    │           │   │   │   ├── SearchBar.tsx.html
    │           │   │   │   ├── ShoppingCart.tsx.html
    │           │   │   │   ├── index.html
    │           │   │   │   └── index.ts.html
    │           │   │   ├── store/
    │           │   │   │   ├── index.html
    │           │   │   │   └── marketplaceStore.ts.html
    │           │   │   └── types/
    │           │   │       ├── index.html
    │           │   │       └── marketplace.ts.html
    │           │   ├── messaging/
    │           │   │   ├── index.html
    │           │   │   ├── index.ts.html
    │           │   │   ├── components/
    │           │   │   │   ├── MessagesPage.tsx.html
    │           │   │   │   ├── VoiceMessageRecorder.tsx.html
    │           │   │   │   └── index.html
    │           │   │   └── contexts/
    │           │   │       ├── MessageContext.tsx.html
    │           │   │       └── index.html
    │           │   ├── security/
    │           │   │   ├── SecurityProvider.tsx.html
    │           │   │   └── index.html
    │           │   └── web3/
    │           │       ├── index.html
    │           │       ├── index.ts.html
    │           │       └── contexts/
    │           │           ├── NotificationContext.tsx.html
    │           │           ├── Web3Context.tsx.html
    │           │           └── index.html
    │           ├── hooks/
    │           │   ├── index.html
    │           │   ├── useAIRecommendations.ts.html
    │           │   ├── useAccessibilityAnnouncer.ts.html
    │           │   ├── useAchievements.ts.html
    │           │   ├── useActivity.ts.html
    │           │   ├── useAdvancedFilters.ts.html
    │           │   ├── useAdvancedGestures.ts.html
    │           │   ├── useAnalytics.ts.html
    │           │   ├── useAnimation.ts.html
    │           │   ├── useAnimationFrame.ts.html
    │           │   ├── useAnimations.ts.html
    │           │   ├── useApi.ts.html
    │           │   ├── useApiState.ts.html
    │           │   ├── useAppState.ts.html
    │           │   ├── useAuth.ts.html
    │           │   ├── useAuthState.ts.html
    │           │   ├── useBreakpoints.ts.html
    │           │   ├── useCache.ts.html
    │           │   ├── useCacheManager.ts.html
    │           │   ├── useCachedData.ts.html
    │           │   ├── useCalendar.ts.html
    │           │   ├── useCalendarViews.ts.html
    │           │   ├── useCommunity.ts.html
    │           │   ├── useCommunityEngagement.ts.html
    │           │   ├── useCommunityInteractions.ts.html
    │           │   ├── useCommunityMembers.ts.html
    │           │   ├── useContextualNavigation.ts.html
    │           │   ├── useCustomShortcuts.ts.html
    │           │   ├── useDialog.ts.html
    │           │   ├── useErrorHandler.ts.html
    │           │   ├── useEventFilters.ts.html
    │           │   ├── useEventReminders.ts.html
    │           │   ├── useEventsCache.ts.html
    │           │   ├── useEventsPagination.ts.html
    │           │   ├── useFiles.ts.html
    │           │   ├── useFinancialService.ts.html
    │           │   ├── useForm.ts.html
    │           │   ├── useFormOptimizer.ts.html
    │           │   ├── useFormValidation.ts.html
    │           │   ├── useGestureNavigation.ts.html
    │           │   ├── useI18n.ts.html
    │           │   ├── useImageLazyLoad.ts.html
    │           │   ├── useImageOptimizer.ts.html
    │           │   ├── useInfiniteQuery.ts.html
    │           │   ├── useIntersectionObserver.ts.html
    │           │   ├── useKeyboardNavigation.ts.html
    │           │   ├── useLayoutState.ts.html
    │           │   ├── useLocalStorage.ts.html
    │           │   ├── useLoyaltySystem.ts.html
    │           │   ├── useMedia.ts.html
    │           │   ├── useMediaOptimization.ts.html
    │           │   ├── useMobileOptimization.ts.html
    │           │   ├── useMutation.ts.html
    │           │   ├── useNavigationPreferences.ts.html
    │           │   ├── useNotificationManager.ts.html
    │           │   ├── useNotificationSound.ts.html
    │           │   ├── useNotificationSystem.ts.html
    │           │   ├── useNotifications.ts.html
    │           │   ├── useOfflineManager.ts.html
    │           │   ├── useOptimization.ts.html
    │           │   ├── useOptimizedForm.ts.html
    │           │   ├── useOptimizedQuery.ts.html
    │           │   ├── usePageTransition.ts.html
    │           │   ├── usePayment.ts.html
    │           │   ├── usePerformance.ts.html
    │           │   ├── usePerformanceMetrics.ts.html
    │           │   ├── usePerformanceMonitor.ts.html
    │           │   ├── usePerformanceOptimizer.ts.html
    │           │   ├── usePermissions.ts.html
    │           │   ├── usePoll.ts.html
    │           │   ├── usePostRecommendations.ts.html
    │           │   ├── usePrefetch.ts.html
    │           │   ├── useProMatcher.ts.html
    │           │   ├── useProfile.ts.html
    │           │   ├── useProgressiveNavigation.ts.html
    │           │   ├── usePushNotifications.ts.html
    │           │   ├── useQueryCache.ts.html
    │           │   ├── useQueryConfig.ts.html
    │           │   ├── useQueryState.ts.html
    │           │   ├── useRealtimeNotifications.ts.html
    │           │   ├── useRenderOptimizer.ts.html
    │           │   ├── useResponsive.ts.html
    │           │   ├── useRetreatContract.ts.html
    │           │   ├── useRetreatManagement.ts.html
    │           │   ├── useRetreatService.ts.html
    │           │   ├── useRetreats.ts.html
    │           │   ├── useRoutePrefetch.ts.html
    │           │   ├── useSchedule.ts.html
    │           │   ├── useScrollPosition.ts.html
    │           │   ├── useScrollRestoration.ts.html
    │           │   ├── useSearchResults.ts.html
    │           │   ├── useSecurityStats.ts.html
    │           │   ├── useServiceBooking.ts.html
    │           │   ├── useServiceWorker.ts.html
    │           │   ├── useServices.ts.html
    │           │   ├── useSocialFeed.ts.html
    │           │   ├── useSound.ts.html
    │           │   ├── useTwoFactor.ts.html
    │           │   ├── useUserPreferences.ts.html
    │           │   ├── useVoiceNavigation.ts.html
    │           │   ├── useWeb3.ts.html
    │           │   ├── useWeb3Profile.ts.html
    │           │   ├── useWebSocket.ts.html
    │           │   └── useWebSocketNotifications.ts.html
    │           ├── i18n/
    │           │   ├── config.ts.html
    │           │   ├── index.html
    │           │   └── index.ts.html
    │           ├── lib/
    │           │   ├── index.html
    │           │   ├── react-query.ts.html
    │           │   └── utils.ts.html
    │           ├── middleware/
    │           │   ├── AuthMiddleware.tsx.html
    │           │   ├── apiInterceptor.ts.html
    │           │   ├── authMiddleware.ts.html
    │           │   ├── errorMiddleware.ts.html
    │           │   ├── index.html
    │           │   ├── index.ts.html
    │           │   ├── loggingMiddleware.ts.html
    │           │   └── securityMiddleware.ts.html
    │           ├── pages/
    │           │   ├── BecomePartnerPage.tsx.html
    │           │   ├── BlogPage.tsx.html
    │           │   ├── CarDetailsPage.tsx.html
    │           │   ├── CarRentalPage.tsx.html
    │           │   ├── CareersPage.tsx.html
    │           │   ├── CaterersPage.tsx.html
    │           │   ├── CommunityMemberPage.tsx.html
    │           │   ├── CommunityPage.js.html
    │           │   ├── CommunityPage.tsx.html
    │           │   ├── ContactPage.tsx.html
    │           │   ├── Dashboard.tsx.html
    │           │   ├── EducationPage.tsx.html
    │           │   ├── EventsPage.tsx.html
    │           │   ├── ExploreRetreatsPage.tsx.html
    │           │   ├── FAQPage.tsx.html
    │           │   ├── FoundationPage.tsx.html
    │           │   ├── GDPRPage.tsx.html
    │           │   ├── GalleryPage.tsx.html
    │           │   ├── HomePage.tsx.html
    │           │   ├── HostsPage.tsx.html
    │           │   ├── InsurancePage.tsx.html
    │           │   ├── InvoicePage.tsx.html
    │           │   ├── LandingPage.tsx.html
    │           │   ├── LivestreamRetreatPage.tsx.html
    │           │   ├── LoyaltyPage.tsx.html
    │           │   ├── MainPage.tsx.html
    │           │   ├── MarketplacePage.tsx.html
    │           │   ├── MessagesPage.tsx.html
    │           │   ├── NFTGalleryPage.tsx.html
    │           │   ├── NewsPage.tsx.html
    │           │   ├── NotFoundPage.tsx.html
    │           │   ├── NotificationHistory.tsx.html
    │           │   ├── OrganizersPage.tsx.html
    │           │   ├── PartnersPage.tsx.html
    │           │   ├── PrivacyPolicyPage.tsx.html
    │           │   ├── Profile.tsx.html
    │           │   ├── ProfilePage.js.html
    │           │   ├── ProfilePage.tsx.html
    │           │   ├── ProgramsPage.tsx.html
    │           │   ├── RegisterPage.tsx.html
    │           │   ├── ReportsPage.tsx.html
    │           │   ├── ResourcesPage.tsx.html
    │           │   ├── RetreatPage.tsx.html
    │           │   ├── RetreatsPage.tsx.html
    │           │   ├── RewardsPage.tsx.html
    │           │   ├── SecurityPage.tsx.html
    │           │   ├── Settings.tsx.html
    │           │   ├── SettingsPage.tsx.html
    │           │   ├── SocialFeedPage.tsx.html
    │           │   ├── SupportPage.tsx.html
    │           │   ├── TermsPage.tsx.html
    │           │   ├── TestimonialsPage.tsx.html
    │           │   ├── TokenPage.tsx.html
    │           │   ├── TravelAgenciesPage.tsx.html
    │           │   ├── UnderConstructionPage.tsx.html
    │           │   ├── UpgradePage.tsx.html
    │           │   ├── WellnessPage.tsx.html
    │           │   ├── index.html
    │           │   ├── index.ts.html
    │           │   ├── Booking/
    │           │   │   ├── BookingPage.tsx.html
    │           │   │   ├── index.html
    │           │   │   └── components/
    │           │   │       ├── BookingCalendar.tsx.html
    │           │   │       ├── BookingForm.tsx.html
    │           │   │       ├── BookingSummary.tsx.html
    │           │   │       └── index.html
    │           │   ├── Dashboard/
    │           │   │   ├── Dashboard.tsx.html
    │           │   │   ├── index.html
    │           │   │   └── components/
    │           │   │       ├── ActivityFeed.tsx.html
    │           │   │       ├── BookingCard.tsx.html
    │           │   │       ├── RecentBookings.tsx.html
    │           │   │       ├── Stats.tsx.html
    │           │   │       ├── UpcomingEvents.tsx.html
    │           │   │       └── index.html
    │           │   ├── Files/
    │           │   │   ├── index.html
    │           │   │   └── index.tsx.html
    │           │   ├── Login/
    │           │   │   ├── index.html
    │           │   │   └── index.tsx.html
    │           │   ├── MarketplacePage/
    │           │   │   ├── index.html
    │           │   │   └── index.tsx.html
    │           │   ├── NftGallery/
    │           │   │   ├── index.html
    │           │   │   └── index.tsx.html
    │           │   ├── Profile/
    │           │   │   ├── ProfilePage.tsx.html
    │           │   │   ├── index.html
    │           │   │   └── components/
    │           │   │       ├── ChangePasswordDialog.tsx.html
    │           │   │       └── index.html
    │           │   ├── Register/
    │           │   │   ├── index.html
    │           │   │   └── index.tsx.html
    │           │   ├── Settings/
    │           │   │   ├── SettingsPage.tsx.html
    │           │   │   ├── index.html
    │           │   │   └── components/
    │           │   │       ├── AppearanceSettings.tsx.html
    │           │   │       ├── NotificationsSettings.tsx.html
    │           │   │       ├── SecuritySettings.tsx.html
    │           │   │       └── index.html
    │           │   ├── SettingsPage/
    │           │   │   ├── SettingsPage.tsx.html
    │           │   │   ├── index.html
    │           │   │   ├── components/
    │           │   │   │   ├── NotificationsSection.tsx.html
    │           │   │   │   ├── PreferencesSection.tsx.html
    │           │   │   │   ├── PrivacySection.tsx.html
    │           │   │   │   ├── SecuritySection.tsx.html
    │           │   │   │   └── index.html
    │           │   │   └── hooks/
    │           │   │       ├── index.html
    │           │   │       └── useSettings.ts.html
    │           │   ├── SocialFeed/
    │           │   │   ├── index.html
    │           │   │   └── index.tsx.html
    │           │   ├── admin/
    │           │   │   ├── AdminDashboard.tsx.html
    │           │   │   ├── Analytics.tsx.html
    │           │   │   ├── Content.tsx.html
    │           │   │   ├── Dashboard.tsx.html
    │           │   │   ├── MonitoringDashboard.tsx.html
    │           │   │   ├── Roles.tsx.html
    │           │   │   ├── Settings.tsx.html
    │           │   │   ├── Users.tsx.html
    │           │   │   └── index.html
    │           │   ├── auth/
    │           │   │   ├── ForgotPasswordPage.tsx.html
    │           │   │   ├── LoginPage.tsx.html
    │           │   │   ├── RegisterPage.tsx.html
    │           │   │   ├── ResetPasswordPage.tsx.html
    │           │   │   └── index.html
    │           │   ├── dashboards/
    │           │   │   ├── AdminDashboard.tsx.html
    │           │   │   ├── GroupDashboard.tsx.html
    │           │   │   ├── MainDashboard.tsx.html
    │           │   │   ├── PartnerDashboard.tsx.html
    │           │   │   ├── UserDashboard.tsx.html
    │           │   │   └── index.html
    │           │   ├── error/
    │           │   │   ├── NotFoundPage.tsx.html
    │           │   │   └── index.html
    │           │   ├── partner/
    │           │   │   ├── AdminDashboardPage.tsx.html
    │           │   │   ├── CreateRetreatAIPage.tsx.html
    │           │   │   ├── GetStartedPage.tsx.html
    │           │   │   ├── OnboardingPage.tsx.html
    │           │   │   └── index.html
    │           │   └── retreats/
    │           │       ├── index.html
    │           │       ├── types.ts.html
    │           │       └── components/
    │           │           ├── RetreatCard.tsx.html
    │           │           ├── RetreatFilters.tsx.html
    │           │           └── index.html
    │           ├── providers/
    │           │   ├── AppProviders.tsx.html
    │           │   ├── DatePickerProvider.tsx.html
    │           │   ├── QueryProvider.tsx.html
    │           │   ├── SecurityProvider.tsx.html
    │           │   └── index.html
    │           ├── router/
    │           │   ├── AdminRoutes.tsx.html
    │           │   ├── LazyRoutes.tsx.html
    │           │   └── index.html
    │           ├── routes/
    │           │   ├── AppRoutes.tsx.html
    │           │   ├── FinancialRoutes.tsx.html
    │           │   ├── PremiumRoute.tsx.html
    │           │   ├── PrivateRoute.js.html
    │           │   ├── PrivateRoute.tsx.html
    │           │   ├── PrivateRoutes.tsx.html
    │           │   ├── PublicRoutes.tsx.html
    │           │   ├── RouteMiddleware.tsx.html
    │           │   ├── VideoRoutes.tsx.html
    │           │   ├── WebsiteCreatorRoutes.tsx.html
    │           │   ├── index.html
    │           │   ├── index.js.html
    │           │   ├── index.tsx.html
    │           │   └── lazyRoutes.tsx.html
    │           ├── security/
    │           │   ├── ClientMalwareService.ts.html
    │           │   ├── DDoSProtectionService.ts.html
    │           │   ├── EnhancedSecurityService.ts.html
    │           │   ├── MultiFactorAuthService.ts.html
    │           │   ├── SecureBackupService.ts.html
    │           │   ├── ThreatDetectionService.ts.html
    │           │   └── index.html
    │           ├── services/
    │           │   ├── AIRecommendationService.ts.html
    │           │   ├── ActivityService.ts.html
    │           │   ├── AdminNotificationService.ts.html
    │           │   ├── AlertService.ts.html
    │           │   ├── AnimationService.ts.html
    │           │   ├── ChatService.ts.html
    │           │   ├── CryptoService.ts.html
    │           │   ├── ErrorService.ts.html
    │           │   ├── MediaService.ts.html
    │           │   ├── MessagingService.ts.html
    │           │   ├── MicroserviceConnector.ts.html
    │           │   ├── MonitoringService.ts.html
    │           │   ├── ParticipantService.ts.html
    │           │   ├── PaymentService.ts.html
    │           │   ├── PerformanceService.ts.html
    │           │   ├── PollService.ts.html
    │           │   ├── ProMatcherService.ts.html
    │           │   ├── RateLimitService.ts.html
    │           │   ├── RealtimeNotificationService.ts.html
    │           │   ├── RetreatManagementService.ts.html
    │           │   ├── RetreatStreamService.ts.html
    │           │   ├── ScheduleService.ts.html
    │           │   ├── ScheduledReportService.ts.html
    │           │   ├── SecurityMonitoringService.ts.html
    │           │   ├── SecurityService.ts.html
    │           │   ├── ServiceRegistry.ts.html
    │           │   ├── SocialService.ts.html
    │           │   ├── StreamingService.ts.html
    │           │   ├── TonService.ts.html
    │           │   ├── UserProfileService.ts.html
    │           │   ├── Web3Service.ts.html
    │           │   ├── WebRTCService.ts.html
    │           │   ├── WorkshopService.ts.html
    │           │   ├── accessibilityMonitor.ts.html
    │           │   ├── advancedImageOptimizer.ts.html
    │           │   ├── analyticsService.ts.html
    │           │   ├── api.ts.html
    │           │   ├── apiService.ts.html
    │           │   ├── auth-header.js.html
    │           │   ├── auth-header.ts.html
    │           │   ├── auth.service.js.html
    │           │   ├── authService.ts.html
    │           │   ├── axiosConfig.ts.html
    │           │   ├── bookingService.ts.html
    │           │   ├── cacheManager.ts.html
    │           │   ├── cacheService.ts.html
    │           │   ├── community.service.js.html
    │           │   ├── community.service.ts.html
    │           │   ├── errorBoundaryService.ts.html
    │           │   ├── errorManager.ts.html
    │           │   ├── fileService.ts.html
    │           │   ├── formManager.ts.html
    │           │   ├── gdprService.ts.html
    │           │   ├── imageOptimizer.ts.html
    │           │   ├── imageService.ts.html
    │           │   ├── index.html
    │           │   ├── meta-tags.service.ts.html
    │           │   ├── navigationService.ts.html
    │           │   ├── nftService.ts.html
    │           │   ├── notification.ts.html
    │           │   ├── notificationService.ts.html
    │           │   ├── performanceMonitor.ts.html
    │           │   ├── queryClient.ts.html
    │           │   ├── resourceMonitor.ts.html
    │           │   ├── retreat.service.js.html
    │           │   ├── retreat.service.ts.html
    │           │   ├── routingService.ts.html
    │           │   ├── search-console.service.ts.html
    │           │   ├── seo-analytics.service.ts.html
    │           │   ├── stateService.ts.html
    │           │   ├── storageService.ts.html
    │           │   ├── syncService.ts.html
    │           │   ├── themeService.ts.html
    │           │   ├── user.service.js.html
    │           │   ├── user.service.ts.html
    │           │   ├── validationService.ts.html
    │           │   ├── webSocketService.ts.html
    │           │   ├── wellnessService.ts.html
    │           │   ├── Activity/
    │           │   │   ├── Service.ts.html
    │           │   │   └── index.html
    │           │   ├── CalendarEvent/
    │           │   │   ├── index.html
    │           │   │   └── service.ts.html
    │           │   ├── CalendarView/
    │           │   │   ├── Service.ts.html
    │           │   │   └── index.html
    │           │   ├── EventReminder/
    │           │   │   ├── index.html
    │           │   │   └── service.ts.html
    │           │   ├── Notification/
    │           │   │   ├── index.html
    │           │   │   └── service.ts.html
    │           │   ├── RecurringEvent/
    │           │   │   ├── Service.ts.html
    │           │   │   └── index.html
    │           │   ├── api/
    │           │   │   ├── config.ts.html
    │           │   │   ├── httpClient.ts.html
    │           │   │   └── index.html
    │           │   ├── education/
    │           │   │   ├── educationService.ts.html
    │           │   │   └── index.html
    │           │   ├── insurance/
    │           │   │   ├── index.html
    │           │   │   └── insuranceService.ts.html
    │           │   ├── marketplace/
    │           │   │   ├── index.html
    │           │   │   └── marketplaceService.ts.html
    │           │   └── security/
    │           │       ├── auth.ts.html
    │           │       └── index.html
    │           ├── shared/
    │           │   ├── index.html
    │           │   ├── index.ts.html
    │           │   └── components/
    │           │       └── ui/
    │           │           ├── LoadingSpinner.tsx.html
    │           │           └── index.html
    │           ├── store/
    │           │   ├── aiMatchingStore.ts.html
    │           │   ├── index.html
    │           │   ├── index.ts.html
    │           │   ├── messageStore.ts.html
    │           │   ├── types.ts.html
    │           │   ├── slices/
    │           │   │   ├── authSlice.ts.html
    │           │   │   ├── bookingSlice.ts.html
    │           │   │   ├── calendarSlice.ts.html
    │           │   │   ├── index.html
    │           │   │   ├── loyaltySlice.ts.html
    │           │   │   ├── retreatSlice.ts.html
    │           │   │   ├── serviceSlice.ts.html
    │           │   │   ├── socialSlice.ts.html
    │           │   │   ├── uiSlice.ts.html
    │           │   │   └── web3Slice.ts.html
    │           │   └── thunks/
    │           │       ├── calendarThunks.ts.html
    │           │       └── index.html
    │           ├── styles/
    │           │   ├── index.html
    │           │   └── theme.ts.html
    │           ├── theme/
    │           │   ├── index.html
    │           │   └── index.ts.html
    │           ├── types/
    │           │   ├── activities.ts.html
    │           │   ├── analytics.ts.html
    │           │   ├── auth.ts.html
    │           │   ├── auth.types.ts.html
    │           │   ├── booking.ts.html
    │           │   ├── calendar.ts.html
    │           │   ├── community.ts.html
    │           │   ├── education.ts.html
    │           │   ├── gdpr.ts.html
    │           │   ├── index.html
    │           │   ├── index.ts.html
    │           │   ├── insurance.ts.html
    │           │   ├── logger.ts.html
    │           │   ├── marketplace.ts.html
    │           │   ├── monitoring.ts.html
    │           │   ├── routing.ts.html
    │           │   ├── user.ts.html
    │           │   └── web3.ts.html
    │           └── utils/
    │               ├── AppProviders.tsx.html
    │               ├── Web3Provider.tsx.html
    │               ├── accessibility.ts.html
    │               ├── aiMatching.ts.html
    │               ├── aiMatchingStore.ts.html
    │               ├── analytics.ts.html
    │               ├── auth-header.js.html
    │               ├── auth-header.ts.html
    │               ├── auth.service.js.html
    │               ├── auth.service.ts.html
    │               ├── axiosConfig.ts.html
    │               ├── chatbot.ts.html
    │               ├── chatbotStore.ts.html
    │               ├── community.service.js.html
    │               ├── community.service.ts.html
    │               ├── config.ts.html
    │               ├── dateTime.ts.html
    │               ├── education.ts.html
    │               ├── educationStore.ts.html
    │               ├── errorBoundary.tsx.html
    │               ├── errorHandler.ts.html
    │               ├── featureFlags.ts.html
    │               ├── formatters.ts.html
    │               ├── group.ts.html
    │               ├── groupStore.ts.html
    │               ├── imageOptimization.ts.html
    │               ├── index.html
    │               ├── insurance.ts.html
    │               ├── invoice.ts.html
    │               ├── invoiceGenerator.ts.html
    │               ├── jsonld.ts.html
    │               ├── jwt.ts.html
    │               ├── logger.ts.html
    │               ├── loyalty.ts.html
    │               ├── loyaltyStore.ts.html
    │               ├── marketplace.ts.html
    │               ├── marketplaceStore.ts.html
    │               ├── matching.ts.html
    │               ├── message.ts.html
    │               ├── messageStore.ts.html
    │               ├── nft.ts.html
    │               ├── nftService.ts.html
    │               ├── nftStore.ts.html
    │               ├── notification.ts.html
    │               ├── notificationStore.ts.html
    │               ├── oerformance.ts.html
    │               ├── openai.ts.html
    │               ├── partner.ts.html
    │               ├── partnerStats.ts.html
    │               ├── performance.ts.html
    │               ├── retreat.service.js.html
    │               ├── retreat.service.ts.html
    │               ├── roles.ts.html
    │               ├── routeLogger.ts.html
    │               ├── rtl.ts.html
    │               ├── security.ts.html
    │               ├── securityAudit.ts.html
    │               ├── securityMiddleware.ts.html
    │               ├── sentry.ts.html
    │               ├── sitemap.ts.html
    │               ├── social.ts.html
    │               ├── socialStore.ts.html
    │               ├── socket.ts.html
    │               ├── timeUtils.ts.html
    │               ├── user.service.js.html
    │               ├── user.service.ts.html
    │               ├── validationSchemas.ts.html
    │               ├── web3Storage.ts.html
    │               ├── insurance/
    │               │   ├── index.html
    │               │   ├── insuranceService.ts.html
    │               │   └── providers/
    │               │       ├── aigAPI.ts.html
    │               │       ├── allianzAPI.ts.html
    │               │       └── index.html
    │               └── security/
    │                   ├── auditLog.ts.html
    │                   ├── auth.ts.html
    │                   ├── authorization.ts.html
    │                   ├── bruteForceProtection.ts.html
    │                   ├── cryptography.ts.html
    │                   ├── deviceFingerprint.ts.html
    │                   ├── encryption.ts.html
    │                   ├── inactivityManager.ts.html
    │                   ├── index.html
    │                   ├── mfa.ts.html
    │                   ├── passwordPolicy.ts.html
    │                   ├── sanitization.ts.html
    │                   ├── secureStorage.ts.html
    │                   ├── securityHeaders.ts.html
    │                   └── sessionManager.ts.html
    ├── cypress/
    │   ├── tsconfig.json
    │   ├── .DS_Store
    │   ├── e2e/
    │   │   ├── auth.cy.ts
    │   │   ├── booking.cy.ts
    │   │   ├── performance.cy.ts
    │   │   ├── professional.cy.ts
    │   │   ├── retreat-booking.cy.ts
    │   │   ├── user-profile.cy.ts
    │   │   ├── visual.cy.ts
    │   │   ├── microservices/
    │   │   │   └── nft.cy.ts
    │   │   └── professional/
    │   │       └── dashboard.cy.ts
    │   ├── fixtures/
    │   │   └── analytics.json
    │   └── support/
    │       ├── commands.ts
    │       ├── e2e.ts
    │       └── index.d.ts
    ├── k8s/
    │   ├── deployment.yaml
    │   └── service.yaml
    ├── lighthouse/
    │   └── booking.test.js
    ├── public/
    │   ├── _headers
    │   ├── index.html
    │   ├── manifest.json
    │   ├── service-worker.js
    │   ├── serviceWorker.js
    │   ├── .DS_Store
    │   ├── icons/
    │   ├── images/
    │   │   └── chains/
    │   ├── locales/
    │   │   ├── en/
    │   │   │   └── common.json
    │   │   └── fr/
    │   │       └── common.json
    │   └── workers/
    │       └── compression.worker.js
    ├── scripts/
    │   ├── generate-icons.js
    │   ├── generate-sitemap.ts
    │   └── lighthouse.js
    ├── src/
    │   ├── App.css
    │   ├── App.tsx
    │   ├── api.ts
    │   ├── config.ts
    │   ├── index.css
    │   ├── index.tsx
    │   ├── main.tsx
    │   ├── serviceWorker.ts
    │   ├── setupTests.test.tsx
    │   ├── setupTests.ts
    │   ├── setupTests.tsx
    │   ├── test-utils.tsx
    │   ├── theme.ts
    │   ├── vite-env.d.ts
    │   ├── .DS_Store
    │   ├── __tests__/
    │   │   └── hooks/
    │   │       └── usePerformance.test.ts
    │   ├── api/
    │   │   ├── api.ts
    │   │   ├── apiClient.ts
    │   │   ├── axiosConfig.ts
    │   │   ├── carRental.ts
    │   │   ├── community.ts
    │   │   ├── profile.ts
    │   │   ├── retreats.ts
    │   │   ├── socialVideo.ts
    │   │   └── web3.ts
    │   ├── assets/
    │   ├── components/
    │   │   ├── README.md
    │   │   ├── AIAgentManager.tsx
    │   │   ├── AIRecommendations.tsx
    │   │   ├── AdminGuard.tsx
    │   │   ├── AdminSidebar.tsx
    │   │   ├── AllyProvider.tsx
    │   │   ├── App.test.tsx
    │   │   ├── BlogHighlights.tsx
    │   │   ├── BookingForm.tsx
    │   │   ├── BookingList.tsx
    │   │   ├── BookingModal.tsx
    │   │   ├── CalendarComponent.tsx
    │   │   ├── CarRentalManager.tsx
    │   │   ├── CommunitySection.tsx
    │   │   ├── ConfigForm.tsx
    │   │   ├── EducationManager.tsx
    │   │   ├── ErrorBoundary.tsx
    │   │   ├── ExpenseManager.tsx
    │   │   ├── FeaturedRetreats.tsx
    │   │   ├── Features.tsx
    │   │   ├── FlightFinder.tsx
    │   │   ├── Footer.tsx
    │   │   ├── Foundation.tsx
    │   │   ├── Hero.tsx
    │   │   ├── HotelBookingManager.tsx
    │   │   ├── InsuranceComparator.tsx
    │   │   ├── InteractiveMap.tsx
    │   │   ├── LiveRetreats.tsx
    │   │   ├── LivestreamSection.tsx
    │   │   ├── LoadingSpinner.tsx
    │   │   ├── Login.tsx
    │   │   ├── LoyaltyProgramManager.tsx
    │   │   ├── MarketplaceManager.tsx
    │   │   ├── MessagingManager.tsx
    │   │   ├── NFTGallery.tsx
    │   │   ├── Navbar.tsx
    │   │   ├── PartnerRegistrationManager.tsx
    │   │   ├── Partners.tsx
    │   │   ├── RealTimeMetrics.tsx
    │   │   ├── RetreatCalendar.tsx
    │   │   ├── RetreatCard.tsx
    │   │   ├── RetreatCategories.tsx
    │   │   ├── RetreatFilters.tsx
    │   │   ├── RetreatHighlights.tsx
    │   │   ├── RetreatList.tsx
    │   │   ├── RetreatListHeader.tsx
    │   │   ├── RetreatMap.tsx
    │   │   ├── RetreatProMatcherManager.tsx
    │   │   ├── RetreatSearch.tsx
    │   │   ├── RetreatSort.tsx
    │   │   ├── RetreatStats.tsx
    │   │   ├── RetreatStreamManager.tsx
    │   │   ├── RouteErrorBoundary.tsx
    │   │   ├── ScrollToTop.tsx
    │   │   ├── SearchTransportManager.tsx
    │   │   ├── SecureFileUpload.tsx
    │   │   ├── SecurityDashboard.tsx
    │   │   ├── SocialManager.tsx
    │   │   ├── Testimonials.tsx
    │   │   ├── TokenDistribution.tsx
    │   │   ├── TokenEcosystem.tsx
    │   │   ├── TokenMetrics.tsx
    │   │   ├── TransportBookingManager.tsx
    │   │   ├── VRManager.tsx
    │   │   ├── VideoManager.tsx
    │   │   ├── WalletConnect.tsx
    │   │   ├── Web3NFTManager.tsx
    │   │   ├── WebsiteCreatorManager.tsx
    │   │   ├── index.ts
    │   │   ├── Admin/
    │   │   │   ├── AdminAnalytics.tsx
    │   │   │   ├── AdminBookingList.tsx
    │   │   │   ├── AdminDashboard.tsx
    │   │   │   ├── AdvancedAnalytics.tsx
    │   │   │   ├── AutomatedReports.tsx
    │   │   │   ├── BackupManagement.tsx
    │   │   │   ├── BookingFilters.tsx
    │   │   │   ├── BookingReports.tsx
    │   │   │   ├── GeoRestrictions.tsx
    │   │   │   ├── NotificationCenter.tsx
    │   │   │   ├── PasswordManager.tsx
    │   │   │   ├── RefundManagement.tsx
    │   │   │   ├── ReportVisualizations.tsx
    │   │   │   ├── SecurityEventsList.tsx
    │   │   │   ├── SecurityNotifications.tsx
    │   │   │   ├── SecurityRules.tsx
    │   │   │   ├── SessionManager.tsx
    │   │   │   ├── SmartAlerts.tsx
    │   │   │   ├── SystemHealth.tsx
    │   │   │   ├── TwoFactorAuth.tsx
    │   │   │   ├── UserDialog.tsx
    │   │   │   └── UserManagement.tsx
    │   │   ├── Calendar/
    │   │   │   ├── DraggableEvent.tsx
    │   │   │   ├── EventCalendar.tsx
    │   │   │   ├── EventDetailsDialog.tsx
    │   │   │   ├── EventFilters.tsx
    │   │   │   ├── EventForm.tsx
    │   │   │   ├── EventNotifications.tsx
    │   │   │   ├── EventReminderDialog.tsx
    │   │   │   └── __tests__/
    │   │   │       ├── EventCalendar.test.tsx
    │   │   │       └── EventForm.test.tsx
    │   │   ├── Chat/
    │   │   │   └── ChatWindow.tsx
    │   │   ├── FileList/
    │   │   │   ├── Filelist.css
    │   │   │   └── Filelist.tsx
    │   │   ├── FileUploader/
    │   │   │   ├── FileUploader.css
    │   │   │   ├── FileUploader.tsx
    │   │   │   ├── index.tsx
    │   │   │   └── styles.css
    │   │   ├── Header/
    │   │   │   ├── Header.css
    │   │   │   ├── Header.tsx
    │   │   │   ├── HeaderCarousel.tsx
    │   │   │   ├── NavigationLinks.ts
    │   │   │   ├── NavigationLinks.tsx
    │   │   │   └── index.ts
    │   │   ├── PrivateRoute/
    │   │   │   └── PrivateRoute.tsx
    │   │   ├── RetreatBooking/
    │   │   │   └── RetreatBookingForm.tsx
    │   │   ├── SEO/
    │   │   │   ├── Breadcrumbs.tsx
    │   │   │   ├── CommunitySEO.tsx
    │   │   │   ├── DefaultSEO.tsx
    │   │   │   ├── EventSEO.tsx
    │   │   │   ├── GoogleSearchConsole.tsx
    │   │   │   ├── MetaTags.tsx
    │   │   │   ├── PageMetadata.tsx
    │   │   │   ├── PagePerformance.tsx
    │   │   │   ├── PageSEO.tsx
    │   │   │   ├── ProfileSEO.tsx
    │   │   │   ├── SEO.tsx
    │   │   │   ├── SEODashboard.tsx
    │   │   │   ├── SearchSEO.tsx
    │   │   │   ├── Dashboard/
    │   │   │   │   └── SEODashboard.tsx
    │   │   │   └── Suggestions/
    │   │   │       ├── SEOFilters.tsx
    │   │   │       ├── SEOStats.tsx
    │   │   │       └── SEOSuggestions.tsx
    │   │   ├── VirtualEvents/
    │   │   │   ├── EventChat.tsx
    │   │   │   ├── InteractiveWorkshop.tsx
    │   │   │   ├── LivestreamComponent.tsx
    │   │   │   ├── ParticipantsList.tsx
    │   │   │   ├── VirtualEventRoom.tsx
    │   │   │   ├── Activities/
    │   │   │   │   ├── ActivityCard.tsx
    │   │   │   │   ├── ActivityFilters.tsx
    │   │   │   │   ├── ActivityManager.tsx
    │   │   │   │   ├── BreakoutRoomActivity.tsx
    │   │   │   │   ├── CreateActivityDialog.tsx
    │   │   │   │   ├── ExerciseActivity.tsx
    │   │   │   │   ├── MeditationActivity.tsx
    │   │   │   │   ├── PollActivity.tsx
    │   │   │   │   ├── QuizActivity.tsx
    │   │   │   │   ├── ThreeDActivity.tsx
    │   │   │   │   ├── WhiteboardActivity.tsx
    │   │   │   │   └── Mobile/
    │   │   │   │       ├── MobileActivityBase.tsx
    │   │   │   │       ├── MobileBreakoutRoom.tsx
    │   │   │   │       ├── MobileChat.tsx
    │   │   │   │       ├── MobileEventViewer.tsx
    │   │   │   │       ├── MobileNetworking.tsx
    │   │   │   │       ├── MobilePoll.tsx
    │   │   │   │       ├── MobileQA.tsx
    │   │   │   │       ├── MobileVirtualExpo.tsx
    │   │   │   │       ├── MobileWhiteboard.tsx
    │   │   │   │       └── MobileWorkshop.tsx
    │   │   │   ├── ScheduleBuilder/
    │   │   │   │   ├── AIRecommendations.tsx
    │   │   │   │   └── ScheduleBuilder.tsx
    │   │   │   └── VideoPlayer/
    │   │   │       └── MobileVideoPlayer.tsx
    │   │   ├── accessibility/
    │   │   │   ├── AccessibleComponents.tsx
    │   │   │   └── KeyboardShortcuts.tsx
    │   │   ├── affiliate/
    │   │   │   ├── AffiliateBadges.tsx
    │   │   │   ├── AffiliateChallenges.tsx
    │   │   │   ├── AffiliateExclusiveRewards.tsx
    │   │   │   ├── AffiliateLeaderboard.tsx
    │   │   │   ├── AffiliateList.tsx
    │   │   │   ├── AffiliateNotifications.tsx
    │   │   │   ├── AffiliatePoints.tsx
    │   │   │   ├── AffiliateProgressCharts.tsx
    │   │   │   ├── AffiliateREferralProgram.tsx
    │   │   │   ├── AffiliateRegistration.tsx
    │   │   │   ├── AffiliateSeasonalEvents.tsx
    │   │   │   └── AffiliateStats.tsx
    │   │   ├── ai/
    │   │   │   ├── MatchingResults.tsx
    │   │   │   └── PartnerMatchingAI.tsx
    │   │   ├── analytics/
    │   │   │   ├── AnalyticsChart.tsx
    │   │   │   ├── BookingTrendsChart.tsx
    │   │   │   ├── CustomerInsights.tsx
    │   │   │   ├── MetricCard.tsx
    │   │   │   ├── OrganizerDashboard.tsx
    │   │   │   ├── PartnerAnalytics.tsx
    │   │   │   ├── RetreatAnalytics.tsx
    │   │   │   └── RevenueAnalytics.tsx
    │   │   ├── analyzer/
    │   │   │   └── AnalyzerDashboard.tsx
    │   │   ├── atoms/
    │   │   │   ├── Button/
    │   │   │   │   ├── Button.stories.tsx
    │   │   │   │   └── Button.tsx
    │   │   │   └── Skeleton/
    │   │   │       └── Skeleton.tsx
    │   │   ├── auth/
    │   │   │   ├── AdminGuard.tsx
    │   │   │   ├── AuthButtons.tsx
    │   │   │   ├── AuthComponent.tsx
    │   │   │   ├── AuthGuard.tsx
    │   │   │   ├── LoginForm.tsx
    │   │   │   ├── LoginPage.tsx
    │   │   │   ├── PrivateRoute.tsx
    │   │   │   ├── ProtectedRoute.tsx
    │   │   │   ├── RegisterForm.tsx
    │   │   │   ├── RegisterPage.tsx
    │   │   │   ├── RequestPasswordResetForm.tsx
    │   │   │   ├── ResetPasswordForm.tsx
    │   │   │   ├── RoleBasedRoute.tsx
    │   │   │   ├── TwoFactorVerification.tsx
    │   │   │   ├── UnauthorizedPage.tsx
    │   │   │   ├── Verify2FAForm.tsx
    │   │   │   ├── VerifyEmailForm.tsx
    │   │   │   └── __tests__/
    │   │   │       ├── AuthComponent.test.tsx
    │   │   │       ├── LoginForm.test.tsx
    │   │   │       ├── LoginPage.test.tsx
    │   │   │       ├── PrivateRoute.test.tsx
    │   │   │       ├── RegisterPage.test.tsx
    │   │   │       ├── ResetPasswordForm.test.tsx
    │   │   │       └── Verify2FAForm.test.tsx
    │   │   ├── booking/
    │   │   │   ├── GroupBooking.tsx
    │   │   │   ├── RetreatBooking.tsx
    │   │   │   ├── SecureBookingForm.tsx
    │   │   │   └── SmartBooking.tsx
    │   │   ├── car-rental/
    │   │   │   ├── CarCard.tsx
    │   │   │   ├── CarList.tsx
    │   │   │   └── CarSearchForm.tsx
    │   │   ├── chatbot/
    │   │   │   └── Chatbot.tsx
    │   │   ├── common/
    │   │   │   ├── ConfirmDialog.tsx
    │   │   │   ├── CustomSnackbar.tsx
    │   │   │   ├── ErrorBoundary.tsx
    │   │   │   ├── ErrorFeedback.tsx
    │   │   │   ├── FormField.tsx
    │   │   │   ├── Image0ptimizer.tsx
    │   │   │   ├── LanguageSelector.tsx
    │   │   │   ├── LanguageSwitcher.tsx
    │   │   │   ├── Layout.tsx
    │   │   │   ├── LazyImage.tsx
    │   │   │   ├── LazyRoute.tsx
    │   │   │   ├── LoadingButton.tsx
    │   │   │   ├── LoadingFallback.tsx
    │   │   │   ├── LoadingScreen.tsx
    │   │   │   ├── LoadingSpinner.tsx
    │   │   │   ├── LoadingView.tsx
    │   │   │   ├── Modal.tsx
    │   │   │   ├── Notification.tsx
    │   │   │   ├── NotificationItem.tsx
    │   │   │   ├── NotificationProvider.tsx
    │   │   │   ├── OptimizedImage.tsx
    │   │   │   ├── OptimizedPage.tsx
    │   │   │   ├── PageHeader.tsx
    │   │   │   ├── PageHelmet.tsx
    │   │   │   ├── PageTransition.tsx
    │   │   │   ├── Pagination.tsx
    │   │   │   ├── PrivateRoute.tsx
    │   │   │   ├── RetreatCard.tsx
    │   │   │   ├── RoleBasedRoute.tsx
    │   │   │   ├── SEO.tsx
    │   │   │   ├── ScrollToTop.tsx
    │   │   │   ├── SearchAndFilter.tsx
    │   │   │   ├── SecureForm.tsx
    │   │   │   ├── SecureInput.tsx
    │   │   │   ├── Sidebar.tsx
    │   │   │   ├── Skeleton.tsx
    │   │   │   ├── SkeletonLoader.tsx
    │   │   │   ├── SkeletonPatterns.tsx
    │   │   │   ├── ThemeToggle.tsx
    │   │   │   ├── Toast.tsx
    │   │   │   ├── VirtualScroll.tsx
    │   │   │   ├── WalletButton.tsx
    │   │   │   ├── index.ts
    │   │   │   ├── withImageOptimization.tsx
    │   │   │   ├── withLazyLoading.tsx
    │   │   │   ├── Form/
    │   │   │   │   └── Input.tsx
    │   │   │   ├── Table/
    │   │   │   │   └── Table.tsx
    │   │   │   └── __tests__/
    │   │   │       └── accessibility.test.tsx
    │   │   ├── community/
    │   │   │   ├── CommunityFeed.tsx
    │   │   │   ├── CommunityFilters.tsx
    │   │   │   ├── CommunityMemberCard.tsx
    │   │   │   ├── CommunityMemberList.tsx
    │   │   │   ├── CommunitySearch.tsx
    │   │   │   ├── CreatePost.tsx
    │   │   │   ├── GroupVirtualEvents.tsx
    │   │   │   ├── Post.tsx
    │   │   │   ├── PostFeed.tsx
    │   │   │   ├── PostList.tsx
    │   │   │   ├── ThematicGroups.tsx
    │   │   │   ├── VirtualizedMemberList.tsx
    │   │   │   └── __tests__/
    │   │   │       ├── CreatePost.test.tsx
    │   │   │       └── Post.test.tsx
    │   │   ├── contact/
    │   │   │   └── SecureContactForm.tsx
    │   │   ├── dashboard/
    │   │   │   ├── AnalyticsCard.tsx
    │   │   │   ├── AnalyticsDashboard.tsx
    │   │   │   ├── Dashboard.tsx
    │   │   │   ├── DashboardMessages.tsx
    │   │   │   ├── PartnerDashboard.tsx
    │   │   │   └── __tests__/
    │   │   │       ├── AnalyticsCard.test.tsx
    │   │   │       └── AnalyticsDashboard.test.tsx
    │   │   ├── devops/
    │   │   │   └── APIGatewayMonitor.tsx
    │   │   ├── education/
    │   │   │   ├── CourseCard.tsx
    │   │   │   ├── CourseFilters.tsx
    │   │   │   └── CourseGrid.tsx
    │   │   ├── error/
    │   │   │   ├── ErrorBoundary.tsx
    │   │   │   └── RouteErrorBoundary.tsx
    │   │   ├── errors/
    │   │   │   └── ErrorFallback.tsx
    │   │   ├── events/
    │   │   │   └── EventCalendar.tsx
    │   │   ├── filters/
    │   │   │   └── AdvancedFilters.tsx
    │   │   ├── flights/
    │   │   │   ├── AirportSearch.tsx
    │   │   │   ├── FlightDatePicker.tsx
    │   │   │   ├── FlightResults.tsx
    │   │   │   ├── FlightSearch.tsx
    │   │   │   └── PassengerSelector.tsx
    │   │   ├── gamification/
    │   │   │   ├── AchievementSystem.tsx
    │   │   │   └── Achievements.tsx
    │   │   ├── gdpr/
    │   │   │   ├── ConsentManager.tsx
    │   │   │   └── SubjectRequestForm.tsx
    │   │   ├── gift-cards/
    │   │   │   ├── GiftCard.tsx
    │   │   │   ├── GiftCardAdmin.tsx
    │   │   │   └── GiftCardList.tsx
    │   │   ├── groups/
    │   │   │   └── CreateGroupModal.tsx
    │   │   ├── insurance/
    │   │   │   ├── InsuranceComparison.tsx
    │   │   │   ├── InsuranceQuoteForm.tsx
    │   │   │   └── PolicyDetails.tsx
    │   │   ├── invoice/
    │   │   │   ├── InvoiceActions.tsx
    │   │   │   ├── InvoiceGenerator.tsx
    │   │   │   └── InvoiceTemplate.tsx
    │   │   ├── layout/
    │   │   │   ├── AuthLayout.module.css
    │   │   │   ├── ErrorBoundary.tsx
    │   │   │   ├── Footer.tsx
    │   │   │   ├── Header.tsx
    │   │   │   ├── Layout.tsx
    │   │   │   ├── MainLayout.tsx
    │   │   │   ├── MobileOptimizedLayout.tsx
    │   │   │   ├── PrivateLayout.tsx
    │   │   │   ├── PublicLayout.tsx
    │   │   │   ├── PublicNavbar.tsx
    │   │   │   ├── ResponsiveLayout.tsx
    │   │   │   ├── Sidebar.tsx
    │   │   │   ├── TopBar.tsx
    │   │   │   ├── index.ts
    │   │   │   ├── index.tsx
    │   │   │   ├── types.ts
    │   │   │   ├── MobileDrawer/
    │   │   │   │   └── MobileDrawer.tsx
    │   │   │   ├── ProfileMenu/
    │   │   │   │   └── ProfileMenu.tsx
    │   │   │   ├── SearchBar/
    │   │   │   │   ├── SearchBar.tsx
    │   │   │   │   └── __tests__/
    │   │   │   │       └── SearchBar.test.tsx
    │   │   │   └── __tests__/
    │   │   │       ├── Layout.test.tsx
    │   │   │       └── Sidebar.test.tsx
    │   │   ├── layouts/
    │   │   │   ├── AdminLayout.tsx
    │   │   │   └── MainLayout.tsx
    │   │   ├── livestream/
    │   │   │   ├── LiveChat.tsx
    │   │   │   ├── LivestreamRetreat.tsx
    │   │   │   ├── ParticipantsList.tsx
    │   │   │   └── StreamControls.tsx
    │   │   ├── loyalty/
    │   │   │   ├── LoyaltyCard.tsx
    │   │   │   ├── LoyaltyTierCard.tsx
    │   │   │   ├── PointsConverter.tsx
    │   │   │   └── PointsHistory.tsx
    │   │   ├── meditation/
    │   │   │   └── AIMeditationGuide.tsx
    │   │   ├── messaging/
    │   │   │   ├── ChatHeader.tsx
    │   │   │   ├── ChatWindow.tsx
    │   │   │   ├── ConversationList.tsx
    │   │   │   ├── MessageInput.tsx
    │   │   │   ├── MessageList.tsx
    │   │   │   └── MessagingPanel.tsx
    │   │   ├── monitoring/
    │   │   │   ├── AlertRuleForm.tsx
    │   │   │   ├── AlertsManager.tsx
    │   │   │   ├── Dashboard.tsx
    │   │   │   ├── MetricCard.tsx
    │   │   │   ├── PerformanceChart.tsx
    │   │   │   ├── PerformanceMetrics.tsx
    │   │   │   ├── ReportsManager.tsx
    │   │   │   ├── ScheduledReportForm.tsx
    │   │   │   ├── TestDetails.tsx
    │   │   │   ├── charts/
    │   │   │   │   └── ReportChart.tsx
    │   │   │   ├── exports/
    │   │   │   │   └── ReportExport.tsx
    │   │   │   └── filters/
    │   │   │       └── ReportFilters.tsx
    │   │   ├── navigation/
    │   │   │   ├── ContextualNavigation.tsx
    │   │   │   ├── CustomShortcutsManager.tsx
    │   │   │   ├── Footer.tsx
    │   │   │   ├── GestureVisualizer.tsx
    │   │   │   ├── KeyboardShortcuts.tsx
    │   │   │   ├── NavItem.tsx
    │   │   │   ├── Navbar.tsx
    │   │   │   ├── NavigationHistory.tsx
    │   │   │   ├── NavigationPreferences.tsx
    │   │   │   ├── Sidebar.tsx
    │   │   │   ├── VoiceNavigation.tsx
    │   │   │   └── __tests__/
    │   │   │       └── NavItem.test.tsx
    │   │   ├── nft/
    │   │   │   ├── NFTCard.tsx
    │   │   │   ├── NFTCollection.tsx
    │   │   │   ├── NFTGallery.tsx
    │   │   │   ├── NFTGrid.tsx
    │   │   │   ├── NFTMinter.tsx
    │   │   │   ├── OptimizedNFTGrid.tsx
    │   │   │   └── __tests__/
    │   │   │       └── NFTGallery.test.tsx
    │   │   ├── notifications/
    │   │   │   ├── ABTestForm.tsx
    │   │   │   ├── ABTestResults.tsx
    │   │   │   ├── ABTestingDashboard.tsx
    │   │   │   ├── ConnectionStatus.tsx
    │   │   │   ├── NotificationAnalytics.tsx
    │   │   │   ├── NotificationBadge.tsx
    │   │   │   ├── NotificationCenter.tsx
    │   │   │   ├── NotificationDashboard.tsx
    │   │   │   ├── NotificationDropdown.tsx
    │   │   │   ├── NotificationHistory.tsx
    │   │   │   ├── NotificationManager.tsx
    │   │   │   ├── NotificationPreferences.tsx
    │   │   │   ├── NotificationScheduler.tsx
    │   │   │   ├── NotificationSystem.tsx
    │   │   │   ├── NotificationTemplates.tsx
    │   │   │   └── RealTimeNotifications.tsx
    │   │   ├── optimizations/
    │   │   │   ├── MediaResource.tsx
    │   │   │   └── OptimizedImage.tsx
    │   │   ├── partner/
    │   │   │   ├── PartnerHeader.tsx
    │   │   │   ├── PartnerStats.tsx
    │   │   │   ├── PartnerTierBadge.tsx
    │   │   │   ├── PartnerTypeIcon.tsx
    │   │   │   ├── StatCard.tsx
    │   │   │   └── UpgradeBanner.tsx
    │   │   ├── payment/
    │   │   │   └── SecurePaymentForm.tsx
    │   │   ├── performance/
    │   │   │   └── PerformanceMonitor.tsx
    │   │   ├── professional/
    │   │   │   ├── AnalyticsDashboard.tsx
    │   │   │   ├── BookingManagement.tsx
    │   │   │   └── PerformanceMetrics.tsx
    │   │   ├── profile/
    │   │   │   ├── AchievementsDisplay.tsx
    │   │   │   ├── ActiveSessions.tsx
    │   │   │   ├── ActivityFeed.tsx
    │   │   │   ├── AppPermissions.tsx
    │   │   │   ├── ConnectedApps.tsx
    │   │   │   ├── ConnectionsGrid.tsx
    │   │   │   ├── EditProfileDialog.tsx
    │   │   │   ├── LoginHistory.tsx
    │   │   │   ├── NotificationCenter.tsx
    │   │   │   ├── PrivacyPreferences.tsx
    │   │   │   ├── ProfileAnalytics.tsx
    │   │   │   ├── ProfileCard.tsx
    │   │   │   ├── ProfileDashboard.tsx
    │   │   │   ├── ProfileSettings.tsx
    │   │   │   ├── RetreatHistory.tsx
    │   │   │   ├── SecureProfileForm.tsx
    │   │   │   ├── SecurityActivityHistory.tsx
    │   │   │   ├── SecurityAlerts.tsx
    │   │   │   ├── SecurityAudit.tsx
    │   │   │   ├── SecurityKeys.tsx
    │   │   │   ├── SecurityNavigation.tsx
    │   │   │   ├── SecurityNotifications.tsx
    │   │   │   ├── SecuritySettings.tsx
    │   │   │   ├── TrustedDevices.tsx
    │   │   │   ├── UserProfile.tsx
    │   │   │   ├── WellnessPreferences.tsx
    │   │   │   └── __tests__/
    │   │   │       └── ProfileSettings.test.tsx
    │   │   ├── referral/
    │   │   │   └── ReferralProgram.tsx
    │   │   ├── retreats/
    │   │   │   ├── BookingModal.tsx
    │   │   │   ├── PaymentDetails.tsx
    │   │   │   ├── RetreatCard.js
    │   │   │   ├── RetreatCard.tsx
    │   │   │   ├── RetreatDetail.tsx
    │   │   │   ├── RetreatDetails.tsx
    │   │   │   ├── RetreatFilters.tsx
    │   │   │   ├── RetreatGrid.js
    │   │   │   ├── RetreatGrid.tsx
    │   │   │   ├── RetreatList.tsx
    │   │   │   ├── ReviewSection.tsx
    │   │   │   └── __tests__/
    │   │   │       ├── RetreatFilters.test.tsx
    │   │   │       └── RetreatList.test.tsx
    │   │   ├── reviews/
    │   │   │   ├── ReviewSystem.tsx
    │   │   │   └── SecureReviewForm.tsx
    │   │   ├── rewards/
    │   │   │   └── RewardsCatalog.tsx
    │   │   ├── routes/
    │   │   │   ├── PrivateRoutes.tsx
    │   │   │   ├── RoleRoute.tsx
    │   │   │   └── Web3Route.tsx
    │   │   ├── routing/
    │   │   │   ├── AdminRoute.tsx
    │   │   │   ├── NavigationTransition.tsx
    │   │   │   ├── PrivateRoute.tsx
    │   │   │   ├── ProtectedRoute.tsx
    │   │   │   ├── PublicRoute.tsx
    │   │   │   ├── RoleBasedRedirect.tsx
    │   │   │   ├── RoleRoute.tsx
    │   │   │   └── __tests__/
    │   │   │       └── NavigationTransition.test.tsx
    │   │   ├── search/
    │   │   │   ├── GlobalSearch.tsx
    │   │   │   ├── SearchBar.tsx
    │   │   │   └── SearchResults.tsx
    │   │   ├── security/
    │   │   │   ├── AuditLog.tsx
    │   │   │   ├── DeviceManager.tsx
    │   │   │   ├── InactivityMonitor.tsx
    │   │   │   ├── PasswordManager.tsx
    │   │   │   ├── ProtectedRoute.tsx
    │   │   │   ├── RecentActivityList.tsx
    │   │   │   ├── SecurityAlertsList.tsx
    │   │   │   ├── SecurityChart.tsx
    │   │   │   ├── SecurityDashboard.tsx
    │   │   │   ├── SecurityProvider.tsx
    │   │   │   ├── SecuritySettings.tsx
    │   │   │   ├── SessionManager.tsx
    │   │   │   ├── TwoFactorSetup.tsx
    │   │   │   ├── TwoFactorVerify.tsx
    │   │   │   ├── Web3Route.tsx
    │   │   │   ├── withXSSProtection.tsx
    │   │   │   └── __tests__/
    │   │   │       ├── SecuritySettings.test.tsx
    │   │   │       └── TwoFactorSetup.test.tsx
    │   │   ├── settings/
    │   │   │   ├── SecureSettingsForm.tsx
    │   │   │   └── TwoFactorSettings.tsx
    │   │   ├── shared/
    │   │   │   ├── Map.tsx
    │   │   │   └── SharedHeader.tsx
    │   │   ├── social/
    │   │   │   ├── CreatePost.tsx
    │   │   │   ├── LiveStream.tsx
    │   │   │   ├── PostCard.tsx
    │   │   │   ├── SocialFeed.tsx
    │   │   │   ├── SocialSidebar.tsx
    │   │   │   ├── Feed/
    │   │   │   │   ├── CreatePost.tsx
    │   │   │   │   ├── FeedFilters.tsx
    │   │   │   │   ├── FeedItem.tsx
    │   │   │   │   ├── FeedList.tsx
    │   │   │   │   ├── RecommendedPosts.tsx
    │   │   │   │   ├── SocialFeedContainer.tsx
    │   │   │   │   └── TrendingPosts.tsx
    │   │   │   ├── Poll/
    │   │   │   │   ├── CreatePollDialog.tsx
    │   │   │   │   ├── PollComponent.tsx
    │   │   │   │   └── __tests__/
    │   │   │   │       └── PollComponent.test.tsx
    │   │   │   └── __tests__/
    │   │   │       └── SocialFeed.test.tsx
    │   │   ├── storage/
    │   │   │   └── StorageManager.tsx
    │   │   ├── token/
    │   │   │   └── RABTokenIntegration.tsx
    │   │   ├── transitions/
    │   │   │   └── PageTransition.tsx
    │   │   ├── ui/
    │   │   │   ├── Badge.tsx
    │   │   │   ├── LoadingSpinner.tsx
    │   │   │   ├── OptimizedImage.tsx
    │   │   │   ├── Sidebar.tsx
    │   │   │   ├── Skeletons.tsx
    │   │   │   ├── Transitions.tsx
    │   │   │   ├── button.tsx
    │   │   │   ├── card.tsx
    │   │   │   ├── index.ts
    │   │   │   ├── input.tsx
    │   │   │   └── __tests__/
    │   │   │       └── OptimizedImage.test.tsx
    │   │   ├── video/
    │   │   │   ├── GroupVideoCall.tsx
    │   │   │   └── VideoCall.tsx
    │   │   └── wellness/
    │   │       └── WellnessTracker.tsx
    │   ├── config/
    │   │   ├── adminRoutes.ts
    │   │   ├── api.ts
    │   │   ├── apiRoutes.ts
    │   │   ├── appConfig.ts
    │   │   ├── axios.ts
    │   │   ├── chains.ts
    │   │   ├── env.ts
    │   │   ├── index.js
    │   │   ├── index.ts
    │   │   ├── monitoring.ts
    │   │   ├── routeConfig.ts
    │   │   ├── routeMetadata.ts
    │   │   └── routes.ts
    │   ├── constants/
    │   │   ├── routes.ts
    │   │   └── final/
    │   │       └── Project-Final/
    │   │           └── frontend/
    │   │               └── src/
    │   │                   └── constants/
    │   ├── containers/
    │   │   ├── Financial/
    │   │   │   ├── context/
    │   │   │   │   └── PaymentContext.ts
    │   │   │   ├── layouts/
    │   │   │   │   └── FinancialLayout.tsx
    │   │   │   └── pages/
    │   │   │       ├── DashboardPage.tsx
    │   │   │       ├── PaymentMethodsPage.tsx
    │   │   │       └── TransactionHistoryPage.tsx
    │   │   ├── Search-Transport-Service/
    │   │   │   └── pages/
    │   │   │       └── TransportSearch.tsx
    │   │   └── Social/
    │   │       └── pages/
    │   │           ├── BlogPage.tsx
    │   │           └── BlogPostDetail.tsx
    │   ├── contexts/
    │   │   ├── AuthContext.tsx
    │   │   ├── BookingContext.tsx
    │   │   ├── NotificationContext.tsx
    │   │   ├── SearchContext.tsx
    │   │   ├── SnackbarContext.tsx
    │   │   ├── ThemeContext.tsx
    │   │   ├── UIContext.tsx
    │   │   ├── Web3Context.tsx
    │   │   └── index.ts
    │   ├── contracts/
    │   │   ├── RetreatBooking.sol
    │   │   └── test/
    │   │       └── RetreatBooking.test.ts
    │   ├── features/
    │   │   ├── auth/
    │   │   │   ├── index.ts
    │   │   │   ├── __tests__/
    │   │   │   │   └── AuthContext.test.tsx
    │   │   │   ├── components/
    │   │   │   │   ├── Login.js
    │   │   │   │   ├── Login.tsx
    │   │   │   │   ├── Register.js
    │   │   │   │   ├── Register.tsx
    │   │   │   │   ├── ResetPassword.tsx
    │   │   │   │   ├── VerifyEmail.tsx
    │   │   │   │   └── index.ts
    │   │   │   ├── contexts/
    │   │   │   │   └── AuthContext.tsx
    │   │   │   ├── services/
    │   │   │   │   └── authService.ts
    │   │   │   └── types/
    │   │   │       └── index.ts
    │   │   ├── calendar/
    │   │   │   └── contexts/
    │   │   │       └── CalendarContext.tsx
    │   │   ├── marketplace/
    │   │   │   ├── index.ts
    │   │   │   ├── types.ts
    │   │   │   ├── components/
    │   │   │   │   ├── CategoryFilter.tsx
    │   │   │   │   ├── Marketplace.tsx
    │   │   │   │   ├── ProductCard.tsx
    │   │   │   │   ├── ProductGrid.tsx
    │   │   │   │   ├── SearchBar.tsx
    │   │   │   │   ├── ShoppingCart.tsx
    │   │   │   │   └── index.ts
    │   │   │   ├── store/
    │   │   │   │   └── marketplaceStore.ts
    │   │   │   └── types/
    │   │   │       └── marketplace.ts
    │   │   ├── messaging/
    │   │   │   ├── index.ts
    │   │   │   ├── components/
    │   │   │   │   ├── MessagesPage.tsx
    │   │   │   │   └── VoiceMessageRecorder.tsx
    │   │   │   └── contexts/
    │   │   │       └── MessageContext.tsx
    │   │   └── security/
    │   │       └── SecurityProvider.tsx
    │   ├── hooks/
    │   │   ├── useAIRecommendations.ts
    │   │   ├── useAccessibilityAnnouncer.ts
    │   │   ├── useAchievements.ts
    │   │   ├── useActivity.ts
    │   │   ├── useAdvancedFilters.ts
    │   │   ├── useAdvancedGestures.ts
    │   │   ├── useAnalytics.ts
    │   │   ├── useAnimation.ts
    │   │   ├── useAnimationFrame.ts
    │   │   ├── useAnimations.ts
    │   │   ├── useApi.ts
    │   │   ├── useApiState.ts
    │   │   ├── useAppState.ts
    │   │   ├── useAuth.test.tsx
    │   │   ├── useAuth.ts
    │   │   ├── useAuth.tsx
    │   │   ├── useAuthState.ts
    │   │   ├── useBooking.tsx
    │   │   ├── useBreakpoints.ts
    │   │   ├── useCache.ts
    │   │   ├── useCacheManager.ts
    │   │   ├── useCachedData.ts
    │   │   ├── useCalendar.ts
    │   │   ├── useCalendar.tsx
    │   │   ├── useCalendarViews.ts
    │   │   ├── useCommunity.ts
    │   │   ├── useCommunityEngagement.ts
    │   │   ├── useCommunityInteractions.ts
    │   │   ├── useCommunityMembers.ts
    │   │   ├── useConfig.ts
    │   │   ├── useContextualNavigation.ts
    │   │   ├── useCustomShortcuts.ts
    │   │   ├── useDialog.ts
    │   │   ├── useErrorHandler.ts
    │   │   ├── useEventFilters.ts
    │   │   ├── useEventReminders.ts
    │   │   ├── useEventsCache.ts
    │   │   ├── useEventsPagination.ts
    │   │   ├── useFiles.ts
    │   │   ├── useFinancialService.ts
    │   │   ├── useForm.ts
    │   │   ├── useFormOptimizer.ts
    │   │   ├── useFormValidation.ts
    │   │   ├── useGestureNavigation.ts
    │   │   ├── useI18n.ts
    │   │   ├── useImageLazyLoad.ts
    │   │   ├── useImageOptimizer.ts
    │   │   ├── useInfiniteQuery.ts
    │   │   ├── useIntersectionObserver.ts
    │   │   ├── useKeyboardNavigation.ts
    │   │   ├── useLayoutState.ts
    │   │   ├── useLocalStorage.ts
    │   │   ├── useLocationService.ts
    │   │   ├── useLoyaltySystem.ts
    │   │   ├── useMedia.ts
    │   │   ├── useMediaOptimization.ts
    │   │   ├── useMobileOptimization.ts
    │   │   ├── useMutation.ts
    │   │   ├── useNavigationPreferences.ts
    │   │   ├── useNotification.ts
    │   │   ├── useNotificationManager.ts
    │   │   ├── useNotificationSound.ts
    │   │   ├── useNotificationSystem.ts
    │   │   ├── useNotifications.ts
    │   │   ├── useOfflineManager.ts
    │   │   ├── useOptimization.ts
    │   │   ├── useOptimizedForm.ts
    │   │   ├── useOptimizedQuery.ts
    │   │   ├── usePageTransition.ts
    │   │   ├── usePayment.ts
    │   │   ├── usePerformance.ts
    │   │   ├── usePerformanceMetrics.ts
    │   │   ├── usePerformanceMonitor.ts
    │   │   ├── usePerformanceOptimizer.ts
    │   │   ├── usePermissions.ts
    │   │   ├── usePoll.ts
    │   │   ├── usePostRecommendations.ts
    │   │   ├── usePrefetch.ts
    │   │   ├── useProMatcher.ts
    │   │   ├── useProfile.ts
    │   │   ├── useProgressiveNavigation.ts
    │   │   ├── useProtectedRoute.ts
    │   │   ├── usePushNotifications.ts
    │   │   ├── useQueryCache.ts
    │   │   ├── useQueryConfig.ts
    │   │   ├── useQueryState.ts
    │   │   ├── useRealtimeNotifications.ts
    │   │   ├── useRenderOptimizer.ts
    │   │   ├── useResponsive.ts
    │   │   ├── useRetreatContract.ts
    │   │   ├── useRetreatManagement.ts
    │   │   ├── useRetreatService.ts
    │   │   ├── useRetreats.ts
    │   │   ├── useRoutePrefetch.ts
    │   │   ├── useSchedule.ts
    │   │   ├── useScrollPosition.ts
    │   │   ├── useScrollRestoration.ts
    │   │   ├── useSearchResults.ts
    │   │   ├── useSecurityStats.ts
    │   │   ├── useServiceBooking.ts
    │   │   ├── useServiceWorker.ts
    │   │   ├── useServices.ts
    │   │   ├── useSocialFeed.ts
    │   │   ├── useSound.ts
    │   │   ├── useTwoFactor.ts
    │   │   ├── useUserPreferences.ts
    │   │   ├── useVirtualList.ts
    │   │   ├── useVoiceNavigation.ts
    │   │   ├── useWeb3.ts
    │   │   ├── useWeb3Profile.ts
    │   │   ├── useWebSocket.ts
    │   │   ├── useWebSocketNotifications.ts
    │   │   └── __tests__/
    │   │       ├── useActivity.test.tsx
    │   │       ├── useBreakpoints.test.tsx
    │   │       ├── useCommunityEngagement.test.tsx
    │   │       ├── useCommunityInteractions.test.ts
    │   │       ├── useLayoutState.test.ts
    │   │       ├── useNotifications.test.tsx
    │   │       ├── usePoll.test.tsx
    │   │       ├── useScrollPosition.test.tsx
    │   │       └── useSocialFeed.test.tsx
    │   ├── i18n/
    │   │   ├── config.ts
    │   │   ├── index.ts
    │   │   └── locales/
    │   │       ├── en.json
    │   │       └── fr.json
    │   ├── layouts/
    │   │   ├── DashboardLayout.tsx
    │   │   ├── FinancialLayout.tsx
    │   │   ├── PrivateLayout.tsx
    │   │   ├── PublicLayout.tsx
    │   │   └── SettingsLayout.tsx
    │   ├── lib/
    │   │   ├── react-query.ts
    │   │   └── utils.ts
    │   ├── middleware/
    │   │   ├── AuthMiddleware.tsx
    │   │   ├── apiInterceptor.ts
    │   │   ├── errorMiddleware.ts
    │   │   ├── index.ts
    │   │   ├── loggingMiddleware.ts
    │   │   ├── securityMiddleware.ts
    │   │   └── __tests__/
    │   │       └── authMiddleware.test.tsx
    │   ├── pages/
    │   │   ├── AdminDashboard.tsx
    │   │   ├── AdminPanel.tsx
    │   │   ├── AffiliatePortal.tsx
    │   │   ├── BecomePartnerPage.tsx
    │   │   ├── BlogPage.tsx
    │   │   ├── CGUPage.tsx
    │   │   ├── CarDetailsPage.tsx
    │   │   ├── CarRentalPage.tsx
    │   │   ├── CareersPage.tsx
    │   │   ├── CaterersPage.tsx
    │   │   ├── CommunityMemberPage.tsx
    │   │   ├── CommunityPage.tsx
    │   │   ├── ContactPage.tsx
    │   │   ├── Dashboard.tsx
    │   │   ├── DashboardPage.tsx
    │   │   ├── EducationPage.tsx
    │   │   ├── Error404Page.tsx
    │   │   ├── Error500Page.tsx
    │   │   ├── ErrorPage.tsx
    │   │   ├── EventsPage.tsx
    │   │   ├── ExploreRetreatsPage.tsx
    │   │   ├── FAQPage.tsx
    │   │   ├── FoundationPage.tsx
    │   │   ├── GDPRPage.tsx
    │   │   ├── GalleryPage.tsx
    │   │   ├── HomePage.tsx
    │   │   ├── HostsPage.tsx
    │   │   ├── InsurancePage.tsx
    │   │   ├── InvoicePage.tsx
    │   │   ├── LandingPage.tsx
    │   │   ├── LivestreamRetreatPage.tsx
    │   │   ├── LocationPage.tsx
    │   │   ├── LoginPage.tsx
    │   │   ├── LoyaltyPage.tsx
    │   │   ├── MessagesPage.tsx
    │   │   ├── NFTGalleryPage.tsx
    │   │   ├── NewsPage.tsx
    │   │   ├── NotFoundPage.tsx
    │   │   ├── NotificationHistory.tsx
    │   │   ├── OrganizersPage.tsx
    │   │   ├── PartnersPage.tsx
    │   │   ├── PrivacyPolicyPage.tsx
    │   │   ├── Profile.tsx
    │   │   ├── ProfilePage.tsx
    │   │   ├── ProgramsPage.tsx
    │   │   ├── RegisterPage.tsx
    │   │   ├── ReportsPage.tsx
    │   │   ├── ResourcesPage.tsx
    │   │   ├── RetreatPage.tsx
    │   │   ├── RewardsPage.tsx
    │   │   ├── SecurityPage.tsx
    │   │   ├── Settings.tsx
    │   │   ├── SettingsPage.tsx
    │   │   ├── SocialFeedPage.tsx
    │   │   ├── SupportPage.tsx
    │   │   ├── TermsPage.tsx
    │   │   ├── TestimonialsPage.tsx
    │   │   ├── TokenPage.tsx
    │   │   ├── TravelAgenciesPage.tsx
    │   │   ├── UnauthorizedPage.tsx
    │   │   ├── UnderConstructionPage.tsx
    │   │   ├── UpgradePage.tsx
    │   │   ├── WellnessPage.tsx
    │   │   ├── index.ts
    │   │   ├── Booking/
    │   │   │   ├── BookingPage.tsx
    │   │   │   └── components/
    │   │   │       ├── BookingCalendar.tsx
    │   │   │       ├── BookingForm.tsx
    │   │   │       └── BookingSummary.tsx
    │   │   ├── Dashboard/
    │   │   │   ├── Analytics.tsx
    │   │   │   ├── Dashboard.tsx
    │   │   │   ├── Home.tsx
    │   │   │   └── components/
    │   │   │       ├── ActivityFeed.tsx
    │   │   │       ├── BookingCard.tsx
    │   │   │       ├── RecentBookings.tsx
    │   │   │       ├── Stats.tsx
    │   │   │       └── UpcomingEvents.tsx
    │   │   ├── Files/
    │   │   │   ├── index.tsx
    │   │   │   └── styles.css
    │   │   ├── Login/
    │   │   │   └── index.tsx
    │   │   ├── MarketplacePage/
    │   │   │   └── index.tsx
    │   │   ├── NftGallery/
    │   │   │   └── index.tsx
    │   │   ├── Private/
    │   │   │   ├── AdminPage.tsx
    │   │   │   ├── DashboardPage.tsx
    │   │   │   ├── Dashboard/
    │   │   │   │   └── DashboardPage.tsx
    │   │   │   └── Profile/
    │   │   │       ├── ProfilePage.tsx
    │   │   │       └── SettingsPage.tsx
    │   │   ├── Profile/
    │   │   │   ├── ProfilePage.tsx
    │   │   │   └── components/
    │   │   │       └── ChangePasswordDialog.tsx
    │   │   ├── Public/
    │   │   │   ├── HomePage.test.tsx
    │   │   │   ├── HomePage.tsx
    │   │   │   ├── LoginPage.tsx
    │   │   │   ├── MainPage.tsx
    │   │   │   ├── PartnersPage.tsx
    │   │   │   ├── RetreatsPage.tsx
    │   │   │   ├── UnauthorizedPage.tsx
    │   │   │   └── Retreats/
    │   │   │       └── DetailsPage.tsx
    │   │   ├── Register/
    │   │   │   └── index.tsx
    │   │   ├── Settings/
    │   │   │   ├── Profile.tsx
    │   │   │   ├── Security.tsx
    │   │   │   ├── SettingsPage.tsx
    │   │   │   └── components/
    │   │   │       ├── AppearanceSettings.tsx
    │   │   │       ├── NotificationsSettings.tsx
    │   │   │       └── SecuritySettings.tsx
    │   │   ├── SettingsPage/
    │   │   │   ├── SettingsPage.tsx
    │   │   │   ├── components/
    │   │   │   │   ├── NotificationsSection.tsx
    │   │   │   │   ├── PreferencesSection.tsx
    │   │   │   │   ├── PrivacySection.tsx
    │   │   │   │   └── SecuritySection.tsx
    │   │   │   └── hooks/
    │   │   │       └── useSettings.ts
    │   │   ├── SocialFeed/
    │   │   │   └── index.tsx
    │   │   ├── admin/
    │   │   │   ├── AdminDashboard.tsx
    │   │   │   ├── Analytics.tsx
    │   │   │   ├── Content.tsx
    │   │   │   ├── Dashboard.tsx
    │   │   │   ├── MonitoringDashboard.tsx
    │   │   │   ├── Roles.tsx
    │   │   │   ├── Settings.tsx
    │   │   │   └── Users.tsx
    │   │   ├── auth/
    │   │   │   ├── ForgotPasswordPage.tsx
    │   │   │   ├── LoginPage.tsx
    │   │   │   ├── RegisterPage.tsx
    │   │   │   └── ResetPasswordPage.tsx
    │   │   ├── dashboards/
    │   │   │   ├── AdminDashboard.tsx
    │   │   │   ├── GroupDashboard.tsx
    │   │   │   ├── MainDashboard.tsx
    │   │   │   ├── PartnerDashboard.tsx
    │   │   │   └── UserDashboard.tsx
    │   │   ├── error/
    │   │   │   └── NotFoundPage.tsx
    │   │   ├── errors/
    │   │   │   ├── Error404Page.tsx
    │   │   │   └── __tests__/
    │   │   │       └── Error404Page.test.tsx
    │   │   ├── financial/
    │   │   │   ├── FinancialReports.tsx
    │   │   │   ├── InvoiceList.tsx
    │   │   │   └── PaymentManager.tsx
    │   │   ├── microservices/
    │   │   │   ├── Analytics.tsx
    │   │   │   ├── AnalyzerDashboard.tsx
    │   │   │   ├── AnalyzerLayout.tsx
    │   │   │   ├── AnalyzerReports.tsx
    │   │   │   ├── BrowseFiles.tsx
    │   │   │   ├── CompareInsurance.tsx
    │   │   │   ├── CompareInsuranceLayout.tsx
    │   │   │   ├── Domains.tsx
    │   │   │   ├── Editor.tsx
    │   │   │   ├── FinancialManagement.tsx
    │   │   │   ├── FinancialManagementLayout.tsx
    │   │   │   ├── Messaging.tsx
    │   │   │   ├── NFT.tsx
    │   │   │   ├── RetreatMatcher.tsx
    │   │   │   ├── RetreatMatcherLayout.tsx
    │   │   │   ├── RetreatStream.tsx
    │   │   │   ├── SecurityService.tsx
    │   │   │   ├── Social.tsx
    │   │   │   ├── SocialVideo.tsx
    │   │   │   ├── StorageLayout.tsx
    │   │   │   ├── TransportBooking.tsx
    │   │   │   ├── Upload.tsx
    │   │   │   ├── VR.tsx
    │   │   │   ├── VRLayout.tsx
    │   │   │   └── WebsiteCreatorLayout.tsx
    │   │   ├── partner/
    │   │   │   ├── AdminDashboardPage.tsx
    │   │   │   ├── CreateRetreatAIPage.tsx
    │   │   │   ├── GetStartedPage.tsx
    │   │   │   └── OnboardingPage.tsx
    │   │   ├── professional/
    │   │   │   ├── Dashboard.tsx
    │   │   │   └── index.tsx
    │   │   └── retreats/
    │   │       ├── types.ts
    │   │       └── components/
    │   │           ├── RetreatCard.tsx
    │   │           └── RetreatFilters.tsx
    │   ├── providers/
    │   │   ├── AppProviders.tsx
    │   │   ├── DatePickerProvider.tsx
    │   │   ├── QueryProvider.tsx
    │   │   └── SecurityProvider.tsx
    │   ├── router/
    │   │   ├── AdminRoutes.tsx
    │   │   └── LazyRoutes.tsx
    │   ├── routes/
    │   │   ├── AppRoutes.tsx
    │   │   ├── FinancialRoutes.tsx
    │   │   ├── PremiumRoute.tsx
    │   │   ├── PrivateRoute.tsx
    │   │   ├── PrivateRoutes.tsx
    │   │   ├── ProtectedRoute.tsx
    │   │   ├── PublicRoutes.tsx
    │   │   ├── RouteMiddleware.tsx
    │   │   ├── VideoRoutes.tsx
    │   │   ├── WebsiteCreatorRoutes.tsx
    │   │   ├── index.tsx
    │   │   └── lazyRoutes.tsx
    │   ├── security/
    │   │   ├── ClientMalwareService.ts
    │   │   ├── DDoSProtectionService.ts
    │   │   ├── EnhancedSecurityService.ts
    │   │   ├── MultiFactorAuthService.ts
    │   │   ├── SecureBackupService.ts
    │   │   ├── ThreatDetectionService.ts
    │   │   └── trusted-types.ts
    │   ├── services/
    │   │   ├── AIAgentService.ts
    │   │   ├── AIRecommendationService.ts
    │   │   ├── ActivityService.ts
    │   │   ├── AdminNotificationService.ts
    │   │   ├── AlertService.ts
    │   │   ├── AnimationService.ts
    │   │   ├── CarRentalService.ts
    │   │   ├── ChatService.ts
    │   │   ├── CryptoService.ts
    │   │   ├── EducationService.ts
    │   │   ├── ErrorService.ts
    │   │   ├── ExpenseSharingService.ts
    │   │   ├── FlightService.ts
    │   │   ├── GPSService.ts
    │   │   ├── HotelBookingService.ts
    │   │   ├── InsuranceService.ts
    │   │   ├── LoyaltyProgramService.ts
    │   │   ├── MarketplaceService.ts
    │   │   ├── MediaService.ts
    │   │   ├── MessagingService.ts
    │   │   ├── MicroserviceConnector.ts
    │   │   ├── MonitoringService.ts
    │   │   ├── ParticipantService.ts
    │   │   ├── PartnerRegistrationService.ts
    │   │   ├── PaymentService.ts
    │   │   ├── PerformanceService.ts
    │   │   ├── PollService.ts
    │   │   ├── ProMatcherService.ts
    │   │   ├── RateLimitService.ts
    │   │   ├── RealtimeNotificationService.ts
    │   │   ├── RetreatManagementService.ts
    │   │   ├── RetreatProMatcherService.ts
    │   │   ├── RetreatStreamService.ts
    │   │   ├── ScheduleService.ts
    │   │   ├── ScheduledReportService.ts
    │   │   ├── SearchTransportService.ts
    │   │   ├── SecurityMonitoringService.ts
    │   │   ├── SecurityService.ts
    │   │   ├── ServiceRegistry.ts
    │   │   ├── SocialService.ts
    │   │   ├── StreamingService.ts
    │   │   ├── TonService.ts
    │   │   ├── TransportBookingService.ts
    │   │   ├── UserProfileService.ts
    │   │   ├── VRService.ts
    │   │   ├── Web3NFTService.ts
    │   │   ├── Web3Service.ts
    │   │   ├── WebRTCService.ts
    │   │   ├── WebsiteCreatorService.ts
    │   │   ├── WorkshopService.ts
    │   │   ├── accessibilityMonitor.ts
    │   │   ├── advancedImageOptimizer.ts
    │   │   ├── analyticsService.ts
    │   │   ├── api.ts
    │   │   ├── apiService.ts
    │   │   ├── auth-header.ts
    │   │   ├── auth.service.ts
    │   │   ├── authService.ts
    │   │   ├── axiosConfig.ts
    │   │   ├── bookingService.ts
    │   │   ├── cacheManager.ts
    │   │   ├── cacheService.ts
    │   │   ├── community.service.ts
    │   │   ├── errorBoundaryService.ts
    │   │   ├── errorManager.ts
    │   │   ├── fileService.ts
    │   │   ├── financialApi.ts
    │   │   ├── formManager.ts
    │   │   ├── gdprService.ts
    │   │   ├── imageOptimizer.ts
    │   │   ├── imageService.ts
    │   │   ├── locationService.ts
    │   │   ├── meta-tags.service.ts
    │   │   ├── navigationService.ts
    │   │   ├── nftService.ts
    │   │   ├── notification.ts
    │   │   ├── notificationService.ts
    │   │   ├── performanceMonitor.ts
    │   │   ├── queryClient.ts
    │   │   ├── resourceMonitor.ts
    │   │   ├── retreat.service.ts
    │   │   ├── routingService.ts
    │   │   ├── search-console.service.ts
    │   │   ├── seo-analytics.service.ts
    │   │   ├── seo.service.ts
    │   │   ├── socialPlatformService.ts
    │   │   ├── socketService.ts
    │   │   ├── stateService.ts
    │   │   ├── storageService.ts
    │   │   ├── syncService.ts
    │   │   ├── themeService.ts
    │   │   ├── tokenService.ts
    │   │   ├── user.service.ts
    │   │   ├── validationService.ts
    │   │   ├── webSocketService.ts
    │   │   ├── wellnessService.ts
    │   │   ├── Activity/
    │   │   │   └── Service.ts
    │   │   ├── CalendarEvent/
    │   │   │   └── service.ts
    │   │   ├── CalendarView/
    │   │   │   └── Service.ts
    │   │   ├── EventReminder/
    │   │   │   └── service.ts
    │   │   ├── Notification/
    │   │   │   └── service.ts
    │   │   ├── RecurringEvent/
    │   │   │   └── Service.ts
    │   │   ├── __mocks__/
    │   │   │   └── TonService.ts
    │   │   ├── __tests__/
    │   │   │   ├── LoginPage.test.tsx
    │   │   │   ├── RegisterPage.test.tsx
    │   │   │   ├── SecuritySettings.test.tsx
    │   │   │   ├── TwoFactorSetup.test.tsx
    │   │   │   ├── Web3Service.test.ts
    │   │   │   ├── authService.test.ts
    │   │   │   └── fileService.test.ts
    │   │   ├── affiliate/
    │   │   │   └── AffiliateService.ts
    │   │   ├── analytics/
    │   │   │   └── AnalyticsService.ts
    │   │   ├── api/
    │   │   │   ├── ApiClient.ts
    │   │   │   ├── config.ts
    │   │   │   └── httpClient.ts
    │   │   ├── core/
    │   │   │   └── BaseService.ts
    │   │   ├── education/
    │   │   │   └── educationService.ts
    │   │   ├── insurance/
    │   │   │   └── insuranceService.ts
    │   │   ├── marketplace/
    │   │   │   └── marketplaceService.ts
    │   │   ├── security/
    │   │   │   └── auth.ts
    │   │   └── template/
    │   │       └── MicroserviceTemplate.ts
    │   ├── shared/
    │   │   ├── index.ts
    │   │   └── components/
    │   │       └── ui/
    │   │           └── LoadingSpinner.tsx
    │   ├── store/
    │   │   ├── aiMatchingStore.ts
    │   │   ├── index.ts
    │   │   ├── messageStore.ts
    │   │   ├── types.ts
    │   │   ├── slices/
    │   │   │   ├── authSlice.ts
    │   │   │   ├── bookingSlice.ts
    │   │   │   ├── calendarSlice.ts
    │   │   │   ├── loyaltySlice.ts
    │   │   │   ├── retreatSlice.ts
    │   │   │   ├── serviceSlice.ts
    │   │   │   ├── socialSlice.ts
    │   │   │   ├── uiSlice.ts
    │   │   │   └── web3Slice.ts
    │   │   └── thunks/
    │   │       └── calendarThunks.ts
    │   ├── styles/
    │   │   ├── accessibility.css
    │   │   ├── affiliate.css
    │   │   ├── animations.css
    │   │   └── theme.ts
    │   ├── test/
    │   │   └── __mocks__/
    │   │       └── fileMock.js
    │   ├── tests/
    │   │   ├── accessibility/
    │   │   │   ├── BookingAccessibility.test.tsx
    │   │   │   ├── CalendarAccessibility.test.tsx
    │   │   │   └── a11y.test.tsx
    │   │   ├── components/
    │   │   │   └── BookingForm.test.tsx
    │   │   └── integration/
    │   │       ├── BookingFlow.test.tsx
    │   │       └── CalendarFlow.test.tsx
    │   ├── theme/
    │   │   └── index.ts
    │   ├── types/
    │   │   ├── CarRental.ts
    │   │   ├── activities.ts
    │   │   ├── affiliate.ts
    │   │   ├── analytics.ts
    │   │   ├── api.ts
    │   │   ├── apiErrors.ts
    │   │   ├── auth.ts
    │   │   ├── auth.types.ts
    │   │   ├── booking.ts
    │   │   ├── calendar.ts
    │   │   ├── community.ts
    │   │   ├── education.d.ts
    │   │   ├── education.ts
    │   │   ├── ethereum.d.ts
    │   │   ├── expense.ts
    │   │   ├── flight.ts
    │   │   ├── gdpr.ts
    │   │   ├── hotel.ts
    │   │   ├── index.ts
    │   │   ├── insurance.ts
    │   │   ├── logger.ts
    │   │   ├── marketplace.ts
    │   │   ├── messaging.ts
    │   │   ├── monitoring.ts
    │   │   ├── routing.ts
    │   │   ├── user.ts
    │   │   ├── web3.d.ts
    │   │   ├── web3.ts
    │   │   └── window.d.ts
    │   └── utils/
    │       ├── AppProviders.tsx
    │       ├── Web3Provider.tsx
    │       ├── accessibility.ts
    │       ├── aiMatching.ts
    │       ├── aiMatchingStore.ts
    │       ├── analytics.ts
    │       ├── auth-header.ts
    │       ├── auth.service.ts
    │       ├── axiosConfig.ts
    │       ├── chatbot.ts
    │       ├── chatbotStore.ts
    │       ├── community.service.ts
    │       ├── config.ts
    │       ├── dateTime.ts
    │       ├── education.ts
    │       ├── educationStore.ts
    │       ├── errorBoundary.tsx
    │       ├── errorHandler.ts
    │       ├── featureFlags.ts
    │       ├── formatters.ts
    │       ├── group.ts
    │       ├── groupStore.ts
    │       ├── imageOptimization.ts
    │       ├── insurance.ts
    │       ├── invoice.ts
    │       ├── invoiceGenerator.ts
    │       ├── jsonld.ts
    │       ├── jwt.ts
    │       ├── logger.ts
    │       ├── loyalty.ts
    │       ├── loyaltyStore.ts
    │       ├── marketplace.ts
    │       ├── matching.ts
    │       ├── message.ts
    │       ├── messageStore.ts
    │       ├── nft.ts
    │       ├── nftService.ts
    │       ├── nftStore.ts
    │       ├── notification.ts
    │       ├── notificationStore.ts
    │       ├── oerformance.ts
    │       ├── openai.ts
    │       ├── partner.ts
    │       ├── partnerStats.ts
    │       ├── performance.ts
    │       ├── retreat.service.ts
    │       ├── roles.ts
    │       ├── routeLogger.ts
    │       ├── rtl.ts
    │       ├── security.ts
    │       ├── securityAudit.ts
    │       ├── securityMiddleware.ts
    │       ├── sentry.ts
    │       ├── sitemap.ts
    │       ├── social.ts
    │       ├── socialStore.ts
    │       ├── socket.ts
    │       ├── test-utils.tsx
    │       ├── timeUtils.ts
    │       ├── user.service.ts
    │       ├── validationSchemas.ts
    │       ├── web3Storage.ts
    │       ├── insurance/
    │       │   ├── insuranceService.ts
    │       │   └── providers/
    │       │       ├── aigAPI.ts
    │       │       └── allianzAPI.ts
    │       ├── locales/
    │       │   ├── en.json
    │       │   └── fr.json
    │       └── security/
    │           ├── auditLog.ts
    │           ├── auth.ts
    │           ├── authorization.ts
    │           ├── bruteForceProtection.ts
    │           ├── cryptography.ts
    │           ├── deviceFingerprint.ts
    │           ├── encryption.ts
    │           ├── inactivityManager.ts
    │           ├── mfa.ts
    │           ├── passwordPolicy.ts
    │           ├── sanitization.ts
    │           ├── secureStorage.ts
    │           ├── securityHeaders.ts
    │           └── sessionManager.ts
    ├── .husky/
    │   └── pre-commit
    └── .storybook/
        ├── main.ts
        └── preview.tsx