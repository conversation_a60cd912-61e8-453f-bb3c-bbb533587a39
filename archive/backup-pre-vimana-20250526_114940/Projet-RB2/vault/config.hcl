storage "file" {
  path = "/vault/file"
}

listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = 1
}

api_addr = "http://0.0.0.0:8200"
cluster_addr = "https://0.0.0.0:8201"

ui = true

# Enable auto-unsealing with AWS KMS
# seal "awskms" {
#   region = "us-west-2"
#   kms_key_id = "your-kms-key-id"
# }

# Enable audit logging
audit {
  type = "file"
  path = "/vault/logs/audit.log"
  description = "Vault audit log"
}

# Enable the KV secrets engine
path "secret/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Enable token authentication
auth "token" {
  description = "Token-based authentication"
}

# Configure policies
path "auth/token/lookup-self" {
  capabilities = ["read"]
}

path "auth/token/renew-self" {
  capabilities = ["update"]
}

path "auth/token/revoke-self" {
  capabilities = ["update"]
}

# Configure telemetry for monitoring
telemetry {
  statsite_address = "statsite:8125"
  disable_hostname = true
}

max_lease_ttl = "768h"
default_lease_ttl = "768h"
