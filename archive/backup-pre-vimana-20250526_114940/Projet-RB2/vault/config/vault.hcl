# Main Vault server configuration

storage "file" {
  path = "/vault/data"
}

listener "tcp" {
  address = "0.0.0.0:8200"
  tls_disable = 1  # Enable TLS in production
}

api_addr = "http://0.0.0.0:8200"

# Enable audit logging
audit {
  type = "file"
  path = "stdout"
}

# UI configuration
ui = true

# Enable the KV secrets engine
path "secret/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Configure automatic key rotation
seal "transit" {
  disable_renewal = false
  key_rotation_period = "168h"  # 7 days rotation
}

# MFA Configuration
mfa "totp" {
  mount_accessor = "auth_userpass_e6c27bd1"
  namespace = "ns1"
  name = "my_totp"
  issuer = "Project-Final"
  period = 30
  key_size = 20
  qr_size = 200
  algorithm = "SHA1"
  digits = 6
}