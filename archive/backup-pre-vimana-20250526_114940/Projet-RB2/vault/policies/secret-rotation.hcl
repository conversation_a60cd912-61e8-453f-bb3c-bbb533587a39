# Enable secret rotation for database credentials
path "database/creds/*" {
  capabilities = ["read"]
  min_lease_ttl = "1h"
  max_lease_ttl = "24h"
}

# Enable secret rotation for service accounts
path "auth/kubernetes/role/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Enable secret rotation for API keys
path "secret/data/api-keys/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
  min_lease_ttl = "1h"
  max_lease_ttl = "720h"
}

# Enable audit logging
path "sys/audit/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Enable monitoring
path "sys/metrics" {
  capabilities = ["read"]
}