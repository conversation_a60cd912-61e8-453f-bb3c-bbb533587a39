/**
 * Lighthouse configuration file
 *
 * This file configures Lighthouse CI for performance testing.
 * It sets thresholds for various performance metrics and configures
 * the audit settings.
 */

module.exports = {
  ci: {
    collect: {
      // Number of samples to collect
      numberOfRuns: 3,
      // URLs to test
      url: [
        'http://localhost:3000/',
        'http://localhost:3000/retreats',
        'http://localhost:3000/retreats/search',
        'http://localhost:3000/admin/retreats',
        'http://localhost:3000/admin/dashboard',
        'http://localhost:3000/admin/users',
        'http://localhost:3000/settings',
        'http://localhost:3000/profile',
        'http://localhost:3000/auth/login',
        'http://localhost:3000/partner/dashboard'
      ],
      // Settings for headless Chrome
      settings: {
        // Tester à la fois en mode desktop et mobile
        formFactor: process.env.DEVICE_TYPE || 'desktop',
        screenEmulation: process.env.DEVICE_TYPE === 'mobile' ? {
          mobile: true,
          width: 375,
          height: 667,
          deviceScaleFactor: 2,
          disabled: false,
        } : {
          mobile: false,
          width: 1350,
          height: 940,
          deviceScaleFactor: 1,
          disabled: false,
        },
        throttling: {
          // Simulated throttling settings - plus réalistes pour les tests
          rttMs: process.env.DEVICE_TYPE === 'mobile' ? 150 : 40,
          throughputKbps: process.env.DEVICE_TYPE === 'mobile' ? 1600 : 10240,
          cpuSlowdownMultiplier: process.env.DEVICE_TYPE === 'mobile' ? 4 : 1,
          requestLatencyMs: process.env.DEVICE_TYPE === 'mobile' ? 150 : 0,
          downloadThroughputKbps: process.env.DEVICE_TYPE === 'mobile' ? 1600 : 0,
          uploadThroughputKbps: process.env.DEVICE_TYPE === 'mobile' ? 750 : 0
        },
        // Skip audits that require JavaScript or might be flaky in CI
        skipAudits: ['uses-http2', 'uses-long-cache-ttl', 'canonical'],
        // Set a longer timeout for slow CI environments
        maxWaitForLoad: 60000,
        // Authentification pour les pages protégées
        extraHeaders: {
          // Ajouter des cookies d'authentification si nécessaire
          Cookie: process.env.AUTH_COOKIE || ''
        }
      }
    },
    // Assert against thresholds
    assert: {
      // Performance score thresholds
      preset: 'lighthouse:recommended',
      assertions: {
        // Performance metrics - ajustés selon le type d'appareil
        'categories:performance': ['error', {
          minScore: process.env.DEVICE_TYPE === 'mobile' ? 0.6 : 0.7
        }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.9 }],

        // Specific metrics - ajustés selon le type d'appareil
        'first-contentful-paint': ['error', {
          maxNumericValue: process.env.DEVICE_TYPE === 'mobile' ? 3000 : 2000
        }],
        'largest-contentful-paint': ['error', {
          maxNumericValue: process.env.DEVICE_TYPE === 'mobile' ? 4000 : 2500
        }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['error', {
          maxNumericValue: process.env.DEVICE_TYPE === 'mobile' ? 500 : 300
        }],
        'speed-index': ['error', {
          maxNumericValue: process.env.DEVICE_TYPE === 'mobile' ? 5000 : 3500
        }],
        'interactive': ['error', {
          maxNumericValue: process.env.DEVICE_TYPE === 'mobile' ? 6000 : 3500
        }],

        // Resource assertions - ajustées pour être plus réalistes
        'resource-summary:script:size': ['warn', { maxNumericValue: 500000 }],
        'resource-summary:stylesheet:size': ['warn', { maxNumericValue: 150000 }],
        'resource-summary:image:size': ['warn', { maxNumericValue: 300000 }],
        'resource-summary:font:size': ['warn', { maxNumericValue: 100000 }],
        'resource-summary:third-party:size': ['warn', { maxNumericValue: 200000 }],

        // Accessibilité spécifique
        'aria-required-attr': ['error', { minScore: 1 }],
        'aria-valid-attr': ['error', { minScore: 1 }],
        'button-name': ['error', { minScore: 1 }],
        'color-contrast': ['error', { minScore: 1 }],
        'document-title': ['error', { minScore: 1 }],
        'html-has-lang': ['error', { minScore: 1 }],
        'image-alt': ['error', { minScore: 1 }],
        'label': ['error', { minScore: 1 }],
        'link-name': ['error', { minScore: 1 }],
        'meta-viewport': ['error', { minScore: 1 }],

        // Best practices
        'uses-responsive-images': ['warn', { minScore: 0.9 }],
        'uses-optimized-images': ['warn', { minScore: 0.9 }],
        'uses-text-compression': ['error', { minScore: 1 }],
        'uses-rel-preconnect': ['warn', { minScore: 0.9 }],

        // SEO spécifique
        'meta-description': ['warn', { minScore: 1 }],
        'robots-txt': ['warn', { minScore: 1 }],

        // Disable some audits that might be flaky in CI
        'uses-http2': 'off',
        'uses-long-cache-ttl': 'off',
        'canonical': 'off',
        'maskable-icon': 'off',
        'service-worker': 'off',
        'offline-start-url': 'off'
      }
    },
    // Upload results to temporary storage for CI or local filesystem for development
    upload: {
      target: process.env.CI ? 'temporary-public-storage' : 'filesystem',
      outputDir: './lighthouse-results',
      reportFilenamePattern: '%%PATHNAME%%-%%DATETIME%%-report.%%EXTENSION%%'
    }
  }
};
