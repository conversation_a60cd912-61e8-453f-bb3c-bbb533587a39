{
  "compilerOptions": {
    "target": "es2022",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "react-jsx",
    "incremental": true,
    "baseUrl": ".",

    /* Vérifications supplémentaires pour une meilleure qualité de code */
    "noImplicitAny": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "strictBindCallApply": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictPropertyInitialization": true,
    "useUnknownInCatchVariables": true,
    "alwaysStrict": true,

    /* Pour les imports/exports */
    "allowSyntheticDefaultImports": true,

    /* Autres options utiles */
    "declaration": true,                  /* Génère les fichiers .d.ts pour les packages partageables */
    "declarationMap": true,               /* Génère les sourcemaps pour les fichiers .d.ts */
    "sourceMap": true,                    /* Génère les sourcemaps pour les fichiers compilés */
    "removeComments": false,              /* Conserve les commentaires pendant la compilation */

    "paths": {
      "@projet-rb2/core/*": ["packages/core/src/*"],
      "@projet-rb2/ui/*": ["packages/ui/src/*"],
      "@projet-rb2/api/*": ["packages/api/src/*"],
      "@projet-rb2/state/*": ["packages/state/src/*"],
      "@projet-rb2/web/*": ["packages/web/src/*"],
      "@projet-rb2/mobile/*": ["packages/mobile/src/*"],
      "@projet-rb2/desktop/*": ["packages/desktop/src/*"]
    }
  },
  "exclude": ["node_modules", "dist", "build", "**/*.bak", "**/*.test.ts", "**/*.test.tsx"],
  "watchOptions": {
    "watchFile": "useFsEvents",
    "watchDirectory": "useFsEvents",
    "fallbackPolling": "dynamicPriority",
    "excludeDirectories": ["**/node_modules", "**/dist", "**/build"]
  }
} 