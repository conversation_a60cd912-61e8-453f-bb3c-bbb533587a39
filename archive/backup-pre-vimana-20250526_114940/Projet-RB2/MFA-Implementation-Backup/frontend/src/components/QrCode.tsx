import React from 'react';
import { Spin, Image } from 'antd';

interface QrCodeProps {
  url: string;
  size?: number;
}

/**
 * QR Code component for MFA setup
 */
export const QrCode: React.FC<QrCodeProps> = ({ url, size = 200 }) => {
  const [loading, setLoading] = React.useState(true);

  if (!url) {
    return (
      <div style={{ width: size, height: size, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Spin tip="Loading..." />
      </div>
    );
  }

  return (
    <div style={{ position: 'relative', margin: '0 auto', width: size, height: size }}>
      {loading && (
        <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin tip="Loading QR code..." />
        </div>
      )}
      <Image
        src={url}
        alt="QR Code for authenticator app"
        width={size}
        height={size}
        style={{ objectFit: 'contain', display: loading ? 'none' : 'block' }}
        onLoad={() => setLoading(false)}
        preview={false}
      />
    </div>
  );
}; 