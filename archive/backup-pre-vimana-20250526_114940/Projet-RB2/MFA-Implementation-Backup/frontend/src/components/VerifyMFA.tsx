import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useMFA } from '../../hooks/useMFA';
import { Button, Card, Form, Input, Typography, Alert, Spin, Space, Divider, Tag } from 'antd';
import { MFAService } from '../../services/auth/mfa.service';

const { Title, Paragraph, Text } = Typography;

const VerifyMFA: React.FC = () => {
  const [verificationCode, setVerificationCode] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [mockState, setMockState] = useState<any>(null);
  const { token, mfaRequired, mfaVerified, isAuthenticated } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { verifyMFAAndNavigate, verifyState } = useMFA();
  const mfaService = MFAService.getInstance();

  // Get the intended redirect path after successful verification
  const redirectPath = location.state?.from?.pathname || '/dashboard';

  useEffect(() => {
    // Check if user is authenticated but MFA is not verified
    if (isAuthenticated && !mfaVerified && !mfaRequired) {
      navigate(redirectPath, { replace: true });
    }
    
    // Get the mock state for testing
    if (mfaService) {
      setMockState(mfaService.getMockState());
    }
  }, [isAuthenticated, mfaVerified, mfaRequired, navigate, redirectPath]);

  const handleVerify = async () => {
    if (!verificationCode) {
      setError('Please enter the verification code');
      return;
    }

    if (!token) {
      setError('Authentication token is missing');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      await verifyMFAAndNavigate(verificationCode, redirectPath);
      
      // If we reach here, that means verification failed
      if (verifyState.error) {
        setError(verifyState.error);
      }
    } catch (err: any) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Error verifying 2FA code:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card style={{ maxWidth: '500px', margin: '0 auto' }}>
      <Title level={3}>Two-Factor Authentication</Title>
      <Paragraph>
        Please enter the 6-digit verification code from your authenticator app to complete login.
      </Paragraph>

      {error && <Alert message={error} type="error" style={{ marginBottom: '16px' }} />}

      <Form layout="vertical">
        <Form.Item label="Verification Code" required>
          <Input
            placeholder="Enter 6-digit code"
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            maxLength={6}
            style={{ width: '100%' }}
            autoFocus
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" onClick={handleVerify} loading={loading} block>
            Verify
          </Button>
        </Form.Item>
      </Form>
      
      {mfaService.getMockState && (
        <>
          <Divider>Testing Info</Divider>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>Valid test codes:</Text>
            <div>
              {mockState?.validTestCodes?.map((code: string) => (
                <Tag key={code} color="blue" style={{ marginBottom: 8 }}>{code}</Tag>
              ))}
            </div>
            <Paragraph type="secondary" style={{ fontSize: 12 }}>
              For testing purposes, only these codes will work for verification.
              You can add more codes from the test page.
            </Paragraph>
          </Space>
        </>
      )}
      
      <Paragraph style={{ marginTop: '16px', fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)' }}>
        If you're having trouble accessing your account, please contact support.
      </Paragraph>
    </Card>
  );
};

export default VerifyMFA; 