import React from 'react';
import { Typo<PERSON>, <PERSON>, <PERSON>ati<PERSON>, Row, Col, Button, Alert } from 'antd';
import { useAuth } from '../hooks/useAuth';
import { SafetyOutlined, LockOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';

const { Title, Paragraph } = Typography;

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  
  return (
    <div>
      <Title level={2}>Welcome to Your Dashboard</Title>
      <Paragraph>
        This is a secure area protected by authentication. 
        {user?.twoFactorEnabled ? (
          ' Your account is secured with two-factor authentication.'
        ) : (
          ' Consider enabling two-factor authentication for increased security.'
        )}
      </Paragraph>
      
      {!user?.twoFactorEnabled && (
        <Alert
          message="Security Recommendation"
          description="Enable two-factor authentication to make your account more secure."
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
          action={
            <Link to="/setup-mfa">
              <Button size="small" type="primary">
                Setup 2FA
              </Button>
            </Link>
          }
        />
      )}
      
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} md={8}>
          <Card>
            <Statistic
              title="Authentication Status"
              value="Authenticated"
              valueStyle={{ color: '#3f8600' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8}>
          <Card>
            <Statistic
              title="2FA Status"
              value={user?.twoFactorEnabled ? 'Enabled' : 'Disabled'}
              valueStyle={{ color: user?.twoFactorEnabled ? '#3f8600' : '#cf1322' }}
              prefix={<SafetyOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8}>
          <Card>
            <Statistic
              title="Security Level"
              value={user?.twoFactorEnabled ? 'High' : 'Basic'}
              valueStyle={{ color: user?.twoFactorEnabled ? '#3f8600' : '#faad14' }}
              prefix={<LockOutlined />}
            />
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        <Col xs={24}>
          <Card title="MFA Testing Tools">
            <Paragraph>
              Use our comprehensive testing tools to test and verify the MFA implementation.
            </Paragraph>
            
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Link to="/mfa-test">
                  <Button type="primary" icon={<SafetyOutlined />} block>
                    MFA Test Component
                  </Button>
                </Link>
              </Col>
              
              <Col xs={24} sm={12} md={8}>
                <Link to="/setup-mfa">
                  <Button type="default" icon={<SafetyOutlined />} block>
                    Setup MFA
                  </Button>
                </Link>
              </Col>
              
              <Col xs={24} sm={12} md={8}>
                <Link to="/test-login">
                  <Button type="default" icon={<LockOutlined />} block>
                    Test Login
                  </Button>
                </Link>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage; 