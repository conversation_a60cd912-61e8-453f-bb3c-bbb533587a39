import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from '../context/AuthContext';
import { ProtectedRoute } from './ProtectedRoute';
import AppLayout from '../layouts/AppLayout';
import LoginPage from '../pages/LoginPage';
import RegisterPage from '../pages/RegisterPage';
import DashboardPage from '../pages/DashboardPage';
import SettingsPage from '../pages/SettingsPage';
import NotFoundPage from '../pages/NotFoundPage';
import SetupMFA from '../components/auth/SetupMFA';
import VerifyMFA from '../components/auth/VerifyMFA';
import TestPage from '../pages/TestPage';
import TestLoginPage from '../pages/TestLoginPage';
import MFATestComponent from '../components/auth/MFATestComponent';

const Router: React.FC = () => {
  return (
    <BrowserRouter>
      <AuthProvider>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="/verify-mfa" element={<VerifyMFA />} />
          
          {/* Testing routes */}
          <Route path="/test" element={<TestPage />} />
          <Route path="/test-login" element={<TestLoginPage />} />
          <Route path="/mfa-test" element={<MFATestComponent />} />
          
          {/* Protected routes */}
          <Route element={<ProtectedRoute />}>
            <Route element={<AppLayout />}>
              <Route path="/dashboard" element={<DashboardPage />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/setup-mfa" element={<SetupMFA />} />
            </Route>
          </Route>
          
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </AuthProvider>
    </BrowserRouter>
  );
};

export default Router;