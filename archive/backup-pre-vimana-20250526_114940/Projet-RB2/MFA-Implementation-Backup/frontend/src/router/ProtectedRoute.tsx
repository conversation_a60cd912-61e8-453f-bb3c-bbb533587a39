import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

export const ProtectedRoute: React.FC = () => {
  const { isAuthenticated, mfaRequired, mfaVerified } = useAuth();
  const location = useLocation();

  // If user is not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/test-login" state={{ from: location }} replace />;
  }

  // If MFA is required but not verified, redirect to MFA verification
  if (mfaRequired && !mfaVerified) {
    return <Navigate to="/verify-mfa" state={{ from: location }} replace />;
  }

  // User is authenticated and M<PERSON> is verified (if required)
  return <Outlet />;
}; 