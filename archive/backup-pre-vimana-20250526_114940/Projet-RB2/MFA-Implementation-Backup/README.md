# Two-Factor Authentication (2FA/MFA) Implementation

A complete implementation of Two-Factor Authentication (2FA/MFA) for web applications, with comprehensive testing tools and components.

## Features

- **Complete MFA Implementation**: Setup, verification, and disabling of MFA using TOTP (Time-based One-Time Password)
- **Mock Service**: Fully functional mock service for testing without a backend
- **Testing Components**: Dedicated test pages and components for verifying MFA functionality
- **React Hooks**: Custom hooks for MFA operations
- **Visual Testing**: QR code display and verification code input

## Components

### Core MFA Components

- **SetupMFA**: Guides users through the setup process with QR code display
- **VerifyMFA**: Allows users to verify their MFA code during login
- **QrCode**: Displays QR codes for MFA setup

### Testing Components

- **MFATestComponent**: All-in-one testing component for MFA functionality
- **MockLoginForm**: Simulates login with MFA toggle for testing
- **TestLoginPage**: Dedicated page for testing the MFA login flow
- **TestPage**: General testing page with MFA state management

## Services

- **MFAService**: Handles all MFA operations with mock implementation for testing

## Hooks

- **useMFA**: Custom hook providing MFA functionality for React components
- **useAuth**: Auth context hook with MFA support

## Usage

### Setting Up MFA

```jsx
import { useMFA } from '../hooks/useMFA';
import { SetupMFA } from '../components/SetupMFA';

// In your component
const { setupMFA, enableMFA } = useMFA();

// Generate QR code
await setupMFA();

// Enable MFA with verification code
await enableMFA('123456');
```

### Verifying MFA During Login

```jsx
import { useMFA } from '../hooks/useMFA';
import { VerifyMFA } from '../components/VerifyMFA';

// In your component
const { verifyMFA } = useMFA();

// Verify code
await verifyMFA('123456');
```

### Testing MFA

1. Use `/test-login` page to create mock users with MFA enabled
2. Enter verification codes from the list of valid test codes
3. Test the full MFA flow including setup, verification, and disabling

## Implementation Details

The implementation uses Time-based One-Time Password (TOTP) protocol and follows security best practices:

- QR code generation for easy setup
- Secure storage of MFA secrets
- Proper session handling with MFA verification
- Validation of verification codes

## License

MIT 