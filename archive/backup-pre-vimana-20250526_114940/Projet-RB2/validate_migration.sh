#!/bin/bash
# Script de validation de la migration

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Validation de la migration src vers superagent ===${NC}"

# Vérifier que le dossier superagent existe
if [ ! -d "superagent" ]; then
  echo -e "${RED}Le dossier superagent n'existe pas. Impossible de continuer.${NC}"
  exit 1
fi

# Créer un répertoire pour les résultats de validation
mkdir -p validation_results

# Étape 1: Vérification de la structure des dossiers
echo -e "\n${YELLOW}Étape 1: Vérification de la structure des dossiers...${NC}"

# Liste des dossiers essentiels qui doivent exister
ESSENTIAL_DIRS=(
  "superagent/agents"
  "superagent/agents/nodes"
  "superagent/graph"
  "superagent/api"
  "superagent/config"
  "superagent/utils"
  "superagent/workflows"
)

MISSING_DIRS=0
for dir in "${ESSENTIAL_DIRS[@]}"; do
  if [ ! -d "$dir" ]; then
    echo -e "${RED}Dossier manquant: $dir${NC}"
    MISSING_DIRS=$((MISSING_DIRS+1))
  else
    echo -e "${GREEN}Dossier présent: $dir${NC}"
  fi
done

if [ $MISSING_DIRS -eq 0 ]; then
  echo -e "${GREEN}Tous les dossiers essentiels sont présents.${NC}"
else
  echo -e "${RED}$MISSING_DIRS dossiers essentiels sont manquants.${NC}"
fi

# Étape 2: Vérification des fichiers clés
echo -e "\n${YELLOW}Étape 2: Vérification des fichiers clés...${NC}"

# Liste des fichiers clés qui doivent exister
KEY_FILES=(
  "superagent/agents/nodes/browser_node.py"
  "superagent/agents/nodes/coder_node.py"
  "superagent/agents/nodes/coordinator_node.py"
  "superagent/graph/workflow.py"
  "superagent/graph/types.py"
  "superagent/config/settings.py"
)

MISSING_FILES=0
for file in "${KEY_FILES[@]}"; do
  if [ ! -f "$file" ]; then
    echo -e "${RED}Fichier manquant: $file${NC}"
    MISSING_FILES=$((MISSING_FILES+1))
  else
    echo -e "${GREEN}Fichier présent: $file${NC}"
  fi
done

if [ $MISSING_FILES -eq 0 ]; then
  echo -e "${GREEN}Tous les fichiers clés sont présents.${NC}"
else
  echo -e "${RED}$MISSING_FILES fichiers clés sont manquants.${NC}"
fi

# Étape 3: Vérification des chemins d'importation
echo -e "\n${YELLOW}Étape 3: Vérification des chemins d'importation...${NC}"

# Rechercher les importations qui font encore référence à 'src'
SRC_IMPORTS=$(grep -r "from src" superagent --include="*.py" | wc -l)
SRC_IMPORTS_DIRECT=$(grep -r "import src" superagent --include="*.py" | wc -l)
TOTAL_SRC_IMPORTS=$((SRC_IMPORTS + SRC_IMPORTS_DIRECT))

if [ $TOTAL_SRC_IMPORTS -eq 0 ]; then
  echo -e "${GREEN}Aucune référence à 'src' trouvée dans les importations.${NC}"
else
  echo -e "${RED}$TOTAL_SRC_IMPORTS références à 'src' trouvées dans les importations.${NC}"
  echo -e "${YELLOW}Exemples:${NC}"
  grep -r "from src" superagent --include="*.py" | head -3
  grep -r "import src" superagent --include="*.py" | head -3
fi

# Étape 4: Vérification des erreurs d'importation
echo -e "\n${YELLOW}Étape 4: Vérification des erreurs d'importation potentielles...${NC}"

# Rechercher les motifs qui pourraient indiquer des erreurs d'importation
IMPORT_ERRORS=$(grep -r "ImportError" superagent --include="*.py" | wc -l)

if [ $IMPORT_ERRORS -eq 0 ]; then
  echo -e "${GREEN}Aucune erreur d'importation potentielle détectée.${NC}"
else
  echo -e "${YELLOW}$IMPORT_ERRORS erreurs d'importation potentielles détectées.${NC}"
  echo -e "${YELLOW}Note: Certaines peuvent être des vérifications de dépendances légitimes.${NC}"
  echo -e "${YELLOW}Exemples:${NC}"
  grep -r "ImportError" superagent --include="*.py" | head -3
fi

# Étape 5: Exécution de tests simples
echo -e "\n${YELLOW}Étape 5: Exécution de tests simples...${NC}"

# Créer un script de test simple
cat > test_imports.py << EOF
#!/usr/bin/env python3
"""
Test simple pour vérifier que les importations fonctionnent correctement.
"""
import sys
import os

# Ajouter le répertoire parent au chemin de recherche
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    print("Tentative d'importation des modules superagent...")
    
    # Essayer d'importer quelques modules clés
    from superagent.graph.types import State
    print("✅ Importation de superagent.graph.types réussie")
    
    from superagent.config import settings
    print("✅ Importation de superagent.config.settings réussie")
    
    # Essayer d'importer un module d'agent si disponible
    try:
        from superagent.agents.nodes import coordinator_node
        print("✅ Importation de superagent.agents.nodes.coordinator_node réussie")
    except ImportError as e:
        print(f"❌ Erreur lors de l'importation de superagent.agents.nodes.coordinator_node: {e}")
    
    print("Tests d'importation terminés avec succès.")
    sys.exit(0)
except ImportError as e:
    print(f"❌ Erreur d'importation: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Erreur inattendue: {e}")
    sys.exit(2)
EOF

chmod +x test_imports.py

# Exécuter le test d'importation
echo -e "${YELLOW}Exécution du test d'importation...${NC}"
python3 test_imports.py > validation_results/import_test.log 2>&1
IMPORT_TEST_RESULT=$?

if [ $IMPORT_TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Test d'importation réussi.${NC}"
  cat validation_results/import_test.log
else
  echo -e "${RED}Test d'importation échoué.${NC}"
  cat validation_results/import_test.log
fi

# Créer un rapport de validation
echo -e "\n${YELLOW}Création du rapport de validation...${NC}"

cat > validation_results/validation_report.md << EOF
# Rapport de Validation de Migration

## Résumé

- **Structure des dossiers**: ${MISSING_DIRS} dossiers manquants sur ${#ESSENTIAL_DIRS[@]} vérifiés
- **Fichiers clés**: ${MISSING_FILES} fichiers manquants sur ${#KEY_FILES[@]} vérifiés
- **Chemins d'importation**: ${TOTAL_SRC_IMPORTS} références à 'src' trouvées
- **Erreurs d'importation potentielles**: ${IMPORT_ERRORS} détectées
- **Test d'importation**: $([ $IMPORT_TEST_RESULT -eq 0 ] && echo "Réussi" || echo "Échoué")

## Détails

### Structure des dossiers
$(for dir in "${ESSENTIAL_DIRS[@]}"; do
  if [ -d "$dir" ]; then
    echo "- ✅ $dir"
  else
    echo "- ❌ $dir"
  fi
done)

### Fichiers clés
$(for file in "${KEY_FILES[@]}"; do
  if [ -f "$file" ]; then
    echo "- ✅ $file"
  else
    echo "- ❌ $file"
  fi
done)

### Chemins d'importation
$(if [ $TOTAL_SRC_IMPORTS -eq 0 ]; then
  echo "✅ Aucune référence à 'src' trouvée dans les importations."
else
  echo "❌ $TOTAL_SRC_IMPORTS références à 'src' trouvées dans les importations."
  echo "\`\`\`"
  grep -r "from src" superagent --include="*.py" | head -5
  grep -r "import src" superagent --include="*.py" | head -5
  echo "\`\`\`"
fi)

### Erreurs d'importation potentielles
$(if [ $IMPORT_ERRORS -eq 0 ]; then
  echo "✅ Aucune erreur d'importation potentielle détectée."
else
  echo "⚠️ $IMPORT_ERRORS erreurs d'importation potentielles détectées."
  echo "\`\`\`"
  grep -r "ImportError" superagent --include="*.py" | head -5
  echo "\`\`\`"
fi)

### Test d'importation
\`\`\`
$(cat validation_results/import_test.log)
\`\`\`

## Conclusion

$(if [ $MISSING_DIRS -eq 0 ] && [ $MISSING_FILES -eq 0 ] && [ $TOTAL_SRC_IMPORTS -eq 0 ] && [ $IMPORT_TEST_RESULT -eq 0 ]; then
  echo "✅ La migration a été validée avec succès. Tous les tests ont passé."
  echo ""
  echo "Recommandation: Le dossier \`src\` peut être supprimé en toute sécurité."
else
  echo "❌ La migration n'a pas été validée complètement. Certains tests ont échoué."
  echo ""
  echo "Recommandation: Corriger les problèmes identifiés avant de supprimer le dossier \`src\`."
fi)

---

*Rapport généré le: $(date)*
EOF

echo -e "${GREEN}Rapport de validation créé: validation_results/validation_report.md${NC}"

# Afficher la conclusion
if [ $MISSING_DIRS -eq 0 ] && [ $MISSING_FILES -eq 0 ] && [ $TOTAL_SRC_IMPORTS -eq 0 ] && [ $IMPORT_TEST_RESULT -eq 0 ]; then
  echo -e "\n${GREEN}La migration a été validée avec succès. Tous les tests ont passé.${NC}"
  echo -e "${GREEN}Le dossier 'src' peut être supprimé en toute sécurité.${NC}"
else
  echo -e "\n${RED}La migration n'a pas été validée complètement. Certains tests ont échoué.${NC}"
  echo -e "${YELLOW}Veuillez corriger les problèmes identifiés avant de supprimer le dossier 'src'.${NC}"
fi

echo -e "\n${BLUE}=== Validation terminée ===${NC}"
