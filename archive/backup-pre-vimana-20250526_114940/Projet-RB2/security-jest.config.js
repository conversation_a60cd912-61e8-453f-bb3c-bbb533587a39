module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/security/**/*.test.ts', '**/security/**/*.test.tsx'],
  modulePathIgnorePatterns: [
    '<rootDir>/backup/',
    '<rootDir>/node_modules/'
  ],
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': '<rootDir>/frontend/src/test/__mocks__/styleMock.js',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/frontend/src/test/__mocks__/fileMock.js',
    '^@/(.*)$': '<rootDir>/frontend/src/$1'
  },
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.json'
    }]
  },
  transformIgnorePatterns: [
    '/node_modules/(?!react-router-dom)/'
  ],
  haste: {
    hasteImplModulePath: null
  }
};
