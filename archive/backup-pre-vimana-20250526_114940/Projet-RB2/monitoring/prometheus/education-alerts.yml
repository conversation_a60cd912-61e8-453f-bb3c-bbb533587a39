groups:
- name: education-service
  rules:
  - alert: HighErrorRate
    expr: rate(education_http_errors_total[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
      service: education
    annotations:
      summary: "High error rate detected in Education Service"
      description: "Error rate is above 10% for 5 minutes"

  - alert: HighLatency
    expr: histogram_quantile(0.95, rate(education_request_duration_seconds_bucket[5m])) > 0.5
    for: 5m
    labels:
      severity: warning
      service: education
    annotations:
      summary: "High latency in Education Service"
      description: "95th percentile latency is above 500ms"

  - alert: HighMemoryUsage
    expr: container_memory_usage_bytes{container='education-service'} > 1.5 * 1024 * 1024 * 1024
    for: 15m
    labels:
      severity: warning
      service: education
    annotations:
      summary: "High memory usage in Education Service"
      description: "Memory usage above 1.5GB for 15 minutes"

  - alert: HighCPUUsage
    expr: rate(container_cpu_usage_seconds_total{container='education-service'}[5m]) > 0.8
    for: 10m
    labels:
      severity: warning
      service: education
    annotations:
      summary: "High CPU usage in Education Service"
      description: "CPU usage above 80% for 10 minutes"

  - alert: PodRestarting
    expr: changes(kube_pod_container_status_restarts_total{container='education-service'}[1h]) > 2
    labels:
      severity: warning
      service: education
    annotations:
      summary: "Pod restarting frequently"
      description: "Pod has restarted more than 2 times in the last hour"

  - alert: FeatureFlagError
    expr: rate(feature_flag_error_total{service='education'}[5m]) > 0
    for: 5m
    labels:
      severity: warning
      service: education
    annotations:
      summary: "Feature flag evaluation errors detected"
      description: "Feature flag system is experiencing errors"