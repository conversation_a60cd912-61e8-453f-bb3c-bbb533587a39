import client from 'prom-client';
import { Logger } from '../../utils/logger';

export class EncryptionMetrics {
  private static instance: EncryptionMetrics;

  private encryptionDuration: client.Histogram;
  private encryptionErrors: client.Counter;
  private cacheHits: client.Counter;
  private cacheMisses: client.Counter;
  private activeOperations: client.Gauge;

  private constructor() {
    // Initialiser les métriques Prometheus
    this.encryptionDuration = new client.Histogram({
      name: 'encryption_operation_duration_seconds',
      help: 'Duration of encryption operations',
      labelNames: ['operation_type', 'algorithm'],
      buckets: [0.1, 0.5, 1, 2, 5]
    });

    this.encryptionErrors = new client.Counter({
      name: 'encryption_operation_errors_total',
      help: 'Total number of encryption operation errors',
      labelNames: ['operation_type', 'error_type']
    });

    this.cacheHits = new client.Counter({
      name: 'encryption_cache_hits_total',
      help: 'Total number of cache hits'
    });

    this.cacheMisses = new client.Counter({
      name: 'encryption_cache_misses_total',
      help: 'Total number of cache misses'
    });

    this.activeOperations = new client.Gauge({
      name: 'encryption_active_operations',
      help: 'Number of currently active encryption operations'
    });
  }

  public static getInstance(): EncryptionMetrics {
    if (!EncryptionMetrics.instance) {
      EncryptionMetrics.instance = new EncryptionMetrics();
    }
    return EncryptionMetrics.instance;
  }

  public observeOperation(type: string, algorithm: string, duration: number): void {
    this.encryptionDuration.labels(type, algorithm).observe(duration);
  }

  public incrementError(type: string, errorType: string): void {
    this.encryptionErrors.labels(type, errorType).inc();
    Logger.error(`Encryption error: ${type} - ${errorType}`);
  }

  public recordCacheHit(): void {
    this.cacheHits.inc();
  }

  public recordCacheMiss(): void {
    this.cacheMisses.inc();
  }

  public setActiveOperations(count: number): void {
    this.activeOperations.set(count);
  }

  public async getMetrics(): Promise<string> {
    return await client.register.metrics();
  }
}