groups:
- name: push_notification_alerts
  rules:
  - alert: HighNotificationFailureRate
    expr: rate(push_notification_failed_total[5m]) / rate(push_notification_sent_total[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Taux d'échec élevé des notifications"
      description: "Le taux d'échec des notifications push est supérieur à 10% depuis 5 minutes"

  - alert: HighNotificationLatency
    expr: histogram_quantile(0.95, rate(push_notification_send_duration_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Latence élevée des notifications"
      description: "La latence P95 des notifications push est supérieure à 2 secondes depuis 5 minutes"

  - alert: LowSubscriptionRate
    expr: rate(push_subscription_saved_total[24h]) < 1
    for: 1h
    labels:
      severity: info
    annotations:
      summary: "Faible taux de souscription"
      description: "Le taux de nouvelles souscriptions est faible sur les dernières 24 heures"

  - alert: HighSubscriptionExpiration
    expr: rate(push_subscription_expired_total[1h]) > rate(push_subscription_saved_total[1h])
    for: 1h
    labels:
      severity: warning
    annotations:
      summary: "Taux d'expiration élevé des souscriptions"
      description: "Le taux d'expiration des souscriptions est supérieur au taux de nouvelles souscriptions"
