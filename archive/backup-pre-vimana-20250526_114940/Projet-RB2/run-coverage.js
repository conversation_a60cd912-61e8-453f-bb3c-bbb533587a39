/**
 * Script pour exécuter les tests avec couverture de code
 * 
 * Ce script permet de:
 * 1. Exécuter les tests avec couverture de code
 * 2. Générer un rapport de couverture
 * 3. Afficher un résumé de la couverture
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Configuration
const MEMORY_LIMIT = 2048; // Limite de mémoire en MB
const CONFIG_FILE = 'minimal-jest.config.js';

// Fonction pour exécuter les tests avec couverture
async function runTestsWithCoverage(pattern) {
  const command = `NODE_OPTIONS="--max-old-space-size=${MEMORY_LIMIT}" npx jest --config=${CONFIG_FILE} ${pattern} --coverage`;
  
  try {
    console.log(`Exécution des tests avec couverture: ${pattern}`);
    execSync(command, { stdio: 'inherit' });
    console.log('Tests terminés avec succès');
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'exécution des tests: ${error.message}`);
    return false;
  }
}

// Fonction pour afficher un résumé de la couverture
function displayCoverageSummary() {
  try {
    const coverageSummaryPath = path.join('coverage', 'coverage-summary.json');
    if (!fs.existsSync(coverageSummaryPath)) {
      console.log('Aucun rapport de couverture trouvé');
      return;
    }
    
    const summary = JSON.parse(fs.readFileSync(coverageSummaryPath, 'utf8'));
    const total = summary.total;
    
    console.log('\n=== Résumé de la couverture de code ===');
    console.log(`Lignes: ${total.lines.pct.toFixed(2)}%`);
    console.log(`Fonctions: ${total.functions.pct.toFixed(2)}%`);
    console.log(`Branches: ${total.branches.pct.toFixed(2)}%`);
    console.log(`Statements: ${total.statements.pct.toFixed(2)}%`);
    console.log('=====================================\n');
    
    console.log('Rapport complet disponible dans le répertoire "coverage"');
    console.log('Ouvrez coverage/lcov-report/index.html dans votre navigateur pour voir le rapport détaillé');
  } catch (error) {
    console.error(`Erreur lors de l'affichage du résumé de couverture: ${error.message}`);
  }
}

// Fonction principale
async function main() {
  // Vérifier les arguments
  const args = process.argv.slice(2);
  const testPattern = args[0] || 'Backend/src/tests/**/*.test.ts';
  
  // Exécuter les tests avec couverture
  const success = await runTestsWithCoverage(testPattern);
  
  // Afficher le résumé de la couverture
  if (success) {
    displayCoverageSummary();
  }
}

// Exécuter le script
main().catch(error => {
  console.error(`Erreur lors de l'exécution du script: ${error.message}`);
  process.exit(1);
});
