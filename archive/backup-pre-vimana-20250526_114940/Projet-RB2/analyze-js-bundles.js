const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Vérifier si webpack-bundle-analyzer est installé
try {
  require.resolve('webpack-bundle-analyzer');
} catch (e) {
  console.log('Installing webpack-bundle-analyzer...');
  execSync('npm install --save-dev webpack-bundle-analyzer');
}

// Fonction pour modifier le fichier de configuration Vite
function updateViteConfig() {
  const viteConfigPath = path.join(__dirname, 'frontend', 'vite.config.ts');
  
  if (!fs.existsSync(viteConfigPath)) {
    console.error(`Vite config file not found at ${viteConfigPath}`);
    return false;
  }
  
  let viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
  
  // Vérifier si webpack-bundle-analyzer est déjà configuré
  if (viteConfig.includes('webpack-bundle-analyzer')) {
    console.log('webpack-bundle-analyzer is already configured in vite.config.ts');
    return true;
  }
  
  // Ajouter l'import pour le plugin
  if (!viteConfig.includes('import { visualizer }')) {
    viteConfig = viteConfig.replace(
      /import {/,
      `import { visualizer } from 'rollup-plugin-visualizer';\nimport {`
    );
  }
  
  // Ajouter le plugin à la configuration
  viteConfig = viteConfig.replace(
    /plugins: \[([\s\S]*?)\]/,
    (match, plugins) => {
      return `plugins: [${plugins}${plugins.trim() ? ',' : ''}
    visualizer({
      open: true,
      filename: 'dist/stats.html',
      gzipSize: true,
      brotliSize: true,
    })
  ]`;
    }
  );
  
  // Sauvegarder le fichier modifié
  fs.writeFileSync(viteConfigPath, viteConfig);
  console.log('Updated vite.config.ts with bundle analyzer plugin');
  
  // Installer rollup-plugin-visualizer
  try {
    execSync('npm install --save-dev rollup-plugin-visualizer', { stdio: 'inherit' });
    console.log('Installed rollup-plugin-visualizer');
    return true;
  } catch (error) {
    console.error('Failed to install rollup-plugin-visualizer:', error);
    return false;
  }
}

// Fonction pour générer un rapport d'analyse des bundles
function analyzeBundles() {
  console.log('Building project for bundle analysis...');
  
  try {
    execSync('cd frontend && npm run build', { stdio: 'inherit' });
    console.log('Build completed. Bundle analysis report generated at frontend/dist/stats.html');
    
    // Ouvrir le rapport dans le navigateur
    const platform = process.platform;
    if (platform === 'darwin') {
      execSync('open frontend/dist/stats.html');
    } else if (platform === 'win32') {
      execSync('start frontend/dist/stats.html');
    } else {
      execSync('xdg-open frontend/dist/stats.html');
    }
    
    return true;
  } catch (error) {
    console.error('Failed to build project:', error);
    return false;
  }
}

// Fonction pour générer des recommandations d'optimisation
function generateOptimizationRecommendations() {
  console.log('\nJavaScript Bundle Optimization Recommendations:');
  console.log('===========================================');
  
  console.log('\n1. Code Splitting');
  console.log('   - Utilisez React.lazy() et Suspense pour charger les composants à la demande');
  console.log('   - Exemple:');
  console.log('     ```jsx');
  console.log('     const ProfilePage = React.lazy(() => import(\'./pages/ProfilePage\'));');
  console.log('     ```');
  
  console.log('\n2. Réduire la taille des dépendances');
  console.log('   - Utilisez des alternatives plus légères pour les bibliothèques volumineuses');
  console.log('   - Importez uniquement les fonctions nécessaires des bibliothèques');
  console.log('   - Exemple:');
  console.log('     ```jsx');
  console.log('     // Avant');
  console.log('     import { Button, Modal, Form, Input } from \'antd\';');
  console.log('     ');
  console.log('     // Après');
  console.log('     import Button from \'antd/lib/button\';');
  console.log('     import Modal from \'antd/lib/modal\';');
  console.log('     ```');
  
  console.log('\n3. Optimiser les images et les assets');
  console.log('   - Utilisez des formats d\'image modernes (WebP, AVIF)');
  console.log('   - Chargez les images de manière paresseuse');
  console.log('   - Utilisez des sprites SVG pour les icônes');
  
  console.log('\n4. Mettre en cache les données API');
  console.log('   - Utilisez React Query ou SWR pour la mise en cache et la revalidation');
  console.log('   - Mettez en cache les résultats d\'API dans le localStorage lorsque c\'est approprié');
  
  console.log('\n5. Optimiser les rendus React');
  console.log('   - Utilisez React.memo pour éviter les rendus inutiles');
  console.log('   - Utilisez useCallback et useMemo pour mémoriser les fonctions et les valeurs');
  console.log('   - Évitez les rendus en cascade avec une gestion d\'état appropriée');
  
  console.log('\nPour plus d\'informations, consultez le rapport d\'analyse des bundles généré.');
}

// Fonction principale
async function main() {
  console.log('Starting JavaScript bundle analysis...');
  
  const configUpdated = updateViteConfig();
  if (!configUpdated) {
    console.error('Failed to update Vite configuration. Aborting analysis.');
    return;
  }
  
  const analysisSuccessful = analyzeBundles();
  if (analysisSuccessful) {
    generateOptimizationRecommendations();
  }
}

// Exécuter le script
main().catch(error => {
  console.error('Error analyzing JavaScript bundles:', error);
  process.exit(1);
});
