import React from 'react';
import useTransport from '../hooks/useTransport.js';
import { Box, Grid, Card, CardContent, Typography, CircularProgress, Alert } from '@mui/material';

const TransportList = () => {
  const { transports, loading, error } = useTransport();

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Grid container spacing={3}>
      {transports.map((transport) => (
        <Grid item xs={12} sm={6} md={4} key={transport.id}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                {transport.type}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Capacité: {transport.capacity} personnes
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Prix: {transport.price}€
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Disponibilité: {transport.available ? 'Disponible' : 'Non disponible'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default TransportList;
