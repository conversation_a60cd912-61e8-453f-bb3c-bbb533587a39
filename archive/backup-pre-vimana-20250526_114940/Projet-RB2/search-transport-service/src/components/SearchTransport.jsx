import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Paper,
  Stack,
  Typography,
  InputAdornment,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { fr } from 'date-fns/locale';
import SearchIcon from '@mui/icons-material/Search';

const SearchTransport = ({ onSearch }) => {
  const [searchCriteria, setSearchCriteria] = useState({
    departure: '',
    destination: '',
    date: null,
    passengers: 1,
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setSearchCriteria((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDateChange = (newValue) => {
    setSearchCriteria((prev) => ({
      ...prev,
      date: newValue,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSearch(searchCriteria);
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Rechercher un transport
      </Typography>

      <form onSubmit={handleSubmit}>
        <Stack spacing={3}>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField
              name="departure"
              label="Lieu de départ"
              value={searchCriteria.departure}
              onChange={handleChange}
              required
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              name="destination"
              label="Destination"
              value={searchCriteria.destination}
              onChange={handleChange}
              required
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
              <DateTimePicker
                label="Date et heure"
                value={searchCriteria.date}
                onChange={handleDateChange}
                renderInput={(params) => (
                  <TextField {...params} required fullWidth />
                )}
                minDateTime={new Date()}
              />
            </LocalizationProvider>

            <TextField
              name="passengers"
              label="Nombre de passagers"
              type="number"
              value={searchCriteria.passengers}
              onChange={handleChange}
              required
              InputProps={{
                inputProps: { min: 1 },
              }}
              sx={{ width: '200px' }}
            />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              type="submit"
              variant="contained"
              size="large"
              startIcon={<SearchIcon />}
            >
              Rechercher
            </Button>
          </Box>
        </Stack>
      </form>
    </Paper>
  );
};

export default SearchTransport;
