import React, { useState } from 'react';
import useReservation from '../hooks/useReservation.js';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Stack,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import fr from 'date-fns/locale/fr';

const ReservationForm = ({ transportId, onSuccess }) => {
  const { createReservation, loading, error } = useReservation();
  const [formData, setFormData] = useState({
    pickupLocation: '',
    dropoffLocation: '',
    pickupDateTime: null,
    passengers: 1,
    specialRequests: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDateChange = (newValue) => {
    setFormData((prev) => ({
      ...prev,
      pickupDateTime: newValue,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const reservationData = {
      ...formData,
      transportId,
      status: 'pending',
    };

    const result = await createReservation(reservationData);
    if (result) {
      onSuccess?.(result);
      // Réinitialiser le formulaire
      setFormData({
        pickupLocation: '',
        dropoffLocation: '',
        pickupDateTime: null,
        passengers: 1,
        specialRequests: '',
      });
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Nouvelle Réservation
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Stack spacing={3}>
          <TextField
            name="pickupLocation"
            label="Lieu de prise en charge"
            value={formData.pickupLocation}
            onChange={handleChange}
            required
            fullWidth
          />

          <TextField
            name="dropoffLocation"
            label="Lieu de destination"
            value={formData.dropoffLocation}
            onChange={handleChange}
            required
            fullWidth
          />

          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
            <DateTimePicker
              label="Date et heure de prise en charge"
              value={formData.pickupDateTime}
              onChange={handleDateChange}
              renderInput={(params) => <TextField {...params} required fullWidth />}
              minDateTime={new Date()}
            />
          </LocalizationProvider>

          <TextField
            name="passengers"
            label="Nombre de passagers"
            type="number"
            value={formData.passengers}
            onChange={handleChange}
            required
            fullWidth
            InputProps={{ inputProps: { min: 1 } }}
          />

          <TextField
            name="specialRequests"
            label="Demandes spéciales"
            value={formData.specialRequests}
            onChange={handleChange}
            multiline
            rows={4}
            fullWidth
          />

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
              sx={{ minWidth: 120 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Réserver'}
            </Button>
          </Box>
        </Stack>
      </form>
    </Paper>
  );
};

export default ReservationForm;
