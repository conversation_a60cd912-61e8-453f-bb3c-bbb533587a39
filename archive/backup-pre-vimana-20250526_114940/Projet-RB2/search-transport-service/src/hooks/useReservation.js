import { useState } from 'react';

const useReservation = () => {
  const [reservations, setReservations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Créer une nouvelle réservation
  const createReservation = async (reservationData) => {
    setLoading(true);
    try {
      // Simuler un appel API - À remplacer par votre véritable appel API
      const response = await fetch('/api/reservations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reservationData),
      });
      const data = await response.json();
      setReservations([...reservations, data]);
      setError(null);
      return data;
    } catch (err) {
      setError('Erreur lors de la création de la réservation');
      console.error('Erreur de création:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Obtenir les réservations d'un utilisateur
  const getUserReservations = async (userId) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/users/${userId}/reservations`);
      const data = await response.json();
      setReservations(data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des réservations');
      console.error('Erreur de chargement:', err);
    } finally {
      setLoading(false);
    }
  };

  // Annuler une réservation
  const cancelReservation = async (reservationId) => {
    setLoading(true);
    try {
      await fetch(`/api/reservations/${reservationId}`, {
        method: 'DELETE',
      });
      setReservations(reservations.filter(res => res.id !== reservationId));
      setError(null);
      return true;
    } catch (err) {
      setError('Erreur lors de l\'annulation de la réservation');
      console.error('Erreur d\'annulation:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Mettre à jour une réservation
  const updateReservation = async (reservationId, updateData) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/reservations/${reservationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });
      const updatedReservation = await response.json();
      setReservations(reservations.map(res => 
        res.id === reservationId ? updatedReservation : res
      ));
      setError(null);
      return updatedReservation;
    } catch (err) {
      setError('Erreur lors de la mise à jour de la réservation');
      console.error('Erreur de mise à jour:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    reservations,
    loading,
    error,
    createReservation,
    getUserReservations,
    cancelReservation,
    updateReservation,
  };
};

export default useReservation;
