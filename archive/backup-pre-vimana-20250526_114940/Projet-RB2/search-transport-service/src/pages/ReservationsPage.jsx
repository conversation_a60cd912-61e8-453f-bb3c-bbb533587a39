import React from 'react';
import { Container, Typography, Box, Tab, Tabs } from '@mui/material';
import ReservationList from '../components/ReservationList.jsx';

const ReservationsPage = () => {
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // TODO: Remplacer par l'ID réel de l'utilisateur connecté
  const userId = "user123";

  return (
    <Container maxWidth="xl">
      <Box sx={{ mt: 4, mb: 8 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Mes R<PERSON>ervations
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange}
            aria-label="réservations tabs"
          >
            <Tab label="En cours" />
            <Tab label="Historique" />
          </Tabs>
        </Box>

        {tabValue === 0 && (
          <ReservationList 
            userId={userId}
            filter="active"
          />
        )}
        
        {tabValue === 1 && (
          <ReservationList 
            userId={userId}
            filter="history"
          />
        )}
      </Box>
    </Container>
  );
};

export default ReservationsPage;
