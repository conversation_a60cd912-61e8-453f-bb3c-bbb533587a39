import React from 'react';
import { Container, Typography, Box, Paper } from '@mui/material';
import SearchTransport from '../components/SearchTransport.jsx';
import TransportList from '../components/TransportList.jsx';

const HomePage = () => {
  const handleSearch = (criteria) => {
    // À implémenter: logique de recherche
    console.log('Critères de recherche:', criteria);
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ mt: 4, mb: 8 }}>
        <Paper 
          elevation={0} 
          sx={{ 
            p: 4, 
            mb: 4, 
            backgroundColor: 'primary.main', 
            color: 'white',
            borderRadius: 2
          }}
        >
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 700,
              fontSize: { xs: '2.5rem', md: '3.75rem' },
            }}
          >
            Réservez votre transport
          </Typography>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 400,
              maxWidth: '800px',
            }}
          >
            Trouvez le transport idéal pour votre prochain trajet. Simple, rapide et fiable.
          </Typography>
        </Paper>

        <SearchTransport onSearch={handleSearch} />
        
        <Box sx={{ mt: 6 }}>
          <Typography variant="h4" component="h2" gutterBottom>
            Transports disponibles
          </Typography>
          <TransportList />
        </Box>
      </Box>
    </Container>
  );
};

export default HomePage;
