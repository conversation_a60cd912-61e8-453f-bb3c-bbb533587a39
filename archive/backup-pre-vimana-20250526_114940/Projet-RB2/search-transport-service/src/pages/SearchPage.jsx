import React, { useState } from 'react';
import { Container, Typography, Box } from '@mui/material';
import SearchTransport from '../components/SearchTransport.jsx';
import TransportList from '../components/TransportList.jsx';
import useTransport from '../hooks/useTransport.js';

const SearchPage = () => {
  const { searchTransports } = useTransport();
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (criteria) => {
    await searchTransports(criteria);
    setHasSearched(true);
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ mt: 4, mb: 8 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Rechercher un transport
        </Typography>

        <SearchTransport onSearch={handleSearch} />
        
        {hasSearched && (
          <Box sx={{ mt: 4 }}>
            <Typography variant="h5" component="h2" gutterBottom>
              Résultats de la recherche
            </Typography>
            <TransportList />
          </Box>
        )}
      </Box>
    </Container>
  );
};

export default SearchPage;
