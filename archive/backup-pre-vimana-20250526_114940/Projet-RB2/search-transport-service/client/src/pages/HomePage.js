import React from 'react';
import { Container, Typography, Box, Button } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

const HomePage = () => {
  return (
    <Container>
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="h2" component="h1" gutterBottom>
          Welcome to Search Transport
        </Typography>
        <Typography variant="h5" component="h2" gutterBottom>
          Find and book your transportation easily
        </Typography>
        <Box sx={{ mt: 4 }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            component={RouterLink}
            to="/search"
            sx={{ mr: 2 }}
          >
            Search Transport
          </Button>
          <Button
            variant="outlined"
            color="primary"
            size="large"
            component={RouterLink}
            to="/register"
          >
            Register Now
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default HomePage;
