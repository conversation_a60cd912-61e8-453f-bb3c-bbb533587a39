{"name": "search-transport-client", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/material": "^5.13.0", "@mui/icons-material": "^5.11.16", "axios": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.1", "react-scripts": "5.0.1", "@babel/plugin-proposal-private-property-in-object": "^7.21.11"}, "scripts": {"start": "react-scripts start", "build": "CI=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}