apiVersion: v1
kind: Service
metadata:
  name: {{ include "search-transport-service.fullname" . }}
  labels:
    {{- include "search-transport-service.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "search-transport-service.selectorLabels" . | nindent 4 }}