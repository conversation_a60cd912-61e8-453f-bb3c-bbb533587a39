version: '3.8'

services:
  # Backend NestJS
  backend-nestjs:
    build:
      context: ./Backend-NestJS
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=****************************************/retreat_and_be?schema=public
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - JWT_REFRESH_SECRET=your-refresh-secret-key
      - MICROSERVICES_SOCIAL_PLATFORM_VIDEO_URL=http://social-platform-video:3002
      - MICROSERVICES_SOCIAL_PLATFORM_VIDEO_API_KEY=social-platform-video-api-key
      - MICROSERVICES_MESSAGING_URL=http://messaging-service:5178
      - MICROSERVICES_MESSAGING_API_KEY=messaging-service-api-key
    volumes:
      - ./Backend-NestJS:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - social-platform-video
      - messaging-service
    networks:
      - retreat-network
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./Front-Audrey-V1-Main-main
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=http://localhost:3000
      - REACT_APP_SOCIAL_PLATFORM_VIDEO_URL=http://localhost:3002
      - REACT_APP_MESSAGING_SERVICE_URL=http://localhost:5178
    volumes:
      - ./Front-Audrey-V1-Main-main:/app
      - /app/node_modules
    depends_on:
      - backend-nestjs
    networks:
      - retreat-network
    restart: unless-stopped

  # Social Platform Video Microservice
  social-platform-video:
    build:
      context: ./Social-Platform-Video
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - MONGODB_URI=mongodb://mongodb:27017/social-platform-video
      - JWT_SECRET=your-jwt-secret-key
      - MESSAGING_SERVICE_URL=http://messaging-service:5178
      - MESSAGING_SERVICE_API_KEY=messaging-service-api-key
    volumes:
      - ./Social-Platform-Video:/app
      - /app/node_modules
    depends_on:
      - mongodb
    networks:
      - retreat-network
    restart: unless-stopped

  # Messaging Service Microservice
  messaging-service:
    build:
      context: ./messaging-service
      dockerfile: Dockerfile
    ports:
      - "5178:5178"
    environment:
      - NODE_ENV=development
      - PORT=5178
      - MONGODB_URI=mongodb://mongodb:27017/messaging-service
      - JWT_SECRET=your-jwt-secret-key
      - SOCIAL_SERVICE_URL=http://backend-nestjs:3000/social
      - SOCIAL_PLATFORM_VIDEO_URL=http://social-platform-video:3002
      - SOCIAL_API_KEY=social-api-key
      - ALLOWED_ORIGINS=http://localhost,http://localhost:80,http://localhost:3000
    volumes:
      - ./messaging-service:/app
      - /app/node_modules
    depends_on:
      - mongodb
    networks:
      - retreat-network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=retreat_and_be
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - retreat-network
    restart: unless-stopped

  # MongoDB Database
  mongodb:
    image: mongo:6-jammy
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - retreat-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - retreat-network
    restart: unless-stopped

networks:
  retreat-network:
    driver: bridge

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
