groups:
- name: node
  rules:
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High CPU usage on {{ $labels.instance }}
      description: CPU usage is above 80% for 5 minutes

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High memory usage on {{ $labels.instance }}
      description: Memory usage is above 85% for 5 minutes

  - alert: DiskSpaceRunningOut
    expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Disk space running out on {{ $labels.instance }}
      description: Disk usage is above 85% for 5 minutes

- name: containers
  rules:
  - alert: ContainerKilled
    expr: time() - container_last_seen > 60
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Container killed on {{ $labels.instance }}
      description: Container {{ $labels.name }} has been killed

  - alert: ContainerHighMemory
    expr: container_memory_usage_bytes / container_spec_memory_limit_bytes * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Container high memory usage on {{ $labels.instance }}
      description: Container {{ $labels.name }} memory usage is above 80%

- name: services
  rules:
  - alert: ServiceDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: Service {{ $labels.job }} is down
      description: Service has been down for more than 1 minute

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High response time for {{ $labels.service }}
      description: 95th percentile of response time is above 2 seconds for 5 minutes
