// Serveur HTTP natif sans dépendances externes
const http = require('http');
const fs = require('fs');
const path = require('path');

// Configuration du port
const PORT = 3500;

// Types MIME pour différentes extensions de fichiers
const MIME_TYPES = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.mjs': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.txt': 'text/plain',
  '.pdf': 'application/pdf',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'font/otf',
  '.xml': 'application/xml',
};

// Créer le serveur HTTP
const server = http.createServer((req, res) => {
  console.log(`Requête reçue: ${req.method} ${req.url}`);
  
  // Normaliser l'URL
  let url = req.url;
  
  // Vérifier si l'URL contient un point (indique probablement un fichier)
  // Sinon servir index.html pour le routage SPA
  if (!url.includes('.')) {
    console.log(`Route SPA détectée: ${url} -> index.html`);
    url = '/index.html';
  }
  
  // Construire le chemin du fichier
  const filePath = path.join(__dirname, url === '/' ? 'index.html' : url.substr(1));
  
  // Obtenir l'extension du fichier
  const extname = path.extname(filePath);
  
  // Déterminer le type de contenu en fonction de l'extension
  const contentType = MIME_TYPES[extname] || 'application/octet-stream';
  
  // Lire le fichier
  fs.readFile(filePath, (err, content) => {
    if (err) {
      if (err.code === 'ENOENT') {
        // Fichier non trouvé - servir index.html à la place (SPA routing)
        fs.readFile(path.join(__dirname, 'index.html'), (err, content) => {
          if (err) {
            // Si même index.html n'est pas trouvé, renvoyer une erreur 500
            res.writeHead(500);
            res.end(`Erreur serveur: ${err.code}`);
          } else {
            // Servir index.html avec le type MIME approprié
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(content, 'utf-8');
            console.log(`Servi index.html pour la route: ${url}`);
          }
        });
      } else {
        // Autre erreur serveur
        res.writeHead(500);
        res.end(`Erreur serveur: ${err.code}`);
      }
      return;
    }
    
    // Fichier trouvé, le servir avec le bon type MIME
    res.writeHead(200, { 'Content-Type': contentType });
    res.end(content, 'utf-8');
    console.log(`Servi: ${filePath} (${contentType})`);
  });
});

// Démarrer le serveur
server.listen(PORT, () => {
  console.log(`
╔══════════════════════════════════════════════════════════╗
║                                                          ║
║   🚀 Serveur HTTP démarré sur http://localhost:${PORT}      ║
║                                                          ║
║   📝 Toutes les routes servent index.html (SPA routing)   ║
║                                                          ║
║   🔍 Les types MIME sont configurés correctement:         ║
║      .js   -> application/javascript                     ║
║      .html -> text/html                                  ║
║      .css  -> text/css                                   ║
║                                                          ║
║   ✅ Routes à tester:                                     ║
║      http://localhost:${PORT}/                              ║
║      http://localhost:${PORT}/test                          ║
║      http://localhost:${PORT}/login                         ║
║      http://localhost:${PORT}/dashboard                     ║
║                                                          ║
╚══════════════════════════════════════════════════════════╝
  `);
}); 