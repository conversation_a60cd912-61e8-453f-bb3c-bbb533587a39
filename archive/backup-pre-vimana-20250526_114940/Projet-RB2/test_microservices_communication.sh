#!/bin/bash
# Script pour tester la communication entre les microservices

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Test de communication entre les microservices ===${NC}"

# Vérifier que Docker est installé
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker n'est pas installé. Veuillez l'installer avant de continuer.${NC}"
    exit 1
fi

# Vérifier que Docker Compose est installé
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Docker Compose n'est pas installé. Veuillez l'installer avant de continuer.${NC}"
    exit 1
fi

# Démarrer les services avec Docker Compose
echo -e "\n${YELLOW}Démarrage des services avec Docker Compose...${NC}"
docker-compose up -d

# Attendre que les services soient prêts
echo -e "\n${YELLOW}Attente que les services soient prêts...${NC}"
sleep 10

# Vérifier que les services sont en cours d'exécution
echo -e "\n${YELLOW}Vérification que les services sont en cours d'exécution...${NC}"
AGENT_RB_RUNNING=$(docker-compose ps | grep agent-rb | grep Up | wc -l)
SUPERAGENT_RUNNING=$(docker-compose ps | grep superagent | grep Up | wc -l)
AGENT_IA_RUNNING=$(docker-compose ps | grep agent-ia | grep Up | wc -l)

if [ $AGENT_RB_RUNNING -eq 0 ]; then
    echo -e "${RED}Le service Agent-RB n'est pas en cours d'exécution.${NC}"
    docker-compose logs agent-rb
    exit 1
fi

if [ $SUPERAGENT_RUNNING -eq 0 ]; then
    echo -e "${RED}Le service superagent n'est pas en cours d'exécution.${NC}"
    docker-compose logs superagent
    exit 1
fi

if [ $AGENT_IA_RUNNING -eq 0 ]; then
    echo -e "${RED}Le service Agent IA n'est pas en cours d'exécution.${NC}"
    docker-compose logs agent-ia
    exit 1
fi

echo -e "${GREEN}Tous les services sont en cours d'exécution.${NC}"

# Tester la communication entre Agent-RB et superagent
echo -e "\n${YELLOW}Test de communication entre Agent-RB et superagent...${NC}"
docker-compose exec agent-rb curl -s http://superagent:5001/health
if [ $? -ne 0 ]; then
    echo -e "${RED}Échec de la communication entre Agent-RB et superagent.${NC}"
    exit 1
fi
echo -e "${GREEN}Communication entre Agent-RB et superagent réussie.${NC}"

# Tester la communication entre Agent-RB et Agent IA
echo -e "\n${YELLOW}Test de communication entre Agent-RB et Agent IA...${NC}"
docker-compose exec agent-rb curl -s http://agent-ia:5002/health
if [ $? -ne 0 ]; then
    echo -e "${RED}Échec de la communication entre Agent-RB et Agent IA.${NC}"
    exit 1
fi
echo -e "${GREEN}Communication entre Agent-RB et Agent IA réussie.${NC}"

# Tester la communication entre superagent et Agent-RB
echo -e "\n${YELLOW}Test de communication entre superagent et Agent-RB...${NC}"
docker-compose exec superagent curl -s http://agent-rb:5000/health
if [ $? -ne 0 ]; then
    echo -e "${RED}Échec de la communication entre superagent et Agent-RB.${NC}"
    exit 1
fi
echo -e "${GREEN}Communication entre superagent et Agent-RB réussie.${NC}"

# Tester la communication entre superagent et Agent IA
echo -e "\n${YELLOW}Test de communication entre superagent et Agent IA...${NC}"
docker-compose exec superagent curl -s http://agent-ia:5002/health
if [ $? -ne 0 ]; then
    echo -e "${RED}Échec de la communication entre superagent et Agent IA.${NC}"
    exit 1
fi
echo -e "${GREEN}Communication entre superagent et Agent IA réussie.${NC}"

# Tester la communication entre Agent IA et Agent-RB
echo -e "\n${YELLOW}Test de communication entre Agent IA et Agent-RB...${NC}"
docker-compose exec agent-ia curl -s http://agent-rb:5000/health
if [ $? -ne 0 ]; then
    echo -e "${RED}Échec de la communication entre Agent IA et Agent-RB.${NC}"
    exit 1
fi
echo -e "${GREEN}Communication entre Agent IA et Agent-RB réussie.${NC}"

# Tester la communication entre Agent IA et superagent
echo -e "\n${YELLOW}Test de communication entre Agent IA et superagent...${NC}"
docker-compose exec agent-ia curl -s http://superagent:5001/health
if [ $? -ne 0 ]; then
    echo -e "${RED}Échec de la communication entre Agent IA et superagent.${NC}"
    exit 1
fi
echo -e "${GREEN}Communication entre Agent IA et superagent réussie.${NC}"

# Exécuter les exemples de communication
echo -e "\n${YELLOW}Exécution des exemples de communication...${NC}"

echo -e "\n${YELLOW}Exemple de communication dans Agent-RB...${NC}"
docker-compose exec agent-rb python examples/communication_example.py

echo -e "\n${YELLOW}Exemple de communication dans superagent...${NC}"
docker-compose exec superagent python examples/communication_example.py

# Arrêter les services
echo -e "\n${YELLOW}Arrêt des services...${NC}"
docker-compose down

echo -e "\n${GREEN}Test de communication terminé avec succès.${NC}"
