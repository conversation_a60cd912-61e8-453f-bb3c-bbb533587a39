#!/bin/bash

# Set text colors
GREEN="\033[0;32m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}Correction des problèmes JSX dans les composants affiliate et ai...${NC}"

# 1. Correction des balises mal fermées dans les fichiers affiliate
echo "Correction des balises </h5> dans les fichiers affiliate..."
find frontend/src/components/affiliate -name "*.tsx" -exec sed -i "" "s/<\/h5>/<\/div>/g" {} \;

# 2. Correction des balises React et des éléments sans parent unique
echo "Correction des éléments sans parent unique..."
find frontend/src/components/affiliate -name "*.tsx" -exec sed -i "" "s/<React>/<React.Fragment>/g" {} \;
find frontend/src/components/affiliate -name "*.tsx" -exec sed -i "" "s/<\/React>/<\/React.Fragment>/g" {} \;

# 3. Correction des balises auto-fermantes avec button
echo "Correction des balises auto-fermantes..."
find frontend/src/components/affiliate -name "*.tsx" -exec sed -i "" "s/\/><\/button>/\/>/g" {} \;
find frontend/src/components/affiliate -name "*.tsx" -exec sed -i "" "s/<\/input>/<\/div>/g" {} \;

# 4. Correction des problèmes de syntaxe dans AffiliateRegistration.tsx
echo "Correction spécifique pour AffiliateRegistration.tsx..."
sed -i "" "s/const { name, value } = e;.target;/const { name, value } = e.target;/g" frontend/src/components/affiliate/AffiliateRegistration.tsx
sed -i "" "s/<div><\/div><\/div>/<\/div>/g" frontend/src/components/affiliate/AffiliateRegistration.tsx

# 5. Correction des balises fermantes multiples dans AffiliateSeasonalEvents.tsx
echo "Correction des balises fermantes multiples dans AffiliateSeasonalEvents.tsx..."
sed -i "" "s/<div><\/div><\/div><\/div><\/div><\/div><\/div><\/div><\/div><\/div><\/div><\/div>/<\/div>/g" frontend/src/components/affiliate/AffiliateSeasonalEvents.tsx
sed -i "" "s/<option key={type} value={type}  \/><\/option>/<option key={type} value={type} \/>/g" frontend/src/components/affiliate/AffiliateSeasonalEvents.tsx

# 6. Correction des balises fermantes multiples dans AffiliateStats.tsx
echo "Correction spécifique pour AffiliateStats.tsx..."
sed -i "" "s/<div><\/div><\/div><\/div><\/div><\/div>/<\/div>/g" frontend/src/components/affiliate/AffiliateStats.tsx

# 7. Correction des problèmes dans les fichiers ai
echo "Correction des composants AI..."
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/useMobileOptimization;()/useMobileOptimization()/g" {} \;
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/<\/SendIcon>/<\/div>/g" {} \;
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/<\/AssistantIcon>/<\/div>/g" {} \;
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/<\/SettingsIcon>/<\/IconButton>/g" {} \;
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/<\/DeleteIcon>/<\/IconButton>/g" {} \;
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/<\/CloseIcon>/<\/IconButton>/g" {} \;
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/<\/NavigateNextIcon>/<\/IconButton>/g" {} \;
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/<\/ExpandMoreIcon>/<\/IconButton>/g" {} \;
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/<\/HelpIcon>/<\/IconButton>/g" {} \;
find frontend/src/components/ai -name "*.tsx" -exec sed -i "" "s/<\/React.Fragment\/>/<\/React.Fragment>/g" {} \;

# 8. Correction des balises dans MatchingResults.tsx
echo "Correction spécifique pour MatchingResults.tsx..."
sed -i "" "s/<\/button><\/button>/<\/button>/g" frontend/src/components/ai/MatchingResults.tsx
sed -i "" "s/<ArrowLeft>/<ArrowLeft \/>/g" frontend/src/components/ai/MatchingResults.tsx

echo -e "${GREEN}Correction des balises JSX terminée${NC}"
echo -e "${BLUE}Vérification des erreurs restantes...${NC}" 