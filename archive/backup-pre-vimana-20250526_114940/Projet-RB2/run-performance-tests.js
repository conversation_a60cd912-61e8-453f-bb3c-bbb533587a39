#!/usr/bin/env node

/**
 * Script d'exécution des tests de performance
 * 
 * Ce script exécute une série de tests de performance pour mesurer:
 * - Temps de réponse des API
 * - Charge maximale supportée
 * - Consommation de ressources (CPU, mémoire)
 * - Performance du frontend (temps de chargement, métriques Web Vitals)
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { performance } = require('perf_hooks');

// Définition des couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Vérifier les arguments
const args = process.argv.slice(2);
const saveReport = args.includes('--save-report');
const runSpecificTest = args.find(arg => !arg.startsWith('--'));

// Configuration des tests
const performanceTests = {
  api: {
    name: 'API Response Time Tests',
    description: 'Tests de temps de réponse des API',
    endpoints: [
      { name: 'Login API', url: '/api/auth/login', method: 'POST', payload: { email: '<EMAIL>', password: 'password123' } },
      { name: 'Get Profile', url: '/api/users/profile', method: 'GET', needsAuth: true },
      { name: 'List Retreats', url: '/api/retreats', method: 'GET' },
      { name: 'Retreat Details', url: '/api/retreats/1', method: 'GET' },
      { name: 'Search Retreats', url: '/api/retreats/search', method: 'GET', queryParams: { keyword: 'yoga', location: 'france' } }
    ]
  },
  load: {
    name: 'Load Testing',
    description: 'Tests de charge pour déterminer la capacité maximale',
    scenarios: [
      { name: 'Browse Homepage', duration: 60, vus: [10, 20, 50, 100], url: '/' },
      { name: 'Search Retreats', duration: 60, vus: [10, 20, 50, 100], url: '/retreats/search?keyword=yoga' },
      { name: 'Login Flow', duration: 60, vus: [10, 20, 50], script: 'scripts/k6/login-flow.js' }
    ]
  },
  resource: {
    name: 'Resource Consumption Tests',
    description: 'Tests de consommation de ressources',
    operations: [
      { name: 'API Server Idle', duration: 30 },
      { name: 'API Server Under Medium Load', duration: 60, loadScript: 'scripts/k6/medium-load.js' },
      { name: 'API Server Under Heavy Load', duration: 60, loadScript: 'scripts/k6/heavy-load.js' },
      { name: 'Database Operations', duration: 60, script: 'scripts/resource-tests/db-operations.js' }
    ]
  },
  frontend: {
    name: 'Frontend Performance Tests',
    description: 'Tests de performance du frontend',
    pages: [
      { name: 'Homepage', url: '/' },
      { name: 'Retreats Listing', url: '/retreats' },
      { name: 'Retreat Details', url: '/retreats/1' },
      { name: 'User Dashboard', url: '/dashboard', needsAuth: true },
      { name: 'Checkout Flow', script: 'scripts/lighthouse/checkout-flow.js' }
    ]
  }
};

// Fonction pour exécuter une commande shell et capturer la sortie
function execCommand(command, options = {}) {
  try {
    return execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options
    });
  } catch (error) {
    if (options.ignoreError) {
      return error.stdout || '';
    }
    console.error(`${colors.red}Erreur lors de l'exécution de la commande: ${command}${colors.reset}`);
    if (error.stdout) console.error(error.stdout);
    if (error.stderr) console.error(error.stderr);
    return '';
  }
}

// Fonction pour vérifier les dépendances nécessaires
function checkDependencies() {
  console.log(`\n${colors.blue}Vérification des dépendances pour les tests de performance...${colors.reset}`);
  
  const requiredTools = [
    { name: 'k6', checkCommand: 'k6 version', installGuide: 'https://k6.io/docs/getting-started/installation' },
    { name: 'lighthouse', checkCommand: 'lighthouse --version', installCommand: 'npm install -g lighthouse' },
    { name: 'autocannon', checkCommand: 'autocannon --version', installCommand: 'npm install -g autocannon' }
  ];
  
  let allDependenciesInstalled = true;
  
  for (const tool of requiredTools) {
    try {
      execSync(tool.checkCommand, { stdio: 'pipe' });
      console.log(`${colors.green}✓ ${tool.name} est installé${colors.reset}`);
    } catch (error) {
      allDependenciesInstalled = false;
      console.log(`${colors.red}✗ ${tool.name} n'est pas installé${colors.reset}`);
      
      if (tool.installCommand) {
        console.log(`  Installez-le avec: ${colors.yellow}${tool.installCommand}${colors.reset}`);
      }
      
      if (tool.installGuide) {
        console.log(`  Guide d'installation: ${colors.cyan}${tool.installGuide}${colors.reset}`);
      }
    }
  }
  
  if (!allDependenciesInstalled) {
    console.log(`\n${colors.yellow}Veuillez installer les outils manquants avant de continuer.${colors.reset}`);
    process.exit(1);
  }
  
  return true;
}

// Fonction pour exécuter les tests d'API
async function runApiTests() {
  console.log(`\n${colors.magenta}Exécution des tests de temps de réponse d'API...${colors.reset}`);
  
  const results = [];
  const baseUrl = process.env.API_URL || 'http://localhost:3000';
  let authToken = null;
  
  // Fonction pour obtenir un token d'authentification si nécessaire
  async function getAuthToken() {
    if (authToken) return authToken;
    
    console.log(`${colors.blue}Obtention du token d'authentification...${colors.reset}`);
    
    try {
      const loginEndpoint = performanceTests.api.endpoints.find(e => e.name === 'Login API');
      if (!loginEndpoint) throw new Error('Endpoint de login non trouvé');
      
      const result = execCommand(`curl -s -X POST ${baseUrl}${loginEndpoint.url} -H "Content-Type: application/json" -d '${JSON.stringify(loginEndpoint.payload)}'`, { silent: true });
      
      const response = JSON.parse(result);
      authToken = response.token || response.access_token || response.accessToken;
      
      if (!authToken) {
        throw new Error('Token d\'authentification non trouvé dans la réponse');
      }
      
      console.log(`${colors.green}Token d'authentification obtenu avec succès${colors.reset}`);
      return authToken;
    } catch (error) {
      console.error(`${colors.red}Erreur lors de l'obtention du token d'authentification: ${error.message}${colors.reset}`);
      console.log(`${colors.yellow}Les tests nécessitant une authentification seront ignorés${colors.reset}`);
      return null;
    }
  }
  
  // Exécuter les tests pour chaque endpoint
  for (const endpoint of performanceTests.api.endpoints) {
    console.log(`\n${colors.cyan}Test de l'endpoint: ${endpoint.name}${colors.reset}`);
    
    // Vérifier si l'authentification est nécessaire
    if (endpoint.needsAuth) {
      const token = await getAuthToken();
      if (!token) {
        console.log(`${colors.yellow}Test ignoré car l'authentification est requise${colors.reset}`);
        continue;
      }
    }
    
    // Construire la commande autocannon
    let url = `${baseUrl}${endpoint.url}`;
    if (endpoint.queryParams) {
      const queryString = Object.entries(endpoint.queryParams)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');
      
      url += `?${queryString}`;
    }
    
    let command = `autocannon -c 10 -d 10 ${url}`;
    
    // Ajouter les headers si nécessaire
    const headers = [];
    
    if (endpoint.needsAuth && authToken) {
      headers.push(`"Authorization: Bearer ${authToken}"`);
    }
    
    if (endpoint.method === 'POST' || endpoint.payload) {
      headers.push('"Content-Type: application/json"');
    }
    
    if (headers.length > 0) {
      command += ` -H ${headers.join(' -H ')}`;
    }
    
    // Ajouter le payload si nécessaire
    if (endpoint.payload) {
      command += ` -m ${endpoint.method} -b '${JSON.stringify(endpoint.payload)}'`;
    } else if (endpoint.method && endpoint.method !== 'GET') {
      command += ` -m ${endpoint.method}`;
    }
    
    // Exécuter la commande et capturer la sortie
    const output = execCommand(command, { silent: true });
    
    // Parser les résultats
    try {
      const latency = output.match(/Latency: (\d+\.\d+)ms/);
      const requests = output.match(/Requests\/sec: (\d+\.\d+)/);
      const throughput = output.match(/Throughput: (\d+\.\d+)MB/);
      
      const result = {
        endpoint: endpoint.name,
        url: url,
        method: endpoint.method || 'GET',
        latencyMs: latency ? parseFloat(latency[1]) : null,
        requestsPerSec: requests ? parseFloat(requests[1]) : null,
        throughputMB: throughput ? parseFloat(throughput[1]) : null,
        timestamp: new Date().toISOString()
      };
      
      results.push(result);
      
      console.log(`${colors.green}Latence moyenne: ${result.latencyMs}ms${colors.reset}`);
      console.log(`${colors.green}Requêtes par seconde: ${result.requestsPerSec}${colors.reset}`);
    } catch (error) {
      console.error(`${colors.red}Erreur lors de l'analyse des résultats: ${error.message}${colors.reset}`);
    }
  }
  
  return results;
}

// Fonction pour exécuter les tests de charge
async function runLoadTests() {
  console.log(`\n${colors.magenta}Exécution des tests de charge...${colors.reset}`);
  
  const results = [];
  const baseUrl = process.env.API_URL || 'http://localhost:3000';
  
  for (const scenario of performanceTests.load.scenarios) {
    console.log(`\n${colors.cyan}Scénario de test de charge: ${scenario.name}${colors.reset}`);
    
    for (const vus of scenario.vus) {
      console.log(`${colors.blue}Exécution avec ${vus} utilisateurs virtuels...${colors.reset}`);
      
      const startTime = performance.now();
      let command;
      
      if (scenario.script) {
        // Utiliser un script k6 personnalisé
        command = `k6 run --vus ${vus} --duration ${scenario.duration}s ${scenario.script}`;
      } else {
        // Générer un script k6 simple pour tester une URL
        const tempScriptFile = path.join(__dirname, `temp-k6-script-${Date.now()}.js`);
        
        const k6Script = `
          import http from 'k6/http';
          import { sleep } from 'k6';
          
          export default function() {
            http.get('${baseUrl}${scenario.url}');
            sleep(1);
          }
        `;
        
        fs.writeFileSync(tempScriptFile, k6Script);
        command = `k6 run --vus ${vus} --duration ${scenario.duration}s ${tempScriptFile}`;
      }
      
      // Exécuter la commande et capturer la sortie
      const output = execCommand(command, { silent: true });
      const endTime = performance.now();
      
      if (!scenario.script) {
        // Supprimer le script temporaire
        try {
          fs.unlinkSync(path.join(__dirname, `temp-k6-script-${Date.now()}.js`));
        } catch (error) {
          // Ignorer les erreurs de suppression
        }
      }
      
      // Parser les résultats
      try {
        const http_req_duration = output.match(/http_req_duration.+avg=([0-9.]+)/);
        const http_reqs = output.match(/http_reqs.+count=([0-9.]+)/);
        const iteration_duration = output.match(/iteration_duration.+avg=([0-9.]+)/);
        
        const result = {
          scenario: scenario.name,
          vus: vus,
          duration: scenario.duration,
          avgResponseTime: http_req_duration ? parseFloat(http_req_duration[1]) : null,
          totalRequests: http_reqs ? parseInt(http_reqs[1]) : null,
          avgIterationDuration: iteration_duration ? parseFloat(iteration_duration[1]) : null,
          executionTimeMs: endTime - startTime,
          timestamp: new Date().toISOString()
        };
        
        results.push(result);
        
        console.log(`${colors.green}Temps de réponse moyen: ${result.avgResponseTime}ms${colors.reset}`);
        console.log(`${colors.green}Requêtes totales: ${result.totalRequests}${colors.reset}`);
        console.log(`${colors.green}Durée d'itération moyenne: ${result.avgIterationDuration}ms${colors.reset}`);
      } catch (error) {
        console.error(`${colors.red}Erreur lors de l'analyse des résultats: ${error.message}${colors.reset}`);
      }
    }
  }
  
  return results;
}

// Fonction pour exécuter les tests de consommation de ressources
async function runResourceTests() {
  console.log(`\n${colors.magenta}Exécution des tests de consommation de ressources...${colors.reset}`);
  
  const results = [];
  
  for (const operation of performanceTests.resource.operations) {
    console.log(`\n${colors.cyan}Test de consommation: ${operation.name}${colors.reset}`);
    
    const pidCommand = `pgrep -f "node.*server"`;
    const pid = execCommand(pidCommand, { silent: true }).trim();
    
    if (!pid) {
      console.log(`${colors.yellow}Aucun processus serveur Node.js trouvé, test ignoré${colors.reset}`);
      continue;
    }
    
    console.log(`${colors.blue}Surveillance du processus PID: ${pid} pendant ${operation.duration} secondes...${colors.reset}`);
    
    // Démarrer la génération de charge si nécessaire
    let loadProcess = null;
    if (operation.loadScript) {
      console.log(`${colors.blue}Démarrage de la génération de charge avec: ${operation.loadScript}${colors.reset}`);
      loadProcess = require('child_process').spawn('k6', ['run', operation.loadScript], {
        detached: true,
        stdio: 'ignore'
      });
    } else if (operation.script) {
      console.log(`${colors.blue}Exécution du script: ${operation.script}${colors.reset}`);
      execCommand(`node ${operation.script}`, { silent: true });
    }
    
    // Collecter les métriques de ressources à intervalles réguliers
    const cpuUsage = [];
    const memoryUsage = [];
    const interval = 1000; // 1 seconde
    const iterations = operation.duration;
    
    for (let i = 0; i < iterations; i++) {
      // Obtenir l'utilisation CPU
      const cpuCommand = `ps -p ${pid} -o %cpu | tail -1`;
      const cpu = parseFloat(execCommand(cpuCommand, { silent: true }).trim());
      cpuUsage.push(cpu);
      
      // Obtenir l'utilisation mémoire
      const memCommand = `ps -p ${pid} -o rss | tail -1`;
      const mem = parseInt(execCommand(memCommand, { silent: true }).trim()) / 1024; // Convertir en MB
      memoryUsage.push(mem);
      
      // Attendre pour la prochaine itération
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    // Terminer le processus de charge si nécessaire
    if (loadProcess) {
      process.kill(-loadProcess.pid);
    }
    
    // Calculer les moyennes
    const avgCpu = cpuUsage.reduce((a, b) => a + b, 0) / cpuUsage.length;
    const avgMem = memoryUsage.reduce((a, b) => a + b, 0) / memoryUsage.length;
    const maxCpu = Math.max(...cpuUsage);
    const maxMem = Math.max(...memoryUsage);
    
    const result = {
      operation: operation.name,
      duration: operation.duration,
      avgCpuUsage: avgCpu,
      maxCpuUsage: maxCpu,
      avgMemoryUsageMB: avgMem,
      maxMemoryUsageMB: maxMem,
      cpuSamples: cpuUsage,
      memorySamples: memoryUsage,
      timestamp: new Date().toISOString()
    };
    
    results.push(result);
    
    console.log(`${colors.green}Utilisation CPU moyenne: ${avgCpu.toFixed(2)}%${colors.reset}`);
    console.log(`${colors.green}Utilisation CPU max: ${maxCpu.toFixed(2)}%${colors.reset}`);
    console.log(`${colors.green}Utilisation mémoire moyenne: ${avgMem.toFixed(2)} MB${colors.reset}`);
    console.log(`${colors.green}Utilisation mémoire max: ${maxMem.toFixed(2)} MB${colors.reset}`);
  }
  
  return results;
}

// Fonction pour exécuter les tests de performance frontend
async function runFrontendTests() {
  console.log(`\n${colors.magenta}Exécution des tests de performance frontend...${colors.reset}`);
  
  const results = [];
  const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
  
  for (const page of performanceTests.frontend.pages) {
    console.log(`\n${colors.cyan}Test de la page: ${page.name}${colors.reset}`);
    
    if (page.needsAuth) {
      console.log(`${colors.yellow}Note: Ce test peut nécessiter une authentification manuelle${colors.reset}`);
    }
    
    if (page.script) {
      console.log(`${colors.blue}Exécution du script personnalisé: ${page.script}${colors.reset}`);
      execCommand(`node ${page.script}`, { silent: true });
      continue;
    }
    
    const url = `${baseUrl}${page.url}`;
    const outputFile = path.join(__dirname, `../reports/lighthouse-${page.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.json`);
    
    // Créer le répertoire des rapports s'il n'existe pas
    if (!fs.existsSync(path.dirname(outputFile))) {
      fs.mkdirSync(path.dirname(outputFile), { recursive: true });
    }
    
    console.log(`${colors.blue}Exécution de Lighthouse pour: ${url}${colors.reset}`);
    
    // Exécuter Lighthouse
    const command = `lighthouse ${url} --output json --output-path ${outputFile} --chrome-flags="--headless --no-sandbox --disable-gpu"`;
    execCommand(command, { silent: true });
    
    try {
      // Lire et parser le rapport Lighthouse
      const report = JSON.parse(fs.readFileSync(outputFile, 'utf8'));
      
      const result = {
        page: page.name,
        url: url,
        performanceScore: report.categories.performance.score * 100,
        firstContentfulPaint: report.audits['first-contentful-paint'].numericValue,
        speedIndex: report.audits['speed-index'].numericValue,
        largestContentfulPaint: report.audits['largest-contentful-paint'].numericValue,
        timeToInteractive: report.audits['interactive'].numericValue,
        totalBlockingTime: report.audits['total-blocking-time'].numericValue,
        cumulativeLayoutShift: report.audits['cumulative-layout-shift'].numericValue,
        reportPath: outputFile,
        timestamp: new Date().toISOString()
      };
      
      results.push(result);
      
      console.log(`${colors.green}Score de performance: ${result.performanceScore.toFixed(1)}/100${colors.reset}`);
      console.log(`${colors.green}First Contentful Paint: ${(result.firstContentfulPaint / 1000).toFixed(2)}s${colors.reset}`);
      console.log(`${colors.green}Largest Contentful Paint: ${(result.largestContentfulPaint / 1000).toFixed(2)}s${colors.reset}`);
      console.log(`${colors.green}Time to Interactive: ${(result.timeToInteractive / 1000).toFixed(2)}s${colors.reset}`);
      console.log(`${colors.green}Cumulative Layout Shift: ${result.cumulativeLayoutShift.toFixed(3)}${colors.reset}`);
    } catch (error) {
      console.error(`${colors.red}Erreur lors de l'analyse du rapport Lighthouse: ${error.message}${colors.reset}`);
    }
  }
  
  return results;
}

// Fonction pour générer un rapport consolidé
function generateReport(results) {
  console.log(`\n${colors.blue}Génération du rapport de performance...${colors.reset}`);
  
  // Créer le répertoire des rapports s'il n'existe pas
  const reportsDir = path.join(__dirname, '../reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  const reportPath = path.join(reportsDir, `performance-report-${new Date().toISOString().slice(0, 10)}.json`);
  
  // Construire le rapport
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      apiTests: results.api.length,
      loadTests: results.load.length,
      resourceTests: results.resource.length,
      frontendTests: results.frontend.length
    },
    results: {
      api: results.api,
      load: results.load,
      resource: results.resource,
      frontend: results.frontend
    }
  };
  
  // Ajouter des analyses et recommandations au rapport
  report.analysis = {
    api: analyzeApiResults(results.api),
    load: analyzeLoadResults(results.load),
    resource: analyzeResourceResults(results.resource),
    frontend: analyzeFrontendResults(results.frontend)
  };
  
  // Sauvegarder le rapport
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`${colors.green}Rapport généré et sauvegardé: ${reportPath}${colors.reset}`);
  
  return reportPath;
}

// Fonctions d'analyse des résultats
function analyzeApiResults(results) {
  if (!results || results.length === 0) return { issues: [], recommendations: [] };
  
  const issues = [];
  const recommendations = [];
  
  // Trouver les endpoints lents
  const slowEndpoints = results.filter(r => r.latencyMs > 100);
  if (slowEndpoints.length > 0) {
    issues.push(`${slowEndpoints.length} endpoints ont une latence > 100ms`);
    recommendations.push('Optimiser les requêtes de base de données pour les endpoints lents');
    recommendations.push('Considérer l\'implémentation de mise en cache pour les endpoints fréquemment accédés');
  }
  
  // Vérifier les endpoints avec un faible débit
  const lowThroughputEndpoints = results.filter(r => r.requestsPerSec < 100);
  if (lowThroughputEndpoints.length > 0) {
    issues.push(`${lowThroughputEndpoints.length} endpoints ont un débit < 100 req/sec`);
    recommendations.push('Améliorer la scalabilité des endpoints à faible débit');
  }
  
  return { issues, recommendations };
}

function analyzeLoadResults(results) {
  if (!results || results.length === 0) return { issues: [], recommendations: [] };
  
  const issues = [];
  const recommendations = [];
  
  // Vérifier les scénarios avec une dégradation significative sous charge
  const degradedScenarios = results.filter(r => {
    const baselineResult = results.find(br => br.scenario === r.scenario && br.vus === Math.min(...results.filter(rr => rr.scenario === r.scenario).map(rr => rr.vus)));
    return baselineResult && r.avgResponseTime > baselineResult.avgResponseTime * 3;
  });
  
  if (degradedScenarios.length > 0) {
    issues.push(`${degradedScenarios.length} scénarios montrent une dégradation significative des performances sous charge`);
    recommendations.push('Améliorer la scalabilité horizontale');
    recommendations.push('Optimiser les requêtes de base de données pour une meilleure performance sous charge');
    recommendations.push('Considérer l\'implémentation de caching');
  }
  
  return { issues, recommendations };
}

function analyzeResourceResults(results) {
  if (!results || results.length === 0) return { issues: [], recommendations: [] };
  
  const issues = [];
  const recommendations = [];
  
  // Vérifier l'utilisation CPU élevée
  const highCpuResults = results.filter(r => r.maxCpuUsage > 80);
  if (highCpuResults.length > 0) {
    issues.push(`${highCpuResults.length} opérations montrent une utilisation CPU > 80%`);
    recommendations.push('Profiler le code pour identifier les goulots d\'étranglement CPU');
    recommendations.push('Optimiser les algorithmes et boucles intensifs');
  }
  
  // Vérifier l'utilisation mémoire élevée
  const highMemResults = results.filter(r => r.maxMemoryUsageMB > 500);
  if (highMemResults.length > 0) {
    issues.push(`${highMemResults.length} opérations utilisent > 500MB de mémoire`);
    recommendations.push('Rechercher les fuites de mémoire potentielles');
    recommendations.push('Optimiser la gestion des objets volumineux');
  }
  
  return { issues, recommendations };
}

function analyzeFrontendResults(results) {
  if (!results || results.length === 0) return { issues: [], recommendations: [] };
  
  const issues = [];
  const recommendations = [];
  
  // Vérifier les scores de performance faibles
  const lowScorePages = results.filter(r => r.performanceScore < 70);
  if (lowScorePages.length > 0) {
    issues.push(`${lowScorePages.length} pages ont un score de performance < 70/100`);
    recommendations.push('Optimiser les images et ressources statiques');
    recommendations.push('Implémenter le lazy loading pour les images et composants non critiques');
  }
  
  // Vérifier les LCP lents
  const slowLCPPages = results.filter(r => r.largestContentfulPaint > 2500);
  if (slowLCPPages.length > 0) {
    issues.push(`${slowLCPPages.length} pages ont un LCP > 2.5s`);
    recommendations.push('Optimiser le rendu du contenu principal');
    recommendations.push('Réduire la taille des ressources critiques');
  }
  
  // Vérifier le CLS élevé
  const highCLSPages = results.filter(r => r.cumulativeLayoutShift > 0.1);
  if (highCLSPages.length > 0) {
    issues.push(`${highCLSPages.length} pages ont un CLS > 0.1`);
    recommendations.push('Fixer les dimensions des images et éléments média');
    recommendations.push('Éviter d\'insérer du contenu au-dessus du contenu existant');
  }
  
  return { issues, recommendations };
}

// Fonction principale
async function main() {
  console.log(`\n${colors.magenta}=== Tests de Performance - Retreat And Be ===${colors.reset}`);
  
  // Vérifier les dépendances
  checkDependencies();
  
  // Stocker les résultats
  const results = {
    api: [],
    load: [],
    resource: [],
    frontend: []
  };
  
  // Exécuter les tests spécifiques ou tous les tests
  if (!runSpecificTest || runSpecificTest === 'api') {
    results.api = await runApiTests();
  }
  
  if (!runSpecificTest || runSpecificTest === 'load') {
    results.load = await runLoadTests();
  }
  
  if (!runSpecificTest || runSpecificTest === 'resource') {
    results.resource = await runResourceTests();
  }
  
  if (!runSpecificTest || runSpecificTest === 'frontend') {
    results.frontend = await runFrontendTests();
  }
  
  // Générer le rapport si demandé
  if (saveReport) {
    const reportPath = generateReport(results);
    console.log(`\n${colors.green}Rapport de performance complet généré à: ${reportPath}${colors.reset}`);
  }
  
  // Afficher un résumé
  console.log(`\n${colors.magenta}=== Résumé des Tests de Performance ===${colors.reset}`);
  
  if (results.api.length > 0) {
    const avgLatency = results.api.reduce((sum, r) => sum + (r.latencyMs || 0), 0) / results.api.length;
    console.log(`${colors.cyan}API Tests:${colors.reset} ${results.api.length} endpoints testés, latence moyenne: ${avgLatency.toFixed(2)}ms`);
  }
  
  if (results.load.length > 0) {
    console.log(`${colors.cyan}Load Tests:${colors.reset} ${results.load.length} scénarios de charge exécutés`);
  }
  
  if (results.resource.length > 0) {
    const avgCpu = results.resource.reduce((sum, r) => sum + r.avgCpuUsage, 0) / results.resource.length;
    const avgMem = results.resource.reduce((sum, r) => sum + r.avgMemoryUsageMB, 0) / results.resource.length;
    console.log(`${colors.cyan}Resource Tests:${colors.reset} ${results.resource.length} opérations analysées, CPU moyen: ${avgCpu.toFixed(2)}%, Mémoire moyenne: ${avgMem.toFixed(2)}MB`);
  }
  
  if (results.frontend.length > 0) {
    const avgScore = results.frontend.reduce((sum, r) => sum + (r.performanceScore || 0), 0) / results.frontend.length;
    console.log(`${colors.cyan}Frontend Tests:${colors.reset} ${results.frontend.length} pages testées, score moyen: ${avgScore.toFixed(1)}/100`);
  }
  
  // Afficher les problèmes détectés
  const apiAnalysis = analyzeApiResults(results.api);
  const loadAnalysis = analyzeLoadResults(results.load);
  const resourceAnalysis = analyzeResourceResults(results.resource);
  const frontendAnalysis = analyzeFrontendResults(results.frontend);
  
  const allIssues = [
    ...apiAnalysis.issues.map(i => `API: ${i}`),
    ...loadAnalysis.issues.map(i => `Load: ${i}`),
    ...resourceAnalysis.issues.map(i => `Resource: ${i}`),
    ...frontendAnalysis.issues.map(i => `Frontend: ${i}`)
  ];
  
  if (allIssues.length > 0) {
    console.log(`\n${colors.yellow}Problèmes détectés:${colors.reset}`);
    allIssues.forEach(issue => console.log(`- ${issue}`));
  }
  
  // Afficher les recommandations principales
  const allRecommendations = [
    ...apiAnalysis.recommendations,
    ...loadAnalysis.recommendations,
    ...resourceAnalysis.recommendations,
    ...frontendAnalysis.recommendations
  ];
  
  if (allRecommendations.length > 0) {
    console.log(`\n${colors.green}Recommandations:${colors.reset}`);
    
    // Dédupliquer les recommandations
    const uniqueRecommendations = [...new Set(allRecommendations)];
    uniqueRecommendations.forEach(rec => console.log(`- ${rec}`));
  }
  
  console.log(`\n${colors.green}Tests de performance terminés.${colors.reset}`);
}

// Exécuter la fonction principale
main().catch(error => {
  console.error(`${colors.red}Erreur lors de l'exécution des tests de performance: ${error.message}${colors.reset}`);
  process.exit(1);
}); 