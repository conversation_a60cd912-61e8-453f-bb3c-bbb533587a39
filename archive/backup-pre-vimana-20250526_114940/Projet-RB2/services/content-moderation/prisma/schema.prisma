// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modèle pour les règles de modération de texte
model TextModerationRule {
  id          String   @id @default(uuid())
  name        String
  description String?
  pattern     String   // Regex ou mot-clé
  severity    Severity
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Modèle pour les règles de modération d'images
model ImageModerationRule {
  id          String   @id @default(uuid())
  name        String
  description String?
  category    String   // Type de contenu à détecter
  threshold   Float    // Seuil de confiance
  severity    Severity
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Modèle pour les signalements
model Report {
  id              String           @id @default(uuid())
  contentType     ContentType
  contentId       String
  reporterId      String
  reason          String
  description     String?
  status          ReportStatus     @default(PENDING)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  moderationActions ModerationAction[]
}

// Modèle pour les actions de modération
model ModerationAction {
  id          String       @id @default(uuid())
  report      Report       @relation(fields: [reportId], references: [id])
  reportId    String
  moderatorId String
  action      ActionType
  comment     String?
  createdAt   DateTime     @default(now())
}

// Modèle pour la réputation des utilisateurs
model UserReputation {
  id              String   @id @default(uuid())
  userId          String   @unique
  reputationScore Int      @default(0)
  isTrusted       Boolean  @default(false)
  privileges      String[] // Liste des privilèges basés sur la réputation
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

// Modèle pour les métriques de modération
model ModerationMetric {
  id                String   @id @default(uuid())
  date              DateTime @default(now())
  totalReports      Int      @default(0)
  processedReports  Int      @default(0)
  approvedReports   Int      @default(0)
  rejectedReports   Int      @default(0)
  averageProcessTime Float?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

// Énumérations
enum Severity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ContentType {
  TEXT
  IMAGE
  VIDEO
  AUDIO
  RETREAT
  PROFILE
  COMMENT
  MESSAGE
}

enum ReportStatus {
  PENDING
  IN_REVIEW
  APPROVED
  REJECTED
  ESCALATED
}

enum ActionType {
  APPROVE
  REJECT
  ESCALATE
  WARN_USER
  BAN_USER
  DELETE_CONTENT
  HIDE_CONTENT
}
