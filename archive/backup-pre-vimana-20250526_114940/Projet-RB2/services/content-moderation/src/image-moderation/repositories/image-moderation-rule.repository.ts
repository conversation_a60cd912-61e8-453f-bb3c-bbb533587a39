import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ImageModerationRule, Severity } from '@prisma/client';

@Injectable()
export class ImageModerationRuleRepository {
  private readonly logger = new Logger(ImageModerationRuleRepository.name);

  constructor(private readonly prisma: PrismaService) {}

  async findAll(): Promise<ImageModerationRule[]> {
    return this.prisma.imageModerationRule.findMany({
      where: { isActive: true },
    });
  }

  async findById(id: string): Promise<ImageModerationRule | null> {
    return this.prisma.imageModerationRule.findUnique({
      where: { id },
    });
  }

  async create(data: {
    name: string;
    description?: string;
    category: string;
    threshold: number;
    severity: Severity;
    isActive?: boolean;
  }): Promise<ImageModerationRule> {
    return this.prisma.imageModerationRule.create({
      data,
    });
  }

  async update(
    id: string,
    data: {
      name?: string;
      description?: string;
      category?: string;
      threshold?: number;
      severity?: Severity;
      isActive?: boolean;
    },
  ): Promise<ImageModerationRule> {
    return this.prisma.imageModerationRule.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<ImageModerationRule> {
    return this.prisma.imageModerationRule.delete({
      where: { id },
    });
  }

  async findBySeverity(severity: Severity): Promise<ImageModerationRule[]> {
    return this.prisma.imageModerationRule.findMany({
      where: { severity, isActive: true },
    });
  }

  async findByCategory(category: string): Promise<ImageModerationRule[]> {
    return this.prisma.imageModerationRule.findMany({
      where: { category, isActive: true },
    });
  }
}
