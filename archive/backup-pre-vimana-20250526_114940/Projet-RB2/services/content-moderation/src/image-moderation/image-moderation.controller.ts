import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Put,
  Delete,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ImageModerationService } from './image-moderation.service';
import { ModerateImageDto } from './dto/moderate-image.dto';
import { CreateImageRuleDto } from './dto/create-image-rule.dto';
import { UpdateImageRuleDto } from './dto/update-image-rule.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('image-moderation')
@Controller('moderation/image')
export class ImageModerationController {
  private readonly logger = new Logger(ImageModerationController.name);

  constructor(private readonly imageModerationService: ImageModerationService) {}

  @Post()
  @ApiOperation({ summary: 'Modérer une image' })
  @ApiResponse({
    status: 200,
    description: 'Résultat de la modération de l\'image',
  })
  async moderateImage(@Body() moderateImageDto: ModerateImageDto) {
    this.logger.debug(`Received request to moderate image: ${moderateImageDto.imageUrl || 'base64 image'}`);
    return this.imageModerationService.moderateImage(moderateImageDto);
  }

  @Get('rules')
  @ApiOperation({ summary: 'Obtenir toutes les règles de modération d\'images' })
  @ApiResponse({
    status: 200,
    description: 'Liste des règles de modération d\'images',
  })
  async getRules() {
    return this.imageModerationService.getRules();
  }

  @Post('rules')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer une nouvelle règle de modération d\'images' })
  @ApiResponse({
    status: 201,
    description: 'La règle a été créée avec succès',
  })
  async createRule(@Body() createRuleDto: CreateImageRuleDto) {
    return this.imageModerationService.createRule(createRuleDto);
  }

  @Put('rules/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour une règle de modération d\'images' })
  @ApiResponse({
    status: 200,
    description: 'La règle a été mise à jour avec succès',
  })
  async updateRule(
    @Param('id') id: string,
    @Body() updateRuleDto: UpdateImageRuleDto,
  ) {
    return this.imageModerationService.updateRule(id, updateRuleDto);
  }

  @Delete('rules/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprimer une règle de modération d\'images' })
  @ApiResponse({
    status: 200,
    description: 'La règle a été supprimée avec succès',
  })
  async deleteRule(@Param('id') id: string) {
    return this.imageModerationService.deleteRule(id);
  }
}
