import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ReportRepository } from './repositories/report.repository';
import { CreateReportDto } from './dto/create-report.dto';
import { UpdateReportDto } from './dto/update-report.dto';
import { FilterReportsDto } from './dto/filter-reports.dto';
import { AddModerationActionDto } from './dto/add-moderation-action.dto';
import { ReportStatus, ContentType, ActionType } from '@prisma/client';
import { TextModerationService } from '../text-moderation/text-moderation.service';
import { ImageModerationService } from '../image-moderation/image-moderation.service';

@Injectable()
export class ReportsService {
  private readonly logger = new Logger(ReportsService.name);

  constructor(
    private readonly reportRepository: ReportRepository,
    private readonly textModerationService: TextModerationService,
    private readonly imageModerationService: ImageModerationService,
  ) {}

  async findAll(filters: FilterReportsDto) {
    this.logger.debug(`Finding reports with filters: ${JSON.stringify(filters)}`);
    return this.reportRepository.findAll(
      {
        status: filters.status,
        contentType: filters.contentType,
        reporterId: filters.reporterId,
      },
      {
        skip: filters.skip,
        take: filters.take,
      },
    );
  }

  async findById(id: string) {
    const report = await this.reportRepository.findById(id);
    if (!report) {
      throw new NotFoundException(`Report with ID ${id} not found`);
    }
    return report;
  }

  async create(createReportDto: CreateReportDto) {
    this.logger.debug(`Creating report: ${JSON.stringify(createReportDto)}`);
    
    // Créer le signalement
    const report = await this.reportRepository.create(createReportDto);
    
    // Vérifier automatiquement le contenu signalé si c'est du texte ou une image
    if (createReportDto.contentType === ContentType.TEXT) {
      // Simuler la récupération du texte depuis un service externe
      const text = `Ceci est un exemple de texte pour le contenu ${createReportDto.contentId}`;
      
      // Modérer le texte
      const moderationResult = await this.textModerationService.moderateText(text);
      
      // Si le texte est inapproprié, mettre à jour le statut du signalement
      if (moderationResult.isInappropriate) {
        await this.reportRepository.updateStatus(report.id, ReportStatus.APPROVED);
        
        // Ajouter une action de modération automatique
        await this.reportRepository.addModerationAction(
          report.id,
          'system',
          ActionType.APPROVE,
          `Contenu automatiquement détecté comme inapproprié avec une confiance de ${moderationResult.confidence}`,
        );
      }
    } else if (createReportDto.contentType === ContentType.IMAGE) {
      // Simuler la récupération de l'URL de l'image depuis un service externe
      const imageUrl = `https://example.com/images/${createReportDto.contentId}.jpg`;
      
      // Modérer l'image
      const moderationResult = await this.imageModerationService.moderateImage({ imageUrl });
      
      // Si l'image est inappropriée, mettre à jour le statut du signalement
      if (moderationResult.isInappropriate) {
        await this.reportRepository.updateStatus(report.id, ReportStatus.APPROVED);
        
        // Ajouter une action de modération automatique
        await this.reportRepository.addModerationAction(
          report.id,
          'system',
          ActionType.APPROVE,
          `Contenu automatiquement détecté comme inapproprié avec une confiance de ${moderationResult.confidence}`,
        );
      }
    }
    
    return this.reportRepository.findById(report.id);
  }

  async update(id: string, updateReportDto: UpdateReportDto) {
    await this.findById(id); // Vérifier que le signalement existe
    return this.reportRepository.update(id, updateReportDto);
  }

  async updateStatus(id: string, status: ReportStatus) {
    await this.findById(id); // Vérifier que le signalement existe
    return this.reportRepository.updateStatus(id, status);
  }

  async addModerationAction(id: string, addModerationActionDto: AddModerationActionDto) {
    await this.findById(id); // Vérifier que le signalement existe
    
    const { moderatorId, action, comment } = addModerationActionDto;
    
    // Ajouter l'action de modération
    const moderationAction = await this.reportRepository.addModerationAction(
      id,
      moderatorId,
      action,
      comment,
    );
    
    // Mettre à jour le statut du signalement en fonction de l'action
    let newStatus: ReportStatus;
    
    switch (action) {
      case ActionType.APPROVE:
        newStatus = ReportStatus.APPROVED;
        break;
      case ActionType.REJECT:
        newStatus = ReportStatus.REJECTED;
        break;
      case ActionType.ESCALATE:
        newStatus = ReportStatus.ESCALATED;
        break;
      default:
        newStatus = ReportStatus.IN_REVIEW;
    }
    
    await this.reportRepository.updateStatus(id, newStatus);
    
    return moderationAction;
  }

  async getModerationActions(id: string) {
    await this.findById(id); // Vérifier que le signalement existe
    return this.reportRepository.getModerationActions(id);
  }

  async getReportsByContentId(contentType: ContentType, contentId: string) {
    return this.reportRepository.getReportsByContentId(contentType, contentId);
  }

  async getReportStats() {
    return this.reportRepository.getReportStats();
  }
}
