import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ReportsService } from './reports.service';
import { CreateReportDto } from './dto/create-report.dto';
import { UpdateReportDto } from './dto/update-report.dto';
import { FilterReportsDto } from './dto/filter-reports.dto';
import { AddModerationActionDto } from './dto/add-moderation-action.dto';
import { ContentType } from '@prisma/client';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('reports')
@Controller('reports')
export class ReportsController {
  private readonly logger = new Logger(ReportsController.name);

  constructor(private readonly reportsService: ReportsService) {}

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir la liste des signalements' })
  @ApiResponse({
    status: 200,
    description: 'Liste des signalements',
  })
  async findAll(@Query() filters: FilterReportsDto) {
    return this.reportsService.findAll(filters);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir un signalement par son ID' })
  @ApiResponse({
    status: 200,
    description: 'Le signalement',
  })
  @ApiResponse({
    status: 404,
    description: 'Signalement non trouvé',
  })
  async findOne(@Param('id') id: string) {
    return this.reportsService.findById(id);
  }

  @Post()
  @ApiOperation({ summary: 'Créer un nouveau signalement' })
  @ApiResponse({
    status: 201,
    description: 'Le signalement a été créé avec succès',
  })
  async create(@Body() createReportDto: CreateReportDto) {
    return this.reportsService.create(createReportDto);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour un signalement' })
  @ApiResponse({
    status: 200,
    description: 'Le signalement a été mis à jour avec succès',
  })
  @ApiResponse({
    status: 404,
    description: 'Signalement non trouvé',
  })
  async update(
    @Param('id') id: string,
    @Body() updateReportDto: UpdateReportDto,
  ) {
    return this.reportsService.update(id, updateReportDto);
  }

  @Post(':id/actions')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Ajouter une action de modération à un signalement' })
  @ApiResponse({
    status: 201,
    description: 'L\'action de modération a été ajoutée avec succès',
  })
  @ApiResponse({
    status: 404,
    description: 'Signalement non trouvé',
  })
  async addModerationAction(
    @Param('id') id: string,
    @Body() addModerationActionDto: AddModerationActionDto,
  ) {
    return this.reportsService.addModerationAction(id, addModerationActionDto);
  }

  @Get(':id/actions')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les actions de modération d\'un signalement' })
  @ApiResponse({
    status: 200,
    description: 'Liste des actions de modération',
  })
  @ApiResponse({
    status: 404,
    description: 'Signalement non trouvé',
  })
  async getModerationActions(@Param('id') id: string) {
    return this.reportsService.getModerationActions(id);
  }

  @Get('content/:type/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les signalements pour un contenu spécifique' })
  @ApiResponse({
    status: 200,
    description: 'Liste des signalements pour le contenu',
  })
  async getReportsByContentId(
    @Param('type') type: ContentType,
    @Param('id') id: string,
  ) {
    return this.reportsService.getReportsByContentId(type, id);
  }

  @Get('stats/summary')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les statistiques des signalements' })
  @ApiResponse({
    status: 200,
    description: 'Statistiques des signalements',
  })
  async getReportStats() {
    return this.reportsService.getReportStats();
  }
}
