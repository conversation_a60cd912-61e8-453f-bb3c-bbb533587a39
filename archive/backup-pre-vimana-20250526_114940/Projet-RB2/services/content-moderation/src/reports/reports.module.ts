import { Module } from '@nestjs/common';
import { ReportsService } from './reports.service';
import { ReportsController } from './reports.controller';
import { ReportRepository } from './repositories/report.repository';
import { TextModerationModule } from '../text-moderation/text-moderation.module';
import { ImageModerationModule } from '../image-moderation/image-moderation.module';

@Module({
  imports: [TextModerationModule, ImageModerationModule],
  controllers: [ReportsController],
  providers: [ReportsService, ReportRepository],
  exports: [ReportsService],
})
export class ReportsModule {}
