import { IsString, IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ReportStatus } from '@prisma/client';

export class UpdateReportDto {
  @ApiProperty({
    description: 'Statut du signalement',
    enum: ReportStatus,
    example: ReportStatus.IN_REVIEW,
    required: false,
  })
  @IsEnum(ReportStatus)
  @IsOptional()
  status?: ReportStatus;

  @ApiProperty({
    description: 'Description détaillée du signalement',
    example: 'Ce contenu contient des insultes et des propos haineux',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
