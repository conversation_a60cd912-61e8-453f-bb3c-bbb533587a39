import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { TextModerationRule, Severity } from '@prisma/client';

@Injectable()
export class TextModerationRuleRepository {
  private readonly logger = new Logger(TextModerationRuleRepository.name);

  constructor(private readonly prisma: PrismaService) {}

  async findAll(): Promise<TextModerationRule[]> {
    return this.prisma.textModerationRule.findMany({
      where: { isActive: true },
    });
  }

  async findById(id: string): Promise<TextModerationRule | null> {
    return this.prisma.textModerationRule.findUnique({
      where: { id },
    });
  }

  async create(data: {
    name: string;
    description?: string;
    pattern: string;
    severity: Severity;
    isActive?: boolean;
  }): Promise<TextModerationRule> {
    return this.prisma.textModerationRule.create({
      data,
    });
  }

  async update(
    id: string,
    data: {
      name?: string;
      description?: string;
      pattern?: string;
      severity?: Severity;
      isActive?: boolean;
    },
  ): Promise<TextModerationRule> {
    return this.prisma.textModerationRule.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<TextModerationRule> {
    return this.prisma.textModerationRule.delete({
      where: { id },
    });
  }

  async findBySeverity(severity: Severity): Promise<TextModerationRule[]> {
    return this.prisma.textModerationRule.findMany({
      where: { severity, isActive: true },
    });
  }
}
