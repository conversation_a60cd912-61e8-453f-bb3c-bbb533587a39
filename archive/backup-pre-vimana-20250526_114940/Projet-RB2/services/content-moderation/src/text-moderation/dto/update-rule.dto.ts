import { IsString, IsEnum, IsOptional, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Severity } from '@prisma/client';

export class UpdateRuleDto {
  @ApiProperty({
    description: 'Nom de la règle',
    example: 'Détection d\'insultes',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Description de la règle',
    example: 'Détecte les insultes courantes en français et en anglais',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Pattern (regex ou mot-clé) pour la détection',
    example: '\\b(insulte|idiot|stupide)\\b',
    required: false,
  })
  @IsString()
  @IsOptional()
  pattern?: string;

  @ApiProperty({
    description: 'Sévérité de la règle',
    enum: Severity,
    example: Severity.MEDIUM,
    required: false,
  })
  @IsEnum(Severity)
  @IsOptional()
  severity?: Severity;

  @ApiProperty({
    description: 'Indique si la règle est active',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
