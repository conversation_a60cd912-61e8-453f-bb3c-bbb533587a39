import { Module } from '@nestjs/common';
import { TextModerationService } from './text-moderation.service';
import { TextModerationController } from './text-moderation.controller';
import { TextModerationRuleRepository } from './repositories/text-moderation-rule.repository';

@Module({
  controllers: [TextModerationController],
  providers: [TextModerationService, TextModerationRuleRepository],
  exports: [TextModerationService],
})
export class TextModerationModule {}
