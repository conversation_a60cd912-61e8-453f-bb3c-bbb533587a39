import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosRequestConfig } from 'axios';

@Injectable()
export class ExternalServiceClient {
  private readonly logger = new Logger(ExternalServiceClient.name);
  private readonly userServiceUrl: string;
  private readonly contentServiceUrl: string;
  private readonly notificationServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.userServiceUrl = this.configService.get<string>('USER_SERVICE_URL');
    this.contentServiceUrl = this.configService.get<string>('CONTENT_SERVICE_URL');
    this.notificationServiceUrl = this.configService.get<string>('NOTIFICATION_SERVICE_URL');
  }

  async getUserById(userId: string, token?: string): Promise<any> {
    try {
      const config: AxiosRequestConfig = {};
      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }

      const response = await firstValueFrom(
        this.httpService.get(`${this.userServiceUrl}/users/${userId}`, config),
      );
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to get user by ID: ${error.message}`);
      throw error;
    }
  }

  async getContentById(contentType: string, contentId: string, token?: string): Promise<any> {
    try {
      const config: AxiosRequestConfig = {};
      if (token) {
        config.headers = {
          Authorization: `Bearer ${token}`,
        };
      }

      const response = await firstValueFrom(
        this.httpService.get(`${this.contentServiceUrl}/${contentType}/${contentId}`, config),
      );
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to get content by ID: ${error.message}`);
      throw error;
    }
  }

  async updateContentStatus(contentType: string, contentId: string, status: string, token: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.patch(
          `${this.contentServiceUrl}/${contentType}/${contentId}/status`,
          { status },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        ),
      );
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to update content status: ${error.message}`);
      throw error;
    }
  }

  async sendNotification(userId: string, notification: any, token: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.notificationServiceUrl}/notifications`,
          {
            userId,
            ...notification,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        ),
      );
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to send notification: ${error.message}`);
      throw error;
    }
  }
}
