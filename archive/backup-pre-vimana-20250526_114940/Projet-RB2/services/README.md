# Services RB2

## Vue d'ensemble
Ce dossier contient tous les microservices de la plateforme RB2. Chaque service est conçu pour être déployé de manière indépendante tout en partageant des composants communs via le dossier `shared`.

## Services

### Analyzer Service (`/services/analyzer`)
Service d'analyse de code avec :
- Détection de problèmes de sécurité
- Analyse de performance
- Vérification de qualité de code
- Intégration CI/CD

### Security Service (`/services/security`)
Service de sécurité fournissant :
- Authentification JWT
- Autorisation RBAC
- Détection de menaces
- Audit de sécurité

### AI Agent Service (`/services/ai-agent`)
Service d'intelligence artificielle offrant :
- Analyse prédictive
- Détection de patterns
- Optimisation automatique
- Apprentissage continu

## Composants Partagés (`/services/shared`)
- Utils : Fonctions utilitaires communes
- Types : Définitions TypeScript partagées
- Constants : Constantes globales
- Monitoring : Outils de télémétrie

## Développement

### Installation
```bash
# Installation des dépendances pour tous les services
./scripts/install-all.sh

# Installation pour un service spécifique
cd services/<service-name>
npm install
```

### Tests
```bash
# Tests pour tous les services
./scripts/test-all.sh

# Tests pour un service spécifique
cd services/<service-name>
npm test
```

### Déploiement
```bash
# Déploiement via Helm
helm upgrade -i <service-name> ./charts/<service-name>
```
