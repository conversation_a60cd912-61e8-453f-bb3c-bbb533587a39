// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modèle pour les métriques d'engagement
model EngagementMetric {
  id            String         @id @default(uuid())
  creatorId     String
  contentId     String
  contentType   ContentType
  views         Int            @default(0)
  likes         Int            @default(0)
  comments      Int            @default(0)
  shares        Int            @default(0)
  bookmarks     Int            @default(0)
  clickThroughs Int            @default(0)
  date          DateTime       @default(now())
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@index([creatorId])
  @@index([contentId])
  @@index([date])
}

// Modèle pour les métriques d'audience
model AudienceMetric {
  id                String         @id @default(uuid())
  creatorId         String
  totalFollowers    Int            @default(0)
  newFollowers      Int            @default(0)
  lostFollowers     Int            @default(0)
  activeFollowers   Int            @default(0)
  demographics      Json?          // Données démographiques (âge, genre, localisation)
  date              DateTime       @default(now())
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt

  @@index([creatorId])
  @@index([date])
}

// Modèle pour les métriques de revenus
model RevenueMetric {
  id                String         @id @default(uuid())
  creatorId         String
  contentId         String?
  amount            Float          @default(0)
  currency          String         @default("EUR")
  source            RevenueSource
  date              DateTime       @default(now())
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt

  @@index([creatorId])
  @@index([contentId])
  @@index([date])
}

// Modèle pour les tableaux de bord personnalisés
model Dashboard {
  id            String         @id @default(uuid())
  creatorId     String
  name          String
  description   String?
  layout        Json           // Configuration du layout
  widgets       Widget[]       // Widgets du tableau de bord
  isDefault     Boolean        @default(false)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@index([creatorId])
}

// Modèle pour les widgets des tableaux de bord
model Widget {
  id            String         @id @default(uuid())
  dashboard     Dashboard      @relation(fields: [dashboardId], references: [id], onDelete: Cascade)
  dashboardId   String
  type          WidgetType
  title         String
  config        Json           // Configuration du widget
  position      Json           // Position dans le tableau de bord
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
}

// Modèle pour les prévisions
model Forecast {
  id            String         @id @default(uuid())
  creatorId     String
  contentId     String?
  metricType    MetricType
  predictions   Json           // Valeurs prédites
  confidence    Float          // Niveau de confiance
  startDate     DateTime       // Date de début de la prévision
  endDate       DateTime       // Date de fin de la prévision
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@index([creatorId])
  @@index([contentId])
}

// Modèle pour les benchmarks
model Benchmark {
  id            String         @id @default(uuid())
  category      String         // Catégorie du benchmark
  metricType    MetricType
  value         Float          // Valeur du benchmark
  percentile    Float?         // Percentile (optionnel)
  date          DateTime       @default(now())
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@index([category])
  @@index([metricType])
  @@index([date])
}

// Modèle pour les recommandations
model Recommendation {
  id            String              @id @default(uuid())
  creatorId     String
  type          RecommendationType
  title         String
  description   String
  impact        Float               // Impact estimé
  difficulty    DifficultyLevel
  isImplemented Boolean             @default(false)
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt

  @@index([creatorId])
  @@index([type])
}

// Énumérations
enum ContentType {
  RETREAT
  POST
  VIDEO
  ARTICLE
  STORY
  LIVE
  COMMENT
}

enum RevenueSource {
  SUBSCRIPTION
  DONATION
  PRODUCT_SALE
  AFFILIATE
  ADVERTISING
  SPONSORSHIP
}

enum WidgetType {
  LINE_CHART
  BAR_CHART
  PIE_CHART
  COUNTER
  TABLE
  HEATMAP
  MAP
  GAUGE
  CUSTOM
}

enum MetricType {
  VIEWS
  LIKES
  COMMENTS
  SHARES
  BOOKMARKS
  CLICK_THROUGH_RATE
  FOLLOWERS
  ENGAGEMENT_RATE
  REVENUE
  CONVERSION_RATE
}

enum RecommendationType {
  CONTENT
  TIMING
  ENGAGEMENT
  MONETIZATION
  AUDIENCE
}

enum DifficultyLevel {
  EASY
  MEDIUM
  HARD
}
