# Application
NODE_ENV=development
PORT=3020
API_PREFIX=api
APP_NAME=creator-analytics

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/creator_analytics?schema=public

# Authentication
JWT_SECRET=your-jwt-secret
JWT_EXPIRATION=1d
JWT_REFRESH_SECRET=your-refresh-secret
JWT_REFRESH_EXPIRATION=7d

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Logging
LOG_LEVEL=debug

# External Services
USER_SERVICE_URL=http://localhost:3000/api
CONTENT_SERVICE_URL=http://localhost:3001/api
EVENT_SERVICE_URL=http://localhost:3002/api
RECOMMENDATION_SERVICE_URL=http://localhost:3003/api

# Python Analytics Service
PYTHON_ANALYTICS_SERVICE_URL=http://localhost:5000

# Data Collection
DATA_COLLECTION_INTERVAL=3600000 # 1 hour in milliseconds

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100
