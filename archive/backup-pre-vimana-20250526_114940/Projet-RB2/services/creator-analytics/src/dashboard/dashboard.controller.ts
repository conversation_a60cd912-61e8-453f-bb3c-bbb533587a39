import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../../Backend-NestJS/src/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../../Backend-NestJS/src/modules/auth/guards/roles.guard';
import { Roles } from '../../../../Backend-NestJS/src/common/decorators/roles.decorator';
import { DashboardService } from './dashboard.service';
import { FilterAnalyticsDto } from '../dto/filter-analytics.dto';
import { DashboardConfigDto } from '../dto/dashboard-config.dto';

@ApiTags('creator-analytics-dashboard')
@Controller('creator-analytics/dashboard')
export class DashboardController {
  private readonly logger = new Logger(DashboardController.name);

  constructor(private readonly dashboardService: DashboardService) {}

  @Get('overview/:creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get analytics overview for a creator' })
  @ApiResponse({
    status: 200,
    description: 'Returns analytics overview',
  })
  async getOverview(
    @Param('creatorId') creatorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.debug(`Getting analytics overview for creator: ${creatorId}`);
    return this.dashboardService.getOverview(
      creatorId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('audience/:creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get audience analytics for a creator' })
  @ApiResponse({
    status: 200,
    description: 'Returns audience analytics',
  })
  async getAudienceAnalytics(
    @Param('creatorId') creatorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.debug(`Getting audience analytics for creator: ${creatorId}`);
    return this.dashboardService.getAudienceAnalytics(
      creatorId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('content/:creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get content performance analytics for a creator' })
  @ApiResponse({
    status: 200,
    description: 'Returns content performance analytics',
  })
  async getContentAnalytics(
    @Param('creatorId') creatorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.debug(`Getting content analytics for creator: ${creatorId}`);
    return this.dashboardService.getContentAnalytics(
      creatorId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('engagement/:creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get engagement analytics for a creator' })
  @ApiResponse({
    status: 200,
    description: 'Returns engagement analytics',
  })
  async getEngagementAnalytics(
    @Param('creatorId') creatorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.debug(`Getting engagement analytics for creator: ${creatorId}`);
    return this.dashboardService.getEngagementAnalytics(
      creatorId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('revenue/:creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get revenue analytics for a creator' })
  @ApiResponse({
    status: 200,
    description: 'Returns revenue analytics',
  })
  async getRevenueAnalytics(
    @Param('creatorId') creatorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.debug(`Getting revenue analytics for creator: ${creatorId}`);
    return this.dashboardService.getRevenueAnalytics(
      creatorId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('custom/:creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get custom analytics for a creator' })
  @ApiQuery({ name: 'metrics', required: true, type: [String] })
  @ApiResponse({
    status: 200,
    description: 'Returns custom analytics',
  })
  async getCustomAnalytics(
    @Param('creatorId') creatorId: string,
    @Query('metrics') metrics: string[],
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query() filterDto?: FilterAnalyticsDto,
  ) {
    this.logger.debug(`Getting custom analytics for creator: ${creatorId}`);
    return this.dashboardService.getCustomAnalytics(
      creatorId,
      Array.isArray(metrics) ? metrics : [metrics],
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      filterDto,
    );
  }

  @Post('config/:creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Save dashboard configuration for a creator' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard configuration saved successfully',
  })
  async saveDashboardConfig(
    @Param('creatorId') creatorId: string,
    @Body() configDto: DashboardConfigDto,
  ) {
    this.logger.debug(`Saving dashboard config for creator: ${creatorId}`);
    return this.dashboardService.saveDashboardConfig(creatorId, configDto);
  }

  @Get('config/:creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get dashboard configuration for a creator' })
  @ApiResponse({
    status: 200,
    description: 'Returns dashboard configuration',
  })
  async getDashboardConfig(@Param('creatorId') creatorId: string) {
    this.logger.debug(`Getting dashboard config for creator: ${creatorId}`);
    return this.dashboardService.getDashboardConfig(creatorId);
  }

  @Get('export/:creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Export analytics data for a creator' })
  @ApiQuery({ name: 'format', enum: ['csv', 'json', 'pdf'], required: true })
  @ApiQuery({ name: 'metrics', required: false, type: [String] })
  @ApiResponse({
    status: 200,
    description: 'Returns analytics data in the requested format',
  })
  async exportAnalytics(
    @Param('creatorId') creatorId: string,
    @Query('format') format: 'csv' | 'json' | 'pdf',
    @Query('metrics') metrics?: string[],
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.debug(`Exporting analytics for creator: ${creatorId} in ${format} format`);
    return this.dashboardService.exportAnalytics(
      creatorId,
      format,
      Array.isArray(metrics) ? metrics : metrics ? [metrics] : undefined,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }
}
