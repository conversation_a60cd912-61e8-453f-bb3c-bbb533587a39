import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsDate, IsOptional, IsArray, ValidateNested, IsEnum, IsObject } from 'class-validator';
import { Type } from 'class-transformer';

export enum TrendDirection {
  INCREASING = 'increasing',
  DECREASING = 'decreasing',
  STABLE = 'stable',
}

export class PeriodDto {
  @ApiProperty({ description: 'Date de début de la période' })
  @IsDate()
  @Type(() => Date)
  start: Date;

  @ApiProperty({ description: 'Date de fin de la période' })
  @IsDate()
  @Type(() => Date)
  end: Date;
}

export class TrendResultDto {
  @ApiProperty({ description: 'Métrique analysée' })
  @IsString()
  metric: string;

  @ApiProperty({ description: 'Direction de la tendance', enum: TrendDirection })
  @IsEnum(TrendDirection)
  trend: TrendDirection;

  @ApiProperty({ description: 'Taux de changement' })
  @IsNumber()
  changeRate: number;

  @ApiProperty({ description: 'Significativité de la tendance (0-1)', minimum: 0, maximum: 1 })
  @IsNumber()
  significance: number;

  @ApiProperty({ description: 'Période d\'analyse', type: PeriodDto })
  @ValidateNested()
  @Type(() => PeriodDto)
  period: PeriodDto;
}

export class DailyPatternDto {
  @ApiProperty({ description: 'Motif horaire', type: Object })
  @IsObject()
  pattern: Record<string, number>;

  @ApiProperty({ description: 'Heures de pointe', type: [Number] })
  @IsArray()
  peakHours: number[];

  @ApiProperty({ description: 'Heures creuses', type: [Number] })
  @IsArray()
  lowHours: number[];
}

export class WeeklyPatternDto {
  @ApiProperty({ description: 'Motif journalier', type: Object })
  @IsObject()
  pattern: Record<string, number>;

  @ApiProperty({ description: 'Jours de pointe', type: [Number] })
  @IsArray()
  peakDays: number[];

  @ApiProperty({ description: 'Jours creux', type: [Number] })
  @IsArray()
  lowDays: number[];
}

export class SeasonalityResultDto {
  @ApiProperty({ description: 'Métrique analysée' })
  @IsString()
  metric: string;

  @ApiPropertyOptional({ description: 'Motif quotidien', type: DailyPatternDto })
  @ValidateNested()
  @Type(() => DailyPatternDto)
  @IsOptional()
  daily?: DailyPatternDto;

  @ApiPropertyOptional({ description: 'Motif hebdomadaire', type: WeeklyPatternDto })
  @ValidateNested()
  @Type(() => WeeklyPatternDto)
  @IsOptional()
  weekly?: WeeklyPatternDto;

  @ApiPropertyOptional({ description: 'Motif mensuel', type: Object })
  @IsObject()
  @IsOptional()
  monthly?: {
    pattern: Record<string, number>;
    peakDays: number[];
    lowDays: number[];
  };
}

export class EngagementTrendsResponseDto {
  @ApiProperty({ description: 'ID du créateur' })
  @IsString()
  creatorId: string;

  @ApiProperty({ description: 'Tendances d\'engagement', type: [TrendResultDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TrendResultDto)
  trends: TrendResultDto[];

  @ApiProperty({ description: 'Période d\'analyse' })
  @IsObject()
  period: {
    startDate: Date | string;
    endDate: Date | string;
  };

  @ApiProperty({ description: 'Date d\'analyse' })
  @IsDate()
  @Type(() => Date)
  analyzedAt: Date;
}

export class SeasonalityAnalysisResponseDto {
  @ApiProperty({ description: 'ID du créateur' })
  @IsString()
  creatorId: string;

  @ApiProperty({ description: 'Analyse de saisonnalité', type: [SeasonalityResultDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SeasonalityResultDto)
  seasonality: SeasonalityResultDto[];

  @ApiProperty({ description: 'Période d\'analyse' })
  @IsObject()
  period: {
    startDate: Date | string;
    endDate: Date | string;
  };

  @ApiProperty({ description: 'Date d\'analyse' })
  @IsDate()
  @Type(() => Date)
  analyzedAt: Date;
}

export class TrendQueryParamsDto {
  @ApiPropertyOptional({ description: 'Date de début pour l\'analyse' })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional({ description: 'Date de fin pour l\'analyse' })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  endDate?: Date;
}
