import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsDate, IsOptional, IsArray, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

export enum MetricType {
  VIEWS = 'VIEWS',
  LIKES = 'LIKES',
  COMMENTS = 'COMMENTS',
  SHARES = 'SHARES',
  BOOKMARKS = 'BOOKMARKS',
  FOLLOWERS = 'FOLLOWERS',
  ENGAGEMENT_RATE = 'ENGAGEMENT_RATE',
  REVENUE = 'REVENUE',
}

export class ForecastPointDto {
  @ApiProperty({ description: 'Date de la prévision' })
  @IsDate()
  @Type(() => Date)
  date: Date;

  @ApiProperty({ description: 'Valeur prévue' })
  @IsNumber()
  value: number;

  @ApiPropertyOptional({ description: 'Borne inférieure de l\'intervalle de confiance' })
  @IsNumber()
  @IsOptional()
  lowerBound?: number;

  @ApiPropertyOptional({ description: 'Borne supérieure de l\'intervalle de confiance' })
  @IsNumber()
  @IsOptional()
  upperBound?: number;
}

export class ForecastResultDto {
  @ApiProperty({ description: 'Métrique prévue', enum: MetricType })
  @IsEnum(MetricType)
  metric: string;

  @ApiProperty({ description: 'Points de prévision', type: [ForecastPointDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ForecastPointDto)
  predictions: ForecastPointDto[];

  @ApiProperty({ description: 'Niveau de confiance de la prévision (0-1)', minimum: 0, maximum: 1 })
  @IsNumber()
  confidence: number;
}

export class EngagementForecastResponseDto {
  @ApiProperty({ description: 'ID du créateur' })
  @IsString()
  creatorId: string;

  @ApiProperty({ description: 'Prévisions d\'engagement', type: [ForecastResultDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ForecastResultDto)
  forecasts: ForecastResultDto[];

  @ApiProperty({ description: 'Date de génération des prévisions' })
  @IsDate()
  @Type(() => Date)
  generatedAt: Date;
}

export class ContentPerformanceForecastResponseDto {
  @ApiProperty({ description: 'ID du créateur' })
  @IsString()
  creatorId: string;

  @ApiProperty({ description: 'ID du contenu' })
  @IsString()
  contentId: string;

  @ApiProperty({ description: 'Prévisions de performance', type: [ForecastResultDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ForecastResultDto)
  forecasts: ForecastResultDto[];

  @ApiProperty({ description: 'Date de génération des prévisions' })
  @IsDate()
  @Type(() => Date)
  generatedAt: Date;
}

export class ForecastQueryParamsDto {
  @ApiPropertyOptional({ description: 'Nombre de jours à prévoir', minimum: 1, maximum: 90 })
  @IsNumber()
  @IsOptional()
  days?: number;

  @ApiPropertyOptional({ description: 'Date de début pour les données historiques' })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional({ description: 'Date de fin pour les données historiques' })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  endDate?: Date;
}
