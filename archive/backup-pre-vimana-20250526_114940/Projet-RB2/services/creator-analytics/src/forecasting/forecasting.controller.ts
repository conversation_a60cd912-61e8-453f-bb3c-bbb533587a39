import {
  <PERSON>,
  Get,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ForecastingService } from './forecasting.service';
import { JwtAuthGuard } from '../../../../Backend-NestJS/src/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../../Backend-NestJS/src/modules/auth/guards/roles.guard';
import { Roles } from '../../../../Backend-NestJS/src/common/decorators/roles.decorator';

@ApiTags('forecasting')
@Controller('forecasting')
export class ForecastingController {
  private readonly logger = new Logger(ForecastingController.name);

  constructor(private readonly forecastingService: ForecastingService) {}

  @Get(':creatorId/engagement')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les prévisions d\'engagement pour un créateur' })
  @ApiParam({ name: 'creatorId', description: 'ID du créateur' })
  @ApiQuery({ name: 'days', description: 'Nombre de jours à prévoir', required: false, type: Number })
  @ApiQuery({ name: 'startDate', description: 'Date de début pour les données historiques', required: false })
  @ApiQuery({ name: 'endDate', description: 'Date de fin pour les données historiques', required: false })
  @ApiResponse({
    status: 200,
    description: 'Prévisions d\'engagement générées avec succès',
  })
  async getEngagementForecasts(
    @Param('creatorId') creatorId: string,
    @Query('days') days?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      this.logger.debug(`Getting engagement forecasts for creator ${creatorId}`);
      
      const startDateObj = startDate ? new Date(startDate) : undefined;
      const endDateObj = endDate ? new Date(endDate) : undefined;
      
      const forecasts = await this.forecastingService.forecastEngagement(
        creatorId,
        days ? parseInt(days.toString()) : undefined,
        startDateObj,
        endDateObj,
      );
      
      return {
        creatorId,
        forecasts,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error generating engagement forecasts: ${error.message}`);
      throw new HttpException(
        'Failed to generate engagement forecasts',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':creatorId/content/:contentId/performance')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les prévisions de performance pour un contenu spécifique' })
  @ApiParam({ name: 'creatorId', description: 'ID du créateur' })
  @ApiParam({ name: 'contentId', description: 'ID du contenu' })
  @ApiQuery({ name: 'days', description: 'Nombre de jours à prévoir', required: false, type: Number })
  @ApiResponse({
    status: 200,
    description: 'Prévisions de performance générées avec succès',
  })
  async getContentPerformanceForecasts(
    @Param('creatorId') creatorId: string,
    @Param('contentId') contentId: string,
    @Query('days') days?: number,
  ) {
    try {
      this.logger.debug(`Getting performance forecasts for content ${contentId}`);
      
      const forecasts = await this.forecastingService.forecastContentPerformance(
        creatorId,
        contentId,
        days ? parseInt(days.toString()) : undefined,
      );
      
      return {
        creatorId,
        contentId,
        forecasts,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error generating content performance forecasts: ${error.message}`);
      throw new HttpException(
        'Failed to generate content performance forecasts',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':creatorId/trends')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les tendances d\'engagement pour un créateur' })
  @ApiParam({ name: 'creatorId', description: 'ID du créateur' })
  @ApiQuery({ name: 'startDate', description: 'Date de début pour l\'analyse', required: false })
  @ApiQuery({ name: 'endDate', description: 'Date de fin pour l\'analyse', required: false })
  @ApiResponse({
    status: 200,
    description: 'Tendances d\'engagement analysées avec succès',
  })
  async getEngagementTrends(
    @Param('creatorId') creatorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      this.logger.debug(`Getting engagement trends for creator ${creatorId}`);
      
      const startDateObj = startDate ? new Date(startDate) : undefined;
      const endDateObj = endDate ? new Date(endDate) : undefined;
      
      const trends = await this.forecastingService.analyzeEngagementTrends(
        creatorId,
        startDateObj,
        endDateObj,
      );
      
      return {
        creatorId,
        trends,
        period: {
          startDate: startDateObj || 'all',
          endDate: endDateObj || 'now',
        },
        analyzedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error analyzing engagement trends: ${error.message}`);
      throw new HttpException(
        'Failed to analyze engagement trends',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':creatorId/seasonality')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir l\'analyse de saisonnalité pour un créateur' })
  @ApiParam({ name: 'creatorId', description: 'ID du créateur' })
  @ApiQuery({ name: 'startDate', description: 'Date de début pour l\'analyse', required: false })
  @ApiQuery({ name: 'endDate', description: 'Date de fin pour l\'analyse', required: false })
  @ApiResponse({
    status: 200,
    description: 'Analyse de saisonnalité effectuée avec succès',
  })
  async getSeasonalityAnalysis(
    @Param('creatorId') creatorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      this.logger.debug(`Getting seasonality analysis for creator ${creatorId}`);
      
      const startDateObj = startDate ? new Date(startDate) : undefined;
      const endDateObj = endDate ? new Date(endDate) : undefined;
      
      const seasonality = await this.forecastingService.analyzeSeasonality(
        creatorId,
        startDateObj,
        endDateObj,
      );
      
      return {
        creatorId,
        seasonality,
        period: {
          startDate: startDateObj || 'all',
          endDate: endDateObj || 'now',
        },
        analyzedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error analyzing seasonality: ${error.message}`);
      throw new HttpException(
        'Failed to analyze seasonality',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
