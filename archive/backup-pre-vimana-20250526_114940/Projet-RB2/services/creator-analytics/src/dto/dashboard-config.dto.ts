import { ApiProperty } from '@nestjs/swagger';
import { IsA<PERSON>y, IsObject, IsString, IsEnum, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class PositionDto {
  @ApiProperty({ description: 'X position of the widget' })
  x: number;

  @ApiProperty({ description: 'Y position of the widget' })
  y: number;

  @ApiProperty({ description: 'Width of the widget' })
  w: number;

  @ApiProperty({ description: 'Height of the widget' })
  h: number;
}

export class LayoutItemDto {
  @ApiProperty({ description: 'ID of the layout item' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Type of the layout item' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Position and size of the layout item' })
  @IsObject()
  @ValidateNested()
  @Type(() => PositionDto)
  position: PositionDto;
}

export class WidgetDto {
  @ApiProperty({ description: 'ID of the widget' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Type of the widget' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Data source for the widget' })
  @IsString()
  dataSource: string;

  @ApiProperty({ description: 'Title of the widget', required: false })
  @IsString()
  title?: string;

  @ApiProperty({ description: 'Additional configuration for the widget', required: false })
  @IsObject()
  config?: any;
}

export class PreferencesDto {
  @ApiProperty({ description: 'Theme of the dashboard', enum: ['light', 'dark'] })
  @IsEnum(['light', 'dark'])
  theme: 'light' | 'dark';

  @ApiProperty({ description: 'Default date range for the dashboard', enum: ['today', 'yesterday', 'last7Days', 'last30Days', 'thisMonth', 'lastMonth', 'custom'] })
  @IsEnum(['today', 'yesterday', 'last7Days', 'last30Days', 'thisMonth', 'lastMonth', 'custom'])
  dateRange: 'today' | 'yesterday' | 'last7Days' | 'last30Days' | 'thisMonth' | 'lastMonth' | 'custom';

  @ApiProperty({ description: 'Auto-refresh interval in seconds (0 for no auto-refresh)' })
  refreshInterval: number;
}

export class DashboardConfigDto {
  @ApiProperty({ description: 'Layout of the dashboard', type: [LayoutItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LayoutItemDto)
  layout: LayoutItemDto[];

  @ApiProperty({ description: 'Widgets in the dashboard', type: [WidgetDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WidgetDto)
  widgets: WidgetDto[];

  @ApiProperty({ description: 'User preferences for the dashboard' })
  @IsObject()
  @ValidateNested()
  @Type(() => PreferencesDto)
  preferences: PreferencesDto;
}
