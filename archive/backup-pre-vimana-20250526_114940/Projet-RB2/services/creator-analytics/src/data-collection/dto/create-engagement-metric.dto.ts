import { IsString, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Int, IsOptional, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ContentType } from '@prisma/client';

export class CreateEngagementMetricDto {
  @ApiProperty({
    description: 'ID du créateur',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  creatorId: string;

  @ApiProperty({
    description: 'ID du contenu',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @IsString()
  @IsNotEmpty()
  contentId: string;

  @ApiProperty({
    description: 'Type de contenu',
    enum: ContentType,
    example: ContentType.RETREAT,
  })
  @IsEnum(ContentType)
  contentType: ContentType;

  @ApiProperty({
    description: 'Nombre de vues',
    example: 1000,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  views?: number = 0;

  @ApiProperty({
    description: 'Nombre de likes',
    example: 50,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  likes?: number = 0;

  @ApiProperty({
    description: 'Nombre de commentaires',
    example: 10,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  comments?: number = 0;

  @ApiProperty({
    description: 'Nombre de partages',
    example: 5,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  shares?: number = 0;

  @ApiProperty({
    description: 'Nombre de favoris',
    example: 20,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  bookmarks?: number = 0;

  @ApiProperty({
    description: 'Nombre de clics sur les liens',
    example: 30,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  clickThroughs?: number = 0;

  @ApiProperty({
    description: 'Date de la métrique',
    example: '2023-10-15T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  date?: Date;
}
