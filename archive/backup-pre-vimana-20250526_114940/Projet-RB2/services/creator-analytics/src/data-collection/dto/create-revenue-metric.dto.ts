import { IsString, IsNotEmpty, IsEnum, <PERSON><PERSON><PERSON>ber, IsOptional, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { RevenueSource } from '@prisma/client';

export class CreateRevenueMetricDto {
  @ApiProperty({
    description: 'ID du créateur',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  creatorId: string;

  @ApiProperty({
    description: 'ID du contenu (optionnel)',
    example: '123e4567-e89b-12d3-a456-426614174001',
    required: false,
  })
  @IsString()
  @IsOptional()
  contentId?: string;

  @ApiProperty({
    description: 'Montant du revenu',
    example: 100.50,
  })
  @IsNumber()
  @Min(0)
  amount: number;

  @ApiProperty({
    description: 'Devise',
    example: 'EUR',
    default: 'EUR',
  })
  @IsString()
  @IsOptional()
  currency?: string = 'EUR';

  @ApiProperty({
    description: 'Source du revenu',
    enum: RevenueSource,
    example: RevenueSource.SUBSCRIPTION,
  })
  @IsEnum(RevenueSource)
  source: RevenueSource;

  @ApiProperty({
    description: 'Date de la métrique',
    example: '2023-10-15T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  date?: Date;
}
