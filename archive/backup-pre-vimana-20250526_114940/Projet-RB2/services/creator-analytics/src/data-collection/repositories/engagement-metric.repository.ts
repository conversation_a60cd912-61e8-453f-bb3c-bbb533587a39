import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { EngagementMetric, ContentType } from '@prisma/client';
import { CreateEngagementMetricDto } from '../dto/create-engagement-metric.dto';

@Injectable()
export class EngagementMetricRepository {
  private readonly logger = new Logger(EngagementMetricRepository.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateEngagementMetricDto): Promise<EngagementMetric> {
    return this.prisma.engagementMetric.create({
      data,
    });
  }

  async findByCreatorId(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<EngagementMetric[]> {
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    return this.prisma.engagementMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });
  }

  async findByContentId(
    contentId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<EngagementMetric[]> {
    const where: any = { contentId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    return this.prisma.engagementMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });
  }

  async findByCreatorAndContentType(
    creatorId: string,
    contentType: ContentType,
    startDate?: Date,
    endDate?: Date,
  ): Promise<EngagementMetric[]> {
    const where: any = { creatorId, contentType };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    return this.prisma.engagementMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });
  }

  async getAggregatedMetrics(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    const metrics = await this.prisma.engagementMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });

    // Agréger les métriques
    const totalViews = metrics.reduce((sum, metric) => sum + metric.views, 0);
    const totalLikes = metrics.reduce((sum, metric) => sum + metric.likes, 0);
    const totalComments = metrics.reduce((sum, metric) => sum + metric.comments, 0);
    const totalShares = metrics.reduce((sum, metric) => sum + metric.shares, 0);
    const totalBookmarks = metrics.reduce((sum, metric) => sum + metric.bookmarks, 0);
    const totalClickThroughs = metrics.reduce((sum, metric) => sum + metric.clickThroughs, 0);

    // Calculer les métriques dérivées
    const engagementRate = totalViews > 0
      ? ((totalLikes + totalComments + totalShares) / totalViews) * 100
      : 0;
    
    const clickThroughRate = totalViews > 0
      ? (totalClickThroughs / totalViews) * 100
      : 0;

    return {
      totalViews,
      totalLikes,
      totalComments,
      totalShares,
      totalBookmarks,
      totalClickThroughs,
      engagementRate,
      clickThroughRate,
      period: {
        startDate: startDate || metrics[0]?.date,
        endDate: endDate || metrics[metrics.length - 1]?.date,
      },
    };
  }

  async getTopPerformingContent(
    creatorId: string,
    limit: number = 10,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any[]> {
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    const metrics = await this.prisma.engagementMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });

    // Agréger par contenu
    const contentMap = new Map();
    
    metrics.forEach(metric => {
      if (!contentMap.has(metric.contentId)) {
        contentMap.set(metric.contentId, {
          contentId: metric.contentId,
          contentType: metric.contentType,
          views: 0,
          likes: 0,
          comments: 0,
          shares: 0,
          bookmarks: 0,
          clickThroughs: 0,
        });
      }
      
      const content = contentMap.get(metric.contentId);
      content.views += metric.views;
      content.likes += metric.likes;
      content.comments += metric.comments;
      content.shares += metric.shares;
      content.bookmarks += metric.bookmarks;
      content.clickThroughs += metric.clickThroughs;
    });
    
    // Convertir en tableau et trier par engagement
    const contentArray = Array.from(contentMap.values());
    contentArray.forEach(content => {
      content.engagement = content.likes + content.comments + content.shares;
      content.engagementRate = content.views > 0
        ? (content.engagement / content.views) * 100
        : 0;
    });
    
    // Trier par engagement et limiter
    return contentArray
      .sort((a, b) => b.engagement - a.engagement)
      .slice(0, limit);
  }
}
