import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AudienceMetric } from '@prisma/client';
import { CreateAudienceMetricDto } from '../dto/create-audience-metric.dto';

@Injectable()
export class AudienceMetricRepository {
  private readonly logger = new Logger(AudienceMetricRepository.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateAudienceMetricDto): Promise<AudienceMetric> {
    return this.prisma.audienceMetric.create({
      data,
    });
  }

  async findByCreatorId(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<AudienceMetric[]> {
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    return this.prisma.audienceMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });
  }

  async getLatestAudienceMetric(creatorId: string): Promise<AudienceMetric | null> {
    return this.prisma.audienceMetric.findFirst({
      where: { creatorId },
      orderBy: { date: 'desc' },
    });
  }

  async getAudienceGrowth(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    const metrics = await this.prisma.audienceMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });

    if (metrics.length === 0) {
      return {
        totalFollowers: 0,
        netGrowth: 0,
        growthRate: 0,
        newFollowers: 0,
        lostFollowers: 0,
        period: {
          startDate,
          endDate,
        },
      };
    }

    const firstMetric = metrics[0];
    const lastMetric = metrics[metrics.length - 1];
    
    const totalFollowers = lastMetric.totalFollowers;
    const netGrowth = lastMetric.totalFollowers - firstMetric.totalFollowers;
    const growthRate = firstMetric.totalFollowers > 0
      ? (netGrowth / firstMetric.totalFollowers) * 100
      : 0;
    
    const newFollowers = metrics.reduce((sum, metric) => sum + metric.newFollowers, 0);
    const lostFollowers = metrics.reduce((sum, metric) => sum + metric.lostFollowers, 0);

    return {
      totalFollowers,
      netGrowth,
      growthRate,
      newFollowers,
      lostFollowers,
      period: {
        startDate: startDate || firstMetric.date,
        endDate: endDate || lastMetric.date,
      },
    };
  }

  async getAudienceDemographics(creatorId: string): Promise<any> {
    const latestMetric = await this.getLatestAudienceMetric(creatorId);
    
    if (!latestMetric || !latestMetric.demographics) {
      return {
        age: {},
        gender: {},
        location: {},
      };
    }
    
    return latestMetric.demographics;
  }
}
