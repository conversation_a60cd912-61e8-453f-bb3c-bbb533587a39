import {
  Controller,
  Post,
  Body,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { DataCollectionService } from './data-collection.service';
import { CreateEngagementMetricDto } from './dto/create-engagement-metric.dto';
import { CreateAudienceMetricDto } from './dto/create-audience-metric.dto';
import { CreateRevenueMetricDto } from './dto/create-revenue-metric.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('data-collection')
@Controller('data-collection')
export class DataCollectionController {
  private readonly logger = new Logger(DataCollectionController.name);

  constructor(private readonly dataCollectionService: DataCollectionService) {}

  @Post('engagement')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer une métrique d\'engagement' })
  @ApiResponse({
    status: 201,
    description: 'La métrique d\'engagement a été créée avec succès',
  })
  async createEngagementMetric(@Body() createEngagementMetricDto: CreateEngagementMetricDto) {
    this.logger.debug(`Creating engagement metric for creator: ${createEngagementMetricDto.creatorId}, content: ${createEngagementMetricDto.contentId}`);
    return this.dataCollectionService.createEngagementMetric(createEngagementMetricDto);
  }

  @Post('audience')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer une métrique d\'audience' })
  @ApiResponse({
    status: 201,
    description: 'La métrique d\'audience a été créée avec succès',
  })
  async createAudienceMetric(@Body() createAudienceMetricDto: CreateAudienceMetricDto) {
    this.logger.debug(`Creating audience metric for creator: ${createAudienceMetricDto.creatorId}`);
    return this.dataCollectionService.createAudienceMetric(createAudienceMetricDto);
  }

  @Post('revenue')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer une métrique de revenus' })
  @ApiResponse({
    status: 201,
    description: 'La métrique de revenus a été créée avec succès',
  })
  async createRevenueMetric(@Body() createRevenueMetricDto: CreateRevenueMetricDto) {
    this.logger.debug(`Creating revenue metric for creator: ${createRevenueMetricDto.creatorId}`);
    return this.dataCollectionService.createRevenueMetric(createRevenueMetricDto);
  }

  @Post('collect')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Déclencher manuellement la collecte de données' })
  @ApiResponse({
    status: 200,
    description: 'La collecte de données a été déclenchée avec succès',
  })
  async triggerDataCollection() {
    this.logger.debug('Manually triggering data collection');
    await this.dataCollectionService.collectData();
    return { message: 'Data collection triggered successfully' };
  }
}
