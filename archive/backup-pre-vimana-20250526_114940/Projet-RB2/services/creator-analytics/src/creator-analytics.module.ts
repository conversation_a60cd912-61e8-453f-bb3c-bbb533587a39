import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CreatorAnalyticsController } from './creator-analytics.controller';
import { CreatorAnalyticsService } from './creator-analytics.service';
import { MetricsService } from './metrics/metrics.service';
import { DataCollectionService } from './data-collection/data-collection.service';
import { DashboardController } from './dashboard/dashboard.controller';
import { DashboardService } from './dashboard/dashboard.service';
import { PrismaModule } from '../../../Backend-NestJS/src/prisma/prisma.module';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
  ],
  controllers: [
    CreatorAnalyticsController,
    DashboardController,
  ],
  providers: [
    CreatorAnalyticsService,
    MetricsService,
    DataCollectionService,
    DashboardService,
  ],
  exports: [
    CreatorAnalyticsService,
    MetricsService,
    DataCollectionService,
    DashboardService,
  ],
})
export class CreatorAnalyticsModule {}
