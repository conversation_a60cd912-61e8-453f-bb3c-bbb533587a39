import { IsOptional, IsDateString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ContentType } from '@prisma/client';
import { Type } from 'class-transformer';

export class GetMetricsDto {
  @ApiProperty({
    description: 'Date de début pour filtrer les métriques',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    description: 'Date de fin pour filtrer les métriques',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({
    description: 'Type de contenu pour filtrer les métriques',
    enum: ContentType,
    required: false,
  })
  @IsEnum(ContentType)
  @IsOptional()
  contentType?: ContentType;
}
