import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { JwtModule } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import * as Joi from 'joi';

import { PrismaModule } from './prisma/prisma.module';
import { DataCollectionModule } from './data-collection/data-collection.module';
import { MetricsModule } from './metrics/metrics.module';
import { DashboardsModule } from './dashboards/dashboards.module';
import { ForecastingModule } from './forecasting/forecasting.module';
import { BenchmarksModule } from './benchmarks/benchmarks.module';
import { RecommendationsModule } from './recommendations/recommendations.module';
import { AuthModule } from './auth/auth.module';
import { CommonModule } from './common/common.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        NODE_ENV: Joi.string()
          .valid('development', 'production', 'test')
          .default('development'),
        PORT: Joi.number().default(3020),
        API_PREFIX: Joi.string().default('api'),
        APP_NAME: Joi.string().default('creator-analytics'),
        DATABASE_URL: Joi.string().required(),
        JWT_SECRET: Joi.string().required(),
        JWT_EXPIRATION: Joi.string().default('1d'),
        JWT_REFRESH_SECRET: Joi.string().required(),
        JWT_REFRESH_EXPIRATION: Joi.string().default('7d'),
        REDIS_HOST: Joi.string().default('localhost'),
        REDIS_PORT: Joi.number().default(6379),
        REDIS_PASSWORD: Joi.string().allow(''),
        LOG_LEVEL: Joi.string()
          .valid('error', 'warn', 'info', 'debug')
          .default('info'),
        USER_SERVICE_URL: Joi.string().required(),
        CONTENT_SERVICE_URL: Joi.string().required(),
        EVENT_SERVICE_URL: Joi.string().required(),
        RECOMMENDATION_SERVICE_URL: Joi.string().required(),
        PYTHON_ANALYTICS_SERVICE_URL: Joi.string().required(),
        DATA_COLLECTION_INTERVAL: Joi.number().default(3600000),
        THROTTLE_TTL: Joi.number().default(60),
        THROTTLE_LIMIT: Joi.number().default(100),
      }),
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => ({
        ttl: config.get<number>('THROTTLE_TTL'),
        limit: config.get<number>('THROTTLE_LIMIT'),
      }),
    }),

    // JWT
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => ({
        secret: config.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: config.get<string>('JWT_EXPIRATION'),
        },
      }),
    }),

    // Scheduling
    ScheduleModule.forRoot(),

    // Modules de l'application
    PrismaModule,
    CommonModule,
    AuthModule,
    DataCollectionModule,
    MetricsModule,
    DashboardsModule,
    ForecastingModule,
    BenchmarksModule,
    RecommendationsModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
