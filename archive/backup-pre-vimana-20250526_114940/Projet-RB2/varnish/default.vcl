vcl 4.1;

import std;
import directors;

backend frontend {
    .host = "frontend";
    .port = "80";
    .probe = {
        .url = "/health";
        .timeout = 5s;
        .interval = 10s;
        .window = 5;
        .threshold = 3;
    }
}

sub vcl_recv {
    # Exclude paths that shouldn't be cached
    if (req.url ~ "^/api/") {
        return (pass);
    }

    # Remove cookies for static files
    if (req.url ~ "\.(css|js|png|gif|jp(e?)g|svg|woff|woff2|ttf|eot)") {
        unset req.http.Cookie;
        return (hash);
    }

    # Handle purge requests
    if (req.method == "PURGE") {
        if (!std.ip(req.http.X-Forwarded-For, "127.0.0.1") ~ purge) {
            return(synth(405, "Method not allowed"));
        }
        return (purge);
    }
}

sub vcl_backend_response {
    # Cache static files
    if (bereq.url ~ "\.(css|js|png|gif|jp(e?)g|svg|woff|woff2|ttf|eot)") {
        unset beresp.http.Set-Cookie;
        set beresp.ttl = 24h;
    }

    # Don't cache if backend says so
    if (beresp.http.Cache-Control ~ "private") {
        set beresp.uncacheable = true;
        return (deliver);
    }
}

sub vcl_deliver {
    # Add debug header to see if hit or miss
    if (obj.hits > 0) {
        set resp.http.X-Cache = "HIT";
    } else {
        set resp.http.X-Cache = "MISS";
    }
}
