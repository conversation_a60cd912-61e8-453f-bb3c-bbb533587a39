# docker-compose.consul.yml
version: '3.8'

services:
  consul-server:
    image: consul:1.15
    ports:
      - "8500:8500"
    volumes:
      - consul-data:/consul/data
    environment:
      CONSUL_LOCAL_CONFIG: |
        {
          "datacenter": "dc1",
          "server": true,
          "bootstrap_expect": 1
        }

  rabbitmq:
    image: rabbitmq:3.12-management
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: secret
    ports:
      - "5672:5672"
      - "15672:15672"

  auth-service:
    build: ./backend
    environment:
      - CONSUL_HOST=consul-server
      - SERVICE_PORT=3001
    ports:
      - "3001:3001"
    depends_on:
      - consul-server

volumes:
  consul-data: