#!/bin/bash

# Script pour fusionner front-Audrey-V1-main avec frontend
# Principalement pour importer les pages et styles

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Début de la fusion de front-Audrey-V1-main vers frontend...${NC}"

# Créer une sauvegarde avant de commencer
BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="./backups/frontend_merge_$BACKUP_DATE"

echo -e "${YELLOW}Création d'une sauvegarde dans $BACKUP_DIR...${NC}"
mkdir -p "$BACKUP_DIR"
cp -r frontend/* "$BACKUP_DIR/"
cp -r front-Audrey-V1-main/* "$BACKUP_DIR/audrey-v1-backup/"

# Créer les répertoires nécessaires s'ils n'existent pas
mkdir -p frontend/src/pages
mkdir -p frontend/src/styles
mkdir -p frontend/src/components

# Copier les pages
echo -e "${YELLOW}Copie des pages...${NC}"
cp -r front-Audrey-V1-main/src/pages/* frontend/src/pages/

# Copier les styles
echo -e "${YELLOW}Copie des styles...${NC}"
cp -r front-Audrey-V1-main/src/styles/* frontend/src/styles/

# Copier les composants si nécessaire
echo -e "${YELLOW}Copie des composants...${NC}"
cp -r front-Audrey-V1-main/src/components/* frontend/src/components/

# Corriger les imports dans les fichiers copiés
echo -e "${YELLOW}Correction des imports dans les fichiers...${NC}"

# Utiliser les scripts existants pour corriger les problèmes d'import
cd frontend
bash ./fix-path-imports.sh
bash ./fix-quotes-in-imports.sh
bash ./fix-jsx.sh

# Adapter le fichier index.tsx ou main.tsx si nécessaire
echo -e "${YELLOW}Adaptation du fichier principal...${NC}"

# Vérifier si le fichier main.tsx existe dans frontend
if [ -f "src/main.tsx" ]; then
  # Adapter main.tsx pour inclure les routes d'Audrey-V1
  echo "Adaptation de src/main.tsx..."
  # Logique d'adaptation à implémenter selon la structure
else
  # Créer un fichier de routes basé sur App.tsx d'Audrey-V1
  echo "Création d'un fichier de routes basé sur App.tsx d'Audrey-V1..."
  cp ../front-Audrey-V1-main/src/App.tsx src/routes/AudreyRoutes.tsx
  # Adapter les imports dans ce fichier
  sed -i '' 's/\.\/pages/\.\.\/pages/g' src/routes/AudreyRoutes.tsx
fi

echo -e "${GREEN}Fusion terminée avec succès!${NC}"
echo -e "${YELLOW}Vérifiez maintenant les conflits potentiels et testez l'application.${NC}"