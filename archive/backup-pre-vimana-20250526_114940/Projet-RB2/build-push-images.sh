#!/bin/bash

# Configuration
DOCKER_REGISTRY="docker.io"  # Remplacez par votre registre Docker
NAMESPACE="votre-namespace"   # Remplacez par votre namespace

# Liste des services
SERVICES=(
    "frontend"
    "backend"
    "partner-registration"
    "insurance-service"
    "website-creator-service"
    "gps-service"
    "marketplace-service"
    "analyzer-dashboard"
)

# Construction et push des images
for service in "${SERVICES[@]}"; do
    echo "Building $service..."
    docker build -t $DOCKER_REGISTRY/$NAMESPACE/$service:latest ./$service
    
    echo "Pushing $service..."
    docker push $DOCKER_REGISTRY/$NAMESPACE/$service:latest
done

echo "All images have been built and pushed successfully!"
