stages:
  - test
  - security
  - build
  - deploy

security_checks:
  stage: security
  image: aquasec/trivy:latest
  script:
    - trivy fs --severity HIGH,CRITICAL .
    - trivy config --severity HIGH,CRITICAL ./charts/

performance_tests:
  stage: test
  image: node:16
  script:
    - npm run test:load
  artifacts:
    paths:
      - load-test-results/
    expire_in: 1 week

unit_tests:
  stage: test
  image: node:16
  script:
    - npm ci
    - npm test

build_image:
  stage: build
  image: docker:20.10
  services:
    - docker:dind
  script:
    - docker build -t registry.example.com/financial-service:$CI_COMMIT_SHA .
    - docker push registry.example.com/financial-service:$CI_COMMIT_SHA

deploy_prod:
  stage: deploy
  image: alpine/helm:3.7.1
  script:
    - helm upgrade financial-service ./charts/financial
      --install
      --namespace financial-prod
      --set image.tag=$CI_COMMIT_SHA
      --set security.oauth2.clientSecret=$OAUTH2_SECRET
      --atomic
      --timeout 10m
