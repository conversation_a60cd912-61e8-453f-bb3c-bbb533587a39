import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errors');

export const options = {
  stages: [
    { duration: '1m', target: 50 },  // Montée progressive
    { duration: '3m', target: 50 },  // Test de charge
    { duration: '1m', target: 100 }, // Test de stress
    { duration: '1m', target: 0 },   // Retour au calme
  ],
  thresholds: {
    'http_req_duration': ['p(95)<500'], // 95% des requêtes sous 500ms
    'errors': ['rate<0.1'],             // Moins de 10% d'erreurs
  },
};

const BASE_URL = __ENV.API_URL || 'http://localhost:8080';

export default function() {
  const payload = {
    data: 'Test data for encryption',
    algorithm: 'AES-256-GCM',
  };

  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${__ENV.API_TOKEN}`,
    },
  };

  // Test d'encryption
  const encryptResponse = http.post(
    `${BASE_URL}/api/v1/encrypt`,
    JSON.stringify(payload),
    params
  );

  check(encryptResponse, {
    'encrypt status is 200': (r) => r.status === 200,
    'encrypt response has encrypted data': (r) => r.json('encryptedData') !== undefined,
  }) || errorRate.add(1);

  // Test de décryption
  if (encryptResponse.status === 200) {
    const decryptPayload = {
      encryptedData: encryptResponse.json('encryptedData'),
    };

    const decryptResponse = http.post(
      `${BASE_URL}/api/v1/decrypt`,
      JSON.stringify(decryptPayload),
      params
    );

    check(decryptResponse, {
      'decrypt status is 200': (r) => r.status === 200,
      'decrypt response matches original': (r) => r.json('data') === payload.data,
    }) || errorRate.add(1);
  }

  sleep(1);
}