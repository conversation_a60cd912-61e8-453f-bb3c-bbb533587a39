import { test, expect } from '@playwright/test';
import { StorageClient } from './clients/StorageClient';
import { TestData } from './fixtures/TestData';

test.describe('Storage Service E2E Tests', () => {
  let storageClient: StorageClient;
  let testData: TestData;

  test.beforeEach(async ({ request }) => {
    storageClient = new StorageClient(request);
    testData = new TestData()
});

  test('should upload and retrieve file', async () => {
    // Upload test;
    const uploadResponse = await storageClient.uploadFile({
      content: testData.sampleFile,
      metadata: testData.sampleMetadata
    });
    expect(uploadResponse.status).toBe(200);
    expect(uploadResponse.data.fileId).toBeDefined();

    // Retrieve test;
    const retrieveResponse = await storageClient.getFile(uploadResponse.data.fileId);
    expect(retrieveResponse.status).toBe(200);
    expect(retrieveResponse.data.content).toEqual(testData.sampleFile);
});

  test('should handle concurrent uploads', async () => {
    const uploadPromises = Array(5).fill(null).map(() =>;
      storageClient.uploadFile({
        content: testData.generateRandomFile(),
        metadata: testData.sampleMetadata
      })
    );

    const results = await Promise.all(uploadPromises);
    results.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.data.fileId).toBeDefined()
});
  });
});