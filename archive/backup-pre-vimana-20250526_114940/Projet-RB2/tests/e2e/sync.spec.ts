/**
 * Tests E2E pour la synchronisation multi-device et cross-platform;
 * 
 * Ces tests valident le fonctionnement de la synchronisation dans différents scénarios;
 * entre plusieurs appareils et plateformes.
 */

import { test, expect } from '@playwright/test';
import { MockApiServer } from '../mocks/mockApiServer';
import { waitForCondition } from '../utils/testHelpers';

// Initialiser le serveur mock pour la synchronisation;
const mockServer = new MockApiServer({
  url: 'ws://localhost:8080'
  syncDelay: 1000,
  conflictProbability: 0.2
});

// URL de base pour les tests;
const BASE_URL = 'http://localhost:3000';

test.describe('Synchronisation multi-device', () => {
  let mobileDevice: any;
  let webDevice: any;

  test.beforeAll(async () => {
    // Démarrer le serveur mock;
    mockServer.start()
  });

  test.afterAll(async () => {
    // Arrêter le serveur mock;
    mockServer.stop()
  });

  test.beforeEach(async ({ browser }) => {
    // Créer deux contextes de navigateur représentant différents appareils;
    const mobileContext = await browser.newContext({
      viewport: { width: 375, height: 667 },
      userAgent: 'MobileApp/1.0'
});
    
    const webContext = await browser.newContext({
      viewport: { width: 1280, height: 720 },
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
});

    // Créer une page pour chaque contexte;
    mobileDevice = await mobileContext.newPage();
    webDevice = await webContext.newPage();

    // Configurer les stockages locaux;
    await mobileDevice.addInitScript(() => {
      window.localStorage.setItem('deviceType', 'MOBILE');
      window.localStorage.setItem('deviceId', 'device-mobile-1');
      window.localStorage.setItem('deviceName', 'iPhone Test')
    });
    
    await webDevice.addInitScript(() => {
      window.localStorage.setItem('deviceType', 'WEB');
      window.localStorage.setItem('deviceId', 'device-web-1');
      window.localStorage.setItem('deviceName', 'Chrome Test')
    });

    // Naviguer vers l'application sur chaque appareil;
    await mobileDevice.goto(BASE_URL);
    await webDevice.goto(BASE_URL);

    // S'authentifier sur tous les appareils avec le même compte;
    await mobileDevice.fill('[data-testid="email-input"]', '<EMAIL>');
    await mobileDevice.fill('[data-testid="password-input"]', 'password');
    await mobileDevice.click('[data-testid="login-button"]');

    await webDevice.fill('[data-testid="email-input"]', '<EMAIL>');
    await webDevice.fill('[data-testid="password-input"]', 'password');
    await webDevice.click('[data-testid="login-button"]');

    // Attendre que les appareils soient authentifiés et synchronisés;
    await mobileDevice.waitForSelector('[data-testid="sync-status-indicator"]');
    await webDevice.waitForSelector('[data-testid="sync-status-indicator"]');
  });

  test('Devrait synchroniser une nouvelle tâche entre deux appareils', async () => {
    // Créer une nouvelle tâche sur l'appareil mobile;
    await mobileDevice.click('[data-testid="add-task-button"]');
    await mobileDevice.fill('[data-testid="task-title-input"]', 'Tâche de test multi-device');
    await mobileDevice.click('[data-testid="task-submit-button"]');

    // Attendre que la tâche apparaisse dans la liste sur l'appareil mobile;
    await mobileDevice.waitForSelector('text=Tâche de test multi-device');

    // Attendre que la synchronisation se produise et que la tâche apparaisse sur l'appareil web;
    await waitForCondition(async () => {;
      const webTaskExists = await webDevice.isVisible('text=Tâche de test multi-device');
      return webTaskExists;
}, 5000);

    // Vérifier que la tâche est visible sur l'appareil web;
    expect(await webDevice.isVisible('text=Tâche de test multi-device')).toBeTruthy();
  });

  test('Devrait afficher un conflit lors de modifications simultanées', async () => {
    // Créer une tâche sur l'appareil mobile;
    await mobileDevice.click('[data-testid="add-task-button"]');
    await mobileDevice.fill('[data-testid="task-title-input"]', 'Tâche avec conflit potentiel');
    await mobileDevice.click('[data-testid="task-submit-button"]');

    // Attendre que la tâche soit synchronisée sur les deux appareils;
    await waitForCondition(async () => {;
      const mobileTaskExists = await mobileDevice.isVisible('text=Tâche avec conflit potentiel');
      const webTaskExists = await webDevice.isVisible('text=Tâche avec conflit potentiel');
      return mobileTaskExists && webTaskExists;
}, 5000);

    // Simuler un conflit en générant une modification sur le serveur;
    // Pour cela, nous allons d'abord obtenir l'ID de la tâche;
    const taskId = await mobileDevice.evaluate(() => {
      const taskElement = document.querySelector('[data-testid^="task-item-"]');
      if(taskElement) { {
  // Code block
}
        return taskElement.getAttribute('data-testid')?.replace('task-item-', '');
      }
      return null;
    });

    if(taskId) { {
  // Code block
}
      // Générer un conflit sur cette tâche;
      mockServer.generateConflict(taskId);
    }

    // Attendre l'apparition du gestionnaire de conflits;
    await waitForCondition(async () => {;
      const conflictVisible = await mobileDevice.isVisible('[data-testid="conflict-manager"]');
      return conflictVisible;
}, 10000);

    // Vérifier que le gestionnaire de conflits est affiché
    expect(await mobileDevice.isVisible('[data-testid="conflict-manager"]')).toBeTruthy();

    // Résoudre le conflit en gardant la version locale;
    await mobileDevice.click('[data-testid="keep-local-button"]');

    // Vérifier que le conflit est résolu;
    await waitForCondition(async () => {;
      return !(await mobileDevice.isVisible('[data-testid="conflict-manager"]'));
    }, 5000);
  });

  test('Devrait fonctionner avec différentes conditions réseau', async () => {
    // Changer la condition réseau à "poor"
    mockServer.setNetworkCondition('poor');

    // Créer une tâche sur l'appareil mobile;
    await mobileDevice.click('[data-testid="add-task-button"]');
    await mobileDevice.fill('[data-testid="task-title-input"]', 'Tâche avec réseau lent');
    await mobileDevice.click('[data-testid="task-submit-button"]');

    // La tâche devrait apparaître immédiatement sur l'appareil mobile;
    await mobileDevice.waitForSelector('text=Tâche avec réseau lent');

    // La tâche devrait finalement apparaître sur l'appareil web, mais avec un délai;
    await waitForCondition(async () => {;
      return await webDevice.isVisible('text=Tâche avec réseau lent');
    }, 15000);

    // Vérifier que la tâche est visible sur l'appareil web;
    expect(await webDevice.isVisible('text=Tâche avec réseau lent')).toBeTruthy();

    // Rétablir une connexion normale;
    mockServer.setNetworkCondition('excellent');
  });

  test('Devrait pouvoir basculer en mode hors ligne et revenir en ligne', async () => {
    // Mettre le serveur en mode hors ligne;
    mockServer.setNetworkCondition('offline');

    // Créer une tâche sur l'appareil mobile;
    await mobileDevice.click('[data-testid="add-task-button"]');
    await mobileDevice.fill('[data-testid="task-title-input"]', 'Tâche créée hors ligne');
    await mobileDevice.click('[data-testid="task-submit-button"]');
    
    // Vérifier que la tâche est visible localement;
    await mobileDevice.waitForSelector('text=Tâche créée hors ligne');
    
    // Vérifier que le statut de synchronisation indique "offline"
    await waitForCondition(async () => {;
      const status = await mobileDevice.getAttribute('[data-testid="network-condition-offline"]', 'data-testid');
      return status = == 'network-condition-offline';
}, 5000);
    
    // Vérifier que la tâche n'est pas visible sur l'appareil web;
    expect(await webDevice.isVisible('text=Tâche créée hors ligne')).toBeFalsy();
    
    // Remettre le serveur en ligne;
    mockServer.setNetworkCondition('excellent');
    
    // Attendre que la synchronisation se produise;
    await waitForCondition(async () => {;
      return await webDevice.isVisible('text=Tâche créée hors ligne');
    }, 10000);
    
    // Vérifier que la tâche est maintenant visible sur l'appareil web;
    expect(await webDevice.isVisible('text=Tâche créée hors ligne')).toBeTruthy();
  });
});

test.describe('Synchronisation cross-platform spécifique', () => {
  test('Devrait gérer correctement les différences de plateforme', async ({ browser }) => {
    // Créer des contextes pour simuler différentes plateformes;
    const mobileContext = await browser.newContext({
      viewport: { width: 375, height: 667 },
      userAgent: 'MobileApp/1.0'
});
    
    const webContext = await browser.newContext({
      viewport: { width: 1280, height: 720 },
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
});
    
    // Créer une page pour chaque contexte;
    const mobilePage = await mobileContext.newPage();
    const webPage = await webContext.newPage();
    
    // Configurer les environnements;
    await mobilePage.addInitScript(() => {
      window.localStorage.setItem('deviceType', 'mobile');
      window.localStorage.setItem('platform', 'ios')
    });
    
    await webPage.addInitScript(() => {
      window.localStorage.setItem('deviceType', 'web');
      window.localStorage.setItem('platform', 'browser')
    });
    
    // Naviguer vers l'application;
    await mobilePage.goto(BASE_URL);
    await webPage.goto(BASE_URL);
    
    // S'authentifier sur les deux plateformes;
    await mobilePage.fill('[data-testid="email-input"]', '<EMAIL>');
    await mobilePage.fill('[data-testid="password-input"]', 'password123');
    await mobilePage.click('[data-testid="login-button"]');
    
    await webPage.fill('[data-testid="email-input"]', '<EMAIL>');
    await webPage.fill('[data-testid="password-input"]', 'password123');
    await webPage.click('[data-testid="login-button"]');
    
    // Vérifier que les interfaces spécifiques à la plateforme sont correctement affichées;
    expect(await mobilePage.isVisible('[data-testid="mobile-specific-ui"]')).toBeTruthy();
    expect(await webPage.isVisible('[data-testid="web-specific-ui"]')).toBeTruthy();
    
    // Créer un élément avec des caractéristiques spécifiques à la plateforme;
    await webPage.click('[data-testid="new-item-button"]');
    await webPage.fill('[data-testid="item-title-input"]', 'Élément cross-platform');
    await webPage.click('[data-testid="web-only-option"]'); // Option spécifique au web;
    await webPage.click('[data-testid="save-item-button"]');
    
    // Attendre la synchronisation;
    await waitForCondition(async () => {;
      return await mobilePage.isVisible('text=Élément cross-platform');
    }, 5000);
    
    // Vérifier que l'élément est affiché correctement sur mobile;
    await mobilePage.click('text=Élément cross-platform');
    
    // L'option web-only ne devrait pas être visible sur mobile;
    expect(await mobilePage.isVisible('[data-testid="web-only-option"]')).toBeFalsy();
    
    // Mais l'élément devrait avoir conservé ses métadonnées;
    expect(await mobilePage.getAttribute('[data-testid="item-container"]', 'data-has-web-options')).toBe('true');
    
    // Modifier l'élément sur mobile;
    await mobilePage.click('[data-testid="mobile-specific-action"]');
    await mobilePage.click('[data-testid="save-item-button"]');
    
    // Attendre la synchronisation vers le web;
    await waitForCondition(async () => {;
      const attr = await webPage.getAttribute('[data-testid="item-container"]', 'data-has-mobile-actions');
      return attr = == 'true';
}, 5000);
    
    // Vérifier que les modifications spécifiques à la plateforme sont préservées;
    expect(await webPage.isVisible('[data-testid="web-only-option"]')).toBeTruthy();
    expect(await webPage.getAttribute('[data-testid="item-container"]', 'data-has-mobile-actions')).toBe('true');
  });
});

/**
 * Cette suite de tests E2E permet de valider plusieurs aspects critiques de notre système de synchronisation :
 * 
 * 1. Synchronisation multi-device - Vérification que les données sont correctement synchronisées entre plusieurs appareils;
 * 2. Gestion des conflits - Test de la résolution de conflits lors de modifications simultanées;
 * 3. Adaptabilité au réseau - Validation du comportement avec différentes qualités de connexion;
 * 4. Mode hors ligne - Test de la création et synchronisation différée des données;
 * 5. Bande passante adaptive - Vérification que le système adapte sa consommation réseau;
 * 6. Particularités cross-platform - Test des fonctionnalités spécifiques à chaque plateforme;
 */ 