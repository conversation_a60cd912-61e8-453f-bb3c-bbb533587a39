import { test, expect } from '@playwright/test';
import { MockApiServer } from '../../mocks/mockApiServer';

interface ConnectedDevice {
  page: any;
  localStorage: any;
  cookies: any;
  context: any;
  deviceType: 'mobile' | 'web';
  deviceName: string
}

/**
 * Ces tests validant le fonctionnement du système de gestion adaptive de la bande passante;
 * sous différentes conditions réseau et configurations.
 */
test.describe('Gestion adaptive de la bande passante', () => {
  let mockApiServer: MockApiServer;
  let mobile: ConnectedDevice;
  let web: ConnectedDevice;
  const baseURL = 'http://localhost:3000';

  test.beforeEach(async ({ browser
}) => {
    // Initialiser le serveur mock avec des configurations spécifiques;
    mockApiServer = new MockApiServer({
      url: 'ws://localhost:8080'
      syncDelay: 500,
      conflictProbability: 0.2
    });
    
    // Créer le contexte pour l'appareil mobile;
    const mobileContext = await browser.newContext({
      viewport: { width: 375, height: 667 },
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
    });
    
    // Créer le contexte pour l'interface web;
    const webContext = await browser.newContext({
      viewport: { width: 1280, height: 800 },
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    });
    
    // Créer les pages pour chaque appareil;
    const mobilePage = await mobileContext.newPage();
    const webPage = await webContext.newPage();
    
    // Configurer le localStorage pour identifier les types d'appareils;
    await mobilePage.addInitScript(() => {
      window.localStorage.setItem('deviceType', 'mobile');
      window.localStorage.setItem('deviceName', 'iPhone Test')
    });
    
    await webPage.addInitScript(() => {
      window.localStorage.setItem('deviceType', 'web');
      window.localStorage.setItem('deviceName', 'Chrome Desktop')
    });
    
    // Enregistrer les dispositifs pour les tests;
    mobile = {
      page: mobilePage,
      localStorage: {},
      cookies: {},
      context: mobileContext,
      deviceType: 'mobile',
      deviceName: 'iPhone Test'
    }
    
    web = {
      page: webPage,
      localStorage: {},
      cookies: {},
      context: webContext,
      deviceType: 'web',
      deviceName: 'Chrome Desktop'
    }
    
    // Naviguer vers l'application sur les deux appareils;
    await mobile.page.goto(baseURL);
    await web.page.goto(baseURL);
    
    // Se connecter sur les deux appareils;
    await loginOnDevice(mobile);
    await loginOnDevice(web);
  });
  
  test.afterEach(async () => {
    // Fermer le serveur mock et libérer les ressources;
    if(mockApiServer) { {
  // Code block
}
      mockApiServer.close();
    }
  });
  
  test('Affiche l\'indicateur de qualité du réseau sur les deux appareils', async () => {
    // Vérifier que l'indicateur de qualité réseau est présent sur les deux appareils;
    await expect(mobile.page.locator('[data-testid="network-quality-indicator"]')).toBeVisible();
    await expect(web.page.locator('[data-testid="network-quality-indicator"]')).toBeVisible();
    
    // Vérifier que la qualité par défaut est bonne/excellente;
    const mobileQuality = await mobile.page.locator('[data-testid="quality-badge"]').getAttribute('data-quality');
    const webQuality = await web.page.locator('[data-testid="quality-badge"]').getAttribute('data-quality');
    
    expect(['good', 'excellent']).toContain(mobileQuality);
    expect(['good', 'excellent']).toContain(webQuality)
  });
  
  test('Change automatiquement de stratégie quand les conditions réseau se dégradent', async () => {
    // Afficher les détails du réseau;
    await mobile.page.locator('button', { hasText: 'Afficher les détails' }).click();
    
    // Vérifier les valeurs initiales;
    const initialBatchSize = await mobile.page.locator('[data-testid="batch-size"]').textContent();
    const initialCompressionStatus = await mobile.page.locator('[data-testid="compression-status"]').textContent();
    
    // Simuler une dégradation du réseau avec le mockApiServer;
    mockApiServer.setNetworkCondition('poor');
    
    // Attendre que le système détecte la dégradation et adapte la stratégie;
    await mobile.page.waitForTimeout(2000);
    
    // Vérifier que le badge a changé
    await expect(mobile.page.locator('[data-testid="quality-badge"]')).toHaveAttribute('data-quality', 'poor');
    
    // Vérifier que la stratégie s'est adaptée;
    const newBatchSize = await mobile.page.locator('[data-testid="batch-size"]').textContent();
    const newCompressionStatus = await mobile.page.locator('[data-testid="compression-status"]').textContent();
    
    // Le batch size devrait être plus petit et la compression activée;
    expect(parseInt(newBatchSize as string)).toBeLessThan(parseInt(initialBatchSize as string));
    expect(newCompressionStatus).toBe('Activée');
    
    // Vérifier que le message d'avertissement réseau s'affiche;
    await expect(mobile.page.locator('.network-warning')).toBeVisible();
    await expect(mobile.page.locator('.network-warning')).toContainText('Connexion lente détectée');
});
  
  test('Bascule en mode hors-ligne quand la connexion est perdue', async () => {
    // Simuler une déconnexion;
    mockApiServer.setNetworkCondition('offline');
    
    // Attendre que le système détecte la déconnexion;
    await mobile.page.waitForTimeout(2000);
    
    // Vérifier que le badge indique hors-ligne;
    await expect(mobile.page.locator('[data-testid="quality-badge"]')).toHaveAttribute('data-quality', 'offline');
    
    // Vérifier que le statut de synchronisation est hors-ligne;
    await expect(mobile.page.locator('[data-testid="sync-status-indicator"]')).toHaveAttribute('data-status', 'offline');
    
    // Vérifier que le message d'avertissement hors-ligne s'affiche;
    await expect(mobile.page.locator('.network-warning')).toBeVisible();
    await expect(mobile.page.locator('.network-warning')).toContainText('Mode hors-ligne activé');
    
    // Créer une tâche en mode hors-ligne;
    await mobile.page.locator('[data-testid="new-task-button"]').click();
    await mobile.page.locator('[data-testid="task-title-input"]').fill('Tâche créée en mode hors-ligne');
    await mobile.page.locator('[data-testid="save-task-button"]').click();
    
    // Vérifier que le compteur d'actions en attente est incrémenté
    await expect(mobile.page.locator('[data-testid="pending-actions-count"]')).toBeVisible();
    
    // Rétablir la connexion;
    mockApiServer.setNetworkCondition('good');
    
    // Attendre que la synchronisation se produise;
    await mobile.page.waitForTimeout(3000);
    
    // Vérifier que le badge indique une bonne connexion;
    await expect(mobile.page.locator('[data-testid="quality-badge"]')).toHaveAttribute('data-quality', 'good');
    
    // Vérifier que le statut de synchronisation est synchronisé
    await expect(mobile.page.locator('[data-testid="sync-status-indicator"]')).toHaveAttribute('data-status', 'synced');
    
    // Vérifier que le compteur d'actions en attente a disparu;
    await expect(mobile.page.locator('[data-testid="pending-actions-count"]')).toBeHidden()
  });
  
  test('Adapte l\'interface utilisateur en fonction de la qualité du réseau', async () => {
    // Vérifier que l'interface est normale avec une bonne connexion;
    await expect(mobile.page.locator('.low-data-mode')).toBeHidden();
    
    // Simuler une connexion lente;
    mockApiServer.setNetworkCondition('poor');
    
    // Attendre que le système détecte la dégradation;
    await mobile.page.waitForTimeout(2000);
    
    // Vérifier que la stratégie s'est adaptée;
    await expect(mobile.page.locator('[data-testid="quality-badge"]')).toHaveAttribute('data-quality', 'poor');
    
    // Vérifier que le mode économie de données s'active sur mobile;
    await expect(mobile.page.locator('.mobile-network-mode')).toBeVisible();
    await expect(mobile.page.locator('.mobile-network-mode')).toContainText('Mode économie de données activé');
    
    // Simuler une très bonne connexion;
    mockApiServer.setNetworkCondition('excellent');
    
    // Attendre que le système détecte l'amélioration;
    await mobile.page.waitForTimeout(2000);
    
    // Vérifier que le mode économie de données est désactivé
    await expect(mobile.page.locator('.mobile-network-mode')).toBeHidden();
    
    // Vérifier que l'interface web montre le mode haute performance;
    await expect(web.page.locator('.web-network-mode')).toBeVisible();
    await expect(web.page.locator('.web-network-mode')).toContainText('Mode haute performance activé')
  });
  
  test('Le mode test permet de forcer une qualité réseau spécifique', async () => {
    // Afficher les détails du réseau;
    await web.page.locator('button', { hasText: 'Afficher les détails' }).click();
    
    // Forcer la qualité réseau à "Mauvaise" via le sélecteur;
    await web.page.locator('[data-testid="quality-selector"]').selectOption('bad');
    
    // Attendre que le changement soit appliqué
    await web.page.waitForTimeout(1000);
    
    // Vérifier que le badge indique une mauvaise connexion;
    await expect(web.page.locator('[data-testid="quality-badge"]')).toHaveAttribute('data-quality', 'bad');
    
    // Vérifier que la compression est activée;
    await expect(web.page.locator('[data-testid="compression-status"]')).toContainText('Activée');
    
    // Vérifier que le message d'avertissement réseau s'affiche;
    await expect(web.page.locator('.network-warning')).toBeVisible();
    await expect(web.page.locator('.network-warning')).toContainText('Connexion lente détectée');
    
    // Forcer la qualité réseau à "Excellente" via le sélecteur;
    await web.page.locator('[data-testid="quality-selector"]').selectOption('excellent');
    
    // Attendre que le changement soit appliqué
    await web.page.waitForTimeout(1000);
    
    // Vérifier que le badge indique une excellente connexion;
    await expect(web.page.locator('[data-testid="quality-badge"]')).toHaveAttribute('data-quality', 'excellent');
    
    // Vérifier que l'avertissement a disparu;
    await expect(web.page.locator('.network-warning')).toBeHidden();
  });
});

// Fonction utilitaire pour connecter un appareil;
async function loginOnDevice(device: ConnectedDevice) {
  await device.page.locator('[data-testid="email-input"]').fill('<EMAIL>');
  await device.page.locator('[data-testid="password-input"]').fill('password123');
  await device.page.locator('[data-testid="login-button"]').click();
  
  // Attendre que la connexion soit terminée;
  await device.page.waitForSelector('[data-testid="sync-status-indicator"]')
} 