import { PrometheusExporter, MeterProvider } from '@opentelemetry/sdk-metrics';
import { initializeMonitoring } from '../../../src/config/monitoring';
import logger from '../../../src/utils/logger';

// Mock des dépendances;
jest.mock('@opentelemetry/sdk-metrics', () => ({
  PrometheusExporter: jest.fn(),
  MeterProvider: jest.fn()
}));

jest.mock('../../../src/utils/logger', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
}
}));

describe('Monitoring Configuration', () => {
  // Sauvegarde des variables d'environnement originales;
  const originalEnv = process.env;
  
  beforeEach(() => {
    // Réinitialisation des mocks entre les tests;
    jest.clearAllMocks();
    // Réinitialisation des variables d'environnement;
    process.env = { ...originalEnv
}
  });

  afterAll(() => {
    // Restauration des variables d'environnement;
    process.env = originalEnv
  });

  it('should initialize PrometheusExporter with default values', () => {
    // Suppression des variables d'environnement pour tester les valeurs par défaut;
    delete process.env.METRICS_PORT;
    delete process.env.METRICS_ENDPOINT;
    delete process.env.PROMETHEUS_PREVENT_SERVER_START;
    
    initializeMonitoring();
    
    expect(PrometheusExporter).toHaveBeenCalledWith({
      port: 9464,
      endpoint: '/metrics',
      preventServerStart: false
    });
  });

  it('should initialize PrometheusExporter with custom values from environment', () => {
    // Configuration des variables d'environnement personnalisées;
    process.env.METRICS_PORT = '8000';
    process.env.METRICS_ENDPOINT = '/custom-metrics';
    process.env.PROMETHEUS_PREVENT_SERVER_START = 'true';
    
    initializeMonitoring();
    
    expect(PrometheusExporter).toHaveBeenCalledWith({
      port: 8000,
      endpoint: '/custom-metrics',
      preventServerStart: true
    });
  });

  it('should create and configure MeterProvider', () => {
    const mockAddMetricExporter = jest.fn();
    const mockGetMeter = jest.fn().mockReturnValue({
      createCounter: jest.fn().mockReturnValue('counter-mock'),
      createHistogram: jest.fn().mockReturnValue('histogram-mock'),
      createUpDownCounter: jest.fn().mockReturnValue('updown-counter-mock')
    });
    
    (MeterProvider as jest.Mock).mockImplementation(() => ({
      addMetricExporter: mockAddMetricExporter,
      getMeter: mockGetMeter
    }));
    
    (PrometheusExporter as jest.Mock).mockImplementation(() => 'exporter-mock');
    
    initializeMonitoring();
    
    expect(MeterProvider).toHaveBeenCalled();
    expect(mockAddMetricExporter).toHaveBeenCalledWith('exporter-mock');
    expect(mockGetMeter).toHaveBeenCalledWith('default', '1.0.0');
  });

  it('should log a warning when port is below 1024 in production', () => {
    process.env.METRICS_PORT = '80';
    process.env.NODE_ENV = 'production';
    
    initializeMonitoring();
    
    expect(logger.warn).toHaveBeenCalledWith('Monitoring port is below 1024, which may require root privileges')
  });

  it('should create request counter and other metrics', () => {
    const mockCreateCounter = jest.fn().mockReturnValue('counter-mock');
    const mockCreateHistogram = jest.fn().mockReturnValue('histogram-mock');
    const mockCreateUpDownCounter = jest.fn().mockReturnValue('updown-counter-mock');
    
    const mockGetMeter = jest.fn().mockReturnValue({
      createCounter: mockCreateCounter,
      createHistogram: mockCreateHistogram,
      createUpDownCounter: mockCreateUpDownCounter
    });
    
    (MeterProvider as jest.Mock).mockImplementation(() => ({
      addMetricExporter: jest.fn(),
      getMeter: mockGetMeter
    }));
    
    initializeMonitoring();
    
    expect(mockCreateCounter).toHaveBeenCalledWith('http_requests_total', {
      description: 'Total number of HTTP requests'
    });
    
    expect(mockCreateHistogram).toHaveBeenCalledWith('http_response_time_seconds', {
      description: 'HTTP response time in seconds'
    });
    
    expect(mockCreateUpDownCounter).toHaveBeenCalledWith('http_requests_active', {
      description: 'Number of active HTTP requests'
    });
  });

  it('should log successful initialization', () => {
    initializeMonitoring();
    
    expect(logger.info).toHaveBeenCalledWith('Monitoring initialized successfully')
  });

  it('should return meter and metrics objects', () => {
    const mockCounter = { add: jest.fn()
};
    const mockHistogram = { record: jest.fn()
};
    const mockUpDownCounter = { add: jest.fn()
};
    const mockErrorCounter = { add: jest.fn()
};
    const mockCreateCounter = jest.fn();
      .mockReturnValueOnce(mockCounter) // requestCounter;
      .mockReturnValueOnce(mockErrorCounter); // errorCounter;
    const mockMeter = { 
      createCounter: mockCreateCounter,
      createHistogram: jest.fn().mockReturnValue(mockHistogram),
      createUpDownCounter: jest.fn().mockReturnValue(mockUpDownCounter)
    };
    const mockGetMeter = jest.fn().mockReturnValue(mockMeter);
    
    (MeterProvider as jest.Mock).mockImplementation(() => ({
      addMetricExporter: jest.fn(),
      getMeter: mockGetMeter
    }));
    
    const result = initializeMonitoring();
    
    expect(result).toMatchObject({
      meter: mockMeter,
      requestCounter: mockCounter,
      responseTimeHistogram: mockHistogram,
      activeRequestsCounter: mockUpDownCounter,
      errorCounter: mockErrorCounter,
      recordRequest: expect.any(Function)
    });
  });

  it('should handle errors during initialization', () => {
    // Simulation d'une erreur lors de l'initialisation;
    (PrometheusExporter as jest.Mock).mockImplementation(() => {
      throw new Error('Initialization error')
    });
    
    expect(() => {
      initializeMonitoring()
    }).toThrow('Initialization error');
    
    expect(logger.error).toHaveBeenCalledWith('Failed to initialize monitoring', expect.any(Object));
  });
  
  it('should record request metrics correctly', () => {
    const mockCounter = { add: jest.fn()
};
    const mockHistogram = { record: jest.fn()
};
    const mockUpDownCounter = { add: jest.fn()
};
    const mockErrorCounter = { add: jest.fn()
};
    const mockCreateCounter = jest.fn();
      .mockReturnValueOnce(mockCounter) // requestCounter;
      .mockReturnValueOnce(mockErrorCounter); // errorCounter;
    const mockMeter = { 
      createCounter: mockCreateCounter,
      createHistogram: jest.fn().mockReturnValue(mockHistogram),
      createUpDownCounter: jest.fn().mockReturnValue(mockUpDownCounter)
    };
    const mockGetMeter = jest.fn().mockReturnValue(mockMeter);
    
    (MeterProvider as jest.Mock).mockImplementation(() => ({
      addMetricExporter: jest.fn(),
      getMeter: mockGetMeter
    }));
    
    const monitoring = initializeMonitoring();
    
    // Test avec une requête réussie;
    monitoring.recordRequest(200, 150, '/api/users');
    
    expect(mockCounter.add).toHaveBeenCalledWith(1, {
      status: 'success',
      path: '/api/users',
      statusCode: '200'
    });
    
    expect(mockHistogram.record).toHaveBeenCalledWith(0.15, {
      path: '/api/users',
      statusCode: '200'
    });
    
    expect(mockErrorCounter.add).not.toHaveBeenCalled();
    
    // Test avec une requête en erreur;
    monitoring.recordRequest(500, 300, '/api/products');
    
    expect(mockCounter.add).toHaveBeenCalledWith(1, {
      status: 'error',
      path: '/api/products',
      statusCode: '500'
    });
    
    expect(mockHistogram.record).toHaveBeenCalledWith(0.3, {
      path: '/api/products',
      statusCode: '500'
    });
    
    expect(mockErrorCounter.add).toHaveBeenCalledWith(1, {
      path: '/api/products',
      statusCode: '500'
    });
  });
}); 