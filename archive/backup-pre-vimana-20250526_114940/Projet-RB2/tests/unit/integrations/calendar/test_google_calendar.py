"""
Tests unitaires pour l'intégration avec Google Calendar.
"""

import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime

from src.integrations.calendar.google_calendar import GoogleCalendarService

class TestGoogleCalendarService:
    """
    Tests pour le service d'intégration avec Google Calendar.
    """
    
    def setup_method(self):
        """
        Configuration avant chaque test.
        """
        self.service = GoogleCalendarService()
        self.service.dev_mode = True  # Utiliser le mode développement pour les tests
    
    def test_init(self):
        """
        Test de l'initialisation du service.
        """
        assert self.service.credentials_file is not None
        assert self.service.token_file is not None
        assert self.service.dev_mode is True
        assert self.service.service is None
    
    @pytest.mark.asyncio
    async def test_create_event(self):
        """
        Test de la création d'un événement.
        """
        # Paramètres de l'événement
        title = "Test Event"
        start_time = datetime(2023, 1, 1, 10, 0, 0)
        end_time = datetime(2023, 1, 1, 12, 0, 0)
        description = "Test Description"
        location = "Test Location"
        attendees = ["<EMAIL>"]
        
        # Créer l'événement
        event = await self.service.create_event(
            title=title,
            start_time=start_time,
            end_time=end_time,
            description=description,
            location=location,
            attendees=attendees
        )
        
        # Vérifier que l'événement est correct
        assert event["title"] == title
        assert event["start_time"] == start_time.isoformat()
        assert event["end_time"] == end_time.isoformat()
        assert event["description"] == description
        assert event["location"] == location
        assert event["attendees"] == attendees
    
    @pytest.mark.asyncio
    async def test_update_event(self):
        """
        Test de la mise à jour d'un événement.
        """
        # Paramètres de l'événement
        event_id = "test_event_id"
        title = "Updated Event"
        start_time = datetime(2023, 1, 1, 11, 0, 0)
        end_time = datetime(2023, 1, 1, 13, 0, 0)
        
        # Mettre à jour l'événement
        event = await self.service.update_event(
            event_id=event_id,
            title=title,
            start_time=start_time,
            end_time=end_time
        )
        
        # Vérifier que l'événement est correct
        assert event["id"] == event_id
        assert event["title"] == title
        assert event["start_time"] == start_time.isoformat()
        assert event["end_time"] == end_time.isoformat()
    
    @pytest.mark.asyncio
    async def test_delete_event(self):
        """
        Test de la suppression d'un événement.
        """
        # Paramètres de l'événement
        event_id = "test_event_id"
        
        # Supprimer l'événement
        result = await self.service.delete_event(event_id)
        
        # Vérifier que la suppression a réussi
        assert result is True
    
    @pytest.mark.asyncio
    async def test_get_event(self):
        """
        Test de la récupération d'un événement.
        """
        # Paramètres de l'événement
        event_id = "test_event_id"
        
        # Récupérer l'événement
        event = await self.service.get_event(event_id)
        
        # Vérifier que l'événement est correct
        assert event["id"] == event_id
        assert "title" in event
        assert "start_time" in event
        assert "end_time" in event
    
    @pytest.mark.asyncio
    async def test_list_events(self):
        """
        Test de la récupération des événements.
        """
        # Récupérer les événements
        events = await self.service.list_events()
        
        # Vérifier que les événements sont corrects
        assert isinstance(events, list)
        assert len(events) > 0
        assert all("id" in event for event in events)
        assert all("title" in event for event in events)
        assert all("start_time" in event for event in events)
        assert all("end_time" in event for event in events)
    
    @pytest.mark.asyncio
    async def test_generate_ical(self):
        """
        Test de la génération d'un fichier iCalendar.
        """
        # Paramètres de l'événement
        event_id = "test_event_id"
        
        # Générer le fichier iCalendar
        ical = await self.service.generate_ical(event_id)
        
        # Vérifier que le fichier iCalendar est correct
        assert isinstance(ical, str)
        assert "BEGIN:VCALENDAR" in ical
        assert "END:VCALENDAR" in ical
        assert "BEGIN:VEVENT" in ical
        assert "END:VEVENT" in ical
    
    def test_get_service(self):
        """
        Test de la récupération du service Google Calendar.
        """
        # Désactiver le mode développement
        self.service.dev_mode = False
        
        # Mocker la fonction Credentials.from_authorized_user_info
        with patch("src.integrations.calendar.google_calendar.Credentials") as mock_credentials, \
             patch("src.integrations.calendar.google_calendar.build") as mock_build, \
             patch("src.integrations.calendar.google_calendar.os.path.exists", return_value=True), \
             patch("builtins.open", MagicMock()), \
             patch("src.integrations.calendar.google_calendar.json.load", return_value={}):
            
            # Mocker le retour de Credentials.from_authorized_user_info
            mock_credentials.from_authorized_user_info.return_value.valid = True
            
            # Mocker le retour de build
            mock_service = MagicMock()
            mock_build.return_value = mock_service
            
            # Récupérer le service
            service = self.service._get_service()
            
            # Vérifier que le service est correct
            assert service == mock_service
            
            # Vérifier que les fonctions ont été appelées
            mock_credentials.from_authorized_user_info.assert_called_once()
            mock_build.assert_called_once_with("calendar", "v3", credentials=mock_credentials.from_authorized_user_info.return_value)
