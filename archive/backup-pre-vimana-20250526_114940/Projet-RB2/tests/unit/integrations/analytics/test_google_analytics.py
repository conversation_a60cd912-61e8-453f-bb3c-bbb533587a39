"""
Tests unitaires pour l'intégration avec Google Analytics.
"""

import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime

from src.integrations.analytics.google_analytics import GoogleAnalyticsService

class TestGoogleAnalyticsService:
    """
    Tests pour le service d'intégration avec Google Analytics.
    """
    
    def setup_method(self):
        """
        Configuration avant chaque test.
        """
        self.service = GoogleAnalyticsService()
        self.service.dev_mode = True  # Utiliser le mode développement pour les tests
    
    def test_init(self):
        """
        Test de l'initialisation du service.
        """
        assert self.service.measurement_id is not None
        assert self.service.api_secret is not None
        assert self.service.dev_mode is True
        assert isinstance(self.service.events, list)
        assert isinstance(self.service.page_views, list)
        assert isinstance(self.service.conversions, list)
    
    @pytest.mark.asyncio
    async def test_track_event(self):
        """
        Test de l'enregistrement d'un événement.
        """
        # Paramètres de l'événement
        event_name = "test_event"
        event_data = {"key": "value"}
        user_id = "test_user"
        
        # Enregistrer l'événement
        event = await self.service.track_event(event_name, event_data, user_id)
        
        # Vérifier que l'événement est correct
        assert event["name"] == event_name
        assert event["data"] == event_data
        assert event["user_id"] == user_id
        assert "id" in event
        assert "timestamp" in event
        
        # Vérifier que l'événement a été stocké
        assert len(self.service.events) == 1
        assert self.service.events[0] == event
    
    @pytest.mark.asyncio
    async def test_track_page_view(self):
        """
        Test de l'enregistrement d'une vue de page.
        """
        # Paramètres de la vue de page
        page_path = "/test"
        page_title = "Test Page"
        user_id = "test_user"
        
        # Enregistrer la vue de page
        page_view = await self.service.track_page_view(page_path, page_title, user_id)
        
        # Vérifier que la vue de page est correcte
        assert page_view["page_path"] == page_path
        assert page_view["page_title"] == page_title
        assert page_view["user_id"] == user_id
        assert "id" in page_view
        assert "timestamp" in page_view
        
        # Vérifier que la vue de page a été stockée
        assert len(self.service.page_views) == 1
        assert self.service.page_views[0] == page_view
    
    @pytest.mark.asyncio
    async def test_track_conversion(self):
        """
        Test de l'enregistrement d'une conversion.
        """
        # Paramètres de la conversion
        conversion_name = "test_conversion"
        conversion_value = 100.0
        user_id = "test_user"
        
        # Enregistrer la conversion
        conversion = await self.service.track_conversion(conversion_name, conversion_value, user_id)
        
        # Vérifier que la conversion est correcte
        assert conversion["name"] == conversion_name
        assert conversion["value"] == conversion_value
        assert conversion["user_id"] == user_id
        assert "id" in conversion
        assert "timestamp" in conversion
        
        # Vérifier que la conversion a été stockée
        assert len(self.service.conversions) == 1
        assert self.service.conversions[0] == conversion
    
    @pytest.mark.asyncio
    async def test_get_events(self):
        """
        Test de la récupération des événements.
        """
        # Ajouter des événements
        event1 = {
            "id": "event_1",
            "name": "test_event_1",
            "data": {"key": "value1"},
            "user_id": "test_user",
            "timestamp": datetime(2023, 1, 1, 10, 0, 0).isoformat()
        }
        
        event2 = {
            "id": "event_2",
            "name": "test_event_2",
            "data": {"key": "value2"},
            "user_id": "test_user",
            "timestamp": datetime(2023, 1, 2, 10, 0, 0).isoformat()
        }
        
        self.service.events = [event1, event2]
        
        # Récupérer les événements
        start_date = datetime(2023, 1, 1, 0, 0, 0)
        end_date = datetime(2023, 1, 3, 0, 0, 0)
        events = await self.service.get_events(start_date, end_date)
        
        # Vérifier que les événements sont corrects
        assert len(events) == 2
        assert events[0] == event1
        assert events[1] == event2
        
        # Récupérer les événements avec un filtre sur le nom
        events = await self.service.get_events(start_date, end_date, "test_event_1")
        
        # Vérifier que les événements sont corrects
        assert len(events) == 1
        assert events[0] == event1
        
        # Récupérer les événements avec une plage de dates restreinte
        events = await self.service.get_events(datetime(2023, 1, 2, 0, 0, 0), end_date)
        
        # Vérifier que les événements sont corrects
        assert len(events) == 1
        assert events[0] == event2
    
    @pytest.mark.asyncio
    async def test_get_page_views(self):
        """
        Test de la récupération des vues de page.
        """
        # Ajouter des vues de page
        page_view1 = {
            "id": "page_view_1",
            "page_path": "/test1",
            "page_title": "Test Page 1",
            "user_id": "test_user",
            "timestamp": datetime(2023, 1, 1, 10, 0, 0).isoformat()
        }
        
        page_view2 = {
            "id": "page_view_2",
            "page_path": "/test2",
            "page_title": "Test Page 2",
            "user_id": "test_user",
            "timestamp": datetime(2023, 1, 2, 10, 0, 0).isoformat()
        }
        
        self.service.page_views = [page_view1, page_view2]
        
        # Récupérer les vues de page
        start_date = datetime(2023, 1, 1, 0, 0, 0)
        end_date = datetime(2023, 1, 3, 0, 0, 0)
        page_views = await self.service.get_page_views(start_date, end_date)
        
        # Vérifier que les vues de page sont correctes
        assert len(page_views) == 2
        assert page_views[0] == page_view1
        assert page_views[1] == page_view2
        
        # Récupérer les vues de page avec un filtre sur le chemin
        page_views = await self.service.get_page_views(start_date, end_date, "/test1")
        
        # Vérifier que les vues de page sont correctes
        assert len(page_views) == 1
        assert page_views[0] == page_view1
        
        # Récupérer les vues de page avec une plage de dates restreinte
        page_views = await self.service.get_page_views(datetime(2023, 1, 2, 0, 0, 0), end_date)
        
        # Vérifier que les vues de page sont correctes
        assert len(page_views) == 1
        assert page_views[0] == page_view2
    
    @pytest.mark.asyncio
    async def test_get_conversions(self):
        """
        Test de la récupération des conversions.
        """
        # Ajouter des conversions
        conversion1 = {
            "id": "conversion_1",
            "name": "test_conversion_1",
            "value": 100.0,
            "user_id": "test_user",
            "timestamp": datetime(2023, 1, 1, 10, 0, 0).isoformat()
        }
        
        conversion2 = {
            "id": "conversion_2",
            "name": "test_conversion_2",
            "value": 200.0,
            "user_id": "test_user",
            "timestamp": datetime(2023, 1, 2, 10, 0, 0).isoformat()
        }
        
        self.service.conversions = [conversion1, conversion2]
        
        # Récupérer les conversions
        start_date = datetime(2023, 1, 1, 0, 0, 0)
        end_date = datetime(2023, 1, 3, 0, 0, 0)
        conversions = await self.service.get_conversions(start_date, end_date)
        
        # Vérifier que les conversions sont correctes
        assert len(conversions) == 2
        assert conversions[0] == conversion1
        assert conversions[1] == conversion2
        
        # Récupérer les conversions avec un filtre sur le nom
        conversions = await self.service.get_conversions(start_date, end_date, "test_conversion_1")
        
        # Vérifier que les conversions sont correctes
        assert len(conversions) == 1
        assert conversions[0] == conversion1
        
        # Récupérer les conversions avec une plage de dates restreinte
        conversions = await self.service.get_conversions(datetime(2023, 1, 2, 0, 0, 0), end_date)
        
        # Vérifier que les conversions sont correctes
        assert len(conversions) == 1
        assert conversions[0] == conversion2
