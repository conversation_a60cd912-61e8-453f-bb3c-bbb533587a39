"""
Tests unitaires pour l'intégration avec Google Maps.
"""

import pytest
from unittest.mock import patch, MagicMock

from src.integrations.maps.google_maps import GoogleMapsService

class TestGoogleMapsService:
    """
    Tests pour le service d'intégration avec Google Maps.
    """
    
    def setup_method(self):
        """
        Configuration avant chaque test.
        """
        self.service = GoogleMapsService()
        self.service.dev_mode = True  # Utiliser le mode développement pour les tests
    
    def test_init(self):
        """
        Test de l'initialisation du service.
        """
        assert self.service.api_key is not None
        assert self.service.dev_mode is True
        assert self.service.client is None
    
    @pytest.mark.asyncio
    async def test_geocode(self):
        """
        Test du géocodage d'une adresse.
        """
        # Paramètres de l'adresse
        address = "Paris, France"
        
        # Géocoder l'adresse
        location = await self.service.geocode(address)
        
        # Vérifier que la localisation est correcte
        assert location["address"] == address
        assert "latitude" in location
        assert "longitude" in location
        assert "country" in location
        assert "country_code" in location
        assert "city" in location
        assert "formatted_address" in location
    
    @pytest.mark.asyncio
    async def test_reverse_geocode(self):
        """
        Test du géocodage inverse.
        """
        # Paramètres des coordonnées
        latitude = 48.8566
        longitude = 2.3522
        
        # Géocoder les coordonnées
        location = await self.service.reverse_geocode(latitude, longitude)
        
        # Vérifier que la localisation est correcte
        assert location["latitude"] == latitude
        assert location["longitude"] == longitude
        assert "country" in location
        assert "country_code" in location
        assert "city" in location
        assert "formatted_address" in location
    
    @pytest.mark.asyncio
    async def test_get_distance(self):
        """
        Test du calcul de la distance entre deux adresses.
        """
        # Paramètres des adresses
        origin = "Paris, France"
        destination = "Lyon, France"
        
        # Calculer la distance
        distance = await self.service.get_distance(origin, destination)
        
        # Vérifier que la distance est correcte
        assert distance["origin"] == origin
        assert distance["destination"] == destination
        assert "distance" in distance
        assert "duration" in distance
        assert "mode" in distance
    
    @pytest.mark.asyncio
    async def test_get_places_nearby(self):
        """
        Test de la recherche de lieux à proximité.
        """
        # Paramètres de la recherche
        location = "Paris, France"
        radius = 1000
        type = "restaurant"
        
        # Rechercher des lieux à proximité
        places = await self.service.get_places_nearby(location, radius, type)
        
        # Vérifier que les lieux sont corrects
        assert isinstance(places, list)
        assert len(places) > 0
        assert all("name" in place for place in places)
        assert all("address" in place for place in places)
        assert all("latitude" in place for place in places)
        assert all("longitude" in place for place in places)
    
    def test_generate_static_map_url(self):
        """
        Test de la génération d'une URL pour une carte statique.
        """
        # Paramètres de la carte
        center = "Paris, France"
        zoom = 14
        size = (600, 400)
        markers = [
            {
                "color": "red",
                "label": "A",
                "location": "Paris, France"
            }
        ]
        
        # Générer l'URL
        url = self.service.generate_static_map_url(center, zoom, size, markers)
        
        # Vérifier que l'URL est correcte
        assert isinstance(url, str)
        assert "https://maps.googleapis.com/maps/api/staticmap" in url
        assert f"center={center}" in url
        assert f"zoom={zoom}" in url
        assert f"size={size[0]}x{size[1]}" in url
        assert "key=" in url
        assert "markers" in url
    
    def test_generate_directions_url(self):
        """
        Test de la génération d'une URL pour un itinéraire.
        """
        # Paramètres de l'itinéraire
        origin = "Paris, France"
        destination = "Lyon, France"
        mode = "driving"
        
        # Générer l'URL
        url = self.service.generate_directions_url(origin, destination, mode)
        
        # Vérifier que l'URL est correcte
        assert isinstance(url, str)
        assert "https://www.google.com/maps/dir/" in url
        assert f"origin={origin}" in url
        assert f"destination={destination}" in url
        assert f"travelmode={mode}" in url
