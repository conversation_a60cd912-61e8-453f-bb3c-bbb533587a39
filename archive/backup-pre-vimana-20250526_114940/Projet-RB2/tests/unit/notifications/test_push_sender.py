"""
Tests unitaires pour le service d'envoi de notifications push.
"""

import pytest
from unittest.mock import patch, MagicMock

from src.notifications.push_sender import PushSender

class TestPushSender:
    """
    Tests pour le service d'envoi de notifications push.
    """
    
    def setup_method(self):
        """
        Configuration avant chaque test.
        """
        self.push_sender = PushSender()
        self.push_sender.dev_mode = True  # Utiliser le mode développement pour les tests
    
    def test_init(self):
        """
        Test de l'initialisation du service.
        """
        assert self.push_sender.credentials_file is not None
        assert self.push_sender.app_name == "retreat-and-be"
        assert self.push_sender.dev_mode is True
        assert self.push_sender.app is None
    
    @pytest.mark.asyncio
    async def test_send_push(self):
        """
        Test de l'envoi d'une notification push.
        """
        # Paramètres de la notification
        token = "test_token"
        title = "Test Title"
        body = "Test Body"
        data = {"key": "value"}
        
        # Envoyer la notification
        result = await self.push_sender.send_push(token, title, body, data)
        
        # Vérifier que le résultat est correct
        assert result["success"] is True
        assert result["token"] == token
        assert result["title"] == title
    
    @pytest.mark.asyncio
    async def test_send_push_to_topic(self):
        """
        Test de l'envoi d'une notification push à un sujet.
        """
        # Paramètres de la notification
        topic = "test_topic"
        title = "Test Title"
        body = "Test Body"
        data = {"key": "value"}
        
        # Envoyer la notification
        result = await self.push_sender.send_push_to_topic(topic, title, body, data)
        
        # Vérifier que le résultat est correct
        assert result["success"] is True
        assert result["topic"] == topic
        assert result["title"] == title
    
    @pytest.mark.asyncio
    async def test_send_push_to_multiple_tokens(self):
        """
        Test de l'envoi d'une notification push à plusieurs appareils.
        """
        # Paramètres de la notification
        tokens = ["token1", "token2", "token3"]
        title = "Test Title"
        body = "Test Body"
        data = {"key": "value"}
        
        # Envoyer la notification
        result = await self.push_sender.send_push_to_multiple_tokens(tokens, title, body, data)
        
        # Vérifier que le résultat est correct
        assert result["success"] is True
        assert result["tokens"] == tokens
        assert result["title"] == title
        assert result["success_count"] == len(tokens)
        assert result["failure_count"] == 0
    
    @pytest.mark.asyncio
    async def test_subscribe_to_topic(self):
        """
        Test de l'abonnement à un sujet.
        """
        # Paramètres de l'abonnement
        token = "test_token"
        topic = "test_topic"
        
        # S'abonner au sujet
        result = await self.push_sender.subscribe_to_topic(token, topic)
        
        # Vérifier que le résultat est correct
        assert result["success"] is True
        assert result["token"] == token
        assert result["topic"] == topic
    
    @pytest.mark.asyncio
    async def test_unsubscribe_from_topic(self):
        """
        Test du désabonnement d'un sujet.
        """
        # Paramètres du désabonnement
        token = "test_token"
        topic = "test_topic"
        
        # Se désabonner du sujet
        result = await self.push_sender.unsubscribe_from_topic(token, topic)
        
        # Vérifier que le résultat est correct
        assert result["success"] is True
        assert result["token"] == token
        assert result["topic"] == topic
    
    @pytest.mark.asyncio
    async def test_send_push_with_firebase(self):
        """
        Test de l'envoi d'une notification push avec Firebase.
        """
        # Désactiver le mode développement
        self.push_sender.dev_mode = False
        
        # Paramètres de la notification
        token = "test_token"
        title = "Test Title"
        body = "Test Body"
        data = {"key": "value"}
        
        # Mocker Firebase
        with patch("src.notifications.push_sender.messaging") as mock_messaging, \
             patch("src.notifications.push_sender.initialize_app") as mock_initialize_app, \
             patch("src.notifications.push_sender.credentials") as mock_credentials, \
             patch("src.notifications.push_sender.os.path.exists", return_value=True):
            
            # Mocker le retour de messaging.send
            mock_messaging.send.return_value = "message_id"
            
            # Mocker le retour de initialize_app
            mock_app = MagicMock()
            mock_initialize_app.return_value = mock_app
            
            # Définir l'application
            self.push_sender.app = mock_app
            
            # Envoyer la notification
            result = await self.push_sender.send_push(token, title, body, data)
            
            # Vérifier que le résultat est correct
            assert result["success"] is True
            assert result["token"] == token
            assert result["title"] == title
            assert result["response"] == "message_id"
            
            # Vérifier que les fonctions ont été appelées
            mock_messaging.Message.assert_called_once()
            mock_messaging.Notification.assert_called_once_with(title=title, body=body)
            mock_messaging.send.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_push_with_error(self):
        """
        Test de l'envoi d'une notification push avec une erreur.
        """
        # Désactiver le mode développement
        self.push_sender.dev_mode = False
        
        # Paramètres de la notification
        token = "test_token"
        title = "Test Title"
        body = "Test Body"
        data = {"key": "value"}
        
        # Mocker Firebase pour lever une exception
        with patch("src.notifications.push_sender.messaging") as mock_messaging, \
             patch("src.notifications.push_sender.initialize_app") as mock_initialize_app, \
             patch("src.notifications.push_sender.credentials") as mock_credentials, \
             patch("src.notifications.push_sender.os.path.exists", return_value=True), \
             patch("src.notifications.push_sender.logger") as mock_logger:
            
            # Mocker le retour de messaging.send pour lever une exception
            mock_messaging.send.side_effect = Exception("Test error")
            
            # Mocker le retour de initialize_app
            mock_app = MagicMock()
            mock_initialize_app.return_value = mock_app
            
            # Définir l'application
            self.push_sender.app = mock_app
            
            # Envoyer la notification
            result = await self.push_sender.send_push(token, title, body, data)
            
            # Vérifier que le résultat est correct
            assert result["success"] is False
            assert result["token"] == token
            assert result["title"] == title
            assert "error" in result
            assert result["error"] == "Test error"
            
            # Vérifier que les fonctions ont été appelées
            mock_messaging.Message.assert_called_once()
            mock_messaging.Notification.assert_called_once_with(title=title, body=body)
            mock_messaging.send.assert_called_once()
            mock_logger.error.assert_called_once()
