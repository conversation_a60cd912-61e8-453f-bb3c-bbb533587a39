"""
Tests unitaires pour le service d'internationalisation.
"""

import os
import json
import pytest
from unittest.mock import patch, MagicMock

from src.i18n.i18n_service import I18nService
from src.i18n.translations import get_translation, get_supported_languages

class TestI18nService:
    """
    Tests pour le service d'internationalisation.
    """
    
    def setup_method(self):
        """
        Configuration avant chaque test.
        """
        self.i18n_service = I18nService(default_language="fr")
    
    def test_init(self):
        """
        Test de l'initialisation du service.
        """
        assert self.i18n_service.default_language == "fr"
        assert isinstance(self.i18n_service.supported_languages, dict)
        assert "fr" in self.i18n_service.supported_languages
        assert "en" in self.i18n_service.supported_languages
    
    def test_init_with_unsupported_language(self):
        """
        Test de l'initialisation avec une langue non supportée.
        """
        with patch("src.i18n.i18n_service.logger") as mock_logger:
            i18n_service = I18nService(default_language="xx")
            
            # Vérifier que le logger a été appelé avec un avertissement
            mock_logger.warning.assert_called_once()
            
            # Vérifier que la langue par défaut a été remplacée par "fr"
            assert i18n_service.default_language == "fr"
    
    def test_translate(self):
        """
        Test de la traduction d'une clé.
        """
        # Mocker la fonction get_translation
        with patch("src.i18n.i18n_service.get_translation") as mock_get_translation:
            mock_get_translation.return_value = "Bienvenue sur Retreat & Be"
            
            # Traduire une clé
            translation = self.i18n_service.translate("common.welcome")
            
            # Vérifier que la fonction get_translation a été appelée avec les bons paramètres
            mock_get_translation.assert_called_once_with("common.welcome", "fr")
            
            # Vérifier que la traduction est correcte
            assert translation == "Bienvenue sur Retreat & Be"
    
    def test_translate_with_language(self):
        """
        Test de la traduction d'une clé avec une langue spécifiée.
        """
        # Mocker la fonction get_translation
        with patch("src.i18n.i18n_service.get_translation") as mock_get_translation:
            mock_get_translation.return_value = "Welcome to Retreat & Be"
            
            # Traduire une clé avec une langue spécifiée
            translation = self.i18n_service.translate("common.welcome", "en")
            
            # Vérifier que la fonction get_translation a été appelée avec les bons paramètres
            mock_get_translation.assert_called_once_with("common.welcome", "en")
            
            # Vérifier que la traduction est correcte
            assert translation == "Welcome to Retreat & Be"
    
    def test_translate_with_unsupported_language(self):
        """
        Test de la traduction d'une clé avec une langue non supportée.
        """
        # Mocker la fonction get_translation
        with patch("src.i18n.i18n_service.get_translation") as mock_get_translation, \
             patch("src.i18n.i18n_service.logger") as mock_logger:
            mock_get_translation.return_value = "Bienvenue sur Retreat & Be"
            
            # Traduire une clé avec une langue non supportée
            translation = self.i18n_service.translate("common.welcome", "xx")
            
            # Vérifier que le logger a été appelé avec un avertissement
            mock_logger.warning.assert_called_once()
            
            # Vérifier que la fonction get_translation a été appelée avec les bons paramètres
            mock_get_translation.assert_called_once_with("common.welcome", "fr")
            
            # Vérifier que la traduction est correcte
            assert translation == "Bienvenue sur Retreat & Be"
    
    def test_translate_with_params(self):
        """
        Test de la traduction d'une clé avec des paramètres.
        """
        # Mocker la fonction get_translation
        with patch("src.i18n.i18n_service.get_translation") as mock_get_translation:
            mock_get_translation.return_value = "Doit contenir au moins {min} caractères"
            
            # Traduire une clé avec des paramètres
            translation = self.i18n_service.translate("error.minLength", "fr", {"min": 8})
            
            # Vérifier que la fonction get_translation a été appelée avec les bons paramètres
            mock_get_translation.assert_called_once_with("error.minLength", "fr")
            
            # Vérifier que la traduction est correcte
            assert translation == "Doit contenir au moins 8 caractères"
    
    def test_translate_with_missing_params(self):
        """
        Test de la traduction d'une clé avec des paramètres manquants.
        """
        # Mocker la fonction get_translation
        with patch("src.i18n.i18n_service.get_translation") as mock_get_translation, \
             patch("src.i18n.i18n_service.logger") as mock_logger:
            mock_get_translation.return_value = "Doit contenir au moins {min} caractères"
            
            # Traduire une clé avec des paramètres manquants
            translation = self.i18n_service.translate("error.minLength", "fr", {})
            
            # Vérifier que le logger a été appelé avec un avertissement
            mock_logger.warning.assert_called_once()
            
            # Vérifier que la fonction get_translation a été appelée avec les bons paramètres
            mock_get_translation.assert_called_once_with("error.minLength", "fr")
            
            # Vérifier que la traduction est correcte (non formatée)
            assert translation == "Doit contenir au moins {min} caractères"
    
    def test_get_translations(self):
        """
        Test de la récupération des traductions pour une langue.
        """
        # Créer un fichier de traduction temporaire
        translations = {
            "common.welcome": "Bienvenue sur Retreat & Be",
            "common.error": "Une erreur est survenue"
        }
        
        # Mocker la fonction open
        mock_open = MagicMock()
        mock_open.return_value.__enter__.return_value.read.return_value = json.dumps(translations)
        
        with patch("builtins.open", mock_open), \
             patch("os.path.exists", return_value=True), \
             patch("json.load", return_value=translations):
            
            # Récupérer les traductions
            result = self.i18n_service.get_translations("fr")
            
            # Vérifier que les traductions sont correctes
            assert result == translations
    
    def test_get_translations_with_unsupported_language(self):
        """
        Test de la récupération des traductions pour une langue non supportée.
        """
        # Mocker la fonction open
        with patch("src.i18n.i18n_service.logger") as mock_logger:
            # Récupérer les traductions pour une langue non supportée
            self.i18n_service.get_translations("xx")
            
            # Vérifier que le logger a été appelé avec un avertissement
            mock_logger.warning.assert_called_once()
    
    def test_get_supported_languages(self):
        """
        Test de la récupération des langues supportées.
        """
        # Récupérer les langues supportées
        languages = self.i18n_service.get_supported_languages()
        
        # Vérifier que les langues sont correctes
        assert isinstance(languages, list)
        assert len(languages) > 0
        assert all(isinstance(lang, dict) for lang in languages)
        assert all("code" in lang and "name" in lang for lang in languages)
        assert any(lang["code"] == "fr" for lang in languages)
        assert any(lang["code"] == "en" for lang in languages)


class TestTranslations:
    """
    Tests pour les fonctions de traduction.
    """
    
    def test_get_supported_languages(self):
        """
        Test de la récupération des langues supportées.
        """
        # Récupérer les langues supportées
        languages = get_supported_languages()
        
        # Vérifier que les langues sont correctes
        assert isinstance(languages, dict)
        assert "fr" in languages
        assert "en" in languages
        assert languages["fr"] == "Français"
        assert languages["en"] == "English"
    
    def test_get_translation(self):
        """
        Test de la récupération d'une traduction.
        """
        # Créer un fichier de traduction temporaire
        translations = {
            "common.welcome": "Bienvenue sur Retreat & Be",
            "common.error": "Une erreur est survenue"
        }
        
        # Mocker la fonction _load_translations
        with patch("src.i18n.translations._load_translations", return_value=translations):
            # Récupérer une traduction
            translation = get_translation("common.welcome", "fr")
            
            # Vérifier que la traduction est correcte
            assert translation == "Bienvenue sur Retreat & Be"
    
    def test_get_translation_missing_key(self):
        """
        Test de la récupération d'une traduction pour une clé manquante.
        """
        # Créer un fichier de traduction temporaire
        translations = {
            "common.welcome": "Bienvenue sur Retreat & Be",
            "common.error": "Une erreur est survenue"
        }
        
        # Mocker la fonction _load_translations
        with patch("src.i18n.translations._load_translations", return_value=translations):
            # Récupérer une traduction pour une clé manquante
            translation = get_translation("common.missing", "fr")
            
            # Vérifier que la traduction est la clé elle-même
            assert translation == "common.missing"
    
    def test_get_translation_fallback_to_fr(self):
        """
        Test de la récupération d'une traduction avec fallback vers le français.
        """
        # Créer des fichiers de traduction temporaires
        translations_fr = {
            "common.welcome": "Bienvenue sur Retreat & Be",
            "common.error": "Une erreur est survenue"
        }
        
        translations_en = {
            "common.welcome": "Welcome to Retreat & Be"
        }
        
        # Mocker la fonction _load_translations
        def mock_load_translations(language):
            if language == "fr":
                return translations_fr
            elif language == "en":
                return translations_en
            return {}
        
        with patch("src.i18n.translations._load_translations", side_effect=mock_load_translations):
            # Récupérer une traduction qui n'existe pas en anglais mais existe en français
            translation = get_translation("common.error", "en")
            
            # Vérifier que la traduction est celle du français
            assert translation == "Une erreur est survenue"
