"""
Tests pour le repository Partner.
"""

import pytest
from datetime import datetime

from src.models.partner import Partner, PartnerType, PartnerStatus, PartnerSpecialty
from src.database.repositories import PartnerRepository

@pytest.fixture
def partner_repo(test_db_path):
    """
    Fixture pour le repository Partner.
    """
    return PartnerRepository()

@pytest.fixture
def sample_partner():
    """
    Fixture pour un partenaire de test.
    """
    return Partner(
        id="partner_test_123",
        name="Test Yoga Studio",
        email="<EMAIL>",
        phone="+33123456789",
        partner_type=PartnerType.WELLNESS_EXPERT,
        status=PartnerStatus.ACTIVE,
        specialties=[PartnerSpecialty.YOGA, PartnerSpecialty.MEDITATION],
        description="Un studio de yoga de test",
        website="https://test-yogastudio.com",
        languages=["fr", "en"],
        location={"country": "France", "region": "Provence", "city": "Aix-en-Provence"},
        created_at=datetime.now(),
        updated_at=datetime.now()
    )

def test_create_partner(partner_repo, sample_partner, clean_db):
    """
    Test la création d'un partenaire dans la base de données.
    """
    # Créer le partenaire
    created_partner = partner_repo.create(sample_partner)
    
    # Vérifier que le partenaire a été créé
    assert created_partner.id == sample_partner.id
    assert created_partner.name == sample_partner.name
    assert created_partner.email == sample_partner.email
    
    # Récupérer le partenaire depuis la base de données
    retrieved_partner = partner_repo.get_by_id(sample_partner.id)
    
    # Vérifier que le partenaire récupéré correspond
    assert retrieved_partner is not None
    assert retrieved_partner.id == sample_partner.id
    assert retrieved_partner.name == sample_partner.name
    assert retrieved_partner.email == sample_partner.email
    assert retrieved_partner.partner_type == sample_partner.partner_type
    assert retrieved_partner.status == sample_partner.status
    assert len(retrieved_partner.specialties) == len(sample_partner.specialties)
    assert retrieved_partner.specialties[0] == sample_partner.specialties[0]

def test_update_partner(partner_repo, sample_partner, clean_db):
    """
    Test la mise à jour d'un partenaire dans la base de données.
    """
    # Créer le partenaire
    partner_repo.create(sample_partner)
    
    # Modifier le partenaire
    sample_partner.name = "Updated Yoga Studio"
    sample_partner.status = PartnerStatus.VERIFIED
    
    # Mettre à jour le partenaire
    updated_partner = partner_repo.update(sample_partner)
    
    # Vérifier que le partenaire a été mis à jour
    assert updated_partner.name == "Updated Yoga Studio"
    assert updated_partner.status == PartnerStatus.VERIFIED
    
    # Récupérer le partenaire depuis la base de données
    retrieved_partner = partner_repo.get_by_id(sample_partner.id)
    
    # Vérifier que le partenaire récupéré correspond
    assert retrieved_partner is not None
    assert retrieved_partner.name == "Updated Yoga Studio"
    assert retrieved_partner.status == PartnerStatus.VERIFIED

def test_delete_partner(partner_repo, sample_partner, clean_db):
    """
    Test la suppression d'un partenaire dans la base de données.
    """
    # Créer le partenaire
    partner_repo.create(sample_partner)
    
    # Vérifier que le partenaire existe
    assert partner_repo.get_by_id(sample_partner.id) is not None
    
    # Supprimer le partenaire
    result = partner_repo.delete(sample_partner.id)
    
    # Vérifier que la suppression a réussi
    assert result is True
    
    # Vérifier que le partenaire n'existe plus
    assert partner_repo.get_by_id(sample_partner.id) is None

def test_find_by_type(partner_repo, sample_partner, clean_db):
    """
    Test la recherche de partenaires par type.
    """
    # Créer le partenaire
    partner_repo.create(sample_partner)
    
    # Rechercher des partenaires par type
    partners = partner_repo.find_by_type(PartnerType.WELLNESS_EXPERT)
    
    # Vérifier que le partenaire a été trouvé
    assert len(partners) == 1
    assert partners[0].id == sample_partner.id
    
    # Rechercher des partenaires par un autre type
    partners = partner_repo.find_by_type(PartnerType.ACCOMMODATION)
    
    # Vérifier qu'aucun partenaire n'a été trouvé
    assert len(partners) == 0

def test_find_by_specialty(partner_repo, sample_partner, clean_db):
    """
    Test la recherche de partenaires par spécialité.
    """
    # Créer le partenaire
    partner_repo.create(sample_partner)
    
    # Rechercher des partenaires par spécialité
    partners = partner_repo.find_by_specialty(PartnerSpecialty.YOGA)
    
    # Vérifier que le partenaire a été trouvé
    assert len(partners) == 1
    assert partners[0].id == sample_partner.id
    
    # Rechercher des partenaires par une autre spécialité
    partners = partner_repo.find_by_specialty(PartnerSpecialty.COOKING)
    
    # Vérifier qu'aucun partenaire n'a été trouvé
    assert len(partners) == 0

def test_find_by_location(partner_repo, sample_partner, clean_db):
    """
    Test la recherche de partenaires par localisation.
    """
    # Créer le partenaire
    partner_repo.create(sample_partner)
    
    # Rechercher des partenaires par pays
    partners = partner_repo.find_by_location("France")
    
    # Vérifier que le partenaire a été trouvé
    assert len(partners) == 1
    assert partners[0].id == sample_partner.id
    
    # Rechercher des partenaires par pays et région
    partners = partner_repo.find_by_location("France", "Provence")
    
    # Vérifier que le partenaire a été trouvé
    assert len(partners) == 1
    assert partners[0].id == sample_partner.id
    
    # Rechercher des partenaires par un autre pays
    partners = partner_repo.find_by_location("Espagne")
    
    # Vérifier qu'aucun partenaire n'a été trouvé
    assert len(partners) == 0
