"""
Tests pour le service de notification.
"""

import pytest
from unittest.mock import AsyncMock, patch

from src.notifications.notification_service import NotificationService
from src.notifications.notification_types import NotificationType

@pytest.fixture
def notification_service():
    """
    Fixture pour le service de notification.
    """
    # Créer des mocks pour les services d'envoi
    email_sender = AsyncMock()
    email_sender.send_email = AsyncMock(return_value={"success": True})
    
    sms_sender = AsyncMock()
    sms_sender.send_sms = AsyncMock(return_value={"success": True})
    
    # Créer le service de notification avec les mocks
    return NotificationService(email_sender, sms_sender)

@pytest.mark.asyncio
async def test_send_notification_email(notification_service):
    """
    Test l'envoi d'une notification par email.
    """
    # Données de test
    recipient = {
        "id": "client_123",
        "email": "<EMAIL>",
        "name": "<PERSON>"
    }
    
    data = {
        "recipient_name": "<PERSON>",
        "retreat_title": "Retraite de Yoga en Provence",
        "start_date": "2023-07-15",
        "end_date": "2023-07-22",
        "location": "Provence, France"
    }
    
    # Envoyer la notification
    result = await notification_service.send_notification(
        recipient,
        NotificationType.RETREAT_CREATED,
        data,
        channels=["email"]
    )
    
    # Vérifier que l'email a été envoyé
    notification_service.email_sender.send_email.assert_called_once()
    
    # Vérifier que le SMS n'a pas été envoyé
    notification_service.sms_sender.send_sms.assert_not_called()
    
    # Vérifier le résultat
    assert result["recipient_id"] == "client_123"
    assert result["notification_type"] == "retreat_created"
    assert "email" in result["results"]
    assert result["results"]["email"]["success"] is True

@pytest.mark.asyncio
async def test_send_notification_sms(notification_service):
    """
    Test l'envoi d'une notification par SMS.
    """
    # Données de test
    recipient = {
        "id": "client_123",
        "phone": "+33123456789",
        "name": "John Doe"
    }
    
    data = {
        "recipient_name": "John Doe",
        "retreat_title": "Retraite de Yoga en Provence",
        "start_date": "2023-07-15",
        "total_price": 1200,
        "currency": "EUR"
    }
    
    # Envoyer la notification
    result = await notification_service.send_notification(
        recipient,
        NotificationType.BOOKING_CREATED,
        data,
        channels=["sms"]
    )
    
    # Vérifier que le SMS a été envoyé
    notification_service.sms_sender.send_sms.assert_called_once()
    
    # Vérifier que l'email n'a pas été envoyé
    notification_service.email_sender.send_email.assert_not_called()
    
    # Vérifier le résultat
    assert result["recipient_id"] == "client_123"
    assert result["notification_type"] == "booking_created"
    assert "sms" in result["results"]
    assert result["results"]["sms"]["success"] is True

@pytest.mark.asyncio
async def test_send_notification_both_channels(notification_service):
    """
    Test l'envoi d'une notification par email et SMS.
    """
    # Données de test
    recipient = {
        "id": "client_123",
        "email": "<EMAIL>",
        "phone": "+33123456789",
        "name": "John Doe"
    }
    
    data = {
        "recipient_name": "John Doe",
        "retreat_title": "Retraite de Yoga en Provence",
        "start_date": "2023-07-15"
    }
    
    # Envoyer la notification
    result = await notification_service.send_notification(
        recipient,
        NotificationType.RETREAT_REMINDER,
        data,
        channels=["email", "sms"]
    )
    
    # Vérifier que l'email et le SMS ont été envoyés
    notification_service.email_sender.send_email.assert_called_once()
    notification_service.sms_sender.send_sms.assert_called_once()
    
    # Vérifier le résultat
    assert result["recipient_id"] == "client_123"
    assert result["notification_type"] == "retreat_reminder"
    assert "email" in result["results"]
    assert "sms" in result["results"]
    assert result["results"]["email"]["success"] is True
    assert result["results"]["sms"]["success"] is True

@pytest.mark.asyncio
async def test_send_bulk_notifications(notification_service):
    """
    Test l'envoi de notifications en masse.
    """
    # Données de test
    recipients = [
        {
            "id": "client_123",
            "email": "<EMAIL>",
            "name": "John Doe"
        },
        {
            "id": "client_456",
            "email": "<EMAIL>",
            "name": "Jane Smith"
        }
    ]
    
    data = {
        "retreat_title": "Retraite de Yoga en Provence",
        "start_date": "2023-07-15",
        "end_date": "2023-07-22",
        "location": "Provence, France"
    }
    
    # Envoyer les notifications
    results = await notification_service.send_bulk_notifications(
        recipients,
        NotificationType.RETREAT_CREATED,
        data,
        channels=["email"]
    )
    
    # Vérifier que les emails ont été envoyés
    assert notification_service.email_sender.send_email.call_count == 2
    
    # Vérifier les résultats
    assert len(results) == 2
    assert results[0]["recipient_id"] == "client_123"
    assert results[1]["recipient_id"] == "client_456"
    assert results[0]["notification_type"] == "retreat_created"
    assert results[1]["notification_type"] == "retreat_created"
