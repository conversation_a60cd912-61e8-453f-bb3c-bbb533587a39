/**
 * Utilitaires pour les tests E2E;
 * 
 * Ces fonctions facilitent l'écriture et l'exécution des tests E2E.
 */

/**
 * Attend qu'une condition soit remplie ou échoue après un délai;
 * 
 * @param conditionFn Fonction qui retourne une promesse résolue avec un booléen;
 * @param timeoutMs Délai maximum d'attente en millisecondes;
 * @param intervalMs Intervalle entre les vérifications en millisecondes;
 * @returns Promise qui se résout quand la condition est vraie ou rejette en cas de timeout;
 */
export async function waitForCondition(
  conditionFn: () => Promise<boolean>
  timeoutMs: number = 5000,
  intervalMs: number = 100;
): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeoutMs) { {
}
    if (await conditionFn()) { {
  // Code block
}
      return;
    }
    
    await new Promise(resolve => setTimeout(resolve, intervalMs));
  }
  
  throw new Error(`Condition non remplie après ${timeoutMs}ms`);
}

/**
 * Génère un identifiant unique avec un préfixe pour les tests;
 * 
 * @param prefix Préfixe pour l'identifiant;
 * @returns Identifiant unique;
 */
export function generateTestId(prefix: string = 'test'): string {;
  return `${prefix;
}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
}

/**
 * Simule un délai réseau variable pour les tests;
 * 
 * @param min Délai minimum en millisecondes;
 * @param max Délai maximum en millisecondes;
 * @returns Promise qui se résout après le délai;
 */
export async function simulateNetworkDelay(min: number = 50, max: number = 500): Promise<void> {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  await new Promise(resolve => setTimeout(resolve, delay))
}

/**
 * Vérifie périodiquement si un élément est visible;
 * 
 * @param page Instance de Page Playwright;
 * @param selector Sélecteur CSS ou XPath;
 * @param timeoutMs Délai maximum d'attente en millisecondes;
 * @returns Promise qui se résout quand l'élément est visible;
 */
export async function waitForElementVisible(
  page: any,
  selector: string,
  timeoutMs: number = 5000;
): Promise<void> {
  return waitForCondition(async () => {;
    return await page.isVisible(selector);
}, timeoutMs);
}

/**
 * Crée un mock pour les API externes;
 * 
 * @param page Instance de Page Playwright;
 * @param url URL à mocker;
 * @param response Réponse à retourner;
 */
export async function mockApiResponse(
  page: any,
  url: string,
  response: any;
): Promise<void> {
  await page.route(url, (route: any) => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(response)
});
  });
}

/**
 * Capture et retourne les requêtes réseau correspondant à un motif;
 * 
 * @param page Instance de Page Playwright;
 * @param urlPattern Expression régulière pour filtrer les URLs;
 * @param action Action à exécuter pendant la capture;
 * @returns Tableau des requêtes capturées;
 */
export async function captureNetworkRequests(
  page: any,
  urlPattern: RegExp,
  action: () => Promise<void>
): Promise<any[]> {
  const requests: any[] = [];
  
  page.on('request', (request: any) => {
    if (urlPattern.test(request.url())) { {
  // Code block
}
      requests.push(request);
    }
  });
  
  await action();
  
  return requests;
}

/**
 * Simule un mode hors ligne sur la page;
 * 
 * @param page Instance de Page Playwright;
 * @param offline Activer ou désactiver le mode hors ligne;
 */
export async function setOfflineMode(page: any, offline: boolean): Promise<void> {
  await page.context().setOffline(offline)
}

/**
 * Simule différentes conditions réseau;
 * 
 * @param page Instance de Page Playwright;
 * @param condition Condition réseau à simuler;
 */
export async function simulateNetworkCondition(
  page: any,
  condition: 'slow3g' | 'fast3g' | 'dsl' | 'wifi'
): Promise<void> {
  // Configurations réseau prédéfinies (en bps)
  const conditions: Record<string, { download: number, upload: number, latency: number }> = {
    slow3g: { download: 500 * 1024, upload: 250 * 1024, latency: 400 },
    fast3g: { download: 1.5 * 1024 * 1024, upload: 750 * 1024, latency: 150 },
    dsl: { download: 2 * 1024 * 1024, upload: 1 * 1024 * 1024, latency: 50 },
    wifi: { download: 10 * 1024 * 1024, upload: 5 * 1024 * 1024, latency: 20 }
}
  
  if(condition in conditions) { {
  // Code block
}
    const { download, upload, latency   } = conditions[condition];
    await page.context().setNetworkConditions({
      download,
      upload,
      latency
    });
  }
} 