"""
Tests d'intégration pour les routes d'internationalisation.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from src.api.app import create_app
from src.i18n.i18n_service import I18nService

@pytest.fixture
def client():
    """
    Fixture pour créer un client de test.
    """
    app = create_app()
    return TestClient(app)

@pytest.fixture
def mock_i18n_service():
    """
    Fixture pour mocker le service d'internationalisation.
    """
    with patch("src.api.routes.i18n_routes.i18n_service") as mock_service:
        yield mock_service

class TestI18nRoutes:
    """
    Tests pour les routes d'internationalisation.
    """
    
    def test_get_languages(self, client, mock_i18n_service):
        """
        Test de la récupération des langues supportées.
        """
        # Mocker la méthode get_supported_languages
        mock_i18n_service.get_supported_languages.return_value = [
            {"code": "fr", "name": "Français"},
            {"code": "en", "name": "English"}
        ]
        
        # Faire une requête GET /api/i18n/languages
        response = client.get("/api/i18n/languages")
        
        # Vérifier que la réponse est correcte
        assert response.status_code == 200
        assert response.json() == {
            "status": "success",
            "message": "Langues récupérées avec succès",
            "languages": [
                {"code": "fr", "name": "Français"},
                {"code": "en", "name": "English"}
            ]
        }
        
        # Vérifier que la méthode get_supported_languages a été appelée
        mock_i18n_service.get_supported_languages.assert_called_once()
    
    def test_get_languages_error(self, client, mock_i18n_service):
        """
        Test de la récupération des langues supportées avec une erreur.
        """
        # Mocker la méthode get_supported_languages pour lever une exception
        mock_i18n_service.get_supported_languages.side_effect = Exception("Erreur test")
        
        # Faire une requête GET /api/i18n/languages
        response = client.get("/api/i18n/languages")
        
        # Vérifier que la réponse est une erreur 500
        assert response.status_code == 500
        assert response.json() == {
            "detail": "Erreur lors de la récupération des langues"
        }
        
        # Vérifier que la méthode get_supported_languages a été appelée
        mock_i18n_service.get_supported_languages.assert_called_once()
    
    def test_get_translations(self, client, mock_i18n_service):
        """
        Test de la récupération des traductions pour une langue.
        """
        # Mocker la méthode get_translations
        mock_i18n_service.get_translations.return_value = {
            "common.welcome": "Bienvenue sur Retreat & Be",
            "common.error": "Une erreur est survenue"
        }
        
        # Faire une requête GET /api/i18n/translations/fr
        response = client.get("/api/i18n/translations/fr")
        
        # Vérifier que la réponse est correcte
        assert response.status_code == 200
        assert response.json() == {
            "status": "success",
            "message": "Traductions récupérées avec succès",
            "language": "fr",
            "translations": {
                "common.welcome": "Bienvenue sur Retreat & Be",
                "common.error": "Une erreur est survenue"
            }
        }
        
        # Vérifier que la méthode get_translations a été appelée avec les bons paramètres
        mock_i18n_service.get_translations.assert_called_once_with("fr")
    
    def test_get_translations_error(self, client, mock_i18n_service):
        """
        Test de la récupération des traductions pour une langue avec une erreur.
        """
        # Mocker la méthode get_translations pour lever une exception
        mock_i18n_service.get_translations.side_effect = Exception("Erreur test")
        
        # Faire une requête GET /api/i18n/translations/fr
        response = client.get("/api/i18n/translations/fr")
        
        # Vérifier que la réponse est une erreur 500
        assert response.status_code == 500
        assert response.json() == {
            "detail": "Erreur lors de la récupération des traductions"
        }
        
        # Vérifier que la méthode get_translations a été appelée avec les bons paramètres
        mock_i18n_service.get_translations.assert_called_once_with("fr")
    
    def test_translate(self, client, mock_i18n_service):
        """
        Test de la traduction d'une clé.
        """
        # Mocker la méthode translate
        mock_i18n_service.translate.return_value = "Bienvenue sur Retreat & Be"
        
        # Faire une requête GET /api/i18n/translate?key=common.welcome&language=fr
        response = client.get("/api/i18n/translate?key=common.welcome&language=fr")
        
        # Vérifier que la réponse est correcte
        assert response.status_code == 200
        assert response.json() == {
            "status": "success",
            "message": "Traduction réussie",
            "key": "common.welcome",
            "language": "fr",
            "translation": "Bienvenue sur Retreat & Be"
        }
        
        # Vérifier que la méthode translate a été appelée avec les bons paramètres
        mock_i18n_service.translate.assert_called_once_with("common.welcome", "fr", None)
    
    def test_translate_with_params(self, client, mock_i18n_service):
        """
        Test de la traduction d'une clé avec des paramètres.
        """
        # Mocker la méthode translate
        mock_i18n_service.translate.return_value = "Doit contenir au moins 8 caractères"
        
        # Faire une requête GET /api/i18n/translate?key=error.minLength&language=fr&params={"min":8}
        response = client.get("/api/i18n/translate?key=error.minLength&language=fr&params={\"min\":8}")
        
        # Vérifier que la réponse est correcte
        assert response.status_code == 200
        assert response.json() == {
            "status": "success",
            "message": "Traduction réussie",
            "key": "error.minLength",
            "language": "fr",
            "translation": "Doit contenir au moins 8 caractères"
        }
        
        # Vérifier que la méthode translate a été appelée avec les bons paramètres
        mock_i18n_service.translate.assert_called_once_with("error.minLength", "fr", {"min": 8})
    
    def test_translate_error(self, client, mock_i18n_service):
        """
        Test de la traduction d'une clé avec une erreur.
        """
        # Mocker la méthode translate pour lever une exception
        mock_i18n_service.translate.side_effect = Exception("Erreur test")
        
        # Faire une requête GET /api/i18n/translate?key=common.welcome&language=fr
        response = client.get("/api/i18n/translate?key=common.welcome&language=fr")
        
        # Vérifier que la réponse est une erreur 500
        assert response.status_code == 500
        assert response.json() == {
            "detail": "Erreur lors de la traduction"
        }
        
        # Vérifier que la méthode translate a été appelée avec les bons paramètres
        mock_i18n_service.translate.assert_called_once_with("common.welcome", "fr", None)
