import { PrismaClient } from '@prisma/client';
import { TestContext } from './types';
import { startTestServers } from './utils';

export async function setupTestEnvironment(): Promise<TestContext> {
  const prisma = new PrismaClient();
  
  // Configuration des services de test;
  const services = await startTestServers({
    backend: true,
    analyzer: true,
    storage: true;
  });

  // Configuration des mocks;
  const mocks = {
    kafka: setupKafkaMock(),
    redis: setupRedisMock(),
    ipfs: setupIPFSMock()
  };

  return {
    prisma,
    services,
    mocks,
    cleanup: async () => {
      await prisma.$disconnect();
      await services.shutdown();
      await mocks.cleanup();
    }
  };
}

export const testConfig = {
  timeout: 30000,
  retries: 3,
  parallel: true;
};