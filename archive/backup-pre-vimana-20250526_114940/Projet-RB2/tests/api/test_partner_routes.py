"""
Tests pour les routes de l'API Partner.
"""

import pytest
from fastapi.testclient import TestClient

from src.api.app import create_app
from src.models.partner import Partner, PartnerType, PartnerStatus, PartnerSpecialty
from src.database.repositories import PartnerRepository

@pytest.fixture
def client():
    """
    Fixture pour le client de test FastAPI.
    """
    app = create_app()
    return TestClient(app)

@pytest.fixture
def partner_repo(test_db_path):
    """
    Fixture pour le repository Partner.
    """
    return PartnerRepository()

@pytest.fixture
def sample_partner():
    """
    Fixture pour un partenaire de test.
    """
    return {
        "id": "partner_api_test_123",
        "name": "API Test Yoga Studio",
        "email": "<EMAIL>",
        "phone": "+33123456789",
        "partner_type": "wellness_expert",
        "status": "active",
        "specialties": ["yoga", "meditation"],
        "description": "Un studio de yoga pour les tests API",
        "website": "https://api-test-yogastudio.com",
        "languages": ["fr", "en"],
        "location": {"country": "France", "region": "Provence", "city": "Aix-en-Provence"},
        "rating": 4.5
    }

def test_create_partner(client, sample_partner, clean_db):
    """
    Test la création d'un partenaire via l'API.
    """
    # Envoyer une requête POST pour créer un partenaire
    response = client.post("/api/partners/", json=sample_partner)
    
    # Vérifier que la requête a réussi
    assert response.status_code == 201
    
    # Vérifier les données retournées
    data = response.json()
    assert data["id"] == sample_partner["id"]
    assert data["name"] == sample_partner["name"]
    assert data["email"] == sample_partner["email"]
    assert data["partner_type"] == sample_partner["partner_type"]
    assert data["status"] == sample_partner["status"]
    assert data["specialties"] == sample_partner["specialties"]

def test_get_partner(client, sample_partner, partner_repo, clean_db):
    """
    Test la récupération d'un partenaire via l'API.
    """
    # Créer un partenaire dans la base de données
    partner = Partner.from_dict(sample_partner)
    partner_repo.create(partner)
    
    # Envoyer une requête GET pour récupérer le partenaire
    response = client.get(f"/api/partners/{sample_partner['id']}")
    
    # Vérifier que la requête a réussi
    assert response.status_code == 200
    
    # Vérifier les données retournées
    data = response.json()
    assert data["id"] == sample_partner["id"]
    assert data["name"] == sample_partner["name"]
    assert data["email"] == sample_partner["email"]
    assert data["partner_type"] == sample_partner["partner_type"]
    assert data["status"] == sample_partner["status"]
    assert data["specialties"] == sample_partner["specialties"]

def test_get_partner_not_found(client):
    """
    Test la récupération d'un partenaire inexistant via l'API.
    """
    # Envoyer une requête GET pour récupérer un partenaire inexistant
    response = client.get("/api/partners/nonexistent")
    
    # Vérifier que la requête a échoué
    assert response.status_code == 404

def test_update_partner(client, sample_partner, partner_repo, clean_db):
    """
    Test la mise à jour d'un partenaire via l'API.
    """
    # Créer un partenaire dans la base de données
    partner = Partner.from_dict(sample_partner)
    partner_repo.create(partner)
    
    # Modifier les données du partenaire
    updated_data = sample_partner.copy()
    updated_data["name"] = "Updated API Test Yoga Studio"
    updated_data["status"] = "verified"
    
    # Envoyer une requête PUT pour mettre à jour le partenaire
    response = client.put(f"/api/partners/{sample_partner['id']}", json=updated_data)
    
    # Vérifier que la requête a réussi
    assert response.status_code == 200
    
    # Vérifier les données retournées
    data = response.json()
    assert data["id"] == sample_partner["id"]
    assert data["name"] == "Updated API Test Yoga Studio"
    assert data["status"] == "verified"
    
    # Vérifier que les données ont été mises à jour dans la base de données
    updated_partner = partner_repo.get_by_id(sample_partner["id"])
    assert updated_partner.name == "Updated API Test Yoga Studio"
    assert updated_partner.status == PartnerStatus.VERIFIED

def test_delete_partner(client, sample_partner, partner_repo, clean_db):
    """
    Test la suppression d'un partenaire via l'API.
    """
    # Créer un partenaire dans la base de données
    partner = Partner.from_dict(sample_partner)
    partner_repo.create(partner)
    
    # Envoyer une requête DELETE pour supprimer le partenaire
    response = client.delete(f"/api/partners/{sample_partner['id']}")
    
    # Vérifier que la requête a réussi
    assert response.status_code == 204
    
    # Vérifier que le partenaire a été supprimé de la base de données
    assert partner_repo.get_by_id(sample_partner["id"]) is None

def test_get_partners(client, sample_partner, partner_repo, clean_db):
    """
    Test la récupération de la liste des partenaires via l'API.
    """
    # Créer un partenaire dans la base de données
    partner = Partner.from_dict(sample_partner)
    partner_repo.create(partner)
    
    # Envoyer une requête GET pour récupérer la liste des partenaires
    response = client.get("/api/partners/")
    
    # Vérifier que la requête a réussi
    assert response.status_code == 200
    
    # Vérifier les données retournées
    data = response.json()
    assert len(data) == 1
    assert data[0]["id"] == sample_partner["id"]
    assert data[0]["name"] == sample_partner["name"]
    assert data[0]["email"] == sample_partner["email"]

def test_get_partners_by_type(client, sample_partner, partner_repo, clean_db):
    """
    Test la récupération de la liste des partenaires par type via l'API.
    """
    # Créer un partenaire dans la base de données
    partner = Partner.from_dict(sample_partner)
    partner_repo.create(partner)
    
    # Envoyer une requête GET pour récupérer la liste des partenaires par type
    response = client.get("/api/partners/?partner_type=wellness_expert")
    
    # Vérifier que la requête a réussi
    assert response.status_code == 200
    
    # Vérifier les données retournées
    data = response.json()
    assert len(data) == 1
    assert data[0]["id"] == sample_partner["id"]
    assert data[0]["partner_type"] == "wellness_expert"
    
    # Envoyer une requête GET pour récupérer la liste des partenaires par un autre type
    response = client.get("/api/partners/?partner_type=accommodation")
    
    # Vérifier que la requête a réussi mais qu'aucun partenaire n'a été trouvé
    assert response.status_code == 200
    assert len(response.json()) == 0
