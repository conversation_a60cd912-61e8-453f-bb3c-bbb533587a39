"""
Configuration des tests.
"""

import os
import sys
import pytest
import sqlite3
from pathlib import Path

# Ajouter le répertoire parent au chemin pour pouvoir importer les modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.database.connection import init_db

# Chemin vers la base de données de test
TEST_DB_PATH = os.path.join(os.path.dirname(__file__), 'test_db.sqlite')

@pytest.fixture(scope="session")
def test_db_path():
    """
    Fixture pour le chemin de la base de données de test.
    """
    # Supprimer la base de données de test si elle existe
    if os.path.exists(TEST_DB_PATH):
        os.remove(TEST_DB_PATH)
    
    # Initialiser la base de données de test
    init_db(TEST_DB_PATH)
    
    yield TEST_DB_PATH
    
    # Supprimer la base de données de test après les tests
    if os.path.exists(TEST_DB_PATH):
        os.remove(TEST_DB_PATH)

@pytest.fixture
def db_connection(test_db_path):
    """
    Fixture pour une connexion à la base de données de test.
    """
    # Créer une connexion à la base de données de test
    conn = sqlite3.connect(test_db_path)
    conn.row_factory = sqlite3.Row
    
    # Activer les foreign keys
    conn.execute("PRAGMA foreign_keys = ON")
    
    yield conn
    
    # Fermer la connexion
    conn.close()

@pytest.fixture
def clean_db(db_connection):
    """
    Fixture pour nettoyer la base de données entre les tests.
    """
    # Supprimer toutes les données des tables
    tables = [
        "bookings",
        "clients",
        "retreats",
        "partners"
    ]
    
    for table in tables:
        db_connection.execute(f"DELETE FROM {table}")
    
    db_connection.commit()
    
    yield
