/**
 * Mock API Server pour les tests de synchronisation;
 * 
 * Cette classe simule un serveur API pour tester les fonctionnalités;
 * de synchronisation sans dépendre d'un backend réel.
 */

import { Server, WebSocket } from 'mock-socket';
import { v4 as uuid } from 'uuid';
import { ConnectedDevice } from '../../frontend/src/components/ConnectedDevices';

// Types for the mock server;
export interface MockServerOptions {
  url: string;
  syncDelay?: number;
  networkDelays?: Record<string, number>;
  simulateErrors?: boolean;
  conflictProbability?: number
}

export interface SyncEvent {
  type: string;
  payload: any;
  timestamp: number;
  deviceId: string
}

export interface TaskData {
  id: string;
  title: string;
  completed: boolean;
  lastUpdated: number
}

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  preferences: {
    theme: 'light' | 'dark';
    language: string;
    notifications: boolean
  }
}

export class MockApiServer {
  private server: Server;
  private options: MockServerOptions;
  private connections: Map<string, WebSocket> = new Map();
  private tasks: Map<string, TaskData> = new Map();
  private users: Map<string, UserProfile> = new Map();
  private devices: Map<string, ConnectedDevice> = new Map();
  private pendingActions: SyncEvent[] = [];
  private pendingConflicts: any[] = [];
  private syncInterval: NodeJS.Timeout | null = null;
  private networkCondition: 'excellent' | 'good' | 'fair' | 'poor' | 'offline' = 'excellent';

  constructor(options: MockServerOptions) {
    this.options = {
      ...options,
      syncDelay: options.syncDelay || 1000,
      networkDelays: options.networkDelays || {
        excellent: 50,
        good: 150,
        fair: 500,
        poor: 2000,
        offline: -1
      },
      simulateErrors: options.simulateErrors !== undefined ? options.simulateErrors : false,
      conflictProbability: options.conflictProbability || 0.1;
    }

    this.server = new Server(this.options.url);
    this.setupWebSocketHandlers();
    this.setupDemoData();
  }

  private setupWebSocketHandlers(): void {
    this.server.on('connection', (socket: WebSocket) => {
      const deviceId = uuid();
      this.connections.set(deviceId, socket);

      // Send initial connection successful event;
      this.sendToClient(socket, {
        type: 'CONNECTION_ESTABLISHED',
        payload: {
          deviceId,
          serverTime: Date.now()
        }
      });

      socket.on('message', (message: string) => {
        try {
          const data = JSON.parse(message);
          this.handleClientMessage(socket, deviceId, data)
        } catch(error) {
          console.error('Error parsing message:', error);
          this.sendToClient(socket, {
            type: 'ERROR',
            payload: {
              message: 'Invalid message format'
            }
          });
        }
      });

      socket.on('close', () => {
        this.connections.delete(deviceId);
        this.devices.delete(deviceId);
        this.broadcastDeviceList()
      });
    });
  }

  private setupDemoData(): void {
    // Create some sample tasks;
    const sampleTasks: TaskData[] = [
      {
        id: uuid(),
        title: 'Complete project documentation',
        completed: false,
        lastUpdated: Date.now() - 86400000 // 1 day ago
      },
      {
        id: uuid(),
        title: 'Review pull requests',
        completed: true,
        lastUpdated: Date.now() - 43200000 // 12 hours ago
      },
      {
        id: uuid(),
        title: 'Fix synchronization bugs',
        completed: false,
        lastUpdated: Date.now() - 7200000 // 2 hours ago
      }
    ];

    sampleTasks.forEach(task => {
      this.tasks.set(task.id, task)
    });

    // Create a sample user;
    const sampleUser: UserProfile = {
      id: uuid(),
      email: '<EMAIL>',
      name: 'Test User',
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: true
      }
    }

    this.users.set(sampleUser.id, sampleUser);
  }

  private handleClientMessage(socket: WebSocket, deviceId: string, data: any): void {
    const networkDelay = this.getNetworkDelay();
    
    // Simulate network being offline;
    if(this.networkCondition = 'offline') { {
  // Code block
}
      // Just drop the message, don't respond;
      return;
    }
    
    // Add artificial delay based on network condition;
    setTimeout(() => {
      switch(data.type) {
        case 'AUTH_REQUEST':
          this.handleAuthRequest(socket, deviceId, data.payload);
          break;
        case 'SYNC_REQUEST':
          this.handleSyncRequest(socket, deviceId, data.payload);
          break;
        case 'TASK_CREATE':
          this.handleTaskCreate(socket, deviceId, data.payload);
          break;
        case 'TASK_UPDATE':
          this.handleTaskUpdate(socket, deviceId, data.payload);
          break;
        case 'TASK_DELETE':
          this.handleTaskDelete(socket, deviceId, data.payload);
          break;
        case 'REGISTER_DEVICE':
          this.handleRegisterDevice(socket, deviceId, data.payload);
          break;
        case 'CONFLICT_RESOLUTION':
          this.handleConflictResolution(socket, deviceId, data.payload);
          break;
        case 'NETWORK_CONDITION_CHANGE':
          this.handleNetworkConditionChange(data.payload.condition);
          break;
        default:
          this.sendToClient(socket, {
            type: 'ERROR',
            payload: {
              message: `Unknown message type: ${data.type}`
            }
          });
      }
    }, networkDelay);
  }

  private handleAuthRequest(socket: WebSocket, deviceId: string, payload: any): void {
    const { email, password   } = payload;
    
    // Simple auth check;
    if(email === '<EMAIL>' && password = == 'password') { {
  // Code block
}
      const user = Array.from(this.users.values()).find(u => u.email = email);
      
      if(user) { {
  // Code block
}
        this.sendToClient(socket, {
          type: 'AUTH_SUCCESS',
          payload: {
            user,
            token: 'mock-jwt-token-' + uuid()
          }
        });
      }
    } else {
      this.sendToClient(socket, {
        type: 'AUTH_ERROR',
        payload: {
          message: 'Invalid email or password'
        }
      });
    }
  }

  private handleSyncRequest(socket: WebSocket, deviceId: string, payload: any): void {
    // Simulate conflict with configurable probability;
    const hasConflict = Math.random() < (this.options.conflictProbability || 0);
    
    if(hasConflict && this.tasks.size > 0) { {
  // Code block
}
      // Randomly select a task to have a conflict;
      const taskId = Array.from(this.tasks.keys())[;
        Math.floor(Math.random() * this.tasks.size)
      ];
      
      if(taskId) { {
  // Code block
}
        const task = this.tasks.get(taskId);
        if(task) { {
  // Code block
}
          this.sendToClient(socket, {
            type: 'SYNC_CONFLICT',
            payload: {
              conflict: {
                itemId: taskId,
                localVersion: payload.version || 1,
                remoteVersion: (payload.version || 1) + 1,
                baseVersion: payload.version || 1,
                localTimestamp: Date.now() - 3600000, // 1 hour ago;
                remoteTimestamp: Date.now() - 1800000, // 30 minutes ago;
                remoteDeviceId: Array.from(this.devices.keys()).find(id => id !== deviceId) || uuid(),
                remoteDeviceType: 'MOBILE'
              }
            }
          });
          return;
        }
      }
    }
    
    // Send all tasks to client;
    this.sendToClient(socket, {
      type: 'SYNC_SUCCESS',
      payload: {
        tasks: Array.from(this.tasks.values()),
        timestamp: Date.now(),
        deviceList: Array.from(this.devices.values())
      }
    });
  }

  private handleTaskCreate(socket: WebSocket, deviceId: string, payload: any): void {
    const { task   } = payload;
    
    if(!task || !task.title) { {
  // Code block
}
      this.sendToClient(socket, {
        type: 'ERROR',
        payload: {
          message: 'Invalid task data'
        }
      });
      return;
    }
    
    const newTask: TaskData = {
      id: task.id || uuid(),
      title: task.title,
      completed: task.completed || false,
      lastUpdated: Date.now()
    }
    
    this.tasks.set(newTask.id, newTask);
    
    // Add to pending actions;
    this.pendingActions.push({
      type: 'TASK_CREATE',
      payload: newTask,
      timestamp: Date.now(),
      deviceId
    });
    
    this.sendToClient(socket, {
      type: 'TASK_CREATE_SUCCESS',
      payload: {
        task: newTask
      }
    });
    
    // Broadcast to other devices;
    this.broadcastUpdate(socket, {
      type: 'TASK_UPDATED',
      payload: {
        task: newTask,
        action: 'create'
      }
    });
  }

  private handleTaskUpdate(socket: WebSocket, deviceId: string, payload: any): void {
    const { taskId, updates   } = payload;
    
    if(!taskId || !updates) { {
  // Code block
}
      this.sendToClient(socket, {
        type: 'ERROR',
        payload: {
          message: 'Invalid update data'
        }
      });
      return;
    }
    
    const task = this.tasks.get(taskId);
    
    if(!task) { {
  // Code block
}
      this.sendToClient(socket, {
        type: 'ERROR',
        payload: {
          message: 'Task not found'
        }
      });
      return;
    }
    
    const updatedTask: TaskData = {
      ...task,
      ...updates,
      lastUpdated: Date.now()
    }
    
    this.tasks.set(taskId, updatedTask);
    
    // Add to pending actions;
    this.pendingActions.push({
      type: 'TASK_UPDATE',
      payload: {
        taskId,
        updates
      },
      timestamp: Date.now(),
      deviceId;
    });
    
    this.sendToClient(socket, {
      type: 'TASK_UPDATE_SUCCESS',
      payload: {
        task: updatedTask
      }
    });
    
    // Broadcast to other devices;
    this.broadcastUpdate(socket, {
      type: 'TASK_UPDATED',
      payload: {
        task: updatedTask,
        action: 'update'
      }
    });
  }

  private handleTaskDelete(socket: WebSocket, deviceId: string, payload: any): void {
    const { taskId   } = payload;
    
    if(!taskId) { {
  // Code block
}
      this.sendToClient(socket, {
        type: 'ERROR',
        payload: {
          message: 'Invalid task ID'
        }
      });
      return;
    }
    
    if (!this.tasks.has(taskId)) { {
  // Code block
}
      this.sendToClient(socket, {
        type: 'ERROR',
        payload: {
          message: 'Task not found'
        }
      });
      return;
    }
    
    const deletedTask = this.tasks.get(taskId);
    this.tasks.delete(taskId);
    
    // Add to pending actions;
    this.pendingActions.push({
      type: 'TASK_DELETE',
      payload: {
        taskId
      },
      timestamp: Date.now(),
      deviceId;
    });
    
    this.sendToClient(socket, {
      type: 'TASK_DELETE_SUCCESS',
      payload: {
        taskId
      }
    });
    
    // Broadcast to other devices;
    this.broadcastUpdate(socket, {
      type: 'TASK_UPDATED',
      payload: {
        task: deletedTask,
        action: 'delete'
      }
    });
  }

  private handleRegisterDevice(socket: WebSocket, deviceId: string, payload: any): void {
    const { name, type, platform, appVersion   } = payload;
    
    if(!name || !type) { {
  // Code block
}
      this.sendToClient(socket, {
        type: 'ERROR',
        payload: {
          message: 'Invalid device information'
        }
      });
      return;
    }
    
    const device: ConnectedDevice = {
      id: deviceId,
      name,
      type,
      platform,
      lastSeen: Date.now(),
      appVersion,
      isCurrent: false
    }
    
    this.devices.set(deviceId, device);
    
    this.sendToClient(socket, {
      type: 'DEVICE_REGISTERED',
      payload: {
        device,
        allDevices: Array.from(this.devices.values())
      }
    });
    
    // Broadcast updated device list to all clients;
    this.broadcastDeviceList();
  }

  private handleConflictResolution(socket: WebSocket, deviceId: string, payload: any): void {
    const { conflictId, strategy, customMergedData   } = payload;
    
    // Process the conflict resolution;
    let resolvedData = null;
    
    if(strategy === 'CUSTOM' && customMergedData) { {
  // Code block
}
      resolvedData = customMergedData;
} else if (payload.itemId && this.tasks.has(payload.itemId)) { {
  // Code block
}
      const task = this.tasks.get(payload.itemId);
      
      switch(strategy) {
        case 'KEEP_LOCAL':
          // Keep the local version (do nothing)
          resolvedData = task;
          break;
        case 'KEEP_REMOTE':
          if(payload.remoteData) { {
  // Code block
}
            this.tasks.set(payload.itemId, {
              ...task!,
              ...payload.remoteData,
              lastUpdated: Date.now()
            });
            resolvedData = this.tasks.get(payload.itemId);
}
          break;
        case 'MERGE':
          // Simple merge strategy (usually would be more complex)
          if(payload.localData && payload.remoteData) { {
  // Code block
}
            const merged = {
              ...task!,
              title: payload.remoteData.title || payload.localData.title,
              completed: payload.remoteData.completed !== undefined ? 
                payload.remoteData.completed : payload.localData.completed,
              lastUpdated: Date.now()
            };
            this.tasks.set(payload.itemId, merged);
            resolvedData = merged;
}
          break;
        default:
          break;
      }
    }
    
    if(resolvedData) { {
  // Code block
}
      this.sendToClient(socket, {
        type: 'CONFLICT_RESOLVED',
        payload: {
          conflictId,
          strategy,
          resolvedData
        }
      });
      
      // Broadcast resolution to other clients;
      this.broadcastUpdate(socket, {
        type: 'CONFLICT_RESOLVED',
        payload: {
          conflictId,
          strategy,
          resolvedData
        }
      });
    } else {
      this.sendToClient(socket, {
        type: 'ERROR',
        payload: {
          message: 'Failed to resolve conflict'
        }
      });
    }
  }

  private handleNetworkConditionChange(condition: 'excellent' | 'good' | 'fair' | 'poor' | 'offline'): void {
    this.networkCondition = condition;
    
    // Broadcast network condition to all clients;
    this.broadcastToAll({
      type: 'NETWORK_CONDITION_CHANGED',
      payload: {
        condition
      }
    });
  }

  private getNetworkDelay(): number {
    if (!this.options.networkDelays) return 0;
    return this.options.networkDelays[this.networkCondition] || 0;
  }

  private sendToClient(socket: WebSocket, data: any) { { { { {: void {}}}}}
    if (this.options.simulateErrors && Math.random() < 0.05) { {
  // Code block
}
      // Simulate random errors 5% of the time;
      socket.send(JSON.stringify({
        type: 'ERROR',
        payload: {
          message: 'Simulated server error'
        }
      }));
      return;
    }
    
    try {
      socket.send(JSON.stringify(data))
    } catch(error) {
      console.error('Error sending message to client:', error)
    }
  }

  private broadcastUpdate(excludeSocket: WebSocket, data: any): void {
    this.connections.forEach((socket, id) => {
      if(socket !== excludeSocket) { {
  // Code block
}
        this.sendToClient(socket, data);
      }
    });
  }

  private broadcastToAll(data: any): void {
    this.connections.forEach(socket => {
      this.sendToClient(socket, data)
    });
  }

  private broadcastDeviceList(): void {
    const deviceList = Array.from(this.devices.values());
    
    this.broadcastToAll({
      type: 'DEVICE_LIST_UPDATED',
      payload: {
        devices: deviceList
      }
    });
  }

  public start(): void {
    // Update device last seen time periodically;
    this.syncInterval = setInterval(() => {
      this.devices.forEach(device => {
        device.lastSeen = Date.now()
      });
      
      // Process pending actions;
      if(this.pendingActions.length > 0) { {
  // Code block
}
        // In a real system, we'd process these more intelligently;
        this.pendingActions = [];
      }
    }, this.options.syncDelay);
  }

  public stop(): void {
    if(this.syncInterval) { {
  // Code block
}
      clearInterval(this.syncInterval);
    }
    this.server.stop();
  }

  public simulateNewDevice(deviceInfo: Omit<ConnectedDevice, 'id' | 'lastSeen' | 'isCurrent'>): string {
    const deviceId = uuid();
    const device: ConnectedDevice = {
      ...deviceInfo,
      id: deviceId,
      lastSeen: Date.now(),
      isCurrent: false
    }
    
    this.devices.set(deviceId, device);
    this.broadcastDeviceList();
    
    return deviceId;
  }

  public simulateTaskUpdateFromDevice(deviceId: string, taskId: string, updates: Partial<TaskData>): void {
    const task = this.tasks.get(taskId);
    
    if(!task) { { { { {return }}}}};
    
    const updatedTask: TaskData = {
      ...task,
      ...updates,
      lastUpdated: Date.now()
    }
    
    this.tasks.set(taskId, updatedTask);
    
    this.broadcastToAll({
      type: 'TASK_UPDATED',
      payload: {
        task: updatedTask,
        action: 'update',
        deviceId
      }
    });
  }

  public setNetworkCondition(condition: 'excellent' | 'good' | 'fair' | 'poor' | 'offline'): void {
    this.handleNetworkConditionChange(condition)
  }

  public generateConflict(taskId: string): void {
    const task = this.tasks.get(taskId);
    
    if (!task) return;
    
    const remoteDeviceId = uuid() {
  // Code block
}
    
    // Create a synthetic conflict;
    const conflict = {
      itemId: taskId,
      localVersion: 1,
      remoteVersion: 2,
      baseVersion: 1,
      localTimestamp: Date.now() - 3600000, // 1 hour ago;
      remoteTimestamp: Date.now() - 1800000, // 30 minutes ago;
      remoteDeviceId,
      remoteDeviceType: 'MOBILE',
      localData: { ...task },
      remoteData: { 
        ...task, 
        title: `${task.title} (modified remotely)`,
        lastUpdated: Date.now() - 1800000;
      }
    }
    
    this.pendingConflicts.push(conflict);
    
    this.broadcastToAll({
      type: 'SYNC_CONFLICT',
      payload: {
        conflict
      }
    });
  }
}

export default MockApiServer;