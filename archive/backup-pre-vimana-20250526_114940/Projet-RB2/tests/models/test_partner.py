"""
Tests pour le modèle Partner.
"""

import pytest
from datetime import datetime

from src.models.partner import Partner, PartnerType, PartnerStatus, PartnerSpecialty, PartnerAvailability, PartnerReview

def test_partner_creation():
    """
    Test la création d'un partenaire.
    """
    # Créer un partenaire
    partner = Partner(
        id="partner_123",
        name="Yoga Studio",
        email="<EMAIL>",
        phone="+33123456789",
        partner_type=PartnerType.WELLNESS_EXPERT,
        status=PartnerStatus.ACTIVE,
        specialties=[PartnerSpecialty.YOGA, PartnerSpecialty.MEDITATION],
        description="Un studio de yoga professionnel",
        website="https://yogastudio.com",
        languages=["fr", "en"],
        location={"country": "France", "region": "Provence", "city": "Aix-en-Provence"}
    )
    
    # Vérifier les attributs
    assert partner.id == "partner_123"
    assert partner.name == "Yoga Studio"
    assert partner.email == "<EMAIL>"
    assert partner.phone == "+33123456789"
    assert partner.partner_type == PartnerType.WELLNESS_EXPERT
    assert partner.status == PartnerStatus.ACTIVE
    assert PartnerSpecialty.YOGA in partner.specialties
    assert PartnerSpecialty.MEDITATION in partner.specialties
    assert partner.description == "Un studio de yoga professionnel"
    assert partner.website == "https://yogastudio.com"
    assert "fr" in partner.languages
    assert "en" in partner.languages
    assert partner.location["country"] == "France"
    assert partner.location["region"] == "Provence"

def test_partner_to_dict():
    """
    Test la conversion d'un partenaire en dictionnaire.
    """
    # Créer un partenaire
    partner = Partner(
        id="partner_123",
        name="Yoga Studio",
        email="<EMAIL>",
        partner_type=PartnerType.WELLNESS_EXPERT,
        status=PartnerStatus.ACTIVE,
        specialties=[PartnerSpecialty.YOGA]
    )
    
    # Convertir en dictionnaire
    partner_dict = partner.to_dict()
    
    # Vérifier les attributs
    assert partner_dict["id"] == "partner_123"
    assert partner_dict["name"] == "Yoga Studio"
    assert partner_dict["email"] == "<EMAIL>"
    assert partner_dict["partner_type"] == "wellness_expert"
    assert partner_dict["status"] == "active"
    assert partner_dict["specialties"] == ["yoga"]

def test_partner_from_dict():
    """
    Test la création d'un partenaire à partir d'un dictionnaire.
    """
    # Créer un dictionnaire
    partner_dict = {
        "id": "partner_123",
        "name": "Yoga Studio",
        "email": "<EMAIL>",
        "partner_type": "wellness_expert",
        "status": "active",
        "specialties": ["yoga", "meditation"],
        "description": "Un studio de yoga professionnel",
        "website": "https://yogastudio.com",
        "languages": ["fr", "en"],
        "location": {"country": "France", "region": "Provence", "city": "Aix-en-Provence"},
        "created_at": "2023-01-01T00:00:00",
        "updated_at": "2023-01-01T00:00:00"
    }
    
    # Créer un partenaire à partir du dictionnaire
    partner = Partner.from_dict(partner_dict)
    
    # Vérifier les attributs
    assert partner.id == "partner_123"
    assert partner.name == "Yoga Studio"
    assert partner.email == "<EMAIL>"
    assert partner.partner_type == PartnerType.WELLNESS_EXPERT
    assert partner.status == PartnerStatus.ACTIVE
    assert PartnerSpecialty.YOGA in partner.specialties
    assert PartnerSpecialty.MEDITATION in partner.specialties
    assert partner.description == "Un studio de yoga professionnel"
    assert partner.website == "https://yogastudio.com"
    assert "fr" in partner.languages
    assert "en" in partner.languages
    assert partner.location["country"] == "France"
    assert partner.location["region"] == "Provence"
    assert partner.created_at.isoformat() == "2023-01-01T00:00:00"
    assert partner.updated_at.isoformat() == "2023-01-01T00:00:00"

def test_partner_availability():
    """
    Test la disponibilité d'un partenaire.
    """
    # Créer des disponibilités
    start_date = datetime(2023, 7, 1)
    end_date = datetime(2023, 7, 15)
    availability = PartnerAvailability(
        start_date=start_date,
        end_date=end_date,
        status="available",
        notes="Disponible pour des retraites"
    )
    
    # Créer un partenaire avec des disponibilités
    partner = Partner(
        id="partner_123",
        name="Yoga Studio",
        email="<EMAIL>",
        partner_type=PartnerType.WELLNESS_EXPERT,
        status=PartnerStatus.ACTIVE,
        availability=[availability]
    )
    
    # Vérifier les disponibilités
    assert len(partner.availability) == 1
    assert partner.availability[0].start_date == start_date
    assert partner.availability[0].end_date == end_date
    assert partner.availability[0].status == "available"
    assert partner.availability[0].notes == "Disponible pour des retraites"

def test_partner_reviews():
    """
    Test les avis sur un partenaire.
    """
    # Créer des avis
    review_date = datetime(2023, 6, 15)
    review = PartnerReview(
        client_id="client_456",
        rating=4.5,
        comment="Excellent professeur de yoga",
        date=review_date,
        retreat_id="retreat_789"
    )
    
    # Créer un partenaire avec des avis
    partner = Partner(
        id="partner_123",
        name="Yoga Studio",
        email="<EMAIL>",
        partner_type=PartnerType.WELLNESS_EXPERT,
        status=PartnerStatus.ACTIVE,
        reviews=[review],
        rating=4.5
    )
    
    # Vérifier les avis
    assert len(partner.reviews) == 1
    assert partner.reviews[0].client_id == "client_456"
    assert partner.reviews[0].rating == 4.5
    assert partner.reviews[0].comment == "Excellent professeur de yoga"
    assert partner.reviews[0].date == review_date
    assert partner.reviews[0].retreat_id == "retreat_789"
    assert partner.rating == 4.5
