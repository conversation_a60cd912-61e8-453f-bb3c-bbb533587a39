# Intégration Financial-Management et RandB-Loyalty-Program

Ce document décrit l'intégration entre les microservices Financial-Management et RandB-Loyalty-Program pour la plateforme Retreat and Be.

## Architecture d'intégration

L'intégration entre les deux microservices suit une architecture API-first avec des points d'entrée clairement définis pour permettre une communication bidirectionnelle.

```
┌─────────────────────┐                 ┌─────────────────────┐
│                     │                 │                     │
│  Financial-         │◄────API Call───►│  RandB-Loyalty-     │
│  Management         │                 │  Program            │
│                     │                 │                     │
└─────────────────────┘                 └─────────────────────┘
        ▲                                       ▲
        │                                       │
        │                                       │
        │                                       │
        ▼                                       ▼
┌─────────────────────┐                 ┌─────────────────────┐
│                     │                 │                     │
│  Base de données    │                 │  Base de données    │
│  financière         │                 │  de fidélité        │
│                     │                 │                     │
└─────────────────────┘                 └─────────────────────┘
```

## Points d'intégration

### 1. Attribution de points pour les transactions financières

Lorsqu'une transaction financière est effectuée, le service Financial-Management appelle le service RandB-Loyalty-Program pour attribuer des points de fidélité au client.

**Endpoint**: `POST /api/loyalty/award-points`

**Payload**:
```json
{
  "userId": "user-123",
  "transactionId": "trans-456",
  "amount": 100.00,
  "currency": "EUR",
  "transactionType": "BOOKING",
  "source": "financial-management"
}
```

**Réponse**:
```json
{
  "success": true,
  "pointsAwarded": 125,
  "newTotal": 1250,
  "transactionId": "loyalty-trans-789"
}
```

### 2. Application des récompenses de fidélité

Lorsqu'un client souhaite utiliser ses points de fidélité pour obtenir une réduction, le service RandB-Loyalty-Program valide la demande et informe le service Financial-Management du montant de la réduction à appliquer.

**Endpoint**: `POST /api/loyalty/apply-reward`

**Payload**:
```json
{
  "userId": "user-123",
  "rewardId": "reward-456",
  "bookingId": "booking-789",
  "source": "financial-management"
}
```

**Réponse**:
```json
{
  "success": true,
  "discountAmount": 50.00,
  "discountType": "FIXED",
  "appliedTo": "booking-789",
  "message": "Réduction de 50€ appliquée à votre réservation"
}
```

### 3. Récupération du statut de fidélité

Le service Financial-Management peut récupérer le statut de fidélité d'un client pour l'afficher dans le tableau de bord financier.

**Endpoint**: `GET /api/loyalty/status/:userId`

**Réponse**:
```json
{
  "tier": "GOLD",
  "points": 5250,
  "nextTierProgress": {
    "current": 5250,
    "required": 10000,
    "percentage": 52
  },
  "availableRewards": [
    {
      "id": "reward-1",
      "name": "10% Discount",
      "description": "10% discount on your next booking",
      "pointsCost": 500,
      "type": "DISCOUNT"
    }
  ]
}
```

### 4. Synchronisation des transactions

Pour assurer la cohérence des données, les deux services peuvent synchroniser l'historique des transactions.

**Endpoint**: `POST /api/loyalty/sync-transactions`

**Payload**:
```json
{
  "userId": "user-123",
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-12-31T23:59:59Z",
  "source": "financial-management"
}
```

**Réponse**:
```json
{
  "success": true,
  "syncedTransactions": 15,
  "totalPointsAwarded": 1875
}
```

## Sécurité

L'authentification entre les services est assurée par des clés API dédiées. Chaque service doit inclure sa clé API dans l'en-tête `Authorization` de chaque requête, ainsi qu'un en-tête `X-Service-Name` pour identifier le service appelant.

```
Authorization: Bearer {API_KEY}
X-Service-Name: financial-management
```

## Configuration

Les variables d'environnement suivantes doivent être configurées pour permettre l'intégration :

### Financial-Management

```
LOYALTY_API_URL=http://randb-loyalty:5173/api
LOYALTY_API_KEY=loyalty-service-key
LOYALTY_INTEGRATION_ENABLED=true
LOYALTY_POINTS_CONVERSION_RATE=0.01
LOYALTY_SYNC_INTERVAL=3600000
```

### RandB-Loyalty-Program

```
FINANCIAL_API_URL=http://financial-management:5000/api
FINANCIAL_API_KEY=financial-service-key
FINANCIAL_INTEGRATION_ENABLED=true
FINANCIAL_MANAGEMENT_API_KEY=financial-service-key
```

## Déploiement

Les deux services sont configurés pour fonctionner ensemble dans un environnement Docker. Voir le fichier `docker-compose.yml` pour plus de détails sur la configuration réseau et les dépendances.

## Prochaines étapes

1. Implémentation d'un système de messaging (Kafka/RabbitMQ) pour la communication asynchrone
2. Mise en place d'un cache distribué pour améliorer les performances
3. Développement d'un tableau de bord unifié pour visualiser les données financières et de fidélité
4. Implémentation de tests d'intégration automatisés entre les deux services
