#!/bin/bash
# Script de nettoyage pour la migration

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Nettoyage des fichiers temporaires ===${NC}"

# Supprimer le répertoire temp_merge
if [ -d "temp_merge" ]; then
  echo -e "Suppression du répertoire temp_merge..."
  rm -rf temp_merge
  echo -e "${GREEN}Répertoire temp_merge supprimé avec succès.${NC}"
else
  echo -e "${YELLOW}Le répertoire temp_merge n'existe pas.${NC}"
fi

# Créer un rapport de nettoyage
echo -e "\n${YELLOW}Création du rapport de nettoyage...${NC}"

cat > cleanup_report.md << EOF
# Rapport de Nettoyage

## Actions effectuées

- Suppression du répertoire \`temp_merge\` contenant les fichiers temporaires de migration
- Conservation des sauvegardes dans le répertoire \`backups\` pour référence future

## État actuel

- Structure de répertoire propre et optimisée
- Tous les fichiers temporaires ont été supprimés
- Les sauvegardes sont conservées pour référence

## Recommandations

- Conserver les sauvegardes pendant au moins 30 jours après la migration
- Effectuer une revue finale de la structure du projet
- Mettre à jour la documentation pour refléter la nouvelle structure

---

*Rapport généré le: $(date)*
EOF

echo -e "${GREEN}Rapport de nettoyage créé avec succès: cleanup_report.md${NC}"

echo -e "\n${YELLOW}=== Fin du nettoyage ===${NC}"
