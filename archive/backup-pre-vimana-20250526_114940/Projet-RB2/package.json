{"name": "retreat-and-be-backend", "version": "1.0.0", "private": true, "description": "Backend API for the Retreat And Be wellness retreat platform", "author": "Retreat And Be Team", "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "report": "node scripts/generate-project-report.js", "prepare": "node husky-init.js", "init:performance": "ts-node src/modules/performance/scripts/init.ts", "performance:report": "node scripts/generate-performance-report.js"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/schedule": "^10.0.0", "@nestjs/serve-static": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/websockets": "^10.0.0", "@prisma/client": "^5.0.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@willsoto/nestjs-prometheus": "^6.0.0", "axios": "^1.6.0", "bcrypt": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "helmet": "^7.0.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.0", "rxjs": "^7.8.1", "socket.io": "^4.7.0", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.0", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.3", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/react": "^18.2.33", "@types/react-dom": "^18.2.14", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "jest": "^29.5.0", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "prisma": "^5.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.{js,ts,tsx}": ["eslint --fix", "jest --findRelatedTests"]}}