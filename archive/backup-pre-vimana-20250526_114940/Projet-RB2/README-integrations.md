# Intégrations du système de retraites de bien-être

Ce document décrit les nouvelles intégrations ajoutées au système de retraites de bien-être.

## Intégrations de calendrier

Le système peut maintenant s'intégrer avec des services de calendrier pour permettre aux utilisateurs de gérer leurs événements de retraite.

### Google Calendar

L'intégration avec Google Calendar permet de:

- Créer des événements pour les retraites
- Mettre à jour les événements existants
- Supprimer des événements
- Récupérer les événements d'un calendrier
- Générer des liens vers les événements

### iCalendar

Le système peut également générer des fichiers iCalendar (.ics) qui peuvent être importés dans n'importe quel logiciel de calendrier:

- Génération de fichiers iCalendar pour les retraites
- Envoi de fichiers iCalendar par email
- Téléchargement direct des fichiers iCalendar

## Intégrations de cartographie

Des services de cartographie ont été intégrés pour améliorer l'expérience utilisateur.

### Google Maps

L'intégration avec Google Maps permet de:

- Géocoder des adresses (convertir une adresse en coordonnées)
- Géocoder inversement des coordonnées (convertir des coordonnées en adresse)
- Calculer des distances et temps de trajet entre deux points
- Rechercher des lieux à proximité d'une adresse
- Générer des cartes statiques
- Générer des liens vers des itinéraires

## Système de notifications avancé

Le système de notifications a été amélioré pour supporter plusieurs canaux de communication.

### Notifications push

Les notifications push permettent d'envoyer des alertes en temps réel aux utilisateurs:

- Envoi de notifications push aux appareils mobiles
- Support pour iOS et Android via Firebase Cloud Messaging
- Gestion des abonnements aux sujets
- Envoi de notifications à plusieurs appareils simultanément

## Analyse de données

Un système d'analyse de données a été intégré pour suivre l'utilisation de la plateforme.

### Google Analytics

L'intégration avec Google Analytics permet de:

- Suivre les événements (réservations, paiements, etc.)
- Suivre les vues de page
- Suivre les conversions
- Récupérer des statistiques d'utilisation

## Comment utiliser les intégrations

### Calendrier

```bash
# Créer un événement dans Google Calendar pour une retraite
curl -X POST "http://localhost:8000/api/integrations/calendar/event" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "retreat_id": "retreat_123"
  }'

# Générer un fichier iCalendar pour une retraite
curl -X GET "http://localhost:8000/api/integrations/calendar/ical/retreat_123" \
  -H "Authorization: Bearer <token>"
```

### Cartographie

```bash
# Géocoder une adresse
curl -X GET "http://localhost:8000/api/integrations/maps/geocode?address=Paris,France" \
  -H "Authorization: Bearer <token>"

# Rechercher des lieux à proximité
curl -X GET "http://localhost:8000/api/integrations/maps/places?location=Paris,France&radius=1000&type=restaurant" \
  -H "Authorization: Bearer <token>"
```

### Notifications push

```bash
# Envoyer une notification push
curl -X POST "http://localhost:8000/api/notifications/push" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "<device_token>",
    "title": "Nouvelle retraite disponible",
    "body": "Une nouvelle retraite de yoga est disponible en Provence"
  }'
```

### Analyse de données

```bash
# Enregistrer un événement
curl -X POST "http://localhost:8000/api/analytics/events" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "event_name": "booking_completed",
    "event_data": {
      "retreat_id": "retreat_123",
      "amount": 1200
    }
  }'

# Récupérer les conversions
curl -X GET "http://localhost:8000/api/analytics/conversions?start_date=2023-01-01T00:00:00&end_date=2023-12-31T23:59:59" \
  -H "Authorization: Bearer <token>"
```

## Prochaines étapes

- **Intégration avec d'autres services de calendrier**: Apple Calendar, Outlook, etc.
- **Intégration avec d'autres services de cartographie**: Mapbox, OpenStreetMap, etc.
- **Amélioration des notifications push**: Segmentation des utilisateurs, personnalisation avancée, etc.
- **Intégration avec d'autres services d'analyse**: Mixpanel, Amplitude, etc.
- **Tableau de bord d'analyse**: Visualisation des données d'analyse en temps réel
