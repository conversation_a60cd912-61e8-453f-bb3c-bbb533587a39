# Mises à jour du système de retraites de bien-être

Ce document décrit les nouvelles fonctionnalités ajoutées au système de retraites de bien-être.

## Authentification et autorisation

Un système complet d'authentification et d'autorisation a été implémenté pour sécuriser l'API:

- **Authentification JWT**: Génération et validation de tokens JWT pour l'authentification des utilisateurs
- **Système de rôles**: Différents rôles (admin, partner, client, staff) avec des permissions spécifiques
- **Contrôle d'accès**: Vérification des permissions pour chaque route de l'API

## Paiements

Un système de paiement a été intégré pour gérer les transactions:

- **Intégration Stripe**: Traitement des paiements via Stripe
- **Différents types de paiement**: Paiement complet, acompte, paiement échelonné
- **Remboursements**: Gestion des remboursements complets ou partiels
- **Suivi des paiements**: Historique des paiements pour chaque réservation

## Interface d'administration

Une interface d'administration a été créée pour gérer le système:

- **Tableau de bord**: Vue d'ensemble des statistiques du système
- **Gestion des retraites**: Création, modification et suppression de retraites
- **Gestion des partenaires**: Gestion des partenaires professionnels
- **Gestion des clients**: Gestion des clients et de leurs préférences
- **Gestion des réservations**: Suivi et gestion des réservations
- **Gestion des utilisateurs**: Création et gestion des comptes utilisateurs

## Comment utiliser les nouvelles fonctionnalités

### Authentification

```bash
# Obtenir un token JWT
curl -X POST "http://localhost:8000/api/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=admin123"

# Utiliser le token pour accéder à l'API
curl -X GET "http://localhost:8000/api/retreats" \
  -H "Authorization: Bearer <token>"
```

### Paiements

```bash
# Créer une intention de paiement
curl -X POST "http://localhost:8000/api/payments/intent" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "booking_123",
    "amount": 1200,
    "payment_type": "full_payment"
  }'

# Traiter un paiement
curl -X POST "http://localhost:8000/api/payments/process" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "booking_123",
    "amount": 1200,
    "payment_type": "full_payment",
    "payment_method": "credit_card"
  }'
```

### Interface d'administration

1. Exécuter l'interface d'administration:
   ```bash
   python run_admin.py
   ```

2. Accéder à l'interface d'administration à l'adresse http://localhost:8001

3. Se connecter avec les identifiants par défaut:
   - Email: <EMAIL>
   - Mot de passe: admin123

## Prochaines étapes

- **Intégration avec des services tiers**: Calendriers, cartes, etc.
- **Système de notifications avancé**: Notifications push, SMS programmés, etc.
- **Analyse de données**: Tableaux de bord avancés, rapports personnalisés, etc.
- **Application mobile**: Intégration avec l'application mobile existante
- **Internationalisation**: Support de plusieurs langues
