# Retreat And Be - Integration Guide

This guide explains how to integrate Front-Audrey-V1-Main-main with Backend-NestJS and the database schema.

## Project Structure

The project consists of the following main components:

- **Front-Audrey-V1-Main-main**: React frontend application
- **Backend-NestJS**: NestJS backend API
- **Database**: PostgreSQL database with Prisma ORM

## Prerequisites

- Node.js 16.x or higher
- npm 8.x or higher
- PostgreSQL 14.x or higher
- Docker and Docker Compose (optional)

## Setup Instructions

### 1. Database Setup

1. Install PostgreSQL if not already installed
2. Create a new database:
   ```sql
   CREATE DATABASE retreatandbe;
   ```
3. Configure the database connection in `.env.development` or `.env.production` file in the Backend-NestJS directory

### 2. Backend Setup

1. Navigate to the Backend-NestJS directory:
   ```bash
   cd Backend-NestJS
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Generate Prisma client:
   ```bash
   npx prisma generate
   ```

4. Run database migrations:
   ```bash
   npx prisma migrate dev
   ```

5. Start the backend server:
   ```bash
   npm run start:dev
   ```

### 3. Frontend Setup

1. Navigate to the Front-Audrey-V1-Main-main directory:
   ```bash
   cd Front-Audrey-V1-Main-main
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file with the following content:
   ```
   REACT_APP_API_URL=http://localhost:3000/api
   REACT_APP_SECURITY_URL=http://localhost:3001/api
   REACT_APP_AGENT_IA_URL=http://localhost:3002/api
   REACT_APP_SOCIAL_URL=http://localhost:3003/api
   REACT_APP_FINANCIAL_URL=http://localhost:3004/api
   ```

4. Start the frontend server:
   ```bash
   npm start
   ```

## Docker Setup (Optional)

1. Make sure Docker and Docker Compose are installed
2. Run the following command from the project root:
   ```bash
   docker-compose up -d
   ```

## API Integration

The frontend communicates with the backend through a set of API services. These services are defined in the `src/services/api` directory of the frontend project.

### Key API Services

- **apiClient.ts**: Base API client with interceptors for authentication, caching, and error handling
- **authService.ts**: Authentication service for login, registration, etc.
- **userService.ts**: User service for profile management
- **retreatService.ts**: Retreat service for retreat management
- **bookingService.ts**: Booking service for retreat bookings

### Authentication Flow

1. User logs in via the frontend
2. Backend validates credentials and returns JWT tokens
3. Frontend stores tokens in localStorage
4. API requests include the token in the Authorization header
5. Token refresh is handled automatically by the API client

## Database Schema

The database schema is defined in the `prisma/schema.prisma` file in the Backend-NestJS directory. It includes models for:

- Users
- Profiles
- Retreats
- Bookings
- Courses
- Lessons
- Journals
- And many more

## Type Safety

TypeScript interfaces are generated from the Prisma schema to ensure type safety between the frontend and backend. These interfaces are stored in:

- Backend: `src/types/prisma-types.ts`
- Frontend: `src/types/prisma-types.ts`

To regenerate these types, run:

```bash
cd Backend-NestJS
node scripts/generate-types.js
```

## Environment Variables

### Backend Environment Variables

- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET`: Secret key for JWT tokens
- `JWT_REFRESH_SECRET`: Secret key for refresh tokens
- `PORT`: Port for the backend server
- `NODE_ENV`: Environment (development, production)

### Frontend Environment Variables

- `REACT_APP_API_URL`: URL of the backend API
- `REACT_APP_SECURITY_URL`: URL of the security microservice
- `REACT_APP_AGENT_IA_URL`: URL of the agent IA microservice
- `REACT_APP_SOCIAL_URL`: URL of the social microservice
- `REACT_APP_FINANCIAL_URL`: URL of the financial microservice

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check that PostgreSQL is running
   - Verify the DATABASE_URL in the .env file
   - Ensure the database exists

2. **API Connection Issues**
   - Check that the backend server is running
   - Verify the REACT_APP_API_URL in the frontend .env file
   - Check for CORS issues in the browser console

3. **Authentication Issues**
   - Clear localStorage in the browser
   - Check JWT_SECRET in the backend .env file
   - Verify that the token is being sent in API requests

## Additional Resources

- [NestJS Documentation](https://docs.nestjs.com/)
- [Prisma Documentation](https://www.prisma.io/docs/)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
