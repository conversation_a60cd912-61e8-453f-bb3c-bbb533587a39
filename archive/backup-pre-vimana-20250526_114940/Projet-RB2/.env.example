# Environment
NODE_ENV=development

# Database URLs
MONGODB_URI=mongodb://mongodb:27017/retreatandbe
REDIS_URL=redis://redis:6379
DATABASE_URL=********************************************/analyzer

# API URLs
VITE_API_URL=http://localhost:7000
VITE_WS_URL=ws://localhost:7000
FRONTEND_URL=http://localhost:5175
STORAGE_API_URL=http://decentralized-storage:3000

# Security
JWT_SECRET=your_jwt_secret_key_here
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password

# Blockchain Configuration
ETHEREUM_NODE_URL=http://localhost:8545
BLOCKCHAIN_NETWORK=localhost
HARDHAT_NETWORK=localhost
VITE_BLOCKCHAIN_NETWORK=localhost

# IPFS Configuration
IPFS_HOST=localhost
IPFS_PORT=5001
IPFS_PROTOCOL=http

# API Keys
OPENAI_API_KEY=your_openai_key_here
