# compiled output
/dist
node_modules/

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store
.cache/

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Prisma
/prisma/migrations

# Project specific
/uploads
/public/uploads
/PROJECT-REPORT.md

# Build output
build/
target/
*.tsbuildinfo

# Test Reports
playwright-report/
test-results/
audit-reports/
lighthouse-report/
*.html
jest-results-*.json
test-performance-report.md

# Logs
typescript_errors.log

# Temp/Backup Files
*.bak
*.temp
backup/
backups/
archive/
*.tar.gz

# OS generated files
Thumbs.db

# VS Code
*.code-workspace

# Analysis/Tool Output
ts-error-analysis/
validation_results/

# Large Dependencies / Sub-projects / Examples
istio-1.18.2/
Agent IA/
Agent-RB/
Analyzer/
Car-Rental/
Compare-Insurance/
Decentralized-Storage/
Education/
Expense-Sharing/
Financial-Management/
Flight-Finder/
GPS-Service/
Hotel-Booking/
Marketplace/
RandB-Loyalty-Program/
Retreat-Pro-Matcher/
Retreat-Stream/
Security/
Social-Platform-video/
Social/
Transport-Booking/
VR/
Website-Creator/

# SSH keys
2025
2025.pub
