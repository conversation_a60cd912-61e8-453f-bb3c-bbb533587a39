# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#     https://docs.fastlane.tools/actions
#

default_platform(:android)

platform :android do
  desc "Submit a new production build to the Google Play Store"
  lane :release do
    # Ensure we're on the right branch
    ensure_git_branch(branch: 'main')
    
    # Increment version code
    increment_version_code(
      gradle_file_path: "./app/build.gradle"
    )
    
    # Build the Android app
    gradle(
      task: "clean assembleRelease",
      properties: {
        "android.injected.signing.store.file" => ENV["KEYSTORE_PATH"],
        "android.injected.signing.store.password" => ENV["KEYSTORE_PASSWORD"],
        "android.injected.signing.key.alias" => ENV["KEY_ALIAS"],
        "android.injected.signing.key.password" => ENV["KEY_PASSWORD"],
      }
    )
    
    # Upload to Google Play
    upload_to_play_store(
      track: "production",
      release_status: "completed",
      skip_upload_metadata: false,
      skip_upload_images: true,
      skip_upload_screenshots: true,
      aab: "./app/build/outputs/bundle/release/app-release.aab"
    )
    
    # Notify the team
    slack(
      message: "Successfully deployed new Android app update!",
      success: true,
      slack_url: ENV["SLACK_URL"]
    ) if ENV["SLACK_URL"]
  end
  
  desc "Submit a new build to the beta track"
  lane :beta do
    # Increment version code
    increment_version_code(
      gradle_file_path: "./app/build.gradle"
    )
    
    # Build the Android app
    gradle(
      task: "clean assembleRelease",
      properties: {
        "android.injected.signing.store.file" => ENV["KEYSTORE_PATH"],
        "android.injected.signing.store.password" => ENV["KEYSTORE_PASSWORD"],
        "android.injected.signing.key.alias" => ENV["KEY_ALIAS"],
        "android.injected.signing.key.password" => ENV["KEY_PASSWORD"],
      }
    )
    
    # Upload to Google Play beta track
    upload_to_play_store(
      track: "beta",
      release_status: "completed",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true,
      aab: "./app/build/outputs/bundle/release/app-release.aab"
    )
    
    # Notify the team
    slack(
      message: "Successfully uploaded a new build to Google Play Beta!",
      success: true,
      slack_url: ENV["SLACK_URL"]
    ) if ENV["SLACK_URL"]
  end
  
  desc "Run tests"
  lane :test do
    gradle(task: "test")
  end
  
  # Helper action to increment the version code
  private_lane :increment_version_code do |options|
    path = options[:gradle_file_path]
    re = /versionCode (\d+)/
    
    s = File.read(path)
    versionCode = s[re, 1].to_i
    s[re, 1] = (versionCode + 1).to_s
    
    File.write(path, s)
    
    UI.message("Incremented version code to #{versionCode + 1}")
  end
end