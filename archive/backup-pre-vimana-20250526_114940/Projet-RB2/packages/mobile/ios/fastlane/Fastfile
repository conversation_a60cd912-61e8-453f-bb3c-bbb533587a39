# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#     https://docs.fastlane.tools/actions
#

default_platform(:ios)

platform :ios do
  desc "Push a new release build to the App Store"
  lane :release do
    # Ensure we're on the right branch
    ensure_git_branch(branch: 'main')
    
    # Increment build number
    increment_build_number(
      build_number: latest_testflight_build_number + 1,
      xcodeproj: "RB2.xcodeproj"
    )
    
    # Update code signing settings
    setup_ci if ENV['CI']
    
    # Match is used to sync certificates and provisioning profiles
    match(
      type: "appstore",
      readonly: true
    )
    
    # Build the app
    build_app(
      scheme: "RB2",
      workspace: "RB2.xcworkspace",
      export_method: "app-store",
      clean: true,
      output_directory: "./build",
      include_bitcode: false,
      include_symbols: true
    )
    
    # Upload to App Store Connect
    upload_to_app_store(
      skip_metadata: false,
      skip_screenshots: true,
      force: true,
      precheck_include_in_app_purchases: false,
      submission_information: {
        add_id_info_uses_idfa: false
      }
    )
    
    # Notify the team
    slack(
      message: "Successfully deployed new iOS app update!",
      success: true,
      slack_url: ENV["SLACK_URL"]
    ) if ENV["SLACK_URL"]
  end
  
  desc "Submit a new Beta build to TestFlight"
  lane :beta do
    # Increment build number
    increment_build_number(
      build_number: latest_testflight_build_number + 1,
      xcodeproj: "RB2.xcodeproj"
    )
    
    # Update code signing settings
    setup_ci if ENV['CI']
    
    # Match is used to sync certificates and provisioning profiles
    match(
      type: "appstore",
      readonly: true
    )
    
    # Build the app
    build_app(
      scheme: "RB2",
      workspace: "RB2.xcworkspace",
      export_method: "app-store",
      clean: true,
      output_directory: "./build",
      include_bitcode: false,
      include_symbols: true
    )
    
    # Upload to TestFlight
    upload_to_testflight(
      skip_waiting_for_build_processing: true,
      distribute_external: false,
      notify_external_testers: false
    )
    
    # Notify the team
    slack(
      message: "Successfully uploaded a new build to TestFlight!",
      success: true,
      slack_url: ENV["SLACK_URL"]
    ) if ENV["SLACK_URL"]
  end
end