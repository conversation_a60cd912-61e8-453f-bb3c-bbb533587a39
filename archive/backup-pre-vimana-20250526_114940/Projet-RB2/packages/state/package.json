{"name": "@projet-rb2/state", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "dev": "tsup src/index.ts --format esm,cjs --watch --dts", "lint": "eslint src/**/*.ts* --fix", "test": "jest", "test:coverage": "jest --coverage", "stress-test": "node scripts/stress-test.js", "stress-test:ci": "node scripts/stress-test.js --quiet --threshold=500", "prepare": "chmod +x scripts/stress-test.js"}, "dependencies": {"@projet-rb2/core": "*", "@projet-rb2/api": "*", "@reduxjs/toolkit": "^2.2.1", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "redux-logger": "^4.0.0"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.8.4", "@types/react": "^18.2.48", "@types/react-redux": "^7.1.32", "@types/redux-logger": "^3.0.12", "eslint": "^8.56.0", "jest": "^29.7.0", "react": "^18.2.0", "ts-jest": "^29.1.1", "tsup": "^8.0.1", "typescript": "^5.4.5"}, "peerDependencies": {"react": "^18.2.0"}}