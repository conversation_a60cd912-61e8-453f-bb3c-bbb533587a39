# @projet-rb2/state

Package de gestion d'état cross-platform pour le projet RB2, avec support pour la synchronisation en temps réel et le mode hors ligne.

## Installation

```bash
npm install @projet-rb2/state
```

## Fonctionnalités

- ✅ Gestion d'état Redux avec Redux Toolkit
- ✅ Hooks typés pour React et React Native
- ✅ Persistence de l'état avec Redux-Persist
- ✅ Synchronisation en temps réel via WebSockets
- ✅ Support du mode hors ligne avec file d'attente de synchronisation
- ✅ Gestion avancée des conflits de données
- ✅ Journalisation complète des événements et erreurs
- ✅ Middleware de gestion d'erreurs centralisé
- ✅ Surveillance des performances de l'application
- ✅ Tests de stress pour validation de robustesse
- ✅ Architecture modulaire et extensible

## Architecture

Le package est structuré en plusieurs modules :

- `/store` : Configuration du store Redux global
- `/slices` : Slices Redux pour différentes entités (auth, users, etc.)
- `/hooks` : Hooks React pour l'accès au store
- `/sync` : Système de synchronisation en temps réel
- `/middleware` : Middlewares Redux pour diverses fonctionnalités
- `/utils` : Utilitaires pour la journalisation, la performance, etc.

## Utilisation de base

```tsx
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '@projet-rb2/state';

function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <YourApp />
      </PersistGate>
    </Provider>
  );
}
```

## Utilisation des hooks

```tsx
import { useAppSelector, useAppDispatch } from '@projet-rb2/state';
import { login } from '@projet-rb2/state/slices/auth';

function LoginComponent() {
  const dispatch = useAppDispatch();
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  
  const handleLogin = () => {
    dispatch(login({ username, password }));
  };
  
  return (
    // ...
  );
}
```

## Synchronisation en temps réel

### Configuration

Pour activer la synchronisation en temps réel avec votre backend, configurez le middleware dans votre store :

```typescript
import { createStore, configureSyncMiddleware } from '@projet-rb2/state';

// Configure the sync middleware
const syncMiddleware = configureSyncMiddleware({
  wsUrl: 'wss://api.projet-rb2.com',
  wsPath: '/sync',
  offlineQueueEnabled: true,
  offlineQueueRetryInterval: 30000 // 30 secondes
});

// Create store with sync middleware
const { store, persistor } = createStore({
  middleware: [syncMiddleware]
});
```

Ou utilisez la fonction d'initialisation complète :

```typescript
import { createStore, initializeSynchronization } from '@projet-rb2/state';

// Create store
const { store, persistor } = createStore();

// Initialize synchronization and bind to store
const { middleware, cleanup } = initializeSynchronization(store, {
  wsUrl: 'wss://api.projet-rb2.com',
  wsPath: '/sync'
});

// Cleanup on unmount
// React: useEffect(() => { return cleanup; }, []);
```

### Synchronisation des actions

Les actions suivantes sont automatiquement synchronisées avec le backend :

- auth/login, auth/logout
- users/create, users/update, users/delete
- tasks/addTask, tasks/updateTask, tasks/removeTask, tasks/completeTask
- projects/create, projects/update, projects/delete

Vous pouvez personnaliser cette liste :

```typescript
import { configureSyncMiddleware } from '@projet-rb2/state';

const syncMiddleware = configureSyncMiddleware({
  // ...autres options
  actionTypesToSync: [
    'auth/login',
    'custom/action',
    // ...
  ]
});
```

### Gestion hors ligne

Le système gère automatiquement les actions déclenchées lorsque l'appareil est hors ligne. Ces actions sont stockées dans une file d'attente et synchronisées automatiquement lorsque la connexion est rétablie.

Pour vérifier ou gérer manuellement la file d'attente :

```typescript
import { 
  getPendingSyncActions, 
  forceSynchronization 
} from '@projet-rb2/state';

// Vérifier les actions en attente
const pendingActions = getPendingSyncActions();
console.log(`${pendingActions.length} actions en attente de synchronisation`);

// Forcer la synchronisation immédiate
async function syncNow() {
  const result = await forceSynchronization();
  console.log(`Synchronisation terminée: ${result.successful}/${result.total} réussies`);
}
```

## Fonctionnalités avancées

### Gestion des erreurs

Le package inclut un système complet de gestion d'erreurs qui capture, catégorise et traite les erreurs de manière centralisée.

```typescript
import { createErrorMiddleware, ErrorType, ErrorFactory } from '@projet-rb2/state/middleware';

// Créer un middleware d'erreur personnalisé
const errorMiddleware = createErrorMiddleware({
  errorHandlers: {
    [ErrorType.AUTHENTICATION]: (error, store) => {
      // Rediriger vers la page de connexion si l'authentification échoue
      if (error.status === 401) {
        store.dispatch({ type: 'auth/logout' });
        // Navigation vers la page de connexion
      }
    }
  }
});

// Dans votre code, vous pouvez créer des erreurs typées
const handleApiError = (error) => {
  const apiError = ErrorFactory.api(
    'Erreur lors de la récupération des données',
    error.status,
    error.code,
    error,
    { endpoint: '/users', method: 'GET' }
  );
  
  dispatch({ type: 'api/error', payload: apiError });
};
```

### Journalisation

Un système de journalisation avancé est inclus pour suivre les événements et les erreurs de l'application.

```typescript
import { logger, LogLevel } from '@projet-rb2/state/utils/logger';

// Configuration du logger
logger.configure({
  level: LogLevel.INFO,
  persistLogs: true,
  redactionKeys: ['password', 'token', 'secret'],
  environment: process.env.NODE_ENV
});

// Journaliser des événements
logger.info('Application démarrée', { version: '1.0.0' }, 'startup');
logger.debug('Détail de configuration', { config }, 'config');
logger.warn('Ressource non trouvée', { resourceId: 123 }, 'api');
logger.error('Erreur critique', error, 'crash');

// Récupérer les journaux pour le débogage
const errors = logger.filter({ level: LogLevel.ERROR });
console.table(errors);

// Créer un middleware de journalisation Redux
const loggerMiddleware = createLoggerMiddleware({
  actionFilter: (action) => !action.type.startsWith('@@internal/')
});
```

### Surveillance des performances

Le package inclut un système de surveillance des performances pour identifier les goulots d'étranglement.

```typescript
import { performance, MetricType } from '@projet-rb2/state/utils/performance';

// Configuration du moniteur de performance
performance.configure({
  enabled: true,
  sampleRate: 0.1, // Échantillonner 10% des métriques
  reportingThreshold: 300, // Signaler les opérations > 300ms
  logPerformanceIssues: true
});

// Mesurer manuellement une opération
const measureId = performance.startMeasure(
  MetricType.API_CALL,
  'fetchUsers',
  ['api', 'users']
);

// ...exécuter l'opération...

performance.endMeasure(measureId, { count: results.length });

// Mesurer une fonction (promesse ou synchrone)
const result = performance.measure(
  MetricType.USER_INTERACTION,
  'processUserData',
  () => processData(data),
  ['data-processing']
);

// Obtenir des statistiques
const apiStats = performance.getStatistics(MetricType.API_CALL);
console.log(`Temps moyen des appels API: ${apiStats.averageDuration.toFixed(2)}ms`);

// Identifier les opérations lentes
const slowOperations = performance.filterMetrics({
  minDuration: 500,
  limit: 10
});
```

### Tests de stress

Le package inclut un module de test de stress pour valider la robustesse du système de gestion d'état sous charge.

```typescript
import { runStressTest, generateRandomActions } from '@projet-rb2/state/utils';

// Actions à tester
const actionCreators = [
  () => ({ type: 'counter/increment', payload: 1 }),
  () => ({ type: 'counter/decrement', payload: 1 }),
  () => ({ type: 'tasks/add', payload: { id: Math.random(), title: 'Nouvelle tâche' } }),
  // ...autres actions
];

// Configuration du test
const testConfig = {
  actionsCount: 10000,              // Nombre total d'actions
  actionCreators,                   // Actions à dispatcher
  batchSize: 50,                    // Nombre d'actions par lot
  pattern: 'burst',                 // Mode dispatch: 'linear', 'burst' ou 'random'
  parallel: false,                  // Exécution en parallèle
  interval: 0,                      // Intervalle entre les lots (ms)
  verbose: true,                    // Journalisation détaillée
  onStart: () => console.log('Test démarré'),
  onComplete: (results) => {
    console.log(`Test terminé: ${results.actionsPerSecond.toFixed(2)} actions/sec`);
  }
};

// Exécuter le test
async function validateStorePerformance() {
  const results = await runStressTest(store, testConfig);
  
  console.log(`Actions traitées: ${results.totalActions}`);
  console.log(`Temps total: ${results.totalTime.toFixed(2)}ms`);
  console.log(`Actions/sec: ${results.actionsPerSecond.toFixed(2)}`);
  console.log(`Temps moyen/action: ${results.averageTimePerAction.toFixed(2)}ms`);
  console.log(`Croissance mémoire: ${results.memoryGrowth} bytes`);
  
  return results.actionsPerSecond > 1000; // Vérifier performance minimale
}

// Générer des actions aléatoires pour les tests
const randomActions = generateRandomActions(
  ['counter/increment', 'tasks/add', 'user/update'], 
  100,  // Nombre de créateurs d'actions à générer
  (type) => {
    // Générateur de payload selon le type
    switch(type) {
      case 'counter/increment': return Math.floor(Math.random() * 10);
      case 'tasks/add': return { id: Math.random(), title: `Tâche ${Date.now()}` };
      default: return { id: 1, name: 'Test' };
    }
  }
);

// Diagnostiquer l'état du store
import { diagnoseStore } from '@projet-rb2/state/utils';

const diagnosis = diagnoseStore(store);
console.log('Structure du store:', diagnosis.slices);
console.log('Taille mémoire:', diagnosis.storeSize, 'bytes');
console.log('Profondeur maximale:', diagnosis.stateDepth);
```

### Hydratation sélective

Le système d'hydratation sélective permet d'optimiser la gestion des données volumineuses dans le store Redux. Il offre plusieurs stratégies pour charger et décharger les données à la demande, réduisant ainsi l'empreinte mémoire de l'application.

```typescript
import { 
  selectiveHydration, 
  HydrationStrategy, 
  HydrationEntityType,
  withHydration,
  createHydrationMiddleware
} from '@projet-rb2/state/utils';

// Configuration de l'hydratation pour les utilisateurs
const usersHydrationConfig = {
  entityType: HydrationEntityType.USER,
  strategy: HydrationStrategy.LAZY,  // Chargement à la demande
  metadataFields: ['id', 'username', 'role', 'lastLogin'],  // Champs toujours conservés
  slices: ['users'],
  expirationTime: 5 * 60 * 1000,  // 5 minutes
  maxItemsInMemory: 100,
  createLoadAction: (id) => ({ type: 'users/loadDetails', payload: { id } })
};

// Créer un reducer avec support d'hydratation
import { createReducer } from '@reduxjs/toolkit';
const baseReducer = createReducer({}, {/* ... */});
const hydratedReducer = withHydration(usersHydrationConfig, baseReducer);

// Configurer le store avec le middleware d'hydratation
import { configureStore } from '@reduxjs/toolkit';
const store = configureStore({
  reducer: {
    users: hydratedReducer
  },
  middleware: (getDefaultMiddleware) => 
    getDefaultMiddleware().concat(createHydrationMiddleware())
});

// Initialiser le gestionnaire d'hydratation
selectiveHydration.initialize(store, 50 * 1024 * 1024); // Limite de 50 MB

// Initialiser une entité avec seulement ses métadonnées
selectiveHydration.initializeEntity(
  HydrationEntityType.USER, 
  'user123', 
  { id: 'user123', username: 'john.doe', role: 'admin' }
);

// Charger les données complètes d'une entité
selectiveHydration.loadEntityData(
  HydrationEntityType.USER,
  'user123',
  {
    id: 'user123',
    username: 'john.doe',
    role: 'admin',
    email: '<EMAIL>',
    preferences: { theme: 'dark' },
    address: { city: 'Paris' },
    // ... autres données complètes
  }
);

// Créer un sélecteur qui gère l'hydratation à la demande
const baseSelector = (state, id) => state.users[id];
const userSelector = selectiveHydration.createEntitySelector(
  HydrationEntityType.USER,
  baseSelector
);

// Utiliser le sélecteur (déclenchera automatiquement le chargement si nécessaire)
const UserComponent = ({ userId }) => {
  const user = useSelector(state => userSelector(state, userId));
  // ...
};

// Libérer manuellement la mémoire quand nécessaire
selectiveHydration.unloadEntityData(HydrationEntityType.USER, 'user123');
```

Les stratégies d'hydratation disponibles sont :

- `FULL` : Charge toujours les données complètes de l'entité.
- `LAZY` : Charge uniquement les métadonnées et charge les données complètes à la demande.
- `PROGRESSIVE` : Charge les données progressivement par lots.
- `AUTO_UNLOAD` : Décharge automatiquement les données non utilisées après un certain temps.

Le système surveille également l'utilisation de la mémoire et décharge automatiquement les entités les moins utilisées lorsque la limite mémoire est atteinte.

### Gestion des conflits

Le système de gestion des conflits permet de résoudre les divergences entre les données locales et distantes.

```typescript
import { conflictManager, ConflictResolutionStrategies } from '@projet-rb2/state/sync/conflictManager';

// Enregistrer une stratégie personnalisée
conflictManager.registerStrategy({
  name: 'MERGE_TASKS',
  resolve: (serverData, localData) => {
    // Logique personnalisée pour fusionner les données
    return {
      ...serverData,
      completedItems: [
        ...new Set([...serverData.completedItems, ...localData.completedItems])
      ],
      updatedAt: Date.now()
    };
  }
});

// Définir des stratégies par type d'entité
conflictManager.setDefaultStrategyForEntityType('tasks', 'MERGE_TASKS');
conflictManager.setDefaultStrategyForEntityType('profile', 'CLIENT_WINS');
conflictManager.setDefaultStrategyForEntityType('settings', 'SERVER_WINS');

// Enregistrer un gestionnaire personnalisé pour un type spécifique
conflictManager.registerConflictHandler('comments', async (conflict) => {
  // Logique complexe pour résoudre les conflits de commentaires
  const mergedData = await mergeComments(conflict.serverData, conflict.clientData);
  
  return {
    resolved: true,
    resolvedData: mergedData,
    strategy: 'CUSTOM_MERGE'
  };
});
```

### Diffusion sélective (Selective Broadcast)

Le système de diffusion sélective permet d'optimiser la communication en temps réel en envoyant les actions Redux uniquement aux destinataires concernés, plutôt qu'à tous les clients connectés. Cette approche réduit considérablement la bande passante utilisée et améliore la scalabilité des applications.

```typescript
import { 
  selectiveBroadcast, 
  createSelectiveBroadcastMiddleware,
  RecipientType,
  withBroadcast,
  createUserBasedAnalyzer,
  createRoleBasedAnalyzer,
  createResourceBasedAnalyzer
} from '@projet-rb2/state/sync';

// Configurer le store avec le middleware de diffusion sélective
import { configureStore } from '@reduxjs/toolkit';
const store = configureStore({
  reducer: {
    // vos reducers...
  },
  middleware: (getDefaultMiddleware) => 
    getDefaultMiddleware().concat(createSelectiveBroadcastMiddleware())
});

// Initialiser le système de diffusion avec le service de synchronisation
import { createRealtimeSync } from '@projet-rb2/state/sync';
const realtimeSync = createRealtimeSync({
  serverUrl: 'wss://api.exemple.com/realtime'
});

selectiveBroadcast.initialize(store, realtimeSync, {
  enableBatching: true,
  batchInterval: 200,
  defaultDelay: 0,
  defaultPriority: 5,
  maxBatchSize: 20
});

// Enregistrer des analyseurs automatiques d'actions
// 1. Analyseur basé sur l'utilisateur
const taskAssigneeAnalyzer = createUserBasedAnalyzer(
  'tasks/assignTask',
  (action) => [action.payload.userId]  // Envoyer à l'utilisateur assigné
);

// 2. Analyseur basé sur les rôles
const adminAnalyzer = createRoleBasedAnalyzer(
  'users/delete',
  () => ['admin']  // Envoyer à tous les administrateurs
);

// 3. Analyseur basé sur les ressources
const projectAnalyzer = createResourceBasedAnalyzer(
  'projects/update',
  (action, store) => [{
    type: 'project',
    id: action.payload.id
  }]  // Envoyer à tous les abonnés du projet
);

// Enregistrer les analyseurs
selectiveBroadcast.registerBroadcastAnalyzer(taskAssigneeAnalyzer);
selectiveBroadcast.registerBroadcastAnalyzer(adminAnalyzer);
selectiveBroadcast.registerBroadcastAnalyzer(projectAnalyzer);

// Créer des destinataires pour la diffusion manuelle
const userRecipient = selectiveBroadcast.constructor.createUserRecipient('user123');
const roleRecipient = selectiveBroadcast.constructor.createRoleRecipient('manager');
const groupRecipient = selectiveBroadcast.constructor.createGroupRecipient('team-alpha');

// Diffuser manuellement une action à des destinataires spécifiques
const broadcastId = selectiveBroadcast.broadcastAction(
  { type: 'notification/new', payload: { message: 'Important!' } },
  {
    recipients: [userRecipient, roleRecipient],
    priority: 1,  // Priorité élevée (0-10)
    delay: 0      // Envoyer immédiatement
  }
);

// Créer une action avec des métadonnées de diffusion intégrées
const broadcastAction = withBroadcast(
  { type: 'tasks/update', payload: { id: 'task123', status: 'completed' } },
  {
    recipients: [
      { type: RecipientType.USER, id: 'user456' },
      { type: RecipientType.GROUP, id: 'project-team' }
    ],
    priority: 5
  }
);
dispatch(broadcastAction);  // Le middleware gérera la diffusion

// Annuler une diffusion programmée
selectiveBroadcast.cancelBroadcast(broadcastId);

// Créer un hook React pour la diffusion sélective
import { useDispatch } from 'react-redux';
import { useCallback } from 'react';

function useBroadcastDispatch() {
  const dispatch = useDispatch();
  
  return useCallback((action, recipients, options = {}) => {
    const broadcastAction = withBroadcast(action, {
      recipients,
      ...options
    });
    
    dispatch(broadcastAction);
  }, [dispatch]);
}

// Exemple d'utilisation du hook
function NotificationButton() {
  const broadcastDispatch = useBroadcastDispatch();
  
  const sendNotification = () => {
    broadcastDispatch(
      { type: 'notification/send', payload: { message: 'Nouvelle mise à jour!' } },
      [
        { type: RecipientType.ROLE, id: 'user' },
        { type: RecipientType.USER, id: 'admin123' }
      ],
      { priority: 2 }
    );
  };
  
  return <button onClick={sendNotification}>Envoyer notification</button>;
}
```

Le système de diffusion sélective offre plusieurs avantages:

- **Efficacité** : Minimise la consommation de bande passante en envoyant les mises à jour uniquement aux clients concernés.
- **Sécurité** : Garantit que les utilisateurs ne reçoivent que les données auxquelles ils ont accès.
- **Scalabilité** : Permet de gérer un grand nombre de clients connectés simultanément.
- **Flexibilité** : Offre plusieurs stratégies d'analyse des actions pour déterminer automatiquement les destinataires.

Vous pouvez combiner la diffusion sélective avec les autres fonctionnalités comme la gestion hors ligne et la résolution des conflits pour créer des applications en temps réel performantes et robustes.

## Configuration complète du store

Voici un exemple de configuration complète du store avec toutes les fonctionnalités:

```typescript
import { createStore } from '@projet-rb2/state';
import { LogLevel } from '@projet-rb2/state/utils/logger';

const { store, persistor } = createStore({
  // Options de persistance
  persistOptions: {
    key: 'myapp',
    version: 2,
    blacklist: ['ui', 'temp']
  },
  
  // Configuration des middlewares
  middlewareOptions: {
    enable: {
      sync: true,
      error: true,
      logger: true
    },
    
    // Options de synchronisation
    syncOptions: {
      wsUrl: 'wss://api.projet-rb2.com',
      wsPath: '/sync',
      offlineQueueEnabled: true,
      offlineQueueRetryInterval: 30000
    },
    
    // Options pour le middleware d'erreur
    errorOptions: {
      errorHandlers: {
        // Gérer les erreurs d'authentification
        authentication: (error, store) => {
          if (error.status === 401) {
            store.dispatch({ type: 'auth/logout' });
          }
        }
      }
    },
    
    // Options pour le logger
    loggerOptions: {
      actionFilter: (action) => !action.type.startsWith('@@internal')
    }
  },
  
  // Activer le suivi des performances
  enablePerformanceTracking: true,
  
  // État initial optionnel
  preloadedState: initialState
});
```

## Licence

Propriété de Projet-RB2. Tous droits réservés. 