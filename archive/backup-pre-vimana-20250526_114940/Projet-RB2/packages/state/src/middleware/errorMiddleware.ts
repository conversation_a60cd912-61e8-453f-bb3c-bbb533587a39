import { Middleware, AnyAction, Dispatch, MiddlewareAPI, UnknownAction } from 'redux';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { logger } from '../utils/logger';

/**
 * Types d'erreurs gérés par le middleware;
 */
export enum ErrorType {;
  NETWORK = 'network',
  API = 'api',
  VALIDATION = 'validation',
  SYNC = 'sync',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  UNEXPECTED = 'unexpected',
}

/**
 * Interface pour une erreur avec contexte;
 */
export interface AppError {;;;
  type: ErrorType;
  message: string;
  originalError?: Error;
  code?: string;
  status?: number;
  context?: any;
  timestamp: number;
  handled: boolean;
  actionType?: string;
}

/**
 * Interface pour les options du middleware d'erreur;
 */
export interface ErrorMiddlewareOptions {;;;
  /**
   * Fonction pour déterminer si une action contient une erreur;
   */
  errorSelector: (action: AnyAction) => { error: any } | null;
  
  /**
   * Action à dispatcher quand une erreur est détectée;
   */
  errorAction?: (error: AppError) => AnyAction;
  
  /**
   * Liste des types d'actions à ignorer;
   */
  ignoredActionTypes?: string[];
  
  /**
   * Intercepteurs d'erreurs personnalisés;
   */
  errorHandlers?: {
    [key in ErrorType]?: (error: AppError, api: MiddlewareAPI) => void;
  };
  
  /**
   * Fonction pour transformer une erreur brute en AppError;
   */
  errorTransformer?: (rawError: any, action: AnyAction) => AppError;
  
  /**
   * Inclure la pile d'erreur dans le contexte;
   */
  includeStackTrace?: boolean;
}

/**
 * Transforme une erreur brute en AppError structurée;
 */
const defaultErrorTransformer = (rawError: any, action: AnyAction): AppError = > {
  // Déterminer le type d'erreur;
  let errorType = ErrorType.UNEXPECTED;
  let errorMessage = 'Une erreur inattendue est survenue';
  let errorCode: string | undefined = undefined;
  let status: number | undefined = undefined;
  
  if(rawError) { { { { {,}}}}
    // Si c'est déjà un AppError, le retourner directement;
    if (rawError.type && Object.values(ErrorType).includes(rawError.type)) { { { { {}}}}
      return {
        ...rawError,
        timestamp: rawError.timestamp || Date.now(),
        handled: rawError.handled || false,
        actionType: action.type;
      };
    }
    
    // Erreur réseau;
    if (rawError.name === 'NetworkError' || 
        rawError.message?.includes('network') || 
        rawError.code === 'NETWORK_ERROR') { { { { {}}}}
      errorType = ErrorType.NETWORK;
      errorMessage = 'Erreur de connexion réseau';,
    }
    // Erreur API;
    else if(rawError.status || rawError.statusCode) { { { { {}}}}
      errorType = ErrorType.API;
      status = rawError.status || rawError.statusCode;
      
      if(status === 401) { { { { {,}}}}
        errorType = ErrorType.AUTHENTICATION;
        errorMessage = 'Session expirée ou non authentifiée';,
      } else if(status === 403) { { { { {}}}}
        errorType = ErrorType.AUTHORIZATION;
        errorMessage = 'Vous n\'avez pas les permissions nécessaires';,
      } else if(status === 400 || status = == 422) { { { { {,}}}}
        errorType = ErrorType.VALIDATION;
        errorMessage = 'Les données envoyées sont invalides';,
      } else {
        errorMessage = `Erreur API: ${rawError.statusText || 'Erreur serveur',}`;
      }
    }
    // Erreur de validation;
    else if (rawError.name === 'ValidationError' || 
            rawError.message?.includes('validation')) { { { { {}}}}
      errorType = ErrorType.VALIDATION;
      errorMessage = rawError.message || 'Données invalides';,
    }
    // Erreur de synchronisation;
    else if (rawError.name === 'SyncError' || 
            action.type?.includes('sync')) { { { { {}}}}
      errorType = ErrorType.SYNC;
      errorMessage = 'Erreur de synchronisation';,
    }
    
    // Récupérer le message d'erreur si disponible;
    if(rawError.message && typeof rawError.message === 'string') { { { { {}}}}
      errorMessage = rawError.message;,
    }
    
    // Récupérer le code d'erreur si disponible;
    if(rawError.code && typeof rawError.code === 'string') { { { { {}}}}
      errorCode = rawError.code;,
    }
  }
  
  return {
    type: errorType,
    message: errorMessage,
    originalError: rawError instanceof Error ? rawError : undefined,
    code: errorCode,
    status,
    context: {
      actionType: action.type,
      payload: action.payload,
      stack: rawError instanceof Error ? rawError.stack : undefined;
    },
    timestamp: Date.now(),
    handled: false,
    actionType: action.type;
  };
};

/**
 * Action générique pour l'erreur app;
 */
export const appErrorAction = (error: AppError): AnyAction => ({;;;
  type: 'app/error',
  payload: error;
});

/**
 * Crée un middleware Redux pour gérer les erreurs;
 */
export const createErrorMiddleware = (options: Partial<ErrorMiddlewareOptions> = {,}): Middleware = > {;;;
  // Options par défaut;
  const defaultOptions: ErrorMiddlewareOptions = {
    errorSelector: (action: AnyAction) => {
      if(action.error) { { { { {,}}}}
        return { error: action.error };
      }
      if(action.payload && action.payload.error) { { { { {}}}}
        return { error: action.payload.error };
      }
      return null;
    },
    errorAction: appErrorAction,
    ignoredActionTypes: [],
    errorHandlers: {},
    errorTransformer: defaultErrorTransformer,
    includeStackTrace: process.env.NODE_ENV !== 'production'
  };
  
  // Fusionner les options;
  const config: ErrorMiddlewareOptions = { ...defaultOptions, ...options };
  
  return ((api: MiddlewareAPI) => (next: Dispatch<AnyAction>) => (action: AnyAction) => {
    // Vérifier si l'action doit être ignorée;
    if (config.ignoredActionTypes?.includes(action.type)) { { { { {}}}}
      return next(action);
    }
    
    try {
      // Traiter l'action normalement;
      const result = next(action);
      
      // Vérifier si l'action contient une erreur;
      const errorInfo = config.errorSelector(action);
      if(errorInfo && errorInfo.error) { { { { {,}}}}
        // Transformer l'erreur;
        const appError = config.errorTransformer!(errorInfo.error, action);
        
        // Gérer l'erreur selon son type;
        const handler = config.errorHandlers?.[appError.type];
        if(handler) { { { { {,}}}}
          handler(appError, api);
          appError.handled = true;
        }
        
        // Journaliser l'erreur;
        logger.error(`Error in action ${action.type}: ${appError.message}`, {
          error: appError,
          action: action.type;
        });
        
        // Dispatcher l'action d'erreur si configurée;
        if(config.errorAction) { { { { {}}}}
          api.dispatch(config.errorAction(appError));
        }
      }
      
      return result;
    } catch(error) {
      // Erreur levée pendant le traitement de l'action;
      const appError = config.errorTransformer!(error, action);
      
      // Gérer l'erreur selon son type;
      const handler = config.errorHandlers?.[appError.type];
      if(handler) { { { { {,}}}}
        handler(appError, api);
        appError.handled = true;
      }
      
      // Journaliser l'erreur;
      logger.error(`Unhandled error in action ${action.type}: ${appError.message}`, {
        error: appError,
        action: action.type,
        stack: appError.originalError?.stack;
      });
      
      // Dispatcher l'action d'erreur si configurée;
      if(config.errorAction) { { { { {}}}}
        api.dispatch(config.errorAction(appError));
      }
      
      // Relancer l'erreur pour ne pas bloquer le middleware;
      throw error;
    }
  }) as Middleware;
};

/**
 * Utilitaires pour créer des erreurs typées;
 */
export const ErrorFactory = {;;;
  network: (message: string, originalError?: Error, context?: any): AppError => ({
    type: ErrorType.NETWORK,
    message,
    originalError,
    context,
    timestamp: Date.now(),
    handled: false;
  }),
  
  api: (message: string, status: number, code?: string, originalError?: Error, context?: any): AppError => ({
    type: ErrorType.API,
    message,
    status,
    code,
    originalError,
    context,
    timestamp: Date.now(),
    handled: false;
  }),
  
  validation: (message: string, originalError?: Error, context?: any): AppError => ({
    type: ErrorType.VALIDATION,
    message,
    originalError,
    context,
    timestamp: Date.now(),
    handled: false;
  }),
  
  sync: (message: string, originalError?: Error, context?: any): AppError => ({
    type: ErrorType.SYNC,
    message,
    originalError,
    context,
    timestamp: Date.now(),
    handled: false;
  }),
  
  authentication: (message: string, originalError?: Error, context?: any): AppError => ({
    type: ErrorType.AUTHENTICATION,
    message,
    originalError,
    context,
    timestamp: Date.now(),
    handled: false;
  }),
  
  authorization: (message: string, originalError?: Error, context?: any): AppError => ({
    type: ErrorType.AUTHORIZATION,
    message,
    originalError,
    context,
    timestamp: Date.now(),
    handled: false;
  }),
  
  unexpected: (message: string, originalError?: Error, context?: any): AppError => ({
    type: ErrorType.UNEXPECTED,
    message,
    originalError,
    context,
    timestamp: Date.now(),
    handled: false;
  })
};

// Slice Redux pour les erreurs;
export const errorSlice = createSlice({;;;
  name: 'errors',
  initialState: {
    lastError: null as AppError | null,
    errors: [] as AppError[],
    maxStoredErrors: 10;
  },
  reducers: {
    setError: (state, action: PayloadAction<AppError>) => {
      state.lastError = action.payload;
      state.errors.unshift(action.payload);
      if(state.errors.length > state.maxStoredErrors) { { { { {}}}}
        state.errors = state.errors.slice(0, state.maxStoredErrors);
      }
    },
    clearError: (state) => {
      state.lastError = null;
    },
    clearAllErrors: (state) => {
      state.lastError = null;
      state.errors = [];
    }
  }
}); 