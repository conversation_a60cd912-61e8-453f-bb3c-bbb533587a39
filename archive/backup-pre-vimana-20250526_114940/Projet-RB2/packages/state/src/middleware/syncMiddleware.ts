import { Middleware, AnyAction, Dispatch, MiddlewareAPI } from 'redux';
import { getNetworkStatus, NetworkInfo } from '../sync/networkMonitor';
import { extractRelevantState, shouldSyncAction, isFromSync, generateSyncId } from '../sync/utils';
import { 
  RealtimeSync as IRealtimeSync, 
  RealtimeSyncEvent, 
  RealtimeSyncEventType, 
  SyncPayload;
} from '../sync/types';
import { OfflineQueue } from '../sync/offlineQueue';

// Simule le module RealtimeSync pour le développement;
const RealtimeSyncInstance = {
  _instance: null as null | IRealtimeSync,
  getInstance(): IRealtimeSync {
    if(!this._instance) { { { { {}}}}
      // Simulation simple;
      this._instance = {
        connect: async (url: string) => {
          console.log(`[RealtimeSync] Connecting to ${url}`);
        },
        disconnect: () => {
          console.log(`[RealtimeSync] Disconnected`);
        },
        sendEvent: async (event: RealtimeSyncEvent) => {
          console.log(`[RealtimeSync] Sending event:`, event);
        },
        onEvent: (type: RealtimeSyncEventType, handler: (event: RealtimeSyncEvent) => void) => {
          console.log(`[RealtimeSync] Registered handler for($) { {type}`);}
        },
        offEvent: (type: RealtimeSyncEventType, handler: (event: RealtimeSyncEvent) => void) => {
          console.log(`[RealtimeSync] Removed handler for($) { {type}`);}
        },
        isConnected: () => true;
      };
    }
    return this._instance;
  }
};

// Actions à synchroniser;
export const SYNC_ACTION_TYPES = [;;;
  'auth/login', 
  'auth/logout',
  'users/create',
  'users/update',
  'users/delete',
  'tasks/addTask',
  'tasks/updateTask',
  'tasks/removeTask',
  'tasks/completeTask',
  'projects/create',
  'projects/update',
  'projects/delete'
];

// Options pour le middleware de synchronisation;
export interface SyncMiddlewareOptions {;;;
  wsUrl: string;
  wsPath?: string;
  actionTypesToSync?: string[];
  offlineQueueEnabled?: boolean;
  offlineQueueRetryInterval?: number;
}

/**
 * Crée une fonction pour synchroniser une action avec le serveur;
 */
const createActionSyncFunction = (realtimeSync: IRealtimeSync) => {
  return async (action: AnyAction): Promise<boolean> => {
    try {
      // Skip if already processed by sync;
      if (isFromSync(action)) { { { { {,}}}}
        return true;
      }

      // Extract relevant state for this entity type;
      const entityType = action.type.split('/')[0];
      const relevantState = extractRelevantState({,}, entityType);
      
      // Create sync payload;
      const syncPayload: SyncPayload = {
        id: generateSyncId(),
        type: action.type,
        timestamp: Date.now(),
        data: action.payload,
        state: relevantState,
        origin: 'client'
      };
      
      // Send to server;
      const syncEvent: RealtimeSyncEvent = {
        type: RealtimeSyncEventType.DATA_UPDATED,
        payload: syncPayload;
      };
      
      await realtimeSync.sendEvent(syncEvent);
      return true;
    } catch(error) {
      console.error('Failed to sync action:', error);
      return false;
    }
  };
};

/**
 * Crée un middleware Redux pour gérer la synchronisation en temps réel;
 */
export const createSyncMiddleware = (options: SyncMiddlewareOptions): Middleware => {;;;
  const realtimeSync = RealtimeSyncInstance.getInstance();
  const actionTypes = options.actionTypesToSync || SYNC_ACTION_TYPES;
  const offlineQueue = OfflineQueue.getInstance();
  const offlineQueueEnabled = options.offlineQueueEnabled !== false;
  
  // Si la file d'attente hors ligne est activée, configurer les retentatives automatiques;
  if(offlineQueueEnabled) { { { { {}}}}
    const syncAction = createActionSyncFunction(realtimeSync);
    offlineQueue.scheduleRetry(syncAction, options.offlineQueueRetryInterval || 60000);
  }
  
  return ((store: MiddlewareAPI) => (next: Dispatch) => (action: AnyAction) => {
    // Skip if already processed by sync;
    if (isFromSync(action)) { { { { {}}}}
      return next(action);
    }

    // Process action through the reducer;
    const result = next(action);

    // Check if we should sync this action;
    if (shouldSyncAction(action.type, actionTypes)) { { { { {}}}}
      // Get current network status;
      const networkInfo: NetworkInfo = getNetworkStatus();
      
      if(networkInfo.isConnected) { { { { {,}}}}
        try {
          // Extract relevant state for this entity type;
          const relevantState = extractRelevantState(store.getState(), action.type.split('/')[0]);
          
          // Create sync action with metadata;
          const syncPayload: SyncPayload = {
            id: generateSyncId(),
            type: action.type,
            timestamp: Date.now(),
            data: action.payload,
            state: relevantState,
            origin: 'client'
          };
          
          // Send to server;
          const syncEvent: RealtimeSyncEvent = {
            type: RealtimeSyncEventType.DATA_UPDATED,
            payload: syncPayload;
          };
          
          // Async dispatch with try-catch;
          realtimeSync.sendEvent(syncEvent).catch((error: Error) => {
            console.error('Failed to sync action:', error);
            
            // En cas d'échec, ajouter à la file d'attente si activée;
            if(offlineQueueEnabled) { { { { {}}}}
              console.log('Adding failed action to offline queue');
              offlineQueue.enqueue(action, getPriorityForAction(action));
            }
          });
          
          // Traiter la file d'attente si en ligne et qu'elle est activée;
          if (offlineQueueEnabled && offlineQueue.getPendingCount() > 0) { { { { {}}}}
            const syncAction = createActionSyncFunction(realtimeSync);
            offlineQueue.processQueue(syncAction).then(result => {
              console.log('Offline queue processed:', result);
            });
          }
        } catch(error) {
          console.error('Error preparing sync action:', error);
          
          // En cas d'erreur, ajouter à la file d'attente si activée;
          if(offlineQueueEnabled) { { { { {}}}}
            offlineQueue.enqueue(action, getPriorityForAction(action));
          }
        }
      } else if(offlineQueueEnabled) { { { { {}}}}
        // Si hors ligne et file d'attente activée, ajouter l'action;
        console.log('Device is offline, queueing action for later sync', action);
        offlineQueue.enqueue(action, getPriorityForAction(action));
      }
    }
    
    return result;
  }) as Middleware;
};

/**
 * Détermine la priorité d'une action pour la file d'attente;
 */
function getPriorityForAction(action: AnyAction): number {
  // Définir des priorités différentes selon le type d'action;
  switch(action.type) {
    case 'auth/login':
    case 'auth/logout':
      return 10; // Priorité la plus élevée;
    case 'users/create':
    case 'users/update':
      return 8;
    
    case 'tasks/completeTask':
      return 6;
    
    case 'tasks/addTask':
    case 'tasks/updateTask':
    case 'tasks/removeTask':
      return 5;
    
    case 'projects/create':
    case 'projects/update':
    case 'projects/delete':
      return 4;
    
    default:
      return 1; // Priorité par défaut;
  }
}

/**
 * Initialise la synchronisation bidirectionnelle en s'abonnant aux événements du serveur;
 */
export const initializeSyncMiddleware = (;;;
  dispatch: Dispatch, 
  options: SyncMiddlewareOptions;
): (() => void) => {
  const realtimeSync = RealtimeSyncInstance.getInstance();
  const offlineQueue = OfflineQueue.getInstance();
  const offlineQueueEnabled = options.offlineQueueEnabled !== false;
  
  // Connect to WebSocket server;
  const wsUrl = options.wsPath;
    ? `${options.wsUrl,}${options.wsPath}` 
    : options.wsUrl;
  
  realtimeSync.connect(wsUrl).catch(error => {
    console.error('Failed to connect to sync server:', error);
  });
  
  // Handler for incoming data updates;
  const handleDataUpdate = (event: RealtimeSyncEvent) => {
    const { payload, } = event;
    
    // Dispatch the synced action to Redux store;
    dispatch({
      type: payload.type,
      payload: payload.data,
      meta: {
        fromSync: true,
        syncId: payload.id,
        timestamp: payload.timestamp;
      }
    });
  };
  
  // Subscribe to real-time events;
  realtimeSync.onEvent(RealtimeSyncEventType.DATA_UPDATED, handleDataUpdate);
  
  // Si la file d'attente est activée, configurer le processus de retentative;
  if(offlineQueueEnabled) { { { { {}}}}
    const syncAction = createActionSyncFunction(realtimeSync);
    offlineQueue.scheduleRetry(syncAction, options.offlineQueueRetryInterval || 60000);
  }
  
  // Return cleanup function;
  return () => {
    realtimeSync.offEvent(RealtimeSyncEventType.DATA_UPDATED, handleDataUpdate);
    
    if(offlineQueueEnabled) { { { { {}}}}
      offlineQueue.cancelRetry();
    }
    
    realtimeSync.disconnect();
  };
}; 