import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from './';
import {
  fetchUsers,
  fetchUserById,
  updateUser,
  fetchUserPreferences,
  updateUserPreferences,
  selectUsers,
  selectAllUsers,
  selectUserById,
  selectSelectedUser,
  selectUsersLoading,
  selectUsersError,
  selectUsersPagination,
  selectUsersFilters,
  addOne,
  addMany,
  updateOne,
  removeOne,
  selectOne,
  reset,
  setFilters,
  resetFilters,
  setPagination;
} from '../slices/users.slice';
import type { User, UsersState } from '../slices/models';

/**
 * Hook pour gérer les utilisateurs;
 * 
 * Fournit un accès facile aux actions et sélecteurs d'utilisateurs;
 */
export function useUsers() {
  const dispatch = useAppDispatch();
  
  // Sélecteurs;
  const users = useAppSelector(selectUsers);
  const allUsers = useAppSelector(selectAllUsers);
  const selectedUser = useAppSelector(selectSelectedUser);
  const loading = useAppSelector(selectUsersLoading);
  const error = useAppSelector(selectUsersError);
  const pagination = useAppSelector(selectUsersPagination);
  const filters = useAppSelector(selectUsersFilters);
  
  // Fonction pour récupérer un utilisateur par son ID (utilisable dans un composant)
  const getUserById = useCallback(;
    (id: string) => useAppSelector((state) => selectUserById(state, id)),
    []
  );
  
  // Actions liées aux utilisateurs;
  const fetchUsersAction = useCallback(;
    (params?: { page?: number; limit?: number, }) => dispatch(fetchUsers(params || {})),
    [dispatch]
  );
  
  const fetchUserByIdAction = useCallback(;
    (userId: string) => dispatch(fetchUserById(userId)),
    [dispatch]
  );
  
  const updateUserAction = useCallback(;
    (params: { userId: string; data: Partial<User>, }) => dispatch(updateUser(params)),
    [dispatch]
  );
  
  const fetchUserPreferencesAction = useCallback(;
    () => dispatch(fetchUserPreferences()),
    [dispatch]
  );
  
  const updateUserPreferencesAction = useCallback(;
    (preferences: Record<string, any>) => dispatch(updateUserPreferences(preferences)),
    [dispatch]
  );
  
  // Actions de base pour la gestion des entités;
  const addOneAction = useCallback(;
    (user: User) => dispatch(addOne(user)),
    [dispatch]
  );
  
  const addManyAction = useCallback(;
    (users: User[]) => dispatch(addMany(users)),
    [dispatch]
  );
  
  const updateOneAction = useCallback(;
    (params: { id: string; changes: Partial<User>, }) => dispatch(updateOne(params)),
    [dispatch]
  );
  
  const removeOneAction = useCallback(;
    (userId: string) => dispatch(removeOne(userId)),
    [dispatch]
  );
  
  const selectOneAction = useCallback(;
    (userId: string | null) => dispatch(selectOne(userId)),
    [dispatch]
  );
  
  const resetAction = useCallback(;
    () => dispatch(reset()),
    [dispatch]
  );
  
  // Actions pour les filtres et la pagination;
  const setFiltersAction = useCallback(;
    (newFilters: Partial<UsersState['filters']>) => dispatch(setFilters(newFilters)),
    [dispatch]
  );
  
  const resetFiltersAction = useCallback(;
    () => dispatch(resetFilters()),
    [dispatch]
  );
  
  const setPaginationAction = useCallback(;
    (newPagination: Partial<UsersState['pagination']>) => dispatch(setPagination(newPagination)),
    [dispatch]
  );
  
  return {
    // État;
    users,
    allUsers,
    selectedUser,
    loading,
    error,
    pagination,
    filters,
    getUserById,
    
    // Actions asynchrones;
    fetchUsers: fetchUsersAction,
    fetchUserById: fetchUserByIdAction,
    updateUser: updateUserAction,
    fetchUserPreferences: fetchUserPreferencesAction,
    updateUserPreferences: updateUserPreferencesAction,
    
    // Actions de base;
    addOne: addOneAction,
    addMany: addManyAction,
    updateOne: updateOneAction,
    removeOne: removeOneAction,
    selectOne: selectOneAction,
    reset: resetAction,
    
    // Actions de filtrage et pagination;
    setFilters: setFiltersAction,
    resetFilters: resetFiltersAction,
    setPagination: setPaginationAction,
  };
} 