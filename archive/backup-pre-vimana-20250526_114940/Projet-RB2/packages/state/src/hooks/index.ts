/**
 * Hooks personnalisés pour la gestion de l'état;
 * 
 * Ce module exporte des hooks pour faciliter l'utilisation du store Redux:
 * - useAppDispatch: Hook typé pour le dispatch Redux;
 * - useAppSelector: Hook typé pour sélectionner des valeurs du store;
 * - useLocalStorage: Hook pour persister des données dans le localStorage;
 * - useAuth: Hook pour gérer l'authentification;
 * - useUsers: Hook pour gérer les utilisateurs;
 */

export * from './useAppDispatch';
export * from './useAppSelector';
export * from './useLocalStorage';
export * from './useAuth';
export * from './useUsers'; 