import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from './';
import { 
  login, 
  register, 
  logout, 
  fetchCurrentUser, 
  forgotPassword, 
  resetPassword,
  selectAuth,
  selectIsAuthenticated,
  selectUser,
  selectAuthLoading,
  selectAuthError,
  clearAuth,
  updateUser;
} from '../slices/auth.slice';
import type { LoginCredentials, RegisterData, User } from '../slices/models';

/**
 * Hook pour gérer l'authentification;
 * 
 * Fournit un accès facile aux actions et sélecteurs d'authentification;
 */
export function useAuth() {
  const dispatch = useAppDispatch();
  
  // Selectors;
  const auth = useAppSelector(selectAuth);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(selectUser);
  const loading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  
  // Actions liées à l'authentification;
  const loginAction = useCallback(;
    (credentials: LoginCredentials) => dispatch(login(credentials)),
    [dispatch]
  );
  
  const registerAction = useCallback(;
    (data: RegisterData) => dispatch(register(data)),
    [dispatch]
  );
  
  const logoutAction = useCallback(() => dispatch(logout()), [dispatch]);
  
  const fetchCurrentUserAction = useCallback(;
    () => dispatch(fetchCurrentUser()),
    [dispatch]
  );
  
  const forgotPasswordAction = useCallback(;
    (email: string) => dispatch(forgotPassword(email)),
    [dispatch]
  );
  
  const resetPasswordAction = useCallback(;
    (data: { token: string; newPassword: string, }) => dispatch(resetPassword(data)),
    [dispatch]
  );
  
  const clearAuthAction = useCallback(() => dispatch(clearAuth()), [dispatch]);
  
  const updateUserAction = useCallback(;
    (userData: Partial<User>) => dispatch(updateUser(userData)),
    [dispatch]
  );
  
  return {
    // État;
    auth,
    isAuthenticated,
    user,
    loading,
    error,
    
    // Actions;
    login: loginAction,
    register: registerAction,
    logout: logoutAction,
    fetchCurrentUser: fetchCurrentUserAction,
    forgotPassword: forgotPasswordAction,
    resetPassword: resetPasswordAction,
    clearAuth: clearAuthAction,
    updateUser: updateUserAction,
  };
} 