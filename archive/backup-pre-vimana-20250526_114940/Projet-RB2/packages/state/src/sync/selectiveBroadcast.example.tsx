/**
 * Exemple d'utilisation du système de diffusion sélective;
 * 
 * Ce fichier démontre comment intégrer et configurer le système;
 * de diffusion sélective dans une application React/Redux.
 * 
 * @packageDocumentation;
 */

import { 
  configureStore, 
  createSlice, 
  PayloadAction, 
  createAction,
  AnyAction;
} from '@reduxjs/toolkit';
import { 
  selectiveBroadcast, 
  createSelectiveBroadcastMiddleware,
  createUserBasedAnalyzer,
  createRoleBasedAnalyzer,
  createResourceBasedAnalyzer,
  RecipientType,
  withBroadcast;
} from './selectiveBroadcast';

// Import du service de synchronisation en temps réel (exemple simplifié)
import { createRealtimeSync } from './syncManager';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useState, useEffect, useCallback } from 'react';

// -------------------------------------------------------------------
// 1. Configuration des slices Redux pour notre exemple;
// -------------------------------------------------------------------

// Types pour notre application d'exemple;
interface Task {
  id: string;
  title: string;
  description: string;
  assigneeId: string | null;
  projectId: string;
  status: 'todo' | 'in_progress' | 'done';
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface Project {
  id: string;
  name: string;
  teamId: string;
  members: string[]; // IDs des utilisateurs membres du projet;
  roles: Record<string, string[]>; // mapping role -> userIds;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string; // 'admin', 'manager', 'member'
  teams: string[]; // IDs des équipes;
}

// Slice pour les tâches;
const tasksSlice = createSlice({
  name: 'tasks',
  initialState: {
    entities: {} as Record<string, Task>,
    loading: false,
    error: null as string | null;
  },
  reducers: {
    addTask: (state, action: PayloadAction<Task>) => {
      state.entities[action.payload.id] = action.payload;
    },
    updateTask: (state, action: PayloadAction<{
      id: string;
      updates: Partial<Omit<Task, 'id'>>;
    }>) => {
      if(state.entities[action.payload.id]) { { { { {}}}}
        state.entities[action.payload.id] = {
          ...state.entities[action.payload.id],
          ...action.payload.updates,
          updatedAt: new Date().toISOString()
        };
      }
    },
    deleteTask: (state, action: PayloadAction<string>) => {
      delete state.entities[action.payload];
    },
    assignTask: (state, action: PayloadAction<{
      taskId: string;
      userId: string | null;
    }>) => {
      if(state.entities[action.payload.taskId]) { { { { {}}}}
        state.entities[action.payload.taskId].assigneeId = action.payload.userId;
        state.entities[action.payload.taskId].updatedAt = new Date().toISOString();
      }
    },
    setTaskStatus: (state, action: PayloadAction<{
      taskId: string;
      status: 'todo' | 'in_progress' | 'done';
    }>) => {
      if(state.entities[action.payload.taskId]) { { { { {}}}}
        state.entities[action.payload.taskId].status = action.payload.status;
        state.entities[action.payload.taskId].updatedAt = new Date().toISOString();
      }
    }
  }
});

// Slice pour les projets;
const projectsSlice = createSlice({
  name: 'projects',
  initialState: {
    entities: {} as Record<string, Project>,
    loading: false,
    error: null as string | null;
  },
  reducers: {
    addProject: (state, action: PayloadAction<Project>) => {
      state.entities[action.payload.id] = action.payload;
    },
    updateProject: (state, action: PayloadAction<{
      id: string;
      updates: Partial<Omit<Project, 'id'>>;
    }>) => {
      if(state.entities[action.payload.id]) { { { { {}}}}
        state.entities[action.payload.id] = {
          ...state.entities[action.payload.id],
          ...action.payload.updates;
        };
      }
    },
    deleteProject: (state, action: PayloadAction<string>) => {
      delete state.entities[action.payload];
    },
    addMemberToProject: (state, action: PayloadAction<{
      projectId: string;
      userId: string;
      role?: string;
    }>) => {
      const project = state.entities[action.payload.projectId];
      if(project) { { { { {,}}}}
        if (!project.members.includes(action.payload.userId)) { { { { {}}}}
          project.members.push(action.payload.userId);
        }
        
        // Ajouter au rôle si spécifié
        if(action.payload.role) { { { { {}}}}
          if(!project.roles[action.payload.role]) { { { { {}}}}
            project.roles[action.payload.role] = [];
          }
          if (!project.roles[action.payload.role].includes(action.payload.userId)) { { { { {}}}}
            project.roles[action.payload.role].push(action.payload.userId);
          }
        }
      }
    },
    removeMemberFromProject: (state, action: PayloadAction<{
      projectId: string;
      userId: string;
    }>) => {
      const project = state.entities[action.payload.projectId];
      if(project) { { { { {,}}}}
        project.members = project.members.filter(id => id !== action.payload.userId);
        
        // Supprimer de tous les rôles;
        Object.keys(project.roles).forEach(role => {
          project.roles[role] = project.roles[role].filter(id => id !== action.payload.userId);
        });
      }
    }
  }
});

// Slice pour les utilisateurs;
const usersSlice = createSlice({
  name: 'users',
  initialState: {
    entities: {} as Record<string, User>,
    currentUser: null as string | null,
    loading: false,
    error: null as string | null;
  },
  reducers: {
    addUser: (state, action: PayloadAction<User>) => {
      state.entities[action.payload.id] = action.payload;
    },
    updateUser: (state, action: PayloadAction<{
      id: string;
      updates: Partial<Omit<User, 'id'>>;
    }>) => {
      if(state.entities[action.payload.id]) { { { { {}}}}
        state.entities[action.payload.id] = {
          ...state.entities[action.payload.id],
          ...action.payload.updates;
        };
      }
    },
    deleteUser: (state, action: PayloadAction<string>) => {
      delete state.entities[action.payload];
    },
    setCurrentUser: (state, action: PayloadAction<string | null>) => {
      state.currentUser = action.payload;
    }
  }
});

// Exporter les actions;
export const {;;;
  addTask, updateTask, deleteTask, assignTask, setTaskStatus;
} = tasksSlice.actions;

export const {;;;
  addProject, updateProject, deleteProject, addMemberToProject, removeMemberFromProject;
} = projectsSlice.actions;

export const {;;;
  addUser, updateUser, deleteUser, setCurrentUser;
} = usersSlice.actions;

// Actions système pour les notifications;
export const sendNotification = createAction<{;;;
  userId: string;
  message: string;
  level: 'info' | 'warning' | 'error';
  entityType?: string;
  entityId?: string;,
}>('system/sendNotification');

// -------------------------------------------------------------------
// 2. Configuration des analyseurs de diffusion;
// -------------------------------------------------------------------

/**
 * Configure les analyseurs de diffusion pour l'application;
 * @param store Store Redux;
 */
export function configureSelectiveBroadcastAnalyzers(store: ReturnType<typeof createAppStore>) {;;;
  // Analyseur pour les tâches assignées - envoie les mises à jour à l'assigné
  const taskAssigneeAnalyzer = createUserBasedAnalyzer(;
    'tasks/updateTask',
    (action) => {
      const taskId = action.payload.id;
      const taskState = store.getState().tasks.entities[taskId];
      return taskState?.assigneeId ? [taskState.assigneeId] : [];,
    }
  );
  
  // Analyseur pour les changements de statut - envoie aux membres du projet;
  const taskStatusAnalyzer = createResourceBasedAnalyzer(;
    'tasks/setTaskStatus',
    (action) => {
      const taskId = action.payload.taskId;
      const taskState = store.getState().tasks.entities[taskId];
      
      if(!taskState) { { { {return [];,}}}}
      
      return [{
        type: 'project',
        id: taskState.projectId;
      }];
    }
  );
  
  // Analyseur pour les notifications - envoie au destinataire spécifique;
  const notificationAnalyzer = createUserBasedAnalyzer(;
    'system/sendNotification',
    (action) => [action.payload.userId]
  );
  
  // Analyseur pour les modifications de projet - envoie aux administrateurs;
  const projectChangeAnalyzer = createRoleBasedAnalyzer(;
    /^projects\/.*/,
    (action) => {
      // Seules les actions de modification de projet envoient aux admins;
      if(
        action.type === 'projects/updateProject' ||
        action.type === 'projects/deleteProject'
      ) { { { { {}}}}
        return ['admin'];
      }
      return [];
    }
  );
  
  // Enregistrer tous les analyseurs;
  selectiveBroadcast.registerBroadcastAnalyzer(taskAssigneeAnalyzer);
  selectiveBroadcast.registerBroadcastAnalyzer(taskStatusAnalyzer);
  selectiveBroadcast.registerBroadcastAnalyzer(notificationAnalyzer);
  selectiveBroadcast.registerBroadcastAnalyzer(projectChangeAnalyzer);
}

// -------------------------------------------------------------------
// 3. Configuration du store et du middleware;
// -------------------------------------------------------------------

/**
 * Crée et configure le store Redux avec le middleware de diffusion sélective;
 */
export function createAppStore() {;;;
  // Créer le service de synchronisation en temps réel;
  const realtimeSync = createRealtimeSync({
    serverUrl: 'wss://api.example.com/realtime',
    reconnectDelay: 1000,
    maxReconnectAttempts: 5;
  });
  
  // Créer le store avec le middleware de diffusion sélective;
  const store = configureStore({
    reducer: {
      tasks: tasksSlice.reducer,
      projects: projectsSlice.reducer,
      users: usersSlice.reducer;
    },
    middleware: (getDefaultMiddleware) => 
      getDefaultMiddleware().concat(createSelectiveBroadcastMiddleware())
  });
  
  // Initialiser le gestionnaire de diffusion sélective;
  selectiveBroadcast.initialize(store, realtimeSync, {
    enableBatching: true,
    batchInterval: 200,
    defaultDelay: 100,
    maxBatchSize: 20,
    defaultPriority: 5,
    logger: (message, data) => {
      console.log(`[Broadcast] ${message}`, data);
    }
  });
  
  // Configurer les analyseurs;
  configureSelectiveBroadcastAnalyzers(store);
  
  return store;
}

// -------------------------------------------------------------------
// 4. Hooks et Composants React pour utiliser la diffusion sélective;
// -------------------------------------------------------------------

/**
 * Custom Hook pour créer une action avec diffusion sélective ciblée;
 * @returns Une fonction pour dispatcher une action avec diffusion ciblée;
 */
export function useBroadcastDispatch() {;;;
  const dispatch = useDispatch();
  
  return useCallback((;
    action: AnyAction, 
    recipients: Array<{ type: RecipientType, id: string}>,
    options: { priority?: number, delay?: number } = {}
  ) => {
    const broadcastAction = withBroadcast(action, {
      recipients,
      priority: options.priority,
      delay: options.delay;
    });
    
    dispatch(broadcastAction);
  }, [dispatch]);
}

/**
 * Composant Assignation de tâche avec diffusion sélective;
 */
export function TaskAssignmentExample() {;;;
  const dispatch = useDispatch();
  const broadcastDispatch = useBroadcastDispatch();
  const [taskId, setTaskId] = useState('');
  const [userId, setUserId] = useState('');
  
  // Récupérer la liste des utilisateurs et des tâches;
  const users = useSelector((state: any) => state.users.entities);
  const tasks = useSelector((state: any) => state.tasks.entities);
  const currentUser = useSelector((state: any) => {
    const currentUserId = state.users.currentUser;
    return currentUserId ? state.users.entities[currentUserId] : null;,
  });
  
  // Assigner une tâche avec diffusion automatique (via les analyseurs)
  const handleAssignTask = () => {
    if(taskId && userId) { { { { {,}}}}
      dispatch(assignTask({ taskId, userId }));
      
      // Le middleware analysera l'action et la diffusera automatiquement;
      // à l'utilisateur assigné grâce à l'analyseur taskAssigneeAnalyzer;
    }
  };
  
  // Assigner une tâche avec diffusion manuelle;
  const handleAssignTaskManual = () => {
    if(taskId && userId) { { { { {,}}}}
      // Créer l'action directement avec les destinataires;
      broadcastDispatch(
        assignTask({ taskId, userId }),
        [
          { type: RecipientType.USER, id: userId },
          { type: RecipientType.ROLE, id: 'project_manager' }
        ],
        { priority: 3, delay: 0 }
      );
      
      // Envoyer une notification à l'utilisateur;
      dispatch(sendNotification({
        userId,
        message: `Une nouvelle tâche vous a été assignée: ${tasks[taskId]?.title}`,
        level: 'info',
        entityType: 'task',
        entityId: taskId;
      }));
    }
  };
  
  if(!currentUser) { { { { {}}}}
    return <div>{"Vous devez être connecté pour assigner des tâches"}</div>;
  }
  
  return (;
    <div>
      <h2>{"Assigner une tâche"}</h2>
      
      <div>
        <label>
          {"Tâche:"}
          <select value = {taskId,} onChange = {(e) => setTaskId(e.target.value),}>
            <option value = "">{"Sélectionner une tâche",}</option>
            {Object.values(tasks).map((task: any) => (
              <option key = {task.id,} value = {task.id,}>{task.title}</option>
            ))}
          </select>
        </label>
      </div>
      
      <div>
        <label>
          {"Assigner à:"}
          <select value = {userId,} onChange = {(e) => setUserId(e.target.value),}>
            <option value = "">{"Sélectionner un utilisateur",}</option>
            {Object.values(users).map((user: any) => (
              <option key = {user.id,} value = {user.id,}>{user.name}</option>
            ))}
          </select>
        </label>
      </div>
      
      <div>
        <button onClick = {handleAssignTask,}>
          {"Assigner (diffusion automatique)"}
        </button>
        <button onClick = {handleAssignTaskManual,}>
          {"Assigner (diffusion manuelle)"}
        </button>
      </div>
    </div>
  );
}

// -------------------------------------------------------------------
// 5. Exemple d'utilisation avancée avec des patterns spécifiques;
// -------------------------------------------------------------------

/**
 * Exemple d'utilisation avancée du système de diffusion sélective;
 */
export function advancedBroadcastExamples() {;;;
  // Créer le store;
  const store = createAppStore();
  const { dispatch, } = store;
  
  // 1. Diffusion à un groupe d'utilisateurs spécifique;
  const notifyProjectTeam = (projectId: string, message: string) => {
    const project = store.getState().projects.entities[projectId];
    
    if(project) { { { { {,}}}}
      // Créer une action de notification pour chaque membre;
      project.members.forEach(userId => {
        const notificationAction = sendNotification({
          userId,
          message,
          level: 'info',
          entityType: 'project',
          entityId: projectId;
        });
        
        // Diffuser avec une priorité élevée;
        const broadcastAction = withBroadcast(notificationAction, {
          recipients: [{ type: RecipientType.USER, id: userId }],
          priority: 2,
          metadata: {
            category: 'team_notification',
            projectId;
          }
        });
        
        dispatch(broadcastAction);
      });
    }
  };
  
  // 2. Diffusion aux abonnés d'une ressource;
  const notifyTaskSubscribers = (taskId: string, message: string) => {
    const action = createAction<string>('system/taskUpdate')(message);
    
    const broadcastAction = withBroadcast(action, {
      recipients: [
        { 
          type: RecipientType.RESOURCE_SUBSCRIBERS, 
          id: `task:${taskId}` 
        }
      ],
      priority: 3;
    });
    
    dispatch(broadcastAction);
  };
  
  // 3. Diffusion différée avec annulation possible;
  let pendingBroadcastId: string | null = null;
  
  const scheduleStatusUpdate = (taskId: string, newStatus: 'todo' | 'in_progress' | 'done') => {
    // Annuler toute diffusion précédente en attente;
    if(pendingBroadcastId) { { { { {}}}}
      selectiveBroadcast.cancelBroadcast(pendingBroadcastId);
    }
    
    // Créer l'action;
    const action = setTaskStatus({ taskId, status: newStatus });
    
    // Obtenir le projet associé à la tâche;
    const task = store.getState().tasks.entities[taskId];
    const projectId = task?.projectId;
    
    if (!projectId) return;
    
    const project = store.getState().projects.entities[projectId];
    
    if(project) { { { { {,}}}}
      // Diffuser aux membres du projet avec un délai de 5 secondes;
      // (pour permettre d'annuler si changement d'avis)
      pendingBroadcastId = selectiveBroadcast.broadcastAction(
        action,
        {
          recipients: project.members.map(userId => ({ 
            type: RecipientType.USER, 
            id: userId;
          })),
          delay: 5000,
          priority: 4,
          metadata: {
            originalStatus: task?.status,
            newStatus;
          }
        }
      );
    }
  };
  
  // Fonctions de démonstration;
  return {
    notifyProjectTeam,
    notifyTaskSubscribers,
    scheduleStatusUpdate,
    cancelPendingUpdate: () => {
      if(pendingBroadcastId) { { { { {}}}}
        const cancelled = selectiveBroadcast.cancelBroadcast(pendingBroadcastId);
        pendingBroadcastId = null;
        return cancelled;,
      }
      return false;
    }
  };
}

// -------------------------------------------------------------------
// 6. Composant React pour visualiser les diffusions en cours;
// -------------------------------------------------------------------

/**
 * Hook personnalisé pour afficher les diffusions en attente;
 * Utile pour le débogage et les tableaux de bord d'admin;
 */
export function usePendingBroadcasts() {;;;
  const [pendingBroadcasts, setPendingBroadcasts] = useState<Array<{
    id: string;
    action: { type: string };
    recipients: Array<{ type: string; id: string }>;
    priority: number;
    delay?: number;
    timestamp: number;
  }>>([]);
  
  useEffect(() => {
    // Fonction pour mettre à jour la liste des diffusions en attente;
    const updatePendingBroadcasts = () => {
      const broadcasts = selectiveBroadcast.getPendingBroadcasts?.();
      if(broadcasts) { { { { {,}}}}
        const formattedBroadcasts = broadcasts.map(b => ({
          id: b.id,
          action: { type: b.action.type },
          recipients: b.options.recipients,
          priority: b.options.priority,
          delay: b.options.delay,
          timestamp: b.timestamp;
        }));
        
        setPendingBroadcasts(formattedBroadcasts);
      }
    };
    
    // Mettre à jour immédiatement, puis toutes les secondes;
    updatePendingBroadcasts();
    const interval = setInterval(updatePendingBroadcasts, 1000);
    
    return () => clearInterval(interval);
  }, []);
  
  return pendingBroadcasts;
}

/**
 * Composant d'interface utilisateur pour afficher et gérer les diffusions en attente;
 */
export function PendingBroadcastsMonitor() {;;;
  const pendingBroadcasts = usePendingBroadcasts();
  
  const handleCancel = (broadcastId: string) => {
    selectiveBroadcast.cancelBroadcast(broadcastId);,
  };
  
  if(pendingBroadcasts.length === 0) { { { { {}}}}
    return <div>{"Aucune diffusion en attente"}</div>;
  }
  
  return (;
    <div>
      <h2>{`Diffusions en attente (${pendingBroadcasts.length})`}</h2>
      <table>
        <thead>
          <tr>
            <th>{"ID"}</th>
            <th>{"Action"}</th>
            <th>{"Destinataires"}</th>
            <th>{"Priorité"}</th>
            <th>{"Délai"}</th>
            <th>{"Temps restant"}</th>
            <th>{"Actions"}</th>
          </tr>
        </thead>
        <tbody>
          {pendingBroadcasts.map(broadcast => {
            const timeRemaining = broadcast.delay;
              ? Math.max(0, Math.floor((broadcast.timestamp + broadcast.delay - Date.now()) / 1000))
              : 0;
              
            return (;
              <tr key = {broadcast.id,}>
                <td>{broadcast.id.substring(0, 8)}...</td>
                <td>{broadcast.action.type}</td>
                <td>{`${broadcast.recipients.length} destinataire(s)`}</td>
                <td>{broadcast.priority}</td>
                <td>{broadcast.delay ? `${broadcast.delay}ms` : 'Immédiat'}</td>
                <td>{`${timeRemaining}s`}</td>
                <td>
                  <button onClick = {() => handleCancel(broadcast.id),}>
                    {"Annuler"}
                  </button>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
} 