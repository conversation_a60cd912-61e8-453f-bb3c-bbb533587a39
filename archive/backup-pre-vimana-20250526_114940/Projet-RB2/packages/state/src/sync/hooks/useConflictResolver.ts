/**
 * @file useConflictResolver.ts;
 * @description Hook personnalisé pour gérer les conflits de synchronisation;
 */

import { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';

import { 
  multiDeviceSync, 
  MultiDeviceSyncEventType,
  Conflict,
  ConflictResolutionStrategy;
} from '../multiDeviceSync';

import { conflictResolver } from '../conflictResolver';

/**
 * Interface pour les détails d'un conflit;
 */
export interface ConflictDetails {;;;
  /** Type d'entité concernée par le conflit */
  entityType: string;
  /** ID de l'entité concernée par le conflit */
  entityId: string;
  /** Version locale de l'entité */
  localVersion: any;
  /** Version distante de l'entité */
  remoteVersion: any;
  /** Liste des champs modifiés causant le conflit */
  modifiedFields: string[];
  /** Stratégie recommandée pour résoudre le conflit */
  recommendedStrategy?: ConflictResolutionStrategy;
}

/**
 * Interface pour les options du hook;
 */
export interface UseConflictResolverOptions {;;;
  /** Stratégie de résolution par défaut pour tous les types d'entités */
  defaultStrategy?: ConflictResolutionStrategy;
  /** Stratégies de résolution par type d'entité */
  entityStrategies?: Record<string, ConflictResolutionStrategy>;
  /** Callback appelé après la résolution d'un conflit */
  onConflictResolved?: (conflictId: string, strategy: ConflictResolutionStrategy) => void;
  /** Callback appelé lors de la détection d'un conflit */
  onConflictDetected?: (conflict: Conflict, details: ConflictDetails) => void;
}

/**
 * Interface pour les résultats du hook;
 */
export interface UseConflictResolverResult {;;;
  /** Liste des conflits non résolus */
  conflicts: Conflict[];
  /** Nombre total de conflits non résolus */
  conflictsCount: number;
  /** Résoudre un conflit avec une stratégie spécifiée */
  resolveConflict: (
    conflictId: string, 
    strategy: ConflictResolutionStrategy, 
    manualData?: any;
  ) => Promise<void>;
  /** Résoudre tous les conflits avec la stratégie par défaut */
  resolveAllConflicts: () => Promise<void>;
  /** Obtenir les détails d'un conflit */
  getConflictDetails: (conflictId: string) => ConflictDetails | null;
  /** Obtenir la stratégie recommandée pour un conflit */
  getRecommendedStrategy: (conflictId: string) => ConflictResolutionStrategy;
  /** Définir la stratégie par défaut pour un type d'entité */
  setDefaultStrategyForEntityType: (
    entityType: string, 
    strategy: ConflictResolutionStrategy;
  ) => void;
}

/**
 * Hook personnalisé pour gérer les conflits de synchronisation;
 * 
 * @param options Options de configuration du hook;
 * @returns Interface pour interagir avec le système de résolution des conflits;
 * 
 * @example;
 * ```tsx;
 * const { 
 *   conflicts, 
 *   resolveConflict, 
 *   getConflictDetails;
 * } = useConflictResolver({
 *   defaultStrategy: ConflictResolutionStrategy.MERGE_FIELDS,
 *   entityStrategies: {
 *     tasks: ConflictResolutionStrategy.LAST_WRITE_WINS,
 *     users: ConflictResolutionStrategy.KEEP_LOCAL;
 *   },
 *   onConflictResolved: (conflictId, strategy) => {
 *     console.log(`Conflit ${conflictId} résolu avec stratégie ${strategy}`);
 *   }
 * });
 * 
 * return (;
 *   <View>
 *     <Text>Conflits à résoudre: {conflicts.length}</Text>
 *     {conflicts.map(conflict => {
 *       const details = getConflictDetails(conflict.id);
 *       return (;
 *         <ConflictItem;
 *           key={conflict.id;,}
 *           conflict = {conflict;,}
 *           details = {details;,}
 *           onResolve={(strategy) => resolveConflict(conflict.id, strategy)}
 *         />
 *       );
 *     })}
 *   </View>
 * );
 * ```
 */
export function useConflictResolver(;;;
  options: UseConflictResolverOptions = {,}
): UseConflictResolverResult {
  const {
    defaultStrategy = ConflictResolutionStrategy.MERGE_FIELDS,
    entityStrategies = {,},
    onConflictResolved,
    onConflictDetected;
  } = options;

  const dispatch = useDispatch();
  
  // États locaux;
  const [conflicts, setConflicts] = useState<Conflict[]>([]);
  const [conflictDetails, setConflictDetails] = useState<Record<string, ConflictDetails>>({});
  
  /**
   * Mettre à jour la liste des conflits;
   */
  const refreshConflicts = useCallback(() => {
    const unresolvedConflicts = conflictResolver.getUnresolvedConflicts();
    setConflicts(unresolvedConflicts);
    
    // Mettre à jour les détails pour les nouveaux conflits;
    const newDetails: Record<string, ConflictDetails> = {};
    
    unresolvedConflicts.forEach(conflict => {
      if(!conflictDetails[conflict.id]) { { { { {}}}}
        const details = conflictResolver.getConflictDetails(conflict.id);
        if(details) { { { { {,}}}}
          const { entityType, entityId, localVersion, remoteVersion, modifiedFields } = details;
          newDetails[conflict.id] = {
            entityType,
            entityId,
            localVersion,
            remoteVersion,
            modifiedFields,
            recommendedStrategy: conflictResolver.getRecommendedStrategy(entityType, modifiedFields)
          };
        }
      } else {
        newDetails[conflict.id] = conflictDetails[conflict.id];
      }
    });
    
    setConflictDetails(newDetails);
  }, [conflictDetails]);
  
  /**
   * Résoudre un conflit avec une stratégie spécifiée;
   */
  const resolveConflict = useCallback(async (;
    conflictId: string,
    strategy: ConflictResolutionStrategy,
    manualData?: any;
  ): Promise<void> => {
    try {
      await conflictResolver.resolveConflict(conflictId, strategy, manualData);
      
      if(onConflictResolved) { { { { {}}}}
        onConflictResolved(conflictId, strategy);
      }
      
      // Rafraîchir la liste des conflits;
      refreshConflicts();
    } catch(error) {
      console.error('Erreur lors de la résolution du conflit:', error);
      throw error;
    }
  }, [refreshConflicts, onConflictResolved]);
  
  /**
   * Résoudre tous les conflits avec la stratégie par défaut;
   */
  const resolveAllConflicts = useCallback(async (): Promise<void> => {
    try {
      const resolutionPromises = conflicts.map(conflict => {
        const details = conflictDetails[conflict.id];
        const strategy = details?.recommendedStrategy ||;
                        entityStrategies[details?.entityType] || 
                        defaultStrategy;
                        
        return resolveConflict(conflict.id, strategy);
      });
      
      await Promise.all(resolutionPromises);
    } catch(error) {
      console.error('Erreur lors de la résolution de tous les conflits:', error);
      throw error;
    }
  }, [conflicts, conflictDetails, entityStrategies, defaultStrategy, resolveConflict]);
  
  /**
   * Obtenir les détails d'un conflit;
   */
  const getConflictDetails = useCallback((conflictId: string): ConflictDetails | null => {
    return conflictDetails[conflictId] || null;,
  }, [conflictDetails]);
  
  /**
   * Obtenir la stratégie recommandée pour un conflit;
   */
  const getRecommendedStrategy = useCallback((conflictId: string): ConflictResolutionStrategy => {
    const details = conflictDetails[conflictId];
    
    if(!details) { { { { {,}}}}
      return defaultStrategy;
    }
    
    return details.recommendedStrategy || ;
          entityStrategies[details.entityType] || 
          defaultStrategy;
  }, [conflictDetails, entityStrategies, defaultStrategy]);
  
  /**
   * Définir la stratégie par défaut pour un type d'entité
   */
  const setDefaultStrategyForEntityType = useCallback((;
    entityType: string,
    strategy: ConflictResolutionStrategy;
  ): void => {
    conflictResolver.setDefaultStrategyForEntityType(entityType, strategy);
  }, []);
  
  // Initialiser les stratégies de résolution au montage;
  useEffect(() => {
    // Configurer la stratégie par défaut;
    conflictResolver.setDefaultStrategy(defaultStrategy);
    
    // Configurer les stratégies par type d'entité
    Object.entries(entityStrategies).forEach(([entityType, strategy]) => {
      conflictResolver.setDefaultStrategyForEntityType(entityType, strategy);
    });
  }, [defaultStrategy, entityStrategies]);
  
  // Initialiser les écouteurs d'événements;
  useEffect(() => {
    // Rafraîchir la liste des conflits au montage;
    refreshConflicts();
    
    // Configurer les écouteurs d'événements;
    const handleConflictDetected = (conflict: Conflict) => {
      refreshConflicts();
      
      if(onConflictDetected && conflictDetails[conflict.id]) { { { { {,}}}}
        onConflictDetected(conflict, conflictDetails[conflict.id]);
      }
    };
    
    const handleConflictResolved = () => {
      refreshConflicts();,
    };
    
    // Enregistrer les écouteurs;
    multiDeviceSync.on(MultiDeviceSyncEventType.CONFLICT_DETECTED, handleConflictDetected);
    multiDeviceSync.on(MultiDeviceSyncEventType.CONFLICT_RESOLVED, handleConflictResolved);
    
    // Nettoyage lors du démontage;
    return () => {
      // Supprimer les écouteurs;
      multiDeviceSync.off(MultiDeviceSyncEventType.CONFLICT_DETECTED, handleConflictDetected);
      multiDeviceSync.off(MultiDeviceSyncEventType.CONFLICT_RESOLVED, handleConflictResolved);
    };
  }, [refreshConflicts, conflictDetails, onConflictDetected]);
  
  return {
    conflicts,
    conflictsCount: conflicts.length,
    resolveConflict,
    resolveAllConflicts,
    getConflictDetails,
    getRecommendedStrategy,
    setDefaultStrategyForEntityType;
  };
}

export default useConflictResolver;;