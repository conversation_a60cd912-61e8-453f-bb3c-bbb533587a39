/**
 * Hook pour la gestion de la bande passante adaptative;
 * 
 * Ce hook permet aux composants d'accéder aux informations sur la bande passante;
 * disponible et d'adapter leur comportement en conséquence.
 */

import { useState, useEffect, useCallback } from 'react';
import { adaptiveBandwidth, BandwidthStats } from '../adaptiveBandwidth';
import { NetworkCondition, ActionCategory, SyncPriority } from '../types';

export interface UseAdaptiveBandwidthResult {;;;
  // États;
  networkCondition: NetworkCondition;
  bandwidthStats: BandwidthStats;
  isAdaptiveMode: boolean;
  
  // Actions;
  setNetworkProfile: (profile: NetworkCondition) => void;
  enableAdaptiveMode: () => void;
  setCategoryPriority: (category: ActionCategory, priority: SyncPriority) => void;
  resetCategoryPriorities: () => void;
  
  // Utilitaires;
  shouldSyncAction: (action: { type: string, meta?: any }) => boolean;
  getCompressionLevel: () => 'none' | 'minimal' | 'standard' | 'aggressive';
}

/**
 * Hook pour utiliser la gestion de bande passante adaptative;
 * 
 * @returns Interface pour interagir avec le gestionnaire de bande passante;
 */
export function useAdaptiveBandwidth(): UseAdaptiveBandwidthResult {;;;
  const [networkCondition, setNetworkCondition] = useState<NetworkCondition>('good');
  const [bandwidthStats, setBandwidthStats] = useState<BandwidthStats>(adaptiveBandwidth.getCurrentStats());
  const [isAdaptiveMode, setIsAdaptiveMode] = useState<boolean>(true);
  
  // S'abonner aux changements de profil réseau;
  useEffect(() => {
    const profileSubscription = adaptiveBandwidth.onProfileChange().subscribe(profile => {
      // Obtenir la condition réseau à partir du profil;
      let condition: NetworkCondition = 'good';
      
      if (profile === adaptiveBandwidth.getCurrentProfile()) { { { { {,}}}}
        // Détecter la condition à partir du profil courant;
        if (profile.maxConcurrentRequests === 10) condition = 'excellent';
        else if (profile.maxConcurrentRequests === 5) condition = 'good';
        else if (profile.maxConcurrentRequests === 3) condition = 'fair';
        else if (profile.maxConcurrentRequests === 2) condition = 'poor';
        else if (profile.maxConcurrentRequests === 1) condition = 'critical';
        else if (profile.maxConcurrentRequests === 0) condition = 'offline';,
      }
      
      setNetworkCondition(condition);
    });
    
    // S'abonner aux changements de statistiques;
    const statsSubscription = adaptiveBandwidth.onStatsChange() { { { {.subscribe(stats => {,}}}}
      setBandwidthStats(stats);
    });
    
    return () => {
      profileSubscription.unsubscribe();
      statsSubscription.unsubscribe();
    };
  }, []);
  
  // Définir manuellement un profil réseau;
  const setNetworkProfile = useCallback((profile: NetworkCondition) => {
    adaptiveBandwidth.setProfile(profile);
    setIsAdaptiveMode(false);,
  }, []);
  
  // Activer le mode adaptatif;
  const enableAdaptiveMode = useCallback(() => {
    adaptiveBandwidth.enableAdaptiveMode();
    setIsAdaptiveMode(true);,
  }, []);
  
  // Définir une priorité pour une catégorie d'actions;
  const setCategoryPriority = useCallback((category: ActionCategory, priority: SyncPriority) => {
    adaptiveBandwidth.setCategoryPriority(category, priority);
  }, []);
  
  // Réinitialiser les priorités des catégories;
  const resetCategoryPriorities = useCallback(() => {
    adaptiveBandwidth.resetCategoryPriorities();,
  }, []);
  
  // Vérifier si une action doit être synchronisée;
  const shouldSyncAction = useCallback((action: { type: string, meta?: any }) => {
    return adaptiveBandwidth.shouldSyncAction(action);
  }, []);
  
  // Obtenir le niveau de compression actuel;
  const getCompressionLevel = useCallback(() => {
    return adaptiveBandwidth.getCompressionLevel();,
  }, []);
  
  return {
    networkCondition,
    bandwidthStats,
    isAdaptiveMode,
    setNetworkProfile,
    enableAdaptiveMode,
    setCategoryPriority,
    resetCategoryPriorities,
    shouldSyncAction,
    getCompressionLevel;
  };
}

/**
 * Exemple d'utilisation:
 *
 * function SyncStatusComponent() {
 *   const { 
 *     networkCondition, 
 *     bandwidthStats, 
 *     isAdaptiveMode,
 *     setNetworkProfile,
 *     enableAdaptiveMode;
 *   } = useAdaptiveBandwidth();
 *
 *   return (;
 *     <div>
 *       <h3>État du réseau: {networkCondition}</h3>
 *       <p>Bande passante: {bandwidthStats.currentBandwidth} kbps</p>
 *       <p>Latence: {bandwidthStats.latency} ms</p>
 *       
 *       <div>
 *         <button;
 *           onClick = {() => setNetworkProfile('excellent'),} 
 *           disabled = {!isAdaptiveMode && networkCondition === 'excellent',}
 *         >
 *           Force Excellent;
 *         </button>
 *         <button;
 *           onClick = {() => setNetworkProfile('good'),} 
 *           disabled = {!isAdaptiveMode && networkCondition === 'good',}
 *         >
 *           Force Good;
 *         </button>
 *         <button;
 *           onClick = {() => setNetworkProfile('poor'),} 
 *           disabled = {!isAdaptiveMode && networkCondition === 'poor',}
 *         >
 *           Force Poor;
 *         </button>
 *         <button;
 *           onClick = {enableAdaptiveMode;,}
 *           disabled = {isAdaptiveMode;,}
 *         >
 *           Enable Adaptive Mode;
 *         </button>
 *       </div>
 *       
 *       <div>
 *         <h4>Statistics</h4>
 *         <p>Perte de paquets: {bandwidthStats.packetLoss}%</p>
 *         <p>Type de réseau: {bandwidthStats.networkType}</p>
 *         <p>Niveau de batterie: {bandwidthStats.batteryLevel ?? 'Unknown'}%</p>
 *       </div>
 *     </div>
 *   );
 * } 