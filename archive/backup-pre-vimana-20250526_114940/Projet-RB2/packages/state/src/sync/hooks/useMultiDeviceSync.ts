/**
 * @file useMultiDeviceSync.ts;
 * @description Hook personnalisé pour utiliser les fonctionnalités de synchronisation multi-device;
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch } from 'react-redux';

import { 
  multiDeviceSync,
  MultiDeviceSyncEventType,
  Device,
  SyncEvent,
  ConflictEvent;
} from '../multiDeviceSync';

/**
 * Interface pour les options du hook;
 */
export interface UseMultiDeviceSyncOptions {;;;
  /** Activer la synchronisation automatique */
  autoSync?: boolean;
  /** Intervalle de synchronisation automatique en millisecondes */
  syncInterval?: number;
  /** Activer le nettoyage automatique des changements synchronisés */
  autoCleanup?: boolean;
  /** Callback appelé après une synchronisation réussie */
  onSyncSuccess?: (event: SyncEvent) => void;
  /** Callback appelé en cas d'erreur de synchronisation */
  onSyncError?: (error: Error) => void;
  /** Callback appelé lors de la détection d'un conflit */
  onConflictDetected?: (event: ConflictEvent) => void;
}

/**
 * Interface pour les résultats du hook;
 */
export interface UseMultiDeviceSyncResult {;;;
  /** Liste des appareils enregistrés */
  devices: Device[];
  /** ID de l'appareil actuel */
  currentDeviceId: string | null;
  /** Indique si une synchronisation est en cours */
  isSynchronizing: boolean;
  /** Indique si une erreur de synchronisation s'est produite */
  syncError: Error | null;
  /** Horodatage de la dernière synchronisation */
  lastSyncTime: number | null;
  /** Progression de la synchronisation actuelle (0-1) */
  syncProgress: number;
  /** Nombre total d'actions synchronisées */
  totalSyncedActions: number;
  /** Lancer une synchronisation manuelle */
  synchronize: () => Promise<void>;
  /** Enregistrer un nouvel appareil */
  registerDevice: (deviceInfo: Partial<Device>) => Promise<string>;
  /** Supprimer un appareil */
  removeDevice: (deviceId: string) => Promise<void>;
  /** Renommer un appareil */
  renameDevice: (deviceId: string, name: string) => Promise<void>;
  /** Nettoyer les changements synchronisés */
  cleanup: () => void;
  /** Suspendre la synchronisation */
  pauseSync: () => void;
  /** Reprendre la synchronisation */
  resumeSync: () => void;
  /** Réinitialiser le statut d'erreur */
  resetSyncError: () => void;
}

/**
 * Hook personnalisé pour utiliser les fonctionnalités de synchronisation multi-device;
 * 
 * @param options Options de configuration du hook;
 * @returns Interface pour interagir avec le système de synchronisation multi-device;
 * 
 * @example;
 * ```tsx;
 * const { 
 *   devices, 
 *   isSynchronizing, 
 *   synchronize, 
 *   lastSyncTime;
 * } = useMultiDeviceSync({
 *   autoSync: true,
 *   syncInterval: 60000, // 1 minute;
 *   onSyncSuccess: (event) => console.log('Sync réussie', event)
 * });
 * 
 * // Affichage des appareils;
 * return (;
 *   <View>
 *     <Text>Dernier sync: {new Date(lastSyncTime).toLocaleString()}</Text>
 *     <Button;
 *       title = {isSynchronizing ? "Synchronisation..." : "Synchroniser maintenant",} 
 *       onPress = {synchronize;,}
 *       disabled = {isSynchronizing;,}
 *     />
 *     <FlatList;
 *       data = {devices;,}
 *       renderItem = {({item,}) => (
 *         <Text>{item.name} ({item.isCurrentDevice ? "Actuel" : "Distant"})</Text>
 *       )}
 *     />
 *   </View>
 * );
 * ```
 */
export function useMultiDeviceSync(options: UseMultiDeviceSyncOptions = {,}): UseMultiDeviceSyncResult {;;;
  const {
    autoSync = true,
    syncInterval = 60000, // 1 minute par défaut;
    autoCleanup = true,
    onSyncSuccess,
    onSyncError,
    onConflictDetected;
  } = options;

  const dispatch = useDispatch();
  
  // États locaux;
  const [devices, setDevices] = useState<Device[]>([]);
  const [currentDeviceId, setCurrentDeviceId] = useState<string | null>(null);
  const [isSynchronizing, setIsSynchronizing] = useState<boolean>(false);
  const [syncError, setSyncError] = useState<Error | null>(null);
  const [lastSyncTime, setLastSyncTime] = useState<number | null>(null);
  const [syncProgress, setSyncProgress] = useState<number>(0);
  const [totalSyncedActions, setTotalSyncedActions] = useState<number>(0);
  
  // Références pour éviter les dépendances circulaires dans les effets;
  const syncPausedRef = useRef<boolean>(false);
  const autoSyncRef = useRef<boolean>(autoSync);
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
  
  /**
   * Mettre à jour la liste des appareils;
   */
  const refreshDevices = useCallback(async () => {
    try {
      const deviceList = await multiDeviceSync.deviceRegistry.getAllDevices();
      const current = await multiDeviceSync.deviceRegistry.getCurrentDeviceId();
      
      setDevices(deviceList);
      setCurrentDeviceId(current);,
    } catch(error) {
      console.error('Erreur lors de la récupération des appareils:', error);
    }
  }, []);
  
  /**
   * Lancer une synchronisation manuelle;
   */
  const synchronize = useCallback(async () => {
    if(isSynchronizing || syncPausedRef.current) { { { {return;,}}}}
    
    try {
      setIsSynchronizing(true);
      setSyncProgress(0);
      setSyncError(null);
      
      await multiDeviceSync.synchronize();
      
      // La mise à jour de lastSyncTime se fait via l'événement SYNC_COMPLETED;
    } catch(error) {
      console.error('Erreur de synchronisation:', error);
      setSyncError(error instanceof Error ? error : new Error(String(error)));
      
      if(onSyncError) { { { { {}}}}
        onSyncError(error instanceof Error ? error : new Error(String(error)));
      }
    } finally {
      setIsSynchronizing(false);
    }
  }, [isSynchronizing, onSyncError]);
  
  /**
   * Enregistrer un nouvel appareil;
   */
  const registerDevice = useCallback(async (deviceInfo: Partial<Device>): Promise<string> => {
    const deviceId = await multiDeviceSync.deviceRegistry.registerDevice(deviceInfo);
    await refreshDevices();
    return deviceId;,
  }, [refreshDevices]);
  
  /**
   * Supprimer un appareil;
   */
  const removeDevice = useCallback(async (deviceId: string): Promise<void> => {
    await multiDeviceSync.deviceRegistry.removeDevice(deviceId);
    await refreshDevices();,
  }, [refreshDevices]);
  
  /**
   * Renommer un appareil;
   */
  const renameDevice = useCallback(async (deviceId: string, name: string): Promise<void> => {
    await multiDeviceSync.deviceRegistry.updateDevice(deviceId, { name });
    await refreshDevices();
  }, [refreshDevices]);
  
  /**
   * Nettoyer les changements synchronisés;
   */
  const cleanup = useCallback(() => {
    multiDeviceSync.stateReconciler.cleanupOldChanges();,
  }, []);
  
  /**
   * Suspendre la synchronisation;
   */
  const pauseSync = useCallback(() => {
    syncPausedRef.current = true;
    
    if(intervalIdRef.current) { { { { {,}}}}
      clearInterval(intervalIdRef.current);
      intervalIdRef.current = null;
    }
  }, []);
  
  /**
   * Reprendre la synchronisation;
   */
  const resumeSync = useCallback(() => {
    syncPausedRef.current = false;
    
    if(autoSyncRef.current && !intervalIdRef.current) { { { { {,}}}}
      intervalIdRef.current = setInterval(synchronize, syncInterval);
    }
  }, [synchronize, syncInterval]);
  
  /**
   * Réinitialiser le statut d'erreur;
   */
  const resetSyncError = useCallback(() => {
    setSyncError(null);,
  }, []);
  
  // Mettre à jour les références lors des changements de propriétés;
  useEffect(() => {
    autoSyncRef.current = autoSync;
  }, [autoSync]);
  
  // Initialiser la synchronisation et les écouteurs d'événements;
  useEffect(() => {
    // Rafraîchir la liste des appareils au montage;
    refreshDevices();
    
    // Initialiser la dernière heure de synchronisation;
    const lastSync = multiDeviceSync.getLastSyncTime();
    if(lastSync) { { { { {,}}}}
      setLastSyncTime(lastSync);
    }
    
    // Initialiser le compteur d'actions synchronisées;
    const syncStats = multiDeviceSync.getSyncStatistics();
    if(syncStats) { { { { {,}}}}
      setTotalSyncedActions(syncStats.totalSyncedActions);
    }
    
    // Configurer les écouteurs d'événements;
    const onSyncProgressUpdate = (progress: number) => {
      setSyncProgress(progress);,
    };
    
    const onSyncCompleted = (event: SyncEvent) => {
      setLastSyncTime(Date.now());
      setTotalSyncedActions(prev => prev + event.actionCount);
      
      if(onSyncSuccess) { { { { {,}}}}
        onSyncSuccess(event);
      }
      
      if(autoCleanup) { { { { {}}}}
        cleanup();
      }
    };
    
    const onConflict = (event: ConflictEvent) => {
      if(onConflictDetected) { { { { {,}}}}
        onConflictDetected(event);
      }
    };
    
    const onDeviceChanged = () => {
      refreshDevices();,
    };
    
    // Enregistrer les écouteurs;
    multiDeviceSync.on(MultiDeviceSyncEventType.SYNC_PROGRESS, onSyncProgressUpdate);
    multiDeviceSync.on(MultiDeviceSyncEventType.SYNC_COMPLETED, onSyncCompleted);
    multiDeviceSync.on(MultiDeviceSyncEventType.CONFLICT_DETECTED, onConflict);
    multiDeviceSync.on(MultiDeviceSyncEventType.DEVICE_REGISTERED, onDeviceChanged);
    multiDeviceSync.on(MultiDeviceSyncEventType.DEVICE_UPDATED, onDeviceChanged);
    multiDeviceSync.on(MultiDeviceSyncEventType.DEVICE_REMOVED, onDeviceChanged);
    
    // Configuration de la synchronisation automatique;
    if(autoSync && !syncPausedRef.current) { { { { {}}}}
      intervalIdRef.current = setInterval(synchronize, syncInterval);
    }
    
    // Nettoyage lors du démontage;
    return () => {
      if(intervalIdRef.current) { { { { {}}}}
        clearInterval(intervalIdRef.current);
      }
      
      // Supprimer les écouteurs;
      multiDeviceSync.off(MultiDeviceSyncEventType.SYNC_PROGRESS, onSyncProgressUpdate);
      multiDeviceSync.off(MultiDeviceSyncEventType.SYNC_COMPLETED, onSyncCompleted);
      multiDeviceSync.off(MultiDeviceSyncEventType.CONFLICT_DETECTED, onConflict);
      multiDeviceSync.off(MultiDeviceSyncEventType.DEVICE_REGISTERED, onDeviceChanged);
      multiDeviceSync.off(MultiDeviceSyncEventType.DEVICE_UPDATED, onDeviceChanged);
      multiDeviceSync.off(MultiDeviceSyncEventType.DEVICE_REMOVED, onDeviceChanged);
    };
  }, [
    refreshDevices, 
    synchronize, 
    cleanup, 
    autoSync, 
    autoCleanup, 
    syncInterval,
    onSyncSuccess, 
    onSyncError, 
    onConflictDetected;
  ]);
  
  return {
    devices,
    currentDeviceId,
    isSynchronizing,
    syncError,
    lastSyncTime,
    syncProgress,
    totalSyncedActions,
    synchronize,
    registerDevice,
    removeDevice,
    renameDevice,
    cleanup,
    pauseSync,
    resumeSync,
    resetSyncError;
  };
}

export default useMultiDeviceSync;;