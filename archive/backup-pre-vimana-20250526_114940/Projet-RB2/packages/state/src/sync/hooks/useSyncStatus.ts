/**
 * @file useSyncStatus.ts;
 * @description Hook personnalisé pour suivre le statut de la synchronisation;
 */

import { useState, useEffect, useCallback } from 'react';

import { 
  multiDeviceSync, 
  networkMonitor,
  NetworkStatus,
  SyncStatus,
  MultiDeviceSyncEventType;
} from '../multiDeviceSync';

/**
 * Interface pour les résultats du hook;
 */
export interface UseSyncStatusResult {;;;
  /** État de la connexion réseau */
  isOnline: boolean;
  /** Type de connexion réseau actuel */
  networkType: string | null;
  /** État de la synchronisation */
  syncStatus: SyncStatus;
  /** Progression de la synchronisation (0-1) */
  syncProgress: number;
  /** Nombre d'actions en attente de synchronisation */
  pendingActions: number;
  /** Horodatage de la dernière synchronisation */
  lastSyncTime: number | null;
  /** Erreur de synchronisation le cas échéant */
  syncError: Error | null;
  /** Qualité de la connexion (0-1, 1 étant la meilleure) */
  connectionQuality: number;
  /** Vérifier manuellement l'état du réseau */
  checkNetworkStatus: () => Promise<boolean>;
  /** Réinitialiser l'erreur de synchronisation */
  resetSyncError: () => void;
}

/**
 * Interface pour les options du hook;
 */
export interface UseSyncStatusOptions {;;;
  /** Callback appelé lorsque l'état du réseau change */
  onNetworkStatusChange?: (isOnline: boolean) => void;
  /** Callback appelé lorsque l'état de la synchronisation change */
  onSyncStatusChange?: (status: SyncStatus) => void;
  /** Callback appelé en cas d'erreur de synchronisation */
  onSyncError?: (error: Error) => void;
}

/**
 * Hook personnalisé pour suivre le statut de la synchronisation;
 * 
 * @param options Options de configuration du hook;
 * @returns Interface pour interagir avec le statut de synchronisation;
 * 
 * @example;
 * ```tsx;
 * const { 
 *   isOnline, 
 *   syncStatus, 
 *   pendingActions;
 * } = useSyncStatus({
 *   onNetworkStatusChange: (isOnline) => {
 *     console.log(`Connexion réseau: ${isOnline ? 'En ligne' : 'Hors ligne'}`);
 *   }
 * });
 * 
 * return (;
 *   <View>
 *     <Text>État réseau: {isOnline ? 'En ligne' : 'Hors ligne'}</Text>
 *     <Text>État synchronisation: {syncStatus}</Text>
 *     <Text>Actions en attente: {pendingActions}</Text>
 *   </View>
 * );
 * ```
 */
export function useSyncStatus(options: UseSyncStatusOptions = {,}): UseSyncStatusResult {;;;
  const {
    onNetworkStatusChange,
    onSyncStatusChange,
    onSyncError;
  } = options;
  
  // États locaux;
  const [isOnline, setIsOnline] = useState<boolean>(networkMonitor.isOnline());
  const [networkType, setNetworkType] = useState<string | null>(networkMonitor.getNetworkType());
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(multiDeviceSync.getSyncStatus());
  const [syncProgress, setSyncProgress] = useState<number>(0);
  const [pendingActions, setPendingActions] = useState<number>(multiDeviceSync.getPendingActionsCount());
  const [lastSyncTime, setLastSyncTime] = useState<number | null>(multiDeviceSync.getLastSyncTime());
  const [syncError, setSyncError] = useState<Error | null>(null);
  const [connectionQuality, setConnectionQuality] = useState<number>(networkMonitor.getConnectionQuality());
  
  /**
   * Vérifier manuellement l'état du réseau;
   */
  const checkNetworkStatus = useCallback(async (): Promise<boolean> => {
    const status = await networkMonitor.checkConnection();
    setIsOnline(status);
    setNetworkType(networkMonitor.getNetworkType());
    setConnectionQuality(networkMonitor.getConnectionQuality());
    return status;,
  }, []);
  
  /**
   * Réinitialiser l'erreur de synchronisation;
   */
  const resetSyncError = useCallback((): void => {
    setSyncError(null);,
  }, []);
  
  // Initialiser les écouteurs d'événements;
  useEffect(() => {
    // Configurer les écouteurs d'événements pour le réseau;
    const handleNetworkStatusChange = (status: NetworkStatus) => {
      setIsOnline(status.isOnline);
      setNetworkType(status.type);
      setConnectionQuality(status.quality);
      
      if(onNetworkStatusChange) { { { { {,}}}}
        onNetworkStatusChange(status.isOnline);
      }
    };
    
    // Configurer les écouteurs d'événements pour la synchronisation;
    const handleSyncStatusChange = (status: SyncStatus) => {
      setSyncStatus(status);
      
      if(onSyncStatusChange) { { { { {,}}}}
        onSyncStatusChange(status);
      }
    };
    
    const handleSyncProgress = (progress: number) => {
      setSyncProgress(progress);,
    };
    
    const handlePendingActionsChange = (count: number) => {
      setPendingActions(count);,
    };
    
    const handleSyncCompleted = () => {
      setLastSyncTime(Date.now());
      setSyncStatus(SyncStatus.IDLE);,
    };
    
    const handleSyncError = (error: Error) => {
      setSyncError(error);
      setSyncStatus(SyncStatus.ERROR);
      
      if(onSyncError) { { { { {,}}}}
        onSyncError(error);
      }
    };
    
    // Enregistrer les écouteurs pour le réseau;
    networkMonitor.on('statusChange', handleNetworkStatusChange);
    
    // Enregistrer les écouteurs pour la synchronisation;
    multiDeviceSync.on(MultiDeviceSyncEventType.SYNC_STATUS_CHANGED, handleSyncStatusChange);
    multiDeviceSync.on(MultiDeviceSyncEventType.SYNC_PROGRESS, handleSyncProgress);
    multiDeviceSync.on(MultiDeviceSyncEventType.PENDING_ACTIONS_CHANGED, handlePendingActionsChange);
    multiDeviceSync.on(MultiDeviceSyncEventType.SYNC_COMPLETED, handleSyncCompleted);
    multiDeviceSync.on(MultiDeviceSyncEventType.SYNC_ERROR, handleSyncError);
    
    // Initialiser les états;
    checkNetworkStatus();
    setPendingActions(multiDeviceSync.getPendingActionsCount());
    setLastSyncTime(multiDeviceSync.getLastSyncTime());
    setSyncStatus(multiDeviceSync.getSyncStatus());
    
    // Nettoyage lors du démontage;
    return () => {
      // Supprimer les écouteurs pour le réseau;
      networkMonitor.off('statusChange', handleNetworkStatusChange);
      
      // Supprimer les écouteurs pour la synchronisation;
      multiDeviceSync.off(MultiDeviceSyncEventType.SYNC_STATUS_CHANGED, handleSyncStatusChange);
      multiDeviceSync.off(MultiDeviceSyncEventType.SYNC_PROGRESS, handleSyncProgress);
      multiDeviceSync.off(MultiDeviceSyncEventType.PENDING_ACTIONS_CHANGED, handlePendingActionsChange);
      multiDeviceSync.off(MultiDeviceSyncEventType.SYNC_COMPLETED, handleSyncCompleted);
      multiDeviceSync.off(MultiDeviceSyncEventType.SYNC_ERROR, handleSyncError);
    };
  }, [checkNetworkStatus, onNetworkStatusChange, onSyncStatusChange, onSyncError]);
  
  return {
    isOnline,
    networkType,
    syncStatus,
    syncProgress,
    pendingActions,
    lastSyncTime,
    syncError,
    connectionQuality,
    checkNetworkStatus,
    resetSyncError;
  };
}

export default useSyncStatus;;