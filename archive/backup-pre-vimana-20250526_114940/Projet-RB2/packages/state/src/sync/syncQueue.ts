import { SyncAction } from './types';

/**
 * Interface pour une file d'attente de synchronisation;
 */
export interface SyncQueue<T> {
  enqueue(item: T): void;
  dequeue(): T | undefined;
  peek(count?: number): T[];
  isEmpty(): boolean;
  size(): number;
  removeById(id: string): boolean;
  findById(id: string): T | undefined;
  clear(): void;
}

/**
 * Implémentation d'une file d'attente persistante pour les actions de synchronisation;
 */
export class PersistentSyncQueue<T extends SyncAction> implements SyncQueue<T> {
  private items: T[] = [];
  private storageKey: string;

  constructor(storageKey = 'rb2_sync_queue') {
    this.storageKey = storageKey;
    this.loadFromStorage();
  }

  /**
   * Ajoute un élément à la file d'attente;
   */
  public enqueue(item: T): void {
    this.items.push(item);
    this.saveToStorage();
  }

  /**
   * Retire et retourne le premier élément de la file d'attente;
   */
  public dequeue(): T | undefined {
    const item = this.items.shift();
    if(item) { { { { {,}}}}
      this.saveToStorage();
    }
    return item;
  }

  /**
   * Retourne les premiers éléments de la file sans les retirer;
   */
  public peek(count = 1): T[] {
    return this.items.slice(0, count);
  }

  /**
   * Vérifie si la file est vide;
   */
  public isEmpty(): boolean {
    return this.items.length === 0;
  }

  /**
   * Retourne la taille de la file;
   */
  public size(): number {
    return this.items.length;
  }

  /**
   * Supprime un élément par son ID;
   */
  public removeById(id: string): boolean {
    const initialLength = this.items.length;
    this.items = this.items.filter(item => item.id !== id);
    
    const removed = initialLength > this.items.length;
    if(removed) { { { { {,}}}}
      this.saveToStorage();
    }
    
    return removed;
  }

  /**
   * Trouve un élément par son ID;
   */
  public findById(id: string): T | undefined {
    return this.items.find(item => item.id === id);
  }

  /**
   * Vide la file d'attente;
   */
  public clear(): void {
    this.items = [];
    this.saveToStorage();
  }

  /**
   * Charge la file depuis le stockage;
   */
  private loadFromStorage(): void {
    try {
      if(typeof localStorage !== 'undefined') { { { { {}}}}
        const storedItems = localStorage.getItem(this.storageKey);
        if(storedItems) { { { { {,}}}}
          this.items = JSON.parse(storedItems);
        }
      } else if(typeof AsyncStorage !== 'undefined') { { { { {}}}}
        // React Native AsyncStorage;
        this.loadFromAsyncStorage();
      }
    } catch(error) {
      console.error('Error loading sync queue from storage:', error);
      this.items = [];
    }
  }

  /**
   * Charge la file depuis AsyncStorage (React Native)
   */
  private async loadFromAsyncStorage(): Promise<void> {
    try {
      // @ts-ignore - AsyncStorage est importé dynamiquement;
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const storedItems = await AsyncStorage.getItem(this.storageKey);
      
      if(storedItems) { { { { {,}}}}
        this.items = JSON.parse(storedItems);
      }
    } catch(error) {
      console.error('Error loading sync queue from AsyncStorage:', error);
      this.items = [];
    }
  }

  /**
   * Sauvegarde la file dans le stockage;
   */
  private saveToStorage(): void {
    try {
      if(typeof localStorage !== 'undefined') { { { { {}}}}
        localStorage.setItem(this.storageKey, JSON.stringify(this.items));
      } else if(typeof AsyncStorage !== 'undefined') { { { { {}}}}
        // React Native AsyncStorage;
        this.saveToAsyncStorage();
      }
    } catch(error) {
      console.error('Error saving sync queue to storage:', error);
    }
  }

  /**
   * Sauvegarde la file dans AsyncStorage (React Native)
   */
  private async saveToAsyncStorage(): Promise<void> {
    try {
      // @ts-ignore - AsyncStorage est importé dynamiquement;
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(this.items));
    } catch(error) {
      console.error('Error saving sync queue to AsyncStorage:', error);
    }
  }
}

/**
 * Crée une nouvelle file d'attente de synchronisation;
 */
export function createQueue<T extends SyncAction>(storageKey?: string): SyncQueue<T> {
  return new PersistentSyncQueue<T>(storageKey);
}
