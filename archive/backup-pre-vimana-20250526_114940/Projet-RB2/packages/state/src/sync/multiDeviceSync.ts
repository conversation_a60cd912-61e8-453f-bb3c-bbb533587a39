/**
 * Module de synchronisation multi-device avancé
 * 
 * Ce module permet de synchroniser l'état de l'application entre plusieurs appareils;
 * d'un même utilisateur avec une gestion avancée des conflits, une synchronisation;
 * en temps réel et une résilience aux problèmes de connectivité.
 * 
 * @packageDocumentation;
 */

import { AnyAction, Middleware, Store } from 'redux';
import { nanoid } from '@reduxjs/toolkit';
import { RealtimeSync, RealtimeSyncEvent } from './types';
import { logger } from '../utils/logger';
import { DeviceRegistry } from './deviceRegistry';
import { ConflictResolver } from './conflictResolver';
import { StateReconciler } from './stateReconciler';
import { OfflineQueue } from './offlineQueue';

/**
 * Interface pour les options de configuration du système de synchronisation multi-device;
 */
export interface MultiDeviceSyncOptions {;;;
  /** Activer la compression des données */
  enableCompression?: boolean;
  /** Niveau de compression (1-9) */
  compressionLevel?: number;
  /** Activer le chiffrement de bout en bout */
  enableEncryption?: boolean;
  /** Clé de chiffrement personnalisée */
  encryptionKey?: string;
  /** Intervalle de synchronisation périodique (ms) */
  syncInterval?: number;
  /** Nombre maximum de tentatives de synchronisation */
  maxRetries?: number;
  /** Taille maximale des données à synchroniser par lot (en octets) */
  maxBatchSize?: number;
  /** Préférence pour la synchronisation sélective */
  selectiveSyncPreferences?: Record<string, boolean>;
  /** Fonction de log personnalisée */
  logger?: (message: string, data?: any) => void;
}

/**
 * Type d'événement de synchronisation multi-device;
 */
export enum MultiDeviceSyncEventType {;
  DEVICE_REGISTERED = 'device_registered',
  DEVICE_UNREGISTERED = 'device_unregistered',
  STATE_CHANGED = 'state_changed',
  CONFLICT_DETECTED = 'conflict_detected',
  CONFLICT_RESOLVED = 'conflict_resolved',
  SYNC_STARTED = 'sync_started',
  SYNC_COMPLETED = 'sync_completed',
  SYNC_FAILED = 'sync_failed',
}

/**
 * Interface pour un événement de synchronisation multi-device;
 */
export interface MultiDeviceSyncEvent {;;;
  type: MultiDeviceSyncEventType;
  payload: any;
  deviceId: string;
  timestamp: number;
}

/**
 * Interface pour un écouteur d'événements de synchronisation multi-device;
 */
export interface MultiDeviceSyncEventListener {;;;
  (event: MultiDeviceSyncEvent): void;
}

/**
 * Interface pour un changement d'état à synchroniser;
 */
export interface StateChange {;;;
  id: string;
  sliceName: string;
  changes: any;
  action: AnyAction;
  timestamp: number;
  deviceId: string;
  version: number;
  ancestors: string[];
}

/**
 * Classe principale pour la gestion de la synchronisation multi-device;
 */
export class MultiDeviceSync {;;;
  private static instance: MultiDeviceSync;
  private options: Required<MultiDeviceSyncOptions>;
  private deviceRegistry: DeviceRegistry;
  private conflictResolver: ConflictResolver;
  private stateReconciler: StateReconciler;
  private offlineQueue: OfflineQueue;
  private realtimeSync?: RealtimeSync;
  private store?: Store;
  private syncInterval?: NodeJS.Timeout;
  private eventListeners: Map<MultiDeviceSyncEventType, Set<MultiDeviceSyncEventListener>>;
  private initialized: boolean;
  
  /**
   * Constructeur privé (singleton)
   */
  private constructor() {
    // Options par défaut;
    this.options = {
      enableCompression: true,
      compressionLevel: 6,
      enableEncryption: true,
      encryptionKey: '',
      syncInterval: 60000, // 1 minute;
      maxRetries: 5,
      maxBatchSize: 1024 * 50, // 50 KB;
      selectiveSyncPreferences: {},
      logger: (message: string, data?: any) => {
        logger.info(`[MultiDeviceSync] ${message}`, data, 'multiDeviceSync');
      }
    };
    
    this.deviceRegistry = new DeviceRegistry();
    this.conflictResolver = new ConflictResolver();
    this.stateReconciler = new StateReconciler();
    this.offlineQueue = new OfflineQueue();
    this.eventListeners = new Map();
    this.initialized = false;
    
    // Initialiser tous les types d'événements;
    Object.values(MultiDeviceSyncEventType).forEach(type => {
      this.eventListeners.set(type as MultiDeviceSyncEventType, new Set());
    });
  }
  
  /**
   * Obtient l'instance singleton de MultiDeviceSync;
   */
  public static getInstance(): MultiDeviceSync {
    if(!MultiDeviceSync.instance) { { { { {}}}}
      MultiDeviceSync.instance = new MultiDeviceSync();
    }
    return MultiDeviceSync.instance;
  }
  
  /**
   * Initialise le système de synchronisation multi-device;
   * @param store Store Redux;
   * @param realtimeSync Service de synchronisation en temps réel;
   * @param options Options de configuration;
   */
  public initialize(
    store: Store,
    realtimeSync: RealtimeSync,
    options?: Partial<MultiDeviceSyncOptions>
  ): void {
    this.store = store;
    this.realtimeSync = realtimeSync;
    
    // Fusionner les options personnalisées avec les valeurs par défaut;
    if(options) { { { { {}}}}
      this.options = { ...this.options, ...options };
    }
    
    // Initialiser les composants;
    this.deviceRegistry.initialize(this.options.logger);
    this.conflictResolver.initialize(store, this.options.logger);
    this.stateReconciler.initialize(store, this.options.logger);
    this.offlineQueue.initialize(this.options.logger);
    
    // Configurer les écouteurs d'événements;
    this.configureEventListeners();
    
    // Démarrer la synchronisation périodique;
    this.startPeriodicSync();
    
    this.initialized = true;
    this.options.logger('Système de synchronisation multi-device initialisé');
    
    // Émettre un événement de démarrage;
    this.emitEvent(MultiDeviceSyncEventType.SYNC_STARTED, { message: 'Système initialisé' });
  }
  
  /**
   * Vérifie si le système est initialisé
   * @throws Error si le système n'est pas initialisé
   */
  private checkInitialized(): void {
    if(!this.initialized || !this.store || !this.realtimeSync) { { { { {}}}}
      throw new Error('MultiDeviceSync n\'est pas initialisé. Appelez initialize() d\'abord.');
    }
  }
  
  /**
   * Configure les écouteurs d'événements pour la synchronisation en temps réel;
   */
  private configureEventListeners(): void {
    if (!this.realtimeSync) return;
    
    // Écouter les événements entrants;
    this.realtimeSync.onEvent('action', (event: RealtimeSyncEvent) { { { {=> {}}}}
      this.handleIncomingSync(event);
    });
    
    // Écouter les événements spécifiques à la synchronisation multi-device;
    this.realtimeSync.onEvent('multi_device_sync', (event: RealtimeSyncEvent) => {
      this.handleMultiDeviceEvent(event);
    });
  }
  
  /**
   * Gère les événements de synchronisation entrants;
   * @param event Événement de synchronisation;
   */
  private async handleIncomingSync(event: RealtimeSyncEvent): Promise<void> {
    this.checkInitialized();
    
    try {
      // Vérifier si l'événement concerne une action;
      if(event.type === 'action' && event.payload) { { { { {}}}}
        const action = event.payload;
        const meta = event.meta || {,};
        
        // Ne pas traiter les actions provenant de cet appareil;
        if (meta.deviceId === this.deviceRegistry.getCurrentDeviceId()) { { { { {}}}}
          return;
        }
        
        // Vérifier s'il y a un conflit potentiel;
        const stateChange: StateChange = {
          id: meta.changeId || nanoid(),
          sliceName: this.getSliceNameFromAction(action),
          changes: action.payload,
          action,
          timestamp: meta.timestamp || Date.now(),
          deviceId: meta.deviceId || 'unknown',
          version: meta.version || 1,
          ancestors: meta.ancestors || []
        };
        
        const conflicts = await this.conflictResolver.detectConflicts(stateChange);
        
        if(conflicts.length > 0) { { { { {,}}}}
          // Des conflits détectés, les résoudre;
          this.options.logger('Conflits détectés', { conflicts });
          this.emitEvent(MultiDeviceSyncEventType.CONFLICT_DETECTED, { conflicts });
          
          // Résoudre les conflits;
          const resolvedState = await this.conflictResolver.resolveConflicts(conflicts);
          
          // Appliquer l'état résolu;
          await this.stateReconciler.applyResolvedState(resolvedState);
          
          this.emitEvent(MultiDeviceSyncEventType.CONFLICT_RESOLVED, { resolvedState });
        } else {
          // Pas de conflit, appliquer l'action;
          if(this.store) { { { { {}}}}
            // Marquer l'action comme provenant d'une synchronisation;
            const syncedAction = {
              ...action,
              meta: {
                ...action.meta,
                fromSync: true;
              }
            };
            
            this.store.dispatch(syncedAction);
          }
        }
      }
    } catch(error) {
      this.options.logger('Erreur lors du traitement de l\'événement entrant', { error });
    }
  }
  
  /**
   * Gère les événements spécifiques à la synchronisation multi-device;
   * @param event Événement de synchronisation;
   */
  private async handleMultiDeviceEvent(event: RealtimeSyncEvent): Promise<void> {
    try {
      if(event.payload?.type === 'device_registered') { { { { {}}}}
        // Un nouvel appareil a été enregistré
        const deviceInfo = event.payload.device;
        await this.deviceRegistry.addDevice(deviceInfo);,
      } else if(event.payload?.type === 'device_unregistered') { { { { {}}}}
        // Un appareil a été désenregistré
        const deviceId = event.payload.deviceId;
        await this.deviceRegistry.removeDevice(deviceId);,
      }
    } catch(error) {
      this.options.logger('Erreur lors du traitement de l\'événement multi-device', { error });
    }
  }
  
  /**
   * Démarre la synchronisation périodique;
   */
  private startPeriodicSync(): void {
    if(this.syncInterval) { { { { {}}}}
      clearInterval(this.syncInterval);
    }
    
    this.syncInterval = setInterval(() => {
      this.synchronize().catch(error => {
        this.options.logger('Erreur lors de la synchronisation périodique', { error });
      });
    }, this.options.syncInterval);
  }
  
  /**
   * Arrête la synchronisation périodique;
   */
  private stopPeriodicSync(): void {
    if(this.syncInterval) { { { { {}}}}
      clearInterval(this.syncInterval);
      this.syncInterval = undefined;
    }
  }
  
  /**
   * Effectue une synchronisation complète;
   */
  public async synchronize(): Promise<void> {
    this.checkInitialized();
    
    try {
      this.emitEvent(MultiDeviceSyncEventType.SYNC_STARTED, { timestamp: Date.now() });
      
      // Traiter les éléments en attente dans la file;
      await this.processOfflineQueue();
      
      // Synchroniser les changements locaux non envoyés;
      await this.synchronizeLocalChanges();
      
      this.emitEvent(MultiDeviceSyncEventType.SYNC_COMPLETED, { timestamp: Date.now() });
    } catch(error) {
      this.options.logger('Erreur lors de la synchronisation', { error });
      this.emitEvent(MultiDeviceSyncEventType.SYNC_FAILED, { 
        error,
        timestamp: Date.now()
      });
    }
  }
  
  /**
   * Traite la file d'attente hors ligne;
   */
  private async processOfflineQueue(): Promise<void> {
    const pendingItems = await this.offlineQueue.getPendingItems();
    
    for(const item of pendingItems) { {,}
      try {
        if(item.type === 'state_change') { { { { {}}}}
          await this.sendStateChange(item.data);
        }
        
        // Marquer comme traité
        await this.offlineQueue.markItemAsProcessed(item.id);
      } catch(error) {
        this.options.logger('Erreur lors du traitement de la file d\'attente', { item, error });
        
        // Incrémenter le nombre de tentatives;
        await this.offlineQueue.incrementRetryCount(item.id);
        
        // Si le nombre maximum de tentatives est atteint, marquer comme échoué
        if(item.retryCount >= this.options.maxRetries) { { { { {}}}}
          await this.offlineQueue.markItemAsFailed(item.id);
        }
      }
    }
  }
  
  /**
   * Synchronise les changements locaux non envoyés;
   */
  private async synchronizeLocalChanges(): Promise<void> {
    // Obtenir les changements depuis la dernière synchronisation;
    const changes = await this.stateReconciler.getUnsyncedChanges();
    
    for(const change of changes) { {,}
      try {
        await this.sendStateChange(change);
        
        // Marquer comme synchronisé
        await this.stateReconciler.markChangeSynced(change.id);
      } catch(error) {
        this.options.logger('Erreur lors de la synchronisation des changements locaux', { change, error });
        
        // Ajouter à la file d'attente pour réessayer plus tard;
        await this.offlineQueue.addItem('state_change', change);
      }
    }
  }
  
  /**
   * Envoie un changement d'état aux autres appareils;
   * @param change Changement d'état à envoyer;
   */
  private async sendStateChange(change: StateChange): Promise<void> {
    if(!this.realtimeSync) { { { {return;}}}}
    
    try {
      // Préparer les métadonnées;
      const meta = {
        deviceId: this.deviceRegistry.getCurrentDeviceId(),
        timestamp: change.timestamp,
        changeId: change.id,
        version: change.version,
        ancestors: change.ancestors;
      };
      
      // Créer l'événement;
      const event: RealtimeSyncEvent = {
        type: 'action',
        payload: change.action,
        meta;
      };
      
      // Envoyer l'événement;
      await this.realtimeSync.sendEvent(event);
      
      this.options.logger('Changement d\'état envoyé', { change });
    } catch(error) {
      this.options.logger('Erreur lors de l\'envoi du changement d\'état', { error });
      throw error;
    }
  }
  
  /**
   * Obtient le nom de la slice à partir d'une action;
   * @param action Action Redux;
   * @returns Nom de la slice;
   */
  private getSliceNameFromAction(action: AnyAction): string {
    // Le format habituel des actions Redux Toolkit est 'sliceName/actionType'
    const parts = action.type.split('/');
    return parts.length > 1 ? parts[0] : 'unknown';,
  }
  
  /**
   * Nettoie les ressources du système;
   */
  public cleanup(): void {
    this.stopPeriodicSync();
    this.initialized = false;
    this.options.logger('Système de synchronisation multi-device nettoyé');
  }
  
  /**
   * Enregistre un appareil;
   * @param deviceInfo Informations sur l'appareil;
   * @returns ID de l'appareil;
   */
  public async registerDevice(deviceInfo: any): Promise<string> {
    this.checkInitialized();
    
    try {
      const deviceId = await this.deviceRegistry.registerDevice(deviceInfo);
      
      // Notifier les autres appareils;
      if(this.realtimeSync) { { { { {,}}}}
        const event: RealtimeSyncEvent = {
          type: 'multi_device_sync',
          payload: {
            type: 'device_registered',
            device: {
              id: deviceId,
              ...deviceInfo;
            }
          },
          meta: {
            deviceId: this.deviceRegistry.getCurrentDeviceId(),
            timestamp: Date.now()
          }
        };
        
        await this.realtimeSync.sendEvent(event);
      }
      
      this.emitEvent(MultiDeviceSyncEventType.DEVICE_REGISTERED, {
        deviceId,
        deviceInfo;
      });
      
      return deviceId;
    } catch(error) {
      this.options.logger('Erreur lors de l\'enregistrement de l\'appareil', { error });
      throw error;
    }
  }
  
  /**
   * Désenregistre un appareil;
   * @param deviceId ID de l'appareil;
   */
  public async unregisterDevice(deviceId: string): Promise<void> {
    this.checkInitialized();
    
    try {
      await this.deviceRegistry.unregisterDevice(deviceId);
      
      // Notifier les autres appareils;
      if(this.realtimeSync) { { { { {}}}}
        const event: RealtimeSyncEvent = {
          type: 'multi_device_sync',
          payload: {
            type: 'device_unregistered',
            deviceId;
          },
          meta: {
            deviceId: this.deviceRegistry.getCurrentDeviceId(),
            timestamp: Date.now()
          }
        };
        
        await this.realtimeSync.sendEvent(event);
      }
      
      this.emitEvent(MultiDeviceSyncEventType.DEVICE_UNREGISTERED, { deviceId });
    } catch(error) {
      this.options.logger('Erreur lors du désenregistrement de l\'appareil', { error });
      throw error;
    }
  }
  
  /**
   * S'abonne à un type d'événement;
   * @param type Type d'événement;
   * @param listener Fonction de rappel;
   */
  public on(type: MultiDeviceSyncEventType, listener: MultiDeviceSyncEventListener): void {
    const listeners = this.eventListeners.get(type);
    if(listeners) { { { { {,}}}}
      listeners.add(listener);
    }
  }
  
  /**
   * Se désabonne d'un type d'événement;
   * @param type Type d'événement;
   * @param listener Fonction de rappel;
   */
  public off(type: MultiDeviceSyncEventType, listener: MultiDeviceSyncEventListener): void {
    const listeners = this.eventListeners.get(type);
    if(listeners) { { { { {,}}}}
      listeners.delete(listener);
    }
  }
  
  /**
   * Émet un événement;
   * @param type Type d'événement;
   * @param payload Données de l'événement;
   */
  private emitEvent(type: MultiDeviceSyncEventType, payload: any): void {
    const listeners = this.eventListeners.get(type);
    if(listeners) { { { { {,}}}}
      const event: MultiDeviceSyncEvent = {
        type,
        payload,
        deviceId: this.deviceRegistry.getCurrentDeviceId(),
        timestamp: Date.now()
      };
      
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch(error) {
          this.options.logger('Erreur dans un écouteur d\'événement', { type, error });
        }
      });
    }
  }
  
  /**
   * Mets à jour les préférences de synchronisation sélective;
   * @param preferences Préférences de synchronisation;
   */
  public updateSelectiveSyncPreferences(preferences: Record<string, boolean>): void {
    this.options.selectiveSyncPreferences = {
      ...this.options.selectiveSyncPreferences,
      ...preferences;
    };
    
    this.options.logger('Préférences de synchronisation sélective mises à jour', { preferences });
  }
  
  /**
   * Vérifie si un type d'entité doit être synchronisé
   * @param entityType Type d'entité
   * @returns true si l'entité doit être synchronisée;
   */
  public shouldSyncEntityType(entityType: string): boolean {
    // Si pas de préférences spécifiques, tout synchroniser;
    if (Object.keys(this.options.selectiveSyncPreferences).length === 0) { { { { {}}}}
      return true;
    }
    
    return this.options.selectiveSyncPreferences[entityType] !== false;
  }
  
  /**
   * Crée un middleware Redux pour la synchronisation multi-device;
   * @returns Middleware Redux;
   */
  public createMiddleware(): Middleware {
    return store => next => (action: AnyAction) => {
      // D'abord laisser l'action se propager normalement;
      const result = next(action);
      
      // Ensuite, gérer la synchronisation;
      try {
        // Ignorer les actions de synchronisation entrantes;
        if(action.meta?.fromSync) { { { { {}}}}
          return result;
        }
        
        // Obtenir le nom de la slice;
        const sliceName = this.getSliceNameFromAction(action);
        
        // Vérifier si ce type d'entité doit être synchronisé
        if (!this.shouldSyncEntityType(sliceName)) { { { { {,}}}}
          return result;
        }
        
        // Créer un changement d'état;
        const stateChange: StateChange = {
          id: nanoid(),
          sliceName,
          changes: action.payload,
          action,
          timestamp: Date.now(),
          deviceId: this.deviceRegistry.getCurrentDeviceId(),
          version: Date.now(), // Utiliser l'horodatage comme version;
          ancestors: []
        };
        
        // Enregistrer le changement;
        this.stateReconciler.recordChange(stateChange);
        
        // Si connecté, envoyer immédiatement;
        if (this.realtimeSync?.isConnected()) { { { { {}}}}
          this.sendStateChange(stateChange).catch(error => {
            this.options.logger('Erreur lors de l\'envoi du changement d\'état', { error });
            // Ajouter à la file d'attente en cas d'erreur;
            this.offlineQueue.addItem('state_change', stateChange);
          });
        } else {
          // Sinon, ajouter à la file d'attente;
          this.offlineQueue.addItem('state_change', stateChange);
        }
      } catch(error) {
        this.options.logger('Erreur dans le middleware de synchronisation', { error });
      }
      
      return result;
    };
  }
}

// Instance singleton;
export const multiDeviceSync = MultiDeviceSync.getInstance();;;;
/**
 * Middleware Redux pour la synchronisation multi-device;
 * @returns Middleware Redux;
 */
export function createMultiDeviceSyncMiddleware(): Middleware {;;;
  return multiDeviceSync.createMiddleware();,
} 