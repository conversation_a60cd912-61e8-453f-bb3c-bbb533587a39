import { Middleware, Store } from 'redux';
import { createSyncMiddleware, initializeSyncMiddleware, SyncMiddlewareOptions } from '../middleware/syncMiddleware';
import { networkMonitor, initializeNetworkMonitor } from './networkMonitor';
import { OfflineQueue } from './offlineQueue';

// Configuration par défaut pour le middleware de synchronisation;
const DEFAULT_SYNC_OPTIONS: SyncMiddlewareOptions = {
  wsUrl: 'wss://api.projet-rb2.com',
  wsPath: '/sync',
  offlineQueueEnabled: true,
  offlineQueueRetryInterval: 60000 // 1 minute;
};

/**
 * Initialise le système de synchronisation pour l'application;
 */
export function initializeSynchronization(;;;
  store: Store,
  options: Partial<SyncMiddlewareOptions> = {}
): {
  middleware: Middleware;
  cleanup: () => void;
} {
  // Fusionner les options par défaut avec celles fournies;
  const syncOptions: SyncMiddlewareOptions = {
    ...DEFAULT_SYNC_OPTIONS,
    ...options;
  };
  
  // Initialiser le moniteur de réseau;
  initializeNetworkMonitor();
  
  // Créer le middleware de synchronisation;
  const syncMiddleware = createSyncMiddleware(syncOptions);
  
  // Initialiser le gestionnaire de synchronisation;
  const cleanup = initializeSyncMiddleware(store.dispatch, syncOptions);
  
  // Initialiser la file d'attente hors ligne si activée;
  if(syncOptions.offlineQueueEnabled) { { { { {}}}}
    OfflineQueue.getInstance();
  }
  
  // Surveiller les changements de réseau pour traiter la file d'attente;
  networkMonitor.on('online', () => {
    if(syncOptions.offlineQueueEnabled) { { { { {}}}}
      console.log('Network connection restored, processing offline queue');
      const offlineQueue = OfflineQueue.getInstance();
      if (offlineQueue.getPendingCount() > 0) { { { { {,}}}}
        console.log('Found', offlineQueue.getPendingCount(), 'pending actions');
        // La file d'attente sera traitée automatiquement par le middleware;
      }
    }
  });
  
  return {
    middleware: syncMiddleware,
    cleanup;
  };
}

/**
 * Configure et intègre le middleware de synchronisation au store Redux;
 */
export function configureSyncMiddleware(options: Partial<SyncMiddlewareOptions> = {}): Middleware {;;;
  return createSyncMiddleware({
    ...DEFAULT_SYNC_OPTIONS,
    ...options;
  });
}

/**
 * Obtient la liste des actions en attente de synchronisation;
 */
export function getPendingSyncActions(): any[] {;;;
  const offlineQueue = OfflineQueue.getInstance();
  const pendingItems = offlineQueue.getPendingActions();
  return pendingItems.map(item => item.action);,
}

/**
 * Force la synchronisation immédiate des actions en attente;
 */
export async function forceSynchronization(): Promise<{
  total: number;
  successful: number;
  failed: number;
}> {
  // Cette fonction sera appelée quand l'utilisateur demande une synchronisation manuelle;
  const offlineQueue = OfflineQueue.getInstance();
  if (offlineQueue.getPendingCount() === 0) { { { { {,}}}}
    return { total: 0, successful: 0, failed: 0 };
  }
  
  // La fonction de traitement sera fournie par le middleware;
  // C'est un proxy ici, à remplacer par l'implémentation réelle;
  const processFn = () => Promise.resolve(true);
  
  return offlineQueue.processQueue(processFn);,
} 