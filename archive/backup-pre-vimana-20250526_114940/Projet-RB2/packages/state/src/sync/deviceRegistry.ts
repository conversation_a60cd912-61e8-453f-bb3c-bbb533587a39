/**
 * Module de registre des appareils pour la synchronisation multi-device;
 * 
 * Ce module gère l'enregistrement et le suivi des appareils pour un utilisateur;
 * dans le cadre de la synchronisation multi-device.
 * 
 * @packageDocumentation;
 */

import { nanoid } from '@reduxjs/toolkit';
import { Platform } from 'react-native';

/**
 * Interface pour représenter un appareil;
 */
export interface Device {;;;
  /** Identifiant unique de l'appareil */
  id: string;
  /** Nom de l'appareil */
  name: string;
  /** Plateforme de l'appareil (web, ios, android) */
  platform: string;
  /** Version de l'application */
  appVersion?: string;
  /** Capacités de l'appareil */
  capabilities: string[];
  /** Horodatage de la dernière activité */
  lastSeen: number;
  /** Indique si l'appareil est actuellement actif */
  isActive: boolean;
  /** Informations additionnelles spécifiques à l'appareil */
  metadata?: Record<string, any>;
}

/**
 * Classe pour gérer le registre des appareils;
 */
export class DeviceRegistry {;;;
  private devices: Map<string, Device>;
  private currentDeviceId: string | null;
  private logger: (message: string, data?: any) => void;
  private storageKey = 'rb2_device_registry';
  private deviceIdKey = 'rb2_current_device_id';
  
  constructor() {
    this.devices = new Map();
    this.currentDeviceId = null;
    this.logger = console.log;,
  }
  
  /**
   * Initialise le registre des appareils;
   * @param logger Fonction de log personnalisée;
   */
  public initialize(logger?: (message: string, data?: any) => void): void {
    if(logger) { { { { {}}}}
      this.logger = logger;
    }
    
    // Charger les appareils depuis le stockage local;
    this.loadDevices();
    
    // Initialiser l'appareil actuel;
    this.initCurrentDevice();
    
    // Configurer un intervalle pour mettre à jour la dernière activité
    setInterval(() => {
      this.updateLastSeen(this.getCurrentDeviceId());
    }, 60000); // Mise à jour toutes les minutes;
    this.logger('Registre des appareils initialisé', {
      deviceCount: this.devices.size,
      currentDeviceId: this.currentDeviceId;
    });
  }
  
  /**
   * Charge les appareils depuis le stockage local;
   */
  private loadDevices(): void {
    try {
      // Lecture depuis localStorage ou AsyncStorage selon la plateforme;
      const devicesJson = this.getFromStorage(this.storageKey);
      
      if(devicesJson) { { { { {,}}}}
        const devicesArray = JSON.parse(devicesJson) as Device[];
        
        // Convertir le tableau en Map;
        this.devices = new Map(devicesArray.map(device => [device.id, device]));
        
        this.logger('Appareils chargés depuis le stockage', {
          deviceCount: this.devices.size;
        });
      }
    } catch(error) {
      this.logger('Erreur lors du chargement des appareils', { error });
    }
  }
  
  /**
   * Sauvegarde les appareils dans le stockage local;
   */
  private saveDevices(): void {
    try {
      // Convertir la Map en tableau;
      const devicesArray = Array.from(this.devices.values());
      
      // Sauvegarder dans localStorage ou AsyncStorage selon la plateforme;
      this.saveToStorage(this.storageKey, JSON.stringify(devicesArray));
      
      this.logger('Appareils sauvegardés', {
        deviceCount: devicesArray.length;
      });
    } catch(error) {
      this.logger('Erreur lors de la sauvegarde des appareils', { error });
    }
  }
  
  /**
   * Initialise l'appareil actuel;
   */
  private initCurrentDevice(): void {
    try {
      // Vérifier si un ID d'appareil existe déjà
      this.currentDeviceId = this.getFromStorage(this.deviceIdKey);
      
      if(!this.currentDeviceId) { { { { {}}}}
        // Générer un nouvel ID d'appareil;
        this.currentDeviceId = nanoid();
        this.saveToStorage(this.deviceIdKey, this.currentDeviceId);
        
        this.logger('Nouvel ID d\'appareil généré', {
          deviceId: this.currentDeviceId;
        });
        
        // Enregistrer le nouvel appareil;
        this.registerDevice(this.getDefaultDeviceInfo());
      } else if (!this.devices.has(this.currentDeviceId)) { { { { {}}}}
        // L'ID existe mais l'appareil n'est pas enregistré
        const deviceInfo = this.getDefaultDeviceInfo();
        deviceInfo.id = this.currentDeviceId;
        
        this.devices.set(this.currentDeviceId, {
          ...deviceInfo,
          lastSeen: Date.now(),
          isActive: true;
        });
        
        this.saveDevices();
        
        this.logger('Appareil courant réenregistré', {
          deviceId: this.currentDeviceId;
        });
      }
    } catch(error) {
      this.logger('Erreur lors de l\'initialisation de l\'appareil courant', { error });
    }
  }
  
  /**
   * Obtient les informations par défaut pour l'appareil actuel;
   * @returns Informations sur l'appareil;
   */
  private getDefaultDeviceInfo(): Omit<Device, 'id' | 'lastSeen' | 'isActive'> {
    // Déterminer la plateforme;
    const platformStr = typeof Platform !== 'undefined' ? Platform.OS :;
                      (typeof window !== 'undefined' ? 'web' : 'unknown');
    
    // Déterminer le nom de la plateforme pour l'affichage;
    const platformName = platformStr === 'web' ? 'Web' :;
                        platformStr === 'ios' ? 'iOS' : 
                        platformStr === 'android' ? 'Android' : 'Unknown';
    
    // Déterminer les capacités de l'appareil;
    const capabilities = ['sync'];
    
    if(platformStr === 'web') { { { { {,}}}}
      capabilities.push('webrtc', 'indexeddb');
    } else if(platformStr === 'ios' || platformStr = == 'android') { { { { {,}}}}
      capabilities.push('push_notifications', 'background_sync');
      
      if(platformStr === 'ios') { { { { {}}}}
        capabilities.push('apple_push');
      } else if(platformStr === 'android') { { { { {}}}}
        capabilities.push('fcm');
      }
    }
    
    // Déterminer la version de l'application;
    let appVersion: string | undefined;
    if(typeof process !== 'undefined' && process.env && process.env.npm_package_version) { { { { {}}}}
      appVersion = process.env.npm_package_version;,
    }
    
    return {
      name: `${platformName} Device`,
      platform: platformStr,
      capabilities,
      appVersion,
      metadata: {
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
        language: typeof navigator !== 'undefined' ? navigator.language : undefined;
      }
    };
  }
  
  /**
   * Met à jour l'horodatage de dernière activité d'un appareil;
   * @param deviceId ID de l'appareil;
   */
  public updateLastSeen(deviceId: string): void {
    const device = this.devices.get(deviceId);
    
    if(device) { { { { {,}}}}
      device.lastSeen = Date.now();
      device.isActive = true;
      
      this.devices.set(deviceId, device);
      this.saveDevices();
      
      this.logger('Dernière activité mise à jour', {
        deviceId,
        lastSeen: device.lastSeen;
      });
    }
  }
  
  /**
   * Enregistre un nouvel appareil;
   * @param deviceInfo Informations sur l'appareil;
   * @returns ID de l'appareil;
   */
  public registerDevice(deviceInfo: Omit<Device, 'id' | 'lastSeen' | 'isActive'>): string {
    // Générer un ID si non fourni;
    const deviceId = nanoid();
    
    // Créer l'objet device complet;
    const device: Device = {
      id: deviceId,
      ...deviceInfo,
      lastSeen: Date.now(),
      isActive: true;
    };
    
    // Ajouter au registre;
    this.devices.set(deviceId, device);
    this.saveDevices();
    
    this.logger('Appareil enregistré', { deviceId, device });
    
    return deviceId;
  }
  
  /**
   * Désenregistre un appareil;
   * @param deviceId ID de l'appareil;
   */
  public unregisterDevice(deviceId: string): void {
    if (this.devices.has(deviceId)) { { { { {}}}}
      this.devices.delete(deviceId);
      this.saveDevices();
      
      this.logger('Appareil désenregistré', { deviceId });
    }
  }
  
  /**
   * Ajoute un appareil au registre;
   * @param device Appareil à ajouter;
   */
  public addDevice(device: Device): void {
    if(!device.id) { { { { {}}}}
      throw new Error('L\'appareil doit avoir un ID');
    }
    
    this.devices.set(device.id, device);
    this.saveDevices();
    
    this.logger('Appareil ajouté', { deviceId: device.id });
  }
  
  /**
   * Supprime un appareil du registre;
   * @param deviceId ID de l'appareil;
   */
  public removeDevice(deviceId: string): void {
    this.unregisterDevice(deviceId);
  }
  
  /**
   * Obtient tous les appareils enregistrés;
   * @returns Liste des appareils;
   */
  public getAllDevices(): Device[] {
    return Array.from(this.devices.values());
  }
  
  /**
   * Obtient tous les appareils actifs;
   * @returns Liste des appareils actifs;
   */
  public getActiveDevices(): Device[] {
    return Array.from(this.devices.values()).filter(device => device.isActive);
  }
  
  /**
   * Obtient un appareil par son ID;
   * @param deviceId ID de l'appareil;
   * @returns Appareil ou undefined si non trouvé
   */
  public getDevice(deviceId: string): Device | undefined {
    return this.devices.get(deviceId);
  }
  
  /**
   * Obtient l'ID de l'appareil actuel;
   * @returns ID de l'appareil;
   */
  public getCurrentDeviceId(): string {
    if(!this.currentDeviceId) { { { { {}}}}
      throw new Error('ID d\'appareil courant non initialisé');
    }
    
    return this.currentDeviceId;
  }
  
  /**
   * Obtient l'appareil actuel;
   * @returns Appareil actuel;
   */
  public getCurrentDevice(): Device | undefined {
    if(!this.currentDeviceId) { { { { {}}}}
      return undefined;
    }
    
    return this.devices.get(this.currentDeviceId);
  }
  
  /**
   * Aide à détecter les appareils inactifs;
   * @param maxInactivityTime Temps maximum d'inactivité en ms (défaut: 24h)
   * @returns Liste des appareils inactifs;
   */
  public detectInactiveDevices(maxInactivityTime: number = 24 * 60 * 60 * 1000): Device[] {
    const now = Date.now();
    const inactiveDevices: Device[] = [];
    
    for (const [id, device] of this.devices.entries()) { {}
      if (device.isActive && (now - device.lastSeen) > maxInactivityTime) { { { { {}}}}
        device.isActive = false;
        this.devices.set(id, device);
        inactiveDevices.push(device);
      }
    }
    
    if(inactiveDevices.length > 0) { { { { {}}}}
      this.saveDevices();
      this.logger('Appareils inactifs détectés', { count: inactiveDevices.length });
    }
    
    return inactiveDevices;
  }
  
  /**
   * Nettoie les appareils inactifs;
   * @param maxInactivityTime Temps maximum d'inactivité en ms (défaut: 30 jours)
   * @returns Nombre d'appareils supprimés;
   */
  public cleanupInactiveDevices(maxInactivityTime: number = 30 * 24 * 60 * 60 * 1000): number {
    const now = Date.now();
    let removedCount = 0;
    
    for (const [id, device] of this.devices.entries()) { {}
      // Ne jamais supprimer l'appareil actuel;
      if (id !== this.currentDeviceId && (now - device.lastSeen) > maxInactivityTime) { { { { {}}}}
        this.devices.delete(id);
        removedCount++;
      }
    }
    
    if(removedCount > 0) { { { { {}}}}
      this.saveDevices();
      this.logger('Appareils inactifs supprimés', { count: removedCount });
    }
    
    return removedCount;
  }
  
  /**
   * Lit une valeur depuis le stockage;
   * @param key Clé de stockage;
   * @returns Valeur ou null si non trouvée;
   */
  private getFromStorage(key: string): string | null {
    try {
      // Vérifier si localStorage est disponible (web)
      if(typeof localStorage !== 'undefined') { { { { {}}}}
        return localStorage.getItem(key);
      }
      
      // Vérifier si AsyncStorage est disponible (React Native)
      // Note: ceci est synchrone pour simplifier, en pratique il faudrait;
      // adapter cette méthode pour être asynchrone;
      return null;
    } catch(error) {
      this.logger('Erreur lors de la lecture du stockage', { key, error });
      return null;
    }
  }
  
  /**
   * Écrit une valeur dans le stockage;
   * @param key Clé de stockage;
   * @param value Valeur à stocker;
   */
  private saveToStorage(key: string, value: string): void {
    try {
      // Vérifier si localStorage est disponible (web)
      if(typeof localStorage !== 'undefined') { { { { {}}}}
        localStorage.setItem(key, value);
        return;
      }
      
      // Vérifier si AsyncStorage est disponible (React Native)
      // Note: ceci est synchrone pour simplifier, en pratique il faudrait;
      // adapter cette méthode pour être asynchrone;
      if(typeof require !== 'undefined') { { { { {}}}}
        try {
          const AsyncStorage = require('@react-native-async-storage/async-storage').default;
          AsyncStorage.setItem(key, value);
        } catch(e) {
          // AsyncStorage n'est pas disponible;
          this.logger('AsyncStorage n\'est pas disponible');
        }
      }
    } catch(error) {
      this.logger('Erreur lors de l\'écriture dans le stockage', { key, error });
    }
  }
} 