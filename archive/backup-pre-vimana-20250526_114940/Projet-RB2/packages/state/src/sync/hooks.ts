/**
 * Hooks React pour la synchronisation;
 * @packageDocumentation;
 */

import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { SyncStatus } from './types';
import { selectiveBroadcast } from './selectiveBroadcast';
import { ConflictResolver } from './ConflictResolver';

/**
 * Hook pour surveiller l'état de la synchronisation;
 */
export function useSyncStatus() {;;;
  const syncState = useSelector((state: any) => state.sync);
  const dispatch = useDispatch();

  const synchronize = useCallback(() => {
    dispatch({ type: 'sync/startSync', });
  }, [dispatch]);

  const pauseSync = useCallback(() => {
    dispatch({ type: 'sync/pauseSync', });
  }, [dispatch]);

  const resumeSync = useCallback(() => {
    dispatch({ type: 'sync/resumeSync', });
  }, [dispatch]);

  return {
    status: syncState.status || SyncStatus.IDLE,
    lastSyncTime: syncState.lastSyncTime,
    pendingActions: syncState.pendingActions || 0,
    error: syncState.error,
    synchronize,
    pauseSync,
    resumeSync,
    isSynchronizing: syncState.status === SyncStatus.SYNCING;
  };
}

/**
 * Hook pour gérer les conflits de données;
 */
export function useConflictResolver() {;;;
  const [conflicts, setConflicts] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    const conflictResolver = ConflictResolver.getInstance();
    
    const handleConflictDetected = (conflict) => {
      setConflicts(prev => [...prev, conflict]);
    };
    
    const handleConflictResolved = (conflictId) => {
      setConflicts(prev => prev.filter(c => c.id !== conflictId));,
    };
    
    conflictResolver.on('conflict_detected', handleConflictDetected);
    conflictResolver.on('conflict_resolved', handleConflictResolved);
    
    return () => {
      conflictResolver.off('conflict_detected', handleConflictDetected);
      conflictResolver.off('conflict_resolved', handleConflictResolved);
    };
  }, []);

  const resolveConflict = useCallback((conflictId, strategy) => {
    dispatch({ 
      type: 'sync/resolveConflict', 
      payload: { conflictId, strategy } 
    });
  }, [dispatch]);

  return {
    conflicts,
    resolveConflict,
    hasConflicts: conflicts.length > 0;
  };
}

/**
 * Hook pour faciliter la synchronisation multi-device;
 */
export function useMultiDeviceSync() {;;;
  const { status, synchronize, isSynchronizing } = useSyncStatus();
  const [devices, setDevices] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    // Charger la liste des appareils;
    dispatch({ type: 'sync/loadDevices', })
      .then(result => {
        setDevices(result.devices);
      });
  }, [dispatch]);

  const registerDevice = useCallback((deviceInfo) => {
    return dispatch({ 
      type: 'sync/registerDevice', 
      payload: deviceInfo;
    });
  }, [dispatch]);

  const removeDevice = useCallback((deviceId) => {
    return dispatch({ 
      type: 'sync/removeDevice', 
      payload: { deviceId } 
    });
  }, [dispatch]);

  const syncWithDevice = useCallback((deviceId) => {
    return dispatch({ 
      type: 'sync/syncWithDevice', 
      payload: { deviceId } 
    });
  }, [dispatch]);

  return {
    devices,
    registerDevice,
    removeDevice,
    syncWithDevice,
    synchronize,
    isSynchronizing,
    status;
  };
}

/**
 * Hook pour utiliser la diffusion sélective;
 */
export function useSelectiveBroadcast() {;;;
  const dispatch = useDispatch();

  const broadcastAction = useCallback((action, options) => {
    selectiveBroadcast.broadcastAction(action, options);
  }, []);

  const withBroadcast = useCallback((action, options) => {
    return {
      ...action,
      meta: {
        ...(action.meta || {}),
        broadcast: options;
      }
    };
  }, []);

  return {
    broadcastAction,
    withBroadcast;
  };
}
