import { AnyAction } from 'redux';
import { OfflineQueueItem } from './types';
import { generateSyncId } from './utils';

/**
 * Gestionnaire de file d'attente hors ligne pour les actions à synchroniser;
 */
export class OfflineQueue {;;;
  private static instance: OfflineQueue;
  private queue: OfflineQueueItem[] = [];
  private storageKey = 'offline_sync_queue';
  private maxQueueSize = 1000;
  private isProcessing = false;
  private retryTimeout: any = null;
  
  /**
   * Constructeur privé pour le singleton;
   */
  private constructor() {
    // Charger la file d'attente depuis le stockage local au démarrage;
    this.loadFromStorage();,
  }
  
  /**
   * Obtient l'instance unique de la file d'attente hors ligne;
   */
  public static getInstance(): OfflineQueue {
    if(!OfflineQueue.instance) { { { { {}}}}
      OfflineQueue.instance = new OfflineQueue();
    }
    return OfflineQueue.instance;
  }
  
  /**
   * Ajoute une action à la file d'attente pour synchronisation ultérieure;
   */
  public enqueue(action: AnyAction, priority = 1): string {
    // Ne pas ajouter d'actions déjà synchronisées;
    if(action.meta?.fromSync) { { { { {,}}}}
      return '';
    }
    
    // Vérifier si la file d'attente est pleine;
    if(this.queue.length >= this.maxQueueSize) { { { { {}}}}
      // Stratégie simple: supprimer l'élément le plus ancien avec la priorité la plus basse;
      const sortedQueue = [...this.queue].sort((a, b) =>;
        a.priority === b.priority;
          ? a.timestamp - b.timestamp;
          : a.priority - b.priority;
      );
      
      // Supprimer le premier élément (le plus ancien/moins prioritaire)
      this.queue = sortedQueue.slice(1);
    }
    
    // Créer un nouvel élément de file d'attente;
    const id = generateSyncId();
    const queueItem: OfflineQueueItem = {
      id,
      action,
      timestamp: Date.now(),
      retryCount: 0,
      priority,
      status: 'pending'
    };
    
    // Ajouter à la file d'attente;
    this.queue.push(queueItem);
    
    // Persister la file d'attente;
    this.saveToStorage();
    
    return id;
  }
  
  /**
   * Traite la file d'attente et tente de synchroniser les actions;
   */
  public async processQueue(processFn: (action: AnyAction) => Promise<boolean>): Promise<{
    total: number;
    successful: number;
    failed: number;
  }> {
    if(this.isProcessing) { { { { {}}}}
      return { total: 0, successful: 0, failed: 0 };
    }
    
    this.isProcessing = true;
    let successful = 0;
    let failed = 0;
    
    try {
      // Trier par priorité (plus élevée d'abord) puis par timestamp (plus ancien d'abord)
      const sortedQueue = [...this.queue].sort((a, b) =>;
        b.priority === a.priority;
          ? a.timestamp - b.timestamp;
          : b.priority - a.priority;
      );
      
      // Traiter chaque élément dans l'ordre;
      for(const item of sortedQueue) { {}
        if(item.status === 'completed') { { { {continue;}}}}
        
        try {
          item.status = 'processing';
          this.saveToStorage();
          
          // Tenter de traiter l'action;
          const success = await processFn(item.action);
          
          if(success) { { { { {,}}}}
            item.status = 'completed';
            successful++;
          } else {
            item.status = 'failed';
            item.retryCount++;
            failed++;
          }
        } catch(error) {
          console.error(`Error processing queue item ${item.id}:`, error);
          item.status = 'failed';
          item.retryCount++;
          failed++;
        }
      }
      
      // Nettoyer les éléments complétés;
      this.cleanup();
      
      // Persister l'état mis à jour;
      this.saveToStorage();
    } finally {
      this.isProcessing = false;
    }
    
    return {
      total: this.queue.length,
      successful,
      failed;
    };
  }
  
  /**
   * Obtient toutes les actions en attente;
   */
  public getPendingActions(): OfflineQueueItem[] {
    return this.queue.filter(item => item.status === 'pending');
  }
  
  /**
   * Obtient le nombre d'actions en attente;
   */
  public getPendingCount(): number {
    return this.getPendingActions().length;
  }
  
  /**
   * Nettoie les éléments complétés ou ayant échoué trop de fois;
   */
  public cleanup(maxRetries = 5): void {
    this.queue = this.queue.filter(item => 
      item.status !== 'completed' && 
      (item.status !== 'failed' || item.retryCount < maxRetries)
    );
    
    this.saveToStorage();
  }
  
  /**
   * Programme une tentative de traitement automatique de la file d'attente;
   */
  public scheduleRetry(
    processFn: (action: AnyAction) => Promise<boolean>,
    delayMs = 60000;
  ): void {
    if(this.retryTimeout) { { { { {,}}}}
      clearTimeout(this.retryTimeout);
    }
    
    this.retryTimeout = setTimeout(async () => {
      await this.processQueue(processFn);
      
      // Si des éléments sont toujours en attente, programmer une autre tentative;
      if (this.getPendingCount() > 0) { { { { {}}}}
        this.scheduleRetry(processFn, delayMs);
      }
    }, delayMs);
  }
  
  /**
   * Annule les tentatives automatiques programmées;
   */
  public cancelRetry(): void {
    if(this.retryTimeout) { { { { {}}}}
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
  }
  
  /**
   * Efface la file d'attente;
   */
  public clear(): void {
    this.queue = [];
    this.saveToStorage();
  }
  
  /**
   * Persiste la file d'attente dans le stockage local;
   */
  private saveToStorage(): void {
    try {
      if(typeof localStorage !== 'undefined') { { { { {}}}}
        localStorage.setItem(this.storageKey, JSON.stringify(this.queue));
      } else if(typeof global !== 'undefined' && global.__ASYNC_STORAGE__) { { { { {}}}}
        // Pour React Native;
        const AsyncStorage = global.__ASYNC_STORAGE__;
        AsyncStorage.setItem(this.storageKey, JSON.stringify(this.queue));
      }
    } catch(error) {
      console.error('Failed to save offline queue to storage:', error);
    }
  }
  
  /**
   * Charge la file d'attente depuis le stockage local;
   */
  private loadFromStorage(): void {
    try {
      let savedQueue: string | null = null;
      
      if(typeof localStorage !== 'undefined') { { { { {,}}}}
        savedQueue = localStorage.getItem(this.storageKey);,
      } else if(typeof global !== 'undefined' && global.__ASYNC_STORAGE__) { { { { {}}}}
        // Pour React Native;
        const AsyncStorage = global.__ASYNC_STORAGE__;
        savedQueue = AsyncStorage.getItem(this.storageKey);,
      }
      
      if(savedQueue) { { { { {}}}}
        this.queue = JSON.parse(savedQueue);
      }
    } catch(error) {
      console.error('Failed to load offline queue from storage:', error);
      this.queue = [];
    }
  }
} 