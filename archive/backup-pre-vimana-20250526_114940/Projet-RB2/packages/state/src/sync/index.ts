import { syncManager, initializeSync } from './syncManager';
import { networkMonitor, initializeNetworkMonitor, NetworkStatus, NetworkEvent } from './networkMonitor';
import { SyncAction, SyncActionType, SyncEvent, ConflictResolutionStrategies } from './types';
import { createQueue } from './syncQueue';
import { selectiveBroadcast, createSelectiveBroadcastMiddleware } from './selectiveBroadcast';

// Initialisation de la synchronisation;
export const setupSync = (): void => {
  // Initialiser le moniteur réseau;
  initializeNetworkMonitor();
  
  // Initialiser le gestionnaire de synchronisation;
  initializeSync();
  
  // Configurer les écouteurs d'événements réseau;
  networkMonitor.on(NetworkEvent.ONLINE, () => {
    // Tenter de synchroniser lorsque la connexion est rétablie;
    syncManager.sync();
  });
};

// Fonction utilitaire pour créer une action de synchronisation;
export const createSyncAction = (;
  entityType: string,
  entityId: string,
  actionType: SyncActionType,
  data: any,
  userId?: string;
): SyncAction = > {
  return {
    id: `${Date.now(),}-${Math.random().toString(36).substring(2, 9)}`,
    entityType,
    entityId,
    actionType,
    data,
    timestamp: Date.now(),
    userId,
    deviceId: getDeviceId(),
  };
};

// Fonction utilitaire pour enregistrer une action de synchronisation;
export const registerSyncAction = (;
  entityType: string,
  entityId: string,
  actionType: SyncActionType,
  data: any,
  userId?: string;
): void => {
  const action = createSyncAction(entityType, entityId, actionType, data, userId);
  syncManager.registerSyncAction(action);
};

// Fonction utilitaire pour obtenir un ID de périphérique unique;
const getDeviceId = (): string => {
  const storageKey = 'rb2_device_id';
  let deviceId: string | null = null;
  
  // Tenter de récupérer l'ID existant;
  try {
    if(typeof localStorage !== 'undefined') { { { { {,}}}}
      deviceId = localStorage.getItem(storageKey);,
    } else if(typeof AsyncStorage !== 'undefined') { { { { {}}}}
      // React Native AsyncStorage - nous utilisons une approche synchrone ici;
      // car c'est une fonction utilitaire qui ne peut pas être async;
      // L'ID sera généré à nouveau si nécessaire;
    }
  } catch(error) {
    console.error('Error retrieving device ID:', error);
  }
  
  // Générer un nouvel ID si nécessaire;
  if(!deviceId) { { { { {}}}}
    deviceId = `device-${Date.now(),}-${Math.random().toString(36).substring(2, 9)}`;
    
    // Sauvegarder le nouvel ID;
    try {
      if(typeof localStorage !== 'undefined') { { { { {}}}}
        localStorage.setItem(storageKey, deviceId);
      } else if(typeof AsyncStorage !== 'undefined') { { { { {}}}}
        // React Native AsyncStorage - sauvegarde asynchrone;
        try {
          const AsyncStorage = require('@react-native-async-storage/async-storage').default;
          AsyncStorage.setItem(storageKey, deviceId);
        } catch(error) {
          console.error('Error saving device ID to AsyncStorage:', error);
        }
      }
    } catch(error) {
      console.error('Error saving device ID:', error);
    }
  }
  
  return deviceId;
};

// Exporter toutes les fonctionnalités;
export {
  syncManager,
  networkMonitor,
  createQueue,
  SyncActionType,
  SyncEvent,
  NetworkStatus,
  NetworkEvent,
  ConflictResolutionStrategies,
  selectiveBroadcast,
  createSelectiveBroadcastMiddleware;
};

// Types;
export type { SyncAction, SyncStatus } from './types';
// Synchronisation en temps réel;
export * from './types';
export * from './networkMonitor';
export * from './offlineQueue';
export * from './selectiveBroadcast';
export * from './initSyncMiddleware';
export * from './utils';

// Export par défaut de la configuration;
export { configureSyncMiddleware, initializeSynchronization } from './initSyncMiddleware';

// Hooks de synchronisation;
export * from './hooks';
