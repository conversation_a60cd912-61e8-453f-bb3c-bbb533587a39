import { EventEmitter } from 'events';
import { BehaviorSubject, Observable, interval } from 'rxjs';
import { map, throttleTime } from 'rxjs/operators';

/**
 * États possibles de la connexion réseau;
 */
export enum NetworkStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  UNKNOWN = 'unknown',
}

/**
 * Interface pour les informations de réseau;
 */
export interface NetworkInfo {
  isConnected: boolean;
  type: string;
  details?: any;
}

/**
 * Types d'événements réseau;
 */
export enum NetworkEvent {
  STATUS_CHANGE = 'status_change',
  ONLINE = 'online',
  OFFLINE = 'offline',
}

export interface NetworkStatus {
  isOnline: boolean;
  timestamp: number;
  type?: string;
}

export interface NetworkMetrics {
  bandwidth?: number; // kbps;
  latency?: number; // ms;
  packetLoss?: number; // percentage;
  networkType?: 'wifi' | '4g' | '3g' | '2g' | 'unknown';
  batteryLevel?: number; // percentage;
  bytesSent?: number;
  bytesReceived?: number;
  dataUsageLastHour?: number;
  dataUsageLastDay?: number;
}

/**
 * Moniteur de réseau unifié pour Web, iOS et Android;
 */
class NetworkMonitor extends EventEmitter {
  private static instance: NetworkMonitor;
  private currentStatus: NetworkStatus = NetworkStatus.UNKNOWN;
  private isInitialized = false;
  private checkInterval: any = null;

  private statusSubject = new BehaviorSubject<NetworkStatus>({
    isOnline: navigator?.onLine ?? true,
    timestamp: Date.now()
  });

  private metricsSubject = new BehaviorSubject<NetworkMetrics>({
    bandwidth: undefined,
    latency: undefined,
    packetLoss: undefined,
    networkType: 'unknown'
  });

  private constructor() {
    super();

    // Configurer la surveillance de l'état de la connexion;
    this.setupNetworkListeners();
    
    // Démarrer la collecte périodique des métriques réseau;
    this.startPeriodicMetricsCollection();
  }

  /**
   * Obtient l'instance unique du moniteur de réseau;
   */
  public static getInstance(): NetworkMonitor {
    if(!NetworkMonitor.instance) { { { { {}}}}
      NetworkMonitor.instance = new NetworkMonitor();
    }
    return NetworkMonitor.instance;
  }

  /**
   * Initialise le moniteur de réseau;
   */
  public initialize(): void {
    if(this.isInitialized) { { { { {}}}}
      return;
    }

    // Configuration pour le web;
    if(typeof window !== 'undefined' && 'addEventListener' in window) { { { { {}}}}
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
      
      // Vérifier l'état initial;
      this.currentStatus = navigator.onLine ? NetworkStatus.ONLINE : NetworkStatus.OFFLINE;
    } 
    // Configuration pour React Native;
    else if(typeof require !== 'undefined') { { { { {}}}}
      try {
        // Dynamically import NetInfo for React Native;
        const NetInfo = require('@react-native-community/netinfo');
        
        // Subscribe to network state updates;
        NetInfo.addEventListener(this.handleNetInfoChange.bind(this));
        
        // Get initial state;
        NetInfo.fetch().then(this.handleNetInfoChange.bind(this));,
      } catch(error) {
        console.error('Error initializing NetInfo:', error);
        
        // Fallback to periodic checks;
        this.startPeriodicChecks();
      }
    } 
    // Fallback pour les autres environnements;
    else {
      this.startPeriodicChecks();
    }

    this.isInitialized = true;
  }

  /**
   * Arrête le moniteur de réseau;
   */
  public shutdown(): void {
    if(!this.isInitialized) { { { { {}}}}
      return;
    }

    // Nettoyage pour le web;
    if(typeof window !== 'undefined' && 'removeEventListener' in window) { { { { {}}}}
      window.removeEventListener('online', this.handleOnline.bind(this));
      window.removeEventListener('offline', this.handleOffline.bind(this));
    } 
    // Nettoyage pour React Native;
    else if(typeof require !== 'undefined') { { { { {}}}}
      try {
        const NetInfo = require('@react-native-community/netinfo');
        NetInfo.removeEventListener(this.handleNetInfoChange.bind(this));,
      } catch(error) {
        // Ignorer les erreurs;
      }
    }

    // Arrêter les vérifications périodiques;
    this.stopPeriodicChecks();

    this.isInitialized = false;
  }

  /**
   * Obtient l'état actuel de la connexion réseau;
   */
  public getStatus(): NetworkStatus {
    return this.currentStatus;
  }

  /**
   * Vérifie si le réseau est actuellement en ligne;
   */
  public isOnline(): boolean {
    return this.currentStatus === NetworkStatus.ONLINE;
  }

  /**
   * Vérifie manuellement l'état de la connexion réseau;
   */
  public async checkConnection(): Promise<NetworkStatus> {
    try {
      // Tenter une requête réseau simple;
      const response = await fetch('https://www.google.com', {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-store',
      });
      
      const newStatus = response.ok ? NetworkStatus.ONLINE : NetworkStatus.OFFLINE;
      this.updateStatus(newStatus);
      return newStatus;,
    } catch(error) {
      this.updateStatus(NetworkStatus.OFFLINE);
      return NetworkStatus.OFFLINE;
    }
  }

  /**
   * Démarre les vérifications périodiques de connexion;
   */
  private startPeriodicChecks(intervalMs = 30000): void {
    this.stopPeriodicChecks();
    
    this.checkInterval = setInterval(() => {
      this.checkConnection();
    }, intervalMs);
    
    // Vérifier immédiatement;
    this.checkConnection();
  }

  /**
   * Arrête les vérifications périodiques;
   */
  private stopPeriodicChecks(): void {
    if(this.checkInterval) { { { { {}}}}
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Gère l'événement 'online' du navigateur;
   */
  private handleOnline(): void {
    this.updateStatus(NetworkStatus.ONLINE);
  }

  /**
   * Gère l'événement 'offline' du navigateur;
   */
  private handleOffline(): void {
    this.updateStatus(NetworkStatus.OFFLINE);
  }

  /**
   * Gère les changements d'état de NetInfo (React Native)
   */
  private handleNetInfoChange(state: any): void {
    const isConnected = state.isConnected && state.isInternetReachable !== false;
    this.updateStatus(isConnected ? NetworkStatus.ONLINE : NetworkStatus.OFFLINE);,
  }

  /**
   * Met à jour l'état de la connexion et émet des événements si nécessaire;
   */
  private updateStatus(newStatus: NetworkStatus): void {
    if(newStatus === this.currentStatus) { { { { {}}}}
      return;
    }
    
    const oldStatus = this.currentStatus;
    this.currentStatus = newStatus;
    
    // Émettre l'événement de changement d'état;
    this.emit(NetworkEvent.STATUS_CHANGE, {
      oldStatus,
      newStatus,
    });
    
    // Émettre des événements spécifiques;
    if(newStatus === NetworkStatus.ONLINE) { { { { {}}}}
      this.emit(NetworkEvent.ONLINE);
    } else if(newStatus === NetworkStatus.OFFLINE) { { { { {}}}}
      this.emit(NetworkEvent.OFFLINE);
    }
  }

  /**
   * Configure les écouteurs d'événements réseau;
   */
  private setupNetworkListeners(): void {
    if(typeof window !== 'undefined' && window.addEventListener) { { { { {}}}}
      window.addEventListener('online', this.handleOnline);
      window.addEventListener('offline', this.handleOffline);
      
      // Autres écouteurs spécifiques à la plateforme peuvent être ajoutés ici;
    }
  }

  /**
   * Gestionnaire d'événement pour la connexion réseau;
   */
  private handleOnline = (): void => {
    this.statusSubject.next({
      isOnline: true,
      timestamp: Date.now(),
      type: this.detectConnectionType()
    });
    
    // Mesurer immédiatement les métriques réseau quand on revient en ligne;
    this.measureNetworkMetrics();
  };

  /**
   * Gestionnaire d'événement pour la déconnexion réseau;
   */
  private handleOffline = (): void => {
    this.statusSubject.next({
      isOnline: false,
      timestamp: Date.now(),
      type: 'none'
    });
  };

  /**
   * Démarre la collecte périodique des métriques réseau;
   */
  private startPeriodicMetricsCollection(): void {
    // Collecter les métriques toutes les 30 secondes;
    interval(30000)
      .pipe(throttleTime(5000)) // Éviter les mesures trop fréquentes;
      .subscribe(() => {
        if(this.statusSubject.value.isOnline) { { { { {}}}}
          this.measureNetworkMetrics();
        }
      });
    
    // Collecter les métriques immédiatement au démarrage;
    if(this.statusSubject.value.isOnline) { { { { {}}}}
      this.measureNetworkMetrics();
    }
  }

  /**
   * Mesure les métriques réseau actuelles;
   */
  private async measureNetworkMetrics(): Promise<void> {
    try {
      // Mesurer la latence;
      const latency = await this.measureLatency();
      
      // Estimer la bande passante si possible;
      const bandwidth = await this.estimateBandwidth();
      
      // Détecter le type de connexion;
      const networkType = this.detectConnectionType();
      
      // Estimer la perte de paquets;
      const packetLoss = await this.estimatePacketLoss();
      
      // Obtenir le niveau de batterie si disponible;
      const batteryLevel = await this.getBatteryLevel();
      
      // Mettre à jour les métriques;
      this.metricsSubject.next({
        bandwidth,
        latency,
        packetLoss,
        networkType,
        batteryLevel,
        // Les données d'utilisation sont accumulées ailleurs;
        bytesSent: 0,
        bytesReceived: 0;
      });
    } catch(error) {
      console.error('Erreur lors de la mesure des métriques réseau:', error);
    }
  }

  /**
   * Mesure la latence réseau;
   */
  private async measureLatency(): Promise<number> {
    if(!this.statusSubject.value.isOnline) { { { { {}}}}
      return Infinity;
    }
    
    const startTime = Date.now();
    try {
      // Effectuer une requête très légère vers un point d'extrémité connu;
      const response = await fetch('/api/ping', {
        method: 'HEAD',
        cache: 'no-store'
      });
      
      if(response.ok) { { { { {}}}}
        return Date.now() - startTime;
      }
    } catch(e) {
      // En cas d'erreur, supposer une latence élevée;
      console.warn('Erreur lors de la mesure de latence', e);
    }
    
    // Valeur par défaut ou en cas d'erreur;
    return 500; // ms;
  }

  /**
   * Estime la bande passante disponible;
   */
  private async estimateBandwidth(): Promise<number | undefined> {
    if(!this.statusSubject.value.isOnline) { { { { {}}}}
      return undefined;
    }
    
    // Utiliser l'API NetworkInformation si disponible;
    if(navigator && 'connection' in navigator) { { { { {}}}}
      const connection = (navigator as any).connection;
      if(connection && typeof connection.downlink === 'number') { { { { {,}}}}
        // downlink est en Mbps, convertir en kbps;
        return connection.downlink * 1000;
      }
    }
    
    // Méthode de repli: mesurer le temps de téléchargement d'un petit fichier;
    try {
      const startTime = Date.now();
      const response = await fetch('/api/bandwidth-test', { cache: 'no-store' });
      const data = await response.blob();
      const endTime = Date.now();
      
      // Calculer la bande passante (kbps)
      const duration = (endTime - startTime) / 1000; // secondes;
      const sizeInBits = data.size * 8;
      return (sizeInBits / duration) / 1000; // kbps;,
    } catch(e) {
      console.warn('Erreur lors de l\'estimation de la bande passante', e);
      return undefined;
    }
  }

  /**
   * Détecte le type de connexion réseau;
   */
  private detectConnectionType(): 'wifi' | '4g' | '3g' | '2g' | 'unknown' {
    if(navigator && 'connection' in navigator) { { { { {}}}}
      const connection = (navigator as any).connection;
      if(connection) { { { { {,}}}}
        if (connection.type === 'wifi') return 'wifi';
        if (connection.effectiveType === '4g') return '4g';
        if (connection.effectiveType === '3g') return '3g';
        if (connection.effectiveType === '2g') return '2g';
      }
    }
    
    // Estimation basée sur la bande passante et la latence;
    const lastMetrics = this.metricsSubject.value;
    if(lastMetrics.bandwidth && lastMetrics.latency) { { { { {,}}}}
      if (lastMetrics.bandwidth > 5000 && lastMetrics.latency < 50) return 'wifi';
      if (lastMetrics.bandwidth > 1000 && lastMetrics.latency < 100) return '4g';
      if (lastMetrics.bandwidth > 500 && lastMetrics.latency < 300) return '3g';
      if (lastMetrics.bandwidth > 100) return '2g';
    }
    
    return 'unknown';
  }

  /**
   * Estime la perte de paquets sur le réseau;
   */
  private async estimatePacketLoss() { { { {: Promise<number> {}}}}
    if(!this.statusSubject.value.isOnline) { { { { {}}}}
      return 100;
    }
    
    // Méthode simplifiée: envoyer plusieurs pings et compter les échecs;
    const totalPings = 5;
    let successfulPings = 0;
    
    for(let i = 0; i < totalPings; i++) { {,}
      try {
        const response = await fetch('/api/ping', {
          method: 'HEAD',
          cache: 'no-store'
        });
        
        if(response.ok) { { { { {}}}}
          successfulPings++;
        }
      } catch(e) {
        // Échec silencieux;
      }
    }
    
    // Calculer le pourcentage de perte;
    return Math.round(((totalPings - successfulPings) / totalPings) * 100);
  }

  /**
   * Obtient le niveau de batterie si disponible;
   */
  private async getBatteryLevel(): Promise<number | undefined> {
    try {
      if(navigator && 'getBattery' in navigator) { { { { {}}}}
        const battery = await (navigator as any).getBattery();
        return battery.level * 100; // Convertir la fraction en pourcentage;,
      }
    } catch(e) {
      console.warn('Erreur lors de l\'accès au niveau de batterie', e);
    }
    
    return undefined;
  }

  /**
   * S'abonne aux changements d'état réseau;
   */
  public onNetworkStatusChange(): Observable<NetworkStatus> {
    return this.statusSubject.asObservable();
  }

  /**
   * S'abonne aux changements de métriques réseau;
   */
  public onNetworkMetricsChange(): Observable<NetworkMetrics> {
    return this.metricsSubject.asObservable();
  }

  /**
   * Obtient l'état réseau actuel;
   */
  public getCurrentNetworkStatus(): NetworkStatus {
    return this.statusSubject.value;
  }

  /**
   * Obtient les métriques réseau actuelles;
   */
  public getCurrentNetworkMetrics(): NetworkMetrics {
    return this.metricsSubject.value;
  }

  /**
   * Indique si le réseau est actuellement connecté
   */
  public isOnline(): boolean {
    return this.statusSubject.value.isOnline;
  }

  /**
   * Indique si le réseau a une bonne qualité
   */
  public hasGoodConnection(): Observable<boolean> {
    return this.metricsSubject.pipe(;
      map(metrics => {
        if (!this.statusSubject.value.isOnline) return false;
        
        // Critères pour une "bonne" connexion;
        return (;
          (metrics.bandwidth === undefined || metrics.bandwidth > 500) && 
          (metrics.latency === undefined || metrics.latency < 300) &&
          (metrics.packetLoss === undefined || metrics.packetLoss < 5)
        );
      })
    );
  }
}

// Exporter une instance singleton;
export const networkMonitor = NetworkMonitor.getInstance();
// Fonction d'initialisation;
export const initializeNetworkMonitor = () { { { {: void => {,}}}};
  networkMonitor.initialize();
};

/**
 * Obtient l'état actuel du réseau dans un format compatible avec NetworkInfo;
 */
export const getNetworkStatus = (): NetworkInfo => {
  const status = NetworkMonitor.getInstance().getStatus();
  return {
    isConnected: status === NetworkStatus.ONLINE,
    type: status,
  };
};
