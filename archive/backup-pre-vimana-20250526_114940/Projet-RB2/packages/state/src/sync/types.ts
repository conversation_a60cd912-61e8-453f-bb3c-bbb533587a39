/**
 * Types d'actions de synchronisation;
 */
export enum SyncActionType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
}

/**
 * Types d'événements de synchronisation en temps réel;
 */
export enum RealtimeSyncEventType {
  DATA_UPDATED = 'data_updated',
  DATA_DELETED = 'data_deleted',
  SYNC_REQUIRED = 'sync_required',
  SYNC_COMPLETE = 'sync_complete',
  CONFLICT_DETECTED = 'conflict_detected',
  CONFLICT_RESOLVED = 'conflict_resolved',
  ERROR = 'error',
  ACTION = 'action' // Nouvel événement pour les actions Redux;,
}

/**
 * Interface pour une action de synchronisation;
 */
export interface SyncAction {
  id: string;
  entityType: string;
  entityId: string;
  actionType: SyncActionType;
  data: any;
  timestamp: number;
  userId?: string;
  deviceId?: string;
  retryCount?: number;
  priority?: number;
}

/**
 * Interface pour un payload de synchronisation en temps réel;
 */
export interface SyncPayload {
  id: string;
  type: string; // Type d'action Redux;
  timestamp: number;
  data: any; // Données de l'action;
  state?: any; // État pertinent;
  origin: 'client' | 'server';
  version?: number; // Version pour la gestion des conflits;
}

/**
 * Interface pour un événement de synchronisation en temps réel;
 */
export interface RealtimeSyncEvent {
  type: RealtimeSyncEventType | 'action'; // Ajouter le support pour les actions;
  payload: SyncPayload | any; // Peut être soit un SyncPayload soit une action Redux directement;
  meta?: {
    deviceId?: string;
    sessionId?: string;
    userId?: string;
    broadcast?: BroadcastEventMetadata; // Métadonnées pour la diffusion sélective;
    [key: string]: any;
  };
}

/**
 * Types de destinataires pour la diffusion sélective;
 */
export enum BroadcastRecipientType {
  USER = 'user',
  ROLE = 'role',
  GROUP = 'group',
  DEVICE = 'device',
  RESOURCE_SUBSCRIBERS = 'resource',
  ALL = 'all',
}

/**
 * Interface pour un destinataire de diffusion;
 */
export interface BroadcastRecipient {
  type: BroadcastRecipientType;
  id: string;
}

/**
 * Métadonnées pour la diffusion sélective d'un événement;
 */
export interface BroadcastEventMetadata {
  id: string; // ID unique de la diffusion;
  recipients: BroadcastRecipient[]; // Liste des destinataires;
  priority: number; // Priorité (0-10, 10 étant la plus élevée)
  timestamp: number; // Horodatage d'envoi;
  metadata?: Record<string, any>; // Métadonnées personnalisées;
}

/**
 * Types pour le système de synchronisation multi-device et cross-platform;
 */

/**
 * Représente les différents statuts possibles d'une synchronisation;
 */
export enum SyncStatus {
  /** Le système est en train de se connecter au serveur de synchronisation */
  CONNECTING = 'connecting',
  /** Connecté et synchronisé avec les dernières données */
  SYNCED = 'synced',
  /** En train de synchroniser des données (envoi/réception) */
  SYNCING = 'syncing',
  /** Des modifications locales sont en attente d'être synchronisées */
  PENDING = 'pending',
  /** Actuellement hors ligne, les modifications seront synchronisées plus tard */
  OFFLINE = 'offline',
  /** Un conflit de synchronisation a été détecté et nécessite une résolution */
  CONFLICT = 'conflict',
  /** Une erreur s'est produite pendant la synchronisation */
  ERROR = 'error',
}

/**
 * Types d'appareils supportés par le système;
 */
export enum DeviceType {
  MOBILE = 'mobile',
  TABLET = 'tablet',
  WEB = 'web',
  DESKTOP = 'desktop',
}

/**
 * Informations sur un appareil connecté
 */
export interface Device {
  /** Identifiant unique de l'appareil */
  id: string;
  /** Nom de l'appareil (fourni par l'utilisateur ou généré) */
  name: string;
  /** Type d'appareil */
  type: DeviceType;
  /** Plateforme spécifique (ios, android, windows, macos, linux, browser, etc.) */
  platform: string;
  /** Dernière fois que l'appareil a été vu en ligne */
  lastSeen: string;
  /** Version de l'application sur cet appareil */
  appVersion?: string;
  /** Si cet appareil est l'appareil actuel */
  isCurrent: boolean;
}

/**
 * Représente l'état d'une connexion réseau;
 */
export type NetworkCondition = 'excellent' | 'good' | 'fair' | 'poor' | 'offline';
/**
 * Configuration des délais pour les différentes conditions réseau (en ms)
 */
export const NETWORK_DELAYS: Record<NetworkCondition, number> = {
  excellent: 50,
  good: 150,
  fair: 500,
  poor: 2000,
  offline: Infinity;
};

/**
 * Interface pour un conflit de synchronisation;
 */
export interface SyncConflict<T = any> {
  /** Identifiant unique de l'élément en conflit */
  itemId: string;
  /** Version locale de l'élément */
  localVersion: T;
  /** Version distante de l'élément */
  remoteVersion: T;
  /** Version de base commune (avant les modifications conflictuelles) */
  baseVersion: T;
  /** Horodatage de la modification locale */
  localTimestamp: string;
  /** Horodatage de la modification distante */
  remoteTimestamp: string;
  /** Identifiant de l'appareil distant qui a créé le conflit */
  remoteDeviceId: string;
  /** Type d'appareil distant */
  remoteDeviceType?: DeviceType;
}

/**
 * Options disponibles pour résoudre un conflit;
 */
export enum ConflictResolutionStrategy {
  /** Conserver la version locale */
  KEEP_LOCAL = 'keep_local',
  /** Adopter la version distante */
  KEEP_REMOTE = 'keep_remote',
  /** Fusionner les deux versions */
  MERGE = 'merge',
  /** Créer une nouvelle version à partir des deux */
  CUSTOM = 'custom',
}

/**
 * Statistiques de synchronisation;
 */
export interface SyncStats {
  /** Nombre total d'actions synchronisées */
  totalSyncedActions: number;
  /** Nombre d'actions en attente de synchronisation */
  pendingActions: number;
  /** Nombre de conflits résolus */
  resolvedConflicts: number;
  /** Nombre d'octets envoyés */
  bytesSent: number;
  /** Nombre d'octets reçus */
  bytesReceived: number;
  /** Temps moyen de synchronisation (ms) */
  averageSyncTime: number;
  /** Dernière synchronisation réussie */
  lastSuccessfulSync: string | null;
  /** Nombre d'erreurs de synchronisation */
  errors: number;
}

/**
 * Paramètres de configuration pour la bande passante adaptative;
 */
export interface AdaptiveBandwidthConfig {
  /** Activer ou désactiver la gestion adaptative de la bande passante */
  enabled: boolean;
  /** Taille maximale des paquets en mode économie (octets) */
  maxPacketSizeEconomy: number;
  /** Taille maximale des paquets en mode normal (octets) */
  maxPacketSizeNormal: number;
  /** Seuil pour passer en mode économie de données (Kbps) */
  economyModeThreshold: number;
  /** Intervalle minimal entre les synchronisations en mode économie (ms) */
  minSyncIntervalEconomy: number;
  /** Intervalle minimal entre les synchronisations en mode normal (ms) */
  minSyncIntervalNormal: number;
  /** Activer la compression des données */
  enableCompression: boolean;
  /** Priorité des actions de synchronisation (ordre d'envoi) */
  actionPriorities: { [actionType: string]: number };
}

/**
 * Profil de connexion prédéfini;
 */
export interface NetworkProfile {
  /** Nom du profil */
  name: string;
  /** Description du profil */
  description: string;
  /** Configuration de bande passante associée */
  config: AdaptiveBandwidthConfig;
}

/**
 * Type d'événement de synchronisation;
 */
export enum SyncEvent {
  SYNC_STARTED = 'sync_started',
  SYNC_COMPLETED = 'sync_completed',
  SYNC_FAILED = 'sync_failed',
  CONFLICT_DETECTED = 'conflict_detected',
  CONFLICT_RESOLVED = 'conflict_resolved',
  BROADCAST_SENT = 'broadcast_sent',
  BROADCAST_RECEIVED = 'broadcast_received',
}

/**
 * Interface pour un écouteur d'événements de synchronisation;
 */
export interface SyncEventListener {
  (event: SyncEvent, data?: any): void;
}

/**
 * Interface pour la file d'attente de synchronisation hors ligne;
 */
export interface OfflineQueueItem {
  id: string;
  action: any; // Action Redux;
  timestamp: number;
  retryCount: number;
  priority: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

/**
 * Interface pour le service RealtimeSync;
 */
export interface RealtimeSync {
  connect(url: string): Promise<void>;
  disconnect(): void;
  sendEvent(event: RealtimeSyncEvent): Promise<void>;
  onEvent(type: RealtimeSyncEventType, handler: (event: RealtimeSyncEvent) => void): void;
  offEvent(type: RealtimeSyncEventType, handler: (event: RealtimeSyncEvent) => void): void;
  isConnected(): boolean;
}

/**
 * Configuration pour le système de diffusion sélective;
 */
export interface SelectiveBroadcastConfig {
  enableBatching?: boolean;       // Activer le regroupement des diffusions;
  maxBatchSize?: number;          // Taille maximale d'un lot;
  batchInterval?: number;         // Intervalle entre les lots;
  defaultDelay?: number;          // Délai par défaut;
  defaultPriority?: number;       // Priorité par défaut;
  logger?: (message: string, data?: any) => void; // Logger personnalisé
}

/**
 * Options pour la diffusion d'une action;
 */
export interface SelectiveBroadcastOptions {
  recipients: BroadcastRecipient[];  // Destinataires;
  priority?: number;                 // Priorité
  delay?: number;                    // Délai avant envoi;
  id?: string;                       // ID personnalisé
  noBatch?: boolean;                 // Ne pas regrouper;
  metadata?: Record<string, any>;    // Métadonnées;
}

/**
 * Priorités de synchronisation;
 */
export enum SyncPriority {
  VERY_LOW = 0,
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4;,
}

/**
 * Catégories d'actions pour la prioritisation;
 */
export type ActionCategory = 'user' | 'auth' | 'data' | 'ui' | 'analytics' | 'logs' | 'system';