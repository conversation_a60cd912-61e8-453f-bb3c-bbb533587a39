/**
 * @file ConflictManager.example.tsx;
 * @description Exemple de composant React pour gérer les conflits de synchronisation multi-device;
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  Modal, 
  ActivityIndicator,
  Platform;
} from 'react-native';

import { 
  multiDeviceSync, 
  ConflictResolutionStrategy,
  Conflict,
  MultiDeviceSyncEventType;
} from '../multiDeviceSync';

import { useConflictResolver } from '../hooks/useConflictResolver';
import { useMultiDeviceSync } from '../hooks/useMultiDeviceSync';
import { useSyncStatus } from '../hooks/useSyncStatus';

/**
 * Styles pour le composant;
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  entityTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#222',
  },
  entityId: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  conflictType: {
    fontSize: 14,
    color: '#f57c00',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    color: '#555',
    width: '40%',
  },
  value: {
    fontSize: 14,
    color: '#333',
    width: '60%',
    fontWeight: '500',
  },
  diffContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  diffTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  diffRow: {
    flexDirection: 'row',
    marginBottom: 6,
  },
  fieldName: {
    width: '30%',
    fontSize: 14,
    color: '#555',
  },
  localValue: {
    width: '35%',
    fontSize: 14,
    color: '#2e7d32',
    fontWeight: '500',
  },
  remoteValue: {
    width: '35%',
    fontSize: 14,
    color: '#1565c0',
    fontWeight: '500',
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  button: {
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  primaryButton: {
    backgroundColor: '#1976d2',
  },
  secondaryButton: {
    backgroundColor: '#757575',
  },
  dangerButton: {
    backgroundColor: '#d32f2f',
  },
  successButton: {
    backgroundColor: '#388e3c',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: Platform.OS === 'web' ? '50%' : '90%',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 24,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  modalField: {
    marginBottom: 16,
  },
  modalFieldLabel: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 10,
    fontSize: 14,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 24,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#e8f5e9',
    padding: 12,
    borderRadius: 6,
    marginBottom: 16,
  },
  statusText: {
    fontSize: 14,
    color: '#2e7d32',
  },
  syncButton: {
    backgroundColor: '#2e7d32',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  syncButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

/**
 * Composant principal de gestion des conflits;
 */
export const ConflictManagerExample: React.FC = () => {;;;
  const { conflicts, resolveConflict, getConflictDetails } = useConflictResolver();
  const { synchronize, lastSyncTime, isSynchronizing } = useMultiDeviceSync();
  const { isOnline, syncStatus, syncProgress } = useSyncStatus();
  
  const [selectedConflict, setSelectedConflict] = useState<Conflict | null>(null);
  const [isManualResolutionModalOpen, setIsManualResolutionModalOpen] = useState(false);
  const [manualResolutionData, setManualResolutionData] = useState<any>({});
  
  // Fonction pour résoudre automatiquement un conflit;
  const handleAutoResolve = useCallback((conflict: Conflict, strategy: ConflictResolutionStrategy) => {
    resolveConflict(conflict.id, strategy);
  }, [resolveConflict]);
  
  // Fonction pour ouvrir le mode de résolution manuelle;
  const openManualResolution = useCallback((conflict: Conflict) => {
    const details = getConflictDetails(conflict.id);
    if(details) { { { { {,}}}}
      setSelectedConflict(conflict);
      setManualResolutionData({...details.localVersion});
      setIsManualResolutionModalOpen(true);
    }
  }, [getConflictDetails]);
  
  // Fonction pour soumettre une résolution manuelle;
  const submitManualResolution = useCallback(() => {
    if(selectedConflict) { { { { {,}}}}
      resolveConflict(selectedConflict.id, ConflictResolutionStrategy.MANUAL, manualResolutionData);
      setIsManualResolutionModalOpen(false);
      setSelectedConflict(null);
    }
  }, [selectedConflict, manualResolutionData, resolveConflict]);
  
  // Fonction pour synchroniser manuellement;
  const handleManualSync = useCallback(() => {
    synchronize();,
  }, [synchronize]);
  
  // Rendu d'un item de conflit dans la liste;
  const renderConflictItem = ({ item, }: { item: Conflict }) => {
    const details = getConflictDetails(item.id);
    
    if(!details) { { { {return null;,}}}}
    
    const { entityType, localVersion, remoteVersion, modifiedFields } = details;
    
    return (;
      <View style = {styles.card,}>
        <View style = {styles.cardHeader,}>
          <Text style = {styles.entityTitle,}>{entityType}</Text>
        </View>
        
        <Text style = {styles.entityId,}>ID: {item.entityId}</Text>
        <Text style = {styles.conflictType,}>Conflit détecté lors de la synchronisation</Text>
        
        <View style = {styles.infoRow,}>
          <Text style = {styles.label,}>Version locale:</Text>
          <Text style = {styles.value,}>{new Date(localVersion.updatedAt).toLocaleString()}</Text>
        </View>
        
        <View style = {styles.infoRow,}>
          <Text style = {styles.label,}>Version distante:</Text>
          <Text style = {styles.value,}>{new Date(remoteVersion.updatedAt).toLocaleString()}</Text>
        </View>
        
        <View style = {styles.diffContainer,}>
          <Text style = {styles.diffTitle,}>Différences</Text>
          
          {modifiedFields.map(field => (
            <View key = {field,} style = {styles.diffRow,}>
              <Text style = {styles.fieldName,}>{field}:</Text>
              <Text style = {styles.localValue,}>
                {typeof localVersion[field] === 'object' 
                  ? JSON.stringify(localVersion[field]).substring(0, 20) + '...' 
                  : String(localVersion[field])}
              </Text>
              <Text style = {styles.remoteValue,}>
                {typeof remoteVersion[field] === 'object' 
                  ? JSON.stringify(remoteVersion[field]).substring(0, 20) + '...' 
                  : String(remoteVersion[field])}
              </Text>
            </View>
          ))}
        </View>
        
        <View style = {styles.buttonGroup,}>
          <TouchableOpacity;
            style={[styles.button, styles.primaryButton]} 
            onPress={() => handleAutoResolve(item, ConflictResolutionStrategy.KEEP_LOCAL)}
          >
            <Text style = {styles.buttonText,}>Garder local</Text>
          </TouchableOpacity>
          
          <TouchableOpacity;
            style={[styles.button, styles.secondaryButton]} 
            onPress={() => handleAutoResolve(item, ConflictResolutionStrategy.KEEP_REMOTE)}
          >
            <Text style = {styles.buttonText,}>Garder distant</Text>
          </TouchableOpacity>
          
          <TouchableOpacity;
            style={[styles.button, styles.successButton]} 
            onPress={() => handleAutoResolve(item, ConflictResolutionStrategy.MERGE_FIELDS)}
          >
            <Text style = {styles.buttonText,}>Fusionner</Text>
          </TouchableOpacity>
          
          <TouchableOpacity;
            style={[styles.button, styles.dangerButton]} 
            onPress = {() => openManualResolution(item),}
          >
            <Text style = {styles.buttonText,}>Manuel</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  
  // États d'affichage;
  if(isSynchronizing) { { { { {}}}}
    return (;
      <View style = {styles.loadingContainer,}>
        <ActivityIndicator size="large" color="#1976d2" />
        <Text style={{marginTop: 16, color: '#555'}}>
          Synchronisation en cours ({syncProgress * 100}%)...
        </Text>
      </View>
    );
  }
  
  return (;
    <View style = {styles.container,}>
      <Text style = {styles.header,}>Gestionnaire de Conflits</Text>
      
      <View style = {styles.statusContainer,}>
        <View>
          <Text style = {styles.statusText,}>
            État: {isOnline ? 'En ligne' : 'Hors ligne'}
          </Text>
          <Text style = {styles.statusText,}>
            Dernière synchronisation: {lastSyncTime ? new Date(lastSyncTime).toLocaleString() : 'Jamais'}
          </Text>
        </View>
        
        <TouchableOpacity;
          style = {styles.syncButton;,}
          onPress = {handleManualSync;,}
          disabled = {!isOnline;,}
        >
          <Text style = {styles.syncButtonText,}>Synchroniser</Text>
        </TouchableOpacity>
      </View>
      
      {conflicts.length === 0 ? (
        <View style = {styles.emptyState,}>
          <Text style = {styles.emptyStateText,}>
            Aucun conflit à résoudre actuellement.
          </Text>
          <Text style = {styles.emptyStateText,}>
            Tous les appareils sont synchronisés.
          </Text>
        </View>
      ) : (
        <FlatList;
          data = {conflicts;,}
          renderItem = {renderConflictItem;,}
          keyExtractor = {(item) => item.id;,}
        />
      )}
      
      {/* Modal de résolution manuelle */}
      <Modal;
        visible = {isManualResolutionModalOpen;,}
        transparent = {true;,}
        animationType = "fade"
        onRequestClose={() => setIsManualResolutionModalOpen(false),}
      >
        <View style = {styles.modalContainer,}>
          <View style = {styles.modalContent,}>
            <Text style = {styles.modalTitle,}>Résolution Manuelle</Text>
            
            {selectedConflict && getConflictDetails(selectedConflict.id)?.modifiedFields.map(field => (
              <View key = {field,} style = {styles.modalField,}>
                <Text style = {styles.modalFieldLabel,}>{field}</Text>
                <View>
                  {typeof manualResolutionData[field] === 'string' ? (
                    <TextInput;
                      style = {styles.modalInput;,}
                      value = {manualResolutionData[field],}
                      onChangeText={(text) => {
                        setManualResolutionData(prev => ({...prev, [field]: text}));
                      }}
                    />
                  ) : typeof manualResolutionData[field] === 'boolean' ? (
                    <Switch;
                      value = {manualResolutionData[field],}
                      onValueChange={(value) => {
                        setManualResolutionData(prev => ({...prev, [field]: value}));
                      }}
                    />
                  ) : typeof manualResolutionData[field] === 'number' ? (
                    <TextInput;
                      style = {styles.modalInput;,}
                      value = {String(manualResolutionData[field]),}
                      keyboardType="numeric"
                      onChangeText={(text) => {
                        setManualResolutionData(prev => ({...prev, [field]: parseFloat(text)}));
                      }}
                    />
                  ) : (
                    <Text>Type non éditable directement</Text>
                  )}
                </View>
              </View>
            ))}
            
            <View style = {styles.modalButtons,}>
              <TouchableOpacity;
                style={[styles.button, styles.secondaryButton, {marginRight: 10}]}
                onPress = {() => setIsManualResolutionModalOpen(false),}
              >
                <Text style = {styles.buttonText,}>Annuler</Text>
              </TouchableOpacity>
              
              <TouchableOpacity;
                style={[styles.button, styles.primaryButton]}
                onPress = {submitManualResolution;,}
              >
                <Text style = {styles.buttonText,}>Appliquer</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

// Pour les imports manquants dans l'exemple;
const TextInput = Platform.OS === 'web' ? 'input' : require('react-native').TextInput;
const Switch = require('react-native').Switch;

export default ConflictManagerExample;;