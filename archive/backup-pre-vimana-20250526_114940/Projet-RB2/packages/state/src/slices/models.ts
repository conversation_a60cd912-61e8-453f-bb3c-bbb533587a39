/**
 * Models for state slices;
 * 
 * Ce fichier contient les interfaces pour les modèles de données utilisés;
 * dans les slices Redux.
 */

/**
 * Interface de base pour toutes les entités avec un ID;
 */
export interface BaseEntity {
  id: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * État de chargement et d'erreur pour les opérations asynchrones;
 */
export interface AsyncState {
  loading: boolean;
  error: string | null;
}

/**
 * État de base pour les slices qui gèrent des collections d'entités;
 */
export interface EntityState<T extends BaseEntity> extends AsyncState {
  data: Record<string, T>;
  ids: string[];
  selected: string | null;
}

/**
 * Modèle d'utilisateur;
 */
export interface User extends BaseEntity {
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  avatar?: string;
  preferences?: Record<string, any>;
}

/**
 * État d'authentification;
 */
export interface AuthState extends AsyncState {
  isAuthenticated: boolean;
  token: string | null;
  refreshToken: string | null;
  user: User | null;
  expiresAt: number | null;
}

/**
 * État de gestion des utilisateurs;
 */
export interface UsersState extends EntityState<User> {
  // Propriétés spécifiques à la gestion des utilisateurs;
  filters?: {
    role?: string;
    search?: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

/**
 * Préférences de l'application;
 */
export interface AppPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: boolean;
  fontSize: 'small' | 'medium' | 'large';
}

/**
 * État général de l'application;
 */
export interface AppState extends AsyncState {
  initialized: boolean;
  online: boolean;
  lastSynced: number | null;
  preferences: AppPreferences;
}

/**
 * Interface pour la réponse d'authentification;
 */
export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: User;
  expiresIn?: number;
}

/**
 * Informations d'identification pour la connexion;
 */
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Données d'inscription;
 */
export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword?: string;
} 