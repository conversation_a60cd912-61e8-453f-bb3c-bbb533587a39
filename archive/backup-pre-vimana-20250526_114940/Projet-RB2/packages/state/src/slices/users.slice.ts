import { createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { usersService } from '@projet-rb2/api';
import { User, UsersState } from './models';
import { createEntitySlice, createEntityState, handleAsyncThunk } from './createStateSlice';

// État initial;
const initialState: UsersState = {
  ...createEntityState<User>(),
  filters: {
    role: undefined,
    search: undefined;
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0;
  }
};

// Thunks (actions asynchrones)

/**
 * Thunk pour récupérer la liste des utilisateurs (admin)
 */
export const fetchUsers = createAsyncThunk(;
  'users/fetchUsers',
  async ({ page = 1, limit = 10, }: { page?: number; limit?: number }, { rejectWithValue, getState }) => {
    try {
      // Récupérer les filtres actuels;
      const state = getState() as { users: UsersState, };
      const { role, search } = state.users.filters || {};
      
      // Appeler le service avec les paramètres de pagination et de filtre;
      const response = await usersService.getUsers(page, limit);
      
      return {
        users: response.users,
        pagination: {
          page: response.page,
          limit: response.limit,
          total: response.total;
        }
      };
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de récupération des utilisateurs');
    }
  }
);

/**
 * Thunk pour récupérer un utilisateur par son ID;
 */
export const fetchUserById = createAsyncThunk(;
  'users/fetchUserById',
  async (userId: string, { rejectWithValue }) => {
    try {
      const user = await usersService.getUserById(userId);
      return user;,
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de récupération de l\'utilisateur');
    }
  }
);

/**
 * Thunk pour mettre à jour un utilisateur;
 */
export const updateUser = createAsyncThunk(;
  'users/updateUser',
  async ({ userId, data }: { userId: string; data: Partial<User> }, { rejectWithValue }) => {
    try {
      // Si c'est l'utilisateur actuel, utiliser updateProfile;
      if(userId === 'me') { { { { {}}}}
        const user = await usersService.updateProfile(data);
        return user;,
      } else {
        // Dans un cas réel, il faudrait un endpoint pour mettre à jour un utilisateur quelconque;
        // C'est juste pour l'exemple;
        const user = await usersService.getUserById(userId);
        return { ...user, ...data };
      }
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de mise à jour de l\'utilisateur');
    }
  }
);

/**
 * Thunk pour récupérer les préférences d'un utilisateur;
 */
export const fetchUserPreferences = createAsyncThunk(;
  'users/fetchUserPreferences',
  async (_, { rejectWithValue }) => {
    try {
      const preferences = await usersService.getUserPreferences();
      return preferences;,
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de récupération des préférences');
    }
  }
);

/**
 * Thunk pour mettre à jour les préférences d'un utilisateur;
 */
export const updateUserPreferences = createAsyncThunk(;
  'users/updateUserPreferences',
  async (preferences: Record<string, any>, { rejectWithValue }) => {
    try {
      const updatedPreferences = await usersService.updateUserPreferences(preferences);
      return updatedPreferences;,
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de mise à jour des préférences');
    }
  }
);

// Création du slice;
const usersSlice = createEntitySlice<User, any, 'users'>({
  name: 'users',
  initialState,
  reducers: {
    // Définir les filtres;
    setFilters: (state, action: PayloadAction<Partial<UsersState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // Réinitialiser les filtres;
    resetFilters: (state) => {
      state.filters = {
        role: undefined,
        search: undefined;
      };
    },
    
    // Définir la pagination;
    setPagination: (state, action: PayloadAction<Partial<UsersState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    }
  },
  extraReducers: (builder) => {
    // Récupération de la liste des utilisateurs;
    handleAsyncThunk(builder, fetchUsers);
    builder.addCase(fetchUsers.fulfilled, (state, action) => {
      const { users, pagination } = action.payload;
      
      // Ajouter les utilisateurs;
      state.data = users.reduce((acc, user) => {
        acc[user.id] = user;
        return acc;
      }, {} as Record<string, User>);
      
      state.ids = users.map(user => user.id);
      
      // Mettre à jour la pagination;
      state.pagination = pagination;
    });
    
    // Récupération d'un utilisateur par ID;
    handleAsyncThunk(builder, fetchUserById);
    builder.addCase(fetchUserById.fulfilled, (state, action) => {
      const user = action.payload;
      state.data[user.id] = user;
      
      if (!state.ids.includes(user.id)) { { { { {,}}}}
        state.ids.push(user.id);
      }
      
      state.selected = user.id;
    });
    
    // Mise à jour d'un utilisateur;
    handleAsyncThunk(builder, updateUser);
    builder.addCase(updateUser.fulfilled, (state, action) => {
      const user = action.payload;
      
      // Mettre à jour l'utilisateur dans l'état;
      state.data[user.id] = user;
      
      if (!state.ids.includes(user.id)) { { { { {,}}}}
        state.ids.push(user.id);
      }
    });
    
    // Récupération des préférences;
    handleAsyncThunk(builder, fetchUserPreferences);
    builder.addCase(fetchUserPreferences.fulfilled, (state, action) => {
      // Dans un cas réel, il faudrait mettre à jour l'utilisateur actuel;
      // Pour l'exemple, on ne fait rien de spécial;
    });
    
    // Mise à jour des préférences;
    handleAsyncThunk(builder, updateUserPreferences);
    builder.addCase(updateUserPreferences.fulfilled, (state, action) => {
      // Dans un cas réel, il faudrait mettre à jour l'utilisateur actuel;
      // Pour l'exemple, on ne fait rien de spécial;
    });
  }
});

// Exporter les actions;
export const { ;
  addOne, 
  addMany, 
  updateOne, 
  removeOne, 
  selectOne, 
  reset,
  setFilters,
  resetFilters,
  setPagination;
} = usersSlice.actions;

// Selectors;
export const selectUsers = (state: { users: UsersState, }) => state.users;
export const selectAllUsers = (state: { users: UsersState, }) => Object.values(state.users.data);
export const selectUserById = (state: { users: UsersState, }, id: string) => state.users.data[id];
export const selectSelectedUser = (state: { users: UsersState, }) => {
  const selectedId = state.users.selected;
  return selectedId ? state.users.data[selectedId] : null;,
};
export const selectUsersLoading = (state: { users: UsersState, }) => state.users.loading;
export const selectUsersError = (state: { users: UsersState, }) => state.users.error;
export const selectUsersPagination = (state: { users: UsersState, }) => state.users.pagination;
export const selectUsersFilters = (state: { users: UsersState, }) => state.users.filters;
// Exporter le reducer;
export default usersSlice.reducer;