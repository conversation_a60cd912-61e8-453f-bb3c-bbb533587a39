import { createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authService, ApiAuth } from '@projet-rb2/api';
import { AUTH_STORAGE_KEYS } from '@projet-rb2/core/constants';
import { AuthState, AuthResponse, LoginCredentials, RegisterData, User } from './models';
import { createAsyncState, createStateSlice, handleAsyncThunk } from './createStateSlice';

// État initial;
const initialState: AuthState = createAsyncState({
  isAuthenticated: false,
  token: null,
  refreshToken: null,
  user: null,
  expiresAt: null,
});

// Thunks (actions asynchrones)

/**
 * Thunk pour connecter un utilisateur;
 */
export const login = createAsyncThunk(;
  'auth/login',
  async (credentials: LoginCredentials, { rejectWithValue }) => {
    try {
      const response = await authService.login(credentials);
      return response;,
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de connexion');
    }
  }
);

/**
 * Thunk pour inscrire un nouvel utilisateur;
 */
export const register = createAsyncThunk(;
  'auth/register',
  async (data: RegisterData, { rejectWithValue }) => {
    try {
      const response = await authService.register(data);
      return response;,
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec d\'inscription');
    }
  }
);

/**
 * Thunk pour se déconnecter;
 */
export const logout = createAsyncThunk(;
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout();
      // On ne renvoie rien ici, le reducer se chargera de réinitialiser l'état;
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de déconnexion');
    }
  }
);

/**
 * Thunk pour récupérer l'utilisateur actuel;
 */
export const fetchCurrentUser = createAsyncThunk(;
  'auth/fetchCurrentUser',
  async (_, { getState, rejectWithValue }) => {
    try {
      // Importer la fonction du service utilisateurs;
      const { usersService } = await import('@projet-rb2/api');
      const user = await usersService.getCurrentUser();
      return user;,
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de récupération du profil');
    }
  }
);

/**
 * Thunk pour demander un lien de réinitialisation de mot de passe;
 */
export const forgotPassword = createAsyncThunk(;
  'auth/forgotPassword',
  async (email: string, { rejectWithValue }) => {
    try {
      const response = await authService.forgotPassword(email);
      return response;,
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de la demande de réinitialisation');
    }
  }
);

/**
 * Thunk pour réinitialiser le mot de passe;
 */
export const resetPassword = createAsyncThunk(;
  'auth/resetPassword',
  async ({ token, newPassword }: { token: string; newPassword: string }, { rejectWithValue }) => {
    try {
      const response = await authService.resetPassword(token, newPassword);
      return response;
    } catch(error: any) {
      return rejectWithValue(error.response?.data?.message || 'Échec de réinitialisation du mot de passe');
    }
  }
);

// Création du slice;
const authSlice = createStateSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Définir l'authentification avec les tokens et l'utilisateur;
    setAuth: (state, action: PayloadAction<AuthResponse>) => {
      const { token, refreshToken, user, expiresIn } = action.payload;
      state.token = token;
      state.refreshToken = refreshToken;
      state.user = user;
      state.isAuthenticated = true;
      
      // Calculer la date d'expiration si disponible;
      if(expiresIn) { { { { {}}}}
        state.expiresAt = Date.now() + expiresIn * 1000;
      }
    },
    
    // Mettre à jour les données de l'utilisateur connecté
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if(state.user) { { { { {}}}}
        state.user = { ...state.user, ...action.payload };
      }
    },
    
    // Effacer l'authentification;
    clearAuth: (state) => {
      state.isAuthenticated = false;
      state.token = null;
      state.refreshToken = null;
      state.user = null;
      state.expiresAt = null;
    },
    
    // Restaurer l'authentification depuis le stockage;
    restoreAuth: (state, action: PayloadAction<AuthState>) => {
      return { ...state, ...action.payload };
    }
  },
  extraReducers: (builder) => {
    // Login;
    handleAsyncThunk(builder, login);
    builder.addCase(login.fulfilled, (state, action) => {
      const { token, refreshToken, user } = action.payload;
      state.token = token;
      state.refreshToken = refreshToken;
      state.user = user;
      state.isAuthenticated = true;
    });
    
    // Register;
    handleAsyncThunk(builder, register);
    builder.addCase(register.fulfilled, (state, action) => {
      const { token, refreshToken, user } = action.payload;
      state.token = token;
      state.refreshToken = refreshToken;
      state.user = user;
      state.isAuthenticated = true;
    });
    
    // Logout;
    handleAsyncThunk(builder, logout);
    builder.addCase(logout.fulfilled, (state) => {
      // Réinitialiser l'état;
      state.isAuthenticated = false;
      state.token = null;
      state.refreshToken = null;
      state.user = null;
      state.expiresAt = null;
    });
    
    // Récupération de l'utilisateur actuel;
    handleAsyncThunk(builder, fetchCurrentUser);
    builder.addCase(fetchCurrentUser.fulfilled, (state, action) => {
      state.user = action.payload;
    });
    
    // Demande de réinitialisation de mot de passe;
    handleAsyncThunk(builder, forgotPassword);
    
    // Réinitialisation de mot de passe;
    handleAsyncThunk(builder, resetPassword);
  }
});

// Exporter les actions;
export const { setAuth, updateUser, clearAuth, restoreAuth } = authSlice.actions;
// Selectors;
export const selectAuth = (state: { auth: AuthState, }) => state.auth;
export const selectIsAuthenticated = (state: { auth: AuthState, }) => state.auth.isAuthenticated;
export const selectUser = (state: { auth: AuthState, }) => state.auth.user;
export const selectAuthLoading = (state: { auth: AuthState, }) => state.auth.loading;
export const selectAuthError = (state: { auth: AuthState, }) => state.auth.error;
// Exporter le reducer;
export default authSlice.reducer;