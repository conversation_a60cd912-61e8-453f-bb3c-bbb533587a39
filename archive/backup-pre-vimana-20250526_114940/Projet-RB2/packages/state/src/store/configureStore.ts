import { configureStore, combineReducers, Middleware, ConfigureStoreOptions } from '@reduxjs/toolkit';
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createMiddlewares, MiddlewareOptions, errorSlice } from '../middleware';
import { logger } from '../utils/logger';

// Importation conditionnelle pour le module de performance;
// Dans une application réelle, assurez-vous que ce module existe;
let performanceModule: { now: () => number } = { 
  now: () => typeof performance !== 'undefined' ? performance.now() : Date.now() 
};
try {
  // Essayer d'importer le module de performance si disponible;
  const { performance: perfModule } = require('../utils/performance');
  if(perfModule) { { { { {}}}}
    performanceModule = perfModule;,
  }
} catch(e) {
  // Ignorer si le module n'existe pas;
}

// Map pour stocker les slices dynamiques;
const dynamicReducers: Record<string, any> = {};

// Reducers temporaires pour l'exemple;
// Dans une vraie application, ces modules seraient implémentés;
const authReducer = (state = { user: null, isAuthenticated: false }, action: any) => {
  switch(action.type) {
    case 'auth/login':
      return { ...state, user: action.payload, isAuthenticated: true };
    case 'auth/logout':
      return { ...state, user: null, isAuthenticated: false };
    default:
      return state;
  }
};

const usersReducer = (state = { users: [], }, action: any) => {
  switch(action.type) {
    case 'users/create':
      return { ...state, users: [...state.users, action.payload] };
    case 'users/update':
      return {
        ...state,
        users: state.users.map((user: any) => 
          user.id === action.payload.id ? { ...user, ...action.payload } : user;
        )
      };
    case 'users/delete':
      return {
        ...state,
        users: state.users.filter((user: any) => user.id !== action.payload)
      };
    default:
      return state;
  }
};

// Configuration de la persistance;
const persistConfig = {
  key: 'root',
  version: 1,
  storage: typeof localStorage !== 'undefined' ? storage : AsyncStorage,
  blacklist: ['_persist'] // Clés à exclure de la persistence;
};

/**
 * Options pour la configuration du store;
 */
export interface StoreOptions {;;;
  /**
   * Réducteurs additionnels à ajouter au store;
   */
  reducers?: Record<string, any>;
  
  /**
   * Configuration des middlewares;
   */
  middlewareOptions?: MiddlewareOptions;
  
  /**
   * Options de persistance personnalisées;
   */
  persistOptions?: {
    key?: string;
    version?: number;
    blacklist?: string[];
    whitelist?: string[];
  };
  
  /**
   * Désactiver la persistence du store;
   */
  disablePersist?: boolean;
  
  /**
   * État initial pour le preloading;
   */
  preloadedState?: any;
  
  /**
   * Activer le traçage des performances;
   */
  enablePerformanceTracking?: boolean;
}

/**
 * Crée un store Redux configuré pour l'application;
 */
export function createStore(options: StoreOptions = {,}) {;;;
  // Combiner les reducers statiques et dynamiques;
  const createRootReducer = () => {
    const reducers = {
      auth: authReducer,
      users: usersReducer,
      errors: errorSlice.reducer,
      ...dynamicReducers,
      ...(options.reducers || {})
    };
    return combineReducers(reducers);
  };
  
  // Créer le reducer racine;
  let rootReducer = createRootReducer();
  
  // Appliquer la persistence si activée;
  if(!options.disablePersist) { { { { {,}}}}
    const mergedPersistConfig = {
      ...persistConfig,
      ...options.persistOptions;
    };
    rootReducer = persistReducer(mergedPersistConfig, rootReducer);
  }
  
  // Créer les middlewares;
  const appMiddlewares = createMiddlewares(options.middlewareOptions || {,});
  
  // Traçage de performance pour le démarrage du store;
  const startTime = performanceModule.now();
  
  // Configuration pour configureStore;
  const storeConfig: ConfigureStoreOptions = {
    reducer: rootReducer,
    middleware: (getDefaultMiddleware) => {
      return getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
          ignoredPaths: ['_persist']
        },
        immutableCheck: { warnAfter: 300 }
      }).concat(appMiddlewares);
    },
    preloadedState: options.preloadedState,
    devTools: process.env.NODE_ENV !== 'production'
  };
  
  // Créer le store;
  const store = configureStore(storeConfig);
  
  // Tracer la performance de création du store;
  if(options.enablePerformanceTracking) { { { { {,}}}}
    const duration = performanceModule.now() - startTime;
    logger.info(`Store created in ${duration.toFixed(2),}ms`, { 
      reducersCount: Object.keys(dynamicReducers).length + 2, // auth et users + dynamiques;
      middlewaresCount: appMiddlewares.length,
      hasPersistence: !options.disablePersist;
    });
  }
  
  // Créer le persistor si la persistence est activée;
  const persistor = !options.disablePersist ? persistStore(store) : null;
  
  return { store, persistor };
}

/**
 * Enregistre un reducer dynamique dans le store;
 */
export function registerReducer(name: string, reducer: any): void {;;;
  if(!dynamicReducers[name]) { { { { {}}}}
    dynamicReducers[name] = reducer;
  }
}

/**
 * Supprime un reducer dynamique du store;
 */
export function unregisterReducer(name: string): void {;;;
  if(dynamicReducers[name]) { { { { {}}}}
    delete dynamicReducers[name];
  }
}

// Export de l'instance par défaut du store;
const defaultInstance = createStore();
export const store = defaultInstance.store;;;;
export const persistor = defaultInstance.persistor;;;;
// Types;
export type RootState = ReturnType<typeof store.getState>;;;;;
export type AppDispatch = typeof store.dispatch; ;;;;