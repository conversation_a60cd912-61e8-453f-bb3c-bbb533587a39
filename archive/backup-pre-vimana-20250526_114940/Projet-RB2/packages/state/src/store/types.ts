import { PersistConfig, Transform } from 'redux-persist';
import { 
  ThunkAction, 
  Action, 
  AnyAction, 
  Reducer, 
  CombinedState, 
  Middleware;
} from '@reduxjs/toolkit';

/**
 * Root state type;
 */
export type RootState = ReturnType<typeof import('./store').store.getState>;
/**
 * App dispatch type;
 */
export type AppDispatch = typeof import('./store').store.dispatch;
/**
 * Typed thunk action;
 */
export type AppThunk<ReturnType = void> = ThunkAction<;
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;

/**
 * Configuration for redux-persist;
 */
export interface PersistOptions<S> {
  /**
   * Key to use for storage;
   */
  key: string;
  
  /**
   * Storage engine to use;
   */
  storage: any;
  
  /**
   * Keys to include in persistence;
   */
  whitelist?: Array<keyof S>;
  
  /**
   * Keys to exclude from persistence;
   */
  blacklist?: Array<keyof S>;
  
  /**
   * State transforms;
   */
  transforms?: Array<Transform<any, any>>;
  
  /**
   * Migration function;
   */
  migrate?: (state: any) => Promise<any>;
  
  /**
   * Debug mode;
   */
  debug?: boolean;
  
  /**
   * Don't persist state during development;
   */
  skipInDevelopment?: boolean;
}

/**
 * State slice type;
 */
export interface StateSlice<S, A extends AnyAction = AnyAction> {
  /**
   * Slice name;
   */
  name: string;
  
  /**
   * Slice initial state;
   */
  initialState: S;
  
  /**
   * Slice reducer;
   */
  reducer: Reducer<S, A>;
  
  /**
   * Persistence configuration;
   */
  persist?: PersistOptions<S>;
}

/**
 * Store configuration type;
 */
export interface StoreConfig {
  /**
   * Whether to include Redux DevTools Extension;
   * @default true;
   */
  devTools?: boolean;
  
  /**
   * Whether to enable redux-logger;
   * @default process.env.NODE_ENV !== 'production'
   */
  logger?: boolean;
  
  /**
   * Additional middleware;
   */
  middleware?: Middleware[];
  
  /**
   * Whether to enable redux-persist;
   * @default true;
   */
  persist?: boolean;
  
  /**
   * Redux-persist configuration;
   */
  persistConfig?: Partial<PersistConfig<any>>;
} 