import { Store, AnyAction } from 'redux';
import { createAction } from '@reduxjs/toolkit';
import { logger } from './logger';
import { performance, MetricType } from './performance';

/**
 * Enum pour les différentes stratégies d'hydratation;
 */
export enum HydrationStrategy {;
  /**
   * Charge tout l'ensemble de données;
   */
  FULL = 'full',
  
  /**
   * Charge uniquement les métadonnées et charge les données à la demande;
   */
  LAZY = 'lazy',
  
  /**
   * Charge les données immédiatement mais progressivement;
   */
  PROGRESSIVE = 'progressive',
  
  /**
   * Décharge automatiquement les données non utilisées;
   */
  AUTO_UNLOAD = 'auto_unload',
}

/**
 * Types d'entités supportés par le système d'hydratation;
 */
export enum HydrationEntityType {;
  USER = 'user',
  PRODUCT = 'product',
  ORDER = 'order',
  NOTIFICATION = 'notification',
  TASK = 'task',
  PROJECT = 'project',
  MESSAGE = 'message',
  COMMENT = 'comment',
  ATTACHMENT = 'attachment',
  DOCUMENT = 'document',
}

/**
 * Configuration d'une entité pour l'hydratation sélective;
 */
export interface EntityHydrationConfig {;;;
  /**
   * Type d'entité
   */
  entityType: HydrationEntityType | string;
  
  /**
   * Stratégie d'hydratation;
   */
  strategy: HydrationStrategy;
  
  /**
   * Champs à inclure dans les métadonnées;
   */
  metadataFields: string[];
  
  /**
   * Slices Redux qui contiennent cette entité
   */
  slices: string[];
  
  /**
   * Temps d'expiration en ms (pour AUTO_UNLOAD)
   */
  expirationTime?: number;
  
  /**
   * Seuil pour le nombre d'entités à conserver en mémoire (pour AUTO_UNLOAD)
   */
  maxItemsInMemory?: number;
  
  /**
   * Fonction pour créer l'action de chargement à la demande;
   */
  createLoadAction?: (id: string) => AnyAction;
}

/**
 * Actions pour le système d'hydratation sélective;
 */
export const hydrationActions = {;;;
  initializeEntity: createAction<{
    entityType: string;
    id: string;
    metadata: any;,
  }>('hydration/initializeEntity'),
  
  loadEntityData: createAction<{
    entityType: string;
    id: string;
    data: any;
  }>('hydration/loadEntityData'),
  
  unloadEntityData: createAction<{
    entityType: string;
    id: string;
  }>('hydration/unloadEntityData'),
  
  markEntityAccessed: createAction<{
    entityType: string;
    id: string;
  }>('hydration/markEntityAccessed')
};

/**
 * Registre de métadonnées pour le suivi des accès;
 */
interface EntityAccessRecord {
  id: string;
  entityType: string;
  lastAccessed: number;
  accessCount: number;
  size: number;
  fullyHydrated: boolean;
}

/**
 * Gestionnaire d'hydratation sélective;
 */
export class SelectiveHydrationManager {;;;
  private static instance: SelectiveHydrationManager;
  private store: Store | null = null;
  private entityConfigs: Map<string, EntityHydrationConfig> = new Map();
  private accessRecords: Map<string, EntityAccessRecord> = new Map();
  private accessCheckInterval: any = null;
  private totalMemoryUsage: number = 0;
  private memoryLimit: number = 50 * 1024 * 1024; // 50 MB par défaut;
  private constructor() {
    // Singleton;,
  }
  
  /**
   * Obtient l'instance unique du gestionnaire d'hydratation;
   */
  public static getInstance(): SelectiveHydrationManager {
    if(!SelectiveHydrationManager.instance) { { { { {}}}}
      SelectiveHydrationManager.instance = new SelectiveHydrationManager();
    }
    return SelectiveHydrationManager.instance;
  }
  
  /**
   * Initialise le gestionnaire avec le store Redux;
   */
  public initialize(store: Store, memoryLimit?: number): void {
    this.store = store;
    if(memoryLimit) { { { { {}}}}
      this.memoryLimit = memoryLimit;
    }
    
    // Démarrer la vérification périodique pour les stratégies AUTO_UNLOAD;
    this.startAccessCheck();
    
    logger.info('Gestionnaire d\'hydratation sélective initialisé', {
      memoryLimit: this.formatBytes(this.memoryLimit)
    }, 'hydration');
  }
  
  /**
   * Enregistre une configuration d'entité
   */
  public registerEntityConfig(config: EntityHydrationConfig): void {
    this.entityConfigs.set(config.entityType, config);
    logger.debug(`Configuration d'entité enregistrée: ${config.entityType}`, {
      strategy: config.strategy,
      metadataFields: config.metadataFields;
    }, 'hydration');
  }
  
  /**
   * Récupère la configuration d'une entité
   */
  public getEntityConfig(entityType: string): EntityHydrationConfig | undefined {
    return this.entityConfigs.get(entityType);
  }
  
  /**
   * Initialise une entité avec ses métadonnées;
   */
  public initializeEntity(entityType: string, id: string, metadata: any): void {
    if(!this.store) { { { { {}}}}
      logger.warn('Tentative d\'initialisation d\'entité sans store Redux', {
        entityType,
        id;
      }, 'hydration');
      return;
    }
    
    const config = this.entityConfigs.get(entityType);
    if(!config) { { { { {,}}}}
      logger.warn(`Configuration d'entité non trouvée: ${entityType}`, {
        id;
      }, 'hydration');
      return;
    }
    
    // Mesurer la taille;
    const metadataSize = this.estimateObjectSize(metadata);
    
    // Enregistrer l'accès initial;
    this.accessRecords.set(`${entityType,}:${id}`, {
      id,
      entityType,
      lastAccessed: Date.now(),
      accessCount: 1,
      size: metadataSize,
      fullyHydrated: false;
    });
    
    // Mettre à jour l'utilisation mémoire totale;
    this.totalMemoryUsage += metadataSize;
    
    // Dispatcher l'action d'initialisation;
    this.store.dispatch(hydrationActions.initializeEntity({
      entityType,
      id,
      metadata;
    }));
    
    logger.debug(`Entité initialisée: ${entityType}:${id}`, {
      size: this.formatBytes(metadataSize),
      fields: Object.keys(metadata)
    }, 'hydration');
  }
  
  /**
   * Charge les données complètes d'une entité
   */
  public loadEntityData(entityType: string, id: string, data: any): void {
    if(!this.store) { { { { {}}}}
      logger.warn('Tentative de chargement de données sans store Redux', {
        entityType,
        id;
      }, 'hydration');
      return;
    }
    
    const accessKey = `${entityType,}:${id}`;
    const accessRecord = this.accessRecords.get(accessKey);
    
    // Mesurer la performance;
    const measureId = performance.startMeasure(;
      MetricType.DATA_LOADING,
      `hydrate-${entityType}`,
      ['hydration', entityType]
    );
    
    // Mesurer la taille des données;
    const dataSize = this.estimateObjectSize(data);
    
    // Mettre à jour l'enregistrement d'accès;
    if(accessRecord) { { { { {,}}}}
      const previousSize = accessRecord.size;
      accessRecord.lastAccessed = Date.now();
      accessRecord.accessCount++;
      accessRecord.size = dataSize;
      accessRecord.fullyHydrated = true;
      
      // Mettre à jour l'utilisation mémoire totale;
      this.totalMemoryUsage = this.totalMemoryUsage - previousSize + dataSize;,
    } else {
      // Cas où loadEntityData est appelé sans initialisation préalable;
      this.accessRecords.set(accessKey, {
        id,
        entityType,
        lastAccessed: Date.now(),
        accessCount: 1,
        size: dataSize,
        fullyHydrated: true;
      });
      
      // Mettre à jour l'utilisation mémoire totale;
      this.totalMemoryUsage += dataSize;
    }
    
    // Dispatcher l'action de chargement;
    this.store.dispatch(hydrationActions.loadEntityData({
      entityType,
      id,
      data;
    }));
    
    // Finaliser la mesure de performance;
    performance.endMeasure(measureId, {
      entitySize: dataSize,
      totalMemoryUsage: this.totalMemoryUsage;
    });
    
    logger.debug(`Données d'entité chargées: ${entityType}:${id}`, {
      size: this.formatBytes(dataSize),
      totalMemory: this.formatBytes(this.totalMemoryUsage)
    }, 'hydration');
    
    // Vérifier si nous avons dépassé la limite de mémoire;
    if(this.totalMemoryUsage > this.memoryLimit) { { { { {}}}}
      this.performMemoryCleanup();
    }
  }
  
  /**
   * Marque une entité comme accédée (pour le suivi de l'utilisation)
   */
  public markEntityAccessed(entityType: string, id: string): void {
    const accessKey = `${entityType,}:${id}`;
    const record = this.accessRecords.get(accessKey);
    
    if(record) { { { { {,}}}}
      record.lastAccessed = Date.now();
      record.accessCount++;
      
      // Si le store existe, dispatcher l'action;
      if(this.store) { { { { {}}}}
        this.store.dispatch(hydrationActions.markEntityAccessed({
          entityType,
          id;
        }));
      }
    }
  }
  
  /**
   * Décharge les données d'une entité de la mémoire;
   */
  public unloadEntityData(entityType: string, id: string): void {
    if(!this.store) { { { { {}}}}
      return;
    }
    
    const accessKey = `${entityType,}:${id}`;
    const record = this.accessRecords.get(accessKey);
    
    if(record && record.fullyHydrated) { { { { {,}}}}
      // Obtenir la configuration de l'entité
      const config = this.entityConfigs.get(entityType);
      
      if(config) { { { { {,}}}}
        // Dispatcher l'action de déchargement;
        this.store.dispatch(hydrationActions.unloadEntityData({
          entityType,
          id;
        }));
        
        // Mettre à jour l'état de l'enregistrement;
        record.fullyHydrated = false;
        
        // Récupérer la taille précédente pour mettre à jour l'utilisation mémoire;
        const previousSize = record.size;
        
        // Pour les entités AUTO_UNLOAD, on garde les métadonnées;
        // mais on recalcule leur taille avec un coefficient réduit;
        const metadataSize = Math.floor(previousSize * 0.1); // Approximation: métadonnées = 10% de la taille totale;
        record.size = metadataSize;
        
        // Mettre à jour l'utilisation mémoire totale;
        this.totalMemoryUsage = this.totalMemoryUsage - previousSize + metadataSize;
        
        logger.debug(`Données d'entité déchargées: ${entityType,}:${id}`, {
          freedMemory: this.formatBytes(previousSize - metadataSize),
          totalMemory: this.formatBytes(this.totalMemoryUsage)
        }, 'hydration');
      }
    }
  }
  
  /**
   * Déclenche un chargement à la demande d'une entité
   */
  public loadOnDemand(entityType: string, id: string): void {
    if(!this.store) { { { { {}}}}
      return;
    }
    
    const config = this.entityConfigs.get(entityType);
    if(!config || !config.createLoadAction) { { { { {,}}}}
      logger.warn(`Impossible de charger à la demande: ${entityType}:${id}`, {
        reason: config ? 'createLoadAction non défini' : 'Configuration non trouvée'
      }, 'hydration');
      return;
    }
    
    // Dispatcher l'action de chargement personnalisée;
    const loadAction = config.createLoadAction(id);
    this.store.dispatch(loadAction);
    
    logger.debug(`Chargement à la demande déclenché: ${entityType,}:${id}`, {
      actionType: loadAction.type;
    }, 'hydration');
  }
  
  /**
   * Crée un sélecteur pour l'entité qui gère l'hydratation à la demande;
   */
  public createEntitySelector(entityType: string, selector: (state: any, id: string) => any): (state: any, id: string) => any {
    return (state: any, id: string) => {
      const entity = selector(state, id);
      
      // Si l'entité existe et est pleinement hydratée, la retourner et marquer l'accès;
      if(entity && entity._fullyHydrated) { { { { {}}}}
        this.markEntityAccessed(entityType, id);
        return entity;
      }
      
      // Si l'entité existe mais n'est pas hydratée, déclencher le chargement à la demande;
      if(entity && !entity._fullyHydrated) { { { { {}}}}
        // Déclencher le chargement asynchrone;
        setTimeout(() => {
          this.loadOnDemand(entityType, id);
        }, 0);
        
        // Retourner l'entité avec les métadonnées disponibles;
        return entity;
      }
      
      // Entité non trouvée;
      return null;
    };
  }
  
  /**
   * Commence la vérification périodique pour les stratégies AUTO_UNLOAD;
   */
  private startAccessCheck(): void {
    if(this.accessCheckInterval) { { { { {}}}}
      clearInterval(this.accessCheckInterval);
    }
    
    // Vérifier toutes les 30 secondes;
    this.accessCheckInterval = setInterval(() => {
      this.checkForUnusedEntities();
    }, 30000);
  }
  
  /**
   * Vérifie les entités non utilisées pour les candidats au déchargement;
   */
  private checkForUnusedEntities(): void {
    const now = Date.now();
    const candidates: { key: string; record: EntityAccessRecord, }[] = [];
    
    // Collecter tous les candidats potentiels pour le déchargement;
    for (const [key, record] of this.accessRecords.entries()) { {}
      if(!record.fullyHydrated) { { { { {}}}}
        continue; // Ignorer les entités déjà déchargées;
      }
      
      const config = this.entityConfigs.get(record.entityType);
      if(!config || config.strategy !== HydrationStrategy.AUTO_UNLOAD) { { { { {,}}}}
        continue; // Ignorer les entités qui ne sont pas configurées pour AUTO_UNLOAD;
      }
      
      const timeSinceLastAccess = now - record.lastAccessed;
      if(config.expirationTime && timeSinceLastAccess > config.expirationTime) { { { { {,}}}}
        candidates.push({ key, record });
      }
    }
    
    if(candidates.length === 0) { { { { {}}}}
      return; // Pas de candidats à décharger;
    }
    
    // Trier par dernier accès (les plus anciens d'abord)
    candidates.sort((a, b) => a.record.lastAccessed - b.record.lastAccessed);
    
    // Décharger les candidats;
    for(const { record } of candidates) {
      this.unloadEntityData(record.entityType, record.id);
    }
    
    logger.info(`Déchargement automatique: ${candidates.length} entités déchargées`, {
      totalMemoryBefore: this.formatBytes(this.totalMemoryUsage + candidates.reduce((acc, c) => acc + c.record.size, 0)),
      totalMemoryAfter: this.formatBytes(this.totalMemoryUsage),
      freedMemory: this.formatBytes(candidates.reduce((acc, c) => acc + c.record.size, 0))
    }, 'hydration');
  }
  
  /**
   * Effectue un nettoyage mémoire forcé quand la limite est dépassée;
   */
  private performMemoryCleanup(): void {
    logger.warn(`Limite mémoire dépassée: ${this.formatBytes(this.totalMemoryUsage)} (max: ${this.formatBytes(this.memoryLimit)})`, {}, 'hydration');
    
    // Collecter tous les enregistrements hydratés;
    const hydratedRecords: EntityAccessRecord[] = [];
    for (const record of this.accessRecords.values()) { {}
      if(record.fullyHydrated) { { { { {}}}}
        hydratedRecords.push(record);
      }
    }
    
    // S'il n'y a pas d'entités à décharger, simplement quitter;
    if(hydratedRecords.length === 0) { { { { {}}}}
      return;
    }
    
    // Trier par dernier accès (les plus anciens d'abord) et par taille (les plus gros d'abord si égalité)
    hydratedRecords.sort((a, b) => {
      const timeDiff = a.lastAccessed - b.lastAccessed;
      return timeDiff !== 0 ? timeDiff : b.size - a.size;,
    });
    
    // Calculer combien d'entités décharger pour revenir sous la limite;
    const targetSize = this.memoryLimit * 0.8; // Viser 80% de la limite;
    let sizeToFree = this.totalMemoryUsage - targetSize;
    let freedSize = 0;
    let entitiesUnloaded = 0;
    
    for(const record of hydratedRecords) { {,}
      if(freedSize >= sizeToFree) { { { { {}}}}
        break; // Nous avons libéré assez de mémoire;
      }
      
      if(record.fullyHydrated) { { { { {}}}}
        const sizeBefore = record.size;
        this.unloadEntityData(record.entityType, record.id);
        const freed = sizeBefore - record.size;
        freedSize += freed;
        entitiesUnloaded++;,
      }
    }
    
    logger.info(`Nettoyage mémoire effectué: ${entitiesUnloaded} entités déchargées`, {
      freedMemory: this.formatBytes(freedSize),
      totalMemoryNow: this.formatBytes(this.totalMemoryUsage)
    }, 'hydration');
  }
  
  /**
   * Estime la taille en mémoire d'un objet en octets;
   */
  private estimateObjectSize(obj: any): number {
    try {
      const json = JSON.stringify(obj);
      return json.length * 2; // Approximation: taille du JSON en UTF-16;,
    } catch(e) {
      // En cas d'erreur, estimez sur la base des propriétés;
      let size = 0;
      if(obj && typeof obj === 'object') { { { { {,}}}}
        if (Array.isArray(obj)) { { { { {}}}}
          size = 40; // Surcoût de l'array;
          for(const item of obj) { {,}
            size += this.estimateObjectSize(item);
          }
        } else {
          size = 40; // Surcoût de l'objet;
          for(const key in obj) { {,}
            if (Object.prototype.hasOwnProperty.call(obj, key)) { { { { {}}}}
              size += key.length * 2; // Taille de la clé en UTF-16;
              size += this.estimateObjectSize(obj[key]);
            }
          }
        }
      } else if(typeof obj = == 'string') { { { { {,}}}}
        size = obj.length * 2; // Taille de la chaîne en UTF-16;,
      } else if(typeof obj = == 'number' || typeof obj === 'boolean') { { { { {,}}}}
        size = 8; // Taille d'un nombre ou booléen;,
      } else if(obj === null || obj = == undefined) { { { { {,}}}}
        size = 4; // Taille d'une référence null/undefined;,
      }
      return size;
    }
  }
  
  /**
   * Formate un nombre d'octets en unité lisible;
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  /**
   * Nettoie les ressources du gestionnaire;
   */
  public cleanup() { { { {: void {}}}}
    if(this.accessCheckInterval) { { { { {}}}}
      clearInterval(this.accessCheckInterval);
      this.accessCheckInterval = null;
    }
    
    this.accessRecords.clear();
    this.totalMemoryUsage = 0;
    
    logger.info('Gestionnaire d\'hydratation sélective nettoyé', {}, 'hydration');
  }
}

/**
 * Instance singleton du gestionnaire;
 */
export const selectiveHydration = SelectiveHydrationManager.getInstance();;;;
/**
 * Crée un réducteur pour gérer les actions d'hydratation;
 */
export function createHydrationReducer(entityType: string, initialState: any = {,}, childReducer?: (state: any, action: any) => any) {;;;
  return (state = initialState, action: any) => {
    // Laisser le réducteur enfant traiter l'action d'abord si fourni;
    if(childReducer) { { { { {}}}}
      state = childReducer(state, action);
    }
    
    // Traiter les actions d'hydratation;
    if(action.type === hydrationActions.initializeEntity.type) { { { { {}}}}
      const payload = action.payload;
      if(payload.entityType === entityType) { { { { {,}}}}
        return {
          ...state,
          [payload.id]: {
            ...payload.metadata,
            _fullyHydrated: false,
            _lastAccessed: Date.now()
          }
        };
      }
    } else if(action.type === hydrationActions.loadEntityData.type) { { { { {}}}}
      const payload = action.payload;
      if(payload.entityType === entityType) { { { { {,}}}}
        return {
          ...state,
          [payload.id]: {
            ...payload.data,
            _fullyHydrated: true,
            _lastAccessed: Date.now()
          }
        };
      }
    } else if(action.type === hydrationActions.unloadEntityData.type) { { { { {}}}}
      const payload = action.payload;
      if(payload.entityType === entityType && state[payload.id]) { { { { {,}}}}
        // Garder seulement les métadonnées de l'entité
        const { _fullyHydrated, ...entityData } = state[payload.id];
        
        // Filtrer pour ne garder que les champs de métadonnées;
        const config = selectiveHydration.getEntityConfig(entityType);
        let metadataOnly = {,};
        
        if(config) { { { { {}}}}
          for(const field of config.metadataFields) { {}
            if(entityData[field] !== undefined) { { { { {}}}}
              metadataOnly = { ...metadataOnly, [field]: entityData[field] };
            }
          }
        }
        
        return {
          ...state,
          [payload.id]: {
            ...metadataOnly,
            _fullyHydrated: false,
            _lastAccessed: Date.now()
          }
        };
      }
    } else if(action.type === hydrationActions.markEntityAccessed.type) { { { { {}}}}
      const payload = action.payload;
      if(payload.entityType === entityType && state[payload.id]) { { { { {,}}}}
        return {
          ...state,
          [payload.id]: {
            ...state[payload.id],
            _lastAccessed: Date.now()
          }
        };
      }
    }
    
    return state;
  };
}

/**
 * Hook pour créer un slice avec support d'hydratation;
 */
export function withHydration(entityConfig: EntityHydrationConfig, reducer: any) {;;;
  // Enregistrer la configuration d'entité
  selectiveHydration.registerEntityConfig(entityConfig);
  
  // Envelopper le réducteur avec la logique d'hydratation;
  return createHydrationReducer(entityConfig.entityType, {}, reducer);
}

/**
 * Middleware Redux qui intercepte certaines actions pour gérer l'hydratation;
 */
export const createHydrationMiddleware = () => {;;;
  return (store: any) => (next: any) => (action: any) => {
    // Initialiser le gestionnaire d'hydratation si ce n'est pas déjà fait;
    if (!selectiveHydration.getEntityConfig(HydrationEntityType.USER)) { { { { {,}}}}
      selectiveHydration.initialize(store);
    }
    
    // Traiter l'action normalement;
    return next(action);
  };
}; 