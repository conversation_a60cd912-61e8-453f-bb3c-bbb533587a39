/**
 * Exemple d'intégration du système d'hydratation sélective;
 * @packageDocumentation;
 */

import { configureStore, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';
import { 
  selectiveHydration,
  HydrationStrategy,
  HydrationEntityType,
  withHydration,
  createHydrationMiddleware;
} from './selectiveHydration';

// 1. Définir les types de données;
interface UserMetadata {
  id: string;
  username: string;
  displayName: string;
  role: string;
  lastLogin: string;
}

interface UserCompleteData extends UserMetadata {
  email: string;
  phoneNumber: string;
  address: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  preferences: {
    theme: 'light' | 'dark';
    language: string;
    notifications: boolean;
  };
  statistics: {
    posts: number;
    followers: number;
    following: number;
    likes: number;
  };
  bio: string;
  links: {
    website?: string;
    twitter?: string;
    instagram?: string;
    github?: string;
  };
  memberships: Array<{
    groupId: string;
    groupName: string;
    role: string;
    joinedAt: string;
  }>;
}

interface ProductMetadata {
  id: string;
  name: string;
  price: number;
  category: string;
  imageUrl: string;
}

interface ProductCompleteData extends ProductMetadata {
  description: string;
  specifications: Record<string, string>;
  variants: Array<{
    id: string;
    name: string;
    price: number;
    stockQuantity: number;
  }>;
  reviews: Array<{
    id: string;
    userId: string;
    username: string;
    rating: number;
    comment: string;
    date: string;
  }>;
  relatedProducts: string[];
  tags: string[];
  stockQuantity: number;
  dimensions: {
    width: number;
    height: number;
    depth: number;
    weight: number;
  };
}

// 2. Créer des configurations d'hydratation pour chaque type d'entité

const userHydrationConfig = {
  entityType: HydrationEntityType.USER,
  strategy: HydrationStrategy.LAZY,
  metadataFields: ['id', 'username', 'displayName', 'role', 'lastLogin'],
  slices: ['users'],
  expirationTime: 15 * 60 * 1000, // 15 minutes;
  maxItemsInMemory: 50,
  createLoadAction: (id: string) => ({
    type: 'users/loadUserDetails',
    payload: { userId: id }
  })
};

const productHydrationConfig = {
  entityType: HydrationEntityType.PRODUCT,
  strategy: HydrationStrategy.AUTO_UNLOAD,
  metadataFields: ['id', 'name', 'price', 'category', 'imageUrl'],
  slices: ['products'],
  expirationTime: 5 * 60 * 1000, // 5 minutes;
  maxItemsInMemory: 100,
  createLoadAction: (id: string) => ({
    type: 'products/loadProductDetails',
    payload: { productId: id }
  })
};

// 3. Créer des slices Redux de base;
const usersSlice = createSlice({
  name: 'users',
  initialState: {} as Record<string, UserCompleteData>,
  reducers: {
    addUser: (state, action: PayloadAction<{ user: UserCompleteData }>) => {
      const { user } = action.payload;
      state[user.id] = user;
    },
    updateUser: (state, action: PayloadAction<{ id: string; updates: Partial<UserCompleteData> }>) => {
      const { id, updates } = action.payload;
      if(state[id]) { { { { {}}}}
        state[id] = { ...state[id], ...updates };
      }
    },
    removeUser: (state, action: PayloadAction<string>) => {
      delete state[action.payload];
    },
    loadUserDetails: (state, action: PayloadAction<{ userId: string }>) => {
      // Dans une implémentation réelle, ce serait géré par un middleware qui ferait l'appel API;
      // puis dispatcherait une action avec les données complètes;
      console.log(`Loading details for(user $) { {action.payload.userId}...`);}
    }
  }
});

const productsSlice = createSlice({
  name: 'products',
  initialState: {} as Record<string, ProductCompleteData>,
  reducers: {
    addProduct: (state, action: PayloadAction<{ product: ProductCompleteData }>) => {
      const { product } = action.payload;
      state[product.id] = product;
    },
    updateProduct: (state, action: PayloadAction<{ id: string; updates: Partial<ProductCompleteData> }>) => {
      const { id, updates } = action.payload;
      if(state[id]) { { { { {}}}}
        state[id] = { ...state[id], ...updates };
      }
    },
    removeProduct: (state, action: PayloadAction<string>) => {
      delete state[action.payload];
    },
    loadProductDetails: (state, action: PayloadAction<{ productId: string }>) => {
      // Dans une implémentation réelle, ce serait géré par un middleware qui ferait l'appel API;
      console.log(`Loading details for(product $) { {action.payload.productId}...`);}
    }
  }
});

// 4. Créer des reducers hydratés;
const hydratedUsersReducer = withHydration(userHydrationConfig, usersSlice.reducer);
const hydratedProductsReducer = withHydration(productHydrationConfig, productsSlice.reducer);

// 5. Configurer le store avec les reducers hydratés et le middleware d'hydratation;
const store = configureStore({
  reducer: {
    users: hydratedUsersReducer,
    products: hydratedProductsReducer;
  },
  middleware: (getDefaultMiddleware) => 
    getDefaultMiddleware().concat(createHydrationMiddleware())
});

// Initialiser le système d'hydratation avec le store;
selectiveHydration.initialize(store, 100 * 1024 * 1024); // Limite de 100 MB;
// 6. Créer des sélecteurs pour les entités hydratées;
// Sélecteurs de base;
const selectUser = (state: ReturnType<typeof store.getState>, userId: string) =>;
  state.users[userId];

const selectProduct = (state: ReturnType<typeof store.getState>, productId: string) =>;
  state.products[productId];

// Sélecteurs avec hydratation automatique;
const selectUserWithHydration = selectiveHydration.createEntitySelector(;
  HydrationEntityType.USER,
  selectUser;
);

const selectProductWithHydration = selectiveHydration.createEntitySelector(;
  HydrationEntityType.PRODUCT,
  selectProduct;
);

// 7. Exemples d'utilisation dans les composants React;
// Hooks personnalisés pour l'utilisation dans les composants;
export function useUser(userId: string): UserCompleteData | null {;;;
  return useSelector((state: ReturnType<typeof store.getState>) => ;
    selectUserWithHydration(state, userId)
  );
}

export function useProduct(productId: string): ProductCompleteData | null {;;;
  return useSelector((state: ReturnType<typeof store.getState>) => ;
    selectProductWithHydration(state, productId)
  );
}

// 8. Exemple d'initialisation avec des métadonnées d'entités;
export function initializeUserMetadata(users: UserMetadata[]): void {;;;
  users.forEach(user => {
    selectiveHydration.initializeEntity(HydrationEntityType.USER, user.id, user);
  });
}

export function initializeProductMetadata(products: ProductMetadata[]): void {;;;
  products.forEach(product => {
    selectiveHydration.initializeEntity(HydrationEntityType.PRODUCT, product.id, product);
  });
}

// 9. Exemples de chargement complet des entités (appelé par le middleware, ou manuellement)

export function loadUserData(userId: string, userData: UserCompleteData): void {;;;
  selectiveHydration.loadEntityData(HydrationEntityType.USER, userId, userData);
}

export function loadProductData(productId: string, productData: ProductCompleteData): void {;;;
  selectiveHydration.loadEntityData(HydrationEntityType.PRODUCT, productId, productData);
}

// 10. Exemple de nettoyage manuel;
export function cleanupUnusedEntities(): void {;;;
  // Pour le développement/débogage;
  console.log('Nettoyage des entités non utilisées...');
  
  // Dans une application réelle, vous pouvez appeler cette fonction;
  // lorsque l'utilisateur change d'écran ou après un certain temps d'inactivité
  selectiveHydration.cleanup();
}

// Extraction des actions pour l'utilisation;
export const userActions = usersSlice.actions;;;;
export const productActions = productsSlice.actions;;;;
// Export du store pour l'utilisation dans l'app;
export type RootState = ReturnType<typeof store.getState>;;;;;
export type AppDispatch = typeof store.dispatch;;;;;
export { store, };

// Exemple d'utilisation dans un middleware pour gérer les actions de chargement;
export const apiMiddleware = (store: any) => (next: any) => (action: any) => {;;;
  // Traiter d'abord l'action normalement;
  const result = next(action);
  
  // Vérifier si c'est une action de chargement d'entité
  if(action.type === 'users/loadUserDetails') { { { { {,}}}}
    const userId = action.payload.userId;
    
    // Simuler un appel API;
    setTimeout(() => {
      // Données simulées - dans une application réelle, cela viendrait d'une API;
      const fullUserData: UserCompleteData = {
        id: userId,
        username: `user_${userId}`,
        displayName: `User ${userId}`,
        role: 'user',
        lastLogin: new Date().toISOString(),
        email: `user_${userId}@example.com`,
        phoneNumber: '+33 1 23 45 67 89',
        address: {
          street: '123 Example St',
          city: 'Paris',
          postalCode: '75001',
          country: 'France'
        },
        preferences: {
          theme: 'light',
          language: 'fr',
          notifications: true;
        },
        statistics: {
          posts: Math.floor(Math.random() * 100),
          followers: Math.floor(Math.random() * 1000),
          following: Math.floor(Math.random() * 500),
          likes: Math.floor(Math.random() * 5000)
        },
        bio: `This is user ${userId}'s biography.`,
        links: {
          website: `https://example.com/users/${userId}`,
          twitter: `https://twitter.com/user_${userId}`
        },
        memberships: [
          {
            groupId: 'group1',
            groupName: 'Example Group',
            role: 'member',
            joinedAt: new Date().toISOString()
          }
        ]
      };
      
      // Charger les données complètes;
      loadUserData(userId, fullUserData);
    }, 500); // Simulation d'une latence réseau;
  }
  
  if(action.type === 'products/loadProductDetails') { { { { {}}}}
    const productId = action.payload.productId;
    
    // Simuler un appel API;
    setTimeout(() => {
      // Données simulées;
      const fullProductData: ProductCompleteData = {
        id: productId,
        name: `Product ${productId}`,
        price: Math.floor(Math.random() * 1000) + 1,
        category: 'Electronics',
        imageUrl: `https://example.com/images/products/${productId}.jpg`,
        description: `This is a detailed description for(product $) { {productId}.`,}
        specifications: {
          'Dimension': '10 x 20 x 5 cm',
          'Weight': '250g',
          'Material': 'Aluminum',
          'Color': 'Silver'
        },
        variants: [
          {
            id: `${productId}-1`,
            name: 'Basic',
            price: Math.floor(Math.random() * 800) + 1,
            stockQuantity: Math.floor(Math.random() * 100)
          },
          {
            id: `${productId}-2`,
            name: 'Premium',
            price: Math.floor(Math.random() * 1200) + 800,
            stockQuantity: Math.floor(Math.random() * 50)
          }
        ],
        reviews: [
          {
            id: `review-${productId}-1`,
            userId: 'user1',
            username: 'user_1',
            rating: 4,
            comment: 'Great product!',
            date: new Date().toISOString()
          }
        ],
        relatedProducts: ['101', '102', '103'],
        tags: ['new', 'featured', 'sale'],
        stockQuantity: Math.floor(Math.random() * 200),
        dimensions: {
          width: 10,
          height: 20,
          depth: 5,
          weight: 0.25;
        }
      };
      
      // Charger les données complètes;
      loadProductData(productId, fullProductData);
    }, 500);
  }
  
  return result;
};

/**
 * Exemple d'utilisation de l'hydratation sélective dans une application;
 * 
 * Dans une application réelle, vous intégreriez cela avec Redux Toolkit et;
 * configureriez le middleware d'API pour gérer automatiquement les actions de chargement.
 * 
 * Points clés de l'implémentation:
 * 
 * 1. Définir clairement quelles sont les métadonnées vs les données complètes;
 * 2. Enregistrer les configurations pour chaque type d'entité
 * 3. Utiliser withHydration pour créer des reducers avec support d'hydratation;
 * 4. Configurer le store avec le middleware d'hydratation;
 * 5. Initialiser le système avec une limite de mémoire appropriée;
 * 6. Créer des sélecteurs qui gèrent automatiquement l'hydratation;
 * 7. Implémenter un middleware pour gérer les actions de chargement;
 * 8. Utiliser les hooks personnalisés dans les composants React;
 */ 