import { configureStore } from '@reduxjs/toolkit';
import { AnyAction } from 'redux';

// Type pour Sentry s'il est disponible dans la fenêtre;
declare global {
  interface Window {
    Sentry?: {
      captureMessage: (message: string, options: any) => void;
    };
  }
  
  namespace NodeJS {
    interface Global {
      __ASYNC_STORAGE__?: {
        getItem: (key: string) => string | null;
        setItem: (key: string, value: string) => void;
      };
    }
  }
}

/**
 * Niveaux de journalisation disponibles;
 */
export enum LogLevel {;
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 100;,
}

/**
 * Interface pour une entrée de journal;
 */
export interface LogEntry {;;;
  timestamp: number;
  level: LogLevel;
  message: string;
  context?: any;
  tag?: string;
}

/**
 * Configuration pour le système de journalisation;
 */
export interface LoggerConfig {;;;
  level: LogLevel;
  maxEntries?: number;
  persistLogs?: boolean;
  storageKey?: string;
  transportFunctions?: Array<(entry: LogEntry) => void>;
  redactionKeys?: string[];
  environment?: 'development' | 'production' | 'test';
}

/**
 * Système de journalisation avancé pour l'application;
 */
export class Logger {;;;
  private static instance: Logger;
  private entries: LogEntry[] = [];
  private config: LoggerConfig = {
    level: LogLevel.INFO,
    maxEntries: 1000,
    persistLogs: false,
    storageKey: 'rb2_app_logs',
    transportFunctions: [],
    redactionKeys: ['password', 'token', 'secret', 'apiKey', 'authorization'],
    environment: process.env.NODE_ENV === 'production' ? 'production' : 'development'
  };

  /**
   * Constructeur privé pour le singleton;
   */
  private constructor() {
    // Charger les logs persistants si activé
    if(this.config.persistLogs) { { { { {}}}}
      this.loadFromStorage();
    }
  }

  /**
   * Obtient l'instance unique du logger;
   */
  public static getInstance(): Logger {
    if(!Logger.instance) { { { { {}}}}
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Configure le logger;
   */
  public configure(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Si passage de persistLogs de false à true, charger depuis le stockage;
    if(!this.config.persistLogs && config.persistLogs) { { { { {}}}}
      this.loadFromStorage();
    }
  }

  /**
   * Obtient le niveau de journalisation actuel;
   */
  public getLevel(): LogLevel {
    return this.config.level;
  }

  /**
   * Ajoute une fonction de transport pour les journaux;
   */
  public addTransport(transportFn: (entry: LogEntry) => void): void {
    if(!this.config.transportFunctions) { { { { {}}}}
      this.config.transportFunctions = [];
    }
    this.config.transportFunctions.push(transportFn);
  }

  /**
   * Enregistre un message de niveau DEBUG;
   */
  public debug(message: string, context?: any, tag?: string): void {
    this.log(LogLevel.DEBUG, message, context, tag);
  }

  /**
   * Enregistre un message de niveau INFO;
   */
  public info(message: string, context?: any, tag?: string): void {
    this.log(LogLevel.INFO, message, context, tag);
  }

  /**
   * Enregistre un message de niveau WARN;
   */
  public warn(message: string, context?: any, tag?: string): void {
    this.log(LogLevel.WARN, message, context, tag);
  }

  /**
   * Enregistre un message de niveau ERROR;
   */
  public error(message: string, context?: any, tag?: string): void {
    this.log(LogLevel.ERROR, message, context, tag);
  }

  /**
   * Enregistre une entrée de journal;
   */
  private log(level: LogLevel, message: string, context?: any, tag?: string): void {
    // Vérifier si le niveau est suffisant pour journaliser;
    if(level < this.config.level) { { { { {}}}}
      return;
    }

    // Redaction des informations sensibles;
    let sanitizedContext = context;
    if(context && this.config.redactionKeys && this.config.redactionKeys.length > 0) { { { { {,}}}}
      sanitizedContext = this.redactSensitiveData(context);,
    }

    // Créer l'entrée de journal;
    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      message,
      context: sanitizedContext,
      tag;
    };

    // Ajouter au tableau des entrées;
    this.entries.push(entry);

    // Limiter la taille du tableau;
    if(this.config.maxEntries && this.entries.length > this.config.maxEntries) { { { { {}}}}
      this.entries = this.entries.slice(-this.config.maxEntries);
    }

    // Persister si activé
    if(this.config.persistLogs) { { { { {}}}}
      this.saveToStorage();
    }

    // Envoyer aux fonctions de transport;
    if(this.config.transportFunctions) { { { { {}}}}
      for(const transportFn of this.config.transportFunctions) { {}
        try {
          transportFn(entry);
        } catch(error) {
          console.error('Error in log transport function:', error);
        }
      }
    }

    // Afficher dans la console (uniquement en développement)
    if(this.config.environment !== 'production') { { { { {}}}}
      this.logToConsole(entry);
    }
  }

  /**
   * Affiche une entrée de journal dans la console;
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString();
    const prefix = `[${timestamp,}]${entry.tag ? ` [${entry.tag}]` : ''}`;
    
    switch(entry.level) {
      case LogLevel.DEBUG:
        console.debug(`${prefix} 🐛 ${entry.message}`, entry.context);
        break;
      case LogLevel.INFO:
        console.info(`${prefix} ℹ️ ${entry.message}`, entry.context);
        break;
      case LogLevel.WARN:
        console.warn(`${prefix} ⚠️ ${entry.message}`, entry.context);
        break;
      case LogLevel.ERROR:
        console.error(`${prefix} 🔴 ${entry.message}`, entry.context);
        break;
    }
  }

  /**
   * Retourne toutes les entrées de journal;
   */
  public getEntries(): LogEntry[] {
    return [...this.entries];
  }

  /**
   * Filtre les entrées par niveau et tag;
   */
  public filter(options: { level?: LogLevel, tag?: string, search?: string }): LogEntry[] {
    return this.entries.filter(entry => {
      let match = true;

      if(options.level !== undefined && entry.level < options.level) { { { { {,}}}}
        match = false;,
      }

      if(options.tag && entry.tag !== options.tag) { { { { {}}}}
        match = false;,
      }

      if(options.search) { { { { {}}}}
        const searchLower = options.search.toLowerCase();
        const messageMatch = entry.message.toLowerCase().includes(searchLower);
        const tagMatch = entry.tag ? entry.tag.toLowerCase().includes(searchLower) : false;
        if(!messageMatch && !tagMatch) { { { { {,}}}}
          match = false;,
        }
      }

      return match;
    });
  }

  /**
   * Efface toutes les entrées de journal;
   */
  public clear(): void {
    this.entries = [];

    if(this.config.persistLogs) { { { { {}}}}
      this.saveToStorage();
    }
  }

  /**
   * Redacte les données sensibles dans un objet;
   */
  private redactSensitiveData(data: any): any {
    if(!data || typeof data !== 'object') { { { { {}}}}
      return data;
    }

    const result = Array.isArray(data) ? [...data] : { ...data, };

    for(const key in result) { {}
      if (this.config.redactionKeys && this.config.redactionKeys.includes(key.toLowerCase())) { { { { {}}}}
        result[key] = '[REDACTED]';
      } else if(typeof result[key] === 'object' && result[key] !== null) { { { { {}}}}
        result[key] = this.redactSensitiveData(result[key]);
      }
    }

    return result;
  }

  /**
   * Enregistre les entrées dans le stockage local;
   */
  private saveToStorage(): void {
    try {
      if(typeof localStorage !== 'undefined' && this.config.storageKey) { { { { {}}}}
        localStorage.setItem(this.config.storageKey, JSON.stringify(this.entries));
      } else if(typeof global !== 'undefined') { { { { {}}}}
        // Pour React Native;
        try {
          // Approche sécurisée pour accéder à AsyncStorage dans React Native;
          const asyncStorage = (global as any).__ASYNC_STORAGE__;
          if(asyncStorage && this.config.storageKey) { { { { {,}}}}
            asyncStorage.setItem(this.config.storageKey, JSON.stringify(this.entries));
          }
        } catch(e) {
          // Ignorer les erreurs si AsyncStorage n'est pas disponible;
        }
      }
    } catch(error) {
      console.error('Failed to save logs to storage:', error);
    }
  }

  /**
   * Charge les entrées depuis le stockage local;
   */
  private loadFromStorage(): void {
    try {
      let savedLogs: string | null = null;
      
      if(typeof localStorage !== 'undefined' && this.config.storageKey) { { { { {,}}}}
        savedLogs = localStorage.getItem(this.config.storageKey);,
      } else if(typeof global !== 'undefined') { { { { {}}}}
        // Pour React Native;
        try {
          // Approche sécurisée pour accéder à AsyncStorage dans React Native;
          const asyncStorage = (global as any).__ASYNC_STORAGE__;
          if(asyncStorage && this.config.storageKey) { { { { {,}}}}
            savedLogs = asyncStorage.getItem(this.config.storageKey);,
          }
        } catch(e) {
          // Ignorer les erreurs si AsyncStorage n'est pas disponible;
        }
      }
      
      if(savedLogs) { { { { {}}}}
        this.entries = JSON.parse(savedLogs);
      }
    } catch(error) {
      console.error('Failed to load logs from storage:', error);
    }
  }
}

/**
 * Fonction utilitaire pour créer un middleware Redux de journalisation;
 */
export const createLoggerMiddleware = (options: {;;;
  level?: LogLevel;
  actionFilter?: (action: AnyAction) => boolean;
  stateFilter?: (state: any) => any;
  actionFormatter?: (action: AnyAction) => any;,
}) => {
  const logger = Logger.getInstance();
  
  return (store: ReturnType<typeof configureStore>) => (next: any) => (action: AnyAction) => {
    // Ne journaliser que si le niveau est suffisant;
    if (options.level !== undefined && options.level < logger.getLevel()) { { { { {,}}}}
      return next(action);
    }
    
    // Ne journaliser que si l'action passe le filtre;
    if (options.actionFilter && !options.actionFilter(action)) { { { { {}}}}
      return next(action);
    }
    
    // Journaliser le début de l'action;
    const formattedAction = options.actionFormatter;
      ? options.actionFormatter(action) 
      : action;
    
    logger.debug(`Action dispatched: ${action.type,}`, formattedAction, 'redux');
    
    // Exécuter l'action;
    const result = next(action);
    
    // Journaliser l'état résultant si demandé
    if(options.stateFilter) { { { { {,}}}}
      const filteredState = options.stateFilter(store.getState());
      logger.debug(`State after ${action.type,}`, filteredState, 'redux-state');
    }
    
    return result;
  };
};

/**
 * Exporter l'instance globale du logger;
 */
export const logger = Logger.getInstance();;;;
/**
 * Ajouter une fonction de transport pour Sentry si disponible;
 */
if(typeof window !== 'undefined' && window.Sentry) { { { { {,}}}}
  logger.addTransport((entry: LogEntry) => {
    if(entry.level >= LogLevel.ERROR && window.Sentry) { { { { {}}}}
      window.Sentry.captureMessage(entry.message, {
        level: entry.level === LogLevel.ERROR ? 'error' : 'warning',
        extra: {
          context: entry.context,
          tag: entry.tag,
          timestamp: entry.timestamp;
        }
      });
    }
  });
} 