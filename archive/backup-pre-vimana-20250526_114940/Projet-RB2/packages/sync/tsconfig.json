{"compilerOptions": {"target": "es2018", "module": "commonjs", "lib": ["es2018", "dom"], "declaration": true, "outDir": "dist", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "react-native", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true}, "include": ["src"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}