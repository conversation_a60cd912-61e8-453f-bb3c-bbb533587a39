# Module de Synchronisation pour Projet-RB2

Ce module fournit des fonctionnalités complètes de synchronisation pour le projet RB2, compatibles avec les plateformes Web, iOS et Android.

## Fonctionnalités

### 1. Gestion des WebSockets
- Connexion automatique et reconnexion
- Gestion des événements WebSocket
- Support des salles (rooms)
- Gestion des erreurs et des timeouts

### 2. Synchronisation en Temps Réel
- Synchronisation bidirectionnelle des données
- Différentes stratégies de synchronisation
- Gestion des événements de synchronisation
- Support pour la synchronisation périodique

### 3. Gestion Hors Ligne
- File d'attente pour les opérations hors ligne
- Persistance des opérations
- Synchronisation automatique lors de la reconnexion
- Gestion des priorités et des dépendances

### 4. Gestion des Conflits
- Détection des conflits entre versions client et serveur
- Stratégies de résolution de conflits (client-wins, server-wins, merge, manual)
- Calcul des différences entre versions
- Interface pour la résolution manuelle des conflits

### 5. Cache d'Assets
- Mise en cache des ressources (images, vidéos, audio, documents)
- Gestion de la taille et de l'âge du cache
- Préchargement des ressources
- Statistiques d'utilisation du cache

### 6. Information Réseau
- Détection de l'état du réseau
- Notification des changements d'état du réseau
- Support pour différents types de connexions

### 7. Configuration d'Environnement
- Gestion des variables d'environnement
- Support pour différents environnements (dev, staging, prod)
- Feature flags pour activer/désactiver des fonctionnalités
- Configuration spécifique à la plateforme

## Installation

```bash
npm install @projet-rb2/sync
# ou
yarn add @projet-rb2/sync
```

## Utilisation

### WebSocket Manager

```typescript
import { WebSocketManager } from '@projet-rb2/sync';

// Obtenir l'instance singleton
const wsManager = WebSocketManager.getInstance({
  url: 'wss://example.com/socket',
  autoConnect: true,
  reconnectionAttempts: 5,
});

// Écouter les événements
wsManager.on('connect', () => {
  console.log('Connected to WebSocket server');
});

// Envoyer un message
wsManager.emit('message', { text: 'Hello, world!' });

// Rejoindre une salle
wsManager.join('room1');

// Quitter une salle
wsManager.leave('room1');
```

### Synchronisation en Temps Réel

```typescript
import { RealtimeSync } from '@projet-rb2/sync';

// Obtenir l'instance singleton
const realtimeSync = RealtimeSync.getInstance();

// S'abonner aux événements de synchronisation
realtimeSync.subscribe('users', (data) => {
  console.log('Received user data:', data);
});

// Envoyer des données
realtimeSync.send('users', { id: 1, name: 'John Doe' });

// Configurer la synchronisation périodique
realtimeSync.configurePeriodic('users', {
  interval: 60000, // 1 minute
  enabled: true,
});
```

### File d'Attente Hors Ligne

```typescript
import { OfflineQueue } from '@projet-rb2/sync';

// Obtenir l'instance singleton
const offlineQueue = OfflineQueue.getInstance();

// Ajouter une opération à la file d'attente
offlineQueue.enqueue({
  type: 'CREATE',
  resource: 'users',
  data: { name: 'John Doe' },
  priority: 1,
});

// Traiter la file d'attente
offlineQueue.process();

// S'abonner aux changements d'état de la file d'attente
offlineQueue.subscribe((state) => {
  console.log('Queue state:', state);
});
```

### Gestionnaire de Conflits

```typescript
import { ConflictManager, ConflictResolutionStrategy } from '@projet-rb2/sync';

// Obtenir l'instance singleton
const conflictManager = ConflictManager.getInstance({
  defaultStrategy: ConflictResolutionStrategy.SERVER_WINS,
  autoResolve: true,
});

// Détecter un conflit
const conflict = conflictManager.detectConflict(
  'users',
  '123',
  { name: 'John Doe', age: 30 },
  { name: 'John Doe', age: 25 },
);

// Résoudre un conflit
if (conflict) {
  const resolved = conflictManager.resolveConflict(
    conflict.id,
    ConflictResolutionStrategy.MERGE,
  );
  console.log('Resolved version:', resolved?.resolvedVersion);
}
```

### Cache d'Assets

```typescript
import { AssetCache, AssetType } from '@projet-rb2/sync';

// Obtenir l'instance singleton
const assetCache = AssetCache.getInstance({
  maxSize: 50 * 1024 * 1024, // 50 MB
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 jours
});

// Mettre en cache un asset
const key = await assetCache.cacheFromUrl(
  'https://example.com/image.jpg',
  {
    type: AssetType.IMAGE,
    metadata: { alt: 'Example image' },
  },
);

// Récupérer un asset du cache
const image = await assetCache.get(key);

// Précharger des assets
assetCache.preloadAssets([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg',
]);

// Obtenir les statistiques du cache
const stats = assetCache.getStats();
console.log('Cache stats:', stats);
```

### Information Réseau

```typescript
import { isNetworkAvailable, getNetworkType, onNetworkStateChange } from '@projet-rb2/sync';

// Vérifier si le réseau est disponible
const available = await isNetworkAvailable();
console.log('Network available:', available);

// Obtenir le type de réseau
const networkType = await getNetworkType();
console.log('Network type:', networkType);

// S'abonner aux changements d'état du réseau
const unsubscribe = onNetworkStateChange((state) => {
  console.log('Network state changed:', state);
});

// Se désabonner
unsubscribe();
```

### Configuration d'Environnement

```typescript
import { EnvironmentConfig } from '@projet-rb2/sync';

// Obtenir l'instance singleton
const envConfig = EnvironmentConfig.getInstance();

// Obtenir une variable d'environnement
const apiUrl = envConfig.getApiUrl();
console.log('API URL:', apiUrl);

// Vérifier si un feature flag est activé
const isFeatureEnabled = envConfig.isFeatureEnabled('enableExperimentalFeatures');
console.log('Experimental features enabled:', isFeatureEnabled);

// Définir un feature flag
envConfig.setFeatureFlag('enableDebugMode', true);

// Vérifier l'environnement
if (envConfig.isDevelopment()) {
  console.log('Running in development mode');
}
```

## Licence

MIT
