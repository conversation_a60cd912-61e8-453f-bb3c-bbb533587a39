{"name": "@projet-rb2/sync", "version": "0.1.0", "description": "Synchronisation et gestion d'état pour Projet-RB2", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "clean": "rm -rf dist", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"socket.io-client": "^4.7.2", "rxjs": "^7.8.1", "localforage": "^1.10.0", "uuid": "^9.0.0", "fast-json-patch": "^3.1.1", "eventemitter3": "^5.0.1"}, "peerDependencies": {"react": ">=16.8.0", "react-native": ">=0.60.0"}, "devDependencies": {"@types/jest": "^29.5.3", "@types/node": "^20.4.5", "@types/react": "^18.2.18", "@types/react-native": "^0.72.2", "@types/uuid": "^9.0.2", "eslint": "^8.46.0", "jest": "^29.6.2", "ts-jest": "^29.1.1", "typescript": "^5.1.6"}}