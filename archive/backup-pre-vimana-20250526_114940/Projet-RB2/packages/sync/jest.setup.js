// Mock pour les API de navigateur qui pourraient ne pas être disponibles dans l'environnement de test

// Mock pour localStorage
if (typeof localStorage === 'undefined') {
  global.localStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
}

// Mock pour sessionStorage
if (typeof sessionStorage === 'undefined') {
  global.sessionStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
}

// Mock pour window.fetch si nécessaire
if (typeof fetch === 'undefined') {
  global.fetch = jest.fn().mockImplementation(() => {
    return Promise.resolve({
      ok: true,
      json: () => Promise.resolve({}),
      text: () => Promise.resolve(''),
      blob: () => Promise.resolve(new Blob()),
      headers: new Headers({
        'content-type': 'application/json',
        'content-length': '0',
      }),
    });
  });
}

// Mock pour Blob
if (typeof Blob === 'undefined') {
  global.Blob = class Blob {
    constructor(content, options) {
      this.content = content || [];
      this.options = options || {};
      this.size = this.content.reduce((acc, val) => acc + (val.length || 0), 0);
      this.type = this.options.type || '';
    }
  };
}

// Mock pour Headers
if (typeof Headers === 'undefined') {
  global.Headers = class Headers {
    constructor(init) {
      this.headers = {};
      if (init) {
        Object.entries(init).forEach(([key, value]) => {
          this.headers[key.toLowerCase()] = value;
        });
      }
    }
    append(name, value) {
      this.headers[name.toLowerCase()] = value;
    }
    delete(name) {
      delete this.headers[name.toLowerCase()];
    }
    get(name) {
      return this.headers[name.toLowerCase()] || null;
    }
    has(name) {
      return name.toLowerCase() in this.headers;
    }
    set(name, value) {
      this.headers[name.toLowerCase()] = value;
    }
  };
}

// Mock pour URL
if (typeof URL === 'undefined') {
  global.URL = {
    createObjectURL: jest.fn(),
    revokeObjectURL: jest.fn(),
  };
}

// Mock pour navigator
if (typeof navigator === 'undefined') {
  global.navigator = {
    onLine: true,
    userAgent: 'jest-test-environment',
  };
}

// Mock pour uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-uuid-v4'),
}));

// Ajouter des utilitaires pour les tests
global.waitFor = async (condition, timeout = 5000, interval = 100) => {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    if (condition()) {
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  throw new Error('Timeout waiting for condition');
};

// Augmenter la précision des erreurs de Jest
expect.extend({
  toBeWithinRange(received, floor, ceiling) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },
});

// Réinitialiser tous les mocks après chaque test
beforeEach(() => {
  jest.clearAllMocks();
});

