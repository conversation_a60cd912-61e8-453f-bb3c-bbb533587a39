import { OfflineQueue, QueueItem, QueueItemStatus, OfflineQueueOptions } from '../src/offline/OfflineQueue';
import { jest } from '@jest/globals'; // Added for expect.any
import { NetworkState } from '../src/network/NetworkInfo'; // Import NetworkState type

// Mock pour localforage;
jest.mock('localforage', () => {
  const mockData: Record<string, any> = {};
  
  return {
    createInstance: jest.fn(() => ({
      getItem: jest.fn((key: string) => Promise.resolve(mockData[key])),
      setItem: jest.fn((key: string, value: any) => {
        mockData[key] = value;
        return Promise.resolve(value);
      }),
      removeItem: jest.fn((key: string) => {
        delete mockData[key];
        return Promise.resolve();
      }),
      clear: jest.fn(() => {
        Object.keys(mockData).forEach(key => delete mockData[key]);
        return Promise.resolve();
      }),
    })),
  };
});

// Mock pour NetworkInfo with typed callback
jest.mock('../src/network/NetworkInfo', () => ({
  isNetworkAvailable: jest.fn().mockResolvedValue(true),
  onNetworkStateChange: jest.fn((callback: (state: NetworkState) => void) => {
    callback({ isConnected: true, type: 'wifi' });
    return jest.fn(); // Retourne une fonction de désabonnement;
  }),
}));

describe('OfflineQueue', () => {
  let offlineQueue: OfflineQueue;
  
  beforeEach(() => {
    jest.clearAllMocks();
    // Réinitialiser l'instance singleton pour chaque test;
    (OfflineQueue as any).instance = undefined;
    offlineQueue = OfflineQueue.getInstance();
  });
  
  it('should create a singleton instance', () => {
    const instance1 = OfflineQueue.getInstance();
    const instance2 = OfflineQueue.getInstance();
    expect(instance1).toBe(instance2);
  });
  
  it('should add items to the queue', async () => {
    const itemData = { name: 'John Doe' };
    // Simplify item based on required fields for add()
    const itemToAdd: Omit<QueueItem, 'id' | 'status' | 'createdAt' | 'retryCount'> = {
      operation: 'create',
      resourceType: 'users',
      data: itemData,
      priority: 1,
      maxRetries: 3,
      resourceId: undefined // Provide optional fields if necessary
    };

    const id = await offlineQueue.add(itemToAdd);
    expect(id).toBeDefined();

    // Vérifier que l'item a été ajouté à la file d'attente;
    const queue = offlineQueue.getQueueObservable().getValue(); // Use getQueueObservable()
    expect(queue.length).toBe(1);
    const addedItem = queue[0];
    expect(addedItem.id).toBe(id);
    expect(addedItem.operation).toBe(itemToAdd.operation);
    expect(addedItem.resourceType).toBe(itemToAdd.resourceType);
    expect(addedItem.data).toEqual(itemToAdd.data);
    expect(addedItem.priority).toBe(itemToAdd.priority);
    expect(addedItem.status).toBe(QueueItemStatus.PENDING);
  });
  
  it('should process items in the queue', async () => {
    // Mock pour le processeur d'items - resolve with void/undefined
    const mockProcessor = jest.fn().mockResolvedValue(undefined);
    offlineQueue.registerProcessor('users', mockProcessor);

    // Ajouter un item à la file d'attente;
    const itemData = { name: 'John Doe' };
    const itemToAdd: Omit<QueueItem, 'id' | 'status' | 'createdAt' | 'retryCount'> = {
      operation: 'create',
      resourceType: 'users',
      data: itemData,
      priority: 1,
      maxRetries: 3,
      resourceId: undefined
    };
    const id = await offlineQueue.add(itemToAdd);

    // Traiter la file d'attente;
    await offlineQueue.processQueue();

    // Vérifier que le processeur a été appelé
    expect(mockProcessor).toHaveBeenCalledTimes(1);
    const calledWithItem = mockProcessor.mock.calls[0][0] as QueueItem; // Type the arg
    expect(calledWithItem.id).toBe(id);
    expect(calledWithItem.operation).toBe(itemToAdd.operation);
    expect(calledWithItem.resourceType).toBe(itemToAdd.resourceType);
    expect(calledWithItem.data).toEqual(itemToAdd.data);

    // Vérifier que l'item a été marqué comme traité
    const queue = offlineQueue.getQueueObservable().getValue();
    const processedItem = queue.find(item => item.id === id);
    expect(processedItem?.status).toBe(QueueItemStatus.COMPLETED);
  });
  
  it('should handle failed processing', async () => {
    // Mock pour le processeur d'items qui échoue;
    const mockProcessor = jest.fn().mockRejectedValue(new Error('Processing failed'));
    offlineQueue.registerProcessor('users', mockProcessor);

    // Ajouter un item à la file d'attente;
    const itemData = { name: 'John Doe' };
    const itemToAdd: Omit<QueueItem, 'id' | 'status' | 'createdAt' | 'retryCount'> = {
      operation: 'create',
      resourceType: 'users',
      data: itemData,
      priority: 1,
      maxRetries: 3,
      resourceId: undefined
    };
    const id = await offlineQueue.add(itemToAdd);

    // Traiter la file d'attente;
    await offlineQueue.processQueue();

    // Vérifier que le processeur a été appelé
    expect(mockProcessor).toHaveBeenCalled();

    // Vérifier que l'item a été marqué comme échoué mais toujours en attente;
    const queue = offlineQueue.getQueueObservable().getValue();
    const failedItem = queue.find(item => item.id === id);
    expect(failedItem?.status).toBe(QueueItemStatus.PENDING);
    expect(failedItem?.retryCount).toBe(1);
    expect(failedItem?.errorMessage).toBe('Processing failed');
  });
  
  it('should mark items as failed after max attempts', async () => {
    // Mock pour le processeur d'items qui échoue;
    const mockProcessor = jest.fn().mockRejectedValue(new Error('Processing failed'));
    offlineQueue.registerProcessor('users', mockProcessor);

    // Ajouter un item à la file d'attente avec un maximum de 2 tentatives;
    const itemData = { name: 'John Doe' };
    const itemToAdd: Omit<QueueItem, 'id' | 'status' | 'createdAt' | 'retryCount'> = {
      operation: 'create',
      resourceType: 'users',
      data: itemData,
      priority: 1,
      maxRetries: 2,
      resourceId: undefined
    };
    const id = await offlineQueue.add(itemToAdd);

    // Première tentative;
    await offlineQueue.processQueue();
    let queue = offlineQueue.getQueueObservable().getValue();
    let failedItem = queue.find(item => item.id === id);
    expect(failedItem?.retryCount).toBe(1);
    expect(failedItem?.status).toBe(QueueItemStatus.PENDING);

    // Deuxième tentative;
    await offlineQueue.processQueue();
    queue = offlineQueue.getQueueObservable().getValue();
    failedItem = queue.find(item => item.id === id);
    expect(failedItem?.retryCount).toBe(2);
    expect(failedItem?.status).toBe(QueueItemStatus.FAILED);
  });
  
  it('should clear the queue', async () => {
    // Ajouter quelques items à la file d'attente;
    await offlineQueue.add({
      operation: 'create',
      resourceType: 'users',
      data: { name: 'John Doe' },
      priority: 1,
      maxRetries: 3,
      resourceId: undefined
    });

    await offlineQueue.add({
      operation: 'update',
      resourceType: 'posts',
      resourceId: '1',
      data: { title: 'Jane Doe' },
      priority: 2,
      maxRetries: 3
    });

    // Vérifier que les items ont été ajoutés;
    let queue = offlineQueue.getQueueObservable().getValue();
    expect(queue.length).toBe(2);

    // Vider la file d'attente;
    await offlineQueue.clearQueue();

    // Vérifier que la file d'attente est vide;
    queue = offlineQueue.getQueueObservable().getValue();
    expect(queue.length).toBe(0);
  });
  
  it('should prioritize items correctly', async () => {
    // Ajouter des items avec différentes priorités;
    await offlineQueue.add({
      operation: 'create',
      resourceType: 'tasks',
      data: { name: 'Low Priority' },
      priority: 10,
      maxRetries: 3,
      resourceId: undefined
    });

    await offlineQueue.add({
      operation: 'update',
      resourceType: 'projects',
      resourceId: '1',
      data: { name: 'High Priority' },
      priority: 100,
      maxRetries: 3
    });

    await offlineQueue.add({
      operation: 'delete',
      resourceType: 'comments',
      resourceId: '2',
      data: { },
      priority: 50,
      maxRetries: 3
    });

    // Mock pour le processeur d'items;
    const processedItems: QueueItem[] = [];
    const mockProcessor = jest.fn((item: QueueItem) => {
      processedItems.push(item);
      return Promise.resolve(undefined); // Resolve with void/undefined
    });

    // Register processors for each resource type used
    offlineQueue.registerProcessor('tasks', mockProcessor);
    offlineQueue.registerProcessor('projects', mockProcessor);
    offlineQueue.registerProcessor('comments', mockProcessor);

    // Traiter la file d'attente;
    await offlineQueue.processQueue();

    // Vérifier l'ordre de traitement (higher priority number first)
    expect(processedItems.length).toBe(3);
    expect(processedItems[0].priority).toBe(100); // High
    expect(processedItems[1].priority).toBe(50);  // Medium
    expect(processedItems[2].priority).toBe(10);  // Low
    expect(processedItems[0].data).toEqual({ name: 'High Priority' });
    expect(processedItems[1].resourceId).toEqual('2');
    expect(processedItems[2].data).toEqual({ name: 'Low Priority' });
  });
  
  it('should notify subscribers of queue state changes', async () => {
    const callback = jest.fn();
    const subscription = offlineQueue.getQueueObservable().subscribe(callback);
    
    // Ajouter un item à la file d'attente;
    await offlineQueue.add({
      operation: 'create',
      resourceType: 'users',
      data: { name: 'John Doe' },
      priority: 1,
      maxRetries: 3,
      resourceId: undefined
    });

    // Callback is called initially and after add
    expect(callback).toHaveBeenCalledTimes(2);
    // Check queue in second call (after add)
    const secondCallArgs = callback.mock.calls[1][0] as QueueItem[];
    expect(secondCallArgs.length).toBe(1);
    
    // Nettoyer;
    subscription.unsubscribe();
  });
});
