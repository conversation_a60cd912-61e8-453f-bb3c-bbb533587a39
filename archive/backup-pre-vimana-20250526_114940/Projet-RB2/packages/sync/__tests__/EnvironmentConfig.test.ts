import { EnvironmentConfig, Environment } from '../src/config/EnvironmentConfig';

// Mock pour Platform de react-native;
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
    select: jest.fn((obj) => obj.ios || obj.default),
  },
}));

describe('EnvironmentConfig', () => {
  let envConfig: EnvironmentConfig;
  
  beforeEach(() => {
    // Réinitialiser l'instance singleton pour chaque test;
    (EnvironmentConfig as any).instance = undefined;
    envConfig = EnvironmentConfig.getInstance();
  });
  
  afterEach(() => {
    // Réinitialiser les mocks;
    jest.clearAllMocks();
  });
  
  it('should create a singleton instance', () => {
    const instance1 = EnvironmentConfig.getInstance();
    const instance2 = EnvironmentConfig.getInstance();
    expect(instance1).toBe(instance2);
  });
  
  it('should set and get environment variables', () => {
    // Définir une variable d'environnement;
    envConfig.set('API_URL', 'https://api.test.com');
    
    // Récupérer la variable;
    const apiUrl = envConfig.get('API_URL');
    
    expect(apiUrl).toBe('https://api.test.com');
  });
  
  it('should get all environment variables', () => {
    // Récupérer toutes les variables;
    const allVars = envConfig.getAll();
    
    // Vérifier que les variables essentielles sont présentes;
    expect(allVars).toHaveProperty('API_URL');
    expect(allVars).toHaveProperty('WEBSOCKET_URL');
    expect(allVars).toHaveProperty('ENVIRONMENT');
  });
  
  it('should check if feature flags are enabled', () => {
    // Get current flags, modify, and set them back
    const currentFlags = envConfig.getFeatureFlags();
    const modifiedFlags = {
      ...currentFlags,
      TEST_FEATURE: true,
    };
    envConfig.set('FEATURE_FLAGS', modifiedFlags);

    // Vérifier que le feature flag est activé
    expect(envConfig.isFeatureEnabled('TEST_FEATURE')).toBe(true);

    // Vérifier qu'un feature flag non défini est désactivé
    expect(envConfig.isFeatureEnabled('NON_EXISTENT_FEATURE')).toBe(false);
  });
  
  it('should get all feature flags', () => {
    // Set flags by modifying the whole object
    const newFlags = {
      FEATURE1: true,
      FEATURE2: false,
    };
    envConfig.set('FEATURE_FLAGS', newFlags);

    // Récupérer tous les feature flags;
    const featureFlags = envConfig.getFeatureFlags();

    // Vérifier que les feature flags sont présents;
    // Note: getFeatureFlags() might return default flags merged with overrides
    // Adjust expectations based on actual merging behavior if needed
    expect(featureFlags).toHaveProperty('FEATURE1', true);
    expect(featureFlags).toHaveProperty('FEATURE2', false);
  });
  
  it('should check environment types', () => {
    // Par défaut, l'environnement est TEST dans l'environnement de test Jest;
    expect(envConfig.getEnvironment()).toBe(Environment.TEST);
    expect(envConfig.isTest()).toBe(true);
    expect(envConfig.isProduction()).toBe(false);
    
    // Modifier l'environnement;
    (envConfig as any).config.ENVIRONMENT = Environment.PRODUCTION;
    
    // Vérifier que les méthodes reflètent le changement;
    expect(envConfig.getEnvironment()).toBe(Environment.PRODUCTION);
    expect(envConfig.isProduction()).toBe(true);
    expect(envConfig.isTest()).toBe(false);
  });
  
  it('should get API URLs', () => {
    // Vérifier les URL par défaut;
    expect(envConfig.getApiUrl()).toBeDefined();
    expect(envConfig.getWebsocketUrl()).toBeDefined();
    expect(envConfig.getAuthUrl()).toBeDefined();
    expect(envConfig.getMediaUrl()).toBeDefined();
    
    // Modifier une URL;
    envConfig.set('API_URL', 'https://custom-api.example.com');
    
    // Vérifier que la modification est prise en compte;
    expect(envConfig.getApiUrl()).toBe('https://custom-api.example.com');
  });
  
  it('should get app version and build number', () => {
    expect(envConfig.getAppVersion()).toBeDefined();
    expect(envConfig.getBuildNumber()).toBeDefined();
  });
  
  it('should check debug mode', () => {
    // Vérifier l'état initial du mode debug;
    const initialDebugState = envConfig.isDebug();
    
    // Inverser l'état du mode debug;
    envConfig.set('DEBUG', !initialDebugState);
    
    // Vérifier que le changement est pris en compte;
    expect(envConfig.isDebug()).toBe(!initialDebugState);
  });
});
