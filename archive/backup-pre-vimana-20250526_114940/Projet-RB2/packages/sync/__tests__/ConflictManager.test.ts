import { ConflictManager, ConflictResolutionStrategy, Conflict } from '../src/conflict/ConflictManager';
import { jest } from '@jest/globals'; // Import jest types

describe('ConflictManager', () => {
  let conflictManager: ConflictManager;
  
  beforeEach(() => {
    jest.clearAllMocks();
    // Réinitialiser l'instance singleton pour chaque test;
    (ConflictManager as any).instance = undefined;
    conflictManager = ConflictManager.getInstance({
      defaultStrategy: ConflictResolutionStrategy.SERVER_WINS,
      autoResolve: false,
    });
  });
  
  it('should create a singleton instance', () => {
    const instance1 = ConflictManager.getInstance();
    const instance2 = ConflictManager.getInstance();
    expect(instance1).toBe(instance2);
  });
  
  it('should detect conflicts between versions', () => {
    const clientVersion = { name: '<PERSON>', age: 30 };
    const serverVersion = { name: '<PERSON>', age: 25 };
    
    const conflict = conflictManager.detectConflict(
      'users',
      '123',
      clientVersion,
      serverVersion
    );
    
    expect(conflict).not.toBeNull();
    expect(conflict?.resourceType).toBe('users');
    expect(conflict?.resourceId).toBe('123');
    expect(conflict?.clientVersion).toEqual(clientVersion);
    expect(conflict?.serverVersion).toEqual(serverVersion);
    expect(conflict?.resolved).toBe(false);
    expect(conflict?.diff).toBeDefined();
  });
  
  it('should not detect conflicts when versions are identical', () => {
    const clientVersion = { name: 'John Doe', age: 30 };
    const serverVersion = { name: 'John Doe', age: 30 };
    
    const conflict = conflictManager.detectConflict(
      'users',
      '123',
      clientVersion,
      serverVersion
    );
    
    expect(conflict).toBeNull();
  });
  
  it('should resolve conflicts with SERVER_WINS strategy', () => {
    const clientVersion = { name: 'John Doe', age: 30 };
    const serverVersion = { name: 'John Doe', age: 25 };
    
    const conflict = conflictManager.detectConflict(
      'users',
      '123',
      clientVersion,
      serverVersion
    );
    
    if (!conflict) {
      fail('Conflict should be detected');
      return;
    }
    
    const resolvedConflict = conflictManager.resolveConflict(
      conflict.id,
      ConflictResolutionStrategy.SERVER_WINS
    );
    
    expect(resolvedConflict).not.toBeNull();
    expect(resolvedConflict?.resolved).toBe(true);
    expect(resolvedConflict?.resolvedVersion).toEqual(serverVersion);
    expect(resolvedConflict?.strategy).toBe(ConflictResolutionStrategy.SERVER_WINS);
  });
  
  it('should resolve conflicts with CLIENT_WINS strategy', () => {
    const clientVersion = { name: 'John Doe', age: 30 };
    const serverVersion = { name: 'John Doe', age: 25 };
    
    const conflict = conflictManager.detectConflict(
      'users',
      '123',
      clientVersion,
      serverVersion
    );
    
    if (!conflict) {
      fail('Conflict should be detected');
      return;
    }
    
    const resolvedConflict = conflictManager.resolveConflict(
      conflict.id,
      ConflictResolutionStrategy.CLIENT_WINS
    );
    
    expect(resolvedConflict).not.toBeNull();
    expect(resolvedConflict?.resolved).toBe(true);
    expect(resolvedConflict?.resolvedVersion).toEqual(clientVersion);
    expect(resolvedConflict?.strategy).toBe(ConflictResolutionStrategy.CLIENT_WINS);
  });
  
  it('should resolve conflicts with LAST_WRITE_WINS strategy', () => {
    const clientVersion = { name: 'John Doe', age: 30 };
    const serverVersion = { name: 'John Doe', age: 25 };
    const clientTime = Date.now();
    const serverTime = clientTime - 1000; // 1 seconde plus tôt
    
    const conflict = conflictManager.detectConflict(
      'users',
      '123',
      clientVersion,
      serverVersion,
      {
        clientTimestamp: clientTime,
        serverTimestamp: serverTime,
      }
    );
    
    if (!conflict) {
      fail('Conflict should be detected');
      return;
    }
    
    const resolvedConflict = conflictManager.resolveConflict(
      conflict.id,
      ConflictResolutionStrategy.LAST_WRITE_WINS
    );
    
    expect(resolvedConflict).not.toBeNull();
    expect(resolvedConflict?.resolved).toBe(true);
    expect(resolvedConflict?.resolvedVersion).toEqual(clientVersion);
    expect(resolvedConflict?.strategy).toBe(ConflictResolutionStrategy.LAST_WRITE_WINS);
  });
  
  it('should resolve conflicts with MERGE strategy', () => {
    const clientVersion = { name: 'John Doe', age: 30, email: '<EMAIL>' };
    const serverVersion = { name: 'John Doe', age: 25, phone: '************' };
    
    const conflict = conflictManager.detectConflict(
      'users',
      '123',
      clientVersion,
      serverVersion
    );
    
    if (!conflict) {
      fail('Conflict should be detected');
      return;
    }
    
    const resolvedConflict = conflictManager.resolveConflict(
      conflict.id,
      ConflictResolutionStrategy.MERGE
    );
    
    expect(resolvedConflict).not.toBeNull();
    expect(resolvedConflict?.resolved).toBe(true);
    
    const mergedVersion = resolvedConflict?.resolvedVersion as any;
    expect(mergedVersion.name).toBe('John Doe');
    expect(mergedVersion.age).toBe(30);
    expect(mergedVersion.email).toBe('<EMAIL>');
    expect(mergedVersion.phone).toBe('************');
  });
  
  it('should register custom strategy handlers', () => {
    const customStrategy = 'AVERAGE_AGE' as any as ConflictResolutionStrategy;
    
    conflictManager.registerStrategyHandler(
      customStrategy,
      (conflict: Conflict<{ age: number }>) => {
        const clientAge = conflict.clientVersion.age;
        const serverAge = conflict.serverVersion.age;
        const averageAge = Math.round((clientAge + serverAge) / 2);
        
        return {
          ...conflict.serverVersion,
          age: averageAge,
        };
      }
    );
    
    const clientVersion = { name: 'John Doe', age: 30 };
    const serverVersion = { name: 'John Doe', age: 20 };
    
    const conflict = conflictManager.detectConflict(
      'users',
      '123',
      clientVersion,
      serverVersion
    );
    
    if (!conflict) {
      fail('Conflict should be detected');
      return;
    }
    
    const resolvedConflict = conflictManager.resolveConflict(
      conflict.id,
      customStrategy
    );
    
    expect(resolvedConflict).not.toBeNull();
    expect(resolvedConflict?.resolved).toBe(true);
    expect((resolvedConflict?.resolvedVersion as any).age).toBe(25);
  });
  
  it('should notify subscribers of conflict changes', (done: jest.DoneCallback) => {
    let callCount = 0;
    
    conflictManager.subscribe().subscribe((conflicts: Conflict[]) => {
      callCount++;
      
      if (callCount === 1) {
        expect(conflicts.length).toBe(0);
      } else if (callCount === 2) {
        expect(conflicts.length).toBe(1);
        expect(conflicts[0].resolved).toBe(false);
        
        conflictManager.resolveConflict(
          conflicts[0].id,
          ConflictResolutionStrategy.SERVER_WINS
        );
      } else if (callCount === 3) {
        expect(conflicts.length).toBe(1);
        expect(conflicts[0].resolved).toBe(true);
        done();
      }
    });
    
    const clientVersion = { name: 'John Doe', age: 30 };
    const serverVersion = { name: 'John Doe', age: 25 };
    
    conflictManager.detectConflict(
      'users',
      '123',
      clientVersion,
      serverVersion
    );
  });
  
  it('should get unresolved conflicts', () => {
    const conflict1 = conflictManager.detectConflict(
      'users',
      '123',
      { name: 'John', age: 30 },
      { name: 'John', age: 25 }
    );
    
    const conflict2 = conflictManager.detectConflict(
      'posts',
      '456',
      { title: 'Hello', content: 'World' },
      { title: 'Hello', content: 'Universe' }
    );
    
    if (conflict1) {
      conflictManager.resolveConflict(
        conflict1.id,
        ConflictResolutionStrategy.SERVER_WINS
      );
    }
    
    const unresolvedConflicts = conflictManager.getUnresolvedConflicts();
    expect(unresolvedConflicts.length).toBe(1);
    expect(unresolvedConflicts[0].resourceType).toBe('posts');
  });
  
  it('should remove conflicts', () => {
    const conflict = conflictManager.detectConflict(
      'users',
      '123',
      { name: 'John', age: 30 },
      { name: 'John', age: 25 }
    );
    
    if (!conflict) {
      fail('Conflict should be detected');
      return;
    }
    
    expect(conflictManager.getConflicts().length).toBe(1);
    
    const result = conflictManager.removeConflict(conflict.id);
    expect(result).toBe(true);
    
    expect(conflictManager.getConflicts().length).toBe(0);
  });
  
  it('should clear all conflicts', () => {
    conflictManager.detectConflict(
      'users',
      '123',
      { name: 'John', age: 30 },
      { name: 'John', age: 25 }
    );
    
    conflictManager.detectConflict(
      'posts',
      '456',
      { title: 'Hello', content: 'World' },
      { title: 'Hello', content: 'Universe' }
    );
    
    expect(conflictManager.getConflicts().length).toBe(2);
    
    conflictManager.clearConflicts();
    
    expect(conflictManager.getConflicts().length).toBe(0);
  });
});
