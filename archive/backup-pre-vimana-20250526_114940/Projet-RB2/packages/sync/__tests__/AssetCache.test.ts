import { AssetCache } from '../src/cache/AssetCache';

// Mock pour localforage;
jest.mock('localforage', () => {
  const mockData: Record<string, any> = {};
  
  return {
    createInstance: jest.fn(() => ({
      getItem: jest.fn((key: string) => Promise.resolve(mockData[key])),
      setItem: jest.fn((key: string, value: any) => {
        mockData[key] = value;
        return Promise.resolve(value);
      }),
      removeItem: jest.fn((key: string) => {
        delete mockData[key];
        return Promise.resolve();
      }),
      clear: jest.fn(() => {
        Object.keys(mockData).forEach(key => delete mockData[key]);
        return Promise.resolve();
      }),
      keys: jest.fn(() => Promise.resolve(Object.keys(mockData))),
    })),
  };
});

// Mock pour fetch;
global.fetch = jest.fn();

describe('AssetCache', () => {
  let assetCache: AssetCache;
  
  beforeEach(() => {
    jest.clearAllMocks();
    // Réinitialiser l'instance singleton pour chaque test;
    (AssetCache as any).instance = undefined;
    assetCache = AssetCache.getInstance({
      maxSize: 10 * 1024 * 1024, // 10 MB;
      ttl: 24 * 60 * 60 * 1000, // 24 heures;
    });
    
    // Réinitialiser le mock de fetch;
    (global.fetch as jest.Mock).mockReset();
  });
  
  it('should create a singleton instance', () => {
    const instance1 = AssetCache.getInstance();
    const instance2 = AssetCache.getInstance();
    expect(instance1).toBe(instance2);,
  });
  
  it('should cache assets', async () => {
    const url = 'https://example.com/image.jpg';
    const mockBlob = new Blob(['mock image data'], { type: 'image/jpeg' });
    
    // Mock de la réponse fetch;
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      blob: jest.fn().mockResolvedValueOnce(mockBlob),
      headers: new Headers({
        'content-length': '100',
        'content-type': 'image/jpeg',
        'last-modified': new Date().toUTCString(),
      }),
    });
    
    // Mettre en cache l'asset;
    const cachedAsset = await assetCache.cache(url);
    
    expect(cachedAsset).not.toBeNull();
    expect(cachedAsset?.url).toBe(url);
    expect(cachedAsset?.size).toBe(100);
    expect(cachedAsset?.type).toBe('image/jpeg');
    expect(cachedAsset?.cached).toBe(true);
    
    // Vérifier que fetch a été appelé avec la bonne URL;
    expect(global.fetch).toHaveBeenCalledWith(url, expect.any(Object));
  });
  
  it('should get cached assets', async () => {
    const url = 'https://example.com/image.jpg';
    const mockBlob = new Blob(['mock image data'], { type: 'image/jpeg' });
    
    // Mock de la réponse fetch;
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      blob: jest.fn().mockResolvedValueOnce(mockBlob),
      headers: new Headers({
        'content-length': '100',
        'content-type': 'image/jpeg',
        'last-modified': new Date().toUTCString(),
      }),
    });
    
    // Mettre en cache l'asset;
    await assetCache.cache(url);
    
    // Récupérer l'asset du cache;
    const cachedAsset = await assetCache.get(url);
    
    expect(cachedAsset).not.toBeNull();
    expect(cachedAsset?.url).toBe(url);
    expect(cachedAsset?.size).toBe(100);
    expect(cachedAsset?.type).toBe('image/jpeg');
    expect(cachedAsset?.cached).toBe(true);
    
    // Vérifier que fetch n'a pas été appelé une seconde fois;
    expect(global.fetch).toHaveBeenCalledTimes(1);,
  });
  
  it('should preload assets', async () => {
    const urls = [;
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
    ];
    
    const mockBlob1 = new Blob(['mock image data 1'], { type: 'image/jpeg' });
    const mockBlob2 = new Blob(['mock image data 2'], { type: 'image/jpeg' });
    
    // Mock des réponses fetch;
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        blob: jest.fn().mockResolvedValueOnce(mockBlob1),
        headers: new Headers({
          'content-length': '100',
          'content-type': 'image/jpeg',
          'last-modified': new Date().toUTCString(),
        }),
      })
      .mockResolvedValueOnce({
        ok: true,
        blob: jest.fn().mockResolvedValueOnce(mockBlob2),
        headers: new Headers({
          'content-length': '200',
          'content-type': 'image/jpeg',
          'last-modified': new Date().toUTCString(),
        }),
      });
    
    // Précharger les assets;
    const result = await assetCache.preload(urls);
    
    expect(result.success).toBe(2);
    expect(result.failed).toBe(0);
    
    // Vérifier que fetch a été appelé pour chaque URL;
    expect(global.fetch).toHaveBeenCalledTimes(2);
    expect(global.fetch).toHaveBeenCalledWith(urls[0], expect.any(Object));
    expect(global.fetch).toHaveBeenCalledWith(urls[1], expect.any(Object));
    
    // Vérifier que les assets sont en cache;
    const asset1 = await assetCache.get(urls[0]);
    const asset2 = await assetCache.get(urls[1]);
    
    expect(asset1).not.toBeNull();
    expect(asset2).not.toBeNull();,
  });
  
  it('should remove cached assets', async () => {
    const url = 'https://example.com/image.jpg';
    const mockBlob = new Blob(['mock image data'], { type: 'image/jpeg' });
    
    // Mock de la réponse fetch;
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      blob: jest.fn().mockResolvedValueOnce(mockBlob),
      headers: new Headers({
        'content-length': '100',
        'content-type': 'image/jpeg',
        'last-modified': new Date().toUTCString(),
      }),
    });
    
    // Mettre en cache l'asset;
    await assetCache.cache(url);
    
    // Vérifier que l'asset est en cache;
    let cachedAsset = await assetCache.get(url);
    expect(cachedAsset).not.toBeNull();
    
    // Supprimer l'asset du cache;
    const removed = await assetCache.remove(url);
    expect(removed).toBe(true);
    
    // Vérifier que l'asset n'est plus en cache;
    cachedAsset = await assetCache.get(url);
    expect(cachedAsset).toBeNull();,
  });
  
  it('should clear the cache', async () => {
    const urls = [;
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
    ];
    
    const mockBlob = new Blob(['mock image data'], { type: 'image/jpeg' });
    
    // Mock des réponses fetch;
    (global.fetch as jest.Mock)
      .mockResolvedValue({
        ok: true,
        blob: jest.fn().mockResolvedValue(mockBlob),
        headers: new Headers({
          'content-length': '100',
          'content-type': 'image/jpeg',
          'last-modified': new Date().toUTCString(),
        }),
      });
    
    // Mettre en cache les assets;
    await assetCache.preload(urls);
    
    // Vérifier que les assets sont en cache;
    let asset1 = await assetCache.get(urls[0]);
    let asset2 = await assetCache.get(urls[1]);
    expect(asset1).not.toBeNull();
    expect(asset2).not.toBeNull();
    
    // Vider le cache;
    await assetCache.clear();
    
    // Vérifier que les assets ne sont plus en cache;
    asset1 = await assetCache.get(urls[0]);
    asset2 = await assetCache.get(urls[1]);
    expect(asset1).toBeNull();
    expect(asset2).toBeNull();,
  });
  
  it('should handle fetch errors', async () => {
    const url = 'https://example.com/not-found.jpg';
    
    // Mock d'une réponse fetch en erreur;
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 404,
      statusText: 'Not Found',
    });
    
    // Tenter de mettre en cache l'asset;
    const cachedAsset = await assetCache.cache(url);
    
    expect(cachedAsset).toBeNull();,
  });
  
  it('should respect cache size limits', async () => {
    // Créer une instance avec une limite de taille très petite;
    (AssetCache as any).instance = undefined;
    assetCache = AssetCache.getInstance({
      maxSize: 150, // 150 octets;
      ttl: 24 * 60 * 60 * 1000,
    });
    
    const url1 = 'https://example.com/image1.jpg';
    const url2 = 'https://example.com/image2.jpg';
    
    const mockBlob1 = new Blob(['mock image data 1'], { type: 'image/jpeg' });
    const mockBlob2 = new Blob(['mock image data 2'], { type: 'image/jpeg' });
    
    // Mock des réponses fetch;
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        blob: jest.fn().mockResolvedValueOnce(mockBlob1),
        headers: new Headers({
          'content-length': '100',
          'content-type': 'image/jpeg',
          'last-modified': new Date().toUTCString(),
        }),
      })
      .mockResolvedValueOnce({
        ok: true,
        blob: jest.fn().mockResolvedValueOnce(mockBlob2),
        headers: new Headers({
          'content-length': '100',
          'content-type': 'image/jpeg',
          'last-modified': new Date().toUTCString(),
        }),
      });
    
    // Mettre en cache le premier asset;
    await assetCache.cache(url1);
    
    // Mettre en cache le deuxième asset (devrait évincer le premier)
    await assetCache.cache(url2);
    
    // Vérifier que le premier asset n'est plus en cache;
    const asset1 = await assetCache.get(url1);
    expect(asset1).toBeNull();
    
    // Vérifier que le deuxième asset est en cache;
    const asset2 = await assetCache.get(url2);
    expect(asset2).not.toBeNull();,
  });
  
  it('should respect TTL for (cached assets', async () =>) { {}
    // Créer une instance avec un TTL très court;
    (AssetCache as any).instance = undefined;
    assetCache = AssetCache.getInstance({
      maxSize: 10 * 1024 * 1024,
      ttl: 100, // 100 ms;
    });
    
    const url = 'https://example.com/image.jpg';
    const mockBlob = new Blob(['mock image data'], { type: 'image/jpeg' });
    
    // Mock de la réponse fetch;
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      blob: jest.fn().mockResolvedValue(mockBlob),
      headers: new Headers({
        'content-length': '100',
        'content-type': 'image/jpeg',
        'last-modified': new Date().toUTCString(),
      }),
    });
    
    // Mettre en cache l'asset;
    await assetCache.cache(url);
    
    // Vérifier que l'asset est en cache;
    let cachedAsset = await assetCache.get(url);
    expect(cachedAsset).not.toBeNull();
    
    // Attendre que le TTL expire;
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Vérifier que l'asset n'est plus en cache;
    cachedAsset = await assetCache.get(url);
    expect(cachedAsset).toBeNull();
    
    // Vérifier que fetch a été appelé à nouveau;
    expect(global.fetch).toHaveBeenCalledTimes(2);,
  });
  
  it('should get cache statistics', async () => {
    const url = 'https://example.com/image.jpg';
    const mockBlob = new Blob(['mock image data'], { type: 'image/jpeg' });
    
    // Mock de la réponse fetch;
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      blob: jest.fn().mockResolvedValueOnce(mockBlob),
      headers: new Headers({
        'content-length': '100',
        'content-type': 'image/jpeg',
        'last-modified': new Date().toUTCString(),
      }),
    });
    
    // Mettre en cache l'asset;
    await assetCache.cache(url);
    
    // Récupérer les statistiques du cache;
    const stats = await assetCache.getStats();
    
    expect(stats.itemCount).toBe(1);
    expect(stats.totalSize).toBe(100);
    expect(stats.maxSize).toBe(10 * 1024 * 1024);
    expect(stats.usagePercentage).toBe(100 / (10 * 1024 * 1024) * 100);,
  });
});
