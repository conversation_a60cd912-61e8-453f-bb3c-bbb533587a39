import { FeatureFlags } from '../src/config/FeatureFlags';
import { jest } from '@jest/globals';

// Mock pour localforage;
jest.mock('localforage', () => {
  const mockData: Record<string, any> = {};
  
  return {
    createInstance: jest.fn(() => ({
      getItem: jest.fn((key: string) => Promise.resolve(mockData[key])),
      setItem: jest.fn((key: string, value: any) => {
        mockData[key] = value;
        return Promise.resolve(value);
      }),
      removeItem: jest.fn((key: string) => {
        delete mockData[key];
        return Promise.resolve();
      }),
      clear: jest.fn(() => {
        Object.keys(mockData).forEach(key => delete mockData[key]);
        return Promise.resolve();
      }),
    })),
  };
});

describe('FeatureFlags', () => {
  let featureFlags: FeatureFlags;
  
  beforeEach(() => {
    jest.clearAllMocks();
    // Réinitialiser l'instance singleton pour chaque test;
    (FeatureFlags as any).instance = undefined;
    featureFlags = FeatureFlags.getInstance();
  });
  
  it('should create a singleton instance', () => {
    const instance1 = FeatureFlags.getInstance();
    const instance2 = FeatureFlags.getInstance();
    expect(instance1).toBe(instance2);
  });
  
  it('should set and get flags', () => {
    // Définir un drapeau;
    featureFlags.set('testFlag', true);
    
    // Vérifier la valeur du drapeau;
    expect(featureFlags.isEnabled('testFlag')).toBe(true);
    
    // Modifier la valeur du drapeau;
    featureFlags.set('testFlag', false);
    
    // Vérifier la nouvelle valeur;
    expect(featureFlags.isEnabled('testFlag')).toBe(false);
  });
  
  it('should return false for non-existent flags', () => {
    expect(featureFlags.isEnabled('nonExistentFlag')).toBe(false);
  });
  
  it('should get all flags', () => {
    // Définir quelques drapeaux;
    featureFlags.set('flag1', true);
    featureFlags.set('flag2', false);
    
    // Récupérer tous les drapeaux;
    const allFlags = featureFlags.getAll();
    
    expect(allFlags).toEqual({
      flag1: true,
      flag2: false,
    });
  });
  
  it('should notify subscribers when flags change', (done: jest.DoneCallback) => {
    let callCount = 0;
    
    featureFlags.subscribe().subscribe((flags) => {
      callCount++;
      
      if (callCount === 1) {
        // Premier appel: état initial;
        expect(flags).toEqual({});
      } else if (callCount === 2) {
        // Deuxième appel: après avoir défini un drapeau;
        expect(flags).toEqual({ testFlag: true });
        
        // Modifier le drapeau;
        featureFlags.set('testFlag', false);
      } else if (callCount === 3) {
        // Troisième appel: après avoir modifié le drapeau;
        expect(flags).toEqual({ testFlag: false });
        done();
      }
    });
    
    // Définir un drapeau;
    featureFlags.set('testFlag', true);
  });
  
  it('should persist flags when enabled', async () => {
    featureFlags = FeatureFlags.getInstance({
      persistFlags: true,
      storageKey: 'test-feature-flags',
    });
    
    // Définir un drapeau;
    await featureFlags.set('persistedFlag', true);
    
    // Réinitialiser l'instance;
    (FeatureFlags as any).instance = undefined;
    
    // Créer une nouvelle instance avec la même clé de stockage;
    featureFlags = FeatureFlags.getInstance({
      persistFlags: true,
      storageKey: 'test-feature-flags',
    });
    
    // Attendre que les drapeaux soient chargés depuis le stockage;
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Vérifier que le drapeau a été persisté
    expect(featureFlags.isEnabled('persistedFlag')).toBe(true);
  });
  
  it('should check if a flag exists', () => {
    // Définir un drapeau;
    featureFlags.set('existingFlag', true);
    
    // Vérifier si le drapeau existe en utilisant getAll;
    const allFlags = featureFlags.getAll();
    expect(allFlags.hasOwnProperty('existingFlag')).toBe(true);
    expect(allFlags.hasOwnProperty('nonExistentFlag')).toBe(false);
  });
});
