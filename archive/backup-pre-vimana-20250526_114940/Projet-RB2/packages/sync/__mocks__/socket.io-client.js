// Mock pour socket.io-client
const mockOn = jest.fn();
const mockEmit = jest.fn();
const mockConnect = jest.fn();
const mockDisconnect = jest.fn();
const mockClose = jest.fn();
const mockJoin = jest.fn();
const mockLeave = jest.fn();

const mockSocket = {
  on: mockOn,
  emit: mockEmit,
  connect: mockConnect,
  disconnect: mockDisconnect,
  close: mockClose,
  join: mockJoin,
  leave: mockLeave,
  connected: false,
  id: 'mock-socket-id',
};

module.exports = {
  io: jest.fn(() => mockSocket),
  Socket: jest.fn(),
  mockSocket,
  mockOn,
  mockEmit,
  mockConnect,
  mockDisconnect,
};
