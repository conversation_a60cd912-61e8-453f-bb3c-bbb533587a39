// Mock pour localforage
const mockData = {};

module.exports = {
  createInstance: jest.fn(() => ({
    getItem: jest.fn((key) => Promise.resolve(mockData[key])),
    setItem: jest.fn((key, value) => {
      mockData[key] = value;
      return Promise.resolve(value);
    }),
    removeItem: jest.fn((key) => {
      delete mockData[key];
      return Promise.resolve();
    }),
    clear: jest.fn(() => {
      Object.keys(mockData).forEach(key => delete mockData[key]);
      return Promise.resolve();
    }),
    keys: jest.fn(() => Promise.resolve(Object.keys(mockData))),
    length: jest.fn(() => Promise.resolve(Object.keys(mockData).length)),
    iterate: jest.fn((callback) => {
      Object.entries(mockData).forEach(([key, value]) => callback(value, key));
      return Promise.resolve();
    }),
  })),
  INDEXEDDB: 'INDEXEDDB',
  WEBSQL: 'WEBSQL',
  LOCALSTORAGE: 'LOCALSTORAGE',
};
