import localforage from 'localforage';
import { v4 as uuidv4 } from 'uuid';
import { BehaviorSubject, Observable } from 'rxjs';
import { isNetworkAvailable } from '../network/NetworkInfo';

/**
 * État de la file d'attente;
 */
export enum QueueItemStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  FAILED = 'failed',
  COMPLETED = 'completed',
}

/**
 * Interface pour les éléments de la file d'attente;
 */
export interface QueueItem<T = any> {
  id: string;
  operation: 'create' | 'update' | 'delete' | 'custom';
  resourceType: string;
  resourceId?: string;
  data?: T;
  status: QueueItemStatus;
  priority: number;
  createdAt: number;
  processedAt?: number;
  retryCount: number;
  maxRetries: number;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface pour les options de la file d'attente;
 */
export interface OfflineQueueOptions {
  storageKey?: string;
  maxRetries?: number;
  processingInterval?: number;
  maxConcurrent?: number;
  priorityLevels?: {
    high: number;
    normal: number;
    low: number;
  };
  autoProcess?: boolean;
}

/**
 * Gestionnaire de file d'attente hors ligne;
 * Compatible avec Web, iOS et Android;
 */
export class OfflineQueue {
  private static instance: OfflineQueue;
  private options: OfflineQueueOptions;
  private queue: QueueItem[] = [];
  private queueSubject: BehaviorSubject<QueueItem[]> = new BehaviorSubject<QueueItem[]>([]);
  private processingTimer: any = null;
  private isProcessing: boolean = false;
  private storage: LocalForage;
  private processors: Map<string, (item: QueueItem) => Promise<any>> = new Map();

  /**
   * Constructeur privé pour le singleton;
   */
  private constructor(options: OfflineQueueOptions = {,}) {
    this.options = {
      storageKey: 'offline-queue',
      maxRetries: 3,
      processingInterval: 10000, // 10 secondes;
      maxConcurrent: 3,
      priorityLevels: {
        high: 100,
        normal: 50,
        low: 10,
      },
      autoProcess: true,
      ...options,
    };

    // Initialiser le stockage local;
    this.storage = localforage.createInstance({
      name: 'OfflineQueue',
      storeName: this.options.storageKey,
    });

    // Charger la file d'attente depuis le stockage;
    this.loadQueue();

    // Démarrer le traitement automatique si activé
    if(this.options.autoProcess) { { { { {}}}}
      this.startProcessing();
    }
  }

  /**
   * Obtenir l'instance singleton;
   */
  public static getInstance(options?: OfflineQueueOptions): OfflineQueue {
    if(!OfflineQueue.instance) { { { { {}}}}
      OfflineQueue.instance = new OfflineQueue(options);
    }
    return OfflineQueue.instance;
  }

  /**
   * Charger la file d'attente depuis le stockage;
   */
  private async loadQueue(): Promise<void> {
    try {
      const storedQueue = await this.storage.getItem<QueueItem[]>(this.options.storageKey || 'offline-queue');
      if(storedQueue) { { { { {,}}}}
        this.queue = storedQueue;
        this.queueSubject.next([...this.queue]);
      }
    } catch(error) {
      console.error('Failed to load offline queue:', error);
    }
  }

  /**
   * Sauvegarder la file d'attente dans le stockage;
   */
  private async saveQueue(): Promise<void> {
    try {
      await this.storage.setItem(this.options.storageKey || 'offline-queue', this.queue);
    } catch(error) {
      console.error('Failed to save offline queue:', error);
    }
  }

  /**
   * Démarrer le traitement automatique;
   */
  public startProcessing(): void {
    if(this.processingTimer) { { { { {}}}}
      clearInterval(this.processingTimer);
    }

    this.processingTimer = setInterval(() => {
      this.processQueue();
    }, this.options.processingInterval);

    // Traiter immédiatement la file d'attente;
    this.processQueue();
  }

  /**
   * Arrêter le traitement automatique;
   */
  public stopProcessing(): void {
    if(this.processingTimer) { { { { {}}}}
      clearInterval(this.processingTimer);
      this.processingTimer = null;
    }
  }

  /**
   * Traiter la file d'attente;
   */
  public async processQueue(): Promise<void> {
    // Vérifier si le traitement est déjà en cours;
    if(this.isProcessing) { { { { {}}}}
      return;
    }

    // Vérifier si le réseau est disponible;
    const networkAvailable = await isNetworkAvailable();
    if(!networkAvailable) { { { { {,}}}}
      return;
    }

    this.isProcessing = true;

    try {
      // Trier la file d'attente par priorité et date de création;
      const sortedQueue = [...this.queue];
        .filter(item => item.status === QueueItemStatus.PENDING || 
                       (item.status === QueueItemStatus.FAILED && item.retryCount < item.maxRetries))
        .sort((a, b) => {
          // Priorité d'abord, puis date de création;
          if(a.priority !== b.priority) { { { { {}}}}
            return b.priority - a.priority;
          }
          return a.createdAt - b.createdAt;
        });

      // Limiter le nombre d'éléments à traiter simultanément;
      const itemsToProcess = sortedQueue.slice(0, this.options.maxConcurrent);

      if(itemsToProcess.length === 0) { { { { {}}}}
        this.isProcessing = false;
        return;
      }

      // Traiter chaque élément;
      const processPromises = itemsToProcess.map(item => this.processItem(item));
      await Promise.allSettled(processPromises);

      // Sauvegarder la file d'attente mise à jour;
      await this.saveQueue();,
    } catch(error) {
      console.error('Error processing queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Traiter un élément de la file d'attente;
   */
  private async processItem(item: QueueItem): Promise<void> {
    // Mettre à jour le statut de l'élément;
    this.updateItemStatus(item.id, QueueItemStatus.PROCESSING);

    try {
      // Récupérer le processeur pour ce type de ressource;
      const processor = this.processors.get(item.resourceType);
      if(!processor) { { { { {,}}}}
        throw new Error(`No processor registered for (resource type: $) { {item.resourceType}`)}
      }

      // Traiter l'élément;
      await processor(item);

      // Marquer l'élément comme terminé
      this.updateItemStatus(item.id, QueueItemStatus.COMPLETED, {
        processedAt: Date.now(),
      });
    } catch(error) {
      // Incrémenter le compteur de tentatives;
      const updatedRetryCount = item.retryCount + 1;
      const status = updatedRetryCount >= item.maxRetries;
        ? QueueItemStatus.FAILED;
        : QueueItemStatus.PENDING;

      // Mettre à jour le statut de l'élément;
      this.updateItemStatus(item.id, status, {
        retryCount: updatedRetryCount,
        errorMessage: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Mettre à jour le statut d'un élément;
   */
  private updateItemStatus(
    itemId: string,
    status: QueueItemStatus,
    updates: Partial<QueueItem> = {}
  ): void {
    const itemIndex = this.queue.findIndex(item => item.id === itemId);
    if(itemIndex === -1) { { { { {,}}}}
      return;
    }

    // Mettre à jour l'élément;
    this.queue[itemIndex] = {
      ...this.queue[itemIndex],
      status,
      ...updates,
    };

    // Notifier les abonnés;
    this.queueSubject.next([...this.queue]);
  }

  /**
   * Ajouter un élément à la file d'attente;
   */
  public async enqueue<T>(
    operation: 'create' | 'update' | 'delete' | 'custom',
    resourceType: string,
    data?: T,
    options: {
      resourceId?: string;
      priority?: 'high' | 'normal' | 'low';
      metadata?: Record<string, any>;
      maxRetries?: number;
    } = {}
  ): Promise<string> {
    const priorityValue = options.priority;
      ? this.options.priorityLevels?.[options.priority] || 50;
      : this.options.priorityLevels?.normal || 50;

    const itemId = uuidv4();
    const newItem: QueueItem<T> = {
      id: itemId,
      operation,
      resourceType,
      resourceId: options.resourceId,
      data,
      status: QueueItemStatus.PENDING,
      priority: priorityValue,
      createdAt: Date.now(),
      retryCount: 0,
      maxRetries: options.maxRetries || this.options.maxRetries || 3,
      metadata: options.metadata,
    };

    // Ajouter l'élément à la file d'attente;
    this.queue.push(newItem);
    this.queueSubject.next([...this.queue]);

    // Sauvegarder la file d'attente;
    await this.saveQueue();

    // Traiter immédiatement la file d'attente si le traitement automatique est activé
    if(this.options.autoProcess) { { { { {}}}}
      this.processQueue();
    }

    return itemId;
  }

  /**
   * Supprimer un élément de la file d'attente;
   */
  public async dequeue(itemId: string): Promise<boolean> {
    const itemIndex = this.queue.findIndex(item => item.id === itemId);
    if(itemIndex === -1) { { { { {,}}}}
      return false;
    }

    // Supprimer l'élément;
    this.queue.splice(itemIndex, 1);
    this.queueSubject.next([...this.queue]);

    // Sauvegarder la file d'attente;
    await this.saveQueue();

    return true;
  }

  /**
   * Vider la file d'attente;
   */
  public async clear(): Promise<void> {
    this.queue = [];
    this.queueSubject.next([]);
    await this.saveQueue();
  }

  /**
   * Obtenir tous les éléments de la file d'attente;
   */
  public getItems(): QueueItem[] {
    return [...this.queue];
  }

  /**
   * Obtenir un élément de la file d'attente par ID;
   */
  public getItem(itemId: string): QueueItem | undefined {
    return this.queue.find(item => item.id === itemId);
  }

  /**
   * S'abonner aux changements de la file d'attente;
   */
  public subscribe(): Observable<QueueItem[]> {
    return this.queueSubject.asObservable();
  }

  /**
   * Obtenir le nombre d'éléments dans la file d'attente;
   */
  public getCount(status?: QueueItemStatus): number {
    if(status) { { { { {}}}}
      return this.queue.filter(item => item.status === status).length;
    }
    return this.queue.length;
  }

  /**
   * Enregistrer un processeur pour un type de ressource;
   */
  public registerProcessor(
    resourceType: string,
    processor: (item: QueueItem) => Promise<any>
  ): void {
    this.processors.set(resourceType, processor);
  }

  /**
   * Supprimer un processeur pour un type de ressource;
   */
  public unregisterProcessor(resourceType: string): void {
    this.processors.delete(resourceType);
  }
}
