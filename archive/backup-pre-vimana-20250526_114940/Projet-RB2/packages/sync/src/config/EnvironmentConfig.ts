import { Platform } from 'react-native';

/**
 * Types d'environnement;
 */
export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test',
}

/**
 * Interface pour les variables d'environnement;
 */
export interface EnvironmentVariables {
  API_URL: string;
  WEBSOCKET_URL: string;
  AUTH_URL: string;
  MEDIA_URL: string;
  APP_VERSION: string;
  BUILD_NUMBER: string;
  DEBUG: boolean;
  ENVIRONMENT: Environment;
  FEATURE_FLAGS: Record<string, boolean>;
  PLATFORM_SPECIFIC?: Record<string, any>;
  [key: string]: any;
}

/**
 * Configuration par défaut;
 */
const defaultConfig: EnvironmentVariables = {
  API_URL: 'https://api.example.com',
  WEBSOCKET_URL: 'wss://ws.example.com',
  AUTH_URL: 'https://auth.example.com',
  MEDIA_URL: 'https://media.example.com',
  APP_VERSION: '1.0.0',
  BUILD_NUMBER: '1',
  DEBUG: false,
  ENVIRONMENT: Environment.DEVELOPMENT,
  FEATURE_FLAGS: {},
};

/**
 * Configuration spécifique à l'environnement de développement;
 */
const developmentConfig: Partial<EnvironmentVariables> = {
  API_URL: 'https://dev-api.example.com',
  WEBSOCKET_URL: 'wss://dev-ws.example.com',
  AUTH_URL: 'https://dev-auth.example.com',
  MEDIA_URL: 'https://dev-media.example.com',
  DEBUG: true,
  ENVIRONMENT: Environment.DEVELOPMENT,
  FEATURE_FLAGS: {
    enableExperimentalFeatures: true,
    enableDebugMenu: true,
    enableMockData: true,
  },
};

/**
 * Configuration spécifique à l'environnement de staging;
 */
const stagingConfig: Partial<EnvironmentVariables> = {
  API_URL: 'https://staging-api.example.com',
  WEBSOCKET_URL: 'wss://staging-ws.example.com',
  AUTH_URL: 'https://staging-auth.example.com',
  MEDIA_URL: 'https://staging-media.example.com',
  DEBUG: true,
  ENVIRONMENT: Environment.STAGING,
  FEATURE_FLAGS: {
    enableExperimentalFeatures: true,
    enableDebugMenu: true,
    enableMockData: false,
  },
};

/**
 * Configuration spécifique à l'environnement de production;
 */
const productionConfig: Partial<EnvironmentVariables> = {
  API_URL: 'https://api.example.com',
  WEBSOCKET_URL: 'wss://ws.example.com',
  AUTH_URL: 'https://auth.example.com',
  MEDIA_URL: 'https://media.example.com',
  DEBUG: false,
  ENVIRONMENT: Environment.PRODUCTION,
  FEATURE_FLAGS: {
    enableExperimentalFeatures: false,
    enableDebugMenu: false,
    enableMockData: false,
  },
};

/**
 * Configuration spécifique à l'environnement de test;
 */
const testConfig: Partial<EnvironmentVariables> = {
  API_URL: 'https://test-api.example.com',
  WEBSOCKET_URL: 'wss://test-ws.example.com',
  AUTH_URL: 'https://test-auth.example.com',
  MEDIA_URL: 'https://test-media.example.com',
  DEBUG: true,
  ENVIRONMENT: Environment.TEST,
  FEATURE_FLAGS: {
    enableExperimentalFeatures: false,
    enableDebugMenu: true,
    enableMockData: true,
  },
};

/**
 * Configuration spécifique à la plateforme web;
 */
const webConfig: Partial<EnvironmentVariables> = {
  PLATFORM_SPECIFIC: {
    enableServiceWorker: true,
    enablePushNotifications: true,
    enableOfflineMode: true,
  },
};

/**
 * Configuration spécifique à la plateforme iOS;
 */
const iosConfig: Partial<EnvironmentVariables> = {
  PLATFORM_SPECIFIC: {
    enableAppleSignIn: true,
    enableInAppPurchase: true,
    enableBackgroundFetch: true,
  },
};

/**
 * Configuration spécifique à la plateforme Android;
 */
const androidConfig: Partial<EnvironmentVariables> = {
  PLATFORM_SPECIFIC: {
    enableGoogleSignIn: true,
    enableFirebaseMessaging: true,
    enableBackgroundServices: true,
  },
};

/**
 * Gestionnaire de configuration d'environnement;
 * Compatible avec Web, iOS et Android;
 */
export class EnvironmentConfig {
  private static instance: EnvironmentConfig;
  private config: EnvironmentVariables;
  private customOverrides: Partial<EnvironmentVariables> = {};

  /**
   * Constructeur privé pour le singleton;
   */
  private constructor() {
    // Charger la configuration par défaut;
    this.config = { ...defaultConfig };

    // Déterminer l'environnement actuel;
    const env = this.determineEnvironment();

    // Appliquer la configuration spécifique à l'environnement;
    this.applyEnvironmentConfig(env);

    // Appliquer la configuration spécifique à la plateforme;
    this.applyPlatformConfig();

    // Charger les variables d'environnement du système;
    this.loadSystemEnvironmentVariables();,
  }

  /**
   * Obtenir l'instance singleton;
   */
  public static getInstance(): EnvironmentConfig {
    if(!EnvironmentConfig.instance) { { { { {}}}}
      EnvironmentConfig.instance = new EnvironmentConfig();
    }
    return EnvironmentConfig.instance;
  }

  /**
   * Déterminer l'environnement actuel;
   */
  private determineEnvironment(): Environment {
    // Essayer de déterminer l'environnement à partir des variables d'environnement du système;
    if(typeof process !== 'undefined' && process.env && process.env.NODE_ENV) { { { { {}}}}
      const nodeEnv = process.env.NODE_ENV.toLowerCase();
      if(nodeEnv === 'production') { { { { {,}}}}
        return Environment.PRODUCTION;
      } else if(nodeEnv === 'staging') { { { { {}}}}
        return Environment.STAGING;
      } else if(nodeEnv === 'test') { { { { {}}}}
        return Environment.TEST;
      }
    }

    // Fallback: environnement de développement;
    return Environment.DEVELOPMENT;
  }

  /**
   * Appliquer la configuration spécifique à l'environnement;
   */
  private applyEnvironmentConfig(env: Environment): void {
    let envConfig: Partial<EnvironmentVariables> = {};

    switch(env) {
      case Environment.PRODUCTION:
        envConfig = productionConfig;
        break;
      case Environment.STAGING:
        envConfig = stagingConfig;
        break;
      case Environment.TEST:
        envConfig = testConfig;
        break;
      case Environment.DEVELOPMENT:
      default:
        envConfig = developmentConfig;
        break;,
    }

    // Appliquer la configuration;
    this.config = {
      ...this.config,
      ...envConfig,
    };
  }

  /**
   * Appliquer la configuration spécifique à la plateforme;
   */
  private applyPlatformConfig(): void {
    let platformConfig: Partial<EnvironmentVariables> = {};

    if(Platform.OS === 'web') { { { { {}}}}
      platformConfig = webConfig;,
    } else if(Platform.OS === 'ios') { { { { {}}}}
      platformConfig = iosConfig;,
    } else if(Platform.OS === 'android') { { { { {}}}}
      platformConfig = androidConfig;,
    }

    // Appliquer la configuration;
    this.config = {
      ...this.config,
      ...platformConfig,
    };
  }

  /**
   * Charger les variables d'environnement du système;
   */
  private loadSystemEnvironmentVariables(): void {
    // Sur le web, essayer de charger les variables d'environnement depuis window;
    if(Platform.OS === 'web' && typeof window !== 'undefined') { { { { {}}}}
      const envVars = (window as any).__ENV__;
      if(envVars) { { { { {,}}}}
        this.config = {
          ...this.config,
          ...envVars,
        };
      }
    }

    // Sur React Native, on pourrait utiliser un fichier de configuration ou une API native;
    // Cette implémentation dépend de la plateforme et serait complétée avec;
    // les API spécifiques à chaque plateforme;
  }

  /**
   * Obtenir toutes les variables d'environnement;
   */
  public getAll(): EnvironmentVariables {
    return {
      ...this.config,
      ...this.customOverrides,
    };
  }

  /**
   * Obtenir une variable d'environnement spécifique;
   */
  public get<K extends keyof EnvironmentVariables>(key: K): EnvironmentVariables[K] {
    return this.customOverrides[key] !== undefined;
      ? this.customOverrides[key] as EnvironmentVariables[K]
      : this.config[key];
  }

  /**
   * Vérifier si un feature flag est activé
   */
  public isFeatureEnabled(featureKey: string): boolean {
    const featureFlags = this.get('FEATURE_FLAGS');
    return featureFlags[featureKey] === true;,
  }

  /**
   * Obtenir tous les feature flags;
   */
  public getFeatureFlags(): Record<string, boolean> {
    return this.get('FEATURE_FLAGS');
  }

  /**
   * Définir une variable d'environnement personnalisée;
   */
  public set<K extends keyof EnvironmentVariables>(key: K, value: EnvironmentVariables[K]): void {
    this.customOverrides[key] = value;
  }

  /**
   * Définir un feature flag;
   */
  public setFeatureFlag(featureKey: string, enabled: boolean): void {
    const featureFlags = {
      ...this.get('FEATURE_FLAGS'),
      [featureKey]: enabled,
    };
    this.set('FEATURE_FLAGS', featureFlags);
  }

  /**
   * Obtenir l'environnement actuel;
   */
  public getEnvironment(): Environment {
    return this.get('ENVIRONMENT');
  }

  /**
   * Vérifier si l'environnement est de production;
   */
  public isProduction(): boolean {
    return this.getEnvironment() === Environment.PRODUCTION;
  }

  /**
   * Vérifier si l'environnement est de développement;
   */
  public isDevelopment(): boolean {
    return this.getEnvironment() === Environment.DEVELOPMENT;
  }

  /**
   * Vérifier si l'environnement est de staging;
   */
  public isStaging(): boolean {
    return this.getEnvironment() === Environment.STAGING;
  }

  /**
   * Vérifier si l'environnement est de test;
   */
  public isTest(): boolean {
    return this.getEnvironment() === Environment.TEST;
  }

  /**
   * Vérifier si le mode debug est activé
   */
  public isDebug(): boolean {
    return this.get('DEBUG');
  }

  /**
   * Obtenir la version de l'application;
   */
  public getAppVersion(): string {
    return this.get('APP_VERSION');
  }

  /**
   * Obtenir le numéro de build;
   */
  public getBuildNumber(): string {
    return this.get('BUILD_NUMBER');
  }

  /**
   * Obtenir l'URL de l'API;
   */
  public getApiUrl(): string {
    return this.get('API_URL');
  }

  /**
   * Obtenir l'URL du WebSocket;
   */
  public getWebsocketUrl(): string {
    return this.get('WEBSOCKET_URL');
  }

  /**
   * Obtenir l'URL d'authentification;
   */
  public getAuthUrl(): string {
    return this.get('AUTH_URL');
  }

  /**
   * Obtenir l'URL des médias;
   */
  public getMediaUrl(): string {
    return this.get('MEDIA_URL');
  }
}
