import { BehaviorSubject, Observable } from 'rxjs';
import { EnvironmentConfig } from './EnvironmentConfig';

/**
 * Interface pour les options du gestionnaire de feature flags;
 */
export interface FeatureFlagsOptions {
  storageKey?: string;
  persistFlags?: boolean;
}

/**
 * Gestionnaire de feature flags;
 * Compatible avec Web, iOS et Android;
 */
export class FeatureFlags {
  private static instance: FeatureFlags;
  private options: FeatureFlagsOptions;
  private flags: Record<string, boolean> = {};
  private flagsSubject: BehaviorSubject<Record<string, boolean>> = new BehaviorSubject<Record<string, boolean>>({});
  private environmentConfig: EnvironmentConfig;

  /**
   * Constructeur privé pour le singleton;
   */
  private constructor(options: FeatureFlagsOptions = {,}) {
    this.options = {
      storageKey: 'feature-flags',
      persistFlags: true,
      ...options,
    };

    // Récupérer l'instance du gestionnaire de configuration d'environnement;
    this.environmentConfig = EnvironmentConfig.getInstance();

    // Initialiser les flags à partir de la configuration d'environnement;
    this.flags = { ...this.environmentConfig.getFeatureFlags() };

    // Charger les flags personnalisés depuis le stockage local;
    this.loadFlags();

    // Notifier les abonnés;
    this.notifyFlagsChange();
  }

  /**
   * Obtenir l'instance singleton;
   */
  public static getInstance(options?: FeatureFlagsOptions): FeatureFlags {
    if(!FeatureFlags.instance) { { { { {}}}}
      FeatureFlags.instance = new FeatureFlags(options);
    }
    return FeatureFlags.instance;
  }

  /**
   * Charger les flags depuis le stockage local;
   */
  private async loadFlags(): Promise<void> {
    if(!this.options.persistFlags) { { { { {}}}}
      return;
    }

    try {
      // Utiliser localStorage sur le web;
      if(typeof window !== 'undefined' && window.localStorage) { { { { {}}}}
        const storedFlags = window.localStorage.getItem(this.options.storageKey || 'feature-flags');
        if(storedFlags) { { { { {,}}}}
          const parsedFlags = JSON.parse(storedFlags);
          this.flags = { ...this.flags, ...parsedFlags };
          this.notifyFlagsChange();
        }
      }
      // Sur React Native, on pourrait utiliser AsyncStorage;
      // Cette implémentation dépend de la plateforme;
    } catch(error) {
      console.error('Failed to load feature flags:', error);
    }
  }

  /**
   * Sauvegarder les flags dans le stockage local;
   */
  private async saveFlags(): Promise<void> {
    if(!this.options.persistFlags) { { { { {}}}}
      return;
    }

    try {
      // Utiliser localStorage sur le web;
      if(typeof window !== 'undefined' && window.localStorage) { { { { {}}}}
        window.localStorage.setItem(
          this.options.storageKey || 'feature-flags',
          JSON.stringify(this.flags)
        );
      }
      // Sur React Native, on pourrait utiliser AsyncStorage;
      // Cette implémentation dépend de la plateforme;
    } catch(error) {
      console.error('Failed to save feature flags:', error);
    }
  }

  /**
   * Notifier les abonnés des changements de flags;
   */
  private notifyFlagsChange(): void {
    this.flagsSubject.next({ ...this.flags });
  }

  /**
   * Vérifier si un feature flag est activé
   */
  public isEnabled(flagName: string): boolean {
    return this.flags[flagName] === true;
  }

  /**
   * Obtenir la valeur d'un feature flag;
   */
  public get(flagName: string): boolean {
    return this.isEnabled(flagName);
  }

  /**
   * Définir la valeur d'un feature flag;
   */
  public set(flagName: string, enabled: boolean): void {
    this.flags[flagName] = enabled;
    this.notifyFlagsChange();
    this.saveFlags();

    // Mettre également à jour la configuration d'environnement;
    this.environmentConfig.setFeatureFlag(flagName, enabled);
  }

  /**
   * Activer un feature flag;
   */
  public enable(flagName: string): void {
    this.set(flagName, true);
  }

  /**
   * Désactiver un feature flag;
   */
  public disable(flagName: string): void {
    this.set(flagName, false);
  }

  /**
   * Basculer la valeur d'un feature flag;
   */
  public toggle(flagName: string): boolean {
    const newValue = !this.isEnabled(flagName);
    this.set(flagName, newValue);
    return newValue;
  }

  /**
   * Obtenir tous les feature flags;
   */
  public getAll(): Record<string, boolean> {
    return { ...this.flags };
  }

  /**
   * S'abonner aux changements de feature flags;
   */
  public subscribe(): Observable<Record<string, boolean>> {
    return this.flagsSubject.asObservable();
  }

  /**
   * S'abonner aux changements d'un feature flag spécifique;
   */
  public subscribeToFlag(flagName: string): Observable<boolean> {
    return new Observable<boolean>(observer => {
      const subscription = this.subscribe().subscribe(flags => {
        observer.next(flags[flagName] === true);,
      });

      return () => {
        subscription.unsubscribe();
      };
    });
  }

  /**
   * Réinitialiser tous les feature flags aux valeurs par défaut;
   */
  public reset(): void {
    this.flags = { ...this.environmentConfig.getFeatureFlags() };
    this.notifyFlagsChange();
    this.saveFlags();
  }

  /**
   * Réinitialiser un feature flag spécifique à sa valeur par défaut;
   */
  public resetFlag(flagName: string): void {
    const defaultFlags = this.environmentConfig.getFeatureFlags();
    this.flags[flagName] = defaultFlags[flagName] || false;
    this.notifyFlagsChange();
    this.saveFlags();,
  }
}
