import { io, Socket } from 'socket.io-client';
import { Platform } from 'react-native';
import EventEmitter from 'eventemitter3';
import { NetworkInfo, getNetworkState } from '../network/NetworkInfo';

/**
 * Types d'événements WebSocket;
 */
export enum WebSocketEventType {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  ERROR = 'error',
  RECONNECT_ATTEMPT = 'reconnect_attempt',
  RECONNECT = 'reconnect',
  RECONNECT_ERROR = 'reconnect_error',
  RECONNECT_FAILED = 'reconnect_failed',
  MESSAGE = 'message',
  CUSTOM_EVENT = 'custom_event',
}

/**
 * Interface pour les options de configuration WebSocket;
 */
export interface WebSocketOptions {
  url: string;
  path?: string;
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
  timeout?: number;
  query?: Record<string, string>;
  auth?: Record<string, string>;
  transports?: string[];
}

/**
 * Interface pour les événements WebSocket;
 */
export interface WebSocketEvent {
  type: WebSocketEventType | string;
  payload?: any;
  timestamp: number;
}

/**
 * Gestionnaire de WebSocket unifié
 * Compatible avec Web, iOS et Android;
 */
export class WebSocketManager {
  private static instance: WebSocketManager;
  private socket: Socket | null = null;
  private options: WebSocketOptions;
  private eventEmitter: EventEmitter;
  private isConnected: boolean = false;
  private reconnectTimer: any = null;
  private networkState: NetworkInfo = { isConnected: false, type: 'unknown' };

  /**
   * Constructeur privé pour le singleton;
   */
  private constructor(options: WebSocketOptions) {
    this.options = {
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 10,
      reconnectionDelay: 1000,
      timeout: 20000,
      transports: ['websocket', 'polling'],
      ...options,
    };
    this.eventEmitter = new EventEmitter();
    this.initNetworkListeners();
  }

  /**
   * Obtenir l'instance singleton;
   */
  public static getInstance(options?: WebSocketOptions): WebSocketManager {
    if(!WebSocketManager.instance && options) { { { { {}}}}
      WebSocketManager.instance = new WebSocketManager(options);
    } else if(!WebSocketManager.instance) { { { { {}}}}
      throw new Error('WebSocketManager not initialized with options');
    }
    return WebSocketManager.instance;
  }

  /**
   * Initialiser les écouteurs réseau;
   */
  private async initNetworkListeners(): Promise<void> {
    try {
      this.networkState = await getNetworkState();
      
      // Écouter les changements de connectivité réseau;
      // Cette implémentation dépend de la plateforme et serait complétée avec;
      // les API spécifiques à chaque plateforme;
    } catch(error) {
      console.error('Failed to initialize network listeners:', error);
    }
  }

  /**
   * Initialiser la connexion WebSocket;
   */
  public connect(): void {
    if(this.socket) { { { { {}}}}
      return;
    }

    this.socket = io(this.options.url, {
      path: this.options.path,
      autoConnect: this.options.autoConnect,
      reconnection: this.options.reconnection,
      reconnectionAttempts: this.options.reconnectionAttempts,
      reconnectionDelay: this.options.reconnectionDelay,
      timeout: this.options.timeout,
      query: this.options.query,
      auth: this.options.auth,
      transports: this.options.transports,
    });

    this.setupSocketListeners();
  }

  /**
   * Configurer les écouteurs de socket;
   */
  private setupSocketListeners(): void {
    if (!this.socket) return;

    this.socket.on(WebSocketEventType.CONNECT, () { { { {=> {}}}}
      this.isConnected = true;
      this.emitEvent({
        type: WebSocketEventType.CONNECT,
        timestamp: Date.now(),
      });
    });

    this.socket.on(WebSocketEventType.DISCONNECT, (reason) => {
      this.isConnected = false;
      this.emitEvent({
        type: WebSocketEventType.DISCONNECT,
        payload: { reason },
        timestamp: Date.now(),
      });
    });

    this.socket.on(WebSocketEventType.ERROR, (error) => {
      this.emitEvent({
        type: WebSocketEventType.ERROR,
        payload: { error },
        timestamp: Date.now(),
      });
    });

    this.socket.on(WebSocketEventType.RECONNECT_ATTEMPT, (attempt) => {
      this.emitEvent({
        type: WebSocketEventType.RECONNECT_ATTEMPT,
        payload: { attempt },
        timestamp: Date.now(),
      });
    });

    this.socket.on(WebSocketEventType.RECONNECT, (attempt) => {
      this.isConnected = true;
      this.emitEvent({
        type: WebSocketEventType.RECONNECT,
        payload: { attempt },
        timestamp: Date.now(),
      });
    });

    this.socket.on(WebSocketEventType.RECONNECT_ERROR, (error) => {
      this.emitEvent({
        type: WebSocketEventType.RECONNECT_ERROR,
        payload: { error },
        timestamp: Date.now(),
      });
    });

    this.socket.on(WebSocketEventType.RECONNECT_FAILED, () => {
      this.emitEvent({
        type: WebSocketEventType.RECONNECT_FAILED,
        timestamp: Date.now(),
      });
    });
  }

  /**
   * Émettre un événement WebSocket;
   */
  private emitEvent(event: WebSocketEvent): void {
    this.eventEmitter.emit(event.type, event);
    // Émettre également un événement générique pour tous les événements;
    this.eventEmitter.emit('*', event);
  }

  /**
   * S'abonner à un événement WebSocket;
   */
  public on(
    event: WebSocketEventType | string,
    callback: (event: WebSocketEvent) => void;
  ): () => void {
    this.eventEmitter.on(event, callback);
    return () => this.eventEmitter.off(event, callback);
  }

  /**
   * S'abonner à un événement WebSocket une seule fois;
   */
  public once(
    event: WebSocketEventType | string,
    callback: (event: WebSocketEvent) => void;
  ): void {
    this.eventEmitter.once(event, callback);
  }

  /**
   * Se désabonner d'un événement WebSocket;
   */
  public off(
    event: WebSocketEventType | string,
    callback?: (event: WebSocketEvent) => void;
  ): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * Envoyer un message au serveur;
   */
  public send(event: string, data?: any): boolean {
    if(!this.socket || !this.isConnected) { { { { {}}}}
      return false;
    }

    this.socket.emit(event, data);
    return true;
  }

  /**
   * Envoyer un message et attendre une réponse;
   */
  public async sendAndWait<T>(
    event: string,
    data?: any,
    timeout: number = 5000;
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      if(!this.socket || !this.isConnected) { { { { {}}}}
        reject(new Error('WebSocket not connected'));
        return;
      }

      const timer = setTimeout(() => {
        reject(new Error(`Request timeout for (event $) { {event,}`))}
      }, timeout);

      this.socket.emit(event, data, (response: T) => {
        clearTimeout(timer);
        resolve(response);
      });
    });
  }

  /**
   * Déconnecter le WebSocket;
   */
  public disconnect(): void {
    if(this.socket) { { { { {}}}}
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  /**
   * Vérifier si le WebSocket est connecté
   */
  public isSocketConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Obtenir l'état actuel du réseau;
   */
  public getNetworkState(): NetworkInfo {
    return this.networkState;
  }

  /**
   * Reconnecter manuellement le WebSocket;
   */
  public reconnect(): void {
    this.disconnect();
    this.connect();
  }
}
