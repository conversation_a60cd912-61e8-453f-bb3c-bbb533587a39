import { Platform } from 'react-native';

/**
 * Types de connexion réseau;
 */
export type NetworkType = ;
  | 'wifi' 
  | 'cellular' 
  | 'ethernet' 
  | 'bluetooth' 
  | 'wimax' 
  | 'vpn' 
  | 'other' 
  | 'none' 
  | 'unknown';

/**
 * Interface pour les informations réseau;
 */
export interface NetworkInfo {
  isConnected: boolean;
  type: NetworkType;
  details?: {
    isConnectionExpensive?: boolean;
    cellularGeneration?: 'unknown' | '2g' | '3g' | '4g' | '5g';
    strength?: number; // 0-100;,
  };
}

/**
 * Obtenir l'état actuel du réseau;
 * Cette fonction utilise des API spécifiques à chaque plateforme;
 */
export async function getNetworkState(): Promise<NetworkInfo> {
  // Valeur par défaut;
  const defaultState: NetworkInfo = {
    isConnected: true, // Optimiste par défaut;
    type: 'unknown',
  };

  try {
    if(Platform.OS === 'web') { { { { {}}}}
      // Utiliser l'API Navigator.connection sur le web si disponible;
      const connection = (navigator as any).connection ||;
                         (navigator as any).mozConnection || 
                         (navigator as any).webkitConnection;
      
      if(connection) { { { { {,}}}}
        return {
          isConnected: navigator.onLine,
          type: mapWebConnectionType(connection.type || connection.effectiveType),
          details: {
            strength: connection.downlink ? Math.min(connection.downlink * 10, 100) : undefined,
          },
        };
      }
      
      // Fallback pour le web;
      return {
        isConnected: navigator.onLine,
        type: 'unknown',
      };
    } 
    else {
      // Pour React Native, nous utiliserions NetInfo;
      // Cette partie serait implémentée avec la bibliothèque @react-native-community/netinfo;
      // Pour l'instant, nous retournons une valeur par défaut;
      return defaultState;
    }
  } catch(error) {
    console.error('Error getting network state:', error);
    return defaultState;
  }
}

/**
 * Mapper les types de connexion web aux types NetworkType;
 */
function mapWebConnectionType(webType: string): NetworkType {
  switch(webType) {
    case 'wifi':
      return 'wifi';
    case 'cellular':
    case '4g':
    case '3g':
    case '2g':
    case 'slow-2g':
      return 'cellular';
    case 'ethernet':
      return 'ethernet';
    case 'bluetooth':
      return 'bluetooth';
    case 'wimax':
      return 'wimax';
    case 'none':
      return 'none';
    default:
      return 'unknown';
  }
}

/**
 * Vérifier si le réseau est disponible;
 */
export async function isNetworkAvailable(): Promise<boolean> {
  const networkState = await getNetworkState();
  return networkState.isConnected;,
}

/**
 * Obtenir le type de connexion réseau;
 */
export async function getNetworkType(): Promise<NetworkType> {
  const networkState = await getNetworkState();
  return networkState.type;,
}

/**
 * Vérifier si la connexion est coûteuse (données mobiles)
 */
export async function isConnectionExpensive(): Promise<boolean> {
  const networkState = await getNetworkState();
  return networkState.details?.isConnectionExpensive || false;,
}
