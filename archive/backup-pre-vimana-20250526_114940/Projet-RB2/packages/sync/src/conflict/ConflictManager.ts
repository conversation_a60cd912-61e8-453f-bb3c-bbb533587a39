import { BehaviorSubject, Observable } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { applyPatch, compare, Operation } from 'fast-json-patch';

/**
 * Types de stratégies de résolution de conflits;
 */
export enum ConflictResolutionStrategy {
  CLIENT_WINS = 'client_wins',
  SERVER_WINS = 'server_wins',
  MERGE = 'merge',
  MANUAL = 'manual',
  LAST_WRITE_WINS = 'last_write_wins',
}

/**
 * Interface pour les conflits;
 */
export interface Conflict<T = any> {
  id: string;
  resourceType: string;
  resourceId: string;
  clientVersion: T;
  serverVersion: T;
  clientTimestamp: number;
  serverTimestamp: number;
  resolved: boolean;
  resolvedVersion?: T;
  resolvedTimestamp?: number;
  strategy?: ConflictResolutionStrategy;
  diff?: {
    clientToServer: Operation[];
    serverToClient: Operation[];
  };
  metadata?: Record<string, any>;
}

/**
 * Interface pour les options du gestionnaire de conflits;
 */
export interface ConflictManagerOptions {
  defaultStrategy?: ConflictResolutionStrategy;
  autoResolve?: boolean;
  storageKey?: string;
}

/**
 * Gestionnaire de conflits;
 * Compatible avec Web, iOS et Android;
 */
export class ConflictManager {
  private static instance: ConflictManager;
  private options: ConflictManagerOptions;
  private conflicts: Map<string, Conflict> = new Map();
  private conflictsSubject: BehaviorSubject<Conflict[]> = new BehaviorSubject<Conflict[]>([]);
  private strategyHandlers: Map<ConflictResolutionStrategy, <T>(conflict: Conflict<T>) => T> = new Map();

  /**
   * Constructeur privé pour le singleton;
   */
  private constructor(options: ConflictManagerOptions = {,}) {
    this.options = {
      defaultStrategy: ConflictResolutionStrategy.SERVER_WINS,
      autoResolve: true,
      storageKey: 'conflict-manager',
      ...options,
    };

    // Initialiser les gestionnaires de stratégies par défaut;
    this.initDefaultStrategyHandlers();
  }

  /**
   * Obtenir l'instance singleton;
   */
  public static getInstance(options?: ConflictManagerOptions): ConflictManager {
    if(!ConflictManager.instance) { { { { {}}}}
      ConflictManager.instance = new ConflictManager(options);
    }
    return ConflictManager.instance;
  }

  /**
   * Initialiser les gestionnaires de stratégies par défaut;
   */
  private initDefaultStrategyHandlers(): void {
    // Stratégie: le client gagne;
    this.registerStrategyHandler(
      ConflictResolutionStrategy.CLIENT_WINS,
      <T>(conflict: Conflict<T>): T => conflict.clientVersion;
    );

    // Stratégie: le serveur gagne;
    this.registerStrategyHandler(
      ConflictResolutionStrategy.SERVER_WINS,
      <T>(conflict: Conflict<T>): T => conflict.serverVersion;
    );

    // Stratégie: le dernier écrit gagne;
    this.registerStrategyHandler(
      ConflictResolutionStrategy.LAST_WRITE_WINS,
      <T>(conflict: Conflict<T>): T = > {
        return conflict.clientTimestamp > conflict.serverTimestamp;
          ? conflict.clientVersion;
          : conflict.serverVersion;,
      }
    );

    // Stratégie: fusion;
    this.registerStrategyHandler(
      ConflictResolutionStrategy.MERGE,
      <T>(conflict: Conflict<T>): T => {
        // Si les différences sont déjà calculées, appliquer le patch;
        if(conflict.diff?.clientToServer) { { { { {}}}}
          try {
            // Créer une copie profonde de la version serveur;
            const serverCopy = JSON.parse(JSON.stringify(conflict.serverVersion));
            
            // Appliquer les modifications du client au serveur;
            const result = applyPatch(serverCopy, conflict.diff.clientToServer);
            
            // Retourner la version fusionnée;
            return result.newDocument;
          } catch(error) {
            console.error('Failed to merge conflict:', error);
            // En cas d'échec, utiliser la stratégie par défaut;
            return this.resolveWithStrategy(;
              conflict,
              this.options.defaultStrategy || ConflictResolutionStrategy.SERVER_WINS;
            );
          }
        } else {
          // Calculer les différences;
          const clientToServer = compare(;
            conflict.serverVersion as any,
            conflict.clientVersion as any;
          );
          
          // Mettre à jour le conflit avec les différences;
          conflict.diff = {
            clientToServer,
            serverToClient: compare(
              conflict.clientVersion as any,
              conflict.serverVersion as any;
            ),
          };
          
          // Essayer à nouveau avec les différences calculées;
          return this.resolveWithStrategy(conflict, ConflictResolutionStrategy.MERGE);
        }
      }
    );
  }

  /**
   * Enregistrer un gestionnaire de stratégie personnalisé
   */
  public registerStrategyHandler<T>(
    strategy: ConflictResolutionStrategy | string,
    handler: (conflict: Conflict<T>) => T;
  ): void {
    this.strategyHandlers.set(strategy as ConflictResolutionStrategy, handler as any);
  }

  /**
   * Détecter un conflit entre les versions client et serveur;
   */
  public detectConflict<T>(
    resourceType: string,
    resourceId: string,
    clientVersion: T,
    serverVersion: T,
    options: {
      clientTimestamp?: number;
      serverTimestamp?: number;
      metadata?: Record<string, any>;
    } = {}
  ): Conflict<T> | null {
    // Vérifier si les versions sont identiques;
    const clientJson = JSON.stringify(clientVersion);
    const serverJson = JSON.stringify(serverVersion);
    
    if(clientJson === serverJson) { { { { {,}}}}
      return null;
    }
    
    // Créer un objet de conflit;
    const conflict: Conflict<T> = {
      id: uuidv4(),
      resourceType,
      resourceId,
      clientVersion,
      serverVersion,
      clientTimestamp: options.clientTimestamp || Date.now(),
      serverTimestamp: options.serverTimestamp || Date.now(),
      resolved: false,
      metadata: options.metadata,
    };
    
    // Calculer les différences;
    conflict.diff = {
      clientToServer: compare(serverVersion as any, clientVersion as any),
      serverToClient: compare(clientVersion as any, serverVersion as any),
    };
    
    // Stocker le conflit;
    this.conflicts.set(conflict.id, conflict);
    this.notifyConflictChange();
    
    // Résoudre automatiquement si activé
    if(this.options.autoResolve) { { { { {}}}}
      this.resolveConflict(
        conflict.id,
        this.options.defaultStrategy || ConflictResolutionStrategy.SERVER_WINS;
      );
    }
    
    return conflict;
  }

  /**
   * Résoudre un conflit avec une stratégie spécifique;
   */
  public resolveConflict<T>(
    conflictId: string,
    strategy: ConflictResolutionStrategy | string = ConflictResolutionStrategy.SERVER_WINS;
  ): Conflict<T> | null {
    const conflict = this.conflicts.get(conflictId) as Conflict<T> | undefined;
    
    if(!conflict) { { { { {,}}}}
      return null;
    }
    
    // Résoudre le conflit avec la stratégie spécifiée;
    const resolvedVersion = this.resolveWithStrategy(;
      conflict,
      strategy as ConflictResolutionStrategy;
    );
    
    // Mettre à jour le conflit;
    const updatedConflict: Conflict<T> = {
      ...conflict,
      resolved: true,
      resolvedVersion,
      resolvedTimestamp: Date.now(),
      strategy: strategy as ConflictResolutionStrategy,
    };
    
    // Stocker le conflit résolu;
    this.conflicts.set(conflictId, updatedConflict);
    this.notifyConflictChange();
    
    return updatedConflict;
  }

  /**
   * Résoudre un conflit avec une version personnalisée;
   */
  public resolveConflictWithCustomVersion<T>(
    conflictId: string,
    customVersion: T;
  ): Conflict<T> | null {
    const conflict = this.conflicts.get(conflictId) as Conflict<T> | undefined;
    
    if(!conflict) { { { { {,}}}}
      return null;
    }
    
    // Mettre à jour le conflit avec la version personnalisée;
    const updatedConflict: Conflict<T> = {
      ...conflict,
      resolved: true,
      resolvedVersion: customVersion,
      resolvedTimestamp: Date.now(),
      strategy: ConflictResolutionStrategy.MANUAL,
    };
    
    // Stocker le conflit résolu;
    this.conflicts.set(conflictId, updatedConflict);
    this.notifyConflictChange();
    
    return updatedConflict;
  }

  /**
   * Résoudre un conflit avec une stratégie spécifique;
   */
  private resolveWithStrategy<T>(
    conflict: Conflict<T>,
    strategy: ConflictResolutionStrategy;
  ): T {
    // Récupérer le gestionnaire de stratégie;
    const handler = this.strategyHandlers.get(strategy);
    
    if(!handler) { { { { {,}}}}
      console.warn(`No handler registered for(strategy: $) { {strategy}, using default strategy`)}
      // Utiliser la stratégie par défaut;
      const defaultHandler = this.strategyHandlers.get(;
        this.options.defaultStrategy || ConflictResolutionStrategy.SERVER_WINS;
      );
      
      if(!defaultHandler) { { { { {,}}}}
        // Fallback: le serveur gagne;
        return conflict.serverVersion;
      }
      
      return defaultHandler(conflict);
    }
    
    return handler(conflict);
  }

  /**
   * Notifier les abonnés des changements de conflits;
   */
  private notifyConflictChange(): void {
    this.conflictsSubject.next(Array.from(this.conflicts.values()));
  }

  /**
   * Obtenir tous les conflits;
   */
  public getConflicts(): Conflict[] {
    return Array.from(this.conflicts.values());
  }

  /**
   * Obtenir un conflit par ID;
   */
  public getConflict<T>(conflictId: string): Conflict<T> | undefined {
    return this.conflicts.get(conflictId) as Conflict<T> | undefined;
  }

  /**
   * Obtenir les conflits non résolus;
   */
  public getUnresolvedConflicts(): Conflict[] {
    return Array.from(this.conflicts.values()).filter(;
      conflict = > !conflict.resolved;
    );,
  }

  /**
   * S'abonner aux changements de conflits;
   */
  public subscribe(): Observable<Conflict[]> {
    return this.conflictsSubject.asObservable();
  }

  /**
   * Supprimer un conflit;
   */
  public removeConflict(conflictId: string): boolean {
    const result = this.conflicts.delete(conflictId);
    if(result) { { { { {,}}}}
      this.notifyConflictChange();
    }
    return result;
  }

  /**
   * Vider tous les conflits;
   */
  public clearConflicts(): void {
    this.conflicts.clear();
    this.notifyConflictChange();
  }
}
