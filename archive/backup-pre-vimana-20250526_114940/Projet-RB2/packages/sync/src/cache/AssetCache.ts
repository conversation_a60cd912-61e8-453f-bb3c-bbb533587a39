import localforage from 'localforage';
import { Platform } from 'react-native';
import { BehaviorSubject, Observable } from 'rxjs';
import { isNetworkAvailable } from '../network/NetworkInfo';

/**
 * Types d'assets;
 */
export enum AssetType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  JSON = 'json',
  OTHER = 'other',
}

/**
 * Interface pour les métadonnées d'asset;
 */
export interface AssetMetadata {
  key: string;
  url: string;
  type: AssetType;
  size?: number;
  width?: number;
  height?: number;
  format?: string;
  lastModified?: number;
  expiresAt?: number;
  createdAt: number;
  lastAccessed?: number;
  accessCount: number;
  hash?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface pour les options du cache d'assets;
 */
export interface AssetCacheOptions {
  storageKey?: string;
  maxSize?: number; // en octets;
  maxAge?: number; // en millisecondes;
  preloadAssets?: string[]; // URLs à précharger;
  defaultExpiration?: number; // en millisecondes;
  cleanupInterval?: number; // en millisecondes;
  networkTimeout?: number; // en millisecondes;
  compressionLevel?: number; // 0-9, 0 = pas de compression;
  autoCleanup?: boolean;
  persistMetadata?: boolean;
  cachePolicy?: 'network-first' | 'cache-first' | 'network-only' | 'cache-only';,
}

/**
 * Interface pour les statistiques du cache;
 */
export interface CacheStats {
  totalSize: number;
  totalItems: number;
  oldestItem?: AssetMetadata;
  newestItem?: AssetMetadata;
  mostAccessed?: AssetMetadata;
  typeCounts: Record<AssetType, number>;
  typeSizes: Record<AssetType, number>;
  hitRate: number;
  missRate: number;
  lastCleanup?: number;
}

/**
 * Gestionnaire de cache d'assets;
 * Compatible avec Web, iOS et Android;
 */
export class AssetCache {
  private static instance: AssetCache;
  private options: AssetCacheOptions;
  private storage: LocalForage;
  private metadataStorage: LocalForage;
  private assetMetadata: Map<string, AssetMetadata> = new Map();
  private statsSubject: BehaviorSubject<CacheStats> = new BehaviorSubject<CacheStats>(this.getEmptyStats());
  private cleanupTimer: any = null;
  private hits: number = 0;
  private misses: number = 0;
  private preloadQueue: string[] = [];
  private isPreloading: boolean = false;

  /**
   * Constructeur privé pour le singleton;
   */
  private constructor(options: AssetCacheOptions = {,}) {
    this.options = {
      storageKey: 'asset-cache',
      maxSize: 100 * 1024 * 1024, // 100 MB par défaut;
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 jours par défaut;
      defaultExpiration: 24 * 60 * 60 * 1000, // 1 jour par défaut;
      cleanupInterval: 60 * 60 * 1000, // 1 heure par défaut;
      networkTimeout: 30000, // 30 secondes par défaut;
      compressionLevel: 0, // Pas de compression par défaut;
      autoCleanup: true,
      persistMetadata: true,
      cachePolicy: 'cache-first',
      ...options,
    };

    // Initialiser le stockage local;
    this.storage = localforage.createInstance({
      name: 'AssetCache',
      storeName: this.options.storageKey,
    });

    // Initialiser le stockage des métadonnées;
    this.metadataStorage = localforage.createInstance({
      name: 'AssetCacheMetadata',
      storeName: `${this.options.storageKey}-metadata`,
    });

    // Charger les métadonnées;
    this.loadMetadata();

    // Démarrer le nettoyage automatique si activé
    if(this.options.autoCleanup) { { { { {}}}}
      this.startCleanupTimer();
    }

    // Précharger les assets si spécifiés;
    if(this.options.preloadAssets && this.options.preloadAssets.length > 0) { { { { {}}}}
      this.preloadQueue = [...this.options.preloadAssets];
      this.startPreloading();
    }
  }

  /**
   * Obtenir l'instance singleton;
   */
  public static getInstance(options?: AssetCacheOptions): AssetCache {
    if(!AssetCache.instance) { { { { {}}}}
      AssetCache.instance = new AssetCache(options);
    }
    return AssetCache.instance;
  }

  /**
   * Charger les métadonnées depuis le stockage;
   */
  private async loadMetadata(): Promise<void> {
    if(!this.options.persistMetadata) { { { { {}}}}
      return;
    }

    try {
      const storedMetadata = await this.metadataStorage.getItem<Record<string, AssetMetadata>>('metadata');
      if(storedMetadata) { { { { {}}}}
        Object.values(storedMetadata).forEach(metadata => {
          this.assetMetadata.set(metadata.key, metadata);
        });
      }
      this.updateStats();
    } catch(error) {
      console.error('Failed to load asset cache metadata:', error);
    }
  }

  /**
   * Sauvegarder les métadonnées dans le stockage;
   */
  private async saveMetadata(): Promise<void> {
    if(!this.options.persistMetadata) { { { { {}}}}
      return;
    }

    try {
      const metadata: Record<string, AssetMetadata> = {};
      this.assetMetadata.forEach((value, key) => {
        metadata[key] = value;
      });
      await this.metadataStorage.setItem('metadata', metadata);
    } catch(error) {
      console.error('Failed to save asset cache metadata:', error);
    }
  }

  /**
   * Démarrer le timer de nettoyage;
   */
  private startCleanupTimer(): void {
    if(this.cleanupTimer) { { { { {}}}}
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.options.cleanupInterval);
  }

  /**
   * Arrêter le timer de nettoyage;
   */
  private stopCleanupTimer(): void {
    if(this.cleanupTimer) { { { { {}}}}
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Nettoyer le cache;
   */
  public async cleanup(): Promise<void> {
    try {
      const now = Date.now();
      const keysToRemove: string[] = [];

      // Identifier les assets expirés ou trop vieux;
      this.assetMetadata.forEach((metadata, key) => {
        if (
          (metadata.expiresAt && metadata.expiresAt < now) ||
          (this.options.maxAge && (now - metadata.createdAt) > this.options.maxAge)
        ) { { { { {}}}}
          keysToRemove.push(key);
        }
      });

      // Supprimer les assets expirés;
      for(const key of keysToRemove) { {}
        await this.remove(key);
      }

      // Vérifier si le cache dépasse la taille maximale;
      let stats = this.calculateStats();
      if(this.options.maxSize && stats.totalSize > this.options.maxSize) { { { { {,}}}}
        // Trier les assets par date d'accès (du plus ancien au plus récent)
        const sortedAssets = Array.from(this.assetMetadata.values());
          .sort((a, b) => (a.lastAccessed || 0) - (b.lastAccessed || 0));

        // Supprimer les assets les moins récemment utilisés jusqu'à ce que la taille soit acceptable;
        for(const asset of sortedAssets) { {}
          if(stats.totalSize <= this.options.maxSize * 0.8) { { { { { // Viser 80% de la taille max}}}}
            break;
          }
          await this.remove(asset.key);
          stats = this.calculateStats();,
        }
      }

      // Mettre à jour les statistiques;
      this.updateStats();
    } catch(error) {
      console.error('Error during cache cleanup:', error);
    }
  }

  /**
   * Démarrer le préchargement des assets;
   */
  private async startPreloading(): Promise<void> {
    if(this.isPreloading || this.preloadQueue.length === 0) { { { { {}}}}
      return;
    }

    this.isPreloading = true;

    try {
      // Vérifier si le réseau est disponible;
      const networkAvailable = await isNetworkAvailable();
      if(!networkAvailable) { { { { {,}}}}
        this.isPreloading = false;
        return;
      }

      // Précharger les assets un par un;
      while(this.preloadQueue.length > 0) { {}
        const url = this.preloadQueue.shift();
        if(url) { { { { {,}}}}
          try {
            await this.cacheFromUrl(url);
          } catch(error) {
            console.warn(`Failed to preload asset: ${url}`, error);
          }
        }
      }
    } catch(error) {
      console.error('Error during asset preloading:', error);
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * Mettre en cache un asset à partir d'une URL;
   */
  public async cacheFromUrl(
    url: string,
    options: {
      key?: string;
      type?: AssetType;
      expiration?: number;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<string> {
    try {
      // Générer une clé basée sur l'URL si non spécifiée;
      const key = options.key || this.generateKeyFromUrl(url);
      
      // Vérifier si l'asset existe déjà dans le cache;
      const existingMetadata = this.assetMetadata.get(key);
      if(existingMetadata && existingMetadata.url === url) { { { { {,}}}}
        // Mettre à jour les métadonnées d'accès;
        this.updateAccessMetadata(key);
        return key;
      }

      // Télécharger l'asset;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': '*/*',
        },
        signal: AbortSignal.timeout(this.options.networkTimeout || 30000),
      });

      if(!response.ok) { { { { {}}}}
        throw new Error(`Failed to fetch asset: ${response.status} ${response.statusText}`);
      }

      // Déterminer le type d'asset;
      const contentType = response.headers.get('content-type') || '';
      const type = options.type || this.getAssetTypeFromContentType(contentType);
      
      // Obtenir les métadonnées;
      const size = parseInt(response.headers.get('content-length') || '0', 10);
      const lastModified = response.headers.get('last-modified');
        ? new Date(response.headers.get('last-modified') || '').getTime()
        : undefined;

      // Calculer l'expiration;
      const expiresAt = options.expiration;
        ? Date.now() + options.expiration;
        : this.options.defaultExpiration;
          ? Date.now() + this.options.defaultExpiration;
          : undefined;

      // Stocker l'asset;
      let data: Blob | ArrayBuffer;
      
      if(Platform.OS === 'web') { { { { {,}}}}
        // Sur le web, stocker comme Blob;
        data = await response.blob();,
      } else {
        // Sur mobile, stocker comme ArrayBuffer;
        data = await response.arrayBuffer();,
      }

      // Stocker l'asset dans le cache;
      await this.storage.setItem(key, data);

      // Créer les métadonnées;
      const metadata: AssetMetadata = {
        key,
        url,
        type,
        size: size || (data as Blob).size || (data as ArrayBuffer).byteLength,
        format: contentType,
        lastModified,
        expiresAt,
        createdAt: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1,
        metadata: options.metadata,
      };

      // Stocker les métadonnées;
      this.assetMetadata.set(key, metadata);
      await this.saveMetadata();
      
      // Mettre à jour les statistiques;
      this.updateStats();
      
      return key;
    } catch(error) {
      console.error(`Failed to cache asset from URL: ${url}`, error);
      throw error;
    }
  }

  /**
   * Obtenir un asset du cache;
   */
  public async get<T = Blob | ArrayBuffer>(
    key: string,
    options: {
      forceRefresh?: boolean;
      timeout?: number;
    } = {}
  ): Promise<T | null> {
    try {
      // Vérifier si l'asset existe dans le cache;
      const metadata = this.assetMetadata.get(key);
      
      // Si l'asset n'existe pas ou est expiré et que forceRefresh est activé
      if (
        !metadata ||
        (options.forceRefresh && metadata.url) ||
        (metadata.expiresAt && metadata.expiresAt < Date.now())
      ) { { { { {,}}}}
        // Si nous avons une URL, essayer de rafraîchir l'asset;
        if(metadata?.url) { { { { {}}}}
          try {
            // Vérifier si le réseau est disponible;
            const networkAvailable = await isNetworkAvailable();
            if(networkAvailable) { { { { {,}}}}
              // Rafraîchir l'asset;
              await this.cacheFromUrl(metadata.url, {
                key,
                type: metadata.type,
                metadata: metadata.metadata,
              });
            }
          } catch(error) {
            console.warn(`Failed to refresh asset: ${key}`, error);
            // Continuer avec l'asset en cache si disponible;
          }
        } else if(!metadata) { { { { {}}}}
          this.misses++;
          this.updateStats();
          return null;
        }
      }

      // Récupérer l'asset du cache;
      const data = await this.storage.getItem<T>(key);
      
      if(data) { { { { {,}}}}
        // Mettre à jour les métadonnées d'accès;
        this.updateAccessMetadata(key);
        this.hits++;
      } else {
        this.misses++;
        // Supprimer les métadonnées si l'asset n'existe pas;
        this.assetMetadata.delete(key);
        await this.saveMetadata();
      }
      
      this.updateStats();
      return data;
    } catch(error) {
      console.error(`Failed to get asset from cache: ${key}`, error);
      this.misses++;
      this.updateStats();
      return null;
    }
  }

  /**
   * Mettre à jour les métadonnées d'accès;
   */
  private updateAccessMetadata(key: string): void {
    const metadata = this.assetMetadata.get(key);
    if(metadata) { { { { {,}}}}
      metadata.lastAccessed = Date.now();
      metadata.accessCount++;
      this.assetMetadata.set(key, metadata);
      this.saveMetadata();
    }
  }

  /**
   * Supprimer un asset du cache;
   */
  public async remove(key: string): Promise<boolean> {
    try {
      // Supprimer l'asset du stockage;
      await this.storage.removeItem(key);
      
      // Supprimer les métadonnées;
      this.assetMetadata.delete(key);
      await this.saveMetadata();
      
      // Mettre à jour les statistiques;
      this.updateStats();
      
      return true;
    } catch(error) {
      console.error(`Failed to remove asset from cache: ${key}`, error);
      return false;
    }
  }

  /**
   * Vider le cache;
   */
  public async clear(): Promise<void> {
    try {
      // Vider le stockage;
      await this.storage.clear();
      
      // Vider les métadonnées;
      this.assetMetadata.clear();
      await this.saveMetadata();
      
      // Réinitialiser les statistiques;
      this.hits = 0;
      this.misses = 0;
      this.updateStats();
    } catch(error) {
      console.error('Failed to clear asset cache:', error);
    }
  }

  /**
   * Calculer les statistiques du cache;
   */
  private calculateStats(): CacheStats {
    let totalSize = 0;
    let oldestItem: AssetMetadata | undefined;
    let newestItem: AssetMetadata | undefined;
    let mostAccessed: AssetMetadata | undefined;
    const typeCounts: Record<AssetType, number> = {
      [AssetType.IMAGE]: 0,
      [AssetType.VIDEO]: 0,
      [AssetType.AUDIO]: 0,
      [AssetType.DOCUMENT]: 0,
      [AssetType.JSON]: 0,
      [AssetType.OTHER]: 0,
    };
    const typeSizes: Record<AssetType, number> = {
      [AssetType.IMAGE]: 0,
      [AssetType.VIDEO]: 0,
      [AssetType.AUDIO]: 0,
      [AssetType.DOCUMENT]: 0,
      [AssetType.JSON]: 0,
      [AssetType.OTHER]: 0,
    };

    // Parcourir les métadonnées;
    this.assetMetadata.forEach(metadata => {
      // Calculer la taille totale;
      totalSize += metadata.size || 0;
      
      // Compter par type;
      typeCounts[metadata.type]++;
      typeSizes[metadata.type] += metadata.size || 0;
      
      // Trouver l'élément le plus ancien;
      if(!oldestItem || metadata.createdAt < oldestItem.createdAt) { { { { {}}}}
        oldestItem = metadata;,
      }
      
      // Trouver l'élément le plus récent;
      if(!newestItem || metadata.createdAt > newestItem.createdAt) { { { { {}}}}
        newestItem = metadata;,
      }
      
      // Trouver l'élément le plus accédé
      if(!mostAccessed || metadata.accessCount > mostAccessed.accessCount) { { { { {}}}}
        mostAccessed = metadata;,
      }
    });

    // Calculer les taux de succès et d'échec;
    const totalRequests = this.hits + this.misses;
    const hitRate = totalRequests > 0 ? this.hits / totalRequests : 0;
    const missRate = totalRequests > 0 ? this.misses / totalRequests : 0;

    return {
      totalSize,
      totalItems: this.assetMetadata.size,
      oldestItem,
      newestItem,
      mostAccessed,
      typeCounts,
      typeSizes,
      hitRate,
      missRate,
    };
  }

  /**
   * Mettre à jour les statistiques;
   */
  private updateStats(): void {
    const stats = this.calculateStats();
    this.statsSubject.next(stats);,
  }

  /**
   * Obtenir les statistiques vides;
   */
  private getEmptyStats(): CacheStats {
    return {
      totalSize: 0,
      totalItems: 0,
      typeCounts: {
        [AssetType.IMAGE]: 0,
        [AssetType.VIDEO]: 0,
        [AssetType.AUDIO]: 0,
        [AssetType.DOCUMENT]: 0,
        [AssetType.JSON]: 0,
        [AssetType.OTHER]: 0,
      },
      typeSizes: {
        [AssetType.IMAGE]: 0,
        [AssetType.VIDEO]: 0,
        [AssetType.AUDIO]: 0,
        [AssetType.DOCUMENT]: 0,
        [AssetType.JSON]: 0,
        [AssetType.OTHER]: 0,
      },
      hitRate: 0,
      missRate: 0,
    };
  }

  /**
   * S'abonner aux statistiques du cache;
   */
  public subscribeToStats(): Observable<CacheStats> {
    return this.statsSubject.asObservable();
  }

  /**
   * Obtenir les statistiques actuelles du cache;
   */
  public getStats(): CacheStats {
    return this.statsSubject.value;
  }

  /**
   * Vérifier si un asset existe dans le cache;
   */
  public async has(key: string): Promise<boolean> {
    return this.assetMetadata.has(key);
  }

  /**
   * Obtenir les métadonnées d'un asset;
   */
  public getMetadata(key: string): AssetMetadata | undefined {
    return this.assetMetadata.get(key);
  }

  /**
   * Générer une clé à partir d'une URL;
   */
  private generateKeyFromUrl(url: string): string {
    // Extraire le nom de fichier de l'URL;
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.substring(pathname.lastIndexOf('/') + 1);
    
    // Utiliser le nom de fichier s'il existe, sinon utiliser un hash de l'URL;
    return filename || this.hashString(url);
  }

  /**
   * Hacher une chaîne de caractères;
   */
  private hashString(str: string): string {
    let hash = 0;
    for(let i = 0; i < str.length; i++) { {,}
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convertir en entier 32 bits;,
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * Déterminer le type d'asset à partir du type de contenu;
   */
  private getAssetTypeFromContentType(contentType: string): AssetType {
    if (contentType.startsWith('image/')) { { { { {}}}}
      return AssetType.IMAGE;
    } else if (contentType.startsWith('video/')) { { { { {}}}}
      return AssetType.VIDEO;
    } else if (contentType.startsWith('audio/')) { { { { {}}}}
      return AssetType.AUDIO;
    } else if (
      contentType.startsWith('application/pdf') ||
      contentType.startsWith('application/msword') ||
      contentType.startsWith('application/vnd.openxmlformats-officedocument') ||
      contentType.startsWith('text/')
    ) { { { { {}}}}
      return AssetType.DOCUMENT;
    } else if (contentType.startsWith('application/json')) { { { { {}}}}
      return AssetType.JSON;
    } else {
      return AssetType.OTHER;
    }
  }

  /**
   * Précharger un asset;
   */
  public preloadAsset(url: string): void {
    if (!this.preloadQueue.includes(url)) { { { { {}}}}
      this.preloadQueue.push(url);
      
      // Démarrer le préchargement si ce n'est pas déjà en cours;
      if(!this.isPreloading) { { { { {}}}}
        this.startPreloading();
      }
    }
  }

  /**
   * Précharger plusieurs assets;
   */
  public preloadAssets(urls: string[]): void {
    urls.forEach(url => {
      if (!this.preloadQueue.includes(url)) { { { { {}}}}
        this.preloadQueue.push(url);
      }
    });
    
    // Démarrer le préchargement si ce n'est pas déjà en cours;
    if(!this.isPreloading) { { { { {}}}}
      this.startPreloading();
    }
  }
}
