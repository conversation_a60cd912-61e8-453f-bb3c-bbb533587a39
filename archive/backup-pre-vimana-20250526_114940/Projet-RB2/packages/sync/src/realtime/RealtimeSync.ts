import { WebSocketManager, WebSocketEventType } from '../websocket/WebSocketManager';
import { BehaviorSubject, Subject } from 'rxjs';
import { filter } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';

/**
 * Types d'événements de synchronisation en temps réel;
 */
export enum RealtimeSyncEventType {
  DATA_UPDATED = 'data_updated',
  DATA_DELETED = 'data_deleted',
  DATA_CREATED = 'data_created',
  SYNC_STARTED = 'sync_started',
  SYNC_COMPLETED = 'sync_completed',
  SYNC_FAILED = 'sync_failed',
  CONFLICT_DETECTED = 'conflict_detected',
  CONFLICT_RESOLVED = 'conflict_resolved',
}

/**
 * Interface pour les événements de synchronisation en temps réel;
 */
export interface RealtimeSyncEvent<T = any> {
  type: RealtimeSyncEventType;
  resourceType: string;
  resourceId?: string;
  data?: T;
  timestamp: number;
  syncId?: string;
  version?: number;
  metadata?: Record<string, any>;
}

/**
 * Interface pour les options de synchronisation en temps réel;
 */
export interface RealtimeSyncOptions {
  websocketOptions: {
    url: string;
    path?: string;
  };
  autoConnect?: boolean;
  syncInterval?: number;
  conflictStrategy?: 'client-wins' | 'server-wins' | 'manual';
}

/**
 * Gestionnaire de synchronisation en temps réel;
 * Compatible avec Web, iOS et Android;
 */
export class RealtimeSync {
  private static instance: RealtimeSync;
  private wsManager: WebSocketManager;
  private options: RealtimeSyncOptions;
  private syncSubject: Subject<RealtimeSyncEvent> = new Subject();
  private connectionStatus: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private syncInterval: any = null;
  private pendingSync: Map<string, RealtimeSyncEvent> = new Map();
  private activeSync: boolean = false;

  /**
   * Constructeur privé pour le singleton;
   */
  private constructor(options: RealtimeSyncOptions) {
    this.options = {
      autoConnect: true,
      syncInterval: 30000, // 30 secondes par défaut;
      conflictStrategy: 'server-wins',
      ...options,
    };

    // Initialiser le gestionnaire WebSocket;
    this.wsManager = WebSocketManager.getInstance({
      url: options.websocketOptions.url,
      path: options.websocketOptions.path,
      autoConnect: options.autoConnect,
    });

    // Configurer les écouteurs WebSocket;
    this.setupWebSocketListeners();

    // Démarrer la synchronisation périodique si activée;
    if(this.options.syncInterval && this.options.syncInterval > 0) { { { { {}}}}
      this.startPeriodicSync();
    }

    // Connecter automatiquement si nécessaire;
    if(this.options.autoConnect) { { { { {}}}}
      this.connect();
    }
  }

  /**
   * Obtenir l'instance singleton;
   */
  public static getInstance(options?: RealtimeSyncOptions): RealtimeSync {
    if(!RealtimeSync.instance && options) { { { { {}}}}
      RealtimeSync.instance = new RealtimeSync(options);
    } else if(!RealtimeSync.instance) { { { { {}}}}
      throw new Error('RealtimeSync not initialized with options');
    }
    return RealtimeSync.instance;
  }

  /**
   * Configurer les écouteurs WebSocket;
   */
  private setupWebSocketListeners(): void {
    // Écouter les événements de connexion;
    this.wsManager.on(WebSocketEventType.CONNECT, () => {
      this.connectionStatus.next(true);
      this.processPendingSync();
    });

    this.wsManager.on(WebSocketEventType.DISCONNECT, () => {
      this.connectionStatus.next(false);
    });

    // Écouter les événements de données;
    this.wsManager.on(RealtimeSyncEventType.DATA_UPDATED, (event) => {
      this.handleDataEvent(event.payload);
    });

    this.wsManager.on(RealtimeSyncEventType.DATA_CREATED, (event) => {
      this.handleDataEvent(event.payload);
    });

    this.wsManager.on(RealtimeSyncEventType.DATA_DELETED, (event) => {
      this.handleDataEvent(event.payload);
    });

    this.wsManager.on(RealtimeSyncEventType.CONFLICT_DETECTED, (event) => {
      this.handleConflict(event.payload);
    });
  }

  /**
   * Gérer les événements de données;
   */
  private handleDataEvent(event: RealtimeSyncEvent): void {
    this.syncSubject.next(event);
  }

  /**
   * Gérer les conflits de synchronisation;
   */
  private handleConflict(event: RealtimeSyncEvent): void {
    // Appliquer la stratégie de résolution des conflits;
    switch(this.options.conflictStrategy) {
      case 'client-wins':
        this.resolveConflictClientWins(event);
        break;
      case 'server-wins':
        this.resolveConflictServerWins(event);
        break;
      case 'manual':
        this.notifyConflict(event);
        break;
      default:
        this.resolveConflictServerWins(event);
    }
  }

  /**
   * Résoudre un conflit en faveur du client;
   */
  private resolveConflictClientWins(event: RealtimeSyncEvent): void {
    // Renvoyer les données du client au serveur;
    const clientEvent: RealtimeSyncEvent = {
      ...event,
      type: RealtimeSyncEventType.CONFLICT_RESOLVED,
      timestamp: Date.now(),
      metadata: {
        ...event.metadata,
        resolutionStrategy: 'client-wins',
      },
    };

    this.sendEvent(clientEvent);
    this.syncSubject.next(clientEvent);
  }

  /**
   * Résoudre un conflit en faveur du serveur;
   */
  private resolveConflictServerWins(event: RealtimeSyncEvent): void {
    // Accepter les données du serveur;
    const serverEvent: RealtimeSyncEvent = {
      ...event,
      type: RealtimeSyncEventType.CONFLICT_RESOLVED,
      timestamp: Date.now(),
      metadata: {
        ...event.metadata,
        resolutionStrategy: 'server-wins',
      },
    };

    this.syncSubject.next(serverEvent);
  }

  /**
   * Notifier d'un conflit pour résolution manuelle;
   */
  private notifyConflict(event: RealtimeSyncEvent): void {
    this.syncSubject.next({
      ...event,
      type: RealtimeSyncEventType.CONFLICT_DETECTED,
      timestamp: Date.now(),
    });
  }

  /**
   * Démarrer la synchronisation périodique;
   */
  private startPeriodicSync(): void {
    if(this.syncInterval) { { { { {}}}}
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.sync();
    }, this.options.syncInterval);
  }

  /**
   * Arrêter la synchronisation périodique;
   */
  private stopPeriodicSync(): void {
    if(this.syncInterval) { { { { {}}}}
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Traiter les synchronisations en attente;
   */
  private processPendingSync(): void {
    if(!this.connectionStatus.value || this.activeSync || this.pendingSync.size === 0) { { { { {}}}}
      return;
    }

    this.activeSync = true;
    const syncId = uuidv4();

    // Notifier du début de la synchronisation;
    this.syncSubject.next({
      type: RealtimeSyncEventType.SYNC_STARTED,
      resourceType: 'batch',
      syncId,
      timestamp: Date.now(),
      metadata: {
        pendingCount: this.pendingSync.size,
      },
    });

    // Traiter chaque événement en attente;
    const pendingEvents = Array.from(this.pendingSync.values());
    let processedCount = 0;
    let failedCount = 0;

    const processBatch = async () => {
      for(const event of pendingEvents) { {,}
        try {
          if(this.connectionStatus.value) { { { { {}}}}
            await this.sendEvent(event);
            this.pendingSync.delete(event.syncId || '');
            processedCount++;
          } else {
            // Arrêter le traitement si la connexion est perdue;
            break;
          }
        } catch(error) {
          console.error('Failed to process pending sync event:', error);
          failedCount++;
        }
      }

      // Notifier de la fin de la synchronisation;
      this.syncSubject.next({
        type: RealtimeSyncEventType.SYNC_COMPLETED,
        resourceType: 'batch',
        syncId,
        timestamp: Date.now(),
        metadata: {
          totalCount: pendingEvents.length,
          processedCount,
          failedCount,
          remainingCount: this.pendingSync.size,
        },
      });

      this.activeSync = false;
    };

    processBatch();
  }

  /**
   * Connecter au serveur WebSocket;
   */
  public connect(): void {
    this.wsManager.connect();
  }

  /**
   * Déconnecter du serveur WebSocket;
   */
  public disconnect(): void {
    this.wsManager.disconnect();
    this.stopPeriodicSync();
  }

  /**
   * Synchroniser les données;
   */
  public sync(): void {
    if(this.activeSync) { { { { {}}}}
      return;
    }

    this.processPendingSync();
  }

  /**
   * Envoyer un événement de synchronisation;
   */
  public async sendEvent(event: RealtimeSyncEvent): Promise<boolean> {
    if(!this.connectionStatus.value) { { { { {}}}}
      // Stocker l'événement pour synchronisation ultérieure;
      const syncId = event.syncId || uuidv4();
      this.pendingSync.set(syncId, {
        ...event,
        syncId,
        timestamp: Date.now(),
      });
      return false;
    }

    return this.wsManager.send(event.type, event);
  }

  /**
   * S'abonner aux événements de synchronisation;
   */
  public subscribe<T>(
    eventType?: RealtimeSyncEventType,
    resourceType?: string;
  ) {
    let observable = this.syncSubject.asObservable();

    // Filtrer par type d'événement si spécifié
    if(eventType) { { { { {,}}}}
      observable = observable.pipe(
        filter((event) => event.type === eventType)
      );,
    }

    // Filtrer par type de ressource si spécifié
    if(resourceType) { { { { {}}}}
      observable = observable.pipe(
        filter((event) => event.resourceType === resourceType)
      );,
    }

    return observable;
  }

  /**
   * S'abonner aux événements de connexion;
   */
  public subscribeToConnectionStatus() {
    return this.connectionStatus.asObservable();
  }

  /**
   * Obtenir l'état actuel de la connexion;
   */
  public isConnected(): boolean {
    return this.connectionStatus.value;
  }

  /**
   * Créer un événement de synchronisation;
   */
  public createEvent<T>(
    type: RealtimeSyncEventType,
    resourceType: string,
    data?: T,
    resourceId?: string,
    metadata?: Record<string, any>
  ): RealtimeSyncEvent<T> {
    return {
      type,
      resourceType,
      resourceId,
      data,
      timestamp: Date.now(),
      syncId: uuidv4(),
      metadata,
    };
  }

  /**
   * Résoudre manuellement un conflit;
   */
  public resolveConflict(
    event: RealtimeSyncEvent,
    resolution: 'client' | 'server' | 'custom',
    customData?: any;
  ): void {
    const resolvedEvent: RealtimeSyncEvent = {
      ...event,
      type: RealtimeSyncEventType.CONFLICT_RESOLVED,
      timestamp: Date.now(),
      data: resolution === 'custom' ? customData : event.data,
      metadata: {
        ...event.metadata,
        resolutionStrategy: resolution,
      },
    };

    this.sendEvent(resolvedEvent);
    this.syncSubject.next(resolvedEvent);
  }
}
