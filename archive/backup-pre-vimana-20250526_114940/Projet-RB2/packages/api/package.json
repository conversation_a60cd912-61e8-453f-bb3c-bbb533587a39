{"name": "@projet-rb2/api", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "dev": "tsup src/index.ts --format esm,cjs --watch --dts", "lint": "eslint src/**/*.ts* --fix", "test": "jest", "test:coverage": "jest --coverage"}, "dependencies": {"@projet-rb2/core": "*", "axios": "^1.8.3", "axios-retry": "^4.0.0"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.8.4", "eslint": "^8.56.0", "jest": "^29.7.0", "msw": "^2.2.5", "ts-jest": "^29.1.1", "tsup": "^8.0.1", "typescript": "^5.4.5"}}