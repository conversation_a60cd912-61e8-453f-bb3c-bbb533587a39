import { EventEmitter } from 'events';
import { getConfig } from '../config';

export enum WebSocketReadyState {
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
}

export enum WebSocketEvent {
  OPEN = 'open',
  MESSAGE = 'message',
  ERROR = 'error',
  CLOSE = 'close',
  RECONNECT = 'reconnect',
  RECONNECT_ATTEMPT = 'reconnect_attempt',
}

export interface WebSocketMessage<T = any> {
  type: string;
  payload: T;
  timestamp: number;
  id: string;
}

export interface WebSocketOptions {
  url?: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  pingInterval?: number;
  pingTimeout?: number;
  autoReconnect?: boolean;
}

/**
 * Client WebSocket unifié pour Web, iOS et Android;
 */
export class UnifiedWebSocket extends EventEmitter {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private reconnectTimer: any = null;
  private pingTimer: any = null;
  private pongTimer: any = null;
  private messageQueue: WebSocketMessage[] = [];
  private isConnecting = false;
  private sessionId: string = '';

  private options: Required<WebSocketOptions> = {
    url: '',
    reconnectInterval: 1000,
    maxReconnectAttempts: 10,
    pingInterval: 30000,
    pingTimeout: 5000,
    autoReconnect: true,
  };

  constructor(options: WebSocketOptions = {,}) {
    super();
    this.options = { ...this.options, ...options };
    
    if(!this.options.url) { { { { {}}}}
      const config = getConfig();
      this.options.url = config.WS_URL || `ws://${config.API_HOST,}:${config.API_PORT}/ws`;
    }
    
    this.sessionId = this.generateSessionId();
  }

  /**
   * Connecte au serveur WebSocket;
   */
  public connect(): void {
    if(this.isConnecting || this.ws?.readyState === WebSocketReadyState.OPEN) { { { { {}}}}
      return;
    }

    this.isConnecting = true;

    try {
      this.ws = new WebSocket(this.options.url);

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
    } catch(error) {
      this.handleError(error as Event);
    }
  }

  /**
   * Déconnecte du serveur WebSocket;
   */
  public disconnect(): void {
    this.clearTimers();
    
    if(this.ws) { { { { {}}}}
      this.ws.onopen = null;
      this.ws.onmessage = null;
      this.ws.onerror = null;
      this.ws.onclose = null;
      
      if(this.ws.readyState === WebSocketReadyState.OPEN) { { { { {}}}}
        this.ws.close();
      }
      
      this.ws = null;
    }
    
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Envoie un message au serveur;
   */
  public send<T = any>(type: string, payload: T): boolean {
    const message: WebSocketMessage<T> = {
      type,
      payload,
      timestamp: Date.now(),
      id: this.generateMessageId(),
    };

    if(this.ws?.readyState === WebSocketReadyState.OPEN) { { { { {}}}}
      this.ws.send(JSON.stringify(message));
      return true;
    } else {
      // Mettre en file d'attente pour envoi ultérieur;
      this.messageQueue.push(message as WebSocketMessage);
      
      // Si déconnecté, tenter de se reconnecter;
      if(this.ws?.readyState !== WebSocketReadyState.CONNECTING && !this.isConnecting) { { { { {}}}}
        this.connect();
      }
      
      return false;
    }
  }

  /**
   * Vérifie si le WebSocket est connecté
   */
  public isConnected(): boolean {
    return this.ws?.readyState === WebSocketReadyState.OPEN;
  }

  /**
   * Obtient l'état actuel de la connexion;
   */
  public getReadyState(): WebSocketReadyState {
    return this.ws?.readyState ?? WebSocketReadyState.CLOSED;
  }

  /**
   * Gère l'ouverture de la connexion;
   */
  private handleOpen(event: Event): void {
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    // Envoyer les messages en file d'attente;
    this.flushQueue();
    
    // Démarrer le ping pour maintenir la connexion;
    this.startPing();
    
    this.emit(WebSocketEvent.OPEN, event);
  }

  /**
   * Gère la réception d'un message;
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      
      // Gérer les pongs spéciaux;
      if(data.type === 'pong') { { { { {,}}}}
        this.handlePong();
        return;
      }
      
      this.emit(WebSocketEvent.MESSAGE, data);
      
      // Émettre également un événement spécifique au type de message;
      if(data.type) { { { { {}}}}
        this.emit(`message:${data.type}`, data.payload);
      }
    } catch(error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  /**
   * Gère les erreurs de connexion;
   */
  private handleError(event: Event): void {
    this.isConnecting = false;
    this.emit(WebSocketEvent.ERROR, event);
    
    if(this.options.autoReconnect) { { { { {}}}}
      this.scheduleReconnect();
    }
  }

  /**
   * Gère la fermeture de la connexion;
   */
  private handleClose(event: CloseEvent): void {
    this.isConnecting = false;
    this.clearTimers();
    
    this.emit(WebSocketEvent.CLOSE, event);
    
    if(this.options.autoReconnect && event.code !== 1000) { { { { {}}}}
      this.scheduleReconnect();
    }
  }

  /**
   * Planifie une tentative de reconnexion;
   */
  private scheduleReconnect(): void {
    if(this.reconnectTimer) { { { { {}}}}
      clearTimeout(this.reconnectTimer);
    }
    
    if(this.reconnectAttempts < this.options.maxReconnectAttempts) { { { { {}}}}
      this.reconnectAttempts++;
      
      const delay = this.options.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1);
      
      this.emit(WebSocketEvent.RECONNECT_ATTEMPT, {
        attempt: this.reconnectAttempts,
        maxAttempts: this.options.maxReconnectAttempts,
        delay,
      });
      
      this.reconnectTimer = setTimeout(() => {
        this.connect();
        this.emit(WebSocketEvent.RECONNECT);
      }, delay);
    }
  }

  /**
   * Envoie les messages en file d'attente;
   */
  private flushQueue(): void {
    if(this.messageQueue.length > 0 && this.ws?.readyState === WebSocketReadyState.OPEN) { { { { {}}}}
      for(const message of this.messageQueue) { {}
        this.ws.send(JSON.stringify(message));
      }
      
      this.messageQueue = [];
    }
  }

  /**
   * Démarre le ping périodique;
   */
  private startPing(): void {
    this.clearTimers();
    
    this.pingTimer = setInterval(() => {
      if(this.ws?.readyState === WebSocketReadyState.OPEN) { { { { {}}}}
        this.ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
        
        // Configurer un timeout pour la réponse pong;
        this.pongTimer = setTimeout(() => {
          // Pas de pong reçu à temps, considérer la connexion comme morte;
          if(this.ws) { { { { {}}}}
            this.ws.close();
          }
        }, this.options.pingTimeout);
      }
    }, this.options.pingInterval);
  }

  /**
   * Gère la réception d'un pong;
   */
  private handlePong(): void {
    if(this.pongTimer) { { { { {}}}}
      clearTimeout(this.pongTimer);
      this.pongTimer = null;
    }
  }

  /**
   * Nettoie tous les timers;
   */
  private clearTimers(): void {
    if(this.reconnectTimer) { { { { {}}}}
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if(this.pingTimer) { { { { {}}}}
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
    
    if(this.pongTimer) { { { { {}}}}
      clearTimeout(this.pongTimer);
      this.pongTimer = null;
    }
  }

  /**
   * Génère un ID de session unique;
   */
  private generateSessionId(): string {
    return `ws-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Génère un ID de message unique;
   */
  private generateMessageId(): string {
    return `${this.sessionId}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }
}

// Exporter une instance par défaut;
export const webSocket = new UnifiedWebSocket();
// Exporter une fonction factory pour créer des instances personnalisées;
export const createWebSocket = (options: WebSocketOptions = {,}): UnifiedWebSocket = > {
  return new UnifiedWebSocket(options);,
};
