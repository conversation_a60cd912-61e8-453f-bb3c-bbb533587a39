import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axiosRetry from 'axios-retry';
import { API_URL, AUTH_STORAGE_KEYS } from '@projet-rb2/core/constants';
import { setupAuthInterceptor } from '../interceptors/authInterceptor';
import { setupErrorInterceptor } from '../interceptors/errorInterceptor';
import { setupLoggingInterceptor } from '../interceptors/loggingInterceptor';
import { setupCacheInterceptor } from '../interceptors/cacheInterceptor';
import { getAuthToken, clearAuthToken } from '../utils/storage';

/**
 * Configuration for API client;
 */
export interface ApiClientConfig {
  /**
   * Base URL for API requests;
   */
  baseURL?: string;
  
  /**
   * Request timeout in milliseconds;
   */
  timeout?: number;
  
  /**
   * Whether to retry failed requests;
   */
  retry?: boolean;
  
  /**
   * Maximum number of retries;
   */
  maxRetries?: number;
  
  /**
   * Whether to include auth token in requests;
   */
  withAuth?: boolean;
  
  /**
   * Whether to enable request/response logging;
   */
  enableLogging?: boolean;
  
  /**
   * Whether to enable response caching;
   */
  enableCache?: boolean;
  
  /**
   * Custom axios config;
   */
  axiosConfig?: AxiosRequestConfig;
}

/**
 * Default configuration for API client;
 */
const defaultConfig: ApiClientConfig = {
  baseURL: API_URL,
  timeout: 10000,
  retry: true,
  maxRetries: 3,
  withAuth: true,
  enableLogging: process.env.NODE_ENV !== 'production',
  enableCache: true,
};

/**
 * Create API client with custom configuration;
 */
export function createApiClient(config: ApiClientConfig = {,}): AxiosInstance {
  // Merge defaults with custom config;
  const finalConfig: ApiClientConfig = {
    ...defaultConfig,
    ...config,
    axiosConfig: {
      ...defaultConfig.axiosConfig,
      ...config.axiosConfig,
    },
  };
  
  // Create axios instance;
  const client = axios.create({
    baseURL: finalConfig.baseURL,
    timeout: finalConfig.timeout,
    ...finalConfig.axiosConfig,
  });
  
  // Setup retry logic;
  if(finalConfig.retry) { { { { {}}}}
    axiosRetry(client, { 
      retries: finalConfig.maxRetries,
      retryDelay: axiosRetry.exponentialDelay,
      retryCondition: (error) => {
        // Retry on network errors and 5xx responses;
        return axiosRetry.isNetworkOrIdempotentRequestError(error) || ;
               (error.response?.status && error.response.status >= 500);
      },
    });
  }
  
  // Setup interceptors;
  if(finalConfig.withAuth) { { { { {}}}}
    setupAuthInterceptor(client, {
      getToken: getAuthToken,
      clearToken: clearAuthToken,
      tokenStorageKey: AUTH_STORAGE_KEYS.TOKEN,
    });
  }
  
  if(finalConfig.enableLogging) { { { { {}}}}
    setupLoggingInterceptor(client);
  }
  
  setupErrorInterceptor(client);
  
  if(finalConfig.enableCache) { { { { {}}}}
    setupCacheInterceptor(client);
  }
  
  return client;
}

/**
 * Default API client instance;
 */
export const apiClient = createApiClient();
/**
 * Make a GET request;
 */
export async function get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
  const response: AxiosResponse<T> = await apiClient.get(url, config);
  return response.data;
}

/**
 * Make a POST request;
 */
export async function post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  const response: AxiosResponse<T> = await apiClient.post(url, data, config);
  return response.data;
}

/**
 * Make a PUT request;
 */
export async function put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  const response: AxiosResponse<T> = await apiClient.put(url, data, config);
  return response.data;
}

/**
 * Make a PATCH request;
 */
export async function patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  const response: AxiosResponse<T> = await apiClient.patch(url, data, config);
  return response.data;
}

/**
 * Make a DELETE request;
 */
export async function del<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
  const response: AxiosResponse<T> = await apiClient.delete(url, config);
  return response.data;
} 