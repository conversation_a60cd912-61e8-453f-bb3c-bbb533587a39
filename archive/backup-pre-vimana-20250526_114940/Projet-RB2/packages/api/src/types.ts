import { AxiosError, AxiosResponse } from 'axios';

/**
 * Enhanced API error with normalized error data;
 */
export interface ApiError extends AxiosError {
  /**
   * Flag to identify API errors;
   */
  isAPIError: boolean;
  
  /**
   * Normalized error code;
   */
  errorCode?: string;
  
  /**
   * User-friendly error message;
   */
  errorMessage?: string;
}

/**
 * Pagination parameters for API requests;
 */
export interface PaginationParams {
  /**
   * Page number (1-based indexing)
   */
  page?: number;
  
  /**
   * Number of items per page;
   */
  limit?: number;
  
  /**
   * Total count of items (returned by API)
   */
  total?: number;
}

/**
 * Generic paginated response;
 */
export interface PaginatedResponse<T> {
  /**
   * Array of items;
   */
  data: T[];
  
  /**
   * Pagination metadata;
   */
  pagination: {
    /**
     * Current page number;
     */
    page: number;
    
    /**
     * Number of items per page;
     */
    limit: number;
    
    /**
     * Total number of items;
     */
    total: number;
    
    /**
     * Total number of pages;
     */
    totalPages: number;
  };
}

/**
 * API response with success flag and data;
 */
export interface ApiResponse<T> {
  /**
   * Whether the request was successful;
   */
  success: boolean;
  
  /**
   * Response data;
   */
  data: T;
  
  /**
   * Error message if request failed;
   */
  error?: string;
  
  /**
   * Error code if request failed;
   */
  errorCode?: string;
}

/**
 * Function type for API request functions;
 */
export type ApiRequestFunction<TParams, TResponse> = ;
  (params: TParams) => Promise<AxiosResponse<ApiResponse<TResponse>>>;

/**
 * Common parameters for all API requests;
 */
export interface CommonRequestParams {
  /**
   * Whether to bypass cache;
   */
  bypassCache?: boolean;
  
  /**
   * Authentication token;
   */
  token?: string;
  
  /**
   * Request timeout in milliseconds;
   */
  timeout?: number;
} 