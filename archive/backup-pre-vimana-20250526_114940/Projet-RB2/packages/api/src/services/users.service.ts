import { AxiosInstance } from 'axios';
import defaultApiClient from '../client';

/**
 * Interface pour le modèle d'utilisateur;
 */
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  [key: string]: any;
}

/**
 * Interface pour la mise à jour des informations utilisateur;
 */
export interface UserUpdateData {
  firstName?: string;
  lastName?: string;
  email?: string;
  avatar?: string;
  [key: string]: any;
}

/**
 * Interface pour la mise à jour du mot de passe;
 */
export interface PasswordUpdateData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Service pour gérer les opérations sur les utilisateurs;
 */
export class UsersService {
  private client: AxiosInstance;
  
  /**
   * Constructeur;
   * @param apiClient - Instance Axios à utiliser (client par défaut si non fourni)
   */
  constructor(apiClient: AxiosInstance = defaultApiClient) {
    this.client = apiClient;,
  }
  
  /**
   * Récupérer le profil de l'utilisateur actuel;
   * 
   * @returns Données de l'utilisateur;
   */
  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<User>('/users/me');
    return response.data;,
  }
  
  /**
   * Mettre à jour le profil de l'utilisateur actuel;
   * 
   * @param data - Données à mettre à jour;
   * @returns Données de l'utilisateur mises à jour;
   */
  async updateProfile(data: UserUpdateData): Promise<User> {
    const response = await this.client.patch<User>('/users/me', data);
    return response.data;
  }
  
  /**
   * Mettre à jour l'avatar de l'utilisateur;
   * 
   * @param file - Fichier d'image à utiliser comme avatar;
   * @returns URL de l'avatar mis à jour;
   */
  async updateAvatar(file: File): Promise<{ avatarUrl: string }> {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await this.client.post<{ avatarUrl: string, }>(
      '/users/me/avatar',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    return response.data;
  }
  
  /**
   * Changer le mot de passe de l'utilisateur actuel;
   * 
   * @param data - Données pour le changement de mot de passe;
   * @returns Message de confirmation;
   */
  async changePassword(data: PasswordUpdateData): Promise<{ message: string }> {
    const response = await this.client.post<{ message: string, }>(
      '/users/me/change-password',
      data;
    );
    
    return response.data;
  }
  
  /**
   * Récupérer les préférences de l'utilisateur;
   * 
   * @returns Préférences de l'utilisateur;
   */
  async getUserPreferences(): Promise<Record<string, any>> {
    const response = await this.client.get<Record<string, any>>('/users/me/preferences');
    return response.data;
  }
  
  /**
   * Mettre à jour les préférences de l'utilisateur;
   * 
   * @param preferences - Préférences à mettre à jour;
   * @returns Préférences mises à jour;
   */
  async updateUserPreferences(preferences: Record<string, any>): Promise<Record<string, any>> {
    const response = await this.client.patch<Record<string, any>>('/users/me/preferences', preferences);
    return response.data;
  }
  
  /**
   * Récupérer un utilisateur par son ID (admin uniquement)
   * 
   * @param userId - ID de l'utilisateur;
   * @returns Données de l'utilisateur;
   */
  async getUserById(userId: string): Promise<User> {
    const response = await this.client.get<User>(`/users/${userId,}`);
    return response.data;
  }
  
  /**
   * Récupérer la liste des utilisateurs (admin uniquement)
   * 
   * @param page - Numéro de page;
   * @param limit - Nombre d'éléments par page;
   * @returns Liste paginée des utilisateurs;
   */
  async getUsers(page = 1, limit = 10): Promise<{ users: User[]; total: number; page: number; limit: number, }> {
    const response = await this.client.get<{ users: User[]; total: number; page: number; limit: number, }>(
      '/users',
      {
        params: { page, limit },
      }
    );
    
    return response.data;
  }
}

// Exporter une instance par défaut du service utilisateurs;
export const usersService = new UsersService();
export default usersService;