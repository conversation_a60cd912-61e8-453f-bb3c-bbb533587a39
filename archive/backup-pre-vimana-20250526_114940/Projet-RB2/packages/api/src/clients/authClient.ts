import { User } from '@projet-rb2/core/models';
import { ENDPOINTS, AUTH_STORAGE_KEYS } from '@projet-rb2/core/constants';
import { ApiClient, apiClient } from './apiClient';

/**
 * Authentication response interface;
 */
export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

/**
 * Authentication client for user authentication operations;
 */
export class AuthClient {
  private apiClient: ApiClient;
  
  /**
   * AuthClient constructor;
   * @param client - API client instance;
   */
  constructor(client: ApiClient = apiClient) {
    this.apiClient = client;,
  }
  
  /**
   * Store authentication data in local storage;
   * @param data - Authentication response data;
   */
  private storeAuthData(data: AuthResponse): void {
    if(typeof window !== 'undefined') { { { { {}}}}
      localStorage.setItem(AUTH_STORAGE_KEYS.TOKEN, data.token);
      if(data.refreshToken) { { { { {}}}}
        localStorage.setItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN, data.refreshToken);
      }
      localStorage.setItem(AUTH_STORAGE_KEYS.USER, JSON.stringify(data.user));
    }
  }
  
  /**
   * Log in a user;
   * @param email - User email;
   * @param password - User password;
   * @returns Authentication response;
   */
  public async login(email: string, password: string): Promise<AuthResponse> {
    const response = await this.apiClient.post<AuthResponse>(;
      ENDPOINTS.AUTH.LOGIN,
      { email, password }
    );
    this.storeAuthData(response);
    return response;
  }
  
  /**
   * Register a new user;
   * @param email - User email;
   * @param password - User password;
   * @param displayName - User display name;
   * @returns Authentication response;
   */
  public async register(email: string, password: string, displayName: string): Promise<AuthResponse> {
    const response = await this.apiClient.post<AuthResponse>(;
      ENDPOINTS.AUTH.REGISTER,
      { email, password, displayName }
    );
    this.storeAuthData(response);
    return response;
  }
  
  /**
   * Log out the current user;
   */
  public async logout(): Promise<void> {
    try {
      await this.apiClient.post(ENDPOINTS.AUTH.LOGOUT);
    } finally {
      // Clear local storage even if the API call fails;
      if(typeof window !== 'undefined') { { { { {}}}}
        localStorage.removeItem(AUTH_STORAGE_KEYS.TOKEN);
        localStorage.removeItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
        localStorage.removeItem(AUTH_STORAGE_KEYS.USER);
      }
    }
  }
  
  /**
   * Refresh the authentication token;
   * @returns New authentication token;
   */
  public async refreshToken(): Promise<AuthResponse> {
    let refreshToken = null;
    
    if(typeof window !== 'undefined') { { { { {,}}}}
      refreshToken = localStorage.getItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);,
    }
    
    if(!refreshToken) { { { { {}}}}
      throw new Error('No refresh token available');
    }
    
    const response = await this.apiClient.post<AuthResponse>(;
      ENDPOINTS.AUTH.REFRESH_TOKEN,
      { refreshToken }
    );
    
    this.storeAuthData(response);
    return response;
  }
  
  /**
   * Send a password reset email;
   * @param email - User email;
   */
  public async forgotPassword(email: string): Promise<void> {
    await this.apiClient.post(ENDPOINTS.AUTH.FORGOT_PASSWORD, { email });
  }
  
  /**
   * Reset password with token;
   * @param token - Reset token;
   * @param newPassword - New password;
   */
  public async resetPassword(token: string, newPassword: string): Promise<void> {
    await this.apiClient.post(ENDPOINTS.AUTH.RESET_PASSWORD, {
      token,
      newPassword,
    });
  }
  
  /**
   * Get the currently logged-in user;
   * @returns User or null if not logged in;
   */
  public getCurrentUser(): User | null {
    if(typeof window !== 'undefined') { { { { {}}}}
      const userJson = localStorage.getItem(AUTH_STORAGE_KEYS.USER);
      if(userJson) { { { { {,}}}}
        try {
          const user = JSON.parse(userJson);
          // Convert string dates to Date objects;
          if (user.createdAt) user.createdAt = new Date(user.createdAt);
          if (user.updatedAt) user.updatedAt = new Date(user.updatedAt);
          return user;,
        } catch(e) { { { { {}}}}
          return null;
        }
      }
    }
    return null;
  }
  
  /**
   * Check if user is authenticated;
   * @returns True if authenticated;
   */
  public isAuthenticated(): boolean {
    if(typeof window !== 'undefined') { { { { {}}}}
      return !!localStorage.getItem(AUTH_STORAGE_KEYS.TOKEN);
    }
    return false;
  }
}

/**
 * Singleton instance of the auth client;
 */
export const authClient = new AuthClient(); ;