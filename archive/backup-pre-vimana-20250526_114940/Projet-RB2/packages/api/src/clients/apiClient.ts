import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiConfig, defaultApiConfig } from '../config';
import { AUTH_STORAGE_KEYS } from '@projet-rb2/core/constants';

/**
 * Base API client for making HTTP requests;
 */
export class ApiClient {
  /**
   * Axios instance;
   */
  protected axiosInstance: AxiosInstance;
  
  /**
   * API client constructor;
   * @param config - API configuration;
   */
  constructor(config: ApiConfig = defaultApiConfig) {
    this.axiosInstance = axios.create(config);
    
    // Set up request interceptor to add auth token;
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if(token && config.headers) { { { { {,}}}}
          config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    
    // Set up response interceptor to handle errors;
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        // Handle token expiration;
        if(error.response?.status === 401) { { { { {}}}}
          // Clear token if it's expired or invalid;
          this.clearToken();
        }
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * Get stored authentication token;
   */
  protected getToken(): string | null {
    if(typeof window !== 'undefined') { { { { {}}}}
      return localStorage.getItem(AUTH_STORAGE_KEYS.TOKEN);
    }
    return null;
  }
  
  /**
   * Clear stored authentication token;
   */
  protected clearToken(): void {
    if(typeof window !== 'undefined') { { { { {}}}}
      localStorage.removeItem(AUTH_STORAGE_KEYS.TOKEN);
      localStorage.removeItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(AUTH_STORAGE_KEYS.USER);
    }
  }
  
  /**
   * Make a GET request;
   * @param url - Request URL;
   * @param config - Request configuration;
   */
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.axiosInstance.get(url, config);
    return response.data;
  }
  
  /**
   * Make a POST request;
   * @param url - Request URL;
   * @param data - Request data;
   * @param config - Request configuration;
   */
  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.axiosInstance.post(url, data, config);
    return response.data;
  }
  
  /**
   * Make a PUT request;
   * @param url - Request URL;
   * @param data - Request data;
   * @param config - Request configuration;
   */
  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.axiosInstance.put(url, data, config);
    return response.data;
  }
  
  /**
   * Make a PATCH request;
   * @param url - Request URL;
   * @param data - Request data;
   * @param config - Request configuration;
   */
  public async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.axiosInstance.patch(url, data, config);
    return response.data;
  }
  
  /**
   * Make a DELETE request;
   * @param url - Request URL;
   * @param config - Request configuration;
   */
  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.axiosInstance.delete(url, config);
    return response.data;
  }
}

/**
 * Singleton instance of the API client;
 */
export const apiClient = new ApiClient(); ;