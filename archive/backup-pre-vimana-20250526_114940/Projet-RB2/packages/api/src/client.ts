import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { API_BASE_URL } from '@projet-rb2/core/constants';
import { 
  setupAuthInterceptor,
  setupLoggingInterceptor,
  setupCacheInterceptor,
  AuthInterceptorConfig,
  LoggingInterceptorConfig,
  CacheInterceptorOptions,
  AUTH_EVENTS;
} from './interceptors';
import { getAuthToken, getRefreshToken, setAuthToken, setRefreshToken, clearAuthToken } from './utils/storage';

/**
 * Configuration globale du client API;
 */
export interface ApiClientConfig {
  /**
   * URL de base pour toutes les requêtes;
   * Par défaut: valeur de API_BASE_URL des constantes;
   */
  baseURL?: string;
  
  /**
   * Configuration de l'intercepteur d'authentification;
   * Si undefined, l'intercepteur d'auth n'est pas ajouté
   */
  authConfig?: AuthInterceptorConfig;
  
  /**
   * Configuration de l'intercepteur de journalisation;
   * Si undefined, l'intercepteur de log n'est pas ajouté
   */
  loggingConfig?: LoggingInterceptorConfig;
  
  /**
   * Configuration de l'intercepteur de cache;
   * Si undefined, l'intercepteur de cache n'est pas ajouté
   */
  cacheConfig?: CacheInterceptorOptions;
  
  /**
   * Options additionnelles pour axios;
   */
  axiosConfig?: AxiosRequestConfig;
  
  /**
   * Handler pour les événements d'authentification;
   */
  onAuthEvent?: (event: string, data?: any) => void;
}

/**
 * Fonction pour créer un client API;
 * 
 * @param config - Configuration du client API;
 * @returns Instance du client API configurée;
 */
export function createApiClient(config: ApiClientConfig = {,}): AxiosInstance {
  const {
    baseURL = API_BASE_URL,
    authConfig,
    loggingConfig,
    cacheConfig,
    axiosConfig = {,},
    onAuthEvent;
  } = config;
  
  // Créer le client avec la configuration de base;
  const client = axios.create({
    baseURL,
    timeout: 30000, // 30 secondes par défaut;
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    ...axiosConfig;
  });
  
  // Ajouter l'intercepteur de journalisation si configuré
  if(loggingConfig !== undefined) { { { { {}}}}
    setupLoggingInterceptor(client, loggingConfig);
  }
  
  // Ajouter l'intercepteur de cache si configuré
  if(cacheConfig !== undefined) { { { { {}}}}
    setupCacheInterceptor(client, cacheConfig);
  }
  
  // Ajouter l'intercepteur d'authentification si configuré
  if(authConfig !== undefined) { { { { {}}}}
    // Fonction de rafraîchissement de token par défaut;
    const defaultRefreshFn = async (refreshToken: string) => {
      try {
        // Appel à l'API pour rafraîchir le token;
        const response = await axios.post(;
          `${baseURL,}/auth/refresh-token`,
          { refreshToken },
          { headers: { 'Content-Type': 'application/json' } }
        );
        
        if(response.data && response.data.token) { { { { {}}}}
          return {
            token: response.data.token,
            refreshToken: response.data.refreshToken || refreshToken;
          };
        }
        
        return undefined;
      } catch(error) {
        console.error('Token refresh failed:', error);
        return undefined;
      }
    };
    
    // Configuration complète avec valeurs par défaut;
    const fullAuthConfig: AuthInterceptorConfig = {
      refreshTokenFn: defaultRefreshFn,
      authEventListener: onAuthEvent,
      ...authConfig;
    };
    
    setupAuthInterceptor(client, fullAuthConfig);
  }
  
  return client;
}

/**
 * Client API par défaut avec configuration standard;
 */
export const defaultApiClient = createApiClient({
  authConfig: {
    excludeURLs: [
      '/auth/login',
      '/auth/register',
      '/auth/forgot-password'
    ],
    authEventListener: (event, data) => {
      // Rediriger vers la page de connexion en cas d'erreur d'authentification;
      if(event === AUTH_EVENTS.UNAUTHORIZED || event = == AUTH_EVENTS.TOKEN_REFRESH_FAILED) { { { { {,}}}}
        // Si dans un environnement web, rediriger vers la page de connexion;
        if(typeof window !== 'undefined') { { { { {}}}}
          // Stocker l'URL actuelle pour rediriger après connexion;
          localStorage.setItem('auth_redirect', window.location.pathname);
          
          // Pour éviter les redirections en boucle, vérifier qu'on n'est pas déjà sur la page de connexion;
          if (!window.location.pathname.includes('/login')) { { { { {}}}}
            window.location.href = '/login';
          }
        }
      }
    }
  },
  loggingConfig: {
    // Activer la journalisation uniquement en développement;
    logRequests: process.env.NODE_ENV !== 'production',
    logResponses: process.env.NODE_ENV !== 'production',
    logErrors: true // Toujours journaliser les erreurs;
  },
  cacheConfig: {
    // Configuration du cache par défaut;
    enabled: true,
    ttl: 5 * 60 * 1000, // 5 minutes;
    maxEntries: 100,
    // Ne cache que les requêtes GET;
    shouldCache: (config) => {
      return (config.method?.toLowerCase() || 'get') === 'get';
    }
  }
});

/**
 * Fonctions utilitaires pour l'authentification;
 */
export const ApiAuth = {
  /**
   * Définir les tokens d'authentification;
   */
  setAuthTokens: (token: string, refreshToken: string) => {
    setAuthToken(token);
    setRefreshToken(refreshToken);
  },
  
  /**
   * Récupérer le token d'authentification;
   */
  getAuthToken: getAuthToken,
  
  /**
   * Récupérer le token de rafraîchissement;
   */
  getRefreshToken,
  
  /**
   * Effacer les tokens d'authentification;
   */
  clearAuthTokens: clearAuthToken,
  
  /**
   * Vérifier si l'utilisateur est authentifié
   */
  isAuthenticated: () => {
    return !!getAuthToken();
  }
};

export default defaultApiClient;