import { API_URL } from '@projet-rb2/core/constants';

/**
 * API configuration options;
 */
export interface ApiConfig {
  /**
   * Base URL for API requests;
   */
  baseURL: string;
  
  /**
   * Request timeout in milliseconds;
   */
  timeout: number;
  
  /**
   * Whether to include credentials in requests;
   */
  withCredentials: boolean;
  
  /**
   * Default headers for all requests;
   */
  headers: Record<string, string>;
}

/**
 * Default API configuration;
 */
export const defaultApiConfig: ApiConfig = {
  baseURL: API_URL,
  timeout: 30000, // 30 seconds;
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

/**
 * Create API configuration by extending the default config;
 */
export function createApiConfig(overrides?: Partial<ApiConfig>): ApiConfig {
  return {
    ...defaultApiConfig,
    ...overrides,
    headers: {
      ...defaultApiConfig.headers,
      ...(overrides?.headers || {}),
    },
  };
} 