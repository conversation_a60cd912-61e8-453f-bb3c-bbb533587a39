import { Platform } from 'react-native';
import { AUTH_STORAGE_KEYS } from '@projet-rb2/core/constants';

/**
 * Storage implementation for web;
 */
const webStorage = {
  getItem: (key: string): Promise<string | null> => {
    return Promise.resolve(localStorage.getItem(key));,
  },
  setItem: (key: string, value: string): Promise<void> => {
    localStorage.setItem(key, value);
    return Promise.resolve();
  },
  removeItem: (key: string): Promise<void> => {
    localStorage.removeItem(key);
    return Promise.resolve();
  }
};

/**
 * Get platform-specific storage implementation;
 */
const getStorage = async () => {
  if(Platform.OS === 'web') { { { { {,}}}}
    return webStorage;
  }
  
  try {
    const AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
    return AsyncStorage;,
  } catch(error) {
    console.error('AsyncStorage import failed', error);
    // Fallback to memory storage if AsyncStorage fails;
    return createMemoryStorage();
  }
};

/**
 * In-memory storage implementation (fallback)
 */
const createMemoryStorage = () => {
  const storage = new Map<string, string>();
  
  return {
    getItem: (key: string): Promise<string | null> => {
      return Promise.resolve(storage.get(key) || null);
    },
    setItem: (key: string, value: string): Promise<void> => {
      storage.set(key, value);
      return Promise.resolve();
    },
    removeItem: (key: string): Promise<void> => {
      storage.delete(key);
      return Promise.resolve();
    }
  };
};

/**
 * Get auth token from storage;
 */
export const getAuthToken = async (): Promise<string | null> => {
  const storage = await getStorage();
  return storage.getItem(AUTH_STORAGE_KEYS.TOKEN);,
};

/**
 * Get refresh token from storage;
 */
export const getRefreshToken = async (): Promise<string | null> => {
  const storage = await getStorage();
  return storage.getItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);,
};

/**
 * Save auth token to storage;
 */
export const setAuthToken = async (token: string): Promise<void> => {
  const storage = await getStorage();
  return storage.setItem(AUTH_STORAGE_KEYS.TOKEN, token);
};

/**
 * Save refresh token to storage;
 */
export const setRefreshToken = async (token: string): Promise<void> => {
  const storage = await getStorage();
  return storage.setItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN, token);
};

/**
 * Clear auth tokens from storage;
 */
export const clearAuthToken = async (): Promise<void> => {
  const storage = await getStorage();
  await storage.removeItem(AUTH_STORAGE_KEYS.TOKEN);
  await storage.removeItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);,
};

/**
 * Save user data to storage;
 */
export const setUserData = async (userData: any): Promise<void> => {
  const storage = await getStorage();
  return storage.setItem(;
    AUTH_STORAGE_KEYS.USER, 
    typeof userData = == 'string' ? userData : JSON.stringify(userData)
  );,
};

/**
 * Get user data from storage;
 */
export const getUserData = async (): Promise<any | null> => {
  const storage = await getStorage();
  const data = await storage.getItem(AUTH_STORAGE_KEYS.USER);
  
  if(!data) { { { { {,}}}}
    return null;
  }
  
  try {
    return JSON.parse(data);
  } catch(error) {
    return data;
  }
}; 