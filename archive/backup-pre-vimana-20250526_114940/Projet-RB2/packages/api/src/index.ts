/**
 * Package API de l'application;
 * 
 * Ce package contient toutes les fonctionnalités nécessaires pour communiquer avec l'API backend:
 * - Client API configuré avec Axios;
 * - Intercepteurs pour gérer l'authentification et les logs;
 * - Services pour les différentes ressources;
 * - Utilitaires pour le stockage des tokens;
 * 
 * @module api;
 */

// Exporter le client par défaut et les utilitaires d'authentification;
export { default, createApiClient, ApiAuth } from './client';

// Exporter les intercepteurs;
export * from './interceptors';

// Exporter les services;
export * from './services';

// Exporter les utilitaires;
export * from './utils'; 