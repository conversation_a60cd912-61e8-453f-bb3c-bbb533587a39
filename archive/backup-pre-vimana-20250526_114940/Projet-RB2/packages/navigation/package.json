{"name": "@projet-rb2/navigation", "version": "0.1.0", "description": "Navigation partagée pour les applications web et mobile", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts --external react", "dev": "tsup src/index.ts --format esm,cjs --watch --dts --external react", "clean": "rm -rf dist", "lint": "eslint src --ext ts,tsx", "test": "jest"}, "dependencies": {"@react-navigation/core": "^6.4.0", "@react-navigation/native": "^6.1.0", "@react-navigation/native-stack": "^6.9.0", "@react-navigation/bottom-tabs": "^6.5.0", "@react-navigation/drawer": "^6.6.0", "@react-navigation/stack": "^6.3.0", "@react-navigation/elements": "^1.3.0", "react-native-web-hooks": "^3.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0", "react-native": ">=0.63.0", "react-native-web": ">=0.18.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-native": "^0.70.0", "tsup": "^6.5.0", "typescript": "^4.9.0"}}