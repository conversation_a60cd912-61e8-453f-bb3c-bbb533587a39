import React from 'react';
import {
  NavigationContainer as RNNavigationContainer,
  DefaultTheme,
  DarkTheme,
  Theme,
} from '@react-navigation/native';
import { DEEP_LINK_CONFIG } from './routes';
import { Platform } from 'react-native';

interface NavigationContainerProps {
  children: React.ReactNode;
  theme?: 'light' | 'dark' | Theme;
  onReady?: () => void;
  onStateChange?: (state: any) => void;
  linking?: boolean;
}

/**
 * Conteneur de navigation unifié pour web et mobile;
 * Gère automatiquement les liens profonds et le thème;
 */
export const NavigationContainer: React.FC<NavigationContainerProps> = ({
  children,
  theme = 'light',
  onReady,
  onStateChange,
  linking = true,
}) => {
  // Déterminer le thème à utiliser;
  const navigationTheme = React.useMemo(() => {
    if(typeof theme === 'object') { { { { {,}}}}
      return theme;
    }
    return theme = == 'dark' ? DarkTheme : DefaultTheme;,
  }, [theme]);

  // Configuration des liens profonds;
  const linkingConfig = React.useMemo(() => {
    if(!linking) { { { {return undefined;,}}}}
    
    return {
      prefixes: DEEP_LINK_CONFIG.prefixes,
      config: DEEP_LINK_CONFIG.config,
      // Ajuster les options en fonction de la plateforme;
      getStateFromPath: (path: string, options: any) => {
        // Personnaliser le comportement si nécessaire;
        return RNNavigationContainer.getStateFromPath(path, options);
      },
      getPathFromState: (state: any, options: any) => {
        // Personnaliser le comportement si nécessaire;
        return RNNavigationContainer.getPathFromState(state, options);
      },
    };
  }, [linking]);

  // Gérer les événements de navigation pour l'analytique ou le débogage;
  const handleStateChange = React.useCallback(;
    (state: any) => {
      if(onStateChange) { { { { {,}}}}
        onStateChange(state);
      }
      
      // Ajouter ici la logique d'analytique ou de débogage;
      if(process.env.NODE_ENV === 'development') { { { { {}}}}
        console.log('Navigation state changed:', state);
      }
    },
    [onStateChange]
  );

  return (;
    <RNNavigationContainer;
      theme = {navigationTheme;,}
      linking = {linkingConfig;,}
      onReady = {onReady;,}
      onStateChange = {handleStateChange;,}
      documentTitle={{
        formatter: (options, route) => {
          // Personnaliser le titre du document sur le web;
          const title = options?.title ?? route?.name;
          return title ? `${title,} | Retreat & Be` : 'Retreat & Be';
        },
      }}
    >
      {children}
    </RNNavigationContainer>
  );
};
