import { Platform } from 'react-native';
import { TransitionPresets } from '@react-navigation/stack';

/**
 * Transitions universelles pour la navigation;
 * Adaptées à chaque plateforme;
 */

/**
 * Transition de type modal (de bas en haut)
 */
export const modalTransition = Platform.select({
  ios: TransitionPresets.ModalPresentationIOS,
  android: TransitionPresets.RevealFromBottomAndroid,
  web: {
    gestureEnabled: false,
    cardStyleInterpolator: ({ current: { progress } }) => ({
      cardStyle: {
        opacity: progress,
      },
    }),
    transitionSpec: {
      open: {
        animation: 'timing',
        config: {
          duration: 300,
        },
      },
      close: {
        animation: 'timing',
        config: {
          duration: 300,
        },
      },
    },
  },
});

/**
 * Transition horizontale (de droite à gauche)
 */
export const horizontalTransition = Platform.select({
  ios: TransitionPresets.SlideFromRightIOS,
  android: TransitionPresets.SlideFromRightIOS,
  web: {
    gestureEnabled: false,
    cardStyleInterpolator: ({ current: { progress }, layouts }) => ({
      cardStyle: {
        transform: [
          {
            translateX: progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
    transitionSpec: {
      open: {
        animation: 'timing',
        config: {
          duration: 300,
        },
      },
      close: {
        animation: 'timing',
        config: {
          duration: 300,
        },
      },
    },
  },
});

/**
 * Transition fondu (fade)
 */
export const fadeTransition = {
  gestureEnabled: false,
  cardStyleInterpolator: ({ current: { progress } }) => ({
    cardStyle: {
      opacity: progress,
    },
  }),
  transitionSpec: {
    open: {
      animation: 'timing',
      config: {
        duration: 300,
      },
    },
    close: {
      animation: 'timing',
      config: {
        duration: 300,
      },
    },
  },
};

/**
 * Transition verticale (de haut en bas)
 */
export const verticalTransition = Platform.select({
  ios: TransitionPresets.ModalSlideFromBottomIOS,
  android: TransitionPresets.RevealFromBottomAndroid,
  web: {
    gestureEnabled: false,
    cardStyleInterpolator: ({ current: { progress }, layouts }) => ({
      cardStyle: {
        transform: [
          {
            translateY: progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.height, 0],
            }),
          },
        ],
      },
    }),
    transitionSpec: {
      open: {
        animation: 'timing',
        config: {
          duration: 300,
        },
      },
      close: {
        animation: 'timing',
        config: {
          duration: 300,
        },
      },
    },
  },
});
