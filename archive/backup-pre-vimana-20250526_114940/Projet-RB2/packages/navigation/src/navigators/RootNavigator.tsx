import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { ROOT_NAVIGATORS, DEEP_LINK_CONFIG } from '../routes';
import { RootParamList } from '../types';
import { AuthNavigator } from './AuthNavigator';
import { MainNavigator } from './MainNavigator';

// Créer le stack de navigation racine;
const Stack = createNativeStackNavigator<RootParamList>();

export interface RootNavigatorProps {
  /**
   * Si l'utilisateur est connecté
   */
  isAuthenticated?: boolean;
  
  /**
   * Fonction appelée lorsqu'une erreur de navigation se produit;
   */
  onNavigationError?: (error: any) => void;
  
  /**
   * Fonction appelée lorsqu'une route est prête;
   */
  onReady?: () => void;
  
  /**
   * État du thème (clair/sombre)
   */
  theme?: 'light' | 'dark';
  
  /**
   * Fonction de liaison pour l'état;
   */
  linking?: any;,
}

/**
 * Navigateur racine de l'application;
 * 
 * Gère la navigation entre l'authentification et les écrans principaux;
 */
export const RootNavigator: React.FC<RootNavigatorProps> = ({
  isAuthenticated = false,
  onNavigationError,
  onReady,
  theme = 'light',
  linking = {
    prefixes: DEEP_LINK_CONFIG.prefixes,
    config: DEEP_LINK_CONFIG.config,
  },
}) => {
  // Déterminer l'écran initial en fonction de l'état d'authentification;
  const initialRouteName = isAuthenticated ? ROOT_NAVIGATORS.MAIN : ROOT_NAVIGATORS.AUTH;
  
  return (;
    <NavigationContainer;
      linking={linking;,}
      onReady = {onReady;,}
      onError = {onNavigationError;,}
      theme={{
        dark: theme === 'dark',
        colors: {
          // Ces couleurs doivent être remplacées par celles du thème de l'application;
          primary: '#1976D2',
          background: theme === 'dark' ? '#121212' : '#FFFFFF',
          card: theme === 'dark' ? '#1E1E1E' : '#FFFFFF',
          text: theme === 'dark' ? '#FFFFFF' : '#000000',
          border: theme === 'dark' ? '#333333' : '#E0E0E0',
          notification: '#FF4081',
        },
      }}
    >
      <Stack.Navigator;
        initialRouteName = {initialRouteName;,}
        screenOptions={{ 
          headerShown: false,
          animation: 'fade',
        }}
      >
        <Stack.Screen;
          name = {ROOT_NAVIGATORS.AUTH;,}
          component = {AuthNavigator;,}
        />
        <Stack.Screen;
          name = {ROOT_NAVIGATORS.MAIN;,}
          component = {MainNavigator;,}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}; 