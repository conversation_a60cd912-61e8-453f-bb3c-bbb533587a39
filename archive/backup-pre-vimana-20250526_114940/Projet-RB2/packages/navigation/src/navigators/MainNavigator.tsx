import React from 'react';
import { Platform } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { MAIN_ROUTES } from '../routes';
import { MainParamList } from '../types';

// Créer les navigateurs;
const Tab = createBottomTabNavigator<MainParamList>();
const Stack = createNativeStackNavigator<MainParamList>();

export interface MainNavigatorProps {
  /**
   * Options de configuration supplémentaires;
   */
  options?: {
    /**
     * Si les animations sont activées;
     */
    animation?: boolean;
    
    /**
     * Si les headers sont affichés;
     */
    showHeaders?: boolean;
    
    /**
     * Type de navigateur à utiliser (tab ou stack)
     */
    navigatorType?: 'tab' | 'stack';,
  };
  
  /**
   * Fonctions de rendu des icônes pour le navigateur à onglets;
   */
  tabBarIcons?: {
    [key in keyof typeof MAIN_ROUTES]?: {
      focused: () => React.ReactNode;
      unfocused: () => React.ReactNode;
    };
  };
}

/**
 * Navigateur principal;
 * 
 * Gère la navigation entre les écrans principaux de l'application;
 */
export const MainNavigator: React.FC<MainNavigatorProps> = ({
  options = {
    animation: true,
    showHeaders: true,
    navigatorType: Platform.OS === 'web' ? 'stack' : 'tab',
  },
  tabBarIcons,
}) => {
  // Utilisation d'un Navigateur à Onglets sur mobile par défaut;
  if(options.navigatorType === 'tab') { { { { {}}}}
    return (;
      <Tab.Navigator;
        screenOptions = {({ route, }) => ({
          headerShown: options.showHeaders,
          tabBarIcon: ({ focused }) => {
            const routeName = route.name as keyof typeof MAIN_ROUTES;
            if(tabBarIcons && tabBarIcons[routeName]) { { { { {,}}}}
              return focused;
                ? tabBarIcons[routeName]?.focused() 
                : tabBarIcons[routeName]?.unfocused();
            }
            // Icône par défaut si aucune n'est fournie;
            return null;
          },
        })}
      >
        <Tab.Screen;
          name = {MAIN_ROUTES.HOME;,}
          getComponent = {() => 
            Platform.OS === 'web'
              ? require('@projet-rb2/web/src/screens/Main/Home').default;
              : require('@projet-rb2/mobile/src/screens/Main/Home').default;,
          }
          options = {{ title: 'Accueil', }}
        />
        
        <Tab.Screen;
          name = {MAIN_ROUTES.BOOKINGS;,}
          getComponent = {() => 
            Platform.OS === 'web'
              ? require('@projet-rb2/web/src/screens/Main/Bookings').default;
              : require('@projet-rb2/mobile/src/screens/Main/Bookings').default;,
          }
          options = {{ title: 'Réservations', }}
        />
        
        <Tab.Screen;
          name = {MAIN_ROUTES.NOTIFICATIONS;,}
          getComponent = {() => 
            Platform.OS === 'web'
              ? require('@projet-rb2/web/src/screens/Main/Notifications').default;
              : require('@projet-rb2/mobile/src/screens/Main/Notifications').default;,
          }
          options = {{ title: 'Notifications', }}
        />
        
        <Tab.Screen;
          name = {MAIN_ROUTES.PROFILE;,}
          getComponent = {() => 
            Platform.OS === 'web'
              ? require('@projet-rb2/web/src/screens/Main/Profile').default;
              : require('@projet-rb2/mobile/src/screens/Main/Profile').default;,
          }
          options = {{ title: 'Profil', }}
        />
      </Tab.Navigator>
    );
  }
  
  // Utilisation d'un Stack Navigator (par défaut sur web)
  return (;
    <Stack.Navigator;
      initialRouteName = {MAIN_ROUTES.HOME;,}
      screenOptions={{
        headerShown: options.showHeaders,
        headerBackTitleVisible: false,
        animation: Platform.OS === 'web' ? 'fade' : (options.animation ? 'default' : 'none'),
      }}
    >
      <Stack.Screen;
        name = {MAIN_ROUTES.HOME;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Main/Home').default;
            : require('@projet-rb2/mobile/src/screens/Main/Home').default;,
        }
        options = {{ title: 'Accueil', }}
      />
      
      <Stack.Screen;
        name = {MAIN_ROUTES.PROFILE;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Main/Profile').default;
            : require('@projet-rb2/mobile/src/screens/Main/Profile').default;,
        }
        options = {{ title: 'Profil', }}
      />
      
      <Stack.Screen;
        name = {MAIN_ROUTES.SETTINGS;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Main/Settings').default;
            : require('@projet-rb2/mobile/src/screens/Main/Settings').default;,
        }
        options = {{ title: 'Paramètres', }}
      />
      
      <Stack.Screen;
        name = {MAIN_ROUTES.NOTIFICATIONS;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Main/Notifications').default;
            : require('@projet-rb2/mobile/src/screens/Main/Notifications').default;,
        }
        options = {{ title: 'Notifications', }}
      />
      
      <Stack.Screen;
        name = {MAIN_ROUTES.BOOKINGS;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Main/Bookings').default;
            : require('@projet-rb2/mobile/src/screens/Main/Bookings').default;,
        }
        options = {{ title: 'Réservations', }}
      />
      
      <Stack.Screen;
        name = {MAIN_ROUTES.BOOKING_DETAILS;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Main/BookingDetails').default;
            : require('@projet-rb2/mobile/src/screens/Main/BookingDetails').default;,
        }
        options = {{ title: 'Détails de la réservation', }}
      />
      
      <Stack.Screen;
        name = {MAIN_ROUTES.RETREAT_DETAILS;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Main/RetreatDetails').default;
            : require('@projet-rb2/mobile/src/screens/Main/RetreatDetails').default;,
        }
        options = {{ title: 'Détails du séjour', }}
      />
      
      <Stack.Screen;
        name = {MAIN_ROUTES.USER_DETAILS;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Main/UserDetails').default;
            : require('@projet-rb2/mobile/src/screens/Main/UserDetails').default;,
        }
        options = {{ title: 'Profil utilisateur', }}
      />
      
      <Stack.Screen;
        name = {MAIN_ROUTES.CHAT;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Main/Chat').default;
            : require('@projet-rb2/mobile/src/screens/Main/Chat').default;,
        }
        options = {{ title: 'Messagerie', }}
      />
    </Stack.Navigator>
  );
}; 