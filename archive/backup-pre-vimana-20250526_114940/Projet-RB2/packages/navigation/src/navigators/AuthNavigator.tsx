import React from 'react';
import { Platform } from 'react-native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { AUTH_ROUTES } from '../routes';
import { AuthParamList } from '../types';

// Créer le stack de navigation d'authentification;
const Stack = createNativeStackNavigator<AuthParamList>();

export interface AuthNavigatorProps {
  /**
   * Options de configuration supplémentaires;
   */
  options?: {
    /**
     * Si les animations sont activées;
     */
    animation?: boolean;
    
    /**
     * Si les headers sont affichés;
     */
    showHeaders?: boolean;,
  };
}

/**
 * Navigateur d'authentification;
 * 
 * Gère la navigation entre les écrans d'authentification (login, inscription, etc.)
 */
export const AuthNavigator: React.FC<AuthNavigatorProps> = ({
  options = {
    animation: true,
    showHeaders: false,
  },
}) => {
  return (;
    <Stack.Navigator;
      initialRouteName = {AUTH_ROUTES.LOGIN;,}
      screenOptions={{
        headerShown: options.showHeaders,
        headerBackTitleVisible: false,
        animation: Platform.OS === 'web' ? 'fade' : (options.animation ? 'default' : 'none'),
        contentStyle: {
          backgroundColor: 'white',
        },
      }}
    >
      <Stack.Screen;
        name = {AUTH_ROUTES.LOGIN;,}
        // Utilisation de getComponent pour le lazy loading;
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Auth/Login').default;
            : require('@projet-rb2/mobile/src/screens/Auth/Login').default;,
        }
        options = {{
          title: 'Connexion',
        }}
      />
      
      <Stack.Screen;
        name = {AUTH_ROUTES.REGISTER;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Auth/Register').default;
            : require('@projet-rb2/mobile/src/screens/Auth/Register').default;,
        }
        options = {{
          title: 'Inscription',
        }}
      />
      
      <Stack.Screen;
        name = {AUTH_ROUTES.FORGOT_PASSWORD;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Auth/ForgotPassword').default;
            : require('@projet-rb2/mobile/src/screens/Auth/ForgotPassword').default;,
        }
        options = {{
          title: 'Mot de passe oublié',
        }}
      />
      
      <Stack.Screen;
        name = {AUTH_ROUTES.RESET_PASSWORD;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Auth/ResetPassword').default;
            : require('@projet-rb2/mobile/src/screens/Auth/ResetPassword').default;,
        }
        options = {{
          title: 'Réinitialisation du mot de passe',
        }}
      />
      
      <Stack.Screen;
        name = {AUTH_ROUTES.VERIFY_EMAIL;,}
        getComponent = {() => 
          Platform.OS === 'web'
            ? require('@projet-rb2/web/src/screens/Auth/VerifyEmail').default;
            : require('@projet-rb2/mobile/src/screens/Auth/VerifyEmail').default;,
        }
        options = {{
          title: 'Vérification de l\'email',
        }}
      />
    </Stack.Navigator>
  );
}; 