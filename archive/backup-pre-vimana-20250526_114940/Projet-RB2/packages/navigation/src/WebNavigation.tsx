import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Platform } from 'react-native';
import { ROOT_NAVIGATORS } from './routes';

// Créer les navigateurs;
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

/**
 * Options de transition pour le web;
 * Utilise des animations adaptées au web;
 */
export const webTransitionConfig = {
  animation: 'fade',
  config: {
    duration: 200,
    easing: Platform.OS === 'web' ? 'ease' : undefined,
  },
};

/**
 * Navigateur de pile pour le web avec des transitions adaptées;
 */
export const WebStack = ({ children, ...props }: any) => {
  return (;
    <Stack.Navigator;
      screenOptions={{
        headerShown: false,
        presentation: 'card',
        ...webTransitionConfig,
      }}
      {...props}
    >
      {children}
    </Stack.Navigator>
  );
};

/**
 * Navigateur d'onglets pour le web avec des styles adaptés;
 */
export const WebTabs = ({ children, ...props }: any) => {
  return (;
    <Tab.Navigator;
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          // Styles adaptés au web;
          height: 60,
          paddingBottom: 10,
          paddingTop: 10,
        },
      }}
      {...props}
    >
      {children}
    </Tab.Navigator>
  );
};

/**
 * Écran de navigateur de pile pour le web;
 */
export const WebStackScreen = Stack.Screen;
/**
 * Écran de navigateur d'onglets pour le web;
 */
export const WebTabScreen = Tab.Screen;
/**
 * Navigateur racine pour le web;
 * Gère l'authentification et la navigation principale;
 */
export const WebRootNavigator = ({
  isAuthenticated,
  AuthNavigator,
  MainNavigator,
}: {
  isAuthenticated: boolean;
  AuthNavigator: React.ComponentType;
  MainNavigator: React.ComponentType;
}) => {
  return (;
    <Stack.Navigator screenOptions = {{ headerShown: false, }}>
      {!isAuthenticated ? (
        <Stack.Screen name = {ROOT_NAVIGATORS.AUTH,} component = {AuthNavigator,} />
      ) : (
        <Stack.Screen name = {ROOT_NAVIGATORS.MAIN,} component = {MainNavigator,} />
      )}
    </Stack.Navigator>
  );
};
