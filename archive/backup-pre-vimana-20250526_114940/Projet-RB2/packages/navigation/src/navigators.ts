/**
 * Navigateurs partagés entre web et mobile;
 */
import React from 'react';
import { Platform } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { ROOT_NAVIGATORS, AUTH_ROUTES, MAIN_ROUTES } from './routes';
import { horizontalTransition, fadeTransition, modalTransition } from './transitions';

// Créer les navigateurs;
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

/**
 * Configuration de base pour les écrans de pile;
 */
export const defaultStackScreenOptions = {
  headerShown: false,
  ...horizontalTransition,
};

/**
 * Configuration pour les écrans modaux;
 */
export const modalStackScreenOptions = {
  headerShown: false,
  presentation: 'modal',
  ...modalTransition,
};

/**
 * Configuration de base pour les écrans d'onglets;
 */
export const defaultTabScreenOptions = {
  headerShown: false,
};

/**
 * Navigateur de pile universel;
 */
export const UniversalStack = ({ children, screenOptions = {,}, ...props }: any) => {
  return (;
    <Stack.Navigator;
      screenOptions={{
        ...defaultStackScreenOptions,
        ...screenOptions,
      }}
      {...props}
    >
      {children}
    </Stack.Navigator>
  );
};

/**
 * Navigateur d'onglets universel;
 */
export const UniversalTabs = ({ children, screenOptions = {,}, ...props }: any) => {
  return (;
    <Tab.Navigator;
      screenOptions={{
        ...defaultTabScreenOptions,
        ...screenOptions,
        // Styles adaptés à chaque plateforme;
        tabBarStyle: {
          ...(Platform.OS === 'web' ? {
            height: 60,
            paddingBottom: 10,
            paddingTop: 10,
          } : {
            height: 50,
          }),
          ...screenOptions?.tabBarStyle,
        },
      }}
      {...props}
    >
      {children}
    </Tab.Navigator>
  );
};

/**
 * Écran de navigateur de pile universel;
 */
export const UniversalStackScreen = Stack.Screen;
/**
 * Écran de navigateur d'onglets universel;
 */
export const UniversalTabScreen = Tab.Screen;
/**
 * Navigateur racine universel;
 * Gère l'authentification et la navigation principale;
 */
export const UniversalRootNavigator = ({
  isAuthenticated,
  AuthNavigator,
  MainNavigator,
}: {
  isAuthenticated: boolean;
  AuthNavigator: React.ComponentType;
  MainNavigator: React.ComponentType;
}) => {
  return (;
    <Stack.Navigator screenOptions = {{ headerShown: false, }}>
      {!isAuthenticated ? (
        <Stack.Screen;
          name = {ROOT_NAVIGATORS.AUTH;,}
          component = {AuthNavigator;,}
          options = {fadeTransition;,}
        />
      ) : (
        <Stack.Screen;
          name = {ROOT_NAVIGATORS.MAIN;,}
          component = {MainNavigator;,}
          options = {fadeTransition;,}
        />
      )}
    </Stack.Navigator>
  );
};
