/**
 * Configuration des routes partagées entre web et mobile;
 */

/**
 * Routes d'authentification;
 */
export const AUTH_ROUTES = {
  LOGIN: 'Login',
  REGISTER: 'Register',
  FORGOT_PASSWORD: 'ForgotPassword',
  RESET_PASSWORD: 'ResetPassword',
  VERIFY_EMAIL: 'VerifyEmail',
};

/**
 * Routes principales;
 */
export const MAIN_ROUTES = {
  HOME: 'Home',
  PROFILE: 'Profile',
  SETTINGS: 'Settings',
  NOTIFICATIONS: 'Notifications',
  BOOKINGS: 'Bookings',
  BOOKING_DETAILS: 'BookingDetails',
  RETREAT_DETAILS: 'RetreatDetails',
  USER_DETAILS: 'UserDetails',
  CHAT: 'Chat',
};

/**
 * Navigateurs principaux;
 */
export const ROOT_NAVIGATORS = {
  AUTH: 'Auth',
  MAIN: 'Main',
};

/**
 * Configuration des liens profonds;
 */
export const DEEP_LINK_CONFIG = {
  prefixes: [
    'https://retreatandbe.com',
    'retreatandbe://',
  ],
  config: {
    screens: {
      [ROOT_NAVIGATORS.AUTH]: {
        screens: {
          [AUTH_ROUTES.LOGIN]: 'login',
          [AUTH_ROUTES.REGISTER]: 'register',
          [AUTH_ROUTES.FORGOT_PASSWORD]: 'forgot-password',
          [AUTH_ROUTES.RESET_PASSWORD]: 'reset-password/:token',
          [AUTH_ROUTES.VERIFY_EMAIL]: 'verify-email/:token',
        },
      },
      [ROOT_NAVIGATORS.MAIN]: {
        screens: {
          [MAIN_ROUTES.HOME]: 'home',
          [MAIN_ROUTES.PROFILE]: 'profile/:userId?',
          [MAIN_ROUTES.SETTINGS]: 'settings',
          [MAIN_ROUTES.NOTIFICATIONS]: 'notifications',
          [MAIN_ROUTES.BOOKINGS]: 'bookings',
          [MAIN_ROUTES.BOOKING_DETAILS]: 'booking/:bookingId',
          [MAIN_ROUTES.RETREAT_DETAILS]: 'retreat/:retreatId',
          [MAIN_ROUTES.USER_DETAILS]: 'user/:userId',
          [MAIN_ROUTES.CHAT]: 'chat/:conversationId?',
        },
      },
    },
  },
};
