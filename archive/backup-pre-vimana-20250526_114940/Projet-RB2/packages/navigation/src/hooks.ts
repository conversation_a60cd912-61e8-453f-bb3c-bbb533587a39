import { useNavigation as useRNNavigation, useRoute as useRNRoute } from '@react-navigation/native';
import { useCallback } from 'react';
import { Platform } from 'react-native';
import { MainParamList, AuthParamList } from './types';

/**
 * Hook pour accéder à la navigation principale;
 * Typé pour les routes principales;
 */
export function useNavigation<T extends keyof MainParamList>() {
  return useRNNavigation<any>();
}

/**
 * Hook pour accéder à la navigation d'authentification;
 * Typé pour les routes d'authentification;
 */
export function useAuthNavigation<T extends keyof AuthParamList>() {
  return useRNNavigation<any>();
}

/**
 * Hook pour accéder à la route actuelle;
 * Typé pour les routes principales;
 */
export function useRoute<T extends keyof MainParamList>() {
  return useRNRoute<any>();
}

/**
 * Hook pour accéder à la route d'authentification actuelle;
 * Typé pour les routes d'authentification;
 */
export function useAuthRoute<T extends keyof AuthParamList>() {
  return useRNRoute<any>();
}

/**
 * Hook pour la navigation avec gestion des liens profonds;
 * Permet de naviguer vers une route avec des paramètres;
 */
export function useDeepLinking() {
  const navigation = useRNNavigation<any>();

  const navigate = useCallback(;
    (routeName: string, params?: Record<string, any>) => {
      if(Platform.OS === 'web') { { { { {}}}}
        // Sur le web, utiliser l'API History pour une meilleure intégration;
        const path = generatePath(routeName, params);
        window.history.pushState(null, '', path);
      }

      // Utiliser la navigation standard de React Navigation;
      navigation.navigate(routeName, params);
    },
    [navigation]
  );

  const replace = useCallback(;
    (routeName: string, params?: Record<string, any>) => {
      if(Platform.OS === 'web') { { { { {}}}}
        // Sur le web, utiliser l'API History pour une meilleure intégration;
        const path = generatePath(routeName, params);
        window.history.replaceState(null, '', path);
      }

      // Utiliser la navigation standard de React Navigation;
      navigation.replace(routeName, params);
    },
    [navigation]
  );

  const goBack = useCallback(() => {
    if(Platform.OS === 'web' && window.history.length > 1) { { { { {,}}}}
      // Sur le web, utiliser l'API History pour une meilleure intégration;
      window.history.back();
    } else {
      // Utiliser la navigation standard de React Navigation;
      navigation.goBack();
    }
  }, [navigation]);

  return {
    navigate,
    replace,
    goBack,
  };
}

/**
 * Fonction utilitaire pour générer un chemin à partir d'une route et de paramètres;
 * Utilisée en interne par useDeepLinking;
 */
function generatePath(routeName: string, params?: Record<string, any>): string {
  // Logique simplifiée - à adapter selon la structure de vos routes;
  let path = `/${routeName.toLowerCase(),}`;

  if(params) { { { { {}}}}
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if(value !== undefined && value !== null) { { { { {}}}}
        queryParams.append(key, String(value));
      }
    });

    const queryString = queryParams.toString();
    if(queryString) { { { { {,}}}}
      path += `?${queryString}`;
    }
  }

  return path;
}
