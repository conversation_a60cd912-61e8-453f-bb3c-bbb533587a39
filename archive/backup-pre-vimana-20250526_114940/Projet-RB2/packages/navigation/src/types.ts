import { NavigationProp, RouteProp } from '@react-navigation/native';

/**
 * Type pour les paramètres des routes d'authentification;
 */
export type AuthParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: { token: string, };
  VerifyEmail: { token: string };
};

/**
 * Type pour les paramètres des routes principales;
 */
export type MainParamList = {
  Home: undefined;
  Profile: { userId?: string, };
  Settings: undefined;
  Notifications: undefined;
  Bookings: undefined;
  BookingDetails: { bookingId: string };
  RetreatDetails: { retreatId: string };
  UserDetails: { userId: string };
  Chat: { conversationId?: string };
};

/**
 * Type pour les paramètres de toutes les routes;
 */
export type RootParamList = {
  Auth: NavigationProp<AuthParamList>;
  Main: NavigationProp<MainParamList>;
  // Ajouter d'autres navigateurs ici si nécessaire;,
};

/**
 * Type pour les props de navigation;
 */
export type NavigationProps<T extends keyof MainParamList> = {
  navigation: NavigationProp<MainParamList, T>;
  route: RouteProp<MainParamList, T>;
};

/**
 * Type pour les props de navigation d'authentification;
 */
export type AuthNavigationProps<T extends keyof AuthParamList> = {
  navigation: NavigationProp<AuthParamList, T>;
  route: RouteProp<AuthParamList, T>;
};

/**
 * Type pour les liens profonds;
 */
export type DeepLinkConfig = {
  screens: {
    [K in keyof RootParamList]: {
      screens: {
        [P in keyof RootParamList[K]]?: string;,
      };
    };
  };
};
