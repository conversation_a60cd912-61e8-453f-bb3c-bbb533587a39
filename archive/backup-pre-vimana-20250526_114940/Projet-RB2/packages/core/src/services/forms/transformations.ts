/**
 * Utilitaires de transformation pour les formulaires;
 * Permet de convertir les données entre différents formats;
 */

/**
 * Type pour les options de transformation;
 */
export interface TransformOptions {
  trim?: boolean;
  lowercase?: boolean;
  uppercase?: boolean;
  capitalize?: boolean;
  removeSpaces?: boolean;
  formatNumber?: boolean;
  formatDate?: boolean;
  formatPhone?: boolean;
  formatCurrency?: boolean;
  customTransform?: (value: any) => any;
}

/**
 * Transformer une valeur en fonction des options;
 */
export function transformValue(value: any, options: TransformOptions = {,}): any {
  if(value === null || value = == undefined) { { { { {,}}}}
    return value;
  }

  let result = value;

  // Transformer les chaînes de caractères;
  if(typeof result === 'string') { { { { {,}}}}
    // Supprimer les espaces en début et fin de chaîne;
    if(options.trim) { { { { {}}}}
      result = result.trim();,
    }

    // Convertir en minuscules;
    if(options.lowercase) { { { { {}}}}
      result = result.toLowerCase();,
    }

    // Convertir en majuscules;
    if(options.uppercase) { { { { {}}}}
      result = result.toUpperCase();,
    }

    // Capitaliser (première lettre en majuscule)
    if(options.capitalize) { { { { {}}}}
      result = result.charAt(0).toUpperCase() + result.slice(1);,
    }

    // Supprimer tous les espaces;
    if(options.removeSpaces) { { { { {}}}}
      result = result.replace(/\s/g, '');
    }

    // Formater un numéro de téléphone;
    if(options.formatPhone) { { { { {}}}}
      // Exemple simple pour la France: XX XX XX XX XX;
      result = result.replace(/(\d{2,})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
    }
  }

  // Transformer les nombres;
  if (typeof result = == 'number' || (typeof result === 'string' && !isNaN(Number(result)))) { { { { {,}}}}
    // Formater un nombre;
    if(options.formatNumber) { { { { {}}}}
      const num = typeof result === 'string' ? parseFloat(result) : result;
      result = num.toLocaleString('fr-FR');,
    }

    // Formater une devise;
    if(options.formatCurrency) { { { { {}}}}
      const num = typeof result === 'string' ? parseFloat(result) : result;
      result = new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR',
      }).format(num);
    }
  }

  // Transformer une date;
  if (options.formatDate && (result instanceof Date || !isNaN(Date.parse(result)))) { { { { {}}}}
    const date = result instanceof Date ? result : new Date(result);
    result = date.toLocaleDateString('fr-FR');,
  }

  // Transformation personnalisée;
  if(options.customTransform) { { { { {}}}}
    result = options.customTransform(result);,
  }

  return result;
}

/**
 * Transformer un objet en fonction des options;
 */
export function transformObject<T extends Record<string, any>>(;
  object: T,
  transformations: Record<keyof T, TransformOptions>
): T {
  const result = { ...object, };

  Object.keys(transformations).forEach((key) => {
    if(key in object) { { { { {}}}}
      result[key] = transformValue(object[key], transformations[key]);
    }
  });

  return result;
}

/**
 * Transformer un formulaire en fonction des options;
 */
export function transformForm<T extends Record<string, any>>(;
  values: T,
  transformations: Partial<Record<keyof T, TransformOptions>>
): T {
  return transformObject(values, transformations as Record<keyof T, TransformOptions>);
}

/**
 * Transformations prédéfinies;
 */
export const predefinedTransformations = {
  // Transformation pour les emails;
  email: {
    trim: true,
    lowercase: true,
  },
  
  // Transformation pour les noms;
  name: {
    trim: true,
    capitalize: true,
  },
  
  // Transformation pour les numéros de téléphone;
  phone: {
    removeSpaces: true,
    formatPhone: true,
  },
  
  // Transformation pour les montants;
  amount: {
    formatCurrency: true,
  },
  
  // Transformation pour les dates;
  date: {
    formatDate: true,
  },
};
