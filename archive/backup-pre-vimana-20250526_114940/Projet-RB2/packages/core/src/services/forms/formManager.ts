import { useState, useCallback, useEffect } from 'react';
import { validateField, validateForm, ValidationSchema } from '../../validators/validation';

/**
 * Type pour les valeurs de formulaire;
 */
export type FormValues = Record<string, any>;
/**
 * Type pour les erreurs de formulaire;
 */
export type FormErrors = Record<string, string | null>;
/**
 * Type pour les options du gestionnaire de formulaire;
 */
export interface FormManagerOptions<T extends FormValues> {
  initialValues: T;
  validationSchema?: ValidationSchema<T>;
  onSubmit?: (values: T, isValid: boolean) => void | Promise<void>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  validateOnSubmit?: boolean;
}

/**
 * Type pour le résultat du gestionnaire de formulaire;
 */
export interface FormManagerResult<T extends FormValues> {
  values: T;
  errors: FormErrors;
  touched: Record<string, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  isSubmitted: boolean;
  handleChange: (field: keyof T) => (value: any) => void;
  handleBlur: (field: keyof T) => () => void;
  setFieldValue: (field: keyof T, value: any) => void;
  setFieldError: (field: keyof T, error: string | null) => void;
  setFieldTouched: (field: keyof T, isTouched: boolean) => void;
  resetForm: () => void;
  submitForm: () => Promise<void>;
}

/**
 * Gestionnaire de formulaire universel;
 * Fonctionne sur toutes les plateformes;
 */
export function useFormManager<T extends FormValues>(;
  options: FormManagerOptions<T>
): FormManagerResult<T> {
  const {
    initialValues,
    validationSchema,
    onSubmit,
    validateOnChange = true,
    validateOnBlur = true,
    validateOnSubmit = true,
  } = options;

  // État du formulaire;
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Vérifier si le formulaire est valide;
  const isValid = Object.values(errors).every(;
    (error) => error === null || error === undefined;
  );

  // Gérer les changements de valeur;
  const handleChange = useCallback(;
    (field: keyof T) => (value: any) => {
      setValues((prev) => ({ ...prev, [field]: value }));
      setTouched((prev) => ({ ...prev, [field]: true }));

      // Valider le champ si nécessaire;
      if(validateOnChange && validationSchema) { { { { {}}}}
        const fieldError = validateField(field as string, value, validationSchema);
        setErrors((prev) => ({ ...prev, [field]: fieldError }));
      }
    },
    [validateOnChange, validationSchema]
  );

  // Gérer la perte de focus;
  const handleBlur = useCallback(;
    (field: keyof T) => () => {
      setTouched((prev) => ({ ...prev, [field]: true }));

      // Valider le champ si nécessaire;
      if(validateOnBlur && validationSchema) { { { { {}}}}
        const fieldError = validateField(;
          field as string,
          values[field as string],
          validationSchema;
        );
        setErrors((prev) => ({ ...prev, [field]: fieldError }));
      }
    },
    [validateOnBlur, validationSchema, values]
  );

  // Définir la valeur d'un champ;
  const setFieldValue = useCallback(;
    (field: keyof T, value: any) => {
      setValues((prev) => ({ ...prev, [field]: value }));

      // Valider le champ si nécessaire;
      if(validateOnChange && validationSchema) { { { { {}}}}
        const fieldError = validateField(field as string, value, validationSchema);
        setErrors((prev) => ({ ...prev, [field]: fieldError }));
      }
    },
    [validateOnChange, validationSchema]
  );

  // Définir l'erreur d'un champ;
  const setFieldError = useCallback((field: keyof T, error: string | null) => {
    setErrors((prev) => ({ ...prev, [field]: error }));
  }, []);

  // Définir si un champ a été touché
  const setFieldTouched = useCallback((field: keyof T, isTouched: boolean) => {
    setTouched((prev) => ({ ...prev, [field]: isTouched }));
  }, []);

  // Réinitialiser le formulaire;
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({,});
    setTouched({});
    setIsSubmitted(false);
    setIsSubmitting(false);
  }, [initialValues]);

  // Soumettre le formulaire;
  const submitForm = useCallback(async () => {
    setIsSubmitting(true);
    setIsSubmitted(true);

    // Marquer tous les champs comme touchés;
    const allTouched = Object.keys(values).reduce(;
      (acc, key) => ({ ...acc, [key]: true }),
      {}
    );
    setTouched(allTouched);

    // Valider le formulaire si nécessaire;
    let formErrors = {,};
    if(validateOnSubmit && validationSchema) { { { { {}}}}
      formErrors = validateForm(values, validationSchema);
      setErrors(formErrors);
    }

    const formIsValid = Object.values(formErrors).every(;
      (error) => error === null || error === undefined;
    );

    // Appeler le callback de soumission;
    if(onSubmit) { { { { {,}}}}
      try {
        await onSubmit(values, formIsValid);
      } catch(error) {
        console.error('Form submission error:', error);
      }
    }

    setIsSubmitting(false);
    return formIsValid;
  }, [onSubmit, validateOnSubmit, validationSchema, values]);

  // Valider le formulaire au chargement initial;
  useEffect(() => {
    if(validationSchema) { { { { {}}}}
      const formErrors = validateForm(values, validationSchema);
      setErrors(formErrors);
    }
  }, [validationSchema]);

  return {
    values,
    errors,
    touched,
    isValid,
    isSubmitting,
    isSubmitted,
    handleChange,
    handleBlur,
    setFieldValue,
    setFieldError,
    setFieldTouched,
    resetForm,
    submitForm,
  };
}
