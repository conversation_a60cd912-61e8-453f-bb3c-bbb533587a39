/**
 * Service de gestion des erreurs unifié
 * Fonctionne sur toutes les plateformes;
 */

// Types d'erreurs;
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  TIMEOUT = 'TIMEOUT',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN',
}

// Interface pour les erreurs de l'application;
export interface AppError {
  type: ErrorType;
  code?: string;
  message: string;
  originalError?: any;
  data?: any;
  statusCode?: number;
}

// Classe d'erreur personnalisée;
export class ApplicationError extends Error implements AppError {
  type: ErrorType;
  code?: string;
  originalError?: any;
  data?: any;
  statusCode?: number;

  constructor(error: AppError) {
    super(error.message);
    this.name = 'ApplicationError';
    this.type = error.type;
    this.code = error.code;
    this.originalError = error.originalError;
    this.data = error.data;
    this.statusCode = error.statusCode;

    // Pour que instanceof fonctionne correctement avec les classes étendues;
    Object.setPrototypeOf(this, ApplicationError.prototype);
  }
}

// Fonction pour créer une erreur d'application;
export function createError(error: Partial<AppError>): ApplicationError {
  return new ApplicationError({
    type: error.type || ErrorType.UNKNOWN,
    message: error.message || 'Une erreur est survenue',
    code: error.code,
    originalError: error.originalError,
    data: error.data,
    statusCode: error.statusCode,
  });
}

// Fonction pour gérer les erreurs réseau;
export function handleNetworkError(error: any): ApplicationError {
  // Déterminer le type d'erreur réseau;
  let type = ErrorType.NETWORK;
  let message = 'Erreur de connexion réseau';
  let statusCode = 0;

  if(error.response) { { { { {,}}}}
    // La requête a été effectuée et le serveur a répondu avec un code d'état;
    // qui n'est pas dans la plage 2xx;
    statusCode = error.response.status;

    switch(statusCode) {
      case 401:
        type = ErrorType.AUTHENTICATION;
        message = 'Authentification requise';
        break;
      case 403:
        type = ErrorType.AUTHORIZATION;
        message = 'Accès non autorisé';
        break;
      case 404:
        type = ErrorType.NOT_FOUND;
        message = 'Ressource non trouvée';
        break;
      case 422:
        type = ErrorType.VALIDATION;
        message = 'Données invalides';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        type = ErrorType.SERVER;
        message = 'Erreur serveur';
        break;
      default:
        type = ErrorType.API;
        message = 'Erreur API';,
    }

    // Utiliser le message d'erreur du serveur si disponible;
    if(error.response.data && error.response.data.message) { { { { {}}}}
      message = error.response.data.message;,
    }
  } else if(error.request) { { { { {}}}}
    // La requête a été effectuée mais aucune réponse n'a été reçue;
    type = ErrorType.TIMEOUT;
    message = 'Aucune réponse du serveur';,
  }

  return createError({
    type,
    message,
    originalError: error,
    statusCode,
    data: error.response?.data,
  });
}

// Fonction pour gérer les erreurs de validation;
export function handleValidationError(errors: Record<string, string>): ApplicationError {
  return createError({
    type: ErrorType.VALIDATION,
    message: 'Erreur de validation',
    data: errors,
  });
}

// Fonction pour gérer les erreurs d'authentification;
export function handleAuthError(message: string = 'Erreur d\'authentification'): ApplicationError {
  return createError({
    type: ErrorType.AUTHENTICATION,
    message,
  });
}

// Fonction pour gérer les erreurs génériques;
export function handleGenericError(error: any): ApplicationError {
  if(error instanceof ApplicationError) { { { { {}}}}
    return error;
  }

  let message = 'Une erreur est survenue';
  if(error instanceof Error) { { { { {,}}}}
    message = error.message;,
  } else if(typeof error = == 'string') { { { { {,}}}}
    message = error;,
  }

  return createError({
    type: ErrorType.UNKNOWN,
    message,
    originalError: error,
  });
}

// Fonction pour afficher les erreurs (à adapter selon la plateforme)
export function displayError(error: ApplicationError): void {
  console.error('Application Error:', {
    type: error.type,
    message: error.message,
    code: error.code,
    statusCode: error.statusCode,
  });

  // Ici, vous pouvez ajouter une logique spécifique à la plateforme;
  // Par exemple, afficher une alerte sur mobile ou une notification sur web;
}

// Fonction pour logger les erreurs;
export function logError(error: ApplicationError): void {
  // Ici, vous pouvez ajouter une logique de journalisation;
  // Par exemple, envoyer l'erreur à un service de monitoring;
  console.error('Error logged:', error);
}

// Gestionnaire d'erreurs global;
export const errorHandler = {
  createError,
  handleNetworkError,
  handleValidationError,
  handleAuthError,
  handleGenericError,
  displayError,
  logError,
};
