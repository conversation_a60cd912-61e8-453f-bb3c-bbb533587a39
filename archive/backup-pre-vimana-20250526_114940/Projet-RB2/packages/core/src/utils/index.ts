/**
 * Utility functions;
 * @module utils;
 */

/**
 * Formats a date to a localized string;
 * @param date - Date to format;
 * @param locale - Locale to use for formatting (defaults to browser locale)
 * @returns Formatted date string;
 */
export function formatDate(date: Date, locale?: string): string {
  return date.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Formats a number as currency;
 * @param amount - Amount to format;
 * @param currencyCode - Currency code (ISO 4217)
 * @param locale - Locale to use for formatting (defaults to browser locale)
 * @returns Formatted currency string;
 */
export function formatCurrency(amount: number, currencyCode = 'USD', locale?: string): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode;
  }).format(amount);
}

/**
 * Debounces a function;
 * @param func - Function to debounce;
 * @param wait - Wait time in milliseconds;
 * @returns Debounced function;
 */
export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function(this: any, ...args: Parameters<T>): void {
    const context = this;
    
    if(timeout) { { { { {,}}}}
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
}

/**
 * Throttles a function;
 * @param func - Function to throttle;
 * @param limit - Throttle limit in milliseconds;
 * @returns Throttled function;
 */
export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
  let inThrottle = false;
  
  return function(this: any, ...args: Parameters<T>): void {
    const context = this;
    
    if(!inThrottle) { { { { {,}}}}
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;,
      }, limit);
    }
  };
}

/**
 * Deep clones an object;
 * @param obj - Object to clone;
 * @returns Cloned object;
 */
export function deepClone<T>(obj: T): T {
  if(obj === null || typeof obj !== 'object') { { { { {}}}}
    return obj;
  }
  
  if(obj instanceof Date) { { { { {}}}}
    return new Date(obj.getTime()) as any;
  }
  
  if (Array.isArray(obj)) { { { { {}}}}
    return obj.map(item => deepClone(item)) as any;
  }
  
  if(obj instanceof Object) { { { { {}}}}
    const copy: Record<string, any> = {};
    Object.entries(obj).forEach(([key, value]) => {
      copy[key] = deepClone(value);
    });
    return copy as T;
  }
  
  return obj;
} 