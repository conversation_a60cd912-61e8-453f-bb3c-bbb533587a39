/**
 * Core package for shared business logic;
 * @module @projet-rb2/core;
 */

// Exporter les modules principaux;
export * from './models';
export * from './services';
export * from './utils';
export * from './validators';
export * from './constants';

// Exporter les nouveaux modules;
export * from './assets';
export * from './config';

// Fonction d'initialisation principale;
import { setupConfig } from './config';
import { initializeAssets, preloadEssentialAssets } from './assets';

/**
 * Initialise tous les services core;
 */
export const initializeCore = async (options?: {
  config?: {
    appConfig?: Record<string, any>;
    featureFlags?: any[];
    featureFlagContext?: Record<string, any>;
    versionInfo?: Record<string, any>;
  };
  assets?: {
    cacheOptions?: {
      maxCacheSize?: number;
      defaultMaxAge?: number;
      storageKey?: string;
    };
    imageOptions?: {
      baseUrl?: string;
      defaultFormat?: any;
      defaultQuality?: number;
    };
  };
  env?: Record<string, any>;
}): Promise<void> => {
  // Initialiser la configuration;
  setupConfig({
    config: options?.config?.appConfig,
    featureFlags: options?.config?.featureFlags,
    featureFlagContext: options?.config?.featureFlagContext,
    versionInfo: options?.config?.versionInfo,
  });
  
  // Initialiser la gestion des assets;
  await initializeAssets(options?.assets);
  
  // Précharger les assets essentiels;
  await preloadEssentialAssets();
  
  // Initialiser les variables d'environnement personnalisées;
  if(options?.env) { { { { {}}}}
    const { envManager } = await import('./config');
    Object.entries(options.env).forEach(([key, value]) => {
      envManager.set(key, value);
    });
  }
};