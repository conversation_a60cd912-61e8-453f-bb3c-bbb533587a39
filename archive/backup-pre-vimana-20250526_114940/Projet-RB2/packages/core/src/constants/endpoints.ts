/**
 * Constantes pour les URLs des endpoints API;
 */

/**
 * URL de base de l'API;
 * Variable d'environnement: REACT_APP_API_URL ou NEXT_PUBLIC_API_URL;
 */
export const API_BASE_URL = typeof process !== 'undefined' && process.env;
  ? (process.env.REACT_APP_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api')
  : 'http://localhost:3001/api';

/**
 * Endpoints pour l'authentification;
 */
export const AUTH_ENDPOINTS = {
  /**
   * Endpoint de connexion;
   */
  LOGIN: '/auth/login',
  
  /**
   * Endpoint d'inscription;
   */
  REGISTER: '/auth/register',
  
  /**
   * Endpoint de déconnexion;
   */
  LOGOUT: '/auth/logout',
  
  /**
   * Endpoint de demande de réinitialisation de mot de passe;
   */
  FORGOT_PASSWORD: '/auth/forgot-password',
  
  /**
   * Endpoint de réinitialisation de mot de passe;
   */
  RESET_PASSWORD: '/auth/reset-password',
  
  /**
   * Endpoint de rafraîchissement du token;
   */
  REFRESH_TOKEN: '/auth/refresh-token',
};

/**
 * Endpoints pour les utilisateurs;
 */
export const USERS_ENDPOINTS = {
  /**
   * Endpoint pour l'utilisateur actuel;
   */
  ME: '/users/me',
  
  /**
   * Endpoint pour mettre à jour l'avatar;
   */
  AVATAR: '/users/me/avatar',
  
  /**
   * Endpoint pour changer le mot de passe;
   */
  CHANGE_PASSWORD: '/users/me/change-password',
  
  /**
   * Endpoint pour les préférences utilisateur;
   */
  PREFERENCES: '/users/me/preferences',
  
  /**
   * Endpoint pour la liste des utilisateurs (admin)
   */
  LIST: '/users',
  
  /**
   * Endpoint pour un utilisateur spécifique;
   * @param id - ID de l'utilisateur;
   */
  BY_ID: (id: string) => `/users/${id}`,
};

/**
 * Structure complète des endpoints;
 */
export const ENDPOINTS = {
  AUTH: AUTH_ENDPOINTS,
  USERS: USERS_ENDPOINTS,
}; 