/**
 * Application constants;
 * @module constants;
 */

/**
 * API URL based on environment;
 */
export const API_URL = process.env.REACT_APP_API_URL || 'https://api.exemple.com';
/**
 * Authentication storage keys;
 */
export const AUTH_STORAGE_KEYS = {
  TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER: 'user_data',
};

/**
 * Default pagination limits;
 */
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
};

/**
 * Application routes;
 */
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  PRODUCTS: '/products',
  PRODUCT_DETAILS: '/products/:id',
  CART: '/cart',
  CHECKOUT: '/checkout',
  ORDERS: '/orders',
  ORDER_DETAILS: '/orders/:id',
  PROFILE: '/profile',
  ADMIN: '/admin',
};

/**
 * API endpoints;
 */
export const ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH_TOKEN: '/auth/refresh-token',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
  },
  USERS: {
    ME: '/users/me',
    BY_ID: '/users/:id',
    UPDATE: '/users/:id',
  },
  PRODUCTS: {
    LIST: '/products',
    BY_ID: '/products/:id',
    CREATE: '/products',
    UPDATE: '/products/:id',
    DELETE: '/products/:id',
  },
  ORDERS: {
    LIST: '/orders',
    BY_ID: '/orders/:id',
    CREATE: '/orders',
    UPDATE: '/orders/:id',
    CANCEL: '/orders/:id/cancel',
  },
};

/**
 * Theme constants;
 */
export const THEME = {
  COLORS: {
    PRIMARY: '#3f51b5',
    SECONDARY: '#f50057',
    SUCCESS: '#4caf50',
    ERROR: '#f44336',
    WARNING: '#ff9800',
    INFO: '#2196f3',
    LIGHT: '#f5f5f5',
    DARK: '#212121',
  },
  SPACING: {
    XS: '0.25rem',
    SM: '0.5rem',
    MD: '1rem',
    LG: '1.5rem',
    XL: '2rem',
    XXL: '3rem',
  },
  BREAKPOINTS: {
    XS: '0px',
    SM: '600px',
    MD: '960px',
    LG: '1280px',
    XL: '1920px',
  },
};

/**
 * Constantes partagées par toute l'application;
 * 
 * Ce module exporte des constantes communes à tous les packages:
 * - endpoints: URLs de l'API;
 * - auth: Constantes liées à l'authentification;
 * - storage: Clés de stockage;
 */

export * from './endpoints';
export * from './auth';
export * from './common'; 