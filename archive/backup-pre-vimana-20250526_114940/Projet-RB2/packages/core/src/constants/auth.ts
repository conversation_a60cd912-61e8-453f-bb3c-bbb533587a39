/**
 * Constantes liées à l'authentification;
 */

/**
 * Clés de stockage pour les tokens et données d'authentification;
 */
export const AUTH_STORAGE_KEYS = {
  /**
   * C<PERSON> pour le token d'authentification JWT;
   */
  TOKEN: 'auth_token',
  
  /**
   * C<PERSON> pour le token de rafraîchissement;
   */
  REFRESH_TOKEN: 'auth_refresh_token',
  
  /**
   * Clé pour les données utilisateur;
   */
  USER_DATA: 'auth_user_data',
  
  /**
   * C<PERSON> pour stocker l'URL de redirection après connexion;
   */
  REDIRECT_URL: 'auth_redirect',
};

/**
 * Types de rôles utilisateur;
 */
export const USER_ROLES = {
  /**
   * Administrateur avec tous les droits;
   */
  ADMIN: 'admin',
  
  /**
   * Utilisateur standard;
   */
  USER: 'user',
  
  /**
   * Utilisateur en lecture seule;
   */
  VIEWER: 'viewer',
};

/**
 * Routes liées à l'authentification;
 */
export const AUTH_ROUTES = {
  /**
   * Page de connexion;
   */
  LOGIN: '/login',
  
  /**
   * Page d'inscription;
   */
  REGISTER: '/register',
  
  /**
   * Page de mot de passe oublié
   */
  FORGOT_PASSWORD: '/forgot-password',
  
  /**
   * Page de réinitialisation de mot de passe;
   */
  RESET_PASSWORD: '/reset-password',
};

/**
 * Événements d'authentification;
 */
export const AUTH_EVENTS = {
  /**
   * Événement déclenché lors de la connexion;
   */
  LOGIN: 'auth:login',
  
  /**
   * Événement déclenché lors de la déconnexion;
   */
  LOGOUT: 'auth:logout',
  
  /**
   * Événement déclenché lorsque le token est rafraîchi;
   */
  TOKEN_REFRESHED: 'auth:token-refreshed',
  
  /**
   * Événement déclenché lorsque l'authentification échoue (401)
   */
  UNAUTHORIZED: 'auth:unauthorized',
  
  /**
   * Événement déclenché lorsque l'accès est refusé (403)
   */
  FORBIDDEN: 'auth:forbidden',
}; 