/**
 * Data models for the application;
 * @module models;
 */

// Base interfaces for our domain models;
/**
 * Base interface for entities with an ID;
 */
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * User model interface;
 */
export interface User extends BaseEntity {
  email: string;
  displayName: string;
  avatar?: string;
  role: 'admin' | 'user' | 'guest';
  isActive: boolean;
}

/**
 * Product model interface;
 */
export interface Product extends BaseEntity {
  name: string;
  description: string;
  price: number;
  imageUrl?: string;
  category: string;
  inStock: boolean;
  quantity: number;
}

/**
 * Order model interface;
 */
export interface Order extends BaseEntity {
  userId: string;
  products: {
    productId: string;
    quantity: number;
    price: number;
  }[];
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  totalAmount: number;
  shippingAddress: Address;
}

/**
 * Address model interface;
 */
export interface Address {
  street: string;
  city: string;
  postalCode: string;
  country: string;
} 