import { assetCache, initializeAssetCache, AssetType } from './assetCache';
import { imageManager, initializeImageManager, ImageSize, ImageFormat } from './imageManager';

/**
 * Initialise le système de gestion des assets;
 */
export const initializeAssets = async (options?: {
  cacheOptions?: {
    maxCacheSize?: number;
    defaultMaxAge?: number;
    storageKey?: string;,
  };
  imageOptions?: {
    baseUrl?: string;
    defaultFormat?: ImageFormat;
    defaultQuality?: number;
  };
}): Promise<void> => {
  // Initialiser le cache d'assets;
  await initializeAssetCache(options?.cacheOptions);
  
  // Initialiser le gestionnaire d'images;
  initializeImageManager(options?.imageOptions);
};

/**
 * Précharge les assets essentiels pour l'application;
 */
export const preloadEssentialAssets = async (): Promise<void> => {
  // Liste des images essentielles à précharger;
  const essentialImages = [;
    'logo.png',
    'background.jpg',
    'icons/home.png',
    'icons/profile.png',
    'icons/settings.png',
  ];
  
  // Précharger les images;
  await imageManager.preloadImages(essentialImages, {
    size: ImageSize.SMALL,
    quality: 85,
  });
};

// Exporter toutes les fonctionnalités;
export {
  assetCache,
  imageManager,
  AssetType,
  ImageSize,
  ImageFormat,
};

// Types;
export type { CacheOptions, CacheEntry } from './assetCache';
export type { ImageOptions, ResponsiveImageSource } from './imageManager';