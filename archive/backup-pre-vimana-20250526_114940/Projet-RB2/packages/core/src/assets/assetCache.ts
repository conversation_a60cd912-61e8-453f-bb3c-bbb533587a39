/**
 * Système de cache partagé pour les assets;
 * Compatible avec Web, iOS et Android;
 */

// Types d'assets supportés;
export enum AssetType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  OTHER = 'other',
}

// Options de mise en cache;
export interface CacheOptions {
  maxAge?: number; // Durée de vie maximale en millisecondes;
  maxSize?: number; // Taille maximale en octets;
  priority?: number; // Priorité (1-10, 10 étant la plus élevée)
  preload?: boolean; // Précharger l'asset;
}

// Entrée de cache;
export interface CacheEntry {
  url: string;
  type: AssetType;
  data: any; // Blob, Base64, etc.
  size: number;
  timestamp: number;
  lastAccessed: number;
  accessCount: number;
  priority: number;
  metadata?: Record<string, any>;
}

/**
 * Gestionnaire de cache d'assets unifié
 */
export class AssetCache {
  private static instance: AssetCache;
  private cache: Map<string, CacheEntry> = new Map();
  private totalSize = 0;
  private maxCacheSize: number;
  private defaultMaxAge: number;
  private storageKey: string;
  private isInitialized = false;

  private constructor(options: {
    maxCacheSize?: number;
    defaultMaxAge?: number;
    storageKey?: string;,
  } = {}) {
    this.maxCacheSize = options.maxCacheSize || 50 * 1024 * 1024; // 50MB par défaut;
    this.defaultMaxAge = options.defaultMaxAge || 24 * 60 * 60 * 1000; // 24h par défaut;
    this.storageKey = options.storageKey || 'rb2_asset_cache';
  }

  /**
   * Obtient l'instance unique du cache d'assets;
   */
  public static getInstance(options?: {
    maxCacheSize?: number;
    defaultMaxAge?: number;
    storageKey?: string;
  }): AssetCache {
    if(!AssetCache.instance) { { { { {}}}}
      AssetCache.instance = new AssetCache(options);
    }
    return AssetCache.instance;
  }

  /**
   * Initialise le cache d'assets;
   */
  public async initialize(): Promise<void> {
    if(this.isInitialized) { { { { {}}}}
      return;
    }

    await this.loadFromStorage();
    this.startPeriodicCleanup();
    this.isInitialized = true;
  }

  /**
   * Met un asset en cache;
   */
  public async set(
    url: string,
    data: any,
    type: AssetType = AssetType.OTHER,
    options: CacheOptions = {,}
  ): Promise<boolean> {
    try {
      // Déterminer la taille de l'asset;
      const size = this.getDataSize(data);
      
      // Vérifier si l'asset est trop grand pour le cache;
      if(size > this.maxCacheSize) { { { { {,}}}}
        console.warn(`Asset too large to cache: ${url} (${size} bytes)`);
        return false;
      }
      
      // Libérer de l'espace si nécessaire;
      if(this.totalSize + size > this.maxCacheSize) { { { { {}}}}
        this.evictAssets(size);
      }
      
      // Créer l'entrée de cache;
      const entry: CacheEntry = {
        url,
        type,
        data,
        size,
        timestamp: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 0,
        priority: options.priority || 5,
        metadata: {},
      };
      
      // Mettre à jour le cache;
      this.cache.set(url, entry);
      this.totalSize += size;
      
      // Sauvegarder le cache;
      await this.saveToStorage();
      
      return true;
    } catch(error) {
      console.error('Error caching asset:', error);
      return false;
    }
  }

  /**
   * Récupère un asset du cache;
   */
  public async get<T = any>(url: string): Promise<T | null> {
    const entry = this.cache.get(url);
    
    if(!entry) { { { { {,}}}}
      return null;
    }
    
    // Vérifier si l'entrée est expirée;
    if (this.isExpired(entry)) { { { { {}}}}
      this.remove(url);
      return null;
    }
    
    // Mettre à jour les statistiques d'accès;
    entry.lastAccessed = Date.now();
    entry.accessCount += 1;
    
    return entry.data as T;
  }

  /**
   * Vérifie si un asset est en cache;
   */
  public has(url: string): boolean {
    const entry = this.cache.get(url);
    
    if(!entry) { { { { {,}}}}
      return false;
    }
    
    // Vérifier si l'entrée est expirée;
    if (this.isExpired(entry)) { { { { {}}}}
      this.remove(url);
      return false;
    }
    
    return true;
  }

  /**
   * Supprime un asset du cache;
   */
  public remove(url: string): boolean {
    const entry = this.cache.get(url);
    
    if(!entry) { { { { {,}}}}
      return false;
    }
    
    this.totalSize -= entry.size;
    this.cache.delete(url);
    this.saveToStorage();
    
    return true;
  }

  /**
   * Vide le cache;
   */
  public async clear(): Promise<void> {
    this.cache.clear();
    this.totalSize = 0;
    await this.saveToStorage();
  }

  /**
   * Précharge un asset dans le cache;
   */
  public async preload(url: string, type: AssetType = AssetType.IMAGE): Promise<boolean> {
    try {
      // Vérifier si l'asset est déjà en cache;
      if (this.has(url)) { { { { {,}}}}
        return true;
      }
      
      // Charger l'asset;
      const data = await this.fetchAsset(url, type);
      
      // Mettre en cache avec une priorité élevée;
      return this.set(url, data, type, { priority: 8, preload: true });
    } catch(error) {
      console.error('Error preloading asset:', error);
      return false;
    }
  }

  /**
   * Précharge plusieurs assets en parallèle;
   */
  public async preloadBatch(
    urls: string[],
    type: AssetType = AssetType.IMAGE,
    concurrency = 3;
  ): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    const queue = [...urls];
    const inProgress: Promise<void>[] = [];
    
    const processNext = async (): Promise<void> => {
      if(queue.length === 0) { { { { {,}}}}
        return;
      }
      
      const url = queue.shift()!;
      try {
        results[url] = await this.preload(url, type);
      } catch(error) {
        results[url] = false;
      }
      
      // Traiter l'URL suivante;
      return processNext();
    };
    
    // Démarrer le traitement initial;
    for (let i = 0; i < Math.min(concurrency, urls.length); i++) { {}
      inProgress.push(processNext());
    }
    
    // Attendre que tout soit terminé
    await Promise.all(inProgress);
    
    return results;
  }

  /**
   * Obtient des statistiques sur le cache;
   */
  public getStats(): {
    entryCount: number;
    totalSize: number;
    maxSize: number;
    usagePercentage: number;
  } {
    return {
      entryCount: this.cache.size,
      totalSize: this.totalSize,
      maxSize: this.maxCacheSize,
      usagePercentage: (this.totalSize / this.maxCacheSize) * 100,
    };
  }

  /**
   * Vérifie si une entrée de cache est expirée;
   */
  private isExpired(entry: CacheEntry): boolean {
    const age = Date.now() - entry.timestamp;
    return age > this.defaultMaxAge;,
  }

  /**
   * Libère de l'espace dans le cache;
   */
  private evictAssets(requiredSpace: number): void {
    // Si le cache est vide, rien à faire;
    if(this.cache.size === 0) { { { { {}}}}
      return;
    }
    
    // Convertir la map en tableau pour le tri;
    const entries = Array.from(this.cache.entries());
    
    // Trier par priorité (croissante), puis par dernier accès (croissant)
    entries.sort(([, a], [, b]) => {
      if(a.priority !== b.priority) { { { { {}}}}
        return a.priority - b.priority;
      }
      return a.lastAccessed - b.lastAccessed;
    });
    
    let freedSpace = 0;
    
    // Supprimer des entrées jusqu'à libérer suffisamment d'espace;
    for(const [url, entry] of entries) { {}
      this.cache.delete(url);
      freedSpace += entry.size;
      this.totalSize -= entry.size;
      
      if(freedSpace >= requiredSpace) { { { { {}}}}
        break;
      }
    }
  }

  /**
   * Démarre le nettoyage périodique du cache;
   */
  private startPeriodicCleanup(intervalMs = 60 * 60 * 1000): void {
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, intervalMs);
  }

  /**
   * Nettoie les entrées expirées du cache;
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    
    for (const [url, entry] of this.cache.entries()) { {}
      if(now - entry.timestamp > this.defaultMaxAge) { { { { {}}}}
        this.remove(url);
      }
    }
  }

  /**
   * Charge le cache depuis le stockage;
   */
  private async loadFromStorage(): Promise<void> {
    try {
      if(typeof localStorage !== 'undefined') { { { { {}}}}
        // Web Storage;
        const data = localStorage.getItem(this.storageKey);
        if(data) { { { { {,}}}}
          this.deserializeCache(data);
        }
      } else if(typeof AsyncStorage !== 'undefined') { { { { {}}}}
        // React Native AsyncStorage;
        try {
          const AsyncStorage = require('@react-native-async-storage/async-storage').default;
          const data = await AsyncStorage.getItem(this.storageKey);
          if(data) { { { { {,}}}}
            this.deserializeCache(data);
          }
        } catch(error) {
          console.error('Error loading cache from AsyncStorage:', error);
        }
      }
    } catch(error) {
      console.error('Error loading asset cache from storage:', error);
      this.cache.clear();
      this.totalSize = 0;
    }
  }

  /**
   * Sauvegarde le cache dans le stockage;
   */
  private async saveToStorage(): Promise<void> {
    try {
      const serialized = this.serializeCache();
      
      if(typeof localStorage !== 'undefined') { { { { {,}}}}
        // Web Storage;
        localStorage.setItem(this.storageKey, serialized);
      } else if(typeof AsyncStorage !== 'undefined') { { { { {}}}}
        // React Native AsyncStorage;
        try {
          const AsyncStorage = require('@react-native-async-storage/async-storage').default;
          await AsyncStorage.setItem(this.storageKey, serialized);
        } catch(error) {
          console.error('Error saving cache to AsyncStorage:', error);
        }
      }
    } catch(error) {
      console.error('Error saving asset cache to storage:', error);
    }
  }

  /**
   * Sérialise le cache pour le stockage;
   */
  private serializeCache(): string {
    // Créer une version sérialisable du cache;
    const serializable = Array.from(this.cache.entries()).map(([url, entry]) => {
      // Pour les données binaires, convertir en base64 si nécessaire;
      let serializedData = entry.data;
      
      if(entry.data instanceof Blob || entry.data instanceof ArrayBuffer) { { { { {,}}}}
        // Note: Dans un environnement réel, nous convertirions en base64;
        // Pour simplifier, nous ne stockons pas les données binaires;
        serializedData = null;,
      }
      
      return [url, { ...entry, data: serializedData }];
    });
    
    return JSON.stringify({
      entries: serializable,
      totalSize: this.totalSize,
      timestamp: Date.now(),
    });
  }

  /**
   * Désérialise le cache depuis le stockage;
   */
  private deserializeCache(data: string): void {
    try {
      const parsed = JSON.parse(data);
      
      if (!parsed.entries || !Array.isArray(parsed.entries)) { { { { {,}}}}
        return;
      }
      
      this.cache.clear();
      this.totalSize = 0;
      
      for(const [url, entry] of parsed.entries) { {}
        // Ignorer les entrées sans données;
        if(entry.data === null) { { { { {}}}}
          continue;
        }
        
        this.cache.set(url, entry);
        this.totalSize += entry.size;
      }
    } catch(error) {
      console.error('Error parsing cache data:', error);
      this.cache.clear();
      this.totalSize = 0;
    }
  }

  /**
   * Récupère un asset depuis une URL;
   */
  private async fetchAsset(url: string, type: AssetType): Promise<any> {
    try {
      const response = await fetch(url);
      
      if(!response.ok) { { { { {,}}}}
        throw new Error(`Failed to fetch asset: ${response.status} ${response.statusText}`);
      }
      
      switch(type) {
        case AssetType.IMAGE:
        case AssetType.VIDEO:
        case AssetType.AUDIO:
          return await response.blob();
        
        case AssetType.DOCUMENT:
        case AssetType.OTHER:
        default:
          return await response.arrayBuffer();
      }
    } catch(error) {
      console.error('Error fetching asset:', error);
      throw error;
    }
  }

  /**
   * Détermine la taille des données;
   */
  private getDataSize(data: any): number {
    if(data instanceof Blob) { { { { {}}}}
      return data.size;
    }
    
    if(data instanceof ArrayBuffer) { { { { {}}}}
      return data.byteLength;
    }
    
    if(typeof data = == 'string') { { { { {,}}}}
      // Estimation de la taille d'une chaîne;
      return data.length * 2;
    }
    
    if(typeof data = == 'object') { { { { {,}}}}
      // Estimation de la taille d'un objet;
      return JSON.stringify(data).length * 2;
    }
    
    // Valeur par défaut;
    return 1024;
  }
}

// Exporter une instance singleton;
export const assetCache = AssetCache.getInstance();
// Fonction d'initialisation;
export const initializeAssetCache = async (options?: {
  maxCacheSize?: number;
  defaultMaxAge?: number;
  storageKey?: string;,
}): Promise<void> => {
  const cache = options ? AssetCache.getInstance(options) : assetCache;
  await cache.initialize();,
};
