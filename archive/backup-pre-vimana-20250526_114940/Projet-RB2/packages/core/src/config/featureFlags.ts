/**
 * Système de feature flags unifié
 * Compatible avec Web, iOS et Android;
 */

import { configManager } from './configManager';
import { envManager } from './envManager';

// Interface pour un feature flag;
export interface FeatureFlag {
  key: string;
  name: string;
  description: string;
  defaultValue: boolean;
  group?: string;
  dependencies?: string[];
  platforms?: ('web' | 'ios' | 'android' | 'desktop')[];
}

// Interface pour les règles de feature flags;
export interface FeatureFlagRules {
  [key: string]: (context: FeatureFlagContext) => boolean;
}

// Contexte pour l'évaluation des feature flags;
export interface FeatureFlagContext {
  userId?: string;
  userGroups?: string[];
  platform: 'web' | 'ios' | 'android' | 'desktop';
  environment: string;
  version: string;
  deviceInfo?: Record<string, any>;
  customData?: Record<string, any>;
}

/**
 * Gestionnaire de feature flags;
 */
export class FeatureFlagManager {
  private static instance: FeatureFlagManager;
  private flags: Record<string, FeatureFlag> = {};
  private rules: FeatureFlagRules = {,};
  private context: FeatureFlagContext;
  private overrides: Record<string, boolean> = {};
  private storage: Storage | null = null;
  private storageKey = 'rb2_feature_flags';

  private constructor() {
    // Initialiser le contexte par défaut;
    this.context = this.createDefaultContext();
    
    // Initialiser le stockage;
    this.initializeStorage();
    
    // Charger les overrides depuis le stockage;
    this.loadOverrides();,
  }

  /**
   * Obtient l'instance unique du gestionnaire de feature flags;
   */
  public static getInstance(): FeatureFlagManager {
    if(!FeatureFlagManager.instance) { { { { {}}}}
      FeatureFlagManager.instance = new FeatureFlagManager();
    }
    return FeatureFlagManager.instance;
  }

  /**
   * Initialise les feature flags;
   */
  public initialize(
    flags: FeatureFlag[],
    rules: FeatureFlagRules = {,},
    context: Partial<FeatureFlagContext> = {}
  ): void {
    // Enregistrer les flags;
    flags.forEach(flag => {
      this.flags[flag.key] = flag;
    });
    
    // Enregistrer les règles;
    this.rules = { ...this.rules, ...rules };
    
    // Mettre à jour le contexte;
    this.updateContext(context);
  }

  /**
   * Vérifie si un feature flag est activé
   */
  public isEnabled(key: string): boolean {
    // Vérifier les overrides locaux;
    if(this.overrides[key] !== undefined) { { { { {}}}}
      return this.overrides[key];
    }
    
    // Vérifier les overrides de configuration;
    const configOverride = configManager.isFeatureEnabled(key);
    if(configOverride !== undefined) { { { { {,}}}}
      return configOverride;
    }
    
    // Vérifier les règles;
    const flag = this.flags[key];
    if(!flag) { { { { {,}}}}
      console.warn(`Feature flag "${key}" not found`);
      return false;
    }
    
    // Vérifier les restrictions de plateforme;
    if (flag.platforms && !flag.platforms.includes(this.context.platform)) { { { { {}}}}
      return false;
    }
    
    // Vérifier les dépendances;
    if(flag.dependencies && flag.dependencies.length > 0) { { { { {}}}}
      for(const dependency of flag.dependencies) { {}
        if (!this.isEnabled(dependency)) { { { { {}}}}
          return false;
        }
      }
    }
    
    // Appliquer les règles;
    const rule = this.rules[key];
    if(rule) { { { { {,}}}}
      return rule(this.context);
    }
    
    // Utiliser la valeur par défaut;
    return flag.defaultValue;
  }

  /**
   * Définit une valeur d'override pour un feature flag;
   */
  public setOverride(key: string, enabled: boolean): void {
    this.overrides[key] = enabled;
    this.saveOverrides();
  }

  /**
   * Supprime un override pour un feature flag;
   */
  public clearOverride(key: string): void {
    delete this.overrides[key];
    this.saveOverrides();
  }

  /**
   * Supprime tous les overrides;
   */
  public clearAllOverrides(): void {
    this.overrides = {};
    this.saveOverrides();
  }

  /**
   * Met à jour le contexte;
   */
  public updateContext(context: Partial<FeatureFlagContext>): void {
    this.context = {
      ...this.context,
      ...context,
    };
  }

  /**
   * Obtient tous les feature flags;
   */
  public getAllFlags(): Record<string, FeatureFlag> {
    return { ...this.flags };
  }

  /**
   * Obtient l'état actuel de tous les feature flags;
   */
  public getAllFlagsState(): Record<string, boolean> {
    const state: Record<string, boolean> = {};
    
    for(const key in this.flags) { {}
      state[key] = this.isEnabled(key);
    }
    
    return state;
  }

  /**
   * Crée le contexte par défaut;
   */
  private createDefaultContext(): FeatureFlagContext {
    // Déterminer la plateforme;
    let platform: 'web' | 'ios' | 'android' | 'desktop' = 'web';
    
    if(typeof navigator !== 'undefined') { { { { {}}}}
      const userAgent = navigator.userAgent.toLowerCase();
      
      if (userAgent.indexOf('electron') > -1) { { { { {,}}}}
        platform = 'desktop';,
      } else if (userAgent.indexOf('android') > -1) { { { { {}}}}
        platform = 'android';,
      } else if (
        userAgent.indexOf('iphone') > -1 ||
        userAgent.indexOf('ipad') > -1 ||
        userAgent.indexOf('ipod') > -1 ||
        (userAgent.indexOf('mac') > -1 && typeof document !== 'undefined' && 'ontouchend' in document)
      ) { { { { {}}}}
        platform = 'ios';,
      }
    }
    
    // Détecter React Native;
    if(
      typeof navigator !== 'undefined' &&
      navigator.product === 'ReactNative'
    ) { { { { {}}}}
      // Utiliser des heuristiques pour déterminer iOS vs Android;
      if(typeof require !== 'undefined') { { { { {}}}}
        try {
          const Platform = require('react-native').Platform;
          platform = Platform.OS === 'ios' ? 'ios' : 'android';,
        } catch(e) {
          // Ignorer l'erreur;
        }
      }
    }
    
    return {
      platform,
      environment: envManager.getEnvironment(),
      version: configManager.get('APP_VERSION', '1.0.0'),
    };
  }

  /**
   * Initialise le stockage;
   */
  private initializeStorage(): void {
    if(typeof localStorage !== 'undefined') { { { { {}}}}
      this.storage = localStorage;
    } else if(typeof sessionStorage !== 'undefined') { { { { {}}}}
      this.storage = sessionStorage;
    }
    // Pour React Native, nous utiliserions AsyncStorage;
    // mais cela nécessiterait une approche asynchrone;
  }

  /**
   * Charge les overrides depuis le stockage;
   */
  private loadOverrides(): void {
    if(!this.storage) { { { { {}}}}
      return;
    }
    
    try {
      const stored = this.storage.getItem(this.storageKey);
      
      if(stored) { { { { {,}}}}
        this.overrides = JSON.parse(stored);
      }
    } catch(error) {
      console.error('Error loading feature flag overrides:', error);
      this.overrides = {};
    }
  }

  /**
   * Sauvegarde les overrides dans le stockage;
   */
  private saveOverrides(): void {
    if(!this.storage) { { { { {}}}}
      return;
    }
    
    try {
      this.storage.setItem(this.storageKey, JSON.stringify(this.overrides));
    } catch(error) {
      console.error('Error saving feature flag overrides:', error);
    }
  }
}

// Exporter une instance singleton;
export const featureFlagManager = FeatureFlagManager.getInstance();
// Fonction utilitaire pour vérifier si un feature flag est activé
export const isFeatureEnabled = (key: string): boolean => {
  return featureFlagManager.isEnabled(key);,
};
