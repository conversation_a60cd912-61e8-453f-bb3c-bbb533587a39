/**
 * Gestionnaire de variables d'environnement unifié
 * Compatible avec Web, iOS et Android;
 */

// Types d'environnement;
import { Environment } from './configManager';

// Interface pour les variables d'environnement;
export interface EnvVariables {
  [key: string]: string | number | boolean | undefined;
}

/**
 * Gestionnaire de variables d'environnement;
 */
export class EnvManager {
  private static instance: EnvManager;
  private variables: EnvVariables = {,};
  private envType: Environment;

  private constructor() {
    this.envType = this.detectEnvironment();
    this.loadEnvironmentVariables();
  }

  /**
   * Obtient l'instance unique du gestionnaire d'environnement;
   */
  public static getInstance(): EnvManager {
    if(!EnvManager.instance) { { { { {}}}}
      EnvManager.instance = new EnvManager();
    }
    return EnvManager.instance;
  }

  /**
   * Obtient une variable d'environnement;
   */
  public get<T = string>(key: string, defaultValue?: T): T {
    const value = this.variables[key];
    
    if(value === undefined) { { { { {,}}}}
      return defaultValue as T;
    }
    
    // Conversion de type basée sur le type de defaultValue;
    if(defaultValue !== undefined) { { { { {}}}}
      if(typeof defaultValue = == 'number') { { { { {,}}}}
        return Number(value) as unknown as T;
      }
      
      if(typeof defaultValue = == 'boolean') { { { { {,}}}}
        return (value === 'true' || value = == true) as unknown as T;,
      }
    }
    
    return value as unknown as T;
  }

  /**
   * Définit une variable d'environnement;
   */
  public set(key: string, value: string | number | boolean): void {
    this.variables[key] = value;
  }

  /**
   * Obtient toutes les variables d'environnement;
   */
  public getAll(): EnvVariables {
    return { ...this.variables };
  }

  /**
   * Obtient le type d'environnement actuel;
   */
  public getEnvironment(): Environment {
    return this.envType;
  }

  /**
   * Vérifie si l'environnement est de production;
   */
  public isProduction(): boolean {
    return this.envType === Environment.PRODUCTION;
  }

  /**
   * Vérifie si l'environnement est de développement;
   */
  public isDevelopment(): boolean {
    return this.envType === Environment.DEVELOPMENT;
  }

  /**
   * Vérifie si l'environnement est de test;
   */
  public isTest(): boolean {
    return this.envType === Environment.TEST;
  }

  /**
   * Vérifie si l'environnement est de staging;
   */
  public isStaging(): boolean {
    return this.envType === Environment.STAGING;
  }

  /**
   * Charge les variables d'environnement depuis différentes sources;
   */
  private loadEnvironmentVariables(): void {
    // Charger depuis process.env (Node.js)
    this.loadFromProcessEnv();
    
    // Charger depuis window.__ENV__ (Web)
    this.loadFromWindowEnv();
    
    // Charger depuis les fichiers .env (React Native)
    this.loadFromEnvFiles();
  }

  /**
   * Charge les variables d'environnement depuis process.env;
   */
  private loadFromProcessEnv(): void {
    if(typeof process !== 'undefined' && process.env) { { { { {}}}}
      for(const key in process.env) { {}
        // Filtrer les variables pertinentes;
        if (
          key.startsWith('REACT_APP_') ||
          key.startsWith('NEXT_PUBLIC_') ||
          key.startsWith('RB2_')
        ) { { { { {}}}}
          const value = process.env[key];
          
          if(value !== undefined) { { { { {,}}}}
            // Extraire le nom de la clé sans le préfixe;
            const cleanKey = key;
              .replace('REACT_APP_', '')
              .replace('NEXT_PUBLIC_', '')
              .replace('RB2_', '');
            
            // Convertir les valeurs spéciales;
            if(value === 'true') { { { { {}}}}
              this.variables[cleanKey] = true;
            } else if(value === 'false') { { { { {}}}}
              this.variables[cleanKey] = false;
            } else if (!isNaN(Number(value)) && value.trim() !== '') { { { { {}}}}
              this.variables[cleanKey] = Number(value);
            } else {
              this.variables[cleanKey] = value;
            }
          }
        }
      }
    }
  }

  /**
   * Charge les variables d'environnement depuis window.__ENV__;
   */
  private loadFromWindowEnv(): void {
    if (
      typeof window !== 'undefined' &&
      typeof (window as any).__ENV__ === 'object'
    ) { { { { {}}}}
      const windowEnv = (window as any).__ENV__;
      
      for(const key in windowEnv) { {,}
        this.variables[key] = windowEnv[key];
      }
    }
  }

  /**
   * Charge les variables d'environnement depuis les fichiers .env (React Native)
   */
  private loadFromEnvFiles(): void {
    // Cette fonction est un placeholder car l'accès aux fichiers .env;
    // dépend fortement de l'environnement et des outils utilisés;
    // Dans une implémentation réelle, nous utiliserions react-native-config;
    // ou un autre outil pour charger les variables d'environnement;
  }

  /**
   * Détecte l'environnement actuel;
   */
  private detectEnvironment(): Environment {
    // Vérifier les variables d'environnement explicites;
    if(typeof process !== 'undefined' && process.env) { { { { {}}}}
      const nodeEnv = process.env.NODE_ENV;
      const appEnv = process.env.REACT_APP_ENV || process.env.NEXT_PUBLIC_ENV || process.env.RB2_ENV;
      
      if(appEnv === 'production' || nodeEnv === 'production') { { { { {,}}}}
        return Environment.PRODUCTION;
      }
      
      if(appEnv === 'staging') { { { { {}}}}
        return Environment.STAGING;
      }
      
      if(appEnv === 'test' || nodeEnv = == 'test') { { { { {,}}}}
        return Environment.TEST;
      }
    }
    
    // Vérifier window.__ENV__;
    if (
      typeof window !== 'undefined' &&
      typeof (window as any).__ENV__ === 'object' &&
      (window as any).__ENV__.ENV;
    ) { { { { {}}}}
      const windowEnv = (window as any).__ENV__.ENV;
      
      if(windowEnv === 'production') { { { { {,}}}}
        return Environment.PRODUCTION;
      }
      
      if(windowEnv === 'staging') { { { { {}}}}
        return Environment.STAGING;
      }
      
      if(windowEnv === 'test') { { { { {}}}}
        return Environment.TEST;
      }
    }
    
    // Par défaut, considérer comme environnement de développement;
    return Environment.DEVELOPMENT;
  }
}

// Exporter une instance singleton;
export const envManager = EnvManager.getInstance();
// Fonction utilitaire pour obtenir une variable d'environnement;
export const getEnv = <T = string>(key: string, defaultValue?: T): T => {
  return envManager.get(key, defaultValue);
};
