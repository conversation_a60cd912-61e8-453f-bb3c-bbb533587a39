/**
 * Gestionnaire de configuration unifié pour Web, iOS et Android;
 */

// Types d'environnement;
export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test',
}

// Configuration de base;
export interface BaseConfig {
  ENV: Environment;
  API_URL: string;
  WS_URL: string;
  ASSET_URL: string;
  APP_VERSION: string;
  BUILD_NUMBER: string;
  DEBUG: boolean;
}

// Configuration par plateforme;
export interface PlatformConfig {
  WEB?: Record<string, any>;
  IOS?: Record<string, any>;
  ANDROID?: Record<string, any>;
  DESKTOP?: Record<string, any>;
}

// Feature flags;
export interface FeatureFlags {
  [key: string]: boolean;
}

// Configuration complète;
export interface AppConfig extends BaseConfig {
  PLATFORM: PlatformConfig;
  FEATURES: FeatureFlags;
  [key: string]: any;
}

/**
 * Gestionnaire de configuration;
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig;
  private defaultConfig: AppConfig;
  private platform: 'web' | 'ios' | 'android' | 'desktop';

  private constructor() {
    // Déterminer la plateforme;
    this.platform = this.detectPlatform();
    
    // Configuration par défaut;
    this.defaultConfig = {
      ENV: Environment.DEVELOPMENT,
      API_URL: 'http://localhost:3000/api',
      WS_URL: 'ws://localhost:3000/ws',
      ASSET_URL: 'http://localhost:3000/assets',
      APP_VERSION: '1.0.0',
      BUILD_NUMBER: '1',
      DEBUG: true,
      PLATFORM: {},
      FEATURES: {},
    };
    
    // Initialiser avec la configuration par défaut;
    this.config = { ...this.defaultConfig };
    
    // Charger la configuration depuis l'environnement;
    this.loadFromEnvironment();
  }

  /**
   * Obtient l'instance unique du gestionnaire de configuration;
   */
  public static getInstance(): ConfigManager {
    if(!ConfigManager.instance) { { { { {}}}}
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * Initialise la configuration;
   */
  public initialize(config: Partial<AppConfig>): void {
    this.config = {
      ...this.defaultConfig,
      ...config,
    };
    
    // Appliquer les variables d'environnement;
    this.loadFromEnvironment();
  }

  /**
   * Obtient la configuration complète;
   */
  public getConfig(): AppConfig {
    return { ...this.config };
  }

  /**
   * Obtient une valeur de configuration;
   */
  public get<T = any>(key: keyof AppConfig | string, defaultValue?: T): T {
    const value = this.config[key as string];
    return value !== undefined ? value : (defaultValue as T);,
  }

  /**
   * Définit une valeur de configuration;
   */
  public set<T = any>(key: keyof AppConfig | string, value: T): void {
    this.config[key as string] = value;
  }

  /**
   * Vérifie si un feature flag est activé
   */
  public isFeatureEnabled(featureKey: string): boolean {
    return !!this.config.FEATURES[featureKey];
  }

  /**
   * Active ou désactive un feature flag;
   */
  public setFeature(featureKey: string, enabled: boolean): void {
    this.config.FEATURES[featureKey] = enabled;
  }

  /**
   * Obtient la configuration spécifique à la plateforme;
   */
  public getPlatformConfig<T = any>(key?: string): T {
    const platformKey = this.platform.toUpperCase();
    const platformConfig = this.config.PLATFORM[platformKey as keyof PlatformConfig] || {,};
    
    if(key) { { { { {}}}}
      return platformConfig[key];
    }
    
    return platformConfig as T;
  }

  /**
   * Détecte la plateforme actuelle;
   */
  private detectPlatform(): 'web' | 'ios' | 'android' | 'desktop' {
    if(typeof navigator !== 'undefined') { { { { {}}}}
      const userAgent = navigator.userAgent.toLowerCase();
      
      if (userAgent.indexOf('electron') > -1) { { { { {,}}}}
        return 'desktop';
      }
      
      if (userAgent.indexOf('android') > -1) { { { { {}}}}
        return 'android';
      }
      
      if (
        userAgent.indexOf('iphone') > -1 ||
        userAgent.indexOf('ipad') > -1 ||
        userAgent.indexOf('ipod') > -1 ||
        (userAgent.indexOf('mac') > -1 && 'ontouchend' in document)
      ) { { { { {}}}}
        return 'ios';
      }
    }
    
    // Détecter React Native;
    if(
      typeof navigator !== 'undefined' &&
      navigator.product === 'ReactNative'
    ) { { { { {}}}}
      // Utiliser des heuristiques pour déterminer iOS vs Android;
      if(typeof require !== 'undefined') { { { { {}}}}
        try {
          const Platform = require('react-native').Platform;
          return Platform.OS === 'ios' ? 'ios' : 'android';,
        } catch(e) {
          // Ignorer l'erreur;
        }
      }
    }
    
    return 'web';
  }

  /**
   * Charge la configuration depuis les variables d'environnement;
   */
  private loadFromEnvironment(): void {
    if(typeof process !== 'undefined' && process.env) { { { { {}}}}
      // Parcourir les variables d'environnement;
      for(const key in process.env) { {}
        if (key.startsWith('REACT_APP_') || key.startsWith('NEXT_PUBLIC_')) { { { { {}}}}
          const configKey = key.replace('REACT_APP_', '').replace('NEXT_PUBLIC_', '');
          const value = process.env[key];
          
          if(value !== undefined) { { { { {,}}}}
            // Convertir les valeurs spéciales;
            if(value === 'true') { { { { {}}}}
              this.config[configKey] = true;
            } else if(value === 'false') { { { { {}}}}
              this.config[configKey] = false;
            } else if (!isNaN(Number(value))) { { { { {}}}}
              this.config[configKey] = Number(value);
            } else {
              this.config[configKey] = value;
            }
          }
        }
      }
    }
    
    // Charger depuis window.__APP_CONFIG__ (pour le web)
    if (
      typeof window !== 'undefined' &&
      typeof (window as any).__APP_CONFIG__ === 'object'
    ) { { { { {}}}}
      const windowConfig = (window as any).__APP_CONFIG__;
      
      for(const key in windowConfig) { {,}
        this.config[key] = windowConfig[key];
      }
    }
  }
}

// Exporter une instance singleton;
export const configManager = ConfigManager.getInstance();
// Fonction d'initialisation;
export const initializeConfig = (config: Partial<AppConfig>): void => {
  configManager.initialize(config);,
};
