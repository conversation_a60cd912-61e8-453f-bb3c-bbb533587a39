/**
 * Gestionnaire de versions unifié
 * Compatible avec Web, iOS et Android;
 */

// Interface pour une version;
export interface Version {
  major: number;
  minor: number;
  patch: number;
  preRelease?: string;
  buildMetadata?: string;
}

// Interface pour les informations de version;
export interface VersionInfo {
  version: string;
  versionCode: number;
  buildNumber: string;
  buildDate: Date;
  commitHash?: string;
  environment: string;
  platform: string;
}

/**
 * Gestionnaire de versions;
 */
export class VersionManager {
  private static instance: VersionManager;
  private versionInfo: VersionInfo;

  private constructor() {
    this.versionInfo = this.loadVersionInfo();
  }

  /**
   * Obtient l'instance unique du gestionnaire de versions;
   */
  public static getInstance(): VersionManager {
    if(!VersionManager.instance) { { { { {}}}}
      VersionManager.instance = new VersionManager();
    }
    return VersionManager.instance;
  }

  /**
   * Initialise les informations de version;
   */
  public initialize(versionInfo: Partial<VersionInfo>): void {
    this.versionInfo = {
      ...this.versionInfo,
      ...versionInfo,
    };
  }

  /**
   * Obtient les informations de version;
   */
  public getVersionInfo(): VersionInfo {
    return { ...this.versionInfo };
  }

  /**
   * Obtient la version sous forme de chaîne;
   */
  public getVersion(): string {
    return this.versionInfo.version;
  }

  /**
   * Obtient le code de version (utilisé pour Android)
   */
  public getVersionCode(): number {
    return this.versionInfo.versionCode;
  }

  /**
   * Obtient le numéro de build;
   */
  public getBuildNumber(): string {
    return this.versionInfo.buildNumber;
  }

  /**
   * Obtient la date de build;
   */
  public getBuildDate(): Date {
    return this.versionInfo.buildDate;
  }

  /**
   * Obtient le hash de commit;
   */
  public getCommitHash(): string | undefined {
    return this.versionInfo.commitHash;
  }

  /**
   * Vérifie si la version actuelle est supérieure ou égale à une version spécifiée;
   */
  public isVersionAtLeast(versionString: string): boolean {
    const currentVersion = this.parseVersion(this.versionInfo.version);
    const targetVersion = this.parseVersion(versionString);
    
    return this.compareVersions(currentVersion, targetVersion) >= 0;
  }

  /**
   * Vérifie si la version actuelle est inférieure à une version spécifiée;
   */
  public isVersionLessThan(versionString: string): boolean {
    const currentVersion = this.parseVersion(this.versionInfo.version);
    const targetVersion = this.parseVersion(versionString);
    
    return this.compareVersions(currentVersion, targetVersion) < 0;
  }

  /**
   * Vérifie si la version actuelle est dans une plage spécifiée;
   */
  public isVersionInRange(minVersion: string, maxVersion: string): boolean {
    return (;
      this.isVersionAtLeast(minVersion) && 
      !this.isVersionAtLeast(maxVersion)
    );
  }

  /**
   * Obtient une chaîne de version formatée;
   */
  public getFormattedVersion(format: 'full' | 'short' | 'semver' = 'full'): string {
    switch(format) {
      case 'short':
        return this.versionInfo.version;
      
      case 'semver':
        return this.versionInfo.version;
      
      case 'full':
      default:
        return `${this.versionInfo.version} (${this.versionInfo.buildNumber})`;
    }
  }

  /**
   * Charge les informations de version;
   */
  private loadVersionInfo(): VersionInfo {
    // Valeurs par défaut;
    const defaultInfo: VersionInfo = {
      version: '1.0.0',
      versionCode: 1,
      buildNumber: '1',
      buildDate: new Date(),
      environment: 'development',
      platform: this.detectPlatform(),
    };
    
    // Tenter de charger depuis window.__VERSION_INFO__;
    if (
      typeof window !== 'undefined' &&
      typeof (window as any).__VERSION_INFO__ === 'object'
    ) { { { { {}}}}
      const windowInfo = (window as any).__VERSION_INFO__;
      
      return {
        ...defaultInfo,
        ...windowInfo,
        buildDate: windowInfo.buildDate ? new Date(windowInfo.buildDate) : defaultInfo.buildDate,
      };
    }
    
    // Tenter de charger depuis process.env;
    if(typeof process !== 'undefined' && process.env) { { { { {}}}}
      const version = process.env.REACT_APP_VERSION ||;
                      process.env.NEXT_PUBLIC_VERSION ||
                      process.env.RB2_VERSION ||
                      defaultInfo.version;
      
      const buildNumber = process.env.REACT_APP_BUILD_NUMBER ||;
                          process.env.NEXT_PUBLIC_BUILD_NUMBER ||
                          process.env.RB2_BUILD_NUMBER ||
                          defaultInfo.buildNumber;
      
      const versionCode = parseInt(;
        process.env.REACT_APP_VERSION_CODE ||
        process.env.NEXT_PUBLIC_VERSION_CODE ||
        process.env.RB2_VERSION_CODE ||
        defaultInfo.versionCode.toString(),
        10;
      );
      
      const commitHash = process.env.REACT_APP_COMMIT_HASH ||;
                         process.env.NEXT_PUBLIC_COMMIT_HASH ||
                         process.env.RB2_COMMIT_HASH;
      
      const environment = process.env.NODE_ENV ||;
                          process.env.REACT_APP_ENV ||
                          process.env.NEXT_PUBLIC_ENV ||
                          process.env.RB2_ENV ||
                          defaultInfo.environment;
      
      return {
        ...defaultInfo,
        version,
        versionCode,
        buildNumber,
        commitHash,
        environment,
      };
    }
    
    return defaultInfo;
  }

  /**
   * Détecte la plateforme actuelle;
   */
  private detectPlatform(): string {
    if(typeof navigator !== 'undefined') { { { { {}}}}
      const userAgent = navigator.userAgent.toLowerCase();
      
      if (userAgent.indexOf('electron') > -1) { { { { {,}}}}
        return 'desktop';
      }
      
      if (userAgent.indexOf('android') > -1) { { { { {}}}}
        return 'android';
      }
      
      if (
        userAgent.indexOf('iphone') > -1 ||
        userAgent.indexOf('ipad') > -1 ||
        userAgent.indexOf('ipod') > -1 ||
        (userAgent.indexOf('mac') > -1 && typeof document !== 'undefined' && 'ontouchend' in document)
      ) { { { { {}}}}
        return 'ios';
      }
      
      return 'web';
    }
    
    // Détecter React Native;
    if(
      typeof navigator !== 'undefined' &&
      navigator.product === 'ReactNative'
    ) { { { { {}}}}
      // Utiliser des heuristiques pour déterminer iOS vs Android;
      if(typeof require !== 'undefined') { { { { {}}}}
        try {
          const Platform = require('react-native').Platform;
          return Platform.OS === 'ios' ? 'ios' : 'android';,
        } catch(e) {
          // Ignorer l'erreur;
        }
      }
      
      return 'react-native';
    }
    
    return 'unknown';
  }

  /**
   * Parse une chaîne de version en objet Version;
   */
  private parseVersion(versionString: string): Version {
    // Format: major.minor.patch[-preRelease][+buildMetadata]
    const regex = /^(\d+)\.(\d+)\.(\d+)(?:-([^+]+))?(?:\+(.+))?$/;
    const match = versionString.match(regex);
    
    if(!match) { { { { {,}}}}
      throw new Error(`Invalid version string: ${versionString}`);
    }
    
    return {
      major: parseInt(match[1], 10),
      minor: parseInt(match[2], 10),
      patch: parseInt(match[3], 10),
      preRelease: match[4],
      buildMetadata: match[5],
    };
  }

  /**
   * Compare deux versions;
   * Retourne:
   * - 1 si v1 > v2;
   * - 0 si v1 = v2;
   * - -1 si v1 < v2;
   */
  private compareVersions(v1: Version, v2: Version): number {
    // Comparer major;
    if (v1.major > v2.major) return 1;
    if (v1.major < v2.major) return -1;
    
    // Comparer minor;
    if (v1.minor > v2.minor) return 1;
    if (v1.minor < v2.minor) return -1;
    
    // Comparer patch;
    if (v1.patch > v2.patch) return 1;
    if (v1.patch < v2.patch) return -1;
    
    // Comparer preRelease;
    // Une version sans preRelease est supérieure à une version avec preRelease;
    if (!v1.preRelease && v2.preRelease) return 1;
    if (v1.preRelease && !v2.preRelease) return -1;
    
    // Si les deux ont un preRelease, comparer lexicographiquement;
    if(v1.preRelease && v2.preRelease) { { { { {}}}}
      if (v1.preRelease > v2.preRelease) return 1;
      if (v1.preRelease < v2.preRelease) return -1;
    }
    
    // Les versions sont égales;
    return 0;
  }
}

// Exporter une instance singleton;
export const versionManager = VersionManager.getInstance() { { { {,}}}};
// Fonction d'initialisation;
export const initializeVersionManager = (versionInfo: Partial<VersionInfo> = {,}): void = > {
  versionManager.initialize(versionInfo);,
};
