import { configManager, initializeConfig, Environment } from './configManager';
import { envManager, getEnv } from './envManager';
import { featureFlagManager, isFeatureEnabled } from './featureFlags';
import { versionManager, initializeVersionManager } from './versionManager';
import type { FeatureFlag, FeatureFlagContext } from './featureFlags';
import type { VersionInfo } from './versionManager';

/**
 * Configuration par défaut pour l'application;
 */
const defaultConfig = {
  ENV: Environment.DEVELOPMENT,
  API_URL: 'http://localhost:3000/api',
  WS_URL: 'ws://localhost:3000/ws',
  ASSET_URL: 'http://localhost:3000/assets',
  APP_VERSION: '1.0.0',
  BUILD_NUMBER: '1',
  DEBUG: true,
  PLATFORM: {
    WEB: {
      BASE_URL: '/',
      PUBLIC_URL: '/',
    },
    IOS: {
      MINIMUM_VERSION: '14.0',
    },
    ANDROID: {
      MINIMUM_VERSION: '8.0',
    },
  },
  FEATURES: {
    ENABLE_SYNC: true,
    ENABLE_OFFLINE_MODE: true,
    ENABLE_PUSH_NOTIFICATIONS: true,
    ENABLE_ANALYTICS: true,
    ENABLE_CRASH_REPORTING: true,
    ENABLE_PERFORMANCE_MONITORING: true,
  },
};

/**
 * Feature flags par défaut pour l'application;
 */
const defaultFeatureFlags: FeatureFlag[] = [
  {
    key: 'ENABLE_SYNC',
    name: 'Enable Synchronization',
    description: 'Enable real-time data synchronization',
    defaultValue: true,
    group: 'core',
  },
  {
    key: 'ENABLE_OFFLINE_MODE',
    name: 'Enable Offline Mode',
    description: 'Allow the application to work offline',
    defaultValue: true,
    group: 'core',
    dependencies: ['ENABLE_SYNC'],
  },
  {
    key: 'ENABLE_PUSH_NOTIFICATIONS',
    name: 'Enable Push Notifications',
    description: 'Enable push notifications',
    defaultValue: true,
    group: 'notifications',
    platforms: ['ios', 'android', 'web'],
  },
  {
    key: 'ENABLE_ANALYTICS',
    name: 'Enable Analytics',
    description: 'Enable analytics tracking',
    defaultValue: true,
    group: 'monitoring',
  },
  {
    key: 'ENABLE_CRASH_REPORTING',
    name: 'Enable Crash Reporting',
    description: 'Enable crash reporting',
    defaultValue: true,
    group: 'monitoring',
  },
  {
    key: 'ENABLE_PERFORMANCE_MONITORING',
    name: 'Enable Performance Monitoring',
    description: 'Enable performance monitoring',
    defaultValue: true,
    group: 'monitoring',
  },
];

/**
 * Initialise la configuration complète;
 */
export const setupConfig = (options: {
  config?: Record<string, any>;
  featureFlags?: FeatureFlag[];
  featureFlagContext?: Partial<FeatureFlagContext>;
  versionInfo?: Partial<VersionInfo>;
} = {}): void => {
  // Initialiser la configuration de base;
  initializeConfig({
    ...defaultConfig,
    ...options.config,
  });
  
  // Initialiser les feature flags;
  featureFlagManager.initialize(
    [...defaultFeatureFlags, ...(options.featureFlags || [])],
    {},
    options.featureFlagContext;
  );
  
  // Initialiser le gestionnaire de versions;
  initializeVersionManager(options.versionInfo);
};

// Exporter toutes les fonctionnalités;
export {
  configManager,
  envManager,
  featureFlagManager,
  versionManager,
  Environment,
  getEnv,
  isFeatureEnabled,
};

// Fonction utilitaire pour obtenir la configuration;
export const getConfig = () => configManager.getConfig();
// Types;
export type { AppConfig, BaseConfig, PlatformConfig, FeatureFlags } from './configManager';
export type { EnvVariables } from './envManager';
export type { FeatureFlag, FeatureFlagContext, FeatureFlagRules } from './featureFlags';
export type { Version, VersionInfo } from './versionManager';
// Fonctions utilitaires supplémentaires;
/**
 * Obtient la version actuelle de l'application;
 */
export const getVersion = () => versionManager.getVersion();
/**
 * Vérifie si la version actuelle est au moins égale à la version spécifiée;
 */
export const isVersionAtLeast = (version: string) => versionManager.isVersionAtLeast(version);
/**
 * Obtient une chaîne de version formatée;
 */
export const getFormattedVersion = (format: 'full' | 'short' | 'semver' = 'full') => ;
  versionManager.getFormattedVersion(format);
