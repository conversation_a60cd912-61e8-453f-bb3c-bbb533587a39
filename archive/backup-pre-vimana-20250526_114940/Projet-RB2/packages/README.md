# Unified Frontend Monorepo

This directory contains the packages for our unified frontend approach across Web, iOS, and Android platforms.

## Package Structure

- `/core` - Business logic shared across all platforms
- `/ui` - Shared UI components that work on all platforms
- `/api` - Shared API client for all platforms
- `/state` - Shared state management
- `/web` - Web application specific code
- `/mobile` - Mobile application specific code (iOS & Android)
- `/desktop` - Desktop application specific code (optional)

## Development

To run the development environment:

```bash
npm run dev
```

## Building

To build all packages:

```bash
npm run build
```

To build specific packages:

```bash
npm run build -- --filter=web
npm run build -- --filter=mobile
```

## Testing

To run tests for all packages:

```bash
npm run test
```

## Directory Structure Overview

Each package follows a standard structure:

```
/package-name
  /src          # Source code
  /tests        # Tests
  package.json  # Package-specific dependencies and scripts
  tsconfig.json # TypeScript configuration
  README.md     # Package-specific documentation
```

## Shared Code Principles

1. **Platform Agnostic Logic**: Business logic should be written once in the `core` package
2. **Responsive Components**: UI components should adapt to different screen sizes and platforms
3. **Offline-First**: All applications should work offline and sync when connected
4. **Type Safety**: Use TypeScript throughout the codebase
5. **Testing**: All code should have appropriate unit, integration, and e2e tests 