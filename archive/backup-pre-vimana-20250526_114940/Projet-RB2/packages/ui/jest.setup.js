// Polyfills et mocks
global.setImmediate = jest.fn((fn) => setTimeout(fn, 0));
global.clearImmediate = jest.fn((id) => clearTimeout(id));

// Mock pour react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

// Mock pour react-native-svg
jest.mock('react-native-svg', () => {
  const React = require('react');
  const ReactNativeSvg = {
    SvgXml: (props) => React.createElement('SvgXml', props, props.children),
    Svg: (props) => React.createElement('Svg', props, props.children),
    Path: (props) => React.createElement('Path', props, props.children),
    Circle: (props) => React.createElement('Circle', props, props.children),
    Rect: (props) => React.createElement('Rect', props, props.children),
    G: (props) => React.createElement('G', props, props.children),
    Defs: (props) => React.createElement('Defs', props, props.children),
    ClipPath: (props) => React.createElement('ClipPath', props, props.children),
  };
  return ReactNativeSvg;
});

// Mock pour Platform.OS pour simuler l'environnement web
jest.mock('react-native/Libraries/Utilities/Platform', () => ({
  OS: 'web',
  select: (obj) => obj.web || obj.default,
}));

// Mock pour Animated
jest.mock('react-native', () => {
  const rn = jest.requireActual('react-native');
  rn.Animated.timing = jest.fn(() => ({
    start: jest.fn((callback) => callback && callback()),
  }));
  rn.Animated.spring = jest.fn(() => ({
    start: jest.fn((callback) => callback && callback()),
  }));
  rn.Animated.loop = jest.fn(() => ({
    start: jest.fn((callback) => callback && callback()),
  }));
  return rn;
});

// Mock des modules de React Native qui pourraient ne pas être compatibles avec l'environnement de test
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');
jest.mock('react-native/Libraries/Animated/Animated', () => ({
  ...jest.requireActual('react-native/Libraries/Animated/Animated'),
  timing: () => ({
    start: jest.fn(),
    reset: jest.fn(),
  }),
}));

// Mock pour react-native-safe-area-context
jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 0, right: 0, bottom: 0, left: 0 }),
  SafeAreaProvider: ({ children }) => children,
}));

// Supprime les avertissements de React liés aux tests
jest.spyOn(console, 'error').mockImplementation((message) => {
  // Ignorer certains avertissements spécifiques si nécessaire
  if (message && message.includes('Warning:')) {
    return;
  }
  console.warn(message);
});

// Configuration globale
global.requestAnimationFrame = (callback) => {
  setTimeout(callback, 0);
};

// Augmenter Jest avec des assertions personnalisées
expect.extend({
  // Exemple d'assertion personnalisée pour tester les styles
  toHaveStyleRule(received, property, value) {
    const style = received?.props?.style;
    const hasProperty = style && style[property] !== undefined;
    const propertyValue = hasProperty ? style[property] : null;
    const pass = hasProperty && propertyValue === value;
    
    return {
      pass,
      message: () => pass
        ? `Expected ${received} not to have style property ${property}=${value}`
        : `Expected ${received} to have style property ${property}=${value}, got ${propertyValue}`,
    };
  },
}); 