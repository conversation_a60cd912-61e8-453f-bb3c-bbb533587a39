{"name": "@projet-rb2/ui", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "dev": "tsup src/index.ts --format esm,cjs --watch --dts", "lint": "eslint src/**/*.ts* --fix", "test": "jest", "test:coverage": "jest --coverage", "test:web": "cross-env PLATFORM=web jest", "test:ios": "cross-env PLATFORM=ios jest", "test:android": "cross-env PLATFORM=android jest", "test:all": "npm run test:web && npm run test:ios && npm run test:android", "test:watch": "jest --watch"}, "dependencies": {"@projet-rb2/core": "*", "react": "^18.2.0", "react-dom": "^18.2.0", "react-native": "^0.73.2", "react-native-web": "^0.19.9"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.8.4", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-native": "^0.73.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsup": "^8.0.1", "typescript": "^5.4.5"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-native": "^0.73.2", "react-native-web": "^0.19.9"}}