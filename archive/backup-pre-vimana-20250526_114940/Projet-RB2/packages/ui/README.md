# @projet-rb2/ui

Bibliothèque de composants UI partagés pour les applications web et mobile.

## Installation

```bash
npm install @projet-rb2/ui
```

## Description

Ce package contient tous les composants UI partagés entre les applications web et mobile du projet RB2. Chaque composant est conçu pour être utilisé de manière transparente sur toutes les plateformes (web, iOS, Android).

## Composants disponibles

- Text: Composant texte configurable
- Button: Bouton avec plusieurs variantes
- Input: Champ de saisie
- Card: Carte
- Avatar: Avatar pour utilisateurs
- Badge: Badge pour notifications
- Spinner: Indicateur de chargement
- Modal: Fenêtre modale
- Alert: Alertes et notifications
- Icon: Système d'icônes

## Tests cross-platform

Les tests cross-platform sont organisés pour vérifier le comportement de chaque composant sur les différentes plateformes (web, iOS, Android).

### Structure des tests

Les tests cross-platform sont situés dans le dossier `src/__tests__/cross-platform/` et suivent la convention de nommage suivante:

```
ComponentName.cross-platform.test.tsx
```

### Utilitaires de test

- `PlatformHelper.ts`: Utilitaire pour simuler différentes plateformes dans les tests

### Exécution des tests

Pour exécuter tous les tests:

```bash
npm test
```

Pour exécuter uniquement les tests cross-platform:

```bash
npm test -- --testMatch="**/*.cross-platform.test.tsx"
```

Pour exécuter les tests avec la couverture de code:

```bash
npm test -- --coverage
```

### Exemples de tests cross-platform

#### Test du composant Text

```tsx
// Text.cross-platform.test.tsx
import React from 'react';
import { render } from '@testing-library/react-native';
import { Text } from '../../components/Text/Text';
import { setPlatform, resetPlatform } from './PlatformHelper';

describe('Text Cross-Platform Tests', () => {
  afterEach(() => {
    resetPlatform();
  });
  
  describe('on Web platform', () => {
    beforeEach(() => {
      setPlatform('web');
    });
    
    test('renders correctly with web-specific properties', () => {
      // Test du composant sur le web
    });
  });
  
  describe('on iOS platform', () => {
    beforeEach(() => {
      setPlatform('ios');
    });
    
    test('renders correctly with iOS-specific styles', () => {
      // Test du composant sur iOS
    });
  });
  
  describe('on Android platform', () => {
    beforeEach(() => {
      setPlatform('android');
    });
    
    test('renders correctly with Android-specific styles', () => {
      // Test du composant sur Android
    });
  });
  
  describe('on all platforms', () => {
    const platforms = ['ios', 'android', 'web'];
    
    platforms.forEach(platform => {
      test(`behaves correctly on ${platform}`, () => {
        setPlatform(platform);
        // Test du comportement commun sur toutes les plateformes
      });
    });
  });
});
```

## Développement

### Ajout d'un nouveau composant

1. Créer le composant dans `src/components/ComponentName/`
2. Ajouter les exportations nécessaires dans `src/index.ts`
3. Ajouter des tests cross-platform dans `src/__tests__/cross-platform/`

### Principes à suivre

- Chaque composant doit fonctionner sur toutes les plateformes
- Utiliser les adaptations spécifiques à la plateforme quand nécessaire
- Toujours écrire des tests pour chaque plateforme

## Licence

Propriété de Projet RB2 - Tous droits réservés 