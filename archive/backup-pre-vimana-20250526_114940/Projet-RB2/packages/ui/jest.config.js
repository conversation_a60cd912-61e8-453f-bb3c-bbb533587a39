/** @type {import('jest').Config} */

module.exports = {
  preset: 'react-native',
  
  // Chemins où chercher les tests
  roots: ['<rootDir>/src'],
  
  // Transformations des fichiers
  transform: {
    '^.+\\.(ts|tsx)$': 'babel-jest',
  },
  
  // Extensions des fichiers à prendre en compte
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  
  // Fichier de setup pour Jest
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  
  // Les fichiers à ignorer pour les tests
  testPathIgnorePatterns: ['/node_modules/', '/dist/'],
  
  // Les extensions des fichiers de test
  testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$',
  
  // Les chemins à mapper pour les imports
  moduleNameMapper: {
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/__mocks__/fileMock.js',
    '\\.(css|scss)$': 'identity-obj-proxy',
    // Mappage des packages locaux
    '@projet-rb2/core/(.*)': '<rootDir>/../core/src/$1',
  },
  
  // Collecte de la couverture de code
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/index.ts',
  ],
  
  // Dossier où stocker les résultats de la couverture
  coverageDirectory: '<rootDir>/coverage',
  
  // Seuil minimal de couverture
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
  
  // Configuration des reporters
  reporters: ['default'],
  
  // Spécifie si jest doit s'exécuter avec jsdom ou node
  testEnvironment: 'jsdom',
  
  // Configuration de jsdom
  testEnvironmentOptions: {
    url: 'http://localhost',
  },
  
  // Lier les tests par dépendance
  detectOpenHandles: true,
  
  // Temps d'attente avant de considérer un test comme échoué
  testTimeout: 10000,
}; 