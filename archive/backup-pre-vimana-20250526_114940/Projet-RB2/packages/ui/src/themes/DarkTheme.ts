import { DefaultTheme, Theme } from './DefaultTheme';

/**
 * Dark theme extending the default theme;
 */
export const DarkTheme: Theme = {
  ...DefaultTheme,
  dark: true,
  colors: {
    ...DefaultTheme.colors,
    primary: '#738BFF', // Lighter shade of primary for dark mode;
    background: '#121212',
    card: '#1E1E1E',
    text: '#FFFFFF',
    border: '#2C2C2C',
    surface: '#1E1E1E',
    surfaceVariant: '#2C2C2C',
  },
  elevation: {
    none: 'none',
    xs: '0 1px 2px rgba(0, 0, 0, 0.9)',
    sm: '0 1px 3px rgba(0, 0, 0, 0.9), 0 1px 2px rgba(0, 0, 0, 0.9)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.9), 0 2px 4px -1px rgba(0, 0, 0, 0.9)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.9), 0 4px 6px -2px rgba(0, 0, 0, 0.9)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.9), 0 10px 10px -5px rgba(0, 0, 0, 0.9)',
  },
}; 