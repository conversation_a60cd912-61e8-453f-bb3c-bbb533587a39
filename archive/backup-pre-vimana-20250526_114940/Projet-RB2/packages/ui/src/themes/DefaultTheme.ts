import { THEME } from '@projet-rb2/core/constants';

export interface Theme {
  dark: boolean;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    card: string;
    text: string;
    border: string;
    notification: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    surface: string;
    surfaceVariant: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
  };
  borderRadius: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    pill: number;
  };
  typography: {
    fontFamily: {
      base: string;
      heading: string;
      monospace: string;
    };
    fontWeight: {
      regular: string;
      medium: string;
      semiBold: string;
      bold: string;
    };
    fontSize: {
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
      xxl: number;
    };
  };
  elevation: {
    none: string;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

/**
 * Default light theme;
 */
export const DefaultTheme: Theme = {
  dark: false,
  colors: {
    primary: THEME.COLORS.PRIMARY,
    secondary: THEME.COLORS.SECONDARY,
    background: '#FFFFFF',
    card: '#FFFFFF',
    text: THEME.COLORS.DARK,
    border: '#E1E1E1',
    notification: THEME.COLORS.SECONDARY,
    success: THEME.COLORS.SUCCESS,
    warning: THEME.COLORS.WARNING,
    error: THEME.COLORS.ERROR,
    info: THEME.COLORS.INFO,
    surface: '#FFFFFF',
    surfaceVariant: '#F5F5F5',
  },
  spacing: {
    xs: THEME.SPACING.XS,
    sm: THEME.SPACING.SM,
    md: THEME.SPACING.MD,
    lg: THEME.SPACING.LG,
    xl: THEME.SPACING.XL,
    xxl: THEME.SPACING.XXL,
  },
  borderRadius: {
    xs: 2,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    pill: 9999,
  },
  typography: {
    fontFamily: {
      base: 'System',
      heading: 'System',
      monospace: 'monospace',
    },
    fontWeight: {
      regular: '400',
      medium: '500',
      semiBold: '600',
      bold: '700',
    },
    fontSize: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
      xxl: 24,
    },
  },
  elevation: {
    none: 'none',
    xs: '0 1px 2px rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  },
}; 