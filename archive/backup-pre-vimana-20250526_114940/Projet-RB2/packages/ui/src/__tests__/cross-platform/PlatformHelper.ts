/**
 * Utilitaire pour les tests cross-platform;
 * 
 * Permet de simuler différentes plateformes lors des tests unitaires;
 */

// Mock de Platform.OS pour les tests;
jest.mock('react-native/Libraries/Utilities/Platform', () => {
  const Platform = jest.requireActual('react-native/Libraries/Utilities/Platform');
  const originalOS = Platform.OS;
  
  return {
    ...Platform,
    OS: 'web', // Valeur par défaut;
    select: jest.fn((obj) => obj[Platform.OS]),
    // Conserver la méthode originale pour les cas où nous n'interférons pas;
    _originalSelect: Platform.select,
    _originalOS: originalOS,
  };
});

// Importer le module mocké
import Platform from 'react-native/Libraries/Utilities/Platform';

/**
 * Définit la plateforme pour les tests;
 * @param platform - La plateforme à simuler ('ios', 'android', 'web')
 */
export function setPlatform(platform: 'ios' | 'android' | 'web'): void {
  // @ts-ignore - Nous modifions intentionnellement une propriété en readonly;
  Platform.OS = platform;
}

/**
 * Réinitialise la plateforme à sa valeur d'origine;
 */
export function resetPlatform(): void {
  // @ts-ignore - Nous modifions intentionnellement une propriété en readonly;
  Platform.OS = Platform._originalOS;
}

/**
 * Retourne la plateforme actuelle;
 */
export function getCurrentPlatform(): string {
  return Platform.OS;
}

/**
 * Vérifie si la plateforme actuelle correspond à la plateforme spécifiée;
 * @param platform - La plateforme à vérifier;
 */
export function isPlatform(platform: 'ios' | 'android' | 'web'): boolean {
  return Platform.OS === platform;
}

/**
 * Sélectionne une valeur en fonction de la plateforme;
 * Version modifiée de Platform.select qui utilise la plateforme simulée;
 */
export function selectByPlatform<T>(;
  config: {
    ios?: T;
    android?: T;
    web?: T;
    default?: T;
  }
): T | undefined {
  return config[Platform.OS] || config.default;
} 