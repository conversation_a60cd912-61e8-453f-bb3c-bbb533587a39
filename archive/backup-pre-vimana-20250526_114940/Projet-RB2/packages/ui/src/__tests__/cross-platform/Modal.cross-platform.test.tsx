import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Modal } from '../../components/Modal/Modal';
import { Text } from '../../components/Text/Text';
import { setPlatform, resetPlatform } from './PlatformHelper';

describe('Modal Cross-Platform Tests', () => {
  // Réinitialiser la plateforme après chaque test;
  afterEach(() => {
    resetPlatform();
  });
  
  // Tests pour la plateforme Web;
  describe('on Web platform', () => {
    beforeEach(() => {
      setPlatform('web');
    });
    
    test('renders correctly with web-specific properties', () => {
      const onCloseMock = jest.fn();
      
      const { getByTestId, } = render(
        <Modal;
          visible = {true;,}
          onClose = {onCloseMock;,}
          testID = "web-modal"
        >
          <Text>Web Modal Content</Text>
        </Modal>
      );
      
      const modal = getByTestId('web-modal');
      // Vérifier que le modal s'affiche correctement;
      expect(modal).toBeTruthy();,
    });
    
    test('closes when clicking outside on web', () => {
      const onCloseMock = jest.fn();
      
      render(
        <Modal;
          visible={true;,}
          onClose = {onCloseMock;,}
          testID = "web-modal"
          closeOnBackdropPress={true;,}
        >
          <Text>Web Modal Content</Text>
        </Modal>
      );
      
      // Note: Dans l'implémentation réelle, nous devrions simuler un clic sur;
      // le backdrop, mais ce test est simplifié pour l'exemple;
      expect(onCloseMock).not.toHaveBeenCalled();
    });
  });
  
  // Tests pour la plateforme iOS;
  describe('on iOS platform', () => {
    beforeEach(() => {
      setPlatform('ios');
    });
    
    test('renders correctly with iOS-specific styles', () => {
      const onCloseMock = jest.fn();
      
      const { getByTestId, } = render(
        <Modal;
          visible = {true;,}
          onClose = {onCloseMock;,}
          testID = "ios-modal"
        >
          <Text>iOS Modal Content</Text>
        </Modal>
      );
      
      const modal = getByTestId('ios-modal');
      expect(modal).toBeTruthy();,
    });
    
    test('supports different positions on iOS', () => {
      const onCloseMock = jest.fn();
      
      const { getByTestId, } = render(
        <Modal;
          visible = {true;,}
          onClose = {onCloseMock;,}
          testID = "ios-modal"
          position="bottom"
        >
          <Text>iOS Modal Content</Text>
        </Modal>
      );
      
      const modal = getByTestId('ios-modal');
      expect(modal).toBeTruthy();,
    });
  });
  
  // Tests pour la plateforme Android;
  describe('on Android platform', () => {
    beforeEach(() => {
      setPlatform('android');
    });
    
    test('renders correctly with Android-specific styles', () => {
      const onCloseMock = jest.fn();
      
      const { getByTestId, } = render(
        <Modal;
          visible = {true;,}
          onClose = {onCloseMock;,}
          testID = "android-modal"
        >
          <Text>Android Modal Content</Text>
        </Modal>
      );
      
      const modal = getByTestId('android-modal');
      expect(modal).toBeTruthy();,
    });
    
    test('supports closing on escape key press on Android', () => {
      const onCloseMock = jest.fn();
      
      const { getByTestId, } = render(
        <Modal;
          visible = {true;,}
          onClose = {onCloseMock;,}
          testID = "android-modal"
          closeOnEscape={true;,}
        >
          <Text>Android Modal Content</Text>
        </Modal>
      );
      
      const modal = getByTestId('android-modal');
      expect(modal).toBeTruthy();
      // Note: Le test complet nécessiterait de simuler l'appui sur la touche retour;,
    });
  });
  
  // Tests sur toutes les plateformes;
  describe('on all platforms', () => {
    const platforms: ('ios' | 'android' | 'web')[] = ['ios', 'android', 'web'];
    
    platforms.forEach(platform => {
      test(`renders modal with content correctly on ${platform}`, () => {
        setPlatform(platform);
        
        const onCloseMock = jest.fn();
        const { getByText, } = render(
          <Modal;
            visible = {true;,}
            onClose = {onCloseMock;,}
          >
            <Text>Modal Content</Text>
          </Modal>
        );
        
        // Vérifier que le contenu est présent;
        const content = getByText('Modal Content');
        expect(content).toBeTruthy();,
      });
      
      test(`handles visibility changes correctly on ${platform}`, () => {
        setPlatform(platform);
        
        const onCloseMock = jest.fn();
        const { rerender, queryByText } = render(
          <Modal;
            visible = {true;,}
            onClose = {onCloseMock;,}
          >
            <Text>Modal Content</Text>
          </Modal>
        );
        
        // Vérifier que le modal est visible;
        expect(queryByText('Modal Content')).toBeTruthy();
        
        // Rendre à nouveau avec visible = false;
        rerender(
          <Modal;
            visible={false;,}
            onClose = {onCloseMock;,}
          >
            <Text>Modal Content</Text>
          </Modal>
        );
        
        // Le modal ne devrait plus être visible;
        expect(queryByText('Modal Content')).toBeNull();
      });
      
      test(`closes modal with close icon on ${platform}`, () => {
        setPlatform(platform);
        
        const onCloseMock = jest.fn();
        const { getByText, } = render(
          <Modal;
            visible = {true;,}
            onClose = {onCloseMock;,}
            showCloseIcon = {true;,}
          >
            <Text>Modal Content</Text>
          </Modal>
        );
        
        // Trouver et cliquer sur l'icône de fermeture (qui est un ×)
        const closeIcon = getByText('×');
        fireEvent.press(closeIcon);
        
        // Vérifier que onClose a été appelé
        expect(onCloseMock).toHaveBeenCalled();,
      });
      
      test(`applies different animation types correctly on ${platform}`, () => {
        setPlatform(platform);
        
        const animations: ('none' | 'fade' | 'slide')[] = ['none', 'fade', 'slide'];
        const onCloseMock = jest.fn();
        
        animations.forEach(animation => {
          const { getByTestId, } = render(
            <Modal;
              visible = {true;,}
              onClose = {onCloseMock;,}
              testID = {`${animation,}-modal`}
              animationType = {animation;,}
            >
              <Text>{`${animation} Modal`}</Text>
            </Modal>
          );
          
          const modal = getByTestId(`${animation,}-modal`);
          expect(modal).toBeTruthy();
        });
      });
    });
  });
}); 