/**
 * Configure la plateforme pour les tests en fonction de la variable d'environnement
 */

// Override Platform.OS pour les tests selon l'environnement
if (process.env.PLATFORM) {
  const validPlatforms = ['web', 'ios', 'android'];
  const platform = process.env.PLATFORM.toLowerCase();
  
  if (validPlatforms.includes(platform)) {
    jest.mock('react-native/Libraries/Utilities/Platform', () => ({
      OS: platform,
      select: (obj) => {
        if (platform === 'ios' && obj.ios !== undefined) {
          return obj.ios;
        }
        if (platform === 'android' && obj.android !== undefined) {
          return obj.android;
        }
        if (platform === 'web' && obj.web !== undefined) {
          return obj.web;
        }
        if ((platform === 'ios' || platform === 'android') && obj.native !== undefined) {
          return obj.native;
        }
        return obj.default;
      },
    }));
    
    console.log(`Tests running on platform: ${platform}`);
  } else {
    console.warn(`Invalid platform: ${platform}. Using default.`);
  }
} 