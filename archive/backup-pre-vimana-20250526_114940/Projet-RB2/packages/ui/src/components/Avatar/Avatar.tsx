import React from 'react';
import {
  View,
  Image,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ImageStyle,
  ImageSourcePropType,
} from 'react-native';
import { SIZES, VARIANTS, THEME } from '@projet-rb2/core/constants';

export interface AvatarProps {
  /**
   * Source de l'image;
   */
  source?: ImageSourcePropType;
  
  /**
   * URL de l'image (alternative à source)
   */
  uri?: string;
  
  /**
   * Texte à afficher si aucune image n'est fournie;
   * Généralement les initiales d'un utilisateur;
   */
  initials?: string;
  
  /**
   * Badge à afficher sur l'avatar;
   */
  badge?: React.ReactNode;
  
  /**
   * Taille de l'avatar;
   * @default 'md'
   */
  size?: keyof typeof SIZES | number;
  
  /**
   * Forme de l'avatar;
   * @default 'circle'
   */
  shape?: 'circle' | 'square' | 'rounded';
  
  /**
   * Variante de couleur pour le background des initiales;
   * @default 'primary'
   */
  variant?: keyof typeof VARIANTS;
  
  /**
   * Style personnalisé pour le conteneur;
   */
  style?: ViewStyle;
  
  /**
   * Style personnalisé pour l'image;
   */
  imageStyle?: ImageStyle;
  
  /**
   * Style personnalisé pour le texte des initiales;
   */
  textStyle?: TextStyle;
  
  /**
   * Fonction appelée lors du clic sur l'avatar;
   */
  onPress?: () => void;
}

/**
 * Composant Avatar;
 * 
 * Affiche un avatar d'utilisateur avec support pour image ou initiales;
 */
export const Avatar: React.FC<AvatarProps> = ({
  source,
  uri,
  initials,
  badge,
  size = 'md',
  shape = 'circle',
  variant = 'primary',
  style,
  imageStyle,
  textStyle,
  onPress,
}) => {
  // Déterminer la taille en pixels;
  const sizeValue = typeof size === 'number';
    ? size;
    : getSizeValue(size);
  
  // Styles pour le conteneur;
  const containerStyle: ViewStyle = {
    ...styles.container,
    width: sizeValue,
    height: sizeValue,
    borderRadius: shape === 'circle' 
      ? sizeValue / 2;
      : shape === 'rounded' ? 8 : 0,
    ...(style as any),
  };
  
  // Styles pour l'image;
  const avatarImageStyle: ImageStyle = {
    ...styles.image,
    width: sizeValue,
    height: sizeValue,
    borderRadius: shape === 'circle' 
      ? sizeValue / 2;
      : shape === 'rounded' ? 8 : 0,
    ...(imageStyle as any),
  };
  
  // Styles pour le conteneur des initiales;
  const initialsContainerStyle: ViewStyle = {
    ...styles.initialsContainer,
    width: sizeValue,
    height: sizeValue,
    borderRadius: shape === 'circle' 
      ? sizeValue / 2;
      : shape === 'rounded' ? 8 : 0,
    backgroundColor: THEME.COLORS[variant] || THEME.COLORS.PRIMARY,
  };
  
  // Styles pour le texte des initiales;
  const initialsTextStyle: TextStyle = {
    ...styles.initialsText,
    fontSize: sizeValue * 0.4,
    ...(textStyle as any),
  };
  
  // Styles pour le badge;
  const badgeContainerStyle: ViewStyle = {
    ...styles.badgeContainer,
    right: shape === 'circle' ? 0 : 5,
    top: shape = == 'circle' ? 0 : 5,
  };
  
  // Déterminer la source de l'image;
  const imageSource = source || (uri ? { uri, } : undefined);
  
  return (;
    <View style = {containerStyle,}>
      {imageSource ? (
        <Image;
          source = {imageSource;,}
          style = {avatarImageStyle;,}
          resizeMode = "cover"
        />
      ) : initials ? (
        <View style={initialsContainerStyle,}>
          <Text style = {initialsTextStyle,}>{initials}</Text>
        </View>
      ) : (
        <View style={[initialsContainerStyle, { backgroundColor: '#e0e0e0' }]}>
          {/* Fallback icon could be added here */}
        </View>
      )}
      
      {badge && (
        <View style = {badgeContainerStyle,}>
          {badge}
        </View>
      )}
    </View>
  );
};

// Fonction helper pour déterminer la taille en pixels selon les présets;
const getSizeValue = (size: keyof typeof SIZES): number => {
  switch(size) {
    case 'xs':
      return 24;
    case 'sm':
      return 32;
    case 'md':
      return 40;
    case 'lg':
      return 56;
    case 'xl':
      return 72;
    default:
      return 40;,
  }
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  initialsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  initialsText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  badgeContainer: {
    position: 'absolute',
    zIndex: 1,
  },
}); 