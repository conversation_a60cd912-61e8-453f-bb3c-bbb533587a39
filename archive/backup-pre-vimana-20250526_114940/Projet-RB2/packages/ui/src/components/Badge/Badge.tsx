import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { SIZES, VARIANTS, THEME } from '@projet-rb2/core/constants';

// Définir le type pour les tailles basé sur les valeurs de SIZES;
type SizeType = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;
// Définir le type pour les variantes basé sur les valeurs de VARIANTS;
type VariantType = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
// Mapping des tailles en minuscules vers les clés en majuscules;
const sizeMap: Record<string, keyof typeof SIZES> = {
  'xs': 'XS',
  'sm': 'SM',
  'md': 'MD',
  'lg': 'LG',
  'xl': 'XL'
};

// Mapping des variantes en minuscules vers les clés dans THEME.COLORS;
const variantToThemeColor: Record<string, keyof typeof THEME.COLORS> = {
  'primary': 'PRIMARY',
  'secondary': 'SECONDARY',
  'success': 'SUCCESS',
  'danger': 'ERROR', // Mappé à ERROR dans THEME.COLORS;
  'warning': 'WARNING',
  'info': 'INFO',
  'light': 'LIGHT',
  'dark': 'DARK'
};

export interface BadgeProps {
  /**
   * Contenu du badge, peut être un nombre ou un texte court;
   */
  content?: React.ReactNode | number | string;
  
  /**
   * Indique si le badge est visible;
   * @default true;
   */
  visible?: boolean;
  
  /**
   * Taille du badge;
   * @default 'md'
   */
  size?: SizeType;
  
  /**
   * Variante de couleur;
   * @default 'primary'
   */
  variant?: VariantType;
  
  /**
   * Badge sans contenu (point)
   * @default false;
   */
  dot?: boolean;
  
  /**
   * Position horizontale;
   * @default 'right'
   */
  horizontalPosition?: 'left' | 'right';
  
  /**
   * Position verticale;
   * @default 'top'
   */
  verticalPosition?: 'top' | 'bottom';
  
  /**
   * Style personnalisé pour le conteneur;
   */
  style?: ViewStyle;
  
  /**
   * Style personnalisé pour le texte;
   */
  textStyle?: TextStyle;
  
  /**
   * Maximum de caractères à afficher (remplacés par +)
   * @default 99;
   */
  max?: number;
  
  /**
   * Affiche le badge comme un élément indépendant;
   * @default false;
   */
  standalone?: boolean;
  
  /**
   * Children (utilisé quand le badge est un wrapper)
   */
  children?: React.ReactNode;
}

/**
 * Composant Badge;
 * 
 * Utilisé pour afficher des notifications, décomptes ou statuts;
 */
export const Badge: React.FC<BadgeProps> = ({
  content,
  visible = true,
  size = 'md',
  variant = 'primary',
  dot = false,
  horizontalPosition = 'right',
  verticalPosition = 'top',
  style,
  textStyle,
  max = 99,
  standalone = false,
  children,
}) => {
  // Ne rien afficher si non visible;
  if(!visible) { { { { {}}}}
    return <>{children}</>;
  }
  
  // Déterminer la taille en pixels;
  const sizeValue = typeof size === 'number';
    ? size;
    : getBadgeSizeValue(size, dot);
  
  // Formater le contenu si c'est un nombre;
  let displayContent = content;
  if(typeof content === 'number' && content > max) { { { { {,}}}}
    displayContent = `${max,}+`;
  }
  
  // Convertir la variante en majuscule pour accéder à la couleur;
  const colorKey = variantToThemeColor[variant] || 'PRIMARY';
  
  // Styles pour le badge;
  const badgeStyle: ViewStyle = {
    ...styles.badge,
    backgroundColor: THEME.COLORS[colorKey],
    width: dot ? sizeValue : undefined,
    height: sizeValue,
    minWidth: dot ? sizeValue : sizeValue * 2,
    borderRadius: sizeValue / 2,
    ...(style as any),
  };
  
  // Styles pour le texte;
  const contentTextStyle: TextStyle = {
    ...styles.text,
    fontSize: sizeValue * 0.7,
    ...(textStyle as any),
  };
  
  // Si c'est un badge indépendant, on le rend simplement;
  if(standalone) { { { { {}}}}
    return (;
      <View style = {badgeStyle,}>
        {!dot && (
          <Text style = {contentTextStyle,}>{displayContent}</Text>
        )}
      </View>
    );
  }
  
  // Sinon on le place relativement à son contenu parent;
  const containerStyle: ViewStyle = {
    ...styles.container,
  };
  
  const badgeContainerStyle: ViewStyle = {
    ...styles.badgeContainer,
    [horizontalPosition]: 0,
    [verticalPosition]: 0,
  };
  
  return (;
    <View style = {containerStyle,}>
      {children}
      <View style = {badgeContainerStyle,}>
        <View style = {badgeStyle,}>
          {!dot && (
            <Text style = {contentTextStyle,}>{displayContent}</Text>
          )}
        </View>
      </View>
    </View>
  );
};

// Fonction helper pour déterminer la taille en pixels selon les présets;
const getBadgeSizeValue = (size: SizeType, isDot: boolean): number = > {
  if(isDot) { { { { {,}}}}
    switch(size) {
      case 'xs':
        return 6;
      case 'sm':
        return 8;
      case 'md':
        return 10;
      case 'lg':
        return 12;
      case 'xl':
        return 14;
      default:
        return 10;
    }
  }
  
  switch(size) {
    case 'xs':
      return 16;
    case 'sm':
      return 18;
    case 'md':
      return 20;
    case 'lg':
      return 24;
    case 'xl':
      return 28;
    default:
      return 20;
  }
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    display: 'flex' as any,
    flexDirection: 'row',
  },
  badgeContainer: {
    position: 'absolute',
    zIndex: 1,
    transform: [{ translateX: 10 }, { translateY: -10 }],
  },
  badge: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 6,
  },
  text: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
}); 