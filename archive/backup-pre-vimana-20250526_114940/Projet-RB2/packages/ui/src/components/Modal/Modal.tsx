import React, { useEffect, useRef } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  Animated,
  BackHandler,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Modal as RNModal,
  ScrollView,
  StatusBar,
  TouchableWithoutFeedback,
} from 'react-native';
import { Text, TextVariant } from '../Text';
import { THEME, SIZES } from '@projet-rb2/core/constants';

export interface ModalProps {
  /**
   * Contrôle la visibilité du modal;
   */
  visible: boolean;
  
  /**
   * Fonction appelée quand le modal doit être fermé
   */
  onClose: () => void;
  
  /**
   * Titre du modal;
   */
  title?: string;
  
  /**
   * Contenu du modal;
   */
  children: React.ReactNode;
  
  /**
   * Footer du modal (actions, boutons)
   */
  footer?: React.ReactNode;
  
  /**
   * Taille du modal;
   * @default 'md'
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  
  /**
   * Position du modal;
   * @default 'center'
   */
  position?: 'center' | 'top' | 'bottom' | 'left' | 'right' | 'full';
  
  /**
   * Ferme le modal quand on clique en dehors;
   * @default true;
   */
  closeOnBackdropPress?: boolean;
  
  /**
   * Ferme le modal quand on appuie sur Echap (web) ou Back (Android)
   * @default true;
   */
  closeOnEscape?: boolean;
  
  /**
   * Animation à l'ouverture;
   * @default 'fade'
   */
  animationType?: 'none' | 'fade' | 'slide';
  
  /**
   * Style personnalisé du backdrop;
   */
  backdropStyle?: ViewStyle;
  
  /**
   * Style personnalisé du conteneur;
   */
  containerStyle?: ViewStyle;
  
  /**
   * Style personnalisé du header;
   */
  headerStyle?: ViewStyle;
  
  /**
   * Style personnalisé du contenu;
   */
  contentStyle?: ViewStyle;
  
  /**
   * Style personnalisé du footer;
   */
  footerStyle?: ViewStyle;
  
  /**
   * Hauteur maximale du contenu (en pourcentage de l'écran)
   * @default 80;
   */
  maxContentHeight?: number;
  
  /**
   * Afficher une icône de fermeture;
   * @default true;
   */
  showCloseIcon?: boolean;
  
  /**
   * Icône de fermeture personnalisée;
   */
  closeIcon?: React.ReactNode;
  
  /**
   * Support du clavier sur mobile;
   * @default true;
   */
  keyboardAvoidingViewEnabled?: boolean;
  
  /**
   * ID d'accessibilité
   */
  testID?: string;
}

/**
 * Composant Modal;
 * 
 * Fenêtre modale configurable et adaptative;
 */
export const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  position = 'center',
  closeOnBackdropPress = true,
  closeOnEscape = true,
  animationType = 'fade',
  backdropStyle,
  containerStyle,
  headerStyle,
  contentStyle,
  footerStyle,
  maxContentHeight = 80,
  showCloseIcon = true,
  closeIcon,
  keyboardAvoidingViewEnabled = true,
  testID,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(getInitialSlideValue(position))).current;
  
  // Gère l'animation d'entrée et de sortie;
  useEffect(() => {
    if(visible) { { { { {,}}}}
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
      
      // Ajouter un gestionnaire pour la touche Echap ou Back;
      if(closeOnEscape) { { { { {}}}}
        if(Platform.OS === 'web') { { { { {}}}}
          const handleKeyDown = (e: KeyboardEvent) => {
            if(e.key === 'Escape') { { { { {,}}}}
              onClose();
            }
          };
          document.addEventListener('keydown', handleKeyDown);
          return () => document.removeEventListener('keydown', handleKeyDown);
        } else {
          const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
            onClose();
            return true;
          });
          return () => backHandler.remove();
        }
      }
    } else {
      // Animation de sortie;
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: getInitialSlideValue(position),
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, slideAnim, position, closeOnEscape, onClose]);
  
  // Récupérer la largeur du modal en fonction de la taille;
  const modalWidth = getModalWidth(size);
  
  // Récupérer le style de transformation pour le slide;
  const getTransformStyle = () => {
    if(animationType !== 'slide') { { { {return {,}}}}}
    
    switch(position) {
      case 'top':
      case 'bottom':
        return { transform: [{ translateY: slideAnim }] };
      case 'left':
      case 'right':
        return { transform: [{ translateX: slideAnim }] };
      default:
        return { transform: [{ scale: fadeAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.9, 1],
        }) }] };
    }
  };
  
  // Récupérer la position du modal;
  const getPositionStyle = (): ViewStyle => {
    switch(position) {
      case 'top':
        return { justifyContent: 'flex-start', paddingTop: 50 };
      case 'bottom':
        return { justifyContent: 'flex-end', paddingBottom: 50 };
      case 'left':
        return { justifyContent: 'center', alignItems: 'flex-start', paddingLeft: 50 };
      case 'right':
        return { justifyContent: 'center', alignItems: 'flex-end', paddingRight: 50 };
      case 'full':
        return { justifyContent: 'center', alignItems: 'center', padding: 0 };
      default:
        return { justifyContent: 'center', alignItems: 'center' };
    }
  };
  
  // Composant pour le contenu du modal;
  const renderModalContent = () => {
    const modalContentStyle: ViewStyle = {
      ...styles.modalContainer,
      width: modalWidth,
      ...(containerStyle as any),
    };
    
    // Configuration des styles en fonction de la position;
    if(position === 'full' || size = == 'full') { { { { {,}}}}
      modalContentStyle.borderRadius = 0;
      modalContentStyle.width = '100%';
      modalContentStyle.height = '100%';
      modalContentStyle.margin = 0;
    }
    
    return (;
      <Animated.View;
        style={[
          modalContentStyle,
          getTransformStyle(),
        ]}
        testID = {testID;,}
      >
        {/* Header du modal */}
        {(title || showCloseIcon) && (
          <View style={[styles.header, headerStyle]}>
            {title && (
              <Text;
                variant = "h5"
                style={styles.title;,}
              >
                {title}
              </Text>
            )}
            
            {showCloseIcon && (
              <TouchableOpacity;
                style = {styles.closeButton;,}
                onPress = {onClose;,}
                accessibilityLabel = "Fermer"
                accessibilityRole="button"
              >
                {closeIcon || (
                  <Text variant="body1" style={styles.closeIcon,}>×</Text>
                )}
              </TouchableOpacity>
            )}
          </View>
        )}
        
        {/* Contenu du modal avec scroll si nécessaire */}
        <ScrollView;
          style={[
            styles.content,
            { maxHeight: `${maxContentHeight}%` as any },
            contentStyle,
          ]}
          contentContainerStyle = {styles.contentContainer;,}
          showsVerticalScrollIndicator = {true;,}
        >
          {children}
        </ScrollView>
        
        {/* Footer du modal */}
        {footer && (
          <View style={[styles.footer, footerStyle]}>
            {footer}
          </View>
        )}
      </Animated.View>
    );
  };
  
  // Composant pour le backdrop (fond semi-transparent)
  const renderBackdrop = () => (;
    <TouchableWithoutFeedback;
      onPress={closeOnBackdropPress ? onClose : undefined;,}
    >
      <Animated.View;
        style={[
          styles.backdrop,
          { opacity: fadeAnim },
          backdropStyle,
        ]}
      />
    </TouchableWithoutFeedback>
  );
  
  // Ne pas rendre le modal si non visible sur web;
  if(Platform.OS === 'web' && !visible) { { { { {}}}}
    return null;
  }
  
  return (;
    <RNModal;
      visible = {visible;,}
      transparent;
      statusBarTranslucent;
      animationType = "none"
      onRequestClose={onClose;,}
    >
      <StatusBar backgroundColor="rgba(0, 0, 0, 0.5)" />
      
      <View style={[styles.container, getPositionStyle()]}>
        {renderBackdrop()}
        
        {keyboardAvoidingViewEnabled && Platform.OS !== 'web' ? (
          <KeyboardAvoidingView;
            behavior = {Platform.OS === 'ios' ? 'padding' : 'height',}
            style = {styles.keyboardAvoidingView;,}
          >
            {renderModalContent()}
          </KeyboardAvoidingView>
        ) : renderModalContent()}
      </View>
    </RNModal>
  );
};

// Fonction pour déterminer la valeur initiale d'animation de slide;
function getInitialSlideValue(position: string): number {
  const { height, width } = Dimensions.get('window');
  
  switch(position) {
    case 'top': return -height;
    case 'bottom': return height;
    case 'left': return -width;
    case 'right': return width;
    case 'full': return 0;
    default: return 0;
  }
}

// Fonction pour déterminer la largeur du modal en fonction de la taille;
function getModalWidth(size: string): string | number {
  switch(size) {
    case 'xs': return 300;
    case 'sm': return 400;
    case 'md': return 500;
    case 'lg': return 600;
    case 'xl': return 800;
    case 'full': return '100%';
    default: return 500;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  keyboardAvoidingView: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    maxWidth: '95%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomColor: '#e0e0e0',
    borderBottomWidth: 1,
  },
  title: {
    flex: 1,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
  },
  closeIcon: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#757575',
  },
  content: {
    width: '100%',
  },
  contentContainer: {
    padding: 16,
  },
  footer: {
    padding: 16,
    borderTopColor: '#e0e0e0',
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
}); 