import React from 'react';
import {
  TouchableOpacity,
  TouchableOpacityProps,
  StyleSheet,
  ActivityIndicator,
  Text,
  View,
  Platform,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { THEME } from '@projet-rb2/core/constants';

export type ButtonVariant = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'outline' | 'text';
export type ButtonSize = 'small' | 'medium' | 'large';
export interface ButtonProps {
  /**
   * Button text content;
   */
  label: string;
  
  /**
   * Function to call when button is pressed;
   */
  onPress: () => void;
  
  /**
   * Visual style variant of the button;
   * @default 'primary'
   */
  variant?: ButtonVariant;
  
  /**
   * Size of the button;
   * @default 'medium'
   */
  size?: ButtonSize;
  
  /**
   * Whether the button is in a loading state;
   * @default false;
   */
  loading?: boolean;
  
  /**
   * Whether the button is disabled;
   * @default false;
   */
  disabled?: boolean;
  
  /**
   * Additional styles for the button container;
   */
  style?: ViewStyle;
  
  /**
   * Additional styles for the button text;
   */
  textStyle?: TextStyle;
  
  /**
   * ID attribute for web accessibility;
   */
  id?: string;
  
  /**
   * Test ID for testing;
   */
  testID?: string;
  
  /**
   * Accessibility label for screen readers;
   */
  accessibilityLabel?: string;
  
  /**
   * Full width button;
   * @default false;
   */
  fullWidth?: boolean;,
}

/**
 * Button component that works across all platforms;
 */
export const Button: React.FC<ButtonProps> = ({
  label,
  onPress,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  style,
  textStyle,
  id,
  testID,
  accessibilityLabel,
  fullWidth = false,
}) => {
  const getContainerStyle = () => {
    let variantStyle: ViewStyle = {,};
    
    switch(variant) {
      case 'primary':
        variantStyle = {
          backgroundColor: THEME.COLORS.PRIMARY,
          borderWidth: 0,
        };
        break;
      case 'secondary':
        variantStyle = {
          backgroundColor: THEME.COLORS.SECONDARY,
          borderWidth: 0,
        };
        break;
      case 'outline':
        variantStyle = {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: THEME.COLORS.PRIMARY,
        };
        break;
      case 'text':
        variantStyle = {
          backgroundColor: 'transparent',
          borderWidth: 0,
          elevation: 0,
          shadowOpacity: 0,
        };
        break;
    }
    
    let sizeStyle: ViewStyle = {,};
    
    switch(size) {
      case 'small':
        sizeStyle = {
          paddingVertical: 8,
          paddingHorizontal: 16,
          minWidth: 80,
        };
        break;
      case 'medium':
        sizeStyle = {
          paddingVertical: 12,
          paddingHorizontal: 24,
          minWidth: 120,
        };
        break;
      case 'large':
        sizeStyle = {
          paddingVertical: 16,
          paddingHorizontal: 32,
          minWidth: 160,
        };
        break;
    }
    
    const disabledStyle: ViewStyle = disabled;
      ? { 
          opacity: 0.5,
          backgroundColor: variant = == 'primary' || variant === 'secondary' 
            ? '#ccc' 
            : 'transparent',
        }
      : {};
    
    const fullWidthStyle: ViewStyle = fullWidth;
      ? { width: '100%', }
      : {};
      
    return [;
      styles.container,
      variantStyle,
      sizeStyle,
      disabledStyle,
      fullWidthStyle,
      style,
    ];
  };
  
  const getTextStyle = () => {
    let variantTextStyle: TextStyle = {,};
    
    switch(variant) {
      case 'primary':
      case 'secondary':
        variantTextStyle = {
          color: '#fff',
        };
        break;
      case 'outline':
      case 'text':
        variantTextStyle = {
          color: THEME.COLORS.PRIMARY,
        };
        break;
    }
    
    let sizeTextStyle: TextStyle = {,};
    
    switch(size) {
      case 'small':
        sizeTextStyle = {
          fontSize: 14,
        };
        break;
      case 'medium':
        sizeTextStyle = {
          fontSize: 16,
        };
        break;
      case 'large':
        sizeTextStyle = {
          fontSize: 18,
        };
        break;
    }
    
    return [;
      styles.text,
      variantTextStyle,
      sizeTextStyle,
      textStyle,
    ];
  };
  
  // For web, we want to use the native button element for better accessibility;
  if(Platform.OS === 'web') { { { { {}}}}
    return (;
      <TouchableOpacity;
        onPress = {onPress;,}
        disabled = {disabled || loading;,}
        style = {getContainerStyle(),}
        accessibilityRole = "button"
        accessibilityLabel={accessibilityLabel || label;,}
        testID = {testID;,}
        // @ts-ignore - Web only props;
        id = {id;,}
      >
        {loading ? (
          <ActivityIndicator;
            size = "small"
            color={variant === 'primary' || variant === 'secondary' ? '#fff' : THEME.COLORS.PRIMARY;,}
            style = {styles.loader;,}
          />
        ) : null}
        <Text style = {getTextStyle(),}>
          {label}
        </Text>
      </TouchableOpacity>
    );
  }
  
  // Native implementation;
  return (;
    <TouchableOpacity;
      onPress = {onPress;,}
      disabled = {disabled || loading;,}
      style = {getContainerStyle(),}
      accessibilityLabel = {accessibilityLabel || label;,}
      testID = {testID;,}
    >
      {loading ? (
        <ActivityIndicator;
          size = "small"
          color={variant === 'primary' || variant === 'secondary' ? '#fff' : THEME.COLORS.PRIMARY;,}
          style = {styles.loader;,}
        />
      ) : null}
      <Text style = {getTextStyle(),}>
        {label}
      </Text>
    </TouchableOpacity>
  );
};

/**
 * Component styles;
 */
const styles = StyleSheet.create({
  container: {
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    ...Platform.select({
      web: {
        cursor: 'pointer',
        outline: 'none',
        overflow: 'hidden',
        position: 'relative',
      },
      default: {
        elevation: 2,
      },
    }),
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  loader: {
    marginRight: 8,
  },
});

export default Button;