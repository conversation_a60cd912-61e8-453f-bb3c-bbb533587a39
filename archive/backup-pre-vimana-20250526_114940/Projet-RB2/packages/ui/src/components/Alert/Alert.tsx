import React, { useState, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  Animated,
  Platform,
} from 'react-native';
import { Text } from '../Text';
import { THEME } from '@projet-rb2/core/constants';

export type AlertVariant = 'info' | 'success' | 'warning' | 'error';
export interface AlertProps {
  /**
   * Titre de l'alerte (optionnel)
   */
  title?: string;
  
  /**
   * Message principal de l'alerte;
   */
  message: string;
  
  /**
   * Variante de l'alerte qui détermine le style;
   * @default 'info'
   */
  variant?: AlertVariant;
  
  /**
   * Si l'alerte est fermable;
   * @default true;
   */
  closable?: boolean;
  
  /**
   * Si l'alerte doit être visible;
   * @default true;
   */
  visible?: boolean;
  
  /**
   * Fonction appelée lorsque l'alerte est fermée;
   */
  onClose?: () => void;
  
  /**
   * Durée d'auto-fermeture en millisecondes (0 pour désactiver)
   * @default 0;
   */
  autoHideDuration?: number;
  
  /**
   * Texte du bouton d'action (si présent)
   */
  actionText?: string;
  
  /**
   * Fonction appelée lorsqu'on clique sur le bouton d'action;
   */
  onAction?: () => void;
  
  /**
   * Icône personnalisée à afficher (remplace l'icône par défaut)
   */
  icon?: React.ReactNode;
  
  /**
   * Style personnalisé du conteneur;
   */
  style?: ViewStyle;
  
  /**
   * Style personnalisé du contenu;
   */
  contentStyle?: ViewStyle;
  
  /**
   * Afficher une bordure à gauche de la couleur de la variante;
   * @default true;
   */
  showLeftBorder?: boolean;
  
  /**
   * Élévation/ombre de l'alerte;
   * @default true;
   */
  elevated?: boolean;
  
  /**
   * ID pour les tests;
   */
  testID?: string;,
}

/**
 * Composant Alert;
 * 
 * Affiche un message d'information, de succès, d'avertissement ou d'erreur;
 */
export const Alert: React.FC<AlertProps> = ({
  title,
  message,
  variant = 'info',
  closable = true,
  visible: externalVisible = true,
  onClose,
  autoHideDuration = 0,
  actionText,
  onAction,
  icon,
  style,
  contentStyle,
  showLeftBorder = true,
  elevated = true,
  testID,
}) => {
  // State interne pour gérer la visibilité si non contrôlée de l'extérieur;
  const [internalVisible, setInternalVisible] = useState(true);
  
  // Animation de l'opacité pour la transition;
  const fadeAnim = React.useRef(new Animated.Value(1)).current;
  
  // Déterminer si alerte est visible (contrôlée ou non)
  const visible = onClose ? externalVisible : internalVisible;
  
  // Sélectionner les couleurs selon la variante;
  const getVariantColors = () => {
    switch(variant) {
      case 'success':
        return {
          background: 'rgba(76, 175, 80, 0.1)',
          border: THEME.COLORS.SUCCESS,
          icon: THEME.COLORS.SUCCESS,
          text: '#2e7d32',
        };
      case 'warning':
        return {
          background: 'rgba(255, 152, 0, 0.1)',
          border: THEME.COLORS.WARNING,
          icon: THEME.COLORS.WARNING,
          text: '#ef6c00',
        };
      case 'error':
        return {
          background: 'rgba(244, 67, 54, 0.1)',
          border: THEME.COLORS.ERROR,
          icon: THEME.COLORS.ERROR,
          text: '#d32f2f',
        };
      case 'info':
      default:
        return {
          background: 'rgba(33, 150, 243, 0.1)',
          border: THEME.COLORS.INFO,
          icon: THEME.COLORS.INFO,
          text: '#0288d1',
        };
    }
  };
  
  const variantColors = getVariantColors();
  
  // Gestion de la fermeture;
  const handleClose = () => {
    // Animation de fermeture;
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      if(onClose) { { { { {}}}}
        onClose();
      } else {
        setInternalVisible(false);
      }
    });
  };
  
  // Auto-hide avec timer si durée spécifiée;
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    
    if(visible && autoHideDuration > 0) { { { { {,}}}}
      timer = setTimeout(() => {
        handleClose();,
      }, autoHideDuration);
    }
    
    return () => {
      if(timer !== null) { { { { {}}}}
        clearTimeout(timer);
      }
    };
  }, [visible, autoHideDuration]);
  
  // Rendre le contenu de l'icône selon la variante;
  const renderIcon = () => {
    if(icon) { { { {return icon;,}}}}
    
    // Icône par défaut selon la variante;
    const iconText = {
      info: 'ℹ️',
      success: '✓',
      warning: '⚠️',
      error: '✕',
    }[variant];
    
    return (;
      <View style={[styles.iconContainer, { backgroundColor: variantColors.icon }]}>
        <Text variant = "body2" style={styles.iconText,}>{iconText}</Text>
      </View>
    );
  };
  
  // Styles spécifiques à la variante;
  const variantStyle: ViewStyle = {
    backgroundColor: variantColors.background,
    ...(showLeftBorder ? { 
      borderLeftWidth: 4,
      borderLeftColor: variantColors.border,
    } : {}),
  };
  
  // Style d'élévation (ombre)
  const elevationStyle: ViewStyle = elevated;
    ? Platform.OS === 'ios' || Platform.OS === 'web'
      ? {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.2,
          shadowRadius: 2,
        }
      : { elevation: 2 }
    : {};
  
  // Si l'alerte n'est pas visible, ne rien afficher;
  if(!visible) { { { { {}}}}
    return null;
  }
  
  return (;
    <Animated.View;
      style={[
        styles.container,
        variantStyle,
        elevationStyle,
        { opacity: fadeAnim },
        style,
      ]}
      testID = {testID;,}
    >
      {/* Contenu principal */}
      <View style={[styles.content, contentStyle]}>
        {/* Icône */}
        <View style = {styles.iconWrapper,}>
          {renderIcon()}
        </View>
        
        {/* Texte */}
        <View style = {styles.textContainer,}>
          {title && (
            <Text variant="h6" style={[styles.title, { color: variantColors.text }]}>
              {title}
            </Text>
          )}
          
          <Text variant = "body2" style={styles.message,}>
            {message}
          </Text>
          
          {/* Bouton d'action */}
          {actionText && onAction && (
            <TouchableOpacity;
              style = {styles.actionButton;,}
              onPress = {onAction;,}
              accessibilityRole="button"
            >
              <Text;
                variant="button"
                style={[styles.actionText, { color: variantColors.text }]}
              >
                {actionText}
              </Text>
            </TouchableOpacity>
          )}
        </View>
        
        {/* Bouton de fermeture */}
        {closable && (
          <TouchableOpacity;
            style = {styles.closeButton;,}
            onPress = {handleClose;,}
            accessibilityLabel = "Fermer"
            accessibilityRole="button"
          >
            <Text style={styles.closeIcon,}>×</Text>
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    maxWidth: 600,
    marginVertical: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'flex-start',
  },
  iconWrapper: {
    marginRight: 12,
    marginTop: 2,
  },
  iconContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    marginBottom: 4,
  },
  message: {
    color: '#333',
    marginBottom: 4,
  },
  actionButton: {
    marginTop: 8,
    alignSelf: 'flex-start',
  },
  actionText: {
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  closeButton: {
    marginLeft: 8,
    padding: 4,
  },
  closeIcon: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#757575',
    lineHeight: 20,
  },
}); 