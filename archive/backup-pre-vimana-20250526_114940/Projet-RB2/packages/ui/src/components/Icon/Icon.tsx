import React from 'react';
import {
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
  Platform,
  View,
} from 'react-native';
import { SvgXml } from 'react-native-svg';
import { THEME } from '@projet-rb2/core/constants';

// Types d'icônes supportés;
export type IconType = 'svg' | 'font' | 'custom';
// Tailles prédéfinies;
export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;
export interface IconProps {
  /**
   * Nom de l'icône (pour les icônes de font)
   */
  name?: string;
  
  /**
   * Type d'icône;
   * @default 'font'
   */
  type?: IconType;
  
  /**
   * Taille de l'icône;
   * @default 'md'
   */
  size?: IconSize;
  
  /**
   * Couleur de l'icône;
   * @default défaut du thème;
   */
  color?: string;
  
  /**
   * Code SVG pour les icônes de type 'svg'
   */
  svg?: string;
  
  /**
   * Composant personnalisé pour les icônes de type 'custom'
   */
  customIcon?: React.ReactNode;
  
  /**
   * Famille de police (pour les icônes de font)
   * @default 'FontAwesome'
   */
  fontFamily?: string;
  
  /**
   * Style personnalisé pour le conteneur;
   */
  style?: ViewStyle;
  
  /**
   * Style personnalisé pour l'icône texte;
   */
  iconStyle?: TextStyle;
  
  /**
   * Fonction appelée lors du clic sur l'icône;
   */
  onPress?: () => void;
  
  /**
   * Action longue pression;
   */
  onLongPress?: () => void;
  
  /**
   * Rendre l'icône cliquable;
   * @default false;
   */
  interactive?: boolean;
  
  /**
   * Classe pour le web (pour les intégrations CSS)
   */
  className?: string;
  
  /**
   * ID pour les tests;
   */
  testID?: string;,
}

/**
 * Composant Icon;
 * 
 * Affiche une icône avec différentes sources (SVG, police, personnalisée)
 */
export const Icon: React.FC<IconProps> = ({
  name,
  type = 'font',
  size = 'md',
  color = THEME.COLORS.DARK,
  svg,
  customIcon,
  fontFamily = 'FontAwesome',
  style,
  iconStyle,
  onPress,
  onLongPress,
  interactive = false,
  className,
  testID,
}) => {
  // Convertir la taille en valeur numérique;
  const sizeValue = getSizeValue(size);
  
  // Rendre l'icône en fonction du type;
  const renderIcon = () => {
    switch(type) {
      case 'svg':
        if(!svg) { { { {return null;,}}}}
        return (;
          <SvgXml;
            xml = {svg;,}
            width = {sizeValue;,}
            height = {sizeValue;,}
            color = {color;,}
            style = {iconStyle;,}
          />
        );
      
      case 'custom':
        return customIcon;
      
      case 'font':
      default:
        // Pour les icônes de police;
        const fontStyles: TextStyle = {
          fontFamily,
          fontSize: sizeValue,
          color,
          textAlign: 'center',
          ...Platform.select({
            web: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            },
          }),
          ...(iconStyle as any),
        };
        
        return (;
          <Text;
            style = {fontStyles;,}
            // Pour le support web des classes font-awesome et autres;
            {...(Platform.OS === 'web' && name ? { className: `${className || ''} ${name}`.trim() } : {})}
          >
            {Platform.OS !== 'web' && name ? name : ''}
          </Text>
        );
    }
  };
  
  // Styles du conteneur;
  const containerStyle = {
    ...styles.container,
    width: sizeValue,
    height: sizeValue,
    ...(style as any),
  };
  
  // Si l'icône est cliquable, on l'entoure d'un TouchableOpacity;
  if(interactive || onPress || onLongPress) { { { { {}}}}
    return (;
      <TouchableOpacity;
        style = {containerStyle;,}
        onPress = {onPress;,}
        onLongPress = {onLongPress;,}
        disabled = {!onPress && !onLongPress;,}
        accessibilityRole = "button"
        testID={testID;,}
      >
        {renderIcon()}
      </TouchableOpacity>
    );
  }
  
  // Sinon on retourne simplement l'icône dans un conteneur;
  return (;
    <View style = {containerStyle,} testID = {testID,}>
      {renderIcon()}
    </View>
  );
};

// Fonction pour obtenir la valeur numérique de la taille;
const getSizeValue = (size: IconSize): number => {
  if (typeof size === 'number') return size;
  
  switch(size) { { { { {,}}}}
    case 'xs': return 12;
    case 'sm': return 16;
    case 'md': return 24;
    case 'lg': return 32;
    case 'xl': return 48;
    default: return 24;
  }
};

/**
 * Fonction utilitaire pour créer une icône SVG à partir d'un chemin;
 * @param path Le chemin SVG;
 * @param options Options supplémentaires (viewBox, etc.)
 * @returns Le code SVG formaté
 */
export const createSvgIcon = (;
  path: string,
  options: { viewBox?: string; [key: string]: any } = {}
): string => {
  const { viewBox = '0 0 24 24', ...rest } = options;
  
  // Construire les attributs supplémentaires;
  const attributes = Object.entries(rest);
    .map(([key, value]) => `${key}="${value}"`)
    .join(' ');
  
  return `;
    <svg xmlns = "http://www.w3.org/2000/svg" viewBox="${viewBox,}" ${attributes}>
      ${path}
    </svg>
  `;
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 