import React from 'react';
import { Text as RNText, StyleSheet, Platform, TextStyle } from 'react-native';
import { THEME } from '@projet-rb2/core/constants';

export type TextVariant = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'button' | 'overline';
export interface TextProps {
  /**
   * The text content to display
   */
  children: React.ReactNode;
  
  /**
   * Text variant that controls the size and style
   * @default 'body1'
   */
  variant?: TextVariant;
  
  /**
   * Font weight of the text
   */
  weight?: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
  
  /**
   * Color of the text
   */
  color?: string;
  
  /**
   * Text alignment
   */
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
  
  /**
   * Whether text should be in italics
   * @default false
   */
  italic?: boolean;
  
  /**
   * Whether text should be underlined
   * @default false
   */
  underline?: boolean;
  
  /**
   * Whether text should have a strike through
   * @default false
   */
  strikethrough?: boolean;
  
  /**
   * Maximum number of lines to show
   */
  numberOfLines?: number;
  
  /**
   * Selectable text (web only)
   * @default false
   */
  selectable?: boolean;
  
  /**
   * Additional styles for the text
   */
  style?: TextStyle | TextStyle[];
  
  /**
   * Test ID for testing
   */
  testID?: string;
  
  /**
   * Accessibility label for screen readers
   */
  accessibilityLabel?: string;
  
  /**
   * ID attribute for web
   */
  id?: string;
}

/**
 * Text component that works across all platforms
 */
export const Text: React.FC<TextProps> = ({
  children,
  variant = 'body1',
  weight,
  color,
  align,
  italic = false,
  underline = false,
  strikethrough = false,
  numberOfLines,
  selectable = false,
  style,
  testID,
  accessibilityLabel,
  id,
}) => {
  const getTextStyle = () => {
    const variantStyle = getVariantStyle(variant);
    
    const customStyles: TextStyle = {
      ...(weight && { fontWeight: weight }),
      ...(color && { color }),
      ...(align && { textAlign: align }),
      ...(italic && { fontStyle: 'italic' }),
      ...(underline && { textDecorationLine: 'underline' }),
      ...(strikethrough && { textDecorationLine: 'line-through' }),
      ...(underline && strikethrough && { textDecorationLine: 'underline line-through' }),
    };
    
    return [
      styles.text,
      variantStyle,
      customStyles,
      style,
    ];
  };
  
  const webProps = Platform.OS === 'web'
    ? { 
        selectable, 
        // @ts-ignore - Web only prop
        id,
      } 
    : {};
    
  return (
    <RNText
      style={getTextStyle()}
      numberOfLines={numberOfLines}
      testID={testID}
      accessibilityLabel={accessibilityLabel}
      {...webProps}
    >
      {children}
    </RNText>
  );
};

// Helper function to get style based on variant
const getVariantStyle = (variant: TextVariant): TextStyle => {
  switch(variant) {
    case 'h1':
      return {
        fontSize: 32,
        fontWeight: 'bold',
        lineHeight: 40,
        marginBottom: 16,
      };
    case 'h2':
      return {
        fontSize: 28,
        fontWeight: 'bold',
        lineHeight: 36,
        marginBottom: 16,
      };
    case 'h3':
      return {
        fontSize: 24,
        fontWeight: 'bold',
        lineHeight: 32,
        marginBottom: 8,
      };
    case 'h4':
      return {
        fontSize: 20,
        fontWeight: 'bold',
        lineHeight: 28,
        marginBottom: 8,
      };
    case 'h5':
      return {
        fontSize: 18,
        fontWeight: '600',
        lineHeight: 24,
        marginBottom: 8,
      };
    case 'h6':
      return {
        fontSize: 16,
        fontWeight: '600',
        lineHeight: 22,
        marginBottom: 8,
      };
    case 'body1':
      return {
        fontSize: 16,
        lineHeight: 24,
      };
    case 'body2':
      return {
        fontSize: 14,
        lineHeight: 20,
      };
    case 'button':
      return {
        fontSize: 14,
        fontWeight: '600',
        letterSpacing: 0.5,
        textTransform: 'uppercase' as TextStyle['textTransform'],
      };
    case 'caption':
      return {
        fontSize: 12,
        lineHeight: 16,
      };
    case 'overline':
      return {
        fontSize: 10,
        fontWeight: '500',
        letterSpacing: 1.5,
        textTransform: 'uppercase' as TextStyle['textTransform'],
      };
    default:
      return {};
  }
};

const styles = StyleSheet.create({
  text: {
    color: THEME.COLORS.DARK,
    ...Platform.select({
      web: {
        WebkitFontSmoothing: 'antialiased' as any,
        MozOsxFontSmoothing: 'grayscale' as any,
      },
    }),
  },
});

export default Text;