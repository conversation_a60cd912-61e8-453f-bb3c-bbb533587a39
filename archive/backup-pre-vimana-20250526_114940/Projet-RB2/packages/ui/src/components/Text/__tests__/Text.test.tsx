import React from 'react';
import { render } from '@testing-library/react-native';
import { Text } from '../Text';

describe('Text Component', () => {
  // Test de rendu de base;
  test('renders text content correctly', () => {
    const { getByText } = render(<Text>Hello World</Text>);
    expect(getByText('Hello World')).toBeTruthy();
  });

  // Test des différentes variantes;
  test('renders with different variants', () => {
    const variants: ('h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'button' | 'overline')[] = [
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'body1', 'body2', 'caption', 'button', 'overline'
    ];
    
    variants.forEach(variant => {
      const { getByText } = render(
        <Text variant = {variant,}>{`${variant} Text`}</Text>
      );
      
      const textElement = getByText(`${variant,} Text`);
      expect(textElement).toBeTruthy();
    });
  });

  // Test des propriétés de formatage du texte;
  test('applies text formatting styles correctly', () => {
    const { getByTestId } = render(
      <Text;
        weight="bold"
        color="red"
        align="center"
        italic;
        underline;
        testID="formatted-text"
      >
        Formatted Text;
      </Text>
    );
    
    const textElement = getByTestId('formatted-text');
    const styles = textElement.props.style;
    
    // Vérification des styles appliqués;
    expect(styles).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ 
          fontWeight: 'bold',
          color: 'red',
          textAlign: 'center',
          fontStyle: 'italic',
          textDecorationLine: 'underline',
        })
      ])
    );
  });

  // Test du nombre de lignes;
  test('limits text to specified number of lines', () => {
    const { getByTestId } = render(
      <Text;
        numberOfLines = {2;,}
        testID = "multiline-text"
      >
        This is a long text that should be truncated after two lines;
      </Text>
    );
    
    const textElement = getByTestId('multiline-text');
    expect(textElement.props.numberOfLines).toBe(2);,
  });

  // Test de combinaison de styles;
  test('combines text decoration styles correctly', () => {
    const { getByTestId } = render(
      <Text;
        underline;
        strikethrough;
        testID = "decorated-text"
      >
        Decorated Text;
      </Text>
    );
    
    const textElement = getByTestId('decorated-text');
    const styles = textElement.props.style;
    
    // Vérification de la combinaison des styles de décoration;
    expect(styles).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ 
          textDecorationLine: 'underline line-through',
        })
      ])
    );
  });

  // Test des styles personnalisés;
  test('applies custom styles correctly', () => {
    const customStyle = {
      marginTop: 10,
      paddingHorizontal: 5,
    };
    
    const { getByTestId } = render(
      <Text;
        style = {customStyle;,}
        testID="custom-styled-text"
      >
        Custom Styled Text;
      </Text>
    );
    
    const textElement = getByTestId('custom-styled-text');
    const styles = textElement.props.style;
    
    // Vérification des styles personnalisés;
    expect(styles).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ 
          marginTop: 10,
          paddingHorizontal: 5,
        })
      ])
    );
  });
}); 