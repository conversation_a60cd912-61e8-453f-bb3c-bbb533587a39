import React from 'react';
import {
  ActivityIndicator,
  View,
  StyleSheet,
  ViewStyle,
  Platform,
  Animated,
  Easing,
} from 'react-native';
import { THEME, SIZES } from '@projet-rb2/core/constants';

// Définir le type pour les tailles
type SizeType = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;
// Définir le type pour les variantes de couleur
type ColorVariantType = 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info' | 'light' | 'dark';
export interface SpinnerProps {
  /**
   * Taille du spinner
   * @default 'md'
   */
  size?: SizeType;
  
  /**
   * Variante de couleur
   * @default 'primary'
   */
  variant?: ColorVariantType;
  
  /**
   * Couleur personnalisée (remplace la variante)
   */
  color?: string;
  
  /**
   * Type de spinner
   * @default 'circular'
   */
  type?: 'circular' | 'dots' | 'bar';
  
  /**
   * Texte d'accessibilité
   * @default 'Chargement en cours'
   */
  accessibilityLabel?: string;
  
  /**
   * Style personnalisé pour le conteneur
   */
  style?: ViewStyle;
  
  /**
   * Afficher en plein écran avec fond semi-transparent
   * @default false
   */
  fullscreen?: boolean;
}

/**
 * Composant Spinner
 * 
 * Affiche un indicateur de chargement
 */
export const Spinner: React.FC<SpinnerProps> = ({
  size = 'md',
  variant = 'primary',
  color,
  type = 'circular',
  accessibilityLabel = 'Chargement en cours',
  style,
  fullscreen = false,
}) => {
  // Obtenir la taille en pixels
  const sizeValue = React.useMemo(() => {
    if (typeof size === 'number') return size;
    
    switch(size) {
      case 'xs': return 16;
      case 'sm': return 20;
      case 'md': return 24;
      case 'lg': return 32;
      case 'xl': return 48;
      default: return 24;
    }
  }, [size]);
  
  // Obtenir la couleur
  const spinnerColor = React.useMemo(() => {
    if (color) return color;
    
    switch(variant) {
      case 'primary': return THEME.COLORS.PRIMARY;
      case 'secondary': return THEME.COLORS.SECONDARY;
      case 'success': return THEME.COLORS.SUCCESS;
      case 'error': return THEME.COLORS.ERROR;
      case 'warning': return THEME.COLORS.WARNING;
      case 'info': return THEME.COLORS.INFO;
      case 'light': return THEME.COLORS.LIGHT;
      case 'dark': return THEME.COLORS.DARK;
      default: return THEME.COLORS.PRIMARY;
    }
  }, [variant, color]);
  
  // Animation pour les types personnalisés
  const spinAnim = React.useRef(new Animated.Value(0)).current;
  // Animations individuelles pour les dots
  const dotAnims = React.useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;
  
  React.useEffect(() => {
    if(type !== 'circular') {
      if(type === 'dots') {
        // Animer chaque point séparément
        dotAnims.forEach((anim, index) => {
          Animated.loop(
            Animated.sequence([
              Animated.delay(index * 300),
              Animated.timing(anim, {
                toValue: 1,
                duration: 600,
                easing: Easing.ease,
                useNativeDriver: true,
              }),
              Animated.timing(anim, {
                toValue: 0,
                duration: 600,
                easing: Easing.ease,
                useNativeDriver: true,
              }),
            ])
          ).start();
        });
      } else {
        // Animation pour la barre
        Animated.loop(
          Animated.timing(spinAnim, {
            toValue: 1,
            duration: 800,
            easing: Easing.linear,
            useNativeDriver: true,
          })
        ).start();
      }
    }
    
    return () => {
      spinAnim.stopAnimation();
      dotAnims.forEach(anim => anim.stopAnimation());
    };
  }, [spinAnim, dotAnims, type]);
  
  // Rendu du spinner en fonction du type
  const renderSpinner = () => {
    switch(type) {
      case 'dots':
        return (
          <View style={styles.dotsContainer}>
            {[0, 1, 2].map(i => (
              <Animated.View
                key={i}
                style={[
                  styles.dot,
                  {
                    width: sizeValue / 3,
                    height: sizeValue / 3,
                    backgroundColor: spinnerColor,
                    opacity: dotAnims[i].interpolate({
                      inputRange: [0, 0.5, 1],
                      outputRange: [0.3, 1, 0.3],
                    }),
                    transform: [
                      {
                        scale: dotAnims[i].interpolate({
                          inputRange: [0, 0.5, 1],
                          outputRange: [0.8, 1.2, 0.8],
                        }),
                      },
                    ],
                  },
                ]}
              />
            ))}
          </View>
        );
      case 'bar':
        return (
          <View style={styles.barContainer}>
            <Animated.View
              style={[
                styles.bar,
                {
                  width: sizeValue * 3,
                  height: sizeValue / 5,
                  backgroundColor: spinnerColor,
                  transform: [
                    {
                      scaleX: spinAnim.interpolate({
                        inputRange: [0, 0.5, 1],
                        outputRange: [0.1, 1, 0.1],
                      }),
                    },
                  ],
                },
              ]}
            />
          </View>
        );
      default:
        return (
          <ActivityIndicator
            size={Platform.OS === 'ios' 
              ? (sizeValue > 32 ? 'large' : 'small') 
              : sizeValue}
            color={spinnerColor}
            accessibilityLabel={accessibilityLabel}
          />
        );
    }
  };

  // Si fullscreen, afficher un conteneur qui prend tout l'écran
  if (fullscreen) {
    return (
      <View style={styles.fullscreenContainer}>
        <View style={styles.spinner}>
          {renderSpinner()}
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {renderSpinner()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullscreenContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 9999,
  },
  spinner: {
    padding: 24,
    borderRadius: 12,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    margin: 4,
    borderRadius: 50,
  },
  barContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 24,
  },
  bar: {
    borderRadius: 2,
  },
}); 