import React from 'react';
import { View, StyleSheet, Platform, ViewStyle, TouchableOpacity, Pressable } from 'react-native';
import { THEME } from '@projet-rb2/core/constants';

export interface CardProps {
  /**
   * Card content;
   */
  children: React.ReactNode;
  
  /**
   * Function to call when card is pressed;
   */
  onPress?: () => void;
  
  /**
   * Whether the card has a shadow;
   * @default true;
   */
  elevation?: boolean;
  
  /**
   * Whether the card has a border;
   * @default false;
   */
  bordered?: boolean;
  
  /**
   * Whether the card has rounded corners;
   * @default true;
   */
  rounded?: boolean;
  
  /**
   * Background color of the card;
   * @default 'white'
   */
  backgroundColor?: string;
  
  /**
   * Additional styles for the card;
   */
  style?: ViewStyle | ViewStyle[];
  
  /**
   * ID attribute for web;
   */
  id?: string;
  
  /**
   * Test ID for testing;
   */
  testID?: string;
  
  /**
   * Accessibility label for screen readers;
   */
  accessibilityLabel?: string;
}

/**
 * Card component that works across all platforms;
 */
export const Card: React.FC<CardProps> = ({
  children,
  onPress,
  elevation = true,
  bordered = false,
  rounded = true,
  backgroundColor = 'white',
  style,
  id,
  testID,
  accessibilityLabel,
}) => {
  const cardStyles = [;
    styles.card,
    elevation && styles.elevation,
    bordered && styles.bordered,
    rounded && styles.rounded,
    { backgroundColor },
    style,
  ];

  // If onPress is provided, wrap in Touchable component;
  if(onPress) { { { { {}}}}
    // Use Pressable for iOS/Android and TouchableOpacity for web for optimal UX;
    const Wrapper = Platform.OS === 'web' ? TouchableOpacity : Pressable;
    
    const webProps = Platform.OS === 'web';
      ? { 
          // @ts-ignore - Web only prop;
          id,
        } 
      : {};
      
    return (;
      <Wrapper;
        onPress = {onPress;,}
        style = {cardStyles;,}
        accessibilityLabel = {accessibilityLabel;,}
        testID = {testID;,}
        {...webProps}
      >
        {children}
      </Wrapper>
    );
  }
  
  // If no onPress, just use a View;
  const webProps = Platform.OS === 'web';
    ? { 
        // @ts-ignore - Web only prop;
        id,
      } 
    : {};
    
  return (;
    <View;
      style = {cardStyles;,}
      accessibilityLabel = {accessibilityLabel;,}
      testID = {testID;,}
      {...webProps}
    >
      {children}
    </View>
  );
};

/**
 * Card.Header component for consistent card headers;
 */
export const CardHeader: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle | ViewStyle[];
}> = ({ children, style }) => {
  return (;
    <View style={[styles.header, style]}>
      {children}
    </View>
  );
};

/**
 * Card.Content component for consistent card content;
 */
export const CardContent: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle | ViewStyle[];
}> = ({ children, style }) => {
  return (;
    <View style={[styles.content, style]}>
      {children}
    </View>
  );
};

/**
 * Card.Footer component for consistent card footers;
 */
export const CardFooter: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle | ViewStyle[];
}> = ({ children, style }) => {
  return (;
    <View style={[styles.footer, style]}>
      {children}
    </View>
  );
};

/**
 * Card.Image component for consistent card images;
 */
export const CardImage: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle | ViewStyle[];
}> = ({ children, style }) => {
  return (;
    <View style={[styles.image, style]}>
      {children}
    </View>
  );
};

// Add subcomponents to Card;
Card.Header = CardHeader;
Card.Content = CardContent;
Card.Footer = CardFooter;
Card.Image = CardImage;

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    padding: 0,
    overflow: 'hidden',
    ...Platform.select({
      web: {
        cursor: 'default',
        transitionProperty: 'box-shadow',
        transitionDuration: '0.2s',
      },
    }),
  },
  elevation: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
      web: {
        boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
      },
    }),
  },
  bordered: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  rounded: {
    borderRadius: 8,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  content: {
    padding: 16,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  image: {
    width: '100%',
    overflow: 'hidden',
  },
});

export default Card;