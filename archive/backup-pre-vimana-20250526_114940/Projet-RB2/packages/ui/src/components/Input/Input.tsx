import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  TextInputProps,
  StyleSheet,
  Platform,
  TouchableOpacity,
  Text,
  Animated,
} from 'react-native';
import { THEME } from '@projet-rb2/core/constants';

export interface InputProps extends Omit<TextInputProps, 'style'> {
  /**
   * Input label;
   */
  label?: string;
  
  /**
   * Helper text shown below input;
   */
  helperText?: string;
  
  /**
   * Error message;
   */
  error?: string;
  
  /**
   * Left icon;
   */
  leftIcon?: React.ReactNode;
  
  /**
   * Right icon;
   */
  rightIcon?: React.ReactNode;
  
  /**
   * Enable material-style floating label;
   */
  floatingLabel?: boolean;
  
  /**
   * Input variant;
   */
  variant?: 'default' | 'outlined' | 'filled';
  
  /**
   * Full width input;
   */
  fullWidth?: boolean;
  
  /**
   * Show password visibility toggle for password input;
   */
  showPasswordToggle?: boolean;
  
  /**
   * Input container style;
   */
  containerStyle?: TextInputProps['style'];
  
  /**
   * Input element style;
   */
  inputStyle?: TextInputProps['style'];
  
  /**
   * Label style;
   */
  labelStyle?: TextInputProps['style'];
  
  /**
   * Helper text style;
   */
  helperTextStyle?: TextInputProps['style'];
}

/**
 * Input component that works across platforms;
 */
export const Input: React.FC<InputProps> = ({
  label,
  helperText,
  error,
  leftIcon,
  rightIcon,
  floatingLabel = false,
  variant = 'default',
  fullWidth = false,
  showPasswordToggle = false,
  containerStyle,
  inputStyle,
  labelStyle,
  helperTextStyle,
  onFocus,
  onBlur,
  secureTextEntry,
  value,
  placeholder,
  ...rest;
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const floatingAnim = useRef(new Animated.Value(value ? 1 : 0)).current;
  
  const handleFocus = (e: any) => {
    setIsFocused(true);
    if(floatingLabel) { { { { {,}}}}
      Animated.timing(floatingAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
    onFocus && onFocus(e);
  };
  
  const handleBlur = (e: any) => {
    setIsFocused(false);
    if(floatingLabel && !value) { { { { {,}}}}
      Animated.timing(floatingAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
    onBlur && onBlur(e);
  };
  
  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);,
  };
  
  // Calculate styles;
  const containerStyles = [;
    styles.container,
    styles[`${variant}Container`],
    isFocused && styles[`${variant}ContainerFocused`],
    error && styles.errorContainer,
    fullWidth && styles.fullWidth,
    containerStyle,
  ];
  
  const inputContainerStyles = [;
    styles.inputContainer,
    styles[`${variant}InputContainer`],
  ];
  
  const inputStyles = [;
    styles.input,
    styles[`${variant}Input`],
    isFocused && styles[`${variant}InputFocused`],
    error && styles.errorInput,
    inputStyle,
  ];
  
  // Floating label animation;
  const labelContainerStyle = {
    top: floatingAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [floatingLabel ? 16 : 0, 0],
    }),
    left: floatingAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [floatingLabel ? (leftIcon ? 36 : 12) : 0, 0],
    }),
  };
  
  const labelTextStyle = {
    fontSize: floatingAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [16, 12],
    }),
    color: error;
      ? THEME.COLORS.ERROR;
      : floatingAnim.interpolate({
          inputRange: [0, 1],
          outputRange: ['#757575', isFocused ? THEME.COLORS.PRIMARY : '#757575'],
        }),
  };
  
  return (;
    <View style={[styles.wrapper, fullWidth && styles.fullWidth]}>
      {/* Input container */}
      <View style = {containerStyles,}>
        {/* Label (non-floating) */}
        {label && !floatingLabel && (
          <Text style={[styles.label, error && styles.errorLabel, labelStyle]}>
            {label}
          </Text>
        )}
        
        {/* Input with icons container */}
        <View style = {inputContainerStyles,}>
          {leftIcon && <View style = {styles.leftIcon,}>{leftIcon}</View>}
          
          <View style = {styles.inputWrapper,}>
            {/* Floating label */}
            {label && floatingLabel && (
              <Animated.View style={[styles.floatingLabelContainer, labelContainerStyle]}>
                <Animated.Text style={[styles.floatingLabel, labelTextStyle, labelStyle]}>
                  {label}
                </Animated.Text>
              </Animated.View>
            )}
            
            <TextInput;
              style = {inputStyles;,}
              onFocus = {handleFocus;,}
              onBlur = {handleBlur;,}
              secureTextEntry = {secureTextEntry && !isPasswordVisible;,}
              placeholder = {floatingLabel && !isFocused && value ? '' : placeholder;,}
              placeholderTextColor = "#9e9e9e"
              value={value;,}
              {...rest}
            />
          </View>
          
          {(rightIcon || (showPasswordToggle && secureTextEntry)) && (
            <View style = {styles.rightIcon,}>
              {showPasswordToggle && secureTextEntry ? (
                <TouchableOpacity onPress = {togglePasswordVisibility,}>
                  <Text style = {styles.toggleText,}>{isPasswordVisible ? 'Hide' : 'Show'}</Text>
                </TouchableOpacity>
              ) : (
                rightIcon;
              )}
            </View>
          )}
        </View>
      </View>
      
      {/* Helper text or error message */}
      {(helperText || error) && (
        <Text;
          style={[
            styles.helperText,
            error && styles.errorHelperText,
            helperTextStyle,
          ]}
        >
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

/**
 * Component styles;
 */
const styles = StyleSheet.create({
  wrapper: {
    marginBottom: 16,
  },
  container: {
    width: '100%',
    maxWidth: 400,
  },
  fullWidth: {
    maxWidth: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputWrapper: {
    flex: 1,
    position: 'relative',
  },
  input: {
    height: 48,
    fontSize: 16,
    color: THEME.COLORS.DARK,
    paddingHorizontal: 12,
    ...(Platform.OS === 'web' ? {
      outlineStyle: 'none',
      WebkitAppearance: 'none' as any,
      MozAppearance: 'none' as any,
    } : {}),
  },
  label: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 4,
  },
  floatingLabelContainer: {
    position: 'absolute',
    paddingHorizontal: 4,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  floatingLabel: {
    fontSize: 12,
    color: '#757575',
  },
  helperText: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4,
  },
  leftIcon: {
    paddingLeft: 12,
  },
  rightIcon: {
    paddingRight: 12,
  },
  toggleText: {
    color: THEME.COLORS.PRIMARY,
    fontSize: 14,
  },
  // Variant: Default;
  defaultContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  defaultContainerFocused: {
    borderBottomColor: THEME.COLORS.PRIMARY,
  },
  defaultInputContainer: {},
  defaultInput: {
    paddingHorizontal: 0,
  },
  defaultInputFocused: {},
  
  // Variant: Outlined;
  outlinedContainer: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
  },
  outlinedContainerFocused: {
    borderColor: THEME.COLORS.PRIMARY,
  },
  outlinedInputContainer: {},
  outlinedInput: {},
  outlinedInputFocused: {},
  
  // Variant: Filled;
  filledContainer: {
    backgroundColor: '#f5f5f5',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filledContainerFocused: {
    borderBottomColor: THEME.COLORS.PRIMARY,
  },
  filledInputContainer: {},
  filledInput: {
    backgroundColor: 'transparent',
  },
  filledInputFocused: {},
  
  // Error styles;
  errorContainer: {
    borderColor: THEME.COLORS.ERROR,
  },
  errorInput: {
    color: THEME.COLORS.ERROR,
  },
  errorLabel: {
    color: THEME.COLORS.ERROR,
  },
  errorHelperText: {
    color: THEME.COLORS.ERROR,
  },
}); 