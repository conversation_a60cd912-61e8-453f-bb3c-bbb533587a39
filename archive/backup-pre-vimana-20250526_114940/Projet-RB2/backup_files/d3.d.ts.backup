declare module 'd3' {
  export function select(selector: string | Element): Selection;
  export function selectAll(selector: string): Selection;
  export function scaleLinear(): ScaleLinear;
  export function scaleTime(): ScaleTime;
  export function scaleOrdinal(): ScaleOrdinal;
  export function scaleBand(): ScaleBand;
  export function axisTop(scale: any): Axis;
  export function axisRight(scale: any): Axis;
  export function axisBottom(scale: any): Axis;
  export function axisLeft(scale: any): Axis;
  export function extent<T>(array: T[], accessor?: (d: T, i: number) => number): [number, number];
  export function line<T>(): Line<T>;
  export function pie<T>(): Pie<T>;
  export function arc<T>(): Arc<T>;
  export function format(specifier: string): (n: number) => string;
  export function timeFormat(specifier: string): (date: Date) => string;
  export const schemeCategory10: string[];
  export const schemePaired: string[];
  export const schemeSet3: string[];
  export function interpolateRainbow(t: number): string;

  export interface Selection {
    attr(name: string, value: any): Selection;
    style(name: string, value: any, priority?: string): Selection;
    property(name: string, value?: any): Selection;
    classed(name: string, value: boolean): Selection;
    text(value: string): Selection;
    html(value: string): Selection;
    append(name: string): Selection;
    remove(): Selection;
    data<T>(data: T[], key?: (d: T, i: number) => string): Selection;
    enter(): Selection;
    exit(): Selection;
    merge(selection: Selection): Selection;
    transition(): Transition;
    call(func: (selection: Selection, ...args: any[]) => void, ...args: any[]): Selection;
    on(type: string, listener: (event: Event, d: any) => void): Selection;
    selectAll(selector: string): Selection;
    select(selector: string): Selection;
    node(): Element;
    nodes(): Element[];
    size(): number;
  }

  export interface ScaleLinear {
    domain(domain: number[]): ScaleLinear;
    range(range: any[]): ScaleLinear;
    (value: number): number;
    invert(value: number): number;
    nice(count?: number): ScaleLinear;
    clamp(clamp: boolean): ScaleLinear;
  }

  export interface ScaleTime {
    domain(domain: Date[]): ScaleTime;
    range(range: any[]): ScaleTime;
    (value: Date): number;
    invert(value: number): Date;
    nice(count?: number): ScaleTime;
    clamp(clamp: boolean): ScaleTime;
  }

  export interface ScaleOrdinal {
    domain(domain: any[]): ScaleOrdinal;
    range(range: any[]): ScaleOrdinal;
    (value: any): any;
  }

  export interface ScaleBand {
    domain(domain: any[]): ScaleBand;
    range(range: number[]): ScaleBand;
    (value: any): number;
    bandwidth(): number;
    padding(padding: number): ScaleBand;
    paddingInner(padding: number): ScaleBand;
    paddingOuter(padding: number): ScaleBand;
  }

  export interface Axis {
    (selection: Selection): void;
    scale(scale: any): Axis;
    ticks(count?: number): Axis;
    tickValues(values: any[]): Axis;
    tickFormat(format: (d: any) => string): Axis;
    tickSize(size: number): Axis;
    tickSizeInner(size: number): Axis;
    tickSizeOuter(size: number): Axis;
    tickPadding(padding: number): Axis;
  }

  export interface Transition {
    attr(name: string, value: any): Transition;
    style(name: string, value: any, priority?: string): Transition;
    duration(ms: number): Transition;
    delay(ms: number): Transition;
    ease(ease: (t: number) => number): Transition;
    on(type: string, listener: (d: any, i: number) => void): Transition;
  }

  export interface Line<T> {
    (data: T[]): string | null;
    x(x: (d: T, i: number) => number): Line<T>;
    y(y: (d: T, i: number) => number): Line<T>;
    defined(defined: (d: T, i: number) => boolean): Line<T>;
    curve(curve: any): Line<T>;
  }

  export interface Pie<T> {
    (data: T[]): Array<{
      data: T;
      value: number;
      index: number;
      startAngle: number;
      endAngle: number;
      padAngle: number;
    }>;
    value(value: (d: T, i: number) => number): Pie<T>;
    sort(comparator: null | ((a: T, b: T) => number)): Pie<T>;
    sortValues(comparator: (a: number, b: number) => number): Pie<T>;
    padAngle(angle: number): Pie<T>;
  }

  export interface Arc<T> {
    (d: any, i?: number): string | null;
    innerRadius(radius: number | ((d: T, i: number) => number)): Arc<T>;
    outerRadius(radius: number | ((d: T, i: number) => number)): Arc<T>;
    cornerRadius(radius: number | ((d: T, i: number) => number)): Arc<T>;
    startAngle(angle: number | ((d: T, i: number) => number)): Arc<T>;
    endAngle(angle: number | ((d: T, i: number) => number)): Arc<T>;
    padAngle(angle: number | ((d: T, i: number) => number)): Arc<T>;
    padRadius(radius: number | ((d: T, i: number) => number)): Arc<T>;
  }
}
