import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

interface DependencyNode {
  name: string;
  version: string;
  dependencies: DependencyNode[];
  vulnerabilities: Vulnerability[];
}

interface Vulnerability {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedVersions: string;
  fixedVersions?: string;
  references: string[];
}

interface ScanResult {
  timestamp: Date;
  projectName: string;
  dependencies: DependencyNode[];
  vulnerabilitySummary: {
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
}

@Injectable()
export class DependencyAnalyzer {
  private readonly logger = new Logger(DependencyAnalyzer.name);

  constructor(private readonly configService: ConfigService) {}

  async analyzeDependencies(projectPath: string): Promise<ScanResult> {
    this.logger.log(`Analyzing dependencies for project at ${projectPath}`);

    // This would normally use a real scanner like npm audit or snyk
    // This is just a mock implementation for demonstration purposes
    const mockScanResult: ScanResult = {
      timestamp: new Date(),
      projectName: 'smart-contract-project',
      dependencies: [
        {
          name: 'web3',
          version: '1.8.0',
          dependencies: [
            {
              name: 'bn.js',
              version: '4.12.0',
              dependencies: [],
              vulnerabilities: [],
            },
          ],
          vulnerabilities: [],
        },
        {
          name: 'solidity-parser-antlr',
          version: '0.4.12',
          dependencies: [],
          vulnerabilities: [
            {
              id: 'CVE-2021-12345',
              severity: 'high',
              description: 'Path traversal vulnerability in solidity-parser-antlr',
              affectedVersions: '<0.4.13',
              fixedVersions: '>=0.4.13',
              references: ['https://nvd.nist.gov/vuln/detail/CVE-2021-12345'],
            },
          ],
        },
      ],
      vulnerabilitySummary: {
        total: 1,
        critical: 0,
        high: 1,
        medium: 0,
        low: 0,
      },
    };

    return mockScanResult;
  }

  async generateDependencyGraph(projectPath: string): Promise<string> {
    // This would generate a visualization of the dependency tree
    // For demonstration, we'll just return a mock DOT format graph
    return `
digraph Dependencies {
  "project" -> "web3";
  "web3" -> "bn.js";
  "project" -> "solidity-parser-antlr";
}
`;
  }

  async checkForVulnerabilities(projectPath: string): Promise<Vulnerability[]> {
    const scanResult = await this.analyzeDependencies(projectPath);

    // Flatten the dependency tree and extract all vulnerabilities
    const allVulnerabilities: Vulnerability[] = [];

    const extractVulnerabilities = (node: DependencyNode) => {
      allVulnerabilities.push(...node.vulnerabilities);
      node.dependencies.forEach(extractVulnerabilities);
    };

    scanResult.dependencies.forEach(extractVulnerabilities);

    return allVulnerabilities;
  }

  async generateSecurityReport(projectPath: string): Promise<string> {
    const scanResult = await this.analyzeDependencies(projectPath);
    const vulnerabilities = await this.checkForVulnerabilities(projectPath);

    let report = '# Dependency Security Analysis Report\n\n';
    report += `Generated: ${scanResult.timestamp.toISOString()}\n\n`;
    report += `## Project: ${scanResult.projectName}\n\n`;

    report += '## Vulnerability Summary\n\n';
    report += `Total vulnerabilities: ${scanResult.vulnerabilitySummary.total}\n`;
    report += `- Critical: ${scanResult.vulnerabilitySummary.critical}\n`;
    report += `- High: ${scanResult.vulnerabilitySummary.high}\n`;
    report += `- Medium: ${scanResult.vulnerabilitySummary.medium}\n`;
    report += `- Low: ${scanResult.vulnerabilitySummary.low}\n\n`;

    if (vulnerabilities.length > 0) {
      report += '## Detected Vulnerabilities\n\n';

      vulnerabilities.forEach(vuln => {
        report += `### ${vuln.id} (${vuln.severity.toUpperCase()})\n\n`;
        report += `${vuln.description}\n\n`;
        report += `Affected versions: ${vuln.affectedVersions}\n`;
        if (vuln.fixedVersions) {
          report += `Fixed versions: ${vuln.fixedVersions}\n`;
        }
        report += '\nReferences:\n';
        vuln.references.forEach(ref => {
          report += `- ${ref}\n`;
        });
        report += '\n';
      });
    } else {
      report += '## No vulnerabilities detected\n\n';
    }

    return report;
  }
}
