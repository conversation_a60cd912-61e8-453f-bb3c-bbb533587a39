# Guide du Système de Recommandation en Temps Réel

Ce document décrit l'architecture et l'utilisation du système de recommandation en temps réel de Retreat And Be.

## Architecture Générale

Le système de recommandation en temps réel permet de générer des recommandations personnalisées basées sur les interactions récentes des utilisateurs. Il est composé de trois composants principaux :

1. **Gestion des Sessions** : Suivi des interactions utilisateur
2. **Analyse des Intérêts** : Détection des intérêts actuels
3. **Génération de Recommandations** : Création de recommandations adaptatives

```
┌─────────────────────────────────────────────────────────────┐
│            Système de Recommandation en Temps Réel          │
│                                                             │
│  ┌───────────────┐  ┌───────────────┐  ┌───────────────┐   │
│  │  Gestion des  │  │  Analyse des  │  │ Génération de │   │
│  │   Sessions    │──▶│   Intérêts    │──▶│Recommandations│   │
│  └───────────────┘  └───────────────┘  └───────────────┘   │
│                                                             │
│  ┌───────────────────────────────────────────────────────┐ │
│  │              Système de Notification                  │ │
│  └───────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Gestion des Sessions

Le système maintient des sessions utilisateur pour suivre les interactions récentes.

### Structure de Session

```typescript
interface UserSession {
  userId: string;
  interactions: UserInteraction[];
  lastActive: Date;
  currentContext: UserContext;
}

interface UserInteraction {
  itemId: string;
  itemType: string;
  interactionType: InteractionType;
  timestamp: Date;
  metadata: Record<string, any>;
}
```

### Méthodes de Gestion

```typescript
// Récupérer ou créer une session utilisateur
private getOrCreateSession(userId: string): UserSession {
  if (!this.userSessions.has(userId)) {
    this.userSessions.set(userId, {
      userId,
      interactions: [],
      lastActive: new Date(),
      currentContext: {},
    });
  }
  
  return this.userSessions.get(userId);
}

// Ajouter une interaction à la session
async addInteraction(userId: string, interaction: UserInteraction): Promise<void> {
  const session = this.getOrCreateSession(userId);
  
  // Ajouter l'interaction
  session.interactions.push(interaction);
  session.lastActive = new Date();
  
  // Limiter le nombre d'interactions stockées
  if (session.interactions.length > this.maxInteractionsPerSession) {
    session.interactions = session.interactions.slice(-this.maxInteractionsPerSession);
  }
  
  // Mettre à jour le contexte utilisateur
  session.currentContext = await this.updateUserContext(userId, interaction);
}
```

### Nettoyage des Sessions

```typescript
// Nettoyer les sessions inactives
@Cron(CronExpression.EVERY_HOUR)
cleanupSessions(): void {
  const now = new Date();
  const expirationTime = this.sessionExpirationTime * 60 * 1000; // Convertir en millisecondes
  
  for (const [userId, session] of this.userSessions.entries()) {
    const inactiveTime = now.getTime() - session.lastActive.getTime();
    
    if (inactiveTime > expirationTime) {
      this.userSessions.delete(userId);
      this.logger.log(`Session de l'utilisateur ${userId} supprimée pour inactivité`);
    }
  }
}
```

## Analyse des Intérêts

Le système analyse les interactions récentes pour déterminer les intérêts actuels de l'utilisateur.

### Structure des Intérêts

```typescript
interface UserInterests {
  categories: Map<string, number>;
  tags: Map<string, number>;
  items: Map<string, number>;
  locations: Map<string, number>;
  recency: Map<string, Date>;
}
```

### Méthode d'Analyse

```typescript
// Analyser les interactions récentes
private analyzeRecentInteractions(interactions: UserInteraction[]): UserInterests {
  const interests: UserInterests = {
    categories: new Map(),
    tags: new Map(),
    items: new Map(),
    locations: new Map(),
    recency: new Map(),
  };
  
  // Trier les interactions par date (plus récentes d'abord)
  const sortedInteractions = [...interactions].sort(
    (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
  );
  
  // Analyser chaque interaction
  for (let i = 0; i < sortedInteractions.length; i++) {
    const interaction = sortedInteractions[i];
    const recencyWeight = Math.exp(-i * 0.1); // Poids décroissant avec l'ancienneté
    const interactionWeight = this.getInteractionTypeWeight(interaction.interactionType);
    const weight = recencyWeight * interactionWeight;
    
    // Mettre à jour les intérêts
    this.updateInterestMap(interests.items, interaction.itemId, weight);
    
    // Mettre à jour les catégories
    if (interaction.metadata?.categories) {
      for (const category of interaction.metadata.categories) {
        this.updateInterestMap(interests.categories, category, weight);
      }
    }
    
    // Mettre à jour les tags
    if (interaction.metadata?.tags) {
      for (const tag of interaction.metadata.tags) {
        this.updateInterestMap(interests.tags, tag, weight);
      }
    }
    
    // Mettre à jour les localisations
    if (interaction.metadata?.location) {
      this.updateInterestMap(interests.locations, interaction.metadata.location, weight);
    }
    
    // Mettre à jour la récence
    interests.recency.set(interaction.itemId, interaction.timestamp);
  }
  
  return interests;
}

// Mettre à jour une carte d'intérêts
private updateInterestMap(map: Map<string, number>, key: string, weight: number): void {
  const currentWeight = map.get(key) || 0;
  map.set(key, currentWeight + weight);
}

// Obtenir le poids d'un type d'interaction
private getInteractionTypeWeight(type: InteractionType): number {
  switch (type) {
    case InteractionType.VIEW:
      return 1;
    case InteractionType.CLICK:
      return 2;
    case InteractionType.FAVORITE:
      return 3;
    case InteractionType.BOOK:
      return 4;
    default:
      return 1;
  }
}
```

### Extraction des Intérêts Principaux

```typescript
// Obtenir les éléments les plus intéressants
private getTopItems<T>(map: Map<T, number>, limit: number): T[] {
  return Array.from(map.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, limit)
    .map(entry => entry[0]);
}
```

## Génération de Recommandations

Le système génère des recommandations basées sur les intérêts actuels de l'utilisateur.

### Méthode de Génération

```typescript
// Générer des recommandations à partir des intérêts
private async generateRecommendationsFromInterests(
  userId: string,
  type: RecommendationType,
  interests: UserInterests,
  options: RecommendationOptions = {},
): Promise<Recommendation[]> {
  // Construire les filtres basés sur les intérêts
  const filters: any = {};
  
  // Filtrer par catégories
  const topCategories = this.getTopItems(interests.categories, 3);
  if (topCategories.length > 0) {
    filters.categories = {
      some: {
        name: {
          in: topCategories,
        },
      },
    };
  }
  
  // Filtrer par tags
  const topTags = this.getTopItems(interests.tags, 5);
  if (topTags.length > 0) {
    filters.metadata = {
      path: ['tags'],
      array_contains: topTags,
    };
  }
  
  // Exclure les items déjà vus
  const viewedItems = Array.from(interests.items.keys());
  if (viewedItems.length > 0) {
    filters.id = {
      notIn: viewedItems,
    };
  }
  
  // Utiliser le service basé sur le contenu avec les filtres personnalisés
  const recommendations = await this.contentBasedService.getRecommendations(
    userId,
    type,
    {
      ...options,
      filters,
      limit: (options.limit || 10) * 2, // Récupérer plus de recommandations pour avoir plus de choix
    },
  );
  
  // Diversifier les résultats
  return this.diversityFilterService.applyDiversityFilter(
    recommendations,
    options.limit || 10,
    {
      categoryDiversity: 0.3,
      tagDiversity: 0.3,
    }
  );
}
```

## Système de Notification

Le système peut déclencher des notifications basées sur les recommandations en temps réel.

```typescript
// Envoyer des notifications pour les recommandations pertinentes
async sendRecommendationNotifications(userId: string, recommendations: Recommendation[]): Promise<void> {
  // Filtrer les recommandations hautement pertinentes
  const highlyRelevant = recommendations.filter(rec => rec.score > 0.8);
  
  if (highlyRelevant.length === 0) {
    return;
  }
  
  // Vérifier les préférences de notification de l'utilisateur
  const userPreferences = await this.getUserNotificationPreferences(userId);
  
  if (!userPreferences.enableRecommendationNotifications) {
    return;
  }
  
  // Vérifier la fréquence des notifications
  const lastNotification = await this.getLastNotificationTime(userId);
  const now = new Date();
  const minInterval = userPreferences.notificationFrequency || 24; // Heures
  
  if (lastNotification && (now.getTime() - lastNotification.getTime()) < minInterval * 3600 * 1000) {
    return;
  }
  
  // Sélectionner la meilleure recommandation
  const topRecommendation = highlyRelevant[0];
  
  // Envoyer la notification
  await this.notificationService.sendNotification(userId, {
    title: 'Recommandation pour vous',
    body: `Découvrez "${topRecommendation.title}" qui correspond à vos intérêts récents`,
    data: {
      type: 'RECOMMENDATION',
      itemId: topRecommendation.id,
      itemType: topRecommendation.type,
    },
  });
  
  // Mettre à jour le timestamp de dernière notification
  await this.updateLastNotificationTime(userId, now);
}
```

## Configuration

Le système est configurable via le fichier `config/recommendation.config.ts` :

```typescript
realtime: {
  enabled: process.env.RECOMMENDATION_REALTIME_ENABLED === 'true',
  sessionExpirationTime: parseInt(process.env.RECOMMENDATION_SESSION_EXPIRATION_MINUTES || '30', 10),
  maxInteractionsPerSession: parseInt(process.env.RECOMMENDATION_MAX_INTERACTIONS_PER_SESSION || '100', 10),
  notificationThreshold: parseFloat(process.env.RECOMMENDATION_NOTIFICATION_THRESHOLD || '0.8'),
}
```

## Exemples d'Utilisation

### Enregistrement d'Interactions

```typescript
// Dans un contrôleur
@Post('interactions')
async recordInteraction(
  @Req() request,
  @Body() interactionData: CreateInteractionDto,
) {
  const userId = request.user.id;
  
  // Enregistrer l'interaction
  await this.interactionService.createInteraction(userId, interactionData);
  
  // Ajouter l'interaction à la session en temps réel
  await this.realtimeRecommendationService.addInteraction(userId, {
    itemId: interactionData.itemId,
    itemType: interactionData.itemType,
    interactionType: interactionData.type,
    timestamp: new Date(),
    metadata: interactionData.metadata,
  });
  
  return { success: true };
}
```

### Obtention de Recommandations en Temps Réel

```typescript
// Dans un contrôleur
@Get('recommendations/realtime')
async getRealtimeRecommendations(
  @Req() request,
  @Query('limit') limit: number = 10,
) {
  const userId = request.user.id;
  
  return this.realtimeRecommendationService.getRecommendations(
    userId,
    RecommendationType.RETREAT,
    { limit }
  );
}
```

## Bonnes Pratiques

1. **Performance** : Optimiser les algorithmes pour des réponses rapides (<200ms)
2. **Équilibre** : Trouver le bon équilibre entre réactivité et stabilité des recommandations
3. **Fallback** : Prévoir des mécanismes de secours en cas d'absence d'interactions récentes
4. **Respect de la Vie Privée** : Informer les utilisateurs du suivi de leurs interactions

## Dépannage

### Problèmes Courants

1. **Recommandations Trop Volatiles** : Ajuster les poids de récence pour plus de stabilité
2. **Surconsommation Mémoire** : Optimiser la gestion des sessions et le nettoyage
3. **Notifications Excessives** : Ajuster les seuils et fréquences de notification
