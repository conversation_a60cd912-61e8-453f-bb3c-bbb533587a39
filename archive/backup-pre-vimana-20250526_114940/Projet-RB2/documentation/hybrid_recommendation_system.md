# Guide du Système de Recommandation Hybride

Ce document décrit l'architecture et l'utilisation du système de recommandation hybride de Retreat And Be.

## Architecture Générale

Le système de recommandation hybride combine plusieurs approches de recommandation pour améliorer la qualité des résultats. Il est composé de quatre composants principaux :

1. **Algorithmes de Base** : Différentes approches de recommandation
2. **Méthodes d'Hybridation** : Stratégies pour combiner les algorithmes
3. **Optimisation des Poids** : Ajustement dynamique des poids
4. **Sélection Dynamique** : Choix intelligent des algorithmes

```
┌─────────────────────────────────────────────────────────────┐
│                  Système de Recommandation Hybride          │
│                                                             │
│  ┌───────────────┐  ┌───────────────┐  ┌───────────────┐   │
│  │ Algorithmes   │  │  Méthodes     │  │ Optimisation  │   │
│  │    de Base    │  │ d'Hybridation │  │   des Poids   │   │
│  └───────────────┘  └───────────────┘  └───────────────┘   │
│                                                             │
│  ┌───────────────────────────────────────────────────────┐ │
│  │              Sélection Dynamique                      │ │
│  └───────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Algorithmes de Base

Le système utilise plusieurs algorithmes de recommandation de base :

### ContentBasedService

Recommandations basées sur les caractéristiques des retraites.

```typescript
// Exemple d'utilisation
const contentBasedRecommendations = await contentBasedService.getRecommendations(
  userId,
  RecommendationType.RETREAT,
  { limit: 10 }
);
```

### CollaborativeFilteringService

Recommandations basées sur les comportements similaires des utilisateurs.

```typescript
// Exemple d'utilisation
const collaborativeRecommendations = await collaborativeFilteringService.getRecommendations(
  userId,
  RecommendationType.RETREAT,
  { limit: 10 }
);
```

### MatrixFactorizationService

Recommandations basées sur la factorisation matricielle.

```typescript
// Exemple d'utilisation
const matrixFactorizationRecommendations = await matrixFactorizationService.getRecommendations(
  userId,
  RecommendationType.RETREAT,
  { limit: 10 }
);
```

### ContextualRecommendationService

Recommandations basées sur le contexte utilisateur.

```typescript
// Exemple d'utilisation
const contextualRecommendations = await contextualRecommendationService.getRecommendations(
  userId,
  RecommendationType.RETREAT,
  {
    limit: 10,
    context: {
      location: user.location,
      season: 'SUMMER',
      timeOfDay: 'MORNING'
    }
  }
);
```

## Méthodes d'Hybridation

Le système implémente quatre méthodes d'hybridation principales :

### Weighted (Pondérée)

Combine les résultats de différents algorithmes en utilisant des poids.

```typescript
// Implémentation simplifiée
async getWeightedRecommendations(userId, type, options) {
  // Récupérer les recommandations de chaque méthode
  const [contentBased, collaborative, matrixFactorization, contextual] = 
    await Promise.all([...]);
  
  // Appliquer les poids
  const weightedResults = [];
  for (const item of allItems) {
    let score = 0;
    if (contentBasedMap.has(item.id)) {
      score += contentBasedWeight * contentBasedMap.get(item.id).score;
    }
    // Répéter pour les autres algorithmes
    
    weightedResults.push({ ...item, score });
  }
  
  return weightedResults.sort((a, b) => b.score - a.score).slice(0, limit);
}
```

### Switching (Commutation)

Sélectionne dynamiquement l'algorithme le plus approprié en fonction du contexte.

```typescript
// Implémentation simplifiée
async getSwitchingRecommendations(userId, type, options) {
  const interactionCount = await this.getUserInteractionCount(userId);
  
  if (interactionCount >= interactionThreshold * 2) {
    // Beaucoup d'interactions : utiliser la factorisation matricielle
    return this.matrixFactorizationService.getRecommendations(userId, type, options);
  } else if (interactionCount >= interactionThreshold) {
    // Nombre moyen d'interactions : utiliser le filtrage collaboratif
    return this.collaborativeFilteringService.getRecommendations(userId, type, options);
  } else if (context && (context.location || context.season)) {
    // Peu d'interactions mais contexte disponible : utiliser les recommandations contextuelles
    return this.contextualRecommendationService.getRecommendations(userId, type, options);
  } else {
    // Peu d'interactions et pas de contexte : utiliser la recommandation basée sur le contenu
    return this.contentBasedService.getRecommendations(userId, type, options);
  }
}
```

### Cascading (Cascade)

Applique séquentiellement des filtres algorithmiques.

```typescript
// Implémentation simplifiée
async getCascadingRecommendations(userId, type, options) {
  // Étape 1: Obtenir des recommandations basées sur le contenu
  let recommendations = await this.contentBasedService.getRecommendations(
    userId, type, { ...options, limit: limit * 3 }
  );
  
  // Étape 2: Filtrer avec le filtrage collaboratif
  recommendations = await this.applyCollaborativeFilter(userId, recommendations);
  
  // Étape 3: Réordonner avec le contexte
  recommendations = await this.applyContextualReordering(userId, recommendations, options.context);
  
  return recommendations.slice(0, options.limit || 10);
}
```

### Mixed (Mixte)

Fusionne les recommandations de différentes sources.

```typescript
// Implémentation simplifiée
async getMixedRecommendations(userId, type, options) {
  // Récupérer les recommandations de chaque méthode
  const [contentBased, collaborative, matrixFactorization, contextual] = 
    await Promise.all([...]);
  
  // Fusionner les résultats
  const mixedResults = [];
  const seenIds = new Set();
  
  // Ajouter les résultats de chaque source en alternance
  for (let i = 0; i < Math.max(contentBased.length, collaborative.length, ...); i++) {
    if (i < contentBased.length && !seenIds.has(contentBased[i].id)) {
      mixedResults.push({ ...contentBased[i], sources: ['content-based'] });
      seenIds.add(contentBased[i].id);
    }
    // Répéter pour les autres sources
  }
  
  return mixedResults.slice(0, options.limit || 10);
}
```

## Optimisation des Poids

Le système ajuste dynamiquement les poids des algorithmes en fonction des performances.

### Mécanisme d'Apprentissage

```typescript
// Implémentation simplifiée
async updateAlgorithmWeights(userId, interactionData) {
  const currentWeights = await this.getUserAlgorithmWeights(userId);
  
  // Calculer les performances de chaque algorithme
  const performances = await this.calculateAlgorithmPerformances(userId, interactionData);
  
  // Ajuster les poids en fonction des performances
  const newWeights = {
    contentBased: currentWeights.contentBased * (1 + performances.contentBased * 0.1),
    collaborative: currentWeights.collaborative * (1 + performances.collaborative * 0.1),
    matrixFactorization: currentWeights.matrixFactorization * (1 + performances.matrixFactorization * 0.1),
    contextual: currentWeights.contextual * (1 + performances.contextual * 0.1),
  };
  
  // Normaliser les poids
  const sum = Object.values(newWeights).reduce((a, b) => a + b, 0);
  for (const key in newWeights) {
    newWeights[key] /= sum;
  }
  
  // Sauvegarder les nouveaux poids
  await this.saveUserAlgorithmWeights(userId, newWeights);
  
  return newWeights;
}
```

## Sélection Dynamique

Le système sélectionne intelligemment les algorithmes en fonction du contexte utilisateur.

```typescript
// Implémentation simplifiée
async selectBestAlgorithm(userId, context) {
  // Récupérer l'historique des performances
  const performanceHistory = await this.getAlgorithmPerformanceHistory(userId);
  
  // Facteurs de décision
  const userInteractions = await this.getUserInteractionCount(userId);
  const hasContext = context && (context.location || context.season || context.timeOfDay);
  const timeOfDay = context?.timeOfDay || this.getCurrentTimeOfDay();
  
  // Logique de sélection
  if (userInteractions < 10) {
    return 'content-based'; // Nouvel utilisateur
  } else if (hasContext && performanceHistory.contextual > 0.7) {
    return 'contextual'; // Bon historique avec contexte disponible
  } else if (userInteractions > 100 && performanceHistory.matrixFactorization > 0.8) {
    return 'matrix-factorization'; // Utilisateur établi avec bon historique
  } else {
    return 'collaborative'; // Par défaut
  }
}
```

## Configuration

Le système est configurable via le fichier `config/recommendation.config.ts` :

```typescript
strategies: {
  default: process.env.RECOMMENDATION_DEFAULT_STRATEGY || 'HYBRID',
  defaultHybridMethod: process.env.RECOMMENDATION_DEFAULT_HYBRID_METHOD || 'WEIGHTED',
  weights: {
    contentBased: parseFloat(process.env.RECOMMENDATION_WEIGHT_CONTENT_BASED || '0.3'),
    collaborative: parseFloat(process.env.RECOMMENDATION_WEIGHT_COLLABORATIVE || '0.3'),
    matrixFactorization: parseFloat(process.env.RECOMMENDATION_WEIGHT_MATRIX_FACTORIZATION || '0.2'),
    contextual: parseFloat(process.env.RECOMMENDATION_WEIGHT_CONTEXTUAL || '0.2'),
  },
}
```

## Exemples d'Utilisation

### Utilisation Simple

```typescript
// Dans un contrôleur
@Get('recommendations')
async getRecommendations(@Req() request) {
  const userId = request.user.id;
  
  return this.hybridRecommendationService.getRecommendations(
    userId,
    RecommendationType.RETREAT
  );
}
```

### Utilisation Avancée

```typescript
// Dans un contrôleur
@Get('recommendations/advanced')
async getAdvancedRecommendations(
  @Req() request,
  @Query('method') method: HybridMethod,
  @Query('limit') limit: number,
) {
  const userId = request.user.id;
  
  return this.hybridRecommendationService.getRecommendations(
    userId,
    RecommendationType.RETREAT,
    {
      hybridMethod: method,
      limit,
      context: await this.getUserContext(userId),
      enrichWithExternalData: true,
    }
  );
}
```

## Bonnes Pratiques

1. **Évaluation Continue** : Mesurer régulièrement les performances des différents algorithmes
2. **Tests A/B** : Tester différentes configurations pour optimiser les résultats
3. **Fallback** : Toujours prévoir des mécanismes de secours en cas d'échec d'un algorithme
4. **Personnalisation** : Adapter les poids et les méthodes en fonction des préférences utilisateur

## Dépannage

### Problèmes Courants

1. **Recommandations Trop Similaires** : Ajuster les poids pour favoriser la diversité
2. **Performances Lentes** : Optimiser les requêtes et utiliser le cache
3. **Cold Start** : Utiliser des recommandations basées sur le contenu pour les nouveaux utilisateurs
