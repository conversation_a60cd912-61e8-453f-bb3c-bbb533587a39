# Guide d'Intégration du Système de Recommandation

Ce guide détaille les étapes nécessaires pour intégrer le système de recommandation de Retreat And Be dans une application ou un service.

## Table des Matières

1. [Architecture Générale](#architecture-générale)
2. [Prérequis](#prérequis)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [Intégration Frontend](#intégration-frontend)
6. [Intégration Backend](#intégration-backend)
7. [Authentification et Sécurité](#authentification-et-sécurité)
8. [Monitoring et Logging](#monitoring-et-logging)
9. [Dépannage](#dépannage)
10. [Bonnes Pratiques](#bonnes-pratiques)

## Architecture Générale

Le système de recommandation est conçu comme un service indépendant qui peut être intégré à différentes applications via des API REST. L'architecture générale est la suivante:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Application   │     │   Système de    │     │   Services      │
│   Cliente       │────▶│  Recommandation │────▶│   Externes      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │
                               ▼
                        ┌─────────────────┐
                        │   Base de       │
                        │   Données       │
                        └─────────────────┘
```

## Prérequis

Avant d'intégrer le système de recommandation, assurez-vous de disposer des éléments suivants:

- Node.js v14+ pour les applications JavaScript/TypeScript
- PostgreSQL 12+ pour la base de données
- Redis 6+ pour le cache
- Accès aux API Retreat And Be (clés API)
- Environnement de développement configuré

## Installation

### Via NPM (pour les applications Node.js)

```bash
npm install @retreat-and-be/recommendation-client
```

### Via Docker

```bash
docker pull retreatandbe/recommendation-service:latest
docker run -p 3000:3000 -e DATABASE_URL=**************************** retreatandbe/recommendation-service
```

### Déploiement Manuel

1. Clonez le dépôt:
   ```bash
   git clone https://github.com/retreat-and-be/recommendation-service.git
   cd recommendation-service
   ```

2. Installez les dépendances:
   ```bash
   npm install
   ```

3. Configurez l'environnement:
   ```bash
   cp .env.example .env
   # Éditez le fichier .env avec vos paramètres
   ```

4. Lancez le service:
   ```bash
   npm run start:prod
   ```

## Configuration

Le système de recommandation utilise un fichier de configuration pour définir son comportement. Voici les principales options:

### Fichier .env

```
# Base de données
DATABASE_URL=postgres://user:password@localhost:5432/recommendation

# Cache
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# API
API_PORT=3000
API_PREFIX=/api/v1

# Recommandation
RECOMMENDATION_DEFAULT_LIMIT=10
RECOMMENDATION_CACHE_ENABLED=true
RECOMMENDATION_HYBRID_METHOD=WEIGHTED

# Sécurité
JWT_SECRET=your_jwt_secret
API_KEY=your_api_key

# Logging
LOG_LEVEL=info
```

### Configuration Avancée

Pour une configuration plus avancée, modifiez le fichier `config/recommendation.config.ts`:

```typescript
export default {
  database: {
    // Configuration de la base de données
  },
  cache: {
    // Configuration du cache
  },
  api: {
    // Configuration de l'API
  },
  recommendation: {
    // Configuration des algorithmes de recommandation
  },
  security: {
    // Configuration de la sécurité
  },
  logging: {
    // Configuration du logging
  },
};
```

## Intégration Frontend

### Installation du Client JavaScript

```bash
npm install @retreat-and-be/recommendation-client
```

### Utilisation de Base

```javascript
import { RecommendationClient } from '@retreat-and-be/recommendation-client';

// Initialiser le client
const client = new RecommendationClient({
  apiUrl: 'https://api.retreatandbe.com/recommendations',
  apiKey: 'your_api_key',
});

// Obtenir des recommandations
async function getRecommendations(userId) {
  try {
    const recommendations = await client.getRecommendations({
      userId,
      type: 'RETREAT',
      limit: 10,
    });
    
    return recommendations;
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    return [];
  }
}
```

### Exemple React

```jsx
import React, { useState, useEffect } from 'react';
import { RecommendationClient } from '@retreat-and-be/recommendation-client';

const client = new RecommendationClient({
  apiUrl: process.env.REACT_APP_RECOMMENDATION_API_URL,
  apiKey: process.env.REACT_APP_RECOMMENDATION_API_KEY,
});

function RecommendationList({ userId }) {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchRecommendations() {
      try {
        setLoading(true);
        const data = await client.getRecommendations({
          userId,
          type: 'RETREAT',
          limit: 10,
        });
        setRecommendations(data);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    }

    fetchRecommendations();
  }, [userId]);

  if (loading) return <div>Loading recommendations...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="recommendation-list">
      <h2>Recommended for You</h2>
      <div className="recommendations">
        {recommendations.map(item => (
          <div key={item.id} className="recommendation-item">
            <img src={item.imageUrl} alt={item.title} />
            <h3>{item.title}</h3>
            <p>{item.description}</p>
            <div className="recommendation-score">Match: {Math.round(item.score * 100)}%</div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default RecommendationList;
```

## Intégration Backend

### Utilisation avec Node.js

```javascript
const { RecommendationService } = require('@retreat-and-be/recommendation-sdk');

// Initialiser le service
const recommendationService = new RecommendationService({
  apiUrl: 'https://api.retreatandbe.com/recommendations',
  apiKey: 'your_api_key',
});

// Express middleware
app.get('/api/recommendations', async (req, res) => {
  try {
    const userId = req.user.id;
    const recommendations = await recommendationService.getRecommendations({
      userId,
      type: req.query.type || 'RETREAT',
      limit: parseInt(req.query.limit, 10) || 10,
    });
    
    res.json(recommendations);
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    res.status(500).json({ error: 'Failed to fetch recommendations' });
  }
});
```

### Intégration avec d'Autres Langages

Le système de recommandation expose des API REST qui peuvent être consommées par n'importe quel langage capable d'effectuer des requêtes HTTP.

#### Exemple Python

```python
import requests

def get_recommendations(user_id, api_key, api_url, recommendation_type='RETREAT', limit=10):
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    params = {
        'userId': user_id,
        'type': recommendation_type,
        'limit': limit
    }
    
    response = requests.get(f'{api_url}/recommendations', headers=headers, params=params)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Error fetching recommendations: {response.status_code} - {response.text}")
```

## Authentification et Sécurité

Le système de recommandation prend en charge plusieurs méthodes d'authentification:

### Authentification par API Key

```javascript
const client = new RecommendationClient({
  apiUrl: 'https://api.retreatandbe.com/recommendations',
  apiKey: 'your_api_key',
});
```

### Authentification par JWT

```javascript
const client = new RecommendationClient({
  apiUrl: 'https://api.retreatandbe.com/recommendations',
  jwt: 'your_jwt_token',
});
```

### Authentification OAuth2

```javascript
const client = new RecommendationClient({
  apiUrl: 'https://api.retreatandbe.com/recommendations',
  oauth: {
    clientId: 'your_client_id',
    clientSecret: 'your_client_secret',
    tokenUrl: 'https://auth.retreatandbe.com/oauth/token',
  },
});
```

## Monitoring et Logging

Le système de recommandation fournit des métriques et des logs pour le monitoring:

### Métriques Prometheus

Le service expose des métriques Prometheus à l'endpoint `/metrics`:

```
# HELP recommendation_requests_total Total number of recommendation requests
# TYPE recommendation_requests_total counter
recommendation_requests_total{type="RETREAT"} 1234

# HELP recommendation_response_time_seconds Response time in seconds
# TYPE recommendation_response_time_seconds histogram
recommendation_response_time_seconds_bucket{le="0.1"} 2000
recommendation_response_time_seconds_bucket{le="0.5"} 3000
recommendation_response_time_seconds_bucket{le="1"} 3500
recommendation_response_time_seconds_bucket{le="2"} 4000
recommendation_response_time_seconds_bucket{le="+Inf"} 4100
recommendation_response_time_seconds_sum 1234.5
recommendation_response_time_seconds_count 4100
```

### Intégration avec ELK Stack

Le service peut envoyer des logs au format JSON à un serveur Logstash:

```
LOG_FORMAT=json
LOGSTASH_HOST=logstash.example.com
LOGSTASH_PORT=5000
```

## Dépannage

### Problèmes Courants

#### Erreur 401 Unauthorized

- Vérifiez que votre API key ou JWT est valide
- Assurez-vous que l'utilisateur a les permissions nécessaires

#### Erreur 429 Too Many Requests

- Respectez les limites de rate limiting
- Implémentez un mécanisme de retry avec exponential backoff

#### Recommandations Vides

- Vérifiez que l'utilisateur a suffisamment d'interactions
- Assurez-vous que le type de recommandation est valide

### Logs de Débogage

Pour activer les logs de débogage:

```
LOG_LEVEL=debug
```

## Bonnes Pratiques

1. **Mise en Cache**
   - Mettez en cache les recommandations côté client pour réduire la charge
   - Utilisez un TTL approprié selon la fréquence de mise à jour

2. **Gestion des Erreurs**
   - Implémentez une gestion robuste des erreurs
   - Prévoyez des fallbacks en cas d'indisponibilité du service

3. **Performance**
   - Limitez le nombre de recommandations demandées
   - Utilisez la pagination pour les grandes listes

4. **Sécurité**
   - Ne stockez jamais les API keys dans le code source
   - Utilisez HTTPS pour toutes les communications

5. **Feedback**
   - Envoyez des événements de feedback (clics, conversions)
   - Ces données améliorent la qualité des recommandations futures
