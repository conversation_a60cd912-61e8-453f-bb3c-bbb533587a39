# Guide d'Intégration des Données Externes

Ce document décrit l'architecture et l'utilisation du système d'intégration de données externes pour le système de recommandation de Retreat And Be.

## Architecture Générale

Le système d'intégration de données externes est composé de trois couches principales :

1. **Connecteurs API** : Interfaces avec les API tierces
2. **Service d'Enrichissement** : Traitement et enrichissement des recommandations
3. **Stockage et Cache** : Persistance et optimisation des données externes

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Connecteurs    │     │    Service      │     │   Stockage      │
│     API         │────▶│ d'Enrichissement│────▶│   et Cache      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                        │                       │
        ▼                        ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  APIs Externes  │     │ Recommandations │     │  Base de        │
│                 │     │                 │     │  Données        │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Connecteurs API

### Configuration

Les connecteurs API sont configurés dans le fichier `config/recommendation.config.ts` :

```typescript
externalData: {
  enabled: process.env.RECOMMENDATION_EXTERNAL_DATA_ENABLED === 'true',
  refreshInterval: parseInt(process.env.RECOMMENDATION_EXTERNAL_DATA_REFRESH_INTERVAL || '24', 10),
  apiKeys: {
    openWeatherMap: process.env.OPENWEATHERMAP_API_KEY,
    eventful: process.env.EVENTFUL_API_KEY,
    googleMaps: process.env.GOOGLE_MAPS_API_KEY,
    sncf: process.env.SNCF_API_KEY,
  },
  sources: {
    openWeatherMap: {
      enabled: process.env.OPENWEATHERMAP_ENABLED === 'true',
    },
    eventful: {
      enabled: process.env.EVENTFUL_ENABLED === 'true',
    },
    googleMaps: {
      enabled: process.env.GOOGLE_MAPS_ENABLED === 'true',
    },
    sncf: {
      enabled: process.env.SNCF_ENABLED === 'true',
    },
  },
}
```

### Connecteurs Disponibles

#### WeatherConnectorService

Récupère les données météorologiques actuelles et les prévisions.

```typescript
// Exemple d'utilisation
const weatherData = await weatherConnector.getCurrentWeather('Paris');
const forecast = await weatherConnector.getForecast({ lat: 48.8566, lon: 2.3522 }, 5);
```

#### EventsConnectorService

Récupère les événements à proximité d'une localisation.

```typescript
// Exemple d'utilisation
const events = await eventsConnector.getNearbyEvents('Paris', 10, ['wellness', 'yoga']);
const eventDetails = await eventsConnector.getEventDetails('event123');
```

#### TransportConnectorService

Calcule les itinéraires et les informations de transport.

```typescript
// Exemple d'utilisation
const routes = await transportConnector.getRoutes(
  { lat: 48.8566, lon: 2.3522 },
  { lat: 43.2965, lon: 5.3698 }
);
```

## Service d'Enrichissement

Le service `ExternalDataService` est responsable de l'enrichissement des recommandations avec des données externes.

### Méthodes Principales

#### `enrichItems`

Enrichit une liste d'éléments avec des données externes pertinentes.

```typescript
// Exemple d'utilisation
const enrichedItems = await externalDataService.enrichItems(recommendations, {
  location: user.location,
  date: new Date('2023-07-15'),
  includeTypes: [ExternalDataType.WEATHER, ExternalDataType.EVENT],
});
```

#### `getExternalData`

Récupère les données externes pour un utilisateur et un type de recommandation.

```typescript
// Exemple d'utilisation
const externalData = await externalDataService.getExternalData(userId, RecommendationType.RETREAT);
```

### Tâches Planifiées

Le service utilise des tâches CRON pour mettre à jour périodiquement les données externes :

```typescript
@Cron(CronExpression.EVERY_HOUR)
async refreshExternalData() {
  if (!this.enabled) {
    return;
  }
  
  this.logger.log('Refreshing external data...');
  await this.fetchAndStoreExternalData();
}
```

## Stockage et Cache

Les données externes sont stockées dans la base de données via le modèle `ExternalData` :

```typescript
model ExternalData {
  id            String   @id @default(uuid())
  dataType      String
  source        String
  title         String
  content       String?
  url           String?
  imageUrl      String?
  relevanceScore Float    @default(0.5)
  metadata      Json?
  applicableTypes String[]
  userId        String?
  createdAt     DateTime @default(now())
  expiresAt     DateTime
}
```

### Stratégie de Cache

Le système utilise un cache avec TTL (Time To Live) configurable pour optimiser les performances :

```typescript
ttl: {
  externalData: parseInt(process.env.RECOMMENDATION_CACHE_TTL_EXTERNAL_DATA || '3600', 10), // 1 heure
}
```

## Intégration avec le Système de Recommandation

### Dans HybridRecommendationService

```typescript
// Exemple d'enrichissement dans le service de recommandation hybride
const recommendations = await this.getBaseRecommendations(userId, type, options);

// Enrichir avec des données externes si nécessaire
if (options.enrichWithExternalData) {
  return this.enrichRecommendations(recommendations, userId, options);
}
```

## Bonnes Pratiques

1. **Gestion des Erreurs** : Toujours prévoir des mécanismes de fallback en cas d'échec des API externes
2. **Limitation des Requêtes** : Utiliser le cache pour limiter les appels aux API externes
3. **Pertinence** : Filtrer les données externes pour ne conserver que celles qui sont pertinentes
4. **Performance** : Utiliser des requêtes asynchrones pour ne pas bloquer le processus principal

## Exemples d'Utilisation

### Enrichissement de Recommandations de Retraites avec la Météo

```typescript
// Dans un contrôleur ou un service
async getRecommendedRetreats(userId: string) {
  const recommendations = await this.recommendationService.getRecommendations(
    userId,
    RecommendationType.RETREAT,
    {
      enrichWithExternalData: true,
      externalDataOptions: {
        includeTypes: [ExternalDataType.WEATHER, ExternalDataType.EVENT],
        location: await this.getUserLocation(userId),
      }
    }
  );
  
  return recommendations;
}
```

## Dépannage

### Problèmes Courants

1. **API Inaccessible** : Vérifier les clés API et les quotas
2. **Données Obsolètes** : Vérifier le fonctionnement des tâches CRON
3. **Performance Lente** : Optimiser les stratégies de cache

### Logs et Monitoring

Le service génère des logs détaillés pour faciliter le dépannage :

```
[ExternalDataService] Refreshing external data...
[WeatherConnectorService] Fetching weather data for Paris
[ExternalDataService] Stored 15 external data items
```
