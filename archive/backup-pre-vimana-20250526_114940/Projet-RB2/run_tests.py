#!/usr/bin/env python
"""
Script pour exécuter les tests.
"""

import os
import sys
import argparse
import subprocess

def parse_args():
    """
    Parse les arguments de la ligne de commande.
    
    Returns:
        Arguments parsés
    """
    parser = argparse.ArgumentParser(description="Exécute les tests")
    
    parser.add_argument(
        "--unit",
        action="store_true",
        help="Exécute les tests unitaires"
    )
    
    parser.add_argument(
        "--integration",
        action="store_true",
        help="Exécute les tests d'intégration"
    )
    
    parser.add_argument(
        "--all",
        action="store_true",
        help="Exécute tous les tests"
    )
    
    parser.add_argument(
        "--i18n",
        action="store_true",
        help="Exécute les tests d'internationalisation"
    )
    
    parser.add_argument(
        "--calendar",
        action="store_true",
        help="Exécute les tests d'intégration avec les calendriers"
    )
    
    parser.add_argument(
        "--maps",
        action="store_true",
        help="Exécute les tests d'intégration avec les cartes"
    )
    
    parser.add_argument(
        "--analytics",
        action="store_true",
        help="Exécute les tests d'intégration avec l'analyse de données"
    )
    
    parser.add_argument(
        "--notifications",
        action="store_true",
        help="Exécute les tests de notifications"
    )
    
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Génère un rapport de couverture de code"
    )
    
    parser.add_argument(
        "--html",
        action="store_true",
        help="Génère un rapport HTML"
    )
    
    return parser.parse_args()

def run_tests(args):
    """
    Exécute les tests.
    
    Args:
        args: Arguments parsés
    
    Returns:
        Code de retour
    """
    # Construire la commande
    cmd = ["pytest"]
    
    # Ajouter les marqueurs
    markers = []
    
    if args.unit:
        markers.append("unit")
    
    if args.integration:
        markers.append("integration")
    
    if args.i18n:
        markers.append("i18n")
    
    if args.calendar:
        markers.append("calendar")
    
    if args.maps:
        markers.append("maps")
    
    if args.analytics:
        markers.append("analytics")
    
    if args.notifications:
        markers.append("notifications")
    
    # Si aucun marqueur n'est spécifié et que --all n'est pas spécifié, exécuter les tests unitaires
    if not markers and not args.all:
        markers.append("unit")
    
    # Ajouter les marqueurs à la commande
    if markers and not args.all:
        cmd.append("-m")
        cmd.append(" or ".join(markers))
    
    # Ajouter les options de couverture
    if args.coverage:
        cmd.append("--cov=src")
        cmd.append("--cov-report=term")
        
        if args.html:
            cmd.append("--cov-report=html")
    
    # Exécuter la commande
    print(f"Exécution de la commande: {' '.join(cmd)}")
    return subprocess.call(cmd)

def main():
    """
    Point d'entrée principal.
    
    Returns:
        Code de retour
    """
    args = parse_args()
    return run_tests(args)

if __name__ == "__main__":
    sys.exit(main())
