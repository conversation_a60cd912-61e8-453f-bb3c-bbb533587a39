apiVersion: integreatly.org/v1alpha1
kind: GrafanaDashboard
metadata:
  name: financial-service-dashboard
spec:
  json: |
    {
      "title": "Financial Service Metrics",
      "panels": [
        {
          "type": "graph",
          "title": "Requêtes HTTP/Sec",
          "targets": [{
            "expr": "rate(http_request_duration_seconds_count[5m])",
            "legendFormat": "{{route}}"
          }]
        },
        {
          "type": "singlestat",
          "title": "Latence Moyenne",
          "targets": [{
            "expr": "avg(http_request_duration_seconds)"
          }]
        }
      ]
    }
