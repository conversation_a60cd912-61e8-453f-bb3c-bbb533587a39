replicaCount: 2

image:
  repository: quay.io/keycloak/keycloak
  tag: 22.0.1
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 8080
  
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: auth.retreatandbe.com
      paths:
        - path: /
          pathType: Prefix

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

livenessProbe:
  httpGet:
    path: /health/live
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /health/ready
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10

persistence:
  enabled: true
  storageClass: "standard"
  size: 5Gi

keycloak:
  extraEnv:
    - name: KEYCLOAK_ADMIN
      valueFrom:
        secretKeyRef:
          name: keycloak-credentials
          key: admin-user
    - name: KEYCLOAK_ADMIN_PASSWORD
      valueFrom:
        secretKeyRef:
          name: keycloak-credentials
          key: admin-password
    - name: KC_DB
      value: "postgres"
    - name: KC_DB_URL
      valueFrom:
        secretKeyRef:
          name: keycloak-db
          key: url
    - name: KC_DB_USERNAME
      valueFrom:
        secretKeyRef:
          name: keycloak-db
          key: username
    - name: KC_DB_PASSWORD
      valueFrom:
        secretKeyRef:
          name: keycloak-db
          key: password
    - name: KC_PROXY
      value: "edge"
    - name: KC_HOSTNAME
      value: "auth.retreatandbe.com"