# Development environment values for rb-new
global:
  environment: development
  imageRegistry: ""
  imagePullPolicy: IfNotPresent

# Reduce resource requirements for development
orchestrator:
  resources:
    limits:
      cpu: 300m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi

# Configure AI Engine services for development
aiEngine:
  recommender:
    resources:
      limits:
        cpu: 300m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
  
  chatbot:
    resources:
      limits:
        cpu: 300m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
  
  contentGenerator:
    resources:
      limits:
        cpu: 300m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
  
  analyticsEngine:
    resources:
      limits:
        cpu: 300m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi

# Enable development ingress
ingress:
  enabled: true
  hosts:
    - host: rb-new-dev.local
      paths:
        - path: /
          pathType: Prefix

# Use smaller persistence sizes for development
persistence:
  models:
    size: 5Gi
  logs:
    size: 2Gi

# Environment variables for development
env:
  common:
    - name: ENVIRONMENT
      value: "development"
    - name: LOG_LEVEL
      value: "DEBUG"
