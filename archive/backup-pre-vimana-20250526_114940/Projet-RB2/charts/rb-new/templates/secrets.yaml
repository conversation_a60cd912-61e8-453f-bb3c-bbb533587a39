apiVersion: v1
kind: Secret
metadata:
  name: {{ include "rb-new.fullname" . }}-api-keys
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
type: Opaque
data:
  {{- if .Values.secrets.openaiApiKey }}
  OPENAI_API_KEY: {{ .Values.secrets.openaiApiKey | b64enc | quote }}
  {{- end }}
  {{- if .Values.secrets.huggingfaceApiKey }}
  HUGGINGFACE_API_KEY: {{ .Values.secrets.huggingfaceApiKey | b64enc | quote }}
  {{- end }}
  {{- range $key, $value := .Values.secrets.custom }}
  {{ $key }}: {{ $value | b64enc | quote }}
  {{- end }}
