{{- if .Values.aiEngine.featureFlags.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rb-new.featureFlags.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: feature-flags
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.aiEngine.featureFlags.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rb-new.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: feature-flags
{{- end }}
