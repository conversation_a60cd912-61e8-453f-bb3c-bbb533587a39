{{- if .Values.mlflow.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rb-new.mlflow.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: mlflow
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.mlflow.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rb-new.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: mlflow
{{- end }}
