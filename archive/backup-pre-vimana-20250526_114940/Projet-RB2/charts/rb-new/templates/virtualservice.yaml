{{- if .Values.istio.enabled }}
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ include "rb-new.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  hosts:
  {{- range .Values.istio.hosts }}
  - {{ . | quote }}
  {{- end }}
  {{- if .Values.istio.gateways }}
  gateways:
  {{- range .Values.istio.gateways }}
  - {{ . | quote }}
  {{- end }}
  {{- end }}
  http:
  - match:
    - uri:
        prefix: /api/orchestrator
    rewrite:
      uri: /
    route:
    - destination:
        host: {{ include "rb-new.orchestrator.fullname" . }}
        port:
          number: {{ .Values.orchestrator.service.port }}
  {{- if .Values.aiEngine.recommender.enabled }}
  - match:
    - uri:
        prefix: /api/recommender
    rewrite:
      uri: /
    route:
    - destination:
        host: {{ include "rb-new.recommender.fullname" . }}
        port:
          number: {{ .Values.aiEngine.recommender.service.port }}
  {{- end }}
  {{- if .Values.aiEngine.chatbot.enabled }}
  - match:
    - uri:
        prefix: /api/chatbot
    rewrite:
      uri: /
    route:
    - destination:
        host: {{ include "rb-new.chatbot.fullname" . }}
        port:
          number: {{ .Values.aiEngine.chatbot.service.port }}
  {{- end }}
  {{- if .Values.aiEngine.contentGenerator.enabled }}
  - match:
    - uri:
        prefix: /api/content
    rewrite:
      uri: /
    route:
    - destination:
        host: {{ include "rb-new.contentGenerator.fullname" . }}
        port:
          number: {{ .Values.aiEngine.contentGenerator.service.port }}
  {{- end }}
  {{- if .Values.aiEngine.analyticsEngine.enabled }}
  - match:
    - uri:
        prefix: /api/analytics
    rewrite:
      uri: /
    route:
    - destination:
        host: {{ include "rb-new.analyticsEngine.fullname" . }}
        port:
          number: {{ .Values.aiEngine.analyticsEngine.service.port }}
  {{- end }}
  {{- if .Values.aiEngine.featureFlags.enabled }}
  - match:
    - uri:
        prefix: /api/features
    rewrite:
      uri: /
    route:
    - destination:
        host: {{ include "rb-new.featureFlags.fullname" . }}
        port:
          number: {{ .Values.aiEngine.featureFlags.service.port }}
  {{- end }}
  {{- if .Values.aiEngine.virtualCoach.enabled }}
  - match:
    - uri:
        prefix: /api/coach
    rewrite:
      uri: /
    route:
    - destination:
        host: {{ include "rb-new.virtualCoach.fullname" . }}
        port:
          number: {{ .Values.aiEngine.virtualCoach.service.port }}
  {{- end }}
  {{- if .Values.mlflow.enabled }}
  - match:
    - uri:
        prefix: /mlflow
    rewrite:
      uri: /
    route:
    - destination:
        host: {{ include "rb-new.mlflow.fullname" . }}
        port:
          number: {{ .Values.mlflow.service.port }}
  {{- end }}
{{- end }}
