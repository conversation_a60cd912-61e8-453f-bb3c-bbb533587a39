{{- if .Values.modsecurity.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "rb-new.fullname" . }}-modsecurity-config
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
data:
  modsecurity.conf: |
    # -- Rule engine initialization ----------------------------------------------
    Sec<PERSON>uleE<PERSON>ine {{ .Values.modsecurity.ruleEngine }}
    
    # -- Request body handling --------------------------------------------------
    SecRequestBodyAccess On
    SecRequestBodyLimit 10485760
    SecRequestBodyNoFilesLimit 131072
    SecRequestBodyInMemoryLimit 131072
    SecRequestBodyLimitAction Reject
    
    # -- Response body handling -------------------------------------------------
    SecResponseBodyAccess On
    SecResponseBodyMimeType text/plain text/html text/xml application/json
    SecResponseBodyLimit 10485760
    SecResponseBodyLimitAction ProcessPartial
    
    # -- Logging configuration --------------------------------------------------
    SecAuditEngine {{ .Values.modsecurity.auditEngine }}
    SecAuditLogRelevantStatus "^(?:5|4(?!04))"
    SecAuditLogParts ABIJDEFHZ
    SecAuditLogType Serial
    SecAuditLog /var/log/modsec_audit.log
    
    # -- Debug log configuration ------------------------------------------------
    SecDebugLog /var/log/modsec_debug.log
    SecDebugLogLevel {{ .Values.modsecurity.debugLevel }}
    
    # -- OWASP Core Rule Set ----------------------------------------------------
    Include /etc/modsecurity.d/owasp-crs/crs-setup.conf
    Include /etc/modsecurity.d/owasp-crs/rules/*.conf
    
    # -- Custom rules ----------------------------------------------------------
    # Protection contre les injections SQL
    SecRule REQUEST_COOKIES|REQUEST_COOKIES_NAMES|REQUEST_FILENAME|REQUEST_HEADERS|REQUEST_HEADERS_NAMES|REQUEST_BODY|REQUEST_LINE|ARGS|ARGS_NAMES "(?i:(?:select|union|insert|update|delete|drop|alter).*(?:from|into|where|table))" \
        "id:1000,phase:2,deny,status:403,log,msg:'SQL Injection Attack'"
    
    # Protection contre les XSS
    SecRule REQUEST_COOKIES|REQUEST_COOKIES_NAMES|REQUEST_FILENAME|REQUEST_HEADERS|REQUEST_HEADERS_NAMES|REQUEST_BODY|REQUEST_LINE|ARGS|ARGS_NAMES "(?i:<script.*?>)" \
        "id:1001,phase:2,deny,status:403,log,msg:'XSS Attack'"
    
    # Protection contre les attaques de traversée de répertoire
    SecRule REQUEST_URI|REQUEST_HEADERS|ARGS "(?:\.\.|\/\.\.)" \
        "id:1002,phase:1,deny,status:403,log,msg:'Directory Traversal Attack'"
    
    # Protection contre les attaques par inclusion de fichier
    SecRule ARGS "(?:(?:https?|ftp|php|data|file)://)" \
        "id:1003,phase:2,deny,status:403,log,msg:'Remote File Inclusion Attack'"
    
    # Protection contre les attaques par commande
    SecRule REQUEST_COOKIES|REQUEST_COOKIES_NAMES|REQUEST_FILENAME|REQUEST_HEADERS|REQUEST_HEADERS_NAMES|REQUEST_BODY|REQUEST_LINE|ARGS|ARGS_NAMES "(?:;|\||\`|\$\(|\$\{|&gt;|&lt;)" \
        "id:1004,phase:2,deny,status:403,log,msg:'Command Injection Attack'"
    
    # Whitelist pour les endpoints spécifiques
    {{- range .Values.modsecurity.whitelist }}
    SecRule REQUEST_URI "^{{ . }}$" "id:2000,phase:1,allow,msg:'Whitelisted URL'"
    {{- end }}
{{- end }}
