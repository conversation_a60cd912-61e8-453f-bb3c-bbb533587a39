{{- if .Values.auth.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "rb-new.fullname" . }}-auth-config
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
data:
  AUTH_ENABLED: "true"
  AUTH_PROVIDER: {{ .Values.auth.provider | quote }}
  AUTH_REALM: {{ .Values.auth.realm | quote }}
  AUTH_CLIENT_ID: {{ .Values.auth.clientId | quote }}
  AUTH_SERVER_URL: {{ .Values.auth.serverUrl | quote }}
  AUTH_REDIRECT_URI: {{ .Values.auth.redirectUri | quote }}
  {{- if eq .Values.auth.provider "keycloak" }}
  KEYCLOAK_PUBLIC_KEY: {{ .Values.auth.keycloak.publicKey | quote }}
  {{- end }}
  {{- if eq .Values.auth.provider "auth0" }}
  AUTH0_DOMAIN: {{ .Values.auth.auth0.domain | quote }}
  AUTH0_AUDIENCE: {{ .Values.auth.auth0.audience | quote }}
  {{- end }}
{{- end }}
