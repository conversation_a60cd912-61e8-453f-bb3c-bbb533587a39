apiVersion: v1
kind: Service
metadata:
  name: {{ include "rb-new.orchestrator.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: orchestrator
spec:
  type: {{ .Values.orchestrator.service.type }}
  ports:
    - port: {{ .Values.orchestrator.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rb-new.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: orchestrator
