{{- if .Values.aiEngine.analyticsEngine.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rb-new.analyticsEngine.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: analytics-engine
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.aiEngine.analyticsEngine.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rb-new.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: analytics-engine
{{- end }}
