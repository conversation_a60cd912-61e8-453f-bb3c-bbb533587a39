{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "rb-new.fullname" . }}-role
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
rules:
  - apiGroups: [""]
    resources: ["pods", "services", "configmaps", "secrets"]
    verbs: ["get", "list", "watch"]
  {{- if .Values.rbac.allowConfigUpdate }}
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["update", "patch"]
  {{- end }}
  {{- if .Values.rbac.allowSecretUpdate }}
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["update", "patch"]
  {{- end }}
  {{- with .Values.rbac.additionalRules }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "rb-new.fullname" . }}-rolebinding
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "rb-new.fullname" . }}-role
subjects:
- kind: ServiceAccount
  name: {{ include "rb-new.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
