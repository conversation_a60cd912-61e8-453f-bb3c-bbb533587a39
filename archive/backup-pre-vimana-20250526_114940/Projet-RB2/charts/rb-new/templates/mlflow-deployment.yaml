{{- if .Values.mlflow.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "rb-new.mlflow.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: mlflow
spec:
  replicas: 1
  selector:
    matchLabels:
      {{- include "rb-new.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: mlflow
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "rb-new.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: mlflow
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "rb-new.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}-mlflow
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.mlflow.image.repository }}:{{ .Values.mlflow.image.tag | default "latest" }}"
          imagePullPolicy: {{ .Values.mlflow.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.mlflow.service.port }}
              protocol: TCP
          command:
            - mlflow
            - server
            - --host
            - 0.0.0.0
            - --port
            - "{{ .Values.mlflow.service.port }}"
            - --backend-store-uri
            - file:///mlruns
            - --default-artifact-root
            - file:///mlruns/artifacts
          resources:
            {{- toYaml .Values.mlflow.resources | nindent 12 }}
          volumeMounts:
            - name: mlflow-data
              mountPath: /mlruns
      volumes:
        - name: mlflow-data
          {{- if .Values.mlflow.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "rb-new.fullname" . }}-mlflow
          {{- else }}
          emptyDir: {}
          {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
