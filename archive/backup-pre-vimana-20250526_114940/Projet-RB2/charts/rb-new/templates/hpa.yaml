{{- if .Values.orchestrator.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "rb-new.orchestrator.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: orchestrator
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "rb-new.orchestrator.fullname" . }}
  minReplicas: {{ .Values.orchestrator.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.orchestrator.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.orchestrator.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.orchestrator.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.orchestrator.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.orchestrator.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
    {{- with .Values.orchestrator.autoscaling.customMetrics }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  behavior:
    scaleDown:
      stabilizationWindowSeconds: {{ .Values.orchestrator.autoscaling.scaleDownStabilizationWindowSeconds | default 300 }}
      policies:
      - type: Percent
        value: {{ .Values.orchestrator.autoscaling.scaleDownPercentage | default 10 }}
        periodSeconds: {{ .Values.orchestrator.autoscaling.scaleDownPeriodSeconds | default 60 }}
    scaleUp:
      stabilizationWindowSeconds: {{ .Values.orchestrator.autoscaling.scaleUpStabilizationWindowSeconds | default 0 }}
      policies:
      - type: Percent
        value: {{ .Values.orchestrator.autoscaling.scaleUpPercentage | default 100 }}
        periodSeconds: {{ .Values.orchestrator.autoscaling.scaleUpPeriodSeconds | default 15 }}
      - type: Pods
        value: {{ .Values.orchestrator.autoscaling.scaleUpPods | default 4 }}
        periodSeconds: {{ .Values.orchestrator.autoscaling.scaleUpPeriodSeconds | default 15 }}
      selectPolicy: Max
{{- end }}

{{- if and .Values.aiEngine.recommender.enabled .Values.aiEngine.recommender.autoscaling.enabled }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "rb-new.recommender.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: recommender
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "rb-new.recommender.fullname" . }}
  minReplicas: {{ .Values.aiEngine.recommender.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.aiEngine.recommender.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.aiEngine.recommender.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.aiEngine.recommender.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.aiEngine.recommender.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.aiEngine.recommender.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}

{{- if and .Values.aiEngine.chatbot.enabled .Values.aiEngine.chatbot.autoscaling.enabled }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "rb-new.chatbot.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: chatbot
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "rb-new.chatbot.fullname" . }}
  minReplicas: {{ .Values.aiEngine.chatbot.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.aiEngine.chatbot.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.aiEngine.chatbot.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.aiEngine.chatbot.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.aiEngine.chatbot.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.aiEngine.chatbot.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
