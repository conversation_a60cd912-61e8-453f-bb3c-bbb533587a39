apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "rb-new.orchestrator.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: orchestrator
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "rb-new.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: orchestrator
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "rb-new.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: orchestrator
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "rb-new.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- if .Values.migrations.enabled }}
      initContainers:
        - name: db-migrations
          image: "{{ .Values.global.imageRegistry }}{{ .Values.migrations.image.repository }}:{{ .Values.migrations.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.migrations.image.pullPolicy }}
          command: ["python", "-m", "alembic", "upgrade", "head"]
          env:
            {{- range .Values.env.database }}
            - name: {{ .name }}
              {{- if .value }}
              value: {{ .value | quote }}
              {{- else if .valueFrom }}
              valueFrom:
                {{- toYaml .valueFrom | nindent 16 }}
              {{- end }}
            {{- end }}
            - name: MIGRATION_DIR
              value: "/app/migrations"
          resources:
            {{- toYaml .Values.migrations.resources | nindent 12 }}
          volumeMounts:
            - name: migrations-volume
              mountPath: /app/migrations
      {{- end }}
      containers:
        {{- if .Values.metrics.enabled }}
        - name: metrics-exporter
          image: "{{ .Values.metrics.image.repository }}:{{ .Values.metrics.image.tag }}"
          imagePullPolicy: {{ .Values.metrics.image.pullPolicy }}
          ports:
            - name: metrics
              containerPort: {{ .Values.metrics.port }}
              protocol: TCP
          resources:
            {{- toYaml .Values.metrics.resources | nindent 12 }}
          env:
            - name: METRICS_PORT
              value: "{{ .Values.metrics.port }}"
            - name: SCRAPE_INTERVAL
              value: "{{ .Values.metrics.scrapeInterval }}"
            - name: TARGET_SERVICE
              value: "localhost:{{ .Values.orchestrator.service.port }}"
        {{- end }}
        - name: {{ .Chart.Name }}-orchestrator
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.global.imageRegistry }}{{ .Values.orchestrator.image.repository }}:{{ .Values.orchestrator.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.orchestrator.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.orchestrator.service.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
          resources:
            {{- toYaml .Values.orchestrator.resources | nindent 12 }}
          env:
            {{- range .Values.env.common }}
            - name: {{ .name }}
              value: {{ .value | quote }}
            {{- end }}
            {{- range .Values.env.orchestrator }}
            - name: {{ .name }}
              value: {{ .value | quote }}
            {{- end }}
            {{- range .Values.env.database }}
            - name: {{ .name }}
              {{- if .value }}
              value: {{ .value | quote }}
              {{- else if .valueFrom }}
              valueFrom:
                {{- toYaml .valueFrom | nindent 16 }}
              {{- end }}
            {{- end }}
            {{- range .Values.env.redis }}
            - name: {{ .name }}
              {{- if .value }}
              value: {{ .value | quote }}
              {{- else if .valueFrom }}
              valueFrom:
                {{- toYaml .valueFrom | nindent 16 }}
              {{- end }}
            {{- end }}
            # API keys from secrets
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "rb-new.fullname" . }}-api-keys
                  key: OPENAI_API_KEY
                  optional: true
            - name: HUGGINGFACE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "rb-new.fullname" . }}-api-keys
                  key: HUGGINGFACE_API_KEY
                  optional: true
          volumeMounts:
            - name: models-volume
              mountPath: /app/models
            - name: logs-volume
              mountPath: /app/logs
            - name: config-volume
              mountPath: /app/config
            - name: app-config
              mountPath: /app/config/app-config.json
              subPath: app-config.json
      volumes:
        - name: models-volume
          {{- if .Values.persistence.models.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "rb-new.fullname" . }}-models
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: logs-volume
          {{- if .Values.persistence.logs.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "rb-new.fullname" . }}-logs
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: config-volume
          configMap:
            name: {{ include "rb-new.fullname" . }}-config
        - name: app-config
          configMap:
            name: {{ include "rb-new.fullname" . }}-config
            items:
              - key: app-config.json
                path: app-config.json
        {{- if .Values.migrations.enabled }}
        - name: migrations-volume
          {{- if .Values.migrations.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "rb-new.fullname" . }}-migrations
          {{- else }}
          emptyDir: {}
          {{- end }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
