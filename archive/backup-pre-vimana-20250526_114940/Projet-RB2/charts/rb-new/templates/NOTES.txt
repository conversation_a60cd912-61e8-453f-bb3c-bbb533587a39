Thank you for installing {{ .Chart.Name }}.

Your release is named {{ .Release.Name }} and deployed to namespace {{ .Release.Namespace }}.

To get the status of the deployment, run:
  kubectl get pods -n {{ .Release.Namespace }} -l "app.kubernetes.io/instance={{ .Release.Name }}"

{{- if .Values.ingress.enabled }}
The application can be accessed via the following URLs:
{{- range .Values.ingress.hosts }}
  http{{ if $.Values.ingress.tls }}s{{ end }}://{{ .host }}
{{- end }}
{{- else }}
The application can be accessed within the cluster at:
  http://{{ include "rb-new.orchestrator.fullname" . }}.{{ .Release.Namespace }}.svc.cluster.local:{{ .Values.orchestrator.service.port }}

To access the application from outside the cluster, you can use port-forwarding:
  kubectl port-forward -n {{ .Release.Namespace }} svc/{{ include "rb-new.orchestrator.fullname" . }} {{ .Values.orchestrator.service.port }}:{{ .Values.orchestrator.service.port }}

Then access the application at:
  http://localhost:{{ .Values.orchestrator.service.port }}
{{- end }}

Available services:
  - Orchestrator: {{ include "rb-new.orchestrator.fullname" . }}:{{ .Values.orchestrator.service.port }}
{{- if .Values.aiEngine.recommender.enabled }}
  - Recommender: {{ include "rb-new.recommender.fullname" . }}:{{ .Values.aiEngine.recommender.service.port }}
{{- end }}
{{- if .Values.aiEngine.chatbot.enabled }}
  - Chatbot: {{ include "rb-new.chatbot.fullname" . }}:{{ .Values.aiEngine.chatbot.service.port }}
{{- end }}
{{- if .Values.aiEngine.contentGenerator.enabled }}
  - Content Generator: {{ include "rb-new.contentGenerator.fullname" . }}:{{ .Values.aiEngine.contentGenerator.service.port }}
{{- end }}
{{- if .Values.aiEngine.analyticsEngine.enabled }}
  - Analytics Engine: {{ include "rb-new.analyticsEngine.fullname" . }}:{{ .Values.aiEngine.analyticsEngine.service.port }}
{{- end }}
{{- if .Values.aiEngine.featureFlags.enabled }}
  - Feature Flags: {{ include "rb-new.featureFlags.fullname" . }}:{{ .Values.aiEngine.featureFlags.service.port }}
{{- end }}
{{- if .Values.aiEngine.virtualCoach.enabled }}
  - Virtual Coach: {{ include "rb-new.virtualCoach.fullname" . }}:{{ .Values.aiEngine.virtualCoach.service.port }}
{{- end }}
{{- if .Values.mlflow.enabled }}
  - MLflow: {{ include "rb-new.mlflow.fullname" . }}:{{ .Values.mlflow.service.port }}
{{- end }}

{{- if .Values.mlflow.enabled }}
To access MLflow UI, run:
  kubectl port-forward -n {{ .Release.Namespace }} svc/{{ include "rb-new.mlflow.fullname" . }} {{ .Values.mlflow.service.port }}:{{ .Values.mlflow.service.port }}

Then access MLflow at:
  http://localhost:{{ .Values.mlflow.service.port }}
{{- end }}

For more information, see the documentation at:
  https://github.com/retreatandbe/rb-new/docs
