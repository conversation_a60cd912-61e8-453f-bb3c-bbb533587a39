{{- if and .Values.auth.enabled .Values.auth.middleware.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "rb-new.fullname" . }}-auth-middleware
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: auth-middleware
spec:
  replicas: {{ .Values.auth.middleware.replicaCount }}
  selector:
    matchLabels:
      {{- include "rb-new.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: auth-middleware
  template:
    metadata:
      labels:
        {{- include "rb-new.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: auth-middleware
    spec:
      serviceAccountName: {{ include "rb-new.serviceAccountName" . }}
      containers:
        - name: auth-middleware
          image: "{{ .Values.auth.middleware.image.repository }}:{{ .Values.auth.middleware.image.tag }}"
          imagePullPolicy: {{ .Values.auth.middleware.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.auth.middleware.service.port }}
              protocol: TCP
          env:
            - name: AUTH_PROVIDER
              valueFrom:
                configMapKeyRef:
                  name: {{ include "rb-new.fullname" . }}-auth-config
                  key: AUTH_PROVIDER
            - name: AUTH_REALM
              valueFrom:
                configMapKeyRef:
                  name: {{ include "rb-new.fullname" . }}-auth-config
                  key: AUTH_REALM
            - name: AUTH_CLIENT_ID
              valueFrom:
                configMapKeyRef:
                  name: {{ include "rb-new.fullname" . }}-auth-config
                  key: AUTH_CLIENT_ID
            - name: AUTH_SERVER_URL
              valueFrom:
                configMapKeyRef:
                  name: {{ include "rb-new.fullname" . }}-auth-config
                  key: AUTH_SERVER_URL
            - name: AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "rb-new.fullname" . }}-auth-secret
                  key: client-secret
          resources:
            {{- toYaml .Values.auth.middleware.resources | nindent 12 }}
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rb-new.fullname" . }}-auth-middleware
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: auth-middleware
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.auth.middleware.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rb-new.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: auth-middleware
{{- end }}
