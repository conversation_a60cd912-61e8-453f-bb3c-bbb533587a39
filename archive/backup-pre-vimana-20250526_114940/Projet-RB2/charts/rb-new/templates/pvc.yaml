{{- if .Values.persistence.models.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "rb-new.fullname" . }}-models
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: {{ .Values.persistence.models.size }}
{{- end }}

{{- if .Values.persistence.logs.enabled }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "rb-new.fullname" . }}-logs
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: {{ .Values.persistence.logs.size }}
{{- end }}

{{- if and .Values.mlflow.enabled .Values.mlflow.persistence.enabled }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "rb-new.fullname" . }}-mlflow
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: {{ .Values.mlflow.persistence.size }}
{{- end }}

{{- if and .Values.migrations.enabled .Values.migrations.persistence.enabled }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "rb-new.fullname" . }}-migrations
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: {{ .Values.migrations.persistence.size }}
{{- end }}

{{- if and .Values.backup.enabled .Values.backup.persistence.enabled }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "rb-new.fullname" . }}-backup
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: {{ .Values.backup.persistence.size }}
{{- end }}
