{{- if .Values.rateLimiter.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "rb-new.fullname" . }}-rate-limited
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
  annotations:
    kubernetes.io/ingress.class: {{ .Values.rateLimiter.ingressClass | quote }}
    {{- if eq .Values.rateLimiter.type "nginx" }}
    nginx.ingress.kubernetes.io/limit-rps: {{ .Values.rateLimiter.requestsPerSecond | quote }}
    nginx.ingress.kubernetes.io/limit-connections: {{ .Values.rateLimiter.connections | quote }}
    nginx.ingress.kubernetes.io/limit-burst-multiplier: {{ .Values.rateLimiter.burstMultiplier | quote }}
    nginx.ingress.kubernetes.io/limit-req-status-code: {{ .Values.rateLimiter.statusCode | quote }}
    nginx.ingress.kubernetes.io/limit-whitelist: {{ .Values.rateLimiter.whitelist | quote }}
    {{- end }}
    {{- if eq .Values.rateLimiter.type "kong" }}
    konghq.com/plugins: {{ include "rb-new.fullname" . }}-rate-limiting
    {{- end }}
spec:
  rules:
  - host: {{ .Values.rateLimiter.host | quote }}
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: {{ include "rb-new.orchestrator.fullname" . }}
            port:
              number: {{ .Values.orchestrator.service.port }}
{{- end }}

{{- if and .Values.rateLimiter.enabled (eq .Values.rateLimiter.type "kong") }}
---
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: {{ include "rb-new.fullname" . }}-rate-limiting
spec:
  plugin: rate-limiting
  config:
    second: {{ .Values.rateLimiter.requestsPerSecond }}
    minute: {{ .Values.rateLimiter.requestsPerMinute }}
    hour: {{ .Values.rateLimiter.requestsPerHour }}
    policy: local
    fault_tolerant: true
    hide_client_headers: false
    redis_timeout: 2000
    limit_by: ip
{{- end }}
