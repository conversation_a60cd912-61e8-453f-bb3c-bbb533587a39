{{- if .Values.trivy.enabled }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "rb-new.fullname" . }}-trivy-scan
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  schedule: {{ .Values.trivy.schedule | quote }}
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            {{- include "rb-new.selectorLabels" . | nindent 12 }}
            app.kubernetes.io/component: trivy-scan
        spec:
          serviceAccountName: {{ include "rb-new.serviceAccountName" . }}
          containers:
          - name: trivy
            image: "{{ .Values.trivy.image.repository }}:{{ .Values.trivy.image.tag }}"
            imagePullPolicy: {{ .Values.trivy.image.pullPolicy }}
            command:
            - /bin/sh
            - -c
            - |
              # Scan orchestrator image
              echo "Scanning {{ .Values.orchestrator.image.repository }}:{{ .Values.orchestrator.image.tag | default .Chart.AppVersion }}"
              trivy image --severity {{ .Values.trivy.severity }} --exit-code {{ .Values.trivy.exitCode }} {{ .Values.global.imageRegistry }}{{ .Values.orchestrator.image.repository }}:{{ .Values.orchestrator.image.tag | default .Chart.AppVersion }}
              
              # Scan recommender image
              echo "Scanning {{ .Values.aiEngine.recommender.image.repository }}:{{ .Values.aiEngine.recommender.image.tag | default .Chart.AppVersion }}"
              trivy image --severity {{ .Values.trivy.severity }} --exit-code {{ .Values.trivy.exitCode }} {{ .Values.global.imageRegistry }}{{ .Values.aiEngine.recommender.image.repository }}:{{ .Values.aiEngine.recommender.image.tag | default .Chart.AppVersion }}
              
              # Scan chatbot image
              echo "Scanning {{ .Values.aiEngine.chatbot.image.repository }}:{{ .Values.aiEngine.chatbot.image.tag | default .Chart.AppVersion }}"
              trivy image --severity {{ .Values.trivy.severity }} --exit-code {{ .Values.trivy.exitCode }} {{ .Values.global.imageRegistry }}{{ .Values.aiEngine.chatbot.image.repository }}:{{ .Values.aiEngine.chatbot.image.tag | default .Chart.AppVersion }}
              
              # Scan content generator image
              echo "Scanning {{ .Values.aiEngine.contentGenerator.image.repository }}:{{ .Values.aiEngine.contentGenerator.image.tag | default .Chart.AppVersion }}"
              trivy image --severity {{ .Values.trivy.severity }} --exit-code {{ .Values.trivy.exitCode }} {{ .Values.global.imageRegistry }}{{ .Values.aiEngine.contentGenerator.image.repository }}:{{ .Values.aiEngine.contentGenerator.image.tag | default .Chart.AppVersion }}
              
              # Scan analytics engine image
              echo "Scanning {{ .Values.aiEngine.analyticsEngine.image.repository }}:{{ .Values.aiEngine.analyticsEngine.image.tag | default .Chart.AppVersion }}"
              trivy image --severity {{ .Values.trivy.severity }} --exit-code {{ .Values.trivy.exitCode }} {{ .Values.global.imageRegistry }}{{ .Values.aiEngine.analyticsEngine.image.repository }}:{{ .Values.aiEngine.analyticsEngine.image.tag | default .Chart.AppVersion }}
            resources:
              {{- toYaml .Values.trivy.resources | nindent 14 }}
          restartPolicy: OnFailure
{{- end }}
