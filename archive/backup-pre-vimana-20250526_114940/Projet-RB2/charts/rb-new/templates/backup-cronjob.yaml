{{- if .Values.backup.enabled }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "rb-new.fullname" . }}-backup
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: backup
spec:
  schedule: {{ .Values.backup.schedule | quote }}
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: {{ .Values.backup.successfulJobsHistoryLimit }}
  failedJobsHistoryLimit: {{ .Values.backup.failedJobsHistoryLimit }}
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            {{- include "rb-new.selectorLabels" . | nindent 12 }}
            app.kubernetes.io/component: backup
        spec:
          serviceAccountName: {{ include "rb-new.serviceAccountName" . }}
          restartPolicy: OnFailure
          containers:
            - name: backup
              image: "{{ .Values.backup.image.repository }}:{{ .Values.backup.image.tag }}"
              imagePullPolicy: {{ .Values.backup.image.pullPolicy }}
              command:
                - "/bin/sh"
                - "-c"
                - |
                  TIMESTAMP=$(date +%Y%m%d%H%M%S)
                  BACKUP_FILE="/backups/rb-new-db-${TIMESTAMP}.sql.gz"
                  
                  echo "Starting backup to ${BACKUP_FILE}..."
                  
                  # Perform database backup
                  PGPASSWORD=${DB_PASSWORD} pg_dump -h ${DB_HOST} -U ${DB_USER} -d ${DB_NAME} -F c | gzip > ${BACKUP_FILE}
                  
                  # Check if backup was successful
                  if [ $? -eq 0 ]; then
                    echo "Backup completed successfully."
                    
                    # Cleanup old backups if retention is set
                    if [ "${BACKUP_RETENTION}" -gt 0 ]; then
                      echo "Cleaning up backups older than ${BACKUP_RETENTION} days..."
                      find /backups -name "rb-new-db-*.sql.gz" -type f -mtime +${BACKUP_RETENTION} -delete
                    fi
                    
                    # Upload to cloud storage if enabled
                    if [ "${UPLOAD_TO_CLOUD}" = "true" ]; then
                      echo "Uploading backup to cloud storage..."
                      # Add cloud storage upload commands here
                    fi
                  else
                    echo "Backup failed!"
                    exit 1
                  fi
              env:
                - name: BACKUP_RETENTION
                  value: {{ .Values.backup.retentionDays | quote }}
                - name: UPLOAD_TO_CLOUD
                  value: {{ .Values.backup.uploadToCloud | quote }}
                {{- range .Values.env.database }}
                - name: {{ .name }}
                  {{- if .value }}
                  value: {{ .value | quote }}
                  {{- else if .valueFrom }}
                  valueFrom:
                    {{- toYaml .valueFrom | nindent 20 }}
                  {{- end }}
                {{- end }}
              resources:
                {{- toYaml .Values.backup.resources | nindent 16 }}
              volumeMounts:
                - name: backup-volume
                  mountPath: /backups
          volumes:
            - name: backup-volume
              {{- if .Values.backup.persistence.enabled }}
              persistentVolumeClaim:
                claimName: {{ include "rb-new.fullname" . }}-backup
              {{- else }}
              emptyDir: {}
              {{- end }}
{{- end }}
