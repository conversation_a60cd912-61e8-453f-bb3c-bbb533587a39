# R&B new Helm Chart

## Description
This Helm chart deploys the R&B new AI Layer for the Retreat And Be platform. It includes the AI orchestrator and various AI engine microservices such as recommender, chatbot, content generator, analytics engine, feature flags, and virtual coach.

## Prerequisites
- Kubernetes 1.19+
- Helm 3.2.0+
- PV provisioner support in the underlying infrastructure (if persistence is enabled)

## Installation

```bash
# Add the required Helm repositories
helm repo add bitnami https://charts.bitnami.com/bitnami

# Install the chart
helm install rb-new ./charts/rb-new
```

## Configuration

### Global Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `global.environment` | Environment (development, staging, production) | `development` |
| `global.imageRegistry` | Global Docker image registry | `""` |
| `global.imagePullPolicy` | Global Docker image pull policy | `IfNotPresent` |

### Orchestrator Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `orchestrator.image.repository` | Orchestrator image repository | `rb-new-orchestrator` |
| `orchestrator.image.tag` | Orchestrator image tag | `latest` |
| `orchestrator.service.port` | Orchestrator service port | `8000` |
| `orchestrator.resources` | Orchestrator CPU/Memory resource requests/limits | See `values.yaml` |
| `orchestrator.autoscaling.enabled` | Enable autoscaling for the orchestrator | `false` |

### AI Engine Microservices Parameters

Each microservice has similar configuration parameters:

| Parameter | Description | Default |
|-----------|-------------|---------|
| `aiEngine.<service>.enabled` | Enable the service | `true` |
| `aiEngine.<service>.image.repository` | Service image repository | `rb-new-<service>` |
| `aiEngine.<service>.image.tag` | Service image tag | `latest` |
| `aiEngine.<service>.service.port` | Service port | Varies by service |
| `aiEngine.<service>.resources` | Service CPU/Memory resource requests/limits | See `values.yaml` |

### Database Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `postgresql.enabled` | Enable PostgreSQL | `true` |
| `postgresql.auth.username` | PostgreSQL username | `retreatandbe` |
| `postgresql.auth.password` | PostgreSQL password | `""` (auto-generated) |
| `postgresql.auth.database` | PostgreSQL database name | `retreatandbe` |
| `postgresql.primary.persistence.size` | PostgreSQL PVC size | `10Gi` |

### Redis Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `redis.enabled` | Enable Redis | `true` |
| `redis.auth.enabled` | Enable Redis authentication | `true` |
| `redis.auth.password` | Redis password | `""` (auto-generated) |
| `redis.master.persistence.enabled` | Enable Redis persistence | `true` |
| `redis.master.persistence.size` | Redis PVC size | `8Gi` |

### Persistence Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `persistence.models.enabled` | Enable persistence for models | `true` |
| `persistence.models.size` | Models PVC size | `10Gi` |
| `persistence.logs.enabled` | Enable persistence for logs | `true` |
| `persistence.logs.size` | Logs PVC size | `5Gi` |

### Ingress Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `ingress.enabled` | Enable ingress | `false` |
| `ingress.className` | Ingress class name | `nginx` |
| `ingress.hosts` | Ingress hosts | See `values.yaml` |
| `ingress.tls` | Ingress TLS configuration | `[]` |

## Architecture

The R&B new AI Layer consists of the following components:

1. **AI Orchestrator**: Central component that coordinates all AI services
2. **Recommender Engine**: Provides personalized recommendations
3. **Chatbot**: Conversational AI assistant
4. **Content Generator**: Generates content for various purposes
5. **Analytics Engine**: Analyzes data and provides insights
6. **Feature Flags**: Manages feature flags for AI capabilities
7. **Virtual Coach**: Provides virtual coaching services
8. **MLflow**: For experiment tracking and model management

## Dependencies

This chart depends on the following Helm charts:

- [PostgreSQL](https://github.com/bitnami/charts/tree/master/bitnami/postgresql)
- [Redis](https://github.com/bitnami/charts/tree/master/bitnami/redis)

## Upgrading

```bash
helm upgrade rb-new ./charts/rb-new
```
