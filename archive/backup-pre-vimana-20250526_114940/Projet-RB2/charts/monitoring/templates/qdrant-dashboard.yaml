apiVersion: monitoring.grafana.com/v1alpha1
kind: GrafanaDashboard
metadata:
  name: qdrant-metrics
spec:
  json: >
    {
      "title": "Qdrant Vector DB Metrics",
      "panels": [
        {
          "type": "stat",
          "title": "Vectors Stored",
          "targets": [{
            "expr": "qdrant_collection_vectors_count{collection='ai-cache'}",
            "legendFormat": "Total Vectors"
          }]
        },
        {
          "type": "graph",
          "title": "Query Latency",
          "targets": [{
            "expr": "rate(qdrant_query_latency_seconds_sum[5m])",
            "legendFormat": "Query Latency"
          }]
        },
        {
          "type": "gauge",
          "title": "Memory Usage",
          "targets": [{
            "expr": "container_memory_usage_bytes{container='qdrant'}",
            "legendFormat": "Memory Usage"
          }]
        },
        {
          "type": "timeseries",
          "title": "CPU Usage",
          "targets": [{
            "expr": "rate(container_cpu_usage_seconds_total{container='qdrant'}[5m])",
            "legendFormat": "CPU Usage"
          }]
        }
      ],
      "refresh": "10s",
      "schemaVersion": 16,
      "style": "dark",
      "time": {
        "from": "now-1h",
        "to": "now"
      }
    }