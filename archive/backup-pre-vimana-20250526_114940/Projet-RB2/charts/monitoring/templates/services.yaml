apiVersion: v1
kind: Service
metadata:
  name: prometheus
  labels:
    app: prometheus
spec:
  ports:
    - port: 9090
      targetPort: 9090
      name: prometheus
  selector:
    app: prometheus
---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  labels:
    app: grafana
spec:
  ports:
    - port: 3000
      targetPort: 3000
      name: grafana
  selector:
    app: grafana
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger
  labels:
    app: jaeger
spec:
  ports:
    - port: 16686
      targetPort: 16686
      name: query
    - port: 14250
      targetPort: 14250
      name: grpc
  selector:
    app: jaeger