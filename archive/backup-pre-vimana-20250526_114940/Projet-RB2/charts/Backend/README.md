# Backend Service Helm Chart

## Description
The Backend service is the main API service of the Retreat And Be platform, handling core business logic, data processing, and integration with other microservices. It provides RESTful APIs for client applications and manages the platform's primary functionality.

## Prerequisites
- Kubernetes 1.19+
- Helm 3.2.0+
- MongoDB 4.4+
- Redis 6.0+

## Installation

```bash
helm install backend ./charts/Backend
```

## Configuration

### Required Values

| Parameter | Description | Default |
|-----------|-------------|----------|
| image.repository | Backend service image repository | retreat-and-be/backend |
| image.tag | Image tag | latest |
| image.pullPolicy | Image pull policy | IfNotPresent |
| mongodb.uri | MongoDB connection URI | mongodb://mongodb:27017/retreat |
| redis.host | Redis host | redis |

### Optional Values

| Parameter | Description | Default |
|-----------|-------------|----------|
| replicaCount | Number of replicas | 2 |
| resources.limits.cpu | CPU limit | 1000m |
| resources.limits.memory | Memory limit | 1Gi |
| resources.requests.cpu | CPU request | 500m |
| resources.requests.memory | Memory request | 512Mi |
| service.type | Service type | ClusterIP |
| service.port | Service port | 3000 |
| autoscaling.enabled | Enable HPA | true |
| autoscaling.minReplicas | Minimum replicas | 2 |
| autoscaling.maxReplicas | Maximum replicas | 10 |

## Dependencies
- MongoDB: Primary database
- Redis: Caching and session management
- Security-Service: Authentication and authorization
- Messaging-Service: Real-time notifications

## Architecture
The Backend service is a Node.js/Express application that serves as the core API layer of the platform. It integrates with:
- MongoDB for data persistence
- Redis for caching and session management
- Security-Service for user authentication
- Various feature services through REST APIs

## Maintenance

### Scaling
- Horizontal scaling is managed by HPA based on CPU/Memory metrics
- Vertical scaling can be adjusted through resource requests/limits
- Redis connection pool size should be adjusted proportionally

### Monitoring
- CPU and Memory usage
- API response times
- Error rates and status codes
- Database connection pool metrics
- Cache hit/miss rates

### Troubleshooting
- Check MongoDB connection status
- Verify Redis connectivity
- Review API logs for errors
- Monitor resource utilization
- Check network policies

## Security Considerations
- All external communication is TLS encrypted
- API authentication required via Security-Service
- Sensitive data is encrypted at rest
- Network policies restrict unauthorized access
- Regular security scans and updates

## Version History

| Chart Version | App Version | Description |
|---------------|-------------|-------------|
| 1.0.0 | 1.0.0 | Initial release |
| 1.1.0 | 1.1.0 | Added autoscaling and monitoring |

## Contributing
1. Follow the project's coding standards
2. Add tests for new features
3. Update documentation
4. Submit pull requests with detailed descriptions

## Support
1. Check the troubleshooting guide
2. Review the logs and monitoring dashboards
3. Contact the platform team
4. Submit detailed bug reports with reproduction steps