replicaCount: 2
image:
  repository: service-name
  tag: latest
  pullPolicy: IfNotPresent
service:
  type: ClusterIP
  port: 8080
  targetPort: 3000
securityContext:
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
resources:
  requests:
    cpu: 500m
    memory: 512Mi
  limits:
    cpu: 1000m
    memory: 1Gi
livenessProbe:
  path: /health
  initialDelaySeconds: 30
  periodSeconds: 10
readinessProbe:
  path: /ready
  initialDelaySeconds: 5
  periodSeconds: 10
ingress:
  enabled: false
  hosts: []
  annotations: {}
  tls: []
nodeSelector: {}
affinity: {}
tolerations: []
env: []
configMap:
  enabled: false
  data: {}
secret:
  enabled: false
  data: {}
