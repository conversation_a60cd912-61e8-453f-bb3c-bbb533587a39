{{- define "common.configmap" -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.fullname" . }}
  labels:
    {{- include "common.labels" . | nindent 4 }}
data:
  {{- if .Values.configMap.data }}
  {{- toYaml .Values.configMap.data | nindent 2 }}
  {{- end }}
  {{- if .Values.configMap.files }}
  {{- range $key, $value := .Values.configMap.files }}
  {{ $key }}: |
    {{- $value | nindent 4 }}
  {{- end }}
  {{- end }}
{{- end -}}