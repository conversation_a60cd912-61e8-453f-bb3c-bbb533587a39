apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-agent-qdrant
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-agent-qdrant
  template:
    metadata:
      labels:
        app: ai-agent-qdrant
    spec:
      containers:
      - name: qdrant
        image: qdrant/qdrant:v1.7.0
        ports:
        - containerPort: 6334
        env:
        - name: QDRANT__SERVICE__GRPC_PORT
          value: "6334"
        volumeMounts:
        - mountPath: /qdrant/storage
          name: qdrant-data
        resources:
          limits:
            cpu: 2
            memory: 4Gi
      volumes:
      - name: qdrant-data
        persistentVolumeClaim:
          claimName: qdrant-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: ai-agent-qdrant
spec:
  selector:
    app: ai-agent-qdrant
  ports:
  - protocol: TCP
    port: 6334
    targetPort: 6334