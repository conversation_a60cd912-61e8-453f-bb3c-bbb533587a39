replicaCount: 1
qdrant:
  enabled: true
  image:
    repository: qdrant/qdrant
    tag: v1.7.0
    pullPolicy: IfNotPresent
  storageSize: 100Gi
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 4
      memory: 8Gi
  vectorSize: 1536
  distanceMetric: cosine
  persistence:
    enabled: true
    storageClass: standard
    size: 100Gi
  service:
    type: ClusterIP
    port: 6334
  securityContext:
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true
      interval: 15s
