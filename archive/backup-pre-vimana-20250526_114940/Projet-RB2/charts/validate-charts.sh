#!/bin/bash

echo "Starting Helm chart validation..."

# Function to validate a single chart
validate_chart() {
    local chart=$1
    echo "\nValidating chart: $chart"
    
    # Run helm lint
    echo "Running helm lint..."
    if ! helm lint "$chart"; then
        echo "Error: Helm lint failed for $chart"
        return 1
    fi
    
    # Run helm template
    echo "Running helm template..."
    if ! helm template "$chart" > /dev/null; then
        echo "Error: Helm template failed for $chart"
        return 1
    fi
    
    # Check for required files
    for file in Chart.yaml values.yaml; do
        if [ ! -f "$chart/$file" ]; then
            echo "Error: Missing required file $file in $chart"
            return 1
        fi
    done
    
    # Verify templates directory exists
    if [ ! -d "$chart/templates" ]; then
        echo "Error: Missing templates directory in $chart"
        return 1
    fi
    
    return 0
}

# Main execution
ERROR_COUNT=0

# Find and validate all charts
for chart in ./*/; do
    # Skip shared directory as it's not a complete chart
    if [[ "$chart" == "./shared/" ]]; then
        continue
    fi
    
    if ! validate_chart "$chart"; then
        ERROR_COUNT=$((ERROR_COUNT + 1))
    fi
done

# Final report
echo "\nValidation complete!"
if [ $ERROR_COUNT -eq 0 ]; then
    echo "All charts passed validation successfully!"
    exit 0
else
    echo "Found $ERROR_COUNT chart(s) with errors"
    exit 1
fi