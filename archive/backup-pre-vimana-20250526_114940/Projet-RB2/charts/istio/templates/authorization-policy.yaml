apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: analyzer-access
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Release.Name }}
    {{- include "istio.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app: analyzer
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/frontend/sa/frontend-sa"]
        namespaces: ["frontend"]
    to:
    - operation:
        methods: ["POST", "GET"]
        paths: ["/api/v1/analyze/*", "/metrics"]
  - from:
    - source:
        principals: ["cluster.local/ns/monitoring/sa/prometheus"]
    to:
    - operation:
        methods: ["GET"]
        paths: ["/metrics", "/health"]
    when:
    - key: request.headers[Content-Type]
      values: ["application/json"]