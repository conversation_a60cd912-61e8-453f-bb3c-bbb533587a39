apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: opa-filter
spec:
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: envoy.filters.network.http_connection_manager
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.ext_authz
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.http.ext_authz.v3.ExtAuthz
          grpc_service:
            envoy_grpc:
              cluster_name: outbound|9191||opa.istio-system.svc.cluster.local
            timeout: 0.5s
          transport_api_version: V3
          failure_mode_allow: false
          with_request_body:
            max_request_bytes: 8192
            allow_partial_message: true