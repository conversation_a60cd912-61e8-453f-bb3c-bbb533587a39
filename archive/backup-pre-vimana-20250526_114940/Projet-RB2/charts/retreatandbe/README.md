# Retreat And <PERSON> Helm Chart

## Description
This is the main Helm chart for the Retreat And Be platform. It includes all the components of the platform as dependencies, allowing for a unified deployment.

## Components
- **<PERSON>&<PERSON> new**: AI Layer for the platform
- **Backend**: Backend API services
- **Frontend**: Frontend web application

## Prerequisites
- Kubernetes 1.19+
- Helm 3.2.0+
- PV provisioner support in the underlying infrastructure

## Installation

```bash
# Install the chart
helm install retreatandbe ./charts/retreatandbe
```

## Configuration

Each component can be enabled or disabled and configured separately. See the `values.yaml` file for the default configuration.

### R&B new Configuration

```yaml
rb-new:
  enabled: true
  global:
    environment: development
  # Additional configuration...
```

### Backend Configuration

```yaml
backend:
  enabled: true
  # Additional configuration...
```

### Frontend Configuration

```yaml
frontend:
  enabled: true
  # Additional configuration...
```

## Upgrading

```bash
helm upgrade retreatandbe ./charts/retreatandbe
```

## Uninstalling

```bash
helm uninstall retreatandbe
```
