#!/bin/bash

echo "📱 Installation des dépendances pour l'application mobile ⚡"

# Installer les dépendances principales
npm install --save @nozbe/watermelondb
npm install --save @react-native-community/netinfo
npm install --save @react-native-async-storage/async-storage
npm install --save react-native-fs react-native-zip-archive
npm install --save pako buffer

# Installer les dépendances de développement pour les décorateurs
npm install --save-dev @babel/plugin-proposal-decorators
npm install --save-dev @babel/plugin-proposal-class-properties

# Mettre à jour la configuration babel.config.js
echo "✅ Mise à jour de la configuration de Babel pour supporter WatermelonDB"
sed -i '' 's/plugins:.*/plugins: [\n    ["@babel\/plugin-proposal-decorators", { "legacy": true }],\n    ["@babel\/plugin-proposal-class-properties", { "loose": false }],\n    "react-native-reanimated\/plugin",\n  ],/g' babel.config.js

# Vérifier que les décorateurs sont activés dans tsconfig.json
echo "✅ Vérification de la configuration TypeScript pour les décorateurs"
grep -q "experimentalDecorators" tsconfig.json || echo 'Veuillez ajouter "experimentalDecorators": true dans votre tsconfig.json'

echo "🚀 Installation terminée!"
echo "➡️ N'oubliez pas de lancer 'pod install' dans le dossier ios si vous utilisez iOS"
echo "➡️ Redémarrez votre bundler avec 'npm start -- --reset-cache'"