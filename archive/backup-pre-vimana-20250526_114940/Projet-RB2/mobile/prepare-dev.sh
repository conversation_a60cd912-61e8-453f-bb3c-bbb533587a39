#!/bin/bash

echo "🚀 Préparation de l'environnement de développement pour Retreat & Be Mobile"

# Vérification de Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Vérification de npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Vérification de watchman (utile pour React Native)
if ! command -v watchman &> /dev/null; then
    echo "⚠️ Watchman n'est pas installé. Il est recommandé pour React Native."
    read -p "Voulez-vous installer Watchman via Homebrew? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        brew install watchman
    fi
fi

# Installation des dépendances
echo "📦 Installation des dépendances..."
npm install

# Installation des pods pour iOS si Cocoapods est installé
if command -v pod &> /dev/null; then
    echo "📱 Installation des pods pour iOS..."
    cd ios && pod install && cd ..
else
    echo "⚠️ Cocoapods n'est pas installé. Les pods iOS ne seront pas installés."
fi

# Installation de WatermelonDB et configuration des décorateurs
echo "💧 Installation et configuration de WatermelonDB..."
./setup.sh

# Nettoyage du cache
echo "🧹 Nettoyage du cache..."
watchman watch-del-all
rm -rf $TMPDIR/react-native-packager-cache-*
rm -rf $TMPDIR/metro-bundler-cache-*
rm -rf node_modules/.cache

# Vérification de l'environnement React Native
echo "🔍 Vérification de l'environnement React Native..."
npx react-native doctor

# Mise en place de l'environnement
echo "🌍 Configuration de l'environnement de développement..."
cp -n .env.development .env

echo "✅ Environnement de développement prêt!"
echo ""
echo "Pour démarrer l'application:"
echo "  iOS     : npm run ios"
echo "  Android : npm run android"
echo ""
echo "Pour démarrer le Metro Bundler: npm start" 