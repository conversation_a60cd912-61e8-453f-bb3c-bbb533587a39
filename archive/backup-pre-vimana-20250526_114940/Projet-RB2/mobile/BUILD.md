# Guide de génération des versions téléchargeables

Ce document explique comment générer des versions téléchargeables de l'application Retreat & Be pour différents systèmes d'exploitation.

## Prérequis

### Pour toutes les plateformes
- Node.js (v14 ou supérieur)
- npm ou yarn
- Git

### Pour Android
- JDK 11
- Android Studio
- Android SDK
- Variables d'environnement configurées (ANDROID_HOME, JAVA_HOME)

### Pour iOS (macOS uniquement)
- Xcode (dernière version)
- CocoaPods
- Compte développeur Apple (pour la distribution)

### Pour Web
- Webpack et dépendances associées (installées automatiquement par le script)

## Génération des versions téléchargeables

### Configuration initiale

Avant de générer les versions téléchargeables, vous devez configurer l'environnement de build:

1. C<PERSON>r le dépôt et installer les dépendances:
   ```bash
   git clone <repository-url>
   cd mobile
   npm install
   ```

2. Pour Android, générer un keystore pour signer l'application:
   ```bash
   ./scripts/generate-android-keystore.sh
   ```
   Suivez les instructions à l'écran pour créer le keystore.

3. Pour iOS, configurez les certificats et profils de provisionnement dans Xcode.

### Génération des builds

#### Générer toutes les versions (Android, iOS, Web)
```bash
./scripts/build-all.sh
```

#### Générer uniquement la version Android
```bash
./scripts/build-android.sh
```

#### Générer uniquement la version iOS (macOS uniquement)
```bash
./scripts/build-ios.sh
```

#### Générer uniquement la version Web (PWA)
```bash
./scripts/build-web.sh
```

## Fichiers générés

Tous les fichiers générés sont placés dans le dossier `builds` avec un nom incluant la version et la date:

- Android: 
  - APK: `builds/retreat-and-be-v{version}-{date}.apk`
  - AAB: `builds/retreat-and-be-v{version}-{date}.aab`

- iOS:
  - IPA: `builds/retreat-and-be-v{version}-{date}.ipa`

- Web:
  - ZIP: `builds/retreat-and-be-web-v{version}-{date}.zip`

## Distribution

### Android
- L'APK peut être installé directement sur les appareils Android
- L'AAB doit être téléchargé sur Google Play Console pour la distribution via le Play Store

### iOS
- L'IPA doit être téléchargé sur App Store Connect via Xcode ou Application Loader
- Pour la distribution ad-hoc, utilisez un service comme TestFlight ou Diawi

### Web
- Décompressez l'archive ZIP sur votre serveur web
- Configurez le serveur pour servir correctement les fichiers statiques et gérer les routes

## Résolution des problèmes

### Android
- Si vous rencontrez des erreurs de signature, vérifiez que le keystore est correctement configuré
- Pour les erreurs de build, vérifiez les logs dans `android/build/outputs/logs`

### iOS
- Pour les erreurs de provisionnement, vérifiez vos certificats et profils dans Xcode
- Pour les erreurs de build, consultez les logs dans Xcode

### Web
- Pour les erreurs de build, vérifiez les logs de webpack
- Assurez-vous que toutes les dépendances sont correctement installées
