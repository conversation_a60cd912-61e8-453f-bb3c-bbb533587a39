FROM node:18-alpine AS builder

# Installation des dépendances pour les builds mobiles
RUN apk add --no-cache openjdk11 ruby ruby-dev build-base bash git curl
RUN gem install fastlane

# Installation des dépendances Android
ENV ANDROID_HOME /opt/android-sdk
RUN mkdir -p ${ANDROID_HOME}/cmdline-tools
RUN curl -o cmdline-tools.zip https://dl.google.com/android/repository/commandlinetools-linux-8092744_latest.zip && \
    unzip cmdline-tools.zip -d ${ANDROID_HOME}/cmdline-tools && \
    mv ${ANDROID_HOME}/cmdline-tools/cmdline-tools ${ANDROID_HOME}/cmdline-tools/latest && \
    rm cmdline-tools.zip

ENV PATH ${PATH}:${ANDROID_HOME}/cmdline-tools/latest/bin:${ANDROID_HOME}/platform-tools

RUN yes | sdkmanager --licenses && \
    sdkmanager "platform-tools" "platforms;android-33" "build-tools;33.0.0"

WORKDIR /app

# Copie et installation des dépendances
COPY package*.json ./
RUN npm install

# Copie du code source
COPY . .

# Étape de build
RUN npm run build

# Image finale
FROM node:18-alpine

WORKDIR /app

# Copie des fichiers de build
COPY --from=builder /app/build ./build
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./

# Exposition du port (si nécessaire pour un serveur de build ou d'API)
EXPOSE 8080

# Commande par défaut
CMD ["npm", "run", "serve"]
