import React, { createContext, useContext, useState } from 'react';

// Types de thèmes disponibles
export type ThemeType = 'light' | 'dark';

// Structure des couleurs du thème
export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  card: string;
  text: string;
  textSecondary: string;
  border: string;
  notification: string;
  success: string;
  error: string;
  warning: string;
  info: string;
  accent: string;
}

// Structure complète du thème
export interface Theme {
  type: ThemeType;
  colors: ThemeColors;
}

// Thème clair (par défaut)
export const lightTheme: Theme = {
  type: 'light',
  colors: {
    primary: '#5469D4',
    secondary: '#7B61FF',
    background: '#F7F8FA',
    card: '#FFFFFF',
    text: '#1A1F36',
    textSecondary: '#697386',
    border: '#E6E8EB',
    notification: '#FF6464',
    success: '#32D74B',
    error: '#FF3B30',
    warning: '#F7C137',
    info: '#64D2FF',
    accent: '#FF9500',
  },
};

// Thème sombre
export const darkTheme: Theme = {
  type: 'dark',
  colors: {
    primary: '#7B61FF',
    secondary: '#5469D4',
    background: '#1A1F36',
    card: '#242B42',
    text: '#F7F8FA',
    textSecondary: '#A0A8C0',
    border: '#384055',
    notification: '#FF6464',
    success: '#30D158',
    error: '#FF453A',
    warning: '#FFD60A',
    info: '#5AC8FA',
    accent: '#FF9F0A',
  },
};

// Interface du contexte de thème
interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isDarkMode: boolean;
}

// Création du contexte avec une valeur par défaut
const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  setTheme: () => {},
  toggleTheme: () => {},
  isDarkMode: false,
});

// Hook useTheme
export const useTheme = () => {
  const context = useContext(ThemeContext);
  
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return {
    theme: context.theme.colors,
    isDark: context.isDarkMode,
    toggleTheme: context.toggleTheme,
    setTheme: context.setTheme
  };
};

// Fournisseur de thème
interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setTheme] = useState<Theme>(lightTheme);

  const toggleTheme = () => {
    setTheme(theme.type === 'light' ? darkTheme : lightTheme);
  };

  const value = {
    theme,
    setTheme,
    toggleTheme,
    isDarkMode: theme.type === 'dark'
  };

  return React.createElement(
    ThemeContext.Provider,
    { value },
    children
  );
};