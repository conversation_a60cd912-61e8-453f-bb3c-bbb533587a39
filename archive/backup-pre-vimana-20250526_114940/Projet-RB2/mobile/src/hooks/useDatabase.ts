import { useEffect, useState } from 'react';
import { Model, Q } from '@nozbe/watermelondb';
import { database, CollectionMock, Database } from '../database';

interface UseDatabaseOptions<T extends Model> {
  collection: string;
  observe?: boolean;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  filters?: Q.Clause[];
}

/**
 * Type pour les fonctions et données retournées par le hook useDatabase
 */
export interface DatabaseHookResult<T extends Model> {
  records: T[];
  loading: boolean;
  error: Error | null;
  create: (data: Partial<T>) => Promise<T>;
  update: (id: string, data: Partial<T>) => Promise<T>;
  remove: (id: string) => Promise<boolean>;
  findById: (id: string) => Promise<T>;
  refresh: () => Promise<void>;
  collection: CollectionMock<T>;
  database: Database;
}

/**
 * Hook pour gérer les opérations de base sur une collection WatermelonDB
 * @param options Options de configuration
 * @returns Un objet contenant des méthodes pour interagir avec la collection
 */
const useDatabase = <T extends Model>({
  collection,
  observe = true,
  sortBy,
  sortDirection = 'asc',
  filters = [],
}: UseDatabaseOptions<T>): DatabaseHookResult<T> => {
  const [records, setRecords] = useState<T[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Référence à la collection
  const dbCollection = database.get<T>(collection);

  // Charger et/ou observer les changements
  useEffect(() => {
    setLoading(true);
    setError(null);

    // Fonction pour appliquer les tris et filtres à la collection
    const getQuery = () => {
      // Commencer avec les filtres fournis
      const queryFilters = [...filters];
      
      // Ajouter une clause de tri si demandée
      if (sortBy) {
        queryFilters.push(
          sortDirection === 'asc' 
            ? Q.sortBy(sortBy, Q.asc)
            : Q.sortBy(sortBy, Q.desc)
        );
      }
      
      // Créer la requête avec tous les filtres et tris
      return dbCollection.query(...queryFilters);
    };

    // Fonction pour charger les données
    const fetchData = async () => {
      try {
        const data = await getQuery().fetch();
        if (!observe) {
          setRecords(data);
          setLoading(false);
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Erreur lors du chargement des données'));
        setLoading(false);
      }
    };

    // Si observe est activé, on s'abonne aux changements
    if (observe) {
      try {
        const subscription = getQuery().observe().subscribe({
          next: data => {
            setRecords(data);
            setLoading(false);
          },
          error: err => {
            setError(err instanceof Error ? err : new Error('Erreur lors de l\'observation des données'));
            setLoading(false);
          }
        });

        // Nettoyage de l'abonnement
        return () => subscription.unsubscribe();
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Erreur lors de la configuration de l\'observation'));
        setLoading(false);
      }
    } else {
      // Chargement ponctuel sans observation
      fetchData();
    }
  }, [collection, JSON.stringify(filters), sortBy, sortDirection, observe]);

  // Fonction pour créer un nouvel enregistrement
  const create = async (data: Partial<T>) => {
    try {
      let record: T | undefined = undefined;
      await database.write(async () => {
        record = await dbCollection.create((item: any) => {
          Object.assign(item, data);
        });
      });
      return record as unknown as T;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Erreur lors de la création'));
      throw err;
    }
  };

  // Fonction pour mettre à jour un enregistrement
  const update = async (id: string, data: Partial<T>) => {
    try {
      let updatedRecord: T | undefined = undefined;
      await database.write(async () => {
        const record = await dbCollection.find(id);
        updatedRecord = await record.update((item: any) => {
          Object.assign(item, data);
        });
      });
      return updatedRecord as unknown as T;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Erreur lors de la mise à jour'));
      throw err;
    }
  };

  // Fonction pour supprimer un enregistrement
  const remove = async (id: string) => {
    try {
      await database.write(async () => {
        const record = await dbCollection.find(id);
        await record.markAsDeleted();
      });
      return true;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Erreur lors de la suppression'));
      throw err;
    }
  };

  // Fonction pour récupérer un enregistrement par son ID
  const findById = async (id: string) => {
    try {
      return await dbCollection.find(id);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Enregistrement non trouvé'));
      throw err;
    }
  };

  // Fonction pour recharger les données manuellement
  const refresh = async () => {
    setLoading(true);
    try {
      const data = await dbCollection.query(...filters).fetch();
      setRecords(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Erreur lors du rafraîchissement'));
    } finally {
      setLoading(false);
    }
  };

  return {
    records,
    loading,
    error,
    create,
    update,
    remove,
    findById,
    refresh,
    collection: dbCollection,
    database
  };
};

export default useDatabase; 