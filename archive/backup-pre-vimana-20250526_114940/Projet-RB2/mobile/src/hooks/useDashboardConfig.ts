/**
 * Hook pour utiliser les configurations de tableaux de bord personnalisables;
 */
import { useState, useEffect, useCallback } from 'react';
import { dashboardConfigService, DEFAULT_DASHBOARD_CONFIG } from '../services/dashboardConfig.service';
import { DashboardConfig, DashboardViewConfig, DashboardPreferences, MetricConfig } from '../types/dashboardTypes';
import { unifiedMonitoring } from '../utils/unifiedMonitoring';
import useMonitoring from './useMonitoring';

interface UseDashboardConfigProps {
  defaultDashboardId?: string;
}

interface UseDashboardConfigResult {
  // Données des tableaux de bord;
  dashboards: DashboardConfig[];
  activeDashboard: DashboardConfig;
  activeView: DashboardViewConfig;
  preferences: DashboardPreferences;
  
  // Métriques actuellement visibles;
  visibleMetrics: MetricConfig[];
  
  // État de chargement;
  isLoading: boolean;
  
  // Actions;
  setActiveDashboard: (dashboardId: string) => Promise<void>;
  setActiveView: (viewId: string) => void;
  createDashboard: (config: Omit<DashboardConfig, 'createdAt' | 'updatedAt'>) => Promise<DashboardConfig>;
  updateDashboard: (id: string, updates: Partial<DashboardConfig>) => Promise<DashboardConfig | undefined>;
  deleteDashboard: (id: string) => Promise<boolean>;
  updatePreferences: (updates: Partial<DashboardPreferences>) => Promise<void>;
  
  // Gestion des métriques;
  toggleMetricVisibility: (metricId: string) => void;
  updateMetricConfig: (metricId: string, updates: Partial<MetricConfig>) => void;
  reorderMetrics: (metricIds: string[]) => void;
  resetToDefaults: () => Promise<void>;
  refreshMetrics: () => Promise<void>
}

/**
 * Hook pour gérer les configurations des tableaux de bord;
 */
export function useDashboardConfig({ defaultDashboardId }: UseDashboardConfigProps = {}): UseDashboardConfigResult {
  // États;
  const [dashboards, setDashboards] = useState<DashboardConfig[]>([DEFAULT_DASHBOARD_CONFIG]);
  const [activeDashboard, setActiveDashboard] = useState<DashboardConfig>(DEFAULT_DASHBOARD_CONFIG);
  const [activeView, setActiveView] = useState<DashboardViewConfig>(DEFAULT_DASHBOARD_CONFIG.views[0]);
  const [preferences, setPreferences] = useState<DashboardPreferences>({
    lastUsedDashboard: 'default',
    defaultDashboard: 'default',
    colorTheme: 'system',
    chartAnimations: true,
    autoRefresh: true
  });
  const [isLoading, setIsLoading] = useState(true);
  
  // Monitoring pour les événements utilisateur;
  const { trackUserAction } = useMonitoring('useDashboardConfig');
  
  // Effet pour initialiser les tableaux de bord;
  useEffect(() => {
    initializeDashboards();
  }, []);

  // Fonction pour initialiser les tableaux de bord;
  const initializeDashboards = async () => {
    try {
      setIsLoading(true);
      
      // Initialiser le service de configuration;
      await dashboardConfigService.initialize();
      
      // Récupérer les configurations de tableaux de bord;
      const configs = await dashboardConfigService.getDashboardConfigs();
      setDashboards(configs);
      
      // Récupérer les préférences;
      const prefs = await dashboardConfigService.getPreferences();
      setPreferences(prefs);
      
      // Déterminer le tableau de bord actif;
      const dashboardId = defaultDashboardId || prefs.lastUsedDashboard || prefs.defaultDashboard || 'default';
      
      // Trouver le tableau de bord;
      const dashboard = configs.find(d => d.id === dashboardId) || configs[0] || DEFAULT_DASHBOARD_CONFIG;
      setActiveDashboard(dashboard);
      
      // Déterminer la vue active;
      const viewId = dashboard.defaultView || (dashboard.views.length > 0 ? dashboard.views[0].id : 'main');
      const view = dashboard.views.find(v => v.id === viewId) || dashboard.views[0] || DEFAULT_DASHBOARD_CONFIG.views[0];
      setActiveView(view);
      
      // Si le tableau de bord actif est différent des préférences, mettre à jour les préférences;
      if(dashboard.id !== prefs.lastUsedDashboard) {
        await dashboardConfigService.updatePreferences({
          lastUsedDashboard: dashboard.id
        });
      }
    } catch(error) {
      console.error('Erreur lors de l\'initialisation des tableaux de bord:', error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.initializeDashboards'
      });
      
      // Utiliser les valeurs par défaut en cas d'erreur;
      setDashboards([DEFAULT_DASHBOARD_CONFIG]);
      setActiveDashboard(DEFAULT_DASHBOARD_CONFIG);
      setActiveView(DEFAULT_DASHBOARD_CONFIG.views[0]);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fonction pour changer de tableau de bord actif;
  const handleSetActiveDashboard = async (dashboardId: string) => {
    try {
      // Trouver le tableau de bord;
      const dashboard = dashboards.find(d => d.id === dashboardId);
      
      if(!dashboard) {
        console.warn(`Tableau de bord avec l'ID ${dashboardId} non trouvé`);
        return;
      }
      
      // Mettre à jour le tableau de bord actif;
      setActiveDashboard(dashboard);
      
      // Déterminer la vue active;
      const viewId = dashboard.defaultView || (dashboard.views.length > 0 ? dashboard.views[0].id : 'main');
      const view = dashboard.views.find(v => v.id === viewId) || dashboard.views[0] || DEFAULT_DASHBOARD_CONFIG.views[0];
      setActiveView(view);
      
      // Mettre à jour les préférences;
      await dashboardConfigService.updatePreferences({
        lastUsedDashboard: dashboard.id
      });
    } catch(error) {
      console.error(`Erreur lors du changement de tableau de bord actif vers ${dashboardId}:`, error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.handleSetActiveDashboard'
      });
    }
  };
  
  // Fonction pour changer de vue active;
  const handleSetActiveView = (viewId: string) => {
    try {
      // Trouver la vue;
      const view = activeDashboard.views.find(v => v.id === viewId);
      
      if(!view) {
        console.warn(`Vue avec l'ID ${viewId} non trouvée`);
        return;
      }
      
      // Mettre à jour la vue active;
      setActiveView(view);
    } catch(error) {
      console.error(`Erreur lors du changement de vue active vers ${viewId}:`, error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.handleSetActiveView'
      });
    }
  };
  
  // Fonction pour créer un nouveau tableau de bord;
  const createDashboard = async (config: Omit<DashboardConfig, 'createdAt' | 'updatedAt'>) => {
    try {
      const newDashboard = await dashboardConfigService.createDashboardConfig(config);
      
      // Mettre à jour la liste des tableaux de bord;
      setDashboards(prev => [...prev, newDashboard]);
      
      return newDashboard;
    } catch(error) {
      console.error('Erreur lors de la création du tableau de bord:', error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.createDashboard'
      });
      throw error;
    }
  };
  
  // Fonction pour mettre à jour un tableau de bord;
  const updateDashboard = async (id: string, updates: Partial<DashboardConfig>) => {
    try {
      const updatedDashboard = await dashboardConfigService.updateDashboardConfig(id, updates);
      
      if(!updatedDashboard) {
        return undefined;
      }
      
      // Mettre à jour la liste des tableaux de bord;
      setDashboards(prev => 
        prev.map(dashboard => dashboard.id === id ? updatedDashboard : dashboard)
      );
      
      // Si le tableau de bord actif est mis à jour, mettre à jour l'état local;
      if(activeDashboard.id === id) {
        setActiveDashboard(updatedDashboard);
        
        // Trouver la vue active dans le tableau de bord mis à jour;
        const updatedView = updatedDashboard.views.find(v => v.id === activeView.id);
        
        // Si la vue active existe encore, la mettre à jour;
        if(updatedView) {
          setActiveView(updatedView);
        } 
        // Sinon, utiliser la première vue disponible;
        else if(updatedDashboard.views.length > 0) {
          setActiveView(updatedDashboard.views[0]);
        }
      }
      
      return updatedDashboard;
    } catch(error) {
      console.error(`Erreur lors de la mise à jour du tableau de bord ${id}:`, error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.updateDashboard'
      });
      return undefined;
    }
  };
  
  // Fonction pour supprimer un tableau de bord;
  const deleteDashboard = async (id: string) => {
    try {
      const success = await dashboardConfigService.deleteDashboardConfig(id);
      
      if(success) {
        // Mettre à jour la liste des tableaux de bord;
        const updatedDashboards = dashboards.filter(dashboard => dashboard.id !== id);
        setDashboards(updatedDashboards);
        
        // Si le tableau de bord actif est supprimé, utiliser le tableau de bord par défaut;
        if(activeDashboard.id === id) {
          const defaultId = preferences.defaultDashboard || 'default';
          const defaultDashboard = updatedDashboards.find(d => d.id === defaultId) || updatedDashboards[0];
          
          setActiveDashboard(defaultDashboard);
          
          // Utiliser la première vue du nouveau tableau de bord actif;
          if(defaultDashboard.views.length > 0) {
            setActiveView(defaultDashboard.views[0]);
          }
          
          // Mettre à jour les préférences;
          await dashboardConfigService.updatePreferences({
            lastUsedDashboard: defaultDashboard.id
          });
        }
      }
      
      return success;
    } catch(error) {
      console.error(`Erreur lors de la suppression du tableau de bord ${id}:`, error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.deleteDashboard'
      });
      return false;
    }
  };
  
  // Fonction pour mettre à jour les préférences;
  const updatePreferences = async (updates: Partial<DashboardPreferences>) => {
    try {
      const updatedPreferences = await dashboardConfigService.updatePreferences(updates);
      setPreferences(updatedPreferences);
    } catch(error) {
      console.error('Erreur lors de la mise à jour des préférences:', error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.updatePreferences'
      });
      throw error;
    }
  };
  
  // Fonction pour basculer la visibilité d'une métrique;
  const toggleMetricVisibility = useCallback((metricId: string) => {
    try {
      // Copier la vue active;
      const updatedView = { ...activeView };
      
      // Trouver la métrique;
      const metricIndex = updatedView.metrics.findIndex(m => m.id === metricId);
      
      if(metricIndex === -1) {
        console.warn(`Métrique avec l'ID ${metricId} non trouvée`);
        return;
      }
      
      // Basculer la visibilité;
      updatedView.metrics[metricIndex] = {
        ...updatedView.metrics[metricIndex],
        visible: !updatedView.metrics[metricIndex].visible
      };
      
      // Mettre à jour la vue;
      setActiveView(updatedView);
      
      // Mettre à jour le tableau de bord;
      const updatedDashboard = { ...activeDashboard };
      const viewIndex = updatedDashboard.views.findIndex(v => v.id === activeView.id);
      
      if(viewIndex !== -1) {
        updatedDashboard.views[viewIndex] = updatedView;
        setActiveDashboard(updatedDashboard);
        
        // Sauvegarder les modifications;
        dashboardConfigService.updateDashboardConfig(updatedDashboard.id, {
          views: updatedDashboard.views
        });
      }
    } catch(error) {
      console.error(`Erreur lors de la modification de la visibilité de la métrique ${metricId}:`, error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.toggleMetricVisibility'
      });
    }
  }, [activeView, activeDashboard]);
  
  // Fonction pour mettre à jour la configuration d'une métrique;
  const updateMetricConfig = useCallback((metricId: string, updates: Partial<MetricConfig>) => {
    try {
      // Copier la vue active;
      const updatedView = { ...activeView };
      
      // Trouver la métrique;
      const metricIndex = updatedView.metrics.findIndex(m => m.id === metricId);
      
      if(metricIndex === -1) {
        console.warn(`Métrique avec l'ID ${metricId} non trouvée`);
        return;
      }
      
      // Mettre à jour la métrique;
      updatedView.metrics[metricIndex] = {
        ...updatedView.metrics[metricIndex],
        ...updates
      };
      
      // Mettre à jour la vue;
      setActiveView(updatedView);
      
      // Mettre à jour le tableau de bord;
      const updatedDashboard = { ...activeDashboard };
      const viewIndex = updatedDashboard.views.findIndex(v => v.id === activeView.id);
      
      if(viewIndex !== -1) {
        updatedDashboard.views[viewIndex] = updatedView;
        setActiveDashboard(updatedDashboard);
        
        // Sauvegarder les modifications;
        dashboardConfigService.updateDashboardConfig(updatedDashboard.id, {
          views: updatedDashboard.views
        });
      }
    } catch(error) {
      console.error(`Erreur lors de la mise à jour de la configuration de la métrique ${metricId}:`, error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.updateMetricConfig'
      });
    }
  }, [activeView, activeDashboard]);
  
  // Fonction pour réorganiser les métriques;
  const reorderMetrics = useCallback((metricIds: string[]) => {
    try {
      // Copier la vue active;
      const updatedView = { ...activeView };
      
      // Mettre à jour l'ordre des métriques;
      const updatedMetrics = [...updatedView.metrics];
      
      // Parcourir les IDs de métrique et mettre à jour l'ordre;
      metricIds.forEach((id, index) => {
        const metricIndex = updatedMetrics.findIndex(m => m.id === id);
        
        if(metricIndex !== -1) {
          updatedMetrics[metricIndex] = {
            ...updatedMetrics[metricIndex],
            order: index
          };
        }
      });
      
      // Mettre à jour la vue;
      updatedView.metrics = updatedMetrics;
      setActiveView(updatedView);
      
      // Mettre à jour le tableau de bord;
      const updatedDashboard = { ...activeDashboard };
      const viewIndex = updatedDashboard.views.findIndex(v => v.id === activeView.id);
      
      if(viewIndex !== -1) {
        updatedDashboard.views[viewIndex] = updatedView;
        setActiveDashboard(updatedDashboard);
        
        // Sauvegarder les modifications;
        dashboardConfigService.updateDashboardConfig(updatedDashboard.id, {
          views: updatedDashboard.views
        });
      }
    } catch(error) {
      console.error('Erreur lors de la réorganisation des métriques:', error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.reorderMetrics'
      });
    }
  }, [activeView, activeDashboard]);
  
  // Fonction pour réinitialiser aux valeurs par défaut;
  const resetToDefaults = async () => {
    try {
      // Supprimer toutes les configurations (sauf celle par défaut)
      await dashboardConfigService.deleteDashboardConfig('default');
      
      // Réinitialiser les préférences;
      await dashboardConfigService.updatePreferences({
        lastUsedDashboard: 'default',
        defaultDashboard: 'default'
      });
      
      // Réinitialiser l'état local;
      await initializeDashboards();
    } catch(error) {
      console.error('Erreur lors de la réinitialisation aux valeurs par défaut:', error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.resetToDefaults'
      });
    }
  };
  
  // Fonction pour rafraîchir les métriques;
  const refreshMetrics = async () => {
    try {
      // Marquer comme en chargement;
      setIsLoading(true);
      
      // Obtenir les statistiques actuelles du service de monitoring;
      const stats = unifiedMonitoring.getPerformanceStats();
      
      if(!stats) {
        return;
      }
      
      // Mettre à jour les métriques de la vue active;
      const updatedMetrics = [...activeView.metrics];
      
      // Parcourir les métriques et mettre à jour les valeurs;
      for(const metric of updatedMetrics) {
        if(stats[metric.name]) {
          const data = stats[metric.name];
          
          // Mettre à jour les données de la métrique;
          // (les composants utilisant ces données feront le rendu de manière appropriée)}
        }
      }
      
      // Mettre à jour la vue;
      const updatedView = { ...activeView, metrics: updatedMetrics };
      setActiveView(updatedView);
      
      // Mettre à jour le tableau de bord;
      const updatedDashboard = { ...activeDashboard };
      const viewIndex = updatedDashboard.views.findIndex(v => v.id === activeView.id);
      
      if(viewIndex !== -1) {
        updatedDashboard.views[viewIndex] = updatedView;
        setActiveDashboard(updatedDashboard);
      }
    } catch(error) {
      console.error('Erreur lors du rafraîchissement des métriques:', error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'useDashboardConfig.refreshMetrics'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Calculer les métriques visibles;
  const visibleMetrics = activeView.metrics.filter(metric => metric.visible);
  
  return {
    dashboards,
    activeDashboard,
    activeView,
    preferences,
    visibleMetrics,
    isLoading,
    setActiveDashboard: handleSetActiveDashboard,
    setActiveView: handleSetActiveView,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    updatePreferences,
    toggleMetricVisibility,
    updateMetricConfig,
    reorderMetrics,
    resetToDefaults,
    refreshMetrics
  };
} 