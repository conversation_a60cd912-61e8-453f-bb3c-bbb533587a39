import { useColorScheme } from 'react-native';
import { useState, useEffect } from 'react';

type Theme = {
  primary: string;
  secondary: string;
  background: string;
  card: string;
  text: string;
  border: string;
  error: string;
  success: string;
  warning: string;
  cardBackground: string;
};

const lightTheme: Theme = {
  primary: '#007AFF',
  secondary: '#5856D6',
  background: '#FFFFFF',
  card: '#F2F2F7',
  text: '#000000',
  border: '#C7C7CC',
  error: '#FF3B30',
  success: '#4CD964',
  warning: '#FF9500',
  cardBackground: '#FFFFFF'
};

const darkTheme: Theme = {
  primary: '#0A84FF',
  secondary: '#5E5CE6',
  background: '#000000',
  card: '#1C1C1E',
  text: '#FFFFFF',
  border: '#38383A',
  error: '#FF453A',
  success: '#32D74B',
  warning: '#FF9F0A',
  cardBackground: '#1C1C1E'
};

export const useTheme = () => {
  const colorScheme = useColorScheme();
  const [theme, setTheme] = useState<Theme>(colorScheme === 'dark' ? darkTheme : lightTheme);

  useEffect(() => {
    setTheme(colorScheme === 'dark' ? darkTheme : lightTheme);
  }, [colorScheme]);

  return { theme, isDarkMode: colorScheme === 'dark' };
}; 