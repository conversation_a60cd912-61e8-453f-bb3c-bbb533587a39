import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { api } from '../services/api';
import { User } from '../models';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getErrorMessage, logError } from '../utils/errorUtils';
import { NavigationProp, StackActions } from '@react-navigation/native';

// Définir le type de navigation pour pouvoir spécifier les routes
type RootStackParamList = {
  Main: undefined;
  Login: undefined;
  // Ajouter d'autres routes si nécessaire
};

// Utiliser le NavigationProp standard au lieu de NativeStackNavigationProp
type AuthNavigationProp = NavigationProp<RootStackParamList>;

/**
 * Hook pour gérer l'authentification utilisateur
 */
export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const navigation = useNavigation<AuthNavigationProp>();

  /**
   * Connexion utilisateur
   */
  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.post('/auth/login', { email, password });
      
      if (response?.data?.user) {
        const userData = response.data.user;
        
        // Stocker les données utilisateur et le token
        await AsyncStorage.setItem('user', JSON.stringify(userData));
        await AsyncStorage.setItem('token', response.data.token);
        
        setUser(userData);
        
        // Rediriger vers l'écran principal avec le type correct
        navigation.reset({
          index: 0,
          routes: [{ name: 'Main' as const }],
        });
      } else {
        setError('Réponse invalide du serveur');
      }
    } catch (err) {
      logError('login', err);
      setError(getErrorMessage(err, 'Échec de la connexion'));
    } finally {
      setLoading(false);
    }
  };

  /**
   * Déconnexion utilisateur
   */
  const logout = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await api.post('/auth/logout');
    } catch (err) {
      // On logue l'erreur mais on continue la déconnexion
      logError('logout', err);
    } finally {
      // Supprimer les données locales
      await AsyncStorage.removeItem('user');
      await AsyncStorage.removeItem('token');
      
      setUser(null);
      setLoading(false);
      
      // Rediriger vers l'écran de connexion avec le type correct
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' as const }],
      });
    }
  };

  /**
   * Vérifier si l'utilisateur est déjà connecté au chargement de l'app
   */
  const checkAuth = async () => {
    setLoading(true);
    
    try {
      const storedUser = await AsyncStorage.getItem('user');
      const token = await AsyncStorage.getItem('token');
      
      if (storedUser && token) {
        setUser(JSON.parse(storedUser));
      }
    } catch (err) {
      logError('checkAuth', err);
      setError(getErrorMessage(err, 'Erreur lors de la vérification de l\'authentification'));
    } finally {
      setLoading(false);
    }
  };

  return { 
    user, 
    loading, 
    error, 
    login, 
    logout, 
    checkAuth,
    isAuthenticated: !!user
  };
};
