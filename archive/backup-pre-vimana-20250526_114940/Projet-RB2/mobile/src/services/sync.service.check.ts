// Copie de sync.service.ts avec des imports modifiés pour la vérification de types
// Les imports originaux sont redirigés vers nos stubs

import { Database, Model, Q } from '@nozbe/watermelondb';
import NetInfo from '@react-native-community/netinfo';
import axios from 'axios';
import { API_CONFIG } from '../config/api.config';
import { API_URL } from '../config/api.config';
import { Booking, Notification, Retreat, User } from '../types/stubs/models';
import { ChangeSet, ConflictResolution, SyncConfig, SyncOptions } from '../types/sync';
import { Logger } from '../utils/logger';
import { updateModel } from '../utils/modelUtils';
import { sleep } from '../utils/sleep';

// Importer le contenu du fichier sync.service.ts ici
export class SyncService {
  private database: Database;
  private syncConfig: SyncConfig;
  private syncOptions: SyncOptions;
  private isOnline: boolean = true;
  private isSyncing: boolean = false;

  constructor(database: Database, syncConfig: SyncConfig, syncOptions: SyncOptions = {}) {
    this.database = database;
    this.syncConfig = syncConfig;
    this.syncOptions = {
      retryDelayMs: 5000,
      maxRetries: 3,
      ...(syncOptions || {}),
    };

    // Monitor connectivity changes
    NetInfo.addEventListener(state => {
      this.isOnline = state.isConnected || false;
      if (this.isOnline && this.syncOptions.syncOnReconnect) {
        this.sync().catch(error => Logger.error('Auto-sync failed:', error));
      }
    });
  }

  /**
   * Sync data between local database and server
   */
  async sync(): Promise<void> {
    if (this.isSyncing) {
      Logger.info('Sync already in progress');
      return;
    }

    if (!this.isOnline) {
      Logger.warning('Cannot sync: device offline');
      return;
    }

    try {
      this.isSyncing = true;
      Logger.info('Starting sync process');

      // Get last sync timestamp
      const lastPulledAt = await this.getLastSyncTimestamp();
      
      // Pull changes from server
      await this.pullChanges(lastPulledAt);
      
      // Push local changes to server
      await this.pushChanges();
      
      // Update last sync timestamp
      await this.updateLastSyncTimestamp();
      
      Logger.info('Sync completed successfully');
    } catch (error) {
      Logger.error('Sync failed:', error);
      throw error;
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Retrieve changes from server since lastPulledAt
   */
  private async pullChanges(lastPulledAt: number): Promise<void> {
    try {
      Logger.info(`Pulling changes since ${new Date(lastPulledAt).toISOString()}`);

      // For each model type in sync config
      for (const key in this.syncConfig.models) {
        const config = this.syncConfig.models[key];
        
        if (!config.pull) continue;
        
        Logger.info(`Pulling ${key} records`);
        
        // Fetch remote changes
        let retries = 0;
        let success = false;
        
        while (!success && retries <= this.syncOptions.maxRetries!) {
          try {
            // Construct the API URL
            const endpoint = config.endpoint as string;
            const url = this.getApiUrl(endpoint);
            
            // Make API request
            const response = await axios.get(url, {
              params: { last_pulled_at: lastPulledAt },
              timeout: this.syncOptions.timeoutMs,
            });
            
            // Process and save remote changes
            if (response && response.data) {
              await this.applyRemoteChanges(key, response.data.changes);
            }
            
            success = true;
          } catch (error) {
            retries++;
            
            if (retries > this.syncOptions.maxRetries!) {
              throw error;
            }
            
            Logger.warning(`Pull retry ${retries}/${this.syncOptions.maxRetries} for ${key}`);
            await sleep(this.syncOptions.retryDelayMs!);
          }
        }
      }
    } catch (error) {
      Logger.error('Error pulling changes:', error);
      throw error;
    }
  }

  /**
   * Push local changes to server
   */
  private async pushChanges(): Promise<void> {
    try {
      Logger.info('Pushing local changes to server');

      // Collect all local changes
      const changeSet: ChangeSet = await this.prepareChangeSet();
      
      if (this.isEmptyChangeSet(changeSet)) {
        Logger.info('No local changes to push');
        return;
      }
      
      // For each model type in the change set
      for (const key in this.syncConfig.models) {
        const config = this.syncConfig.models[key];
        
        if (!config.push) continue;
        
        const modelChanges = changeSet[key];
        if (!modelChanges || this.isEmptyModelChanges(modelChanges)) continue;
        
        Logger.info(`Pushing ${key} changes: ${JSON.stringify({
          created: modelChanges.created?.length || 0,
          updated: modelChanges.updated?.length || 0,
          deleted: modelChanges.deleted?.length || 0,
        })}`);
        
        // Push changes to server
        let retries = 0;
        let success = false;
        
        while (!success && retries <= this.syncOptions.maxRetries!) {
          try {
            // Construct the API URL
            const endpoint = config.endpoint as string;
            const url = this.getApiUrl(endpoint);
            
            // Make API request
            const response = await axios.post(url, {
              changes: modelChanges
            }, {
              timeout: this.syncOptions.timeoutMs,
            });
            
            // Handle conflicts if any
            if (response.data && response.data.conflicts) {
              await this.resolveConflicts(key, response.data.conflicts);
            }
            
            success = true;
          } catch (error) {
            retries++;
            
            if (retries > this.syncOptions.maxRetries!) {
              throw error;
            }
            
            Logger.warning(`Push retry ${retries}/${this.syncOptions.maxRetries} for ${key}`);
            await sleep(this.syncOptions.retryDelayMs!);
          }
        }
      }
    } catch (error) {
      Logger.error('Error pushing changes:', error);
      throw error;
    }
  }

  /**
   * Apply remote changes to local database
   */
  private async applyRemoteChanges(modelName: string, changes: any): Promise<void> {
    if (!changes) return;
    
    const { created = [], updated = [], deleted = [] } = changes;
    
    await this.database.write(async () => {
      // Get the model collection
      const collection = this.database.get(modelName);
      
      // Process created records
      for (const item of created) {
        await collection.create((record: any) => {
          Object.assign(record, this.prepareRecord(item));
        });
      }
      
      // Process updated records
      for (const item of updated) {
        const localItem = await collection.find(item.id);
        if (localItem) {
          await (localItem as Model).update((record: any) => {
            Object.assign(record, this.prepareRecord(item));
          });
        }
      }
      
      // Process deleted records
      for (const id of deleted) {
        try {
          const localItem = await collection.find(id);
          if (localItem) {
            await (localItem as Model).markAsDeleted();
          }
        } catch (error) {
          // Record might already be deleted locally
          Logger.warning(`Could not delete record ${id}:`, error);
        }
      }
    });
  }

  /**
   * Prepare a change set containing all local changes
   */
  private async prepareChangeSet(): Promise<ChangeSet> {
    const changeSet: ChangeSet = {};
    
    await this.database.write(async () => {
      for (const key in this.syncConfig.models) {
        const collection = this.database.get(key);
        
        // Get all changes since last sync
        const created = await collection.query(Q.where('_status', 'created')).fetch();
        const updated = await collection.query(Q.where('_status', 'updated')).fetch();
        const deleted = await collection.query(Q.where('_status', 'deleted')).fetch();
        
        if (created.length || updated.length || deleted.length) {
          changeSet[key] = {
            created: created.map(record => this.prepareRecordForSync(record)),
            updated: updated.map(record => this.prepareRecordForSync(record)),
            deleted: deleted.map(record => record.id)
          };
        }
      }
    });
    
    return changeSet;
  }

  /**
   * Resolve conflicts between local and remote changes
   */
  private async resolveConflicts(modelName: string, conflicts: any[]): Promise<void> {
    if (!conflicts || !conflicts.length) return;
    
    Logger.info(`Resolving ${conflicts.length} conflicts for ${modelName}`);
    
    await this.database.write(async () => {
      const collection = this.database.get(modelName);
      
      for (const conflict of conflicts) {
        const { serverVersion, clientVersion, resolution } = conflict;
        
        try {
          // Find the local record
          const localItem = await collection.find(clientVersion.id);
          
          if (!localItem) {
            Logger.warning(`Could not find local record for conflict: ${clientVersion.id}`);
            continue;
          }
          
          // Apply resolution strategy
          switch (resolution) {
            case ConflictResolution.SERVER_WINS:
              // Update local record with server version
              await (localItem as Model).update((record: any) => {
                Object.assign(record, this.prepareRecord(serverVersion));
              });
              break;
              
            case ConflictResolution.CLIENT_WINS:
              // Server will keep client version, nothing to do locally
              break;
              
            case ConflictResolution.MANUAL:
              // Store conflict for manual resolution
              await this.storePendingConflict(modelName, serverVersion, clientVersion);
              break;
          }
        } catch (error) {
          Logger.error(`Error resolving conflict for ${clientVersion.id}:`, error);
          // Store for manual resolution as fallback
          await this.storePendingConflict(modelName, serverVersion, clientVersion);
        }
      }
    });
  }

  /**
   * Store conflict for manual resolution
   */
  private async storePendingConflict(modelName: string, serverVersion: any, clientVersion: any): Promise<void> {
    // Implementation depends on how you want to handle manual conflict resolution
    // Typically, you would store the conflict in a separate table for later resolution
    Logger.info(`Storing conflict for manual resolution: ${modelName} - ${clientVersion.id}`);
    
    // This is a placeholder implementation
    const pendingConflictsCollection = this.database.get('pending_conflicts');
    
    await pendingConflictsCollection.create((record: any) => {
      record.modelName = modelName;
      record.recordId = clientVersion.id;
      record.serverData = JSON.stringify(serverVersion);
      record.clientData = JSON.stringify(clientVersion);
      record.resolved = false;
      record.createdAt = new Date().getTime();
    });
  }

  /**
   * Helper method to prepare a record for local database
   */
  private prepareRecord(raw: any): any {
    // Convert API format to WatermelonDB format
    const record: any = { ...raw };
    
    // Handle date fields
    if (record.created_at) {
      record.createdAt = new Date(record.created_at);
      delete record.created_at;
    }
    
    if (record.updated_at) {
      record.updatedAt = new Date(record.updated_at);
      delete record.updated_at;
    }
    
    // Handle snake_case to camelCase conversion
    for (const key in record) {
      if (key.includes('_') && !key.startsWith('_')) {
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        if (key !== camelKey) {
          record[camelKey] = record[key];
          delete record[key];
        }
      }
    }
    
    return record;
  }

  /**
   * Helper method to prepare a record for API
   */
  private prepareRecordForSync(record: Model): any {
    const raw = record._raw;
    
    // Create a representation suitable for the API
    const prepared: any = { ...raw };
    
    // Remove WatermelonDB internal fields
    delete prepared._status;
    delete prepared._changed;
    
    // Convert camelCase to snake_case for API
    for (const key in prepared) {
      if (!key.startsWith('_') && /[A-Z]/.test(key)) {
        const snakeKey = key.replace(/([A-Z])/g, (_, letter) => `_${letter.toLowerCase()}`);
        if (key !== snakeKey) {
          prepared[snakeKey] = prepared[key];
          delete prepared[key];
        }
      }
    }
    
    return prepared;
  }

  /**
   * Check if a change set is empty
   */
  private isEmptyChangeSet(changeSet: ChangeSet): boolean {
    return Object.keys(changeSet).length === 0;
  }

  /**
   * Check if a model's changes are empty
   */
  private isEmptyModelChanges(changes: any): boolean {
    const { created = [], updated = [], deleted = [] } = changes;
    return created.length === 0 && updated.length === 0 && deleted.length === 0;
  }

  /**
   * Get API URL for a given endpoint
   */
  private getApiUrl(endpoint: string): string {
    return `${API_URL}/${endpoint}`;
  }

  /**
   * Get last sync timestamp
   */
  private async getLastSyncTimestamp(): Promise<number> {
    // Implementation would typically read from async storage or similar
    // For simplicity, returning a recent timestamp or 0 if not found
    try {
      // Placeholder implementation
      return 0; // Default to epoch start if no previous sync
    } catch (error) {
      Logger.error('Error getting last sync timestamp:', error);
      return 0;
    }
  }

  /**
   * Update last sync timestamp to current time
   */
  private async updateLastSyncTimestamp(): Promise<void> {
    // Implementation would typically write to async storage or similar
    try {
      const timestamp = new Date().getTime();
      // Placeholder implementation
      Logger.info(`Updated last sync timestamp to ${new Date(timestamp).toISOString()}`);
    } catch (error) {
      Logger.error('Error updating last sync timestamp:', error);
    }
  }
} 