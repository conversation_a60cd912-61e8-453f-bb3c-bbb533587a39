/**
 * Service pour gérer les configurations des tableaux de bord personnalisables;
 */
import AsyncStorage from '@react-native-async-storage/async-storage'
import { DashboardConfig, DashboardPreferences, MetricConfig } from '../types/dashboardTypes'
import { unifiedMonitoring, LogLevel } from '../utils/unifiedMonitoring'

// Clés de stockage;
const STORAGE_KEYS = {
  DASHBOARD_CONFIGS: 'dashboardConfigs',
  DASHBOARD_PREFERENCES: 'dashboardPreferences'
}

// Configuration par défaut pour les préférences;
const DEFAULT_PREFERENCES: DashboardPreferences = {
  lastUsedDashboard: 'default',
  defaultDashboard: 'default',
  colorTheme: 'system',
  chartAnimations: true,
  autoRefresh: true
}

// Configuration par défaut pour un tableau de bord;
export const DEFAULT_DASHBOARD_CONFIG: DashboardConfig = {
  id: 'default',
  name: 'Tableau de bord par défaut',
  description: 'Tableau de bord par défaut avec les métriques essentielles',
  views: [
    {
      id: 'main',
      name: 'Général',
      metrics: [],
      layout: {
        columns: 2,
        rowHeight: 120,
        groupByCategory: true
      },
      filters: {
        categories: ['performance', 'http', 'component']
      },
      refreshInterval: 30
    },
    {
      id: 'http',
      name: 'API & Réseau',
      metrics: [],
      layout: {
        columns: 1,
        rowHeight: 150,
        groupByCategory: false
      },
      filters: {
        categories: ['http']
      }
    },
    {
      id: 'performance',
      name: 'Performance',
      metrics: [],
      layout: {
        columns: 2,
        rowHeight: 120,
        groupByCategory: false
      },
      filters: {
        categories: ['performance', 'component']
      }
    }
  ],
  defaultView: 'main',
  createdAt: Date.now(),
  updatedAt: Date.now()
}

/**
 * Service pour gérer les configurations des tableaux de bord
 */
class DashboardConfigService {
  private initialized: boolean = false;

  /**
   * Initialise le service;
   */
  async initialize(): Promise<void> {
    try {
      if(this.initialized) { return; }

      // Vérifier si des configurations existent déjà
      const configsStr = await AsyncStorage.getItem(STORAGE_KEYS.DASHBOARD_CONFIGS);
      
      if(!configsStr) {
        // Aucune configuration trouvée, créer la configuration par défaut;
        await this.createDefaultDashboard();
      }
      
      // Vérifier si des préférences existent déjà
      const prefsStr = await AsyncStorage.getItem(STORAGE_KEYS.DASHBOARD_PREFERENCES);
      
      if(!prefsStr) {
        // Aucune préférence trouvée, créer les préférences par défaut;
        await AsyncStorage.setItem(
          STORAGE_KEYS.DASHBOARD_PREFERENCES,
          JSON.stringify(DEFAULT_PREFERENCES)
        );
      }
      
      this.initialized = true;
      unifiedMonitoring.log(LogLevel.INFO, 'Service de configuration des tableaux de bord initialisé');
    } catch(error) {
      console.error('Erreur lors de l\'initialisation du service de configuration des tableaux de bord:', error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.initialize' 
      });
    }
  }

  /**
   * Crée le tableau de bord par défaut;
   */
  private async createDefaultDashboard(): Promise<void> {
    try {
      // Obtenir les métriques actuelles;
      const metrics = this.getDefaultMetrics();

      // Cloner la configuration par défaut;
      const defaultConfig = { ...DEFAULT_DASHBOARD_CONFIG };
      
      // Appliquer les métriques à chaque vue;
      defaultConfig.views = defaultConfig.views.map(view => {
        // Filtrer les métriques par catégorie;
        const viewMetrics = metrics.filter(metric => 
          view.filters?.categories?.includes(metric.category || 'other')
        );
        
        return {
          ...view,
          metrics: viewMetrics
        };
      });
      
      // Sauvegarder la configuration;
      await AsyncStorage.setItem(
        STORAGE_KEYS.DASHBOARD_CONFIGS,
        JSON.stringify([defaultConfig])
      );
    } catch(error) {
      console.error('Erreur lors de la création du tableau de bord par défaut:', error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.createDefaultDashboard' 
      });
    }
  }

  /**
   * Obtient les métriques par défaut;
   */
  private getDefaultMetrics(): MetricConfig[] {
    try {
      // Obtenir les statistiques actuelles;
      const stats = unifiedMonitoring.getPerformanceStats();
      
      if (!stats) return [];
      
      // Convertir les statistiques en métriques;
      const metrics: MetricConfig[] = [];
      let order = 0;
      
      for (const [name, data] of Object.entries(stats)) {
        // Déterminer la catégorie;
        let category = 'other';
        if (name.startsWith('http.')) {
          category = 'http';
        } else if (name.startsWith('component.')) {
          category = 'component';
        } else if (name.startsWith('performance.')) {
          category = 'performance';
        } else if (name.startsWith('operation.')) {
          category = 'operation';
        } else if (name.startsWith('analytics.')) {
          category = 'analytics';
        }
        
        // Extraire le nom d'affichage;
        const displayName = name.includes('.') ? name.split('.').slice(1).join('.') : name;
        
        // Déterminer le format;
        let format = '';
        if (name.includes('duration') || name.includes('time')) {
          format = 'ms';
        } else if (name.includes('size') || name.includes('bytes')) {
          format = 'kb';
        }
        
        // Ajouter la métrique;
        metrics.push({
          id: `default_${name}`,
          name,
          displayName,
          category,
          visible: true,
          order: order++,
          format,
          chartType: 'line'
        });
      }
      
      return metrics;
    } catch(error) {
      console.error('Erreur lors de la récupération des métriques par défaut:', error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.getDefaultMetrics' 
      });
      return [];
    }
  }

  /**
   * Récupère toutes les configurations de tableaux de bord;
   */
  async getDashboardConfigs(): Promise<DashboardConfig[]> {
    try {
      await this.initialize();
      
      const configsStr = await AsyncStorage.getItem(STORAGE_KEYS.DASHBOARD_CONFIGS);
      
      if(!configsStr) {
        return [DEFAULT_DASHBOARD_CONFIG];
      }
      
      return JSON.parse(configsStr);
    } catch(error) {
      console.error('Erreur lors de la récupération des configurations de tableaux de bord:', error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.getDashboardConfigs' 
      });
      return [DEFAULT_DASHBOARD_CONFIG];
    }
  }

  /**
   * Récupère une configuration de tableau de bord par son ID;
   */
  async getDashboardConfig(id: string): Promise<DashboardConfig | undefined> {
    try {
      const configs = await this.getDashboardConfigs();
      return configs.find(config => config.id === id);
    } catch(error) {
      console.error(`Erreur lors de la récupération de la configuration du tableau de bord ${id}:`, error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.getDashboardConfig' 
      });
      return undefined;
    }
  }

  /**
   * Crée une nouvelle configuration de tableau de bord;
   */
  async createDashboardConfig(config: Omit<DashboardConfig, 'createdAt' | 'updatedAt'>): Promise<DashboardConfig> {
    try {
      const configs = await this.getDashboardConfigs();
      
      // Vérifier si l'ID existe déjà
      if (configs.some(c => c.id === config.id)) {
        throw new Error(`Une configuration avec l'ID ${config.id} existe déjà`);
      }
      
      // Créer la nouvelle configuration;
      const newConfig: DashboardConfig = {
        ...config,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
      
      // Ajouter à la liste et sauvegarder;
      const updatedConfigs = [...configs, newConfig];
      await AsyncStorage.setItem(
        STORAGE_KEYS.DASHBOARD_CONFIGS,
        JSON.stringify(updatedConfigs)
      );
      
      return newConfig;
    } catch(error) {
      console.error('Erreur lors de la création d\'une configuration de tableau de bord:', error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.createDashboardConfig' 
      });
      throw error;
    }
  }

  /**
   * Met à jour une configuration de tableau de bord;
   */
  async updateDashboardConfig(id: string, updates: Partial<DashboardConfig>): Promise<DashboardConfig | undefined> {
    try {
      const configs = await this.getDashboardConfigs();
      
      // Trouver l'index de la configuration;
      const index = configs.findIndex(config => config.id === id);
      
      if(index === -1) {
        console.warn(`Configuration avec l'ID ${id} non trouvée`);
        return undefined;
      }
      
      // Mettre à jour la configuration;
      const updatedConfig: DashboardConfig = {
        ...configs[index],
        ...updates,
        id, // S'assurer que l'ID ne change pas;
        updatedAt: Date.now()
      };
      
      // Remplacer la configuration dans la liste;
      configs[index] = updatedConfig;
      
      // Sauvegarder la liste;
      await AsyncStorage.setItem(
        STORAGE_KEYS.DASHBOARD_CONFIGS,
        JSON.stringify(configs)
      );
      
      return updatedConfig;
    } catch(error) {
      console.error(`Erreur lors de la mise à jour de la configuration du tableau de bord ${id}:`, error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.updateDashboardConfig' 
      });
      throw error;
    }
  }

  /**
   * Supprime une configuration de tableau de bord;
   */
  async deleteDashboardConfig(id: string): Promise<boolean> {
    try {
      // Empêcher la suppression du tableau de bord par défaut;
      if(id === 'default') {
        // Au lieu de supprimer, réinitialiser;
        await this.createDefaultDashboard();
        return true;
      }
      
      const configs = await this.getDashboardConfigs();
      
      // Filtrer la configuration à supprimer;
      const newConfigs = configs.filter(config => config.id !== id);
      
      if(newConfigs.length === configs.length) {
        // Aucune configuration supprimée;
        return false;
      }
      
      // Sauvegarder la nouvelle liste;
      await AsyncStorage.setItem(
        STORAGE_KEYS.DASHBOARD_CONFIGS,
        JSON.stringify(newConfigs)
      );
      
      // Mettre à jour les préférences si nécessaire;
      const prefs = await this.getPreferences();
      
      if(prefs.lastUsedDashboard === id || prefs.defaultDashboard === id) {
        await this.updatePreferences({
          lastUsedDashboard: prefs.lastUsedDashboard === id ? 'default' : prefs.lastUsedDashboard,
          defaultDashboard: prefs.defaultDashboard === id ? 'default' : prefs.defaultDashboard
        });
      }
      
      return true;
    } catch(error) {
      console.error(`Erreur lors de la suppression de la configuration du tableau de bord ${id}:`, error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.deleteDashboardConfig' 
      });
      return false;
    }
  }

  /**
   * Récupère les préférences de l'utilisateur;
   */
  async getPreferences(): Promise<DashboardPreferences> {
    try {
      await this.initialize();
      
      const prefsStr = await AsyncStorage.getItem(STORAGE_KEYS.DASHBOARD_PREFERENCES);
      
      if(!prefsStr) {
        return DEFAULT_PREFERENCES;
      }
      
      return { ...DEFAULT_PREFERENCES, ...JSON.parse(prefsStr) };
    } catch(error) {
      console.error('Erreur lors de la récupération des préférences:', error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.getPreferences' 
      });
      return DEFAULT_PREFERENCES;
    }
  }

  /**
   * Met à jour les préférences de l'utilisateur;
   */
  async updatePreferences(updates: Partial<DashboardPreferences>): Promise<DashboardPreferences> {
    try {
      const prefs = await this.getPreferences();
      
      // Mettre à jour les préférences;
      const updatedPrefs = {
        ...prefs,
        ...updates
      };
      
      // Sauvegarder les préférences;
      await AsyncStorage.setItem(
        STORAGE_KEYS.DASHBOARD_PREFERENCES,
        JSON.stringify(updatedPrefs)
      );
      
      return updatedPrefs;
    } catch(error) {
      console.error('Erreur lors de la mise à jour des préférences:', error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardConfigService.updatePreferences' 
      });
      throw error;
    }
  }
}

// Instance unique du service;
export const dashboardConfigService = new DashboardConfigService() 