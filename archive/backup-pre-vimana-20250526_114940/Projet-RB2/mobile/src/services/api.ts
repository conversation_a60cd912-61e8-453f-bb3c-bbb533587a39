/**
 * Configuration et services API
 */

import { API_CONFIG } from '../config/api.config';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthService } from './auth.service'
import NetInfo from '@react-native-community/netinfo'
import { EventEmitter } from '../utils/eventEmitter'
import { cacheManager, CachePriority } from '../utils/cacheManager';
import { offlineManager } from '../utils/offlineManager';
import { AppError, NetworkError } from '../utils/errors';

// Création d'une instance axios avec les configurations par défaut
export const api = axios.create({
  baseURL: 'https://api.example.com/v1', // Remplacer par l'URL de base de votre API
  timeout: API_CONFIG.REQUEST_TIMEOUT,
  headers: API_CONFIG.DEFAULT_HEADERS,
});

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
  async (config) => {
    // Vérification de la connexion Internet
    const netInfo = await NetInfo.fetch()
    if(!netInfo.isConnected) {
      throw new NetworkError('Pas de connexion Internet');
    }

    const token = await AsyncStorage.getItem(AuthService.TOKEN_KEY);
    if(token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Intercepteur pour gérer les erreurs et le refresh token
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    // Si l'erreur est 401 (non autorisé) et qu'il n'y a pas déjà eu de tentative de refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Tentative de rafraîchissement du token
        const newToken = await AuthService.refreshToken()
        
        // Mise à jour des tokens
        AsyncStorage.setItem(AuthService.TOKEN_KEY, newToken);
        
        // Mise à jour de l'en-tête d'autorisation
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        
        // Réessayer la requête d'origine
        return api.request(originalRequest);
      } catch (refreshError) {
        // Échec du rafraîchissement du token, déconnexion
        AsyncStorage.removeItem(AuthService.TOKEN_KEY);
        
        // Redirection vers la page de connexion ou notification à l'utilisateur
        EventEmitter.emit('logout');
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Type pour les options de cache
export interface CacheOptions {
  enabled: boolean;
  ttl?: number;
  priority?: CachePriority;
  tags?: string[];
  revalidate?: boolean;
}

// Type pour les priorités offline
type OfflinePriority = 'low' | 'medium' | 'high';

// Type pour les options de requête étendues
export interface EnhancedRequestOptions {
  cache?: CacheOptions;
  retry?: boolean | {
    maxRetries: number;
    initialDelayMs: number;
    maxDelayMs: number;
    backoffFactor: number;
    jitter: boolean;
    retryableStatusCodes: number[];
  };
  offline?: {
    enabled: boolean;
    priority?: OfflinePriority;
    expiration?: number;
    fallbackStrategy?: string;
  };
  metrics?: {
    name: string;
    trackPerformance: boolean;
  };
}

// API améliorée avec fonctionnalités de cache et offline
export const enhancedApi = {
  // Implémentation de la méthode GET avec gestion du cache et offline
  async get(url: string, options: EnhancedRequestOptions = {}) {
    // Vérification du cache si activé
    if (options.cache?.enabled) {
      const cacheKey = `api:${url}`;
      const cachedData = await cacheManager.get(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }
    
    // Effectuer la requête
    const response = await api.get(url);
    
    // Mettre en cache si activé
    if (options.cache?.enabled) {
      const cacheKey = `api:${url}`;
      await cacheManager.set(cacheKey, response, {
        ttl: options.cache.ttl,
        priority: options.cache.priority,
        tags: options.cache.tags
      });
    }
    
    return response;
  },
  
  // Implémentation de la méthode POST avec gestion offline
  async post(url: string, data: any = {}, options: EnhancedRequestOptions = {}) {
    // Si mode offline activé et pas de connexion
    if (options.offline?.enabled && !offlineManager.isDeviceOnline()) {
      // Ajouter à la file d'attente
      await offlineManager.queueOperation({
        method: 'POST',
        url,
        data,
        priority: (options.offline.priority || 'medium') as OfflinePriority,
        expiration: options.offline.expiration
      });
      
      // Retourner une réponse simulée
      return { 
        data: { success: true, queued: true },
        status: 202,
        statusText: 'Accepted (Offline)'
      };
    }
    
    return api.post(url, data);
  },
  
  // Implémentation de la méthode PUT avec gestion offline
  async put(url: string, data: any = {}, options: EnhancedRequestOptions = {}) {
    // Si mode offline activé et pas de connexion
    if (options.offline?.enabled && !offlineManager.isDeviceOnline()) {
      // Ajouter à la file d'attente
      await offlineManager.queueOperation({
        method: 'PUT',
        url,
        data,
        priority: (options.offline.priority || 'medium') as OfflinePriority,
        expiration: options.offline.expiration
      });
      
      // Retourner une réponse simulée
      return { 
        data: { success: true, queued: true },
        status: 202,
        statusText: 'Accepted (Offline)'
      };
    }
    
    return api.put(url, data);
  },
  
  // Implémentation de la méthode DELETE avec gestion offline
  async delete(url: string, options: EnhancedRequestOptions = {}) {
    // Si mode offline activé et pas de connexion
    if (options.offline?.enabled && !offlineManager.isDeviceOnline()) {
      // Ajouter à la file d'attente
      await offlineManager.queueOperation({
        method: 'DELETE',
        url,
        priority: (options.offline.priority || 'medium') as OfflinePriority,
        expiration: options.offline.expiration
      });
      
      // Retourner une réponse simulée
      return { 
        data: { success: true, queued: true },
        status: 202,
        statusText: 'Accepted (Offline)'
      };
    }
    
    return api.delete(url);
  },
  
  // Méthodes spécifiques pour la gestion du cache et des opérations offline
  invalidateCache: async (tags: string[]): Promise<void> => {
    await cacheManager.invalidateByTags(tags);
  },
  
  syncPendingOperations: async (): Promise<boolean> => {
    return await offlineManager.syncOperations();
  },
  
  getPendingOperationsCount: (): number => {
    return offlineManager.getPendingOperations().length;
  }
};