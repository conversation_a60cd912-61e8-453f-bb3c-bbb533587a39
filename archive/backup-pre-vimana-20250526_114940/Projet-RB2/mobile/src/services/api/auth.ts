import { API_URL } from '../../config';

// Type pour les réponses d'erreur
interface ErrorResponse {
  message: string;
}

// Fonction pour réinitialiser le mot de passe
export const resetPassword = async (email: string): Promise<void> => {
  try {
    const response = await fetch(`${API_URL}/auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      const errorData = await response.json() as ErrorResponse;
      throw new Error(errorData.message || 'Échec de la réinitialisation du mot de passe');
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Une erreur est survenue lors de la réinitialisation du mot de passe');
  }
};

// Fonction pour se connecter
export const login = async (email: string, password: string): Promise<any> => {
  try {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      const errorData = await response.json() as ErrorResponse;
      throw new Error(errorData.message || 'Échec de la connexion');
    }

    return await response.json();
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Une erreur est survenue lors de la connexion');
  }
};

// Fonction pour s'inscrire
export const register = async (userData: any): Promise<any> => {
  try {
    const response = await fetch(`${API_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const errorData = await response.json() as ErrorResponse;
      throw new Error(errorData.message || 'Échec de l\'inscription');
    }

    return await response.json();
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Une erreur est survenue lors de l\'inscription');
  }
};

// Fonction pour se déconnecter
export const logout = async (token: string): Promise<void> => {
  try {
    const response = await fetch(`${API_URL}/auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json() as ErrorResponse;
      throw new Error(errorData.message || 'Échec de la déconnexion');
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Une erreur est survenue lors de la déconnexion');
  }
}; 