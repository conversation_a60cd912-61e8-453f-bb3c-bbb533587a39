import axios, { AxiosInstance } from 'axios';
import { API_URL } from '../../config';
import { SyncStats } from '../../types/sync';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import pako from 'pako';
import { Buffer } from 'buffer';

// Clé pour le token d'authentification
const AUTH_TOKEN_KEY = '@app:authToken';

/**
 * Adaptateur de synchronisation pour communiquer avec l'API Prisma
 */
class SyncApiAdapter {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: `${API_URL}/api/sync`,
      timeout: 30000, // 30 secondes de timeout
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Client-Platform': Platform.OS,
        'X-Client-Version': Platform.Version,
      }
    });

    // Configurer les intercepteurs pour ajouter le token
    this.api.interceptors.request.use(async (config) => {
      // Récupérer le token si nous ne l'avons pas déjà
      if (!this.token) {
        this.token = await this.getAuthToken();
      }

      if (this.token) {
        config.headers['Authorization'] = `Bearer ${this.token}`;
      }

      return config;
    });
  }

  /**
   * Récupère le token d'authentification depuis AsyncStorage
   */
  private async getAuthToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(AUTH_TOKEN_KEY);
    } catch (error) {
      console.error('Erreur lors de la récupération du token:', error);
      return null;
    }
  }

  /**
   * Compresse des données JSON
   */
  private compressData(data: any): string {
    const jsonString = JSON.stringify(data);
    const compressed = pako.deflate(jsonString);
    return Buffer.from(compressed).toString('base64');
  }

  /**
   * Décompresse des données
   */
  private decompressData(compressedBase64: string): any {
    const compressed = Buffer.from(compressedBase64, 'base64');
    const decompressed = pako.inflate(compressed, { to: 'string' });
    const decodedText = new TextDecoder().decode(decompressed);
    return JSON.parse(decodedText);
  }

  /**
   * Récupère les changements depuis la date spécifiée
   * @param lastPulledAt Timestamp de la dernière synchronisation
   * @param tables Tables à synchroniser
   * @param compression Utiliser la compression
   */
  async pull(lastPulledAt: number | null, tables: string[], compression = false): Promise<any> {
    try {
      const response = await this.api.post('/pull', {
        lastPulledAt,
        tables,
        compression
      });

      // Si les données sont compressées, les décompresser
      if (compression && response.data.compressed) {
        return this.decompressData(response.data.data);
      }

      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des données:', error);
      throw error;
    }
  }

  /**
   * Envoie les changements locaux au serveur
   * @param changes Changements à pousser
   * @param lastPulledAt Timestamp de la dernière synchronisation
   * @param compression Utiliser la compression
   */
  async push(changes: any, lastPulledAt: number | null, compression = false): Promise<any> {
    try {
      let payload = { changes, lastPulledAt };

      // Compresser les données si nécessaire
      if (compression) {
        const compressedData = this.compressData(payload);
        payload = { 
          compressed: true, 
          data: compressedData 
        } as any;
      }

      const response = await this.api.post('/push', payload);

      // Si les données sont compressées, les décompresser
      if (compression && response.data.compressed) {
        return this.decompressData(response.data.data);
      }

      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'envoi des changements:', error);
      throw error;
    }
  }

  /**
   * Récupère le schéma depuis le serveur
   */
  async getSchema(): Promise<any> {
    try {
      const response = await this.api.get('/schema');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du schéma:', error);
      throw error;
    }
  }

  /**
   * Envoie des statistiques de synchronisation au serveur
   */
  async reportStats(stats: SyncStats): Promise<void> {
    try {
      await this.api.post('/stats', { stats });
    } catch (error) {
      console.error('Erreur lors de l\'envoi des statistiques:', error);
      // On ne propage pas l'erreur car c'est non critique
    }
  }

  /**
   * Vérifie la connexion au serveur
   */
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.api.get('/health');
      return response.status === 200;
    } catch (error) {
      console.error('Erreur de connexion au serveur:', error);
      return false;
    }
  }
}

// Exporter une instance unique
export const syncApi = new SyncApiAdapter();
export default syncApi; 