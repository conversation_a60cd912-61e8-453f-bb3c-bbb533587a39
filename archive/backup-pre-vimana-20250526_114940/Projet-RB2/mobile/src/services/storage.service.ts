import { setGenerator } from '@nozbe/watermelondb/utils/common/randomId';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { appSchema, tableSchema } from '@nozbe/watermelondb/Schema';
import { Database } from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';

// Import des modèles par défaut
import Retreat from '../models/Retreat';
import User from '../models/User';
import Booking from '../models/Booking';
import Notification from '../models/Notification';
import StoragePreference from '../models/StoragePreference';
import IPFSNode from '../models/IPFSNode';

// Set up UUID generator for WatermelonDB
setGenerator(() => uuidv4());

// Export du schema pour WatermelonDB avec le format correct
export const schemas = appSchema({
  version: 1,
  tables: [
    tableSchema({
      name: 'retreats',
      columns: [
        { name: 'name', type: 'string' },
        { name: 'description', type: 'string' },
        { name: 'location', type: 'string' },
        { name: 'image_url', type: 'string', isOptional: true },
        { name: 'start_date', type: 'number' },
        { name: 'end_date', type: 'number' },
        { name: 'price', type: 'number' },
        { name: 'status', type: 'string' },
        { name: 'capacity', type: 'number' },
        { name: 'rating', type: 'number', isOptional: true },
        { name: 'review_count', type: 'number', isOptional: true },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' }
      ]
    }),
    
    tableSchema({
      name: 'users',
      columns: [
        { name: 'email', type: 'string' },
        { name: 'first_name', type: 'string' },
        { name: 'last_name', type: 'string' },
        { name: 'avatar_url', type: 'string', isOptional: true },
        { name: 'phone', type: 'string', isOptional: true },
        { name: 'role', type: 'string' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' }
      ]
    }),
    
    tableSchema({
      name: 'bookings',
      columns: [
        { name: 'retreat_id', type: 'string' },
        { name: 'user_id', type: 'string' },
        { name: 'status', type: 'string' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' }
      ]
    }),
    
    tableSchema({
      name: 'notifications',
      columns: [
        { name: 'type', type: 'string' },
        { name: 'title', type: 'string' },
        { name: 'message', type: 'string' },
        { name: 'data', type: 'string' },
        { name: 'user_id', type: 'string' },
        { name: 'read', type: 'boolean' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' }
      ]
    }),
    
    tableSchema({
      name: 'storage_preferences',
      columns: [
        { name: 'key', type: 'string' },
        { name: 'value', type: 'string' }
      ]
    }),
    
    tableSchema({
      name: 'ipfs_nodes',
      columns: [
        { name: 'address', type: 'string' },
        { name: 'status', type: 'string' },
        { name: 'last_sync', type: 'string', isOptional: true },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' }
      ]
    })
  ]
});

// Créer l'adaptateur SQLite
const adapter = new SQLiteAdapter({
  schema: schemas,
  dbName: 'retreats',
  onSetUpError: error => {
    console.error("Erreur d'initialisation de la base de données:", error);
  }
});

// Créer et exporter la base de données
export const database = new Database({
  adapter,
  modelClasses: [
    User,
    Retreat,
    Booking,
    Notification,
    StoragePreference,
    IPFSNode
  ]
});
