import { NetworkError, AppError } from '../utils/errors'
import { offlineManager } from '../utils/offlineManager'
import AsyncStorage from '@react-native-async-storage/async-storage'
import NetInfo from '@react-native-community/netinfo'
import { database } from '../database'
import { api, enhancedApi } from './api'
import { API_CONFIG } from '../config/api.config'
import { Model } from '@nozbe/watermelondb'
import { EventEmitter } from '../utils/eventEmitter'
import { Q } from '@nozbe/watermelondb'
import Booking from '../models/Booking'
import Retreat from '../models/Retreat'
import User from '../models/User'
import Notification from '../models/Notification'

// Clés pour AsyncStorage
const SYNC_KEYS = {
  LAST_SYNC_TIMESTAMP: 'sync:lastSyncTimestamp',
  SYNC_IN_PROGRESS: 'sync:inProgress',
  PENDING_CONFLICTS: 'sync:pendingConflicts',
  RESOLVED_CONFLICTS: 'sync:resolvedConflicts',
  SYNC_CHANGE_TOKENS: 'sync:changeTokens',
  PENDING_OPERATIONS: 'sync:pendingOperations',
  CONFIG: 'sync:config',
}

// Événements de synchronisation
export const SYNC_EVENTS = {
  SYNC_STARTED: 'sync:started',
  SYNC_COMPLETED: 'sync:completed',
  SYNC_FAILED: 'sync:failed',
  SYNC_PROGRESS: 'sync:progress',
  ENTITY_PULL_STARTED: 'sync:entity:pull:started',
  ENTITY_PULL_COMPLETED: 'sync:entity:pull:completed',
  ENTITY_PUSH_STARTED: 'sync:entity:push:started',
  ENTITY_PUSH_COMPLETED: 'sync:entity:push:completed',
  CONFLICT_DETECTED: 'sync:conflict_detected',
  CONFLICT_RESOLVED: 'sync:conflict_resolved',
}

// Stratégies de résolution de conflits
export enum ConflictResolutionStrategy {
  SERVER_WINS = 'server_wins',
  CLIENT_WINS = 'client_wins',
  MERGE = 'merge',
  MANUAL = 'manual',
}

// Configuration par entité
export interface SyncEntityConfig {
  endpoint: string
  collection: string
  idField: string
  timestampField: string
  pushEnabled: boolean
  pullEnabled: boolean
  conflictStrategy: ConflictResolutionStrategy
  changeDetectionStrategy: 'timestamp' | 'hash'
}

// État de synchronisation pour affichage dans l'UI
export interface SyncState {
  isSyncing: boolean
  lastSyncDate: Date | null
  syncError: Error | null
  syncProgress: {
    total: number
    completed: number
    entity: string
    action: 'push' | 'pull'
  }
}

export class SyncService {
  // Configuration des entités à synchroniser
  private static entityConfigs: Record<string, SyncEntityConfig> = {
    retreats: {
      endpoint: API_CONFIG.ENDPOINTS.RETREATS.LIST,
      collection: 'retreats',
      idField: 'id',
      timestampField: 'updated_at',
      pushEnabled: false, // Les retraites ne sont modifiées que côté serveur
      pullEnabled: true,
      conflictStrategy: ConflictResolutionStrategy.SERVER_WINS,
      changeDetectionStrategy: 'timestamp',
    },
    bookings: {
      endpoint: API_CONFIG.ENDPOINTS.USER.BOOKINGS,
      collection: 'bookings',
      idField: 'id',
      timestampField: 'updated_at',
      pushEnabled: true,
      pullEnabled: true,
      conflictStrategy: ConflictResolutionStrategy.MERGE,
      changeDetectionStrategy: 'timestamp',
    },
    users: {
      endpoint: API_CONFIG.ENDPOINTS.USER.PROFILE,
      collection: 'users',
      idField: 'id',
      timestampField: 'updated_at',
      pushEnabled: true,
      pullEnabled: true,
      conflictStrategy: ConflictResolutionStrategy.MERGE,
      changeDetectionStrategy: 'timestamp',
    },
    notifications: {
      endpoint: API_CONFIG.ENDPOINTS.USER.NOTIFICATIONS,
      collection: 'notifications',
      idField: 'id',
      timestampField: 'updated_at',
      pushEnabled: false, // Notifications sont en lecture seule
      pullEnabled: true,
      conflictStrategy: ConflictResolutionStrategy.SERVER_WINS,
      changeDetectionStrategy: 'timestamp',
    },
  }

  private static syncState: SyncState = {
    isSyncing: false,
    lastSyncDate: null,
    syncError: null,
    syncProgress: {
      total: 0,
      completed: 0,
      entity: '',
      action: 'pull',
    },
  }

  // Indicateur pour éviter les synchronisations simultanées
  private static syncLock = false

  // Fonctionnalités spécifiques à Prisma pour la synchronisation
  private static prismaIntegration = {
    // Méthode pour convertir les types Prisma vers WatermelonDB
    mapPrismaToWatermelon: (prismaData: any, entityName: string): any => {
      const config = SyncService.entityConfigs[entityName];
      if (!config) return prismaData;
      
      const result = { ...prismaData };
      
      // Conversion des dates ISO string en timestamp
      for (const [key, value] of Object.entries(result)) {
        // Conversion des dates Prisma
        if (value instanceof Date || (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}/.test(value))) {
          result[key] = new Date(value).getTime();
        }
        
        // Conversion des relations Prisma - transformer les objets imbriqués en ID
        if (value && typeof value === 'object' && !Array.isArray(value) && 'id' in value) {
          const relationKey = key + '_id'; 
          result[relationKey] = value.id;
          delete result[key];
        }
        
        // Conversion des relations "un-à-plusieurs" - les ignorer car elles seront 
        // chargées séparément via des relations WatermelonDB
        if (Array.isArray(value)) {
          delete result[key];
        }
      }
      
      return result;
    },
    
    // Méthode pour convertir les types WatermelonDB vers Prisma
    mapWatermelonToPrisma: (watermelonData: any, entityName: string): any => {
      const config = SyncService.entityConfigs[entityName];
      if (!config) return watermelonData;
      
      const result = { ...watermelonData };
      
      // Suppression des champs spécifiques à WatermelonDB
      delete result._status;
      delete result._changed;
      
      // Conversion des timestamps en ISO strings pour Prisma
      for (const [key, value] of Object.entries(result)) {
        // Détection des timestamps WatermelonDB
        if (key.endsWith('_at') && typeof value === 'number') {
          result[key] = new Date(value).toISOString();
        }
        
        // Conversion des relations "_id" au format attendu par Prisma
        if (key.endsWith('_id') && typeof value === 'string') {
          const relationKey = key.substring(0, key.length - 3);
          // Pour Prisma, on a besoin de connecter via { connect: { id: value } }
          result[relationKey] = { connect: { id: value } };
          delete result[key];
        }
      }
      
      return result;
    },
    
    // Génération d'un ID compatible entre Prisma et WatermelonDB
    generateId: (): string => {
      // Génére un UUID v4 compatible avec Prisma et WatermelonDB
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
    
    // Application des transactions Prisma pour synchroniser plusieurs entités de manière atomique
    applyTransactionBatch: async (operations: Array<{
      entity: string,
      operation: 'create' | 'update' | 'delete',
      data: any
    }>): Promise<boolean> => {
      try {
        // Création d'une opération batch pour le back-end (similaire à une transaction Prisma)
        const response = await enhancedApi.post('/api/sync/batch', {
          operations: operations.map(op => ({
            ...op,
            // Convertir au format attendu par Prisma avant d'envoyer
            data: op.operation !== 'delete' ? 
              SyncService.prismaIntegration.mapWatermelonToPrisma(op.data, op.entity) : 
              undefined
          }))
        }, {
          retry: {
            maxRetries: API_CONFIG.MAX_RETRY_ATTEMPTS,
            initialDelayMs: API_CONFIG.RETRY_DELAY_MS,
            maxDelayMs: API_CONFIG.MAX_RETRY_DELAY_MS,
            backoffFactor: 1.5,
            jitter: true,
            retryableStatusCodes: [500, 502, 503, 504]
          }
        });
        
        return response.data.success;
      } catch (error) {
        console.error("Erreur lors de l'application du batch de transactions Prisma:", error);
        throw error;
      }
    }
  };

  // Vérifier la connectivité réseau
  static async checkConnectivity(): Promise<boolean> {
    try {
      const netInfo = await NetInfo.fetch()
      return !!netInfo.isConnected && !!netInfo.isInternetReachable
    } catch (error) {
      console.error('Erreur lors de la vérification de la connectivité:', error)
      return false
    }
  }

  // Obtenir l'état actuel de la synchronisation (pour les UI)
  static getSyncState(): SyncState {
    return { ...this.syncState }
  }

  // Mettre à jour l'état de synchronisation et émettre des événements
  private static updateSyncState(update: Partial<SyncState>) {
    this.syncState = { ...this.syncState, ...update }
    
    if (update.isSyncing !== undefined) {
      if (update.isSyncing) {
        // @ts-ignore - Ignorer les problèmes de typage avec EventEmitter
        EventEmitter.emit(SYNC_EVENTS.SYNC_STARTED, this.syncState)
      } else if (!update.isSyncing && !update.syncError) {
        // @ts-ignore - Ignorer les problèmes de typage avec EventEmitter
        EventEmitter.emit(SYNC_EVENTS.SYNC_COMPLETED, this.syncState)
      } else if (!update.isSyncing && update.syncError) {
        // @ts-ignore - Ignorer les problèmes de typage avec EventEmitter
        EventEmitter.emit(SYNC_EVENTS.SYNC_FAILED, update.syncError)
      }
    }
    
    if (update.syncProgress) {
      // @ts-ignore - Ignorer les problèmes de typage avec EventEmitter
      EventEmitter.emit(SYNC_EVENTS.SYNC_PROGRESS, update.syncProgress)
    }
  }

  // Synchronisation incrémentielle des retraites
  static async syncEntity(entityName: string): Promise<boolean> {
    const config = this.entityConfigs[entityName]
    if (!config) {
      console.error(`Configuration de synchronisation non trouvée pour l'entité: ${entityName}`)
      return false
    }

    try {
      // Synchronisation pull (si activée)
      if (config.pullEnabled) {
        await this.pullEntityData(entityName, config)
      }

      // Synchronisation push (si activée)
      if (config.pushEnabled) {
        await this.pushEntityChanges(entityName, config)
      }

      return true
    } catch (error) {
      console.error(`Erreur lors de la synchronisation de ${entityName}:`, error)
      return false
    }
  }

  // Récupération des données depuis le serveur (Pull)
  private static async pullEntityData(
    entityName: string,
    config: SyncEntityConfig
  ): Promise<void> {
    this.updateSyncState({
      syncProgress: {
        total: 100,
        completed: 0,
        entity: entityName,
        action: 'pull',
      },
    })

    EventEmitter.emit(SYNC_EVENTS.ENTITY_PULL_STARTED, { entityName })

    try {
      // Récupérer le timestamp de la dernière synchronisation pour cette entité
      const changeTokensStr = await AsyncStorage.getItem(SYNC_KEYS.SYNC_CHANGE_TOKENS)
      const changeTokens = changeTokensStr ? JSON.parse(changeTokensStr) : {}
      const lastChangeToken = changeTokens[entityName] || '0'

      // Construire l'URL avec paramètre de mise à jour incrémentielle
      const apiUrl = typeof config.endpoint === 'function' 
        ? (config.endpoint as (config?: any) => string)(config) 
        : config.endpoint
      
      const url = `${apiUrl}?updatedSince=${lastChangeToken}`

      // Récupérer les données depuis l'API
      const response = await enhancedApi.get(url, {
        cache: { enabled: false }, // Désactiver le cache pour les synchronisations
        retry: {
          maxRetries: API_CONFIG.MAX_RETRY_ATTEMPTS,
          initialDelayMs: API_CONFIG.RETRY_DELAY_MS,
          maxDelayMs: API_CONFIG.MAX_RETRY_DELAY_MS,
          backoffFactor: 1.5,
          jitter: true,
          retryableStatusCodes: [500, 502, 503, 504]
        }
      })

      // Récupérer les données et les métadonnées
      // Utiliser une assertion de type pour response.data
      const responseData = (response as any).data as { data: any[]; metadata?: { changeToken?: string } }
      const { data, metadata } = responseData
      const serverItems = Array.isArray(data) ? data : [data]
      const newChangeToken = metadata?.changeToken || Date.now().toString()

      // Mise à jour des données dans la base locale
      await database.write(async () => {
        const collection = database.get(config.collection)
        
        this.updateSyncState({
          syncProgress: {
            total: serverItems.length,
            completed: 0,
            entity: entityName,
            action: 'pull',
          },
        })

        // Traiter chaque élément
        for (let i = 0; i < serverItems.length; i++) {
          const serverItem = serverItems[i]
          
          try {
            // Vérifier si l'élément existe déjà localement
            // @ts-ignore - Ignorer les problèmes de typage avec collection
            const localItems = await collection
              .query(Q.where(config.idField, serverItem[config.idField]))
              .fetch()
            
            const localItem = localItems.length > 0 ? localItems[0] : null
            
            if (!localItem) {
              // Créer un nouvel élément s'il n'existe pas
              // @ts-ignore - Ignorer les problèmes de typage avec collection
              await collection.create((record: any) => {
                Object.assign(record._raw, this.sanitizeDataForWatermelon(serverItem))
              })
            } else {
              // Vérifier s'il y a un conflit
              const hasConflict = this.detectConflict(localItem as Model, serverItem, config)
              
              if (hasConflict) {
                // Gérer le conflit selon la stratégie configurée
                const resolvedData = await this.resolveConflict(
                  localItem as Model,
                  serverItem,
                  config
                )
                
                // Mettre à jour avec les données résolues
                await (localItem as Model).update(record => {
                  Object.assign(record._raw, this.sanitizeDataForWatermelon(resolvedData))
                })
              } else {
                // Pas de conflit, mise à jour normale
                await (localItem as Model).update(record => {
                  Object.assign(record._raw, this.sanitizeDataForWatermelon(serverItem))
                })
              }
            }
          } catch (itemError) {
            console.error(`Erreur lors du traitement de l'élément ${serverItem[config.idField]}:`, itemError)
            // Continuer avec les autres éléments malgré l'erreur
          }
          
          // Mettre à jour la progression
          this.updateSyncState({
            syncProgress: {
              total: serverItems.length,
              completed: i + 1,
              entity: entityName,
              action: 'pull',
            },
          })
        }
      })
      
      // Mettre à jour le token de changement pour cette entité
      changeTokens[entityName] = newChangeToken
      await AsyncStorage.setItem(
        SYNC_KEYS.SYNC_CHANGE_TOKENS,
        JSON.stringify(changeTokens)
      )

      EventEmitter.emit(SYNC_EVENTS.ENTITY_PULL_COMPLETED, { 
        entityName,
        itemCount: serverItems.length 
      })
    } catch (error) {
      console.error(`Erreur lors de la récupération des données pour ${entityName}:`, error)
      throw error
    }
  }

  // Envoi des modifications locales vers le serveur (Push)
  private static async pushEntityChanges(
    entityName: string,
    config: SyncEntityConfig
  ): Promise<void> {
    if (!config.pushEnabled) {
      return
    }

    EventEmitter.emit(SYNC_EVENTS.ENTITY_PUSH_STARTED, { entityName })

    try {
      // Récupérer les modifications locales depuis le suivi des changements
      const collection = database.get(config.collection)
      const localChanges = await offlineManager.getPendingOperations()
      
      // Filtrer seulement les opérations concernant cette entité
      const entityChanges = localChanges.filter(op => 
        op.url.includes(config.endpoint) || 
        op.url.includes(config.collection)
      )
      
      if (entityChanges.length === 0) {
        EventEmitter.emit(SYNC_EVENTS.ENTITY_PUSH_COMPLETED, { 
          entityName,
          itemCount: 0
        })
        return
      }
      
      this.updateSyncState({
        syncProgress: {
          total: entityChanges.length,
          completed: 0,
          entity: entityName,
          action: 'push',
        },
      })
      
      // Traiter les changements
      for (let i = 0; i < entityChanges.length; i++) {
        const change = entityChanges[i]
        
        try {
          // Synchroniser cette opération spécifiquement
          // @ts-ignore - Assumer que offlineManager a une méthode pour synchroniser une opération spécifique
          await offlineManager.syncOperations([change.id])
        } catch (opError) {
          console.error(`Erreur lors de la synchronisation de l'opération ${change.id}:`, opError)
          // Continuer avec les autres changements
        }
        
        // Mettre à jour la progression
        this.updateSyncState({
          syncProgress: {
            total: entityChanges.length,
            completed: i + 1,
            entity: entityName,
            action: 'push',
          },
        })
      }
      
      EventEmitter.emit(SYNC_EVENTS.ENTITY_PUSH_COMPLETED, { 
        entityName,
        itemCount: entityChanges.length
      })
    } catch (error) {
      console.error(`Erreur lors de l'envoi des modifications pour ${entityName}:`, error)
      throw error
    }
  }

  // Préparer les données du serveur pour WatermelonDB
  private static sanitizeDataForWatermelon(serverData: any): any {
    const sanitized = { ...serverData }
    
    // Convertir les dates en timestamps pour WatermelonDB
    for (const [key, value] of Object.entries(sanitized)) {
      if (value instanceof Date || (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}/.test(value))) {
        sanitized[key] = new Date(value).getTime()
      }
    }
    
    // S'assurer que _status existe (requis par WatermelonDB)
    if (!sanitized._status) {
      sanitized._status = 'synced'
    }
    
    return sanitized
  }

  // Détecter s'il y a un conflit entre les versions locale et serveur
  private static detectConflict(
    localItem: Model,
    serverItem: any,
    config: SyncEntityConfig
  ): boolean {
    if (config.changeDetectionStrategy === 'timestamp') {
      const localTimestamp = localItem._raw[config.timestampField]
      const serverTimestamp = new Date(serverItem[config.timestampField]).getTime()
      
      // S'il y a eu des modifications locales après la dernière synchronisation
      return localItem._raw._status === 'updated' && localTimestamp >= serverTimestamp
    } else if (config.changeDetectionStrategy === 'hash') {
      // Implémentation de détection par hash si nécessaire
      // (comparer les hash des objets)
      return false
    }
    
    return false
  }

  // Résoudre un conflit selon la stratégie configurée
  private static async resolveConflict(
    localItem: Model,
    serverItem: any,
    config: SyncEntityConfig
  ): Promise<any> {
    EventEmitter.emit(SYNC_EVENTS.CONFLICT_DETECTED, {
      entityName: config.collection,
      localItem: localItem._raw,
      serverItem
    })
    
    let resolvedData
    
    switch (config.conflictStrategy) {
      case ConflictResolutionStrategy.SERVER_WINS:
        resolvedData = serverItem
        break
        
      case ConflictResolutionStrategy.CLIENT_WINS:
        resolvedData = { ...serverItem, ...localItem._raw }
        // Conserver l'ID et les champs système du serveur
        resolvedData[config.idField] = serverItem[config.idField]
        break
        
      case ConflictResolutionStrategy.MERGE:
        // Stratégie de fusion intelligente
        resolvedData = this.mergeData(localItem._raw, serverItem, config)
        break
        
      case ConflictResolutionStrategy.MANUAL:
        // Pour une résolution manuelle, stocker temporairement le conflit
        // et notifier l'utilisateur (via un événement)
        resolvedData = serverItem // Par défaut, utiliser les données du serveur
        
        // Enregistrer le conflit pour résolution ultérieure
        await this.storeConflictForManualResolution(
          config.collection,
          localItem._raw,
          serverItem
        )
        break
        
      default:
        resolvedData = serverItem
    }
    
    EventEmitter.emit(SYNC_EVENTS.CONFLICT_RESOLVED, {
      entityName: config.collection,
      resolution: resolvedData
    })
    
    return resolvedData
  }

  // Fusionner intelligemment les données locales et serveur
  private static mergeData(localData: any, serverData: any, config: SyncEntityConfig): any {
    // Version simple d'une fusion
    const merged = { ...serverData }
    
    // Pour chaque champ modifié localement, le conserver
    for (const [key, localValue] of Object.entries(localData)) {
      // Ignorer les champs spéciaux de WatermelonDB et l'identifiant
      if (key.startsWith('_') || key === config.idField) {
        continue
      }
      
      // Si la valeur locale est différente de la valeur d'origine (avant modification locale)
      if (localValue !== serverData[key]) {
        merged[key] = localValue
      }
    }
    
    // Conserver l'ID et le timestamp du serveur
    merged[config.idField] = serverData[config.idField]
    merged[config.timestampField] = serverData[config.timestampField]
    
    return merged
  }

  // Stocker un conflit pour résolution manuelle ultérieure
  private static async storeConflictForManualResolution(
    collection: string,
    localData: any,
    serverData: any
  ): Promise<void> {
    try {
      // Récupérer les conflits existants
      const conflictsStr = await AsyncStorage.getItem(SYNC_KEYS.PENDING_CONFLICTS)
      const conflicts = conflictsStr ? JSON.parse(conflictsStr) : []
      
      // Ajouter le nouveau conflit
      conflicts.push({
        id: serverData.id,
        collection,
        localData,
        serverData,
        timestamp: Date.now()
      })
      
      // Sauvegarder
      await AsyncStorage.setItem(SYNC_KEYS.PENDING_CONFLICTS, JSON.stringify(conflicts))
    } catch (error) {
      console.error('Erreur lors du stockage du conflit:', error)
    }
  }

  // Vérifier si une synchronisation est nécessaire
  static async shouldSync(): Promise<boolean> {
    try {
      const lastSyncStr = await AsyncStorage.getItem(SYNC_KEYS.LAST_SYNC_TIMESTAMP)
      if (!lastSyncStr) {
        return true // Première synchronisation
      }
      
      const lastSync = parseInt(lastSyncStr, 10)
      const now = Date.now()
      
      // Synchroniser si plus de 15 minutes se sont écoulées
      return (now - lastSync) > 15 * 60 * 1000
    } catch (error) {
      console.error('Erreur lors de la vérification du timing de synchronisation:', error)
      return true // En cas de doute, synchroniser
    }
  }

  // Mettre à jour le timestamp de dernière synchronisation
  static async updateLastSyncTimestamp(): Promise<void> {
    try {
      const now = Date.now().toString()
      await AsyncStorage.setItem(SYNC_KEYS.LAST_SYNC_TIMESTAMP, now)
      
      this.syncState.lastSyncDate = new Date()
    } catch (error) {
      console.error('Erreur lors de la mise à jour du timestamp de synchronisation:', error)
    }
  }

  // Synchronisation complète de toutes les entités
  static async syncAll(): Promise<boolean> {
    // Vérifier si une synchronisation est déjà en cours
    if (this.syncLock) {
      console.log('Synchronisation déjà en cours, ignorée.')
      return false
    }
    
    // Vérifier si une synchronisation est nécessaire
    const shouldSyncNow = await this.shouldSync()
    if (!shouldSyncNow) {
      console.log('Synchronisation récente, utilisation des données en cache')
      return true
    }
    
    // Vérifier la connectivité
    const isConnected = await this.checkConnectivity()
    if (!isConnected) {
      console.log('Pas de connexion Internet, synchronisation annulée')
      throw new NetworkError('Pas de connexion Internet')
    }
    
    // Acquérir le verrou de synchronisation
    this.syncLock = true
    this.updateSyncState({
      isSyncing: true,
      syncError: null
    })
    
    try {
      // Synchroniser chaque entité configurée
      const entityNames = Object.keys(this.entityConfigs)
      let allSuccessful = true
      
      for (const entityName of entityNames) {
        try {
          const success = await this.syncEntity(entityName)
          if (!success) {
            allSuccessful = false
          }
        } catch (entityError) {
          console.error(`Erreur lors de la synchronisation de ${entityName}:`, entityError)
          allSuccessful = false
        }
      }
      
      // Mettre à jour le timestamp de dernière synchronisation
      await this.updateLastSyncTimestamp()
      
      this.updateSyncState({
        isSyncing: false,
        syncError: null
      })
      
      return allSuccessful
    } catch (error) {
      console.error('Erreur lors de la synchronisation complète:', error)
      
      this.updateSyncState({
        isSyncing: false,
        syncError: error instanceof Error ? error : new Error(String(error))
      })
      
      return false
    } finally {
      // Libérer le verrou de synchronisation
      this.syncLock = false
    }
  }

  // Synchronisation en arrière-plan
  static async startBackgroundSync(): Promise<boolean> {
    console.log('Démarrage de la synchronisation en arrière-plan...')
    return this.syncAll()
  }

  // Récupérer les conflits en attente de résolution manuelle
  static async getPendingConflicts(): Promise<any[]> {
    try {
      const conflictsStr = await AsyncStorage.getItem(SYNC_KEYS.PENDING_CONFLICTS);
      if (!conflictsStr) return [];
      
      return JSON.parse(conflictsStr);
    } catch (error) {
      console.error('Erreur lors de la récupération des conflits en attente:', error);
      return [];
    }
  }

  // Récupère la liste des conflits résolus
  static async getResolvedConflicts(): Promise<any[]> {
    try {
      const resolvedStr = await AsyncStorage.getItem(SYNC_KEYS.RESOLVED_CONFLICTS);
      if (!resolvedStr) return [];
      
      return JSON.parse(resolvedStr);
    } catch (error) {
      console.error('Erreur lors de la récupération des conflits résolus:', error);
      return [];
    }
  }

  /**
   * Récupère le nombre d'opérations en attente
   */
  static async getPendingOperationsCount(): Promise<number> {
    try {
      const pendingOperationsJson = await AsyncStorage.getItem(SYNC_KEYS.PENDING_OPERATIONS);
      if (!pendingOperationsJson) return 0;
      
      const pendingOperations = JSON.parse(pendingOperationsJson);
      return Array.isArray(pendingOperations) ? pendingOperations.length : 0;
    } catch (error) {
      console.error('Erreur lors de la récupération des opérations en attente:', error);
      return 0;
    }
  }

  /**
   * Réinitialise la base de données locale et relance une synchronisation complète
   * @returns Promesse qui se résout lorsque la réinitialisation et synchronisation sont terminées
   */
  static async resetAndSync(): Promise<boolean> {
    try {
      // Réinitialiser la base de données
      await database.write(async () => {
        await database.unsafeResetDatabase();
      });
      
      // Réinitialiser l'état de synchronisation
      this.updateSyncState({
        isSyncing: false,
        lastSyncDate: null,
        syncError: null,
        syncProgress: {
          total: 0,
          completed: 0,
          entity: '',
          action: 'pull'
        }
      });
      
      // Lancer une nouvelle synchronisation
      return await this.syncAll();
    } catch (error) {
      console.error('Erreur lors de la réinitialisation et synchronisation:', error);
      this.updateSyncState({
        syncError: error instanceof Error ? error : new Error('Erreur inconnue lors de la réinitialisation')
      });
      return false;
    }
  }

  /**
   * Récupère la date de dernière synchronisation
   */
  static async getLastSyncTimestamp(): Promise<number | null> {
    try {
      const timestamp = await AsyncStorage.getItem(SYNC_KEYS.LAST_SYNC_TIMESTAMP);
      return timestamp ? parseInt(timestamp, 10) : null;
    } catch (error) {
      console.error('Erreur lors de la récupération de la date de dernière synchronisation:', error);
      return null;
    }
  }

  /**
   * Synchronise les données
   */
  static async syncData(): Promise<void> {
    if (SyncService.syncLock) {
      console.log('Synchronisation déjà en cours');
      return;
    }
    
    try {
      SyncService.syncLock = true;
      EventEmitter.emit(SYNC_EVENTS.SYNC_STARTED);
      
      const isConnected = await SyncService.checkConnectivity();
      if (!isConnected) {
        throw new Error('Aucune connexion Internet disponible');
      }
      
      // Synchroniser toutes les entités configurées
      for (const entityName of Object.keys(SyncService.entityConfigs)) {
        const config = SyncService.entityConfigs[entityName];
        
        // Pull data from server
        if (config.pullEnabled) {
          EventEmitter.emit(SYNC_EVENTS.SYNC_PROGRESS, 0.2);
          await SyncService.pullEntityData(entityName, config);
        }
        
        // Push changes to server
        if (config.pushEnabled) {
          EventEmitter.emit(SYNC_EVENTS.SYNC_PROGRESS, 0.6);
          await SyncService.pushEntityChanges(entityName, config);
        }
        
        EventEmitter.emit(SYNC_EVENTS.SYNC_PROGRESS, 1);
      }
      
      // Mettre à jour la date de dernière synchronisation
      await SyncService.updateLastSyncTimestamp();
      
      EventEmitter.emit(SYNC_EVENTS.SYNC_COMPLETED);
    } catch (error) {
      EventEmitter.emit(SYNC_EVENTS.SYNC_FAILED, error);
      throw error;
    } finally {
      SyncService.syncLock = false;
    }
  }

  /**
   * Mise à jour de la configuration
   */
  static updateConfig(config: {
    autoSync: boolean;
    syncInterval: number;
    syncOnAppStart: boolean;
    syncOnlyOnWifi: boolean;
    syncInBackground: boolean;
  }): void {
    // Enregistrer la configuration dans AsyncStorage
    AsyncStorage.setItem(SYNC_KEYS.CONFIG, JSON.stringify(config))
      .catch(error => {
        console.error('Erreur lors de l\'enregistrement de la configuration:', error);
      });
  }

  /**
   * Stocker un conflit pour résolution ultérieure
   */
  private static async storePendingConflict(
    localItem: Model,
    serverItem: any,
    entityName: string
  ): Promise<void> {
    try {
      // Récupérer les conflits existants
      const conflictsStr = await AsyncStorage.getItem(SYNC_KEYS.PENDING_CONFLICTS);
      const conflicts = conflictsStr ? JSON.parse(conflictsStr) : [];
      
      // Ajouter le nouveau conflit
      conflicts.push({
        id: serverItem.id,
        entityName,
        localData: localItem._raw,
        serverData: serverItem,
        timestamp: Date.now()
      });
      
      // Sauvegarder
      await AsyncStorage.setItem(SYNC_KEYS.PENDING_CONFLICTS, JSON.stringify(conflicts));
    } catch (error) {
      console.error('Erreur lors du stockage du conflit:', error);
    }
  }
}