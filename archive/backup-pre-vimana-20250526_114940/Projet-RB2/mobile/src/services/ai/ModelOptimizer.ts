/**
 * Service for optimizing and managing lightweight AI models for mobile devices.
 * Supports TensorFlow Lite and CoreML model formats.
 */

import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'react-native-fs';
import { API_URL, STORAGE_KEY } from '../../config';

// Model types supported by the optimizer
export enum ModelType {
  TENSORFLOW_LITE = 'tflite',
  CORE_ML = 'mlmodel',
  ONNX = 'onnx'
}

// Model metadata interface
export interface ModelMetadata {
  id: string;
  name: string;
  version: string;
  type: ModelType;
  size: number;
  lastUpdated: string;
  path: string;
  hash: string;
}

// Model optimization options
export interface OptimizationOptions {
  quantize?: boolean;
  pruning?: boolean;
  compressionLevel?: 'none' | 'low' | 'medium' | 'high';
  targetSize?: number; // Target size in KB
  prioritizeSpeed?: boolean;
}

/**
 * Service for managing and optimizing AI models for mobile devices
 */
export class ModelOptimizer {
  private static instance: ModelOptimizer;
  private models: Map<string, ModelMetadata> = new Map();
  private modelBasePath: string;

  /**
   * Get the singleton instance of the ModelOptimizer
   */
  public static getInstance(): ModelOptimizer {
    if (!ModelOptimizer.instance) {
      ModelOptimizer.instance = new ModelOptimizer();
    }
    return ModelOptimizer.instance;
  }

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    // Set the base path for model storage based on platform
    this.modelBasePath = Platform.OS === 'ios' 
      ? `${FileSystem.DocumentDirectoryPath}/models/`
      : `${FileSystem.ExternalDirectoryPath}/models/`;
    
    // Ensure the directory exists
    this.ensureModelDirectory();
  }

  /**
   * Initialize the model optimizer
   */
  public async initialize(): Promise<void> {
    try {
      // Load cached model metadata
      await this.loadModelMetadata();
      
      // Check for model updates
      await this.checkForModelUpdates();
      
      console.log('Model optimizer initialized successfully');
    } catch (error) {
      console.error('Failed to initialize model optimizer:', error);
      throw new Error(`Model optimizer initialization failed: ${error}`);
    }
  }

  /**
   * Ensure the model directory exists
   */
  private async ensureModelDirectory(): Promise<void> {
    try {
      const exists = await FileSystem.exists(this.modelBasePath);
      if (!exists) {
        await FileSystem.mkdir(this.modelBasePath);
      }
    } catch (error) {
      console.error('Failed to create model directory:', error);
      throw new Error(`Failed to create model directory: ${error}`);
    }
  }

  /**
   * Load model metadata from local storage
   */
  private async loadModelMetadata(): Promise<void> {
    try {
      const modelMetadataJson = await AsyncStorage.getItem(`${STORAGE_KEY}model_metadata`);
      if (modelMetadataJson) {
        const modelMetadata = JSON.parse(modelMetadataJson);
        this.models = new Map(Object.entries(modelMetadata));
      }
    } catch (error) {
      console.error('Failed to load model metadata:', error);
      // Continue with empty model metadata
    }
  }

  /**
   * Save model metadata to local storage
   */
  private async saveModelMetadata(): Promise<void> {
    try {
      const modelMetadataObj = Object.fromEntries(this.models);
      await AsyncStorage.setItem(
        `${STORAGE_KEY}model_metadata`,
        JSON.stringify(modelMetadataObj)
      );
    } catch (error) {
      console.error('Failed to save model metadata:', error);
      throw new Error(`Failed to save model metadata: ${error}`);
    }
  }

  /**
   * Check for model updates from the server
   */
  private async checkForModelUpdates(): Promise<void> {
    try {
      // Get the list of available models from the server
      const response = await fetch(`${API_URL}/api/mobile/models/available`);
      if (!response.ok) {
        throw new Error(`Failed to fetch available models: ${response.statusText}`);
      }
      
      const availableModels: ModelMetadata[] = await response.json();
      
      // Check if we need to download or update any models
      for (const model of availableModels) {
        const localModel = this.models.get(model.id);
        
        // If the model doesn't exist locally or has a different version, download it
        if (!localModel || localModel.version !== model.version || localModel.hash !== model.hash) {
          await this.downloadModel(model);
        }
      }
    } catch (error) {
      console.error('Failed to check for model updates:', error);
      // Continue with existing models
    }
  }

  /**
   * Download a model from the server
   */
  private async downloadModel(model: ModelMetadata): Promise<void> {
    try {
      const modelPath = `${this.modelBasePath}${model.id}.${model.type}`;
      
      // Download the model
      const downloadResult = await FileSystem.downloadFile({
        fromUrl: `${API_URL}/api/mobile/models/download/${model.id}`,
        toFile: modelPath,
        background: true,
        discretionary: true,
        cacheable: true,
      }).promise;
      
      if (downloadResult.statusCode !== 200) {
        throw new Error(`Failed to download model: ${downloadResult.statusCode}`);
      }
      
      // Update the model metadata
      model.path = modelPath;
      this.models.set(model.id, model);
      
      // Save the updated metadata
      await this.saveModelMetadata();
      
      console.log(`Model ${model.id} downloaded successfully`);
    } catch (error) {
      console.error(`Failed to download model ${model.id}:`, error);
      throw new Error(`Failed to download model ${model.id}: ${error}`);
    }
  }

  /**
   * Get a model by ID
   */
  public async getModel(modelId: string): Promise<ModelMetadata | null> {
    const model = this.models.get(modelId);
    if (!model) {
      return null;
    }
    
    // Check if the model file exists
    const exists = await FileSystem.exists(model.path);
    if (!exists) {
      // If the file doesn't exist, try to download it
      try {
        await this.downloadModel(model);
        return this.models.get(modelId) || null;
      } catch (error) {
        console.error(`Failed to get model ${modelId}:`, error);
        return null;
      }
    }
    
    return model;
  }

  /**
   * Get all available models
   */
  public getAllModels(): ModelMetadata[] {
    return Array.from(this.models.values());
  }

  /**
   * Request an optimized model from the server
   */
  public async requestOptimizedModel(
    modelId: string,
    options: OptimizationOptions
  ): Promise<ModelMetadata | null> {
    try {
      // Request an optimized model from the server
      const response = await fetch(`${API_URL}/api/mobile/models/optimize/${modelId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceInfo: {
            platform: Platform.OS,
            version: Platform.Version,
            model: Platform.OS === 'ios' ? 'iPhone' : 'Android',
          },
          options,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to request optimized model: ${response.statusText}`);
      }
      
      const optimizedModel: ModelMetadata = await response.json();
      
      // Download the optimized model
      await this.downloadModel(optimizedModel);
      
      return this.models.get(optimizedModel.id) || null;
    } catch (error) {
      console.error(`Failed to request optimized model ${modelId}:`, error);
      return null;
    }
  }

  /**
   * Delete a model
   */
  public async deleteModel(modelId: string): Promise<boolean> {
    try {
      const model = this.models.get(modelId);
      if (!model) {
        return false;
      }
      
      // Delete the model file
      await FileSystem.unlink(model.path);
      
      // Remove the model from the metadata
      this.models.delete(modelId);
      
      // Save the updated metadata
      await this.saveModelMetadata();
      
      return true;
    } catch (error) {
      console.error(`Failed to delete model ${modelId}:`, error);
      return false;
    }
  }

  /**
   * Clear all models
   */
  public async clearAllModels(): Promise<boolean> {
    try {
      // Delete all model files
      for (const model of this.models.values()) {
        try {
          await FileSystem.unlink(model.path);
        } catch (error) {
          console.warn(`Failed to delete model file ${model.path}:`, error);
        }
      }
      
      // Clear the model metadata
      this.models.clear();
      
      // Save the updated metadata
      await this.saveModelMetadata();
      
      return true;
    } catch (error) {
      console.error('Failed to clear all models:', error);
      return false;
    }
  }
}

// Export the singleton instance
export const modelOptimizer = ModelOptimizer.getInstance();
