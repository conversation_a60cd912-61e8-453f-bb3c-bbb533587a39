/**
 * Intelligent notification service for mobile devices.
 * Uses AI to prioritize, time, and personalize notifications.
 */

import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'react-native-fs';
import { modelOptimizer, ModelType } from './ModelOptimizer';
import { NotificationService } from '../notification.service';
import { API_URL, STORAGE_KEY } from '../../config';
import { UserPreferenceService } from '../user/preferences.service';

// Notification priority levels
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// Notification category types
export enum NotificationCategory {
  RETREAT_UPDATE = 'retreat_update',
  PARTNER_REQUEST = 'partner_request',
  PAYMENT = 'payment',
  PROMOTION = 'promotion',
  SYSTEM = 'system',
  WELLNESS_TIP = 'wellness_tip',
  TRAVEL_ALERT = 'travel_alert'
}

// Notification data interface
export interface NotificationData {
  id: string;
  title: string;
  body: string;
  category: NotificationCategory;
  priority: NotificationPriority;
  timestamp: string;
  data?: Record<string, any>;
  read: boolean;
  actionTaken: boolean;
}

// User behavior data for notification timing
export interface UserBehaviorData {
  activeHours: {
    [hour: number]: number; // Hour (0-23) -> activity score (0-1)
  };
  categoryInteractions: {
    [category: string]: {
      openRate: number;
      responseTime: number;
      dismissRate: number;
    };
  };
  lastNotificationTime: string;
  notificationResponseTimes: number[]; // in seconds
}

/**
 * Service for intelligent notification management
 */
export class IntelligentNotificationService {
  private static instance: IntelligentNotificationService;
  private notificationHistory: NotificationData[] = [];
  private userBehavior: UserBehaviorData | null = null;
  private modelLoaded: boolean = false;
  private prioritizationModelId: string = 'notification_prioritization';
  private timingModelId: string = 'notification_timing';

  /**
   * Get the singleton instance of the IntelligentNotificationService
   */
  public static getInstance(): IntelligentNotificationService {
    if (!IntelligentNotificationService.instance) {
      IntelligentNotificationService.instance = new IntelligentNotificationService();
    }
    return IntelligentNotificationService.instance;
  }

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    // Initialize the service
  }

  /**
   * Initialize the intelligent notification service
   */
  public async initialize(): Promise<void> {
    try {
      // Load notification history
      await this.loadNotificationHistory();
      
      // Load user behavior data
      await this.loadUserBehaviorData();
      
      // Load AI models
      await this.loadModels();
      
      console.log('Intelligent notification service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize intelligent notification service:', error);
      // Continue with basic functionality
    }
  }

  /**
   * Load notification history from local storage
   */
  private async loadNotificationHistory(): Promise<void> {
    try {
      const historyJson = await AsyncStorage.getItem(`${STORAGE_KEY}notification_history`);
      if (historyJson) {
        this.notificationHistory = JSON.parse(historyJson);
      }
    } catch (error) {
      console.error('Failed to load notification history:', error);
      // Continue with empty history
    }
  }

  /**
   * Save notification history to local storage
   */
  private async saveNotificationHistory(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        `${STORAGE_KEY}notification_history`,
        JSON.stringify(this.notificationHistory)
      );
    } catch (error) {
      console.error('Failed to save notification history:', error);
    }
  }

  /**
   * Load user behavior data from local storage
   */
  private async loadUserBehaviorData(): Promise<void> {
    try {
      const behaviorJson = await AsyncStorage.getItem(`${STORAGE_KEY}user_behavior`);
      if (behaviorJson) {
        this.userBehavior = JSON.parse(behaviorJson);
      } else {
        // Initialize with default values
        this.userBehavior = {
          activeHours: {},
          categoryInteractions: {},
          lastNotificationTime: new Date().toISOString(),
          notificationResponseTimes: []
        };
        
        // Initialize active hours (default to working hours)
        for (let i = 0; i < 24; i++) {
          this.userBehavior.activeHours[i] = i >= 8 && i <= 22 ? 0.5 : 0.1;
        }
      }
    } catch (error) {
      console.error('Failed to load user behavior data:', error);
      // Initialize with default values
      this.userBehavior = {
        activeHours: {},
        categoryInteractions: {},
        lastNotificationTime: new Date().toISOString(),
        notificationResponseTimes: []
      };
    }
  }

  /**
   * Save user behavior data to local storage
   */
  private async saveUserBehaviorData(): Promise<void> {
    try {
      if (this.userBehavior) {
        await AsyncStorage.setItem(
          `${STORAGE_KEY}user_behavior`,
          JSON.stringify(this.userBehavior)
        );
      }
    } catch (error) {
      console.error('Failed to save user behavior data:', error);
    }
  }

  /**
   * Load AI models for notification intelligence
   */
  private async loadModels(): Promise<void> {
    try {
      // Load the prioritization model
      const prioritizationModel = await modelOptimizer.getModel(this.prioritizationModelId);
      
      // Load the timing model
      const timingModel = await modelOptimizer.getModel(this.timingModelId);
      
      // Set model loaded flag
      this.modelLoaded = !!(prioritizationModel && timingModel);
      
      if (!this.modelLoaded) {
        console.warn('Notification intelligence models not available, using fallback logic');
      }
    } catch (error) {
      console.error('Failed to load notification intelligence models:', error);
      this.modelLoaded = false;
    }
  }

  /**
   * Process a new notification
   * @param notification The notification to process
   * @returns Whether the notification should be shown immediately
   */
  public async processNotification(notification: NotificationData): Promise<boolean> {
    try {
      // Add to history
      this.notificationHistory.push({
        ...notification,
        read: false,
        actionTaken: false
      });
      
      // Save updated history
      await this.saveNotificationHistory();
      
      // Determine priority
      const priority = await this.determinePriority(notification);
      
      // Determine if the notification should be shown immediately
      const showImmediately = await this.shouldShowImmediately(notification, priority);
      
      if (showImmediately) {
        // Show the notification immediately
        await this.showNotification(notification);
        return true;
      } else {
        // Schedule the notification for later
        await this.scheduleNotification(notification, priority);
        return false;
      }
    } catch (error) {
      console.error('Failed to process notification:', error);
      
      // Fallback: show the notification immediately
      await this.showNotification(notification);
      return true;
    }
  }

  /**
   * Determine the priority of a notification
   */
  private async determinePriority(notification: NotificationData): Promise<NotificationPriority> {
    try {
      if (this.modelLoaded) {
        // Use AI model to determine priority
        // This would involve running inference on the loaded model
        // For now, we'll use a simple rule-based approach
      }
      
      // Fallback logic based on category
      switch (notification.category) {
        case NotificationCategory.PAYMENT:
        case NotificationCategory.TRAVEL_ALERT:
          return NotificationPriority.HIGH;
          
        case NotificationCategory.PARTNER_REQUEST:
        case NotificationCategory.RETREAT_UPDATE:
          return NotificationPriority.MEDIUM;
          
        case NotificationCategory.PROMOTION:
        case NotificationCategory.WELLNESS_TIP:
          return NotificationPriority.LOW;
          
        case NotificationCategory.SYSTEM:
        default:
          return NotificationPriority.MEDIUM;
      }
    } catch (error) {
      console.error('Failed to determine notification priority:', error);
      return notification.priority || NotificationPriority.MEDIUM;
    }
  }

  /**
   * Determine if a notification should be shown immediately
   */
  private async shouldShowImmediately(
    notification: NotificationData,
    priority: NotificationPriority
  ): Promise<boolean> {
    try {
      // High and urgent priority notifications should always be shown immediately
      if (priority === NotificationPriority.HIGH || priority === NotificationPriority.URGENT) {
        return true;
      }
      
      if (this.modelLoaded && this.userBehavior) {
        // Use AI model to determine timing
        // This would involve running inference on the loaded model
        
        // For now, use a simple rule-based approach based on user behavior
        const currentHour = new Date().getHours();
        const activityScore = this.userBehavior.activeHours[currentHour] || 0.5;
        
        // If the user is likely to be active, show the notification
        if (activityScore > 0.7) {
          return true;
        }
        
        // Check category interaction data
        const categoryData = this.userBehavior.categoryInteractions[notification.category];
        if (categoryData && categoryData.openRate > 0.7) {
          // User frequently engages with this category
          return true;
        }
      }
      
      // Default behavior based on priority
      return priority === NotificationPriority.MEDIUM;
    } catch (error) {
      console.error('Failed to determine if notification should be shown immediately:', error);
      
      // Fallback: show medium and high priority immediately
      return priority === NotificationPriority.MEDIUM || 
             priority === NotificationPriority.HIGH || 
             priority === NotificationPriority.URGENT;
    }
  }

  /**
   * Show a notification immediately
   */
  private async showNotification(notification: NotificationData): Promise<void> {
    try {
      // Use the notification service to show the notification
      await NotificationService.showNotification({
        id: notification.id,
        title: notification.title,
        body: notification.body,
        data: notification.data || {}
      });
      
      // Update the last notification time
      if (this.userBehavior) {
        this.userBehavior.lastNotificationTime = new Date().toISOString();
        await this.saveUserBehaviorData();
      }
    } catch (error) {
      console.error('Failed to show notification:', error);
      throw error;
    }
  }

  /**
   * Schedule a notification for later
   */
  private async scheduleNotification(
    notification: NotificationData,
    priority: NotificationPriority
  ): Promise<void> {
    try {
      // Determine the best time to show the notification
      const scheduledTime = await this.determineBestTime(notification, priority);
      
      // Schedule the notification
      await NotificationService.scheduleNotification({
        id: notification.id,
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        date: scheduledTime
      });
    } catch (error) {
      console.error('Failed to schedule notification:', error);
      
      // Fallback: show the notification immediately
      await this.showNotification(notification);
    }
  }

  /**
   * Determine the best time to show a notification
   */
  private async determineBestTime(
    notification: NotificationData,
    priority: NotificationPriority
  ): Promise<Date> {
    try {
      if (this.modelLoaded && this.userBehavior) {
        // Use AI model to determine the best time
        // This would involve running inference on the loaded model
        
        // For now, use a simple rule-based approach
        const now = new Date();
        const currentHour = now.getHours();
        
        // Find the next hour with high activity
        let bestHour = currentHour;
        let bestScore = this.userBehavior.activeHours[currentHour] || 0;
        
        for (let i = 1; i <= 8; i++) {
          const hour = (currentHour + i) % 24;
          const score = this.userBehavior.activeHours[hour] || 0;
          
          if (score > bestScore) {
            bestHour = hour;
            bestScore = score;
          }
        }
        
        // Create a date for the best hour
        const bestTime = new Date(now);
        if (bestHour <= currentHour) {
          // If the best hour is tomorrow
          bestTime.setDate(bestTime.getDate() + 1);
        }
        bestTime.setHours(bestHour, 0, 0, 0);
        
        return bestTime;
      }
      
      // Fallback: schedule for a reasonable time based on priority
      const now = new Date();
      const scheduledTime = new Date(now);
      
      switch (priority) {
        case NotificationPriority.MEDIUM:
          // Schedule for 1 hour later
          scheduledTime.setHours(scheduledTime.getHours() + 1);
          break;
          
        case NotificationPriority.LOW:
          // Schedule for the next day at 9 AM
          scheduledTime.setDate(scheduledTime.getDate() + 1);
          scheduledTime.setHours(9, 0, 0, 0);
          break;
          
        default:
          // Schedule for 30 minutes later
          scheduledTime.setMinutes(scheduledTime.getMinutes() + 30);
          break;
      }
      
      return scheduledTime;
    } catch (error) {
      console.error('Failed to determine best notification time:', error);
      
      // Fallback: schedule for 1 hour later
      const scheduledTime = new Date();
      scheduledTime.setHours(scheduledTime.getHours() + 1);
      return scheduledTime;
    }
  }

  /**
   * Mark a notification as read
   */
  public async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const index = this.notificationHistory.findIndex(n => n.id === notificationId);
      if (index === -1) {
        return false;
      }
      
      // Mark as read
      this.notificationHistory[index].read = true;
      
      // Save updated history
      await this.saveNotificationHistory();
      
      // Update user behavior data
      await this.updateUserBehaviorOnRead(this.notificationHistory[index]);
      
      return true;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      return false;
    }
  }

  /**
   * Mark a notification as having action taken
   */
  public async markActionTaken(notificationId: string): Promise<boolean> {
    try {
      const index = this.notificationHistory.findIndex(n => n.id === notificationId);
      if (index === -1) {
        return false;
      }
      
      // Mark action taken
      this.notificationHistory[index].actionTaken = true;
      
      // Save updated history
      await this.saveNotificationHistory();
      
      // Update user behavior data
      await this.updateUserBehaviorOnAction(this.notificationHistory[index]);
      
      return true;
    } catch (error) {
      console.error('Failed to mark notification action taken:', error);
      return false;
    }
  }

  /**
   * Update user behavior data when a notification is read
   */
  private async updateUserBehaviorOnRead(notification: NotificationData): Promise<void> {
    try {
      if (!this.userBehavior) {
        return;
      }
      
      // Update category interaction data
      if (!this.userBehavior.categoryInteractions[notification.category]) {
        this.userBehavior.categoryInteractions[notification.category] = {
          openRate: 0,
          responseTime: 0,
          dismissRate: 0
        };
      }
      
      const categoryData = this.userBehavior.categoryInteractions[notification.category];
      
      // Calculate response time
      const notificationTime = new Date(notification.timestamp);
      const now = new Date();
      const responseTimeSeconds = (now.getTime() - notificationTime.getTime()) / 1000;
      
      // Update response time (moving average)
      if (categoryData.responseTime === 0) {
        categoryData.responseTime = responseTimeSeconds;
      } else {
        categoryData.responseTime = (categoryData.responseTime * 0.8) + (responseTimeSeconds * 0.2);
      }
      
      // Add to response times array
      this.userBehavior.notificationResponseTimes.push(responseTimeSeconds);
      
      // Keep only the last 50 response times
      if (this.userBehavior.notificationResponseTimes.length > 50) {
        this.userBehavior.notificationResponseTimes.shift();
      }
      
      // Update active hours
      const hour = now.getHours();
      if (!this.userBehavior.activeHours[hour]) {
        this.userBehavior.activeHours[hour] = 0.5;
      }
      this.userBehavior.activeHours[hour] = Math.min(
        1,
        this.userBehavior.activeHours[hour] + 0.1
      );
      
      // Save updated behavior data
      await this.saveUserBehaviorData();
    } catch (error) {
      console.error('Failed to update user behavior on notification read:', error);
    }
  }

  /**
   * Update user behavior data when action is taken on a notification
   */
  private async updateUserBehaviorOnAction(notification: NotificationData): Promise<void> {
    try {
      if (!this.userBehavior) {
        return;
      }
      
      // Update category interaction data
      if (!this.userBehavior.categoryInteractions[notification.category]) {
        this.userBehavior.categoryInteractions[notification.category] = {
          openRate: 0,
          responseTime: 0,
          dismissRate: 0
        };
      }
      
      const categoryData = this.userBehavior.categoryInteractions[notification.category];
      
      // Update open rate (moving average)
      categoryData.openRate = (categoryData.openRate * 0.8) + 0.2;
      
      // Update active hours more significantly
      const hour = new Date().getHours();
      if (!this.userBehavior.activeHours[hour]) {
        this.userBehavior.activeHours[hour] = 0.5;
      }
      this.userBehavior.activeHours[hour] = Math.min(
        1,
        this.userBehavior.activeHours[hour] + 0.2
      );
      
      // Save updated behavior data
      await this.saveUserBehaviorData();
    } catch (error) {
      console.error('Failed to update user behavior on notification action:', error);
    }
  }

  /**
   * Get all notifications
   */
  public getNotifications(): NotificationData[] {
    return [...this.notificationHistory].sort(
      (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }

  /**
   * Get unread notifications
   */
  public getUnreadNotifications(): NotificationData[] {
    return this.notificationHistory
      .filter(n => !n.read)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  /**
   * Clear all notifications
   */
  public async clearAllNotifications(): Promise<boolean> {
    try {
      this.notificationHistory = [];
      await this.saveNotificationHistory();
      return true;
    } catch (error) {
      console.error('Failed to clear notifications:', error);
      return false;
    }
  }

  /**
   * Delete a notification
   */
  public async deleteNotification(notificationId: string): Promise<boolean> {
    try {
      const index = this.notificationHistory.findIndex(n => n.id === notificationId);
      if (index === -1) {
        return false;
      }
      
      // Remove the notification
      this.notificationHistory.splice(index, 1);
      
      // Save updated history
      await this.saveNotificationHistory();
      
      return true;
    } catch (error) {
      console.error('Failed to delete notification:', error);
      return false;
    }
  }
}

// Export the singleton instance
export const intelligentNotification = IntelligentNotificationService.getInstance();
