import messaging from '@react-native-firebase/messaging'
import PushNotification from 'react-native-push-notification'
import { api } from './api'

export class NotificationService {
  static async initialize() {
    await this.requestPermission()
    this.configurePushNotifications()
    this.handleFCMToken()
    this.listenToNotifications()
  }

  static async requestPermission() {
    try {
      const authStatus = await messaging().requestPermission()
      return authStatus === messaging.AuthorizationStatus.AUTHORIZED
    } catch(error) {
      console.error('Failed to get push notification permission:', error);
      return false;
    }
  }

  private static configurePushNotifications() {
    PushNotification.configure({
      onNotification: function(notification) {
        console.log('NOTIFICATION:', notification)
      },
      permissions: {
        alert: true,
        badge: true,
        sound: true
      },
      popInitialNotification: true
    })
  }

  private static async handleFCMToken() {
    const token = await messaging().getToken()
    if(token) {
      await this.updateFCMToken(token);
    }

    messaging().onTokenRefresh(async (newToken) => {
      await this.updateFCMToken(newToken);
    })
  }

  private static async updateFCMToken(token: string) {
    try {
      await api.post('/users/fcm-token', { token })
    } catch(error) {
      console.error('Failed to update FCM token:', error);
    }
  }

  private static listenToNotifications() {
    messaging().onMessage(async (remoteMessage) => {
      PushNotification.localNotification({
        title: remoteMessage.notification?.title,
        message: remoteMessage.notification?.body || '',
        data: remoteMessage.data
      })
    })
  }
}