import { AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { syncDatabase } from '../database/sync';
import { database } from '../database';
import { SYNC_EVENTS } from './sync.service';
import { EventEmitter } from '../utils/eventEmitter';

// Clés pour AsyncStorage
const PERIODIC_SYNC_KEYS = {
  LAST_SYNC_TIMESTAMP: 'periodicSync:lastSyncTimestamp',
  SYNC_INTERVAL: 'periodicSync:interval',
};

// Intervalle par défaut (5 minutes en millisecondes)
const DEFAULT_SYNC_INTERVAL = 5 * 60 * 1000;

class PeriodicSyncService {
  private syncInterval: number = DEFAULT_SYNC_INTERVAL;
  private syncTimer: NodeJS.Timeout | null = null;
  private isAppActive: boolean = true;
  private isSyncInProgress: boolean = false;

  constructor() {
    // Initialiser l'intervalle de synchronisation depuis AsyncStorage
    this.initSyncInterval();
    
    // Écouter les changements d'état de l'application
    AppState.addEventListener('change', this.handleAppStateChange);
    
    // Écouter les changements de connectivité
    NetInfo.addEventListener(this.handleConnectivityChange);
  }

  /**
   * Initialise l'intervalle de synchronisation depuis AsyncStorage
   */
  private async initSyncInterval(): Promise<void> {
    try {
      const storedInterval = await AsyncStorage.getItem(PERIODIC_SYNC_KEYS.SYNC_INTERVAL);
      if (storedInterval) {
        this.syncInterval = parseInt(storedInterval, 10);
      } else {
        // Si aucun intervalle n'est stocké, utiliser la valeur par défaut
        await AsyncStorage.setItem(
          PERIODIC_SYNC_KEYS.SYNC_INTERVAL,
          DEFAULT_SYNC_INTERVAL.toString()
        );
      }
    } catch (error) {
      console.error('Erreur lors de l\'initialisation de l\'intervalle de synchronisation:', error);
    }
  }

  /**
   * Démarre la synchronisation périodique
   */
  public startPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(async () => {
      if (this.isAppActive && !this.isSyncInProgress) {
        await this.performSync();
      }
    }, this.syncInterval);

    console.log(`Synchronisation périodique démarrée (intervalle: ${this.syncInterval / 1000}s)`);
  }

  /**
   * Arrête la synchronisation périodique
   */
  public stopPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
      console.log('Synchronisation périodique arrêtée');
    }
  }

  /**
   * Modifie l'intervalle de synchronisation
   * @param interval Nouvel intervalle en millisecondes
   */
  public async setSyncInterval(interval: number): Promise<void> {
    try {
      this.syncInterval = interval;
      await AsyncStorage.setItem(PERIODIC_SYNC_KEYS.SYNC_INTERVAL, interval.toString());
      
      // Redémarrer la synchronisation avec le nouvel intervalle
      if (this.syncTimer) {
        this.stopPeriodicSync();
        this.startPeriodicSync();
      }
      
      console.log(`Intervalle de synchronisation mis à jour: ${interval / 1000}s`);
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'intervalle de synchronisation:', error);
    }
  }

  /**
   * Effectue une synchronisation manuelle
   */
  public async performManualSync(): Promise<boolean> {
    if (this.isSyncInProgress) {
      console.log('Une synchronisation est déjà en cours');
      return false;
    }
    
    return await this.performSync();
  }

  /**
   * Effectue la synchronisation
   */
  private async performSync(): Promise<boolean> {
    this.isSyncInProgress = true;
    EventEmitter.emit(SYNC_EVENTS.SYNC_STARTED);
    
    try {
      // Vérifier la connectivité
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected) {
        console.log('Pas de connexion Internet, synchronisation reportée');
        EventEmitter.emit(SYNC_EVENTS.SYNC_FAILED, { reason: 'no_connection' });
        this.isSyncInProgress = false;
        return false;
      }
      
      // Effectuer la synchronisation
      await syncDatabase(database);
      
      // Mettre à jour le timestamp de dernière synchronisation
      const timestamp = Date.now();
      await AsyncStorage.setItem(PERIODIC_SYNC_KEYS.LAST_SYNC_TIMESTAMP, timestamp.toString());
      
      console.log(`Synchronisation réussie à ${new Date(timestamp).toLocaleString()}`);
      EventEmitter.emit(SYNC_EVENTS.SYNC_COMPLETED, { timestamp });
      
      this.isSyncInProgress = false;
      return true;
    } catch (error) {
      console.error('Erreur lors de la synchronisation:', error);
      EventEmitter.emit(SYNC_EVENTS.SYNC_FAILED, { error });
      
      this.isSyncInProgress = false;
      return false;
    }
  }

  /**
   * Gère les changements d'état de l'application
   */
  private handleAppStateChange = (nextAppState: AppStateStatus): void => {
    this.isAppActive = nextAppState === 'active';
    
    if (this.isAppActive) {
      // L'application est revenue au premier plan, vérifier si une synchronisation est nécessaire
      this.checkSyncNeeded();
    }
  };

  /**
   * Gère les changements de connectivité
   */
  private handleConnectivityChange = (state: any): void => {
    if (state.isConnected && this.isAppActive) {
      // La connexion a été rétablie, vérifier si une synchronisation est nécessaire
      this.checkSyncNeeded();
    }
  };

  /**
   * Vérifie si une synchronisation est nécessaire en fonction du dernier timestamp
   */
  private async checkSyncNeeded(): Promise<void> {
    try {
      const lastSyncTimestamp = await AsyncStorage.getItem(PERIODIC_SYNC_KEYS.LAST_SYNC_TIMESTAMP);
      
      if (!lastSyncTimestamp) {
        // Première synchronisation
        await this.performSync();
        return;
      }
      
      const lastSync = parseInt(lastSyncTimestamp, 10);
      const now = Date.now();
      const timeSinceLastSync = now - lastSync;
      
      if (timeSinceLastSync >= this.syncInterval) {
        await this.performSync();
      }
    } catch (error) {
      console.error('Erreur lors de la vérification de la nécessité de synchronisation:', error);
    }
  }

  /**
   * Obtient la date de la dernière synchronisation
   */
  public async getLastSyncTimestamp(): Promise<number | null> {
    try {
      const timestamp = await AsyncStorage.getItem(PERIODIC_SYNC_KEYS.LAST_SYNC_TIMESTAMP);
      return timestamp ? parseInt(timestamp, 10) : null;
    } catch (error) {
      console.error('Erreur lors de la récupération du timestamp de dernière synchronisation:', error);
      return null;
    }
  }
}

// Exporter une instance singleton
export const periodicSyncService = new PeriodicSyncService();
