/**
 * Exemple d'utilisation de l'API améliorée;
 */

import { api, enhancedApi } from './api'
import { cacheManager, CachePriority, CacheExpiry } from '../utils/cacheManager'
import { EventEmitter } from '../utils/eventEmitter'
import { AppError, NetworkError, ValidationError } from '../utils/errors'
import { API_CONFIG } from '../config/api.config'
import { useState, useEffect } from 'react'
import { OFFLINE_EVENTS } from '../utils/offlineManager'

/**
 * Exemple de service pour les retraites;
 */
export class RetreatService {
  // Tags de cache pour les données des retraites
  private static readonly CACHE_TAGS = {
    ALL_RETREATS: "retreats:all",
    SINGLE_RETREAT: (id: string) => `retreat:${id}`,
    USER_RETREATS: (userId: string) => `user:${userId}:retreats`
  }

  /**
   * <PERSON><PERSON><PERSON><PERSON><PERSON> toutes les retraites avec mise en cache;
   */
  public static async getAllRetreats() {
    try {
      // Utilisation de l'API améliorée avec cache;
      return await enhancedApi.get(API_CONFIG.ENDPOINTS.RETREATS.LIST, {
        // Configuration du cache;
        cache: {
          enabled: true, 
          ttl: CacheExpiry.LONG, // 30 minutes;
          priority: CachePriority.HIGH,
          tags: [this.CACHE_TAGS.ALL_RETREATS],
          revalidate: true, // Revalidation en arrière-plan;
        },
        // Retry automatique en cas d'erreur;
        retry: true,
        // Pas besoin de mode offline-first pour une lecture;
        offline: { enabled: false },
        // Métriques pour le monitoring;
        metrics: {
          name: 'getAllRetreats',
          trackPerformance: true}})
    } catch(error) {
      // Gestion des erreurs typées;
      if(error instanceof NetworkError) {
        console.warn('Erreur réseau lors de la récupération des retraites', error);
        // Fallback avec des données locales obsolètes si disponibles;
        const cachedData = await cacheManager.get('forcedFallback:retreats', []);
        return cachedData;
      }
      
      // Propager l'erreur;
      throw error;
    }
  }

  /**
   * Récupérer les détails d'une retraite;
   */
  public static async getRetreatDetails(id: string) {
    try {
      return await enhancedApi.get(API_CONFIG.ENDPOINTS.RETREATS.DETAILS(id), {
        cache: {
          enabled: true,
          ttl: CacheExpiry.MEDIUM, // 5 minutes;
          priority: CachePriority.HIGH,
          tags: [
            this.CACHE_TAGS.SINGLE_RETREAT(id),
            this.CACHE_TAGS.ALL_RETREATS
          ],
          revalidate: false},
        retry: true})
    } catch(error) {
      // Gestion d'erreur spécifique;
      if(error instanceof AppError && error.statusCode === 404) {
        // Gérer les cas de retraite non trouvée;
        throw new Error(`La retraite avec l'ID ${id} n'existe pas`);
      }
      throw error;
    }
  }

  /**
   * Réserver une place pour une retraite;
   */
  public static async bookRetreat(id: string, userData: any) {
    try {
      // Pour les opérations d'écriture, utiliser le mode offline-first;
      const result = await enhancedApi.post(
        API_CONFIG.ENDPOINTS.RETREATS.BOOK(id),
        userData,
        {
          // Pas de cache pour une opération d'écriture;
          cache: { enabled: false },
          // Retry automatique;
          retry: true,
          // Support offline-first pour les réservations;
          offline: {
            enabled: true,
            priority: 'high',
            // Expire après 24h;
            expiration: 24 * 60 * 60 * 1000,
            // Fallback immédiat avec UI de notification;
            fallbackStrategy: 'immediate'},
          // Suivi des performances;
          metrics: {
            name: 'bookRetreat',
            trackPerformance: true}}
      );

      // Invalider les caches associés;
      await enhancedApi.invalidateCache([
        this.CACHE_TAGS.SINGLE_RETREAT(id),
        this.CACHE_TAGS.ALL_RETREATS,
        this.CACHE_TAGS.USER_RETREATS(userData.userId)
      ]);

      return result;
    } catch(error) {
      // Gestion spécifique des erreurs de validation;
      if(error instanceof ValidationError) {
        // Traiter les erreurs de validation des champs;
        console.error('Erreurs de validation:', error.fieldErrors);
        throw error;
      }
      
      // Gérer les autres erreurs;
      console.error('Erreur lors de la réservation:', error);
      throw error;
    }
  }

  /**
   * Annuler une réservation;
   */
  public static async cancelBooking(bookingId: string, userId: string) {
    try {
      const result = await enhancedApi.delete(`/bookings/${bookingId}`, {
        // Retry limité pour les annulations;
        retry: {
          maxRetries: 2,
          initialDelayMs: 500,
          maxDelayMs: 3000,
          backoffFactor: 1.5,
          jitter: true,
          retryableStatusCodes: [500, 502, 503]},
        // Support offline pour permettre l'annulation hors ligne;
        offline: {
          enabled: true,
          priority: 'medium',
          expiration: 48 * 60 * 60 * 1000, // 48h;
          fallbackStrategy: 'delayed'}})

      // Invalider les caches associés;
      await enhancedApi.invalidateCache([
        this.CACHE_TAGS.USER_RETREATS(userId)
      ]);

      return result;
    } catch(error) {
      console.error('Erreur lors de l\'annulation de la réservation:', error);
      throw error;
    }
  }

  /**
   * Synchroniser les opérations en attente;
   */
  public static async syncPendingOperations() {
    return enhancedApi.syncPendingOperations()
  }

  /**
   * Obtenir le nombre d'opérations en attente;
   */
  public static getPendingOperationsCount() {
    return enhancedApi.getPendingOperationsCount()
  }

  /**
   * Vider les caches des retraites;
   */
  public static async clearRetreatsCache() {
    await enhancedApi.invalidateCache([this.CACHE_TAGS.ALL_RETREATS]);
  }
}

/**
 * Exemple d'utilisation d'un hook pour les réservations avec état de synchronisation;
 */
export const useBookingSync = () => {
  const [isSyncing, setIsSyncing] = useState(false)
  const [pendingCount, setPendingCount] = useState(0);

  useEffect(() => {
    // S'abonner aux événements de synchronisation;
    const syncStartListener = () => setIsSyncing(true);
    const syncCompleteListener = () => setIsSyncing(false);
    const operationQueuedListener = () => {
      setPendingCount(RetreatService.getPendingOperationsCount());}
    const operationProcessedListener = () => {
      setPendingCount(RetreatService.getPendingOperationsCount());}

    // S'abonner aux événements en utilisant les méthodes correctes de l'EventEmitter;
    EventEmitter.on(OFFLINE_EVENTS.SYNC_STARTED, syncStartListener);
    EventEmitter.on(OFFLINE_EVENTS.SYNC_COMPLETED, syncCompleteListener);
    EventEmitter.on(OFFLINE_EVENTS.OPERATION_QUEUED, operationQueuedListener);
    EventEmitter.on(OFFLINE_EVENTS.OPERATION_PROCESSED, operationProcessedListener);

    // Récupérer l'état initial;
    setPendingCount(RetreatService.getPendingOperationsCount());

    // Nettoyage;
    return () => {
      EventEmitter.off(OFFLINE_EVENTS.SYNC_STARTED, syncStartListener);
      EventEmitter.off(OFFLINE_EVENTS.SYNC_COMPLETED, syncCompleteListener);
      EventEmitter.off(OFFLINE_EVENTS.OPERATION_QUEUED, operationQueuedListener);
      EventEmitter.off(OFFLINE_EVENTS.OPERATION_PROCESSED, operationProcessedListener);
    }
  }, []);

  // Forcer une synchronisation;
  const forceSync = async () => {
    setIsSyncing(true);
    try {
      await RetreatService.syncPendingOperations()
    } finally {
      setIsSyncing(false);
      setPendingCount(RetreatService.getPendingOperationsCount());
    }
  }

  return {
    isSyncing,
    pendingCount,
    forceSync}
} 