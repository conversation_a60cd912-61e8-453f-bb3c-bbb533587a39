import AsyncStorage from '@react-native-async-storage/async-storage'
import { api } from './api'

export class AuthService {
  static TOKEN_KEY = '@RetreatAndBe:token'
  static REFRESH_TOKEN_KEY = '@RetreatAndBe:refreshToken';

  static async login(email: string, password: string) {
    try {
      const response = await api.post('/auth/login', { email, password })
      await this.saveTokens(response.data.token, response.data.refreshToken);
      return response.data;
    } catch(error) {
      throw this.handleError(error);
    }
  }

  static async logout() {
    try {
      await api.post('/auth/logout');
      await this.clearTokens()
    } catch(error) {
      await this.clearTokens()
      throw this.handleError(error);
    }
  }

  static async refreshToken() {
    try {
      const refreshToken = await AsyncStorage.getItem(this.REFRESH_TOKEN_KEY);
      const response = await api.post('/auth/refresh', { refreshToken })
      await this.saveTokens(response.data.token, response.data.refreshToken);
      return response.data.token;
    } catch(error) {
      await this.clearTokens()
      throw this.handleError(error);
    }
  }

  private static async saveTokens(token: string, refreshToken: string) {
    await AsyncStorage.setItem(this.TOKEN_KEY, token);
    await AsyncStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  private static async clearTokens() {
    await AsyncStorage.multiRemove([this.TOKEN_KEY, this.REFRESH_TOKEN_KEY]);
  }

  private static handleError(error: any) {
    return error.response?.data?.message || 'Une erreur est survenue';
  }
}