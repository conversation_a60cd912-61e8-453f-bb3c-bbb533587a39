import { Database } from '@nozbe/watermelondb'
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite'
import { schemas } from './storage.service'

// Import des modèles mis à jour
import { User } from '../models/User'
import { Retreat } from '../models/Retreat'
import { Booking } from '../models/Booking'
import Notification from '../models/Notification'
import StoragePreference from '../models/StoragePreference'
import IPFSNode from '../models/IPFSNode'

// Étendre l'interface Database pour supporter le driver
declare module '@nozbe/watermelondb' {
  interface Database {
    driver: {
      getSchema(): Promise<any>;
    };
  }
}

// Configuration
// Commenté car non utilisé
// const adapterConfig = {
//   schema: schemas
// }

// Adapter
const adapter = new SQLiteAdapter({
  schema: schemas,
  dbName: 'retraites', // nom de la base
  onSetUpError: error => {
    console.error('Erreur lors de la configuration de la base de données', error)
  }
})

// Database
export const database = new Database({
  adapter,
  modelClasses: [
    User,
    Retreat,
    Booking,
    Notification,
    StoragePreference,
    IPFSNode
  ]
})

export class DatabaseService {
  static async initialize() {
    try {
      await database.action(async () => {
        // Vérification de la connexion
        const tables = await database.driver.getSchema()
        console.log('Database initialized with tables:', tables);
      })
    } catch(error) {
      console.error('Database initialization error:', error);
      throw error;
    }
  }

  static async resetDatabase() {
    if(__DEV__) {
      await database.write(async () => {
        // À remplir
      })
    }
  }
}