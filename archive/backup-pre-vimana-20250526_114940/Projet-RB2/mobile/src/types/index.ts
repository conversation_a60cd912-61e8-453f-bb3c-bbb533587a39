export * from './user';
export * from './monitoring';

// Définitions de type supplémentaires pour le projet
export type ErrorWithMessage = {
  message: string;
  [key: string]: any;
};

// Fonctions utilitaires pour le typage
export function hasMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as any).message === 'string'
  );
}

// Fonction pour extraire le message d'erreur de manière sécurisée
export function getErrorMessage(error: unknown): string {
  if (hasMessage(error)) {
    return error.message;
  }
  
  return String(error);
} 