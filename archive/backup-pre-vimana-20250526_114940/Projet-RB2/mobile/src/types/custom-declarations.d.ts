// Fix for TypeScript property decorators in WatermelonDB
declare module '@nozbe/watermelondb/decorators' {
  import { ColumnName } from '@nozbe/watermelondb';
  
  export function field(columnName: ColumnName): PropertyDecorator;
  export function date(columnName: ColumnName): PropertyDecorator;
  export function readonly(target: any, key: string | symbol): void;
  export function relation(tableName: string, columnName: ColumnName): PropertyDecorator;
  export function table(tableName: string): ClassDecorator;
  export function children(tableName: string, foreignKey: ColumnName): PropertyDecorator;
  export function lazy(): PropertyDecorator;
  export function text(columnName: ColumnName): PropertyDecorator;
  export function nochange(): PropertyDecorator;
  export function json(columnName: ColumnName, propName?: string): PropertyDecorator;
  export function action(): MethodDecorator;
} 