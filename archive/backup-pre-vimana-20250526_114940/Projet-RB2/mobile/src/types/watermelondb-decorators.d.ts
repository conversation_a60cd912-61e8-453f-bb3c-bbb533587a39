import { Model, ColumnName, ColumnType } from '@nozbe/watermelondb';

declare module '@nozbe/watermelondb/decorators' {
  /**
   * Décorateur pour définir une colonne de champ texte ou numérique
   */
  export function field(columnName: string): PropertyDecorator;

  /**
   * Décorateur pour définir une colonne de champ de date
   */
  export function date(columnName: string): PropertyDecorator;

  /**
   * Décorateur pour définir un champ en lecture seule
   */
  export function readonly(target: any, key: string): void;

  /**
   * Décorateur pour définir une relation enfants
   */
  export function children(relationInfo: string): PropertyDecorator;

  /**
   * Décorateur pour définir une relation parent
   */
  export function relation(relationInfo: string, relationName?: string): PropertyDecorator;

  /**
   * Décorateur pour définir une action
   */
  export function action<T extends Function>(target: any, key: string, descriptor: TypedPropertyDescriptor<T>): TypedPropertyDescriptor<T>;

  /**
   * Décorateur pour définir une table
   */
  export function table(tableName: string): ClassDecorator;

  /**
   * Décorateur pour définir un nom à afficher
   */
  export function writer<T extends Function>(target: any, key: string, descriptor: TypedPropertyDescriptor<T>): TypedPropertyDescriptor<T>;

  /**
   * Décorateur pour définir une méthode de lancement d'erreur
   */
  export function nochange(target: any, key: string): void;

  /**
   * Décorateur pour définir une colonne de texte avec indexation
   */
  export function text(columnName: string): PropertyDecorator;

  /**
   * Décorateur pour définir une colonne numérique avec indexation
   */
  export function number(columnName: string): PropertyDecorator;

  /**
   * Décorateur pour définir une colonne booléenne
   */
  export function boolean(columnName: string): PropertyDecorator;

  /**
   * Décorateur pour définir une colonne json
   */
  export function json(columnName: string): PropertyDecorator;

  /**
   * Décorateur d'immutabilité
   */
  export function immutableRelation(relationInfo: string, relationName?: string): PropertyDecorator;
}

declare module '@nozbe/watermelondb/Schema' {
  export interface ColumnSchema {
    name: string;
    type: ColumnType;
    isOptional?: boolean;
    isIndexed?: boolean;
  }

  export interface ColumnMap {
    [name: string]: ColumnSchema;
  }
} 