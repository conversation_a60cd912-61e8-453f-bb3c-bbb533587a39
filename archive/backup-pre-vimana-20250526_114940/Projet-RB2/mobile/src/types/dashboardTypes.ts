/**
 * Types pour les tableaux de bord personnalisables;
 */

/**
 * Configuration d'une métrique;
 */
export interface MetricConfig  {
  id: string;
  name: string;
  displayName?: string;
  description?: string;
  category?: string;
  visible: boolean;
  order: number;
  format?: string;
  unit?: string;
  chartType?: 'line' | 'bar' | 'pie';
  colorThreshold?: {
    warning?: number;
    critical?: number;
  }
}

/**
 * Disposition du tableau de bord;
 */
export interface DashboardLayout  {
  columns: number;
  rowHeight: number;
  groupByCategory: boolean
}

/**
 * Filtres du tableau de bord;
 */
export interface DashboardFilters  {
  categories?: string[];
  timeRange?: {
    start: number;
    end: number
  }
  search?: string;
}

/**
 * Configuration d'une vue du tableau de bord;
 */
export interface DashboardViewConfig  {
  id: string;
  name: string;
  metrics: MetricConfig[];
  layout: DashboardLayout;
  filters?: DashboardFilters;
  refreshInterval?: number;
}

/**
 * Configuration complète du tableau de bord;
 */
export interface DashboardConfig  {
  id: string;
  name: string;
  description?: string;
  views: DashboardViewConfig[];
  defaultView: string;
  createdAt?: number;
  updatedAt?: number;
}

/**
 * Préférences utilisateur pour les tableaux de bord;
 */
export interface DashboardPreferences  {
  lastUsedDashboard?: string;
  defaultDashboard?: string;
  colorTheme: 'light' | 'dark' | 'system';
  chartAnimations: boolean;
  autoRefresh: boolean
} 