// Type définitions pour éviter les erreurs de décorateurs WatermelonDB

// Fonction utilitaire pour créer des décorateurs qui ne font rien
const createNoOpDecorator = (name: string) => {
  return (...args: any[]): any => {
    return (target: any, key?: string | symbol, descriptor?: PropertyDescriptor): any => {
      return descriptor || target;
    };
  };
};

// Exports des décorateurs 
export const field = createNoOpDecorator('field');
export const date = createNoOpDecorator('date');
export const readonly = createNoOpDecorator('readonly');
export const relation = createNoOpDecorator('relation');
export const table = createNoOpDecorator('table');
export const children = createNoOpDecorator('children');
export const lazy = createNoOpDecorator('lazy');
export const text = createNoOpDecorator('text');
export const json = createNoOpDecorator('json');
export const action = createNoOpDecorator('action');
export const nochange = createNoOpDecorator('nochange'); 