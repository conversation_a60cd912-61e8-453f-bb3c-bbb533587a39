// Déclarations pour les modules core
declare module '@projet-rb2/core' {
  export const featureFlagManager: {
    getAllFlags(): Record<string, any>;
    isFeatureEnabled(key: string): boolean;
    setFeatureEnabled(key: string, value: boolean): void;
    clearFeatureOverrides(): void;
  };
  export function isFeatureEnabled(key: string): () => boolean;
}

/**
 * Déclarations pour @react-navigation/native-stack
 */
declare module '@react-navigation/native-stack' {
  import { ComponentType } from 'react';
  import { StyleProp, TextStyle, ViewStyle } from 'react-native';
  import {
    DefaultNavigatorOptions,
    ParamListBase,
    RouteConfig,
    StackNavigationState,
    NavigationProp,
    RouteProp
  } from '@react-navigation/native';

  export type NativeStackNavigationEventMap = {
    /**
     * Event which fires when a screen appears
     */
    appear: { data: undefined };
    /**
     * Event which fires when a screen disappears
     */
    dismiss: { data: undefined };
    /**
     * Event which fires when the transition animation starts
     */
    transitionStart: { data: { closing: boolean } };
    /**
     * Event which fires when the transition animation ends
     */
    transitionEnd: { data: { closing: boolean } };
  };

  export type NativeStackNavigationProp<
    ParamList extends ParamListBase,
    RouteName extends keyof ParamList = string
  > = NavigationProp<
    ParamList,
    RouteName,
    StackNavigationState<ParamList>,
    NativeStackNavigationOptions,
    NativeStackNavigationEventMap
  >;

  export type NativeStackScreenProps<
    ParamList extends ParamListBase,
    RouteName extends keyof ParamList = string
  > = {
    navigation: NativeStackNavigationProp<ParamList, RouteName>;
    route: RouteProp<ParamList, RouteName>;
  };

  export type NativeStackNavigationOptions = {
    title?: string;
    headerStyle?: StyleProp<ViewStyle>;
    headerTitleStyle?: StyleProp<TextStyle>;
    headerTintColor?: string;
    headerShown?: boolean;
    headerTransparent?: boolean;
    headerBackTitle?: string;
    headerBackVisible?: boolean;
    headerRight?: () => React.ReactNode;
    headerLeft?: () => React.ReactNode;
    headerTitle?: string | ((props: { children: string }) => React.ReactNode);
    headerTitleAlign?: 'left' | 'center';
    animation?: 'default' | 'fade' | 'slide_from_right' | 'slide_from_left' | 'none';
    statusBarStyle?: 'auto' | 'inverted' | 'light' | 'dark';
    statusBarColor?: string;
    statusBarAnimation?: 'fade' | 'none' | 'slide';
    statusBarHidden?: boolean;
    presentation?: 'card' | 'modal' | 'transparentModal';
    contentStyle?: StyleProp<ViewStyle>;
    gestureEnabled?: boolean;
    animationTypeForReplace?: 'push' | 'pop';
  };

  export type NativeStackNavigatorProps = DefaultNavigatorOptions<
    ParamListBase,
    StackNavigationState<ParamListBase>,
    NativeStackNavigationOptions,
    NativeStackNavigationEventMap
  > & {
    screenOptions?: NativeStackNavigationOptions;
  };

  export const createNativeStackNavigator: () => {
    Navigator: ComponentType<NativeStackNavigatorProps>;
    Screen: ComponentType<RouteConfig<
      ParamListBase,
      string,
      StackNavigationState<ParamListBase>,
      NativeStackNavigationOptions,
      NativeStackNavigationEventMap
    >>;
  };
}

// Déclarations pour les modèles
declare module '../models/schema' {
  import { Schema } from '@nozbe/watermelondb';
  export const schema: Schema;
}

declare module '../models/migrations' {
  import { SchemaMigrations } from '@nozbe/watermelondb/Schema/migrations';
  export const migrations: SchemaMigrations;
}

// Déclarations pour le client API
declare module '../config/api' {
  export const apiClient: {
    get(url: string, config?: any): Promise<any>;
    post(url: string, data?: any, config?: any): Promise<any>;
    put(url: string, data?: any, config?: any): Promise<any>;
    delete(url: string, config?: any): Promise<any>;
  };
  export const API_URL: string;
}

// Déclarations pour les erreurs
declare module './errors' {
  export class NetworkError extends Error {
    incrementRetry(): void;
  }
  export class OfflineError extends Error {}
  export class TimeoutError extends Error {}
  export class ServerError extends Error {}
}

// Types pour EmptyState
interface EmptyStateProps {
  icon: string;
  title: string;
  description: string;
  actionLabel: string;
  onAction: () => void;
  useDarkMode?: boolean;
}

// Types pour FilterModal
interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  filterOptions: { id: string; label: string; options: { id: string; label: string; icon: string; }[] }[];
  initialFilters: Record<string, string[]>;
  onApply?: (filters: Record<string, string[]>) => void;
}

// Ajouter des propriétés manquantes aux modèles existants
declare module '../models' {
  export interface User {
    phone?: string;
    token?: string;
    avatar?: string;
  }
  
  export class StoragePreference {}
  export class IPFSNode {}
} 