import { Model } from '@nozbe/watermelondb';

// États possibles du processus de synchronisation
export type SyncStatus = 
  | 'idle'         // Aucune synchronisation en cours
  | 'checking'     // Vérification de la connectivité et des changements
  | 'pulling'      // Récupération des données du serveur
  | 'pushing'      // Envoi des données au serveur
  | 'conflict'     // Résolution des conflits en cours/nécessaire
  | 'error'        // Erreur de synchronisation
  | 'complete';    // Synchronisation terminée avec succès

// Un conflit individuel entre données locales et distantes
export interface SyncConflict<T extends Model> {
  id: string;              // Identifiant unique du conflit
  tableName: string;       // Nom de la table concernée
  clientRecord: T;         // Enregistrement local (WatermelonDB)
  serverData: Record<string, any>; // Données du serveur (JSON brut)
  timestamp: number;       // Date de détection du conflit
  resolved?: boolean;      // Indique si le conflit a été résolu
}

// Conflit résolu
export interface ResolvedConflict {
  id: string;              // Identifiant du conflit résolu
  tableName: string;       // Nom de la table concernée
  recordId: string;        // ID de l'enregistrement
  timestamp: number;       // Date de résolution
  strategy: string;        // Stratégie utilisée pour résoudre le conflit
}

// Configuration de synchronisation pour une entité
export interface EntitySyncConfig {
  enabled: boolean;        // Synchronisation activée pour cette entité
  pullEnabled: boolean;    // Récupération des données activée
  pushEnabled: boolean;    // Envoi des données activé
  priority: number;        // Priorité de synchronisation (plus petit = plus prioritaire)
  conflictStrategy: string; // Stratégie par défaut pour résoudre les conflits
}

// Configuration de synchronisation pour un modèle
export interface ModelSyncConfig {
  endpoint?: string;
  pull?: boolean;
  push?: boolean;
}

// Configuration globale de synchronisation
export interface SyncConfig {
  autoSync: boolean;           // Synchronisation automatique activée
  autoSyncInterval: number;    // Intervalle de synchronisation automatique (minutes)
  backgroundSync: boolean;     // Synchronisation en arrière-plan activée
  onlyWifi: boolean;           // Synchroniser uniquement sur WiFi
  compressionEnabled: boolean; // Compression des données activée
  maxConcurrentOperations: number; // Nombre maximum d'opérations simultanées
  entities: Record<string, EntitySyncConfig>; // Configuration par entité
  models: Record<string, ModelSyncConfig>;
}

// État de synchronisation actuel
export interface SyncState {
  status: SyncStatus;
  progress: number;          // Progression globale (0-100)
  lastSync: number | null;   // Timestamp de la dernière synchronisation
  currentEntity?: string;    // Entité en cours de synchronisation
  error?: string;            // Message d'erreur éventuel
  conflicts: SyncConflict<Model>[]; // Conflits non résolus
  resolvedConflicts: ResolvedConflict[]; // Conflits résolus
  pendingOperations: number; // Nombre d'opérations en attente
}

// Événements de synchronisation
export enum SyncEvent {
  SYNC_STARTED = 'sync:started',
  SYNC_PROGRESS = 'sync:progress',
  SYNC_COMPLETE = 'sync:complete',
  SYNC_ERROR = 'sync:error',
  CONFLICT_DETECTED = 'sync:conflict_detected',
  CONFLICT_RESOLVED = 'sync:conflict_resolved',
  CONNECTION_CHANGE = 'sync:connection_change',
}

// Données de progression de la synchronisation
export interface SyncProgressData {
  status: SyncStatus;
  progress: number;
  entity?: string;
  total?: number;
  processed?: number;
}

// Statistiques de synchronisation
export interface SyncStats {
  pulledItems: number;      // Éléments récupérés
  pushedItems: number;      // Éléments envoyés
  conflictsDetected: number; // Conflits détectés
  conflictsResolved: number; // Conflits résolus
  syncDuration: number;     // Durée de la synchronisation (ms)
  compressionRatio?: number; // Taux de compression (si activé)
  byEntity: Record<string, {
    pulled: number;
    pushed: number;
    conflicts: number;
  }>;
}

// Options de synchronisation
export interface SyncOptions {
  retryDelayMs?: number;
  maxRetries?: number;
  timeoutMs?: number;
  syncOnReconnect?: boolean;
}

// Ensemble de changements pour la synchronisation
export interface ChangeSet {
  [modelName: string]: {
    created?: any[];
    updated?: any[];
    deleted?: string[];
  };
}

// Stratégie de résolution de conflits
export enum ConflictResolution {
  SERVER_WINS = 'server_wins',
  CLIENT_WINS = 'client_wins',
  MANUAL = 'manual'
} 