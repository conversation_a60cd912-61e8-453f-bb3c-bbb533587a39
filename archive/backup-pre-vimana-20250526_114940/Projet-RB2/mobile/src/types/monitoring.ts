export interface UnifiedMonitoring {
  startSpan(name: string, options?: any): any;
  endSpan(name: string, success?: boolean, attributes?: Record<string, any>): void;
  startOperation(name: string, options?: any): any;
  endOperation(name: string, success?: boolean, attributes?: Record<string, any>): void;
  recordMetric(name: string, value: number, options?: Record<string, any>): void;
  recordError(error: Error, context?: Record<string, any>): void;
  setGlobalAttribute(key: string, value: any): void;
  clearGlobalAttributes(): void;
  flushMetrics(): Promise<boolean>;
  shutdown(): void;
} 