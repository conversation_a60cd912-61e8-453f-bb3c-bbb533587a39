import { Model } from '@nozbe/watermelondb';

// Stub pour éviter les erreurs de décorateurs pendant la compilation
export class Booking extends Model {
  retreatId!: string;
  userId!: string;
  status!: string;
  createdAt!: Date;
  updatedAt!: Date;
}

export enum NotificationType {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  SUCCESS = 'success'
}

export class Notification extends Model {
  type!: NotificationType;
  title!: string;
  message!: string;
  data!: any;
  userId!: string;
  read!: boolean;
  createdAt!: Date;
  updatedAt!: Date;
}

export class Retreat extends Model {
  name!: string;
  description!: string;
  location!: string;
  startDate!: Date;
  endDate!: Date;
  capacity!: number;
  price!: number;
  imageUrl?: string;
  status!: string;
  createdAt!: Date;
  updatedAt!: Date;
  bookings: any[] = [];
}

export class User extends Model {
  email!: string;
  firstName!: string;
  lastName!: string;
  avatarUrl?: string;
  phone?: string;
  role!: string;
  createdAt!: Date;
  updatedAt!: Date;
  bookings: any[] = [];
} 