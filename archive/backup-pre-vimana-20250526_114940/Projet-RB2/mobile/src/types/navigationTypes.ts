/**
 * Type definitions for navigation stacks;
 */

// Main application navigation stack;
export type MainStackParamList =  {
  // Home tabs;
  Home: undefined;
  Bookings: undefined;
  Dashboard: undefined;
  Notifications: undefined;
  Profile: undefined;
  Settings: undefined;
  
  // Home stack;
  HomeMain: undefined;
  
  // Bookings stack;
  BookingsMain: undefined;
  BookingConfirmation: {
    retreatId: string;
    startDate: string;
    endDate: string;
    guests: number}
  BookingSuccess: {
    bookingId: string
  }
  
  // Dashboard stack;
  DashboardMain: undefined;
  
  // Profile stack;
  ProfileMain: undefined;
  
  // Notifications stack;
  NotificationsMain: undefined;
  
  // Details screens;
  RetreatDetail: {
    retreatId: string
  }
  Search: {
    initialQuery?: string;
  }
}

// Authentication stack;
export type AuthStackParamList =  {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined}

// Root navigator;
export type RootStackParamList =  {
  Auth: undefined;
  Main: undefined;
  Loading: undefined} 