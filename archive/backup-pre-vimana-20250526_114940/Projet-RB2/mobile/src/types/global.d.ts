// Déclarations des modules manquants
declare module '@projet-rb2/core' {
  export const featureFlagManager: any;
  export const isFeatureEnabled: any;
  export const getVersion: () => string;
  export const getFormattedVersion: () => string;
  export const versionManager: any;
}

declare module '@react-navigation/native-stack' {
  export const createNativeStackNavigator: any;
  export type NativeStackNavigationProp<T, K extends keyof T> = any;
}

declare module '@react-navigation/bottom-tabs' {
  export const createBottomTabNavigator: any;
}

declare module 'pako' {
  export function deflate(data: any, options?: any): Uint8Array;
  export function inflate(data: any, options?: any): Uint8Array;
  export default { deflate, inflate };
}

declare module 'react-native-push-notification' {
  const PushNotification: any;
  export default PushNotification;
}

declare module '@react-native-firebase/messaging' {
  const messaging: any;
  export default messaging;
}

// Déclarations globales
declare const APP_VERSION: string;
declare const Platform: { OS: string };

// Fonctions globales de navigation
declare const createStackNavigator: any;

// Déclarations de types pour les modèles
declare module '../models/schema' {
  export const schema: any;
}

declare module '../models/migrations' {
  export const migrations: any;
}

// Déclarations pour les erreurs
declare module './errors' {
  export class NetworkError extends Error {
    incrementRetry(): void;
  }
  export class OfflineError extends Error {}
  export class TimeoutError extends Error {}
  export class ServerError extends Error {}
}

// Pour éviter les erreurs de duplication
declare interface unifiedMonitoring {
  recordMetric(name: string, value: number, options?: any): void;
  flushMetrics(): Promise<boolean>;
  shutdown(): void;
} 