import { ViewStyle } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';

// Étendre le type des boutons pour accepter les styles composés
declare module '../components/ui/Button' {
  interface ButtonProps {
    style?: ViewStyle | ViewStyle[];
    variant?: 'primary' | 'secondary' | 'outline' | 'text' | 'outlined';
  }
}

// Paramètres pour les écrans
interface MainStackParamList {
  Home: undefined;
  Login: undefined;
  Profile: undefined;
  BookingDetails: { bookingId: string };
  BookingConfirmation: { retreatId: string };
  BookingSuccess: { 
    bookingId: string;
    retreatTitle?: string;
    startDate?: string;
    endDate?: string;
  };
  BackupScreen: undefined;
  SyncQueueView: undefined;
  ConflictResolution: undefined;
  SyncSettingsScreen: undefined;
  RetreatDetail: { retreatId: string };
  FilterModal: undefined;
  BackupRestore: undefined;
  Main: undefined;
}

// Étendre le type NativeStackNavigationProp
declare module '@react-navigation/native-stack' {
  export type NativeStackNavigationProp<
    T extends Record<string, object | undefined> = MainStackParamList,
    K extends keyof T = keyof T
  > = StackNavigationProp<T, K>;
}

// Corriger EmptyState
declare module '../components/ui/EmptyState' {
  interface EmptyStateProps {
    icon: string;
    title: string;
    description: string;
    actionLabel: string;
    onAction: () => void;
    useDarkMode?: boolean;
    isDarkMode?: boolean; // Pour compatibilité
  }
}

// Corriger FilterModal
declare module '../components/ui/FilterModal' {
  interface FilterModalProps {
    visible: boolean;
    onClose: () => void;
    filterOptions: { id: string; label: string; options: { id: string; label: string; icon: string; }[] }[];
    initialFilters: Record<string, string[]>;
    onApply: (filters: Record<string, string[]>) => void;
  }
}

// Corriger RetreatCard
declare module '../components/ui/RetreatCard' {
  interface RetreatCardProps {
    title: string;
    location: string;
    imageUrl: string;
    startDate: string;
    endDate: string;
    price: string;
    rating?: number;
    reviewCount?: number;
    isFavorite?: boolean;
    onPress?: () => void;
    onFavoritePress?: () => void;
    style?: ViewStyle;
    imageStyle?: ImageStyle;
    id?: string; // Propriété optionnelle pour compatibilité
  }
} 