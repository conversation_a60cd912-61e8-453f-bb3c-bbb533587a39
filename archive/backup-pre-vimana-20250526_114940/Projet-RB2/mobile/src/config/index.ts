// Environnements disponibles
export type Environment = 'development' | 'staging' | 'production';

// Environnement courant
export const ENV: Environment = 
  (__DEV__ ? 'development' : 'production') as Environment;

// URL de base de l'API selon l'environnement
const API_URLS = {
  development: 'http://localhost:3000',
  staging: 'https://staging-api.rb2.app',
  production: 'https://api.rb2.app',
};

// Configuration des URL
export const API_BASE_URL = API_URLS[ENV];

// Configuration de synchronisation par défaut
export const DEFAULT_SYNC_CONFIG = {
  autoSync: true,
  autoSyncInterval: 15, // minutes
  backgroundSync: true,
  onlyWifi: false,
  compressionEnabled: true,
  maxConcurrentOperations: 3,
  entities: {
    retreats: {
      enabled: true,
      pullEnabled: true,
      pushEnabled: true,
      priority: 1,
      conflictStrategy: 'merge'
    },
    bookings: {
      enabled: true,
      pullEnabled: true,
      pushEnabled: true,
      priority: 2,
      conflictStrategy: 'merge'
    },
    users: {
      enabled: true,
      pullEnabled: true,
      pushEnabled: false, // Les utilisateurs ne sont généralement modifiés que côté serveur
      priority: 3,
      conflictStrategy: 'server_wins'
    },
    notifications: {
      enabled: true,
      pullEnabled: true,
      pushEnabled: true,
      priority: 4,
      conflictStrategy: 'server_wins'
    }
  }
};

// Configuration des sauvegardes
export const BACKUP_CONFIG = {
  autoBackup: true,
  autoBackupInterval: 24 * 60, // 24 heures en minutes
  maxBackups: 5, // Nombre maximum de sauvegardes à conserver
  backupPath: 'RB2/backups',
};

// Autres constantes de l'application
export const APP_CONFIG = {
  version: '1.0.0',
  buildNumber: '1',
  supportEmail: '<EMAIL>',
  privacyPolicyUrl: 'https://rb2.app/privacy',
  termsOfServiceUrl: 'https://rb2.app/terms',
  maxRetries: 3, // Nombre maximum de tentatives pour les opérations réseau
  defaultTimeout: 30000, // Timeout par défaut pour les requêtes API (30 secondes)
  minSyncInterval: 5, // Intervalle minimum entre synchronisations (minutes)
  defaultLanguage: 'fr',
  dateFormat: 'dd/MM/yyyy',
  timeFormat: 'HH:mm',
}; 