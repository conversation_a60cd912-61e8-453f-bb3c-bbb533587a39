/**
 * Configuration API pour l'application
 */

export const API_URL = 'https://api.retreat-and-be.com/v1';

export const API_CONFIG = {
  // Timeouts
  REQUEST_TIMEOUT: 10000,
  REFRESH_TOKEN_TIMEOUT: 5000,
  
  // Retry
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MS: 1000,
  MAX_RETRY_DELAY_MS: 5000,
  
  // Cache
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  
  // Headers
  DEFAULT_HEADERS: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },
  
  // Endpoints
  ENDPOINTS: {
    AUTH: {
      LOGIN: '/auth/login',
      REGISTER: '/auth/register',
      REFRESH: '/auth/refresh'
    },
    RETREATS: {
      LIST: '/retreats',
      DETAILS: (id: string): string => `/retreats/${id}`,
      BOOK: (id: string): string => `/retreats/${id}/book`
    },
    USER: {
      PROFILE: '/users/profile',
      PREFERENCES: '/users/preferences',
      BOOKINGS: '/users/bookings',
      NOTIFICATIONS: '/users/notifications'
    }
  },
  
  // Error messages
  ERROR_MESSAGES: {
    NETWORK: 'Network connection error',
    AUTH: 'Authentication error',
    TIMEOUT: 'Request timeout'
  }
}; 