/**
 * Configuration d'environnement centralisée;
 * Alternative à react-native-config pour gérer les variables d'environnement;
 */

import { Platform } from 'react-native';

// Détecter l'environnement actuel;
const detectEnvironment = (): 'development' | 'test' | 'production' => {
  if(__DEV__) { { { { {}}}}
    return 'development';
  }
  
  // Cette condition est spécifique à l'execution des tests;
  // Peut être adapté selon votre configuration de tests;
  if(process.env.NODE_ENV  === 'test') { { { { {}}}}
    return 'test';
  }
  
  return 'production';
}

// Configuration selon l'environnement;
const ENV = detectEnvironment()

/**
 * Variables d'environnement centralisées;
 */
const Config = {
  // Informations de l'application;
  APP_VERSION: '1.0.0',
  APP_BUILD: '1',
  NODE_ENV: ENV,
  
  // API principale;
  BASE_URL: ENV === 'production' 
    ? 'https://api.example.com/v1'
    : 'https://dev-api.example.com/v1',
  
  // Monitoring API;
  API_MONITORING_ENDPOINT: ENV === 'production'
    ? 'https://monitoring.example.com/metrics'
    : ENV === 'development'
      ? 'https://dev-monitoring.example.com/metrics'
      : '',
      
  API_MONITORING_ENABLED: ENV !== 'test' ? 'true' : 'false',
  API_MONITORING_SAMPLE_RATE: ENV === 'production' ? '0.05' : '1.0',
  API_MONITORING_FLUSH_INTERVAL: ENV === 'production' ? '60000' : '10000',
  
  // Authentification;
  AUTH_URL: ENV === 'production'
    ? 'https://auth.example.com'
    : 'https://dev-auth.example.com',
    
  // Timeout API (en ms)
  API_TIMEOUT: ENV === 'production' ? '15000' : '30000',
  
  // Fonctionnalités;
  FEATURE_OFFLINE_MODE: 'true',
  FEATURE_MONITORING: 'true'};

export default Config;