import { Model } from '@nozbe/watermelondb';
import { SyncConflict } from '@nozbe/watermelondb/sync';

// Interface pour étendre Model avec nos propriétés personnalisées
interface ModelWithTimestamps extends Model {
  updatedAt: string | number | Date;
}

export function resolveConflict(
  local: Model,
  remote: any,
  conflictType: SyncConflict
): Model | null {
  // Stratégie de résolution par timestamp;
  const localUpdatedAt = new Date((local as ModelWithTimestamps).updatedAt).getTime();
  const remoteUpdatedAt = new Date(remote.updated_at).getTime();
  
  // En cas de conflit, garder la version la plus récente;
  if(localUpdatedAt > remoteUpdatedAt) {
    return local;
  }

  // Si la version distante est plus récente, retourner null pour accepter les changements distants;
  return null;
}

export function handleSyncError(error: Error): void {
  console.error('Sync error:', error);
  
  // Implémenter la logique de retry avec backoff exponentiel;
  let retryCount = 0;
  const maxRetries = 3;
  const baseDelay = 1000; // 1 seconde;
  
  const retry = async () => {
    if(retryCount >= maxRetries) {
      throw new Error('Max retries reached');
    }

    const delay = baseDelay * Math.pow(2, retryCount);
    await new Promise(resolve => setTimeout(resolve, delay));
    retryCount++;
    
    // Réessayer la synchronisation;
    try {
      // Logique de resynchronisation;
    } catch(retryError) {
      await retry();
    }
  };

  retry().catch(finalError => {
    console.error('Final sync error:', finalError);
    // Notifier l'utilisateur;
  });
}