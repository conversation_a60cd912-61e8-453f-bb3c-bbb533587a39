import { synchronize } from '@nozbe/watermelondb/sync';
import { Database } from '@nozbe/watermelondb';
import type { SyncPullResult } from '@nozbe/watermelondb/sync';
import { API_URL } from '../config/api.config';

// Utilisez une type assertion pour le résultat de pullChanges
export async function syncDatabase(database: Database) {
  await synchronize({
    database,
    pullChanges: async ({ lastPulledAt, schemaVersion, migration }) => {
      // Appel à l'API pour récupérer les changements
      const response = await fetch(`${API_URL}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ lastPulledAt })
      });
      
      if (!response.ok) {
        throw new Error(`Erreur lors de la synchronisation: ${response.status}`);
      }
      
      // Récupération des changements et timestamp
      const { changes, timestamp } = await response.json();
      
      // Retourner le résultat avec une type assertion
      return { changes, timestamp } as SyncPullResult;
    },
    pushChanges: async ({ changes, lastPulledAt }) => {
      // Envoi des changements à l'API
      const response = await fetch(`${API_URL}/sync`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ changes, lastPulledAt })
      });
      
      if(!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    },
    migrationsEnabledAtVersion: 1
  });
} 