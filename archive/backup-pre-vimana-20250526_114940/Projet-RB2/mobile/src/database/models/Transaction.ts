import { Model } from '@nozbe/watermelondb';
import { field, date } from '@nozbe/watermelondb/decorators';

export default class Transaction extends Model {
  static table = 'transactions';

  @field('hash') hash!: string;
  @field('from_address') fromAddress!: string;
  @field('to_address') toAddress!: string;
  @field('value') value!: string;
  @field('status') status!: string;
  @date('last_sync_at') lastSyncAt!: Date;
  @date('created_at') createdAt!: Date;
  @date('updated_at') updatedAt!: Date;
} 