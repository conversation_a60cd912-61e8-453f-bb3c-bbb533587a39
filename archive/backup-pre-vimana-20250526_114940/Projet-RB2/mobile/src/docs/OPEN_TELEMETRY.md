# Documentation OpenTelemetry pour Applications Mobiles

## Introduction

Ce document décrit l'implémentation d'OpenTelemetry dans notre application mobile React Native. OpenTelemetry est un framework standardisé pour la télémétrie, permettant de collecter des traces, des métriques et des logs de manière cohérente à travers différents services et applications.

## Table des matières

1. [Architecture OpenTelemetry](#architecture-opentelemetry)
2. [Installation et Configuration](#installation-et-configuration)
3. [Types de données collectées](#types-de-données-collectées)
4. [Utilisation dans les composants](#utilisation-dans-les-composants)
5. [Utilisation dans les services API](#utilisation-dans-les-services-api)
6. [Performance et optimisations](#performance-et-optimisations)
7. [Intégration avec les backends de télémétrie](#intégration-avec-les-backends-de-télémétrie)
8. [Considérations pour les environnements mobiles](#considérations-pour-les-environnements-mobiles)

## Architecture OpenTelemetry

Notre implémentation d'OpenTelemetry s'articule autour des composants suivants :

### Architecture globale

```
┌─────────────────────────────────────────────────┐
│                Application Mobile                │
│                                                 │
│  ┌───────────────┐       ┌───────────────────┐  │
│  │ Composants UI │◄──────┤ Hooks Télémétrie  │  │
│  └───────┬───────┘       └─────────┬─────────┘  │
│          │                         │            │
│          ▼                         ▼            │
│  ┌───────────────┐       ┌───────────────────┐  │
│  │ Services API  │◄──────┤ Module OpenTel.   │  │
│  └───────┬───────┘       └─────────┬─────────┘  │
│          │                         │            │
│          ▼                         ▼            │
│  ┌───────────────┐       ┌───────────────────┐  │
│  │ Cache/Offline │       │ Exporteur OTLP    │  │
│  └───────────────┘       └─────────┬─────────┘  │
│                                    │            │
└────────────────────────────────────┼────────────┘
                                     │
                                     ▼
                         ┌─────────────────────┐
                         │ Backend Télémétrie  │
                         │  (Jaeger, Zipkin,   │
                         │  Prometheus, etc.)  │
                         └─────────────────────┘
```

### Modules principaux

1. **Module OpenTelemetry (`openTelemetry.ts`)** :
   - Implémentation d'un client OpenTelemetry adapté à React Native
   - Gestion des spans, traces et métriques
   - Exportation des données vers un backend

2. **Configuration du monitoring (`setupMonitoring.ts`)** :
   - Configuration de la télémétrie selon l'environnement
   - Intégration avec le système de monitoring API existant

3. **Hooks et utilitaires React** :
   - Hooks pour intégrer facilement la télémétrie dans les composants
   - Utilitaires pour suivre les performances des opérations asynchrones

## Installation et Configuration

### Prérequis

Pour utiliser OpenTelemetry dans votre application, vous devez :

1. Avoir configuré le module `openTelemetry.ts` dans votre projet
2. Avoir intégré le module dans `setupMonitoring.ts`
3. Appeler `monitoring.setup()` au démarrage de l'application

### Configuration personnalisée

La configuration se fait via l'objet `TelemetryConfig` :

```typescript
interface TelemetryConfig {
  enabled: boolean;            // Activer/désactiver la télémétrie
  endpoint: string;            // Point de terminaison OTLP
  serviceName: string;         // Nom du service
  serviceVersion: string;      // Version du service
  environment: string;         // Environnement (dev, prod, test)
  sampleRate: number;          // Taux d'échantillonnage (0-1)
  batchSize: number;           // Taille des lots
  batchIntervalMs: number;     // Intervalle d'envoi en ms
  propagateTraceHeadersCorsUrls: string[]; // URLs autorisées pour propagation
  bufferMaxSize: number;       // Taille maximale du tampon
  debugMode: boolean;          // Mode débogage
}
```

### Initialisation

```typescript
import { monitoring } from './utils/setupMonitoring';

// Dans votre composant App.tsx
useEffect(() => {
  // Initialiser le monitoring au démarrage
  monitoring.setup();
  
  return () => {
    // Nettoyer à la fermeture
    monitoring.teardown();
  };
}, []);
```

## Types de données collectées

### Traces et Spans

Une trace est une collection de spans qui représentent un flux de travail distribué. Chaque span représente une opération unitaire dans ce flux.

```typescript
interface Span {
  traceId: string;       // ID unique de la trace
  spanId: string;        // ID unique du span
  parentSpanId?: string; // ID du span parent (si existant)
  name: string;          // Nom de l'opération
  kind: SpanKind;        // Type de span (serveur, client, etc.)
  startTime: number;     // Heure de début (timestamp)
  endTime?: number;      // Heure de fin (timestamp)
  status: SpanStatus;    // Statut (OK, ERROR, UNSET)
  attributes: Record<string, any>; // Attributs supplémentaires
}
```

### Métriques

Les métriques sont des mesures numériques collectées à intervalles réguliers. Elles peuvent être des compteurs, des jauges ou des histogrammes.

```typescript
interface Metric {
  name: string;         // Nom de la métrique
  description?: string; // Description
  unit?: string;        // Unité de mesure
  value: number;        // Valeur
  timestamp: number;    // Horodatage
  labels: Record<string, string>; // Étiquettes pour segmentation
  type: MetricType;     // Type (COUNTER, GAUGE, HISTOGRAM)
}
```

## Utilisation dans les composants

### Hooks pour les composants React

```typescript
import { openTelemetry, withTelemetry, SpanStatusCode } from '../utils/openTelemetry';
import { useEffect } from 'react';

const MyComponent = () => {
  // Suivre le cycle de vie du composant
  useEffect(() => {
    // Créer un span pour le montage du composant
    const span = openTelemetry.createRootSpan('MyComponent_mount', {
      component: 'MyComponent',
    });
    
    // Enregistrer une métrique
    openTelemetry.recordMetric('component.mount', 1, {
      component: 'MyComponent',
    });
    
    return () => {
      // Terminer le span au démontage
      openTelemetry.endSpan(span);
    };
  }, []);
  
  // Utilisation pour une fonction asynchrone
  const handleComplexOperation = async () => {
    try {
      // Utiliser l'utilitaire withTelemetry
      const result = await withTelemetry(
        'complex_operation',
        async () => {
          // Code de l'opération...
          return { success: true };
        },
        { operation_type: 'user_action' }
      );
      
      return result;
    } catch (error) {
      console.error('Erreur:', error);
    }
  };
  
  return (
    // JSX du composant...
  );
};
```

### Hook personnalisé pour les requêtes API

```typescript
const useTracedFetch = (url: string) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchData = useCallback(async () => {
    // Créer un span
    const span = openTelemetry.createRootSpan('fetch_data', {
      'http.url': url,
    });
    
    setLoading(true);
    
    try {
      const response = await fetch(url);
      const result = await response.json();
      
      // Terminer le span avec succès
      openTelemetry.endSpan(span, SpanStatusCode.OK);
      
      setData(result);
      return result;
    } catch (err) {
      // Terminer le span avec erreur
      openTelemetry.endSpan(span, SpanStatusCode.ERROR, err.message);
      
      setError(err);
      return null;
    } finally {
      setLoading(false);
    }
  }, [url]);

  return { data, loading, error, fetchData };
};
```

## Utilisation dans les services API

### Intégration avec les services API

```typescript
import { openTelemetry } from '../utils/openTelemetry';
import { monitoring } from '../utils/setupMonitoring';

class UserService {
  async getUserProfile(userId: string) {
    return monitoring.trace(
      'getUserProfile',
      async () => {
        // Code pour récupérer le profil utilisateur
        const response = await api.get(`/users/${userId}`);
        return response.data;
      },
      { userId, operation: 'getUserProfile' }
    );
  }
  
  async updateUserSettings(userId: string, settings: any) {
    // Créer un span manuellement
    const span = openTelemetry.createRootSpan('updateUserSettings', {
      userId,
      settingsCount: Object.keys(settings).length,
    });
    
    try {
      const response = await api.put(`/users/${userId}/settings`, settings);
      
      // Ajouter plus d'informations au span
      openTelemetry.addSpanAttributes(span, {
        'response.status': response.status,
        'response.time': Date.now() - span.startTime,
      });
      
      // Terminer le span avec succès
      openTelemetry.endSpan(span);
      
      return response.data;
    } catch (error) {
      // Terminer le span avec erreur
      openTelemetry.endSpan(span, SpanStatusCode.ERROR, error.message);
      throw error;
    }
  }
}
```

## Performance et optimisations

### Échantillonnage intelligent

L'implémentation utilise un mécanisme d'échantillonnage configurable pour réduire la quantité de données collectées et envoyées, tout en conservant une vision représentative du système.

```typescript
// Configuration d'échantillonnage par environnement
if (isProduction) {
  config.sampleRate = 0.05; // 5% en production
} else if (isDevelopment) {
  config.sampleRate = 1.0;  // 100% en développement
}
```

### Mode hors ligne

Les données télémétriques sont stockées localement lorsque l'appareil est hors ligne et envoyées automatiquement lorsque la connexion est rétablie.

```typescript
// Si pas de connexion, stocker pour plus tard
if (!this.isNetworkAvailable) {
  await this.storeDataForLater();
  return false;
}
```

### Traitement par lots

Les données sont envoyées par lots pour réduire l'impact sur les performances et la consommation de batterie.

```typescript
this.batchTimer = setInterval(() => {
  this.exportBatch();
}, this.config.batchIntervalMs);
```

## Intégration avec les backends de télémétrie

### Backends supportés

Notre implémentation d'OpenTelemetry peut envoyer des données aux backends suivants :

- **Jaeger** : Pour la visualisation des traces
- **Zipkin** : Alternative à Jaeger
- **Prometheus** : Pour les métriques
- **Grafana** : Pour visualiser les métriques
- **Elastic APM** : Pour une solution tout-en-un

### Format OTLP

Les données sont exportées au format OTLP (OpenTelemetry Protocol), qui est le format standard d'OpenTelemetry.

```typescript
// Préparer le payload au format OTLP
const payload = {
  resourceSpans: [
    {
      resource: this.resource,
      instrumentationLibrarySpans: [
        {
          instrumentationLibrary: {
            name: 'opentelemetry-react-native',
            version: '1.0.0',
          },
          spans: [...this.spans],
        },
      ],
    },
  ],
  resourceMetrics: [
    // ...
  ],
};
```

## Considérations pour les environnements mobiles

### Économie de batterie

L'implémentation tient compte des contraintes des appareils mobiles :

- Envoi par lots pour réduire les transmissions réseau
- Échantillonnage pour réduire la quantité de données
- Suspension de l'envoi en mode économie d'énergie

### Considérations de vie privée

Notre implémentation respecte la vie privée des utilisateurs :

- Sanitisation des données sensibles (tokens, identifiants, etc.)
- Pas de collecte d'informations personnelles
- Possibilité de désactiver complètement la télémétrie

### Taille de l'implémentation

L'implémentation est légère et optimisée pour les applications mobiles :

- Module autonome sans dépendances lourdes
- Utilisation minimale de la mémoire
- Empreinte réseau réduite

## Conclusion

OpenTelemetry offre une solution standardisée pour la télémétrie dans notre application mobile, permettant une meilleure observabilité, un débogage plus facile et une amélioration continue des performances. Cette implémentation est conçue spécifiquement pour les environnements mobiles, avec une attention particulière à la consommation de batterie, à la performance et à la vie privée.

---

*Dernière mise à jour : 2024-05-20* 