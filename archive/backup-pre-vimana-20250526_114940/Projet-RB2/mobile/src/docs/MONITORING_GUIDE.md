# Guide du Système de Monitoring Unifié

## Introduction

Ce document présente le système de monitoring unifié implémenté dans notre application mobile. Ce système fournit une approche cohérente pour la collecte de métriques de performance, le suivi des erreurs, et l'analytics à travers toutes les plateformes (iOS, Android, Web).

## Table des matières

1. [Architecture du système](#architecture-du-système)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Utilisation dans les composants](#utilisation-dans-les-composants)
5. [Tracking d'erreurs](#tracking-derreurs)
6. [Métriques de performance](#métriques-de-performance)
7. [Analytics](#analytics)
8. [Tableau de bord de performance](#tableau-de-bord-de-performance)
9. [Bonnes pratiques](#bonnes-pratiques)
10. [Dépannage](#dépannage)

## Architecture du système

Notre système de monitoring unifié est composé de plusieurs couches :

```
┌─────────────────────────────────────────────────────────────────┐
│                    Composants d'application                     │
│   ┌─────────────┐    ┌─────────────┐    ┌─────────────────┐     │
│   │ useMonitoring│    │MonitoringContext│    │PerformanceDashboard│     │
│   └─────────────┘    └─────────────┘    └─────────────────┘     │
└─────────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                  API de Monitoring Unifiée                      │
│                    (unifiedMonitoring.ts)                       │
└─────────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌───────────────────────┐  ┌───────────────────────┐
│   API Monitoring      │  │    OpenTelemetry      │
│  (apiMonitoring.ts)   │  │  (openTelemetry.ts)   │
└───────────────────────┘  └───────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Backend de télémétrie                       │
│               (Jaeger, Prometheus, etc.)                        │
└─────────────────────────────────────────────────────────────────┘
```

1. **Couche d'utilisation** : Composants et hooks React pour une intégration facile dans l'application
2. **Couche de monitoring unifiée** : API centrale qui coordonne toutes les fonctionnalités
3. **Implémentations spécifiques** : Modules spécialisés pour API monitoring et OpenTelemetry
4. **Backend** : Services qui reçoivent et analysent les données collectées

## Installation

Pour intégrer le monitoring dans votre application, suivez ces étapes :

### 1. Envelopper votre application avec le MonitoringProvider

```tsx
// App.tsx
import React from 'react';
import { MonitoringProvider } from './contexts/MonitoringContext';
import Config from './config/environment';

const App = () => {
  return (
    <MonitoringProvider 
      appName="MyApp" 
      version={Config.APP_VERSION}
      environment={Config.NODE_ENV}
    >
      {/* Votre application */}
    </MonitoringProvider>
  );
};

export default App;
```

### 2. Utiliser le hook useMonitoring dans vos composants

```tsx
// MonComposant.tsx
import React from 'react';
import { View, Button } from 'react-native';
import useMonitoring from '../hooks/useMonitoring';

const MonComposant = () => {
  const { trackUserAction, measurePerformance } = useMonitoring('MonComposant');
  
  const handlePress = () => {
    trackUserAction('button_pressed');
    
    measurePerformance('process_data', () => {
      // Code à mesurer
      const result = expensiveCalculation();
      return result;
    });
  };
  
  return (
    <View>
      <Button title="Appuyer" onPress={handlePress} />
    </View>
  );
};

export default MonComposant;
```

## Configuration

La configuration du système de monitoring se fait principalement via le fichier d'environnement. Voici les principales options disponibles :

| Option | Description | Valeur par défaut |
|--------|-------------|-------------------|
| `API_MONITORING_ENDPOINT` | Endpoint où envoyer les données de télémétrie | `""` (désactivé) |
| `API_MONITORING_ENABLED` | Activer/désactiver le monitoring | `true` |
| `API_MONITORING_SAMPLE_RATE` | Taux d'échantillonnage (0.0 à 1.0) | `0.1` (10%) |
| `API_MONITORING_FLUSH_INTERVAL` | Intervalle d'envoi des données (ms) | `30000` (30s) |
| `API_MONITORING_DEBUG` | Mode debug pour le monitoring | `false` en production |

## Utilisation dans les composants

### Utilisation du hook useMonitoring

Le hook `useMonitoring` offre une API simplifiée pour instrumenter vos composants React :

```tsx
const { 
  trackUserAction,         // Suivre les actions utilisateur
  measurePerformance,      // Mesurer les performances (synchrone)
  measurePerformanceAsync, // Mesurer les performances (asynchrone)
  startMeasure,            // Démarrer une mesure manuelle
  endMeasure,              // Terminer une mesure manuelle
  log, debug, info, warn, error, // Fonctions de log
  captureError             // Capturer une erreur
} = useMonitoring('NomDuComposant');
```

### Exemples d'utilisation courants

#### Suivi d'une action utilisateur

```tsx
const handleButtonPress = () => {
  trackUserAction('button_pressed', { buttonId: 'submit', screen: 'checkout' });
  // Traitement...
};
```

#### Mesure de performance synchrone

```tsx
const result = measurePerformance('calculate_total', () => {
  return items.reduce((sum, item) => sum + item.price, 0);
});
```

#### Mesure de performance asynchrone

```tsx
const fetchData = async () => {
  const result = await measurePerformanceAsync('fetch_products', async () => {
    const response = await api.get('/products');
    return response.data;
  });
  
  setProducts(result);
};
```

#### Mesure manuelle (pour des opérations plus complexes)

```tsx
const handleComplexOperation = () => {
  const markerId = startMeasure('complex_operation');
  
  // Première étape
  const data = prepareData();
  
  // Deuxième étape
  processData(data);
  
  // Terminer la mesure
  endMeasure(markerId, { dataSize: data.length });
};
```

#### Logging

```tsx
debug('Initialisation du composant', { props });
info('Données chargées', { count: data.length });
warn('Caractéristique utilisée mais bientôt dépréciée');
error('Échec du chargement des données', { error });
```

## Tracking d'erreurs

Le système de monitoring capture automatiquement les erreurs non gérées dans l'application. Pour capturer des erreurs spécifiques :

```tsx
try {
  // Code qui peut échouer
} catch (error) {
  captureError(error, { 
    component: 'MonComposant',
    operation: 'fetchData',
    userId: user.id
  });
  
  // Traitement de l'erreur...
}
```

## Métriques de performance

Les métriques de performance sont collectées automatiquement pour :

- **Requêtes API** : Temps de réponse, taille des données, statut
- **Rendu des composants** : Temps de montage, démontage
- **Actions utilisateur** : Fréquence, distribution
- **Opérations personnalisées** : Définies via les méthodes de mesure

Toutes ces métriques sont accessibles via le tableau de bord de performance.

## Analytics

Le système permet également de suivre des événements pour l'analyse comportementale :

```tsx
// Via le hook
trackUserAction('view_product', { productId: '123', category: 'electronics' });

// Via le contexte
const monitoring = useMonitoringContext();
monitoring.trackEvent('purchase_completed', { 
  orderId: order.id,
  total: order.total,
  items: order.items.length
});
```

## Tableau de bord de performance

Le composant `PerformanceDashboard` fournit une visualisation en temps réel des métriques collectées :

```tsx
import { PerformanceDashboard } from '../components/PerformanceDashboard';

// Dans votre navigation
<Stack.Screen name="PerformanceDashboard" component={PerformanceDashboard} />
```

Ce tableau de bord affiche :
- Métriques HTTP (temps de réponse, taux d'erreur)
- Performances des composants
- Métriques personnalisées
- Analytics

## Bonnes pratiques

### Nommage des métriques et événements

Suivez une convention cohérente pour nommer vos métriques et événements :

- **Actions utilisateur** : `action_[objet]_[verbe]` 
  - Exemple : `button_pressed`, `form_submitted`
- **Opérations** : `[domaine]_[opération]` 
  - Exemple : `data_processing`, `image_upload`

### Attributs contextuels

Ajoutez toujours des attributs contextuels pertinents :

```tsx
trackUserAction('button_pressed', {
  button_id: 'submit',      // Identifiant unique
  screen: 'checkout',       // Contexte
  user_type: user.type,     // Segmentation
  session_duration: sessionTime  // Métrique temporelle
});
```

### Échantillonnage

En production, utilisez l'échantillonnage pour réduire le volume de données :

- **Haute fréquence** : 1-5% d'échantillonnage (`0.01` - `0.05`)
- **Moyenne fréquence** : 10-20% d'échantillonnage (`0.1` - `0.2`)
- **Basse fréquence** : 50-100% d'échantillonnage (`0.5` - `1.0`)

## Dépannage

### Les métriques n'apparaissent pas dans le tableau de bord

1. Vérifiez que le monitoring est activé (`API_MONITORING_ENABLED=true`)
2. Vérifiez que le taux d'échantillonnage n'est pas trop bas
3. Essayez d'appeler manuellement `monitoring.flushMetrics()` pour forcer l'envoi

### Erreurs lors de l'envoi des métriques

1. Vérifiez que l'URL du endpoint est correcte
2. Vérifiez que le réseau est disponible
3. Consultez les logs pour les erreurs spécifiques

### Performance réduite

Si vous constatez un impact sur les performances :

1. Réduisez le taux d'échantillonnage
2. Augmentez l'intervalle d'envoi des données
3. Limitez le nombre de métriques personnalisées

## Conclusion

Le système de monitoring unifié fournit une vision complète des performances et du comportement de votre application sur toutes les plateformes. En utilisant ces outils de manière cohérente à travers votre code, vous pouvez identifier rapidement les problèmes, optimiser les performances et améliorer l'expérience utilisateur. 