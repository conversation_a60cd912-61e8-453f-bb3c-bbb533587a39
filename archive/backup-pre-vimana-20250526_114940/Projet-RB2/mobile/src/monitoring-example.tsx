/**
 * Exemple d'intégration du monitoring dans l'application;
 * 
 * Ce code montre comment intégrer le monitoring API dans une application React Native;
 * Il peut être utilisé comme référence pour l'intégration dans l'application principale;
 */

import React, { useEffect } from 'react';
import { SafeAreaView, StatusBar, View, Text, TouchableOpacity, Button } from 'react-native';
import { monitoring as monitoringInstance } from './utils/setupMonitoring';
import ApiStats from './components/ApiStats';
import ApiDashboardScreen from './screens/ApiDashboardScreen';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// Créer un Stack Navigator pour l'exemple
const Stack = createNativeStackNavigator()

// Exemple d'écran d'accueil avec le composant ApiStats
const HomeScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  return (
    <SafeAreaView style={{ flex: 1, padding: 16 }}>
      <Text style={{ fontSize: 24, marginBottom: 20 }}>Accueil</Text>
      
      {/* Statistiques API en version compacte */}
      <ApiStats compact={true} showControls={false} />
      
      <View style={{ marginTop: 20 }}>
        <Text style={{ fontSize: 18, marginBottom: 10 }}>
          Exemple d'intégration du monitoring API
        </Text>
        
        <Text style={{ marginBottom: 20 }}>
          Le monitoring API est intégré et collecte des données en arrière-plan.
          Vous pouvez voir les statistiques détaillées dans le tableau de bord.
        </Text>
        
        {/* Bouton pour accéder au tableau de bord */}
        <TouchableOpacity
          style={{
            backgroundColor: '#007bff',
            padding: 12,
            borderRadius: 6,
            alignItems: 'center'
          }}
          onPress={() => navigation.navigate('ApiDashboard')}
        >
          <Text style={{ color: '#fff' }}>Voir le tableau de bord</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

// Composant principal de l'application
const App: React.FC = () => {
  // Initialiser le monitoring au démarrage
  useEffect(() => {
    // Configurer et démarrer le monitoring
    monitoringInstance.setup()
    
    // Nettoyer à la fermeture de l'application
    return () => {
      monitoringInstance.teardown()
    }
  }, []);

  return (
    <NavigationContainer>
      <StatusBar barStyle="dark-content" />
      <Stack.Navigator initialRouteName="Home">
        <Stack.Screen name="Home" component={HomeScreen} options={{ title: 'Accueil' }} />
        <Stack.Screen
          name="ApiDashboard" 
          component={ApiDashboardScreen}
          options={{ title: 'Tableau de bord API' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default App;

// Interface pour les statistiques
interface ApiStats {
  avgResponseTime: number;
  isOffline: boolean;
}

// Interface pour les erreurs
interface ApiError {
  message: string;
  type: string;
}

// Interface pour les opérations
interface OperationResult {
  success: boolean;
  duration: number;
  error?: ApiError;
}

// Hook pour le monitoring API
const useApiMonitoring = () => {
  const [stats, setStats] = React.useState<ApiStats>({
    avgResponseTime: 0,
    isOffline: false
  });

  const refreshStats = () => {
    // Logique pour rafraîchir les stats
  };

  return { stats, refreshStats };
};

// Hook pour la performance des composants
const useComponentPerformance = (componentName: string) => {
  const startOperation = (operationName: string) => {
    // Logique pour démarrer l'opération
  };

  const endOperation = (operationName: string, success: boolean, error?: ApiError) => {
    // Logique pour terminer l'opération
  };

  return { startOperation, endOperation };
};

// Exemple d'utilisation dans un composant
const MyComponent: React.FC = () => {
  const { stats, refreshStats } = useApiMonitoring();
  const { startOperation, endOperation } = useComponentPerformance('MyComponent');

  const handleComplexOperation = async () => {
    startOperation('dataProcessing');
    
    try {
      await someComplexAsyncOperation();
      endOperation('dataProcessing', true);
    } catch(error) {
      const apiError = error as ApiError;
      endOperation('dataProcessing', false, { 
        message: apiError.message,
        type: 'operation_error'
      });
    }
  };

  return (
    <View>
      <Text>Temps moyen de réponse API: {stats.avgResponseTime.toFixed(2)}ms</Text>
      <Text>État du réseau: {stats.isOffline ? 'Hors ligne' : 'En ligne'}</Text>
      
      <Button title="Opération" onPress={handleComplexOperation} />
      <Button title="Rafraîchir stats" onPress={refreshStats} />
    </View>
  );
};

// Service utilisateur avec monitoring
interface UserService {
  fetchUserProfile: (userId: string) => Promise<any>;
}

const UserService: UserService = {
  async fetchUserProfile(userId: string) {
    monitoringInstance.track('user.fetch.started', 1, { userId });
    
    try {
      const { duration } = await measureExecutionTime(
        async () => {
          return await api.get(`/users/${userId}`);
        },
        'user.fetch',
        { logToConsole: true }
      );
      
      monitoringInstance.track('user.fetch.success', 1, { 
        userId, 
        duration,
        hasData: true
      });
      
      return await api.get(`/users/${userId}`);
    } catch(error) {
      const apiError = error as ApiError;
      monitoringInstance.track('user.fetch.error', 1, {
        userId,
        error: {
          message: apiError.message,
          type: 'api_error'
        }
      });
      
      throw error;
    }
  }
};

// Mesure du temps d'exécution
const measureExecutionTime = async (
  operation: () => Promise<any>,
  operationName: string,
  options: { logToConsole: boolean }
): Promise<OperationResult> => {
  const startTime = Date.now();
  try {
    await operation();
    const duration = Date.now() - startTime;
    
    if (options.logToConsole) {
      console.log(`${operationName} terminé en ${duration}ms`);
    }
    
    return { success: true, duration };
  } catch(error) {
    const duration = Date.now() - startTime;
    
    if (options.logToConsole) {
      console.error(`${operationName} échoué en ${duration}ms`);
    }
    
    throw error;
  }
};

// API mock
interface ApiResponse {
  data: {
    id: string;
    name: string;
  };
}

const api = {
  get: async (url: string): Promise<ApiResponse> => {
    return new Promise(resolve => {
      setTimeout(() => resolve({ data: { id: url.split('/').pop() || '', name: 'User' } }), 500);
    });
  }
};

// Fonction à mesurer mentionnée dans l'exemple
async function someComplexAsyncOperation() {
  return new Promise(resolve => setTimeout(resolve, 1000));
}