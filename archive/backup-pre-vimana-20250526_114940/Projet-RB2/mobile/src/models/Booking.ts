import Model from '@nozbe/watermelondb/Model';
// import { field, date, readonly, relation, table } from '@nozbe/watermelondb/decorators';
import { field, date, readonly, relation, table } from '../types/watermelondb-stubs';

@table('bookings')
export class Booking extends Model {
  static table = 'bookings';
  
  @field('retreat_id') retreatId!: string;
  @field('user_id') userId!: string;
  @field('status') status!: string;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;
  
  @relation('retreats', 'retreat_id') retreat: any;
  @relation('users', 'user_id') user: any;
}

// Export par défaut pour compatibilité avec code existant
export default Booking;
