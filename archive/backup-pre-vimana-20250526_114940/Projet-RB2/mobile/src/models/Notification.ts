import { Model } from '@nozbe/watermelondb';
import { field, date, readonly, relation, table } from '@nozbe/watermelondb/decorators';

export enum NotificationType {
  RETREAT_CREATED = 'retreat_created',
  RETREAT_UPDATED = 'retreat_updated',
  BOOKING_CREATED = 'booking_created',
  BOOKING_UPDATED = 'booking_updated',
  BOOKING_ACCEPTED = 'booking_accepted',
  BOOKING_REJECTED = 'booking_rejected',
  BOOKING_CONFIRMED = 'booking_confirmed',
  BOOKING_CANCELED = 'booking_canceled',
  MESSAGE_RECEIVED = 'message_received',
}

@table('notifications')
export default class Notification extends Model {
  static table = 'notifications';

  @field('type') type!: NotificationType;
  @field('title') title!: string;
  @field('message') message!: string;
  @field('data') data!: any;
  @field('user_id') userId!: string;
  @field('read') read!: boolean;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;
  
  @relation('users', 'user_id') user: any;
}
