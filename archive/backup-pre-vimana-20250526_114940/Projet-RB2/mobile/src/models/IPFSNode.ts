import { Model } from '@nozbe/watermelondb';
import { field, date, readonly, table } from '@nozbe/watermelondb/decorators';

@table('ipfs_nodes')
export default class IPFSNode extends Model {
  static table = 'ipfs_nodes';
  
  @field('address') address!: string;
  @field('status') status!: string;
  @field('last_sync') lastSync?: Date;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;
} 