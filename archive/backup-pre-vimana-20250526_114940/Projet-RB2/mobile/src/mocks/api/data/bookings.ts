/**
 * Données mockées pour les réservations
 */

export interface BookingMock {
  id: string;
  retreatId: string;
  userId: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  participants: number;
  totalPrice: number;
  currency: string;
  date: string;
  specialRequests?: string;
  paymentMethod?: string;
  paymentStatus?: 'pending' | 'paid' | 'refunded';
  createdAt: string;
  updatedAt: string;
}

export const bookingsMock: BookingMock[] = [
  {
    id: 'booking-1',
    retreatId: 'retreat-1',
    userId: 'current-user',
    status: 'confirmed',
    participants: 2,
    totalPrice: 2400,
    currency: 'EUR',
    date: '2024-07-15T00:00:00Z',
    specialRequests: 'Régime végétarien pour les deux participants',
    paymentMethod: 'carte',
    paymentStatus: 'paid',
    createdAt: '2024-03-10T14:30:00Z',
    updatedAt: '2024-03-10T14:30:00Z'
  },
  {
    id: 'booking-2',
    retreatId: 'retreat-3',
    userId: 'current-user',
    status: 'pending',
    participants: 1,
    totalPrice: 1800,
    currency: 'EUR',
    date: '2024-09-05T00:00:00Z',
    specialRequests: 'Chambre avec vue sur la mer si possible',
    paymentMethod: 'virement',
    paymentStatus: 'pending',
    createdAt: '2024-03-15T09:45:00Z',
    updatedAt: '2024-03-15T09:45:00Z'
  },
  {
    id: 'booking-3',
    retreatId: 'retreat-2',
    userId: 'user-2',
    status: 'confirmed',
    participants: 2,
    totalPrice: 3000,
    currency: 'EUR',
    date: '2024-08-10T00:00:00Z',
    paymentMethod: 'carte',
    paymentStatus: 'paid',
    createdAt: '2024-03-05T16:20:00Z',
    updatedAt: '2024-03-05T16:20:00Z'
  },
  {
    id: 'booking-4',
    retreatId: 'retreat-4',
    userId: 'user-3',
    status: 'cancelled',
    participants: 1,
    totalPrice: 950,
    currency: 'EUR',
    date: '2024-10-01T00:00:00Z',
    specialRequests: 'Allergies aux noix et aux arachides',
    paymentMethod: 'carte',
    paymentStatus: 'refunded',
    createdAt: '2024-03-01T11:30:00Z',
    updatedAt: '2024-03-08T14:15:00Z'
  },
  {
    id: 'booking-5',
    retreatId: 'retreat-5',
    userId: 'current-user',
    status: 'completed',
    participants: 1,
    totalPrice: 1350,
    currency: 'EUR',
    date: '2023-11-10T00:00:00Z',
    paymentMethod: 'carte',
    paymentStatus: 'paid',
    createdAt: '2023-10-05T10:30:00Z',
    updatedAt: '2023-10-05T10:30:00Z'
  }
]; 