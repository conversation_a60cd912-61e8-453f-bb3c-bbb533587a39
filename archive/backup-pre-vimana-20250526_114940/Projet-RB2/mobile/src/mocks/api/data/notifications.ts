/**
 * Données mockées pour les notifications
 */

export interface NotificationMock {
  id: string;
  userId: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  read: boolean;
  data?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export const notificationsMock: NotificationMock[] = [
  {
    id: 'notif-1',
    userId: 'current-user',
    type: 'success',
    title: 'Réservation confirmée',
    message: 'Votre réservation pour la retraite Yoga et Méditation au Lac a été confirmée.',
    read: true,
    data: {
      bookingId: 'booking-1',
      retreatId: 'retreat-1'
    },
    createdAt: '2024-03-10T14:35:00Z',
    updatedAt: '2024-03-10T16:20:00Z'
  },
  {
    id: 'notif-2',
    userId: 'current-user',
    type: 'info',
    title: 'Rappel de retraite',
    message: 'Votre retraite Yoga et Méditation au Lac commence dans 2 semaines. N\'oubliez pas de préparer vos affaires !',
    read: false,
    data: {
      retreatId: 'retreat-1',
      startDate: '2024-07-15T00:00:00Z'
    },
    createdAt: '2024-07-01T10:00:00Z',
    updatedAt: '2024-07-01T10:00:00Z'
  },
  {
    id: 'notif-3',
    userId: 'current-user',
    type: 'warning',
    title: 'Paiement en attente',
    message: 'Votre paiement pour la retraite Méditerranéenne est en attente. Veuillez finaliser votre paiement pour confirmer votre place.',
    read: false,
    data: {
      bookingId: 'booking-2',
      retreatId: 'retreat-3',
      paymentStatus: 'pending'
    },
    createdAt: '2024-03-15T10:00:00Z',
    updatedAt: '2024-03-15T10:00:00Z'
  },
  {
    id: 'notif-4',
    userId: 'current-user',
    type: 'info',
    title: 'Nouvelle retraite disponible',
    message: 'Une nouvelle retraite a été ajoutée qui correspond à vos préférences : Immersion Forestière.',
    read: true,
    data: {
      retreatId: 'retreat-4'
    },
    createdAt: '2024-02-10T17:00:00Z',
    updatedAt: '2024-02-10T18:45:00Z'
  },
  {
    id: 'notif-5',
    userId: 'current-user',
    type: 'info',
    title: 'Enquête de satisfaction',
    message: 'Merci d\'avoir participé à notre retraite Artistique. Pourriez-vous prendre quelques minutes pour partager votre expérience ?',
    read: false,
    data: {
      retreatId: 'retreat-5',
      surveyUrl: 'https://survey.retreat-and-be.com/s/12345'
    },
    createdAt: '2023-11-18T09:30:00Z',
    updatedAt: '2023-11-18T09:30:00Z'
  },
  {
    id: 'notif-6',
    userId: 'user-2',
    type: 'success',
    title: 'Réservation confirmée',
    message: 'Votre réservation pour la retraite Detox Digital en Montagne a été confirmée.',
    read: true,
    data: {
      bookingId: 'booking-3',
      retreatId: 'retreat-2'
    },
    createdAt: '2024-03-05T16:25:00Z',
    updatedAt: '2024-03-05T16:30:00Z'
  },
  {
    id: 'notif-7',
    userId: 'user-3',
    type: 'error',
    title: 'Annulation de réservation',
    message: 'Votre réservation pour la retraite Immersion Forestière a été annulée. Un remboursement a été effectué.',
    read: true,
    data: {
      bookingId: 'booking-4',
      retreatId: 'retreat-4',
      refundId: 'refund-1234'
    },
    createdAt: '2024-03-08T14:15:00Z',
    updatedAt: '2024-03-08T14:30:00Z'
  }
]; 