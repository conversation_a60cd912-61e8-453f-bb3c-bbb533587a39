/**
 * Configuration et initialisation des mocks pour le développement
 */

import { setupMockServer } from './api/mockServer';

// Variable qui stocke le disposer du mock server
let disposeMockServer: (() => void) | null = null;

/**
 * Configure le mock server en fonction des variables d'environnement
 */
export function setupMocks() {
  // Vérifier si on doit activer les mocks API
  const shouldMockApi = process.env.MOCK_API_RESPONSES === 'true';
  
  if (shouldMockApi) {
    console.log('🔄 Initialisation du mock server API...');
    disposeMockServer = setupMockServer();
    console.log('✅ Mock server API initialisé!');
  }
}

/**
 * Nettoie les mocks lorsqu'ils ne sont plus nécessaires
 */
export function teardownMocks() {
  if (disposeMockServer) {
    console.log('🧹 Nettoyage du mock server API...');
    disposeMockServer();
    disposeMockServer = null;
    console.log('✅ Mock server API nettoyé!');
  }
} 