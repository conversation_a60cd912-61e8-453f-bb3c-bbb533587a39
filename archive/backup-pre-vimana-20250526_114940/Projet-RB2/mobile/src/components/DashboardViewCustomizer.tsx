import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Switch,
  FlatList,
  Alert
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { DashboardViewConfig, MetricConfig } from '../types/dashboardTypes';
import { unifiedMonitoring } from '../utils/unifiedMonitoring';

interface DashboardViewCustomizerProps {
  view: DashboardViewConfig;
  onUpdate: (updates: Partial<DashboardViewConfig>) => void;
  onDelete: () => void;
  isDarkMode: boolean;
}

/**
 * Composant pour personnaliser une vue de tableau de bord
 */
const DashboardViewCustomizer: React.FC<DashboardViewCustomizerProps> = ({
  view,
  onUpdate,
  onDelete,
  isDarkMode
}) => {
  // États locaux
  const [name, setName] = useState(view.name);
  const [columns, setColumns] = useState(view.layout.columns.toString());
  const [rowHeight, setRowHeight] = useState(
    (view.layout.rowHeight || 100).toString()
  );
  const [refreshInterval, setRefreshInterval] = useState(
    (view.refreshInterval || 0).toString()
  );
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    view.filters?.categories || []
  );
  const [isRefreshEnabled, setIsRefreshEnabled] = useState(
    view.refreshInterval !== undefined && view.refreshInterval > 0
  );
  
  // Liste des catégories disponibles
  const availableCategories = [
    'performance',
    'http',
    'component',
    'analytics',
    'operation',
    'other'
  ];

  // Mettre à jour la vue lorsque les états locaux changent
  useEffect(() => {
    onUpdate({
      name,
      layout: {
        columns: parseInt(columns) || 1,
        rowHeight: parseInt(rowHeight) || 100,
        groupByCategory: view.layout.groupByCategory || false
      },
      refreshInterval: isRefreshEnabled ? parseInt(refreshInterval) || 30 : undefined,
      filters: {
        ...view.filters,
        categories: selectedCategories
      }
    });
  }, [name, columns, rowHeight, refreshInterval, selectedCategories, isRefreshEnabled, view.layout.groupByCategory]);

  // Fonction pour ajouter ou supprimer une catégorie
  const toggleCategory = (category: string) => {
    if (selectedCategories.includes(category)) {
      setSelectedCategories(selectedCategories.filter(c => c !== category));
    } else {
      setSelectedCategories([...selectedCategories, category]);
    }
  };

  // Fonction pour confirmer la suppression
  const confirmDelete = () => {
    Alert.alert(
      'Supprimer la vue',
      `Êtes-vous sûr de vouloir supprimer "${view.name}" ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: onDelete
        }
      ]
    );
  };

  // Rafraîchir les métriques
  const refreshMetrics = async () => {
    try {
      // Obtenir les métriques actuelles du service de monitoring
      const stats = unifiedMonitoring.getPerformanceStats();
      
      // Construire un tableau de métriques à partir des statistiques
      const metrics: MetricConfig[] = [];
      let order = 0;
      
      for (const [name, data] of Object.entries(stats)) {
        // Déterminer la catégorie
        let category = 'other';
        if (name.startsWith('http.')) {
          category = 'http';
        } else if (name.startsWith('component.')) {
          category = 'component';
        } else if (name.startsWith('performance.')) {
          category = 'performance';
        } else if (name.startsWith('operation.')) {
          category = 'operation';
        } else if (name.startsWith('analytics.')) {
          category = 'analytics';
        }
        
        // Si cette catégorie est incluse dans les filtres de la vue
        if (selectedCategories.includes(category)) {
          // Extraire le nom d'affichage
          const displayName = name.includes('.') ? name.split('.').slice(1).join('.') : name;
          
          // Déterminer le format
          let format = '';
          if (name.includes('duration') || name.includes('time')) {
            format = 'ms';
          } else if (name.includes('size') || name.includes('bytes')) {
            format = 'kb';
          }
          
          // Ajouter la métrique
          metrics.push({
            id: `${view.id}_${name}`,
            name,
            displayName,
            category,
            visible: true,
            order: order++,
            format,
            chartType: 'line'
          });
        }
      }
      
      // Mettre à jour la vue avec les nouvelles métriques
      onUpdate({
        metrics
      });
      
      Alert.alert(
        'Métriques mises à jour',
        `${metrics.length} métriques ont été ajoutées à la vue.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Erreur lors du rafraîchissement des métriques:', error);
      unifiedMonitoring.captureError(error as Error, { 
        context: 'DashboardViewCustomizer.refreshMetrics' 
      });
      
      Alert.alert(
        'Erreur',
        'Une erreur est survenue lors du rafraîchissement des métriques',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <View style={styles.container}>
      {/* Informations de base */}
      <View style={[styles.formGroup, isDarkMode && styles.formGroupDark]}>
        <Text style={[styles.label, isDarkMode && styles.textDark]}>Nom de la vue</Text>
        <TextInput
          style={[styles.input, isDarkMode && styles.inputDark]}
          value={name}
          onChangeText={setName}
          placeholder="Nom de la vue"
          placeholderTextColor={isDarkMode ? '#999999' : '#999999'}
        />
      </View>

      {/* Mise en page */}
      <Text style={[styles.sectionSubtitle, isDarkMode && styles.textDark]}>
        Mise en page
      </Text>
      <View style={styles.rowContainer}>
        <View style={[styles.formGroupHalf, isDarkMode && styles.formGroupDark]}>
          <Text style={[styles.label, isDarkMode && styles.textDark]}>Colonnes</Text>
          <TextInput
            style={[styles.input, isDarkMode && styles.inputDark]}
            value={columns}
            onChangeText={setColumns}
            keyboardType="number-pad"
            placeholder="1"
            placeholderTextColor={isDarkMode ? '#999999' : '#999999'}
          />
        </View>
        <View style={[styles.formGroupHalf, isDarkMode && styles.formGroupDark]}>
          <Text style={[styles.label, isDarkMode && styles.textDark]}>Hauteur des lignes</Text>
          <TextInput
            style={[styles.input, isDarkMode && styles.inputDark]}
            value={rowHeight}
            onChangeText={setRowHeight}
            keyboardType="number-pad"
            placeholder="100"
            placeholderTextColor={isDarkMode ? '#999999' : '#999999'}
          />
        </View>
      </View>

      {/* Intervalle de rafraîchissement */}
      <View style={styles.switchContainer}>
        <Text style={[styles.switchLabel, isDarkMode && styles.textDark]}>
          Rafraîchissement automatique
        </Text>
        <Switch
          value={isRefreshEnabled}
          onValueChange={setIsRefreshEnabled}
          trackColor={{ false: '#D1D1D6', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>
      
      {isRefreshEnabled && (
        <View style={[styles.formGroup, isDarkMode && styles.formGroupDark]}>
          <Text style={[styles.label, isDarkMode && styles.textDark]}>
            Intervalle de rafraîchissement (secondes)
          </Text>
          <TextInput
            style={[styles.input, isDarkMode && styles.inputDark]}
            value={refreshInterval}
            onChangeText={setRefreshInterval}
            keyboardType="number-pad"
            placeholder="30"
            placeholderTextColor={isDarkMode ? '#999999' : '#999999'}
          />
        </View>
      )}

      {/* Catégories de métriques */}
      <Text style={[styles.sectionSubtitle, isDarkMode && styles.textDark]}>
        Catégories de métriques
      </Text>
      <View style={styles.categoriesContainer}>
        {availableCategories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryChip,
              selectedCategories.includes(category) && styles.selectedCategoryChip,
              isDarkMode && styles.categoryChipDark,
              selectedCategories.includes(category) && isDarkMode && styles.selectedCategoryChipDark
            ]}
            onPress={() => toggleCategory(category)}
          >
            <Text
              style={[
                styles.categoryChipText,
                selectedCategories.includes(category) && styles.selectedCategoryChipText,
                isDarkMode && styles.categoryChipTextDark
              ]}
            >
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Métriques */}
      <View style={styles.metricsHeader}>
        <Text style={[styles.sectionSubtitle, isDarkMode && styles.textDark]}>
          Métriques
        </Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={refreshMetrics}
        >
          <Icon name="refresh" size={16} color="#007AFF" />
          <Text style={styles.refreshButtonText}>Actualiser les métriques</Text>
        </TouchableOpacity>
      </View>
      
      <View style={[styles.metricsContainer, isDarkMode && styles.metricsContainerDark]}>
        {view.metrics.length === 0 ? (
          <Text style={[styles.emptyText, isDarkMode && styles.emptyTextDark]}>
            Aucune métrique disponible. Cliquez sur "Actualiser les métriques" pour charger les métriques disponibles.
          </Text>
        ) : (
          <Text style={[styles.metricCount, isDarkMode && styles.textDark]}>
            {view.metrics.length} métriques disponibles dans cette vue
          </Text>
        )}
      </View>

      {/* Action de suppression */}
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={confirmDelete}
      >
        <Icon name="delete" size={16} color="#E53935" />
        <Text style={styles.deleteButtonText}>Supprimer cette vue</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8
  },
  formGroup: {
    marginBottom: 16
  },
  formGroupDark: {
    backgroundColor: '#1E1E1E'
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  formGroupHalf: {
    width: '48%',
    marginBottom: 16
  },
  label: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8
  },
  textDark: {
    color: '#E0E0E0'
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    padding: 10,
    fontSize: 14,
    color: '#333333'
  },
  inputDark: {
    borderColor: '#444444',
    color: '#E0E0E0',
    backgroundColor: '#2A2A2A'
  },
  sectionSubtitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  switchLabel: {
    fontSize: 14,
    color: '#666666'
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16
  },
  categoryChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0'
  },
  categoryChipDark: {
    backgroundColor: '#2A2A2A',
    borderColor: '#444444'
  },
  selectedCategoryChip: {
    backgroundColor: '#E1F5FE',
    borderColor: '#4FC3F7'
  },
  selectedCategoryChipDark: {
    backgroundColor: '#0D47A1',
    borderColor: '#1976D2'
  },
  categoryChipText: {
    fontSize: 14,
    color: '#666666'
  },
  categoryChipTextDark: {
    color: '#BBBBBB'
  },
  selectedCategoryChipText: {
    color: '#0288D1',
    fontWeight: '500'
  },
  metricsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4
  },
  refreshButtonText: {
    color: '#007AFF',
    marginLeft: 4,
    fontSize: 14
  },
  metricsContainer: {
    padding: 12,
    backgroundColor: '#F9F9F9',
    borderRadius: 4,
    marginBottom: 16
  },
  metricsContainerDark: {
    backgroundColor: '#242424'
  },
  emptyText: {
    textAlign: 'center',
    color: '#999999',
    fontSize: 14,
    fontStyle: 'italic'
  },
  emptyTextDark: {
    color: '#BBBBBB'
  },
  metricCount: {
    fontSize: 14,
    color: '#666666'
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    padding: 4
  },
  deleteButtonText: {
    color: '#E53935',
    marginLeft: 4,
    fontSize: 14
  }
});

export default DashboardViewCustomizer;