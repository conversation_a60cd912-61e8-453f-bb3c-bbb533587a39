import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  useColorScheme
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

/**
 * Types pour le composant RoadmapView
 */
export interface SubTask {
  id: string;
  title: string;
  status: 'completed' | 'in-progress' | 'pending'
}

export interface RoadmapTask {
  id: string;
  title: string;
  status: 'completed' | 'in-progress' | 'pending';
  subtasks: SubTask[];
  period?: string;
}

export interface RoadmapViewProps {
  title: string;
  description?: string;
  tasks: RoadmapTask[];
  period?: string;
}

/**
 * Composant pour afficher une roadmap des tâches avec leur état d'avancement
 */
const RoadmapView: React.FC<RoadmapViewProps> = ({
  title,
  description,
  tasks,
  period
}) => {
  const colorScheme = useColorScheme()
  const isDarkMode = colorScheme === 'dark';
  
  // Calculer le pourcentage d'avancement global
  const calculateProgress = (task: RoadmapTask): number => {
    const totalSubtasks = task.subtasks.length;
    if (totalSubtasks === 0) return task.status === 'completed' ? 100 : 0;
    
    const completedSubtasks = task.subtasks.filter(
      subtask => subtask.status === 'completed'
    ).length;
    
    return Math.round((completedSubtasks / totalSubtasks) * 100);
  }
  
  // Obtenir l'icône pour le statut
  const getStatusIcon = (status: 'completed' | 'in-progress' | 'pending'): string => {
    switch(status) {
      case 'completed':
        return 'check-circle';
      case 'in-progress':
        return 'progress-clock';
      case 'pending':
        return 'clock-outline';
      default: return 'help-circle-outline'
    }
  }
  
  // Obtenir la couleur pour le statut
  const getStatusColor = (status: 'completed' | 'in-progress' | 'pending'): string => {
    switch(status) {
      case 'completed':
        return '#34C759'; // Vert
      case 'in-progress':
        return '#FF9500'; // Orange
      case 'pending':
        return isDarkMode ? '#777777' : '#AAAAAA'; // Gris
      default: return '#CCCCCC'
    }
  }
  
  // Obtenir l'emoji pour le statut
  const getStatusEmoji = (status: 'completed' | 'in-progress' | 'pending'): string => {
    switch(status) {
      case 'completed':
        return '✅';
      case 'in-progress':
        return '🟡';
      case 'pending':
        return '⏳';
      default: return '❓'
    }
  }
  
  return (
    <ScrollView
      style={[styles.container, isDarkMode && styles.containerDark]}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.header}>
        <Text style={[styles.title, isDarkMode && styles.titleDark]}>
          {title}
        </Text>
        {period && (
          <Text style={[styles.period, isDarkMode && styles.periodDark]}>
            {period}
          </Text>
        )}
      </View>
      
      {description && (
        <Text style={[styles.description, isDarkMode && styles.descriptionDark]}>
          {description}
        </Text>
      )}
      
      <View style={styles.taskList}>
        {tasks.map((task, index) => {
          const progress = calculateProgress(task);
          
          return (
            <View key={task.id} style={styles.taskItem}>
              <View style={styles.taskHeader}>
                <View style={styles.taskTitleContainer}>
                  <Text style={[styles.taskNumber, isDarkMode && styles.taskNumberDark]}>
                    {index + 1}.
                  </Text>
                  <Text style={[styles.taskTitle, isDarkMode && styles.taskTitleDark]}>
                    {getStatusEmoji(task.status)} {task.title}
                  </Text>
                </View>
                
                <View style={styles.progressContainer}>
                  <Text style={[styles.progressText, { color: getStatusColor(task.status) }]}>
                    {progress}%
                  </Text>
                </View>
              </View>
              
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill, 
                    { 
                      width: `${progress}%`,
                      backgroundColor: getStatusColor(task.status)
                    }
                  ]} 
                />
              </View>
              
              <View style={styles.subtaskList}>
                {task.subtasks.map(subtask => (
                  <View key={subtask.id} style={styles.subtaskItem}>
                    <Icon
                      name={getStatusIcon(subtask.status)} 
                      size={16}
                      color={getStatusColor(subtask.status)} 
                      style={styles.subtaskIcon}
                    />
                    <Text style={[styles.subtaskText, isDarkMode && styles.subtaskTextDark]}>
                      {subtask.title}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA'
  },
  containerDark: {
    backgroundColor: '#121212'
  },
  contentContainer: {
    padding: 16
  },
  header: {
    marginBottom: 16
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4
  },
  titleDark: {
    color: '#FFFFFF'
  },
  period: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8
  },
  periodDark: {
    color: '#AAAAAA'
  },
  description: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 24
  },
  descriptionDark: {
    color: '#BBBBBB'
  },
  taskList: {
    gap: 24
  },
  taskItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },
  taskTitleContainer: {
    flexDirection: 'row',
    flex: 1
  },
  taskNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginRight: 8
  },
  taskNumberDark: {
    color: '#FFFFFF'
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    flex: 1
  },
  taskTitleDark: {
    color: '#FFFFFF'
  },
  progressContainer: {
    marginLeft: 8
  },
  progressText: {
    fontSize: 14,
    fontWeight: 'bold'
  },
  progressBar: {
    height: 6,
    backgroundColor: '#EEEEEE',
    borderRadius: 3,
    marginBottom: 16,
    overflow: 'hidden'
  },
  progressFill: {
    height: '100%'
  },
  subtaskList: {
    gap: 8
  },
  subtaskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6
  },
  subtaskIcon: {
    marginRight: 8
  },
  subtaskText: {
    fontSize: 14,
    color: '#333333'
  },
  subtaskTextDark: {
    color: '#E0E0E0'
  }
});

export default RoadmapView;