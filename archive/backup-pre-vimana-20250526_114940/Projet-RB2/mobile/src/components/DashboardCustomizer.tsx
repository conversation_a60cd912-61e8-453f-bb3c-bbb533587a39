import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Alert,
  useColorScheme,
  SafeAreaView
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { v4 as uuidv4 } from 'uuid';
import { DashboardConfig, DashboardViewConfig } from '../types/dashboardTypes';
import { unifiedMonitoring } from '../utils/unifiedMonitoring';
import { dashboardConfigService } from '../services/dashboardConfig.service';
import DashboardViewCustomizer from './DashboardViewCustomizer';
import Button from './ui/Button';

/**
 * Props du composant DashboardCustomizer
 */
interface DashboardCustomizerProps {
  visible: boolean;
  onClose: () => void;
  activeDashboard?: DashboardConfig;
}

/**
 * Composant pour personnaliser les tableaux de bord
 */
const DashboardCustomizer: React.FC<DashboardCustomizerProps> = ({
  visible,
  onClose,
  activeDashboard
}) => {
  // États locaux
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [dashboardName, setDashboardName] = useState<string>('');
  const [dashboardDescription, setDashboardDescription] = useState<string>('');
  const [dashboardViews, setDashboardViews] = useState<DashboardViewConfig[]>([]);
  const [isDefault, setIsDefault] = useState<boolean>(false);
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0);
  const colorScheme = useColorScheme();
  const isDarkMode = colorScheme === 'dark';

  // Effet pour initialiser le formulaire
  useEffect(() => {
    if (visible) {
      if (activeDashboard) {
        // Mode édition
        setIsEditing(true);
        setDashboardName(activeDashboard.name);
        setDashboardDescription(activeDashboard.description || '');
        setDashboardViews([...activeDashboard.views]);
        setIsDefault(activeDashboard.id === 'default');
      } else {
        // Mode création
        resetForm();
      }
    }
  }, [visible, activeDashboard]);

  // Réinitialiser le formulaire
  const resetForm = () => {
    setIsEditing(false);
    setDashboardName('Nouveau tableau de bord');
    setDashboardDescription('');
    setDashboardViews([
      {
        id: uuidv4(),
        name: 'Vue principale',
        metrics: [],
        layout: {
          columns: 2,
          rowHeight: 120,
          groupByCategory: true
        },
        filters: {
          categories: ['performance', 'http', 'component']
        },
        refreshInterval: 30
      }
    ]);
    setIsDefault(false);
    setActiveTabIndex(0);
  };

  // Soumettre le formulaire
  const handleSubmit = async () => {
    try {
      if (!dashboardName.trim()) {
        Alert.alert(
          'Erreur',
          'Veuillez saisir un nom pour le tableau de bord',
          [{ text: 'OK' }]
        );
        return;
      }

      if (dashboardViews.length === 0) {
        Alert.alert(
          'Erreur',
          'Le tableau de bord doit avoir au moins une vue',
          [{ text: 'OK' }]
        );
        return;
      }

      // Valider toutes les vues
      for (const view of dashboardViews) {
        if (!view.name.trim()) {
          Alert.alert(
            'Erreur',
            'Toutes les vues doivent avoir un nom',
            [{ text: 'OK' }]
          );
          return;
        }
      }

      // Construire la configuration du tableau de bord
      const dashboardConfig: Omit<DashboardConfig, 'createdAt' | 'updatedAt'> = {
        id: isEditing ? activeDashboard!.id : uuidv4(),
        name: dashboardName.trim(),
        description: dashboardDescription.trim() || undefined,
        views: dashboardViews,
        defaultView: dashboardViews[0].id
      };

      // Sauvegarder la configuration
      if (isEditing) {
        await dashboardConfigService.updateDashboardConfig(
          dashboardConfig.id,
          dashboardConfig
        );
        Alert.alert(
          'Succès',
          'Le tableau de bord a été mis à jour',
          [{ text: 'OK', onPress: onClose }]
        );
      } else {
        await dashboardConfigService.createDashboardConfig(dashboardConfig);
        Alert.alert(
          'Succès',
          'Le tableau de bord a été créé',
          [{ text: 'OK', onPress: onClose }]
        );
      }

      // Si le tableau de bord doit être défini comme tableau de bord par défaut
      if (isDefault) {
        await dashboardConfigService.updatePreferences({
          defaultDashboard: dashboardConfig.id
        });
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du tableau de bord:', error);
      unifiedMonitoring.captureError(error as Error, {
        context: 'DashboardCustomizer.handleSubmit'
      });

      Alert.alert(
        'Erreur',
        'Une erreur est survenue lors de la sauvegarde du tableau de bord',
        [{ text: 'OK' }]
      );
    }
  };

  // Fonction pour supprimer le tableau de bord
  const handleDelete = () => {
    if (!isEditing || !activeDashboard) {
      return;
    }

    Alert.alert(
      'Supprimer le tableau de bord',
      `Êtes-vous sûr de vouloir supprimer "${activeDashboard.name}" ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              await dashboardConfigService.deleteDashboardConfig(activeDashboard.id);
              Alert.alert(
                'Succès',
                'Le tableau de bord a été supprimé',
                [{ text: 'OK', onPress: onClose }]
              );
            } catch (error) {
              console.error('Erreur lors de la suppression du tableau de bord:', error);
              unifiedMonitoring.captureError(error as Error, {
                context: 'DashboardCustomizer.handleDelete'
              });

              Alert.alert(
                'Erreur',
                'Une erreur est survenue lors de la suppression du tableau de bord',
                [{ text: 'OK' }]
              );
            }
          }
        },
      ]
    );
  };

  // Fonction pour ajouter une vue
  const addView = () => {
    const newView: DashboardViewConfig = {
      id: uuidv4(),
      name: `Nouvelle vue ${dashboardViews.length + 1}`,
      metrics: [],
      layout: {
        columns: 2,
        rowHeight: 120,
        groupByCategory: false
      },
      filters: {
        categories: ['performance']
      }
    };

    setDashboardViews([...dashboardViews, newView]);
    setActiveTabIndex(dashboardViews.length);
  };

  // Fonction pour mettre à jour une vue
  const updateView = (index: number, updates: Partial<DashboardViewConfig>) => {
    const updatedViews = [...dashboardViews];
    updatedViews[index] = {
      ...updatedViews[index],
      ...updates
    };
    setDashboardViews(updatedViews);
  };

  // Fonction pour supprimer une vue
  const deleteView = (index: number) => {
    if (dashboardViews.length <= 1) {
      Alert.alert(
        'Erreur',
        'Vous ne pouvez pas supprimer la dernière vue du tableau de bord',
        [{ text: 'OK' }]
      );
      return;
    }

    const updatedViews = [...dashboardViews];
    updatedViews.splice(index, 1);
    setDashboardViews(updatedViews);

    if (activeTabIndex >= updatedViews.length) {
      setActiveTabIndex(updatedViews.length - 1);
    }
  };

  // Rendu de l'onglet actif
  const renderActiveTab = () => {
    if (activeTabIndex === 0) {
      return (
        <View style={styles.formContainer}>
          <View style={[styles.formGroup, isDarkMode && styles.formGroupDark]}>
            <Text style={[styles.label, isDarkMode && styles.textDark]}>Nom du tableau de bord</Text>
            <TextInput
              style={[styles.input, isDarkMode && styles.inputDark]}
              value={dashboardName}
              onChangeText={setDashboardName}
              placeholder="Nom du tableau de bord"
              placeholderTextColor={isDarkMode ? '#999999' : '#999999'}
            />
          </View>

          <View style={[styles.formGroup, isDarkMode && styles.formGroupDark]}>
            <Text style={[styles.label, isDarkMode && styles.textDark]}>Description (optionnelle)</Text>
            <TextInput
              style={[styles.input, styles.textArea, isDarkMode && styles.inputDark]}
              value={dashboardDescription}
              onChangeText={setDashboardDescription}
              placeholder="Description du tableau de bord"
              placeholderTextColor={isDarkMode ? '#999999' : '#999999'}
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.switchContainer}>
            <Text style={[styles.switchLabel, isDarkMode && styles.textDark]}>
              Définir comme tableau de bord par défaut
            </Text>
            <Switch
              value={isDefault}
              onValueChange={setIsDefault}
              trackColor={{ false: '#D1D1D6', true: '#007AFF' }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>
      );
    }

    const viewIndex = activeTabIndex - 1;
    if (viewIndex >= 0 && viewIndex < dashboardViews.length) {
      return (
        <DashboardViewCustomizer
          view={dashboardViews[viewIndex]}
          onUpdate={(updates) => updateView(viewIndex, updates)}
          onDelete={() => deleteView(viewIndex)}
          isDarkMode={isDarkMode}
        />
      );
    }

    return null;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.modalContainer, isDarkMode && styles.modalContainerDark]}>
        <View style={[styles.modalContent, isDarkMode && styles.modalContentDark]}>
          {/* En-tête */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Icon
                name="close"
                size={24}
                color={isDarkMode ? '#E0E0E0' : '#333333'}
              />
            </TouchableOpacity>
            <Text style={[styles.title, isDarkMode && styles.textDark]}>
              {isEditing ? 'Modifier le tableau de bord' : 'Nouveau tableau de bord'}
            </Text>
            <TouchableOpacity
              style={[styles.saveButton, !dashboardName.trim() && styles.saveButtonDisabled]}
              onPress={handleSubmit}
              disabled={!dashboardName.trim()}
            >
              <Text
                style={[
                  styles.saveButtonText,
                  !dashboardName.trim() && styles.saveButtonTextDisabled,
                ]}
              >
                Enregistrer
              </Text>
            </TouchableOpacity>
          </View>

          {/* Onglets */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.tabsContainer}
            contentContainerStyle={styles.tabsContent}
          >
            <TouchableOpacity
              style={[
                styles.tab,
                activeTabIndex === 0 && styles.activeTab,
                isDarkMode && styles.tabDark,
                activeTabIndex === 0 && isDarkMode && styles.activeTabDark,
              ]}
              onPress={() => setActiveTabIndex(0)}
            >
              <Icon
                name="information-outline"
                size={16}
                color={activeTabIndex === 0 ? '#007AFF' : isDarkMode ? '#BBBBBB' : '#666666'}
                style={styles.tabIcon}
              />
              <Text
                style={[
                  styles.tabText,
                  activeTabIndex === 0 && styles.activeTabText,
                  isDarkMode && styles.tabTextDark,
                ]}
              >
                Informations
              </Text>
            </TouchableOpacity>

            {dashboardViews.map((view, index) => (
              <TouchableOpacity
                key={view.id}
                style={[
                  styles.tab,
                  activeTabIndex === index + 1 && styles.activeTab,
                  isDarkMode && styles.tabDark,
                  activeTabIndex === index + 1 && isDarkMode && styles.activeTabDark,
                ]}
                onPress={() => setActiveTabIndex(index + 1)}
              >
                <Icon
                  name="view-dashboard-outline"
                  size={16}
                  color={activeTabIndex === index + 1 ? '#007AFF' : isDarkMode ? '#BBBBBB' : '#666666'}
                  style={styles.tabIcon}
                />
                <Text
                  style={[
                    styles.tabText,
                    activeTabIndex === index + 1 && styles.activeTabText,
                    isDarkMode && styles.tabTextDark,
                  ]}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {view.name}
                </Text>
              </TouchableOpacity>
            ))}

            <TouchableOpacity
              style={[styles.addButton, isDarkMode && styles.addButtonDark]}
              onPress={addView}
            >
              <Icon name="plus" size={20} color={isDarkMode ? '#E0E0E0' : '#333333'} />
            </TouchableOpacity>
          </ScrollView>

          {/* Contenu */}
          <ScrollView
            style={styles.content}
            contentContainerStyle={styles.contentInner}
          >
            {renderActiveTab()}
          </ScrollView>

          {/* Bouton de suppression */}
          {isEditing && activeDashboard && activeDashboard.id !== 'default' && (
            <View style={styles.deleteContainer}>
              <Button
                title="Supprimer ce tableau de bord"
                onPress={handleDelete}
                type="danger"
                icon="delete"
              />
            </View>
          )}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)'
  },
  modalContainerDark: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)'
  },
  modalContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    marginTop: 60
  },
  modalContentDark: {
    backgroundColor: '#121212'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0'
  },
  closeButton: {
    padding: 4
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333'
  },
  textDark: {
    color: '#E0E0E0'
  },
  saveButton: {
    padding: 8
  },
  saveButtonDisabled: {
    opacity: 0.5
  },
  saveButtonText: {
    color: '#007AFF',
    fontWeight: '600'
  },
  saveButtonTextDisabled: {
    color: '#999999'
  },
  tabsContainer: {
    flexDirection: 'row',
    height: 48,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0'
  },
  tabsContent: {
    alignItems: 'center'
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 4
  },
  tabDark: {
    backgroundColor: '#121212'
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF'
  },
  activeTabDark: {
    borderBottomColor: '#0A84FF'
  },
  tabIcon: {
    marginRight: 8
  },
  tabText: {
    fontSize: 14,
    color: '#666666'
  },
  tabTextDark: {
    color: '#BBBBBB'
  },
  activeTabText: {
    fontWeight: '600',
    color: '#007AFF'
  },
  addButton: {
    padding: 12,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    marginHorizontal: 8
  },
  addButtonDark: {
    backgroundColor: '#2A2A2A'
  },
  content: {
    flex: 1
  },
  contentInner: {
    padding: 16
  },
  formContainer: {
    marginBottom: 16
  },
  formGroup: {
    marginBottom: 16
  },
  formGroupDark: {
    backgroundColor: '#121212'
  },
  label: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    padding: 10,
    fontSize: 14,
    color: '#333333'
  },
  inputDark: {
    borderColor: '#444444',
    color: '#E0E0E0',
    backgroundColor: '#2A2A2A'
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top'
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  switchLabel: {
    fontSize: 14,
    color: '#666666'
  },
  deleteContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0'
  }
});

export default DashboardCustomizer;