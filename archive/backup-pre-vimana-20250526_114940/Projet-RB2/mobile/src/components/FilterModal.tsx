import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  Modal, 
  StyleSheet, 
  TouchableOpacity, 
  FlatList, 
  ScrollView 
} from 'react-native';
import { useTheme } from '../hooks/useTheme';
import Icon from 'react-native-vector-icons/Ionicons';

interface FilterOption {
  id: string;
  label: string;
  icon: string;
}

interface FilterCategory {
  id: string;
  label: string;
  options: FilterOption[];
}

export interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: Record<string, string[]>) => void;
  filterOptions: FilterCategory[];
  initialFilters?: Record<string, string[]>;
}

const FilterModal: React.FC<FilterModalProps> = ({ 
  visible, 
  onClose,
  onApply,
  filterOptions,
  initialFilters = {}
}) => {
  const { theme } = useTheme();
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>(initialFilters);

  useEffect(() => {
    if (visible) {
      setSelectedFilters(initialFilters);
    }
  }, [visible, initialFilters]);

  const toggleFilter = (categoryId: string, optionId: string) => {
    setSelectedFilters(prev => {
      const currentCategory = prev[categoryId] || [];
      
      if (currentCategory.includes(optionId)) {
        return {
          ...prev,
          [categoryId]: currentCategory.filter(id => id !== optionId)
        };
      } else {
        return {
          ...prev,
          [categoryId]: [...currentCategory, optionId]
        };
      }
    });
  };

  const handleApply = () => {
    onApply(selectedFilters);
    onClose();
  };

  const handleClear = () => {
    setSelectedFilters({});
  };

  // Compte le nombre total de filtres sélectionnés
  const getSelectedCount = () => {
    return Object.values(selectedFilters).reduce((sum, options) => sum + options.length, 0);
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
        <View style={[styles.content, { backgroundColor: theme.background }]}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.text }]}>Filtres</Text>
            <TouchableOpacity onPress={onClose}>
              <Icon name="close" size={24} color={theme.text} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.filtersContainer}>
            {filterOptions.map(category => (
              <View key={category.id} style={styles.categoryContainer}>
                <Text style={[styles.categoryTitle, { color: theme.text }]}>
                  {category.label}
                </Text>
                <View style={styles.optionsContainer}>
                  {category.options.map(option => {
                    const isSelected = (selectedFilters[category.id] || []).includes(option.id);
                    
                    return (
                      <TouchableOpacity
                        key={option.id}
                        style={[
                          styles.optionButton,
                          { borderColor: theme.border },
                          isSelected && { backgroundColor: theme.primary, borderColor: theme.primary }
                        ]}
                        onPress={() => toggleFilter(category.id, option.id)}
                      >
                        <Icon
                          name={option.icon}
                          size={18}
                          color={isSelected ? '#FFFFFF' : theme.text}
                          style={styles.optionIcon}
                        />
                        <Text
                          style={[
                            styles.optionLabel,
                            { color: isSelected ? '#FFFFFF' : theme.text }
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>
            ))}
          </ScrollView>
          
          <View style={styles.footer}>
            <TouchableOpacity
              style={[styles.clearButton, { borderColor: theme.border }]}
              onPress={handleClear}
            >
              <Text style={{ color: theme.text }}>Effacer</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.applyButton, { backgroundColor: theme.primary }]}
              onPress={handleApply}
            >
              <Text style={styles.applyButtonText}>
                Appliquer{getSelectedCount() > 0 ? ` (${getSelectedCount()})` : ''}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  content: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingBottom: 30,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filtersContainer: {
    flex: 1,
  },
  categoryContainer: {
    paddingVertical: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8,
  },
  optionIcon: {
    marginRight: 6,
  },
  optionLabel: {
    fontSize: 14,
  },
  footer: {
    flexDirection: 'row',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  clearButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    marginRight: 8,
  },
  applyButton: {
    flex: 2,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  applyButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});

export default FilterModal; 