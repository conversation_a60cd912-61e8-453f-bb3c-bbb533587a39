import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme'

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; },
  get info() { return theme.primary; } // Fallback pour 'info'
});
;
import Text from './Text';

export type NotificationType = 'info' | 'success' | 'warning' | 'error';
interface NotificationItemProps {
  title: string;
  message: string;
  timestamp: string | Date;
  type?: NotificationType;
  read?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  title,
  message,
  timestamp,
  type = 'info',
  read = false,
  onPress,
  style
}) => {
  const { theme: originalTheme } = useTheme();
  // Utiliser l'adaptateur de thème pour la compatibilité
  const theme = getCompatibleTheme(originalTheme);

  const getIconName = () => {
    switch(type) {
      case 'success':
        return 'check-circle';
      case 'warning':
        return 'alert-circle';
      case 'error':
        return 'close-circle';
      case 'info':
      default: 
        return 'information';
    }
  };

  const getIconColor = () => {
    switch(type) {
      case 'success':
        return theme.secondary;
      case 'warning':
        return theme.accent;
      case 'error':
        return theme.error;
      case 'info':
      default: 
        return theme.primary;
    }
  };

  const formatTimestamp = (time: string | Date) => {
    const date = typeof time === 'string' ? new Date(time) : time;
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);
    const diffHours = Math.round(diffMs / 3600000);
    const diffDays = Math.round(diffMs / 86400000);

    if (diffMins < 60) {
      return `Il y a ${diffMins} minute${diffMins > 1 ? 's' : ''}`;
    } else if (diffHours < 24) {
      return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else if (diffDays < 7) {
      return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    } else {
      return date.toLocaleDateString('fr-FR', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: read ? theme.card : theme.card + '80',
          borderLeftColor: getIconColor()
        },
        style
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.iconContainer}>
        <Icon name={getIconName()} size={24} color={getIconColor()} />
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text
            style={[
              styles.title,
              { color: theme.text },
              read && { fontWeight: 'normal' }
            ]}
            numberOfLines={1}
          >
            {title}
          </Text>
          
          <Text
            style={[styles.timestamp, { color: theme.text + '80' }]}
          >
            {formatTimestamp(timestamp)}
          </Text>
        </View>
        
        <Text
          style={[styles.message, { color: theme.text + '90' }]}
          numberOfLines={2}
        >
          {message}
        </Text>
      </View>
      
      {!read && (
        <View
          style={[
            styles.unreadIndicator,
            { backgroundColor: getIconColor() }
          ]}
        />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1
  },
  iconContainer: {
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center'
  },
  content: {
    flex: 1,
    padding: 12,
    paddingLeft: 0
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8
  },
  timestamp: {
    fontSize: 12
  },
  message: {
    fontSize: 14,
    lineHeight: 20
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    position: 'absolute',
    top: 12,
    right: 12
  }
});

export default NotificationItem;