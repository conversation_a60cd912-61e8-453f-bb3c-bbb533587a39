import * as React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme'

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; },
  get info() { return theme.primary; } // Fallback pour 'info'
});
;
import Text from './Text';

interface FilterChipProps {
  label: string;
  selected?: boolean;
  icon?: string;
  onPress?: () => void;
  style?: ViewStyle;
  labelStyle?: TextStyle;
  disabled?: boolean;
}

const FilterChip: React.FC<FilterChipProps> = ({
  label,
  selected = false,
  icon,
  onPress,
  style,
  labelStyle,
  disabled = false
}) => {
  const { theme: originalTheme } = useTheme();
  // Utiliser l'adaptateur de thème pour la compatibilité
  const theme = getCompatibleTheme(originalTheme);

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: selected ? theme.primary + '20' : theme.card,
          borderColor: selected ? theme.primary : theme.border
        },
        disabled && { opacity: 0.6 },
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.7}
      disabled={disabled}
    >
      {icon && (
        <Icon
          name={icon}
          size={16}
          color={selected ? theme.primary : theme.text + '80'}
          style={styles.icon}
        />
      )}
      
      <Text
        style={[
          styles.label,
          {
            color: selected ? theme.primary : theme.text
          },
          labelStyle,
        ]}
      >
        {label}
      </Text>
      
      {selected && (
        <Icon
          name="close-circle"
          size={16}
          color={theme.primary}
          style={styles.closeIcon}
        />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8
  },
  icon: {
    marginRight: 6
  },
  label: {
    fontSize: 14,
    fontWeight: '500'
  },
  closeIcon: {
    marginLeft: 6
  }
});

export default FilterChip;