import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { DashboardConfig, DashboardViewConfig } from '../../types/dashboardTypes';

/**
 * Props pour le composant DashboardHeader
 */
export interface DashboardHeaderProps {
  dashboards: DashboardConfig[];
  activeDashboard: DashboardConfig;
  activeView: DashboardViewConfig;
  onChangeDashboard: (dashboardId: string) => void;
  onChangeView: (viewId: string) => void;
  onOpenCustomizer: () => void;
  onEditDashboard: () => void;
  isDarkMode?: boolean;
}

/**
 * Composant d'en-tête pour les tableaux de bord personnalisables
 */
const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  dashboards,
  activeDashboard,
  activeView,
  onChangeDashboard,
  onChangeView,
  onOpenCustomizer,
  onEditDashboard,
  isDarkMode = false
}) => {
  return (
    <View style={[styles.container, isDarkMode && styles.containerDark]}>
      {/* Sélecteur de tableaux de bord */}
      <View style={[styles.row, styles.dashboardSelector]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
        >
          {dashboards.map(dashboard => (
            <TouchableOpacity
              key={dashboard.id}
              style={[
                styles.dashboardItem,
                activeDashboard.id === dashboard.id && styles.activeDashboardItem,
                isDarkMode && styles.dashboardItemDark,
                activeDashboard.id === dashboard.id && isDarkMode && styles.activeDashboardItemDark,
              ]}
              onPress={() => onChangeDashboard(dashboard.id)}
            >
              <Text
                style={[
                  styles.dashboardItemText,
                  activeDashboard.id === dashboard.id && styles.activeDashboardItemText,
                  isDarkMode && styles.dashboardItemTextDark,
                ]}
                numberOfLines={1}
              >
                {dashboard.name}
              </Text>
            </TouchableOpacity>
          ))}
          
          <TouchableOpacity
            style={[
              styles.dashboardItem,
              styles.addDashboardItem,
              isDarkMode && styles.dashboardItemDark,
            ]}
            onPress={onOpenCustomizer}
          >
            <Icon
              name="plus"
              size={16}
              color={isDarkMode ? '#E0E0E0' : '#333333'}
              style={styles.addIcon}
            />
            <Text
              style={[
                styles.dashboardItemText,
                isDarkMode && styles.dashboardItemTextDark,
              ]}
            >
              Nouveau
            </Text>
          </TouchableOpacity>
        </ScrollView>
        
        <TouchableOpacity
          style={styles.editButton}
          onPress={onEditDashboard}
        >
          <Icon
            name="pencil"
            size={18}
            color={isDarkMode ? '#E0E0E0' : '#333333'}
          />
        </TouchableOpacity>
      </View>
      
      {/* Sélecteur de vues */}
      {activeDashboard.views.length > 1 && (
        <View style={[styles.row, styles.viewSelector]}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
          >
            {activeDashboard.views.map(view => (
              <TouchableOpacity
                key={view.id}
                style={[
                  styles.viewItem,
                  activeView.id === view.id && styles.activeViewItem,
                  isDarkMode && styles.viewItemDark,
                  activeView.id === view.id && isDarkMode && styles.activeViewItemDark,
                ]}
                onPress={() => onChangeView(view.id)}
              >
                <Text
                  style={[
                    styles.viewItemText,
                    activeView.id === view.id && styles.activeViewItemText,
                    isDarkMode && styles.viewItemTextDark,
                  ]}
                  numberOfLines={1}
                >
                  {view.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE'
  },
  containerDark: {
    backgroundColor: '#1C1C1E',
    borderBottomColor: '#333333'
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  dashboardSelector: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE'
  },
  viewSelector: {
    paddingVertical: 8,
    paddingHorizontal: 16
  },
  scrollView: {
    flex: 1
  },
  scrollContent: {
    alignItems: 'center'
  },
  dashboardItem: {
    backgroundColor: '#F5F7FA',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    flexDirection: 'row',
    alignItems: 'center'
  },
  dashboardItemDark: {
    backgroundColor: '#2A2A2A'
  },
  activeDashboardItem: {
    backgroundColor: '#007AFF'
  },
  activeDashboardItemDark: {
    backgroundColor: '#0A84FF'
  },
  dashboardItemText: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500'
  },
  dashboardItemTextDark: {
    color: '#E0E0E0'
  },
  activeDashboardItemText: {
    color: '#FFFFFF'
  },
  addDashboardItem: {
    backgroundColor: '#E5F1FF'
  },
  addIcon: {
    marginRight: 4
  },
  editButton: {
    padding: 8,
    marginLeft: 8
  },
  viewItem: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 4
  },
  viewItemDark: {
    backgroundColor: 'transparent'
  },
  activeViewItem: {
    backgroundColor: '#F5F7FA'
  },
  activeViewItemDark: {
    backgroundColor: '#2A2A2A'
  },
  viewItemText: {
    fontSize: 14,
    color: '#666666'
  },
  viewItemTextDark: {
    color: '#BBBBBB'
  },
  activeViewItemText: {
    color: '#333333',
    fontWeight: '500'
  }
})

export default DashboardHeader;