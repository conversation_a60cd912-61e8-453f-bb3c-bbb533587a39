import React from 'react';
import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  ImageStyle
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme';
import Text from './Text';

interface AvatarProps {
  uri?: string;
  name?: string;
  size?: number;
  showBadge?: boolean;
  badgeColor?: string;
  onPress?: () => void;
  style?: ViewStyle;
  imageStyle?: ImageStyle;
}

const Avatar: React.FC<AvatarProps> = ({
  uri,
  name,
  size = 48,
  showBadge = false,
  badgeColor,
  onPress,
  style,
  imageStyle
}) => {
  const { theme } = useTheme()

  const getInitials = () => {
    if (!name) return '';
    
    const nameParts = name.split(' ');
    if(nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase()
    }
    
    return (
      nameParts[0].charAt(0).toUpperCase() +
      nameParts[nameParts.length - 1].charAt(0).toUpperCase()
    );
  };

  const getRandomColor = () => {
    if (!name) return theme.primary;
    
    // Generate a consistent color based on the name
    const colors = [
      theme.primary,
      theme.secondary,
      theme.secondary,
      '#6366F1', // Indigo
      '#8B5CF6', // Violet
      '#EC4899', // Pink
      '#F43F5E', // Rose
      '#F59E0B', // Amber
      '#10B981', // Emerald
    ];
    
    const charCodeSum = name.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
    return colors[charCodeSum % colors.length];
  };

  const Container = onPress ? TouchableOpacity : View;

  return (
    <Container
      style={[
        styles.container,
        {
          width: size,
          height: size,
          borderRadius: size / 2
        },
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {uri ? (
        <Image
          source={{ uri }}
          style={[
            styles.image,
            {
              width: size,
              height: size,
              borderRadius: size / 2
            },
            imageStyle,
          ]}
        />
      ) : (
        <View
          style={[
            styles.initialsContainer,
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              backgroundColor: getRandomColor()
            },
          ]}
        >
          <Text
            style={[
              styles.initials,
              {
                fontSize: size * 0.4,
                color: '#FFFFFF'
              },
            ]}
          >
            {getInitials()}
          </Text>
        </View>
      )}
      
      {showBadge && (
        <View
          style={[
            styles.badge,
            {
              width: size * 0.3,
              height: size * 0.3,
              borderRadius: size * 0.15,
              borderWidth: size * 0.03,
              backgroundColor: badgeColor || theme.secondary,
              borderColor: theme.background,
              right: 0,
              bottom: 0
            },
          ]}
        />
      )}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'visible'
  },
  image: {
    width: '100%',
    height: '100%'
  },
  initialsContainer: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  initials: {
    fontWeight: 'bold',
    textAlign: 'center'
  },
  badge: {
    position: 'absolute'
  }
})

export default Avatar;