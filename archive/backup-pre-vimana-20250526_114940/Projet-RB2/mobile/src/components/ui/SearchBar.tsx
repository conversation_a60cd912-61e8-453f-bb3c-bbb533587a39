import React, { useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  Platform
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme';

interface SearchBarProps {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  onSubmit?: () => void;
  onClear?: () => void;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  autoFocus?: boolean;
}

const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; }
});

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Rechercher...',
  value,
  onChangeText,
  onSubmit,
  onClear,
  containerStyle,
  inputStyle,
  autoFocus = false
}) => {
  const { theme: originalTheme } = useTheme();
  const theme = getCompatibleTheme(originalTheme);
  const [isFocused, setIsFocused] = useState(false);

  const handleClear = () => {
    onChangeText('');
    if (onClear) {
      onClear();
    }
  };

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: theme.card,
          borderColor: isFocused ? theme.primary : theme.border
        },
        containerStyle
      ]}
    >
      <Icon
        name="magnify"
        size={20}
        color={theme.text + '80'}
        style={styles.searchIcon}
      />
      
      <TextInput
        style={[
          styles.input,
          { color: theme.text },
          inputStyle
        ]}
        placeholder={placeholder}
        placeholderTextColor={theme.text + '60'}
        value={value}
        onChangeText={onChangeText}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onSubmitEditing={onSubmit}
        returnKeyType="search"
        clearButtonMode="never"
        autoCapitalize="none"
        autoCorrect={false}
        autoFocus={autoFocus}
      />
      
      {value.length > 0 && (
        <TouchableOpacity
          onPress={handleClear}
          style={styles.clearButton}
          activeOpacity={0.7}
        >
          <Icon
            name="close-circle"
            size={18}
            color={theme.text + '60'}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 12,
    height: 48,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  },
  searchIcon: {
    marginRight: 8
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 8,
    paddingRight: 8,
    height: '100%',
    ...Platform.select({
      web: {
        outlineStyle: 'none'
      }
    })
  },
  clearButton: {
    padding: 4
  }
});

export default SearchBar;