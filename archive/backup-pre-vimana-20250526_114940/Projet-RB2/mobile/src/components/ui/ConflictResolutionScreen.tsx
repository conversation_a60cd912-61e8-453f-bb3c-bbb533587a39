import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  SafeAreaView,
  ActivityIndicator,
  Alert
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSynchronization } from '../../hooks/useSynchronization';
import { useTheme } from '../../theme'

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; },
  get info() { return theme.primary; } // Fallback pour 'info'
});
;
import { ConflictResolutionStrategy } from '../../services/sync.service';

interface ConflictTabProps {
  isActive: boolean;
  title: string;
  count: number;
  onPress: () => void;
}

// Interface pour représenter un conflit
interface Conflict {
  id: string;
  entityName: string;
  localData: any;
  serverData: any;
  localModifiedAt: number;
  serverModifiedAt: number;
}

// Interface pour un conflit résolu
interface ResolvedConflict extends Conflict {
  resolvedAt: number;
  resolutionStrategy: ConflictResolutionStrategy;
}

const ConflictTab: React.FC<ConflictTabProps> = ({ isActive, title, count, onPress }) => {
  const { theme: originalTheme } = useTheme();
  // Utiliser l'adaptateur de thème pour la compatibilité
  const theme = getCompatibleTheme(originalTheme);
  
  return (
    <TouchableOpacity
      style={[
        styles.tab,
        isActive && { borderBottomColor: theme.primary, borderBottomWidth: 2 }
      ]}
      onPress={onPress}
    >
      <Text style={[
        styles.tabText,
        { color: isActive ? theme.primary : theme.textSecondary }
      ]}>
        {title} ({count})
      </Text>
    </TouchableOpacity>
  );
};

const ConflictResolutionScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { theme: originalTheme } = useTheme();
  // Utiliser l'adaptateur de thème pour la compatibilité
  const theme = getCompatibleTheme(originalTheme);
  const [activeTab, setActiveTab] = useState('pending');
  const [resolvingConflictId, setResolvingConflictId] = useState<string | null>(null);
  
  const { 
    pendingConflicts, 
    resolvedConflicts = [],
    resolveConflict,
    loadPendingConflicts
  } = useSynchronization();

  useEffect(() => {
    // Charger les conflits au montage du composant
    loadPendingConflicts();
  }, [loadPendingConflicts]);

  // Grouper les conflits par entité
  const groupedConflicts = React.useMemo(() => {
    const conflicts = activeTab === 'pending' ? pendingConflicts : resolvedConflicts;
    return conflicts.reduce<Record<string, Array<Conflict | ResolvedConflict>>>((groups, conflict) => {
      const entity = conflict.entityName;
      if (!groups[entity]) {
        groups[entity] = [];
      }
      groups[entity].push(conflict);
      return groups;
    }, {});
  }, [activeTab, pendingConflicts, resolvedConflicts]);

  // Générer les sections pour la FlatList
  const sections = Object.keys(groupedConflicts).map(entityName => ({
    title: entityName.charAt(0).toUpperCase() + entityName.slice(1),
    data: groupedConflicts[entityName]
  }));

  // Résoudre un conflit
  const handleResolveConflict = async (
    conflictId: string, 
    strategy: ConflictResolutionStrategy
  ) => {
    try {
      setResolvingConflictId(conflictId);
      await resolveConflict(conflictId, strategy);
      setResolvingConflictId(null);
    } catch (error) {
      Alert.alert(
        'Erreur',
        `Impossible de résoudre le conflit: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
        [{ text: 'OK' }]
      );
      setResolvingConflictId(null);
    }
  };

  // Afficher les détails d'un conflit
  const handleShowDetails = (conflict: Conflict | ResolvedConflict) => {
    Alert.alert(
      `Conflit - ${conflict.entityName}`,
      `ID: ${conflict.id}\n\nLocal: ${JSON.stringify(conflict.localData, null, 2)}\n\nServeur: ${JSON.stringify(conflict.serverData, null, 2)}`,
      [{ text: 'Fermer' }],
      { cancelable: true }
    );
  };

  // Formater la date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Afficher un élément de conflit
  const renderConflictItem = ({ item }: { item: Conflict | ResolvedConflict }) => {
    const isResolving = resolvingConflictId === item.id;
    
    return (
      <View style={[styles.conflictItem, { backgroundColor: theme.card }]}>
        <View style={styles.conflictHeader}>
          <Text style={[styles.conflictTitle, { color: theme.text }]}>
            {item.entityName}: {item.id.substring(0, 8)}...
          </Text>
          
          <TouchableOpacity onPress={() => handleShowDetails(item)}>
            <Icon name="information-outline" size={20} color={theme.info} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.conflictTimestamps}>
          <Text style={[styles.timestampText, { color: theme.textSecondary }]}>
            Local: {formatDate(item.localModifiedAt)}
          </Text>
          <Text style={[styles.timestampText, { color: theme.textSecondary }]}>
            Serveur: {formatDate(item.serverModifiedAt)}
          </Text>
        </View>
        
        {activeTab === 'pending' ? (
          <View style={styles.conflictActions}>
            {isResolving ? (
              <ActivityIndicator size="small" color={theme.primary} />
            ) : (
              <>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: theme.primary }]}
                  onPress={() => handleResolveConflict(item.id, ConflictResolutionStrategy.SERVER_WINS)}
                >
                  <Text style={styles.actionButtonText}>Serveur</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: theme.secondary }]}
                  onPress={() => handleResolveConflict(item.id, ConflictResolutionStrategy.CLIENT_WINS)}
                >
                  <Text style={styles.actionButtonText}>Local</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: theme.success }]}
                  onPress={() => handleResolveConflict(item.id, ConflictResolutionStrategy.MERGE)}
                >
                  <Text style={styles.actionButtonText}>Fusionner</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        ) : (
          <View style={styles.resolvedInfo}>
            <Text style={[styles.resolvedText, { color: theme.success }]}>
              Résolu avec stratégie: {(item as ResolvedConflict).resolutionStrategy}
            </Text>
            <Text style={[styles.timestampText, { color: theme.textSecondary }]}>
              Résolu le: {formatDate((item as ResolvedConflict).resolvedAt)}
            </Text>
          </View>
        )}
      </View>
    );
  };

  // Afficher un groupe d'entités
  const renderSection = ({ item }: { item: { title: string, data: (Conflict | ResolvedConflict)[] } }) => (
    <View style={styles.section}>
      <Text style={[styles.sectionHeader, { color: theme.primary }]}>
        {item.title} ({item.data.length})
      </Text>
      
      {item.data.map(conflict => (
        <View key={conflict.id}>
          {renderConflictItem({ item: conflict })}
        </View>
      ))}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={[styles.header, { borderBottomColor: theme.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-left" size={24} color={theme.text} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: theme.text }]}>
          Résolution des conflits
        </Text>
        
        <View style={{ width: 24 }} />
      </View>
      
      <View style={styles.tabs}>
        <ConflictTab
          isActive={activeTab === 'pending'}
          title="En attente"
          count={pendingConflicts.length}
          onPress={() => setActiveTab('pending')}
        />
        
        <ConflictTab
          isActive={activeTab === 'resolved'}
          title="Résolus"
          count={resolvedConflicts.length}
          onPress={() => setActiveTab('resolved')}
        />
      </View>
      
      {sections.length > 0 ? (
        <FlatList
          data={sections}
          renderItem={renderSection}
          keyExtractor={item => item.title}
          contentContainerStyle={styles.listContent}
        />
      ) : (
        <View style={styles.emptyState}>
          <Icon 
            name="check-circle-outline" 
            size={64} 
            color={theme.secondary}
          />
          <Text style={[styles.emptyText, { color: theme.text }]}>
            {activeTab === 'pending' 
              ? 'Aucun conflit en attente de résolution'
              : 'Aucun conflit résolu à afficher'}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  tabs: {
    flexDirection: 'row',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  conflictItem: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  conflictHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  conflictTitle: {
    fontSize: 15,
    fontWeight: '500',
  },
  conflictTimestamps: {
    marginTop: 8,
  },
  timestampText: {
    fontSize: 12,
    marginVertical: 2,
  },
  conflictActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 4,
  },
  actionButtonText: {
    color: '#FFF',
    fontWeight: '500',
    fontSize: 13,
  },
  resolvedInfo: {
    marginTop: 8,
  },
  resolvedText: {
    fontSize: 13,
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: 16,
  },
});

export default ConflictResolutionScreen; 