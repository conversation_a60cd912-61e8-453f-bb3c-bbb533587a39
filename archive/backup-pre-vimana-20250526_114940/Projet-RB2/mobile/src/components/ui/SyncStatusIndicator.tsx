import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSynchronization } from '../../hooks/useSynchronization';

interface SyncStatusIndicatorProps {
  onPress?: () => void;
  showText?: boolean;
  size?: 'small' | 'large';
}

/**
 * Composant affichant l'état de synchronisation et permettant de la contrôler
 */
const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({
  onPress,
  showText = true,
  size = 'small',
}) => {
  const {
    isSyncing,
    lastSyncTimestamp,
    formatLastSyncTime,
    isSyncNeeded,
    error,
    pendingConflicts,
    pendingOperationsCount,
    triggerSync,
  } = useSynchronization();

  const iconSize = size === 'small' ? 18 : 24;
  const textSize = size === 'small' ? 12 : 14;

  // Couleurs fixes pour éviter les problèmes de typage avec le thème
  const colors = {
    success: '#32D74B',
    error: '#FF3B30',
    primary: '#5469D4',
    warning: '#F7C137',
    info: '#64D2FF',
    secondary: '#7B61FF',
  };

  // Déterminer l'état et la couleur de l'indicateur
  let statusColor = colors.success;
  let statusIcon = 'check-circle';
  let statusText = 'Synchronisé';

  if (error) {
    statusColor = colors.error;
    statusIcon = 'alert-circle';
    statusText = 'Erreur de synchronisation';
  } else if (isSyncing) {
    statusColor = colors.primary;
    statusIcon = 'sync';
    statusText = 'Synchronisation en cours...';
  } else if (pendingConflicts.length > 0) {
    statusColor = colors.warning;
    statusIcon = 'alert-circle';
    statusText = `${pendingConflicts.length} conflit(s) à résoudre`;
  } else if (pendingOperationsCount > 0) {
    statusColor = colors.info;
    statusIcon = 'sync-alert';
    statusText = `${pendingOperationsCount} opération(s) en attente`;
  } else if (isSyncNeeded()) {
    statusColor = colors.secondary;
    statusIcon = 'sync-off';
    statusText = 'Synchronisation nécessaire';
  }

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else if (!isSyncing && (isSyncNeeded() || pendingOperationsCount > 0)) {
      triggerSync();
    }
  };

  const renderLastSyncTime = () => {
    if (!lastSyncTimestamp) return 'Jamais synchronisé';
    return `Dernière sync: ${formatLastSyncTime()}`;
  };

  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={handlePress}
      disabled={isSyncing}
    >
      <View style={styles.iconContainer}>
        {isSyncing ? (
          <ActivityIndicator size={iconSize} color={statusColor} />
        ) : (
          <Icon name={statusIcon} size={iconSize} color={statusColor} />
        )}
      </View>
      
      {showText && (
        <View style={styles.textContainer}>
          <Text style={[styles.statusText, { color: statusColor, fontSize: textSize }]}>
            {statusText}
          </Text>
          {!isSyncing && (
            <Text style={[styles.syncTimeText, { fontSize: textSize - 2 }]}>
              {renderLastSyncTime()}
            </Text>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  iconContainer: {
    marginRight: 8,
  },
  textContainer: {
    flex: 1,
  },
  statusText: {
    fontWeight: '500',
  },
  syncTimeText: {
    marginTop: 2,
    color: '#666',
  },
});

export default SyncStatusIndicator; 