import type { Database as WatermelonDatabase } from '@nozbe/watermelondb';
import { Database } from '../database';

/**
 * Adapte notre base de données personnalisée pour être utilisée avec les fonctions 
 * qui attendent une base de données WatermelonDB.
 * 
 * @param db Notre base de données personnalisée
 * @returns Un objet qui imite l'interface d'une base de données WatermelonDB
 */
export function adaptDatabaseForSync(db: Database): WatermelonDatabase {
  // Créer un proxy qui intercepte les appels à la base de données
  return {
    // Propriétés minimum requises par synchronize()
    get schema() { return db.schema || {}; },
    get collections() { return db.collections || {}; },
    
    // Implémentation de write qui appelle notre méthode write
    async write<T>(callback: any): Promise<T> {
      return await db.write(callback) as unknown as T;
    },
    
    // Autres méthodes qui pourraient être nécessaires
    get<T>(collection: string) {
      return db.get<T>(collection) as any;
    },
    
    batch() { 
      console.warn('Database.batch() called but not fully implemented');
      return Promise.resolve(); 
    },
    
    // Ajouter d'autres méthodes selon les besoins du synchronize
    withChanges() {
      console.warn('Database.withChanges() called but not fully implemented');
      return { write: async () => Promise.resolve() } as any;
    },
    
    // Méthode unsafeResetDatabase 
    async unsafeResetDatabase() {
      return await db.unsafeResetDatabase();
    }
  } as unknown as WatermelonDatabase;
} 