/**
 * Système d'émission d'événements simple pour l'application
 */

import { AppEventEmitter } from './AppEventEmitter';

export type EventCallback = (...args: any[]) => void;

export interface Subscription {
  off: () => void;
}

export class EventEmitterClass {
  private appEventEmitter: AppEventEmitter;

  constructor() {
    this.appEventEmitter = new AppEventEmitter();
  }

  /**
   * S'abonne à un événement
   * @param eventName Nom de l'événement
   * @param callback Fonction à appeler
   * @returns Un objet avec une méthode off() pour se désabonner
   */
  on(eventName: string, callback: EventCallback): Subscription {
    this.appEventEmitter.on(eventName, callback);
    return {
      off: () => this.off(eventName, callback)
    };
  }

  /**
   * Se désabonne d'un événement
   * @param eventName Nom de l'événement
   * @param callback Fonction à désabonner
   */
  off(eventName: string, callback: EventCallback): void {
    this.appEventEmitter.off(eventName, callback);
  }

  /**
   * Émet un événement avec les arguments donnés
   * @param eventName Nom de l'événement
   * @param args Arguments à passer au callback
   */
  emit(eventName: string, ...args: any[]): void {
    this.appEventEmitter.emit(eventName, ...args);
  }

  /**
   * S'abonne à un événement une seule fois
   * @param eventName Nom de l'événement
   * @param callback Fonction à appeler
   * @returns Un objet avec une méthode off() pour se désabonner
   */
  once(eventName: string, callback: EventCallback): Subscription {
    const onceCallback = (...args: any[]) => {
      this.off(eventName, onceCallback);
      callback(...args);
    };
    
    this.appEventEmitter.on(eventName, onceCallback);
    return {
      off: () => this.off(eventName, onceCallback)
    };
  }

  /**
   * Supprimer tous les handlers pour un événement spécifique
   * @param eventName Nom de l'événement
   */
  removeAllListeners(eventName?: string): void {
    if (eventName) {
      this.appEventEmitter.removeAllListeners(eventName);
    } else {
      this.appEventEmitter.removeAllListeners();
    }
  }

  /**
   * Obtenir la liste des handlers pour un événement
   * @param eventName Nom de l'événement
   */
  listeners(eventName: string): EventCallback[] {
    return this.appEventEmitter.listeners(eventName);
  }

  /**
   * Vérifier si un événement a des handlers
   * @param eventName Nom de l'événement
   */
  hasListeners(eventName: string): boolean {
    return this.appEventEmitter.hasListeners(eventName);
  }
}

// Créer une instance unique de l'EventEmitter
const eventEmitter = new EventEmitterClass();

// Exporter l'instance et la classe
export { eventEmitter as EventEmitter };
export default eventEmitter;