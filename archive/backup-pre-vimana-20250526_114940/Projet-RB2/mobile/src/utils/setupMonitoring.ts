/**
 * Configuration et initialisation du monitoring API au démarrage de l'application
 */

import { Platform } from 'react-native'
import { apiMonitoring, MonitoringConfig } from './apiMonitoring'
import openTelemetry, { TelemetryConfig, SpanStatusCode } from './openTelemetry'
import Config from '../config/environment'

// Déclaration des méthodes manquantes dans l'interface ApiMonitoring
declare module './apiMonitoring' {
  interface ApiMonitoring {
    getInstance(): ApiMonitoring;
    addCustomMetric(name: string, value: number, attributes: Record<string, any>): void;
    getPerformanceStats(): Record<string, any>;
    flushMetrics?(): Promise<boolean>;
    shutdown?(): void;
  }
}

// Récupérer les configurations depuis l'environnement ou utiliser les valeurs par défaut
const MONITORING_ENDPOINT = Config.API_MONITORING_ENDPOINT || '';
const MONITORING_ENABLED = Config.API_MONITORING_ENABLED !== 'false';
const MONITORING_SAMPLE_RATE = parseFloat(Config.API_MONITORING_SAMPLE_RATE || '0.1');
const MONITORING_FLUSH_INTERVAL = parseInt(Config.API_MONITORING_FLUSH_INTERVAL || '30000', 10);
const MONITORING_DEBUG = Config.NODE_ENV !== 'production';

// Configuration selon l'environnement
const getMonitoringConfig = (): MonitoringConfig => {
  const isProduction = Config.NODE_ENV  === 'production';
  const isDevelopment = Config.NODE_ENV  === 'development';
  const isTest = Config.NODE_ENV  === 'test';

  // Configuration de base
  const config: MonitoringConfig = {
    enabled: MONITORING_ENABLED,
    sampleRate: MONITORING_SAMPLE_RATE,
    flushInterval: MONITORING_FLUSH_INTERVAL,
    maxQueueSize: 100,
    reportErrors: true,
    serverEndpoint: MONITORING_ENDPOINT,
    debugMode: MONITORING_DEBUG
  }

  // Ajustements selon l'environnement
  if(isProduction) {
    // En production, être plus économe sur les ressources
    config.sampleRate = 0.05; // 5% des requêtes
    config.flushInterval = 60000; // 1 minute
    config.debugMode = false;
  } else if(isDevelopment) {
    // En développement, capturer plus de données
    config.sampleRate = 1.0; // 100% des requêtes
    config.flushInterval = 10000; // 10 secondes
    config.debugMode = true;
  } else if(isTest) {
    // En test, capturer toutes les données mais ne pas envoyer
    config.sampleRate = 1.0;
    config.serverEndpoint = ''; // Ne pas envoyer
    config.debugMode = true;
  }

  return config;
}

// Configuration OpenTelemetry selon l'environnement
const getTelemetryConfig = (): TelemetryConfig => {
  const isProduction = Config.NODE_ENV  === 'production';
  const isDevelopment = Config.NODE_ENV  === 'development';
  const isTest = Config.NODE_ENV  === 'test';

  // Configuration de base
  const config: TelemetryConfig = {
    enabled: MONITORING_ENABLED,
    endpoint: MONITORING_ENDPOINT,
    serviceName: 'mobile-app',
    serviceVersion: Config.APP_VERSION || '1.0.0',
    environment: Config.NODE_ENV || 'development',
    samplingRate: MONITORING_SAMPLE_RATE,
    batchSize: 30,
    batchIntervalMs: MONITORING_FLUSH_INTERVAL,
    bufferMaxSize: 100,
    debugMode: MONITORING_DEBUG,
    resource: {
      attributes: {
        'service.name': 'mobile-app',
        'service.version': Config.APP_VERSION || '1.0.0',
        'telemetry.sdk.name': 'opentelemetry',
        'telemetry.sdk.language': 'javascript',
        'telemetry.sdk.version': '1.0.0',
      }
    }
  }

  // Ajustements selon l'environnement
  if(isProduction) {
    config.samplingRate = 0.05;
    config.batchIntervalMs = 60000;
    config.debugMode = false;
  } else if(isDevelopment) {
    config.samplingRate = 1.0;
    config.batchIntervalMs = 10000;
    config.debugMode = true;
  } else if(isTest) {
    config.samplingRate = 1.0;
    config.endpoint = '';
    config.debugMode = true;
  }

  return config;
}

/**
 * Initialise le monitoring de l'API
 */
export function setupApiMonitoring(): void {
  const monitoringConfig = getMonitoringConfig()
  const telemetryConfig = getTelemetryConfig()
  
  // Utiliser les méthodes directement disponibles sur apiMonitoring
  // Note: apiMonitoring.addCustomMetric existe déjà d'après le code source
  
  // Initialiser OpenTelemetry avec la méthode init()
  openTelemetry.init(telemetryConfig);
  
  // Enregistrer la version et l'environnement de l'application
  apiMonitoring.addCustomMetric('app.start', 1, {
    version: Config.APP_VERSION || '1.0.0',
    environment: Config.NODE_ENV || 'development',
    platform: Platform.OS,
    platformVersion: Platform.Version,
    timestamp: Date.now()
  });

  // Enregistrer également l'information dans OpenTelemetry
  openTelemetry.recordMetric('app.start', 1, {
    version: Config.APP_VERSION || '1.0.0',
    environment: Config.NODE_ENV || 'development',
    platform: Platform.OS,
    platformVersion: String(Platform.Version)
  });
  
  if(monitoringConfig.debugMode) {
    console.log('[API Monitoring] Initialized with config:', {
      enabled: monitoringConfig.enabled,
      sampleRate: `${monitoringConfig.sampleRate * 100}%`,
      flushInterval: `${monitoringConfig.flushInterval / 1000}s`,
      endpoint: monitoringConfig.serverEndpoint || 'None (debug mode)',
      debug: monitoringConfig.debugMode
    });

    console.log('[OpenTelemetry] Initialized with config:', {
      enabled: telemetryConfig.enabled,
      samplingRate: `${telemetryConfig.samplingRate * 100}%`,
      batchInterval: `${telemetryConfig.batchIntervalMs / 1000}s`,
      endpoint: telemetryConfig.endpoint || 'None (debug mode)',
      debug: telemetryConfig.debugMode
    });
  }
}

/**
 * Nettoie les ressources de monitoring à la fermeture de l'application
 */
export function teardownApiMonitoring(): void {
  // Utiliser la méthode shutdown si elle existe
  if (typeof apiMonitoring.shutdown === 'function') {
    apiMonitoring.shutdown();
  }
  
  // Utiliser la méthode shutdown() pour OpenTelemetry
  openTelemetry.shutdown();
}

// Exporter un objet unique pour faciliter l'utilisation
export const monitoring = {
  setup: setupApiMonitoring,
  teardown: teardownApiMonitoring,
  
  // Expose certaines fonctionnalités de monitoring directement
  getStats: () => apiMonitoring.getPerformanceStats(),
  
  flush: async () => {
    // Utiliser flushMetrics si disponible, sinon essayer d'autres méthodes
    if (typeof apiMonitoring.flushMetrics === 'function') {
      await apiMonitoring.flushMetrics();
    }
    await openTelemetry.flush();
  },
  
  track: (name: string, value: number, attrs: Record<string, any> = {}) => {
    apiMonitoring.addCustomMetric(name, value, attrs);
    
    openTelemetry.recordMetric(name, value, 
      Object.entries(attrs).reduce((acc, [k, v]) => {
        acc[k] = String(v);
        return acc;
      }, {} as Record<string, string>)
    );
  },

  // Fonctionnalités OpenTelemetry
  trace: async<T>(name: string, operation: () => Promise<T>, attributes: Record<string, any> = {}): Promise<T> => {
    const span = openTelemetry.createRootSpan(name, attributes);
    try {
      const result = await operation();
      openTelemetry.endSpan(span);
      return result;
    } catch(error) {
      openTelemetry.endSpan(span, SpanStatusCode.ERROR, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }
} 