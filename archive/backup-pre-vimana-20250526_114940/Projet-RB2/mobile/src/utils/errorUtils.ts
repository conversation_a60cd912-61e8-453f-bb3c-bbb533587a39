/**
 * Utilitaires pour manipuler les erreurs de manière sécurisée au niveau des types
 */

/**
 * Extrait un message d'erreur d'un objet d'erreur de manière sécurisée
 * @param error Objet d'erreur de type quelconque
 * @param defaultMessage Message par défaut à utiliser si aucun message n'est trouvé
 * @returns Le message d'erreur extrait ou le message par défaut
 */
export function getErrorMessage(error: unknown, defaultMessage: string = 'Une erreur s\'est produite'): string {
  // Vérifier si c'est une Error avec un message
  if (error instanceof Error) {
    return error.message;
  }
  
  // Vérifier si c'est un objet avec un message
  if (typeof error === 'object' && error !== null && 'message' in error && typeof error.message === 'string') {
    return error.message;
  }
  
  // Vérifier si c'est un objet avec une propriété data qui contient un message
  if (
    typeof error === 'object' && 
    error !== null && 
    'data' in error && 
    typeof error.data === 'object' && 
    error.data !== null && 
    'message' in error.data && 
    typeof error.data.message === 'string'
  ) {
    return error.data.message;
  }
  
  // Si c'est une chaîne, la retourner directement
  if (typeof error === 'string') {
    return error;
  }
  
  // Sinon, retourner le message par défaut
  return defaultMessage;
}

/**
 * Affiche une erreur dans la console de manière formatée
 * @param context Contexte de l'erreur (où elle s'est produite)
 * @param error L'erreur elle-même
 */
export function logError(context: string, error: unknown): void {
  console.error(`[ERROR] ${context}:`, error);
  // On pourrait aussi envoyer l'erreur à un service de monitoring ici
}

/**
 * Détermine si l'erreur peut être réessayée (connexion réseau, etc.)
 * @param error L'erreur à analyser
 * @returns true si l'erreur peut être réessayée
 */
export function isRetryableError(error: unknown): boolean {
  // Si c'est une NetworkError ou une TimeoutError, on peut réessayer
  if (
    (error instanceof Error && error.name === 'NetworkError') ||
    (error instanceof Error && error.name === 'TimeoutError') ||
    (error instanceof Error && error.name === 'OfflineError')
  ) {
    return true;
  }
  
  // Si c'est une ServerError avec un code 5xx, on peut réessayer
  if (
    error instanceof Error && 
    error.name === 'ServerError' && 
    'statusCode' in error && 
    typeof error.statusCode === 'number' && 
    error.statusCode >= 500 && 
    error.statusCode < 600
  ) {
    return true;
  }
  
  return false;
} 