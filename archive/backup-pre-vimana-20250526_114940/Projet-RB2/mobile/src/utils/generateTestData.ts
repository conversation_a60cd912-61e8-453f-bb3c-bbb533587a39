/**
 * Utilitaire pour générer des données de test pour la base de données locale
 */

import { database, resetDatabase } from '../database';
import { retreatsMock } from '../mocks/api/data/retreats';
import { bookingsMock } from '../mocks/api/data/bookings';
import { usersMock } from '../mocks/api/data/users';
import { notificationsMock } from '../mocks/api/data/notifications';
import { Collection, Model } from '@nozbe/watermelondb';

// Types pour les données mock
interface MockData {
  id: string;
  createdAt: string | number | Date;
  updatedAt: string | number | Date;
  [key: string]: any;
}

interface UserMock extends MockData {
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  avatarUrl: string;
  role: string;
}

interface RetreatMock extends MockData {
  name: string;
  description: string;
  location: string;
  startDate: string | Date;
  endDate: string | Date;
  price: number;
  capacity: number;
  imageUrl: string;
}

interface BookingMock extends MockData {
  userId: string;
  retreatId: string;
  status: string;
  paymentStatus: string;
  amount: number;
}

interface NotificationMock extends MockData {
  userId: string;
  type: string;
  message: string;
  read: boolean;
}

/**
 * Récupère les collections de données
 */
const getCollections = () => {
  const retreatsCollection = database.get<Model>('retreats') as Collection<Model>;
  const bookingsCollection = database.get<Model>('bookings') as Collection<Model>;
  const usersCollection = database.get<Model>('users') as Collection<Model>;
  const notificationsCollection = database.get<Model>('notifications') as Collection<Model>;
  
  return {
    retreatsCollection,
    bookingsCollection,
    usersCollection,
    notificationsCollection
  };
};

/**
 * Génère des données de test pour la base de données
 */
export async function generateTestData() {
  try {
    console.log('🧪 Génération des données de test...');
    
    // Réinitialiser la base de données d'abord
    await resetDatabase();
    
    const {
      retreatsCollection,
      bookingsCollection,
      usersCollection,
      notificationsCollection
    } = getCollections();
    
    // Générer les données en batch
    await database.write(async () => {
      // Insérer les utilisateurs
      for (const user of usersMock) {
        const userData = {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          phone: user.phone,
          avatarUrl: user.avatarUrl,
          role: user.role,
          createdAt: new Date(user.createdAt),
          updatedAt: new Date(user.updatedAt)
        };
        await usersCollection.create(userData as any);
      }
      
      // Insérer les retraites
      for (const retreat of retreatsMock) {
        const retreatData = {
          id: retreat.id,
          title: retreat.title,
          description: retreat.description,
          location: retreat.location,
          startDate: new Date(retreat.startDate),
          endDate: new Date(retreat.endDate),
          price: retreat.price,
          currency: retreat.currency,
          maxParticipants: retreat.maxParticipants,
          currentParticipants: retreat.currentParticipants,
          imageUrl: retreat.imageUrl,
          tags: JSON.stringify(retreat.tags),
          amenities: JSON.stringify(retreat.amenities),
          hostId: retreat.hostId,
          createdAt: new Date(retreat.createdAt),
          updatedAt: new Date(retreat.updatedAt)
        };
        await retreatsCollection.create(retreatData as any);
      }
      
      // Insérer les réservations
      for (const booking of bookingsMock) {
        if (booking.userId === 'current-user') {  // Seulement les réservations de l'utilisateur courant
          const bookingData = {
            id: booking.id,
            retreatId: booking.retreatId,
            userId: booking.userId,
            status: booking.status,
            participants: booking.participants,
            totalPrice: booking.totalPrice,
            currency: booking.currency,
            date: new Date(booking.date),
            specialRequests: booking.specialRequests,
            paymentMethod: booking.paymentMethod,
            paymentStatus: booking.paymentStatus,
            createdAt: new Date(booking.createdAt),
            updatedAt: new Date(booking.updatedAt)
          };
          await bookingsCollection.create(bookingData as any);
        }
      }
      
      // Insérer les notifications
      for (const notification of notificationsMock) {
        if (notification.userId === 'current-user') {  // Seulement les notifications de l'utilisateur courant
          const notificationData = {
            id: notification.id,
            userId: notification.userId,
            type: notification.type,
            title: notification.title,
            message: notification.message,
            read: notification.read,
            data: notification.data ? JSON.stringify(notification.data) : undefined,
            createdAt: new Date(notification.createdAt),
            updatedAt: new Date(notification.updatedAt)
          };
          await notificationsCollection.create(notificationData as any);
        }
      }
    });
    
    console.log('✅ Données de test générées avec succès!');
    return true;
  } catch (error) {
    console.error('❌ Erreur lors de la génération des données de test:', error);
    return false;
  }
}

/**
 * Vérifier si des données existent déjà dans la base de données
 */
export async function hasData(): Promise<boolean> {
  try {
    const { retreatsCollection } = getCollections();
    const retreats = await retreatsCollection.query().fetch();
    return retreats.length > 0;
  } catch (error) {
    console.error('❌ Erreur lors de la vérification des données:', error);
    return false;
  }
} 