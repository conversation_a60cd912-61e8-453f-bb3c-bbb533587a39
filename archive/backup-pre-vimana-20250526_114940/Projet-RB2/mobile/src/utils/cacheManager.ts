/**
 * Gestionnaire de cache à plusieurs niveaux;
 * - Cache en mémoire pour les données fréquentes;
 * - Cache persistant pour les données importantes
 * - Gestion du TTL et de l'invalidation sélective;
 */

import AsyncStorage from '@react-native-async-storage/async-storage'
import { Platform } from 'react-native'
import { EventEmitter } from './eventEmitter'
import * as LRU from 'lru-cache'
import { MMKV } from 'react-native-mmkv'

// Configuration du cache;
export const CACHE_CONFIG = {
  PREFIX: '@RB2:cache:',
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes en ms
  MAX_MEMORY_ITEMS: 100, // Nombre maximum d'éléments en mémoire;
  MAX_MEMORY_SIZE: 5 * 1024 * 1024, // 5 MB max en mémoire;
  MAX_PERSISTENT_AGE: 7 * 24 * 60 * 60 * 1000, // 7 jours en ms;
}

// Types d'expiration;
export enum CacheExpiry {
  SHORT = 60 * 1000, // 1 minute
  MEDIUM = 5 * 60 * 1000, // 5 minutes;
  LONG = 30 * 60 * 1000, // 30 minutes;
  VERY_LONG = 24 * 60 * 60 * 1000, // 24 heures;
  DAY = 24 * 60 * 60 * 1000, // 1 jour;
  WEEK = 7 * 24 * 60 * 60 * 1000, // 1 semaine;
  NEVER = 0, // Pas d'expiration;
}

// Types pour les niveaux de priorité
export enum CachePriority {
  LOW = 'low', // Peut être supprimé facilement
  MEDIUM = 'medium', // Priorité normale;
  HIGH = 'high', // Données importantes
  CRITICAL = 'critical', // Données critiques pour le fonctionnement offline;
}

// Interface pour les entrées du cache;
export interface CacheEntry<T>  {
  value: T;
  timestamp: number;
  expiry: number; // 0 = pas d'expiration;
  priority: CachePriority;
  tags: string[];
}

// Événements du cache;
export const CACHE_EVENTS = {
  ITEM_ADDED: 'cache.itemAdded',
  ITEM_REMOVED: 'cache.itemRemoved',
  ITEM_EXPIRED: 'cache.itemExpired',
  CACHE_CLEARED: 'cache.cleared',
  TAGS_INVALIDATED: 'cache.tagsInvalidated'
}

// Stratégies de préchargement
export enum PreloadStrategy {
  EAGER = 'eager', // Tout précharger
  LAZY = 'lazy', // Précharger à la demande;
  BACKGROUND = 'background', // Précharger en arrière-plan;
}

/**
 * Gestionnaire de cache multi-niveaux;
 */
export class CacheManager {
  private static instance: CacheManager
  
  // Cache en mémoire (LRU)
  private memoryCache: LRU.LRUCache<string, CacheEntry<any>>;
  
  // Cache persistant rapide (MMKV)
  private fastStorage: MMKV;
  
  // Pour stocker les clés par tag (en mémoire)
  private tagToKeys: Map<string, Set<string>> = new Map()
  
  // Indique si le préchargement a été effectué
  private preloaded: boolean = false;

  private constructor() {
    // Initialiser le cache mémoire LRU;
    this.memoryCache = new LRU.LRUCache({
      max: CACHE_CONFIG.MAX_MEMORY_ITEMS,
      maxSize: CACHE_CONFIG.MAX_MEMORY_SIZE,
      sizeCalculation: (value: any, key: string) => {
        // Estimation de la taille en mémoire (en octets)
        return JSON.stringify(value).length * 2; // * 2 car UTF-16;
      },
      ttl: CACHE_CONFIG.DEFAULT_TTL,
      allowStale: false,
      updateAgeOnGet: true
    })
    
    // Initialiser le stockage MMKV pour un cache persistent rapide;
    this.fastStorage = new MMKV({
      id: 'cache-storage',
      encryptionKey: 'rb2-cache-encryption-key', // Idéalement, générer et stocker cette clé de manière sécurisée;
    })
  }

  /**
   * Obtenir l'instance unique du gestionnaire de cache;
   */
  public static getInstance(): CacheManager {
    if(!CacheManager.instance) {
      CacheManager.instance = new CacheManager()
    }
    return CacheManager.instance;
  }

  /**
   * Initialiser le cache et précharger les données si nécessaire;
   */
  public async initialize(preloadStrategy: PreloadStrategy = PreloadStrategy.LAZY): Promise<void> {
    // Charger la table d'indexation des tags;
    await this.loadTagIndexes()
    
    // Précharger selon la stratégie;
    if(preloadStrategy === PreloadStrategy.EAGER && !this.preloaded) {
      await this.preloadFromStorage()
    }
  }

  /**
   * Stocker une valeur dans le cache;
   */
  public async set<T>(
    key: string,
    value: T,
    options: {
      ttl?: number | CacheExpiry;
      priority?: CachePriority;
      persistent?: boolean;
      tags?: string[];
    } = {}
  ): Promise<void> {
    const {
      ttl = CACHE_CONFIG.DEFAULT_TTL,
      priority = CachePriority.MEDIUM,
      persistent = false,
      tags = []
    } = options;

    // Créer l'entrée de cache;
    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      expiry: ttl === 0 ? 0 : Date.now() + (ttl as number),
      priority,
      tags
    };

    // Stocker en mémoire pour un accès rapide;
    this.memoryCache.set(key, entry);

    // Ajouter aux index de tags;
    if(tags.length > 0) {
      this.addKeyToTags(key, tags);
    }
    
    // Si persistant, stocker aussi dans le stockage;
    if(persistent) {
      await this.persistToStorage(key, entry);
    }
    
    // Émettre un événement;
    EventEmitter.emit(CACHE_EVENTS.ITEM_ADDED, { key, tags, persistent });
  }

  /**
   * Récupérer une valeur du cache;
   */
  public async get<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    // Vérifier d'abord en mémoire (plus rapide)
    const memoryEntry = this.memoryCache.get(key) as CacheEntry<T> | undefined;
    
    if(memoryEntry) {
      // Vérifier si l'entrée est expirée;
      if (memoryEntry.expiry !== 0 && memoryEntry.expiry < Date.now()) {
        this.memoryCache.delete(key);
        EventEmitter.emit(CACHE_EVENTS.ITEM_EXPIRED, { key });
        return defaultValue;
      }
      
      return memoryEntry.value;
    }
    
    // Si pas en mémoire, essayer dans le stockage persistant;
    try {
      // D'abord le stockage rapide MMKV;
      const fastValue = this.fastStorage.getString(key);
      if(fastValue) {
        const entry = JSON.parse(fastValue) as CacheEntry<T>;
        
        // Vérifier si l'entrée est expirée;
        if (entry.expiry !== 0 && entry.expiry < Date.now()) {
          this.fastStorage.delete(key);
          EventEmitter.emit(CACHE_EVENTS.ITEM_EXPIRED, { key });
          return defaultValue;
        }
        
        // Stocker en mémoire pour les accès futurs;
        this.memoryCache.set(key, entry);
        return entry.value;
      }
      
      // Ensuite AsyncStorage (plus lent mais plus fiable)
      const asyncValue = await AsyncStorage.getItem(`${CACHE_CONFIG.PREFIX}${key}`);
      if(asyncValue) {
        const entry = JSON.parse(asyncValue) as CacheEntry<T>;
        
        // Vérifier si l'entrée est expirée;
        if (entry.expiry !== 0 && entry.expiry < Date.now()) {
          await AsyncStorage.removeItem(`${CACHE_CONFIG.PREFIX}${key}`);
          EventEmitter.emit(CACHE_EVENTS.ITEM_EXPIRED, { key });
          return defaultValue;
        }
        
        // Stocker en mémoire pour les accès futurs;
        this.memoryCache.set(key, entry);
        
        // Également en stockage rapide;
        this.fastStorage.set(key, asyncValue);
        
        return entry.value;
      }
    } catch(error) {
      console.error('Erreur lors de la récupération dans le cache', error);
    }
    
    return defaultValue;
  }

  /**
   * Obtenir les statistiques d'utilisation du cache
   */
  public getStats(): {
    memoryItemsCount: number;
    persistentItemsCount: number;
    tagCount: number;
  } {
    return {
      memoryItemsCount: this.memoryCache.size,
      persistentItemsCount: this.fastStorage.getAllKeys().length,
      tagCount: this.tagToKeys.size
    };
  }

  /**
   * Vérifier si une clé existe dans le cache
   */
  public async has(key: string): Promise<boolean> {
    // Vérifier d'abord en mémoire
    if (this.memoryCache.has(key)) {
      const entry = this.memoryCache.get(key);
      
      // Vérifier si l'entrée est expirée
      if (entry && entry.expiry !== 0 && entry.expiry < Date.now()) {
        this.memoryCache.delete(key);
        EventEmitter.emit(CACHE_EVENTS.ITEM_EXPIRED, { key });
        return false;
      }
      
      return true;
    }
    
    // Si pas en mémoire, essayer dans le stockage persistant
    try {
      // D'abord le stockage rapide MMKV
      if (this.fastStorage.contains(key)) {
        const value = this.fastStorage.getString(key);
        if (value) {
          const entry = JSON.parse(value);
          
          // Vérifier si l'entrée est expirée
          if (entry.expiry !== 0 && entry.expiry < Date.now()) {
            this.fastStorage.delete(key);
            EventEmitter.emit(CACHE_EVENTS.ITEM_EXPIRED, { key });
            return false;
          }
          
          // Mettre en cache mémoire pour la prochaine fois
          this.memoryCache.set(key, entry);
          return true;
        }
      }
      
      // Ensuite AsyncStorage
      const value = await AsyncStorage.getItem(`${CACHE_CONFIG.PREFIX}${key}`);
      if(value) {
        const entry = JSON.parse(value);
        
        // Vérifier si l'entrée est expirée
        if (entry.expiry !== 0 && entry.expiry < Date.now()) {
          await AsyncStorage.removeItem(`${CACHE_CONFIG.PREFIX}${key}`);
          EventEmitter.emit(CACHE_EVENTS.ITEM_EXPIRED, { key });
          return false;
        }
        
        // Mettre en cache mémoire et rapide pour la prochaine fois
        this.memoryCache.set(key, entry);
        this.fastStorage.set(key, value);
        return true;
      }
    } catch(error) {
      console.warn('Erreur lors de la vérification du cache', error);
    }
    
    return false;
  }

  /**
   * Supprimer une entrée du cache
   */
  public async remove(key: string): Promise<boolean> {
    let found = false;
    
    // Supprimer de la mémoire
    if (this.memoryCache.has(key)) {
      const entry = this.memoryCache.get(key);
      if (entry && entry.tags && entry.tags.length > 0) {
        this.removeKeyFromTags(key, entry.tags);
      }
      this.memoryCache.delete(key);
      found = true;
    }
    
    // Supprimer du stockage rapide
    if (this.fastStorage.contains(key)) {
      this.fastStorage.delete(key);
      found = true;
    }
    
    // Supprimer d'AsyncStorage
    try {
      await AsyncStorage.removeItem(`${CACHE_CONFIG.PREFIX}${key}`);
      found = true;
    } catch(error) {
      console.warn('Erreur lors de la suppression du cache', error);
    }
    
    if(found) {
      EventEmitter.emit(CACHE_EVENTS.ITEM_REMOVED, { key });
    }
    
    return found;
  }

  /**
   * Invalider toutes les entrées associées à certains tags
   */
  public async invalidateByTags(tags: string[]): Promise<number> {
    let count = 0;
    
    // Obtenir toutes les clés à invalider
    const keysToInvalidate = new Set<string>();
    
    for (const tag of tags) {
      const keys = this.tagToKeys.get(tag);
      if (keys) {
        for (const key of Array.from(keys)) {
          keysToInvalidate.add(key);
        }
      }
    }
    
    // Invalider chaque clé
    for (const key of Array.from(keysToInvalidate)) {
      if (await this.remove(key)) {
        count++;
      }
    }
    
    if(count > 0) {
      EventEmitter.emit(CACHE_EVENTS.TAGS_INVALIDATED, { tags, count });
    }
    
    return count;
  }

  /**
   * Vider complètement le cache
   */
  public async clear(): Promise<void> {
    // Vider le cache mémoire
    this.memoryCache.clear();
    
    // Vider le stockage rapide
    const fastKeys = this.fastStorage.getAllKeys();
    for (const key of fastKeys) {
      this.fastStorage.delete(key);
    }
    
    // Vider AsyncStorage (seulement les entrées avec notre préfixe)
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(k => k.startsWith(CACHE_CONFIG.PREFIX));
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }
    } catch (error) {
      console.warn('Erreur lors du nettoyage du cache AsyncStorage', error);
    }
    
    // Réinitialiser les index de tags
    this.tagToKeys.clear();
    
    // Événement
    EventEmitter.emit(CACHE_EVENTS.CACHE_CLEARED, {});
  }

  /**
   * Nettoyer les entrées expirées et ajuster les priorités
   */
  public async cleanup(): Promise<number> {
    let removed = 0;
    
    // Nettoyer en mémoire
    for (const key of Array.from(this.memoryCache.keys())) {
      const entry = this.memoryCache.get(key);
      if (entry && entry.expiry !== 0 && entry.expiry < Date.now()) {
        if (entry.tags && entry.tags.length > 0) {
          this.removeKeyFromTags(key, entry.tags);
        }
        this.memoryCache.delete(key);
        removed++;
        EventEmitter.emit(CACHE_EVENTS.ITEM_EXPIRED, { key });
      }
    }
    
    // Nettoyer le stockage rapide
    try {
      const fastKeys = this.fastStorage.getAllKeys();
      for (const key of fastKeys) {
        const value = this.fastStorage.getString(key);
        if (value) {
          try {
            const entry = JSON.parse(value);
            if (entry.expiry !== 0 && entry.expiry < Date.now()) {
              this.fastStorage.delete(key);
              removed++;
              EventEmitter.emit(CACHE_EVENTS.ITEM_EXPIRED, { key });
            }
          } catch (e) {
            // Ignorer les entrées non-JSON
            this.fastStorage.delete(key);
          }
        }
      }
    } catch (e) {
      console.warn('Erreur lors du nettoyage du cache rapide', e);
    }
    
    // Nettoyer AsyncStorage
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(k => k.startsWith(CACHE_CONFIG.PREFIX));
      
      for (const fullKey of cacheKeys) {
        try {
          const value = await AsyncStorage.getItem(fullKey);
          if (value) {
            const entry = JSON.parse(value);
            if (entry.expiry !== 0 && entry.expiry < Date.now()) {
              const key = fullKey.substring(CACHE_CONFIG.PREFIX.length);
              await AsyncStorage.removeItem(fullKey);
              removed++;
              EventEmitter.emit(CACHE_EVENTS.ITEM_EXPIRED, { key });
            }
          }
        } catch (e) {
          // Ignorer les entrées non-JSON
          await AsyncStorage.removeItem(fullKey);
        }
      }
    } catch (e) {
      console.warn('Erreur lors du nettoyage du cache AsyncStorage', e);
    }
    
    return removed;
  }

  /**
   * Précharger le cache depuis le stockage persistant
   */
  private async preloadFromStorage(): Promise<void> {
    // D'abord charger depuis le stockage rapide
    try {
      const fastKeys = this.fastStorage.getAllKeys();
      for (const key of fastKeys) {
        if (!this.memoryCache.has(key)) {
          const value = this.fastStorage.getString(key);
          if (value) {
            try {
              const entry = JSON.parse(value);
              
              // Ne pas charger les entrées expirées
              if (entry.expiry === 0 || entry.expiry > Date.now()) {
                this.memoryCache.set(key, entry);
                
                // Ajouter aux index de tags
                if (entry.tags && entry.tags.length > 0) {
                  this.addKeyToTags(key, entry.tags);
                }
              } else {
                // Supprimer les entrées expirées
                this.fastStorage.delete(key);
              }
            } catch (e) {
              // Ignorer les entrées non-JSON
              this.fastStorage.delete(key);
            }
          }
        }
      }
    } catch (e) {
      console.warn('Erreur lors du préchargement du cache rapide', e);
    }
    
    this.preloaded = true;
  }

  /**
   * Persister une entrée dans le stockage
   */
  private async persistToStorage<T>(key: string, entry: CacheEntry<T>): Promise<void> {
    try {
      // D'abord le stockage rapide
      const serialized = JSON.stringify(entry);
      this.fastStorage.set(key, serialized);
      
      // Ensuite AsyncStorage pour durabilité
      await AsyncStorage.setItem(`${CACHE_CONFIG.PREFIX}${key}`, serialized);
    } catch(error) {
      console.warn('Erreur lors de la persistance dans le cache', error);
    }
  }

  /**
   * Ajouter une clé aux index de tags
   */
  private addKeyToTags(key: string, tags: string[]): void {
    for(const tag of tags) {
      if (!this.tagToKeys.has(tag)) {
        this.tagToKeys.set(tag, new Set());
      }
      
      const keys = this.tagToKeys.get(tag);
      keys?.add(key);
    }
    
    // Sauvegarder les index
    this.saveTagIndexes();
  }

  /**
   * Supprimer une clé des index de tags
   */
  private removeKeyFromTags(key: string, tags: string[]): void {
    for(const tag of tags) {
      const keys = this.tagToKeys.get(tag);
      if (keys) {
        keys.delete(key);
        
        // Supprimer le tag s'il n'a plus de clés associées
        if (keys.size === 0) {
          this.tagToKeys.delete(tag);
        }
      }
    }
    
    // Sauvegarder les index
    this.saveTagIndexes();
  }

  /**
   * Sauvegarder les index de tags
   */
  private async saveTagIndexes(): Promise<void> {
    try {
      const tagIndexes: Record<string, string[]> = {};
      
      for (const [tag, keys] of Array.from(this.tagToKeys.entries())) {
        tagIndexes[tag] = Array.from(keys);
      }
      
      await AsyncStorage.setItem(`${CACHE_CONFIG.PREFIX}tagIndexes`, JSON.stringify(tagIndexes));
    } catch(error) {
      console.warn('Erreur lors de la sauvegarde des index de tags', error);
    }
  }

  /**
   * Charger les index de tags
   */
  private async loadTagIndexes(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(`${CACHE_CONFIG.PREFIX}tagIndexes`);
      
      if(data) {
        const tagIndexes = JSON.parse(data) as Record<string, string[]>;
        
        for (const [tag, keys] of Object.entries(tagIndexes)) {
          const keySet = new Set(keys);
          this.tagToKeys.set(tag, keySet);
        }
      }
    } catch(error) {
      console.warn('Erreur lors du chargement des index de tags', error);
    }
  }

  /**
   * Obtenir une entrée avec son état complet (métadonnées)
   */
  public async getEntryDetails<T>(key: string): Promise<CacheEntry<T> | undefined> {
    // Vérifier d'abord en mémoire
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key) as CacheEntry<T>;
    }
    
    // Ensuite le stockage rapide
    const fastValue = this.fastStorage.getString(key);
    if(fastValue) {
      return JSON.parse(fastValue) as CacheEntry<T>;
    }
    
    // Enfin AsyncStorage
    try {
      const value = await AsyncStorage.getItem(`${CACHE_CONFIG.PREFIX}${key}`);
      if(value) {
        return JSON.parse(value) as CacheEntry<T>;
      }
    } catch(error) {
      console.warn('Erreur lors de la récupération des détails', error);
    }
    
    return undefined;
  }

  /**
   * Mettre à jour le TTL d'une entrée
   */
  public async updateTTL(key: string, ttl: number): Promise<boolean> {
    // Chercher l'entrée
    const entry = await this.getEntryDetails<any>(key);
    
    if (entry) {
      // Mettre à jour l'expiration
      entry.expiry = ttl === 0 ? 0 : Date.now() + ttl;
      
      // Mettre à jour en mémoire
      this.memoryCache.set(key, entry);
      
      // Si entrée avec tags, c'est persistant
      if (entry.tags && entry.tags.length > 0) {
        await this.persistToStorage(key, entry);
      }
      
      return true;
    }
    
    return false;
  }
}

// Exporter une instance unique;
export const cacheManager = CacheManager.getInstance() 