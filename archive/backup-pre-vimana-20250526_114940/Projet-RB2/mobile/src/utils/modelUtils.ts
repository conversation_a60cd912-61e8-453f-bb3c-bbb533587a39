// Utilities for working with WatermelonDB models
import { Model } from '@nozbe/watermelondb';

/**
 * Update a model with new data
 * @param model The model to update
 * @param data The data to apply to the model
 */
export const updateModel = async (model: Model, data: Record<string, any>): Promise<void> => {
  await model.update((record: any) => {
    Object.assign(record, data);
  });
};

/**
 * Convert a model to a plain object
 * @param model The model to convert
 * @returns A plain object representation of the model
 */
export const modelToObject = (model: Model): Record<string, any> => {
  const obj: Record<string, any> = { id: model.id };
  
  // Add all properties that aren't functions
  Object.keys(model).forEach(key => {
    if (typeof (model as any)[key] !== 'function' && !key.startsWith('_')) {
      obj[key] = (model as any)[key];
    }
  });
  
  return obj;
}; 