/**
 * Classe de base pour la gestion des événements dans l'application
 */

type EventCallback = (...args: any[]) => void;

interface EventHandlers {
  [eventName: string]: EventCallback[];
}

export class AppEventEmitter {
  private handlers: EventHandlers = {};

  /**
   * S'abonner à un événement
   * @param eventName Nom de l'événement
   * @param callback Fonction à exécuter quand l'événement est émis
   */
  on(eventName: string, callback: EventCallback): void {
    if (!this.handlers[eventName]) {
      this.handlers[eventName] = [];
    }
    this.handlers[eventName].push(callback);
  }

  /**
   * Se désabonner d'un événement
   * @param eventName Nom de l'événement
   * @param callback Fonction à retirer
   */
  off(eventName: string, callback: EventCallback): void {
    if (!this.handlers[eventName]) return;
    
    const index = this.handlers[eventName].indexOf(callback);
    if (index !== -1) {
      this.handlers[eventName].splice(index, 1);
    }
    
    // Supprimer la liste si elle est vide
    if (this.handlers[eventName].length === 0) {
      delete this.handlers[eventName];
    }
  }

  /**
   * Émettre un événement
   * @param eventName Nom de l'événement
   * @param args Arguments à passer aux callbacks
   */
  emit(eventName: string, ...args: any[]): void {
    if (!this.handlers[eventName]) return;
    
    // Copier le tableau de handlers pour éviter les problèmes 
    // si un handler modifie la liste pendant l'itération
    const handlers = [...this.handlers[eventName]];
    
    for (const callback of handlers) {
      try {
        callback(...args);
      } catch (error) {
        console.error(`Erreur lors de l'exécution du callback pour l'événement ${eventName}:`, error);
      }
    }
  }

  /**
   * Supprimer tous les écouteurs d'un événement spécifique
   * ou tous les écouteurs si aucun événement n'est spécifié
   * @param eventName Nom de l'événement (optionnel)
   */
  removeAllListeners(eventName?: string): void {
    if (eventName) {
      delete this.handlers[eventName];
    } else {
      this.handlers = {};
    }
  }

  /**
   * Récupérer la liste des écouteurs pour un événement
   * @param eventName Nom de l'événement
   */
  listeners(eventName: string): EventCallback[] {
    return this.handlers[eventName] ? [...this.handlers[eventName]] : [];
  }

  /**
   * Vérifier si un événement a des écouteurs
   * @param eventName Nom de l'événement
   */
  hasListeners(eventName: string): boolean {
    return !!this.handlers[eventName] && this.handlers[eventName].length > 0;
  }
} 