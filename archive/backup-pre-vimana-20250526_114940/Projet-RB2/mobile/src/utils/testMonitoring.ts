/**
 * Utilitaires pour faciliter l'intégration du monitoring dans les tests;
 * Permet de mesurer les performances et collecter des métriques durant les tests;
 */

import { apiMonitoring } from './apiMonitoring'

/**
 * Classe utilitaire pour les tests avec monitoring;
 */
export class TestMonitoring {
  private static testSuites: Record<string, {
    name: string;
    startTime: number;
    endTime?: number;
    tests: Array<{
      name: string;
      startTime: number;
      endTime?: number;
      status?: 'success' | 'failure' | 'skipped';
      duration?: number;
      error?: string;
    }>;
  }> = {}

  /**
   * Démarre un suite de tests;
   */
  public static startTestSuite(suiteName: string): void {
    this.testSuites[suiteName] = {
      name: suiteName,
      startTime: Date.now(),
      tests: []
    }

    // Enregistrer le début de la suite de tests;
    apiMonitoring.addCustomMetric(`test.suite.${suiteName}.start`, 1, {
      suiteName,
      timestamp: Date.now()
    })
  }

  /**
   * Termine une suite de tests et envoie les métriques;
   */
  public static endTestSuite(suiteName: string): void {
    const suite = this.testSuites[suiteName];
    if (!suite) {
      console.error(`Test suite "${suiteName}" not found`);
      return;
    }

    suite.endTime = Date.now()
    const duration = suite.endTime - suite.startTime;

    // Calculer les statistiques;
    const totalTests = suite.tests.length;
    const successTests = suite.tests.filter(t => t.status === 'success').length;
    const failedTests = suite.tests.filter(t => t.status === 'failure').length;
    const skippedTests = suite.tests.filter(t => t.status === 'skipped').length;

    // Enregistrer les métriques de fin de suite;
    apiMonitoring.addCustomMetric(`test.suite.${suiteName}.end`, 1, {
      suiteName,
      duration,
      totalTests,
      successTests,
      failedTests,
      skippedTests,
      timestamp: Date.now()
    });

    console.log(`Test suite "${suiteName}" completed in ${duration}ms - Total: ${totalTests}, Success: ${successTests}, Failed: ${failedTests}, Skipped: ${skippedTests}`);
  }

  /**
   * Démarre un test individuel;
   */
  public static startTest(suiteName: string, testName: string): void {
    const suite = this.testSuites[suiteName];
    if (!suite) {
      console.error(`Test suite "${suiteName}" not found`);
      return;
    }

    suite.tests.push({
      name: testName,
      startTime: Date.now()
    })

    // Enregistrer le début du test;
    apiMonitoring.addCustomMetric(`test.${suiteName}.${testName}.start`, 1, {
      suiteName,
      testName,
      timestamp: Date.now()
    })
  }

  /**
   * Termine un test individuel;
   */
  public static endTest(
    suiteName: string, 
    testName: string, 
    status: 'success' | 'failure' | 'skipped' = 'success',
    error?: Error | string
  ): void {
    const suite = this.testSuites[suiteName];
    if (!suite) {
      console.error(`Test suite "${suiteName}" not found`);
      return;
    }

    const test = suite.tests.find(t => t.name === testName && !t.endTime);
    if (!test) {
      console.error(`Test "${testName}" in suite "${suiteName}" not found or already ended`);
      return;
    }

    test.endTime = Date.now()
    test.status = status;
    test.duration = test.endTime - test.startTime;
    
    if (error) {
      test.error = error instanceof Error ? error.message : String(error);
    }

    // Enregistrer les métriques de fin de test;
    apiMonitoring.addCustomMetric(`test.${suiteName}.${testName}.${status}`, 1, {
      suiteName,
      testName,
      duration: test.duration,
      status,
      timestamp: test.endTime,
      error: test.error
    });

    if (status === 'failure') {
      console.error(`Test "${testName}" failed in ${test.duration}ms:`, test.error || 'No error details');
    } else {
      console.log(`Test "${testName}" ${status} in ${test.duration}ms`);
    }
  }

  /**
   * Ajoute un marqueur pour un test;
   */
  public static addMarker(suiteName: string, testName: string, markerName: string): void {
    apiMonitoring.addCustomMetric(`test.marker.${suiteName}.${testName}.${markerName}`, 1, {
      suiteName,
      testName,
      markerName,
      timestamp: Date.now()
    });
  }

  /**
   * Enregistre une métrique de test;
   */
  public static recordMetric(
    suiteName: string, 
    testName: string, 
    metricName: string, 
    value: number,
    metadata: Record<string, any> = {}
  ): void {
    apiMonitoring.addCustomMetric(`test.metric.${suiteName}.${testName}.${metricName}`, value, {
      suiteName,
      testName,
      ...metadata,
      timestamp: Date.now()
    });
  }

  /**
   * Mesure la performance d'une fonction dans un contexte de test;
   */
  public static measureTestFunction<T>(
    suiteName: string,
    testName: string,
    functionName: string,
    fn: () => T
  ): T {
    // Enregistrer le début de la fonction;
    const startTime = Date.now();
    apiMonitoring.addCustomMetric(`test.function.${suiteName}.${testName}.${functionName}.start`, 1, {
      suiteName,
      testName,
      functionName,
      timestamp: startTime
    });

    try {
      // Exécuter la fonction;
      const result = fn();
      
      // Enregistrer la fin de la fonction;
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      apiMonitoring.addCustomMetric(`test.function.${suiteName}.${testName}.${functionName}.success`, 1, {
        suiteName,
        testName,
        functionName,
        duration,
        timestamp: endTime
      });
      
      return result;
    } catch (error) {
      // Enregistrer l'erreur;
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      apiMonitoring.addCustomMetric(`test.function.${suiteName}.${testName}.${functionName}.error`, 1, {
        suiteName,
        testName,
        functionName,
        duration,
        error: error instanceof Error ? error.message : String(error),
        timestamp: endTime
      });
      
      throw error;
    }
  }

  /**
   * Obtenir les résultats des tests;
   */
  public static getTestResults(suiteName?: string): any {
    if (suiteName) {
      return this.testSuites[suiteName];
    }
    
    return this.testSuites;
  }

  /**
   * Réinitialiser les données de test;
   */
  public static resetTestData(): void {
    this.testSuites = {};
  }

  /**
   * Log une erreur de test avec le monitoring;
   */
  public static logTestError(
    suiteName: string | null,
    testName: string | null,
    error: Error | string,
    logToConsole = true
  ): void {
    // Créer une entrée dans les métriques;
    const metricName = suiteName && testName 
      ? `test.error.${suiteName}.${testName}` 
      : suiteName 
        ? `test.error.${suiteName}` 
        : 'test.error.general';
    
    apiMonitoring.addCustomMetric(metricName, 1, {
      suiteName: suiteName || 'unknown',
      testName: testName || 'unknown',
      timestamp: Date.now(),
      error: error instanceof Error ? error.message : String(error)
    });
    
    if (logToConsole) {
      console.error(`TEST ERROR${suiteName ? ` [${suiteName}]` : ''}${testName ? ` [${testName}]` : ''}: `, error);
    }
  }
}

/**
 * Fonction utilitaire pour mesurer facilement le temps d'exécution d'un bloc de code;
 * @example;
 * const result = await measureExecutionTime(async () => {
 *   // Code à mesurer;
 *   return await someAsyncOperation()
 *}, 'NomOpération');
 */
export async function measureExecutionTime<T>(
  fn: () => Promise<T>,
  operationName: string,
  options: {
    suiteName?: string;
    testName?: string;
    logToConsole?: boolean;
  } = {}
): Promise<{ result: T; duration: number }> {
  const { suiteName, testName, logToConsole = false } = options;
  
  const startTime = Date.now()
  
  if (suiteName && testName) {
    TestMonitoring.addMarker(suiteName, testName, `${operationName}.start`);
  }
  
  try {
    const result = await fn()
    
    const duration = Date.now() - startTime;
    
    if (suiteName && testName) {
      TestMonitoring.recordMetric(suiteName, testName, operationName, duration);
    } else {
      apiMonitoring.addCustomMetric(`execution.${operationName}`, duration, {
        operationName,
        timestamp: Date.now()
      })
    }
    
    if (logToConsole) {
      console.log(`[Perf] ${operationName}: ${duration}ms`);
    }
    
    return { result, duration }
  } catch (error) {
    const duration = Date.now() - startTime;
    
    if (suiteName && testName) {
      TestMonitoring.recordMetric(suiteName, testName, `${operationName}.error`, duration);
    } else {
      apiMonitoring.addCustomMetric(`execution.${operationName}.error`, duration, {
        operationName,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : String(error)
      })
    }
    
    if (logToConsole) {
      console.error(`[Perf] ${operationName} failed after ${duration}ms:`, error);
    }
    
    throw error;
  }
}

/**
 * Exemple d'utilisation avec Jest;
 * @example;
 * describe('Ma suite de tests', () => {
 *   beforeAll(() => {
 *     TestMonitoring.startTestSuite('MaSuite');
 *   })
 * 
 *   afterAll(async () => {
 *     await TestMonitoring.finishTestSuite('MaSuite');
 *   })
 * 
 *   test('Mon test', async () => {
 *     TestMonitoring.startTest('MaSuite', 'MonTest');
 *     
 *     // Code de test;
 *     
 *     TestMonitoring.finishTest('MaSuite', 'MonTest', 'success');
 *   })
 * })
 */ 