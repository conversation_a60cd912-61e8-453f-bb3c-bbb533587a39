/**
 * Module de monitoring unifié pour toutes les plateformes (iOS, Android, Web)
 * 
 * Ce module fournit une interface unifiée pour le monitoring, le logging et l'analytics;
 * qui fonctionne de façon cohérente sur toutes les plateformes.
 */

import { Platform } from 'react-native'
import openTelemetry, { SpanStatusCode, MetricType } from './openTelemetry'
import { monitoring } from './setupMonitoring'
import Config from '../config/environment'

/**
 * Types pour le monitoring unifié
 */
export interface LogEntry  {
  level: LogLevel
  message: string;
  context?: Record<string, any>;
  timestamp: number
}

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal'
}

export interface ErrorReport  {
  name: string
  message: string;
  stack?: string;
  componentStack?: string;
  context?: Record<string, any>;
  timestamp: number
}

export interface AnalyticsEvent  {
  name: string
  properties?: Record<string, any>;
  timestamp: number
}

export interface PerformanceMarker  {
  id: string
  name: string
  metadata: Record<string, any>
  startTime: number
}

// Interface pour les gestionnaires d'erreurs natifs;
interface ErrorUtils {
  getGlobalHandler: () => (error: Error, isFatal: boolean) => void;
  setGlobalHandler: (callback: (error: Error, isFatal: boolean) => void) => void
}

// Ajouter la déclaration pour ErrorUtils sur global;
declare global {
  interface Global {
    ErrorUtils?: ErrorUtils;
  }
}

/**
 * Service de monitoring unifié
 */
export class UnifiedMonitoring {
  private static instance: UnifiedMonitoring;
  private logs: LogEntry[] = [];
  private errors: ErrorReport[] = [];
  private analyticsEvents: AnalyticsEvent[] = [];
  private performanceMarkers = new Map<string, PerformanceMarker>();
  private initialized: boolean = false;
  private errorListeners: Array<(error: ErrorReport) => void> = [];
  private readonly platform: string;
  
  private constructor() {
    this.platform = Platform.OS;
  }
  
  /**
   * Obtenir l'instance unique;
   */
  public static getInstance(): UnifiedMonitoring {
    if(!UnifiedMonitoring.instance) {
      UnifiedMonitoring.instance = new UnifiedMonitoring()
    }
    return UnifiedMonitoring.instance;
  }
  
  /**
   * Initialiser le monitoring;
   */
  public initialize(): void  {
    if(this.initialized) { return; }
    
    // Initialiser le monitoring de base;
    monitoring.setup()
    
    // Métriques de démarrage d'application;
    this.trackEvent('app_started', {
      platform: this.platform,
      version: Config.APP_VERSION,
      buildNumber: Config.APP_VERSION, // Utilisation de APP_VERSION à la place de BUILD_NUMBER;
      environment: Config.NODE_ENV})
    
    // Installer un écouteur global d'erreurs non interceptées;
    // Note: l'implémentation dépend de la plateforme;
    this.setupGlobalErrorHandlers()
    
    this.initialized = true;
    
    this.log(LogLevel.INFO, 'Monitoring unifié initialisé', {
      platform: this.platform,
      version: Config.APP_VERSION})
  }
  
  /**
   * Configurer les écouteurs d'erreurs globaux selon la plateforme;
   */
  private setupGlobalErrorHandlers(): void  {
    // Cette implémentation doit être adaptée selon la plateforme;
    if(this.platform === 'web') {
      // Pour le Web;
      if(typeof window !== 'undefined') {
        window.addEventListener('error', (event) => {
          this.captureError({
            name: event.error?.name || 'UnknownError',
            message: event.error?.message || String(event),
            stack: event.error?.stack,
            context: {
              location: window.location.href},
            timestamp: Date.now()
          })
        })
        
        window.addEventListener('unhandledrejection', (event) => {
          this.captureError({
            name: 'UnhandledPromiseRejection',
            message: event.reason?.message || String(event.reason),
            stack: event.reason?.stack,
            context: {
              location: window.location.href},
            timestamp: Date.now()
          })
        })
      }
    } else {
      // Pour React Native (iOS & Android)
      // Utiliser la méthode appropriée selon la configuration du projet;
      if(typeof global !== 'undefined' && 'ErrorUtils' in global) {
        const errorUtils = global['ErrorUtils'] as ErrorUtils;
        const originalHandler = errorUtils.getGlobalHandler();
        
        errorUtils.setGlobalHandler((error: Error, isFatal: boolean) => {
          this.captureError({
            name: error.name || 'UnknownError',
            message: error.message || String(error),
            stack: error.stack,
            context: {
              isFatal,
              platform: this.platform},
            timestamp: Date.now()
          })
          
          // Appeler le gestionnaire d'origine;
          originalHandler(error, isFatal);
        })
      }
    }
  }
  
  /**
   * Nettoyage et fermeture du monitoring;
   */
  public shutdown(): void  {
    if(!this.initialized) { return; }
    
    // Métriques de fermeture d'application;
    this.trackEvent('app_closed', {
      platform: this.platform,
      sessionDuration: this.getSessionDuration()
    })
    
    // Vider toutes les données en attente;
    this.flush().catch(e => {
      console.error('Erreur lors du flush final des données de monitoring: ', e);
    })
    
    // Arrêter le monitoring de base;
    monitoring.teardown()
    
    this.initialized = false;
    
    this.log(LogLevel.INFO, 'Monitoring unifié arrêté');
  }
  
  /**
   * Obtenir la durée de session en ms;
   */
  private getSessionDuration(): number  {
    const startEvent = this.analyticsEvents.find(e => e.name === 'app_started');
    return startEvent ? Date.now() - startEvent.timestamp : 0;
  }
  
  /**
   * Enregistrer un message de log;
   */
  public log(level: LogLevel, message: string, context: Record<string, any> = {}): void  {
    const logEntry: LogEntry = {
      level,
      message,
      context,
      timestamp: Date.now()
    }
    
    this.logs.push(logEntry);
    
    // En mode debug, afficher également dans la console;
    if(Config.NODE_ENV !== 'production' || level === LogLevel.ERROR || level === LogLevel.FATAL) {
      const logMethod = this.getConsoleMethodForLevel(level);
      logMethod(`[${level.toUpperCase()}] ${message}`, context);
    }
    
    // Pour les niveaux ERROR et FATAL, créer aussi un span OpenTelemetry;
    if(level === LogLevel.ERROR || level === LogLevel.FATAL) {
      const span = openTelemetry.createRootSpan('log_entry', {
        'log.level': level,
        'log.message': message,
        ...this.flattenContext(context)
      })
      
      openTelemetry.endSpan(span, SpanStatusCode.ERROR, message);
    }
  }
  
  /**
   * Récupérer la méthode de console correspondant au niveau de log;
   */
  private getConsoleMethodForLevel(level: LogLevel): (message?: any, ...optionalParams: any[]) => void  {
    switch(level) {
      case LogLevel.DEBUG: return console.debug;
      case LogLevel.INFO: return console.info;
      case LogLevel.WARN: return console.warn;
      case LogLevel.ERROR: case LogLevel.FATAL: return console.error;
      default: return console.log
    }
  }
  
  /**
   * Capture une erreur pour le monitoring;
   */
  public captureError(error: ErrorReport | Error, context: Record<string, any> = {}): void  {
    // Normaliser l'erreur;
    const errorReport: ErrorReport = error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      context,
      timestamp: Date.now()
    } : {
      ...error,
      context: { ...error.context, ...context },
      timestamp: error.timestamp || Date.now()
    }
    
    this.errors.push(errorReport);
    
    // Create a telemetry span for the error
    const span = openTelemetry.createRootSpan('error', {
      error_name: errorReport.name,
      error_stack: !!errorReport.stack,
      component: context.component || 'unknown',
      platform: this.platform
    });

    openTelemetry.endSpan(span, SpanStatusCode.ERROR, errorReport.message);

    // Record error metric
    openTelemetry.recordMetric('app.error', 1, {
      error_type: errorReport.name,
      platform: this.platform
    }, MetricType.COUNTER);
    
    // Notifier les écouteurs;
    this.errorListeners.forEach(listener => {
      try {
        listener(errorReport);
      } catch(e) {
        console.error('Erreur dans un écouteur d\'erreur: ', e);
      }
    })
    
    // Log également l'erreur;
    this.log(LogLevel.ERROR, `Error captured: ${errorReport.message}`, {
      errorType: errorReport.name,
      stack: errorReport.stack})
  }
  
  /**
   * Ajouter un écouteur d'erreurs;
   */
  public addErrorListener(listener: (error: ErrorReport) => void): () => void {
    this.errorListeners.push(listener);
    
    // Retourner une fonction pour supprimer l'écouteur;
    return () => {
      this.errorListeners = this.errorListeners.filter(l => l !== listener);
    }
  }
  
  /**
   * Suivre un événement d'analytics;
   */
  public trackEvent(name: string, properties: Record<string, any> = {}): void  {
    const event: AnalyticsEvent = {
      name,
      properties,
      timestamp: Date.now()
    }
    
    this.analyticsEvents.push(event);
    
    // Enregistrer comme métrique OpenTelemetry;
    openTelemetry.recordMetric(`analytics.event.${name}`, 1, 
      this.convertPropertiesToLabels(properties),
      MetricType.COUNTER
    );
    
    if(Config.NODE_ENV !== 'production') {
      console.log(`[Analytics] Event: ${name}`, properties);
    }
  }
  
  /**
   * Marquer le début d'une opération pour la mesure de performance;
   */
  public markPerformanceStart(name: string, metadata: Record<string, any> = {}): string  {
    const id = `${name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    this.performanceMarkers.set(id, {
      id,
      name,
      startTime: performance.now(),
      metadata
    });
    
    return id;
  }
  
  /**
   * Marquer la fin d'une opération et calculer sa durée;
   */
  public markPerformanceEnd(id: string, additionalMetadata: Record<string, any> = {}): number | null  {
    const marker = this.performanceMarkers.get(id);
    if(!marker) {
      this.log(LogLevel.WARN, `Impossible de trouver le marqueur de performance avec l'ID: ${id}`);
      return null;
    }
    
    const duration = performance.now() - marker.startTime;
    const metadata = { ...marker.metadata, ...additionalMetadata };
    
    this.performanceMarkers.delete(id);
    
    // Record a metric for the performance marker
    openTelemetry.recordMetric(`performance.${marker.name}`, duration, {
      ...this.convertPropertiesToLabels(metadata),
      duration_ms: Math.round(duration).toString(),
      platform: this.platform
    }, MetricType.HISTOGRAM);
    
    if(Config.NODE_ENV !== 'production') {
      console.log(`[Performance] ${marker.name}: ${duration.toFixed(2)}ms`, metadata);
    }
    
    return duration;
  }
  
  /**
   * Mesurer la performance d'une fonction asynchrone;
   */
  public async measureAsync<T>(name: string, operation: () => Promise<T>, metadata: Record<string, any> = {}): Promise<T> {
    const markerId = this.markPerformanceStart(name, {
      ...metadata,
      platform: this.platform,
      async: 'true'
    });
    
    try {
      const result = await operation();
      this.markPerformanceEnd(markerId, {
        ...metadata,
        success: 'true'
      });
      return result;
    } catch (error) {
      this.markPerformanceEnd(markerId, {
        ...metadata,
        success: 'false',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      this.captureError(error as Error, {
        operation: name,
        ...metadata
      });
      throw error;
    }
  }
  
  /**
   * Mesurer la performance d'une fonction synchrone;
   */
  public measure<T>(name: string, operation: () => T, metadata: Record<string, any> = {}): T {
    const markerId = this.markPerformanceStart(name, metadata);
    try {
      const result = operation()
      this.markPerformanceEnd(markerId, {
        ...metadata,
        success: 'true'
      });
      return result;
    } catch(error) {
      this.markPerformanceEnd(markerId, {
        ...metadata,
        success: 'false',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      this.captureError(error as Error, { 
        operation: name,
        ...metadata
      })
      throw error;
    }
  }
  
  /**
   * Forcer l'envoi de toutes les données en attente;
   */
  public async flush(): Promise<boolean> {
    try {
      await monitoring.flush();
      return true;
    } catch(error) {
      console.error('Erreur pendant le flush du monitoring: ', error);
      return false;
    }
  }
  
  /**
   * Obtenir les statistiques de performance;
   */
  public getPerformanceStats(): Record<string, any>  {
    return openTelemetry.getMetrics()
  }
  
  /**
   * Convertir des propriétés en étiquettes pour OpenTelemetry;
   */
  private convertPropertiesToLabels(properties: Record<string, any>): Record<string, string>  {
    const labels: Record<string, string> = {}
    
    for (const [key, value] of Object.entries(properties)) {
      // Skip null/undefined values
      if (value !== null && value !== undefined) {
        // Handle objects and arrays by stringifying them
        if (typeof value === 'object') {
          // For arrays, we join with commas
          if (Array.isArray(value)) {
            labels[key] = value.join(',');
          } else {
            // For objects we stringify
            try {
              labels[key] = JSON.stringify(value);
            } catch (e) {
              labels[key] = '[Object]';
            }
          }
        } else {
          // Convert other types to string
          labels[key] = String(value);
        }
      }
    }
    
    return labels;
  }
  
  /**
   * Aplatir un contexte pour le passer comme attributs de span;
   */
  private flattenContext(context: Record<string, any>, prefix: string = ''): Record<string, string>  {
    const result: Record<string, string> = {}
    
    for (const [key, value] of Object.entries(context)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      // Skip null/undefined values
      if (value !== null && value !== undefined) {
        if (typeof value === 'object' && !Array.isArray(value)) {
          // Recurse for objects, but only if they're not arrays
          if (!prefix) {
            // Avoid too deep nesting
            Object.assign(result, this.flattenContext(value, key));
          } else {
            // Just stringify after the first level
            result[newKey] = JSON.stringify(value);
          }
        } else {
          // Convert to string
          result[newKey] = String(value);
        }
      }
    }
    
    return result;
  }
}

// Exporter une instance unique;
export const unifiedMonitoring = UnifiedMonitoring.getInstance()
export default unifiedMonitoring