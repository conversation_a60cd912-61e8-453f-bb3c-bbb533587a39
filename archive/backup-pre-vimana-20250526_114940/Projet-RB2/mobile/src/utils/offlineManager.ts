/**
 * Gestionnaire des opérations hors ligne pour l'application
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { EventEmitter } from './eventEmitter';

// Clé pour les opérations en attente
const PENDING_OPERATIONS_KEY = 'offlineManager:pendingOperations';

// Types d'événements émis
export const OFFLINE_EVENTS = {
  SYNC_STARTED: 'offline:sync:started',
  SYNC_COMPLETED: 'offline:sync:completed',
  SYNC_FAILED: 'offline:sync:failed',
  OPERATION_QUEUED: 'offline:operation:queued',
  OPERATION_PROCESSED: 'offline:operation:processed',
  OPERATION_FAILED: 'offline:operation:failed',
  CONNECTIVITY_CHANGED: 'offline:connectivity:changed'
};

// Type d'opération en attente
export interface PendingOperation {
  id: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: any;
  headers?: Record<string, string>;
  priority: 'low' | 'medium' | 'high';
  timestamp: number;
  expiration?: number;
  retryCount?: number;
  status: 'pending' | 'processing' | 'failed';
  lastError?: string;
}

class OfflineManager {
  private static instance: OfflineManager;
  private isOnline: boolean = true;
  private isSyncing: boolean = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private pendingOperations: PendingOperation[] = [];
  private initialized: boolean = false;

  private constructor() {
    // Constructeur privé pour Singleton
  }

  /**
   * Obtenir l'instance unique du gestionnaire hors ligne
   */
  public static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager();
    }
    return OfflineManager.instance;
  }

  /**
   * Initialiser le gestionnaire hors ligne
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Vérifier l'état de la connexion
      const networkState = await NetInfo.fetch();
      this.isOnline = !!networkState.isConnected && !!networkState.isInternetReachable;

      // Charger les opérations en attente
      await this.loadPendingOperations();

      // Configurer les listeners pour les changements de connexion
      NetInfo.addEventListener(state => {
        const newOnlineStatus = !!state.isConnected && !!state.isInternetReachable;
        
        if (this.isOnline !== newOnlineStatus) {
          this.isOnline = newOnlineStatus;
          EventEmitter.emit(OFFLINE_EVENTS.CONNECTIVITY_CHANGED, this.isOnline);
          
          // Si on revient en ligne, démarrer la synchronisation
          if (this.isOnline && this.pendingOperations.length > 0) {
            this.syncOperations();
          }
        }
      });

      // Configurer la synchronisation périodique
      this.setupPeriodicSync();
      
      this.initialized = true;
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du gestionnaire hors ligne:', error);
    }
  }

  /**
   * Ajouter une opération à la file d'attente
   */
  public async queueOperation(operation: Omit<PendingOperation, 'id' | 'timestamp' | 'status'>): Promise<string> {
    const id = `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const pendingOperation: PendingOperation = {
      ...operation,
      id,
      timestamp: Date.now(),
      status: 'pending',
      retryCount: 0
    };
    
    this.pendingOperations.push(pendingOperation);
    await this.savePendingOperations();
    
    EventEmitter.emit(OFFLINE_EVENTS.OPERATION_QUEUED, pendingOperation);
    
    // Si en ligne, essayer de synchroniser immédiatement
    if (this.isOnline && !this.isSyncing) {
      this.syncOperations();
    }
    
    return id;
  }

  /**
   * Récupérer toutes les opérations en attente
   */
  public getPendingOperations(): PendingOperation[] {
    return [...this.pendingOperations];
  }

  /**
   * Supprimer une opération de la file d'attente
   */
  public async removeOperation(id: string): Promise<boolean> {
    const initialLength = this.pendingOperations.length;
    this.pendingOperations = this.pendingOperations.filter(op => op.id !== id);
    
    if (initialLength !== this.pendingOperations.length) {
      await this.savePendingOperations();
      return true;
    }
    
    return false;
  }

  /**
   * Synchroniser les opérations en attente
   */
  public async syncOperations(): Promise<boolean> {
    if (!this.isOnline || this.isSyncing || this.pendingOperations.length === 0) {
      return false;
    }
    
    this.isSyncing = true;
    EventEmitter.emit(OFFLINE_EVENTS.SYNC_STARTED);
    
    try {
      // Trier les opérations par priorité et timestamp
      const sortedOperations = [...this.pendingOperations].sort((a, b) => {
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
        return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
      });
      
      let hasErrors = false;
      
      // Traiter chaque opération
      for (const operation of sortedOperations) {
        // Vérifier si l'opération a expiré
        if (operation.expiration && Date.now() > operation.timestamp + operation.expiration) {
          await this.removeOperation(operation.id);
          EventEmitter.emit(OFFLINE_EVENTS.OPERATION_FAILED, operation, 'Operation expired');
          continue;
        }
        
        // Mettre à jour le statut
        operation.status = 'processing';
        await this.savePendingOperations();
        
        try {
          // Ici, nous simulons l'exécution de l'opération (à remplacer par le code réel)
          // Dans une vraie implémentation, vous utiliseriez fetch ou axios pour exécuter la requête
          console.log(`Exécution de l'opération ${operation.id}: ${operation.method} ${operation.url}`);
          
          // Attendre un peu pour simuler le temps de la requête
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Si tout s'est bien passé, supprimer l'opération
          await this.removeOperation(operation.id);
          EventEmitter.emit(OFFLINE_EVENTS.OPERATION_PROCESSED, operation);
        } catch (error) {
          hasErrors = true;
          
          // Mettre à jour le nombre d'essais et le statut
          operation.retryCount = (operation.retryCount || 0) + 1;
          operation.status = 'failed';
          operation.lastError = error instanceof Error ? error.message : String(error);
          
          await this.savePendingOperations();
          EventEmitter.emit(OFFLINE_EVENTS.OPERATION_FAILED, operation, operation.lastError);
        }
      }
      
      this.isSyncing = false;
      EventEmitter.emit(OFFLINE_EVENTS.SYNC_COMPLETED, !hasErrors);
      return !hasErrors;
    } catch (error) {
      this.isSyncing = false;
      EventEmitter.emit(OFFLINE_EVENTS.SYNC_FAILED, error);
      return false;
    }
  }

  /**
   * Vérifier si l'appareil est en ligne
   */
  public isDeviceOnline(): boolean {
    return this.isOnline;
  }

  /**
   * Vérifier si une synchronisation est en cours
   */
  public isSyncInProgress(): boolean {
    return this.isSyncing;
  }

  /**
   * Charger les opérations en attente depuis le stockage
   */
  private async loadPendingOperations(): Promise<void> {
    try {
      const storedOperations = await AsyncStorage.getItem(PENDING_OPERATIONS_KEY);
      if (storedOperations) {
        this.pendingOperations = JSON.parse(storedOperations);
        
        // Nettoyer les opérations expirées
        const now = Date.now();
        this.pendingOperations = this.pendingOperations.filter(op => {
          return !op.expiration || now < op.timestamp + op.expiration;
        });
        
        await this.savePendingOperations();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des opérations en attente:', error);
      this.pendingOperations = [];
    }
  }

  /**
   * Sauvegarder les opérations en attente
   */
  private async savePendingOperations(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        PENDING_OPERATIONS_KEY,
        JSON.stringify(this.pendingOperations)
      );
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des opérations en attente:', error);
    }
  }

  /**
   * Configurer la synchronisation périodique
   */
  private setupPeriodicSync(intervalMs: number = 5 * 60 * 1000): void {
    // Nettoyer l'ancien intervalle s'il existe
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    // Créer un nouvel intervalle
    this.syncInterval = setInterval(() => {
      if (this.isOnline && this.pendingOperations.length > 0 && !this.isSyncing) {
        this.syncOperations();
      }
    }, intervalMs);
  }

  /**
   * Arrêter la synchronisation périodique
   */
  public stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }
}

// Exporter une instance unique
export const offlineManager = OfflineManager.getInstance(); 