/**
 * Module de monitoring des performances pour l'API;
 * Implémentation de OpenTelemetry pour le monitoring des requêtes API;
 */

import { EventEmitter as ApiEventEmitter, EventCallback } from './eventEmitter'
import AsyncStorage from '@react-native-async-storage/async-storage'
import NetInfo from '@react-native-community/netinfo'
import pako from 'pako'

// Types pour les métriques de performance;
export interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime: number;
  duration: number;
  status: 'success' | 'error';
  statusCode?: number;
  errorType?: string;
  cached?: boolean;
  retryCount?: number;
  offline?: boolean;
  networkType?: NetworkType;
  metadata?: Record<string, unknown>;
}

// Types pour les événements de trace distribuée;
export interface TraceEvent {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  name: string;
  startTime: number;
  endTime?: number;
  status?: 'ok' | 'error';
  attributes: Record<string, unknown>;
}

// Types pour les événements API;
export interface ApiRequestEvent {
  name: string;
  startTime: number;
  config: {
    method: string;
    url: string;
    params?: Record<string, unknown>;
    headers?: Record<string, string>;
    retry?: boolean;
    cache?: {
      enabled: boolean;
    };
    offline?: {
      enabled: boolean;
    };
    metadata?: Record<string, unknown>;
  };
}

export interface ApiResponseEvent extends ApiRequestEvent {
  endTime: number;
  response: {
    status: number;
    data: unknown;
    headers: Record<string, string>;
    cached?: boolean;
  };
  config: {
    method: string;
    url: string;
    params?: Record<string, unknown>;
    headers?: Record<string, string>;
    retry?: boolean;
    retryCount?: number;
    cache?: {
      enabled: boolean;
    };
    offline?: {
      enabled: boolean;
    };
    metadata?: Record<string, unknown>;
  };
}

export interface ApiErrorConfig {
  method: string;
  url: string;
  params?: Record<string, unknown>;
  headers?: Record<string, string>;
  retry?: boolean;
  retryCount?: number;
  metadata?: Record<string, unknown>;
  offline?: {
    enabled: boolean;
  };
}

// Types pour les erreurs
interface ApiMonitoringError extends Error {
  code?: string;
  details?: Record<string, unknown>;
}

export interface ApiErrorEvent extends ApiRequestEvent {
  endTime: number;
  error: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
    config?: ApiErrorConfig;
    response?: {
      status: number;
      data: unknown;
    };
  };
}

// Configuration du monitoring;
export interface MonitoringConfig {
  enabled: boolean;
  sampleRate: number; // 0-1, pourcentage des requêtes à monitorer;
  flushInterval: number; // Intervalle d'envoi en ms;
  maxQueueSize: number; // Taille maximale de la file d'attente;
  reportErrors: boolean; // Signaler les erreurs;
  serverEndpoint?: string; // Point de terminaison pour l'envoi des données;
  debugMode?: boolean; // Mode débogage;
}

export interface DeviceInfo {
  appVersion: string;
  platform: string;
  osVersion: string;
  deviceModel: string;
  deviceId: string;
}

// Types d'événements API
interface ApiEventMap {
  'api.request.start': ApiRequestEvent;
  'api.request.success': ApiResponseEvent;
  'api.request.error': ApiErrorEvent;
  'api.network.state_change': boolean;
}

// Types pour le payload d'envoi
interface MonitoringPayload {
  device: DeviceInfo;
  session: {
    id: string;
    timestamp: number;
  };
  metrics: PerformanceMetric[];
  traces: TraceEvent[];
}

// Types pour les statistiques de performance
interface PerformanceStats {
  requestCount: number;
  successCount: number;
  errorCount: number;
  cachedCount: number;
  avgDuration: number;
  errorRate: number;
  cacheHitRate: number;
}

// Types pour les métriques personnalisées
interface CustomMetricAttributes {
  customMetric: true;
  value: number;
  [key: string]: unknown;
}

// Constantes pour les clés de stockage
const STORAGE_KEYS = {
  DEVICE_ID: '@ApiMonitoring:deviceId',
  QUEUED_METRICS: '@ApiMonitoring:queuedMetrics',
  QUEUED_TRACES: '@ApiMonitoring:queuedTraces'
} as const;

// Types pour le réseau
type NetworkType = 'wifi' | 'cellular' | 'none' | 'unknown';

// Types pour les en-têtes HTTP
interface SafeHeaders {
  [key: string]: string | undefined;
  Authorization?: string;
}

// Constantes de configuration par défaut
const DEFAULT_CONFIG: MonitoringConfig = {
  enabled: true,
  sampleRate: 0.1,
  flushInterval: 30000,
  maxQueueSize: 100,
  reportErrors: true,
  debugMode: false
};

const MAX_METRIC_NAME_LENGTH = 100;
const MAX_METRIC_VALUE = 1e9;
const MIN_METRIC_VALUE = -1e9;

// Constantes pour la configuration
const DEFAULT_TIMEOUT = 30000; // 30 secondes
const SENSITIVE_FIELDS = ['password', 'token', 'secret', 'key', 'auth', 'credentials'] as const;
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY_BASE = 1000; // Délai de base en ms
const COMPRESSION_THRESHOLD = 1024 * 10; // 10KB

// Types pour la stratégie de retry
interface RetryConfig {
  attempt: number;
  maxAttempts: number;
  delay: number;
}

// Types pour les métriques internes
interface InternalMetrics {
  // Métriques de base
  flushAttempts: number;
  flushSuccesses: number;
  flushFailures: number;
  
  // Métriques de compression
  compressionRatio: number;
  lastCompressedSize: number;
  lastOriginalSize: number;
  totalBytesSaved: number;
  
  // Métriques de performance
  lastFlushDuration: number;
  averageFlushDuration: number;
  minFlushDuration: number;
  maxFlushDuration: number;
  
  // Métriques de retry
  retryAttempts: number;
  retrySuccesses: number;
  totalRetryDelay: number;
  
  // Métriques réseau
  networkErrors: number;
  timeoutErrors: number;
  lastNetworkLatency: number;
  
  // Métriques de mémoire
  peakQueueSize: number;
  totalMetricsProcessed: number;
  droppedMetrics: number;
}

// Constantes pour le batching
const BATCH_SIZE = 50; // Nombre maximum de métriques par batch
const BATCH_INTERVAL = 5000; // Intervalle minimum entre les batchs en ms

interface MetricsBatch {
  metrics: PerformanceMetric[];
  traces: TraceEvent[];
  timestamp: number;
}

// Classe principale pour le monitoring;
export class ApiMonitoring {
  private static instance: ApiMonitoring;
  private config: MonitoringConfig = DEFAULT_CONFIG;
  private metrics: PerformanceMetric[] = [];
  private traces: TraceEvent[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private isNetworkAvailable: boolean = true;
  private sessionId: string;
  private deviceInfo: DeviceInfo = {
    appVersion: '1.0.0',
    platform: 'unknown',
    osVersion: 'unknown',
    deviceModel: 'unknown',
    deviceId: 'unknown'
  };
  private retryTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private internalMetrics: InternalMetrics = {
    // Métriques de base
    flushAttempts: 0,
    flushSuccesses: 0,
    flushFailures: 0,
    
    // Métriques de compression
    compressionRatio: 0,
    lastCompressedSize: 0,
    lastOriginalSize: 0,
    totalBytesSaved: 0,
    
    // Métriques de performance
    lastFlushDuration: 0,
    averageFlushDuration: 0,
    minFlushDuration: Infinity,
    maxFlushDuration: 0,
    
    // Métriques de retry
    retryAttempts: 0,
    retrySuccesses: 0,
    totalRetryDelay: 0,
    
    // Métriques réseau
    networkErrors: 0,
    timeoutErrors: 0,
    lastNetworkLatency: 0,
    
    // Métriques de mémoire
    peakQueueSize: 0,
    totalMetricsProcessed: 0,
    droppedMetrics: 0
  };
  private batchQueue: MetricsBatch[] = [];
  private lastBatchTime: number = 0;
  private batchTimer: NodeJS.Timeout | null = null;

  private constructor() {
    this.sessionId = this.generateId();
    this.initNetworkListener();
    this.initDeviceInfo();
  }

  /**
   * Obtenir l'instance unique du monitoring;
   */
  public static getInstance(): ApiMonitoring {
    if(!ApiMonitoring.instance) {
      ApiMonitoring.instance = new ApiMonitoring();
    }
    return ApiMonitoring.instance;
  }

  /**
   * Initialiser le monitoring avec la configuration;
   */
  public initialize(config?: Partial<MonitoringConfig>): void {
    // Valider la configuration
    if (config?.sampleRate !== undefined) {
      config.sampleRate = Math.max(0, Math.min(1, config.sampleRate));
    }
    
    if (config?.maxQueueSize !== undefined) {
      config.maxQueueSize = Math.max(10, config.maxQueueSize);
    }
    
    if (config?.flushInterval !== undefined) {
      config.flushInterval = Math.max(1000, config.flushInterval);
    }

    this.config = { ...DEFAULT_CONFIG, ...config };

    if(this.config.enabled) {
      // Démarrer le timer de flush automatique;
      this.startFlushTimer();

      // S'abonner aux événements API;
      this.subscribeToApiEvents();

      // Récupérer les métriques mises en file d'attente depuis le stockage;
      void this.loadQueuedMetrics();
    }
  }

  /**
   * S'abonner aux événements de l'API;
   */
  private subscribeToApiEvents(): void {
    ApiEventEmitter.on('api.request.start', (event: ApiRequestEvent) => this.onRequestStart(event));
    ApiEventEmitter.on('api.request.success', (event: ApiResponseEvent) => this.onRequestSuccess(event));
    ApiEventEmitter.on('api.request.error', (event: ApiErrorEvent) => this.onRequestError(event));
    ApiEventEmitter.on('api.network.state_change', (isConnected: boolean) => this.onNetworkStateChange(isConnected));
  }

  /**
   * Initialiser l'écouteur d'état réseau;
   */
  private initNetworkListener(): void {
    NetInfo.addEventListener(state => {
      this.isNetworkAvailable = state.isConnected ?? false;
      this.onNetworkStateChange(this.isNetworkAvailable);
    });
  }

  /**
   * Recueillir des informations sur l'appareil;
   */
  private async initDeviceInfo(): Promise<void> {
    try {
      const deviceId = await this.getOrCreateDeviceId();
      this.deviceInfo = {
        ...this.deviceInfo,
        deviceId
      };
    } catch(error) {
      console.warn('Erreur lors de la collecte des informations sur l\'appareil', error);
    }
  }

  /**
   * Obtenir ou créer un ID d'appareil;
   */
  private async getOrCreateDeviceId(): Promise<string> {
    try {
      const storedId = await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID);
      if (storedId) return storedId;

      const newId = this.generateId();
      await AsyncStorage.setItem(STORAGE_KEYS.DEVICE_ID, newId);
      return newId;
    } catch(error) {
      return this.generateId();
    }
  }

  /**
   * Démarrer le timer de flush automatique;
   */
  private startFlushTimer(): void {
    if(this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(() => {
      if (typeof this.flushMetrics === 'function') {
        void this.flushMetrics();
      }
    }, this.config.flushInterval);
  }

  /**
   * Événement de démarrage de requête;
   */
  private onRequestStart(event: ApiRequestEvent): void {
    if (!this.shouldSample()) return;

    const traceId = this.generateId();
    const spanId = this.generateId();

    // Créer un événement de trace;
    const trace: TraceEvent = {
      traceId,
      spanId,
      name: event.name,
      startTime: event.startTime || Date.now(),
      attributes: {
        method: event.config.method,
        url: event.config.url,
        params: event.config.params,
        headers: this.sanitizeHeaders(event.config.headers),
        retry: !!event.config.retry,
        cache: !!event.config.cache?.enabled,
        offline: !!event.config.offline?.enabled
      }
    };

    this.traces.push(trace);

    // Stocker l'ID de trace dans la configuration;
    if(event.config.metadata) {
      event.config.metadata.traceId = traceId;
      event.config.metadata.spanId = spanId;
    }
  }

  /**
   * Événement de succès de requête;
   */
  private async onRequestSuccess(event: ApiResponseEvent): Promise<void> {
    if (!this.shouldSample()) return;

    const endTime = Date.now();
    const traceId = event.config?.metadata?.traceId;
    const spanId = event.config?.metadata?.spanId;
    const networkType = await this.getNetworkType();

    // Mettre à jour la trace si elle existe;
    if(traceId && spanId) {
      const trace = this.traces.find(t => t.traceId === traceId && t.spanId === spanId);
      if(trace) {
        trace.endTime = endTime;
        trace.status = 'ok';
        trace.attributes.response = {
          status: event.response?.status,
          headers: this.sanitizeHeaders(event.response?.headers),
          cached: !!event.response?.cached,
          size: event.response?.data ? JSON.stringify(event.response.data).length : 0
        };
      }
    }

    // Créer une métrique de performance;
    const metric: PerformanceMetric = {
      name: event.name || 'api_request',
      startTime: event.startTime || 0,
      endTime,
      duration: event.startTime ? endTime - event.startTime : 0,
      status: 'success',
      statusCode: event.response?.status,
      cached: !!event.response?.cached,
      retryCount: event.config?.retryCount || 0,
      offline: !!event.config?.offline?.enabled,
      networkType,
      metadata: {
        traceId,
        spanId,
        url: event.config?.url,
        method: event.config?.method
      }
    };

    this.metrics.push(metric);
    this.pruneMetricsIfNeeded();
  }

  /**
   * Événement d'erreur de requête avec gestion améliorée des erreurs;
   */
  private async onRequestError(event: ApiErrorEvent): Promise<void> {
    if (!this.shouldSample() || !this.config.reportErrors) return;

    const endTime = Date.now();
    const traceId = event.error?.config?.metadata?.traceId;
    const spanId = event.error?.config?.metadata?.spanId;
    const networkType = await this.getNetworkType();

    // Mettre à jour la trace si elle existe;
    if(traceId && spanId) {
      const trace = this.traces.find(t => t.traceId === traceId && t.spanId === spanId);
      if(trace) {
        trace.endTime = endTime;
        trace.status = 'error';
        trace.attributes.error = {
          message: event.error?.message,
          code: event.error?.code,
          status: event.error?.response?.status,
          type: event.error?.name || 'Error'
        };
      }
    }

    // Créer une métrique d'erreur avec plus de détails;
    const metric: PerformanceMetric = {
      name: event.name || 'api_request',
      startTime: event.startTime || 0,
      endTime,
      duration: event.startTime ? endTime - event.startTime : 0,
      status: 'error',
      statusCode: event.error?.response?.status,
      errorType: event.error?.name || 'Error',
      retryCount: event.error?.config?.retryCount || 0,
      offline: !!event.error?.config?.offline?.enabled,
      networkType,
      metadata: {
        traceId,
        spanId,
        url: event.error?.config?.url,
        method: event.error?.config?.method,
        message: event.error?.message,
        errorCode: event.error?.code,
        responseData: event.error?.response?.data,
        stack: event.error?.stack
      }
    };

    this.metrics.push(metric);
    this.pruneMetricsIfNeeded();
  }

  /**
   * Événement de changement d'état réseau;
   */
  private onNetworkStateChange(isConnected: boolean): void {
    this.isNetworkAvailable = isConnected;
    
    // Si le réseau redevient disponible, tenter d'envoyer les métriques en attente;
    if(isConnected && typeof this.flushMetrics === 'function') {
      void this.flushMetrics();
    }
  }

  /**
   * Générer un ID unique;
   */
  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Décider si cette requête doit être échantillonnée;
   */
  private shouldSample(): boolean {
    return this.config.enabled && Math.random() < this.config.sampleRate;
  }

  /**
   * Obtenir le type de réseau actuel;
   */
  private async getNetworkType(): Promise<NetworkType> {
    try {
      const netInfo = await NetInfo.fetch();
      
      if (!netInfo.isConnected) {
        return 'none';
      }
      
      switch (netInfo.type) {
        case 'wifi':
          return 'wifi';
        case 'cellular':
          return 'cellular';
        default:
          return 'unknown';
      }
    } catch (error) {
      console.warn('Erreur lors de la récupération du type de réseau:', error);
      return 'unknown';
    }
  }

  /**
   * Nettoyer les informations sensibles des en-têtes;
   */
  private sanitizeHeaders(headers: SafeHeaders | undefined | null): SafeHeaders {
    if(!headers) { 
      return {}; 
    }
    
    const sanitized: SafeHeaders = { ...headers };
    
    // Retirer les informations d'authentification sensibles;
    if(sanitized.Authorization) {
      sanitized.Authorization = '[FILTERED]';
    }
    
    return sanitized;
  }

  /**
   * Élaguer les métriques si elles dépassent la taille maximale;
   */
  private pruneMetricsIfNeeded(): void {
    if(this.metrics.length > this.config.maxQueueSize) {
      this.metrics = this.metrics.slice(-Math.floor(this.config.maxQueueSize * 0.8));
    }
    
    if(this.traces.length > this.config.maxQueueSize) {
      this.traces = this.traces.slice(-Math.floor(this.config.maxQueueSize * 0.8));
    }
  }

  /**
   * Charger les métriques mises en file d'attente depuis le stockage;
   */
  private async loadQueuedMetrics(): Promise<void> {
    try {
      const queuedMetrics = await AsyncStorage.getItem(STORAGE_KEYS.QUEUED_METRICS);
      const queuedTraces = await AsyncStorage.getItem(STORAGE_KEYS.QUEUED_TRACES);
      
      if(queuedMetrics) {
        const metrics = JSON.parse(queuedMetrics) as PerformanceMetric[];
        this.metrics.push(...metrics);
      }
      
      if(queuedTraces) {
        const traces = JSON.parse(queuedTraces) as TraceEvent[];
        this.traces.push(...traces);
      }
      
      // Nettoyer le stockage après le chargement;
      await AsyncStorage.removeItem(STORAGE_KEYS.QUEUED_METRICS);
      await AsyncStorage.removeItem(STORAGE_KEYS.QUEUED_TRACES);
      
      // S'assurer que les files ne dépassent pas la taille maximale;
      this.pruneMetricsIfNeeded();
    } catch(error) {
      console.warn('Erreur lors du chargement des métriques mises en file d\'attente', error);
    }
  }

  /**
   * Nettoyer les données sensibles d'un objet
   * @param data Données à nettoyer
   * @param depth Profondeur maximale de récursion
   */
  private sanitizeData(data: unknown, depth: number = 3): unknown {
    if (depth <= 0 || !data) return data;

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item, depth - 1));
    }

    if (typeof data === 'object') {
      const sanitized: Record<string, unknown> = {};
      
      for (const [key, value] of Object.entries(data)) {
        const lowerKey = key.toLowerCase();
        
        if (SENSITIVE_FIELDS.some(field => lowerKey.includes(field))) {
          sanitized[key] = '[FILTERED]';
        } else {
          sanitized[key] = this.sanitizeData(value, depth - 1);
        }
      }
      
      return sanitized;
    }

    return data;
  }

  /**
   * Exporter les métriques dans un format spécifique
   * @param format Format d'export ('json' | 'csv')
   */
  public async exportMetrics(format: 'json' | 'csv' = 'json'): Promise<string> {
    const stats = this.getPerformanceStats();
    const exportData = {
      timestamp: new Date().toISOString(),
      deviceInfo: this.deviceInfo,
      stats,
      metrics: this.metrics.map(metric => ({
        ...metric,
        metadata: this.sanitizeData(metric.metadata)
      })),
      traces: this.traces.map(trace => ({
        ...trace,
        attributes: this.sanitizeData(trace.attributes)
      }))
    };

    if (format === 'csv') {
      // Convertir en CSV
      const headers = ['timestamp', 'name', 'duration', 'status', 'statusCode', 'networkType'];
      const rows = this.metrics.map(metric => [
        new Date(metric.endTime).toISOString(),
        metric.name,
        metric.duration,
        metric.status,
        metric.statusCode || '',
        metric.networkType || ''
      ]);

      return [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n');
    }

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Compresser les données si nécessaire
   */
  private async compressPayload(data: string): Promise<string> {
    if (data.length < COMPRESSION_THRESHOLD) {
      return data;
    }

    try {
      const encoder = new TextEncoder();
      const input = encoder.encode(data);
      const compressed = pako.deflate(input);
      const compressionRatio = data.length / compressed.length;
      
      this.internalMetrics.compressionRatio = compressionRatio;
      this.internalMetrics.lastCompressedSize = compressed.length;
      this.internalMetrics.lastOriginalSize = data.length;
      this.internalMetrics.totalBytesSaved += (data.length - compressed.length);
      
      return Buffer.from(compressed).toString('base64');
    } catch (error) {
      console.warn('Erreur lors de la compression:', error);
      return data;
    }
  }

  /**
   * Décompresser les données
   */
  private async decompressPayload(data: string, encoding: string): Promise<string> {
    if (encoding !== 'deflate') {
      return data;
    }

    try {
      const compressed = Buffer.from(data, 'base64');
      const decompressed = pako.inflate(compressed);
      const decoder = new TextDecoder();
      return decoder.decode(decompressed);
    } catch (error) {
      console.warn('Erreur lors de la décompression:', error);
      return data;
    }
  }

  /**
   * Calculer le délai de retry avec backoff exponentiel
   */
  private calculateRetryDelay(attempt: number): number {
    return Math.min(
      RETRY_DELAY_BASE * Math.pow(2, attempt),
      30000 // Max 30 secondes
    );
  }

  /**
   * Planifier une nouvelle tentative d'envoi
   */
  private scheduleRetry(payload: MonitoringPayload, config: RetryConfig): void {
    const retryId = this.generateId();
    const delay = this.calculateRetryDelay(config.attempt);

    // Nettoyer tout retry précédent
    if (this.retryTimeouts.size > 0) {
      for (const [, timeout] of this.retryTimeouts) {
        clearTimeout(timeout);
      }
      this.retryTimeouts.clear();
    }

    const timeout = setTimeout(async () => {
      this.retryTimeouts.delete(retryId);
      await this.sendMetricsWithRetry(payload, {
        ...config,
        attempt: config.attempt + 1
      });
    }, delay);

    this.retryTimeouts.set(retryId, timeout);
  }

  /**
   * Envoyer les métriques avec gestion des retries
   */
  private async sendMetricsWithRetry(payload: MonitoringPayload, retryConfig?: RetryConfig): Promise<boolean> {
    const config: RetryConfig = retryConfig || {
      attempt: 0,
      maxAttempts: MAX_RETRY_ATTEMPTS,
      delay: RETRY_DELAY_BASE
    };

    if (!this.config.serverEndpoint) {
      return false;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), DEFAULT_TIMEOUT);
    const startTime = Date.now();

    try {
      const compressedData = await this.compressPayload(
        JSON.stringify(this.sanitizeData(payload))
      );

      const response = await fetch(this.config.serverEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Encoding': compressedData.length !== JSON.stringify(payload).length ? 'deflate' : 'identity'
        },
        body: compressedData,
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const duration = Date.now() - startTime;
      
      this.updatePerformanceMetrics(duration);
      this.updateNetworkMetrics(undefined, duration);

      if (!response.ok) {
        throw new Error(`Échec de l'envoi des métriques: ${response.status}`);
      }

      this.updateRetryMetrics(config.delay, true);
      this.internalMetrics.flushSuccesses++;
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateNetworkMetrics(error instanceof Error ? error : new Error(String(error)), duration);
      this.internalMetrics.flushFailures++;

      if (config.attempt < config.maxAttempts) {
        this.scheduleRetry(payload, config);
        return false;
      }

      throw error;
    }
  }

  /**
   * Ajouter des métriques au batch
   */
  private addToBatch(metrics: PerformanceMetric[], traces: TraceEvent[]): void {
    if (!metrics.length && !traces.length) return;

    this.batchQueue.push({
      metrics,
      traces,
      timestamp: Date.now()
    });

    this.scheduleBatchProcessing();
  }

  /**
   * Planifier le traitement du batch
   */
  private scheduleBatchProcessing(): void {
    if (this.batchTimer) return;

    const timeUntilNextBatch = Math.max(
      0,
      this.lastBatchTime + BATCH_INTERVAL - Date.now()
    );

    this.batchTimer = setTimeout(() => {
      void this.processBatch();
    }, timeUntilNextBatch);
  }

  /**
   * Traiter le batch courant
   */
  private async processBatch(): Promise<void> {
    this.batchTimer = null;
    if (!this.batchQueue.length) return;

    const now = Date.now();
    this.lastBatchTime = now;

    // Fusionner les batchs
    const mergedBatch: MetricsBatch = {
      metrics: [],
      traces: [],
      timestamp: now
    };

    while (this.batchQueue.length && 
           mergedBatch.metrics.length < BATCH_SIZE &&
           mergedBatch.traces.length < BATCH_SIZE) {
      const batch = this.batchQueue.shift();
      if (!batch) continue;

      const remainingMetricsSpace = BATCH_SIZE - mergedBatch.metrics.length;
      const remainingTracesSpace = BATCH_SIZE - mergedBatch.traces.length;

      mergedBatch.metrics.push(...batch.metrics.slice(0, remainingMetricsSpace));
      mergedBatch.traces.push(...batch.traces.slice(0, remainingTracesSpace));

      // Si le batch n'a pas été entièrement consommé, le remettre en queue
      if (batch.metrics.length > remainingMetricsSpace || 
          batch.traces.length > remainingTracesSpace) {
        this.batchQueue.unshift({
          metrics: batch.metrics.slice(remainingMetricsSpace),
          traces: batch.traces.slice(remainingTracesSpace),
          timestamp: batch.timestamp
        });
        break;
      }
    }

    // Envoyer le batch
    if (mergedBatch.metrics.length || mergedBatch.traces.length) {
      const payload: MonitoringPayload = {
        device: this.deviceInfo,
        session: {
          id: this.sessionId,
          timestamp: now
        },
        metrics: mergedBatch.metrics,
        traces: mergedBatch.traces
      };

      try {
        await this.sendMetricsWithRetry(payload);
      } catch (error) {
        console.warn('Erreur lors de l\'envoi du batch:', error);
        // Remettre le batch en queue pour réessayer plus tard
        this.batchQueue.unshift(mergedBatch);
      }
    }

    // Planifier le prochain batch si nécessaire
    if (this.batchQueue.length) {
      this.scheduleBatchProcessing();
    }
  }

  /**
   * Envoyer les métriques au serveur
   */
  public async flushMetrics(): Promise<boolean> {
    if (!this.config.enabled || (!this.metrics.length && !this.traces.length)) {
      return false;
    }

    this.internalMetrics.flushAttempts++;
    
    // Stocker pour plus tard si hors ligne
    if (!this.isNetworkAvailable) {
      await this.storeMetricsForLater();
      return false;
    }

    const currentMetrics = [...this.metrics];
    const currentTraces = [...this.traces];
    
    // Réinitialiser les files
    this.metrics = [];
    this.traces = [];

    // Ajouter au batch
    this.addToBatch(currentMetrics, currentTraces);
    
    return true;
  }

  /**
   * Stocker les métriques pour un envoi ultérieur;
   */
  private async storeMetricsForLater(): Promise<void> {
    try {
      if(this.metrics.length) {
        await AsyncStorage.setItem(STORAGE_KEYS.QUEUED_METRICS, JSON.stringify(this.metrics));
      }
      
      if(this.traces.length) {
        await AsyncStorage.setItem(STORAGE_KEYS.QUEUED_TRACES, JSON.stringify(this.traces));
      }
    } catch(error) {
      console.warn('Erreur lors du stockage des métriques pour plus tard', error);
    }
  }

  /**
   * Nettoyer les ressources lors de la désactivation;
   */
  public shutdown(): void {
    if(this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    if(this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
    
    // Traiter le dernier batch
    if (this.batchQueue.length) {
      void this.processBatch();
    }
  }

  /**
   * Obtenir des statistiques sur les performances actuelles;
   */
  public getPerformanceStats(): PerformanceStats {
    // Calculer quelques statistiques de base;
    const successMetrics = this.metrics.filter(m => m.status === 'success');
    const errorMetrics = this.metrics.filter(m => m.status === 'error');
    const cachedMetrics = this.metrics.filter(m => m.cached);
    
    const avgSuccessDuration = successMetrics.length 
      ? successMetrics.reduce((sum, m) => sum + m.duration, 0) / successMetrics.length
      : 0;
      
    return {
      requestCount: this.metrics.length,
      successCount: successMetrics.length,
      errorCount: errorMetrics.length,
      cachedCount: cachedMetrics.length,
      avgDuration: avgSuccessDuration,
      errorRate: this.metrics.length ? errorMetrics.length / this.metrics.length : 0,
      cacheHitRate: this.metrics.length ? cachedMetrics.length / this.metrics.length : 0
    };
  }

  /**
   * Valider une métrique personnalisée
   */
  private validateCustomMetric(name: string, value: number): boolean {
    if (!name || typeof name !== 'string') {
      console.warn('[ApiMonitoring] Nom de métrique invalide');
      return false;
    }

    if (name.length > MAX_METRIC_NAME_LENGTH) {
      console.warn(`[ApiMonitoring] Nom de métrique trop long (max ${MAX_METRIC_NAME_LENGTH} caractères)`);
      return false;
    }

    if (typeof value !== 'number' || isNaN(value)) {
      console.warn('[ApiMonitoring] Valeur de métrique invalide');
      return false;
    }

    if (value > MAX_METRIC_VALUE || value < MIN_METRIC_VALUE) {
      console.warn(`[ApiMonitoring] Valeur de métrique hors limites (${MIN_METRIC_VALUE} à ${MAX_METRIC_VALUE})`);
      return false;
    }

    return true;
  }

  /**
   * Ajouter une métrique personnalisée;
   */
  public addCustomMetric(name: string, value: number, attributes: Omit<CustomMetricAttributes, 'customMetric' | 'value'> = {}): void {
    if (!this.config.enabled || !this.shouldSample()) return;
    
    if (!this.validateCustomMetric(name, value)) return;
    
    const now = Date.now();
    
    // Créer la métrique;
    const metric: PerformanceMetric = {
      name,
      startTime: now,
      endTime: now,
      duration: 0,
      status: 'success',
      metadata: {
        customMetric: true,
        value,
        ...attributes
      }
    };
    
    this.metrics.push(metric);
    this.pruneMetricsIfNeeded();
  }

  /**
   * Nettoyer les métriques plus anciennes qu'une certaine durée
   * @param maxAge Durée maximale en millisecondes (par défaut 24h)
   */
  public cleanOldMetrics(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now();
    const hasMetricsToFlush = this.metrics.length > 0 || this.traces.length > 0;
    
    this.metrics = this.metrics.filter(metric => 
      now - metric.endTime <= maxAge
    );
    
    this.traces = this.traces.filter(trace => 
      trace.endTime ? now - trace.endTime <= maxAge : true
    );
    
    // Forcer un flush si beaucoup de métriques ont été nettoyées
    if (hasMetricsToFlush && typeof this.flushMetrics === 'function') {
      void this.flushMetrics();
    }
  }

  /**
   * Créer une erreur typée pour le monitoring
   */
  private createMonitoringError(message: string, code?: string, details?: Record<string, unknown>): ApiMonitoringError {
    const error = new Error(message) as ApiMonitoringError;
    error.name = 'ApiMonitoringError';
    if (code) error.code = code;
    if (details) error.details = details;
    return error;
  }

  /**
   * Gérer une erreur réseau
   */
  private handleNetworkError(error: unknown): ApiMonitoringError {
    if (error instanceof Error) {
      return this.createMonitoringError(
        error.message,
        'NETWORK_ERROR',
        { originalError: error.name, stack: error.stack }
      );
    }
    
    return this.createMonitoringError(
      'Erreur réseau inconnue',
      'UNKNOWN_NETWORK_ERROR',
      { originalError: String(error) }
    );
  }

  /**
   * Obtenir les métriques internes du monitoring
   */
  public getInternalMetrics() {
    return {
      ...this.internalMetrics,
      retryCount: this.retryTimeouts.size,
      queueSize: this.metrics.length + this.traces.length,
      memoryUsage: process.memoryUsage().heapUsed
    };
  }

  /**
   * Mettre à jour les métriques de performance
   */
  private updatePerformanceMetrics(duration: number): void {
    const { internalMetrics } = this;
    
    internalMetrics.lastFlushDuration = duration;
    internalMetrics.minFlushDuration = Math.min(internalMetrics.minFlushDuration, duration);
    internalMetrics.maxFlushDuration = Math.max(internalMetrics.maxFlushDuration, duration);
    
    // Calculer la moyenne mobile
    const alpha = 0.1; // Facteur de lissage
    internalMetrics.averageFlushDuration = internalMetrics.averageFlushDuration === 0
      ? duration
      : (1 - alpha) * internalMetrics.averageFlushDuration + alpha * duration;
  }

  /**
   * Mettre à jour les métriques de file d'attente
   */
  private updateQueueMetrics(droppedCount: number = 0): void {
    const currentQueueSize = this.metrics.length + this.traces.length;
    this.internalMetrics.peakQueueSize = Math.max(this.internalMetrics.peakQueueSize, currentQueueSize);
    this.internalMetrics.totalMetricsProcessed += this.metrics.length;
    this.internalMetrics.droppedMetrics += droppedCount;
  }

  /**
   * Mettre à jour les métriques réseau
   */
  private updateNetworkMetrics(error?: Error, latency?: number): void {
    if (error) {
      this.internalMetrics.networkErrors++;
      if (error.name === 'AbortError') {
        this.internalMetrics.timeoutErrors++;
      }
    }
    
    if (latency) {
      this.internalMetrics.lastNetworkLatency = latency;
    }
  }

  /**
   * Mettre à jour les métriques de retry
   */
  private updateRetryMetrics(delay: number, success: boolean): void {
    this.internalMetrics.retryAttempts++;
    this.internalMetrics.totalRetryDelay += delay;
    if (success) {
      this.internalMetrics.retrySuccesses++;
    }
  }
}

// Exporter une instance unique;
export const apiMonitoring = ApiMonitoring.getInstance() 