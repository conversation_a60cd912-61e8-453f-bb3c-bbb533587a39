/**
 * Module de OpenTelemetry pour React Native
 * 
 * Ce module permet de collecter et d'exporter des données de télémétrie
 * (traces, spans, métriques) depuis une application React Native.
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

// Énumérations pour les statuts et types
export enum SpanStatusCode {
  UNSET = 0,
  OK = 1,
  ERROR = 2
}

export enum SpanKind {
  INTERNAL = 0,
  SERVER = 1,
  CLIENT = 2,
  PRODUCER = 3,
  CONSUMER = 4
}

export enum MetricType {
  GAUGE = 0,
  COUNTER = 1,
  HISTOGRAM = 2
}

// Types pour les spans et métriques
export interface Span {
  name: string;
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  startTime: number;
  endTime?: number;
  kind: SpanKind;
  status: { 
    code: SpanStatusCode;
    message?: string;
  };
  attributes: Record<string, any>;
}

export interface Metric {
  name: string;
  value: number;
  timestamp: number;
  labels: Record<string, string>;
  type: MetricType;
}

export interface Resource {
  attributes: Record<string, string>;
}

// Configuration par défaut
export interface TelemetryConfig {
  enabled: boolean;
  serviceName: string;
  serviceVersion: string;
  environment: string;
  endpoint: string;
  apiKey?: string;
  samplingRate: number;
  batchSize: number;
  batchIntervalMs: number;
  bufferMaxSize: number;
  debugMode: boolean;
  resource: Resource;
}

// Configuration par défaut
const DEFAULT_CONFIG: TelemetryConfig = {
  enabled: true,
  serviceName: 'react-native-app',
  serviceVersion: '1.0.0',
  environment: 'development',
  endpoint: '',
  samplingRate: 1.0, // 100%
  batchSize: 30,
  batchIntervalMs: 30000, // 30 secondes
  bufferMaxSize: 100,
  debugMode: false,
  resource: {
    attributes: {
      'service.name': 'react-native-app',
      'service.version': '1.0.0',
      'telemetry.sdk.name': 'opentelemetry',
      'telemetry.sdk.language': 'javascript',
      'telemetry.sdk.version': '1.0.0',
    }
  }
};

/**
 * Classe Singleton pour gérer la télémétrie
 */
export class OpenTelemetry {
  private static instance: OpenTelemetry;
  private config: TelemetryConfig;
  private spans: Span[] = [];
  private metrics: Metric[] = [];
  private isInitialized: boolean = false;
  private isNetworkAvailable: boolean = true;
  private batchTimer: NodeJS.Timeout | null = null;
  
  private constructor() {
    this.config = { ...DEFAULT_CONFIG };
  }

  public static getInstance(): OpenTelemetry {
    if (!OpenTelemetry.instance) {
      OpenTelemetry.instance = new OpenTelemetry();
    }
    return OpenTelemetry.instance;
  }

  /**
   * Initialise la télémétrie avec la configuration fournie
   */
  public async init(config: Partial<TelemetryConfig> = {}): Promise<void> {
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    // Met à jour les attributs de ressource
    if (config.serviceName) {
      this.config.resource.attributes['service.name'] = config.serviceName;
    }
    
    if (config.serviceVersion) {
      this.config.resource.attributes['service.version'] = config.serviceVersion;
    }

    // Surveille l'état du réseau
    NetInfo.addEventListener(state => {
      this.isNetworkAvailable = state.isConnected ?? false;
      
      // Si le réseau est de nouveau disponible, tente d'exporter les données
      if (this.isNetworkAvailable) {
        this.exportBatch().catch(err => {
          if (this.config.debugMode) {
            console.error('[OpenTelemetry] Erreur lors de l\'exportation:', err);
          }
        });
      }
    });

    // Charge les données précédemment mises en file d'attente
    await this.loadQueuedData();

    // Démarre le timer pour l'exportation périodique
    if (this.config.enabled && !this.batchTimer) {
      this.batchTimer = setInterval(() => {
        this.exportBatch().catch(err => {
          if (this.config.debugMode) {
            console.error('[OpenTelemetry] Erreur lors de l\'exportation par lot:', err);
          }
        });
      }, this.config.batchIntervalMs);
    }

    this.isInitialized = true;
  }

  /**
   * Gère un événement de requête API
   */
  public handleApiRequestEvent(
    url: string,
    method: string,
    headers: Record<string, any>,
    body?: any,
    startTime: number = Date.now()
  ): Span {
    const span = this.createRootSpan('http.request', {
      'http.url': this.sanitizeUrl(url),
      'http.method': method,
      'http.host': this.extractHostname(url),
      'http.request_content_length': body ? JSON.stringify(body).length : 0,
      'http.headers': this.sanitizeHeaders(headers),
    });

    if (body) {
      try {
        // Ne pas inclure le corps complet, seulement sa taille
        span.attributes['http.request.body.size'] = JSON.stringify(body).length;
      } catch (e) {
        // Ignorer les erreurs de sérialisation
      }
    }

    return span;
  }

  /**
   * Termine un span de requête API avec les informations de réponse
   */
  public handleApiResponseEvent(
    span: Span,
    status: number,
    headers?: Record<string, any>,
    body?: any,
    error?: Error
  ): void {
    if (!span) return;

    const attributes: Record<string, any> = {
      'http.status_code': status,
    };

    if (headers) {
      attributes['http.response.headers'] = this.sanitizeHeaders(headers);
    }

    if (body) {
      try {
        // Ne pas inclure le corps complet, seulement sa taille
        attributes['http.response.body.size'] = JSON.stringify(body).length;
      } catch (e) {
        // Ignorer les erreurs de sérialisation
      }
    }

    this.addSpanAttributes(span, attributes);

    if (error || status >= 400) {
      this.endSpan(span, SpanStatusCode.ERROR, error?.message || `HTTP Error ${status}`);
    } else {
      this.endSpan(span, SpanStatusCode.OK);
    }
  }

  /**
   * Détermine si l'échantillonnage doit être effectué selon le taux configuré
   */
  private shouldSample(): boolean {
    return Math.random() < this.config.samplingRate;
  }

  /**
   * Génère un ID de trace unique
   */
  private generateTraceId(): string {
    // 16 octets (32 caractères hexadécimaux)
    let traceId = '';
    for (let i = 0; i < 32; i++) {
      traceId += Math.floor(Math.random() * 16).toString(16);
    }
    return traceId;
  }

  /**
   * Génère un ID de span unique
   */
  private generateSpanId(): string {
    // 8 octets (16 caractères hexadécimaux)
    let spanId = '';
    for (let i = 0; i < 16; i++) {
      spanId += Math.floor(Math.random() * 16).toString(16);
    }
    return spanId;
  }

  /**
   * Extrait le nom d'hôte d'une URL
   */
  private extractHostname(url?: string): string {
    if (!url) return '';
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (e) {
      return '';
    }
  }

  /**
   * Assaini les en-têtes pour éviter de capturer des informations sensibles
   */
  private sanitizeHeaders(headers: Record<string, any> = {}): Record<string, string> {
    const sanitized: Record<string, string> = {};
    
    Object.entries(headers).forEach(([key, value]) => {
      if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        if (key.toLowerCase().includes('auth') && typeof value === 'string') {
          sanitized[key] = value.startsWith('Bearer ') ? 'Bearer [REDACTED]' : '[REDACTED]';
        } else if (key.toLowerCase().includes('token') || key.toLowerCase().includes('key')) {
          sanitized[key] = '[REDACTED]';
        } else {
          sanitized[key] = String(value);
        }
      }
    });
    
    return sanitized;
  }

  /**
   * Assaini une URL pour supprimer les informations sensibles
   */
  private sanitizeUrl(url?: string): string {
    if (!url) return '';
    try {
      const urlObj = new URL(url);
      
      // Masquer les paramètres sensibles
      const searchParams = new URLSearchParams(urlObj.search);
      const sensitiveParams = ['token', 'key', 'password', 'secret', 'auth'];
      
      sensitiveParams.forEach(param => {
        if (searchParams.has(param)) {
          searchParams.set(param, '[REDACTED]');
        }
      });
      
      urlObj.search = searchParams.toString();
      return urlObj.toString();
    } catch (e) {
      return url;
    }
  }

  /**
   * Réduit la taille des données si nécessaire
   */
  private pruneDataIfNeeded(): void {
    if (this.spans.length > this.config.bufferMaxSize) {
      this.spans = this.spans.slice(-Math.floor(this.config.bufferMaxSize / 2));
    }
    
    if (this.metrics.length > this.config.bufferMaxSize) {
      this.metrics = this.metrics.slice(-Math.floor(this.config.bufferMaxSize / 2));
    }
  }

  /**
   * Charge les données mises en file d'attente depuis le stockage local
   */
  private async loadQueuedData(): Promise<void> {
    try {
      const queuedSpans = await AsyncStorage.getItem('@OpenTelemetry:queuedSpans');
      const queuedMetrics = await AsyncStorage.getItem('@OpenTelemetry:queuedMetrics');
      
      if (queuedSpans) {
        const spans = JSON.parse(queuedSpans);
        this.spans.push(...spans);
      }
      
      if (queuedMetrics) {
        const metrics = JSON.parse(queuedMetrics);
        this.metrics.push(...metrics);
      }
      
      // Vider les données mises en file d'attente
      await AsyncStorage.multiRemove(['@OpenTelemetry:queuedSpans', '@OpenTelemetry:queuedMetrics']);
    } catch (error) {
      if (this.config.debugMode) {
        console.error('[OpenTelemetry] Erreur lors du chargement des données mises en file d\'attente:', error);
      }
    }
  }

  /**
   * Stocke les données pour une exportation ultérieure
   */
  private async storeDataForLater(): Promise<void> {
    try {
      if (this.spans.length) {
        await AsyncStorage.setItem('@OpenTelemetry:queuedSpans', JSON.stringify(this.spans));
      }
      
      if (this.metrics.length) {
        await AsyncStorage.setItem('@OpenTelemetry:queuedMetrics', JSON.stringify(this.metrics));
      }
    } catch (error) {
      if (this.config.debugMode) {
        console.error('[OpenTelemetry] Erreur lors du stockage des données:', error);
      }
    }
  }

  /**
   * Exporte un lot de données de télémétrie
   */
  public async exportBatch(): Promise<boolean> {
    if (!this.config.enabled || (!this.spans.length && !this.metrics.length)) {
      return true;
    }
    
    // Vérifier si le réseau est disponible
    if (!this.isNetworkAvailable) {
      await this.storeDataForLater();
      return false;
    }
    
    // Si le mode debug est activé mais pas d'endpoint, juste imprimer les données
    if (!this.config.endpoint && this.config.debugMode) {
      console.log(
        '[OpenTelemetry] Données à exporter (pas d\'endpoint configuré):',
        {
          spans: this.spans,
          metrics: this.metrics,
        }
      );
      
      this.spans = [];
      this.metrics = [];
      return true;
    }
    
    // Si pas d'endpoint configuré, stocke pour plus tard
    if (!this.config.endpoint) {
      if (this.config.debugMode) {
        console.warn('[OpenTelemetry] Pas d\'endpoint configuré pour l\'exportation');
      }
      
      await this.storeDataForLater();
      return false;
    }
    
    try {
      // Préparer les données à exporter
      const batchSize = this.config.batchSize;
      const spansToExport = this.spans.slice(0, batchSize);
      const metricsToExport = this.metrics.slice(0, batchSize);
      
      // Si rien à exporter, simplement retourner
      if (!spansToExport.length && !metricsToExport.length) {
        return true;
      }
      
      // Données à envoyer
      const payload = {
        resource: this.config.resource,
        spans: spansToExport,
        metrics: metricsToExport,
      };
      
      // En-têtes pour la requête
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      // Ajouter la clé API si configurée
      if (this.config.apiKey) {
        headers['Authorization'] = `Bearer ${this.config.apiKey}`;
      }
      
      // Effectuer la requête
      const response = await fetch(this.config.endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      
      // Si l'exportation réussit, mettre à jour les tableaux
      this.spans = this.spans.slice(spansToExport.length);
      this.metrics = this.metrics.slice(metricsToExport.length);
      
      return true;
    } catch (error) {
      // En cas d'erreur, stocker les données pour une exportation ultérieure
      if (this.config.debugMode) {
        console.error('[OpenTelemetry] Erreur lors de l\'exportation des données:', error);
      }
      
      await this.storeDataForLater();
      return false;
    }
  }

  /**
   * Crée un span racine
   */
  public createRootSpan(name: string, attributes: Record<string, any> = {}): Span {
    if (!this.config.enabled || !this.shouldSample()) {
      // Retourner un span nul si désactivé ou non échantillonné
      return {
        name,
        traceId: '0',
        spanId: '0',
        startTime: Date.now(),
        kind: SpanKind.INTERNAL,
        status: { code: SpanStatusCode.UNSET },
        attributes: {},
      };
    }
    
    const span: Span = {
      name,
      traceId: this.generateTraceId(),
      spanId: this.generateSpanId(),
      startTime: Date.now(),
      kind: SpanKind.INTERNAL,
      status: {
        code: SpanStatusCode.UNSET,
      },
      attributes: {
        ...attributes,
        'service.name': this.config.serviceName,
        'service.version': this.config.serviceVersion,
        'service.environment': this.config.environment,
      },
    };
    
    this.spans.push(span);
    this.pruneDataIfNeeded();
    
    return span;
  }

  /**
   * Crée un span enfant
   */
  public createChildSpan(name: string, parentSpan: Span, attributes: Record<string, any> = {}): Span {
    if (!this.config.enabled || !parentSpan || parentSpan.traceId === '0') {
      // Retourner un span nul si désactivé ou parent invalide
      return {
        name,
        traceId: '0',
        spanId: '0',
        startTime: Date.now(),
        kind: SpanKind.INTERNAL,
        status: { code: SpanStatusCode.UNSET },
        attributes: {},
      };
    }
    
    const span: Span = {
      name,
      traceId: parentSpan.traceId,
      spanId: this.generateSpanId(),
      parentSpanId: parentSpan.spanId,
      startTime: Date.now(),
      kind: SpanKind.INTERNAL,
      status: {
        code: SpanStatusCode.UNSET,
      },
      attributes: {
        ...attributes,
        'service.name': this.config.serviceName,
        'service.version': this.config.serviceVersion,
        'service.environment': this.config.environment,
      },
    };
    
    this.spans.push(span);
    this.pruneDataIfNeeded();
    
    return span;
  }

  /**
   * Termine un span avec un statut et un message optionnel
   */
  public endSpan(span: Span, status: SpanStatusCode = SpanStatusCode.OK, message?: string): void {
    if (!this.config.enabled || !span.traceId) { return; }
    
    const spanToUpdate = this.spans.find(s => s.spanId === span.spanId && s.traceId === span.traceId);
    
    if (spanToUpdate) {
      spanToUpdate.endTime = Date.now();
      spanToUpdate.status = {
        code: status,
        message: message,
      };
    }
  }

  /**
   * Ajoute des attributs à un span existant
   */
  public addSpanAttributes(span: Span, attributes: Record<string, any>): void {
    if (!this.config.enabled || !span.traceId) { return; }
    
    const spanToUpdate = this.spans.find(s => s.spanId === span.spanId && s.traceId === span.traceId);
    
    if (spanToUpdate) {
      spanToUpdate.attributes = {
        ...spanToUpdate.attributes,
        ...attributes,
      };
    }
  }

  /**
   * Enregistre une métrique
   */
  public recordMetric(
    name: string,
    value: number,
    labels: Record<string, string> = {},
    type: MetricType = MetricType.GAUGE
  ): void {
    if (!this.config.enabled || !this.shouldSample()) { return; }
    
    const metric: Metric = {
      name,
      value,
      timestamp: Date.now(),
      labels: {
        'service.name': this.config.serviceName,
        'service.version': this.config.serviceVersion,
        'service.environment': this.config.environment,
        ...labels,
      },
      type,
    };
    
    this.metrics.push(metric);
    this.pruneDataIfNeeded();
  }

  /**
   * Force l'exportation des données
   */
  public async flush(): Promise<boolean> {
    return this.exportBatch();
  }

  /**
   * Récupère les métriques collectées
   */
  public getMetrics(): Record<string, any> {
    // Regrouper les métriques par nom
    const metricsMap: Record<string, any> = {};
    
    this.metrics.forEach(metric => {
      if (!metricsMap[metric.name]) {
        metricsMap[metric.name] = [];
      }
      
      metricsMap[metric.name].push({
        value: metric.value,
        timestamp: metric.timestamp,
        labels: metric.labels,
        type: metric.type
      });
    });
    
    return metricsMap;
  }

  /**
   * Arrête la télémétrie et exporte les données restantes
   */
  public shutdown(): void {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
      this.batchTimer = null;
    }
    
    // Tenter d'exporter les données restantes
    this.exportBatch().catch(err => {
      if (this.config.debugMode) {
        console.error('[OpenTelemetry] Erreur lors de l\'exportation finale:', err);
      }
    });
  }
}

// Exporte l'instance singleton
export default OpenTelemetry.getInstance();