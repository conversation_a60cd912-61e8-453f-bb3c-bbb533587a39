import { AppError, NetworkError, ValidationError, logError, isNetworkError, formatError } from '../../errors';

/**
 * Service d'API pour l'exemple
 */
export class ApiService {
  private apiUrl = 'https://api.retreat-and-be.com';
  
  /**
   * Méthode HTTP générique avec gestion des erreurs
   */
  private async fetchWithErrorHandling<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    try {
      const response = await fetch(`${this.apiUrl}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });
      
      // Vérifier si la requête a réussi
      if (!response.ok) {
        // Analyser la réponse d'erreur JSON
        const errorData = await response.json().catch(() => ({}));
        
        // Transformer en AppError appropriée
        switch (response.status) {
          case 400:
            throw new ValidationError(
              errorData.message || 'Validation error', 
              errorData.details || {}
            );
          case 401:
            throw new AppError(
              'You must be logged in to access this resource', 
              401
            );
          case 403:
            throw new AppError(
              'You do not have permission to access this resource', 
              403
            );
          case 404:
            throw new AppError(
              errorData.message || 'Resource not found', 
              404
            );
          default:
            throw new AppError(
              errorData.message || 'An error occurred with the API', 
              response.status
            );
        }
      }
      
      // Analyser la réponse JSON
      return await response.json();
    } catch (error) {
      // Si c'est une erreur réseau (hors ligne, timeout, etc.)
      if (error instanceof TypeError && error.message.includes('network')) {
        throw new NetworkError('Unable to connect to the server');
      }
      
      // Laisser passer les erreurs déjà traitées
      if (error instanceof AppError) {
        throw error;
      }
      
      // Journaliser et transformer les erreurs non gérées
      logError(error, { context: 'API request', endpoint });
      throw new AppError(
        'An unexpected error occurred while communicating with the server',
        500
      );
    }
  }
  
  /**
   * Récupère un utilisateur par son ID
   */
  async getUser(userId: string) {
    try {
      return await this.fetchWithErrorHandling<any>(`/users/${userId}`);
    } catch (error) {
      // Exemple de gestion spécifique d'erreur
      if (isNetworkError(error)) {
        // Tenter de récupérer depuis le cache
        return this.getUserFromCache(userId);
      }
      throw error;
    }
  }
  
  /**
   * Crée un nouvel utilisateur
   */
  async createUser(userData: any) {
    return this.fetchWithErrorHandling<any>('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }
  
  /**
   * Met à jour un utilisateur
   */
  async updateUser(userId: string, updates: any) {
    return this.fetchWithErrorHandling<any>(`/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }
  
  /**
   * Supprime un utilisateur
   */
  async deleteUser(userId: string) {
    return this.fetchWithErrorHandling<any>(`/users/${userId}`, {
      method: 'DELETE',
    });
  }
  
  /**
   * Exemple de récupération depuis le cache
   */
  private getUserFromCache(userId: string) {
    console.log('Retrieving user from cache:', userId);
    return { id: userId, username: 'cached-user', isCached: true };
  }
}

/**
 * Exemple d'utilisation dans un composant React
 * 
 * ```tsx
 * import React, { useState, useEffect } from 'react';
 * import { Text, Button, ActivityIndicator, View } from 'react-native';
 * import { ApiService } from '../services/api';
 * import { AppError, isNetworkError, getErrorMessage } from '../utils/errors';
 * 
 * const UserProfileScreen = ({ userId }) => {
 *   const [user, setUser] = useState(null);
 *   const [loading, setLoading] = useState(true);
 *   const [error, setError] = useState(null);
 *   const api = new ApiService();
 *   
 *   useEffect(() => {
 *     loadUser();
 *   }, [userId]);
 *   
 *   const loadUser = async () => {
 *     try {
 *       setLoading(true);
 *       setError(null);
 *       const userData = await api.getUser(userId);
 *       setUser(userData);
 *     } catch (error) {
 *       setError(error);
 *       // Vous pouvez également utiliser reportError ici pour les erreurs critiques
 *     } finally {
 *       setLoading(false);
 *     }
 *   };
 *   
 *   if (loading) {
 *     return <ActivityIndicator size="large" />;
 *   }
 *   
 *   if (error) {
 *     return (
 *       <View>
 *         <Text>Error: {getErrorMessage(error)}</Text>
 *         <Text>{isNetworkError(error) ? 'You are offline. Showing cached data.' : ''}</Text>
 *         <Button title="Retry" onPress={loadUser} />
 *       </View>
 *     );
 *   }
 *   
 *   return (
 *     <View>
 *       <Text>Username: {user.username}</Text>
 *       {user.isCached && <Text>(Offline Mode)</Text>}
 *     </View>
 *   );
 * };
 * ```
 */ 