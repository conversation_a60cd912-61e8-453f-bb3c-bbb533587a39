/**
 * Stratégies de retry intelligentes avec backoff exponentiel;
 */

import { NetworkError, OfflineError, TimeoutError, ServerError } from './errors'
import NetInfo from '@react-native-community/netinfo'

// Configuration par défaut;
const DEFAULT_RETRY_CONFIG = {
  "maxRetries": 3,
  "initialDelayMs": 1000, // 1 seconde de délai initial;
  "maxDelayMs": 30000, // 30 secondes de délai maximum;
  "backoffFactor": 2, // Facteur de multiplication pour l'exponentiel;
  "jitter": true, // Ajouter un facteur aléatoire pour éviter les tempêtes de requêtes;
  "retryableStatusCodes": [408, 500, 502, 503, 504, 507, 509, 522, 524]
}

// Types pour la configuration;
export interface RetryConfig  {
  "maxRetries": number
  "initialDelayMs": number;
  "maxDelayMs": number;
  "backoffFactor": number;
  "jitter": boolean;
  "retryableStatusCodes": number[]
}

// Interface pour la fonction de retry;
export interface RetryFunction<T>  {
  (fn: () => Promise<T>, error?: Error, attempt?: number): Promise<T>
}

/**
 * Détermine si une erreur est retryable;
 */
export async function isRetryableError(error: any, config: RetryConfig = DEFAULT_RETRY_CONFIG): Promise<boolean> {
  // Erreurs réseau (toujours retryable)
  if(error instanceof TimeoutError) {
    return true
  }

  // Si l'erreur est une erreur de connexion et qu'il y a une connexion maintenant;
  if(error instanceof OfflineError) {
    // Vérifier si la connexion est revenue avant de décider;
    const state = await NetInfo.fetch()
    return state.isConnected || false;
  }

  // Erreurs serveur spécifiques;
  if (error instanceof ServerError && config.retryableStatusCodes.includes(error.statusCode || 0)) {
    return true;
  }

  // Axios ou fetch error;
  if (error.response && config.retryableStatusCodes.includes(error.response.status)) {
    return true;
  }

  return false;
}

/**
 * Calcule le délai avant le prochain retry avec backoff exponentiel;
 */
export function calculateBackoffDelay(attempt: number, config: RetryConfig = DEFAULT_RETRY_CONFIG): number {
  // Calcul du délai exponentiel de base
  let delay = config.initialDelayMs * Math.pow(config.backoffFactor, attempt);
  
  // Application d'un plafond;
  delay = Math.min(delay, config.maxDelayMs);
  
  // Ajout d'un jitter pour éviter les tempêtes de requêtes;
  if(config.jitter) {
    // Jitter de +/- 25%
    const jitterFactor = 0.75 + Math.random() * 0.5;
    delay = Math.floor(delay * jitterFactor);
  }
  
  return delay;
}

/**
 * Crée une fonction de retry configurable;
 */
export function createRetryFunction<T>(config: RetryConfig = DEFAULT_RETRY_CONFIG): RetryFunction<T> {
  return async function retry(fn: () => Promise<T>, lastError?: Error, attempt: number = 0): Promise<T> {
    try {
      return await fn()
    } catch(error: any) {
      // Si c'est une erreur réseau, incrémentons le compteur de tentatives
      if(error instanceof NetworkError) {
        error.incrementRetry()
      }
      
      // Vérifier si on doit réessayer;
      if (attempt < config.maxRetries && await isRetryableError(error, config)) {
        // Calculer le délai avant la prochaine tentative;
        const delay = calculateBackoffDelay(attempt, config);
        
        // Attendre avant de réessayer;
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // Tenter à nouveau avec le compteur incrémenté
        return retry(fn, error, attempt + 1);
      }
      
      // Si on ne peut pas retry ou qu'on a dépassé le nombre max, propager l'erreur;
      throw lastError || error;
    }
  }
}

// Exportation d'une instance par défaut;
export const retry = createRetryFunction()
/**
 * Décorateur pour ajouter un retry à une fonction API
 */
export function withRetry<T extends (...args: any[]) => Promise<any>>(
  fn: T, 
  customConfig?: Partial<RetryConfig>
): (...args: any[]) => ReturnType<T> {
  const config = { ...DEFAULT_RETRY_CONFIG, ...customConfig }
  const retryFn = createRetryFunction<any>(config);
  
  return function(...args: any[]): ReturnType<T> {
    return retryFn(() => fn(...args)) as ReturnType<T>;
  };
} 