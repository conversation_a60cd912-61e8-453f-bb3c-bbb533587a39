import { Model } from '@nozbe/watermelondb';
import { SyncConflict } from '../types/sync';
import database from '../database';

// Interface pour étendre Model avec nos propriétés personnalisées
interface ModelWithTimestamps extends Model {
  updatedAt: string | number | Date;
}

export type ConflictResolutionStrategy = 
  | 'server_wins' 
  | 'client_wins' 
  | 'merge' 
  | 'manual';

/**
 * Résoud automatiquement un conflit selon la stratégie spécifiée
 * @param conflict Le conflit à résoudre
 * @param strategy La stratégie de résolution à appliquer
 * @returns L'enregistrement résolu
 */
export const resolveConflict = async <T extends Model>(
  conflict: SyncConflict<T>,
  strategy: ConflictResolutionStrategy
): Promise<T> => {
  const { clientRecord, serverData, tableName, id } = conflict;
  
  // Collection pour le type d'entité en conflit
  const collection = database.get<T>(tableName);
  
  switch (strategy) {
    case 'server_wins':
      // Appliquer les données du serveur directement
      return await applyServerData(clientRecord, serverData);
      
    case 'client_wins':
      // Garder les données locales telles quelles
      return clientRecord;
      
    case 'merge':
      // Fusionner intelligemment les deux versions
      return await mergeData(clientRecord, serverData);
      
    case 'manual':
      // Ne rien faire, cela sera géré par l'interface utilisateur
      return clientRecord;
      
    default:
      throw new Error(`Stratégie de résolution non reconnue: ${strategy}`);
  }
};

/**
 * Applique les données du serveur à l'enregistrement local
 */
const applyServerData = async <T extends Model>(
  clientRecord: T, 
  serverData: Record<string, any>
): Promise<T> => {
  let updatedRecord: T;
  
  await database.write(async () => {
    updatedRecord = await clientRecord.update((record: any) => {
      Object.keys(serverData).forEach(key => {
        // Ne pas écraser les clés spéciales de WatermelonDB
        if (!key.startsWith('_')) {
          record[key] = serverData[key];
        }
      });
    });
  });
  
  return updatedRecord!;
};

/**
 * Fusionne intelligemment les données locales et distantes
 */
const mergeData = async <T extends Model>(
  clientRecord: T, 
  serverData: Record<string, any>
): Promise<T> => {
  let updatedRecord: T;
  
  await database.write(async () => {
    updatedRecord = await clientRecord.update((record: any) => {
      Object.keys(serverData).forEach(key => {
        // Ne pas écraser les clés spéciales de WatermelonDB
        if (!key.startsWith('_')) {
          // Stratégie simple: préférer la version la plus récente
          // On pourrait implémenter une logique plus complexe ici
          const serverUpdatedAt = new Date(serverData.updated_at || 0).getTime();
          const clientUpdatedAt = new Date((record as ModelWithTimestamps).updatedAt || 0).getTime();
          
          if (serverUpdatedAt > clientUpdatedAt) {
            record[key] = serverData[key];
          }
          // Sinon, conserver la valeur locale
        }
      });
    });
  });
  
  return updatedRecord!;
};

/**
 * Récupère un conflit spécifique par ID
 */
export const getConflictById = async <T extends Model>(
  conflictId: string,
  conflicts: SyncConflict<T>[]
): Promise<SyncConflict<T> | undefined> => {
  return conflicts.find(conflict => conflict.id === conflictId);
};

/**
 * Résout tous les conflits d'une entité selon une stratégie donnée
 */
export const resolveAllConflictsForTable = async <T extends Model>(
  tableName: string,
  conflicts: SyncConflict<T>[],
  strategy: ConflictResolutionStrategy
): Promise<T[]> => {
  const tableConflicts = conflicts.filter(conflict => conflict.tableName === tableName);
  
  const resolved = await Promise.all(
    tableConflicts.map(conflict => resolveConflict(conflict, strategy))
  );
  
  return resolved;
}; 