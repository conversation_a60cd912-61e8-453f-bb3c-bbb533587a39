import * as RNFS from 'react-native-fs';
import { zip, unzip } from 'react-native-zip-archive';
import { Share, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Database } from '@nozbe/watermelondb';
import { EventEmitter } from './eventEmitter';

// Événements liés aux sauvegardes
export const BACKUP_EVENTS = {
  BACKUP_STARTED: 'backup:started',
  BACKUP_COMPLETED: 'backup:completed',
  BACKUP_FAILED: 'backup:failed',
  RESTORE_STARTED: 'restore:started',
  RESTORE_COMPLETED: 'restore:completed',
  RESTORE_FAILED: 'restore:failed',
};

class BackupManager {
  private static instance: BackupManager;
  private database: Database | null = null;
  private tempDir: string;
  private backupDir: string;

  private constructor() {
    // Répertoires de travail
    this.tempDir = `${RNFS.CachesDirectoryPath}/backup_temp`;
    this.backupDir = `${RNFS.DocumentDirectoryPath}/backups`;
    
    // Initialiser les répertoires
    this.initializeDirectories();
  }

  /**
   * Récupère ou crée l'instance unique de BackupManager (singleton)
   */
  public static getInstance(): BackupManager {
    if (!BackupManager.instance) {
      BackupManager.instance = new BackupManager();
    }
    return BackupManager.instance;
  }

  /**
   * Initialise la classe avec une référence à la base de données
   */
  public initialize(database: Database): void {
    this.database = database;
  }

  /**
   * Crée les répertoires nécessaires s'ils n'existent pas
   */
  private async initializeDirectories(): Promise<void> {
    try {
      const tempDirExists = await RNFS.exists(this.tempDir);
      const backupDirExists = await RNFS.exists(this.backupDir);

      if (!tempDirExists) {
        await RNFS.mkdir(this.tempDir);
      }

      if (!backupDirExists) {
        await RNFS.mkdir(this.backupDir);
      }
    } catch (error) {
      console.error("Erreur lors de l'initialisation des répertoires:", error);
    }
  }

  /**
   * Crée une sauvegarde complète et la partage
   */
  public async createBackup(): Promise<string> {
    if (!this.database) {
      throw new Error("La base de données n'a pas été initialisée");
    }

    EventEmitter.emit(BACKUP_EVENTS.BACKUP_STARTED);

    try {
      // Nettoyer le répertoire temporaire
      await this.cleanTempDirectory();

      // Exporter les données de la base WatermelonDB (propriétaire du format)
      await this.database.write(async () => {
        const dbPath = `${this.tempDir}/watermelon.db`;
        // Cette méthode est spécifique à l'implémentation - à ajuster selon la doc WatermelonDB
        // await this.database.exportToFile(dbPath);
      });

      // Exporter les données d'AsyncStorage
      const allKeys = await AsyncStorage.getAllKeys();
      const allItems = await AsyncStorage.multiGet(allKeys);
      const asyncStorageData = JSON.stringify(allItems);
      await RNFS.writeFile(`${this.tempDir}/asyncstorage.json`, asyncStorageData, 'utf8');

      // Créer un manifest avec des métadonnées
      const manifest = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        appVersion: '1.0.0', // À remplacer par la version réelle de l'app
      };
      await RNFS.writeFile(`${this.tempDir}/manifest.json`, JSON.stringify(manifest), 'utf8');

      // Créer l'archive ZIP
      const timestamp = new Date().getTime();
      const backupFilename = `backup_${timestamp}.zip`;
      const backupPath = `${this.backupDir}/${backupFilename}`;

      await zip(this.tempDir, backupPath);

      // Nettoyer après la sauvegarde
      await this.cleanTempDirectory();

      EventEmitter.emit(BACKUP_EVENTS.BACKUP_COMPLETED, backupPath);
      return backupPath;
    } catch (error) {
      console.error('Erreur lors de la création de la sauvegarde:', error);
      EventEmitter.emit(BACKUP_EVENTS.BACKUP_FAILED, error);
      throw error;
    }
  }

  /**
   * Partage la sauvegarde créée
   */
  public async shareBackup(backupPath: string): Promise<void> {
    try {
      if (Platform.OS === 'android') {
        const shareOptions = {
          title: 'Partager la sauvegarde',
          url: `file://${backupPath}`,
          type: 'application/zip',
        };
        await Share.share(shareOptions);
      } else {
        // iOS utilise un chemin de fichier différent
        const shareOptions = {
          title: 'Partager la sauvegarde',
          url: backupPath,
          type: 'application/zip',
        };
        await Share.share(shareOptions);
      }
    } catch (error) {
      console.error('Erreur lors du partage de la sauvegarde:', error);
      throw error;
    }
  }

  /**
   * Restaure les données à partir d'une sauvegarde
   */
  public async restoreBackup(backupPath: string): Promise<void> {
    if (!this.database) {
      throw new Error("La base de données n'a pas été initialisée");
    }

    EventEmitter.emit(BACKUP_EVENTS.RESTORE_STARTED);

    try {
      // Nettoyer le répertoire temporaire
      await this.cleanTempDirectory();

      // Extraire l'archive
      await unzip(backupPath, this.tempDir);

      // Vérifier le manifest
      const manifestPath = `${this.tempDir}/manifest.json`;
      const manifestExists = await RNFS.exists(manifestPath);
      
      if (!manifestExists) {
        throw new Error('Fichier de sauvegarde invalide: manifest.json manquant');
      }

      const manifestContent = await RNFS.readFile(manifestPath, 'utf8');
      const manifest = JSON.parse(manifestContent);

      // Restaurer les données WatermelonDB
      const dbPath = `${this.tempDir}/watermelon.db`;
      const dbExists = await RNFS.exists(dbPath);
      
      if (dbExists) {
        if (!this.database) {
          console.warn('Database is not initialized for restore operation');
        } else {
          await this.database.write(async () => {
            // Reset database first
            await this.database!.unsafeResetDatabase();
            
            // Cette méthode est spécifique à l'implémentation - à ajuster selon la doc WatermelonDB
            // await this.database.importFromFile(dbPath);
          });
        }
      }

      // Restaurer AsyncStorage
      const asyncStoragePath = `${this.tempDir}/asyncstorage.json`;
      const asyncStorageExists = await RNFS.exists(asyncStoragePath);
      
      if (asyncStorageExists) {
        const asyncStorageContent = await RNFS.readFile(asyncStoragePath, 'utf8');
        const asyncStorageItems = JSON.parse(asyncStorageContent);

        // Effacer d'abord les données existantes (sauf certaines clés)
        const allKeys = await AsyncStorage.getAllKeys();
        const keysToPreserve = ['auth_token', 'refresh_token']; // Clés à ne pas supprimer
        const keysToRemove = allKeys.filter(key => !keysToPreserve.includes(key));
        
        if (keysToRemove.length > 0) {
          await AsyncStorage.multiRemove(keysToRemove);
        }

        // Restaurer les données AsyncStorage sauvegardées
        for (const [key, value] of asyncStorageItems) {
          // Ne pas écraser les clés préservées
          if (!keysToPreserve.includes(key)) {
            await AsyncStorage.setItem(key, value);
          }
        }
      }

      // Nettoyer après la restauration
      await this.cleanTempDirectory();

      EventEmitter.emit(BACKUP_EVENTS.RESTORE_COMPLETED);
    } catch (error) {
      console.error('Erreur lors de la restauration de la sauvegarde:', error);
      EventEmitter.emit(BACKUP_EVENTS.RESTORE_FAILED, error);
      throw error;
    }
  }

  /**
   * Liste les sauvegardes disponibles
   */
  public async listBackups(): Promise<{ path: string; name: string; date: Date; size: number }[]> {
    try {
      const files = await RNFS.readDir(this.backupDir);
      const backups = files
        .filter(file => file.name.startsWith('backup_') && file.name.endsWith('.zip'))
        .map(file => {
          const timestamp = parseInt(file.name.replace('backup_', '').replace('.zip', ''), 10);
          return {
            path: file.path,
            name: file.name,
            date: new Date(timestamp),
            size: file.size,
          };
        })
        .sort((a, b) => b.date.getTime() - a.date.getTime()); // Tri par date décroissante

      return backups;
    } catch (error) {
      console.error('Erreur lors de la liste des sauvegardes:', error);
      return [];
    }
  }

  /**
   * Supprime une sauvegarde
   */
  public async deleteBackup(backupPath: string): Promise<void> {
    try {
      const exists = await RNFS.exists(backupPath);
      if (exists) {
        await RNFS.unlink(backupPath);
      }
    } catch (error) {
      console.error('Erreur lors de la suppression de la sauvegarde:', error);
      throw error;
    }
  }

  /**
   * Nettoie le répertoire temporaire
   */
  private async cleanTempDirectory(): Promise<void> {
    try {
      const exists = await RNFS.exists(this.tempDir);
      if (exists) {
        const files = await RNFS.readDir(this.tempDir);
        for (const file of files) {
          await RNFS.unlink(file.path);
        }
      }
    } catch (error) {
      console.error('Erreur lors du nettoyage du répertoire temporaire:', error);
    }
  }
}

export const backupManager = BackupManager.getInstance();
export default backupManager; 