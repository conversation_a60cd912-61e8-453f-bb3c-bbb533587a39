import { jest } from '@jest/globals';
import fetchMock from 'jest-fetch-mock';

// Configuration de fetch-mock
fetchMock.enableMocks();

// Mock pour AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock pour NetInfo
jest.mock('@react-native-community/netinfo', () => ({
  addEventListener: jest.fn(),
  fetch: jest.fn(),
}));

// Configuration globale pour les timeouts
jest.setTimeout(10000);

// Nettoyage des mocks après chaque test
afterEach(() => {
  jest.clearAllMocks();
  fetchMock.resetMocks();
}); 