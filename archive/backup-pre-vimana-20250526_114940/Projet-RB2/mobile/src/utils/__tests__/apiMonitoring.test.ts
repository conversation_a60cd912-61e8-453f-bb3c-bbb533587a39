import { ApiMonitoring, apiMonitoring } from '../apiMonitoring';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import fetchMock from 'jest-fetch-mock';

// Mock des dépendances
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

jest.mock('@react-native-community/netinfo', () => ({
  addEventListener: jest.fn(),
  fetch: jest.fn(),
}));

// Configuration de fetch mock
fetchMock.enableMocks();

describe('ApiMonitoring', () => {
  beforeEach(() => {
    // Réinitialiser tous les mocks avant chaque test
    jest.clearAllMocks();
    fetchMock.resetMocks();
    
    // Mock de NetInfo par défaut
    (NetInfo.fetch as jest.Mock).mockResolvedValue({
      isConnected: true,
      type: 'wifi'
    });
    
    // Mock de AsyncStorage par défaut
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
  });

  describe('Initialisation', () => {
    it('devrait créer une instance unique', () => {
      const instance1 = ApiMonitoring.getInstance();
      const instance2 = ApiMonitoring.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('devrait initialiser avec la configuration par défaut', () => {
      apiMonitoring.initialize();
      const stats = apiMonitoring.getPerformanceStats();
      expect(stats).toEqual({
        requestCount: 0,
        successCount: 0,
        errorCount: 0,
        cachedCount: 0,
        avgDuration: 0,
        errorRate: 0,
        cacheHitRate: 0
      });
    });

    it('devrait respecter la configuration personnalisée', () => {
      const config = {
        enabled: true,
        sampleRate: 0.5,
        flushInterval: 5000,
        maxQueueSize: 50,
        reportErrors: true,
        debugMode: true
      };
      apiMonitoring.initialize(config);
      const metrics = apiMonitoring.getInternalMetrics();
      expect(metrics.flushAttempts).toBe(0);
    });
  });

  describe('Gestion des métriques', () => {
    beforeEach(() => {
      apiMonitoring.initialize({
        enabled: true,
        sampleRate: 1, // 100% d'échantillonnage pour les tests
        debugMode: true
      });
    });

    it('devrait ajouter une métrique personnalisée valide', () => {
      apiMonitoring.addCustomMetric('test_metric', 42, { category: 'test' });
      const stats = apiMonitoring.getPerformanceStats();
      expect(stats.requestCount).toBe(1);
    });

    it('devrait rejeter une métrique invalide', () => {
      const consoleSpy = jest.spyOn(console, 'warn');
      apiMonitoring.addCustomMetric('', NaN);
      expect(consoleSpy).toHaveBeenCalled();
      const stats = apiMonitoring.getPerformanceStats();
      expect(stats.requestCount).toBe(0);
    });
  });

  describe('Gestion du batch', () => {
    beforeEach(() => {
      apiMonitoring.initialize({
        enabled: true,
        sampleRate: 1,
        serverEndpoint: 'http://test.com/metrics'
      });
    });

    it('devrait traiter un batch de métriques', async () => {
      fetchMock.mockResponseOnce(JSON.stringify({ status: 'ok' }));

      // Ajouter plusieurs métriques
      for (let i = 0; i < 5; i++) {
        apiMonitoring.addCustomMetric(`metric_${i}`, i);
      }

      // Forcer un flush
      await apiMonitoring.flushMetrics();

      // Vérifier que fetch a été appelé avec les bonnes données
      expect(fetchMock).toHaveBeenCalled();
      const [url, options] = fetchMock.mock.calls[0];
      expect(url).toBe('http://test.com/metrics');
      expect(options?.method).toBe('POST');
    });

    it('devrait gérer les erreurs réseau', async () => {
      fetchMock.mockRejectOnce(new Error('Network error'));

      apiMonitoring.addCustomMetric('test_metric', 1);
      const result = await apiMonitoring.flushMetrics();

      expect(result).toBe(false);
      const metrics = apiMonitoring.getInternalMetrics();
      expect(metrics.networkErrors).toBeGreaterThan(0);
    });
  });

  describe('Gestion hors ligne', () => {
    beforeEach(() => {
      apiMonitoring.initialize({
        enabled: true,
        sampleRate: 1
      });
    });

    it('devrait stocker les métriques localement quand hors ligne', async () => {
      // Simuler une déconnexion
      (NetInfo.fetch as jest.Mock).mockResolvedValueOnce({
        isConnected: false,
        type: 'none'
      });

      apiMonitoring.addCustomMetric('offline_metric', 1);
      await apiMonitoring.flushMetrics();

      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('devrait charger et envoyer les métriques stockées lors de la reconnexion', async () => {
      // Simuler des métriques stockées
      const storedMetrics = [{
        name: 'stored_metric',
        value: 1,
        timestamp: Date.now()
      }];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify(storedMetrics));

      // Réinitialiser avec connexion
      apiMonitoring.initialize({
        enabled: true,
        sampleRate: 1,
        serverEndpoint: 'http://test.com/metrics'
      });

      // Vérifier que les métriques sont chargées et envoyées
      expect(AsyncStorage.getItem).toHaveBeenCalled();
      expect(AsyncStorage.removeItem).toHaveBeenCalled();
    });
  });

  describe('Compression des données', () => {
    it('devrait compresser les données au-dessus du seuil', async () => {
      apiMonitoring.initialize({
        enabled: true,
        sampleRate: 1,
        serverEndpoint: 'http://test.com/metrics'
      });

      // Générer beaucoup de métriques pour dépasser le seuil de compression
      for (let i = 0; i < 100; i++) {
        apiMonitoring.addCustomMetric(`large_metric_${i}`, i, {
          largeMetadata: 'x'.repeat(100) // Données volumineuses
        });
      }

      await apiMonitoring.flushMetrics();

      // Vérifier que la compression a été utilisée
      const calls = fetchMock.mock.calls;
      if (calls.length > 0) {
        const [, options] = calls[0];
        expect(options?.headers?.['Content-Encoding']).toBe('deflate');
      }
    });
  });

  describe('Nettoyage', () => {
    it('devrait nettoyer correctement les ressources', () => {
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');

      apiMonitoring.initialize();
      apiMonitoring.shutdown();

      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(clearTimeoutSpy).toHaveBeenCalled();
    });
  });
}); 