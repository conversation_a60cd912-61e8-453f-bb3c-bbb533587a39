// Simple logger utility

export class Logger {
  static info(message: string, ...args: any[]): void {
    console.info(`[INFO] ${message}`, ...args);
  }

  static warning(message: string, ...args: any[]): void {
    console.warn(`[WARNING] ${message}`, ...args);
  }

  static error(message: string, ...args: any[]): void {
    console.error(`[ERROR] ${message}`, ...args);
  }

  static debug(message: string, ...args: any[]): void {
    if (__DEV__) {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  }
} 