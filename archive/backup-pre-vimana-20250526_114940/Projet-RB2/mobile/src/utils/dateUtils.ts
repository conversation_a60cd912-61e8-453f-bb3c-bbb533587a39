import { format, parseISO, formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

/**
 * Formate une date pour l'affichage
 * @param date Date à formater (Date ou string ISO)
 * @param formatStr Format à utiliser (par défaut: dd/MM/yyyy)
 * @returns Date formatée
 */
export const formatDate = (date: Date | string | number, formatStr = 'dd/MM/yyyy'): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? parseISO(date) : 
                 typeof date === 'number' ? new Date(date) : date;
  
  return format(dateObj, formatStr, { locale: fr });
};

/**
 * Formate une date et heure pour l'affichage
 * @param date Date à formater (Date ou string ISO)
 * @returns Date et heure formatées
 */
export const formatDateTime = (date: Date | string | number): string => {
  return formatDate(date, 'dd/MM/yyyy à HH:mm');
};

/**
 * Retourne une représentation relative de la date par rapport à maintenant
 * @param date Date à formater
 * @returns Représentation relative (ex: "il y a 5 minutes")
 */
export const formatRelativeTime = (date: Date | string | number): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? parseISO(date) : 
                 typeof date === 'number' ? new Date(date) : date;
  
  return formatDistanceToNow(dateObj, { addSuffix: true, locale: fr });
};

/**
 * Convertit une date JavaScript en timestamp pour WatermelonDB
 * @param date Date à convertir
 * @returns Timestamp en millisecondes
 */
export const dateToTimestamp = (date: Date | string): number => {
  if (!date) return 0;
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return dateObj.getTime();
};

/**
 * Convertit un timestamp WatermelonDB en objet Date
 * @param timestamp Timestamp en millisecondes
 * @returns Objet Date JavaScript
 */
export const timestampToDate = (timestamp: number): Date => {
  return new Date(timestamp);
}; 