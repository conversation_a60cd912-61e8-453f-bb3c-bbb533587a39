/**
 * Classes d'erreurs personnalisées pour l'application
 */

export class AppError extends Error {
  statusCode: number;
  
  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
  }
}

export class NetworkError extends Error {
  retryCount: number = 0;
  
  constructor(message: string) {
    super(message);
    this.name = 'NetworkError';
  }
  
  incrementRetry() {
    this.retryCount += 1;
  }
}

export class ValidationError extends AppError {
  fieldErrors: Record<string, string>;
  
  constructor(message: string = 'Validation error', fieldErrors: Record<string, string> = {}) {
    super(message, 400);
    this.name = 'ValidationError';
    this.fieldErrors = fieldErrors;
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication error') {
    super(message, 401);
    this.name = 'AuthenticationError';
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string, id?: string) {
    const message = id 
      ? `Resource ${resource} with id ${id} not found` 
      : `Resource ${resource} not found`;
    super(message, 404);
    this.name = 'NotFoundError';
  }
}

export class OfflineError extends Error {
  constructor(message: string = 'No internet connection available') {
    super(message);
    this.name = 'OfflineError';
  }
}

export class TimeoutError extends Error {
  constructor(message: string = 'The request timed out') {
    super(message);
    this.name = 'TimeoutError';
  }
}

export class ServerError extends Error {
  statusCode: number;
  
  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.name = 'ServerError';
    this.statusCode = statusCode;
  }
}

/**
 * Vérifie si l'erreur est une instance d'une erreur spécifique de l'app
 */
export function isAppError(error: unknown): error is AppError {
  return error instanceof AppError;
}

/**
 * Vérifie si l'erreur est une instance d'une erreur réseau
 */
export function isNetworkError(error: unknown): error is NetworkError {
  return error instanceof NetworkError;
}

/**
 * Vérifie si l'erreur est une instance d'une erreur hors-ligne
 */
export function isOfflineError(error: unknown): error is OfflineError {
  return error instanceof OfflineError;
}

/**
 * Extrait le message d'erreur d'une erreur inconnue
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String((error as any).message);
  }
  return 'An unknown error occurred';
}

/**
 * Formate une erreur pour la journalisation
 */
export function formatError(error: unknown): Record<string, any> {
  if (error instanceof Error) {
    return {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...(error instanceof AppError ? { statusCode: error.statusCode } : {}),
      ...(error instanceof ValidationError ? { fieldErrors: error.fieldErrors } : {}),
      ...(error instanceof NetworkError ? { retryCount: error.retryCount } : {})
    };
  }
  return { message: String(error) };
}

/**
 * Journalise une erreur avec un contexte optionnel
 */
export function logError(error: unknown, context?: Record<string, unknown>): void {
  console.error(
    'Error:', 
    getErrorMessage(error), 
    { error: formatError(error), ...context }
  );
}
