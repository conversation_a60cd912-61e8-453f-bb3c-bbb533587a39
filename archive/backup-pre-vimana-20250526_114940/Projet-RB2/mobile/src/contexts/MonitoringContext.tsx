/**
 * Contexte de monitoring pour l'application;
 * 
 * Ce contexte permet d'instrumenter l'application entière et de fournir;
 * un accès facile aux fonctionnalités de monitoring depuis n'importe quel;
 * composant dans l'arbre React.
 */

import React, { createContext, useContext, useEffect, useMemo, ReactNode } from 'react';
import { unifiedMonitoring, LogLevel } from '../utils/unifiedMonitoring';

// Interface du contexte de monitoring;
interface MonitoringContextProps {
  // Logs;
  debug: (message: string, context?: Record<string, any>) => void;
  info: (message: string, context?: Record<string, any>) => void;
  warn: (message: string, context?: Record<string, any>) => void;
  error: (message: string, context?: Record<string, any>) => void;
  
  // Analytics;
  trackEvent: (eventName: string, properties?: Record<string, any>) => void;
  
  // Erreurs;
  captureError: (error: Error, context?: Record<string, any>) => void;
  
  // Performance;
  measurePerformance: <T>(name: string, fn: () => T, metadata?: Record<string, any>) => T;
  measurePerformanceAsync: <T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>) => Promise<T>;
  
  // API manuelle;
  startMeasure: (name: string, metadata?: Record<string, any>) => string;
  endMeasure: (id: string, additionalMetadata?: Record<string, any>) => number | null;
  
  // Opérations bas niveau;
  flushMetrics: () => Promise<boolean>;
  
  // Informations sur l'état de monitoring;
  isEnabled: boolean
}

// Créer le contexte;
const MonitoringContext = createContext<MonitoringContextProps | undefined>(undefined);

// Interface pour les props du fournisseur;
interface MonitoringProviderProps {
  children: ReactNode;
  appName: string;
  version: string;
  environment?: string;
  features?: Record<string, boolean>;
  disabled?: boolean;
}

/**
 * Fournisseur du contexte de monitoring;
 */
export const MonitoringProvider: React.FC<MonitoringProviderProps> = ({
  children,
  appName,
  version,
  environment = 'development',
  features = {},
  disabled = false
}) => {
  // Initialiser le monitoring au démarrage;
  useEffect(() => {
    if (disabled) return;
    
    // Initialiser le monitoring;
    unifiedMonitoring.initialize();
    
    // Enregistrer les informations sur l'application;
    unifiedMonitoring.trackEvent('app_context', {
      appName,
      version,
      environment,
      features
    });
    
    // Nettoyer au démontage;
    return () => {
      unifiedMonitoring.flush().catch(e => {
        console.error('Erreur lors du flush des métriques:', e);
      });
    };
  }, [appName, version, environment, features, disabled]);
  
  // Créer les valeurs du contexte;
  const contextValue = useMemo<MonitoringContextProps>(() => ({
    // Logs;
    debug: (message, context = {}) => {
      if (disabled) return;
      unifiedMonitoring.log(LogLevel.DEBUG, message, context);
    },
    
    info: (message, context = {}) => {
      if (disabled) return;
      unifiedMonitoring.log(LogLevel.INFO, message, context);
    },
    
    warn: (message, context = {}) => {
      if (disabled) return;
      unifiedMonitoring.log(LogLevel.WARN, message, context);
    },
    
    error: (message, context = {}) => {
      if (disabled) return;
      unifiedMonitoring.log(LogLevel.ERROR, message, context);
    },
    
    // Analytics;
    trackEvent: (eventName, properties = {}) => {
      if (disabled) return;
      unifiedMonitoring.trackEvent(eventName, properties);
    },
    
    // Erreurs;
    captureError: (error, context = {}) => {
      if (disabled) return;
      unifiedMonitoring.captureError(error, context);
    },
    
    // Performance;
    measurePerformance: (name, fn, metadata = {}) => {
      if (disabled) return fn();
      return unifiedMonitoring.measure(name, fn, metadata);
    },
    
    measurePerformanceAsync: async (name, fn, metadata = {}) => {
      if (disabled) return fn();
      return unifiedMonitoring.measureAsync(name, fn, metadata);
    },
    
    // API manuelle;
    startMeasure: (name, metadata = {}) => {
      if (disabled) return '';
      return unifiedMonitoring.markPerformanceStart(name, metadata);
    },
    
    endMeasure: (id, additionalMetadata = {}) => {
      if (disabled) return null;
      return unifiedMonitoring.markPerformanceEnd(id, additionalMetadata);
    },
    
    // Opérations bas niveau;
    flushMetrics: async() => {
      if (disabled) return true;
      return unifiedMonitoring.flush();
    },
    
    // Informations sur l'état de monitoring;
    isEnabled: !disabled
  }), [disabled, appName, version, environment, features]);
  
  return (
    <MonitoringContext.Provider value={contextValue}>
      {children}
    </MonitoringContext.Provider>
  );
};

/**
 * Hook pour utiliser le contexte de monitoring;
 */
export const useMonitoringContext = (): MonitoringContextProps => {
  const context = useContext(MonitoringContext);
  
  if(context === undefined) {
    throw new Error("useMonitoringContext doit être utilisé à l'intérieur d'un MonitoringProvider");
  }
  
  return context;
};

export default MonitoringContext;