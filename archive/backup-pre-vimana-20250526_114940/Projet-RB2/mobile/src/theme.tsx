import React, { createContext, useContext, useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ThemeColors {
  background: string;
  cardBackground: string;
  text: string;
  secondaryText: string;
  primary: string;
  secondary: string;
  border: string;
  error: string;
  success: string;
  warning: string;
  accent: string;
  danger: string;
}

export interface Theme {
  isDark: boolean;
  colors: ThemeColors;
  toggleTheme: () => void;
}

const lightTheme: ThemeColors = {
  background: '#F7F9FC',
  cardBackground: '#FFFFFF',
  text: '#1A1F36',
  secondaryText: '#697386',
  primary: '#5469D4',
  secondary: '#7A869A',
  border: '#E3E8EF',
  error: '#ED5F74',
  success: '#3ECF8E',
  warning: '#F7C137',
  accent: '#56B6F7',
  danger: '#F2758C',
};

const darkTheme: ThemeColors = {
  background: '#1A1F36',
  cardBackground: '#252D43',
  text: '#F7F9FC',
  secondaryText: '#A3AED0',
  primary: '#7B8CDE',
  secondary: '#8F9BB3',
  border: '#313D5C',
  error: '#F2758C',
  success: '#57D9A3',
  warning: '#F9CF6A',
  accent: '#7ECBF9',
  danger: '#F2758C',
};

interface ThemeContextType {
  theme: ThemeColors;
  isDark: boolean;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  isDark: false,
  toggleTheme: () => {},
});

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const colorScheme = useColorScheme();
  const [isDark, setIsDark] = useState(colorScheme === 'dark');
  
  // Mettre à jour le thème en fonction du schéma de couleurs du système
  useEffect(() => {
    setIsDark(colorScheme === 'dark');
  }, [colorScheme]);
  
  const toggleTheme = () => {
    setIsDark(prev => !prev);
  };
  
  const theme = isDark ? darkTheme : lightTheme;
  
  return (
    <ThemeContext.Provider value={{ theme, isDark, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext); 