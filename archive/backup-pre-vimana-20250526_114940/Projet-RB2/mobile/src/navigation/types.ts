/**
 * Types pour la navigation dans l'application
 */

// Liste des écrans dans la pile de navigation principale
export type AppStackParamList = {
  Home: undefined;
  Settings: undefined;
  ConflictResolution: undefined;
  BackupScreen: undefined;
  SyncSettings: undefined;
  RetreatDetails: { retreatId: string };
  BookingForm: { retreatId: string } | undefined;
  UserProfile: { userId: string } | undefined;
  Notifications: undefined;
};

// Types pour les paramètres de navigation
export type MainStackParamList = {
  Home: undefined;
  Search: undefined;
  Profile: undefined;
  Settings: undefined;
  Bookings: undefined;
  Notifications: undefined;
  RetreatDetail: { retreatId: string };
  BookingDetails: { bookingId: string };
  BookingConfirmation: { retreatId: string; startDate?: string; endDate?: string; guests?: number };
  BookingSuccess: { 
    bookingId: string;
    retreatTitle?: string;
    startDate?: string;
    endDate?: string;
  };
  SyncSettings: undefined;
  FeatureFlags: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

// Mise à jour des types de navigation pour l'authentification
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: { token: string };
}; 