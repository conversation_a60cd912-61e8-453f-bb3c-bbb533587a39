import ConflictResolutionScreen from '../components/ui/ConflictResolutionScreen';
import BackupScreen from '../screens/BackupScreen';

const Stack = createStackNavigator();

const AppNavigator = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="ConflictResolution" 
        component={ConflictResolutionScreen} 
        options={{ headerShown: false }} 
      />
      <Stack.Screen 
        name="Backup" 
        component={BackupScreen} 
        options={{ headerShown: false }} 
      />
    </Stack.Navigator>
  );
};

export default AppNavigator; 