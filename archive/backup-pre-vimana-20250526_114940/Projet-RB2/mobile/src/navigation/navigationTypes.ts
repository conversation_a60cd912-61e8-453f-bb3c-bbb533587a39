/**
 * Types pour la navigation
 */

export type RootStackParamList = {
  Main: undefined;
  Auth: undefined
}

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined
}

export type MainTabParamList = {
  Home: undefined;
  Bookings: undefined;
  Dashboard: undefined;
  Notifications: undefined;
  Profile: undefined;
  Settings: undefined
}

export type HomeStackParamList = {
  HomeMain: undefined;
  HomeDetail: { id: string }
};

export type BookingsStackParamList = {
  BookingsMain: undefined;
  BookingDetail: { id: string }
};

export type DashboardStackParamList = {
  DashboardMain: undefined;
  Roadmap: undefined
}

export type NotificationsStackParamList = {
  NotificationsMain: undefined;
  NotificationDetail: { id: string }
};

export type ProfileStackParamList = {
  ProfileMain: undefined;
  EditProfile: undefined
} 