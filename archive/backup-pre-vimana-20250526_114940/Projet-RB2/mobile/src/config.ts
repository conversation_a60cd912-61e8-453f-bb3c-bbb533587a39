// Configuration de l'application

// URL de l'API
export const API_URL = process.env.API_URL || 'https://api.retraite-bien-etre.com';

// Version de l'application
export const APP_VERSION = process.env.APP_VERSION || '1.0.0';

// Timeout pour les requêtes API (en millisecondes)
export const API_TIMEOUT = 15000;

// Clé pour le stockage local
export const STORAGE_KEY = '@RetraiteBienEtre:';

// Configuration pour le monitoring
export const MONITORING = {
  enabled: process.env.NODE_ENV === 'production',
  sampleRate: 0.1, // Taux d'échantillonnage pour les événements
  errorSampleRate: 1.0, // Taux d'échantillonnage pour les erreurs
};

// Configuration pour les fonctionnalités expérimentales
export const FEATURES = {
  enableOfflineMode: true,
  enableP2PSync: false,
  enableIPFSStorage: false,
  enableNotifications: true,
}; 