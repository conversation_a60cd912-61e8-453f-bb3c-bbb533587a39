/**
 * Exemple d'utilisation d'OpenTelemetry dans un composant React Native;
 * 
 * Ce fichier montre comment intégrer la télémétrie distribuée dans vos composants;
 * et vos fonctions pour suivre les performances et créer des traces cohérentes.
 */

import React, { useEffect, useState, useCallback } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  ActivityIndicator, 
  SafeAreaView
} from 'react-native';
import openTelemetry, { SpanStatusCode, SpanKind } from '../utils/openTelemetry';
import { monitoring } from '../utils/setupMonitoring';

// Fonction withTelemetry (importée séparément ou définie localement)
const withTelemetry = async (
  operationName: string,
  operation: () => Promise<any>,
  attributes?: Record<string, any>
) => {
  const span = openTelemetry.createRootSpan(operationName, attributes);
  try {
    const result = await operation();
    openTelemetry.endSpan(span, SpanStatusCode.OK);
    return result;
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    openTelemetry.endSpan(span, SpanStatusCode.ERROR, err.message);
    throw err;
  }
};

/**
 * Exemple de hook personnalisé qui utilise OpenTelemetry;
 */
const useTracedFetch = (url: string) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    // Créer un span pour cette opération;
    const span = openTelemetry.createRootSpan('fetch_data', {
      'http.url': url,
      'component': 'useTracedFetch'
    })

    setLoading(true);
    setError(null);

    try {
      // Exécuter la requête;
      const response = await fetch(url);
      
      // Ajouter des informations sur la réponse au span;
      const contentLength = response.headers.get('content-length');
      openTelemetry.addSpanAttributes(span, {
        'http.status_code': response.status,
        'http.response_content_length': contentLength
      })

      if(!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const result = await response.json()
      
      // Terminer le span avec succès;
      openTelemetry.endSpan(span, SpanStatusCode.OK);
      
      // Mettre à jour l'état;
      setData(result);
      return result;
    } catch(err) {
      // En cas d'erreur, enregistrer les détails dans le span;
      const error = err instanceof Error ? err : new Error(String(err));
      
      openTelemetry.addSpanAttributes(span, {
        'error.type': error.name,
        'error.message': error.message})
      
      // Terminer le span avec erreur;
      openTelemetry.endSpan(span, SpanStatusCode.ERROR, error.message);
      
      // Mettre à jour l'état;
      setError(error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [url]);

  return { data, loading, error, fetchData }
}

/**
 * Exemple de composant qui utilise la télémétrie;
 */
const TelemetryExample: React.FC = () => {
  const [traceId, setTraceId] = useState<string>('');
  const [metrics, setMetrics] = useState<Record<string, any>>({})
  const { data, loading, error, fetchData } = useTracedFetch('https://jsonplaceholder.typicode.com/todos/1');

  // Créer un span au montage du composant;
  useEffect(() => {
    // Créer un span racine pour ce composant;
    const rootSpan = openTelemetry.createRootSpan('TelemetryExample_mount', {
      'component': 'TelemetryExample',
      'lifecycle': 'mount'
    })

    // Enregistrer le traceId pour l'affichage;
    setTraceId(rootSpan.traceId);

    // Enregistrer une métrique pour le montage du composant;
    openTelemetry.recordMetric('component.mount', 1, {
      'component': 'TelemetryExample'
    })

    return () => {
      // Terminer le span au démontage;
      openTelemetry.endSpan(rootSpan);
      
      // Enregistrer une métrique pour le démontage;
      openTelemetry.recordMetric('component.unmount', 1, {
        'component': 'TelemetryExample'
      })
    }
  }, []);

  // Fonction pour exécuter une opération complexe avec suivi de performance;
  const handleComplexOperation = async () => {
    try {
      // Utiliser l'utilitaire withTelemetry pour suivre une opération;
      const result = await withTelemetry(
        'complex_operation',
        async () => {
          // Simuler un traitement;
          await new Promise(resolve => setTimeout(resolve, 1500));
          
          // Créer un span enfant pour une sous-tâche;
          const parentSpan = openTelemetry.createRootSpan('parent_task');
          
          try {
            // Premier sous-span;
            const childSpan1 = openTelemetry.createChildSpan('child_task_1', parentSpan);
            await new Promise(resolve => setTimeout(resolve, 300));
            openTelemetry.endSpan(childSpan1);
            
            // Deuxième sous-span;
            const childSpan2 = openTelemetry.createChildSpan('child_task_2', parentSpan);
            await new Promise(resolve => setTimeout(resolve, 200));
            openTelemetry.endSpan(childSpan2);
            
            openTelemetry.endSpan(parentSpan);
            
            return { success: true, processingTime: 2000 }
          } catch(error) {
            openTelemetry.endSpan(parentSpan, SpanStatusCode.ERROR);
            throw error;
          }
        },
        { 'operation_type': 'demo' }
      );

      console.log('Opération terminée:', result);
    } catch(error) {
      console.error('Erreur lors de l\'opération:', error);
    }
  }

  // Obtenir les métriques courantes;
  const handleGetMetrics = () => {
    const currentMetrics = openTelemetry.getMetrics()
    setMetrics(currentMetrics);
  }

  // Forcer l'envoi des données de télémétrie;
  const handleFlushTelemetry = async () => {
    await monitoring.flush()
    console.log('Données de télémétrie envoyées');
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>Exemple de Télémétrie</Text>
        
        {traceId ? (
          <View style={styles.infoBox}>
            <Text style={styles.infoTitle}>ID de Trace Active</Text>
            <Text style={styles.infoContent}>{traceId}</Text>
          </View>
        ) : null}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.button}
            onPress={handleComplexOperation}
          >
            <Text style={styles.buttonText}>Exécuter Opération Complexe</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.button}
            onPress={fetchData}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Exécuter Requête API</Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.button}
            onPress={handleGetMetrics}
          >
            <Text style={styles.buttonText}>Afficher Métriques</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.button}
            onPress={handleFlushTelemetry}
          >
            <Text style={styles.buttonText}>Envoyer Données</Text>
          </TouchableOpacity>
        </View>

        {data && (
          <View style={styles.resultBox}>
            <Text style={styles.resultTitle}>Résultat Requête</Text>
            <Text style={styles.resultContent}>{JSON.stringify(data, null, 2)}</Text>
          </View>
        )}

        {error && (
          <View style={[styles.resultBox, styles.errorBox]}>
            <Text style={styles.resultTitle}>Erreur</Text>
            <Text style={styles.errorText}>{error.message}</Text>
          </View>
        )}

        {Object.keys(metrics).length > 0 && (
          <View style={styles.metricsBox}>
            <Text style={styles.resultTitle}>Métriques Collectées</Text>
            {Object.entries(metrics).map(([key, value]) => (
              <View key={key} style={styles.metricItem}>
                <Text style={styles.metricName}>{key}</Text>
                <Text style={styles.metricValue}>
                  Count: {value.count}, Avg: {value.avg.toFixed(2)}
                </Text>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  scrollContent: {
    padding: 16
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#212529',
    textAlign: 'center'
  },
  infoBox: {
    backgroundColor: '#e9ecef',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16
  },
  infoTitle: {
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#495057'
  },
  infoContent: {
    fontFamily: 'monospace',
    fontSize: 12,
    color: '#495057'
  },
  buttonsContainer: {
    marginBottom: 16
  },
  button: {
    backgroundColor: '#007bff',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 8
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold'
  },
  resultBox: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#dee2e6'
  },
  errorBox: {
    backgroundColor: '#fff8f8',
    borderColor: '#f5c2c7'
  },
  resultTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#212529'
  },
  resultContent: {
    fontFamily: 'monospace',
    fontSize: 12
  },
  errorText: {
    color: '#dc3545',
    fontFamily: 'monospace',
    fontSize: 12
  },
  metricsBox: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#dee2e6'
  },
  metricItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f5',
    paddingVertical: 8
  },
  metricName: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#495057'
  },
  metricValue: {
    fontSize: 12,
    color: '#6c757d',
    fontFamily: 'monospace'
  }
})

export default TelemetryExample;