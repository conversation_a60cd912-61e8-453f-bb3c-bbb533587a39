import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Image, Alert } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Text, Button, TextInput } from '../../components/ui';
import { useTheme } from '../../theme';
import { useAuth } from '../../hooks/useAuth';
import { selectAuth, updateUser } from '../../store/slices/authSlice';
import { getErrorMessage, logError } from '../../utils/errorUtils';
import { API_URL } from '../../config/api.config';

const ProfileScreen = () => {
  const { user, logout } = useAuth();
  const dispatch = useDispatch();
  const { theme } = useTheme();
  
  const enhancedUser = {
    name: user?.name || `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'Utilisateur',
    email: user?.email || '',
    phone: user?.phone || '',
    avatar: user?.avatar || user?.avatarUrl || 'https://via.placeholder.com/150',
    token: user?.token || '',
    id: user?.id || ''
  };
  
  const [isEditing, setIsEditing] = useState(false);
  const [name, setName] = useState(enhancedUser.name);
  const [email, setEmail] = useState(enhancedUser.email);
  const [phone, setPhone] = useState(enhancedUser.phone);
  const [loading, setLoading] = useState(false);

  const handleSaveProfile = async () => {
    if (!user) return;
    
    setLoading(true);
    
    try {
      const response = await fetch(`${API_URL}/users/${enhancedUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${enhancedUser.token}`
        },
        body: JSON.stringify({
          firstName: name,
          lastName: name,
          phone
        })
      });
      
      if (response.ok) {
        const updatedUserData = await response.json();
        dispatch(updateUser({
          firstName: updatedUserData.firstName,
          lastName: updatedUserData.lastName,
          phone: updatedUserData.phone
        }));
        
        Alert.alert('Succès', 'Profil mis à jour avec succès');
      } else {
        throw new Error('Failed to update profile');
      }
    } catch (error) {
      logError('updateProfile', error);
      Alert.alert('Erreur', getErrorMessage(error, 'Échec de la mise à jour du profil'));
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <View style={styles.profileImageContainer}>
            <Image
              source={{ uri: enhancedUser.avatar }}
              style={styles.profileImage}
            />
            <TouchableOpacity
              style={[styles.editImageButton, { backgroundColor: theme.primary }]}
              onPress={() => Alert.alert('Fonctionnalité', 'Changer la photo de profil')}
            >
              <Icon name="camera" size={16} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
          
          <Text style={[styles.name, { color: theme.text }]}>
            {enhancedUser.name}
          </Text>
          
          <Text style={[styles.email, { color: theme.text + '80' }]}>
            {enhancedUser.email}
          </Text>
          
          {!isEditing && (
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setIsEditing(true)}
            >
              <Text style={{ color: theme.primary }}>Modifier le profil</Text>
            </TouchableOpacity>
          )}
        </View>
        
        {isEditing ? (
          <View style={styles.editForm}>
            <TextInput
              label="Nom"
              value={name}
              onChangeText={setName}
              style={[styles.input, { borderColor: theme.border }]}
            />
            
            <TextInput
              label="Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              style={[styles.input, { borderColor: theme.border }]}
            />
            
            <TextInput
              label="Téléphone"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              style={[styles.input, { borderColor: theme.border }]}
            />
            
            <View style={styles.buttonRow}>
              <Button
                title="Annuler"
                onPress={() => setIsEditing(false)}
                style={styles.buttonCancel}
                textStyle={{ color: theme.text }}
                variant="outline"
              />
              
              <Button
                title="Enregistrer"
                onPress={handleSaveProfile}
                loading={loading}
                style={styles.buttonSave}
                textStyle={{ color: '#FFFFFF' }}
                variant="primary"
              />
            </View>
          </View>
        ) : (
          <View style={styles.profileInfo}>
            <View style={styles.infoSection}>
              <Text style={[styles.sectionTitle, { color: theme.text }]}>
                Informations personnelles
              </Text>
              
              <View style={[styles.infoItem, { borderBottomColor: theme.border }]}>
                <Text style={{ color: theme.text + '80' }}>Nom</Text>
                <Text style={{ color: theme.text }}>{enhancedUser.name}</Text>
              </View>
              
              <View style={[styles.infoItem, { borderBottomColor: theme.border }]}>
                <Text style={{ color: theme.text + '80' }}>Email</Text>
                <Text style={{ color: theme.text }}>{enhancedUser.email}</Text>
              </View>
              
              <View style={[styles.infoItem, { borderBottomColor: theme.border }]}>
                <Text style={{ color: theme.text + '80' }}>Téléphone</Text>
                <Text style={{ color: theme.text }}>{enhancedUser.phone}</Text>
              </View>
            </View>
            
            <Button
              title="Se déconnecter"
              onPress={handleLogout}
              style={styles.buttonLogout}
              textStyle={{ color: theme.error }}
              variant="outline"
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  scrollContent: {
    flexGrow: 1
  },
  header: {
    alignItems: 'center',
    padding: 20
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 15
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center'
  },
  name: {
    fontSize: 22,
    fontWeight: 'bold'
  },
  email: {
    fontSize: 16,
    marginTop: 5
  },
  editButton: {
    marginTop: 15,
    padding: 8
  },
  profileInfo: {
    padding: 20
  },
  infoSection: {
    marginBottom: 25
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 1
  },
  editForm: {
    padding: 20
  },
  input: {
    marginBottom: 15,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10
  },
  buttonCancel: {
    flex: 1,
    marginHorizontal: 5,
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#cccccc'
  },
  buttonSave: {
    flex: 1,
    marginHorizontal: 5,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#4a90e2'
  },
  buttonLogout: {
    marginTop: 20,
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 0, 0, 0.1)'
  }
});

export default ProfileScreen;