import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, RefreshControl, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

// Define the Notification interface
interface Notification {
  id: string;
  title: string;
  message: string;
  date: string;
  read: boolean;
  type: string;
}

// Mock notifications with proper type annotation
const MOCK_NOTIFICATIONS: Notification[] = [
  {
    id: '1',
    title: 'Welcome',
    message: 'Welcome to our app!',
    date: '2023-10-01',
    read: false,
    type: 'info'
  },
  {
    id: '2',
    title: 'Update Available',
    message: 'A new update is available. Please update your app.',
    date: '2023-10-02',
    read: false,
    type: 'warning'
  }
];

const NotificationsScreen: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>(MOCK_NOTIFICATIONS);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [syncStatus, setSyncStatus] = useState<any>(null);

  // Fetch notifications (simulate network request)
  const fetchNotifications = async (): Promise<Notification[]> => {
    // Simulate a network delay
    return new Promise(resolve => {
      setTimeout(() => resolve(MOCK_NOTIFICATIONS), 1000);
    });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    const data = await fetchNotifications();
    setNotifications(data);
    setRefreshing(false);
  };

  // Render a single notification item
  const renderNotificationItem = ({ item }: { item: Notification }) => (
    <View style={styles.notificationItem}>
      <Text style={styles.notificationTitle}>{item.title}</Text>
      <Text style={styles.notificationMessage}>{item.message}</Text>
      <Text style={styles.notificationDate}>{item.date}</Text>
      <Icon name="notifications-outline" size={20} color="#000" />
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={notifications}
        keyExtractor={(item) => item.id}
        renderItem={renderNotificationItem}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff'
  },
  notificationItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc'
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  notificationMessage: {
    fontSize: 14,
    marginVertical: 4
  },
  notificationDate: {
    fontSize: 12,
    color: '#888'
  }
});

export default NotificationsScreen;