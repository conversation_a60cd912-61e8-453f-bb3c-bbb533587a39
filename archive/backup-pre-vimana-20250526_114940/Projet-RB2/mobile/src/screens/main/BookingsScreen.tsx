import React, { useEffect, useState } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text, BookingCard } from '../../components/ui';
import { useTheme } from '../../theme';
import { useSelector } from 'react-redux';
import { selectAuth } from '../../store/slices/authSlice';

// Mock data - replace with actual API call
const MOCK_BOOKINGS = [
  {
    id: '1',
    retreatName: 'Retraite Yoga & Méditation',
    location: 'Bali, Indonésie',
    startDate: '2025-06-15',
    endDate: '2025-06-22',
    status: 'confirmed',
    price: '1200€'
  },
  {
    id: '2',
    retreatName: 'Détox & Bien-être',
    location: 'Tulum, Mexique',
    startDate: '2025-07-03',
    endDate: '2025-07-10',
    status: 'pending',
    price: '1500€'
  },
  {
    id: '3',
    retreatName: 'Mindfulness & Nature',
    location: 'Alpes, France',
    startDate: '2025-08-20',
    endDate: '2025-08-27',
    status: 'completed',
    price: '950€'
  }
];

const BookingsScreen = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useSelector(selectAuth);

  // Fetch bookings
  const fetchBookings = async () => {
    try {
      // Replace with actual API call
      // const response = await fetch(`https://api.example.com/users/${user.id}/bookings`, {
      //   headers: {
      //     Authorization: `Bearer ${user.token}`,
      //   },
      // })
      // const data = await response.json()
      
      // Using mock data for now
      setTimeout(() => {
        setBookings(MOCK_BOOKINGS);
        setLoading(false);
        setRefreshing(false);
      }, 1000);
    } catch(error) {
      console.error('Error fetching bookings:', error);
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchBookings();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchBookings();
  };

  const handleBookingPress = (bookingId) => {
    navigation.navigate('BookingDetails', { bookingId });
  };

  const renderBookingItem = ({ item }) => (
    <BookingCard
      retreatName={item.retreatName}
      location={item.location}
      startDate={item.startDate}
      endDate={item.endDate}
      status={item.status}
      price={item.price}
      onPress={() => handleBookingPress(item.id)}
      style={styles.card}
    />
  );

  if(loading && !refreshing) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color={theme.primary} />
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>Mes Réservations</Text>
      </View>

      <FlatList
        data={bookings}
        renderItem={renderBookingItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.primary]}
            tintColor={theme.primary}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: theme.text }]}>
              Vous n'avez pas encore de réservations
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold'
  },
  listContent: {
    padding: 15,
    flexGrow: 1
  },
  card: {
    marginBottom: 15
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 50
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center'
  }
});

export default BookingsScreen;