import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme'

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; },
  get info() { return theme.primary; } // Fallback pour 'info'
});
;
import { Text, Button, TextInput, Card } from '../../components/ui';
import { MainStackParamList } from '../../navigation/MainStack';

type BookingConfirmationRouteProp = RouteProp<MainStackParamList, 'BookingConfirmation'>;
type BookingConfirmationNavigationProp = NativeStackNavigationProp<MainStackParamList, 'BookingConfirmation'>;
interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  participants: number;
  specialRequests: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  participants?: string;
}

const BookingConfirmationScreen: React.FC = () => {
  const { theme: originalTheme } = useTheme();
  // Utiliser l'adaptateur de thème pour la compatibilité
  const theme = getCompatibleTheme(originalTheme);
  const navigation = useNavigation<BookingConfirmationNavigationProp>();
  const route = useRoute<BookingConfirmationRouteProp>();
  const { retreatId } = route.params;
  
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    participants: 1,
    specialRequests: ''
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Mock data for the retreat - in a real app, this would be fetched from an API
  const retreat = {
    id: retreatId,
    title: 'Retraite de Yoga et Méditation',
    location: 'Mont-Saint-Michel, Normandie',
    startDate: '2025-06-15',
    endDate: '2025-06-20',
    price: 850,
    image: 'https://images.unsplash.com/photo-1545389336-cf090694435e'
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData({
      ...formData,
      [field]: value
    });
    
    // Clear error when user types
    if(errors[field as keyof FormErrors]) {
      setErrors({
        ...errors,
        [field]: undefined
      });
    }
  };

  const incrementParticipants = () => {
    if(formData.participants < 10) {
      handleInputChange('participants', formData.participants + 1);
    }
  };

  const decrementParticipants = () => {
    if(formData.participants > 1) {
      handleInputChange('participants', formData.participants - 1);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'Le prénom est requis';
    }
    
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Le nom est requis';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = 'Le numéro de téléphone est requis';
    } else if (!/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(formData.phone)) {
      newErrors.phone = 'Format de téléphone invalide';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // In a real app, you would send the booking data to your API
      // const response = await api.createBooking({ retreatId, ...formData })
      
      // Navigate to success screen
      navigation.navigate('BookingSuccess', { 
        bookingId: 'BK' + Math.floor(Math.random() * 10000),
        retreatTitle: retreat.title,
        startDate: retreat.startDate,
        endDate: retreat.endDate
      });
    } catch(error) {
      Alert.alert(
        'Erreur',
        'Une erreur est survenue lors de la réservation. Veuillez réessayer.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateTotal = () => {
    return retreat.price * formData.participants;
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={[styles.header, { backgroundColor: theme.card }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={theme.text} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: theme.text }]}>
          Confirmation de réservation
        </Text>
        
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Card
          title={retreat.title}
          subtitle={retreat.location}
          description={`${formatDate(retreat.startDate)} - ${formatDate(retreat.endDate)}`}
          imageUrl={retreat.image}
          price={`${retreat.price} €`}
          style={styles.retreatCard}
        />
        
        <View style={[styles.formContainer, { backgroundColor: theme.card }]}>
          <Text style={[styles.formTitle, { color: theme.text }]}>
            Informations personnelles
          </Text>
          
          <View style={styles.formRow}>
            <View style={styles.formHalfColumn}>
              <TextInput
                label="Prénom"
                value={formData.firstName}
                onChangeText={(text) => handleInputChange('firstName', text)}
                error={errors.firstName}
                containerStyle={styles.inputContainer}
              />
            </View>
            
            <View style={styles.formHalfColumn}>
              <TextInput
                label="Nom"
                value={formData.lastName}
                onChangeText={(text) => handleInputChange('lastName', text)}
                error={errors.lastName}
                containerStyle={styles.inputContainer}
              />
            </View>
          </View>
          
          <TextInput
            label="Email"
            value={formData.email}
            onChangeText={(text) => handleInputChange('email', text)}
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors.email}
            containerStyle={styles.inputContainer}
          />
          
          <TextInput
            label="Téléphone"
            value={formData.phone}
            onChangeText={(text) => handleInputChange('phone', text)}
            keyboardType="phone-pad"
            error={errors.phone}
            containerStyle={styles.inputContainer}
          />
          
          <Text style={[styles.label, { color: theme.text }]}>
            Nombre de participants
          </Text>
          
          <View style={[styles.participantsContainer, { borderColor: theme.border }]}>
            <TouchableOpacity
              style={[styles.participantButton, { borderColor: theme.border }]}
              onPress={decrementParticipants}
              disabled={formData.participants <= 1}
            >
              <Icon name="minus" size={20} color={formData.participants <= 1 ? theme.text + '40' : theme.text} />
            </TouchableOpacity>
            
            <Text style={[styles.participantsCount, { color: theme.text }]}>
              {formData.participants}
            </Text>
            
            <TouchableOpacity
              style={[styles.participantButton, { borderColor: theme.border }]}
              onPress={incrementParticipants}
              disabled={formData.participants >= 10}
            >
              <Icon name="plus" size={20} color={formData.participants >= 10 ? theme.text + '40' : theme.text} />
            </TouchableOpacity>
          </View>
          
          <TextInput
            label="Demandes spéciales (optionnel)"
            value={formData.specialRequests}
            onChangeText={(text) => handleInputChange('specialRequests', text)}
            multiline
            numberOfLines={4}
            containerStyle={styles.inputContainer}
          />
        </View>
        
        <View style={[styles.summaryContainer, { backgroundColor: theme.card }]}>
          <Text style={[styles.summaryTitle, { color: theme.text }]}>
            Résumé de la réservation
          </Text>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.text + '80' }]}>
              Prix par personne
            </Text>
            <Text style={[styles.summaryValue, { color: theme.text }]}>
              {retreat.price} €
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.text + '80' }]}>
              Nombre de participants
            </Text>
            <Text style={[styles.summaryValue, { color: theme.text }]}>
              {formData.participants}
            </Text>
          </View>
          
          <View style={[styles.totalRow, { borderTopColor: theme.border }]}>
            <Text style={[styles.totalLabel, { color: theme.text }]}>
              Total
            </Text>
            <Text style={[styles.totalValue, { color: theme.primary }]}>
              {calculateTotal()} €
            </Text>
          </View>
        </View>
      </ScrollView>
      
      <View style={[styles.footer, { backgroundColor: theme.background, borderTopColor: theme.border }]}>
        <Button
          title="Confirmer la réservation"
          onPress={handleSubmit}
          loading={isSubmitting}
          style={styles.submitButton}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 50: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)'
  },
  backButton: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  placeholder: {
    width: 40
  },
  scrollView: {
    flex: 1
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100
  },
  retreatCard: {
    marginBottom: 16
  },
  formContainer: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  formHalfColumn: {
    width: '48%'
  },
  inputContainer: {
    marginBottom: 16
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8
  },
  participantsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
    height: 48
  },
  participantButton: {
    width: 48,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 1,
    borderLeftWidth: 1
  },
  participantsCount: {
    flex: 1,
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '500'
  },
  summaryContainer: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12
  },
  summaryLabel: {
    fontSize: 14
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500'
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 16,
    marginTop: 8,
    borderTopWidth: 1
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 30 : 16
  },
  submitButton: {
    width: '100%'
  }
});

export default BookingConfirmationScreen;