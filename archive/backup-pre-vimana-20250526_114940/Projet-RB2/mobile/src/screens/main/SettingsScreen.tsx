import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Switch, ScrollView, Alert, TouchableOpacity, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Text } from '../../components/ui';
import { useTheme } from '../../theme';
import { useAuth } from '../../hooks/useAuth';
import { useNetworkStatus } from '../../hooks/useNetworkStatus';
import { useFeatureFlags } from '../../hooks/useFeatureFlags';
import FeatureFlagsManager from '../../components/settings/FeatureFlagsManager';
import { getVersion, getFormattedVersion, versionManager } from '@projet-rb2/core';

const SettingsScreen = () => {
  const { theme, isDark, toggleTheme } = useTheme();
  const { logout } = useAuth();
  const { isConnected } = useNetworkStatus();
  const { isFeatureEnabled, setFeatureOverride } = useFeatureFlags();
  
  // Modal pour les feature flags
  const [featureFlagsModalVisible, setFeatureFlagsModalVisible] = useState(false);
  
  // Notification settings
  const [pushNotifications, setPushNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [marketingEmails, setMarketingEmails] = useState(false);
  
  // Privacy settings
  const [locationServices, setLocationServices] = useState(true);
  const [dataCollection, setDataCollection] = useState(true);
  
  // App settings
  const [offlineMode, setOfflineMode] = useState(false);
  const [autoPlayVideos, setAutoPlayVideos] = useState(true);
  
  // Version info
  const versionInfo = {
    version: '1.0.0',
    buildNumber: '42',
    formattedVersion: getFormattedVersion(),
    buildDate: new Date().toLocaleDateString('fr-FR'),
    environment: 'development'
  };
  
  // Synchroniser l'état du mode hors ligne avec les feature flags
  useEffect(() => {
    const syncEnabled = isFeatureEnabled('ENABLE_SYNC');
    const offlineModeEnabled = isFeatureEnabled('ENABLE_OFFLINE_MODE');
    setOfflineMode(offlineModeEnabled);
  }, [isFeatureEnabled]);
  
  const handleClearCache = () => {
    Alert.alert(
      'Vider le cache',
      'Êtes-vous sûr de vouloir vider le cache de l\'application? Cette action ne supprimera pas vos données personnelles.',
      [
        {
          text: 'Annuler',
          style: 'cancel'
        },
        {
          text: 'Vider',
          onPress: async () => {
            try {
              // Implement cache clearing logic
              // Idéalement, utiliser une fonction du package core
              // await clearAssetCache()
              
              // Simuler le nettoyage du cache
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              Alert.alert('Succès', 'Le cache a été vidé avec succès');
            } catch(error) {
              Alert.alert('Erreur', 'Une erreur est survenue lors du nettoyage du cache');
            }
          }
        },
      ]
    );
  };
  
  // Gérer le changement du mode hors ligne
  const handleOfflineModeChange = (value: boolean) => {
    setOfflineMode(value);
    
    // Mettre à jour le feature flag correspondant
    setFeatureOverride('ENABLE_OFFLINE_MODE', value);
    
    // Si on active le mode hors ligne, s'assurer que la synchronisation est activée
    if (value && !isFeatureEnabled('ENABLE_SYNC')) {
      setFeatureOverride('ENABLE_SYNC', true);
      Alert.alert(
        'Synchronisation activée',
        'La synchronisation a été automatiquement activée pour permettre le mode hors ligne.'
      );
    }
  };
  
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };
  
  const renderSettingItem = ({ icon, title, description, value, onValueChange, type = 'switch' }) => (
    <View style={[styles.settingItem, { borderBottomColor: theme.border }]}>
      <View style={styles.settingInfo}>
        <View style={[styles.iconContainer, { backgroundColor: theme.primary + '20' }]}>
          <Icon name={icon} size={22} color={theme.primary} />
        </View>
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, { color: theme.text }]}>{title}</Text>
          {description && (
            <Text style={[styles.settingDescription, { color: theme.text + '80' }]}>
              {description}
            </Text>
          )}
        </View>
      </View>
      
      {type === 'switch' && (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#767577', true: theme.primary + '80' }}
          thumbColor={value ? theme.primary : '#f4f3f4'}
          ios_backgroundColor="#3e3e3e"
        />
      )}
      
      {type === 'button' && (
        <Icon name="chevron-right" size={24} color={theme.text + '60'} />
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>Paramètres</Text>
      </View>
      
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>Apparence</Text>
          
          {renderSettingItem({
            icon: 'theme-light-dark',
            title: 'Mode sombre',
            description: 'Activer le thème sombre pour l\'application',
            value: isDark,
            onValueChange: toggleTheme
          })}
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>Notifications</Text>
          
          {renderSettingItem({
            icon: 'bell',
            title: 'Notifications push',
            description: 'Recevoir des alertes sur votre appareil',
            value: pushNotifications,
            onValueChange: setPushNotifications
          })}
          
          {renderSettingItem({
            icon: 'email',
            title: 'Notifications par email',
            description: 'Recevoir des mises à jour par email',
            value: emailNotifications,
            onValueChange: setEmailNotifications
          })}
          
          {renderSettingItem({
            icon: 'tag',
            title: 'Emails marketing',
            description: 'Recevoir des offres et promotions',
            value: marketingEmails,
            onValueChange: setMarketingEmails
          })}
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>Confidentialité</Text>
          
          {renderSettingItem({
            icon: 'map-marker',
            title: 'Services de localisation',
            description: 'Permettre l\'accès à votre position',
            value: locationServices,
            onValueChange: setLocationServices
          })}
          
          {renderSettingItem({
            icon: 'database',
            title: 'Collecte de données',
            description: 'Améliorer l\'application avec vos données d\'utilisation',
            value: dataCollection,
            onValueChange: setDataCollection
          })}
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>Application</Text>
          
          {renderSettingItem({
            icon: 'wifi-off',
            title: 'Mode hors ligne',
            description: !isConnected
              ? 'Vous êtes actuellement hors ligne' 
              : 'Accéder au contenu sans connexion internet',
            value: offlineMode,
            onValueChange: handleOfflineModeChange
          })}
          
          {renderSettingItem({
            icon: 'play-circle',
            title: 'Lecture automatique des vidéos',
            description: 'Lire automatiquement les vidéos en défilement',
            value: autoPlayVideos,
            onValueChange: setAutoPlayVideos
          })}
          
          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: theme.border }]}
            onPress={handleClearCache}
          >
            <View style={styles.settingInfo}>
              <View style={[styles.iconContainer, { backgroundColor: theme.primary + '20' }]}>
                <Icon name="cached" size={22} color={theme.primary} />
              </View>
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.text }]}>Vider le cache</Text>
                <Text style={[styles.settingDescription, { color: theme.text + '80' }]}>
                  Libérer de l'espace sur votre appareil
                </Text>
              </View>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: theme.border }]}
            onPress={() => setFeatureFlagsModalVisible(true)}
          >
            <View style={styles.settingInfo}>
              <View style={[styles.iconContainer, { backgroundColor: theme.primary + '20' }]}>
                <Icon name="flag-variant" size={22} color={theme.primary} />
              </View>
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.text }]}>Fonctionnalités expérimentales</Text>
                <Text style={[styles.settingDescription, { color: theme.text + '80' }]}>
                  Activer ou désactiver des fonctionnalités avancées
                </Text>
              </View>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: theme.border }]}
            onPress={handleLogout}
          >
            <View style={styles.settingInfo}>
              <View style={[styles.iconContainer, { backgroundColor: theme.error + '20' }]}>
                <Icon name="logout" size={22} color={theme.error} />
              </View>
              <View style={styles.settingText}>
                <Text style={[styles.settingTitle, { color: theme.text }]}>Déconnexion</Text>
                <Text style={[styles.settingDescription, { color: theme.text + '80' }]}>
                  Se déconnecter de l'application
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>À propos</Text>
          
          <View style={[styles.versionInfo, { backgroundColor: theme.cardBackground }]}>
            <Text style={[styles.versionText, { color: theme.text }]}>
              Version {versionInfo.formattedVersion}
            </Text>
            <Text style={[styles.versionDetail, { color: theme.text + '80' }]}>
              Build du {versionInfo.buildDate}
            </Text>
            <Text style={[styles.versionDetail, { color: theme.text + '80' }]}>
              Environnement: {versionInfo.environment}
            </Text>
          </View>
        </View>
      </ScrollView>
      
      <Modal
        animationType="slide"
        transparent={false}
        visible={featureFlagsModalVisible}
        onRequestClose={() => setFeatureFlagsModalVisible(false)}
      >
        <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.title, { color: theme.text }]}>Fonctionnalités expérimentales</Text>
            <TouchableOpacity
              onPress={() => setFeatureFlagsModalVisible(false)}
              style={styles.closeButton}
            >
              <Icon name="close" size={24} color={theme.text} />
            </TouchableOpacity>
          </View>
          
          <FeatureFlagsManager />
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 8,
  },
  content: {
    paddingBottom: 24,
  },
  section: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  actionItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  versionInfo: {
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  versionText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  versionDetail: {
    fontSize: 14,
    marginBottom: 4,
  },
});

export default SettingsScreen;