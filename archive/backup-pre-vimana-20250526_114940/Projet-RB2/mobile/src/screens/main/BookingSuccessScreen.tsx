import React from 'react';
import {
  View,
  StyleSheet,
  Image,
  ScrollView,
  Platform,
  Share
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme'

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme: any) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; },
  get info() { return theme.primary; } // Fallback pour 'info'
});

import { Text, Button } from '../../components/ui';
import { MainStackParamList } from '../../navigation/MainStack';

type BookingSuccessRouteProp = RouteProp<MainStackParamList, 'BookingSuccess'>;
type BookingSuccessNavigationProp = NativeStackNavigationProp<MainStackParamList, 'BookingSuccess'>;

const BookingSuccessScreen: React.FC = () => {
  const { theme: originalTheme } = useTheme();
  // Utiliser l'adaptateur de thème pour la compatibilité
  const theme = getCompatibleTheme(originalTheme);
  const navigation = useNavigation<BookingSuccessNavigationProp>();
  const route = useRoute<BookingSuccessRouteProp>();

  // Enrichir la déclaration pour inclure tous les paramètres nécessaires
  // En réalité, ces valeurs devraient venir du serveur
  const params = {
    bookingId: route.params.bookingId,
    retreatTitle: route.params.retreatTitle || 'Retraite',
    startDate: route.params.startDate || new Date().toISOString(),
    endDate: route.params.endDate || new Date().toISOString()
  };

  const { bookingId, retreatTitle, startDate, endDate } = params;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const shareBooking = async () => {
    try {
      await Share.share({
        message: `J'ai réservé une retraite "${retreatTitle}" du ${formatDate(startDate)} au ${formatDate(endDate)}. Rejoins-moi pour cette expérience incroyable! https://retreatandbe.com/bookings/${bookingId}`
      });
    } catch (error) {
      console.error('Error sharing booking:', error);
    }
  };

  const goToBookings = () => {
    navigation.navigate('Bookings');
  };

  const goToHome = () => {
    navigation.navigate('Home');
  };

  const handleDownloadReceipt = () => {
    // Implementation of handleDownloadReceipt function
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.background }]}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.successImageContainer}>
        <Image
          source={require('../../assets/images/booking-success.png')}
          style={styles.successImage}
          resizeMode="contain"
        />
      </View>
      
      <View style={styles.content}>
        <Icon
          name="check-circle"
          size={80}
          color={theme.secondary}
          style={styles.successIcon}
        />
        
        <Text style={[styles.successTitle, { color: theme.text }]}>
          Réservation confirmée !
        </Text>
        
        <Text style={[styles.successMessage, { color: theme.text + '90' }]}>
          Votre réservation pour "{retreatTitle}" a été confirmée avec succès. Vous recevrez bientôt un email de confirmation avec tous les détails.
        </Text>
        
        <View style={[styles.bookingInfoCard, { backgroundColor: theme.card, borderColor: theme.border }]}>
          <View style={styles.bookingInfoRow}>
            <Text style={[styles.bookingInfoLabel, { color: theme.text + '80' }]}>
              Numéro de réservation
            </Text>
            <Text style={[styles.bookingInfoValue, { color: theme.text }]}>
              {bookingId}
            </Text>
          </View>
          
          <View style={[styles.divider, { backgroundColor: theme.border }]} />
          
          <View style={styles.bookingInfoRow}>
            <Text style={[styles.bookingInfoLabel, { color: theme.text + '80' }]}>
              Retraite
            </Text>
            <Text style={[styles.bookingInfoValue, { color: theme.text }]}>
              {retreatTitle}
            </Text>
          </View>
          
          <View style={[styles.divider, { backgroundColor: theme.border }]} />
          
          <View style={styles.bookingInfoRow}>
            <Text style={[styles.bookingInfoLabel, { color: theme.text + '80' }]}>
              Dates
            </Text>
            <Text style={[styles.bookingInfoValue, { color: theme.text }]}>
              {formatDate(startDate)} - {formatDate(endDate)}
            </Text>
          </View>
        </View>
        
        <View style={styles.nextStepsContainer}>
          <Text style={[styles.nextStepsTitle, { color: theme.text }]}>
            Prochaines étapes
          </Text>
          
          <View style={styles.stepItem}>
            <View style={[styles.stepIconContainer, { backgroundColor: theme.primary + '20' }]}>
              <Icon name="email-outline" size={24} color={theme.primary} />
            </View>
            <View style={styles.stepTextContainer}>
              <Text style={[styles.stepTitle, { color: theme.text }]}>
                Vérifiez votre email
              </Text>
              <Text style={[styles.stepDescription, { color: theme.text + '80' }]}>
                Nous vous avons envoyé un email avec tous les détails de votre réservation.
              </Text>
            </View>
          </View>
          
          <View style={styles.stepItem}>
            <View style={[styles.stepIconContainer, { backgroundColor: theme.primary + '20' }]}>
              <Icon name="calendar-check" size={24} color={theme.primary} />
            </View>
            <View style={styles.stepTextContainer}>
              <Text style={[styles.stepTitle, { color: theme.text }]}>
                Marquez votre calendrier
              </Text>
              <Text style={[styles.stepDescription, { color: theme.text + '80' }]}>
                Ajoutez les dates de la retraite à votre calendrier pour ne pas les oublier.
              </Text>
            </View>
          </View>
          
          <View style={styles.stepItem}>
            <View style={[styles.stepIconContainer, { backgroundColor: theme.primary + '20' }]}>
              <Icon name="bag-suitcase" size={24} color={theme.primary} />
            </View>
            <View style={styles.stepTextContainer}>
              <Text style={[styles.stepTitle, { color: theme.text }]}>
                Préparez votre voyage
              </Text>
              <Text style={[styles.stepDescription, { color: theme.text + '80' }]}>
                Consultez notre guide de préparation pour savoir quoi emporter.
              </Text>
            </View>
          </View>
        </View>
        
        <Button
          title="Partager ma réservation"
          icon="share-variant"
          onPress={shareBooking}
          variant="outline"
          style={styles.actionButton}
        />
        
        <Button
          title="Voir mes réservations"
          onPress={goToBookings}
          style={styles.viewBookingsButton}
        />
        
        <Button
          title="Retour à l'accueil"
          onPress={goToHome}
          variant="text"
          style={styles.homeButton}
        />
        
        <Button
          title="Télécharger le reçu"
          icon="file-download"
          onPress={handleDownloadReceipt}
          variant="outline"
          style={styles.actionButton}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  contentContainer: {
    paddingBottom: 40
  },
  successImageContainer: {
    width: '100%',
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20
  },
  successImage: {
    width: '80%',
    height: '100%'
  },
  content: {
    padding: 24,
    alignItems: 'center'
  },
  successIcon: {
    marginBottom: 16
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center'
  },
  successMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24
  },
  bookingInfoCard: {
    width: '100%',
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
    borderWidth: 1
  },
  bookingInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12
  },
  bookingInfoLabel: {
    fontSize: 14
  },
  bookingInfoValue: {
    fontSize: 14,
    fontWeight: '600'
  },
  divider: {
    height: 1,
    width: '100%'
  },
  nextStepsContainer: {
    width: '100%',
    marginBottom: 32
  },
  nextStepsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16
  },
  stepItem: {
    flexDirection: 'row',
    marginBottom: 16
  },
  stepIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16
  },
  stepTextContainer: {
    flex: 1
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4
  },
  stepDescription: {
    fontSize: 14,
    lineHeight: 20
  },
  shareButton: {
    marginBottom: 12,
    width: '100%'
  },
  viewBookingsButton: {
    marginBottom: 12,
    width: '100%'
  },
  homeButton: {
    marginBottom: 24
  },
  actionButton: {
    marginBottom: 12,
    width: '100%'
  }
});

export default BookingSuccessScreen;