import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  ScrollView
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme';
import { SearchBar, FilterChip, Text } from '../../components/ui';
import FilterModal from '../../components/FilterModal';
import RetreatCard from '../../components/RetreatCard';
import { MainStackParamList } from '../../navigation/MainStack';

type SearchScreenNavigationProp = NativeStackNavigationProp<MainStackParamList, 'Search'>;

interface Retreat {
  id: string;
  title: string;
  location: string;
  imageUrl: string;
  startDate: string;
  endDate: string;
  price: string;
  rating?: number;
  reviewCount?: number;
  categories: string[];
  features: string[];
  duration: number;
}

// Mock filter options
const filterOptions = [
  {
    id: 'categories',
    label: 'Catégories',
    options: [
      { id: 'yoga', label: 'Yoga', icon: 'yoga' },
      { id: 'meditation', label: 'Méditation', icon: 'meditation' },
      { id: 'wellness', label: 'Bien-être', icon: 'heart-pulse' },
      { id: 'nature', label: 'Nature', icon: 'tree' },
      { id: 'spiritual', label: 'Spirituel', icon: 'candle' },
      { id: 'detox', label: 'Détox', icon: 'food-apple' }
    ]
  },
  {
    id: 'features',
    label: 'Caractéristiques',
    options: [
      { id: 'pool', label: 'Piscine', icon: 'pool' },
      { id: 'spa', label: 'Spa', icon: 'spa' },
      { id: 'wifi', label: 'Wifi', icon: 'wifi' },
      { id: 'organic', label: 'Repas bio', icon: 'leaf' },
      { id: 'private', label: 'Chambre privée', icon: 'door' },
      { id: 'beach', label: 'Plage', icon: 'beach' }
    ]
  },
  {
    id: 'duration',
    label: 'Durée',
    options: [
      { id: '2', label: '2-3 jours', icon: 'calendar-weekend' },
      { id: '4', label: '4-7 jours', icon: 'calendar-week' },
      { id: '8', label: '8-14 jours', icon: 'calendar-month' },
      { id: '15', label: '15+ jours', icon: 'calendar-range' }
    ]
  }
];

// Mock retreat data
const mockRetreats: Retreat[] = [
  {
    id: '1',
    title: 'Retraite de Yoga et Méditation',
    location: 'Mont-Saint-Michel, Normandie',
    imageUrl: 'https://images.unsplash.com/photo-1545389336-cf090694435e',
    startDate: '2025-06-15',
    endDate: '2025-06-20',
    price: '850 €',
    rating: 4.8,
    reviewCount: 24,
    categories: ['yoga', 'meditation'],
    features: ['wifi', 'organic'],
    duration: 5
  },
  {
    id: '2',
    title: 'Détox et Bien-être en Provence',
    location: 'Aix-en-Provence, PACA',
    imageUrl: 'https://images.unsplash.com/photo-1540555700478-4be289fbecef',
    startDate: '2025-07-10',
    endDate: '2025-07-17',
    price: '1200 €',
    rating: 4.5,
    reviewCount: 18,
    categories: ['wellness', 'detox'],
    features: ['pool', 'spa', 'organic'],
    duration: 7
  },
  {
    id: '3',
    title: 'Reconnexion à la Nature',
    location: 'Chamonix, Alpes',
    imageUrl: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470',
    startDate: '2025-08-05',
    endDate: '2025-08-12',
    price: '950 €',
    rating: 4.7,
    reviewCount: 32,
    categories: ['nature', 'spiritual'],
    features: ['wifi', 'private'],
    duration: 7
  },
  {
    id: '4',
    title: 'Retraite Spirituelle',
    location: 'Abbaye de Sénanque, Provence',
    imageUrl: 'https://images.unsplash.com/photo-1548625149-fc4a29cf7092',
    startDate: '2025-09-20',
    endDate: '2025-09-27',
    price: '780 €',
    rating: 4.9,
    reviewCount: 15,
    categories: ['spiritual', 'meditation'],
    features: ['organic', 'private'],
    duration: 7
  },
  {
    id: '5',
    title: 'Yoga en Bord de Mer',
    location: 'Biarritz, Pays Basque',
    imageUrl: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773',
    startDate: '2025-07-25',
    endDate: '2025-08-01',
    price: '1100 €',
    rating: 4.6,
    reviewCount: 27,
    categories: ['yoga', 'nature'],
    features: ['beach', 'pool'],
    duration: 7
  }
];

const SearchScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<SearchScreenNavigationProp>();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [retreats, setRetreats] = useState<Retreat[]>([]);
  const [filteredRetreats, setFilteredRetreats] = useState<Retreat[]>([]);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({});
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);
  
  // Load initial data
  useEffect(() => {
    fetchRetreats();
  }, []);
  
  // Apply filters when search query or selected filters change
  useEffect(() => {
    applyFilters();
  }, [searchQuery, selectedFilters, retreats]);
  
  // Count active filters
  useEffect(() => {
    const count = Object.values(selectedFilters).reduce(
      (total, filters) => total + filters.length,
      0
    );
    setActiveFiltersCount(count);
  }, [selectedFilters]);
  
  const fetchRetreats = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setRetreats(mockRetreats);
      setFilteredRetreats(mockRetreats);
    } catch(error) {
      console.error('Error fetching retreats:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const applyFilters = () => {
    let results = [...retreats];
    
    // Apply search query filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      results = results.filter(
        retreat =>
          retreat.title.toLowerCase().includes(query) ||
          retreat.location.toLowerCase().includes(query)
      );
    }
    
    // Apply category filters
    if(selectedFilters.categories?.length) {
      results = results.filter(retreat =>
        retreat.categories.some(category => selectedFilters.categories.includes(category))
      );
    }
    
    // Apply feature filters
    if(selectedFilters.features?.length) {
      results = results.filter(retreat =>
        retreat.features.some(feature => selectedFilters.features.includes(feature))
      );
    }
    
    // Apply duration filters
    if(selectedFilters.duration?.length) {
      results = results.filter(retreat => {
        const durationMatches = selectedFilters.duration.some(durationFilter => {
          const duration = parseInt(durationFilter, 10);
          switch(duration) {
            case 2:
              return retreat.duration >= 2 && retreat.duration <= 3;
            case 4:
              return retreat.duration >= 4 && retreat.duration <= 7;
            case 8:
              return retreat.duration >= 8 && retreat.duration <= 14;
            case 15:
              return retreat.duration >= 15;
            default: return false;
          }
        });
        return durationMatches;
      });
    }
    
    setFilteredRetreats(results);
  };
  
  const handleSearch = () => {
    applyFilters();
  };
  
  const clearSearch = () => {
    setSearchQuery('');
  };
  
  const toggleFilterModal = () => {
    setIsFilterModalVisible(!isFilterModalVisible);
  };
  
  const handleApplyFilters = (filters: Record<string, string[]>) => {
    setSelectedFilters(filters);
  };
  
  const clearAllFilters = () => {
    setSelectedFilters({});
  };
  
  const handleRetreatPress = (retreatId: string) => {
    navigation.navigate('RetreatDetail', { retreatId });
  };
  
  const renderRetreatItem = ({ item }: { item: Retreat }) => (
    <RetreatCard
      id={item.id}
      title={item.title}
      location={item.location}
      imageUrl={item.imageUrl}
      startDate={item.startDate}
      endDate={item.endDate}
      price={item.price}
      rating={item.rating}
      reviewCount={item.reviewCount}
      onPress={() => handleRetreatPress(item.id)}
      style={styles.retreatCard}
    />
  );
  
  const renderEmptyState = () => (
    <View style={styles.emptyStateContainer}>
      <Text style={styles.emptyStateTitle}>
        Aucune retraite trouvée
      </Text>
      <Text style={styles.emptyStateMessage}>
        Essayez de modifier vos filtres ou votre recherche
      </Text>
      {activeFiltersCount > 0 && (
        <TouchableOpacity
          style={styles.clearFiltersButton}
          onPress={clearAllFilters}
        >
          <Text style={styles.clearFiltersButtonText}>
            Réinitialiser les filtres
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <SearchBar
            placeholder="Rechercher des retraites..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmit={handleSearch}
            onClear={clearSearch}
            containerStyle={styles.searchBar}
          />
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              activeFiltersCount > 0 && styles.activeFilterButton
            ]}
            onPress={toggleFilterModal}
          >
            <Icon
              name="filter-variant"
              size={20}
              color={activeFiltersCount > 0 ? theme.primary : theme.text}
            />
            {activeFiltersCount > 0 && (
              <View
                style={[
                  styles.filterBadge,
                  { backgroundColor: theme.primary }
                ]}
              >
                <Text style={styles.filterBadgeText}>
                  {activeFiltersCount}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
        
        {activeFiltersCount > 0 && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.filtersScrollView}
            contentContainerStyle={styles.filtersContainer}
          >
            {Object.entries(selectedFilters).map(([categoryId, optionIds]) =>
              optionIds.map(optionId => {
                // Trouver la catégorie et l'option
                const category = filterOptions.find(c => c.id === categoryId);
                const option = category?.options.find(o => o.id === optionId);
                
                if (!option) return null;
                
                return (
                  <FilterChip
                    key={`${categoryId}-${optionId}`}
                    label={option.label}
                    icon={option.icon}
                    selected
                    onPress={() => {
                      // Supprimer cette option des filtres sélectionnés
                      setSelectedFilters({
                        ...selectedFilters,
                        [categoryId]: selectedFilters[categoryId].filter(
                          id => id !== optionId
                        )
                      });
                    }}
                  />
                );
              })
            )}
            
            {activeFiltersCount > 1 && (
              <TouchableOpacity
                style={styles.clearAllButton}
                onPress={clearAllFilters}
              >
                <Text style={styles.clearAllButtonText}>
                  Tout effacer
                </Text>
              </TouchableOpacity>
            )}
          </ScrollView>
        )}
      </View>
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredRetreats}
          renderItem={renderRetreatItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
        />
      )}
      
      <FilterModal
        visible={isFilterModalVisible}
        onClose={toggleFilterModal}
        onApply={handleApplyFilters}
        filterOptions={filterOptions}
        initialFilters={selectedFilters}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f8fa'
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  searchBar: {
    flex: 1,
    marginRight: 8
  },
  filterButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4
      },
      android: {
        elevation: 2
      }
    })
  },
  activeFilterButton: {
    backgroundColor: '#f0f4ff'
  },
  filterBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4
  },
  filterBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '700'
  },
  filtersScrollView: {
    marginTop: 12
  },
  filtersContainer: {
    paddingBottom: 8,
    flexDirection: 'row',
    flexWrap: 'nowrap'
  },
  listContent: {
    padding: 16,
    paddingTop: 8
  },
  retreatCard: {
    marginBottom: 16
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center'
  },
  emptyStateMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.6
  },
  clearFiltersButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f4ff'
  },
  clearFiltersButtonText: {
    color: '#5469D4',
    fontWeight: '600'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  clearAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e6e8eb',
    marginRight: 8,
    marginBottom: 8
  },
  clearAllButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#697386'
  }
});

export default SearchScreen;