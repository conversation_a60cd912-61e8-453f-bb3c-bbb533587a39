import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  Share,
  StatusBar,
  Platform
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme';
import { Text, Button } from '../../components/ui';
import { MainStackParamList } from '../../navigation/MainStack';

const { width } = Dimensions.get('window');

type RetreatDetailRouteProp = RouteProp<MainStackParamList, 'RetreatDetail'>;
type RetreatDetailNavigationProp = NativeStackNavigationProp<MainStackParamList, 'RetreatDetail'>;

interface Amenity {
  id: string;
  name: string;
  icon: string;
}

interface Instructor {
  id: string;
  name: string;
  avatar: string;
  bio: string;
}

const RetreatDetailScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<RetreatDetailNavigationProp>();
  const route = useRoute<RetreatDetailRouteProp>();
  const { retreatId } = route.params;
  
  const [isFavorite, setIsFavorite] = useState(false);
  
  // Mock data for the retreat - in a real app, this would be fetched from an API
  const retreat = {
    id: retreatId,
    title: 'Retraite de Yoga et Méditation',
    description: 'Rejoignez-nous pour une retraite de yoga et méditation de 5 jours dans un cadre magnifique. Cette retraite est conçue pour vous aider à vous reconnecter avec vous-même, à réduire le stress et à trouver la paix intérieure. Nos instructeurs expérimentés vous guideront à travers des séances de yoga quotidiennes, des méditations guidées et des ateliers de développement personnel.',
    location: 'Mont-Saint-Michel, Normandie',
    startDate: '2025-06-15',
    endDate: '2025-06-20',
    price: '850 €',
    rating: 4.8,
    reviewCount: 24,
    images: [
      'https://images.unsplash.com/photo-1545389336-cf090694435e',
      'https://images.unsplash.com/photo-1588286840104-8957b019727f',
      'https://images.unsplash.com/photo-1531685250784-7569952593d2',
    ],
    amenities: [
      { id: '1', name: 'Wifi gratuit', icon: 'wifi' },
      { id: '2', name: 'Repas bio inclus', icon: 'food-apple' },
      { id: '3', name: 'Piscine', icon: 'pool' },
      { id: '4', name: 'Spa', icon: 'spa' },
      { id: '5', name: 'Vue sur la mer', icon: 'waves' },
      { id: '6', name: 'Yoga en plein air', icon: 'yoga' },
    ] as Amenity[],
    instructors: [
      {
        id: '1',
        name: 'Sophie Martin',
        avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        bio: 'Sophie pratique le yoga depuis plus de 15 ans et enseigne depuis 8 ans. Elle est spécialisée en Hatha Yoga et Yin Yoga.'
      },
      {
        id: '2',
        name: 'Thomas Dubois',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        bio: 'Thomas est un expert en méditation et pleine conscience. Il guide des retraites depuis plus de 10 ans.'
      },
    ] as Instructor[],
    schedule: [
      { day: 'Jour 1', activities: ['Arrivée et installation', 'Dîner de bienvenue', 'Méditation du soir'] },
      { day: 'Jour 2-4', activities: ['Yoga matinal', 'Petit-déjeuner', 'Atelier de développement personnel', 'Déjeuner', 'Temps libre', 'Yoga du soir', 'Dîner', 'Méditation'] },
      { day: 'Jour 5', activities: ['Yoga matinal', 'Petit-déjeuner', 'Cérémonie de clôture', 'Départ'] },
    ]
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // In a real app, you would also update this in your backend/state management
  };

  const shareRetreat = async () => {
    try {
      await Share.share({
        message: `Découvre cette superbe retraite: ${retreat.title} à ${retreat.location}. Du ${formatDate(retreat.startDate)} au ${formatDate(retreat.endDate)}. https://retreatandbe.com/retreats/${retreat.id}`
      });
    } catch(error) {
      console.error('Error sharing retreat:', error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const handleBookNow = () => {
    navigation.navigate('BookingConfirmation', { retreatId: retreat.id });
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <StatusBar barStyle="light-content" />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: retreat.images[0] }}
            style={styles.headerImage}
            resizeMode="cover"
          />
          
          <View style={styles.imageOverlay}>
            <TouchableOpacity
              style={[styles.backButton, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}
              onPress={() => navigation.goBack()}
            >
              <Icon name="arrow-left" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}
                onPress={toggleFavorite}
              >
                <Icon
                  name={isFavorite ? 'heart' : 'heart-outline'}
                  size={24}
                  color={isFavorite ? theme.error : '#FFFFFF'}
                />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}
                onPress={shareRetreat}
              >
                <Icon name="share-variant" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={styles.imagePagination}>
            {retreat.images.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.paginationDot,
                  { backgroundColor: index === 0 ? '#FFFFFF' : 'rgba(255, 255, 255, 0.5)' },
                ]}
              />
            ))}
          </View>
        </View>
        
        <View style={styles.contentContainer}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.text }]}>
              {retreat.title}
            </Text>
            
            <View style={styles.locationContainer}>
              <Icon name="map-marker" size={16} color={theme.text + '80'} />
              <Text style={[styles.location, { color: theme.text + '80' }]}>
                {retreat.location}
              </Text>
            </View>
            
            <View style={styles.ratingContainer}>
              <View style={styles.stars}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <Icon
                    key={star}
                    name={
                      star <= Math.floor(retreat.rating)
                        ? 'star'
                        : star === Math.ceil(retreat.rating) && star > Math.floor(retreat.rating)
                        ? 'star-half-full'
                        : 'star-outline'
                    }
                    size={18}
                    color={theme.accent}
                    style={styles.starIcon}
                  />
                ))}
              </View>
              
              <Text style={[styles.ratingText, { color: theme.text + '80' }]}>
                {retreat.rating} ({retreat.reviewCount} avis)
              </Text>
            </View>
            
            <View style={styles.dateContainer}>
              <Icon name="calendar-range" size={16} color={theme.text + '80'} />
              <Text style={[styles.date, { color: theme.text + '80' }]}>
                {formatDate(retreat.startDate)} - {formatDate(retreat.endDate)}
              </Text>
            </View>
            
            <View style={[styles.priceContainer, { backgroundColor: theme.primary + '15' }]}>
              <Text style={[styles.priceLabel, { color: theme.text + '80' }]}>
                Prix par personne
              </Text>
              <Text style={[styles.price, { color: theme.primary }]}>
                {retreat.price}
              </Text>
            </View>
          </View>
          
          <View style={[styles.section, { borderBottomColor: theme.border }]}>
            <Text style={[styles.sectionTitle, { color: theme.text }]}>
              Description
            </Text>
            <Text style={[styles.description, { color: theme.text + '90' }]}>
              {retreat.description}
            </Text>
          </View>
          
          <View style={[styles.section, { borderBottomColor: theme.border }]}>
            <Text style={[styles.sectionTitle, { color: theme.text }]}>
              Équipements et services
            </Text>
            <View style={styles.amenitiesContainer}>
              {retreat.amenities.map((amenity) => (
                <View key={amenity.id} style={styles.amenityItem}>
                  <Icon name={amenity.icon} size={20} color={theme.primary} />
                  <Text style={[styles.amenityText, { color: theme.text + '90' }]}>
                    {amenity.name}
                  </Text>
                </View>
              ))}
            </View>
          </View>
          
          <View style={[styles.section, { borderBottomColor: theme.border }]}>
            <Text style={[styles.sectionTitle, { color: theme.text }]}>
              Instructeurs
            </Text>
            {retreat.instructors.map((instructor) => (
              <View key={instructor.id} style={styles.instructorContainer}>
                <Image
                  source={{ uri: instructor.avatar }}
                  style={styles.instructorAvatar}
                />
                <View style={styles.instructorInfo}>
                  <Text style={[styles.instructorName, { color: theme.text }]}>
                    {instructor.name}
                  </Text>
                  <Text style={[styles.instructorBio, { color: theme.text + '90' }]}>
                    {instructor.bio}
                  </Text>
                </View>
              </View>
            ))}
          </View>
          
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.text }]}>
              Programme
            </Text>
            {retreat.schedule.map((item, index) => (
              <View key={index} style={styles.scheduleItem}>
                <Text style={[styles.scheduleDay, { color: theme.text }]}>
                  {item.day}
                </Text>
                {item.activities.map((activity, actIndex) => (
                  <View key={actIndex} style={styles.activityItem}>
                    <View style={[styles.activityDot, { backgroundColor: theme.primary }]} />
                    <Text style={[styles.activityText, { color: theme.text + '90' }]}>
                      {activity}
                    </Text>
                  </View>
                ))}
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
      
      <View style={[styles.footer, { backgroundColor: theme.background, borderTopColor: theme.border }]}>
        <View style={styles.footerContent}>
          <View>
            <Text style={[styles.footerPrice, { color: theme.primary }]}>
              {retreat.price}
            </Text>
            <Text style={[styles.footerPriceLabel, { color: theme.text + '80' }]}>
              par personne
            </Text>
          </View>
          
          <Button
            title="Réserver maintenant"
            onPress={handleBookNow}
            style={styles.bookButton}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  scrollView: {
    flex: 1
  },
  scrollContent: {
    paddingBottom: 100
  },
  imageContainer: {
    position: 'relative',
    height: 300,
    width: '100%'
  },
  headerImage: {
    width: '100%',
    height: '100%'
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 16
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center'
  },
  actionButtons: {
    flexDirection: 'row'
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8
  },
  imagePagination: {
    position: 'absolute',
    bottom: 16,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4
  },
  contentContainer: {
    padding: 16
  },
  header: {
    marginBottom: 20
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  location: {
    fontSize: 14,
    marginLeft: 6
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  stars: {
    flexDirection: 'row',
    marginRight: 8
  },
  starIcon: {
    marginRight: 2
  },
  ratingText: {
    fontSize: 14
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  date: {
    fontSize: 14,
    marginLeft: 6
  },
  priceContainer: {
    padding: 12,
    borderRadius: 8,
    alignSelf: 'flex-start'
  },
  priceLabel: {
    fontSize: 12,
    marginBottom: 4
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  section: {
    paddingVertical: 20,
    borderBottomWidth: 1
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16
  },
  description: {
    fontSize: 16,
    lineHeight: 24
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  amenityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: 12
  },
  amenityText: {
    fontSize: 14,
    marginLeft: 8
  },
  instructorContainer: {
    flexDirection: 'row',
    marginBottom: 16
  },
  instructorAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30
  },
  instructorInfo: {
    flex: 1,
    marginLeft: 12
  },
  instructorName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4
  },
  instructorBio: {
    fontSize: 14,
    lineHeight: 20
  },
  scheduleItem: {
    marginBottom: 16
  },
  scheduleDay: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6
  },
  activityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8
  },
  activityText: {
    fontSize: 14,
    lineHeight: 20
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: Platform.OS === 'ios' ? 30 : 12
  },
  footerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  footerPrice: {
    fontSize: 20,
    fontWeight: 'bold'
  },
  footerPriceLabel: {
    fontSize: 12
  },
  bookButton: {
    width: '60%'
  }
});

export default RetreatDetailScreen;