import React, { useEffect, useState } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Text, Card } from '../../components/ui';
import { useTheme } from '../../theme';
import { SafeAreaView } from 'react-native-safe-area-context';

// Define interface for retreats
interface Retreat {
  id: string;
  title: string;
  location: string;
  date: string;
  image: string;
  price: string
}

// Mock data - replace with actual API call
const MOCK_RETREATS: Retreat[] = [
  {
    id: '1',
    title: 'Retraite Yoga & Méditation',
    location: 'Bali, Indonésie',
    date: '15-22 Juin 2025',
    image: 'https://example.com/retreat1.jpg',
    price: '1200€'},
  {
    id: '2',
    title: 'Détox & Bien-être',
    location: 'Tulum, Mexique',
    date: '3-10 Juillet 2025',
    image: 'https://example.com/retreat2.jpg',
    price: '1500€'},
  {
    id: '3',
    title: 'Mindfulness & Nature',
    location: 'Alpes, France',
    date: '20-27 Août 2025',
    image: 'https://example.com/retreat3.jpg',
    price: '950€'},
];

const HomeScreen = () => {
  const [retreats, setRetreats] = useState<Retreat[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const navigation = useNavigation<any>();
  const { theme } = useTheme()

  // Fetch retreats
  const fetchRetreats = async () => {
    try {
      // Replace with actual API call
      // const response = await fetch('https://api.example.com/retreats');
      // const data = await response.json()
      
      // Using mock data for now
      setTimeout(() => {
        setRetreats(MOCK_RETREATS);
        setLoading(false);
        setRefreshing(false);
      }, 1000);
    } catch(error) {
      console.error('Error fetching retreats:', error);
      setLoading(false);
      setRefreshing(false);
    }
  }

  useEffect(() => {
    fetchRetreats()
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchRetreats()
  };

  const renderRetreatItem = ({ item }: { item: Retreat }) => (
    <Card
      title={item.title}
      subtitle={`${item.location} • ${item.date}`}
      imageUrl={item.image}
      price={item.price}
      onPress={() => navigation.navigate('RetreatDetails', { retreatId: item.id } as never)}
      style={styles.card}
    />
  );

  if(loading && !refreshing) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color={theme.primary} />
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>Découvrez</Text>
        <Text style={[styles.subtitle, { color: theme.text }]}>
          Les meilleures retraites pour vous
        </Text>
      </View>

      <FlatList
        data={retreats}
        renderItem={renderRetreatItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.primary]}
            tintColor={theme.primary}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: theme.text }]}>
              Aucune retraite disponible pour le moment
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1},
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'},
  header: {
    padding: 20},
  title: {
    fontSize: 28,
    fontWeight: 'bold'},
  subtitle: {
    fontSize: 16,
    marginTop: 5,
    opacity: 0.8},
  listContent: {
    padding: 15},
  card: {
    marginBottom: 15},
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20},
  emptyText: {
    fontSize: 16,
    textAlign: 'center'}})

export default HomeScreen;