import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Switch,
  SafeAreaView,
  RefreshControl
} from 'react-native';
import ApiStats from '../components/ApiStats';
import { useApiMonitoring } from '../hooks/useApiMonitoring';
import { apiMonitoring } from '../utils/apiMonitoring';
import { RetreatService } from '../services/apiExample';
import { cacheManager, CachePriority } from '../utils/cacheManager';
import { offlineManager } from '../utils/offlineManager';

/**
 * Écran de tableau de bord pour visualiser et tester les performances de l'API;
 * 
 * Cet écran est destiné aux développeurs pour surveiller et déboguer;
 * les performances de l'API en environnement de développement.
 */
const ApiDashboardScreen: React.FC = () => {
  const { stats, refreshStats, flushMetrics } = useApiMonitoring(
    { debugMode: true },
    10000 // Rafraîchir toutes les 10 secondes;
  );
  
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [testResults, setTestResults] = useState<Array<{
    name: string;
    status: 'success' | 'error' | 'pending';
    duration?: number;
    message?: string;
  }>>([]);
  const [debugMode, setDebugMode] = useState(true);
  const [pendingOps, setPendingOps] = useState(0);
  
  // Rafraîchir les indicateurs au chargement;
  useEffect(() => {
    updateIndicators()
  }, []);
  
  // Mettre à jour les indicateurs clés;
  const updateIndicators = async () => {
    setPendingOps(RetreatService.getPendingOperationsCount());
  }
  
  // Exécuter plusieurs tests d'API pour générer des métriques;
  const runAllTests = async () => {
    setIsRefreshing(true);
    setTestResults([]);
    
    try {
      // Test 1: Récupération des retraites;
      await executeTest('Récupération des retraites', async () => {
        await RetreatService.getAllRetreats();
      });
      
      // Test 2: Récupération des détails d'une retraite (simulé)
      await executeTest('Détails d\'une retraite', async () => {
        await RetreatService.getRetreatDetails('12345');
      });
      
      // Test 3: Réservation d'une retraite (simulé, peut échouer)
      await executeTest('Réservation (simulée)', async () => {
        await RetreatService.bookRetreat('12345', { userId: 'user1', options: {} });
      });
      
      // Test 4: Requête cache uniquement;
      await executeTest('Cache seulement', async () => {
        await cacheManager.get('test-key', { test: true });
      });
      
      // Test 5: Synchronisation des opérations offline;
      await executeTest('Synchronisation', async () => {
        await RetreatService.syncPendingOperations();
        setPendingOps(RetreatService.getPendingOperationsCount());
      });
      
      // Mise à jour finale des statistiques;
      refreshStats();
      await flushMetrics();
      await updateIndicators();
    } finally {
      setIsRefreshing(false);
    }
  }
  
  // Exécuter un test individuel avec gestion des erreurs;
  const executeTest = async (name: string, testFn: () => Promise<any>) => {
    setTestResults(prev => [...prev, { 
      name, 
      status: 'pending'
    }]);
    
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      
      setTestResults(prev => 
        prev.map(test => 
          test.name === name
            ? { ...test, status: 'success', duration } 
            : test
        )
      );
      
      // Ajouter une métrique personnalisée pour le test;
      apiMonitoring.addCustomMetric(`test.${name.toLowerCase().replace(/\s+/g, '_')}`, duration, {
        testName: name,
        success: true
      });
      
    } catch(error: any) {
      setTestResults(prev => 
        prev.map(test => 
          test.name === name
            ? { 
                ...test, 
                status: 'error', 
                duration: Date.now() - startTime,
                message: error?.message || 'Erreur inconnue'
              } 
            : test
        )
      );
      
      // Ajouter une métrique personnalisée pour l'échec;
      apiMonitoring.addCustomMetric(`test.${name.toLowerCase().replace(/\s+/g, '_')}`, Date.now() - startTime, {
        testName: name,
        success: false,
        error: error?.message
      });
    }
  }
  
  // Vider tous les caches;
  const clearAllCaches = async () => {
    await cacheManager.clear();
    await RetreatService.clearRetreatsCache();
    refreshStats();
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Tableau de bord API</Text>
        <View style={styles.debugToggle}>
          <Text style={styles.debugLabel}>Mode débogage</Text>
          <Switch
            value={debugMode}
            onValueChange={setDebugMode}
          />
        </View>
      </View>
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={runAllTests}
          />
        }
      >
        {/* Statistiques API */}
        <ApiStats />
        
        {/* Indicateurs */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Indicateurs</Text>
          <View style={styles.indicatorsContainer}>
            <View style={styles.indicator}>
              <Text style={styles.indicatorValue}>{stats.totalRequests}</Text>
              <Text style={styles.indicatorLabel}>Requêtes</Text>
            </View>
            
            <View style={styles.indicator}>
              <Text style={styles.indicatorValue}>{pendingOps}</Text>
              <Text style={styles.indicatorLabel}>Opérations en attente</Text>
            </View>
            
            <View style={styles.indicator}>
              <Text style={[
                styles.indicatorValue,
                stats.errorRate > 0.1 ? styles.errorText : styles.successText
              ]}>
                {(stats.errorRate * 100).toFixed(1)}%
              </Text>
              <Text style={styles.indicatorLabel}>Taux d'erreur</Text>
            </View>
          </View>
        </View>
        
        {/* Tests récents */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tests récents</Text>
          {testResults.length === 0 ? (
            <Text style={styles.emptyMessage}>
              Aucun test exécuté. Tirez vers le bas pour lancer les tests.
            </Text>
          ) : (
            <View style={styles.testResults}>
              {testResults.map((test, index) => (
                <View key={index} style={styles.testItem}>
                  <View style={styles.testHeader}>
                    <Text style={styles.testName}>{test.name}</Text>
                    <View style={[
                      styles.statusBadge,
                      test.status === 'success' ? styles.successBadge :
                      test.status === 'error' ? styles.errorBadge :
                      styles.pendingBadge
                    ]}>
                      <Text style={styles.statusText}>
                        {test.status === 'success' ? 'Succès' :
                         test.status === 'error' ? 'Échec' :
                         'En cours'}
                      </Text>
                    </View>
                  </View>
                  
                  {test.duration !== undefined && (
                    <Text style={styles.testDuration}>
                      {test.duration} ms
                    </Text>
                  )}
                  
                  {test.message && (
                    <Text style={styles.errorMessage}>{test.message}</Text>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
        
        {/* Contrôles */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contrôles</Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.button}
              onPress={runAllTests}
              disabled={isRefreshing}
            >
              <Text style={styles.buttonText}>
                Exécuter tous les tests
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, styles.dangerButton]} 
              onPress={clearAllCaches}
            >
              <Text style={styles.buttonText}>
                Vider le cache
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.button}
              onPress={() => RetreatService.syncPendingOperations()}
            >
              <Text style={styles.buttonText}>
                Synchroniser
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.button}
              onPress={flushMetrics}
            >
              <Text style={styles.buttonText}>
                Envoyer les métriques
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Informations de débogage */}
        {debugMode && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations de débogage</Text>
            <View style={styles.debugInfo}>
              <Text style={styles.debugText}>
                Réseau: {stats.networkType}
              </Text>
              <Text style={styles.debugText}>
                Version API: {1.0}
              </Text>
              <Text style={styles.debugText}>
                Connexion: {/* Statut de connexion géré par offlineManager */}
              </Text>
              <Text style={styles.debugText}>
                Mode échantillonnage: {10}%
              </Text>
            </View>
          </View>
        )}
        
        {/* Espace en bas pour le scroll */}
        <View style={{ height: 40 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef'
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212529'
  },
  debugToggle: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  debugLabel: {
    marginRight: 8,
    fontSize: 14,
    color: '#6c757d'
  },
  scrollView: {
    flex: 1,
    padding: 16
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 1
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#212529'
  },
  indicatorsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  indicator: {
    alignItems: 'center',
    flex: 1
  },
  indicatorValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212529'
  },
  indicatorLabel: {
    fontSize: 12,
    color: '#6c757d',
    marginTop: 4
  },
  successText: {
    color: '#28a745'
  },
  errorText: {
    color: '#dc3545'
  },
  emptyMessage: {
    textAlign: 'center',
    color: '#6c757d',
    fontStyle: 'italic',
    padding: 16
  },
  testResults: {
    marginTop: 8
  },
  testItem: {
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    padding: 12,
    marginBottom: 8
  },
  testHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4
  },
  testName: {
    fontWeight: 'bold',
    color: '#212529',
    flex: 1
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4
  },
  successBadge: {
    backgroundColor: '#d4edda'
  },
  errorBadge: {
    backgroundColor: '#f8d7da'
  },
  pendingBadge: {
    backgroundColor: '#fff3cd'
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold'
  },
  testDuration: {
    fontSize: 12,
    color: '#6c757d'
  },
  errorMessage: {
    marginTop: 8,
    color: '#dc3545',
    fontSize: 12
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8
  },
  button: {
    backgroundColor: '#007bff',
    borderRadius: 6,
    padding: 12,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4
  },
  dangerButton: {
    backgroundColor: '#dc3545'
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14
  },
  debugInfo: {
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    padding: 12
  },
  debugText: {
    fontFamily: 'monospace',
    fontSize: 12,
    color: '#212529',
    marginBottom: 4
  }
});

export default ApiDashboardScreen;