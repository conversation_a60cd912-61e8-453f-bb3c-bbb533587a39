import React, { useEffect, useCallback } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  StatusBar,
  useColorScheme,
  View,
  TouchableOpacity,
  Text
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { DashboardStackParamList } from '../navigation/navigationTypes';
import CustomizableDashboard from '../components/CustomizableDashboard';
import useMonitoring from '../hooks/useMonitoring';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

type DashboardScreenProps = {
  navigation: StackNavigationProp<DashboardStackParamList, 'DashboardMain'>;
}

/**
 * Écran du tableau de bord principal
 */
const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation }) => {
  const colorScheme = useColorScheme();
  const isDarkMode = colorScheme === 'dark';
  const { trackUserAction, measurePerformance } = useMonitoring('DashboardScreen');
  
  // Suivi de l'écran
  useEffect(() => {
    trackUserAction('view_dashboard_screen');
    
    // Simuler des métriques de performance pour la démo
    const interval = setInterval(() => {
      simulateMetrics();
    }, 10000);
    
    return () => clearInterval(interval);
  }, [trackUserAction]);
  
  // Simuler des métriques pour la démo
  const simulateMetrics = useCallback(() => {
    // CPU, mémoire, batterie
    const cpuUsage = Math.random() * 100;
    const memoryUsage = Math.random() * 100;
    const batteryUsage = Math.random() * 5;
    
    // Performances
    const startupTime = 500 + Math.random() * 1000;
    const httpRequests = Math.round(Math.random() * 50);
    const componentPerformance = 16 + Math.random() * 50;
    const operations = Math.round(Math.random() * 200);
    
    // Mesurer la performance de diverses opérations
    measurePerformance('cpu_usage', () => {}, { value: cpuUsage });
    measurePerformance('memory_usage', () => {}, { value: memoryUsage });
    measurePerformance('battery_usage', () => {}, { value: batteryUsage });
    measurePerformance('startup_time', () => {}, { value: startupTime });
    measurePerformance('http_requests', () => {}, { value: httpRequests });
    measurePerformance('component_performance', () => {}, { value: componentPerformance });
    measurePerformance('operations', () => {}, { value: operations });
  }, [measurePerformance]);

  // Navigation vers l'écran de roadmap
  const navigateToRoadmap = useCallback(() => {
    trackUserAction('navigate_to_roadmap');
    navigation.navigate('Roadmap');
  }, [navigation, trackUserAction]);
  
  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      
      <View style={styles.headerContainer}>
        <Text style={[styles.headerTitle, isDarkMode && styles.headerTitleDark]}>
          Tableau de bord
        </Text>
        <TouchableOpacity
          style={[styles.roadmapButton, isDarkMode && styles.roadmapButtonDark]}
          onPress={navigateToRoadmap}
        >
          <Icon name="road-variant" size={18} color={isDarkMode ? '#FFFFFF' : '#333333'} />
          <Text style={[styles.roadmapButtonText, isDarkMode && styles.roadmapButtonTextDark]}>
            Roadmap
          </Text>
        </TouchableOpacity>
      </View>
      
      <CustomizableDashboard />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA'
  },
  containerDark: {
    backgroundColor: '#121212'
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333'
  },
  headerTitleDark: {
    color: '#FFFFFF'
  },
  roadmapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E5F1FF',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16
  },
  roadmapButtonDark: {
    backgroundColor: '#0A84FF'
  },
  roadmapButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginLeft: 4
  },
  roadmapButtonTextDark: {
    color: '#FFFFFF'
  }
});

export default DashboardScreen;