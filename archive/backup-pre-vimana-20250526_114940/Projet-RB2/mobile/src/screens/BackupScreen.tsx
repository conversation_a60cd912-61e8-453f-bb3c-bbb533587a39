import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { database } from '../services/database.service';
import backupManager, { BACKUP_EVENTS } from '../utils/backupManager';
import { EventEmitter } from '../utils/eventEmitter';
import { useTheme } from '../theme';

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; }
});

interface BackupItem {
  path: string;
  name: string;
  date: Date;
  size: number;
}

interface BackupScreenProps {
  navigation: any;
}

const BackupScreen: React.FC<BackupScreenProps> = ({ navigation }) => {
  const { theme: originalTheme } = useTheme();
  // Utiliser l'adaptateur de thème pour la compatibilité
  const theme = getCompatibleTheme(originalTheme);
  const [backups, setBackups] = useState<BackupItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [operation, setOperation] = useState<'idle' | 'backup' | 'restore'>('idle');

  // Initialiser le gestionnaire de sauvegarde
  useEffect(() => {
    backupManager.initialize(database);
    loadBackups();

    // Écouter les événements de sauvegarde
    const backupStartedListener = EventEmitter.on(
      BACKUP_EVENTS.BACKUP_STARTED,
      () => setOperation('backup')
    );
    
    const backupCompletedListener = EventEmitter.on(
      BACKUP_EVENTS.BACKUP_COMPLETED,
      () => {
        setOperation('idle');
        loadBackups();
      }
    );
    
    const backupFailedListener = EventEmitter.on(
      BACKUP_EVENTS.BACKUP_FAILED,
      (error: Error) => {
        setOperation('idle');
        Alert.alert(
          'Erreur de sauvegarde',
          `La sauvegarde a échoué: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
        );
      }
    );
    
    const restoreStartedListener = EventEmitter.on(
      BACKUP_EVENTS.RESTORE_STARTED,
      () => setOperation('restore')
    );
    
    const restoreCompletedListener = EventEmitter.on(
      BACKUP_EVENTS.RESTORE_COMPLETED,
      () => {
        setOperation('idle');
        Alert.alert(
          'Restauration terminée',
          'La restauration a été effectuée avec succès. L\'application va redémarrer.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Ici, vous pourriez redémarrer l'application ou naviguer vers l'écran d'accueil
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Main' }],
                });
              },
            },
          ]
        );
      }
    );
    
    const restoreFailedListener = EventEmitter.on(
      BACKUP_EVENTS.RESTORE_FAILED,
      (error: Error) => {
        setOperation('idle');
        Alert.alert(
          'Erreur de restauration',
          `La restauration a échoué: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
        );
      }
    );

    return () => {
      backupStartedListener.off();
      backupCompletedListener.off();
      backupFailedListener.off();
      restoreStartedListener.off();
      restoreCompletedListener.off();
      restoreFailedListener.off();
    };
  }, [navigation]);

  // Charger la liste des sauvegardes
  const loadBackups = async () => {
    try {
      setLoading(true);
      const backupList = await backupManager.listBackups();
      setBackups(backupList);
    } catch (error) {
      console.error('Erreur lors du chargement des sauvegardes:', error);
      Alert.alert(
        'Erreur',
        'Impossible de charger la liste des sauvegardes'
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Rafraîchir la liste des sauvegardes
  const handleRefresh = () => {
    setRefreshing(true);
    loadBackups();
  };

  // Créer une nouvelle sauvegarde
  const handleCreateBackup = async () => {
    try {
      Alert.alert(
        'Créer une sauvegarde',
        'Voulez-vous créer une sauvegarde complète de vos données ?',
        [
          { text: 'Annuler', style: 'cancel' },
          {
            text: 'Créer',
            onPress: async () => {
              const backupPath = await backupManager.createBackup();
              await backupManager.shareBackup(backupPath);
            },
          },
        ]
      );
    } catch (error) {
      console.error('Erreur lors de la création de la sauvegarde:', error);
      Alert.alert(
        'Erreur',
        'Impossible de créer la sauvegarde'
      );
    }
  };

  // Restaurer une sauvegarde
  const handleRestoreBackup = (backup: BackupItem) => {
    Alert.alert(
      'Restaurer la sauvegarde',
      'Attention: cette action va remplacer toutes vos données actuelles. Voulez-vous continuer ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Restaurer',
          style: 'destructive',
          onPress: async () => {
            try {
              await backupManager.restoreBackup(backup.path);
            } catch (error) {
              console.error('Erreur lors de la restauration:', error);
            }
          },
        },
      ]
    );
  };

  // Supprimer une sauvegarde
  const handleDeleteBackup = (backup: BackupItem) => {
    Alert.alert(
      'Supprimer la sauvegarde',
      'Êtes-vous sûr de vouloir supprimer cette sauvegarde ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              await backupManager.deleteBackup(backup.path);
              loadBackups();
            } catch (error) {
              console.error('Erreur lors de la suppression:', error);
              Alert.alert(
                'Erreur',
                'Impossible de supprimer la sauvegarde'
              );
            }
          },
        },
      ]
    );
  };

  // Partager une sauvegarde
  const handleShareBackup = async (backup: BackupItem) => {
    try {
      await backupManager.shareBackup(backup.path);
    } catch (error) {
      console.error('Erreur lors du partage:', error);
      Alert.alert(
        'Erreur',
        'Impossible de partager la sauvegarde'
      );
    }
  };

  // Formater la taille du fichier
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // Rendu d'un élément de la liste
  const renderBackupItem = ({ item }: { item: BackupItem }) => (
    <View style={[styles.backupItem, { backgroundColor: theme.card }]}>
      <View style={styles.backupInfo}>
        <Text style={[styles.backupDate, { color: theme.text }]}>
          {item.date.toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          })}
        </Text>
        <Text style={[styles.backupSize, { color: theme.textSecondary }]}>
          {formatFileSize(item.size)}
        </Text>
      </View>
      
      <View style={styles.backupActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.primary }]}
          onPress={() => handleShareBackup(item)}
          disabled={operation !== 'idle'}
        >
          <Icon name="share-variant" size={16} color="#FFF" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.success }]}
          onPress={() => handleRestoreBackup(item)}
          disabled={operation !== 'idle'}
        >
          <Icon name="restore" size={16} color="#FFF" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.error }]}
          onPress={() => handleDeleteBackup(item)}
          disabled={operation !== 'idle'}
        >
          <Icon name="delete" size={16} color="#FFF" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-left" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.text }]}>
          Sauvegardes
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {operation !== 'idle' && (
        <View style={[styles.operationOverlay, { backgroundColor: 'rgba(0,0,0,0.7)' }]}>
          <ActivityIndicator size="large" color={theme.primary} />
          <Text style={[styles.operationText, { color: '#FFF' }]}>
            {operation === 'backup'
              ? 'Création de la sauvegarde...'
              : 'Restauration en cours...'}
          </Text>
        </View>
      )}

      <View style={styles.content}>
        <View style={styles.createSection}>
          <TouchableOpacity
            style={[styles.createButton, { backgroundColor: theme.primary }]}
            onPress={handleCreateBackup}
            disabled={operation !== 'idle'}
          >
            <Icon name="backup-restore" size={22} color="#FFF" />
            <Text style={styles.createButtonText}>Créer une sauvegarde</Text>
          </TouchableOpacity>
          
          <Text style={[styles.infoText, { color: theme.textSecondary }]}>
            Sauvegardez vos données pour les restaurer ultérieurement ou les transférer vers un autre appareil.
          </Text>
        </View>

        <View style={styles.listSection}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>
            Sauvegardes disponibles
          </Text>
          
          {loading && !refreshing ? (
            <ActivityIndicator size="large" color={theme.primary} style={styles.loader} />
          ) : backups.length === 0 ? (
            <View style={styles.emptyState}>
              <Icon name="backup-restore" size={64} color={theme.secondary} />
              <Text style={[styles.emptyText, { color: theme.text }]}>
                Aucune sauvegarde disponible
              </Text>
            </View>
          ) : (
            <FlatList
              data={backups}
              renderItem={renderBackupItem}
              keyExtractor={(item) => item.path}
              contentContainerStyle={styles.listContent}
              onRefresh={handleRefresh}
              refreshing={refreshing}
            />
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  createSection: {
    marginBottom: 24,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 14,
    borderRadius: 8,
    marginBottom: 12,
  },
  createButtonText: {
    color: '#FFF',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 10,
  },
  infoText: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  listSection: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  listContent: {
    paddingBottom: 20,
  },
  backupItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backupInfo: {
    flex: 1,
  },
  backupDate: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  backupSize: {
    fontSize: 12,
  },
  backupActions: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    marginTop: 12,
  },
  loader: {
    marginTop: 40,
  },
  operationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  operationText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default BackupScreen; 