import React, { useState } from 'react';
import { View, StyleSheet, Alert, ViewStyle } from 'react-native';
import { useDispatch } from 'react-redux';
import { Button, TextInput, Text } from '../../components/ui';
import { login } from '../../store/slices/authSlice';
import { useTheme } from '../../theme';

export const LoginScreen = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const { theme } = useTheme();

  const handleLogin = async () => {
    try {
      setLoading(true);
      // @ts-ignore - Ignoring type issues with Redux action
      await dispatch(login({ email, password }));
    } catch(error: any) {
      Alert.alert('Erreur', error?.message || 'Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Text style={[styles.title, { color: theme.text }]}>Connexion</Text>
      <TextInput
        value={email}
        onChangeText={setEmail}
        placeholder="Email"
        keyboardType="email-address"
        autoCapitalize="none"
        style={[styles.input, { borderColor: theme.border }]}
        placeholderTextColor={theme.text + '80'}
      />
      <TextInput
        value={password}
        onChangeText={setPassword}
        placeholder="Mot de passe"
        secureTextEntry
        style={[styles.input, { borderColor: theme.border }]}
        placeholderTextColor={theme.text + '80'}
      />
      <Button
        title="Se connecter"
        onPress={handleLogin}
        loading={loading}
        // @ts-ignore - Style properties will be handled by the Button component
        style={[styles.button, { backgroundColor: theme.primary }]}
        textStyle={{ color: '#FFFFFF' }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center'
  },
  input: {
    marginBottom: 15,
    borderWidth: 1,
    borderRadius: 8,
    padding: 15
  },
  button: {
    marginTop: 10,
    padding: 15,
    borderRadius: 8
  }
});