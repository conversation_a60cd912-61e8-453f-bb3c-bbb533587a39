import React, { useState } from 'react';
import { View, StyleSheet, Alert, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Button, TextInput, Text } from '../../components/ui';
import { useTheme } from '../../theme';
import { useAuth } from '../../hooks/useAuth';

const RegisterScreen = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  // @ts-ignore - Navigation typing issues will be handled separately
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { login } = useAuth();

  const handleRegister = async () => {
    // Basic validation
    if(!name || !email || !password || !confirmPassword) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs');
      return;
    }

    if(password !== confirmPassword) {
      Alert.alert('Erreur', 'Les mots de passe ne correspondent pas');
      return;
    }

    try {
      setLoading(true);
      
      // Replace with actual API call
      const response = await fetch('https://api.example.com/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name, email, password })
      });
      
      const data = await response.json();
      
      if(!response.ok) {
        throw new Error(data.message || 'Inscription échouée');
      }
      
      Alert.alert(
        'Succès', 
        'Votre compte a été créé avec succès',
        [
          {
            text: 'OK',
            onPress: () => {
              // Auto-login after successful registration
              login(email, password);
            }
          }
        ]
      );
    } catch(error: any) {
      Alert.alert('Erreur', error.message || 'Inscription échouée');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView
      contentContainerStyle={[
        styles.container, 
        { backgroundColor: theme.background }
      ]}
    >
      <Text style={[styles.title, { color: theme.text }]}>Créer un compte</Text>
      
      <TextInput
        value={name}
        onChangeText={setName}
        placeholder="Nom complet"
        autoCapitalize="words"
        style={[styles.input, { borderColor: theme.border }]}
        placeholderTextColor={theme.text + '80'}
      />
      
      <TextInput
        value={email}
        onChangeText={setEmail}
        placeholder="Email"
        keyboardType="email-address"
        autoCapitalize="none"
        style={[styles.input, { borderColor: theme.border }]}
        placeholderTextColor={theme.text + '80'}
      />
      
      <TextInput
        value={password}
        onChangeText={setPassword}
        placeholder="Mot de passe"
        secureTextEntry
        style={[styles.input, { borderColor: theme.border }]}
        placeholderTextColor={theme.text + '80'}
      />
      
      <TextInput
        value={confirmPassword}
        onChangeText={setConfirmPassword}
        placeholder="Confirmer le mot de passe"
        secureTextEntry
        style={[styles.input, { borderColor: theme.border }]}
        placeholderTextColor={theme.text + '80'}
      />
      
      <Button
        title="S'inscrire"
        onPress={handleRegister}
        loading={loading}
        // @ts-ignore - Style will be handled by the Button component
        style={[styles.button, { backgroundColor: theme.primary }]}
        textStyle={{ color: '#FFFFFF' }}
      />
      
      <View style={styles.footer}>
        <Text style={{ color: theme.text }}>Vous avez déjà un compte? </Text>
        <Text
          style={{ color: theme.primary, fontWeight: 'bold' }}
          // @ts-ignore - Navigation typing issues will be handled separately
          onPress={() => navigation.navigate('Login')}
        >
          Se connecter
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center'
  },
  input: {
    marginBottom: 15,
    borderWidth: 1,
    borderRadius: 8,
    padding: 15
  },
  button: {
    marginTop: 10,
    padding: 15,
    borderRadius: 8
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20
  }
});

export default RegisterScreen;