import React, { useState } from 'react';
import { View, StyleSheet, Alert, Text } from 'react-native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import { AuthStackParamList } from '../../navigation/types';
import Button from '../../components/ui/Button';
import TextInput from '../../components/ui/TextInput';
import { useTheme } from '../../hooks/useTheme';
import { resetPassword } from '../../services/api/auth';

// Correction du typage pour useNavigation
type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'ForgotPassword'>;

const ForgotPasswordScreen = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);
  const navigation = useNavigation<ForgotPasswordScreenNavigationProp>();
  const { theme } = useTheme();

  const handleResetPassword = async () => {
    // Basic validation
    if(!email) {
      Alert.alert('Erreur', 'Veuillez saisir votre adresse email');
      return;
    }

    try {
      setLoading(true);
      
      // Appel du service de réinitialisation
      await resetPassword(email);
      
      setSent(true);
    } catch(error) {
      // Gestion sécurisée de l'erreur
      const errorMessage = error instanceof Error ? error.message : 'Échec de la demande de réinitialisation';
      Alert.alert('Erreur', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {!sent ? (
        <>
          <Text style={[styles.title, { color: theme.text }]}>
            Mot de passe oublié
          </Text>
          <Text style={[styles.subtitle, { color: theme.text }]}>
            Entrez votre adresse email pour recevoir un lien de réinitialisation
          </Text>
          
          <TextInput
            value={email}
            onChangeText={setEmail}
            placeholder="Email"
            keyboardType="email-address"
            autoCapitalize="none"
            style={[styles.input, { borderColor: theme.border }]}
            placeholderTextColor={theme.text + '80'}
          />
          
          <Button
            title="Réinitialiser le mot de passe"
            onPress={handleResetPassword}
            loading={loading}
            style={{
              marginTop: 10,
              padding: 15,
              borderRadius: 8,
              backgroundColor: theme.primary
            }}
            textStyle={{ color: '#FFFFFF'}}
          />
        </>
      ) : (
        <>
          <Text style={[styles.title, { color: theme.text }]}>
            Email envoyé!
          </Text>
          <Text style={[styles.subtitle, { color: theme.text }]}>
            Nous avons envoyé un lien de réinitialisation à {email}. Veuillez vérifier votre boîte de réception.
          </Text>
          
          <Button
            title="Retour à la connexion"
            onPress={() => navigation.navigate('Login')}
            style={{
              marginTop: 10,
              padding: 15,
              borderRadius: 8,
              backgroundColor: theme.primary
            }}
            textStyle={{ color: '#FFFFFF'}}
          />
        </>
      )}
      
      <View style={styles.footer}>
        <Text
          style={{ color: theme.primary, fontWeight: 'bold' }}
          onPress={() => navigation.navigate('Login')}
        >
          Retour à la connexion
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center'
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
    textAlign: 'center'
  },
  input: {
    marginBottom: 15,
    borderWidth: 1,
    borderRadius: 8,
    padding: 15
  },
  button: {
    marginTop: 10,
    padding: 15,
    borderRadius: 8
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20
  }
});

export default ForgotPasswordScreen;