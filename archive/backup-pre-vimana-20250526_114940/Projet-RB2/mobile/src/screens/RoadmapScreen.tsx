import React, { useEffect } from 'react';
import { SafeAreaView, StyleSheet, useColorScheme } from 'react-native';
import RoadmapView, { RoadmapTask } from '../components/RoadmapView';
import useMonitoring from '../hooks/useMonitoring';
import { v4 as uuidv4 } from 'uuid';

/**
 * Écran affichant la roadmap du projet
 */
const RoadmapScreen: React.FC = () => {
  const colorScheme = useColorScheme();
  const isDarkMode = colorScheme === 'dark';
  const { trackUserAction } = useMonitoring('RoadmapScreen');

  // Suivi de l'écran
  useEffect(() => {
    trackUserAction('view_roadmap_screen');
  }, [trackUserAction]);

  // Définition des tâches de la roadmap moyen terme
  const mediumTermTasks: RoadmapTask[] = [
    {
      id: uuidv4(),
      title: "Implémentation d'un moteur de règles en temps réel",
      status: 'in-progress',
      subtasks: [
        {
          id: uuidv4(),
          title: "Configuration de base du moteur",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Intégration avec le système de monitoring",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Optimisation des performances sous charge",
          status: 'pending'
        },
        {
          id: uuidv4(),
          title: "Tests de validation à grande échelle",
          status: 'pending'
        }
      ]
    },
    {
      id: uuidv4(),
      title: "Développement d'un système de détection des anomalies par apprentissage",
      status: 'pending',
      subtasks: [
        {
          id: uuidv4(),
          title: "Collecte et préparation des données historiques",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Implémentation du modèle de base",
          status: 'in-progress'
        },
        {
          id: uuidv4(),
          title: "Intégration avec le système existant",
          status: 'pending'
        },
        {
          id: uuidv4(),
          title: "Phase d'apprentissage et calibration",
          status: 'pending'
        }
      ]
    },
    {
      id: uuidv4(),
      title: "Mise en place d'un système de déploiement continu multi-environnement",
      status: 'in-progress',
      subtasks: [
        {
          id: uuidv4(),
          title: "Configuration des pipelines de base",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Intégration avec le système de monitoring",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Tests automatisés cross-platform",
          status: 'in-progress'
        },
        {
          id: uuidv4(),
          title: "Validation des déploiements automatiques",
          status: 'pending'
        }
      ]
    },
    {
      id: uuidv4(),
      title: "Implémentation d'un module de diffusion sélective (selective broadcast)",
      status: 'completed',
      subtasks: [
        {
          id: uuidv4(),
          title: "Architecture de base du système",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Optimisation de la bande passante",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Tests de performance",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Documentation complète",
          status: 'completed'
        }
      ]
    },
    {
      id: uuidv4(),
      title: "Création d'un système de reconstruction d'état optimisé (optimistic UI)",
      status: 'in-progress',
      subtasks: [
        {
          id: uuidv4(),
          title: "Architecture de base",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Gestion des conflits",
          status: 'completed'
        },
        {
          id: uuidv4(),
          title: "Synchronisation multi-device",
          status: 'in-progress'
        },
        {
          id: uuidv4(),
          title: "Tests de robustesse",
          status: 'pending'
        }
      ]
    }
  ];

  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
      <RoadmapView
        title="Roadmap de Développement"
        period="Moyen Terme (30 jours)"
        description="Suivi des projets en cours et planification des objectifs à atteindre dans les 30 prochains jours."
        tasks={mediumTermTasks}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA'
  },
  containerDark: {
    backgroundColor: '#121212'
  }
});

export default RoadmapScreen;