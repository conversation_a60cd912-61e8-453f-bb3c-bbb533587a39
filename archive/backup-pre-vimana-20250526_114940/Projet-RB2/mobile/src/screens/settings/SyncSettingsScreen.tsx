import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSynchronization } from '../../hooks/useSynchronization';
import SyncStatusIndicator from '../../components/ui/SyncStatusIndicator';
import { SyncService } from '../../services/sync.service';
import { useTheme } from '../../theme';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Clés pour les paramètres de synchronisation
const SYNC_SETTINGS_KEYS = {
  AUTO_SYNC: 'sync_auto_enabled',
  SYNC_INTERVAL: 'sync_interval',
  SYNC_ON_APP_START: 'sync_on_app_start',
  SYNC_ONLY_ON_WIFI: 'sync_only_on_wifi',
  SYNC_BACKGROUND: 'sync_background',
};

// Options d'intervalle de synchronisation en minutes
const SYNC_INTERVALS = [
  { label: '5 minutes', value: 5 },
  { label: '15 minutes', value: 15 },
  { label: '30 minutes', value: 30 },
  { label: '1 heure', value: 60 },
  { label: '3 heures', value: 180 },
  { label: '6 heures', value: 360 },
  { label: '12 heures', value: 720 },
  { label: '24 heures', value: 1440 },
];

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; }
});

const SyncSettingsScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const { theme: originalTheme } = useTheme();
  // Utiliser l'adaptateur de thème pour la compatibilité
  const theme = getCompatibleTheme(originalTheme);
  const {
    isSyncing,
    lastSyncTimestamp,
    formatLastSyncTime,
    pendingConflicts,
    pendingOperationsCount,
    triggerSync,
  } = useSynchronization();

  // États pour les paramètres
  const [autoSync, setAutoSync] = useState(true);
  const [syncInterval, setSyncInterval] = useState(30); // 30 minutes par défaut
  const [syncOnAppStart, setSyncOnAppStart] = useState(true);
  const [syncOnlyOnWifi, setSyncOnlyOnWifi] = useState(false);
  const [syncInBackground, setSyncInBackground] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [resetConfirmVisible, setResetConfirmVisible] = useState(false);

  // Charger les paramètres enregistrés
  useEffect(() => {
    loadSettings();
  }, []);

  // Enregistrer les paramètres lors de leurs modifications
  useEffect(() => {
    if (!isLoading) {
      saveSettings();
    }
  }, [autoSync, syncInterval, syncOnAppStart, syncOnlyOnWifi, syncInBackground, isLoading]);

  // Charger les paramètres de synchronisation
  const loadSettings = async () => {
    try {
      setIsLoading(true);
      
      const autoSyncValue = await AsyncStorage.getItem(SYNC_SETTINGS_KEYS.AUTO_SYNC);
      const intervalValue = await AsyncStorage.getItem(SYNC_SETTINGS_KEYS.SYNC_INTERVAL);
      const onStartValue = await AsyncStorage.getItem(SYNC_SETTINGS_KEYS.SYNC_ON_APP_START);
      const onlyWifiValue = await AsyncStorage.getItem(SYNC_SETTINGS_KEYS.SYNC_ONLY_ON_WIFI);
      const backgroundValue = await AsyncStorage.getItem(SYNC_SETTINGS_KEYS.SYNC_BACKGROUND);
      
      if (autoSyncValue !== null) setAutoSync(autoSyncValue === 'true');
      if (intervalValue !== null) setSyncInterval(parseInt(intervalValue, 10));
      if (onStartValue !== null) setSyncOnAppStart(onStartValue === 'true');
      if (onlyWifiValue !== null) setSyncOnlyOnWifi(onlyWifiValue === 'true');
      if (backgroundValue !== null) setSyncInBackground(backgroundValue === 'true');
      
      setIsLoading(false);
    } catch (error) {
      console.error('Erreur lors du chargement des paramètres:', error);
      setIsLoading(false);
    }
  };

  // Enregistrer les paramètres de synchronisation
  const saveSettings = async () => {
    try {
      await AsyncStorage.setItem(SYNC_SETTINGS_KEYS.AUTO_SYNC, autoSync.toString());
      await AsyncStorage.setItem(SYNC_SETTINGS_KEYS.SYNC_INTERVAL, syncInterval.toString());
      await AsyncStorage.setItem(SYNC_SETTINGS_KEYS.SYNC_ON_APP_START, syncOnAppStart.toString());
      await AsyncStorage.setItem(SYNC_SETTINGS_KEYS.SYNC_ONLY_ON_WIFI, syncOnlyOnWifi.toString());
      await AsyncStorage.setItem(SYNC_SETTINGS_KEYS.SYNC_BACKGROUND, syncInBackground.toString());
      
      // Mettre à jour la configuration du service de synchronisation
      SyncService.updateConfig({
        autoSync,
        syncInterval: syncInterval * 60 * 1000, // Convertir en millisecondes
        syncOnAppStart,
        syncOnlyOnWifi,
        syncInBackground,
      });
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres:', error);
    }
  };

  // Réinitialiser les données locales
  const handleResetData = async () => {
    try {
      Alert.alert(
        'Réinitialiser les données',
        'Êtes-vous sûr de vouloir supprimer toutes les données locales et les resynchroniser depuis le serveur ? Cette action est irréversible.',
        [
          { text: 'Annuler', style: 'cancel' },
          {
            text: 'Réinitialiser',
            style: 'destructive',
            onPress: async () => {
              try {
                await SyncService.resetAndSync();
                Alert.alert(
                  'Données réinitialisées',
                  'Toutes les données ont été réinitialisées et sont en cours de resynchronisation.',
                  [{ text: 'OK' }]
                );
              } catch (error) {
                Alert.alert(
                  'Erreur',
                  `Impossible de réinitialiser les données: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
                  [{ text: 'OK' }]
                );
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('Erreur lors de la réinitialisation des données:', error);
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color={theme.primary} />
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={[styles.header, { borderBottomColor: theme.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-left" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.text }]}>
          Paramètres de synchronisation
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {/* Statut de synchronisation */}
        <View style={[styles.statusCard, { backgroundColor: theme.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.primary }]}>
            État de la synchronisation
          </Text>
          
          <SyncStatusIndicator size="large" />
          
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.text }]}>
                {formatLastSyncTime()}
              </Text>
              <Text style={[styles.statLabel, { color: theme.textSecondary }]}>
                Dernière synchronisation
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.text }]}>
                {pendingOperationsCount}
              </Text>
              <Text style={[styles.statLabel, { color: theme.textSecondary }]}>
                Opérations en attente
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.text }]}>
                {pendingConflicts.length}
              </Text>
              <Text style={[styles.statLabel, { color: theme.textSecondary }]}>
                Conflits
              </Text>
            </View>
          </View>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.primary }]}
              onPress={triggerSync}
              disabled={isSyncing}
            >
              <Icon name="sync" size={18} color="#FFF" />
              <Text style={styles.actionButtonText}>
                Synchroniser maintenant
              </Text>
            </TouchableOpacity>
            
            {pendingConflicts.length > 0 && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.warning }]}
                onPress={() => navigation.navigate('ConflictResolution')}
              >
                <Icon name="alert-circle" size={18} color="#FFF" />
                <Text style={styles.actionButtonText}>
                  Résoudre les conflits
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Paramètres de synchronisation */}
        <View style={[styles.card, { backgroundColor: theme.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.primary }]}>
            Options de synchronisation
          </Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingTextContainer}>
              <Text style={[styles.settingLabel, { color: theme.text }]}>
                Synchronisation automatique
              </Text>
              <Text style={[styles.settingDescription, { color: theme.textSecondary }]}>
                Synchroniser automatiquement les données
              </Text>
            </View>
            <Switch
              value={autoSync}
              onValueChange={setAutoSync}
              trackColor={{ false: theme.border, true: theme.primary }}
              thumbColor="#FFF"
            />
          </View>
          
          {autoSync && (
            <View style={styles.settingRow}>
              <View style={styles.settingTextContainer}>
                <Text style={[styles.settingLabel, { color: theme.text }]}>
                  Intervalle de synchronisation
                </Text>
                <Text style={[styles.settingDescription, { color: theme.textSecondary }]}>
                  {SYNC_INTERVALS.find(i => i.value === syncInterval)?.label || 'Non défini'}
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  Alert.alert(
                    'Intervalle de synchronisation',
                    'Choisissez un intervalle',
                    [
                      ...SYNC_INTERVALS.map(interval => ({
                        text: interval.label,
                        onPress: () => setSyncInterval(interval.value),
                      })),
                      { text: 'Annuler', style: 'cancel' },
                    ]
                  );
                }}
              >
                <Icon name="chevron-right" size={24} color={theme.text} />
              </TouchableOpacity>
            </View>
          )}
          
          <View style={styles.settingRow}>
            <View style={styles.settingTextContainer}>
              <Text style={[styles.settingLabel, { color: theme.text }]}>
                Synchroniser au démarrage
              </Text>
              <Text style={[styles.settingDescription, { color: theme.textSecondary }]}>
                Synchroniser les données au lancement de l'application
              </Text>
            </View>
            <Switch
              value={syncOnAppStart}
              onValueChange={setSyncOnAppStart}
              trackColor={{ false: theme.border, true: theme.primary }}
              thumbColor="#FFF"
            />
          </View>
          
          <View style={styles.settingRow}>
            <View style={styles.settingTextContainer}>
              <Text style={[styles.settingLabel, { color: theme.text }]}>
                Synchroniser uniquement en Wi-Fi
              </Text>
              <Text style={[styles.settingDescription, { color: theme.textSecondary }]}>
                Ne pas synchroniser sur les données mobiles
              </Text>
            </View>
            <Switch
              value={syncOnlyOnWifi}
              onValueChange={setSyncOnlyOnWifi}
              trackColor={{ false: theme.border, true: theme.primary }}
              thumbColor="#FFF"
            />
          </View>
          
          <View style={styles.settingRow}>
            <View style={styles.settingTextContainer}>
              <Text style={[styles.settingLabel, { color: theme.text }]}>
                Synchronisation en arrière-plan
              </Text>
              <Text style={[styles.settingDescription, { color: theme.textSecondary }]}>
                Synchroniser même lorsque l'application est en arrière-plan
              </Text>
            </View>
            <Switch
              value={syncInBackground}
              onValueChange={setSyncInBackground}
              trackColor={{ false: theme.border, true: theme.primary }}
              thumbColor="#FFF"
            />
          </View>
        </View>

        {/* Actions avancées */}
        <View style={[styles.card, { backgroundColor: theme.card }]}>
          <Text style={[styles.sectionTitle, { color: theme.primary }]}>
            Actions avancées
          </Text>
          
          <TouchableOpacity
            style={[styles.advancedAction, { borderColor: theme.border }]}
            onPress={handleResetData}
          >
            <Icon name="database-remove" size={22} color={theme.error} style={styles.advancedActionIcon} />
            <View style={styles.advancedActionTextContainer}>
              <Text style={[styles.advancedActionTitle, { color: theme.error }]}>
                Réinitialiser les données locales
              </Text>
              <Text style={[styles.advancedActionDescription, { color: theme.secondaryText }]}>
                Supprime toutes les données et réinitialise la synchronisation
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color={theme.border} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.advancedAction, { borderColor: theme.border }]}
            onPress={() => navigation.navigate('BackupScreen')}
          >
            <Icon name="backup-restore" size={24} color={theme.text} style={styles.advancedActionIcon} />
            <View style={styles.advancedActionTextContainer}>
              <Text style={[styles.advancedActionTitle, { color: theme.text }]}>
                Sauvegarde et restauration
              </Text>
              <Text style={[styles.advancedActionDescription, { color: theme.secondaryText }]}>
                Gérer les sauvegardes de la base de données
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color={theme.border} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.advancedAction, { borderColor: theme.border }]}
            onPress={() => navigation.navigate('ConflictResolution')}
          >
            <Icon name="alert-circle-outline" size={24} color={theme.text} style={styles.advancedActionIcon} />
            <View style={styles.advancedActionTextContainer}>
              <Text style={[styles.advancedActionTitle, { color: theme.text }]}>
                Gestionnaire de conflits
              </Text>
              <Text style={[styles.advancedActionDescription, { color: theme.secondaryText }]}>
                Afficher et gérer les conflits de données
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color={theme.border} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.advancedAction, { borderColor: theme.border }]}
            onPress={() => navigation.navigate('SyncQueueView')}
          >
            <Icon name="format-list-checks" size={24} color={theme.primary} style={styles.advancedActionIcon} />
            <View style={styles.advancedActionTextContainer}>
              <Text style={[styles.advancedActionTitle, { color: theme.text }]}>
                File d'attente de synchronisation
              </Text>
              <Text style={[styles.advancedActionDescription, { color: theme.secondaryText }]}>
                Voir les opérations en attente de synchronisation
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color={theme.border} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  statusCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  card: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 4,
    marginRight: 8,
    flex: 1,
  },
  actionButtonText: {
    color: '#FFF',
    fontWeight: '500',
    marginLeft: 6,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  settingTextContainer: {
    flex: 1,
  },
  settingLabel: {
    fontSize: 15,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 12,
    marginTop: 2,
  },
  advancedAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  advancedActionIcon: {
    marginRight: 12,
  },
  advancedActionTextContainer: {
    flex: 1,
    marginHorizontal: 12,
  },
  advancedActionTitle: {
    fontSize: 15,
    fontWeight: '500',
  },
  advancedActionDescription: {
    fontSize: 12,
    marginTop: 2,
  },
});

export default SyncSettingsScreen; 