import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  StyleSheet, 
  Alert, 
  SafeAreaView
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import NetInfo from '@react-native-community/netinfo';
import { useSynchronization } from '../hooks/useSynchronization';
import SyncStatusIndicator from '../components/ui/SyncStatusIndicator';

// Type pour les props de navigation
type SettingsScreenProps = {
  navigation: any;
};

const SettingsScreen: React.FC<SettingsScreenProps> = ({ navigation }) => {
  // États
  const [isOnline, setIsOnline] = useState(true);
  
  // Utiliser le hook de synchronisation
  const {
    isSyncing,
    lastSyncTimestamp,
    formatLastSyncTime,
    pendingConflicts,
    pendingOperationsCount,
    triggerSync,
    resetDatabase
  } = useSynchronization();
  
  // Vérifier la connectivité
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(!!state.isConnected);
    });

    return () => unsubscribe();
  }, []);

  // Fonction de synchronisation
  const syncAll = async () => {
    if (isSyncing || !isOnline) return;
    
    try {
      await triggerSync();
      Alert.alert('Synchronisation', 'Synchronisation terminée avec succès');
    } catch (error) {
      Alert.alert('Erreur', 'La synchronisation a échoué');
    }
  };

  // Réinitialiser et synchroniser
  const handleResetAndSync = async () => {
    if (isSyncing || !isOnline) return;

    try {
      // Confirmation avant réinitialisation
      Alert.alert(
        'Réinitialiser les données',
        'Êtes-vous sûr de vouloir réinitialiser toutes les données locales ? Cette action est irréversible.',
        [
          { text: 'Annuler', style: 'cancel' },
          { 
            text: 'Réinitialiser', 
            style: 'destructive',
            onPress: async () => {
              // Réinitialiser la base de données
              const success = await resetDatabase();
              
              if (success) {
                // Synchroniser à nouveau
                await triggerSync();
                Alert.alert('Réinitialisation', 'Les données ont été réinitialisées et synchronisées');
              } else {
                Alert.alert('Erreur', 'La réinitialisation a échoué');
              }
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Une erreur est survenue');
    }
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Synchronisation</Text>
          <View style={styles.card}>
            {/* Utiliser notre composant SyncStatusIndicator */}
            <View style={styles.syncStatusContainer}>
              <SyncStatusIndicator 
                onPress={syncAll} 
                showText={true} 
                size="large"
              />
            </View>
            
            {pendingConflicts.length > 0 && (
              <TouchableOpacity 
                style={styles.settingItem}
                onPress={() => navigation.navigate('ConflictResolution')}
              >
                <View style={styles.settingContent}>
                  <Icon name="sync-alert" size={24} color="#F7C137" style={styles.icon} />
                  <View>
                    <Text style={styles.settingTitle}>Résoudre les conflits</Text>
                    <Text style={styles.settingDescription}>
                      {pendingConflicts.length} conflit(s) en attente de résolution
                    </Text>
                  </View>
                </View>
                <Icon name="chevron-right" size={20} color="#999" />
              </TouchableOpacity>
            )}
            
            <TouchableOpacity 
              style={styles.settingItem}
              onPress={syncAll}
              disabled={isSyncing || !isOnline}
            >
              <View style={styles.settingContent}>
                <Icon name="sync" size={24} color="#5469D4" style={styles.icon} />
                <View>
                  <Text style={[styles.settingTitle, (!isOnline || isSyncing) && { color: '#999' }]}>
                    Synchroniser maintenant
                  </Text>
                  <Text style={styles.settingDescription}>
                    Mettre à jour les données avec le serveur
                    {pendingOperationsCount > 0 && ` (${pendingOperationsCount} opération(s) en attente)`}
                  </Text>
                </View>
              </View>
              <Icon name="chevron-right" size={20} color="#999" />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.settingItem}
              onPress={handleResetAndSync}
              disabled={isSyncing || !isOnline}
            >
              <View style={styles.settingContent}>
                <Icon name="restart" size={24} color="#ED5F74" style={styles.icon} />
                <View>
                  <Text style={[styles.settingTitle, (!isOnline || isSyncing) && { color: '#999' }]}>
                    Réinitialiser et synchroniser
                  </Text>
                  <Text style={styles.settingDescription}>
                    Effacer les données locales et tout synchroniser
                  </Text>
                </View>
              </View>
              <Icon name="chevron-right" size={20} color="#999" />
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Autres sections */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sauvegardes</Text>
          <View style={styles.card}>
            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => navigation.navigate('BackupScreen')}
            >
              <View style={styles.settingContent}>
                <Icon name="backup-restore" size={24} color="#5469D4" style={styles.icon} />
                <View>
                  <Text style={styles.settingTitle}>Gérer les sauvegardes</Text>
                  <Text style={styles.settingDescription}>
                    Créer ou restaurer des sauvegardes de vos données
                  </Text>
                </View>
              </View>
              <Icon name="chevron-right" size={20} color="#999" />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F8FA',
  },
  section: {
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginVertical: 12,
  },
  card: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  syncStatusContainer: {
    paddingVertical: 8,
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  icon: {
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  }
});

export default SettingsScreen; 