import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { ActivityIndicator, View, Text, StyleSheet } from 'react-native';
import { store } from './store';
import RootNavigator from './navigation/RootNavigator';
import { ThemeProvider } from './theme';
import { database } from './database';
import { setupMocks, teardownMocks } from './mocks';
import { generateTestData, hasData } from './utils/generateTestData';
import { adaptDatabaseForSync } from './utils/databaseAdapter';
import { periodicSyncService } from './services/periodicSync.service';

// Initialiser les mocks si nécessaire
if (__DEV__) {
  setupMocks();
}

// Composant de chargement simple
const LoadingScreen = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#0066cc" />
    <Text style={styles.loadingText}>Chargement de vos données...</Text>
  </View>
);

const App: React.FC = () => {
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  // Charger ou générer des données de test en mode développement
  useEffect(() => {
    const prepareData = async () => {
      if (__DEV__) {
        // Vérifier si des données existent déjà
        const hasExistingData = await hasData();

        if (!hasExistingData) {
          console.log('Aucune donnée existante, génération de données de test...');
          await generateTestData();
        } else {
          console.log('Données existantes trouvées.');
        }
      }

      setIsDataLoaded(true);
    };

    prepareData();
  }, []);

  useEffect(() => {
    // Ne synchroniser que lorsque les données sont chargées
    if (!isDataLoaded) return;

    // Démarrer la synchronisation périodique
    periodicSyncService.startPeriodicSync();

    // Effectuer une synchronisation initiale
    periodicSyncService.performManualSync().catch((error: Error) => {
      console.error('Initial sync failed:', error);
    });

    return () => {
      // Arrêter la synchronisation périodique
      periodicSyncService.stopPeriodicSync();

      // Nettoyer les mocks si nécessaire
      if (__DEV__) {
        teardownMocks();
      }
    };
  }, [isDataLoaded])

  // Afficher un chargement si les données ne sont pas encore prêtes
  if (!isDataLoaded) {
    return (
      <SafeAreaProvider>
        <ThemeProvider>
          <LoadingScreen />
        </ThemeProvider>
      </SafeAreaProvider>
    );
  }

  return (
    <ThemeProvider>
      <Provider store={store}>
        <SafeAreaProvider>
          <NavigationContainer>
            <RootNavigator />
          </NavigationContainer>
        </SafeAreaProvider>
      </Provider>
    </ThemeProvider>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
    color: '#333',
  },
});

export default App;
