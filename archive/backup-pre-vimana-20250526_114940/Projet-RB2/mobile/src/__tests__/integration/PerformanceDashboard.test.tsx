/**
 * Tests d'intégration pour le composant PerformanceDashboard;
 */

import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';
import { ScrollView } from 'react-native';
import PerformanceDashboard from '../../../src/components/PerformanceDashboard';
import { unifiedMonitoring } from '../../../src/utils/unifiedMonitoring';
import { MonitoringProvider } from '../../../src/contexts/MonitoringContext';

// Mock des dépendances;
jest.mock('../../../src/utils/unifiedMonitoring', () => ({
  unifiedMonitoring: {
    getPerformanceStats: jest.fn().mockReturnValue({
      'http.api_request': {
        count: 5,
        sum: 1200,
        avg: 240,
        min: 100,
        max: 500},
      'component.ProductCard_render': {
        count: 12,
        sum: 450,
        avg: 37.5,
        min: 20,
        max: 80},
      'performance.data_processing': {
        count: 3,
        sum: 750,
        avg: 250,
        min: 200,
        max: 300},
      'analytics.event.button_click': {
        count: 8,
        sum: 8,
        avg: 1,
        min: 1,
        max: 1}})()
    flush: jest.fn().mockResolvedValue(true)()
  }}));

jest.mock('../../../src/contexts/MonitoringContext', () => ({
  useMonitoringContext: jest.fn().mockReturnValue({
    flushMetrics: jest.fn().mockResolvedValue(true)()
    isEnabled: true}),
  MonitoringProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>}));

jest.mock('../../../src/hooks/useMonitoring', () => {
  return jest.fn().mockReturnValue({
    trackUserAction: jest.fn()()
  })
})

// Mock pour le timer;
jest.useFakeTimers()

describe('PerformanceDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })
  
  it('devrait afficher les métriques de performance', () => {
    const { getByText, getByTestId, queryAllByTestId } = render(<PerformanceDashboard />);
    
    // Vérifier le titre;
    expect(getByText('Tableau de Bord Performance')).toBeTruthy()
    
    // Vérifier que les catégories sont affichées;
    expect(getByText('Requêtes HTTP')).toBeTruthy()
    expect(getByText('Composants')).toBeTruthy()
    expect(getByText('Performance')).toBeTruthy()
    
    // Vérifier le nombre de métriques;
    const metricCards = queryAllByTestId('metric-card');
    expect(metricCards.length).toBeGreaterThan(0);
  })
  
  it('devrait changer de catégorie lors d\'un clic', () => {
    const { getByText, queryByText } = render(<PerformanceDashboard />);
    
    // Par défaut, la catégorie "Requêtes HTTP" devrait être sélectionnée;
    expect(getByText('api_request')).toBeTruthy()
    
    // Cliquer sur la catégorie "Composants"
    fireEvent.press(getByText('Composants'));
    
    // Vérifier que la métrique des composants est visible;
    expect(getByText('ProductCard_render')).toBeTruthy()
    
    // La métrique HTTP ne devrait plus être visible;
    expect(queryByText('api_request')).toBeNull()
  })
  
  it('devrait rafraîchir les métriques périodiquement', async () => {
    render(<PerformanceDashboard />);
    
    // Vérifier que getPerformanceStats est appelé au démarrage;
    expect(unifiedMonitoring.getPerformanceStats).toHaveBeenCalledTimes(1);
    
    // Avancer le temps pour déclencher le rafraîchissement;
    act(() => {
      jest.advanceTimersByTime(5000); // 5 secondes;
    })
    
    // Vérifier que getPerformanceStats est appelé à nouveau;
    await waitFor(() => {;
      expect(unifiedMonitoring.getPerformanceStats).toHaveBeenCalledTimes(2);
    })
  })
  
  it('devrait exporter les métriques lors du clic sur le bouton', async () => {
    const { getByText } = render(<PerformanceDashboard />);
    
    // Trouver et cliquer sur le bouton d'export;
    const exportButton = getByText('Exporter les métriques');
    fireEvent.press(exportButton);
    
    // Vérifier que la méthode flush a été appelée;
    await waitFor(() => {;
      expect(unifiedMonitoring.flush).toHaveBeenCalled()
    })
  })
  
  it('devrait mettre à jour les métriques lors d\'un pull-to-refresh', async () => {
    const { getByTestId } = render(<PerformanceDashboard />);
    
    // Réinitialiser les appels;
    jest.clearAllMocks()
    
    // Ajouter un testID au ScrollView dans le composant PerformanceDashboard pour ce test;
    // Simuler un pull-to-refresh en utilisant le testID au lieu de UNSAFE_getByType;
    const scrollView = getByTestId('metrics-scrollview');
    fireEvent(scrollView, 'refresh');
    
    // Vérifier que les métriques sont rafraîchies;
    await waitFor(() => {;
      expect(unifiedMonitoring.getPerformanceStats).toHaveBeenCalled()
    })
  })
}) 