events {
    worker_connections 1024;
}

http {
    # Configuration SSL
    ssl_protocols TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers EECDH+AESGCM:EDH+AESGCM;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Configuration de base
    server_tokens off;
    client_max_body_size 10M;

    # En-têtes de sécurité
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;

    upstream security_backend {
        server security-service:7010;
    }

    upstream analyzer_backend {
        server analyzer-service:7008;
    }

    upstream agent_ia_backend {
        server agent-ia:5678;
    }

    upstream main_backend {
        server backend-service:3000;
    }

    upstream retreat_pro_matcher_backend {
        server retreat-pro-matcher:3001;
    }

    server {
        listen 8080 ssl;
        ssl_certificate /etc/nginx/certs/server.crt;
        ssl_certificate_key /etc/nginx/certs/server.key;

        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
        limit_req zone=api_limit burst=20 nodelay;

        # Validation JWT
        auth_jwt on;
        auth_jwt_key_file /etc/nginx/certs/jwt_pubkey.pem;

        # Routes sécurisées
        location /api/security/ {
            proxy_pass http://security_backend/;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/analyzer/ {
            proxy_pass http://analyzer_backend/;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/ai/ {
            proxy_pass http://agent_ia_backend/;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/matcher/ {
            proxy_pass http://retreat_pro_matcher_backend/api/;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/ {
            proxy_pass http://main_backend/;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}