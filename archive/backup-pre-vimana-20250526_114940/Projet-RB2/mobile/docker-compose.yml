version: '3.8'

services:
  # Services accessibles directement depuis le mobile
  social-service:
    image: retreat-social:latest
    ports:
      - "4000:4000"
    networks:
      - mobile-network
      - internal-network

  messaging-service:
    image: retreat-messaging:latest
    ports:
      - "5178:5178"
    networks:
      - mobile-network
      - internal-network

  # API Gateway sécurisée pour les services critiques
  mobile-gateway:
    image: nginx:alpine
    ports:
      - "8080:8080"
    volumes:
      - ./gateway/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./gateway/certs:/etc/nginx/certs:ro
    networks:
      - mobile-network
      - secure-network
    depends_on:
      - security-service
      - analyzer-service
      - agent-ia
      - backend-service

  # Services critiques (réseau isolé)
  security-service:
    image: retreat-security:latest
    expose:
      - "7010"
    networks:
      - secure-network
    deploy:
      placement:
        constraints:
          - node.role == worker
          - node.labels.security-zone == high

  analyzer-service:
    image: retreat-analyzer:latest
    expose:
      - "7008"
    networks:
      - secure-network
    deploy:
      placement:
        constraints:
          - node.role == worker
          - node.labels.security-zone == high

  agent-ia:
    image: retreat-agent-ia:latest
    expose:
      - "5678"
    networks:
      - secure-network
    deploy:
      placement:
        constraints:
          - node.role == worker
          - node.labels.security-zone == high

  backend-service:
    image: retreat-backend:latest
    expose:
      - "3000"
    networks:
      - secure-network
    deploy:
      placement:
        constraints:
          - node.role == worker
          - node.labels.security-zone == high

networks:
  mobile-network:
    driver: bridge
  secure-network:
    driver: overlay
    internal: true
    driver_opts:
      encrypted: "true"
  internal-network:
    driver: bridge