{"compilerOptions": {"target": "es2018", "module": "commonjs", "lib": ["es2018", "dom"], "jsx": "react-native", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "baseUrl": ".", "paths": {"*": ["src/*"]}, "types": ["jest", "node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}