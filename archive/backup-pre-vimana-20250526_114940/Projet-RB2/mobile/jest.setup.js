/**
 * Configuration de l'environnement de test Jest
 * Mocks globaux et configuration pour les tests universels
 */

// Détecter la plate-forme courante 
const platform = process.env.PLATFORM || 'native';
global.__PLATFORM__ = platform;

// Mock pour react-native
jest.mock('react-native', () => {
  // Importer la version réelle de RN pour les tests natifs
  if (platform === 'native') {
    return jest.requireActual('react-native');
  }
  
  // Simuler RN pour les tests web
  return {
    Platform: {
      OS: 'web',
      select: (obj) => obj.web || obj.default,
    },
    Dimensions: {
      get: jest.fn().mockReturnValue({ width: 360, height: 640 }),
    },
    StyleSheet: {
      create: (styles) => styles,
      flatten: (style) => style,
    },
    Alert: {
      alert: jest.fn(),
    },
    NativeModules: {},
    Animated: {
      Value: jest.fn(() => ({
        interpolate: jest.fn(),
        setValue: jest.fn(),
      })),
      View: 'View',
      Text: 'Text',
      createAnimatedComponent: (comp) => comp,
    },
  };
});

// Mock pour les modules natifs
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  getAllKeys: jest.fn(),
  multiGet: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
}));

jest.mock('@react-native-community/netinfo', () => ({
  addEventListener: jest.fn(() => jest.fn()),
  fetch: jest.fn().mockResolvedValue({
    isConnected: true,
    isInternetReachable: true,
    type: 'wifi',
  }),
}));

// MMKV mock
jest.mock('react-native-mmkv', () => ({
  MMKV: class {
    constructor() {}
    set = jest.fn();
    getString = jest.fn();
    getBoolean = jest.fn();
    getNumber = jest.fn();
    getObject = jest.fn();
    delete = jest.fn();
    getAllKeys = jest.fn().mockReturnValue([]);
    contains = jest.fn();
    clearAll = jest.fn();
  },
  useMMKVString: jest.fn().mockReturnValue(['', jest.fn()]),
  useMMKVObject: jest.fn().mockReturnValue([null, jest.fn()]),
  useMMKVNumber: jest.fn().mockReturnValue([0, jest.fn()]),
  useMMKVBoolean: jest.fn().mockReturnValue([false, jest.fn()]),
}));

// Mock pour VectorIcons
jest.mock('react-native-vector-icons/Ionicons', () => 'Icon');

// Mock navigation
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    setOptions: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
}));

// Créez un mock pour le module Config
jest.mock('./src/config/environment', () => ({
  NODE_ENV: 'test',
  API_URL: 'https://api.test.com',
  API_MONITORING_ENABLED: true,
  API_MONITORING_SAMPLE_RATE: 1.0, // 100% en test
  API_VERSION: 'v1',
  APP_VERSION: '1.0.0-test',
}));

// Mock pour fetch
global.fetch = jest.fn().mockImplementation(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    status: 200,
    headers: {
      get: jest.fn(),
    },
  })
);

// Supprimer les avertissements de dépreciation de React
jest.spyOn(console, 'error').mockImplementation((...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: ') || args[0].includes('React.createFactory()'))
  ) {
    return;
  }
  console.error(...args);
});

// Créer une fonction de timeout plus performante
global.setTimeout = jest.fn((cb, ms) => {
  if (typeof cb === 'function') cb();
  return null;
});

// Configuration pour jest-axe (accessibilité)
if (typeof window !== 'undefined') {
  window.getComputedStyle = jest.fn(() => ({
    getPropertyValue: jest.fn(() => ''),
  }));
  
  // Crée un élément DOM vide pour les tests d'accessibilité
  if (!document.body) {
    const div = document.createElement('div');
    div.id = 'root';
    document.body = document.createElement('body');
    document.body.appendChild(div);
  }
}

// Configuration globale pour les matchers de tests
expect.extend({
  /**
   * Vérifie si une fonction a été appelée avec des arguments correspondant à une structure partielle
   * Utilisé pour vérifier des objets complexes où certaines propriétés peuvent varier
   */
  toHaveBeenCalledWithPartialMatch(received, expectedPartialArg, argIndex = 0) {
    const calls = received.mock.calls;
    
    if (calls.length === 0) {
      return {
        pass: false,
        message: () => `La fonction n'a jamais été appelée`,
      };
    }
    
    // Vérifier chaque appel
    for (const call of calls) {
      if (call.length <= argIndex) continue;
      
      const actualArg = call[argIndex];
      // Vérifie si actualArg est un objet qui contient toutes les propriétés de expectedPartialArg
      if (
        actualArg &&
        typeof actualArg === 'object' &&
        Object.keys(expectedPartialArg).every(
          (key) => 
            key in actualArg && 
            JSON.stringify(actualArg[key]) === JSON.stringify(expectedPartialArg[key])
        )
      ) {
        return {
          pass: true,
          message: () => `La fonction a été appelée avec un argument correspondant à ${JSON.stringify(expectedPartialArg)}`,
        };
      }
    }
    
    return {
      pass: false,
      message: () => `La fonction n'a pas été appelée avec un argument correspondant à ${JSON.stringify(expectedPartialArg)}`,
    };
  },
}); 