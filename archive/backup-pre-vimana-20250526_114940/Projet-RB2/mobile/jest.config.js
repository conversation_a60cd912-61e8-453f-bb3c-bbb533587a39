/**
 * Configuration universelle Jest pour les tests React Native & Web
 * Compatible avec iOS, Android et Web
 */

module.exports = {
  preset: 'react-native',
  
  // Transformations pour TypeScript et JSX
  transform: {
    '^.+\\.(ts|tsx|js|jsx)$': 'ts-jest',
  },
  
  // Patterns de fichiers pour les tests
  testMatch: [
    '**/__tests__/**/*.test.(ts|tsx|js|jsx)',
    '**/*.test.(ts|tsx|js|jsx)',
  ],
  
  // Configuration de coverage
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**'
  ],
  coverageDirectory: 'coverage',
  
  // Modules à ignorer dans la couverture
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/android/',
    '/ios/',
    '/__tests__/',
    '/__mocks__/',
  ],
  
  // Modules à mocker pour les tests
  moduleNameMapper: {
    // Mappages pour les fichiers de styles (CSS, images, etc.)
    '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/__mocks__/fileMock.js',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  
  // Setup files pour configurer l'environnement de test
  setupFiles: [
    '<rootDir>/src/utils/__tests__/setup.ts'
  ],
  
  // Extensions à résoudre automatiquement
  moduleFileExtensions: [
    'ts',
    'tsx',
    'js',
    'jsx',
    'json',
    'node',
  ],
  
  // Extension setup pour @testing-library/jest-native
  setupFilesAfterEnv: [
    '@testing-library/jest-native/extend-expect',
  ],
  
  // Transformations spécifiques pour certains modules
  transformIgnorePatterns: [
    'node_modules/(?!(jest-)?react-native|@react-native|@react-native-community|@react-navigation)'
  ],
  
  // Environnement par défaut (jsdom pour le web)
  testEnvironment: 'node',
  
  // Configuration spécifique pour les tests natifs vs web
  projects: [
    {
      // Configuration pour les tests React Native (iOS & Android)
      displayName: 'mobile',
      preset: 'react-native',
      testEnvironment: 'node',
      testMatch: [
        '<rootDir>/**/*.test.(ts|tsx)',
        '<rootDir>/**/__tests__/**/*.(ts|tsx)',
      ],
    },
    {
      // Configuration pour les tests Web
      displayName: 'web',
      testEnvironment: 'jsdom',
      testMatch: [
        '<rootDir>/**/*.web.test.(ts|tsx|js|jsx)',
        '<rootDir>/**/__tests__/**/*.web.test.(ts|tsx|js|jsx)',
      ],
    },
  ],
  
  // Rapport de test détaillé
  verbose: true,
  
  // Configuration spécifique pour les tests natifs vs web
  testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.[jt]sx?$',
  
  // Configuration spécifique pour les reporters de coverage
  coverageReporters: ['text', 'lcov'],
}; 