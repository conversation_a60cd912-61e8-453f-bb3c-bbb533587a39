# Guide de Tests du Système de Monitoring

Ce document décrit comment tester le système de monitoring unifié de l'application React Native.

## Installation

Avant de pouvoir exécuter les tests, assurez-vous d'avoir installé toutes les dépendances :

```bash
npm install --legacy-peer-deps
```

## Organisation des Tests

Les tests sont organisés selon la structure suivante :

```
src/__tests__/
  ├── unit/                    # Tests unitaires
  │   └── unifiedMonitoring.test.ts
  ├── integration/             # Tests d'intégration
  │   ├── useMonitoring.test.tsx
  │   └── PerformanceDashboard.test.tsx
  ├── accessibility/           # Tests d'accessibilité
  │   └── a11y.test.tsx
  ├── performance/             # Tests de performance
  │   └── monitoring-performance.test.ts
  └── e2e/                     # Tests End-to-End
      └── monitoring.e2e.ts
```

## Exécution des Tests

### Tous les tests

Pour exécuter tous les tests avec rapport de couverture :

```bash
npm run test:all
```

### Tests spécifiques

Vous pouvez exécuter des catégories spécifiques de tests :

```bash
# Tests unitaires
npm run test:unit

# Tests d'intégration
npm run test:integration

# Tests d'accessibilité
npm run test:accessibility

# Tests de performance
npm run test:performance

# Tests End-to-End
npm run test:e2e
```

### Mode watch

Pour développer en TDD, utilisez le mode watch :

```bash
npm run test:watch
```

## Types de Tests

### Tests Unitaires

Les tests unitaires vérifient le fonctionnement de chaque module indépendamment. Pour le système de monitoring, ils testent :

- L'initialisation
- La journalisation (logging)
- La capture d'erreurs
- Le suivi d'événements (analytics)
- La mesure de performance
- La récupération de métriques
- L'arrêt propre du système

### Tests d'Intégration

Les tests d'intégration vérifient l'interaction entre les différents modules :

- `useMonitoring` : Teste l'interaction entre les hooks React et le système de monitoring
- `PerformanceDashboard` : Teste l'affichage et l'interaction avec les métriques collectées

### Tests d'Accessibilité

Les tests d'accessibilité s'assurent que les composants liés au monitoring sont utilisables par tous les utilisateurs, y compris ceux ayant des besoins spécifiques. Ils vérifient :

- La conformité aux règles d'accessibilité avec jest-axe
- La présence de labels d'accessibilité
- Le contraste des couleurs
- La navigabilité au clavier

### Tests de Performance

Les tests de performance vérifient que le système de monitoring lui-même n'impacte pas négativement les performances de l'application :

- Temps d'exécution des logs
- Impact du suivi des événements
- Surcoût de la mesure de performance
- Gestion des spans concurrentes
- Utilisation de la mémoire
- Impact sur le rendu des composants

### Tests End-to-End (E2E)

Les tests E2E simulent un scénario complet d'utilisation de l'application pour vérifier que le monitoring fonctionne correctement de bout en bout.

## Configuration de Jest

La configuration de Jest se trouve dans les fichiers :

- `jest.config.js` : Configuration principale
- `jest.setup.js` : Configuration de l'environnement de test
- `__mocks__/fileMock.js` : Mock pour les fichiers statiques

## Pratiques Recommandées

- **Maintenez vos tests à jour** : Lorsque vous modifiez le code de l'application, assurez-vous de mettre à jour les tests correspondants.
- **Visez une couverture élevée** : Essayez de maintenir une couverture de code d'au moins 80%.
- **Identifiez les régressions** : Exécutez la suite de tests complète avant chaque merge pour identifier les régressions.
- **Tests de performance réguliers** : Exécutez les tests de performance régulièrement pour détecter les dégradations de performance.

## Dépannage

### Si les tests échouent

1. Vérifiez que toutes les dépendances sont installées : `npm install --legacy-peer-deps`
2. Nettoyez le cache de Jest : `npx jest --clearCache`
3. Vérifiez les mocks pour s'assurer qu'ils correspondent à l'implémentation actuelle
4. Examinez les journaux d'erreur pour identifier le problème spécifique

### Problèmes connus

- Les tests d'accessibilité peuvent parfois échouer en raison de limitations de la conversion React Native vers DOM.
- Les tests de performance peuvent varier selon l'environnement d'exécution.

## Résultats des Tests

Après l'exécution des tests, un rapport de couverture est généré dans le dossier `coverage/`. Ce rapport fournit des informations détaillées sur la couverture du code par les tests. 