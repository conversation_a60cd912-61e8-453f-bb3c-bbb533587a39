#!/bin/bash

# Script pour générer une version web (PWA) de l'application

echo "Génération d'une version web (PWA) de l'application..."

# Vérifier si react-native-web est installé
if ! grep -q "react-native-web" package.json; then
  echo "Installation de react-native-web et des dépendances nécessaires..."
  npm install --save react-native-web react-dom
  npm install --save-dev webpack webpack-cli webpack-dev-server babel-loader html-webpack-plugin
fi

# Créer un fichier webpack.config.js s'il n'existe pas
if [ ! -f "./webpack.config.js" ]; then
  echo "Création du fichier webpack.config.js..."
  cat > "./webpack.config.js" << EOL
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
  mode: 'production',
  entry: './src/index.web.js',
  output: {
    path: path.resolve(__dirname, 'web-build'),
    filename: 'bundle.[contenthash].js',
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
            plugins: [
              ['@babel/plugin-proposal-decorators', { legacy: true }],
              ['module-resolver', {
                alias: {
                  'react-native': 'react-native-web',
                },
              }],
            ],
          },
        },
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        use: [
          {
            loader: 'file-loader',
            options: {
              name: 'images/[name].[hash].[ext]',
            },
          },
        ],
      },
    ],
  },
  resolve: {
    extensions: ['.web.js', '.web.jsx', '.web.ts', '.web.tsx', '.js', '.jsx', '.ts', '.tsx'],
    alias: {
      'react-native$': 'react-native-web',
    },
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/index.html',
      filename: 'index.html',
    }),
  ],
};
EOL
fi

# Créer un fichier index.web.js s'il n'existe pas
if [ ! -f "./src/index.web.js" ]; then
  echo "Création du fichier index.web.js..."
  cat > "./src/index.web.js" << EOL
import { AppRegistry } from 'react-native';
import App from './App';

// Enregistrer l'application
AppRegistry.registerComponent('RetreatAndBe', () => App);

// Initialiser l'application web
AppRegistry.runApplication('RetreatAndBe', {
  rootTag: document.getElementById('root'),
});
EOL
fi

# Créer un fichier index.html s'il n'existe pas
mkdir -p "./public"
if [ ! -f "./public/index.html" ]; then
  echo "Création du fichier index.html..."
  cat > "./public/index.html" << EOL
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="theme-color" content="#5469D4">
  <title>Retreat & Be</title>
  <style>
    html, body, #root {
      height: 100%;
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }
  </style>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <div id="root"></div>
  <noscript>
    Vous devez activer JavaScript pour exécuter cette application.
  </noscript>
</body>
</html>
EOL
fi

# Créer un fichier manifest.json s'il n'existe pas
if [ ! -f "./public/manifest.json" ]; then
  echo "Création du fichier manifest.json..."
  cat > "./public/manifest.json" << EOL
{
  "short_name": "Retreat & Be",
  "name": "Retreat & Be - Trouvez votre retraite idéale",
  "icons": [
    {
      "src": "icons/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "icons/icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ],
  "start_url": ".",
  "display": "standalone",
  "theme_color": "#5469D4",
  "background_color": "#ffffff"
}
EOL
fi

# Créer un dossier pour les icônes
mkdir -p "./public/icons"

# Ajouter un script de build web dans package.json s'il n'existe pas
if ! grep -q "\"build:web\"" package.json; then
  echo "Ajout du script build:web dans package.json..."
  sed -i.bak 's/"scripts": {/"scripts": {\n    "build:web": "webpack --config webpack.config.js",/g' package.json
  rm package.json.bak
fi

# Générer la version web
echo "Génération de la version web..."
npm run build:web

# Vérifier si la génération a réussi
if [ -d "./web-build" ]; then
  echo "Version web générée avec succès dans le dossier web-build"
  
  # Créer un dossier pour les builds
  BUILDS_DIR="./builds"
  mkdir -p "$BUILDS_DIR"
  
  # Créer une archive de la version web
  VERSION=$(grep -o '"version": "[^"]*' package.json | cut -d'"' -f4)
  DATE=$(date +"%Y%m%d_%H%M%S")
  WEB_ZIP="retreat-and-be-web-v${VERSION}-${DATE}.zip"
  
  echo "Création de l'archive $WEB_ZIP..."
  cd web-build && zip -r "../$BUILDS_DIR/$WEB_ZIP" . && cd ..
  
  echo "Archive web créée: $BUILDS_DIR/$WEB_ZIP"
else
  echo "Erreur lors de la génération de la version web."
  exit 1
fi
