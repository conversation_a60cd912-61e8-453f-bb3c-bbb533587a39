#!/bin/bash

# Script pour générer un keystore pour signer l'application Android

echo "Génération d'un keystore pour la signature de l'application Android..."

# Définir les variables
KEYSTORE_PATH="./android/app/retreat-and-be.keystore"
KEY_ALIAS="retreat-and-be-key"
VALIDITY=10000 # Validité en jours

# Vérifier si le keystore existe déjà
if [ -f "$KEYSTORE_PATH" ]; then
  echo "Le keystore existe déjà à l'emplacement $KEYSTORE_PATH"
  echo "Voulez-vous le remplacer? (y/n)"
  read -r response
  if [ "$response" != "y" ]; then
    echo "Opération annulée."
    exit 0
  fi
fi

# Demander les informations pour le keystore
echo "Veuillez entrer les informations pour le keystore:"
echo "Mot de passe du keystore:"
read -s KEYSTORE_PASSWORD
echo "Mot de passe de la clé (peut être identique au mot de passe du keystore):"
read -s KEY_PASSWORD

# Générer le keystore
keytool -genkeypair \
  -v \
  -keystore "$KEYSTORE_PATH" \
  -alias "$KEY_ALIAS" \
  -keyalg RSA \
  -keysize 2048 \
  -validity "$VALIDITY" \
  -storepass "$KEYSTORE_PASSWORD" \
  -keypass "$KEY_PASSWORD" \
  -dname "CN=Retreat and Be, OU=Mobile, O=Retreat and Be, L=Paris, S=Ile-de-France, C=FR"

# Vérifier si la génération a réussi
if [ $? -eq 0 ]; then
  echo "Keystore généré avec succès à l'emplacement $KEYSTORE_PATH"
  
  # Créer le fichier gradle.properties avec les informations de signature
  GRADLE_PROPERTIES="./android/gradle.properties"
  
  # Ajouter les propriétés de signature au fichier gradle.properties
  echo "" >> "$GRADLE_PROPERTIES"
  echo "# Propriétés de signature pour la version de production" >> "$GRADLE_PROPERTIES"
  echo "MYAPP_RELEASE_STORE_FILE=retreat-and-be.keystore" >> "$GRADLE_PROPERTIES"
  echo "MYAPP_RELEASE_KEY_ALIAS=$KEY_ALIAS" >> "$GRADLE_PROPERTIES"
  echo "MYAPP_RELEASE_STORE_PASSWORD=$KEYSTORE_PASSWORD" >> "$GRADLE_PROPERTIES"
  echo "MYAPP_RELEASE_KEY_PASSWORD=$KEY_PASSWORD" >> "$GRADLE_PROPERTIES"
  
  echo "Informations de signature ajoutées au fichier $GRADLE_PROPERTIES"
  echo ""
  echo "IMPORTANT: Ne partagez pas le keystore ni les mots de passe. Conservez-les en lieu sûr."
else
  echo "Erreur lors de la génération du keystore."
fi
