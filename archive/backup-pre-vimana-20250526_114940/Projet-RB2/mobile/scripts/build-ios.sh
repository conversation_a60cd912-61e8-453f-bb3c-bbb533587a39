#!/bin/bash

# Script pour générer un IPA pour iOS

echo "Génération d'un IPA pour iOS..."

# Vérifier si Xcode est installé
if ! command -v xcodebuild &> /dev/null; then
  echo "Xcode n'est pas installé ou n'est pas accessible depuis la ligne de commande."
  exit 1
fi

# Définir les variables d'environnement
export ENVFILE=.env.production

# Vérifier si le répertoire ios existe
if [ ! -d "./ios" ]; then
  echo "Le répertoire ios n'existe pas."
  exit 1
fi

# Installer les dépendances CocoaPods
echo "Installation des dépendances CocoaPods..."
cd ios && pod install && cd ..

# Définir les variables pour le build
PROJECT_NAME="RetreatAndBe"
SCHEME="RetreatAndBe"
CONFIGURATION="Release"
WORKSPACE="./ios/${PROJECT_NAME}.xcworkspace"
ARCHIVE_PATH="./ios/build/${PROJECT_NAME}.xcarchive"
EXPORT_PATH="./ios/build/Export"
EXPORT_OPTIONS_PLIST="./ios/ExportOptions.plist"

# Créer le fichier ExportOptions.plist s'il n'existe pas
if [ ! -f "$EXPORT_OPTIONS_PLIST" ]; then
  echo "Création du fichier ExportOptions.plist..."
  cat > "$EXPORT_OPTIONS_PLIST" << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>app-store</string>
    <key>teamID</key>
    <string>YOUR_TEAM_ID</string>
    <key>uploadBitcode</key>
    <false/>
    <key>uploadSymbols</key>
    <true/>
    <key>compileBitcode</key>
    <false/>
</dict>
</plist>
EOL

  echo "Fichier ExportOptions.plist créé. Veuillez remplacer YOUR_TEAM_ID par votre identifiant d'équipe Apple."
  echo "Puis relancez ce script."
  exit 1
fi

# Nettoyer les builds précédents
echo "Nettoyage des builds précédents..."
rm -rf "./ios/build"

# Créer l'archive
echo "Création de l'archive Xcode..."
xcodebuild clean archive \
  -workspace "$WORKSPACE" \
  -scheme "$SCHEME" \
  -configuration "$CONFIGURATION" \
  -archivePath "$ARCHIVE_PATH" \
  -allowProvisioningUpdates

# Vérifier si l'archive a été créée
if [ ! -d "$ARCHIVE_PATH" ]; then
  echo "Erreur lors de la création de l'archive."
  exit 1
fi

# Exporter l'IPA
echo "Exportation de l'IPA..."
xcodebuild -exportArchive \
  -archivePath "$ARCHIVE_PATH" \
  -exportOptionsPlist "$EXPORT_OPTIONS_PLIST" \
  -exportPath "$EXPORT_PATH" \
  -allowProvisioningUpdates

# Vérifier si l'exportation a réussi
IPA_PATH=$(find "$EXPORT_PATH" -name "*.ipa" -type f)
if [ -z "$IPA_PATH" ]; then
  echo "Erreur lors de l'exportation de l'IPA."
  exit 1
fi

# Créer un dossier pour les builds
BUILDS_DIR="./builds"
mkdir -p "$BUILDS_DIR"

# Copier l'IPA dans le dossier des builds avec un nom incluant la version et la date
VERSION=$(grep -o '"version": "[^"]*' package.json | cut -d'"' -f4)
DATE=$(date +"%Y%m%d_%H%M%S")
IPA_NAME="retreat-and-be-v${VERSION}-${DATE}.ipa"
cp "$IPA_PATH" "$BUILDS_DIR/$IPA_NAME"

echo "Build iOS terminé avec succès!"
echo "IPA généré: $BUILDS_DIR/$IPA_NAME"
