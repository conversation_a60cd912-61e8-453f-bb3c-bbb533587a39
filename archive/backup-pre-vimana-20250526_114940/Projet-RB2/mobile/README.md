# Système de synchronisation WatermelonDB - Prisma

Ce système permet la synchronisation des données entre la base de données locale WatermelonDB de l'application mobile et le backend Prisma.

## Architecture

Le système de synchronisation est composé des éléments suivants :

- **SyncService** : Service principal gérant la synchronisation entre WatermelonDB et le backend.
- **SyncApiAdapter** : Adaptateur pour communiquer avec l'API Prisma.
- **ConflictResolver** : Utilitaire pour résoudre les conflits de synchronisation.
- **BackupManager** : Système de sauvegarde et restauration de la base de données.
- **Hooks et composants UI** : `useSynchronization`, `SyncStatusIndicator`, etc.

## Fonctionnalités

- ✅ Synchronisation bidirectionnelle avec détection de modifications
- ✅ Stratégies de résolution des conflits (automatique et manuelle)
- ✅ Support pour les relations (one-to-many, many-to-many)
- ✅ Compression des données pour économiser la bande passante
- ✅ Synchronisation en arrière-plan
- ✅ Sauvegarde et restauration de la base de données
- ✅ Interface de gestion des conflits
- ✅ Indicateur de statut de synchronisation
- ✅ Configuration flexible par entité

## Installation des dépendances

Le système de synchronisation nécessite les dépendances suivantes :

```bash
npm install @nozbe/watermelondb @react-native-community/netinfo
npm install @react-native-async-storage/async-storage
npm install react-native-fs react-native-zip-archive
npm install pako buffer
```

## Configuration

La configuration du système de synchronisation se fait dans le fichier `config/index.ts` :

```typescript
export const DEFAULT_SYNC_CONFIG = {
  autoSync: true,
  autoSyncInterval: 15, // minutes
  backgroundSync: true,
  onlyWifi: false,
  compressionEnabled: true,
  maxConcurrentOperations: 3,
  entities: {
    // Configuration par entité...
  }
};
```

## Utilisation

### Initialisation

```typescript
import { syncService } from './services/sync.service';
import database from './database';

// Initialiser le service de synchronisation
syncService.initialize(database);

// Démarrer une synchronisation manuelle
await syncService.sync();
```

### Hook de synchronisation

```typescript
import { useSynchronization } from './hooks/useSynchronization';

function MyComponent() {
  const { 
    status, 
    lastSync, 
    sync, 
    conflicts,
    resolveConflict 
  } = useSynchronization();

  // Utilisation...
}
```

### Gestion des conflits

```typescript
// Résoudre automatiquement un conflit
resolveConflict(conflictId, 'server_wins');

// Résoudre tous les conflits pour une entité
resolveAllConflictsForTable('retreats', 'client_wins');
```

## Modèles de données

Les modèles suivants sont configurés pour la synchronisation :

- **Retreat** : Retraites disponibles
- **Booking** : Réservations des utilisateurs
- **User** : Utilisateurs de l'application
- **Notification** : Notifications système

## Architecture de la synchronisation

```
┌─────────────────┐       ┌───────────────┐       ┌───────────────┐
│                 │       │               │       │               │
│  WatermelonDB   │<─────>│  SyncService  │<─────>│  Prisma API   │
│  Local Database │       │               │       │               │
│                 │       │               │       │               │
└─────────────────┘       └───────┬───────┘       └───────────────┘
                                  │
                          ┌───────┴───────┐
                          │               │
                          │ ConflictStore │
                          │               │
                          └───────────────┘
```

## Événements de synchronisation

Le système émet les événements suivants :

- `sync:started` : Synchronisation démarrée
- `sync:progress` : Progression de la synchronisation
- `sync:complete` : Synchronisation terminée
- `sync:error` : Erreur de synchronisation
- `sync:conflict_detected` : Conflit détecté
- `sync:conflict_resolved` : Conflit résolu
- `sync:connection_change` : Changement d'état de la connexion

## Sauvegarde et restauration

Le système inclut des fonctionnalités de sauvegarde et restauration :

```typescript
import { backupManager } from './utils/backupManager';

// Créer une sauvegarde
await backupManager.createBackup();

// Restaurer une sauvegarde
await backupManager.restoreBackup('backup_file.zip');

// Lister les sauvegardes disponibles
const backups = await backupManager.listBackups();
``` 