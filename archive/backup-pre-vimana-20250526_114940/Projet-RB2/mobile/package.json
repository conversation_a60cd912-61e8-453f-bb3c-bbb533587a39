{"name": "retreat-and-be-mobile", "version": "1.0.0", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "dev": "MOCK_API_RESPONSES=true react-native start --reset-cache", "android:dev": "MOCK_API_RESPONSES=true react-native run-android", "ios:dev": "MOCK_API_RESPONSES=true react-native run-ios", "test": "jest", "test:unit": "jest \"src/__tests__/unit/\"", "test:integration": "jest \"src/__tests__/integration/\"", "test:e2e": "jest \"src/__tests__/e2e/\"", "test:accessibility": "jest src/__tests__/accessibility/a11y.test.tsx --no-coverage", "test:performance": "jest \"src/__tests__/performance/\"", "test:all": "jest --coverage", "test:watch": "jest --watch", "typecheck": "node scripts/tsc-ignore-errors.js", "typecheck:strict": "tsc --noEmit", "build:android": "./scripts/build-android.sh", "build:ios": "./scripts/build-ios.sh", "build:web": "webpack --config webpack.config.js", "build:all": "./scripts/build-all.sh"}, "dependencies": {"@nozbe/watermelondb": "^0.27.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^6.0.0", "@react-navigation/stack": "^6.0.0", "@reduxjs/toolkit": "^2.6.1", "@types/uuid": "^10.0.0", "lru-cache": "^11.0.2", "react": "18.2.0", "react-native": "0.72.0", "react-native-mmkv": "^3.2.0", "react-native-safe-area-context": "^5.3.0", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.25.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react": "^16.2.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^12.9.0", "@types/jest": "^29.5.14", "@types/jest-axe": "^3.5.9", "@types/lru-cache": "^7.10.9", "@types/react": "^19.0.12", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "@types/redux": "^3.6.31", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "jest-expo": "^52.0.6", "ts-jest": "^29.2.6", "typescript": "^5.8.2"}}