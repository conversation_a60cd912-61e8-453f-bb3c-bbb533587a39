import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Divider,
  Chip,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField
} from '@mui/material';
import { PartnerTier } from '../types';
import { partnerTiers } from '../data';
import PromoCodeForm from './PromoCodeForm';

// This component would normally fetch data from the Financial-Management microservice
// For demo purposes, we're simulating the API calls

interface SubscriptionManagerProps {
  partnerId: string;
  currentTier: PartnerTier;
  onTierChange: (tier: PartnerTier) => void;
}

interface SubscriptionDetails {
  id: string;
  tier: PartnerTier;
  status: string;
  startDate: Date;
  endDate?: Date;
  nextBillingDate?: Date;
  autoRenew: boolean;
}

const SubscriptionManager: React.FC<SubscriptionManagerProps> = ({
  partnerId,
  currentTier,
  onTierChange
}) => {
  const [subscription, setSubscription] = useState<SubscriptionDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [changeTierDialogOpen, setChangeTierDialogOpen] = useState(false);
  const [selectedTier, setSelectedTier] = useState<PartnerTier>(currentTier);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvc, setCvc] = useState('');
  const [processingPayment, setProcessingPayment] = useState(false);
  const [appliedPromoCode, setAppliedPromoCode] = useState<any>(null);

  // Simulate fetching subscription details
  useEffect(() => {
    const fetchSubscription = async () => {
      setLoading(true);
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock subscription data
        const mockSubscription: SubscriptionDetails = {
          id: 'sub_123456',
          tier: currentTier,
          status: 'ACTIVE',
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
          nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          autoRenew: true
        };

        setSubscription(mockSubscription);
        setError(null);
      } catch (err) {
        setError('Failed to load subscription details. Please try again later.');
        console.error('Error fetching subscription:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscription();
  }, [currentTier, partnerId]);

  const handleOpenChangeTierDialog = () => {
    setSelectedTier(currentTier);
    setChangeTierDialogOpen(true);
  };

  const handleCloseChangeTierDialog = () => {
    setChangeTierDialogOpen(false);
  };

  const handleTierChange = () => {
    handleCloseChangeTierDialog();

    // If upgrading to a paid tier, show payment dialog
    if (selectedTier !== 'PARTNER' && (currentTier === 'PARTNER' ||
        (currentTier === 'CERTIFIED' && selectedTier === 'PREMIUM'))) {
      setPaymentDialogOpen(true);
    } else {
      // For downgrades or free tier, process immediately
      processSubscriptionChange();
    }
  };

  const handleClosePaymentDialog = () => {
    setPaymentDialogOpen(false);
    setCardNumber('');
    setExpiryDate('');
    setCvc('');
    setProcessingPayment(false);
    setAppliedPromoCode(null);
  };

  const handleProcessPayment = async () => {
    setProcessingPayment(true);

    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Process subscription change after payment
      processSubscriptionChange();

      // Close payment dialog
      handleClosePaymentDialog();
    } catch (err) {
      setError('Payment processing failed. Please try again.');
      setProcessingPayment(false);
    }
  };

  const processSubscriptionChange = async () => {
    setLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update local state
      if (subscription) {
        setSubscription({
          ...subscription,
          tier: selectedTier
        });
      }

      // Notify parent component
      onTierChange(selectedTier);

      setError(null);
    } catch (err) {
      setError('Failed to update subscription. Please try again later.');
      console.error('Error updating subscription:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    setLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update local state
      if (subscription) {
        setSubscription({
          ...subscription,
          status: 'CANCELLED',
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          autoRenew: false
        });
      }

      // Reset to free tier
      onTierChange('PARTNER');

      setError(null);
    } catch (err) {
      setError('Failed to cancel subscription. Please try again later.');
      console.error('Error cancelling subscription:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !subscription) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !subscription) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        Gestion de votre abonnement
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {subscription && (
        <>
          <Box sx={{ mb: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Niveau actuel
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    label={partnerTiers[subscription.tier].name}
                    color={
                      subscription.tier === 'PREMIUM'
                        ? 'success'
                        : subscription.tier === 'CERTIFIED'
                          ? 'primary'
                          : 'default'
                    }
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="body2">
                    {partnerTiers[subscription.tier].price}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Statut
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    label={subscription.status}
                    color={subscription.status === 'ACTIVE' ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Date de début
                </Typography>
                <Typography variant="body2">
                  {subscription.startDate.toLocaleDateString()}
                </Typography>
              </Grid>

              {subscription.status === 'ACTIVE' && subscription.nextBillingDate && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Prochaine facturation
                  </Typography>
                  <Typography variant="body2">
                    {subscription.nextBillingDate.toLocaleDateString()}
                  </Typography>
                </Grid>
              )}

              {subscription.status === 'CANCELLED' && subscription.endDate && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Date de fin
                  </Typography>
                  <Typography variant="body2">
                    {subscription.endDate.toLocaleDateString()}
                  </Typography>
                </Grid>
              )}
            </Grid>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
            {subscription.status === 'ACTIVE' && (
              <>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={handleOpenChangeTierDialog}
                  disabled={loading}
                >
                  Changer de niveau
                </Button>

                <Button
                  variant="outlined"
                  color="error"
                  onClick={handleCancelSubscription}
                  disabled={loading || subscription.tier === 'PARTNER'}
                >
                  {loading ? <CircularProgress size={24} /> : 'Annuler l\'abonnement'}
                </Button>
              </>
            )}

            {subscription.status === 'CANCELLED' && (
              <Button
                variant="contained"
                color="primary"
                onClick={handleOpenChangeTierDialog}
                disabled={loading}
              >
                Réactiver l'abonnement
              </Button>
            )}
          </Box>
        </>
      )}

      {/* Change Tier Dialog */}
      <Dialog open={changeTierDialogOpen} onClose={handleCloseChangeTierDialog}>
        <DialogTitle>Changer de niveau d'abonnement</DialogTitle>
        <DialogContent>
          <Typography variant="body2" paragraph>
            Sélectionnez le niveau d'abonnement que vous souhaitez utiliser :
          </Typography>

          <RadioGroup
            value={selectedTier}
            onChange={(e) => setSelectedTier(e.target.value as PartnerTier)}
          >
            {Object.entries(partnerTiers).map(([key, tier]) => (
              <FormControlLabel
                key={key}
                value={key}
                control={<Radio />}
                label={
                  <Box>
                    <Typography variant="subtitle1">
                      {tier.name} - {tier.price}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {tier.description}
                    </Typography>
                  </Box>
                }
              />
            ))}
          </RadioGroup>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseChangeTierDialog}>Annuler</Button>
          <Button
            onClick={handleTierChange}
            variant="contained"
            color="primary"
            disabled={selectedTier === currentTier}
          >
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Payment Dialog */}
      <Dialog open={paymentDialogOpen} onClose={handleClosePaymentDialog}>
        <DialogTitle>Informations de paiement</DialogTitle>
        <DialogContent>
          <Typography variant="body2" paragraph>
            Veuillez entrer vos informations de carte bancaire pour passer au niveau {partnerTiers[selectedTier].name}.
          </Typography>

          <Box sx={{ mt: 2 }}>
            <TextField
              label="Numéro de carte"
              fullWidth
              margin="normal"
              value={cardNumber}
              onChange={(e) => setCardNumber(e.target.value)}
              placeholder="1234 5678 9012 3456"
              disabled={processingPayment}
            />

            <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
              <TextField
                label="Date d'expiration"
                value={expiryDate}
                onChange={(e) => setExpiryDate(e.target.value)}
                placeholder="MM/AA"
                disabled={processingPayment}
                sx={{ flex: 1 }}
              />

              <TextField
                label="CVC"
                value={cvc}
                onChange={(e) => setCvc(e.target.value)}
                placeholder="123"
                disabled={processingPayment}
                sx={{ flex: 1 }}
              />
            </Box>
          </Box>

          <Divider sx={{ my: 3 }} />

          <PromoCodeForm
            onApply={setAppliedPromoCode}
            onRemove={() => setAppliedPromoCode(null)}
            appliedCode={appliedPromoCode}
            disabled={processingPayment}
          />

          <Typography variant="body2" color="text.secondary" sx={{ mt: 3 }}>
            {appliedPromoCode ? (
              <>
                <Box component="span" sx={{ textDecoration: 'line-through', mr: 1 }}>
                  {partnerTiers[selectedTier].price}
                </Box>
                {appliedPromoCode.discountType === 'PERCENTAGE' ? (
                  `${parseFloat(partnerTiers[selectedTier].price) * (1 - appliedPromoCode.discountValue / 100)}€/mois`
                ) : appliedPromoCode.discountType === 'FIXED_AMOUNT' ? (
                  `${Math.max(0, parseFloat(partnerTiers[selectedTier].price) - appliedPromoCode.discountValue)}€/mois`
                ) : (
                  `${partnerTiers[selectedTier].price} (premier mois gratuit)`
                )}
              </>
            ) : (
              `Vous serez facturé ${partnerTiers[selectedTier].price} immédiatement, puis tous les mois.`
            )}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePaymentDialog} disabled={processingPayment}>
            Annuler
          </Button>
          <Button
            onClick={handleProcessPayment}
            variant="contained"
            color="primary"
            disabled={!cardNumber || !expiryDate || !cvc || processingPayment}
          >
            {processingPayment ? <CircularProgress size={24} /> : 'Payer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default SubscriptionManager;
