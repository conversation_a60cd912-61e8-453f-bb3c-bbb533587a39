#!/usr/bin/env python3
import os
import glob
import sys
from concurrent.futures import ThreadPoolExecutor

def find_backup_files():
    """Recherche tous les fichiers .bak dans le projet"""
    return glob.glob('**/*.bak', recursive=True)

def find_temp_reports():
    """Recherche les rapports temporaires"""
    report_patterns = [
        "ts_fix_report_*.html", 
        "*.log", 
        "*.tmp"
    ]
    
    temp_files = []
    for pattern in report_patterns:
        temp_files.extend(glob.glob(pattern))
    
    return temp_files

def remove_file(file_path):
    """Supprime un fichier et retourne son chemin"""
    try:
        os.remove(file_path)
        return file_path, True, None
    except Exception as e:
        return file_path, False, str(e)

def main():
    backup_files = find_backup_files()
    temp_files = find_temp_reports()
    
    all_files = backup_files + temp_files
    total_files = len(all_files)
    
    if total_files == 0:
        print("Aucun fichier de sauvegarde ou rapport temporaire trouvé.")
        return
    
    print(f"Trouvé {len(backup_files)} fichiers .bak et {len(temp_files)} rapports temporaires.")
    
    if input(f"Voulez-vous supprimer ces {total_files} fichiers ? (o/n): ").lower() != 'o':
        print("Opération annulée.")
        return
    
    # Suppression en parallèle pour plus d'efficacité
    removed = 0
    errors = 0
    
    with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
        results = list(executor.map(remove_file, all_files))
    
    for file_path, success, error in results:
        if success:
            removed += 1
            print(f"✅ Supprimé: {file_path}")
        else:
            errors += 1
            print(f"❌ Erreur lors de la suppression de {file_path}: {error}")
    
    print(f"\nRésumé:")
    print(f"  - {removed} fichiers supprimés")
    print(f"  - {errors} erreurs")

if __name__ == "__main__":
    main() 