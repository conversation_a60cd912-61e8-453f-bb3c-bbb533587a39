/** @type {import('jest').Config} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: [
    '**/tests/**/*monitoring*.test.ts'
  ],
  moduleFileExtensions: [
    'js',
    'json',
    'ts'
  ],
  transform: {
    '^.+\\.(t|j)s$': ['ts-jest', {
      tsconfig: 'tsconfig.json'
    }]
  },
  collectCoverageFrom: [
    '**/src/config/monitoring.ts'
  ],
  coverageDirectory: './coverage/monitoring',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup.ts'
  ],
  globals: {
    'ts-jest': {
      isolatedModules: true
    }
  },
  // Ignore les problèmes de types lors de l'exécution des tests
  // utile pour les tests qui utilisent des mocks de modules externes
  transformIgnorePatterns: [
    'node_modules/(?!(ts-invariant)/)'
  ]
}; 