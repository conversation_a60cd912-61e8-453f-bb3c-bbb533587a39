apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"networking.k8s.io/v1","kind":"Ingress","metadata":{"annotations":{"kubernetes.io/ingress.class":"nginx","nginx.ingress.kubernetes.io/rewrite-target":"/$1","nginx.ingress.kubernetes.io/use-regex":"true"},"name":"rb2-ingress","namespace":"retreat-and-be"},"spec":{"rules":[{"host":"rb2.example.com","http":{"paths":[{"backend":{"service":{"name":"frontend-frontend","port":{"number":3000}}},"path":"/(.*)","pathType":"Prefix"},{"backend":{"service":{"name":"backend-backend","port":{"number":5000}}},"path":"/api/(.*)","pathType":"Prefix"},{"backend":{"service":{"name":"keycloak","port":{"number":8080}}},"path":"/auth/(.*)","pathType":"Prefix"},{"backend":{"service":{"name":"financial-service","port":{"number":80}}},"path":"/financial/(.*)","pathType":"Prefix"},{"backend":{"service":{"name":"security-service","port":{"number":80}}},"path":"/security/(.*)","pathType":"Prefix"},{"backend":{"service":{"name":"messaging-service","port":{"number":80}}},"path":"/messaging/(.*)","pathType":"Prefix"},{"backend":{"service":{"name":"transport-booking","port":{"number":80}}},"path":"/transport/(.*)","pathType":"Prefix"},{"backend":{"service":{"name":"website-creator","port":{"number":80}}},"path":"/website/(.*)","pathType":"Prefix"},{"backend":{"service":{"name":"flight-finder-flight-finder","port":{"number":80}}},"path":"/flights/(.*)","pathType":"Prefix"}]}}]}}
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/use-regex: "true"
  creationTimestamp: "2025-03-19T01:55:33Z"
  generation: 2
  name: rb2-ingress
  namespace: retreat-and-be
  resourceVersion: "61356"
  uid: 35a6715c-1166-4a7b-8bab-f7ebda5fbd33
spec:
  rules:
  - host: rb2.example.com
    http:
      paths:
      - backend:
          service:
            name: frontend-frontend
            port:
              number: 3000
        path: /(.*)
        pathType: Prefix
      - backend:
          service:
            name: backend-backend
            port:
              number: 5000
        path: /api/(.*)
        pathType: Prefix
      - backend:
          service:
            name: keycloak
            port:
              number: 8080
        path: /auth/(.*)
        pathType: Prefix
      - backend:
          service:
            name: financial-service
            port:
              number: 80
        path: /financial/(.*)
        pathType: Prefix
      - backend:
          service:
            name: security-service
            port:
              number: 80
        path: /security/(.*)
        pathType: Prefix
      - backend:
          service:
            name: messaging-service
            port:
              number: 80
        path: /messaging/(.*)
        pathType: Prefix
      - backend:
          service:
            name: transport-booking
            port:
              number: 80
        path: /transport/(.*)
        pathType: Prefix
      - backend:
          service:
            name: website-creator
            port:
              number: 80
        path: /website/(.*)
        pathType: Prefix
      - backend:
          service:
            name: flight-finder-flight-finder
            port:
              number: 80
        path: /flights/(.*)
        pathType: Prefix
status:
  loadBalancer: {}
