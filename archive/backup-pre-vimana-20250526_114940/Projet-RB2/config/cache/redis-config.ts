import { createClient } from 'redis';
import { Logger } from '../utils/logger';

export class CacheConfig {
  private static instance: CacheConfig;
  private client: ReturnType<typeof createClient>;

  private constructor() {
    this.client = createClient({
      url: process.env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            Logger.error('Redis connection failed after 10 retries');
            return new Error('Redis connection failed');
          }
          return Math.min(retries * 100, 3000);
        },
      },
    });

    this.client.on('error', (err) => Logger.error('Redis Client Error', err));
  }

  public static getInstance(): CacheConfig {
    if (!CacheConfig.instance) {
      CacheConfig.instance = new CacheConfig();
    }
    return CacheConfig.instance;
  }

  public async get<T>(key: string): Promise<T | null> {
    const data = await this.client.get(key);
    return data ? JSON.parse(data) : null;
  }

  public async set(key: string, value: any, ttlSeconds?: number): Promise<void> {
    const options = ttlSeconds ? { EX: ttlSeconds } : undefined;
    await this.client.set(key, JSON.stringify(value), options);
  }

  public async invalidate(pattern: string): Promise<void> {
    const keys = await this.client.keys(pattern);
    if (keys.length > 0) {
      await this.client.del(keys);
    }
  }
}