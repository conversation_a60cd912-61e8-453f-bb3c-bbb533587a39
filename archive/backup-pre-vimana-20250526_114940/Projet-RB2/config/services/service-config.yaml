services:
  backend:
    replicas: 3
    resources:
      limits:
        cpu: "1"
        memory: "2Gi"
      requests:
        cpu: "500m"
        memory: "1Gi"
    environment:
      NODE_ENV: production
      LOG_LEVEL: info
      REDIS_URL: ${REDIS_URL}
      KAFKA_BROKERS: ${KAFKA_BROKERS}

  analyzer:
    replicas: 2
    resources:
      limits:
        cpu: "2"
        memory: "4Gi"
      requests:
        cpu: "1"
        memory: "2Gi"
    environment:
      MONGODB_URI: ${MONGODB_URI}
      ANALYSIS_BATCH_SIZE: "100"
      MAX_CONCURRENT_ANALYSIS: "5"

  decentralized-storage:
    replicas: 3
    resources:
      limits:
        cpu: "1"
        memory: "2Gi"
      requests:
        cpu: "500m"
        memory: "1Gi"
    environment:
      IPFS_NODE: ${IPFS_NODE}
      STORAGE_PATH: /data/ipfs
      MAX_FILE_SIZE: "100MB"

networking:
  ingress:
    annotations:
      kubernetes.io/ingress-class: nginx
      cert-manager.io/cluster-issuer: letsencrypt-prod
    tls:
      - secretName: tls-secret
        hosts:
          - api.retreatandbe.com
          - storage.retreatandbe.com