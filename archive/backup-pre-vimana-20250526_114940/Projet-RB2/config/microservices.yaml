shared:
  kafka:
    brokers:
      - kafka-0.kafka-headless:9092
      - kafka-1.kafka-headless:9092
    topics:
      events: retreat-events
      notifications: user-notifications
      analytics: user-analytics

  redis:
    host: redis-master
    port: 6379
    ttl: 3600

  monitoring:
    prometheus:
      enabled: true
      scrapeInterval: 15s
    grafana:
      enabled: true
      dashboards:
        - name: "Service Overview"
        - name: "Performance Metrics"
        - name: "Error Tracking"

services:
  backend:
    scaling:
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilization: 70
    resources:
      requests:
        cpu: 500m
        memory: 512Mi
      limits:
        cpu: 1000m
        memory: 1Gi

  analyzer:
    scaling:
      minReplicas: 1
      maxReplicas: 5
      targetCPUUtilization: 80
    batch:
      size: 100
      interval: 300
    models:
      path: /opt/models
      autoUpdate: true

  decentralized-storage:
    ipfs:
      swarmKey: /key/swarm/psk/1.0.0/
      bootstrap:
        - /ip4/**************/tcp/4001/p2p/QmaCpDMGvV2BGHeYERUEnRQAwe3N8SzbUtfsmvsqQLuvuJ
    storage:
      replication: 3
      pinning:
        enabled: true
        policy: selective