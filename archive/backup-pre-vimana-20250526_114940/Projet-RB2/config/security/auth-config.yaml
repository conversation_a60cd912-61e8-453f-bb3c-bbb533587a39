auth:
  jwt:
    secret: ${JWT_SECRET}
    expiresIn: 24h
    refreshToken:
      expiresIn: 7d
      
  rateLimit:
    window: 15m
    max: 100
    
  cors:
    origins:
      - https://app.retreatandbe.com
      - https://admin.retreatandbe.com
    methods:
      - GET
      - POST
      - PUT
      - DELETE
    
  oauth:
    google:
      clientId: ${GOOGLE_CLIENT_ID}
      clientSecret: ${GOOGLE_CLIENT_SECRET}
      callbackURL: /auth/google/callback
      
    facebook:
      clientId: ${FACEBOOK_CLIENT_ID}
      clientSecret: ${FACEBOOK_CLIENT_SECRET}
      callbackURL: /auth/facebook/callback

  roles:
    - name: ADMIN
      permissions: ['*']
    - name: PARTNER
      permissions: ['read:*', 'write:own', 'manage:content']
    - name: USER
      permissions: ['read:public', 'write:own']