apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: encryption-services-mtls
  namespace: security
spec:
  selector:
    matchLabels:
      app: encryption-services
  mtls:
    mode: STRICT
  portLevelMtls:
    8443:
      mode: STRICT
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: encryption-services-tls
  namespace: security
spec:
  host: encryption-services.security.svc.cluster.local
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 10
    outlierDetection:
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s