[{"id": "alert-4", "timestamp": "2025-03-14T22:42:25.578Z", "type": "failure_rate", "severity": "info", "message": "Taux d'échec pour \"inventory\" (12.8%) dépasse le seuil de 20.0%", "reportType": "inventory", "value": 0.12775308344621206, "threshold": 0.2, "metadata": {"totalReports": 18, "failedReports": 2}, "acknowledged": false}, {"id": "alert-17", "timestamp": "2025-03-14T10:29:05.382Z", "type": "average_duration", "severity": "warning", "message": "Durée moyenne pour \"marketing\" (5142ms) dépasse le seuil de 5000ms", "reportType": "marketing", "value": 5141.599247081836, "threshold": 5000, "metadata": {"totalReports": 47}, "acknowledged": false}, {"id": "alert-11", "timestamp": "2025-03-14T05:39:03.866Z", "type": "consecutive_failures", "severity": "critical", "message": "6 échecs consécutifs ", "value": 6, "threshold": 3, "metadata": {"lastReportId": "report-662", "errorMessage": "Erreur de connexion à la base de données"}, "acknowledged": false}, {"id": "alert-9", "timestamp": "2025-03-13T21:13:43.292Z", "type": "failure_rate", "severity": "critical", "message": "Taux d'échec pour \"performance\" (10.0%) dépasse le seuil de 20.0%", "reportType": "performance", "value": 0.09998980189597906, "threshold": 0.2, "metadata": {"totalReports": 44, "failedReports": 4}, "acknowledged": true, "acknowledgedBy": "system", "acknowledgedAt": "2025-03-13T22:41:33.331Z"}, {"id": "alert-6", "timestamp": "2025-03-13T15:00:34.973Z", "type": "consecutive_failures", "severity": "error", "message": "7 échecs consécutifs ", "value": 7, "threshold": 3, "metadata": {"lastReportId": "report-885", "errorMessage": "Erreur de connexion à la base de données"}, "acknowledged": false}, {"id": "alert-16", "timestamp": "2025-03-13T06:28:31.482Z", "type": "consecutive_failures", "severity": "info", "message": "3 échecs consécutifs ", "value": 3, "threshold": 3, "metadata": {"lastReportId": "report-946", "errorMessage": "Erreur de connexion à la base de données"}, "acknowledged": false}, {"id": "alert-15", "timestamp": "2025-03-12T13:25:14.439Z", "type": "average_duration", "severity": "critical", "message": "Durée moyenne pour \"financial\" (9824ms) dépasse le seuil de 5000ms", "reportType": "financial", "value": 9823.931784098944, "threshold": 5000, "metadata": {"totalReports": 35}, "acknowledged": false}, {"id": "alert-14", "timestamp": "2025-03-12T12:29:42.821Z", "type": "average_duration", "severity": "info", "message": "<PERSON><PERSON><PERSON> moy<PERSON> (5253ms) dépasse le seuil de 5000ms", "value": 5253.115130422491, "threshold": 5000, "metadata": {"totalReports": 17}, "acknowledged": false}, {"id": "alert-12", "timestamp": "2025-03-12T11:03:11.457Z", "type": "average_duration", "severity": "critical", "message": "Durée moyenne pour \"inventory\" (3208ms) dépasse le seuil de 5000ms", "reportType": "inventory", "value": 3208.158708078976, "threshold": 5000, "metadata": {"totalReports": 21}, "acknowledged": false}, {"id": "alert-20", "timestamp": "2025-03-12T02:19:11.642Z", "type": "consecutive_failures", "severity": "error", "message": "4 échecs consécutifs pour les rapports de type \"marketing\"", "reportType": "marketing", "value": 4, "threshold": 3, "metadata": {"lastReportId": "report-599", "errorMessage": "Erreur de connexion à la base de données"}, "acknowledged": false}, {"id": "alert-13", "timestamp": "2025-03-11T21:11:07.831Z", "type": "failure_rate", "severity": "critical", "message": "Taux d'échec pour \"inventory\" (39.1%) dépasse le seuil de 20.0%", "reportType": "inventory", "value": 0.3909337828414582, "threshold": 0.2, "metadata": {"totalReports": 100, "failedReports": 39}, "acknowledged": true, "acknowledgedBy": "admin", "acknowledgedAt": "2025-03-11T21:31:58.791Z"}, {"id": "alert-7", "timestamp": "2025-03-10T21:36:14.347Z", "type": "consecutive_failures", "severity": "info", "message": "3 échecs consécutifs pour les rapports de type \"financial\"", "reportType": "financial", "value": 3, "threshold": 3, "metadata": {"lastReportId": "report-288", "errorMessage": "Erreur de connexion à la base de données"}, "acknowledged": true, "acknowledgedBy": "system", "acknowledgedAt": "2025-03-10T22:53:43.867Z"}, {"id": "alert-2", "timestamp": "2025-03-10T19:31:43.785Z", "type": "consecutive_failures", "severity": "error", "message": "6 échecs consécutifs ", "value": 6, "threshold": 3, "metadata": {"lastReportId": "report-950", "errorMessage": "Erreur de connexion à la base de données"}, "acknowledged": false}, {"id": "alert-10", "timestamp": "2025-03-10T10:49:50.398Z", "type": "delivery_failure", "severity": "warning", "message": "Échec de livraison pour le rapport report-676", "reportType": "sales", "value": 1, "threshold": 1, "metadata": {"deliveryId": "delivery-650", "recipients": ["<EMAIL>", "<EMAIL>"], "errorMessage": "Erreur SMTP: <PERSON><PERSON><PERSON> indisponible"}, "acknowledged": true, "acknowledgedBy": "system", "acknowledgedAt": "2025-03-10T12:06:13.770Z"}, {"id": "alert-3", "timestamp": "2025-03-10T05:32:36.557Z", "type": "failure_rate", "severity": "critical", "message": "Taux d'échec pour \"marketing\" (19.8%) dépasse le seuil de 20.0%", "reportType": "marketing", "value": 0.1977246411636171, "threshold": 0.2, "metadata": {"totalReports": 28, "failedReports": 5}, "acknowledged": false}, {"id": "alert-1", "timestamp": "2025-03-10T03:12:26.021Z", "type": "consecutive_failures", "severity": "error", "message": "7 échecs consécutifs pour les rapports de type \"inventory\"", "reportType": "inventory", "value": 7, "threshold": 3, "metadata": {"lastReportId": "report-594", "errorMessage": "Erreur de connexion à la base de données"}, "acknowledged": false}, {"id": "alert-5", "timestamp": "2025-03-09T19:05:25.549Z", "type": "average_duration", "severity": "critical", "message": "Durée moyenne pour \"inventory\" (8203ms) dépasse le seuil de 5000ms", "reportType": "inventory", "value": 8202.98125246003, "threshold": 5000, "metadata": {"totalReports": 107}, "acknowledged": false}, {"id": "alert-18", "timestamp": "2025-03-09T16:25:18.624Z", "type": "delivery_failure", "severity": "warning", "message": "Échec de livraison pour le rapport report-734", "reportType": "sales", "value": 1, "threshold": 1, "metadata": {"deliveryId": "delivery-996", "recipients": ["<EMAIL>", "<EMAIL>"], "errorMessage": "Erreur SMTP: <PERSON><PERSON><PERSON> indisponible"}, "acknowledged": false}, {"id": "alert-8", "timestamp": "2025-03-08T21:50:09.204Z", "type": "delivery_failure", "severity": "error", "message": "Échec de livraison pour le rapport report-112", "reportType": "marketing", "value": 1, "threshold": 1, "metadata": {"deliveryId": "delivery-139", "recipients": ["<EMAIL>", "<EMAIL>"], "errorMessage": "Erreur SMTP: <PERSON><PERSON><PERSON> indisponible"}, "acknowledged": true, "acknowledgedBy": "admin", "acknowledgedAt": "2025-03-08T22:21:45.898Z"}, {"id": "alert-19", "timestamp": "2025-03-08T16:53:45.334Z", "type": "delivery_failure", "severity": "info", "message": "Échec de livraison pour le rapport report-716", "reportType": "marketing", "value": 1, "threshold": 1, "metadata": {"deliveryId": "delivery-702", "recipients": ["<EMAIL>", "<EMAIL>"], "errorMessage": "Erreur SMTP: <PERSON><PERSON><PERSON> indisponible"}, "acknowledged": false}]