# Implémentation des Tests End-to-End (E2E)

## Introduction

Ce document détaille l'approche et les étapes nécessaires pour implémenter une suite complète de tests end-to-end (E2E) pour le projet RB2. Ces tests sont essentiels pour garantir le bon fonctionnement de l'application dans son ensemble, à travers tous ses composants interconnectés.

## Objectifs des Tests E2E

- **Validation de l'intégration** : Vérifier que tous les composants fonctionnent correctement ensemble
- **Simulation utilisateur** : Reproduire les parcours utilisateurs critiques
- **Régression** : Détecter les régressions fonctionnelles avant le déploiement
- **Validation de performance** : Mesurer les temps de réponse et les performances globales

## Framework et Outils

### Cypress comme Framework Principal

Nous utilisons Cypress comme framework principal pour les tests E2E en raison de:

- Interface utilisateur intuitive et puissante
- Exécution stable et reproductible
- Visualisation et dépannage simplifiés
- Support des captures d'écran et vidéos automatiques

### Outils Complémentaires

- **Percy** : Tests visuels et de régression
- **Lighthouse CI** : Tests de performance et d'accessibilité
- **Cucumber** : Définition des scénarios en langage Gherkin
- **GitHub Actions** : Exécution automatisée dans le pipeline CI/CD

## Architecture des Tests E2E

```
tests/e2e/
├── fixtures/            # Données de test
├── integration/         # Tests par fonctionnalité
│   ├── auth/            # Tests d'authentification
│   ├── backend/         # Tests API backend
│   └── frontend/        # Tests d'interface utilisateur
├── plugins/             # Extensions Cypress
├── support/             # Helpers et commandes personnalisées
│   ├── commands.js      # Commandes Cypress personnalisées
│   └── index.js         # Point d'entrée
└── cypress.json         # Configuration Cypress
```

## Configuration de l'Environnement

### Configuration de Base

```json
// cypress.json
{
  "baseUrl": "http://localhost:3000",
  "viewportWidth": 1280,
  "viewportHeight": 720,
  "defaultCommandTimeout": 10000,
  "requestTimeout": 15000,
  "responseTimeout": 15000,
  "video": true,
  "screenshotOnRunFailure": true,
  "screenshotsFolder": "cypress/screenshots",
  "videosFolder": "cypress/videos",
  "supportFile": "cypress/support/index.js",
  "pluginsFile": "cypress/plugins/index.js",
  "fixturesFolder": "cypress/fixtures",
  "testFiles": "**/*.spec.js",
  "env": {
    "apiUrl": "http://localhost:4000/api"
  }
}
```

### Environnements Multiples

Les configurations spécifiques par environnement sont définies dans des fichiers séparés:

```javascript
// cypress/config/local.json
{
  "baseUrl": "http://localhost:3000",
  "env": {
    "apiUrl": "http://localhost:4000/api"
  }
}

// cypress/config/staging.json
{
  "baseUrl": "https://staging.rb2.com",
  "env": {
    "apiUrl": "https://api-staging.rb2.com"
  }
}
```

## Commandes Personnalisées

Les commandes personnalisées pour simplifier les tests:

```javascript
// cypress/support/commands.js

// Authentification
Cypress.Commands.add('login', (username, password) => {
  cy.visit('/login');
  cy.get('#username').type(username);
  cy.get('#password').type(password);
  cy.get('button[type="submit"]').click();
  cy.url().should('include', '/dashboard');
});

// Vérification d'API
Cypress.Commands.add('verifyApiResponse', (endpoint, expectedStatus = 200) => {
  cy.request({
    url: `${Cypress.env('apiUrl')}${endpoint}`,
    failOnStatusCode: false
  }).then((response) => {
    expect(response.status).to.eq(expectedStatus);
  });
});
```

## Parcours Utilisateurs à Tester

Les parcours utilisateurs critiques qui doivent être couverts:

1. **Authentification**
   - Inscription
   - Connexion
   - Récupération de mot de passe
   - Déconnexion

2. **Navigation**
   - Menu principal
   - Recherche
   - Filtres

3. **Fonctionnalités Métier**
   - Création de contenu
   - Édition de profil
   - Gestion des paramètres

4. **Flux de Paiement** (si applicable)
   - Ajout au panier
   - Processus de checkout
   - Confirmation

## Exemple de Test E2E

```javascript
// cypress/integration/auth/login.spec.js
describe('Login Flow', () => {
  beforeEach(() => {
    cy.visit('/login');
  });

  it('should login with valid credentials', () => {
    cy.get('#username').type('<EMAIL>');
    cy.get('#password').type('password123');
    cy.get('button[type="submit"]').click();
    
    // Vérification de la redirection
    cy.url().should('include', '/dashboard');
    
    // Vérification des éléments après connexion
    cy.get('.user-profile-dropdown').should('be.visible');
    cy.get('.logout-button').should('exist');
  });

  it('should show error with invalid credentials', () => {
    cy.get('#username').type('<EMAIL>');
    cy.get('#password').type('wrongpassword');
    cy.get('button[type="submit"]').click();
    
    // Vérification du message d'erreur
    cy.get('.error-message').should('be.visible');
    cy.get('.error-message').should('contain', 'Identifiants invalides');
    
    // Vérification qu'on reste sur la page de login
    cy.url().should('include', '/login');
  });
});
```

## Intégration avec CI/CD

### Configuration GitHub Actions

```yaml
# .github/workflows/e2e-tests.yml
name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16
          
      - name: Install dependencies
        run: npm ci
        
      - name: Start backend service
        run: npm run start:api:ci
        
      - name: Build frontend
        run: npm run build
        
      - name: Start frontend
        run: npm run start:ci &
        
      - name: Run Cypress tests
        uses: cypress-io/github-action@v4
        with:
          browser: chrome
          config-file: cypress/config/ci.json
          
      - name: Upload screenshots
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots
          
      - name: Upload videos
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cypress-videos
          path: cypress/videos
```

## Bonnes Pratiques

1. **Données de test**
   - Utiliser des fixtures pour les données statiques
   - Créer des données dynamiques via l'API si nécessaire
   - Nettoyer les données après les tests

2. **Stabilité des tests**
   - Utiliser des sélecteurs robustes (attributs data-testid)
   - Attendre explicitement les conditions plutôt que des délais fixes
   - Isoler les tests pour éviter les dépendances

3. **Performance**
   - Regrouper les tests logiquement
   - Réutiliser l'état de l'application quand possible
   - Paralléliser l'exécution des tests

4. **Maintenabilité**
   - Suivre le pattern Page Object pour les interfaces complexes
   - Documenter les tests complexes
   - Rendre les messages d'erreur explicites

## Plan d'Action Détaillé : Implémentation des Tests E2E

## Vue d'ensemble
Ce document présente un plan d'action détaillé pour l'implémentation et l'amélioration des tests end-to-end (E2E) dans notre application frontend, avec un focus sur l'utilisation de Cypress et Percy pour les tests de régression visuelle.

## Durée totale estimée : 3 semaines

## Phase 1 : Configuration (1 semaine)

### 1.1 Finaliser la configuration Cypress existante
- Corriger le fichier `cypress.config.ts` qui contient des erreurs de syntaxe
- Ajouter des commandes personnalisées pertinentes dans `cypress/support/commands.ts`
- Configurer les fixtures et les mocks pour simuler les API
- Mettre en place des helpers pour les opérations communes (login, navigation, etc.)
- Standardiser la structure des tests dans le dossier `cypress/e2e`

### 1.2 Configurer Percy pour les tests de régression visuelle
- Réviser le fichier `.percy.yml` existant pour s'assurer qu'il correspond aux besoins actuels
- Ajouter la configuration Percy dans les workflows CI/CD
- Définir les breakpoints stratégiques pour les captures d'écran
- Mettre en place des sélecteurs pour ignorer les éléments dynamiques
- Créer des tests visuels de base pour les composants critiques

### 1.3 Mettre en place l'intégration CI/CD dans GitHub Actions
- Améliorer le workflow `.github/workflows/e2e-tests.yml` existant
- Configurer la parallélisation des tests pour accélérer l'exécution
- Mettre en place la rétention des artefacts (vidéos, captures d'écran)
- Ajouter des notifications en cas d'échec
- Configurer le dashboard Cypress pour le suivi des exécutions

## Phase 2 : Implémentation des Tests Critiques (2 semaines)

### 2.1 Tests d'authentification
```typescript
// cypress/e2e/auth/login.cy.ts
describe('Authentication', () => {
  beforeEach(() => {
    cy.visit('/login')
  })

  it('should successfully login with valid credentials', () => {
    cy.get('[data-testid="email-input"]').type('<EMAIL>')
    cy.get('[data-testid="password-input"]').type('password123')
    cy.get('[data-testid="login-button"]').click()
    cy.url().should('include', '/dashboard')
    cy.get('[data-testid="user-welcome"]').should('be.visible')
  })

  it('should display error with invalid credentials', () => {
    cy.get('[data-testid="email-input"]').type('<EMAIL>')
    cy.get('[data-testid="password-input"]').type('wrongpass')
    cy.get('[data-testid="login-button"]').click()
    cy.get('[data-testid="error-message"]').should('be.visible')
    cy.url().should('include', '/login')
  })

  it('should allow password reset request', () => {
    cy.get('[data-testid="forgot-password"]').click()
    cy.url().should('include', '/reset-password')
    cy.get('[data-testid="email-input"]').type('<EMAIL>')
    cy.get('[data-testid="submit-button"]').click()
    cy.get('[data-testid="success-message"]').should('be.visible')
  })
})
```

### 2.2 Tests de navigation
```typescript
// cypress/e2e/navigation/main-navigation.cy.ts
describe('Main Navigation', () => {
  beforeEach(() => {
    cy.login() // Utilisation d'une commande personnalisée
    cy.visit('/dashboard')
  })

  it('should navigate to profile page', () => {
    cy.get('[data-testid="nav-profile"]').click()
    cy.url().should('include', '/profile')
    cy.get('[data-testid="profile-header"]').should('be.visible')
  })

  it('should navigate to settings page', () => {
    cy.get('[data-testid="nav-settings"]').click()
    cy.url().should('include', '/settings')
    cy.get('[data-testid="settings-header"]').should('be.visible')
  })

  it('should toggle sidebar on mobile view', () => {
    cy.viewport('iphone-x')
    cy.get('[data-testid="sidebar"]').should('not.be.visible')
    cy.get('[data-testid="menu-toggle"]').click()
    cy.get('[data-testid="sidebar"]').should('be.visible')
  })
})
```

### 2.3 Tests des fonctionnalités métier
```typescript
// cypress/e2e/business/retreat-booking.cy.ts
describe('Retreat Booking Flow', () => {
  beforeEach(() => {
    cy.login()
    cy.visit('/retreats')
  })

  it('should complete a booking from search to confirmation', () => {
    // Recherche
    cy.get('[data-testid="search-location"]').type('Bali')
    cy.get('[data-testid="search-dates"]').click()
    cy.get('.calendar-day[data-value="2023-10-15"]').click()
    cy.get('.calendar-day[data-value="2023-10-22"]').click()
    cy.get('[data-testid="search-button"]').click()
    
    // Sélection
    cy.get('[data-testid="retreat-card"]').first().click()
    cy.url().should('include', '/retreat-details')
    
    // Détails du séjour
    cy.get('[data-testid="select-package"]').click()
    cy.get('[data-testid="continue-button"]').click()
    
    // Paiement
    cy.get('[data-testid="card-number"]').type('****************')
    cy.get('[data-testid="card-expiry"]').type('12/25')
    cy.get('[data-testid="card-cvc"]').type('123')
    cy.get('[data-testid="payment-button"]').click()
    
    // Confirmation
    cy.url().should('include', '/booking-confirmation')
    cy.get('[data-testid="booking-reference"]').should('be.visible')
  })
})
```

### 2.4 Tests de paiement
```typescript
// cypress/e2e/payment/payment-methods.cy.ts
describe('Payment Processing', () => {
  beforeEach(() => {
    cy.login()
    cy.visit('/checkout/123') // ID fictif de panier
  })

  it('should process credit card payment successfully', () => {
    cy.get('[data-testid="payment-method-cc"]').click()
    cy.get('[data-testid="card-number"]').type('****************')
    cy.get('[data-testid="card-expiry"]').type('12/25')
    cy.get('[data-testid="card-cvc"]').type('123')
    cy.get('[data-testid="pay-button"]').click()
    cy.get('[data-testid="payment-success"]', { timeout: 10000 }).should('be.visible')
  })

  it('should show error with invalid card', () => {
    cy.get('[data-testid="payment-method-cc"]').click()
    cy.get('[data-testid="card-number"]').type('****************') // Carte rejetée
    cy.get('[data-testid="card-expiry"]').type('12/25')
    cy.get('[data-testid="card-cvc"]').type('123')
    cy.get('[data-testid="pay-button"]').click()
    cy.get('[data-testid="payment-error"]').should('be.visible')
  })

  it('should process PayPal payment redirect flow', () => {
    cy.get('[data-testid="payment-method-paypal"]').click()
    cy.get('[data-testid="paypal-button"]').click()
    // Simuler le retour de PayPal
    cy.visit('/checkout/123/success?token=test-token')
    cy.get('[data-testid="payment-success"]').should('be.visible')
  })
})
```

### 2.5 Tests de performance visuelle
```typescript
// cypress/e2e/visual/critical-pages.cy.ts
describe('Visual Regression Testing', () => {
  it('should match homepage visual snapshot', () => {
    cy.visit('/')
    cy.percySnapshot('Homepage')
  })

  it('should match dashboard visual snapshot', () => {
    cy.login()
    cy.visit('/dashboard')
    cy.percySnapshot('Dashboard')
  })

  it('should match retreat details visual snapshot', () => {
    cy.visit('/retreat-details/123') // ID fictif
    cy.percySnapshot('Retreat Details')
  })

  it('should match checkout page visual snapshot', () => {
    cy.login()
    cy.visit('/checkout/123') // ID fictif
    cy.percySnapshot('Checkout')
  })
})
```

## Métriques de succès

### Couverture
- Atteindre une couverture > 80% des parcours utilisateurs critiques
- Couvrir 100% des fonctionnalités de paiement
- Atteindre une couverture > 90% des pages principales

### Performance
- Temps d'exécution de la suite complète < 10 minutes
- Temps d'exécution moyen par test < 15 secondes

### Fiabilité
- Taux de succès > 98% en environnement CI
- < 2% de tests flaky (instables)

### Visuel
- 100% des composants UI critiques couverts par Percy
- Aucune régression visuelle non détectée

## Intégration continue

```yaml
# .github/workflows/e2e-tests.yml
name: E2E Tests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: Install dependencies
        run: npm ci
      - name: Run Cypress tests
        uses: cypress-io/github-action@v5
        with:
          browser: chrome
          record: true
          parallel: true
          group: 'UI Tests'
          config-file: cypress.config.ts
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Run Percy tests
        run: npm run test:visual
        env:
          PERCY_TOKEN: ${{ secrets.PERCY_TOKEN }}
      
      - name: Upload test artifacts
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots-videos
          path: |
            cypress/screenshots
            cypress/videos
          retention-days: 7
```

## Conclusion

L'implémentation des tests E2E est une priorité immédiate pour garantir la qualité et la fiabilité de l'application RB2. En suivant ce plan d'implémentation, nous pourrons progressivement construire une suite de tests solide qui sécurisera nos déploiements et réduira les régressions.

## Prochaines Étapes

- [ ] Finaliser la configuration de Cypress et l'intégration CI/CD
- [ ] Former l'équipe sur l'écriture des tests E2E
- [ ] Définir les parcours critiques avec les parties prenantes
- [ ] Commencer l'implémentation des tests prioritaires 