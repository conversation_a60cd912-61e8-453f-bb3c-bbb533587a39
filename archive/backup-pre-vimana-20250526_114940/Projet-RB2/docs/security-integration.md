# Documentation d'Intégration avec le Microservice Sécurité

Ce document décrit l'architecture et l'implémentation de l'intégration entre le frontend et le microservice Sécurité dans le cadre du projet Retreat & Be.

## Table des matières

1. [Architecture générale](#architecture-générale)
2. [Couche d'intégration](#couche-dintégration)
3. [Hooks React](#hooks-react)
4. [Composants d'interface](#composants-dinterface)
5. [Configuration](#configuration)
6. [Journalisation des événements](#journalisation-des-événements)
7. [Filtrage de sécurité des fichiers](#filtrage-de-sécurité-des-fichiers)
8. [Authentification et autorisation](#authentification-et-autorisation)
9. [Bonnes pratiques](#bonnes-pratiques)
10. [Dépannage](#dépannage)

## Architecture générale

L'intégration avec le microservice Sécurité suit une architecture en couches :

```
┌─────────────────────────────────────────────────────────────┐
│                    Composants React                         │
│ (SecurityDashboard, VulnerabilityScanner, etc.)             │
└───────────────────────────┬─────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────┐
│                      Hooks React                            │
│ (useSecurityIntegration, useVulnerabilityScanner, etc.)     │
└───────────────────────────┬─────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────┐
│                Service d'intégration                        │
│ (SecurityIntegrationService)                                │
└───────────────────────────┬─────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────┐
│                  Configuration                              │
│ (securityConfig.ts)                                         │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
                    Microservice Sécurité
```

Cette architecture permet une séparation claire des responsabilités et facilite la maintenance et les tests.

## Couche d'intégration

La couche d'intégration est implémentée dans le fichier `SecurityIntegrationService.ts`. Ce service :

- Gère les appels API vers le microservice Sécurité
- S'occupe de l'authentification et de la gestion des tokens
- Journalise les événements de sécurité
- Gère les erreurs et les exceptions

### Principales fonctionnalités

- **Authentification** : Connexion, déconnexion, gestion de la 2FA
- **Analyse de sécurité** : Scan des ressources et des fichiers
- **Gestion des vulnérabilités** : Lancement de scans, suivi des résultats
- **Conformité** : Vérification et génération de rapports
- **Gestion des incidents** : Signalement et suivi des incidents
- **Métriques et alertes** : Surveillance et notification

### Exemple d'utilisation

```typescript
import securityIntegrationService from '../services/SecurityIntegrationService';

// Authentification
const authResponse = await securityIntegrationService.authenticate({
  username: '<EMAIL>',
  password: 'password123'
});

// Scan de fichier
const scanResult = await securityIntegrationService.scanFile(file);

// Vérification de conformité
const complianceResult = await securityIntegrationService.checkCompliance({
  framework: 'gdpr',
  scope: ['data-protection', 'user-rights']
});
```

## Hooks React

Les hooks React fournissent une interface adaptée au paradigme React pour utiliser le service d'intégration. Ils sont implémentés dans le fichier `useSecurityIntegration.ts`.

### Hooks disponibles

- **useFileSecurity** : Analyse de sécurité des fichiers
- **useVulnerabilityScanner** : Gestion des scans de vulnérabilités
- **useComplianceCheck** : Vérification de conformité
- **useSecurityIncidents** : Gestion des incidents de sécurité
- **useSecurityMetrics** : Métriques de sécurité
- **useSecurityAlerts** : Alertes de sécurité
- **useSecurityIntegration** : Hook principal combinant tous les hooks spécifiques

### Exemple d'utilisation

```tsx
import { useVulnerabilityScanner } from '../../hooks/useSecurityIntegration';

const MyComponent = () => {
  const {
    startScan,
    checkScanStatus,
    getScanResults,
    scanning,
    vulnerabilities,
    error
  } = useVulnerabilityScanner();

  const handleStartScan = async () => {
    try {
      await startScan('https://example.com', 'quick');
    } catch (err) {
      console.error('Erreur lors du démarrage du scan:', err);
    }
  };

  return (
    <div>
      <button onClick={handleStartScan} disabled={scanning}>
        {scanning ? 'Scan en cours...' : 'Lancer un scan'}
      </button>

      {error && <div className="error">{error}</div>}

      {vulnerabilities.length > 0 && (
        <ul>
          {vulnerabilities.map(vuln => (
            <li key={vuln.id}>{vuln.title} - {vuln.severity}</li>
          ))}
        </ul>
      )}
    </div>
  );
};
```

## Composants d'interface

Les composants d'interface utilisent les hooks React pour fournir une interface utilisateur complète pour les fonctionnalités de sécurité.

### Composants disponibles

- **SecurityDashboardPage** : Page principale du tableau de bord de sécurité
- **SecurityOverview** : Vue d'ensemble de la sécurité
- **SecurityMonitoring** : Surveillance des métriques de sécurité
- **VulnerabilityScanner** : Scanner de vulnérabilités
- **SecurityIncidentManager** : Gestion des incidents de sécurité
- **SecurityNotifications** : Notifications de sécurité
- **SecurityAuditLog** : Journal d'audit de sécurité
- **KeyManagement** : Gestion des clés API
- **FileUploadSecurity** : Sécurité des téléchargements de fichiers
- **AccessControlPanel** : Contrôle d'accès
- **ComplianceReport** : Rapports de conformité
- **SecuritySettings** : Paramètres de sécurité

## Configuration

La configuration de l'intégration avec le microservice Sécurité est centralisée dans le fichier `securityConfig.ts`.

### Principales sections de configuration

- **SECURITY_API_CONFIG** : Configuration des endpoints du microservice
- **FILE_SECURITY_CONFIG** : Configuration des filtres de sécurité pour les fichiers
- **PASSWORD_POLICY** : Configuration de la politique de mots de passe
- **AUTH_CONFIG** : Configuration de l'authentification
- **SECURITY_LOGGING_CONFIG** : Configuration de la journalisation des événements
- **THREAT_DETECTION_CONFIG** : Configuration de la détection des menaces
- **CSP_CONFIG** : Configuration de la Content Security Policy
- **MICROSERVICES_SECURITY_CONFIG** : Configuration pour l'intégration avec les autres microservices

### Exemple de configuration

```typescript
// Configuration des endpoints du microservice de sécurité
export const SECURITY_API_CONFIG = {
  baseUrl: process.env.REACT_APP_SECURITY_SERVICE_URL || 'https://api.retreatandbe.com/security',
  timeout: 10000,
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    // ...
  },
  // ...
};

// Configuration des filtres de sécurité pour les fichiers
export const FILE_SECURITY_CONFIG = {
  dangerousExtensions: ['exe', 'dll', 'js', 'vbs', ...],
  dangerousMimeTypes: ['application/x-msdownload', ...],
  maxFileSize: 10 * 1024 * 1024, // 10 MB
  dualFiltering: {
    enabled: true,
    // ...
  }
};
```

## Journalisation des événements

La journalisation des événements de sécurité est gérée par le module `securityEvents.ts`. Ce module :

- Définit les types d'événements de sécurité
- Définit les niveaux de sévérité
- Fournit des fonctions pour journaliser les événements
- Permet de surveiller les activités suspectes

### Types d'événements

Les événements sont classés en plusieurs catégories :

- Événements d'authentification (LOGIN_SUCCESS, LOGIN_FAILURE, etc.)
- Événements de gestion des fichiers (FILE_UPLOAD, FILE_DOWNLOAD)
- Événements de gestion des accès (PERMISSION_CHANGE, API_KEY_CREATED, etc.)
- Événements de détection de menaces (SUSPICIOUS_ACTIVITY, SECURITY_INCIDENT, etc.)
- Événements d'API et d'intégration (API_ERROR)
- Événements de configuration de sécurité (SECURITY_SETTING_CHANGED, etc.)
- Événements de scan et d'analyse (VULNERABILITY_SCAN_STARTED, etc.)
- Événements de conformité (COMPLIANCE_CHECK, REPORT_GENERATED)
- Événements de gestion des alertes (ALERT_STATUS_CHANGED)

### Exemple d'utilisation

```typescript
import { SecurityEventType, SecurityEventSeverity, logSecurityEvent } from '../utils/securityEvents';

// Journaliser un événement de connexion réussie
logSecurityEvent({
  type: SecurityEventType.LOGIN_SUCCESS,
  timestamp: new Date(),
  severity: SecurityEventSeverity.INFO,
  userId: 'user123',
  details: {
    username: '<EMAIL>',
    method: 'password'
  }
});

// Journaliser un événement de sécurité critique
logSecurityEvent({
  type: SecurityEventType.SECURITY_INCIDENT,
  timestamp: new Date(),
  severity: SecurityEventSeverity.CRITICAL,
  details: {
    title: 'Tentative d\'accès non autorisé',
    description: 'Tentative d\'accès à des données sensibles détectée',
    sourceIp: '***********'
  }
});
```

## Filtrage de sécurité des fichiers

Le filtrage de sécurité des fichiers est implémenté selon une approche à double filtre :

1. **Premier filtre** : Via le microservice Sécurité
2. **Second filtre** : Via le service de sécurité Backend

### Processus de filtrage

1. Le fichier est d'abord validé côté client (taille, type MIME, extension)
2. Le fichier est envoyé au microservice Sécurité pour analyse
3. Si le fichier passe le premier filtre, il est envoyé au service Backend pour une seconde analyse
4. Si le fichier passe les deux filtres, il est considéré comme sûr et peut être traité

### Configuration du filtrage

La configuration du filtrage est définie dans `FILE_SECURITY_CONFIG` :

```typescript
export const FILE_SECURITY_CONFIG = {
  // Extensions de fichiers dangereuses à bloquer
  dangerousExtensions: [
    'exe', 'dll', 'com', 'bat', 'cmd', 'msi', 'ps1',
    'js', 'vbs', 'py', 'rb', 'pl', 'sh', 'bash',
    // ...
  ],

  // Types MIME à bloquer
  dangerousMimeTypes: [
    'application/x-msdownload',
    'application/x-msdos-program',
    // ...
  ],

  // Taille maximale de fichier (en octets)
  maxFileSize: 10 * 1024 * 1024, // 10 MB

  // Configuration du double filtrage
  dualFiltering: {
    enabled: true,
    firstFilter: {
      endpoint: '/scan/file',
      timeout: 30000 // 30 secondes
    },
    secondFilter: {
      endpoint: '/api/security/file-validation',
      timeout: 30000 // 30 secondes
    }
  }
};
```

## Authentification et autorisation

L'authentification et l'autorisation sont gérées par le microservice Sécurité, avec une intégration côté frontend.

### Processus d'authentification

1. L'utilisateur saisit ses identifiants
2. Le frontend envoie les identifiants au microservice Sécurité
3. Le microservice vérifie les identifiants et renvoie un token JWT
4. Le frontend stocke le token et l'utilise pour les requêtes ultérieures
5. Le token est automatiquement rafraîchi avant expiration

### Authentification à deux facteurs (2FA)

L'authentification à deux facteurs est prise en charge via :

- Configuration de la 2FA (génération d'un QR code)
- Activation de la 2FA (vérification du code)
- Désactivation de la 2FA (vérification du code)

### Autorisation basée sur les rôles

L'autorisation est basée sur les rôles et les permissions :

- Les rôles sont attribués aux utilisateurs
- Les permissions sont attribuées aux rôles
- Les composants vérifient les permissions avant d'afficher certaines fonctionnalités

## Bonnes pratiques

### Sécurité

- Toujours valider les entrées utilisateur côté client ET côté serveur
- Utiliser HTTPS pour toutes les communications
- Ne jamais stocker de données sensibles en clair
- Journaliser tous les événements de sécurité importants
- Mettre en place une politique de mots de passe robuste
- Utiliser l'authentification à deux facteurs lorsque c'est possible

### Performance

- Mettre en cache les résultats des appels API lorsque c'est approprié
- Utiliser la pagination pour les listes longues
- Optimiser les requêtes pour minimiser le transfert de données
- Utiliser des timeouts appropriés pour les opérations longues

### Maintenance

- Centraliser la configuration dans des fichiers dédiés
- Documenter les interfaces et les comportements attendus
- Utiliser des types TypeScript pour garantir la cohérence des données
- Suivre une convention de nommage cohérente

## Dépannage

### Problèmes courants

#### Le microservice Sécurité n'est pas accessible

- Vérifier que l'URL du microservice est correcte dans la configuration
- Vérifier que le microservice est en cours d'exécution
- Vérifier les logs du microservice pour détecter d'éventuelles erreurs

#### Les tokens d'authentification expirent trop rapidement

- Vérifier la configuration de durée de vie des tokens dans le microservice
- Vérifier que le mécanisme de rafraîchissement des tokens fonctionne correctement

#### Les scans de vulnérabilités échouent

- Vérifier que la cible du scan est accessible
- Vérifier que les permissions nécessaires sont accordées
- Vérifier les logs du microservice pour détecter d'éventuelles erreurs

### Journalisation et débogage

- Activer la journalisation détaillée en développement
- Utiliser les outils de développement du navigateur pour inspecter les requêtes
- Vérifier les logs du microservice pour détecter d'éventuelles erreurs côté serveur

### Contact et support

Pour toute question ou problème concernant l'intégration avec le microservice Sécurité, contacter l'équipe de sécurité à l'adresse <EMAIL>.

## Mise à jour du document

Ce document est régulièrement mis à jour pour refléter les changements dans l'intégration avec le microservice Sécurité.

Dernière mise à jour : 18 avril 2025