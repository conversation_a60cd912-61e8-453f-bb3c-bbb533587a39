# Tests Optimisés

Ce répertoire contient la documentation relative à l'optimisation des tests dans le projet. Il explique les problèmes rencontrés, les solutions mises en œuvre et les meilleures pratiques à suivre.

## Contenu

- [TEST_OPTIMIZATIONS.md](./TEST_OPTIMIZATIONS.md) - Documentation détaillée des optimisations effectuées sur les tests unitaires isolés
- Scripts de test optimisés dans le répertoire `/scripts`

## Problèmes Résolus

Nous avons rencontré plusieurs problèmes de timeout lors de l'exécution des tests unitaires isolés :

1. **Timeouts dans les tests d'isolement** - Les tests `CacheServiceIsolated.test.ts`, `ConfigServiceIsolated.test.ts`, `DatabaseServiceIsolated.test.ts` et `EmailServiceIsolated.test.ts` échouaient fréquemment en raison de timeouts.

2. **Délais artificiels trop longs** - Certains tests utilisaient des délais artificiels (`setTimeout`) qui pouvaient être trop longs ou variables selon l'environnement d'exécution.

3. **Tests de temporisation réelle** - Certains tests tentaient de vérifier des comportements temporels réels (comme l'expiration de cache) qui sont intrinsèquement instables dans un environnement de test.

## Solutions Mises en Œuvre

1. **Réduction des délais artificiels** - Suppression ou réduction significative des délais dans les mocks.

2. **Validation d'état au lieu de temporisation** - Test des états directement plutôt que d'attendre des changements temporels.

3. **Assertions plus flexibles** - Utilisation d'assertions moins strictes pour éviter les échecs dus à des variations mineures.

4. **Hooks d'initialisation simplifiés** - Élimination des attentes asynchrones inutiles dans les hooks `beforeEach`.

5. **Timeouts adaptés par test** - Ajustement des timeouts en fonction de la complexité réelle des tests.

## Utilisation du Script d'Exécution des Tests

Le script `run-all-tests.js` dans le répertoire `/scripts` permet d'exécuter tous les tests avec les configurations optimales de timeout.

### Exemples d'utilisation

```bash
# Exécuter les tests unitaires isolés (par défaut)
node scripts/run-all-tests.js

# Exécuter les tests unitaires
node scripts/run-all-tests.js --type=unit

# Exécuter les tests d'intégration
node scripts/run-all-tests.js --type=integration 

# Exécuter tous les tests
node scripts/run-all-tests.js --type=all

# Exécuter avec un timeout personnalisé (en millisecondes)
node scripts/run-all-tests.js --timeout=180000

# Activer le mode verbeux et le rapport de couverture
node scripts/run-all-tests.js --verbose --coverage
```

### Types de tests disponibles

- `isolated` - Tests unitaires isolés (CacheService, ConfigService, etc.)
- `unit` - Tous les tests unitaires
- `integration` - Tests d'intégration
- `e2e` - Tests end-to-end
- `all` - Tous les types de tests

### Options

- `--type=<type>` - Type de tests à exécuter (par défaut: isolated)
- `--timeout=<ms>` - Timeout en millisecondes (par défaut: 120000)
- `--verbose` - Active le mode verbeux
- `--coverage` - Active le rapport de couverture

## Meilleures Pratiques

Pour maintenir la stabilité des tests, suivez ces meilleures pratiques :

1. **Évitez les délais réels** - N'utilisez pas `setTimeout` dans les tests lorsque c'est possible.

2. **Simulez les états manuellement** - Pour tester des transitions d'état temporelles, manipulez directement l'état plutôt que d'attendre des changements temporels.

3. **Utilisez des assertions flexibles** - Préférez `toBeGreaterThan(0)` à `toBe(3)` pour les assertions où le nombre exact n'est pas crucial.

4. **Gérez les erreurs élégamment** - Utilisez des catch ou des callbacks d'erreur pour éviter que les erreurs non critiques n'interrompent les tests.

5. **Définissez des timeouts adaptés** - 5 secondes pour les tests simples, 2 minutes pour les tests complexes.

## Résultats

Grâce à ces optimisations :

- Le temps d'exécution total des tests isolés est passé de plusieurs minutes à quelques secondes
- Tous les tests passent de manière cohérente sans échecs aléatoires
- Les tests sont plus lisibles et maintenables

## Documentation Supplémentaire

Pour une explication technique détaillée des optimisations, consultez [TEST_OPTIMIZATIONS.md](./TEST_OPTIMIZATIONS.md).

---
Dernière mise à jour: 2024-02-25 