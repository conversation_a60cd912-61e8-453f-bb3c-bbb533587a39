# Documentation des Scripts - Retreat And Be

Ce document fournit une documentation détaillée pour les scripts d'automatisation utilisés dans la plateforme Retreat And Be, notamment les scripts de backup et de restauration des données.

## Table des matières

1. [Scripts de Backup](#1-scripts-de-backup)
   - [automated_backup.sh](#11-automated_backupsh)
   - [Utilisation avec Kubernetes](#12-utilisation-avec-kubernetes)
   - [Configuration](#13-configuration)
   - [Logs et Monitoring](#14-logs-et-monitoring)

2. [Scripts de Restauration](#2-scripts-de-restauration)
   - [restore_backup.sh](#21-restore_backupsh)
   - [Scénarios de Restauration](#22-scénarios-de-restauration)
   - [Restauration Point-in-Time](#23-restauration-point-in-time)

3. [Procédures de Maintenance](#3-procédures-de-maintenance)
   - [Vérification de l'Intégrité des Backups](#31-vérification-de-lintégrité-des-backups)
   - [Rotation des Logs](#32-rotation-des-logs)
   - [Tests de Restauration](#33-tests-de-restauration)

4. [Mises à Jour et Gestion des Dépendances](#4-mises-à-jour-et-gestion-des-dépendances)
   - [Mise à Jour des Scripts](#41-mise-à-jour-des-scripts)
   - [Dépendances Requises](#42-dépendances-requises)
   - [Compatibilité des Versions](#43-compatibilité-des-versions)

## 1. Scripts de Backup

### 1.1 automated_backup.sh

Le script `automated_backup.sh` est le script principal pour la sauvegarde automatisée des données de la plateforme. Il effectue des sauvegardes quotidiennes, hebdomadaires et mensuelles des éléments suivants :

- Base de données PostgreSQL
- Fichiers de configuration Kubernetes
- Inventaire des fichiers médias stockés sur S3

#### Fonctionnalités principales

- Backup complet de la base de données PostgreSQL avec compression
- Sauvegarde des configurations Kubernetes (deployments, services, configmaps, secrets)
- Création d'un inventaire des fichiers médias stockés sur S3
- Synchronisation des backups vers un bucket S3 distant
- Rotation automatique des backups selon la politique de rétention
- Journalisation détaillée des opérations

#### Syntaxe

```bash
./scripts/backup/automated_backup.sh
```

Le script ne nécessite aucun paramètre et s'exécute en tant que tâche planifiée via un CronJob Kubernetes.

### 1.2 Utilisation avec Kubernetes

Le script est configuré pour s'exécuter dans un pod Kubernetes via un CronJob. Voici un exemple de configuration :

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: automated-backup
  namespace: retreatnbe-production
spec:
  schedule: "0 1 * * *"  # Tous les jours à 1h du matin
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: backup-service-account
          containers:
          - name: backup
            image: retreatnbe/backup-tools:latest
            command:
            - /bin/bash
            - -c
            - "/scripts/backup/automated_backup.sh"
            volumeMounts:
            - name: backup-storage
              mountPath: /var/backups/retreatnbe
            - name: scripts
              mountPath: /scripts
            env:
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-key
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          - name: scripts
            configMap:
              name: backup-scripts
          restartPolicy: OnFailure
```

### 1.3 Configuration

Les variables de configuration principales du script se trouvent au début du fichier et peuvent être modifiées selon les besoins :

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `BACKUP_DIR` | Répertoire principal des backups | `/var/backups/retreatnbe` |
| `DB_BACKUP_DIR` | Répertoire des backups de base de données | `$BACKUP_DIR/database` |
| `CONFIG_BACKUP_DIR` | Répertoire des backups de configuration | `$BACKUP_DIR/configs` |
| `MEDIA_BACKUP_DIR` | Répertoire des inventaires de médias | `$BACKUP_DIR/media` |
| `RETENTION_DAYS` | Jours de conservation des backups quotidiens | `14` |
| `WEEKLY_RETENTION_WEEKS` | Semaines de conservation des backups hebdomadaires | `8` |
| `MONTHLY_RETENTION_MONTHS` | Mois de conservation des backups mensuels | `24` |
| `DB_HOST` | Hôte de la base de données | `postgresql.retreatnbe-production.svc.cluster.local` |
| `S3_BUCKET` | Bucket S3 pour les backups distants | `retreatnbe-backups` |

### 1.4 Logs et Monitoring

Le script génère des logs détaillés dans le répertoire `/var/backups/retreatnbe/logs/`. Chaque exécution crée un fichier de log avec un horodatage unique.

Pour surveiller les backups, vous pouvez :

1. **Vérifier les logs** : `cat /var/backups/retreatnbe/logs/backup_YYYYMMDD_HHMMSS.log`
2. **Configurer des alertes** : Les erreurs dans les logs peuvent déclencher des alertes via Prometheus/Grafana
3. **Dashboard de monitoring** : Un dashboard Grafana est disponible pour visualiser l'état des backups

## 2. Scripts de Restauration

### 2.1 restore_backup.sh

Le script `restore_backup.sh` permet de restaurer des backups en cas de besoin. Il prend en charge la restauration de différents types de données et offre plusieurs options.

#### Fonctionnalités principales

- Restauration complète ou sélective (base de données, configurations, fichiers médias)
- Support pour les backups quotidiens, hebdomadaires et mensuels
- Restauration à partir d'une date spécifique
- Point-in-time recovery pour la base de données
- Confirmation interactive pour éviter les restaurations accidentelles

#### Syntaxe

```bash
./scripts/backup/restore_backup.sh [options]
```

Options disponibles :

| Option | Description | Exemple |
|--------|-------------|---------|
| `-h, --help` | Afficher l'aide | `--help` |
| `-t, --type TYPE` | Type de restauration (db, config, media, all) | `--type db` |
| `-b, --backup-type TYPE` | Type de backup (daily, weekly, monthly) | `--backup-type weekly` |
| `-d, --date DATE` | Date du backup à restaurer (YYYYMMDD) | `--date 20231015` |
| `-l, --latest` | Restaurer le backup le plus récent | `--latest` |
| `-p, --point-in-time DATETIME` | Point-in-time recovery (YYYY-MM-DD HH:MM:SS) | `--point-in-time "2023-10-15 14:30:00"` |
| `-n, --namespace NAMESPACE` | Namespace Kubernetes à restaurer | `--namespace retreatnbe-staging` |
| `-y, --yes` | Ne pas demander de confirmation | `--yes` |

### 2.2 Scénarios de Restauration

#### Restauration complète

Pour restaurer l'ensemble des données à partir du backup le plus récent :

```bash
./scripts/backup/restore_backup.sh --type all --latest
```

#### Restauration de la base de données uniquement

Pour restaurer uniquement la base de données à partir d'un backup hebdomadaire spécifique :

```bash
./scripts/backup/restore_backup.sh --type db --backup-type weekly --date 20231015
```

#### Restauration dans un environnement de test

Pour restaurer dans l'environnement de staging pour des tests :

```bash
./scripts/backup/restore_backup.sh --type db --latest --namespace retreatnbe-staging
```

### 2.3 Restauration Point-in-Time

La fonctionnalité Point-in-Time Recovery (PITR) permet de restaurer la base de données à un moment précis dans le passé, utile en cas de corruption de données ou d'erreur utilisateur.

```bash
./scripts/backup/restore_backup.sh --type db --point-in-time "2023-10-15 14:30:00"
```

Cette commande :
1. Trouve le backup complet le plus récent avant le point dans le temps spécifié
2. Restaure ce backup
3. Applique les journaux de transactions (WAL) jusqu'au moment spécifié

**Note** : Le PITR nécessite que l'archivage WAL soit correctement configuré dans PostgreSQL.

## 3. Procédures de Maintenance

### 3.1 Vérification de l'Intégrité des Backups

Il est recommandé de vérifier régulièrement l'intégrité des backups :

```bash
# Vérifier l'intégrité d'un backup de base de données
gzip -t /var/backups/retreatnbe/database/daily/db_YYYYMMDD_HHMMSS.dump.gz

# Lister le contenu d'un backup pour vérification
gzip -dc /var/backups/retreatnbe/database/daily/db_YYYYMMDD_HHMMSS.dump.gz | pg_restore --list
```

### 3.2 Rotation des Logs

Les logs de backup sont automatiquement nettoyés après une période définie (par défaut 28 jours). Pour nettoyer manuellement les anciens logs :

```bash
find /var/backups/retreatnbe/logs -name "*.log" -type f -mtime +28 -delete
```

### 3.3 Tests de Restauration

Il est fortement recommandé de tester régulièrement les procédures de restauration :

1. Planifier des tests de restauration mensuels dans l'environnement de staging
2. Documenter les résultats de chaque test et les problèmes éventuels
3. Simuler différents scénarios de restauration (corruption de données, perte complète, etc.)

## 4. Mises à Jour et Gestion des Dépendances

### 4.1 Mise à Jour des Scripts

Les scripts sont maintenus dans le dépôt Git principal. Pour mettre à jour les scripts :

1. Cloner le dépôt : `git clone https://github.com/retreatnbe/platform.git`
2. Modifier les scripts dans le répertoire `scripts/backup/`
3. Tester les modifications dans l'environnement de développement
4. Soumettre un pull request pour review
5. Après approbation, déployer via la pipeline CI/CD

### 4.2 Dépendances Requises

Les scripts dépendent des outils suivants :

| Outil | Version minimale | Utilisation |
|-------|------------------|-------------|
| `bash` | 4.0+ | Interpréteur de script |
| `pg_dump` / `pg_restore` | 14.0+ | Sauvegarde/restauration PostgreSQL |
| `kubectl` | 1.22+ | Interaction avec Kubernetes |
| `aws` CLI | 2.0+ | Interaction avec AWS S3 |
| `gzip` | 1.6+ | Compression des backups |
| `tar` | 1.30+ | Archivage des configurations |

### 4.3 Compatibilité des Versions

Les scripts ont été testés avec les versions suivantes :

- PostgreSQL 14.x
- Kubernetes 1.24+
- AWS CLI 2.4+
- Bash 5.0+

En cas de mise à jour majeure de ces composants, des tests supplémentaires doivent être effectués pour s'assurer de la compatibilité.

---

Document préparé par l'équipe DevOps de Retreat And Be.  
Dernière mise à jour : Octobre 2023 