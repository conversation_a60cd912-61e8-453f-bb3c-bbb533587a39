# Guide global de mise à jour des dépendances

Ce document fournit un guide complet pour la standardisation et la mise à jour des dépendances à travers l'ensemble des microservices du projet.

## Objectifs

1. **Harmoniser les versions** des bibliothèques communes entre les microservices
2. **Supprimer les dépendances obsolètes** et dépréciées
3. **Mettre à jour vers les dernières versions stables** pour bénéficier des améliorations de sécurité et de performance
4. **Standardiser les outils de développement** (ESLint, Prettier, Jest, etc.)
5. **Améliorer la télémétrie** en utilisant les dernières versions d'OpenTelemetry

## Versions standardisées recommandées

### Frontend

| Bibliothèque | Version standardisée | Notes |
|--------------|---------------------|-------|
| React | 18.3.1 | Dernière version stable |
| React DOM | 18.3.1 | Doit correspondre à la version de React |
| React Router | 6.23.3 | Dernière version stable |
| TypeScript | 5.4.3 | Dernière version stable |
| Vite | 5.2.3 | Dernière version stable |
| MUI | 5.16.3 | Dernière version stable |
| Jest | 29.7.0 | Dernière version stable |
| ESLint | 9.1.1 (front) / 8.57.1 (back) | Dernière version stable (9.x pour front, 8.x pour back) |
| Prettier | 3.2.5 | Dernière version stable |

### Backend

| Bibliothèque | Version standardisée | Notes |
|--------------|---------------------|-------|
| NestJS | 11.0.10 | Dernière version stable |
| Express | 4.19.1 | Dernière version stable |
| TypeScript | 5.4.3 | Dernière version stable |
| Prisma | 5.12.0 | Dernière version stable |
| Jest | 29.7.0 | Dernière version stable |
| Winston | 3.11.0 | Dernière version stable |
| Node.js | >=18.0.0 | Minimum recommandé dans tous les projets |

### OpenTelemetry

| Bibliothèque | Version standardisée | Notes |
|--------------|---------------------|-------|
| @opentelemetry/api | 1.7.0 | Version harmonisée à utiliser partout |
| @opentelemetry/sdk-metrics | 1.30.1 | Pour les métriques |
| @opentelemetry/resources | 1.19.0 | Pour la configuration des ressources |
| @opentelemetry/semantic-conventions | 1.19.0 | Pour les conventions sémantiques |
| @opentelemetry/sdk-node | 0.49.0 | Pour l'instrumentation Node.js |
| @opentelemetry/exporter-trace-otlp-http | 0.49.0 | Remplace les exportateurs dépréciés |
| prom-client | 15.1.3 | Pour l'exportation Prometheus |

## Dépendances obsolètes à supprimer ou remplacer

| Dépendance | Raison | Remplacement recommandé |
|------------|--------|-------------------------|
| opentelemetry-node | Déprécié | @opentelemetry/sdk-node |
| @opentelemetry/exporter-prometheus | Déprécié | @opentelemetry/sdk-metrics |
| @opentelemetry/metrics | Déprécié | @opentelemetry/sdk-metrics |
| @opentelemetry/exporter-jaeger | Déprécié | @opentelemetry/exporter-trace-otlp-http |
| jaeger-client | Déprécié | Utiliser OpenTelemetry |
| opentracing | API dépréciée | Utiliser OpenTelemetry API |
| React v17 dans les backends | Non pertinent | Supprimer des dépendances backend |

## Procédure de mise à jour

Pour chaque microservice, suivez cette procédure :

1. **Analyse préliminaire** :
   - Lisez le `package.json` et identifiez les dépendances obsolètes
   - Identifiez les dépendances à mettre à jour
   - Vérifiez les incompatibilités potentielles

2. **Préparation** :
   - Créez une branche dédiée à la mise à jour des dépendances
   - Sauvegardez le `package.json` actuel
   - Assurez-vous que tous les tests passent avant de commencer

3. **Mise à jour** :
   - Mettez à jour les versions dans `package.json` selon les recommandations
   - Supprimez le `package-lock.json` ou `yarn.lock`
   - Exécutez `npm install` ou `yarn install`

4. **Adaptation du code** :
   - Mettez à jour les importations si nécessaire
   - Adaptez le code aux nouvelles API si nécessaire
   - Corrigez les erreurs signalées par le linter

5. **Tests** :
   - Exécutez la suite de tests complète
   - Corrigez les tests qui échouent
   - Vérifiez que la télémétrie fonctionne correctement

6. **Finalisation** :
   - Faites un audit de sécurité : `npm audit --production`
   - Documentez les changements significatifs
   - Créez une pull request pour review

## Ordonnancement des mises à jour

Pour minimiser les risques, mettez à jour les microservices dans cet ordre :

1. **Bibliothèques partagées** et **utilitaires communs**
2. **Services backend** avec peu de dépendances
3. **Services backend** avec de nombreuses dépendances
4. **Frontend** et interfaces utilisateur
5. **Services d'API Gateway** et points d'entrée

## Scripts utilitaires

Utilisez ces scripts pour faciliter la mise à jour :

### Script de vérification des versions

Créez un script `check-versions.js` dans le dossier `scripts` pour vérifier l'harmonisation des versions :

```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Définir les versions standardisées
const standardVersions = {
  react: '18.3.1',
  typescript: '5.4.3',
  nestjs: '11.0.10',
  jest: '29.7.0',
  // Ajoutez d'autres dépendances
};

// Trouver tous les package.json
const packageJsons = glob.sync('**/package.json', {
  ignore: ['**/node_modules/**', '**/dist/**']
});

// Vérifier les versions
packageJsons.forEach(file => {
  const packageJson = JSON.parse(fs.readFileSync(file, 'utf8'));
  const allDeps = { 
    ...packageJson.dependencies, 
    ...packageJson.devDependencies 
  };
  
  console.log(`\nVérifiant ${file}:`);
  
  Object.entries(standardVersions).forEach(([pkgName, standardVersion]) => {
    // Vérifier les packages NestJS (qui commencent par @nestjs/)
    if (pkgName === 'nestjs') {
      Object.keys(allDeps).forEach(dep => {
        if (dep.startsWith('@nestjs/')) {
          const version = allDeps[dep].replace('^', '').replace('~', '');
          if (version !== standardVersion) {
            console.log(`  ⚠️ ${dep}: ${version} (standard: ${standardVersion})`);
          } else {
            console.log(`  ✅ ${dep}: ${version}`);
          }
        }
      });
    } 
    // Vérifier les autres packages
    else if (allDeps[pkgName]) {
      const version = allDeps[pkgName].replace('^', '').replace('~', '');
      if (version !== standardVersion) {
        console.log(`  ⚠️ ${pkgName}: ${version} (standard: ${standardVersion})`);
      } else {
        console.log(`  ✅ ${pkgName}: ${version}`);
      }
    }
  });
});
```

### Script d'audit de sécurité global

Créez un script `audit-all.js` dans le dossier `scripts` :

```javascript
#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Trouver tous les package.json
const packageJsons = glob.sync('**/package.json', {
  ignore: ['**/node_modules/**', '**/dist/**']
});

// Exécuter npm audit pour chaque projet
packageJsons.forEach(file => {
  const dir = path.dirname(file);
  console.log(`\n=== Auditing ${dir} ===\n`);
  
  try {
    const output = execSync('npm audit --production', { 
      cwd: dir, 
      stdio: ['ignore', 'pipe', 'pipe'] 
    }).toString();
    
    console.log(output);
  } catch (error) {
    console.log(error.stdout.toString());
    console.error(`Vulnerabilities found in ${dir}`);
  }
});
```

## Vérifications post-mise à jour

Pour chaque microservice mis à jour, vérifiez :

1. **Tests** : Tous les tests passent
2. **Sécurité** : Pas de vulnérabilités critiques
3. **Performance** : Pas de régression de performance
4. **Télémétrie** : Les traces et métriques sont correctement collectées
5. **Intégration** : Le service s'intègre correctement avec les autres services

## Considérations spécifiques par type de service

### Services frontend (React)

- Testez sur différents navigateurs
- Vérifiez la compatibilité des hooks React
- Testez les performances avec Lighthouse

### Services NestJS

- Vérifiez la compatibilité des décorateurs
- Assurez-vous que les providers sont correctement injectés
- Testez les intercepteurs et les guards

### Services utilisant OpenTelemetry

- Vérifiez que les traces sont exportées correctement
- Assurez-vous que les métriques sont disponibles dans Prometheus
- Testez les tableaux de bord Grafana 