# Guide des intégrations et fonctionnalités avancées

Ce guide détaille les nouvelles fonctionnalités et intégrations ajoutées au système Retreat & Be, notamment les intégrations de calendrier, de cartographie, les notifications push et l'analyse de données.

## Table des matières

1. [Intégrations de calendrier](#intégrations-de-calendrier)
2. [Intégrations de cartographie](#intégrations-de-cartographie)
3. [Système de notifications avancé](#système-de-notifications-avancé)
4. [Analyse de données](#analyse-de-données)
5. [Exemples d'utilisation](#exemples-dutilisation)

## Intégrations de calendrier

Les intégrations de calendrier permettent aux utilisateurs de gérer facilement les événements liés aux retraites dans leurs calendriers préférés.

### Google Calendar

L'intégration avec Google Calendar permet de synchroniser les retraites avec le calendrier Google des utilisateurs.

#### Configuration

Pour configurer l'intégration avec Google Calendar, vous devez:

1. Créer un projet dans la [Console Google Cloud](https://console.cloud.google.com/)
2. Activer l'API Google Calendar
3. Créer des identifiants OAuth 2.0
4. Télécharger le fichier de credentials JSON
5. Placer le fichier à la racine du projet et mettre à jour le fichier `.env`:

```
GOOGLE_CREDENTIALS_FILE=credentials.json
GOOGLE_TOKEN_FILE=token.json
```

#### Fonctionnalités

- Création d'événements pour les retraites
- Mise à jour des événements existants
- Suppression d'événements
- Récupération des événements d'un calendrier
- Génération de liens vers les événements

#### Exemple d'utilisation via l'API

```bash
# Créer un événement dans Google Calendar pour une retraite
curl -X POST "http://localhost:8000/api/integrations/calendar/event" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "retreat_id": "retreat_123",
    "calendar_id": "primary"
  }'
```

### iCalendar

Le système peut également générer des fichiers iCalendar (.ics) qui peuvent être importés dans n'importe quel logiciel de calendrier.

#### Configuration

L'intégration iCalendar ne nécessite pas de configuration particulière, mais vous pouvez spécifier le répertoire de sortie des fichiers iCalendar dans le fichier `.env`:

```
ICAL_OUTPUT_DIR=ical
```

#### Fonctionnalités

- Génération de fichiers iCalendar pour les retraites
- Envoi de fichiers iCalendar par email
- Téléchargement direct des fichiers iCalendar

#### Exemple d'utilisation via l'API

```bash
# Générer un fichier iCalendar pour une retraite
curl -X GET "http://localhost:8000/api/integrations/calendar/ical/retreat_123" \
  -H "Authorization: Bearer <token>"
```

## Intégrations de cartographie

Les intégrations de cartographie permettent d'améliorer l'expérience utilisateur en fournissant des informations géographiques précises.

### Google Maps

L'intégration avec Google Maps permet de géolocaliser les retraites et de fournir des informations sur les lieux à proximité.

#### Configuration

Pour configurer l'intégration avec Google Maps, vous devez:

1. Créer un projet dans la [Console Google Cloud](https://console.cloud.google.com/) (si ce n'est pas déjà fait)
2. Activer l'API Google Maps
3. Créer une clé API
4. Mettre à jour le fichier `.env`:

```
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
```

#### Fonctionnalités

- Géocodage d'adresses (convertir une adresse en coordonnées)
- Géocodage inverse (convertir des coordonnées en adresse)
- Calcul de distances et temps de trajet entre deux points
- Recherche de lieux à proximité d'une adresse
- Génération de cartes statiques
- Génération de liens vers des itinéraires

#### Exemple d'utilisation via l'API

```bash
# Géocoder une adresse
curl -X GET "http://localhost:8000/api/integrations/maps/geocode?address=Paris,France" \
  -H "Authorization: Bearer <token>"

# Rechercher des lieux à proximité
curl -X GET "http://localhost:8000/api/integrations/maps/places?location=Paris,France&radius=1000&type=restaurant" \
  -H "Authorization: Bearer <token>"
```

#### Intégration dans l'interface utilisateur

Pour intégrer une carte Google Maps dans l'interface utilisateur, utilisez le code suivant:

```html
<div id="map" style="width: 100%; height: 400px;"></div>
<script>
  function initMap() {
    const location = { lat: 48.8566, lng: 2.3522 }; // Paris
    const map = new google.maps.Map(document.getElementById("map"), {
      zoom: 14,
      center: location,
    });
    const marker = new google.maps.Marker({
      position: location,
      map: map,
    });
  }
</script>
<script async defer
  src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap">
</script>
```

## Système de notifications avancé

Le système de notifications permet d'envoyer des notifications aux utilisateurs par différents canaux: email, SMS et notifications push.

### Notifications push

Les notifications push permettent d'envoyer des alertes en temps réel aux utilisateurs sur leurs appareils mobiles.

#### Configuration

Pour configurer les notifications push, vous devez:

1. Créer un projet dans la [Console Firebase](https://console.firebase.google.com/)
2. Ajouter votre application (iOS et/ou Android)
3. Télécharger le fichier de credentials Firebase
4. Placer le fichier à la racine du projet et mettre à jour le fichier `.env`:

```
FIREBASE_CREDENTIALS_FILE=firebase-credentials.json
```

#### Fonctionnalités

- Envoi de notifications push aux appareils mobiles
- Support pour iOS et Android via Firebase Cloud Messaging
- Gestion des abonnements aux sujets
- Envoi de notifications à plusieurs appareils simultanément

#### Exemple d'utilisation via l'API

```bash
# Envoyer une notification push
curl -X POST "http://localhost:8000/api/notifications/push" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "fcm_token_123",
    "title": "Nouvelle retraite disponible",
    "body": "Une nouvelle retraite de yoga est disponible en Provence",
    "data": {
      "retreat_id": "retreat_123",
      "type": "retreat_created"
    }
  }'

# Envoyer une notification push à un sujet
curl -X POST "http://localhost:8000/api/notifications/push-topic" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "yoga_retreats",
    "title": "Nouvelle retraite de yoga",
    "body": "Une nouvelle retraite de yoga est disponible en Provence",
    "data": {
      "retreat_id": "retreat_123",
      "type": "retreat_created"
    }
  }'
```

#### Intégration dans l'application mobile

Pour intégrer les notifications push dans une application mobile React Native, utilisez le code suivant:

```javascript
import messaging from '@react-native-firebase/messaging';

// Demander la permission pour les notifications
async function requestUserPermission() {
  const authStatus = await messaging().requestPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL;

  if (enabled) {
    console.log('Authorization status:', authStatus);
  }
}

// Obtenir le token FCM
async function getToken() {
  const token = await messaging().getToken();
  console.log('FCM Token:', token);
  // Envoyer le token au serveur
  await sendTokenToServer(token);
}

// Gérer les notifications en premier plan
messaging().onMessage(async remoteMessage => {
  console.log('Notification reçue en premier plan!', remoteMessage);
});

// Gérer les notifications en arrière-plan
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Notification reçue en arrière-plan!', remoteMessage);
});

// S'abonner à un sujet
messaging().subscribeToTopic('yoga_retreats');

// Se désabonner d'un sujet
messaging().unsubscribeFromTopic('yoga_retreats');
```

## Analyse de données

L'analyse de données permet de suivre l'utilisation de la plateforme et de comprendre le comportement des utilisateurs.

### Google Analytics

L'intégration avec Google Analytics permet de suivre les événements, les vues de page et les conversions.

#### Configuration

Pour configurer l'intégration avec Google Analytics, vous devez:

1. Créer un compte Google Analytics
2. Créer une propriété Google Analytics 4
3. Obtenir l'ID de mesure et le secret API
4. Mettre à jour le fichier `.env`:

```
GA_MEASUREMENT_ID=your-ga-measurement-id
GA_API_SECRET=your-ga-api-secret
```

#### Fonctionnalités

- Suivi des événements (réservations, paiements, etc.)
- Suivi des vues de page
- Suivi des conversions
- Récupération des statistiques d'utilisation

#### Exemple d'utilisation via l'API

```bash
# Enregistrer un événement
curl -X POST "http://localhost:8000/api/analytics/events" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "event_name": "booking_completed",
    "event_data": {
      "retreat_id": "retreat_123",
      "amount": 1200
    }
  }'

# Récupérer les conversions
curl -X GET "http://localhost:8000/api/analytics/conversions?start_date=2023-01-01T00:00:00&end_date=2023-12-31T23:59:59" \
  -H "Authorization: Bearer <token>"
```

#### Intégration dans l'interface utilisateur

Pour intégrer Google Analytics dans l'interface utilisateur, utilisez le code suivant:

```html
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=YOUR_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'YOUR_MEASUREMENT_ID');
  
  // Exemple d'événement personnalisé
  function trackBooking(retreatId, amount) {
    gtag('event', 'booking_completed', {
      'retreat_id': retreatId,
      'amount': amount
    });
  }
</script>
```

## Exemples d'utilisation

### Scénario 1: Création d'une retraite avec intégration de calendrier et cartographie

```javascript
// 1. Créer une retraite
const retreatResponse = await fetch('http://localhost:8000/api/retreats', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: 'Retraite de yoga en Provence',
    description: 'Une semaine de yoga et de méditation en Provence',
    retreat_type: 'yoga',
    status: 'published',
    start_date: '2023-07-15',
    end_date: '2023-07-22',
    location: {
      country: 'France',
      region: 'Provence',
      city: 'Aix-en-Provence'
    },
    capacity: 10,
    organizer_id: 'partner_123'
  })
});

const retreat = await retreatResponse.json();

// 2. Géocoder l'adresse
const geocodeResponse = await fetch(`http://localhost:8000/api/integrations/maps/geocode?address=Aix-en-Provence,France`, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

const location = await geocodeResponse.json();

// 3. Mettre à jour la retraite avec les coordonnées
await fetch(`http://localhost:8000/api/retreats/${retreat.id}`, {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    location: {
      ...retreat.location,
      latitude: location.latitude,
      longitude: location.longitude,
      formatted_address: location.formatted_address
    }
  })
});

// 4. Créer un événement dans Google Calendar
await fetch('http://localhost:8000/api/integrations/calendar/event', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    retreat_id: retreat.id
  })
});

// 5. Envoyer une notification aux utilisateurs abonnés
await fetch('http://localhost:8000/api/notifications/push-topic', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    topic: 'yoga_retreats',
    title: 'Nouvelle retraite de yoga',
    body: `Une nouvelle retraite de yoga est disponible à ${location.formatted_address}`,
    data: {
      retreat_id: retreat.id,
      type: 'retreat_created'
    }
  })
});

// 6. Enregistrer l'événement dans Google Analytics
await fetch('http://localhost:8000/api/analytics/events', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    event_name: 'retreat_created',
    event_data: {
      retreat_id: retreat.id,
      retreat_type: 'yoga',
      location: 'Provence, France'
    }
  })
});
```

### Scénario 2: Réservation d'une retraite avec notification et analyse

```javascript
// 1. Créer une réservation
const bookingResponse = await fetch('http://localhost:8000/api/bookings', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    client_id: 'client_123',
    retreat_id: 'retreat_123',
    status: 'pending',
    booking_date: new Date().toISOString().split('T')[0],
    participants: [
      {
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>'
      }
    ],
    accommodation_type: 'single_room',
    total_price: 1200,
    currency: 'EUR',
    payment_status: 'pending'
  })
});

const booking = await bookingResponse.json();

// 2. Créer une intention de paiement
const paymentIntentResponse = await fetch('http://localhost:8000/api/payments/intent', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    booking_id: booking.id,
    amount: booking.total_price,
    payment_type: 'full_payment'
  })
});

const paymentIntent = await paymentIntentResponse.json();

// 3. Traiter le paiement (simulé)
const paymentResponse = await fetch('http://localhost:8000/api/payments/process', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    booking_id: booking.id,
    amount: booking.total_price,
    payment_type: 'full_payment',
    payment_method: 'credit_card'
  })
});

const payment = await paymentResponse.json();

// 4. Envoyer une notification au client
const clientResponse = await fetch(`http://localhost:8000/api/clients/${booking.client_id}`, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

const client = await clientResponse.json();

await fetch('http://localhost:8000/api/notifications/send', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    recipient: {
      id: client.id,
      email: client.email,
      phone: client.phone,
      device_token: client.device_token
    },
    notification_type: 'booking_created',
    data: {
      recipient_name: `${client.first_name} ${client.last_name}`,
      retreat_title: 'Retraite de yoga en Provence',
      start_date: '2023-07-15',
      total_price: booking.total_price,
      currency: booking.currency
    },
    channels: ['email', 'sms', 'push']
  })
});

// 5. Générer un fichier iCalendar et l'envoyer au client
const icalResponse = await fetch(`http://localhost:8000/api/integrations/calendar/ical/${booking.retreat_id}`, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

const ical = await icalResponse.json();

// 6. Enregistrer la conversion dans Google Analytics
await fetch('http://localhost:8000/api/analytics/conversions', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    conversion_name: 'booking_paid',
    conversion_value: booking.total_price
  })
});
```

Ces exemples montrent comment utiliser les différentes intégrations ensemble pour créer des fonctionnalités complètes et améliorer l'expérience utilisateur.
