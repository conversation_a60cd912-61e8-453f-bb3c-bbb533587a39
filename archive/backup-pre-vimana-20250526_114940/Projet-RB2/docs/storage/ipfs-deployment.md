# Guide de Déploiement IPFS

Ce guide détaille les étapes de déploiement et de configuration du système de stockage IPFS pour Retreat And Be.

## Prérequis

### Système
- Linux (Ubuntu 20.04 LTS recommandé)
- 4 CPU cores minimum
- 8GB RAM minimum
- 100GB SSD minimum

### Logiciels
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 16+
- Go 1.17+

## Installation

### 1. IPFS Node

```bash
# Installation de go-ipfs
wget https://dist.ipfs.io/go-ipfs/v0.12.0/go-ipfs_v0.12.0_linux-amd64.tar.gz
tar -xvzf go-ipfs_v0.12.0_linux-amd64.tar.gz
cd go-ipfs
sudo bash install.sh

# Initialisation
ipfs init
```

### 2. IPFS Cluster

```bash
# Installation
wget https://dist.ipfs.io/ipfs-cluster-service/v1.0.0/ipfs-cluster-service_v1.0.0_linux-amd64.tar.gz
tar -xvzf ipfs-cluster-service_v1.0.0_linux-amd64.tar.gz
sudo mv ipfs-cluster-service/ipfs-cluster-service /usr/local/bin/

# Configuration
ipfs-cluster-service init
```

### 3. API Service

```bash
# Installation des dépendances
npm install ipfs-http-client
npm install @ipfs/cluster-api
npm install aes-256-gcm
```

## Configuration

### 1. IPFS Node

```bash
# Configuration réseau
ipfs config Addresses.API /ip4/0.0.0.0/tcp/5001
ipfs config Addresses.Gateway /ip4/0.0.0.0/tcp/8080
ipfs config --json API.HTTPHeaders.Access-Control-Allow-Origin '["*"]'

# Configuration swarm
ipfs config --json Swarm.ConnMgr.HighWater 2000
ipfs config --json Swarm.ConnMgr.LowWater 1500
```

### 2. IPFS Cluster

```yaml
# ~/.ipfs-cluster/service.json
{
  "cluster": {
    "secret": "${CLUSTER_SECRET}",
    "listen_multiaddress": "/ip4/0.0.0.0/tcp/9096",
    "state_sync_interval": "1m",
    "ipfs_sync_interval": "2m",
    "replication_factor_min": 3,
    "replication_factor_max": 5,
    "monitor_ping_interval": "15s"
  },
  "consensus": {
    "crdt": {
      "cluster_name": "randb-storage",
      "trusted_peers": [
        "12D3KooWXXXX...Peer1",
        "12D3KooWXXXX...Peer2",
        "12D3KooWXXXX...Peer3"
      ]
    }
  },
  "ipfs_connector": {
    "ipfshttp": {
      "node_multiaddress": "/ip4/127.0.0.1/tcp/5001"
    }
  }
}
```

### 3. API Service

```typescript
// config/storage.ts
export const storageConfig = {
  ipfs: {
    host: 'localhost',
    port: 5001,
    protocol: 'http'
  },
  cluster: {
    host: 'localhost',
    port: 9096,
    protocol: 'http'
  },
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 12
  }
};
```

## Déploiement

### 1. Docker Compose

```yaml
version: '3.8'

services:
  ipfs:
    image: ipfs/go-ipfs:v0.12.0
    ports:
      - "4001:4001"
      - "5001:5001"
      - "8080:8080"
    volumes:
      - ipfs_data:/data/ipfs
      - ipfs_export:/export
    environment:
      - IPFS_PROFILE=server
      - IPFS_PATH=/data/ipfs
    restart: always

  ipfs-cluster:
    image: ipfs/ipfs-cluster:v1.0.0
    depends_on:
      - ipfs
    ports:
      - "9096:9096"
    volumes:
      - cluster_data:/data/ipfs-cluster
    environment:
      - CLUSTER_SECRET=${CLUSTER_SECRET}
      - CLUSTER_IPFSHTTP_NODEMULTIADDRESS=/ip4/ipfs/tcp/5001
    restart: always

  api:
    build: 
      context: ./api
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - IPFS_HOST=ipfs
      - IPFS_PORT=5001
      - CLUSTER_HOST=ipfs-cluster
      - CLUSTER_PORT=9096
    depends_on:
      - ipfs
      - ipfs-cluster
    restart: always

volumes:
  ipfs_data:
  ipfs_export:
  cluster_data:
```

### 2. Kubernetes

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: ipfs-node
spec:
  serviceName: ipfs
  replicas: 3
  selector:
    matchLabels:
      app: ipfs
  template:
    metadata:
      labels:
        app: ipfs
    spec:
      containers:
      - name: ipfs
        image: ipfs/go-ipfs:v0.12.0
        ports:
        - containerPort: 4001
        - containerPort: 5001
        - containerPort: 8080
        volumeMounts:
        - name: ipfs-storage
          mountPath: /data/ipfs
  volumeClaimTemplates:
  - metadata:
      name: ipfs-storage
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 100Gi
```

## Monitoring

### 1. Prometheus Metrics

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'ipfs'
    static_configs:
      - targets: ['localhost:5001']
    metrics_path: '/debug/metrics/prometheus'
```

### 2. Grafana Dashboard

```json
{
  "dashboard": {
    "panels": [
      {
        "title": "IPFS Network Traffic",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(ipfs_network_traffic_total[5m])"
          }
        ]
      },
      {
        "title": "Storage Usage",
        "type": "gauge",
        "targets": [
          {
            "expr": "ipfs_repo_size_bytes"
          }
        ]
      }
    ]
  }
}
```

## Sécurité

### 1. Firewall

```bash
# Configuration UFW
sudo ufw allow 4001/tcp  # IPFS swarm
sudo ufw allow 5001/tcp  # IPFS API
sudo ufw allow 8080/tcp  # IPFS Gateway
sudo ufw allow 9096/tcp  # IPFS Cluster
```

### 2. SSL/TLS

```nginx
# nginx.conf
server {
    listen 443 ssl;
    server_name api.storage.retreatandbe.com;

    ssl_certificate /etc/letsencrypt/live/api.storage.retreatandbe.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.storage.retreatandbe.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Maintenance

### 1. Backup

```bash
# Backup IPFS data
ipfs-cluster-ctl pin ls | while read -r pin; do
    ipfs get "$pin" -o /backup/ipfs/
done

# Backup configurations
tar -czf /backup/config/ipfs-$(date +%Y%m%d).tar.gz ~/.ipfs/
tar -czf /backup/config/cluster-$(date +%Y%m%d).tar.gz ~/.ipfs-cluster/
```

### 2. Monitoring

```bash
# Check node status
ipfs-cluster-ctl status

# Check peers
ipfs-cluster-ctl peers ls

# Check pinned items
ipfs-cluster-ctl pin ls

# Check metrics
curl localhost:5001/debug/metrics/prometheus
```

## Troubleshooting

### 1. Problèmes Courants

```bash
# Réinitialisation du daemon
ipfs daemon --init

# Nettoyage du repo
ipfs repo gc

# Vérification des connexions
ipfs swarm peers
```

### 2. Logs

```bash
# IPFS logs
journalctl -u ipfs -f

# Cluster logs
journalctl -u ipfs-cluster -f

# API logs
docker logs api-service
```

## Mise à Jour

### 1. IPFS Node

```bash
# Update IPFS
ipfs shutdown
wget https://dist.ipfs.io/go-ipfs/vX.Y.Z/go-ipfs_vX.Y.Z_linux-amd64.tar.gz
tar -xvzf go-ipfs_vX.Y.Z_linux-amd64.tar.gz
cd go-ipfs
sudo bash install.sh
ipfs daemon
```

### 2. IPFS Cluster

```bash
# Update Cluster
ipfs-cluster-service shutdown
wget https://dist.ipfs.io/ipfs-cluster-service/vX.Y.Z/ipfs-cluster-service_vX.Y.Z_linux-amd64.tar.gz
tar -xvzf ipfs-cluster-service_vX.Y.Z_linux-amd64.tar.gz
sudo mv ipfs-cluster-service/ipfs-cluster-service /usr/local/bin/
ipfs-cluster-service daemon
```

## Support

Pour toute assistance :
- Documentation : https://docs.retreatandbe.com/storage
- Support : <EMAIL>
- Urgence : +1 (555) 123-4567
