# Guide de Déploiement

Ce répertoire contient la documentation complète pour le déploiement de Retreat And Be dans différents environnements.

## Structure

```
├── environments/
│   ├── development.md
│   ├── staging.md
│   └── production.md
├── kubernetes/
│   ├── setup.md
│   ├── monitoring.md
│   └── maintenance.md
├── ci-cd/
│   ├── github-actions.md
│   └── pipelines.md
└── infrastructure/
    ├── aws.md
    └── terraform.md
```

## Guides de Déploiement

1. [Configuration des Environnements](./environments/README.md)
2. [Configuration Kubernetes](./kubernetes/README.md)
3. [Pipelines CI/CD](./ci-cd/README.md)
4. [Infrastructure as Code](./infrastructure/README.md)

## Prérequis

- Docker v20+
- Kubernetes v1.24+
- Terraform v1.0+
- AWS CLI v2
- GitHub CLI

## Quick Start

1. **Cloner le repository**
```bash
git clone https://github.com/LUCDIZA/Retreat-And-Be-V2.git
cd Retreat-And-Be-V2
```

2. **Configurer les variables d'environnement**
```bash
cp .env.example .env
# Éditer .env avec vos configurations
```

3. **Déployer l'infrastructure**
```bash
cd terraform
terraform init
terraform plan
terraform apply
```

4. **Déployer l'application**
```bash
kubectl apply -f kubernetes/
```

## Environnements

### Development
- URL: dev.retreatandbe.com
- Cluster: EKS dev-cluster
- Namespace: development

### Staging
- URL: staging.retreatandbe.com
- Cluster: EKS staging-cluster
- Namespace: staging

### Production
- URL: retreatandbe.com
- Cluster: EKS prod-cluster
- Namespace: production

## Monitoring

- Prometheus: metrics.retreatandbe.com
- Grafana: grafana.retreatandbe.com
- AlertManager: alerts.retreatandbe.com

## Sécurité

- Certificats SSL via Let's Encrypt
- Secrets gérés via AWS Secrets Manager
- Network Policies Kubernetes
- WAF configuré sur le ALB

## Maintenance

### Backups
- Base de données: Snapshots quotidiens
- Fichiers IPFS: Réplication sur plusieurs nœuds
- Configuration: Stockée dans Git

### Mises à jour
- Images Docker: Mises à jour automatiques via Renovate
- Kubernetes: Updates planifiés mensuellement
- Dépendances: Scan hebdomadaire

## Support

Pour toute question ou problème :
- Email: <EMAIL>
- Slack: #devops-support
