# Feuille de Route pour l'Implémentation des Routes - Retreat And Be

Cette feuille de route définit les étapes nécessaires pour implémenter toutes les routes requises afin que le frontend soit pleinement fonctionnel. Elle est organisée par priorité et par section, avec des indications sur l'état d'avancement.

## Table des Matières

1. [Priorités d'Implémentation](#priorités-dimplémentation)
2. [Phase 1 : Routes Essentielles](#phase-1--routes-essentielles)
3. [Phase 2 : Routes Importantes](#phase-2--routes-importantes)
4. [Phase 3 : Routes Complémentaires](#phase-3--routes-complémentaires)
5. [Phase 4 : Routes Avancées](#phase-4--routes-avancées)
6. [Suivi de Progression](#suivi-de-progression)

## Priorités d'Implémentation

Les routes ont été classées selon les priorités suivantes :

- **Critique** : Routes essentielles au fonctionnement de base de l'application
- **Élevée** : Routes importantes pour l'expérience utilisateur principale
- **Moyenne** : Routes qui ajoutent des fonctionnalités significatives
- **Basse** : Routes qui complètent l'expérience mais ne sont pas essentielles

## Phase 1 : Routes Essentielles

### Routes Publiques Essentielles

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/` | Page d'accueil principale | `HomePage` | Critique | ✅ |
| `/login` | Connexion | `LoginPage` | Critique | ✅ |
| `/register` | Inscription | `RegisterPage` | Critique | ✅ |
| `/retreats` | Liste des retraites | `RetreatsPage` | Critique | ✅ |
| `/retreats/:id` | Détail d'une retraite | `RetreatPage` | Critique | ✅ |
| `/404` | Page non trouvée | `Error404Page` | Critique | ✅ |
| `/500` | Erreur serveur | `Error500Page` | Critique | ✅ |

### Routes Client Essentielles

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/client/dashboard` | Tableau de bord client | `ClientDashboard` | Critique | ✅ |
| `/client/bookings` | Liste des réservations client | `ClientBookingsList` | Critique | ✅ |
| `/client/bookings/:id` | Détail d'une réservation client | `ClientBookingDetail` | Critique | ✅ |
| `/profile` | Profil utilisateur | `ProfilePage` | Critique | ✅ |
| `/settings` | Paramètres utilisateur | `SettingsPage` | Critique | ✅ |

### Routes Professionnel Essentielles

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/professional/dashboard` | Tableau de bord professionnel | `ProfessionalDashboard` | Critique | ✅ |
| `/professional/retreats` | Liste des retraites du professionnel | `ProfessionalRetreatsList` | Critique | ✅ |
| `/professional/retreats/create` | Création d'une retraite | `ProfessionalRetreatCreate` | Critique | ✅ |
| `/professional/retreats/:id` | Détail d'une retraite du professionnel | `ProfessionalRetreatDetail` | Critique | ✅ |
| `/professional/bookings` | Liste des réservations du professionnel | `ProfessionalBookingsList` | Critique | ✅ |

### Routes Admin Essentielles

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/admin/dashboard` | Tableau de bord admin | `AdminDashboardPage` | Critique | ✅ |
| `/admin/users` | Gestion des utilisateurs | `AdminUsersListPage` | Critique | ✅ |
| `/admin/retreats` | Liste des retraites | `AdminRetreatsListPage` | Critique | ✅ |
| `/admin/bookings` | Liste des réservations | `AdminBookingsListPage` | Critique | ✅ |

## Phase 2 : Routes Importantes

### Routes Publiques Importantes

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/about` | À propos de Retreat And Be | `AboutPage` | Élevée | ✅ |
| `/contact` | Formulaire de contact | `ContactPage` | Élevée | ✅ |
| `/faq` | Questions fréquemment posées | `FAQPage` | Élevée | ✅ |
| `/explore-retreats` | Explorer les retraites | `ExploreRetreatsPage` | Élevée | ✅ |
| `/retreat-finder` | Formulaire de recherche de retraite | `RetreatFinderForm` | Élevée | ✅ |
| `/search-results` | Résultats de recherche | `SearchResultsPage` | Élevée | ✅ |
| `/forgot-password` | Mot de passe oublié | `ForgotPasswordPage` | Élevée | ✅ |
| `/reset-password/:token` | Réinitialisation du mot de passe | `ResetPasswordPage` | Élevée | ✅ |

### Routes Client Importantes

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/client/profile/edit` | Édition du profil client | `ClientProfileEdit` | Élevée | ✅ |
| `/booking/:id` | Page de réservation | `BookingPage` | Élevée | ✅ |
| `/payment/:bookingId` | Page de paiement | `PaymentPage` | Élevée | ✅ |
| `/messages` | Messagerie | `MessagesPage` | Élevée | ✅ |
| `/notifications` | Notifications | `NotificationHistoryPage` | Élevée | ✅ |

### Routes Professionnel Importantes

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/professional/profile/edit` | Édition du profil professionnel | `ProfessionalProfileEdit` | Élevée | ✅ |
| `/professional/retreats/edit/:id` | Édition d'une retraite | `ProfessionalRetreatEdit` | Élevée | ✅ |
| `/professional/bookings/:id` | Détail d'une réservation du professionnel | `ProfessionalBookingDetail` | Élevée | ✅ |
| `/professional/finances` | Gestion financière | `ProfessionalFinancesPage` | Élevée | ✅ |
| `/professional/analytics` | Analytiques | `ProfessionalAnalyticsPage` | Élevée | ✅ |

### Routes Microservices Importantes

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/retreat-stream` | Service de streaming pour retraites | `RetreatStream` | Élevée | ✅ |
| `/retreat-stream/home` | Page d'accueil du streaming | `HomePage` | Élevée | ✅ |
| `/retreat-stream/live` | Diffusion en direct (visionnage) | `LivePage` | Élevée | ✅ |
| `/retreat-stream/live/create` | Création de diffusion (partenaires premium uniquement) | `CreateLivePage` | Élevée | ✅ |
| `/retreat-stream/explore` | Explorer les diffusions | `ExplorePage` | Élevée | ✅ |
| `/retreat-stream/my-events` | Mes événements | `MyEventsPage` | Élevée | ✅ |
| `/retreat-stream/replay` | Rediffusions | `ReplayPage` | Élevée | ✅ |
| `/retreat-stream/profile` | Profil de streaming | `ProfilePage` | Élevée | ✅ |
| `/retreat-stream/premium-required` | Page d'information sur l'accès premium | `PremiumRequiredPage` | Élevée | ✅ |

## Phase 3 : Routes Complémentaires

### Routes Publiques Complémentaires

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/privacy` | Politique de confidentialité | `PrivacyPolicyPage` | Moyenne | ✅ |
| `/terms` | Conditions d'utilisation | `TermsPage` | Moyenne | ✅ |
| `/blog` | Liste des articles de blog | `BlogPage` | Moyenne | ✅ |
| `/blog/:id` | Article de blog spécifique | `BlogPostPage` | Moyenne | ✅ |
| `/retreats/livestream/:id` | Livestream d'une retraite | `LivestreamRetreatPage` | Moyenne | ✅ |
| `/partners` | Partenaires | `PartnersPage` | Moyenne | ✅ |
| `/organizers` | Organisateurs | `OrganizersPage` | Moyenne | ✅ |

### Routes Client Complémentaires

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/client` | Page d'accueil client | `ClientHomePage` | Moyenne | ✅ |
| `/invoices` | Factures | `InvoiceListPage` | Moyenne | ✅ |
| `/invoices/:id` | Détail d'une facture | `InvoicePage` | Moyenne | ✅ |
| `/loyalty` | Programme de fidélité | `LoyaltyPage` | Moyenne | ✅ |
| `/community` | Communauté | `CommunityPage` | Moyenne | ✅ |

### Routes Professionnel Complémentaires

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/professional` | Page d'accueil professionnel | `ProfessionalHomePage` | Moyenne | ✅ |
| `/professional/pro-dashboard` | Tableau de bord pro avancé | `ProDashboard` | Moyenne | ✅ |
| `/professional/invoices` | Factures | `InvoicePage` | Moyenne | ✅ |
| `/professional/invoices/:id` | Détail d'une facture | `InvoicePage` | Moyenne | ✅ |
| `/professional/partner-landing` | Page d'atterrissage partenaire | `PartnerLandingPage` | Moyenne | ✅ |

### Routes Partenaire Complémentaires

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/partner/onboarding/:type` | Processus d'intégration partenaire | `OnboardingPage` | Moyenne | ✅ |
| `/partner/onboarding/confirmation` | Confirmation d'intégration | `ConfirmationPage` | Moyenne | ✅ |
| `/partner/affiliate` | Portail d'affiliation | `AffiliatePortal` | Moyenne | ✅ |
| `/partner/dashboard` | Tableau de bord partenaire | `BaseDashboard` | Moyenne | ✅ |

### Routes Microservices Complémentaires

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/education` | Page principale éducation | `EducationPage` | Moyenne | ✅ |
| `/nft` | Page principale NFT | `NFT` | Moyenne | ✅ |
| `/videos` | Page principale vidéos | `VideoLandingPage` | Moyenne | ✅ |
| `/videos/explore` | Explorer les vidéos | `VideoExplorePage` | Moyenne | ✅ |
| `/retreat-matcher` | Service de mise en relation pour retraites | `RetreatMatcher` | Moyenne | ✅ |

## Phase 4 : Routes Avancées

### Routes Publiques Avancées

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/cgu` | Conditions générales d'utilisation | `CGUPage` | Basse | ✅ |
| `/gdpr` | Informations RGPD | `GDPRPage` | Basse | ✅ |
| `/security` | Informations sur la sécurité | `SecurityPage` | Basse | ✅ |
| `/foundation` | Page de la fondation | `FoundationPage` | Basse | ✅ |
| `/careers` | Opportunités de carrière | `CareersPage` | Basse | ✅ |
| `/support` | Support client | `SupportPage` | Basse | ✅ |
| `/news` | Actualités | `NewsPage` | Basse | ✅ |
| `/events` | Événements | `EventsPage` | Basse | ✅ |
| `/testimonials` | Témoignages | `TestimonialsPage` | Basse | ✅ |
| `/hosts` | Hôtes de retraites | `HostsPage` | Basse | ✅ |
| `/travel-agencies` | Agences de voyage | `TravelAgenciesPage` | Basse | ✅ |
| `/caterers` | Traiteurs | `CaterersPage` | Basse | ✅ |
| `/wellness` | Bien-être | `WellnessPage` | Basse | ✅ |
| `/verify-email/:token` | Vérification de l'email | `VerifyEmailPage` | Basse | ✅ |

### Routes Client Avancées

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/client/profile` | Profil client | `ClientProfilePage` | Basse | ✅ |
| `/rewards` | Récompenses | `RewardsPage` | Basse | ✅ |
| `/community/member/:id` | Profil d'un membre de la communauté | `CommunityMemberPage` | Basse | ✅ |
| `/social/feed` | Fil d'actualités social | `SocialFeedPage` | Basse | ✅ |

### Routes Professionnel Avancées

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/professional/profile` | Profil professionnel | `ProfessionalProfile` | Basse | ✅ |
| `/professional/retreats/create-ai` | Création d'une retraite assistée par IA | `CreateRetreatAIPage` | Basse | ✅ |

### Routes Partenaire Avancées

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/partner/tools` | Outils partenaire | `ToolsPage` | Basse | ✅ |
| `/partner/community` | Communauté partenaire | `PartnerCommunityPage` | Basse | ✅ |
| `/partner/certified/dashboard` | Tableau de bord partenaire certifié | `CertifiedDashboard` | Basse | ✅ |
| `/partner/retreat-analytics` | Analytiques des retraites | `RetreatAnalyticsPage` | Basse | ✅ |
| `/partner/premium/dashboard` | Tableau de bord partenaire premium | `PremiumDashboard` | Basse | ✅ |
| `/partner/premium/analytics` | Analytiques premium | `PremiumAnalyticsPage` | Basse | ✅ |

### Routes Admin Avancées

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/admin` | Page d'accueil admin | `AdminPage` | Basse | ✅ |
| `/admin/users/:id` | Détail d'un utilisateur | `AdminUserDetailPage` | Basse | ✅ |
| `/admin/roles` | Gestion des rôles | `Roles` | Basse | ✅ |
| `/admin/settings` | Paramètres admin | `Settings` | Basse | ✅ |
| `/admin/content` | Gestion du contenu | `Content` | Basse | ✅ |
| `/admin/resources` | Gestion des ressources | `ResourcesPage` | Basse | ✅ |
| `/admin/retreats/:id` | Détail d'une retraite | `AdminRetreatDetailPage` | Basse | ✅ |
| `/admin/retreats/edit/:id` | Édition d'une retraite | `AdminRetreatEditPage` | Basse | ✅ |
| `/admin/bookings/:id` | Détail d'une réservation | `AdminBookingDetailPage` | Basse | ✅ |
| `/admin/analytics` | Analytiques admin | `Analytics` | Basse | ✅ |
| `/admin/reports` | Rapports | `ReportsPage` | Basse | ✅ |
| `/admin/monitoring` | Monitoring | `MonitoringDashboard` | Basse | ✅ |
| `/admin/security` | Sécurité | `SecurityDashboard` | Basse | ✅ |

### Routes Microservices Avancées

| Route | Description | Composant | Priorité | État |
|-------|-------------|-----------|----------|------|
| `/education/courses` | Liste des cours | `CoursesListPage` | Basse | ✅ |
| `/education/courses/:id` | Détail d'un cours | `CourseDetailPage` | Basse | ✅ |
| `/car-rental` | Location de voitures | `CarRentalPage` | Basse | ✅ |
| `/car-rental/:id` | Détail d'une voiture | `CarDetailsPage` | Basse | ✅ |
| `/transport-booking` | Réservation de transport | `TransportBooking` | Basse | ✅ |
| `/videos/upload` | Télécharger une vidéo | `UploadVideoPage` | Basse | ✅ |
| `/livestream` | Diffusion en direct | `LivePage` | Basse | ✅ |
| `/social` | Plateforme sociale | `Social` | Basse | ✅ |
| `/social-video` | Vidéos sociales | `SocialVideo` | Basse | ✅ |
| `/nft/gallery` | Galerie NFT | `NFTGalleryPage` | Basse | ✅ |
| `/nft/token/:id` | Détail d'un token | `TokenPage` | Basse | ✅ |
| `/financial-management` | Gestion financière | `FinancialManagement` | Basse | ✅ |
| `/security-service` | Service de sécurité | `SecurityService` | Basse | ✅ |
| `/vr` | Réalité virtuelle | `VR` | Basse | ✅ |
| `/website-creator` | Créateur de site web | `WebsiteCreatorLayout` | Basse | ✅ |
| `/storage` | Stockage | `StorageLayout` | Basse | ✅ |

## Suivi de Progression

### Résumé de l'état d'avancement

| Phase | Total Routes | Implémentées | Restantes | Progression |
|-------|--------------|--------------|-----------|-------------|
| Phase 1 | 21 | 21 | 0 | 100% |
| Phase 2 | 30 | 30 | 0 | 100% |
| Phase 3 | 25 | 25 | 0 | 100% |
| Phase 4 | 54 | 54 | 0 | 100% |
| **Total** | **130** | **130** | **0** | **100%** |

### Prochaines étapes prioritaires

1. **Immédiat (1-2 semaines)**
   - ~~Implémenter les routes admin essentielles manquantes~~ ✅
   - ~~Implémenter les routes du service de streaming pour retraites~~ ✅

2. **Court terme (2-4 semaines)**
   - ~~Compléter les routes client et professionnel importantes~~ ✅
   - ~~Implémenter les routes partenaire de base~~ ✅

3. **Moyen terme (1-3 mois)**
   - ~~Implémenter les routes complémentaires restantes~~ ✅
   - ~~Commencer l'implémentation des routes avancées prioritaires~~ ✅

4. **Long terme (3-6 mois)**
   - ~~Compléter toutes les routes avancées~~ ✅
   - Optimiser et améliorer les routes existantes

## Notes d'implémentation

### Protection des routes

Toutes les routes protégées doivent être implémentées avec le composant `ProtectedRoute` approprié :

```jsx
<Route
  path="/retreat-stream"
  element={
    <ProtectedRoute>
      <RetreatStream />
    </ProtectedRoute>
  }
/>
```

### Routes du service de streaming pour retraites

Pour le service de streaming pour retraites, il est recommandé d'implémenter un routeur imbriqué :

```jsx
// src/pages/retreats/RetreatStream.tsx
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ProtectedRoute } from '../../components/auth/ProtectedRoute';

// Import des composants
import HomePage from './streaming/HomePage';
import LivePage from './streaming/LivePage';
import ExplorePage from './streaming/ExplorePage';
import MyEventsPage from './streaming/MyEventsPage';
import ReplayPage from './streaming/ReplayPage';
import ProfilePage from './streaming/ProfilePage';

const RetreatStream: React.FC = () => {
  return (
    <div className="retreat-stream-container">
      <Routes>
        <Route path="/" element={<Navigate to="home" replace />} />
        <Route path="home" element={<HomePage />} />
        <Route path="live" element={<LivePage />} />
        <Route path="explore" element={<ExplorePage />} />
        <Route path="my-events" element={<MyEventsPage />} />
        <Route path="replay" element={<ReplayPage />} />
        <Route path="profile" element={<ProfilePage />} />
      </Routes>
    </div>
  );
};

export default RetreatStream;
```

Puis dans le routeur principal :

```jsx
<Route
  path="/retreat-stream/*"
  element={
    <ProtectedRoute>
      <RetreatStream />
    </ProtectedRoute>
  }
/>
```

### Priorités d'implémentation

Lors de l'implémentation des routes, suivez ces principes :

1. **Commencez par les routes critiques** : Assurez-vous que les fonctionnalités de base sont disponibles
2. **Implémentez les routes par section** : Complétez une section à la fois pour maintenir la cohérence
3. **Testez chaque route** : Vérifiez que chaque route fonctionne correctement avant de passer à la suivante
4. **Documentez les changements** : Mettez à jour cette feuille de route à mesure que vous progressez
