# Guide du Développeur

## Installation

```bash
# Installation des dépendances
npm install @rb/encryption @rb/auth @rb/key-management

# Configuration de l'environnement
cp .env.example .env
```

## Utilisation

### Chiffrement de base

```typescript
import { EncryptionService } from '@rb/encryption';

const encryptionService = new EncryptionService({
  algorithm: 'POST_QUANTUM',
  keyManagement: {
    provider: 'vault',
    config: {
      address: process.env.VAULT_ADDR
    }
  }
});

// Chiffrement
const encrypted = await encryptionService.encrypt({
  data: 'Données sensibles',
  options: {
    algorithm: 'KYBER768'
  }
});

// Déchiffrement
const decrypted = await encryptionService.decrypt({
  ciphertext: encrypted.ciphertext,
  metadata: encrypted.metadata
});
```

### Chiffrement homomorphique

```typescript
import { HomomorphicService } from '@rb/encryption';

const homomorphicService = new HomomorphicService();

// Chiffrement de nombres
const encrypted1 = await homomorphicService.encrypt(5);
const encrypted2 = await homomorphicService.encrypt(3);

// Opération sur données chiffrées
const encryptedSum = await homomorphicService.add(encrypted1, encrypted2);

// Déchiffrement du résultat
const sum = await homomorphicService.decrypt(encryptedSum); // 8
```

## Tests

```bash
# Exécution des tests
npm run test

# Tests avec couverture
npm run test:coverage

# Tests d'intégration
npm run test:integration
```

## Bonnes pratiques

1. Toujours utiliser les dernières versions des bibliothèques
2. Implémenter la rotation automatique des clés
3. Logger toutes les opérations cryptographiques
4. Valider les entrées avant chiffrement
5. Gérer correctement les erreurs