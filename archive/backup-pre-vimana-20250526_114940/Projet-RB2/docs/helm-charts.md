# Documentation des Helm Charts

## Introduction

Les Helm Charts du projet RB2 permettent un déploiement cohérent et reproductible de tous les composants de l'application sur des clusters Kubernetes. Cette documentation détaille les charts disponibles, leur configuration et les meilleures pratiques pour les utiliser.

## Structure des Helm Charts

Le projet utilise une architecture multi-charts où chaque composant dispose de son propre chart Helm:

```
helm/
├── backend/             # Services backend
├── frontend/            # Application frontend
├── vr/                  # Composants de réalité virtuelle
├── social/              # Services d'intégration sociale
├── agent-ia/            # Services d'intelligence artificielle
├── security/            # Services de sécurité
├── education/           # Modules éducatifs
├── analyzer/            # Services d'analyse
└── common/              # Bibliothèque de templates partagés
```

## Chart Common

Le chart `common` fournit des templates réutilisables pour tous les autres charts. Il inclut :

- Templates de déploiement standardisés
- ConfigMaps et Secrets
- Services et Ingress
- Configuration des sondes de santé
- Gestion des ressources
- Politiques de scalabilité

## Configuration Standardisée des Ressources

Tous les charts respectent la même structure de configuration pour les ressources :

```yaml
resources:
  limits:
    cpu: "1"
    memory: "1Gi"
  requests:
    cpu: "100m"
    memory: "128Mi"
```

### Recommandations par composant

| Composant | CPU (Request) | Mémoire (Request) | CPU (Limit) | Mémoire (Limit) |
|-----------|--------------|------------------|------------|----------------|
| Backend   | 200m         | 256Mi            | 1          | 1Gi            |
| Frontend  | 100m         | 128Mi            | 500m       | 512Mi          |
| Analyzer  | 500m         | 1Gi              | 2          | 4Gi            |
| Agent IA  | 500m         | 2Gi              | 4          | 8Gi            |

## Configuration des Health Checks

Chaque chart inclut des sondes de disponibilité configurables :

```yaml
probes:
  liveness:
    path: /health
    initialDelaySeconds: 30
    periodSeconds: 10
  readiness:
    path: /ready
    initialDelaySeconds: 5
    periodSeconds: 5
```

### Health Checks manquants (à compléter)

- [ ] VR: Configuration des health checks spécifiques WebRTC
- [ ] Social: Vérification des connexions aux services externes
- [ ] Education: Health checks pour le système de streaming
- [ ] Security: Vérification de l'état des certificats et tokens

## Utilisation

### Installation ou mise à jour d'un composant

```bash
# Installation initiale
helm install [RELEASE_NAME] ./helm/[CHART] -n [NAMESPACE]

# Mise à jour
helm upgrade [RELEASE_NAME] ./helm/[CHART] -n [NAMESPACE]

# Installation ou mise à jour
helm upgrade --install [RELEASE_NAME] ./helm/[CHART] -n [NAMESPACE]
```

### Installation complète de l'application

```bash
# Utiliser le script d'installation automatique
./scripts/deploy-all.sh [ENVIRONMENT]
```

## Valeurs personnalisables

Chaque chart accepte un fichier de valeurs personnalisées. Voici un exemple pour le backend :

```yaml
# values-production.yaml
image:
  repository: registry.example.com/rb2/backend
  tag: 1.2.3
  pullPolicy: IfNotPresent

replicas: 3

resources:
  limits:
    cpu: "2"
    memory: "2Gi"
  requests:
    cpu: "500m"
    memory: "512Mi"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: api.example.com
      paths:
        - path: /
          pathType: Prefix

env:
  - name: NODE_ENV
    value: production
  - name: LOG_LEVEL
    value: info
```

## Meilleures pratiques

1. **Versionnement** : Toujours spécifier une version explicite des images dans les environnements de production
2. **Ressources** : Configurer des limites de ressources appropriées pour chaque composant
3. **Secrets** : Utiliser des gestionnaires de secrets externes (Vault, Sealed Secrets) plutôt que des secrets Kubernetes bruts
4. **Configuration** : Externaliser la configuration dans des ConfigMaps ou Secrets dédiés
5. **Déploiement** : Utiliser des stratégies de déploiement progressif (Rolling Update)
6. **Monitoring** : Activer la configuration des métriques pour Prometheus

## Procédures de déploiement

### Déploiement en production

```bash
# 1. Mettre à jour les valeurs de production
cp helm/backend/values.yaml helm/backend/values-production.yaml
# Éditer values-production.yaml avec les valeurs appropriées

# 2. Effectuer le déploiement
helm upgrade --install backend-prod ./helm/backend \
  -f helm/backend/values-production.yaml \
  -n rb2-production
```

### Rollback

En cas de problème après un déploiement :

```bash
# Lister les révisions disponibles
helm history backend-prod -n rb2-production

# Effectuer un rollback vers une révision spécifique
helm rollback backend-prod [REVISION] -n rb2-production
```

## Prochaines étapes

- [ ] Finaliser la documentation des Helm Charts existants *(priorité immédiate)*
- [ ] Standardiser la configuration des ressources pour tous les composants *(priorité immédiate)*
- [ ] Compléter les health checks manquants *(priorité immédiate)*
- [ ] Automatiser le déploiement avec ArgoCD ou Flux CD
- [ ] Implémenter des tests de smoke post-déploiement 