# Module de Performance

## Introduction

Le module de performance est un composant essentiel de l'application Backend-NestJS de Retreat And Be qui fournit des fonctionnalités complètes pour surveiller les performances de l'application, collecter des métriques, générer des alertes et visualiser les données via des tableaux de bord personnalisables.

Ce document décrit l'architecture, les fonctionnalités et l'utilisation du module de performance.

## Architecture

Le module de performance est composé de plusieurs services spécialisés :

- **PerformanceService** : Service principal qui coordonne les autres services et gère les rapports de performance.
- **MetricsService** : Gestion des métriques (collecte, stockage, récupération, agrégation).
- **AlertService** : Gestion des alertes et des règles d'alerte.
- **SystemMonitorService** : Surveillance des métriques système (CPU, mémoire, disque, réseau).
- **NotificationService** : Gestion des notifications d'alerte.
- **DashboardService** : Gestion des tableaux de bord personnalisables.

Ces services sont exposés via trois contrôleurs :

- **PerformanceController** : Expose les fonctionnalités de métriques et de rapports.
- **AlertController** : Expose les fonctionnalités d'alertes et de règles d'alerte.
- **DashboardController** : Expose les fonctionnalités de tableaux de bord.

## Modèles de données

Le module utilise les modèles de données suivants :

### Metric

```prisma
model Metric {
  id          String    @id @default(uuid())
  name        String
  description String
  type        String
  value       Float
  unit        String
  timestamp   DateTime
  tags        Json
  metadata    Json?

  @@index([name])
  @@index([type])
  @@index([timestamp])
}
```

### AlertRule

```prisma
model AlertRule {
  id          String    @id @default(uuid())
  name        String
  description String
  enabled     Boolean   @default(true)
  severity    String
  source      String
  metric      String
  condition   String
  threshold   Float
  duration    Int       @default(60)
  cooldown    Int       @default(300)
  tags        Json
  metadata    Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([enabled])
  @@index([severity])
  @@index([source])
  @@index([metric])
}
```

### Alert

```prisma
model Alert {
  id              String    @id @default(uuid())
  name            String
  description     String
  severity        String
  status          String
  source          String
  timestamp       DateTime
  resolvedAt      DateTime?
  acknowledgedAt  DateTime?
  acknowledgedBy  String?
  tags            Json
  metadata        Json?
  value           Float?
  threshold       Float?
  metric          String?

  // Relations
  notifications   AlertNotification[]

  @@index([severity])
  @@index([status])
  @@index([source])
  @@index([timestamp])
  @@index([metric])
}
```

### AlertNotification

```prisma
model AlertNotification {
  id          String    @id @default(uuid())
  alertId     String
  channel     String
  status      String
  sentAt      DateTime
  deliveredAt DateTime?
  error       String?
  metadata    Json?

  // Relations
  alert       Alert     @relation(fields: [alertId], references: [id], onDelete: Cascade)

  @@index([alertId])
  @@index([channel])
  @@index([status])
  @@index([sentAt])
}
```

### Dashboard

```prisma
model Dashboard {
  id          String    @id @default(uuid())
  name        String
  description String
  type        String
  layout      Json
  panels      Json
  isDefault   Boolean   @default(false)
  isPublic    Boolean   @default(true)
  ownerId     String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  metadata    Json?

  @@index([type])
  @@index([isDefault])
  @@index([isPublic])
  @@index([ownerId])
}
```

### PerformanceReport

```prisma
model PerformanceReport {
  id          String    @id @default(uuid())
  startDate   DateTime
  endDate     DateTime
  report      Json
  createdAt   DateTime  @default(now())

  @@index([startDate, endDate])
  @@index([createdAt])
}
```

## Fonctionnalités

### Collecte de métriques

Le module permet de collecter différents types de métriques :

- **Métriques système** : CPU, mémoire, disque, réseau
- **Métriques de base de données** : Requêtes, temps de réponse, connexions
- **Métriques API** : Requêtes, temps de réponse, erreurs
- **Métriques personnalisées** : Définies par l'utilisateur

Les métriques sont collectées à intervalles réguliers (par défaut toutes les minutes) et stockées dans la base de données.

### Alertes

Le module permet de définir des règles d'alerte basées sur des seuils pour les métriques collectées. Lorsqu'une règle est déclenchée, une alerte est générée et des notifications peuvent être envoyées par différents canaux :

- **Email**
- **Slack**
- **Webhook**

Les alertes peuvent être acquittées et résolues par les utilisateurs.

### Tableaux de bord

Le module permet de créer des tableaux de bord personnalisables pour visualiser les métriques collectées. Les tableaux de bord peuvent contenir différents types de panneaux :

- **Graphiques** : Ligne, barre, camembert, aire, nuage de points
- **Jauges**
- **Tableaux**
- **Texte**
- **Statistiques**

Les tableaux de bord peuvent être partagés avec d'autres utilisateurs et définis comme tableaux de bord par défaut pour un type de métriques.

### Rapports

Le module génère des rapports de performance périodiques (par défaut tous les jours à minuit) qui contiennent des statistiques sur les métriques collectées. Ces rapports peuvent être consultés ultérieurement.

## Configuration

Le module de performance est configurable via des variables d'environnement ou un fichier de configuration. Voici les principales options de configuration :

```typescript
// Configuration générale
metricsInterval: 60, // Intervalle de collecte des métriques en secondes
metricsRetention: 30, // Durée de rétention des métriques en jours
maxAlerts: 1000, // Nombre maximum d'alertes à conserver
monitoringEnabled: true, // Activer/désactiver le monitoring
alertsEnabled: true, // Activer/désactiver les alertes
notificationsEnabled: true, // Activer/désactiver les notifications

// Configuration des métriques
metrics: {
  http: {
    enabled: true, // Activer/désactiver les métriques HTTP
    slowRequestThreshold: 1000, // Seuil de requête lente en ms
    excludedRoutes: ['/metrics', '/health', '/favicon.ico'], // Routes exclues
    detailedRouteMetrics: false, // Métriques détaillées par route
  },
  database: {
    enabled: true, // Activer/désactiver les métriques de base de données
    slowQueryThreshold: 500, // Seuil de requête lente en ms
    detailedQueryMetrics: false, // Métriques détaillées par requête
  },
  system: {
    enabled: true, // Activer/désactiver les métriques système
    interval: 60, // Intervalle de collecte en secondes
    highCpuThreshold: 80, // Seuil d'utilisation CPU élevée en %
    highMemoryThreshold: 80, // Seuil d'utilisation mémoire élevée en %
    highDiskThreshold: 80, // Seuil d'utilisation disque élevée en %
  },
  custom: {
    enabled: true, // Activer/désactiver les métriques personnalisées
    metrics: [], // Liste des métriques personnalisées
  },
},

// Configuration des alertes
alerts: {
  http: {
    enabled: true, // Activer/désactiver les alertes HTTP
    errorRateThreshold: 5, // Seuil de taux d'erreur en %
    latencyThreshold: 2000, // Seuil de latence en ms
    requestRateThreshold: 1000, // Seuil de taux de requêtes par minute
  },
  database: {
    enabled: true, // Activer/désactiver les alertes de base de données
    queryLatencyThreshold: 1000, // Seuil de latence de requête en ms
    errorRateThreshold: 1, // Seuil de taux d'erreur en %
    connectionThreshold: 80, // Seuil d'utilisation des connexions en %
  },
  system: {
    enabled: true, // Activer/désactiver les alertes système
    cpuThreshold: 90, // Seuil d'alerte CPU en %
    memoryThreshold: 90, // Seuil d'alerte mémoire en %
    diskThreshold: 90, // Seuil d'alerte disque en %
  },
  custom: {
    enabled: true, // Activer/désactiver les alertes personnalisées
    alerts: [], // Liste des alertes personnalisées
  },
},

// Configuration des notifications
notifications: {
  channels: {
    email: {
      enabled: false, // Activer/désactiver les notifications par email
      recipients: [], // Liste des destinataires
    },
    slack: {
      enabled: false, // Activer/désactiver les notifications Slack
      webhookUrl: '', // URL du webhook Slack
      channel: '#alerts', // Canal Slack
    },
    webhook: {
      enabled: false, // Activer/désactiver les notifications webhook
      url: '', // URL du webhook
    },
  },
  severityLevels: {
    minLevel: 'warning', // Niveau minimum de sévérité pour les notifications
    email: 'error,critical', // Niveaux de sévérité pour les emails
    slack: 'warning,error,critical', // Niveaux de sévérité pour Slack
    webhook: 'warning,error,critical', // Niveaux de sévérité pour webhook
  },
},

// Configuration des tableaux de bord
dashboards: {
  enabled: true, // Activer/désactiver les tableaux de bord
  baseUrl: '/dashboards', // URL de base des tableaux de bord
  available: ['overview', 'http', 'database', 'system', 'custom'], // Types de tableaux de bord disponibles
},

// Configuration des tâches planifiées
scheduledTasks: {
  metricsCleanupInterval: 1440, // Intervalle de nettoyage des métriques en minutes
  alertsCleanupInterval: 1440, // Intervalle de nettoyage des alertes en minutes
  alertCheckInterval: 5, // Intervalle de vérification des alertes en minutes
  reportGenerationInterval: 1440, // Intervalle de génération des rapports en minutes
},
```

## API

### Métriques

#### Récupérer les métriques

```
GET /api/performance/metrics
```

Paramètres de requête :
- `name` : Nom de la métrique
- `type` : Type de métrique
- `startTime` : Date de début (ISO 8601)
- `endTime` : Date de fin (ISO 8601)
- `limit` : Nombre maximum de résultats (défaut: 100)
- `offset` : Nombre de résultats à ignorer (défaut: 0)
- `sortBy` : Champ de tri (défaut: timestamp)
- `sortOrder` : Ordre de tri (asc, desc) (défaut: desc)
- `includeStats` : Inclure les statistiques (défaut: false)

#### Récupérer les métriques système

```
GET /api/performance/metrics/system
```

#### Récupérer les métriques de base de données

```
GET /api/performance/metrics/database
```

#### Récupérer les métriques API

```
GET /api/performance/metrics/api
```

#### Récupérer les séries temporelles de métriques

```
GET /api/performance/metrics/timeseries
```

Paramètres de requête supplémentaires :
- `interval` : Intervalle pour les séries temporelles (en secondes)
- `aggregation` : Fonction d'agrégation (min, max, avg, sum, count) (défaut: avg)

#### Générer un rapport de performance

```
GET /api/performance/report
```

Paramètres de requête :
- `startDate` : Date de début (ISO 8601)
- `endDate` : Date de fin (ISO 8601)

#### Nettoyer les anciennes métriques

```
POST /api/performance/cleanup/metrics
```

Paramètres de requête :
- `retentionDays` : Nombre de jours de rétention (défaut: 30)

### Alertes

#### Créer une règle d'alerte

```
POST /api/alerts/rules
```

Corps de la requête :
```json
{
  "name": "High CPU Usage",
  "description": "Alert when CPU usage exceeds 90%",
  "severity": "warning",
  "source": "system",
  "metric": "system.cpu.usage",
  "condition": "greater_than",
  "threshold": 90,
  "duration": 300,
  "cooldown": 1800,
  "tags": {
    "environment": "production"
  }
}
```

#### Récupérer toutes les règles d'alerte

```
GET /api/alerts/rules
```

Paramètres de requête :
- `enabled` : État d'activation (true, false)
- `severity` : Sévérité (info, warning, error, critical)
- `source` : Source (system, database, api, application, custom)
- `metric` : Métrique
- `skip` : Nombre de résultats à ignorer
- `take` : Nombre de résultats à retourner

#### Récupérer une règle d'alerte par son ID

```
GET /api/alerts/rules/:id
```

#### Mettre à jour une règle d'alerte

```
PUT /api/alerts/rules/:id
```

Corps de la requête :
```json
{
  "name": "Updated CPU Usage Alert",
  "threshold": 95
}
```

#### Supprimer une règle d'alerte

```
DELETE /api/alerts/rules/:id
```

#### Activer ou désactiver une règle d'alerte

```
PUT /api/alerts/rules/:id/enabled
```

Corps de la requête :
```json
{
  "enabled": true
}
```

#### Récupérer les alertes

```
GET /api/alerts
```

Paramètres de requête :
- `severity` : Sévérité (info, warning, error, critical)
- `status` : Statut (active, acknowledged, resolved)
- `source` : Source (system, database, api, application, custom)
- `startTime` : Date de début (ISO 8601)
- `endTime` : Date de fin (ISO 8601)
- `limit` : Nombre maximum de résultats (défaut: 100)
- `offset` : Nombre de résultats à ignorer (défaut: 0)
- `sortBy` : Champ de tri (défaut: timestamp)
- `sortOrder` : Ordre de tri (asc, desc) (défaut: desc)

#### Récupérer une alerte par son ID

```
GET /api/alerts/:id
```

#### Acquitter une alerte

```
POST /api/alerts/:id/acknowledge
```

#### Résoudre une alerte

```
POST /api/alerts/:id/resolve
```

#### Vérifier les règles d'alerte

```
POST /api/alerts/check
```

#### Nettoyer les anciennes alertes

```
POST /api/alerts/cleanup
```

Paramètres de requête :
- `maxAlerts` : Nombre maximum d'alertes à conserver (défaut: 1000)

### Tableaux de bord

#### Créer un tableau de bord

```
POST /api/dashboards
```

Corps de la requête :
```json
{
  "name": "System Overview",
  "description": "Overview of system metrics",
  "type": "system",
  "layout": {
    "columns": 12,
    "rows": 24,
    "gridGap": 8
  },
  "panels": [
    {
      "title": "CPU Usage",
      "type": "chart",
      "position": { "x": 0, "y": 0 },
      "size": { "width": 6, "height": 8 },
      "config": {
        "chartType": "line",
        "timeRange": { "from": "now-6h", "to": "now" },
        "refreshInterval": 60,
        "displayLegend": true
      },
      "metrics": ["system.cpu.usage"]
    }
  ],
  "isDefault": false,
  "isPublic": true
}
```

#### Récupérer tous les tableaux de bord

```
GET /api/dashboards
```

Paramètres de requête :
- `type` : Type de tableau de bord
- `isPublic` : Visibilité publique (true, false)
- `skip` : Nombre de résultats à ignorer
- `take` : Nombre de résultats à retourner

#### Récupérer un tableau de bord par son ID

```
GET /api/dashboards/:id
```

#### Mettre à jour un tableau de bord

```
PUT /api/dashboards/:id
```

Corps de la requête :
```json
{
  "name": "Updated System Overview",
  "panels": [
    {
      "title": "CPU Usage",
      "type": "chart",
      "position": { "x": 0, "y": 0 },
      "size": { "width": 6, "height": 8 },
      "config": {
        "chartType": "line",
        "timeRange": { "from": "now-6h", "to": "now" },
        "refreshInterval": 60,
        "displayLegend": true
      },
      "metrics": ["system.cpu.usage"]
    },
    {
      "title": "Memory Usage",
      "type": "chart",
      "position": { "x": 6, "y": 0 },
      "size": { "width": 6, "height": 8 },
      "config": {
        "chartType": "line",
        "timeRange": { "from": "now-6h", "to": "now" },
        "refreshInterval": 60,
        "displayLegend": true
      },
      "metrics": ["system.memory.usage"]
    }
  ]
}
```

#### Supprimer un tableau de bord

```
DELETE /api/dashboards/:id
```

#### Récupérer le tableau de bord par défaut pour un type

```
GET /api/dashboards/default/:type
```

#### Définir un tableau de bord comme tableau de bord par défaut

```
PUT /api/dashboards/:id/default
```

Corps de la requête :
```json
{
  "isDefault": true
}
```

#### Définir la visibilité publique d'un tableau de bord

```
PUT /api/dashboards/:id/public
```

Corps de la requête :
```json
{
  "isPublic": true
}
```

## Utilisation dans le code

### Collecter des métriques personnalisées

```typescript
// Dans un service
constructor(private readonly metricsService: MetricsService) {}

async trackCustomMetric() {
  // Collecter une métrique personnalisée
  await this.metricsService.storeMetric({
    name: 'custom.metric',
    description: 'Custom metric',
    type: MetricType.COUNTER,
    value: 1,
    unit: 'count',
    timestamp: new Date(),
    tags: { type: 'custom' },
  });
}
```

### Créer une règle d'alerte

```typescript
// Dans un service
constructor(private readonly alertService: AlertService) {}

async createCustomAlertRule() {
  // Créer une règle d'alerte personnalisée
  const rule = await this.alertService.createAlertRule({
    name: 'Custom Metric Alert',
    description: 'Alert when custom metric exceeds threshold',
    severity: AlertSeverity.WARNING,
    source: AlertSource.CUSTOM,
    metric: 'custom.metric',
    condition: AlertCondition.GREATER_THAN,
    threshold: 100,
    duration: 300,
    cooldown: 1800,
    tags: { type: 'custom' },
  });

  return rule;
}
```

### Créer un tableau de bord personnalisé

```typescript
// Dans un service
constructor(private readonly dashboardService: DashboardService) {}

async createCustomDashboard(userId: string) {
  // Créer un tableau de bord personnalisé
  const dashboard = await this.dashboardService.createDashboard({
    name: 'Custom Dashboard',
    description: 'Custom metrics dashboard',
    type: DashboardType.CUSTOM,
    layout: {
      columns: 12,
      rows: 24,
      gridGap: 8,
    },
    panels: [
      {
        title: 'Custom Metric',
        type: PanelType.CHART,
        position: { x: 0, y: 0 },
        size: { width: 12, height: 8 },
        config: {
          chartType: ChartType.LINE,
          timeRange: { from: 'now-24h', to: 'now' },
          refreshInterval: 60,
          displayLegend: true,
        },
        metrics: ['custom.metric'],
      },
    ],
    isDefault: false,
    isPublic: true,
  }, userId);

  return dashboard;
}
```

## Bonnes pratiques

### Métriques

- Utilisez des noms de métriques cohérents et descriptifs (ex: `system.cpu.usage`, `api.request.latency`).
- Ajoutez des tags aux métriques pour faciliter le filtrage et l'agrégation.
- Limitez le nombre de métriques collectées pour éviter de surcharger la base de données.
- Nettoyez régulièrement les anciennes métriques pour économiser de l'espace de stockage.

### Alertes

- Définissez des seuils d'alerte appropriés pour éviter les faux positifs.
- Utilisez des durées et des temps de refroidissement adaptés pour éviter les alertes répétitives.
- Configurez les canaux de notification en fonction de la sévérité des alertes.
- Acquittez et résolvez les alertes rapidement pour maintenir la visibilité sur les problèmes actifs.

### Tableaux de bord

- Créez des tableaux de bord spécifiques pour différents cas d'utilisation.
- Organisez les panneaux de manière logique et cohérente.
- Utilisez des intervalles de rafraîchissement adaptés à l'importance des métriques.
- Partagez les tableaux de bord avec les utilisateurs concernés.

## Conclusion

Le module de performance est un outil puissant pour surveiller et améliorer les performances de l'application Backend-NestJS de Retreat And Be. Il fournit des fonctionnalités complètes pour collecter des métriques, générer des alertes et visualiser les données via des tableaux de bord personnalisables.

En utilisant ce module, vous pouvez :
- Détecter et résoudre rapidement les problèmes de performance
- Suivre l'évolution des performances de l'application dans le temps
- Recevoir des alertes en cas de problème
- Visualiser les métriques de manière claire et intuitive
- Générer des rapports de performance pour l'analyse à long terme
