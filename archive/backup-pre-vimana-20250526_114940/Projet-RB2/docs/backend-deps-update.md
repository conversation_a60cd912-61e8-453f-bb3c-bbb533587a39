# Mise à jour des dépendances du Backend

## Résumé des changements

La mise à jour des dépendances du backend vise à :
1. Mettre à jour les bibliothèques vers leurs dernières versions stables
2. Supprimer les dépendances obsolètes ou non utilisées
3. Harmoniser les versions d'OpenTelemetry à travers les microservices
4. Renforcer la sécurité et les performances des applications

## Dépendances à mettre à jour

| Dépendance | Version actuelle | Version recommandée | Commentaire |
|------------|------------------|---------------------|-------------|
| `@prisma/client` | 5.10.0 | 5.12.0 | Corrections de bugs et améliorations de performances |
| `ioredis` | 5.3.2 | 5.3.2 | Conserver (stable) |
| `kafkajs` | 2.2.4 | 2.2.4 | Conserver (stable) |

## Dépendances à ajouter

| Dépendance | Version recommandée | Commentaire |
|------------|---------------------|-------------|
| `@nestjs/common` | 11.0.10 | Standardisation avec autres microservices |
| `@nestjs/core` | 11.0.10 | Standardisation avec autres microservices |
| `@nestjs/platform-express` | 11.0.10 | Standardisation avec autres microservices |
| `@opentelemetry/api` | 1.7.0 | Pour la télémétrie (monitoring et traçage) |
| `@opentelemetry/sdk-metrics` | 1.30.1 | Pour les métriques OpenTelemetry |
| `@opentelemetry/resources` | 1.19.0 | Pour la configuration des ressources OpenTelemetry |
| `@opentelemetry/semantic-conventions` | 1.19.0 | Pour les conventions sémantiques OpenTelemetry |
| `prom-client` | 15.1.3 | Pour l'exportation des métriques vers Prometheus |
| `express` | 4.19.1 | Pour la création d'API RESTful |
| `winston` | 3.11.0 | Pour la journalisation structurée |
| `helmet` | 7.1.0 | Pour la sécurité HTTP |
| `cors` | 2.8.5 | Pour la gestion des requêtes cross-origin |

## Dépendances de développement à mettre à jour

| Dépendance | Version actuelle | Version recommandée | Commentaire |
|------------|------------------|---------------------|-------------|
| `@types/node` | 20.11.0 | 20.12.6 | Mise à jour des types pour Node.js |
| `@types/ioredis` | 5.0.0 | 5.0.0 | Conserver (stable) |
| `typescript` | 5.3.3 | 5.4.3 | Nouvelles fonctionnalités et corrections de bugs |
| `prisma` | non présent | 5.12.0 | À ajouter comme devDependency pour synchroniser avec @prisma/client |
| `jest` | non présent | 29.7.0 | Pour les tests unitaires |
| `ts-jest` | non présent | 29.1.2 | Pour tester le code TypeScript avec Jest |
| `@types/jest` | non présent | 29.5.12 | Types pour Jest |
| `eslint` | non présent | 8.57.1 | Pour le linting |
| `prettier` | non présent | 3.2.5 | Pour le formatage du code |

## Dépendances à standardiser à travers les microservices

Pour améliorer la cohérence entre les différents microservices, il est recommandé d'utiliser les mêmes versions des bibliothèques suivantes :

1. NestJS : v11.0.10
2. OpenTelemetry : version harmonisée (@opentelemetry/api v1.7.0)
3. Prisma : v5.12.0
4. Jest : v29.7.0
5. TypeScript : v5.4.3
6. ESLint : v8.57.1
7. Prettier : v3.2.5

## Instructions de mise à jour

1. Sauvegardez le fichier `package.json` actuel
2. Mettez à jour les versions dans le fichier `package.json`
3. Supprimez le fichier `package-lock.json` ou `yarn.lock`
4. Exécutez `npm install` ou `yarn install` pour installer les nouvelles versions
5. Mettez à jour les schémas Prisma si nécessaire : `npx prisma migrate dev`
6. Exécutez les tests pour vérifier que tout fonctionne correctement

## Impact sur le code

La mise à jour de certaines dépendances pourrait nécessiter des modifications du code, notamment :

1. Mise à jour des importations OpenTelemetry selon la nouvelle structure API
2. Adaptation du code aux nouvelles versions de NestJS (si ajouté)
3. Revue des migrations Prisma pour s'assurer de leur compatibilité

## Mesures de suivi

Après la mise à jour :

1. Exécutez l'audit de sécurité npm : `npm audit --production`
2. Vérifiez que la télémétrie fonctionne correctement
3. Surveillez les journaux pour détecter d'éventuelles erreurs
4. Testez les performances pour s'assurer qu'elles n'ont pas été dégradées 