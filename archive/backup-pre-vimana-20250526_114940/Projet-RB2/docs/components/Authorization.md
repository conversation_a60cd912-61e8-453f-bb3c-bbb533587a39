# Role-Based Authorization System

## Overview
The application implements a comprehensive role-based access control (RBAC) system with a hierarchical role structure. This document outlines the components, implementation details, and usage guidelines.

## Role Hierarchy

The system implements the following role hierarchy (from lowest to highest privileges):
```
USER < PARTNER < CERTIFIED_PARTNER < PREMIUM_PARTNER < MODERATOR < ADMIN
```

## Components

### RoleRoute
A route component that protects routes based on user roles and permissions.

```tsx
import { RoleRoute } from '../components/routing/RoleRoute';

interface RoleRouteProps {
  element: JSX.Element;
  requiredRole: Role;
  requiredPermission?: Permission;
  redirectPath?: string;
}

<RoleRoute
  element={<ProtectedComponent />}
  requiredRole="ADMIN"
  requiredPermission="MANAGE_USERS"
  redirectPath="/unauthorized"
/>
```

#### Features
- Role-based access control
- Optional permission checking
- Configurable redirect path
- Automatic authentication check

### RoleBasedRoute
An enhanced version of RoleRoute with additional features and loading states.

```tsx
import { RoleBasedRoute } from '../components/routing/RoleBasedRoute';

<RoleBasedRoute
  element={<ProtectedComponent />}
  requiredRole="PARTNER"
  requiredPermission="VIEW_ANALYTICS"
  redirectPath="/unauthorized"
  requireAuth={true}
/>
```

#### Features
- Loading state handling
- Optional authentication requirement
- Role hierarchy enforcement
- Permission-based access control

## Authorization Service

The `AuthorizationService` provides utility functions for role and permission checks:

```typescript
import { AuthorizationService } from '../utils/security/authorization';

// Check if user has required role or higher
const hasRole = AuthorizationService.hasRole(userRole, requiredRole);

// Check if user has specific permission
const hasPermission = AuthorizationService.hasPermission(userRole, permission);
```

## Usage Examples

### Protecting Routes
```tsx
// App.tsx
import { RoleBasedRoute } from "./components/routing/RoleBasedRoute';

function App() {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route
        path="/admin"
        element={
          <RoleBasedRoute
            element={<AdminDashboard />}
            requiredRole="ADMIN"
            redirectPath="/unauthorized"
          />
        }
      />
      <Route
        path="/partner"
        element={
          <RoleBasedRoute
            element={<PartnerDashboard />}
            requiredRole="PARTNER"
            requiredPermission="VIEW_DASHBOARD"
          />
        }
      />
    </Routes>
  );
}
```

### Component-Level Protection
```tsx
import { useAuth } from '../contexts/AuthContext';
import { AuthorizationService } from '../utils/security/authorization';

function FeatureComponent() {
  const { user } = useAuth();
  const userRole = user?.role || 'USER';

  if (!AuthorizationService.hasRole(userRole, 'PREMIUM_PARTNER')) {
    return <RestrictedAccess />;
  }

  return <PremiumFeature />;
}
```

## Best Practices

1. Always use RoleBasedRoute for route protection
2. Implement both role and permission checks when needed
3. Use the AuthorizationService for consistent access control
4. Handle loading states appropriately
5. Provide meaningful feedback for unauthorized access
6. Keep the role hierarchy in mind when setting required roles

## Error Handling

The authorization system includes built-in error handling:

- Redirects to login for unauthenticated users
- Shows unauthorized page for insufficient permissions
- Displays loading states during authentication checks
- Provides fallback UI for edge cases

## Security Considerations

1. Always implement authorization checks on both frontend and backend
2. Never rely solely on client-side role checking
3. Regularly audit role assignments and permissions
4. Keep the role hierarchy simple and maintainable
5. Log unauthorized access attempts for security monitoring