# Intégration du microservice Social-Platform-Video

## Introduction

Ce document décrit l'intégration complète du microservice Social-Platform-Video avec le frontend (Front-Audrey-V1-Main-main) et le backend (Backend-NestJS). Cette intégration permet d'ajouter des fonctionnalités sociales avancées à la plateforme Retreat And Be, notamment les vidéoconférences, les livestreams, les articles de blog et les analyses sociales.

## Table des matières

1. [Architecture](#architecture)
2. [Fonctionnalités implémentées](#fonctionnalités-implémentées)
3. [Intégration backend](#intégration-backend)
4. [Intégration frontend](#intégration-frontend)
5. [Sécurité](#sécurité)
6. [Déploiement](#déploiement)
7. [Maintenance et évolution](#maintenance-et-évolution)

## Architecture

L'intégration du microservice Social-Platform-Video suit une architecture microservices qui permet une séparation claire des responsabilités et une évolutivité optimale.

### Diagramme d'architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Frontend       │◄───►│  Backend-NestJS │◄───►│  Social-Platform│
│  (React)        │     │  (Gateway)      │     │  -Video         │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Flux de données

1. Le frontend communique avec le backend via des API REST
2. Le backend agit comme une passerelle (gateway) vers le microservice Social-Platform-Video
3. Le microservice Social-Platform-Video gère toutes les fonctionnalités sociales et vidéo
4. Les événements sont propagés entre les services via un système de messagerie asynchrone

## Fonctionnalités implémentées

### 1. Vidéoconférences pour le matching

- Création de salles de vidéoconférence pour les matchings entre partenaires et organisateurs
- Gestion du cycle de vie des salles (programmation, démarrage, fin)
- Contrôle d'accès basé sur les rôles (hôte, participant)
- Intégration avec le système d'analyse pour le suivi des conversions

### 2. Livestreams

- Création et gestion de livestreams (en direct, programmés, terminés)
- Chat en direct pour l'interaction avec les spectateurs
- Enregistrement automatique pour visionnage ultérieur
- Statistiques de visionnage et d'engagement

### 3. Blog

- Création et gestion d'articles de blog
- Système de commentaires et de likes
- Catégorisation par tags
- Statistiques de lecture et d'engagement

### 4. Analyses sociales

- Tableau de bord d'analyse pour suivre les performances du contenu
- Statistiques d'engagement (vues, likes, commentaires)
- Identification du contenu populaire
- Tendances d'engagement dans le temps

## Intégration backend

### Services implémentés

1. **MatchingVideoService**
   - Intégration avec le service de matching pour les vidéoconférences
   - Gestion des salles de vidéoconférence pour les matchings

2. **LivestreamService**
   - Création et gestion des livestreams
   - Gestion des messages du chat

3. **BlogService**
   - Création et gestion des articles de blog
   - Gestion des commentaires et des likes

4. **SocialAnalyticsService**
   - Collecte et analyse des données d'engagement
   - Génération de rapports et de statistiques

### Contrôleurs API

1. **MatchingVideoController**
   - Endpoints pour la gestion des vidéoconférences de matching

2. **LivestreamController**
   - Endpoints pour la gestion des livestreams

3. **BlogController**
   - Endpoints pour la gestion des articles de blog

4. **SocialAnalyticsController**
   - Endpoints pour l'accès aux analyses sociales

## Intégration frontend

### Services clients

1. **matchingVideoService**
   - Communication avec l'API backend pour les vidéoconférences de matching

2. **livestreamService**
   - Communication avec l'API backend pour les livestreams

3. **blogService**
   - Communication avec l'API backend pour les articles de blog

4. **socialAnalyticsService**
   - Communication avec l'API backend pour les analyses sociales

### Composants UI

1. **Vidéoconférences**
   - `MatchingVideoRooms`: Affichage des salles de vidéoconférence pour un matching
   - `MatchingVideoRoomForm`: Formulaire de création de salle de vidéoconférence

2. **Livestreams**
   - `LivestreamList`: Liste des livestreams avec filtrage
   - `LivestreamPage`: Page de détail d'un livestream avec chat en direct

3. **Blog**
   - `BlogPostList`: Liste des articles de blog avec filtrage
   - `BlogPostPage`: Page de détail d'un article de blog avec commentaires

4. **Analyses**
   - `SocialAnalyticsDashboard`: Tableau de bord d'analyse sociale
   - `SocialAnalyticsPage`: Page d'analyse sociale complète

## Sécurité

### Authentification et autorisation

- Utilisation de JWT pour l'authentification
- Contrôle d'accès basé sur les rôles (RBAC)
- Vérification des permissions pour chaque action

### Protection des données

- Validation des entrées utilisateur
- Protection contre les attaques CSRF et XSS
- Chiffrement des communications

## Déploiement

### Configuration

1. **Variables d'environnement**
   - `SOCIAL_PLATFORM_VIDEO_URL`: URL du microservice Social-Platform-Video
   - `SOCIAL_PLATFORM_VIDEO_API_KEY`: Clé API pour l'authentification

2. **Dépendances**
   - NestJS HttpModule pour les communications inter-services
   - EventEmitter pour la gestion des événements

### Intégration avec Kubernetes

1. **Services**
   - Définition du service Social-Platform-Video
   - Configuration du service de découverte

2. **Déploiements**
   - Configuration des déploiements pour le microservice
   - Gestion des ressources et de la scalabilité

## Maintenance et évolution

### Surveillance

- Mise en place de métriques pour suivre les performances
- Alertes en cas de problèmes
- Journalisation des événements importants

### Évolutions futures

1. **Fonctionnalités avancées de livestream**
   - Streaming multi-participants
   - Partage d'écran et tableau blanc
   - Enregistrement et montage automatisés

2. **Améliorations du blog**
   - Éditeur WYSIWYG avancé
   - Planification des publications
   - SEO automatisé

3. **Analyses avancées**
   - Prédiction des tendances
   - Recommandations de contenu
   - Segmentation des utilisateurs

4. **Intégration avec d'autres plateformes**
   - Partage sur les réseaux sociaux
   - Diffusion simultanée sur YouTube, Facebook, etc.
   - Importation de contenu externe
