# CI/CD Pipeline for Unified Frontend

This document describes the Continuous Integration and Continuous Deployment (CI/CD) pipeline for the unified frontend project that spans web, iOS, and Android platforms.

## Overview

The unified CI/CD pipeline automates the building, testing, and deployment of our frontend applications across all platforms. It ensures consistent quality and streamlines the release process.

## Pipeline Structure

The pipeline is implemented using GitHub Actions and is defined in `.github/workflows/unified-frontend-ci-cd.yml`. It consists of the following stages:

1. **Lint**: Validates code quality and style
2. **Test**: Runs unit and integration tests
3. **Security Scan**: Performs security vulnerability checks
4. **Build**: Creates platform-specific builds (web, iOS, Android)
5. **Deploy**: Deploys applications to their respective platforms
6. **Version Management**: Handles versioning across platforms

## Workflow Triggers

The pipeline is triggered by:
- Push to `main` or `develop` branches (affecting packages directory)
- Pull requests to `main` or `develop` branches (affecting packages directory)
- Manual trigger via GitHub Actions UI with customizable parameters

## Platform-Specific Builds

### Web
- Built using Turborepo with the filter `--filter=web`
- Deployed to Firebase Hosting

### iOS
- Built using Xcode and CocoaPods
- Deployed to App Store using Fastlane

### Android
- Built using Gradle
- Deployed to Play Store using Fastlane

## Environment Configuration

The pipeline supports three environments:
- `test`: For testing and development
- `staging`: For pre-production validation
- `production`: For production releases

## Required Secrets

The following secrets need to be configured in GitHub:

### General
- `TURBO_TOKEN`: For Turborepo remote caching
- `TURBO_TEAM`: For Turborepo team identification

### Web Deployment
- `FIREBASE_SERVICE_ACCOUNT`: Firebase service account credentials

### iOS Deployment
- `APP_STORE_CONNECT_API_KEY_KEY_ID`: App Store Connect API Key ID
- `APP_STORE_CONNECT_API_KEY_ISSUER_ID`: App Store Connect API Key Issuer ID
- `APP_STORE_CONNECT_API_KEY_KEY`: App Store Connect API Key
- `MATCH_PASSWORD`: Password for match code signing
- `MATCH_GIT_BASIC_AUTHORIZATION`: Git authorization for match repository

### Android Deployment
- `GOOGLE_PLAY_SERVICE_ACCOUNT_JSON`: Google Play service account credentials

## Manual Workflow Execution

To manually trigger the workflow:

1. Go to the Actions tab in the GitHub repository
2. Select "Unified Frontend CI/CD Pipeline"
3. Click "Run workflow"
4. Select the desired environment and platform options
5. Click "Run workflow"

## Version Management

The pipeline automatically handles versioning using Lerna:

- Versions are bumped automatically on successful production deployments
- Version changes are committed and tagged in git
- Version tags follow semantic versioning

## Extending the Pipeline

To add new platforms or modify the existing pipeline:

1. Edit the `.github/workflows/unified-frontend-ci-cd.yml` file
2. Add new jobs or modify existing ones as needed
3. Test changes by creating a pull request

## Troubleshooting

### Common Issues

- **Build Failures**: Check the specific platform build logs for errors
- **Deployment Failures**: Verify that all required secrets are properly configured
- **Version Management Issues**: Ensure git credentials are properly set up

### Logs and Artifacts

Build artifacts and logs are available in the GitHub Actions UI for each workflow run. Test coverage reports and security scan results are uploaded as artifacts and can be downloaded for further analysis.

## Best Practices

- Always run tests locally before pushing changes
- Monitor workflow runs for any failures
- Keep secrets up to date
- Regularly update dependencies to address security vulnerabilities

## Future Improvements

- Add performance testing to the pipeline
- Implement canary deployments
- Add automated UI testing across platforms
- Integrate with monitoring and alerting systems