# Guide de Surveillance et Alertes

## Tableaux de Bord

### Accès aux Dashboards

1. Grafana : https://metrics.retreatandbe.com
2. Prometheus : https://prometheus.retreatandbe.com
3. Alertmanager : https://alerts.retreatandbe.com

### Métriques Principales

| Métrique | Description | Seuil d'Alerte |
|----------|-------------|----------------|
| CPU Usage | Utilisation CPU | > 80% |
| Memory | Utilisation mémoire | > 85% |
| Error Rate | Taux d'erreurs | > 1% |
| Response Time | Temps de réponse | > 500ms |

## Configuration des Alertes

### Slack

Les alertes sont envoyées dans les canaux :
- #prod-alerts : Alertes critiques
- #prod-warnings : Avertissements
- #prod-info : Informations générales

### Email

Format des notifications :
```text
[ALERT] Service: {service_name}
Status: {status}
Description: {description}
Actions: {recommended_actions}
```

## Réponse aux Incidents

1. Consul<PERSON><PERSON> le [Guide d'Incident Response](./incident-response.md)
2. Su<PERSON>z la procédure d'escalade appropriée
3. Documentez l'incident dans Jira