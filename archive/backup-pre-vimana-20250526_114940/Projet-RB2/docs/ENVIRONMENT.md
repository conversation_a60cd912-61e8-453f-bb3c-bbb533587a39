# Variables d'Environnement

Ce document décrit les variables d'environnement nécessaires pour le projet.

## Frontend (.env)

### Configuration API
- `VITE_API_URL` : URL de l'API backend (ex: http://localhost:3000/api)
- `VITE_WS_URL` : URL du serveur WebSocket (ex: ws://localhost:3000)

### Feature Flags
- `VITE_USE_MOCK_DATA` : Activer les données mockées (true/false)
- `VITE_ENABLE_LIVESTREAM` : Activer la fonctionnalité de livestream (true/false)
- `VITE_ENABLE_NFT` : Activer les fonctionnalités NFT (true/false)
- `VITE_ENABLE_SOCIAL` : Activer les fonctionnalités sociales (true/false)
- `VITE_ENABLE_AI` : Activer les fonctionnalités IA (true/false)

### Configuration Web3
- `VITE_IPFS_GATEWAY` : URL de la passerelle IPFS
- `VITE_CHAIN_ID` : ID de la chaîne blockchain
- `VITE_CONTRACT_ADDRESS` : Adresse du smart contract
- `VITE_WALLETCONNECT_PROJECT_ID` : ID du projet WalletConnect

### Analytics et Monitoring
- `VITE_SENTRY_DSN` : DSN pour Sentry error tracking
- `VITE_GA_MEASUREMENT_ID` : ID de mesure Google Analytics

## Configuration des Secrets GitHub

Pour configurer les secrets dans GitHub Actions :

1. Allez dans votre repository GitHub
2. Cliquez sur "Settings" > "Secrets and variables" > "Actions"
3. Cliquez sur "New repository secret"
4. Ajoutez les secrets suivants :

```
VITE_API_URL=<votre_url_api>
VITE_SENTRY_DSN=<votre_dsn_sentry>
VITE_GA_MEASUREMENT_ID=<votre_id_ga>
```

### Environnements GitHub

Le workflow utilise deux environnements : staging et production. Pour chaque environnement, vous devrez configurer des secrets spécifiques :

1. Dans GitHub, allez dans "Settings" > "Environments"
2. Créez deux environnements : "staging" et "production"
3. Pour chaque environnement, ajoutez les secrets appropriés avec des valeurs différentes

#### Staging
```
VITE_API_URL=https://api-staging.votreapp.com
VITE_SENTRY_DSN=<dsn_staging>
VITE_GA_MEASUREMENT_ID=<ga_id_staging>
```

#### Production
```
VITE_API_URL=https://api.votreapp.com
VITE_SENTRY_DSN=<dsn_production>
VITE_GA_MEASUREMENT_ID=<ga_id_production>
```

## Développement Local

1. Copiez le fichier `.env.example` vers `.env` :
```bash
cp frontend/.env.example frontend/.env
```

2. Mettez à jour les valeurs dans votre `.env` local avec vos configurations de développement

3. Ne committez jamais le fichier `.env` dans le repository

## Notes de Sécurité

- Ne stockez jamais de secrets sensibles directement dans le code
- Utilisez toujours des variables d'environnement pour les configurations sensibles
- Vérifiez que `.env` est bien dans votre `.gitignore`
- Pour le développement local, utilisez `.env.local` qui ne sera pas commité
