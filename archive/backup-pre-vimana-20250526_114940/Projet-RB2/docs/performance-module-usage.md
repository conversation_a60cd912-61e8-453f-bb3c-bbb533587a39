# Guide d'utilisation du module de performance

Ce guide explique comment utiliser le module de performance dans votre application Retreat And Be.

## Table des matières

1. [Installation](#installation)
2. [Configuration](#configuration)
3. [Collecte de métriques](#collecte-de-métriques)
4. [Création d'alertes](#création-dalertes)
5. [Tableaux de bord](#tableaux-de-bord)
6. [Rapports](#rapports)
7. [Bonnes pratiques](#bonnes-pratiques)
8. [Dépannage](#dépannage)

## Installation

Le module de performance est déjà intégré dans l'application Backend-NestJS de Retreat And Be. Il est importé dans le module principal de l'application (`AppModule`) et est prêt à être utilisé.

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { PerformanceModule } from './modules/performance/performance.module';

@Module({
  imports: [
    // ...
    PerformanceModule,
    // ...
  ],
})
export class AppModule {}
```

## Configuration

Le module de performance est configurable via des variables d'environnement. Voici les principales variables d'environnement que vous pouvez définir dans votre fichier `.env` :

```env
# Configuration générale
METRICS_INTERVAL=60
METRICS_RETENTION=30
MAX_ALERTS=1000
MONITORING_ENABLED=true
ALERTS_ENABLED=true
NOTIFICATIONS_ENABLED=true

# Configuration des métriques HTTP
HTTP_METRICS_ENABLED=true
SLOW_REQUEST_THRESHOLD=1000
METRICS_EXCLUDED_ROUTES=/metrics,/health,/favicon.ico
DETAILED_ROUTE_METRICS=false

# Configuration des métriques de base de données
DATABASE_METRICS_ENABLED=true
SLOW_QUERY_THRESHOLD=500
DETAILED_QUERY_METRICS=false

# Configuration des métriques système
SYSTEM_METRICS_ENABLED=true
SYSTEM_METRICS_INTERVAL=60
HIGH_CPU_THRESHOLD=80
HIGH_MEMORY_THRESHOLD=80
HIGH_DISK_THRESHOLD=80

# Configuration des métriques personnalisées
CUSTOM_METRICS_ENABLED=true
CUSTOM_METRICS=

# Configuration des alertes HTTP
HTTP_ALERTS_ENABLED=true
HTTP_ERROR_RATE_THRESHOLD=5
HTTP_LATENCY_THRESHOLD=2000
HTTP_REQUEST_RATE_THRESHOLD=1000

# Configuration des alertes de base de données
DATABASE_ALERTS_ENABLED=true
DB_QUERY_LATENCY_THRESHOLD=1000
DB_ERROR_RATE_THRESHOLD=1
DB_CONNECTION_THRESHOLD=80

# Configuration des alertes système
SYSTEM_ALERTS_ENABLED=true
CPU_ALERT_THRESHOLD=90
MEMORY_ALERT_THRESHOLD=90
DISK_ALERT_THRESHOLD=90

# Configuration des alertes personnalisées
CUSTOM_ALERTS_ENABLED=true
CUSTOM_ALERTS=

# Configuration des notifications par email
EMAIL_NOTIFICATIONS_ENABLED=false
EMAIL_NOTIFICATION_RECIPIENTS=
EMAIL_NOTIFICATION_SEVERITY=error,critical

# Configuration des notifications Slack
SLACK_NOTIFICATIONS_ENABLED=false
SLACK_WEBHOOK_URL=
SLACK_NOTIFICATION_CHANNEL=#alerts
SLACK_NOTIFICATION_SEVERITY=warning,error,critical

# Configuration des notifications webhook
WEBHOOK_NOTIFICATIONS_ENABLED=false
WEBHOOK_URL=
WEBHOOK_NOTIFICATION_SEVERITY=warning,error,critical

# Configuration des tableaux de bord
DASHBOARDS_ENABLED=true
DASHBOARDS_BASE_URL=/dashboards
AVAILABLE_DASHBOARDS=overview,http,database,system,custom

# Configuration des tâches planifiées
METRICS_CLEANUP_INTERVAL=1440
ALERTS_CLEANUP_INTERVAL=1440
ALERT_CHECK_INTERVAL=5
REPORT_GENERATION_INTERVAL=1440
```

## Collecte de métriques

### Métriques système

Les métriques système sont collectées automatiquement par le module de performance. Vous n'avez pas besoin de faire quoi que ce soit pour les collecter.

### Métriques personnalisées

Vous pouvez collecter des métriques personnalisées en injectant le service `MetricsService` dans vos services et en appelant la méthode `storeMetric` :

```typescript
import { Injectable } from '@nestjs/common';
import { MetricsService } from '../modules/performance/services/metrics.service';
import { MetricType } from '../modules/performance/interfaces/metric.interface';

@Injectable()
export class YourService {
  constructor(private readonly metricsService: MetricsService) {}

  async doSomething() {
    // Faire quelque chose...

    // Collecter une métrique personnalisée
    await this.metricsService.storeMetric({
      name: 'your.custom.metric',
      description: 'Your custom metric',
      type: MetricType.COUNTER,
      value: 1,
      unit: 'count',
      timestamp: new Date(),
      tags: { type: 'custom', category: 'your-category' },
    });
  }
}
```

### Types de métriques

Le module de performance prend en charge quatre types de métriques :

- **GAUGE** : Une valeur qui peut augmenter ou diminuer (ex: utilisation CPU)
- **COUNTER** : Une valeur qui ne peut qu'augmenter (ex: nombre de requêtes)
- **HISTOGRAM** : Une distribution de valeurs (ex: temps de réponse)
- **SUMMARY** : Un résumé de valeurs (ex: percentiles de temps de réponse)

### Tags de métriques

Les tags sont des paires clé-valeur qui permettent de filtrer et d'agréger les métriques. Ils sont stockés sous forme de JSON dans la base de données.

Exemples de tags :
- `{ type: 'system', component: 'cpu' }`
- `{ type: 'api', endpoint: '/users', method: 'GET' }`
- `{ type: 'database', query: 'SELECT', table: 'users' }`

## Création d'alertes

### Règles d'alerte

Vous pouvez créer des règles d'alerte via l'API ou en injectant le service `AlertService` dans vos services :

```typescript
import { Injectable } from '@nestjs/common';
import { AlertService } from '../modules/performance/services/alert.service';
import { AlertSeverity, AlertSource, AlertCondition } from '../modules/performance/interfaces/alert.interface';

@Injectable()
export class YourService {
  constructor(private readonly alertService: AlertService) {}

  async createAlertRule() {
    // Créer une règle d'alerte
    const rule = await this.alertService.createAlertRule({
      name: 'Your Custom Alert',
      description: 'Alert when your custom metric exceeds threshold',
      severity: AlertSeverity.WARNING,
      source: AlertSource.CUSTOM,
      metric: 'your.custom.metric',
      condition: AlertCondition.GREATER_THAN,
      threshold: 100,
      duration: 300,
      cooldown: 1800,
      tags: { type: 'custom', category: 'your-category' },
    });

    return rule;
  }
}
```

### Conditions d'alerte

Le module de performance prend en charge six conditions d'alerte :

- **GREATER_THAN** : La valeur est supérieure au seuil
- **GREATER_THAN_OR_EQUAL** : La valeur est supérieure ou égale au seuil
- **LESS_THAN** : La valeur est inférieure au seuil
- **LESS_THAN_OR_EQUAL** : La valeur est inférieure ou égale au seuil
- **EQUAL** : La valeur est égale au seuil
- **NOT_EQUAL** : La valeur n'est pas égale au seuil

### Sévérité d'alerte

Le module de performance prend en charge quatre niveaux de sévérité :

- **INFO** : Information
- **WARNING** : Avertissement
- **ERROR** : Erreur
- **CRITICAL** : Critique

### Statut d'alerte

Une alerte peut avoir trois statuts :

- **ACTIVE** : L'alerte est active
- **ACKNOWLEDGED** : L'alerte a été acquittée
- **RESOLVED** : L'alerte a été résolue

### Notifications

Le module de performance peut envoyer des notifications par différents canaux :

- **Email** : Envoie un email aux destinataires configurés
- **Slack** : Envoie un message à un canal Slack
- **Webhook** : Envoie une requête HTTP à une URL configurée

## Tableaux de bord

### Création de tableaux de bord

Vous pouvez créer des tableaux de bord via l'API ou en injectant le service `DashboardService` dans vos services :

```typescript
import { Injectable } from '@nestjs/common';
import { DashboardService } from '../modules/performance/services/dashboard.service';
import { DashboardType, PanelType, ChartType } from '../modules/performance/dto/create-dashboard.dto';

@Injectable()
export class YourService {
  constructor(private readonly dashboardService: DashboardService) {}

  async createDashboard(userId: string) {
    // Créer un tableau de bord
    const dashboard = await this.dashboardService.createDashboard({
      name: 'Your Custom Dashboard',
      description: 'Your custom dashboard description',
      type: DashboardType.CUSTOM,
      layout: {
        columns: 12,
        rows: 24,
        gridGap: 8,
      },
      panels: [
        {
          title: 'Your Custom Metric',
          type: PanelType.CHART,
          position: { x: 0, y: 0 },
          size: { width: 6, height: 8 },
          config: {
            chartType: ChartType.LINE,
            timeRange: { from: 'now-6h', to: 'now' },
            refreshInterval: 60,
            displayLegend: true,
          },
          metrics: ['your.custom.metric'],
        },
      ],
      isDefault: false,
      isPublic: true,
    }, userId);

    return dashboard;
  }
}
```

### Types de panneaux

Le module de performance prend en charge cinq types de panneaux :

- **CHART** : Un graphique
- **GAUGE** : Une jauge
- **TABLE** : Un tableau
- **TEXT** : Du texte
- **STAT** : Une statistique

### Types de graphiques

Le module de performance prend en charge cinq types de graphiques :

- **LINE** : Un graphique en ligne
- **BAR** : Un graphique en barres
- **PIE** : Un graphique en camembert
- **AREA** : Un graphique en aire
- **SCATTER** : Un graphique en nuage de points

## Rapports

### Génération de rapports

Vous pouvez générer des rapports de performance via l'API ou en injectant le service `PerformanceService` dans vos services :

```typescript
import { Injectable } from '@nestjs/common';
import { PerformanceService } from '../modules/performance/services/performance.service';

@Injectable()
export class YourService {
  constructor(private readonly performanceService: PerformanceService) {}

  async generateReport() {
    // Générer un rapport pour les dernières 24 heures
    const endDate = new Date();
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 1);

    const report = await this.performanceService.generateReport(startDate, endDate);

    return report;
  }
}
```

### Rapports automatiques

Le module de performance génère automatiquement des rapports quotidiens à minuit. Ces rapports sont stockés dans la base de données et peuvent être consultés ultérieurement.

## Bonnes pratiques

### Métriques

- Utilisez des noms de métriques cohérents et descriptifs (ex: `system.cpu.usage`, `api.request.latency`).
- Ajoutez des tags aux métriques pour faciliter le filtrage et l'agrégation.
- Limitez le nombre de métriques collectées pour éviter de surcharger la base de données.
- Nettoyez régulièrement les anciennes métriques pour économiser de l'espace de stockage.

### Alertes

- Définissez des seuils d'alerte appropriés pour éviter les faux positifs.
- Utilisez des durées et des temps de refroidissement adaptés pour éviter les alertes répétitives.
- Configurez les canaux de notification en fonction de la sévérité des alertes.
- Acquittez et résolvez les alertes rapidement pour maintenir la visibilité sur les problèmes actifs.

### Tableaux de bord

- Créez des tableaux de bord spécifiques pour différents cas d'utilisation.
- Organisez les panneaux de manière logique et cohérente.
- Utilisez des intervalles de rafraîchissement adaptés à l'importance des métriques.
- Partagez les tableaux de bord avec les utilisateurs concernés.

## Dépannage

### Problèmes courants

#### Les métriques ne sont pas collectées

- Vérifiez que le module de performance est correctement importé dans le module principal de l'application.
- Vérifiez que la variable d'environnement `MONITORING_ENABLED` est définie à `true`.
- Vérifiez que la base de données est accessible.
- Vérifiez les logs pour les erreurs.

#### Les alertes ne sont pas déclenchées

- Vérifiez que la variable d'environnement `ALERTS_ENABLED` est définie à `true`.
- Vérifiez que les règles d'alerte sont correctement configurées.
- Vérifiez que les métriques sont collectées.
- Vérifiez les logs pour les erreurs.

#### Les notifications ne sont pas envoyées

- Vérifiez que la variable d'environnement `NOTIFICATIONS_ENABLED` est définie à `true`.
- Vérifiez que les canaux de notification sont correctement configurés.
- Vérifiez que les niveaux de sévérité des notifications sont correctement configurés.
- Vérifiez les logs pour les erreurs.

### Logs

Le module de performance utilise le système de journalisation de NestJS. Vous pouvez consulter les logs pour diagnostiquer les problèmes.

Les services du module de performance utilisent les loggers suivants :
- `PerformanceService`
- `MetricsService`
- `AlertService`
- `NotificationService`
- `SystemMonitorService`
- `DashboardService`

### Débuggage

Pour débugger le module de performance, vous pouvez activer le mode debug en définissant la variable d'environnement `DEBUG` à `true`. Cela affichera des informations supplémentaires dans les logs.

```env
DEBUG=true
```

## Conclusion

Le module de performance est un outil puissant pour surveiller et améliorer les performances de votre application Backend-NestJS de Retreat And Be. En suivant ce guide, vous devriez être en mesure d'utiliser efficacement toutes les fonctionnalités du module.

Si vous avez des questions ou des problèmes, n'hésitez pas à consulter la documentation détaillée du module ou à contacter l'équipe de développement.
