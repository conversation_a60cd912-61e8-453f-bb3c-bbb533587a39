# Documentation de Conformité PCI DSS - Retreat And Be

**Version :** 1.0  
**Date de publication :** Novembre 2023  
**Dernière mise à jour :** Octobre 2023

## Table des matières

1. [Introduction](#1-introduction)
2. [Portée PCI DSS](#2-portée-pci-dss)
3. [Conformité aux 12 exigences](#3-conformité-aux-12-exigences)
4. [Architecture de sécurité des paiements](#4-architecture-de-sécurité-des-paiements)
5. [Gestion des risques](#5-gestion-des-risques)
6. [Procédures de maintenance](#6-procédures-de-maintenance)
7. [Plan d'intervention en cas d'incident](#7-plan-dintervention-en-cas-dincident)
8. [Formation et sensibilisation](#8-formation-et-sensibilisation)
9. [Procédures d'audit](#9-procédures-daudit)
10. [Annexes](#10-annexes)

## 1. Introduction

Ce document détaille les mesures mises en place par Retreat And Be pour se conformer à la norme PCI DSS (Payment Card Industry Data Security Standard) version 4.0. La conformité PCI DSS est essentielle pour toute organisation qui traite, stocke ou transmet des données de cartes de paiement, afin d'assurer la sécurité de ces informations sensibles.

### 1.1 Objectif du document

- Documenter les mesures de conformité PCI DSS implémentées
- Servir de référence pour les audits internes et externes
- Fournir des directives aux équipes techniques et commerciales
- Démontrer notre engagement envers la sécurité des données de paiement

### 1.2 Niveau de conformité

Retreat And Be est classifié comme un marchand de niveau 3, traitant entre 20 000 et 1 million de transactions par carte par an. Nous nous conformons aux exigences correspondant à ce niveau, y compris l'auto-évaluation annuelle (SAQ D) et les scans trimestriels de vulnérabilités.

## 2. Portée PCI DSS

### 2.1 Environnement des données des titulaires de cartes (CDE)

Notre environnement des données des titulaires de cartes est strictement limité et segmenté du reste de notre infrastructure. Nous utilisons un modèle de tokenisation où les données de cartes ne sont jamais stockées dans nos systèmes.

#### 2.1.1 Composants inclus dans la portée

- Passerelles d'API vers les processeurs de paiement
- Journaux de transactions (sans données de cartes)
- Interfaces utilisateur pour la saisie de paiements

#### 2.1.2 Flux de données de paiement

1. L'utilisateur saisit les données de carte dans un formulaire sécurisé
2. Les données sont tokenisées directement par notre processeur de paiement (Stripe/PayPal)
3. Notre backend ne reçoit que le token et les métadonnées de transaction
4. Les transactions sont traitées à l'aide de ces tokens, sans jamais manipuler les données de cartes

### 2.2 Diagramme de réseau

```
┌───────────────┐     ┌───────────────┐      ┌────────────────┐
│ Client Browser │────▶│ Frontend SPA  │─────▶│ API Gateway    │
└───────┬───────┘     └───────┬───────┘      └────────┬───────┘
        │                     │                       │
        │                     ▼                       ▼
        │             ┌───────────────┐      ┌────────────────┐
        └────────────▶│ Payment       │      │ Backend        │
                      │ Service       │      │ Services       │
                      │ Provider (PSP)│      │ (token only)   │
                      └───────────────┘      └────────────────┘
                             │                       │
                             ▼                       ▼
                      ┌───────────────┐      ┌────────────────┐
                      │ Card Networks │      │ Database       │
                      │               │      │ (no card data) │
                      └───────────────┘      └────────────────┘
```

### 2.3 Réduction de la portée

Nous avons mis en œuvre plusieurs stratégies pour réduire la portée PCI DSS :

1. **Tokenisation complète** : Les données de carte sont tokenisées avant d'atteindre nos serveurs
2. **Intégration avec iFrame** : Utilisation des iFrames de Stripe/PayPal pour la saisie des cartes
3. **Segmentation réseau** : Isolation stricte des systèmes de paiement
4. **Pas de stockage** : Aucune donnée de carte n'est stockée dans nos systèmes

## 3. Conformité aux 12 exigences

### 3.1 Construire et maintenir un réseau sécurisé

#### Exigence 1 : Installer et maintenir une configuration de pare-feu pour protéger les données

- Mise en place de pare-feux à plusieurs niveaux (périmètre, application, base de données)
- Segmentation réseau avec des VLAN dédiés pour les composants de paiement
- Règles de pare-feu restrictives basées sur le principe du moindre privilège
- Revue trimestrielle de la configuration des pare-feux

#### Exigence 2 : Ne pas utiliser les mots de passe et autres paramètres de sécurité fournis par défaut

- Procédure de changement systématique de tous les mots de passe par défaut
- Inventaire de tous les composants du système avec vérification des paramètres de sécurité
- Durcissement des systèmes selon les recommandations CIS
- Audits réguliers des configurations de sécurité

### 3.2 Protéger les données des titulaires de cartes

#### Exigence 3 : Protéger les données stockées des titulaires de cartes

- Utilisation de la tokenisation pour éviter le stockage des données de cartes
- Mise en œuvre du chiffrement TLS 1.2+ pour toutes les communications
- Politique de conservation des données minimisant les informations stockées
- Procédure de suppression sécurisée des données

#### Exigence 4 : Chiffrer la transmission des données des titulaires de cartes sur les réseaux ouverts et publics

- Utilisation exclusive de TLS 1.2+ pour toutes les transmissions de données
- Validation des certificats avec HSTS activé
- Surveillance des tentatives de connexion non chiffrées
- Scan régulier des vulnérabilités sur les protocoles de chiffrement

### 3.3 Maintenir un programme de gestion des vulnérabilités

#### Exigence 5 : Protéger tous les systèmes contre les logiciels malveillants

- Déploiement de solutions antivirus/anti-malware sur tous les systèmes
- Mises à jour automatiques des définitions de virus
- Scans de malware programmés
- Procédures de réponse aux incidents de malware

#### Exigence 6 : Développer et maintenir des systèmes et des applications sécurisés

- Programme de développement sécurisé (SDLC) avec revue de code
- Tests de sécurité automatisés dans le pipeline CI/CD
- Analyse statique et dynamique du code
- Mise en œuvre de pratiques OWASP Top 10
- Patching régulier des vulnérabilités

### 3.4 Mettre en œuvre des mesures de contrôle d'accès strictes

#### Exigence 7 : Restreindre l'accès aux données selon le besoin de connaître

- Contrôles d'accès basés sur les rôles (RBAC)
- Principe du moindre privilège pour tous les accès
- Revue trimestrielle des droits d'accès
- Séparation des tâches pour les fonctions critiques

#### Exigence 8 : Identifier et authentifier l'accès aux composants du système

- Authentification multi-facteurs (MFA) pour tous les accès administratifs
- Politique de mots de passe forts
- Verrouillage de compte après tentatives infructueuses
- Gestion centralisée des identités avec journalisation

#### Exigence 9 : Restreindre l'accès physique aux données des titulaires de cartes

- Contrôles d'accès physique aux centres de données
- Surveillance vidéo 24/7
- Procédures d'escorte pour les visiteurs
- Destruction sécurisée des médias physiques

### 3.5 Surveiller et tester régulièrement les réseaux

#### Exigence 10 : Suivre et surveiller tous les accès aux ressources réseau et aux données

- Journalisation centralisée de tous les événements de sécurité
- Surveillance en temps réel avec alertes automatisées
- Conservation des journaux pendant au moins un an
- Révision quotidienne des événements de sécurité critiques

#### Exigence 11 : Tester régulièrement les systèmes et processus de sécurité

- Scans trimestriels de vulnérabilités par un ASV approuvé
- Tests de pénétration annuels
- Surveillance de l'intégrité des fichiers
- Tests d'intrusion basés sur les méthodologies OWASP

### 3.6 Maintenir une politique de sécurité de l'information

#### Exigence 12 : Maintenir une politique qui traite de la sécurité de l'information

- Politique de sécurité globale documentée
- Procédures opérationnelles pour chaque exigence PCI DSS
- Formation annuelle de sensibilisation à la sécurité
- Évaluation des risques annuelle

## 4. Architecture de sécurité des paiements

### 4.1 Modèle de tokenisation

Notre plateforme utilise un modèle de tokenisation avancé qui élimine la nécessité de stocker ou de traiter directement les données de cartes de paiement :

```javascript
// Extrait du code d'intégration (frontend)
const stripe = Stripe('pk_live_...');
const elements = stripe.elements();
const card = elements.create('card', { 
  style: cardStyle,
  hidePostalCode: false
});

// Lorsque l'utilisateur soumet le paiement
const handleSubmit = async (event) => {
  event.preventDefault();
  setIsProcessing(true);
  
  // La tokenisation se fait côté client via l'API Stripe
  const { token, error } = await stripe.createToken(card);
  
  if (error) {
    setError(error.message);
    setIsProcessing(false);
    return;
  }
  
  // Seul le token est envoyé à notre backend, jamais les données de carte
  const response = await api.post('/payments/process', {
    bookingId: bookingId,
    amount: totalAmount,
    paymentToken: token.id,  // Uniquement le token
    currency: 'EUR'
  });
  
  // Traitement de la réponse...
}
```

### 4.2 Segmentation réseau

La segmentation réseau est implémentée selon le schéma suivant :

```
┌───────────────────────────────────────────────────────────┐
│                   Zone Internet publique                  │
└─────────────────────────────┬─────────────────────────────┘
                              │
┌─────────────────────────────▼─────────────────────────────┐
│                       Zone DMZ                            │
│  ┌─────────────┐    ┌──────────────┐   ┌──────────────┐   │
│  │ Load        │    │ WAF          │   │ API          │   │
│  │ Balancer    ├───▶│ (ModSecurity)├──▶│ Gateway      │   │
│  └─────────────┘    └──────────────┘   └──────┬───────┘   │
└──────────────────────────────────────────────┬────────────┘
                                               │
┌──────────────────────────────────────────────▼────────────┐
│                   Zone Application                         │
│  ┌─────────────┐    ┌──────────────┐   ┌──────────────┐   │
│  │ Application │    │ Payment      │   │ Autres       │   │
│  │ Services    │    │ Service      │   │ Services     │   │
│  └──────┬──────┘    └──────┬───────┘   └──────────────┘   │
└─────────┬────────────────────────────────────────────────┘
          │                  │
┌─────────▼──────────────────▼────────────────────────────┐
│                   Zone Base de données                   │
│  ┌─────────────┐    ┌──────────────┐                    │
│  │ Base de     │    │ Base des     │                    │
│  │ données     │    │ transactions │                    │
│  │ principale  │    │ (sans PAN)   │                    │
│  └─────────────┘    └──────────────┘                    │
└───────────────────────────────────────────────────────┘
```

### 4.3 Chiffrement et protection des données

- TLS 1.2+ pour toutes les communications avec une préférence pour TLS 1.3
- Configuration des suites de chiffrement selon les recommandations NIST
- HSTS avec preload pour prévenir les attaques de déclassement
- Rotation automatique des certificats avec alertes d'expiration

## 5. Gestion des risques

### 5.1 Évaluation des risques

Une évaluation des risques complète est réalisée annuellement et après tout changement significatif des systèmes de paiement. Notre méthodologie inclut :

1. Identification des actifs et de leur valeur
2. Identification des menaces et des vulnérabilités
3. Évaluation de la probabilité et de l'impact
4. Détermination du niveau de risque inhérent
5. Identification des contrôles existants
6. Évaluation du risque résiduel
7. Plan de traitement des risques

### 5.2 Matrice de risques

| ID | Risque | Probabilité | Impact | Niveau | Contrôles | Risque résiduel |
|----|--------|-------------|--------|--------|-----------|-----------------|
| R1 | Accès non autorisé aux systèmes de paiement | Faible | Critique | Élevé | MFA, segmentation, surveillance | Faible |
| R2 | Interception des données de cartes | Faible | Critique | Élevé | TLS 1.3, tokenisation, pas de stockage | Très faible |
| R3 | Vulnérabilités dans les applications | Moyenne | Élevé | Élevé | SAST/DAST, pentests, secure SDLC | Faible |
| R4 | Malware sur les systèmes | Faible | Élevé | Moyen | Antivirus, durcissement, monitoring | Très faible |
| R5 | Usurpation d'identité | Moyenne | Élevé | Élevé | MFA, formation, journalisation | Faible |

## 6. Procédures de maintenance

### 6.1 Gestion des vulnérabilités

Notre programme de gestion des vulnérabilités comprend :

- Scans automatisés hebdomadaires avec Nessus et OWASP ZAP
- Scans trimestriels par un ASV (Approved Scanning Vendor)
- Évaluation des vulnérabilités selon le CVSS
- Délais de correction basés sur la criticité :
  - Critique : 24 heures
  - Élevée : 7 jours
  - Moyenne : 30 jours
  - Faible : 90 jours

### 6.2 Gestion des correctifs

```bash
# Script de vérification automatique des correctifs
#!/bin/bash
# check_patches.sh

CRITICAL_UPDATES=$(apt list --upgradable 2>/dev/null | grep -i security | wc -l)

if [ $CRITICAL_UPDATES -gt 0 ]; then
  echo "ALERTE: $CRITICAL_UPDATES mises à jour de sécurité disponibles"
  apt list --upgradable | grep -i security | mail -s "Correctifs de sécurité requis" <EMAIL>
  exit 1
else
  echo "Aucune mise à jour de sécurité critique en attente"
  exit 0
fi
```

Ce script est exécuté quotidiennement par cron sur tous les serveurs.

### 6.3 Revue des configurations

Les configurations des systèmes critiques sont vérifiées mensuellement pour s'assurer qu'elles n'ont pas été modifiées de manière non autorisée :

```bash
# Vérification de l'intégrité des fichiers de configuration
find /etc/nginx /etc/apache2 /etc/ssl -type f -name "*.conf" | xargs md5sum > /var/log/config_checksums.new
diff /var/log/config_checksums.old /var/log/config_checksums.new || echo "ALERTE: Modifications de configuration détectées" | mail -s "Alerte de configuration" <EMAIL>
mv /var/log/config_checksums.new /var/log/config_checksums.old
```

## 7. Plan d'intervention en cas d'incident

### 7.1 Classification des incidents

| Niveau | Description | Exemple | Temps de réponse |
|--------|-------------|---------|------------------|
| 1 - Critique | Impact sur les données de cartes | Compromission, fuite de données | Immédiat (24/7) |
| 2 - Élevé | Atteinte potentielle à la sécurité | Accès non autorisé, malware | < 2 heures |
| 3 - Moyen | Incident de sécurité mineur | Tentative d'intrusion échouée | < 8 heures |
| 4 - Faible | Événement de sécurité | Anomalie de configuration | < 24 heures |

### 7.2 Procédure de réponse

1. **Détection et alerte**
   - Surveillance continue des systèmes
   - Alertes automatisées en cas d'anomalie

2. **Évaluation et classification**
   - Analyse préliminaire de l'incident
   - Classification selon la matrice définie

3. **Confinement**
   - Isolation des systèmes affectés
   - Blocage des accès suspects

4. **Éradication**
   - Suppression de la menace
   - Correction des vulnérabilités exploitées

5. **Récupération**
   - Restauration des systèmes et services
   - Vérification de l'intégrité des données

6. **Analyse post-incident**
   - Investigation complète
   - Documentation et leçons apprises

7. **Notification**
   - Notification aux parties concernées
   - Communication avec les autorités si nécessaire

## 8. Formation et sensibilisation

### 8.1 Programme de formation

Tous les employés impliqués dans le traitement des paiements suivent :

- Formation initiale à l'embauche
- Formation annuelle de mise à jour
- Formations spécifiques pour les développeurs et administrateurs système
- Tests de phishing réguliers

### 8.2 Contenu de la formation

- Fondamentaux de la sécurité des données de cartes
- Exigences PCI DSS et responsabilités
- Reconnaissance des incidents de sécurité
- Procédures de réponse aux incidents
- Bonnes pratiques de sécurité quotidiennes

### 8.3 Sensibilisation continue

- Bulletin mensuel de sécurité
- Affichage de rappels de sécurité
- Partage des incidents de sécurité du secteur
- Programme de reconnaissance pour le signalement des problèmes de sécurité

## 9. Procédures d'audit

### 9.1 Audits internes

Des audits internes sont effectués trimestriellement pour vérifier la conformité continue :

- Revue des contrôles PCI DSS
- Tests des procédures de sécurité
- Vérification de la documentation
- Entretiens avec le personnel clé

### 9.2 Audits externes

- Scan trimestriel par un ASV (Approved Scanning Vendor)
- Test de pénétration annuel par un tiers qualifié
- Évaluation annuelle de la conformité PCI DSS

### 9.3 Rapports d'audit

Chaque audit génère un rapport formel comprenant :

- Périmètre et méthodologie
- Résumé des conclusions
- Détail des non-conformités
- Recommandations
- Plan d'action avec échéances

## 10. Annexes

### 10.1 Attestation de conformité (AOC)

Le document d'Attestation de Conformité est disponible sur demande et mis à jour annuellement après l'évaluation complète.

### 10.2 Inventaire des systèmes dans le périmètre

| ID | Système | Fonction | Version | Localisation | Responsable |
|----|---------|----------|---------|--------------|-------------|
| SRV01 | api-gateway | API Gateway | v3.2.1 | Cloud - Zone A | Équipe DevOps |
| SRV02 | payment-service | Service de paiement | v2.1.0 | Cloud - Zone A | Équipe DevOps |
| SRV03 | transaction-db | Base de données des transactions | PostgreSQL 16 | Cloud - Zone B | Équipe DBA |
| FW01 | main-firewall | Pare-feu principal | OPNsense 23.1 | Cloud - Périmètre | Équipe Sécurité |
| WAF01 | web-application-firewall | WAF | ModSecurity 3.0 | Cloud - DMZ | Équipe Sécurité |

### 10.3 Contacts importants

| Rôle | Nom | Email | Téléphone |
|------|-----|-------|-----------|
| Responsable sécurité (CISO) | Sophie Dubois | <EMAIL> | +33 6 12 34 56 78 |
| Responsable conformité PCI | Thomas Martin | <EMAIL> | +33 6 23 45 67 89 |
| Contact incident 24/7 | Équipe d'astreinte | <EMAIL> | +33 1 23 45 67 89 |

---

Document préparé par l'équipe Sécurité & Conformité de Retreat And Be.  
© 2023 Retreat And Be. Tous droits réservés.  
Classification : Confidentiel - Usage Interne 