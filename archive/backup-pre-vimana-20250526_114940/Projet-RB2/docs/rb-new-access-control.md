# Sécurité et gestion des accès au Backend R&B new

Ce document explique comment gérer la sécurité et les accès au Backend R&B new dans un environnement Kubernetes.

## Vue d'ensemble

La sécurité et la gestion des accès au Backend R&B new sont assurées par plusieurs mécanismes complémentaires :

1. **Authentification et autorisation** : Vérification de l'identité des utilisateurs et de leurs droits
2. **Contrôle d'accès réseau** : Limitation des communications réseau entre les services
3. **API Gateway** : Point d'entrée unique pour les API avec gestion des accès
4. **RBAC Kubernetes** : Contrôle d'accès basé sur les rôles pour les ressources Kubernetes
5. **Sécurité des conteneurs** : Restrictions des privilèges et isolation des conteneurs
6. **Politiques de sécurité des pods** : Application de règles de sécurité strictes pour les pods
7. **Détection d'intrusion** : Surveillance des comportements suspects avec Falco
8. **Gestion sécurisée des secrets** : Intégration avec HashiCorp Vault
9. **Analyse de vulnérabilités** : Scan des images avec Trivy
10. **Protection contre les attaques DDoS** : Limitation du taux de requêtes
11. **Protection contre les injections** : Filtrage des requêtes avec ModSecurity

## 1. Authentification et autorisation

### Intégration avec un fournisseur d'identité

R&B new peut s'intégrer avec différents fournisseurs d'identité :

- **Keycloak** (par défaut)
- **Auth0**
- **Okta**
- Autres fournisseurs compatibles OpenID Connect

Configuration dans `values.yaml` :

```yaml
auth:
  enabled: true
  provider: "keycloak"
  realm: "retreatandbe"
  clientId: "rb-new-backend"
  serverUrl: "https://auth.retreatandbe.com/auth"
```

### Middleware d'authentification

Un middleware d'authentification est déployé pour valider les jetons JWT et appliquer les politiques d'autorisation :

```yaml
auth:
  middleware:
    enabled: true
    replicaCount: 2
```

### Gestion des secrets

Les secrets d'authentification (clés client, clés JWT) sont gérés de manière sécurisée :

- Stockés dans des secrets Kubernetes
- Générés automatiquement si non spécifiés
- Rotation possible via des outils comme Vault

## 2. Contrôle d'accès réseau

### Network Policies

Les Network Policies Kubernetes définissent quels pods peuvent communiquer avec le Backend :

```yaml
networkPolicy:
  enabled: true
  allowFrontendAccess: true
  frontendNamespace: "default"
```

### Règles d'accès

- Accès limité aux services internes du même namespace
- Accès possible depuis le frontend (configurable)
- Accès depuis l'API Gateway ou l'Ingress Controller
- Accès externe limité et contrôlé

## 3. API Gateway

### Configuration de l'API Gateway

Un API Gateway (Kong, Ambassador, etc.) peut être déployé comme point d'entrée unique :

```yaml
apiGateway:
  enabled: true
  type: "kong"
  host: "api.retreatandbe.com"
```

### Fonctionnalités de l'API Gateway

- **Authentification** : Validation des jetons JWT
- **Rate limiting** : Limitation du nombre de requêtes
- **Transformation** : Modification des requêtes/réponses
- **Logging** : Journalisation des accès
- **Monitoring** : Surveillance des API

## 4. RBAC Kubernetes

### Rôles et autorisations

Le RBAC Kubernetes définit les autorisations des services sur les ressources Kubernetes :

```yaml
rbac:
  create: true
  allowConfigUpdate: false
  allowSecretUpdate: false
```

### Principes de moindre privilège

- Chaque service n'a accès qu'aux ressources dont il a besoin
- Permissions en lecture seule par défaut
- Permissions d'écriture limitées et explicites

## Exemples de configuration

### Configuration minimale pour le développement

```yaml
auth:
  enabled: true
  provider: "keycloak"
  realm: "retreatandbe"
  clientId: "rb-new-backend"
  middleware:
    enabled: true

networkPolicy:
  enabled: true
  allowFrontendAccess: true

apiGateway:
  enabled: false
```

### Configuration de production sécurisée

```yaml
auth:
  enabled: true
  provider: "keycloak"
  realm: "retreatandbe"
  clientId: "rb-new-backend"
  middleware:
    enabled: true
    replicaCount: 3

networkPolicy:
  enabled: true
  allowFrontendAccess: true
  allowIngressFromNamespace: true
  ingressNamespace: "ingress-nginx"
  allowExternalEgress: false

apiGateway:
  enabled: true
  type: "kong"
  host: "api.retreatandbe.com"

rbac:
  create: true
  allowConfigUpdate: false
  allowSecretUpdate: false
```

## 5. Sécurité des conteneurs

### SecurityContext avancé

Les conteneurs sont exécutés avec des restrictions strictes :

```yaml
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  seccompProfile:
    type: RuntimeDefault
```

### Isolation des conteneurs

- Exécution en tant qu'utilisateur non-root
- Système de fichiers en lecture seule
- Suppression de toutes les capabilities Linux
- Prévention de l'escalade de privilèges

## 6. Politiques de sécurité des pods

### PodSecurityPolicy

Application de règles de sécurité strictes pour tous les pods :

```yaml
podSecurityPolicy:
  enabled: true
  readOnlyRootFilesystem: true
```

### OPA/Gatekeeper

Validation des ressources Kubernetes selon des politiques définies :

```yaml
opa:
  enabled: true
  enforcementAction: deny
```

## 7. Détection d'intrusion

### Falco

Surveillance en temps réel des comportements suspects :

```yaml
falco:
  enabled: true
  alertOutput: stdout
  alertPriority: WARNING
```

### Règles de détection

- Détection des shells dans les conteneurs
- Détection des processus de gestion de paquets
- Détection des connexions vers des réseaux suspects
- Détection des modifications de fichiers système

## 8. Gestion sécurisée des secrets

### HashiCorp Vault

Intégration avec Vault pour la gestion des secrets :

```yaml
vault:
  enabled: true
  role: "rb-new"
  secretPath: "secret/data/rb-new"
```

### Injection de secrets

- Injection dynamique des secrets dans les pods
- Rotation automatique des secrets
- Audit des accès aux secrets

## 9. Analyse de vulnérabilités

### Trivy

Scan régulier des images pour détecter les vulnérabilités :

```yaml
trivy:
  enabled: true
  schedule: "0 2 * * *"
  severity: "CRITICAL,HIGH"
```

### Intégration CI/CD

- Scan des images pendant le processus de build
- Blocage du déploiement en cas de vulnérabilités critiques
- Rapports de vulnérabilités

## 10. Protection contre les attaques DDoS

### Rate Limiting

Limitation du nombre de requêtes par client :

```yaml
rateLimiter:
  enabled: true
  requestsPerSecond: "10"
  requestsPerMinute: "300"
  connections: "20"
```

### Whitelist

- Liste blanche d'IPs ou de réseaux de confiance
- Augmentation des limites pour les clients légitimes

## 11. Protection contre les injections

### ModSecurity

Filtrage des requêtes pour bloquer les attaques :

```yaml
modsecurity:
  enabled: true
  ruleEngine: "On"
  auditEngine: "RelevantOnly"
```

### Règles de sécurité

- Protection contre les injections SQL
- Protection contre les attaques XSS
- Protection contre les traversées de répertoire
- Protection contre les inclusions de fichiers

## Bonnes pratiques

1. **Principe du moindre privilège** : N'accorder que les permissions minimales nécessaires
2. **Défense en profondeur** : Utiliser plusieurs couches de sécurité
3. **Chiffrement des communications** : Utiliser TLS pour toutes les communications
4. **Rotation des secrets** : Changer régulièrement les clés et secrets
5. **Audit et logging** : Enregistrer et analyser les accès
6. **Mise à jour régulière** : Maintenir les dépendances à jour
7. **Tests de pénétration** : Réaliser des tests de sécurité réguliers
8. **Surveillance continue** : Mettre en place une surveillance 24/7

## Dépannage

### Problèmes d'authentification

Si les utilisateurs ne peuvent pas s'authentifier :

1. Vérifier la configuration du fournisseur d'identité
2. Vérifier les logs du middleware d'authentification
3. Vérifier la validité des jetons JWT

### Problèmes d'accès réseau

Si les services ne peuvent pas communiquer :

1. Vérifier les Network Policies
2. Vérifier les logs des services
3. Utiliser des outils comme `kubectl exec` pour tester la connectivité

### Problèmes d'API Gateway

Si l'API Gateway ne fonctionne pas correctement :

1. Vérifier la configuration de l'API Gateway
2. Vérifier les logs de l'API Gateway
3. Tester les endpoints directement et via l'API Gateway
