# Intégration vidéo pour Retreat-Pro-Matcher

## Introduction

L'intégration vidéo pour Retreat-Pro-Matcher permet aux partenaires et aux organisateurs de retraites de communiquer en temps réel via des vidéoconférences. Cette fonctionnalité s'appuie sur le microservice Social-Platform-Video pour offrir une expérience de communication fluide et sécurisée.

## Table des matières

1. [Architecture](#architecture)
2. [Fonctionnalités](#fonctionnalités)
3. [Intégration backend](#intégration-backend)
4. [Intégration frontend](#intégration-frontend)
5. [Sécurité](#sécurité)
6. [Bonnes pratiques](#bonnes-pratiques)
7. [Dépannage](#dépannage)

## Architecture

L'intégration vidéo repose sur une architecture microservices qui connecte le service Retreat-Pro-Matcher au microservice Social-Platform-Video. Voici les composants principaux :

1. **Backend-NestJS** : Contient les services et contrôleurs qui gèrent la logique métier liée aux vidéoconférences.
2. **Social-Platform-Video** : Microservice dédié à la gestion des salles de vidéoconférence et des sessions en direct.
3. **Front-Audrey-V1-Main-main** : Interface utilisateur qui permet aux utilisateurs d'interagir avec les fonctionnalités vidéo.

### Diagramme de flux

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Frontend       │◄───►│  Backend-NestJS │◄───►│  Social-Platform│
│  (React)        │     │  (Matching)     │     │  -Video         │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Fonctionnalités

### Création de salles de vidéoconférence

- Programmation de vidéoconférences à l'avance
- Configuration des paramètres de la salle (titre, description, durée, etc.)
- Protection par mot de passe (optionnel)
- Invitation automatique des participants concernés

### Gestion des salles

- Liste des salles de vidéoconférence pour un matching
- Détails d'une salle (participants, statut, liens, etc.)
- Modification des paramètres d'une salle
- Fermeture d'une salle

### Participation aux vidéoconférences

- Génération de liens de participation sécurisés
- Différenciation des rôles (hôte, participant)
- Interface de vidéoconférence intégrée
- Partage d'écran et chat textuel

### Analyse et suivi

- Enregistrement des événements liés aux vidéoconférences
- Statistiques d'utilisation
- Intégration avec le système d'analyse de matching

## Intégration backend

### Services

1. **MatchingVideoService** : Service principal qui gère l'interaction avec le microservice Social-Platform-Video.
   - Création de salles de vidéoconférence
   - Récupération des salles existantes
   - Génération de liens de participation
   - Gestion du cycle de vie des salles

2. **MatchingAnalyticsService** : Service qui enregistre les événements liés aux vidéoconférences pour l'analyse.
   - Enregistrement des créations de salles
   - Suivi des participations
   - Analyse des conversions

### Contrôleurs

1. **MatchingVideoController** : Contrôleur qui expose les endpoints API pour les fonctionnalités vidéo.
   - Création de salles (`POST /matching/video/rooms`)
   - Récupération des salles (`GET /matching/video/rooms/:partnerId/:retreatId`)
   - Détails d'une salle (`GET /matching/video/rooms/details/:roomId`)
   - Génération de liens de participation (`GET /matching/video/join/:roomId`)
   - Fermeture d'une salle (`POST /matching/video/rooms/:roomId/end`)

### Modèles de données

1. **MatchingVideoRoom** : Modèle qui représente une salle de vidéoconférence.
   - ID externe (référence à la salle dans le microservice Social-Platform-Video)
   - Informations de base (titre, description, etc.)
   - Paramètres de programmation (date, durée, etc.)
   - Statut et liens de participation

## Intégration frontend

### Services clients

1. **matchingVideoService** : Service client qui communique avec l'API backend.
   - Création de salles de vidéoconférence
   - Récupération des salles existantes
   - Génération de liens de participation
   - Gestion du cycle de vie des salles

### Composants UI

1. **MatchingVideoRooms** : Composant qui affiche la liste des salles de vidéoconférence pour un matching.
   - Liste des salles avec leurs détails
   - Boutons pour rejoindre ou terminer une salle
   - Bouton pour créer une nouvelle salle

2. **MatchingVideoRoomForm** : Composant qui permet de créer une nouvelle salle de vidéoconférence.
   - Formulaire avec les champs nécessaires
   - Validation des données
   - Feedback sur le résultat de la création

3. **Intégration dans MatchingDetailsPage** : Ajout des fonctionnalités vidéo à la page de détails d'un matching.
   - Section dédiée aux vidéoconférences
   - Modal pour créer une nouvelle salle
   - Bouton pour accéder aux fonctionnalités vidéo

## Sécurité

### Authentification et autorisation

- Vérification des permissions avant chaque action
- Génération de liens de participation sécurisés
- Protection des salles par mot de passe (optionnel)

### Protection des données

- Chiffrement des communications
- Validation des entrées utilisateur
- Prévention des attaques CSRF et XSS

## Bonnes pratiques

### Création de salles

- Utilisez des titres descriptifs pour faciliter l'identification des salles
- Programmez les vidéoconférences à l'avance pour permettre aux participants de se préparer
- Limitez la durée des vidéoconférences pour maintenir l'efficacité

### Participation aux vidéoconférences

- Testez votre équipement avant de rejoindre une vidéoconférence
- Utilisez un environnement calme et bien éclairé
- Préparez vos questions ou points de discussion à l'avance

## Dépannage

### Problèmes courants

1. **Impossible de créer une salle**
   - Vérifiez que vous avez les permissions nécessaires
   - Assurez-vous que le microservice Social-Platform-Video est opérationnel
   - Vérifiez que les paramètres de la salle sont valides

2. **Impossible de rejoindre une salle**
   - Vérifiez que la salle existe et qu'elle est active
   - Assurez-vous que vous êtes autorisé à rejoindre cette salle
   - Vérifiez que le lien de participation est valide

3. **Problèmes de qualité vidéo**
   - Vérifiez votre connexion internet
   - Fermez les applications qui consomment beaucoup de bande passante
   - Essayez de réduire la résolution vidéo
