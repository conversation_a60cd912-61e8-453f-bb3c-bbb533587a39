# Guide pour les Tests Isolés

## Introduction

Les tests isolés sont une approche de test qui permet de tester un composant ou un service indépendamment de ses dépendances externes. Cette approche présente plusieurs avantages :

- **Fiabilité** : Les tests ne dépendent pas de services externes qui peuvent être indisponibles ou instables
- **Rapidité** : Pas de délai d'attente pour les connexions à des services externes
- **Précision** : Le comportement exact des dépendances peut être contrôlé pour tester différents scénarios
- **Couverture complète** : Possibilité de tester des cas d'erreur difficiles à reproduire avec de véritables dépendances

Ce guide explique comment créer des tests isolés pour nos services.

## Principes à suivre

1. **Indépendance complète** : Un test isolé ne doit pas dépendre de bibliothèques externes, de bases de données, de services web, ou d'autres composants du système.

2. **Isolation du service** : Chaque service testé doit être une implémentation simplifiée mais fonctionnelle, reproduisant le comportement du service réel.

3. **Mocks appropriés** : Si le service testé dépend d'autres services, ces dépendances doivent être mockées de manière appropriée.

4. **Convention de nommage** : Les fichiers de tests isolés doivent suivre la convention `[ServiceName]Isolated.test.ts`.

## Structure d'un Test Isolé

Un test isolé typique suit cette structure :

1. **Définition des types** : Types nécessaires pour l'implémentation du service
2. **Implémentation du service** : Une version simplifiée du service réel
3. **Tests** : Tests unitaires pour le service

### Exemple

```typescript
// Types pour le service
interface MyServiceData {
  id: string;
  value: string;
}

// Implémentation simplifiée pour les tests
class MyServiceMock {
  private static instance: MyServiceMock;
  private data: MyServiceData[] = [];

  private constructor() {}

  public static getInstance(): MyServiceMock {
    if (!MyServiceMock.instance) {
      MyServiceMock.instance = new MyServiceMock();
    }
    return MyServiceMock.instance;
  }

  public async create(value: string): Promise<MyServiceData> {
    const item = {
      id: `id-${Date.now()}`,
      value
    };
    this.data.push(item);
    return item;
  }

  public async get(id: string): Promise<MyServiceData | null> {
    const item = this.data.find(item => item.id === id);
    return item || null;
  }

  public async getAll(): Promise<MyServiceData[]> {
    return [...this.data];
  }
  
  // Méthode utilitaire pour réinitialiser les données entre les tests
  public clear(): void {
    this.data = [];
  }
}

// Tests
describe('MyServiceIsolated', () => {
  let service: MyServiceMock;

  beforeEach(() => {
    service = MyServiceMock.getInstance();
    service.clear();
  });

  describe('create', () => {
    it('should create a new item', async () => {
      const item = await service.create('test value');
      expect(item.id).toBeDefined();
      expect(item.value).toBe('test value');
    });
  });

  describe('get', () => {
    it('should return an existing item', async () => {
      const created = await service.create('test');
      const item = await service.get(created.id);
      expect(item).not.toBeNull();
      expect(item?.value).toBe('test');
    });

    it('should return null for non-existent item', async () => {
      const item = await service.get('non-existent-id');
      expect(item).toBeNull();
    });
  });
});
```

## Comment créer un nouveau test isolé

### 1. Identifier les fonctionnalités clés

Commencez par identifier les fonctionnalités principales du service que vous voulez tester. Concentrez-vous sur les méthodes publiques qui constituent l'API du service.

### 2. Créer l'implémentation isolée

Implémentez une version simplifiée du service qui :
- Imite le comportement du service réel
- Utilise des structures de données en mémoire (comme des tableaux ou des Maps)
- N'a pas de dépendances externes

### 3. Implémenter le modèle Singleton

Utilisez le pattern Singleton pour votre service mockée pour faciliter les tests :

```typescript
class MyServiceMock {
  private static instance: MyServiceMock;
  
  private constructor() {
    // Initialisation
  }
  
  public static getInstance(): MyServiceMock {
    if (!MyServiceMock.instance) {
      MyServiceMock.instance = new MyServiceMock();
    }
    return MyServiceMock.instance;
  }
  
  // Méthodes du service...
}
```

### 4. Ajouter des méthodes utilitaires pour les tests

Ajoutez des méthodes utilitaires pour faciliter les tests, comme :
- `clear()` : pour réinitialiser l'état entre les tests
- `getState()` : pour accéder à l'état interne (à des fins de test uniquement)

### 5. Écrire les tests

Écrivez des tests qui couvrent :
- Les cas normaux (happy path)
- Les cas d'erreur
- Les cas limites
- Les comportements spécifiques

### 6. Ajouter au script de test

Une fois votre test isolé terminé et fonctionnel, ajoutez-le au script `run-isolated-tests.js` pour l'inclure dans l'exécution des tests isolés.

```javascript
// Dans scripts/run-isolated-tests.js
const isolatedTestFiles = [
  // Tests existants...
  'MyServiceIsolated.test.ts',
];
```

## Bonnes pratiques

1. **Tests indépendants** : Chaque test doit être indépendant des autres tests. Utilisez `beforeEach` pour réinitialiser l'état.

2. **Couverture complète** : Assurez-vous de tester toutes les méthodes publiques du service et tous les chemins d'exécution possibles.

3. **Tests lisibles** : Utilisez des noms de test descriptifs qui expliquent ce que le test vérifie.

4. **Tests maintenables** : Évitez la duplication de code dans les tests. Utilisez des fonctions d'aide pour les opérations répétitives.

5. **Comportement réaliste** : L'implémentation mockée doit se comporter de manière similaire au service réel, y compris en ce qui concerne les erreurs et les cas limites.

## Exemple de services déjà isolés

Voici quelques exemples de services qui ont déjà été isolés pour les tests :

1. **ValidationService** : Gère la validation des données
2. **NotificationService** : Gère les notifications système
3. **ConfigService** : Gère les configurations de l'application
4. **CacheService** : Gère le cache de l'application
5. **LoggingService** : Gère la journalisation des événements

Vous pouvez consulter ces implémentations dans le répertoire `Backend/src/tests/unit/services/` pour vous guider dans la création de vos propres tests isolés.

## Résolution des problèmes courants

### 1. Dépendances circulaires

**Problème** : Service A dépend de Service B qui dépend de Service A

**Solution** : Utilisez le lazy loading pour reporter l'initialisation de la dépendance ou restructurez votre implémentation pour éviter la dépendance circulaire.

### 2. Services externes

**Problème** : Le service utilise une bibliothèque externe comme AWS SDK ou Redis

**Solution** : Créez un mock approprié dans le répertoire `mocks/` et configurez Jest pour utiliser ce mock au lieu de la bibliothèque réelle.

### 3. État partagé entre les tests

**Problème** : Les tests interfèrent entre eux à cause de l'état partagé

**Solution** : Assurez-vous de réinitialiser l'état dans `beforeEach` et utilisez des données uniques pour chaque test.

### 4. Tests asynchrones

**Problème** : Les tests asynchrones échouent ou génèrent des comportements inattendus

**Solution** : Utilisez correctement `async/await` et assurez-vous de retourner les promesses dans les tests. Utilisez `jest.setTimeout` si nécessaire pour les opérations plus longues.

## Conclusion

Les tests isolés sont un élément essentiel de notre stratégie de test, offrant une couverture fiable et maintenable. Ils nous permettent de développer avec confiance et d'éviter les problèmes liés aux dépendances externes.

Si vous avez des questions ou besoin d'aide pour créer des tests isolés, n'hésitez pas à contacter l'équipe de développement. 