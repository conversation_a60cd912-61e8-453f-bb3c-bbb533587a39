# Specialized Pages Documentation

This document provides detailed information about the specialized pages in the Retreat And Be platform, including their purpose, components, and integration with other services.

## Table of Contents

- [TravelAgenciesPage](#travelagenciespage)
- [CaterersPage](#catererspage)
- [InsurancePage](#insurancepage)
- [TokenPage](#tokenpage)
- [NFTGalleryPage](#nftgallerypage)
- [WellnessPage](#wellnesspage)

## TravelAgenciesPage

### Overview

The Travel Agencies page allows travel agencies to partner with Retreat And Be to offer wellness retreats to their clients. It provides information about the benefits of partnership and includes a registration form.

### URL

`/travel-agencies`

### Components

- **Hero Section**: Introduces the partnership opportunity
- **Benefits Section**: Highlights the advantages of partnering with Retreat And Be
- **Registration Form**: Allows travel agencies to apply for partnership

### Integration

- Form submissions are processed by the Backend service
- Partner data is stored in the database
- Approved partners are integrated with the Retreat-Pro-Matcher service

### Usage

```tsx
import TravelAgenciesPage from '../pages/Public/TravelAgenciesPage';

// In your router
<Route path="/travel-agencies" element={<TravelAgenciesPage />} />
```

## CaterersPage

### Overview

The Caterers page allows catering services to partner with Retreat And Be to provide food services for wellness retreats. It showcases the benefits of partnership and includes a registration form.

### URL

`/caterers`

### Components

- **Hero Section**: Introduces the catering partnership opportunity
- **Benefits Section**: Highlights the advantages for caterers
- **Registration Form**: Allows catering services to apply for partnership

### Integration

- Form submissions are processed by the Backend service
- Caterer data is stored in the database
- Approved caterers are integrated with the Retreat-Pro-Matcher service

### Usage

```tsx
import CaterersPage from '../pages/Public/CaterersPage';

// In your router
<Route path="/caterers" element={<CaterersPage />} />
```

## InsurancePage

### Overview

The Insurance page allows users to compare and purchase travel insurance for their wellness retreats. It provides a comparison of different insurance providers, allows users to get quotes, and facilitates the purchase of policies.

### URL

`/insurance`

### Components

- **InsuranceComparison**: Displays available insurance providers
- **InsuranceQuoteForm**: Allows users to get quotes based on their retreat details
- **Quote Display**: Shows the details of the selected insurance quote
- **PolicyDetails**: Displays the details of a purchased policy

### Integration

- Insurance data is fetched from the Backend service
- Insurance quotes are processed through the Financial-Management service
- Policy purchases are handled by the Financial-Management service

### Usage

```tsx
import InsurancePage from '../pages/Public/InsurancePage';

// In your router
<Route path="/insurance" element={<InsurancePage />} />
```

## TokenPage

### Overview

The Token page provides information about the RandB token, its utility, and how it powers the wellness retreat ecosystem. It includes various components that display different aspects of the token.

### URL

`/token`

### Components

- **TokenInfo**: Provides general information about the RandB token
- **TokenChart**: Displays price and trading volume charts
- **TokenUtility**: Explains the utility of the token within the ecosystem
- **TokenMetrics**: Shows key metrics such as supply and distribution
- **TokenStaking**: Provides information about staking opportunities
- **TokenSwap**: Allows users to swap tokens
- **TokenGovernance**: Explains the governance model
- **TokenDistribution**: Shows how tokens are distributed

### Integration

- Token data is fetched from the RandB-Loyalty-Program service
- Token transactions are processed through the Financial-Management service
- Token governance is integrated with the blockchain infrastructure

### Usage

```tsx
import TokenPage from '../pages/Public/TokenPage';

// In your router
<Route path="/token" element={<TokenPage />} />
```

## NFTGalleryPage

### Overview

The NFT Gallery page allows users to explore and manage their NFT collection. It displays the user's NFTs and showcases exclusive collections available for purchase.

### URL

`/nft-gallery`

### Components

- **NFTCard**: Displays individual NFTs
- **NFTCollection**: Displays collections of NFTs
- **Empty State**: Shown when the user has no NFTs

### Integration

- NFT data is fetched from the blockchain through the NFT store
- NFT purchases are processed through the Financial-Management service
- NFT metadata is stored and retrieved from the Backend service

### Usage

```tsx
import NFTGalleryPage from '../pages/Public/NFTGalleryPage';

// In your router
<Route path="/nft-gallery" element={<NFTGalleryPage />} />
```

## WellnessPage

### Overview

The Wellness page provides users with tools to enhance their wellness journey. It includes a retreat booking tool and an AI-guided meditation feature.

### URL

`/wellness`

### Components

- **TabPanel**: Manages the different tabs
- **SmartBooking**: Allows users to book retreats
- **AIMeditationGuide**: Provides AI-guided meditation sessions

### Integration

- Retreat booking is integrated with the Backend service
- AI-guided meditation is powered by the Agent-RB service
- User preferences are stored in the user profile

### Usage

```tsx
import WellnessPage from '../pages/Public/WellnessPage';

// In your router
<Route path="/wellness" element={<WellnessPage />} />
```

## Testing

All specialized pages have both unit tests and end-to-end tests:

- Unit tests are located in `frontend/src/pages/Public/__tests__/`
- End-to-end tests are located in `frontend/cypress/e2e/specializedPages.cy.ts`

To run the unit tests:

```bash
npm test -- --testPathPattern=pages/Public/__tests__
```

To run the end-to-end tests:

```bash
npm run cypress:run -- --spec "cypress/e2e/specializedPages.cy.ts"
```

## Deployment

The specialized pages are included in the main frontend build and are deployed along with the rest of the application. To optimize the build for production:

```bash
node scripts/optimize-for-production.js
```

This script will optimize images, generate source maps, analyze bundles, and validate the build.
