# Guide de développement

Ce guide explique les principes fondamentaux pour développer et étendre la plateforme d'analyse RB2.

## Architecture du projet

Le projet RB2 est construit sur une architecture moderne, séparant clairement le frontend et le backend :

- **Frontend** : Application React avec Redux pour la gestion d'état
- **Backend** : API RESTful avec WebSockets pour les mises à jour en temps réel

### Structure du frontend

Le frontend est organisé selon les principes de conception par fonctionnalités et composants réutilisables :

```
frontend/
├── public/                # Fichiers statiques
├── src/
│   ├── components/        # Composants réutilisables
│   │   ├── common/        # Composants génériques (boutons, champs, etc.)
│   │   ├── layout/        # Composants de mise en page
│   │   ├── dashboard/     # Composants spécifiques au tableau de bord
│   │   └── analysis/      # Composants spécifiques aux analyses
│   ├── pages/             # Pages de l'application
│   │   ├── auth/          # Pages d'authentification
│   │   ├── user/          # Pages de gestion d'utilisateur
│   │   ├── analysis/      # Pages liées aux analyses
│   │   └── error/         # Pages d'erreur
│   ├── services/          # Services d'intégration API et utilitaires
│   ├── store/             # Configuration Redux 
│   │   ├── slices/        # Slices Redux 
│   │   └── store.ts       # Configuration du store
│   ├── types/             # Types TypeScript
│   ├── utils/             # Fonctions utilitaires
│   ├── App.tsx            # Composant racine
│   └── index.tsx          # Point d'entrée
└── tests/                 # Tests
```

## Technologies principales

### Frontend
- **React 18+** : Bibliothèque UI
- **Redux Toolkit** : Gestion d'état global
- **Material-UI** : Système de design
- **TypeScript** : Typage statique
- **Socket.io-client** : Communication temps réel
- **Recharts** : Visualisations de données
- **jsPDF** : Génération de PDF

### Backend
- **Node.js** : Runtime JavaScript
- **Express** : Framework web
- **MongoDB** : Base de données principale
- **PostgreSQL** : Base de données relationnelle
- **Socket.io** : Communication temps réel
- **Docker** : Conteneurisation

## Gestion de l'état

Le projet utilise Redux Toolkit pour la gestion de l'état. Les principaux slices sont :

- `authSlice` : Gestion de l'authentification
- `analysisSlice` : Gestion des analyses
- `uiSlice` : État de l'interface utilisateur
- `notificationsSlice` : Gestion des notifications

```typescript
// Exemple d'utilisation des actions Redux
import { useDispatch, useSelector } from 'react-redux';
import { fetchAnalyses, selectAnalyses } from '../store/slices/analysisSlice';

const Component = () => {
  const dispatch = useDispatch();
  const analyses = useSelector(selectAnalyses);
  
  useEffect(() => {
    dispatch(fetchAnalyses());
  }, [dispatch]);
  
  // ...
}
```

## Services

Les services sont des modules qui encapsulent la logique métier et les appels API. Les principaux services incluent :

### analysisService

Gère les opérations CRUD pour les analyses.

```typescript
import { analysisService } from '../services/analysisService';

// Exemple d'utilisation
const analysis = await analysisService.getAnalysisById(id);
```

### exportService

Gère l'exportation des analyses en différents formats, notamment PDF.

```typescript
import exportService from '../services/exportService';

// Générer un PDF pour une analyse spécifique
try {
  await exportService.generateAnalysisPDF(analysis);
} catch (error) {
  console.error('Erreur lors de la génération du PDF:', error);
}

// Générer un PDF de comparaison entre deux analyses
try {
  await exportService.generateComparisonPDF(firstAnalysis, secondAnalysis, selectedMetrics);
} catch (error) {
  console.error('Erreur lors de la génération du PDF de comparaison:', error);
}
```

### realtimeService

Gère les communications en temps réel via WebSockets.

```typescript
import realtimeService from '../services/realtimeService';

// Initialiser le service
const cleanup = realtimeService.init(enqueueSnackbar);

// S'abonner à une analyse spécifique
realtimeService.subscribeToAnalysis(analysisId);

// Se désabonner
realtimeService.unsubscribeFromAnalysis(analysisId);
```

## Services d'exportation avancés

Le système d'exportation permet la génération de rapports dans différents formats pour les analyses individuelles, les comparaisons d'analyses et les listes d'analyses.

### Architecture du service d'exportation

Le service d'exportation est basé sur une architecture modulaire avec des fonctions spécialisées pour chaque type d'exportation et format :

```
exportService/
├── PDF Functions
│   ├── generateAnalysisPDF()        # Génère un rapport PDF pour une analyse unique
│   ├── generateComparisonPDF()      # Génère un rapport PDF comparant deux analyses
│   └── Helpers (formatage, mise en page)
├── CSV Functions
│   ├── generateAnalysisCSV()        # Exporte une analyse en CSV
│   ├── generateComparisonCSV()      # Exporte une comparaison en CSV
│   ├── exportAnalysesListToCSV()    # Exporte une liste d'analyses en CSV
│   └── Helpers (formatage des données)
└── Utilities
    ├── Gestion d'erreurs
    ├── Formatage de dates
    └── Calcul de différences
```

### Formats supportés et bibliothèques

Le service d'exportation utilise plusieurs bibliothèques pour générer les différents formats :

- **PDF** : jsPDF + jsPDF-autoTable
- **CSV** : Génération native via Blob API

Exemple de génération de PDF avec jsPDF :

```typescript
// Initialiser le document
const doc = new jsPDF();

// Personnaliser le formatage
doc.setFontSize(20);
doc.text('Titre du rapport', 105, 20, { align: 'center' });

// Ajouter un tableau
autoTable(doc, {
  startY: 30,
  head: [['Colonne 1', 'Colonne 2']],
  body: [
    ['Données 1', 'Données 2'],
    ['Données 3', 'Données 4']
  ]
});

// Enregistrer le PDF
doc.save('rapport.pdf');
```

### Cycle de vie d'une exportation

Chaque fonction d'exportation suit un processus similaire :

1. **Validation** : Vérification des données d'entrée
2. **Préparation** : Organisation des données pour le format cible
3. **Génération** : Création du document selon le format
4. **Téléchargement** : Déclenchement du téléchargement via le navigateur
5. **Nettoyage** : Libération des ressources

### Extension avec de nouveaux formats

Pour ajouter un nouveau format d'exportation (par exemple, Excel) :

1. Installez la bibliothèque nécessaire :
   ```bash
   npm install xlsx
   ```

2. Créez une nouvelle fonction dans `exportService.ts` :
   ```typescript
   generateAnalysisExcel: async (analysis: Analysis): Promise<boolean> => {
     try {
       // Validation des données
       if (!analysis) {
         throw new Error('Analyse non définie');
       }
       
       // Logique de génération Excel
       const workbook = XLSX.utils.book_new();
       
       // Feuille d'informations générales
       const generalInfo = [
         ['ID', analysis.id],
         ['Nom', analysis.metadata.name],
         ['Statut', analysis.status],
         ['Type', analysis.metadata.type],
         ['Priorité', analysis.priority || 'N/A'],
         ['Créé le', new Date(analysis.createdAt).toLocaleString()]
       ];
       
       const worksheetGeneral = XLSX.utils.aoa_to_sheet(generalInfo);
       XLSX.utils.book_append_sheet(workbook, worksheetGeneral, 'Informations générales');
       
       // Feuille de métriques si disponibles
       if (analysis.results?.metrics) {
         const metrics = Object.entries(analysis.results.metrics).map(
           ([key, value]) => [key, value]
         );
         
         const worksheetMetrics = XLSX.utils.aoa_to_sheet([
           ['Métrique', 'Valeur'],
           ...metrics
         ]);
         
         XLSX.utils.book_append_sheet(workbook, worksheetMetrics, 'Métriques');
       }
       
       // Téléchargement
       const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
       const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
       
       const url = URL.createObjectURL(blob);
       const link = document.createElement('a');
       link.setAttribute('href', url);
       link.setAttribute('download', `analyse_${analysis.id}_${analysis.metadata.name.replace(/\s+/g, '_')}.xlsx`);
       document.body.appendChild(link);
       link.click();
       document.body.removeChild(link);
       
       return true;
     } catch (error) {
       console.error('Erreur lors de la génération Excel:', error);
       throw new Error(`Erreur lors de la génération Excel: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
     }
   }
   ```

3. Ajoutez les tests correspondants dans `__tests__/exportService.test.ts`

### Tests des services d'exportation

Les fonctions d'exportation sont testées à l'aide de mocks pour simuler jsPDF, autoTable et les méthodes DOM :

1. **Mock jsPDF** : `mockExportDependencies.ts` simule les méthodes jsPDF
2. **Mock DOM** : `mockDOMExport.ts` simule les méthodes DOM pour le téléchargement
3. **Données de test** : `testData.ts` contient des analyses fictives pour les tests

Pour chaque fonction d'exportation, les tests unitaires vérifient :
- Le fonctionnement nominal
- Les cas d'erreur (données manquantes, erreurs pendant la génération)
- Les validations d'entrée

### Amélioration des exports PDF

Pour améliorer visuellement les rapports PDF :

1. **Ajouter un logo** : Incorporez le logo de l'entreprise
   ```typescript
   // Ajouter un logo (à partir d'une image base64)
   doc.addImage(logoBase64, 'PNG', 10, 10, 30, 15);
   ```

2. **Utiliser des couleurs personnalisées** :
   ```typescript
   doc.setTextColor(0, 82, 164); // Bleu d'entreprise
   ```

3. **Ajouter des graphiques** :
   ```typescript
   // Fonction pour générer un graphique dans le PDF
   const addChartToPDF = (doc, chartData, startY) => {
     // Créer un canvas temporaire pour le graphique
     const canvas = document.createElement('canvas');
     canvas.width = 500;
     canvas.height = 300;
     
     // Dessiner le graphique avec Chart.js ou une autre bibliothèque
     new Chart(canvas, {
       type: 'bar',
       data: chartData,
       // Options...
     });
     
     // Convertir le canvas en image et l'ajouter au PDF
     const imgData = canvas.toDataURL('image/png');
     doc.addImage(imgData, 'PNG', 20, startY, 170, 100);
     
     return startY + 110; // Retourne la nouvelle position Y
   };
   ```

## Guides pratiques

### Extension du service d'exportation PDF

Pour ajouter un nouveau format d'exportation ou étendre les fonctionnalités existantes :

1. Ouvrez le fichier `frontend/src/services/exportService.ts`
2. Ajoutez une nouvelle méthode à l'objet `exportService`
3. Utilisez les bibliothèques comme jsPDF et autoTable pour le formatage
4. Assurez-vous d'implémenter une gestion d'erreurs appropriée
5. Ajoutez des tests unitaires dans `__tests__/exportService.test.ts`

Exemple d'ajout d'une nouvelle fonction d'exportation :

```typescript
/**
 * Exporte une liste d'analyses au format CSV
 * @param analyses - Liste des analyses à exporter
 * @returns Promise<boolean> - Succès de l'opération
 */
exportAnalysesListToCSV: async (analyses: Analysis[]): Promise<boolean> => {
  try {
    if (!analyses || analyses.length === 0) {
      throw new Error('La liste d\'analyses est vide');
    }
    
    // Logique d'exportation CSV
    // ...
    
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'exportation CSV:', error);
    throw new Error(`Erreur lors de l\'exportation CSV: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
}
```

### Ajout d'un nouveau composant d'analyse

Pour ajouter un nouveau composant d'analyse :

1. Créez un nouveau fichier dans `src/components/analysis/`
2. Importez les composants nécessaires et hooks de React/Redux
3. Suivez le modèle des composants existants pour maintenir la cohérence
4. Ajoutez des tests unitaires

### Extension des métriques d'analyse

Pour ajouter une nouvelle métrique d'analyse :

1. Assurez-vous que le backend prend en charge cette métrique
2. Mettez à jour l'interface `AnalysisResults` dans `types/analysis.ts` si nécessaire
3. Ajoutez le support d'affichage dans les composants concernés
4. Mettez à jour les fonctions d'exportation pour inclure la nouvelle métrique

## Bonnes pratiques

### TypeScript

- Utilisez des interfaces pour définir les structures de données
- Préférez les types énumérés aux chaînes de caractères brutes
- Utilisez les génériques pour les composants et fonctions réutilisables
- Évitez `any` autant que possible

### React

- Utilisez les composants fonctionnels et hooks
- Décomposez les gros composants en sous-composants
- Utilisez React.memo pour les composants purement présentationnels
- Gérez correctement les effets secondaires avec useEffect

### Redux

- Suivez le modèle des slices de Redux Toolkit
- Utilisez createAsyncThunk pour les opérations asynchrones
- Préférez les sélecteurs mémoïsés pour accéder à l'état
- Gardez l'état Redux normalisé

### Tests

- Écrivez des tests unitaires pour les composants, services et reducers
- Testez les cas heureux et les cas d'erreur
- Utilisez des mocks pour isoler les composants de leurs dépendances
- Suivez les principes AAA (Arrange, Act, Assert)

## Dépannage

### Problèmes d'authentification

Si vous rencontrez des problèmes d'authentification :

1. Vérifiez que le token JWT est correctement stocké
2. Assurez-vous que les en-têtes d'autorisation sont envoyés avec les requêtes
3. Vérifiez la validité et l'expiration du token

### Problèmes de communication en temps réel

Pour les problèmes avec WebSockets :

1. Vérifiez que le service Socket.io est correctement initialisé
2. Assurez-vous que les événements sont correctement abonnés
3. Vérifiez les logs côté serveur pour les erreurs de connexion

### Problèmes d'exportation PDF

Si l'exportation PDF échoue :

1. Vérifiez les erreurs dans la console
2. Assurez-vous que les données d'analyse sont complètes et valides
3. Vérifiez les limitations du navigateur concernant la génération de fichiers

## Ressources

- [Documentation React](https://reactjs.org/docs/getting-started.html)
- [Documentation Redux Toolkit](https://redux-toolkit.js.org/introduction/getting-started)
- [Documentation Material-UI](https://mui.com/getting-started/usage/)
- [Documentation jsPDF](https://artskydj.github.io/jsPDF/docs/jsPDF.html)
- [Documentation Socket.io](https://socket.io/docs/v4/)

## Fonctionnalités d'Exportation de Documents

Cette section documente les fonctionnalités d'exportation de l'application, qui permettent aux utilisateurs de générer et télécharger des rapports dans différents formats.

### Architecture des Exports

Le système d'export est basé sur une architecture modulaire qui permet de générer facilement des documents dans différents formats. Les éléments principaux sont :

1. **Service d'exportation** : Le cœur de la fonctionnalité, qui encapsule toute la logique de génération des documents.
2. **Composants d'interface utilisateur** : Un dialogue de configuration qui permet aux utilisateurs de personnaliser leurs exports.
3. **Intégration dans les pages** : L'intégration des fonctions d'export dans les différentes pages de l'application.

### Service d'Exportation

Le service d'exportation (`exportService.ts`) fournit les fonctions principales pour générer des documents :

```typescript
// Service d'exportation
export const exportService = {
  // Génère un PDF pour une analyse
  generateAnalysisPDF: async (analysis: Analysis, config?: ExportConfig): Promise<boolean> => {
    // ...
  },
  
  // Génère un PDF pour comparer deux analyses
  generateComparisonPDF: async (firstAnalysis: Analysis, secondAnalysis: Analysis, metrics: string[], config?: ExportConfig): Promise<boolean> => {
    // ...
  },
  
  // Génère un CSV pour une analyse
  generateAnalysisCSV: async (analysis: Analysis, config?: ExportConfig): Promise<boolean> => {
    // ...
  },
  
  // Génère un CSV pour comparer deux analyses
  generateComparisonCSV: async (firstAnalysis: Analysis, secondAnalysis: Analysis, metrics: string[], config?: ExportConfig): Promise<boolean> => {
    // ...
  },
  
  // Exporte une liste d'analyses en CSV
  exportAnalysesListToCSV: async (analyses: Analysis[], config?: ExportConfig): Promise<boolean> => {
    // ...
  }
};
```

Toutes ces fonctions acceptent un paramètre de configuration optionnel qui permet de personnaliser le contenu et la présentation du document généré.

### Configuration d'Export

La configuration d'export est définie par l'interface `ExportConfig` :

```typescript
export interface ExportConfig {
  format: 'pdf' | 'csv';
  includeIssues: boolean;
  includeMetrics: boolean;
  includeSummary: boolean;
  selectedMetrics: string[];
  customTitle?: string;
  customFooter?: string;
}
```

Chaque propriété permet de configurer un aspect de l'export :

- `format` : Détermine le format du document (PDF ou CSV)
- `includeIssues` : Indique si les problèmes détectés doivent être inclus
- `includeMetrics` : Indique si les métriques doivent être incluses
- `includeSummary` : Indique si le résumé doit être inclus
- `selectedMetrics` : Liste des métriques à inclure dans le document
- `customTitle` : Titre personnalisé pour le document
- `customFooter` : Pied de page personnalisé (PDF uniquement)

### Composant de Configuration d'Export

Le dialogue de configuration d'export (`ExportConfigDialog.tsx`) est un composant réutilisable qui permet aux utilisateurs de personnaliser leurs exports :

```typescript
const ExportConfigDialog: React.FC<ExportConfigDialogProps> = ({
  open,
  onClose,
  onExport,
  availableMetrics,
  isLoading = false,
  exportType,
  defaultTitle
}) => {
  // ...
};
```

Ce composant affiche un dialogue avec des onglets pour les différents formats d'export et des options pour personnaliser le contenu.

### Intégration dans les Pages

Les fonctionnalités d'export sont intégrées dans plusieurs pages :

1. **Page de détails d'analyse** : Permet d'exporter une analyse individuelle.
2. **Page de comparaison d'analyses** : Permet d'exporter une comparaison entre deux analyses.
3. **Page de liste d'analyses** : Permet d'exporter une liste d'analyses.

L'intégration suit généralement ce modèle :

```typescript
// Gérer l'exportation configurée
const handleConfiguredExport = async (config: ExportConfig) => {
  try {
    setIsLoading(true);
    
    if (config.format === 'pdf') {
      await exportService.generateAnalysisPDF(analysis, config);
    } else {
      await exportService.generateAnalysisCSV(analysis, config);
    }
    
    // Afficher un message de succès
  } catch (error) {
    // Gérer l'erreur
  } finally {
    setIsLoading(false);
  }
};
```

### Tests

Les fonctionnalités d'export sont couvertes par des tests unitaires qui vérifient à la fois les cas de succès et les cas d'erreur. Les tests utilisent des mocks pour simuler les dépendances externes comme jsPDF et les méthodes DOM :

```typescript
describe('exportService - Tests PDF', () => {
  describe('generateAnalysisPDF', () => {
    test('doit générer un PDF pour une analyse complète', async () => {
      // ...
    });
    
    test('doit gérer les erreurs correctement', async () => {
      // ...
    });
  });
  
  // ...
});
```

### Extension des Fonctionnalités d'Export

Pour ajouter un nouveau format d'export, suivez ces étapes :

1. **Ajouter le format à l'interface `ExportConfig`** :
   ```typescript
   export interface ExportConfig {
     format: 'pdf' | 'csv' | 'excel'; // Ajouter le nouveau format
     // ...
   }
   ```

2. **Créer les fonctions d'export dans `exportService.ts`** :
   ```typescript
   generateAnalysisExcel: async (analysis: Analysis, config?: ExportConfig): Promise<boolean> => {
     // Logique de génération Excel
   }
   ```

3. **Mettre à jour le composant `ExportConfigDialog.tsx`** pour inclure le nouveau format.

4. **Mettre à jour les pages** pour utiliser le nouveau format.

5. **Ajouter des tests** pour le nouveau format.

### Dépendances Externes

Les fonctionnalités d'export utilisent plusieurs bibliothèques externes :

- **jsPDF** : Pour la génération de documents PDF
- **jsPDF-AutoTable** : Pour générer des tableaux dans les PDF
- **Blob API** : Pour créer et télécharger des fichiers

Assurez-vous que ces dépendances sont disponibles et correctement configurées. 