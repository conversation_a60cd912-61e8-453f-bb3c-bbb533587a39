# Routes Professionnel - Retreat And Be

Les routes professionnel sont destinées aux organisateurs de retraites et prestataires de services. Ces routes permettent la gestion des retraites, des réservations, des finances et du profil professionnel.

## Tableau de bord et profil

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/professional` | Page d'accueil professionnel | `ProfessionalHomePage` | Non | ✅ |
| `/professional/dashboard` | Tableau de bord professionnel | `ProfessionalDashboard` | Oui | ✅ |
| `/professional/pro-dashboard` | Tableau de bord pro avancé | `ProDashboard` | Oui | ✅ |
| `/professional/profile` | Profil professionnel | `ProfessionalProfile` | Oui | ❌ |
| `/professional/profile/edit` | Édition du profil professionnel | `ProfessionalProfileEdit` | Oui | ✅ |

## Gestion des retraites

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/professional/retreats` | Liste des retraites du professionnel | `ProfessionalRetreatsList` | Oui | ✅ |
| `/professional/retreats/create` | Création d'une retraite | `ProfessionalRetreatCreate` | Oui | ✅ |
| `/professional/retreats/edit/:id` | Édition d'une retraite | `ProfessionalRetreatEdit` | Oui | ✅ |
| `/professional/retreats/:id` | Détail d'une retraite du professionnel | `ProfessionalRetreatDetail` | Oui | ✅ |
| `/professional/retreats/create-ai` | Création d'une retraite assistée par IA | `CreateRetreatAIPage` | Oui | ❌ |

## Gestion des réservations

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/professional/bookings` | Liste des réservations du professionnel | `ProfessionalBookingsList` | Oui | ✅ |
| `/professional/bookings/:id` | Détail d'une réservation du professionnel | `ProfessionalBookingDetail` | Oui | ✅ |

## Finances et analytics

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/professional/finances` | Gestion financière | `ProfessionalFinancesPage` | Oui | ✅ |
| `/professional/analytics` | Analytiques | `ProfessionalAnalyticsPage` | Oui | ✅ |
| `/professional/invoices` | Factures | `InvoicePage` | Oui | ✅ |
| `/professional/invoices/:id` | Détail d'une facture | `InvoicePage` | Oui | ✅ |

## Partenariat

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/professional/partner-landing` | Page d'atterrissage partenaire | `PartnerLandingPage` | Non | ✅ |
| `/partner/onboarding/:type` | Processus d'intégration partenaire | `OnboardingPage` | Non | ✅ |
| `/partner/onboarding/confirmation` | Confirmation d'intégration | `ConfirmationPage` | Non | ✅ |
| `/partner/affiliate` | Portail d'affiliation | `AffiliatePortal` | Oui | ✅ |

## Notes d'implémentation

### Niveaux d'accès professionnel

Les professionnels peuvent avoir différents niveaux d'accès :
- **Standard** : Accès de base à la gestion des retraites
- **Certifié** : Accès aux fonctionnalités avancées
- **Premium** : Accès complet à toutes les fonctionnalités

La vérification du niveau d'accès doit être effectuée dans le composant `ProtectedRoute` ou dans les composants individuels.

### Tableau de bord professionnel

Le tableau de bord professionnel doit afficher :
- Un résumé des retraites actives
- Les réservations récentes
- Les revenus et statistiques
- Les tâches en attente
- Les notifications importantes

### Création et gestion des retraites

Le système de gestion des retraites doit permettre :
- La création de retraites avec des détails complets
- Le téléchargement d'images et de documents
- La définition de prix et de disponibilités
- La gestion des options et services additionnels
- La publication et le déréférencement des retraites

### Création assistée par IA

La création de retraite assistée par IA (`/professional/retreats/create-ai`) doit :
- Guider le professionnel à travers un processus simplifié
- Suggérer des descriptions et des titres
- Recommander des prix basés sur des données de marché
- Proposer des images et des thèmes
- Optimiser le référencement

### Gestion financière

Les fonctionnalités financières doivent inclure :
- Un tableau de bord des revenus
- Des rapports détaillés
- La gestion des paiements
- L'exportation des données comptables
- La configuration des méthodes de paiement
