# Routes Admin - Retreat And Be

Les routes admin sont destinées à l'administration de la plateforme. Elles permettent la gestion des utilisateurs, du contenu, des retraites, des réservations, ainsi que l'accès aux analytiques et aux rapports. Toutes ces routes nécessitent une authentification avec des privilèges d'administrateur.

## Tableau de bord et gestion

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/admin` | Page d'accueil admin | `AdminPage` | Oui | ✅ |
| `/admin/dashboard` | Tableau de bord admin | `AdminDashboardPage` | Oui | ✅ |
| `/admin/users` | Gestion des utilisateurs | `AdminUsersListPage` | Oui | ❌ |
| `/admin/users/:id` | Détail d'un utilisateur | `AdminUserDetailPage` | Oui | ❌ |
| `/admin/roles` | Gestion des rôles | `Roles` | Oui | ❌ |
| `/admin/settings` | Paramètres admin | `Settings` | Oui | ❌ |

## Gestion du contenu

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/admin/content` | Gestion du contenu | `Content` | Oui | ❌ |
| `/admin/resources` | Gestion des ressources | `ResourcesPage` | Oui | ❌ |

## Gestion des retraites et réservations

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/admin/retreats` | Liste des retraites | `AdminRetreatsListPage` | Oui | ❌ |
| `/admin/retreats/:id` | Détail d'une retraite | `AdminRetreatDetailPage` | Oui | ❌ |
| `/admin/retreats/edit/:id` | Édition d'une retraite | `AdminRetreatEditPage` | Oui | ❌ |
| `/admin/bookings` | Liste des réservations | `AdminBookingsListPage` | Oui | ❌ |
| `/admin/bookings/:id` | Détail d'une réservation | `AdminBookingDetailPage` | Oui | ❌ |

## Analytiques et rapports

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/admin/analytics` | Analytiques admin | `Analytics` | Oui | ❌ |
| `/admin/reports` | Rapports | `ReportsPage` | Oui | ❌ |
| `/admin/monitoring` | Monitoring | `MonitoringDashboard` | Oui | ❌ |
| `/admin/security` | Sécurité | `SecurityDashboard` | Oui | ❌ |

## Notes d'implémentation

### Protection des routes admin

Toutes les routes admin doivent être protégées avec un composant `AdminProtectedRoute` qui vérifie non seulement l'authentification mais aussi les privilèges d'administrateur :

```jsx
<Route 
  path="/admin/dashboard" 
  element={
    <AdminProtectedRoute>
      <AdminDashboardPage />
    </AdminProtectedRoute>
  } 
/>
```

### Niveaux d'administration

La plateforme peut définir différents niveaux d'administration :
- **Admin standard** : Accès limité à certaines fonctionnalités
- **Super admin** : Accès complet à toutes les fonctionnalités
- **Admin spécialisé** : Accès à des sections spécifiques (contenu, utilisateurs, etc.)

### Tableau de bord admin

Le tableau de bord admin (`/admin/dashboard`) doit afficher :
- Des statistiques globales de la plateforme
- Des alertes et notifications importantes
- Des actions rapides
- Des graphiques d'activité
- Des liens vers les sections principales

### Gestion des utilisateurs

La gestion des utilisateurs doit permettre :
- La recherche et le filtrage des utilisateurs
- La modification des profils
- La gestion des rôles et permissions
- La suspension ou suppression de comptes
- L'envoi de communications

### Gestion du contenu

La gestion du contenu doit inclure :
- Un éditeur WYSIWYG pour les pages statiques
- La gestion des médias (images, vidéos)
- La modération des contenus générés par les utilisateurs
- La gestion des traductions
- La planification de publications

### Analytiques et rapports

Les fonctionnalités d'analytiques et de rapports doivent offrir :
- Des tableaux de bord personnalisables
- Des rapports exportables (PDF, CSV, Excel)
- Des alertes configurables
- Des analyses prédictives
- Des visualisations avancées

### Sécurité

La page de sécurité (`/admin/security`) doit permettre :
- La surveillance des tentatives de connexion
- La gestion des sessions actives
- La configuration des politiques de sécurité
- L'audit des actions administratives
- La gestion des incidents de sécurité
