# Routes Client - Retreat And Be

Les routes client sont destinées aux utilisateurs finaux authentifiés qui recherchent et réservent des retraites. La plupart de ces routes nécessitent une authentification.

## Tableau de bord et profil

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/client` | Page d'accueil client | `ClientHomePage` | Non | ✅ |
| `/client/dashboard` | Tableau de bord client | `ClientDashboard` | Oui | ✅ |
| `/client/profile` | Profil client | `ClientProfilePage` | Oui | ❌ |
| `/client/profile/edit` | Édition du profil client | `ClientProfileEdit` | Oui | ✅ |
| `/profile` | Profil utilisateur (général) | `ProfilePage` | Oui | ✅ |
| `/settings` | Paramètres utilisateur | `SettingsPage` | Oui | ✅ |

## Réservations et paiements

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/client/bookings` | Liste des réservations client | `ClientBookingsList` | Oui | ✅ |
| `/client/bookings/:id` | Détail d'une réservation client | `ClientBookingDetail` | Oui | ✅ |
| `/booking/:id` | Page de réservation | `BookingPage` | Oui | ✅ |
| `/payment/:bookingId` | Page de paiement | `PaymentPage` | Oui | ✅ |
| `/invoices` | Factures | `InvoiceListPage` | Oui | ✅ |
| `/invoices/:id` | Détail d'une facture | `InvoicePage` | Oui | ✅ |

## Fonctionnalités sociales et communication

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/loyalty` | Programme de fidélité | `LoyaltyPage` | Oui | ✅ |
| `/rewards` | Récompenses | `RewardsPage` | Oui | ❌ |
| `/messages` | Messagerie | `MessagesPage` | Oui | ✅ |
| `/notifications` | Notifications | `NotificationHistoryPage` | Oui | ✅ |
| `/community` | Communauté | `CommunityPage` | Oui | ❌ |
| `/community/member/:id` | Profil d'un membre de la communauté | `CommunityMemberPage` | Oui | ❌ |
| `/social/feed` | Fil d'actualités social | `SocialFeedPage` | Oui | ❌ |

## Notes d'implémentation

### Protection des routes

Toutes les routes marquées "Oui" dans la colonne Protection doivent être enveloppées dans un composant `ProtectedRoute` :

```jsx
<Route 
  path="/client/dashboard" 
  element={
    <ProtectedRoute>
      <ClientDashboard />
    </ProtectedRoute>
  } 
/>
```

### Tableau de bord client

Le tableau de bord client (`/client/dashboard`) doit afficher :
- Un résumé des réservations à venir
- Des recommandations personnalisées
- Des notifications récentes
- Des statistiques d'utilisation
- Des liens rapides vers les fonctionnalités principales

### Profil et paramètres

Les pages de profil et de paramètres doivent permettre :
- La modification des informations personnelles
- La gestion des préférences de notification
- La configuration de la sécurité du compte
- La gestion des méthodes de paiement
- La personnalisation de l'expérience utilisateur

### Réservations et paiements

Le système de réservation doit :
- Afficher clairement les détails de la réservation
- Permettre des modifications (si autorisées)
- Offrir des options d'annulation
- Fournir des reçus et factures téléchargeables
- Intégrer un système de paiement sécurisé

### Fonctionnalités sociales

Les fonctionnalités sociales doivent :
- Respecter la vie privée des utilisateurs
- Permettre des interactions entre membres
- Offrir des options de partage
- Inclure des systèmes de modération
- Supporter les notifications en temps réel
