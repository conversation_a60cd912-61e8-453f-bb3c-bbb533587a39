# Routes Utilitaires - Retreat And Be

Les routes utilitaires comprennent les pages de développement et les pages d'erreur. Ces routes sont essentielles pour le développement, les tests et la gestion des erreurs dans l'application.

## Pages de développement

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/dev/performance` | Tableau de bord de performance | `PerformanceDashboardPage` | Oui | ✅ |
| `/dev/accessibility` | Audit d'accessibilité | `AccessibilityAuditPage` | Oui | ✅ |
| `/components` | Vitrine des composants | `ComponentsShowcasePage` | Non | ✅ |
| `/molecular-components` | Vitrine des composants moléculaires | `MolecularComponentsShowcasePage` | Non | ✅ |
| `/examples/partner-matching` | Exemple de mise en relation | `PartnerMatchingExample` | Non | ✅ |
| `/examples/chatbot` | Exemple de chatbot | `ChatBotExample` | Non | ✅ |
| `/star-border` | Exemple de bordure étoilée | `StarBorderPage` | Non | ✅ |

## Pages d'erreur

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/404` | Page non trouvée | `Error404Page` | Non | ✅ |
| `/500` | Erreur serveur | `Error500Page` | Non | ✅ |
| `/unauthorized` | Non autorisé | `UnauthorizedPage` | Non | ✅ |
| `/under-construction` | En construction | `UnderConstructionPage` | Non | ✅ |
| `/upgrade` | Mise à niveau requise | `UpgradePage` | Non | ✅ |
| `*` | Toute route non définie | `NotFoundPage` | Non | ✅ |

## Notes d'implémentation

### Pages de développement

Les pages de développement sont essentielles pour :
- Tester les composants individuellement
- Vérifier les performances de l'application
- Assurer l'accessibilité
- Démontrer les fonctionnalités
- Faciliter la documentation

Ces pages ne doivent pas être accessibles en production ou doivent être protégées par une authentification.

#### Vitrine des composants

La vitrine des composants (`/components` et `/molecular-components`) doit :
- Présenter tous les composants disponibles
- Montrer les différentes variantes
- Permettre l'interaction avec les composants
- Fournir la documentation d'utilisation
- Afficher le code source

```jsx
// Exemple d'implémentation de la vitrine des composants
<Route path="/components" element={
  process.env.NODE_ENV === 'production' 
    ? <ProtectedRoute><ComponentsShowcasePage /></ProtectedRoute>
    : <ComponentsShowcasePage />
} />
```

#### Pages d'exemples

Les pages d'exemples (`/examples/partner-matching` et `/examples/chatbot`) doivent :
- Démontrer des cas d'utilisation réels
- Être interactives
- Inclure des explications détaillées
- Servir de documentation vivante
- Faciliter les tests

### Pages d'erreur

Les pages d'erreur sont cruciales pour :
- Améliorer l'expérience utilisateur en cas d'erreur
- Fournir des informations utiles
- Guider l'utilisateur vers des solutions
- Maintenir la cohérence visuelle
- Collecter des données sur les erreurs

#### Gestion des erreurs

La gestion des erreurs doit être implémentée à plusieurs niveaux :

1. **Au niveau des routes** : Capture des routes non définies
   ```jsx
   <Route path="*" element={<NotFoundPage />} />
   ```

2. **Au niveau des composants** : Utilisation de Error Boundaries
   ```jsx
   <ErrorBoundary fallback={<Error500Page />}>
     <Component />
   </ErrorBoundary>
   ```

3. **Au niveau des requêtes API** : Gestion des erreurs HTTP
   ```jsx
   try {
     const response = await api.get('/data');
     // Traitement normal
   } catch (error) {
     if (error.response?.status === 401) {
       navigate('/unauthorized');
     } else if (error.response?.status === 404) {
       navigate('/404');
     } else {
       navigate('/500');
     }
   }
   ```

#### Personnalisation des pages d'erreur

Les pages d'erreur doivent :
- Être cohérentes avec le design de l'application
- Fournir des messages clairs et utiles
- Offrir des options pour résoudre le problème
- Inclure des liens vers les pages principales
- Collecter des informations pour le débogage (si nécessaire)

### Environnements

La gestion des routes utilitaires peut varier selon l'environnement :
- **Développement** : Toutes les routes sont accessibles
- **Test** : Routes protégées par authentification
- **Production** : Routes de développement désactivées ou fortement protégées
