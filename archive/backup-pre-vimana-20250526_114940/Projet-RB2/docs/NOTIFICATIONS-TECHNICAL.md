# Système de Notifications - Documentation Technique

## Vue d'ensemble

Le système de notifications est une infrastructure complète qui permet d'envoyer, de gérer et d'afficher des notifications en temps réel aux utilisateurs de la plateforme. Il prend en charge plusieurs canaux de notification (in-app, push, email) et offre des fonctionnalités avancées comme la personnalisation, la priorisation et l'analyse.

## Architecture

Le système de notifications est implémenté comme un microservice dédié qui s'intègre avec les autres composants de la plateforme:

```
┌─────────────────────┐      ┌─────────────────────┐
│                     │      │                     │
│  Frontend           │◄────►│  API Gateway        │
│                     │      │                     │
└─────────────────────┘      └──────────┬──────────┘
                                        │
                                        ▼
┌─────────────────────┐      ┌─────────────────────┐
│                     │      │                     │
│  WebSocket Server   │◄────►│  Notification       │
│                     │      │  Service            │
└─────────────────────┘      │                     │
                             └──────────┬──────────┘
                                        │
                                        ▼
┌─────────────────────┐      ┌─────────────────────┐
│                     │      │                     │
│  Push Notification  │◄────►│  Email Service      │
│  Service            │      │                     │
└─────────────────────┘      └─────────────────────┘
```

### Composants Clés

1. **Notification Service**: Service principal qui gère la création, le stockage et la distribution des notifications
2. **WebSocket Server**: Permet la livraison en temps réel des notifications aux clients connectés
3. **Push Notification Service**: Gère l'envoi de notifications push aux appareils mobiles et navigateurs
4. **Email Service**: Responsable de l'envoi des notifications par email et des résumés périodiques
5. **Frontend Components**: Composants UI pour afficher et interagir avec les notifications

## Modèle de Données

### Notification

```typescript
interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  data?: {
    userId?: string;
    username?: string;
    name?: string;
    avatar?: string;
    contentId?: string;
    contentType?: 'video' | 'post' | 'story' | 'livestream';
    contentTitle?: string;
    thumbnailUrl?: string;
    amount?: number;
    currency?: string;
    collaborationId?: string;
    messageId?: string;
    achievementId?: string;
    achievementTitle?: string;
    url?: string;
    [key: string]: any;
  };
}
```

### Types de Notifications

```typescript
type NotificationType = 
  | 'like'
  | 'comment'
  | 'follow'
  | 'mention'
  | 'share'
  | 'collaboration_request'
  | 'collaboration_accepted'
  | 'collaboration_declined'
  | 'subscription'
  | 'tip'
  | 'payment'
  | 'payout'
  | 'message'
  | 'system'
  | 'content_published'
  | 'content_trending'
  | 'achievement';
```

### Paramètres de Notification

```typescript
interface NotificationSettings {
  enabled: boolean;
  pushEnabled: boolean;
  emailEnabled: boolean;
  inAppEnabled: boolean;
  types: {
    [key in NotificationType]?: {
      enabled: boolean;
      push: boolean;
      email: boolean;
      inApp: boolean;
    };
  };
  digestFrequency: 'never' | 'daily' | 'weekly';
  quietHoursEnabled: boolean;
  quietHoursStart: string; // HH:MM format
  quietHoursEnd: string; // HH:MM format
}
```

## API REST

### Endpoints Principaux

#### Récupération des Notifications

```
GET /api/notifications
```

**Paramètres de requête**:
- `page`: Numéro de page (défaut: 1)
- `limit`: Nombre de notifications par page (défaut: 20)
- `filter`: Filtrer par type ou 'unread' pour les notifications non lues

**Réponse**:
```json
{
  "notifications": [Notification],
  "totalCount": number,
  "unreadCount": number,
  "page": number,
  "totalPages": number
}
```

#### Marquer une Notification comme Lue

```
PUT /api/notifications/:notificationId/read
```

**Réponse**:
```json
{
  "id": "string",
  "isRead": true,
  ...
}
```

#### Marquer Toutes les Notifications comme Lues

```
PUT /api/notifications/read-all
```

**Réponse**:
```json
{
  "success": true,
  "count": number
}
```

#### Supprimer une Notification

```
DELETE /api/notifications/:notificationId
```

**Réponse**:
```json
{
  "success": true
}
```

#### Supprimer Toutes les Notifications

```
DELETE /api/notifications
```

**Réponse**:
```json
{
  "success": true,
  "count": number
}
```

#### Récupérer les Paramètres de Notification

```
GET /api/notifications/settings
```

**Réponse**:
```json
{
  "enabled": boolean,
  "pushEnabled": boolean,
  "emailEnabled": boolean,
  "inAppEnabled": boolean,
  "types": {
    "like": {
      "enabled": boolean,
      "push": boolean,
      "email": boolean,
      "inApp": boolean
    },
    ...
  },
  "digestFrequency": "never" | "daily" | "weekly",
  "quietHoursEnabled": boolean,
  "quietHoursStart": "string",
  "quietHoursEnd": "string"
}
```

#### Mettre à Jour les Paramètres de Notification

```
PUT /api/notifications/settings
```

**Corps de la requête**:
```json
{
  "enabled": boolean,
  "pushEnabled": boolean,
  ...
}
```

**Réponse**:
```json
{
  "enabled": boolean,
  "pushEnabled": boolean,
  ...
}
```

#### Statistiques des Notifications

```
GET /api/notifications/statistics
```

**Réponse**:
```json
{
  "totalCount": number,
  "unreadCount": number,
  "typeCounts": {
    "like": number,
    "comment": number,
    ...
  },
  "weeklyStats": [
    {
      "date": "string",
      "count": number
    },
    ...
  ]
}
```

## WebSocket API

Le système utilise Socket.IO pour les communications en temps réel.

### Connexion

```javascript
const socket = io(`${SOCKET_URL}/notifications`, {
  auth: {
    token: "JWT_TOKEN"
  }
});
```

### Événements

#### Réception d'une Nouvelle Notification

```javascript
socket.on('notification', (notification) => {
  // Traiter la nouvelle notification
});
```

#### Mise à Jour du Compteur de Notifications

```javascript
socket.on('notification_count', (counts) => {
  // counts = { total: number, unread: number }
});
```

## Composants Frontend

### NotificationCenter

Composant principal qui affiche un bouton de notification avec un badge de compteur et un panneau déroulant contenant les notifications récentes.

### NotificationItem

Composant qui affiche une notification individuelle avec des actions (marquer comme lu, supprimer).

### NotificationSettings

Composant qui permet aux utilisateurs de configurer leurs préférences de notification.

## Flux de Travail

### Création et Envoi de Notifications

1. Un événement se produit dans le système (like, commentaire, etc.)
2. Le service concerné envoie une demande de création de notification au Notification Service
3. Le Notification Service crée la notification et la stocke dans la base de données
4. Le service vérifie les paramètres de notification de l'utilisateur
5. En fonction des paramètres, la notification est envoyée via les canaux appropriés:
   - WebSocket pour les notifications in-app en temps réel
   - Push Notification Service pour les notifications push
   - Email Service pour les notifications par email
6. Les compteurs de notification sont mis à jour et diffusés via WebSocket

### Réception et Affichage des Notifications

1. Le client se connecte au WebSocket Server lors de l'initialisation
2. Le client reçoit les notifications en temps réel via WebSocket
3. Le NotificationCenter affiche un badge avec le nombre de notifications non lues
4. L'utilisateur peut cliquer sur l'icône de notification pour voir les notifications récentes
5. L'utilisateur peut interagir avec les notifications (marquer comme lu, supprimer, cliquer pour naviguer)

## Considérations de Performance

### Mise en Cache

- Les compteurs de notification sont mis en cache dans Redis pour un accès rapide
- Les notifications récentes sont mises en cache pour réduire la charge de la base de données

### Pagination

- Les notifications sont paginées pour éviter de charger un grand nombre de notifications à la fois
- Le chargement infini est implémenté pour une expérience utilisateur fluide

### Optimisation des WebSockets

- Les connexions WebSocket sont regroupées par utilisateur
- Les événements sont diffusés uniquement aux utilisateurs concernés
- Heartbeat pour maintenir les connexions actives

## Sécurité

- Authentification JWT pour toutes les requêtes API et WebSocket
- Validation des données d'entrée pour prévenir les injections
- Vérification des autorisations pour s'assurer que les utilisateurs ne peuvent accéder qu'à leurs propres notifications
- Protection contre les attaques par déni de service avec limitation de débit

## Monitoring et Logging

- Métriques de performance pour surveiller le temps de traitement des notifications
- Logs détaillés pour le débogage et l'audit
- Alertes pour les erreurs critiques ou les pics de charge
- Tableau de bord pour visualiser l'activité des notifications en temps réel

## Extensibilité

Le système est conçu pour être facilement extensible:

- Nouveaux types de notifications peuvent être ajoutés sans modifier l'architecture
- Nouveaux canaux de notification peuvent être intégrés via des adaptateurs
- Personnalisation avancée des notifications par type et par utilisateur
- Support pour les notifications groupées et les résumés
