# Testing Infrastructure

This document describes the testing infrastructure for the project, including unit tests, accessibility tests, visual regression tests, and performance tests.

## Table of Contents

- [Overview](#overview)
- [Test Types](#test-types)
  - [Unit Tests](#unit-tests)
  - [Accessibility Tests](#accessibility-tests)
  - [Visual Regression Tests](#visual-regression-tests)
  - [Performance Tests](#performance-tests)
- [CI/CD Integration](#cicd-integration)
- [Running Tests Locally](#running-tests-locally)
- [Adding New Tests](#adding-new-tests)
- [Test Reports](#test-reports)

## Overview

Our testing infrastructure is designed to ensure high-quality code and a great user experience. We use a combination of different test types to catch issues early in the development process.

## Test Types

### Unit Tests

Unit tests verify that individual components and functions work as expected in isolation.

- **Framework**: Jest
- **Location**: `__tests__` directories throughout the codebase
- **Run Command**: `npm test`

#### Key Features:

- Component testing with React Testing Library
- Snapshot testing for UI components
- Mocking of external dependencies
- Coverage reporting

### Accessibility Tests

Accessibility tests ensure that our application is usable by people with disabilities and complies with WCAG guidelines.

- **Framework**: axe-core with <PERSON><PERSON>
- **Location**: `scripts/accessibility-tests.js`
- **Run Command**: `npm run test:accessibility`

#### Key Features:

- Automated testing against WCAG 2.1 AA standards
- Testing across multiple pages
- Detailed reporting of issues
- Integration with CI/CD
- Authentication support for protected pages
- Customizable rules and configuration
- HTML report generation

### Visual Regression Tests

Visual regression tests capture screenshots of the application and compare them with baseline images to detect unintended visual changes.

- **Framework**: Playwright with pixelmatch
- **Location**: `scripts/visual-regression-tests.js`
- **Run Commands**:
  - All viewports: `npm run test:visual`
  - Desktop only: `npm run test:visual:desktop`
  - Tablet only: `npm run test:visual:tablet`
  - Mobile only: `npm run test:visual:mobile`

#### Key Features:

- Testing across multiple viewport sizes (desktop, tablet, mobile)
- Pixel-by-pixel comparison
- Visual diff generation
- Configurable threshold for differences
- Authentication support for protected pages
- User interaction simulation
- Handling of dynamic content
- Detailed HTML reports

### Performance Tests

Performance tests measure the loading speed and runtime performance of the application.

- **Framework**: Lighthouse CI
- **Location**: `lighthouse-config.js`
- **Run Commands**:
  - All devices: `npm run test:lighthouse`
  - Desktop only: `npm run test:lighthouse:desktop`
  - Mobile only: `npm run test:lighthouse:mobile`

#### Key Features:

- Testing of key performance metrics (FCP, LCP, CLS, TBT, SI, TTI)
- Accessibility scoring
- Best practices evaluation
- SEO evaluation
- Configurable thresholds
- Device-specific testing (desktop and mobile)
- Authentication support for protected pages
- Detailed performance reports
- Resource size monitoring

## CI/CD Integration

All tests are integrated into our CI/CD pipeline using GitHub Actions. The workflow is defined in `.github/workflows/accessibility-performance.yml`.

The workflow runs on:
- Push to main and develop branches
- Pull requests to main and develop branches
- Manual triggering

### CI/CD Steps:

1. Checkout code
2. Set up Node.js environment
3. Install dependencies
4. Build the project
5. Run tests (unit, accessibility, visual regression, performance)
6. Upload test reports as artifacts
7. Fail the build if tests don't meet thresholds

## Running Tests Locally

To run all tests locally:

```bash
npm run test:all
```

To run specific test types:

```bash
# Unit tests
npm test

# Accessibility tests
npm run test:accessibility

# Visual regression tests - all viewports
npm run test:visual

# Visual regression tests - specific viewport
npm run test:visual:desktop
npm run test:visual:tablet
npm run test:visual:mobile

# Performance tests - all devices
npm run test:lighthouse

# Performance tests - specific device
npm run test:lighthouse:desktop
npm run test:lighthouse:mobile

# Run all tests for CI environment
npm run test:ci
```

### Environment Variables

You can customize the test behavior with environment variables:

```bash
# Set base URL for tests
BASE_URL=http://localhost:3000 npm run test:visual

# Set test user credentials
TEST_USER_EMAIL=<EMAIL> TEST_USER_PASSWORD=password123 npm run test:accessibility

# Set viewport for visual tests
VIEWPORT=tablet npm run test:visual

# Set device type for performance tests
DEVICE_TYPE=mobile npm run test:lighthouse
```

## Adding New Tests

### Adding Unit Tests

1. Create a new test file in the `__tests__` directory next to the component or function you're testing
2. Import the component or function
3. Write your test cases using Jest and React Testing Library
4. Run the tests with `npm test`

### Adding Pages to Accessibility and Visual Tests

To add a new page to accessibility and visual regression tests:

1. Open `scripts/accessibility-tests.js` or `scripts/visual-regression-tests.js`
2. Add the page URL to the `urls` array
3. Run the tests to create new baselines

### Updating Performance Thresholds

To update performance thresholds:

1. Open `lighthouse-config.js`
2. Modify the thresholds in the `assertions` object
3. Run the tests to verify the new thresholds

## Test Reports

Test reports are generated in the following locations:

- **Unit Tests**: `coverage/` directory
- **Accessibility Tests**: `reports/accessibility/` directory
- **Visual Regression Tests**: `reports/visual-regression/` directory
- **Performance Tests**: `lighthouse-results/` directory

In CI/CD, these reports are uploaded as artifacts and can be downloaded from the GitHub Actions workflow run page.
