# État du Déploiement de la Plateforme RB2

## Résumé

Ce document présente l'état actuel du déploiement de la plateforme RB2, les problèmes rencontrés, les solutions mises en œuvre et les prochaines étapes recommandées pour un déploiement complet.

## État actuel

### Services fonctionnels

Les services suivants sont actuellement opérationnels :

1. **Services de bases de données**
   - PostgreSQL (pour Keycloak, Backend, Transport-Booking, Website-Creator)
   - Redis (maîtres et répliques pour plusieurs services)
   - MongoDB (pour Social-Platform-Video et VR)

2. **Services applicatifs**
   - `financial-service`: Opérationnel (1/1 Ready)
   - `keycloak`: Opérationnel (2/2 Ready)

### Services mis en pause

Pour simplifier le déploiement initial, les services suivants ont été temporairement mis en pause (réplicas=0) :

- backend-backend
- decentralized-storage-decentralized-storage
- financial-management
- flight-finder-flight-finder
- frontend-frontend
- messaging-service
- retreat-pro-matcher
- retreat-stream
- security-service
- social
- social-platform-video
- transport-booking
- vr
- web3-nft-service
- website-creator

## Problèmes rencontrés et solutions appliquées

### 1. Problèmes de charts Helm

**Problèmes** :
- Charts incomplets (Chart.yaml manquants)
- Variables Grafana non échappées
- Références à des fonctions "service" non définies

**Solutions** :
- Création de fichiers Chart.yaml manquants
- Échappement correct des variables Grafana avec `{{ "{{variable}}" }}`
- Correction des templates Grafana dans le chart Monitoring

### 2. Problèmes d'Istio

**Problèmes** :
- CRDs Istio manquants
- Échec de l'installation de l'opérateur Istio

**Solutions** :
- Installation complète d'Istio avec le profil "demo"
- Vérification de la présence de tous les CRDs nécessaires
- Désactivation temporaire de l'injection d'Istio pour simplifier le déploiement

### 3. Problèmes d'images Docker

**Problèmes** :
- Images inexistantes ou non accessibles (ImagePullBackOff)
- Références à des registres privés

**Solutions** :
- Remplacement temporaire par l'image nginx:alpine
- Modification de la politique de pull (IfNotPresent)
- Pour les services essentiels, création de pods fonctionnels

### 4. Problèmes de contexte de sécurité

**Problèmes** :
- Erreur "container has runAsNonRoot and image will run as root"
- Contraintes de sécurité incompatibles avec les images

**Solutions** :
- Suppression ou désactivation du paramètre runAsNonRoot
- Modification des contextes de sécurité dans les déploiements

### 5. Problèmes de ressources

**Problèmes** :
- Nombreux pods en état Pending en raison de contraintes de ressources
- Limites de ressources trop élevées pour un environnement de développement

**Solutions** :
- Réduction des limites de ressources (CPU, mémoire)
- Réduction du nombre de réplicas à 1 pour chaque service
- Mise en pause des services non essentiels

## Scripts de correction créés

Plusieurs scripts ont été créés pour résoudre les problèmes rencontrés :

1. `scripts/fix-all-errors.sh` - Script principal pour corriger diverses erreurs
2. `scripts/fix-analyzer.sh` - Correction des problèmes spécifiques au chart Analyzer
3. `scripts/install-istio-complete.sh` - Installation complète d'Istio avec tous les CRDs
4. `scripts/fix-all-images.sh` - Correction des problèmes d'images pour tous les services
5. `scripts/fix-security-context.sh` - Correction des problèmes de contexte de sécurité
6. `scripts/adjust-resource-limits.sh` - Ajustement des limites de ressources
7. `scripts/simplified-deployment.sh` - Simplification du déploiement pour avoir une version minimale fonctionnelle

## Prochaines étapes recommandées

### 1. Création d'images Docker appropriées

Pour chaque service, développer une image Docker dédiée :

```bash
# Exemple pour le service frontend
cd frontend
docker build -t rb2/frontend:latest .
docker tag rb2/frontend:latest <registry>/rb2/frontend:latest
docker push <registry>/rb2/frontend:latest
```

### 2. Mise à jour des charts Helm

Pour chaque service, mettre à jour les références d'images dans les charts Helm :

```yaml
# Exemple pour values.yaml du chart frontend
image:
  repository: <registry>/rb2/frontend
  tag: latest
  pullPolicy: IfNotPresent
```

### 3. Réactivation progressive des services

Une fois les images appropriées disponibles, réactiver les services un par un :

```bash
# Exemple pour réactiver le service frontend
kubectl scale --replicas=1 deployment/frontend-frontend -n retreat-and-be
```

### 4. Configuration des ingress

Configurer les règles d'ingress pour accéder aux services depuis l'extérieur du cluster :

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rb2-ingress
  namespace: retreat-and-be
  annotations:
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
  - host: rb2.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-frontend
            port:
              number: 3000
```

### 5. Réactivation de l'injection Istio

Une fois que tous les services fonctionnent correctement, réactiver l'injection d'Istio :

```bash
kubectl label namespace retreat-and-be istio-injection=enabled --overwrite
kubectl rollout restart deployment -n retreat-and-be
```

### 6. Mise en place de la surveillance

Déployer correctement le chart Monitoring avec Grafana et Prometheus :

```bash
helm upgrade --install monitoring ./charts/monitoring -n retreat-and-be
```

## Conclusion

Le déploiement de la plateforme RB2 a rencontré plusieurs défis qui ont été partiellement résolus. Une version minimale fonctionnelle est maintenant disponible avec les services essentiels (financial-service et keycloak) ainsi que toutes les bases de données nécessaires.

Pour un déploiement complet, il est nécessaire de développer des images Docker appropriées pour chaque service et de les déployer progressivement. Les scripts et corrections mis en place fournissent une base solide pour poursuivre le déploiement. 