# Procédures de Sauvegarde

Ce document détaille les procédures de sauvegarde mises en place pour assurer la protection et la récupération des données en cas d'incident.

## Stratégie Globale de Sauvegarde

### Fréquence des sauvegardes
- **Sauvegardes quotidiennes** : Automatisées chaque jour à 2h00 UTC
- **Sauvegardes hebdomadaires** : Complètes, tous les dimanches à 3h00 UTC 
- **Sauvegardes mensuelles** : Archivage le premier jour de chaque mois à 4h00 UTC
- **Sauvegardes annuelles** : Conservation long terme, 1er janvier à 5h00 UTC

### Stratégie de rétention
- Quotidiennes : Conservation 14 jours
- Hebdomadaires : Conservation 2 mois
- Mensuelles : Conservation 1 an
- Annuelles : Conservation 7 ans

## Infrastructure de Sauvegarde

### Stockage Primaire
- **Type** : Amazon S3 Standard
- **Région** : eu-west-1 (Irlande)
- **Bucket** : `company-backups-primary`
- **Structure** :
  ```
  /databases/
    /postgres/
    /mongodb/
  /files/
    /user-content/
    /system-files/
  /configurations/
    /kubernetes/
    /applications/
  /logs/
  ```

### Stockage Secondaire (Archives)
- **Type** : Amazon S3 Glacier
- **Région** : eu-central-1 (Francfort)
- **Bucket** : `company-backups-archives`
- **Politique de transfert** : Transfert automatique des sauvegardes mensuelles après 30 jours

### Réplication Géographique
- Réplication cross-région activée
- Destinations : `us-east-1` (Virginie) et `ap-northeast-1` (Tokyo)
- Fréquence : Quotidienne (sauvegardes critiques uniquement)

## Procédures par Type de Données

### Bases de données PostgreSQL

#### Sauvegarde Complète
```bash
#!/bin/bash
# /scripts/backup/postgres-full.sh

DATE=$(date +%Y-%m-%d)
BACKUP_DIR="/mnt/backups/databases/postgres/${DATE}"
POSTGRES_USER="backup_user"
POSTGRES_DB="production_db"

mkdir -p ${BACKUP_DIR}

# Sauvegarde complète avec compression
pg_dump -U ${POSTGRES_USER} -d ${POSTGRES_DB} -F c -Z 9 -f ${BACKUP_DIR}/full_backup.dump

# Vérification d'intégrité
pg_restore -l ${BACKUP_DIR}/full_backup.dump > /dev/null
if [ $? -eq 0 ]; then
  echo "Backup integrity check passed"
  # Transfert vers S3
  aws s3 cp ${BACKUP_DIR}/full_backup.dump s3://company-backups-primary/databases/postgres/${DATE}/
else
  echo "Backup integrity check failed"
  # Notification d'erreur
  /scripts/notify-team.sh "PostgreSQL backup failed on ${DATE}"
fi
```

#### Sauvegarde des Journaux de Transactions (WAL)
```bash
# Configuration dans postgresql.conf
wal_level = replica
archive_mode = on
archive_command = 'gzip < %p > /mnt/backups/databases/postgres/wal/%f.gz && aws s3 cp /mnt/backups/databases/postgres/wal/%f.gz s3://company-backups-primary/databases/postgres/wal/'
```

### Bases de données MongoDB

#### Sauvegarde Complète
```bash
#!/bin/bash
# /scripts/backup/mongodb-full.sh

DATE=$(date +%Y-%m-%d)
BACKUP_DIR="/mnt/backups/databases/mongodb/${DATE}"
MONGO_URI="mongodb://backup:<EMAIL>:27017/admin"

mkdir -p ${BACKUP_DIR}

# Sauvegarde complète
mongodump --uri="${MONGO_URI}" --out=${BACKUP_DIR} --gzip

# Compression de tous les fichiers
tar -czf ${BACKUP_DIR}.tar.gz ${BACKUP_DIR}

# Transfert vers S3
aws s3 cp ${BACKUP_DIR}.tar.gz s3://company-backups-primary/databases/mongodb/

# Nettoyage
rm -rf ${BACKUP_DIR}
```

### Fichiers Utilisateurs

```bash
#!/bin/bash
# /scripts/backup/user-files.sh

DATE=$(date +%Y-%m-%d)
SOURCE_DIR="/mnt/user-content"
BACKUP_DIR="/mnt/backups/files/user-content/${DATE}"

# Sauvegarde incrémentielle avec rsync
rsync -avz --delete ${SOURCE_DIR}/ ${BACKUP_DIR}/

# Compression
tar -czf ${BACKUP_DIR}.tar.gz ${BACKUP_DIR}

# Transfert vers S3
aws s3 cp ${BACKUP_DIR}.tar.gz s3://company-backups-primary/files/user-content/

# Nettoyage
rm -rf ${BACKUP_DIR}
```

### Configurations Systèmes et Applications

```bash
#!/bin/bash
# /scripts/backup/configurations.sh

DATE=$(date +%Y-%m-%d)
BACKUP_DIR="/mnt/backups/configurations/${DATE}"

# Exportation des configurations Kubernetes
mkdir -p ${BACKUP_DIR}/kubernetes
kubectl get all --all-namespaces -o yaml > ${BACKUP_DIR}/kubernetes/all-resources.yaml
kubectl get configmaps --all-namespaces -o yaml > ${BACKUP_DIR}/kubernetes/configmaps.yaml
kubectl get secrets --all-namespaces -o yaml > ${BACKUP_DIR}/kubernetes/secrets.yaml

# Sauvegarde des fichiers de configuration des applications
mkdir -p ${BACKUP_DIR}/applications
cp -r /etc/app/* ${BACKUP_DIR}/applications/

# Compression
tar -czf ${BACKUP_DIR}.tar.gz ${BACKUP_DIR}

# Transfert vers S3
aws s3 cp ${BACKUP_DIR}.tar.gz s3://company-backups-primary/configurations/

# Nettoyage
rm -rf ${BACKUP_DIR}
```

## Surveillance et Vérification des Sauvegardes

### Processus de Vérification Automatisé

```bash
#!/bin/bash
# /scripts/verify-backups.sh

DATE=$(date +%Y-%m-%d --date="yesterday")
LOG_FILE="/var/log/backup-verify-${DATE}.log"

echo "Starting backup verification for ${DATE}" > ${LOG_FILE}

# Vérification des sauvegardes PostgreSQL
aws s3 ls s3://company-backups-primary/databases/postgres/${DATE}/ >> ${LOG_FILE}
if [ $? -ne 0 ]; then
  echo "ERROR: PostgreSQL backup missing for ${DATE}" >> ${LOG_FILE}
  /scripts/notify-team.sh "PostgreSQL backup verification failed for ${DATE}"
fi

# Vérification des sauvegardes MongoDB
aws s3 ls s3://company-backups-primary/databases/mongodb/${DATE}.tar.gz >> ${LOG_FILE}
if [ $? -ne 0 ]; then
  echo "ERROR: MongoDB backup missing for ${DATE}" >> ${LOG_FILE}
  /scripts/notify-team.sh "MongoDB backup verification failed for ${DATE}"
fi

# Test de restauration sur environnement de test
/scripts/test-restore.sh --date=${DATE} >> ${LOG_FILE}
```

### Vérification Manuelle Hebdomadaire

Responsable : Équipe DevOps
Jour : Lundi matin
Procédure :
1. Vérifier les logs de sauvegarde de la semaine précédente
2. Confirmer le transfert réussi vers le stockage secondaire
3. Vérifier l'espace de stockage disponible
4. Effectuer un test de restauration aléatoire
5. Documenter les résultats dans le rapport hebdomadaire

## Gestion des Erreurs de Sauvegarde

### Procédure en cas d'échec

1. **Notification** : Alerte automatique à l'équipe d'astreinte via PagerDuty
2. **Diagnostic** :
   - Vérifier les logs de sauvegarde
   - Vérifier la connectivité réseau
   - Vérifier l'espace disque
   - Vérifier les permissions
3. **Correction** :
   - Résoudre le problème technique identifié
   - Lancer une sauvegarde manuelle
4. **Documentation** :
   - Documenter l'incident dans le système de ticketing
   - Mettre à jour les procédures si nécessaire
5. **Prévention** :
   - Analyser la cause profonde
   - Mettre en place des mesures préventives

## Maintenance du Système de Sauvegarde

### Tâches périodiques

- **Hebdomadaire** : Vérification des logs et rapports de sauvegarde
- **Mensuelle** : Test de restauration complet
- **Trimestrielle** : Audit des politiques et permissions
- **Semestrielle** : Révision complète de la stratégie de sauvegarde
- **Annuelle** : Test de disaster recovery complet 