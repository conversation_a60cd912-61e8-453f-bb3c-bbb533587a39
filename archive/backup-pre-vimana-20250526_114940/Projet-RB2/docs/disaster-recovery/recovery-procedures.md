# Procédures de Restauration

Ce document détaille les procédures de restauration à suivre en cas d'incident ou de désastre nécessitant la récupération des données et services.

## Évaluation de l'Incident

Avant de commencer toute restauration, suivez cette procédure d'évaluation :

1. **Identifier l'étendue de l'impact**
   - Systèmes touchés
   - Données potentiellement perdues
   - Utilisateurs affectés

2. **Déterminer la cause**
   - Défaillance technique
   - Erreur humaine
   - Attaque malveillante
   - Catastrophe naturelle

3. **Évaluer les options de restauration**
   - Restauration complète
   - Restauration partielle
   - Basculement vers site secondaire

4. **Définir le niveau d'urgence**
   - Critique (impact financier/réputation immédiat)
   - Élevé (impact sur les opérations métier)
   - Moyen (impact limité, solutions de contournement disponibles)
   - Faible (impact minimal)

## Restauration des Bases de Données

### PostgreSQL

#### Restauration Complète

**Prérequis :**
- Accès aux identifiants administrateur
- Fichier de sauvegarde identifié
- Services applicatifs arrêtés

**Étapes :**

1. **Identifier et récupérer la sauvegarde**
   ```bash
   # Lister les sauvegardes disponibles
   aws s3 ls s3://company-backups-primary/databases/postgres/
   
   # Télécharger la sauvegarde souhaitée
   aws s3 cp s3://company-backups-primary/databases/postgres/2023-05-15/full_backup.dump /tmp/
   ```

2. **Arrêter les services dépendants**
   ```bash
   # Kubernetes
   kubectl scale deployment app-deployment --replicas=0
   
   # Docker Compose
   docker-compose -f docker-compose.prod.yml stop app api
   ```

3. **Restaurer la base de données**
   ```bash
   # Option 1: Base de données vide
   pg_restore -U postgres -d production_db -C /tmp/full_backup.dump
   
   # Option 2: Écraser la base existante
   pg_restore -U postgres -d production_db -c /tmp/full_backup.dump
   ```

4. **Vérifier l'intégrité**
   ```bash
   psql -U postgres -d production_db -c "SELECT count(*) FROM users;"
   psql -U postgres -d production_db -c "SELECT max(created_at) FROM transactions;"
   ```

5. **Redémarrer les services**
   ```bash
   # Kubernetes
   kubectl scale deployment app-deployment --replicas=3
   
   # Docker Compose
   docker-compose -f docker-compose.prod.yml up -d app api
   ```

#### Restauration Point-in-Time (avec WAL)

1. **Récupérer la dernière sauvegarde complète**
   ```bash
   aws s3 cp s3://company-backups-primary/databases/postgres/2023-05-15/full_backup.dump /tmp/
   ```

2. **Récupérer les WAL jusqu'au point désiré**
   ```bash
   mkdir -p /tmp/wal_archives
   aws s3 sync s3://company-backups-primary/databases/postgres/wal/ /tmp/wal_archives/
   ```

3. **Créer un fichier recovery.conf**
   ```
   restore_command = 'gunzip < /tmp/wal_archives/%f.gz > %p'
   recovery_target_time = '2023-05-16 14:30:00 UTC'
   ```

4. **Restaurer la base**
   ```bash
   pg_restore -U postgres -d production_db -C /tmp/full_backup.dump
   ```

5. **Déplacer recovery.conf dans le répertoire de données PostgreSQL et redémarrer**
   ```bash
   cp recovery.conf /var/lib/postgresql/data/
   systemctl restart postgresql
   ```

### MongoDB

#### Restauration Complète

1. **Identifier et récupérer la sauvegarde**
   ```bash
   aws s3 ls s3://company-backups-primary/databases/mongodb/
   aws s3 cp s3://company-backups-primary/databases/mongodb/2023-05-15.tar.gz /tmp/
   tar -xzf /tmp/2023-05-15.tar.gz -C /tmp/
   ```

2. **Arrêter les services dépendants**
   ```bash
   kubectl scale deployment app-deployment --replicas=0
   ```

3. **Restaurer la base de données**
   ```bash
   mongorestore --uri="mongodb://admin:<EMAIL>:27017/admin" --drop /tmp/2023-05-15/
   ```

4. **Vérifier l'intégrité**
   ```bash
   mongo --uri="mongodb://admin:<EMAIL>:27017/admin" --eval "db.users.count()"
   ```

5. **Redémarrer les services**
   ```bash
   kubectl scale deployment app-deployment --replicas=3
   ```

## Restauration des Applications

### Conteneurs Docker/Kubernetes

1. **Identifier la version à restaurer**
   ```bash
   # Consulter les versions d'images disponibles
   aws ecr describe-images --repository-name app-repository
   ```

2. **Redéployer l'application**
   ```bash
   # Kubernetes
   kubectl set image deployment/app-deployment app=ecr.repo.url/app-repository:v1.2.3
   
   # Récupérer les configurations
   aws s3 cp s3://company-backups-primary/configurations/2023-05-15.tar.gz /tmp/
   tar -xzf /tmp/2023-05-15.tar.gz -C /tmp/
   kubectl apply -f /tmp/2023-05-15/kubernetes/configmaps.yaml
   kubectl apply -f /tmp/2023-05-15/kubernetes/secrets.yaml
   ```

3. **Vérifier le déploiement**
   ```bash
   kubectl get pods -l app=app-deployment
   kubectl logs deployment/app-deployment
   ```

## Restauration des Fichiers Utilisateurs

1. **Récupérer la sauvegarde**
   ```bash
   aws s3 cp s3://company-backups-primary/files/user-content/2023-05-15.tar.gz /tmp/
   tar -xzf /tmp/2023-05-15.tar.gz -C /tmp/
   ```

2. **Restaurer les fichiers**
   ```bash
   rsync -avz --delete /tmp/2023-05-15/ /mnt/user-content/
   ```

3. **Vérifier les permissions**
   ```bash
   chown -R app:app /mnt/user-content/
   chmod -R 755 /mnt/user-content/
   ```

## Restauration de l'Infrastructure

### Via Infrastructure as Code

1. **Accéder au code d'infrastructure**
   ```bash
   git clone https://github.com/company/infrastructure.git
   cd infrastructure
   git checkout v2.3.4  # version stable connue
   ```

2. **Restaurer l'état Terraform (si nécessaire)**
   ```bash
   aws s3 cp s3://company-terraform-state/production/terraform.tfstate .
   ```

3. **Appliquer la configuration**
   ```bash
   terraform init
   terraform plan -var-file=production.tfvars
   terraform apply -var-file=production.tfvars
   ```

### Restauration Manuelle (Cloud)

#### AWS EC2

1. **Restaurer une instance à partir d'une AMI**
   ```bash
   aws ec2 run-instances \
     --image-id ami-12345678 \
     --instance-type t3.large \
     --key-name production-key \
     --security-group-ids sg-12345678 \
     --subnet-id subnet-12345678
   ```

2. **Attacher les volumes EBS (si nécessaire)**
   ```bash
   aws ec2 attach-volume \
     --volume-id vol-12345678 \
     --instance-id i-87654321 \
     --device /dev/sdf
   ```

3. **Mettre à jour les entrées DNS**
   ```bash
   aws route53 change-resource-record-sets \
     --hosted-zone-id Z123456789 \
     --change-batch file://dns-changes.json
   ```

## Procédures de Basculement

### Basculement vers une Région Secondaire

1. **Activer les ressources de la région de secours**
   ```bash
   cd infrastructure/dr
   terraform apply -var-file=dr.tfvars
   ```

2. **Vérifier l'état des ressources**
   ```bash
   kubectl --context=dr-cluster get pods
   ```

3. **Mettre à jour la configuration DNS pour rediriger le trafic**
   ```bash
   aws route53 change-resource-record-sets \
     --hosted-zone-id Z123456789 \
     --change-batch file://dr-dns-changes.json
   ```

4. **Informer les utilisateurs du basculement via la page de statut**
   ```bash
   curl -X POST https://status.company.com/api/v1/incidents \
     -H "Authorization: Bearer ${STATUS_API_KEY}" \
     -d '{"title": "Service basculé vers site secondaire", "status": "monitoring"}'
   ```

## Retour à la Normale

### Procédures Post-Restauration

1. **Vérification complète du système**
   ```bash
   ./scripts/health-check.sh --environment=production
   ```

2. **Mise à jour de la documentation**
   - Documenter l'incident
   - Noter les points d'amélioration
   - Mettre à jour les procédures si nécessaire

3. **Communication aux utilisateurs**
   ```bash
   curl -X PATCH https://status.company.com/api/v1/incidents/current \
     -H "Authorization: Bearer ${STATUS_API_KEY}" \
     -d '{"status": "resolved", "message": "Tous les services sont revenus à la normale"}'
   ```

## Temps de Restauration Estimés

| Système | Taille | Temps Estimé |
|---------|--------|--------------|
| Base PostgreSQL | <10 GB | 10-15 minutes |
| Base PostgreSQL | >10 GB | 30-60 minutes |
| Base MongoDB | <10 GB | 10-15 minutes |
| Base MongoDB | >10 GB | 30-60 minutes |
| Fichiers utilisateurs | <100 GB | 20-30 minutes |
| Fichiers utilisateurs | >100 GB | 1-2 heures |
| Infrastructure complète | - | 30-60 minutes |
| Basculement DR | - | 15-30 minutes | 