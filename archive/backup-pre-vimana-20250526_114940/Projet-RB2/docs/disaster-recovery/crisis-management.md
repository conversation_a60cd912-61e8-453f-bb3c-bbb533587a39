# Plan de Gestion de Crise

Ce document définit les rôles, responsabilités et procédures à suivre lors d'un incident majeur nécessitant l'activation du plan de gestion de crise.

## Équipe de Gestion de Crise

### Composition de l'équipe

| R<PERSON><PERSON> | Responsabilité | Contact Principal | Contact Suppléant |
|------|----------------|-------------------|-------------------|
| **Coordinateur de Crise** | Direction globale, prise de décision | CTO | COO |
| **Responsable Technique** | Supervision des opérations de restauration | Lead DevOps | Lead SRE |
| **Responsable BDD** | Restauration et validation des données | DBA Lead | Data Engineer Senior |
| **Responsable Infrastructure** | Gestion de l'infrastructure cloud/on-premise | Infrastructure Manager | Cloud Architect |
| **Responsable Support Client** | Communication avec les clients | Customer Support Manager | Customer Success Lead |
| **Responsable Communication** | Communication interne et externe | CMO | PR Manager |
| **Coordinateur Juridique** | Conformité réglementaire, obligations légales | Legal Counsel | Compliance Officer |

### Contact d'Urgence

**Numéro de conférence dédié** : +33 1 XX XX XX XX  
**Code d'accès** : 123456#  
**Canal Slack d'urgence** : #incident-response  
**Liste de diffusion email** : <EMAIL>

## Niveaux de Crise

### Niveau 1 : Incident Mineur
- Impact limité sur un service non critique
- Résolution attendue < 4 heures
- Gestion par l'équipe d'astreinte standard

### Niveau 2 : Incident Significatif
- Impact sur des services importants
- Temps de restauration estimé entre 4 et 8 heures
- Activation partielle de l'équipe de crise

### Niveau 3 : Incident Majeur
- Impact sur des services critiques
- Temps de restauration estimé > 8 heures
- Potentiel impact financier ou réputationnel
- Activation complète de l'équipe de crise

### Niveau 4 : Crise Critique
- Impact sur l'ensemble des services critiques
- Perte potentielle de données significative
- Fort impact financier et/ou réputationnel
- Activation de l'équipe de crise et du comité de direction

## Procédure d'Activation

### Détection et Évaluation Initiale

1. **Détection de l'incident**
   - Via système de monitoring (alertes)
   - Via signalement utilisateur
   - Via équipe d'astreinte

2. **Évaluation préliminaire**
   - Responsable d'astreinte évalue la gravité
   - Détermine s'il s'agit d'un incident standard ou d'une crise potentielle

3. **Critères d'escalade vers la gestion de crise**
   - Impact sur plus de 25% des utilisateurs
   - Indisponibilité d'un service critique > 1 heure
   - Brèche de sécurité confirmée
   - Perte de données potentielle
   - Temps de restauration estimé > RTO défini

### Activation du Plan

1. **Notification initiale**
   ```
   Incident critique détecté à [HEURE]. Impact sur [SYSTÈMES].
   Niveau de crise estimé : [NIVEAU].
   Conférence d'urgence dans 15 minutes.
   Confirmez votre disponibilité par retour.
   ```

2. **Conférence de démarrage**
   - Évaluation de l'incident
   - Définition du niveau de crise
   - Attribution des rôles
   - Établissement des premiers objectifs
   - Définition de la fréquence des points de situation

3. **Mise en place du centre des opérations**
   - Canal Slack dédié créé : #incident-YYYYMMDD
   - Document de suivi partagé mis en place
   - Salle de conférence permanente activée

## Protocole de Communication

### Communication Interne

1. **Équipe de crise**
   - Points de situation toutes les 30 minutes
   - Communication continue via canal Slack dédié
   - Journal des actions maintenu en temps réel

2. **Personnel de l'entreprise**
   - Notification initiale de l'incident
   - Updates réguliers (toutes les 2 heures)
   - Instructions spécifiques si nécessaire

### Communication Externe

1. **Clients**
   - Mise à jour de la page de statut dans les 15 minutes
   - Email aux clients impactés dans l'heure
   - Updates réguliers (toutes les 2 heures)
   - Estimation du temps de résolution dès que possible

2. **Partenaires stratégiques**
   - Notification si impact sur les services partagés
   - Point de contact dédié assigné

3. **Régulateurs/Autorités**
   - Si requis par la réglementation (RGPD, etc.)
   - Préparé par l'équipe juridique

### Modèles de Communication

#### Notification Initiale (Page de Statut)
```
Nous rencontrons actuellement des difficultés techniques affectant [SERVICE].
Notre équipe technique travaille activement à la résolution de ce problème.
Nous vous fournirons des mises à jour régulières sur cette page.
```

#### Update Intermédiaire
```
L'incident affectant [SERVICE] est toujours en cours d'investigation.
Nous avons identifié [CAUSE SI CONNUE] et travaillons à [ACTION].
Temps de résolution estimé : [ESTIMATION SI POSSIBLE].
Nous nous excusons pour la gêne occasionnée.
```

#### Notification de Résolution
```
L'incident affectant [SERVICE] a été résolu à [HEURE].
Cause : [EXPLICATION SIMPLIFIÉE].
Actions prises : [RÉSUMÉ DES ACTIONS].
Mesures préventives : [SI APPLICABLE].
Nous vous remercions pour votre patience et nous excusons pour les désagréments causés.
```

## Procédures de Gestion de l'Incident

### Phase 1 : Confinement et Évaluation

1. **Limiter l'impact**
   - Isoler les systèmes affectés si nécessaire
   - Mettre en place des mesures temporaires pour les utilisateurs
   - Sécuriser les preuves en cas d'incident de sécurité

2. **Évaluation approfondie**
   - Identifier la cause racine
   - Évaluer l'étendue des dommages
   - Estimer le temps de récupération

3. **Développer un plan d'action**
   - Définir les étapes de résolution
   - Identifier les ressources nécessaires
   - Établir un calendrier de restauration

### Phase 2 : Résolution et Restauration

1. **Exécuter le plan de restauration**
   - Suivre les procédures de disaster recovery appropriées
   - Documenter toutes les actions entreprises
   - Effectuer des vérifications à chaque étape

2. **Contrôle de qualité**
   - Vérifier l'intégrité des systèmes restaurés
   - Effectuer des tests fonctionnels
   - Valider avec les utilisateurs clés si possible

3. **Retour à la normale**
   - Réactiver progressivement tous les services
   - Surveiller étroitement les performances
   - Préparer la communication de résolution

### Phase 3 : Analyse Post-Incident

1. **Réunion post-mortem**
   - Organiser dans les 48h après résolution
   - Participants : équipe de crise + experts techniques impliqués
   - Analyse sans blâme, focus sur l'amélioration

2. **Documentation complète**
   - Chronologie détaillée
   - Analyse des causes
   - Actions entreprises
   - Efficacité des actions

3. **Plan d'amélioration**
   - Mesures pour éviter la récurrence
   - Améliorations des procédures de DR
   - Besoins en formation ou ressources

## Scénarios Spécifiques

### Attaque de Sécurité

**Actions immédiates supplémentaires :**
- Impliquer l'équipe de sécurité dès la détection
- Considérer la déconnexion du réseau externe
- Préserver les preuves pour analyse forensique
- Préparer la notification aux autorités si nécessaire (CNIL)

### Défaillance du Site Principal

**Actions immédiates supplémentaires :**
- Activer la procédure de basculement vers le site secondaire
- Vérifier la réplication des données récentes
- Rediriger le trafic utilisateur

### Catastrophe Naturelle

**Actions immédiates supplémentaires :**
- Vérifier la sécurité physique des employés en priorité
- Évaluer les dommages à l'infrastructure
- Activer les procédures de travail à distance si nécessaire

## Formation et Préparation

### Programme d'Exercices

- **Fréquence** : Trimestrielle
- **Types d'exercices** :
  - Simulations sur table
  - Tests techniques limités
  - Simulations complètes

### Évaluation et Amélioration

- Revue du plan après chaque exercice
- Mise à jour du plan suite aux leçons apprises
- Formation continue des membres de l'équipe

## Annexes

### Checklist d'Activation de Crise

- [ ] Incident détecté et évalué comme critique
- [ ] Coordinateur de crise notifié
- [ ] Équipe de crise convoquée
- [ ] Canal Slack d'incident créé
- [ ] Document de suivi partagé créé
- [ ] Conférence téléphonique établie
- [ ] Page de statut mise à jour
- [ ] Première évaluation complétée
- [ ] Plan d'action initial établi
- [ ] Communication initiale envoyée

### Liste des Contacts d'Urgence

| Service | Contact | Numéro | Email |
|---------|---------|--------|-------|
| AWS Support | Support Premium | +1 XXX XXX XXXX | N/A |
| Google Cloud | Support Enterprise | +1 XXX XXX XXXX | N/A |
| Fournisseur Internet | NOC | +33 X XX XX XX XX | <EMAIL> |
| CERT-FR | Réponse incidents | +33 X XX XX XX XX | <EMAIL> |
| CNIL | Notification violations | N/A | <EMAIL> |

### Journal de Bord de l'Incident

**Modèle à utiliser dans le document partagé :**

```
INCIDENT: [IDENTIFIANT]
DATE: [DATE]
NIVEAU: [1-4]

CHRONOLOGIE:
[HEURE] - [ACTION/OBSERVATION] - [RESPONSABLE]
[HEURE] - [ACTION/OBSERVATION] - [RESPONSABLE]
...

STATUT ACTUEL:
[RÉSUMÉ DE LA SITUATION]

PROCHAINES ACTIONS:
- [ACTION 1] - [RESPONSABLE] - [DEADLINE]
- [ACTION 2] - [RESPONSABLE] - [DEADLINE]
...

COMMUNICATIONS ENVOYÉES:
[HEURE] - [TYPE] - [DESTINATAIRES] - [RÉSUMÉ]
...
``` 