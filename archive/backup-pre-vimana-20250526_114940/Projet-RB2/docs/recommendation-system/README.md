# Système de Recommandation - Documentation

## Vue d'ensemble

Le système de recommandation de Retreat And Be est conçu pour fournir des recommandations personnalisées aux utilisateurs en fonction de leurs préférences, comportements et contextes. Il utilise une combinaison d'algorithmes avancés, d'optimisations de performance et de fonctionnalités d'analyse pour offrir une expérience utilisateur optimale.

## Architecture

Le système de recommandation est organisé en plusieurs composants :

1. **Services de base** : Implémentent les différents algorithmes de recommandation
2. **Services d'optimisation** : Améliorent les performances du système
3. **Services d'apprentissage automatique** : Utilisent des techniques avancées d'IA
4. **Services d'analyse** : Fournissent des métriques et des visualisations
5. **Contrôleurs** : Exposent les fonctionnalités via des API REST

## Algorithmes de recommandation

Le système prend en charge plusieurs stratégies de recommandation :

| Stratégie | Description | Cas d'utilisation |
|-----------|-------------|-------------------|
| `CONTENT_BASED` | Recommandations basées sur les attributs des items et les préférences utilisateur | Nouveaux utilisateurs avec peu d'historique |
| `COLLABORATIVE` | Recommandations basées sur les comportements similaires d'utilisateurs | Utilisateurs avec un historique d'interactions |
| `HYBRID` | Combinaison des approches basées sur le contenu et le filtrage collaboratif | Cas général, équilibre entre précision et diversité |
| `MATRIX_FACTORIZATION` | Recommandations basées sur des modèles de factorisation matricielle | Utilisateurs avec un historique d'interactions important |
| `CONTEXTUAL` | Recommandations adaptées au contexte (saison, localisation, etc.) | Recommandations sensibles au contexte |
| `KNOWLEDGE_BASED` | Recommandations basées sur des règles métier et l'expertise du domaine | Cas spécifiques nécessitant des règles métier |
| `DEEP_LEARNING` | Recommandations basées sur des modèles d'apprentissage profond | Utilisateurs avec des comportements complexes |
| `REALTIME` | Recommandations en temps réel basées sur le comportement immédiat | Sessions actives avec de nombreuses interactions |

## Optimisations de performance

Le système intègre plusieurs optimisations pour améliorer les performances :

1. **Cache** : Stockage temporaire des recommandations fréquemment demandées
2. **Préchargement** : Génération anticipée des recommandations pour les utilisateurs actifs
3. **Diversification** : Équilibre entre pertinence et diversité des recommandations
4. **Pagination** : Chargement progressif des recommandations

## Fonctionnalités avancées

### A/B Testing

Le système intègre un mécanisme d'A/B testing pour comparer l'efficacité des différents algorithmes de recommandation. Les utilisateurs sont répartis dans différents groupes de test, et leurs interactions sont suivies pour évaluer les performances de chaque stratégie.

### Personnalisation avancée

Les utilisateurs peuvent configurer leurs préférences de recommandation, notamment :

- Stratégie de recommandation préférée
- Paramètres de diversification
- Catégories préférées et à éviter
- Nombre maximum de recommandations

### Explications des recommandations

Chaque recommandation peut être accompagnée d'une explication personnalisée indiquant pourquoi elle a été suggérée à l'utilisateur. Ces explications améliorent la transparence et la confiance des utilisateurs.

### Recommandations collaboratives

Les utilisateurs peuvent partager des recommandations avec d'autres utilisateurs, créant ainsi une dimension sociale au système de recommandation.

### Intégration de données externes

Le système peut enrichir les recommandations avec des données provenant de sources externes, telles que :

- Tendances Google
- Actualités
- Données météorologiques
- Événements

### Recommandations en temps réel

Le système peut générer des recommandations en temps réel basées sur le comportement immédiat de l'utilisateur pendant sa session.

## Analyse et visualisation

Le système fournit des outils d'analyse avancés pour évaluer l'efficacité des recommandations :

1. **Métriques globales** : Taux de conversion, taux d'engagement, etc.
2. **Métriques par utilisateur** : Interactions, préférences, etc.
3. **Visualisations** : Graphes de similarité, cartes de chaleur, etc.
4. **Rapports PDF** : Génération de rapports détaillés

## API

### Recommandations

```
GET /api/recommendations
```

Paramètres :
- `type` : Type de recommandation (COURSE, RETREAT, etc.)
- `limit` : Nombre maximum de recommandations
- `offset` : Décalage pour la pagination
- `strategy` : Stratégie de recommandation
- `includeExplanations` : Inclure les explications (true/false)
- `includeExternalData` : Inclure les données externes (true/false)

### A/B Testing

```
GET /api/ab-testing/results
```

Récupère les résultats des tests A/B.

```
POST /api/ab-testing/interaction
```

Enregistre une interaction dans le cadre d'un test A/B.

### Préférences de recommandation

```
GET /api/recommendation-preferences
```

Récupère les préférences de recommandation de l'utilisateur.

```
PUT /api/recommendation-preferences
```

Met à jour les préférences de recommandation de l'utilisateur.

### Interactions en temps réel

```
POST /api/realtime-interactions
```

Enregistre une interaction en temps réel.

### Partage collaboratif

```
POST /api/collaborative-sharing/share
```

Partage une recommandation avec un autre utilisateur.

```
GET /api/collaborative-sharing/received
```

Récupère les recommandations partagées avec l'utilisateur.

```
GET /api/collaborative-sharing/sent
```

Récupère les recommandations partagées par l'utilisateur.

### Analyse

```
GET /api/recommendation-analytics/global
```

Récupère les métriques globales des recommandations.

```
GET /api/recommendation-analytics/me
```

Récupère les métriques des recommandations pour l'utilisateur courant.

### Visualisation

```
GET /api/recommendation-visualization/user-similarity-graph
```

Génère un graphe de similarité entre les utilisateurs.

```
GET /api/recommendation-visualization/my-recommendation-graph
```

Génère un graphe de recommandations pour l'utilisateur courant.

```
GET /api/recommendation-visualization/recommendation-heatmap
```

Génère une carte de chaleur des recommandations.

### Rapports

```
GET /api/recommendation-reports/global
```

Génère un rapport PDF global sur les recommandations.

```
GET /api/recommendation-reports/me
```

Génère un rapport PDF pour l'utilisateur courant.

## Modèle de données

Le système utilise les modèles de données suivants :

- `Recommendation` : Recommandation générée pour un utilisateur
- `UserInteraction` : Interaction d'un utilisateur avec une recommandation
- `UserPreference` : Préférences de recommandation d'un utilisateur
- `ABTestAssignment` : Assignation d'un utilisateur à un groupe de test A/B
- `ABTestInteraction` : Interaction dans le cadre d'un test A/B
- `ABTestMetrics` : Métriques des tests A/B
- `ExternalData` : Données externes pour enrichir les recommandations
- `SharedRecommendation` : Recommandation partagée entre utilisateurs
- `AnalyticsReport` : Rapport d'analyse des recommandations

## Déploiement

Le système de recommandation est déployé en tant que module NestJS au sein du backend. Il peut être configuré via le fichier de configuration de l'application.

## Configuration

Exemple de configuration :

```yaml
recommendation:
  defaultStrategy: HYBRID
  cache:
    enabled: true
    ttl: 3600 # 1 heure
  preloader:
    enabled: true
    interval: 3600 # 1 heure
    userLimit: 100 # Nombre maximum d'utilisateurs à précharger
  abTesting:
    enabled: true
    testId: recommendation-test-1
    startDate: 2023-01-01
    endDate: 2023-01-31
  externalData:
    enabled: true
    refreshInterval: 24 # 24 heures
    apiKeys:
      googleTrends: API_KEY
      newsApi: API_KEY
      openWeatherMap: API_KEY
  reports:
    directory: reports
```

## Développement

Pour étendre le système de recommandation, vous pouvez :

1. Ajouter de nouvelles stratégies de recommandation
2. Intégrer de nouvelles sources de données externes
3. Améliorer les algorithmes existants
4. Ajouter de nouvelles visualisations et métriques

## Conclusion

Le système de recommandation de Retreat And Be offre une solution complète et flexible pour fournir des recommandations personnalisées aux utilisateurs. Grâce à ses algorithmes avancés, ses optimisations de performance et ses fonctionnalités d'analyse, il permet d'améliorer l'expérience utilisateur et d'augmenter l'engagement sur la plateforme.
