# Module d'Apprentissage Continu

## Introduction

Le module d'apprentissage continu est un composant clé du système de recommandation de Retreat And Be. Il permet d'adapter les recommandations en fonction des interactions des utilisateurs en temps réel, améliorant ainsi la pertinence et la personnalisation des suggestions.

## Objectifs

- **Adaptation en temps réel** : Ajuster les recommandations en fonction des interactions récentes des utilisateurs
- **Détection des changements de comportement** : Identifier les évolutions dans les préférences des utilisateurs
- **Amélioration continue** : Optimiser les modèles de recommandation au fil du temps
- **Transparence** : Fournir des explications sur les recommandations et leur évolution

## Architecture

Le module d'apprentissage continu s'intègre au système de recommandation existant et se compose des éléments suivants :

### Composants principaux

1. **Service d'apprentissage continu** (`ContinuousLearningService`) : Coordonne l'ensemble du processus d'apprentissage
2. **Modèles utilisateur** (`UserModel`) : Stockent les préférences et intérêts des utilisateurs
3. **Système de traitement des interactions** : Collecte et analyse les interactions utilisateur
4. **Mécanismes de détection des changements** : Identifie les évolutions dans les comportements
5. **API d'explications** : Fournit des informations sur les recommandations

### Flux de données

```
Interaction utilisateur → File d'attente → Traitement → Mise à jour du modèle → Adaptation des recommandations
```

## Fonctionnalités

### 1. Traitement des interactions en temps réel

Le système collecte et traite les interactions utilisateur (vues, likes, bookmarks, etc.) en temps réel pour adapter les recommandations. Les interactions sont d'abord placées dans une file d'attente, puis traitées par lots pour optimiser les performances.

### 2. Modèles utilisateur adaptatifs

Chaque utilisateur dispose d'un modèle personnalisé qui évolue en fonction de ses interactions. Ce modèle comprend :

- Préférences explicites
- Intérêts par catégorie
- Intérêts par tag
- Historique des interactions récentes
- Contexte utilisateur (localisation, appareil, etc.)

### 3. Détection des changements de comportement

Le système est capable de détecter les changements significatifs dans le comportement des utilisateurs, comme :

- Nouveaux centres d'intérêt
- Changements de préférences
- Évolutions saisonnières
- Comportements inhabituels

### 4. Filtrage des comportements aberrants

Pour éviter que des comportements atypiques n'influencent trop les recommandations, le système intègre des mécanismes de détection et de filtrage des anomalies.

### 5. Métriques d'évaluation

Le module fournit des métriques détaillées pour évaluer la performance des recommandations :

- Précision
- Rappel
- Score F1
- Taux de conversion
- Engagement utilisateur

## Configuration

Le module d'apprentissage continu est hautement configurable via le fichier `continuous-learning.config.ts`. Les principaux paramètres sont :

| Paramètre | Description | Valeur par défaut |
|-----------|-------------|-------------------|
| `learningRate` | Taux d'apprentissage pour les mises à jour incrémentielles | 0.1 |
| `forgettingFactor` | Facteur d'oubli pour les anciennes interactions | 0.95 |
| `changeDetectionThreshold` | Seuil pour détecter les changements de comportement | 0.3 |
| `recentInteractionsWindow` | Fenêtre temporelle pour l'analyse des interactions récentes (en heures) | 24 |
| `minInteractionsForUpdate` | Nombre minimum d'interactions pour déclencher une mise à jour | 5 |
| `modelUpdateInterval` | Intervalle de mise à jour des modèles (en minutes) | 15 |
| `interactionWeights` | Poids des différents types d'interactions | Voir config |
| `enableOutlierDetection` | Activer/désactiver la détection des comportements aberrants | true |
| `outlierDetectionThreshold` | Seuil pour la détection des comportements aberrants | 2.5 |

## API

Le module expose plusieurs endpoints pour interagir avec le système d'apprentissage continu :

### Endpoints utilisateur

- `GET /api/v1/recommendation/continuous-learning/metrics/me` : Récupérer les métriques d'apprentissage pour l'utilisateur courant
- `GET /api/v1/recommendation/continuous-learning/model/me` : Récupérer le modèle utilisateur pour l'utilisateur courant

### Endpoints administrateur

- `GET /api/v1/recommendation/continuous-learning/metrics/user/:userId` : Récupérer les métriques d'apprentissage pour un utilisateur spécifique
- `GET /api/v1/recommendation/continuous-learning/model/user/:userId` : Récupérer le modèle utilisateur pour un utilisateur spécifique
- `POST /api/v1/recommendation/continuous-learning/event/:userId` : Enregistrer un événement d'apprentissage
- `POST /api/v1/recommendation/continuous-learning/params` : Mettre à jour les paramètres d'apprentissage continu

## Modèle de données

Le module utilise plusieurs modèles de données pour stocker les informations nécessaires à l'apprentissage continu :

### UserModel

Stocke les informations générales du modèle utilisateur :

```prisma
model UserModel {
  id        String   @id @default(uuid())
  userId    String   @unique
  context   Json     @default("{}")
  metrics   Json     @default("{}")
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  user              User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  preferences       UserPreference[]
  categoryInterests CategoryInterest[]
  tagInterests      TagInterest[]
}
```

### UserPreference

Stocke les préférences explicites des utilisateurs :

```prisma
model UserPreference {
  id          String   @id @default(uuid())
  userId      String
  userModelId String
  key         String
  value       Float
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userModel UserModel @relation(fields: [userModelId], references: [id], onDelete: Cascade)

  @@unique([userId, key])
}
```

### CategoryInterest

Stocke les intérêts des utilisateurs par catégorie :

```prisma
model CategoryInterest {
  id            String   @id @default(uuid())
  userId        String
  userModelId   String
  category      String
  interestLevel Float
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now())

  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userModel UserModel @relation(fields: [userModelId], references: [id], onDelete: Cascade)

  @@unique([userId, category])
}
```

### TagInterest

Stocke les intérêts des utilisateurs par tag :

```prisma
model TagInterest {
  id            String   @id @default(uuid())
  userId        String
  userModelId   String
  tag           String
  interestLevel Float
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now())

  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userModel UserModel @relation(fields: [userModelId], references: [id], onDelete: Cascade)

  @@unique([userId, tag])
}
```

### LearningEvent

Stocke les événements d'apprentissage :

```prisma
model LearningEvent {
  id              String   @id @default(uuid())
  userId          String
  eventType       String
  data            Json
  estimatedImpact Float    @default(0.5)
  timestamp       DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

## Intégration avec les autres services

Le module d'apprentissage continu s'intègre avec plusieurs autres services du système :

- **RecommendationService** : Utilise les modèles utilisateur pour personnaliser les recommandations
- **RealtimeRecommendationService** : Fournit des interactions en temps réel pour l'apprentissage
- **ABTestingService** : Évalue l'efficacité des différentes stratégies d'apprentissage
- **AnalyticsService** : Fournit des métriques pour évaluer les performances du système

## Prochaines étapes

- Implémentation d'algorithmes d'apprentissage plus avancés (apprentissage par renforcement, réseaux de neurones)
- Amélioration de la détection des changements de comportement
- Optimisation des performances pour gérer un plus grand volume d'interactions
- Développement d'une interface utilisateur pour visualiser les métriques d'apprentissage
