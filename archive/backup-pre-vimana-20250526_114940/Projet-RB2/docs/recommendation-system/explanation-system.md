# Système d'Explications des Recommandations

## Introduction

Le système d'explications des recommandations est un composant essentiel du système de recommandation de Retreat And Be. Il permet de fournir des explications claires et transparentes sur les recommandations proposées aux utilisateurs, améliorant ainsi la confiance et l'engagement.

## Objectifs

- **Transparence** : Expliquer clairement pourquoi une recommandation a été faite
- **Confiance** : Renforcer la confiance des utilisateurs dans le système de recommandation
- **Engagement** : Encourager les utilisateurs à interagir avec les recommandations
- **Apprentissage** : Aider les utilisateurs à comprendre leurs préférences
- **Amélioration** : Collecter des données sur l'efficacité des explications pour améliorer le système

## Architecture

Le système d'explications s'intègre au système de recommandation existant et se compose des éléments suivants :

### Composants principaux

1. **Service d'explications** (`ExplanationService`) : Génère des explications personnalisées pour les recommandations
2. **Contrôleur d'explications** (`ExplanationController`) : Expose les endpoints de l'API pour les explications
3. **Modèles de données** : Stockent les explications, les événements et les modèles d'explication
4. **Intégration avec l'apprentissage continu** : Utilise les données du module d'apprentissage continu pour améliorer les explications

### Flux de données

```
Recommandation → Génération d'explication → Présentation à l'utilisateur → Collecte de feedback → Amélioration
```

## Types d'explications

Le système prend en charge plusieurs types d'explications, en fonction de la stratégie de recommandation utilisée :

### 1. Explications basées sur le contenu

Expliquent les recommandations en fonction des attributs du contenu qui correspondent aux préférences de l'utilisateur.

**Exemple** : "Cette retraite vous est recommandée car elle correspond à votre intérêt pour le yoga et la méditation."

### 2. Explications basées sur le filtrage collaboratif

Expliquent les recommandations en fonction des comportements d'utilisateurs similaires.

**Exemple** : "5 utilisateurs avec des intérêts similaires aux vôtres ont aimé cette retraite."

### 3. Explications basées sur la factorisation matricielle

Fournissent une explication générique pour les recommandations basées sur des modèles complexes.

**Exemple** : "Notre modèle prédictif avancé a identifié des motifs cachés dans vos préférences qui correspondent à cette recommandation."

### 4. Explications contextuelles

Expliquent les recommandations en fonction du contexte actuel de l'utilisateur.

**Exemple** : "Cette retraite est particulièrement pertinente pour la saison actuelle (été) et se trouve à proximité de votre localisation (Paris)."

### 5. Explications en temps réel

Expliquent les recommandations en fonction des interactions récentes de l'utilisateur.

**Exemple** : "Basé sur votre activité récente, nous avons remarqué votre intérêt pour la catégorie 'Yoga'. Cette recommandation contient des sujets similaires à ceux que vous avez consultés récemment : méditation, pleine conscience."

## Facteurs d'explication

Chaque explication est composée de plusieurs facteurs qui ont influencé la recommandation. Les principaux types de facteurs sont :

- **Catégorie** : Correspondance avec les catégories préférées de l'utilisateur
- **Tag** : Correspondance avec les tags d'intérêt de l'utilisateur
- **Similarité** : Similarité avec des éléments appréciés par l'utilisateur
- **Popularité** : Popularité de l'élément parmi des utilisateurs similaires
- **Contexte** : Facteurs contextuels comme la localisation, la saison, etc.
- **Interaction** : Interactions récentes de l'utilisateur

## API

Le système expose plusieurs endpoints pour interagir avec les explications :

### Endpoints utilisateur

- `GET /api/v1/recommendation/explanations/:recommendationId` : Récupérer une explication pour une recommandation
- `POST /api/v1/recommendation/explanations/:recommendationId/events` : Enregistrer un événement d'interaction avec une explication

### Endpoints administrateur

- `GET /api/v1/recommendation/explanations/stats` : Récupérer les statistiques d'explications

## Modèle de données

Le système utilise plusieurs modèles de données pour stocker les informations nécessaires aux explications :

### ExplanationHistory

Stocke l'historique des explications générées :

```prisma
model ExplanationHistory {
  id              String   @id @default(uuid())
  recommendationId String
  userId          String
  itemId          String
  itemType        String
  strategy        String
  score           Float
  factors         Json
  summary         String
  metadata        Json     @default("{}")
  createdAt       DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

### ExplanationEvent

Stocke les événements d'interaction avec les explications :

```prisma
model ExplanationEvent {
  id              String   @id @default(uuid())
  userId          String
  recommendationId String
  eventType       String
  data            Json     @default("{}")
  createdAt       DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

### ExplanationTemplate

Stocke les modèles de texte pour les explications :

```prisma
model ExplanationTemplate {
  id              String   @id @default(uuid())
  name            String
  description     String
  factorType      String
  template        String
  variables       String[] @default([])
  language        String   @default("fr")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now())
}
```

## Intégration avec l'apprentissage continu

Le système d'explications s'intègre étroitement avec le module d'apprentissage continu pour :

1. **Utiliser les modèles utilisateur** pour générer des explications plus précises
2. **Collecter des données sur l'efficacité des explications** pour améliorer les recommandations
3. **Adapter les explications** en fonction des changements de comportement des utilisateurs
4. **Fournir des métriques** pour évaluer l'impact des explications sur l'engagement

## Métriques d'évaluation

Le système fournit plusieurs métriques pour évaluer l'efficacité des explications :

- **Taux d'interaction** : Pourcentage d'utilisateurs qui interagissent avec les explications
- **Taux de conversion** : Pourcentage d'utilisateurs qui suivent une recommandation après avoir vu son explication
- **Facteurs les plus influents** : Facteurs qui ont le plus d'impact sur les décisions des utilisateurs
- **Satisfaction utilisateur** : Feedback des utilisateurs sur la qualité des explications

## Prochaines étapes

- **Personnalisation avancée** des explications en fonction des préférences utilisateur
- **Explications multimodales** combinant texte, images et graphiques
- **Tests A/B** pour évaluer différentes stratégies d'explication
- **Intégration avec l'interface utilisateur** pour une présentation optimale des explications
- **Explications interactives** permettant aux utilisateurs d'explorer les facteurs de recommandation
