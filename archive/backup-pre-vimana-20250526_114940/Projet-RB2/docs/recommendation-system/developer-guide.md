# Guide du développeur - Système de Recommandation

Ce guide est destiné aux développeurs qui souhaitent étendre ou modifier le système de recommandation de Retreat And Be.

## Architecture du code

Le système de recommandation est organisé selon la structure suivante :

```
src/modules/recommendation/
├── controllers/           # Contrôleurs REST
├── dto/                   # Objets de transfert de données
├── enums/                 # Énumérations
├── interfaces/            # Interfaces
├── services/              # Services
├── tests/                 # Tests unitaires
└── recommendation.module.ts  # Module NestJS
```

### Services principaux

- `RecommendationService` : Service principal qui coordonne les différentes stratégies
- `ContentBasedService` : Recommandations basées sur le contenu
- `CollaborativeFilteringService` : Recommandations basées sur le filtrage collaboratif
- `HybridRecommendationService` : Combinaison des approches basées sur le contenu et le filtrage collaboratif
- `MatrixFactorizationService` : Recommandations basées sur des modèles de factorisation matricielle
- `ContextualRecommendationService` : Recommandations adaptées au contexte
- `DeepLearningService` : Recommandations basées sur des modèles d'apprentissage profond
- `RealtimeRecommendationService` : Recommandations en temps réel

### Services d'optimisation

- `RecommendationCacheService` : Cache des recommandations
- `RecommendationPreloaderService` : Préchargement des recommandations
- `DiversityFilterService` : Diversification des recommandations

### Services avancés

- `ABTestingService` : Tests A/B pour comparer les stratégies
- `PersonalizationService` : Personnalisation des recommandations
- `ExplanationService` : Explications des recommandations
- `CollaborativeSharingService` : Partage de recommandations entre utilisateurs
- `ExternalDataService` : Intégration de données externes

### Services d'analyse

- `AnalyticsService` : Métriques et analyses
- `VisualizationService` : Visualisations des recommandations
- `ReportGeneratorService` : Génération de rapports PDF

## Ajouter une nouvelle stratégie de recommandation

Pour ajouter une nouvelle stratégie de recommandation, suivez ces étapes :

1. Créez un nouveau service dans le répertoire `services/`
2. Implémentez les méthodes `getRecommendations` et `countRecommendations`
3. Ajoutez la nouvelle stratégie à l'énumération `RecommendationStrategy`
4. Mettez à jour le service `RecommendationService` pour utiliser la nouvelle stratégie
5. Ajoutez des tests unitaires pour la nouvelle stratégie

Exemple de service de recommandation :

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';

@Injectable()
export class NewStrategyService {
  private readonly logger = new Logger(NewStrategyService.name);

  constructor(private readonly prisma: PrismaService) {}

  async getRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    this.logger.log(`Génération de recommandations avec la nouvelle stratégie pour l'utilisateur ${userId}`);
    
    // Implémentez votre logique de recommandation ici
    
    return [];
  }

  async countRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ): Promise<number> {
    // Implémentez votre logique de comptage ici
    
    return 0;
  }
}
```

## Intégrer une nouvelle source de données externes

Pour intégrer une nouvelle source de données externes, suivez ces étapes :

1. Mettez à jour le service `ExternalDataService` pour ajouter la nouvelle source
2. Ajoutez les paramètres de configuration nécessaires
3. Implémentez la logique de récupération et de traitement des données
4. Mettez à jour les tests unitaires

Exemple d'ajout d'une nouvelle source :

```typescript
// Dans le constructeur du service ExternalDataService
this.sources = [
  // Sources existantes
  {
    name: 'NewSource',
    enabled: this.configService.get<boolean>('recommendation.externalData.sources.newSource.enabled', false),
    url: 'https://api.newsource.com/data',
    apiKeyName: 'newSource',
    dataType: 'NEW_TYPE',
  },
];

// Ajouter la clé API
this.apiKeys = {
  // Clés existantes
  newSource: this.configService.get<string>('recommendation.externalData.apiKeys.newSource', ''),
};

// Implémenter le traitement des données
private processSourceData(source: ExternalDataSource, rawData: any): Partial<ExternalData>[] {
  // Code existant
  
  if (source.name === 'NewSource') {
    // Traitement spécifique pour la nouvelle source
    return rawData.items.map(item => ({
      type: 'NEW_TYPE' as ExternalDataType,
      source: source.name,
      title: item.title,
      content: item.description,
      url: item.link,
      imageUrl: item.image,
      relevanceScore: 0.7,
      metadata: {
        // Métadonnées spécifiques
      },
      applicableTypes: [RecommendationType.COURSE, RecommendationType.RETREAT],
    }));
  }
  
  // Autres cas
}
```

## Ajouter une nouvelle visualisation

Pour ajouter une nouvelle visualisation, suivez ces étapes :

1. Mettez à jour le service `VisualizationService` pour ajouter la nouvelle méthode de visualisation
2. Créez un nouvel endpoint dans le contrôleur `VisualizationController`
3. Implémentez la logique de génération de la visualisation
4. Mettez à jour les tests unitaires

Exemple d'ajout d'une nouvelle visualisation :

```typescript
// Dans le service VisualizationService
async generateNewVisualization(
  param1: string,
  param2: number,
): Promise<NewVisualizationResult> {
  try {
    this.logger.log(`Génération d'une nouvelle visualisation (param1=${param1}, param2=${param2})`);
    
    // Implémentez votre logique de visualisation ici
    
    return {
      // Résultat de la visualisation
    };
  } catch (error) {
    this.logger.error(`Erreur lors de la génération de la visualisation: ${error.message}`);
    return {
      // Résultat par défaut en cas d'erreur
    };
  }
}

// Dans le contrôleur VisualizationController
@Get('new-visualization')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@ApiOperation({ summary: 'Générer une nouvelle visualisation' })
@ApiResponse({ status: 200, description: 'Nouvelle visualisation' })
async getNewVisualization(
  @Query('param1') param1: string,
  @Query('param2') param2?: number,
) {
  this.logger.log(`Génération d'une nouvelle visualisation (param1=${param1}, param2=${param2})`);
  
  return this.visualizationService.generateNewVisualization(
    param1,
    param2 ? parseInt(param2.toString(), 10) : undefined,
  );
}
```

## Améliorer les algorithmes existants

Pour améliorer un algorithme existant, suivez ces étapes :

1. Identifiez le service à améliorer
2. Comprenez la logique actuelle et les points d'amélioration
3. Implémentez les modifications
4. Mettez à jour les tests unitaires
5. Utilisez l'A/B testing pour comparer l'ancienne et la nouvelle version

## Ajouter de nouvelles métriques d'analyse

Pour ajouter de nouvelles métriques d'analyse, suivez ces étapes :

1. Mettez à jour le service `AnalyticsService` pour ajouter les nouvelles métriques
2. Mettez à jour les interfaces correspondantes
3. Implémentez la logique de calcul des métriques
4. Mettez à jour les tests unitaires

Exemple d'ajout d'une nouvelle métrique :

```typescript
// Dans l'interface GlobalMetrics
export interface GlobalMetrics {
  // Métriques existantes
  
  /** Nouvelles métriques */
  newMetrics: {
    metric1: number;
    metric2: number;
  };
}

// Dans la méthode getGlobalMetrics du service AnalyticsService
async getGlobalMetrics(
  startDate?: Date,
  endDate?: Date,
): Promise<GlobalMetrics> {
  // Code existant
  
  // Calculer les nouvelles métriques
  const metric1 = await this.calculateMetric1(start, end);
  const metric2 = await this.calculateMetric2(start, end);
  
  return {
    // Métriques existantes
    
    newMetrics: {
      metric1,
      metric2,
    },
  };
}

// Ajouter les méthodes de calcul
private async calculateMetric1(startDate: Date, endDate: Date): Promise<number> {
  // Implémentez votre logique de calcul ici
  return 0;
}

private async calculateMetric2(startDate: Date, endDate: Date): Promise<number> {
  // Implémentez votre logique de calcul ici
  return 0;
}
```

## Bonnes pratiques

### Performance

- Utilisez le cache pour les opérations coûteuses
- Optimisez les requêtes à la base de données
- Utilisez la pagination pour limiter la taille des résultats
- Préchargez les données fréquemment utilisées

### Sécurité

- Validez toutes les entrées utilisateur
- Utilisez les gardes d'authentification et d'autorisation
- Ne stockez pas de données sensibles dans les logs

### Testabilité

- Écrivez des tests unitaires pour chaque service
- Utilisez des mocks pour simuler les dépendances
- Testez les cas limites et les cas d'erreur

### Maintenabilité

- Suivez les principes SOLID
- Documentez votre code
- Utilisez des noms de variables et de méthodes explicites
- Évitez la duplication de code

## Dépannage

### Problèmes courants

1. **Les recommandations ne sont pas générées**
   - Vérifiez les logs pour les erreurs
   - Vérifiez que l'utilisateur a des interactions
   - Vérifiez que la stratégie de recommandation est correctement configurée

2. **Les performances sont lentes**
   - Vérifiez que le cache est activé
   - Optimisez les requêtes à la base de données
   - Utilisez le préchargement pour les utilisateurs actifs

3. **Les tests A/B ne fonctionnent pas**
   - Vérifiez que l'A/B testing est activé dans la configuration
   - Vérifiez que les utilisateurs sont correctement assignés aux groupes
   - Vérifiez que les interactions sont correctement enregistrées

## Ressources

- [Documentation NestJS](https://docs.nestjs.com/)
- [Documentation Prisma](https://www.prisma.io/docs/)
- [Algorithmes de recommandation](https://en.wikipedia.org/wiki/Recommender_system)
- [A/B Testing](https://en.wikipedia.org/wiki/A/B_testing)
- [Métriques d'évaluation des recommandations](https://en.wikipedia.org/wiki/Evaluation_measures_(information_retrieval))

## Conclusion

Ce guide vous a fourni les informations nécessaires pour étendre et améliorer le système de recommandation de Retreat And Be. N'hésitez pas à consulter le code source et la documentation pour plus de détails.
