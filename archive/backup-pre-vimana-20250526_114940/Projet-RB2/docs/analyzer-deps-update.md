# Mise à jour des dépendances du microservice Analyzer

## Résumé des changements

L'analyse du fichier package.json du microservice Analyzer a révélé plusieurs dépendances obsolètes ou dépréciées, particulièrement dans le domaine de la télémétrie (OpenTelemetry). Cette mise à jour vise à :

1. Remplacer les dépendances OpenTelemetry dépréciées par leurs équivalents modernes
2. Harmoniser les versions avec les autres microservices
3. Supprimer les dépendances non utilisées ou redondantes
4. Mettre à jour les dépendances de développement

## Dépendances OpenTelemetry à mettre à jour

| Dépendance obsolète | Version actuelle | Remplacer par | Version recommandée | Commentaire |
|---------------------|------------------|---------------|---------------------|-------------|
| `@opentelemetry/exporter-jaeger` | 1.15.1 | `@opentelemetry/exporter-trace-otlp-http` | 0.49.0 | Jaeger exporter est déprécié |
| `@opentelemetry/exporter-trace-otlp-http` | 0.36.0 | `@opentelemetry/exporter-trace-otlp-http` | 0.49.0 | Mise à jour majeure |
| `@opentelemetry/auto-instrumentations-node` | 0.56.1 | `@opentelemetry/auto-instrumentations-node` | 0.44.0 | Mise à jour de compatibilité |
| `@opentelemetry/sdk-node` | 0.36.0 | `@opentelemetry/sdk-node` | 0.49.0 | Mise à jour majeure |
| `@opentelemetry/sdk-trace-base` | 1.0.0 | `@opentelemetry/sdk-trace-node` | 1.22.0 | Remplacer par le package recommandé |
| `@opentelemetry/instrumentation-express` | 0.33.1 | `@opentelemetry/instrumentation-express` | 0.39.0 | Mise à jour |
| `@opentelemetry/instrumentation-http` | 0.41.1 | `@opentelemetry/instrumentation-http` | 0.49.0 | Mise à jour |
| `@opentelemetry/instrumentation-mongodb` | 0.36.1 | `@opentelemetry/instrumentation-mongodb` | 0.40.0 | Mise à jour |
| `@opentelemetry/instrumentation-redis` | 0.35.1 | `@opentelemetry/instrumentation-redis` | 0.38.0 | Mise à jour |
| `@opentelemetry/resources` | 1.0.0 | `@opentelemetry/resources` | 1.19.0 | Mise à jour |
| `@opentelemetry/semantic-conventions` | 1.0.0 | `@opentelemetry/semantic-conventions` | 1.19.0 | Mise à jour |
| `@opentelemetry/api` | 1.4.1 | `@opentelemetry/api` | 1.7.0 | Mise à jour |

## Dépendances obsolètes à supprimer

| Dépendance | Raison | Alternative recommandée |
|------------|--------|-------------------------|
| `jaeger-client` | Déprécié, remplacé par OpenTelemetry | Utiliser uniquement OpenTelemetry |
| `opentracing` | API dépréciée | Utiliser l'API OpenTelemetry |
| `@types/react` v17 | Version obsolète et utilisée dans un backend | Supprimer ou mettre à jour |
| `@types/react-dom` v17 | Version obsolète et utilisée dans un backend | Supprimer ou mettre à jour |
| `react` v17 | Utilisé dans un microservice backend | Déplacer cette dépendance vers le frontend |
| `react-dom` v17 | Utilisé dans un microservice backend | Déplacer cette dépendance vers le frontend |

## Dépendances NestJS à mettre à jour

| Dépendance | Version actuelle | Version recommandée | Commentaire |
|------------|------------------|---------------------|-------------|
| `@nestjs/microservices` | 9.0.0 | 11.0.10 | Mise à jour majeure pour aligner avec les autres dépendances NestJS |
| `@nestjs/passport` | 9.0.0 | 11.0.10 | Mise à jour majeure pour aligner avec les autres dépendances NestJS |
| `@nestjs/config` | 4.0.0 | 4.0.0 | Conserver (compatible avec NestJS 11) |

## Dépendances à mettre à jour

| Dépendance | Version actuelle | Version recommandée | Commentaire |
|------------|------------------|---------------------|-------------|
| `@prisma/client` | 5.7.0 | 5.12.0 | Mise à jour avec corrections de bugs |
| `prisma` | 5.7.0 | 5.12.0 | Mettre à jour en parallèle avec @prisma/client |
| `express` | 4.18.2 | 4.19.1 | Corrections de sécurité |
| `helmet` | 7.2.0 | 7.2.0 | Conserver (stable) |
| `openai` | 4.0.0 | 4.33.0 | Nouvelles fonctionnalités et corrections de bugs |
| `prom-client` | 14.0.1 | 15.1.3 | Mise à jour majeure avec nouvelles fonctionnalités |
| `socket.io` | 4.7.2 | 4.7.5 | Corrections de bugs |
| `winston` | 3.10.0 | 3.11.0 | Corrections de bugs |

## Dépendances de développement à mettre à jour

| Dépendance | Version actuelle | Version recommandée | Commentaire |
|------------|------------------|---------------------|-------------|
| `@nestjs/core` | 11.0.10 | 11.0.10 | Conserver (stable) |
| `@nestjs/testing` | 11.0.10 | 11.0.10 | Conserver (stable) |
| `eslint` | 8.45.0 | 8.57.1 | Mises à jour de sécurité et nouvelles règles |
| `nodemon` | 3.0.1 | 3.1.0 | Corrections de bugs |
| `typescript` | 4.8.4 | 5.4.3 | Mise à jour majeure avec nouvelles fonctionnalités |
| `@babel/core` | 7.22.9 | 7.24.4 | Mise à jour avec corrections de bugs |
| `@babel/preset-env` | 7.22.9 | 7.24.4 | Mise à jour avec corrections de bugs |
| `@babel/plugin-transform-modules-commonjs` | 7.22.9 | 7.24.3 | Mise à jour avec corrections de bugs |

## Instructions de mise à jour spécifiques pour OpenTelemetry

La mise à jour des dépendances OpenTelemetry nécessitera des modifications du code :

1. Remplacer l'utilisation de l'exportateur Jaeger :

```typescript
// Ancien code
import { JaegerExporter } from '@opentelemetry/exporter-jaeger';
const exporter = new JaegerExporter({ endpoint: 'http://jaeger:14268/api/traces' });

// Nouveau code
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
const exporter = new OTLPTraceExporter({ 
  url: 'http://jaeger:4318/v1/traces'
});
```

2. Mettre à jour l'initialisation du SDK OpenTelemetry :

```typescript
// Ancien code
import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';

const sdk = new NodeSDK({
  traceExporter: exporter,
  instrumentations: [getNodeAutoInstrumentations()]
});

// Nouveau code
import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';

const sdk = new NodeSDK({
  traceExporter: exporter,
  instrumentations: [getNodeAutoInstrumentations()],
  resource: new Resource({
    [SemanticResourceAttributes.SERVICE_NAME]: 'analyzer-service'
  })
});
```

## Étapes de mise à jour

1. Créer une branche dédiée à la mise à jour des dépendances
2. Sauvegarder le fichier `package.json` actuel
3. Mettre à jour les versions dans le fichier `package.json`
4. Identifier et modifier le code qui utilise les dépendances mises à jour, surtout OpenTelemetry
5. Exécuter les tests pour vérifier que tout fonctionne correctement
6. Résoudre les éventuels problèmes identifiés

## Vérifications après mise à jour

1. Exécuter `npm audit` pour vérifier les vulnérabilités résiduelles
2. Vérifier que les traces sont correctement envoyées à Jaeger
3. Vérifier que les métriques sont correctement exportées vers Prometheus
4. Vérifier que les instrumentations automatiques fonctionnent correctement 