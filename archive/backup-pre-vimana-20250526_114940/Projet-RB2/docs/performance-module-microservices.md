# Intégration du module de performance avec les microservices

Ce guide explique comment intégrer le module de performance du Backend-NestJS avec d'autres microservices dans l'architecture Retreat And Be.

## Table des matières

1. [Architecture de l'intégration](#architecture-de-lintégration)
2. [Configuration des microservices](#configuration-des-microservices)
3. [Collecte de métriques](#collecte-de-métriques)
4. [Alertes et notifications](#alertes-et-notifications)
5. [Tableaux de bord](#tableaux-de-bord)
6. [Exemples d'intégration](#exemples-dintégration)
7. [Bonnes pratiques](#bonnes-pratiques)

## Architecture de l'intégration

Le module de performance peut être intégré avec d'autres microservices de deux manières :

1. **Mode centralisé** : Un seul service de performance collecte les métriques de tous les microservices
2. **Mode distribué** : Chaque microservice a son propre module de performance

### Mode centralisé

Dans le mode centralisé, un service dédié à la performance collecte les métriques de tous les microservices via :

- Des API REST
- Des messages via un bus d'événements (Kafka, RabbitMQ, etc.)
- Des agents de collecte de métriques (Prometheus, StatsD, etc.)

![Architecture centralisée](./images/performance-centralized.png)

### Mode distribué

Dans le mode distribué, chaque microservice a son propre module de performance qui collecte et stocke ses propres métriques. Un service d'agrégation peut ensuite collecter et agréger les métriques de tous les microservices.

![Architecture distribuée](./images/performance-distributed.png)

## Configuration des microservices

### Configuration du service de performance

Pour configurer le service de performance en mode centralisé, vous devez :

1. Déployer le service de performance en tant que microservice distinct
2. Configurer les points d'entrée pour la collecte de métriques
3. Configurer les connexions aux autres microservices

Exemple de configuration Docker Compose :

```yaml
version: '3.8'

services:
  performance-service:
    build:
      context: ./performance
      dockerfile: Dockerfile
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/performance
      - METRICS_INTERVAL=60
      - METRICS_RETENTION=30
      - MONITORING_ENABLED=true
      - ALERTS_ENABLED=true
      - NOTIFICATIONS_ENABLED=true
      - KAFKA_BROKERS=kafka:9092
      - KAFKA_GROUP_ID=performance-service
      - KAFKA_METRICS_TOPIC=metrics
      - KAFKA_ALERTS_TOPIC=alerts
    depends_on:
      - postgres
      - kafka
    networks:
      - retreatandbe-network
```

### Configuration des microservices clients

Pour configurer un microservice client pour envoyer des métriques au service de performance, vous devez :

1. Ajouter une bibliothèque cliente pour la collecte de métriques
2. Configurer la connexion au service de performance
3. Instrumenter le code pour collecter des métriques

Exemple de configuration pour un microservice NestJS :

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { PerformanceClientModule } from './performance-client/performance-client.module';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'PERFORMANCE_SERVICE',
        transport: Transport.KAFKA,
        options: {
          client: {
            clientId: 'your-microservice',
            brokers: ['kafka:9092'],
          },
          consumer: {
            groupId: 'your-microservice-consumer',
          },
        },
      },
    ]),
    PerformanceClientModule,
    // Autres modules...
  ],
})
export class AppModule {}
```

## Collecte de métriques

### Envoi de métriques via Kafka

Pour envoyer des métriques via Kafka, vous pouvez créer un service client :

```typescript
// src/performance-client/performance-client.service.ts
import { Injectable, Inject } from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';
import { MetricType } from './interfaces/metric.interface';

@Injectable()
export class PerformanceClientService {
  constructor(
    @Inject('PERFORMANCE_SERVICE') private readonly performanceClient: ClientKafka,
  ) {}

  async onModuleInit() {
    await this.performanceClient.connect();
  }

  async sendMetric(metric: {
    name: string;
    description: string;
    type: MetricType;
    value: number;
    unit: string;
    tags: Record<string, any>;
  }) {
    return this.performanceClient.emit('metrics', {
      ...metric,
      timestamp: new Date(),
      source: 'your-microservice',
    });
  }
}
```

### Envoi de métriques via HTTP

Pour envoyer des métriques via HTTP, vous pouvez créer un service client :

```typescript
// src/performance-client/performance-client.service.ts
import { Injectable, HttpService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MetricType } from './interfaces/metric.interface';

@Injectable()
export class PerformanceClientService {
  private readonly performanceServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.performanceServiceUrl = this.configService.get<string>('PERFORMANCE_SERVICE_URL');
  }

  async sendMetric(metric: {
    name: string;
    description: string;
    type: MetricType;
    value: number;
    unit: string;
    tags: Record<string, any>;
  }) {
    return this.httpService.post(`${this.performanceServiceUrl}/api/metrics`, {
      ...metric,
      timestamp: new Date(),
      source: 'your-microservice',
    }).toPromise();
  }
}
```

### Utilisation d'un agent de collecte de métriques

Pour utiliser un agent de collecte de métriques comme Prometheus, vous pouvez configurer un exportateur de métriques :

```typescript
// src/performance-client/prometheus.service.ts
import { Injectable } from '@nestjs/common';
import { Registry, Counter, Gauge, Histogram } from 'prom-client';

@Injectable()
export class PrometheusService {
  private readonly registry: Registry;
  private readonly counters: Map<string, Counter> = new Map();
  private readonly gauges: Map<string, Gauge> = new Map();
  private readonly histograms: Map<string, Histogram> = new Map();

  constructor() {
    this.registry = new Registry();
  }

  getRegistry(): Registry {
    return this.registry;
  }

  getCounter(name: string, help: string, labelNames: string[] = []): Counter {
    if (!this.counters.has(name)) {
      const counter = new Counter({
        name,
        help,
        labelNames,
        registers: [this.registry],
      });
      this.counters.set(name, counter);
    }
    return this.counters.get(name);
  }

  getGauge(name: string, help: string, labelNames: string[] = []): Gauge {
    if (!this.gauges.has(name)) {
      const gauge = new Gauge({
        name,
        help,
        labelNames,
        registers: [this.registry],
      });
      this.gauges.set(name, gauge);
    }
    return this.gauges.get(name);
  }

  getHistogram(
    name: string,
    help: string,
    labelNames: string[] = [],
    buckets: number[] = [0.1, 0.5, 1, 2, 5, 10],
  ): Histogram {
    if (!this.histograms.has(name)) {
      const histogram = new Histogram({
        name,
        help,
        labelNames,
        buckets,
        registers: [this.registry],
      });
      this.histograms.set(name, histogram);
    }
    return this.histograms.get(name);
  }
}
```

## Alertes et notifications

### Réception d'alertes via Kafka

Pour recevoir des alertes via Kafka, vous pouvez créer un contrôleur :

```typescript
// src/performance-client/performance-client.controller.ts
import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { AlertService } from './alert.service';

@Controller()
export class PerformanceClientController {
  constructor(private readonly alertService: AlertService) {}

  @MessagePattern('alerts')
  async handleAlert(@Payload() alert: any) {
    // Traiter l'alerte
    await this.alertService.processAlert(alert);
  }
}
```

### Envoi de notifications personnalisées

Pour envoyer des notifications personnalisées, vous pouvez créer un service :

```typescript
// src/performance-client/notification.service.ts
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/common';

@Injectable()
export class NotificationService {
  private readonly slackWebhookUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.slackWebhookUrl = this.configService.get<string>('SLACK_WEBHOOK_URL');
  }

  async sendSlackNotification(alert: any) {
    if (!this.slackWebhookUrl) {
      return;
    }

    const message = {
      text: `🚨 *Alert: ${alert.name}*`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `🚨 Alert: ${alert.name}`,
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Description:*\n${alert.description}`,
            },
            {
              type: 'mrkdwn',
              text: `*Severity:*\n${alert.severity}`,
            },
            {
              type: 'mrkdwn',
              text: `*Source:*\n${alert.source}`,
            },
            {
              type: 'mrkdwn',
              text: `*Timestamp:*\n${new Date(alert.timestamp).toLocaleString()}`,
            },
          ],
        },
      ],
    };

    await this.httpService.post(this.slackWebhookUrl, message).toPromise();
  }
}
```

## Tableaux de bord

### Intégration avec Grafana

Pour intégrer le module de performance avec Grafana, vous pouvez configurer Grafana pour utiliser la base de données PostgreSQL comme source de données :

```yaml
version: '3.8'

services:
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=grafana-postgresql-datasource
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - retreatandbe-network
```

Ensuite, vous pouvez configurer une source de données PostgreSQL dans Grafana et créer des tableaux de bord pour visualiser les métriques.

### Intégration avec l'interface utilisateur

Pour intégrer les tableaux de bord du module de performance avec l'interface utilisateur de votre application, vous pouvez créer une API pour récupérer les données des tableaux de bord :

```typescript
// src/performance-client/dashboard.controller.ts
import { Controller, Get, Param, Query } from '@nestjs/common';
import { DashboardService } from './dashboard.service';

@Controller('dashboards')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get()
  async getDashboards() {
    return this.dashboardService.getDashboards();
  }

  @Get(':id')
  async getDashboard(@Param('id') id: string) {
    return this.dashboardService.getDashboard(id);
  }

  @Get(':id/panels/:panelId/data')
  async getPanelData(
    @Param('id') id: string,
    @Param('panelId') panelId: string,
    @Query('from') from: string,
    @Query('to') to: string,
  ) {
    return this.dashboardService.getPanelData(id, panelId, from, to);
  }
}
```

## Exemples d'intégration

### Intégration avec le microservice d'authentification

```typescript
// src/auth/auth.service.ts
import { Injectable } from '@nestjs/common';
import { PerformanceClientService } from '../performance-client/performance-client.service';
import { MetricType } from '../performance-client/interfaces/metric.interface';

@Injectable()
export class AuthService {
  constructor(private readonly performanceClient: PerformanceClientService) {}

  async login(username: string, password: string) {
    try {
      // Logique d'authentification...

      // Envoyer une métrique de succès
      await this.performanceClient.sendMetric({
        name: 'auth.login.success',
        description: 'Successful login attempts',
        type: MetricType.COUNTER,
        value: 1,
        unit: 'count',
        tags: {
          username,
          environment: process.env.NODE_ENV || 'development',
        },
      });

      return { success: true };
    } catch (error) {
      // Envoyer une métrique d'échec
      await this.performanceClient.sendMetric({
        name: 'auth.login.failure',
        description: 'Failed login attempts',
        type: MetricType.COUNTER,
        value: 1,
        unit: 'count',
        tags: {
          username,
          reason: error.message,
          environment: process.env.NODE_ENV || 'development',
        },
      });

      throw error;
    }
  }
}
```

### Intégration avec le microservice de paiement

```typescript
// src/payment/payment.service.ts
import { Injectable } from '@nestjs/common';
import { PerformanceClientService } from '../performance-client/performance-client.service';
import { MetricType } from '../performance-client/interfaces/metric.interface';

@Injectable()
export class PaymentService {
  constructor(private readonly performanceClient: PerformanceClientService) {}

  async processPayment(userId: string, amount: number, currency: string) {
    const startTime = Date.now();

    try {
      // Logique de traitement du paiement...

      // Envoyer une métrique de succès
      await this.performanceClient.sendMetric({
        name: 'payment.process.success',
        description: 'Successful payment processing',
        type: MetricType.COUNTER,
        value: 1,
        unit: 'count',
        tags: {
          userId,
          currency,
          environment: process.env.NODE_ENV || 'development',
        },
      });

      // Envoyer une métrique de montant
      await this.performanceClient.sendMetric({
        name: 'payment.amount',
        description: 'Payment amount',
        type: MetricType.GAUGE,
        value: amount,
        unit: currency,
        tags: {
          userId,
          environment: process.env.NODE_ENV || 'development',
        },
      });

      // Envoyer une métrique de temps de traitement
      await this.performanceClient.sendMetric({
        name: 'payment.process.time',
        description: 'Payment processing time',
        type: MetricType.HISTOGRAM,
        value: Date.now() - startTime,
        unit: 'ms',
        tags: {
          userId,
          currency,
          environment: process.env.NODE_ENV || 'development',
        },
      });

      return { success: true };
    } catch (error) {
      // Envoyer une métrique d'échec
      await this.performanceClient.sendMetric({
        name: 'payment.process.failure',
        description: 'Failed payment processing',
        type: MetricType.COUNTER,
        value: 1,
        unit: 'count',
        tags: {
          userId,
          currency,
          reason: error.message,
          environment: process.env.NODE_ENV || 'development',
        },
      });

      throw error;
    }
  }
}
```

### Intégration avec le microservice de réservation

```typescript
// src/reservation/reservation.service.ts
import { Injectable } from '@nestjs/common';
import { PerformanceClientService } from '../performance-client/performance-client.service';
import { MetricType } from '../performance-client/interfaces/metric.interface';

@Injectable()
export class ReservationService {
  constructor(private readonly performanceClient: PerformanceClientService) {}

  async createReservation(userId: string, retreatId: string, startDate: Date, endDate: Date) {
    try {
      // Logique de création de réservation...

      // Envoyer une métrique de succès
      await this.performanceClient.sendMetric({
        name: 'reservation.create.success',
        description: 'Successful reservation creation',
        type: MetricType.COUNTER,
        value: 1,
        unit: 'count',
        tags: {
          userId,
          retreatId,
          environment: process.env.NODE_ENV || 'development',
        },
      });

      // Envoyer une métrique de durée de réservation
      const durationDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      await this.performanceClient.sendMetric({
        name: 'reservation.duration',
        description: 'Reservation duration',
        type: MetricType.GAUGE,
        value: durationDays,
        unit: 'days',
        tags: {
          userId,
          retreatId,
          environment: process.env.NODE_ENV || 'development',
        },
      });

      return { success: true };
    } catch (error) {
      // Envoyer une métrique d'échec
      await this.performanceClient.sendMetric({
        name: 'reservation.create.failure',
        description: 'Failed reservation creation',
        type: MetricType.COUNTER,
        value: 1,
        unit: 'count',
        tags: {
          userId,
          retreatId,
          reason: error.message,
          environment: process.env.NODE_ENV || 'development',
        },
      });

      throw error;
    }
  }
}
```

## Bonnes pratiques

### Nommage des métriques

Utilisez un schéma de nommage cohérent pour vos métriques :

- Utilisez des points (`.`) pour séparer les parties du nom
- Commencez par le nom de votre microservice
- Suivez avec la catégorie de la métrique
- Terminez par le nom spécifique de la métrique

Exemples :
- `auth.login.success`
- `payment.process.time`
- `reservation.create.failure`

### Tags de métriques

Utilisez des tags pour ajouter des dimensions à vos métriques :

- Ajoutez toujours un tag `environment` pour distinguer les environnements
- Ajoutez un tag `service` ou `microservice` pour identifier le service
- Ajoutez des tags spécifiques à votre métrique (ex: `userId`, `currency`, `retreatId`)

### Performance

Optimisez la collecte de métriques pour minimiser l'impact sur les performances :

- Utilisez des méthodes asynchrones pour envoyer des métriques
- Utilisez un buffer pour regrouper les métriques avant de les envoyer
- Utilisez un mécanisme de retry avec backoff pour gérer les erreurs
- Utilisez un circuit breaker pour éviter de surcharger le service de performance

### Sécurité

Sécurisez vos métriques et alertes :

- Ne stockez pas d'informations sensibles dans les métriques
- Utilisez des connexions sécurisées (HTTPS, SSL/TLS) pour envoyer des métriques
- Utilisez l'authentification pour sécuriser l'accès aux API de métriques
- Validez les entrées utilisateur avant de les inclure dans les métriques

### Résilience

Assurez-vous que votre système reste résilient même en cas de défaillance du service de performance :

- Utilisez des timeouts pour les appels au service de performance
- Utilisez un circuit breaker pour éviter les appels répétés en cas de défaillance
- Utilisez un mécanisme de fallback pour stocker les métriques localement en cas de défaillance
- Utilisez un mécanisme de retry pour réessayer d'envoyer les métriques plus tard

## Conclusion

L'intégration du module de performance du Backend-NestJS avec vos microservices vous permet de collecter des métriques, de générer des alertes et de visualiser les performances de votre application. En suivant les bonnes pratiques décrites dans ce guide, vous pouvez créer un système de monitoring robuste et efficace pour votre architecture de microservices.
