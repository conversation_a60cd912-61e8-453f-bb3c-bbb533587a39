# Guide Utilisateur : <PERSON>ache Optimisé et Recherche Performante

Ce guide vous aide à comprendre et utiliser efficacement les fonctionnalités de cache et de recherche optimisées de la plateforme Retreat And Be.

## Sommaire
1. [Introduction](#introduction)
2. [<PERSON><PERSON> Optimisé](#cache-optimisé)
   - [Niveaux de Cache](#niveaux-de-cache)
   - [Invalidation Intelligente](#invalidation-intelligente)
   - [Bonnes Pratiques](#bonnes-pratiques-cache)
3. [Recherche Optimisée](#recherche-optimisée)
   - [Fonctionnalités Principales](#fonctionnalités-principales)
   - [Filtres et Facettes](#filtres-et-facettes)
   - [Suggestions de Recherche](#suggestions-de-recherche)
4. [Exemples d'Utilisation](#exemples-dutilisation)
5. [Résolution de Problèmes](#résolution-de-problèmes)

## Introduction

Notre plateforme utilise un système de cache à plusieurs niveaux et une recherche optimisée pour offrir des performances exceptionnelles. Ces optimisations permettent :

- Des temps de réponse API inférieurs à 100ms
- Une meilleure expérience utilisateur
- Une réduction de la charge sur les bases de données et serveurs
- Une disponibilité accrue, même en cas de problèmes réseau

## Cache Optimisé

Le système de cache optimisé stocke temporairement les données fréquemment accédées pour réduire les temps de réponse et la charge sur les systèmes backend.

### Niveaux de Cache

Notre plateforme utilise un système de cache à plusieurs niveaux, chacun avec des durées de vie (TTL) différentes :

| Niveau | Durée | Utilisation |
|--------|-------|-------------|
| Micro | 10 secondes | Données très volatiles (suggestions, statuts) |
| Frequent | 5 minutes | Données qui changent fréquemment (résultats de recherche) |
| Standard | 1 heure | Données à durée de vie moyenne (contenus, événements) |
| Stable | 24 heures | Données relativement stables (configurations, options) |
| Reference | 7 jours | Données de référence qui changent rarement (taxonomies) |

### Invalidation Intelligente

Le cache utilise un système d'invalidation intelligent basé sur des tags :

- **Invalidation par clé** : Invalide une entrée de cache spécifique
- **Invalidation par tag** : Invalide toutes les entrées associées à un tag
- **Invalidation par motif** : Invalide les entrées correspondant à un motif

Exemple : Lorsqu'un événement est modifié, toutes les entrées de cache liées à cet événement sont automatiquement invalidées grâce aux tags.

### Bonnes Pratiques Cache

Pour tirer le meilleur parti du système de cache :

1. **Utilisez le niveau de cache approprié** à votre cas d'usage
2. **Ajoutez des tags pertinents** pour faciliter l'invalidation groupée
3. **Évitez de stocker des données sensibles** dans le cache
4. **Surveillez les statistiques de cache** pour optimiser l'utilisation

## Recherche Optimisée

Notre système de recherche utilise Elasticsearch et le cache optimisé pour offrir des résultats rapides et pertinents.

### Fonctionnalités Principales

- **Recherche full-text** avec support des opérateurs logiques (AND, OR, NOT)
- **Recherche multi-champs** pour une pertinence améliorée
- **Correction orthographique** et tolérance aux fautes avec fuzziness
- **Mise en cache intelligente** des résultats fréquents
- **Exécution parallèle** des requêtes (recherche, facettes, comptage)

### Filtres et Facettes

La recherche supporte des filtres avancés et des facettes pour affiner les résultats :

```json
{
  "index": "events",
  "query": "yoga retraite",
  "filters": {
    "categories": ["bien-être", "méditation"],
    "price": { "gte": 100, "lte": 500 },
    "date": { "gte": "2023-01-01" }
  },
  "facets": ["categories", "location", "duration"],
  "sort": { "date": "asc", "popularity": "desc" }
}
```

Les facettes sont automatiquement calculées et permettent de créer des filtres dynamiques dans l'interface utilisateur.

### Suggestions de Recherche

Le système offre des suggestions de recherche en temps réel pour améliorer l'expérience utilisateur :

- **Autocomplétion** basée sur les recherches précédentes
- **Suggestions contextuelles** basées sur les filtres actifs
- **Mise en cache micro** (10 secondes) pour des performances optimales

## Exemples d'Utilisation

### Exemple 1 : Recherche simple avec cache

```javascript
// Frontend (React)
const SearchComponent = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  
  const handleSearch = async () => {
    try {
      const response = await api.post('/api/search', {
        index: 'events',
        query,
        includeFacets: true,
        facets: ['categories', 'location'],
        includeCount: true
      });
      
      setResults(response.data.hits);
    } catch (error) {
      console.error('Search error:', error);
    }
  };
  
  return (
    <div>
      <input 
        type="text" 
        value={query} 
        onChange={(e) => setQuery(e.target.value)} 
      />
      <button onClick={handleSearch}>Rechercher</button>
      
      <div className="results">
        {results.map(result => (
          <div key={result.id} className="result-item">
            <h3>{result.title}</h3>
            <p>{result.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Exemple 2 : Suggestions de recherche

```javascript
// Frontend (React)
const SearchSuggestions = () => {
  const [prefix, setPrefix] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  
  useEffect(() => {
    if (prefix.length < 2) return;
    
    const getSuggestions = async () => {
      try {
        const response = await api.get('/api/search/suggest', {
          params: {
            index: 'events',
            field: 'title',
            prefix
          }
        });
        
        setSuggestions(response.data);
      } catch (error) {
        console.error('Suggestions error:', error);
      }
    };
    
    const timer = setTimeout(getSuggestions, 200);
    return () => clearTimeout(timer);
  }, [prefix]);
  
  return (
    <div>
      <input 
        type="text" 
        value={prefix} 
        onChange={(e) => setPrefix(e.target.value)} 
        placeholder="Commencez à taper..."
      />
      
      {suggestions.length > 0 && (
        <ul className="suggestions">
          {suggestions.map((suggestion, index) => (
            <li key={index} onClick={() => setPrefix(suggestion)}>
              {suggestion}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
```

## Résolution de Problèmes

### Problèmes Courants

| Problème | Cause Possible | Solution |
|----------|----------------|----------|
| Résultats obsolètes | Cache non invalidé | Utilisez l'endpoint /api/cache/invalidate avec la clé appropriée |
| Performances lentes | Cache mal configuré | Vérifiez les statistiques de cache et ajustez les niveaux |
| Résultats manquants | Problème d'indexation | Vérifiez que les documents sont correctement indexés |
| Erreurs 504 | Requêtes trop complexes | Simplifiez les filtres ou utilisez des requêtes paginées |

### Surveillance du Cache

Vous pouvez surveiller les performances du cache via l'endpoint `/api/cache/stats` qui retourne :

```json
{
  "hits": 15420,
  "misses": 3201,
  "hitRate": 0.83,
  "setOperations": 3201,
  "getOperations": 18621,
  "invalidations": 420,
  "averageLookupTime": 3.2
}
```

Ces statistiques vous aident à optimiser l'utilisation du cache pour votre application.

---

Pour plus d'informations, consultez la [Documentation API](../api/README.md) ou contactez notre support technique à <EMAIL>.

## À Propos des Performances

Les optimisations de cache et de recherche ont permis d'atteindre un temps de réponse API moyen de 95ms, soit une amélioration de 21% par rapport aux versions précédentes (120ms). Ces améliorations se traduisent par une expérience utilisateur plus fluide et réactive. 