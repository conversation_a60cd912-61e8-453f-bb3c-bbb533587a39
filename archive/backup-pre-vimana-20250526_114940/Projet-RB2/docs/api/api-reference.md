# API Reference Documentation

## Overview

This document provides detailed technical specifications for all API endpoints in the Retreat And Be platform.

## Authentication

### JWT Authentication

All authenticated endpoints require a JW<PERSON> token in the Authorization header:

```
Authorization: Bearer <token>
```

### Authentication Endpoints

#### Register User

```http
POST /api/v1/auth/register

Request:
{
  "email": "string",
  "password": "string",
  "name": "string"
}

Response:
{
  "token": "string",
  "user": {
    "id": "string",
    "email": "string",
    "name": "string"
  }
}
```

#### Login

```http
POST /api/v1/auth/login

Request:
{
  "email": "string",
  "password": "string"
}

Response:
{
  "token": "string",
  "user": {...}
}
```

## Core Endpoints

### Events

#### List Events

```http
GET /api/v1/events

Query Parameters:
- page (integer, default: 1)
- limit (integer, default: 20)
- category (string, optional)
- date (string, optional)

Response:
{
  "data": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "date": "string",
      "location": {...}
    }
  ],
  "pagination": {
    "total": "integer",
    "page": "integer",
    "pages": "integer"
  }
}
```

#### Create Event

```http
POST /api/v1/events

Request:
{
  "title": "string",
  "description": "string",
  "date": "string",
  "location": {...}
}

Response:
{
  "id": "string",
  "title": "string",
  ...
}
```

### Files

#### Upload File

```http
POST /api/v1/files/upload

Request: multipart/form-data
- file: File

Response:
{
  "id": "string",
  "url": "string",
  "ipfsHash": "string"
}
```

### Messages

#### List Conversations

```http
GET /api/v1/messages/conversations

Response:
{
  "data": [
    {
      "id": "string",
      "participants": [...],
      "lastMessage": {...}
    }
  ]
}
```

#### Send Message

```http
POST /api/v1/messages

Request:
{
  "conversationId": "string",
  "content": "string",
  "attachments": ["string"]
}

Response:
{
  "id": "string",
  "content": "string",
  "sender": {...},
  "timestamp": "string"
}
```

## Error Handling

The API uses standard HTTP status codes and returns errors in the following format:

```json
{
  "code": "ERROR_CODE",
  "message": "Human readable error message",
  "details": {} // Optional additional information
}
```

Common error codes:
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `429`: Too Many Requests
- `500`: Internal Server Error

## Rate Limiting

API requests are limited to:
- 100 requests per minute for authenticated users
- 20 requests per minute for unauthenticated users

Rate limit information is included in response headers:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1620000000
```

## Data Models

### User
```json
{
  "id": "string",
  "email": "string",
  "name": "string",
  "avatar": "string",
  "createdAt": "string"
}
```

### Event
```json
{
  "id": "string",
  "title": "string",
  "description": "string",
  "date": "string",
  "location": {
    "address": "string",
    "city": "string",
    "country": "string",
    "coordinates": {
      "lat": "number",
      "lng": "number"
    }
  },
  "organizer": "User",
  "participants": ["User"],
  "status": "string"
}
```

### Message
```json
{
  "id": "string",
  "conversationId": "string",
  "sender": "User",
  "content": "string",
  "attachments": [{
    "id": "string",
    "url": "string",
    "type": "string"
  }],
  "timestamp": "string",
  "readBy": ["string"]
}
```

## Webhooks

Webhooks can be configured to receive real-time updates:

```http
POST /api/v1/webhooks

Request:
{
  "url": "string",
  "events": ["string"],
  "secret": "string"
}
```

Webhook payloads are signed using HMAC-SHA256 with your webhook secret. Verify the signature in the `X-Signature` header.