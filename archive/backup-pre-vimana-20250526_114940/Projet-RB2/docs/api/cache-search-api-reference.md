# API Reference: Cache et Recherche Optimisée

Ce document décrit les endpoints API liés au système de cache optimisé et aux fonctionnalités de recherche avancée.

## Base URL

```
https://api.retreatandbe.com/v1
```

## Authentification

Tous les endpoints nécessitent une authentification sauf indication contraire. Utilisez un token JWT dans l'en-tête `Authorization` :

```
Authorization: Bearer {token}
```

---

## Endpoints Cache

### Obtenir les Statistiques du Cache

Récupère les statistiques d'utilisation du cache.

```
GET /cache/stats
```

#### Paramètres de Requête

| Paramètre | Type | Requis | Description |
|-----------|------|--------|-------------|
| `period` | string | Non | Période d'analyse. Valeurs : `last-hour`, `last-day`, `last-week`. Défaut : `last-hour` |
| `format` | string | Non | Format de réponse. Valeurs : `simple`, `detailed`. Défaut : `simple` |

#### Réponse

```json
{
  "status": "success",
  "data": {
    "hits": 15420,
    "misses": 3201,
    "hitRate": 0.83,
    "setOperations": 3201,
    "getOperations": 18621,
    "invalidations": 420,
    "averageLookupTime": 3.2,
    "memoryUsage": {
      "used": "325MB",
      "available": "1GB"
    },
    "redisUsage": {
      "used": "2.1GB",
      "available": "8GB"
    }
  }
}
```

### Invalider des Entrées du Cache

Invalide une ou plusieurs entrées du cache.

```
POST /cache/invalidate
```

#### Corps de la Requête

```json
{
  "key": "user:profile:123",
  "pattern": false,
  "tags": [],
  "localOnly": false,
  "waitForCompletion": true
}
```

| Champ | Type | Requis | Description |
|-------|------|--------|-------------|
| `key` | string/array | Oui | Clé(s) à invalider. Peut être une clé unique ou un tableau |
| `pattern` | boolean | Non | Si true, `key` est traitée comme un pattern regex. Défaut : `false` |
| `tags` | array | Non | Tags à invalider. Toutes les entrées ayant ces tags seront invalidées |
| `localOnly` | boolean | Non | Si true, n'invalide que le cache local. Défaut : `false` |
| `waitForCompletion` | boolean | Non | Attendre confirmation d'invalidation. Défaut : `true` |

#### Réponse

```json
{
  "status": "success",
  "data": {
    "invalidated": 12,
    "affected": ["user:profile:123", "user:preferences:123"]
  }
}
```

### Précharger le Cache

Précharge des données dans le cache pour réduire la latence.

```
POST /cache/prefetch
```

#### Corps de la Requête

```json
{
  "keys": [
    { 
      "key": "featured-events:homepage",
      "source": "events",
      "params": { "type": "featured", "limit": 10 }
    },
    {
      "key": "popular-categories",
      "source": "categories",
      "params": { "orderBy": "popularity", "limit": 20 }
    }
  ],
  "options": {
    "tier": "STANDARD",
    "parallel": true
  }
}
```

| Champ | Type | Requis | Description |
|-------|------|--------|-------------|
| `keys` | array | Oui | Tableau de définitions de clés à précharger |
| `keys[].key` | string | Oui | Clé de cache à précharger |
| `keys[].source` | string | Oui | Source de données (mapper vers un service) |
| `keys[].params` | object | Non | Paramètres pour la récupération des données |
| `options.tier` | string | Non | Niveau de cache. Défaut : `STANDARD` |
| `options.parallel` | boolean | Non | Précharger en parallèle. Défaut : `true` |

#### Réponse

```json
{
  "status": "success",
  "data": {
    "prefetched": 2,
    "failed": 0,
    "timing": {
      "total": 235,
      "averagePerKey": 117.5
    }
  }
}
```

---

## Endpoints Recherche

### Recherche Générale

Endpoint principal pour la recherche de contenu.

```
POST /search
```

#### Corps de la Requête

```json
{
  "index": "events",
  "query": "yoga retraite montagne",
  "filters": {
    "categories": ["bien-être", "méditation"],
    "price": { "gte": 100, "lte": 500 },
    "date": { "gte": "2023-01-01" }
  },
  "facets": ["categories", "location", "duration"],
  "sort": [
    { "date": "asc" },
    { "popularity": "desc" }
  ],
  "from": 0,
  "size": 20,
  "includeTotal": true,
  "includeFacets": true,
  "cacheOptions": {
    "tier": "FREQUENT",
    "bypassCache": false
  }
}
```

| Champ | Type | Requis | Description |
|-------|------|--------|-------------|
| `index` | string | Oui | Index de recherche (ex: events, users, content) |
| `query` | string | Non | Terme de recherche textuelle |
| `filters` | object | Non | Filtres pour affiner les résultats |
| `facets` | array | Non | Facettes à calculer pour filtrage dynamique |
| `sort` | array/object | Non | Ordre de tri des résultats |
| `from` | number | Non | Offset pour pagination. Défaut : 0 |
| `size` | number | Non | Nombre de résultats. Défaut : 20, Max : 100 |
| `includeTotal` | boolean | Non | Inclure le nombre total de résultats. Défaut : true |
| `includeFacets` | boolean | Non | Inclure les facettes calculées. Défaut : true |
| `cacheOptions` | object | Non | Options de cache spécifiques à cette recherche |

#### Réponse

```json
{
  "status": "success",
  "data": {
    "hits": [
      {
        "id": "evt-123",
        "title": "Retraite Yoga dans les Alpes",
        "description": "Profitez d'une semaine de détente...",
        "categories": ["bien-être", "méditation", "yoga"],
        "price": 350,
        "date": "2023-06-15",
        "location": "Chamonix",
        "duration": 7,
        "highlights": {
          "title": ["Retraite <em>Yoga</em> dans les <em>Alpes</em>"]
        },
        "_score": 8.76
      },
      // ... autres résultats
    ],
    "total": 142,
    "facets": {
      "categories": [
        { "value": "yoga", "count": 78 },
        { "value": "méditation", "count": 45 },
        { "value": "bien-être", "count": 124 }
      ],
      "location": [
        { "value": "Alpes", "count": 28 },
        { "value": "Provence", "count": 19 }
      ],
      "duration": [
        { "value": "weekend", "count": 56 },
        { "value": "semaine", "count": 72 },
        { "value": "long-séjour", "count": 14 }
      ]
    },
    "meta": {
      "from": 0,
      "size": 20,
      "took": 45,
      "cached": true,
      "expiresAt": "2023-01-01T12:30:45Z"
    }
  }
}
```

### Suggestions de Recherche

Fournit des suggestions de termes de recherche en fonction d'un préfixe.

```
GET /search/suggest
```

#### Paramètres de Requête

| Paramètre | Type | Requis | Description |
|-----------|------|--------|-------------|
| `index` | string | Oui | Index de recherche (ex: events, users, content) |
| `field` | string | Oui | Champ sur lequel faire la suggestion (ex: title, name) |
| `prefix` | string | Oui | Préfixe de recherche (min 2 caractères) |
| `limit` | number | Non | Nombre de suggestions. Défaut : 5, Max : 20 |
| `fuzzy` | boolean | Non | Activer correspondance approximative. Défaut : true |
| `contexts` | string | Non | Contextes JSON encodés pour filtrer les suggestions |

#### Réponse

```json
{
  "status": "success",
  "data": {
    "suggestions": [
      "yoga et méditation",
      "yoga pour débutants",
      "yoga montagne",
      "yoga thérapeutique",
      "yoga nidra"
    ],
    "meta": {
      "took": 12,
      "cached": true
    }
  }
}
```

### Recherche Avancée

Endpoint pour recherches complexes avec options avancées.

```
POST /search/advanced
```

#### Corps de la Requête

```json
{
  "index": "events",
  "query": {
    "bool": {
      "must": [
        { "match": { "title": "yoga" } },
        { "match": { "description": "méditation" } }
      ],
      "filter": [
        { "range": { "price": { "lte": 500 } } },
        { "term": { "verified": true } }
      ],
      "should": [
        { "match": { "categories": "pleine conscience" } }
      ],
      "must_not": [
        { "term": { "canceled": true } }
      ]
    }
  },
  "aggregations": {
    "price_ranges": {
      "range": {
        "field": "price",
        "ranges": [
          { "to": 100 },
          { "from": 100, "to": 300 },
          { "from": 300 }
        ]
      }
    },
    "avg_duration": {
      "avg": { "field": "duration" }
    }
  },
  "highlight": {
    "fields": {
      "title": {},
      "description": { "fragment_size": 150, "number_of_fragments": 3 }
    }
  },
  "from": 0,
  "size": 20,
  "cacheOptions": {
    "tier": "MICRO",
    "ttl": 60
  }
}
```

| Champ | Type | Requis | Description |
|-------|------|--------|-------------|
| `index` | string | Oui | Index de recherche |
| `query` | object | Oui | Requête Elasticsearch complète |
| `aggregations` | object | Non | Agrégations Elasticsearch |
| `highlight` | object | Non | Configuration de mise en évidence |
| `from` | number | Non | Offset pour pagination. Défaut : 0 |
| `size` | number | Non | Nombre de résultats. Défaut : 20, Max : 100 |
| `cacheOptions` | object | Non | Options de cache spécifiques |

#### Réponse

```json
{
  "status": "success",
  "data": {
    "hits": [
      {
        "id": "evt-123",
        "title": "Retraite Yoga dans les Alpes",
        "description": "Profitez d'une semaine de détente...",
        "price": 350,
        "verified": true,
        "highlight": {
          "title": ["Retraite <em>Yoga</em> dans les Alpes"],
          "description": ["Profitez d'une semaine de <em>détente</em>..."]
        },
        "_score": 8.76
      },
      // ... autres résultats
    ],
    "aggregations": {
      "price_ranges": {
        "buckets": [
          { "key": "*-100.0", "to": 100, "doc_count": 25 },
          { "key": "100.0-300.0", "from": 100, "to": 300, "doc_count": 45 },
          { "key": "300.0-*", "from": 300, "doc_count": 72 }
        ]
      },
      "avg_duration": {
        "value": 5.4
      }
    },
    "total": 142,
    "meta": {
      "from": 0,
      "size": 20,
      "took": 78,
      "cached": false,
      "expiresAt": "2023-01-01T12:01:45Z"
    }
  }
}
```

### Enregistrement de Terme de Recherche

Enregistre un terme de recherche pour améliorer les suggestions.

```
POST /search/record
```

#### Corps de la Requête

```json
{
  "term": "yoga retraite montagne",
  "index": "events",
  "user": "usr-123",
  "resultCount": 12,
  "selectedResult": "evt-456",
  "context": {
    "location": "mobile",
    "filters": { "categories": ["bien-être"] }
  }
}
```

| Champ | Type | Requis | Description |
|-------|------|--------|-------------|
| `term` | string | Oui | Terme de recherche à enregistrer |
| `index` | string | Oui | Index de recherche |
| `user` | string | Non | ID utilisateur (si connecté) |
| `resultCount` | number | Non | Nombre de résultats retournés |
| `selectedResult` | string | Non | ID du résultat sélectionné |
| `context` | object | Non | Contexte de recherche |

#### Réponse

```json
{
  "status": "success",
  "data": {
    "recorded": true,
    "similarTerms": ["yoga montagne", "retraite alpes"]
  }
}
```

---

## Codes d'Erreur

| Code | Description |
|------|-------------|
| 400 | Requête invalide (paramètres manquants ou invalides) |
| 401 | Non authentifié |
| 403 | Non autorisé (permissions insuffisantes) |
| 404 | Ressource non trouvée |
| 429 | Trop de requêtes (rate limiting) |
| 500 | Erreur serveur interne |
| 503 | Service indisponible (maintenance) |

## Erreurs Spécifiques

| Code | Type | Description |
|------|------|-------------|
| 4001 | `INVALID_QUERY` | Requête de recherche invalide |
| 4002 | `MISSING_INDEX` | Index non spécifié ou introuvable |
| 4003 | `INVALID_FILTER` | Filtre de recherche invalide |
| 4004 | `CACHE_UNAVAILABLE` | Service de cache temporairement indisponible |
| 4005 | `SEARCH_TIMEOUT` | Délai d'attente dépassé pour la recherche |

## Exemple d'Erreur

```json
{
  "status": "error",
  "error": {
    "code": 4001,
    "type": "INVALID_QUERY",
    "message": "La requête de recherche contient un opérateur non supporté",
    "details": {
      "operator": "NEAR",
      "position": 12
    }
  }
}
```

---

## Limites de Rate

Les limites suivantes s'appliquent aux endpoints :

| Endpoint | Limite | Période |
|----------|--------|---------|
| `/search` | 30 | Minute par IP |
| `/search` | 300 | Heure par utilisateur |
| `/search/suggest` | 60 | Minute par IP |
| `/search/advanced` | 15 | Minute par IP |
| `/cache/invalidate` | 10 | Minute par utilisateur |

---

## Versions

| Version | Date | Changements |
|---------|------|-------------|
| v1.0.0 | 2023-01-15 | Version initiale |
| v1.1.0 | 2023-03-20 | Ajout des options de cache |
| v1.2.0 | 2023-05-10 | Ajout des facettes dynamiques |
| v1.3.0 | 2023-07-01 | Ajout de la recherche avancée |
| v1.3.1 | 2023-08-15 | Ajout des statistiques de cache |

---

Pour toute question sur l'API, <NAME_EMAIL> 