# Documentation API Retreat And Be

Cette documentation détaille l'API REST de Retreat And Be, une plateforme décentralisée de bien-être.

## Vue d'ensemble

L'API est organisée autour des principes REST. Elle accepte des requêtes JSON encodées en UTF-8 et renvoie des réponses JSON encodées en UTF-8. Elle utilise des codes de statut HTTP standards et l'authentification Bearer Token.

## Base URL

- Production: `https://api.retreatandbe.com/v1`
- Staging: `https://staging-api.retreatandbe.com/v1`
- Development: `http://localhost:3000/v1`

## Authentification

L'API utilise JWT (JSON Web Tokens) pour l'authentification. Pour les endpoints authentifiés, incluez le token dans le header Authorization :

```
Authorization: Bearer <token>
```

## Endpoints principaux

### Authentification
- `POST /auth/register` - Inscription d'un nouvel utilisateur
- `POST /auth/login` - Connexion utilisateur

### Événements
- `GET /events` - Liste des événements
- `POST /events` - Création d'un nouvel événement

### Fichiers
- `POST /files/upload` - Upload de fichier sur IPFS

### Marketplace
- `GET /marketplace/products` - Liste des produits

### Messagerie
- `GET /messages` - Liste des conversations
- `POST /messages` - Envoi d'un message

### Paiements
- `POST /payments/create-intent` - Création d'une intention de paiement

## Modèles de données

Les principaux modèles de données sont :

- User (Utilisateur)
- Event (Événement)
- Product (Produit)
- Conversation
- Message

Pour une documentation détaillée de tous les endpoints et modèles, consultez le fichier OpenAPI (`openapi.yaml`).

## Pagination

Les endpoints qui retournent des listes supportent la pagination avec les paramètres :
- `page` (défaut: 1)
- `limit` (défaut: varie selon l'endpoint)

## Gestion des erreurs

L'API utilise des codes de statut HTTP standards et retourne des erreurs au format :

```json
{
  "code": "ERROR_CODE",
  "message": "Description de l'erreur"
}
```

## Sécurité

- Toutes les requêtes doivent être faites en HTTPS (sauf en développement)
- Les tokens JWT expirent après 24h
- Rate limiting appliqué sur tous les endpoints
- Validation des données entrantes

## Support

Pour toute question ou problème :
- Email: <EMAIL>
- Documentation: https://docs.retreatandbe.com
