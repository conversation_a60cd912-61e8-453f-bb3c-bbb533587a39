# API de Notifications Push

Cette documentation décrit les endpoints disponibles pour la gestion des notifications push.

## Configuration requise

Avant d'utiliser l'API, assurez-vous d'avoir :

1. Les clés VAPID configurées dans les variables d'environnement :
   ```env
   VAPID_PUBLIC_KEY=votre_cle_publique
   VAPID_PRIVATE_KEY=votre_cle_privee
   VAPID_SUBJECT=mailto:<EMAIL>
   ```

2. Un token d'authentification valide (JWT)

## Endpoints

### Enregistrer une souscription push

```http
POST /api/push/subscribe
Authorization: Bearer <token>
Content-Type: application/json

{
  "endpoint": "https://fcm.googleapis.com/fcm/send/...",
  "keys": {
    "p256dh": "base64-encoded-key",
    "auth": "base64-encoded-auth"
  }
}
```

#### Réponse réussie

```json
{
  "message": "Push subscription saved successfully"
}
```

### Supprimer une souscription push

```http
POST /api/push/unsubscribe
Authorization: Bearer <token>
Content-Type: application/json

{
  "endpoint": "https://fcm.googleapis.com/fcm/send/..."
}
```

#### Réponse réussie

```json
{
  "message": "Push subscription deleted successfully"
}
```

### Obtenir les souscriptions de l'utilisateur

```http
GET /api/push/subscriptions
Authorization: Bearer <token>
```

#### Réponse réussie

```json
[
  {
    "endpoint": "https://fcm.googleapis.com/fcm/send/...",
    "p256dh": "base64-encoded-key",
    "auth": "base64-encoded-auth",
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
]
```

### Vérifier le statut des souscriptions

```http
GET /api/push/status
Authorization: Bearer <token>
```

#### Réponse réussie

```json
{
  "hasSubscriptions": true
}
```

## Format des notifications

Les notifications push envoyées suivent ce format :

```json
{
  "title": "Titre de la notification",
  "body": "Corps de la notification",
  "icon": "/chemin/vers/icone.png",
  "badge": "/chemin/vers/badge.png",
  "data": {
    "url": "/chemin/vers/page",
    "additionalData": "..."
  }
}
```

## Gestion des erreurs

L'API retourne les codes d'erreur HTTP standards :

- `400` : Requête invalide
- `401` : Non authentifié
- `403` : Non autorisé
- `404` : Ressource non trouvée
- `500` : Erreur serveur

Exemple de réponse d'erreur :

```json
{
  "error": "Description de l'erreur"
}
```

## Bonnes pratiques

1. **Gestion des souscriptions**
   - Stockez le endpoint de manière sécurisée
   - Gérez les souscriptions expirées
   - Limitez le nombre de souscriptions par utilisateur

2. **Envoi de notifications**
   - Évitez d'envoyer trop de notifications
   - Respectez les préférences de l'utilisateur
   - Incluez des actions pertinentes

3. **Sécurité**
   - Utilisez HTTPS
   - Validez les tokens JWT
   - Protégez les clés VAPID

## Exemples d'utilisation

### JavaScript (Frontend)

```javascript
// Demander la permission et s'abonner
async function subscribeToPush() {
  try {
    const registration = await navigator.serviceWorker.ready;
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(VAPID_PUBLIC_KEY)
    });

    // Envoyer la souscription au serveur
    await fetch('/api/push/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(subscription)
    });
  } catch (error) {
    console.error('Error subscribing to push:', error);
  }
}

// Se désabonner
async function unsubscribeFromPush() {
  try {
    const registration = await navigator.serviceWorker.ready;
    const subscription = await registration.pushManager.getSubscription();
    
    if (subscription) {
      await subscription.unsubscribe();
      await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ endpoint: subscription.endpoint })
      });
    }
  } catch (error) {
    console.error('Error unsubscribing from push:', error);
  }
}
```

### Node.js (Backend)

```typescript
import { PushNotificationService } from '../services/PushNotificationService';

const pushService = new PushNotificationService();

// Envoyer une notification
await pushService.sendNotification(userId, {
  title: 'Nouveau message',
  body: 'Vous avez reçu un nouveau message',
  icon: '/icons/message.png',
  data: {
    url: '/messages',
    messageId: '123'
  }
});
