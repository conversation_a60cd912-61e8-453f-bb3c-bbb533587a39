# Stratégie de Backup et Recovery - Retreat And Be

Ce document décrit la stratégie complète de sauvegarde et de restauration pour la plateforme Retreat And Be, couvrant les politiques de rétention, les procédures, et les plans de reprise après sinistre.

## Table des matières

1. [Objectifs](#1-objectifs)
2. [Composants critiques](#2-composants-critiques)
3. [Types de backups](#3-types-de-backups)
4. [Politique de rétention](#4-politique-de-rétention)
5. [Procédures de sauvegarde](#5-procédures-de-sauvegarde)
6. [Procédures de restauration](#6-procédures-de-restauration)
7. [Plan de reprise après sinistre (DRP)](#7-plan-de-reprise-après-sinistre-drp)
8. [Tests et validation](#8-tests-et-validation)
9. [Responsabilités](#9-responsabilités)

## 1. Objectifs

Notre stratégie de backup et recovery vise à atteindre les objectifs suivants :

- **Recovery Time Objective (RTO)** : < 4 heures
  - Temps maximum acceptable pour restaurer les systèmes après un incident
- **Recovery Point Objective (RPO)** : < 1 heure
  - Perte de données maximale acceptable en cas d'incident
- **Disponibilité des données** : 99.99%
  - Pourcentage de temps pendant lequel les données sont disponibles
- **Intégrité des données** : 100%
  - Garantie que les données restaurées sont cohérentes et non corrompues

## 2. Composants critiques

Les composants suivants sont considérés comme critiques et nécessitent une sauvegarde régulière :

| Composant | Description | Criticité | Fréquence de backup |
|-----------|-------------|-----------|---------------------|
| Base de données PostgreSQL | Données utilisateurs, transactions, contenu | Critique | Toutes les heures + WAL continu |
| Configurations Kubernetes | Déploiements, services, configmaps, secrets | Haute | Quotidienne |
| Fichiers médias | Images, vidéos, documents | Moyenne | Quotidienne |
| Logs applicatifs | Journaux des activités système | Basse | Conservation 12 mois |
| Smart contracts | Contrats déployés sur la blockchain | Critique | À chaque déploiement |

## 3. Types de backups

### 3.1 Base de données

- **Sauvegardes complètes** : Dump complet de la base de données
  - Quotidienne (01h00)
  - Conservation : 14 jours
- **Sauvegardes incrémentielles** : WAL (Write-Ahead Log)
  - Continue (archivage toutes les 15 minutes)
  - Conservation : 72 heures
- **Sauvegardes hebdomadaires**
  - Chaque dimanche (02h00)
  - Conservation : 8 semaines
- **Sauvegardes mensuelles**
  - Premier jour du mois (03h00)
  - Conservation : 24 mois

### 3.2 Configurations

- **Sauvegardes des ressources Kubernetes**
  - Quotidienne (04h00)
  - Inclut : deployments, services, ingress, configmaps, secrets
  - Conservation : 14 jours
- **Sauvegardes des configurations critiques**
  - À chaque modification
  - Stockées dans un dépôt Git avec historique complet

### 3.3 Fichiers médias

- **Inventaire des objets S3**
  - Quotidien (05h00)
  - Conservation : 14 jours
- **Synchronisation vers un bucket de sauvegarde**
  - Quotidienne (06h00)
  - Avec versioning activé
  - Conservation : versions précédentes pendant 90 jours

## 4. Politique de rétention

| Type de backup | Rétention courte | Rétention intermédiaire | Archivage |
|----------------|-------------------|------------------------|-----------|
| DB - Quotidien | 14 jours | - | - |
| DB - Hebdomadaire | - | 8 semaines | - |
| DB - Mensuel | - | - | 24 mois |
| WAL | 72 heures | - | - |
| Configurations | 14 jours | 8 semaines | - |
| Fichiers médias | Versions 90 jours | - | Indéfini pour les médias critiques |
| Logs système | 30 jours | - | 12 mois (compressés) |
| Logs transactionnels | 90 jours | - | 7 ans (conformité légale) |

### 4.1 Gestion du cycle de vie

- Les backups quotidiens sont automatiquement supprimés après 14 jours
- Les backups hebdomadaires sont déplacés vers un stockage à coût réduit après 8 semaines
- Les backups mensuels sont archivés dans un stockage glacial après 24 mois
- Les logs transactionnels sont archivés pour conformité légale (7 ans)

## 5. Procédures de sauvegarde

### 5.1 Automatisation

Les sauvegardes sont entièrement automatisées via les scripts suivants :

- `scripts/backup/automated_backup.sh` : Backup automatisé quotidien
- `scripts/backup/db_backup.sh` : Sauvegarde spécifique de la base de données
- `scripts/backup/k8s_backup.sh` : Sauvegarde des configurations Kubernetes
- `scripts/backup/media_inventory.sh` : Inventaire des fichiers médias

### 5.2 Exécution des backups

#### 5.2.1 Base de données

```bash
# Backup quotidien complet avec compression
PGPASSWORD="$DB_PASSWORD" pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -F c -f $BACKUP_FILE
gzip $BACKUP_FILE

# Point-in-time recovery avec archivage des WAL
postgresql.conf:
  archive_mode = on
  archive_command = 'aws s3 cp %p s3://retreatnbe-backups/wal/%f'
  archive_timeout = 900  # 15 minutes
```

#### 5.2.2 Configurations Kubernetes

```bash
# Sauvegarde des ressources Kubernetes
kubectl get all -n retreatnbe-production -o yaml > k8s_resources_$TIMESTAMP.yaml
kubectl get configmaps -n retreatnbe-production -o yaml > k8s_configmaps_$TIMESTAMP.yaml
kubectl get secrets -n retreatnbe-production -o yaml > k8s_secrets_$TIMESTAMP.yaml

# Compression et chiffrement
tar -czf configs_$TIMESTAMP.tar.gz k8s_*.yaml
gpg --encrypt --recipient <EMAIL> configs_$TIMESTAMP.tar.gz
```

#### 5.2.3 Fichiers médias

```bash
# Inventaire S3
aws s3 ls s3://retreatnbe-media --recursive > media_inventory_$TIMESTAMP.txt

# Synchronisation vers bucket de sauvegarde
aws s3 sync s3://retreatnbe-media s3://retreatnbe-backups/media --delete
```

### 5.3 Monitoring des backups

- **Alertes en cas d'échec** : Notification Slack et e-mail en cas d'échec d'un backup
- **Vérification quotidienne** : Test d'intégrité des backups
- **Rapport hebdomadaire** : Résumé de l'état des backups et de l'espace utilisé
- **Métrique dashboard** : Affichage de l'état des backups dans Grafana

## 6. Procédures de restauration

### 6.1 Restauration de la base de données

#### 6.1.1 Restauration complète

```bash
# Restauration d'un dump complet
gunzip -c $BACKUP_FILE.gz > $BACKUP_FILE
PGPASSWORD="$DB_PASSWORD" pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -v $BACKUP_FILE
```

#### 6.1.2 Point-in-time recovery (PITR)

```bash
# Restauration à un point précis dans le temps
# 1. Restaurer le dernier backup complet avant le point cible
# 2. Créer un fichier recovery.conf
recovery.conf:
  restore_command = 'aws s3 cp s3://retreatnbe-backups/wal/%f %p'
  recovery_target_time = '2023-10-15 14:30:00'
  recovery_target_action = 'promote'
```

### 6.2 Restauration des configurations

```bash
# Déchiffrement et décompression
gpg --decrypt configs_$TIMESTAMP.tar.gz.gpg > configs_$TIMESTAMP.tar.gz
tar -xzf configs_$TIMESTAMP.tar.gz

# Application des configurations
kubectl apply -f k8s_resources_$TIMESTAMP.yaml
kubectl apply -f k8s_configmaps_$TIMESTAMP.yaml
kubectl apply -f k8s_secrets_$TIMESTAMP.yaml
```

### 6.3 Restauration des fichiers médias

```bash
# Restauration depuis le bucket de sauvegarde
aws s3 sync s3://retreatnbe-backups/media s3://retreatnbe-media
```

### 6.4 Script de restauration automatisé

Un script de restauration automatisé est disponible pour faciliter la restauration :

```bash
./scripts/backup/restore_backup.sh --type all --latest
./scripts/backup/restore_backup.sh --type db --backup-type weekly --date 20231015
./scripts/backup/restore_backup.sh --type db --point-in-time "2023-10-15 14:30:00"
```

## 7. Plan de reprise après sinistre (DRP)

### 7.1 Scénarios couverts

| Scénario | Description | RTO | RPO | Procédure |
|----------|-------------|-----|-----|-----------|
| Corruption de données | Données corrompues dans la base | 1h | 15min | PITR |
| Panne majeure d'infrastructure | Perte d'un datacenter | 4h | 1h | Failover vers région secondaire |
| Attaque de sécurité | Intrusion, ransomware | 4h | 1h | Isolation, restauration en environnement propre |
| Erreur humaine | Suppression accidentelle de données | 1h | 15min | PITR |
| Défaillance matérielle | Panne d'un nœud Kubernetes | 10min | 0 | Auto-healing Kubernetes |

### 7.2 Procédure de failover vers la région secondaire

1. **Activation de la région secondaire**
   - Vérifier la réplication de la base de données
   - Promouvoir le replica en primaire
   - Mettre à jour les entrées DNS

2. **Redirection du trafic**
   - Basculer le trafic via les Global Load Balancers
   - Vérifier les connexions actives

3. **Vérification post-failover**
   - Exécuter des tests fonctionnels
   - Vérifier l'intégrité des données
   - Confirmer les métriques d'application

### 7.3 Communication en cas d'incident

- **Équipe interne** : Notification via Slack et PagerDuty
- **Utilisateurs** : Notification via page de statut et e-mail
- **Partenaires** : Communication directe par l'équipe de support

## 8. Tests et validation

### 8.1 Tests réguliers

| Type de test | Fréquence | Description |
|--------------|-----------|-------------|
| Restauration DB | Hebdomadaire | Restauration dans un environnement de test |
| Intégrité des backups | Quotidienne | Vérification automatisée |
| Failover | Mensuel | Test de basculement vers la région secondaire |
| DRP complet | Trimestriel | Simulation d'un désastre majeur |

### 8.2 Validation des backups

Chaque backup est automatiquement validé après création :

```bash
# Validation d'un backup de base de données
pg_restore --list $BACKUP_FILE > /dev/null || echo "Backup corrompu"

# Test de restauration dans un environnement isolé
docker run --rm -e POSTGRES_PASSWORD=test postgres:16
PGPASSWORD=test pg_restore -h localhost -U postgres -d postgres -v $BACKUP_FILE
```

## 9. Responsabilités

| Rôle | Responsabilité |
|------|----------------|
| DevOps | Configuration et monitoring des backups |
| DBA | Validation des backups de base de données |
| SRE | Test des procédures de restauration |
| RSSI | Validation de la sécurité des backups |
| CTO | Approbation du plan de DRP |

### 9.1 Matrice RACI

| Activité | DevOps | DBA | SRE | RSSI | CTO |
|----------|--------|-----|-----|------|-----|
| Configuration des backups | R | C | I | C | I |
| Exécution des backups | R | I | I | - | - |
| Vérification des backups | R | A | C | I | - |
| Test de restauration | C | R | A | I | I |
| Déclenchement DRP | C | C | R | C | A |
| Revue de la stratégie | C | C | C | C | R/A |

R: Responsible, A: Accountable, C: Consulted, I: Informed

---

Document préparé par l'équipe DevOps de Retreat And Be.  
Dernière mise à jour : Octobre 2023 