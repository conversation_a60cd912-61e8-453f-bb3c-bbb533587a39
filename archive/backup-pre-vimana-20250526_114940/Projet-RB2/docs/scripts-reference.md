# Guide de référence des scripts RB2

Ce document fournit une référence complète de tous les scripts utilitaires disponibles pour gérer la plateforme RB2 déployée sur Kubernetes.

## Scripts d'administration et de maintenance

| Script | Description | Utilisation |
|--------|-------------|-------------|
| `scripts/deployment-summary.sh` | Génère un résumé complet de l'état du déploiement | `./scripts/deployment-summary.sh` |
| `scripts/backup-restore-cluster.sh` | Sauvegarde ou restaure l'état du cluster | `./scripts/backup-restore-cluster.sh --backup` <br> `./scripts/backup-restore-cluster.sh --restore --file <fichier>` |
| `scripts/fix-all-errors.sh` | Corrige de manière globale les erreurs connues | `./scripts/fix-all-errors.sh` |
| `scripts/fix-all-images.sh` | Corrige les problèmes d'image pour tous les pods | `./scripts/fix-all-images.sh` |
| `scripts/fix-analyzer.sh` | Corrige les problèmes spécifiques au service Analyzer | `./scripts/fix-analyzer.sh` |
| `scripts/fix-associative-array.sh` | Corrige les erreurs d'array associatif dans les scripts | `./scripts/fix-associative-array.sh` |
| `scripts/fix-mongodb-pods.sh` | Analyse et corrige les problèmes des pods MongoDB | `./scripts/fix-mongodb-pods.sh` |

## Scripts de déploiement et d'installation

| Script | Description | Utilisation |
|--------|-------------|-------------|
| `scripts/install-istio-complete.sh` | Installe Istio avec tous ses CRDs | `./scripts/install-istio-complete.sh` |
| `scripts/activate-service.sh` | Active un service spécifique (1 réplica) | `./scripts/activate-service.sh <nom-du-service>` |

## Scripts de monitoring

| Script | Description | Utilisation |
|--------|-------------|-------------|
| `scripts/check-grafana-dashboards.sh` | Vérifie et liste tous les dashboards Grafana disponibles | `./scripts/check-grafana-dashboards.sh` |
| `scripts/import-grafana-dashboard.sh` | Importe un dashboard dans Grafana | `./scripts/import-grafana-dashboard.sh` |

## Ordre d'utilisation recommandé

Pour un nouveau déploiement ou une récupération après problème, nous recommandons d'utiliser les scripts dans l'ordre suivant :

1. `fix-all-errors.sh` - Pour corriger les problèmes connus
2. `install-istio-complete.sh` - Pour assurer que l'infrastructure Istio est correctement installée
3. `deployment-summary.sh` - Pour vérifier l'état général du déploiement
4. `activate-service.sh` - Pour activer les services un par un selon vos besoins
5. `check-grafana-dashboards.sh` - Pour configurer le monitoring
6. `fix-mongodb-pods.sh` - Pour résoudre les problèmes persistants avec MongoDB

## Exemples de flux de travail courants

### Déploiement complet
```bash
./scripts/fix-all-errors.sh
./scripts/install-istio-complete.sh
./scripts/deployment-summary.sh
./scripts/activate-service.sh website-creator
./scripts/activate-service.sh flight-finder-flight-finder
./scripts/check-grafana-dashboards.sh
```

### Sauvegarde et restauration
```bash
# Sauvegarde
./scripts/backup-restore-cluster.sh --backup

# Restauration
./scripts/backup-restore-cluster.sh --restore --file kubernetes/backups/full_backup_retreat-and-be_20250315_120000.tar.gz
```

### Résolution de problèmes
```bash
# Vérifier l'état du déploiement
./scripts/deployment-summary.sh

# Résoudre les problèmes de MongoDB
./scripts/fix-mongodb-pods.sh

# Vérifier à nouveau après correction
kubectl get pods -n retreat-and-be
```

## Gestion des dashboards Grafana

Les dashboards Grafana suivants sont configurés dans le système :

1. **Kubernetes Pods Monitoring** - Surveillance générale des pods
2. **Service Monitoring Dashboard** - Surveillance détaillée des services

Pour accéder à Grafana :
- URL: http://grafana.rb2.example.com
- Identifiants: admin / admin123

## Références additionnelles

Pour plus d'informations sur les prochaines étapes de développement et d'amélioration, consultez les documents suivants :

- [Guide des prochaines étapes](./next-steps-guide.md) - Actions à entreprendre après le déploiement initial
- [Guide de déploiement](./deployment-guide.md) - Documentation détaillée du processus de déploiement 