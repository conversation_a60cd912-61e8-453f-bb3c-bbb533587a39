# Guide d'utilisation du chiffrement homomorphique

Ce document explique comment utiliser le service de chiffrement homomorphique dans l'application Retreat And Be.

## Qu'est-ce que le chiffrement homomorphique ?

Le chiffrement homomorphique est une forme de chiffrement qui permet d'effectuer des calculs sur des données chiffrées sans les déchiffrer au préalable. Cela permet de préserver la confidentialité des données tout en permettant leur traitement.

## Implémentation

Notre implémentation utilise Microsoft SEAL via la bibliothèque node-seal pour fournir des fonctionnalités de chiffrement homomorphique. Elle supporte deux schémas de chiffrement :

- **BFV** : Adapté aux opérations sur des entiers
- **CKKS** : Adapté aux opérations sur des nombres à virgule flottante (approximatifs)

## Configuration

Le service de chiffrement homomorphique peut être configuré via des variables d'environnement :

```env
# Activer/désactiver le chiffrement homomorphique
HOMOMORPHIC_ENCRYPTION_ENABLED=true

# Schéma de chiffrement (BFV ou CKKS)
HOMOMORPHIC_ENCRYPTION_SCHEME=BFV

# Niveau de sécurité (128 ou 256)
HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL=128

# Degré du polynôme modulaire (puissance de 2, généralement 4096 ou 8192)
HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE=4096

# Module de texte en clair (pour BFV, doit être un nombre premier)
HOMOMORPHIC_ENCRYPTION_PLAINTEXT_MODULUS=1024

# Bits des coefficients du module (pour CKKS)
HOMOMORPHIC_ENCRYPTION_COEFF_MODULUS_BITS=[60, 40, 40, 60]
```

## Utilisation de base

### Importer le service

```typescript
import { HomomorphicEncryptionService } from '@modules/security/services/homomorphic-encryption.service';
```

### Vérifier si le chiffrement homomorphique est activé

```typescript
if (homomorphicService.isEnabled()) {
  // Le chiffrement homomorphique est activé et initialisé
}
```

### Chiffrer une valeur

```typescript
// Chiffrer une valeur numérique
const encryptedValue = await homomorphicService.encrypt(42);
```

### Déchiffrer une valeur

```typescript
// Déchiffrer une valeur chiffrée
const decryptedValue = await homomorphicService.decrypt(encryptedValue);
```

## Opérations homomorphiques

### Addition

```typescript
// Chiffrer deux valeurs
const encrypted1 = await homomorphicService.encrypt(15);
const encrypted2 = await homomorphicService.encrypt(27);

// Effectuer une addition homomorphique
const encryptedSum = await homomorphicService.add(encrypted1, encrypted2);

// Déchiffrer le résultat
const sum = await homomorphicService.decrypt(encryptedSum);
// sum ≈ 42
```

### Multiplication

```typescript
// Chiffrer deux valeurs
const encrypted1 = await homomorphicService.encrypt(6);
const encrypted2 = await homomorphicService.encrypt(7);

// Effectuer une multiplication homomorphique
const encryptedProduct = await homomorphicService.multiply(encrypted1, encrypted2);

// Déchiffrer le résultat
const product = await homomorphicService.decrypt(encryptedProduct);
// product ≈ 42
```

### Moyenne

```typescript
// Chiffrer plusieurs valeurs
const encryptedValues = await Promise.all([10, 20, 30, 40, 50].map(v =>
  homomorphicService.encrypt(v)
));

// Calculer la moyenne homomorphique
const encryptedAverage = await homomorphicService.average(encryptedValues);

// Déchiffrer le résultat
const average = await homomorphicService.decrypt(encryptedAverage);
// average ≈ 30
```

## Gestion des clés

### Générer une paire de clés

```typescript
// Générer une nouvelle paire de clés
const keyPair = await homomorphicService.generateKeyPair();

// Utiliser les clés
console.log(keyPair.publicKey);  // Clé publique en base64
console.log(keyPair.privateKey); // Clé privée en base64
```

## Limites et considérations

1. **Performance** : Le chiffrement homomorphique est computationnellement intensif. Les opérations peuvent être lentes, surtout avec des paramètres de sécurité élevés.

2. **Taille des données** : Les textes chiffrés sont beaucoup plus grands que les données d'origine.

3. **Précision** : Avec le schéma CKKS, les résultats sont approximatifs en raison de la nature du chiffrement.

4. **Profondeur de circuit** : Le nombre d'opérations consécutives est limité, surtout pour les multiplications.

5. **Complexité** : Les opérations plus complexes (division, racine carrée, etc.) nécessitent des algorithmes spéciaux.

## Exemples d'utilisation

Consultez le fichier d'exemple `src/modules/security/examples/homomorphic-encryption-example.ts` pour des exemples complets d'utilisation du service.

## Cas d'utilisation

Le chiffrement homomorphique est particulièrement utile dans les scénarios suivants :

1. **Analyse de données confidentielles** : Effectuer des analyses statistiques sur des données sensibles sans les exposer.

2. **Calculs multi-parties** : Permettre à plusieurs parties de contribuer à un calcul sans révéler leurs données individuelles.

3. **Apprentissage automatique préservant la confidentialité** : Entraîner des modèles sur des données chiffrées.

4. **Stockage cloud sécurisé** : Stocker des données dans le cloud tout en permettant leur traitement sans les déchiffrer.

## Ressources supplémentaires

- [Microsoft SEAL](https://github.com/microsoft/SEAL)
- [node-seal](https://github.com/morfix-io/node-seal)
- [Introduction au chiffrement homomorphique](https://www.microsoft.com/en-us/research/project/homomorphic-encryption/)

## Mise à jour du document

Ce guide est régulièrement mis à jour pour refléter les changements dans l'implémentation du chiffrement homomorphique.

Dernière mise à jour : 18 avril 2025