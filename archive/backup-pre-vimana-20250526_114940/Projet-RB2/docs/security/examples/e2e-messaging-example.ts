/**
 * Exemple d'utilisation du E2EEncryptionService et du SecureMessagingService
 * 
 * Ce fichier montre comment utiliser les services de chiffrement de bout en bout
 * pour sécuriser les messages entre utilisateurs.
 */

import { E2EEncryptionService } from '@messaging/security';
import { SecureMessagingService } from '@messaging/services';

async function e2eMessagingExample() {
  console.log('=== Exemple de messagerie chiffrée de bout en bout ===');

  // Obtenir les instances des services
  const encryptionService = E2EEncryptionService.getInstance();
  const messagingService = SecureMessagingService.getInstance();
  
  // 1. Initialisation des utilisateurs
  console.log('\n1. Initialisation des utilisateurs');
  
  // Initialiser Alice
  const aliceKeys = encryptionService.initializeUserKeys('alice');
  const alicePublicKeys = encryptionService.getUserPublicKeys('alice');
  
  console.log(`Clés d'identité d'Alice :`);
  console.log(`  - Publique : ${aliceKeys.identityKeyPair.publicKey.substring(0, 16)}...`);
  console.log(`  - Privée : ${aliceKeys.identityKeyPair.privateKey.substring(0, 16)}...`);
  
  // Initialiser Bob
  const bobKeys = encryptionService.initializeUserKeys('bob');
  const bobPublicKeys = encryptionService.getUserPublicKeys('bob');
  
  console.log(`Clés d'identité de Bob :`);
  console.log(`  - Publique : ${bobKeys.identityKeyPair.publicKey.substring(0, 16)}...`);
  console.log(`  - Privée : ${bobKeys.identityKeyPair.privateKey.substring(0, 16)}...`);
  
  // 2. Création d'une session directe avec le E2EEncryptionService
  console.log('\n2. Création d\'une session directe avec le E2EEncryptionService');
  
  // Alice crée une session avec Bob
  const sessionId = encryptionService.createSession(
    'alice',
    'bob',
    {
      identityKey: bobPublicKeys!.identityKey,
      signedPreKey: bobPublicKeys!.signedPreKey,
      oneTimePreKey: bobPublicKeys!.oneTimePreKeys[0]
    }
  );
  
  console.log(`Session créée avec l'ID : ${sessionId}`);
  
  // 3. Chiffrement et déchiffrement de messages avec le E2EEncryptionService
  console.log('\n3. Chiffrement et déchiffrement de messages avec le E2EEncryptionService');
  
  // Alice chiffre un message pour Bob
  const message = 'Bonjour Bob, comment vas-tu ?';
  const encryptedMessage = await encryptionService.encryptMessage(sessionId, message);
  
  console.log(`Message chiffré :`);
  console.log(`  - En-tête : ${JSON.stringify(encryptedMessage.header)}`);
  console.log(`  - Texte chiffré : ${encryptedMessage.ciphertext.substring(0, 32)}...`);
  console.log(`  - IV : ${encryptedMessage.iv}`);
  console.log(`  - Tag d'authentification : ${encryptedMessage.authTag}`);
  console.log(`  - HMAC : ${encryptedMessage.hmac.substring(0, 32)}...`);
  
  // Bob déchiffre le message d'Alice
  const decryptedMessage = await encryptionService.decryptMessage(sessionId, encryptedMessage);
  
  console.log(`Message déchiffré : ${decryptedMessage}`);
  
  // 4. Chiffrement et déchiffrement de plusieurs messages
  console.log('\n4. Chiffrement et déchiffrement de plusieurs messages');
  
  const messages = [
    'Comment se passe ta journée ?',
    'J\'ai une question à te poser.',
    'Pouvons-nous nous rencontrer demain ?'
  ];
  
  for (const msg of messages) {
    // Alice chiffre un message
    const encrypted = await encryptionService.encryptMessage(sessionId, msg);
    
    // Bob déchiffre le message
    const decrypted = await encryptionService.decryptMessage(sessionId, encrypted);
    
    console.log(`Message original : ${msg}`);
    console.log(`Message déchiffré : ${decrypted}`);
    console.log(`Compteur d'envoi : ${encrypted.header.counter}`);
    console.log('---');
  }
  
  // 5. Utilisation du SecureMessagingService
  console.log('\n5. Utilisation du SecureMessagingService');
  
  // Initialiser les utilisateurs avec le service de messagerie
  const aliceMessagingKeys = messagingService.initializeUser('alice');
  const bobMessagingKeys = messagingService.initializeUser('bob');
  
  console.log(`Clés publiques d'Alice (via messagingService) :`);
  console.log(`  - Identité : ${aliceMessagingKeys.identityKey.substring(0, 16)}...`);
  console.log(`  - Pré-signée : ${aliceMessagingKeys.signedPreKey.substring(0, 16)}...`);
  
  // Établir une session sécurisée
  const messagingSessionId = messagingService.establishSession(
    'alice',
    'bob',
    {
      identityKey: bobMessagingKeys.identityKey,
      signedPreKey: bobMessagingKeys.signedPreKey,
      oneTimePreKey: bobMessagingKeys.oneTimePreKeys[0]
    }
  );
  
  console.log(`Session de messagerie établie avec l'ID : ${messagingSessionId}`);
  
  // 6. Envoi et réception de messages avec le SecureMessagingService
  console.log('\n6. Envoi et réception de messages avec le SecureMessagingService');
  
  // Alice envoie un message à Bob
  const sentMessage = await messagingService.sendMessage(
    'alice',
    'bob',
    'Bonjour Bob, c\'est Alice. Ce message est chiffré de bout en bout.',
    'text'
  );
  
  console.log(`Message envoyé :`);
  console.log(`  - ID : ${sentMessage.id}`);
  console.log(`  - Expéditeur : ${sentMessage.senderId}`);
  console.log(`  - Destinataire : ${sentMessage.recipientId}`);
  console.log(`  - Contenu : ${sentMessage.content}`);
  console.log(`  - Type : ${sentMessage.type}`);
  console.log(`  - Horodatage : ${sentMessage.timestamp}`);
  
  // Bob reçoit le message d'Alice
  const receivedMessage = await messagingService.receiveMessage('bob', sentMessage.id);
  
  console.log(`Message reçu :`);
  console.log(`  - ID : ${receivedMessage?.id}`);
  console.log(`  - Expéditeur : ${receivedMessage?.senderId}`);
  console.log(`  - Destinataire : ${receivedMessage?.recipientId}`);
  console.log(`  - Contenu : ${receivedMessage?.content}`);
  console.log(`  - Type : ${receivedMessage?.type}`);
  console.log(`  - Horodatage : ${receivedMessage?.timestamp}`);
  
  // 7. Création d'une conversation
  console.log('\n7. Création d\'une conversation');
  
  const conversation = messagingService.createConversation(['alice', 'bob']);
  
  console.log(`Conversation créée :`);
  console.log(`  - ID : ${conversation.id}`);
  console.log(`  - Participants : ${conversation.participants.join(', ')}`);
  console.log(`  - Créée le : ${conversation.createdAt}`);
  
  // 8. Envoi de plusieurs messages dans une conversation
  console.log('\n8. Envoi de plusieurs messages dans une conversation');
  
  const conversationMessages = [
    'Bonjour Bob, comment vas-tu ?',
    'Je travaille sur le projet Retreat And Be.',
    'Pouvons-nous discuter des fonctionnalités de sécurité ?'
  ];
  
  for (const msg of conversationMessages) {
    // Alice envoie un message à Bob
    const sentMsg = await messagingService.sendMessage(
      'alice',
      'bob',
      msg,
      'text'
    );
    
    console.log(`Message envoyé : ${sentMsg.content}`);
  }
  
  // 9. Récupération des messages d'une conversation
  console.log('\n9. Récupération des messages d\'une conversation');
  
  const conversationMsgs = await messagingService.getConversationMessages('bob', conversation.id);
  
  console.log(`Messages de la conversation (${conversationMsgs.length}) :`);
  for (const msg of conversationMsgs) {
    console.log(`  - De ${msg.senderId} à ${msg.recipientId} : ${msg.content}`);
  }
  
  // 10. Récupération des conversations d'un utilisateur
  console.log('\n10. Récupération des conversations d\'un utilisateur');
  
  const aliceConversations = messagingService.getConversations('alice');
  
  console.log(`Conversations d'Alice (${aliceConversations.length}) :`);
  for (const conv of aliceConversations) {
    console.log(`  - ID : ${conv.id}`);
    console.log(`  - Participants : ${conv.participants.join(', ')}`);
    console.log(`  - Dernier message : ${conv.lastMessage ? conv.lastMessage.content : 'Aucun'}`);
  }
}

// Exécuter l'exemple
e2eMessagingExample().catch(error => {
  console.error('Erreur :', error);
});

/**
 * Sortie attendue :
 * 
 * === Exemple de messagerie chiffrée de bout en bout ===
 * 
 * 1. Initialisation des utilisateurs
 * Clés d'identité d'Alice :
 *   - Publique : dGhpcyBpcyBhIHNpbX...
 *   - Privée : dGhpcyBpcyBhIHNpbX...
 * Clés d'identité de Bob :
 *   - Publique : YW5vdGhlciBzaW11bG...
 *   - Privée : YW5vdGhlciBzaW11bG...
 * 
 * 2. Création d'une session directe avec le E2EEncryptionService
 * Session créée avec l'ID : alice:bob:1622548800000
 * 
 * 3. Chiffrement et déchiffrement de messages avec le E2EEncryptionService
 * Message chiffré :
 *   - En-tête : {"ratchetKey":"YW5vdGhlciBzaW11bG...","counter":0,"previousCounter":0}
 *   - Texte chiffré : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 *   - IV : a1b2c3d4e5f6
 *   - Tag d'authentification : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
 *   - HMAC : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 * Message déchiffré : Bonjour Bob, comment vas-tu ?
 * 
 * 4. Chiffrement et déchiffrement de plusieurs messages
 * Message original : Comment se passe ta journée ?
 * Message déchiffré : Comment se passe ta journée ?
 * Compteur d'envoi : 1
 * ---
 * Message original : J'ai une question à te poser.
 * Message déchiffré : J'ai une question à te poser.
 * Compteur d'envoi : 2
 * ---
 * Message original : Pouvons-nous nous rencontrer demain ?
 * Message déchiffré : Pouvons-nous nous rencontrer demain ?
 * Compteur d'envoi : 3
 * ---
 * 
 * 5. Utilisation du SecureMessagingService
 * Clés publiques d'Alice (via messagingService) :
 *   - Identité : dGhpcyBpcyBhIHNpbX...
 *   - Pré-signée : YW5vdGhlciBzaW11bG...
 * Session de messagerie établie avec l'ID : alice:bob:1622548800001
 * 
 * 6. Envoi et réception de messages avec le SecureMessagingService
 * Message envoyé :
 *   - ID : msg_1622548800000_a1b2c3
 *   - Expéditeur : alice
 *   - Destinataire : bob
 *   - Contenu : Bonjour Bob, c'est Alice. Ce message est chiffré de bout en bout.
 *   - Type : text
 *   - Horodatage : 2023-06-01T12:00:00.000Z
 * Message reçu :
 *   - ID : msg_1622548800000_a1b2c3
 *   - Expéditeur : alice
 *   - Destinataire : bob
 *   - Contenu : Bonjour Bob, c'est Alice. Ce message est chiffré de bout en bout.
 *   - Type : text
 *   - Horodatage : 2023-06-01T12:00:00.000Z
 * 
 * 7. Création d'une conversation
 * Conversation créée :
 *   - ID : conv_1622548800000_a1b2c3
 *   - Participants : alice, bob
 *   - Créée le : 2023-06-01T12:00:00.000Z
 * 
 * 8. Envoi de plusieurs messages dans une conversation
 * Message envoyé : Bonjour Bob, comment vas-tu ?
 * Message envoyé : Je travaille sur le projet Retreat And Be.
 * Message envoyé : Pouvons-nous discuter des fonctionnalités de sécurité ?
 * 
 * 9. Récupération des messages d'une conversation
 * Messages de la conversation (4) :
 *   - De alice à bob : Bonjour Bob, c'est Alice. Ce message est chiffré de bout en bout.
 *   - De alice à bob : Bonjour Bob, comment vas-tu ?
 *   - De alice à bob : Je travaille sur le projet Retreat And Be.
 *   - De alice à bob : Pouvons-nous discuter des fonctionnalités de sécurité ?
 * 
 * 10. Récupération des conversations d'un utilisateur
 * Conversations d'Alice (1) :
 *   - ID : conv_1622548800000_a1b2c3
 *   - Participants : alice, bob
 *   - Dernier message : Pouvons-nous discuter des fonctionnalités de sécurité ?
 */
