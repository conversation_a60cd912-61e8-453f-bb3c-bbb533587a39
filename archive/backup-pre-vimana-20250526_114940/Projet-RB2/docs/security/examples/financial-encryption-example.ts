/**
 * Exemple d'utilisation du FinancialEncryptionService
 * 
 * Ce fichier montre comment utiliser le service de chiffrement financier pour
 * protéger les données financières sensibles comme les numéros de carte de crédit,
 * les informations bancaires, etc.
 */

import { FinancialEncryptionService } from '@security/financial-encryption';

async function financialEncryptionExample() {
  console.log('=== Exemple de chiffrement financier ===');

  // Obtenir une instance du service
  const financialEncryptionService = new FinancialEncryptionService();
  
  // 1. Validation de numéros de carte de crédit
  console.log('\n1. Validation de numéros de carte de crédit');
  const validCard = '****************';  // Visa (valide selon l'algorithme de Luhn)
  const invalidCard = '****************';  // Invalide
  
  console.log(`Carte ${validCard} valide : ${financialEncryptionService.validateCardNumber(validCard)}`);
  console.log(`Carte ${invalidCard} valide : ${financialEncryptionService.validateCardNumber(invalidCard)}`);
  
  // 2. Masquage de numéros de carte de crédit
  console.log('\n2. Masquage de numéros de carte de crédit');
  const maskedCard = financialEncryptionService.maskCardNumber(validCard);
  console.log(`Carte masquée : ${maskedCard}`);
  
  // 3. Chiffrement d'un numéro de carte de crédit
  console.log('\n3. Chiffrement d\'un numéro de carte de crédit');
  const encryptedCard = await financialEncryptionService.encryptCardNumber(validCard);
  console.log(`Carte chiffrée : ${encryptedCard}`);
  
  // 4. Déchiffrement d'un numéro de carte de crédit
  console.log('\n4. Déchiffrement d\'un numéro de carte de crédit');
  const decryptedCard = await financialEncryptionService.decryptCardNumber(encryptedCard);
  console.log(`Carte déchiffrée : ${decryptedCard}`);
  
  // 5. Validation d'un numéro IBAN
  console.log('\n5. Validation d\'un numéro IBAN');
  const validIban = '***************************';  // IBAN français valide
  const invalidIban = '***************************';  // IBAN invalide
  
  console.log(`IBAN ${validIban} valide : ${financialEncryptionService.validateIban(validIban)}`);
  console.log(`IBAN ${invalidIban} valide : ${financialEncryptionService.validateIban(invalidIban)}`);
  
  // 6. Chiffrement d'un numéro IBAN
  console.log('\n6. Chiffrement d\'un numéro IBAN');
  const encryptedIban = await financialEncryptionService.encryptIban(validIban);
  console.log(`IBAN chiffré : ${encryptedIban}`);
  
  // 7. Déchiffrement d'un numéro IBAN
  console.log('\n7. Déchiffrement d\'un numéro IBAN');
  const decryptedIban = await financialEncryptionService.decryptIban(encryptedIban);
  console.log(`IBAN déchiffré : ${decryptedIban}`);
  
  // 8. Chiffrement d'un objet contenant des données financières
  console.log('\n8. Chiffrement d\'un objet contenant des données financières');
  const paymentInfo = {
    cardNumber: validCard,
    expiryDate: '12/25',
    cvv: '123',
    cardholderName: 'John Doe',
    billingAddress: {
      street: '123 Main St',
      city: 'Anytown',
      zipCode: '12345',
      country: 'US'
    }
  };
  
  const encryptedPaymentInfo = await financialEncryptionService.encryptObject(
    paymentInfo,
    ['cardNumber', 'cvv']  // Champs à chiffrer
  );
  
  console.log(`Objet chiffré : ${JSON.stringify(encryptedPaymentInfo, null, 2)}`);
  
  // 9. Déchiffrement d'un objet contenant des données financières
  console.log('\n9. Déchiffrement d\'un objet contenant des données financières');
  const decryptedPaymentInfo = await financialEncryptionService.decryptObject(
    encryptedPaymentInfo,
    ['cardNumber', 'cvv']  // Champs à déchiffrer
  );
  
  console.log(`Objet déchiffré : ${JSON.stringify(decryptedPaymentInfo, null, 2)}`);
  
  // 10. Chiffrement d'une transaction financière
  console.log('\n10. Chiffrement d\'une transaction financière');
  const transaction = {
    id: 'txn_123456789',
    amount: 100.00,
    currency: 'EUR',
    date: new Date().toISOString(),
    paymentMethod: {
      type: 'credit_card',
      cardNumber: validCard,
      expiryDate: '12/25',
      cvv: '123'
    },
    customer: {
      id: 'cust_123456789',
      name: 'John Doe',
      email: '<EMAIL>'
    }
  };
  
  const encryptedTransaction = await financialEncryptionService.encryptTransaction(transaction);
  console.log(`Transaction chiffrée : ${JSON.stringify(encryptedTransaction, null, 2)}`);
}

// Exécuter l'exemple
financialEncryptionExample().catch(error => {
  console.error('Erreur :', error);
});

/**
 * Sortie attendue :
 * 
 * === Exemple de chiffrement financier ===
 * 
 * 1. Validation de numéros de carte de crédit
 * Carte **************** valide : true
 * Carte **************** valide : false
 * 
 * 2. Masquage de numéros de carte de crédit
 * Carte masquée : ************1111
 * 
 * 3. Chiffrement d'un numéro de carte de crédit
 * Carte chiffrée : eyJpdiI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJkYXRhIjoiYWJjZGVmMTIzNDU2Nzg5MCIsInRhZyI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJrZXlJZCI6ImtleS0xMjM0NTYifQ==
 * 
 * 4. Déchiffrement d'un numéro de carte de crédit
 * Carte déchiffrée : ****************
 * 
 * 5. Validation d'un numéro IBAN
 * IBAN *************************** valide : true
 * IBAN *************************** valide : false
 * 
 * 6. Chiffrement d'un numéro IBAN
 * IBAN chiffré : eyJpdiI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJkYXRhIjoiYWJjZGVmMTIzNDU2Nzg5MCIsInRhZyI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJrZXlJZCI6ImtleS0xMjM0NTYifQ==
 * 
 * 7. Déchiffrement d'un numéro IBAN
 * IBAN déchiffré : ***************************
 * 
 * 8. Chiffrement d'un objet contenant des données financières
 * Objet chiffré : {
 *   "cardNumber": "eyJpdiI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJkYXRhIjoiYWJjZGVmMTIzNDU2Nzg5MCIsInRhZyI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJrZXlJZCI6ImtleS0xMjM0NTYifQ==",
 *   "expiryDate": "12/25",
 *   "cvv": "eyJpdiI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJkYXRhIjoiYWJjZGVmMTIzNDU2Nzg5MCIsInRhZyI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJrZXlJZCI6ImtleS0xMjM0NTYifQ==",
 *   "cardholderName": "John Doe",
 *   "billingAddress": {
 *     "street": "123 Main St",
 *     "city": "Anytown",
 *     "zipCode": "12345",
 *     "country": "US"
 *   }
 * }
 * 
 * 9. Déchiffrement d'un objet contenant des données financières
 * Objet déchiffré : {
 *   "cardNumber": "****************",
 *   "expiryDate": "12/25",
 *   "cvv": "123",
 *   "cardholderName": "John Doe",
 *   "billingAddress": {
 *     "street": "123 Main St",
 *     "city": "Anytown",
 *     "zipCode": "12345",
 *     "country": "US"
 *   }
 * }
 * 
 * 10. Chiffrement d'une transaction financière
 * Transaction chiffrée : {
 *   "id": "txn_123456789",
 *   "amount": 100,
 *   "currency": "EUR",
 *   "date": "2023-06-01T12:00:00.000Z",
 *   "paymentMethod": {
 *     "type": "credit_card",
 *     "cardNumber": "eyJpdiI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJkYXRhIjoiYWJjZGVmMTIzNDU2Nzg5MCIsInRhZyI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJrZXlJZCI6ImtleS0xMjM0NTYifQ==",
 *     "expiryDate": "12/25",
 *     "cvv": "eyJpdiI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJkYXRhIjoiYWJjZGVmMTIzNDU2Nzg5MCIsInRhZyI6IjEyMzQ1Njc4OTAxMjM0NTYiLCJrZXlJZCI6ImtleS0xMjM0NTYifQ=="
 *   },
 *   "customer": {
 *     "id": "cust_123456789",
 *     "name": "John Doe",
 *     "email": "<EMAIL>"
 *   }
 * }
 */
