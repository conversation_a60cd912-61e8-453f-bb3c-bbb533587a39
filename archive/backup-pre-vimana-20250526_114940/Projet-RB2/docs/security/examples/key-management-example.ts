/**
 * Exemple d'utilisation du KeyManagementService
 * 
 * Ce fichier montre comment utiliser le service de gestion des clés pour créer,
 * récupérer, faire tourner et révoquer des clés cryptographiques.
 */

import { KeyManagementService } from '@security/key-management';
import { VaultService } from '@security/vault';

async function keyManagementExample() {
  console.log('=== Exemple de gestion des clés ===');

  // Obtenir une instance du service
  const keyManagementService = new KeyManagementService();
  
  // 1. Créer une nouvelle clé
  console.log('\n1. Création d\'une nouvelle clé');
  const keyId = await keyManagementService.createKey('encryption', 'example');
  console.log(`Clé créée avec l'ID : ${keyId}`);
  
  // 2. Récupérer une clé active
  console.log('\n2. Récupération d\'une clé active');
  const { key, metadata } = await keyManagementService.getActiveKey('encryption', 'example');
  console.log(`Clé active : ${key.toString('hex').substring(0, 16)}...`);
  console.log(`Métadonnées : ${JSON.stringify(metadata, null, 2)}`);
  
  // 3. Lister toutes les clés actives
  console.log('\n3. Liste des clés actives');
  const activeKeys = await keyManagementService.listActiveKeys('encryption');
  console.log(`Clés actives : ${JSON.stringify(activeKeys, null, 2)}`);
  
  // 4. Effectuer la rotation d'une clé
  console.log('\n4. Rotation d\'une clé');
  await keyManagementService.rotateKey(keyId);
  console.log(`Rotation de la clé ${keyId} effectuée`);
  
  // 5. Vérifier que la clé a été mise à jour
  console.log('\n5. Vérification de la mise à jour de la clé');
  const { key: newKey, metadata: newMetadata } = await keyManagementService.getActiveKey('encryption', 'example');
  console.log(`Nouvelle clé active : ${newKey.toString('hex').substring(0, 16)}...`);
  console.log(`Nouvelles métadonnées : ${JSON.stringify(newMetadata, null, 2)}`);
  
  // 6. Révoquer une clé
  console.log('\n6. Révocation d\'une clé');
  await keyManagementService.revokeKey(keyId);
  console.log(`Clé ${keyId} révoquée`);
  
  // 7. Vérifier que la clé a été révoquée
  console.log('\n7. Vérification de la révocation de la clé');
  try {
    await keyManagementService.getKey(keyId);
  } catch (error) {
    console.log(`Erreur attendue : ${error.message}`);
  }
  
  // 8. Intégration avec HashiCorp Vault (si activé)
  console.log('\n8. Intégration avec HashiCorp Vault');
  if (process.env.VAULT_ENABLED === 'true') {
    const vaultService = new VaultService();
    
    // Vérifier le statut de l'intégration avec Vault
    const status = await vaultService.getStatus();
    console.log(`Statut de Vault : ${JSON.stringify(status, null, 2)}`);
    
    // Stocker un secret dans Vault
    await vaultService.storeSecret('example/secret', { key: 'value' });
    console.log('Secret stocké dans Vault');
    
    // Récupérer un secret depuis Vault
    const secret = await vaultService.getSecret('example/secret');
    console.log(`Secret récupéré : ${JSON.stringify(secret, null, 2)}`);
  } else {
    console.log('Intégration avec Vault désactivée');
  }
}

// Exécuter l'exemple
keyManagementExample().catch(error => {
  console.error('Erreur :', error);
});

/**
 * Sortie attendue :
 * 
 * === Exemple de gestion des clés ===
 * 
 * 1. Création d'une nouvelle clé
 * Clé créée avec l'ID : 01234567-89ab-cdef-0123-456789abcdef
 * 
 * 2. Récupération d'une clé active
 * Clé active : 0123456789abcdef...
 * Métadonnées : {
 *   "id": "01234567-89ab-cdef-0123-456789abcdef",
 *   "type": "encryption",
 *   "purpose": "example",
 *   "algorithm": "aes-256-gcm",
 *   "createdAt": "2023-06-01T12:00:00.000Z",
 *   "expiresAt": "2023-07-01T12:00:00.000Z",
 *   "status": "active",
 *   "version": 1
 * }
 * 
 * 3. Liste des clés actives
 * Clés actives : [
 *   {
 *     "id": "01234567-89ab-cdef-0123-456789abcdef",
 *     "type": "encryption",
 *     "purpose": "example",
 *     "createdAt": "2023-06-01T12:00:00.000Z",
 *     "expiresAt": "2023-07-01T12:00:00.000Z",
 *     "status": "active",
 *     "version": 1
 *   }
 * ]
 * 
 * 4. Rotation d'une clé
 * Rotation de la clé 01234567-89ab-cdef-0123-456789abcdef effectuée
 * 
 * 5. Vérification de la mise à jour de la clé
 * Nouvelle clé active : fedcba9876543210...
 * Nouvelles métadonnées : {
 *   "id": "01234567-89ab-cdef-0123-456789abcdef",
 *   "type": "encryption",
 *   "purpose": "example",
 *   "algorithm": "aes-256-gcm",
 *   "createdAt": "2023-06-01T12:01:00.000Z",
 *   "expiresAt": "2023-07-01T12:01:00.000Z",
 *   "status": "active",
 *   "version": 2
 * }
 * 
 * 6. Révocation d'une clé
 * Clé 01234567-89ab-cdef-0123-456789abcdef révoquée
 * 
 * 7. Vérification de la révocation de la clé
 * Erreur attendue : Key not found or revoked: 01234567-89ab-cdef-0123-456789abcdef
 * 
 * 8. Intégration avec HashiCorp Vault
 * Intégration avec Vault désactivée
 */
