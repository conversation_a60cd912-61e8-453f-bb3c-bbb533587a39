# Programme de Certification en Sécurité Cryptographique

## Niveau 1 : Fondamentaux

### Prérequis
- Connaissance de base en développement
- Familiarité avec TypeScript/JavaScript

### Modules
1. **Principes de Base**
   - Cryptographie symétrique
   - Cryptographie asymétrique
   - Hachage et signatures

2. **Implémentation Sécurisée**
   - Gestion des clés
   - Bonnes pratiques
   - Gestion des erreurs

### Évaluation
- QCM théorique (40%)
- Projet pratique (60%)
- Note minimale : 75%

## Niveau 2 : Avanc<PERSON>
[...]