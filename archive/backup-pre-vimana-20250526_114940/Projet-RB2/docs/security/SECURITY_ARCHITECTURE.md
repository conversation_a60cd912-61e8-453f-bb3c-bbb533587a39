# Architecture de sécurité en microservices

Ce document définit l'architecture de sécurité de l'application, en particulier les responsabilités et interactions entre le microservice Security et les services de sécurité du Backend.

## Vue d'ensemble

L'architecture de sécurité suit une approche en couches avec une défense en profondeur :

1. **Microservice Security** : Première ligne de défense, responsable des contrôles de sécurité génériques et de haut niveau
2. **Backend Security** : Seconde ligne de défense, responsable des contrôles de sécurité spécifiques au domaine et au contexte

Cette séparation permet une meilleure isolation des préoccupations, une plus grande résilience et une défense en profondeur.

## Responsabilités du microservice Security

Le microservice Security est responsable des contrôles de sécurité génériques et de haut niveau :

### Authentification et autorisation de service
- Gestion des identités de service
- Authentification entre services (mTLS, JWT)
- Autorisation de base entre services

### Sécurité des fichiers - Première phase
- Analyse antivirus (ClamAV)
- Détection de contenu malveillant
- Validation des types de fichiers de base
- Analyse des métadonnées pour détecter les anomalies
- Quarantaine des fichiers suspects

### Surveillance et détection
- Détection d'intrusion au niveau réseau
- Surveillance des événements de sécurité globaux
- Agrégation des journaux de sécurité
- Alertes de sécurité de premier niveau

### Gestion des vulnérabilités
- Scans de vulnérabilité
- Gestion des correctifs
- Évaluation des risques

## Responsabilités du Backend Security

Le Backend Security est responsable des contrôles de sécurité spécifiques au domaine et au contexte :

### Authentification et autorisation utilisateur
- Gestion des identités utilisateur
- Authentification utilisateur (JWT, OAuth, etc.)
- Contrôle d'accès basé sur les rôles (RBAC)
- Contrôle d'accès basé sur les attributs (ABAC)

### Sécurité des fichiers - Seconde phase
- Validation spécifique au domaine
- Transformation sécurisée des fichiers
- Stockage sécurisé avec chiffrement
- Gestion des permissions d'accès
- Vérification d'intégrité continue

### Sécurité des données
- Chiffrement des données sensibles
- Masquage des données
- Gestion des clés
- Contrôle d'accès aux données

### Sécurité des API
- Validation des entrées
- Protection contre les injections
- Limitation de taux (rate limiting)
- Protection CSRF

## Flux de validation des fichiers

Le processus de validation des fichiers suit un flux en deux étapes :

1. **Première phase (Microservice Security)**
   - Le fichier est téléchargé et envoyé au microservice Security
   - Validation de base du type de fichier
   - Analyse antivirus avec ClamAV
   - Analyse de contenu malveillant avec IA
   - Si le fichier est suspect, il est mis en quarantaine et le processus s'arrête
   - Si le fichier est sûr, il est transmis au Backend avec un jeton de sécurité

2. **Seconde phase (Backend Security)**
   - Vérification du jeton de sécurité du microservice Security
   - Validation spécifique au domaine
   - Vérification des permissions d'accès
   - Transformation du fichier si nécessaire
   - Stockage sécurisé avec chiffrement
   - Enregistrement des métadonnées et audit

## Communication entre les services

La communication entre le microservice Security et le Backend utilise les mécanismes suivants :

- **mTLS** : Authentification mutuelle TLS pour sécuriser les communications
- **JWT** : Jetons JWT pour l'autorisation et le transfert d'informations de sécurité
- **API sécurisées** : API RESTful sécurisées avec validation des entrées et limitation de taux
- **Bus d'événements** : Bus d'événements pour la communication asynchrone des événements de sécurité

## Gestion des événements de sécurité

Les événements de sécurité sont gérés de manière centralisée :

1. Le microservice Security et le Backend génèrent des événements de sécurité
2. Les événements sont envoyés à un bus d'événements centralisé
3. Le service de surveillance de sécurité agrège et corrèle les événements
4. Les alertes sont générées en fonction des règles de corrélation
5. Les événements sont stockés pour audit et analyse

## Résilience et dégradation gracieuse

En cas de défaillance d'un composant de sécurité, le système doit se dégrader de manière gracieuse :

1. **Défaillance du microservice Security**
   - Le Backend peut fonctionner en mode dégradé avec des contrôles de sécurité renforcés
   - Les opérations critiques peuvent être temporairement restreintes
   - Les fichiers sont mis en quarantaine jusqu'à ce que le microservice Security soit à nouveau disponible

2. **Défaillance du Backend Security**
   - Le microservice Security continue de fournir une protection de base
   - Les fichiers sont mis en quarantaine jusqu'à ce que le Backend soit à nouveau disponible

## Métriques et observabilité

Pour assurer une visibilité complète sur la sécurité, les métriques suivantes sont collectées :

1. **Métriques du microservice Security**
   - Nombre de fichiers analysés
   - Nombre de menaces détectées
   - Temps de réponse des analyses
   - Taux de faux positifs/négatifs

2. **Métriques du Backend Security**
   - Nombre de validations de fichiers
   - Nombre d'accès refusés
   - Temps de traitement des fichiers
   - Utilisation des ressources de sécurité

Ces métriques sont agrégées dans un tableau de bord de sécurité unifié pour une vue d'ensemble de la posture de sécurité.

## Conclusion

Cette architecture de sécurité en couches fournit une défense en profondeur robuste tout en maintenant une séparation claire des responsabilités. Le microservice Security se concentre sur les contrôles de sécurité génériques et de haut niveau, tandis que le Backend Security se concentre sur les contrôles spécifiques au domaine et au contexte.
