# Guide de gestion des certificats mTLS

Ce document décrit les procédures de gestion des certificats mTLS utilisés pour sécuriser les communications entre les microservices.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Génération des certificats](#génération-des-certificats)
3. [Rotation des certificats](#rotation-des-certificats)
4. [Révocation des certificats](#révocation-des-certificats)
5. [Vérification des certificats](#vérification-des-certificats)
6. [Sauvegarde et restauration](#sauvegarde-et-restauration)
7. [Résolution des problèmes](#résolution-des-problèmes)
8. [Bonnes pratiques](#bonnes-pratiques)

## Vue d'ensemble

L'architecture de sécurité utilise mTLS (Mutual TLS) pour sécuriser les communications entre les microservices. mTLS assure une authentification mutuelle, ce qui signifie que les deux parties (client et serveur) s'authentifient l'une auprès de l'autre en utilisant des certificats X.509.

### Infrastructure à clés publiques (PKI)

Notre PKI est composée des éléments suivants :

- **Autorité de certification (CA)** : Émet et signe les certificats pour tous les services
- **Certificats de service** : Chaque service possède son propre certificat signé par la CA
- **Liste de révocation de certificats (CRL)** : Liste des certificats révoqués

### Fichiers de certificats

Les certificats sont stockés dans le répertoire `certs` de chaque service :

- `ca.crt` : Certificat de l'autorité de certification
- `client.crt` / `server.crt` : Certificat du service
- `client.key` / `server.key` : Clé privée du service
- `ca.crl` : Liste de révocation des certificats

## Génération des certificats

### Génération manuelle

Pour générer manuellement les certificats, utilisez le script `generate-mtls-certs.sh` :

```bash
cd Backend/scripts/security
./generate-mtls-certs.sh
```

Ce script génère :
- Une autorité de certification (CA)
- Un certificat client pour le Backend
- Un certificat serveur pour le microservice Security
- Une liste de révocation initiale

Les certificats sont copiés automatiquement dans les répertoires appropriés.

### Configuration

Les certificats mTLS doivent être configurés dans les variables d'environnement suivantes :

Pour le Backend :
```
ENABLE_MTLS=true
MTLS_CERT_PATH=certs/client.crt
MTLS_KEY_PATH=certs/client.key
MTLS_CA_PATH=certs/ca.crt
```

Pour le microservice Security :
```
ENABLE_MTLS=true
MTLS_CERT_PATH=certs/server.crt
MTLS_KEY_PATH=certs/server.key
MTLS_CA_PATH=certs/ca.crt
```

## Rotation des certificats

La rotation des certificats est le processus de remplacement périodique des certificats pour limiter l'impact d'une compromission potentielle.

### Rotation automatique

Le service `CertificateRotationService` gère la rotation automatique des certificats. Par défaut, les certificats sont renouvelés tous les 30 jours.

Configuration de la rotation automatique :
```
ENABLE_CERT_AUTO_ROTATION=true
CERT_ROTATION_INTERVAL_DAYS=30
```

### Rotation manuelle

Pour effectuer une rotation manuelle des certificats :

```bash
cd Backend/scripts/security
./generate-mtls-certs.sh --generate
```

### Vérification de la rotation

Pour vérifier si la rotation a été effectuée avec succès, vous pouvez consulter les journaux du service ou utiliser la commande suivante :

```bash
openssl x509 -in Backend/certs/client.crt -noout -dates
```

## Révocation des certificats

La révocation est le processus d'invalidation d'un certificat avant sa date d'expiration, généralement en raison d'une compromission.

### Processus de révocation

Pour révoquer un certificat :

1. Identifiez le numéro de série du certificat à révoquer :
   ```bash
   openssl x509 -in Backend/certs/client.crt -noout -serial
   ```

2. Révoquez le certificat en utilisant le script :
   ```bash
   cd Backend/scripts/security
   ./generate-mtls-certs.sh --revoke <numéro_de_série>
   ```

3. Vérifiez que le certificat a été révoqué :
   ```bash
   ./generate-mtls-certs.sh --check <numéro_de_série>
   ```

### Propagation de la révocation

Après la révocation d'un certificat, la nouvelle liste de révocation (CRL) est automatiquement copiée dans les répertoires des services. Cependant, les services doivent être redémarrés pour prendre en compte la nouvelle CRL.

## Vérification des certificats

### Vérification manuelle

Pour vérifier manuellement un certificat :

```bash
# Afficher les informations du certificat
openssl x509 -in Backend/certs/client.crt -text -noout

# Vérifier la validité du certificat par rapport à la CA
openssl verify -CAfile Backend/certs/ca.crt Backend/certs/client.crt

# Vérifier si un certificat est révoqué
cd Backend/scripts/security
./generate-mtls-certs.sh --check <numéro_de_série>
```

### Vérification automatique

Le service `CertificateRotationService` vérifie régulièrement la validité des certificats et déclenche une rotation si nécessaire.

Pour vérifier programmatiquement la validité des certificats :

```typescript
import { CertificateRotationService } from '../services/security/CertificateRotationService';

@Injectable()
export class SomeService {
  constructor(
    private readonly certificateRotationService: CertificateRotationService
  ) {}

  async checkCertificates() {
    const result = await this.certificateRotationService.validateCertificates();

    if (result.valid) {
      console.log('Certificates are valid');
      console.log(`Expires in ${result.details.expiresIn} days`);
    } else {
      console.error('Certificates are invalid:', result.details.error);
    }
  }
}
```

## Sauvegarde et restauration

### Sauvegarde des certificats

Les certificats doivent être sauvegardés régulièrement. Le service `CertificateRotationService` sauvegarde automatiquement les anciens certificats avant la rotation.

Les sauvegardes sont stockées dans le répertoire `certs/backup/<timestamp>/`.

### Restauration des certificats

Pour restaurer des certificats à partir d'une sauvegarde :

1. Identifiez le répertoire de sauvegarde :
   ```bash
   ls -la Backend/certs/backup/
   ```

2. Copiez les certificats de la sauvegarde :
   ```bash
   cp Backend/certs/backup/<timestamp>/* Backend/certs/
   ```

3. Redémarrez les services pour prendre en compte les certificats restaurés.

## Résolution des problèmes

### Problèmes courants

1. **Erreur "certificate has expired"** :
   - Les certificats ont expiré et doivent être renouvelés
   - Solution : Exécutez `./generate-mtls-certs.sh --generate`

2. **Erreur "certificate revoked"** :
   - Le certificat a été révoqué
   - Solution : Générez de nouveaux certificats

3. **Erreur "unable to get local issuer certificate"** :
   - Le certificat CA n'est pas correctement configuré
   - Solution : Vérifiez que le chemin vers le certificat CA est correct

4. **Erreur "self signed certificate in certificate chain"** :
   - La chaîne de certificats n'est pas correctement configurée
   - Solution : Vérifiez que tous les certificats sont signés par la même CA

### Journalisation

Les événements liés aux certificats sont enregistrés dans les journaux du service et dans les événements de sécurité. Pour consulter les événements liés aux certificats :

```sql
SELECT * FROM SecurityEvent WHERE type IN ('CERTIFICATE_ROTATION', 'CERTIFICATE_REVOCATION') ORDER BY timestamp DESC;
```

## Bonnes pratiques

1. **Rotation régulière** : Renouvelez les certificats tous les 30 à 90 jours
2. **Clés privées sécurisées** : Protégez les clés privées avec des permissions restrictives (600)
3. **Surveillance** : Surveillez les dates d'expiration des certificats
4. **Automatisation** : Utilisez le service de rotation automatique
5. **Sauvegarde** : Sauvegardez régulièrement les certificats
6. **Documentation** : Documentez toutes les procédures de gestion des certificats
7. **Tests** : Testez régulièrement le processus de rotation et de révocation

---

**Note importante** : Ce document doit être révisé et mis à jour régulièrement, au moins une fois par an ou après des changements significatifs dans l'infrastructure de certificats.

Dernière mise à jour : 18 avril 2025
