# Guide du Développeur pour les Services de Chiffrement

Ce guide fournit des instructions détaillées sur l'utilisation des services de chiffrement dans l'application Retreat And Be. Il est destiné aux développeurs qui souhaitent intégrer des fonctionnalités de chiffrement dans leurs modules.

## Table des matières

1. [Introduction](#introduction)
2. [Services disponibles](#services-disponibles)
3. [Gestion des clés](#gestion-des-clés)
4. [Chiffrement de bout en bout](#chiffrement-de-bout-en-bout)
5. [Chiffrement homomorphique](#chiffrement-homomorphique)
6. [Chiffrement résistant aux ordinateurs quantiques](#chiffrement-résistant-aux-ordinateurs-quantiques)
7. [Communication sécurisée entre microservices](#communication-sécurisée-entre-microservices)
8. [Journalisation sécurisée](#journalisation-sécurisée)
9. [Optimisation des performances](#optimisation-des-performances)
10. [Tests automatisés](#tests-automatisés)
11. [Bonnes pratiques](#bonnes-pratiques)
12. [Dépannage](#dépannage)

## Introduction

Les services de chiffrement fournissent des méthodes pour protéger les données sensibles dans l'application. Ils sont conçus pour être faciles à utiliser tout en offrant un niveau élevé de sécurité.

Pour utiliser ces services, vous devez d'abord importer le module `EncryptionModule` dans votre module :

```typescript
import { Module } from '@nestjs/common';
import { EncryptionModule } from '../security/encryption.module';

@Module({
  imports: [EncryptionModule],
  // ...
})
export class YourModule {}
```

Ensuite, vous pouvez injecter les services dont vous avez besoin dans vos contrôleurs ou services :

```typescript
import { Injectable } from '@nestjs/common';
import { KeyManagementService } from '../security/services/key-management.service';
import { EndToEndEncryptionService } from '../security/services/end-to-end-encryption.service';

@Injectable()
export class YourService {
  constructor(
    private readonly keyManagementService: KeyManagementService,
    private readonly e2eEncryptionService: EndToEndEncryptionService,
  ) {}

  // ...
}
```

## Services disponibles

Les services de chiffrement suivants sont disponibles :

- **KeyManagementService** : Gestion des clés cryptographiques
- **EndToEndEncryptionService** : Chiffrement de bout en bout
- **HomomorphicEncryptionService** : Chiffrement homomorphique de base
- **AdvancedHomomorphicService** : Chiffrement homomorphique avancé
- **QuantumResistantService** : Chiffrement résistant aux ordinateurs quantiques
- **MicroserviceSecurityService** : Communication sécurisée entre microservices
- **CertificateManagementService** : Gestion des certificats pour mTLS
- **CryptoLoggingService** : Journalisation sécurisée des opérations cryptographiques
- **CryptoPerformanceService** : Optimisation des performances cryptographiques
- **CryptoTestingService** : Tests automatisés des services de chiffrement

## Gestion des clés

Le service `KeyManagementService` gère le cycle de vie des clés cryptographiques, y compris la création, la rotation et la révocation des clés.

### Création d'une clé

```typescript
// Créer une clé pour le chiffrement
const keyId = await this.keyManagementService.createKey(
  'myservice', // Nom de la clé
  'aes-256-gcm', // Algorithme
  'encryption', // Usage
  {
    rotationInterval: 7 * 24 * 60 * 60 * 1000, // 7 jours
    autoRotate: true,
  }
);
```

### Récupération d'une clé

```typescript
// Récupérer une clé par son ID
const { key, metadata } = await this.keyManagementService.getKey(keyId);

// Récupérer une clé active pour un usage spécifique
const { key, metadata } = await this.keyManagementService.getActiveKey('encryption', 'myservice');
```

### Rotation d'une clé

```typescript
// Effectuer la rotation d'une clé
const newKeyId = await this.keyManagementService.rotateKey(keyId);
```

### Révocation d'une clé

```typescript
// Révoquer une clé
await this.keyManagementService.revokeKey(keyId);
```

## Chiffrement de bout en bout

Le service `EndToEndEncryptionService` fournit des méthodes pour le chiffrement de bout en bout, où seuls l'expéditeur et le destinataire peuvent accéder au contenu.

### Génération de paire de clés

```typescript
// Générer une paire de clés pour un utilisateur
const { publicKey, privateKey } = await this.e2eEncryptionService.generateUserKeyPair();

// Stocker la clé privée de manière sécurisée (par exemple, dans le profil de l'utilisateur)
// Partager la clé publique avec les autres utilisateurs
```

### Chiffrement d'un message

```typescript
// Chiffrer un message avec la clé publique du destinataire
const encryptedMessage = await this.e2eEncryptionService.encryptMessage(
  'Message secret',
  recipientPublicKey
);
```

### Déchiffrement d'un message

```typescript
// Déchiffrer un message avec la clé privée du destinataire
const decryptedMessage = await this.e2eEncryptionService.decryptMessage(
  encryptedMessage,
  privateKey
);
```

## Chiffrement homomorphique

Le service `HomomorphicEncryptionService` permet d'effectuer des calculs sur des données chiffrées sans les déchiffrer.

### Activation du chiffrement homomorphique

Le chiffrement homomorphique est désactivé par défaut. Pour l'activer, définissez la variable d'environnement `HOMOMORPHIC_ENCRYPTION_ENABLED=true`.

### Chiffrement de données

```typescript
// Vérifier si le chiffrement homomorphique est activé
if (this.homomorphicEncryptionService.isEnabled()) {
  // Chiffrer des données
  const encryptedData = await this.homomorphicEncryptionService.encrypt('42');
}
```

### Opérations homomorphiques

```typescript
// Addition homomorphique
const encryptedSum = await this.homomorphicEncryptionService.add(encryptedData1, encryptedData2);

// Multiplication homomorphique
const encryptedProduct = await this.homomorphicEncryptionService.multiply(encryptedData1, encryptedData2);

// Moyenne homomorphique
const encryptedAverage = await this.homomorphicEncryptionService.average([encryptedData1, encryptedData2, encryptedData3]);
```

### Déchiffrement de données

```typescript
// Déchiffrer des données
const decryptedData = await this.homomorphicEncryptionService.decrypt(encryptedData);
```

## Chiffrement homomorphique avancé

Le service `AdvancedHomomorphicService` étend le service de chiffrement homomorphique de base avec des opérations plus avancées et des optimisations de performance.

### Chiffrement d'un ensemble de données

```typescript
// Chiffrer un ensemble de données
const data = [1, 2, 3, 4, 5];
const encryptedDataset = await this.advancedHomomorphicService.encryptDataset(data);
```

### Opérations avancées

```typescript
// Addition
const additionResult = await this.advancedHomomorphicService.performOperation({
  type: 'add',
  operands: encryptedDataset,
});

// Moyenne pondérée
const averageResult = await this.advancedHomomorphicService.performOperation({
  type: 'average',
  operands: encryptedDataset,
  weights: [0.1, 0.2, 0.3, 0.2, 0.2],
});

// Comparaison
const comparisonResult = await this.advancedHomomorphicService.performOperation({
  type: 'compare',
  operands: [encryptedData1, encryptedData2],
  threshold: 5,
});
```

### Analyse statistique

```typescript
// Effectuer une analyse statistique sur des données chiffrées
const stats = await this.advancedHomomorphicService.performStatisticalAnalysis(encryptedDataset);
```

## Chiffrement résistant aux ordinateurs quantiques

Le service `QuantumResistantService` fournit des méthodes pour le chiffrement résistant aux attaques par ordinateurs quantiques.

### Activation du chiffrement résistant aux ordinateurs quantiques

Le chiffrement résistant aux ordinateurs quantiques est désactivé par défaut. Pour l'activer, définissez la variable d'environnement `QUANTUM_RESISTANT_ENABLED=true`.

### Génération de clés

```typescript
// Vérifier si le chiffrement résistant aux ordinateurs quantiques est activé
if (this.quantumResistantService.isEnabled()) {
  // Générer des clés
  await this.quantumResistantService.generateKeys();
}
```

### Chiffrement de données

```typescript
// Chiffrer des données
const encryptedData = await this.quantumResistantService.encrypt('Données sensibles');
```

### Déchiffrement de données

```typescript
// Déchiffrer des données
const decryptedData = await this.quantumResistantService.decrypt(encryptedData);
```

## Communication sécurisée entre microservices

Le service `MicroserviceSecurityService` fournit des méthodes pour sécuriser les communications entre microservices en utilisant mTLS et le chiffrement de bout en bout.

### Configuration de mTLS

Pour activer mTLS, définissez la variable d'environnement `ZERO_TRUST_MTLS_ENABLED=true` et configurez les chemins des certificats :

```
MTLS_CERT_PATH=./certs/server.crt
MTLS_KEY_PATH=./certs/server.key
MTLS_CA_PATH=./certs/ca.crt
```

### Envoi de requêtes sécurisées

```typescript
// Envoyer une requête GET sécurisée
const response = await this.microserviceSecurityService.get(
  'https://other-service.retreatandbe.com',
  '/api/v1/data',
  { param1: 'value1' }
);

// Envoyer une requête POST sécurisée
const response = await this.microserviceSecurityService.post(
  'https://other-service.retreatandbe.com',
  '/api/v1/data',
  { key: 'value' }
);
```

### Enregistrement de clés publiques

```typescript
// Enregistrer la clé publique d'un service
this.microserviceSecurityService.registerServicePublicKey(
  'other-service',
  publicKey
);

// Obtenir la clé publique de ce service
const publicKey = this.microserviceSecurityService.getPublicKey();
```

## Gestion des certificats

Le service `CertificateManagementService` fournit des méthodes pour générer et gérer des certificats X.509 pour l'authentification mTLS.

### Génération d'une autorité de certification (CA)

```typescript
// Générer une autorité de certification
const { certPath, keyPath } = await this.certificateManagementService.generateCA({
  commonName: 'Retreat And Be CA',
  organization: 'Retreat And Be',
  country: 'FR',
});
```

### Génération de certificats

```typescript
// Générer un certificat pour un service
const { certPath, keyPath, csrPath } = await this.certificateManagementService.generateCertificate(
  'api-service',
  {
    commonName: 'api.retreatandbe.com',
    organization: 'Retreat And Be',
    organizationalUnit: 'API Services',
  }
);
```

### Génération de certificats pour un service

```typescript
// Générer des certificats pour un service
const { certPath, keyPath, caCertPath } = await this.certificateManagementService.generateServiceCertificates(
  'payment-service'
);
```

### Vérification de certificats

```typescript
// Vérifier si un certificat est valide
const isValid = await this.certificateManagementService.verifyCertificate(certPath);
```

## Journalisation sécurisée

Le service `CryptoLoggingService` fournit des méthodes pour journaliser les opérations cryptographiques de manière sécurisée.

### Journalisation d'événements cryptographiques

```typescript
// Journaliser une opération de chiffrement
await this.cryptoLoggingService.logEncryption(
  'UserService',
  'aes-256-gcm',
  keyId,
  executionTimeMs,
  userId,
  ipAddress
);

// Journaliser une opération de déchiffrement
await this.cryptoLoggingService.logDecryption(
  'UserService',
  'aes-256-gcm',
  keyId,
  executionTimeMs,
  userId,
  ipAddress
);

// Journaliser une rotation de clé
await this.cryptoLoggingService.logKeyRotation(
  'KeyManagementService',
  oldKeyId,
  newKeyId,
  executionTimeMs,
  userId,
  ipAddress
);

// Journaliser une erreur
await this.cryptoLoggingService.logError(
  'UserService',
  'encrypt',
  'Failed to encrypt data: Invalid key',
  keyId,
  'aes-256-gcm',
  userId,
  ipAddress
);
```

### Vérification de l'intégrité des journaux

```typescript
// Vérifier l'intégrité des journaux
const result = await this.cryptoLoggingService.verifyLogIntegrity();
```

## Optimisation des performances

Le service `CryptoPerformanceService` fournit des méthodes pour optimiser les performances des opérations cryptographiques.

### Exécution avec optimisation

```typescript
// Exécuter une opération avec optimisation
const result = await this.cryptoPerformanceService.executeWithOptimization(
  async () => {
    // Opération cryptographique
    return this.e2eEncryptionService.encryptMessage(message, publicKey);
  },
  `encrypt-${message.substring(0, 10)}` // Clé de cache
);
```

### Exécution par lots

```typescript
// Exécuter un lot d'opérations en parallèle
const operations = messages.map(message =>
  () => this.e2eEncryptionService.encryptMessage(message, publicKey)
);

const results = await this.cryptoPerformanceService.executeBatch(operations);
```

### Métriques de performance

```typescript
// Obtenir les métriques de performance
const metrics = this.cryptoPerformanceService.calculatePerformanceMetrics();

// Générer un rapport de performance
const report = await this.cryptoPerformanceService.generatePerformanceReport();
```

## Tests automatisés

Le service `CryptoTestingService` fournit des méthodes pour tester automatiquement les services de chiffrement.

### Exécution de tests

```typescript
// Exécuter tous les tests
const report = await this.cryptoTestingService.runAllTests();

// Exécuter des tests de performance
const performanceReport = await this.cryptoTestingService.runPerformanceTests(
  100, // Nombre d'itérations
  1024 // Taille des données en octets
);
```

## Bonnes pratiques

### Sécurité

1. **Ne stockez jamais les clés privées en clair** dans la base de données ou les fichiers de configuration.
2. **Utilisez toujours des clés différentes** pour différents types de données et différents services.
3. **Effectuez régulièrement la rotation des clés** pour limiter l'impact d'une compromission.
4. **Validez toujours les entrées** avant de les chiffrer ou de les déchiffrer.
5. **Journalisez les opérations cryptographiques** pour détecter les anomalies.

### Performance

1. **Utilisez le service `CryptoPerformanceService`** pour optimiser les opérations cryptographiques.
2. **Mettez en cache les résultats** des opérations cryptographiques coûteuses.
3. **Utilisez le traitement par lots** pour les opérations en masse.
4. **Évitez de chiffrer des données déjà chiffrées** (double chiffrement).
5. **Utilisez des algorithmes appropriés** pour chaque cas d'utilisation.

## Dépannage

### Problèmes courants

#### 1. Erreur "Key not found"

Cette erreur se produit lorsque vous essayez d'utiliser une clé qui n'existe pas ou qui a été révoquée.

**Solution :** Vérifiez l'ID de la clé et utilisez `getActiveKey` pour obtenir une clé active.

#### 2. Erreur "Homomorphic encryption is disabled"

Cette erreur se produit lorsque vous essayez d'utiliser le chiffrement homomorphique alors qu'il est désactivé.

**Solution :** Activez le chiffrement homomorphique en définissant `HOMOMORPHIC_ENCRYPTION_ENABLED=true` dans les variables d'environnement.

#### 3. Erreur "Failed to decrypt data"

Cette erreur peut se produire pour plusieurs raisons : clé incorrecte, données corrompues, algorithme incorrect, etc.

**Solution :** Vérifiez que vous utilisez la bonne clé et le bon algorithme, et que les données n'ont pas été corrompues.

#### 4. Performances lentes

Si les opérations cryptographiques sont lentes, vous pouvez utiliser le service `CryptoPerformanceService` pour les optimiser.

**Solution :** Utilisez `executeWithOptimization` ou `executeBatch` pour améliorer les performances.

### Journalisation et débogage

Pour faciliter le débogage, vous pouvez utiliser le service `CryptoLoggingService` pour journaliser les opérations cryptographiques :

```typescript
try {
  // Opération cryptographique
  const result = await this.e2eEncryptionService.encryptMessage(message, publicKey);

  // Journaliser le succès
  await this.cryptoLoggingService.logEncryption(
    'MyService',
    'aes-256-gcm',
    keyId,
    Date.now() - startTime,
    userId,
    ipAddress
  );

  return result;
} catch (error) {
  // Journaliser l'erreur
  await this.cryptoLoggingService.logError(
    'MyService',
    'encrypt',
    error.message,
    keyId,
    'aes-256-gcm',
    userId,
    ipAddress
  );

  throw error;
}
```

### Support

Si vous rencontrez des problèmes avec les services de chiffrement, contactez l'équipe de sécurité à <EMAIL>.

## Mise à jour du document

Ce guide est régulièrement mis à jour pour refléter les changements dans les services de chiffrement et les bonnes pratiques.

Dernière mise à jour : 18 avril 2025