# Guide d'utilisation des services de chiffrement

Ce guide fournit des instructions détaillées sur l'utilisation des différents services de chiffrement implémentés dans le projet Retreat And Be. Il est destiné aux développeurs qui souhaitent intégrer ces services dans leurs modules.

## Table des matières

1. [Introduction](#introduction)
2. [Services de chiffrement disponibles](#services-de-chiffrement-disponibles)
3. [Service de gestion des clés](#service-de-gestion-des-clés)
4. [Chiffrement financier](#chiffrement-financier)
5. [Chiffrement hybride pour le stockage décentralisé](#chiffrement-hybride-pour-le-stockage-décentralisé)
6. [Chiffrement de bout en bout pour la messagerie](#chiffrement-de-bout-en-bout-pour-la-messagerie)
7. [Communication sécurisée entre microservices](#communication-sécurisée-entre-microservices)
8. [Chiffrement homomorphique](#chiffrement-homomorphique)
9. [Chiffrement résistant aux ordinateurs quantiques](#chiffrement-résistant-aux-ordinateurs-quantiques)
10. [Bonnes pratiques](#bonnes-pratiques)
11. [Dépannage](#dépannage)
12. [Exemples de code](#exemples-de-code)

## Introduction

La sécurité des données est un aspect critique de notre plateforme. Ce guide présente les différents services de chiffrement disponibles dans notre architecture et explique comment les utiliser correctement. L'objectif est de fournir une protection optimale des données tout en maintenant les performances et la facilité d'utilisation.

## Services de chiffrement disponibles

Le projet Retreat And Be propose plusieurs services de chiffrement spécialisés pour différents cas d'utilisation :

| Service | Description | Cas d'utilisation |
|---------|-------------|-------------------|
| `KeyManagementService` | Gestion du cycle de vie des clés cryptographiques | Tous les services nécessitant des clés |
| `FinancialEncryptionService` | Chiffrement AES-256-GCM pour les données financières | Informations de paiement, données bancaires |
| `HybridEncryptionService` | Chiffrement hybride (RSA + AES) pour le stockage | Fichiers stockés sur IPFS |
| `E2EEncryptionService` | Chiffrement de bout en bout pour la messagerie | Messages entre utilisateurs |
| `MicroserviceSecurityService` | Communication sécurisée entre microservices | API internes |
| `HomomorphicEncryptionService` | Calculs sur données chiffrées | Analyses statistiques sur données sensibles |
| `QuantumResistantService` | Protection contre les menaces quantiques | Données à long terme |

## Service de gestion des clés

Le `KeyManagementService` est le service fondamental qui gère le cycle de vie des clés cryptographiques utilisées par tous les autres services de chiffrement.

### Configuration

```typescript
// Configuration via variables d'environnement
process.env.KEY_ROTATION_INTERVAL = '604800000';  // 7 jours
process.env.KEY_EXPIRY = '2592000000';  // 30 jours
process.env.VAULT_ENABLED = 'true';  // Activer l'intégration avec HashiCorp Vault
```

### Utilisation de base

```typescript
import { KeyManagementService } from '@security/key-management';

// Obtenir une instance du service
const keyManagementService = new KeyManagementService();

// Créer une nouvelle clé
const keyId = await keyManagementService.createKey('encryption', 'financial');

// Récupérer une clé active
const { key, metadata } = await keyManagementService.getActiveKey('encryption', 'financial');

// Effectuer la rotation d'une clé
await keyManagementService.rotateKey(keyId);

// Révoquer une clé
await keyManagementService.revokeKey(keyId);
```

### Intégration avec HashiCorp Vault

```typescript
import { VaultService } from '@security/vault';

// Obtenir une instance du service
const vaultService = new VaultService();

// Vérifier le statut de l'intégration avec Vault
const status = await vaultService.getStatus();

// Stocker un secret dans Vault
await vaultService.storeSecret('path/to/secret', { key: 'value' });

// Récupérer un secret depuis Vault
const secret = await vaultService.getSecret('path/to/secret');
```

## Chiffrement financier

Le `FinancialEncryptionService` est spécialisé dans le chiffrement des données financières sensibles comme les numéros de carte de crédit, les informations bancaires, etc.

### Configuration

```typescript
// Configuration via variables d'environnement
process.env.FINANCIAL_ENCRYPTION_ENABLED = 'true';
```

### Chiffrement de données financières

```typescript
import { FinancialEncryptionService } from '@security/financial-encryption';

// Obtenir une instance du service
const financialEncryptionService = new FinancialEncryptionService();

// Chiffrer un numéro de carte de crédit
const encryptedCard = await financialEncryptionService.encryptCardNumber('****************');

// Déchiffrer un numéro de carte de crédit
const cardNumber = await financialEncryptionService.decryptCardNumber(encryptedCard);

// Masquer un numéro de carte de crédit (pour l'affichage)
const maskedCard = financialEncryptionService.maskCardNumber('****************');
// Résultat : '************1111'

// Chiffrer un objet contenant des données financières
const paymentInfo = {
  cardNumber: '****************',
  expiryDate: '12/25',
  cvv: '123',
  cardholderName: 'John Doe'
};

const encryptedPaymentInfo = await financialEncryptionService.encryptObject(
  paymentInfo,
  ['cardNumber', 'cvv']  // Champs à chiffrer
);
```

### Validation de données financières

```typescript
// Valider un numéro de carte de crédit (algorithme de Luhn)
const isValid = financialEncryptionService.validateCardNumber('****************');

// Valider un numéro IBAN
const isValidIban = financialEncryptionService.validateIban('***************************');
```

## Chiffrement hybride pour le stockage décentralisé

Le `HybridEncryptionService` combine le chiffrement asymétrique (RSA) et symétrique (AES) pour sécuriser les fichiers stockés sur IPFS.

### Génération de clés

```typescript
import { HybridEncryptionService } from '@decentralized-storage/security';

// Obtenir une instance du service
const encryptionService = new HybridEncryptionService();

// Générer une paire de clés RSA
const keyPair = encryptionService.generateKeyPair();
console.log(keyPair.publicKey);  // Clé publique à partager
console.log(keyPair.privateKey);  // Clé privée à conserver en sécurité
```

### Chiffrement et déchiffrement de fichiers

```typescript
// Chiffrer un fichier
const fileData = Buffer.from('Contenu du fichier');
const encryptedData = await encryptionService.encrypt(fileData, recipientPublicKey);

// Sérialiser les données chiffrées pour le stockage
const serializedData = encryptionService.serializeEncryptedData(encryptedData);

// Stocker serializedData sur IPFS...

// Plus tard, récupérer et désérialiser les données
const retrievedData = encryptionService.deserializeEncryptedData(serializedData);

// Déchiffrer le fichier
const decryptedData = await encryptionService.decrypt(retrievedData, privateKey);
```

### Chiffrement pour plusieurs destinataires

```typescript
// Chiffrer un fichier pour plusieurs destinataires
const fileData = Buffer.from('Contenu du fichier');
const publicKeys = [user1PublicKey, user2PublicKey, user3PublicKey];

const encryptedResult = await encryptionService.encryptForMultipleRecipients(
  fileData,
  publicKeys
);

// Chaque destinataire peut déchiffrer avec sa propre clé privée
const encryptedDataForUser1 = {
  data: encryptedResult.data,
  iv: encryptedResult.iv,
  tag: encryptedResult.tag,
  encryptedKey: encryptedResult.encryptedKeys[0]
};

const decryptedData = await encryptionService.decrypt(encryptedDataForUser1, user1PrivateKey);
```

## Chiffrement de bout en bout pour la messagerie

Le `E2EEncryptionService` implémente un protocole inspiré de Double Ratchet (Signal Protocol) pour sécuriser les messages entre utilisateurs.

### Initialisation des utilisateurs

```typescript
import { E2EEncryptionService } from '@messaging/security';

// Obtenir l'instance du service
const encryptionService = E2EEncryptionService.getInstance();

// Initialiser les clés pour un utilisateur
const keys = encryptionService.initializeUserKeys('user123');

// Obtenir les clés publiques d'un utilisateur
const publicKeys = encryptionService.getUserPublicKeys('user123');
```

### Établissement de session

```typescript
// Créer une session entre deux utilisateurs
const sessionId = encryptionService.createSession(
  'sender123',
  'recipient456',
  {
    identityKey: recipientPublicKeys.identityKey,
    signedPreKey: recipientPublicKeys.signedPreKey,
    oneTimePreKey: recipientPublicKeys.oneTimePreKeys[0]
  }
);
```

### Chiffrement et déchiffrement de messages

```typescript
// Chiffrer un message
const message = 'Message secret';
const encryptedMessage = await encryptionService.encryptMessage(sessionId, message);

// Envoyer encryptedMessage au destinataire...

// Le destinataire déchiffre le message
const decryptedMessage = await encryptionService.decryptMessage(sessionId, encryptedMessage);
```

### Utilisation du service de messagerie sécurisée

```typescript
import { SecureMessagingService } from '@messaging/services';

// Obtenir l'instance du service
const messagingService = SecureMessagingService.getInstance();

// Initialiser un utilisateur
const publicKeys = messagingService.initializeUser('user123');

// Établir une session sécurisée
const sessionId = messagingService.establishSession(
  'sender123',
  'recipient456',
  recipientPublicKeys
);

// Envoyer un message sécurisé
const message = await messagingService.sendMessage(
  'sender123',
  'recipient456',
  'Message secret',
  'text'
);

// Recevoir un message
const receivedMessage = await messagingService.receiveMessage('recipient456', messageId);
```

## Communication sécurisée entre microservices

Le `MicroserviceSecurityService` sécurise les communications entre les différents microservices de la plateforme.

### Configuration de mTLS

```typescript
// Configuration via variables d'environnement
process.env.ZERO_TRUST_MTLS_ENABLED = 'true';
process.env.CERT_PATH = '/path/to/certs';
process.env.PRIVATE_KEY_PATH = '/path/to/private/key';
process.env.CA_CERT_PATH = '/path/to/ca/cert';
```

### Sécurisation des communications

```typescript
import { MicroserviceSecurityService } from '@security/microservice';

// Obtenir une instance du service
const securityService = new MicroserviceSecurityService();

// Chiffrer un message pour un autre service
const encryptedPayload = await securityService.encryptServicePayload(
  { data: 'Données sensibles' },
  targetServicePublicKey
);

// Envoyer encryptedPayload au service cible...

// Le service cible déchiffre le message
const decryptedPayload = await securityService.decryptServicePayload(
  encryptedPayload,
  privateKey
);
```

### Validation des certificats

```typescript
// Valider un certificat client
const isValid = await securityService.validateClientCertificate(clientCert);

// Vérifier si un certificat est révoqué
const isRevoked = await securityService.isCertificateRevoked(clientCert);
```

## Chiffrement homomorphique

Le `HomomorphicEncryptionService` permet d'effectuer des calculs sur des données chiffrées sans les déchiffrer.

### Configuration

```typescript
// Configuration via variables d'environnement
process.env.HOMOMORPHIC_ENCRYPTION_ENABLED = 'true';
process.env.HOMOMORPHIC_ENCRYPTION_SCHEME = 'BFV';
process.env.HOMOMORPHIC_SECURITY_LEVEL = '128';
process.env.HOMOMORPHIC_POLY_MODULUS_DEGREE = '4096';
```

### Opérations homomorphiques de base

```typescript
import { HomomorphicEncryptionService } from '@security/homomorphic';

// Obtenir une instance du service
const homomorphicService = new HomomorphicEncryptionService();

// Vérifier si le chiffrement homomorphique est activé
if (homomorphicService.isEnabled()) {
  // Chiffrer une valeur
  const encryptedValue = await homomorphicService.encrypt(42);
  
  // Effectuer une addition homomorphique
  const encryptedSum = await homomorphicService.add(encryptedValue, 10);
  
  // Effectuer une multiplication homomorphique
  const encryptedProduct = await homomorphicService.multiply(encryptedValue, 2);
  
  // Déchiffrer le résultat
  const result = await homomorphicService.decrypt(encryptedProduct);
  console.log(result);  // 84 (42 * 2)
}
```

### Opérations homomorphiques avancées

```typescript
// Chiffrer un vecteur de valeurs
const encryptedVector = await homomorphicService.encryptVector([1, 2, 3, 4, 5]);

// Calculer la somme des éléments du vecteur
const encryptedSum = await homomorphicService.sumVector(encryptedVector);

// Calculer le produit scalaire de deux vecteurs
const encryptedDotProduct = await homomorphicService.dotProduct(
  encryptedVector1,
  encryptedVector2
);
```

## Chiffrement résistant aux ordinateurs quantiques

Le `QuantumResistantService` offre une protection contre les menaces quantiques futures.

### Configuration

```typescript
// Configuration via variables d'environnement
process.env.QUANTUM_RESISTANT_ENABLED = 'true';
process.env.QUANTUM_RESISTANT_ALGORITHM = 'KYBER';  // ou 'NTRU', 'MCELIECE', 'SIKE', 'RAINBOW'
```

### Chiffrement et déchiffrement

```typescript
import { QuantumResistantService } from '@security/quantum';

// Obtenir une instance du service
const quantumService = new QuantumResistantService();

// Générer une paire de clés résistante aux ordinateurs quantiques
const keyPair = await quantumService.generateKeyPair();

// Chiffrer des données
const encryptedData = await quantumService.encrypt(
  'Données sensibles à long terme',
  keyPair.publicKey
);

// Déchiffrer des données
const decryptedData = await quantumService.decrypt(
  encryptedData,
  keyPair.privateKey
);
```

### Mode hybride (classique + post-quantique)

```typescript
// Activer le mode hybride
quantumService.setHybridMode(true);

// En mode hybride, les données sont chiffrées à la fois avec un algorithme
// classique (RSA ou ECC) et un algorithme post-quantique
const encryptedData = await quantumService.encrypt(
  'Données doublement protégées',
  keyPair.publicKey
);
```

## Bonnes pratiques

### 1. Gestion des clés

- Utilisez toujours le `KeyManagementService` pour gérer les clés cryptographiques
- Effectuez régulièrement la rotation des clés
- Stockez les clés dans un service sécurisé comme HashiCorp Vault
- Ne stockez jamais les clés privées dans le code source ou les fichiers de configuration

### 2. Choix du service de chiffrement

- Utilisez le service approprié pour chaque cas d'utilisation
- Pour les données financières : `FinancialEncryptionService`
- Pour les fichiers stockés : `HybridEncryptionService`
- Pour la messagerie : `E2EEncryptionService`
- Pour les communications inter-services : `MicroserviceSecurityService`
- Pour les calculs sur données chiffrées : `HomomorphicEncryptionService`
- Pour la protection à long terme : `QuantumResistantService`

### 3. Sécurité des données

- Chiffrez les données sensibles dès leur réception
- Ne déchiffrez les données qu'au moment où elles sont nécessaires
- Limitez l'accès aux données déchiffrées
- Ne journalisez jamais les données sensibles en clair
- Validez toujours les données avant de les chiffrer

### 4. Performance

- Le chiffrement homomorphique est coûteux en ressources, utilisez-le avec parcimonie
- Pour les opérations fréquentes, privilégiez le chiffrement symétrique (AES)
- Mettez en cache les résultats des opérations cryptographiques coûteuses
- Utilisez le traitement par lots pour les opérations en masse

## Dépannage

### Problèmes courants

#### 1. Erreur "Key not found"

Cette erreur se produit lorsque vous essayez d'utiliser une clé qui n'existe pas ou qui a été révoquée.

**Solution :** Vérifiez l'ID de la clé et utilisez `getActiveKey` pour obtenir une clé active.

```typescript
// Incorrect
const key = await keyManagementService.getKey('nonexistent-key-id');

// Correct
const { key, metadata } = await keyManagementService.getActiveKey('encryption', 'financial');
```

#### 2. Erreur "Decryption failed"

Cette erreur peut se produire pour plusieurs raisons : clé incorrecte, données corrompues, etc.

**Solution :** Vérifiez que vous utilisez la bonne clé et que les données n'ont pas été altérées.

```typescript
try {
  const decryptedData = await encryptionService.decrypt(encryptedData, privateKey);
} catch (error) {
  console.error('Erreur de déchiffrement :', error);
  // Vérifiez la clé et les données
}
```

#### 3. Erreur "Session not found"

Cette erreur se produit lorsque vous essayez d'utiliser une session de chiffrement E2E qui n'existe pas.

**Solution :** Vérifiez l'ID de session et créez une nouvelle session si nécessaire.

```typescript
// Vérifier si une session existe
const sessionId = messagingService.getSessionId(senderId, recipientId);
if (!sessionId) {
  // Créer une nouvelle session
  const newSessionId = messagingService.establishSession(
    senderId,
    recipientId,
    recipientPublicKeys
  );
}
```

#### 4. Erreur "Homomorphic encryption is disabled"

Cette erreur se produit lorsque vous essayez d'utiliser le chiffrement homomorphique alors qu'il est désactivé.

**Solution :** Activez le chiffrement homomorphique dans la configuration.

```typescript
// Vérifier si le chiffrement homomorphique est activé
if (!homomorphicService.isEnabled()) {
  console.warn('Le chiffrement homomorphique est désactivé');
  // Utiliser une alternative ou activer le chiffrement homomorphique
  process.env.HOMOMORPHIC_ENCRYPTION_ENABLED = 'true';
}
```

### Journalisation et débogage

Pour faciliter le débogage des problèmes de chiffrement, utilisez le service de journalisation sécurisée :

```typescript
import { CryptoLoggingService } from '@security/logging';

// Obtenir une instance du service
const logger = new CryptoLoggingService();

// Journaliser une opération cryptographique (sans données sensibles)
logger.logOperation('encrypt', {
  service: 'FinancialEncryptionService',
  keyId: 'key-123',
  success: true
});

// Journaliser une erreur
logger.logError('decrypt', {
  service: 'E2EEncryptionService',
  error: 'Invalid key',
  sessionId: 'session-456'
});
```

## Exemples de code

### Exemple 1 : Chiffrement d'une transaction financière

```typescript
import { FinancialEncryptionService } from '@security/financial-encryption';

async function processPayment(paymentInfo) {
  const financialEncryptionService = new FinancialEncryptionService();
  
  // Valider les données de paiement
  if (!financialEncryptionService.validateCardNumber(paymentInfo.cardNumber)) {
    throw new Error('Numéro de carte invalide');
  }
  
  // Chiffrer les données sensibles
  const encryptedPaymentInfo = await financialEncryptionService.encryptObject(
    paymentInfo,
    ['cardNumber', 'cvv']
  );
  
  // Stocker les données chiffrées
  await savePaymentInfo(encryptedPaymentInfo);
  
  // Traiter le paiement avec les données chiffrées
  return processPaymentWithEncryptedData(encryptedPaymentInfo);
}
```

### Exemple 2 : Partage de fichier sécurisé

```typescript
import { HybridEncryptionService } from '@decentralized-storage/security';
import { SecureIPFSService } from '@decentralized-storage/services';

async function shareSecureFile(fileData, ownerId, recipientIds) {
  const encryptionService = new HybridEncryptionService();
  const ipfsService = new SecureIPFSService();
  
  // Générer une paire de clés pour le propriétaire s'il n'en a pas déjà
  let ownerKeyPair = await getUserKeyPair(ownerId);
  if (!ownerKeyPair) {
    ownerKeyPair = encryptionService.generateKeyPair();
    await saveUserKeyPair(ownerId, ownerKeyPair);
  }
  
  // Récupérer les clés publiques des destinataires
  const recipientPublicKeys = await Promise.all(
    recipientIds.map(async (recipientId) => {
      const keyPair = await getUserKeyPair(recipientId);
      return keyPair ? keyPair.publicKey : null;
    })
  );
  
  // Filtrer les clés nulles
  const validRecipientPublicKeys = recipientPublicKeys.filter(key => key !== null);
  
  // Chiffrer et stocker le fichier
  const result = await ipfsService.storeFile(
    fileData,
    {
      name: 'document.pdf',
      size: fileData.length,
      type: 'application/pdf',
      lastModified: Date.now(),
      owner: ownerId
    },
    true,  // chiffrer
    ownerKeyPair.publicKey,
    validRecipientPublicKeys
  );
  
  return {
    cid: result.cid,
    sharedWith: recipientIds.filter((_, index) => recipientPublicKeys[index] !== null)
  };
}
```

### Exemple 3 : Communication sécurisée entre microservices

```typescript
import { MicroserviceSecurityService } from '@security/microservice';
import { CertificateManagementService } from '@security/certificates';

async function secureServiceCall(targetService, payload) {
  const securityService = new MicroserviceSecurityService();
  const certService = new CertificateManagementService();
  
  // Vérifier si mTLS est activé
  if (securityService.isMtlsEnabled()) {
    // Récupérer les certificats
    const clientCert = await certService.getServiceCertificate();
    const clientKey = await certService.getServicePrivateKey();
    const caCert = await certService.getCACertificate();
    
    // Récupérer la clé publique du service cible
    const targetPublicKey = await certService.getServicePublicKey(targetService);
    
    // Chiffrer la charge utile
    const encryptedPayload = await securityService.encryptServicePayload(
      payload,
      targetPublicKey
    );
    
    // Effectuer l'appel sécurisé
    return await makeSecureRequest(
      `https://${targetService}/api/endpoint`,
      encryptedPayload,
      {
        cert: clientCert,
        key: clientKey,
        ca: caCert
      }
    );
  } else {
    // Fallback vers HTTPS standard si mTLS n'est pas activé
    return await makeRequest(
      `https://${targetService}/api/endpoint`,
      payload
    );
  }
}
```

### Exemple 4 : Messagerie sécurisée de bout en bout

```typescript
import { SecureMessagingService } from '@messaging/services';

async function sendSecureMessage(senderId, recipientId, content) {
  const messagingService = SecureMessagingService.getInstance();
  
  // Vérifier si une session existe déjà
  let sessionExists = false;
  try {
    const conversations = messagingService.getConversations(senderId);
    sessionExists = conversations.some(conv => 
      conv.participants.includes(recipientId)
    );
  } catch (error) {
    console.error('Erreur lors de la vérification des conversations :', error);
  }
  
  // Si aucune session n'existe, en établir une nouvelle
  if (!sessionExists) {
    try {
      // Récupérer les clés publiques du destinataire
      const recipientPublicKeys = await fetchUserPublicKeys(recipientId);
      
      // Établir une session
      messagingService.establishSession(
        senderId,
        recipientId,
        recipientPublicKeys
      );
    } catch (error) {
      console.error('Erreur lors de l\'établissement de la session :', error);
      throw new Error('Impossible d\'établir une session sécurisée');
    }
  }
  
  // Envoyer le message
  try {
    const message = await messagingService.sendMessage(
      senderId,
      recipientId,
      content,
      'text'
    );
    
    return message;
  } catch (error) {
    console.error('Erreur lors de l\'envoi du message :', error);
    throw new Error('Impossible d\'envoyer le message sécurisé');
  }
}
```
