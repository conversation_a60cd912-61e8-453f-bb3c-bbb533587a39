# Guide d'utilisation des services de sécurité

Ce guide explique comment utiliser les différents services de sécurité implémentés dans l'application.

## Table des matières

1. [Architecture de sécurité](#architecture-de-sécurité)
2. [Service d'orchestration de sécurité des fichiers](#service-dorchestation-de-sécurité-des-fichiers)
3. [Service de communication inter-services](#service-de-communication-inter-services)
4. [Service de métriques de sécurité](#service-de-métriques-de-sécurité)
5. [Tableau de bord de sécurité](#tableau-de-bord-de-sécurité)
6. [Tests de sécurité automatisés](#tests-de-sécurité-automatisés)
7. [Certificats mTLS](#certificats-mtls)
8. [Bonnes pratiques](#bonnes-pratiques)

## Architecture de sécurité

L'architecture de sécurité de l'application suit une approche en couches avec une défense en profondeur :

1. **Microservice Security** : Première ligne de défense, responsable des contrôles de sécurité génériques et de haut niveau
2. **Backend Security** : Seconde ligne de défense, responsable des contrôles de sécurité spécifiques au domaine et au contexte

Pour plus de détails sur l'architecture de sécurité, consultez le document [SECURITY_ARCHITECTURE.md](./SECURITY_ARCHITECTURE.md).

## Service d'orchestration de sécurité des fichiers

Le service d'orchestration de sécurité des fichiers (`FileSecurityOrchestrator`) coordonne le flux de validation des fichiers entre le microservice Security et le Backend.

### Utilisation

```typescript
import { FileSecurityOrchestrator } from '../services/security/FileSecurityOrchestrator';
import { AllowedFileType } from '../services/security/FileSecurityService';

@Controller('api/files')
export class FileController {
  constructor(
    private readonly fileSecurityOrchestrator: FileSecurityOrchestrator
  ) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Req() request: Request
  ) {
    // Valider le fichier
    const validationResult = await this.fileSecurityOrchestrator.validateFile(
      file,
      AllowedFileType.DOCUMENT,
      request.user?.id,
      request.ip
    );

    if (!validationResult.isValid) {
      throw new BadRequestException(validationResult.errors.join(', '));
    }

    // Enregistrer le fichier de manière sécurisée
    const saveResult = await this.fileSecurityOrchestrator.saveFileSecurely(
      file,
      'uploads',
      AllowedFileType.DOCUMENT,
      request.user?.id,
      request.ip
    );

    if (!saveResult.success) {
      throw new InternalServerErrorException(saveResult.error);
    }

    return {
      success: true,
      filePath: saveResult.filePath,
      metadata: saveResult.metadata
    };
  }
}
```

### Configuration

Le service d'orchestration de sécurité des fichiers peut être configuré via les variables d'environnement suivantes :

- `SECURITY_SERVICE_URL` : URL du microservice Security (par défaut : `http://security-service:3000`)
- `ENABLE_SECURITY_SERVICE` : Activer ou désactiver le microservice Security (par défaut : `true`)
- `SECURITY_FALLBACK_MODE` : Mode de repli en cas d'erreur du microservice Security (`strict` ou `permissive`, par défaut : `strict`)
- `TEMP_DIR` : Répertoire temporaire pour les fichiers (par défaut : `./temp`)

## Service de communication inter-services

Le service de communication inter-services (`InterServiceCommunicationService`) utilise mTLS pour sécuriser les communications entre les microservices.

### Utilisation

```typescript
import { InterServiceCommunicationService } from '../services/security/InterServiceCommunicationService';

@Injectable()
export class SomeService {
  constructor(
    private readonly interServiceCommunicationService: InterServiceCommunicationService
  ) {}

  async callSecurityService() {
    try {
      // Appeler le microservice Security de manière sécurisée
      const result = await this.interServiceCommunicationService.get(
        'https://security-service:3000/api/some-endpoint'
      );
      
      return result;
    } catch (error) {
      // Gérer les erreurs
      console.error('Error calling security service:', error);
      throw error;
    }
  }
}
```

### Configuration

Le service de communication inter-services peut être configuré via les variables d'environnement suivantes :

- `ENABLE_MTLS` : Activer ou désactiver mTLS (par défaut : `true`)
- `MTLS_CERT_PATH` : Chemin vers le certificat client (par défaut : `certs/client.crt`)
- `MTLS_KEY_PATH` : Chemin vers la clé privée client (par défaut : `certs/client.key`)
- `MTLS_CA_PATH` : Chemin vers le certificat de l'autorité de certification (par défaut : `certs/ca.crt`)

## Service de métriques de sécurité

Le service de métriques de sécurité (`SecurityMetricsService`) collecte et agrège les métriques de sécurité de tous les microservices.

### Utilisation

```typescript
import { SecurityMetricsService } from '../services/security/SecurityMetricsService';

@Injectable()
export class SomeService {
  constructor(
    private readonly securityMetricsService: SecurityMetricsService
  ) {}

  async getSecurityMetrics() {
    try {
      // Récupérer les métriques de sécurité
      const metrics = await this.securityMetricsService.getMetrics('daily', 7);
      
      return metrics;
    } catch (error) {
      // Gérer les erreurs
      console.error('Error getting security metrics:', error);
      throw error;
    }
  }
}
```

### Configuration

Le service de métriques de sécurité peut être configuré via les variables d'environnement suivantes :

- `METRICS_RETENTION_DAYS` : Nombre de jours de rétention des métriques (par défaut : `90`)

## Tableau de bord de sécurité

Le tableau de bord de sécurité fournit une interface utilisateur pour visualiser les métriques de sécurité, les événements de sécurité et les audits de sécurité.

### Accès

Le tableau de bord de sécurité est accessible à l'URL suivante : `/security/dashboard`

### Fonctionnalités

- Vue d'ensemble des métriques de sécurité
- Liste des événements de sécurité récents
- Statistiques de sécurité des fichiers
- Statistiques d'authentification
- Liste des audits de sécurité

### Autorisations

Le tableau de bord de sécurité n'est accessible qu'aux utilisateurs ayant les rôles `admin` ou `security-admin`.

## Tests de sécurité automatisés

Les tests de sécurité automatisés sont exécutés dans le pipeline CI/CD pour détecter les vulnérabilités de sécurité.

### Exécution manuelle

Pour exécuter les tests de sécurité manuellement, utilisez la commande suivante :

```bash
bash scripts/security/run-security-tests.sh
```

### Tests exécutés

- Analyse des dépendances avec npm audit
- Analyse statique du code avec ESLint (règles de sécurité)
- Tests de sécurité automatisés
- Analyse de sécurité des conteneurs avec Trivy
- Analyse de sécurité des API avec OWASP ZAP
- Vérification des secrets exposés avec git-secrets
- Vérification des configurations de sécurité

### Rapports

Les rapports de sécurité sont générés dans le répertoire `security-reports/`.

## Certificats mTLS

Les certificats mTLS sont utilisés pour sécuriser les communications entre les microservices.

### Génération des certificats

Pour générer les certificats mTLS, utilisez la commande suivante :

```bash
bash scripts/security/generate-mtls-certs.sh
```

### Configuration

Les certificats mTLS doivent être configurés dans les variables d'environnement suivantes :

- `ENABLE_MTLS=true`
- `MTLS_CERT_PATH=certs/client.crt` (pour le Backend)
- `MTLS_KEY_PATH=certs/client.key` (pour le Backend)
- `MTLS_CA_PATH=certs/ca.crt` (pour le Backend)

Pour le microservice Security :

- `ENABLE_MTLS=true`
- `MTLS_CERT_PATH=certs/server.crt`
- `MTLS_KEY_PATH=certs/server.key`
- `MTLS_CA_PATH=certs/ca.crt`

## Bonnes pratiques

### Validation des entrées

Toujours valider les entrées utilisateur avec des validateurs appropriés :

```typescript
import { IsString, IsEmail, Length, Matches } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @Length(3, 50)
  name: string;

  @IsEmail()
  email: string;

  @IsString()
  @Length(8, 100)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/, {
    message: 'Password too weak'
  })
  password: string;
}
```

### Gestion des événements de sécurité

Toujours enregistrer les événements de sécurité importants :

```typescript
import { SecurityEventService } from '../services/security/SecurityEventService';
import { SecurityEventType, SecuritySeverity } from '../types/security';

@Injectable()
export class SomeService {
  constructor(
    private readonly securityEventService: SecurityEventService
  ) {}

  async someMethod() {
    try {
      // Faire quelque chose
      
      // Enregistrer un événement de sécurité
      await this.securityEventService.createSecurityEvent({
        type: SecurityEventType.SYSTEM_EVENT,
        severity: SecuritySeverity.INFO,
        details: {
          action: 'SOME_ACTION',
          result: 'SUCCESS'
        }
      });
    } catch (error) {
      // Enregistrer un événement de sécurité en cas d'erreur
      await this.securityEventService.createSecurityEvent({
        type: SecurityEventType.SYSTEM_ERROR,
        severity: SecuritySeverity.HIGH,
        details: {
          error: error.message,
          stack: error.stack
        }
      });
      
      throw error;
    }
  }
}
```

### Sécurité des fichiers

Toujours utiliser le service d'orchestration de sécurité des fichiers pour valider et enregistrer les fichiers :

```typescript
import { FileSecurityOrchestrator } from '../services/security/FileSecurityOrchestrator';
import { AllowedFileType } from '../services/security/FileSecurityService';

@Controller('api/files')
export class FileController {
  constructor(
    private readonly fileSecurityOrchestrator: FileSecurityOrchestrator
  ) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Req() request: Request
  ) {
    // Valider et enregistrer le fichier de manière sécurisée
    const saveResult = await this.fileSecurityOrchestrator.saveFileSecurely(
      file,
      'uploads',
      AllowedFileType.DOCUMENT,
      request.user?.id,
      request.ip
    );

    if (!saveResult.success) {
      throw new BadRequestException(saveResult.error);
    }

    return {
      success: true,
      filePath: saveResult.filePath
    };
  }
}
```

### Communications inter-services

Toujours utiliser le service de communication inter-services pour les communications entre microservices :

```typescript
import { InterServiceCommunicationService } from '../services/security/InterServiceCommunicationService';

@Injectable()
export class SomeService {
  constructor(
    private readonly interServiceCommunicationService: InterServiceCommunicationService
  ) {}

  async callSecurityService() {
    // Utiliser le service de communication inter-services
    return this.interServiceCommunicationService.get(
      'https://security-service:3000/api/some-endpoint'
    );
  }
}
```
