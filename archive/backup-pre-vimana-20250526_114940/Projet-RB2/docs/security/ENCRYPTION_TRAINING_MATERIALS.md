# Formation sur les services de chiffrement

Ce document contient les matériaux de formation pour l'équipe de développement sur l'utilisation des services de chiffrement dans le projet Retreat And Be.

## Table des matières

1. [Introduction à la cryptographie](#introduction-à-la-cryptographie)
2. [Architecture de sécurité](#architecture-de-sécurité)
3. [Services de chiffrement](#services-de-chiffrement)
4. [Ateliers pratiques](#ateliers-pratiques)
5. [Bonnes pratiques](#bonnes-pratiques)
6. [Ressources supplémentaires](#ressources-supplémentaires)

## Introduction à la cryptographie

### Concepts fondamentaux

#### Chiffrement symétrique vs asymétrique

- **Chiffrement symétrique** : Utilise la même clé pour chiffrer et déchiffrer
  - Avantages : Rapide, efficace pour de grandes quantités de données
  - Inconvénients : Problème de distribution sécurisée des clés
  - Exemples : AES, ChaCha20

- **Chiffrement asymétrique** : Utilise une paire de clés (publique et privée)
  - Avantages : Résout le problème de distribution des clés
  - Inconvénients : Plus lent, moins efficace pour de grandes quantités de données
  - Exemples : RSA, ECC, EdDSA

#### Fonctions de hachage

- Transforment des données de taille arbitraire en une empreinte de taille fixe
- Propriétés : Déterministe, rapide, résistant aux collisions, effet d'avalanche
- Exemples : SHA-256, SHA-3, BLAKE2

#### Codes d'authentification de message (HMAC)

- Combinaison d'une fonction de hachage et d'une clé secrète
- Utilisés pour vérifier l'intégrité et l'authenticité des données
- Exemple : HMAC-SHA256

#### Chiffrement authentifié

- Combine chiffrement et authentification
- Protège à la fois la confidentialité et l'intégrité
- Exemples : AES-GCM, ChaCha20-Poly1305

### Menaces et attaques

- **Attaque par force brute** : Essai systématique de toutes les clés possibles
- **Attaque par dictionnaire** : Essai de mots de passe courants
- **Attaque de l'homme du milieu (MITM)** : Interception des communications
- **Attaque par rejeu** : Réutilisation de messages légitimes
- **Attaque par canal auxiliaire** : Exploitation d'informations indirectes (temps, consommation d'énergie)
- **Attaque quantique** : Utilisation d'ordinateurs quantiques pour casser certains algorithmes cryptographiques

## Architecture de sécurité

### Vue d'ensemble

L'architecture de sécurité du projet Retreat And Be est basée sur plusieurs couches de protection :

1. **Gestion des clés** : Service centralisé pour la création, la rotation et la révocation des clés
2. **Chiffrement des données** : Services spécialisés pour différents types de données
3. **Authentification et autorisation** : Contrôle d'accès basé sur les rôles
4. **Communication sécurisée** : TLS/mTLS pour les communications entre services
5. **Journalisation et audit** : Suivi des opérations de sécurité

### Diagramme d'architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                     Application Retreat And Be                   │
└───────────────────────────────┬─────────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────────┐
│                     Services de chiffrement                      │
│                                                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌──────────┐ │
│  │ Gestion des │  │ Chiffrement │  │ Chiffrement │  │   E2E    │ │
│  │    clés     │  │  financier  │  │   hybride   │  │Messaging │ │
│  └──────┬──────┘  └──────┬──────┘  └──────┬──────┘  └────┬─────┘ │
│         │                │                │               │      │
│         └────────────────┴────────┬───────┴───────────────┘      │
│                                   │                              │
│  ┌─────────────┐  ┌─────────────┐ │ ┌─────────────┐  ┌──────────┐│
│  │Communication│  │ Chiffrement │ │ │ Chiffrement │  │ Quantum  ││
│  │inter-service│  │homomorphique│ │ │  résistant  │  │Resistant ││
│  └──────┬──────┘  └──────┬──────┘ │ └──────┬──────┘  └────┬─────┘│
│         │                │        │        │               │     │
│         └────────────────┴────────┴────────┴───────────────┘     │
└─────────────────────────────┬────────────────────────────────────┘
                              │
┌─────────────────────────────▼────────────────────────────────────┐
│                    Stockage sécurisé des clés                     │
│                                                                   │
│                        HashiCorp Vault                            │
└───────────────────────────────────────────────────────────────────┘
```

## Services de chiffrement

### KeyManagementService

Le `KeyManagementService` est le service fondamental qui gère le cycle de vie des clés cryptographiques.

#### Fonctionnalités

- Création de clés
- Rotation des clés
- Révocation des clés
- Stockage sécurisé des clés (intégration avec HashiCorp Vault)

#### Exemple d'utilisation

```typescript
// Obtenir une instance du service
const keyManagementService = new KeyManagementService();

// Créer une nouvelle clé
const keyId = await keyManagementService.createKey('encryption', 'financial');

// Récupérer une clé active
const { key, metadata } = await keyManagementService.getActiveKey('encryption', 'financial');
```

### FinancialEncryptionService

Le `FinancialEncryptionService` est spécialisé dans le chiffrement des données financières sensibles.

#### Fonctionnalités

- Chiffrement/déchiffrement de numéros de carte de crédit
- Chiffrement/déchiffrement d'informations bancaires
- Validation de données financières
- Masquage de données sensibles pour l'affichage

#### Exemple d'utilisation

```typescript
// Obtenir une instance du service
const financialEncryptionService = new FinancialEncryptionService();

// Chiffrer un numéro de carte de crédit
const encryptedCard = await financialEncryptionService.encryptCardNumber('****************');

// Masquer un numéro de carte de crédit
const maskedCard = financialEncryptionService.maskCardNumber('****************');
// Résultat : '************1111'
```

### HybridEncryptionService

Le `HybridEncryptionService` combine le chiffrement asymétrique (RSA) et symétrique (AES) pour sécuriser les fichiers stockés sur IPFS.

#### Fonctionnalités

- Génération de paires de clés RSA
- Chiffrement hybride de fichiers
- Chiffrement pour plusieurs destinataires
- Partage sécurisé de fichiers

#### Exemple d'utilisation

```typescript
// Obtenir une instance du service
const encryptionService = new HybridEncryptionService();

// Générer une paire de clés RSA
const keyPair = encryptionService.generateKeyPair();

// Chiffrer un fichier
const fileData = Buffer.from('Contenu du fichier');
const encryptedData = await encryptionService.encrypt(fileData, recipientPublicKey);
```

### E2EEncryptionService

Le `E2EEncryptionService` implémente un protocole inspiré de Double Ratchet (Signal Protocol) pour sécuriser les messages entre utilisateurs.

#### Fonctionnalités

- Gestion des clés d'identité, pré-signées et à usage unique
- Établissement de sessions sécurisées
- Chiffrement/déchiffrement de messages avec forward secrecy
- Protection contre les attaques par rejeu

#### Exemple d'utilisation

```typescript
// Obtenir l'instance du service
const encryptionService = E2EEncryptionService.getInstance();

// Initialiser les clés pour un utilisateur
const keys = encryptionService.initializeUserKeys('user123');

// Créer une session entre deux utilisateurs
const sessionId = encryptionService.createSession(
  'sender123',
  'recipient456',
  recipientPublicKeys
);

// Chiffrer un message
const encryptedMessage = await encryptionService.encryptMessage(sessionId, 'Message secret');
```

### MicroserviceSecurityService

Le `MicroserviceSecurityService` sécurise les communications entre les différents microservices de la plateforme.

#### Fonctionnalités

- Configuration de mTLS
- Chiffrement de bout en bout des charges utiles
- Validation des certificats
- Rotation des certificats

#### Exemple d'utilisation

```typescript
// Obtenir une instance du service
const securityService = new MicroserviceSecurityService();

// Chiffrer un message pour un autre service
const encryptedPayload = await securityService.encryptServicePayload(
  { data: 'Données sensibles' },
  targetServicePublicKey
);
```

### HomomorphicEncryptionService

Le `HomomorphicEncryptionService` permet d'effectuer des calculs sur des données chiffrées sans les déchiffrer.

#### Fonctionnalités

- Chiffrement homomorphique de valeurs
- Opérations arithmétiques sur données chiffrées (addition, multiplication)
- Opérations sur vecteurs chiffrés

#### Exemple d'utilisation

```typescript
// Obtenir une instance du service
const homomorphicService = new HomomorphicEncryptionService();

// Chiffrer une valeur
const encryptedValue = await homomorphicService.encrypt(42);

// Effectuer une addition homomorphique
const encryptedSum = await homomorphicService.add(encryptedValue, 10);

// Déchiffrer le résultat
const result = await homomorphicService.decrypt(encryptedSum);
// Résultat : 52
```

### QuantumResistantService

Le `QuantumResistantService` offre une protection contre les menaces quantiques futures.

#### Fonctionnalités

- Génération de clés résistantes aux ordinateurs quantiques
- Chiffrement/déchiffrement avec des algorithmes post-quantiques
- Mode hybride (classique + post-quantique)

#### Exemple d'utilisation

```typescript
// Obtenir une instance du service
const quantumService = new QuantumResistantService();

// Générer une paire de clés résistante aux ordinateurs quantiques
const keyPair = await quantumService.generateKeyPair();

// Chiffrer des données
const encryptedData = await quantumService.encrypt(
  'Données sensibles à long terme',
  keyPair.publicKey
);
```

## Ateliers pratiques

### Atelier 1 : Gestion des clés

#### Objectif

Apprendre à utiliser le `KeyManagementService` pour gérer les clés cryptographiques.

#### Étapes

1. Configurer l'environnement de développement
   ```bash
   # Configurer les variables d'environnement
   export KEY_ROTATION_INTERVAL=604800000  # 7 jours
   export KEY_EXPIRY=2592000000  # 30 jours
   export VAULT_ENABLED=false  # Désactiver Vault pour l'atelier
   ```

2. Créer une nouvelle clé
   ```typescript
   const keyManagementService = new KeyManagementService();
   const keyId = await keyManagementService.createKey('encryption', 'workshop');
   console.log(`Clé créée avec l'ID : ${keyId}`);
   ```

3. Récupérer une clé active
   ```typescript
   const { key, metadata } = await keyManagementService.getActiveKey('encryption', 'workshop');
   console.log(`Clé active : ${key.toString('hex')}`);
   console.log(`Métadonnées : ${JSON.stringify(metadata)}`);
   ```

4. Effectuer la rotation d'une clé
   ```typescript
   await keyManagementService.rotateKey(keyId);
   console.log(`Rotation de la clé ${keyId} effectuée`);
   ```

5. Révoquer une clé
   ```typescript
   await keyManagementService.revokeKey(keyId);
   console.log(`Clé ${keyId} révoquée`);
   ```

### Atelier 2 : Chiffrement financier

#### Objectif

Apprendre à utiliser le `FinancialEncryptionService` pour protéger les données financières.

#### Étapes

1. Configurer l'environnement de développement
   ```bash
   export FINANCIAL_ENCRYPTION_ENABLED=true
   ```

2. Chiffrer un numéro de carte de crédit
   ```typescript
   const financialEncryptionService = new FinancialEncryptionService();
   
   // Valider un numéro de carte
   const isValid = financialEncryptionService.validateCardNumber('****************');
   console.log(`Carte valide : ${isValid}`);
   
   // Chiffrer un numéro de carte
   const encryptedCard = await financialEncryptionService.encryptCardNumber('****************');
   console.log(`Carte chiffrée : ${encryptedCard}`);
   ```

3. Déchiffrer un numéro de carte de crédit
   ```typescript
   const cardNumber = await financialEncryptionService.decryptCardNumber(encryptedCard);
   console.log(`Carte déchiffrée : ${cardNumber}`);
   ```

4. Masquer un numéro de carte de crédit
   ```typescript
   const maskedCard = financialEncryptionService.maskCardNumber('****************');
   console.log(`Carte masquée : ${maskedCard}`);
   ```

5. Chiffrer un objet contenant des données financières
   ```typescript
   const paymentInfo = {
     cardNumber: '****************',
     expiryDate: '12/25',
     cvv: '123',
     cardholderName: 'John Doe'
   };
   
   const encryptedPaymentInfo = await financialEncryptionService.encryptObject(
     paymentInfo,
     ['cardNumber', 'cvv']  // Champs à chiffrer
   );
   
   console.log(`Objet chiffré : ${JSON.stringify(encryptedPaymentInfo)}`);
   ```

### Atelier 3 : Messagerie sécurisée

#### Objectif

Apprendre à utiliser le `E2EEncryptionService` et le `SecureMessagingService` pour sécuriser les messages.

#### Étapes

1. Initialiser les utilisateurs
   ```typescript
   const messagingService = SecureMessagingService.getInstance();
   
   // Initialiser deux utilisateurs
   const alicePublicKeys = messagingService.initializeUser('alice');
   const bobPublicKeys = messagingService.initializeUser('bob');
   
   console.log(`Clés publiques d'Alice : ${JSON.stringify(alicePublicKeys)}`);
   console.log(`Clés publiques de Bob : ${JSON.stringify(bobPublicKeys)}`);
   ```

2. Établir une session sécurisée
   ```typescript
   // Alice établit une session avec Bob
   const sessionId = messagingService.establishSession(
     'alice',
     'bob',
     bobPublicKeys
   );
   
   console.log(`Session établie avec l'ID : ${sessionId}`);
   ```

3. Envoyer un message sécurisé
   ```typescript
   // Alice envoie un message à Bob
   const message = await messagingService.sendMessage(
     'alice',
     'bob',
     'Bonjour Bob, comment vas-tu ?',
     'text'
   );
   
   console.log(`Message envoyé : ${JSON.stringify(message)}`);
   ```

4. Recevoir un message
   ```typescript
   // Bob reçoit le message
   const receivedMessage = await messagingService.receiveMessage('bob', message.id);
   
   console.log(`Message reçu : ${JSON.stringify(receivedMessage)}`);
   ```

5. Créer une conversation
   ```typescript
   // Créer une conversation entre Alice et Bob
   const conversation = messagingService.createConversation(['alice', 'bob']);
   
   console.log(`Conversation créée : ${JSON.stringify(conversation)}`);
   ```

### Atelier 4 : Stockage sécurisé de fichiers

#### Objectif

Apprendre à utiliser le `HybridEncryptionService` et le `SecureIPFSService` pour sécuriser les fichiers.

#### Étapes

1. Générer des paires de clés
   ```typescript
   const encryptionService = new HybridEncryptionService();
   const ipfsService = new SecureIPFSService();
   
   // Générer des paires de clés pour Alice et Bob
   const aliceKeyPair = encryptionService.generateKeyPair();
   const bobKeyPair = encryptionService.generateKeyPair();
   
   console.log(`Clé publique d'Alice : ${aliceKeyPair.publicKey}`);
   console.log(`Clé publique de Bob : ${bobKeyPair.publicKey}`);
   ```

2. Chiffrer et stocker un fichier
   ```typescript
   // Créer un fichier de test
   const fileData = Buffer.from('Contenu du fichier de test');
   
   // Chiffrer et stocker le fichier
   const result = await ipfsService.storeFile(
     fileData,
     {
       name: 'test.txt',
       size: fileData.length,
       type: 'text/plain',
       lastModified: Date.now(),
       owner: 'alice'
     },
     true,  // chiffrer
     aliceKeyPair.publicKey,
     [bobKeyPair.publicKey]  // partager avec Bob
   );
   
   console.log(`Fichier stocké avec CID : ${result.cid}`);
   ```

3. Récupérer et déchiffrer un fichier
   ```typescript
   // Alice récupère et déchiffre le fichier
   const aliceFile = await ipfsService.retrieveFile(result.cid, aliceKeyPair.privateKey);
   console.log(`Fichier récupéré par Alice : ${aliceFile.toString()}`);
   
   // Bob récupère et déchiffre le fichier
   const bobFile = await ipfsService.retrieveFile(result.cid, bobKeyPair.privateKey);
   console.log(`Fichier récupéré par Bob : ${bobFile.toString()}`);
   ```

4. Partager un fichier avec un autre utilisateur
   ```typescript
   // Générer une paire de clés pour Charlie
   const charlieKeyPair = encryptionService.generateKeyPair();
   
   // Alice partage le fichier avec Charlie
   const encryptedKey = await ipfsService.shareFile(
     result.cid,
     aliceKeyPair.privateKey,
     charlieKeyPair.publicKey
   );
   
   console.log(`Clé chiffrée pour Charlie : ${encryptedKey}`);
   ```

## Bonnes pratiques

### 1. Gestion des clés

- Utilisez toujours le `KeyManagementService` pour gérer les clés cryptographiques
- Effectuez régulièrement la rotation des clés
- Stockez les clés dans un service sécurisé comme HashiCorp Vault
- Ne stockez jamais les clés privées dans le code source ou les fichiers de configuration

### 2. Choix du service de chiffrement

- Utilisez le service approprié pour chaque cas d'utilisation
- Pour les données financières : `FinancialEncryptionService`
- Pour les fichiers stockés : `HybridEncryptionService`
- Pour la messagerie : `E2EEncryptionService`
- Pour les communications inter-services : `MicroserviceSecurityService`
- Pour les calculs sur données chiffrées : `HomomorphicEncryptionService`
- Pour la protection à long terme : `QuantumResistantService`

### 3. Sécurité des données

- Chiffrez les données sensibles dès leur réception
- Ne déchiffrez les données qu'au moment où elles sont nécessaires
- Limitez l'accès aux données déchiffrées
- Ne journalisez jamais les données sensibles en clair
- Validez toujours les données avant de les chiffrer

### 4. Performance

- Le chiffrement homomorphique est coûteux en ressources, utilisez-le avec parcimonie
- Pour les opérations fréquentes, privilégiez le chiffrement symétrique (AES)
- Mettez en cache les résultats des opérations cryptographiques coûteuses
- Utilisez le traitement par lots pour les opérations en masse

### 5. Sécurité du code

- Suivez le principe du moindre privilège
- Utilisez des bibliothèques cryptographiques éprouvées
- Évitez d'implémenter vos propres algorithmes cryptographiques
- Effectuez des revues de code régulières
- Utilisez des outils d'analyse statique pour détecter les vulnérabilités

## Ressources supplémentaires

### Documentation interne

- [Guide d'utilisation des services de chiffrement](./ENCRYPTION_SERVICES_USAGE_GUIDE.md)
- [Documentation des méthodes de cryptage](./ENCRYPTION_METHODS_DOCUMENTATION.md)
- [Guide du développeur pour les services de chiffrement](./ENCRYPTION_DEVELOPER_GUIDE.md)
- [Feuille de route d'implémentation du chiffrement](./ENCRYPTION_IMPLEMENTATION_ROADMAP.md)

### Livres recommandés

- "Cryptographie appliquée" par Bruce Schneier
- "Real-World Cryptography" par David Wong
- "Serious Cryptography" par Jean-Philippe Aumasson

### Ressources en ligne

- [Cryptographie moderne sur Coursera](https://www.coursera.org/learn/crypto)
- [Guide de cryptographie de Mozilla](https://developer.mozilla.org/fr/docs/Web/API/Web_Crypto_API)
- [OWASP Cheat Sheet Series - Cryptographic Storage](https://cheatsheetseries.owasp.org/cheatsheets/Cryptographic_Storage_Cheat_Sheet.html)

### Outils

- [CyberChef](https://gchq.github.io/CyberChef/) - Outil en ligne pour les opérations cryptographiques
- [Cryptool](https://www.cryptool.org/) - Logiciel éducatif pour la cryptographie
- [OpenSSL](https://www.openssl.org/) - Boîte à outils cryptographique
