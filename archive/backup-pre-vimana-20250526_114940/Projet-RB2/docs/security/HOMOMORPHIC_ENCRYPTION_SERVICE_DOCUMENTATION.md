# Documentation du HomomorphicEncryptionService

## Introduction

Le `HomomorphicEncryptionService` est un service de chiffrement homomorphique qui permet d'effectuer des calculs sur des données chiffrées sans avoir à les déchiffrer au préalable. Il utilise la bibliothèque SEAL (Simple Encrypted Arithmetic Library) via le wrapper Node.js `node-seal`.

## Table des matières

1. [Installation et prérequis](#installation-et-prérequis)
2. [Configuration](#configuration)
3. [Schémas de chiffrement](#schémas-de-chiffrement)
4. [API du service](#api-du-service)
5. [Opérations homomorphiques](#opérations-homomorphiques)
6. [Gestion des clés](#gestion-des-clés)
7. [Traitement par lots](#traitement-par-lots)
8. [Exemples d'utilisation](#exemples-dutilisation)
9. [Tests et benchmarks](#tests-et-benchmarks)
10. [Limitations et considérations](#limitations-et-considérations)

## Installation et prérequis

### Prérequis

- Node.js LTS
- CMake (pour la compilation de SEAL)
- Compilateur C++ (g++/clang++)
- Au moins 4 Go de RAM (8 Go recommandés)
- node-seal (`npm install node-seal`)

### Vérification de l'environnement

Un script de vérification est fourni pour valider que votre environnement est correctement configuré :

```bash
# Vérifier l'environnement
./scripts/check-homomorphic-environment.sh
```

### Installation

1. Installer les dépendances :

```bash
npm install @nestjs/config node-seal
```

2. Importer le module dans votre application NestJS :

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SecurityModule } from './modules/security/security.module';

@Module({
  imports: [
    ConfigModule.forRoot(),
    SecurityModule,
  ],
})
export class AppModule {}
```

## Configuration

Le service utilise les variables d'environnement suivantes :

| Variable | Description | Valeur par défaut |
|----------|-------------|------------------|
| `HOMOMORPHIC_ENCRYPTION_ENABLED` | Active/désactive le chiffrement homomorphique | `false` |
| `HOMOMORPHIC_ENCRYPTION_SCHEME` | Schéma à utiliser (`BFV`, `CKKS`) | `CKKS` |
| `HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE` | Degré du polynôme modulaire | `8192` |
| `HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL` | Niveau de sécurité (128, 192, 256) | `128` |

Exemple de configuration dans un fichier `.env` :

```
HOMOMORPHIC_ENCRYPTION_ENABLED=true
HOMOMORPHIC_ENCRYPTION_SCHEME=CKKS
HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE=8192
HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL=128
```

## Schémas de chiffrement

Le service prend en charge deux schémas de chiffrement homomorphique :

### BFV (Brakerski/Fan-Vercauteren)

- Adapté aux opérations sur des entiers
- Supporte l'addition, la multiplication et les opérations modulo
- Plus efficace pour les calculs exacts sur des entiers

### CKKS (Cheon-Kim-Kim-Song)

- Adapté aux opérations sur des nombres à virgule flottante
- Supporte l'addition, la multiplication et les opérations approximatives
- Permet des calculs sur des nombres réels avec une précision contrôlable
- Recommandé pour les applications d'analyse de données et de statistiques

## API du service

### Méthodes d'initialisation

#### `async initialize(): Promise<void>`

Initialise le service de chiffrement homomorphique avec les paramètres configurés.

#### `isEnabled(): boolean`

Vérifie si le chiffrement homomorphique est activé et initialisé.

### Méthodes de chiffrement/déchiffrement

#### `async encrypt(value: number): Promise<Buffer>`

Chiffre une valeur numérique.

#### `async decrypt(encryptedData: Buffer): Promise<number>`

Déchiffre des données chiffrées et retourne la valeur numérique.

#### `async encryptBatch(values: number[]): Promise<Buffer>`

Chiffre un lot de valeurs numériques en une seule opération.

#### `async decryptBatch(encryptedData: Buffer): Promise<number[]>`

Déchiffre un lot de données chiffrées et retourne un tableau de valeurs numériques.

### Méthodes de gestion des clés

#### `async generateFullKeyPair(): Promise<{ publicKey: string; secretKey: string; relinKeys: string }>`

Génère une nouvelle paire de clés complète pour le chiffrement homomorphique.

#### `async rotateKeys(): Promise<{ publicKey: string; secretKey: string; relinKeys: string }>`

Effectue une rotation des clés homomorphiques.

#### `async loadKeys(publicKeyBase64: string, secretKeyBase64: string, relinKeysBase64: string): Promise<void>`

Charge des clés homomorphiques existantes.

## Opérations homomorphiques

### Opérations arithmétiques de base

#### `async add(a: Buffer, b: Buffer): Promise<Buffer>`

Effectue une addition homomorphique sur des données chiffrées.

#### `async subtract(a: Buffer, b: Buffer): Promise<Buffer>`

Effectue une soustraction homomorphique sur des données chiffrées.

#### `async multiply(a: Buffer, b: Buffer): Promise<Buffer>`

Effectue une multiplication homomorphique sur des données chiffrées.

#### `async divide(a: Buffer, b: Buffer | number): Promise<Buffer>`

Effectue une division homomorphique approximative sur des données chiffrées.

### Opérations statistiques

#### `async average(values: Buffer[]): Promise<Buffer>`

Calcule la moyenne homomorphique sur un ensemble de valeurs chiffrées.

#### `async variance(values: Buffer[]): Promise<Buffer>`

Calcule la variance homomorphique sur un ensemble de valeurs chiffrées.

## Gestion des clés

### Génération de clés

Le service génère trois types de clés :

1. **Clé publique** : Utilisée pour le chiffrement des données
2. **Clé secrète** : Utilisée pour le déchiffrement des données
3. **Clés de relinéarisation** : Utilisées pour optimiser les opérations homomorphiques, notamment la multiplication

### Rotation des clés

La rotation des clés est une pratique de sécurité recommandée qui consiste à générer régulièrement de nouvelles clés pour limiter l'impact d'une éventuelle compromission.

```typescript
// Exemple de rotation des clés
const newKeys = await homomorphicEncryptionService.rotateKeys();
```

### Stockage des clés

Les clés sont sérialisées et encodées en base64 pour faciliter leur stockage dans une base de données ou un système de gestion de clés.

## Traitement par lots

Le service prend en charge le traitement par lots (batch processing) qui permet de chiffrer, déchiffrer et effectuer des opérations sur plusieurs valeurs en une seule opération, ce qui améliore considérablement les performances.

```typescript
// Chiffrement par lots
const values = [1, 2, 3, 4, 5];
const encryptedBatch = await homomorphicEncryptionService.encryptBatch(values);

// Déchiffrement par lots
const decryptedValues = await homomorphicEncryptionService.decryptBatch(encryptedBatch);
```

## Exemples d'utilisation

### Chiffrement et déchiffrement simples

```typescript
// Chiffrer une valeur
const value = 42;
const encryptedValue = await homomorphicEncryptionService.encrypt(value);

// Déchiffrer une valeur
const decryptedValue = await homomorphicEncryptionService.decrypt(encryptedValue);
```

### Opérations homomorphiques

```typescript
// Chiffrer deux valeurs
const a = 10;
const b = 5;
const encryptedA = await homomorphicEncryptionService.encrypt(a);
const encryptedB = await homomorphicEncryptionService.encrypt(b);

// Addition homomorphique
const encryptedSum = await homomorphicEncryptionService.add(encryptedA, encryptedB);
const sum = await homomorphicEncryptionService.decrypt(encryptedSum);
// sum ≈ 15

// Multiplication homomorphique
const encryptedProduct = await homomorphicEncryptionService.multiply(encryptedA, encryptedB);
const product = await homomorphicEncryptionService.decrypt(encryptedProduct);
// product ≈ 50

// Division homomorphique
const encryptedQuotient = await homomorphicEncryptionService.divide(encryptedA, b);
const quotient = await homomorphicEncryptionService.decrypt(encryptedQuotient);
// quotient ≈ 2
```

### Calculs statistiques

```typescript
// Chiffrer un ensemble de valeurs
const values = [10, 20, 30, 40, 50];
const encryptedValues = await Promise.all(values.map(v => homomorphicEncryptionService.encrypt(v)));

// Calculer la moyenne homomorphique
const encryptedAverage = await homomorphicEncryptionService.average(encryptedValues);
const average = await homomorphicEncryptionService.decrypt(encryptedAverage);
// average ≈ 30

// Calculer la variance homomorphique
const encryptedVariance = await homomorphicEncryptionService.variance(encryptedValues);
const variance = await homomorphicEncryptionService.decrypt(encryptedVariance);
// variance ≈ 200
```

## Tests et benchmarks

### Exécution des tests

```bash
# Exécuter les tests unitaires
npm run test -- homomorphic-encryption.test.ts
```

### Exécution des benchmarks

```bash
# Compiler le service
npm run build

# Exécuter le benchmark
node scripts/benchmark-homomorphic.js
```

## Limitations et considérations

### Performance

Le chiffrement homomorphique est significativement plus lent que les opérations sur des données non chiffrées. Les facteurs qui influencent les performances sont :

- Le schéma de chiffrement utilisé (BFV vs CKKS)
- Le degré du polynôme modulaire
- Le niveau de sécurité
- La complexité des opérations (la multiplication est plus coûteuse que l'addition)
- Le nombre d'opérations consécutives

### Précision

Le schéma CKKS, utilisé pour les nombres à virgule flottante, introduit des erreurs d'approximation qui s'accumulent avec les opérations successives. Il est important de tenir compte de cette imprécision dans les applications critiques.

### Consommation de mémoire

Le chiffrement homomorphique nécessite beaucoup de mémoire, en particulier pour les opérations complexes et les grands ensembles de données. Assurez-vous que votre environnement dispose de suffisamment de RAM.

### Sécurité

Bien que le chiffrement homomorphique soit considéré comme sécurisé contre les attaques quantiques, il est important de suivre les bonnes pratiques de gestion des clés et de rotation régulière.

---

## Références

- [Microsoft SEAL](https://github.com/microsoft/SEAL) - Bibliothèque de chiffrement homomorphique
- [node-seal](https://github.com/morfix-io/node-seal) - Wrapper Node.js pour Microsoft SEAL
- [Homomorphic Encryption Standardization](https://homomorphicencryption.org/) - Consortium pour la standardisation du chiffrement homomorphique
