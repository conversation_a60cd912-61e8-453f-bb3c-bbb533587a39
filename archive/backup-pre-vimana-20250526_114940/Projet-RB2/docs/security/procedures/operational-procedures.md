# Procédures Opérationnelles de Sécurité

## Gestion des Clés

### Rotation des Clés
1. **Planification**
   - Fréquence : Rotation trimestrielle
   - Fenêtre de maintenance : 02:00-04:00 UTC
   - Notification : J-7

2. **Procédure de Rotation**
```typescript
// Script de rotation des clés
async function rotateKeys() {
  const kms = new KeyManagementService();
  
  // 1. Générer nouvelles clés
  const newKeys = await kms.generateKeyPair({
    algorithm: 'KYBER768',
    purpose: 'ENCRYPTION'
  });

  // 2. Période de transition
  await kms.enableKeyTransition({
    oldKeyId: currentKeyId,
    newKeyId: newKeys.id,
    transitionPeriod: '7d'
  });

  // 3. Validation
  await validateKeyRotation(newKeys.id);
}
```

### Procédure de Backup/Restore
[...]