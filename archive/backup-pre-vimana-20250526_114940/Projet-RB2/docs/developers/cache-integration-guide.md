# Guide d'Intégration du Cache Optimisé

Ce document technique détaille comment implémenter et utiliser le système de cache optimisé dans vos services et applications.

## Table des Matières

1. [Architecture du Cache](#architecture-du-cache)
2. [Configuration et Initialisation](#configuration-et-initialisation)
3. [API de Cache](#api-de-cache)
4. [Patterns d'Utilisation](#patterns-dutilisation)
5. [Stratégies d'Invalidation](#stratégies-dinvalidation)
6. [Surveillance et Métriques](#surveillance-et-métriques)
7. [Considérations pour la Production](#considérations-pour-la-production)

## Architecture du Cache

Le système de cache utilise une architecture à plusieurs niveaux :

```
┌───────────────────────────┐
│ Application               │
└───────────┬───────────────┘
            │
            ▼
┌───────────────────────────┐
│ CacheManager              │
└───────────┬───────────────┘
            │
    ┌───────┴───────┐
    ▼               ▼
┌─────────┐   ┌─────────────┐
│ Memory  │   │ Redis       │
│ Cache   │   │ Distributed │
│ (L1)    │   │ Cache (L2)  │
└─────────┘   └─────────────┘
```

### Composants Principaux

- **CacheManager** : Façade qui gère la stratégie de cache et coordonne les appels
- **MemoryCache** : Cache L1 en mémoire pour accès ultra-rapide (Node.js)
- **RedisCache** : Cache L2 distribué pour partage entre instances
- **CachePolicy** : Définit les règles de cache (TTL, compression, etc.)

## Configuration et Initialisation

Pour initialiser le cache dans votre service :

```typescript
// Import du service de cache
import { CacheManager } from '../services/cache/CacheManager';
import { CacheTier, CacheOptions } from '../types/cache';

// Initialisation
const cacheManager = CacheManager.getInstance();

// Configuration globale (optionnelle)
cacheManager.setDefaultOptions({
  compression: true,
  logCacheMisses: process.env.NODE_ENV === 'development',
  errorFallbackToSource: true
});
```

### Options de Configuration

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `compression` | boolean | `true` | Active la compression des données volumineuses |
| `logCacheMisses` | boolean | `false` | Journalise les échecs de cache pour débogage |
| `errorFallbackToSource` | boolean | `true` | Récupère depuis la source en cas d'erreur |
| `useMemoryCache` | boolean | `true` | Active le cache mémoire L1 |
| `useRedisCache` | boolean | `true` | Active le cache Redis L2 |

## API de Cache

### Méthodes Principales

#### Récupérer une Valeur

```typescript
/**
 * Récupère une donnée du cache, ou l'ajoute si absente
 */
async get<T>(
  key: string,
  fetchSourceFn: () => Promise<T>,
  options?: CacheOptions
): Promise<T>
```

#### Stocker une Valeur

```typescript
/**
 * Stocke une valeur dans le cache
 */
async set(
  key: string,
  value: any,
  options?: CacheOptions
): Promise<void>
```

#### Invalider une Entrée

```typescript
/**
 * Invalide une ou plusieurs entrées du cache
 */
async invalidate(
  key: string | RegExp | string[],
  options?: InvalidationOptions
): Promise<number>
```

#### Invalider par Tag

```typescript
/**
 * Invalide toutes les entrées associées à un ou plusieurs tags
 */
async invalidateByTags(
  tags: string | string[],
  options?: InvalidationOptions
): Promise<number>
```

### Options de Cache

```typescript
interface CacheOptions {
  tier?: CacheTier;         // Niveau de cache (MICRO, FREQUENT, STANDARD, STABLE, REFERENCE)
  ttl?: number;             // Durée de vie en secondes (écrase celle du tier)
  tags?: string[];          // Tags pour invalidation groupée
  compress?: boolean;       // Compression des données (défaut: true pour gros objets)
  staleWhileRevalidate?: boolean; // Renvoie stale data pendant revalidation
  bypassCache?: boolean;    // Ignore le cache, force l'accès à la source
  waitForRefresh?: boolean; // Attend le rafraîchissement avant de répondre
}
```

## Patterns d'Utilisation

### Pattern Clé : Cache-Aside avec Fonction Source

```typescript
async function getUserProfile(userId: string) {
  return cacheManager.get(
    `user:profile:${userId}`,
    async () => {
      // Cette fonction n'est appelée qu'en cas de cache miss
      return await prisma.user.findUnique({
        where: { id: userId },
        include: { preferences: true, settings: true }
      });
    },
    {
      tier: CacheTier.STANDARD,  // Cache pour 1 heure
      tags: [`user:${userId}`, 'user-profiles'],
      compress: true
    }
  );
}
```

### Cache avec Dépendances

```typescript
async function getEventWithAttendees(eventId: string) {
  // Clé de cache composite
  const cacheKey = `event:${eventId}:with-attendees`;
  
  return cacheManager.get(
    cacheKey,
    async () => {
      const event = await prisma.event.findUnique({
        where: { id: eventId },
        include: { 
          attendees: {
            include: { profile: true }
          }
        }
      });
      
      if (!event) throw new NotFoundError(`Event ${eventId} not found`);
      return event;
    },
    {
      tier: CacheTier.FREQUENT,
      // Tags de dépendance pour invalidation intelligente
      tags: [
        `event:${eventId}`,
        ...event.attendees.map(a => `user:${a.userId}`)
      ]
    }
  );
}
```

### Cache Multi-niveau avec SWR (Stale-While-Revalidate)

```typescript
async function getPopularEvents(category: string) {
  return cacheManager.get(
    `popular-events:${category}`,
    async () => {
      return await eventService.findPopularEvents(category);
    },
    {
      tier: CacheTier.STANDARD,
      staleWhileRevalidate: true,  // Permet de renvoyer une valeur périmée pendant la revalidation
      tags: ['popular-events', `category:${category}`]
    }
  );
}
```

### Mise en Cache Conditionnelle

```typescript
async function searchEvents(query: string, filters: any) {
  // Détermine si le résultat est cachable (pas pour les recherches complexes)
  const isCacheable = !filters.advancedFilters && query.length >= 3;
  const cacheKey = isCacheable ? `search:events:${hash(query)}:${hash(filters)}` : null;
  
  return cacheManager.get(
    cacheKey,
    async () => {
      return await searchService.searchEvents(query, filters);
    },
    {
      tier: CacheTier.MICRO,
      bypassCache: !isCacheable  // Bypass le cache si non cacheable
    }
  );
}
```

## Stratégies d'Invalidation

### Invalidation avec Mise à Jour (Write-Through)

```typescript
async function updateUserProfile(userId: string, data: Partial<UserProfile>) {
  // Mettre à jour en DB
  const updatedProfile = await prisma.userProfile.update({
    where: { userId },
    data: data
  });
  
  // Invalider les entrées de cache associées
  await cacheManager.invalidateByTags([`user:${userId}`]);
  
  // Option alternative: mettre à jour le cache directement
  await cacheManager.set(
    `user:profile:${userId}`,
    updatedProfile,
    { tier: CacheTier.STANDARD, tags: [`user:${userId}`] }
  );
  
  return updatedProfile;
}
```

### Invalidation par Pattern

```typescript
// Invalidation de toutes les entrées correspondant à un modèle
await cacheManager.invalidate(/^user:profile:.*$/);

// Invalidation avec options
await cacheManager.invalidate(`event:${eventId}:*`, {
  localOnly: false,    // Invalide aussi dans Redis
  broadcast: true,     // Notifie les autres instances
  waitForCompletion: false  // Ne pas attendre confirmation
});
```

### Invalidation Programmée

```typescript
// Programmation d'invalidation future
await cacheManager.scheduleInvalidation(
  `featured-events:homepage`,
  new Date(Date.now() + 3600 * 1000),  // Dans 1 heure
  {
    retry: true,
    retryCount: 3
  }
);
```

## Surveillance et Métriques

### Récupération des Statistiques

```typescript
const stats = await cacheManager.getStatistics();
console.log(`Cache hit rate: ${stats.hitRate * 100}%`);
console.log(`Average lookup time: ${stats.averageLookupTime}ms`);
```

### Métriques Disponibles

| Métrique | Description |
|----------|-------------|
| `hits` | Nombre de succès de cache |
| `misses` | Nombre d'échecs de cache |
| `hitRate` | Taux de succès (hits / total) |
| `setOperations` | Nombre d'opérations d'écriture |
| `getOperations` | Nombre d'opérations de lecture |
| `invalidations` | Nombre d'invalidations |
| `averageLookupTime` | Temps moyen de recherche (ms) |
| `memoryUsage` | Utilisation mémoire (L1) |
| `redisUsage` | Utilisation Redis (L2) |

### Intégration avec Observabilité

```typescript
import { metrics } from '../utils/monitoring';

// Exemple d'intégration avec Prometheus/Grafana
cacheManager.onStatUpdate((stats) => {
  metrics.gauge('cache_hit_rate', stats.hitRate);
  metrics.gauge('cache_avg_lookup_time', stats.averageLookupTime);
  metrics.counter('cache_hits_total', stats.hits);
  metrics.counter('cache_misses_total', stats.misses);
});
```

## Considérations pour la Production

### Haute Disponibilité

En production, configurez Redis avec réplication et failover :

```typescript
// Configuration Redis HA
const cacheManager = CacheManager.getInstance({
  redis: {
    sentinels: [
      { host: 'redis-sentinel-1', port: 26379 },
      { host: 'redis-sentinel-2', port: 26379 },
      { host: 'redis-sentinel-3', port: 26379 }
    ],
    name: 'mymaster',
    password: process.env.REDIS_PASSWORD,
    enableAutoPipelining: true
  }
});
```

### Gestion des Pannes

Le système est conçu pour être résilient :

1. **Circuit Breaker** : Désactive temporairement Redis en cas de panne
2. **Fallback L1** : Utilise le cache mémoire si Redis est indisponible
3. **Degraded Mode** : Mode dégradé automatique avec TTL réduits

### Sécurité

- Ne stockez **jamais** de données sensibles dans le cache
- Utilisez la compression et non le chiffrement pour les optimisations
- Activez TLS pour les connexions Redis en production
- Définissez des AUTH et ACL pour Redis

### Performance

- Surveillez la taille des objets mis en cache
- Utilisez la compression pour les objets > 1KB
- Évitez de stocker des objets > 100MB
- Surveillez la mémoire Redis et configurez les politiques d'éviction

---

Pour toute question ou besoin d'assistance, contactez l'équipe Backend à <EMAIL>. 