# Guide d'internationalisation

Ce guide explique comment utiliser le système d'internationalisation (i18n) pour traduire l'application dans différentes langues.

## Table des matières

1. [Introduction](#introduction)
2. [Langues supportées](#langues-supportées)
3. [Fichiers de traduction](#fichiers-de-traduction)
4. [Utilisation dans le code](#utilisation-dans-le-code)
5. [API REST](#api-rest)
6. [Interface utilisateur](#interface-utilisateur)
7. [Ajouter une nouvelle langue](#ajouter-une-nouvelle-langue)
8. [Bonnes pratiques](#bonnes-pratiques)

## Introduction

Le système d'internationalisation permet de traduire l'application dans différentes langues. Il utilise des fichiers JSON pour stocker les traductions et fournit des fonctions pour récupérer les traductions dans la langue souhaitée.

## Langues supportées

Les langues actuellement supportées sont:

- Français (fr) - Langue par défaut
- Anglais (en)
- Espagnol (es)
- Allemand (de)
- Italien (it)

## Fichiers de traduction

Les fichiers de traduction sont stockés dans le répertoire `src/i18n/translations`. Chaque langue a son propre fichier JSON, nommé selon le code de la langue (par exemple, `fr.json` pour le français).

Les traductions sont organisées sous forme de paires clé-valeur, où la clé est un identifiant unique et la valeur est la traduction dans la langue correspondante.

Exemple de fichier de traduction (`fr.json`):

```json
{
  "common.welcome": "Bienvenue sur Retreat & Be",
  "common.error": "Une erreur est survenue",
  "retreat.title": "Retraite",
  "retreat.description": "Description",
  "booking.title": "Réservation",
  "booking.status": "Statut"
}
```

Les clés sont organisées par catégorie pour faciliter la maintenance:

- `common.*`: Textes communs à toute l'application
- `retreat.*`: Textes liés aux retraites
- `booking.*`: Textes liés aux réservations
- `partner.*`: Textes liés aux partenaires
- `client.*`: Textes liés aux clients
- `payment.*`: Textes liés aux paiements
- `notification.*`: Textes liés aux notifications
- `error.*`: Messages d'erreur

## Utilisation dans le code

### Backend (Python)

Pour utiliser le système d'internationalisation dans le code Python, importez le service `I18nService` et utilisez la méthode `translate`:

```python
from src.i18n import I18nService

# Créer le service d'internationalisation
i18n_service = I18nService()

# Traduire une clé
welcome_text = i18n_service.translate("common.welcome", "en")
print(welcome_text)  # Output: "Welcome to Retreat & Be"

# Traduire une clé avec des paramètres
error_text = i18n_service.translate("error.minLength", "en", {"min": 8})
print(error_text)  # Output: "Must contain at least 8 characters"
```

### Frontend (JavaScript)

Pour utiliser le système d'internationalisation dans le code JavaScript, utilisez la fonction `t` du module i18n:

```javascript
import { t, setLanguage } from './i18n';

// Définir la langue
setLanguage('en');

// Traduire une clé
const welcomeText = t('common.welcome');
console.log(welcomeText);  // Output: "Welcome to Retreat & Be"

// Traduire une clé avec des paramètres
const errorText = t('error.minLength', { min: 8 });
console.log(errorText);  // Output: "Must contain at least 8 characters"
```

## API REST

L'API REST fournit des endpoints pour récupérer les traductions et les langues supportées:

### Récupérer les langues supportées

```bash
curl -X GET "http://localhost:8000/api/i18n/languages"
```

Réponse:

```json
{
  "status": "success",
  "message": "Langues récupérées avec succès",
  "languages": [
    {
      "code": "fr",
      "name": "Français"
    },
    {
      "code": "en",
      "name": "English"
    },
    {
      "code": "es",
      "name": "Español"
    },
    {
      "code": "de",
      "name": "Deutsch"
    },
    {
      "code": "it",
      "name": "Italiano"
    }
  ]
}
```

### Récupérer les traductions pour une langue

```bash
curl -X GET "http://localhost:8000/api/i18n/translations/en"
```

Réponse:

```json
{
  "status": "success",
  "message": "Traductions récupérées avec succès",
  "language": "en",
  "translations": {
    "common.welcome": "Welcome to Retreat & Be",
    "common.error": "An error occurred",
    ...
  }
}
```

### Traduire une clé

```bash
curl -X GET "http://localhost:8000/api/i18n/translate?key=common.welcome&language=en"
```

Réponse:

```json
{
  "status": "success",
  "message": "Traduction réussie",
  "key": "common.welcome",
  "language": "en",
  "translation": "Welcome to Retreat & Be"
}
```

## Interface utilisateur

L'interface utilisateur utilise le système d'internationalisation pour afficher les textes dans la langue choisie par l'utilisateur.

### Sélecteur de langue

Un sélecteur de langue est disponible dans l'en-tête de l'application pour permettre à l'utilisateur de changer de langue:

```html
<div class="language-selector">
  <select id="language-selector" onchange="changeLanguage(this.value)">
    <option value="fr">Français</option>
    <option value="en">English</option>
    <option value="es">Español</option>
    <option value="de">Deutsch</option>
    <option value="it">Italiano</option>
  </select>
</div>

<script>
  function changeLanguage(language) {
    // Stocker la langue dans le localStorage
    localStorage.setItem('language', language);
    
    // Recharger la page
    location.reload();
  }
  
  // Initialiser le sélecteur de langue
  document.addEventListener('DOMContentLoaded', function() {
    const language = localStorage.getItem('language') || 'fr';
    document.getElementById('language-selector').value = language;
  });
</script>
```

### Initialisation des traductions

Au chargement de la page, les traductions sont récupérées depuis l'API et stockées dans le localStorage:

```javascript
// Récupérer la langue
const language = localStorage.getItem('language') || 'fr';

// Récupérer les traductions
fetch(`/api/i18n/translations/${language}`)
  .then(response => response.json())
  .then(data => {
    // Stocker les traductions dans le localStorage
    localStorage.setItem('translations', JSON.stringify(data.translations));
    
    // Initialiser les traductions
    initTranslations();
  });

// Initialiser les traductions
function initTranslations() {
  // Récupérer les traductions
  const translations = JSON.parse(localStorage.getItem('translations') || '{}');
  
  // Parcourir tous les éléments avec l'attribut data-i18n
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.getAttribute('data-i18n');
    const translation = translations[key] || key;
    
    // Appliquer la traduction
    element.textContent = translation;
  });
}
```

### Utilisation dans le HTML

Pour utiliser les traductions dans le HTML, ajoutez l'attribut `data-i18n` aux éléments:

```html
<h1 data-i18n="common.welcome"></h1>
<p data-i18n="retreat.description"></p>
<button data-i18n="common.save"></button>
```

## Ajouter une nouvelle langue

Pour ajouter une nouvelle langue:

1. Créez un nouveau fichier JSON dans le répertoire `src/i18n/translations` (par exemple, `de.json` pour l'allemand)
2. Ajoutez les traductions dans ce fichier
3. Ajoutez la langue dans la liste des langues supportées dans le fichier `src/i18n/translations.py`

```python
# Langues supportées
_supported_languages = {
    "fr": "Français",
    "en": "English",
    "es": "Español",
    "de": "Deutsch",  # Nouvelle langue
    "it": "Italiano"
}
```

4. Ajoutez la langue dans le sélecteur de langue de l'interface utilisateur

```html
<select id="language-selector" onchange="changeLanguage(this.value)">
  <option value="fr">Français</option>
  <option value="en">English</option>
  <option value="es">Español</option>
  <option value="de">Deutsch</option>  <!-- Nouvelle langue -->
  <option value="it">Italiano</option>
</select>
```

## Bonnes pratiques

- Utilisez des clés descriptives et organisées par catégorie
- Évitez les traductions dupliquées
- Utilisez des paramètres pour les textes variables
- Testez les traductions dans toutes les langues supportées
- Mettez à jour les traductions lorsque vous ajoutez de nouveaux textes
- Utilisez des outils de traduction automatique pour les premières versions, puis faites-les réviser par des locuteurs natifs
