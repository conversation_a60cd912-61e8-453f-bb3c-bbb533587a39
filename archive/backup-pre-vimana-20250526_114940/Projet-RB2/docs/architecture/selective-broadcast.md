# Système de Diffusion Sélective (Selective Broadcast)

## Aperçu
Le système de diffusion sélective permet d'optimiser la synchronisation en temps réel en ne diffusant les mises à jour qu'aux clients concernés. Cette approche réduit considérablement la bande passante utilisée et améliore les performances globales du système, particulièrement dans un contexte multi-utilisateurs et multi-appareils.

## Architecture

### Composants Principaux

1. **SelectiveBroadcastManager** - Gestionnaire central de diffusion sélective
2. **ActionBroadcastAnalyzer** - Analyseur d'actions pour déterminer les destinataires
3. **BroadcastMiddleware** - Middleware Redux pour intégrer la diffusion sélective
4. **RecipientRegistry** - Registre des destinataires et de leurs abonnements

### Diagramme d'Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Action Creator │     │  Redux Store    │     │  Redux Reducer  │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         ▼                       │                       │
┌──────────────────────────────────────────────────────────────────┐
│                                                                  │
│                   Selective Broadcast Middleware                 │
│                                                                  │
├──────────────────────────────────────────────────────────────────┤
│                                                                  │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────┐            │
│  │             │   │             │   │             │            │
│  │ Broadcast   │   │ Action      │   │ Recipient   │            │
│  │ Manager     │   │ Analyzer    │   │ Registry    │            │
│  │             │   │             │   │             │            │
│  └─────────────┘   └─────────────┘   └─────────────┘            │
│                                                                  │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────┐            │
│  │             │   │             │   │             │            │
│  │ Batching    │   │ Compression │   │ Delivery    │            │
│  │ Service     │   │ Service     │   │ Strategy    │            │
│  │             │   │             │   │             │            │
│  └─────────────┘   └─────────────┘   └─────────────┘            │
│                                                                  │
└──────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌──────────────────────────────────────────────────────────────────┐
│                                                                  │
│                     WebSocket Manager                            │
│                                                                  │
└──────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌──────────────────────────────────────────────────────────────────┐
│                                                                  │
│                      Clients Ciblés                              │
│                                                                  │
└──────────────────────────────────────────────────────────────────┘
```

## Implémentation Technique

### Types de Destinataires

Le système prend en charge plusieurs types de destinataires pour une diffusion ciblée :

```typescript
export enum RecipientType {
  /** Diffuser à un utilisateur spécifique */
  USER = 'user',
  
  /** Diffuser à un appareil spécifique */
  DEVICE = 'device',
  
  /** Diffuser à un groupe d'utilisateurs */
  GROUP = 'group',
  
  /** Diffuser à un rôle spécifique */
  ROLE = 'role',
  
  /** Diffuser à tous les abonnés d'une ressource */
  RESOURCE_SUBSCRIBERS = 'resource_subscribers',
  
  /** Diffuser à tous les clients connectés */
  ALL = 'all'
}
```

### Utilisation de Base

#### Diffusion Manuelle

```typescript
import { selectiveBroadcast, RecipientType } from '@projet-rb2/state/sync';

// Diffusion manuelle à des destinataires spécifiques
selectiveBroadcast.broadcastAction(action, {
  recipients: [
    { type: RecipientType.USER, id: 'user123' },
    { type: RecipientType.ROLE, id: 'admin' }
  ],
  priority: 1,
  delay: 0
});
```

#### Diffusion Automatique via Analyseurs

```typescript
// Configuration des analyseurs pour la diffusion automatique
selectiveBroadcast.registerBroadcastAnalyzer({
  actionType: 'messages/addMessage',
  analyze: (action, state) => ({
    recipients: [{ type: RecipientType.USER, id: action.payload.recipientId }]
  })
});
```

### Analyseurs Prédéfinis

Le système fournit des analyseurs prédéfinis pour les cas d'utilisation courants :

#### Analyseur basé sur l'utilisateur

```typescript
// Crée un analyseur qui diffuse des actions en fonction de l'ID utilisateur
const userAnalyzer = selectiveBroadcast.createUserBasedAnalyzer(
  'users/update',
  (action, state) => [action.payload.userId]
);

// Enregistrement de l'analyseur
selectiveBroadcast.registerBroadcastAnalyzer(userAnalyzer);
```

#### Analyseur basé sur le rôle

```typescript
// Crée un analyseur qui diffuse des actions en fonction du rôle
const roleAnalyzer = selectiveBroadcast.createRoleBasedAnalyzer(
  'permissions/update',
  (action, state) => [action.payload.role]
);

// Enregistrement de l'analyseur
selectiveBroadcast.registerBroadcastAnalyzer(roleAnalyzer);
```

#### Analyseur basé sur la ressource

```typescript
// Crée un analyseur qui diffuse des actions en fonction de la ressource
const resourceAnalyzer = selectiveBroadcast.createResourceBasedAnalyzer(
  'entities/update',
  (action, state) => [
    { type: 'task', id: action.payload.taskId }
  ]
);

// Enregistrement de l'analyseur
selectiveBroadcast.registerBroadcastAnalyzer(resourceAnalyzer);
```

## Optimisations

### Regroupement (Batching)

Le système regroupe automatiquement les diffusions pour optimiser la bande passante :

```typescript
// Configuration du regroupement
selectiveBroadcast.configure({
  enableBatching: true,
  batchInterval: 100, // ms
  maxBatchSize: 50
});
```

### Compression

La compression automatique des données est appliquée en fonction de la taille :

```typescript
// Configuration de la compression
selectiveBroadcast.configure({
  enableCompression: true,
  compressionThreshold: 1024 // octets
});
```

### Stratégies de Livraison

Différentes stratégies de livraison sont disponibles selon les besoins :

```typescript
// Configuration de la stratégie de livraison
selectiveBroadcast.broadcastAction(action, {
  recipients: [...],
  deliveryStrategy: 'exactly-once'
});
```

## Intégration avec Redux

Le middleware Redux permet d'intégrer facilement la diffusion sélective :

```typescript
import { createStore, applyMiddleware } from 'redux';
import { createSelectiveBroadcastMiddleware } from '@projet-rb2/state/sync';
import rootReducer from './reducers';

// Création du middleware
const selectiveBroadcastMiddleware = createSelectiveBroadcastMiddleware();

// Création du store avec le middleware
const store = createStore(
  rootReducer,
  applyMiddleware(selectiveBroadcastMiddleware)
);
```

## Avantages

1. **Réduction de la bande passante** - Seuls les clients concernés reçoivent les mises à jour
2. **Amélioration des performances** - Moins de données à traiter pour chaque client
3. **Sécurité améliorée** - Les données sensibles ne sont envoyées qu'aux destinataires autorisés
4. **Scalabilité** - Le système peut gérer un grand nombre de clients connectés
5. **Flexibilité** - Plusieurs types de destinataires et stratégies de diffusion

## Cas d'Utilisation

### Messagerie en Temps Réel

```typescript
// Analyseur pour les messages
selectiveBroadcast.registerBroadcastAnalyzer({
  actionType: 'chat/sendMessage',
  analyze: (action, state) => {
    const { conversationId, message } = action.payload;
    const conversation = state.conversations.byId[conversationId];
    
    // Diffuser à tous les participants de la conversation
    return {
      recipients: conversation.participants.map(userId => ({
        type: RecipientType.USER,
        id: userId
      }))
    };
  }
});
```

### Notifications

```typescript
// Analyseur pour les notifications
selectiveBroadcast.registerBroadcastAnalyzer({
  actionType: 'notifications/create',
  analyze: (action, state) => {
    const { notification } = action.payload;
    const recipients = [];
    
    // Ajouter le destinataire principal
    recipients.push({
      type: RecipientType.USER,
      id: notification.userId
    });
    
    // Ajouter les administrateurs si c'est une notification importante
    if (notification.priority === 'high') {
      recipients.push({
        type: RecipientType.ROLE,
        id: 'admin'
      });
    }
    
    return { recipients };
  }
});
```

### Mises à Jour Collaboratives

```typescript
// Analyseur pour les mises à jour collaboratives
selectiveBroadcast.registerBroadcastAnalyzer({
  actionType: /^documents\/(update|delete|create)/,
  analyze: (action, state) => {
    const { documentId } = action.payload;
    const document = state.documents.byId[documentId];
    
    // Diffuser aux utilisateurs qui ont accès au document
    return {
      recipients: [
        // Propriétaire du document
        { type: RecipientType.USER, id: document.ownerId },
        
        // Collaborateurs
        ...document.collaborators.map(userId => ({
          type: RecipientType.USER,
          id: userId
        })),
        
        // Abonnés à cette ressource
        { 
          type: RecipientType.RESOURCE_SUBSCRIBERS, 
          id: `document:${documentId}` 
        }
      ]
    };
  }
});
```

## Conclusion

Le système de diffusion sélective est un composant essentiel de notre architecture de synchronisation, permettant d'optimiser les performances et la bande passante tout en offrant une expérience utilisateur fluide et réactive. Son intégration avec Redux et le système de synchronisation multi-device en fait un outil puissant pour les applications collaboratives et temps réel.
