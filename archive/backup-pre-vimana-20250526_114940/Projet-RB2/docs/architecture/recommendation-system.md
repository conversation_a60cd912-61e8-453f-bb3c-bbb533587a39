# Architecture du Système de Recommandations

Ce document décrit l'architecture et les composants du système de recommandations de Retreat And Be, conçu pour offrir des suggestions personnalisées aux utilisateurs.

## Table des Matières

1. [Vue d'Ensemble](#vue-densemble)
2. [Composants du Système](#composants-du-système)
3. [Modèles d'Apprentissage Automatique](#modèles-dapprentissage-automatique)
4. [Flux de Données](#flux-de-données)
5. [Intégration avec le Cache](#intégration-avec-le-cache)
6. [Déploiement et Scaling](#déploiement-et-scaling)
7. [Métriques et Amélioration Continue](#métriques-et-amélioration-continue)
8. [Analyse et Visualisation](#analyse-et-visualisation)
9. [Génération de Rapports](#génération-de-rapports)
10. [Intégration de Données Externes](#intégration-de-données-externes)
11. [Personnalisation Avancée](#personnalisation-avancée)
12. [Partage Collaboratif](#partage-collaboratif)

## Vue d'Ensemble

Le système de recommandations de Retreat And Be est conçu pour suggérer des retraites et événements pertinents aux utilisateurs en fonction de leurs préférences, comportements et similitudes avec d'autres utilisateurs.

![Architecture Vue d'Ensemble](../images/recommendation-architecture-overview.png)

### Objectifs

- Augmenter l'engagement utilisateur
- Améliorer la conversion (réservations)
- Personnaliser l'expérience utilisateur
- Maximiser la découverte de contenu pertinent

### Approche Hybride

Notre système utilise une approche hybride combinant :

1. **Filtrage collaboratif** : Recommandations basées sur les comportements similaires d'utilisateurs
2. **Filtrage basé sur le contenu** : Recommandations basées sur les attributs des items et les préférences utilisateur
3. **Recommandations contextuelles** : Adaptées au contexte (saison, localisation, etc.)
4. **Knowledge-based** : Règles métier et expertise du domaine

## Composants du Système

### 1. Collecte et Préparation des Données

- **Sources de données** :
  - Historique de navigation et vues de pages
  - Réservations et achats
  - Préférences explicites (intérêts, enquêtes)
  - Données démographiques et profil utilisateur
  - Interactions (bookmarks, partages, temps passé)

- **Pipeline ETL** :
  - Agrégation et nettoyage des données
  - Normalisation et transformation
  - Extraction de caractéristiques
  - Gestion des valeurs manquantes

### 2. Engine de Recommandation

- **Orchestrateur** : Coordonne les différents modèles et stratégies
- **Service de Scoring** : Calcule et combine les scores de pertinence
- **Gestionnaire de Contexte** : Adapte les recommandations au contexte actuel
- **Filtre de Diversité** : Assure une diversité dans les recommandations
- **Gestionnaire de Fraîcheur** : Équilibre entre items populaires et nouveautés

### 3. API de Recommandation

- **Endpoints REST** pour l'intégration frontend
- **Batch API** pour les recommandations pré-calculées
- **Real-time API** pour les recommandations à la volée

### 4. Services Auxiliaires

- **Service de Feedback** : Collecte les retours sur les recommandations
- **Service d'A/B Testing** : Compare différentes stratégies
- **Service d'Explainability** : Fournit des explications sur les recommandations
- **Service de Monitoring** : Surveille la qualité et la performance

## Modèles d'Apprentissage Automatique

### Filtrage Collaboratif

#### User-Based CF

```python
# Pseudo-code pour User-Based Collaborative Filtering
def user_based_recommendations(user_id, n_recommendations=5):
    # 1. Trouver des utilisateurs similaires
    similar_users = find_similar_users(user_id, method='cosine_similarity')

    # 2. Identifier les items appréciés par ces utilisateurs
    candidate_items = get_items_from_similar_users(similar_users)

    # 3. Filtrer les items déjà vus par l'utilisateur cible
    candidate_items = filter_seen_items(candidate_items, user_id)

    # 4. Calculer les scores et trier
    scored_items = score_items(candidate_items, similar_users)

    # 5. Retourner les top N recommandations
    return top_n_items(scored_items, n_recommendations)
```

#### Item-Based CF

Utilise des matrices d'items similaires pré-calculées pour des recommandations rapides basées sur l'historique utilisateur.

### Modèles de Factorisation Matricielle

Implémentation de SVD (Singular Value Decomposition) et ALS (Alternating Least Squares) pour découvrir les facteurs latents.

```typescript
// TypeScript pseudo-code pour l'utilisation de modèles de factorisation
class MatrixFactorizationModel {
  private userFactors: Map<string, number[]>;
  private itemFactors: Map<string, number[]>;

  predict(userId: string, itemId: string): number {
    const userVector = this.userFactors.get(userId);
    const itemVector = this.itemFactors.get(itemId);

    if (!userVector || !itemVector) return 0;

    // Produit scalaire des vecteurs latents
    return dotProduct(userVector, itemVector);
  }

  getRecommendations(userId: string, n: number): RecommendationItem[] {
    // Calculer le score pour tous les items
    // Trier par score et retourner les top N
    // ...
  }
}
```

### Modèles Deep Learning

Pour les cas d'usage avancés, nous implémentons :

- **Neural Collaborative Filtering** : Amélioration des CF traditionnels
- **Sequential Models** (LSTM/Transformers) : Capture les tendances temporelles
- **Graph Neural Networks** : Exploite les relations entre utilisateurs et items

## Flux de Données

### Offline Processing

```mermaid
graph TD
    A[Données Brutes] --> B[ETL Pipeline]
    B --> C[Feature Store]
    C --> D[Entraînement des Modèles]
    D --> E[Modèles Entraînés]
    E --> F[Calcul Batch de Recommandations]
    F --> G[Cache Redis]
    H[Events Streaming] --> B
    I[Base de Données] --> B
```

1. **Extraction nocturne** des données utilisateurs et items
2. **Traitement et transformation** des données
3. **Entraînement périodique** des modèles (quotidien ou hebdomadaire)
4. **Pré-calcul des recommandations** pour les segments utilisateurs
5. **Stockage dans Redis** avec TTL approprié

### Real-time Processing

```mermaid
graph LR
    A[Requête Client] --> B[API Gateway]
    B --> C[Service de Recommandation]
    C --> D{Cache Hit?}
    D -- Oui --> E[Récupération du Cache]
    D -- Non --> F[Calcul à la Volée]
    F --> G[Mise en Cache]
    G --> E
    E --> H[Réponse au Client]
    I[Événements Utilisateur] --> J[Stream Processor]
    J --> K[Mise à Jour Profil]
    K --> L[Invalidation Cache Sélective]
```

1. **Requête utilisateur** pour des recommandations
2. **Vérification du cache** pour les résultats pré-calculés
3. **Calcul en temps réel** si nécessaire (pour nouveaux utilisateurs ou contextes spécifiques)
4. **Enrichissement** avec informations contextuelles
5. **Mise en cache** des nouveaux résultats

## Intégration avec le Cache

Le système exploite l'infrastructure de cache multi-niveaux existante :

### Stratégie de Cache

| Type de Recommandation | Niveau de Cache | TTL | Stratégie d'Invalidation |
|--------------------------|----------------|-----|--------------------------|
| Recommandations générales | STABLE | 24h | Par événement majeur |
| Recommandations par segment | STANDARD | 1h | Par changement de segment |
| Recommandations personnalisées | FREQUENT | 5min | Par activité utilisateur |
| Recommandations contextuelles | MICRO | 10s | Automatique |

### Pattern d'Utilisation du Cache

```typescript
async function getRecommendations(userId: string, context: RecommendationContext) {
  const cacheKey = `recommendations:user:${userId}:${hash(context)}`;

  return cacheManager.get(
    cacheKey,
    async () => {
      // Cette fonction n'est appelée qu'en cas de cache miss
      const userProfile = await getUserProfile(userId);
      const recommendations = await recommendationEngine.generateRecommendations(
        userProfile,
        context
      );

      // Tracking pour amélioration
      await trackRecommendationGeneration(userId, recommendations);

      return recommendations;
    },
    {
      tier: CacheTier.FREQUENT,
      tags: [`user:${userId}`, 'recommendations'],
      staleWhileRevalidate: true // Permet de retourner des données périmées pendant la revalidation
    }
  );
}
```

### Invalidation Intelligente

- **Par Utilisateur** : Lors de changements de profil ou comportement
- **Par Item** : Lorsqu'un item est modifié ou sa popularité change
- **Par Contexte** : Changements saisonniers, promotions, etc.
- **Globale** : Lors de mises à jour des modèles

## Déploiement et Scaling

### Architecture Microservices

```
┌────────────────────┐     ┌────────────────────┐
│ Recommendation API │     │  Training Service  │
│ - Serving          │     │ - Model Training   │
│ - Caching          │     │ - Feature Pipeline │
│ - Personalization  │     │ - Evaluation       │
└──────────┬─────────┘     └─────────┬──────────┘
           │                          │
           ▼                          ▼
┌────────────────────┐     ┌────────────────────┐
│    Redis Cache     │     │  Model Registry    │
└──────────┬─────────┘     └─────────┬──────────┘
           │                          │
           ▼                          ▼
┌────────────────────┐     ┌────────────────────┐
│  Feature Service   │     │  Feedback Service  │
│ - User Profiles    │     │ - A/B Testing      │
│ - Item Catalog     │     │ - Analytics        │
└────────────────────┘     └────────────────────┘
```

### Stratégie de Déploiement

- **Infrastructure** : Kubernetes avec auto-scaling
- **Modèles** : Déploiement A/B pour comparaison
- **Monitoring** : Prometheus + Grafana
- **Logging** : ELK Stack avec alerting

### Options de Scaling

- **Horizontal** : Replicas pour le service API
- **Vertical** : Ressources dédiées pour l'entraînement
- **Caching** : Redis cluster avec sharding
- **Batch/Real-time** : Séparation des ressources

## Métriques et Amélioration Continue

### KPIs Principaux

- **Taux de Clic (CTR)** : % de recommandations cliquées
- **Taux de Conversion** : % conduisant à une réservation
- **Diversité** : Variété des items recommandés
- **Fraîcheur** : % d'items récents recommandés
- **Temps de Réponse** : Latence des recommandations

### Pipeline d'Évaluation

```mermaid
graph TD
    A[Nouvelle Version du Modèle] --> B[Évaluation Offline]
    B --> C{Métriques OK?}
    C -- Non --> A
    C -- Oui --> D[Déploiement Canary]
    D --> E[Test A/B]
    E --> F{Amélioration?}
    F -- Non --> A
    F -- Oui --> G[Déploiement Complet]
    G --> H[Monitoring Continu]
```

### Système de Feedback

- **Explicite** : Évaluations des recommandations
- **Implicite** : Clics, temps passé, conversions
- **Rejection** : Recommandations ignorées
- **Long-term** : Impact sur rétention et LTV

## Analyse et Visualisation

Le système de recommandation intègre des fonctionnalités avancées d'analyse et de visualisation pour évaluer l'efficacité des recommandations et identifier les axes d'amélioration.

### Service d'Analyse

Le service d'analyse (`AnalyticsService`) fournit des métriques détaillées sur les performances du système de recommandation :

```typescript
interface GlobalMetrics {
  period: { start: Date; end: Date };
  totalRecommendations: number;
  totalInteractions: number;
  interactionCounts: Record<string, number>;
  rates: {
    clickThroughRate: number;
    conversionRate: number;
    engagementRate: number;
  };
  byType: TypeMetrics[];
  byStrategy: StrategyMetrics[];
  daily: DailyMetrics[];
}
```

#### Métriques Globales

- **Taux de Conversion** : Pourcentage d'utilisateurs qui effectuent un achat après avoir vu une recommandation
- **Taux d'Engagement** : Pourcentage d'utilisateurs qui interagissent avec les recommandations
- **Taux de Clic** : Pourcentage d'utilisateurs qui cliquent sur une recommandation

#### Métriques par Utilisateur

- **Préférences** : Catégories et tags préférés
- **Interactions** : Historique des interactions avec les recommandations
- **Conversion** : Taux de conversion personnalisé

### Service de Visualisation

Le service de visualisation (`VisualizationService`) génère des représentations graphiques des données de recommandation :

#### Graphe de Similarité entre Utilisateurs

```mermaid
graph TD
    A[Utilisateur 1] -- 0.8 --> B[Utilisateur 2]
    A -- 0.7 --> C[Utilisateur 3]
    B -- 0.6 --> C
    D[Utilisateur 4] -- 0.9 --> A
```

#### Graphe de Recommandations

```mermaid
graph LR
    A[Utilisateur] -- reçoit --> B[Recommandation 1]
    A -- reçoit --> C[Recommandation 2]
    B -- recommande --> D[Item 1]
    C -- recommande --> E[Item 2]
    A -- view --> D
    A -- like --> E
```

#### Carte de Chaleur des Recommandations

Visualisation de l'efficacité des différentes stratégies de recommandation pour chaque type d'item.

## Génération de Rapports

Le système intègre un service de génération de rapports PDF (`ReportGeneratorService`) pour fournir des analyses détaillées aux administrateurs et aux utilisateurs.

### Rapports Globaux

- **Période d'Analyse** : Configurable (jour, semaine, mois, année)
- **Métriques Globales** : Taux de conversion, engagement, etc.
- **Analyse par Type** : Performance par type de recommandation
- **Analyse par Stratégie** : Performance par stratégie de recommandation
- **Tendances** : Évolution des métriques dans le temps

### Rapports Utilisateur

- **Profil Utilisateur** : Préférences et comportements
- **Recommandations Reçues** : Historique des recommandations
- **Interactions** : Historique des interactions
- **Catégories Préférées** : Analyse des catégories les plus appréciées
- **Suggestions Personnalisées** : Recommandations pour améliorer l'expérience

## Intégration de Données Externes

Le système enrichit les recommandations avec des données provenant de sources externes pour améliorer leur pertinence et leur contextualisation.

### Sources de Données

- **Google Trends** : Tendances de recherche actuelles
- **News API** : Actualités pertinentes
- **OpenWeatherMap** : Données météorologiques
- **Événements Locaux** : Calendrier d'événements

### Architecture d'Intégration

```mermaid
graph TD
    A[Sources Externes] --> B[Service de Données Externes]
    B --> C[Cache Redis]
    B --> D[Base de Données]
    C --> E[Service de Recommandation]
    D --> E
    E --> F[Recommandations Enrichies]
```

### Cycle de Vie des Données

1. **Collecte** : Récupération périodique des données externes
2. **Traitement** : Filtrage et transformation des données
3. **Stockage** : Mise en cache et persistance
4. **Enrichissement** : Intégration aux recommandations
5. **Expiration** : Suppression des données obsolètes

## Personnalisation Avancée

Le système offre des fonctionnalités avancées de personnalisation pour adapter les recommandations aux préférences spécifiques de chaque utilisateur.

### Préférences Utilisateur

```typescript
interface UserPreferences {
  recommendationStrategy: RecommendationStrategy;
  diversityLevel: number;
  preferredCategories: string[];
  excludedCategories: string[];
  preferredTags: string[];
  maxRecommendations: number;
}
```

### Explications des Recommandations

Chaque recommandation est accompagnée d'une explication personnalisée indiquant pourquoi elle a été suggérée à l'utilisateur :

- **Basées sur le Contenu** : "Recommandé car vous avez aimé [item similaire]"
- **Basées sur le Profil** : "Correspond à vos préférences en [catégorie]"
- **Sociales** : "Populaire parmi les utilisateurs similaires à vous"
- **Contextuelles** : "Tendance actuelle dans votre région"

### Apprentissage Continu

Le système s'adapte en continu aux interactions de l'utilisateur :

1. **Collecte de Feedback** : Enregistrement des interactions
2. **Analyse des Patterns** : Identification des préférences
3. **Ajustement des Modèles** : Mise à jour des paramètres
4. **Personnalisation** : Adaptation des recommandations

## Partage Collaboratif

Le système permet aux utilisateurs de partager des recommandations avec d'autres utilisateurs, créant ainsi une dimension sociale au processus de recommandation.

### Modèle de Données

```typescript
interface SharedRecommendation {
  id: string;
  senderId: string;
  recipientId: string;
  itemId: string;
  itemType: RecommendationType;
  message: string;
  status: 'SENT' | 'VIEWED' | 'ACCEPTED' | 'REJECTED';
  createdAt: Date;
  viewedAt?: Date;
  respondedAt?: Date;
}
```

### Flux de Partage

```mermaid
sequenceDiagram
    participant A as Utilisateur A
    participant S as Système
    participant B as Utilisateur B

    A->>S: Partage une recommandation
    S->>B: Notifie du partage
    B->>S: Consulte la recommandation
    S->>A: Notifie de la consultation
    B->>S: Accepte la recommandation
    S->>A: Notifie de l'acceptation
```

### Analyse Sociale

Le système analyse les patterns de partage pour identifier :

- **Influenceurs** : Utilisateurs dont les recommandations sont souvent acceptées
- **Communautés** : Groupes d'utilisateurs qui partagent fréquemment
- **Items Viraux** : Recommandations fréquemment partagées
- **Conversion Sociale** : Taux de conversion des recommandations partagées

---

Ce document fournit un aperçu architectural du système de recommandations. Pour des détails d'implémentation spécifiques, veuillez consulter la documentation technique dans le dossier `docs/recommendation-system/`.