SecRuleEngine On
SecRequestBodyAccess On
SecResponseBodyAccess On
SecResponseBodyMimeType text/plain text/html text/xml application/json
SecRequestBodyLimit 13107200
SecRequestBodyNoFilesLimit 131072
SecRequestBodyInMemoryLimit 131072
SecRequestBodyLimitAction Reject
SecPcreMatchLimit 1000
SecPcreMatchLimitRecursion 1000
SecRule REQUEST_HEADERS:Content-Type "text/xml" \
     "id:'200000',phase:1,t:none,t:lowercase,pass,nolog,ctl:requestBodyProcessor=XML"
SecRule REQUEST_HEADERS:Content-Type "application/json" \
     "id:'200001',phase:1,t:none,t:lowercase,pass,nolog,ctl:requestBodyProcessor=JSON"

# Basic XSS protection
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "(?i:<script[^>]*>[\s\S]*?)" \
    "id:'941100',phase:2,t:none,t:urlDecodeUni,block,msg:'XSS Attack Detected',tag:'application-multi',tag:'language-multi',tag:'platform-multi',tag:'attack-xss'"

# SQL Injection protection
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "(?i:(\b(union|select|insert|update|delete|drop|alter)\b))" \
    "id:'942100',phase:2,t:none,t:urlDecodeUni,block,msg:'SQL Injection Attack Detected',tag:'application-multi',tag:'language-multi',tag:'platform-multi',tag:'attack-sqli'"

# File inclusion protection
SecRule REQUEST_FILENAME|ARGS "(?i:(/\.\./|/\.\.))" \
    "id:'930100',phase:2,t:none,t:urlDecodeUni,block,msg:'Path Traversal Attack Detected',tag:'application-multi',tag:'language-multi',tag:'platform-multi',tag:'attack-lfi'"

# Command injection protection
SecRule REQUEST_COOKIES|!REQUEST_COOKIES:/__utm/|REQUEST_COOKIES_NAMES|ARGS_NAMES|ARGS|XML:/* "(?i:(\b(exec|system|passthru|shell_exec)\b))" \
    "id:'932100',phase:2,t:none,t:urlDecodeUni,block,msg:'Command Injection Attack Detected',tag:'application-multi',tag:'language-multi',tag:'platform-multi',tag:'attack-rce'"

# SSRF protection
SecRule REQUEST_FILENAME|ARGS "(?i:(localhost|127\.0\.0\.1))" \
    "id:'933100',phase:2,t:none,t:urlDecodeUni,block,msg:'Potential SSRF Attack Detected',tag:'application-multi',tag:'language-multi',tag:'platform-multi',tag:'attack-ssrf'"

# Enable logging
SecAuditEngine RelevantOnly
SecAuditLogRelevantStatus "^(?:5|4(?!04))"
SecAuditLogParts ABIJDEFHZ
SecAuditLogType Serial
SecAuditLog /var/log/modsec_audit.log

# Performance settings
SecCollectionTimeout 600
SecDataDir /tmp/
SecTmpDir /tmp/
SecDebugLog /var/log/modsec_debug.log
SecDebugLogLevel 0
