# État du Déploiement Kubernetes - RB2 Platform

## Services Fonctionnels

Les services suivants sont correctement déployés et en état `Running` :

### Services d'Infrastructure
- PostgreSQL (backends de plusieurs services)
- Redis (backends de plusieurs services)
- MongoDB (backends de plusieurs services)

### Services Applicatifs
- Keycloak (authentification et gestion des utilisateurs)
- Financial Service

## Services Problématiques

### Problèmes Helm et CRDs

1. **Istio** : Erreur lors du déploiement liée aux CRDs manquants.
   - Problème : `no matches for kind "AuthorizationPolicy", "EnvoyFilter", "PeerAuthentication", "VirtualService"`
   - Solution appliquée : Installation des CRDs via istioctl, mais certains problèmes persistent.
   - Recommandation : Installation complète d'Istio via istioctl avant de déployer le chart Helm.

2. **Monitoring** : Erreur liée à la fonction "service" non définie.
   - Problème : `function "service" not defined`
   - Solution appliquée : Création d'un fichier _helpers.tpl, mais des problèmes persistent.
   - Recommandation : Vérifier et corriger le template grafana-dashboards.yaml.

3. **Analyzer** : Erreur liée au format de l'expression `{{path}}`.
   - Problème : Erreur d'échappement pour la variable dans le template grafana-dashboard.yaml.
   - Solution appliquée : Correction de l'échappement, mais des problèmes persistent.
   - Recommandation : Réviser entièrement le template et ses dépendances.

### Problèmes d'Images

La plupart des pods de service sont dans l'état `ImagePullBackOff` ou `Pending`, ce qui indique des problèmes avec les images de conteneurs :

1. **ImagePullBackOff** : Les pods ne peuvent pas télécharger les images spécifiées.
   - Problème : Images non accessibles ou inexistantes.
   - Solution appliquée : Patch des déploiements pour utiliser nginx:alpine comme image temporaire.
   - Recommandation : Configurer un registry d'images accessible et mettre à jour les valeurs dans values.yaml.

2. **Pods Pending** : Les pods ne peuvent pas être planifiés.
   - Problème : Ressources insuffisantes ou autres contraintes de planification.
   - Recommandation : Vérifier les ressources du cluster et ajuster les limites de ressources.

## État Général

- **Services d'Infrastructure** (Bases de données) : Fonctionnels
- **Services Core** : Partiellement fonctionnels (problèmes d'images)
- **Services Istio et Monitoring** : Non fonctionnels (problèmes de CRDs et de templates)

## Prochaines Étapes

1. **Correction des Images** :
   - Créer et publier des images Docker pour tous les services
   - Mettre à jour les values.yaml pour référencer ces images

2. **Correction des CRDs** :
   - Installer correctement Istio via istioctl avec tous les composants nécessaires
   - Vérifier que tous les CRDs sont bien installés avant de déployer les services Istio

3. **Correction des Templates Helm** :
   - Réviser les templates problématiques (monitoring, analyzer)
   - Ajouter des fonctions d'helper manquantes

4. **Tests et Validation** :
   - Tester chaque service individuellement une fois les images corrigées
   - Vérifier les connexions entre services 