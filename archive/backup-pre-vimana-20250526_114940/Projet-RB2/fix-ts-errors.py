#!/usr/bin/env python3
import os
import re
import glob
import argparse
import time
import concurrent.futures

# Nombre total de fichiers traités
total_files = 0
# Nombre de fichiers corrigés
corrected_files = 0
# Verbose mode
verbose = False

def fix_typescript_file(file_path):
    """Corrige les erreurs de syntaxe TypeScript introduites par un script précédent."""
    global corrected_files
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Faire une copie du contenu original
        original_content = content
        
        # Corrections pour App.tsx
        # Correction pour setTimeout
        content = re.sub(r'setTimeout\(\(\) => {([^}]+)}\s+(\d+)\);', r'setTimeout(() => {\1}, \2);', content)
        
        # Correction pour la map avec expression incomplète
        content = re.sub(r'(const\s+updatedTasks\s*=\s*tasks\.map\(task\s*=>\s*);', r'\1 {', content)
        content = re.sub(r'(task\.id === currentTask\.id);', r'\1', content)
        
        # Correction pour bloc ternaire mal formé
        content = re.sub(r'\? \{\.\.\.task, title: taskTitle, description: taskDescription \}(\s*): task;', r'? {...task, title: taskTitle, description: taskDescription }\1: task', content)
        
        # Correction pour les return( avec un ; juste après
        content = re.sub(r'return\s*\(;', r'return (', content)
        
        # Corrections pour les balises JSX mal formées
        content = re.sub(r'<(\w+);', r'<\1', content)
        content = re.sub(r'/>(\s*>)', r'/>', content)
        content = re.sub(r'<button;', r'<button', content)
        content = re.sub(r'<div;', r'<div', content)
        content = re.sub(r'<input;', r'<input', content)
        content = re.sub(r'<textarea;', r'<textarea', content)
        
        # Correction pour les attributs avec un ; à la fin
        content = re.sub(r'(required);', r'\1', content)
        
        # Remplacement des tags de fermeture incorrects
        content = re.sub(r'</button>(\s*</div>)', r'</button>\1', content)
        content = re.sub(r'</button>(\s*</form>)', r'</button>\1', content)
        
        # Correction des opérateurs incorrects
        content = re.sub(r'priority\s*=\s*==\s*1', r'priority === 1', content)
        
        # Correction des expressions booléennes incomplètes
        content = re.sub(r'(strategy === LoadingStrategy\.FULL_EXPERIENCE) \|\| ;', r'\1;', content)
        
        # Corrections pour useAdaptiveBandwidth.ts
        content = re.sub(r'adaptiveBandwidth;', r'adaptiveBandwidth,', content)
        content = re.sub(r'!navigator\.onLine;', r'!navigator.onLine', content)
        content = re.sub(r'getPageSize;', r'getPageSize,', content)
        
        # Corrections pour AdaptiveBandwidthManager.ts
        content = re.sub(r'export interface (\w+) {;', r'export interface \1 {', content)
        content = re.sub(r'quality: NetworkQuality\.GOOD;', r'quality: NetworkQuality.GOOD,', content)
        content = re.sub(r'isOffline;', r'isOffline,', content)
        
        # Correction pour la condition if dans AdaptiveBandwidthManager.ts
        content = re.sub(r'if \(!?\(\'connection\' in navigator\) {[\)]\s*{', r'if (!("connection" in navigator)) {', content)
        
        # Correction pour les timeouts qui manquent une virgule
        content = re.sub(r'setTimeout\(\(\) => {([^}]+)}\s+(\d+)\);', r'setTimeout(() => {\1}, \2);', content)
        content = re.sub(r'setTimeout\(\(\) => ([^{][^;]+)\s+(\d+)\);', r'setTimeout(() => \1, \2);', content)
        content = re.sub(r'setTimeout\(\(\) => {([^}]+)}\s*(\d+)\);', r'setTimeout(() => {\1}, \2);', content)
        content = re.sub(r'setTimeout\(\(\) => ([^{][^;]+)\s*(\d+)\);', r'setTimeout(() => \1, \2);', content)
        
        # Correction spécifique pour les timeouts avec setSyncStatus
        content = re.sub(r'setTimeout\(\(\) => {(setSyncStatus\([^}]+\))}\s*(\d+)\);', r'setTimeout(() => {\1}, \2);', content)
        content = re.sub(r'setTimeout\(\(\) => (setSyncStatus\([^)]+\))\s*(\d+)\);', r'setTimeout(() => \1, \2);', content)
        
        # Correction pour les accolades manquantes dans les expressions de map
        content = re.sub(r'(\.map\(task\s*=>\s*)([^{][^;]*?)\n', r'\1 {\n\2\n}', content)
        
        # Corrections générales pour les problèmes de syntaxe TypeScript
        # Ajouter des points-virgules manquants après return statements
        content = re.sub(r'return ([^;{}\n]+)(?!\s*[;{])\s*\n', r'return \1;\n', content)
        
        # Ajouter des virgules manquantes dans les objets
        content = re.sub(r'(\w+):\s*([^,{}\s][^,{}]*[^,{}\s])\s*\n\s*}', r'\1: \2,\n}', content)
        
        # Ajouter des accolades manquantes pour les expressions de bloc if/for/while
        content = re.sub(r'(if\s*\([^)]+\))\s*([^{;\n].*?)(?=\s*\n)', r'\1 {\2}', content)
        content = re.sub(r'(for\s*\([^)]+\))\s*([^{;\n].*?)(?=\s*\n)', r'\1 {\2}', content)
        content = re.sub(r'(while\s*\([^)]+\))\s*([^{;\n].*?)(?=\s*\n)', r'\1 {\2}', content)
        
        # Corriger les <> qui ne sont pas bien fermés dans le JSX
        content = re.sub(r'<([A-Z]\w*)\s*>([^<]*?)<\/(\w+)>', lambda m: f"<{m.group(1)}>{m.group(2)}</{m.group(1)}>" if m.group(1) != m.group(3) else m.group(0), content)
        
        # Corriger les espaces dans les génériques TypeScript
        content = re.sub(r'<(\w+)\s*=\s*{}\s+(\w+)\s*=\s*{}\s+(\w+)', r'<\1 = {}, \2 = {}, \3', content)
        
        # Correction 1: Supprimer les points-virgules superflus après les accolades dans les déclarations
        # d'interfaces, de classes, d'enums, de types, etc.
        content = re.sub(r'(interface|class|enum|type)\s+([A-Za-z0-9_]+)\s*\{;', r'\1 \2 {', content)
        content = re.sub(r'export\s+(interface|class|enum|type)\s+([A-Za-z0-9_]+)\s*\{;', r'export \1 \2 {', content)
        
        # Correction 2: Supprimer les points-virgules après les déclarations d'objets
        content = re.sub(r'([=:]\s*\{);', r'\1', content)
        
        # Correction 3: Corriger les points-virgules à la fin des enums
        content = re.sub(r'export\s+enum\s+([A-Za-z0-9_]+)\s*\{;', r'export enum \1 {', content)
        
        # Correction 4: Supprimer les virgules superflues à la fin des objets
        content = re.sub(r'(\s*)\},(\s*\})', r'\1}\2', content)
        content = re.sub(r'(\s*),(\s*\})', r'\1\2', content)
        
        # Correction 5: Supprimer les points-virgules superflus dans les interfaces
        content = re.sub(r'([a-zA-Z0-9_]+)\s*:\s*([a-zA-Z0-9_<>[\]]+);(\s*[},])', r'\1: \2\3', content)
        
        # Correction 6: Supprimer les points-virgules doublés
        content = re.sub(r';;', r';', content)
        
        # Correction 7: Corriger les structures de contrôle
        content = re.sub(r'if\((.*?)\)\s*\{\s*\{\s*\{\s*\{\s*\{\s*\}\}\}\}\}', r'if(\1) {', content)
        content = re.sub(r'else if\((.*?)\)\s*\{\s*\{\s*\{\s*\{\s*\{\s*\}\}\}\}\}', r'else if(\1) {', content)
        content = re.sub(r'else\s*\{\s*\{\s*\{\s*\{\s*\{\s*\}\}\}\}\}', r'else {', content)
        
        # Correction 8: Supprimer les points-virgules après les valeurs d'enum
        content = re.sub(r'([A-Z_]+)\s*=\s*([\'"][a-z_]+[\'"])(;,)', r'\1 = \2,', content)
        
        # Correction 9: Corriger les points-virgules dans les déclarations de méthodes
        content = re.sub(r'(private|public|protected)\s+([a-zA-Z0-9_]+)\s*\(\s*\):\s*void\s*\{;', r'\1 \2(): void {', content)
        
        # Correction 10: Corriger les fonctions fléchées
        content = re.sub(r'=\s*\(\):\s*void\s*=>\s*\{,', r'= (): void => {', content)
        
        # Correction 11: Corriger les expressions de qualité dans NetworkStats
        content = re.sub(r'quality;', r'quality', content)
        
        # Correction 12: Supprimer les points-virgules à la fin des types
        content = re.sub(r'type\s+([A-Za-z0-9_]+)\s*=\s*(.+?);(\s*[;])', r'type \1 = \2\3', content)
        
        # Correction 13: Supprimer les virgules après les fonctions fléchées
        content = re.sub(r'(\(\)\s*=>\s*\{[^}]*\}),', r'\1', content)
        
        # Correction 14: Corriger les fonctions avec points-virgules multiples
        content = re.sub(r'(\([^)]*\));\s*{', r'\1 {', content)
        
        # Correction 15: Supprimer les points-virgules superflus après les destructurations
        content = re.sub(r'const\s*{\s*([^}]+)\s*}\s*=\s*([^;]+);;', r'const { \1 } = \2;', content)
        
        # Correction 16: Corriger les erreurs liées à la déstructuration
        content = re.sub(r'strategy;(\s*[}])', r'strategy\1', content)
        
        # Correction 17: Corriger les expressions incorrectes avec =
        content = re.sub(r'strategy\s*=\s*==', r'strategy ===', content)
        
        # Correction 18: Corriger les arguments des fonctions qui ont des points-virgules
        content = re.sub(r'onClick\s*=\s*{\s*\(\)\s*=>\s*([^}]+?);\s*}', r'onClick = {() => \1}', content)
        
        # Correction 19: Traiter les JSX incorrects avec des accolades
        content = re.sub(r'<([a-zA-Z]+)[^>]*>\s*\{\s*\{\s*\{\s*,?\s*\}\}\}\s*</\1>', r'<\1></\1>', content)
        
        # Correction 20: Corriger les attributs JSX incorrects
        content = re.sub(r'(\w+)\s*=\s*\{([^{}]+?);,?\}', r'\1={\2}', content)
        
        # Correction 21: Supprimer les points-virgules dans les accolades JSX
        content = re.sub(r'{\s*([^};{}]+);?\s*}', r'{\1}', content)
        
        # Correction 22: Corriger les attributs JSX avec points-virgules
        content = re.sub(r'(\w+)="([^"]*?);"', r'\1="\2"', content)
        
        # Correction 23: Ajouter des crochets pour les destructurations
        if 'const {' in content and '}  =' in content:
            content = re.sub(r'const\s+{([^}]+)}\s+=\s+useAdaptiveBandwidth\(\)', r'const { \1 } = useAdaptiveBandwidth()', content)
        
        # Correction 24: Corriger les erreurs dans App.tsx
        if 'App.tsx' in file_path:
            # Corriger l'erreur de déstructuration spécifique à App.tsx
            content = re.sub(r'const\s+{(\s*networkQuality,\s*isOffline,\s*networkStats,\s*strategy;)\s*}\s*=\s*useAdaptiveBandwidth\(\)', 
                            r'const { networkQuality, isOffline, networkStats, strategy } = useAdaptiveBandwidth()', content)
            
            # Corriger l'erreur d'opérateur d'égalité
            content = re.sub(r'strategy\s*=\s*==', r'strategy ===', content)
            
            # Supprimer les virgules superflues dans les expressions JSX
            content = re.sub(r'onLogin\s*=\s*{\s*handleLogin\s*,\s*}', r'onLogin={handleLogin}', content)
            
            # Corriger les erreurs d'accolades JSX
            content = re.sub(r'\{\s*showNetworkDetails\s*\?\s*\'([^\']+)\'(\s*,)?\s*:\s*\'([^\']+)\'(\s*,)?\s*\}', 
                            r'{showNetworkDetails ? "\1" : "\3"}', content)
        
        # Si le contenu a été modifié, écrire les modifications dans le fichier
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            if verbose:
                print(f"Corrigé: {file_path}")
            
            corrected_files += 1
            return True
        else:
            if verbose:
                print(f"Aucune correction nécessaire pour: {file_path}")
        
        return False
    except Exception as e:
        print(f"Erreur lors de la correction de {file_path}: {e}")
        return False

def find_typescript_files(directory, exclude_node_modules=True):
    """Trouve tous les fichiers TypeScript dans le répertoire."""
    typescript_files = []
    
    # Patterns pour les fichiers TypeScript
    patterns = ['**/*.ts', '**/*.tsx']
    
    for pattern in patterns:
        try:
            matches = glob.glob(os.path.join(directory, pattern), recursive=True)
            
            # Exclure les fichiers node_modules si demandé
            if exclude_node_modules:
                matches = [f for f in matches if 'node_modules' not in f]
            
            typescript_files.extend(matches)
        except Exception as e:
            print(f"Erreur lors de la recherche de fichiers avec pattern {pattern}: {e}")
    
    return typescript_files

def process_directory(directory, exclude_node_modules=True, max_workers=None, batch_size=500):
    """Traite tous les fichiers TypeScript dans le répertoire spécifié."""
    global total_files
    
    # Trouver tous les fichiers TypeScript
    typescript_files = find_typescript_files(directory, exclude_node_modules)
    total_files = len(typescript_files)
    
    if verbose:
        print(f"Trouvé {total_files} fichiers TypeScript dans {directory}")
    
    # Traiter en priorité App.tsx s'il existe
    app_tsx_files = [f for f in typescript_files if "App.tsx" in f]
    other_files = [f for f in typescript_files if "App.tsx" not in f]
    
    # Réorganiser la liste pour traiter App.tsx en priorité
    typescript_files = app_tsx_files + other_files
    
    # Traiter les fichiers par lots pour éviter les timeouts
    for i in range(0, len(typescript_files), batch_size):
        batch = typescript_files[i:i+batch_size]
        
        if verbose and len(typescript_files) > batch_size:
            print(f"Traitement du lot {i//batch_size + 1}/{(len(typescript_files) + batch_size - 1)//batch_size}")
        
        # Utiliser un pool de threads pour traiter les fichiers en parallèle
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Soumettre les tâches au pool
            futures = [executor.submit(fix_typescript_file, file_path) for file_path in batch]
            
            # Attendre la fin de toutes les tâches
            for j, future in enumerate(concurrent.futures.as_completed(futures)):
                if verbose and (j + 1) % 100 == 0 and len(batch) > 100:
                    print(f"Progression: {j + 1}/{len(batch)} fichiers traités dans le lot actuel")

def process_single_file(file_path):
    """Traite un seul fichier TypeScript."""
    global total_files
    total_files = 1
    
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas.")
        return
    
    if verbose:
        print(f"Traitement du fichier {file_path}")
    
    fix_typescript_file(file_path)

def main():
    global verbose
    
    # Configurer l'analyseur d'arguments
    parser = argparse.ArgumentParser(description="Corriger les erreurs de syntaxe TypeScript.")
    parser.add_argument("paths", nargs="+", help="Fichiers ou répertoires à traiter")
    parser.add_argument("--exclude-node-modules", action="store_true", help="Exclure les fichiers node_modules")
    parser.add_argument("--verbose", action="store_true", help="Mode verbeux")
    parser.add_argument("--threads", type=int, default=4, help="Nombre maximum de threads à utiliser (défaut: 4)")
    parser.add_argument("--batch-size", type=int, default=500, help="Nombre de fichiers à traiter par lot (défaut: 500)")
    
    # Analyser les arguments
    args = parser.parse_args()
    
    # Définir le mode verbeux
    verbose = args.verbose
    
    # Mesurer le temps d'exécution
    start_time = time.time()
    
    # Traiter chaque chemin spécifié
    for path in args.paths:
        if os.path.exists(path):
            if os.path.isdir(path):
                process_directory(path, args.exclude_node_modules, args.threads, args.batch_size)
            else:
                process_single_file(path)
        else:
            print(f"Le chemin {path} n'existe pas.")
    
    # Calculer le temps écoulé
    elapsed_time = time.time() - start_time
    
    # Afficher un résumé
    print("\nRésumé:")
    print(f"- {corrected_files}/{total_files} fichiers TypeScript corrigés")
    print(f"- Temps d'exécution: {elapsed_time:.2f} secondes")

if __name__ == "__main__":
    main()
