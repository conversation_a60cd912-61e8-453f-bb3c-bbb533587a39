# Intégration des Microservices

Ce document explique comment les microservices Agent-RB, superagent et Agent IA sont intégrés et comment ils communiquent entre eux.

## Architecture des Microservices

L'architecture se compose de trois microservices principaux :

1. **Agent-RB** (anciennement `src`) : Service principal pour la gestion des retraites et des partenaires
2. **superagent** : Service d'intelligence artificielle pour l'orchestration des agents
3. **Agent IA** : Service d'intelligence artificielle spécialisé

Ces microservices sont déployés dans des conteneurs Docker et orchestrés avec Kubernetes.

## Communication entre les Microservices

Les microservices communiquent entre eux via des API REST. Chaque microservice expose des endpoints que les autres peuvent appeler.

### Endpoints Exposés

#### Agent-RB (port 5000)
- `/api/retreats` : Gestion des retraites
- `/api/partners` : Gestion des partenaires
- `/api/workflows/start` : Démarrage de workflows (communique avec superagent)
- `/api/analyze` : Analyse de texte (communique avec Agent IA)

#### superagent (port 5001)
- `/workflows/start` : Démarrage de workflows
- `/workflows/status` : Statut des workflows
- `/agents/execute` : Exécution d'agents

#### Agent IA (port 5002)
- `/analyze` : Analyse de texte
- `/generate` : Génération de réponses
- `/recommendations` : Recommandations personnalisées

### Variables d'Environnement

Chaque microservice utilise des variables d'environnement pour configurer les URLs des autres services :

#### Agent-RB
- `SUPERAGENT_SERVICE_URL` : URL du service superagent
- `AGENT_IA_SERVICE_URL` : URL du service Agent IA

#### superagent
- `AGENT_RB_SERVICE_URL` : URL du service Agent-RB
- `AGENT_IA_SERVICE_URL` : URL du service Agent IA

#### Agent IA
- `AGENT_RB_SERVICE_URL` : URL du service Agent-RB
- `SUPERAGENT_SERVICE_URL` : URL du service superagent

## Déploiement

### Développement Local

Pour le développement local, utilisez Docker Compose :

```bash
docker-compose up
```

Cela démarrera tous les microservices et les services partagés (PostgreSQL, Redis).

### Déploiement sur Kubernetes

Pour déployer sur Kubernetes, utilisez le script `deploy-k8s.sh` :

```bash
./deploy-k8s.sh
```

Ce script effectue les actions suivantes :
1. Construit les images Docker pour chaque microservice
2. Applique les configurations Kubernetes
3. Déploie les microservices
4. Configure l'Ingress pour exposer les services

## Exemples de Communication

### Agent-RB → superagent

```javascript
// Dans Agent-RB
const startWorkflow = async (data) => {
  try {
    const response = await axios.post(
      `${process.env.SUPERAGENT_SERVICE_URL}/workflows/start`,
      data
    );
    return response.data;
  } catch (error) {
    console.error('Error starting workflow:', error);
    throw error;
  }
};
```

### Agent-RB → Agent IA

```javascript
// Dans Agent-RB
const analyzeText = async (text) => {
  try {
    const response = await axios.post(
      `${process.env.AGENT_IA_SERVICE_URL}/analyze`,
      { text }
    );
    return response.data;
  } catch (error) {
    console.error('Error analyzing text:', error);
    throw error;
  }
};
```

### superagent → Agent IA

```javascript
// Dans superagent
const generateResponse = async (prompt) => {
  try {
    const response = await axios.post(
      `${process.env.AGENT_IA_SERVICE_URL}/generate`,
      { prompt }
    );
    return response.data;
  } catch (error) {
    console.error('Error generating response:', error);
    throw error;
  }
};
```

### Agent IA → Agent-RB

```javascript
// Dans Agent IA
const getRetreats = async (filters) => {
  try {
    const response = await axios.get(
      `${process.env.AGENT_RB_SERVICE_URL}/api/retreats`,
      { params: filters }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching retreats:', error);
    throw error;
  }
};
```

## Surveillance et Logging

Tous les microservices exposent des endpoints de santé :
- `/health` : Vérifie l'état général du service
- `/ready` : Vérifie si le service est prêt à recevoir des requêtes

## Dépannage

### Problèmes Courants

1. **Les services ne peuvent pas communiquer entre eux**
   - Vérifier que les noms de service sont corrects
   - Vérifier que les ports sont correctement configurés
   - Vérifier que les services sont dans le même namespace

2. **Les pods ne démarrent pas**
   - Vérifier les logs des pods : `kubectl logs <pod-name>`
   - Vérifier les événements : `kubectl get events`

3. **Erreurs d'authentification**
   - Vérifier que les secrets sont correctement configurés
   - Vérifier que les variables d'environnement sont correctement définies
