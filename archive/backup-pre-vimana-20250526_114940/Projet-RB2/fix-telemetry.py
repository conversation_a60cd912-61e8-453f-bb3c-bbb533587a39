#!/usr/bin/env python3

"""
Script pour corriger spécifiquement les fichiers de télémétrie problématiques
(openTelemetry.ts et unifiedMonitoring.ts)
"""

import os
import re
import sys

# Fichiers cibles
TARGET_FILES = [
    "mobile/src/utils/openTelemetry.ts",
    "mobile/src/utils/unifiedMonitoring.ts"
]

BACKUP_SUFFIX = ".bak"

def backup_file(file_path):
    """Créer une sauvegarde du fichier."""
    backup_path = f"{file_path}{BACKUP_SUFFIX}"
    try:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as source:
            content = source.read()
            with open(backup_path, 'w', encoding='utf-8') as target:
                target.write(content)
        return True
    except Exception as e:
        print(f"Erreur lors de la sauvegarde de {file_path}: {e}")
        return False

def fix_telemetry_file(file_path):
    """Corriger un fichier de télémétrie spécifique."""
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas.")
        return False
        
    print(f"Correction de {file_path}...")
    
    # Créer une sauvegarde
    if not backup_file(file_path):
        return False
        
    try:
        # Lire le fichier
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
            
        # Appliquer les corrections
        
        # 1. Corriger les guillemets autour des noms de propriétés et paramètres
        content = re.sub(r'"([a-zA-Z0-9_]+)":\s*', r'\1: ', content)
        
        # 2. Corriger les points-virgules après les accolades
        content = re.sub(r'}\s*;', r'}', content)
        
        # 3. Corriger les déclarations d'enum
        content = re.sub(r'(export\s+enum\s+[a-zA-Z0-9_]+\s*){;', r'\1{', content)
        
        # 4. Corriger les interfaces
        content = re.sub(r'(export\s+interface\s+[a-zA-Z0-9_]+\s*){;', r'\1{', content)
        
        # 5. Corriger les classes
        content = re.sub(r'(export\s+class\s+[a-zA-Z0-9_]+\s*){;', r'\1{', content)
        
        # 6. Simplifier les accolades excessives dans les blocs conditionnels
        content = re.sub(r'{\s*{\s*{\s*{\s*{\s*}\s*}\s*}\s*}\s*}', r'{ return; }', content)
        
        # 7. Corriger les méthodes async
        content = re.sub(r'(private|public)\s+async\s+([a-zA-Z0-9_]+)\s*\(([^)]*)\):\s*Promise<([^>]+)>\s*{',
                        r'\1 async \2(\3): Promise<\4> {', content)
        
        # 8. Corriger les parenthèses doubles
        content = re.sub(r'if\s*\(([^)]+)\)\s*\)', r'if(\1)', content)
        
        # 9. Corriger la fonction withTelemetry
        content = re.sub(r'export\s+async\s+function\s+withTelemetry<T>\(\s*"([^"]+)"',
                        r'export async function withTelemetry<T>(\1', content)
        
        # 10. Corriger les chaînes de caractères dans les objets
        content = re.sub(r':\s*{(\s*)"([a-zA-Z0-9_]+)":', r': {\1\2:', content)
        
        # 11. Corriger les méthodes avec paramètres entre guillemets
        content = re.sub(r'public\s+([a-zA-Z0-9_]+)\s*\(\s*"([^"]+)"\s*:',
                        r'public \1(\2:', content)
        content = re.sub(r'private\s+([a-zA-Z0-9_]+)\s*\(\s*"([^"]+)"\s*:',
                        r'private \1(\2:', content)
        
        # 12. Corriger les paramètres suivants avec guillemets
        content = re.sub(r',\s*"([^"]+)"\s*:', r', \1:', content)
        
        # 13. Corriger les appels de fonctions bizarres
        content = re.sub(r'(\w+)\(\)\(\)', r'\1()', content)
        
        # 14. Corriger les opérateurs d'égalité mal formés
        content = re.sub(r'=\s*==', r' ===', content)
        
        # Écrire le contenu corrigé
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
        print(f"✅ Correction terminée pour {file_path}")
        return True
    except Exception as e:
        print(f"❌ Erreur lors de la correction de {file_path}: {e}")
        return False

def main():
    """Fonction principale."""
    success_count = 0
    
    for file_path in TARGET_FILES:
        if fix_telemetry_file(file_path):
            success_count += 1
    
    print(f"\nRésumé: {success_count}/{len(TARGET_FILES)} fichiers corrigés.")

if __name__ == "__main__":
    main() 