phases:
  pre_deployment:
    - name: security_scan
      script: ./security-audit.sh
      timeout: 30m
      
    - name: backup
      script: ./backup-data.sh
      timeout: 1h
      
    - name: compliance_check
      script: ./compliance-check.sh
      timeout: 15m

  deployment:
    strategy: blue_green
    rollback_threshold:
      error_rate: 1%
      response_time: 500ms
      
    steps:
      - name: deploy_new_version
        timeout: 10m
        
      - name: health_check
        timeout: 5m
        
      - name: smoke_test
        timeout: 10m
        
      - name: switch_traffic
        timeout: 5m

  post_deployment:
    - name: performance_test
      script: ./performance-test.sh
      timeout: 30m
      
    - name: security_validation
      script: ./security-validation.sh
      timeout: 15m
      
    - name: metrics_validation
      script: ./validate-metrics.sh
      timeout: 10m

monitoring:
  metrics:
    - response_time
    - error_rate
    - resource_usage
    - security_events
    
  alerts:
    - type: slack
      channel: "#deployments"
    - type: email
      recipients: ["<EMAIL>"]

rollback:
  automatic: true
  conditions:
    - error_rate > 1%
    - response_time > 500ms
    - security_score < 90