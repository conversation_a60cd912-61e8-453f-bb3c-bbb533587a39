#!/bin/bash
set -e

# Configuration
NAMESPACE="retreatandbe-production"
REPORT_DIR="./reports/phase5"
DATE=$(date +%Y-%m-%d)

# Création du répertoire de rapports
mkdir -p "$REPORT_DIR"

# 1. Exécution des tests de sécurité
echo "🔒 Lancement des tests de sécurité..."
./security-audit.sh "$NAMESPACE" "$REPORT_DIR"

# 2. Optimisation des ressources
echo "⚡ Optimisation des ressources..."
./optimize-resources.sh "$NAMESPACE"

# 3. Vérification de la conformité
echo "📋 Vérification de la conformité..."
./compliance-check.sh "$NAMESPACE" "$REPORT_DIR"

# 4. Mise à jour des métriques
echo "📊 Mise à jour des métriques..."
./update-metrics.sh "$NAMESPACE"

# 5. Génération du rapport final
echo "📝 Génération du rapport final..."
cat << EOF > "$REPORT_DIR/phase5-summary-${DATE}.md"
# Rapport d'Exécution Phase 5 - ${DATE}

## Résumé des Actions
- Tests de sécurité complétés
- Optimisation des ressources effectuée
- Vérification de la conformité terminée
- Métriques mises à jour

## Prochaines Actions
1. Revue des rapports de sécurité
2. Validation des optimisations
3. Suivi des points de conformité
4. Analyse des métriques

## Statut Global
✅ Phase 5 déployée avec succès
EOF

echo "✅ Phase 5 terminée avec succès!"