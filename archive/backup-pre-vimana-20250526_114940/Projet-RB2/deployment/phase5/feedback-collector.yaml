apiVersion: apps/v1
kind: Deployment
metadata:
  name: feedback-collector
  namespace: retreatandbe-production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: feedback-collector
  template:
    metadata:
      labels:
        app: feedback-collector
    spec:
      containers:
      - name: feedback-collector
        image: feedback-collector:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: SLACK_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: feedback-secrets
              key: slack-webhook
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 256Mi