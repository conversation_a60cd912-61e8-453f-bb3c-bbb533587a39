#!/bin/bash

# Script d'audit de sécurité

set -e

NAMESPACE="retreatandbe-production"
REPORT_DIR="./security-reports/$(date +%Y-%m-%d)"

# Créer le répertoire pour les rapports
mkdir -p $REPORT_DIR

echo "Démarrage de l'audit de sécurité..."

# Audit des images Docker
echo "Audit des images Docker avec Trivy..."
for deployment in $(kubectl get deployments -n $NAMESPACE -o jsonpath='{.items[*].metadata.name}'); do
  IMAGE=$(kubectl get deployment $deployment -n $NAMESPACE -o jsonpath='{.spec.template.spec.containers[0].image}')
  echo "Analyse de l'image: $IMAGE"
  trivy image --format json --output "$REPORT_DIR/$deployment-trivy.json" $IMAGE
done

# Audit des configurations Kubernetes
echo "Audit des configurations Kubernetes avec kube-bench..."
kube-bench --json --outputfile "$REPORT_DIR/kube-bench.json"

# Vérification des secrets exposés
echo "Vérification des secrets exposés..."
kubectl get secrets -n $NAMESPACE -o json > "$REPORT_DIR/secrets-audit.json"

# Scan des vulnérabilités réseau
echo "Scan des vulnérabilités réseau..."
nmap -sV -oX "$REPORT_DIR/nmap-scan.xml" $(kubectl get services -n $NAMESPACE -o jsonpath='{.items[*].status.loadBalancer.ingress[0].ip}')

# Génération du rapport de synthèse
echo "Génération du rapport de synthèse..."
cat << EOF > "$REPORT_DIR/summary.md"
# Rapport d'audit de sécurité - $(date +%Y-%m-%d)

## Résumé des analyses

### Images Docker
- Nombre d'images analysées: $(ls $REPORT_DIR/*-trivy.json | wc -l)
- Voir les rapports détaillés dans les fichiers *-trivy.json

### Configuration Kubernetes
- Voir le rapport kube-bench.json pour les détails

### Secrets
- Nombre de secrets: $(jq '.items | length' "$REPORT_DIR/secrets-audit.json")

### Vulnérabilités réseau
- Voir nmap-scan.xml pour les détails

## Recommandations
1. Mettre à jour les images avec des vulnérabilités critiques
2. Vérifier les configurations de sécurité Kubernetes
3. Revoir les permissions des secrets
4. Surveiller les ports exposés
EOF

echo "Audit de sécurité terminé. Rapports disponibles dans $REPORT_DIR"