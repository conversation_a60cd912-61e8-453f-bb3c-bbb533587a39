#!/bin/bash

# Script de déploiement canary en production

set -e

NAMESPACE="retreatandbe-production"
SERVICE=$1
NEW_VERSION=$2
CANARY_WEIGHT=$3

if [ -z "$SERVICE" ] || [ -z "$NEW_VERSION" ]; then
  echo "Erreur: Veuillez spécifier un service et une version"
  echo "Usage: $0 <service> <new-version> [canary-weight]"
  echo "Services disponibles: backend, frontend, analyzer, decentralized-storage"
  exit 1
fi

# Définir le poids du canary par défaut si non spécifié
if [ -z "$CANARY_WEIGHT" ]; then
  CANARY_WEIGHT=20
fi

echo "Déploiement canary de $SERVICE version $NEW_VERSION avec poids $CANARY_WEIGHT%..."

# Créer le déploiement canary
kubectl apply -f - <<EOF
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: $SERVICE
  namespace: $NAMESPACE
spec:
  hosts:
  - $SERVICE.$NAMESPACE.svc.cluster.local
  http:
  - route:
    - destination:
        host: $SERVICE
        subset: stable
      weight: $((100 - $CANARY_WEIGHT))
    - destination:
        host: $SERVICE
        subset: canary
      weight: $CANARY_WEIGHT
EOF

# Déployer la nouvelle version comme canary
helm upgrade --install $SERVICE-canary ../charts/$SERVICE \
  --set image.tag=$NEW_VERSION \
  --set service.name=$SERVICE \
  --set deployment.labels.version=canary \
  -f production-values.yaml \
  -n $NAMESPACE

echo "Déploiement canary terminé. Surveillez les métriques pour valider la nouvelle version."
echo "Pour promouvoir le canary à 100%, exécutez: $0 $SERVICE $NEW_VERSION 100"
echo "Pour annuler le canary, exécutez: ./rollback.sh $SERVICE"