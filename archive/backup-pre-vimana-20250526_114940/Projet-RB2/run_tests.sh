#!/bin/bash
# Script pour exécuter les tests

# Définir la variable d'environnement pour le mode développement
export ENVIRONMENT=development

# Exécuter les tests unitaires
echo "=== Exécution des tests unitaires ==="
python -m pytest tests/models tests/database -v

# Exécuter les tests d'API
echo -e "\n\n=== Exécution des tests d'API ==="
python -m pytest tests/api -v

# Exécuter les tests de notification
echo -e "\n\n=== Exécution des tests de notification ==="
python -m pytest tests/notifications -v

# Exécuter tous les tests avec couverture
echo -e "\n\n=== Exécution de tous les tests avec couverture ==="
python -m pytest --cov=src tests/
