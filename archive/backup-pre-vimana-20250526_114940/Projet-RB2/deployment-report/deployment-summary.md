# Rapport de déploiement - Projet RB2

**Date :** 2025-03-19 13:54:06

## Vue d'ensemble

Ce rapport présente un résumé détaillé du déploiement de l'architecture microservices du projet RB2.

## Table des matières

1. [État du cluster](#état-du-cluster)
2. [Services déployés](#services-déployés)
3. [Métriques de surveillance](#métriques-de-surveillance)
4. [Configuration réseau](#configuration-r<PERSON>eau)
5. [Problèmes connus](#problèmes-connus)
6. [Recommandations](#recommandations)
7. [Documentation des services](#documentation-des-services)

## État du cluster

### Information du cluster

```
[0;32mKubernetes control plane[0m is running at [0;33mhttps://127.0.0.1:6443[0m
[0;32mCoreDNS[0m is running at [0;33mhttps://127.0.0.1:6443/api/v1/namespaces/kube-system/services/kube-dns:dns/proxy[0m

To further debug and diagnose cluster problems, use 'kubectl cluster-info dump'.
```

### Namespaces

```
NAME              STATUS   AGE
cert-manager      Active   168m
default           Active   27h
istio-operator    Active   20h
istio-system      Active   19h
kube-node-lease   Active   27h
kube-public       Active   27h
kube-system       Active   27h
observability     Active   169m
retreat-and-be    Active   21h
storage           Active   27h
```

### Distribution des pods

| Namespace | Pods en exécution | Pods défaillants |
|-----------|-------------------|------------------|
| cert-manager | 3 |        0 |
| default | 0 |        0 |
| istio-operator | 1 |        0 |
| istio-system | 3 |        0 |
| kube-node-lease | 0 |        0 |
| kube-public | 0 |        0 |
| kube-system | 9 |        0 |
| observability | 0 |        1 |
| retreat-and-be | 40 |        0 |
| storage | 0 |        2 |

## Services déployés

### Services dans le namespace retreat-and-be

```
NAME                                          TYPE        CLUSTER-IP       EXTERNAL-IP   PORT(S)     AGE   SELECTOR
ai-agent                                      ClusterIP   **************   <none>        8080/TCP    44m   app=ai-agent
analyzer                                      ClusterIP   ************     <none>        8080/TCP    44m   app=analyzer
backend-backend                               ClusterIP   **************   <none>        5000/TCP    20h   app=backend-backend
backend-postgresql                            ClusterIP   **************   <none>        5432/TCP    20h   app.kubernetes.io/component=primary,app.kubernetes.io/instance=backend,app.kubernetes.io/name=postgresql
backend-postgresql-hl                         ClusterIP   None             <none>        5432/TCP    20h   app.kubernetes.io/component=primary,app.kubernetes.io/instance=backend,app.kubernetes.io/name=postgresql
backend-redis-headless                        ClusterIP   None             <none>        6379/TCP    20h   app.kubernetes.io/instance=backend,app.kubernetes.io/name=redis
backend-redis-master                          ClusterIP   **************   <none>        6379/TCP    20h   app.kubernetes.io/component=master,app.kubernetes.io/instance=backend,app.kubernetes.io/name=redis
backend-redis-replicas                        ClusterIP   ************     <none>        6379/TCP    20h   app.kubernetes.io/component=replica,app.kubernetes.io/instance=backend,app.kubernetes.io/name=redis
decentralized-storage-decentralized-storage   ClusterIP   ************     <none>        8080/TCP    21h   app=decentralized-storage-decentralized-storage
financial-management                          ClusterIP   ************     <none>        80/TCP      21h   app=financial-management
flight-finder-flight-finder                   ClusterIP   **************   <none>        80/TCP      20h   app.kubernetes.io/instance=flight-finder,app.kubernetes.io/name=flight-finder-flight-finder
flight-finder-redis-headless                  ClusterIP   None             <none>        6379/TCP    20h   app.kubernetes.io/instance=flight-finder,app.kubernetes.io/name=redis
flight-finder-redis-master                    ClusterIP   **************   <none>        6379/TCP    20h   app.kubernetes.io/component=master,app.kubernetes.io/instance=flight-finder,app.kubernetes.io/name=redis
frontend-frontend                             ClusterIP   ***********      <none>        3000/TCP    20h   app=frontend-frontend
grafana                                       ClusterIP   **************   <none>        3000/TCP    18h   app=grafana
keycloak-postgresql                           ClusterIP   *************    <none>        5432/TCP    21h   app.kubernetes.io/component=primary,app.kubernetes.io/instance=keycloak,app.kubernetes.io/name=postgresql
keycloak-postgresql-hl                        ClusterIP   None             <none>        5432/TCP    21h   app.kubernetes.io/component=primary,app.kubernetes.io/instance=keycloak,app.kubernetes.io/name=postgresql
messaging-service                             ClusterIP   **************   <none>        80/TCP      21h   app=messaging-service
prometheus                                    ClusterIP   **************   <none>        9090/TCP    18h   app=prometheus
retreat-pro-matcher                           ClusterIP   **************   <none>        80/TCP      21h   app=retreat-pro-matcher
retreat-stream                                ClusterIP   **************   <none>        80/TCP      21h   app=retreat-stream
security                                      ClusterIP   *************    <none>        8080/TCP    44m   app=security
security-service                              ClusterIP   ************     <none>        80/TCP      21h   app=security-service
social                                        ClusterIP   *************    <none>        80/TCP      21h   app=social
social-platform-video                         ClusterIP   ************     <none>        80/TCP      20h   app=social-platform-video
social-platform-video-mongodb                 ClusterIP   ************     <none>        27017/TCP   20h   app.kubernetes.io/component=mongodb,app.kubernetes.io/instance=social-platform-video,app.kubernetes.io/name=mongodb
social-platform-video-redis-headless          ClusterIP   None             <none>        6379/TCP    20h   app.kubernetes.io/instance=social-platform-video,app.kubernetes.io/name=redis
social-platform-video-redis-master            ClusterIP   ***********      <none>        6379/TCP    20h   app.kubernetes.io/component=master,app.kubernetes.io/instance=social-platform-video,app.kubernetes.io/name=redis
social-platform-video-redis-replicas          ClusterIP   *************    <none>        6379/TCP    20h   app.kubernetes.io/component=replica,app.kubernetes.io/instance=social-platform-video,app.kubernetes.io/name=redis
transport-booking                             ClusterIP   **************   <none>        80/TCP      20h   app=transport-booking
transport-booking-postgresql                  ClusterIP   ************     <none>        5432/TCP    20h   app.kubernetes.io/component=primary,app.kubernetes.io/instance=transport-booking,app.kubernetes.io/name=postgresql
transport-booking-postgresql-hl               ClusterIP   None             <none>        5432/TCP    20h   app.kubernetes.io/component=primary,app.kubernetes.io/instance=transport-booking,app.kubernetes.io/name=postgresql
transport-booking-redis-headless              ClusterIP   None             <none>        6379/TCP    20h   app.kubernetes.io/instance=transport-booking,app.kubernetes.io/name=redis
transport-booking-redis-master                ClusterIP   *************    <none>        6379/TCP    20h   app.kubernetes.io/component=master,app.kubernetes.io/instance=transport-booking,app.kubernetes.io/name=redis
transport-booking-redis-replicas              ClusterIP   **************   <none>        6379/TCP    20h   app.kubernetes.io/component=replica,app.kubernetes.io/instance=transport-booking,app.kubernetes.io/name=redis
vr                                            ClusterIP   ************     <none>        80/TCP      20h   app=vr-service
vr-mongodb                                    ClusterIP   **************   <none>        27017/TCP   20h   app.kubernetes.io/component=mongodb,app.kubernetes.io/instance=vr,app.kubernetes.io/name=mongodb
vr-redis-headless                             ClusterIP   None             <none>        6379/TCP    20h   app.kubernetes.io/instance=vr,app.kubernetes.io/name=redis
vr-redis-master                               ClusterIP   *************    <none>        6379/TCP    20h   app.kubernetes.io/component=master,app.kubernetes.io/instance=vr,app.kubernetes.io/name=redis
vr-redis-replicas                             ClusterIP   **************   <none>        6379/TCP    20h   app.kubernetes.io/component=replica,app.kubernetes.io/instance=vr,app.kubernetes.io/name=redis
web3-nft-service                              ClusterIP   *************    <none>        80/TCP      21h   app=web3-nft-service
website-creator                               ClusterIP   *************    <none>        80/TCP      20h   app=website-creator
website-creator-postgresql                    ClusterIP   *************    <none>        5432/TCP    20h   app.kubernetes.io/component=primary,app.kubernetes.io/instance=website-creator,app.kubernetes.io/name=postgresql
website-creator-postgresql-hl                 ClusterIP   None             <none>        5432/TCP    20h   app.kubernetes.io/component=primary,app.kubernetes.io/instance=website-creator,app.kubernetes.io/name=postgresql
website-creator-redis-headless                ClusterIP   None             <none>        6379/TCP    20h   app.kubernetes.io/instance=website-creator,app.kubernetes.io/name=redis
website-creator-redis-master                  ClusterIP   *************    <none>        6379/TCP    20h   app.kubernetes.io/component=master,app.kubernetes.io/instance=website-creator,app.kubernetes.io/name=redis
website-creator-redis-replicas                ClusterIP   *************    <none>        6379/TCP    20h   app.kubernetes.io/component=replica,app.kubernetes.io/instance=website-creator,app.kubernetes.io/name=redis
```

### État des déploiements

```
NAME                                          READY   UP-TO-DATE   AVAILABLE   AGE   CONTAINERS              IMAGES                    SELECTOR
ai-agent                                      1/1     1            1           44m   ai-agent                nginx:alpine              app=ai-agent
analyzer                                      1/1     1            1           44m   analyzer                nginx:alpine              app=analyzer
backend-backend                               1/1     1            1           20h   backend                 nginx:alpine              app.kubernetes.io/instance=backend,app.kubernetes.io/name=backend-backend
decentralized-storage-decentralized-storage   0/0     0            0           21h   decentralized-storage   nginx:alpine              app=decentralized-storage-decentralized-storage
financial-management                          0/0     0            0           21h   financial-management    nginx:alpine              app=financial-management
financial-service                             1/1     1            1           20h   financial-service       nginx:alpine              app=financial-service
flight-finder-flight-finder                   1/1     1            1           20h   flight-finder           nginx:alpine              app.kubernetes.io/instance=flight-finder,app.kubernetes.io/name=flight-finder-flight-finder
frontend-frontend                             1/1     1            1           20h   frontend                nginx:alpine              app=frontend-frontend
grafana                                       1/1     1            1           18h   grafana                 nginx:alpine              app=grafana
keycloak                                      1/1     1            1           20h   keycloak                nginx:alpine              app=keycloak
messaging-service                             1/1     1            1           21h   messaging-service       nginx:alpine              app=messaging-service
prometheus                                    1/1     1            1           18h   prometheus              prom/prometheus:v2.45.0   app=prometheus
retreat-pro-matcher                           0/0     0            0           21h   retreat-pro-matcher     nginx:alpine              app=retreat-pro-matcher
retreat-stream                                0/0     0            0           21h   retreat-stream          nginx:alpine              app=retreat-stream
security                                      1/1     1            1           44m   security                nginx:alpine              app=security
security-service                              1/1     1            1           21h   security-service        nginx:alpine              app=security-service
social                                        0/0     0            0           21h   social                  nginx:alpine              app=social
social-platform-video                         0/0     0            0           20h   social-platform-video   nginx:alpine              app=social-platform-video
social-platform-video-mongodb                 0/0     0            0           20h   mongodb                 nginx:alpine              app.kubernetes.io/component=mongodb,app.kubernetes.io/instance=social-platform-video,app.kubernetes.io/name=mongodb
transport-booking                             1/1     1            1           20h   transport-booking       nginx:alpine              app=transport-booking
vr                                            0/0     0            0           20h   vr-service              nginx:alpine              app=vr-service
vr-mongodb                                    0/0     0            0           20h   mongodb                 nginx:alpine              app.kubernetes.io/component=mongodb,app.kubernetes.io/instance=vr,app.kubernetes.io/name=mongodb
web3-nft-service                              0/0     0            0           21h   web3-nft-service        nginx:alpine              app=web3-nft-service
website-creator                               1/1     1            1           20h   website-creator         nginx:alpine              app=website-creator
```

### Services principaux

| Service | État | Endpoints | Pods |
|---------|------|-----------|------|
| frontend-frontend | ✅ Actif | **********:3000 | ✅        1 pod(s) |
| backend-backend | ✅ Actif | : | ❌ Aucun pod trouvé |
| grafana | ✅ Actif | **********:3000 | ✅        1 pod(s) |
| prometheus | ✅ Actif | **********:9090 | ✅        1 pod(s) |
| keycloak | ❌ Non trouvé | N/A | N/A |

## Métriques de surveillance

### Top 5 des pods par utilisation CPU

```

```

### Top 5 des pods par utilisation mémoire

```

```


## Configuration réseau

### Gateway Istio

```yaml
apiVersion: v1
items:
- apiVersion: networking.istio.io/v1beta1
  kind: Gateway
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.istio.io/v1alpha3","kind":"Gateway","metadata":{"annotations":{},"name":"retreat-gateway","namespace":"retreat-and-be"},"spec":{"selector":{"istio":"ingressgateway"},"servers":[{"hosts":["*"],"port":{"name":"http","number":80,"protocol":"HTTP"}}]}}
    creationTimestamp: "2025-03-19T18:19:35Z"
    generation: 1
    name: retreat-gateway
    namespace: retreat-and-be
    resourceVersion: "92918"
    uid: 6fad25c3-5e66-4921-9d7e-8be2911c36b3
  spec:
    selector:
      istio: ingressgateway
    servers:
    - hosts:
      - '*'
      port:
        name: http
        number: 80
        protocol: HTTP
kind: List
metadata:
  resourceVersion: ""
```

### VirtualServices

```
NAME            GATEWAYS              HOSTS   AGE
ai-agent-vs     ["retreat-gateway"]   ["*"]   44m
analyzer-vs     ["retreat-gateway"]   ["*"]   44m
backend-vs      ["retreat-gateway"]   ["*"]   154m
frontend-vs     ["retreat-gateway"]   ["*"]   154m
grafana-vs      ["retreat-gateway"]   ["*"]   154m
keycloak-vs     ["retreat-gateway"]   ["*"]   154m
prometheus-vs   ["retreat-gateway"]   ["*"]   154m
security-vs     ["retreat-gateway"]   ["*"]   44m
```

### URLs d'accès

* **Frontend**: http://localhost:80/
* **Backend API**: http://localhost:80/api
* **Grafana**: http://localhost:80/grafana
* **Prometheus**: http://localhost:80/prometheus
* **Keycloak**: http://localhost:80/auth


## Problèmes connus

### Pods défaillants

✅ Aucun pod défaillant détecté.

### Services sans endpoints

- backend-backend
- decentralized-storage-decentralized-storage
- financial-management
- retreat-pro-matcher
- retreat-stream
- social
- social-platform-video
- social-platform-video-mongodb
- vr
- vr-mongodb
- web3-nft-service

### Services à implémenter selon le plan de refactoring

Selon le plan de réorganisation, les services suivants sont à implémenter ou migrer :

- ✅ analyzer (Implémenté)
- ✅ security (Implémenté)
- ✅ ai-agent (Implémenté)

## Recommandations

Sur la base de l'analyse du déploiement actuel, voici quelques recommandations :

1. **Compléter l'implémentation des services** : Finaliser le déploiement des services manquants identifiés dans le plan de refactoring (analyzer, security, ai-agent).

2. **Améliorer la configuration d'Ingress** : Résoudre les problèmes d'accès externe aux services via l'Ingress Gateway en vérifiant et en optimisant les configurations d'Istio.

3. **Mise en place de tests automatisés** : Développer et intégrer des tests automatisés pour valider le bon fonctionnement des services, en particulier après les déploiements.

4. **Optimisation des ressources** : Analyser l'utilisation des ressources et ajuster les limites et requêtes CPU/mémoire pour optimiser les performances.

5. **Sécurité** : Renforcer la sécurité des services en mettant en place des politiques d'authentification et d'autorisation appropriées avec Keycloak.

6. **Documentation continue** : Maintenir à jour la documentation des services, des endpoints API et des procédures de déploiement.

7. **Surveillance avancée** : Améliorer les tableaux de bord Grafana pour une meilleure surveillance des services et la définition d'alertes proactives.


## Documentation des services

### Architecture globale

```
Ingress Gateway (Istio)
    |
    ├── Frontend (React)
    |     └── Port: 3000
    |
    ├── Backend (Node.js)
    |     ├── Port: 5000
    |     ├── Database: PostgreSQL
    |     └── Cache: Redis
    |
    ├── Monitoring
    |     ├── Prometheus (Port: 9090)
    |     └── Grafana (Port: 3000)
    |
    └── Keycloak (Auth)
          ├── Port: 8080
          └── Database: PostgreSQL
```

### Description des services

#### Frontend (frontend-frontend)
- **Technologie** : React
- **Fonction** : Interface utilisateur principale de l'application
- **Dépendances** : Backend API
- **Port** : 3000

#### Backend (backend-backend)
- **Technologie** : Node.js
- **Fonction** : API principale de l'application
- **Dépendances** : PostgreSQL, Redis
- **Port** : 5000
- **Endpoints API** :
  -  - Gestion des utilisateurs
  -  - Gestion des retraites
  -  - Gestion des réservations

#### Prometheus
- **Fonction** : Collecte et stockage des métriques
- **Port** : 9090
- **Service Monitors** : Configuration pour scraper les métriques des services
- **Règles d'alerte** : Définition des alertes basées sur les métriques

#### Grafana
- **Fonction** : Visualisation des métriques et tableaux de bord
- **Port** : 3000
- **Dashboards** : Plusieurs tableaux de bord pour surveiller les services
- **Source de données** : Prometheus

#### Keycloak
- **Fonction** : Service d'authentification et d'autorisation
- **Port** : 8080
- **Domaine d'application** : Sécurisation des services et des API

### Procédures de maintenance

#### Redémarrage d'un service
```bash
kubectl rollout restart deployment <nom-du-service> -n retreat-and-be
```

#### Vérification des logs d'un service
```bash
kubectl logs -l app=<nom-du-service> -n retreat-and-be
```

#### Accès direct à un service (port-forward)
```bash
kubectl port-forward svc/<nom-du-service> <port-local>:<port-service> -n retreat-and-be
```

#### Mise à jour de l'image d'un service
```bash
kubectl set image deployment/<nom-du-service> <container>=<nouvelle-image> -n retreat-and-be
```


---

*Ce rapport a été généré automatiquement le 2025-03-19 13:54:06.*

Pour une vérification rapide de la santé des services, exécutez le script 'health-check.sh' inclus dans ce répertoire.
