#!/bin/bash

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonctions de logging
log_info() { echo -e "${BLUE}ℹ️ $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️ $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Vérification de tous les services principaux
SERVICES=("frontend-frontend" "backend-backend" "grafana" "prometheus" "keycloak")

log_info "Vérification de la santé des services..."
echo ""

for service in "${SERVICES[@]}"; do
    # Vérifier si le service existe
    if kubectl get service $service -n retreat-and-be &> /dev/null; then
        # Vérifier les endpoints
        ENDPOINTS=$(kubectl get endpoints $service -n retreat-and-be -o jsonpath='{.subsets[0].addresses[0].ip}' 2>/dev/null)
        if [ -z "$ENDPOINTS" ]; then
            log_error "Service $service n'a pas d'endpoints"
            continue
        fi
        
        # Vérifier les pods
        SELECTOR=$(kubectl get service $service -n retreat-and-be -o jsonpath='{.spec.selector}' | sed 's/{//g' | sed 's/}//g' | sed 's/"//g' | sed 's/:/=/g')
        PODS=$(kubectl get pods -n retreat-and-be -l $SELECTOR -o name 2>/dev/null)
        if [ -z "$PODS" ]; then
            log_error "Service $service n'a pas de pods associés"
            continue
        fi
        
        # Vérifier l'état des pods
        NOT_RUNNING=$(kubectl get pods -n retreat-and-be -l $SELECTOR -o jsonpath='{.items[?(@.status.phase!="Running")].metadata.name}')
        if [ -n "$NOT_RUNNING" ]; then
            log_warning "Service $service a des pods qui ne sont pas en état 'Running'"
        else
            log_success "Service $service est en bonne santé"
        fi
    else
        log_error "Service $service n'existe pas"
    fi
done

echo ""
log_info "Santé de l'Ingress Gateway..."
INGRESS_POD=$(kubectl get pods -n istio-system -l app=istio-ingressgateway -o name 2>/dev/null)
if [ -z "$INGRESS_POD" ]; then
    log_error "Ingress Gateway n'est pas déployé"
else
    INGRESS_RUNNING=$(kubectl get pods -n istio-system -l app=istio-ingressgateway -o jsonpath='{.items[0].status.phase}')
    if [ "$INGRESS_RUNNING" == "Running" ]; then
        log_success "Ingress Gateway est en bonne santé"
    else
        log_warning "Ingress Gateway n'est pas en état 'Running'"
    fi
fi

echo ""
log_info "Santé des VirtualServices..."
for service in "${SERVICES[@]}"; do
    VS_NAME="${service%-*}-vs"
    if kubectl get virtualservice $VS_NAME -n retreat-and-be &> /dev/null; then
        log_success "VirtualService $VS_NAME existe"
    else
        log_error "VirtualService $VS_NAME n'existe pas"
    fi
done
