// Mock pour sharp
function sharpMock(input) {
  // Créer une chaîne de fonctions mockées qui retournent this pour le chaînage
  const self = {
    resize: jest.fn().mockReturnThis(),
    rotate: jest.fn().mockReturnThis(),
    flip: jest.fn().mockReturnThis(),
    flop: jest.fn().mockReturnThis(),
    sharpen: jest.fn().mockReturnThis(),
    blur: jest.fn().mockReturnThis(),
    gamma: jest.fn().mockReturnThis(),
    grayscale: jest.fn().mockReturnThis(),
    toColorspace: jest.fn().mockReturnThis(),
    toFormat: jest.fn().mockReturnThis(),
    jpeg: jest.fn().mockReturnThis(),
    png: jest.fn().mockReturnThis(),
    webp: jest.fn().mockReturnThis(),
    tiff: jest.fn().mockReturnThis(),
    composite: jest.fn().mockReturnThis(),
    toBuffer: jest.fn().mockResolvedValue(Buffer.from('mock-image-buffer')),
    toFile: jest.fn().mockResolvedValue({ 
      width: 500, 
      height: 300, 
      size: 12345, 
      format: 'jpeg'
    }),
    metadata: jest.fn().mockResolvedValue({
      width: 1000,
      height: 800,
      format: 'jpeg',
      size: 123456
    }),
    clone: jest.fn().mockImplementation(() => {
      return self;
    })
  };
  
  return self;
}

// Ajouter des propriétés/méthodes statiques
sharpMock.cache = jest.fn().mockReturnValue(sharpMock);
sharpMock.concurrency = jest.fn().mockReturnValue(sharpMock);
sharpMock.simd = jest.fn().mockReturnValue(sharpMock);
sharpMock.format = {
  jpeg: { id: 'jpeg', input: { file: true }, output: { file: true } },
  png: { id: 'png', input: { file: true }, output: { file: true } },
  webp: { id: 'webp', input: { file: true }, output: { file: true } },
  tiff: { id: 'tiff', input: { file: true }, output: { file: true } },
  raw: { id: 'raw', input: { file: true }, output: { file: true } }
};

module.exports = sharpMock;
module.exports.default = module.exports; 