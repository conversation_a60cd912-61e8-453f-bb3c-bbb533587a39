// Mock pour MonitoringService
class MonitoringServiceMock {
  constructor() {
    this.connectionsCount = 0;
    this.lastError = null;
    this.healthChecks = new Map();
    this.listeners = {};
    this.errors = [];
    this.silentMode = true; // Mode silencieux pour éviter l'affichage des erreurs pendant les tests
  }

  trackError(category, operation, error) {
    this.lastError = { category, operation, error };
    this.errors.push({ category, operation, error });
    
    // Ne pas afficher les erreurs en mode silencieux (pour les tests)
    if (!this.silentMode) {
      console.error(`${category} error:`, operation, error);
    }
    return this;
  }

  trackCacheHit(key) {
    return this;
  }

  trackCacheMiss(key) {
    return this;
  }

  incrementConnections() {
    this.connectionsCount++;
    return this;
  }

  decrementConnections() {
    this.connectionsCount--;
    return this;
  }

  notifySuccess(type, context) {
    return this;
  }

  notifyError(type, error, context) {
    this.lastError = { type, error, context };
    this.errors.push({ type, error, context });
    
    // Ne pas afficher les erreurs en mode silencieux (pour les tests)
    if (!this.silentMode) {
      console.error(`error: ${type}`, { error, service: "monitoring-service", timestamp: new Date().toISOString(), ...context });
    }
    return this;
  }

  async checkRedisHealth() {
    this.updateServiceHealth('redis', 'healthy');
    return true;
  }

  async checkDatabaseHealth() {
    this.updateServiceHealth('database', 'healthy');
    return true;
  }

  async checkCacheHealth() {
    this.updateServiceHealth('cache', 'healthy');
    return true;
  }

  async checkQueueHealth() {
    this.updateServiceHealth('queue', 'healthy');
    return true;
  }

  updateServiceHealth(serviceName, status, error) {
    this.healthChecks.set(serviceName, { status, error, lastCheck: new Date() });
    return this;
  }

  recordMetric(name, tags) {
    return this;
  }

  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
    return this;
  }

  emit(event, ...args) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(...args));
    }
    return this;
  }

  removeAllListeners() {
    this.listeners = {};
    return this;
  }

  // Activer/désactiver le mode silencieux
  setSilentMode(value) {
    this.silentMode = value;
    return this;
  }

  // Récupérer les erreurs enregistrées
  getErrors() {
    return this.errors;
  }

  // Réinitialiser les erreurs enregistrées
  clearErrors() {
    this.errors = [];
    return this;
  }

  async metricsEndpoint() {
    return 'mock_metrics_data';
  }

  async getMetrics() {
    return {
      system: {
        cpu: { usage: 10, count: 4, load: [1, 0.5, 0.25] },
        memory: { 
          total: 16000000000, 
          free: **********, 
          used: **********,
          heapTotal: 200000000,
          heapUsed: 100000000
        },
        uptime: 3600
      },
      application: {
        requests: { total: 1000, active: this.connectionsCount, errors: 10, avgResponseTime: 50 },
        connections: { current: this.connectionsCount, peak: 100 },
        cache: { hits: 800, misses: 200, size: 500 },
        security: { blockedRequests: 5, suspiciousActivities: 2 },
        queues: { total: 50, errors: 2 }
      },
      services: Object.fromEntries(this.healthChecks)
    };
  }

  async collectMetrics() {
    return {
      cpu: { usage: 10, count: 4, load: [1, 0.5, 0.25] },
      memory: { 
        total: 16000000000, 
        free: **********, 
        used: **********,
        heapTotal: 200000000,
        heapUsed: 100000000
      },
      uptime: 3600
    };
  }

  cleanup() {
    this.removeAllListeners();
    this.clearErrors();
    return this;
  }

  async shutdown() {
    return this.cleanup();
  }

  // Singleton pattern
  static getInstance() {
    if (!MonitoringServiceMock.instance) {
      MonitoringServiceMock.instance = new MonitoringServiceMock();
    }
    return MonitoringServiceMock.instance;
  }
}

// Initialiser l'instance pour le pattern singleton
MonitoringServiceMock.instance = null;

const MonitoringService = {
  MonitoringService: MonitoringServiceMock
};

module.exports = MonitoringService;
module.exports.default = module.exports; 