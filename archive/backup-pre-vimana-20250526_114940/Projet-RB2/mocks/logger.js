// Mock complet pour le module logger de l'application
const logger = {
  // Méthodes de journalisation de base
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  http: jest.fn(),
  verbose: jest.fn(),
  debug: jest.fn(),
  silly: jest.fn(),
  log: jest.fn(),
  
  // Méthodes supplémentaires
  profile: jest.fn(),
  startTimer: jest.fn().mockReturnValue({
    done: jest.fn(),
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }),
  
  // Méthodes pour créer des sous-loggers
  child: jest.fn().mockReturnThis(),
  
  // Gestion de configuration
  configure: jest.fn().mockReturnThis(),
  
  // Gestion du transport
  add: jest.fn().mockReturnThis(),
  remove: jest.fn().mockReturnThis(),
  clear: jest.fn().mockReturnThis(),
  
  // Gestion des événements
  on: jest.fn().mockReturnThis(),
  once: jest.fn().mockReturnThis(),
  
  // Utilitaires
  format: {
    combine: jest.fn(),
    timestamp: jest.fn(),
    printf: jest.fn(),
    colorize: jest.fn(),
    align: jest.fn(),
    cli: jest.fn(),
    json: jest.fn(),
    prettyPrint: jest.fn(),
    simple: jest.fn(),
    label: jest.fn(),
    metadata: jest.fn(),
    splat: jest.fn(),
    errors: jest.fn(),
    padLevels: jest.fn()
  },
  
  // Méthodes pour la fermeture
  close: jest.fn().mockResolvedValue(undefined),
  end: jest.fn().mockResolvedValue(undefined),
  
  // Méthodes pour les statistiques et le monitoring
  getRequestCount: jest.fn().mockReturnValue(0),
  resetRequestCount: jest.fn(),
  
  // Méthodes spécifiques à l'application
  logRequest: jest.fn(),
  logResponse: jest.fn(),
  logError: jest.fn(),
  logServiceCall: jest.fn(),
  logPerformance: jest.fn(),
  logSecurity: jest.fn(),
  logAudit: jest.fn(),
  
  // Niveaux de log
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    verbose: 4,
    debug: 5,
    silly: 6
  },
  
  // Méthodes pour les tests
  _reset: jest.fn().mockImplementation(() => {
    Object.keys(logger).forEach(key => {
      if (typeof logger[key] === 'function' && logger[key].mockReset) {
        logger[key].mockReset();
      }
    });
  })
};

// Ajouter les méthodes du logger lui-même à l'objet default pour la compatibilité ESM
logger.default = logger;

module.exports = logger; 