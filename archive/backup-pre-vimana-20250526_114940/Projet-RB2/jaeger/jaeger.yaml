apiVersion: jaegertracing.io/v1
kind: Jaeger
metadata:
  name: jaeger
spec:
  strategy: production
  storage:
    type: elasticsearch
    options:
      es:
        server-urls: http://elasticsearch:9200
        username: elastic
        password: changeme
  ingress:
    enabled: true
    security: oauth-proxy
  agent:
    strategy: DaemonSet
  collector:
    maxReplicas: 5
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi
  query:
    serviceType: ClusterIP
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi
    options:
      query:
        base-path: /jaeger
  ingester:
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi
  storage:
    type: elasticsearch
    options:
      es:
        server-urls: http://elasticsearch:9200
        username: elastic
        password: changeme
  volumeMounts:
    - name: certificates
      mountPath: /certificates
  volumes:
    - name: certificates
      secret:
        secretName: jaeger-certificates
  annotations:
    scheduler.alpha.kubernetes.io/critical-pod: ""
  sampling:
    options:
      default_strategy:
        type: probabilistic
        param: 0.1
      service_strategies:
      - service: important-service
        type: probabilistic
        param: 1.0
      - service: debugging-service
        type: probabilistic
        param: 0.5
