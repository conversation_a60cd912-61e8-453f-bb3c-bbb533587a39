#!/bin/bash
# Script pour compléter la migration de src vers superagent

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Complétion de la migration src vers superagent ===${NC}"

# Vérifier que les deux dossiers existent
if [ ! -d "src" ]; then
  echo -e "${RED}Le dossier src n'existe pas. Impossible de continuer.${NC}"
  exit 1
fi

if [ ! -d "superagent" ]; then
  echo -e "${RED}Le dossier superagent n'existe pas. Impossible de continuer.${NC}"
  exit 1
fi

# Créer une sauvegarde
echo -e "\n${YELLOW}Création d'une sauvegarde...${NC}"
BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
mkdir -p "backups/migration_completion_$BACKUP_DATE"
cp -r src "backups/migration_completion_$BACKUP_DATE/"
cp -r superagent "backups/migration_completion_$BACKUP_DATE/"
echo -e "${GREEN}Sauvegarde créée dans backups/migration_completion_$BACKUP_DATE${NC}"

# Copier les agents
echo -e "\n${YELLOW}Copie des agents...${NC}"
mkdir -p superagent/agents/nodes
cp -r src/agents/nodes/* superagent/agents/nodes/
echo -e "${GREEN}Agents copiés avec succès.${NC}"

# Copier les graphes
echo -e "\n${YELLOW}Copie des graphes...${NC}"
cp -r src/graph/* superagent/graph/
echo -e "${GREEN}Graphes copiés avec succès.${NC}"

# Copier les API
echo -e "\n${YELLOW}Copie des API...${NC}"
mkdir -p superagent/api/routes
cp -r src/api/routes/* superagent/api/routes/
echo -e "${GREEN}API copiées avec succès.${NC}"

# Copier les exemples
echo -e "\n${YELLOW}Copie des exemples...${NC}"
mkdir -p superagent/examples
cp -r src/examples/* superagent/examples/
echo -e "${GREEN}Exemples copiés avec succès.${NC}"

# Copier les configurations
echo -e "\n${YELLOW}Copie des configurations...${NC}"
mkdir -p superagent/config
cp -r src/config/* superagent/config/
echo -e "${GREEN}Configurations copiées avec succès.${NC}"

# Copier les utilitaires
echo -e "\n${YELLOW}Copie des utilitaires...${NC}"
mkdir -p superagent/utils
cp -r src/utils/* superagent/utils/
echo -e "${GREEN}Utilitaires copiés avec succès.${NC}"

# Mettre à jour les chemins d'importation
echo -e "\n${YELLOW}Mise à jour des chemins d'importation...${NC}"
find superagent -type f -name "*.py" | xargs sed -i '' 's|from src\.|from superagent.|g'
find superagent -type f -name "*.py" | xargs sed -i '' 's|import src\.|import superagent.|g'
echo -e "${GREEN}Chemins d'importation mis à jour avec succès.${NC}"

# Vérifier les conflits potentiels
echo -e "\n${YELLOW}Vérification des conflits potentiels...${NC}"
CONFLICTS=0
for file in $(find superagent -type f -name "*.py"); do
  if grep -q "ImportError" "$file" 2>/dev/null; then
    echo -e "${RED}Conflit potentiel dans $file${NC}"
    CONFLICTS=$((CONFLICTS+1))
  fi
done

if [ $CONFLICTS -eq 0 ]; then
  echo -e "${GREEN}Aucun conflit potentiel détecté.${NC}"
else
  echo -e "${RED}$CONFLICTS conflits potentiels détectés. Veuillez les résoudre manuellement.${NC}"
fi

# Créer un rapport de migration
echo -e "\n${YELLOW}Création du rapport de migration...${NC}"

cat > migration_completion_report.md << EOF
# Rapport de Complétion de Migration

## Actions Réalisées

- Sauvegarde créée dans \`backups/migration_completion_$BACKUP_DATE\`
- Copie des agents de \`src/agents/nodes\` vers \`superagent/agents/nodes\`
- Copie des graphes de \`src/graph\` vers \`superagent/graph\`
- Copie des API de \`src/api/routes\` vers \`superagent/api/routes\`
- Copie des exemples de \`src/examples\` vers \`superagent/examples\`
- Copie des configurations de \`src/config\` vers \`superagent/config\`
- Copie des utilitaires de \`src/utils\` vers \`superagent/utils\`
- Mise à jour des chemins d'importation dans les fichiers copiés

## Conflits Potentiels

$CONFLICTS conflits potentiels détectés.

## Prochaines Étapes

1. Résoudre les conflits potentiels
2. Exécuter les tests pour vérifier que tout fonctionne correctement
3. Supprimer le dossier \`src\` une fois que tout est validé

---

*Rapport généré le: $(date)*
EOF

echo -e "${GREEN}Rapport de migration créé: migration_completion_report.md${NC}"

echo -e "\n${BLUE}=== Migration complétée avec succès ===${NC}"
echo -e "${YELLOW}Veuillez vérifier le rapport de migration et résoudre les conflits potentiels.${NC}"
echo -e "${YELLOW}Une fois que tout est validé, vous pouvez supprimer le dossier src.${NC}"
