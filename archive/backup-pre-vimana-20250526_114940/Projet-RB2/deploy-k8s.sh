#!/bin/bash

# Script pour déployer les microservices sur Kubernetes

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Déploiement des microservices sur Kubernetes ===${NC}"

# Vérifier que kubectl est installé
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}kubectl n'est pas installé. Veuillez l'installer avant de continuer.${NC}"
    exit 1
fi

# Vérifier que Docker est installé
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker n'est pas installé. Veuillez l'installer avant de continuer.${NC}"
    exit 1
fi

# Construire les images Docker
echo -e "\n${YELLOW}Construction des images Docker...${NC}"

echo -e "${YELLOW}Construction de l'image Agent-RB...${NC}"
docker build -t agent-rb:latest ./Agent-RB
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de la construction de l'image Agent-RB.${NC}"
    exit 1
fi
echo -e "${GREEN}Image Agent-RB construite avec succès.${NC}"

echo -e "${YELLOW}Construction de l'image superagent...${NC}"
docker build -t superagent:latest ./superagent
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de la construction de l'image superagent.${NC}"
    exit 1
fi
echo -e "${GREEN}Image superagent construite avec succès.${NC}"

# Construire l'image Agent IA
echo -e "${YELLOW}Construction de l'image Agent IA...${NC}"
docker build -t agent-ia:latest -f "./Agent IA/Dockerfile.api" "./Agent IA"
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de la construction de l'image Agent IA.${NC}"
    exit 1
fi
echo -e "${GREEN}Image Agent IA construite avec succès.${NC}"

# Appliquer les configurations Kubernetes
echo -e "\n${YELLOW}Application des configurations Kubernetes...${NC}"

# Créer le namespace si nécessaire
kubectl create namespace retreat-system --dry-run=client -o yaml | kubectl apply -f -

# Appliquer les ConfigMaps et Secrets
echo -e "${YELLOW}Application des ConfigMaps et Secrets...${NC}"
kubectl apply -f k8s/shared-config.yaml
kubectl apply -f k8s/shared-secrets.yaml

# Appliquer les déploiements
echo -e "${YELLOW}Application des déploiements...${NC}"
kubectl apply -f k8s/agent-rb-deployment.yaml
kubectl apply -f k8s/superagent-deployment.yaml
kubectl apply -f "Agent IA/k8s/agent-ia-deployment.yaml"
kubectl apply -f "Agent IA/k8s/agent-ia-service.yaml"

# Appliquer l'Ingress
echo -e "${YELLOW}Application de l'Ingress...${NC}"
kubectl apply -f k8s/ingress.yaml

# Vérifier l'état des déploiements
echo -e "\n${YELLOW}Vérification de l'état des déploiements...${NC}"
kubectl get deployments

# Vérifier l'état des services
echo -e "\n${YELLOW}Vérification de l'état des services...${NC}"
kubectl get services

# Vérifier l'état de l'Ingress
echo -e "\n${YELLOW}Vérification de l'état de l'Ingress...${NC}"
kubectl get ingress

echo -e "\n${GREEN}Déploiement terminé avec succès.${NC}"
echo -e "${YELLOW}Veuillez noter que les pods peuvent prendre quelques minutes pour démarrer complètement.${NC}"
echo -e "${YELLOW}Vous pouvez vérifier l'état des pods avec la commande: kubectl get pods${NC}"
