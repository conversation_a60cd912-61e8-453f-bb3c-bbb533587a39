/**
 * Script de maintenance des tests
 * 
 * Ce script permet de:
 * 1. Identifier les tests obsolètes ou redondants
 * 2. Analyser la couverture de code pour identifier les zones non testées
 * 3. Générer un rapport de maintenance des tests
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Configuration
const TEST_DIRS = [
  'Backend/src/tests',
  'frontend/src/tests'
];
const COVERAGE_THRESHOLD = 70; // Seuil de couverture en pourcentage
const COVERAGE_DIR = 'coverage';
const REPORT_PATH = 'test-maintenance-report.md';

// Fonction pour trouver tous les fichiers de test
async function findTestFiles() {
  const patterns = TEST_DIRS.map(dir => `${dir}/**/*.test.{js,ts,tsx}`);
  const files = [];
  
  for (const pattern of patterns) {
    const matches = await glob(pattern);
    files.push(...matches);
  }
  
  return files;
}

// Fonction pour exécuter un test et vérifier s'il passe
async function runTest(testFile) {
  try {
    execSync(`NODE_OPTIONS="--max-old-space-size=1024" npx jest --config=minimal-jest.config.js ${testFile} --silent`, { stdio: 'pipe' });
    return { file: testFile, status: 'pass' };
  } catch (error) {
    return { file: testFile, status: 'fail', error: error.message };
  }
}

// Fonction pour analyser le contenu d'un fichier de test
function analyzeTestFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  // Compter le nombre de tests
  const testCount = (content.match(/it\s*\(/g) || []).length;
  
  // Vérifier si le test est vide (seulement des assertions triviales)
  const hasTrivialAssertions = content.includes('expect(true).toBe(true)');
  const hasRealAssertions = content.includes('expect(') && !content.includes('expect(true).toBe(true)');
  
  // Vérifier si le test est commenté
  const commentedTestCount = (content.match(/\/\/\s*it\s*\(/g) || []).length;
  
  // Vérifier la date de dernière modification
  const stats = fs.statSync(filePath);
  const lastModified = stats.mtime;
  const daysSinceModified = Math.floor((Date.now() - lastModified.getTime()) / (1000 * 60 * 60 * 24));
  
  return {
    file: filePath,
    testCount,
    hasTrivialAssertions,
    hasRealAssertions,
    commentedTestCount,
    lastModified,
    daysSinceModified,
    lines: lines.length
  };
}

// Fonction pour générer un rapport de couverture de code
async function generateCoverageReport() {
  try {
    execSync(`NODE_OPTIONS="--max-old-space-size=2048" npx jest --config=minimal-jest.config.js --coverage`, { stdio: 'pipe' });
    
    if (fs.existsSync(path.join(COVERAGE_DIR, 'coverage-summary.json'))) {
      const summary = JSON.parse(fs.readFileSync(path.join(COVERAGE_DIR, 'coverage-summary.json'), 'utf8'));
      return summary;
    }
    
    return null;
  } catch (error) {
    console.error('Erreur lors de la génération du rapport de couverture:', error.message);
    return null;
  }
}

// Fonction pour identifier les fichiers source sans tests
async function findUntested(coverageSummary) {
  if (!coverageSummary) return [];
  
  const untested = [];
  
  for (const [filePath, coverage] of Object.entries(coverageSummary)) {
    if (filePath === 'total') continue;
    
    if (coverage.lines.pct < COVERAGE_THRESHOLD) {
      untested.push({
        file: filePath,
        coverage: coverage.lines.pct,
        missingLines: coverage.lines.total - coverage.lines.covered
      });
    }
  }
  
  return untested;
}

// Fonction pour générer un rapport de maintenance
function generateMaintenanceReport(testResults, testAnalysis, untested) {
  const failingTests = testResults.filter(test => test.status === 'fail');
  const trivialTests = testAnalysis.filter(test => test.hasTrivialAssertions && !test.hasRealAssertions);
  const oldTests = testAnalysis.filter(test => test.daysSinceModified > 90); // Plus de 3 mois
  
  const report = `# Rapport de maintenance des tests

## Résumé

- **Tests analysés:** ${testResults.length}
- **Tests en échec:** ${failingTests.length}
- **Tests triviaux:** ${trivialTests.length}
- **Tests obsolètes (non modifiés depuis 3 mois):** ${oldTests.length}
- **Fichiers insuffisamment testés:** ${untested.length}

## Tests en échec

${failingTests.length > 0 ? failingTests.map(test => `- ${test.file}`).join('\n') : 'Aucun test en échec.'}

## Tests triviaux

Ces tests ne contiennent que des assertions triviales (ex: \`expect(true).toBe(true)\`) et devraient être améliorés:

${trivialTests.length > 0 ? trivialTests.map(test => `- ${test.file} (${test.testCount} tests)`).join('\n') : 'Aucun test trivial.'}

## Tests obsolètes

Ces tests n'ont pas été modifiés depuis plus de 3 mois et pourraient nécessiter une révision:

${oldTests.length > 0 ? oldTests.map(test => `- ${test.file} (${test.daysSinceModified} jours)`).join('\n') : 'Aucun test obsolète.'}

## Fichiers insuffisamment testés

Ces fichiers ont une couverture de code inférieure à ${COVERAGE_THRESHOLD}% et nécessitent des tests supplémentaires:

${untested.length > 0 ? untested.map(file => `- ${file.file} (${file.coverage.toFixed(2)}%, ${file.missingLines} lignes non couvertes)`).join('\n') : 'Tous les fichiers sont suffisamment testés.'}

## Recommandations

1. Corriger les ${failingTests.length} tests en échec
2. Améliorer les ${trivialTests.length} tests triviaux
3. Réviser les ${oldTests.length} tests obsolètes
4. Ajouter des tests pour les ${untested.length} fichiers insuffisamment testés

Rapport généré le ${new Date().toISOString()}
`;

  fs.writeFileSync(REPORT_PATH, report);
  console.log(`Rapport de maintenance généré: ${REPORT_PATH}`);
  
  return report;
}

// Fonction principale
async function main() {
  console.log('Recherche des fichiers de test...');
  const testFiles = await findTestFiles();
  console.log(`${testFiles.length} fichiers de test trouvés.`);
  
  console.log('Exécution des tests...');
  const testResults = [];
  for (const file of testFiles) {
    console.log(`Exécution de ${file}...`);
    const result = await runTest(file);
    testResults.push(result);
  }
  
  console.log('Analyse des fichiers de test...');
  const testAnalysis = testFiles.map(analyzeTestFile);
  
  console.log('Génération du rapport de couverture...');
  const coverageSummary = await generateCoverageReport();
  
  console.log('Recherche des fichiers insuffisamment testés...');
  const untested = await findUntested(coverageSummary);
  
  console.log('Génération du rapport de maintenance...');
  generateMaintenanceReport(testResults, testAnalysis, untested);
  
  console.log('Terminé!');
}

// Exécuter le script
main().catch(error => {
  console.error(`Erreur lors de l'exécution du script: ${error.message}`);
  process.exit(1);
});
