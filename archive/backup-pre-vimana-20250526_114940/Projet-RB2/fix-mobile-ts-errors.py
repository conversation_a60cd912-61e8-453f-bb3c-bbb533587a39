#!/usr/bin/env python3
import os
import re
import glob

def fix_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        # Create backup
        with open(file_path + '.bak', 'w', encoding='utf-8') as f:
            f.write(content)
        # Common syntax errors
        replacements = [
            # Fix missing semicolons
            (r'([a-zA-Z0-9_])}\s*$', r'\1;}'),  # Add missing semicolon before closing bracket
            (r'([a-zA-Z0-9_])}$', r'\1;}'),  # Add missing semicolon at end of line before }
            (r'([a-zA-Z0-9_])\s*$', r'\1;'),  # Add missing semicolon at end of line
            # Fix missing parentheses
            (r'if\s+([^(].*?)\s*{', r'if (\1) {'),  # Add missing parentheses in if statements
            (r'for\s+([^(].*?)\s*{', r'for (\1) {'),  # Add missing parentheses in for loops
            (r'while\s+([^(].*?)\s*{', r'while (\1) {'),  # Add missing parentheses in while loops
            # Fix missing braces
            (r'(if\s+\([^{]*\))\s*([^{].*)$', r'\1 {\2}'),  # Add missing braces in if statements
            # Fix import syntax
            (r'import\\s+(.*)\\s+from\\s+([\\\'\\"].*[\\\'\\"])\\s*$', r'import \\\1 from \\\2;'),  # Add missing semicolon in import
            # Fix export syntax
            (r'export\\s+(const|let|var|function|class|interface|type)\\s+(.*)\\s*$', r'export \\\1 \\\2;'),  # Add missing semicolon in export
        ]
        # Apply all replacements
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        # Write corrected content back
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f'Fixed {file_path}')
        return True
    except Exception as e:
        print(f'Error processing {file_path}: {e}')
        return False

def find_ts_files(root_dir):
    """Find all TypeScript files in the given directory and its subdirectories."""
    ts_files = []
    for pattern in ['**/*.ts', '**/*.tsx']:
        ts_files.extend(glob.glob(os.path.join(root_dir, pattern), recursive=True))
    return ts_files

def main():
    # Root directory containing TypeScript files
    root_dir = 'mobile/src'
    print(f'Finding TypeScript files in {root_dir}...')
    ts_files = find_ts_files(root_dir)
    print(f'Found {len(ts_files)} TypeScript files')

    # Process each file
    success_count = 0
    for file_path in ts_files:
        if fix_file(file_path):
            success_count += 1

    print(f'Successfully fixed {success_count}/{len(ts_files)} files')

if __name__ == '__main__':
    main()
