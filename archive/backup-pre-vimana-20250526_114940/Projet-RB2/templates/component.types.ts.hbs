import React from 'react';

export interface {{pascalCase name}}Props {
  /**
   * Contenu du composant
   */
  children?: React.ReactNode;
  
  {{#if hasProps}}
  /**
   * Variante de style du composant
   */
  variant?: 'default' | 'primary' | 'secondary';
  
  /**
   * Callback quand le composant est cliqué
   */
  onClick?: () => void;
  {{/if}}
  
  /**
   * Classes CSS additionnelles
   */
  className?: string;
} 