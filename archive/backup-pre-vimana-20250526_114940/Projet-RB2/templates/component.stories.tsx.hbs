import type { Meta, StoryObj } from '@storybook/react';
import { {{pascalCase name}} } from './{{pascalCase name}}';

const meta: Meta<typeof {{pascalCase name}}> = {
  title: 'Atomic/{{atomicType}}/{{pascalCase name}}',
  component: {{pascalCase name}},
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Un composant {{atomicType}} qui...'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    {{#if hasProps}}
    variant: {
      control: 'select',
      options: ['default', 'primary', 'secondary'],
      description: 'Style variant of the component'
    },
    onClick: { action: 'clicked' }
    {{/if}}
  }
};

export default meta;
type Story = StoryObj<typeof {{pascalCase name}}>;

export const Default: Story = {
  args: {
    children: 'Contenu par défaut'
  }
};

{{#if hasProps}}
export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Contenu primaire'
  }
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Contenu secondaire'
  }
};
{{/if}} 