import React from 'react';
{{#if isStyled}}
import styled from 'styled-components';
import { theme } from '../../../theme';
{{/if}}
import { {{pascalCase name}}Props } from './{{pascalCase name}}.types';

/**
 * {{pascalCase name}} Component
 * 
 * @description {{pascalCase name}} est un composant {{atomicType}} qui...
 */
{{#if isStyled}}
const Styled{{pascalCase name}} = styled.div<{ variant?: string }>`
  display: flex;
  padding: ${theme.spacing(2)};
  
  ${({ variant }) => variant === 'primary' && `
    background-color: ${theme.palette.primary.main};
    color: ${theme.palette.primary.contrastText};
  `}
  
  ${({ variant }) => variant === 'secondary' && `
    background-color: ${theme.palette.secondary.main};
    color: ${theme.palette.secondary.contrastText};
  `}
`;
{{/if}}

export const {{pascalCase name}} = ({ 
  children,
  {{#if hasProps}}
  variant = 'default',
  onClick,
  {{/if}}
  className,
  ...props
}: {{pascalCase name}}Props) => {
  return (
    {{#if isStyled}}
    <Styled{{pascalCase name}} 
      variant={variant}
      className={className}
      onClick={onClick}
      {...props}
    >
      {children}
    </Styled{{pascalCase name}}>
    {{else}}
    <div className={className} {...props}>
      {children}
    </div>
    {{/if}}
  );
}; 