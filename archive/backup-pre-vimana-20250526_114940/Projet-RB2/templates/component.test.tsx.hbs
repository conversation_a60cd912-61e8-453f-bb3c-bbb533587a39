import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { {{pascalCase name}} } from './{{pascalCase name}}';

describe('{{pascalCase name}}', () => {
  it('renders correctly', () => {
    render(<{{pascalCase name}}>Test Content</{{pascalCase name}}>);
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });
  
  {{#if hasProps}}
  it('handles click events', async () => {
    const handleClick = jest.fn();
    render(<{{pascalCase name}} onClick={handleClick}>Clickable</{{pascalCase name}}>);
    
    await userEvent.click(screen.getByText('Clickable'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('applies variant styles', () => {
    const { rerender } = render(<{{pascalCase name}} variant="primary">Primary</{{pascalCase name}}>);
    // Vérifier les styles primary...
    
    rerender(<{{pascalCase name}} variant="secondary">Secondary</{{pascalCase name}}>);
    // Vérifier les styles secondary...
  });
  {{/if}}
  
  // Test d'accessibilité
  it('should be accessible', () => {
    const { container } = render(<{{pascalCase name}}>Accessible content</{{pascalCase name}}>);
    expect(container).toBeInTheDocument();
    // Ajouter des tests d'accessibilité plus spécifiques ici
  });
}); 