#!/usr/bin/env node

/**
 * Script pour corriger les imports dans les fichiers de pages Audrey Frontend
 * 
 * Ce script parcourt tous les fichiers dans le dossier src/pages/audrey-frontend
 * et remplace les imports relatifs par des imports depuis le fichier adapters.ts
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { glob } from 'glob';

// Obtenir le chemin du fichier actuel et le répertoire
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Chemin vers le dossier des pages Audrey Frontend
const pagesDir = path.join(__dirname, '../src/pages/audrey-frontend');

// Motifs d'imports à remplacer
const importPatterns = [
  {
    // Remplacer les imports de "../components/ui/Footer"
    pattern: /import\s+Footer\s+from\s+["']\.\.\/components\/ui\/Footer["'];/g,
    replacement: `import { Footer } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/ui/ScrollToTop"
    pattern: /import\s+ScrollToTop\s+from\s+["']\.\.\/components\/ui\/ScrollToTop["'];/g,
    replacement: `import { ScrollToTop } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/ui/LazyImage"
    pattern: /import\s+LazyImage\s+from\s+["']\.\.\/components\/ui\/LazyImage["'];/g,
    replacement: `import { LazyImage } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/ui/star-border"
    pattern: /import\s+\{\s*StarBorder\s*\}\s+from\s+["']\.\.\/components\/ui\/star-border["'];/g,
    replacement: `import { StarBorder } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/organisms/NavBarClient/NavBarClient"
    pattern: /import\s+NavBarClient\s+from\s+["']\.\.\/components\/organisms\/NavBarClient\/NavBarClient["'];/g,
    replacement: `import { NavBarClient } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/HomePage/SearchBar"
    pattern: /import\s+SearchBar\s+from\s+["']\.\.\/components\/HomePage\/SearchBar["'];/g,
    replacement: `import { SearchBar } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/Blog/BlogSidebar"
    pattern: /import\s+BlogSidebar\s+from\s+["']\.\.\/components\/Blog\/BlogSidebar["'];/g,
    replacement: `import { BlogSidebar } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "@heroicons/react/24/outline"
    pattern: /import\s+\{([^}]+)\}\s+from\s+["']@heroicons\/react\/24\/outline["'];/g,
    replacement: `import { $1 } from '@heroicons/react/24/outline';`
  },
  {
    // Remplacer les imports de "styled-components"
    pattern: /import\s+styled\s+from\s+["']styled-components["'];/g,
    replacement: `import styled from 'styled-components';`
  },
  {
    // Remplacer les imports de "../components/atoms/ChatBot"
    pattern: /import\s+ChatBot\s+from\s+["']\.\.\/components\/atoms\/ChatBot["'];/g,
    replacement: `import { ChatBot } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/ui/IconSearch"
    pattern: /import\s+IconSearch\s+from\s+["']\.\.\/components\/ui\/IconSearch["'];/g,
    replacement: `import { IconSearch } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/molecules/FeaturedRetreat"
    pattern: /import\s+FeaturedRetreat\s+from\s+["']\.\.\/components\/molecules\/FeaturedRetreat["'];/g,
    replacement: `import { FeaturedRetreat } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/ui/RetreatSearch"
    pattern: /import\s+RetreatSearch\s+from\s+["']\.\.\/components\/ui\/RetreatSearch["'];/g,
    replacement: `import { RetreatSearch } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/molecules/Retreats"
    pattern: /import\s+Retreats\s+from\s+["']\.\.\/components\/molecules\/Retreats["'];/g,
    replacement: `import { Retreats } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/wallet/Wallet"
    pattern: /import\s+Wallet\s+from\s+["']\.\.\/components\/wallet\/Wallet["'];/g,
    replacement: `import { Wallet } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/Blog/FeaturedPosts"
    pattern: /import\s+FeaturedPosts\s+from\s+["']\.\.\/components\/Blog\/FeaturedPosts["'];/g,
    replacement: `import { FeaturedPosts } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/Blog/RecentPosts"
    pattern: /import\s+RecentPosts\s+from\s+["']\.\.\/components\/Blog\/RecentPosts["'];/g,
    replacement: `import { RecentPosts } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/Blog/PopularCategories"
    pattern: /import\s+PopularCategories\s+from\s+["']\.\.\/components\/Blog\/PopularCategories["'];/g,
    replacement: `import { PopularCategories } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/ui/Footer-accueil"
    pattern: /import\s+FooterAccueil\s+from\s+["']\.\.\/components\/ui\/Footer-accueil["'];/g,
    replacement: `import { FooterAccueil } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../hooks/useIsMobile"
    pattern: /import\s+useIsMobile\s+from\s+["']\.\.\/hooks\/useIsMobile["'];/g,
    replacement: `import { useIsMobile } from '../../components/audrey-frontend/adapters';`
  },
  {
    // Remplacer les imports de "../components/RetreatFinder/IdealRetreatFinder"
    pattern: /import\s+IdealRetreatFinder\s+from\s+["']\.\.\/components\/RetreatFinder\/IdealRetreatFinder["'];/g,
    replacement: `import { IdealRetreatFinder } from '../../components/audrey-frontend/adapters';`
  }
];

// Fonction pour corriger les imports dans un fichier
function fixImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Appliquer chaque motif de remplacement
    importPatterns.forEach(({ pattern, replacement }) => {
      if (pattern.test(content)) {
        content = content.replace(pattern, replacement);
        modified = true;
      }
    });

    // Sauvegarder le fichier si des modifications ont été apportées
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed imports in ${path.relative(process.cwd(), filePath)}`);
    } else {
      console.log(`ℹ️ No imports to fix in ${path.relative(process.cwd(), filePath)}`);
    }
  } catch (error) {
    console.error(`❌ Error fixing imports in ${filePath}:`, error);
  }
}

// Fonction principale asynchrone
async function main() {
  try {
    // Trouver tous les fichiers TypeScript/TSX dans le dossier des pages
    const files = await glob(`${pagesDir}/**/*.{ts,tsx}`);
    
    console.log(`Found ${files.length} files to process`);
    
    // Corriger les imports dans chaque fichier
    for (const file of files) {
      fixImportsInFile(file);
    }
    
    console.log('Import fixing completed!');
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Exécuter la fonction principale
main();
