/**
 * Script pour générer un sitemap XML
 * 
 * Ce script génère un sitemap XML pour le site web.
 * Il parcourt les routes définies et génère une entrée pour chaque route.
 */

const fs = require('fs');
const path = require('path');
const prettier = require('prettier');

// Configuration
const SITE_URL = 'https://retreatandbe.com';
const PUBLIC_DIR = path.join(__dirname, '../public');
const ROUTES_FILE = path.join(__dirname, '../src/routes.tsx');

// Fonction pour extraire les routes du fichier de routes
const extractRoutes = () => {
  try {
    // Lire le fichier de routes
    const routesContent = fs.readFileSync(ROUTES_FILE, 'utf8');
    
    // Extraire les routes avec une expression régulière
    const routeRegex = /path="([^"]+)"/g;
    const routes = [];
    let match;
    
    while ((match = routeRegex.exec(routesContent)) !== null) {
      const route = match[1];
      
      // Ignorer les routes dynamiques et les routes spéciales
      if (
        !route.includes(':') && // Routes dynamiques
        !route.includes('*') && // Routes wildcard
        route !== '/404' && // Page d'erreur
        route !== '/500' && // Page d'erreur
        route !== '/admin' && // Pages d'administration
        !route.startsWith('/admin/') // Pages d'administration
      ) {
        routes.push(route);
      }
    }
    
    return routes;
  } catch (error) {
    console.error('Erreur lors de l\'extraction des routes:', error);
    return [];
  }
};

// Fonction pour générer le sitemap XML
const generateSitemap = (routes) => {
  const today = new Date().toISOString().split('T')[0];
  
  const sitemap = `
    <?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${routes
        .map(
          (route) => `
            <url>
              <loc>${SITE_URL}${route}</loc>
              <lastmod>${today}</lastmod>
              <changefreq>${getChangeFrequency(route)}</changefreq>
              <priority>${getPriority(route)}</priority>
            </url>
          `
        )
        .join('')}
    </urlset>
  `;
  
  // Formater le XML avec prettier
  const formattedSitemap = prettier.format(sitemap, {
    parser: 'html',
    printWidth: 100,
  });
  
  return formattedSitemap;
};

// Fonction pour déterminer la fréquence de changement d'une route
const getChangeFrequency = (route) => {
  if (route === '/') {
    return 'daily';
  } else if (route.includes('/blog/')) {
    return 'weekly';
  } else if (route.includes('/retreats/')) {
    return 'weekly';
  } else {
    return 'monthly';
  }
};

// Fonction pour déterminer la priorité d'une route
const getPriority = (route) => {
  if (route === '/') {
    return '1.0';
  } else if (route === '/retreats') {
    return '0.9';
  } else if (route === '/blog') {
    return '0.8';
  } else if (route.includes('/retreats/')) {
    return '0.7';
  } else if (route.includes('/blog/')) {
    return '0.6';
  } else {
    return '0.5';
  }
};

// Fonction principale
const main = () => {
  try {
    // Extraire les routes
    const routes = extractRoutes();
    
    // Ajouter la route racine si elle n'est pas déjà présente
    if (!routes.includes('/')) {
      routes.unshift('/');
    }
    
    // Générer le sitemap
    const sitemap = generateSitemap(routes);
    
    // Écrire le sitemap dans le répertoire public
    fs.writeFileSync(path.join(PUBLIC_DIR, 'sitemap.xml'), sitemap);
    
    console.log(`Sitemap généré avec succès avec ${routes.length} routes.`);
  } catch (error) {
    console.error('Erreur lors de la génération du sitemap:', error);
    process.exit(1);
  }
};

// Exécuter la fonction principale
main();
