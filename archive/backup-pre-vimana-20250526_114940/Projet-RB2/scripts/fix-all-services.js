const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const colors = require('colors');

// Configuration étendue des services
const SERVICES = {
  // Services Backend principaux
  CORE: [
    'Backend',
    'AuthService',
    'UserService',
    'FileService',
    'PaymentService',
    'NotificationService',
  ],
  // Services Web3
  WEB3: [
    'web3-nft-service',
    'web3-wallet-service',
    'web3-transaction-service',
    'smart-contract-service'
  ],
  // Services Sociaux
  SOCIAL: [
    'Social',
    'Social-Platform-Service',
    'social-graph-service',
    'social-analytics-service'
  ],
  // Services ML/AI
  AI: [
    'MLProcessingService',
    'EventCorrelationService',
    'ai-recommendation-service',
    'data-processing-service'
  ]
};

// Configuration spécifique par type de service
const SERVICE_CONFIGS = {
  // Configuration par défaut
  default: {
    preCheck: ['package.json', 'tsconfig.json'],
    commands: [
      'npm install --legacy-peer-deps',
      'npm run lint:fix',
      'npm run format',
      'npm run build'
    ]
  },
  // Configuration spécifique Web3
  web3: {
    preCheck: ['package.json', 'tsconfig.json', 'hardhat.config.ts'],
    commands: [
      'npm install --legacy-peer-deps',
      'npm run lint:fix',
      'npm run format',
      'npx hardhat compile',
      'npm run build'
    ]
  },
  // Configuration spécifique Social
  social: {
    preCheck: ['package.json', 'tsconfig.json'],
    commands: [
      'npm install --legacy-peer-deps',
      'npm run lint:fix',
      'npm run format',
      'npm run generate-api', // Pour les services avec OpenAPI/Swagger
      'npm run build'
    ]
  }
};

// Fonction utilitaire pour le logging
function log(message, color = 'white') {
  console.log(colors[color](message));
}

// Fonction pour vérifier les prérequis d'un service
function checkServicePrerequisites(servicePath, config) {
  return config.preCheck.every(file => {
    const exists = fs.existsSync(path.join(servicePath, file));
    if (!exists) {
      log(`Missing required file: ${file}`, 'yellow');
    }
    return exists;
  });
}

// Fonction pour corriger un service spécifique
async function fixService(serviceName, serviceType = 'default') {
  const config = SERVICE_CONFIGS[serviceType] || SERVICE_CONFIGS.default;
  const servicePath = path.join(process.cwd(), serviceName);

  log(`\n📦 Processing ${serviceName}...`, 'cyan');

  if (!fs.existsSync(servicePath)) {
    log(`Service directory not found: ${servicePath}`, 'red');
    return false;
  }

  if (!checkServicePrerequisites(servicePath, config)) {
    log(`Prerequisites not met for ${serviceName}`, 'red');
    return false;
  }

  try {
    // Sauvegarde des fichiers importants
    const backupDir = path.join(servicePath, '.backup');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }

    ['package.json', 'tsconfig.json'].forEach(file => {
      const filePath = path.join(servicePath, file);
      if (fs.existsSync(filePath)) {
        fs.copyFileSync(filePath, path.join(backupDir, file));
      }
    });

    // Exécution des commandes de correction
    process.chdir(servicePath);
    for (const command of config.commands) {
      log(`\n🔄 Executing: ${command}`, 'yellow');
      try {
        execSync(command, { 
          stdio: 'inherit',
          env: { ...process.env, HUSKY: '0' }
        });
      } catch (error) {
        log(`Warning: Command failed but continuing: ${command}`, 'yellow');
      }
    }

    // Correction des erreurs TypeScript courantes
    await fixTypescriptIssues(servicePath);

    // Correction des problèmes de composants UI (si applicable)
    if (fs.existsSync(path.join(servicePath, 'src/components'))) {
      await fixUIComponents(servicePath);
    }

    process.chdir('..');
    log(`✅ ${serviceName} fixed successfully`, 'green');
    return true;
  } catch (error) {
    log(`❌ Error fixing ${serviceName}: ${error.message}`, 'red');
    process.chdir('..');
    return false;
  }
}

// Fonction principale
async function main() {
  log('\n🚀 Starting comprehensive service fixes...', 'cyan');

  const results = {
    success: [],
    failed: []
  };

  // Traitement de tous les types de services
  for (const [serviceType, services] of Object.entries(SERVICES)) {
    log(`\n📑 Processing ${serviceType} services...`, 'cyan');

    for (const service of services) {
      const success = await fixService(
        service,
        serviceType.toLowerCase()
      );

      if (success) {
        results.success.push(service);
      } else {
        results.failed.push(service);
      }
    }
  }

  // Rapport final
  log('\n📊 Final Report', 'cyan');
  log('\n✅ Successfully fixed services:', 'green');
  results.success.forEach(service => log(`  - ${service}`, 'green'));

  if (results.failed.length > 0) {
    log('\n❌ Failed services:', 'red');
    results.failed.forEach(service => log(`  - ${service}`, 'red'));
  }

  // Retourner le code de sortie approprié
  return results.failed.length === 0 ? 0 : 1;
}

// Exécution du script
main().then(
  exitCode => process.exit(exitCode),
  error => {
    log('\n❌ Fatal error:', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
);
