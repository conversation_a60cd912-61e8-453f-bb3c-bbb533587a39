#!/usr/bin/env node
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const glob = require('glob');
const colors = require('colors');

// Configuration
const IGNORED_DIRS = ['node_modules', 'dist', 'build', '.git', 'coverage'];
const FILE_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.css', '.scss', '.json', '.md'];
const REPORT_DIR = 'reports';
const REPORT_FILE = `scan-report-${new Date().toISOString().split('T')[0]}.json`;

// Utilitaires de logging
const log = {
  info: (msg) => console.log(colors.blue(msg)),
  success: (msg) => console.log(colors.green(msg)),
  warning: (msg) => console.log(colors.yellow(msg)),
  error: (msg) => console.log(colors.red(msg))
};

// Structure pour le rapport
const report = {
  timestamp: new Date().toISOString(),
  summary: {
    totalFiles: 0,
    totalErrors: 0,
    filesWithErrors: 0,
    fixedErrors: 0
  },
  details: [],
  duration: 0,
  fixes: {
    formatting: 0,
    linting: 0,
    typescript: 0,
    imports: 0
  }
};

// Fonction pour sauvegarder le rapport
function saveReport() {
  if (!fs.existsSync(REPORT_DIR)) {
    fs.mkdirSync(REPORT_DIR, { recursive: true });
  }
  
  const reportPath = path.join(REPORT_DIR, REPORT_FILE);
  const reportContent = JSON.stringify(report, null, 2);
  
  fs.writeFileSync(reportPath, reportContent);
  log.success(`\n📝 Rapport détaillé sauvegardé dans: ${reportPath}`);
  
  // Générer aussi une version HTML du rapport
  const htmlReport = generateHtmlReport();
  const htmlReportPath = reportPath.replace('.json', '.html');
  fs.writeFileSync(htmlReportPath, htmlReport);
  log.success(`📊 Rapport HTML généré: ${htmlReportPath}`);
}

// Fonction pour générer le rapport HTML
function generateHtmlReport() {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Rapport de scan - ${new Date().toLocaleDateString()}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .error-list { margin-top: 20px; }
        .error-item { border-left: 3px solid #ff4444; padding: 10px; margin: 10px 0; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Rapport de scan automatique</h1>
        <p>Date: ${new Date(report.timestamp).toLocaleString()}</p>
        <p>Durée: ${(report.duration / 1000).toFixed(2)}s</p>
      </div>
      
      <div class="summary">
        <h2>Résumé</h2>
        <ul>
          <li>Fichiers analysés: ${report.summary.totalFiles}</li>
          <li>Erreurs trouvées: ${report.summary.totalErrors}</li>
          <li>Fichiers avec erreurs: ${report.summary.filesWithErrors}</li>
          <li>Erreurs corrigées: ${report.summary.fixedErrors}</li>
        </ul>
        
        <h3>Corrections effectuées</h3>
        <ul>
          <li>Formatage: ${report.fixes.formatting}</li>
          <li>Linting: ${report.fixes.linting}</li>
          <li>TypeScript: ${report.fixes.typescript}</li>
          <li>Imports: ${report.fixes.imports}</li>
        </ul>
      </div>
      
      <div class="error-list">
        <h2>Détails des erreurs</h2>
        ${report.details.map(detail => `
          <div class="error-item">
            <h3>${detail.file}</h3>
            <p>Erreurs trouvées: ${detail.errors.length}</p>
            <ul>
              ${detail.errors.map(error => `
                <li class="${error.fixed ? 'success' : 'error'}">
                  ${error.message} ${error.fixed ? '(Corrigé)' : ''}
                </li>
              `).join('')}
            </ul>
          </div>
        `).join('')}
      </div>
    </body>
    </html>
  `;
}

// Fonction pour exécuter une commande shell
function execCommand(command, options = {}) {
  try {
    return execSync(command, { encoding: 'utf8', ...options });
  } catch (error) {
    if (!options.ignoreError) {
      throw error;
    }
    return error.stdout;
  }
}

// Fonction pour trouver tous les fichiers à analyser
function findFiles() {
  const pattern = `**/*{${FILE_EXTENSIONS.join(',')}}`;
  const ignorePattern = IGNORED_DIRS.map(dir => `**/${dir}/**`);
  
  const files = glob.sync(pattern, {
    ignore: ignorePattern,
    nodir: true
  });
  
  report.summary.totalFiles = files.length;
  return files;
}

// Fonction pour analyser les erreurs TypeScript
async function checkTypeScriptErrors(file) {
  if (!['.ts', '.tsx'].includes(path.extname(file))) return [];
  
  try {
    execCommand(`tsc ${file} --noEmit`);
    return [];
  } catch (error) {
    const errors = error.stdout.split('\n')
      .filter(line => line.includes('error TS'))
      .map(line => ({
        type: 'typescript',
        message: line,
        fixed: false
      }));
    
    report.fixes.typescript += errors.length;
    return errors;
  }
}

// Fonction pour corriger le formatage
async function fixFormatting(file) {
  try {
    execCommand(`prettier --write ${file}`, { ignoreError: true });
    report.fixes.formatting++;
    log.success(`Format corrigé: ${file}`);
    return true;
  } catch (error) {
    log.error(`Erreur de formatage: ${file}`);
    return false;
  }
}

// Fonction pour corriger le linting
async function fixLinting(file) {
  try {
    execCommand(`eslint --fix ${file}`, { ignoreError: true });
    report.fixes.linting++;
    log.success(`Lint corrigé: ${file}`);
    return true;
  } catch (error) {
    log.error(`Erreur de linting: ${file}`);
    return false;
  }
}

// Fonction pour corriger les imports
function fixImports(content, file) {
  let fixCount = 0;
  
  // Correction des imports relatifs
  content = content.replace(/from ['"]\.\/(?!\.)[^'"]+['"]/g, (match) => {
    fixCount++;
    return match.replace(/['"]\.\//, '"./');
  });

  // Correction des imports avec extension manquante
  content = content.replace(/from ['"]([^'"]+)['"]/g, (match, importPath) => {
    if (!importPath.startsWith('.')) return match;
    if (path.extname(importPath)) return match;
    
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    for (const ext of extensions) {
      const fullPath = path.resolve(path.dirname(file), importPath + ext);
      if (fs.existsSync(fullPath)) {
        fixCount++;
        return `from '${importPath}${ext}'`;
      }
    }
    return match;
  });

  report.fixes.imports += fixCount;
  return content;
}

// Fonction pour corriger un fichier
async function fixFile(file) {
  log.info(`\nAnalyse du fichier: ${file}`);
  
  let content = fs.readFileSync(file, 'utf8');
  let errors = [];
  let fixed = [];

  // 1. Correction des erreurs TypeScript courantes
  if (['.ts', '.tsx'].includes(path.extname(file))) {
    content = fixCommonTypeScriptErrors(content);
  }

  // 2. Correction des imports
  content = fixImports(content, file);

  // 3. Correction des erreurs de syntaxe courantes
  content = fixCommonSyntaxErrors(content);

  // 4. Correction du formatage
  await fixFormatting(file);

  // 5. Correction du linting
  await fixLinting(file);

  // 6. Vérification finale des erreurs TypeScript
  const remainingTsErrors = await checkTypeScriptErrors(file);
  errors = errors.concat(remainingTsErrors);

  // Sauvegarde des modifications
  fs.writeFileSync(file, content, 'utf8');

  // Mise à jour du rapport
  if (errors.length > 0 || fixed.length > 0) {
    report.summary.filesWithErrors++;
    report.summary.totalErrors += errors.length;
    report.details.push({
      file,
      errors,
      fixed,
      timestamp: new Date().toISOString()
    });
  }

  return errors;
}

// Nouvelle fonction pour corriger les erreurs TypeScript courantes
function fixCommonTypeScriptErrors(content) {
  // Correction des 'any' implicites
  content = content.replace(/function\s+(\w+)\s*\(([\w\s,]*)\)\s*{/g, (match, name, params) => {
    if (!params.includes(':')) {
      const typedParams = params.split(',')
        .map(param => param.trim())
        .filter(param => param)
        .map(param => `${param}: any`)
        .join(', ');
      return `function ${name}(${typedParams}) {`;
    }
    return match;
  });

  // Correction des déclarations de variables sans type
  content = content.replace(/let\s+(\w+)\s*=/g, 'let $1: any =');
  content = content.replace(/const\s+(\w+)\s*=/g, 'const $1: any =');

  // Ajout des types de retour manquants pour les fonctions
  content = content.replace(/async\s+function\s+(\w+)\s*\((.*?)\)\s*{/g, 
    'async function $1($2): Promise<any> {');

  // Correction des erreurs d'interface
  content = content.replace(/interface\s+(\w+)\s*{\s*([^}]*)\s*}/g, (match, name, props) => {
    const fixedProps = props.split('\n')
      .map(line => {
        if (line.trim() && !line.includes(':')) {
          return line.trim().replace(/(\w+)(\s*;|\s*$)/, '$1: any$2');
        }
        return line;
      })
      .join('\n');
    return `interface ${name} {\n${fixedProps}\n}`;
  });

  return content;
}

// Nouvelle fonction pour corriger les erreurs de syntaxe courantes
function fixCommonSyntaxErrors(content) {
  // Correction des points-virgules manquants
  content = content.replace(/([^;{}\n])\n/g, '$1;\n');

  // Correction des accolades manquantes dans les if
  content = content.replace(/if\s*\((.*?)\)\s*(?!{)(.*?)(?!\s*{)$/gm, 
    'if ($1) { $2 }');

  // Correction des déclarations de variables multiples
  content = content.replace(/var\s+([^;]+,.*?);/g, (match, vars) => {
    return vars.split(',')
      .map(v => `var ${v.trim()};`)
      .join('\n');
  });

  // Correction des promesses non gérées
  content = content.replace(/(\w+\s*\.\s*then\s*\(.*?\))\s*(?!\.catch)/g, 
    '$1.catch(error => console.error(error))');

  // Correction des comparaisons non strictes
  content = content.replace(/([^=!])(==|!=)([^=])/g, '$1$2=$3');

  return content;
}

// Fonction principale
async function main() {
  const startTime = Date.now();
  log.info('🔍 Démarrage du scan automatique...');

  // Installation des dépendances nécessaires
  log.info('📦 Installation des dépendances...');
  execCommand('npm install --save-dev prettier eslint typescript @typescript-eslint/parser @typescript-eslint/eslint-plugin', { ignoreError: true });

  // Recherche des fichiers
  const files = findFiles();
  log.info(`📁 ${files.length} fichiers trouvés`);

  // Analyse et correction de chaque fichier
  for (const file of files) {
    const errors = await fixFile(file);
    
    if (errors.length > 0) {
      log.warning(`⚠️  ${errors.length} erreurs trouvées dans ${file}`);
      errors.forEach(error => log.warning(`   - ${error.message}`));
    }
  }

  // Mise à jour du rapport
  report.duration = Date.now() - startTime;
  report.summary.fixedErrors = 
    report.fixes.formatting + 
    report.fixes.linting + 
    report.fixes.typescript + 
    report.fixes.imports;

  // Sauvegarde du rapport
  saveReport();

  // Rapport final dans la console
  if (report.summary.totalErrors > 0) {
    log.warning(`\n⚠️  Total: ${report.summary.totalErrors} erreurs trouvées, ${report.summary.fixedErrors} corrigées`);
  } else {
    log.success('\n✅ Aucune erreur trouvée');
  }

  // Reconstruction du projet
  log.info('\n🔨 Reconstruction du projet...');
  try {
    execCommand('npm run build', { ignoreError: true });
    log.success('✅ Reconstruction réussie');
  } catch (error) {
    log.error('❌ Erreur lors de la reconstruction');
  }
}

// Exécution du script
main().catch(error => {
  log.error(`\n❌ Erreur fatale: ${error.message}`);
  process.exit(1);
});