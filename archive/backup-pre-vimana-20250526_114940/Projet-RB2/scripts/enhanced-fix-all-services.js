#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const colors = require('colors');

// Configuration des services
const SERVICES = {
  backend: ['Backend', 'API', 'Authentication'],
  frontend: ['Frontend'],
  storage: ['Decentralized-Storage']
};

// Fonction utilitaire pour le logging
function log(message, color = 'white') {
  console.log(colors[color](message));
}

// Installation des dépendances globales
async function installGlobalDependencies() {
  const dependencies = [
    'turbo',
    'typescript',
    '@types/node',
    'ts-node'
  ];

  log('\nInstalling global dependencies...', 'cyan');
  try {
    execSync(`npm install -g ${dependencies.join(' ')}`, { stdio: 'inherit' });
    log('✅ Global dependencies installed', 'green');
  } catch (error) {
    log(`❌ Error installing global dependencies: ${error.message}`, 'red');
  }
}

// Fonction pour corriger un service
async function fixService(servicePath, serviceName) {
  try {
    if (!fs.existsSync(servicePath)) {
      log(`Service ${serviceName} not found, skipping...`, 'yellow');
      return false;
    }

    log(`\n🔧 Processing ${serviceName}...`, 'cyan');

    // Installation des dépendances
    try {
      execSync('npm install', { 
        cwd: servicePath,
        stdio: 'inherit'
      });
      log('✅ Dependencies installed', 'green');
    } catch (error) {
      log(`⚠️ Warning during npm install: ${error.message}`, 'yellow');
    }

    // Correction des vulnérabilités
    try {
      execSync('npm audit fix', { 
        cwd: servicePath,
        stdio: 'inherit'
      });
      log('✅ Vulnerabilities fixed', 'green');
    } catch (error) {
      log(`⚠️ Warning during vulnerability fix: ${error.message}`, 'yellow');
    }

    // Build du service
    try {
      if (serviceName === 'Decentralized-Storage') {
        execSync('npx turbo build', { 
          cwd: servicePath,
          stdio: 'inherit'
        });
      } else {
        execSync('npm run build', { 
          cwd: servicePath,
          stdio: 'inherit'
        });
      }
      log(`✅ Successfully built ${serviceName}`, 'green');
    } catch (error) {
      log(`❌ Build failed for ${serviceName}: ${error.message}`, 'red');
    }

    return true;
  } catch (error) {
    log(`❌ Error fixing ${serviceName}: ${error.message}`, 'red');
    return false;
  }
}

// Fonction principale
async function main() {
  try {
    // Installation des dépendances globales
    await installGlobalDependencies();

    // Traitement de chaque service
    for (const [serviceType, services] of Object.entries(SERVICES)) {
      for (const service of services) {
        const servicePath = path.join(process.cwd(), service);
        await fixService(servicePath, service);
      }
    }

    log('\n✅ All services processed', 'green');
  } catch (error) {
    log(`\n❌ Fatal error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Exécution du script avec gestion des erreurs
main().catch(error => {
  log(`\n❌ Unexpected error: ${error.message}`, 'red');
  process.exit(1);
});