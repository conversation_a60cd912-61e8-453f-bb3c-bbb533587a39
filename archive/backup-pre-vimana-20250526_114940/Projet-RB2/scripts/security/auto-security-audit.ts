import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';

interface SecurityIssue {
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  category: string;
  description: string;
  location?: string;
  autoFixed: boolean;
  recommendation?: string;
}

interface SecurityReport {
  timestamp: string;
  issues: SecurityIssue[];
  summary: {
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
    autoFixed: number;
  };
  score: number;
}

class SecurityAuditor {
  private report: SecurityReport;
  private readonly outputDir: string;

  constructor() {
    this.outputDir = path.join(process.cwd(), 'security-reports', new Date().toISOString());
    fs.mkdirSync(this.outputDir, { recursive: true });
    
    this.report = {
      timestamp: new Date().toISOString(),
      issues: [],
      summary: {
        total: 0,
        critical: 0,
        high: 0,
        medium: 0,
        low: 0,
        autoFixed: 0
      },
      score: 100
    };
  }

  async runFullAudit(): Promise<void> {
    console.log('Starting comprehensive security audit...');

    try {
      // 1. Dependency Analysis
      await this.auditDependencies();

      // 2. Static Code Analysis
      await this.staticCodeAnalysis();

      // 3. Configuration Analysis
      await this.auditSecurityConfigs();

      // 4. Secret Detection
      await this.detectSecrets();

      // 5. API Security Analysis
      await this.auditAPIEndpoints();

      // 6. Auto-fix where possible
      await this.autoFixIssues();

      // 7. Generate Final Report
      await this.generateReport();

    } catch (error) {
      console.error('Error during security audit:', error);
      throw error;
    }
  }

  private async auditDependencies(): Promise<void> {
    console.log('Auditing dependencies...');

    try {
      // npm audit
      const npmAudit = execSync('npm audit --json').toString();
      const npmIssues = JSON.parse(npmAudit);

      // Auto-fix low and medium severity issues
      execSync('npm audit fix');

      // Check remaining issues after auto-fix
      const remainingIssues = JSON.parse(execSync('npm audit --json').toString());

      this.addToReport(remainingIssues.map((issue: any) => ({
        severity: issue.severity.toUpperCase(),
        category: 'Dependencies',
        description: `Vulnerable package: ${issue.module_name}`,
        autoFixed: issue.fixable,
        recommendation: issue.recommendation
      })));

    } catch (error) {
      console.error('Error in dependency audit:', error);
    }
  }

  private async staticCodeAnalysis(): Promise<void> {
    console.log('Running static code analysis...');

    // ESLint security rules check
    try {
      execSync('eslint . --config security-eslintrc.json --format json');
    } catch (error) {
      if (error instanceof Error && 'stdout' in error) {
        try {
          const eslintIssues = JSON.parse((error as any).stdout);
          
          // Auto-fix what's possible
          execSync('eslint . --config security-eslintrc.json --fix');

          this.addToReport(eslintIssues.map((issue: any) => ({
            severity: this.mapESLintSeverity(issue.severity),
            category: 'Code Security',
            description: issue.message,
            location: `${issue.filePath}:${issue.line}`,
            autoFixed: issue.fixable
          })));
        } catch (parseError) {
          console.error('Error parsing ESLint output:', parseError);
        }
      } else {
        console.error('Unknown error occurred:', error);
      }
    }
  }

  private async auditSecurityConfigs(): Promise<void> {
    console.log('Auditing security configurations...');

    const securityConfigs = {
      'helmet': {
        pattern: /app\.use\(helmet/,
        fix: "app.use(helmet());",
        severity: 'HIGH'
      },
      'csrf': {
        pattern: /app\.use\(csrf/,
        fix: "app.use(csrf({ cookie: true }));",
        severity: 'HIGH'
      },
      'rateLimit': {
        pattern: /app\.use\(rateLimit/,
        fix: "app.use(rateLimit({ windowMs: 15 * 60 * 1000, max: 100 }));",
        severity: 'MEDIUM'
      }
    };

    for (const [name, config] of Object.entries(securityConfigs)) {
      const files = execSync('find . -type f -name "*.js" -o -name "*.ts"').toString().split('\n');

      for (const file of files) {
        if (!file) continue;

        const content = fs.readFileSync(file, 'utf8');
        if (!config.pattern.test(content)) {
          this.addToReport([{
            severity: config.severity as any,
            category: 'Security Configuration',
            description: `Missing ${name} middleware`,
            location: file,
            autoFixed: true,
            recommendation: `Add ${config.fix}`
          }]);

          // Auto-fix by adding the middleware
          const newContent = `${content}\n${config.fix}\n`;
          fs.writeFileSync(file, newContent);
        }
      }
    }
  }

  private async detectSecrets(): Promise<void> {
    console.log('Detecting secrets...');

    const secretPatterns = {
      'API Key': /(['"])(?:api[_-]?key|apikey)['"]\s*(?::|=>|=)\s*['"]([a-zA-Z0-9]{32,})[']/i,
      'Password': /(['"])password['"]\s*(?::|=>|=)\s*['"]([^'"]+)['"]/i,
      'Access Token': /(['"])?access[_-]?token(['"])?\s*(?::|=>|=)\s*['"]([a-zA-Z0-9._-]+)[']/i
    };

    const files = execSync('find . -type f -not -path "*/node_modules/*" -not -path "*/dist/*"').toString().split('\n');

    for (const file of files) {
      if (!file) continue;

      const content = fs.readFileSync(file, 'utf8');

      for (const [secretType, pattern] of Object.entries(secretPatterns)) {
        const matches = content.match(pattern);
        if (matches) {
          this.addToReport([{
            severity: 'CRITICAL',
            category: 'Secrets',
            description: `Hardcoded ${secretType} detected`,
            location: file,
            autoFixed: false,
            recommendation: 'Move sensitive data to environment variables'
          }]);
        }
      }
    }
  }

  private async auditAPIEndpoints(): Promise<void> {
    console.log('Auditing API endpoints...');

    const apiSecurityChecks = {
      'Input Validation': /req\.body|req\.params|req\.query/,
      'SQL Injection': /execute\s*\(|raw\s*\(/,
      'NoSQL Injection': /\$where|\$regex/,
      'Command Injection': /exec\s*\(|spawn\s*\(/
    };

    const files = execSync('find . -type f -name "*.js" -o -name "*.ts"').toString().split('\n');

    for (const file of files) {
      if (!file) continue;

      const content = fs.readFileSync(file, 'utf8');

      for (const [checkName, pattern] of Object.entries(apiSecurityChecks)) {
        if (pattern.test(content)) {
          this.addToReport([{
            severity: 'HIGH',
            category: 'API Security',
            description: `Potential ${checkName} vulnerability`,
            location: file,
            autoFixed: false,
            recommendation: this.getAPISecurityRecommendation(checkName)
          }]);
        }
      }
    }
  }

  private async autoFixIssues(): Promise<void> {
    console.log('Attempting to auto-fix security issues...');

    for (const issue of this.report.issues) {
      if (this.canAutoFix(issue)) {
        await this.fixIssue(issue);
        issue.autoFixed = true;
        this.report.summary.autoFixed++;
      }
    }
  }

  private async generateReport(): Promise<void> {
    console.log('Generating security report...');

    // Calculate security score
    this.calculateSecurityScore();

    // Generate HTML report
    const htmlReport = this.generateHTMLReport();
    fs.writeFileSync(path.join(this.outputDir, 'security-report.html'), htmlReport);

    // Generate JSON report
    fs.writeFileSync(
      path.join(this.outputDir, 'security-report.json'),
      JSON.stringify(this.report, null, 2)
    );

    console.log(`Security report generated at ${this.outputDir}`);
  }

  private addToReport(issues: SecurityIssue[]): void {
    this.report.issues.push(...issues);
    
    for (const issue of issues) {
      this.report.summary.total++;
      this.report.summary[issue.severity.toLowerCase() as keyof typeof this.report.summary]++;
    }
  }

  private calculateSecurityScore(): void {
    const weights = {
      CRITICAL: 10,
      HIGH: 5,
      MEDIUM: 3,
      LOW: 1
    };

    let deductions = 0;
    for (const issue of this.report.issues) {
      if (!issue.autoFixed) {
        deductions += weights[issue.severity];
      }
    }

    this.report.score = Math.max(0, 100 - deductions);
  }

  private generateHTMLReport(): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Security Audit Report</title>
          <style>
            /* Add your CSS styles here */
          </style>
        </head>
        <body>
          <h1>Security Audit Report</h1>
          <h2>Summary</h2>
          <ul>
            <li>Total Issues: ${this.report.summary.total}</li>
            <li>Critical: ${this.report.summary.critical}</li>
            <li>High: ${this.report.summary.high}</li>
            <li>Medium: ${this.report.summary.medium}</li>
            <li>Low: ${this.report.summary.low}</li>
            <li>Auto-fixed: ${this.report.summary.autoFixed}</li>
            <li>Security Score: ${this.report.score}/100</li>
          </ul>
          <h2>Issues</h2>
          <table>
            <!-- Add table content here -->
          </table>
        </body>
      </html>
    `;
  }

  private canAutoFix(issue: SecurityIssue): boolean {
    // Define which types of issues can be auto-fixed
    const autoFixableCategories = ['Dependencies', 'Security Configuration'];
    return autoFixableCategories.includes(issue.category);
  }

  private async fixIssue(issue: SecurityIssue): Promise<void> {
    switch (issue.category) {
      case 'Dependencies':
        execSync('npm audit fix');
        break;
      case 'Security Configuration':
        // Add security middleware
        if (issue.location) {
          const content = fs.readFileSync(issue.location, 'utf8');
          const newContent = this.addSecurityMiddleware(content, issue);
          fs.writeFileSync(issue.location, newContent);
        }
        break;
    }
  }

  private addSecurityMiddleware(content: string, issue: SecurityIssue): string {
    // Add appropriate security middleware based on the issue
    const middlewareMap: { [key: string]: string } = {
      'helmet': "app.use(helmet());",
      'csrf': "app.use(csrf({ cookie: true }));",
      'rateLimit': "app.use(rateLimit({ windowMs: 15 * 60 * 1000, max: 100 }));"
    };

    const middleware = middlewareMap[issue.description.toLowerCase().split(' ')[1]];
    if (middleware) {
      return `${content}\n${middleware}\n`;
    }
    return content;
  }

  private getAPISecurityRecommendation(checkName: string): string {
    const recommendations: { [key: string]: string } = {
      'Input Validation': 'Use input validation middleware like express-validator',
      'SQL Injection': 'Use parameterized queries or an ORM',
      'NoSQL Injection': 'Sanitize inputs and use mongoose schema validation',
      'Command Injection': 'Avoid executing system commands with user input'
    };
    return recommendations[checkName] || 'Review and fix the security issue';
  }

  private mapESLintSeverity(severity: number): 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' {
    const severityMap: { [key: number]: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' } = {
      2: 'HIGH',
      1: 'MEDIUM',
      0: 'LOW'
    };
    return severityMap[severity] || 'MEDIUM';
  }
}

// Usage
const auditor = new SecurityAuditor();
auditor.runFullAudit().catch(console.error);