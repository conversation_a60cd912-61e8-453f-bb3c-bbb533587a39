#!/bin/bash

# Feature Security Review Script
FEATURE_BRANCH=$1
REPORT_DIR="security-reports"

# Run security checks
echo "Running security review for feature: $FEATURE_BRANCH"

# Static Analysis
echo "Running SAST..."
npm run security:sast -- --branch $FEATURE_BRANCH --output $REPORT_DIR/sast.json

# Dependencies check
echo "Checking dependencies..."
npm audit --json > $REPORT_DIR/dependencies.json

# Custom security rules
echo "Validating security rules..."
npm run security:rules-check -- --config security-rules.json

# Generate report
echo "Generating security report..."
node scripts/generate-security-report.js \
  --sast $REPORT_DIR/sast.json \
  --deps $REPORT_DIR/dependencies.json \
  --output $REPORT_DIR/security-review.md

echo "Security review complete. Report available at $REPORT_DIR/security-review.md"