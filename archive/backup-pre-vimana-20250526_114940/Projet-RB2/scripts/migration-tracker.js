#!/usr/bin/env node

/**
 * Script de suivi de la migration vers l'architecture atomique
 * 
 * Ce script analyse les composants existants et crée un rapport de progression
 * pour suivre l'état de la migration vers l'architecture atomique.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Récupérer le chemin du répertoire courant en ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const FRONTEND_PATH = path.join(process.cwd(), 'frontend');
const COMPONENTS_PATH = path.join(FRONTEND_PATH, 'src', 'components');
const ATOMIC_PATH = path.join(FRONTEND_PATH, 'src', 'atomic');

// Couleurs pour la console
const COLORS = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  DIM: '\x1b[2m',
  UNDERSCORE: '\x1b[4m',
  BLINK: '\x1b[5m',
  REVERSE: '\x1b[7m',
  HIDDEN: '\x1b[8m',
  FG_BLACK: '\x1b[30m',
  FG_RED: '\x1b[31m',
  FG_GREEN: '\x1b[32m',
  FG_YELLOW: '\x1b[33m',
  FG_BLUE: '\x1b[34m',
  FG_MAGENTA: '\x1b[35m',
  FG_CYAN: '\x1b[36m',
  FG_WHITE: '\x1b[37m',
  BG_BLACK: '\x1b[40m',
  BG_RED: '\x1b[41m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_BLUE: '\x1b[44m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',
  BG_WHITE: '\x1b[47m'
};

// Niveaux de complexité
const COMPLEXITY_THRESHOLD = {
  LOW: 50,
  MEDIUM: 150,
  HIGH: 300
};

/**
 * Évalue la complexité d'un composant
 * @param {string} filePath Chemin du fichier
 * @returns {number} Score de complexité
 */
function evaluateComplexity(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    const lines = content.split('\n').length;
    const imports = (content.match(/import .+ from/g) || []).length;
    return lines + (imports * 5); // Formule simple
  } catch (e) {
    return 0;
  }
}

/**
 * Détecte si un fichier est un composant React
 * @param {string} filePath Chemin du fichier
 * @returns {boolean} True si c'est un composant React
 */
function isReactComponent(filePath) {
  if (!filePath.endsWith('.tsx') && !filePath.endsWith('.jsx')) {
    return false;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    return (
      content.includes('import React') || 
      content.includes('from "react"') || 
      content.includes("from 'react'")
    ) && (
      content.includes('function') || 
      content.includes('=>') || 
      content.includes('class') ||
      content.includes('export const') ||
      content.includes('export default')
    );
  } catch (e) {
    return false;
  }
}

/**
 * Cherche récursivement tous les composants dans un répertoire
 * @param {string} dirPath Chemin du répertoire
 * @param {Array} results Résultats accumulés
 * @returns {Array} Liste des composants
 */
function findAllComponents(dirPath, results = []) {
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      findAllComponents(fullPath, results);
    } else if (isReactComponent(fullPath)) {
      results.push(fullPath);
    }
  }

  return results;
}

/**
 * Analyse les composants atomiques
 * @returns {Object} Statistiques des composants atomiques
 */
function analyzeAtomicComponents() {
  const atomicComponents = {
    atoms: [],
    molecules: [],
    organisms: [],
    templates: [],
    pages: []
  };
  
  const atomicTypes = Object.keys(atomicComponents);
  
  for (const type of atomicTypes) {
    const typePath = path.join(ATOMIC_PATH, type);
    
    if (fs.existsSync(typePath)) {
      const components = fs.readdirSync(typePath, { withFileTypes: true });
      
      for (const comp of components) {
        if (comp.isDirectory()) {
          const compPath = path.join(typePath, comp.name);
          const files = fs.readdirSync(compPath);
          
          const mainFile = files.find(f => 
            (f.endsWith('.tsx') || f.endsWith('.jsx')) && 
            !f.includes('.test') && 
            !f.includes('.stories')
          );
          
          if (mainFile) {
            const filePath = path.join(compPath, mainFile);
            const hasTests = files.some(f => f.includes('.test'));
            const hasStory = files.some(f => f.includes('.stories'));
            const hasDocs = files.some(f => f.includes('.md')) || hasStory;
            
            atomicComponents[type].push({
              name: comp.name,
              path: filePath,
              hasTests,
              hasStory,
              hasDocs,
              complexity: evaluateComplexity(filePath)
            });
          }
        }
      }
    }
  }
  
  return atomicComponents;
}

/**
 * Analyse les composants existants dans /components
 * @returns {Array} Liste des composants non migrés
 */
function analyzeExistingComponents() {
  const components = [];
  
  if (!fs.existsSync(COMPONENTS_PATH)) {
    return components;
  }
  
  const allComponentFiles = findAllComponents(COMPONENTS_PATH);
  
  for (const filePath of allComponentFiles) {
    const relativePath = path.relative(COMPONENTS_PATH, filePath);
    const fileName = path.basename(filePath);
    const name = path.basename(fileName, path.extname(fileName));
    
    // Vérifier si ce composant a déjà été migré
    const isMigrated = isComponentMigrated(name);
    
    if (!isMigrated) {
      const directory = path.dirname(relativePath);
      const testFilePath = path.join(path.dirname(filePath), `${name}.test.tsx`);
      const storyFilePath = path.join(path.dirname(filePath), `${name}.stories.tsx`);
      
      components.push({
        name,
        path: filePath,
        relativePath,
        directory,
        hasTests: fs.existsSync(testFilePath),
        hasStory: fs.existsSync(storyFilePath),
        hasDocs: false, // Difficile à déterminer pour les anciens composants
        complexity: evaluateComplexity(filePath)
      });
    }
  }
  
  return components;
}

/**
 * Vérifie si un composant est déjà migré
 * @param {string} componentName Nom du composant
 * @returns {boolean} True si le composant est migré
 */
function isComponentMigrated(componentName) {
  const atomicTypes = ['atoms', 'molecules', 'organisms', 'templates', 'pages'];
  
  for (const type of atomicTypes) {
    const compPath = path.join(ATOMIC_PATH, type, componentName);
    if (fs.existsSync(compPath)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Génère un rapport de progression de la migration
 */
function generateMigrationReport() {
  console.log(`${COLORS.FG_BLUE}${COLORS.BRIGHT}===========================================${COLORS.RESET}`);
  console.log(`${COLORS.FG_BLUE}${COLORS.BRIGHT}  RAPPORT DE MIGRATION - ARCHITECTURE ATOMIQUE${COLORS.RESET}`);
  console.log(`${COLORS.FG_BLUE}${COLORS.BRIGHT}===========================================${COLORS.RESET}\n`);
  
  // Analyser les composants atomiques
  const atomicComponents = analyzeAtomicComponents();
  
  // Analyser les composants existants
  const existingComponents = analyzeExistingComponents();
  
  // Calculer les statistiques
  const totalAtomic = Object.values(atomicComponents).reduce((sum, arr) => sum + arr.length, 0);
  const totalExisting = existingComponents.length;
  const total = totalAtomic + totalExisting;
  
  const completionPercentage = Math.round((totalAtomic / total) * 100) || 0;
  
  // Afficher les statistiques générales
  console.log(`${COLORS.FG_GREEN}${COLORS.BRIGHT}PROGRESSION: ${completionPercentage}% (${totalAtomic}/${total})${COLORS.RESET}`);
  console.log(`${COLORS.FG_YELLOW}Composants en attente: ${existingComponents.length}${COLORS.RESET}\n`);
  
  // Afficher la répartition par type
  console.log(`${COLORS.FG_CYAN}${COLORS.BRIGHT}RÉPARTITION PAR TYPE:${COLORS.RESET}`);
  console.log(`${COLORS.FG_CYAN}Atomes: ${atomicComponents.atoms.length}${COLORS.RESET}`);
  console.log(`${COLORS.FG_CYAN}Molécules: ${atomicComponents.molecules.length}${COLORS.RESET}`);
  console.log(`${COLORS.FG_CYAN}Organismes: ${atomicComponents.organisms.length}${COLORS.RESET}`);
  console.log(`${COLORS.FG_CYAN}Templates: ${atomicComponents.templates.length}${COLORS.RESET}`);
  console.log(`${COLORS.FG_CYAN}Pages: ${atomicComponents.pages.length}${COLORS.RESET}\n`);
  
  // Afficher la qualité des composants
  const withTests = Object.values(atomicComponents).flat().filter(c => c.hasTests).length;
  const withStories = Object.values(atomicComponents).flat().filter(c => c.hasStory).length;
  const withDocs = Object.values(atomicComponents).flat().filter(c => c.hasDocs).length;
  
  console.log(`${COLORS.FG_MAGENTA}${COLORS.BRIGHT}QUALITÉ:${COLORS.RESET}`);
  console.log(`${COLORS.FG_MAGENTA}Composants avec tests: ${withTests} (${Math.round((withTests / totalAtomic) * 100)}%)${COLORS.RESET}`);
  console.log(`${COLORS.FG_MAGENTA}Composants avec stories: ${withStories} (${Math.round((withStories / totalAtomic) * 100)}%)${COLORS.RESET}`);
  console.log(`${COLORS.FG_MAGENTA}Composants avec documentation: ${withDocs} (${Math.round((withDocs / totalAtomic) * 100)}%)${COLORS.RESET}\n`);
  
  // Priorités de migration
  console.log(`${COLORS.FG_RED}${COLORS.BRIGHT}PRIORITÉS DE MIGRATION:${COLORS.RESET}`);
  
  // Trier par complexité
  const sortedByComplexity = [...existingComponents].sort((a, b) => a.complexity - b.complexity);
  
  if (sortedByComplexity.length > 0) {
    // Composants faciles (faible complexité)
    console.log(`${COLORS.FG_GREEN}${COLORS.BRIGHT}Composants faciles à migrer (recommandés pour commencer):${COLORS.RESET}`);
    sortedByComplexity
      .filter(c => c.complexity < COMPLEXITY_THRESHOLD.LOW)
      .slice(0, 5)
      .forEach(c => console.log(`  - ${c.name} (Complexité: ${c.complexity})`));
    
    // Composants moyens
    console.log(`\n${COLORS.FG_YELLOW}${COLORS.BRIGHT}Composants de complexité moyenne:${COLORS.RESET}`);
    sortedByComplexity
      .filter(c => c.complexity >= COMPLEXITY_THRESHOLD.LOW && c.complexity < COMPLEXITY_THRESHOLD.HIGH)
      .slice(0, 5)
      .forEach(c => console.log(`  - ${c.name} (Complexité: ${c.complexity})`));
    
    // Composants complexes
    console.log(`\n${COLORS.FG_RED}${COLORS.BRIGHT}Composants complexes (nécessitant une planification):${COLORS.RESET}`);
    sortedByComplexity
      .filter(c => c.complexity >= COMPLEXITY_THRESHOLD.HIGH)
      .slice(0, 5)
      .forEach(c => console.log(`  - ${c.name} (Complexité: ${c.complexity})`));
  }
  
  // Sauvegarder le rapport au format JSON
  const reportData = {
    date: new Date().toISOString(),
    statistics: {
      total,
      migrated: totalAtomic,
      pending: totalExisting,
      completionPercentage
    },
    atomicComponents,
    pendingComponents: existingComponents
  };
  
  const reportPath = path.join(process.cwd(), 'migration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  
  console.log(`\n${COLORS.FG_BLUE}${COLORS.BRIGHT}Rapport détaillé sauvegardé dans: migration-report.json${COLORS.RESET}`);
  console.log(`${COLORS.FG_BLUE}${COLORS.BRIGHT}===========================================${COLORS.RESET}\n`);
}

// Exécuter le rapport
generateMigrationReport(); 