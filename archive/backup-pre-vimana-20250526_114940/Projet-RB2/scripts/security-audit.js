#!/usr/bin/env node

/**
 * Script d'audit de sécurité
 * 
 * Ce script effectue un audit complet de sécurité selon les critères du roadmap:
 * - OWASP Top 10 scan
 * - Analyse statique (SonarQube-like)
 * - Simulation de pentesting
 * - Audit de configuration
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Définition des couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Répertoire racine du projet
const rootDir = path.resolve(process.cwd(), '..');

// Liste des vulnérabilités OWASP Top 10 2021
const owaspTop10 = [
  { id: 'A01:2021', name: 'Broken Access Control', description: 'Contrôles d\'accès défaillants' },
  { id: 'A02:2021', name: 'Cryptographic Failures', description: 'Défaillances cryptographiques' },
  { id: 'A03:2021', name: 'Injection', description: 'Injections (SQL, NoSQL, OS, etc.)' },
  { id: 'A04:2021', name: 'Insecure Design', description: 'Conception non sécurisée' },
  { id: 'A05:2021', name: 'Security Misconfiguration', description: 'Mauvaise configuration de sécurité' },
  { id: 'A06:2021', name: 'Vulnerable & Outdated Components', description: 'Composants vulnérables et obsolètes' },
  { id: 'A07:2021', name: 'Identification & Authentication Failures', description: 'Défaillances d\'identification et d\'authentification' },
  { id: 'A08:2021', name: 'Software & Data Integrity Failures', description: 'Défaillances d\'intégrité des logiciels et des données' },
  { id: 'A09:2021', name: 'Security Logging & Monitoring Failures', description: 'Défaillances de journalisation et de surveillance' },
  { id: 'A10:2021', name: 'Server-Side Request Forgery', description: 'Falsification de requête côté serveur' }
];

// Patterns de vulnérabilités à rechercher
const vulnerabilityPatterns = {
  'A01:2021': [
    { pattern: '\\b(role|permission|access).*=\\s*req\\.(body|query|params)', description: 'Utilisation directe des entrées utilisateur pour le contrôle d\'accès' },
    { pattern: '\\badmin\\s*=\\s*true', description: 'Attribution directe de privilèges d\'administration' },
    { pattern: '\\brole\\s*=\\s*[\'"]admin[\'"]', description: 'Attribution directe de rôle administrateur' }
  ],
  'A02:2021': [
    { pattern: 'createHash\\([\'"]md5[\'"]\\)', description: 'Utilisation de MD5 (déconseillé)' },
    { pattern: 'createHash\\([\'"]sha1[\'"]\\)', description: 'Utilisation de SHA1 (déconseillé)' },
    { pattern: 'crypto\\.createCipher\\(', description: 'Utilisation de createCipher déprécié (utiliser createCipheriv)' },
    { pattern: '\\biv\\s*=\\s*null', description: 'IV non défini ou null pour chiffrement' }
  ],
  'A03:2021': [
    { pattern: '\\bdb\\.(query|execute)\\([^,]*\\$\\{', description: 'Potentielle injection SQL via interpolation de chaînes' },
    { pattern: '\\bexec\\([^,]*\\$\\{', description: 'Potentielle injection OS via interpolation de chaînes' },
    { pattern: '\\beval\\(', description: 'Utilisation de eval() (risqué)' },
    { pattern: 'new\\s+Function\\(', description: 'Création dynamique de fonction (risqué)' }
  ],
  'A05:2021': [
    { pattern: '\\bprocess\\.env\\.NODE_ENV\\s*!==\\s*[\'"]production[\'"]', description: 'Mode de production potentiellement non activé' },
    { pattern: '\\bconsole\\.log\\(\\s*err', description: 'Affichage d\'erreurs en production' },
    { pattern: '\\b(user|password|secret|key|token)\\s*:\\s*[\'"][^\'"]+[\'"]', description: 'Potentielle fuite de secrets en clair' }
  ],
  'A06:2021': [
    { pattern: 'package\\.json', special: 'dependencies-check', description: 'Dépendances obsolètes ou vulnérables' }
  ],
  'A07:2021': [
    { pattern: '\\bpassword\\s*===\\s*', description: 'Comparaison simple de mot de passe (risque d\'attaque timing)' },
    { pattern: '\\breq\\.session\\.user\\s*=', description: 'Modification directe de session utilisateur' },
    { pattern: '\\bjwt\\.sign\\(\\s*{\\s*admin\\s*:', description: 'Inclusion de privilèges administratifs dans JWT' }
  ],
  'A08:2021': [
    { pattern: '\\bobject\\.assign\\(\\s*{},\\s*req\\.body', description: 'Copie directe des données utilisateur sans validation' },
    { pattern: '\\bdeserialize\\(', description: 'Désérialisation potentiellement non sécurisée' }
  ],
  'A09:2021': [
    { pattern: '\\bcatch\\s*\\([^)]*\\)\\s*{\\s*}', description: 'Catch vide - pas de journalisation d\'erreur' },
    { pattern: '\\bconsole\\.error\\(', description: 'Utilisation de console.error au lieu d\'un vrai système de logging' }
  ],
  'A10:2021': [
    { pattern: '\\b(axios|request|fetch|http\\.get)\\(\\s*[^,]*\\$\\{', description: 'Potentielle SSRF via URL dynamique' },
    { pattern: '\\b(axios|request|fetch|http\\.get)\\(\\s*req\\.(body|query|params)', description: 'Potentielle SSRF via entrée utilisateur' }
  ]
};

// Configuration pour l'analyse de la configuration de sécurité
const securityConfigChecks = [
  {
    name: 'Helmet',
    pattern: 'app\\.use\\(\\s*helmet',
    required: true,
    description: 'Utilisation de Helmet pour les en-têtes HTTP sécurisés'
  },
  {
    name: 'CORS',
    pattern: 'app\\.use\\(\\s*cors',
    recommendedConfig: {
      pattern: '\\{\\s*origin\\s*:',
      description: 'Configuration CORS spécifique plutôt que par défaut'
    },
    description: 'Utilisation de CORS pour contrôler les accès cross-origin'
  },
  {
    name: 'Rate Limiting',
    pattern: 'app\\.use\\(\\s*(rate-?limit|express-rate-limit)',
    recommended: true,
    description: 'Protection contre les attaques par force brute'
  },
  {
    name: 'Content Security Policy',
    pattern: '(helmet\\(\\{\\s*contentSecurityPolicy|csp)',
    recommended: true,
    description: 'Protection contre les attaques XSS via CSP'
  },
  {
    name: 'CSRF Protection',
    pattern: 'app\\.use\\(\\s*csurf',
    recommended: true,
    description: 'Protection contre les attaques CSRF'
  },
  {
    name: 'Cookie Security',
    pattern: '(cookie-parser|express-session|cookie)',
    recommendedConfig: {
      pattern: '\\{\\s*(secure|httpOnly|sameSite)\\s*:',
      description: 'Configuration sécurisée des cookies'
    },
    description: 'Utilisation de cookies sécurisés'
  },
  {
    name: 'SQL Injection Protection',
    pattern: 'parameterized|prepared\\s+statement',
    recommended: true,
    description: 'Protection contre les injections SQL via requêtes paramétrées'
  },
  {
    name: 'Environment Variables',
    pattern: 'require\\([\'"]dotenv[\'"]\\)',
    recommended: true,
    description: 'Utilisation de variables d\'environnement pour les configurations sensibles'
  },
  {
    name: 'Error Handling',
    pattern: 'app\\.use\\(\\s*\\([\'"]error[\'"]|app\\.use\\(\\s*\\([^,)]*err\\s*,',
    required: true,
    description: 'Middleware de gestion des erreurs pour éviter la fuite d\'informations'
  }
];

// Fonction pour exécuter une commande shell et capturer la sortie
function execCommand(command, options = {}) {
  try {
    return execSync(command, { 
      encoding: 'utf8', 
      ...options
    });
  } catch (error) {
    // Pour certaines commandes, un code de sortie non-zéro est attendu
    if (options.ignoreError) {
      return error.stdout;
    }
    console.error(`${colors.red}Erreur lors de l'exécution de la commande: ${command}${colors.reset}`);
    console.error(error.message);
    return '';
  }
}

// Fonction pour générer un rapport markdown
function generateMarkdownReport(data) {
  let markdown = `# Rapport d'Audit de Sécurité - ${new Date().toLocaleDateString()}\n\n`;
  
  // Résumé exécutif
  markdown += `## Résumé\n\n`;
  markdown += `- **Vulnérabilités critiques**: ${data.criticalCount}\n`;
  markdown += `- **Vulnérabilités majeures**: ${data.majorCount}\n`;
  markdown += `- **Vulnérabilités mineures**: ${data.minorCount}\n`;
  markdown += `- **Score de sécurité global**: ${calculateSecurityScore(data)}/100\n\n`;
  
  // OWASP Top 10
  markdown += `## Analyse OWASP Top 10\n\n`;
  
  // Tableau récapitulatif
  markdown += `| Catégorie | Description | Statut | Vulnérabilités |\n`;
  markdown += `|-----------|-------------|--------|----------------|\n`;
  
  owaspTop10.forEach(category => {
    const vulnerabilities = data.vulnerabilities.filter(v => v.category === category.id);
    const status = vulnerabilities.length === 0 
      ? '✅ Aucun problème détecté' 
      : `❌ ${vulnerabilities.length} problème(s) détecté(s)`;
    
    markdown += `| **${category.id}** ${category.name} | ${category.description} | ${status} | ${vulnerabilities.length} |\n`;
  });
  
  markdown += `\n`;
  
  // Détails des vulnérabilités
  markdown += `## Détails des vulnérabilités\n\n`;
  
  owaspTop10.forEach(category => {
    const vulnerabilities = data.vulnerabilities.filter(v => v.category === category.id);
    
    if (vulnerabilities.length > 0) {
      markdown += `### ${category.id}: ${category.name}\n\n`;
      
      vulnerabilities.forEach((vuln, index) => {
        markdown += `#### ${index + 1}. ${vuln.description}\n\n`;
        markdown += `- **Sévérité**: ${vuln.severity}\n`;
        markdown += `- **Fichier**: \`${vuln.file}\`\n`;
        markdown += `- **Ligne**: ${vuln.line}\n`;
        markdown += `- **Code**: \`${vuln.code.trim()}\`\n\n`;
        
        if (vuln.remediation) {
          markdown += `**Recommandation**: ${vuln.remediation}\n\n`;
        }
      });
    }
  });
  
  // Configuration de sécurité
  markdown += `## Configuration de sécurité\n\n`;
  markdown += `| Configuration | Statut | Description |\n`;
  markdown += `|---------------|--------|-------------|\n`;
  
  data.configChecks.forEach(check => {
    const status = check.found
      ? check.configIssue 
        ? `⚠️ Configuration à améliorer` 
        : `✅ Implémenté correctement`
      : check.required 
        ? `❌ Manquant (requis)` 
        : `⚠️ Recommandé mais manquant`;
    
    markdown += `| **${check.name}** | ${status} | ${check.description} |\n`;
  });
  
  // Recommandations
  markdown += `\n## Recommandations\n\n`;
  
  const criticalVulns = data.vulnerabilities.filter(v => v.severity === 'Critique');
  const majorVulns = data.vulnerabilities.filter(v => v.severity === 'Majeure');
  const requiredConfigs = data.configChecks.filter(c => c.required && !c.found);
  
  // Recommandations critiques
  if (criticalVulns.length > 0 || requiredConfigs.length > 0) {
    markdown += `### Actions prioritaires\n\n`;
    
    if (criticalVulns.length > 0) {
      markdown += `1. **Corriger les vulnérabilités critiques**:\n`;
      criticalVulns.slice(0, 5).forEach((vuln, index) => {
        markdown += `   - ${vuln.category} - ${vuln.description} dans \`${vuln.file}\`\n`;
      });
      markdown += `\n`;
    }
    
    if (requiredConfigs.length > 0) {
      markdown += `2. **Implémentation des configurations requises**:\n`;
      requiredConfigs.forEach(config => {
        markdown += `   - ${config.name}: ${config.description}\n`;
      });
      markdown += `\n`;
    }
  }
  
  // Recommandations générales
  markdown += `### Bonnes pratiques à mettre en place\n\n`;
  markdown += `1. **Mettre à jour régulièrement les dépendances**\n`;
  markdown += `2. **Implémenter une revue de code avec focus sécurité**\n`;
  markdown += `3. **Mettre en place un processus de tests de sécurité continu**\n`;
  markdown += `4. **Former les développeurs aux bonnes pratiques de sécurité**\n`;
  
  return markdown;
}

// Fonction pour calculer un score global de sécurité (0-100)
function calculateSecurityScore(data) {
  // Base de 100 points
  let score = 100;
  
  // Pénalités pour les vulnérabilités
  score -= data.criticalCount * 10; // -10 points par vulnérabilité critique
  score -= data.majorCount * 5;     // -5 points par vulnérabilité majeure
  score -= data.minorCount * 1;     // -1 point par vulnérabilité mineure
  
  // Pénalités pour les configurations manquantes
  data.configChecks.forEach(check => {
    if (!check.found && check.required) {
      score -= 10; // -10 points par configuration requise manquante
    } else if (!check.found && check.recommended) {
      score -= 5;  // -5 points par configuration recommandée manquante
    } else if (check.found && check.configIssue) {
      score -= 3;  // -3 points par configuration présente mais mal configurée
    }
  });
  
  // Limiter le score entre 0 et 100
  return Math.max(0, Math.min(100, score));
}

// Fonction pour rechercher des vulnérabilités dans le code
async function scanCodeForVulnerabilities(directory) {
  console.log(`\n${colors.blue}Analyse du code pour détecter des vulnérabilités...${colors.reset}`);
  
  const vulnerabilities = [];
  
  // Extensions de fichiers à analyser
  const extensions = ['.js', '.ts', '.jsx', '.tsx'];
  
  // Fonction récursive pour parcourir les répertoires
  function scanDirectory(dir) {
    try {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        // Ignorer node_modules, build, dist, etc.
        if (file === 'node_modules' || file === 'build' || file === 'dist' || file === '.git') {
          continue;
        }
        
        if (stat.isDirectory()) {
          scanDirectory(filePath);
        } else if (stat.isFile() && extensions.includes(path.extname(file))) {
          const fileContent = fs.readFileSync(filePath, 'utf8');
          const lines = fileContent.split('\n');
          
          // Vérifier chaque pattern de vulnérabilité
          for (const [category, patterns] of Object.entries(vulnerabilityPatterns)) {
            for (const pattern of patterns) {
              // Cas spécial pour la vérification des dépendances
              if (pattern.special === 'dependencies-check' && file === 'package.json') {
                try {
                  const packageJson = JSON.parse(fileContent);
                  const dependencies = { 
                    ...packageJson.dependencies || {}, 
                    ...packageJson.devDependencies || {} 
                  };
                  
                  // Vérifier les dépendances obsolètes avec npm audit
                  const auditOutput = execCommand('npm audit --json', { 
                    cwd: path.dirname(filePath),
                    ignoreError: true
                  });
                  
                  try {
                    const auditData = JSON.parse(auditOutput);
                    if (auditData.vulnerabilities) {
                      for (const [pkg, vuln] of Object.entries(auditData.vulnerabilities)) {
                        if (vuln.severity === 'critical' || vuln.severity === 'high') {
                          const severity = vuln.severity === 'critical' ? 'Critique' : 'Majeure';
                          
                          vulnerabilities.push({
                            category,
                            description: `Dépendance vulnérable: ${pkg}`,
                            severity,
                            file: filePath,
                            line: 1,
                            code: `"${pkg}": "${dependencies[pkg] || 'inconnue'}"`,
                            remediation: `Mettre à jour vers ${vuln.fixAvailable ? vuln.fixAvailable.version : 'la dernière version'} ou supérieure`
                          });
                        }
                      }
                    }
                  } catch (e) {
                    console.log(`${colors.yellow}Erreur lors de l'analyse de l'audit npm: ${e.message}${colors.reset}`);
                  }
                } catch (e) {
                  console.log(`${colors.yellow}Erreur lors de l'analyse du fichier package.json: ${e.message}${colors.reset}`);
                }
                continue;
              }
              
              // Recherche de patterns réguliers
              const regex = new RegExp(pattern.pattern, 'g');
              
              for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (regex.test(line)) {
                  // Déterminer la sévérité en fonction de la catégorie
                  let severity = 'Mineure';
                  if (['A01:2021', 'A02:2021', 'A03:2021', 'A07:2021'].includes(category)) {
                    severity = 'Critique';
                  } else if (['A05:2021', 'A06:2021', 'A08:2021', 'A10:2021'].includes(category)) {
                    severity = 'Majeure';
                  }
                  
                  vulnerabilities.push({
                    category,
                    description: pattern.description,
                    severity,
                    file: filePath,
                    line: i + 1,
                    code: line,
                    remediation: getRemediation(category, pattern.description)
                  });
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.log(`${colors.yellow}Erreur lors de l'analyse du répertoire ${dir}: ${error.message}${colors.reset}`);
    }
  }
  
  scanDirectory(directory);
  console.log(`${colors.green}Analyse du code terminée.${colors.reset}`);
  
  return vulnerabilities;
}

// Fonction pour obtenir des recommandations de correction
function getRemediation(category, description) {
  const remediations = {
    'A01:2021': {
      'Utilisation directe des entrées utilisateur pour le contrôle d\'accès': 
        'Valider les entrées utilisateur et utiliser une logique côté serveur pour déterminer les rôles et permissions.',
      'Attribution directe de privilèges d\'administration': 
        'Implémenter un système de gestion des rôles et de contrôle d\'accès basé sur les rôles (RBAC).',
      'Attribution directe de rôle administrateur': 
        'Utiliser un service d\'authentification centralisé pour gérer les rôles et permissions.'
    },
    'A02:2021': {
      'Utilisation de MD5 (déconseillé)': 
        'Remplacer MD5 par un algorithme moderne comme SHA-256 ou mieux, utiliser des fonctions spécifiques comme bcrypt/argon2 pour les mots de passe.',
      'Utilisation de SHA1 (déconseillé)': 
        'Remplacer SHA1 par un algorithme moderne comme SHA-256 ou SHA-3.',
      'Utilisation de createCipher déprécié (utiliser createCipheriv)': 
        'Utiliser crypto.createCipheriv() avec un vecteur d\'initialisation (IV) explicite.',
      'IV non défini ou null pour chiffrement': 
        'Générer un IV cryptographiquement sûr pour chaque opération de chiffrement.'
    },
    'A03:2021': {
      'Potentielle injection SQL via interpolation de chaînes': 
        'Utiliser des requêtes paramétrées ou des ORM pour se protéger contre les injections SQL.',
      'Potentielle injection OS via interpolation de chaînes': 
        'Utiliser child_process.execFile() au lieu de exec() ou valider strictement les entrées.',
      'Utilisation de eval() (risqué)': 
        'Éviter l\'utilisation de eval(). Chercher des alternatives plus sûres.',
      'Création dynamique de fonction (risqué)': 
        'Éviter new Function(). Utiliser des mécanismes plus sûrs pour le code dynamique.'
    },
    'A05:2021': {
      'Mode de production potentiellement non activé': 
        'S\'assurer que NODE_ENV est correctement configuré en production.',
      'Affichage d\'erreurs en production': 
        'Implémenter une gestion d\'erreurs qui affiche uniquement des messages génériques en production.',
      'Potentielle fuite de secrets en clair': 
        'Utiliser des variables d\'environnement ou des gestionnaires de secrets pour les informations sensibles.'
    },
    'A06:2021': {
      'Dépendances obsolètes ou vulnérables': 
        'Mettre à jour les dépendances vers les dernières versions stables et sécurisées.'
    },
    'A07:2021': {
      'Comparaison simple de mot de passe (risque d\'attaque timing)': 
        'Utiliser crypto.timingSafeEqual() ou les fonctions de comparaison sécurisées de bibliothèques comme bcrypt.',
      'Modification directe de session utilisateur': 
        'Implémenter une gestion de session sécurisée avec des vérifications d\'authentification appropriées.',
      'Inclusion de privilèges administratifs dans JWT': 
        'Stocker les rôles et permissions en base de données, pas dans le token JWT.'
    },
    'A08:2021': {
      'Copie directe des données utilisateur sans validation': 
        'Valider et assainir toutes les entrées utilisateur avant utilisation.',
      'Désérialisation potentiellement non sécurisée': 
        'Utiliser des formats sûrs comme JSON au lieu de la désérialisation native. Valider toutes les données désérialisées.'
    },
    'A09:2021': {
      'Catch vide - pas de journalisation d\'erreur': 
        'Implémenter une journalisation appropriée pour toutes les exceptions et erreurs.',
      'Utilisation de console.error au lieu d\'un vrai système de logging': 
        'Implémenter un système de logging structuré (comme Winston ou Pino) avec différents niveaux de journalisation.'
    },
    'A10:2021': {
      'Potentielle SSRF via URL dynamique': 
        'Valider et assainir toutes les URLs. Utiliser une liste d\'autorisation pour les domaines autorisés.',
      'Potentielle SSRF via entrée utilisateur': 
        'Ne jamais utiliser directement les entrées utilisateur pour les requêtes HTTP. Implémenter une validation stricte.'
    }
  };
  
  return remediations[category] && remediations[category][description]
    ? remediations[category][description]
    : 'Valider et nettoyer toutes les entrées utilisateur. Suivre les principes de sécurité par défaut.';
}

// Fonction pour vérifier la configuration de sécurité
async function checkSecurityConfiguration(directory) {
  console.log(`\n${colors.blue}Vérification de la configuration de sécurité...${colors.reset}`);
  
  const configResults = [];
  
  // Extensions de fichiers à analyser
  const extensions = ['.js', '.ts', '.jsx', '.tsx', '.json'];
  
  // Marquer tous les checks comme non trouvés initialement
  for (const check of securityConfigChecks) {
    configResults.push({
      ...check,
      found: false,
      configIssue: false
    });
  }
  
  // Fonction récursive pour parcourir les répertoires
  function scanDirectory(dir) {
    try {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        // Ignorer node_modules, build, dist, etc.
        if (file === 'node_modules' || file === 'build' || file === 'dist' || file === '.git') {
          continue;
        }
        
        if (stat.isDirectory()) {
          scanDirectory(filePath);
        } else if (stat.isFile() && extensions.includes(path.extname(file))) {
          const fileContent = fs.readFileSync(filePath, 'utf8');
          
          // Vérifier chaque configuration de sécurité
          for (const check of configResults) {
            if (!check.found) {
              const regex = new RegExp(check.pattern, 'g');
              
              if (regex.test(fileContent)) {
                check.found = true;
                
                // Vérifier si la configuration est correcte
                if (check.recommendedConfig) {
                  const configRegex = new RegExp(check.recommendedConfig.pattern, 'g');
                  if (!configRegex.test(fileContent)) {
                    check.configIssue = true;
                    check.configIssueDescription = check.recommendedConfig.description;
                  }
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.log(`${colors.yellow}Erreur lors de l'analyse du répertoire ${dir}: ${error.message}${colors.reset}`);
    }
  }
  
  scanDirectory(directory);
  console.log(`${colors.green}Vérification de la configuration terminée.${colors.reset}`);
  
  return configResults;
}

// Fonction principale d'audit de sécurité
async function performSecurityAudit() {
  console.log(`\n${colors.magenta}=== Audit de Sécurité - Retreat And Be ===${colors.reset}`);
  
  // Création du répertoire reports s'il n'existe pas
  const reportsDir = path.join(rootDir, 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir);
  }
  
  // 1. Scan du code pour les vulnérabilités
  const vulnerabilities = await scanCodeForVulnerabilities(rootDir);
  
  // 2. Vérification de la configuration de sécurité
  const configChecks = await checkSecurityConfiguration(rootDir);
  
  // 3. Préparer les données pour le rapport
  const auditData = {
    vulnerabilities,
    configChecks,
    criticalCount: vulnerabilities.filter(v => v.severity === 'Critique').length,
    majorCount: vulnerabilities.filter(v => v.severity === 'Majeure').length,
    minorCount: vulnerabilities.filter(v => v.severity === 'Mineure').length
  };
  
  // 4. Générer le rapport
  console.log(`\n${colors.blue}Génération du rapport d'audit de sécurité...${colors.reset}`);
  const reportMarkdown = generateMarkdownReport(auditData);
  
  // 5. Sauvegarder le rapport
  const reportPath = path.join(reportsDir, `security-audit-${new Date().toISOString().slice(0, 10)}.md`);
  fs.writeFileSync(reportPath, reportMarkdown);
  
  console.log(`${colors.green}Rapport d'audit de sécurité généré et sauvegardé: ${reportPath}${colors.reset}`);
  
  // 6. Afficher un résumé
  console.log(`\n${colors.magenta}=== Résumé de l'Audit de Sécurité ===${colors.reset}`);
  console.log(`${colors.yellow}Vulnérabilités critiques: ${auditData.criticalCount}${colors.reset}`);
  console.log(`${colors.yellow}Vulnérabilités majeures: ${auditData.majorCount}${colors.reset}`);
  console.log(`${colors.yellow}Vulnérabilités mineures: ${auditData.minorCount}${colors.reset}`);
  
  // Score global
  const securityScore = calculateSecurityScore(auditData);
  const scoreColor = securityScore >= 80 ? colors.green : (securityScore >= 60 ? colors.yellow : colors.red);
  console.log(`${colors.blue}Score de sécurité global: ${scoreColor}${securityScore}/100${colors.reset}`);
  
  // Configuration
  const missingRequiredConfigs = configChecks.filter(c => c.required && !c.found).length;
  const missingRecommendedConfigs = configChecks.filter(c => c.recommended && !c.found).length;
  
  if (missingRequiredConfigs > 0) {
    console.log(`${colors.red}${missingRequiredConfigs} configuration(s) de sécurité requise(s) manquante(s)${colors.reset}`);
  }
  
  if (missingRecommendedConfigs > 0) {
    console.log(`${colors.yellow}${missingRecommendedConfigs} configuration(s) de sécurité recommandée(s) manquante(s)${colors.reset}`);
  }
  
  console.log(`\n${colors.green}Audit de sécurité terminé. Rapport disponible à: ${reportPath}${colors.reset}`);
  
  return { securityScore, vulnerabilities, configChecks, reportPath };
}

// Lancer l'audit complet
performSecurityAudit().catch(error => {
  console.error(`Erreur lors de l'audit de sécurité: ${error.message}`);
  process.exit(1);
}); 