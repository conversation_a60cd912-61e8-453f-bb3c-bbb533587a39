# Two-Factor Authentication Implementation

This repository contains a comprehensive implementation of Two-Factor Authentication (2FA) for web applications using the Time-based One-Time Password (TOTP) method.

## Features

- **Complete 2FA Workflow**: Setup, enable, verify, and disable 2FA
- **QR Code Generation**: Generate QR codes for easy setup with authenticator apps
- **Session Management**: Maintain 2FA verified sessions
- **Security Event Logging**: Track all security events related to 2FA
- **User-friendly UI Components**: Ready-to-use React components
- **Comprehensive Testing**: Unit tests for all components
- **Mock Services**: Development and testing utilities

## Architecture

The implementation is divided into frontend and backend components:

### Backend (NestJS)

- **MFAController**: Handles all 2FA-related endpoints
- **MFAMiddleware**: Enforces 2FA requirements in protected routes
- **SecurityEventService**: Logs security events and generates alerts
- **JwtStrategy**: Updated to handle 2FA verification status
- **MFAModule**: Exports all 2FA-related components

### Frontend (React)

- **MFA Service**: API integration for 2FA operations
- **useMFA Hook**: React hook for consuming the MFA service
- **UI Components**:
  - **SetupMFA**: Guides users through 2FA setup
  - **VerifyMFA**: Handles 2FA code verification
  - **MockLoginForm**: Test component for demonstrating 2FA flow
  - **MFATestComponent**: Component for testing 2FA functionality
  - **MFARoute**: Protected route component requiring 2FA

## Getting Started

### Backend Setup

1. Install required packages:
   ```bash
   npm install otplib qrcode
   ```

2. Import the MFA module in your app.module.ts:
   ```typescript
   import { MFAModule } from './modules/mfa.module';
   
   @Module({
     imports: [
       // ... other imports
       MFAModule,
     ],
   })
   export class AppModule {}
   ```

3. Configure session handling in main.ts:
   ```typescript
   import * as session from 'express-session';
   import * as passport from 'passport';
   
   async function bootstrap() {
     // ... other app configurations
     app.use(
       session({
         secret: configService.get('SESSION_SECRET'),
         resave: false,
         saveUninitialized: false,
         cookie: { secure: process.env.NODE_ENV === 'production' },
       }),
     );
     app.use(passport.initialize());
     app.use(passport.session());
     // ... other app configurations
   }
   ```

### Frontend Setup

1. Install required packages:
   ```bash
   npm install antd @ant-design/icons
   ```

2. Import and use the MFA components:
   ```typescript
   import { SetupMFA } from './components/auth/SetupMFA';
   import { useMFA } from './hooks/useMFA';
   
   function YourComponent() {
     const { setupTwoFactor, enableTwoFactor, verifyTwoFactor } = useMFA();
     
     return (
       <div>
         <SetupMFA onComplete={() => console.log('2FA setup completed')} />
       </div>
     );
   }
   ```

3. Protect routes with 2FA:
   ```typescript
   import { MFARoute } from './components/auth/MFARoute';
   
   function AppRoutes() {
     return (
       <Routes>
         <Route path="/login" element={<Login />} />
         <Route path="/verify-mfa" element={<VerifyMFA />} />
         <Route
           path="/protected"
           element={
             <MFARoute>
               <ProtectedPage />
             </MFARoute>
           }
         />
       </Routes>
     );
   }
   ```

## Testing

The implementation includes comprehensive test files:

### Backend Tests

- **MFAController.test.ts**: Tests for all controller endpoints
- **MFAMiddleware.test.ts**: Tests for middleware functions
- **AuthService.mfa.test.ts**: Tests for MFA-related auth service methods
- **SecurityEventService.test.ts**: Tests for security event logging
- **MFAModule.test.ts**: Tests for module configuration

### Frontend Tests

- **MFAService.test.ts**: Tests for the MFA service
- **MFAHook.test.tsx**: Tests for the useMFA hook
- **Test Pages**: TestLoginPage and TestPage for visual testing

## API Endpoints

- **GET /auth/2fa/setup**: Initialize 2FA setup and generate QR code
- **POST /auth/2fa/enable**: Enable 2FA with verification code
- **POST /auth/2fa/verify**: Verify 2FA code during login
- **POST /auth/2fa/disable**: Disable 2FA with verification code
- **GET /auth/2fa/status**: Check 2FA status (enabled/verified)

## Security Considerations

- All 2FA operations are logged with appropriate severity levels
- Failed verification attempts trigger security alerts
- Session verification prevents session hijacking
- Rate limiting is recommended for verification endpoints

## Mock Implementation

For development and testing, the frontend includes a mock implementation that can be enabled by setting the environment to 'test':

```typescript
// To use mock implementation
if (process.env.NODE_ENV === 'test') {
  const result = await mfaService.mockVerify('123456');
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details. 