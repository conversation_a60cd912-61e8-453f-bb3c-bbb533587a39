# Plan d'optimisation de l'accessibilité

Ce document présente le plan d'optimisation de l'accessibilité pour la fusion des frontends original (RandBeFrontend) et nouveau (frontend-refonte).

## Objectifs

- Rendre l'application accessible à tous les utilisateurs, y compris ceux ayant des handicaps
- Se conformer aux normes WCAG 2.1 niveau AA
- Améliorer l'expérience utilisateur pour tous les utilisateurs
- Mettre en place des fonctionnalités d'accessibilité avancées

## Critères d'accessibilité

### 1. Perception

#### Alternatives textuelles

Fournir des alternatives textuelles pour tout contenu non textuel.

```jsx
// Avant
<img src="/images/logo.png" />

// Après
<img src="/images/logo.png" alt="Logo Retreat And Be" />
```

#### Médias temporels

Fournir des alternatives pour les médias temporels.

```jsx
// Vidéo avec sous-titres et transcription
<video controls>
  <source src="/videos/retreat-tour.mp4" type="video/mp4" />
  <track kind="subtitles" src="/videos/retreat-tour.vtt" srclang="fr" label="Français" />
  <p>Votre navigateur ne prend pas en charge les vidéos HTML5. Vous pouvez <a href="/videos/retreat-tour.mp4">télécharger la vidéo</a> ou <a href="/videos/retreat-tour-transcript.html">lire la transcription</a>.</p>
</video>
```

#### Adaptable

Créer du contenu qui peut être présenté de différentes manières sans perdre d'information.

```jsx
// Utiliser des éléments sémantiques
<article>
  <header>
    <h1>Titre de l'article</h1>
    <p>Publié le <time datetime="2023-05-15">15 mai 2023</time></p>
  </header>
  <section>
    <h2>Section 1</h2>
    <p>Contenu de la section 1...</p>
  </section>
  <section>
    <h2>Section 2</h2>
    <p>Contenu de la section 2...</p>
  </section>
  <footer>
    <p>Auteur: John Doe</p>
  </footer>
</article>
```

#### Distinguable

Faciliter la perception du contenu par les utilisateurs.

```css
/* Assurer un contraste suffisant */
.text-primary {
  color: #2E7D32; /* Contraste suffisant avec un fond blanc */
}

/* Permettre le redimensionnement du texte */
body {
  font-size: 16px; /* Taille de base */
}

/* Ne pas utiliser uniquement la couleur pour transmettre l'information */
.error-field {
  border: 2px solid #F44336; /* Rouge pour indiquer une erreur */
  background-image: url('/icons/error.svg'); /* Icône d'erreur */
}
```

### 2. Opérabilité

#### Accessibilité au clavier

Rendre toutes les fonctionnalités accessibles au clavier.

```jsx
// Assurer que les éléments interactifs sont focusables
<button type="button" onClick={handleClick}>
  Cliquez ici
</button>

// Gérer les événements clavier
const handleKeyDown = (e) => {
  if (e.key === 'Enter' || e.key === ' ') {
    handleAction();
  }
};

<div 
  role="button" 
  tabIndex={0} 
  onClick={handleAction} 
  onKeyDown={handleKeyDown}
>
  Action
</div>
```

#### Délai suffisant

Donner aux utilisateurs suffisamment de temps pour lire et utiliser le contenu.

```jsx
// Notification avec option de prolongation
const Notification = ({ message, duration = 5000 }) => {
  const [visible, setVisible] = useState(true);
  const [timeLeft, setTimeLeft] = useState(duration);
  
  // Prolonger la durée
  const extendDuration = () => {
    setTimeLeft(duration);
  };
  
  // Fermer la notification
  const closeNotification = () => {
    setVisible(false);
  };
  
  return visible ? (
    <div className="notification" role="alert">
      <p>{message}</p>
      <p>Fermeture dans {Math.ceil(timeLeft / 1000)} secondes</p>
      <button onClick={extendDuration}>Prolonger</button>
      <button onClick={closeNotification}>Fermer</button>
    </div>
  ) : null;
};
```

#### Navigation

Fournir des moyens pour aider les utilisateurs à naviguer et à trouver du contenu.

```jsx
// Fil d'Ariane
<nav aria-label="Fil d'Ariane">
  <ol className="breadcrumb">
    <li><a href="/">Accueil</a></li>
    <li><a href="/retraites">Retraites</a></li>
    <li aria-current="page">Retraite de yoga</li>
  </ol>
</nav>

// Liens d'évitement
<a href="#main-content" className="skip-link">
  Aller au contenu principal
</a>
```

#### Modes de fonctionnement

Faciliter l'utilisation des fonctionnalités par différents moyens.

```jsx
// Recherche vocale
const VoiceSearch = () => {
  const [listening, setListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  
  const startListening = () => {
    // Logique pour démarrer la reconnaissance vocale
  };
  
  return (
    <div>
      <input 
        type="text" 
        value={transcript} 
        onChange={(e) => setTranscript(e.target.value)} 
        placeholder="Rechercher..." 
        aria-label="Rechercher"
      />
      <button 
        onClick={startListening} 
        aria-label={listening ? "Arrêter l'écoute" : "Recherche vocale"}
      >
        {listening ? "Arrêter" : "Parler"}
      </button>
    </div>
  );
};
```

### 3. Compréhensibilité

#### Lisible

Rendre le contenu textuel lisible et compréhensible.

```jsx
// Indiquer la langue du document
<html lang="fr">
  {/* Contenu du document */}
</html>

// Indiquer les changements de langue
<p>
  Le mot <span lang="en">mindfulness</span> peut être traduit par "pleine conscience".
</p>
```

#### Prévisible

Faire en sorte que les pages web apparaissent et fonctionnent de manière prévisible.

```jsx
// Utiliser des patterns d'interface cohérents
const Button = ({ variant = 'primary', children, ...props }) => {
  return (
    <button 
      className={`button button-${variant}`} 
      {...props}
    >
      {children}
    </button>
  );
};
```

#### Assistance à la saisie

Aider les utilisateurs à éviter et à corriger les erreurs.

```jsx
// Formulaire avec validation et messages d'erreur
const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState({});
  
  const validate = () => {
    const newErrors = {};
    if (!email) newErrors.email = 'L\'email est requis';
    if (!password) newErrors.password = 'Le mot de passe est requis';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (validate()) {
      // Soumettre le formulaire
    }
  };
  
  return (
    <form onSubmit={handleSubmit} noValidate>
      <div className="form-group">
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          aria-invalid={!!errors.email}
          aria-describedby={errors.email ? "email-error" : undefined}
        />
        {errors.email && (
          <p id="email-error" className="error-message">
            {errors.email}
          </p>
        )}
      </div>
      
      <div className="form-group">
        <label htmlFor="password">Mot de passe</label>
        <input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          aria-invalid={!!errors.password}
          aria-describedby={errors.password ? "password-error" : undefined}
        />
        {errors.password && (
          <p id="password-error" className="error-message">
            {errors.password}
          </p>
        )}
      </div>
      
      <button type="submit">Se connecter</button>
    </form>
  );
};
```

### 4. Robustesse

#### Compatible

Maximiser la compatibilité avec les agents utilisateurs actuels et futurs.

```jsx
// Utiliser des attributs ARIA appropriés
<div 
  role="tablist" 
  aria-label="Informations sur la retraite"
>
  <button 
    role="tab" 
    id="tab-description" 
    aria-selected={activeTab === 'description'} 
    aria-controls="panel-description"
    onClick={() => setActiveTab('description')}
  >
    Description
  </button>
  <button 
    role="tab" 
    id="tab-programme" 
    aria-selected={activeTab === 'programme'} 
    aria-controls="panel-programme"
    onClick={() => setActiveTab('programme')}
  >
    Programme
  </button>
  
  <div 
    role="tabpanel" 
    id="panel-description" 
    aria-labelledby="tab-description"
    hidden={activeTab !== 'description'}
  >
    {/* Contenu de la description */}
  </div>
  <div 
    role="tabpanel" 
    id="panel-programme" 
    aria-labelledby="tab-programme"
    hidden={activeTab !== 'programme'}
  >
    {/* Contenu du programme */}
  </div>
</div>
```

## Fonctionnalités d'accessibilité avancées

### 1. Mode daltonisme

Mettre en place des filtres pour les différents types de daltonisme.

```jsx
// Composant de filtre pour le daltonisme
const ColorBlindnessFilter = ({ type }) => {
  // Définir les matrices de transformation pour chaque type de daltonisme
  const filters = {
    protanopia: 'matrix(0.567, 0.433, 0, 0, 0, 0.558, 0.442, 0, 0, 0, 0, 0.242, 0.758, 0, 0, 0, 0, 0, 1, 0)',
    deuteranopia: 'matrix(0.625, 0.375, 0, 0, 0, 0.7, 0.3, 0, 0, 0, 0, 0.3, 0.7, 0, 0, 0, 0, 0, 1, 0)',
    tritanopia: 'matrix(0.95, 0.05, 0, 0, 0, 0, 0.433, 0.567, 0, 0, 0, 0.475, 0.525, 0, 0, 0, 0, 0, 1, 0)',
    achromatopsia: 'matrix(0.299, 0.587, 0.114, 0, 0, 0.299, 0.587, 0.114, 0, 0, 0.299, 0.587, 0.114, 0, 0, 0, 0, 0, 1, 0)',
  };
  
  return (
    <svg style={{ position: 'absolute', height: 0, width: 0 }}>
      <defs>
        <filter id={`filter-${type}`}>
          <feColorMatrix type="matrix" values={filters[type]} />
        </filter>
      </defs>
    </svg>
  );
};
```

### 2. Mode contraste élevé

Mettre en place un mode contraste élevé pour les utilisateurs ayant des déficiences visuelles.

```jsx
// Hook pour le mode contraste élevé
const useHighContrast = () => {
  const [highContrast, setHighContrast] = useState(false);
  
  useEffect(() => {
    if (highContrast) {
      document.body.classList.add('high-contrast');
    } else {
      document.body.classList.remove('high-contrast');
    }
  }, [highContrast]);
  
  return [highContrast, setHighContrast];
};

// CSS pour le mode contraste élevé
const highContrastStyles = `
  .high-contrast {
    --color-background-primary: #000;
    --color-text-primary: #fff;
    --color-brand-primary: #ffff00;
    --color-brand-secondary: #00ffff;
    --color-border-primary: #fff;
  }
  
  .high-contrast a {
    color: #ffff00;
    text-decoration: underline;
  }
  
  .high-contrast button {
    background-color: #000;
    color: #fff;
    border: 2px solid #fff;
  }
`;
```

### 3. Mode mouvement réduit

Mettre en place un mode mouvement réduit pour les utilisateurs sensibles aux mouvements.

```jsx
// Hook pour le mode mouvement réduit
const useReducedMotion = () => {
  const [reducedMotion, setReducedMotion] = useState(false);
  
  useEffect(() => {
    // Vérifier la préférence système
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setReducedMotion(mediaQuery.matches);
    
    // Écouter les changements de préférence
    const handleChange = (e) => {
      setReducedMotion(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);
  
  return [reducedMotion, setReducedMotion];
};

// Composant d'animation qui respecte la préférence de mouvement réduit
const Animation = ({ children }) => {
  const [reducedMotion] = useReducedMotion();
  
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      initial={{ opacity: 0, y: reducedMotion ? 0 : 20 }}
      transition={{ 
        duration: reducedMotion ? 0 : 0.5,
        ease: 'easeOut'
      }}
    >
      {children}
    </motion.div>
  );
};
```

## Plan d'implémentation

1. **Audit initial**
   - Exécuter des outils d'audit d'accessibilité (axe, Lighthouse)
   - Identifier les problèmes d'accessibilité existants

2. **Corrections de base**
   - Ajouter des attributs alt aux images
   - Améliorer la structure sémantique du HTML
   - Assurer un contraste suffisant des couleurs
   - Rendre tous les éléments interactifs accessibles au clavier

3. **Améliorations avancées**
   - Mettre en place le mode daltonisme
   - Mettre en place le mode contraste élevé
   - Mettre en place le mode mouvement réduit
   - Améliorer la navigation au clavier

4. **Tests d'accessibilité**
   - Tester avec des lecteurs d'écran (NVDA, VoiceOver)
   - Tester la navigation au clavier
   - Tester avec différents paramètres d'accessibilité

5. **Documentation**
   - Documenter les fonctionnalités d'accessibilité
   - Créer des guides pour les développeurs

## Outils de mesure

- **axe DevTools** : Pour identifier les problèmes d'accessibilité
- **Lighthouse** : Pour mesurer l'accessibilité globale
- **WAVE** : Pour visualiser les problèmes d'accessibilité
- **Lecteurs d'écran** : Pour tester l'expérience des utilisateurs de lecteurs d'écran

## Conclusion

Ce plan d'optimisation de l'accessibilité permettra de rendre l'application accessible à tous les utilisateurs, y compris ceux ayant des handicaps. Les améliorations seront mises en œuvre progressivement et testées à chaque étape pour s'assurer qu'elles ont l'impact souhaité.
