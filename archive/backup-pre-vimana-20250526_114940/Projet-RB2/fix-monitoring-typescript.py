#!/usr/bin/env python3

"""
Script pour corriger les problèmes de syntaxe TypeScript spécifiques 
aux fichiers de monitoring et télémétrie.

Cette version est spécialement conçue pour corriger les problèmes courants trouvés
dans les fichiers comme openTelemetry.ts, unifiedMonitoring.ts, etc.
"""

import os
import re
import sys
import glob
from typing import List, Tuple, Dict, Any
import json

# Répertoires cibles pour la correction
TARGET_DIRS = [
    "mobile/src/utils",
    "infrastructure/monitoring",
    "infrastructure/logging",
    "infrastructure/resilience",
    "mobile/src/services"
]

BACKUP_SUFFIX = ".bak"
ADVANCED_MODE = True  # Active les corrections plus complexes

# Modèles de correction pour les fichiers de monitoring
MONITORING_FIXES = [
    # Corriger les déclarations d'énumération avec points-virgules
    (r'export\s+enum\s+(\w+)\s*{;', r'export enum \1 {'),
    
    # Corriger les accolades de fermeture d'énumération
    (r'(UNSET|ERROR|OK|HISTOGRAM|GAUGE|COUNTER)\s*=\s*[^}]+}\s*', lambda m: m.group(0).rstrip('}') + '\n}'),
    
    # Corriger les interfaces avec points-virgules
    (r'export\s+interface\s+(\w+)\s*{;', r'export interface \1 {'),
    
    # Corriger les accolades imbriquées excessives
    (r'{\s*{\s*{\s*{\s*{\s*}\s*}\s*}\s*}\s*}', r'{ return; }'),
    
    # Enlever les parenthèses superflues après les conditions if
    (r'if\s*\(([^)]+)\)\s*\)\s*{', r'if(\1) {'),
    
    # Enlever les guillemets autour des noms de paramètres
    (r'(public|private)\s+\w+\s*\(\s*"([^"]+)"\s*:', r'\1 \2(', -1),
    (r'(,\s*)"([^"]+)"\s*:', r'\1\2:', -1),
    
    # Enlever les guillemets autour des noms de propriétés dans les objets
    (r'{\s*"([^"]+)"\s*:', r'{ \1:', -1),
    (r',\s*"([^"]+)"\s*:', r', \1:', -1),
    
    # Corriger les déclarations de type
    (r'const\s+"([^"]+)"\s*:\s*(\w+)', r'const \1: \2', -1),
    
    # Corriger les méthodes privées
    (r'(private\s+\w+)\s*\(([^)]*)\):\s*([^{]+){', r'\1(\2): \3 {'),
    
    # Corriger les méthodes publiques
    (r'(public\s+\w+)\s*\(([^)]*)\):\s*([^{]+){', r'\1(\2): \3 {'),
    
    # Corriger les méthodes avec async
    (r'(private|public)\s+async\s+(\w+)\s*\(([^)]*)\):\s*Promise<([^>]+)>\s*{', 
     r'\1 async \2(\3): Promise<\4> {'),
    
    # Corriger les constructeurs
    (r'private\s+constructor\s*\(\s*\)\s*{', r'private constructor() {'),
    
    # Corriger la syntaxe des méthodes de classe
    (r'(\w+)\s*\(\);\s*', r'\1();'),
    
    # Corriger les accolades manquantes
    (r'if\s*\(([^)]+)\)\s*return;', r'if(\1) { return; }'),
    
    # Corriger les appels de fonction
    (r'(\w+)\(\)\(\)', r'\1()'),
    
    # Corriger les opérateurs d'égalité mal formés
    (r'([^=<>!])=\s*==\s*', r'\1 === '),
    
    # Corriger les points-virgules dans les blocs de code
    (r'{\s*;', r'{'),
    
    # Enlever les points-virgules après accolades fermantes
    (r'}\s*;', r'}'),
    
    # Corriger les types Record mal formatés
    (r'Record<string,\s*any>', r'Record<string, any>'),
    
    # Corriger les problèmes de méthodes de classe
    (r'public\s+(get|set|static)\s+(\w+)', r'public \1 \2'),
    
    # Corriger les méthodes asynchrones mal formatées
    (r'async\s+<T>', r'async<T>'),
    
    # Enlever les points-virgules après import/export
    (r'(import|export)[^;]+;', lambda m: m.group(0).rstrip(';')),
    
    # Corriger la structure des if imbriqués avec trop d'accolades
    (r'if\s*\(([^)]+)\)\s*{\s*{\s*{\s*{\s*{', r'if(\1) {')
]

# Corrections spécifiques à openTelemetry.ts
OPEN_TELEMETRY_FIXES = [
    # Corriger les guillemets autour des noms de paramètres et propriétés
    (r'"([^"]+)":\s*(string|number|boolean|Span|SpanStatusCode|MetricType|Record|any)', r'\1: \2'),
    
    # Corriger les paramètres avec des guillemets dans la fonction withTelemetry
    (r'withTelemetry<T>\(\s*"([^"]+)":', r'withTelemetry<T>(\1:'),
    (r',\s*"([^"]+)":', r', \1:'),
    
    # Corriger les problèmes d'import
    (r'import\s+[^;]+;\s*', lambda m: m.group(0).replace(';', '')),
    
    # Corriger les accolades dans les déclarations d'enum
    (r'(enum\s+\w+\s*{);', r'\1'),
    
    # Corriger les accolades dans les déclarations d'interface
    (r'(interface\s+\w+\s*{);', r'\1'),
    
    # Remplacer les accolades excessives dans les blocks if
    (r'if\s*\(([^)]+)\)\s*{\s*{\s*{\s*{\s*{\s*}\s*}\s*}\s*}\s*}', r'if(\1) { return; }'),
    
    # Corriger les déclarations async
    (r'(private|public)\s+async\s+(\w+)\s*\(([^)]*)\):\s*Promise<([^>]+)>\s*{', 
     r'\1 async \2(\3): Promise<\4> {'),
    
    # Corriger les valeurs par défaut des paramètres de méthode
    (r'(\w+):\s+(\w+)\s*=\s*([^,\)]+)', r'\1: \2 = \3'),
    
    # Corriger la structure des classes
    (r'export\s+class\s+(\w+)\s*{;', r'export class \1 {'),
    
    # Corriger les expressions entre parenthèses incomplètes
    (r'if\s*\(([^)]+)', lambda m: f'if ({m.group(1).strip()})'),
    
    # Corriger les méthodes statiques 
    (r'public\s+static\s+(\w+)\s*\(([^)]*)\):\s*(\w+)\s*{', 
     r'public static \1(\2): \3 {'),
    
    # Corriger les parenthèses doubles dans les conditions if
    (r'if\s*\(([^)]+)\)\s*\)', r'if(\1)'),
    
    # Corriger les accolades multiples
    (r'{\s*}\s*}\s*}\s*}\s*}', r'{ return; }'),
    
    # Nettoyer les points-virgules à la fin des instructions
    (r'}\);', r'})'),
    
    # Corriger les méthodes avec paramètres entre guillemets
    (r'public\s+([a-zA-Z0-9_]+)\s*\(\s*"([^"]+)"\s*:\s*([a-zA-Z0-9_<>,\s]+)', 
     r'public \1(\2: \3'),
]

# Corrections spécifiques à unifiedMonitoring.ts
UNIFIED_MONITORING_FIXES = [
    # Corriger les déclarations d'enum mal formatées
    (r'export\s+enum\s+LogLevel\s*{;', r'export enum LogLevel {'),
    
    # Corriger les erreurs de syntaxe dans les blocs conditionnels
    (r'if\s*\(([^=]+)=\s*==\s*([^)]+)\)', r'if(\1 === \2)'),
]

# Corrections pour les fichiers Node.js dans node_modules/@types
NODE_TYPES_FIXES = [
    # Corriger les problèmes de virgules dans les interfaces
    (r'(\w+)\?:\s*(\w+),', r'\1?: \2;'),
    
    # Corriger les fermetures génériques
    (r'<([^>]+)={}  ([^>]+)>', r'<\1={}, \2>'),
    
    # Corriger les problèmes de formatage complexes
    (r'T extends new\(\.\.\.(args: any\[\])\) => infer C \? C : never',
     r'T extends new(...args: any[]) => infer C ? C : never'),
]

def find_typescript_files(directories: List[str]) -> List[str]:
    """Trouver tous les fichiers TypeScript dans les répertoires spécifiés."""
    ts_files = []
    
    for directory in directories:
        if not os.path.isdir(directory):
            print(f"Avertissement: Le répertoire '{directory}' n'existe pas.")
            continue
            
        pattern_ts = os.path.join(directory, "**", "*.ts")
        pattern_tsx = os.path.join(directory, "**", "*.tsx")
        
        ts_files.extend(glob.glob(pattern_ts, recursive=True))
        ts_files.extend(glob.glob(pattern_tsx, recursive=True))
    
    return ts_files

def backup_file(file_path: str) -> str:
    """Créer une copie de sauvegarde du fichier."""
    backup_path = f"{file_path}{BACKUP_SUFFIX}"
    with open(file_path, 'r', encoding='utf-8', errors='replace') as source:
        content = source.read()
        with open(backup_path, 'w', encoding='utf-8') as target:
            target.write(content)
    return backup_path

def get_file_specific_fixes(file_path: str) -> List[Tuple[str, Any, int]]:
    """Sélectionner les corrections spécifiques en fonction du fichier."""
    fixes = []
    
    # Ajout des corrections génériques pour tous les fichiers de monitoring
    for pattern, replacement in MONITORING_FIXES:
        if isinstance(replacement, str):
            fixes.append((pattern, replacement, 0))
        else:
            fixes.append((pattern, replacement, 0))
    
    if 'openTelemetry' in file_path:
        for pattern, replacement in OPEN_TELEMETRY_FIXES:
            fixes.append((pattern, replacement, 0))
    elif 'unifiedMonitoring' in file_path:
        for pattern, replacement in UNIFIED_MONITORING_FIXES:
            fixes.append((pattern, replacement, 0))
    elif 'node_modules/@types' in file_path:
        for pattern, replacement in NODE_TYPES_FIXES:
            fixes.append((pattern, replacement, 0))
    
    return fixes

def apply_fixes(file_path: str, patterns: List[Tuple[str, Any, int]]) -> bool:
    """Appliquer les corrections au fichier."""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
        
        modified = False
        for pattern, replacement, count in patterns:
            if callable(replacement):
                new_content = re.sub(pattern, replacement, content, count=count)
            else:
                new_content = re.sub(pattern, replacement, content, count=count)
            
            if new_content != content:
                content = new_content
                modified = True
        
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
        return modified
    except Exception as e:
        print(f"Erreur lors de la correction de {file_path}: {e}")
        return False

def deep_fix_file(file_path: str) -> bool:
    """Analyse et corrige en profondeur le fichier avec une approche plus structurée."""
    if not ADVANCED_MODE or 'node_modules' in file_path:
        return False  # Désactiver pour les node_modules
        
    try:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            lines = f.readlines()
        
        modified = False
        new_lines = []
        
        # Corriger ligne par ligne
        for i, line in enumerate(lines):
            # Corriger les points-virgules en fin de déclaration d'interface/classe/enum
            if re.search(r'(interface|class|enum)\s+\w+\s*{;', line):
                line = line.replace('{;', '{')
                modified = True
            
            # Corriger les accolades fermantes avec point-virgule
            if re.search(r'}\s*;', line):
                line = line.replace('};', '}')
                modified = True
            
            # Corriger les imports/exports avec point-virgule
            if re.search(r'^(import|export)[^;]+;$', line):
                line = line.rstrip(';\n') + '\n'
                modified = True
            
            # Corriger les opérateurs d'égalité malformés
            if '= ==' in line:
                line = line.replace('= ==', ' ===')
                modified = True
            
            # Corriger les accolades excessives dans les blocs if
            if re.search(r'if\s*\([^)]+\)\s*{\s*{\s*{', line):
                line = re.sub(r'(if\s*\([^)]+\))\s*{\s*{\s*{', r'\1 {', line)
                modified = True
                
            # Remplacer les guillemets autour des noms de propriétés
            if re.search(r'"([a-zA-Z0-9_]+)":', line):
                line = re.sub(r'"([a-zA-Z0-9_]+)":', r'\1:', line)
                modified = True
                
            # Corriger les parenthèses doubles dans les conditions if
            if re.search(r'if\s*\([^)]+\)\s*\)', line):
                line = re.sub(r'(if\s*\([^)]+)\)\s*\)', r'\1)', line)
                modified = True
                
            # Corriger les try/catch mal formés
            if re.search(r'} catch\(', line):
                line = re.sub(r'} catch\(', r'} catch (', line)
                modified = True
                
            # Corriger les paramètres de fonction avec guillemets
            if re.search(r'\(\s*"[^"]+"\s*:', line):
                line = re.sub(r'\(\s*"([^"]+)"\s*:', r'(\1:', line)
                modified = True
                
            # Corriger les paramètres suivants avec guillemets
            if re.search(r',\s*"[^"]+"\s*:', line):
                line = re.sub(r',\s*"([^"]+)"\s*:', r', \1:', line)
                modified = True
            
            new_lines.append(line)
        
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
                
        return modified
    except Exception as e:
        print(f"Erreur lors de la correction avancée de {file_path}: {e}")
        return False

def fix_telemetry_structure(file_path: str) -> bool:
    """Corrige spécifiquement les structures complexes dans les fichiers de télémétrie."""
    if 'openTelemetry' not in file_path and 'unifiedMonitoring' not in file_path:
        return False
        
    try:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
        
        # Remplacer tous les "name": string par name: string
        content = re.sub(r'"([a-zA-Z0-9_]+)":\s*(string|number|boolean|any|Span|Record|Promise|MetricType|SpanStatusCode)', 
                         r'\1: \2', content)
        
        # Corriger la fonction withTelemetry
        content = re.sub(r'export\s+async\s+function\s+withTelemetry<T>\(\s*"([^"]+)"',
                     r'export async function withTelemetry<T>(\1', content)
        
        # Corriger les conditions if avec des parenthèses supplémentaires
        content = re.sub(r'if\s*\(([^)]+)\)\s*\)\s*{', r'if(\1) {', content)
        
        # Simplifier les accolades trop imbriquées
        content = re.sub(r'{\s*{\s*{\s*{\s*{[^}]*}\s*}\s*}\s*}\s*}', r'{ return; }', content)
        
        # Restructurer les fonctions async
        content = re.sub(r'(public|private)\s+async\s+(\w+)\s*\(([^)]*)\):\s*Promise<([^>]*)>\s*{',
                     r'\1 async \2(\3): Promise<\4> {', content)
        
        # Corriger les parenthèses fermantes en trop
        content = re.sub(r'\)\)\s*{', r') {', content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
        return True
    except Exception as e:
        print(f"Erreur lors de la correction de structure dans {file_path}: {e}")
        return False

def main():
    """Fonction principale du script."""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print(f"Usage: {sys.argv[0]} [directory1 directory2 ...]")
        print("Si aucun répertoire n'est spécifié, les répertoires par défaut suivants seront utilisés:")
        for dir in TARGET_DIRS:
            print(f"  - {dir}")
        return
    
    # Utiliser les répertoires spécifiés ou les répertoires par défaut
    directories = sys.argv[1:] if len(sys.argv) > 1 else TARGET_DIRS
    
    # Fichiers spécifiques à traiter en priorité
    priority_files = ["mobile/src/utils/openTelemetry.ts", "mobile/src/utils/unifiedMonitoring.ts"]
    
    # Traiter d'abord les fichiers prioritaires
    for file_path in priority_files:
        if os.path.exists(file_path):
            print(f"Traitement prioritaire de {file_path}...")
            try:
                # Sauvegarder le fichier
                backup_path = backup_file(file_path)
                
                # Correction spécifique de structure pour les fichiers de télémétrie
                structure_fixed = fix_telemetry_structure(file_path)
                
                # Sélectionner les corrections spécifiques au fichier
                file_specific_fixes = get_file_specific_fixes(file_path)
                
                # Appliquer les corrections standard
                modified = apply_fixes(file_path, file_specific_fixes)
                
                # Appliquer les corrections avancées si nécessaire
                advanced_modified = deep_fix_file(file_path)
                
                if structure_fixed or modified or advanced_modified:
                    print(f"✅ Corrigé: {file_path}")
                else:
                    print(f"ℹ️ Inchangé: {file_path}")
                    # Supprimer la sauvegarde si aucune modification n'a été apportée
                    os.remove(backup_path)
            except Exception as e:
                print(f"❌ Erreur pour {file_path}: {e}")
    
    # Trouver et traiter les autres fichiers TypeScript
    ts_files = find_typescript_files(directories)
    print(f"Trouvé {len(ts_files)} fichiers TypeScript dans les répertoires spécifiés.")
    
    stats = {"fixed": 0, "unchanged": 0, "error": 0}
    fixed_files = []
    
    for file_path in ts_files:
        # Ignorer les fichiers prioritaires déjà traités
        if file_path in priority_files:
            continue
            
        try:
            print(f"Traitement de {file_path}...")
            
            # Sélectionner les corrections spécifiques au fichier
            file_specific_fixes = get_file_specific_fixes(file_path)
            
            # Sauvegarder le fichier
            backup_path = backup_file(file_path)
            
            # Appliquer les corrections standard
            modified = apply_fixes(file_path, file_specific_fixes)
            
            # Appliquer les corrections avancées si nécessaire
            advanced_modified = deep_fix_file(file_path)
            
            if modified or advanced_modified:
                stats["fixed"] += 1
                fixed_files.append(file_path)
                print(f"✅ Corrigé: {file_path}")
            else:
                stats["unchanged"] += 1
                # Supprimer la sauvegarde si aucune modification n'a été apportée
                os.remove(backup_path)
                print(f"ℹ️ Inchangé: {file_path}")
        except Exception as e:
            stats["error"] += 1
            print(f"❌ Erreur pour {file_path}: {e}")
    
    print("\nRésumé des opérations:")
    print(f"- {stats['fixed']} fichiers corrigés")
    print(f"- {stats['unchanged']} fichiers inchangés")
    print(f"- {stats['error']} fichiers avec erreurs")
    
    if fixed_files:
        print("\nFichiers corrigés:")
        for file in fixed_files:
            print(f"- {file}")

if __name__ == "__main__":
    main() 