# Environment configuration
ENVIRONMENT=development  # development, staging, production

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=True

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=retreatandbe
DB_USER=postgres
DB_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TOPIC_PREFIX=retreatandbe

# AI Model Configuration
MODEL_PATH=models/
RECOMMENDER_MODEL=recommender_v1
CONTENT_MODEL=content_generator_v1
CHATBOT_MODEL=chatbot_v1
ANALYTICS_MODEL=analytics_v1
OPTIMIZER_MODEL=optimizer_v1

# External APIs
OPENAI_API_KEY=your_openai_api_key
HUGGINGFACE_API_KEY=your_huggingface_api_key

# Monitoring
ENABLE_MONITORING=True
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/retreatandbe_ai.log

# Security
JWT_SECRET=your_jwt_secret
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Feature Flags
ENABLE_RECOMMENDER=True
ENABLE_CONTENT_GENERATOR=True
ENABLE_CHATBOT=True
ENABLE_ANALYTICS=True
ENABLE_OPTIMIZER=True
