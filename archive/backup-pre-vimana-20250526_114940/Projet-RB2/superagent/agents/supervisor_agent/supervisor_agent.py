from typing import Dict, Any, Optional, List
import asyncio
import logging
import json
from datetime import datetime
from agents.base.agent_base import BaseAgent

class SupervisorAgent(BaseAgent):
    """
    Agent spécialisé dans la supervision et la coordination des autres agents.
    Capable d'évaluer les résultats, de prendre des décisions et de réorienter le workflow.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("supervisor", config)
        self.active_tasks = {}
        self.decision_history = []
        self.evaluation_criteria = config.get("EVALUATION_CRITERIA", {})
        
    async def _initialize(self):
        """Initialisation spécifique à l'agent de supervision"""
        self.logger.info("Initializing Supervisor Agent")
        # Charger les critères d'évaluation
        if not self.evaluation_criteria:
            self.evaluation_criteria = await self._load_default_criteria()
        
    async def _cleanup(self):
        """Nettoyage des ressources de l'agent"""
        self.logger.info("Cleaning up Supervisor Agent resources")
        # Sauvegarder l'historique des décisions si nécessaire
        if self.config.get("SAVE_DECISION_HISTORY", False):
            await self._save_decision_history()
        
    async def process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Traite les messages reçus par l'agent"""
        action = message.get("action")
        
        if action == "evaluate_result":
            return await self.evaluate_result(
                task_id=message.get("task_id", ""),
                agent_id=message.get("agent_id", ""),
                result=message.get("result", {}),
                criteria=message.get("criteria", [])
            )
        elif action == "make_decision":
            return await self.make_decision(
                task_id=message.get("task_id", ""),
                context=message.get("context", {}),
                options=message.get("options", [])
            )
        elif action == "assign_task":
            return await self.assign_task(
                task_id=message.get("task_id", ""),
                task_description=message.get("task_description", ""),
                agent_type=message.get("agent_type", ""),
                parameters=message.get("parameters", {})
            )
        elif action == "get_task_status":
            return await self.get_task_status(
                task_id=message.get("task_id", "")
            )
        else:
            self.logger.warning(f"Unknown action requested: {action}")
            return {"status": "error", "message": f"Unknown action: {action}"}
            
    async def evaluate_result(self, task_id: str, agent_id: str, result: Dict[str, Any], 
                            criteria: List[str]) -> Dict[str, Any]:
        """
        Évalue le résultat d'une tâche selon des critères spécifiques.
        
        Args:
            task_id: L'identifiant de la tâche
            agent_id: L'identifiant de l'agent qui a produit le résultat
            result: Le résultat à évaluer
            criteria: Les critères d'évaluation à appliquer
            
        Returns:
            L'évaluation du résultat
        """
        self.logger.info(f"Evaluating result for task: {task_id} from agent: {agent_id}")
        
        if not task_id or not agent_id:
            return {"status": "error", "message": "Task ID and Agent ID are required"}
            
        if not result:
            return {"status": "error", "message": "No result provided for evaluation"}
            
        try:
            # Déterminer les critères à utiliser
            evaluation_criteria = criteria if criteria else self._get_default_criteria_for_agent(agent_id)
            
            # Évaluer le résultat selon chaque critère
            evaluation = {}
            overall_score = 0.0
            
            for criterion in evaluation_criteria:
                if criterion in self.evaluation_criteria:
                    criterion_def = self.evaluation_criteria[criterion]
                    score, feedback = await self._evaluate_criterion(result, criterion_def)
                    
                    evaluation[criterion] = {
                        "score": score,
                        "feedback": feedback,
                        "weight": criterion_def.get("weight", 1.0)
                    }
                    
                    overall_score += score * criterion_def.get("weight", 1.0)
                    
            # Calculer le score global normalisé
            total_weight = sum(self.evaluation_criteria[c].get("weight", 1.0) for c in evaluation_criteria 
                              if c in self.evaluation_criteria)
            
            if total_weight > 0:
                normalized_score = overall_score / total_weight
            else:
                normalized_score = 0.0
                
            # Déterminer le statut global
            if normalized_score >= 0.8:
                status = "excellent"
            elif normalized_score >= 0.6:
                status = "good"
            elif normalized_score >= 0.4:
                status = "acceptable"
            else:
                status = "needs_improvement"
                
            # Créer l'évaluation complète
            evaluation_result = {
                "task_id": task_id,
                "agent_id": agent_id,
                "criteria_evaluations": evaluation,
                "overall_score": round(normalized_score, 2),
                "status": status,
                "timestamp": datetime.now().isoformat()
            }
            
            # Mettre à jour la tâche si elle existe
            if task_id in self.active_tasks:
                self.active_tasks[task_id]["evaluation"] = evaluation_result
                self.active_tasks[task_id]["status"] = "evaluated"
                
            return {
                "status": "success",
                "evaluation": evaluation_result
            }
            
        except Exception as e:
            self.logger.error(f"Evaluation error: {str(e)}")
            return {"status": "error", "message": f"Failed to evaluate result: {str(e)}"}
            
    async def make_decision(self, task_id: str, context: Dict[str, Any], 
                          options: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Prend une décision basée sur le contexte et les options disponibles.
        
        Args:
            task_id: L'identifiant de la tâche
            context: Le contexte de la décision
            options: Les options disponibles
            
        Returns:
            La décision prise
        """
        self.logger.info(f"Making decision for task: {task_id}")
        
        if not task_id:
            return {"status": "error", "message": "Task ID is required"}
            
        if not options:
            return {"status": "error", "message": "No options provided for decision making"}
            
        try:
            # Évaluer chaque option
            option_scores = []
            
            for i, option in enumerate(options):
                score = await self._evaluate_option(option, context)
                option_scores.append({
                    "option_index": i,
                    "option": option,
                    "score": score
                })
                
            # Trier les options par score
            option_scores.sort(key=lambda x: x["score"], reverse=True)
            
            # Sélectionner la meilleure option
            best_option = option_scores[0]["option"]
            best_score = option_scores[0]["score"]
            
            # Créer la décision
            decision = {
                "task_id": task_id,
                "selected_option": best_option,
                "score": best_score,
                "alternatives": [o["option"] for o in option_scores[1:]],
                "reasoning": f"Selected option with highest score: {best_score}",
                "timestamp": datetime.now().isoformat()
            }
            
            # Enregistrer la décision dans l'historique
            self.decision_history.append(decision)
            
            # Mettre à jour la tâche si elle existe
            if task_id in self.active_tasks:
                self.active_tasks[task_id]["decision"] = decision
                self.active_tasks[task_id]["status"] = "decision_made"
                
            return {
                "status": "success",
                "decision": decision
            }
            
        except Exception as e:
            self.logger.error(f"Decision making error: {str(e)}")
            return {"status": "error", "message": f"Failed to make decision: {str(e)}"}
            
    async def assign_task(self, task_id: str, task_description: str, 
                        agent_type: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assigne une tâche à un type d'agent spécifique.
        
        Args:
            task_id: L'identifiant de la tâche
            task_description: La description de la tâche
            agent_type: Le type d'agent à qui assigner la tâche
            parameters: Les paramètres de la tâche
            
        Returns:
            Le résultat de l'assignation
        """
        self.logger.info(f"Assigning task: {task_id} to agent type: {agent_type}")
        
        if not task_id or not task_description or not agent_type:
            return {"status": "error", "message": "Task ID, description, and agent type are required"}
            
        try:
            # Vérifier si la tâche existe déjà
            if task_id in self.active_tasks:
                return {"status": "error", "message": f"Task ID already exists: {task_id}"}
                
            # Créer la tâche
            task = {
                "id": task_id,
                "description": task_description,
                "agent_type": agent_type,
                "parameters": parameters,
                "status": "assigned",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            # Enregistrer la tâche
            self.active_tasks[task_id] = task
            
            return {
                "status": "success",
                "task": task
            }
            
        except Exception as e:
            self.logger.error(f"Task assignment error: {str(e)}")
            return {"status": "error", "message": f"Failed to assign task: {str(e)}"}
            
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Récupère le statut d'une tâche.
        
        Args:
            task_id: L'identifiant de la tâche
            
        Returns:
            Le statut de la tâche
        """
        self.logger.info(f"Getting status for task: {task_id}")
        
        if not task_id:
            return {"status": "error", "message": "Task ID is required"}
            
        if task_id not in self.active_tasks:
            return {"status": "error", "message": f"Task not found: {task_id}"}
            
        return {
            "status": "success",
            "task": self.active_tasks[task_id]
        }
    
    # Méthodes utilitaires privées
    
    async def _load_default_criteria(self) -> Dict[str, Any]:
        """Charge les critères d'évaluation par défaut"""
        return {
            "accuracy": {
                "description": "Accuracy of the result",
                "weight": 1.5,
                "evaluation_function": "_evaluate_accuracy"
            },
            "completeness": {
                "description": "Completeness of the result",
                "weight": 1.2,
                "evaluation_function": "_evaluate_completeness"
            },
            "relevance": {
                "description": "Relevance of the result to the task",
                "weight": 1.3,
                "evaluation_function": "_evaluate_relevance"
            },
            "clarity": {
                "description": "Clarity and understandability of the result",
                "weight": 1.0,
                "evaluation_function": "_evaluate_clarity"
            },
            "efficiency": {
                "description": "Efficiency of the approach used",
                "weight": 0.8,
                "evaluation_function": "_evaluate_efficiency"
            }
        }
        
    def _get_default_criteria_for_agent(self, agent_id: str) -> List[str]:
        """Récupère les critères d'évaluation par défaut pour un type d'agent"""
        # Critères communs à tous les agents
        common_criteria = ["accuracy", "completeness", "relevance"]
        
        # Critères spécifiques à chaque type d'agent
        if agent_id == "coder":
            return common_criteria + ["efficiency", "code_quality"]
        elif agent_id == "reporter":
            return common_criteria + ["clarity", "structure"]
        elif agent_id == "researcher":
            return common_criteria + ["depth", "credibility"]
        elif agent_id == "browser":
            return common_criteria + ["coverage", "relevance"]
        else:
            return common_criteria
            
    async def _evaluate_criterion(self, result: Dict[str, Any], criterion_def: Dict[str, Any]) -> tuple:
        """Évalue un résultat selon un critère spécifique"""
        # Dans une implémentation réelle, nous utiliserions des fonctions d'évaluation spécifiques
        # Pour l'instant, nous simulons l'évaluation
        
        # Simuler un score entre 0 et 1
        score = 0.7  # Score par défaut
        
        # Ajuster le score en fonction du contenu du résultat
        if "status" in result and result["status"] == "success":
            score += 0.1
            
        if "error" in result or "message" in result and "error" in result.get("message", "").lower():
            score -= 0.3
            
        # Limiter le score entre 0 et 1
        score = max(0.0, min(1.0, score))
        
        # Générer un feedback
        if score >= 0.8:
            feedback = f"Excellent performance on {criterion_def['description']}"
        elif score >= 0.6:
            feedback = f"Good performance on {criterion_def['description']}"
        elif score >= 0.4:
            feedback = f"Acceptable performance on {criterion_def['description']}"
        else:
            feedback = f"Needs improvement on {criterion_def['description']}"
            
        return score, feedback
        
    async def _evaluate_option(self, option: Dict[str, Any], context: Dict[str, Any]) -> float:
        """Évalue une option dans un contexte donné"""
        # Dans une implémentation réelle, nous utiliserions un modèle de décision
        # Pour l'instant, nous simulons l'évaluation
        
        # Score de base
        score = 0.5
        
        # Facteurs positifs
        if "priority" in option and option["priority"] == "high":
            score += 0.2
            
        if "confidence" in option and option["confidence"] > 0.7:
            score += 0.15
            
        if "alignment" in option and option["alignment"] == "high":
            score += 0.15
            
        # Facteurs négatifs
        if "risks" in option and len(option["risks"]) > 0:
            score -= 0.1 * len(option["risks"])
            
        if "cost" in option and option["cost"] == "high":
            score -= 0.2
            
        # Facteurs contextuels
        if "constraints" in context:
            for constraint in context["constraints"]:
                if "constraint_type" in constraint and constraint["constraint_type"] in option:
                    if option[constraint["constraint_type"]] != constraint.get("allowed_value"):
                        score -= 0.3
                        
        # Limiter le score entre 0 et 1
        return max(0.0, min(1.0, score))
        
    async def _save_decision_history(self):
        """Sauvegarde l'historique des décisions"""
        try:
            history_file = self.config.get("DECISION_HISTORY_FILE", "decision_history.json")
            with open(history_file, "w") as f:
                json.dump(self.decision_history, f, indent=2)
            self.logger.info(f"Decision history saved to {history_file}")
        except Exception as e:
            self.logger.error(f"Failed to save decision history: {str(e)}")
