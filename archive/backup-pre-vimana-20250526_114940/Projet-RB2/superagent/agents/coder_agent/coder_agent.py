from typing import Dict, Any, Optional, List
import asyncio
import logging
from agents.base.agent_base import BaseAgent
from .code_validator import CodeValidator

class CoderAgent(BaseAgent):
    """
    Agent spécialisé dans la génération et la modification de code.
    Capable d'analyser, générer et optimiser du code dans différents langages.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__("coder", config)
        self.supported_languages = config.get("SUPPORTED_LANGUAGES", ["python", "javascript", "typescript"])
        self.validator = CodeValidator(config.get("VALIDATION_RULES", {}))
        self.code_cache = {}

    async def _initialize(self):
        """Initialisation spécifique à l'agent de code"""
        self.logger.info("Initializing Coder Agent")
        # Charger les modèles de code et les templates
        self.code_templates = await self._load_code_templates()
        return self.code_templates

    async def _cleanup(self):
        """Nettoyage des ressources de l'agent"""
        self.logger.info("Cleaning up Coder Agent resources")
        # Sauvegarder les snippets de code générés si nécessaire
        self.code_cache.clear()

    async def process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Traite les messages reçus par l'agent"""
        action = message.get("action")

        if action == "generate_code":
            return await self.generate_code(
                language=message.get("language", "python"),
                requirements=message.get("requirements", []),
                context=message.get("context", {})
            )
        elif action == "analyze_code":
            return await self.analyze_code(
                code=message.get("code", ""),
                language=message.get("language", "python")
            )
        elif action == "optimize_code":
            return await self.optimize_code(
                code=message.get("code", ""),
                language=message.get("language", "python"),
                optimization_goals=message.get("optimization_goals", ["performance"])
            )
        elif action == "validate_code":
            return await self.validate_code(
                code=message.get("code", ""),
                language=message.get("language", "python")
            )
        else:
            self.logger.warning(f"Unknown action requested: {action}")
            return {"status": "error", "message": f"Unknown action: {action}"}

    async def generate_code(self, language: str, requirements: List[str], context: Dict[str, Any]) -> Dict[str, Any]:
        """Génère du code basé sur les exigences et le contexte fournis"""
        if language not in self.supported_languages:
            return {"status": "error", "message": f"Unsupported language: {language}"}

        self.logger.info(f"Generating {language} code with {len(requirements)} requirements")

        # Utiliser le LLM pour générer le code
        try:
            # Simulation de génération de code (à remplacer par l'appel au LLM)
            code = f"# Generated {language} code\n"
            code += f"# Requirements: {', '.join(requirements)}\n\n"

            # Ajouter du code basé sur le template correspondant
            template = self.code_templates.get(language, "")
            code += template.format(**context)

            # Valider le code généré
            validation_result = await self.validator.validate(code, language)

            if validation_result["is_valid"]:
                # Stocker dans le cache pour référence future
                cache_key = f"{language}_{hash(str(requirements))}"
                self.code_cache[cache_key] = code

                return {
                    "status": "success",
                    "code": code,
                    "language": language,
                    "validation": validation_result
                }
            else:
                return {
                    "status": "warning",
                    "code": code,
                    "language": language,
                    "validation": validation_result,
                    "message": "Generated code has validation issues"
                }

        except Exception as e:
            self.logger.error(f"Code generation error: {str(e)}")
            return {"status": "error", "message": f"Failed to generate code: {str(e)}"}

    async def analyze_code(self, code: str, language: str) -> Dict[str, Any]:
        """Analyse le code fourni et retourne des insights"""
        if language not in self.supported_languages:
            return {"status": "error", "message": f"Unsupported language: {language}"}

        self.logger.info(f"Analyzing {language} code")

        try:
            # Simulation d'analyse de code (à remplacer par l'analyse réelle)
            analysis = {
                "complexity": self._calculate_complexity(code),
                "lines_of_code": len(code.split("\n")),
                "potential_issues": await self._identify_issues(code, language),
                "dependencies": await self._extract_dependencies(code, language)
            }

            return {
                "status": "success",
                "analysis": analysis,
                "language": language
            }

        except Exception as e:
            self.logger.error(f"Code analysis error: {str(e)}")
            return {"status": "error", "message": f"Failed to analyze code: {str(e)}"}

    async def optimize_code(self, code: str, language: str, optimization_goals: List[str]) -> Dict[str, Any]:
        """Optimise le code selon les objectifs spécifiés"""
        if language not in self.supported_languages:
            return {"status": "error", "message": f"Unsupported language: {language}"}

        self.logger.info(f"Optimizing {language} code for {', '.join(optimization_goals)}")

        try:
            # Simulation d'optimisation de code (à remplacer par l'optimisation réelle)
            original_analysis = await self._quick_analysis(code, language)

            # Optimiser le code (à implémenter avec le LLM)
            optimized_code = code

            # Ajouter des commentaires sur les optimisations
            optimized_code = f"# Optimized for: {', '.join(optimization_goals)}\n" + optimized_code

            # Analyser le code optimisé
            optimized_analysis = await self._quick_analysis(optimized_code, language)

            return {
                "status": "success",
                "original_code": code,
                "optimized_code": optimized_code,
                "language": language,
                "optimization_goals": optimization_goals,
                "improvements": {
                    "complexity": original_analysis["complexity"] - optimized_analysis["complexity"],
                    "lines_of_code": original_analysis["lines_of_code"] - optimized_analysis["lines_of_code"]
                }
            }

        except Exception as e:
            self.logger.error(f"Code optimization error: {str(e)}")
            return {"status": "error", "message": f"Failed to optimize code: {str(e)}"}

    async def validate_code(self, code: str, language: str) -> Dict[str, Any]:
        """Valide le code selon les règles définies"""
        if language not in self.supported_languages:
            return {"status": "error", "message": f"Unsupported language: {language}"}

        self.logger.info(f"Validating {language} code")

        try:
            validation_result = await self.validator.validate(code, language)
            return {
                "status": "success",
                "validation": validation_result,
                "language": language
            }

        except Exception as e:
            self.logger.error(f"Code validation error: {str(e)}")
            return {"status": "error", "message": f"Failed to validate code: {str(e)}"}

    # Méthodes utilitaires privées

    async def _load_code_templates(self) -> Dict[str, str]:
        """Charge les templates de code pour différents langages"""
        # À remplacer par un chargement depuis des fichiers ou une base de données
        return {
            "python": "def main():\n    \"\"\"{description}\"\"\"\n    # TODO: Implement\n    pass\n\nif __name__ == \"__main__\":\n    main()",
            "javascript": "/**\n * {description}\n */\nfunction main() {\n    // TODO: Implement\n}\n\nmain();",
            "typescript": "/**\n * {description}\n */\nfunction main(): void {\n    // TODO: Implement\n}\n\nmain();"
        }

    def _calculate_complexity(self, code: str) -> float:
        """Calcule une métrique de complexité simplifiée du code"""
        # Implémentation simplifiée - à remplacer par une analyse plus sophistiquée
        complexity = 1.0

        # Facteurs de complexité basiques
        if_count = code.count("if ")
        for_count = code.count("for ")
        while_count = code.count("while ")

        complexity += (if_count * 0.1) + (for_count * 0.2) + (while_count * 0.3)

        return round(complexity, 2)

    async def _identify_issues(self, code: str, language: str) -> List[Dict[str, Any]]:
        """Identifie les problèmes potentiels dans le code"""
        # Implémentation simplifiée - à remplacer par une analyse plus sophistiquée
        issues = []

        # Vérifications basiques
        if "TODO" in code:
            issues.append({
                "type": "incomplete",
                "message": "Code contains TODO comments",
                "severity": "low"
            })

        if language == "python" and "except:" in code and "Exception" not in code:
            issues.append({
                "type": "bad_practice",
                "message": "Bare except clause detected",
                "severity": "medium"
            })

        return issues

    async def _extract_dependencies(self, code: str, language: str) -> List[str]:
        """Extrait les dépendances du code"""
        # Implémentation simplifiée - à remplacer par une analyse plus sophistiquée
        dependencies = []

        if language == "python":
            import_lines = [line for line in code.split("\n") if line.strip().startswith("import ") or line.strip().startswith("from ")]
            for line in import_lines:
                if line.startswith("import "):
                    dependencies.append(line.split("import ")[1].strip())
                elif line.startswith("from "):
                    module = line.split("from ")[1].split(" import")[0].strip()
                    dependencies.append(module)

        elif language in ["javascript", "typescript"]:
            import_lines = [line for line in code.split("\n") if "import " in line or "require(" in line]
            for line in import_lines:
                if "from " in line:
                    module = line.split("from ")[1].strip().replace(";", "").replace("'", "").replace("\"", "")
                    dependencies.append(module)
                elif "require(" in line:
                    module = line.split("require(")[1].split(")")[0].replace("'", "").replace("\"", "")
                    dependencies.append(module)

        return list(set(dependencies))  # Éliminer les doublons

    async def _quick_analysis(self, code: str, language: str) -> Dict[str, Any]:
        """Effectue une analyse rapide du code"""
        return {
            "complexity": self._calculate_complexity(code),
            "lines_of_code": len(code.split("\n"))
        }
