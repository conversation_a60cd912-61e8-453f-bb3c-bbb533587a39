from typing import Dict, Any, List
import re
import asyncio
import logging

class CodeValidator:
    """
    Validateur de code qui vérifie la qualité et la sécurité du code généré.
    """
    
    def __init__(self, validation_rules: Dict[str, Any]):
        self.validation_rules = validation_rules
        self.logger = logging.getLogger("agent.coder.validator")
        
    async def validate(self, code: str, language: str) -> Dict[str, Any]:
        """
        Valide le code selon les règles définies pour le langage spécifié.
        
        Args:
            code: Le code à valider
            language: Le langage de programmation du code
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        self.logger.info(f"Validating {language} code")
        
        # Initialiser le résultat
        result = {
            "is_valid": True,
            "issues": [],
            "warnings": [],
            "security_concerns": [],
            "quality_score": 0.0
        }
        
        # Appliquer les validations spécifiques au langage
        if language == "python":
            await self._validate_python(code, result)
        elif language == "javascript":
            await self._validate_javascript(code, result)
        elif language == "typescript":
            await self._validate_typescript(code, result)
        else:
            result["warnings"].append(f"No specific validation rules for {language}")
            
        # Appliquer les validations génériques
        await self._validate_generic(code, result)
        
        # Calculer le score de qualité
        result["quality_score"] = await self._calculate_quality_score(result)
        
        # Déterminer si le code est valide
        result["is_valid"] = len(result["security_concerns"]) == 0 and result["quality_score"] >= 0.6
        
        return result
        
    async def _validate_python(self, code: str, result: Dict[str, Any]):
        """Valide le code Python"""
        # Vérifier les imports dangereux
        dangerous_imports = ["os.system", "subprocess", "eval", "exec"]
        for imp in dangerous_imports:
            if imp in code:
                result["security_concerns"].append({
                    "type": "dangerous_import",
                    "message": f"Potentially dangerous import or function: {imp}",
                    "severity": "high"
                })
                
        # Vérifier les exceptions nues
        if re.search(r"except\s*:", code):
            result["issues"].append({
                "type": "bare_except",
                "message": "Bare except clause detected",
                "severity": "medium"
            })
            
        # Vérifier les variables non utilisées
        # (Implémentation simplifiée - à améliorer)
        variable_pattern = r"([a-zA-Z_][a-zA-Z0-9_]*)\s*="
        for match in re.finditer(variable_pattern, code):
            var_name = match.group(1)
            if code.count(var_name) == 1:  # Si la variable n'apparaît qu'une fois (lors de sa définition)
                result["warnings"].append({
                    "type": "unused_variable",
                    "message": f"Potentially unused variable: {var_name}",
                    "severity": "low"
                })
                
    async def _validate_javascript(self, code: str, result: Dict[str, Any]):
        """Valide le code JavaScript"""
        # Vérifier les fonctions dangereuses
        dangerous_functions = ["eval(", "Function(", "setTimeout(", "setInterval("]
        for func in dangerous_functions:
            if func in code:
                result["security_concerns"].append({
                    "type": "dangerous_function",
                    "message": f"Potentially dangerous function: {func}",
                    "severity": "high"
                })
                
        # Vérifier l'utilisation de var (préférer let/const)
        if re.search(r"\bvar\b", code):
            result["issues"].append({
                "type": "var_usage",
                "message": "Use of 'var' instead of 'let' or 'const'",
                "severity": "low"
            })
            
        # Vérifier les comparaisons non strictes
        if re.search(r"==(?!=)", code) or re.search(r"!=(?!=)", code):
            result["issues"].append({
                "type": "non_strict_comparison",
                "message": "Use of non-strict comparison (== or !=)",
                "severity": "medium"
            })
            
    async def _validate_typescript(self, code: str, result: Dict[str, Any]):
        """Valide le code TypeScript"""
        # Appliquer d'abord les validations JavaScript
        await self._validate_javascript(code, result)
        
        # Vérifier l'utilisation de any
        if re.search(r":\s*any", code):
            result["issues"].append({
                "type": "any_usage",
                "message": "Use of 'any' type",
                "severity": "medium"
            })
            
        # Vérifier les types implicites
        variable_pattern = r"(let|const)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*"
        for match in re.finditer(variable_pattern, code):
            var_name = match.group(2)
            # Vérifier si une annotation de type est présente
            if not re.search(fr"{var_name}\s*:\s*[a-zA-Z_]", code):
                result["warnings"].append({
                    "type": "implicit_type",
                    "message": f"Variable {var_name} has implicit type",
                    "severity": "low"
                })
                
    async def _validate_generic(self, code: str, result: Dict[str, Any]):
        """Applique des validations génériques à tout type de code"""
        # Vérifier les lignes trop longues
        max_line_length = self.validation_rules.get("max_line_length", 100)
        for i, line in enumerate(code.split("\n")):
            if len(line) > max_line_length:
                result["warnings"].append({
                    "type": "line_too_long",
                    "message": f"Line {i+1} exceeds maximum length of {max_line_length} characters",
                    "severity": "low",
                    "line": i+1
                })
                
        # Vérifier les commentaires TODO
        todo_pattern = r"#\s*TODO|//\s*TODO|/\*\s*TODO"
        for i, line in enumerate(code.split("\n")):
            if re.search(todo_pattern, line, re.IGNORECASE):
                result["warnings"].append({
                    "type": "todo_comment",
                    "message": f"TODO comment found on line {i+1}",
                    "severity": "low",
                    "line": i+1
                })
                
        # Vérifier les mots de passe en dur
        password_pattern = r"password\s*=\s*['\"][^'\"]+['\"]|api_key\s*=\s*['\"][^'\"]+['\"]"
        if re.search(password_pattern, code, re.IGNORECASE):
            result["security_concerns"].append({
                "type": "hardcoded_credentials",
                "message": "Hardcoded credentials detected",
                "severity": "critical"
            })
            
    async def _calculate_quality_score(self, result: Dict[str, Any]) -> float:
        """Calcule un score de qualité basé sur les problèmes détectés"""
        base_score = 1.0
        
        # Réduire le score en fonction des problèmes
        for issue in result["issues"]:
            if issue["severity"] == "high":
                base_score -= 0.2
            elif issue["severity"] == "medium":
                base_score -= 0.1
            elif issue["severity"] == "low":
                base_score -= 0.05
                
        for warning in result["warnings"]:
            if warning["severity"] == "high":
                base_score -= 0.1
            elif warning["severity"] == "medium":
                base_score -= 0.05
            elif warning["severity"] == "low":
                base_score -= 0.02
                
        for concern in result["security_concerns"]:
            if concern["severity"] == "critical":
                base_score -= 0.5
            elif concern["severity"] == "high":
                base_score -= 0.3
            elif concern["severity"] == "medium":
                base_score -= 0.2
                
        # Assurer que le score est entre 0 et 1
        return max(0.0, min(1.0, base_score))
