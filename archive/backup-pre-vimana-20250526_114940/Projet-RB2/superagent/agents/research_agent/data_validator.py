from typing import Dict, Any, List, Optional
import re
import json
import logging

class DataValidator:
    """
    Validateur de données qui vérifie la qualité et la cohérence des données collectées.
    """
    
    def __init__(self, validation_rules: Dict[str, Any]):
        self.validation_rules = validation_rules
        self.logger = logging.getLogger("agent.research.validator")
        
    async def validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Valide les données générales selon les règles définies.
        
        Args:
            data: Les données à valider
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        self.logger.info("Validating general data")
        
        # Initialiser le résultat
        result = {
            "is_valid": True,
            "issues": [],
            "warnings": [],
            "quality_score": 0.0
        }
        
        # Vérifier si les données sont vides
        if not data:
            result["is_valid"] = False
            result["issues"].append({
                "type": "empty_data",
                "message": "The data is empty",
                "severity": "high"
            })
            return result
            
        # Vérifier les types de données
        await self._validate_data_types(data, result)
        
        # Vérifier les valeurs manquantes
        await self._check_missing_values(data, result)
        
        # Vérifier les valeurs aberrantes
        await self._check_outliers(data, result)
        
        # Calculer le score de qualité
        result["quality_score"] = await self._calculate_quality_score(result)
        
        # Déterminer si les données sont valides
        result["is_valid"] = len([i for i in result["issues"] if i["severity"] == "high"]) == 0 and result["quality_score"] >= 0.5
        
        return result
        
    async def validate_search_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Valide les résultats de recherche.
        
        Args:
            results: Les résultats de recherche à valider
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        self.logger.info(f"Validating {len(results)} search results")
        
        # Initialiser le résultat
        result = {
            "is_valid": True,
            "issues": [],
            "warnings": [],
            "quality_score": 0.0,
            "diversity_score": 0.0
        }
        
        # Vérifier si les résultats sont vides
        if not results:
            result["is_valid"] = False
            result["issues"].append({
                "type": "empty_results",
                "message": "No search results found",
                "severity": "medium"
            })
            return result
            
        # Vérifier les doublons
        await self._check_duplicates(results, result)
        
        # Vérifier la diversité des sources
        await self._check_source_diversity(results, result)
        
        # Vérifier la qualité des résultats
        await self._check_result_quality(results, result)
        
        # Calculer le score de qualité
        result["quality_score"] = await self._calculate_quality_score(result)
        
        # Calculer le score de diversité
        result["diversity_score"] = await self._calculate_diversity_score(results)
        
        # Déterminer si les résultats sont valides
        result["is_valid"] = len([i for i in result["issues"] if i["severity"] == "high"]) == 0 and result["quality_score"] >= 0.5
        
        return result
        
    async def validate_against_schema(self, data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Valide les données selon un schéma spécifié.
        
        Args:
            data: Les données à valider
            schema: Le schéma de validation
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        self.logger.info("Validating data against schema")
        
        # Initialiser le résultat
        result = {
            "is_valid": True,
            "issues": [],
            "warnings": [],
            "missing_fields": [],
            "invalid_fields": []
        }
        
        # Vérifier les champs requis
        required_fields = schema.get("required", [])
        for field in required_fields:
            if field not in data:
                result["is_valid"] = False
                result["missing_fields"].append(field)
                result["issues"].append({
                    "type": "missing_required_field",
                    "message": f"Required field '{field}' is missing",
                    "severity": "high"
                })
                
        # Vérifier les types de champs
        properties = schema.get("properties", {})
        for field, field_schema in properties.items():
            if field in data:
                field_type = field_schema.get("type")
                if field_type and not self._validate_field_type(data[field], field_type):
                    result["is_valid"] = False
                    result["invalid_fields"].append(field)
                    result["issues"].append({
                        "type": "invalid_field_type",
                        "message": f"Field '{field}' has invalid type, expected {field_type}",
                        "severity": "high"
                    })
                    
                # Vérifier les contraintes supplémentaires
                if field_type == "string" and "pattern" in field_schema:
                    pattern = field_schema["pattern"]
                    if not re.match(pattern, data[field]):
                        result["is_valid"] = False
                        result["invalid_fields"].append(field)
                        result["issues"].append({
                            "type": "pattern_mismatch",
                            "message": f"Field '{field}' does not match pattern {pattern}",
                            "severity": "high"
                        })
                        
                if field_type == "number" or field_type == "integer":
                    if "minimum" in field_schema and data[field] < field_schema["minimum"]:
                        result["is_valid"] = False
                        result["invalid_fields"].append(field)
                        result["issues"].append({
                            "type": "below_minimum",
                            "message": f"Field '{field}' is below minimum {field_schema['minimum']}",
                            "severity": "high"
                        })
                        
                    if "maximum" in field_schema and data[field] > field_schema["maximum"]:
                        result["is_valid"] = False
                        result["invalid_fields"].append(field)
                        result["issues"].append({
                            "type": "above_maximum",
                            "message": f"Field '{field}' is above maximum {field_schema['maximum']}",
                            "severity": "high"
                        })
                        
                if field_type == "array":
                    if "minItems" in field_schema and len(data[field]) < field_schema["minItems"]:
                        result["is_valid"] = False
                        result["invalid_fields"].append(field)
                        result["issues"].append({
                            "type": "too_few_items",
                            "message": f"Field '{field}' has too few items, minimum {field_schema['minItems']}",
                            "severity": "high"
                        })
                        
                    if "maxItems" in field_schema and len(data[field]) > field_schema["maxItems"]:
                        result["is_valid"] = False
                        result["invalid_fields"].append(field)
                        result["issues"].append({
                            "type": "too_many_items",
                            "message": f"Field '{field}' has too many items, maximum {field_schema['maxItems']}",
                            "severity": "high"
                        })
                        
        return result
        
    # Méthodes utilitaires privées
    
    async def _validate_data_types(self, data: Dict[str, Any], result: Dict[str, Any]):
        """Valide les types de données"""
        for key, value in data.items():
            # Vérifier les types incohérents dans les listes
            if isinstance(value, list) and value:
                first_type = type(value[0])
                for item in value:
                    if not isinstance(item, first_type):
                        result["warnings"].append({
                            "type": "inconsistent_types",
                            "message": f"List '{key}' contains inconsistent types",
                            "severity": "medium"
                        })
                        break
                        
            # Vérifier les types de données attendus si spécifiés dans les règles
            if "expected_types" in self.validation_rules and key in self.validation_rules["expected_types"]:
                expected_type = self.validation_rules["expected_types"][key]
                if expected_type == "string" and not isinstance(value, str):
                    result["issues"].append({
                        "type": "invalid_type",
                        "message": f"Field '{key}' should be a string",
                        "severity": "high"
                    })
                elif expected_type == "number" and not isinstance(value, (int, float)):
                    result["issues"].append({
                        "type": "invalid_type",
                        "message": f"Field '{key}' should be a number",
                        "severity": "high"
                    })
                elif expected_type == "boolean" and not isinstance(value, bool):
                    result["issues"].append({
                        "type": "invalid_type",
                        "message": f"Field '{key}' should be a boolean",
                        "severity": "high"
                    })
                elif expected_type == "array" and not isinstance(value, list):
                    result["issues"].append({
                        "type": "invalid_type",
                        "message": f"Field '{key}' should be an array",
                        "severity": "high"
                    })
                elif expected_type == "object" and not isinstance(value, dict):
                    result["issues"].append({
                        "type": "invalid_type",
                        "message": f"Field '{key}' should be an object",
                        "severity": "high"
                    })
                    
    async def _check_missing_values(self, data: Dict[str, Any], result: Dict[str, Any]):
        """Vérifie les valeurs manquantes"""
        for key, value in data.items():
            # Vérifier les chaînes vides
            if isinstance(value, str) and not value.strip():
                result["warnings"].append({
                    "type": "empty_string",
                    "message": f"Field '{key}' is an empty string",
                    "severity": "low"
                })
                
            # Vérifier les listes vides
            if isinstance(value, list) and not value:
                result["warnings"].append({
                    "type": "empty_array",
                    "message": f"Field '{key}' is an empty array",
                    "severity": "low"
                })
                
            # Vérifier les objets vides
            if isinstance(value, dict) and not value:
                result["warnings"].append({
                    "type": "empty_object",
                    "message": f"Field '{key}' is an empty object",
                    "severity": "low"
                })
                
            # Vérifier les valeurs None
            if value is None:
                result["warnings"].append({
                    "type": "null_value",
                    "message": f"Field '{key}' has null value",
                    "severity": "medium"
                })
                
    async def _check_outliers(self, data: Dict[str, Any], result: Dict[str, Any]):
        """Vérifie les valeurs aberrantes dans les données numériques"""
        for key, value in data.items():
            if isinstance(value, (int, float)):
                # Vérifier les valeurs extrêmes si des limites sont définies
                if "numeric_limits" in self.validation_rules and key in self.validation_rules["numeric_limits"]:
                    limits = self.validation_rules["numeric_limits"][key]
                    if "min" in limits and value < limits["min"]:
                        result["warnings"].append({
                            "type": "below_min_limit",
                            "message": f"Field '{key}' is below minimum limit {limits['min']}",
                            "severity": "medium"
                        })
                    if "max" in limits and value > limits["max"]:
                        result["warnings"].append({
                            "type": "above_max_limit",
                            "message": f"Field '{key}' is above maximum limit {limits['max']}",
                            "severity": "medium"
                        })
                        
            elif isinstance(value, list) and all(isinstance(x, (int, float)) for x in value) and value:
                # Calculer des statistiques basiques
                avg = sum(value) / len(value)
                std_dev = (sum((x - avg) ** 2 for x in value) / len(value)) ** 0.5
                
                # Identifier les valeurs aberrantes (> 3 écarts-types)
                outliers = [x for x in value if abs(x - avg) > 3 * std_dev]
                if outliers:
                    result["warnings"].append({
                        "type": "statistical_outliers",
                        "message": f"Field '{key}' contains {len(outliers)} statistical outliers",
                        "severity": "low",
                        "outliers": outliers
                    })
                    
    async def _check_duplicates(self, results: List[Dict[str, Any]], result: Dict[str, Any]):
        """Vérifie les doublons dans les résultats de recherche"""
        # Implémentation simplifiée - à améliorer pour une détection plus sophistiquée
        
        # Vérifier les URLs en double
        urls = []
        for item in results:
            data = item.get("data", {})
            if "url" in data:
                url = data["url"]
                if url in urls:
                    result["warnings"].append({
                        "type": "duplicate_url",
                        "message": f"Duplicate URL found: {url}",
                        "severity": "low"
                    })
                else:
                    urls.append(url)
                    
        # Vérifier les titres identiques
        titles = []
        for item in results:
            data = item.get("data", {})
            if "title" in data:
                title = data["title"]
                if title in titles:
                    result["warnings"].append({
                        "type": "duplicate_title",
                        "message": f"Duplicate title found: {title}",
                        "severity": "low"
                    })
                else:
                    titles.append(title)
                    
    async def _check_source_diversity(self, results: List[Dict[str, Any]], result: Dict[str, Any]):
        """Vérifie la diversité des sources dans les résultats"""
        sources = {}
        
        # Compter les occurrences de chaque source
        for item in results:
            source = item.get("source")
            if source:
                sources[source] = sources.get(source, 0) + 1
                
        # Vérifier si une source est surreprésentée
        total = len(results)
        for source, count in sources.items():
            percentage = (count / total) * 100
            if percentage > 80:  # Si plus de 80% des résultats viennent d'une seule source
                result["warnings"].append({
                    "type": "source_imbalance",
                    "message": f"Source '{source}' represents {percentage:.1f}% of results",
                    "severity": "medium"
                })
                
        # Vérifier le nombre de sources
        if len(sources) == 1 and total > 5:
            result["warnings"].append({
                "type": "single_source",
                "message": "All results come from a single source",
                "severity": "medium"
            })
            
    async def _check_result_quality(self, results: List[Dict[str, Any]], result: Dict[str, Any]):
        """Vérifie la qualité des résultats de recherche"""
        for i, item in enumerate(results):
            data = item.get("data", {})
            
            # Vérifier les résultats sans titre
            if "title" in data and not data["title"]:
                result["warnings"].append({
                    "type": "empty_title",
                    "message": f"Result {i+1} has empty title",
                    "severity": "low"
                })
                
            # Vérifier les résultats sans extrait
            if "snippet" in data and not data["snippet"]:
                result["warnings"].append({
                    "type": "empty_snippet",
                    "message": f"Result {i+1} has empty snippet",
                    "severity": "low"
                })
                
            # Vérifier les URLs invalides
            if "url" in data:
                url = data["url"]
                if not url.startswith(("http://", "https://")):
                    result["issues"].append({
                        "type": "invalid_url",
                        "message": f"Result {i+1} has invalid URL: {url}",
                        "severity": "medium"
                    })
                    
    async def _calculate_quality_score(self, result: Dict[str, Any]) -> float:
        """Calcule un score de qualité basé sur les problèmes détectés"""
        base_score = 1.0
        
        # Réduire le score en fonction des problèmes
        for issue in result.get("issues", []):
            if issue["severity"] == "high":
                base_score -= 0.2
            elif issue["severity"] == "medium":
                base_score -= 0.1
            elif issue["severity"] == "low":
                base_score -= 0.05
                
        for warning in result.get("warnings", []):
            if warning["severity"] == "high":
                base_score -= 0.1
            elif warning["severity"] == "medium":
                base_score -= 0.05
            elif warning["severity"] == "low":
                base_score -= 0.02
                
        # Assurer que le score est entre 0 et 1
        return max(0.0, min(1.0, base_score))
        
    async def _calculate_diversity_score(self, results: List[Dict[str, Any]]) -> float:
        """Calcule un score de diversité pour les résultats"""
        if not results:
            return 0.0
            
        # Compter les sources
        sources = {}
        for item in results:
            source = item.get("source")
            if source:
                sources[source] = sources.get(source, 0) + 1
                
        # Calculer l'indice de diversité (plus il y a de sources différentes, mieux c'est)
        source_count = len(sources)
        max_possible_sources = min(len(results), 3)  # On considère qu'avoir 3 sources différentes est optimal
        
        # Calculer la répartition (plus c'est équilibré, mieux c'est)
        total = len(results)
        balance_score = 0.0
        if source_count > 0:
            ideal_per_source = total / source_count
            variance = sum((count - ideal_per_source) ** 2 for count in sources.values()) / source_count
            normalized_variance = min(1.0, variance / (ideal_per_source ** 2))
            balance_score = 1.0 - normalized_variance
            
        # Combiner les scores
        diversity_score = (source_count / max_possible_sources) * 0.7 + balance_score * 0.3
        
        return min(1.0, diversity_score)
        
    def _validate_field_type(self, value: Any, expected_type: str) -> bool:
        """Valide le type d'un champ selon le schéma JSON"""
        if expected_type == "string":
            return isinstance(value, str)
        elif expected_type == "number":
            return isinstance(value, (int, float))
        elif expected_type == "integer":
            return isinstance(value, int)
        elif expected_type == "boolean":
            return isinstance(value, bool)
        elif expected_type == "array":
            return isinstance(value, list)
        elif expected_type == "object":
            return isinstance(value, dict)
        elif expected_type == "null":
            return value is None
        else:
            return True  # Type inconnu, on considère que c'est valide
