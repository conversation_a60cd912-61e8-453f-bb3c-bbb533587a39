from typing import Dict, Any, Optional, List
import asyncio
import logging
import json
from datetime import datetime
from agents.base.agent_base import BaseAgent
from .data_validator import DataValidator

class ResearchAgent(BaseAgent):
    """
    Agent spécialisé dans la recherche et l'analyse d'informations.
    Capable de collecter, valider et synthétiser des données provenant de diverses sources.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("research", config)
        self.data_validator = DataValidator(config.get("VALIDATION_RULES", {}))
        self.supported_sources = config.get("SUPPORTED_SOURCES", ["web", "database", "api"])
        self.research_history = []
        self.cache = {}
        
    async def _initialize(self):
        """Initialisation spécifique à l'agent de recherche"""
        self.logger.info("Initializing Research Agent")
        # Initialiser les connecteurs aux sources de données
        self.data_connectors = await self._initialize_data_connectors()
        
    async def _cleanup(self):
        """Nettoyage des ressources de l'agent"""
        self.logger.info("Cleaning up Research Agent resources")
        # Fermer les connexions aux sources de données
        for connector in self.data_connectors.values():
            if hasattr(connector, "close") and callable(connector.close):
                await connector.close()
        
    async def process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Traite les messages reçus par l'agent"""
        action = message.get("action")
        
        if action == "search":
            return await self.search(
                query=message.get("query", ""),
                sources=message.get("sources", ["web"]),
                options=message.get("options", {})
            )
        elif action == "analyze_data":
            return await self.analyze_data(
                data=message.get("data", {}),
                analysis_type=message.get("analysis_type", "general")
            )
        elif action == "validate_data":
            return await self.validate_data(
                data=message.get("data", {}),
                schema=message.get("schema", {})
            )
        elif action == "get_research_history":
            return await self.get_research_history(
                limit=message.get("limit", 10),
                query=message.get("query")
            )
        else:
            self.logger.warning(f"Unknown action requested: {action}")
            return {"status": "error", "message": f"Unknown action: {action}"}
            
    async def search(self, query: str, sources: List[str], options: Dict[str, Any]) -> Dict[str, Any]:
        """Effectue une recherche sur les sources spécifiées"""
        # Vérifier que les sources sont supportées
        unsupported_sources = [s for s in sources if s not in self.supported_sources]
        if unsupported_sources:
            return {"status": "error", "message": f"Unsupported sources: {', '.join(unsupported_sources)}"}
            
        self.logger.info(f"Searching for '{query}' in sources: {', '.join(sources)}")
        
        try:
            # Vérifier le cache
            cache_key = f"{query}_{'-'.join(sorted(sources))}_{json.dumps(options)}"
            if cache_key in self.cache and not options.get("bypass_cache", False):
                self.logger.info(f"Returning cached results for '{query}'")
                return self.cache[cache_key]
                
            # Collecter les résultats de chaque source
            results = {}
            for source in sources:
                source_results = await self._search_source(source, query, options)
                results[source] = source_results
                
            # Fusionner et trier les résultats
            merged_results = await self._merge_results(results, options)
            
            # Valider les résultats
            validation_result = await self.data_validator.validate_search_results(merged_results)
            
            # Enregistrer dans l'historique
            research_entry = {
                "id": len(self.research_history) + 1,
                "timestamp": datetime.now().isoformat(),
                "query": query,
                "sources": sources,
                "options": options,
                "result_count": len(merged_results)
            }
            self.research_history.append(research_entry)
            
            # Mettre en cache les résultats
            if options.get("cache_results", True):
                self.cache[cache_key] = {
                    "status": "success",
                    "results": merged_results,
                    "sources": sources,
                    "validation": validation_result,
                    "research_id": research_entry["id"],
                    "timestamp": research_entry["timestamp"]
                }
                
            return {
                "status": "success",
                "results": merged_results,
                "sources": sources,
                "validation": validation_result,
                "research_id": research_entry["id"],
                "timestamp": research_entry["timestamp"]
            }
            
        except Exception as e:
            self.logger.error(f"Search error: {str(e)}")
            return {"status": "error", "message": f"Failed to search: {str(e)}"}
            
    async def analyze_data(self, data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """Analyse les données fournies selon le type d'analyse spécifié"""
        self.logger.info(f"Analyzing data with analysis type: {analysis_type}")
        
        try:
            # Valider les données avant analyse
            validation_result = await self.data_validator.validate_data(data)
            if not validation_result["is_valid"]:
                return {
                    "status": "error",
                    "message": "Invalid data for analysis",
                    "validation": validation_result
                }
                
            # Effectuer l'analyse selon le type
            if analysis_type == "general":
                analysis_result = await self._perform_general_analysis(data)
            elif analysis_type == "statistical":
                analysis_result = await self._perform_statistical_analysis(data)
            elif analysis_type == "sentiment":
                analysis_result = await self._perform_sentiment_analysis(data)
            elif analysis_type == "trend":
                analysis_result = await self._perform_trend_analysis(data)
            else:
                return {"status": "error", "message": f"Unsupported analysis type: {analysis_type}"}
                
            return {
                "status": "success",
                "analysis": analysis_result,
                "analysis_type": analysis_type,
                "data_size": len(json.dumps(data))
            }
            
        except Exception as e:
            self.logger.error(f"Data analysis error: {str(e)}")
            return {"status": "error", "message": f"Failed to analyze data: {str(e)}"}
            
    async def validate_data(self, data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """Valide les données selon le schéma fourni"""
        self.logger.info("Validating data against schema")
        
        try:
            # Valider les données
            validation_result = await self.data_validator.validate_against_schema(data, schema)
            
            return {
                "status": "success",
                "validation": validation_result,
                "data_size": len(json.dumps(data))
            }
            
        except Exception as e:
            self.logger.error(f"Data validation error: {str(e)}")
            return {"status": "error", "message": f"Failed to validate data: {str(e)}"}
            
    async def get_research_history(self, limit: int, query: Optional[str] = None) -> Dict[str, Any]:
        """Récupère l'historique des recherches effectuées"""
        self.logger.info(f"Retrieving research history (limit: {limit}, query: {query})")
        
        try:
            # Filtrer par requête si spécifiée
            filtered_history = self.research_history
            if query:
                filtered_history = [r for r in filtered_history if query.lower() in r["query"].lower()]
                
            # Limiter le nombre de résultats
            limited_history = filtered_history[-limit:] if limit > 0 else filtered_history
            
            return {
                "status": "success",
                "history": limited_history,
                "total_researches": len(filtered_history),
                "returned_researches": len(limited_history)
            }
            
        except Exception as e:
            self.logger.error(f"Research history retrieval error: {str(e)}")
            return {"status": "error", "message": f"Failed to retrieve research history: {str(e)}"}
    
    # Méthodes utilitaires privées
    
    async def _initialize_data_connectors(self) -> Dict[str, Any]:
        """Initialise les connecteurs aux différentes sources de données"""
        # Implémentation simplifiée - à remplacer par des connecteurs réels
        return {
            "web": {"type": "web", "status": "initialized"},
            "database": {"type": "database", "status": "initialized"},
            "api": {"type": "api", "status": "initialized"}
        }
        
    async def _search_source(self, source: str, query: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Effectue une recherche sur une source spécifique"""
        # Implémentation simplifiée - à remplacer par des recherches réelles
        
        # Simuler un délai de recherche
        await asyncio.sleep(0.5)
        
        if source == "web":
            return [
                {"title": f"Web result 1 for {query}", "url": f"https://example.com/1?q={query}", "snippet": "This is a sample result..."},
                {"title": f"Web result 2 for {query}", "url": f"https://example.com/2?q={query}", "snippet": "Another sample result..."}
            ]
        elif source == "database":
            return [
                {"id": 1, "name": f"DB result 1 for {query}", "data": {"field1": "value1", "field2": "value2"}},
                {"id": 2, "name": f"DB result 2 for {query}", "data": {"field1": "value3", "field2": "value4"}}
            ]
        elif source == "api":
            return [
                {"api_id": "a1", "title": f"API result 1 for {query}", "content": {"key1": "value1", "key2": "value2"}},
                {"api_id": "a2", "title": f"API result 2 for {query}", "content": {"key1": "value3", "key2": "value4"}}
            ]
        else:
            return []
            
    async def _merge_results(self, results: Dict[str, List[Dict[str, Any]]], 
                           options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fusionne les résultats de différentes sources"""
        # Implémentation simplifiée - à remplacer par une fusion plus sophistiquée
        
        merged = []
        
        # Ajouter les résultats de chaque source avec leur provenance
        for source, source_results in results.items():
            for result in source_results:
                merged.append({
                    "source": source,
                    "data": result,
                    "relevance": self._calculate_relevance(result)
                })
                
        # Trier par pertinence si demandé
        if options.get("sort_by_relevance", True):
            merged.sort(key=lambda x: x["relevance"], reverse=True)
            
        # Limiter le nombre de résultats si demandé
        limit = options.get("limit", 10)
        if limit > 0:
            merged = merged[:limit]
            
        return merged
        
    def _calculate_relevance(self, result: Dict[str, Any]) -> float:
        """Calcule un score de pertinence pour un résultat"""
        # Implémentation simplifiée - à remplacer par un calcul plus sophistiqué
        relevance = 0.5  # Score de base
        
        # Facteurs de pertinence basiques
        if "title" in result and len(result["title"]) > 0:
            relevance += 0.2
            
        if "snippet" in result and len(result["snippet"]) > 50:
            relevance += 0.1
            
        if "url" in result and "example.com" in result["url"]:
            relevance += 0.1
            
        return min(1.0, relevance)
        
    async def _perform_general_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Effectue une analyse générale des données"""
        # Implémentation simplifiée - à remplacer par une analyse plus sophistiquée
        
        analysis = {
            "summary": "General analysis of the provided data",
            "data_types": {},
            "structure": {},
            "key_insights": []
        }
        
        # Analyser les types de données
        for key, value in data.items():
            if isinstance(value, dict):
                analysis["data_types"][key] = "object"
                analysis["structure"][key] = len(value)
            elif isinstance(value, list):
                analysis["data_types"][key] = "array"
                analysis["structure"][key] = len(value)
            elif isinstance(value, str):
                analysis["data_types"][key] = "string"
                analysis["structure"][key] = len(value)
            elif isinstance(value, (int, float)):
                analysis["data_types"][key] = "number"
                analysis["structure"][key] = value
            elif isinstance(value, bool):
                analysis["data_types"][key] = "boolean"
                analysis["structure"][key] = value
            else:
                analysis["data_types"][key] = "unknown"
                
        # Générer quelques insights basiques
        if len(data) > 5:
            analysis["key_insights"].append("The data contains multiple fields")
            
        for key, value in data.items():
            if isinstance(value, list) and len(value) > 10:
                analysis["key_insights"].append(f"The field '{key}' contains a large array")
                
        return analysis
        
    async def _perform_statistical_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Effectue une analyse statistique des données numériques"""
        # Implémentation simplifiée - à remplacer par une analyse plus sophistiquée
        
        analysis = {
            "summary": "Statistical analysis of the provided data",
            "numeric_fields": {},
            "correlations": []
        }
        
        # Analyser les champs numériques
        numeric_fields = {}
        for key, value in data.items():
            if isinstance(value, (int, float)):
                numeric_fields[key] = value
            elif isinstance(value, list) and all(isinstance(x, (int, float)) for x in value):
                numeric_fields[key] = value
                
        # Calculer des statistiques basiques pour chaque champ numérique
        for key, value in numeric_fields.items():
            if isinstance(value, list):
                analysis["numeric_fields"][key] = {
                    "min": min(value),
                    "max": max(value),
                    "avg": sum(value) / len(value),
                    "count": len(value)
                }
            else:
                analysis["numeric_fields"][key] = {
                    "value": value
                }
                
        return analysis
        
    async def _perform_sentiment_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Effectue une analyse de sentiment sur les données textuelles"""
        # Implémentation simplifiée - à remplacer par une analyse plus sophistiquée
        
        analysis = {
            "summary": "Sentiment analysis of the provided data",
            "text_fields": {},
            "overall_sentiment": "neutral"
        }
        
        # Analyser les champs textuels
        text_fields = {}
        for key, value in data.items():
            if isinstance(value, str) and len(value) > 10:
                text_fields[key] = value
            elif isinstance(value, list) and all(isinstance(x, str) for x in value):
                text_fields[key] = " ".join(value)
                
        # Simuler une analyse de sentiment basique
        positive_words = ["good", "great", "excellent", "positive", "happy", "satisfied"]
        negative_words = ["bad", "poor", "negative", "unhappy", "disappointed", "terrible"]
        
        for key, text in text_fields.items():
            text_lower = text.lower()
            positive_count = sum(text_lower.count(word) for word in positive_words)
            negative_count = sum(text_lower.count(word) for word in negative_words)
            
            if positive_count > negative_count:
                sentiment = "positive"
            elif negative_count > positive_count:
                sentiment = "negative"
            else:
                sentiment = "neutral"
                
            analysis["text_fields"][key] = {
                "sentiment": sentiment,
                "positive_count": positive_count,
                "negative_count": negative_count,
                "text_length": len(text)
            }
            
        # Calculer le sentiment global
        sentiments = [field["sentiment"] for field in analysis["text_fields"].values()]
        positive_count = sentiments.count("positive")
        negative_count = sentiments.count("negative")
        
        if positive_count > negative_count:
            analysis["overall_sentiment"] = "positive"
        elif negative_count > positive_count:
            analysis["overall_sentiment"] = "negative"
        else:
            analysis["overall_sentiment"] = "neutral"
            
        return analysis
        
    async def _perform_trend_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Effectue une analyse de tendance sur les données temporelles"""
        # Implémentation simplifiée - à remplacer par une analyse plus sophistiquée
        
        analysis = {
            "summary": "Trend analysis of the provided data",
            "time_series": {},
            "trends": []
        }
        
        # Vérifier si les données contiennent des séries temporelles
        time_series_data = {}
        
        # Chercher des champs qui pourraient être des séries temporelles
        for key, value in data.items():
            if isinstance(value, list) and len(value) > 2:
                # Vérifier si chaque élément a une date/heure et une valeur
                if all(isinstance(item, dict) and "date" in item and "value" in item for item in value):
                    time_series_data[key] = value
                    
        # Analyser chaque série temporelle
        for key, series in time_series_data.items():
            # Trier par date
            sorted_series = sorted(series, key=lambda x: x["date"])
            
            # Extraire les valeurs
            values = [item["value"] for item in sorted_series]
            
            # Calculer la tendance (simplifiée)
            if len(values) >= 2:
                first_value = values[0]
                last_value = values[-1]
                
                if last_value > first_value:
                    trend = "increasing"
                elif last_value < first_value:
                    trend = "decreasing"
                else:
                    trend = "stable"
                    
                # Calculer le changement en pourcentage
                if first_value != 0:
                    percent_change = ((last_value - first_value) / abs(first_value)) * 100
                else:
                    percent_change = 0
                    
                analysis["time_series"][key] = {
                    "trend": trend,
                    "start_value": first_value,
                    "end_value": last_value,
                    "percent_change": round(percent_change, 2),
                    "data_points": len(values)
                }
                
                analysis["trends"].append({
                    "series": key,
                    "trend": trend,
                    "percent_change": round(percent_change, 2)
                })
                
        return analysis
