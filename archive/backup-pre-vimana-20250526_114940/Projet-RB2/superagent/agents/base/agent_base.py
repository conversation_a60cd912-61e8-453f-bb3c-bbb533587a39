from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import asyncio
import logging
from utils.security.encryption import encrypt_message, decrypt_message

class BaseAgent(ABC):
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        self.agent_id = agent_id
        self.config = config
        self.logger = logging.getLogger(f"agent.{agent_id}")
        self._is_running = False

        # Créer la queue de messages uniquement si nous sommes dans une boucle d'événements
        try:
            asyncio.get_event_loop()
            self._message_queue = asyncio.Queue()
        except RuntimeError:
            # Pas de boucle d'événements, on ne crée pas de queue pour le moment
            self._message_queue = None

    async def start(self):
        """Démarre l'agent de manière sécurisée"""
        self._is_running = True

        # S'assurer que la queue de messages existe
        if self._message_queue is None:
            self._message_queue = asyncio.Queue()

        try:
            await self._initialize()
            await self._process_queue()
        except Exception as e:
            self.logger.error(f"Agent error: {str(e)}")
            await self.stop()

    async def stop(self):
        """Arrête l'agent proprement"""
        self._is_running = False
        await self._cleanup()

    @abstractmethod
    async def process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Traite un message reçu"""
        pass

    @abstractmethod
    async def _initialize(self):
        """Initialisation spécifique à l'agent"""
        pass

    @abstractmethod
    async def _cleanup(self):
        """Nettoyage spécifique à l'agent"""
        pass

    async def send_message(self, target_agent: str, message: Dict[str, Any]):
        """Envoie un message sécurisé à un autre agent"""
        encrypted_message = encrypt_message(message)

        # S'assurer que la queue de messages existe
        if self._message_queue is None:
            self._message_queue = asyncio.Queue()

        await self._message_queue.put({
            "target": target_agent,
            "payload": encrypted_message
        })

    async def _process_queue(self):
        """Traite les messages dans la queue"""
        if self._message_queue is None:
            self._message_queue = asyncio.Queue()

        while self._is_running:
            try:
                message = await self._message_queue.get()
                target = message.get("target")
                payload = message.get("payload")

                # Traitement du message (à implémenter dans une version plus complète)
                self.logger.info(f"Processing message for {target}")

                self._message_queue.task_done()
            except Exception as e:
                self.logger.error(f"Error processing message: {str(e)}")
                # Continuer malgré l'erreur