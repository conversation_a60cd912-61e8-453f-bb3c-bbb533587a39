from typing import Dict, List, Any, Optional
from agents.base.agent_base import BaseAgent
import asyncio
import logging
import uuid

class Coordinator(BaseAgent):
    def __init__(self, config: Dict[str, Any]):
        super().__init__("coordinator", config)
        self.active_agents: Dict[str, BaseAgent] = {}
        self.workflows: Dict[str, Dict] = {}

    async def _initialize(self):
        """Initialisation spécifique au coordinateur"""
        self.logger.info("Initializing Coordinator")
        # Aucune initialisation spécifique nécessaire pour le moment
        pass

    async def _cleanup(self):
        """Nettoyage des ressources du coordinateur"""
        self.logger.info("Cleaning up Coordinator resources")
        # Arrêter tous les agents actifs
        for agent_id, agent in list(self.active_agents.items()):
            await agent.stop()
            del self.active_agents[agent_id]

    async def process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Traite les messages reçus par le coordinateur"""
        action = message.get("action")

        if action == "register_agent":
            success = await self.register_agent(message.get("agent"))
            return {"status": "success" if success else "error"}
        elif action == "create_workflow":
            workflow_id = await self.create_workflow(message.get("workflow_config", {}))
            return {"status": "success", "workflow_id": workflow_id}
        elif action == "execute_workflow":
            await self.execute_workflow(message.get("workflow_id", ""))
            return {"status": "success"}
        else:
            self.logger.warning(f"Unknown action requested: {action}")
            return {"status": "error", "message": f"Unknown action: {action}"}

    async def register_agent(self, agent: BaseAgent) -> bool:
        """Enregistre un nouvel agent de manière sécurisée"""
        if len(self.active_agents) >= self.config["MAX_CONCURRENT_AGENTS"]:
            self.logger.warning("Maximum agent limit reached")
            return False

        self.active_agents[agent.agent_id] = agent
        self.logger.info(f"Agent {agent.agent_id} registered")
        return True

    async def create_workflow(self, workflow_config: Dict[str, Any]) -> str:
        """Crée un nouveau workflow"""
        workflow_id = self._generate_workflow_id()
        self.workflows[workflow_id] = {
            "config": workflow_config,
            "status": "initialized",
            "agents": []
        }
        return workflow_id

    async def execute_workflow(self, workflow_id: str):
        """Exécute un workflow de manière orchestrée"""
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")

        workflow = self.workflows[workflow_id]
        workflow["status"] = "running"

        try:
            # Exécution séquentielle des tâches
            for task in workflow["config"]["tasks"]:
                await self._execute_task(workflow_id, task)

            workflow["status"] = "completed"
        except Exception as e:
            workflow["status"] = "failed"
            self.logger.error(f"Workflow {workflow_id} failed: {str(e)}")
            raise

    def _generate_workflow_id(self) -> str:
        """Génère un identifiant unique pour un workflow"""
        return str(uuid.uuid4())

    async def _execute_task(self, workflow_id: str, task: Dict[str, Any]):
        """Exécute une tâche spécifique dans un workflow"""
        agent_id = task.get("agent")
        action = task.get("action")
        params = task.get("params", {})

        if agent_id not in self.active_agents:
            raise ValueError(f"Agent {agent_id} not found")

        agent = self.active_agents[agent_id]
        message = {"action": action, **params}

        self.logger.info(f"Executing task: {agent_id}.{action}")
        result = await agent.process_message(message)

        return result