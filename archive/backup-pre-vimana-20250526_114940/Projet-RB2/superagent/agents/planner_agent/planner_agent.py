from typing import Dict, Any, Optional, List
import asyncio
import logging
import json
from datetime import datetime
from agents.base.agent_base import BaseAgent

class PlannerAgent(BaseAgent):
    """
    Agent spécialisé dans la planification et la décomposition de tâches complexes.
    Capable de créer des plans détaillés et de les adapter en fonction des contraintes.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("planner", config)
        self.plan_templates = config.get("PLAN_TEMPLATES", {})
        self.plan_history = []
        
    async def _initialize(self):
        """Initialisation spécifique à l'agent de planification"""
        self.logger.info("Initializing Planner Agent")
        # Charger les templates de plan
        if not self.plan_templates:
            self.plan_templates = await self._load_default_templates()
        
    async def _cleanup(self):
        """Nettoyage des ressources de l'agent"""
        self.logger.info("Cleaning up Planner Agent resources")
        # Sauvegarder l'historique des plans si nécessaire
        if self.config.get("SAVE_PLAN_HISTORY", False):
            await self._save_plan_history()
        
    async def process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Traite les messages reçus par l'agent"""
        action = message.get("action")
        
        if action == "create_plan":
            return await self.create_plan(
                objective=message.get("objective", ""),
                constraints=message.get("constraints", []),
                resources=message.get("resources", {}),
                deadline=message.get("deadline")
            )
        elif action == "refine_plan":
            return await self.refine_plan(
                plan_id=message.get("plan_id", ""),
                feedback=message.get("feedback", ""),
                new_constraints=message.get("new_constraints", [])
            )
        elif action == "get_plan_status":
            return await self.get_plan_status(
                plan_id=message.get("plan_id", "")
            )
        else:
            self.logger.warning(f"Unknown action requested: {action}")
            return {"status": "error", "message": f"Unknown action: {action}"}
            
    async def create_plan(self, objective: str, constraints: List[str], 
                        resources: Dict[str, Any], deadline: Optional[str] = None) -> Dict[str, Any]:
        """
        Crée un plan détaillé pour atteindre un objectif.
        
        Args:
            objective: L'objectif à atteindre
            constraints: Les contraintes à respecter
            resources: Les ressources disponibles
            deadline: La date limite (optionnelle)
            
        Returns:
            Le plan créé
        """
        self.logger.info(f"Creating plan for objective: {objective}")
        
        if not objective:
            return {"status": "error", "message": "No objective provided"}
            
        try:
            # Générer un identifiant unique pour le plan
            plan_id = f"plan_{len(self.plan_history) + 1}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            # Déterminer le template à utiliser
            template_key = self._determine_template(objective, constraints)
            template = self.plan_templates.get(template_key, self.plan_templates.get("default", {}))
            
            # Créer le plan
            plan = await self._generate_plan(objective, constraints, resources, deadline, template)
            
            # Enregistrer le plan dans l'historique
            plan_entry = {
                "id": plan_id,
                "objective": objective,
                "constraints": constraints,
                "resources": resources,
                "deadline": deadline,
                "plan": plan,
                "status": "created",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            self.plan_history.append(plan_entry)
            
            return {
                "status": "success",
                "plan_id": plan_id,
                "plan": plan
            }
            
        except Exception as e:
            self.logger.error(f"Plan creation error: {str(e)}")
            return {"status": "error", "message": f"Failed to create plan: {str(e)}"}
            
    async def refine_plan(self, plan_id: str, feedback: str, new_constraints: List[str]) -> Dict[str, Any]:
        """
        Raffine un plan existant en fonction du feedback et des nouvelles contraintes.
        
        Args:
            plan_id: L'identifiant du plan à raffiner
            feedback: Le feedback sur le plan
            new_constraints: Les nouvelles contraintes à prendre en compte
            
        Returns:
            Le plan raffiné
        """
        self.logger.info(f"Refining plan: {plan_id}")
        
        # Trouver le plan dans l'historique
        plan_entry = next((p for p in self.plan_history if p["id"] == plan_id), None)
        
        if not plan_entry:
            return {"status": "error", "message": f"Plan not found: {plan_id}"}
            
        try:
            # Mettre à jour les contraintes
            updated_constraints = plan_entry["constraints"] + new_constraints
            
            # Raffiner le plan
            refined_plan = await self._refine_existing_plan(
                plan_entry["plan"],
                feedback,
                updated_constraints,
                plan_entry["resources"],
                plan_entry["deadline"]
            )
            
            # Mettre à jour l'entrée du plan
            plan_entry["plan"] = refined_plan
            plan_entry["constraints"] = updated_constraints
            plan_entry["status"] = "refined"
            plan_entry["updated_at"] = datetime.now().isoformat()
            
            return {
                "status": "success",
                "plan_id": plan_id,
                "plan": refined_plan
            }
            
        except Exception as e:
            self.logger.error(f"Plan refinement error: {str(e)}")
            return {"status": "error", "message": f"Failed to refine plan: {str(e)}"}
            
    async def get_plan_status(self, plan_id: str) -> Dict[str, Any]:
        """
        Récupère le statut d'un plan.
        
        Args:
            plan_id: L'identifiant du plan
            
        Returns:
            Le statut du plan
        """
        self.logger.info(f"Getting status for plan: {plan_id}")
        
        # Trouver le plan dans l'historique
        plan_entry = next((p for p in self.plan_history if p["id"] == plan_id), None)
        
        if not plan_entry:
            return {"status": "error", "message": f"Plan not found: {plan_id}"}
            
        return {
            "status": "success",
            "plan_id": plan_id,
            "plan_status": plan_entry["status"],
            "created_at": plan_entry["created_at"],
            "updated_at": plan_entry["updated_at"]
        }
    
    # Méthodes utilitaires privées
    
    async def _load_default_templates(self) -> Dict[str, Any]:
        """Charge les templates de plan par défaut"""
        return {
            "default": {
                "sections": ["objectives", "tasks", "timeline", "resources", "risks"],
                "task_structure": {
                    "id": "",
                    "name": "",
                    "description": "",
                    "dependencies": [],
                    "estimated_duration": "",
                    "required_resources": []
                }
            },
            "research": {
                "sections": ["objectives", "research_questions", "methodology", "data_sources", "analysis_approach", "timeline", "resources", "risks"],
                "task_structure": {
                    "id": "",
                    "name": "",
                    "description": "",
                    "dependencies": [],
                    "estimated_duration": "",
                    "required_resources": [],
                    "expected_outcomes": []
                }
            },
            "development": {
                "sections": ["objectives", "requirements", "architecture", "tasks", "testing_strategy", "deployment_plan", "timeline", "resources", "risks"],
                "task_structure": {
                    "id": "",
                    "name": "",
                    "description": "",
                    "dependencies": [],
                    "estimated_duration": "",
                    "required_resources": [],
                    "acceptance_criteria": []
                }
            }
        }
        
    def _determine_template(self, objective: str, constraints: List[str]) -> str:
        """Détermine le template à utiliser en fonction de l'objectif et des contraintes"""
        # Mots-clés pour la recherche
        research_keywords = ["research", "analyze", "study", "investigate", "explore", "survey", "review"]
        
        # Mots-clés pour le développement
        development_keywords = ["develop", "build", "create", "implement", "code", "program", "design", "architect"]
        
        # Vérifier si l'objectif contient des mots-clés de recherche
        if any(keyword in objective.lower() for keyword in research_keywords):
            return "research"
            
        # Vérifier si l'objectif contient des mots-clés de développement
        if any(keyword in objective.lower() for keyword in development_keywords):
            return "development"
            
        # Template par défaut
        return "default"
        
    async def _generate_plan(self, objective: str, constraints: List[str], 
                           resources: Dict[str, Any], deadline: Optional[str], 
                           template: Dict[str, Any]) -> Dict[str, Any]:
        """Génère un plan détaillé"""
        # Dans une implémentation réelle, nous utiliserions un LLM pour générer le plan
        # Pour l'instant, nous créons un plan simple basé sur le template
        
        sections = template.get("sections", [])
        task_structure = template.get("task_structure", {})
        
        plan = {
            "objective": objective,
            "constraints": constraints,
            "deadline": deadline
        }
        
        # Ajouter les sections du template
        for section in sections:
            if section == "objectives":
                plan["objectives"] = [objective]
                
            elif section == "tasks":
                # Générer des tâches simples
                tasks = []
                
                # Décomposer l'objectif en 3-5 tâches
                words = objective.split()
                num_tasks = min(5, max(3, len(words) // 3))
                
                for i in range(num_tasks):
                    task = task_structure.copy()
                    task["id"] = f"task_{i+1}"
                    task["name"] = f"Task {i+1}"
                    task["description"] = f"Subtask for achieving: {objective}"
                    task["estimated_duration"] = "1 day"
                    
                    if i > 0:
                        task["dependencies"] = [f"task_{i}"]
                        
                    tasks.append(task)
                    
                plan["tasks"] = tasks
                
            elif section == "timeline":
                # Créer une timeline simple
                if "tasks" in plan:
                    timeline = []
                    start_date = datetime.now()
                    
                    for i, task in enumerate(plan["tasks"]):
                        days_offset = i * 1  # 1 jour par tâche
                        task_date = (start_date.replace(hour=0, minute=0, second=0, microsecond=0) + 
                                    datetime.timedelta(days=days_offset))
                        
                        timeline.append({
                            "task_id": task["id"],
                            "start_date": task_date.isoformat(),
                            "end_date": (task_date + datetime.timedelta(days=1)).isoformat()
                        })
                        
                    plan["timeline"] = timeline
                    
            elif section == "resources":
                # Utiliser les ressources fournies
                plan["resources"] = resources
                
            elif section == "risks":
                # Générer des risques simples
                plan["risks"] = [
                    {
                        "description": "Time constraints may affect quality",
                        "probability": "medium",
                        "impact": "high",
                        "mitigation": "Prioritize tasks and allocate buffer time"
                    },
                    {
                        "description": "Resource limitations",
                        "probability": "medium",
                        "impact": "medium",
                        "mitigation": "Identify alternative resources or adjust scope"
                    }
                ]
                
            elif section == "research_questions":
                # Générer des questions de recherche simples
                plan["research_questions"] = [
                    f"What are the key factors affecting {objective}?",
                    f"How can we measure success for {objective}?",
                    f"What existing approaches address similar objectives?"
                ]
                
            elif section == "methodology":
                # Générer une méthodologie simple
                plan["methodology"] = {
                    "approach": "Mixed methods (qualitative and quantitative)",
                    "data_collection": ["Literature review", "Surveys", "Interviews"],
                    "analysis": ["Thematic analysis", "Statistical analysis"]
                }
                
            elif section == "data_sources":
                # Générer des sources de données simples
                plan["data_sources"] = [
                    {"name": "Academic literature", "type": "secondary", "access": "available"},
                    {"name": "Industry reports", "type": "secondary", "access": "requires subscription"},
                    {"name": "User surveys", "type": "primary", "access": "to be collected"}
                ]
                
            elif section == "requirements":
                # Générer des exigences simples
                plan["requirements"] = [
                    {"id": "REQ-1", "description": f"The solution must address {objective}", "priority": "high"},
                    {"id": "REQ-2", "description": "The solution must be scalable", "priority": "medium"},
                    {"id": "REQ-3", "description": "The solution must be secure", "priority": "high"}
                ]
                
            elif section == "architecture":
                # Générer une architecture simple
                plan["architecture"] = {
                    "components": [
                        {"name": "Frontend", "description": "User interface"},
                        {"name": "Backend", "description": "Business logic"},
                        {"name": "Database", "description": "Data storage"}
                    ],
                    "interactions": [
                        {"from": "Frontend", "to": "Backend", "description": "API calls"},
                        {"from": "Backend", "to": "Database", "description": "Data queries"}
                    ]
                }
                
            elif section == "testing_strategy":
                # Générer une stratégie de test simple
                plan["testing_strategy"] = {
                    "unit_testing": "Jest for frontend, pytest for backend",
                    "integration_testing": "API testing with Postman",
                    "user_testing": "Beta testing with selected users"
                }
                
            elif section == "deployment_plan":
                # Générer un plan de déploiement simple
                plan["deployment_plan"] = {
                    "environments": ["Development", "Staging", "Production"],
                    "steps": [
                        "Build and test in Development",
                        "Deploy to Staging for QA",
                        "Deploy to Production after approval"
                    ],
                    "rollback_strategy": "Automated rollback on failure detection"
                }
                
        return plan
        
    async def _refine_existing_plan(self, existing_plan: Dict[str, Any], feedback: str, 
                                  constraints: List[str], resources: Dict[str, Any], 
                                  deadline: Optional[str]) -> Dict[str, Any]:
        """Raffine un plan existant en fonction du feedback et des contraintes"""
        # Dans une implémentation réelle, nous utiliserions un LLM pour raffiner le plan
        # Pour l'instant, nous ajoutons simplement le feedback et les contraintes
        
        refined_plan = existing_plan.copy()
        
        # Ajouter une section de feedback
        refined_plan["feedback"] = feedback
        
        # Mettre à jour les contraintes
        refined_plan["constraints"] = constraints
        
        # Ajouter une tâche supplémentaire basée sur le feedback
        if "tasks" in refined_plan:
            new_task = {
                "id": f"task_{len(refined_plan['tasks']) + 1}",
                "name": "Additional task based on feedback",
                "description": f"Address feedback: {feedback[:50]}...",
                "dependencies": [refined_plan["tasks"][-1]["id"]],
                "estimated_duration": "1 day",
                "required_resources": []
            }
            
            refined_plan["tasks"].append(new_task)
            
            # Mettre à jour la timeline si elle existe
            if "timeline" in refined_plan:
                last_end_date = datetime.fromisoformat(refined_plan["timeline"][-1]["end_date"])
                
                refined_plan["timeline"].append({
                    "task_id": new_task["id"],
                    "start_date": last_end_date.isoformat(),
                    "end_date": (last_end_date + datetime.timedelta(days=1)).isoformat()
                })
                
        # Ajouter un risque supplémentaire basé sur le feedback
        if "risks" in refined_plan:
            new_risk = {
                "description": f"Risk identified from feedback: {feedback[:30]}...",
                "probability": "medium",
                "impact": "medium",
                "mitigation": "Address feedback promptly and validate with stakeholders"
            }
            
            refined_plan["risks"].append(new_risk)
            
        return refined_plan
        
    async def _save_plan_history(self):
        """Sauvegarde l'historique des plans"""
        try:
            history_file = self.config.get("PLAN_HISTORY_FILE", "plan_history.json")
            with open(history_file, "w") as f:
                json.dump(self.plan_history, f, indent=2)
            self.logger.info(f"Plan history saved to {history_file}")
        except Exception as e:
            self.logger.error(f"Failed to save plan history: {str(e)}")
