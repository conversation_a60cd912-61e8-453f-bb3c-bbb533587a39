"""
Researcher node implementation.
The researcher is responsible for gathering information and context.
"""

from superagent.graph.types import State
from superagent.agents.research_agent.research_agent import ResearchAgent
from datetime import datetime
import logging
import json

logger = logging.getLogger(__name__)

async def researcher_node(state: State) -> State:
    """
    Researcher node implementation.

    The researcher is responsible for:
    - Gathering information from various sources
    - Analyzing and synthesizing information
    - Providing context for other agents

    Args:
        state: The current workflow state

    Returns:
        Updated workflow state
    """
    logger.info("Researcher node processing...")

    # Record this step in history with actual timestamp
    state.history.append({
        "agent": "researcher",
        "action": "gather_information",
        "timestamp": datetime.now().isoformat()
    })

    # Get task details
    task = state.task or {}

    # Initialize the research agent with configuration from state context
    agent_config = state.context.get("agent_config", {}).get("research", {})
    research_agent = ResearchAgent(agent_config)

    try:
        # Extract query from task
        query = task.get("query", "")
        if not query and task.get("description"):
            query = task.get("description")

        if not query and task.get("title"):
            query = task.get("title")

        # Determine sources and options
        sources = task.get("sources", ["web"])
        options = task.get("search_options", {})

        # Call the actual research agent to perform the search
        search_result = await research_agent.search(
            query=query,
            sources=sources,
            options=options
        )

        if search_result.get("status") != "success":
            # Handle error case
            logger.error(f"Research search failed: {search_result.get('message', 'Unknown error')}")
            state.error = f"Research search failed: {search_result.get('message', 'Unknown error')}"
            research_results = {
                "error": search_result.get('message', 'Unknown error'),
                "summary": "Failed to gather information"
            }
        else:
            # Process successful search
            search_data = search_result.get("results", [])

            # Analyze the search results
            analysis_result = await research_agent.analyze_data(
                data={"search_results": search_data},
                analysis_type=task.get("analysis_type", "general")
            )

            # Format the results
            research_results = {
                "sources": search_data,
                "analysis": analysis_result.get("analysis", {}),
                "key_findings": analysis_result.get("key_points", [
                    "No key findings available"
                ]),
                "summary": analysis_result.get("summary", "No summary available"),
                "query": query,
                "research_id": search_result.get("research_id"),
                "timestamp": search_result.get("timestamp")
            }
    except Exception as e:
        logger.exception(f"Error in researcher node: {str(e)}")
        state.error = f"Error in researcher node: {str(e)}"
        research_results = {
            "error": str(e),
            "summary": "An error occurred during research"
        }

    # Clean up the agent
    await research_agent._cleanup()

    # Update the state with research results
    state.results["research"] = research_results

    # Update the plan
    plan = state.context.get("plan", {})
    steps = plan.get("steps", [])

    # Mark the current step as completed
    for step in steps:
        if step["id"] == plan.get("current_step"):
            step["status"] = "completed"
            # Find the next step
            for next_step in steps:
                if step["id"] in next_step.get("depends_on", []):
                    plan["current_step"] = next_step["id"]
                    break
            break

    # Set the next agent to be the supervisor
    state.next = "supervisor"

    logger.info(f"Researcher completed. Next agent: {state.next}")
    return state
