"""
Supervisor node implementation.
The supervisor is responsible for monitoring and directing the workflow.
"""

import logging
from typing import Dict, Any, List, Optional

from superagent.graph.types import State
from superagent.utils.time_utils import get_timestamp
from superagent.utils.error_utils import handle_error, ErrorType, is_recoverable_error
from superagent.config import load_config

logger = logging.getLogger(__name__)

async def supervisor_node(state: State) -> State:
    """
    Supervisor node implementation.

    The supervisor is responsible for:
    - Monitoring the workflow progress
    - Deciding which agent to run next
    - Handling errors and exceptions

    Args:
        state: The current workflow state

    Returns:
        Updated workflow state
    """
    logger.info("Supervisor node processing...")

    try:
        # Load configuration
        config = load_config()
        agent_config = config.get_agent_config("supervisor")

        # Record this step in history
        state.history.append({
            "agent": "supervisor",
            "action": "supervise_workflow",
            "timestamp": get_timestamp()
        })

        # Check if there's an error
        if state.error:
            logger.error(f"Error detected: {state.error.get('message', 'Unknown error')}")

            # Check if the error is recoverable
            if is_recoverable_error(state.error):
                logger.info("Attempting to recover from error")
                # Implement recovery logic here
                # For now, we'll just continue with the next agent
                state = _determine_next_agent(state)
            else:
                logger.warning("Non-recoverable error, ending workflow")
                state.next = "__end__"

            return state

        # Determine the next agent
        state = _determine_next_agent(state)

        logger.info(f"Supervisor completed. Next agent: {state.next}")

    except Exception as e:
        logger.error(f"Error in supervisor node: {str(e)}")
        state.error = handle_error(e, context={"agent": "supervisor"}, error_type=ErrorType.EXECUTION_ERROR)
        state.next = "__end__"  # End the workflow if the supervisor itself fails

    return state

def _determine_next_agent(state: State) -> State:
    """
    Determine the next agent to run based on the current state.

    Args:
        state: The current workflow state

    Returns:
        Updated state with next agent set
    """
    # Get the current plan
    plan = state.context.get("plan", {})
    current_step_id = plan.get("current_step")

    if not current_step_id:
        logger.warning("No current step found in plan")
        state.next = "__end__"
        return state

    # Find the current step
    current_step = None
    for step in plan.get("steps", []):
        if step["id"] == current_step_id:
            current_step = step
            break

    if not current_step:
        logger.warning(f"Step {current_step_id} not found in plan")
        state.next = "__end__"
        return state

    # Check if all steps are completed
    all_completed = True
    for step in plan.get("steps", []):
        if step["status"] != "completed":
            all_completed = False
            break

    if all_completed:
        logger.info("All steps completed")
        state.next = "__end__"
        return state

    # Determine the next agent based on the current step
    next_agent = current_step["agent"]
    state.next = next_agent

    return state
