"""
Coder node implementation.
The coder is responsible for implementing solutions and writing code.
"""

from superagent.graph.types import State
from superagent.agents.coder_agent.coder_agent import CoderAgent
from datetime import datetime
import logging
import json

logger = logging.getLogger(__name__)

async def coder_node(state: State) -> State:
    """
    Coder node implementation.

    The coder is responsible for:
    - Implementing solutions based on research
    - Writing and refactoring code
    - Testing implementations

    Args:
        state: The current workflow state

    Returns:
        Updated workflow state
    """
    logger.info("Coder node processing...")

    # Record this step in history with actual timestamp
    state.history.append({
        "agent": "coder",
        "action": "implement_solution",
        "timestamp": datetime.now().isoformat()
    })

    # Get research results and task details
    research_results = state.results.get("research", {})
    task = state.task or {}

    # Extract requirements from the task and research
    requirements = []
    if task.get("description"):
        requirements.append(task.get("description"))

    if research_results.get("key_findings"):
        requirements.extend(research_results.get("key_findings", []))

    # Prepare context for code generation
    context = {
        "research_results": research_results,
        "task_details": task,
        "description": task.get("title", "Generated solution")
    }

    # Initialize the coder agent with configuration from state context
    agent_config = state.context.get("agent_config", {}).get("coder", {})
    coder_agent = CoderAgent(agent_config)

    try:
        # Determine the programming language to use
        language = task.get("language", "python")

        # Call the actual coder agent to generate code
        code_generation_result = await coder_agent.generate_code(
            language=language,
            requirements=requirements,
            context=context
        )

        if code_generation_result.get("status") != "success":
            # Handle error case
            logger.error(f"Code generation failed: {code_generation_result.get('message', 'Unknown error')}")
            state.error = f"Code generation failed: {code_generation_result.get('message', 'Unknown error')}"
            code_results = {
                "error": code_generation_result.get('message', 'Unknown error'),
                "summary": "Failed to generate code based on requirements"
            }
        else:
            # Process successful code generation
            generated_code = code_generation_result.get("code", "")

            # Analyze the generated code
            analysis_result = await coder_agent.analyze_code(
                code=generated_code,
                language=language
            )

            # Format the results
            code_results = {
                "files": [
                    {
                        "name": f"solution.{language}",
                        "content": generated_code,
                        "language": language
                    }
                ],
                "analysis": analysis_result.get("analysis", {}),
                "summary": "Implemented solution based on research findings and requirements"
            }

            # Generate tests if needed
            if task.get("generate_tests", False):
                test_result = await coder_agent.generate_tests(
                    code=generated_code,
                    language=language,
                    context=context
                )

                if test_result.get("status") == "success":
                    code_results["tests"] = [
                        {
                            "name": f"test_solution.{language}",
                            "content": test_result.get("tests", ""),
                            "status": "generated"
                        }
                    ]
    except Exception as e:
        logger.exception(f"Error in coder node: {str(e)}")
        state.error = f"Error in coder node: {str(e)}"
        code_results = {
            "error": str(e),
            "summary": "An error occurred during code generation"
        }

    # Clean up the agent
    await coder_agent._cleanup()

    # Update the state with code results
    state.results["code"] = code_results

    # Update the plan
    plan = state.context.get("plan", {})
    steps = plan.get("steps", [])

    # Mark the current step as completed
    for step in steps:
        if step["id"] == plan.get("current_step"):
            step["status"] = "completed"
            # Find the next step
            for next_step in steps:
                if step["id"] in next_step.get("depends_on", []):
                    plan["current_step"] = next_step["id"]
                    break
            break

    # Set the next agent to be the supervisor
    state.next = "supervisor"

    logger.info(f"Coder completed. Next agent: {state.next}")
    return state
