"""
Reporter node implementation.
The reporter is responsible for generating reports and summaries.
"""

from superagent.graph.types import State
from superagent.agents.reporter_agent.reporter_agent import ReporterAgent
from datetime import datetime
import logging
import json

logger = logging.getLogger(__name__)

async def reporter_node(state: State) -> State:
    """
    Reporter node implementation.

    The reporter is responsible for:
    - Generating reports based on results
    - Summarizing findings
    - Creating documentation

    Args:
        state: The current workflow state

    Returns:
        Updated workflow state
    """
    logger.info("Reporter node processing...")

    # Record this step in history with actual timestamp
    state.history.append({
        "agent": "reporter",
        "action": "generate_report",
        "timestamp": datetime.now().isoformat()
    })

    # Get all results from previous agents
    research_results = state.results.get("research", {})
    code_results = state.results.get("code", {})
    browser_results = state.results.get("browser", {})
    task = state.task or {}

    # Prepare data for report generation
    report_data = {
        "task": task,
        "research": research_results,
        "code": code_results,
        "browser": browser_results,
        "history": state.history,
        "context": state.context
    }

    # Initialize the reporter agent with configuration from state context
    agent_config = state.context.get("agent_config", {}).get("reporter", {})
    reporter_agent = ReporterAgent(agent_config)

    try:
        # Determine the report type and format
        report_type = task.get("report_type", "summary")
        report_format = task.get("format", "markdown")
        report_options = task.get("report_options", {})

        # Call the actual reporter agent to generate the report
        report_generation_result = await reporter_agent.generate_report(
            report_type=report_type,
            data=report_data,
            format=report_format,
            options=report_options
        )

        if report_generation_result.get("status") != "success":
            # Handle error case
            logger.error(f"Report generation failed: {report_generation_result.get('message', 'Unknown error')}")
            state.error = f"Report generation failed: {report_generation_result.get('message', 'Unknown error')}"
            report = {
                "error": report_generation_result.get('message', 'Unknown error'),
                "title": "Error Report",
                "summary": "Failed to generate report",
                "format": report_format
            }
        else:
            # Process successful report generation
            report_content = report_generation_result.get("report", "")

            # Also generate a summary if needed
            summary_result = await reporter_agent.summarize_data(
                data=report_data,
                max_length=500  # Configurable summary length
            )

            # Format the results
            report = {
                "title": f"{task.get('title', 'Task')} Report",
                "content": report_content,
                "summary": summary_result.get("summary", "No summary available"),
                "key_points": summary_result.get("key_points", []),
                "format": report_format,
                "report_id": report_generation_result.get("report_id"),
                "timestamp": report_generation_result.get("timestamp")
            }
    except Exception as e:
        logger.exception(f"Error in reporter node: {str(e)}")
        state.error = f"Error in reporter node: {str(e)}"
        report = {
            "error": str(e),
            "title": "Error Report",
            "summary": "An error occurred during report generation",
            "format": "markdown"
        }

    # Clean up the agent
    await reporter_agent._cleanup()

    # Update the state with the report
    state.results["report"] = report

    # Update the plan
    plan = state.context.get("plan", {})
    steps = plan.get("steps", [])

    # Mark the current step as completed
    for step in steps:
        if step["id"] == plan.get("current_step"):
            step["status"] = "completed"
            break

    # Set the next agent to be the supervisor
    state.next = "supervisor"

    logger.info(f"Reporter completed. Next agent: {state.next}")
    return state
