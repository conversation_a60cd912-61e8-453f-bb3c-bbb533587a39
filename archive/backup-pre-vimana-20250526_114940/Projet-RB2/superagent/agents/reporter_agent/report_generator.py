from typing import Dict, Any, Optional, List
import asyncio
import logging
import json
import os
from datetime import datetime

class ReportGenerator:
    """
    Générateur de rapports qui transforme les données en rapports formatés.
    """
    
    def __init__(self, templates: Dict[str, Any]):
        self.templates = templates
        self.logger = logging.getLogger("agent.reporter.generator")
        
    async def load_templates(self):
        """Charge les templates de rapport depuis les fichiers ou la configuration"""
        self.logger.info("Loading report templates")
        
        # Si les templates sont déjà chargés, ne rien faire
        if self.templates:
            return
            
        # Sinon, charger les templates par défaut
        self.templates = {
            "summary": {
                "markdown": "# Summary Report\n\n{content}\n\n*Generated on {date}*",
                "html": "<h1>Summary Report</h1>\n<div>{content}</div>\n<p><em>Generated on {date}</em></p>",
                "json": {
                    "title": "Summary Report",
                    "content": "{content}",
                    "generated_on": "{date}"
                }
            },
            "detailed": {
                "markdown": "# Detailed Report\n\n## Overview\n{overview}\n\n## Details\n{details}\n\n## Conclusion\n{conclusion}\n\n*Generated on {date}*",
                "html": "<h1>Detailed Report</h1>\n<h2>Overview</h2>\n<div>{overview}</div>\n<h2>Details</h2>\n<div>{details}</div>\n<h2>Conclusion</h2>\n<div>{conclusion}</div>\n<p><em>Generated on {date}</em></p>",
                "json": {
                    "title": "Detailed Report",
                    "overview": "{overview}",
                    "details": "{details}",
                    "conclusion": "{conclusion}",
                    "generated_on": "{date}"
                }
            },
            "analytics": {
                "markdown": "# Analytics Report\n\n## Key Metrics\n{metrics}\n\n## Trends\n{trends}\n\n## Insights\n{insights}\n\n*Generated on {date}*",
                "html": "<h1>Analytics Report</h1>\n<h2>Key Metrics</h2>\n<div>{metrics}</div>\n<h2>Trends</h2>\n<div>{trends}</div>\n<h2>Insights</h2>\n<div>{insights}</div>\n<p><em>Generated on {date}</em></p>",
                "json": {
                    "title": "Analytics Report",
                    "metrics": "{metrics}",
                    "trends": "{trends}",
                    "insights": "{insights}",
                    "generated_on": "{date}"
                }
            }
        }
        
    async def generate(self, report_type: str, data: Dict[str, Any], 
                      format: str, options: Dict[str, Any]) -> str:
        """
        Génère un rapport basé sur le type, les données et le format spécifiés.
        
        Args:
            report_type: Le type de rapport à générer
            data: Les données à inclure dans le rapport
            format: Le format de sortie du rapport
            options: Options supplémentaires pour la génération
            
        Returns:
            Le contenu du rapport généré
        """
        self.logger.info(f"Generating {report_type} report in {format} format")
        
        # Vérifier si le type de rapport est supporté
        if report_type not in self.templates:
            raise ValueError(f"Unsupported report type: {report_type}")
            
        # Vérifier si le format est supporté pour ce type de rapport
        if format not in self.templates[report_type]:
            raise ValueError(f"Unsupported format {format} for report type {report_type}")
            
        # Préparer les données pour le template
        template_data = await self._prepare_template_data(report_type, data, options)
        
        # Appliquer le template
        template = self.templates[report_type][format]
        
        if format == "json":
            # Pour JSON, nous devons d'abord formater chaque champ puis convertir en JSON
            formatted_template = {}
            for key, value in template.items():
                if isinstance(value, str):
                    formatted_template[key] = value.format(**template_data)
                else:
                    formatted_template[key] = value
            return json.dumps(formatted_template, indent=2)
        else:
            # Pour les autres formats, simplement formater le template
            return template.format(**template_data)
            
    async def _prepare_template_data(self, report_type: str, data: Dict[str, Any], 
                                   options: Dict[str, Any]) -> Dict[str, Any]:
        """Prépare les données pour le template"""
        template_data = {
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        if report_type == "summary":
            template_data["content"] = await self._format_summary_content(data, options)
            
        elif report_type == "detailed":
            template_data["overview"] = await self._format_overview(data, options)
            template_data["details"] = await self._format_details(data, options)
            template_data["conclusion"] = await self._format_conclusion(data, options)
            
        elif report_type == "analytics":
            template_data["metrics"] = await self._format_metrics(data, options)
            template_data["trends"] = await self._format_trends(data, options)
            template_data["insights"] = await self._format_insights(data, options)
            
        return template_data
        
    async def _format_summary_content(self, data: Dict[str, Any], options: Dict[str, Any]) -> str:
        """Formate le contenu d'un rapport de résumé"""
        # Implémentation simplifiée - à remplacer par une génération plus sophistiquée
        
        content = ""
        
        # Ajouter une introduction si fournie
        if "introduction" in data:
            content += f"{data['introduction']}\n\n"
            
        # Ajouter les points clés
        if "key_points" in data and isinstance(data["key_points"], list):
            content += "## Key Points\n\n"
            for point in data["key_points"]:
                content += f"- {point}\n"
            content += "\n"
            
        # Ajouter les métriques
        if "metrics" in data and isinstance(data["metrics"], dict):
            content += "## Key Metrics\n\n"
            for key, value in data["metrics"].items():
                content += f"- **{key}**: {value}\n"
            content += "\n"
            
        # Ajouter une conclusion si fournie
        if "conclusion" in data:
            content += f"## Conclusion\n\n{data['conclusion']}\n"
            
        return content
        
    async def _format_overview(self, data: Dict[str, Any], options: Dict[str, Any]) -> str:
        """Formate la section d'aperçu d'un rapport détaillé"""
        # Implémentation simplifiée
        
        overview = ""
        
        if "overview" in data:
            overview = data["overview"]
        elif "summary" in data:
            overview = data["summary"]
        else:
            # Générer un aperçu basique
            overview = "This report provides a detailed analysis of the provided data."
            
            if "period" in data:
                overview += f" The analysis covers the period: {data['period']}."
                
            if "scope" in data:
                overview += f" Scope of analysis: {data['scope']}."
                
        return overview
        
    async def _format_details(self, data: Dict[str, Any], options: Dict[str, Any]) -> str:
        """Formate la section de détails d'un rapport détaillé"""
        # Implémentation simplifiée
        
        details = ""
        
        if "details" in data and isinstance(data["details"], list):
            for i, section in enumerate(data["details"]):
                if isinstance(section, dict) and "title" in section and "content" in section:
                    details += f"### {section['title']}\n\n{section['content']}\n\n"
                else:
                    details += f"### Section {i+1}\n\n{section}\n\n"
        elif "details" in data:
            details = data["details"]
        else:
            details = "No detailed information provided."
            
        return details
        
    async def _format_conclusion(self, data: Dict[str, Any], options: Dict[str, Any]) -> str:
        """Formate la section de conclusion d'un rapport détaillé"""
        # Implémentation simplifiée
        
        if "conclusion" in data:
            return data["conclusion"]
        elif "recommendations" in data:
            conclusion = "Based on the analysis, the following recommendations are provided:\n\n"
            if isinstance(data["recommendations"], list):
                for rec in data["recommendations"]:
                    conclusion += f"- {rec}\n"
            else:
                conclusion += data["recommendations"]
            return conclusion
        else:
            return "No conclusion provided."
            
    async def _format_metrics(self, data: Dict[str, Any], options: Dict[str, Any]) -> str:
        """Formate la section de métriques d'un rapport d'analytique"""
        # Implémentation simplifiée
        
        metrics = ""
        
        if "metrics" in data and isinstance(data["metrics"], dict):
            for category, values in data["metrics"].items():
                metrics += f"### {category}\n\n"
                
                if isinstance(values, dict):
                    for key, value in values.items():
                        metrics += f"- **{key}**: {value}\n"
                elif isinstance(values, list):
                    for item in values:
                        if isinstance(item, dict) and "name" in item and "value" in item:
                            metrics += f"- **{item['name']}**: {item['value']}"
                            if "change" in item:
                                metrics += f" ({item['change']})"
                            metrics += "\n"
                        else:
                            metrics += f"- {item}\n"
                else:
                    metrics += f"{values}\n"
                    
                metrics += "\n"
        elif "metrics" in data:
            metrics = data["metrics"]
        else:
            metrics = "No metrics provided."
            
        return metrics
        
    async def _format_trends(self, data: Dict[str, Any], options: Dict[str, Any]) -> str:
        """Formate la section de tendances d'un rapport d'analytique"""
        # Implémentation simplifiée
        
        trends = ""
        
        if "trends" in data:
            if isinstance(data["trends"], list):
                for trend in data["trends"]:
                    if isinstance(trend, dict) and "title" in trend and "description" in trend:
                        trends += f"### {trend['title']}\n\n{trend['description']}\n\n"
                    else:
                        trends += f"- {trend}\n"
            else:
                trends = data["trends"]
        else:
            trends = "No trend analysis provided."
            
        return trends
        
    async def _format_insights(self, data: Dict[str, Any], options: Dict[str, Any]) -> str:
        """Formate la section d'insights d'un rapport d'analytique"""
        # Implémentation simplifiée
        
        insights = ""
        
        if "insights" in data:
            if isinstance(data["insights"], list):
                for i, insight in enumerate(data["insights"]):
                    if isinstance(insight, dict) and "title" in insight and "description" in insight:
                        insights += f"### {insight['title']}\n\n{insight['description']}\n\n"
                    else:
                        insights += f"### Insight {i+1}\n\n{insight}\n\n"
            else:
                insights = data["insights"]
        else:
            insights = "No insights provided."
            
        return insights
