from typing import Dict, Any, Optional, List
import asyncio
import logging
import json
from datetime import datetime
from agents.base.agent_base import BaseAgent
from .report_generator import ReportGenerator

class ReporterAgent(BaseAgent):
    """
    Agent spécialisé dans la génération de rapports et la synthèse d'informations.
    Capable de collecter, analyser et présenter des données sous différents formats.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("reporter", config)
        self.report_generator = ReportGenerator(config.get("REPORT_TEMPLATES", {}))
        self.supported_formats = config.get("SUPPORTED_FORMATS", ["markdown", "html", "json"])
        self.report_history = []
        
    async def _initialize(self):
        """Initialisation spécifique à l'agent de rapport"""
        self.logger.info("Initializing Reporter Agent")
        # Charger les templates de rapport et les configurations
        await self.report_generator.load_templates()
        
    async def _cleanup(self):
        """Nettoyage des ressources de l'agent"""
        self.logger.info("Cleaning up Reporter Agent resources")
        # Sauvegarder l'historique des rapports si nécessaire
        if self.config.get("SAVE_REPORT_HISTORY", False):
            await self._save_report_history()
        
    async def process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Traite les messages reçus par l'agent"""
        action = message.get("action")
        
        if action == "generate_report":
            return await self.generate_report(
                report_type=message.get("report_type", "summary"),
                data=message.get("data", {}),
                format=message.get("format", "markdown"),
                options=message.get("options", {})
            )
        elif action == "summarize_data":
            return await self.summarize_data(
                data=message.get("data", {}),
                max_length=message.get("max_length", 500)
            )
        elif action == "get_report_history":
            return await self.get_report_history(
                limit=message.get("limit", 10),
                report_type=message.get("report_type")
            )
        else:
            self.logger.warning(f"Unknown action requested: {action}")
            return {"status": "error", "message": f"Unknown action: {action}"}
            
    async def generate_report(self, report_type: str, data: Dict[str, Any], 
                             format: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Génère un rapport basé sur les données fournies"""
        if format not in self.supported_formats:
            return {"status": "error", "message": f"Unsupported format: {format}"}
            
        self.logger.info(f"Generating {report_type} report in {format} format")
        
        try:
            # Générer le rapport
            report_content = await self.report_generator.generate(
                report_type=report_type,
                data=data,
                format=format,
                options=options
            )
            
            # Enregistrer dans l'historique
            report_entry = {
                "id": len(self.report_history) + 1,
                "timestamp": datetime.now().isoformat(),
                "report_type": report_type,
                "format": format,
                "options": options,
                "content_length": len(report_content)
            }
            self.report_history.append(report_entry)
            
            return {
                "status": "success",
                "report": report_content,
                "report_type": report_type,
                "format": format,
                "report_id": report_entry["id"],
                "timestamp": report_entry["timestamp"]
            }
            
        except Exception as e:
            self.logger.error(f"Report generation error: {str(e)}")
            return {"status": "error", "message": f"Failed to generate report: {str(e)}"}
            
    async def summarize_data(self, data: Dict[str, Any], max_length: int) -> Dict[str, Any]:
        """Crée un résumé concis des données fournies"""
        self.logger.info(f"Summarizing data with max length {max_length}")
        
        try:
            # Analyser les données pour extraire les points clés
            key_points = await self._extract_key_points(data)
            
            # Générer le résumé (à implémenter avec le LLM)
            summary = await self._generate_summary(key_points, max_length)
            
            return {
                "status": "success",
                "summary": summary,
                "key_points": key_points,
                "original_data_size": len(json.dumps(data)),
                "summary_size": len(summary)
            }
            
        except Exception as e:
            self.logger.error(f"Data summarization error: {str(e)}")
            return {"status": "error", "message": f"Failed to summarize data: {str(e)}"}
            
    async def get_report_history(self, limit: int, report_type: Optional[str] = None) -> Dict[str, Any]:
        """Récupère l'historique des rapports générés"""
        self.logger.info(f"Retrieving report history (limit: {limit}, type: {report_type})")
        
        try:
            # Filtrer par type si spécifié
            filtered_history = self.report_history
            if report_type:
                filtered_history = [r for r in filtered_history if r["report_type"] == report_type]
                
            # Limiter le nombre de résultats
            limited_history = filtered_history[-limit:] if limit > 0 else filtered_history
            
            return {
                "status": "success",
                "history": limited_history,
                "total_reports": len(filtered_history),
                "returned_reports": len(limited_history)
            }
            
        except Exception as e:
            self.logger.error(f"Report history retrieval error: {str(e)}")
            return {"status": "error", "message": f"Failed to retrieve report history: {str(e)}"}
    
    # Méthodes utilitaires privées
    
    async def _extract_key_points(self, data: Dict[str, Any]) -> List[str]:
        """Extrait les points clés des données"""
        key_points = []
        
        # Implémentation simplifiée - à remplacer par une analyse plus sophistiquée
        
        # Extraire les métriques numériques
        for key, value in data.items():
            if isinstance(value, (int, float)):
                key_points.append(f"{key.replace('_', ' ').title()}: {value}")
                
        # Extraire les listes courtes
        for key, value in data.items():
            if isinstance(value, list) and len(value) <= 5:
                items = ", ".join(str(item) for item in value)
                key_points.append(f"{key.replace('_', ' ').title()}: {items}")
                
        # Extraire les valeurs booléennes importantes
        for key, value in data.items():
            if isinstance(value, bool):
                key_points.append(f"{key.replace('_', ' ').title()}: {'Yes' if value else 'No'}")
                
        return key_points
        
    async def _generate_summary(self, key_points: List[str], max_length: int) -> str:
        """Génère un résumé basé sur les points clés"""
        # Implémentation simplifiée - à remplacer par une génération avec le LLM
        
        # Créer une introduction
        summary = "Summary of the analyzed data:\n\n"
        
        # Ajouter les points clés
        for point in key_points:
            if len(summary) + len(point) + 2 <= max_length:
                summary += f"- {point}\n"
            else:
                summary += "- And more...\n"
                break
                
        return summary
        
    async def _save_report_history(self):
        """Sauvegarde l'historique des rapports"""
        try:
            history_file = self.config.get("REPORT_HISTORY_FILE", "report_history.json")
            with open(history_file, "w") as f:
                json.dump(self.report_history, f, indent=2)
            self.logger.info(f"Report history saved to {history_file}")
        except Exception as e:
            self.logger.error(f"Failed to save report history: {str(e)}")
