from typing import Dict, Any, Optional, List
import asyncio
import logging
import json
import re
from datetime import datetime
from agents.base.agent_base import BaseAgent

class BrowserAgent(BaseAgent):
    """
    Agent spécialisé dans la navigation web et l'extraction d'informations.
    Capable de visiter des sites web, d'extraire des données et d'interagir avec des pages web.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("browser", config)
        self.browsing_history = []
        self.bookmarks = []
        self.current_page = None
        self.extraction_patterns = config.get("EXTRACTION_PATTERNS", {})
        
    async def _initialize(self):
        """Initialisation spécifique à l'agent de navigation"""
        self.logger.info("Initializing Browser Agent")
        # Charger les patterns d'extraction
        if not self.extraction_patterns:
            self.extraction_patterns = await self._load_default_patterns()
        
    async def _cleanup(self):
        """Nettoyage des ressources de l'agent"""
        self.logger.info("Cleaning up Browser Agent resources")
        # Sauvegarder l'historique de navigation si nécessaire
        if self.config.get("SAVE_BROWSING_HISTORY", False):
            await self._save_browsing_history()
        
    async def process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Traite les messages reçus par l'agent"""
        action = message.get("action")
        
        if action == "visit_url":
            return await self.visit_url(
                url=message.get("url", ""),
                options=message.get("options", {})
            )
        elif action == "extract_data":
            return await self.extract_data(
                selectors=message.get("selectors", {}),
                options=message.get("options", {})
            )
        elif action == "search_page":
            return await self.search_page(
                query=message.get("query", ""),
                options=message.get("options", {})
            )
        elif action == "navigate":
            return await self.navigate(
                direction=message.get("direction", ""),
                options=message.get("options", {})
            )
        elif action == "get_browsing_history":
            return await self.get_browsing_history(
                limit=message.get("limit", 10)
            )
        else:
            self.logger.warning(f"Unknown action requested: {action}")
            return {"status": "error", "message": f"Unknown action: {action}"}
            
    async def visit_url(self, url: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Visite une URL spécifique.
        
        Args:
            url: L'URL à visiter
            options: Options supplémentaires pour la visite
            
        Returns:
            Les informations sur la page visitée
        """
        self.logger.info(f"Visiting URL: {url}")
        
        if not url:
            return {"status": "error", "message": "No URL provided"}
            
        try:
            # Simuler la visite d'une page web
            page_content = await self._simulate_page_visit(url, options)
            
            # Créer l'entrée d'historique
            history_entry = {
                "url": url,
                "title": self._extract_title(page_content),
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
            # Ajouter à l'historique
            self.browsing_history.append(history_entry)
            
            # Mettre à jour la page courante
            self.current_page = {
                "url": url,
                "content": page_content,
                "title": history_entry["title"],
                "links": self._extract_links(page_content, url)
            }
            
            return {
                "status": "success",
                "page": {
                    "url": url,
                    "title": history_entry["title"],
                    "content_preview": page_content[:200] + "..." if len(page_content) > 200 else page_content,
                    "link_count": len(self.current_page["links"])
                }
            }
            
        except Exception as e:
            self.logger.error(f"URL visit error: {str(e)}")
            
            # Créer l'entrée d'historique pour l'échec
            history_entry = {
                "url": url,
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }
            
            # Ajouter à l'historique
            self.browsing_history.append(history_entry)
            
            return {"status": "error", "message": f"Failed to visit URL: {str(e)}"}
            
    async def extract_data(self, selectors: Dict[str, Any], options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extrait des données de la page courante selon les sélecteurs spécifiés.
        
        Args:
            selectors: Les sélecteurs pour l'extraction
            options: Options supplémentaires pour l'extraction
            
        Returns:
            Les données extraites
        """
        self.logger.info("Extracting data from current page")
        
        if not self.current_page:
            return {"status": "error", "message": "No current page to extract data from"}
            
        if not selectors:
            return {"status": "error", "message": "No selectors provided for extraction"}
            
        try:
            # Extraire les données selon les sélecteurs
            extracted_data = {}
            
            for key, selector in selectors.items():
                if isinstance(selector, str):
                    # Sélecteur simple (chaîne)
                    extracted_data[key] = self._extract_with_selector(self.current_page["content"], selector)
                elif isinstance(selector, dict) and "pattern" in selector:
                    # Sélecteur avec pattern regex
                    extracted_data[key] = self._extract_with_pattern(
                        self.current_page["content"], 
                        selector["pattern"],
                        selector.get("group", 0),
                        selector.get("all", False)
                    )
                elif isinstance(selector, dict) and "xpath" in selector:
                    # Simuler un sélecteur XPath
                    extracted_data[key] = f"XPath extraction for {selector['xpath']} (simulated)"
                    
            return {
                "status": "success",
                "url": self.current_page["url"],
                "title": self.current_page["title"],
                "extracted_data": extracted_data
            }
            
        except Exception as e:
            self.logger.error(f"Data extraction error: {str(e)}")
            return {"status": "error", "message": f"Failed to extract data: {str(e)}"}
            
    async def search_page(self, query: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recherche une chaîne dans la page courante.
        
        Args:
            query: La chaîne à rechercher
            options: Options supplémentaires pour la recherche
            
        Returns:
            Les résultats de la recherche
        """
        self.logger.info(f"Searching page for: {query}")
        
        if not self.current_page:
            return {"status": "error", "message": "No current page to search"}
            
        if not query:
            return {"status": "error", "message": "No query provided for search"}
            
        try:
            # Options de recherche
            case_sensitive = options.get("case_sensitive", False)
            whole_word = options.get("whole_word", False)
            
            # Préparer la recherche
            content = self.current_page["content"]
            
            if not case_sensitive:
                content_to_search = content.lower()
                search_query = query.lower()
            else:
                content_to_search = content
                search_query = query
                
            # Construire le pattern de recherche
            if whole_word:
                pattern = r'\b' + re.escape(search_query) + r'\b'
            else:
                pattern = re.escape(search_query)
                
            # Rechercher toutes les occurrences
            matches = []
            for match in re.finditer(pattern, content_to_search):
                start, end = match.span()
                
                # Extraire le contexte (50 caractères avant et après)
                context_start = max(0, start - 50)
                context_end = min(len(content), end + 50)
                
                # Extraire le texte correspondant du contenu original
                match_text = content[start:end]
                context = content[context_start:context_end]
                
                matches.append({
                    "text": match_text,
                    "position": start,
                    "context": context
                })
                
            return {
                "status": "success",
                "url": self.current_page["url"],
                "query": query,
                "match_count": len(matches),
                "matches": matches[:10]  # Limiter à 10 résultats
            }
            
        except Exception as e:
            self.logger.error(f"Page search error: {str(e)}")
            return {"status": "error", "message": f"Failed to search page: {str(e)}"}
            
    async def navigate(self, direction: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Navigue dans l'historique ou suit un lien sur la page courante.
        
        Args:
            direction: La direction de navigation ('back', 'forward', 'link')
            options: Options supplémentaires pour la navigation
            
        Returns:
            Les informations sur la nouvelle page
        """
        self.logger.info(f"Navigating: {direction}")
        
        if not direction:
            return {"status": "error", "message": "No direction provided for navigation"}
            
        try:
            if direction == "back":
                # Naviguer en arrière dans l'historique
                if len(self.browsing_history) < 2:
                    return {"status": "error", "message": "No previous page in history"}
                    
                # Récupérer l'URL précédente
                previous_entry = self.browsing_history[-2]
                
                # Visiter l'URL précédente
                return await self.visit_url(previous_entry["url"], options)
                
            elif direction == "forward":
                # Navigation en avant non implémentée (nécessiterait de garder une trace des pages "forward")
                return {"status": "error", "message": "Forward navigation not implemented"}
                
            elif direction == "link":
                # Suivre un lien sur la page courante
                link_text = options.get("link_text")
                link_index = options.get("link_index")
                
                if not self.current_page:
                    return {"status": "error", "message": "No current page to navigate from"}
                    
                if not self.current_page.get("links"):
                    return {"status": "error", "message": "No links found on current page"}
                    
                # Trouver le lien à suivre
                target_link = None
                
                if link_text:
                    # Rechercher par texte
                    for link in self.current_page["links"]:
                        if link_text.lower() in link["text"].lower():
                            target_link = link
                            break
                elif link_index is not None:
                    # Rechercher par index
                    if 0 <= link_index < len(self.current_page["links"]):
                        target_link = self.current_page["links"][link_index]
                        
                if not target_link:
                    return {"status": "error", "message": "Link not found"}
                    
                # Visiter l'URL du lien
                return await self.visit_url(target_link["url"], options)
                
            else:
                return {"status": "error", "message": f"Unknown navigation direction: {direction}"}
                
        except Exception as e:
            self.logger.error(f"Navigation error: {str(e)}")
            return {"status": "error", "message": f"Failed to navigate: {str(e)}"}
            
    async def get_browsing_history(self, limit: int) -> Dict[str, Any]:
        """
        Récupère l'historique de navigation.
        
        Args:
            limit: Nombre maximum d'entrées à récupérer
            
        Returns:
            L'historique de navigation
        """
        self.logger.info(f"Getting browsing history (limit: {limit})")
        
        try:
            # Limiter le nombre d'entrées
            limited_history = self.browsing_history[-limit:] if limit > 0 else self.browsing_history
            
            return {
                "status": "success",
                "history": limited_history,
                "total_entries": len(self.browsing_history),
                "returned_entries": len(limited_history)
            }
            
        except Exception as e:
            self.logger.error(f"History retrieval error: {str(e)}")
            return {"status": "error", "message": f"Failed to retrieve browsing history: {str(e)}"}
    
    # Méthodes utilitaires privées
    
    async def _load_default_patterns(self) -> Dict[str, Any]:
        """Charge les patterns d'extraction par défaut"""
        return {
            "title": r"<title>(.*?)</title>",
            "heading": r"<h1>(.*?)</h1>",
            "paragraph": r"<p>(.*?)</p>",
            "link": r"<a\s+(?:[^>]*?\s+)?href=\"([^\"]*)\"[^>]*>(.*?)</a>",
            "image": r"<img\s+(?:[^>]*?\s+)?src=\"([^\"]*)\"[^>]*>",
            "list_item": r"<li>(.*?)</li>",
            "table": r"<table>(.*?)</table>",
            "meta_description": r"<meta\s+(?:[^>]*?\s+)?name=\"description\"\s+(?:[^>]*?\s+)?content=\"([^\"]*)\"[^>]*>",
            "meta_keywords": r"<meta\s+(?:[^>]*?\s+)?name=\"keywords\"\s+(?:[^>]*?\s+)?content=\"([^\"]*)\"[^>]*>"
        }
        
    async def _simulate_page_visit(self, url: str, options: Dict[str, Any]) -> str:
        """Simule la visite d'une page web"""
        # Dans une implémentation réelle, nous utiliserions un navigateur headless
        # Pour l'instant, nous simulons le contenu de la page
        
        # Simuler un délai de chargement
        await asyncio.sleep(0.5)
        
        # Générer un contenu simulé basé sur l'URL
        domain = url.split("//")[-1].split("/")[0]
        path = "/" + "/".join(url.split("//")[-1].split("/")[1:]) if "/" in url.split("//")[-1] else "/"
        
        # Titre de la page
        title = f"Page title for {domain}{path}"
        
        # Corps de la page
        content = f"""<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
    <meta name="description" content="This is a simulated page for {domain}{path}">
    <meta name="keywords" content="simulation, browser agent, {domain}">
</head>
<body>
    <h1>{title}</h1>
    <p>This is a simulated page content for the URL: {url}</p>
    <p>The browser agent is simulating the visit to this page.</p>
    
    <h2>Sample Content</h2>
    <p>Here is some sample content that might be found on a page like this.</p>
    <ul>
        <li>Sample item 1</li>
        <li>Sample item 2</li>
        <li>Sample item 3</li>
    </ul>
    
    <h2>Links</h2>
    <p>Here are some simulated links:</p>
    <ul>
        <li><a href="{url}/page1">Page 1</a></li>
        <li><a href="{url}/page2">Page 2</a></li>
        <li><a href="https://example.com">External Link</a></li>
    </ul>
    
    <h2>Images</h2>
    <p>Here are some simulated images:</p>
    <img src="{url}/image1.jpg" alt="Image 1">
    <img src="{url}/image2.jpg" alt="Image 2">
    
    <h2>Table</h2>
    <table>
        <tr>
            <th>Header 1</th>
            <th>Header 2</th>
        </tr>
        <tr>
            <td>Cell 1,1</td>
            <td>Cell 1,2</td>
        </tr>
        <tr>
            <td>Cell 2,1</td>
            <td>Cell 2,2</td>
        </tr>
    </table>
</body>
</html>"""
        
        return content
        
    def _extract_title(self, content: str) -> str:
        """Extrait le titre d'une page HTML"""
        match = re.search(r"<title>(.*?)</title>", content, re.IGNORECASE | re.DOTALL)
        return match.group(1) if match else "Untitled Page"
        
    def _extract_links(self, content: str, base_url: str) -> List[Dict[str, Any]]:
        """Extrait les liens d'une page HTML"""
        links = []
        
        # Rechercher tous les liens
        for match in re.finditer(r"<a\s+(?:[^>]*?\s+)?href=\"([^\"]*)\"[^>]*>(.*?)</a>", content, re.IGNORECASE | re.DOTALL):
            href = match.group(1)
            text = match.group(2)
            
            # Nettoyer le texte (supprimer les balises HTML)
            text = re.sub(r"<[^>]*>", "", text).strip()
            
            # Résoudre les URLs relatives
            if href.startswith("http"):
                url = href
            elif href.startswith("/"):
                # URL absolue par rapport au domaine
                domain = "/".join(base_url.split("/")[:3])  # http(s)://domain.com
                url = domain + href
            else:
                # URL relative
                base_path = "/".join(base_url.split("/")[:-1]) + "/"
                url = base_path + href
                
            links.append({
                "url": url,
                "text": text
            })
            
        return links
        
    def _extract_with_selector(self, content: str, selector: str) -> Any:
        """Extrait des données avec un sélecteur simple"""
        # Dans une implémentation réelle, nous utiliserions un parser HTML
        # Pour l'instant, nous simulons l'extraction
        
        if selector == "title":
            return self._extract_title(content)
        elif selector == "links":
            return [link["text"] for link in self._extract_links(content, "")]
        elif selector == "headings":
            # Extraire tous les titres h1-h6
            headings = []
            for i in range(1, 7):
                pattern = f"<h{i}>(.*?)</h{i}>"
                for match in re.finditer(pattern, content, re.IGNORECASE | re.DOTALL):
                    headings.append(match.group(1))
            return headings
        elif selector == "paragraphs":
            # Extraire tous les paragraphes
            paragraphs = []
            for match in re.finditer(r"<p>(.*?)</p>", content, re.IGNORECASE | re.DOTALL):
                paragraphs.append(match.group(1))
            return paragraphs
        else:
            # Sélecteur non reconnu
            return f"Extraction with selector '{selector}' not implemented"
            
    def _extract_with_pattern(self, content: str, pattern: str, group: int, all_matches: bool) -> Any:
        """Extrait des données avec un pattern regex"""
        if all_matches:
            # Extraire toutes les occurrences
            matches = []
            for match in re.finditer(pattern, content, re.IGNORECASE | re.DOTALL):
                if group == 0:
                    matches.append(match.group(0))
                else:
                    try:
                        matches.append(match.group(group))
                    except IndexError:
                        matches.append(None)
            return matches
        else:
            # Extraire la première occurrence
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                if group == 0:
                    return match.group(0)
                else:
                    try:
                        return match.group(group)
                    except IndexError:
                        return None
            else:
                return None
                
    async def _save_browsing_history(self):
        """Sauvegarde l'historique de navigation"""
        try:
            history_file = self.config.get("BROWSING_HISTORY_FILE", "browsing_history.json")
            with open(history_file, "w") as f:
                json.dump(self.browsing_history, f, indent=2)
            self.logger.info(f"Browsing history saved to {history_file}")
        except Exception as e:
            self.logger.error(f"Failed to save browsing history: {str(e)}")
