"""
Workflow engine implementation for the multi-agent system.
This module provides the core functionality for executing workflows.
"""

from typing import Dict, Any, Optional, List, Callable, Union
import logging
import asyncio
from datetime import datetime
import json

from .types import State
from .workflow import build_graph, run_workflow
from .validators.workflow_validator import WorkflowValidator
from .validators.graph_validator import GraphValidator

logger = logging.getLogger("graph.workflow_engine")

class WorkflowEngine:
    """
    Moteur d'exécution de workflows pour le système multi-agent.
    Gère la création, l'exécution et la surveillance des workflows.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialise le moteur de workflow.
        
        Args:
            config: Configuration du moteur de workflow
        """
        self.config = config or {}
        self.active_workflows: Dict[str, Dict[str, Any]] = {}
        self.workflow_history: List[Dict[str, Any]] = []
        self.workflow_validator = WorkflowValidator()
        self.graph_validator = GraphValidator()
        
    async def create_workflow(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """
        Crée un nouveau workflow.
        
        Args:
            task: La tâche à exécuter
            context: Le contexte d'exécution
            
        Returns:
            L'identifiant du workflow créé
        """
        logger.info(f"Creating workflow for task: {task.get('title', 'Untitled')}")
        
        # Valider la tâche
        task_validation = WorkflowValidator.validate_task(task)
        if not task_validation["is_valid"]:
            logger.error(f"Invalid task: {task_validation['errors']}")
            raise ValueError(f"Invalid task: {task_validation['errors']}")
            
        # Générer un identifiant unique pour le workflow
        workflow_id = f"workflow_{len(self.workflow_history) + 1}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Créer l'état initial
        initial_state = State(
            next="coordinator",
            task=task,
            context=context or {},
            results={},
            history=[],
            error=None
        )
        
        # Valider l'état initial
        state_validation = WorkflowValidator.validate_state(initial_state)
        if not state_validation["is_valid"]:
            logger.error(f"Invalid initial state: {state_validation['errors']}")
            raise ValueError(f"Invalid initial state: {state_validation['errors']}")
            
        # Enregistrer le workflow
        self.active_workflows[workflow_id] = {
            "id": workflow_id,
            "state": initial_state,
            "status": "created",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        logger.info(f"Workflow created with ID: {workflow_id}")
        return workflow_id
        
    async def execute_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Exécute un workflow.
        
        Args:
            workflow_id: L'identifiant du workflow à exécuter
            
        Returns:
            Les résultats de l'exécution du workflow
        """
        logger.info(f"Executing workflow: {workflow_id}")
        
        # Vérifier que le workflow existe
        if workflow_id not in self.active_workflows:
            logger.error(f"Workflow not found: {workflow_id}")
            raise ValueError(f"Workflow not found: {workflow_id}")
            
        # Récupérer le workflow
        workflow = self.active_workflows[workflow_id]
        
        # Vérifier que le workflow n'est pas déjà terminé
        if workflow["status"] in ["completed", "failed"]:
            logger.warning(f"Workflow already {workflow['status']}: {workflow_id}")
            return workflow
            
        # Mettre à jour le statut du workflow
        workflow["status"] = "running"
        workflow["updated_at"] = datetime.now().isoformat()
        
        try:
            # Exécuter le workflow
            logger.info(f"Running workflow graph for: {workflow_id}")
            result = await run_workflow(workflow["state"])
            
            # Mettre à jour l'état du workflow
            workflow["state"] = result
            workflow["status"] = "completed" if not result.error else "failed"
            workflow["updated_at"] = datetime.now().isoformat()
            workflow["completed_at"] = datetime.now().isoformat()
            
            # Ajouter le workflow à l'historique
            self.workflow_history.append(workflow.copy())
            
            # Si le workflow est terminé, le supprimer des workflows actifs
            if workflow["status"] in ["completed", "failed"]:
                self.active_workflows.pop(workflow_id, None)
                
            logger.info(f"Workflow {workflow_id} {workflow['status']}")
            return workflow
            
        except Exception as e:
            logger.exception(f"Error executing workflow {workflow_id}: {str(e)}")
            
            # Mettre à jour le statut du workflow
            workflow["status"] = "failed"
            workflow["error"] = str(e)
            workflow["updated_at"] = datetime.now().isoformat()
            
            # Ajouter le workflow à l'historique
            self.workflow_history.append(workflow.copy())
            
            # Supprimer le workflow des workflows actifs
            self.active_workflows.pop(workflow_id, None)
            
            return workflow
            
    async def get_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Récupère un workflow.
        
        Args:
            workflow_id: L'identifiant du workflow à récupérer
            
        Returns:
            Le workflow
        """
        logger.info(f"Getting workflow: {workflow_id}")
        
        # Vérifier dans les workflows actifs
        if workflow_id in self.active_workflows:
            return self.active_workflows[workflow_id]
            
        # Vérifier dans l'historique des workflows
        for workflow in self.workflow_history:
            if workflow["id"] == workflow_id:
                return workflow
                
        logger.error(f"Workflow not found: {workflow_id}")
        raise ValueError(f"Workflow not found: {workflow_id}")
        
    async def list_workflows(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Liste les workflows.
        
        Args:
            status: Filtre optionnel sur le statut des workflows
            
        Returns:
            La liste des workflows
        """
        logger.info(f"Listing workflows with status: {status or 'all'}")
        
        # Combiner les workflows actifs et l'historique
        all_workflows = list(self.active_workflows.values()) + self.workflow_history
        
        # Filtrer par statut si demandé
        if status:
            all_workflows = [w for w in all_workflows if w["status"] == status]
            
        return all_workflows
        
    async def cancel_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Annule un workflow.
        
        Args:
            workflow_id: L'identifiant du workflow à annuler
            
        Returns:
            Le workflow annulé
        """
        logger.info(f"Cancelling workflow: {workflow_id}")
        
        # Vérifier que le workflow existe
        if workflow_id not in self.active_workflows:
            logger.error(f"Workflow not found: {workflow_id}")
            raise ValueError(f"Workflow not found: {workflow_id}")
            
        # Récupérer le workflow
        workflow = self.active_workflows[workflow_id]
        
        # Vérifier que le workflow n'est pas déjà terminé
        if workflow["status"] in ["completed", "failed", "cancelled"]:
            logger.warning(f"Workflow already {workflow['status']}: {workflow_id}")
            return workflow
            
        # Mettre à jour le statut du workflow
        workflow["status"] = "cancelled"
        workflow["updated_at"] = datetime.now().isoformat()
        
        # Ajouter le workflow à l'historique
        self.workflow_history.append(workflow.copy())
        
        # Supprimer le workflow des workflows actifs
        self.active_workflows.pop(workflow_id, None)
        
        logger.info(f"Workflow {workflow_id} cancelled")
        return workflow
        
    async def pause_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Met en pause un workflow.
        
        Args:
            workflow_id: L'identifiant du workflow à mettre en pause
            
        Returns:
            Le workflow mis en pause
        """
        logger.info(f"Pausing workflow: {workflow_id}")
        
        # Vérifier que le workflow existe
        if workflow_id not in self.active_workflows:
            logger.error(f"Workflow not found: {workflow_id}")
            raise ValueError(f"Workflow not found: {workflow_id}")
            
        # Récupérer le workflow
        workflow = self.active_workflows[workflow_id]
        
        # Vérifier que le workflow est en cours d'exécution
        if workflow["status"] != "running":
            logger.warning(f"Cannot pause workflow with status {workflow['status']}: {workflow_id}")
            return workflow
            
        # Mettre à jour le statut du workflow
        workflow["status"] = "paused"
        workflow["updated_at"] = datetime.now().isoformat()
        
        logger.info(f"Workflow {workflow_id} paused")
        return workflow
        
    async def resume_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Reprend un workflow mis en pause.
        
        Args:
            workflow_id: L'identifiant du workflow à reprendre
            
        Returns:
            Le workflow repris
        """
        logger.info(f"Resuming workflow: {workflow_id}")
        
        # Vérifier que le workflow existe
        if workflow_id not in self.active_workflows:
            logger.error(f"Workflow not found: {workflow_id}")
            raise ValueError(f"Workflow not found: {workflow_id}")
            
        # Récupérer le workflow
        workflow = self.active_workflows[workflow_id]
        
        # Vérifier que le workflow est en pause
        if workflow["status"] != "paused":
            logger.warning(f"Cannot resume workflow with status {workflow['status']}: {workflow_id}")
            return workflow
            
        # Mettre à jour le statut du workflow
        workflow["status"] = "running"
        workflow["updated_at"] = datetime.now().isoformat()
        
        # Exécuter le workflow
        return await self.execute_workflow(workflow_id)
        
    async def get_workflow_results(self, workflow_id: str) -> Dict[str, Any]:
        """
        Récupère les résultats d'un workflow.
        
        Args:
            workflow_id: L'identifiant du workflow
            
        Returns:
            Les résultats du workflow
        """
        logger.info(f"Getting results for workflow: {workflow_id}")
        
        # Récupérer le workflow
        workflow = await self.get_workflow(workflow_id)
        
        # Vérifier que le workflow est terminé
        if workflow["status"] not in ["completed", "failed"]:
            logger.warning(f"Workflow not completed: {workflow_id}")
            return {"status": workflow["status"], "results": None}
            
        # Récupérer les résultats
        return {
            "status": workflow["status"],
            "results": workflow["state"].results,
            "error": workflow["state"].error
        }
        
    async def add_feedback(self, workflow_id: str, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ajoute un feedback à un workflow.
        
        Args:
            workflow_id: L'identifiant du workflow
            feedback: Le feedback à ajouter
            
        Returns:
            Le workflow mis à jour
        """
        logger.info(f"Adding feedback to workflow: {workflow_id}")
        
        # Récupérer le workflow
        try:
            workflow = await self.get_workflow(workflow_id)
        except ValueError:
            logger.error(f"Workflow not found: {workflow_id}")
            raise ValueError(f"Workflow not found: {workflow_id}")
            
        # Ajouter le feedback
        if "feedback" not in workflow:
            workflow["feedback"] = []
            
        feedback_entry = {
            "timestamp": datetime.now().isoformat(),
            "content": feedback
        }
        
        workflow["feedback"].append(feedback_entry)
        workflow["updated_at"] = datetime.now().isoformat()
        
        logger.info(f"Feedback added to workflow: {workflow_id}")
        return workflow
