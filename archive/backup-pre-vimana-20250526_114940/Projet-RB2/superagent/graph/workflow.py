"""
Workflow graph implementation using LangGraph.
This module defines the agent workflow graph structure.
"""

from langgraph.graph import StateGraph, START
from superagent.graph.types import State
from superagent.agents.nodes import (
    coordinator_node,
    planner_node,
    supervisor_node,
    researcher_node,
    coder_node,
    browser_node,
    reporter_node,
)

def build_graph():
    """
    Build and return the agent workflow graph.
    
    The graph defines the flow between different agent nodes and the conditions
    for transitioning between them.
    
    Returns:
        A compiled StateGraph that can be executed
    """
    builder = StateGraph(State)

    # Define nodes
    builder.add_node("coordinator", coordinator_node)
    builder.add_node("planner", planner_node)
    builder.add_node("supervisor", supervisor_node)
    builder.add_node("researcher", researcher_node)
    builder.add_node("coder", coder_node)
    builder.add_node("browser", browser_node)
    builder.add_node("reporter", reporter_node)

    # Define edges
    builder.add_edge(START, "coordinator")
    builder.add_edge("coordinator", "planner")  # Coordinator -> Planner
    builder.add_edge("planner", "supervisor")  # Planner -> Supervisor
    
    # Conditional edges from supervisor to other agents
    builder.add_edge(
        "supervisor", 
        "researcher", 
        condition=lambda state: state['next'] == "researcher"
    )
    builder.add_edge(
        "supervisor", 
        "coder", 
        condition=lambda state: state['next'] == "coder"
    )
    builder.add_edge(
        "supervisor", 
        "browser", 
        condition=lambda state: state['next'] == "browser"
    )
    builder.add_edge(
        "supervisor", 
        "reporter", 
        condition=lambda state: state['next'] == "reporter"
    )
    builder.add_edge(
        "supervisor", 
        "__end__", 
        condition=lambda state: state['next'] == "__end__"
    )
    
    # All agents return to supervisor after completion
    builder.add_edge("researcher", "supervisor")
    builder.add_edge("coder", "supervisor")
    builder.add_edge("browser", "supervisor")
    builder.add_edge("reporter", "supervisor")

    # Set entry point and end point
    builder.set_entry_point("coordinator")
    builder.add_end_point("__end__")

    return builder.compile()

async def run_workflow(initial_state=None):
    """
    Run the workflow with an optional initial state.
    
    Args:
        initial_state: Optional initial state for the workflow
        
    Returns:
        The final state after workflow completion
    """
    graph = build_graph()
    
    # Create initial state if not provided
    if initial_state is None:
        initial_state = State()
    
    # Run the workflow
    result = await graph.arun(initial_state)
    
    return result
