"""
Workflow spécifique pour l'assistance client.
Ce module définit un workflow adapté à l'assistance des clients pour la recherche et la réservation de retraites.
"""

from langgraph.graph import StateGraph, START
from superagent.graph.types import State
from superagent.agents.nodes import (
    coordinator_node,
    supervisor_node,
    client_assistant_node,
    retreat_planner_node,
    reporter_node
)

def build_client_workflow():
    """
    Construire et retourner un workflow pour l'assistance client.
    
    Ce workflow est spécialisé pour aider les clients à trouver des retraites adaptées à leurs besoins
    et à répondre à leurs questions.
    
    Returns:
        Un StateGraph compilé qui peut être exécuté
    """
    builder = StateGraph(State)

    # Définir les nœuds
    builder.add_node("coordinator", coordinator_node)
    builder.add_node("supervisor", supervisor_node)
    builder.add_node("client_assistant", client_assistant_node)
    builder.add_node("retreat_planner", retreat_planner_node)
    builder.add_node("reporter", reporter_node)

    # Définir les arêtes
    builder.add_edge(START, "coordinator")
    builder.add_edge("coordinator", "client_assistant")  # Coordinator -> Client Assistant
    builder.add_edge("client_assistant", "supervisor")   # Client Assistant -> Supervisor
    
    # Arêtes conditionnelles depuis le superviseur vers d'autres agents
    builder.add_edge(
        "supervisor", 
        "client_assistant", 
        condition=lambda state: state['next'] == "client_assistant"
    )
    builder.add_edge(
        "supervisor", 
        "retreat_planner", 
        condition=lambda state: state['next'] == "retreat_planner"
    )
    builder.add_edge(
        "supervisor", 
        "reporter", 
        condition=lambda state: state['next'] == "reporter"
    )
    builder.add_edge(
        "supervisor", 
        "__end__", 
        condition=lambda state: state['next'] == "__end__"
    )
    
    # Tous les agents retournent au superviseur après leur exécution
    builder.add_edge("retreat_planner", "supervisor")
    builder.add_edge("reporter", "supervisor")

    # Définir le point d'entrée et le point de sortie
    builder.set_entry_point("coordinator")
    builder.add_end_point("__end__")

    return builder.compile()

async def run_client_workflow(initial_state=None):
    """
    Exécuter le workflow d'assistance client avec un état initial optionnel.
    
    Args:
        initial_state: État initial optionnel pour le workflow
        
    Returns:
        L'état final après l'exécution du workflow
    """
    graph = build_client_workflow()
    
    # Créer un état initial si non fourni
    if initial_state is None:
        initial_state = State()
    
    # Exécuter le workflow
    result = await graph.arun(initial_state)
    
    return result
