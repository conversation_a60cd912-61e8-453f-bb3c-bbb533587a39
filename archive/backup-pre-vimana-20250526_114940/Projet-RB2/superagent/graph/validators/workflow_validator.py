from typing import Dict, Any, List, Optional, Set
import logging
from ..types import State

logger = logging.getLogger("graph.workflow.validator")

class WorkflowValidator:
    """
    Validateur pour les workflows.
    Vérifie que les états de workflow sont valides et respectent les contraintes définies.
    """
    
    @staticmethod
    def validate_state(state: State) -> Dict[str, Any]:
        """
        Valide un état de workflow.
        
        Args:
            state: L'état de workflow à valider
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        logger.info("Validating workflow state")
        
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Vérifier que le prochain agent est défini
        if not state.next:
            result["is_valid"] = False
            result["errors"].append("Next agent is not defined")
            
        # Vérifier que l'historique existe
        if state.history is None:
            result["is_valid"] = False
            result["errors"].append("History is not defined")
            
        # Vérifier que les résultats existent
        if state.results is None:
            result["is_valid"] = False
            result["errors"].append("Results are not defined")
            
        # Vérifier que le contexte existe
        if state.context is None:
            result["warnings"].append("Context is not defined")
            
        # Vérifier la cohérence de l'historique
        if state.history and len(state.history) > 0:
            # Vérifier que chaque entrée d'historique a un agent et une action
            for i, entry in enumerate(state.history):
                if not isinstance(entry, dict):
                    result["is_valid"] = False
                    result["errors"].append(f"History entry {i} is not a dictionary")
                    continue
                    
                if "agent" not in entry:
                    result["is_valid"] = False
                    result["errors"].append(f"History entry {i} does not have an agent")
                    
                if "action" not in entry:
                    result["is_valid"] = False
                    result["errors"].append(f"History entry {i} does not have an action")
                    
                if "timestamp" not in entry:
                    result["warnings"].append(f"History entry {i} does not have a timestamp")
        
        return result
    
    @staticmethod
    def validate_task(task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Valide une tâche de workflow.
        
        Args:
            task: La tâche à valider
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        logger.info("Validating workflow task")
        
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Vérifier que la tâche est un dictionnaire
        if not isinstance(task, dict):
            result["is_valid"] = False
            result["errors"].append("Task is not a dictionary")
            return result
            
        # Vérifier que la tâche a un titre
        if "title" not in task:
            result["warnings"].append("Task does not have a title")
            
        # Vérifier que la tâche a une description
        if "description" not in task:
            result["warnings"].append("Task does not have a description")
            
        # Vérifier que la tâche a un type
        if "type" not in task:
            result["warnings"].append("Task does not have a type")
            
        return result
    
    @staticmethod
    def validate_plan(plan: Dict[str, Any]) -> Dict[str, Any]:
        """
        Valide un plan de workflow.
        
        Args:
            plan: Le plan à valider
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        logger.info("Validating workflow plan")
        
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Vérifier que le plan est un dictionnaire
        if not isinstance(plan, dict):
            result["is_valid"] = False
            result["errors"].append("Plan is not a dictionary")
            return result
            
        # Vérifier que le plan a des étapes
        if "steps" not in plan or not isinstance(plan["steps"], list):
            result["is_valid"] = False
            result["errors"].append("Plan does not have steps or steps is not a list")
            return result
            
        # Vérifier que chaque étape a un ID
        for i, step in enumerate(plan["steps"]):
            if not isinstance(step, dict):
                result["is_valid"] = False
                result["errors"].append(f"Step {i} is not a dictionary")
                continue
                
            if "id" not in step:
                result["is_valid"] = False
                result["errors"].append(f"Step {i} does not have an ID")
                
            if "description" not in step:
                result["warnings"].append(f"Step {i} does not have a description")
                
            if "status" not in step:
                result["warnings"].append(f"Step {i} does not have a status")
                
        # Vérifier que l'étape courante existe
        if "current_step" in plan:
            current_step = plan["current_step"]
            step_ids = [step.get("id") for step in plan["steps"] if isinstance(step, dict) and "id" in step]
            
            if current_step not in step_ids:
                result["is_valid"] = False
                result["errors"].append(f"Current step '{current_step}' does not exist in the plan")
                
        return result
    
    @staticmethod
    def validate_results(results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Valide les résultats d'un workflow.
        
        Args:
            results: Les résultats à valider
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        logger.info("Validating workflow results")
        
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Vérifier que les résultats sont un dictionnaire
        if not isinstance(results, dict):
            result["is_valid"] = False
            result["errors"].append("Results is not a dictionary")
            return result
            
        # Vérifier les types de résultats courants
        if "research" in results and not isinstance(results["research"], dict):
            result["warnings"].append("Research results is not a dictionary")
            
        if "code" in results and not isinstance(results["code"], dict):
            result["warnings"].append("Code results is not a dictionary")
            
        if "browser" in results and not isinstance(results["browser"], dict):
            result["warnings"].append("Browser results is not a dictionary")
            
        if "report" in results and not isinstance(results["report"], dict):
            result["warnings"].append("Report results is not a dictionary")
            
        return result
