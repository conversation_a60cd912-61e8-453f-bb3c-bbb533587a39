from typing import Dict, Any, List, Optional, Set
import logging
from ..node_types import GraphConfig, NodeConfig, EdgeConfig, NodeType

logger = logging.getLogger("graph.validator")

class GraphValidator:
    """
    Validateur pour les graphes de workflow.
    Vérifie que les graphes sont bien formés et respectent les contraintes définies.
    """
    
    @staticmethod
    def validate_graph(graph_config: GraphConfig) -> Dict[str, Any]:
        """
        Valide un graphe de workflow complet.
        
        Args:
            graph_config: Configuration du graphe à valider
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        logger.info(f"Validating graph: {graph_config.name}")
        
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Vérifier que le point d'entrée existe
        entry_point = graph_config.entry_point
        node_names = [node.name for node in graph_config.nodes]
        
        if entry_point not in node_names:
            result["is_valid"] = False
            result["errors"].append(f"Entry point '{entry_point}' does not exist in the graph nodes")
            
        # Vérifier que tous les nœuds ont un type valide
        for node in graph_config.nodes:
            if not isinstance(node.type, NodeType):
                result["is_valid"] = False
                result["errors"].append(f"Node '{node.name}' has invalid type: {node.type}")
                
        # Vérifier que les arêtes référencent des nœuds existants
        for edge in graph_config.edges:
            if edge.source not in node_names:
                result["is_valid"] = False
                result["errors"].append(f"Edge source '{edge.source}' does not exist in the graph nodes")
                
            if edge.target not in node_names:
                result["is_valid"] = False
                result["errors"].append(f"Edge target '{edge.target}' does not exist in the graph nodes")
                
        # Vérifier l'accessibilité des nœuds depuis le point d'entrée
        reachable_nodes = GraphValidator._find_reachable_nodes(graph_config)
        unreachable_nodes = set(node_names) - reachable_nodes
        
        if unreachable_nodes:
            result["warnings"].append(f"Unreachable nodes: {', '.join(unreachable_nodes)}")
            
        # Vérifier les cycles dans le graphe
        cycles = GraphValidator._find_cycles(graph_config)
        if cycles:
            result["warnings"].append(f"Graph contains cycles: {cycles}")
            
        return result
        
    @staticmethod
    def validate_node(node_config: NodeConfig) -> Dict[str, Any]:
        """
        Valide la configuration d'un nœud.
        
        Args:
            node_config: Configuration du nœud à valider
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        logger.info(f"Validating node: {node_config.name}")
        
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Vérifier que le type est valide
        if not isinstance(node_config.type, NodeType):
            result["is_valid"] = False
            result["errors"].append(f"Invalid node type: {node_config.type}")
            
        # Vérifier que le nom n'est pas vide
        if not node_config.name:
            result["is_valid"] = False
            result["errors"].append("Node name cannot be empty")
            
        # Vérifier les schémas d'entrée/sortie si spécifiés
        if node_config.input_schema and not isinstance(node_config.input_schema, dict):
            result["is_valid"] = False
            result["errors"].append("Input schema must be a dictionary")
            
        if node_config.output_schema and not isinstance(node_config.output_schema, dict):
            result["is_valid"] = False
            result["errors"].append("Output schema must be a dictionary")
            
        return result
        
    @staticmethod
    def validate_edge(edge_config: EdgeConfig, available_nodes: List[str]) -> Dict[str, Any]:
        """
        Valide la configuration d'une arête.
        
        Args:
            edge_config: Configuration de l'arête à valider
            available_nodes: Liste des noms de nœuds disponibles
            
        Returns:
            Un dictionnaire contenant les résultats de validation
        """
        logger.info(f"Validating edge: {edge_config.source} -> {edge_config.target}")
        
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Vérifier que les nœuds source et cible existent
        if edge_config.source not in available_nodes:
            result["is_valid"] = False
            result["errors"].append(f"Source node '{edge_config.source}' does not exist")
            
        if edge_config.target not in available_nodes:
            result["is_valid"] = False
            result["errors"].append(f"Target node '{edge_config.target}' does not exist")
            
        # Vérifier que la source et la cible ne sont pas identiques (boucle)
        if edge_config.source == edge_config.target:
            result["warnings"].append(f"Self-loop detected on node '{edge_config.source}'")
            
        return result
    
    @staticmethod
    def _find_reachable_nodes(graph_config: GraphConfig) -> Set[str]:
        """
        Trouve tous les nœuds accessibles depuis le point d'entrée.
        
        Args:
            graph_config: Configuration du graphe
            
        Returns:
            Un ensemble des noms de nœuds accessibles
        """
        # Construire un dictionnaire des arêtes sortantes pour chaque nœud
        outgoing_edges = {}
        for node in graph_config.nodes:
            outgoing_edges[node.name] = []
            
        for edge in graph_config.edges:
            outgoing_edges[edge.source].append(edge.target)
            
        # Effectuer un parcours en profondeur depuis le point d'entrée
        visited = set()
        stack = [graph_config.entry_point]
        
        while stack:
            node = stack.pop()
            if node not in visited:
                visited.add(node)
                stack.extend([target for target in outgoing_edges.get(node, []) if target not in visited])
                
        return visited
        
    @staticmethod
    def _find_cycles(graph_config: GraphConfig) -> List[List[str]]:
        """
        Trouve tous les cycles dans le graphe.
        
        Args:
            graph_config: Configuration du graphe
            
        Returns:
            Une liste des cycles trouvés, chaque cycle étant une liste de noms de nœuds
        """
        # Construire un dictionnaire des arêtes sortantes pour chaque nœud
        outgoing_edges = {}
        for node in graph_config.nodes:
            outgoing_edges[node.name] = []
            
        for edge in graph_config.edges:
            outgoing_edges[edge.source].append(edge.target)
            
        # Rechercher les cycles avec un parcours en profondeur modifié
        cycles = []
        visited = set()
        path = []
        path_set = set()
        
        def dfs(node):
            if node in path_set:
                # Cycle détecté
                cycle_start = path.index(node)
                cycles.append(path[cycle_start:] + [node])
                return
                
            if node in visited:
                return
                
            visited.add(node)
            path.append(node)
            path_set.add(node)
            
            for neighbor in outgoing_edges.get(node, []):
                dfs(neighbor)
                
            path.pop()
            path_set.remove(node)
            
        # Lancer le DFS depuis chaque nœud non visité
        for node in [node.name for node in graph_config.nodes]:
            if node not in visited:
                dfs(node)
                
        return cycles
