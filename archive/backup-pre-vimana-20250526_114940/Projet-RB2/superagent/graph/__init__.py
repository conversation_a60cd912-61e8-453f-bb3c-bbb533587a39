"""
Graph package for agent workflow.
This package contains the implementation of the agent workflow graph using LangGraph.
"""

from .types import State
from .workflow import build_graph, run_workflow

# Workflows spécifiques au domaine des retraites de bien-être
from .retreat_workflow import build_retreat_workflow, run_retreat_workflow
from .partner_workflow import build_partner_workflow, run_partner_workflow
from .client_workflow import build_client_workflow, run_client_workflow

__all__ = [
    # Classes et fonctions de base
    "State",
    "build_graph",
    "run_workflow",

    # Workflows spécifiques au domaine
    "build_retreat_workflow",
    "run_retreat_workflow",
    "build_partner_workflow",
    "run_partner_workflow",
    "build_client_workflow",
    "run_client_workflow"
]
