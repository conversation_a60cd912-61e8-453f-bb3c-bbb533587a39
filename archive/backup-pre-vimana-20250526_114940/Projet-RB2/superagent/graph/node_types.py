from enum import Enum
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field

class NodeType(str, Enum):
    """Types de nœuds disponibles dans le graphe de workflow."""
    COORDINATOR = "coordinator"
    PLANNER = "planner"
    SUPERVISOR = "supervisor"
    RESEARCHER = "researcher"
    CODER = "coder"
    BROWSER = "browser"
    REPORTER = "reporter"

class NodeInput(BaseModel):
    """Modèle pour les entrées d'un nœud."""
    content: str = Field(..., description="Contenu principal de l'entrée")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Métadonnées associées à l'entrée")
    artifacts: Dict[str, Any] = Field(default_factory=dict, description="Artefacts associés à l'entrée")

class NodeOutput(BaseModel):
    """Modèle pour les sorties d'un nœud."""
    content: str = Field(..., description="Contenu principal de la sortie")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Métadonnées associées à la sortie")
    artifacts: Dict[str, Any] = Field(default_factory=dict, description="Artefacts générés par le nœud")
    next_node: Optional[Union[NodeType, List[NodeType]]] = Field(None, description="Prochain(s) nœud(s) à exécuter")

class NodeConfig(BaseModel):
    """Configuration d'un nœud dans le graphe."""
    type: NodeType = Field(..., description="Type du nœud")
    name: str = Field(..., description="Nom unique du nœud")
    description: str = Field("", description="Description du nœud")
    config: Dict[str, Any] = Field(default_factory=dict, description="Configuration spécifique au nœud")
    input_schema: Dict[str, Any] = Field(default_factory=dict, description="Schéma des entrées attendues")
    output_schema: Dict[str, Any] = Field(default_factory=dict, description="Schéma des sorties produites")
    
class EdgeConfig(BaseModel):
    """Configuration d'une arête dans le graphe."""
    source: str = Field(..., description="Nœud source")
    target: str = Field(..., description="Nœud cible")
    condition: Optional[str] = Field(None, description="Condition pour emprunter cette arête")
    
class GraphConfig(BaseModel):
    """Configuration complète du graphe de workflow."""
    name: str = Field(..., description="Nom du graphe")
    description: str = Field("", description="Description du graphe")
    nodes: List[NodeConfig] = Field(default_factory=list, description="Nœuds du graphe")
    edges: List[EdgeConfig] = Field(default_factory=list, description="Arêtes du graphe")
    entry_point: str = Field(..., description="Nœud d'entrée du graphe")
    
class WorkflowState(BaseModel):
    """État d'exécution d'un workflow."""
    workflow_id: str = Field(..., description="Identifiant unique du workflow")
    graph_name: str = Field(..., description="Nom du graphe utilisé")
    current_node: str = Field(..., description="Nœud actuellement en cours d'exécution")
    history: List[Dict[str, Any]] = Field(default_factory=list, description="Historique d'exécution")
    inputs: Dict[str, Any] = Field(default_factory=dict, description="Entrées du workflow")
    outputs: Dict[str, Any] = Field(default_factory=dict, description="Sorties du workflow")
    status: str = Field("running", description="Statut du workflow (running, completed, failed)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Métadonnées du workflow")
    
# Définition des flux de travail standard entre les nœuds
DEFAULT_FLOW = {
    NodeType.COORDINATOR: [NodeType.PLANNER],
    NodeType.PLANNER: [NodeType.SUPERVISOR],
    NodeType.SUPERVISOR: [NodeType.RESEARCHER, NodeType.CODER, NodeType.BROWSER, NodeType.REPORTER],
    NodeType.RESEARCHER: [NodeType.SUPERVISOR],
    NodeType.CODER: [NodeType.SUPERVISOR],
    NodeType.BROWSER: [NodeType.SUPERVISOR],
    NodeType.REPORTER: [NodeType.COORDINATOR]
}
