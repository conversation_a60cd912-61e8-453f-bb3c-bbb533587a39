"""
Error handling utilities.
"""

import logging
import enum
import traceback
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ErrorType(enum.Enum):
    """Enumeration of error types for categorization."""
    VALIDATION_ERROR = "validation_error"
    EXECUTION_ERROR = "execution_error"
    TIMEOUT_ERROR = "timeout_error"
    RESOURCE_ERROR = "resource_error"
    PERMISSION_ERROR = "permission_error"
    NETWORK_ERROR = "network_error"
    UNKNOWN_ERROR = "unknown_error"

def handle_error(error: Exception, 
                 context: Optional[Dict[str, Any]] = None, 
                 error_type: ErrorType = ErrorType.UNKNOWN_ERROR) -> Dict[str, Any]:
    """
    Handle an error and return a structured error object.
    
    Args:
        error: The exception that occurred
        context: Additional context information
        error_type: Type of error for categorization
        
    Returns:
        Structured error object with details
    """
    error_context = context or {}
    
    # Get the stack trace
    stack_trace = traceback.format_exc()
    
    # Log the error
    logger.error(f"Error ({error_type.value}): {str(error)}")
    logger.debug(f"Stack trace: {stack_trace}")
    
    # Create structured error object
    error_obj = {
        "type": error_type.value,
        "message": str(error),
        "stack_trace": stack_trace,
        "context": error_context
    }
    
    return error_obj

def is_recoverable_error(error_obj: Dict[str, Any]) -> bool:
    """
    Determine if an error is recoverable.
    
    Args:
        error_obj: Structured error object
        
    Returns:
        True if the error is recoverable, False otherwise
    """
    # Define which error types are recoverable
    recoverable_types = [
        ErrorType.NETWORK_ERROR.value,
        ErrorType.TIMEOUT_ERROR.value,
        ErrorType.RESOURCE_ERROR.value
    ]
    
    return error_obj.get("type") in recoverable_types
