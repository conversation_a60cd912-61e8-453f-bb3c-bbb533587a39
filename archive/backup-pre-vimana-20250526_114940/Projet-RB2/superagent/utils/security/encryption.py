import base64
import json
import os
from typing import Dict, Any
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Clé pour le chiffrement (dans un environnement de production, cette clé devrait être stockée de manière sécurisée)
# Pour les besoins de ce code, nous générons une clé temporaire
def _generate_key():
    salt = os.urandom(16)
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(b"retreat_and_be_secure_key"))
    return key

# Générer une clé pour cette session
_ENCRYPTION_KEY = _generate_key()
_cipher = Fernet(_ENCRYPTION_KEY)

def encrypt_message(message: Dict[str, Any]) -> str:
    """
    Chiffre un message (dictionnaire) en une chaîne sécurisée.
    
    Args:
        message: Le dictionnaire à chiffrer
        
    Returns:
        Une chaîne chiffrée représentant le message
    """
    # Convertir le dictionnaire en JSON
    message_json = json.dumps(message).encode('utf-8')
    
    # Chiffrer le message
    encrypted_message = _cipher.encrypt(message_json)
    
    # Convertir en chaîne base64 pour faciliter le transport
    return base64.urlsafe_b64encode(encrypted_message).decode('utf-8')

def decrypt_message(encrypted_message: str) -> Dict[str, Any]:
    """
    Déchiffre une chaîne sécurisée en un message (dictionnaire).
    
    Args:
        encrypted_message: La chaîne chiffrée à déchiffrer
        
    Returns:
        Le dictionnaire déchiffré
    """
    # Convertir de base64 à bytes
    encrypted_bytes = base64.urlsafe_b64decode(encrypted_message.encode('utf-8'))
    
    # Déchiffrer le message
    decrypted_message = _cipher.decrypt(encrypted_bytes)
    
    # Convertir le JSON en dictionnaire
    return json.loads(decrypted_message.decode('utf-8'))
