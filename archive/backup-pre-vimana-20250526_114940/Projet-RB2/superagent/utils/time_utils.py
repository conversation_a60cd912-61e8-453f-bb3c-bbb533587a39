"""
Time-related utility functions.
"""

import datetime
from typing import Optional, Union

def get_timestamp() -> str:
    """
    Get the current timestamp in ISO format.
    
    Returns:
        Current timestamp as string in ISO format
    """
    return datetime.datetime.now().isoformat()

def format_timestamp(timestamp: Optional[Union[str, datetime.datetime]] = None, 
                     format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format a timestamp into a human-readable string.
    
    Args:
        timestamp: Timestamp to format (ISO string or datetime object)
                  If None, current time is used
        format_str: Format string for the output
        
    Returns:
        Formatted timestamp string
    """
    if timestamp is None:
        dt = datetime.datetime.now()
    elif isinstance(timestamp, str):
        try:
            dt = datetime.datetime.fromisoformat(timestamp)
        except ValueError:
            # Handle non-ISO format strings
            try:
                dt = datetime.datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                return timestamp  # Return original if parsing fails
    else:
        dt = timestamp
        
    return dt.strftime(format_str)

def calculate_duration(start_time: str, end_time: Optional[str] = None) -> float:
    """
    Calculate duration between two timestamps in seconds.
    
    Args:
        start_time: Start timestamp in ISO format
        end_time: End timestamp in ISO format (default: current time)
        
    Returns:
        Duration in seconds
    """
    start_dt = datetime.datetime.fromisoformat(start_time)
    
    if end_time is None:
        end_dt = datetime.datetime.now()
    else:
        end_dt = datetime.datetime.fromisoformat(end_time)
        
    duration = (end_dt - start_dt).total_seconds()
    return duration
