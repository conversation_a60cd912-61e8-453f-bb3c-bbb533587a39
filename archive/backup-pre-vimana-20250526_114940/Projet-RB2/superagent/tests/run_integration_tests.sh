#!/bin/bash
# Script pour exécuter les tests d'intégration

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Exécution des tests d'intégration ===${NC}"

# Créer le répertoire pour les résultats s'il n'existe pas
mkdir -p tests/results

# Exécuter les tests d'intégration
echo -e "\n${YELLOW}Test d'intégration des workflows...${NC}"
npx jest tests/integration/workflow.integration.test.js --json --outputFile=tests/results/workflow.json || true

echo -e "\n${YELLOW}Test d'intégration des agents et outils...${NC}"
npx jest tests/integration/agent-tool.integration.test.js --json --outputFile=tests/results/agent-tool.json || true

# Générer un rapport de synthèse
echo -e "\n${YELLOW}Génération du rapport de synthèse...${NC}"

# Compter les tests réussis et échoués
PASSED=$(grep -c '"status":"passed"' tests/results/*.json 2>/dev/null || echo "0")
FAILED=$(grep -c '"status":"failed"' tests/results/*.json 2>/dev/null || echo "0")
TOTAL=$((PASSED + FAILED))

# Créer le rapport
cat > tests/results/summary.md << EOF
# Rapport de Tests d'Intégration

## Résumé

- **Tests exécutés**: $TOTAL
- **Tests réussis**: $PASSED
- **Tests échoués**: $FAILED
- **Taux de réussite**: $(( (PASSED * 100) / (TOTAL == 0 ? 1 : TOTAL) ))%

## Détails

### Workflow
$(cat tests/results/workflow.json | jq -r '.testResults[0].assertionResults[] | "- **" + .title + "**: " + .status')

### Agent-Tool
$(cat tests/results/agent-tool.json | jq -r '.testResults[0].assertionResults[] | "- **" + .title + "**: " + .status')

## Problèmes identifiés

$(grep -A 5 '"status":"failed"' tests/results/*.json | grep -B 2 -A 3 "message" | sed 's/^/- /')

## Recommandations

- Corriger les problèmes identifiés ci-dessus
- Ajouter des tests supplémentaires pour les cas limites
- Améliorer la gestion des erreurs dans les composants qui échouent

---

*Rapport généré le: $(date)*
EOF

# Afficher un résumé
if [ $FAILED -eq 0 ]; then
  echo -e "\n${GREEN}Tous les tests d'intégration ont réussi!${NC}"
else
  echo -e "\n${RED}$FAILED tests ont échoué sur un total de $TOTAL tests.${NC}"
  echo -e "${YELLOW}Consultez le rapport détaillé dans tests/results/summary.md${NC}"
fi

echo -e "\n${YELLOW}=== Fin des tests d'intégration ===${NC}"
