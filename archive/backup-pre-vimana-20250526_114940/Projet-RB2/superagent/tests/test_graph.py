import asyncio
import unittest
import sys
import os
import json
from datetime import datetime

# A<PERSON>ter le répertoire parent au chemin pour pouvoir importer les modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configurer le logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

from graph.graph_manager import GraphManager
from graph.node_types import NodeType, NodeConfig, EdgeConfig, GraphConfig

class TestGraph(unittest.TestCase):
    """Tests pour le système de workflow LangGraph"""

    def setUp(self):
        """Configuration des tests"""
        self.graph_manager = GraphManager()

    def test_create_default_graph(self):
        """Teste la création d'un graphe par défaut"""
        # Exécuter la création du graphe de manière asynchrone
        graph_id = asyncio.run(self.graph_manager.create_default_graph())

        self.assertEqual(graph_id, "default_graph")
        self.assertIn(graph_id, self.graph_manager.graphs)
        self.assertIn(graph_id, self.graph_manager.runtime_graphs)

    def test_create_custom_graph(self):
        """Teste la création d'un graphe personnalisé"""
        # Créer une configuration de graphe personnalisée
        nodes = [
            NodeConfig(
                type=NodeType.COORDINATOR,
                name="coordinator",
                description="Coordinator node",
                config={}
            ),
            NodeConfig(
                type=NodeType.CODER,
                name="coder",
                description="Coder node",
                config={"action": "generate_code"}
            ),
            NodeConfig(
                type=NodeType.REPORTER,
                name="reporter",
                description="Reporter node",
                config={"action": "generate_report"}
            )
        ]

        edges = [
            EdgeConfig(
                source="coordinator",
                target="coder"
            ),
            EdgeConfig(
                source="coder",
                target="reporter"
            ),
            EdgeConfig(
                source="reporter",
                target="coordinator"
            )
        ]

        graph_config = GraphConfig(
            name="custom_graph",
            description="Custom workflow graph",
            nodes=nodes,
            edges=edges,
            entry_point="coordinator"
        )

        # Exécuter la création du graphe de manière asynchrone
        graph_id = asyncio.run(self.graph_manager.create_graph(graph_config))

        self.assertEqual(graph_id, "custom_graph")
        self.assertIn(graph_id, self.graph_manager.graphs)
        self.assertIn(graph_id, self.graph_manager.runtime_graphs)

    def test_execute_workflow(self):
        """Teste l'exécution d'un workflow"""
        # Créer d'abord un graphe
        graph_id = asyncio.run(self.graph_manager.create_default_graph())

        # Préparer les données d'entrée
        input_data = {
            "query": "Generate a Python function to calculate factorial",
            "language": "python",
            "format": "markdown"
        }

        # Exécuter le workflow de manière asynchrone
        async def execute_and_check():
            # Exécuter le workflow
            workflow_id = await self.graph_manager.execute_workflow(graph_id, input_data)

            # Vérifier que le workflow a été créé
            self.assertIsNotNone(workflow_id)
            self.assertIn(workflow_id, self.graph_manager.workflows)

            # Attendre un peu pour que le workflow commence à s'exécuter
            await asyncio.sleep(0.1)

            # Récupérer l'état du workflow
            workflow_state = await self.graph_manager.get_workflow_state(workflow_id)

            # Vérifier que le workflow est en cours d'exécution ou terminé
            self.assertIn(workflow_state.status, ["running", "completed"])
            self.assertEqual(workflow_state.graph_name, graph_id)

            return workflow_state

        # Exécuter la fonction asynchrone
        workflow_state = asyncio.run(execute_and_check())

        # Vérifications supplémentaires
        self.assertIsNotNone(workflow_state)

    def test_graph_validation(self):
        """Teste la validation des graphes"""
        # Créer une configuration de graphe invalide (point d'entrée inexistant)
        nodes = [
            NodeConfig(
                type=NodeType.COORDINATOR,
                name="coordinator",
                description="Coordinator node",
                config={}
            )
        ]

        graph_config = GraphConfig(
            name="invalid_graph",
            description="Invalid workflow graph",
            nodes=nodes,
            edges=[],
            entry_point="nonexistent_node"  # Point d'entrée inexistant
        )

        # Vérifier que la création du graphe lève une exception
        with self.assertRaises(ValueError):
            asyncio.run(self.graph_manager.create_graph(graph_config))

if __name__ == "__main__":
    unittest.main()
