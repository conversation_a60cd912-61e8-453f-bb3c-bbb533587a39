import asyncio
import unittest
import sys
import os
import json
from datetime import datetime

# A<PERSON><PERSON> le répertoire parent au chemin pour pouvoir importer les modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configurer le logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

from agents.coder_agent.coder_agent import CoderAgent
from agents.reporter_agent.reporter_agent import ReporterAgent
from agents.research_agent.research_agent import ResearchAgent
from agents.coordinator.coordinator import Coordinator

class TestAgents(unittest.TestCase):
    """Tests pour les agents spécialisés"""

    def setUp(self):
        """Configuration des tests"""
        # Configuration de base pour les agents
        self.coder_config = {
            "SUPPORTED_LANGUAGES": ["python", "javascript", "typescript"],
            "VALIDATION_RULES": {
                "max_line_length": 100
            }
        }

        self.reporter_config = {
            "SUPPORTED_FORMATS": ["markdown", "html", "json"],
            "REPORT_TEMPLATES": {},
            "SAVE_REPORT_HISTORY": False
        }

        self.research_config = {
            "SUPPORTED_SOURCES": ["web", "database", "api"],
            "VALIDATION_RULES": {
                "expected_types": {
                    "query": "string"
                }
            }
        }

        self.coordinator_config = {
            "MAX_CONCURRENT_AGENTS": 5
        }

    def test_coder_agent_initialization(self):
        """Teste l'initialisation de l'agent de code"""
        coder = CoderAgent(self.coder_config)
        self.assertEqual(coder.agent_id, "coder")
        self.assertEqual(coder.supported_languages, ["python", "javascript", "typescript"])

    def test_reporter_agent_initialization(self):
        """Teste l'initialisation de l'agent de rapport"""
        reporter = ReporterAgent(self.reporter_config)
        self.assertEqual(reporter.agent_id, "reporter")
        self.assertEqual(reporter.supported_formats, ["markdown", "html", "json"])

    def test_research_agent_initialization(self):
        """Teste l'initialisation de l'agent de recherche"""
        research = ResearchAgent(self.research_config)
        self.assertEqual(research.agent_id, "research")
        self.assertEqual(research.supported_sources, ["web", "database", "api"])

    def test_coordinator_initialization(self):
        """Teste l'initialisation du coordinateur"""
        coordinator = Coordinator(self.coordinator_config)
        self.assertEqual(coordinator.agent_id, "coordinator")
        self.assertEqual(len(coordinator.active_agents), 0)
        self.assertEqual(len(coordinator.workflows), 0)

    def test_coder_agent_code_generation(self):
        """Teste la génération de code par l'agent de code"""
        coder = CoderAgent(self.coder_config)

        # Initialiser manuellement les templates de code
        async def init_templates():
            return await coder._load_code_templates()

        coder.code_templates = asyncio.run(init_templates())

        # Exécuter la génération de code de manière asynchrone
        result = asyncio.run(coder.generate_code(
            language="python",
            requirements=["Create a function to calculate factorial"],
            context={"description": "Calculate factorial of a number"}
        ))

        self.assertEqual(result["status"], "success")
        self.assertIn("code", result)
        self.assertEqual(result["language"], "python")

    def test_reporter_agent_report_generation(self):
        """Teste la génération de rapport par l'agent de rapport"""
        reporter = ReporterAgent(self.reporter_config)

        # Initialiser l'agent (normalement fait par start())
        asyncio.run(reporter.report_generator.load_templates())

        # Exécuter la génération de rapport de manière asynchrone
        result = asyncio.run(reporter.generate_report(
            report_type="summary",
            data={
                "introduction": "This is a test report",
                "key_points": ["Point 1", "Point 2", "Point 3"],
                "metrics": {"value1": 10, "value2": 20},
                "conclusion": "This is the conclusion"
            },
            format="markdown",
            options={}
        ))

        self.assertEqual(result["status"], "success")
        self.assertIn("report", result)
        self.assertEqual(result["report_type"], "summary")
        self.assertEqual(result["format"], "markdown")

    def test_research_agent_search(self):
        """Teste la recherche par l'agent de recherche"""
        research = ResearchAgent(self.research_config)

        # Exécuter la recherche de manière asynchrone
        result = asyncio.run(research.search(
            query="test query",
            sources=["web"],
            options={"limit": 5}
        ))

        self.assertEqual(result["status"], "success")
        self.assertIn("results", result)
        self.assertEqual(result["sources"], ["web"])

    def test_coordinator_workflow_creation(self):
        """Teste la création de workflow par le coordinateur"""
        coordinator = Coordinator(self.coordinator_config)

        # Créer un workflow de test
        workflow_config = {
            "name": "Test Workflow",
            "description": "A test workflow",
            "tasks": [
                {"agent": "coder", "action": "generate_code", "params": {}},
                {"agent": "reporter", "action": "generate_report", "params": {}}
            ]
        }

        # Exécuter la création de workflow de manière asynchrone
        workflow_id = asyncio.run(coordinator.create_workflow(workflow_config))

        self.assertIsNotNone(workflow_id)
        self.assertIn(workflow_id, coordinator.workflows)
        self.assertEqual(coordinator.workflows[workflow_id]["status"], "initialized")

    def test_agent_message_processing(self):
        """Teste le traitement des messages par les agents"""
        coder = CoderAgent(self.coder_config)

        # Initialiser manuellement les templates de code
        async def init_templates():
            return await coder._load_code_templates()

        coder.code_templates = asyncio.run(init_templates())

        # Message de test pour la génération de code
        message = {
            "action": "generate_code",
            "language": "python",
            "requirements": ["Create a simple function"],
            "context": {"description": "A simple function"}
        }

        # Exécuter le traitement du message de manière asynchrone
        result = asyncio.run(coder.process_message(message))

        self.assertEqual(result["status"], "success")
        self.assertIn("code", result)

        # Message avec une action inconnue
        message = {
            "action": "unknown_action"
        }

        # Exécuter le traitement du message de manière asynchrone
        result = asyncio.run(coder.process_message(message))

        self.assertEqual(result["status"], "error")
        self.assertIn("message", result)

if __name__ == "__main__":
    unittest.main()
