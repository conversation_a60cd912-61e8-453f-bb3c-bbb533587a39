import asyncio
import unittest
import sys
import os
import json
from datetime import datetime

# Ajouter le répertoire parent au chemin pour pouvoir importer les modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configurer le logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

from agents.planner_agent.planner_agent import PlannerAgent
from agents.supervisor_agent.supervisor_agent import SupervisorAgent
from agents.browser_agent.browser_agent import BrowserAgent
from tools.node_tools.coder_tools import CodeGenerationTool, CodeAnalysisTool
from tools.node_tools.reporter_tools import ReportGenerationTool, DataSummarizationTool
from tools.node_tools.research_tools import WebSearchTool, DataAnalysisTool

class TestNewAgents(unittest.TestCase):
    """Tests pour les nouveaux agents et outils"""
    
    def setUp(self):
        """Configuration des tests"""
        # Configuration de base pour les agents
        self.planner_config = {
            "PLAN_TEMPLATES": {},
            "SAVE_PLAN_HISTORY": False
        }
        
        self.supervisor_config = {
            "EVALUATION_CRITERIA": {},
            "SAVE_DECISION_HISTORY": False
        }
        
        self.browser_config = {
            "EXTRACTION_PATTERNS": {},
            "SAVE_BROWSING_HISTORY": False
        }
        
    def test_planner_agent_initialization(self):
        """Teste l'initialisation de l'agent de planification"""
        planner = PlannerAgent(self.planner_config)
        self.assertEqual(planner.agent_id, "planner")
        
    def test_supervisor_agent_initialization(self):
        """Teste l'initialisation de l'agent de supervision"""
        supervisor = SupervisorAgent(self.supervisor_config)
        self.assertEqual(supervisor.agent_id, "supervisor")
        
    def test_browser_agent_initialization(self):
        """Teste l'initialisation de l'agent de navigation"""
        browser = BrowserAgent(self.browser_config)
        self.assertEqual(browser.agent_id, "browser")
        
    def test_planner_agent_create_plan(self):
        """Teste la création de plan par l'agent de planification"""
        planner = PlannerAgent(self.planner_config)
        
        # Exécuter la création de plan de manière asynchrone
        result = asyncio.run(planner.create_plan(
            objective="Develop a new feature",
            constraints=["Time constraint: 2 weeks", "Budget constraint: $5000"],
            resources={"developers": 2, "designers": 1},
            deadline="2023-12-31"
        ))
        
        self.assertEqual(result["status"], "success")
        self.assertIn("plan_id", result)
        self.assertIn("plan", result)
        
    def test_supervisor_agent_evaluate_result(self):
        """Teste l'évaluation de résultat par l'agent de supervision"""
        supervisor = SupervisorAgent(self.supervisor_config)
        
        # Exécuter l'évaluation de résultat de manière asynchrone
        result = asyncio.run(supervisor.evaluate_result(
            task_id="task_1",
            agent_id="coder",
            result={"status": "success", "code": "def hello(): return 'Hello, World!'"},
            criteria=["accuracy", "completeness"]
        ))
        
        self.assertEqual(result["status"], "success")
        self.assertIn("evaluation", result)
        
    def test_browser_agent_visit_url(self):
        """Teste la visite d'URL par l'agent de navigation"""
        browser = BrowserAgent(self.browser_config)
        
        # Exécuter la visite d'URL de manière asynchrone
        result = asyncio.run(browser.visit_url(
            url="https://example.com",
            options={}
        ))
        
        self.assertEqual(result["status"], "success")
        self.assertIn("page", result)
        
    def test_code_generation_tool(self):
        """Teste l'outil de génération de code"""
        tool = CodeGenerationTool()
        
        # Exécuter la génération de code de manière asynchrone
        result = asyncio.run(tool.execute({
            "language": "python",
            "requirements": ["Create a function to calculate factorial"],
            "context": {"description": "Calculate factorial of a number"}
        }))
        
        self.assertEqual(result["status"], "success")
        self.assertIn("code", result)
        self.assertEqual(result["language"], "python")
        
    def test_code_analysis_tool(self):
        """Teste l'outil d'analyse de code"""
        tool = CodeAnalysisTool()
        
        # Exécuter l'analyse de code de manière asynchrone
        result = asyncio.run(tool.execute({
            "code": "def factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n-1)",
            "language": "python",
            "analysis_type": "general"
        }))
        
        self.assertEqual(result["status"], "success")
        self.assertIn("analysis", result)
        
    def test_report_generation_tool(self):
        """Teste l'outil de génération de rapport"""
        tool = ReportGenerationTool()
        
        # Exécuter la génération de rapport de manière asynchrone
        result = asyncio.run(tool.execute({
            "data": {
                "introduction": "This is a test report",
                "key_points": ["Point 1", "Point 2", "Point 3"],
                "metrics": {"value1": 10, "value2": 20},
                "conclusion": "This is the conclusion"
            },
            "report_type": "summary",
            "format": "markdown"
        }))
        
        self.assertEqual(result["status"], "success")
        self.assertIn("report", result)
        
    def test_data_summarization_tool(self):
        """Teste l'outil de résumé de données"""
        tool = DataSummarizationTool()
        
        # Exécuter le résumé de données de manière asynchrone
        result = asyncio.run(tool.execute({
            "data": {
                "field1": 10,
                "field2": 20,
                "field3": [1, 2, 3, 4, 5],
                "field4": True
            },
            "max_length": 500
        }))
        
        self.assertEqual(result["status"], "success")
        self.assertIn("summary", result)
        
    def test_web_search_tool(self):
        """Teste l'outil de recherche web"""
        tool = WebSearchTool()
        
        # Exécuter la recherche web de manière asynchrone
        result = asyncio.run(tool.execute({
            "query": "artificial intelligence",
            "num_results": 3,
            "sources": ["web"]
        }))
        
        self.assertEqual(result["status"], "success")
        self.assertIn("results", result)
        
    def test_data_analysis_tool(self):
        """Teste l'outil d'analyse de données"""
        tool = DataAnalysisTool()
        
        # Exécuter l'analyse de données de manière asynchrone
        result = asyncio.run(tool.execute({
            "data": {
                "field1": 10,
                "field2": 20,
                "field3": [1, 2, 3, 4, 5],
                "text_field": "This is a good example of text data for sentiment analysis"
            },
            "analysis_type": "general"
        }))
        
        self.assertEqual(result["status"], "success")
        self.assertIn("analysis", result)
        
if __name__ == "__main__":
    unittest.main()
