"""
Tests pour le module de recommandation.
"""

import unittest
from unittest.mock import patch, MagicMock
from superagent.recommender.engine import Recommender<PERSON><PERSON>ine, UserProfile, RetreatRecommendation

class TestRecommenderEngine(unittest.TestCase):
    """Tests pour le moteur de recommandation."""
    
    def setUp(self):
        """Initialisation avant chaque test."""
        self.recommender = RecommenderEngine()
        
        # Créer un profil utilisateur de test
        self.user_profile = UserProfile(
            user_id="test_user_1",
            preferences={
                "interests": ["yoga", "méditation", "nature"],
                "price_range": "medium",
                "preferred_duration": [3, 7]
            },
            history=[
                {"retreat_id": "retreat_1", "rating": 4.5, "date": "2023-01-15"}
            ]
        )
    
    def test_initialization(self):
        """Teste l'initialisation du moteur de recommandation."""
        self.assertIsNotNone(self.recommender)
        self.assertEqual(self.recommender.model_name, "recommender_v1")
    
    def test_get_retreat_recommendations(self):
        """Teste la génération de recommandations de retraites."""
        recommendations = self.recommender.get_retreat_recommendations(
            user_profile=self.user_profile,
            count=3
        )
        
        # Vérifier le nombre de recommandations
        self.assertEqual(len(recommendations), 3)
        
        # Vérifier le type des recommandations
        for rec in recommendations:
            self.assertIsInstance(rec, RetreatRecommendation)
            self.assertTrue(hasattr(rec, 'retreat_id'))
            self.assertTrue(hasattr(rec, 'score'))
            self.assertTrue(hasattr(rec, 'reasons'))
            self.assertTrue(hasattr(rec, 'tags'))
    
    def test_get_professional_recommendations(self):
        """Teste la génération de recommandations de professionnels."""
        recommendations = self.recommender.get_professional_recommendations(
            organizer_profile=self.user_profile,
            count=2
        )
        
        # Vérifier le nombre de recommandations
        self.assertEqual(len(recommendations), 2)
        
        # Vérifier la structure des recommandations
        for rec in recommendations:
            self.assertIn('professional_id', rec)
            self.assertIn('score', rec)
            self.assertIn('speciality', rec)
            self.assertIn('complementarity_reason', rec)
    
    def test_optimize_retreat_schedule(self):
        """Teste l'optimisation du planning d'une retraite."""
        retreat_data = {
            "id": "retreat_1",
            "duration": 5,
            "activities": ["yoga", "méditation", "randonnée", "ateliers cuisine"]
        }
        
        constraints = {
            "max_activities_per_day": 4,
            "required_breaks": True,
            "activity_preferences": {
                "yoga": "morning",
                "randonnée": "afternoon"
            }
        }
        
        schedule = self.recommender.optimize_retreat_schedule(
            retreat_data=retreat_data,
            constraints=constraints
        )
        
        # Vérifier la structure du planning
        self.assertIn('days', schedule)
        self.assertEqual(len(schedule['days']), retreat_data['duration'])
        
        # Vérifier les activités pour chaque jour
        for day in schedule['days']:
            self.assertIn('day', day)
            self.assertIn('activities', day)
            self.assertLessEqual(len(day['activities']), constraints['max_activities_per_day'])
            
            # Vérifier la structure de chaque activité
            for activity in day['activities']:
                self.assertIn('time', activity)
                self.assertIn('duration', activity)
                self.assertIn('activity', activity)
    
    @patch('src.recommender.engine.RecommenderEngine._load_model')
    def test_model_loading_error(self, mock_load_model):
        """Teste la gestion des erreurs lors du chargement du modèle."""
        # Simuler une erreur lors du chargement du modèle
        mock_load_model.side_effect = Exception("Model loading error")
        
        # Le constructeur devrait gérer l'erreur et utiliser un modèle de fallback
        recommender = RecommenderEngine()
        self.assertEqual(recommender.model, "fallback_model")
        
        # Les recommandations devraient quand même fonctionner
        recommendations = recommender.get_retreat_recommendations(
            user_profile=self.user_profile,
            count=1
        )
        self.assertEqual(len(recommendations), 1)

if __name__ == '__main__':
    unittest.main()
