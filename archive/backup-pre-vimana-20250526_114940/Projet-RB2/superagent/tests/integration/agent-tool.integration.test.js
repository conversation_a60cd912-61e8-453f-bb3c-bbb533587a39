/**
 * Test d'intégration pour l'interaction entre agents et outils
 * 
 * Ce test vérifie que les agents peuvent correctement utiliser les outils
 * après la migration.
 */

const { browser_node } = require('../../agents/nodes/browser_node');
const { researcher_node } = require('../../agents/nodes/researcher_node');

describe('Agent-Tool Integration Tests', () => {
  test('Browser agent should be able to use web search tools', async () => {
    // Créer un état de test
    const state = {
      task: 'Find information about wellness retreats',
      context: {
        search_query: 'best wellness retreats in France'
      },
      results: {}
    };

    // Exécuter le nœud du navigateur
    const result = await browser_node(state);

    // Vérifier que le nœud a correctement utilisé les outils de recherche
    expect(result).toBeDefined();
    expect(result.results).toBeDefined();
    expect(result.results.web_search).toBeDefined();
    expect(result.status).not.toBe('error');
  });

  test('Research agent should be able to analyze data', async () => {
    // Créer un état de test avec des données à analyser
    const state = {
      task: 'Analyze retreat data',
      context: {
        data: [
          { name: 'Retreat 1', location: 'Paris', rating: 4.5 },
          { name: 'Retreat 2', location: 'Nice', rating: 4.8 },
          { name: 'Retreat 3', location: 'Lyon', rating: 4.2 }
        ]
      },
      results: {}
    };

    // Exécuter le nœud de recherche
    const result = await researcher_node(state);

    // Vérifier que le nœud a correctement analysé les données
    expect(result).toBeDefined();
    expect(result.results).toBeDefined();
    expect(result.results.analysis).toBeDefined();
    expect(result.status).not.toBe('error');
  });

  test('Should handle tool failures gracefully', async () => {
    // Créer un état de test avec des données qui provoqueront une erreur d'outil
    const state = {
      task: 'Use a tool that will fail',
      context: {
        invalid_data: true
      },
      results: {}
    };

    // Exécuter le nœud du navigateur (qui devrait gérer l'échec de l'outil)
    const result = await browser_node(state);

    // Vérifier que l'échec de l'outil a été géré correctement
    expect(result).toBeDefined();
    expect(result.status).not.toBe('error'); // Le nœud lui-même ne devrait pas échouer
    expect(result.results.tool_errors).toBeDefined(); // Mais il devrait enregistrer l'erreur
  });
});
