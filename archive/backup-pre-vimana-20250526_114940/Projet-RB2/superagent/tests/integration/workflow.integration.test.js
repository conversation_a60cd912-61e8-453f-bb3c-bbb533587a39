/**
 * Test d'intégration pour les workflows
 * 
 * Ce test vérifie que les différents composants du système de workflow
 * fonctionnent correctement ensemble après la migration.
 */

const { build_graph, run_workflow } = require('../../graph/workflow');
const { State } = require('../../graph/types');

describe('Workflow Integration Tests', () => {
  test('Should build a graph with all required nodes', async () => {
    // Construire un graphe de workflow
    const graph = build_graph();
    
    // Vérifier que le graphe contient tous les nœuds nécessaires
    expect(graph).toBeDefined();
    expect(graph.getNodes()).toContain('coordinator');
    expect(graph.getNodes()).toContain('planner');
    expect(graph.getNodes()).toContain('supervisor');
    expect(graph.getNodes()).toContain('researcher');
    expect(graph.getNodes()).toContain('coder');
    expect(graph.getNodes()).toContain('browser');
    expect(graph.getNodes()).toContain('reporter');
  });

  test('Should execute a simple workflow end-to-end', async () => {
    // Créer un état initial
    const initialState = new State({
      task: 'Test task for integration testing',
      context: {
        user: {
          id: 'test-user-id',
          preferences: {
            language: 'fr'
          }
        }
      },
      results: {}
    });

    // Exécuter le workflow
    const finalState = await run_workflow(initialState);

    // Vérifier que le workflow s'est exécuté correctement
    expect(finalState).toBeDefined();
    expect(finalState.results).toBeDefined();
    expect(finalState.status).toBe('completed');
  });

  test('Should handle errors gracefully', async () => {
    // Créer un état initial avec des données invalides pour provoquer une erreur
    const initialState = new State({
      task: null, // Tâche invalide
      context: {},
      results: {}
    });

    // Exécuter le workflow
    const finalState = await run_workflow(initialState);

    // Vérifier que l'erreur a été gérée correctement
    expect(finalState).toBeDefined();
    expect(finalState.status).toBe('error');
    expect(finalState.error).toBeDefined();
  });
});
