"""
Tests for the workflow engine.
"""

import asyncio
import unittest
import json
from datetime import datetime

from superagent.graph.workflow_engine import WorkflowEngine
from superagent.graph.types import State

class TestWorkflowEngine(unittest.TestCase):
    """
    Tests for the workflow engine.
    """
    
    def setUp(self):
        """
        Set up the test environment.
        """
        self.engine = WorkflowEngine()
        
    def test_create_workflow(self):
        """
        Test creating a workflow.
        """
        # Create a task
        task = {
            "title": "Test Task",
            "description": "This is a test task",
            "type": "test"
        }
        
        # Create a workflow
        workflow_id = asyncio.run(self.engine.create_workflow(task))
        
        # Check that the workflow was created
        self.assertIsNotNone(workflow_id)
        self.assertTrue(workflow_id.startswith("workflow_"))
        
        # Get the workflow
        workflow = asyncio.run(self.engine.get_workflow(workflow_id))
        
        # Check the workflow properties
        self.assertEqual(workflow["id"], workflow_id)
        self.assertEqual(workflow["status"], "created")
        self.assertEqual(workflow["state"].task, task)
        self.assertEqual(workflow["state"].next, "coordinator")
        
    def test_execute_workflow(self):
        """
        Test executing a workflow.
        """
        # Create a task
        task = {
            "title": "Test Execution",
            "description": "This is a test execution task",
            "type": "test"
        }
        
        # Create a workflow
        workflow_id = asyncio.run(self.engine.create_workflow(task))
        
        # Execute the workflow
        workflow = asyncio.run(self.engine.execute_workflow(workflow_id))
        
        # Check the workflow status
        self.assertIn(workflow["status"], ["running", "completed", "failed"])
        
    def test_cancel_workflow(self):
        """
        Test cancelling a workflow.
        """
        # Create a task
        task = {
            "title": "Test Cancellation",
            "description": "This is a test cancellation task",
            "type": "test"
        }
        
        # Create a workflow
        workflow_id = asyncio.run(self.engine.create_workflow(task))
        
        # Cancel the workflow
        workflow = asyncio.run(self.engine.cancel_workflow(workflow_id))
        
        # Check the workflow status
        self.assertEqual(workflow["status"], "cancelled")
        
    def test_add_feedback(self):
        """
        Test adding feedback to a workflow.
        """
        # Create a task
        task = {
            "title": "Test Feedback",
            "description": "This is a test feedback task",
            "type": "test"
        }
        
        # Create a workflow
        workflow_id = asyncio.run(self.engine.create_workflow(task))
        
        # Add feedback
        feedback = {
            "rating": 5,
            "comment": "This is a test feedback"
        }
        
        workflow = asyncio.run(self.engine.add_feedback(workflow_id, feedback))
        
        # Check the feedback
        self.assertIn("feedback", workflow)
        self.assertEqual(len(workflow["feedback"]), 1)
        self.assertEqual(workflow["feedback"][0]["content"], feedback)
        
    def test_list_workflows(self):
        """
        Test listing workflows.
        """
        # Create some workflows
        tasks = [
            {
                "title": "Test List 1",
                "description": "This is test list task 1",
                "type": "test"
            },
            {
                "title": "Test List 2",
                "description": "This is test list task 2",
                "type": "test"
            }
        ]
        
        for task in tasks:
            asyncio.run(self.engine.create_workflow(task))
            
        # List all workflows
        workflows = asyncio.run(self.engine.list_workflows())
        
        # Check that we have at least the number of workflows we created
        self.assertGreaterEqual(len(workflows), len(tasks))
        
        # Check that all workflows have the expected properties
        for workflow in workflows:
            self.assertIn("id", workflow)
            self.assertIn("status", workflow)
            self.assertIn("state", workflow)
            
    def test_get_workflow_results(self):
        """
        Test getting workflow results.
        """
        # Create a task
        task = {
            "title": "Test Results",
            "description": "This is a test results task",
            "type": "test"
        }
        
        # Create a workflow
        workflow_id = asyncio.run(self.engine.create_workflow(task))
        
        # Execute the workflow
        asyncio.run(self.engine.execute_workflow(workflow_id))
        
        # Get the results
        results = asyncio.run(self.engine.get_workflow_results(workflow_id))
        
        # Check the results
        self.assertIn("status", results)
        self.assertIn("results", results)
        
if __name__ == "__main__":
    unittest.main()
