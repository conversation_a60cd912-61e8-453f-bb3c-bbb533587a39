"""
Tests pour le module de chatbot.
"""

import unittest
from unittest.mock import patch, MagicMock
from superagent.chatbot.engine import ChatbotEngine, Conversation, Message, MessageRole

class TestChatbotEngine(unittest.TestCase):
    """Tests pour le moteur de chatbot."""
    
    def setUp(self):
        """Initialisation avant chaque test."""
        self.chatbot = ChatbotEngine()
        
        # Créer une conversation de test
        self.conversation = Conversation(
            conversation_id="test_conv_1",
            messages=[
                Message(role=MessageRole.SYSTEM, content="Vous êtes l'assistant de support client de Retreat And Be."),
                Message(role=MessageRole.USER, content="Quelles sont les conditions d'annulation?")
            ]
        )
    
    def test_initialization(self):
        """Teste l'initialisation du moteur de chatbot."""
        self.assertIsNotNone(self.chatbot)
        self.assertEqual(self.chatbot.model_name, "chatbot_v1")
        self.assertIn("customer_support", self.chatbot.personas)
        self.assertIn("retreat_planner", self.chatbot.personas)
        self.assertIn("wellness_coach", self.chatbot.personas)
    
    def test_get_response(self):
        """Teste la génération de réponses."""
        # Tester avec différents personas
        for persona in ["customer_support", "retreat_planner", "wellness_coach"]:
            response = self.chatbot.get_response(
                conversation=self.conversation,
                persona=persona
            )
            
            # Vérifier que la réponse n'est pas vide
            self.assertIsNotNone(response)
            self.assertIsInstance(response, str)
            self.assertTrue(len(response) > 0)
    
    def test_get_response_with_unknown_persona(self):
        """Teste la gestion des personas inconnus."""
        response = self.chatbot.get_response(
            conversation=self.conversation,
            persona="unknown_persona"
        )
        
        # Devrait utiliser le persona par défaut (customer_support)
        self.assertIsNotNone(response)
        self.assertIsInstance(response, str)
        self.assertTrue(len(response) > 0)
    
    def test_detect_intent(self):
        """Teste la détection d'intention."""
        # Tester différents types de messages
        test_cases = [
            {
                "message": "Je voudrais réserver une retraite de yoga",
                "expected_intent": "booking_inquiry"
            },
            {
                "message": "Comment puis-je annuler ma réservation?",
                "expected_intent": "cancellation_inquiry"
            },
            {
                "message": "Quel est le prix de la retraite en Provence?",
                "expected_intent": "pricing_inquiry"
            },
            {
                "message": "Proposez-vous des séances de méditation?",
                "expected_intent": "activity_inquiry"
            },
            {
                "message": "Bonjour, comment allez-vous?",
                "expected_intent": "general_inquiry"
            }
        ]
        
        for test_case in test_cases:
            result = self.chatbot.detect_intent(test_case["message"])
            
            # Vérifier la structure du résultat
            self.assertIn("intent", result)
            self.assertIn("confidence", result)
            self.assertIn("entities", result)
            
            # Vérifier l'intention détectée
            self.assertEqual(result["intent"], test_case["expected_intent"])
            
            # Vérifier que la confiance est un nombre entre 0 et 1
            self.assertIsInstance(result["confidence"], float)
            self.assertTrue(0 <= result["confidence"] <= 1)
    
    def test_translate_message(self):
        """Teste la traduction de messages."""
        original_message = "Bonjour, comment puis-je vous aider?"
        
        # Tester la traduction vers différentes langues
        for lang in ["en", "es", "de", "it"]:
            translated = self.chatbot.translate_message(original_message, lang)
            
            # Vérifier que la traduction n'est pas vide
            self.assertIsNotNone(translated)
            self.assertIsInstance(translated, str)
            self.assertTrue(len(translated) > 0)
            
            # Vérifier que la traduction contient le code de langue
            self.assertIn(lang, translated)
    
    @patch('src.chatbot.engine.ChatbotEngine._load_model')
    def test_model_loading_error(self, mock_load_model):
        """Teste la gestion des erreurs lors du chargement du modèle."""
        # Simuler une erreur lors du chargement du modèle
        mock_load_model.side_effect = Exception("Model loading error")
        
        # Le constructeur devrait gérer l'erreur et utiliser un modèle de fallback
        chatbot = ChatbotEngine()
        self.assertEqual(chatbot.model, "fallback_model")
        
        # Les réponses devraient quand même fonctionner
        response = chatbot.get_response(
            conversation=self.conversation,
            persona="customer_support"
        )
        self.assertIsNotNone(response)
        self.assertTrue(len(response) > 0)

if __name__ == '__main__':
    unittest.main()
