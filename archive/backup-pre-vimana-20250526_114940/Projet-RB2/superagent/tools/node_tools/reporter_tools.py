from typing import Dict, Any, Optional, List
import logging
import json
from datetime import datetime
from .base_tool import BaseTool

class ReportGenerationTool(BaseTool):
    """
    Outil pour générer des rapports à partir de données.
    """

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(
            name="report_generation",
            description="Génère des rapports formatés à partir de données",
            config=config
        )

    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Génère un rapport basé sur les données fournies.

        Args:
            inputs: Les entrées pour l'outil, incluant:
                - data: Les données pour le rapport
                - report_type: Le type de rapport à générer
                - format: Le format de sortie du rapport
                - options: Options supplémentaires pour la génération

        Returns:
            Les résultats de la génération de rapport
        """
        self.logger.info("Executing report generation tool")

        # Valider les entrées
        data = inputs.get("data", {})
        report_type = inputs.get("report_type", "summary")
        format = inputs.get("format", "markdown")
        options = inputs.get("options", {})

        if not data:
            return {
                "status": "error",
                "message": "No data provided for report generation"
            }

        # Préparer les paramètres pour l'agent de rapport
        agent_message = {
            "action": "generate_report",
            "report_type": report_type,
            "data": data,
            "format": format,
            "options": options
        }

        # Initialiser l'agent de rapport
        from superagent.agents.reporter_agent.reporter_agent import ReporterAgent

        # Récupérer la configuration de l'agent depuis la configuration de l'outil
        agent_config = self.config.get("agent_config", {})
        reporter_agent = ReporterAgent(agent_config)

        try:
            # Appeler l'agent de rapport pour générer le rapport
            report_result = await reporter_agent.generate_report(
                report_type=report_type,
                data=data,
                format=format,
                options=options
            )

            # Nettoyer les ressources de l'agent
            await reporter_agent._cleanup()

            if report_result.get("status") != "success":
                return {
                    "status": "error",
                    "message": report_result.get("message", "Unknown error during report generation")
                }

            report = report_result.get("report", "")
        except Exception as e:
            self.logger.error(f"Error during report generation: {str(e)}")
            return {
                "status": "error",
                "message": f"Error during report generation: {str(e)}"
            }

        return {
            "status": "success",
            "report": report,
            "report_type": report_type,
            "format": format,
            "timestamp": datetime.now().isoformat()
        }

    def get_input_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des entrées attendues par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des entrées
        """
        return {
            "type": "object",
            "properties": {
                "data": {
                    "type": "object",
                    "description": "Les données à inclure dans le rapport"
                },
                "report_type": {
                    "type": "string",
                    "description": "Le type de rapport à générer",
                    "enum": ["summary", "detailed", "analytics"]
                },
                "format": {
                    "type": "string",
                    "description": "Le format de sortie du rapport",
                    "enum": ["markdown", "html", "json"]
                },
                "options": {
                    "type": "object",
                    "description": "Options supplémentaires pour la génération"
                }
            },
            "required": ["data"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des sorties produites par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des sorties
        """
        return {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "description": "Statut de la génération de rapport",
                    "enum": ["success", "error"]
                },
                "report": {
                    "type": "string",
                    "description": "Le rapport généré"
                },
                "report_type": {
                    "type": "string",
                    "description": "Le type de rapport généré"
                },
                "format": {
                    "type": "string",
                    "description": "Le format du rapport généré"
                },
                "timestamp": {
                    "type": "string",
                    "description": "Horodatage de la génération du rapport"
                },
                "message": {
                    "type": "string",
                    "description": "Message d'erreur en cas d'échec"
                }
            },
            "required": ["status"]
        }

    def _generate_sample_report(self, data: Dict[str, Any], report_type: str,
                              format: str, options: Dict[str, Any]) -> str:
        """
        Génère un exemple de rapport pour les tests.

        Args:
            data: Les données pour le rapport
            report_type: Le type de rapport à générer
            format: Le format de sortie du rapport
            options: Options supplémentaires pour la génération

        Returns:
            Le rapport généré
        """
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if report_type == "summary":
            if format == "markdown":
                # Générer un rapport de résumé en markdown
                report = f"# Summary Report\n\n"

                # Ajouter une introduction si fournie
                if "introduction" in data:
                    report += f"{data['introduction']}\n\n"

                # Ajouter les points clés
                if "key_points" in data and isinstance(data["key_points"], list):
                    report += "## Key Points\n\n"
                    for point in data["key_points"]:
                        report += f"- {point}\n"
                    report += "\n"

                # Ajouter les métriques
                if "metrics" in data and isinstance(data["metrics"], dict):
                    report += "## Key Metrics\n\n"
                    for key, value in data["metrics"].items():
                        report += f"- **{key}**: {value}\n"
                    report += "\n"

                # Ajouter une conclusion si fournie
                if "conclusion" in data:
                    report += f"## Conclusion\n\n{data['conclusion']}\n\n"

                report += f"*Generated on {current_date}*"

                return report

            elif format == "html":
                # Générer un rapport de résumé en HTML
                report = f"<h1>Summary Report</h1>\n\n"

                # Ajouter une introduction si fournie
                if "introduction" in data:
                    report += f"<p>{data['introduction']}</p>\n\n"

                # Ajouter les points clés
                if "key_points" in data and isinstance(data["key_points"], list):
                    report += "<h2>Key Points</h2>\n<ul>\n"
                    for point in data["key_points"]:
                        report += f"<li>{point}</li>\n"
                    report += "</ul>\n\n"

                # Ajouter les métriques
                if "metrics" in data and isinstance(data["metrics"], dict):
                    report += "<h2>Key Metrics</h2>\n<ul>\n"
                    for key, value in data["metrics"].items():
                        report += f"<li><strong>{key}</strong>: {value}</li>\n"
                    report += "</ul>\n\n"

                # Ajouter une conclusion si fournie
                if "conclusion" in data:
                    report += f"<h2>Conclusion</h2>\n<p>{data['conclusion']}</p>\n\n"

                report += f"<p><em>Generated on {current_date}</em></p>"

                return report

            elif format == "json":
                # Générer un rapport de résumé en JSON
                report_data = {
                    "title": "Summary Report",
                    "generated_on": current_date,
                    "content": {}
                }

                # Ajouter les sections du rapport
                if "introduction" in data:
                    report_data["content"]["introduction"] = data["introduction"]

                if "key_points" in data:
                    report_data["content"]["key_points"] = data["key_points"]

                if "metrics" in data:
                    report_data["content"]["metrics"] = data["metrics"]

                if "conclusion" in data:
                    report_data["content"]["conclusion"] = data["conclusion"]

                return json.dumps(report_data, indent=2)

        elif report_type == "detailed":
            if format == "markdown":
                # Générer un rapport détaillé en markdown
                report = f"# Detailed Report\n\n"

                # Ajouter un aperçu
                report += "## Overview\n\n"
                if "overview" in data:
                    report += f"{data['overview']}\n\n"
                else:
                    report += "This report provides a detailed analysis of the provided data.\n\n"

                # Ajouter les détails
                report += "## Details\n\n"
                if "details" in data and isinstance(data["details"], list):
                    for i, section in enumerate(data["details"]):
                        if isinstance(section, dict) and "title" in section and "content" in section:
                            report += f"### {section['title']}\n\n{section['content']}\n\n"
                        else:
                            report += f"### Section {i+1}\n\n{section}\n\n"
                elif "details" in data:
                    report += f"{data['details']}\n\n"
                else:
                    report += "No detailed information provided.\n\n"

                # Ajouter une conclusion
                report += "## Conclusion\n\n"
                if "conclusion" in data:
                    report += f"{data['conclusion']}\n\n"
                elif "recommendations" in data:
                    report += "Based on the analysis, the following recommendations are provided:\n\n"
                    if isinstance(data["recommendations"], list):
                        for rec in data["recommendations"]:
                            report += f"- {rec}\n"
                    else:
                        report += f"{data['recommendations']}\n\n"
                else:
                    report += "No conclusion provided.\n\n"

                report += f"*Generated on {current_date}*"

                return report

            # Formats HTML et JSON similaires à ceux du rapport de résumé
            # (non inclus pour brièveté)

        elif report_type == "analytics":
            if format == "markdown":
                # Générer un rapport d'analytique en markdown
                report = f"# Analytics Report\n\n"

                # Ajouter les métriques clés
                report += "## Key Metrics\n\n"
                if "metrics" in data and isinstance(data["metrics"], dict):
                    for category, values in data["metrics"].items():
                        report += f"### {category}\n\n"

                        if isinstance(values, dict):
                            for key, value in values.items():
                                report += f"- **{key}**: {value}\n"
                        elif isinstance(values, list):
                            for item in values:
                                if isinstance(item, dict) and "name" in item and "value" in item:
                                    report += f"- **{item['name']}**: {item['value']}"
                                    if "change" in item:
                                        report += f" ({item['change']})"
                                    report += "\n"
                                else:
                                    report += f"- {item}\n"
                        else:
                            report += f"{values}\n"

                        report += "\n"
                else:
                    report += "No metrics provided.\n\n"

                # Ajouter les tendances
                report += "## Trends\n\n"
                if "trends" in data:
                    if isinstance(data["trends"], list):
                        for trend in data["trends"]:
                            if isinstance(trend, dict) and "title" in trend and "description" in trend:
                                report += f"### {trend['title']}\n\n{trend['description']}\n\n"
                            else:
                                report += f"- {trend}\n"
                    else:
                        report += f"{data['trends']}\n\n"
                else:
                    report += "No trend analysis provided.\n\n"

                # Ajouter les insights
                report += "## Insights\n\n"
                if "insights" in data:
                    if isinstance(data["insights"], list):
                        for i, insight in enumerate(data["insights"]):
                            if isinstance(insight, dict) and "title" in insight and "description" in insight:
                                report += f"### {insight['title']}\n\n{insight['description']}\n\n"
                            else:
                                report += f"### Insight {i+1}\n\n{insight}\n\n"
                    else:
                        report += f"{data['insights']}\n\n"
                else:
                    report += "No insights provided.\n\n"

                report += f"*Generated on {current_date}*"

                return report

            # Formats HTML et JSON similaires à ceux du rapport de résumé
            # (non inclus pour brièveté)

        # Format par défaut pour les types de rapport non gérés
        return f"# Report\n\nGenerated on {current_date}\n\nReport type '{report_type}' in format '{format}' not fully implemented."


class DataSummarizationTool(BaseTool):
    """
    Outil pour résumer des données complexes.
    """

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(
            name="data_summarization",
            description="Crée un résumé concis à partir de données complexes",
            config=config
        )

    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Résume les données fournies.

        Args:
            inputs: Les entrées pour l'outil, incluant:
                - data: Les données à résumer
                - max_length: Longueur maximale du résumé

        Returns:
            Les résultats de la résumé des données
        """
        self.logger.info("Executing data summarization tool")

        # Valider les entrées
        data = inputs.get("data", {})
        max_length = inputs.get("max_length", 500)

        if not data:
            return {
                "status": "error",
                "message": "No data provided for summarization"
            }

        # Préparer les paramètres pour l'agent de rapport
        agent_message = {
            "action": "summarize_data",
            "data": data,
            "max_length": max_length
        }

        # Initialiser l'agent de rapport
        from superagent.agents.reporter_agent.reporter_agent import ReporterAgent

        # Récupérer la configuration de l'agent depuis la configuration de l'outil
        agent_config = self.config.get("agent_config", {})
        reporter_agent = ReporterAgent(agent_config)

        try:
            # Appeler l'agent de rapport pour résumer les données
            summary_result = await reporter_agent.summarize_data(
                data=data,
                max_length=max_length
            )

            # Nettoyer les ressources de l'agent
            await reporter_agent._cleanup()

            if summary_result.get("status") != "success":
                return {
                    "status": "error",
                    "message": summary_result.get("message", "Unknown error during data summarization")
                }

            summary = summary_result.get("summary", "")
            key_points = summary_result.get("key_points", [])
        except Exception as e:
            self.logger.error(f"Error during data summarization: {str(e)}")
            return {
                "status": "error",
                "message": f"Error during data summarization: {str(e)}"
            }

        return {
            "status": "success",
            "summary": summary,
            "key_points": key_points,
            "original_data_size": len(json.dumps(data)),
            "summary_size": len(summary)
        }

    def get_input_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des entrées attendues par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des entrées
        """
        return {
            "type": "object",
            "properties": {
                "data": {
                    "type": "object",
                    "description": "Les données à résumer"
                },
                "max_length": {
                    "type": "integer",
                    "description": "Longueur maximale du résumé en caractères",
                    "minimum": 100,
                    "maximum": 2000
                }
            },
            "required": ["data"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des sorties produites par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des sorties
        """
        return {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "description": "Statut de la résumé des données",
                    "enum": ["success", "error"]
                },
                "summary": {
                    "type": "string",
                    "description": "Le résumé généré"
                },
                "key_points": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Les points clés extraits des données"
                },
                "original_data_size": {
                    "type": "integer",
                    "description": "Taille des données originales en caractères"
                },
                "summary_size": {
                    "type": "integer",
                    "description": "Taille du résumé en caractères"
                },
                "message": {
                    "type": "string",
                    "description": "Message d'erreur en cas d'échec"
                }
            },
            "required": ["status"]
        }

    def _extract_key_points(self, data: Dict[str, Any]) -> List[str]:
        """
        Extrait les points clés des données.

        Args:
            data: Les données à analyser

        Returns:
            Une liste de points clés
        """
        key_points = []

        # Extraire les métriques numériques
        for key, value in data.items():
            if isinstance(value, (int, float)):
                key_points.append(f"{key.replace('_', ' ').title()}: {value}")

        # Extraire les listes courtes
        for key, value in data.items():
            if isinstance(value, list) and len(value) <= 5:
                items = ", ".join(str(item) for item in value)
                key_points.append(f"{key.replace('_', ' ').title()}: {items}")

        # Extraire les valeurs booléennes importantes
        for key, value in data.items():
            if isinstance(value, bool):
                key_points.append(f"{key.replace('_', ' ').title()}: {'Yes' if value else 'No'}")

        return key_points

    def _generate_summary(self, key_points: List[str], max_length: int) -> str:
        """
        Génère un résumé basé sur les points clés.

        Args:
            key_points: Les points clés à inclure dans le résumé
            max_length: Longueur maximale du résumé

        Returns:
            Le résumé généré
        """
        # Créer une introduction
        summary = "Summary of the analyzed data:\n\n"

        # Ajouter les points clés
        for point in key_points:
            if len(summary) + len(point) + 2 <= max_length:
                summary += f"- {point}\n"
            else:
                summary += "- And more...\n"
                break

        return summary
