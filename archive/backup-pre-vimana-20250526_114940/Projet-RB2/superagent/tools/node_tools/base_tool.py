from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import logging

class BaseTool(ABC):
    """
    Classe de base pour tous les outils de nœuds.
    Les outils fournissent des fonctionnalités spécifiques aux nœuds du graphe.
    """
    
    def __init__(self, name: str, description: str, config: Dict[str, Any] = None):
        self.name = name
        self.description = description
        self.config = config or {}
        self.logger = logging.getLogger(f"tools.{name}")
        
    @abstractmethod
    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Exécute l'outil avec les entrées fournies.
        
        Args:
            inputs: Les entrées pour l'outil
            
        Returns:
            Les résultats de l'exécution de l'outil
        """
        pass
        
    def get_metadata(self) -> Dict[str, Any]:
        """
        Récupère les métadonnées de l'outil.
        
        Returns:
            Un dictionnaire contenant les métadonnées de l'outil
        """
        return {
            "name": self.name,
            "description": self.description,
            "config": {k: v for k, v in self.config.items() if not k.startswith("_")}
        }
        
    @abstractmethod
    def get_input_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des entrées attendues par l'outil.
        
        Returns:
            Un dictionnaire décrivant le schéma des entrées
        """
        pass
        
    @abstractmethod
    def get_output_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des sorties produites par l'outil.
        
        Returns:
            Un dictionnaire décrivant le schéma des sorties
        """
        pass
