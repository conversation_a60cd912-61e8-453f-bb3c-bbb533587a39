from typing import Dict, Any, Optional, List
import logging
import json
import re
import asyncio
from datetime import datetime
from .base_tool import BaseTool

class WebSearchTool(BaseTool):
    """
    Outil pour effectuer des recherches sur le web.
    """

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(
            name="web_search",
            description="Effectue des recherches sur le web pour trouver des informations pertinentes",
            config=config
        )

    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Effectue une recherche sur le web.

        Args:
            inputs: Les entrées pour l'outil, incluant:
                - query: La requête de recherche
                - num_results: Nombre de résultats à retourner
                - sources: Sources à utiliser pour la recherche

        Returns:
            Les résultats de la recherche
        """
        self.logger.info("Executing web search tool")

        # Valider les entrées
        query = inputs.get("query", "")
        num_results = inputs.get("num_results", 5)
        sources = inputs.get("sources", ["web"])

        if not query:
            return {
                "status": "error",
                "message": "No query provided for search"
            }

        # Préparer les paramètres pour l'agent de recherche
        agent_message = {
            "action": "search",
            "query": query,
            "sources": sources,
            "options": {"limit": num_results}
        }

        # Initialiser l'agent de recherche
        from superagent.agents.research_agent.research_agent import ResearchAgent

        # Récupérer la configuration de l'agent depuis la configuration de l'outil
        agent_config = self.config.get("agent_config", {})
        research_agent = ResearchAgent(agent_config)

        try:
            # Appeler l'agent de recherche pour effectuer la recherche
            search_result = await research_agent.search(
                query=query,
                sources=sources,
                options={"limit": num_results}
            )

            # Nettoyer les ressources de l'agent
            await research_agent._cleanup()

            if search_result.get("status") != "success":
                return {
                    "status": "error",
                    "message": search_result.get("message", "Unknown error during search")
                }

            results = search_result.get("results", [])
        except Exception as e:
            self.logger.error(f"Error during search: {str(e)}")
            return {
                "status": "error",
                "message": f"Error during search: {str(e)}"
            }

        return {
            "status": "success",
            "results": results,
            "query": query,
            "sources": sources,
            "timestamp": datetime.now().isoformat()
        }

    def get_input_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des entrées attendues par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des entrées
        """
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "La requête de recherche"
                },
                "num_results": {
                    "type": "integer",
                    "description": "Nombre de résultats à retourner",
                    "minimum": 1,
                    "maximum": 20,
                    "default": 5
                },
                "sources": {
                    "type": "array",
                    "items": {
                        "type": "string",
                        "enum": ["web", "database", "api"]
                    },
                    "description": "Sources à utiliser pour la recherche",
                    "default": ["web"]
                }
            },
            "required": ["query"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des sorties produites par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des sorties
        """
        return {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "description": "Statut de la recherche",
                    "enum": ["success", "error"]
                },
                "results": {
                    "type": "array",
                    "items": {
                        "type": "object"
                    },
                    "description": "Les résultats de la recherche"
                },
                "query": {
                    "type": "string",
                    "description": "La requête de recherche utilisée"
                },
                "sources": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Les sources utilisées pour la recherche"
                },
                "timestamp": {
                    "type": "string",
                    "description": "Horodatage de la recherche"
                },
                "message": {
                    "type": "string",
                    "description": "Message d'erreur en cas d'échec"
                }
            },
            "required": ["status"]
        }

    def _simulate_search_results(self, query: str, num_results: int, sources: List[str]) -> List[Dict[str, Any]]:
        """
        Simule des résultats de recherche pour les tests.

        Args:
            query: La requête de recherche
            num_results: Nombre de résultats à retourner
            sources: Sources à utiliser pour la recherche

        Returns:
            Une liste de résultats de recherche
        """
        results = []

        # Générer des résultats pour chaque source
        for source in sources:
            if source == "web":
                # Simuler des résultats web
                for i in range(min(num_results, 3)):
                    results.append({
                        "source": "web",
                        "title": f"Web result {i+1} for '{query}'",
                        "url": f"https://example.com/search?q={query.replace(' ', '+')}&result={i+1}",
                        "snippet": f"This is a sample result for the query '{query}'. It contains information that might be relevant to your search.",
                        "relevance": 0.9 - (i * 0.1)
                    })
            elif source == "database":
                # Simuler des résultats de base de données
                for i in range(min(num_results, 2)):
                    results.append({
                        "source": "database",
                        "id": i+1,
                        "title": f"Database entry {i+1} for '{query}'",
                        "data": {
                            "field1": f"Value 1 for {query}",
                            "field2": f"Value 2 for {query}",
                            "timestamp": datetime.now().isoformat()
                        },
                        "relevance": 0.85 - (i * 0.1)
                    })
            elif source == "api":
                # Simuler des résultats d'API
                for i in range(min(num_results, 2)):
                    results.append({
                        "source": "api",
                        "api_id": f"api-{i+1}",
                        "title": f"API result {i+1} for '{query}'",
                        "content": {
                            "key1": f"API value 1 for {query}",
                            "key2": f"API value 2 for {query}",
                            "timestamp": datetime.now().isoformat()
                        },
                        "relevance": 0.8 - (i * 0.1)
                    })

        # Trier par pertinence
        results.sort(key=lambda x: x.get("relevance", 0), reverse=True)

        # Limiter au nombre demandé
        return results[:num_results]


class DataAnalysisTool(BaseTool):
    """
    Outil pour analyser des données selon différents types d'analyses.
    """

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(
            name="data_analysis",
            description="Analyse des données selon différents types d'analyses (générale, statistique, sentiment, tendance)",
            config=config
        )

    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyse les données fournies.

        Args:
            inputs: Les entrées pour l'outil, incluant:
                - data: Les données à analyser
                - analysis_type: Le type d'analyse à effectuer

        Returns:
            Les résultats de l'analyse
        """
        self.logger.info("Executing data analysis tool")

        # Valider les entrées
        data = inputs.get("data", {})
        analysis_type = inputs.get("analysis_type", "general")

        if not data:
            return {
                "status": "error",
                "message": "No data provided for analysis"
            }

        # Préparer les paramètres pour l'agent de recherche
        agent_message = {
            "action": "analyze_data",
            "data": data,
            "analysis_type": analysis_type
        }

        # Initialiser l'agent de recherche
        from superagent.agents.research_agent.research_agent import ResearchAgent

        # Récupérer la configuration de l'agent depuis la configuration de l'outil
        agent_config = self.config.get("agent_config", {})
        research_agent = ResearchAgent(agent_config)

        try:
            # Appeler l'agent de recherche pour effectuer l'analyse
            analysis_result_obj = await research_agent.analyze_data(
                data=data,
                analysis_type=analysis_type
            )

            # Nettoyer les ressources de l'agent
            await research_agent._cleanup()

            if analysis_result_obj.get("status") != "success":
                return {
                    "status": "error",
                    "message": analysis_result_obj.get("message", "Unknown error during analysis")
                }

            analysis_result = analysis_result_obj.get("analysis", {})
        except Exception as e:
            self.logger.error(f"Error during data analysis: {str(e)}")
            return {
                "status": "error",
                "message": f"Error during data analysis: {str(e)}"
            }

        return {
            "status": "success",
            "analysis": analysis_result,
            "analysis_type": analysis_type,
            "data_size": len(json.dumps(data)),
            "timestamp": datetime.now().isoformat()
        }

    def get_input_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des entrées attendues par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des entrées
        """
        return {
            "type": "object",
            "properties": {
                "data": {
                    "type": "object",
                    "description": "Les données à analyser"
                },
                "analysis_type": {
                    "type": "string",
                    "description": "Le type d'analyse à effectuer",
                    "enum": ["general", "statistical", "sentiment", "trend"],
                    "default": "general"
                }
            },
            "required": ["data"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des sorties produites par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des sorties
        """
        return {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "description": "Statut de l'analyse",
                    "enum": ["success", "error"]
                },
                "analysis": {
                    "type": "object",
                    "description": "Les résultats de l'analyse"
                },
                "analysis_type": {
                    "type": "string",
                    "description": "Le type d'analyse effectuée"
                },
                "data_size": {
                    "type": "integer",
                    "description": "Taille des données analysées en caractères"
                },
                "timestamp": {
                    "type": "string",
                    "description": "Horodatage de l'analyse"
                },
                "message": {
                    "type": "string",
                    "description": "Message d'erreur en cas d'échec"
                }
            },
            "required": ["status"]
        }

    def _simulate_analysis(self, data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """
        Simule une analyse de données pour les tests.

        Args:
            data: Les données à analyser
            analysis_type: Le type d'analyse à effectuer

        Returns:
            Les résultats de l'analyse
        """
        if analysis_type == "general":
            # Analyse générale
            analysis = {
                "summary": "General analysis of the provided data",
                "data_types": {},
                "structure": {},
                "key_insights": []
            }

            # Analyser les types de données
            for key, value in data.items():
                if isinstance(value, dict):
                    analysis["data_types"][key] = "object"
                    analysis["structure"][key] = len(value)
                elif isinstance(value, list):
                    analysis["data_types"][key] = "array"
                    analysis["structure"][key] = len(value)
                elif isinstance(value, str):
                    analysis["data_types"][key] = "string"
                    analysis["structure"][key] = len(value)
                elif isinstance(value, (int, float)):
                    analysis["data_types"][key] = "number"
                    analysis["structure"][key] = value
                elif isinstance(value, bool):
                    analysis["data_types"][key] = "boolean"
                    analysis["structure"][key] = value
                else:
                    analysis["data_types"][key] = "unknown"

            # Générer quelques insights basiques
            if len(data) > 5:
                analysis["key_insights"].append("The data contains multiple fields")

            for key, value in data.items():
                if isinstance(value, list) and len(value) > 10:
                    analysis["key_insights"].append(f"The field '{key}' contains a large array")

            return analysis

        elif analysis_type == "statistical":
            # Analyse statistique
            analysis = {
                "summary": "Statistical analysis of the provided data",
                "numeric_fields": {},
                "correlations": []
            }

            # Analyser les champs numériques
            numeric_fields = {}
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    numeric_fields[key] = value
                elif isinstance(value, list) and all(isinstance(x, (int, float)) for x in value):
                    numeric_fields[key] = value

            # Calculer des statistiques basiques pour chaque champ numérique
            for key, value in numeric_fields.items():
                if isinstance(value, list):
                    analysis["numeric_fields"][key] = {
                        "min": min(value),
                        "max": max(value),
                        "avg": sum(value) / len(value),
                        "count": len(value)
                    }
                else:
                    analysis["numeric_fields"][key] = {
                        "value": value
                    }

            return analysis

        elif analysis_type == "sentiment":
            # Analyse de sentiment
            analysis = {
                "summary": "Sentiment analysis of the provided data",
                "text_fields": {},
                "overall_sentiment": "neutral"
            }

            # Analyser les champs textuels
            text_fields = {}
            for key, value in data.items():
                if isinstance(value, str) and len(value) > 10:
                    text_fields[key] = value
                elif isinstance(value, list) and all(isinstance(x, str) for x in value):
                    text_fields[key] = " ".join(value)

            # Simuler une analyse de sentiment basique
            positive_words = ["good", "great", "excellent", "positive", "happy", "satisfied"]
            negative_words = ["bad", "poor", "negative", "unhappy", "disappointed", "terrible"]

            for key, text in text_fields.items():
                text_lower = text.lower()
                positive_count = sum(text_lower.count(word) for word in positive_words)
                negative_count = sum(text_lower.count(word) for word in negative_words)

                if positive_count > negative_count:
                    sentiment = "positive"
                elif negative_count > positive_count:
                    sentiment = "negative"
                else:
                    sentiment = "neutral"

                analysis["text_fields"][key] = {
                    "sentiment": sentiment,
                    "positive_count": positive_count,
                    "negative_count": negative_count,
                    "text_length": len(text)
                }

            # Calculer le sentiment global
            sentiments = [field["sentiment"] for field in analysis["text_fields"].values()]
            positive_count = sentiments.count("positive")
            negative_count = sentiments.count("negative")

            if positive_count > negative_count:
                analysis["overall_sentiment"] = "positive"
            elif negative_count > positive_count:
                analysis["overall_sentiment"] = "negative"
            else:
                analysis["overall_sentiment"] = "neutral"

            return analysis

        elif analysis_type == "trend":
            # Analyse de tendance
            analysis = {
                "summary": "Trend analysis of the provided data",
                "time_series": {},
                "trends": []
            }

            # Vérifier si les données contiennent des séries temporelles
            time_series_data = {}

            # Chercher des champs qui pourraient être des séries temporelles
            for key, value in data.items():
                if isinstance(value, list) and len(value) > 2:
                    # Vérifier si chaque élément a une date/heure et une valeur
                    if all(isinstance(item, dict) and "date" in item and "value" in item for item in value):
                        time_series_data[key] = value

            # Analyser chaque série temporelle
            for key, series in time_series_data.items():
                # Trier par date
                sorted_series = sorted(series, key=lambda x: x["date"])

                # Extraire les valeurs
                values = [item["value"] for item in sorted_series]

                # Calculer la tendance (simplifiée)
                if len(values) >= 2:
                    first_value = values[0]
                    last_value = values[-1]

                    if last_value > first_value:
                        trend = "increasing"
                    elif last_value < first_value:
                        trend = "decreasing"
                    else:
                        trend = "stable"

                    # Calculer le changement en pourcentage
                    if first_value != 0:
                        percent_change = ((last_value - first_value) / abs(first_value)) * 100
                    else:
                        percent_change = 0

                    analysis["time_series"][key] = {
                        "trend": trend,
                        "start_value": first_value,
                        "end_value": last_value,
                        "percent_change": round(percent_change, 2),
                        "data_points": len(values)
                    }

                    analysis["trends"].append({
                        "series": key,
                        "trend": trend,
                        "percent_change": round(percent_change, 2)
                    })

            return analysis

        # Type d'analyse non reconnu
        return {
            "summary": f"Analysis type '{analysis_type}' not recognized",
            "available_types": ["general", "statistical", "sentiment", "trend"]
        }
