from typing import Dict, Any, Optional, List
import logging
import json
import re
from .base_tool import BaseTool

class CodeGenerationTool(BaseTool):
    """
    Outil pour générer du code basé sur des exigences.
    """

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(
            name="code_generation",
            description="Génère du code basé sur des exigences spécifiées",
            config=config
        )

    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Génère du code basé sur les exigences fournies.

        Args:
            inputs: Les entrées pour l'outil, incluant:
                - language: Le langage de programmation
                - requirements: Les exigences pour le code
                - context: Le contexte pour la génération

        Returns:
            Les résultats de la génération de code
        """
        self.logger.info("Executing code generation tool")

        # Valider les entrées
        language = inputs.get("language", "python")
        requirements = inputs.get("requirements", [])
        context = inputs.get("context", {})

        if not requirements:
            return {
                "status": "error",
                "message": "No requirements provided for code generation"
            }

        # Préparer les paramètres pour l'agent de code
        agent_message = {
            "action": "generate_code",
            "language": language,
            "requirements": requirements,
            "context": context
        }

        # Initialiser l'agent de code
        from superagent.agents.coder_agent.coder_agent import CoderAgent

        # Récupérer la configuration de l'agent depuis la configuration de l'outil
        agent_config = self.config.get("agent_config", {})
        coder_agent = CoderAgent(agent_config)

        try:
            # Appeler l'agent de code pour générer le code
            code_result = await coder_agent.generate_code(
                language=language,
                requirements=requirements,
                context=context
            )

            # Nettoyer les ressources de l'agent
            await coder_agent._cleanup()

            if code_result.get("status") != "success":
                return {
                    "status": "error",
                    "message": code_result.get("message", "Unknown error during code generation")
                }

            code = code_result.get("code", "")
        except Exception as e:
            self.logger.error(f"Error during code generation: {str(e)}")
            return {
                "status": "error",
                "message": f"Error during code generation: {str(e)}"
            }

        return {
            "status": "success",
            "code": code,
            "language": language,
            "requirements": requirements
        }

    def get_input_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des entrées attendues par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des entrées
        """
        return {
            "type": "object",
            "properties": {
                "language": {
                    "type": "string",
                    "description": "Le langage de programmation pour la génération de code",
                    "enum": ["python", "javascript", "typescript", "java", "c#", "go"]
                },
                "requirements": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Liste des exigences pour le code à générer"
                },
                "context": {
                    "type": "object",
                    "description": "Contexte supplémentaire pour la génération de code"
                }
            },
            "required": ["requirements"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des sorties produites par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des sorties
        """
        return {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "description": "Statut de la génération de code",
                    "enum": ["success", "error"]
                },
                "code": {
                    "type": "string",
                    "description": "Le code généré"
                },
                "language": {
                    "type": "string",
                    "description": "Le langage de programmation du code généré"
                },
                "requirements": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Les exigences utilisées pour la génération"
                },
                "message": {
                    "type": "string",
                    "description": "Message d'erreur en cas d'échec"
                }
            },
            "required": ["status"]
        }

    def _generate_sample_code(self, language: str, requirements: List[str], context: Dict[str, Any]) -> str:
        """
        Génère un exemple de code pour les tests.

        Args:
            language: Le langage de programmation
            requirements: Les exigences pour le code
            context: Le contexte pour la génération

        Returns:
            Le code généré
        """
        description = context.get("description", "Generated code")

        if language == "python":
            return f"""# {description}
# Requirements: {', '.join(requirements)}

def main():
    \"\"\"Main function to implement the requirements\"\"\"
    print("Implementing: {', '.join(requirements)}")

    # TODO: Implement the requirements

    return "Implementation complete"

if __name__ == "__main__":
    result = main()
    print(result)
"""
        elif language == "javascript":
            return f"""// {description}
// Requirements: {', '.join(requirements)}

function main() {{
    // Main function to implement the requirements
    console.log("Implementing: {', '.join(requirements)}");

    // TODO: Implement the requirements

    return "Implementation complete";
}}

// Execute the main function
const result = main();
console.log(result);
"""
        elif language == "typescript":
            return f"""// {description}
// Requirements: {', '.join(requirements)}

function main(): string {{
    // Main function to implement the requirements
    console.log("Implementing: {', '.join(requirements)}");

    // TODO: Implement the requirements

    return "Implementation complete";
}}

// Execute the main function
const result = main();
console.log(result);
"""
        else:
            return f"// Code generation for {language} not implemented yet\n// Requirements: {', '.join(requirements)}"


class CodeAnalysisTool(BaseTool):
    """
    Outil pour analyser du code existant.
    """

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(
            name="code_analysis",
            description="Analyse du code existant pour identifier des problèmes et des opportunités d'amélioration",
            config=config
        )

    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyse le code fourni.

        Args:
            inputs: Les entrées pour l'outil, incluant:
                - code: Le code à analyser
                - language: Le langage de programmation
                - analysis_type: Le type d'analyse à effectuer

        Returns:
            Les résultats de l'analyse de code
        """
        self.logger.info("Executing code analysis tool")

        # Valider les entrées
        code = inputs.get("code", "")
        language = inputs.get("language", "python")
        analysis_type = inputs.get("analysis_type", "general")

        if not code:
            return {
                "status": "error",
                "message": "No code provided for analysis"
            }

        # Préparer les paramètres pour l'agent de code
        agent_message = {
            "action": "analyze_code",
            "code": code,
            "language": language
        }

        # Initialiser l'agent de code
        from superagent.agents.coder_agent.coder_agent import CoderAgent

        # Récupérer la configuration de l'agent depuis la configuration de l'outil
        agent_config = self.config.get("agent_config", {})
        coder_agent = CoderAgent(agent_config)

        try:
            # Appeler l'agent de code pour analyser le code
            analysis_result = await coder_agent.analyze_code(
                code=code,
                language=language
            )

            # Nettoyer les ressources de l'agent
            await coder_agent._cleanup()

            if analysis_result.get("status") != "success":
                return {
                    "status": "error",
                    "message": analysis_result.get("message", "Unknown error during code analysis")
                }

            analysis = analysis_result.get("analysis", {})
        except Exception as e:
            self.logger.error(f"Error during code analysis: {str(e)}")
            return {
                "status": "error",
                "message": f"Error during code analysis: {str(e)}"
            }

        return {
            "status": "success",
            "analysis": analysis,
            "language": language,
            "code_length": len(code.split("\n"))
        }

    def get_input_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des entrées attendues par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des entrées
        """
        return {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "description": "Le code à analyser"
                },
                "language": {
                    "type": "string",
                    "description": "Le langage de programmation du code",
                    "enum": ["python", "javascript", "typescript", "java", "c#", "go"]
                },
                "analysis_type": {
                    "type": "string",
                    "description": "Le type d'analyse à effectuer",
                    "enum": ["general", "security", "performance", "maintainability"]
                }
            },
            "required": ["code"]
        }

    def get_output_schema(self) -> Dict[str, Any]:
        """
        Récupère le schéma des sorties produites par l'outil.

        Returns:
            Un dictionnaire décrivant le schéma des sorties
        """
        return {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "description": "Statut de l'analyse de code",
                    "enum": ["success", "error"]
                },
                "analysis": {
                    "type": "object",
                    "description": "Les résultats de l'analyse"
                },
                "language": {
                    "type": "string",
                    "description": "Le langage de programmation du code analysé"
                },
                "code_length": {
                    "type": "integer",
                    "description": "Le nombre de lignes de code analysées"
                },
                "message": {
                    "type": "string",
                    "description": "Message d'erreur en cas d'échec"
                }
            },
            "required": ["status"]
        }

    def _analyze_sample_code(self, code: str, language: str, analysis_type: str) -> Dict[str, Any]:
        """
        Analyse un exemple de code pour les tests.

        Args:
            code: Le code à analyser
            language: Le langage de programmation
            analysis_type: Le type d'analyse à effectuer

        Returns:
            Les résultats de l'analyse
        """
        # Analyse basique du code
        lines = code.split("\n")
        line_count = len(lines)

        # Compter les commentaires
        comment_markers = {
            "python": ["#"],
            "javascript": ["//", "/*"],
            "typescript": ["//", "/*"],
            "java": ["//", "/*"],
            "c#": ["//", "/*"],
            "go": ["//"]
        }

        markers = comment_markers.get(language, ["#", "//"])
        comment_count = sum(1 for line in lines if any(line.strip().startswith(marker) for marker in markers))

        # Compter les fonctions/méthodes
        function_patterns = {
            "python": r"def\s+\w+\s*\(",
            "javascript": r"function\s+\w+\s*\(|const\s+\w+\s*=\s*\([^)]*\)\s*=>",
            "typescript": r"function\s+\w+\s*\(|const\s+\w+\s*=\s*\([^)]*\)\s*=>",
            "java": r"(public|private|protected)?\s+\w+\s+\w+\s*\(",
            "c#": r"(public|private|protected)?\s+\w+\s+\w+\s*\(",
            "go": r"func\s+\w+\s*\("
        }

        pattern = function_patterns.get(language, r"def\s+\w+\s*\(|function\s+\w+\s*\(")
        function_count = len(re.findall(pattern, code))

        # Analyse spécifique au type
        if analysis_type == "security":
            # Rechercher des problèmes de sécurité courants
            security_issues = []

            # Rechercher des mots de passe en dur
            if re.search(r"password\s*=\s*['\"][^'\"]+['\"]", code, re.IGNORECASE):
                security_issues.append({
                    "type": "hardcoded_credentials",
                    "message": "Hardcoded password detected",
                    "severity": "high"
                })

            # Rechercher des fonctions dangereuses
            dangerous_functions = {
                "python": ["eval", "exec", "os.system", "subprocess.call"],
                "javascript": ["eval", "Function", "setTimeout", "setInterval"],
                "typescript": ["eval", "Function", "setTimeout", "setInterval"],
                "java": ["Runtime.exec", "ProcessBuilder"],
                "c#": ["Process.Start", "Eval"],
                "go": ["exec.Command"]
            }

            for func in dangerous_functions.get(language, []):
                if func in code:
                    security_issues.append({
                        "type": "dangerous_function",
                        "message": f"Potentially dangerous function: {func}",
                        "severity": "medium"
                    })

            return {
                "line_count": line_count,
                "comment_count": comment_count,
                "function_count": function_count,
                "security_issues": security_issues,
                "security_score": 10 - len(security_issues) * 2  # Score sur 10
            }

        elif analysis_type == "performance":
            # Rechercher des problèmes de performance courants
            performance_issues = []

            # Rechercher des boucles imbriquées
            if re.search(r"for\s+.*\s+for\s+", code):
                performance_issues.append({
                    "type": "nested_loops",
                    "message": "Nested loops detected",
                    "severity": "medium"
                })

            # Rechercher des opérations coûteuses
            expensive_operations = {
                "python": [".sort(", ".copy(", "deepcopy("],
                "javascript": [".sort(", ".filter(", ".map(", ".reduce("],
                "typescript": [".sort(", ".filter(", ".map(", ".reduce("],
                "java": [".sort(", ".stream(", ".collect("],
                "c#": [".OrderBy(", ".Select(", ".Where("],
                "go": [".Sort(", "append(", "copy("]
            }

            for op in expensive_operations.get(language, []):
                if op in code:
                    performance_issues.append({
                        "type": "expensive_operation",
                        "message": f"Potentially expensive operation: {op}",
                        "severity": "low"
                    })

            return {
                "line_count": line_count,
                "comment_count": comment_count,
                "function_count": function_count,
                "performance_issues": performance_issues,
                "performance_score": 10 - len(performance_issues)  # Score sur 10
            }

        else:  # Analyse générale
            # Calculer la complexité
            complexity = 1.0

            # Facteurs de complexité basiques
            if_count = code.count("if ")
            for_count = code.count("for ")
            while_count = code.count("while ")

            complexity += (if_count * 0.1) + (for_count * 0.2) + (while_count * 0.3)

            # Rechercher des problèmes généraux
            issues = []

            # Rechercher des TODOs
            todo_count = len(re.findall(r"TODO|FIXME", code, re.IGNORECASE))
            if todo_count > 0:
                issues.append({
                    "type": "todo",
                    "message": f"{todo_count} TODO/FIXME comments found",
                    "severity": "low"
                })

            # Rechercher des lignes longues
            long_lines = [i+1 for i, line in enumerate(lines) if len(line) > 100]
            if long_lines:
                issues.append({
                    "type": "long_lines",
                    "message": f"{len(long_lines)} lines exceed 100 characters",
                    "severity": "low",
                    "lines": long_lines[:5]  # Limiter à 5 lignes pour l'exemple
                })

            return {
                "line_count": line_count,
                "comment_count": comment_count,
                "comment_ratio": round(comment_count / line_count, 2) if line_count > 0 else 0,
                "function_count": function_count,
                "complexity": round(complexity, 2),
                "issues": issues,
                "quality_score": 10 - min(len(issues), 5)  # Score sur 10, limité à -5 pour les problèmes
            }
