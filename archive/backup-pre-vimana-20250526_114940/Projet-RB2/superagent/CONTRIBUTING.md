# Guide de contribution - IA de Retreat And Be

Merci de votre intérêt pour contribuer à l'IA de Retreat And Be ! Ce document fournit des lignes directrices pour contribuer au projet.

## Code de conduite

En participant à ce projet, vous vous engagez à maintenir un environnement respectueux et collaboratif. Nous attendons de tous les contributeurs qu'ils respectent les opinions, les idées et les expériences de chacun.

## Comment contribuer

### Signaler des bugs

Si vous trouvez un bug, veuillez créer une issue sur GitHub avec les informations suivantes :
- Un titre clair et descriptif
- Une description détaillée du problème
- Les étapes pour reproduire le bug
- Le comportement attendu et le comportement observé
- Des captures d'écran si applicable
- Votre environnement (OS, version de Python, etc.)

### Suggérer des améliorations

Pour suggérer une amélioration, créez une issue sur GitHub avec les informations suivantes :
- Un titre clair et descriptif
- Une description détaillée de l'amélioration proposée
- Les avantages de cette amélioration
- Des exemples d'utilisation si applicable

### Pull Requests

1. Forkez le dépôt
2. Créez une branche pour votre fonctionnalité (`git checkout -b feature/amazing-feature`)
3. Committez vos changements (`git commit -m 'Add some amazing feature'`)
4. Poussez vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrez une Pull Request

### Conventions de code

- Suivez les conventions PEP 8 pour le code Python
- Utilisez des noms de variables et de fonctions descriptifs
- Commentez votre code lorsque nécessaire
- Écrivez des docstrings pour toutes les fonctions, classes et modules
- Ajoutez des tests unitaires pour les nouvelles fonctionnalités

## Structure du projet

```
retreatandbe-ai/
├── src/                    # Code source principal
│   ├── orchestrator/       # Orchestrateur d'IA
│   ├── recommender/        # Système de recommandation
│   ├── content/            # Générateur de contenu
│   ├── chatbot/            # Assistant conversationnel
│   ├── analytics/          # Moteur d'analyse
│   └── optimizer/          # Moteur d'optimisation
├── docs/                   # Documentation
├── tests/                  # Tests unitaires et d'intégration
├── config/                 # Fichiers de configuration
├── examples/               # Exemples d'utilisation
└── scripts/                # Scripts utilitaires
```

## Développement

### Installation de l'environnement de développement

```bash
# Cloner le dépôt
git clone https://github.com/retreatandbe/ai-layer.git
cd ai-layer

# Créer un environnement virtuel
python -m venv venv
source venv/bin/activate  # Sur Windows: venv\Scripts\activate

# Installer les dépendances de développement
pip install -r requirements-dev.txt
```

### Exécuter les tests

```bash
# Exécuter tous les tests
pytest

# Exécuter les tests avec couverture
pytest --cov=src

# Exécuter les tests pour un module spécifique
pytest tests/test_recommender.py
```

### Linting

```bash
# Vérifier le style du code
flake8 src tests

# Formater le code automatiquement
black src tests
```

## Processus de release

1. Mettre à jour le numéro de version dans `src/orchestrator/main.py`
2. Mettre à jour le CHANGELOG.md
3. Créer un tag Git pour la nouvelle version
4. Pousser les changements et le tag vers le dépôt
5. Créer une nouvelle release sur GitHub

## Licence

En contribuant à ce projet, vous acceptez que vos contributions soient sous la même licence que le projet (voir le fichier LICENSE).

## Contact

Si vous avez des questions ou besoin d'aide, n'hésitez pas à contacter l'équipe de développement à <EMAIL>.
