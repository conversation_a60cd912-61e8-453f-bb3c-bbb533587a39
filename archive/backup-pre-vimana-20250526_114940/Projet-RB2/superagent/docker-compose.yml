version: '3.8'

services:
  ai-orchestrator:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./:/app
      - ./models:/app/models
      - ./logs:/app/logs
    env_file:
      - .env
    depends_on:
      - redis
      - postgres
    networks:
      - retreatandbe-network

  virtual-coach-api:
    build: ./ai_engine/virtual_coach
    ports:
      - "5005:5005"
    volumes:
      - ./ai_engine/virtual_coach:/app
    env_file:
      - .env
    depends_on:
      - redis
    networks:
      - retreatandbe-network

  ethics-security-api:
    build: ./ai_engine/ethics_security
    ports:
      - "8008:8008"
    volumes:
      - ./ai_engine/ethics_security:/app
    env_file:
      - .env
    depends_on:
      - mongodb
      - redis
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - DATABASE_NAME=ethics_security
      - SECRET_KEY=${ETHICS_SECRET_KEY:-changez_moi_en_production}
      - RECOMMENDER_API_URL=http://ai-orchestrator:8000
      - VIRTUAL_COACH_API_URL=http://virtual-coach-api:5005
    networks:
      - retreatandbe-network

  chatbot-api:
    build: ./ai_engine/chatbot
    ports:
      - "8001:8001"
    volumes:
      - ./ai_engine/chatbot:/app
    env_file:
      - .env
    depends_on:
      - redis
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
    networks:
      - retreatandbe-network

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - retreatandbe-network

  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb-data:/data/db
    networks:
      - retreatandbe-network

  postgres:
    image: postgres:13-alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
      POSTGRES_DB: ${DB_NAME:-retreatandbe}
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - retreatandbe-network

  prometheus:
    image: prom/prometheus:v2.30.0
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - retreatandbe-network

  grafana:
    image: grafana/grafana:8.1.0
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - retreatandbe-network

networks:
  retreatandbe-network:
    driver: bridge

volumes:
  redis-data:
  postgres-data:
  mongodb-data:
  prometheus-data:
  grafana-data:
