import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardActions, 
  Typography, 
  Button, 
  Chip,
  Box,
  LinearProgress,
  Collapse,
  IconButton
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import { workflowService } from '../services/api';

const ExpandMore = styled((props) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme, expand }) => ({
  transform: !expand ? 'rotate(0deg)' : 'rotate(180deg)',
  marginLeft: 'auto',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
}));

const WorkflowStatusCard = ({ workflow, onRefresh }) => {
  const navigate = useNavigate();
  const [expanded, setExpanded] = useState(false);
  const [status, setStatus] = useState(workflow.status);
  const [outputs, setOutputs] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  const handleViewDetails = () => {
    navigate(`/workflows/${workflow.workflow_id}`);
  };

  const refreshStatus = async () => {
    if (loading) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await workflowService.getWorkflowStatus(workflow.workflow_id);
      setStatus(result.status);
      setOutputs(result.outputs);
      
      if (onRefresh) {
        onRefresh();
      }
    } catch (err) {
      setError('Failed to refresh workflow status');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Update status from props when it changes
    setStatus(workflow.status);
  }, [workflow.status]);

  // Status color mapping
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'running':
        return 'primary';
      default:
        return 'default';
    }
  };

  return (
    <Card sx={{ minWidth: 275, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {loading && <LinearProgress />}
      
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h6" component="div" gutterBottom>
          Workflow {workflow.workflow_id.substring(0, 8)}...
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1, mb: 2 }}>
          <Chip 
            label={status} 
            size="small" 
            color={getStatusColor(status)} 
          />
          {workflow.graph_name && (
            <Chip 
              label={workflow.graph_name} 
              size="small" 
              variant="outlined" 
            />
          )}
        </Box>
        
        {workflow.current_node && (
          <Typography variant="body2" color="text.secondary">
            Current node: {workflow.current_node}
          </Typography>
        )}
        
        {error && (
          <Typography variant="body2" color="error" sx={{ mt: 1 }}>
            {error}
          </Typography>
        )}
      </CardContent>
      
      <CardActions disableSpacing>
        <Button size="small" onClick={handleViewDetails}>View Details</Button>
        <Button 
          size="small" 
          color="primary" 
          onClick={refreshStatus}
          disabled={loading || status === 'completed' || status === 'failed'}
        >
          Refresh
        </Button>
        
        <ExpandMore
          expand={expanded}
          onClick={handleExpandClick}
          aria-expanded={expanded}
          aria-label="show more"
        >
          <ExpandMoreIcon />
        </ExpandMore>
      </CardActions>
      
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <CardContent>
          <Typography variant="subtitle2">Workflow Details:</Typography>
          
          {outputs ? (
            <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1, maxHeight: 200, overflow: 'auto' }}>
              <pre style={{ margin: 0, fontSize: '0.75rem' }}>
                {JSON.stringify(outputs, null, 2)}
              </pre>
            </Box>
          ) : (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              No output data available yet.
            </Typography>
          )}
        </CardContent>
      </Collapse>
    </Card>
  );
};

export default WorkflowStatusCard;
