import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import authService from '../services/auth';

const PrivateRoute = ({ children, requiredRole = null, requiredSubscription = null }) => {
  const location = useLocation();
  const isAuthenticated = authService.isAuthenticated();
  
  // Vérifier si l'utilisateur est authentifié
  if (!isAuthenticated) {
    // Rediriger vers la page de connexion avec l'URL actuelle comme destination après la connexion
    return <Navigate to="/login" state={{ from: location }} replace />;
  }
  
  // Vérifier si un rôle spécifique est requis
  if (requiredRole && !authService.hasRole(requiredRole)) {
    // Rediriger vers une page d'accès refusé
    return <Navigate to="/access-denied" state={{ from: location }} replace />;
  }
  
  // Vérifier si un niveau d'abonnement spécifique est requis
  if (requiredSubscription && !authService.hasSubscriptionLevel(requiredSubscription)) {
    // Rediriger vers une page d'abonnement
    return <Navigate to="/subscription" state={{ from: location }} replace />;
  }
  
  // Si toutes les vérifications sont passées, afficher le composant enfant
  return children;
};

export default PrivateRoute;
