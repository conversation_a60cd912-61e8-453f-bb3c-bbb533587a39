import React from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardA<PERSON>, 
  Typography, 
  Button, 
  Chip,
  Box
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

const WorkflowTemplateCard = ({ template }) => {
  const navigate = useNavigate();

  const handleStartWorkflow = () => {
    navigate(`/workflows/create/${template.template_id}`);
  };

  const handleViewDetails = () => {
    navigate(`/workflows/templates/${template.template_id}`);
  };

  return (
    <Card sx={{ minWidth: 275, height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h5" component="div" gutterBottom>
          {template.name}
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          {template.description}
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 2 }}>
          <Chip 
            label={`${template.node_count} nodes`} 
            size="small" 
            color="primary" 
            variant="outlined" 
          />
          <Chip 
            label={`${template.edge_count} edges`} 
            size="small" 
            color="secondary" 
            variant="outlined" 
          />
        </Box>
      </CardContent>
      <CardActions>
        <Button size="small" onClick={handleViewDetails}>View Details</Button>
        <Button size="small" color="primary" onClick={handleStartWorkflow}>
          Start Workflow
        </Button>
      </CardActions>
    </Card>
  );
};

export default WorkflowTemplateCard;
