import api from './api';

// Service pour l'authentification
const authService = {
  // Connexion d'un utilisateur
  login: async (username, password) => {
    try {
      const response = await api.post('/auth/login', {
        username,
        password,
      });
      
      // Stocker le token dans le localStorage
      if (response.data.access_token) {
        localStorage.setItem('token', response.data.access_token);
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }
      
      return response.data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  // Déconnexion d'un utilisateur
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  // Récupérer l'utilisateur courant
  getCurrentUser: async () => {
    try {
      const token = localStorage.getItem('token');
      
      if (!token) {
        return null;
      }
      
      // Configurer le header d'autorisation
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      const response = await api.get('/auth/me');
      return response.data;
    } catch (error) {
      console.error('Get current user error:', error);
      
      // Si le token est invalide, déconnecter l'utilisateur
      if (error.response && error.response.status === 401) {
        authService.logout();
      }
      
      return null;
    }
  },

  // Vérifier si l'utilisateur est connecté
  isAuthenticated: () => {
    return !!localStorage.getItem('token');
  },

  // Récupérer le token
  getToken: () => {
    return localStorage.getItem('token');
  },

  // Récupérer l'utilisateur stocké localement
  getUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },

  // Vérifier si l'utilisateur a un rôle spécifique
  hasRole: (role) => {
    const user = authService.getUser();
    return user && user.role === role;
  },

  // Vérifier si l'utilisateur a un niveau d'abonnement spécifique
  hasSubscriptionLevel: (level) => {
    const user = authService.getUser();
    return user && user.subscription_level === level;
  },

  // Vérifier si l'utilisateur a une permission spécifique
  hasPermission: (resource, action) => {
    const user = authService.getUser();
    
    if (!user || !user.permissions) {
      return false;
    }
    
    return user.permissions[resource] && user.permissions[resource][action];
  },
};

// Configurer l'intercepteur pour ajouter le token à toutes les requêtes
api.interceptors.request.use(
  (config) => {
    const token = authService.getToken();
    
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Configurer l'intercepteur pour gérer les erreurs d'authentification
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      // Déconnecter l'utilisateur si le token est invalide
      authService.logout();
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

export default authService;
