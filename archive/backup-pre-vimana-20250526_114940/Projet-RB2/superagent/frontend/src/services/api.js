import axios from 'axios';

// Configuration de l'API
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Instance Axios avec configuration de base
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Service pour les workflows
const workflowService = {
  // Récupérer tous les templates de workflow
  getWorkflowTemplates: async () => {
    try {
      const response = await api.get('/workflows/templates');
      return response.data;
    } catch (error) {
      console.error('Error fetching workflow templates:', error);
      throw error;
    }
  },

  // Récupérer un template de workflow spécifique
  getWorkflowTemplate: async (templateId) => {
    try {
      const response = await api.get(`/workflows/templates/${templateId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching workflow template ${templateId}:`, error);
      throw error;
    }
  },

  // Créer un nouveau workflow
  createWorkflow: async (workflowType, inputData) => {
    try {
      const response = await api.post('/workflows/create', {
        workflow_type: workflowType,
        input_data: inputData,
      });
      return response.data;
    } catch (error) {
      console.error('Error creating workflow:', error);
      throw error;
    }
  },

  // Récupérer le statut d'un workflow
  getWorkflowStatus: async (workflowId) => {
    try {
      const response = await api.get(`/workflows/status/${workflowId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching workflow status ${workflowId}:`, error);
      throw error;
    }
  },

  // Récupérer tous les workflows
  getWorkflows: async () => {
    try {
      const response = await api.get('/workflows/list');
      return response.data;
    } catch (error) {
      console.error('Error fetching workflows:', error);
      throw error;
    }
  },
};

export { workflowService };
export default api;
