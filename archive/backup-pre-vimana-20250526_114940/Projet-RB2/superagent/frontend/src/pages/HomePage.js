import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Grid, 
  Paper, 
  Box, 
  Button,
  CircularProgress,
  Alert
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { workflowService } from '../services/api';
import WorkflowTemplateCard from '../components/WorkflowTemplateCard';
import WorkflowStatusCard from '../components/WorkflowStatusCard';

const HomePage = () => {
  const navigate = useNavigate();
  const [templates, setTemplates] = useState([]);
  const [recentWorkflows, setRecentWorkflows] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch templates and workflows in parallel
      const [templatesResponse, workflowsResponse] = await Promise.all([
        workflowService.getWorkflowTemplates(),
        workflowService.getWorkflows()
      ]);
      
      setTemplates(templatesResponse.templates);
      
      // Sort workflows by creation time (newest first) and take the first 5
      const sortedWorkflows = workflowsResponse.workflows
        .sort((a, b) => new Date(b.created_at || 0) - new Date(a.created_at || 0))
        .slice(0, 5);
        
      setRecentWorkflows(sortedWorkflows);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleRefresh = () => {
    fetchData();
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Retreat And Be AI Workflows
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h5" component="h2">
                Available Workflow Templates
              </Typography>
              <Button 
                variant="outlined" 
                onClick={() => navigate('/workflows/templates')}
              >
                View All Templates
              </Button>
            </Box>
            
            <Grid container spacing={3}>
              {templates.slice(0, 3).map((template) => (
                <Grid item xs={12} sm={6} md={4} key={template.template_id}>
                  <WorkflowTemplateCard template={template} />
                </Grid>
              ))}
              
              {templates.length === 0 && (
                <Grid item xs={12}>
                  <Typography variant="body1" color="text.secondary" align="center">
                    No workflow templates available.
                  </Typography>
                </Grid>
              )}
            </Grid>
          </Paper>
          
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h5" component="h2">
                Recent Workflows
              </Typography>
              <Box>
                <Button 
                  variant="outlined" 
                  onClick={handleRefresh}
                  sx={{ mr: 1 }}
                >
                  Refresh
                </Button>
                <Button 
                  variant="outlined" 
                  onClick={() => navigate('/workflows')}
                >
                  View All Workflows
                </Button>
              </Box>
            </Box>
            
            <Grid container spacing={3}>
              {recentWorkflows.map((workflow) => (
                <Grid item xs={12} sm={6} md={4} key={workflow.workflow_id}>
                  <WorkflowStatusCard 
                    workflow={workflow} 
                    onRefresh={handleRefresh}
                  />
                </Grid>
              ))}
              
              {recentWorkflows.length === 0 && (
                <Grid item xs={12}>
                  <Typography variant="body1" color="text.secondary" align="center">
                    No workflows have been created yet.
                  </Typography>
                </Grid>
              )}
            </Grid>
          </Paper>
        </>
      )}
    </Container>
  );
};

export default HomePage;
