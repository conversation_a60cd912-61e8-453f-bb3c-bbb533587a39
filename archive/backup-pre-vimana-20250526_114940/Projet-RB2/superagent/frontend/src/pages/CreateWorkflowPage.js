import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Paper, 
  Box, 
  Button,
  TextField,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Divider
} from '@mui/material';
import { useParams, useNavigate } from 'react-router-dom';
import { workflowService } from '../services/api';

// Workflow type to display name and image mapping
const workflowTypeInfo = {
  retreat_search: {
    name: 'Retreat Search',
    description: 'Search for retreats based on criteria',
    image: '/images/retreat_search.jpg',
    defaultInputs: {
      search_query: 'yoga retreats in Bali',
      filters: {
        duration: '1 week',
        price_range: { min: 500, max: 2000 },
        activities: ['yoga', 'meditation', 'wellness'],
        accommodation_type: 'resort'
      },
      sort_by: 'price',
      max_results: 10
    }
  },
  partner_matching: {
    name: 'Partner Matching',
    description: 'Find matching partners for retreat creation',
    image: '/images/partner_matching.jpg',
    defaultInputs: {
      user_profile: {
        id: 'user123',
        name: '<PERSON>',
        location: 'Paris, France',
        expertise: ['yoga', 'meditation'],
        experience_years: 5,
        languages: ['English', 'French'],
        availability: ['weekends', 'evenings'],
        preferred_retreat_types: ['wellness', 'yoga'],
        preferred_locations: ['Bali', 'Thailand', 'France'],
        preferred_duration: '1-2 weeks',
        subscription_level: 'Premium Partner'
      },
      matching_criteria: {
        location_importance: 0.8,
        expertise_importance: 0.9,
        experience_importance: 0.7,
        language_importance: 0.6,
        availability_importance: 0.8
      },
      max_matches: 5
    }
  },
  content_generation: {
    name: 'Content Generation',
    description: 'Generate content for retreat descriptions',
    image: '/images/content_generation.jpg',
    defaultInputs: {
      retreat_type: 'yoga',
      target_audience: 'beginners',
      key_features: ['ocean view', 'daily yoga', 'organic food', 'meditation'],
      tone: 'peaceful',
      length: 'medium',
      include_images: true
    }
  },
  customer_support: {
    name: 'Customer Support',
    description: 'Generate responses for customer inquiries',
    image: '/images/customer_support.jpg',
    defaultInputs: {
      customer_query: 'I need help finding a yoga retreat in Bali for next month',
      customer_info: {
        previous_retreats: 2,
        preferences: ['yoga', 'meditation'],
        budget_range: 'medium'
      },
      response_type: 'helpful',
      include_recommendations: true
    }
  },
  retreat_planning: {
    name: 'Retreat Planning',
    description: 'Plan a complete retreat itinerary',
    image: '/images/retreat_planning.jpg',
    defaultInputs: {
      location: 'Bali, Indonesia',
      duration: 7,
      theme: 'Yoga and Wellness',
      group_size: 12,
      budget_per_person: 1500,
      activities: ['yoga', 'meditation', 'spa', 'hiking', 'cooking class'],
      accommodation_preferences: ['private rooms', 'pool', 'ocean view'],
      meal_preferences: ['vegetarian', 'vegan options', 'local cuisine']
    }
  }
};

const CreateWorkflowPage = () => {
  const { templateId } = useParams();
  const navigate = useNavigate();
  
  const [activeStep, setActiveStep] = useState(0);
  const [template, setTemplate] = useState(null);
  const [inputData, setInputData] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [workflowId, setWorkflowId] = useState(null);

  useEffect(() => {
    const fetchTemplate = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const result = await workflowService.getWorkflowTemplate(templateId);
        setTemplate(result);
        
        // Set default input data based on template type
        const defaultInputs = workflowTypeInfo[templateId]?.defaultInputs || {};
        setInputData(JSON.stringify(defaultInputs, null, 2));
      } catch (err) {
        console.error('Error fetching template:', err);
        setError('Failed to load workflow template. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (templateId) {
      fetchTemplate();
    }
  }, [templateId]);

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleInputChange = (event) => {
    setInputData(event.target.value);
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    setError(null);
    
    try {
      // Parse the input data
      const parsedInputData = JSON.parse(inputData);
      
      // Create the workflow
      const result = await workflowService.createWorkflow(templateId, parsedInputData);
      
      setWorkflowId(result.workflow_id);
      handleNext();
    } catch (err) {
      console.error('Error creating workflow:', err);
      
      if (err instanceof SyntaxError) {
        setError('Invalid JSON format. Please check your input data.');
      } else {
        setError('Failed to create workflow. Please try again later.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleViewWorkflow = () => {
    navigate(`/workflows/${workflowId}`);
  };

  const handleViewAllWorkflows = () => {
    navigate('/workflows');
  };

  // Steps for the workflow creation process
  const steps = ['Select Template', 'Configure Input Data', 'Create Workflow'];

  // Render the current step content
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Selected Template: {workflowTypeInfo[templateId]?.name || templateId}
            </Typography>
            
            <Card sx={{ display: 'flex', mb: 3 }}>
              <CardMedia
                component="img"
                sx={{ width: 200, display: { xs: 'none', sm: 'block' } }}
                image={workflowTypeInfo[templateId]?.image || '/images/default.jpg'}
                alt={workflowTypeInfo[templateId]?.name || templateId}
              />
              <CardContent sx={{ flex: 1 }}>
                <Typography component="h2" variant="h5">
                  {workflowTypeInfo[templateId]?.name || templateId}
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  {workflowTypeInfo[templateId]?.description || 'No description available'}
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Typography variant="body2" paragraph>
                  This workflow contains {template?.nodes?.length || 0} nodes and {template?.edges?.length || 0} edges.
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Entry point: {template?.entry_point || 'N/A'}
                </Typography>
              </CardContent>
            </Card>
            
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
              <Button
                variant="contained"
                onClick={handleNext}
              >
                Next
              </Button>
            </Box>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Configure Input Data
            </Typography>
            <Typography variant="body2" paragraph>
              Provide the input data for the workflow in JSON format.
            </Typography>
            
            <TextField
              label="Input Data (JSON)"
              multiline
              fullWidth
              rows={12}
              value={inputData}
              onChange={handleInputChange}
              variant="outlined"
              sx={{ mb: 3 }}
              error={!!error && error.includes('JSON')}
              helperText={error && error.includes('JSON') ? error : ''}
            />
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button onClick={handleBack}>
                Back
              </Button>
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={submitting}
              >
                {submitting ? <CircularProgress size={24} /> : 'Create Workflow'}
              </Button>
            </Box>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Alert severity="success" sx={{ mb: 3 }}>
              Workflow created successfully!
            </Alert>
            
            <Typography variant="h6" gutterBottom>
              Workflow ID: {workflowId}
            </Typography>
            
            <Typography variant="body1" paragraph>
              Your workflow has been created and is now running. You can view the status and results of your workflow.
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3, gap: 2 }}>
              <Button
                variant="outlined"
                onClick={handleViewAllWorkflows}
              >
                View All Workflows
              </Button>
              <Button
                variant="contained"
                onClick={handleViewWorkflow}
              >
                View This Workflow
              </Button>
            </Box>
          </Box>
        );
      default:
        return 'Unknown step';
    }
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Create New Workflow
      </Typography>
      
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      
      {error && !error.includes('JSON') && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ p: 3 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          getStepContent(activeStep)
        )}
      </Paper>
    </Container>
  );
};

export default CreateWorkflowPage;
