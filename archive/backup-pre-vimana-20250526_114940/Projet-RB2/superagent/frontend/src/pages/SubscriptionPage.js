import React from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import StarIcon from '@mui/icons-material/Star';
import StarBorderIcon from '@mui/icons-material/StarBorder';
import DiamondIcon from '@mui/icons-material/Diamond';

const SubscriptionPage = () => {
  const navigate = useNavigate();
  
  const handleGoBack = () => {
    navigate(-1);
  };
  
  const handleUpgrade = (plan) => {
    // Dans une implémentation réelle, rediriger vers une page de paiement
    alert(`Upgrading to ${plan} plan. This would redirect to a payment page in a real implementation.`);
  };
  
  // Plans d'abonnement
  const plans = [
    {
      name: 'Basic',
      price: 'Free',
      description: 'Basic access to the platform',
      features: [
        { name: 'Retreat Search', included: true },
        { name: 'Content Generation', included: true },
        { name: 'Customer Support', included: true },
        { name: 'Partner Matching', included: false },
        { name: 'Retreat Planning', included: false },
        { name: 'Advanced Analytics', included: false },
      ],
      icon: <StarBorderIcon fontSize="large" />,
      color: 'default'
    },
    {
      name: 'Partner',
      price: '$29.99/month',
      description: 'For retreat partners and organizers',
      features: [
        { name: 'Retreat Search', included: true },
        { name: 'Content Generation', included: true },
        { name: 'Customer Support', included: true },
        { name: 'Partner Matching', included: true },
        { name: 'Retreat Planning', included: false },
        { name: 'Advanced Analytics', included: false },
      ],
      icon: <StarIcon fontSize="large" />,
      color: 'primary'
    },
    {
      name: 'Premium',
      price: '$49.99/month',
      description: 'Full access to all features',
      features: [
        { name: 'Retreat Search', included: true },
        { name: 'Content Generation', included: true },
        { name: 'Customer Support', included: true },
        { name: 'Partner Matching', included: true },
        { name: 'Retreat Planning', included: true },
        { name: 'Advanced Analytics', included: true },
      ],
      icon: <DiamondIcon fontSize="large" />,
      color: 'secondary'
    }
  ];
  
  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom align="center">
          Subscription Plans
        </Typography>
        
        <Typography variant="body1" paragraph align="center" sx={{ mb: 4 }}>
          Upgrade your subscription to access premium features
        </Typography>
        
        <Grid container spacing={3} justifyContent="center">
          {plans.map((plan) => (
            <Grid item xs={12} md={4} key={plan.name}>
              <Card 
                elevation={3} 
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  borderRadius: 2,
                  ...(plan.name === 'Premium' && {
                    border: '2px solid',
                    borderColor: 'secondary.main'
                  })
                }}
              >
                <Box 
                  sx={{ 
                    p: 2, 
                    bgcolor: `${plan.color}.main`, 
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 1
                  }}
                >
                  {plan.icon}
                  <Typography variant="h5" component="h2">
                    {plan.name}
                  </Typography>
                </Box>
                
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h6" align="center" gutterBottom>
                    {plan.price}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 2 }}>
                    {plan.description}
                  </Typography>
                  
                  <Divider sx={{ my: 2 }} />
                  
                  <List dense>
                    {plan.features.map((feature) => (
                      <ListItem key={feature.name}>
                        <ListItemIcon>
                          {feature.included ? (
                            <CheckIcon color="success" />
                          ) : (
                            <CloseIcon color="error" />
                          )}
                        </ListItemIcon>
                        <ListItemText 
                          primary={feature.name} 
                          sx={{
                            ...(feature.included ? {} : { color: 'text.disabled' })
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
                
                <CardActions sx={{ p: 2 }}>
                  <Button 
                    variant="contained" 
                    fullWidth
                    color={plan.color}
                    onClick={() => handleUpgrade(plan.name)}
                  >
                    {plan.name === 'Basic' ? 'Current Plan' : `Upgrade to ${plan.name}`}
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
        
        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Button 
            variant="outlined" 
            onClick={handleGoBack}
          >
            Go Back
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default SubscriptionPage;
