import React from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Grid
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import LockIcon from '@mui/icons-material/Lock';

const AccessDeniedPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Récupérer l'URL de redirection
  const from = location.state?.from?.pathname || '/';
  
  const handleGoBack = () => {
    navigate(-1);
  };
  
  const handleGoHome = () => {
    navigate('/');
  };
  
  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Paper 
          elevation={3} 
          sx={{ 
            p: 4, 
            borderRadius: 2,
            textAlign: 'center'
          }}
        >
          <Box sx={{ mb: 3 }}>
            <LockIcon color="error" sx={{ fontSize: 80 }} />
          </Box>
          
          <Typography variant="h4" component="h1" gutterBottom>
            Access Denied
          </Typography>
          
          <Typography variant="body1" paragraph>
            You don't have permission to access this page.
          </Typography>
          
          <Typography variant="body2" color="text.secondary" paragraph>
            This feature may require a higher subscription level or specific permissions.
          </Typography>
          
          <Grid container spacing={2} justifyContent="center" sx={{ mt: 3 }}>
            <Grid item>
              <Button 
                variant="outlined" 
                onClick={handleGoBack}
              >
                Go Back
              </Button>
            </Grid>
            <Grid item>
              <Button 
                variant="contained" 
                onClick={handleGoHome}
              >
                Go to Home
              </Button>
            </Grid>
          </Grid>
        </Paper>
      </Box>
    </Container>
  );
};

export default AccessDeniedPage;
