import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import {
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  Button,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Avatar,
  Menu,
  MenuItem,
  Tooltip
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import HomeIcon from '@mui/icons-material/Home';
import WorkIcon from '@mui/icons-material/Work';
import DescriptionIcon from '@mui/icons-material/Description';
import HelpIcon from '@mui/icons-material/Help';
import LoginIcon from '@mui/icons-material/Login';
import LogoutIcon from '@mui/icons-material/Logout';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import DiamondIcon from '@mui/icons-material/Diamond';

import authService from './services/auth';
import PrivateRoute from './components/PrivateRoute';

// Import pages
import HomePage from './pages/HomePage';
import CreateWorkflowPage from './pages/CreateWorkflowPage';
import LoginPage from './pages/LoginPage';
import AccessDeniedPage from './pages/AccessDeniedPage';
import SubscriptionPage from './pages/SubscriptionPage';

// Create theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#2e7d32', // Green
    },
    secondary: {
      main: '#00796b', // Teal
    },
    background: {
      default: '#f5f5f5',
    },
  },
});

function App() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Vérifier si l'utilisateur est connecté au chargement de l'application
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('Auth check error:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const toggleDrawer = (open) => (event) => {
    if (event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
      return;
    }
    setDrawerOpen(open);
  };

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    authService.logout();
    setUser(null);
    handleClose();
    window.location.href = '/';
  };

  const menuItems = [
    { text: 'Home', icon: <HomeIcon />, path: '/' },
    { text: 'Workflows', icon: <WorkIcon />, path: '/workflows', requireAuth: true },
    { text: 'Templates', icon: <DescriptionIcon />, path: '/workflows/templates', requireAuth: true },
    { text: 'Help', icon: <HelpIcon />, path: '/help' },
  ];

  const drawer = (
    <Box
      sx={{ width: 250 }}
      role="presentation"
      onClick={toggleDrawer(false)}
      onKeyDown={toggleDrawer(false)}
    >
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" component="div">
          Retreat And Be
        </Typography>
        <Typography variant="subtitle2" color="text.secondary">
          AI Workflow Platform
        </Typography>
      </Box>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItem
            button
            key={item.text}
            component={Link}
            to={item.path}
            disabled={item.requireAuth && !user}
          >
            <ListItemIcon>
              {item.icon}
            </ListItemIcon>
            <ListItemText primary={item.text} />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
          <AppBar position="static">
            <Toolbar>
              <IconButton
                size="large"
                edge="start"
                color="inherit"
                aria-label="menu"
                sx={{ mr: 2 }}
                onClick={toggleDrawer(true)}
              >
                <MenuIcon />
              </IconButton>
              <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                Retreat And Be
              </Typography>

              {user ? (
                <>
                  <Button
                    color="inherit"
                    component={Link}
                    to="/workflows/templates"
                    sx={{ mr: 1 }}
                  >
                    Templates
                  </Button>
                  <Button
                    color="inherit"
                    component={Link}
                    to="/workflows"
                    sx={{ mr: 2 }}
                  >
                    Workflows
                  </Button>

                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {user.subscription_level === 'premium' && (
                      <Tooltip title="Premium Account">
                        <DiamondIcon color="secondary" sx={{ mr: 1 }} />
                      </Tooltip>
                    )}

                    <Tooltip title="Account settings">
                      <IconButton
                        size="large"
                        aria-label="account of current user"
                        aria-controls="menu-appbar"
                        aria-haspopup="true"
                        onClick={handleMenu}
                        color="inherit"
                      >
                        <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                          {user.username ? user.username.charAt(0).toUpperCase() : 'U'}
                        </Avatar>
                      </IconButton>
                    </Tooltip>

                    <Menu
                      id="menu-appbar"
                      anchorEl={anchorEl}
                      anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'right',
                      }}
                      keepMounted
                      transformOrigin={{
                        vertical: 'top',
                        horizontal: 'right',
                      }}
                      open={Boolean(anchorEl)}
                      onClose={handleClose}
                    >
                      <MenuItem disabled>
                        <Typography variant="body2">
                          Signed in as <strong>{user.username}</strong>
                        </Typography>
                      </MenuItem>
                      <Divider />
                      <MenuItem component={Link} to="/profile" onClick={handleClose}>
                        <ListItemIcon>
                          <AccountCircleIcon fontSize="small" />
                        </ListItemIcon>
                        Profile
                      </MenuItem>
                      <MenuItem component={Link} to="/subscription" onClick={handleClose}>
                        <ListItemIcon>
                          <DiamondIcon fontSize="small" />
                        </ListItemIcon>
                        Subscription
                      </MenuItem>
                      <Divider />
                      <MenuItem onClick={handleLogout}>
                        <ListItemIcon>
                          <LogoutIcon fontSize="small" />
                        </ListItemIcon>
                        Logout
                      </MenuItem>
                    </Menu>
                  </Box>
                </>
              ) : (
                <Button
                  color="inherit"
                  component={Link}
                  to="/login"
                  startIcon={<LoginIcon />}
                >
                  Login
                </Button>
              )}
            </Toolbar>
          </AppBar>

          <Drawer
            anchor="left"
            open={drawerOpen}
            onClose={toggleDrawer(false)}
          >
            {drawer}
          </Drawer>

          <Box component="main" sx={{ flexGrow: 1 }}>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/access-denied" element={<AccessDeniedPage />} />
              <Route path="/subscription" element={<SubscriptionPage />} />

              {/* Protected routes */}
              <Route
                path="/workflows/create/:templateId"
                element={
                  <PrivateRoute>
                    <CreateWorkflowPage />
                  </PrivateRoute>
                }
              />

              {/* Premium routes */}
              <Route
                path="/workflows/premium"
                element={
                  <PrivateRoute requiredSubscription="premium">
                    <HomePage />
                  </PrivateRoute>
                }
              />

              {/* Fallback route */}
              <Route path="*" element={<HomePage />} />
            </Routes>
          </Box>

          <Box component="footer" sx={{ py: 3, px: 2, mt: 'auto', backgroundColor: 'background.paper' }}>
            <Container maxWidth="sm">
              <Typography variant="body2" color="text.secondary" align="center">
                © {new Date().getFullYear()} Retreat And Be. All rights reserved.
              </Typography>
            </Container>
          </Box>
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;
