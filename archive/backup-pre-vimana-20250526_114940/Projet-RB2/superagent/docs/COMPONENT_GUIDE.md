# Guide d'Utilisation des Composants R&B new

Ce guide explique comment utiliser efficacement la bibliothèque de composants R&B new dans vos projets. Il couvre l'installation, l'utilisation des différents types de composants, et les bonnes pratiques à suivre.

## Table des matières

1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Architecture Atomic Design](#architecture-atomic-design)
4. [Utilisation des Composants](#utilisation-des-composants)
   - [Atoms (Composants Atomiques)](#atoms-composants-atomiques)
   - [Molecules (Composants Moléculaires)](#molecules-composants-moléculaires)
   - [Organisms (Composants Organismiques)](#organisms-composants-organismiques)
   - [Templates (Gabarits)](#templates-gabarits)
   - [Pages](#pages)
5. [Personnalisation des Composants](#personnalisation-des-composants)
6. [Accessibilité](#accessibilité)
7. [Bonnes Pratiques](#bonnes-pratiques)
8. [Exemples Complets](#exemples-complets)
9. [Dépannage](#dépannage)
10. [Ressources](#ressources)

## Introduction

La bibliothèque de composants R&B new est conçue pour accélérer le développement d'interfaces utilisateur cohérentes et de haute qualité pour les applications web et mobile de Retreat And Be. Elle suit la méthodologie Atomic Design pour organiser les composants de manière logique et modulaire.

## Installation

Pour installer la bibliothèque de composants dans votre projet :

```bash
# Avec npm
npm install @retreat-and-be/ui

# Avec yarn
yarn add @retreat-and-be/ui

# Avec pnpm
pnpm add @retreat-and-be/ui
```

Ensuite, importez les composants dans vos fichiers :

```jsx
import { Button, Card, Header } from '@retreat-and-be/ui';
```

## Architecture Atomic Design

Notre bibliothèque suit la méthodologie Atomic Design, qui organise les composants en cinq catégories :

1. **Atoms** : Les éléments les plus basiques (boutons, inputs, textes, icônes)
2. **Molecules** : Combinaisons simples d'atomes (champs de formulaire, cartes simples)
3. **Organisms** : Sections fonctionnelles complètes (header, formulaires complexes)
4. **Templates** : Mises en page réutilisables sans contenu spécifique
5. **Pages** : Implémentations spécifiques des templates avec du contenu réel

Cette structure permet une réutilisation maximale et une maintenance simplifiée.

## Utilisation des Composants

### Atoms (Composants Atomiques)

Les atoms sont les blocs de construction fondamentaux de l'interface.

#### Button

```jsx
import { Button } from '@retreat-and-be/ui';

// Variantes
<Button variant="primary">Bouton Primaire</Button>
<Button variant="secondary">Bouton Secondaire</Button>
<Button variant="outline">Bouton Contour</Button>
<Button variant="text">Bouton Texte</Button>

// Tailles
<Button size="sm">Petit</Button>
<Button size="md">Moyen</Button>
<Button size="lg">Grand</Button>

// États
<Button disabled>Désactivé</Button>
<Button loading>Chargement</Button>
<Button fullWidth>Pleine Largeur</Button>

// Avec icône
<Button icon={<Heart />}>Avec Icône</Button>
<Button icon={<Heart />} variant="outline" />
```

#### Typography

```jsx
import { Title, Text } from '@retreat-and-be/ui';

// Titres
<Title level={1}>Titre niveau 1</Title>
<Title level={2}>Titre niveau 2</Title>
<Title level={3}>Titre niveau 3</Title>
<Title level={4}>Titre niveau 4</Title>
<Title level={5}>Titre niveau 5</Title>
<Title level={6}>Titre niveau 6</Title>

// Texte
<Text>Texte standard</Text>
<Text size="sm">Petit texte</Text>
<Text size="lg">Grand texte</Text>
<Text weight="bold">Texte en gras</Text>
<Text variant="secondary">Texte secondaire</Text>
```

#### Input

```jsx
import { Input } from '@retreat-and-be/ui';

// Input basique
<Input placeholder="Saisissez votre texte" />

// Avec label
<Input label="Email" placeholder="<EMAIL>" type="email" />

// Avec texte d'aide
<Input 
  placeholder="Mot de passe" 
  type="password" 
  helperText="8 caractères minimum" 
/>

// Avec erreur
<Input 
  placeholder="Nom d'utilisateur" 
  error="Ce champ est requis" 
/>

// Avec icône
<Input 
  placeholder="Rechercher..." 
  icon={<Search />} 
/>

// Désactivé
<Input 
  placeholder="Désactivé" 
  disabled 
/>
```

#### Icon

```jsx
import { Icon } from '@retreat-and-be/ui';
import { User, Heart, Settings } from 'lucide-react';

<Icon icon={<User />} />
<Icon icon={<Heart />} size="lg" color="red" />
<Icon icon={<Settings />} onClick={() => console.log('Icon clicked')} />
```

#### Badge

```jsx
import { Badge } from '@retreat-and-be/ui';

<Badge>Default</Badge>
<Badge variant="primary">Primary</Badge>
<Badge variant="success">Success</Badge>
<Badge variant="warning">Warning</Badge>
<Badge variant="danger">Danger</Badge>
<Badge icon={<Check />}>With Icon</Badge>
<Badge count={5}>Count</Badge>
```

#### Avatar

```jsx
import { Avatar } from '@retreat-and-be/ui';

<Avatar src="/images/user.jpg" alt="User Name" />
<Avatar size="sm" src="/images/user.jpg" alt="User Name" />
<Avatar size="lg" src="/images/user.jpg" alt="User Name" />
<Avatar initials="JD" />
<Avatar icon={<User />} />
<Avatar status="online" src="/images/user.jpg" alt="User Name" />
```

#### Spinner

```jsx
import { Spinner } from '@retreat-and-be/ui';

<Spinner />
<Spinner size="sm" />
<Spinner size="lg" />
<Spinner variant="primary" />
<Spinner variant="secondary" />
```

### Molecules (Composants Moléculaires)

Les molecules combinent plusieurs atoms pour créer des composants plus complexes.

#### Card

```jsx
import { Card, Title, Text, Button } from '@retreat-and-be/ui';

<Card className="p-6">
  <Title level={4}>Titre de la carte</Title>
  <Text className="my-2">Contenu de la carte avec description.</Text>
  <Button variant="primary">Action</Button>
</Card>

// Avec image
<Card className="overflow-hidden">
  <img src="/images/card-image.jpg" alt="Card header" className="w-full h-48 object-cover" />
  <div className="p-6">
    <Title level={4}>Carte avec image</Title>
    <Text className="my-2">Description de la carte avec image.</Text>
    <Button variant="primary">Action</Button>
  </div>
</Card>
```

#### FormField

```jsx
import { FormField } from '@retreat-and-be/ui';

<FormField
  label="Nom d'utilisateur"
  name="username"
  placeholder="Entrez votre nom d'utilisateur"
  helperText="Votre identifiant unique"
/>

<FormField
  label="Email"
  name="email"
  type="email"
  placeholder="Entrez votre email"
  required
/>

<FormField
  label="Mot de passe"
  name="password"
  type="password"
  placeholder="Entrez votre mot de passe"
  error="Le mot de passe doit contenir au moins 8 caractères"
/>

<FormField
  label="Bio"
  name="bio"
  type="textarea"
  placeholder="Parlez-nous de vous"
  rows={3}
/>
```

#### SearchBar

```jsx
import { SearchBar } from '@retreat-and-be/ui';

<SearchBar 
  placeholder="Rechercher des retraites..." 
  onSearch={(value) => console.log('Recherche :', value)}
/>

// Avec suggestions
<SearchBar 
  placeholder="Rechercher..." 
  suggestions={['Yoga', 'Méditation', 'Bien-être', 'Nature']}
  onSearch={(value) => console.log('Recherche :', value)}
  onSuggestionClick={(suggestion) => console.log('Suggestion :', suggestion)}
/>
```

#### Notification

```jsx
import { Notification } from '@retreat-and-be/ui';
import { Check, X, Info, AlertTriangle } from 'lucide-react';

<Notification
  title="Succès"
  message="Votre profil a été mis à jour avec succès."
  variant="success"
  icon={<Check />}
  onClose={() => console.log('Notification fermée')}
/>

<Notification
  title="Erreur"
  message="Une erreur s'est produite lors du traitement de votre demande."
  variant="error"
  icon={<X />}
  onClose={() => console.log('Notification fermée')}
/>

<Notification
  title="Information"
  message="Votre réservation est confirmée pour le 15 juillet."
  variant="info"
  icon={<Info />}
  onClose={() => console.log('Notification fermée')}
/>

<Notification
  title="Avertissement"
  message="Votre session expirera dans 5 minutes."
  variant="warning"
  icon={<AlertTriangle />}
  onClose={() => console.log('Notification fermée')}
/>
```

#### TabGroup

```jsx
import { TabGroup } from '@retreat-and-be/ui';
import { Info, List, MessageSquare, Link } from 'lucide-react';
import { useState } from 'react';

const [activeTab, setActiveTab] = useState(0);

<TabGroup
  tabs={[
    { label: 'Aperçu', icon: <Info /> },
    { label: 'Détails', icon: <List /> },
    { label: 'Avis', icon: <MessageSquare /> },
    { label: 'Liens', icon: <Link /> }
  ]}
  activeTab={activeTab}
  onChange={setActiveTab}
/>

// Contenu des onglets
<div className="p-4 border rounded-b-lg">
  {activeTab === 0 && <div>Contenu de l'aperçu</div>}
  {activeTab === 1 && <div>Contenu des détails</div>}
  {activeTab === 2 && <div>Contenu des avis</div>}
  {activeTab === 3 && <div>Contenu des liens</div>}
</div>
```

#### Breadcrumb

```jsx
import { Breadcrumb } from '@retreat-and-be/ui';

<Breadcrumb
  items={[
    { label: 'Accueil', href: '/' },
    { label: 'Retraites', href: '/retraites' },
    { label: 'Yoga', href: '/retraites/yoga' },
    { label: 'Provence', href: '/retraites/yoga/provence' }
  ]}
/>
```

### Organisms (Composants Organismiques)

Les organisms sont des composants complexes qui forment des sections complètes de l'interface.

#### Header

```jsx
import { Header } from '@retreat-and-be/ui';

<Header
  logo="/images/logo.svg"
  navigation={[
    { label: 'Accueil', href: '/' },
    { label: 'Retraites', href: '/retraites' },
    { label: 'À propos', href: '/a-propos' },
    { label: 'Contact', href: '/contact' }
  ]}
  actions={[
    { label: 'Connexion', href: '/connexion', variant: 'outline' },
    { label: 'Inscription', href: '/inscription', variant: 'primary' }
  ]}
/>
```

#### Hero

```jsx
import { Hero } from '@retreat-and-be/ui';

<Hero
  title="Découvrez Votre Retraite Parfaite"
  subtitle="Trouvez la paix, le bien-être et la connexion avec nos expériences de retraite soigneusement sélectionnées"
  image="/images/hero-bg.jpg"
  cta={{ label: 'Explorer les Retraites', href: '/retraites' }}
/>
```

#### RetreatCard

```jsx
import { RetreatCard } from '@retreat-and-be/ui';

<RetreatCard
  id="retreat-123"
  title="Retraite Yoga & Méditation"
  location="Provence, France"
  duration="7 jours"
  price={1200}
  rating={4.8}
  image="/images/retreat.jpg"
  tags={['yoga', 'méditation', 'nature']}
/>

// Avec badge "En vedette"
<RetreatCard
  id="retreat-456"
  title="Retraite Détox & Bien-être"
  location="Alpes, Suisse"
  duration="5 jours"
  price={950}
  rating={4.6}
  image="/images/retreat-2.jpg"
  tags={['détox', 'bien-être', 'montagne']}
  featured
/>

// Avec réduction
<RetreatCard
  id="retreat-789"
  title="Retraite Méditation Pleine Conscience"
  location="Bali, Indonésie"
  duration="10 jours"
  price={1800}
  rating={4.9}
  image="/images/retreat-3.jpg"
  tags={['méditation', 'pleine conscience', 'spiritualité']}
  discount={15}
/>
```

#### SearchSection

```jsx
import { SearchSection } from '@retreat-and-be/ui';

<SearchSection
  title="Trouvez votre prochaine retraite"
  filters={[
    { type: 'location', label: 'Destination', options: ['France', 'Espagne', 'Italie', 'Suisse', 'Bali'] },
    { type: 'category', label: 'Type', options: ['Yoga', 'Méditation', 'Détox', 'Bien-être', 'Spirituel'] },
    { type: 'duration', label: 'Durée', options: ['Weekend', '1 semaine', '2 semaines', 'Plus'] },
    { type: 'price', label: 'Budget', options: ['Économique', 'Modéré', 'Premium', 'Luxe'] }
  ]}
  onSearch={(filters) => console.log('Recherche avec filtres:', filters)}
/>
```

#### LoginForm

```jsx
import { LoginForm } from '@retreat-and-be/ui';

<LoginForm
  onSubmit={(data) => console.log('Données de connexion:', data)}
  onForgotPassword={() => console.log('Mot de passe oublié')}
  onRegister={() => console.log('Inscription')}
  socialLogins={['google', 'facebook', 'apple']}
/>
```

#### BookingForm

```jsx
import { BookingForm } from '@retreat-and-be/ui';

<BookingForm
  retreatId="retreat-123"
  retreatTitle="Retraite Yoga & Méditation"
  availableDates={[
    { startDate: '2023-07-15', endDate: '2023-07-22', price: 1200, availability: 5 },
    { startDate: '2023-08-05', endDate: '2023-08-12', price: 1300, availability: 3 },
    { startDate: '2023-09-10', endDate: '2023-09-17', price: 1150, availability: 8 }
  ]}
  onSubmit={(data) => console.log('Données de réservation:', data)}
/>
```

#### Footer

```jsx
import { Footer } from '@retreat-and-be/ui';

<Footer
  logo="/images/logo.svg"
  tagline="Découvrez des retraites de bien-être pour l'esprit, le corps et l'âme"
  links={[
    {
      title: 'Entreprise',
      items: [
        { label: 'À propos', href: '/a-propos' },
        { label: 'Notre équipe', href: '/equipe' },
        { label: 'Carrières', href: '/carrieres' },
        { label: 'Contact', href: '/contact' }
      ]
    },
    {
      title: 'Retraites',
      items: [
        { label: 'Yoga', href: '/retraites/yoga' },
        { label: 'Méditation', href: '/retraites/meditation' },
        { label: 'Bien-être', href: '/retraites/bien-etre' },
        { label: 'Spirituel', href: '/retraites/spirituel' }
      ]
    },
    {
      title: 'Support',
      items: [
        { label: 'FAQ', href: '/faq' },
        { label: 'Conditions', href: '/conditions' },
        { label: 'Confidentialité', href: '/confidentialite' },
        { label: 'Cookies', href: '/cookies' }
      ]
    }
  ]}
  social={[
    { platform: 'facebook', url: 'https://facebook.com/retreatandbe' },
    { platform: 'instagram', url: 'https://instagram.com/retreatandbe' },
    { platform: 'twitter', url: 'https://twitter.com/retreatandbe' },
    { platform: 'youtube', url: 'https://youtube.com/retreatandbe' }
  ]}
  newsletter={{
    title: 'Abonnez-vous à notre newsletter',
    description: 'Recevez les dernières nouvelles et offres',
    placeholder: 'Votre adresse email',
    buttonText: 'S'abonner'
  }}
  copyright="© 2023 Retreat And Be. Tous droits réservés."
/>
```

### Templates (Gabarits)

Les templates sont des mises en page réutilisables qui définissent la structure des pages.

#### MainLayout

```jsx
import { MainLayout } from '@retreat-and-be/ui';

<MainLayout
  meta={{
    title: 'Titre de la page | Retreat And Be',
    description: 'Description de la page pour le SEO'
  }}
  announcement={{
    text: 'Nouvelle offre spéciale : 15% de réduction sur toutes les retraites',
    link: '/offres'
  }}
>
  {/* Contenu de la page */}
  <div className="max-w-7xl mx-auto px-4 py-8">
    <h1>Contenu de la page</h1>
    {/* ... */}
  </div>
</MainLayout>
```

#### DashboardLayout

```jsx
import { DashboardLayout } from '@retreat-and-be/ui';

<DashboardLayout
  user={{
    name: 'John Doe',
    avatar: '/images/avatar.jpg',
    role: 'user'
  }}
  navigation={[
    { label: 'Tableau de bord', href: '/dashboard', icon: <Home /> },
    { label: 'Réservations', href: '/dashboard/reservations', icon: <Calendar /> },
    { label: 'Favoris', href: '/dashboard/favoris', icon: <Heart /> },
    { label: 'Profil', href: '/dashboard/profil', icon: <User /> },
    { label: 'Paramètres', href: '/dashboard/parametres', icon: <Settings /> }
  ]}
>
  {/* Contenu du tableau de bord */}
  <div className="p-6">
    <h1>Tableau de bord</h1>
    {/* ... */}
  </div>
</DashboardLayout>
```

### Pages

Les pages sont des implémentations spécifiques des templates avec du contenu réel.

```jsx
import React from 'react';
import { 
  MainLayout, 
  Hero, 
  RetreatCard, 
  SearchSection,
  Title,
  Text,
  Button
} from '@retreat-and-be/ui';

export const HomePage = () => {
  // Données de démonstration
  const featuredRetreats = [
    {
      id: 'retreat-123',
      title: 'Retraite Yoga & Méditation',
      location: 'Provence, France',
      duration: '7 jours',
      price: 1200,
      rating: 4.8,
      image: '/images/retreat-1.jpg',
      tags: ['yoga', 'méditation', 'nature']
    },
    // ...autres retraites
  ];

  return (
    <MainLayout
      meta={{
        title: 'Retreat And Be - Découvrez nos retraites de bien-être',
        description: 'Explorez nos retraites de bien-être, yoga et méditation pour vous ressourcer et vous reconnecter à vous-même.'
      }}
    >
      {/* Hero Section */}
      <Hero
        title="Découvrez Votre Retraite Parfaite"
        subtitle="Trouvez la paix, le bien-être et la connexion avec nos expériences de retraite soigneusement sélectionnées"
        image="/images/hero-bg.jpg"
        cta={{ label: 'Explorer les Retraites', href: '/retraites' }}
      />

      {/* Search Section */}
      <SearchSection
        title="Trouvez votre prochaine retraite"
        filters={[
          { type: 'location', label: 'Destination', options: ['France', 'Espagne', 'Italie', 'Suisse', 'Bali'] },
          { type: 'category', label: 'Type', options: ['Yoga', 'Méditation', 'Détox', 'Bien-être', 'Spirituel'] },
          { type: 'duration', label: 'Durée', options: ['Weekend', '1 semaine', '2 semaines', 'Plus'] },
          { type: 'price', label: 'Budget', options: ['Économique', 'Modéré', 'Premium', 'Luxe'] }
        ]}
        onSearch={(filters) => console.log('Recherche avec filtres:', filters)}
      />

      {/* Featured Retreats */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <Title level={2} className="text-center mb-12">Retraites en Vedette</Title>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredRetreats.map(retreat => (
              <RetreatCard
                key={retreat.id}
                id={retreat.id}
                title={retreat.title}
                location={retreat.location}
                duration={retreat.duration}
                price={retreat.price}
                rating={retreat.rating}
                image={retreat.image}
                tags={retreat.tags}
              />
            ))}
          </div>
          <div className="text-center mt-12">
            <Button variant="primary" size="lg" as="a" href="/retraites">
              Voir toutes les retraites
            </Button>
          </div>
        </div>
      </section>

      {/* Autres sections... */}
    </MainLayout>
  );
};

export default HomePage;
```

## Personnalisation des Composants

### Utilisation des props className

Tous les composants acceptent une prop `className` qui vous permet d'ajouter des classes CSS personnalisées :

```jsx
<Button className="my-custom-class">Bouton personnalisé</Button>
```

### Thème et variables CSS

La bibliothèque utilise des variables CSS pour définir les couleurs, espacements, et autres propriétés visuelles. Vous pouvez les surcharger dans votre fichier CSS global :

```css
:root {
  --rb-color-primary: #3b82f6;
  --rb-color-secondary: #6b7280;
  --rb-color-success: #10b981;
  --rb-color-warning: #f59e0b;
  --rb-color-danger: #ef4444;
  --rb-color-info: #3b82f6;
  
  --rb-font-family: 'Inter', sans-serif;
  --rb-border-radius: 0.375rem;
  
  --rb-spacing-1: 0.25rem;
  --rb-spacing-2: 0.5rem;
  --rb-spacing-3: 0.75rem;
  --rb-spacing-4: 1rem;
  --rb-spacing-6: 1.5rem;
  --rb-spacing-8: 2rem;
  --rb-spacing-12: 3rem;
  --rb-spacing-16: 4rem;
}
```

## Accessibilité

Tous les composants sont conçus pour être accessibles et conformes aux normes WCAG 2.1 niveau AA. Voici quelques bonnes pratiques à suivre :

1. Toujours fournir des textes alternatifs pour les images
2. Utiliser des contrastes de couleur suffisants
3. S'assurer que tous les éléments interactifs sont accessibles au clavier
4. Utiliser des attributs ARIA appropriés lorsque nécessaire

Exemple :

```jsx
// Bon exemple
<Button aria-label="Fermer le dialogue">
  <Icon icon={<X />} />
</Button>

// Mauvais exemple
<Button>
  <Icon icon={<X />} />
</Button>
```

## Bonnes Pratiques

### Composition vs. Héritage

Privilégiez la composition plutôt que l'héritage pour créer des composants complexes :

```jsx
// Bon exemple (composition)
const ProfileCard = () => (
  <Card>
    <Avatar src="/images/user.jpg" alt="User" />
    <Title level={4}>John Doe</Title>
    <Text>Developer</Text>
    <Button variant="primary">View Profile</Button>
  </Card>
);

// Mauvais exemple (héritage)
class ProfileCard extends Card {
  render() {
    return (
      <div>
        <Avatar src="/images/user.jpg" alt="User" />
        <Title level={4}>John Doe</Title>
        <Text>Developer</Text>
        <Button variant="primary">View Profile</Button>
      </div>
    );
  }
}
```

### Gestion des États

Utilisez les hooks React pour gérer les états des composants :

```jsx
// Bon exemple
const FilterSection = () => {
  const [filters, setFilters] = useState({});
  
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };
  
  return (
    <div>
      {/* Composants de filtre */}
    </div>
  );
};
```

### Performance

Optimisez les performances en utilisant la mémoïsation pour les composants qui se redessinent fréquemment :

```jsx
import React, { memo, useMemo } from 'react';

// Mémoïsation d'un composant entier
const MemoizedComponent = memo(({ data }) => {
  return (
    <div>
      {/* Rendu basé sur data */}
    </div>
  );
});

// Mémoïsation de calculs coûteux
const DataVisualization = ({ rawData }) => {
  const processedData = useMemo(() => {
    // Traitement coûteux des données
    return rawData.map(item => /* ... */);
  }, [rawData]);
  
  return (
    <div>
      {/* Rendu basé sur processedData */}
    </div>
  );
};
```

## Exemples Complets

Consultez le fichier `ComponentShowcasePage.tsx` dans le dossier `examples` pour voir un exemple complet d'utilisation de tous les composants disponibles.

```bash
# Chemin vers l'exemple
R&B new/examples/ComponentShowcasePage.tsx
```

## Dépannage

### Problèmes courants et solutions

#### Les styles ne s'appliquent pas correctement

Assurez-vous d'avoir importé les fichiers CSS de la bibliothèque :

```jsx
// Dans votre fichier d'entrée (index.js ou App.js)
import '@retreat-and-be/ui/dist/styles.css';
```

#### Erreurs TypeScript

Si vous rencontrez des erreurs TypeScript, assurez-vous d'utiliser la version correcte de TypeScript et d'avoir les types appropriés :

```bash
npm install --save-dev @types/react @types/react-dom
```

#### Conflits de dépendances

Si vous rencontrez des conflits de dépendances, essayez de résoudre les versions :

```bash
npm dedupe
# ou
yarn dedupe
```

## Ressources

- [Documentation complète de l'API](https://docs.retreatandbe.com/ui)
- [Storybook des composants](https://ui.retreatandbe.com/storybook)
- [Dépôt GitHub](https://github.com/retreatandbe/ui)
- [Signaler un bug](https://github.com/retreatandbe/ui/issues/new)
- [Demander une fonctionnalité](https://github.com/retreatandbe/ui/issues/new?template=feature_request.md)

---

Pour toute question ou assistance supplémentaire, contactez l'équipe de développement à <EMAIL>.
