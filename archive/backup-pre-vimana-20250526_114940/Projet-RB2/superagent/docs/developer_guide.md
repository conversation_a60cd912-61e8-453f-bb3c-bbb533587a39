# Guide du Développeur - Nouvelle Structure

## Introduction

Ce guide explique la nouvelle structure du projet après la migration du dossier `src` vers `superagent/`. Il est destiné aux développeurs qui travaillent sur le projet et fournit des informations sur l'organisation des fichiers, les conventions de codage et les bonnes pratiques.

## Structure du Projet

La structure principale du projet est organisée comme suit :

```
superagent/
├── agents/                # Agents intelligents
│   ├── base/              # Classes de base pour les agents
│   ├── browser_agent/     # Agent pour la navigation web
│   ├── coder_agent/       # Agent pour la génération de code
│   ├── coordinator/       # Agent coordinateur
│   ├── nodes/             # Nœuds pour le graphe de workflow
│   ├── planner_agent/     # Agent de planification
│   ├── reporter_agent/    # Agent de génération de rapports
│   ├── research_agent/    # Agent de recherche
│   └── supervisor_agent/  # Agent superviseur
├── ai_engine/             # Moteur d'IA
│   ├── analytics_engine/  # Analyse de données
│   ├── chatbot/           # Assistant conversationnel
│   ├── content_generator/ # Génération de contenu
│   ├── recommender/       # Système de recommandation
│   └── ...
├── api/                   # API REST
│   ├── auth/              # Authentification
│   ├── middleware/        # Middleware
│   ├── models/            # Modèles de données
│   ├── routes/            # Routes API
│   └── services/          # Services API
├── config/                # Configuration
├── docs/                  # Documentation
├── examples/              # Exemples d'utilisation
├── frontend/              # Interface utilisateur
│   ├── public/            # Fichiers statiques
│   └── src/               # Code source frontend
├── graph/                 # Graphe de workflow
│   └── validators/        # Validateurs de graphe
├── tests/                 # Tests
│   ├── integration/       # Tests d'intégration
│   └── unit/              # Tests unitaires
├── tools/                 # Outils utilisés par les agents
│   └── node_tools/        # Outils pour les nœuds
├── utils/                 # Utilitaires
│   └── security/          # Utilitaires de sécurité
└── workflows/             # Workflows
    ├── examples/          # Exemples de workflows
    └── templates/         # Templates de workflows
```

## Conventions de Codage

### Importations

Les importations doivent suivre ces conventions :

```javascript
// Importations de bibliothèques externes
import { useState, useEffect } from 'react';

// Importations de modules internes
import { AgentBase } from '../../agents/base/agent_base';
import { logger } from '../../utils/logger';

// Importations de types
import type { WorkflowState } from '../../graph/types';
```

### Structure des Fichiers

Chaque fichier doit suivre cette structure :

1. Importations
2. Types/Interfaces
3. Constantes
4. Fonctions utilitaires
5. Composant/Classe principale
6. Exportations

Exemple :

```typescript
// Importations
import { useState } from 'react';
import { logger } from '../../utils/logger';

// Types
interface Props {
  title: string;
  onAction: () => void;
}

// Constantes
const DEFAULT_TITLE = 'Default Title';

// Fonctions utilitaires
function formatTitle(title: string): string {
  return title.toUpperCase();
}

// Composant principal
export function MyComponent({ title = DEFAULT_TITLE, onAction }: Props) {
  const [isActive, setIsActive] = useState(false);
  
  const formattedTitle = formatTitle(title);
  
  return (
    <div onClick={() => {
      setIsActive(!isActive);
      onAction();
    }}>
      {formattedTitle}
    </div>
  );
}
```

## Bonnes Pratiques

### Agents

- Chaque agent doit hériter de la classe `AgentBase`
- Les agents doivent être stateless autant que possible
- Utilisez les outils via l'interface standard

### Workflows

- Utilisez le système de graphe pour définir les workflows
- Documentez clairement les entrées et sorties de chaque nœud
- Gérez correctement les erreurs à chaque étape

### Tests

- Écrivez des tests unitaires pour chaque composant
- Écrivez des tests d'intégration pour les workflows
- Utilisez des mocks pour les dépendances externes

## Déploiement

Le déploiement se fait via Docker et Kubernetes :

```bash
# Construire l'image Docker
docker build -t superagent:latest .

# Déployer sur Kubernetes
kubectl apply -f k8s/superagent.yaml
```

## Résolution de Problèmes

### Problèmes Courants

1. **Erreur d'importation** : Vérifiez les chemins relatifs après la migration
2. **Erreur de type** : Assurez-vous que les interfaces sont correctement importées
3. **Erreur de workflow** : Vérifiez que tous les nœuds sont correctement définis

### Journalisation

Utilisez le système de journalisation pour déboguer :

```javascript
import { logger } from '../../utils/logger';

logger.info('Information message');
logger.warn('Warning message');
logger.error('Error message', { error });
```

## Ressources

- [Documentation API](./api/README.md)
- [Guide des Workflows](./workflows/README.md)
- [Guide des Agents](./agents/README.md)

---

*Document mis à jour le: 8 avril 2024*
