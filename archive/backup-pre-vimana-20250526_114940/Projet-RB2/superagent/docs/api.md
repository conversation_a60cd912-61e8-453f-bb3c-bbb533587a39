# Documentation API de l'IA de Retreat And Be

Cette documentation décrit les APIs exposées par l'orchestrateur d'IA de Retreat And Be.

## Base URL

```
https://api.retreatandbe.com/ai/v1
```

## Authentification

Toutes les requêtes doivent inclure un token d'authentification dans l'en-tête HTTP:

```
Authorization: Bearer YOUR_API_TOKEN
```

## Endpoints

### Système de Recommandation

#### Obtenir des recommandations de retraites

```
GET /recommendations/retreats
```

**Paramètres de requête:**
- `user_id` (obligatoire): ID de l'utilisateur
- `count` (optionnel): Nombre de recommandations à retourner (défaut: 5)
- `context` (optionnel): Contexte supplémentaire au format JSON (localisation, saison, etc.)

**Exemple de réponse:**
```json
{
  "recommendations": [
    {
      "retreat_id": "retreat_123",
      "score": 0.95,
      "reasons": ["Basé sur vos préférences", "Populaire en ce moment"],
      "tags": ["yoga", "méditation", "nature"]
    },
    {
      "retreat_id": "retreat_456",
      "score": 0.87,
      "reasons": ["Similaire à vos réservations précédentes"],
      "tags": ["bien-être", "détox", "montagne"]
    }
  ],
  "metadata": {
    "user_id": "user_789",
    "timestamp": "2023-04-15T14:32:10Z",
    "model_version": "recommender_v1.2"
  }
}
```

#### Obtenir des recommandations de professionnels

```
GET /recommendations/professionals
```

**Paramètres de requête:**
- `organizer_id` (obligatoire): ID de l'organisateur
- `count` (optionnel): Nombre de recommandations à retourner (défaut: 3)

**Exemple de réponse:**
```json
{
  "recommendations": [
    {
      "professional_id": "pro_123",
      "score": 0.92,
      "speciality": "Yoga instructor",
      "complementarity_reason": "Complète parfaitement votre offre actuelle"
    },
    {
      "professional_id": "pro_456",
      "score": 0.85,
      "speciality": "Nutrition coach",
      "complementarity_reason": "Ajoute une dimension nutrition à vos retraites"
    }
  ],
  "metadata": {
    "organizer_id": "org_789",
    "timestamp": "2023-04-15T14:35:22Z",
    "model_version": "recommender_v1.2"
  }
}
```

#### Optimiser le planning d'une retraite

```
POST /recommendations/optimize-schedule
```

**Corps de la requête:**
```json
{
  "retreat_data": {
    "id": "retreat_123",
    "duration": 7,
    "activities": ["yoga", "méditation", "randonnée", "ateliers cuisine"]
  },
  "constraints": {
    "max_activities_per_day": 4,
    "required_breaks": true,
    "activity_preferences": {
      "yoga": "morning",
      "randonnée": "afternoon"
    }
  }
}
```

**Exemple de réponse:**
```json
{
  "optimized_schedule": {
    "days": [
      {
        "day": 1,
        "activities": [
          {"time": "08:00", "duration": 60, "activity": "Méditation matinale"},
          {"time": "10:00", "duration": 120, "activity": "Yoga"},
          {"time": "14:00", "duration": 180, "activity": "Randonnée"},
          {"time": "19:00", "duration": 90, "activity": "Dîner et partage"}
        ]
      },
      // Autres jours...
    ],
    "optimization_score": 0.87,
    "balance_score": 0.92
  },
  "metadata": {
    "retreat_id": "retreat_123",
    "timestamp": "2023-04-15T14:40:15Z",
    "model_version": "scheduler_v1.1"
  }
}
```

### Assistant Conversationnel

#### Obtenir une réponse du chatbot

```
POST /chatbot/response
```

**Corps de la requête:**
```json
{
  "conversation_id": "conv_123",
  "messages": [
    {
      "role": "system",
      "content": "Vous êtes l'assistant de support client de Retreat And Be."
    },
    {
      "role": "user",
      "content": "Quelles sont les conditions d'annulation pour les retraites?"
    }
  ],
  "persona": "customer_support",
  "context": {
    "user_id": "user_789",
    "language": "fr"
  }
}
```

**Exemple de réponse:**
```json
{
  "response": "Les conditions d'annulation sont spécifiques à chaque retraite et sont indiquées dans les conditions générales. En général, une annulation plus de 30 jours avant le début de la retraite donne droit à un remboursement complet moins les frais de dossier. Entre 30 et 14 jours, un remboursement partiel est possible. Moins de 14 jours avant, aucun remboursement n'est généralement accordé. Souhaitez-vous que je vérifie les conditions spécifiques d'une retraite en particulier?",
  "metadata": {
    "conversation_id": "conv_123",
    "timestamp": "2023-04-15T14:45:30Z",
    "model_version": "chatbot_v1.3",
    "detected_intent": "cancellation_inquiry",
    "confidence": 0.92
  }
}
```

#### Détecter l'intention d'un message

```
POST /chatbot/detect-intent
```

**Corps de la requête:**
```json
{
  "message": "Je voudrais annuler ma réservation pour la retraite yoga du 15 mai",
  "context": {
    "user_id": "user_789",
    "language": "fr"
  }
}
```

**Exemple de réponse:**
```json
{
  "intent": "cancellation_inquiry",
  "confidence": 0.95,
  "entities": [
    {
      "type": "action",
      "value": "cancellation"
    },
    {
      "type": "retreat_type",
      "value": "yoga"
    },
    {
      "type": "date",
      "value": "2023-05-15"
    }
  ],
  "metadata": {
    "timestamp": "2023-04-15T14:50:12Z",
    "model_version": "intent_detector_v1.2"
  }
}
```

### Générateur de Contenu

#### Générer du contenu

```
POST /content/generate
```

**Corps de la requête:**
```json
{
  "content_type": "retreat_description",
  "parameters": {
    "theme": "yoga et méditation",
    "duration": 7,
    "target_audience": "débutants en quête de reconnexion",
    "location": "Provence",
    "benefit": "retrouver calme et sérénité",
    "activities": "yoga, méditation, ateliers de pleine conscience"
  },
  "tone": "inspirational",
  "length": "medium",
  "language": "fr"
}
```

**Exemple de réponse:**
```json
{
  "content": "Découvrez notre retraite yoga et méditation de 7 jours spécialement conçue pour débutants en quête de reconnexion. Située en Provence, cette expérience unique vous permettra de retrouver calme et sérénité.",
  "suggestions": [
    "Ajoutez des détails sur l'hébergement en Provence",
    "Mentionnez les repas et la cuisine proposés pendant la retraite",
    "Décrivez l'ambiance et l'environnement de la retraite"
  ],
  "metadata": {
    "content_type": "retreat_description",
    "language": "fr",
    "tone": "inspirational",
    "length": "medium",
    "timestamp": "2023-04-15T14:55:40Z",
    "model_version": "content_generator_v1.1"
  }
}
```

#### Suggérer des médias

```
POST /content/suggest-media
```

**Corps de la requête:**
```json
{
  "content": "Retraite yoga et méditation en Provence, au cœur de la nature, avec vue sur les champs de lavande. Pratique matinale de yoga, méditation au coucher du soleil et ateliers de cuisine provençale.",
  "context": {
    "retreat_type": "yoga",
    "location": "Provence",
    "season": "summer"
  }
}
```

**Exemple de réponse:**
```json
{
  "suggestions": [
    {
      "type": "image",
      "keywords": ["yoga", "provence", "lavande", "nature"],
      "description": "Photo de pratique de yoga dans un champ de lavande en Provence",
      "source": "unsplash",
      "confidence": 0.92
    },
    {
      "type": "video",
      "keywords": ["méditation", "coucher de soleil", "provence"],
      "description": "Vidéo courte de méditation au coucher du soleil en Provence",
      "source": "pexels",
      "confidence": 0.85
    }
  ],
  "metadata": {
    "timestamp": "2023-04-15T15:00:25Z",
    "model_version": "media_suggester_v1.0"
  }
}
```

### Moteur d'Analyse

#### Effectuer une analyse

```
POST /analytics/analyze
```

**Corps de la requête:**
```json
{
  "analysis_type": "trend_analysis",
  "parameters": {
    "metric": "bookings",
    "granularity": "daily"
  },
  "time_frame": "month",
  "filters": {
    "retreat_type": ["yoga", "meditation"],
    "region": "europe"
  }
}
```

**Exemple de réponse:**
```json
{
  "results": {
    "trend_data": {
      "dates": ["2023-03-15", "2023-03-16", "..."],
      "values": [42, 45, "..."]
    },
    "statistics": {
      "growth_rate": 0.15,
      "mean": 48.5,
      "std": 5.2,
      "min": 38,
      "max": 62
    }
  },
  "insights": [
    "Taux de croissance global: 15.00%",
    "Tendance à la hausse sur la période analysée",
    "Pic d'activité observé le 2023-03-28"
  ],
  "metadata": {
    "analysis_type": "trend_analysis",
    "time_frame": "month",
    "start_date": "2023-03-15T00:00:00Z",
    "end_date": "2023-04-14T23:59:59Z",
    "filters": {
      "retreat_type": ["yoga", "meditation"],
      "region": "europe"
    },
    "timestamp": "2023-04-15T15:05:50Z",
    "model_version": "trend_analyzer_v1.2"
  }
}
```

### Moteur d'Optimisation

#### Optimiser l'expérience utilisateur

```
POST /optimizer/optimize
```

**Corps de la requête:**
```json
{
  "optimization_type": "ui_personalization",
  "parameters": {
    "page": "home",
    "available_components": ["hero", "featured_retreats", "testimonials", "newsletter_signup"]
  },
  "context": {
    "user_id": "user_789",
    "user_segment": "Yogis Passionnés",
    "platform": "web"
  },
  "constraints": {
    "max_components": 4,
    "required_components": ["hero"]
  }
}
```

**Exemple de réponse:**
```json
{
  "recommendations": {
    "ui_components": {
      "hero_section": {
        "variant": "nature",
        "cta_text": "Réserver maintenant",
        "priority": 1
      },
      "featured_retreats": {
        "count": 6,
        "filter": "Yogis Passionnés",
        "priority": 2
      },
      "testimonials": {
        "show": true,
        "filter_by_segment": "Yogis Passionnés",
        "priority": 3
      },
      "newsletter_signup": {
        "show": true,
        "position": "bottom",
        "priority": 4
      }
    },
    "layout": ["hero_section", "featured_retreats", "testimonials", "newsletter_signup"],
    "theme": {
      "primary": "#4CAF50",
      "secondary": "#8BC34A",
      "accent": "#FFC107"
    }
  },
  "expected_impact": {
    "engagement_increase": 0.15,
    "conversion_increase": 0.08,
    "satisfaction_increase": 0.12
  },
  "metadata": {
    "optimization_type": "ui_personalization",
    "timestamp": "2023-04-15T15:10:30Z",
    "context": {
      "user_segment": "Yogis Passionnés",
      "platform": "web"
    },
    "model_version": "ui_personalizer_v1.1"
  }
}
```

## Codes d'erreur

- `400 Bad Request`: Requête invalide ou paramètres manquants
- `401 Unauthorized`: Authentification invalide ou manquante
- `403 Forbidden`: Accès refusé à la ressource demandée
- `404 Not Found`: Ressource non trouvée
- `429 Too Many Requests`: Limite de taux dépassée
- `500 Internal Server Error`: Erreur interne du serveur
- `503 Service Unavailable`: Service temporairement indisponible

## Limites de taux

Les limites de taux sont appliquées par API key:

- Recommandations: 100 requêtes par minute
- Chatbot: 200 requêtes par minute
- Générateur de contenu: 50 requêtes par minute
- Analyse: 20 requêtes par minute
- Optimisation: 30 requêtes par minute

## Versions

La version actuelle de l'API est v1. Les versions précédentes seront supportées pendant au moins 6 mois après la sortie d'une nouvelle version.
