# Guide de démarrage rapide - IA de Retreat And Be

Ce guide vous aidera à démarrer avec l'IA de Retreat And Be, en expliquant comment installer, configurer et utiliser les différents modules.

## Prérequis

- Python 3.8 ou supérieur
- pip (gestionnaire de paquets Python)
- <PERSON>er et Docker Compose (pour le déploiement)
- Un compte développeur Retreat And Be avec une clé API

## Installation

### 1. <PERSON><PERSON><PERSON> le dép<PERSON>t

```bash
git clone https://github.com/retreatandbe/ai-layer.git
cd ai-layer
```

### 2. C<PERSON>er un environnement virtuel

```bash
python -m venv venv
source venv/bin/activate  # Sur Windows: venv\Scripts\activate
```

### 3. Installer les dépendances

```bash
pip install -r requirements.txt
```

### 4. Configurer l'environnement

```bash
cp .env.example .env
```

<PERSON><PERSON><PERSON> le fichier `.env` avec vos propres valeurs, notamment:
- Les informations de connexion à la base de données
- Les clés API pour les services externes
- Les paramètres de configuration spécifiques à votre environnement

## Démarrage rapide

### Lancer l'orchestrateur d'IA

```bash
python -m src.orchestrator.main
```

Cela démarrera le serveur FastAPI sur `http://localhost:8000`. Vous pouvez accéder à la documentation interactive de l'API à l'adresse `http://localhost:8000/docs`.

### Utiliser Docker

Pour déployer l'ensemble du système avec Docker:

```bash
docker-compose up -d
```

Cela démarrera tous les services nécessaires, y compris l'orchestrateur d'IA, Redis, PostgreSQL, etc.

## Utilisation des modules

### Système de Recommandation

```python
from src.recommender.engine import RecommenderEngine, UserProfile

# Initialiser le moteur de recommandation
recommender = RecommenderEngine()

# Créer un profil utilisateur
user_profile = UserProfile(
    user_id="user_123",
    preferences={
        "interests": ["yoga", "méditation", "nature"],
        "price_range": "medium",
        "preferred_duration": [3, 7]
    },
    history=[
        {"retreat_id": "retreat_456", "rating": 4.5, "date": "2023-01-15"}
    ]
)

# Obtenir des recommandations
recommendations = recommender.get_retreat_recommendations(
    user_profile=user_profile,
    count=5,
    context={"location": "France", "season": "summer"}
)

# Afficher les recommandations
for rec in recommendations:
    print(f"Retreat: {rec.retreat_id}, Score: {rec.score}")
    print(f"Reasons: {', '.join(rec.reasons)}")
    print(f"Tags: {', '.join(rec.tags)}")
    print("---")
```

### Assistant Conversationnel

```python
from src.chatbot.engine import ChatbotEngine, Conversation, Message, MessageRole

# Initialiser le moteur de chatbot
chatbot = ChatbotEngine()

# Créer une conversation
conversation = Conversation(
    conversation_id="conv_123",
    messages=[
        Message(role=MessageRole.SYSTEM, content="Vous êtes l'assistant de support client de Retreat And Be."),
        Message(role=MessageRole.USER, content="Quelles sont les conditions d'annulation?")
    ]
)

# Obtenir une réponse
response = chatbot.get_response(
    conversation=conversation,
    persona="customer_support",
    context={"user_id": "user_123"}
)

print(f"Chatbot: {response}")

# Détecter l'intention d'un message
intent_data = chatbot.detect_intent("Je voudrais annuler ma réservation")
print(f"Intent: {intent_data['intent']}, Confidence: {intent_data['confidence']}")
```

### Générateur de Contenu

```python
from src.content.engine import ContentEngine, ContentRequest, ContentType

# Initialiser le moteur de génération de contenu
content_engine = ContentEngine()

# Créer une demande de génération de contenu
request = ContentRequest(
    content_type=ContentType.RETREAT_DESCRIPTION,
    parameters={
        "theme": "yoga et méditation",
        "duration": 7,
        "target_audience": "débutants en quête de reconnexion",
        "location": "Provence",
        "benefit": "retrouver calme et sérénité"
    },
    tone="inspirational",
    length="medium",
    language="fr"
)

# Générer du contenu
response = content_engine.generate_content(request)

print(f"Contenu généré: {response.content}")
print("Suggestions:")
for suggestion in response.suggestions:
    print(f"- {suggestion}")

# Suggérer des médias
media_suggestions = content_engine.suggest_media(
    content=response.content,
    context={"retreat_type": "yoga", "location": "Provence"}
)

print("Suggestions de médias:")
for media in media_suggestions:
    print(f"- {media['type']}: {media['description']}")
```

### Moteur d'Analyse

```python
from src.analytics.engine import AnalyticsEngine, AnalysisRequest, AnalysisType, TimeFrame

# Initialiser le moteur d'analyse
analytics_engine = AnalyticsEngine()

# Créer une demande d'analyse
request = AnalysisRequest(
    analysis_type=AnalysisType.TREND_ANALYSIS,
    parameters={
        "metric": "bookings",
        "granularity": "daily"
    },
    time_frame=TimeFrame.MONTH,
    filters={
        "retreat_type": ["yoga", "meditation"],
        "region": "europe"
    }
)

# Effectuer l'analyse
response = analytics_engine.perform_analysis(request)

print("Résultats de l'analyse:")
print(f"Taux de croissance: {response.results['statistics']['growth_rate']:.2%}")

print("Insights:")
for insight in response.insights:
    print(f"- {insight}")
```

### Moteur d'Optimisation

```python
from src.optimizer.engine import OptimizerEngine, OptimizationRequest, OptimizationType

# Initialiser le moteur d'optimisation
optimizer_engine = OptimizerEngine()

# Créer une demande d'optimisation
request = OptimizationRequest(
    optimization_type=OptimizationType.UI_PERSONALIZATION,
    parameters={
        "page": "home",
        "available_components": ["hero", "featured_retreats", "testimonials", "newsletter_signup"]
    },
    context={
        "user_id": "user_123",
        "user_segment": "Yogis Passionnés",
        "platform": "web"
    },
    constraints={
        "max_components": 4,
        "required_components": ["hero"]
    }
)

# Effectuer l'optimisation
response = optimizer_engine.optimize(request)

print("Recommandations d'optimisation:")
print(f"Thème: {response.recommendations['theme']}")
print(f"Layout: {response.recommendations['layout']}")

print("Impact attendu:")
print(f"Augmentation de l'engagement: {response.expected_impact['engagement_increase']:.2%}")
print(f"Augmentation de la conversion: {response.expected_impact['conversion_increase']:.2%}")
```

## Intégration avec les plateformes

### Web (React)

```javascript
// Exemple d'intégration avec React
import React, { useEffect, useState } from 'react';
import axios from 'axios';

const API_URL = 'https://api.retreatandbe.com/ai/v1';
const API_KEY = 'your_api_key';

function RecommendedRetreats({ userId }) {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRecommendations = async () => {
      try {
        const response = await axios.get(`${API_URL}/recommendations/retreats`, {
          params: { user_id: userId, count: 5 },
          headers: { Authorization: `Bearer ${API_KEY}` }
        });
        setRecommendations(response.data.recommendations);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching recommendations:', error);
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [userId]);

  if (loading) return <div>Loading recommendations...</div>;

  return (
    <div className="recommended-retreats">
      <h2>Recommandations pour vous</h2>
      <div className="retreats-grid">
        {recommendations.map(rec => (
          <div key={rec.retreat_id} className="retreat-card">
            <h3>{rec.retreat_id}</h3>
            <div className="tags">
              {rec.tags.map(tag => (
                <span key={tag} className="tag">{tag}</span>
              ))}
            </div>
            <div className="reasons">
              <p>{rec.reasons[0]}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default RecommendedRetreats;
```

### Mobile (React Native)

```javascript
// Exemple d'intégration avec React Native
import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, StyleSheet, ActivityIndicator } from 'react-native';
import axios from 'axios';

const API_URL = 'https://api.retreatandbe.com/ai/v1';
const API_KEY = 'your_api_key';

function RecommendedRetreats({ userId }) {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRecommendations = async () => {
      try {
        const response = await axios.get(`${API_URL}/recommendations/retreats`, {
          params: { user_id: userId, count: 4 },
          headers: { Authorization: `Bearer ${API_KEY}` }
        });
        setRecommendations(response.data.recommendations);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching recommendations:', error);
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [userId]);

  if (loading) return <ActivityIndicator size="large" color="#4CAF50" />;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Recommandations pour vous</Text>
      <FlatList
        data={recommendations}
        keyExtractor={item => item.retreat_id}
        horizontal
        showsHorizontalScrollIndicator={false}
        renderItem={({ item }) => (
          <View style={styles.card}>
            <Text style={styles.cardTitle}>{item.retreat_id}</Text>
            <View style={styles.tagsContainer}>
              {item.tags.map(tag => (
                <Text key={tag} style={styles.tag}>{tag}</Text>
              ))}
            </View>
            <Text style={styles.reason}>{item.reasons[0]}</Text>
          </View>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    paddingHorizontal: 15,
  },
  card: {
    width: 250,
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  tag: {
    backgroundColor: '#E1F5FE',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 5,
    marginBottom: 5,
    fontSize: 12,
  },
  reason: {
    fontSize: 14,
    color: '#666',
  },
});

export default RecommendedRetreats;
```

### Extension Chrome

```javascript
// Exemple de code pour l'extension Chrome (background.js)
const API_URL = 'https://api.retreatandbe.com/ai/v1';
const API_KEY = 'your_api_key';

// Détecter les pages liées au bien-être
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    chrome.tabs.sendMessage(tabId, {
      action: 'analyzePageContent',
      url: tab.url
    });
  }
});

// Écouter les messages de content.js
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'pageContentAnalyzed' && request.wellnessContent) {
    // Si la page contient du contenu lié au bien-être, suggérer des retraites
    fetch(`${API_URL}/recommendations/retreats?count=3&context=${JSON.stringify({
      source_url: sender.tab.url,
      detected_topics: request.wellnessTopics
    })}`, {
      headers: { Authorization: `Bearer ${API_KEY}` }
    })
    .then(response => response.json())
    .then(data => {
      // Afficher une notification avec les recommandations
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon128.png',
        title: 'Retraites recommandées',
        message: `Nous avons trouvé ${data.recommendations.length} retraites qui pourraient vous intéresser.`,
        buttons: [{ title: 'Voir les recommandations' }]
      });
      
      // Stocker les recommandations pour les afficher plus tard
      chrome.storage.local.set({ 
        currentRecommendations: data.recommendations 
      });
    })
    .catch(error => console.error('Error fetching recommendations:', error));
  }
});

// Gérer les clics sur les notifications
chrome.notifications.onButtonClicked.addListener((notificationId, buttonIndex) => {
  if (buttonIndex === 0) {
    // Ouvrir le popup avec les recommandations
    chrome.action.openPopup();
  }
});
```

## Ressources supplémentaires

- [Documentation complète de l'API](api.md)
- [Architecture détaillée](architecture.md)
- [Guide de contribution](../CONTRIBUTING.md)
- [Exemples de code](../examples/)

## Support

Si vous rencontrez des problèmes ou avez des questions, n'hésitez pas à:
- Ouvrir une issue sur GitHub
- Contacter l'équipe de support à <EMAIL>
- Consulter la documentation en ligne à https://docs.retreatandbe.com/ai
