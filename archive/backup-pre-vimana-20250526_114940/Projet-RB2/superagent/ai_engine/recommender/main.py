from fastapi import FastAP<PERSON>, HTTPException, Query
from typing import List, Dict, Any, Optional
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from scipy.sparse import csr_matrix # For user-item matrix
import os
import random
from geopy.distance import geodesic
import numpy as np
from functools import lru_cache # Import LRU cache

app = FastAPI(
    title="Retreat And Be - Recommender API",
    description="Provides hybrid retreat recommendations (content + collaborative), with optional location re-ranking.",
    version="0.4.0" # Version incremented
)

# --- Configuration ---
DATA_DIR = "../data/processed"
RETREAT_DATA_FILE = os.path.join(DATA_DIR, "retreats_processed.parquet")
INTERACTIONS_DATA_FILE = os.path.join(DATA_DIR, "interactions_processed.parquet")

# Hybrid model weighting (alpha for content, 1-alpha for collaborative)
ALPHA = 0.6 # Give slightly more weight to content initially

# --- Global State for Model/Data ---
model_data = {
    "vectorizer": None,
    "content_similarity": None, # Renamed from similarity_matrix
    "collaborative_similarity": None,
    "hybrid_similarity": None,
    "retreats_df": None,
    "interactions_df": None, # Added interactions
    "user_item_matrix": None, # Added user-item matrix
    "id_to_index": None,
    "index_to_id": None,
    "user_id_to_index": None, # Mappings for users
    "item_id_to_index": None # Same as id_to_index, for clarity
}

# --- Model Loading and Training (at startup) ---
def load_and_train_model():
    print("Attempting to load data and train hybrid model...")
    
    # --- 1. Load Retreat Data --- 
    if not os.path.exists(RETREAT_DATA_FILE):
        print(f"ERROR: Retreat data file not found at {RETREAT_DATA_FILE}")
        return
    try:
        retreats_df = pd.read_parquet(RETREAT_DATA_FILE)
        print(f"Loaded {len(retreats_df)} retreats from {RETREAT_DATA_FILE}")
        retreats_df['description'] = retreats_df['description'].fillna('')
        # Verify lat/lon
        if 'latitude' in retreats_df.columns and 'longitude' in retreats_df.columns:
            retreats_df['latitude'] = pd.to_numeric(retreats_df['latitude'], errors='coerce')
            retreats_df['longitude'] = pd.to_numeric(retreats_df['longitude'], errors='coerce')
            retreats_df.dropna(subset=['latitude', 'longitude'], inplace=True)
        else:
             print("Warning: lat/lon columns missing or invalid.")
             
        model_data["retreats_df"] = retreats_df
        # Create item mappings (index based on the final retreat_df)
        item_id_to_index = pd.Series(range(len(retreats_df)), index=retreats_df['retreat_id']).to_dict()
        index_to_id = pd.Series(retreats_df['retreat_id'].values, index=range(len(retreats_df))).to_dict()
        model_data["id_to_index"] = item_id_to_index
        model_data["index_to_id"] = index_to_id
        model_data["item_id_to_index"] = item_id_to_index # Explicitly store item mapping

    except Exception as e:
        print(f"ERROR loading/processing retreat data: {e}")
        return # Stop if retreat data fails
        
    # --- 2. Calculate Content-Based Similarity --- 
    try:
        vectorizer = TfidfVectorizer(stop_words='english', max_features=500)
        tfidf_matrix = vectorizer.fit_transform(model_data["retreats_df"]['description'])
        content_similarity = cosine_similarity(tfidf_matrix)
        np.fill_diagonal(content_similarity, 0) # Remove self-similarity for ranking
        model_data["vectorizer"] = vectorizer
        model_data["content_similarity"] = content_similarity
        print(f"Calculated content similarity matrix: {content_similarity.shape}")
    except Exception as e:
        print(f"ERROR calculating content similarity: {e}")
        model_data["content_similarity"] = None # Mark as failed
        
    # --- 3. Load Interaction Data --- 
    if not os.path.exists(INTERACTIONS_DATA_FILE):
        print(f"Warning: Interaction data file not found at {INTERACTIONS_DATA_FILE}. Collaborative filtering disabled.")
        model_data["collaborative_similarity"] = None
    else:
        try:
            interactions_df = pd.read_parquet(INTERACTIONS_DATA_FILE)
            # Keep only relevant interactions and retreats present in retreats_df
            interactions_df = interactions_df[interactions_df['retreat_id'].isin(model_data["item_id_to_index"].keys())]
            # Consider positive interactions (example: click, bookmark, reservation)
            positive_interactions = interactions_df[interactions_df['interaction_type'].isin(['click', 'bookmark', 'reservation_start', 'reservation_complete'])]
            if positive_interactions.empty:
                 print("Warning: No positive interactions found. Collaborative filtering disabled.")
                 model_data["collaborative_similarity"] = None
            else:
                model_data["interactions_df"] = positive_interactions
                print(f"Loaded {len(positive_interactions)} positive interactions.")
                
                # --- 4. Create User-Item Matrix & Calculate Collaborative Similarity --- 
                # Create user mappings
                user_ids = positive_interactions['user_id'].unique()
                user_id_to_index = pd.Series(range(len(user_ids)), index=user_ids).to_dict()
                model_data["user_id_to_index"] = user_id_to_index
                
                # Map user and item IDs to matrix indices
                user_indices = positive_interactions['user_id'].map(user_id_to_index)
                item_indices = positive_interactions['retreat_id'].map(model_data["item_id_to_index"])
                
                # Create sparse matrix (using 1 for interaction)
                user_item_matrix = csr_matrix(([1] * len(positive_interactions), (user_indices, item_indices)),
                                            shape=(len(user_id_to_index), len(model_data["item_id_to_index"])))
                model_data["user_item_matrix"] = user_item_matrix
                print(f"Created user-item matrix: {user_item_matrix.shape}")
                
                # Calculate item-item collaborative similarity (cosine on transposed matrix)
                # Note: Ensure matrix is dense enough for meaningful similarity
                if user_item_matrix.shape[0] > 1 and user_item_matrix.nnz > 0: # Need at least 2 users and some interactions
                    collaborative_similarity = cosine_similarity(user_item_matrix.T) # Transpose to get item-item
                    np.fill_diagonal(collaborative_similarity, 0) # Remove self-similarity
                    model_data["collaborative_similarity"] = collaborative_similarity
                    print(f"Calculated collaborative similarity matrix: {collaborative_similarity.shape}")
                else:
                    print("Warning: Not enough data for collaborative similarity calculation.")
                    model_data["collaborative_similarity"] = None

        except Exception as e:
            print(f"ERROR loading interactions or calculating collaborative similarity: {e}")
            model_data["collaborative_similarity"] = None

    # --- 5. Combine Similarities for Hybrid Model --- 
    content_sim = model_data.get("content_similarity")
    collab_sim = model_data.get("collaborative_similarity")

    if content_sim is not None and collab_sim is not None and content_sim.shape == collab_sim.shape:
        hybrid_similarity = ALPHA * content_sim + (1 - ALPHA) * collab_sim
        model_data["hybrid_similarity"] = hybrid_similarity
        print("Calculated hybrid similarity matrix.")
    elif content_sim is not None:
        print("Using only content-based similarity.")
        model_data["hybrid_similarity"] = content_sim # Fallback to content-only
    else:
        print("ERROR: No similarity matrices could be calculated.")
        model_data["hybrid_similarity"] = None
        
    print("Model loading and training finished.")

@app.on_event("startup")
async def startup_event():
    load_and_train_model()

# --- Recommendation Logic (Now Hybrid) ---
@lru_cache(maxsize=128) # Add cache to the recommendation function
def get_hybrid_recommendations(target_retreat_id: str, limit: int) -> List[Dict[str, Any]]:
    """Finds retreats similar to the target_retreat_id using the hybrid similarity matrix."""
    if model_data["hybrid_similarity"] is None or model_data["id_to_index"] is None:
        print("Hybrid model not loaded. Returning empty recommendations.")
        return []

    hybrid_similarity = model_data["hybrid_similarity"]
    id_to_index = model_data["id_to_index"]
    index_to_id = model_data["index_to_id"]
    df = model_data["retreats_df"]

    if target_retreat_id not in id_to_index:
        print(f"Warning: Target retreat ID '{target_retreat_id}' not found. Cannot generate recommendations.")
        return []

    target_index = id_to_index[target_retreat_id]
    
    # Get hybrid similarity scores
    similarity_scores = list(enumerate(hybrid_similarity[target_index]))
    sorted_scores = sorted(similarity_scores, key=lambda x: x[1], reverse=True)
    
    # Get top N similar retreat indices (excluding the target itself if necessary, already handled by setting diagonal to 0)
    recommended_indices = [i for i, score in sorted_scores[:limit*3+1]] # Fetch more for re-ranking

    recommendations = []
    for index in recommended_indices:
        # Ensure index is valid and not the target itself (already handled but safe check)
        if index not in index_to_id or index == target_index:
             continue 
             
        retreat_id = index_to_id[index]
        row = df.iloc[index]
        recommendations.append({
            "retreat_id": retreat_id,
            "name": row['name'],
            "score": round(hybrid_similarity[target_index, index], 4), # Use hybrid score
            "latitude": row.get('latitude'), 
            "longitude": row.get('longitude')
        })
        if len(recommendations) >= limit * 2:
             break
             
    print(f"Generated {len(recommendations)} initial hybrid candidates based on '{target_retreat_id}'")
    return recommendations

# --- Location-Based Re-ranking --- 
def rerank_by_distance(
    recommendations: List[Dict[str, Any]], 
    user_lat: float, 
    user_lon: float,
    max_distance_km: Optional[float] = None, # Optional: Filter by max distance
    distance_weight: float = 0.1 # How much distance affects the score (higher = more penalty)
) -> List[Dict[str, Any]]:
    """Re-ranks recommendations based on distance to the user, applying a penalty."""
    if user_lat is None or user_lon is None: # Check if None
        return recommendations # No location provided

    user_location = (user_lat, user_lon)
    ranked_list = []

    for rec in recommendations:
        rec_lat = rec.get('latitude')
        rec_lon = rec.get('longitude')
        
        # Check if coordinates are valid numbers
        if rec_lat is not None and rec_lon is not None and isinstance(rec_lat, (int, float)) and isinstance(rec_lon, (int, float)):
            retreat_location = (rec_lat, rec_lon)
            try:
                distance = geodesic(user_location, retreat_location).km
                rec['distance_km'] = round(distance, 2)

                # Optional: Filter out distant results
                if max_distance_km is not None and distance > max_distance_km:
                    continue 

                # Apply distance penalty: e.g., score * exp(-weight * distance)
                penalty_factor = np.exp(-distance_weight * (distance / 100)) # Scale distance
                # Store original score if not present, handle potential missing score
                if 'original_score' not in rec:
                     rec['original_score'] = rec.get('score', 0)
                rec['score'] = round(rec['original_score'] * penalty_factor, 4)
                
                ranked_list.append(rec)

            except ValueError as e:
                print(f"Could not calculate distance for {rec.get('retreat_id', '?')}: {e}")
                # Decide how to handle items with errors: keep them with original score?
                rec['distance_km'] = None # Mark distance as unknown
                if 'original_score' in rec: # Revert score if penalty was applied partially
                     rec['score'] = rec['original_score'] 
                ranked_list.append(rec)
        else:
            # Handle items without valid location: keep them with original score?
            rec['distance_km'] = None
            if 'original_score' in rec:
                 rec['score'] = rec['original_score']
            ranked_list.append(rec)

    # Sort by the new combined score
    ranked_list.sort(key=lambda x: x.get('score', 0), reverse=True)
    
    print(f"Re-ranked {len(ranked_list)} recommendations based on distance.")
    return ranked_list

# --- API Endpoints ---
@app.get("/recommendations/{user_id}",
         tags=["Recommendations"],
         summary="Get hybrid recommendations, optionally re-ranked by location")
async def recommend_retreats(
    user_id: str, 
    limit: int = Query(5, description="Max number of recommendations to return"),
    latitude: Optional[float] = Query(None, description="User's latitude for location-based ranking"),
    longitude: Optional[float] = Query(None, description="User's longitude for location-based ranking"),
    max_distance: Optional[float] = Query(None, description="Optional: Maximum distance in km to include")
) -> List[Dict[str, Any]]:
    """
    Retrieves hybrid retreat recommendations (content + collaborative item-item).
    If latitude and longitude are provided, results are re-ranked favouring closer retreats.
    
    *Note: Still uses a simulated target retreat for initial candidate generation.*
    """
    simulated_target_retreat = "RETREAT_001"

    if model_data["hybrid_similarity"] is None:
         raise HTTPException(status_code=503, detail="Recommendation model not available.")

    # Get initial hybrid candidates
    candidates = get_hybrid_recommendations(simulated_target_retreat, limit * 3) 
    
    if not candidates and simulated_target_retreat not in model_data["id_to_index"]:
        raise HTTPException(status_code=404, detail=f"Target retreat '{simulated_target_retreat}' not found.")

    final_recommendations = candidates
    if latitude is not None and longitude is not None:
        print(f"Re-ranking recommendations for user {user_id} based on location: ({latitude}, {longitude}) max_dist={max_distance}")
        final_recommendations = rerank_by_distance(
            candidates, latitude, longitude, max_distance_km=max_distance
        )
    else:
        final_recommendations.sort(key=lambda x: x.get('score', 0), reverse=True)

    return final_recommendations[:limit]

# --- Health Check --- 
@app.get("/health", tags=["Health"], summary="Health check endpoint")
async def health_check():
    model_status = {
        "content_similarity_loaded": model_data["content_similarity"] is not None,
        "collaborative_similarity_loaded": model_data["collaborative_similarity"] is not None,
        "hybrid_similarity_loaded": model_data["hybrid_similarity"] is not None,
        "location_data_present": False
    }
    if model_status["hybrid_similarity_loaded"] and model_data["retreats_df"] is not None:
         model_status["location_data_present"] = 'latitude' in model_data["retreats_df"].columns
        
    status = "ok" if model_status["hybrid_similarity_loaded"] else "degraded"
    if status == "ok" and not model_status["content_similarity_loaded"]:
        status = "degraded (content sim failed)"
    if status == "ok" and not model_status["collaborative_similarity_loaded"]:
         status = "degraded (collab sim failed or no data)"
         
    return {"status": status, **model_status}

# If running directly with python main.py (for local testing without docker)
if __name__ == "__main__":
    import uvicorn
    # Ensure data exists before trying to run
    required_files = [RETREAT_DATA_FILE, INTERACTIONS_DATA_FILE]
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
         print("-" * 50)
         print("ERROR: Required data file(s) not found:")
         for f in missing_files:
              print(f"  - {f}")
         print("\nPlease run the data generation scripts first:")
         print("  cd ai_engine/data_processing")
         print("  python fetch_retreat_data.py")
         print("  python fetch_user_interactions.py")
         print("  python preprocess_data.py")
         print("-" * 50)
         exit(1)
         
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=False) # reload=False is usually better for startup events 