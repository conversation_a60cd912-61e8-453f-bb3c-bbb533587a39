"""
Virtual Coach API pour l'application mobile Retreat And Be.
Ce service fournit une expérience de coaching personnalisée pour les utilisateurs.
"""
import os
import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union

import redis
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Configuration
API_VERSION = "1.0.0"
REDIS_HOST = os.getenv("REDIS_HOST", "redis")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "")
REDIS_DB = int(os.getenv("REDIS_DB", "0"))

# Initialisation de l'application FastAPI
app = FastAPI(
    title="Retreat And Be Virtual Coach API",
    description="API pour le coaching virtuel personnalisé dans l'application mobile",
    version=API_VERSION,
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # En production, restreindre aux domaines autorisés
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialisation de Redis pour le stockage des données de session
redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    password=REDIS_PASSWORD,
    db=REDIS_DB,
    decode_responses=True,
)

# Modèles de données
class UserProfile(BaseModel):
    user_id: str
    name: Optional[str] = None
    wellness_goals: List[str] = []
    experience_levels: Dict[str, str] = {}  # Ex: {"yoga": "beginner", "meditation": "intermediate"}
    preferences: Dict[str, Any] = {}
    restrictions: List[str] = []

class CoachSession(BaseModel):
    session_id: str
    user_id: str
    current_phase: str = "onboarding"  # onboarding, assessment, coaching, reflection
    start_date: datetime = Field(default_factory=datetime.now)
    end_date: Optional[datetime] = None
    goals: List[str] = []
    progress: Dict[str, Any] = {}
    metrics: Dict[str, Any] = {}

class CoachInteraction(BaseModel):
    interaction_id: str
    session_id: str
    user_id: str
    timestamp: datetime = Field(default_factory=datetime.now)
    interaction_type: str  # message, assessment, exercise, reflection
    content: Dict[str, Any]
    user_response: Optional[Dict[str, Any]] = None

class CoachResponse(BaseModel):
    response_id: str
    interaction_id: Optional[str] = None
    session_id: str
    user_id: str
    timestamp: datetime = Field(default_factory=datetime.now)
    response_type: str  # message, assessment_result, exercise_feedback, plan
    content: Dict[str, Any]
    follow_up_options: List[Dict[str, str]] = []
    next_steps: List[Dict[str, Any]] = []

class Exercise(BaseModel):
    exercise_id: str
    name: str
    description: str
    category: str  # yoga, meditation, breathing, journaling, etc.
    intensity: str = "moderate"  # light, moderate, intense
    duration_minutes: int
    instructions: List[str]
    benefits: List[str]
    video_url: Optional[str] = None
    image_url: Optional[str] = None
    equipment_needed: List[str] = []
    modifications: Dict[str, List[str]] = {}

class CoachingPlan(BaseModel):
    plan_id: str
    user_id: str
    start_date: datetime
    end_date: datetime
    focus_areas: List[str]
    daily_exercises: Dict[str, List[Exercise]] = {}  # Jour de la semaine -> liste d'exercices
    affirmations: List[str] = []
    reflections: List[Dict[str, str]] = []
    metrics_to_track: List[str] = []

class CreateSessionRequest(BaseModel):
    user_id: str
    goals: List[str] = []
    initial_preferences: Dict[str, Any] = {}

class UserMessage(BaseModel):
    user_id: str
    session_id: str
    message: str
    context: Dict[str, Any] = {}

class UserExerciseLog(BaseModel):
    user_id: str
    session_id: str
    exercise_id: str
    completed_at: datetime = Field(default_factory=datetime.now)
    duration_minutes: int
    difficulty_rating: int  # 1-5
    mood_before: str
    mood_after: str
    notes: str = ""

class UserProgressUpdate(BaseModel):
    user_id: str
    session_id: str
    metrics: Dict[str, Any]
    reflections: Dict[str, str] = {}
    challenges: List[str] = []
    achievements: List[str] = []

# Base de données fictive d'exercices
EXERCISES_DB = {
    "yoga_exercises": [
        {
            "exercise_id": "yoga_sun_salutation",
            "name": "Salutation au Soleil",
            "description": "Série de postures fluides pour échauffer le corps et centrer l'esprit",
            "category": "yoga",
            "intensity": "moderate",
            "duration_minutes": 10,
            "instructions": [
                "Commencez en position debout, Tadasana",
                "Inspirez, levez les bras au-dessus de la tête",
                "Expirez, pliez-vous vers l'avant en Uttanasana",
                "Continuez avec la séquence complète de mouvements"
            ],
            "benefits": [
                "Augmente la flexibilité",
                "Renforce les muscles",
                "Améliore la circulation",
                "Calme l'esprit"
            ],
            "video_url": "https://retreatandbe.com/videos/sun_salutation.mp4",
            "image_url": "https://retreatandbe.com/images/sun_salutation.jpg",
            "equipment_needed": ["tapis de yoga"],
            "modifications": {
                "débutant": ["Gardez les genoux légèrement pliés", "Utilisez des blocs pour Uttanasana"],
                "intermédiaire": ["Ajoutez des variations de Chaturanga"],
                "avancé": ["Intégrez des sauts entre les positions"]
            }
        },
        {
            "exercise_id": "yoga_warrior",
            "name": "Posture du Guerrier",
            "description": "Série de postures debout pour la force et la stabilité",
            "category": "yoga",
            "intensity": "moderate",
            "duration_minutes": 15,
            "instructions": [
                "Commencez en position debout",
                "Écartez largement les jambes",
                "Tournez le pied droit vers l'extérieur à 90 degrés",
                "Pliez le genou droit à 90 degrés",
                "Étendez les bras parallèlement au sol"
            ],
            "benefits": [
                "Renforce les jambes",
                "Améliore l'équilibre",
                "Ouvre les hanches",
                "Développe la concentration"
            ],
            "video_url": "https://retreatandbe.com/videos/warrior_poses.mp4",
            "image_url": "https://retreatandbe.com/images/warrior_pose.jpg",
            "equipment_needed": ["tapis de yoga"],
            "modifications": {
                "débutant": ["Réduisez la profondeur de la flexion du genou", "Placez-vous près d'un mur pour le soutien"],
                "intermédiaire": ["Maintenez la posture plus longtemps"],
                "avancé": ["Ajoutez une torsion du torse"]
            }
        }
    ],
    "meditation_exercises": [
        {
            "exercise_id": "med_breath_awareness",
            "name": "Méditation de Pleine Conscience de la Respiration",
            "description": "Pratique de base pour développer l'attention à la respiration",
            "category": "meditation",
            "intensity": "light",
            "duration_minutes": 10,
            "instructions": [
                "Asseyez-vous confortablement avec le dos droit",
                "Fermez les yeux ou maintenez un regard doux",
                "Portez attention à votre respiration naturelle",
                "Observez le flux et reflux de l'air",
                "Lorsque l'esprit s'égare, ramenez gentiment l'attention à la respiration"
            ],
            "benefits": [
                "Réduit le stress",
                "Améliore la concentration",
                "Développe la conscience du moment présent",
                "Favorise la clarté mentale"
            ],
            "video_url": "https://retreatandbe.com/videos/breath_meditation.mp4",
            "image_url": "https://retreatandbe.com/images/breath_meditation.jpg",
            "equipment_needed": [],
            "modifications": {
                "débutant": ["Commencez par 5 minutes", "Utilisez un minuteur doux"],
                "intermédiaire": ["Prolongez à 15-20 minutes"],
                "avancé": ["Pratiquez sans minuteur, en s'appuyant sur l'intuition"]
            }
        },
        {
            "exercise_id": "med_loving_kindness",
            "name": "Méditation de l'Amour Bienveillant",
            "description": "Pratique pour cultiver la compassion et la bienveillance",
            "category": "meditation",
            "intensity": "light",
            "duration_minutes": 15,
            "instructions": [
                "Asseyez-vous confortablement et fermez les yeux",
                "Commencez par vous souhaiter du bien-être : 'Que je sois heureux, en bonne santé, en paix'",
                "Étendez ces vœux à un être cher",
                "Puis à une personne neutre",
                "Ensuite à une personne difficile",
                "Finalement à tous les êtres"
            ],
            "benefits": [
                "Développe la compassion",
                "Réduit les émotions négatives",
                "Améliore les relations",
                "Favorise le sentiment de connexion"
            ],
            "video_url": "https://retreatandbe.com/videos/loving_kindness.mp4",
            "image_url": "https://retreatandbe.com/images/loving_kindness.jpg",
            "equipment_needed": [],
            "modifications": {
                "débutant": ["Commencez seulement avec vous-même et un être cher"],
                "intermédiaire": ["Incluez la personne neutre et difficile"],
                "avancé": ["Pratiquez pour tous les êtres dans toutes les directions"]
            }
        }
    ],
    "breathing_exercises": [
        {
            "exercise_id": "breath_4_7_8",
            "name": "Respiration 4-7-8",
            "description": "Technique de respiration calmante pour la relaxation et le sommeil",
            "category": "breathing",
            "intensity": "light",
            "duration_minutes": 5,
            "instructions": [
                "Asseyez-vous confortablement ou allongez-vous",
                "Inspirez par le nez pendant 4 secondes",
                "Retenez votre respiration pendant 7 secondes",
                "Expirez lentement par la bouche pendant 8 secondes",
                "Répétez le cycle 4 fois au début, augmentez progressivement"
            ],
            "benefits": [
                "Calme le système nerveux",
                "Réduit l'anxiété",
                "Favorise l'endormissement",
                "Abaisse la tension artérielle"
            ],
            "video_url": "https://retreatandbe.com/videos/4_7_8_breathing.mp4",
            "image_url": "https://retreatandbe.com/images/4_7_8_breathing.jpg",
            "equipment_needed": [],
            "modifications": {
                "débutant": ["Ajustez le timing si nécessaire, l'important est le ratio"],
                "intermédiaire": ["Augmentez le nombre de cycles"],
                "avancé": ["Pratiquez dans des situations stressantes pour calmer rapidement le mental"]
            }
        }
    ]
}

# Helpers et utilitaires
def generate_id(prefix: str) -> str:
    """Génère un ID unique avec préfixe."""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    random_suffix = random.randint(1000, 9999)
    return f"{prefix}_{timestamp}_{random_suffix}"

def get_coach_session(session_id: str, user_id: str) -> Dict[str, Any]:
    """Récupère une session de coaching depuis Redis."""
    session_key = f"coach_session:{session_id}:{user_id}"
    session_data = redis_client.get(session_key)
    if not session_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session de coaching {session_id} non trouvée pour l'utilisateur {user_id}"
        )
    return json.loads(session_data)

def store_coach_session(session: CoachSession) -> None:
    """Stocke une session de coaching dans Redis."""
    session_key = f"coach_session:{session.session_id}:{session.user_id}"
    redis_client.set(session_key, json.dumps(session.dict()), ex=60*60*24*30)  # expire après 30 jours

def create_exercise_plan(user_id: str, goals: List[str], preferences: Dict[str, Any]) -> CoachingPlan:
    """Crée un plan d'exercices personnalisé basé sur les objectifs et préférences."""
    # Sélection d'exercices appropriés en fonction des objectifs
    plan_exercises = {}
    days_of_week = ["lundi", "mardi", "mercredi", "jeudi", "vendredi", "samedi", "dimanche"]
    
    # Mapping des objectifs aux catégories d'exercices
    goal_to_category = {
        "réduire_stress": ["meditation", "breathing"],
        "améliorer_sommeil": ["breathing", "meditation"],
        "augmenter_flexibilité": ["yoga"],
        "augmenter_énergie": ["yoga", "breathing"],
        "trouver_équilibre": ["meditation", "yoga"],
        "améliorer_posture": ["yoga"]
    }
    
    # Déterminer les catégories d'exercices basées sur les objectifs
    categories = set()
    for goal in goals:
        if goal in goal_to_category:
            categories.update(goal_to_category[goal])
    
    if not categories:
        categories = {"yoga", "meditation", "breathing"}  # Par défaut
    
    # Créer un plan d'exercices pour chaque jour
    for day in days_of_week:
        day_exercises = []
        
        for category in categories:
            exercise_list = EXERCISES_DB.get(f"{category}_exercises", [])
            if exercise_list:
                # Sélectionner au hasard un exercice dans cette catégorie
                selected_exercise = random.choice(exercise_list)
                day_exercises.append(Exercise(**selected_exercise))
        
        plan_exercises[day] = day_exercises[:2]  # Limiter à 2 exercices par jour
    
    # Créer le plan de coaching
    plan = CoachingPlan(
        plan_id=generate_id("plan"),
        user_id=user_id,
        start_date=datetime.now(),
        end_date=datetime.now() + timedelta(days=7),
        focus_areas=list(categories),
        daily_exercises=plan_exercises,
        affirmations=[
            "Je prends soin de mon corps et de mon esprit chaque jour",
            "Je progresse à mon propre rythme avec bienveillance",
            "Chaque respiration me rapproche de mes objectifs de bien-être"
        ],
        reflections=[
            {"question": "Comment vous sentez-vous après votre pratique aujourd'hui?"},
            {"question": "Qu'avez-vous appris sur vous-même aujourd'hui?"},
            {"question": "Quels obstacles avez-vous rencontrés dans votre pratique?"}
        ],
        metrics_to_track=[
            "humeur", "qualité_sommeil", "niveau_énergie", "niveau_stress"
        ]
    )
    
    return plan

def generate_coach_message(user_id: str, session_id: str, context: Dict[str, Any]) -> CoachResponse:
    """Génère un message de coaching basé sur le contexte."""
    # Simuler différents types de messages de coaching
    message_types = [
        "encouragement", "instruction", "feedback", "question", "suggestion"
    ]
    
    message_type = random.choice(message_types)
    
    # Messages prédéfinis par type
    messages = {
        "encouragement": [
            "Excellent travail jusqu'à présent ! Continuez comme ça.",
            "Je vois vos progrès, c'est vraiment impressionnant !",
            "Chaque petit pas compte. Vous faites un excellent travail."
        ],
        "instruction": [
            "Essayez de porter plus d'attention à votre respiration pendant l'exercice.",
            "Rappelez-vous de garder le dos droit pendant cette posture.",
            "Prenez un moment pour vous recentrer avant de commencer."
        ],
        "feedback": [
            "Votre constance est remarquable. C'est la clé du progrès.",
            "Je remarque que vous progressez bien dans les exercices de respiration.",
            "Votre pratique devient plus fluide, c'est un excellent signe."
        ],
        "question": [
            "Comment vous sentez-vous après cet exercice ?",
            "Qu'avez-vous trouvé le plus difficile aujourd'hui ?",
            "Quels changements avez-vous remarqués depuis que vous avez commencé ?"
        ],
        "suggestion": [
            "Vous pourriez bénéficier d'ajouter 5 minutes de méditation le matin.",
            "Essayez la technique de respiration 4-7-8 avant de dormir.",
            "Considérez l'ajout d'étirements doux après votre séance."
        ]
    }
    
    # Sélectionner un message aléatoire du type choisi
    content = {
        "message_type": message_type,
        "text": random.choice(messages[message_type])
    }
    
    # Options de suivi basées sur le type de message
    follow_up_options = []
    if message_type == "question":
        follow_up_options = [
            {"id": "positive", "text": "Je me sens bien"},
            {"id": "neutral", "text": "Je ne ressens pas de changement particulier"},
            {"id": "challenging", "text": "C'était difficile pour moi"}
        ]
    elif message_type == "suggestion":
        follow_up_options = [
            {"id": "accept", "text": "J'aimerais essayer"},
            {"id": "later", "text": "Peut-être plus tard"},
            {"id": "info", "text": "Dites m'en plus"}
        ]
    
    # Générer les prochaines étapes
    next_steps = [
        {"type": "exercise", "id": "next_planned_exercise", "text": "Continuer avec l'exercice prévu"}
    ]
    
    if random.random() > 0.7:  # 30% de chance d'avoir une réflexion
        next_steps.append({
            "type": "reflection", 
            "id": "daily_reflection", 
            "text": "Prendre un moment pour réfléchir à votre journée"
        })
    
    # Créer et retourner la réponse
    return CoachResponse(
        response_id=generate_id("resp"),
        session_id=session_id,
        user_id=user_id,
        response_type="message",
        content=content,
        follow_up_options=follow_up_options,
        next_steps=next_steps
    )

# Routes API

@app.get("/", include_in_schema=False)
async def root():
    return {
        "service": "Retreat And Be Virtual Coach API",
        "version": API_VERSION,
        "status": "active",
        "docs": "/docs"
    }

@app.get("/health", tags=["Système"])
async def health_check():
    """Vérifie l'état de santé de l'API."""
    try:
        redis_online = redis_client.ping()
    except Exception:
        redis_online = False
    
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": True,
            "redis": redis_online
        },
        "version": API_VERSION
    }

@app.post("/sessions", response_model=CoachSession, tags=["Sessions"])
async def create_coach_session(request: CreateSessionRequest):
    """Crée une nouvelle session de coaching pour un utilisateur."""
    session_id = generate_id("sess")
    
    session = CoachSession(
        session_id=session_id,
        user_id=request.user_id,
        goals=request.goals,
        progress={
            "onboarding_completed": False,
            "assessment_completed": False,
            "days_active": 0,
            "exercises_completed": 0
        },
        metrics={
            "start_date": datetime.now().isoformat(),
            "last_active": datetime.now().isoformat()
        }
    )
    
    # Stocker la session
    store_coach_session(session)
    
    return session

@app.get("/sessions/{session_id}", response_model=CoachSession, tags=["Sessions"])
async def get_session(session_id: str, user_id: str):
    """Récupère les détails d'une session de coaching."""
    session = get_coach_session(session_id, user_id)
    return CoachSession(**session)

@app.post("/sessions/{session_id}/message", response_model=CoachResponse, tags=["Interactions"])
async def send_message(session_id: str, message: UserMessage):
    """Envoie un message au coach virtuel et reçoit une réponse."""
    # Vérifier que la session existe
    session = get_coach_session(session_id, message.user_id)
    
    # Enregistrer le message de l'utilisateur
    interaction_id = generate_id("int")
    interaction = CoachInteraction(
        interaction_id=interaction_id,
        session_id=session_id,
        user_id=message.user_id,
        interaction_type="message",
        content={"text": message.message, "context": message.context}
    )
    
    # Stocker l'interaction (ceci serait persisté dans une base de données réelle)
    # Pour cette démo, nous ne persistons pas les interactions
    
    # Générer une réponse du coach
    coach_response = generate_coach_message(
        message.user_id, 
        session_id, 
        message.context
    )
    coach_response.interaction_id = interaction_id
    
    # Mettre à jour les métriques de la session
    session_obj = CoachSession(**session)
    session_obj.metrics["last_active"] = datetime.now().isoformat()
    session_obj.metrics["message_count"] = session.get("metrics", {}).get("message_count", 0) + 1
    
    # Stocker la session mise à jour
    store_coach_session(session_obj)
    
    return coach_response

@app.post("/sessions/{session_id}/plan", response_model=CoachingPlan, tags=["Plans"])
async def create_coaching_plan(session_id: str, user_id: str, goals: List[str] = [], preferences: Dict[str, Any] = {}):
    """Crée un plan de coaching personnalisé pour l'utilisateur."""
    # Vérifier que la session existe
    session = get_coach_session(session_id, user_id)
    
    # Utiliser les objectifs de la session s'ils ne sont pas fournis
    if not goals and "goals" in session:
        goals = session["goals"]
    
    # Créer le plan de coaching
    plan = create_exercise_plan(user_id, goals, preferences)
    
    # Stocker le plan (dans un système réel)
    # Pour cette démo, nous ne persistons pas les plans
    
    # Mettre à jour la session
    session_obj = CoachSession(**session)
    session_obj.progress["plan_created"] = True
    session_obj.metrics["plan_created_at"] = datetime.now().isoformat()
    
    # Stocker la session mise à jour
    store_coach_session(session_obj)
    
    return plan

@app.post("/sessions/{session_id}/exercise/log", tags=["Exercices"])
async def log_exercise(session_id: str, log: UserExerciseLog):
    """Enregistre la complétion d'un exercice par l'utilisateur."""
    # Vérifier que la session existe
    session = get_coach_session(session_id, log.user_id)
    
    # Mettre à jour les métriques de la session
    session_obj = CoachSession(**session)
    session_obj.progress["exercises_completed"] = session.get("progress", {}).get("exercises_completed", 0) + 1
    session_obj.metrics["last_active"] = datetime.now().isoformat()
    
    if "exercise_logs" not in session_obj.metrics:
        session_obj.metrics["exercise_logs"] = []
    
    # Ajouter le log d'exercice
    session_obj.metrics["exercise_logs"].append({
        "exercise_id": log.exercise_id,
        "completed_at": log.completed_at.isoformat(),
        "duration_minutes": log.duration_minutes,
        "difficulty_rating": log.difficulty_rating,
        "mood_before": log.mood_before,
        "mood_after": log.mood_after,
        "notes": log.notes
    })
    
    # Stocker la session mise à jour
    store_coach_session(session_obj)
    
    return {
        "status": "success",
        "message": "Exercice enregistré avec succès",
        "exercise_id": log.exercise_id,
        "completed_at": log.completed_at.isoformat()
    }

@app.get("/exercises", tags=["Exercices"])
async def get_exercises(category: Optional[str] = None, intensity: Optional[str] = None, max_duration: Optional[int] = None):
    """Récupère les exercices disponibles avec filtrage optionnel."""
    all_exercises = []
    
    # Collecter tous les exercices de toutes les catégories
    for category_key, exercises in EXERCISES_DB.items():
        all_exercises.extend(exercises)
    
    # Appliquer les filtres
    filtered_exercises = all_exercises
    
    if category:
        filtered_exercises = [e for e in filtered_exercises if e["category"] == category]
    
    if intensity:
        filtered_exercises = [e for e in filtered_exercises if e["intensity"] == intensity]
    
    if max_duration:
        filtered_exercises = [e for e in filtered_exercises if e["duration_minutes"] <= max_duration]
    
    return {"exercises": filtered_exercises, "count": len(filtered_exercises)}

@app.get("/exercises/{exercise_id}", response_model=Exercise, tags=["Exercices"])
async def get_exercise(exercise_id: str):
    """Récupère les détails d'un exercice spécifique."""
    # Chercher l'exercice dans toutes les catégories
    for category, exercises in EXERCISES_DB.items():
        for exercise in exercises:
            if exercise["exercise_id"] == exercise_id:
                return Exercise(**exercise)
    
    # Si l'exercice n'est pas trouvé
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"Exercice {exercise_id} non trouvé"
    )

@app.post("/sessions/{session_id}/progress", tags=["Progression"])
async def update_progress(session_id: str, update: UserProgressUpdate):
    """Met à jour la progression de l'utilisateur dans sa session de coaching."""
    # Vérifier que la session existe
    session = get_coach_session(session_id, update.user_id)
    
    # Mettre à jour les métriques de la session
    session_obj = CoachSession(**session)
    
    # Mettre à jour les métriques
    for key, value in update.metrics.items():
        session_obj.metrics[key] = value
    
    # Ajouter les réflexions
    if "reflections" not in session_obj.metrics:
        session_obj.metrics["reflections"] = []
    
    if update.reflections:
        session_obj.metrics["reflections"].append({
            "timestamp": datetime.now().isoformat(),
            "reflections": update.reflections
        })
    
    # Enregistrer les défis
    if "challenges" not in session_obj.metrics:
        session_obj.metrics["challenges"] = []
    
    session_obj.metrics["challenges"].extend(update.challenges)
    
    # Enregistrer les accomplissements
    if "achievements" not in session_obj.metrics:
        session_obj.metrics["achievements"] = []
    
    session_obj.metrics["achievements"].extend(update.achievements)
    
    # Mettre à jour la date de dernière activité
    session_obj.metrics["last_active"] = datetime.now().isoformat()
    
    # Stocker la session mise à jour
    store_coach_session(session_obj)
    
    return {
        "status": "success",
        "message": "Progression mise à jour avec succès",
        "session_id": session_id,
        "updated_at": datetime.now().isoformat()
    }

@app.get("/demo-session", tags=["Démo"])
async def create_demo_session():
    """Crée une session de démo avec des données pré-remplies pour les tests."""
    user_id = f"demo_user_{random.randint(1000, 9999)}"
    session_id = generate_id("sess")
    
    session = CoachSession(
        session_id=session_id,
        user_id=user_id,
        current_phase="coaching",
        goals=["réduire_stress", "améliorer_sommeil"],
        progress={
            "onboarding_completed": True,
            "assessment_completed": True,
            "days_active": 3,
            "exercises_completed": 5
        },
        metrics={
            "start_date": datetime.now().isoformat(),
            "last_active": datetime.now().isoformat(),
            "stress_level": 6,
            "sleep_quality": 7
        }
    )
    
    # Stocker la session
    store_coach_session(session)
    
    # Créer un plan pour cette session
    plan = create_exercise_plan(
        user_id=user_id,
        goals=session.goals,
        preferences={}
    )
    
    return {
        "message": "Session de démo créée avec succès",
        "user_id": user_id,
        "session_id": session_id,
        "sample_exercises": plan.daily_exercises.get("lundi", []),
        "next_step": "Utilisez cette session pour tester d'autres endpoints"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5005) 