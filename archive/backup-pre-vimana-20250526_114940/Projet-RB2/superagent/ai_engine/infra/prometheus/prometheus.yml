global:
  scrape_interval: 15s # Default scrape interval

scrape_configs:
  - job_name: 'prometheus'
    # metrics_path defaults to '/metrics'
    # scheme defaults to 'http'.
    static_configs:
      - targets: ['localhost:9090']

  # Add other jobs here to scrape your custom services (recommender, chatbot)
  # Example for a service exposing metrics on port 8000:
  # - job_name: 'recommender'
  #   static_configs:
  #     - targets: ['recommender-api:8000'] # Use service name from docker-compose 