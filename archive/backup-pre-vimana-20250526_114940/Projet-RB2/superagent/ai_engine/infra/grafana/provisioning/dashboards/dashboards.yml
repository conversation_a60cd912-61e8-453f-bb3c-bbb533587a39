# config file version
apiVersion: 1

providers:
  # <string> an unique provider name
  - name: 'default'
    # <int> Org id. Default is 1
    orgId: 1
    # <string> name of the dashboard folder. Required
    folder: 'General'
    # <string> folder UID. will be automatically generated if not specified
    # folderUid: ''
    # <string> provider type. Default is file
    type: file
    # <bool> disable dashboard deletion
    disableDeletion: false
    # <bool> enable dashboard editing
    editable: true
    # <bool> allow updating provisioned dashboards from the UI
    allowUiUpdates: true
    options:
      # <string, required> path to dashboard files on disk. Required when using the file provider
      path: /etc/grafana/provisioning/dashboards/ # Path inside the container
      # <bool> use folder names from filesystem to create folders in Grafana
      # foldersFromFilesStructure: true 