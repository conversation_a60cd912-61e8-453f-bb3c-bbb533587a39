version: '3.9'

services:
  recommender-api:
    build: ./recommender
    container_name: recommender_api
    ports:
      - "8000:8000" # Example port, adjust as needed
    networks:
      - ai_network
    restart: unless-stopped

  chatbot-api:
    build: ./chatbot
    container_name: chatbot_api
    ports:
      - "8001:8001" # Example port, adjust as needed
    env_file:
      - ./chatbot/.env
    networks:
      - ai_network
    restart: unless-stopped

  mlflow-server:
    image: ghcr.io/mlflow/mlflow:latest # Use official MLflow image
    container_name: mlflow_server
    ports:
      - "5000:5000"
    volumes:
      - ./mlruns:/mlruns # Store runs locally for simplicity initially
    command: mlflow server --host 0.0.0.0 --port 5000 --backend-store-uri file:///mlruns --default-artifact-root file:///mlruns/artifacts
    networks:
      - ai_network
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest # Use official Prometheus image
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infra/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    networks:
      - ai_network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest # Use official Grafana image
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      # Mount provisioning files
      - ./infra/grafana/provisioning/datasources:/etc/grafana/provisioning/datasources
      - ./infra/grafana/provisioning/dashboards:/etc/grafana/provisioning/dashboards 
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin # Change this in production!
      - GF_USERS_ALLOW_SIGN_UP=false
      # Environment variables can also configure datasources, but file provisioning is often preferred
    networks:
      - ai_network
    restart: unless-stopped
    depends_on:
      - prometheus

  content-generator-api:
    build: ./content_generator
    container_name: content_generator_api
    ports:
      - "8002:8002"
    env_file:
      - ./content_generator/.env # Load API key from .env file
    networks:
      - ai_network
    restart: unless-stopped

  analytics-engine-api:
    build: ./analytics_engine
    container_name: analytics_engine_api
    ports:
      - "8003:8003"
    # Mount the data volume so the service can read the processed data
    volumes:
      - ./data:/app/data # Mount local data dir into container's /app/data
    networks:
      - ai_network
    restart: unless-stopped

  feature-flags-api:
    build: ./feature_flags
    container_name: feature_flags_api
    ports:
      - "8004:8004"
    networks:
      - ai_network
    restart: unless-stopped

  interface-personalization-api:
    build: ./interface_personalization
    container_name: interface_personalization_api
    ports:
      - "8006:8006"
    networks:
      - ai_network
    restart: unless-stopped

  dynamic-pricing-api:
    build: ./dynamic_pricing
    container_name: dynamic_pricing_api
    ports:
      - "8007:8007"
    networks:
      - ai_network
    restart: unless-stopped

  virtual-coach-api:
    build: ./virtual_coach
    container_name: virtual_coach_api
    ports:
      - "5005:5005"
    depends_on:
      - redis  # Le service Virtual Coach a besoin de Redis
    networks:
      - ai_network
    restart: unless-stopped

  redis:
    image: redis:alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai_network
    restart: unless-stopped

networks:
  ai_network:
    driver: bridge

volumes:
  prometheus_data:
  grafana_data:
  redis_data:
  # Add mlruns volume if not already present from mlflow definition
  # mlruns:

# Note: For recommender-api and chatbot-api, empty Dockerfile and requirements.txt
# need to be created in their respective directories ('recommender/' and 'chatbot/').
# Also, the prometheus config needs to be created at 'infra/prometheus/prometheus.yml'. 