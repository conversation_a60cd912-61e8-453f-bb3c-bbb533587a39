import os
import requests
from fastapi import FastAPI, HTTPException, Body
from pydantic import BaseModel, Field
from typing import List, Optional
from dotenv import load_dotenv
from functools import lru_cache # Import LRU cache

# Load environment variables from .env file
load_dotenv()

DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions" # Standard chat completions endpoint

app = FastAPI(
    title="Retreat And Be - Content Generator API",
    description="Provides AI-powered content generation assistance using Deepseek.",
    version="0.1.0"
)

class GenerationRequest(BaseModel):
    topic: str = Field(..., description="Main topic or title for the content (e.g., 'Yoga and Meditation Retreat')")
    keywords: List[str] = Field(default_factory=list, description="Optional keywords to include (e.g., ['relaxing', 'beach', 'beginners'])")
    target_audience: Optional[str] = Field(None, description="Optional target audience (e.g., 'stressed professionals', 'families')")
    desired_length: str = Field("short snippet", description="Desired length or type of content (e.g., 'short snippet', ' catchy headline', 'paragraph')")
    # Add other parameters like tone, style etc. as needed

class GenerationResponse(BaseModel):
    generated_text: str

# --- Deepseek API Call Logic ---
@lru_cache(maxsize=64) # Cache based on prompt and model
def call_deepseek_api(prompt: str, model: str = "deepseek-chat") -> str:
    """Calls the Deepseek Chat Completions API."""
    if not DEEPSEEK_API_KEY:
        raise HTTPException(status_code=500, detail="Deepseek API key not configured.")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
    }

    data = {
        "model": model,
        "messages": [
            {"role": "system", "content": "You are a helpful assistant for Retreat And Be, a wellness retreat platform. Generate concise and appealing content snippets for retreat descriptions based on the user's request. Focus on the benefits and experience."}, 
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 150, # Adjust as needed
        "temperature": 0.7 # Adjust for creativity vs. consistency
    }

    try:
        response = requests.post(DEEPSEEK_API_URL, headers=headers, json=data, timeout=30) # Add timeout
        response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
        
        result = response.json()
        
        if "choices" in result and len(result["choices"]) > 0:
            generated_content = result["choices"][0]["message"]["content"].strip()
            return generated_content
        else:
            print(f"Warning: Deepseek API response did not contain expected choices. Response: {result}")
            raise HTTPException(status_code=500, detail="Failed to generate content from Deepseek API. Unexpected response format.")
            
    except requests.exceptions.RequestException as e:
        print(f"Error calling Deepseek API: {e}")
        raise HTTPException(status_code=503, detail=f"Failed to connect to Deepseek API: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred while generating content.")

# --- API Endpoint ---

@app.post("/generate/description_snippet", 
          tags=["Content Generation"],
          summary="Generate a short description snippet for a retreat")
async def generate_snippet(request: GenerationRequest) -> GenerationResponse:
    """
    Generates a descriptive snippet based on the provided topic and keywords using the Deepseek API.
    """
    # Construct the prompt for Deepseek
    prompt = f"Generate a {request.desired_length} for a wellness retreat about '{request.topic}'."
    if request.keywords:
        prompt += f" Include keywords like: {', '.join(request.keywords)}."
    if request.target_audience:
        prompt += f" Target audience is {request.target_audience}."
    prompt += " Focus on the benefits and the unique experience offered."
    
    print(f"Generated Prompt for Deepseek: {prompt}")
    
    generated_text = call_deepseek_api(prompt)
    
    return GenerationResponse(generated_text=generated_text)

@app.get("/health", tags=["Health"], summary="Health check endpoint")
async def health_check():
    """Simple health check to confirm the API is running."""
    # Optionally, check if API key is present
    key_present = bool(DEEPSEEK_API_KEY)
    return {"status": "ok", "api_key_configured": key_present}

# If running directly (for local testing without docker, ensure .env is in the same directory)
if __name__ == "__main__":
    import uvicorn
    if not DEEPSEEK_API_KEY:
        print("-"*50)
        print("WARNING: DEEPSEEK_API_KEY environment variable not set.")
        print("Please create a .env file in the current directory with your key:")
        print("DEEPSEEK_API_KEY=YOUR_KEY_HERE")
        print("The API will likely fail without the key.")
        print("-"*50)
        
    uvicorn.run("main:app", host="0.0.0.0", port=8002, reload=True) 