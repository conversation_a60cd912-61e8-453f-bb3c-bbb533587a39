# Base Python image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8002

# Command to run the application
# Loads .env file automatically thanks to python-dotenv being imported in main.py
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8002"] 