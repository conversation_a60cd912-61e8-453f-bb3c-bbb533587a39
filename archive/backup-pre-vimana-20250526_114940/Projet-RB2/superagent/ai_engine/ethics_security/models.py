"""
Modèles de données pour le module de sécurité et d'éthique.
"""
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator

# Énumérations
class AuditStatus(str, Enum):
    """État d'un audit."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class AuditType(str, Enum):
    """Type d'audit."""
    BIAS = "bias_audit"
    SECURITY = "security_audit"
    PRIVACY = "privacy_audit"
    TRANSPARENCY = "transparency_audit"
    GENERAL = "general_audit"

class RiskLevel(str, Enum):
    """Niveau de risque."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ModelComponent(str, Enum):
    """Composant modèle concerné."""
    RECOMMENDER = "recommender"
    CHATBOT = "chatbot"
    VIRTUAL_COACH = "virtual_coach"
    CONTENT_GENERATOR = "content_generator"
    ANALYTICS = "analytics"
    PRICING = "dynamic_pricing"
    PERSONALIZATION = "interface_personalization"
    ALL = "all_components"

# Modèles de données
class AuditItem(BaseModel):
    """Élément individuel d'audit."""
    category: str
    name: str
    description: str
    result: Optional[Any] = None
    passing: Optional[bool] = None
    risk_level: Optional[RiskLevel] = None
    details: Optional[Dict[str, Any]] = None
    recommendations: Optional[List[str]] = None

class BiasMetric(BaseModel):
    """Métrique de biais pour un attribut protégé."""
    attribute: str  # Par exemple "gender", "age", etc.
    metric_name: str  # Par exemple "demographic_parity", "equal_opportunity"
    value: float
    threshold: float
    passing: bool
    details: Optional[Dict[str, Any]] = None

class SecurityCheck(BaseModel):
    """Vérification de sécurité."""
    check_name: str
    description: str
    passing: bool
    risk_level: RiskLevel
    details: Optional[Dict[str, Any]] = None
    remediation: Optional[str] = None

class PrivacyRisk(BaseModel):
    """Risque de confidentialité identifié."""
    risk_type: str
    description: str
    affected_data: List[str]
    risk_level: RiskLevel
    mitigation_strategy: Optional[str] = None

class ExplanationItem(BaseModel):
    """Élément d'explication pour la transparence."""
    feature_name: str
    importance: float
    direction: str  # "positive" ou "negative"
    description: Optional[str] = None

class TransparencyScore(BaseModel):
    """Score de transparence pour un composant."""
    component: ModelComponent
    score: float  # 0 à 1
    explanations_available: bool
    user_controls_available: bool
    details: Optional[Dict[str, Any]] = None

class AuditRequest(BaseModel):
    """Demande d'audit."""
    audit_type: AuditType
    components: List[ModelComponent]
    custom_parameters: Optional[Dict[str, Any]] = None
    scheduled: bool = False
    schedule_date: Optional[datetime] = None

class Audit(BaseModel):
    """Audit complet."""
    audit_id: str = Field(..., description="Identifiant unique de l'audit")
    audit_type: AuditType
    status: AuditStatus = AuditStatus.PENDING
    components: List[ModelComponent]
    started_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    requested_by: Optional[str] = None
    summary: Optional[str] = None
    overall_risk_level: Optional[RiskLevel] = None
    items: List[AuditItem] = []
    bias_metrics: Optional[List[BiasMetric]] = None
    security_checks: Optional[List[SecurityCheck]] = None
    privacy_risks: Optional[List[PrivacyRisk]] = None
    transparency_scores: Optional[List[TransparencyScore]] = None
    recommendations: List[str] = []
    
    @validator('completed_at')
    def completed_at_must_be_after_started_at(cls, v, values):
        """Vérifie que la date de fin est après la date de début."""
        if v and 'started_at' in values and v < values['started_at']:
            raise ValueError('La date de fin doit être après la date de début')
        return v

class User(BaseModel):
    """Utilisateur du système d'audit."""
    username: str
    email: str
    disabled: Optional[bool] = None
    permissions: List[str] = []

class UserInDB(User):
    """User with hashed password in database."""
    hashed_password: str

class Token(BaseModel):
    """Token d'authentification."""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Données du token."""
    username: Optional[str] = None
    permissions: List[str] = []

class ModelCard(BaseModel):
    """Carte de modèle pour documenter un modèle d'IA."""
    model_id: str = Field(..., description="Identifiant unique du modèle")
    name: str
    version: str
    description: str
    component: ModelComponent
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    developers: List[str] = []
    intended_use: str
    primary_metric: str
    performance_metrics: Dict[str, float]
    training_data_description: str
    evaluation_data_description: Optional[str] = None
    ethical_considerations: List[str] = []
    caveats_and_limitations: List[str] = []
    bias_assessments: Optional[List[Dict[str, Any]]] = None
    fairness_checks: Optional[List[Dict[str, Any]]] = None

class DatasetMetadata(BaseModel):
    """Métadonnées d'un jeu de données."""
    dataset_id: str = Field(..., description="Identifiant unique du jeu de données")
    name: str
    description: str
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    version: str
    source: str
    license: Optional[str] = None
    num_records: int
    features: List[Dict[str, Any]]
    sensitive_attributes: List[str] = []
    preprocessing_steps: List[str] = []
    data_quality_checks: Optional[List[Dict[str, Any]]] = None
    bias_assessments: Optional[List[Dict[str, Any]]] = None

class FairnessReport(BaseModel):
    """Rapport d'équité pour un modèle."""
    report_id: str = Field(..., description="Identifiant unique du rapport")
    model_id: str
    created_at: datetime = Field(default_factory=datetime.now)
    protected_attributes: List[str]
    fairness_metrics: Dict[str, Dict[str, float]]
    demographic_distributions: Dict[str, Dict[str, float]]
    recommendations: List[str] = []
    detailed_analysis: Optional[Dict[str, Any]] = None 