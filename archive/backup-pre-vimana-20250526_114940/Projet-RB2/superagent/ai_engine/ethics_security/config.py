"""
Configuration pour le module de sécurité et d'éthique.
"""
import os
from pydantic import BaseSettings

class Settings(BaseSettings):
    """Paramètres de configuration pour le service d'éthique et de sécurité."""
    # Informations générales
    API_VERSION: str = "1.0.0"
    API_NAME: str = "Retreat And Be Ethics & Security API"
    API_DESCRIPTION: str = "API pour le suivi et l'audit de l'éthique et de la sécurité des systèmes d'IA"
    
    # Sécurité
    SECRET_KEY: str = os.getenv("SECRET_KEY", "changez_moi_en_production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Base de données
    MONGODB_URL: str = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
    DATABASE_NAME: str = os.getenv("DATABASE_NAME", "ethics_security")
    
    # Paramètres d'audit
    AUDIT_INTERVAL_DAYS: int = int(os.getenv("AUDIT_INTERVAL_DAYS", "7"))  # Fréquence des audits automatiques
    BIAS_THRESHOLD: float = float(os.getenv("BIAS_THRESHOLD", "0.15"))  # Seuil de détection de biais
    PRIVACY_RISK_THRESHOLD: float = float(os.getenv("PRIVACY_RISK_THRESHOLD", "0.3"))  # Seuil de risque pour la vie privée
    
    # Endpoints API des autres services
    RECOMMENDER_API_URL: str = os.getenv("RECOMMENDER_API_URL", "http://recommender-api:8000")
    CHATBOT_API_URL: str = os.getenv("CHATBOT_API_URL", "http://chatbot-api:8001")
    VIRTUAL_COACH_API_URL: str = os.getenv("VIRTUAL_COACH_API_URL", "http://virtual-coach-api:5005")
    
    # Catégories protégées pour l'analyse de biais
    PROTECTED_CATEGORIES: list = [
        "gender", 
        "age", 
        "ethnicity", 
        "disability", 
        "religion", 
        "location"
    ]
    
    # Types de risques de sécurité à surveiller
    SECURITY_RISK_TYPES: list = [
        "data_leak",
        "unauthorized_access",
        "privacy_breach",
        "model_manipulation",
        "api_abuse"
    ]
    
    # Métriques d'équité à calculer
    FAIRNESS_METRICS: list = [
        "demographic_parity",
        "equal_opportunity",
        "statistical_parity",
        "disparate_impact"
    ]
    
    class Config:
        """Configuration de Pydantic pour les variables d'environnement."""
        env_file = ".env"


# Instance des paramètres à utiliser dans l'application
settings = Settings() 