"""
API pour le module d'éthique et de sécurité.
"""
import logging
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union

import httpx
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, status, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth<PERSON><PERSON><PERSON><PERSON><PERSON>earer, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel

from config import settings
from models import (
    Audit, AuditRequest, AuditType, AuditStatus, ModelComponent,
    BiasMetric, SecurityCheck, PrivacyRisk, TransparencyScore, 
    RiskLevel, User, UserInDB, Token, TokenData, ModelCard,
    FairnessReport, DatasetMetadata
)
from bias_audit import bias_auditor
from security_audit import security_auditor
from transparency import transparency_manager

# Configuration du logger
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Création de l'application FastAPI
app = FastAPI(
    title=settings.API_NAME,
    description=settings.API_DESCRIPTION,
    version=settings.API_VERSION,
)

# Configuration des CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # A remplacer par les origines spécifiques en production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration de l'authentification
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Base de données utilisateur simulée (à remplacer par une base de données réelle)
fake_users_db = {
    "admin": {
        "username": "admin",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("adminpassword"),
        "disabled": False,
        "permissions": ["audit", "report", "admin"],
    },
    "auditor": {
        "username": "auditor",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("auditorpassword"),
        "disabled": False,
        "permissions": ["audit", "report"],
    },
    "viewer": {
        "username": "viewer",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("viewerpassword"),
        "disabled": False,
        "permissions": ["view"],
    },
}

# Base de données des audits simulée (à remplacer par une base de données réelle)
audits_db = {}

# Fonctions d'authentification
def verify_password(plain_password, hashed_password):
    """Vérifie si le mot de passe correspond au hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    """Génère un hash pour le mot de passe."""
    return pwd_context.hash(password)

def get_user(db, username: str):
    """Récupère un utilisateur depuis la base de données."""
    if username in db:
        user_dict = db[username]
        return UserInDB(**user_dict)
    return None

def authenticate_user(fake_db, username: str, password: str):
    """Authentifie un utilisateur."""
    user = get_user(fake_db, username)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Crée un token d'accès JWT."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    """Récupère l'utilisateur courant à partir du token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Impossible de valider les identifiants",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(fake_users_db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    """Vérifie que l'utilisateur courant est actif."""
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Utilisateur inactif")
    return current_user

# Routes pour l'authentification
@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """Endpoint pour l'authentification et l'obtention d'un token."""
    user = authenticate_user(fake_users_db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Nom d'utilisateur ou mot de passe incorrect",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "permissions": user.permissions},
        expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/users/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    """Endpoint pour obtenir les informations de l'utilisateur courant."""
    return current_user

# Routes pour les audits
@app.post("/audits", response_model=dict)
async def create_audit(
    audit_request: AuditRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Crée et lance un nouvel audit.
    
    Si l'audit est planifié, il sera mis en file d'attente pour une exécution ultérieure.
    Sinon, il sera exécuté en arrière-plan.
    """
    # Vérifier les permissions
    if "audit" not in current_user.permissions:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Vous n'avez pas les permissions pour créer un audit"
        )
    
    # Générer un ID unique pour l'audit
    audit_id = str(uuid.uuid4())
    
    # Créer l'audit avec le statut initial
    audit = Audit(
        audit_id=audit_id,
        audit_type=audit_request.audit_type,
        status=AuditStatus.SCHEDULED if audit_request.scheduled else AuditStatus.PENDING,
        components=audit_request.components,
        created_at=datetime.now().isoformat(),
        scheduled_at=audit_request.schedule_date if audit_request.scheduled else None,
        requested_by=current_user.username,
        parameters=audit_request.parameters,
        summary=None,
        overall_risk_level=None,
        bias_metrics=[],
        security_checks=[],
        privacy_risks=[],
        transparency_scores=[],
        audit_items=[],
        recommendations=[]
    )
    
    # Enregistrer l'audit dans la base de données
    audits_db[audit_id] = audit
    
    if audit_request.scheduled:
        # Si l'audit est planifié, ajouter un message de confirmation
        logger.info(f"Audit {audit_id} planifié pour {audit_request.schedule_date}")
        return {
            "audit_id": audit_id,
            "status": "scheduled",
            "message": f"Audit planifié pour {audit_request.schedule_date}",
            "scheduled_at": audit_request.schedule_date
        }
    else:
        # Sinon, démarrer l'audit en arrière-plan
        background_tasks.add_task(run_audit, audit_id)
        logger.info(f"Audit {audit_id} initié")
        return {
            "audit_id": audit_id,
            "status": "pending",
            "message": "Audit en cours d'exécution",
        }

@app.get("/audits/{audit_id}", response_model=Audit)
async def get_audit(
    audit_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Récupère les détails d'un audit spécifique."""
    if audit_id not in audits_db:
        raise HTTPException(status_code=404, detail="Audit non trouvé")
    
    # Vérifier les permissions
    if not any(perm in current_user.permissions for perm in ["audit", "report", "view"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Vous n'avez pas les permissions pour consulter cet audit"
        )
    
    return audits_db[audit_id]

@app.get("/audits", response_model=List[Audit])
async def list_audits(
    status: Optional[AuditStatus] = None,
    audit_type: Optional[AuditType] = None,
    component: Optional[ModelComponent] = None,
    limit: int = 10,
    skip: int = 0,
    current_user: User = Depends(get_current_active_user)
):
    """Liste les audits, avec possibilité de filtrage."""
    # Vérifier les permissions
    if not any(perm in current_user.permissions for perm in ["audit", "report", "view"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Vous n'avez pas les permissions pour consulter les audits"
        )
    
    # Filtrer les audits
    filtered_audits = list(audits_db.values())
    
    if status:
        filtered_audits = [a for a in filtered_audits if a.status == status]
    
    if audit_type:
        filtered_audits = [a for a in filtered_audits if a.audit_type == audit_type]
    
    if component:
        filtered_audits = [a for a in filtered_audits if component in a.components]
    
    # Tri par date de création (le plus récent d'abord)
    filtered_audits.sort(key=lambda x: x.created_at, reverse=True)
    
    # Pagination
    paginated_audits = filtered_audits[skip : skip + limit]
    
    return paginated_audits

@app.post("/models/cards", response_model=ModelCard)
async def create_model_card(
    component: ModelComponent,
    current_user: User = Depends(get_current_active_user)
):
    """Crée une fiche modèle (model card) pour un composant."""
    # Vérifier les permissions
    if not any(perm in current_user.permissions for perm in ["audit", "admin"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Vous n'avez pas les permissions pour créer une fiche modèle"
        )
    
    # Générer la fiche modèle
    model_card_data = await transparency_manager.generate_model_card(component)
    model_card = ModelCard(**model_card_data)
    
    # Dans une implémentation réelle, nous sauvegarderions la fiche dans une base de données
    
    return model_card

@app.get("/dashboard/summary", response_model=Dict[str, Any])
async def get_dashboard_summary(
    current_user: User = Depends(get_current_active_user)
):
    """
    Récupère un résumé pour le tableau de bord, incluant les métriques
    clés de tous les composants.
    """
    # Vérifier les permissions
    if not any(perm in current_user.permissions for perm in ["audit", "report", "view"]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Vous n'avez pas les permissions pour consulter le tableau de bord"
        )
    
    # Calculer les statistiques des audits
    total_audits = len(audits_db)
    pending_audits = len([a for a in audits_db.values() if a.status == AuditStatus.PENDING])
    completed_audits = len([a for a in audits_db.values() if a.status == AuditStatus.COMPLETED])
    failed_audits = len([a for a in audits_db.values() if a.status == AuditStatus.FAILED])
    
    # Calculer les niveaux de risque
    risk_levels = {
        "high": 0,
        "medium": 0,
        "low": 0
    }
    
    for audit in audits_db.values():
        if audit.overall_risk_level == RiskLevel.HIGH:
            risk_levels["high"] += 1
        elif audit.overall_risk_level == RiskLevel.MEDIUM:
            risk_levels["medium"] += 1
        elif audit.overall_risk_level == RiskLevel.LOW:
            risk_levels["low"] += 1
    
    # Composants problématiques (simulés)
    component_metrics = {
        ModelComponent.RECOMMENDER.value: {
            "transparency_score": 0.72,
            "bias_level": 0.15,
            "security_score": 0.85,
            "risk_level": "medium"
        },
        ModelComponent.VIRTUAL_COACH.value: {
            "transparency_score": 0.78,
            "bias_level": 0.08,
            "security_score": 0.92,
            "risk_level": "low"
        },
        ModelComponent.CHATBOT.value: {
            "transparency_score": 0.65,
            "bias_level": 0.22,
            "security_score": 0.75,
            "risk_level": "high"
        }
    }
    
    # Tendances (simulées)
    trends = {
        "bias_metrics": [0.24, 0.22, 0.19, 0.17, 0.16, 0.15],
        "transparency_scores": [0.55, 0.58, 0.62, 0.65, 0.68, 0.72],
        "security_scores": [0.70, 0.75, 0.78, 0.82, 0.84, 0.85],
        "months": ["Jan", "Fév", "Mar", "Avr", "Mai", "Juin"]
    }
    
    return {
        "audit_stats": {
            "total": total_audits,
            "pending": pending_audits,
            "completed": completed_audits,
            "failed": failed_audits
        },
        "risk_levels": risk_levels,
        "component_metrics": component_metrics,
        "trends": trends,
        "last_update": datetime.now().isoformat()
    }

# Fonction pour exécuter un audit en arrière-plan
async def run_audit(audit_id: str):
    """
    Exécute un audit en arrière-plan.
    
    Cette fonction effectue l'audit complet pour tous les composants spécifiés,
    en exécutant les vérifications de biais, de sécurité et de transparence.
    """
    logger.info(f"Démarrage de l'audit {audit_id}")
    
    try:
        # Récupérer l'audit
        audit = audits_db[audit_id]
        
        # Mettre à jour le statut
        audit.status = AuditStatus.RUNNING
        audit.started_at = datetime.now().isoformat()
        audits_db[audit_id] = audit
        
        # Résultats de l'audit
        bias_metrics = []
        security_checks = []
        privacy_risks = []
        transparency_scores = []
        
        # Exécuter l'audit pour chaque composant
        for component in audit.components:
            logger.info(f"Audit du composant {component}")
            
            # Audit de biais
            if audit.audit_type in [AuditType.BIAS, AuditType.COMPREHENSIVE]:
                component_bias_metrics = await bias_auditor.audit_component(component)
                bias_metrics.extend(component_bias_metrics)
            
            # Audit de sécurité
            if audit.audit_type in [AuditType.SECURITY, AuditType.COMPREHENSIVE]:
                component_security_checks, component_privacy_risks = await security_auditor.audit_component(component)
                security_checks.extend(component_security_checks)
                privacy_risks.extend(component_privacy_risks)
            
            # Évaluation de la transparence
            if audit.audit_type in [AuditType.TRANSPARENCY, AuditType.COMPREHENSIVE]:
                transparency_score = await transparency_manager.evaluate_transparency(component)
                transparency_scores.append(transparency_score)
        
        # Déterminer le niveau de risque global
        overall_risk_level = determine_overall_risk_level(
            bias_metrics, security_checks, privacy_risks
        )
        
        # Générer des recommandations
        recommendations = generate_recommendations(
            bias_metrics, security_checks, privacy_risks, transparency_scores
        )
        
        # Générer un résumé
        summary = generate_audit_summary(
            audit.audit_type, audit.components,
            bias_metrics, security_checks, privacy_risks, transparency_scores,
            overall_risk_level
        )
        
        # Mettre à jour l'audit avec les résultats
        audit.status = AuditStatus.COMPLETED
        audit.completed_at = datetime.now().isoformat()
        audit.bias_metrics = bias_metrics
        audit.security_checks = security_checks
        audit.privacy_risks = privacy_risks
        audit.transparency_scores = transparency_scores
        audit.overall_risk_level = overall_risk_level
        audit.recommendations = recommendations
        audit.summary = summary
        
        # Enregistrer l'audit mis à jour
        audits_db[audit_id] = audit
        
        logger.info(f"Audit {audit_id} terminé avec succès")
        
    except Exception as e:
        logger.error(f"Erreur lors de l'audit {audit_id}: {str(e)}")
        
        # Mettre à jour le statut en cas d'erreur
        audit = audits_db[audit_id]
        audit.status = AuditStatus.FAILED
        audit.error_message = str(e)
        audit.completed_at = datetime.now().isoformat()
        audits_db[audit_id] = audit

def determine_overall_risk_level(
    bias_metrics: List[BiasMetric],
    security_checks: List[SecurityCheck],
    privacy_risks: List[PrivacyRisk]
) -> RiskLevel:
    """
    Détermine le niveau de risque global basé sur les résultats d'audit.
    
    Args:
        bias_metrics: Métriques de biais
        security_checks: Vérifications de sécurité
        privacy_risks: Risques de confidentialité
        
    Returns:
        Niveau de risque global
    """
    # Vérifier les problèmes critiques
    # Si au moins un risque élevé est détecté, le niveau global est élevé
    high_security = any(check.risk_level == RiskLevel.HIGH and not check.passing 
                        for check in security_checks)
    high_privacy = any(risk.risk_level == RiskLevel.HIGH for risk in privacy_risks)
    high_bias = any(metric.value > 0.3 for metric in bias_metrics)
    
    if high_security or high_privacy or high_bias:
        return RiskLevel.HIGH
    
    # Vérifier les problèmes modérés
    medium_security = any(check.risk_level == RiskLevel.MEDIUM and not check.passing 
                          for check in security_checks)
    medium_privacy = any(risk.risk_level == RiskLevel.MEDIUM for risk in privacy_risks)
    medium_bias = any(0.15 < metric.value <= 0.3 for metric in bias_metrics)
    
    if medium_security or medium_privacy or medium_bias:
        return RiskLevel.MEDIUM
    
    # Si aucun problème significatif, le niveau est faible
    return RiskLevel.LOW

def generate_recommendations(
    bias_metrics: List[BiasMetric],
    security_checks: List[SecurityCheck],
    privacy_risks: List[PrivacyRisk],
    transparency_scores: List[TransparencyScore]
) -> List[str]:
    """
    Génère des recommandations basées sur les résultats d'audit.
    
    Args:
        bias_metrics: Métriques de biais
        security_checks: Vérifications de sécurité
        privacy_risks: Risques de confidentialité
        transparency_scores: Scores de transparence
        
    Returns:
        Liste de recommandations
    """
    recommendations = []
    
    # Recommandations pour les problèmes de biais
    failing_bias_metrics = [m for m in bias_metrics if not m.passing]
    if failing_bias_metrics:
        for metric in failing_bias_metrics[:3]:  # Limiter à 3 recommandations de biais
            recommendations.append(
                f"Réduire le biais pour l'attribut '{metric.attribute}' (métrique: {metric.metric_name}, "
                f"valeur: {metric.value:.2f}, seuil: {metric.threshold:.2f})."
            )
    
    # Recommandations pour les problèmes de sécurité
    failing_security_checks = [c for c in security_checks if not c.passing]
    if failing_security_checks:
        for check in failing_security_checks[:3]:  # Limiter à 3 recommandations de sécurité
            if check.remediation:
                recommendations.append(check.remediation)
    
    # Recommandations pour les risques de confidentialité
    high_privacy_risks = [r for r in privacy_risks if r.risk_level == RiskLevel.HIGH]
    if high_privacy_risks:
        for risk in high_privacy_risks[:2]:  # Limiter à 2 recommandations de confidentialité
            recommendations.append(risk.mitigation_strategy)
    
    # Recommandations pour la transparence
    for score in transparency_scores:
        if score.score < 0.7:  # Seuil arbitraire pour les recommandations
            improvement_areas = score.details.get("improvement_areas", [])
            if improvement_areas:
                recommendations.extend(improvement_areas[:2])  # Limiter à 2 recommandations par composant
    
    # Si aucune recommandation spécifique, ajouter une recommandation générale
    if not recommendations:
        recommendations.append(
            "Continuer à surveiller les performances et l'équité des systèmes d'IA "
            "et rester informé des meilleures pratiques du secteur."
        )
    
    return recommendations

def generate_audit_summary(
    audit_type: AuditType,
    components: List[ModelComponent],
    bias_metrics: List[BiasMetric],
    security_checks: List[SecurityCheck],
    privacy_risks: List[PrivacyRisk],
    transparency_scores: List[TransparencyScore],
    overall_risk_level: RiskLevel
) -> str:
    """
    Génère un résumé de l'audit.
    
    Args:
        audit_type: Type d'audit
        components: Composants audités
        bias_metrics: Métriques de biais
        security_checks: Vérifications de sécurité
        privacy_risks: Risques de confidentialité
        transparency_scores: Scores de transparence
        overall_risk_level: Niveau de risque global
        
    Returns:
        Résumé de l'audit
    """
    component_str = ", ".join([c.value for c in components])
    summary_parts = [
        f"Audit {audit_type.value} des composants: {component_str}. ",
        f"Niveau de risque global: {overall_risk_level.value}. "
    ]
    
    # Résumé des biais
    if bias_metrics:
        failing_metrics = len([m for m in bias_metrics if not m.passing])
        if failing_metrics > 0:
            summary_parts.append(f"{failing_metrics} problèmes de biais détectés. ")
        else:
            summary_parts.append("Aucun problème de biais significatif détecté. ")
    
    # Résumé de la sécurité
    if security_checks:
        failing_checks = len([c for c in security_checks if not c.passing])
        if failing_checks > 0:
            summary_parts.append(f"{failing_checks} problèmes de sécurité identifiés. ")
        else:
            summary_parts.append("Tous les contrôles de sécurité ont été validés. ")
    
    # Résumé des risques de confidentialité
    if privacy_risks:
        high_risks = len([r for r in privacy_risks if r.risk_level == RiskLevel.HIGH])
        medium_risks = len([r for r in privacy_risks if r.risk_level == RiskLevel.MEDIUM])
        if high_risks > 0:
            summary_parts.append(f"{high_risks} risques de confidentialité élevés détectés. ")
        elif medium_risks > 0:
            summary_parts.append(f"{medium_risks} risques de confidentialité modérés détectés. ")
        else:
            summary_parts.append("Risques de confidentialité mineurs. ")
    
    # Résumé de la transparence
    if transparency_scores:
        avg_score = sum(score.score for score in transparency_scores) / len(transparency_scores)
        if avg_score < 0.6:
            summary_parts.append(f"Niveau de transparence insuffisant (score moyen: {avg_score:.2f}). ")
        elif avg_score < 0.8:
            summary_parts.append(f"Niveau de transparence modéré (score moyen: {avg_score:.2f}). ")
        else:
            summary_parts.append(f"Bon niveau de transparence (score moyen: {avg_score:.2f}). ")
    
    return "".join(summary_parts)

# Point d'entrée de l'application
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8008) 