"""
Module pour l'audit de biais dans les modèles d'IA.
"""
import json
import logging
import random
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

import numpy as np
import pandas as pd
from sklearn.metrics import confusion_matrix

from config import settings
from models import BiasMetric, RiskLevel, ModelComponent

# Configuration du logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BiasAuditor:
    """Classe pour effectuer des audits de biais dans les modèles d'IA."""

    def __init__(self):
        """Initialisation de l'auditeur de biais."""
        self.protected_categories = settings.PROTECTED_CATEGORIES
        self.bias_threshold = settings.BIAS_THRESHOLD
        self.fairness_metrics = settings.FAIRNESS_METRICS

    async def audit_component(self, component: ModelComponent, dataset: Optional[pd.DataFrame] = None) -> List[BiasMetric]:
        """
        Effectue un audit de biais pour un composant spécifique.
        
        Args:
            component: Le composant à auditer
            dataset: Optionnel, un DataFrame avec les données à utiliser pour l'audit
            
        Returns:
            Une liste de métriques de biais
        """
        logger.info(f"Démarrage de l'audit de biais pour le composant {component}")
        
        if dataset is None:
            # Si aucun dataset n'est fourni, nous utilisons des données simulées
            # En production, nous récupérerions des données réelles
            dataset = self._get_simulated_data(component)
        
        bias_metrics = []
        
        for category in self.protected_categories:
            if category in dataset.columns:
                for metric_name in self.fairness_metrics:
                    metric = self._calculate_fairness_metric(
                        dataset=dataset, 
                        protected_attribute=category, 
                        metric_name=metric_name,
                        component=component
                    )
                    bias_metrics.append(metric)
                    
                    if not metric.passing:
                        logger.warning(
                            f"Biais détecté pour l'attribut {category} "
                            f"avec la métrique {metric_name} : {metric.value:.4f}"
                        )
        
        return bias_metrics
    
    def _get_simulated_data(self, component: ModelComponent) -> pd.DataFrame:
        """
        Génère des données simulées pour l'audit.
        
        Dans une implémentation réelle, nous récupérerions des données
        de prédiction historiques du composant en question.
        
        Args:
            component: Le composant pour lequel générer des données
            
        Returns:
            Un DataFrame pandas avec des données simulées
        """
        # Nombre d'échantillons à générer
        n_samples = 1000
        
        # Générer des données de base
        data = {
            # Attributs protégés
            "gender": np.random.choice(["male", "female", "other"], size=n_samples),
            "age": np.random.choice(["18-24", "25-34", "35-44", "45-54", "55+"], size=n_samples),
            "ethnicity": np.random.choice(
                ["caucasian", "african", "asian", "hispanic", "other"], 
                size=n_samples, 
                p=[0.6, 0.15, 0.15, 0.05, 0.05]  # Répartition volontairement déséquilibrée
            ),
            "location": np.random.choice(
                ["urban", "suburban", "rural"], 
                size=n_samples,
                p=[0.6, 0.3, 0.1]  # Répartition volontairement déséquilibrée
            ),
            
            # Autres attributs pertinents selon le composant
            "user_activity": np.random.choice(["high", "medium", "low"], size=n_samples),
            "price_sensitivity": np.random.uniform(0, 1, size=n_samples),
            "wellness_interest": np.random.uniform(0, 1, size=n_samples),
        }
        
        # Variables cibles spécifiques au composant
        if component == ModelComponent.RECOMMENDER:
            # Simuler des recommandations avec un léger biais sur l'âge et l'ethnicité
            data["recommended"] = np.zeros(n_samples, dtype=bool)
            
            # Simuler un biais: les utilisateurs plus âgés ont moins de recommandations
            for i in range(n_samples):
                prob_rec = 0.7  # Probabilité de base
                
                # Biais par âge
                if data["age"][i] in ["45-54", "55+"]:
                    prob_rec -= 0.3
                
                # Biais par ethnicité
                if data["ethnicity"][i] in ["african", "hispanic"]:
                    prob_rec -= 0.2
                
                data["recommended"][i] = np.random.random() < prob_rec
                
        elif component == ModelComponent.VIRTUAL_COACH:
            # Simuler l'achèvement du programme avec un léger biais sur le genre
            data["program_completed"] = np.zeros(n_samples, dtype=bool)
            
            for i in range(n_samples):
                prob_complete = 0.6  # Probabilité de base
                
                # Biais par genre
                if data["gender"][i] == "female":
                    prob_complete += 0.15
                
                # Biais par localisation
                if data["location"][i] == "rural":
                    prob_complete -= 0.2
                
                data["program_completed"][i] = np.random.random() < prob_complete
                
        elif component == ModelComponent.PRICING:
            # Simuler des prix avec un biais potentiel par localisation
            base_price = 100
            price_variations = np.zeros(n_samples)
            
            for i in range(n_samples):
                # Variation de base
                variation = np.random.normal(0, 10)
                
                # Biais par localisation
                if data["location"][i] == "urban":
                    variation += 20
                elif data["location"][i] == "rural":
                    variation -= 10
                
                # Biais par sensibilité au prix (légitime)
                variation -= data["price_sensitivity"][i] * 15
                
                price_variations[i] = variation
            
            data["price"] = base_price + price_variations
        
        else:
            # Pour les autres composants, utiliser une variable cible générique
            data["positive_outcome"] = np.random.binomial(1, 0.7, size=n_samples)
        
        return pd.DataFrame(data)
    
    def _calculate_fairness_metric(
        self, 
        dataset: pd.DataFrame, 
        protected_attribute: str, 
        metric_name: str,
        component: ModelComponent
    ) -> BiasMetric:
        """
        Calcule une métrique d'équité spécifique.
        
        Args:
            dataset: Le DataFrame contenant les données
            protected_attribute: L'attribut protégé à évaluer
            metric_name: Le nom de la métrique à calculer
            component: Le composant évalué
            
        Returns:
            Un objet BiasMetric contenant les résultats
        """
        # Identifier la variable cible en fonction du composant
        if component == ModelComponent.RECOMMENDER:
            target_variable = "recommended"
        elif component == ModelComponent.VIRTUAL_COACH:
            target_variable = "program_completed"
        elif component == ModelComponent.PRICING:
            target_variable = "price"
            # Pour les variables continues comme le prix, nous utilisons une approche différente
            return self._calculate_continuous_fairness_metric(
                dataset, protected_attribute, metric_name, "price"
            )
        else:
            target_variable = "positive_outcome"
        
        # Obtenir les groupes uniques pour l'attribut protégé
        protected_groups = dataset[protected_attribute].unique()
        
        # Calculer la métrique appropriée
        if metric_name == "demographic_parity":
            value, details = self._calculate_demographic_parity(
                dataset, protected_attribute, target_variable
            )
        elif metric_name == "equal_opportunity":
            value, details = self._calculate_equal_opportunity(
                dataset, protected_attribute, target_variable
            )
        elif metric_name == "disparate_impact":
            value, details = self._calculate_disparate_impact(
                dataset, protected_attribute, target_variable
            )
        else:  # statistical_parity as default
            value, details = self._calculate_statistical_parity(
                dataset, protected_attribute, target_variable
            )
        
        # Déterminer si la métrique passe le seuil
        passing = abs(value) < self.bias_threshold
        
        return BiasMetric(
            attribute=protected_attribute,
            metric_name=metric_name,
            value=value,
            threshold=self.bias_threshold,
            passing=passing,
            details=details
        )
    
    def _calculate_continuous_fairness_metric(
        self, 
        dataset: pd.DataFrame, 
        protected_attribute: str, 
        metric_name: str,
        target_variable: str
    ) -> BiasMetric:
        """
        Calcule une métrique d'équité pour une variable cible continue.
        
        Args:
            dataset: Le DataFrame contenant les données
            protected_attribute: L'attribut protégé à évaluer
            metric_name: Le nom de la métrique à calculer
            target_variable: La variable cible continue (ex: prix)
            
        Returns:
            Un objet BiasMetric contenant les résultats
        """
        # Obtenir les groupes uniques pour l'attribut protégé
        protected_groups = dataset[protected_attribute].unique()
        
        # Calculer les moyennes par groupe
        group_means = {}
        for group in protected_groups:
            group_means[group] = dataset[dataset[protected_attribute] == group][target_variable].mean()
        
        # Calculer l'écart relatif maximal entre les groupes
        min_mean = min(group_means.values())
        max_mean = max(group_means.values())
        
        # Normaliser l'écart
        if min_mean > 0:
            relative_disparity = (max_mean - min_mean) / min_mean
        else:
            relative_disparity = max_mean - min_mean
        
        details = {
            "group_means": group_means,
            "min_mean": min_mean,
            "max_mean": max_mean,
            "relative_disparity": relative_disparity
        }
        
        # Déterminer si la métrique passe le seuil
        passing = relative_disparity < self.bias_threshold
        
        return BiasMetric(
            attribute=protected_attribute,
            metric_name=f"{metric_name}_continuous",
            value=relative_disparity,
            threshold=self.bias_threshold,
            passing=passing,
            details=details
        )
    
    def _calculate_demographic_parity(
        self, 
        dataset: pd.DataFrame, 
        protected_attribute: str, 
        target_variable: str
    ) -> Tuple[float, Dict[str, Any]]:
        """
        Calcule la parité démographique entre les groupes.
        
        La parité démographique est atteinte lorsque la proportion d'individus
        recevant le résultat positif est la même dans tous les groupes.
        
        Args:
            dataset: Le DataFrame contenant les données
            protected_attribute: L'attribut protégé à évaluer
            target_variable: La variable cible
            
        Returns:
            Un tuple (valeur, détails) où la valeur est la différence maximale de taux
            et les détails contiennent les taux par groupe
        """
        # Obtenir les groupes uniques pour l'attribut protégé
        protected_groups = dataset[protected_attribute].unique()
        
        # Calculer le taux de la variable cible pour chaque groupe
        group_rates = {}
        for group in protected_groups:
            group_data = dataset[dataset[protected_attribute] == group]
            if len(group_data) > 0:
                group_rate = group_data[target_variable].mean()
                group_rates[group] = group_rate
        
        # Calculer la différence maximale entre les taux
        min_rate = min(group_rates.values())
        max_rate = max(group_rates.values())
        max_difference = max_rate - min_rate
        
        details = {
            "group_rates": group_rates,
            "max_difference": max_difference,
            "overall_rate": dataset[target_variable].mean()
        }
        
        return max_difference, details
    
    def _calculate_equal_opportunity(
        self, 
        dataset: pd.DataFrame, 
        protected_attribute: str, 
        target_variable: str
    ) -> Tuple[float, Dict[str, Any]]:
        """
        Calcule l'égalité des chances entre les groupes.
        
        L'égalité des chances est atteinte lorsque les taux de vrais positifs
        sont égaux pour tous les groupes.
        
        Args:
            dataset: Le DataFrame contenant les données
            protected_attribute: L'attribut protégé à évaluer
            target_variable: La variable cible
            
        Returns:
            Un tuple (valeur, détails) où la valeur est la différence maximale de taux
            et les détails contiennent les taux par groupe
        """
        # Pour calculer le taux de vrais positifs, nous avons besoin d'une vérité terrain
        # Pour cette démonstration, nous considérons que la variable cible est binaire
        # et représente à la fois la prédiction et la vérité
        
        # Obtenir les groupes uniques pour l'attribut protégé
        protected_groups = dataset[protected_attribute].unique()
        
        # Calculer le taux de vrais positifs pour chaque groupe
        group_tpr = {}
        for group in protected_groups:
            group_data = dataset[dataset[protected_attribute] == group]
            if len(group_data) > 0:
                # Dans un cas réel, nous aurions des colonnes distinctes pour
                # la prédiction et la vérité terrain
                true_positives = sum((group_data[target_variable] == True))
                actual_positives = sum((group_data[target_variable] == True))
                
                if actual_positives > 0:
                    tpr = true_positives / actual_positives
                else:
                    tpr = 0
                
                group_tpr[group] = tpr
        
        # Calculer la différence maximale entre les taux
        min_tpr = min(group_tpr.values())
        max_tpr = max(group_tpr.values())
        max_difference = max_tpr - min_tpr
        
        details = {
            "group_true_positive_rates": group_tpr,
            "max_difference": max_difference
        }
        
        return max_difference, details
    
    def _calculate_disparate_impact(
        self, 
        dataset: pd.DataFrame, 
        protected_attribute: str, 
        target_variable: str
    ) -> Tuple[float, Dict[str, Any]]:
        """
        Calcule l'impact disparate entre les groupes.
        
        L'impact disparate compare le ratio des taux de résultats positifs
        entre le groupe le plus favorisé et le moins favorisé.
        
        Args:
            dataset: Le DataFrame contenant les données
            protected_attribute: L'attribut protégé à évaluer
            target_variable: La variable cible
            
        Returns:
            Un tuple (valeur, détails) où la valeur est l'écart par rapport à 1.0
            et les détails contiennent les taux par groupe
        """
        # Obtenir les groupes uniques pour l'attribut protégé
        protected_groups = dataset[protected_attribute].unique()
        
        # Calculer le taux de la variable cible pour chaque groupe
        group_rates = {}
        for group in protected_groups:
            group_data = dataset[dataset[protected_attribute] == group]
            if len(group_data) > 0:
                group_rate = group_data[target_variable].mean()
                group_rates[group] = group_rate
        
        # Identifier les groupes avec le taux le plus haut et le plus bas
        max_rate_group = max(group_rates.items(), key=lambda x: x[1])
        min_rate_group = min(group_rates.items(), key=lambda x: x[1])
        
        # Calculer le ratio d'impact disparate
        if min_rate_group[1] > 0:
            disparate_impact_ratio = min_rate_group[1] / max_rate_group[1]
        else:
            disparate_impact_ratio = 0
        
        # L'impact disparate est mesuré par l'écart par rapport à 1.0 (égalité parfaite)
        disparate_impact = 1.0 - disparate_impact_ratio
        
        details = {
            "group_rates": group_rates,
            "max_rate_group": max_rate_group[0],
            "min_rate_group": min_rate_group[0],
            "disparate_impact_ratio": disparate_impact_ratio
        }
        
        return disparate_impact, details
    
    def _calculate_statistical_parity(
        self, 
        dataset: pd.DataFrame, 
        protected_attribute: str, 
        target_variable: str
    ) -> Tuple[float, Dict[str, Any]]:
        """
        Calcule la parité statistique entre les groupes.
        
        Similaire à la parité démographique, mais exprimée en termes d'écart
        par rapport à la moyenne globale.
        
        Args:
            dataset: Le DataFrame contenant les données
            protected_attribute: L'attribut protégé à évaluer
            target_variable: La variable cible
            
        Returns:
            Un tuple (valeur, détails) où la valeur est l'écart maximal par rapport à la moyenne
            et les détails contiennent les écarts par groupe
        """
        # Obtenir les groupes uniques pour l'attribut protégé
        protected_groups = dataset[protected_attribute].unique()
        
        # Calculer le taux global de la variable cible
        overall_rate = dataset[target_variable].mean()
        
        # Calculer le taux pour chaque groupe et l'écart par rapport à la moyenne
        group_rates = {}
        group_disparities = {}
        
        for group in protected_groups:
            group_data = dataset[dataset[protected_attribute] == group]
            if len(group_data) > 0:
                group_rate = group_data[target_variable].mean()
                group_rates[group] = group_rate
                
                # Écart par rapport à la moyenne globale
                disparity = abs(group_rate - overall_rate)
                group_disparities[group] = disparity
        
        # Écart maximal par rapport à la moyenne
        max_disparity = max(group_disparities.values())
        
        details = {
            "overall_rate": overall_rate,
            "group_rates": group_rates,
            "group_disparities": group_disparities,
            "max_disparity": max_disparity
        }
        
        return max_disparity, details

# Instance unique de l'auditeur de biais
bias_auditor = BiasAuditor() 