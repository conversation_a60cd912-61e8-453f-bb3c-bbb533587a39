import os
from typing import Dict, Any, List, Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from sklearn.preprocessing import MinMaxScaler
import logging

app = FastAPI(
    title="Retreat And Be - Interface Personalization API",
    description="Provides customized interface configurations based on user behavior and preferences",
    version="0.1.0"
)

# --- CORS Configuration ---
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Logging Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- API Models ---
class UserProfile(BaseModel):
    user_id: str
    interaction_history: Optional[List[Dict[str, Any]]] = None
    preferences: Optional[Dict[str, Any]] = None
    segment: Optional[str] = None
    
class InterfaceConfig(BaseModel):
    layout: Dict[str, Any]
    components: Dict[str, Any]
    content_emphasis: Dict[str, float]
    notifications: Dict[str, Any]
    accessibility: Dict[str, Any]

# --- Mock Data Store (replace with DB in production) ---
# In a real implementation, this would be a database
user_profiles = {}
interface_templates = {
    "DEFAULT": {
        "layout": {
            "sidebar": True,
            "main_content_columns": 2,
            "show_trending": True,
            "compact_view": False
        },
        "components": {
            "recommendations_position": "top",
            "search_prominent": True,
            "show_social_proof": True,
            "show_reviews_summary": True
        },
        "content_emphasis": {
            "location": 0.5,
            "price": 0.5,
            "activities": 0.5,
            "amenities": 0.5,
            "reviews": 0.5
        },
        "notifications": {
            "frequency": "medium",
            "types": ["price_drops", "availability", "recommended"]
        },
        "accessibility": {
            "font_size": "medium",
            "contrast": "normal",
            "reduced_motion": False
        }
    },
    "PRICE_SENSITIVE": {
        "layout": {
            "sidebar": True,
            "main_content_columns": 1,
            "show_trending": False,
            "compact_view": True
        },
        "components": {
            "recommendations_position": "side",
            "search_prominent": True,
            "show_social_proof": False,
            "show_reviews_summary": True
        },
        "content_emphasis": {
            "location": 0.3,
            "price": 0.9,
            "activities": 0.4,
            "amenities": 0.3,
            "reviews": 0.5
        },
        "notifications": {
            "frequency": "high",
            "types": ["price_drops", "deals", "promotions"]
        },
        "accessibility": {
            "font_size": "medium",
            "contrast": "normal",
            "reduced_motion": False
        }
    },
    "EXPERIENCE_FOCUSED": {
        "layout": {
            "sidebar": False,
            "main_content_columns": 2,
            "show_trending": True,
            "compact_view": False
        },
        "components": {
            "recommendations_position": "top",
            "search_prominent": False,
            "show_social_proof": True,
            "show_reviews_summary": True
        },
        "content_emphasis": {
            "location": 0.5,
            "price": 0.3,
            "activities": 0.9,
            "amenities": 0.7,
            "reviews": 0.8
        },
        "notifications": {
            "frequency": "medium",
            "types": ["new_experiences", "recommended", "popular"]
        },
        "accessibility": {
            "font_size": "medium",
            "contrast": "normal",
            "reduced_motion": False
        }
    },
    "ACCESSIBILITY_FOCUSED": {
        "layout": {
            "sidebar": True,
            "main_content_columns": 1,
            "show_trending": False,
            "compact_view": True
        },
        "components": {
            "recommendations_position": "top",
            "search_prominent": True,
            "show_social_proof": False,
            "show_reviews_summary": True
        },
        "content_emphasis": {
            "location": 0.5,
            "price": 0.5,
            "activities": 0.5,
            "amenities": 0.7,
            "reviews": 0.5
        },
        "notifications": {
            "frequency": "low",
            "types": ["essential"]
        },
        "accessibility": {
            "font_size": "large",
            "contrast": "high",
            "reduced_motion": True
        }
    }
}

# --- Inference Functions ---
def determine_user_persona(user_profile: UserProfile) -> str:
    """
    Determines the most appropriate persona for a user based on their profile.
    """
    if user_profile.segment:
        # Use pre-calculated segment if available
        segment = user_profile.segment 
    else:
        # Default to a basic persona if no history
        if not user_profile.interaction_history or len(user_profile.interaction_history) < 5:
            return "DEFAULT"
        
        # Analyze user interactions to determine persona
        interactions = user_profile.interaction_history
        
        # Count interactions by type
        interaction_counts = {}
        for interaction in interactions:
            interaction_type = interaction.get("interaction_type", "view")
            interaction_counts[interaction_type] = interaction_counts.get(interaction_type, 0) + 1
        
        # Look for price-related interactions
        price_related = sum([
            interaction_counts.get("price_filter", 0),
            interaction_counts.get("sort_by_price", 0),
            interaction_counts.get("view_deals", 0)
        ])
        
        # Look for activity/experience-related interactions
        experience_related = sum([
            interaction_counts.get("view_activity", 0),
            interaction_counts.get("filter_by_activity", 0),
            interaction_counts.get("read_reviews", 0)
        ])
        
        # Check for accessibility preferences
        accessibility_signals = user_profile.preferences.get("accessibility_features", []) if user_profile.preferences else []
        
        # Determine persona based on strongest signal
        if len(accessibility_signals) >= 1:
            return "ACCESSIBILITY_FOCUSED"
        elif price_related > experience_related * 1.5:
            return "PRICE_SENSITIVE"
        elif experience_related > price_related * 1.2:
            return "EXPERIENCE_FOCUSED"
        else:
            return "DEFAULT"
    
    # Map segments to personas as needed
    segment_to_persona = {
        "budget_conscious": "PRICE_SENSITIVE",
        "luxury_seeker": "EXPERIENCE_FOCUSED",
        "activity_focused": "EXPERIENCE_FOCUSED",
        "accessibility_needs": "ACCESSIBILITY_FOCUSED"
    }
    
    return segment_to_persona.get(segment, "DEFAULT")

def personalize_interface(base_template: Dict[str, Any], 
                          user_profile: UserProfile) -> InterfaceConfig:
    """
    Personalizes specific elements of the interface based on user preferences.
    """
    # Create a copy of the base template to customize
    personalized = {
        "layout": dict(base_template["layout"]),
        "components": dict(base_template["components"]),
        "content_emphasis": dict(base_template["content_emphasis"]),
        "notifications": dict(base_template["notifications"]),
        "accessibility": dict(base_template["accessibility"])
    }
    
    # Apply user-specific accessibility preferences if available
    if user_profile.preferences and "accessibility" in user_profile.preferences:
        user_accessibility = user_profile.preferences["accessibility"]
        for key, value in user_accessibility.items():
            if key in personalized["accessibility"]:
                personalized["accessibility"][key] = value
    
    # Adjust notification preferences if specified
    if user_profile.preferences and "notifications" in user_profile.preferences:
        user_notifications = user_profile.preferences["notifications"]
        if "frequency" in user_notifications:
            personalized["notifications"]["frequency"] = user_notifications["frequency"]
        if "types" in user_notifications and user_notifications["types"]:
            personalized["notifications"]["types"] = user_notifications["types"]
    
    # Adjust layout based on device if specified
    if user_profile.preferences and "device_type" in user_profile.preferences:
        device_type = user_profile.preferences["device_type"]
        if device_type == "mobile":
            personalized["layout"]["main_content_columns"] = 1
            personalized["layout"]["compact_view"] = True
        elif device_type == "tablet":
            personalized["layout"]["main_content_columns"] = 2
    
    return InterfaceConfig(**personalized)

# --- API Endpoints ---
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint"""
    return {"status": "ok", "service": "interface-personalization-api"}

@app.post("/interface/personalize", tags=["Personalization"], response_model=InterfaceConfig)
async def personalize_user_interface(user_profile: UserProfile):
    """
    Returns a personalized interface configuration based on the user's profile,
    interaction history, and preferences.
    """
    try:
        # Store or update the user profile
        user_profiles[user_profile.user_id] = user_profile.dict()
        
        # Determine the best template to use as a starting point
        persona = determine_user_persona(user_profile)
        base_template = interface_templates.get(persona, interface_templates["DEFAULT"])
        
        # Further personalize the template based on user-specific preferences
        personalized_config = personalize_interface(base_template, user_profile)
        
        logger.info(f"Generated personalized interface for user {user_profile.user_id} with persona {persona}")
        return personalized_config
        
    except Exception as e:
        logger.error(f"Error personalizing interface: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating personalized interface: {str(e)}")

@app.get("/interface/templates", tags=["Templates"])
async def get_available_templates():
    """Returns available interface templates"""
    return {
        "templates": list(interface_templates.keys()),
        "count": len(interface_templates)
    }

@app.get("/interface/template/{template_id}", tags=["Templates"])
async def get_template(template_id: str):
    """Returns a specific interface template"""
    template_id = template_id.upper()
    if template_id not in interface_templates:
        raise HTTPException(status_code=404, detail=f"Template '{template_id}' not found")
    
    return interface_templates[template_id]

# --- Client-side User Tracking Endpoints ---
class UserInteraction(BaseModel):
    user_id: str
    interaction_type: str
    timestamp: Optional[datetime] = None
    details: Optional[Dict[str, Any]] = None

@app.post("/tracking/interaction", tags=["Tracking"])
async def track_user_interaction(interaction: UserInteraction):
    """
    Records a user interaction for improving personalization.
    This data would typically be stored in a database and processed
    for personalization insights.
    """
    if interaction.timestamp is None:
        interaction.timestamp = datetime.now()
    
    # In a real implementation, this would be stored in a database
    if interaction.user_id not in user_profiles:
        user_profiles[interaction.user_id] = {
            "user_id": interaction.user_id,
            "interaction_history": []
        }
    
    if "interaction_history" not in user_profiles[interaction.user_id]:
        user_profiles[interaction.user_id]["interaction_history"] = []
    
    # Add the interaction to the history
    user_profiles[interaction.user_id]["interaction_history"].append(interaction.dict())
    
    # Limit the size of the history (in production, this would be handled differently)
    history = user_profiles[interaction.user_id]["interaction_history"]
    if len(history) > 100:
        user_profiles[interaction.user_id]["interaction_history"] = history[-100:]
    
    logger.info(f"Tracked {interaction.interaction_type} interaction for user {interaction.user_id}")
    return {"status": "recorded"}

@app.post("/user/preferences", tags=["User Preferences"])
async def update_user_preferences(user_id: str, preferences: Dict[str, Any]):
    """
    Updates a user's preferences for interface personalization.
    """
    if user_id not in user_profiles:
        user_profiles[user_id] = {
            "user_id": user_id,
            "preferences": {}
        }
    
    if "preferences" not in user_profiles[user_id]:
        user_profiles[user_id]["preferences"] = {}
    
    # Update preferences
    user_profiles[user_id]["preferences"].update(preferences)
    
    logger.info(f"Updated preferences for user {user_id}")
    return {"status": "updated", "user_id": user_id}

# If running directly
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8006, reload=False) 