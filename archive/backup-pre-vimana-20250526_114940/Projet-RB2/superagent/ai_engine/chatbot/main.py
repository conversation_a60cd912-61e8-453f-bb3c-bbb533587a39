import os
import requests
import json
from fastapi import FastAPI, HTTPException, Body
from pydantic import BaseModel
from typing import List, Dict, Any
from dotenv import load_dotenv
from functools import lru_cache # Keep cache for external calls

# Load environment variables
load_dotenv()

DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"

app = FastAPI(
    title="Retreat And Be - Chatbot API",
    description="Provides conversational AI capabilities with intent detection.",
    version="0.2.0" # Version incremented
)

class ChatMessage(BaseModel):
    role: str # e.g., "user", "assistant"
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    user_id: str # To potentially personalize responses later

class ChatResponse(BaseModel):
    role: str = "assistant"
    content: str
    intent: str = "unknown" # Add detected intent to response
    entities: Dict[str, Any] = {} # Add detected entities

# Simple FAQ Data (can be loaded from a file later)
FAQ_DATA = {
    "bonjour": "Bonjour ! Comment puis-je vous aider aujourd'hui ?",
    "salut": "Salut ! En quoi puis-je vous assister ?",
    "comment ça va": "Je suis un programme informatique, mais je fonctionne correctement ! Et vous ?",
    "retraite": "Nous proposons diverses retraites de bien-être. Vous cherchez quelque chose de spécifique (yoga, méditation, détox) ?",
    "yoga": "Nous avons plusieurs retraites axées sur le yoga. Souhaitez-vous voir les prochaines dates ?",
    "méditation": "La méditation est au cœur de nombreuses retraites. Recherchez-vous une retraite silencieuse ou guidée ?",
    "prix": "Les prix varient en fonction de la durée et du type de retraite. Pouvez-vous me donner une idée de la retraite qui vous intéresse ?",
    "compte": "Vous pouvez gérer votre compte via la section 'Mon Profil' du site web ou de l'application.",
    "réservation": "Pour réserver, allez sur la page de la retraite souhaitée et cliquez sur 'Réserver'. Avez-vous besoin d'aide pour trouver une retraite ?",
    "aide": "Je peux répondre à des questions simples sur nos retraites et le fonctionnement du site. Pour une aide plus spécifique, vous pouvez contacter notre support.",
    "support": "Vous pouvez contacter le support via le formulaire de contact sur notre site web.",
}

DEFAULT_RESPONSE = "Je ne suis pas sûr de comprendre. Pouvez-vous reformuler votre question ou demander de l'aide pour contacter le support ?"

# Updated chatbot logic using FAQ
def get_faq_chat_response(messages: List[ChatMessage], user_id: str) -> str:
    """Responds based on simple keyword matching in the FAQ."""
    last_user_message = ""
    if messages and messages[-1].role == 'user':
        last_user_message = messages[-1].content.lower().strip()
    
    print(f"Received normalized message from user {user_id}: '{last_user_message}'")

    if not last_user_message:
        return DEFAULT_RESPONSE

    # Simple keyword matching
    for keyword, response in FAQ_DATA.items():
        if keyword in last_user_message:
            print(f"Found matching keyword: '{keyword}'")
            return response

    print("No matching keyword found in FAQ.")
    return DEFAULT_RESPONSE

# --- Intent Detection Logic ---
POSSIBLE_INTENTS = [
    "GREETING", "FAREWELL", "ASK_RETREAT_INFO", "ASK_PRICE", "ASK_AVAILABILITY", 
    "ACCOUNT_ISSUE", "BOOKING_HELP", "GENERAL_QUESTION", "OTHER"
]

@lru_cache(maxsize=128) # Cache intent detection based on last message content
def detect_intent_with_deepseek(last_user_message: str) -> Dict[str, Any]:
    """Uses Deepseek API to detect user intent and extract entities."""
    if not DEEPSEEK_API_KEY:
        print("Warning: Deepseek API key not configured. Intent detection disabled.")
        return {"intent": "unknown", "entities": {}}
        
    if not last_user_message:
        return {"intent": "unknown", "entities": {}}

    prompt = f"Analyze the following user message for a wellness retreat platform chatbot:\n\nUser Message: \"{last_user_message}\"\n\nDetermine the primary intent from this list: {', '.join(POSSIBLE_INTENTS)}. \nAlso extract key entities like retreat type (e.g., yoga, meditation), location, dates, or specific retreat names if mentioned.\n\nRespond ONLY with a JSON object containing two keys: 'intent' (string, one of the provided intents) and 'entities' (object, key-value pairs of extracted entities). If no specific intent fits, use 'OTHER'. If no entities are found, return an empty object for entities." 

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
    }
    data = {
        "model": "deepseek-chat", # Or another suitable model
        "messages": [
            {"role": "system", "content": "You are an expert in analyzing user messages to identify intent and extract entities for a chatbot. Respond ONLY in the requested JSON format."},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 100,
        "temperature": 0.1, # Low temperature for classification/extraction
        "response_format": { "type": "json_object" } # Request JSON output if model supports it
    }

    try:
        response = requests.post(DEEPSEEK_API_URL, headers=headers, json=data, timeout=15)
        response.raise_for_status()
        result = response.json()

        if "choices" in result and len(result["choices"]) > 0:
            content = result["choices"][0]["message"]["content"]
            try:
                # Attempt to parse the JSON content
                parsed_json = json.loads(content)
                intent = parsed_json.get("intent", "OTHER")
                entities = parsed_json.get("entities", {})
                # Validate intent
                if intent not in POSSIBLE_INTENTS:
                    intent = "OTHER"
                print(f"Deepseek detected intent: {intent}, entities: {entities}")    
                return {"intent": intent, "entities": entities}
            except json.JSONDecodeError:
                print(f"Warning: Failed to parse JSON from Deepseek response: {content}")
                # Fallback: Try simple text parsing (less reliable)
                # This part would need careful implementation based on expected text format
                return {"intent": "unknown", "entities": {}} # Fallback to unknown
        else:
            print(f"Warning: Deepseek API response format unexpected: {result}")
            return {"intent": "unknown", "entities": {}}

    except requests.exceptions.RequestException as e:
        print(f"Error calling Deepseek API for intent detection: {e}")
        return {"intent": "error", "entities": {}} # Indicate error
    except Exception as e:
        print(f"An unexpected error occurred during intent detection: {e}")
        return {"intent": "error", "entities": {}}
        
# --- Response Generation Logic ---
# Map intents to predefined responses (can still use FAQ or make more specific)
INTENT_RESPONSES = {
    "GREETING": "Bonjour ! Comment puis-je vous aider avec votre recherche de retraite ?",
    "FAREWELL": "Au revoir ! N'hésitez pas si vous avez d'autres questions.",
    "ASK_RETREAT_INFO": "Nous avons d'excellentes retraites. Cherchez-vous un type spécifique (yoga, méditation...) ou une période particulière ?",
    "ASK_PRICE": "Les tarifs varient. Pour quelle retraite ou type de retraite souhaitez-vous connaître le prix ?",
    "ASK_AVAILABILITY": "Pour vérifier les disponibilités, veuillez consulter la page de la retraite qui vous intéresse. Laquelle est-ce ?",
    "ACCOUNT_ISSUE": "Pour les problèmes de compte, le mieux est de contacter notre support via le formulaire du site web.",
    "BOOKING_HELP": "Vous pouvez réserver directement sur la page de chaque retraite. Rencontrez-vous un problème spécifique ?",
    "GENERAL_QUESTION": "Je peux vous renseigner sur nos retraites. Quelle est votre question ?",
    "OTHER": "Je ne suis pas certain d'avoir compris. Pourriez-vous reformuler ou demander de l'aide ?",
    "unknown": "Hmm, je n'ai pas bien saisi. Pouvez-vous essayer de reformuler ?",
    "error": "Je rencontre un problème technique pour analyser votre demande. Veuillez réessayer plus tard."
}

def generate_response_based_on_intent(intent: str, entities: Dict[str, Any], user_id: str) -> str:
    """Generates a response based on the detected intent."""
    # TODO: Add more sophisticated logic using entities
    # Example: If intent is ASK_RETREAT_INFO and entity 'type' is 'yoga', provide specific yoga info.
    
    return INTENT_RESPONSES.get(intent, INTENT_RESPONSES["OTHER"])

# --- API Endpoint ---
@app.post("/chat", 
          tags=["Chat"],
          summary="Send messages to the chatbot and get an intent-based response")
async def handle_chat(request: ChatRequest) -> ChatResponse:
    """
    Handles a chat interaction using intent detection via Deepseek.
    """
    last_user_message_content = ""
    if request.messages and request.messages[-1].role == 'user':
        last_user_message_content = request.messages[-1].content.strip()

    if not last_user_message_content:
         return ChatResponse(content="Veuillez fournir un message.", intent="unknown")
         
    # Detect intent
    intent_data = detect_intent_with_deepseek(last_user_message_content)
    intent = intent_data["intent"]
    entities = intent_data["entities"]
    
    # Generate response based on intent
    response_content = generate_response_based_on_intent(intent, entities, request.user_id)
    
    return ChatResponse(content=response_content, intent=intent, entities=entities)

# --- Health Check --- 
@app.get("/health", tags=["Health"], summary="Health check endpoint")
async def health_check():
    key_present = bool(DEEPSEEK_API_KEY)
    return {"status": "ok", "api_key_configured": key_present}

# If running directly with python main.py (for local testing without docker)
if __name__ == "__main__":
    import uvicorn
    if not DEEPSEEK_API_KEY:
        print("-"*50)
        print("WARNING: DEEPSEEK_API_KEY environment variable not set.")
        # ... (rest of the warning message) ...
        print("-"*50)
    uvicorn.run(app, host="0.0.0.0", port=8001) 