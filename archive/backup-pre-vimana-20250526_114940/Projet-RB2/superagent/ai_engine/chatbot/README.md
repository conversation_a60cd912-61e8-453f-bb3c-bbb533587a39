# Chatbot pour Retreat And Be

Ce service fournit un chatbot conversationnel pour la plateforme Retreat And Be. Il est conçu pour aider les utilisateurs à trouver des retraites, répondre à des questions fréquentes et fournir une assistance générale.

## Fonctionnalités

- Détection d'intention basée sur l'IA
- Réponses personnalisées basées sur les intentions détectées
- Extraction d'entités pour une meilleure compréhension des requêtes
- Intégration avec l'API Deepseek pour l'analyse du langage naturel
- Support pour différents types de messages (texte, boutons, images, etc.)
- Historique des conversations
- Intégration avec le frontend Front-Audrey-V1-Main-main
- Authentification par token JWT
- Analytiques des interactions utilisateur

## Architecture

Le service chatbot est composé des éléments suivants :

- **API FastAPI** : Expose les endpoints pour interagir avec le chatbot
- **Détection d'intention** : Analyse les messages pour comprendre l'intention de l'utilisateur
- **Extraction d'entités** : Identifie les entités importantes dans les messages
- **Génération de réponses** : Crée des réponses personnalisées basées sur l'intention et les entités
- **Gestion des conversations** : Maintient le contexte des conversations

## Configuration

### Variables d'environnement

- `DEEPSEEK_API_KEY` : Clé API pour le service Deepseek (obligatoire pour la détection d'intention avancée)
- `HOST` : Hôte sur lequel le service sera exécuté (par défaut : 0.0.0.0)
- `PORT` : Port sur lequel le service sera exécuté (par défaut : 8001)
- `LOG_LEVEL` : Niveau de journalisation (par défaut : INFO)
- `JWT_SECRET` : Clé secrète pour la validation des tokens JWT (si l'authentification est activée)

### Dépendances

Voir le fichier `requirements.txt` pour la liste complète des dépendances.

## Utilisation

### Démarrage du service

```bash
# Avec Docker Compose
docker-compose up chatbot-api

# Avec le script de démarrage
../../scripts/start_chatbot.sh

# Manuellement
cd ai_engine/chatbot
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
```

### API Endpoints

#### POST /chat

Envoyer un message au chatbot et recevoir une réponse.

##### Requête

```json
{
  "messages": [
    {
      "role": "user",
      "content": "Je cherche une retraite de yoga en France"
    }
  ],
  "user_id": "user123",
  "conversation_id": "conv456",
  "metadata": {
    "client_info": {
      "platform": "web",
      "language": "fr"
    }
  }
}
```

##### Réponse

```json
{
  "content": "Nous avons plusieurs retraites de yoga en France. Souhaitez-vous voir les prochaines dates ?",
  "intent": "ASK_RETREAT_INFO",
  "entities": {
    "retreat_type": "yoga",
    "location": "France"
  },
  "conversation_id": "conv456",
  "suggestions": [
    "Oui, montrez-moi les dates",
    "Quels sont les prix ?",
    "Je préfère en Espagne"
  ],
  "messageType": "quick_replies",
  "buttons": [
    {
      "text": "Voir les dates",
      "value": "Oui, montrez-moi les dates"
    },
    {
      "text": "Voir les prix",
      "value": "Quels sont les prix ?"
    }
  ]
}
```

#### GET /health

Vérifier l'état du service.

##### Réponse

```json
{
  "status": "ok",
  "api_key_configured": true
}
```

#### GET /conversations/{conversation_id}

Récupérer l'historique d'une conversation.

##### Réponse

```json
{
  "conversation_id": "conv456",
  "messages": [
    {
      "role": "user",
      "content": "Je cherche une retraite de yoga en France",
      "timestamp": "2023-06-15T14:30:00Z"
    },
    {
      "role": "assistant",
      "content": "Nous avons plusieurs retraites de yoga en France. Souhaitez-vous voir les prochaines dates ?",
      "timestamp": "2023-06-15T14:30:05Z",
      "intent": "ASK_RETREAT_INFO"
    }
  ]
}
```

## Types de messages

Le chatbot prend en charge les types de messages suivants :

- **text** : Messages textuels simples
- **buttons** : Messages avec des boutons cliquables
- **quick_replies** : Suggestions de réponses rapides
- **image** : Messages contenant des images
- **card** : Messages formatés sous forme de cartes
- **carousel** : Séries de cartes défilantes
- **audio** : Messages audio
- **video** : Messages vidéo
- **file** : Messages contenant des fichiers à télécharger
- **location** : Messages contenant des informations de localisation
- **typing** : Indicateur de frappe

## Intégration avec Front-Audrey-V1-Main-main

Le chatbot est intégré avec le frontend Front-Audrey-V1-Main-main. L'intégration comprend :

- Un bouton flottant pour ouvrir le chatbot
- Une fenêtre de chat avec historique des messages
- Support pour différents types de messages
- Reconnaissance vocale
- Stockage local des conversations
- Analytiques des interactions

Pour plus de détails sur l'intégration, consultez le fichier `Front-Audrey-V1-Main-main/docs/CHATBOT-INTEGRATION.md`.

## Détection d'intention

Le chatbot utilise l'API Deepseek pour détecter l'intention de l'utilisateur. Les intentions possibles sont :

- **GREETING** : Salutations
- **FAREWELL** : Au revoir
- **ASK_RETREAT_INFO** : Demande d'informations sur les retraites
- **ASK_PRICE** : Demande de prix
- **ASK_AVAILABILITY** : Demande de disponibilité
- **ACCOUNT_ISSUE** : Problème de compte
- **BOOKING_HELP** : Aide à la réservation
- **GENERAL_QUESTION** : Question générale
- **OTHER** : Autre

## Intégration avec superagent

Le chatbot est intégré à superagent et peut être utilisé comme un service indépendant ou comme un composant du système multi-agent. Il communique avec les autres composants via des API REST et peut être orchestré par le système de workflow LangGraph.

## Développement

### Ajout de nouvelles intentions

Pour ajouter une nouvelle intention :

1. Ajoutez l'intention à la liste `POSSIBLE_INTENTS` dans `main.py`
2. Mettez à jour la fonction `generate_response_based_on_intent` pour gérer la nouvelle intention

### Ajout de nouveaux types de messages

Pour ajouter un nouveau type de message :

1. Ajoutez le type à l'enum `MessageType` dans le frontend
2. Créez un nouveau composant pour afficher ce type de message
3. Mettez à jour `MessageContent.tsx` pour utiliser le nouveau composant

Pour contribuer au développement du chatbot, consultez le fichier CONTRIBUTING.md à la racine du projet.
