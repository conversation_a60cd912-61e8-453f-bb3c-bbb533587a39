import csv
import random
import datetime
import os

NUM_INTERACTIONS = 1000
NUM_USERS = 100
NUM_RETREATS = 50
INTERACTION_TYPES = ['view', 'click', 'bookmark', 'reservation_start', 'reservation_complete']
OUTPUT_DIR = "../data/raw"
OUTPUT_FILE = os.path.join(OUTPUT_DIR, "user_interactions.csv")

def generate_fake_interactions():
    """Generates a CSV file with fake user interaction data."""
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    with open(OUTPUT_FILE, 'w', newline='') as csvfile:
        fieldnames = ['user_id', 'retreat_id', 'timestamp', 'interaction_type']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        start_date = datetime.datetime.now() - datetime.timedelta(days=30)

        for i in range(NUM_INTERACTIONS):
            user_id = f"user_{random.randint(1, NUM_USERS):03d}"
            retreat_id = f"RETREAT_{random.randint(1, NUM_RETREATS):03d}"
            timestamp = start_date + datetime.timedelta(seconds=random.randint(0, 30*24*60*60))
            interaction_type = random.choices(INTERACTION_TYPES, weights=[0.5, 0.25, 0.15, 0.07, 0.03], k=1)[0]
            
            writer.writerow({
                'user_id': user_id,
                'retreat_id': retreat_id,
                'timestamp': timestamp.isoformat(),
                'interaction_type': interaction_type
            })
    
    print(f"Successfully generated fake interaction data to {OUTPUT_FILE}")

if __name__ == "__main__":
    generate_fake_interactions() 