import pandas as pd
import os

# Define input and output directories relative to this script
INPUT_DIR = "../data/raw"
OUTPUT_DIR = "../data/processed"

INTERACTIONS_INPUT = os.path.join(INPUT_DIR, "user_interactions.csv")
RETREATS_INPUT = os.path.join(INPUT_DIR, "retreat_data.csv")

INTERACTIONS_OUTPUT = os.path.join(OUTPUT_DIR, "interactions_processed.parquet")
RETREATS_OUTPUT = os.path.join(OUTPUT_DIR, "retreats_processed.parquet")

def preprocess_data():
    """Reads raw CSV data, performs minimal preprocessing, and saves as Parquet."""
    print("Starting preprocessing...")
    
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"Created output directory: {OUTPUT_DIR}")

    # Preprocess Interactions
    try:
        interactions_df = pd.read_csv(INTERACTIONS_INPUT)
        print(f"Read {len(interactions_df)} interactions from {INTERACTIONS_INPUT}")
        
        # Convert timestamp to datetime objects
        interactions_df['timestamp'] = pd.to_datetime(interactions_df['timestamp'])
        
        # Example: Filter out only 'view' and 'reservation_complete' for a simple model later
        # interactions_df = interactions_df[interactions_df['interaction_type'].isin(['view', 'reservation_complete'])]
        
        # Save processed data as Parquet (more efficient)
        interactions_df.to_parquet(INTERACTIONS_OUTPUT, index=False)
        print(f"Saved processed interactions to {INTERACTIONS_OUTPUT}")

    except FileNotFoundError:
        print(f"Error: Input file not found at {INTERACTIONS_INPUT}. Run fetch_user_interactions.py first.")
    except Exception as e:
        print(f"Error processing interactions: {e}")

    # Preprocess Retreats
    try:
        retreats_df = pd.read_csv(RETREATS_INPUT)
        print(f"Read {len(retreats_df)} retreats from {RETREATS_INPUT}")
        
        # Example: Maybe split features string into a list later
        # retreats_df['feature_list'] = retreats_df['features'].str.split(',')
        
        # Save processed data as Parquet
        retreats_df.to_parquet(RETREATS_OUTPUT, index=False)
        print(f"Saved processed retreats to {RETREATS_OUTPUT}")
        
    except FileNotFoundError:
        print(f"Error: Input file not found at {RETREATS_INPUT}. Run fetch_retreat_data.py first.")
    except Exception as e:
        print(f"Error processing retreats: {e}")

    print("Preprocessing finished.")

if __name__ == "__main__":
    # Add pandas dependency check
    try:
        import pandas
    except ImportError:
        print("Error: pandas library not found. Please install it: pip install pandas pyarrow")
        exit(1)
        
    preprocess_data() 