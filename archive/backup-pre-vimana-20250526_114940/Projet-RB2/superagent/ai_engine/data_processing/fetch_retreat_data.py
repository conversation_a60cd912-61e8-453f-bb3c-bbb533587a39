import csv
import random
import os

NUM_RETREATS = 50
OUTPUT_DIR = "../data/raw"
OUTPUT_FILE = os.path.join(OUTPUT_DIR, "retreat_data.csv")

SAMPLE_NAMES = ["Yoga & Meditation", "Digital Detox", "Mindfulness Mountain Escape", "Coastal Wellness Journey", "Ayurveda & Spa", "Silent Retreat", "Nature Immersion", "Creative Writing", "Healthy Cooking", "Fitness Bootcamp"]
SAMPLE_FEATURES = ["wifi", "pool", "spa", "vegan_options", "mountains", "beach", "forest", "luxury", "budget", "pets_allowed", "yoga_studio", "meditation_hall"]

def generate_fake_retreats():
    """Generates a CSV file with fake retreat data."""
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    with open(OUTPUT_FILE, 'w', newline='') as csvfile:
        fieldnames = ['retreat_id', 'name', 'description', 'features']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()

        for i in range(1, NUM_RETREATS + 1):
            retreat_id = f"RETREAT_{i:03d}"
            name = f"{random.choice(SAMPLE_NAMES)} #{random.randint(1, 10)}"
            description = f"A wonderful retreat focusing on {name.split(' #')[0].lower()}. Enjoy peace and tranquility."
            num_features = random.randint(2, 5)
            features = ",".join(random.sample(SAMPLE_FEATURES, num_features))
            
            writer.writerow({
                'retreat_id': retreat_id,
                'name': name,
                'description': description,
                'features': features
            })
            
    print(f"Successfully generated fake retreat data to {OUTPUT_FILE}")

if __name__ == "__main__":
    generate_fake_retreats() 