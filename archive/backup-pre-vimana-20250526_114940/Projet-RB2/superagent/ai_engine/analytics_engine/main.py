import os
import pandas as pd
import numpy as np
from fastapi import FastAPI, HTTPException, Query
from typing import List, Dict, Any, Optional
from datetime import datetime, timed<PERSON><PERSON>
from prophet import Prophet  # Import Prophet for time series forecasting

app = FastAPI(
    title="Retreat And Be - Analytics Engine API",
    description="Provides basic analytics like trending items and user segmentation.",
    version="0.1.0"
)

# --- Configuration ---
# Path inside the container, mapped from ./data via docker-compose volume
DATA_DIR = "./data/processed" 
INTERACTIONS_DATA_FILE = os.path.join(DATA_DIR, "interactions_processed.parquet")

# --- Global State for Data ---
analytics_data = {
    "interactions_df": None
}

# --- Data Loading (at startup) ---

def load_analytics_data():
    """Loads interaction data needed for analytics."""
    print("Attempting to load interaction data for analytics...")
    if not os.path.exists(INTERACTIONS_DATA_FILE):
        print(f"ERROR: Interaction data file not found at {INTERACTIONS_DATA_FILE}")
        print("Please run the data generation scripts in 'ai_engine/data_processing/' first.")
        return

    try:
        df = pd.read_parquet(INTERACTIONS_DATA_FILE)
        # Ensure timestamp is datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        analytics_data["interactions_df"] = df
        print(f"Loaded {len(df)} interactions for analytics.")

    except Exception as e:
        print(f"ERROR during analytics data loading: {e}")
        analytics_data["interactions_df"] = None

@app.on_event("startup")
async def startup_event():
    """Load data when the application starts."""
    load_analytics_data()

# --- Analytics Logic ---

def get_trending_retreats(days: int, top_n: int) -> List[Dict[str, Any]]:
    """Calculates trending retreats based on interaction count in the last N days."""
    if analytics_data["interactions_df"] is None:
        print("Interaction data not loaded. Cannot calculate trends.")
        return []

    df = analytics_data["interactions_df"]
    
    # Filter data for the last N days
    cutoff_date = datetime.now() - timedelta(days=days)
    recent_interactions = df[df['timestamp'] >= cutoff_date]
    
    if recent_interactions.empty:
        print(f"No interactions found in the last {days} days.")
        return []

    # Count interactions per retreat (simple count for now, could weight types later)
    trending = recent_interactions['retreat_id'].value_counts().reset_index()
    trending.columns = ['retreat_id', 'interaction_count']
    
    # Get top N
    top_trending = trending.head(top_n)
    
    print(f"Calculated top {len(top_trending)} trending retreats for the last {days} days.")
    return top_trending.to_dict('records')

def get_user_segment(user_id: str) -> str:
    """Provides a simple activity-based user segment."""
    if analytics_data["interactions_df"] is None:
        print("Interaction data not loaded. Cannot determine user segment.")
        return "unknown"
        
    df = analytics_data["interactions_df"]
    user_interactions = df[df['user_id'] == user_id]
    
    interaction_count = len(user_interactions)
    
    # Simple segmentation logic
    if interaction_count >= 15:
        segment = "very_active"
    elif interaction_count >= 5:
        segment = "active"
    elif interaction_count > 0:
        segment = "low_activity"
    else:
        segment = "inactive"
        
    print(f"Segment for user {user_id}: {segment} (based on {interaction_count} interactions)")
    return segment

def predict_retreat_demand(retreat_id: str, days_to_predict: int = 30) -> List[Dict[str, Any]]:
    """Predicts future demand for a specific retreat based on past interaction patterns."""
    if analytics_data["interactions_df"] is None:
        print("Interaction data not loaded. Cannot predict demand.")
        return []

    df = analytics_data["interactions_df"]
    
    # Filter data for the specific retreat
    retreat_interactions = df[df['retreat_id'] == retreat_id]
    
    if retreat_interactions.empty:
        print(f"No interactions found for retreat {retreat_id}.")
        return []

    # Aggregate interactions by day
    retreat_interactions['date'] = retreat_interactions['timestamp'].dt.date
    daily_counts = retreat_interactions.groupby('date').size().reset_index()
    daily_counts.columns = ['ds', 'y']  # Prophet requires these column names
    
    # Convert to datetime for Prophet
    daily_counts['ds'] = pd.to_datetime(daily_counts['ds'])
    
    # Create and train the Prophet model
    try:
        model = Prophet()
        model.fit(daily_counts)
        
        # Create future dataframe for prediction
        future = model.make_future_dataframe(periods=days_to_predict)
        forecast = model.predict(future)
        
        # Filter only future predictions
        last_date = daily_counts['ds'].max()
        future_forecast = forecast[forecast['ds'] > last_date]
        
        # Prepare response
        predictions = []
        for _, row in future_forecast.iterrows():
            predictions.append({
                "date": row['ds'].strftime('%Y-%m-%d'),
                "predicted_interactions": max(0, round(row['yhat'])),  # Ensure non-negative
                "lower_bound": max(0, round(row['yhat_lower'])),
                "upper_bound": max(0, round(row['yhat_upper']))
            })
        
        print(f"Generated {len(predictions)} days of demand predictions for retreat {retreat_id}.")
        return predictions
        
    except Exception as e:
        print(f"Error during demand prediction: {e}")
        return []

def detect_retreat_anomalies(retreat_id: str, threshold: float = 2.0, days_back: int = 90) -> List[Dict[str, Any]]:
    """Detects anomalies in interaction patterns for a specific retreat."""
    if analytics_data["interactions_df"] is None:
        print("Interaction data not loaded. Cannot detect anomalies.")
        return []

    df = analytics_data["interactions_df"]
    
    # Filter data for the specific retreat
    retreat_interactions = df[df['retreat_id'] == retreat_id]
    
    if retreat_interactions.empty:
        print(f"No interactions found for retreat {retreat_id}.")
        return []
    
    # Filter for analysis period (last N days)
    cutoff_date = datetime.now() - timedelta(days=days_back)
    recent_interactions = retreat_interactions[retreat_interactions['timestamp'] >= cutoff_date]
    
    if len(recent_interactions) < 7:  # Need at least a week of data
        print(f"Insufficient data for retreat {retreat_id}. Need at least 7 days.")
        return []

    # Aggregate interactions by day
    recent_interactions['date'] = recent_interactions['timestamp'].dt.date
    daily_counts = recent_interactions.groupby('date').size().reset_index()
    daily_counts.columns = ['ds', 'y']  # Prophet requires these column names
    
    # Convert to datetime for Prophet
    daily_counts['ds'] = pd.to_datetime(daily_counts['ds'])
    
    try:
        # Train a Prophet model
        model = Prophet()
        model.fit(daily_counts)
        
        # Make predictions for the historical period
        predictions = model.predict(daily_counts[['ds']])
        
        # Calculate residuals (difference between actual and predicted)
        predictions = predictions.merge(daily_counts, on='ds', how='inner')
        predictions['residual'] = predictions['y'] - predictions['yhat']
        
        # Calculate z-scores of residuals to identify anomalies
        mean_residual = predictions['residual'].mean()
        std_residual = predictions['residual'].std()
        predictions['z_score'] = (predictions['residual'] - mean_residual) / max(std_residual, 1e-8)  # Avoid division by zero
        
        # Identify anomalies where z-score exceeds threshold
        anomalies = predictions[abs(predictions['z_score']) > threshold].copy()
        if anomalies.empty:
            print(f"No anomalies detected for retreat {retreat_id} with threshold {threshold}.")
            return []
        
        # Format response
        anomaly_list = []
        for _, row in anomalies.iterrows():
            anomaly_list.append({
                "date": row['ds'].strftime('%Y-%m-%d'),
                "actual_interactions": int(row['y']),
                "expected_interactions": round(row['yhat']),
                "deviation": round(row['residual']),
                "z_score": round(row['z_score'], 2),
                "is_high": bool(row['z_score'] > 0),  # High = more than expected, Low = less than expected
            })
        
        print(f"Detected {len(anomaly_list)} anomalies for retreat {retreat_id}.")
        return anomaly_list
        
    except Exception as e:
        print(f"Error during anomaly detection: {e}")
        return []

def get_retreat_dashboard(retreat_id: str, days_back: int = 90, days_forecast: int = 30) -> Dict[str, Any]:
    """Generates a comprehensive analytics dashboard for a specific retreat."""
    if analytics_data["interactions_df"] is None:
        print("Interaction data not loaded. Cannot generate dashboard.")
        return {}

    df = analytics_data["interactions_df"]
    
    # Filter data for the specific retreat
    retreat_interactions = df[df['retreat_id'] == retreat_id]
    
    if retreat_interactions.empty:
        print(f"No interactions found for retreat {retreat_id}.")
        return {}

    # Filter for analysis period
    cutoff_date = datetime.now() - timedelta(days=days_back)
    recent_interactions = retreat_interactions[retreat_interactions['timestamp'] >= cutoff_date]
    
    if recent_interactions.empty:
        print(f"No recent interactions found for retreat {retreat_id} in the last {days_back} days.")
        return {}
    
    # 1. Usage Statistics
    total_interactions = len(recent_interactions)
    unique_users = recent_interactions['user_id'].nunique()
    
    # Interactions by type
    interaction_types = recent_interactions['interaction_type'].value_counts().to_dict()
    
    # Daily interaction count
    recent_interactions['date'] = recent_interactions['timestamp'].dt.date
    daily_counts = recent_interactions.groupby('date').size().reset_index()
    daily_counts.columns = ['date', 'count']
    
    # Recent trend (last 7 days vs previous 7 days)
    last_7_days = datetime.now() - timedelta(days=7)
    last_7_day_count = len(recent_interactions[recent_interactions['timestamp'] >= last_7_days])
    previous_7_days = len(recent_interactions[(recent_interactions['timestamp'] < last_7_days) & 
                                           (recent_interactions['timestamp'] >= last_7_days - timedelta(days=7))])
    week_over_week_change = 0
    if previous_7_days > 0:
        week_over_week_change = round(((last_7_day_count - previous_7_days) / previous_7_days) * 100)
    
    # 2. Get future demand predictions
    demand_predictions = predict_retreat_demand(retreat_id, days_forecast)
    
    # 3. Get anomalies
    anomalies = detect_retreat_anomalies(retreat_id, threshold=2.0, days_back=days_back)
    
    # 4. Comparison with similar retreats (top 10 by interaction count)
    # Get all retreat IDs except the current one
    all_retreats_df = df[df['retreat_id'] != retreat_id]
    top_retreats = all_retreats_df['retreat_id'].value_counts().head(10).reset_index()
    top_retreats.columns = ['retreat_id', 'total_interactions']
    
    # Calculate interaction rate for the current retreat
    current_retreat_daily_rate = total_interactions / days_back
    
    # Calculate rates for comparison retreats
    comparison = []
    for _, row in top_retreats.iterrows():
        comp_retreat_id = row['retreat_id']
        comp_interactions = row['total_interactions']
        comp_retreat_data = all_retreats_df[all_retreats_df['retreat_id'] == comp_retreat_id]
        comp_recent = comp_retreat_data[comp_retreat_data['timestamp'] >= cutoff_date]
        
        # Only include if we have data in the same time period
        if not comp_recent.empty:
            comp_daily_rate = len(comp_recent) / days_back
            comparison.append({
                'retreat_id': comp_retreat_id,
                'total_interactions': len(comp_recent),
                'daily_rate': round(comp_daily_rate, 2),
                'relative_performance': round((current_retreat_daily_rate / max(comp_daily_rate, 0.1)) * 100) - 100
            })
    
    # 5. Time-of-day analysis
    recent_interactions['hour'] = recent_interactions['timestamp'].dt.hour
    hourly_distribution = recent_interactions.groupby('hour').size().reset_index()
    hourly_distribution.columns = ['hour', 'count']
    hourly_distribution = hourly_distribution.to_dict('records')
    
    # 6. Day-of-week analysis
    recent_interactions['day_of_week'] = recent_interactions['timestamp'].dt.dayofweek  # 0 = Monday, 6 = Sunday
    day_distribution = recent_interactions.groupby('day_of_week').size().reset_index()
    day_distribution.columns = ['day_of_week', 'count']
    day_map = {0: 'Monday', 1: 'Tuesday', 2: 'Wednesday', 3: 'Thursday', 4: 'Friday', 5: 'Saturday', 6: 'Sunday'}
    day_distribution['day_name'] = day_distribution['day_of_week'].map(day_map)
    day_distribution = day_distribution.to_dict('records')
    
    # Compile dashboard data
    dashboard = {
        'retreat_id': retreat_id,
        'generated_at': datetime.now().isoformat(),
        'analysis_period': {
            'days_analyzed': days_back,
            'start_date': cutoff_date.strftime('%Y-%m-%d'),
            'end_date': datetime.now().strftime('%Y-%m-%d')
        },
        'summary_stats': {
            'total_interactions': total_interactions,
            'unique_users': unique_users,
            'average_daily_interactions': round(total_interactions / days_back, 2),
            'week_over_week_change': week_over_week_change
        },
        'interaction_breakdown': interaction_types,
        'daily_interactions': daily_counts.to_dict('records'),
        'hourly_distribution': hourly_distribution,
        'day_of_week_distribution': day_distribution,
        'future_demand': {
            'days_forecasted': days_forecast,
            'predictions': demand_predictions
        },
        'anomalies': {
            'count': len(anomalies),
            'details': anomalies
        },
        'market_comparison': {
            'retreats_compared': len(comparison),
            'comparison_data': comparison
        }
    }
    
    print(f"Generated comprehensive dashboard for retreat {retreat_id}.")
    return dashboard

# --- API Endpoints ---

@app.get("/analytics/trending_retreats", 
         tags=["Analytics"],
         summary="Get trending retreats based on recent interactions")
async def trending_retreats_endpoint(
    days: int = Query(7, description="Number of past days to consider for trending calculation"),
    top_n: int = Query(5, description="Number of top trending retreats to return")
) -> List[Dict[str, Any]]:
    """
    Returns a list of the most interacted-with retreats in the specified recent period.
    """
    if analytics_data["interactions_df"] is None:
        raise HTTPException(status_code=503, detail="Analytics data not available. Please try again later.")
        
    trends = get_trending_retreats(days, top_n)
    return trends

@app.get("/analytics/user_segment/{user_id}", 
         tags=["Analytics"],
         summary="Get a simple activity segment for a user")
async def user_segment_endpoint(user_id: str) -> Dict[str, str]:
    """
    Returns a basic user segment (e.g., active, inactive) based on interaction count.
    """
    if analytics_data["interactions_df"] is None:
        raise HTTPException(status_code=503, detail="Analytics data not available. Please try again later.")

    segment = get_user_segment(user_id)
    return {"user_id": user_id, "segment": segment}

@app.get("/analytics/predict_demand/{retreat_id}", 
         tags=["Analytics"],
         summary="Predict future demand for a specific retreat")
async def predict_demand_endpoint(
    retreat_id: str,
    days: int = Query(30, description="Number of days to forecast into the future")
) -> Dict[str, Any]:
    """
    Predicts future demand (expected interaction count) for a specific retreat 
    based on historical interaction patterns.
    """
    if analytics_data["interactions_df"] is None:
        raise HTTPException(status_code=503, detail="Analytics data not available. Please try again later.")
        
    predictions = predict_retreat_demand(retreat_id, days)
    
    if not predictions:
        raise HTTPException(status_code=404, detail=f"No sufficient data available for retreat {retreat_id} to make predictions.")
    
    return {
        "retreat_id": retreat_id, 
        "days_predicted": days,
        "predictions": predictions
    }

@app.get("/analytics/detect_anomalies/{retreat_id}", 
         tags=["Analytics"],
         summary="Detect anomalies in interaction patterns for a retreat")
async def detect_anomalies_endpoint(
    retreat_id: str,
    threshold: float = Query(2.0, description="Z-score threshold for anomaly detection (default: 2.0)"),
    days_back: int = Query(90, description="Number of past days to analyze")
) -> Dict[str, Any]:
    """
    Detects days with abnormal interaction counts for a retreat compared to expected patterns.
    Returns dates where actual interactions significantly differ from the predicted pattern.
    A higher threshold will detect only more extreme anomalies.
    """
    if analytics_data["interactions_df"] is None:
        raise HTTPException(status_code=503, detail="Analytics data not available. Please try again later.")
        
    anomalies = detect_retreat_anomalies(retreat_id, threshold, days_back)
    
    if not anomalies:
        return {
            "retreat_id": retreat_id, 
            "anomalies_detected": 0,
            "message": f"No anomalies detected with threshold {threshold}.",
            "anomalies": []
        }
    
    return {
        "retreat_id": retreat_id, 
        "anomalies_detected": len(anomalies),
        "threshold_used": threshold,
        "days_analyzed": days_back,
        "anomalies": anomalies
    }

@app.get("/analytics/dashboard/{retreat_id}", 
         tags=["Dashboard"],
         summary="Get comprehensive analytics dashboard for a retreat")
async def retreat_dashboard_endpoint(
    retreat_id: str,
    days_back: int = Query(90, description="Number of past days to analyze"),
    days_forecast: int = Query(30, description="Number of days to forecast into the future")
) -> Dict[str, Any]:
    """
    Provides a complete analytics dashboard for retreat owners, including:
    - Usage statistics and trends
    - Future demand predictions
    - Anomaly detection
    - Market comparison
    - Time and day pattern analysis
    
    This endpoint combines multiple analytics functions to deliver a comprehensive view
    in a single API call, perfect for populating dashboards.
    """
    if analytics_data["interactions_df"] is None:
        raise HTTPException(status_code=503, detail="Analytics data not available. Please try again later.")
        
    dashboard = get_retreat_dashboard(retreat_id, days_back, days_forecast)
    
    if not dashboard:
        raise HTTPException(status_code=404, detail=f"Insufficient data available for retreat {retreat_id} dashboard.")
    
    return dashboard

@app.get("/health", tags=["Health"], summary="Health check endpoint")
async def health_check():
    """Simple health check to confirm the API is running and data is loaded."""
    data_loaded = analytics_data["interactions_df"] is not None
    status = "ok" if data_loaded else "degraded (data not loaded)"
    return {"status": status, "interaction_data_loaded": data_loaded}

# If running directly
if __name__ == "__main__":
    import uvicorn
    if not os.path.exists(INTERACTIONS_DATA_FILE):
        print("-" * 50)
        print(f"ERROR: Interaction data file not found at {INTERACTIONS_DATA_FILE}")
        print("Please run the data generation scripts first:")
        print("  cd ai_engine/data_processing")
        print("  python fetch_user_interactions.py")
        print("  python preprocess_data.py")
        print("-" * 50)
        exit(1)
        
    uvicorn.run("main:app", host="0.0.0.0", port=8003, reload=False) 