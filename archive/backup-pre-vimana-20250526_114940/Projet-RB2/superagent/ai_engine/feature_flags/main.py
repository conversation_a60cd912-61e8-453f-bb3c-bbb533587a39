from fastapi import FastAP<PERSON>
from pydantic import BaseModel
from typing import Dict, Any
import hashlib # For potential future A/B logic

app = FastAPI(
    title="Retreat And Be - Feature Flags API",
    description="Provides feature flags and configuration for experiments.",
    version="0.1.0"
)

class FlagsResponse(BaseModel):
    flags: Dict[str, Any]

# --- Flag Logic ---

def get_flags_for_user(user_id: str) -> Dict[str, Any]:
    """Returns feature flags for a given user. Static for now."""
    
    # Static configuration initially
    flags = {
        "recommender_algorithm": "hybrid", # Could be 'content_only', 'hybrid', 'popular'
        "show_recommendation_explanations": True,
        "chatbot_use_intent_detection": True,
        "content_generator_model": "deepseek-chat",
        "enable_location_reranking": True
        # Add more flags as needed for experiments
    }

    # --- Example A/B Logic (for future use) ---
    # hash_val = int(hashlib.sha1(user_id.encode()).hexdigest(), 16)
    # 
    # # Example: Split users 50/50 for recommender algorithm
    # if hash_val % 2 == 0:
    #     flags["recommender_algorithm"] = "hybrid"
    # else:
    #     flags["recommender_algorithm"] = "content_only" 
    #
    # # Example: Roll out explanations to 20% of users
    # if hash_val % 10 < 2:
    #      flags["show_recommendation_explanations"] = True
    # else:
    #      flags["show_recommendation_explanations"] = False
    # -------------------------------------------
    
    print(f"Returning flags for user {user_id}: {flags}")
    return flags

# --- API Endpoint ---
@app.get("/flags/{user_id}", 
         tags=["Feature Flags"],
         summary="Get feature flags configuration for a user")
async def get_user_flags(user_id: str) -> FlagsResponse:
    """
    Retrieves the current feature flag settings for the specified user.
    (Currently returns static flags).
    """
    flags = get_flags_for_user(user_id)
    return FlagsResponse(flags=flags)

@app.get("/health", tags=["Health"], summary="Health check endpoint")
async def health_check():
    return {"status": "ok"}

# --- Main Block ---
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004) 