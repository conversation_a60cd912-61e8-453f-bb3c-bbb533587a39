import os
from typing import Dict, Any, List, Optional, Union
from fastapi import FastAPI, HTTPException, Query, Body
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from prophet import Prophet
from scipy.optimize import minimize
from pulp import LpMaximize, LpProblem, LpStatus, LpVariable, lpSum, PULP_CBC_CMD
import logging
import random
from tenacity import retry, stop_after_attempt, wait_fixed

app = FastAPI(
    title="Retreat And Be - Dynamic Pricing API",
    description="Provides dynamic pricing optimization and multi-objective strategies",
    version="0.1.0"
)

# --- CORS Configuration ---
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Logging Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- API Models ---
class PricingFactors(BaseModel):
    """Model for pricing input factors"""
    base_price: float = Field(..., description="Base price for the retreat")
    competitor_prices: List[float] = Field(..., description="List of competitor prices for similar retreats")
    seasonality_factor: float = Field(..., gt=0, le=2, description="Seasonal demand factor (0.5 = low season, 1 = normal, 2 = high season)")
    occupancy_rate: float = Field(..., ge=0, le=1, description="Current occupancy rate (0-1)")
    days_until_start: int = Field(..., ge=0, description="Days until retreat starts")
    historical_booking_pace: Optional[List[Dict[str, Any]]] = Field(None, description="Historical booking data by day")
    min_acceptable_price: Optional[float] = Field(None, description="Minimum acceptable price")
    max_acceptable_price: Optional[float] = Field(None, description="Maximum acceptable price")
    price_sensitivity: float = Field(0.5, ge=0, le=1, description="Market price sensitivity (0-1)")
    
class OptimizationConstraints(BaseModel):
    """Model for optimization constraints"""
    min_profit_margin: float = Field(0.2, ge=0, le=1, description="Minimum profit margin (0-1)")
    max_price_increase: float = Field(0.5, ge=0, description="Maximum allowed price increase factor")
    max_price_decrease: float = Field(0.3, ge=0, description="Maximum allowed price decrease factor")
    occupancy_target: float = Field(0.85, ge=0, le=1, description="Target occupancy rate (0-1)")
    
class DynamicPricingRequest(BaseModel):
    """Model for dynamic pricing request"""
    retreat_id: str = Field(..., description="ID of the retreat")
    pricing_factors: PricingFactors = Field(..., description="Pricing input factors")
    optimization_constraints: Optional[OptimizationConstraints] = Field(None, description="Optimization constraints")
    objectives: List[str] = Field(["revenue", "occupancy"], description="Optimization objectives in order of priority")
    
class DynamicPricingResponse(BaseModel):
    """Model for dynamic pricing response"""
    retreat_id: str
    recommended_price: float
    price_range: Dict[str, float]
    expected_outcomes: Dict[str, float]
    pricing_factors_influence: Dict[str, float]
    scenario_analysis: List[Dict[str, Any]]

class PricingExperiment(BaseModel):
    """Model for A/B testing pricing strategies"""
    retreat_id: str
    base_price: float
    strategy_a: Dict[str, Any]
    strategy_b: Dict[str, Any]
    experiment_duration_days: int = Field(14, description="Duration of the experiment in days")
    metrics: List[str] = Field(["conversion_rate", "revenue"], description="Metrics to evaluate")

# --- Mock Data Store (replace with DB in production) ---
# In a real implementation, this would be a database
pricing_experiments = {}
historical_prices = {}

# --- Pricing Logic ---
def calculate_demand_estimate(price: float, competitor_avg: float, sensitivity: float) -> float:
    """
    Estimates demand based on price relative to competitor pricing.
    Returns a factor where 1.0 = 100% of expected demand.
    """
    # Simple price elasticity model
    # As our price increases relative to competitors, demand decreases
    if competitor_avg <= 0:
        return 1.0
        
    price_ratio = price / competitor_avg
    
    # Apply a sigmoid function to create a realistic demand curve
    elasticity = 4 * sensitivity  # Scale sensitivity for sigmoid
    demand_factor = 1 / (1 + np.exp(elasticity * (price_ratio - 1)))
    
    # Normalize to ensure demand_factor is 0.5 when price equals competitor average
    demand_factor = demand_factor * 2
    
    # Cap at reasonable limits
    return max(0.1, min(demand_factor, 2.0))

def generate_price_scenarios(
    base_price: float, 
    recommended_price: float,
    min_price: float,
    max_price: float,
    sensitivity: float,
    competitor_avg: float
) -> List[Dict[str, Any]]:
    """
    Generates price scenarios for analysis.
    """
    scenarios = []
    
    # Define price points to analyze
    price_points = [
        min_price,
        base_price * 0.9,
        base_price,
        recommended_price,
        base_price * 1.1,
        max_price
    ]
    
    # Remove duplicates and sort
    price_points = sorted(list(set([round(p, 2) for p in price_points])))
    
    for price in price_points:
        demand_factor = calculate_demand_estimate(price, competitor_avg, sensitivity)
        scenarios.append({
            "price": price,
            "estimated_demand_factor": round(demand_factor, 2),
            "relative_revenue": round(price * demand_factor / (base_price * calculate_demand_estimate(base_price, competitor_avg, sensitivity)), 2),
            "type": "recommended" if abs(price - recommended_price) < 0.01 else "alternative"
        })
    
    return scenarios

def calculate_dynamic_price(
    pricing_factors: PricingFactors, 
    optimization_constraints: Optional[OptimizationConstraints] = None
) -> Dict[str, Any]:
    """
    Calculates optimal dynamic price based on provided factors and constraints.
    """
    # Set default constraints if not provided
    if optimization_constraints is None:
        optimization_constraints = OptimizationConstraints()
    
    # Extract pricing factors
    base_price = pricing_factors.base_price
    competitor_prices = pricing_factors.competitor_prices
    seasonality = pricing_factors.seasonality_factor
    occupancy = pricing_factors.occupancy_rate
    days_until_start = pricing_factors.days_until_start
    sensitivity = pricing_factors.price_sensitivity
    
    # Set price bounds
    min_price = pricing_factors.min_acceptable_price or base_price * 0.7
    max_price = pricing_factors.max_acceptable_price or base_price * 2.0
    
    # Apply constraint-based limits
    min_price = max(min_price, base_price * (1 - optimization_constraints.max_price_decrease))
    max_price = min(max_price, base_price * (1 + optimization_constraints.max_price_increase))
    
    # --- Compute price factors ---
    
    # 1. Competitor pricing factor
    avg_competitor_price = sum(competitor_prices) / len(competitor_prices) if competitor_prices else base_price
    competitor_factor = avg_competitor_price / base_price if base_price > 0 else 1.0
    
    # 2. Seasonality factor directly from input
    seasonality_factor = seasonality
    
    # 3. Occupancy factor (higher occupancy = higher price)
    # Sigmoid function to create a smooth curve with steeper prices at high occupancy
    occupancy_factor = 1 + 0.5 * (1 / (1 + np.exp(-10 * (occupancy - 0.7))))
    
    # 4. Time-to-event factor (closer to event = higher price, unless occupancy is low)
    if days_until_start <= 7:
        if occupancy < 0.5:  # last-minute discount for low occupancy
            time_factor = 0.9
        else:  # last-minute premium for high occupancy
            time_factor = 1.2
    elif days_until_start <= 30:
        time_factor = 1.1  # slight premium for upcoming retreats
    else:
        time_factor = 1.0  # normal price for far-future retreats
    
    # Basic price calculation from factors
    calculated_price = base_price * competitor_factor * seasonality_factor * occupancy_factor * time_factor
    
    # Adjust based on price sensitivity
    # Higher sensitivity means price moves closer to competitor average
    sensitivity_adjusted_price = (
        calculated_price * (1 - sensitivity) + 
        avg_competitor_price * sensitivity
    )
    
    # Ensure price is within acceptable bounds
    recommended_price = max(min_price, min(sensitivity_adjusted_price, max_price))
    
    # Round to appropriate price point (e.g., $X99.99)
    recommended_price = round(recommended_price / 10) * 10 - 0.01
    if recommended_price <= 0:
        recommended_price = base_price  # Fallback to base price if calculation fails
    
    # Calculate expected outcomes
    estimated_demand_factor = calculate_demand_estimate(recommended_price, avg_competitor_price, sensitivity)
    expected_occupancy = min(1.0, occupancy + (1 - occupancy) * estimated_demand_factor)
    expected_revenue = recommended_price * expected_occupancy
    
    # Factor influence (how much each factor contributed to the final price adjustment)
    price_delta = recommended_price - base_price
    if abs(price_delta) < 0.01:  # If price barely changed
        factor_influence = {
            "competitor_pricing": 0,
            "seasonality": 0,
            "occupancy": 0,
            "time_to_event": 0,
            "price_sensitivity": 0
        }
    else:
        factor_contribution = {
            "competitor_pricing": abs(competitor_factor - 1) * base_price,
            "seasonality": abs(seasonality_factor - 1) * base_price,
            "occupancy": abs(occupancy_factor - 1) * base_price,
            "time_to_event": abs(time_factor - 1) * base_price,
            "price_sensitivity": abs(sensitivity * (avg_competitor_price - calculated_price)) / base_price
        }
        total_contribution = sum(factor_contribution.values())
        
        if total_contribution > 0:
            factor_influence = {
                factor: round(100 * contrib / total_contribution)
                for factor, contrib in factor_contribution.items()
            }
        else:
            factor_influence = {factor: 0 for factor in factor_contribution}
    
    # Generate scenario analysis
    scenarios = generate_price_scenarios(
        base_price, recommended_price, min_price, max_price, 
        sensitivity, avg_competitor_price
    )
    
    return {
        "recommended_price": recommended_price,
        "price_range": {
            "min_price": min_price,
            "max_price": max_price
        },
        "expected_outcomes": {
            "occupancy": expected_occupancy,
            "revenue": expected_revenue,
            "estimated_demand_factor": estimated_demand_factor
        },
        "pricing_factors_influence": factor_influence,
        "scenario_analysis": scenarios
    }

# --- API Endpoints ---
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint"""
    return {"status": "ok", "service": "dynamic-pricing-api"}

@app.post("/pricing/dynamic", tags=["Pricing"], response_model=DynamicPricingResponse)
async def dynamic_pricing_endpoint(request: DynamicPricingRequest):
    """
    Calculates an optimal dynamic price based on various factors
    including competition, seasonality, demand, and time to retreat.
    """
    try:
        pricing_result = calculate_dynamic_price(
            request.pricing_factors, 
            request.optimization_constraints
        )
        
        # Construct response
        response = DynamicPricingResponse(
            retreat_id=request.retreat_id,
            recommended_price=pricing_result["recommended_price"],
            price_range=pricing_result["price_range"],
            expected_outcomes=pricing_result["expected_outcomes"],
            pricing_factors_influence=pricing_result["pricing_factors_influence"],
            scenario_analysis=pricing_result["scenario_analysis"]
        )
        
        logger.info(f"Generated dynamic price of {response.recommended_price} for retreat {request.retreat_id}")
        return response
        
    except Exception as e:
        logger.error(f"Error in dynamic pricing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error calculating dynamic price: {str(e)}")

@app.post("/pricing/experiment", tags=["Pricing Experiments"])
async def create_pricing_experiment(experiment: PricingExperiment):
    """
    Creates an A/B test for two different pricing strategies.
    """
    experiment_id = f"{experiment.retreat_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Store experiment configuration
    pricing_experiments[experiment_id] = {
        "config": experiment.dict(),
        "start_date": datetime.now().isoformat(),
        "end_date": (datetime.now() + timedelta(days=experiment.experiment_duration_days)).isoformat(),
        "status": "active",
        "results": {
            "strategy_a": {metric: 0 for metric in experiment.metrics},
            "strategy_b": {metric: 0 for metric in experiment.metrics},
            "winner": None
        }
    }
    
    logger.info(f"Created pricing experiment {experiment_id} for retreat {experiment.retreat_id}")
    return {
        "experiment_id": experiment_id,
        "status": "active",
        "message": f"Experiment created successfully. Will run for {experiment.experiment_duration_days} days."
    }

@app.get("/pricing/experiment/{experiment_id}", tags=["Pricing Experiments"])
async def get_pricing_experiment(experiment_id: str):
    """
    Retrieves the status and results of a pricing experiment.
    """
    if experiment_id not in pricing_experiments:
        raise HTTPException(status_code=404, detail=f"Experiment {experiment_id} not found")
    
    return pricing_experiments[experiment_id]

@app.post("/pricing/experiment/{experiment_id}/evaluate", tags=["Pricing Experiments"])
async def evaluate_pricing_experiment(
    experiment_id: str,
    metrics: Dict[str, Dict[str, float]] = Body(..., description="Experiment metrics for each strategy")
):
    """
    Evaluates the results of a pricing experiment and determines the winner.
    """
    if experiment_id not in pricing_experiments:
        raise HTTPException(status_code=404, detail=f"Experiment {experiment_id} not found")
    
    experiment = pricing_experiments[experiment_id]
    
    # Update with provided metrics
    for strategy, strategy_metrics in metrics.items():
        if strategy in ["strategy_a", "strategy_b"]:
            experiment["results"][strategy].update(strategy_metrics)
    
    # Determine the winner based on primary metric
    primary_metric = experiment["config"]["metrics"][0]
    if experiment["results"]["strategy_a"][primary_metric] > experiment["results"]["strategy_b"][primary_metric]:
        experiment["results"]["winner"] = "strategy_a"
    elif experiment["results"]["strategy_b"][primary_metric] > experiment["results"]["strategy_a"][primary_metric]:
        experiment["results"]["winner"] = "strategy_b"
    else:
        # If tied on primary, check secondary metrics
        if len(experiment["config"]["metrics"]) > 1:
            secondary_metric = experiment["config"]["metrics"][1]
            if experiment["results"]["strategy_a"][secondary_metric] > experiment["results"]["strategy_b"][secondary_metric]:
                experiment["results"]["winner"] = "strategy_a"
            elif experiment["results"]["strategy_b"][secondary_metric] > experiment["results"]["strategy_a"][secondary_metric]:
                experiment["results"]["winner"] = "strategy_b"
            else:
                experiment["results"]["winner"] = "tie"
        else:
            experiment["results"]["winner"] = "tie"
    
    experiment["status"] = "completed"
    pricing_experiments[experiment_id] = experiment
    
    logger.info(f"Evaluated experiment {experiment_id}. Winner: {experiment['results']['winner']}")
    return experiment

@app.post("/pricing/optimize", tags=["Optimization"])
async def multi_objective_optimization(
    retreat_id: str,
    objectives: List[Dict[str, Any]] = Body(..., description="List of objectives with weights"),
    constraints: Dict[str, Any] = Body(..., description="Optimization constraints"),
    current_state: Dict[str, Any] = Body(..., description="Current retreat state")
):
    """
    Performs multi-objective optimization for retreat pricing and other parameters.
    Each objective should have a 'type', 'weight', and other required parameters.
    
    Example objectives:
    - {"type": "revenue", "weight": 0.7}
    - {"type": "occupancy", "weight": 0.3}
    
    Example constraints:
    - {"min_price": 100, "max_price": 300, "min_occupancy": 0.5}
    
    Example current_state:
    - {"base_price": 150, "occupancy": 0.4, "days_until_start": 45}
    """
    try:
        # Calculate the optimal price
        # In a real implementation, this would be a complex optimization algorithm
        
        # Simplified implementation for demonstration
        weights = {obj["type"]: obj["weight"] for obj in objectives}
        total_weight = sum(weights.values())
        normalized_weights = {k: v/total_weight for k, v in weights.items()}
        
        base_price = current_state.get("base_price", 150)
        min_price = constraints.get("min_price", base_price * 0.7)
        max_price = constraints.get("max_price", base_price * 1.3)
        
        # Create a simple optimization function based on weighted objectives
        def objective_function(price):
            price = price[0]  # Unpack from array
            
            # Revenue objective (higher prices increase revenue if demand is sustained)
            revenue_score = price / max_price
            
            # Occupancy objective (lower prices increase occupancy)
            demand_factor = 1 - 0.2 * (price - min_price) / (max_price - min_price)
            occupancy_score = demand_factor
            
            # Combine objectives with weights
            weighted_score = (
                normalized_weights.get("revenue", 0) * revenue_score + 
                normalized_weights.get("occupancy", 0) * occupancy_score
            )
            
            # We want to maximize, but minimize functions are more common in optimizers
            return -weighted_score
        
        # Run optimization
        result = minimize(
            objective_function, 
            [base_price], 
            bounds=[(min_price, max_price)], 
            method='L-BFGS-B'
        )
        
        optimal_price = float(result.x[0])
        
        # Calculate expected outcomes
        demand_factor = 1 - 0.2 * (optimal_price - min_price) / (max_price - min_price)
        current_occupancy = current_state.get("occupancy", 0.5)
        expected_occupancy = current_occupancy + (1 - current_occupancy) * demand_factor
        expected_revenue = optimal_price * expected_occupancy
        
        logger.info(f"Calculated optimal price of {optimal_price} for retreat {retreat_id} using multi-objective optimization")
        return {
            "retreat_id": retreat_id,
            "optimal_price": round(optimal_price, 2),
            "expected_outcomes": {
                "revenue": round(expected_revenue, 2),
                "occupancy": round(expected_occupancy, 4),
            },
            "optimization_details": {
                "objective_weights": normalized_weights,
                "convergence": bool(result.success),
                "iterations": result.nit
            }
        }
        
    except Exception as e:
        logger.error(f"Error in multi-objective optimization: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in optimization: {str(e)}")

# If running directly
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8007, reload=False) 