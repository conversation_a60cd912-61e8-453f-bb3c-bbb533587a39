FROM python:3.9-slim

WORKDIR /app

# Install required system packages for PuLP
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY ./requirements.txt /app/requirements.txt

# Install dependencies
RUN pip install --no-cache-dir --upgrade -r /app/requirements.txt

# Copy application code
COPY . /app/

# Expose API port
EXPOSE 8007

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8007"] 