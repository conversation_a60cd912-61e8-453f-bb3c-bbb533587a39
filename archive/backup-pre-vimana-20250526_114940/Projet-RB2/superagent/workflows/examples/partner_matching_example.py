import asyncio
import logging
import json
import sys
import os
from datetime import datetime

# A<PERSON><PERSON> le répertoire parent au chemin pour pouvoir importer les modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Configurer le logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("partner_matching_example")

from graph.graph_manager import GraphManager
from workflows.templates.workflow_templates import WorkflowTemplates

async def run_partner_matching_workflow():
    """
    Exécute un exemple de workflow de matching de partenaires.
    """
    logger.info("Starting partner matching workflow example")
    
    # Créer le gestionnaire de graphes
    graph_manager = GraphManager()
    
    # Obtenir le template de workflow pour le matching de partenaires
    workflow_config = WorkflowTemplates.get_partner_matching_workflow()
    
    # <PERSON><PERSON>er le graphe
    graph_id = await graph_manager.create_graph(workflow_config)
    logger.info(f"Created graph with ID: {graph_id}")
    
    # Préparer les données d'entrée pour le workflow
    input_data = {
        "user_profile": {
            "id": "user123",
            "name": "<PERSON> Doe",
            "location": "Paris, France",
            "expertise": ["yoga", "meditation"],
            "experience_years": 5,
            "languages": ["English", "French"],
            "availability": ["weekends", "evenings"],
            "preferred_retreat_types": ["wellness", "yoga"],
            "preferred_locations": ["Bali", "Thailand", "France"],
            "preferred_duration": "1-2 weeks",
            "subscription_level": "Premium Partner"
        },
        "matching_criteria": {
            "location_importance": 0.8,
            "expertise_importance": 0.9,
            "experience_importance": 0.7,
            "language_importance": 0.6,
            "availability_importance": 0.8
        },
        "max_matches": 5
    }
    
    # Exécuter le workflow
    workflow_id = await graph_manager.execute_workflow(graph_id, input_data)
    logger.info(f"Executing workflow with ID: {workflow_id}")
    
    # Attendre que le workflow soit terminé
    while True:
        # Récupérer l'état du workflow
        workflow_state = await graph_manager.get_workflow_state(workflow_id)
        
        if workflow_state.status in ["completed", "failed"]:
            logger.info(f"Workflow {workflow_id} finished with status: {workflow_state.status}")
            break
            
        logger.info(f"Workflow {workflow_id} is still running, current node: {workflow_state.current_node}")
        await asyncio.sleep(1)
        
    # Afficher les résultats du workflow
    if workflow_state.status == "completed":
        logger.info("Workflow completed successfully")
        logger.info(f"Results: {json.dumps(workflow_state.outputs, indent=2)}")
    else:
        logger.error(f"Workflow failed: {workflow_state.metadata.get('error', 'Unknown error')}")
        
    return workflow_state

if __name__ == "__main__":
    # Exécuter le workflow de manière asynchrone
    asyncio.run(run_partner_matching_workflow())
