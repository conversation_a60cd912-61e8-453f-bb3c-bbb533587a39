import asyncio
import logging
import json
import sys
import os
from datetime import datetime

# Ajou<PERSON> le répertoire parent au chemin pour pouvoir importer les modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Configurer le logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("retreat_search_example")

from graph.graph_manager import GraphManager
from workflows.templates.workflow_templates import WorkflowTemplates

async def run_retreat_search_workflow():
    """
    Exécute un exemple de workflow de recherche de retraites.
    """
    logger.info("Starting retreat search workflow example")
    
    # Créer le gestionnaire de graphes
    graph_manager = GraphManager()
    
    # Obtenir le template de workflow pour la recherche de retraites
    workflow_config = WorkflowTemplates.get_retreat_search_workflow()
    
    # C<PERSON>er le graphe
    graph_id = await graph_manager.create_graph(workflow_config)
    logger.info(f"Created graph with ID: {graph_id}")
    
    # Préparer les données d'entrée pour le workflow
    input_data = {
        "search_query": "yoga retreats in Bali",
        "filters": {
            "duration": "1 week",
            "price_range": {"min": 500, "max": 2000},
            "activities": ["yoga", "meditation", "wellness"],
            "accommodation_type": "resort"
        },
        "sort_by": "price",
        "max_results": 10
    }
    
    # Exécuter le workflow
    workflow_id = await graph_manager.execute_workflow(graph_id, input_data)
    logger.info(f"Executing workflow with ID: {workflow_id}")
    
    # Attendre que le workflow soit terminé
    while True:
        # Récupérer l'état du workflow
        workflow_state = await graph_manager.get_workflow_state(workflow_id)
        
        if workflow_state.status in ["completed", "failed"]:
            logger.info(f"Workflow {workflow_id} finished with status: {workflow_state.status}")
            break
            
        logger.info(f"Workflow {workflow_id} is still running, current node: {workflow_state.current_node}")
        await asyncio.sleep(1)
        
    # Afficher les résultats du workflow
    if workflow_state.status == "completed":
        logger.info("Workflow completed successfully")
        logger.info(f"Results: {json.dumps(workflow_state.outputs, indent=2)}")
    else:
        logger.error(f"Workflow failed: {workflow_state.metadata.get('error', 'Unknown error')}")
        
    return workflow_state

if __name__ == "__main__":
    # Exécuter le workflow de manière asynchrone
    asyncio.run(run_retreat_search_workflow())
