from typing import Dict, Any, List
from graph.node_types import NodeType, NodeConfig, EdgeConfig, GraphConfig

class WorkflowTemplates:
    """
    Templates de workflow pour les tâches courantes de la plateforme Retreat And Be.
    """
    
    @staticmethod
    def get_retreat_search_workflow() -> GraphConfig:
        """
        Crée un template de workflow pour la recherche de retraites.
        
        Returns:
            La configuration du graphe pour la recherche de retraites
        """
        # Nœuds du workflow
        nodes = [
            NodeConfig(
                type=NodeType.COORDINATOR,
                name="coordinator",
                description="Coordonne le workflow de recherche de retraites",
                config={}
            ),
            NodeConfig(
                type=NodeType.PLANNER,
                name="planner",
                description="Planifie la recherche de retraites",
                config={
                    "action": "create_plan",
                    "template_key": "research"
                }
            ),
            NodeConfig(
                type=NodeType.SUPERVISOR,
                name="supervisor",
                description="Supervise la recherche de retraites",
                config={}
            ),
            NodeConfig(
                type=NodeType.RESEARCHER,
                name="researcher",
                description="Effectue la recherche de retraites",
                config={
                    "action": "search",
                    "sources": ["web", "database"]
                }
            ),
            NodeConfig(
                type=NodeType.BROWSER,
                name="browser",
                description="Navigue sur les sites de retraites",
                config={
                    "action": "visit_url"
                }
            ),
            NodeConfig(
                type=NodeType.REPORTER,
                name="reporter",
                description="Génère un rapport sur les retraites trouvées",
                config={
                    "action": "generate_report",
                    "report_type": "summary",
                    "format": "markdown"
                }
            )
        ]
        
        # Arêtes du workflow
        edges = [
            EdgeConfig(
                source="coordinator",
                target="planner"
            ),
            EdgeConfig(
                source="planner",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="researcher"
            ),
            EdgeConfig(
                source="researcher",
                target="browser"
            ),
            EdgeConfig(
                source="browser",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="reporter"
            ),
            EdgeConfig(
                source="reporter",
                target="coordinator"
            )
        ]
        
        # Configuration du graphe
        return GraphConfig(
            name="retreat_search_workflow",
            description="Workflow pour la recherche de retraites",
            nodes=nodes,
            edges=edges,
            entry_point="coordinator"
        )
        
    @staticmethod
    def get_partner_matching_workflow() -> GraphConfig:
        """
        Crée un template de workflow pour le matching de partenaires.
        
        Returns:
            La configuration du graphe pour le matching de partenaires
        """
        # Nœuds du workflow
        nodes = [
            NodeConfig(
                type=NodeType.COORDINATOR,
                name="coordinator",
                description="Coordonne le workflow de matching de partenaires",
                config={}
            ),
            NodeConfig(
                type=NodeType.PLANNER,
                name="planner",
                description="Planifie le matching de partenaires",
                config={
                    "action": "create_plan",
                    "template_key": "default"
                }
            ),
            NodeConfig(
                type=NodeType.SUPERVISOR,
                name="supervisor",
                description="Supervise le matching de partenaires",
                config={}
            ),
            NodeConfig(
                type=NodeType.RESEARCHER,
                name="researcher",
                description="Analyse les profils des partenaires",
                config={
                    "action": "analyze_data",
                    "analysis_type": "general"
                }
            ),
            NodeConfig(
                type=NodeType.CODER,
                name="coder",
                description="Génère des algorithmes de matching",
                config={
                    "action": "generate_code",
                    "language": "python"
                }
            ),
            NodeConfig(
                type=NodeType.REPORTER,
                name="reporter",
                description="Génère un rapport sur les matchings trouvés",
                config={
                    "action": "generate_report",
                    "report_type": "detailed",
                    "format": "markdown"
                }
            )
        ]
        
        # Arêtes du workflow
        edges = [
            EdgeConfig(
                source="coordinator",
                target="planner"
            ),
            EdgeConfig(
                source="planner",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="researcher"
            ),
            EdgeConfig(
                source="researcher",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="coder"
            ),
            EdgeConfig(
                source="coder",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="reporter"
            ),
            EdgeConfig(
                source="reporter",
                target="coordinator"
            )
        ]
        
        # Configuration du graphe
        return GraphConfig(
            name="partner_matching_workflow",
            description="Workflow pour le matching de partenaires",
            nodes=nodes,
            edges=edges,
            entry_point="coordinator"
        )
        
    @staticmethod
    def get_content_generation_workflow() -> GraphConfig:
        """
        Crée un template de workflow pour la génération de contenu.
        
        Returns:
            La configuration du graphe pour la génération de contenu
        """
        # Nœuds du workflow
        nodes = [
            NodeConfig(
                type=NodeType.COORDINATOR,
                name="coordinator",
                description="Coordonne le workflow de génération de contenu",
                config={}
            ),
            NodeConfig(
                type=NodeType.PLANNER,
                name="planner",
                description="Planifie la génération de contenu",
                config={
                    "action": "create_plan",
                    "template_key": "development"
                }
            ),
            NodeConfig(
                type=NodeType.SUPERVISOR,
                name="supervisor",
                description="Supervise la génération de contenu",
                config={}
            ),
            NodeConfig(
                type=NodeType.RESEARCHER,
                name="researcher",
                description="Recherche des informations pour le contenu",
                config={
                    "action": "search",
                    "sources": ["web", "database"]
                }
            ),
            NodeConfig(
                type=NodeType.CODER,
                name="coder",
                description="Génère du code pour le contenu interactif",
                config={
                    "action": "generate_code",
                    "language": "javascript"
                }
            ),
            NodeConfig(
                type=NodeType.REPORTER,
                name="reporter",
                description="Génère le contenu final",
                config={
                    "action": "generate_report",
                    "report_type": "detailed",
                    "format": "html"
                }
            )
        ]
        
        # Arêtes du workflow
        edges = [
            EdgeConfig(
                source="coordinator",
                target="planner"
            ),
            EdgeConfig(
                source="planner",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="researcher"
            ),
            EdgeConfig(
                source="researcher",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="coder"
            ),
            EdgeConfig(
                source="coder",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="reporter"
            ),
            EdgeConfig(
                source="reporter",
                target="coordinator"
            )
        ]
        
        # Configuration du graphe
        return GraphConfig(
            name="content_generation_workflow",
            description="Workflow pour la génération de contenu",
            nodes=nodes,
            edges=edges,
            entry_point="coordinator"
        )
        
    @staticmethod
    def get_customer_support_workflow() -> GraphConfig:
        """
        Crée un template de workflow pour le support client.
        
        Returns:
            La configuration du graphe pour le support client
        """
        # Nœuds du workflow
        nodes = [
            NodeConfig(
                type=NodeType.COORDINATOR,
                name="coordinator",
                description="Coordonne le workflow de support client",
                config={}
            ),
            NodeConfig(
                type=NodeType.PLANNER,
                name="planner",
                description="Planifie la réponse au client",
                config={
                    "action": "create_plan",
                    "template_key": "default"
                }
            ),
            NodeConfig(
                type=NodeType.SUPERVISOR,
                name="supervisor",
                description="Supervise la réponse au client",
                config={}
            ),
            NodeConfig(
                type=NodeType.RESEARCHER,
                name="researcher",
                description="Recherche des informations pour répondre au client",
                config={
                    "action": "search",
                    "sources": ["database"]
                }
            ),
            NodeConfig(
                type=NodeType.REPORTER,
                name="reporter",
                description="Génère la réponse au client",
                config={
                    "action": "generate_report",
                    "report_type": "summary",
                    "format": "markdown"
                }
            )
        ]
        
        # Arêtes du workflow
        edges = [
            EdgeConfig(
                source="coordinator",
                target="planner"
            ),
            EdgeConfig(
                source="planner",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="researcher"
            ),
            EdgeConfig(
                source="researcher",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="reporter"
            ),
            EdgeConfig(
                source="reporter",
                target="coordinator"
            )
        ]
        
        # Configuration du graphe
        return GraphConfig(
            name="customer_support_workflow",
            description="Workflow pour le support client",
            nodes=nodes,
            edges=edges,
            entry_point="coordinator"
        )
        
    @staticmethod
    def get_retreat_planning_workflow() -> GraphConfig:
        """
        Crée un template de workflow pour la planification de retraites.
        
        Returns:
            La configuration du graphe pour la planification de retraites
        """
        # Nœuds du workflow
        nodes = [
            NodeConfig(
                type=NodeType.COORDINATOR,
                name="coordinator",
                description="Coordonne le workflow de planification de retraites",
                config={}
            ),
            NodeConfig(
                type=NodeType.PLANNER,
                name="planner",
                description="Planifie la retraite",
                config={
                    "action": "create_plan",
                    "template_key": "development"
                }
            ),
            NodeConfig(
                type=NodeType.SUPERVISOR,
                name="supervisor",
                description="Supervise la planification de la retraite",
                config={}
            ),
            NodeConfig(
                type=NodeType.RESEARCHER,
                name="researcher",
                description="Recherche des informations pour la retraite",
                config={
                    "action": "search",
                    "sources": ["web", "database"]
                }
            ),
            NodeConfig(
                type=NodeType.BROWSER,
                name="browser",
                description="Navigue sur les sites pour la planification",
                config={
                    "action": "visit_url"
                }
            ),
            NodeConfig(
                type=NodeType.REPORTER,
                name="reporter",
                description="Génère le plan de retraite",
                config={
                    "action": "generate_report",
                    "report_type": "detailed",
                    "format": "markdown"
                }
            )
        ]
        
        # Arêtes du workflow
        edges = [
            EdgeConfig(
                source="coordinator",
                target="planner"
            ),
            EdgeConfig(
                source="planner",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="researcher"
            ),
            EdgeConfig(
                source="researcher",
                target="browser"
            ),
            EdgeConfig(
                source="browser",
                target="supervisor"
            ),
            EdgeConfig(
                source="supervisor",
                target="reporter"
            ),
            EdgeConfig(
                source="reporter",
                target="coordinator"
            )
        ]
        
        # Configuration du graphe
        return GraphConfig(
            name="retreat_planning_workflow",
            description="Workflow pour la planification de retraites",
            nodes=nodes,
            edges=edges,
            entry_point="coordinator"
        )
