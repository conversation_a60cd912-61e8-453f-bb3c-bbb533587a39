"""
Module de communication avec les autres microservices.
"""
import os
import logging
import requests
from typing import Dict, Any, Optional, Union

# Configuration du logging
logger = logging.getLogger(__name__)

# URLs des services
AGENT_RB_SERVICE_URL = os.environ.get('AGENT_RB_SERVICE_URL', 'http://localhost:5000')
AGENT_IA_SERVICE_URL = os.environ.get('AGENT_IA_SERVICE_URL', 'http://localhost:5002')

class CommunicationService:
    """Service pour communiquer avec les autres microservices."""
    
    @staticmethod
    def call_agent_rb(endpoint: str, method: str = 'GET', data: Optional[Dict[str, Any]] = None, 
                     params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Appelle un endpoint du service Agent-RB.
        
        Args:
            endpoint: Endpoint à appeler (sans le slash initial)
            method: Méthode HTTP (GET, POST, PUT, DELETE)
            data: Données à envoyer dans le corps de la requête
            params: Paramètres de requête
            
        Returns:
            Réponse du service
            
        Raises:
            requests.exceptions.RequestException: Si la requête échoue
        """
        url = f"{AGENT_RB_SERVICE_URL}/{endpoint}"
        logger.info(f"Calling Agent-RB: {method} {url}")
        
        try:
            response = requests.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error calling Agent-RB: {str(e)}")
            raise
    
    @staticmethod
    def call_agent_ia(endpoint: str, method: str = 'GET', data: Optional[Dict[str, Any]] = None, 
                     params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Appelle un endpoint du service Agent IA.
        
        Args:
            endpoint: Endpoint à appeler (sans le slash initial)
            method: Méthode HTTP (GET, POST, PUT, DELETE)
            data: Données à envoyer dans le corps de la requête
            params: Paramètres de requête
            
        Returns:
            Réponse du service
            
        Raises:
            requests.exceptions.RequestException: Si la requête échoue
        """
        url = f"{AGENT_IA_SERVICE_URL}/{endpoint}"
        logger.info(f"Calling Agent IA: {method} {url}")
        
        try:
            response = requests.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error calling Agent IA: {str(e)}")
            raise
    
    # Méthodes spécifiques pour Agent-RB
    
    @classmethod
    def get_retreats(cls, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Récupère la liste des retraites depuis Agent-RB.
        
        Args:
            filters: Filtres à appliquer
            
        Returns:
            Liste des retraites
        """
        return cls.call_agent_rb("api/retreats", method="GET", params=filters)
    
    @classmethod
    def get_retreat(cls, retreat_id: str) -> Dict[str, Any]:
        """
        Récupère les détails d'une retraite depuis Agent-RB.
        
        Args:
            retreat_id: ID de la retraite
            
        Returns:
            Détails de la retraite
        """
        return cls.call_agent_rb(f"api/retreats/{retreat_id}", method="GET")
    
    @classmethod
    def get_partners(cls, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Récupère la liste des partenaires depuis Agent-RB.
        
        Args:
            filters: Filtres à appliquer
            
        Returns:
            Liste des partenaires
        """
        return cls.call_agent_rb("api/partners", method="GET", params=filters)
    
    # Méthodes spécifiques pour Agent IA
    
    @classmethod
    def analyze_text(cls, text: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyse un texte avec Agent IA.
        
        Args:
            text: Texte à analyser
            options: Options d'analyse
            
        Returns:
            Résultats de l'analyse
        """
        data = {"text": text}
        if options:
            data["options"] = options
        return cls.call_agent_ia("analyze", method="POST", data=data)
    
    @classmethod
    def generate_response(cls, prompt: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Génère une réponse avec Agent IA.
        
        Args:
            prompt: Prompt pour la génération
            options: Options de génération
            
        Returns:
            Réponse générée
        """
        data = {"prompt": prompt}
        if options:
            data["options"] = options
        return cls.call_agent_ia("generate", method="POST", data=data)
    
    @classmethod
    def get_recommendations(cls, preferences: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Récupère des recommandations personnalisées avec Agent IA.
        
        Args:
            preferences: Préférences de l'utilisateur
            options: Options de recommandation
            
        Returns:
            Recommandations
        """
        data = {"preferences": preferences}
        if options:
            data["options"] = options
        return cls.call_agent_ia("recommendations", method="POST", data=data)
    
    # Méthodes de vérification de santé
    
    @classmethod
    def check_agent_rb_health(cls) -> bool:
        """
        Vérifie si le service Agent-RB est en bonne santé.
        
        Returns:
            True si le service est en bonne santé, False sinon
        """
        try:
            response = cls.call_agent_rb("health", method="GET")
            return response.get("status") == "healthy"
        except requests.exceptions.RequestException:
            return False
    
    @classmethod
    def check_agent_ia_health(cls) -> bool:
        """
        Vérifie si le service Agent IA est en bonne santé.
        
        Returns:
            True si le service est en bonne santé, False sinon
        """
        try:
            response = cls.call_agent_ia("health", method="GET")
            return response.get("status") == "healthy"
        except requests.exceptions.RequestException:
            return False
