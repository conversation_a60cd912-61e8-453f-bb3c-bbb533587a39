R&B new/
├── agents/
│   ├── __init__.py
│   ├── base/
│   │   ├── __init__.py
│   │   ├── agent_base.py          # Classe de base pour tous les agents
│   │   └── agent_security.py      # Sécurité commune aux agents
│   ├── nodes/
│   │   ├── __init__.py
│   │   ├── node_base.py
│   │   └── node_manager.py
│   ├── browser_agent/
│   │   ├── __init__.py
│   │   ├── browser_agent.py
│   │   └── browser_security.py
│   ├── coder_agent/
│   │   ├── __init__.py
│   │   ├── coder_agent.py
│   │   └── code_validator.py
│   ├── coordinator/
│   │   ├── __init__.py
│   │   ├── coordinator.py
│   │   └── orchestrator.py
│   ├── reporter_agent/
│   │   ├── __init__.py
│   │   ├── reporter_agent.py
│   │   └── report_generator.py
│   └── research_agent/
│       ├── __init__.py
│       ├── research_agent.py
│       └── data_validator.py

├── components/
│   ├── common/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   └── Loading.tsx
│   ├── agents/
│   │   ├── AgentCard.tsx
│   │   └── AgentList.tsx
│   └── workflow/
│       ├── WorkflowBuilder.tsx
│       └── WorkflowVisualizer.tsx

├── config/
│   ├── __init__.py
│   ├── agent_config.py
│   ├── security_config.py
│   ├── api_config.py
│   └── env/
│       ├── development.env
│       ├── production.env
│       └── test.env

├── graph/
│   ├── __init__.py
│   ├── graph_manager.py
│   ├── node_types.py
│   └── validators/
│       ├── __init__.py
│       └── graph_validator.py

├── llms/
│   ├── __init__.py
│   ├── base_llm.py
│   ├── gpt4_handler.py
│   ├── claude_handler.py
│   └── security/
│       ├── __init__.py
│       └── prompt_sanitizer.py

├── pages/
│   ├── _app.tsx
│   ├── _document.tsx
│   ├── index.tsx
│   ├── api/
│   │   └── agents/
│   │       ├── [...slug].ts
│   │       └── index.ts
│   └── dashboard/
│       ├── index.tsx
│       └── workflows.tsx

├── prompts/
│   ├── __init__.py
│   ├── templates/
│   │   ├── browser_prompts.py
│   │   ├── coder_prompts.py
│   │   └── research_prompts.py
│   └── validators/
│       └── prompt_validator.py

├── services/
│   ├── __init__.py
│   ├── auth/
│   │   ├── __init__.py
│   │   ├── auth_service.py
│   │   └── jwt_handler.py
│   ├── database/
│   │   ├── __init__.py
│   │   ├── models.py
│   │   └── db_manager.py
│   └── api/
│       ├── __init__.py
│       └── endpoints.py

├── tools/
│   ├── __init__.py
│   ├── browser/
│   │   ├── __init__.py
│   │   └── browser_tools.py
│   ├── code/
│   │   ├── __init__.py
│   │   └── code_tools.py
│   └── research/
│       ├── __init__.py
│       └── research_tools.py

├── utils/
│   ├── __init__.py
│   ├── security/
│   │   ├── __init__.py
│   │   ├── encryption.py
│   │   └── validation.py
│   ├── logging/
│   │   ├── __init__.py
│   │   └── logger.py
│   └── helpers/
│       ├── __init__.py
│       └── common.py

├── workflow/
│   ├── __init__.py
│   ├── engine/
│   │   ├── __init__.py
│   │   └── workflow_engine.py
│   ├── models/
│   │   ├── __init__.py
│   │   └── workflow_model.py
│   └── validators/
│       ├── __init__.py
│       └── workflow_validator.py

├── tests/
│   ├── __init__.py
│   ├── unit/
│   │   └── test_agents.py
│   ├── integration/
│   │   └── test_workflow.py
│   └── security/
│       └── test_security.py

├── client.py
├── server.py
├── requirements.txt
├── setup.py
├── README.md
└── docker-compose.yml