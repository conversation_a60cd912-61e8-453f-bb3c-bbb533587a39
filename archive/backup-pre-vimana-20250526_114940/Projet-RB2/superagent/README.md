# Retreat And Be - AI Workflow Platform

Ce projet implémente une plateforme d'IA multi-agents pour la plateforme Retreat And Be, permettant de rechercher des retraites, de matcher des partenaires, de générer du contenu, d'interagir avec les utilisateurs via un chatbot intelligent, et plus encore.

## Vision

L'IA de Retreat And Be vise à personnaliser l'expérience utilisateur, optimiser le matching entre offres et demandes, automatiser les tâches à faible valeur ajoutée, analyser les données pour améliorer continuellement les services, et faciliter la création de contenu et la communication.

## Architecture

Le projet est basé sur une architecture multi-agents avec les composants suivants :

### Moteurs d'IA

- **AI Engine** : Moteur principal d'intelligence artificielle
  - **Chatbot** : Assistant conversationnel avec détection d'intention
  - **Content Generator** : Génération de contenu pour les descriptions et articles
  - **Analytics Engine** : Analyse de données et génération d'insights
  - **Recommender Engine** : Système de recommandation personnalisée
  - **Optimizer Engine** : Optimisation des processus et des ressources

### Agents spécialisés

- **Coder Agent** : Génération et analyse de code
- **Reporter Agent** : Génération de rapports
- **Research Agent** : Recherche et analyse d'informations
- **Planner Agent** : Planification et décomposition de tâches
- **Supervisor Agent** : Supervision et coordination des autres agents
- **Browser Agent** : Navigation web et extraction d'informations
- **Coordinator** : Orchestration des autres agents

### Système de workflow LangGraph

- **GraphManager** : Création, configuration et exécution de graphes de workflow
- **NodeTypes** : Définition des types de nœuds et leurs configurations
- **GraphValidator** : Validation des graphes de workflow

### Outils spécifiques aux nœuds

- **CodeGenerationTool** : Génération de code
- **CodeAnalysisTool** : Analyse de code
- **ReportGenerationTool** : Génération de rapports
- **DataSummarizationTool** : Résumé de données
- **WebSearchTool** : Recherche sur le web
- **DataAnalysisTool** : Analyse de données

### API REST

- **WorkflowService** : Service pour la gestion des workflows
- **WorkflowRoutes** : Routes pour l'API REST
- **ChatbotService** : Service pour le chatbot conversationnel
- **ContentService** : Service pour la génération de contenu

### Interface utilisateur

- **React** : Interface utilisateur
- **Material-UI** : Composants d'interface utilisateur

## Structure du projet

```
superagent/
├── agents/                # Agents intelligents
│   ├── base/              # Classes de base pour les agents
│   ├── browser_agent/     # Agent pour la navigation web
│   ├── coder_agent/       # Agent pour la génération de code
│   ├── coordinator/       # Agent coordinateur
│   ├── nodes/             # Nœuds pour le graphe de workflow
│   ├── planner_agent/     # Agent de planification
│   ├── reporter_agent/    # Agent de génération de rapports
│   ├── research_agent/    # Agent de recherche
│   └── supervisor_agent/  # Agent superviseur
├── ai_engine/             # Moteur d'IA
│   ├── analytics_engine/  # Analyse de données
│   ├── chatbot/           # Assistant conversationnel
│   ├── content_generator/ # Génération de contenu
│   ├── optimizer/         # Optimisation des processus
│   └── recommender/       # Système de recommandation
├── api/                   # API REST
│   ├── controllers/       # Contrôleurs de l'API
│   ├── models/            # Modèles de données
│   ├── routes/            # Routes de l'API
│   └── services/          # Services métier
├── docs/                  # Documentation
├── frontend/              # Interface utilisateur
├── graph/                 # Système de workflow LangGraph
│   ├── manager/           # Gestionnaire de graphes
│   ├── nodes/             # Types de nœuds
│   └── validators/        # Validateurs de graphes
├── scripts/               # Scripts utilitaires
├── tests/                 # Tests
└── utils/                 # Utilitaires
```

## Workflows disponibles

- **Retreat Search** : Recherche de retraites selon des critères
- **Partner Matching** : Matching de partenaires pour la création de retraites
- **Content Generation** : Génération de contenu pour les descriptions de retraites
- **Customer Support** : Support client pour les questions sur les retraites
- **Retreat Planning** : Planification de retraites
- **Client Assistance** : Assistance client via le chatbot

## Plateformes

L'IA est conçue pour s'intégrer à toutes les plateformes de Retreat And Be:
- Web (responsive)
- Applications mobiles (iOS et Android)
- Extension Chrome
- Système back-end (APIs et microservices)

## Installation

### Prérequis

- Python 3.8+
- Node.js 14+
- npm 6+

### Installation des dépendances

```bash
# Installation des dépendances Python
pip install -r requirements.txt

# Installation des dépendances Node.js
cd frontend
npm install
cd ..
```

### Configuration

Copiez le fichier `.env.example` vers `.env` et configurez les variables d'environnement :

```bash
cp .env.example .env
```

Variables importantes à configurer :
- `DEEPSEEK_API_KEY` : Clé API pour le service Deepseek (chatbot)
- `DB_HOST`, `DB_PORT`, `DB_NAME`, etc. : Configuration de la base de données
- `API_HOST`, `API_PORT` : Configuration de l'API

## Démarrage

Pour démarrer tous les services, exécutez le script `start.sh` :

```bash
./start.sh
```

Pour démarrer uniquement le service chatbot :

```bash
./scripts/start_chatbot.sh
```

Ou démarrez les services séparément :

```bash
# Démarrer l'API principale
python -m api.main

# Démarrer le service chatbot
cd ai_engine/chatbot
uvicorn main:app --host 0.0.0.0 --port 8001 --reload

# Démarrer l'interface utilisateur
cd frontend
npm start
```

L'API principale sera disponible à l'adresse `http://localhost:8000`, le service chatbot à l'adresse `http://localhost:8001` et l'interface utilisateur à l'adresse `http://localhost:3000`.

## Intégration avec Front-Audrey-V1-Main-main

Le chatbot est intégré avec le frontend Front-Audrey-V1-Main-main. Pour configurer cette intégration :

1. Dans le projet Front-Audrey-V1-Main-main, configurez la variable d'environnement `REACT_APP_SUPERAGENT_URL` pour pointer vers le service chatbot :

```
REACT_APP_SUPERAGENT_URL=http://localhost:8001
```

2. Le chatbot apparaîtra sous forme d'un bouton flottant dans le coin inférieur droit de l'interface.

Pour plus de détails sur l'intégration, consultez le fichier `Front-Audrey-V1-Main-main/docs/CHATBOT-INTEGRATION.md`.

## Utilisation

### Workflows

1. Accédez à l'interface utilisateur à l'adresse `http://localhost:3000`
2. Sélectionnez un template de workflow
3. Configurez les données d'entrée pour le workflow
4. Créez le workflow
5. Consultez les résultats du workflow

### Chatbot

Le chatbot peut être utilisé de plusieurs façons :

1. **Via l'API REST** : Envoyez des requêtes POST à `http://localhost:8001/chat`
2. **Via l'interface utilisateur** : Utilisez le bouton de chat dans l'interface
3. **Via l'intégration avec Front-Audrey-V1-Main-main** : Utilisez le bouton flottant dans le coin inférieur droit

Exemple de requête API :

```bash
curl -X POST "http://localhost:8001/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "Je cherche une retraite de yoga en France"
      }
    ],
    "user_id": "user123"
  }'
```

## Documentation de l'API

La documentation de l'API principale est disponible à l'adresse `http://localhost:8000/docs`.
La documentation de l'API chatbot est disponible à l'adresse `http://localhost:8001/docs`.

## Exemples

Des exemples d'utilisation des workflows sont disponibles dans le répertoire `workflows/examples`.
Des exemples d'utilisation du chatbot sont disponibles dans le répertoire `ai_engine/chatbot/examples`.

## Tests

Pour exécuter les tests, utilisez les commandes suivantes :

```bash
# Tests des agents
python -m tests.test_agents

# Tests du système de workflow
python -m tests.test_graph

# Tests du chatbot
python -m tests.test_chatbot
```

## Licence

Propriétaire - Tous droits réservés © Retreat And Be
