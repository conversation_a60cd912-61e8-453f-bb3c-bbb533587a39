from typing import Dict, Any
from pydantic import BaseSettings

class SecurityConfig(BaseSettings):
    # API Security
    API_KEY_HEADER: str = "X-API-Key"
    JWT_SECRET: str = "your-secret-key"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Agent Security
    AGENT_COMMUNICATION_ENCRYPTION: bool = True
    AGENT_AUTH_REQUIRED: bool = True
    MAX_CONCURRENT_AGENTS: int = 10
    
    # LLM Security
    PROMPT_VALIDATION_ENABLED: bool = True
    MAX_TOKENS_PER_REQUEST: int = 2000
    SENSITIVE_KEYWORDS: list = ["password", "secret", "key"]
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60  # seconds
    
    class Config:
        env_file = "config/env/production.env"