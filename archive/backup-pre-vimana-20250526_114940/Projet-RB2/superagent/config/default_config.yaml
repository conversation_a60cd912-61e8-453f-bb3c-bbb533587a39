name: default_workflow
description: Default workflow configuration for multi-agent system
version: 1.0.0
max_steps: 100
logging_level: INFO
enable_tracing: false

agents:
  # Agents génériques du système multi-agent
  - name: coordinator
    enabled: true
    timeout_seconds: 30
    max_retries: 3
    parameters:
      priority: normal

  - name: planner
    enabled: true
    timeout_seconds: 60
    max_retries: 3
    parameters:
      max_plan_steps: 10

  - name: supervisor
    enabled: true
    timeout_seconds: 30
    max_retries: 3
    parameters:
      check_interval: 5

  - name: researcher
    enabled: true
    timeout_seconds: 120
    max_retries: 3
    parameters:
      max_sources: 5
      search_depth: 2

  - name: coder
    enabled: true
    timeout_seconds: 180
    max_retries: 3
    parameters:
      languages:
        - python
        - javascript
      test_code: true

  - name: browser
    enabled: true
    timeout_seconds: 120
    max_retries: 3
    parameters:
      max_pages: 5
      screenshot: true

  - name: reporter
    enabled: true
    timeout_seconds: 60
    max_retries: 3
    parameters:
      format: markdown
      include_code_snippets: true

  # Agents spécifiques au domaine des retraites de bien-être
  - name: retreat_planner
    enabled: true
    timeout_seconds: 90
    max_retries: 3
    parameters:
      default_duration: 7
      default_capacity: 15
      default_location:
        country: France
        region: Provence
      default_activities:
        - yoga
        - meditation
        - hiking

  - name: partner_matcher
    enabled: true
    timeout_seconds: 120
    max_retries: 3
    parameters:
      max_partners_per_type: 5
      min_rating: 4.0
      max_distance_km: 50

  - name: client_assistant
    enabled: true
    timeout_seconds: 60
    max_retries: 3
    parameters:
      max_recommendations: 5
      default_language: fr
      include_availability: true
