"""
Configuration settings for the superagent module.
"""
import os
from typing import Dict, Any, Optional

# Environment settings
ENV = os.getenv("ENVIRONMENT", "development")
DEBUG = ENV == "development"

# API settings
API_VERSION = "v1"
API_PREFIX = f"/api/{API_VERSION}"

# LLM settings
DEFAULT_MODEL = "gpt-4"
TEMPERATURE = 0.7
MAX_TOKENS = 4000

# Agent settings
AGENT_TIMEOUT = 300  # seconds
MAX_ITERATIONS = 10

# Workflow settings
WORKFLOW_TIMEOUT = 600  # seconds
MAX_WORKFLOW_STEPS = 20

# Security settings
API_KEY_HEADER = "X-API-Key"
JWT_SECRET = os.getenv("JWT_SECRET", "development-secret-key")
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION = 86400  # 24 hours in seconds

# Storage settings
STORAGE_TYPE = os.getenv("STORAGE_TYPE", "local")
LOCAL_STORAGE_PATH = os.getenv("LOCAL_STORAGE_PATH", "./storage")

# Database settings
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///superagent.db")

# Logging settings
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Feature flags
FEATURE_FLAGS: Dict[str, bool] = {
    "enable_browser_agent": True,
    "enable_coder_agent": True,
    "enable_advanced_workflows": True,
}

def get_setting(key: str, default: Optional[Any] = None) -> Any:
    """
    Get a setting value by key.
    
    Args:
        key: The setting key
        default: Default value if setting is not found
        
    Returns:
        The setting value or default
    """
    return globals().get(key, default)

def update_setting(key: str, value: Any) -> None:
    """
    Update a setting value.
    
    Args:
        key: The setting key
        value: The new value
    """
    globals()[key] = value
