// Background service worker for Retreat And Be Companion

console.log("Retreat And Be Companion background script loaded.");

// Configuration
const API_BASE_URL = 'https://api.retreatandbe.com';
const ASSISTANT_API_URL = `${API_BASE_URL}/ai_engine/chatbot/chat`;
const RECOMMENDATIONS_API_URL = `${API_BASE_URL}/api/recommendations`;
const STORAGE_KEYS = {
    SAVED_PAGES: 'savedPages',
    RECENT_CONTEXTS: 'recentContexts'
};
const MAX_RECENT_CONTEXTS = 20;
const MAX_SAVED_PAGES = 100;

// Fonction pour récupérer les données stockées
async function getStoredData(key, defaultValue = []) {
    return new Promise((resolve) => {
        chrome.storage.local.get([key], (result) => {
            resolve(result[key] || defaultValue);
        });
    });
}

// Fonction pour stocker des données
async function storeData(key, data) {
    return new Promise((resolve) => {
        chrome.storage.local.set({ [key]: data }, () => {
            resolve();
        });
    });
}

// Listener pour les messages du content script ou du popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Background received message:', message, 'from:', sender);
    
    switch (message.action) {
        case 'savePage':
            handleSavePage(message.pageInfo, sendResponse);
            break;
            
        case 'getSavedPages':
            handleGetSavedPages(sendResponse);
            break;
            
        case 'deletePage':
            handleDeletePage(message.url, sendResponse);
            break;
            
        case 'saveContext':
            handleSaveContext(message.context, sendResponse);
            break;
            
        case 'getRecentContexts':
            handleGetRecentContexts(sendResponse);
            break;
            
        case 'askAssistant':
            handleAskAssistant(message.query, sendResponse);
            break;
            
        case 'getRecommendations':
            handleGetRecommendations(message.context, sendResponse);
            break;
            
        default:
            console.warn('Unknown action:', message.action);
            sendResponse({ status: 'error', error: 'Action inconnue' });
    }
    
    // Indiquer si la réponse sera envoyée de manière asynchrone
    return true;
});

// Gestion de la sauvegarde d'une page
async function handleSavePage(pageInfo, sendResponse) {
    try {
        const savedPages = await getStoredData(STORAGE_KEYS.SAVED_PAGES, []);
        
        // Vérifier si la page existe déjà
        const existingIndex = savedPages.findIndex(p => p.url === pageInfo.url);
        if (existingIndex >= 0) {
            // Mettre à jour l'élément existant
            savedPages[existingIndex] = {
                ...savedPages[existingIndex],
                ...pageInfo,
                savedAt: new Date().toISOString() // Mettre à jour la date
            };
        } else {
            // Ajouter la nouvelle page
            savedPages.push(pageInfo);
            
            // Limiter le nombre de pages sauvegardées
            if (savedPages.length > MAX_SAVED_PAGES) {
                savedPages.shift(); // Supprimer l'élément le plus ancien
            }
        }
        
        // Sauvegarder les données mises à jour
        await storeData(STORAGE_KEYS.SAVED_PAGES, savedPages);
        
        // Envoyer une notification à l'utilisateur
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: 'Page sauvegardée',
            message: `"${pageInfo.title}" a été sauvegardée dans votre profil Retreat And Be.`
        });
        
        sendResponse({ status: 'success', message: 'Page sauvegardée avec succès' });
    } catch (error) {
        console.error('Error saving page:', error);
        sendResponse({ status: 'error', error: error.message });
    }
}

// Gestion de la détection de contexte
async function handleSaveContext(context, sendResponse) {
    try {
        const recentContexts = await getStoredData(STORAGE_KEYS.RECENT_CONTEXTS, []);
        
        // Vérifier si le contexte existe déjà avec la même URL
        const existingIndex = recentContexts.findIndex(c => c.url === context.url);
        if (existingIndex >= 0) {
            // Supprimer l'ancien contexte
            recentContexts.splice(existingIndex, 1);
        }
        
        // Ajouter le nouveau contexte au début
        recentContexts.unshift({
            ...context,
            timestamp: new Date().toISOString()
        });
        
        // Limiter le nombre de contextes récents
        if (recentContexts.length > MAX_RECENT_CONTEXTS) {
            recentContexts.pop(); // Supprimer le dernier élément
        }
        
        // Sauvegarder les données mises à jour
        await storeData(STORAGE_KEYS.RECENT_CONTEXTS, recentContexts);
        
        // Mettre à jour l'icône de l'extension si le score de pertinence est élevé
        if (context.relevanceScore > 0.7) {
            chrome.action.setBadgeText({ text: "!", tabId: context.tabId });
            chrome.action.setBadgeBackgroundColor({ color: "#4CAF50", tabId: context.tabId });
        }
        
        sendResponse({ status: 'success' });
    } catch (error) {
        console.error('Error saving context:', error);
        sendResponse({ status: 'error', error: error.message });
    }
}

// Récupération des contextes récents
async function handleGetRecentContexts(sendResponse) {
    try {
        const recentContexts = await getStoredData(STORAGE_KEYS.RECENT_CONTEXTS, []);
        sendResponse({ status: 'success', contexts: recentContexts });
    } catch (error) {
        console.error('Error getting recent contexts:', error);
        sendResponse({ status: 'error', error: error.message });
    }
}

// Récupération des pages sauvegardées
async function handleGetSavedPages(sendResponse) {
    try {
        const savedPages = await getStoredData(STORAGE_KEYS.SAVED_PAGES, []);
        
        // Trier par date de sauvegarde (plus récent en premier)
        savedPages.sort((a, b) => {
            return new Date(b.savedAt) - new Date(a.savedAt);
        });
        
        sendResponse({ status: 'success', pages: savedPages });
    } catch (error) {
        console.error('Error getting saved pages:', error);
        sendResponse({ status: 'error', error: error.message });
    }
}

// Suppression d'une page sauvegardée
async function handleDeletePage(url, sendResponse) {
    try {
        const savedPages = await getStoredData(STORAGE_KEYS.SAVED_PAGES, []);
        
        // Filtrer la page à supprimer
        const updatedPages = savedPages.filter(page => page.url !== url);
        
        // Sauvegarder les données mises à jour
        await storeData(STORAGE_KEYS.SAVED_PAGES, updatedPages);
        
        sendResponse({ status: 'success', message: 'Page supprimée avec succès' });
    } catch (error) {
        console.error('Error deleting page:', error);
        sendResponse({ status: 'error', error: error.message });
    }
}

// Listener pour l'installation ou la mise à jour de l'extension
chrome.runtime.onInstalled.addListener(() => {
    console.log('Retreat And Be Companion extension installed.');
    
    // Initialiser le stockage local
    chrome.storage.local.get(['recentContexts', 'savedPages'], (result) => {
        if (!result.recentContexts) {
            chrome.storage.local.set({ recentContexts: [] });
        }
        
        if (!result.savedPages) {
            chrome.storage.local.set({ savedPages: [] });
        }
    });
});

// Listener pour les changements d'onglet
chrome.tabs.onActivated.addListener((activeInfo) => {
    // Réinitialiser le badge lorsque l'utilisateur change d'onglet
    chrome.action.setBadgeText({ text: "", tabId: activeInfo.tabId });
});

// Nouvelle fonction pour interagir avec l'assistant
async function handleAskAssistant(query, sendResponse) {
    try {
        // Obtenir le contexte des pages sauvegardées et des contextes récents pour enrichir l'IA
        const savedPages = await getStoredData(STORAGE_KEYS.SAVED_PAGES, []);
        const recentContexts = await getStoredData(STORAGE_KEYS.RECENT_CONTEXTS, []);
        
        // Créer un résumé du contexte utilisateur
        const userContext = {
            savedCategories: savedPages.map(p => p.category),
            recentInterests: recentContexts.map(c => c.mainCategory),
            query: query
        };
        
        console.log('Sending assistant query with context:', userContext);
        
        // En mode hors ligne/démo, simuler une réponse
        if (!navigator.onLine || !ASSISTANT_API_URL.includes('retreatandbe.com')) {
            // Simuler un délai de réponse
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const demoResponses = {
                "retraite": "Je vous recommande d'explorer nos retraites de yoga et méditation dans le sud de la France. Ces séjours offrent un cadre idéal pour se ressourcer et se reconnecter à soi.",
                "yoga": "Le yoga est une pratique millénaire qui associe postures, respiration et méditation. Pour les débutants, je recommande de commencer par le Hatha Yoga ou le Yin Yoga, plus doux et accessibles.",
                "meditation": "La méditation de pleine conscience est idéale pour débutants. Commencez par 5-10 minutes par jour et augmentez progressivement. Nos retraites proposent des initiations guidées par des experts.",
                "bien-être": "Pour améliorer votre bien-être quotidien, pensez à intégrer une routine matinale incluant méditation, étirements doux et un petit-déjeuner équilibré. Nos retraites vous aident à établir ces habitudes.",
                "alimentation": "Une alimentation consciente est essentielle au bien-être. Privilégiez les aliments complets, non transformés et de saison. Lors de nos retraites, nos chefs vous initient à une cuisine saine et savoureuse."
            };
            
            // Trouver une réponse appropriée basée sur des mots-clés
            const keywords = Object.keys(demoResponses);
            const matchedKeyword = keywords.find(k => query.toLowerCase().includes(k));
            
            sendResponse({
                status: 'success',
                answer: matchedKeyword 
                    ? demoResponses[matchedKeyword]
                    : "Je suis votre assistant bien-être. N'hésitez pas à me poser des questions sur les retraites, le yoga, la méditation ou les pratiques de bien-être. Je ferai de mon mieux pour vous guider dans votre parcours."
            });
            return;
        }
        
        // Appel à l'API réelle
        const response = await fetch(ASSISTANT_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: query,
                context: userContext
            })
        });
        
        if (!response.ok) {
            throw new Error(`Erreur API: ${response.status}`);
        }
        
        const data = await response.json();
        
        sendResponse({
            status: 'success',
            answer: data.response || "Je n'ai pas pu trouver une réponse appropriée à votre question."
        });
    } catch (error) {
        console.error('Error asking assistant:', error);
        sendResponse({
            status: 'error',
            answer: "Désolé, je rencontre des difficultés techniques. Veuillez réessayer plus tard."
        });
    }
}

// Nouvelle fonction pour obtenir des recommandations
async function handleGetRecommendations(context, sendResponse) {
    try {
        console.log('Getting recommendations for context:', context);
        
        // En mode hors ligne/démo, simuler des recommandations
        if (!navigator.onLine || !RECOMMENDATIONS_API_URL.includes('retreatandbe.com')) {
            // Simuler un délai de réponse
            await new Promise(resolve => setTimeout(resolve, 1200));
            
            const demoRecommendations = [
                {
                    title: "Retraite de yoga et méditation",
                    description: "Une semaine de détente et reconnexion dans un cadre naturel exceptionnel",
                    location: "Provence, France",
                    score: 95,
                    url: "https://retreatandbe.com/retreats/yoga-meditation-provence"
                },
                {
                    title: "Week-end bien-être & nutrition",
                    description: "Apprenez à cuisiner sain tout en prenant soin de votre corps",
                    location: "Catalogne, Espagne",
                    score: 87,
                    url: "https://retreatandbe.com/retreats/wellness-nutrition-catalunya"
                },
                {
                    title: "Retraite Mindfulness en montagne",
                    description: "Reconnectez-vous à la nature et à vous-même",
                    location: "Alpes, Suisse",
                    score: 82,
                    url: "https://retreatandbe.com/retreats/mindfulness-mountains"
                }
            ];
            
            sendResponse({
                status: 'success',
                recommendations: demoRecommendations
            });
            return;
        }
        
        // Appel à l'API réelle
        const response = await fetch(RECOMMENDATIONS_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                context: {
                    url: context.url,
                    title: context.title,
                    content: context.content
                }
            })
        });
        
        if (!response.ok) {
            throw new Error(`Erreur API: ${response.status}`);
        }
        
        const data = await response.json();
        
        sendResponse({
            status: 'success',
            recommendations: data.recommendations || []
        });
    } catch (error) {
        console.error('Error getting recommendations:', error);
        sendResponse({
            status: 'error',
            error: error.message
        });
    }
}