// Content script for Retreat And Be Companion

console.log("Retreat And Be content script injected into page.");

// --- Context Detection Logic ---
function detectContext() {
    // Catégories et mots-clés associés pour une détection plus précise
    const contextCategories = {
        "yoga": ["yoga", "asana", "posture", "vinyasa", "hatha", "ashtanga", "iyengar", "kundalini"],
        "meditation": ["meditation", "mindfulness", "pleine conscience", "zen", "vipassana", "transcendental"],
        "wellness": ["wellness", "bien-être", "well-being", "santé holistique", "holistic health", "self-care"],
        "retreat": ["retreat", "retraite", "séjour", "stage", "immersion", "getaway"],
        "nature": ["nature", "outdoor", "plein air", "forest", "forêt", "mountain", "montagne", "ocean", "océan"],
        "nutrition": ["nutrition", "detox", "organic", "bio", "alimentation", "vegan", "végétarien", "vegetarian"],
        "spa": ["spa", "massage", "thermal", "hot spring", "source chaude", "sauna", "hammam"]
    };
    
    // Extraire le texte de la page
    const pageText = document.body.innerText.toLowerCase();
    const pageTitle = document.title.toLowerCase();
    const metaDescription = document.querySelector('meta[name="description"]')?.content?.toLowerCase() || '';
    const headings = Array.from(document.querySelectorAll('h1, h2, h3')).map(h => h.innerText.toLowerCase());
    
    // Calculer les scores pour chaque catégorie
    const categoryScores = {};
    let totalMatches = 0;
    
    for (const [category, keywords] of Object.entries(contextCategories)) {
        categoryScores[category] = 0;
        
        for (const keyword of keywords) {
            // Vérifier le titre (poids plus élevé)
            if (pageTitle.includes(keyword)) {
                categoryScores[category] += 3;
                totalMatches++;
            }
            
            // Vérifier les en-têtes (poids élevé)
            for (const heading of headings) {
                if (heading.includes(keyword)) {
                    categoryScores[category] += 2;
                    totalMatches++;
                }
            }
            
            // Vérifier la méta description (poids moyen)
            if (metaDescription.includes(keyword)) {
                categoryScores[category] += 2;
                totalMatches++;
            }
            
            // Vérifier le contenu de la page (poids normal)
            const regex = new RegExp(keyword, 'gi');
            const matches = (pageText.match(regex) || []).length;
            if (matches > 0) {
                // Limiter l'impact des mots qui apparaissent très fréquemment
                categoryScores[category] += Math.min(matches, 5);
                totalMatches += Math.min(matches, 5);
            }
        }
    }
    
    // Déterminer si le contexte est pertinent (seuil minimum)
    if (totalMatches >= 5) {
        // Trier les catégories par score
        const sortedCategories = Object.entries(categoryScores)
            .sort((a, b) => b[1] - a[1])
            .filter(([_, score]) => score > 0);
        
        if (sortedCategories.length > 0) {
            const topCategory = sortedCategories[0][0];
            const topScore = sortedCategories[0][1];
            const relevanceScore = Math.min(topScore / 10, 1); // Normaliser entre 0 et 1
            
            console.log(`Context detected: ${topCategory} with score ${topScore} (relevance: ${relevanceScore.toFixed(2)})`);
            
            // Envoyer les informations détaillées au background script
            chrome.runtime.sendMessage({ 
                action: "contextDetected", 
                context: {
                    mainCategory: topCategory,
                    allCategories: sortedCategories.map(([cat, score]) => ({ category: cat, score })),
                    relevanceScore: relevanceScore,
                    url: window.location.href,
                    title: document.title,
                    timestamp: new Date().toISOString()
                }
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error("Error sending context message:", chrome.runtime.lastError);
                } else {
                    console.log("Background script responded to context detection:", response);
                }
            });
        }
    } else {
        console.log("No relevant context detected on this page.");
    }
}

// Exécuter la détection de contexte après un court délai pour permettre à la page de se charger complètement
setTimeout(detectContext, 2000);

// Fonction pour extraire le contenu pertinent de la page
function extractContent() {
    // Extraire le contenu principal de la page
    let mainContent = "";
    
    // Essayer de trouver le contenu principal en utilisant des sélecteurs courants
    const contentSelectors = [
        'article', 'main', '.content', '.main-content', '#content', '#main-content',
        '.post', '.article', '.blog-post', '.entry-content'
    ];
    
    for (const selector of contentSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            mainContent = element.innerText;
            break;
        }
    }
    
    // Si aucun contenu principal n'a été trouvé, utiliser le premier paragraphe substantiel
    if (!mainContent) {
        const paragraphs = document.querySelectorAll('p');
        for (const p of paragraphs) {
            if (p.innerText.length > 100) {
                mainContent = p.innerText;
                break;
            }
        }
    }
    
    // Si toujours rien, prendre les premiers 1000 caractères du body
    if (!mainContent && document.body) {
        mainContent = document.body.innerText.substring(0, 1000);
    }
    
    // Extraire les images pertinentes
    const images = [];
    const imgElements = document.querySelectorAll('img');
    for (const img of imgElements) {
        // Ne prendre que les images substantielles (pas les icônes)
        if (img.width > 200 && img.height > 200 && img.src) {
            images.push({
                src: img.src,
                alt: img.alt || ''
            });
            
            // Limiter à 3 images
            if (images.length >= 3) break;
        }
    }
    
    return {
        text: mainContent,
        images: images,
        url: window.location.href,
        title: document.title
    };
}

// Écouter les messages du popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "extractContent") {
        sendResponse({ content: extractContent() });
    }
    return true; // Indique que la réponse sera envoyée de manière asynchrone
});