// Logic for the popup window (popup.html)

document.addEventListener('DOMContentLoaded', function() {
    // Éléments de l'interface
    const saveButton = document.getElementById('savePageBtn');
    const statusDiv = document.getElementById('status');
    const categorySelect = document.getElementById('category');
    const notesTextarea = document.getElementById('notes');
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const recentContextsContainer = document.getElementById('recent-contexts');
    const savedPagesContainer = document.getElementById('saved-pages');
    
    // Éléments pour l'assistant
    const assistantQuery = document.getElementById('assistant-query');
    const assistantSend = document.getElementById('assistant-send');
    const assistantMessages = document.getElementById('assistant-messages');
    const suggestionTags = document.querySelectorAll('.suggestion-tag');
    
    // Éléments pour la découverte
    const discoverRecommendations = document.getElementById('discover-recommendations');
    const refreshRecommendationsBtn = document.getElementById('refresh-recommendations');
    
    // Gestion des onglets
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Désactiver tous les onglets
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.add('hidden'));
            
            // Activer l'onglet sélectionné
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(`${tabId}-tab`).classList.remove('hidden');
            
            // Charger les données si nécessaire
            if (tabId === 'recent') {
                loadRecentContexts();
            } else if (tabId === 'saved') {
                loadSavedPages();
            } else if (tabId === 'discover') {
                loadRecommendations();
            }
        });
    });
    
    // Bouton de sauvegarde
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            statusDiv.textContent = 'Sauvegarde en cours...';
            statusDiv.className = 'status-message loading';
            
            // Obtenir l'URL de l'onglet actuel et envoyer un message au script d'arrière-plan
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (tabs && tabs.length > 0) {
                    const currentTab = tabs[0];
                    
                    // Demander au content script d'extraire le contenu pertinent
                    chrome.tabs.sendMessage(currentTab.id, { action: "extractContent" }, (contentResponse) => {
                        const pageInfo = {
                            url: currentTab.url,
                            title: currentTab.title,
                            category: categorySelect.value,
                            notes: notesTextarea.value,
                            savedAt: new Date().toISOString(),
                            content: contentResponse?.content || null
                        };
                        
                        chrome.runtime.sendMessage({ 
                            action: "savePage", 
                            pageInfo: pageInfo 
                        }, (response) => {
                            if (chrome.runtime.lastError) {
                                console.error("Error sending savePage message:", chrome.runtime.lastError);
                                statusDiv.textContent = `Erreur: ${chrome.runtime.lastError.message}`;
                                statusDiv.className = 'status-message error';
                            } else {
                                console.log("Background response to savePage:", response);
                                statusDiv.textContent = response?.message || 'Page sauvegardée.';
                                statusDiv.className = 'status-message success';
                                notesTextarea.value = ''; // Réinitialiser les notes
                                
                                // Mettre à jour l'onglet des pages sauvegardées
                                setTimeout(() => {
                                    loadSavedPages();
                                }, 500);
                            }
                        });
                    });
                } else {
                    statusDiv.textContent = 'Erreur: Impossible d\'obtenir l\'URL de l\'onglet actuel.';
                    statusDiv.className = 'status-message error';
                }
            });
        });
    }
    
    // Charger les contextes récents
    function loadRecentContexts() {
        recentContextsContainer.innerHTML = '<div class="loading">Chargement...</div>';
        
        chrome.runtime.sendMessage({ action: "getRecentContexts" }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("Error getting recent contexts:", chrome.runtime.lastError);
                recentContextsContainer.innerHTML = '<div class="error">Erreur lors du chargement des contextes récents.</div>';
                return;
            }
            
            if (response && response.status === "success") {
                const contexts = response.contexts || [];
                
                if (contexts.length === 0) {
                    recentContextsContainer.innerHTML = '<div class="empty">Aucun contexte récent détecté.</div>';
                    return;
                }
                
                recentContextsContainer.innerHTML = '';
                contexts.forEach(context => {
                    const contextElement = document.createElement('div');
                    contextElement.className = 'item';
                    
                    // Formater la date
                    const date = new Date(context.timestamp);
                    const formattedDate = date.toLocaleDateString('fr-FR', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    
                    // Créer le contenu de l'élément
                    contextElement.innerHTML = `
                        <div class="item-title">${context.title || 'Page sans titre'}</div>
                        <div class="item-meta">
                            <span class="item-category">${context.mainCategory}</span>
                            <span class="item-date">${formattedDate}</span>
                        </div>
                    `;
                    
                    // Ajouter un gestionnaire d'événements pour sauvegarder ce contexte
                    contextElement.addEventListener('click', () => {
                        // Pré-remplir le formulaire de sauvegarde
                        categorySelect.value = context.mainCategory;
                        notesTextarea.value = `Détecté automatiquement comme contenu de ${context.mainCategory}`;
                        
                        // Activer l'onglet de sauvegarde
                        tabButtons.forEach(btn => {
                            if (btn.getAttribute('data-tab') === 'save') {
                                btn.click();
                            }
                        });
                        
                        // Mettre en évidence le formulaire
                        const saveForm = document.querySelector('.save-form');
                        saveForm.style.animation = 'highlight 1s';
                        setTimeout(() => {
                            saveForm.style.animation = '';
                        }, 1000);
                    });
                    
                    recentContextsContainer.appendChild(contextElement);
                });
            } else {
                recentContextsContainer.innerHTML = '<div class="error">Erreur lors du chargement des contextes.</div>';
            }
        });
    }
    
    // Charger les pages sauvegardées
    function loadSavedPages() {
        savedPagesContainer.innerHTML = '<div class="loading">Chargement...</div>';
        
        chrome.runtime.sendMessage({ action: "getSavedPages" }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("Error getting saved pages:", chrome.runtime.lastError);
                savedPagesContainer.innerHTML = '<div class="error">Erreur lors du chargement des pages sauvegardées.</div>';
                return;
            }
            
            if (response && response.status === "success") {
                const pages = response.pages || [];
                
                if (pages.length === 0) {
                    savedPagesContainer.innerHTML = '<div class="empty">Aucune page sauvegardée.</div>';
                    return;
                }
                
                savedPagesContainer.innerHTML = '';
                pages.forEach(page => {
                    const pageElement = document.createElement('div');
                    pageElement.className = 'item';
                    
                    // Formater la date
                    const date = new Date(page.savedAt);
                    const formattedDate = date.toLocaleDateString('fr-FR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: '2-digit'
                    });
                    
                    // Créer le contenu de l'élément
                    pageElement.innerHTML = `
                        <div class="item-title">${page.title || 'Page sans titre'}</div>
                        <div class="item-meta">
                            <span class="item-category">${page.category}</span>
                            <span class="item-date">${formattedDate}</span>
                        </div>
                        ${page.notes ? `<div class="item-notes">${page.notes}</div>` : ''}
                        <div class="item-actions">
                            <button class="action-btn open-btn" title="Ouvrir">Ouvrir</button>
                            <button class="action-btn delete-btn" title="Supprimer">Supprimer</button>
                        </div>
                    `;
                    
                    // Ajouter les gestionnaires d'événements pour les boutons
                    pageElement.querySelector('.open-btn').addEventListener('click', () => {
                        chrome.tabs.create({ url: page.url });
                    });
                    
                    pageElement.querySelector('.delete-btn').addEventListener('click', () => {
                        chrome.runtime.sendMessage({
                            action: "deletePage",
                            url: page.url
                        }, (deleteResponse) => {
                            if (deleteResponse && deleteResponse.status === "success") {
                                // Recharger la liste après suppression
                                loadSavedPages();
                            } else {
                                console.error("Error deleting page:", deleteResponse?.error);
                            }
                        });
                    });
                    
                    savedPagesContainer.appendChild(pageElement);
                });
            } else {
                savedPagesContainer.innerHTML = '<div class="error">Erreur lors du chargement des pages sauvegardées.</div>';
            }
        });
    }
    
    // Fonctions pour l'assistant IA
    function addMessageToAssistant(message, isUser = false) {
        const messageEl = document.createElement('div');
        messageEl.className = isUser ? 'assistant-message user' : 'assistant-message';
        messageEl.textContent = message;
        assistantMessages.appendChild(messageEl);
        assistantMessages.scrollTop = assistantMessages.scrollHeight;
    }
    
    function handleAssistantQuery() {
        const query = assistantQuery.value.trim();
        if (!query) return;
        
        // Ajouter la question de l'utilisateur à l'interface
        addMessageToAssistant(query, true);
        
        // Vider le champ de saisie
        assistantQuery.value = '';
        
        // Indiquer que l'assistant réfléchit
        const thinkingEl = document.createElement('div');
        thinkingEl.className = 'assistant-message';
        thinkingEl.textContent = 'Réflexion en cours...';
        assistantMessages.appendChild(thinkingEl);
        
        // Envoyer la requête au script d'arrière-plan pour traitement
        chrome.runtime.sendMessage({
            action: "askAssistant",
            query: query
        }, (response) => {
            // Supprimer le message "réflexion en cours"
            assistantMessages.removeChild(thinkingEl);
            
            if (chrome.runtime.lastError) {
                console.error("Error with assistant:", chrome.runtime.lastError);
                addMessageToAssistant("Désolé, une erreur s'est produite lors du traitement de votre demande.");
                return;
            }
            
            if (response && response.answer) {
                addMessageToAssistant(response.answer);
            } else {
                addMessageToAssistant("Désolé, je n'ai pas pu trouver de réponse appropriée.");
            }
        });
    }
    
    // Event listener pour l'envoi de message à l'assistant
    if (assistantSend) {
        assistantSend.addEventListener('click', handleAssistantQuery);
    }
    
    // Event listener pour l'envoi via la touche Entrée
    if (assistantQuery) {
        assistantQuery.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleAssistantQuery();
            }
        });
    }
    
    // Event listeners pour les suggestions
    suggestionTags.forEach(tag => {
        tag.addEventListener('click', () => {
            assistantQuery.value = tag.textContent;
            handleAssistantQuery();
        });
    });
    
    // Fonctions pour le mode découverte
    function loadRecommendations() {
        discoverRecommendations.innerHTML = '<div class="loading">Chargement des recommandations...</div>';
        
        // Obtenir l'URL actuelle pour le contexte
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs && tabs.length > 0) {
                const currentTab = tabs[0];
                
                // Demander au content script d'extraire le contexte
                chrome.tabs.sendMessage(currentTab.id, { action: "extractContent" }, (contentResponse) => {
                    // Envoyer la requête au script d'arrière-plan
                    chrome.runtime.sendMessage({
                        action: "getRecommendations",
                        context: {
                            url: currentTab.url,
                            title: currentTab.title,
                            content: contentResponse?.content || null
                        }
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            console.error("Error getting recommendations:", chrome.runtime.lastError);
                            discoverRecommendations.innerHTML = '<div class="error">Erreur lors du chargement des recommandations.</div>';
                            return;
                        }
                        
                        if (response && response.recommendations && response.recommendations.length > 0) {
                            discoverRecommendations.innerHTML = '';
                            
                            response.recommendations.forEach(rec => {
                                const recElement = document.createElement('div');
                                recElement.className = 'recommendation-item';
                                
                                recElement.innerHTML = `
                                    <div class="recommendation-title">${rec.title}</div>
                                    <div class="recommendation-desc">${rec.description}</div>
                                    <div class="recommendation-meta">
                                        <span>${rec.location}</span>
                                        <span class="recommendation-score">${rec.score}% pertinent</span>
                                    </div>
                                `;
                                
                                recElement.addEventListener('click', () => {
                                    chrome.tabs.create({ url: rec.url });
                                });
                                
                                discoverRecommendations.appendChild(recElement);
                            });
                        } else {
                            discoverRecommendations.innerHTML = '<div class="empty">Aucune recommandation disponible pour ce contenu.</div>';
                        }
                    });
                });
            } else {
                discoverRecommendations.innerHTML = '<div class="error">Impossible d\'obtenir les informations de l\'onglet actuel.</div>';
            }
        });
    }
    
    // Event listener pour rafraîchir les recommandations
    if (refreshRecommendationsBtn) {
        refreshRecommendationsBtn.addEventListener('click', loadRecommendations);
    }
    
    // Initialiser l'interface
    // Activer l'onglet par défaut (sauvegarde)
    tabButtons[0]?.click();
});