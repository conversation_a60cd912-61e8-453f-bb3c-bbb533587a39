/* Styles pour le popup de l'extension Retreat And Be Companion */

/* Réinitialisation et styles de base */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    width: 350px;
    min-height: 400px;
    color: #333;
    background-color: #f9f9f9;
    padding: 0;
    overflow-x: hidden;
}

/* En-tête */
header {
    background-color: #4CAF50;
    color: white;
    padding: 12px 15px;
    border-bottom: 1px solid #ddd;
}

header h2 {
    font-size: 18px;
    margin-bottom: 10px;
}

/* Onglets */
.tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.tab-btn:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.tab-btn.active {
    color: white;
    border-bottom: 3px solid white;
    font-weight: bold;
}

/* Contenu principal */
main {
    padding: 15px;
}

.tab-content {
    display: block;
}

.tab-content.hidden {
    display: none;
}

/* Formulaire de sauvegarde */
.save-form {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.save-form h3 {
    font-size: 16px;
    margin-bottom: 15px;
    color: #4CAF50;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #555;
}

select, textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

textarea {
    height: 80px;
    resize: vertical;
}

.primary-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    width: 100%;
    transition: background-color 0.3s ease;
}

.primary-btn:hover {
    background-color: #3e8e41;
}

/* Messages de statut */
.status-message {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
}

.status-message.success {
    background-color: #dff0d8;
    color: #3c763d;
}

.status-message.error {
    background-color: #f2dede;
    color: #a94442;
}

.status-message.loading {
    background-color: #d9edf7;
    color: #31708f;
}

/* Liste d'éléments (contextes et pages sauvegardées) */
.items-list {
    margin-top: 10px;
}

.item {
    background-color: white;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.item:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.item-title {
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 5px;
    color: #333;
}

.item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #777;
    margin-bottom: 5px;
}

.item-category {
    background-color: #e8f5e9;
    color: #4CAF50;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.item-notes {
    font-size: 12px;
    color: #555;
    margin-top: 5px;
    padding-top: 5px;
    border-top: 1px solid #eee;
}

.item-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
}

.action-btn {
    background-color: #f1f1f1;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-left: 5px;
    cursor: pointer;
    color: #555;
}

.action-btn:hover {
    background-color: #e0e0e0;
}

.action-btn.open-btn {
    background-color: #e8f5e9;
    color: #4CAF50;
}

.action-btn.delete-btn {
    background-color: #ffebee;
    color: #e53935;
}

/* Messages d'état vide */
.empty, .loading, .error {
    padding: 20px;
    text-align: center;
    color: #777;
    font-size: 14px;
    background-color: white;
    border-radius: 6px;
    margin-top: 10px;
}

.loading {
    color: #31708f;
}

.error {
    color: #a94442;
}

/* Animation de surbrillance */
@keyframes highlight {
    0% { background-color: #fff; }
    50% { background-color: #e8f5e9; }
    100% { background-color: #fff; }
}

/* Pied de page */
footer {
    padding: 10px 15px;
    text-align: center;
    font-size: 12px;
    color: #999;
    border-top: 1px solid #eee;
    margin-top: 15px;
}

/* Styles pour l'Assistant IA */
.assistant-container {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: 280px;
}

.assistant-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 6px;
    margin-bottom: 10px;
}

.assistant-message {
    background-color: white;
    border-radius: 10px;
    padding: 8px 12px;
    margin-bottom: 8px;
    max-width: 85%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.assistant-message.user {
    background-color: #e3f2fd;
    margin-left: auto;
}

.assistant-input {
    display: flex;
    margin-bottom: 10px;
}

.assistant-input input {
    flex: 1;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
}

.assistant-input button {
    border-radius: 0 4px 4px 0;
    padding: 8px 12px;
    width: auto;
}

.assistant-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.suggestion-tag {
    background-color: #f1f8e9;
    color: #689f38;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.suggestion-tag:hover {
    background-color: #dcedc8;
}

/* Styles pour le Mode Découverte */
.discover-context {
    background-color: #e8f5e9;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #2e7d32;
}

.discover-recommendations {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
}

.recommendation-item {
    border-bottom: 1px solid #eee;
    padding: 10px 0;
}

.recommendation-item:last-child {
    border-bottom: none;
}

.recommendation-title {
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 5px;
    color: #333;
}

.recommendation-desc {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.recommendation-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #888;
}

.recommendation-score {
    background-color: #f1f8e9;
    color: #689f38;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.secondary-btn {
    background-color: #f5f5f5;
    color: #555;
    border: 1px solid #ddd;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 10px;
    width: 100%;
    transition: background-color 0.3s ease;
}

.secondary-btn:hover {
    background-color: #e0e0e0;
}