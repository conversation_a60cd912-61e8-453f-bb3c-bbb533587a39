"""
Routes pour les partenaires.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Path, Body, status

from superagent.models.partner import Partner, PartnerType, PartnerStatus, PartnerSpecialty
from superagent.database.repositories import PartnerRepository

# Configuration du logging
logger = logging.getLogger(__name__)

# C<PERSON>er le router
router = APIRouter()

# Créer le repository
partner_repo = PartnerRepository()

@router.get("/", response_model=List[Partner])
async def get_partners(
    partner_type: Optional[str] = Query(None, description="Type de partenaire"),
    specialty: Optional[str] = Query(None, description="Spécialité du partenaire"),
    status: Optional[str] = Query(None, description="Statut du partenaire"),
    country: Optional[str] = Query(None, description="Pays du partenaire"),
    region: Optional[str] = Query(None, description="Région du partenaire"),
    min_rating: Optional[float] = Query(None, description="Note minimale"),
    limit: int = Query(100, description="Nombre maximum de résultats"),
    offset: int = Query(0, description="Décalage pour la pagination")
):
    """
    Récupère la liste des partenaires avec filtrage optionnel.
    """
    try:
        # Appliquer les filtres
        if partner_type:
            try:
                type_enum = PartnerType(partner_type)
                partners = partner_repo.find_by_type(type_enum, limit, offset)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Type de partenaire invalide: {partner_type}"
                )
        elif specialty:
            try:
                specialty_enum = PartnerSpecialty(specialty)
                partners = partner_repo.find_by_specialty(specialty_enum, limit, offset)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Spécialité invalide: {specialty}"
                )
        elif country:
            partners = partner_repo.find_by_location(country, region, limit, offset)
        elif min_rating is not None:
            partners = partner_repo.find_by_rating(min_rating, limit, offset)
        elif status:
            try:
                status_enum = PartnerStatus(status)
                partners = partner_repo.find_by({'status': status_enum.value}, limit, offset)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Statut invalide: {status}"
                )
        else:
            partners = partner_repo.get_all(limit, offset)
        
        return partners
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des partenaires: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des partenaires"
        )

@router.get("/{partner_id}", response_model=Partner)
async def get_partner(
    partner_id: str = Path(..., description="ID du partenaire")
):
    """
    Récupère un partenaire par son ID.
    """
    try:
        partner = partner_repo.get_by_id(partner_id)
        if not partner:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Partenaire non trouvé: {partner_id}"
            )
        
        return partner
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du partenaire {partner_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la récupération du partenaire {partner_id}"
        )

@router.post("/", response_model=Partner, status_code=status.HTTP_201_CREATED)
async def create_partner(
    partner: Partner = Body(..., description="Partenaire à créer")
):
    """
    Crée un nouveau partenaire.
    """
    try:
        # Vérifier si un partenaire avec le même email existe déjà
        existing_partners = partner_repo.find_by({'email': partner.email}, limit=1)
        if existing_partners:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Un partenaire avec l'email {partner.email} existe déjà"
            )
        
        # Créer le partenaire
        created_partner = partner_repo.create(partner)
        return created_partner
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la création du partenaire: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la création du partenaire"
        )

@router.put("/{partner_id}", response_model=Partner)
async def update_partner(
    partner_id: str = Path(..., description="ID du partenaire"),
    partner: Partner = Body(..., description="Partenaire mis à jour")
):
    """
    Met à jour un partenaire existant.
    """
    try:
        # Vérifier si le partenaire existe
        existing_partner = partner_repo.get_by_id(partner_id)
        if not existing_partner:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Partenaire non trouvé: {partner_id}"
            )
        
        # Vérifier que l'ID correspond
        if partner.id != partner_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="L'ID du partenaire ne correspond pas à l'URL"
            )
        
        # Mettre à jour le partenaire
        updated_partner = partner_repo.update(partner)
        return updated_partner
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du partenaire {partner_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour du partenaire {partner_id}"
        )

@router.delete("/{partner_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_partner(
    partner_id: str = Path(..., description="ID du partenaire")
):
    """
    Supprime un partenaire.
    """
    try:
        # Vérifier si le partenaire existe
        existing_partner = partner_repo.get_by_id(partner_id)
        if not existing_partner:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Partenaire non trouvé: {partner_id}"
            )
        
        # Supprimer le partenaire
        partner_repo.delete(partner_id)
        return None
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la suppression du partenaire {partner_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la suppression du partenaire {partner_id}"
        )

@router.get("/for-retreat/{retreat_id}", response_model=List[Partner])
async def get_partners_for_retreat(
    retreat_id: str = Path(..., description="ID de la retraite"),
    limit: int = Query(10, description="Nombre maximum de résultats")
):
    """
    Récupère les partenaires recommandés pour une retraite.
    """
    try:
        from superagent.database.repositories import RetreatRepository
        
        # Récupérer la retraite
        retreat_repo = RetreatRepository()
        retreat = retreat_repo.get_by_id(retreat_id)
        if not retreat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Retraite non trouvée: {retreat_id}"
            )
        
        # Trouver des partenaires adaptés
        partners = partner_repo.find_for_retreat(
            retreat.retreat_type,
            retreat.location,
            retreat.activities,
            limit
        )
        
        return partners
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(f"Erreur lors de la recherche de partenaires pour la retraite {retreat_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la recherche de partenaires pour la retraite {retreat_id}"
        )
