from typing import Dict, Any, List, Optional
import asyncio
import logging
from datetime import datetime

from graph.graph_manager import GraphManager
from workflows.templates.workflow_templates import WorkflowTemplates

logger = logging.getLogger("api.services.workflow")

class WorkflowService:
    """
    Service pour la gestion des workflows.
    """
    
    def __init__(self):
        self.graph_manager = GraphManager()
        self.workflow_templates = {
            "retreat_search": WorkflowTemplates.get_retreat_search_workflow,
            "partner_matching": WorkflowTemplates.get_partner_matching_workflow,
            "content_generation": WorkflowTemplates.get_content_generation_workflow,
            "customer_support": WorkflowTemplates.get_customer_support_workflow,
            "retreat_planning": WorkflowTemplates.get_retreat_planning_workflow
        }
        
    async def create_workflow(self, workflow_type: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Crée un workflow du type spécifié avec les données d'entrée fournies.
        
        Args:
            workflow_type: Type de workflow à créer
            input_data: Données d'entrée pour le workflow
            
        Returns:
            Informations sur le workflow créé
        """
        logger.info(f"Creating workflow of type: {workflow_type}")
        
        # Vérifier si le type de workflow est supporté
        if workflow_type not in self.workflow_templates:
            return {
                "status": "error",
                "message": f"Unsupported workflow type: {workflow_type}"
            }
            
        try:
            # Obtenir le template de workflow
            workflow_config = self.workflow_templates[workflow_type]()
            
            # Créer le graphe
            graph_id = await self.graph_manager.create_graph(workflow_config)
            logger.info(f"Created graph with ID: {graph_id}")
            
            # Exécuter le workflow
            workflow_id = await self.graph_manager.execute_workflow(graph_id, input_data)
            logger.info(f"Executing workflow with ID: {workflow_id}")
            
            return {
                "status": "success",
                "workflow_id": workflow_id,
                "message": f"Workflow of type '{workflow_type}' created and started"
            }
            
        except Exception as e:
            logger.error(f"Error creating workflow: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to create workflow: {str(e)}"
            }
            
    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """
        Récupère le statut d'un workflow.
        
        Args:
            workflow_id: Identifiant du workflow
            
        Returns:
            Statut du workflow
        """
        logger.info(f"Getting status for workflow: {workflow_id}")
        
        try:
            # Récupérer l'état du workflow
            workflow_state = await self.graph_manager.get_workflow_state(workflow_id)
            
            if not workflow_state:
                return {
                    "status": "error",
                    "message": f"Workflow not found: {workflow_id}"
                }
                
            # Construire la réponse
            response = {
                "workflow_id": workflow_id,
                "status": workflow_state.status,
                "current_node": workflow_state.current_node
            }
            
            # Ajouter les sorties si le workflow est terminé
            if workflow_state.status == "completed":
                response["outputs"] = workflow_state.outputs
                
            # Ajouter l'erreur si le workflow a échoué
            if workflow_state.status == "failed":
                response["error"] = workflow_state.metadata.get("error", "Unknown error")
                
            return response
            
        except Exception as e:
            logger.error(f"Error getting workflow status: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to get workflow status: {str(e)}"
            }
            
    async def list_workflows(self) -> Dict[str, Any]:
        """
        Liste tous les workflows.
        
        Returns:
            Liste des workflows
        """
        logger.info("Listing all workflows")
        
        try:
            # Récupérer tous les workflows
            workflows = []
            
            for workflow_id, workflow_state in self.graph_manager.workflows.items():
                workflows.append({
                    "workflow_id": workflow_id,
                    "graph_name": workflow_state.graph_name,
                    "status": workflow_state.status,
                    "current_node": workflow_state.current_node,
                    "created_at": workflow_state.created_at if hasattr(workflow_state, "created_at") else None
                })
                
            return {
                "status": "success",
                "workflows": workflows,
                "count": len(workflows)
            }
            
        except Exception as e:
            logger.error(f"Error listing workflows: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to list workflows: {str(e)}"
            }
            
    async def list_workflow_templates(self) -> Dict[str, Any]:
        """
        Liste tous les templates de workflow disponibles.
        
        Returns:
            Liste des templates de workflow
        """
        logger.info("Listing all workflow templates")
        
        try:
            # Récupérer tous les templates
            templates = []
            
            for template_id, template_func in self.workflow_templates.items():
                # Obtenir la configuration du template
                template_config = template_func()
                
                templates.append({
                    "template_id": template_id,
                    "name": template_config.name,
                    "description": template_config.description,
                    "node_count": len(template_config.nodes),
                    "edge_count": len(template_config.edges)
                })
                
            return {
                "status": "success",
                "templates": templates,
                "count": len(templates)
            }
            
        except Exception as e:
            logger.error(f"Error listing workflow templates: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to list workflow templates: {str(e)}"
            }
            
    async def get_workflow_template(self, template_id: str) -> Dict[str, Any]:
        """
        Récupère un template de workflow spécifique.
        
        Args:
            template_id: Identifiant du template
            
        Returns:
            Template de workflow
        """
        logger.info(f"Getting workflow template: {template_id}")
        
        # Vérifier si le template existe
        if template_id not in self.workflow_templates:
            return {
                "status": "error",
                "message": f"Template not found: {template_id}"
            }
            
        try:
            # Obtenir la configuration du template
            template_config = self.workflow_templates[template_id]()
            
            # Construire la réponse
            response = {
                "status": "success",
                "template_id": template_id,
                "name": template_config.name,
                "description": template_config.description,
                "nodes": [node.dict() for node in template_config.nodes],
                "edges": [edge.dict() for edge in template_config.edges],
                "entry_point": template_config.entry_point
            }
            
            return response
            
        except Exception as e:
            logger.error(f"Error getting workflow template: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to get workflow template: {str(e)}"
            }
