from typing import Dict, Any, Optional
import logging
import requests
import os
from dotenv import load_dotenv
from .jwt_handler import create_access_token, verify_token

# Charger les variables d'environnement
load_dotenv()

# Configuration
AUTH_SERVICE_URL = os.getenv("AUTH_SERVICE_URL", "http://localhost:8001/auth")
logger = logging.getLogger("api.auth.service")

class AuthService:
    """
    Service d'authentification qui s'intègre avec le service d'authentification existant.
    """
    
    @staticmethod
    async def authenticate_user(username: str, password: str) -> Dict[str, Any]:
        """
        Authentifie un utilisateur auprès du service d'authentification existant.
        
        Args:
            username: Nom d'utilisateur
            password: Mot de passe
            
        Returns:
            Les informations d'authentification, y compris le token JWT
        """
        logger.info(f"Authenticating user: {username}")
        
        try:
            # Appeler le service d'authentification existant
            response = requests.post(
                f"{AUTH_SERVICE_URL}/login",
                json={"username": username, "password": password}
            )
            
            # Vérifier la réponse
            if response.status_code == 200:
                auth_data = response.json()
                
                # Si le service d'authentification existant ne renvoie pas de token JWT,
                # nous en créons un nous-mêmes
                if "access_token" not in auth_data:
                    user_data = {
                        "sub": auth_data.get("user_id", username),
                        "username": username,
                        "role": auth_data.get("role", "user"),
                        "subscription_level": auth_data.get("subscription_level", "basic")
                    }
                    
                    access_token = create_access_token(user_data)
                    auth_data["access_token"] = access_token
                    
                return {
                    "status": "success",
                    "user": {
                        "username": username,
                        "role": auth_data.get("role", "user"),
                        "subscription_level": auth_data.get("subscription_level", "basic")
                    },
                    "access_token": auth_data["access_token"],
                    "token_type": "bearer"
                }
            else:
                error_msg = response.json().get("detail", "Authentication failed")
                logger.error(f"Authentication failed: {error_msg}")
                
                return {
                    "status": "error",
                    "message": error_msg
                }
                
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            
            # En cas d'erreur de connexion au service d'authentification existant,
            # nous pouvons simuler l'authentification pour les tests
            if os.getenv("ENVIRONMENT", "development") == "development":
                logger.warning("Using simulated authentication in development mode")
                
                # Vérifier les identifiants de test
                if username == "test" and password == "test":
                    user_data = {
                        "sub": "test_user_id",
                        "username": username,
                        "role": "user",
                        "subscription_level": "premium"
                    }
                    
                    access_token = create_access_token(user_data)
                    
                    return {
                        "status": "success",
                        "user": {
                            "username": username,
                            "role": "user",
                            "subscription_level": "premium"
                        },
                        "access_token": access_token,
                        "token_type": "bearer"
                    }
                else:
                    return {
                        "status": "error",
                        "message": "Invalid credentials"
                    }
            else:
                return {
                    "status": "error",
                    "message": f"Authentication service error: {str(e)}"
                }
                
    @staticmethod
    async def validate_token(token: str) -> Dict[str, Any]:
        """
        Valide un token JWT.
        
        Args:
            token: Le token JWT à valider
            
        Returns:
            Les informations de l'utilisateur si le token est valide
        """
        logger.info("Validating token")
        
        try:
            # Vérifier le token
            payload = verify_token(token)
            
            return {
                "status": "success",
                "user": {
                    "username": payload.get("username"),
                    "role": payload.get("role", "user"),
                    "subscription_level": payload.get("subscription_level", "basic")
                }
            }
            
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            
            return {
                "status": "error",
                "message": str(e)
            }
            
    @staticmethod
    async def get_user_permissions(user_id: str) -> Dict[str, Any]:
        """
        Récupère les permissions d'un utilisateur.
        
        Args:
            user_id: Identifiant de l'utilisateur
            
        Returns:
            Les permissions de l'utilisateur
        """
        logger.info(f"Getting permissions for user: {user_id}")
        
        try:
            # Appeler le service d'authentification existant
            response = requests.get(
                f"{AUTH_SERVICE_URL}/users/{user_id}/permissions"
            )
            
            # Vérifier la réponse
            if response.status_code == 200:
                return {
                    "status": "success",
                    "permissions": response.json()
                }
            else:
                error_msg = response.json().get("detail", "Failed to get permissions")
                logger.error(f"Failed to get permissions: {error_msg}")
                
                return {
                    "status": "error",
                    "message": error_msg
                }
                
        except Exception as e:
            logger.error(f"Permissions error: {str(e)}")
            
            # En cas d'erreur de connexion au service d'authentification existant,
            # nous pouvons simuler les permissions pour les tests
            if os.getenv("ENVIRONMENT", "development") == "development":
                logger.warning("Using simulated permissions in development mode")
                
                # Permissions simulées
                permissions = {
                    "workflows": {
                        "create": True,
                        "view": True,
                        "execute": True
                    },
                    "templates": {
                        "view": True,
                        "create": False,
                        "edit": False
                    },
                    "agents": {
                        "view": True,
                        "configure": False
                    }
                }
                
                return {
                    "status": "success",
                    "permissions": permissions
                }
            else:
                return {
                    "status": "error",
                    "message": f"Permissions service error: {str(e)}"
                }
