from typing import Optional, Dict, Any
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging
from api.auth.jwt_handler import verify_token

logger = logging.getLogger("api.middleware.auth")

class JWTBearer(HTTPBearer):
    """
    Middleware pour vérifier les tokens JWT dans les requêtes.
    """
    
    def __init__(self, auto_error: bool = True):
        super(JWTBearer, self).__init__(auto_error=auto_error)
        
    async def __call__(self, request: Request) -> Optional[Dict[str, Any]]:
        """
        Vérifie le token JWT dans la requête.
        
        Args:
            request: La requête HTTP
            
        Returns:
            Les données du token JWT si valide
            
        Raises:
            HTTPException: Si le token est invalide ou manquant
        """
        credentials: HTTPAuthorizationCredentials = await super(JW<PERSON><PERSON>ear<PERSON>, self).__call__(request)
        
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Invalid authentication scheme",
                )
                
            # Vérifier le token
            payload = self._verify_jwt(credentials.credentials)
            
            return payload
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid authorization code",
            )
            
    def _verify_jwt(self, token: str) -> Dict[str, Any]:
        """
        Vérifie un token JWT.
        
        Args:
            token: Le token JWT à vérifier
            
        Returns:
            Les données du token JWT
            
        Raises:
            HTTPException: Si le token est invalide
        """
        try:
            return verify_token(token)
        except Exception as e:
            logger.error(f"Token verification error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid token or expired token",
            )

# Fonction pour vérifier les permissions d'un utilisateur
def check_permission(required_permission: str):
    """
    Décorateur pour vérifier les permissions d'un utilisateur.
    
    Args:
        required_permission: La permission requise (format: "resource:action")
        
    Returns:
        Une fonction qui vérifie les permissions
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Récupérer l'utilisateur à partir des kwargs
            user = kwargs.get("user")
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User not authenticated",
                )
                
            # Vérifier les permissions
            resource, action = required_permission.split(":")
            
            # Récupérer les permissions de l'utilisateur
            user_permissions = user.get("permissions", {})
            
            # Vérifier si l'utilisateur a la permission requise
            if resource in user_permissions and action in user_permissions[resource]:
                if user_permissions[resource][action]:
                    return await func(*args, **kwargs)
                    
            # Si l'utilisateur n'a pas la permission requise
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied: {required_permission}",
            )
            
        return wrapper
    return decorator
