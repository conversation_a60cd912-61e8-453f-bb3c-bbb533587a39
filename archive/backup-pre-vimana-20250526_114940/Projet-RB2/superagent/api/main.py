from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import uvicorn
import os
from dotenv import load_dotenv

from api.routes.workflow_routes import router as workflow_router
from api.routes.auth_routes import router as auth_router

# Charger les variables d'environnement
load_dotenv()

# Configurer le logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("api.log")
    ]
)

logger = logging.getLogger("api.main")

# Créer l'application FastAPI
app = FastAPI(
    title="Retreat And Be API",
    description="API pour la plateforme Retreat And Be",
    version="1.0.0"
)

# Configurer CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # En production, spécifier les origines autorisées
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Inclure les routes
app.include_router(auth_router)
app.include_router(workflow_router)

@app.get("/")
async def root():
    """
    Route racine de l'API.
    """
    return {
        "message": "Welcome to the Retreat And Be API",
        "version": "1.0.0",
        "docs_url": "/docs"
    }

@app.get("/health")
async def health_check():
    """
    Vérifie l'état de santé de l'API.
    """
    return {
        "status": "healthy",
        "message": "API is running"
    }

if __name__ == "__main__":
    logger.info("Starting API server")
    uvicorn.run("api.main:app", host="0.0.0.0", port=8000, reload=True)
