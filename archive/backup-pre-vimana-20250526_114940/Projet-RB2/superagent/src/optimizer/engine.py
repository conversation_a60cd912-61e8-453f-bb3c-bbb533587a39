"""
Moteur d'optimisation pour Retreat And Be.
Ce module implémente le moteur d'optimisation et personnalisation.
"""

import os
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from pydantic import BaseModel
from enum import Enum
from datetime import datetime

logger = logging.getLogger("optimizer")

class OptimizationType(str, Enum):
    """Types d'optimisations disponibles."""
    UI_PERSONALIZATION = "ui_personalization"
    AB_TESTING = "ab_testing"
    USER_JOURNEY = "user_journey"
    DYNAMIC_PRICING = "dynamic_pricing"
    CONTENT_PLACEMENT = "content_placement"

class OptimizationRequest(BaseModel):
    """Modèle de données pour une demande d'optimisation."""
    optimization_type: OptimizationType
    parameters: Dict[str, Any]
    context: Dict[str, Any] = {}
    constraints: Dict[str, Any] = {}

class OptimizationResponse(BaseModel):
    """Modèle de données pour une réponse d'optimisation."""
    recommendations: Dict[str, Any]
    expected_impact: Dict[str, Any]
    metadata: Dict[str, Any] = {}

class OptimizerEngine:
    """Moteur d'optimisation principal pour Retreat And Be."""
    
    def __init__(self, model_path: Optional[str] = None):
        """
        Initialise le moteur d'optimisation.
        
        Args:
            model_path: Chemin vers le modèle pré-entraîné (optionnel)
        """
        self.model_path = model_path or os.getenv("MODEL_PATH", "models/")
        self.model_name = os.getenv("OPTIMIZER_MODEL", "optimizer_v1")
        self.models = {}
        
        logger.info(f"Initializing OptimizerEngine with model: {self.model_name}")
        
        # Charger les modèles (à implémenter)
        self._load_models()
    
    def _load_models(self):
        """Charge les modèles d'optimisation."""
        try:
            # Vérifier si le répertoire des modèles existe
            if not os.path.exists(self.model_path):
                os.makedirs(self.model_path, exist_ok=True)
                logger.warning(f"Model directory {self.model_path} did not exist, created it")
            
            logger.info(f"Loading models from {self.model_path}/{self.model_name}")
            
            # Charger différents modèles pour différents types d'optimisation
            for optimization_type in OptimizationType:
                model_file = f"{self.model_path}/{optimization_type.lower()}_model.pkl"
                
                if os.path.exists(model_file):
                    try:
                        # Dans une implémentation réelle, nous utiliserions joblib ou pickle
                        # self.models[optimization_type] = joblib.load(model_file)
                        logger.info(f"Loaded model for {optimization_type} from {model_file}")
                        
                        # Pour l'instant, simulons un modèle plus avancé qu'un simple placeholder
                        if optimization_type == OptimizationType.UI_PERSONALIZATION:
                            self.models[optimization_type] = self._create_ui_personalization_model()
                        elif optimization_type == OptimizationType.AB_TESTING:
                            self.models[optimization_type] = self._create_ab_testing_model()
                        elif optimization_type == OptimizationType.USER_JOURNEY:
                            self.models[optimization_type] = self._create_user_journey_model()
                        elif optimization_type == OptimizationType.DYNAMIC_PRICING:
                            self.models[optimization_type] = self._create_dynamic_pricing_model()
                        elif optimization_type == OptimizationType.CONTENT_PLACEMENT:
                            self.models[optimization_type] = self._create_content_placement_model()
                    except Exception as e:
                        logger.error(f"Error loading model {model_file}: {str(e)}")
                        self.models[optimization_type] = self._create_fallback_model(optimization_type)
                else:
                    logger.warning(f"Model file {model_file} not found, using fallback model")
                    self.models[optimization_type] = self._create_fallback_model(optimization_type)
            
            logger.info("Models loaded successfully")
        except Exception as e:
            logger.error(f"Error loading models: {str(e)}")
            # Fallback à des modèles simples basés sur des règles
            for optimization_type in OptimizationType:
                self.models[optimization_type] = self._create_fallback_model(optimization_type)
    
    def _create_fallback_model(self, optimization_type: OptimizationType):
        """Crée un modèle de fallback pour un type d'optimisation donné."""
        logger.info(f"Creating fallback model for {optimization_type}")
        return f"fallback_{optimization_type.lower()}_model"
    
    def _create_ui_personalization_model(self):
        """Crée un modèle pour la personnalisation de l'interface utilisateur."""
        # Dans une implémentation réelle, nous utiliserions un modèle ML
        # Pour l'instant, nous utilisons un dictionnaire avec des règles
        return {
            "name": "ui_personalization_model_v1",
            "type": "rule_based",
            "rules": {
                "Yogis Passionnés": {
                    "theme": {"primary": "#4CAF50", "secondary": "#8BC34A", "accent": "#FFC107"},
                    "layout": ["hero_section", "featured_retreats", "testimonials", "newsletter_signup"],
                    "components": {
                        "hero_section": {"variant": "nature", "cta_text": "Réserver maintenant"},
                        "featured_retreats": {"count": 6, "filter": "Yogis Passionnés"}
                    }
                },
                "Explorateurs Occasionnels": {
                    "theme": {"primary": "#2196F3", "secondary": "#03A9F4", "accent": "#FF5722"},
                    "layout": ["hero_section", "featured_retreats", "testimonials", "newsletter_signup"],
                    "components": {
                        "hero_section": {"variant": "nature", "cta_text": "Découvrir des retraites"},
                        "featured_retreats": {"count": 6, "filter": "Explorateurs Occasionnels"}
                    }
                },
                "default": {
                    "theme": {"primary": "#4CAF50", "secondary": "#8BC34A", "accent": "#FFC107"},
                    "layout": ["hero_section", "featured_retreats", "testimonials", "newsletter_signup"],
                    "components": {
                        "hero_section": {"variant": "wellness", "cta_text": "Découvrir des retraites"},
                        "featured_retreats": {"count": 6, "filter": "all"}
                    }
                }
            }
        }
    
    def _create_ab_testing_model(self):
        """Crée un modèle pour les tests A/B."""
        return {
            "name": "ab_testing_model_v1",
            "type": "statistical",
            "tests": {
                "hero_cta": {
                    "variants": [
                        {"id": "A", "name": "Découvrir", "allocation": 0.5},
                        {"id": "B", "name": "Réserver maintenant", "allocation": 0.5}
                    ],
                    "metrics": ["click_through_rate", "conversion_rate"],
                    "min_sample_size": 1000
                },
                "layout": {
                    "variants": [
                        {"id": "A", "name": "Standard", "allocation": 0.5},
                        {"id": "B", "name": "Alternative", "allocation": 0.5}
                    ],
                    "metrics": ["engagement_time", "conversion_rate"],
                    "min_sample_size": 1500
                }
            },
            "assignment_method": "hash_based"
        }
    
    def _create_user_journey_model(self):
        """Crée un modèle pour l'optimisation du parcours utilisateur."""
        return {
            "name": "user_journey_model_v1",
            "type": "graph_based",
            "journeys": {
                "new_user": [
                    {"step": "landing", "next": ["browse", "search"], "probabilities": [0.7, 0.3]},
                    {"step": "browse", "next": ["retreat_detail", "exit"], "probabilities": [0.6, 0.4]},
                    {"step": "search", "next": ["retreat_detail", "exit"], "probabilities": [0.8, 0.2]},
                    {"step": "retreat_detail", "next": ["booking", "exit"], "probabilities": [0.3, 0.7]},
                    {"step": "booking", "next": ["confirmation", "exit"], "probabilities": [0.9, 0.1]},
                    {"step": "confirmation", "next": ["exit"], "probabilities": [1.0]}
                ],
                "returning_user": [
                    {"step": "landing", "next": ["browse", "search", "account"], "probabilities": [0.4, 0.3, 0.3]},
                    {"step": "browse", "next": ["retreat_detail", "exit"], "probabilities": [0.7, 0.3]},
                    {"step": "search", "next": ["retreat_detail", "exit"], "probabilities": [0.8, 0.2]},
                    {"step": "account", "next": ["retreat_detail", "exit"], "probabilities": [0.6, 0.4]},
                    {"step": "retreat_detail", "next": ["booking", "exit"], "probabilities": [0.5, 0.5]},
                    {"step": "booking", "next": ["confirmation", "exit"], "probabilities": [0.95, 0.05]},
                    {"step": "confirmation", "next": ["exit"], "probabilities": [1.0]}
                ]
            },
            "optimization_targets": ["conversion_rate", "average_order_value"]
        }
    
    def _create_dynamic_pricing_model(self):
        """Crée un modèle pour le pricing dynamique."""
        return {
            "name": "dynamic_pricing_model_v1",
            "type": "rule_based",
            "factors": {
                "demand": {"weight": 0.4, "curve": "linear"},
                "seasonality": {"weight": 0.3, "curve": "sinusoidal"},
                "competitor_prices": {"weight": 0.2, "curve": "step"},
                "inventory": {"weight": 0.1, "curve": "exponential"}
            },
            "constraints": {
                "min_price_factor": 0.8,
                "max_price_factor": 1.3,
                "max_daily_change": 0.05
            }
        }
    
    def _create_content_placement_model(self):
        """Crée un modèle pour l'optimisation du placement de contenu."""
        return {
            "name": "content_placement_model_v1",
            "type": "collaborative_filtering",
            "features": [
                "user_interests",
                "content_category",
                "content_popularity",
                "recency",
                "user_history"
            ],
            "weights": {
                "user_interests": 0.4,
                "content_category": 0.2,
                "content_popularity": 0.2,
                "recency": 0.1,
                "user_history": 0.1
            }
        }
    
    def optimize(self, request: OptimizationRequest) -> OptimizationResponse:
        """
        Effectue une optimisation selon les paramètres spécifiés.
        
        Args:
            request: Demande d'optimisation
            
        Returns:
            Recommandations d'optimisation et impact attendu
        """
        logger.info(f"Performing {request.optimization_type} optimization")
        
        # Logique d'optimisation à implémenter
        # Pour l'instant, retourne des résultats fictifs selon le type d'optimisation
        
        recommendations = {}
        expected_impact = {}
        
        if request.optimization_type == OptimizationType.UI_PERSONALIZATION:
            recommendations, expected_impact = self._perform_ui_personalization(request.parameters, request.context, request.constraints)
        elif request.optimization_type == OptimizationType.AB_TESTING:
            recommendations, expected_impact = self._perform_ab_testing(request.parameters, request.context, request.constraints)
        elif request.optimization_type == OptimizationType.USER_JOURNEY:
            recommendations, expected_impact = self._perform_user_journey_optimization(request.parameters, request.context, request.constraints)
        elif request.optimization_type == OptimizationType.DYNAMIC_PRICING:
            recommendations, expected_impact = self._perform_dynamic_pricing(request.parameters, request.context, request.constraints)
        elif request.optimization_type == OptimizationType.CONTENT_PLACEMENT:
            recommendations, expected_impact = self._perform_content_placement_optimization(request.parameters, request.context, request.constraints)
        
        return OptimizationResponse(
            recommendations=recommendations,
            expected_impact=expected_impact,
            metadata={
                "optimization_type": request.optimization_type,
                "timestamp": datetime.now().isoformat(),
                "context": request.context,
                "constraints": request.constraints
            }
        )
    
    def _perform_ui_personalization(
        self, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any],
        constraints: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Effectue une personnalisation de l'interface utilisateur.
        
        Args:
            parameters: Paramètres spécifiques à l'optimisation
            context: Contexte de l'utilisateur
            constraints: Contraintes à respecter
            
        Returns:
            Recommandations et impact attendu
        """
        # Extraire les informations de l'utilisateur
        user_id = context.get("user_id", "anonymous")
        user_segment = context.get("user_segment", "unknown")
        platform = context.get("platform", "web")
        
        # Personnalisation fictive de l'interface
        ui_components = {
            "hero_section": {
                "variant": "nature" if user_segment in ["Yogis Passionnés", "Explorateurs Occasionnels"] else "wellness",
                "cta_text": "Découvrir des retraites" if user_segment == "Jeunes Curieux" else "Réserver maintenant",
                "priority": 1
            },
            "featured_retreats": {
                "count": 6 if platform == "web" else 4,
                "filter": user_segment,
                "priority": 2
            },
            "testimonials": {
                "show": True,
                "filter_by_segment": user_segment,
                "priority": 3
            },
            "newsletter_signup": {
                "show": user_segment not in ["Seniors Bien-être"],
                "position": "middle" if user_segment == "Jeunes Curieux" else "bottom",
                "priority": 4
            }
        }
        
        # Layout personnalisé selon la plateforme
        layout = {
            "web": ["hero_section", "featured_retreats", "testimonials", "newsletter_signup"],
            "mobile": ["hero_section", "featured_retreats", "newsletter_signup", "testimonials"],
            "extension": ["featured_retreats", "hero_section"]
        }.get(platform, ["hero_section", "featured_retreats", "testimonials", "newsletter_signup"])
        
        # Couleurs et thème
        theme = {
            "Yogis Passionnés": {"primary": "#4CAF50", "secondary": "#8BC34A", "accent": "#FFC107"},
            "Explorateurs Occasionnels": {"primary": "#2196F3", "secondary": "#03A9F4", "accent": "#FF5722"},
            "Seniors Bien-être": {"primary": "#9C27B0", "secondary": "#E1BEE7", "accent": "#FFEB3B"},
            "Jeunes Curieux": {"primary": "#FF5722", "secondary": "#FF9800", "accent": "#607D8B"},
            "Professionnels Stressés": {"primary": "#3F51B5", "secondary": "#7986CB", "accent": "#00BCD4"}
        }.get(user_segment, {"primary": "#4CAF50", "secondary": "#8BC34A", "accent": "#FFC107"})
        
        recommendations = {
            "ui_components": ui_components,
            "layout": layout,
            "theme": theme
        }
        
        expected_impact = {
            "engagement_increase": 0.15,
            "conversion_increase": 0.08,
            "satisfaction_increase": 0.12
        }
        
        return recommendations, expected_impact
    
    def _perform_ab_testing(
        self, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any],
        constraints: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Configure et analyse des tests A/B.
        
        Args:
            parameters: Paramètres spécifiques à l'optimisation
            context: Contexte de l'utilisateur
            constraints: Contraintes à respecter
            
        Returns:
            Recommandations et impact attendu
        """
        # Extraire les informations du test
        test_name = parameters.get("test_name", "default_test")
        test_type = parameters.get("test_type", "ab")  # ab, multivariate, etc.
        
        # Définir les variantes de test fictives
        variants = [
            {
                "id": "A",
                "name": "Control",
                "description": "Version actuelle",
                "allocation": 0.5
            },
            {
                "id": "B",
                "name": "Variant",
                "description": "Nouvelle version avec CTA amélioré",
                "allocation": 0.5
            }
        ]
        
        # Métriques à suivre
        metrics = [
            {
                "name": "click_through_rate",
                "description": "Taux de clic sur le CTA principal",
                "primary": True
            },
            {
                "name": "conversion_rate",
                "description": "Taux de conversion en réservation",
                "primary": True
            },
            {
                "name": "bounce_rate",
                "description": "Taux de rebond",
                "primary": False
            }
        ]
        
        # Durée recommandée du test
        sample_size = 1000  # Taille d'échantillon fictive
        daily_traffic = 100  # Trafic quotidien fictif
        recommended_duration = max(7, sample_size // daily_traffic)  # Au moins 7 jours
        
        # Allocation des utilisateurs
        user_id = context.get("user_id", "anonymous")
        user_hash = hash(user_id) % 100
        assigned_variant = "A" if user_hash < 50 else "B"
        
        recommendations = {
            "test_configuration": {
                "name": test_name,
                "type": test_type,
                "variants": variants,
                "metrics": metrics,
                "recommended_duration": recommended_duration,
                "sample_size": sample_size
            },
            "user_assignment": {
                "user_id": user_id,
                "assigned_variant": assigned_variant
            }
        }
        
        expected_impact = {
            "confidence_level": 0.95,
            "minimum_detectable_effect": 0.1,
            "expected_improvement": 0.15
        }
        
        return recommendations, expected_impact
    
    def _perform_user_journey_optimization(
        self, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any],
        constraints: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Optimise le parcours utilisateur.
        
        Args:
            parameters: Paramètres spécifiques à l'optimisation
            context: Contexte de l'utilisateur
            constraints: Contraintes à respecter
            
        Returns:
            Recommandations et impact attendu
        """
        # Extraire les informations de l'utilisateur
        user_id = context.get("user_id", "anonymous")
        user_segment = context.get("user_segment", "unknown")
        current_page = context.get("current_page", "home")
        user_history = context.get("user_history", [])
        
        # Déterminer l'étape actuelle du parcours
        journey_stage = "discovery"
        if any(page.get("type") == "retreat_detail" for page in user_history):
            journey_stage = "consideration"
        if any(page.get("type") == "checkout" for page in user_history):
            journey_stage = "conversion"
        if any(page.get("type") == "booking_confirmation" for page in user_history):
            journey_stage = "post_purchase"
        
        # Recommandations de parcours optimisé
        next_steps = {
            "discovery": [
                {
                    "action": "show_personalized_retreats",
                    "priority": 1,
                    "description": "Afficher des retraites personnalisées basées sur les préférences"
                },
                {
                    "action": "suggest_preference_quiz",
                    "priority": 2,
                    "description": "Proposer un quiz pour affiner les préférences"
                }
            ],
            "consideration": [
                {
                    "action": "show_testimonials",
                    "priority": 1,
                    "description": "Afficher des témoignages pertinents"
                },
                {
                    "action": "offer_virtual_tour",
                    "priority": 2,
                    "description": "Proposer une visite virtuelle de la retraite"
                },
                {
                    "action": "display_limited_availability",
                    "priority": 3,
                    "description": "Indiquer la disponibilité limitée si applicable"
                }
            ],
            "conversion": [
                {
                    "action": "simplify_checkout",
                    "priority": 1,
                    "description": "Simplifier le processus de paiement"
                },
                {
                    "action": "offer_payment_options",
                    "priority": 2,
                    "description": "Proposer plusieurs options de paiement"
                },
                {
                    "action": "show_security_badges",
                    "priority": 3,
                    "description": "Afficher les badges de sécurité et de confiance"
                }
            ],
            "post_purchase": [
                {
                    "action": "send_preparation_guide",
                    "priority": 1,
                    "description": "Envoyer un guide de préparation pour la retraite"
                },
                {
                    "action": "suggest_complementary_products",
                    "priority": 2,
                    "description": "Suggérer des produits complémentaires"
                },
                {
                    "action": "invite_to_community",
                    "priority": 3,
                    "description": "Inviter à rejoindre la communauté"
                }
            ]
        }
        
        # Points de friction potentiels
        friction_points = [
            {
                "page": "retreat_detail",
                "element": "booking_calendar",
                "issue": "Complexité de sélection des dates",
                "recommendation": "Simplifier le calendrier et pré-sélectionner les dates optimales"
            },
            {
                "page": "checkout",
                "element": "payment_form",
                "issue": "Trop de champs obligatoires",
                "recommendation": "Réduire le nombre de champs et activer l'auto-complétion"
            }
        ]
        
        recommendations = {
            "journey_stage": journey_stage,
            "next_steps": next_steps.get(journey_stage, []),
            "friction_points": [fp for fp in friction_points if fp["page"] == current_page],
            "personalization": {
                "messaging": "Découvrez des retraites qui vous correspondent" if journey_stage == "discovery" else "Finalisez votre expérience bien-être",
                "emphasis": "variety" if journey_stage == "discovery" else "trust" if journey_stage == "conversion" else "value"
            }
        }
        
        expected_impact = {
            "conversion_increase": 0.12,
            "cart_abandonment_reduction": 0.18,
            "average_session_duration_increase": 0.25
        }
        
        return recommendations, expected_impact
    
    def _perform_dynamic_pricing(
        self, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any],
        constraints: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Effectue une optimisation de prix dynamique.
        
        Args:
            parameters: Paramètres spécifiques à l'optimisation
            context: Contexte de l'utilisateur et du marché
            constraints: Contraintes à respecter
            
        Returns:
            Recommandations et impact attendu
        """
        # Extraire les informations du produit et du marché
        retreat_id = parameters.get("retreat_id", "unknown")
        base_price = parameters.get("base_price", 1000)
        min_price = constraints.get("min_price", base_price * 0.7)
        max_price = constraints.get("max_price", base_price * 1.3)
        
        # Facteurs d'ajustement fictifs
        demand_factor = context.get("demand_level", 0.5)  # 0 à 1
        seasonality_factor = context.get("seasonality", 0.5)  # 0 à 1
        competition_factor = context.get("competition_level", 0.5)  # 0 à 1
        user_segment = context.get("user_segment", "unknown")
        
        # Ajustements de prix selon les segments
        segment_multipliers = {
            "Yogis Passionnés": 1.05,
            "Explorateurs Occasionnels": 0.95,
            "Seniors Bien-être": 1.10,
            "Jeunes Curieux": 0.85,
            "Professionnels Stressés": 1.15
        }
        segment_multiplier = segment_multipliers.get(user_segment, 1.0)
        
        # Calcul du prix optimisé
        price_adjustment = (
            (demand_factor - 0.5) * 0.2 +  # -10% à +10% selon la demande
            (seasonality_factor - 0.5) * 0.1 +  # -5% à +5% selon la saisonnalité
            (competition_factor - 0.5) * -0.1  # +5% à -5% selon la concurrence (inverse)
        )
        
        optimized_price = base_price * (1 + price_adjustment) * segment_multiplier
        optimized_price = max(min_price, min(max_price, optimized_price))  # Respecter les contraintes
        
        # Stratégies de prix
        pricing_strategies = [
            {
                "name": "Standard",
                "price": optimized_price,
                "description": "Prix optimisé standard"
            },
            {
                "name": "Early Bird",
                "price": optimized_price * 0.9,
                "description": "Réduction pour réservation anticipée",
                "conditions": "Réservation 60 jours avant la date de début"
            },
            {
                "name": "Last Minute",
                "price": optimized_price * 0.85,
                "description": "Réduction de dernière minute",
                "conditions": "Réservation 7 jours avant la date de début, si disponibilité"
            },
            {
                "name": "Premium",
                "price": optimized_price * 1.2,
                "description": "Offre premium avec services additionnels",
                "additional_services": ["Transfert privé", "Massage de bienvenue", "Chambre supérieure"]
            }
        ]
        
        recommendations = {
            "retreat_id": retreat_id,
            "base_price": base_price,
            "optimized_price": optimized_price,
            "price_adjustment_factors": {
                "demand": demand_factor,
                "seasonality": seasonality_factor,
                "competition": competition_factor,
                "user_segment": user_segment
            },
            "pricing_strategies": pricing_strategies,
            "recommended_strategy": "Standard" if demand_factor > 0.7 else "Early Bird" if demand_factor < 0.3 else "Standard"
        }
        
        expected_impact = {
            "revenue_increase": 0.08,
            "occupancy_rate_increase": 0.05,
            "profit_margin_increase": 0.12
        }
        
        return recommendations, expected_impact
    
    def _perform_content_placement_optimization(
        self, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any],
        constraints: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Optimise le placement de contenu.
        
        Args:
            parameters: Paramètres spécifiques à l'optimisation
            context: Contexte de l'utilisateur
            constraints: Contraintes à respecter
            
        Returns:
            Recommandations et impact attendu
        """
        # Extraire les informations de l'utilisateur et de la page
        user_id = context.get("user_id", "anonymous")
        user_segment = context.get("user_segment", "unknown")
        page_type = context.get("page_type", "home")
        available_slots = parameters.get("available_slots", 6)
        
        # Contenus disponibles (fictifs)
        available_content = [
            {
                "id": "retreat_1",
                "type": "retreat",
                "title": "Retraite Yoga en Provence",
                "relevance_scores": {
                    "Yogis Passionnés": 0.95,
                    "Explorateurs Occasionnels": 0.75,
                    "Seniors Bien-être": 0.65,
                    "Jeunes Curieux": 0.55,
                    "Professionnels Stressés": 0.85
                }
            },
            {
                "id": "retreat_2",
                "type": "retreat",
                "title": "Randonnée Méditative dans les Alpes",
                "relevance_scores": {
                    "Yogis Passionnés": 0.75,
                    "Explorateurs Occasionnels": 0.95,
                    "Seniors Bien-être": 0.55,
                    "Jeunes Curieux": 0.65,
                    "Professionnels Stressés": 0.70
                }
            },
            {
                "id": "retreat_3",
                "type": "retreat",
                "title": "Cure Détox en Bretagne",
                "relevance_scores": {
                    "Yogis Passionnés": 0.65,
                    "Explorateurs Occasionnels": 0.55,
                    "Seniors Bien-être": 0.85,
                    "Jeunes Curieux": 0.45,
                    "Professionnels Stressés": 0.90
                }
            },
            {
                "id": "article_1",
                "type": "article",
                "title": "5 Postures de Yoga pour Débutants",
                "relevance_scores": {
                    "Yogis Passionnés": 0.85,
                    "Explorateurs Occasionnels": 0.55,
                    "Seniors Bien-être": 0.75,
                    "Jeunes Curieux": 0.90,
                    "Professionnels Stressés": 0.65
                }
            },
            {
                "id": "article_2",
                "type": "article",
                "title": "Alimentation et Bien-être: Guide Complet",
                "relevance_scores": {
                    "Yogis Passionnés": 0.80,
                    "Explorateurs Occasionnels": 0.60,
                    "Seniors Bien-être": 0.90,
                    "Jeunes Curieux": 0.75,
                    "Professionnels Stressés": 0.85
                }
            },
            {
                "id": "video_1",
                "type": "video",
                "title": "Méditation Guidée pour Débutants",
                "relevance_scores": {
                    "Yogis Passionnés": 0.75,
                    "Explorateurs Occasionnels": 0.65,
                    "Seniors Bien-être": 0.80,
                    "Jeunes Curieux": 0.95,
                    "Professionnels Stressés": 0.90
                }
            },
            {
                "id": "testimonial_1",
                "type": "testimonial",
                "title": "Comment j'ai transformé ma vie grâce au yoga",
                "relevance_scores": {
                    "Yogis Passionnés": 0.95,
                    "Explorateurs Occasionnels": 0.70,
                    "Seniors Bien-être": 0.75,
                    "Jeunes Curieux": 0.85,
                    "Professionnels Stressés": 0.80
                }
            },
            {
                "id": "promotion_1",
                "type": "promotion",
                "title": "Offre Spéciale: -20% sur les Retraites d'Automne",
                "relevance_scores": {
                    "Yogis Passionnés": 0.85,
                    "Explorateurs Occasionnels": 0.90,
                    "Seniors Bien-être": 0.75,
                    "Jeunes Curieux": 0.95,
                    "Professionnels Stressés": 0.80
                }
            }
        ]
        
        # Trier les contenus par pertinence pour le segment de l'utilisateur
        for content in available_content:
            content["relevance"] = content["relevance_scores"].get(user_segment, 0.5)
        
        sorted_content = sorted(available_content, key=lambda x: x["relevance"], reverse=True)
        
        # Sélectionner les contenus les plus pertinents
        selected_content = sorted_content[:available_slots]
        
        # Définir le placement optimal
        if page_type == "home":
            layout = [
                {"position": "hero", "content_ids": [selected_content[0]["id"]]},
                {"position": "featured", "content_ids": [c["id"] for c in selected_content[1:4]]},
                {"position": "sidebar", "content_ids": [c["id"] for c in selected_content[4:6]]}
            ]
        elif page_type == "retreat_listing":
            layout = [
                {"position": "top_banner", "content_ids": [selected_content[0]["id"]]},
                {"position": "recommended", "content_ids": [c["id"] for c in selected_content[1:5]]},
                {"position": "bottom", "content_ids": [c["id"] for c in selected_content[5:6]]}
            ]
        else:
            layout = [
                {"position": "main", "content_ids": [c["id"] for c in selected_content[:available_slots]]}
            ]
        
        recommendations = {
            "selected_content": selected_content,
            "layout": layout,
            "content_mix": {
                "retreats": len([c for c in selected_content if c["type"] == "retreat"]),
                "articles": len([c for c in selected_content if c["type"] == "article"]),
                "videos": len([c for c in selected_content if c["type"] == "video"]),
                "testimonials": len([c for c in selected_content if c["type"] == "testimonial"]),
                "promotions": len([c for c in selected_content if c["type"] == "promotion"])
            }
        }
        
        expected_impact = {
            "click_through_rate_increase": 0.18,
            "engagement_time_increase": 0.25,
            "conversion_rate_increase": 0.12
        }
        
        return recommendations, expected_impact
