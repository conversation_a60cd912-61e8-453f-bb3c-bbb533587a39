"""
Orchestrateur principal de l'IA de Retreat And Be.
Ce module coordonne les différents composants IA et expose les APIs.
"""

import os
import logging
from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

# Chargement des variables d'environnement
load_dotenv()

# Configuration du logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.getenv("LOG_FILE", "logs/retreatandbe_ai.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("orchestrator")

# Création de l'application FastAPI
app = FastAPI(
    title="Retreat And Be AI API",
    description="API pour l'intelligence artificielle de Retreat And Be",
    version="0.1.0"
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # À remplacer par les domaines spécifiques en production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Importation des sous-modules (à implémenter)
# from superagent.recommender import RecommenderEngine
# from superagent.content import ContentEngine
# from superagent.chatbot import ChatbotEngine
# from superagent.analytics import AnalyticsEngine
# from superagent.optimizer import OptimizerEngine

@app.get("/")
async def root():
    """Point d'entrée principal de l'API."""
    return {
        "message": "Bienvenue sur l'API d'intelligence artificielle de Retreat And Be",
        "status": "online",
        "version": "0.1.0"
    }

@app.get("/health")
async def health_check():
    """Vérification de l'état de santé de l'API."""
    # Vérifier l'état des différents composants
    components_status = {
        "orchestrator": "online",
        "recommender": os.getenv("ENABLE_RECOMMENDER") == "True" and "online" or "disabled",
        "content": os.getenv("ENABLE_CONTENT_GENERATOR") == "True" and "online" or "disabled",
        "chatbot": os.getenv("ENABLE_CHATBOT") == "True" and "online" or "disabled",
        "analytics": os.getenv("ENABLE_ANALYTICS") == "True" and "online" or "disabled",
        "optimizer": os.getenv("ENABLE_OPTIMIZER") == "True" and "online" or "disabled",
    }
    
    return {
        "status": "healthy",
        "components": components_status,
        "environment": os.getenv("ENVIRONMENT", "development")
    }

# Point d'entrée pour exécuter l'application
if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", 8000))
    debug = os.getenv("API_DEBUG", "True").lower() == "true"
    
    logger.info(f"Starting Retreat And Be AI API on {host}:{port} (debug={debug})")
    
    uvicorn.run("src.orchestrator.main:app", host=host, port=port, reload=debug)
