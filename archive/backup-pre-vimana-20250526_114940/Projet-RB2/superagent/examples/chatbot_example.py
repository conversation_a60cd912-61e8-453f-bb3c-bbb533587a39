"""
Exemple d'utilisation du chatbot Retreat And Be.

Ce script montre comment utiliser le chatbot via l'API REST.
"""

import requests
import json
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration
CHATBOT_API_URL = os.getenv("CHATBOT_API_URL", "http://localhost:8001")

def chat_with_bot(message, user_id="example_user"):
    """
    Envoie un message au chatbot et affiche la réponse.
    
    Args:
        message: Le message à envoyer au chatbot
        user_id: Identifiant de l'utilisateur (pour la personnalisation)
    
    Returns:
        La réponse du chatbot
    """
    url = f"{CHATBOT_API_URL}/chat"
    
    payload = {
        "messages": [
            {
                "role": "user",
                "content": message
            }
        ],
        "user_id": user_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()  # Lever une exception si la requête a échoué
        
        result = response.json()
        
        print(f"\nVous: {message}")
        print(f"Chatbot: {result['content']}")
        print(f"Intent détecté: {result['intent']}")
        print(f"Entités: {json.dumps(result['entities'], indent=2, ensure_ascii=False)}")
        
        return result
        
    except requests.exceptions.RequestException as e:
        print(f"Erreur lors de la communication avec le chatbot: {e}")
        return None

def interactive_chat():
    """
    Lance une session de chat interactive avec le chatbot.
    """
    print("=== Chatbot Retreat And Be ===")
    print("Tapez 'exit' ou 'quit' pour quitter.")
    
    user_id = input("Entrez votre identifiant utilisateur (ou laissez vide pour 'guest'): ").strip() or "guest"
    
    while True:
        message = input("\nVous: ").strip()
        
        if message.lower() in ["exit", "quit", "q", "bye"]:
            print("Au revoir!")
            break
            
        if not message:
            continue
            
        chat_with_bot(message, user_id)

def demo_chat():
    """
    Démontre quelques exemples de conversation avec le chatbot.
    """
    print("=== Démonstration du Chatbot Retreat And Be ===\n")
    
    examples = [
        "Bonjour, comment ça va?",
        "Je cherche une retraite de yoga en France",
        "Quels sont les prix de vos retraites?",
        "Comment puis-je annuler ma réservation?",
        "Proposez-vous des retraites de méditation?",
        "Merci pour votre aide!"
    ]
    
    for example in examples:
        chat_with_bot(example)
        input("\nAppuyez sur Entrée pour continuer...")

if __name__ == "__main__":
    # Vérifier si le service est disponible
    try:
        health_check = requests.get(f"{CHATBOT_API_URL}/health")
        if health_check.status_code == 200:
            print(f"Le service chatbot est disponible: {health_check.json()}")
        else:
            print(f"Le service chatbot a répondu avec le code {health_check.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Le service chatbot n'est pas accessible: {e}")
        print(f"Assurez-vous que le service est en cours d'exécution sur {CHATBOT_API_URL}")
        exit(1)
    
    # Choisir le mode
    print("\nChoisissez une option:")
    print("1. Mode interactif")
    print("2. Mode démonstration")
    
    choice = input("Votre choix (1/2): ").strip()
    
    if choice == "1":
        interactive_chat()
    elif choice == "2":
        demo_chat()
    else:
        print("Choix non valide. Au revoir!")
