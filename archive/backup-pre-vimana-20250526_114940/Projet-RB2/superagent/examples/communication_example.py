#!/usr/bin/env python3
"""
Exemple d'utilisation du service de communication.
"""
import logging
import sys
import json
from services.communication import CommunicationService

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_services_health():
    """Vérifie la santé des services."""
    logger.info("Vérification de la santé des services...")
    
    agent_rb_health = CommunicationService.check_agent_rb_health()
    agent_ia_health = CommunicationService.check_agent_ia_health()
    
    logger.info(f"Santé de Agent-RB: {'OK' if agent_rb_health else 'KO'}")
    logger.info(f"Santé de Agent IA: {'OK' if agent_ia_health else 'KO'}")
    
    return agent_rb_health and agent_ia_health

def get_retreats_example():
    """Exemple de récupération des retraites."""
    logger.info("Récupération des retraites...")
    
    filters = {
        "type": "yoga",
        "location": "France"
    }
    
    try:
        result = CommunicationService.get_retreats(filters)
        logger.info(f"Retraites récupérées avec succès: {json.dumps(result, indent=2)}")
        return result.get("data", [])
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des retraites: {str(e)}")
        return []

def get_retreat_details_example(retreat_id):
    """Exemple de récupération des détails d'une retraite."""
    logger.info(f"Récupération des détails de la retraite {retreat_id}...")
    
    try:
        result = CommunicationService.get_retreat(retreat_id)
        logger.info(f"Détails de la retraite récupérés avec succès: {json.dumps(result, indent=2)}")
        return result.get("data", {})
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des détails de la retraite: {str(e)}")
        return {}

def get_partners_example():
    """Exemple de récupération des partenaires."""
    logger.info("Récupération des partenaires...")
    
    try:
        result = CommunicationService.get_partners()
        logger.info(f"Partenaires récupérés avec succès: {json.dumps(result, indent=2)}")
        return result.get("data", [])
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des partenaires: {str(e)}")
        return []

def analyze_text_example():
    """Exemple d'analyse de texte avec Agent IA."""
    logger.info("Analyse de texte avec Agent IA...")
    
    text = "Je cherche une retraite de yoga en Provence pour 7 jours en juillet pour 10 personnes."
    
    try:
        result = CommunicationService.analyze_text(text)
        logger.info(f"Analyse de texte réussie: {json.dumps(result, indent=2)}")
        return result
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse de texte: {str(e)}")
        return None

def main():
    """Fonction principale."""
    logger.info("Démarrage de l'exemple de communication...")
    
    # Vérification de la santé des services
    if not check_services_health():
        logger.error("Les services ne sont pas en bonne santé. Arrêt de l'exemple.")
        sys.exit(1)
    
    # Récupération des retraites
    retreats = get_retreats_example()
    if retreats:
        # Récupération des détails d'une retraite
        retreat_id = retreats[0].get("id")
        retreat_details = get_retreat_details_example(retreat_id)
        if retreat_details:
            logger.info(f"Nom de la retraite: {retreat_details.get('name')}")
            logger.info(f"Description: {retreat_details.get('description')}")
    
    # Récupération des partenaires
    partners = get_partners_example()
    if partners:
        for partner in partners:
            logger.info(f"Partenaire: {partner.get('name')} - Type: {partner.get('type')}")
    
    # Analyse de texte avec Agent IA
    analysis = analyze_text_example()
    if analysis:
        logger.info(f"Entités détectées: {analysis.get('data', {}).get('entities', {})}")
    
    logger.info("Exemple de communication terminé.")

if __name__ == "__main__":
    main()
