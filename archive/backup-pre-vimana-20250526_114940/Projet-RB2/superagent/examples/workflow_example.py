"""
Example script demonstrating how to use the workflow engine.
"""

import asyncio
import json
from datetime import datetime
import logging

from superagent.graph.workflow_engine import WorkflowEngine
from superagent.graph.types import State

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def run_example():
    """
    Run the example workflow using the workflow engine.
    """
    print("Starting workflow engine example...")

    # Create a workflow engine
    engine = WorkflowEngine()

    # Create a task for code generation
    task = {
        "title": "Generate a Python function to calculate Fibonacci numbers",
        "description": "Create a Python function that calculates the nth Fibonacci number using an efficient algorithm.",
        "type": "code_generation",
        "language": "python",
        "generate_tests": True
    }

    # Create a workflow
    workflow_id = await engine.create_workflow(task)
    print(f"Created workflow with ID: {workflow_id}")

    # Execute the workflow
    print("Executing workflow...")
    workflow = await engine.execute_workflow(workflow_id)

    # Check the workflow status
    print(f"Workflow status: {workflow['status']}")

    # Get the results
    if workflow["status"] == "completed":
        results = await engine.get_workflow_results(workflow_id)

        # Print the code results
        if "code" in results["results"]:
            code_results = results["results"]["code"]
            print("\nGenerated Code:")
            print("=" * 50)

            for file in code_results.get("files", []):
                print(f"\nFile: {file.get('name')}")
                print("-" * 50)
                print(file.get("content"))

            if "tests" in code_results:
                print("\nGenerated Tests:")
                print("=" * 50)

                for test in code_results.get("tests", []):
                    print(f"\nTest: {test.get('name')}")
                    print("-" * 50)
                    print(test.get("content"))

        # Print the report results
        if "report" in results["results"]:
            report_results = results["results"]["report"]
            print("\nGenerated Report:")
            print("=" * 50)
            print(f"Title: {report_results.get('title')}")
            print(f"Summary: {report_results.get('summary')}")

            if "content" in report_results:
                print("\nReport Content:")
                print("-" * 50)
                print(report_results.get("content"))
    else:
        print(f"Workflow failed with error: {workflow['state'].error}")

    # Add feedback
    feedback = {
        "rating": 5,
        "comment": "The generated code works perfectly!",
        "improvements": [
            "Could add more comments to explain the algorithm",
            "Could add type hints to the function"
        ]
    }

    await engine.add_feedback(workflow_id, feedback)
    print("\nFeedback added to workflow")

    # List all workflows
    workflows = await engine.list_workflows()
    print(f"\nTotal workflows: {len(workflows)}")

    print("\nWorkflow engine example completed!")

async def run_basic_example():
    """
    Run a basic example using the direct workflow API.
    """
    print("Starting basic workflow example...")

    # Create an initial state with a specific task
    initial_state = State(
        task={
            "id": "example_task_001",
            "type": "code_analysis",
            "description": "Analyze a Python codebase for security vulnerabilities",
            "priority": "high"
        },
        context={
            "user": {
                "id": "user_123",
                "name": "Example User"
            },
            "project": {
                "id": "project_456",
                "name": "Security Analysis Project"
            }
        }
    )

    # Run the workflow using the direct API
    from superagent.graph.workflow import run_workflow
    final_state = await run_workflow(initial_state)

    # Print the results
    print("\nBasic workflow completed!")
    print(f"Final state: next={final_state.next}")
    print("\nHistory:")
    for entry in final_state.history:
        print(f"  - {entry['agent']}: {entry['action']}")

    print("\nReport:")
    report = final_state.results.get("report", {})
    if report:
        print(f"  Title: {report.get('title')}")
        print(f"  Summary: {report.get('summary')}")
        print("  Sections:")
        for section in report.get("sections", []):
            print(f"    - {section.get('title')}")
        print(f"  Conclusion: {report.get('conclusion')}")
    else:
        print("  No report generated")

async def main():
    """
    Run all examples.
    """
    # Run the workflow engine example
    await run_example()

    print("\n" + "=" * 80 + "\n")

    # Run the basic workflow example
    await run_basic_example()

if __name__ == "__main__":
    asyncio.run(main())
