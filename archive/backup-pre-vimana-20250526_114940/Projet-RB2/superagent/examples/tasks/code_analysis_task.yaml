id: code_analysis_001
type: code_analysis
description: Analyze a Python codebase for security vulnerabilities
priority: high

parameters:
  language: python
  focus_areas:
    - input_validation
    - authentication
    - authorization
    - data_protection
  
  repository:
    url: https://github.com/example/repo
    branch: main
    
  files:
    - path: src/auth/
      recursive: true
    - path: src/api/endpoints.py
    - path: src/database/models.py
