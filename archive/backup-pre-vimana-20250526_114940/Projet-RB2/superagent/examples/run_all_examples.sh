#!/bin/bash
# Script pour exécuter tous les exemples de workflows

# Créer le répertoire pour les résultats s'il n'existe pas
mkdir -p src/examples/results

echo "=== Exécution du workflow de planification de retraites ==="
python -m src.examples.retreat_workflow_example

echo -e "\n\n=== Exécution du workflow de mise en relation de partenaires ==="
python -m src.examples.partner_workflow_example

echo -e "\n\n=== Exécution du workflow d'assistance client ==="
python -m src.examples.client_workflow_example

echo -e "\n\nTous les exemples ont été exécutés avec succès !"
echo "Les résultats sont disponibles dans le répertoire src/examples/results/"
