import React, { useState } from 'react';
import {
  // Atoms
  Button,
  Text,
  Title,
  Input,
  Label,
  Icon,
  Badge,
  Avatar,
  Spinner,
  Image,
  
  // Molecules
  Card,
  FormField,
  SearchBar,
  Notification,
  MenuItem,
  Price,
  TabGroup,
  Breadcrumb,
  
  // Organisms
  Header,
  Footer,
  Sidebar,
  Navigation,
  SearchSection,
  RetreatCard,
  BookingForm,
  Hero,
  LoginForm,
  NotificationCenter,
  PaymentForm,
  RetreatFilters,
  ReviewForm,
  UserProfile,
  
  // Templates
  MainLayout,
  DashboardLayout,
  AuthLayout,
  ProfileLayout,
  RetreatLayout
} from '@/atomic';

// Icônes
import { 
  User, 
  Settings, 
  Clock, 
  Calendar, 
  CreditCard, 
  Heart, 
  Search, 
  Menu, 
  Bell, 
  LogOut, 
  Check, 
  X, 
  Info, 
  AlertTriangle 
} from 'lucide-react';

/**
 * Page de démonstration qui présente tous les composants disponibles
 * dans la bibliothèque de composants R&B new
 */
export const ComponentShowcasePage: React.FC = () => {
  // États pour les démos interactives
  const [count, setCount] = useState(0);
  const [inputValue, setInputValue] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [showNotification, setShowNotification] = useState(false);
  const [showModal, setShowModal] = useState(false);

  // Données de démonstration
  const sampleRetreat = {
    id: 'retreat-123',
    title: 'Retraite Yoga & Méditation',
    location: 'Provence, France',
    duration: '7 jours',
    price: 1200,
    rating: 4.8,
    image: '/images/sample-retreat.jpg',
    tags: ['yoga', 'méditation', 'nature'],
    description: 'Une semaine de reconnexion à soi dans un cadre idyllique.'
  };

  // Fonctions de démonstration
  const handleButtonClick = () => {
    setCount(prev => prev + 1);
    setShowNotification(true);
    setTimeout(() => setShowNotification(false), 3000);
  };

  return (
    <MainLayout
      meta={{
        title: 'Démonstration des Composants | R&B new',
        description: 'Page de démonstration de tous les composants disponibles dans R&B new'
      }}
    >
      <div className="max-w-7xl mx-auto px-4 py-8">
        <Title level={1} className="mb-8">Démonstration des Composants R&B new</Title>
        <Text className="mb-12">Cette page présente tous les composants disponibles dans la bibliothèque de composants R&B new, organisés par catégories selon la méthodologie Atomic Design.</Text>

        {/* Section Atoms */}
        <section className="mb-16">
          <Title level={2} className="mb-6 pb-2 border-b">Atoms (Composants Atomiques)</Title>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            
            {/* Buttons */}
            <Card className="p-6">
              <Title level={3} className="mb-4">Buttons</Title>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Button variant="primary">Primary</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="text">Text</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button size="sm">Small</Button>
                  <Button size="md">Medium</Button>
                  <Button size="lg">Large</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button disabled>Disabled</Button>
                  <Button loading>Loading</Button>
                  <Button fullWidth>Full Width</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button icon={<Heart />}>With Icon</Button>
                  <Button icon={<Heart />} variant="outline" />
                </div>
              </div>
            </Card>

            {/* Typography */}
            <Card className="p-6">
              <Title level={3} className="mb-4">Typography</Title>
              <div className="space-y-4">
                <Title level={1}>Heading 1</Title>
                <Title level={2}>Heading 2</Title>
                <Title level={3}>Heading 3</Title>
                <Title level={4}>Heading 4</Title>
                <Title level={5}>Heading 5</Title>
                <Title level={6}>Heading 6</Title>
                <Text>Regular text paragraph</Text>
                <Text size="sm">Small text</Text>
                <Text size="lg">Large text</Text>
                <Text weight="bold">Bold text</Text>
                <Text variant="secondary">Secondary text</Text>
              </div>
            </Card>

            {/* Inputs */}
            <Card className="p-6">
              <Title level={3} className="mb-4">Inputs</Title>
              <div className="space-y-4">
                <Input 
                  placeholder="Text input" 
                  value={inputValue} 
                  onChange={(e) => setInputValue(e.target.value)} 
                />
                <Input 
                  placeholder="With label" 
                  label="Email" 
                  type="email" 
                />
                <Input 
                  placeholder="With helper text" 
                  helperText="This is a helper text" 
                />
                <Input 
                  placeholder="With error" 
                  error="This field is required" 
                />
                <Input 
                  placeholder="With icon" 
                  icon={<Search />} 
                />
                <Input 
                  placeholder="Disabled" 
                  disabled 
                />
              </div>
            </Card>

            {/* Icons */}
            <Card className="p-6">
              <Title level={3} className="mb-4">Icons</Title>
              <div className="grid grid-cols-4 gap-4">
                <div className="flex flex-col items-center">
                  <Icon icon={<User />} />
                  <Text size="sm">User</Text>
                </div>
                <div className="flex flex-col items-center">
                  <Icon icon={<Settings />} />
                  <Text size="sm">Settings</Text>
                </div>
                <div className="flex flex-col items-center">
                  <Icon icon={<Heart />} />
                  <Text size="sm">Heart</Text>
                </div>
                <div className="flex flex-col items-center">
                  <Icon icon={<Calendar />} />
                  <Text size="sm">Calendar</Text>
                </div>
                <div className="flex flex-col items-center">
                  <Icon icon={<Clock />} />
                  <Text size="sm">Clock</Text>
                </div>
                <div className="flex flex-col items-center">
                  <Icon icon={<Bell />} />
                  <Text size="sm">Bell</Text>
                </div>
                <div className="flex flex-col items-center">
                  <Icon icon={<Search />} />
                  <Text size="sm">Search</Text>
                </div>
                <div className="flex flex-col items-center">
                  <Icon icon={<Menu />} />
                  <Text size="sm">Menu</Text>
                </div>
              </div>
            </Card>

            {/* Badges */}
            <Card className="p-6">
              <Title level={3} className="mb-4">Badges</Title>
              <div className="flex flex-wrap gap-2">
                <Badge>Default</Badge>
                <Badge variant="primary">Primary</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="success">Success</Badge>
                <Badge variant="warning">Warning</Badge>
                <Badge variant="danger">Danger</Badge>
                <Badge variant="info">Info</Badge>
                <Badge icon={<Check />}>With Icon</Badge>
                <Badge count={5}>Count</Badge>
              </div>
            </Card>

            {/* Avatars */}
            <Card className="p-6">
              <Title level={3} className="mb-4">Avatars</Title>
              <div className="flex flex-wrap gap-4">
                <Avatar size="sm" src="/images/avatar-1.jpg" alt="User 1" />
                <Avatar size="md" src="/images/avatar-2.jpg" alt="User 2" />
                <Avatar size="lg" src="/images/avatar-3.jpg" alt="User 3" />
                <Avatar initials="JD" />
                <Avatar icon={<User />} />
                <Avatar status="online" src="/images/avatar-4.jpg" alt="User 4" />
                <Avatar status="busy" src="/images/avatar-5.jpg" alt="User 5" />
                <Avatar status="away" src="/images/avatar-6.jpg" alt="User 6" />
              </div>
            </Card>

            {/* Spinners */}
            <Card className="p-6">
              <Title level={3} className="mb-4">Spinners</Title>
              <div className="flex flex-wrap gap-4 items-center">
                <Spinner size="sm" />
                <Spinner size="md" />
                <Spinner size="lg" />
                <Spinner variant="primary" />
                <Spinner variant="secondary" />
                <Spinner variant="light" />
              </div>
            </Card>
          </div>
        </section>

        {/* Section Molecules */}
        <section className="mb-16">
          <Title level={2} className="mb-6 pb-2 border-b">Molecules (Composants Moléculaires)</Title>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            
            {/* Cards */}
            <div>
              <Title level={3} className="mb-4">Cards</Title>
              <div className="space-y-4">
                <Card className="p-6">
                  <Title level={4} className="mb-2">Simple Card</Title>
                  <Text className="mb-4">This is a simple card with some content.</Text>
                  <Button variant="primary">Action</Button>
                </Card>
                
                <Card className="overflow-hidden">
                  <Image src="/images/card-image.jpg" alt="Card header" className="w-full h-48 object-cover" />
                  <div className="p-6">
                    <Title level={4} className="mb-2">Card with Image</Title>
                    <Text className="mb-4">This card includes an image at the top.</Text>
                    <div className="flex justify-between">
                      <Button variant="outline">Cancel</Button>
                      <Button variant="primary">Submit</Button>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            {/* Form Fields */}
            <div>
              <Title level={3} className="mb-4">Form Fields</Title>
              <div className="space-y-4">
                <FormField
                  label="Username"
                  name="username"
                  placeholder="Enter your username"
                  helperText="Your unique identifier"
                />
                
                <FormField
                  label="Email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  required
                />
                
                <FormField
                  label="Password"
                  name="password"
                  type="password"
                  placeholder="Enter your password"
                  error="Password must be at least 8 characters"
                />
                
                <FormField
                  label="Bio"
                  name="bio"
                  type="textarea"
                  placeholder="Tell us about yourself"
                  rows={3}
                />
              </div>
            </div>

            {/* Search Bar */}
            <div>
              <Title level={3} className="mb-4">Search Bar</Title>
              <SearchBar 
                placeholder="Search for retreats..." 
                onSearch={(value) => console.log('Searching for:', value)}
                suggestions={['Yoga', 'Méditation', 'Bien-être', 'Nature']}
              />
            </div>

            {/* Notifications */}
            <div>
              <Title level={3} className="mb-4">Notifications</Title>
              <div className="space-y-4">
                <Notification
                  title="Success"
                  message="Your profile has been updated successfully."
                  variant="success"
                  icon={<Check />}
                  onClose={() => console.log('Notification closed')}
                />
                
                <Notification
                  title="Error"
                  message="There was an error processing your request."
                  variant="error"
                  icon={<X />}
                  onClose={() => console.log('Notification closed')}
                />
                
                <Notification
                  title="Info"
                  message="Your booking is confirmed for July 15th."
                  variant="info"
                  icon={<Info />}
                  onClose={() => console.log('Notification closed')}
                />
                
                <Notification
                  title="Warning"
                  message="Your session will expire in 5 minutes."
                  variant="warning"
                  icon={<AlertTriangle />}
                  onClose={() => console.log('Notification closed')}
                />
              </div>
            </div>

            {/* Tab Group */}
            <div>
              <Title level={3} className="mb-4">Tab Group</Title>
              <TabGroup
                tabs={[
                  { label: 'Overview', icon: <Info /> },
                  { label: 'Details', icon: <List /> },
                  { label: 'Reviews', icon: <MessageSquare /> },
                  { label: 'Related', icon: <Link /> }
                ]}
                activeTab={activeTab}
                onChange={setActiveTab}
              />
              <div className="p-4 border rounded-b-lg">
                {activeTab === 0 && <div>Overview Content</div>}
                {activeTab === 1 && <div>Details Content</div>}
                {activeTab === 2 && <div>Reviews Content</div>}
                {activeTab === 3 && <div>Related Content</div>}
              </div>
            </div>

            {/* Breadcrumb */}
            <div>
              <Title level={3} className="mb-4">Breadcrumb</Title>
              <Breadcrumb
                items={[
                  { label: 'Home', href: '/' },
                  { label: 'Retreats', href: '/retreats' },
                  { label: 'Yoga', href: '/retreats/yoga' },
                  { label: 'Provence', href: '/retreats/yoga/provence' }
                ]}
              />
            </div>
          </div>
        </section>

        {/* Section Organisms */}
        <section className="mb-16">
          <Title level={2} className="mb-6 pb-2 border-b">Organisms (Composants Organismiques)</Title>
          
          {/* Header */}
          <div className="mb-8">
            <Title level={3} className="mb-4">Header</Title>
            <Header
              logo="/images/logo.svg"
              navigation={[
                { label: 'Home', href: '/' },
                { label: 'Retreats', href: '/retreats' },
                { label: 'About', href: '/about' },
                { label: 'Contact', href: '/contact' }
              ]}
              actions={[
                { label: 'Login', href: '/login', variant: 'outline' },
                { label: 'Sign Up', href: '/signup', variant: 'primary' }
              ]}
            />
          </div>

          {/* Hero */}
          <div className="mb-8">
            <Title level={3} className="mb-4">Hero</Title>
            <Hero
              title="Discover Your Perfect Retreat"
              subtitle="Find peace, wellness and connection with our curated retreat experiences"
              image="/images/hero-bg.jpg"
              cta={{ label: 'Explore Retreats', href: '/retreats' }}
            />
          </div>

          {/* Retreat Card */}
          <div className="mb-8">
            <Title level={3} className="mb-4">Retreat Card</Title>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <RetreatCard
                id={sampleRetreat.id}
                title={sampleRetreat.title}
                location={sampleRetreat.location}
                duration={sampleRetreat.duration}
                price={sampleRetreat.price}
                rating={sampleRetreat.rating}
                image={sampleRetreat.image}
                tags={sampleRetreat.tags}
              />
              <RetreatCard
                id="retreat-456"
                title="Retraite Détox & Bien-être"
                location="Alpes, Suisse"
                duration="5 jours"
                price={950}
                rating={4.6}
                image="/images/sample-retreat-2.jpg"
                tags={['détox', 'bien-être', 'montagne']}
                featured
              />
              <RetreatCard
                id="retreat-789"
                title="Retraite Méditation Pleine Conscience"
                location="Bali, Indonésie"
                duration="10 jours"
                price={1800}
                rating={4.9}
                image="/images/sample-retreat-3.jpg"
                tags={['méditation', 'pleine conscience', 'spiritualité']}
                discount={15}
              />
            </div>
          </div>

          {/* Search Section */}
          <div className="mb-8">
            <Title level={3} className="mb-4">Search Section</Title>
            <SearchSection
              title="Trouvez votre prochaine retraite"
              filters={[
                { type: 'location', label: 'Destination', options: ['France', 'Espagne', 'Italie', 'Suisse', 'Bali'] },
                { type: 'category', label: 'Type', options: ['Yoga', 'Méditation', 'Détox', 'Bien-être', 'Spirituel'] },
                { type: 'duration', label: 'Durée', options: ['Weekend', '1 semaine', '2 semaines', 'Plus'] },
                { type: 'price', label: 'Budget', options: ['Économique', 'Modéré', 'Premium', 'Luxe'] }
              ]}
              onSearch={(filters) => console.log('Search with filters:', filters)}
            />
          </div>

          {/* Forms */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <Title level={3} className="mb-4">Login Form</Title>
              <LoginForm
                onSubmit={(data) => console.log('Login data:', data)}
                onForgotPassword={() => console.log('Forgot password')}
                onRegister={() => console.log('Register')}
                socialLogins={['google', 'facebook', 'apple']}
              />
            </div>
            
            <div>
              <Title level={3} className="mb-4">Booking Form</Title>
              <BookingForm
                retreatId={sampleRetreat.id}
                retreatTitle={sampleRetreat.title}
                availableDates={[
                  { startDate: '2023-07-15', endDate: '2023-07-22', price: 1200, availability: 5 },
                  { startDate: '2023-08-05', endDate: '2023-08-12', price: 1300, availability: 3 },
                  { startDate: '2023-09-10', endDate: '2023-09-17', price: 1150, availability: 8 }
                ]}
                onSubmit={(data) => console.log('Booking data:', data)}
              />
            </div>
          </div>

          {/* Footer */}
          <div className="mb-8">
            <Title level={3} className="mb-4">Footer</Title>
            <Footer
              logo="/images/logo.svg"
              tagline="Discover wellness retreats for mind, body and soul"
              links={[
                {
                  title: 'Company',
                  items: [
                    { label: 'About Us', href: '/about' },
                    { label: 'Our Team', href: '/team' },
                    { label: 'Careers', href: '/careers' },
                    { label: 'Contact', href: '/contact' }
                  ]
                },
                {
                  title: 'Retreats',
                  items: [
                    { label: 'Yoga', href: '/retreats/yoga' },
                    { label: 'Meditation', href: '/retreats/meditation' },
                    { label: 'Wellness', href: '/retreats/wellness' },
                    { label: 'Spiritual', href: '/retreats/spiritual' }
                  ]
                },
                {
                  title: 'Support',
                  items: [
                    { label: 'FAQ', href: '/faq' },
                    { label: 'Terms', href: '/terms' },
                    { label: 'Privacy', href: '/privacy' },
                    { label: 'Cookies', href: '/cookies' }
                  ]
                }
              ]}
              social={[
                { platform: 'facebook', url: 'https://facebook.com/retreatandbe' },
                { platform: 'instagram', url: 'https://instagram.com/retreatandbe' },
                { platform: 'twitter', url: 'https://twitter.com/retreatandbe' },
                { platform: 'youtube', url: 'https://youtube.com/retreatandbe' }
              ]}
              newsletter={{
                title: 'Subscribe to our newsletter',
                description: 'Get the latest news and offers',
                placeholder: 'Your email address',
                buttonText: 'Subscribe'
              }}
              copyright="© 2023 Retreat And Be. All rights reserved."
            />
          </div>
        </section>

        {/* Interactive Demo */}
        <section className="mb-16">
          <Title level={2} className="mb-6 pb-2 border-b">Démo Interactive</Title>
          <Card className="p-6">
            <Title level={3} className="mb-4">Compteur Interactif</Title>
            <div className="flex flex-col items-center">
              <Text size="lg" className="mb-4">Vous avez cliqué {count} fois</Text>
              <Button 
                variant="primary" 
                size="lg" 
                onClick={handleButtonClick}
                className="mb-4"
              >
                Cliquez-moi
              </Button>
              
              {showNotification && (
                <Notification
                  title="Compteur incrémenté"
                  message={`Le compteur est maintenant à ${count}`}
                  variant="success"
                  icon={<Check />}
                  onClose={() => setShowNotification(false)}
                  className="mt-4"
                />
              )}
            </div>
          </Card>
        </section>
      </div>
    </MainLayout>
  );
};

export default ComponentShowcasePage;
