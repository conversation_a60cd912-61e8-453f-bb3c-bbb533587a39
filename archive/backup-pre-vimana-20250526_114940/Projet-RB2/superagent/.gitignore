# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
logs/
*.log

# Environment variables
.env

# Models
models/

# Data
data/
*.csv
*.json
*.pkl

# Jupyter Notebooks
.ipynb_checkpoints
*.ipynb

# Testing
.coverage
htmlcov/
.pytest_cache/

# Documentation
docs/_build/

# Docker
.docker/

# Temporary files
tmp/
temp/
