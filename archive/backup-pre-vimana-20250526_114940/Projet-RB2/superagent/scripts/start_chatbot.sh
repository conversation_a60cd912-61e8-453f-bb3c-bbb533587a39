#!/bin/bash

# Script pour démarrer le service chatbot

# Définir les couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Afficher un message de bienvenue
echo -e "${GREEN}=== Démarrage du service chatbot Retreat And Be ===${NC}"

# Vérifier si Python est installé
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Erreur: Python 3 n'est pas installé.${NC}"
    exit 1
fi

# Vérifier si pip est installé
if ! command -v pip3 &> /dev/null; then
    echo -e "${RED}Erreur: pip3 n'est pas installé.${NC}"
    exit 1
fi

# Vérifier si uvicorn est installé
if ! command -v uvicorn &> /dev/null; then
    echo -e "${YELLOW}Avertissement: uvicorn n'est pas installé. Installation en cours...${NC}"
    pip3 install uvicorn
fi

# Vérifier si le fichier .env existe
if [ ! -f "$(dirname "$0")/../.env" ]; then
    echo -e "${YELLOW}Avertissement: Fichier .env non trouvé. Création d'un fichier .env par défaut...${NC}"
    cp "$(dirname "$0")/../.env.example" "$(dirname "$0")/../.env"
    echo -e "${YELLOW}Veuillez configurer le fichier .env avec vos propres valeurs.${NC}"
fi

# Aller dans le répertoire du chatbot
cd "$(dirname "$0")/../ai_engine/chatbot" || exit

# Vérifier si les dépendances sont installées
echo -e "${GREEN}Installation des dépendances...${NC}"
pip3 install -r requirements.txt

# Démarrer le service chatbot
echo -e "${GREEN}Démarrage du service chatbot sur le port 8001...${NC}"
echo -e "${YELLOW}Accédez à l'API à l'adresse: http://localhost:8001${NC}"
echo -e "${YELLOW}Documentation de l'API: http://localhost:8001/docs${NC}"
echo -e "${YELLOW}Utilisez Ctrl+C pour arrêter le service${NC}"
echo -e "${GREEN}===================================================${NC}"

uvicorn main:app --host 0.0.0.0 --port 8001 --reload
