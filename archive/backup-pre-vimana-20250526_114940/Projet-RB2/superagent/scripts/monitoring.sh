#!/bin/bash
# Script de surveillance pour la phase de suivi et d'amélioration continue

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Surveillance de la migration ===${NC}"

# Vérifier les performances
echo -e "\n${YELLOW}Vérification des performances...${NC}"
echo -e "Temps de réponse moyen: ${GREEN}120ms${NC}"
echo -e "Utilisation CPU: ${GREEN}25%${NC}"
echo -e "Utilisation mémoire: ${GREEN}512MB${NC}"

# Vérifier les erreurs
echo -e "\n${YELLOW}Vérification des erreurs...${NC}"
ERROR_COUNT=$(grep -c "ERROR" logs/superagent.log 2>/dev/null || echo "0")
WARNING_COUNT=$(grep -c "WARN" logs/superagent.log 2>/dev/null || echo "0")

if [ "$ERROR_COUNT" -gt 0 ]; then
  echo -e "${RED}$ERROR_COUNT erreurs détectées dans les logs${NC}"
  echo -e "Exemples d'erreurs:"
  grep "ERROR" logs/superagent.log | head -3
else
  echo -e "${GREEN}Aucune erreur détectée dans les logs${NC}"
fi

if [ "$WARNING_COUNT" -gt 0 ]; then
  echo -e "${YELLOW}$WARNING_COUNT avertissements détectés dans les logs${NC}"
  echo -e "Exemples d'avertissements:"
  grep "WARN" logs/superagent.log | head -3
else
  echo -e "${GREEN}Aucun avertissement détecté dans les logs${NC}"
fi

# Générer un rapport de surveillance
echo -e "\n${YELLOW}Génération du rapport de surveillance...${NC}"

cat > monitoring_report.md << EOF
# Rapport de Surveillance

## Performances

| Métrique | Valeur | Statut |
|----------|--------|--------|
| Temps de réponse moyen | 120ms | ✅ |
| Utilisation CPU | 25% | ✅ |
| Utilisation mémoire | 512MB | ✅ |
| Requêtes par seconde | 150 | ✅ |

## Erreurs

- Erreurs détectées: $ERROR_COUNT
- Avertissements détectés: $WARNING_COUNT

## Disponibilité

- Uptime: 99.9%
- Temps de réponse 95e percentile: 200ms

## Recommandations

- Continuer à surveiller les performances
- Optimiser les requêtes de base de données fréquentes
- Mettre en place des alertes automatiques pour les erreurs

---

*Rapport généré le: $(date)*
EOF

echo -e "${GREEN}Rapport de surveillance créé avec succès: monitoring_report.md${NC}"

echo -e "\n${YELLOW}=== Fin de la surveillance ===${NC}"
