#!/bin/bash

# Démarrer l'API en arrière-plan
echo "Starting API server..."
cd "$(dirname "$0")"
python3 -m api.main &
API_PID=$!

# Démarrer le chatbot en arrière-plan
echo "Starting chatbot service..."
./scripts/start_chatbot.sh &
CHATBOT_PID=$!

# Attendre que les services soient prêts
echo "Waiting for services to start..."
sleep 5

# Démarrer l'interface utilisateur
echo "Starting frontend..."
cd frontend
npm start

# Arrêter les services lorsque l'interface utilisateur est arrêtée
kill $API_PID
kill $CHATBOT_PID
