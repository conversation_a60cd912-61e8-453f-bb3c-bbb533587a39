module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    },
    project: ['./tsconfig.json', './*/tsconfig.json', './*/*/tsconfig.json']
  },
  plugins: ['@typescript-eslint', 'prettier'],
  rules: {
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    'prettier/prettier': 'error',
    'no-extra-semi': 'error',
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    'object-curly-spacing': ['error', 'always'],
    'brace-style': ['error', '1tbs'],
    'no-multiple-empty-lines': ['error', { 'max': 1 }],
    // Règles pour éviter les erreurs de syntaxe que nous avons rencontrées
    'no-unexpected-multiline': 'error',
    'no-unreachable': 'error',
    'no-unsafe-finally': 'error',
    'no-unsafe-negation': 'error',
    'valid-typeof': 'error',
    'no-sparse-arrays': 'error',
    'no-regex-spaces': 'error',
    'no-irregular-whitespace': 'error',
    'no-invalid-regexp': 'error',
    'no-func-assign': 'error',
    'no-extra-boolean-cast': 'error',
    'no-duplicate-case': 'error',
    'no-dupe-keys': 'error',
    'no-dupe-args': 'error',
    'no-constant-condition': 'error',
    'no-cond-assign': 'error',
    'no-compare-neg-zero': 'error',
    '@typescript-eslint/member-delimiter-style': ['error', {
      'multiline': {
        'delimiter': 'semi',
        'requireLast': true
      },
      'singleline': {
        'delimiter': 'semi',
        'requireLast': false
      }
    }],
    '@typescript-eslint/type-annotation-spacing': 'error',
    '@typescript-eslint/ban-ts-comment': 'warn',
    '@typescript-eslint/prefer-ts-expect-error': 'warn',
    '@typescript-eslint/no-unnecessary-type-assertion': 'error',
    'no-duplicate-imports': 'error',
    'no-var': 'error',
    'prefer-const': 'error',
    'quote-props': ['error', 'as-needed']
  },
  overrides: [
    {
      files: ['*.tsx', '*.jsx'],
      excludedFiles: ['Front-Audrey-V1-Main-main/**/*.ts', 'Front-Audrey-V1-Main-main/**/*.tsx'],
      extends: [
        'plugin:react/recommended',
        'plugin:react-hooks/recommended'
      ],
      plugins: ['react', 'react-hooks'],
      rules: {
        'react/react-in-jsx-scope': 'off',
        'react/prop-types': 'off',
        'react-hooks/rules-of-hooks': 'error',
        'react-hooks/exhaustive-deps': 'warn',
        'react/no-unescaped-entities': 'error',
        'react/jsx-curly-spacing': ['error', {'when': 'never', 'children': true}],
        'react/jsx-closing-bracket-location': 'error',
        'react/jsx-closing-tag-location': 'error',
        'react/self-closing-comp': 'error'
      },
      settings: {
        react: {
          version: 'detect'
        }
      }
    },
    {
      files: ['*.ts', '*.tsx'],
      rules: {
        '@typescript-eslint/no-empty-interface': 'warn',
        '@typescript-eslint/no-non-null-assertion': 'warn',
        '@typescript-eslint/consistent-type-definitions': ['error', 'interface'],
        '@typescript-eslint/consistent-type-imports': 'error'
      }
    },
    {
      files: ['mobile/**/*.ts', 'mobile/**/*.tsx'],
      rules: {
        'no-extra-semi': 'error',
        'comma-dangle': ['error', 'never'],
        '@typescript-eslint/explicit-function-return-type': 'error'
      }
    }
  ]
};