const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get files with incorrect imports
const output = execSync('cd /Users/<USER>/Desktop/Projet-RB2/frontend && grep -l "import.*{.*Component.*}.*from.*useAuth" --include="*.ts" --include="*.tsx" -r src/', { encoding: 'utf-8' });
const files = output.split('\n').filter(Boolean);

console.log(`Found ${files.length} files with incorrect Component imports.`);

// Process each file
files.forEach(file => {
  const filePath = path.join('/Users/<USER>/Desktop/Projet-RB2/frontend', file);
  try {
    let content = fs.readFileSync(filePath, 'utf-8');
    
    // Replace the Component import with useAuth
    let updated = content.replace(/import\s*{\s*Component\s*}\s*from\s*['"](.+)\/useAuth(\.ts)?['"]/g, 
                                  (match, p1) => `import { useAuth } from "${p1}/useAuth"`);
    
    if (content !== updated) {
      fs.writeFileSync(filePath, updated);
      console.log(`Fixed imports in ${file}`);
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error.message);
  }
});

console.log('Completed fixing imports.');
