const componentTypes = ['atoms', 'molecules', 'organisms', 'templates'];

module.exports = function (plop) {
  // Générateur de composant atomique
  plop.setGenerator('atomic', {
    description: 'Création d'un composant atomique',
    prompts: [
      {
        type: 'list',
        name: 'atomicType',
        message: 'Type de composant atomique:',
        choices: componentTypes
      },
      {
        type: 'input',
        name: 'name',
        message: 'Nom du composant:'
      },
      {
        type: 'confirm',
        name: 'hasProps',
        message: 'Le composant a-t-il des props spécifiques?',
        default: true
      },
      {
        type: 'confirm',
        name: 'isStyled',
        message: 'Utiliser styled-components?',
        default: true
      },
      {
        type: 'confirm',
        name: 'withStory',
        message: 'Créer un fichier Storybook?',
        default: true
      }
    ],
    actions: function(data) {
      const actions = [];
      
      // Fichier principal du composant
      actions.push({
        type: 'add',
        path: 'frontend/src/atomic/{{atomicType}}/{{pascalCase name}}/{{pascalCase name}}.tsx',
        templateFile: 'templates/component.tsx.hbs'
      });
      
      // Types du composant
      actions.push({
        type: 'add',
        path: 'frontend/src/atomic/{{atomicType}}/{{pascalCase name}}/{{pascalCase name}}.types.ts',
        templateFile: 'templates/component.types.ts.hbs'
      });
      
      // Index pour faciliter l'import
      actions.push({
        type: 'add',
        path: 'frontend/src/atomic/{{atomicType}}/{{pascalCase name}}/index.ts',
        template: 'export { {{pascalCase name}} } from "./{{pascalCase name}}";\nexport type { {{pascalCase name}}Props } from "./{{pascalCase name}}.types";\n'
      });
      
      // Tests
      actions.push({
        type: 'add',
        path: 'frontend/src/atomic/{{atomicType}}/{{pascalCase name}}/{{pascalCase name}}.test.tsx',
        templateFile: 'templates/component.test.tsx.hbs'
      });
      
      // Story optionnel
      if (data.withStory) {
        actions.push({
          type: 'add',
          path: 'frontend/src/atomic/{{atomicType}}/{{pascalCase name}}/{{pascalCase name}}.stories.tsx',
          templateFile: 'templates/component.stories.tsx.hbs'
        });
      }
      
      return actions;
    }
  });
  
  // Générateur pour migrer un composant existant
  plop.setGenerator('migrate', {
    description: 'Migrer un composant existant vers l\'architecture atomique',
    prompts: [
      {
        type: 'input',
        name: 'sourcePath',
        message: 'Chemin du composant à migrer (depuis src/):'
      },
      {
        type: 'list',
        name: 'atomicType',
        message: 'Type de composant atomique cible:',
        choices: componentTypes
      }
    ],
    actions: [
      {
        type: 'custom',
        name: 'migrateComponent',
        description: 'Migration du composant',
        async handler(answers) {
          // Logique de migration (à implémenter)
          console.log(`Migration de ${answers.sourcePath} vers ${answers.atomicType}...`);
          return 'Migration terminée'; // Message de retour
        }
      }
    ]
  });
}; 