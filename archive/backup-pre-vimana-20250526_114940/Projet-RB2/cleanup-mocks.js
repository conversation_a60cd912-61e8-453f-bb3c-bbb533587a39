/**
 * <PERSON>ript pour nettoyer les mocks dupliqués
 * Ce script renomme les anciens fichiers mock pour éviter les conflits
 */
const fs = require('fs');
const path = require('path');

// Liste des fichiers mock à renommer
const mockFiles = [
  'frontend/src/test/__mocks__/fileMock.js',
  'frontend/src/test/__mocks__/styleMock.js',
  'frontend/src/test/__mocks__/localforage.js',
  'Backend/src/test/__mocks__/fileMock.js',
  'Backend/src/test/__mocks__/styleMock.js',
  'packages/sync/__mocks__/localforage.js'
];

// Renommer les fichiers
mockFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    const newPath = filePath + '.old';
    try {
      fs.renameSync(filePath, newPath);
      console.log(`Fichier ${filePath} renommé en ${newPath}`);
    } catch (error) {
      console.error(`Erreur lors du renommage de ${filePath}:`, error.message);
    }
  } else {
    console.log(`Fichier ${filePath} non trouvé`);
  }
});

console.log('Nettoyage des mocks terminé');
