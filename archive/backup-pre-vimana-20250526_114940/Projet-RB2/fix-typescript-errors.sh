#!/bin/bash

# Script pour corriger automatiquement les erreurs TypeScript avec Prettier

echo "Correction des erreurs TypeScript avec Prettier..."

# Correction des fichiers TypeScript dans src
npx prettier --write "frontend/src/**/*.ts" "frontend/src/**/*.tsx" "Agent IA/src/**/*.ts" "Agent IA/src/**/*.tsx" "Website-Creator/src/**/*.ts" "Website-Creator/src/**/*.tsx" "backend/**/*.ts"

# Correction des fichiers JavaScript dans src
npx prettier --write "frontend/src/**/*.js" "frontend/src/**/*.jsx" "Agent IA/src/**/*.js" "Agent IA/src/**/*.jsx" "Website-Creator/src/**/*.js" "Website-Creator/src/**/*.jsx" "backend/**/*.js"

echo "Correction terminée !"
