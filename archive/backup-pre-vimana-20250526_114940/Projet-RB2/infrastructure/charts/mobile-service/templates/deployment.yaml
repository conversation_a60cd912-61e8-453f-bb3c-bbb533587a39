apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "mobile-service.fullname" . }}
  labels:
    {{- include "mobile-service.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "mobile-service.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "mobile-service.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "mobile-service.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 60
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ready
              port: http
            initialDelaySeconds: 30
            periodSeconds: 5
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            {{- range $key, $value := .Values.env }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
          volumeMounts:
            {{- if .Values.persistence.enabled }}
            - name: storage-volume
              mountPath: {{ .Values.persistence.mountPath }}
            {{- end }}
            {{- if .Values.secrets.androidKeystore.enabled }}
            - name: android-keystore
              mountPath: {{ .Values.secrets.androidKeystore.mountPath }}
              readOnly: true
            {{- end }}
            {{- if .Values.secrets.iosCertificates.enabled }}
            - name: ios-certificates
              mountPath: {{ .Values.secrets.iosCertificates.mountPath }}
              readOnly: true
            {{- end }}
            {{- if .Values.configMap.enabled }}
            - name: config-volume
              mountPath: /app/config
            {{- end }}
      volumes:
        {{- if .Values.persistence.enabled }}
        - name: storage-volume
          persistentVolumeClaim:
            claimName: {{ include "mobile-service.fullname" . }}
        {{- end }}
        {{- if .Values.secrets.androidKeystore.enabled }}
        - name: android-keystore
          secret:
            secretName: {{ .Values.secrets.androidKeystore.name }}
        {{- end }}
        {{- if .Values.secrets.iosCertificates.enabled }}
        - name: ios-certificates
          secret:
            secretName: {{ .Values.secrets.iosCertificates.name }}
        {{- end }}
        {{- if .Values.configMap.enabled }}
        - name: config-volume
          configMap:
            name: {{ .Values.configMap.name }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
