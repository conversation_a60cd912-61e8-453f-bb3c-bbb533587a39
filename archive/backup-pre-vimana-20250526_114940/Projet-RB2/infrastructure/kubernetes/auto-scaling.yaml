---
# Vertical Pod Autoscaler (VPA) for API service
# Automatically adjusts CPU and memory resource requests and limits
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: api-service-vpa
  namespace: rb2
spec:
  targetRef:
    apiVersion: "apps/v1"
    kind: Deployment
    name: api-service
  updatePolicy:
    updateMode: "Auto"
    minReplicas: 2
  resourcePolicy:
    containerPolicies:
      - containerName: '*'
        minAllowed:
          cpu: 100m
          memory: 100Mi
        maxAllowed:
          cpu: 2
          memory: 2Gi
        controlledResources: ["cpu", "memory"]

---
# Horizontal Pod Autoscaler (HPA) using custom metrics
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-service-custom-hpa
  namespace: rb2
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-service
  minReplicas: 3
  maxReplicas: 20
  metrics:
  # Scale based on CPU utilization
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 75
  
  # Scale based on memory utilization
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  
  # Scale based on request rate (custom Prometheus metric)
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: 100
  
  # Scale based on response time (custom Prometheus metric)
  - type: Object
    object:
      metric:
        name: http_response_time_seconds
      describedObject:
        apiVersion: apps/v1
        kind: Deployment
        name: api-service
      target:
        type: Value
        value: 0.5
  
  # Advanced HPA behavior configuration for smoother scaling
  behavior:
    scaleDown:
      # Wait longer before scaling down to avoid thrashing
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      # Scale up more quickly when needed
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 5
        periodSeconds: 60
      selectPolicy: Max

---
# Cluster Autoscaler Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-autoscaler-config
  namespace: kube-system
data:
  cluster-autoscaler.yaml: |
    autoscalingGroups:
    - name: eks-worker-nodes
      minSize: 3
      maxSize: 20
    
    # General autoscaler configuration
    scaleDownUtilizationThreshold: 0.5
    scaleDownUnneededTime: 5m
    scaleDownDelayAfterAdd: 10m
    maxNodeProvisionTime: 15m
    
    # Prevent scaling down recently added nodes
    newPodScaleUpDelay: 2m
    
    # Optimization settings
    scanInterval: 10s
    scaleDownUnreadyTime: 20m
    maxGracefulTerminationSec: 600
    
    # Balance similar node groups
    balanceSimilarNodeGroups: true
    
    # Automatically discover ASGs with matching tags
    autoDiscovery:
      clusterName: rb2-cluster
      tags:
      - k8s.io/cluster-autoscaler/enabled
      - k8s.io/cluster-autoscaler/rb2-cluster

---
# Custom Metrics API configuration for Prometheus adapter
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-adapter-config
  namespace: monitoring
data:
  config.yaml: |
    rules:
    # HTTP requests per second metric for HPA
    - seriesQuery: 'http_requests_total{job="api-service",code=~"2.."}'
      resources:
        overrides:
          namespace:
            resource: namespace
          pod:
            resource: pod
      name:
        matches: "^(.*)_total$"
        as: "${1}_per_second"
      metricsQuery: 'sum(rate(<<.Series>>{<<.LabelMatchers>>}[2m])) by (<<.GroupBy>>)'
    
    # HTTP response time metric for HPA
    - seriesQuery: 'http_request_duration_seconds_sum{job="api-service"}'
      resources:
        overrides:
          namespace:
            resource: namespace
          pod:
            resource: pod
      name:
        matches: "^(.*)_sum$"
        as: "${1}"
      metricsQuery: 'sum(rate(<<.Series>>{<<.LabelMatchers>>}[2m])) by (<<.GroupBy>>) / sum(rate(http_request_duration_seconds_count{<<.LabelMatchers>>}[2m])) by (<<.GroupBy>>)'
    
    # Queue length metric for HPA
    - seriesQuery: 'app_queue_length{job="api-service"}'
      resources:
        overrides:
          namespace:
            resource: namespace
          pod:
            resource: pod
      name:
        matches: "app_queue_length"
        as: "app_queue_length"
      metricsQuery: 'avg(<<.Series>>{<<.LabelMatchers>>}) by (<<.GroupBy>>)'
    
    # Database connection utilization
    - seriesQuery: 'db_connections_active{job="api-service"}'
      resources:
        overrides:
          namespace:
            resource: namespace
          pod:
            resource: pod
      name:
        matches: "db_connections_active"
        as: "db_connections_utilization"
      metricsQuery: 'avg(<<.Series>>{<<.LabelMatchers>>}) by (<<.GroupBy>>) / on(namespace) group_left() max(db_connections_max{<<.LabelMatchers>>}) by (namespace) * 100'

---
# Prometheus ServiceMonitor for API service metrics
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: api-service-monitor
  namespace: monitoring
  labels:
    release: prometheus
spec:
  selector:
    matchLabels:
      app: api-service
  namespaceSelector:
    matchNames:
      - rb2
  endpoints:
  - port: http
    path: /metrics
    interval: 15s
    scrapeTimeout: 10s
    metricRelabelings:
    # Drop high cardinality metrics to avoid overwhelming storage
    - sourceLabels: [__name__]
      regex: '(http_requests_total|http_request_duration_seconds_bucket|http_request_duration_seconds_sum|http_request_duration_seconds_count|app_queue_length|db_connections_active|db_connections_max)'
      action: keep

---
# Deployment for a Custom Autoscaler for Database Pool Sizing
apiVersion: apps/v1
kind: Deployment
metadata:
  name: db-connection-scaler
  namespace: rb2
  labels:
    app: db-connection-scaler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: db-connection-scaler
  template:
    metadata:
      labels:
        app: db-connection-scaler
    spec:
      serviceAccountName: db-scaler-sa
      containers:
      - name: db-scaler
        image: rb2/db-connection-scaler:latest
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
        env:
        - name: TARGET_SERVICE
          value: "api-service"
        - name: CONNECTION_POOL_MIN
          value: "5"
        - name: CONNECTION_POOL_MAX
          value: "50"
        - name: SCALE_INTERVAL_SECONDS
          value: "30"
        - name: PROMETHEUS_URL
          value: "http://prometheus-service.monitoring:9090"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
# Prometheus Rules for Autoscaling Alerts
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: autoscaling-alerts
  namespace: monitoring
  labels:
    prometheus: prometheus
    role: alert-rules
spec:
  groups:
  - name: autoscaling.rules
    rules:
    # Alert when HPA is at max replicas for extended period
    - alert: HPAMaxedOut
      expr: kube_hpa_status_current_replicas{namespace="rb2"} == kube_hpa_spec_max_replicas{namespace="rb2"}
      for: 30m
      labels:
        severity: warning
        team: platform
      annotations:
        summary: "HPA at max capacity for 30+ minutes"
        description: "HPA {{ $labels.hpa }} in namespace {{ $labels.namespace }} has been at max replicas for more than 30 minutes. Consider increasing the max replicas setting."
    
    # Alert when scale up/down is happening frequently
    - alert: HPAThrashing
      expr: (changes(kube_hpa_status_current_replicas{namespace="rb2"}[30m]) > 5) and (changes(kube_hpa_status_current_replicas{namespace="rb2"}[1h]) > 10)
      for: 10m
      labels:
        severity: warning
        team: platform
      annotations:
        summary: "HPA thrashing detected"
        description: "HPA {{ $labels.hpa }} in namespace {{ $labels.namespace }} is rapidly scaling up and down. Review metrics and adjust HPA settings to stabilize."
    
    # Alert on node autoscaling failures
    - alert: ClusterAutoscalerFailure
      expr: rate(cluster_autoscaler_failed_scale_ups_total[5m]) > 0
      for: 15m
      labels:
        severity: warning
        team: platform
      annotations:
        summary: "Cluster Autoscaler failing to scale up"
        description: "Cluster Autoscaler has been unable to scale up the cluster. Check for quota issues or instance availability."
