#!/bin/bash

# Définition des variables
PRIMARY_CONTEXT="cluster-primary"
REMOTE_CONTEXT="cluster-remote"
ISTIO_VERSION="1.18.2"
ISTIO_NAMESPACE="istio-system"
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CERTS_DIR="${BASE_DIR}/certs"
CLUSTERS_DIR="${BASE_DIR}/clusters"

# Fonction de configuration du cluster primaire
setup_primary_cluster() {
    echo "Configuration du cluster primaire..."
    kubectl config use-context $PRIMARY_CONTEXT

    # Création du namespace istio-system
    kubectl create namespace $ISTIO_NAMESPACE --context=$PRIMARY_CONTEXT --dry-run=client -o yaml | kubectl apply --context=$PRIMARY_CONTEXT -f -
    
    # Application de la configuration du cluster primaire
    kubectl apply -f ${CLUSTERS_DIR}/primary-cluster-config.yaml --context=$PRIMARY_CONTEXT
    
    # Installation d'Istio avec la configuration multicluster
    istioctl install -f ${BASE_DIR}/istio-multicluster-config.yaml --context=$PRIMARY_CONTEXT

    # Configuration du gateway est-ouest
    kubectl apply -f ${BASE_DIR}/../../../istio-1.18.2/samples/multicluster/expose-services.yaml --context=$PRIMARY_CONTEXT -n $ISTIO_NAMESPACE

    # Installation du gateway est-ouest
    istioctl install -f ${BASE_DIR}/eastwest-gateway.yaml --context=$PRIMARY_CONTEXT
    
    # Exposition d'istiod pour l'accès distant
    kubectl apply -f ${BASE_DIR}/../../../istio-1.18.2/samples/multicluster/expose-istiod.yaml --context=$PRIMARY_CONTEXT -n $ISTIO_NAMESPACE
}

# Fonction de configuration du cluster distant
setup_remote_cluster() {
    echo "Configuration du cluster distant..."
    kubectl config use-context $REMOTE_CONTEXT

    # Création du namespace istio-system sur le cluster distant
    kubectl create namespace $ISTIO_NAMESPACE --context=$REMOTE_CONTEXT --dry-run=client -o yaml | kubectl apply --context=$REMOTE_CONTEXT -f -
    
    # Application de la configuration du cluster distant
    kubectl apply -f ${CLUSTERS_DIR}/remote-cluster-config.yaml --context=$REMOTE_CONTEXT
    
    # Création du secret pour l'accès au cluster primaire
    istioctl x create-remote-secret \
        --context=$REMOTE_CONTEXT \
        --name=remote-cluster | \
        kubectl apply -f - --context=$PRIMARY_CONTEXT
        
    # Création du secret pour l'accès au cluster distant depuis le primaire
    istioctl x create-remote-secret \
        --context=$PRIMARY_CONTEXT \
        --name=primary-cluster | \
        kubectl apply -f - --context=$REMOTE_CONTEXT

    # Installation d'Istio sur le cluster distant avec configuration pour remote
    istioctl install -f ${BASE_DIR}/istio-multicluster-config.yaml \
        --set values.global.multiCluster.clusterName=remote-cluster \
        --context=$REMOTE_CONTEXT
        
    # Installation du gateway est-ouest sur le cluster distant
    istioctl install -f ${BASE_DIR}/eastwest-gateway.yaml \
        --set values.global.network=network2 \
        --set components.ingressGateways[0].label.topology\.istio\.io/network=network2 \
        --set components.ingressGateways[0].name=istio-eastwestgateway-remote \
        --context=$REMOTE_CONTEXT
}

# Fonction de vérification
verify_setup() {
    echo "Vérification de la configuration..."
    
    # Vérification du cluster primaire
    echo "\n=== Vérification du cluster primaire ==="
    kubectl config use-context $PRIMARY_CONTEXT
    echo "\nPods dans le namespace $ISTIO_NAMESPACE:"
    kubectl get pods -n $ISTIO_NAMESPACE --context=$PRIMARY_CONTEXT
    echo "\nServices dans le namespace $ISTIO_NAMESPACE:"
    kubectl get svc -n $ISTIO_NAMESPACE --context=$PRIMARY_CONTEXT
    echo "\nAnalyse Istio:"
    istioctl analyze --context=$PRIMARY_CONTEXT -n $ISTIO_NAMESPACE

    # Vérification du cluster distant
    echo "\n=== Vérification du cluster distant ==="
    kubectl config use-context $REMOTE_CONTEXT
    echo "\nPods dans le namespace $ISTIO_NAMESPACE:"
    kubectl get pods -n $ISTIO_NAMESPACE --context=$REMOTE_CONTEXT
    echo "\nServices dans le namespace $ISTIO_NAMESPACE:"
    kubectl get svc -n $ISTIO_NAMESPACE --context=$REMOTE_CONTEXT
    echo "\nAnalyse Istio:"
    istioctl analyze --context=$REMOTE_CONTEXT -n $ISTIO_NAMESPACE
    
    # Vérification de la connectivité inter-cluster
    echo "\n=== Vérification de la connectivité inter-cluster ==="
    echo "Services du cluster primaire visibles depuis le cluster distant:"
    istioctl proxy-status --context=$REMOTE_CONTEXT
}

# Exécution principale
main() {
    echo "Démarrage de la configuration multicluster..."
    
    # Vérification des prérequis
    if ! command -v istioctl &> /dev/null; then
        echo "istioctl non trouvé. Installation..."
        curl -L https://istio.io/downloadIstio | ISTIO_VERSION=$ISTIO_VERSION sh -
        export PATH=$PWD/istio-$ISTIO_VERSION/bin:$PATH
    fi
    
    # Vérification des contextes kubectl
    if ! kubectl config get-contexts $PRIMARY_CONTEXT &> /dev/null; then
        echo "Erreur: Contexte $PRIMARY_CONTEXT non trouvé. Veuillez configurer vos contextes kubectl."
        exit 1
    fi
    
    if ! kubectl config get-contexts $REMOTE_CONTEXT &> /dev/null; then
        echo "Erreur: Contexte $REMOTE_CONTEXT non trouvé. Veuillez configurer vos contextes kubectl."
        exit 1
    fi
    
    # Création du répertoire pour les certificats s'il n'existe pas
    mkdir -p $CERTS_DIR

    # Configuration des clusters
    echo "Voulez-vous configurer le cluster primaire? (y/n)"
    read -r configure_primary
    if [[ "$configure_primary" == "y" ]]; then
        setup_primary_cluster
    fi
    
    echo "Voulez-vous configurer le cluster distant? (y/n)"
    read -r configure_remote
    if [[ "$configure_remote" == "y" ]]; then
        setup_remote_cluster
    fi
    
    verify_setup

    echo "Configuration multicluster terminée!"
    echo "Pour tester la connectivité, déployez une application de test sur les deux clusters."
}

# Exécution du script
main