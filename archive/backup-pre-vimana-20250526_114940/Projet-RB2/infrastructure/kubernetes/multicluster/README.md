# Configuration Istio Multi-Cluster

Ce dossier contient la configuration nécessaire pour déployer Istio en mode multi-cluster, permettant une communication transparente entre différents clusters Kubernetes.

## Structure des fichiers

```
infrastructure/kubernetes/multicluster/
├── README.md                       # Ce fichier
├── istio-multicluster-config.yaml  # Configuration principale d'Istio pour le multi-cluster
├── eastwest-gateway.yaml           # Configuration de la passerelle est-ouest
├── setup-multicluster.sh           # Script d'installation
├── clusters/                       # Configurations spécifiques à chaque cluster
│   ├── primary-cluster-config.yaml # Configuration du cluster primaire
│   └── remote-cluster-config.yaml  # Configuration du cluster distant
└── certs/                          # Répertoire pour stocker les certificats
```

## Prérequis

- Deux clusters Kubernetes (ou plus) accessibles via kubectl
- kubectl configuré avec les contextes appropriés
- istioctl installé (version 1.18.2 ou supérieure)
- Accès administrateur aux clusters

## Configuration

### 1. Préparation des contextes kubectl

Assurez-vous que vos contextes kubectl sont correctement configurés pour accéder aux clusters:

```bash
# Vérifier les contextes disponibles
kubectl config get-contexts

# Si nécessaire, créer des contextes pour vos clusters
kubectl config set-context cluster-primary --cluster=<nom-cluster-primaire> --user=<utilisateur>
kubectl config set-context cluster-remote --cluster=<nom-cluster-distant> --user=<utilisateur>
```

### 2. Personnalisation de la configuration

Avant de déployer, vous pouvez personnaliser les fichiers de configuration:

- `istio-multicluster-config.yaml`: Configuration principale d'Istio
- `eastwest-gateway.yaml`: Configuration de la passerelle est-ouest
- `clusters/primary-cluster-config.yaml`: Configuration spécifique au cluster primaire
- `clusters/remote-cluster-config.yaml`: Configuration spécifique au cluster distant

### 3. Exécution du script d'installation

```bash
# Rendre le script exécutable
chmod +x setup-multicluster.sh

# Exécuter le script
./setup-multicluster.sh
```

Le script vous guidera à travers le processus d'installation:
1. Il vous demandera si vous souhaitez configurer le cluster primaire
2. Il vous demandera si vous souhaitez configurer le cluster distant
3. Il vérifiera l'installation à la fin

## Intégration avec le système de synchronisation multi-device

Cette configuration multi-cluster s'intègre parfaitement avec notre système de synchronisation multi-device avancé, qui comprend:

1. **SyncEngine**: Moteur central de synchronisation qui coordonne tous les composants
2. **DeviceRegistry**: Gestion des appareils connectés et de leurs états
3. **ConflictResolver**: Résolution avancée des conflits avec plusieurs stratégies
4. **StateReconciler**: Réconciliation d'état entre appareils
5. **OfflineQueue**: File d'attente pour opérations hors ligne
6. **SyncProtocol**: Protocole de communication entre appareils
7. **EventStream**: Flux d'événements pour la communication en temps réel

Le système multi-cluster permet d'étendre ces fonctionnalités à travers plusieurs clusters Kubernetes, offrant une haute disponibilité et une résilience accrue.

## Intégration avec le système de diffusion sélective

Le système de diffusion sélective, qui optimise la synchronisation en temps réel en ne diffusant les mises à jour qu'aux clients concernés, bénéficie également de cette architecture multi-cluster:

1. **SelectiveBroadcastManager**: Gestionnaire central qui coordonne la diffusion des actions Redux
2. **Types de destinataires**: USER, DEVICE, GROUP, ROLE, RESOURCE_SUBSCRIBERS et ALL
3. **Analyseurs d'actions**: Permettent de déterminer automatiquement les destinataires en fonction du contenu des actions
4. **Optimisations**: Regroupement (batching), compression et stratégies de livraison configurables

La configuration multi-cluster permet d'étendre cette diffusion sélective à travers les clusters, réduisant encore davantage la consommation de bande passante et améliorant les performances.

## Dépannage

### Problèmes de connectivité inter-cluster

Si les services d'un cluster ne sont pas visibles depuis un autre cluster:

1. Vérifier que les passerelles est-ouest sont correctement déployées:
   ```bash
   kubectl get pods -n istio-system -l app=istio-eastwestgateway
   ```

2. Vérifier que les secrets contenant les kubeconfigs sont correctement créés:
   ```bash
   kubectl get secrets -n istio-system
   ```

3. Vérifier les logs des passerelles est-ouest:
   ```bash
   kubectl logs -n istio-system -l app=istio-eastwestgateway
   ```

### Problèmes de discovery des services

Si les services ne sont pas découverts correctement:

1. Vérifier les logs d'istiod:
   ```bash
   kubectl logs -n istio-system -l app=istiod
   ```

2. Vérifier la configuration des endpoints:
   ```bash
   kubectl get endpoints -A
   ```

## Références

- [Documentation officielle Istio Multi-Cluster](https://istio.io/latest/docs/setup/install/multicluster/)
- [Istio Multi-Primary Installation](https://istio.io/latest/docs/setup/install/multicluster/multi-primary/)
- [Istio Multi-Primary sur différents réseaux](https://istio.io/latest/docs/setup/install/multicluster/multi-primary_multi-network/)
