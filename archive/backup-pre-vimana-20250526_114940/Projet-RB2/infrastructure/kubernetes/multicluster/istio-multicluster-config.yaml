apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: istio-multicluster-config
  namespace: istio-system
spec:
  profile: default
  meshConfig:
    # Enable access logging for debugging multi-cluster traffic
    accessLogFile: "/dev/stdout"
    accessLogEncoding: JSON
    enableTracing: true
    # Configure the trust domain for this cluster
    trustDomain: "cluster.local"
    # Add global domain suffix for multi-cluster service discovery
    defaultServiceExportTo:
      - "*"
    defaultVirtualServiceExportTo:
      - "*"
    defaultDestinationRuleExportTo:
      - "*"
    # Default configuration for proxies
    defaultConfig:
      proxyMetadata:
        # Enable automatic protocol detection
        AUTO_PROTOCOL_DETECT: "true"
      # Configure discovery and proxy settings
      discoveryAddress: istiod.istio-system.svc:15012
      tracing:
        zipkin:
          address: zipkin.istio-system:9411
      holdApplicationUntilProxyStarts: true
  
  # Global values passed through to helm templates
  values:
    global:
      # Enable multiCluster mode
      multiCluster:
        enabled: true
        # Name of this cluster for cross-cluster communication
        clusterName: "primary-cluster"
        # Global domain suffix for multi-cluster service discovery
        globalDomainSuffix: "global"
      
      # Configure mesh networks for multi-cluster communication
      meshNetworks:
        network1:
          endpoints:
          - fromRegistry: primary-cluster
          gateways:
          - address: istio-eastwestgateway.istio-system.svc.cluster.local
            port: 15443
        network2:
          endpoints:
          - fromRegistry: remote-cluster
          gateways:
          - address: istio-eastwestgateway-remote.istio-system.svc.cluster.local
            port: 15443
      
      # Enable features required for multi-cluster
      pilotCertProvider: kubernetes
      
      # Configure proxy for multi-cluster
      proxy:
        # Enable automatic protocol detection
        autoInject: enabled
        # Configure cluster domain
        clusterDomain: "cluster.local"
        # Configure proxy resource limits
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
  
  # Components configuration
  components:
    # Configure base components
    base:
      enabled: true
    
    # Configure pilot (istiod)
    pilot:
      enabled: true
      k8s:
        env:
          # Enable endpoints from other clusters
          - name: PILOT_ENABLE_K8S_SELECT_WORKLOAD_ENTRIES
            value: "true"
          # Enable cross-cluster service discovery
          - name: PILOT_ENABLE_CROSS_CLUSTER_WORKLOAD_ENTRY
            value: "true"
          # Enable endpoint discovery features
          - name: PILOT_ENABLE_ENDPOINT_DISCOVERY
            value: "true"
    
    # Configure ingress gateway
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
  
  # Feature settings
  features:
    # Enable external istiod for remote clusters
    externalIstiod: true
    # Enable mounting of mTLS certificates for secure communication
    mountMtlsCerts: true
    # Include Envoy filter for advanced traffic management
    includeEnvoyFilter: true
