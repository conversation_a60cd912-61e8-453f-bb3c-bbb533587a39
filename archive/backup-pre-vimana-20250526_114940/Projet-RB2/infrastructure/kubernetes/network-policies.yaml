apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mobile-service-network-policy
  namespace: rb2
spec:
  podSelector:
    matchLabels:
      app: mobile-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: default
    ports:
    - protocol: TCP
      port: 80
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: default
    ports:
    - protocol: TCP
      port: 80