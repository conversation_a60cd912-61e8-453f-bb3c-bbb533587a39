groups:
- name: retreat-and-be-alerts
  rules:
  - alert: HighCPUUsage
    expr: container_cpu_usage_seconds_total > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      description: "Container {{ $labels.container }} CPU usage is above 80%"

  - alert: HighMemoryUsage
    expr: container_memory_usage_bytes > 90
    for: 5m
    labels:
      severity: critical
    annotations:
      description: "Container {{ $labels.container }} memory usage is above 90%"

  - alert: APIHighLatency
    expr: http_request_duration_seconds > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      description: "API endpoint {{ $labels.endpoint }} is experiencing high latency"