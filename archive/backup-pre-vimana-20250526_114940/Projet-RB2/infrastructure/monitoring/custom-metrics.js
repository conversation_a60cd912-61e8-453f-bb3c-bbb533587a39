/**
 * Custom Metrics Configuration for RB2
 * 
 * This module configures Prometheus custom metrics for the application,
 * providing detailed monitoring beyond basic system metrics.
 */

const client = require('prom-client');
const Registry = client.Registry;
const registry = new Registry();

// Enable default metrics collection (CPU, memory, etc.)
const collectDefaultMetrics = client.collectDefaultMetrics;
collectDefaultMetrics({ 
  register: registry,
  prefix: 'rb2_',
  labels: { application: 'rb2' }
});

// Create custom counters, gauges, histograms and summaries
const metrics = {
  // HTTP requests
  httpRequestsTotal: new client.Counter({
    name: 'rb2_http_requests_total',
    help: 'Total number of HTTP requests',
    labelNames: ['method', 'route', 'status_code', 'user_type'],
    registers: [registry]
  }),

  // HTTP request duration
  httpRequestDurationSeconds: new client.Histogram({
    name: 'rb2_http_request_duration_seconds',
    help: 'HTTP request duration in seconds',
    labelNames: ['method', 'route', 'status_code'],
    buckets: [0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
    registers: [registry]
  }),

  // Database operations
  dbOperationsTotal: new client.Counter({
    name: 'rb2_db_operations_total',
    help: 'Total number of database operations',
    labelNames: ['operation', 'collection', 'success'],
    registers: [registry]
  }),

  // Database operation duration
  dbOperationDurationSeconds: new client.Histogram({
    name: 'rb2_db_operation_duration_seconds',
    help: 'Database operation duration in seconds',
    labelNames: ['operation', 'collection'],
    buckets: [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1],
    registers: [registry]
  }),

  // Active users
  activeUsers: new client.Gauge({
    name: 'rb2_active_users',
    help: 'Number of active users',
    labelNames: ['user_type'],
    registers: [registry]
  }),

  // User registrations
  userRegistrationsTotal: new client.Counter({
    name: 'rb2_user_registrations_total',
    help: 'Total number of user registrations',
    registers: [registry]
  }),

  // Authentication attempts
  authAttemptsTotal: new client.Counter({
    name: 'rb2_auth_attempts_total',
    help: 'Total number of authentication attempts',
    labelNames: ['success'],
    registers: [registry]
  }),

  // Authentication failures
  authFailuresTotal: new client.Counter({
    name: 'rb2_auth_failures_total',
    help: 'Total number of authentication failures',
    labelNames: ['reason'],
    registers: [registry]
  }),

  // API errors
  apiErrorsTotal: new client.Counter({
    name: 'rb2_api_errors_total',
    help: 'Total number of API errors',
    labelNames: ['route', 'error_code', 'error_type'],
    registers: [registry]
  }),

  // Job processing
  jobsProcessedTotal: new client.Counter({
    name: 'rb2_jobs_processed_total',
    help: 'Total number of background jobs processed',
    labelNames: ['job_type', 'status'],
    registers: [registry]
  }),

  // Job processing duration
  jobProcessingDurationSeconds: new client.Histogram({
    name: 'rb2_job_processing_duration_seconds',
    help: 'Background job processing duration in seconds',
    labelNames: ['job_type'],
    buckets: [0.1, 0.5, 1, 2.5, 5, 10, 30, 60, 120, 300, 600],
    registers: [registry]
  }),

  // Queue sizes
  queueSize: new client.Gauge({
    name: 'rb2_queue_size',
    help: 'Number of items in queue',
    labelNames: ['queue_name'],
    registers: [registry]
  }),

  // Cache operations
  cacheOperationsTotal: new client.Counter({
    name: 'rb2_cache_operations_total',
    help: 'Total number of cache operations',
    labelNames: ['operation', 'success'],
    registers: [registry]
  }),

  // Cache hit ratio (0-1)
  cacheHitRatio: new client.Gauge({
    name: 'rb2_cache_hit_ratio',
    help: 'Cache hit ratio (0-1)',
    labelNames: ['cache_name'],
    registers: [registry]
  }),

  // External API calls
  externalApiCallsTotal: new client.Counter({
    name: 'rb2_external_api_calls_total',
    help: 'Total number of external API calls',
    labelNames: ['api', 'method', 'status_code'],
    registers: [registry]
  }),

  // External API call duration
  externalApiCallDurationSeconds: new client.Histogram({
    name: 'rb2_external_api_call_duration_seconds',
    help: 'External API call duration in seconds',
    labelNames: ['api', 'method'],
    buckets: [0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
    registers: [registry]
  }),

  // Memory usage
  memoryUsage: new client.Gauge({
    name: 'rb2_memory_usage',
    help: 'Memory usage statistics in bytes',
    labelNames: ['type'],
    registers: [registry]
  }),

  // CPU usage
  cpuUsage: new client.Gauge({
    name: 'rb2_cpu_usage',
    help: 'CPU usage percentage',
    registers: [registry]
  }),

  // Event loop lag
  eventLoopLag: new client.Gauge({
    name: 'rb2_event_loop_lag',
    help: 'Event loop lag in milliseconds',
    registers: [registry]
  }),

  // Garbage collection stats
  gcDurationSeconds: new client.Histogram({
    name: 'rb2_gc_duration_seconds',
    help: 'Garbage collection duration in seconds',
    labelNames: ['gc_type'],
    buckets: [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5],
    registers: [registry]
  }),

  // Circuit breaker state
  circuitBreakerState: new client.Gauge({
    name: 'rb2_circuit_breaker_state',
    help: 'Circuit breaker state (0=open, 1=half-open, 2=closed)',
    labelNames: ['name'],
    registers: [registry]
  }),

  // Business metrics - Checkouts
  checkoutAttemptsTotal: new client.Counter({
    name: 'rb2_checkout_attempts_total',
    help: 'Total number of checkout attempts',
    registers: [registry]
  }),

  checkoutFailuresTotal: new client.Counter({
    name: 'rb2_checkout_failures_total',
    help: 'Total number of checkout failures',
    labelNames: ['reason'],
    registers: [registry]
  }),

  // Business metrics - Payments
  paymentAttemptsTotal: new client.Counter({
    name: 'rb2_payment_attempts_total',
    help: 'Total number of payment attempts',
    labelNames: ['payment_method'],
    registers: [registry]
  }),

  paymentFailuresTotal: new client.Counter({
    name: 'rb2_payment_failures_total',
    help: 'Total number of payment failures',
    labelNames: ['payment_method', 'reason'],
    registers: [registry]
  }),

  // Business metrics - Revenue
  revenueTotal: new client.Counter({
    name: 'rb2_revenue_total',
    help: 'Total revenue',
    labelNames: ['currency'],
    registers: [registry]
  }),

  // Feature flag usage
  featureFlagUsage: new client.Counter({
    name: 'rb2_feature_flag_usage',
    help: 'Count of feature flag usage',
    labelNames: ['feature', 'enabled'],
    registers: [registry]
  }),

  // Rate limiting
  rateLimitHitsTotal: new client.Counter({
    name: 'rb2_rate_limit_hits_total',
    help: 'Total number of rate limit hits',
    labelNames: ['route', 'user_type'],
    registers: [registry]
  })
};

// Track memory usage
function updateMemoryMetrics() {
  const memoryUsage = process.memoryUsage();
  metrics.memoryUsage.set({ type: 'rss' }, memoryUsage.rss);
  metrics.memoryUsage.set({ type: 'heapTotal' }, memoryUsage.heapTotal);
  metrics.memoryUsage.set({ type: 'heapUsed' }, memoryUsage.heapUsed);
  metrics.memoryUsage.set({ type: 'external' }, memoryUsage.external);
  
  if (memoryUsage.arrayBuffers) {
    metrics.memoryUsage.set({ type: 'arrayBuffers' }, memoryUsage.arrayBuffers);
  }
}

// Track event loop lag
function updateEventLoopLag() {
  const start = Date.now();
  setImmediate(() => {
    const lag = Date.now() - start;
    metrics.eventLoopLag.set(lag);
  });
}

// Setup periodic updates
setInterval(updateMemoryMetrics, 5000);
setInterval(updateEventLoopLag, 5000);

// Middleware to track HTTP requests
function metricsMiddleware(req, res, next) {
  const start = process.hrtime();
  
  // Create response interceptor
  const originalEnd = res.end;
  res.end = function(...args) {
    // Calculate duration
    const diff = process.hrtime(start);
    const duration = diff[0] + diff[1] / 1e9;
    
    // Determine route pattern (convert URL params to placeholders)
    let route = req.route ? req.route.path : req.path;
    route = route.replace(/\/:[^\/]+/g, '/:param');
    
    // Increment request counter
    metrics.httpRequestsTotal.inc({
      method: req.method,
      route,
      status_code: res.statusCode,
      user_type: req.user ? req.user.type : 'anonymous'
    });
    
    // Record request duration
    metrics.httpRequestDurationSeconds.observe(
      {
        method: req.method,
        route,
        status_code: res.statusCode
      },
      duration
    );
    
    // Track errors
    if (res.statusCode >= 400) {
      metrics.apiErrorsTotal.inc({
        route,
        error_code: res.statusCode,
        error_type: res.statusCode >= 500 ? 'server_error' : 'client_error'
      });
    }
    
    originalEnd.apply(res, args);
  };
  
  next();
}

// Expose metrics endpoint
function setupMetricsEndpoint(app) {
  app.get('/metrics', async (req, res) => {
    try {
      res.set('Content-Type', registry.contentType);
      res.end(await registry.metrics());
    } catch (ex) {
      res.status(500).end(ex.message);
    }
  });
}

// Track database operations
function trackDbOperation(operation, collection, callback) {
  const start = process.hrtime();
  
  return callback()
    .then(result => {
      const diff = process.hrtime(start);
      const duration = diff[0] + diff[1] / 1e9;
      
      metrics.dbOperationsTotal.inc({
        operation,
        collection,
        success: 'true'
      });
      
      metrics.dbOperationDurationSeconds.observe(
        {
          operation,
          collection
        },
        duration
      );
      
      return result;
    })
    .catch(err => {
      metrics.dbOperationsTotal.inc({
        operation,
        collection,
        success: 'false'
      });
      
      throw err;
    });
}

// Track external API calls
function trackApiCall(api, method, callback) {
  const start = process.hrtime();
  
  return callback()
    .then(result => {
      const diff = process.hrtime(start);
      const duration = diff[0] + diff[1] / 1e9;
      
      const statusCode = result.status || (result.response ? result.response.status : 'unknown');
      
      metrics.externalApiCallsTotal.inc({
        api,
        method,
        status_code: statusCode
      });
      
      metrics.externalApiCallDurationSeconds.observe(
        {
          api,
          method
        },
        duration
      );
      
      return result;
    })
    .catch(err => {
      const statusCode = err.response ? err.response.status : 'error';
      
      metrics.externalApiCallsTotal.inc({
        api,
        method,
        status_code: statusCode
      });
      
      throw err;
    });
}

// Track job processing
function trackJob(jobType, callback) {
  const start = process.hrtime();
  
  return callback()
    .then(result => {
      const diff = process.hrtime(start);
      const duration = diff[0] + diff[1] / 1e9;
      
      metrics.jobsProcessedTotal.inc({
        job_type: jobType,
        status: 'success'
      });
      
      metrics.jobProcessingDurationSeconds.observe(
        {
          job_type: jobType
        },
        duration
      );
      
      return result;
    })
    .catch(err => {
      metrics.jobsProcessedTotal.inc({
        job_type: jobType,
        status: 'failure'
      });
      
      throw err;
    });
}

// Update circuit breaker state metrics
function updateCircuitBreakerMetrics(name, state) {
  // Map state to numeric value
  // 0 = open, 1 = half-open, 2 = closed
  const stateValue = state === 'OPEN' ? 0 : state === 'HALF_OPEN' ? 1 : 2;
  
  metrics.circuitBreakerState.set(
    {
      name
    },
    stateValue
  );
}

// Track feature flag usage
function trackFeatureFlag(feature, enabled) {
  metrics.featureFlagUsage.inc({
    feature,
    enabled: enabled.toString()
  });
}

// Add a cache hit or miss
function trackCacheOperation(hit, cacheName = 'default') {
  metrics.cacheOperationsTotal.inc({
    operation: hit ? 'hit' : 'miss',
    success: 'true'
  });
  
  // Update cache hit ratio
  // This is a simple implementation - in production, you'd want to use a more sophisticated approach
  // like a sliding window counter
  const hits = metrics.cacheOperationsTotal.hashMap['operation:hit,success:true'] 
    ? metrics.cacheOperationsTotal.hashMap['operation:hit,success:true'].value 
    : 0;
    
  const misses = metrics.cacheOperationsTotal.hashMap['operation:miss,success:true'] 
    ? metrics.cacheOperationsTotal.hashMap['operation:miss,success:true'].value 
    : 0;
  
  const total = hits + misses;
  
  if (total > 0) {
    metrics.cacheHitRatio.set(
      {
        cache_name: cacheName
      },
      hits / total
    );
  }
}

// Track user authentication
function trackAuthentication(success, reason = null) {
  metrics.authAttemptsTotal.inc({
    success: success.toString()
  });
  
  if (!success && reason) {
    metrics.authFailuresTotal.inc({
      reason
    });
  }
}

// Track active users
function setActiveUsers(count, userType = 'all') {
  metrics.activeUsers.set(
    {
      user_type: userType
    },
    count
  );
}

// Track business transactions
function trackCheckout(success, reason = null) {
  metrics.checkoutAttemptsTotal.inc();
  
  if (!success) {
    metrics.checkoutFailuresTotal.inc({
      reason: reason || 'unknown'
    });
  }
}

function trackPayment(success, paymentMethod, reason = null) {
  metrics.paymentAttemptsTotal.inc({
    payment_method: paymentMethod
  });
  
  if (!success) {
    metrics.paymentFailuresTotal.inc({
      payment_method: paymentMethod,
      reason: reason || 'unknown'
    });
  }
}

function trackRevenue(amount, currency = 'USD') {
  metrics.revenueTotal.inc(
    {
      currency
    },
    amount
  );
}

// Track rate limiting
function trackRateLimit(route, userType = 'anonymous') {
  metrics.rateLimitHitsTotal.inc({
    route,
    user_type: userType
  });
}

module.exports = {
  registry,
  metrics,
  metricsMiddleware,
  setupMetricsEndpoint,
  trackDbOperation,
  trackApiCall,
  trackJob,
  updateCircuitBreakerMetrics,
  trackFeatureFlag,
  trackCacheOperation,
  trackAuthentication,
  setActiveUsers,
  trackCheckout,
  trackPayment,
  trackRevenue,
  trackRateLimit
};
