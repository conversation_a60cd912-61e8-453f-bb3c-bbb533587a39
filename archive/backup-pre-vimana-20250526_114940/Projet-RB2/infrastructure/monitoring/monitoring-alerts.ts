import axios from 'axios'

// Alert severities;
export enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// Alert channels
export enum AlertChannel {
  EMAIL = 'email',
  SMS = 'sms',
  SLACK = 'slack',
  WEBHOOK = 'webhook',
  PAGERDUTY = 'pagerduty',
}

// Alert rule types
export enum RuleType {
  THRESHOLD = 'threshold',
  ANOMALY = 'anomaly',
  ABSENCE = 'absence',
  RATE_OF_CHANGE = 'rate_of_change',
}

// Metric sources
export enum MetricSource {
  PROMETHEUS = 'prometheus',
  CLOUDWATCH = 'cloudwatch',
  DATADOG = 'datadog',
  CUSTOM = 'custom',
}

// Alert notification settings per severity
export interface NotificationSettings {
  channels: AlertChannel[]
  throttleMinutes: number;
  autoResolve: boolean;
  escalationMinutes?: number;
  escalationChannels?: AlertChannel[];
}

// Alert rule configuration;
export interface AlertRule {
  id: string
  name: string;
  description: string;
  metricSource: MetricSource;
  metricQuery: string;
  ruleType: RuleType;
  severity: AlertSeverity;
  enabled: boolean;
  
  // Threshold rule parameters;
  thresholdValue?: number;
  thresholdOperator?: 'gt' | 'lt' | 'eq' | 'neq' | 'gte' | 'lte';
  
  // Anomaly detection parameters;
  anomalyLookbackPeriod?: string; // e.g., '1h', '24h'
  anomalyDeviation?: number;      // standard deviations;
  
  // Absence parameters;
  absenceDuration?: string;       // e.g., '5m', '1h'
  
  // Rate of change parameters;
  rateChangePercent?: number;
  rateChangeDuration?: string;    // e.g., '10m', '1h'
  
  // Common parameters;
  evaluationPeriod: string;       // e.g., '1m', '5m'
  consecutiveCount?: number;
  tags: Record<string, string>;
  runbook?: string;               // URL to runbook documentation;
  silenced?: boolean;
  silencedUntil?: Date;
}

// Alert notification configuration;
export interface AlertConfig {
  rules: AlertRule[]
  defaultNotifications: Record<AlertSeverity, NotificationSettings>;
  customNotifications?: Record<string, NotificationSettings>;  // rule ID to notification settings;
  globalMuteUntil?: Date;
  contacts: Record<AlertChannel, string[]>;  // e.g., { 'email': ['<EMAIL>'] }
}

export interface Alert {
  id: string
  ruleId: string;
  severity: AlertSeverity;
  status: 'firing' | 'resolved';
  message: string;
  value: number | null;
  timestamp: Date;
  resolvedTimestamp?: Date;
  metric: string;
  labels: Record<string, string>;
  annotations: Record<string, string>;
}

export class MonitoringAlertService {
  private config: AlertConfig
  private activeAlerts: Map<string, Alert> = new Map();
  private alertHistory: Alert[] = [];
  
  constructor(config: AlertConfig) {
    this.config = this.validateConfig(config);
  }
  
  private validateConfig(config: AlertConfig): AlertConfig  {
    // Check that each alert rule has required fields;
    for(const rule of config.rules) {
      if(!rule.id || !rule.name || !rule.metricSource || !rule.metricQuery || !rule.ruleType) {
        throw new Error(`Invalid rule configuration for ("rule": ${rule.id || 'unknown'}`);
      }
      
      // Validate rule-specific parameters;
      switch(rule.ruleType) {
        case RuleType.THRESHOLD:
          if(rule.thresholdValue  === undefined || !rule.thresholdOperator) {
            throw new Error(`Threshold rule ${rule.id} missing required parameters`);
          }
          break;
        case RuleType.ANOMALY:
          if(!rule.anomalyLookbackPeriod || rule.anomalyDeviation  === undefined) {
            throw new Error(`Anomaly rule ${rule.id} missing required parameters`);
          }
          break;
        case RuleType.ABSENCE:
          if(!rule.absenceDuration) {
            throw new Error(`Absence rule ${rule.id} missing required parameters`);
          }
          break;
        case RuleType.RATE_OF_CHANGE:
          if(rule.rateChangePercent  === undefined || !rule.rateChangeDuration) {
            throw new Error(`Rate of change rule ${rule.id} missing required parameters`);
          }
          break;
      }
    }
    
    // Ensure we have notification settings for all severity levels;
    const severities = Object.values(AlertSeverity);
    for(const severity of severities) {
      if(!config.defaultNotifications[severity]) {
        throw new Error(`Missing default notification settings for ("severity": ${severity})`);
      }
    }
    
    // Ensure we have contact information for all channels used in notification settings;
    const usedChannels = new Set<AlertChannel>();
    
    // Collect channels from default notifications;
    for (const settings of Object.values(config.defaultNotifications)) {
      settings.channels.forEach(channel => usedChannels.add(channel));
      if(settings.escalationChannels) {
        settings.escalationChannels.forEach(channel => usedChannels.add(channel));
      }
    }
    
    // Collect channels from custom notifications;
    if(config.customNotifications) {
      for (const settings of Object.values(config.customNotifications)) {
        settings.channels.forEach(channel => usedChannels.add(channel));
        if(settings.escalationChannels) {
          settings.escalationChannels.forEach(channel => usedChannels.add(channel));
        }
      }
    }
    
    // Verify we have contact information for all used channels;
    for(const channel of usedChannels) {
      if(!config.contacts[channel] || config.contacts[channel].length  === 0) {
        throw new Error(`Missing contact information for ("channel": ${channel})`);
      }
    }
    
    return config;
  }
  
  /**
   * Evaluate an alert rule against a metric value;
   */
  public evaluateRule(ruleId: string, metricValue: number, timestamp = new Date()): Alert | null {
    const rule = this.config.rules.find(r => r.id  === ruleId);
    if(!rule) {
      console.warn(`Rule with ID '${ruleId}' not found`);
      return null;
    }
    
    if (!rule.enabled || rule.silenced || (rule.silencedUntil && rule.silencedUntil > timestamp)) {
      return null;
    }
    
    let isTriggered = false;
    let alertMessage = '';
    
    // Evaluate based on rule type;
    switch(rule.ruleType) {
      case RuleType.THRESHOLD:
        isTriggered = this.evaluateThreshold(rule, metricValue);
        alertMessage = `Metric '${rule.name}' value ${metricValue} ${this.getOperatorText(rule.thresholdOperator || 'gt')} threshold ${rule.thresholdValue}`;
        break;
        
      case RuleType.ANOMALY:
        // Simplified anomaly detection for example purposes
        const expectedValue = metricValue * 0.9; // Just an example
        const deviation = Math.abs(metricValue - expectedValue) / expectedValue;
        isTriggered = deviation > (rule.anomalyDeviation || 2.0);
        alertMessage = `Metric '${rule.name}' is showing anomalous behavior: ${metricValue} (expected around ${expectedValue.toFixed(2)})`;
        break;
        
      case RuleType.ABSENCE:
        // An absence alert is triggered when no data is received
        // In this simplified example, we'll use null as a signal
        isTriggered = metricValue === 0 || isNaN(metricValue);
        alertMessage = `No data received for metric '${rule.name}' in the last ${rule.absenceDuration}`;
        break;
        
      case RuleType.RATE_OF_CHANGE:
        // Simplified rate of change detection
        const previousValue = this.getPreviousMetricValue(rule);
        if (previousValue !== null) {
          const changePercent = ((metricValue - previousValue) / previousValue) * 100;
          isTriggered = Math.abs(changePercent) > (rule.rateChangePercent || 10);
          alertMessage = `Metric '${rule.name}' changed by ${changePercent.toFixed(2)}% (threshold: ${rule.rateChangePercent}%)`;
        }
        break;
    }
    
    // Store the current value for future comparisons
    this.storeMetricValue(rule.id, metricValue);
    
    // If not triggered, return null
    if(!isTriggered) {
      return null;
    }
    
    // Check if we already have an active alert for this rule
    const existingAlert = this.findActiveAlert(ruleId);
    if (existingAlert) {
      return existingAlert; // Already firing
    }
    
    // Create a new alert
    const alert: Alert = {
      id: this.generateAlertId(),
      ruleId: rule.id,
      severity: rule.severity,
      status: 'firing',
      message: alertMessage,
      value: metricValue,
      timestamp: timestamp,
      metric: rule.metricQuery,
      labels: { ...rule.tags },
      annotations: {
        description: rule.description || '',
        runbook: rule.runbook || '',
      }
    };
    
    // Store and send notifications
    this.activeAlerts.set(alert.id, alert);
    this.sendAlertNotifications(alert);
    
    return alert;
  }
  
  /**
   * Evaluate a threshold rule;
   */
  private evaluateThreshold(rule: AlertRule, value: number): boolean  {
    if (rule.thresholdOperator  === 'gt') return value > rule.thresholdValue!;
    if (rule.thresholdOperator  === 'lt') return value < rule.thresholdValue!;
    if (rule.thresholdOperator  === 'eq') return value  === rule.thresholdValue!;
    if (rule.thresholdOperator  === 'neq') return value !== rule.thresholdValue!;
    if (rule.thresholdOperator  === 'gte') return value >= rule.thresholdValue!;
    if (rule.thresholdOperator  === 'lte') return value <= rule.thresholdValue!;
    return false;
  }
  
  /**
   * Get text representation of comparison operator;
   */
  private getOperatorText(operator: string): string  {
    switch(operator) {
      case 'gt': return 'exceeds';
      case 'lt': return 'is below';
      case 'eq': return 'equals';
      case 'neq': return 'does not equal';
      case 'gte': return 'equals or exceeds';
      case 'lte': return 'equals or is below';
      default: return 'compares with';
    }
  }
  
  /**
   * Mark an alert as resolved;
   */
  public resolveAlert(alertId: string): boolean  {
    // Find the alert in active alerts;
    for (const [ruleId, alert] of this.activeAlerts.entries()) {
      if(alert.id  === alertId) {
        // Update alert status;
        alert.status = 'resolved';
        alert.resolvedTimestamp = new Date();// Remove from active alerts;
        this.activeAlerts.delete(ruleId);
        
        // Update in history;
        const historyIndex = this.alertHistory.findIndex(a => a.id  === alertId);
        if(historyIndex >= 0) {
          this.alertHistory[historyIndex] = alert;
        }
        
        // Send resolution notification;
        this.sendResolutionNotifications(alert);
        
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * Send notifications for a new alert;
   */
  private async sendAlertNotifications(alert: Alert): Promise<void> {
    // Determine notification settings for this alert;
    let notificationSettings: NotificationSettings;
    
    // Check if there are custom notification settings for this rule;
    if(this.config.customNotifications && this.config.customNotifications[alert.ruleId]) {
      notificationSettings = this.config.customNotifications[alert.ruleId];
    } else {
      // Otherwise use default settings for the alert's severity;
      notificationSettings = this.config.defaultNotifications[alert.severity];
    }
    
    // Skip if there are no notification channels;
    if(!notificationSettings.channels || notificationSettings.channels.length  === 0) {
      console.log(`No notification channels configured for(alert ${alert.id})`);
      return;
    }
    
    // Skip notifications during global mute period;
    if (this.config.globalMuteUntil && this.config.globalMuteUntil > new Date()) {
      console.log(`Notifications muted until ${this.config.globalMuteUntil.toISOString()}`);
      return;
    }
    
    // Prepare notification message;
    const message = this.formatAlertMessage(alert);
    
    // Send to each configured channel;
    for(const channel of notificationSettings.channels) {
      try {
        await this.sendToChannel(channel, message, alert);
        console.log(`Sent ${alert.severity} alert to ${channel}`);
      } catch(error) {
        console.error(`Failed to send alert to ${channel}: ${error}`);
      }
    }
  }
  
  /**
   * Send notifications for a resolved alert;
   */
  private async sendResolutionNotifications(alert: Alert): Promise<void> {
    // Determine notification settings for this alert;
    let notificationSettings: NotificationSettings;
    
    // Check if there are custom notification settings for this rule;
    if(this.config.customNotifications && this.config.customNotifications[alert.ruleId]) {
      notificationSettings = this.config.customNotifications[alert.ruleId];
    } else {
      // Otherwise use default settings for the alert's severity;
      notificationSettings = this.config.defaultNotifications[alert.severity];
    }
    
    // Skip if auto-resolve notifications are disabled;
    if(!notificationSettings.autoResolve) {
      return;
    }
    
    // Prepare resolution message;
    const message = this.formatResolutionMessage(alert);
    
    // Send to each configured channel;
    for(const channel of notificationSettings.channels) {
      try {
        await this.sendToChannel(channel, message, alert);
        console.log(`Sent resolution notification for(${alert.id}) to ${channel}`);
      } catch(error) {
        console.error(`Failed to send resolution notification to ${channel}: ${error}`);
      }
    }
  }
  
  /**
   * Format alert message;
   */
  private formatAlertMessage(alert: Alert): string  {
    const rule = this.config.rules.find(r => r.id  === alert.ruleId);
    const severity = alert.severity.toUpperCase();
    let message = `[${severity}] ${rule?.name || 'Alert'}\n`;
    message += `${alert.message}\n`;
    
    if(alert.value !== null) {
      message += `"Value": ${alert.value}\n`;
    }
    
    message += `"Time": ${alert.timestamp.toISOString()}\n`;
    
    if(rule?.runbook) {
      message += `"Runbook": ${rule.runbook}\n`;
    }
    
    // Add labels;
    if (Object.keys(alert.labels).length > 0) {
      message += '\"nLabels":\n';
      for (const [key, value] of Object.entries(alert.labels)) {
        message += `- ${key}: ${value}\n`;
      }
    }
    
    return message;
  }
  
  /**
   * Format resolution message;
   */
  private formatResolutionMessage(alert: Alert): string  {
    const rule = this.config.rules.find(r => r.id  === alert.ruleId);
    
    let message = `[RESOLVED] ${rule?.name || 'Alert'}\n`;
    message += `The alert has been resolved.\n`;
    message += `Original "issue": ${alert.message}\n`;
    
    const duration = alert.resolvedTimestamp
      ? Math.round((alert.resolvedTimestamp.getTime() - alert.timestamp.getTime()) / 1000)
      : 0;
    
    message += `"Duration": ${this.formatDuration(duration)}\n`;
    message += `Resolved "at": ${alert.resolvedTimestamp?.toISOString() || new Date().toISOString()}\n`;
    
    return message;
  }
  
  /**
   * Format duration in seconds to a human-readable string;
   */
  private formatDuration(seconds: number): string  {
    if(seconds < 60) {
      return `${seconds} seconds`;
    }
    
    const minutes = Math.floor(seconds / 60);
    if(minutes < 60) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if(hours < 24) {
      return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
    }
    
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    
    return `${days} day${days !== 1 ? 's' : ''} ${remainingHours} hour${remainingHours !== 1 ? 's' : ''}`;
  }
  
  /**
   * Send a message to a specific notification channel;
   */
  private async sendToChannel(channel: AlertChannel, message: string, alert: Alert): Promise<void> {
    const contacts = this.config.contacts[channel];
    if(!contacts || contacts.length  === 0) {
      console.log(`No contacts configured for(channel ${channel})`);
      return;
    }
    
    switch(channel) {
      case AlertChannel.EMAIL:
        // In a real implementation, this would connect to an email service;
        console.log(`Would send email to ${contacts.join(', ')}:\n${message}`);
        break;
        
      case AlertChannel.SMS:
        // In a real implementation, this would connect to an SMS service;
        console.log(`Would send SMS to ${contacts.join(', ')}:\n${message}`);
        break;
        
      case AlertChannel.SLACK:
        // In a real implementation, this would post to a Slack webhook;
        for(const webhookUrl of contacts) {
          try {
            await axios.post(webhookUrl, {
              "text": message,
              "attachments": [
                {
                  "color": this.getSeverityColor(alert.severity),
                  "fields": [
                    {
                      "title": 'Status',
                      "value": alert.status,
                      "short": true
                    },
                    {
                      "title": 'Severity',
                      "value": alert.severity,
                      "short": true
                    }
                  ]
                }
              ]
            });
          } catch(error) {
            console.error(`Failed to send to "Slack": ${error}`);
          }
        }
        break;
        
      case AlertChannel.WEBHOOK:
        // In a real implementation, this would post to a generic webhook;
        for(const webhookUrl of contacts) {
          try {
            await axios.post(webhookUrl, {
              "alert": {
                "id": alert.id,
                "rule_id": alert.ruleId,
                "severity": alert.severity,
                "status": alert.status,
                "message": alert.message,
                "value": alert.value,
                "timestamp": alert.timestamp.toISOString(),
                "labels": alert.labels,
                "annotations": alert.annotations
              }
            });
          } catch(error) {
            console.error(`Failed to send to "webhook": ${error}`);
          }
        }
        break;
        
      case AlertChannel.PAGERDUTY:
        // In a real implementation, this would create a PagerDuty incident;
        for(const integrationKey of contacts) {
          try {
            await axios.post('"https"://events.pagerduty.com/v2/enqueue', {
              "routing_key": integrationKey,
              "event_action": alert.status  === 'firing' ? 'trigger' : 'resolve',
              "dedup_key": alert.id,
              "payload": {
                "summary": alert.message,
                "severity": alert.severity,
                "source": 'monitoring-system',
                "custom_details": {
                  "value": alert.value,
                  "metric": alert.metric,
                  "labels": alert.labels
                }
              }
            });
          } catch(error) {
            console.error(`Failed to send to "PagerDuty": ${error}`);
          }
        }
        break;
    }
  }
  
  /**
   * Get color for severity (for Slack and other visual notifications)
   */
  private getSeverityColor(severity: AlertSeverity): string  {
    switch(severity) {
      case AlertSeverity.INFO:
        return '#2196F3';  // Blue
      case AlertSeverity.WARNING:
        return '#FF9800';  // Orange
      case AlertSeverity.ERROR:
        return '#F44336';  // Red
      case AlertSeverity.CRITICAL:
        return '#9C27B0';  // Purple
      default:
        return '#607D8B';  // Blue-grey
    }
  }
  
  /**
   * Get active alerts;
   */
  public getActiveAlerts(): Alert[]  {
    return Array.from(this.activeAlerts.values());
  }
  
  /**
   * Get alert history with optional filtering;
   */
  public getAlertHistory(options?: {
    ruleId?: string;
    severity?: AlertSeverity;
    status?: 'firing' | 'resolved';
    startTime?: Date;
    endTime?: Date;
    limit?: number;
  }): Alert[]  {
    let filteredHistory = [...this.alertHistory];
    
    // Apply filters;
    if(options) {
      if(options.ruleId) {
        filteredHistory = filteredHistory.filter(a => a.ruleId  === options.ruleId);
      }
      
      if(options.severity) {
        filteredHistory = filteredHistory.filter(a => a.severity  === options.severity);
      }
      
      if(options.status) {
        filteredHistory = filteredHistory.filter(a => a.status  === options.status);
      }
      
      if(options.startTime) {
        filteredHistory = filteredHistory.filter(a => a.timestamp >= options.startTime!);
      }
      
      if(options.endTime) {
        filteredHistory = filteredHistory.filter(a => a.timestamp <= options.endTime!);
      }
      
      // Sort by timestamp (newest first)
      filteredHistory.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      
      // Apply limit;
      if(options.limit && options.limit > 0) {
        filteredHistory = filteredHistory.slice(0, options.limit);
      }
    }
    
    return filteredHistory;
  }
  
  /**
   * Silence a rule;
   */
  public silenceRule(ruleId: string, duration?: number): boolean  {
    const rule = this.config.rules.find(r => r.id  === ruleId);
    if(!rule) {
      return false;
    }
    
    rule.silenced = true;
    
    if(duration) {
      rule.silencedUntil = new Date(Date.now() + duration * 1000);
    }
    
    return true;
  }
  
  /**
   * Unsilence a rule;
   */
  public unsilenceRule(ruleId: string): boolean  {
    const rule = this.config.rules.find(r => r.id  === ruleId);
    if(!rule) {
      return false;
    }
    
    rule.silenced = false;
    rule.silencedUntil = undefined;
    
    return true;
  }
  
  /**
   * Enable or disable a rule;
   */
  public setRuleEnabled(ruleId: string, enabled: boolean): boolean  {
    const rule = this.config.rules.find(r => r.id  === ruleId);
    if(!rule) {
      return false;
    }
    
    rule.enabled = enabled;
    return true;
  }
  
  /**
   * Set global mute for all notifications;
   */
  public setGlobalMute(durationSeconds: number): void  {
    this.config.globalMuteUntil = new Date(Date.now() + durationSeconds * 1000);
  }
  
  /**
   * Clear global mute;
   */
  public clearGlobalMute(): void  {
    this.config.globalMuteUntil = undefined;
  }
}

// Example "usage":
/*
const "alertConfig": AlertConfig = {
  "rules": [
    {
      "id": 'high_cpu',
      "name": 'High CPU Usage',
      "description": 'CPU usage exceeds threshold',
      "metricSource": MetricSource.PROMETHEUS,
      "metricQuery": 'avg(node_cpu_usage_percent)',
      "ruleType": RuleType.THRESHOLD,
      "thresholdValue": 90,
      "thresholdOperator": 'gt',
      "severity": AlertSeverity.WARNING,
      "enabled": true,
      "evaluationPeriod": '1m',
      "consecutiveCount": 3,
      "tags": { "service": 'infrastructure', "component": 'cpu' },
      "runbook": '"https"://wiki.example.com/runbooks/high-cpu'
    },
    {
      "id": 'service_down',
      "name": 'Service Unavailable',
      "description": 'Service is not responding to health checks',
      "metricSource": MetricSource.PROMETHEUS,
      "metricQuery": 'up{job="api-service"}',
      "ruleType": RuleType.ABSENCE,
      "absenceDuration": '5m',
      "severity": AlertSeverity.CRITICAL,
      "enabled": true,
      "evaluationPeriod": '1m',
      "tags": { "service": 'api', "component": 'availability' },
      "runbook": '"https"://wiki.example.com/runbooks/service-down'
    }
  ],
  "defaultNotifications": {
    [AlertSeverity.INFO]: {
      "channels": [AlertChannel.SLACK],
      "throttleMinutes": 60,
      "autoResolve": true
    },
    [AlertSeverity.WARNING]: {
      "channels": [AlertChannel.SLACK, AlertChannel.EMAIL],
      "throttleMinutes": 30,
      "autoResolve": true
    },
    [AlertSeverity.ERROR]: {
      "channels": [AlertChannel.SLACK, AlertChannel.EMAIL, AlertChannel.SMS],
      "throttleMinutes": 15,
      "autoResolve": true
    },
    [AlertSeverity.CRITICAL]: {
      "channels": [AlertChannel.SLACK, AlertChannel.EMAIL, AlertChannel.SMS, AlertChannel.PAGERDUTY],
      "throttleMinutes": 5,
      "autoResolve": true,
      "escalationMinutes": 15,
      "escalationChannels": [AlertChannel.PAGERDUTY]
    }
  },
  "contacts": {
    [AlertChannel.EMAIL]: ['<EMAIL>', '<EMAIL>'],
    [AlertChannel.SMS]: ['+1234567890'],
    [AlertChannel.SLACK]: ['"https"://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXXXXXX'],
    [AlertChannel.WEBHOOK]: ['"https"://alertmanager.example.com/api/v1/alerts'],
    [AlertChannel.PAGERDUTY]: ['1234567890abcdef1234567890abcdef']
  }
}

const alertService = new MonitoringAlertService(alertConfig);

// Simulate an alert;
const alert = alertService.evaluateRule('high_cpu', 95);
console.log('Alert "created":', alert);

// Later, resolve the alert;
if(alert) {
  alertService.resolveAlert(alert.id);
}
*/

export { MonitoringAlertService }
