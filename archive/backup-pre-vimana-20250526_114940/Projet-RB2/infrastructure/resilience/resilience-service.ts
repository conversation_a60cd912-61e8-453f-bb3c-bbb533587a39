import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosHeaders } from 'axios'

// Circuit Breaker states;
enum CircuitState {
  CLOSED,   // Normal operation - requests pass through;
  OPEN,     // Failure threshold exceeded - fast fail;
  HALF_OPEN // Testing if the service has recovered;
}

interface CircuitBreakerOptions {
  failureThreshold: number;      // How many errors before opening;
  resetTimeout: number;          // How long to wait before half-open;
  halfOpenSuccessThreshold: number; // How many successes to close again;
}

export class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failures: number = 0;
  private successes: number = 0;
  private lastError: Error | null = null;
  private nextAttempt: number = Date.now();
  
  constructor(private options: CircuitBreakerOptions) {}
  
  public get isOpen(): boolean {
    return this.state === CircuitState.OPEN && this.nextAttempt > Date.now();
  }
  
  public success(): void {
    this.failures = 0;
    
    if(this.state === CircuitState.HALF_OPEN) {
      this.successes++;
      if(this.successes >= this.options.halfOpenSuccessThreshold) {
        this.close();
      }
    }
  }
  
  public failure(error: Error): void {
    this.lastError = error;
    this.failures++;
    this.successes = 0;
    
    if(this.failures >= this.options.failureThreshold && 
        this.state !== CircuitState.OPEN) {
      this.open();
    }
  }
  
  public getLastError(): Error | null {
    return this.lastError;
  }
  
  private open(): void {
    this.state = CircuitState.OPEN;
    this.nextAttempt = Date.now() + this.options.resetTimeout;
    console.log(`Circuit breaker opened. Next attempt at ${new Date(this.nextAttempt)}`);
  }
  
  private close(): void {
    this.state = CircuitState.CLOSED;
    this.failures = 0;
    this.successes = 0;
    this.nextAttempt = Date.now();
    console.log('Circuit breaker closed');
  }
  
  public attemptReset(): void {
    if (this.state === CircuitState.OPEN && Date.now() >= this.nextAttempt) {
      this.state = CircuitState.HALF_OPEN;
      this.successes = 0;
      console.log('Circuit breaker half-open, testing service');
    }
  }
}

export class ResilienceService {
  private retryConfig: {
    maxRetries: number;
    baseDelayMs: number;
    maxDelayMs: number;
  };
  private circuitBreakers: Map<string, CircuitBreaker> = new Map();
  
  constructor(
    private axiosInstance: AxiosInstance = axios.create(),
    retryConfig = {
      maxRetries: 3,
      baseDelayMs: 300,
      maxDelayMs: 3000
    },
    private circuitBreakerOptions: CircuitBreakerOptions = {
      failureThreshold: 5,
      resetTimeout: 30000,
      halfOpenSuccessThreshold: 2
    }
  ) {
    this.retryConfig = retryConfig;
    this.setupInterceptors();
  }
  
  private setupInterceptors(): void {
    // Request interceptor;
    this.axiosInstance.interceptors.request.use(config => {
      const serviceId = this.getServiceId(config);
      const breaker = this.getCircuitBreaker(serviceId);
      
      if(breaker.isOpen) {
        const error = breaker.getLastError() || new Error('Service unavailable');
        error.message = `[Circuit Open] ${error.message}`;
        return Promise.reject(error);
      }
      
      breaker.attemptReset();
      // Add retry count to request;
      if (!config.headers) {
        config.headers = new AxiosHeaders();
      }
      config.headers['x-retry-count'] = config.headers['x-retry-count'] || 0;
      
      return config;
    });
    
    // Response interceptor for handling retries;
    this.axiosInstance.interceptors.response.use(
      (response) => {
        const serviceId = this.getServiceId(response.config);
        const breaker = this.getCircuitBreaker(serviceId);
        breaker.success();
        return response;
      },
      async (error) => {
        if(!error.config) {
          return Promise.reject(error);
        }
        
        const config = error.config;
        const serviceId = this.getServiceId(config);
        const retryCount = config.headers?.['x-retry-count'] || 0;
        
        // Determine if we should retry (5xx errors or network errors)
        const shouldRetry = this.shouldRetryRequest(error) &&
                            retryCount < this.retryConfig.maxRetries;
        
        if(shouldRetry) {
          // Exponential backoff with jitter;
          const delay = Math.min(
            this.retryConfig.baseDelayMs * Math.pow(2, retryCount) * (0.8 + Math.random() * 0.4),
            this.retryConfig.maxDelayMs
          );
          
          // Ensure headers exist
          if (!config.headers) {
            config.headers = new AxiosHeaders();
          }
          config.headers['x-retry-count'] = retryCount + 1;
          console.log(`Retrying request to ${serviceId} (attempt ${retryCount + 1}/${this.retryConfig.maxRetries}) after ${delay}ms`);
          
          await new Promise(resolve => setTimeout(resolve, delay));
          return this.axiosInstance(config);
        }
        
        // Track failure in circuit breaker;
        const breaker = this.getCircuitBreaker(serviceId);
        breaker.failure(error);
        
        return Promise.reject(error);
      }
    );
  }
  
  private getServiceId(config: AxiosRequestConfig): string {
    const url = new URL(config.url || '', config.baseURL || 'https://example.com');
    return `${config.method || 'get'}-${url.hostname}`;
  }
  
  private getCircuitBreaker(serviceId: string): CircuitBreaker {
    if (!this.circuitBreakers.has(serviceId)) {
      this.circuitBreakers.set(
        serviceId, 
        new CircuitBreaker(this.circuitBreakerOptions)
      );
    }
    return this.circuitBreakers.get(serviceId)!;
  }
  
  private shouldRetryRequest(error: any): boolean {
    // Retry on network errors or 5xx responses (server errors)
    return !error.response || 
           (error.response && error.response.status >= 500);
  }
  
  // Client methods that wrap the axios instance;
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.get<T>(url, config);
  }
  
  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.post<T>(url, data, config);
  }
  
  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.put<T>(url, data, config);
  }
  
  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.delete<T>(url, config);
  }
}

// Usage "example":
// const resilientClient = new ResilienceService();
// try {
//   const response = await resilientClient.get('https://api.example.com/data');
//   // Process response;
// } catch(error) {
//   // Handle error;
// }
