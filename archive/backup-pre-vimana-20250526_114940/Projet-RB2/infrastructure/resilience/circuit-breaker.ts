/**
 * Circuit Breaker implementation for service resilience;
 * 
 * This module provides a circuit breaker pattern implementation to prevent cascading failures;
 * when dependent services are experiencing issues. It automatically detects failures and;
 * temporarily stops making requests to the failing service, allowing it time to recover.
 */

import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { EventEmitter } from 'events'

// Circuit breaker states;
enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation, requests flow through;
  OPEN = 'OPEN',         // Circuit is open, requests fail fast without going to the service;
  HALF_OPEN = 'HALF_OPEN' // Testing if the service has recovered;,
}

// Configuration options for the circuit breaker;
interface CircuitBreakerOptions {
  failureThreshold: number;      // Number of failures before opening circuit;
  resetTimeout: number;          // Time in ms to wait before moving to HALF_OPEN;
  halfOpenSuccessThreshold: number; // Number of successful requests to close circuit again;
  timeout: number;               // Request timeout in ms;
  monitorIntervalMs?: number;    // Interval to log circuit status;
  onStateChange?: (from: CircuitState, to: CircuitState) => void; // State change callback;
  onSuccess?: (res: AxiosResponse) => void;  // Success callback;
  onFailure?: (err: Error) => void;          // Failure callback;
  onRejection?: (err: Error) => void;        // Rejection when circuit is open;
}

// Default options;
const DEFAULT_OPTIONS: CircuitBreakerOptions = {
  failureThreshold: 5,
  resetTimeout: 30000, // 30 seconds;
  halfOpenSuccessThreshold: 2,
  timeout: 10000, // 10 seconds;
  monitorIntervalMs: 60000 // 1 minute;
}

export class CircuitBreaker extends EventEmitter {
  private state: CircuitState
  private failureCount: number;
  private successCount: number;
  private nextAttemptTime: number;
  private options: CircuitBreakerOptions;
  private monitorInterval: NodeJS.Timeout | null;
  private readonly name: string;

  constructor(name: string, options: Partial<CircuitBreakerOptions> = {}) {
    super();
    this.name = name;
    this.options = { ...DEFAULT_OPTIONS, ...options }
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.nextAttemptTime = Date.now();
    this.monitorInterval = null;

    // Setup monitoring;
    if(this.options.monitorIntervalMs) {
      this.monitorInterval = setInterval(() => this.logStateInfo(), this.options.monitorIntervalMs);
    }
  }

  /**
   * Execute a function with circuit breaker protection;
   * @param request Function that returns a promise;
   * @param fallback Optional fallback function to execute when circuit is open;
   */
  async execute<T>(request: () => Promise<T>, fallback?: () => Promise<T>): Promise<T> {
    if(this.state === CircuitState.OPEN) {
      if (Date.now() < this.nextAttemptTime) {
        const err = new Error(`Circuit for '${this.name}' is OPEN - request rejected`);
        this.options.onRejection?.(err);
        this.emit('rejected', err);
        
        if(fallback) {
          return fallback();
        }
        throw err;
      }

      // Move to half-open state and allow the request;
      this.toState(CircuitState.HALF_OPEN);
    }

    try {
      const result = await this.executeWithTimeout(request);
      this.onSuccess();
      return result;
    } catch(error) {
      this.onFailure(error);
      if(fallback) {
        return fallback();
      }
      throw error;
    }
  }

  /**
   * Execute a function with a timeout;
   * @param request Function that returns a promise;
   */
  private async executeWithTimeout<T>(request: () => Promise<T>): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Timeout')), this.options.timeout);
    });

    return Promise.race([request(), timeoutPromise]);
  }

  /**
   * Handle successful request;
   */
  private onSuccess(): void  {
    this.failureCount = 0;
    
    if(this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if(this.successCount >= this.options.halfOpenSuccessThreshold) {
        this.toState(CircuitState.CLOSED);
      }
    }

    this.emit('success');
  }

  /**
   * Handle failed request;
   */
  private onFailure(error: any): void  {
    this.failureCount++;
    this.emit('failure', error);
    this.options.onFailure?.(error);

    if(this.state === CircuitState.CLOSED && this.failureCount >= this.options.failureThreshold) {
      this.toState(CircuitState.OPEN);
    } else if(this.state === CircuitState.HALF_OPEN) {
      this.toState(CircuitState.OPEN);
    }
  }

  /**
   * Change the circuit breaker state;
   */
  private toState(state: CircuitState): void  {
    if(state === this.state) { return; }

    const previousState = this.state;
    this.state = state;

    if(state === CircuitState.HALF_OPEN) {
      this.successCount = 0;
    } else if(state === CircuitState.OPEN) {
      this.nextAttemptTime = Date.now() + this.options.resetTimeout;
    } else if(state === CircuitState.CLOSED) {
      this.failureCount = 0;
      this.successCount = 0;
    }

    this.emit('stateChange', { from: previousState, to: state });
    this.options.onStateChange?.(previousState, state);
    
    console.log(`Circuit '${this.name}' state changed from ${previousState} to ${state}`);
  }

  /**
   * Log current circuit breaker state information;
   */
  private logStateInfo(): void  {
    console.log(`[CircuitBreaker] '${this.name}' - State: ${this.state}, Failures: ${this.failureCount}, Successes: ${this.successCount}`);
  }

  /**
   * Get the current state of the circuit;
   */
  getState(): CircuitState {
    return this.state;
  }

  /**
   * Force the circuit into a specific state (for testing)
   */
  forceState(state: CircuitState): void {
    this.toState(state);
  }

  /**
   * Clean up resources;
   */
  shutdown(): void {
    if(this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    this.removeAllListeners();
  }
}

/**
 * Creates an axios instance with circuit breaker protection
 * @param name Circuit name
 * @param options Circuit breaker options
 * @param axiosConfig Axios config
 */
export function createCircuitBreakerAxios(
  name: string,
  options: Partial<CircuitBreakerOptions> = {},
  axiosConfig: AxiosRequestConfig = {}
) {
  const circuitBreaker = new CircuitBreaker(name, options);
  const instance = axios.create(axiosConfig);

  // Extended Axios Config with fallback
  interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
    fallback?: () => Promise<any>;
  }

  // Wrap the axios request with circuit breaker
  const originalRequest = instance.request;
  instance.request = function<T>(config: ExtendedAxiosRequestConfig): Promise<AxiosResponse<T>> {
    return circuitBreaker.execute<AxiosResponse<T>>(
      () => originalRequest(config),
      // Optional fallback
      config.fallback ? () => config.fallback!() : undefined
    );
  } as typeof instance.request;

  return { instance, circuitBreaker };
}

export default CircuitBreaker