apiVersion: v1
kind: ConfigMap
metadata:
  name: staging-environment-config
  namespace: rb2-staging
data:
  # General configuration
  ENVIRONMENT: "staging"
  LOG_LEVEL: "debug"
  DEPLOYMENT_TIER: "staging"
  
  # API configuration
  API_RATE_LIMIT: "100"
  API_TIMEOUT: "30000"
  API_ENABLE_CACHE: "true"
  API_CACHE_TTL: "300"
  
  # Frontend configuration
  FRONTEND_URL: "https://staging.rb2-app.com"
  FRONTEND_API_URL: "https://api.staging.rb2-app.com"
  FRONTEND_ASSETS_URL: "https://assets.staging.rb2-app.com"
  FRONTEND_ENABLE_ANALYTICS: "true"
  
  # Monitoring configuration
  MONITORING_ENABLED: "true"
  METRICS_SAMPLE_RATE: "1.0"
  TRACING_SAMPLE_RATE: "1.0"
  
  # Feature flags
  FEATURE_NEW_DASHBOARD: "true"
  FEATURE_ADVANCED_ANALYTICS: "true"
  FEATURE_EXPERIMENTAL_API: "true"
  
  # Integration endpoints
  EXTERNAL_SERVICE_A_URL: "https://api-staging.external-service-a.com"
  EXTERNAL_SERVICE_B_URL: "https://api-staging.external-service-b.com"
  
  # Database connection (non-sensitive parts)
  DB_HOST: "postgres-staging.rb2-internal"
  DB_PORT: "5432"
  DB_NAME: "rb2_staging"
  DB_POOL_SIZE: "10"
  DB_IDLE_TIMEOUT: "10000"
  
  # Redis configuration
  REDIS_HOST: "redis-staging.rb2-internal"
  REDIS_PORT: "6379"
  REDIS_DB_INDEX: "0"
  
  # RabbitMQ configuration
  RABBITMQ_HOST: "rabbitmq-staging.rb2-internal"
  RABBITMQ_PORT: "5672"
  RABBITMQ_VHOST: "rb2-staging"
  
  # Object storage
  STORAGE_BUCKET: "rb2-staging-assets"
  STORAGE_REGION: "us-west-2"
  STORAGE_URL: "https://rb2-staging-assets.s3.us-west-2.amazonaws.com"
  
  # Logging configuration
  LOG_FORMAT: "json"
  LOG_OUTPUT: "stdout,file"
  LOG_FILE_PATH: "/var/log/rb2/app.log"
  LOG_MAX_SIZE: "100m"
  LOG_MAX_FILES: "10"
  
  # Metrics configuration
  METRICS_PORT: "9090"
  METRICS_PATH: "/metrics"
  
  # Health check configuration
  HEALTH_CHECK_PORT: "8080"
  HEALTH_CHECK_PATH: "/health"
  READINESS_CHECK_PATH: "/ready"
  LIVENESS_CHECK_PATH: "/alive"
  
  # Test data configuration
  TEST_DATA_ENABLED: "true"
  TEST_DATA_SEED: "12345"
  TEST_USERS_COUNT: "100"
  TEST_PRODUCTS_COUNT: "1000"
  TEST_ORDERS_COUNT: "500"
  
  # Performance tuning
  MAX_WORKERS: "4"
  WORKER_CONNECTIONS: "1024"
  CONNECTION_TIMEOUT: "5000"
  KEEP_ALIVE_TIMEOUT: "65000"
---
apiVersion: v1
kind: Secret
metadata:
  name: staging-environment-secrets
  namespace: rb2-staging
type: Opaque
stringData:
  # Database credentials
  DB_USER: "rb2_app"
  DB_PASSWORD: "staging_db_password_replace_me"
  
  # Redis authentication
  REDIS_PASSWORD: "staging_redis_password_replace_me"
  
  # RabbitMQ credentials
  RABBITMQ_USER: "rb2_app"
  RABBITMQ_PASSWORD: "staging_rabbitmq_password_replace_me"
  
  # API keys
  API_SECRET_KEY: "staging_api_secret_key_replace_me"
  JWT_SECRET: "staging_jwt_secret_replace_me"
  
  # External service credentials
  EXTERNAL_SERVICE_A_API_KEY: "staging_external_service_a_api_key_replace_me"
  EXTERNAL_SERVICE_B_API_KEY: "staging_external_service_b_api_key_replace_me"
  
  # Storage credentials
  STORAGE_ACCESS_KEY: "staging_storage_access_key_replace_me"
  STORAGE_SECRET_KEY: "staging_storage_secret_key_replace_me"
  
  # Encryption keys
  ENCRYPTION_KEY: "staging_encryption_key_replace_me"
  
  # SMTP configuration for notifications
  SMTP_HOST: "smtp.example.com"
  SMTP_PORT: "587"
  SMTP_USER: "<EMAIL>"
  SMTP_PASSWORD: "staging_smtp_password_replace_me"
  
  # Monitoring credentials
  MONITORING_API_KEY: "staging_monitoring_api_key_replace_me"
  LOGGING_API_KEY: "staging_logging_api_key_replace_me"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: staging-data-seeding-config
  namespace: rb2-staging
data:
  seed-database.js: |
    // Database seeding script for staging environment
    const { Client } = require('pg');
    const faker = require('faker');
    const crypto = require('crypto');
    
    // Configuration
    const config = {
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      database: process.env.DB_NAME
    };
    
    // Set seed for reproducible data
    faker.seed(parseInt(process.env.TEST_DATA_SEED, 10));
    
    async function seedDatabase() {
      const client = new Client(config);
      
      try {
        console.log('Connecting to database...');
        await client.connect();
        
        console.log('Connected. Starting data seeding...');
        
        // Create users
        console.log('Seeding users table...');
        const userCount = parseInt(process.env.TEST_USERS_COUNT, 10);
        
        for (let i = 0; i < userCount; i++) {
          const firstName = faker.name.firstName();
          const lastName = faker.name.lastName();
          const email = faker.internet.email(firstName, lastName, 'staging-example.com');
          const passwordHash = crypto.createHash('sha256').update(`password-${i}`).digest('hex');
          
          await client.query(
            'INSERT INTO users (first_name, last_name, email, password_hash, created_at) VALUES ($1, $2, $3, $4, $5) ON CONFLICT (email) DO NOTHING',
            [firstName, lastName, email, passwordHash, faker.date.past(2)]
          );
          
          if (i % 10 === 0) {
            console.log(`Created ${i}/${userCount} users...`);
          }
        }
        
        // Create products
        console.log('Seeding products table...');
        const productCount = parseInt(process.env.TEST_PRODUCTS_COUNT, 10);
        
        for (let i = 0; i < productCount; i++) {
          const name = faker.commerce.productName();
          const description = faker.commerce.productDescription();
          const price = parseFloat(faker.commerce.price());
          const category = faker.commerce.department();
          
          await client.query(
            'INSERT INTO products (name, description, price, category, created_at) VALUES ($1, $2, $3, $4, $5) RETURNING id',
            [name, description, price, category, faker.date.past(1)]
          );
          
          if (i % 100 === 0) {
            console.log(`Created ${i}/${productCount} products...`);
          }
        }
        
        // Create orders
        console.log('Seeding orders table...');
        const orderCount = parseInt(process.env.TEST_ORDERS_COUNT, 10);
        
        // Get all user IDs
        const userResult = await client.query('SELECT id FROM users');
        const userIds = userResult.rows.map(row => row.id);
        
        // Get all product IDs
        const productResult = await client.query('SELECT id FROM products');
        const productIds = productResult.rows.map(row => row.id);
        
        for (let i = 0; i < orderCount; i++) {
          // Random user
          const userId = userIds[Math.floor(Math.random() * userIds.length)];
          
          // Create order
          const orderDate = faker.date.past(1);
          const status = faker.random.arrayElement(['pending', 'processing', 'shipped', 'delivered', 'cancelled']);
          
          const orderResult = await client.query(
            'INSERT INTO orders (user_id, order_date, status, created_at) VALUES ($1, $2, $3, $4) RETURNING id',
            [userId, orderDate, status, orderDate]
          );
          
          const orderId = orderResult.rows[0].id;
          
          // Add 1-5 order items
          const itemCount = Math.floor(Math.random() * 5) + 1;
          
          for (let j = 0; j < itemCount; j++) {
            const productId = productIds[Math.floor(Math.random() * productIds.length)];
            const quantity = Math.floor(Math.random() * 5) + 1;
            const price = parseFloat(faker.commerce.price());
            
            await client.query(
              'INSERT INTO order_items (order_id, product_id, quantity, price, created_at) VALUES ($1, $2, $3, $4, $5)',
              [orderId, productId, quantity, price, orderDate]
            );
          }
          
          if (i % 50 === 0) {
            console.log(`Created ${i}/${orderCount} orders...`);
          }
        }
        
        console.log('Data seeding completed successfully!');
        
      } catch (err) {
        console.error('Error seeding database:', err);
        process.exit(1);
      } finally {
        await client.end();
      }
    }
    
    seedDatabase();
  
  seed-cache.js: |
    // Redis cache seeding script for staging environment
    const Redis = require('ioredis');
    const faker = require('faker');
    
    // Configuration
    const config = {
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB_INDEX)
    };
    
    // Set seed for reproducible data
    faker.seed(parseInt(process.env.TEST_DATA_SEED, 10));
    
    async function seedCache() {
      const redis = new Redis(config);
      
      try {
        console.log('Connecting to Redis...');
        
        console.log('Connected. Starting cache seeding...');
        
        // Seed common config values
        console.log('Seeding configuration cache...');
        await redis.set('config:maintenance_mode', 'false');
        await redis.set('config:announcement', JSON.stringify({
          enabled: true,
          message: 'Welcome to the staging environment!',
          type: 'info'
        }));
        
        // Seed feature flags
        console.log('Seeding feature flags...');
        await redis.set('feature:new_dashboard', 'true');
        await redis.set('feature:advanced_analytics', 'true');
        await redis.set('feature:experimental_api', 'true');
        
        // Seed popular products cache
        console.log('Seeding popular products cache...');
        const popularProducts = [];
        
        for (let i = 0; i < 20; i++) {
          popularProducts.push({
            id: i + 1,
            name: faker.commerce.productName(),
            price: parseFloat(faker.commerce.price()),
            rating: (Math.random() * 5).toFixed(1),
            reviewCount: Math.floor(Math.random() * 1000)
          });
        }
        
        await redis.set('cache:popular_products', JSON.stringify(popularProducts));
        await redis.expire('cache:popular_products', 3600); // 1 hour TTL
        
        // Seed categories with counts
        console.log('Seeding categories cache...');
        const categories = {};
        
        for (let i = 0; i < 10; i++) {
          const category = faker.commerce.department();
          categories[category] = Math.floor(Math.random() * 500) + 50;
        }
        
        await redis.set('cache:categories', JSON.stringify(categories));
        await redis.expire('cache:categories', 3600); // 1 hour TTL
        
        // Seed some user sessions
        console.log('Seeding user sessions...');
        
        for (let i = 0; i < 50; i++) {
          const sessionId = `sess:${faker.random.uuid()}`;
          const userData = {
            userId: i + 1,
            email: faker.internet.email(),
            firstName: faker.name.firstName(),
            lastName: faker.name.lastName(),
            lastActive: new Date().toISOString(),
            permissions: ['read', 'write']
          };
          
          await redis.set(sessionId, JSON.stringify(userData));
          await redis.expire(sessionId, 86400); // 24 hours TTL
        }
        
        // Seed rate limiting data
        console.log('Seeding rate limiting data...');
        
        for (let i = 0; i < 20; i++) {
          const ip = faker.internet.ip();
          await redis.set(`ratelimit:${ip}`, Math.floor(Math.random() * 100));
          await redis.expire(`ratelimit:${ip}`, 60); // 1 minute TTL
        }
        
        console.log('Cache seeding completed successfully!');
        
      } catch (err) {
        console.error('Error seeding cache:', err);
        process.exit(1);
      } finally {
        redis.disconnect();
      }
    }
    
    seedCache();
---
apiVersion: batch/v1
kind: Job
metadata:
  name: staging-data-seeding
  namespace: rb2-staging
spec:
  backoffLimit: 3
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: db-seeder
        image: node:16
        workingDir: /app
        env:
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: staging-environment-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: staging-environment-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: DB_NAME
        - name: TEST_DATA_SEED
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: TEST_DATA_SEED
        - name: TEST_USERS_COUNT
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: TEST_USERS_COUNT
        - name: TEST_PRODUCTS_COUNT
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: TEST_PRODUCTS_COUNT
        - name: TEST_ORDERS_COUNT
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: TEST_ORDERS_COUNT
        command:
        - sh
        - -c
        - |
          npm init -y
          npm install pg faker@5.5.3
          cat /scripts/seed-database.js > seed-database.js
          node seed-database.js
        volumeMounts:
        - name: seed-scripts
          mountPath: /scripts
      - name: cache-seeder
        image: node:16
        workingDir: /app
        env:
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: REDIS_PORT
        - name: REDIS_DB_INDEX
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: REDIS_DB_INDEX
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: staging-environment-secrets
              key: REDIS_PASSWORD
        - name: TEST_DATA_SEED
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: TEST_DATA_SEED
        command:
        - sh
        - -c
        - |
          npm init -y
          npm install ioredis faker@5.5.3
          cat /scripts/seed-cache.js > seed-cache.js
          node seed-cache.js
        volumeMounts:
        - name: seed-scripts
          mountPath: /scripts
      volumes:
      - name: seed-scripts
        configMap:
          name: staging-data-seeding-config
