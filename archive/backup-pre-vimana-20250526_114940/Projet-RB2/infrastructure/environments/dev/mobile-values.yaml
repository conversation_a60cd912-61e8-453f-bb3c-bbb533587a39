# Valeurs pour l'environnement de développement
replicaCount: 1

image:
  repository: rb2-app/mobile-service
  tag: "dev"

resources:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 200m
    memory: 512Mi

autoscaling:
  enabled: false

env:
  NODE_ENV: development
  LOG_LEVEL: debug
  ENABLE_MOCK_DATA: "true"

persistence:
  enabled: true
  size: 5Gi

configMap:
  data:
    config.json: |
      {
        "buildService": {
          "androidEnabled": true,
          "iosEnabled": true,
          "webhookUrl": "https://api-dev.rb2.com/webhooks/mobile-build"
        },
        "monitoring": {
          "enabled": true,
          "endpoint": "https://monitoring-dev.rb2.com/api/mobile"
        },
        "features": {
          "enableDevTools": true,
          "enableDebugMode": true
        }
      }
