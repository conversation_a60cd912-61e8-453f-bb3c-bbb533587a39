# Valeurs pour l'environnement de production
replicaCount: 2

image:
  repository: rb2-app/mobile-service
  tag: "stable"

resources:
  limits:
    cpu: 4000m
    memory: 8Gi
  requests:
    cpu: 1000m
    memory: 2Gi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 70

service:
  type: ClusterIP

ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: mobile-build.rb2.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: mobile-build-tls
      hosts:
        - mobile-build.rb2.com

env:
  NODE_ENV: production
  LOG_LEVEL: info
  ENABLE_MOCK_DATA: "false"

persistence:
  enabled: true
  size: 20Gi
  storageClass: "premium-rwo"

configMap:
  data:
    config.json: |
      {
        "buildService": {
          "androidEnabled": true,
          "iosEnabled": true,
          "webhookUrl": "https://api.rb2.com/webhooks/mobile-build"
        },
        "monitoring": {
          "enabled": true,
          "endpoint": "https://monitoring.rb2.com/api/mobile"
        },
        "security": {
          "enableAuditLogs": true,
          "complianceChecks": true,
          "encryptionEnabled": true
        },
        "features": {
          "enableDevTools": false,
          "enableDebugMode": false
        }
      }
