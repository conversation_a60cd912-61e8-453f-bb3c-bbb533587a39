import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Créer l'application Express
const app = express();

// Servir les fichiers statiques depuis le dossier public
app.use(express.static(path.join(__dirname, 'public')));

// Route de test spécifique pour vérifier la fonctionnalité
app.get('/api/hello', (req, res) => {
  res.json({ message: 'Hello from API' });
});

// Route de secours pour SPA - toutes les routes non reconnues sont envoyées vers index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Démarrer le serveur sur un port différent pour éviter les conflits
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`
╔════════════════════════════════════════════════════════╗
║                                                        ║
║   🚀 Serveur de test démarré sur port ${PORT}               ║
║   🌐 http://localhost:${PORT}                              ║
║                                                        ║
║   📝 Toutes les routes sont redirigées vers index.html  ║
║   🔍 URLs à tester:                                     ║
║      http://localhost:${PORT}/test.html                    ║
║      http://localhost:${PORT}/test                         ║
║      http://localhost:${PORT}/login                        ║
║      http://localhost:${PORT}/dashboard                    ║
║                                                        ║
╚════════════════════════════════════════════════════════╝
`);
}); 