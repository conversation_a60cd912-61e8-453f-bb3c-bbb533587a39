module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/integration/**/*.fixed.test.ts'],
  modulePathIgnorePatterns: [
    '<rootDir>/backup/',
    '<rootDir>/node_modules/'
  ],
  haste: {
    hasteImplModulePath: null,
    providesModuleNodeModules: []
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/Backend/src/$1'
  },
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.json'
    }]
  }
};
