#!/bin/bash

# Script pour mettre à jour les imports après restructuration
echo "Mise à jour des imports dans les fichiers..."

# Frontend
echo "Mise à jour des imports dans les fichiers frontend..."
find frontend/src -type f -name "*.ts*" -exec sed -i '' 's|from "../src/|from "../|g' {} \;
find frontend/src -type f -name "*.ts*" -exec sed -i '' 's|from "../../src/|from "../../|g' {} \;
find frontend/src -type f -name "*.ts*" -exec sed -i '' 's|from "../../../src/|from "../../../|g' {} \;

# Backend
echo "Mise à jour des imports dans les fichiers backend..."
find backend/src -type f -name "*.ts" -exec sed -i '' 's|from "../src/|from "../|g' {} \;
find backend/src -type f -name "*.ts" -exec sed -i '' 's|from "../../src/|from "../../|g' {} \;
find backend/src -type f -name "*.ts" -exec sed -i '' 's|from "../../../src/|from "../../../|g' {} \;

# Mise à jour des imports relatifs entre modules
echo "Mise à jour des imports relatifs entre modules..."

# Vérifier que les imports fonctionnent
echo "Vérification terminée. Veuillez tester la compilation du projet."