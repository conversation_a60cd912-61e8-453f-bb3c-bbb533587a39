#!/usr/bin/env python3
"""
Test simple pour vérifier que les importations fonctionnent correctement.
"""
import sys
import os

# Ajouter le répertoire parent au chemin de recherche
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    print("Tentative d'importation des modules superagent...")
    
    # Essayer d'importer quelques modules clés
    from superagent.graph.types import State
    print("✅ Importation de superagent.graph.types réussie")
    
    from superagent.config import settings
    print("✅ Importation de superagent.config.settings réussie")
    
    # Essayer d'importer un module d'agent si disponible
    try:
        from superagent.agents.nodes import coordinator_node
        print("✅ Importation de superagent.agents.nodes.coordinator_node réussie")
    except ImportError as e:
        print(f"❌ Erreur lors de l'importation de superagent.agents.nodes.coordinator_node: {e}")
    
    print("Tests d'importation terminés avec succès.")
    sys.exit(0)
except ImportError as e:
    print(f"❌ Erreur d'importation: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Erreur inattendue: {e}")
    sys.exit(2)
