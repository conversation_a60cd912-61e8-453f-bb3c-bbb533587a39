#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

// Parse command line arguments
const args = process.argv.slice(2);
let customDirs = [];
let focusErrorType = null;
let resumeMode = false;
let maxErrors = Infinity;
let skipFiles = [];

// Process command line arguments
for (let i = 0; i < args.length; i++) {
  if (args[i] === '--dir' && args[i + 1]) {
    customDirs.push(args[i + 1]);
    i++;
  } else if (args[i] === '--error-type' && args[i + 1]) {
    focusErrorType = args[i + 1];
    i++;
  } else if (args[i] === '--resume') {
    resumeMode = true;
  } else if (args[i] === '--max-errors' && args[i + 1]) {
    maxErrors = parseInt(args[i + 1], 10);
    i++;
  } else if (args[i] === '--help') {
    console.log(`
TypeScript Error Correction Tool

Usage:
  node ts-error-fixer.js [options]

Options:
  --dir <path>         Specify a directory to scan (can be used multiple times)
  --error-type <type>  Focus on specific error type (e.g., TS2345)
  --resume             Resume from last session
  --max-errors <num>   Maximum number of errors to fix
  --help               Show this help message
`);
    process.exit(0);
  }
}

// Create an interface to read user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const defaultDirs = [
  './frontend/src',
  './src',
  './Backend',
  './mobile',
  './packages'
];

// Use custom directories if provided, otherwise use defaults
const projectDirs = customDirs.length > 0 ? customDirs : defaultDirs;
const tsExtensions = ['.ts', '.tsx'];
let totalErrorsFixed = 0;
let totalErrorsFound = 0;
let totalFilesWithErrors = 0;
let totalFilesChecked = 0;

// Progress tracking
const progressFile = '.ts-error-fixer-progress.json';

// Load progress if in resume mode
if (resumeMode && fs.existsSync(progressFile)) {
  try {
    const progress = JSON.parse(fs.readFileSync(progressFile, 'utf-8'));
    skipFiles = progress.processedFiles || [];
    totalErrorsFixed = progress.totalErrorsFixed || 0;
    console.log(`Resuming from previous session. ${skipFiles.length} files already processed.`);
  } catch (error) {
    console.log(`Error loading progress file: ${error.message}`);
  }
}

// ANSI color codes for better terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m'
};

// Common TypeScript error suggestions
const errorSuggestions = {
  // Type errors
  'TS2322': 'Try using type assertion (as Type) or check if the types are compatible',
  'TS2345': 'Check the argument types in your function call',
  'TS2339': 'The property may not exist on the type. Check for typos or add a type declaration',
  'TS2531': 'Object is possibly null. Use optional chaining (obj?.prop) or nullish coalescing (obj ?? defaultValue)',
  'TS2532': 'Object is possibly undefined. Use optional chaining (obj?.prop) or nullish coalescing (obj ?? defaultValue)',
  'TS2571': 'Object is of type unknown. Use type assertion (as Type) or type guard (if (typeof obj === "type"))',

  // Syntax errors
  'TS1005': 'Check for missing semicolons, parentheses, or brackets',
  'TS1128': 'Declaration or statement expected. Check for missing semicolons or brackets',
  'TS1259': 'Use import * as React from "react" instead of import React from "react"',

  // Import errors
  'TS2307': 'Module not found. Check the import path or install the package',
  'TS1192': 'Module has no default export. Use named imports instead: import { Something } from "module"',

  // JSX errors
  'TS2786': 'JSX element attributes type may be incorrect. Check component prop types',
  'TS2607': 'JSX element class does not support attributes. Check component implementation',
  'TS2322': 'Type mismatch in JSX attributes. Check component prop types'
};

// Function to find all TypeScript files in the project
function findTypeScriptFiles(dirs) {
  let results = [];

  for (const dir of dirs) {
    if (!fs.existsSync(dir)) {
      console.log(`${colors.yellow}Directory ${dir} does not exist, skipping...${colors.reset}`);
      continue;
    }

    try {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const itemPath = path.join(dir, item);

        try {
          const stat = fs.statSync(itemPath);

          if (stat.isDirectory() && !itemPath.includes('node_modules') && !itemPath.includes('.git')) {
            results = results.concat(findTypeScriptFiles([itemPath]));
          } else if (stat.isFile() && tsExtensions.includes(path.extname(itemPath))) {
            results.push(itemPath);
          }
        } catch (error) {
          console.log(`${colors.yellow}Error accessing ${itemPath}: ${error.message}${colors.reset}`);
        }
      }
    } catch (error) {
      console.log(`${colors.yellow}Error reading directory ${dir}: ${error.message}${colors.reset}`);
    }
  }

  return results;
}

// Function to execute the TypeScript compiler and get errors
function getTypeScriptErrors(filePath) {
  try {
    // Use --noEmit to only check for errors without generating output files
    // Use --pretty false to get plain text output that's easier to parse
    execSync(`npx tsc --noEmit --pretty false "${filePath}"`, { encoding: 'utf-8', stdio: ['pipe', 'pipe', 'pipe'] });
    return [];
  } catch (error) {
    if (!error.stdout) {
      console.log(`${colors.yellow}Warning: No stdout from TypeScript compiler for ${filePath}${colors.reset}`);
      return [];
    }

    // Extract errors from the error message
    const errorLines = error.stdout.split('\n').filter(line => line.includes(filePath));

    const errors = errorLines.map(line => {
      // Match pattern like: file.ts(10,20): error TS2345: Error message
      const match = line.match(/\((\d+),(\d+)\): error TS(\d+): (.*)/);
      if (match) {
        return {
          line: parseInt(match[1]),
          column: parseInt(match[2]),
          code: `TS${match[3]}`,
          message: match[4]
        };
      }
      return null;
    }).filter(Boolean);

    // Filter by error type if specified
    if (focusErrorType) {
      return errors.filter(error => error.code === focusErrorType);
    }

    return errors;
  }
}

// Function to save progress
function saveProgress(processedFiles) {
  const progress = {
    processedFiles,
    totalErrorsFixed,
    lastRun: new Date().toISOString()
  };

  fs.writeFileSync(progressFile, JSON.stringify(progress, null, 2), 'utf-8');
}

// Function to ask the user to fix an error
function promptErrorFix(filePath, error) {
  return new Promise((resolve) => {
    try {
      const fileContent = fs.readFileSync(filePath, 'utf-8').split('\n');
      const errorLine = fileContent[error.line - 1];

      console.log('\n' + '-'.repeat(80));
      console.log(`${colors.bright}File:${colors.reset} ${colors.cyan}${filePath}${colors.reset}`);
      console.log(`${colors.bright}Error:${colors.reset} ${colors.red}${error.code}${colors.reset} - ${error.message}`);
      console.log(`${colors.bright}Line ${error.line}, Column ${error.column}:${colors.reset}`);

      // Display some lines before and after for context
      const startLine = Math.max(0, error.line - 3);
      const endLine = Math.min(fileContent.length, error.line + 2);

      for (let i = startLine; i < endLine; i++) {
        if (i === error.line - 1) {
          // Highlight the error line
          console.log(`${colors.bgRed}> ${i + 1}: ${fileContent[i]}${colors.reset}`);

          // Create a pointer to the exact position of the error
          const pointer = ' '.repeat(error.column + 3 + String(i + 1).length) + `${colors.green}^${colors.reset}`;
          console.log(pointer);
        } else {
          console.log(`  ${i + 1}: ${fileContent[i]}`);
        }
      }

      // Show suggestion if available
      if (errorSuggestions[error.code]) {
        console.log(`${colors.bright}${colors.cyan}Suggestion:${colors.reset} ${errorSuggestions[error.code]}`);
      }

      // Generate a potential fix based on the error type
      let suggestedFix = '';

      // Try to generate a suggested fix based on common patterns
      if (error.code === 'TS1259' && errorLine.includes('import React from')) {
        suggestedFix = errorLine.replace('import React from', 'import * as React from');
      } else if (error.code === 'TS2531' || error.code === 'TS2532') {
        // For null/undefined errors, suggest optional chaining
        const match = errorLine.match(/([a-zA-Z0-9_]+)\.([a-zA-Z0-9_]+)/);
        if (match) {
          suggestedFix = errorLine.replace(`${match[1]}.${match[2]}`, `${match[1]}?.${match[2]}`);
        }
      } else if (error.code === 'TS2322' && errorLine.includes(':')) {
        // For type mismatches, suggest type assertion
        const parts = errorLine.split(':');
        if (parts.length >= 2) {
          const varName = parts[0].trim();
          suggestedFix = `${varName} as any; // TODO: Fix type assertion`;
        }
      }

      // Show the suggested fix if available
      if (suggestedFix) {
        console.log(`${colors.bright}${colors.green}Suggested fix:${colors.reset} ${suggestedFix}`);
      }

      const options = [
        `${colors.green}s${colors.reset} - Skip this error`,
        `${colors.yellow}a${colors.reset} - Apply suggested fix (if available)`,
        `${colors.red}q${colors.reset} - Quit the script`,
        `${colors.blue}h${colors.reset} - Show help for this error type`,
        `Or enter your correction directly`
      ];

      console.log('\nOptions:');
      options.forEach(option => console.log(`  ${option}`));

      rl.question(`\n${colors.bright}Your choice:${colors.reset} `, (answer) => {
        if (answer.toLowerCase() === 'q') {
          console.log(`${colors.yellow}Stopping the script...${colors.reset}`);
          // Save progress before exiting
          if (resumeMode) {
            saveProgress(skipFiles);
            console.log(`${colors.green}Progress saved. You can resume later with --resume flag.${colors.reset}`);
          }
          rl.close();
          process.exit(0);
        } else if (answer.toLowerCase() === 's') {
          console.log(`${colors.yellow}Error skipped${colors.reset}`);
          resolve();
        } else if (answer.toLowerCase() === 'a' && suggestedFix) {
          fileContent[error.line - 1] = suggestedFix;
          fs.writeFileSync(filePath, fileContent.join('\n'), 'utf-8');
          totalErrorsFixed++;
          console.log(`${colors.green}Suggested fix applied!${colors.reset}`);
          resolve();
        } else if (answer.toLowerCase() === 'h') {
          // Show more detailed help for this error type
          console.log('\n' + '-'.repeat(40));
          console.log(`${colors.bright}${colors.cyan}Help for ${error.code}:${colors.reset}`);

          if (error.code === 'TS1259') {
            console.log(`This error occurs when using default imports with CommonJS modules.\nTry using: import * as React from 'react' instead.`);
          } else if (error.code === 'TS2531' || error.code === 'TS2532') {
            console.log(`This error occurs when accessing properties on potentially null/undefined objects.\nSolutions:\n1. Use optional chaining: obj?.prop\n2. Add a null check: if (obj) { obj.prop }\n3. Use nullish coalescing: obj ?? defaultValue`);
          } else if (error.code === 'TS2322') {
            console.log(`This is a type mismatch error.\nSolutions:\n1. Fix the type to match what's expected\n2. Use type assertion (as Type) if you're sure about the type\n3. Update the receiving variable's type`);
          } else {
            console.log(`General advice: Check the TypeScript documentation for error ${error.code}.`);
          }

          console.log('-'.repeat(40));

          // Ask again after showing help
          promptErrorFix(filePath, error).then(resolve);
          return;
        } else if (answer.trim() !== '') {
          fileContent[error.line - 1] = answer;
          fs.writeFileSync(filePath, fileContent.join('\n'), 'utf-8');
          totalErrorsFixed++;
          console.log(`${colors.green}Correction applied!${colors.reset}`);
          resolve();
        } else {
          console.log(`${colors.yellow}No changes made. Error skipped.${colors.reset}`);
          resolve();
        }
      });
    } catch (error) {
      console.log(`${colors.red}Error while processing file ${filePath}: ${error.message}${colors.reset}`);
      resolve();
    }
  });
}

// Function to check if an error is fixed
function isErrorFixed(filePath, originalError) {
  const errors = getTypeScriptErrors(filePath);

  // Check if the specific error is no longer present
  return !errors.some(error =>
    error.line === originalError.line &&
    error.code === originalError.code
  );
}

// Main function
async function main() {
  console.log(`${colors.bright}${colors.cyan}TypeScript Error Correction Tool${colors.reset}`);
  console.log('='.repeat(50));

  console.log(`${colors.bright}Searching for TypeScript files in the project...${colors.reset}`);
  const tsFiles = findTypeScriptFiles(projectDirs);
  console.log(`${colors.green}${tsFiles.length} TypeScript files found.${colors.reset}`);

  // Filter out already processed files if in resume mode
  const filesToProcess = resumeMode ? tsFiles.filter(file => !skipFiles.includes(file)) : tsFiles;

  if (resumeMode) {
    console.log(`${colors.cyan}${tsFiles.length - filesToProcess.length} files skipped from previous session.${colors.reset}`);
  }

  console.log(`${colors.bright}Will process ${filesToProcess.length} files.${colors.reset}`);

  // Ask for confirmation before starting
  await new Promise((resolve) => {
    rl.question(`\n${colors.bright}Press Enter to start or 'q' to quit:${colors.reset} `, (answer) => {
      if (answer.toLowerCase() === 'q') {
        console.log(`${colors.yellow}Exiting...${colors.reset}`);
        rl.close();
        process.exit(0);
      }
      resolve();
    });
  });

  let errorCount = 0;

  // Process each file
  for (const file of filesToProcess) {
    totalFilesChecked++;
    console.log(`\n${colors.bright}Analyzing:${colors.reset} ${colors.cyan}${file}${colors.reset} (${totalFilesChecked}/${filesToProcess.length})`);

    const errors = getTypeScriptErrors(file);

    if (errors.length === 0) {
      console.log(`${colors.green}No errors found!${colors.reset}`);
      skipFiles.push(file);
      if (resumeMode && totalFilesChecked % 10 === 0) {
        saveProgress(skipFiles);
      }
      continue;
    }

    totalFilesWithErrors++;
    totalErrorsFound += errors.length;
    console.log(`${colors.yellow}${errors.length} errors found.${colors.reset}`);

    // Process each error
    for (let i = 0; i < errors.length; i++) {
      const error = errors[i];
      errorCount++;

      // Check if we've reached the maximum number of errors to fix
      if (errorCount > maxErrors) {
        console.log(`${colors.yellow}Maximum number of errors (${maxErrors}) reached. Stopping.${colors.reset}`);
        break;
      }

      console.log(`${colors.bright}Processing error ${i + 1}/${errors.length} (Total: ${errorCount})${colors.reset}`);

      await promptErrorFix(file, error);

      // Check if the error is fixed
      if (isErrorFixed(file, error)) {
        console.log(`${colors.green}Error resolved!${colors.reset}`);
      } else {
        console.log(`${colors.yellow}Error may still exist. Moving to the next error.${colors.reset}`);
      }
    }

    // Add file to processed list and save progress periodically
    skipFiles.push(file);
    if (resumeMode && totalFilesChecked % 5 === 0) {
      saveProgress(skipFiles);
      console.log(`${colors.dim}Progress saved.${colors.reset}`);
    }

    // Check if we've reached the maximum number of errors to fix
    if (errorCount >= maxErrors) {
      break;
    }
  }

  // Save final progress if in resume mode
  if (resumeMode) {
    saveProgress(skipFiles);
  }

  // Print summary
  console.log('\n' + '='.repeat(80));
  console.log(`${colors.bright}${colors.green}Scan completed!${colors.reset}`);
  console.log(`${colors.bright}Files checked:${colors.reset} ${totalFilesChecked} of ${tsFiles.length}`);
  console.log(`${colors.bright}Files with errors:${colors.reset} ${totalFilesWithErrors}`);
  console.log(`${colors.bright}Total errors found:${colors.reset} ${totalErrorsFound}`);
  console.log(`${colors.bright}Errors fixed:${colors.reset} ${totalErrorsFixed}`);

  if (resumeMode) {
    console.log(`\n${colors.cyan}Progress saved. You can resume later with --resume flag.${colors.reset}`);
  }

  rl.close();
}

// Start the script
main().catch(err => {
  console.error(`${colors.red}An error occurred:${colors.reset}`, err);
  rl.close();
});
