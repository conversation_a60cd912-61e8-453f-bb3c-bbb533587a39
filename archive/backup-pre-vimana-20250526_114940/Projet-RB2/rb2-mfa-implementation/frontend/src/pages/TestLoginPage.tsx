import React from 'react';
import { Typo<PERSON>, Row, Col, Card, List, Space, Divider } from 'antd';
import { Link } from 'react-router-dom';
import MockLoginForm from '../components/auth/MockLoginForm';
import { MFAService } from '../services/auth/mfa.service';
import { 
  UserOutlined, 
  SafetyOutlined, 
  LockOutlined, 
  SecurityScanOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

interface TestFeature {
  id: string;
  title: string;
  description: string;
  path: string;
  icon: React.ReactNode;
}

const TestLoginPage: React.FC = () => {
  const mfaService = MFAService.getInstance();
  
  const testFeatures: TestFeature[] = [
    {
      id: 'setup-mfa',
      title: 'Set Up MFA',
      description: 'Test the MFA setup flow including QR code generation',
      path: '/setup-mfa',
      icon: <SafetyOutlined />
    },
    {
      id: 'verify-mfa',
      title: 'Verify MFA',
      description: 'Test the MFA verification flow',
      path: '/verify-mfa',
      icon: <LockOutlined />
    },
    {
      id: 'security-settings',
      title: 'Security Settings',
      description: 'Test the security settings page including MFA management',
      path: '/settings',
      icon: <SecurityScanOutlined />
    },
    {
      id: 'mfa-test',
      title: 'MFA Test Component',
      description: 'Direct testing of MFA hooks and service methods',
      path: '/mfa-test',
      icon: <SettingOutlined />
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ textAlign: 'center', marginBottom: '32px' }}>
        MFA Testing Environment
      </Title>
      
      <Row gutter={[24, 24]}>
        <Col xs={24} md={12}>
          <MockLoginForm />
          
          <Card style={{ marginTop: '24px' }}>
            <Title level={4}>Testing Tips</Title>
            <Paragraph>
              <ul>
                <li>Create a mock user with MFA enabled to test the verification flow</li>
                <li>Valid test codes are: 123456, 000000, 111111</li>
                <li>Add custom test codes in the MFA Test Component</li>
                <li>Test both login paths: with and without MFA</li>
                <li>You can use the MFA Test Component to verify all MFA functionality</li>
              </ul>
            </Paragraph>
          </Card>
        </Col>
        
        <Col xs={24} md={12}>
          <Card>
            <Title level={4}>Available Test Features</Title>
            <Paragraph>
              After logging in, you can test these MFA-related features:
            </Paragraph>
            
            <List
              itemLayout="horizontal"
              dataSource={testFeatures}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={item.icon}
                    title={<Link to={item.path}>{item.title}</Link>}
                    description={item.description}
                  />
                </List.Item>
              )}
            />
            
            <Divider />
            
            <Space direction="vertical" style={{ width: '100%' }}>
              <Title level={5}>Testing Flow</Title>
              <Paragraph>
                <ol>
                  <li>Log in with a test user (toggle MFA as needed)</li>
                  <li>If MFA is enabled, you'll be redirected to the verification page</li>
                  <li>Enter one of the valid test codes to complete login</li>
                  <li>Test setup, verification, and disabling of MFA</li>
                </ol>
              </Paragraph>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TestLoginPage; 