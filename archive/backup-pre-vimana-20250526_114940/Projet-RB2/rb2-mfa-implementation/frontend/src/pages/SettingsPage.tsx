import React, { useState } from 'react';
import { Typo<PERSON>, Card, Tabs, Form, Input, Button, Switch, Alert } from 'antd';
import { 
  SafetyOutlined, 
  UserOutlined, 
  LockOutlined, 
  SaveOutlined, 
  DisconnectOutlined 
} from '@ant-design/icons';
import { useAuth } from '../hooks/useAuth';
import { useMFA } from '../hooks/useMFA';
import { Link } from 'react-router-dom';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  const { disableMFA, verifyState } = useMFA();
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDisableMFA = async () => {
    if (!verificationCode) {
      setError('Please enter the verification code');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const result = await disableMFA(verificationCode);
      if (result) {
        setSuccess(true);
      } else if (verifyState.error) {
        setError(verifyState.error);
      }
    } catch (err) {
      setError('Failed to disable MFA');
      console.error('MFA disable error:', err);
    } finally {
      setLoading(false);
      setVerificationCode('');
    }
  };

  return (
    <div>
      <Title level={2}>Settings</Title>
      <Paragraph>
        Manage your account settings and security preferences.
      </Paragraph>
      
      <Tabs defaultActiveKey="security">
        <TabPane 
          tab={<span><UserOutlined /> Profile</span>} 
          key="profile"
        >
          <Card>
            <Form layout="vertical" initialValues={{ email: user?.email, name: user?.name }}>
              <Form.Item label="Email" name="email">
                <Input prefix={<UserOutlined />} disabled />
              </Form.Item>
              
              <Form.Item label="Name" name="name">
                <Input prefix={<UserOutlined />} />
              </Form.Item>
              
              <Form.Item>
                <Button type="primary" icon={<SaveOutlined />}>
                  Save Changes
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><SafetyOutlined /> Security</span>} 
          key="security"
        >
          <Card>
            <Title level={4}>Two-Factor Authentication</Title>
            
            {error && (
              <Alert message={error} type="error" style={{ marginBottom: 16 }} />
            )}
            
            {success && (
              <Alert 
                message="Two-factor authentication disabled successfully" 
                type="success" 
                style={{ marginBottom: 16 }} 
              />
            )}
            
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <div>
                <Text strong>Two-Factor Authentication:</Text>
                <Text> {user?.twoFactorEnabled ? 'Enabled' : 'Disabled'}</Text>
              </div>
              
              <Switch 
                checked={user?.twoFactorEnabled || false} 
                disabled 
              />
            </div>
            
            {user?.twoFactorEnabled ? (
              <div>
                <Paragraph>
                  To disable two-factor authentication, enter a verification code from your authenticator app.
                </Paragraph>
                
                <Form layout="inline" onFinish={handleDisableMFA} style={{ marginBottom: 16 }}>
                  <Form.Item label="Verification Code">
                    <Input
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                      prefix={<LockOutlined />}
                      maxLength={6}
                      style={{ width: 150 }}
                    />
                  </Form.Item>
                  
                  <Form.Item>
                    <Button 
                      type="primary" 
                      danger 
                      icon={<DisconnectOutlined />} 
                      onClick={handleDisableMFA}
                      loading={loading}
                    >
                      Disable 2FA
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            ) : (
              <div>
                <Paragraph>
                  Two-factor authentication is not enabled for your account. Enable it to add an extra layer of security.
                </Paragraph>
                
                <Link to="/setup-mfa">
                  <Button type="primary" icon={<SafetyOutlined />}>
                    Setup Two-Factor Authentication
                  </Button>
                </Link>
              </div>
            )}
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SettingsPage; 