import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { MFAService } from '../services/auth/mfa.service';

interface User {
  id: string;
  email: string;
  name?: string;
  role: string;
  twoFactorEnabled: boolean;
  isVerified: boolean;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  mfaRequired: boolean;
  mfaVerified: boolean;
  login: (token: string, user: User) => void;
  logout: () => void;
  setMfaVerified: (verified: boolean) => void;
  checkMfaStatus: () => Promise<boolean>;
}

const defaultContext: AuthContextType = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: true,
  mfaRequired: false,
  mfaVerified: false,
  login: () => {},
  logout: () => {},
  setMfaVerified: () => {},
  checkMfaStatus: async () => false,
};

export const AuthContext = createContext<AuthContextType>(defaultContext);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [mfaRequired, setMfaRequired] = useState<boolean>(false);
  const [mfaVerified, setMfaVerified] = useState<boolean>(false);

  const mfaService = MFAService.getInstance();

  useEffect(() => {
    // Check for token in localStorage on mount
    const storedToken = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');
    const storedMfaVerified = localStorage.getItem('mfaVerified');
    
    if (storedToken && storedUser) {
      const parsedUser = JSON.parse(storedUser);
      setToken(storedToken);
      setUser(parsedUser);
      setIsAuthenticated(true);
      mfaService.setToken(storedToken);
      
      // Set MFA status
      setMfaRequired(!!parsedUser.twoFactorEnabled);
      setMfaVerified(storedMfaVerified === 'true');
    }
    
    setLoading(false);
  }, []);

  const login = (newToken: string, newUser: User) => {
    localStorage.setItem('token', newToken);
    localStorage.setItem('user', JSON.stringify(newUser));
    
    setToken(newToken);
    setUser(newUser);
    setIsAuthenticated(true);
    setMfaRequired(!!newUser.twoFactorEnabled);
    
    // If 2FA is not enabled, automatically set verified to true
    const newMfaVerified = !newUser.twoFactorEnabled;
    setMfaVerified(newMfaVerified);
    localStorage.setItem('mfaVerified', newMfaVerified.toString());
    
    mfaService.setToken(newToken);
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('mfaVerified');
    
    setToken(null);
    setUser(null);
    setIsAuthenticated(false);
    setMfaRequired(false);
    setMfaVerified(false);
  };

  const handleSetMfaVerified = (verified: boolean) => {
    setMfaVerified(verified);
    localStorage.setItem('mfaVerified', verified.toString());
  };

  const checkMfaStatus = async (): Promise<boolean> => {
    try {
      if (!token || !user?.twoFactorEnabled) {
        return true; // No MFA required
      }
      
      mfaService.setToken(token);
      const response = await mfaService.checkMfaStatus();
      
      // Update state based on the response
      const requiresMfa = response.requiresMfa;
      setMfaRequired(requiresMfa);
      
      if (!requiresMfa) {
        handleSetMfaVerified(true);
      }
      
      return !requiresMfa; // Return true if MFA is not required (verified)
    } catch (error) {
      console.error('Error checking MFA status:', error);
      return false;
    }
  };

  const value = {
    isAuthenticated,
    user,
    token,
    loading,
    mfaRequired,
    mfaVerified,
    login,
    logout,
    setMfaVerified: handleSetMfaVerified,
    checkMfaStatus,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}; 