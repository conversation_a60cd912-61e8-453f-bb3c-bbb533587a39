import React, { useState } from 'react';
import { Form, Input, <PERSON><PERSON>, Card, <PERSON>po<PERSON>, <PERSON><PERSON>, Switch, Divider } from 'antd';
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import { MFAService } from '../../services/auth/mfa.service';
import { useNavigate } from 'react-router-dom';

const { Title, Paragraph, Text } = Typography;

interface MockUser {
  id: string;
  email: string;
  name: string;
  role: string;
  twoFactorEnabled: boolean;
  isVerified: boolean;
}

const MockLoginForm: React.FC = () => {
  const { login, mfaRequired } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password');
  const [name, setName] = useState('Test User');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mfaEnabled, setMfaEnabled] = useState(false);
  const [role, setRole] = useState('USER');
  
  const mfaService = MFAService.getInstance();

  const handleMockLogin = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Create a mock user
      const mockUser: MockUser = {
        id: Math.random().toString(36).substring(2, 10),
        email,
        name,
        role,
        twoFactorEnabled: mfaEnabled,
        isVerified: true
      };
      
      // Generate a mock token
      const mockToken = 'mock_token_' + Date.now();
      
      // Set the mock user in the MFA service
      mfaService.setMockUser(mockUser);
      
      // Login the user
      await login(mockToken, mockUser);
      
      // If MFA is required and enabled, redirect to verify page
      if (mfaEnabled && mfaRequired) {
        navigate('/verify-mfa', { 
          state: { from: { pathname: '/dashboard' } }
        });
      } else {
        navigate('/dashboard');
      }
    } catch (err) {
      setError('Failed to create mock user session');
      console.error('Mock login error:', err);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Card style={{ maxWidth: 400, margin: '0 auto' }}>
      <Title level={3}>Mock Login</Title>
      <Paragraph>
        Use this form to create a test user session for MFA testing.
      </Paragraph>
      
      {error && <Alert message={error} type="error" style={{ marginBottom: 16 }} />}
      
      <Form layout="vertical" onFinish={handleMockLogin}>
        <Form.Item label="Email">
          <Input 
            prefix={<UserOutlined />} 
            value={email} 
            onChange={(e) => setEmail(e.target.value)} 
            placeholder="Email"
          />
        </Form.Item>
        
        <Form.Item label="Name">
          <Input 
            value={name} 
            onChange={(e) => setName(e.target.value)} 
            placeholder="User Name"
          />
        </Form.Item>
        
        <Form.Item label="Password">
          <Input.Password 
            prefix={<LockOutlined />} 
            value={password} 
            onChange={(e) => setPassword(e.target.value)} 
            placeholder="Password (not verified)"
          />
        </Form.Item>
        
        <Form.Item label="Role">
          <select 
            value={role} 
            onChange={(e) => setRole(e.target.value)}
            style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #d9d9d9' }}
          >
            <option value="USER">User</option>
            <option value="ADMIN">Admin</option>
            <option value="MANAGER">Manager</option>
          </select>
        </Form.Item>
        
        <Divider />
        
        <Form.Item label="MFA Settings">
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Switch 
              checked={mfaEnabled} 
              onChange={setMfaEnabled} 
              checkedChildren="MFA On" 
              unCheckedChildren="MFA Off"
            />
            <Text>
              {mfaEnabled 
                ? <Text type="success">User has 2FA enabled</Text> 
                : <Text type="secondary">User has 2FA disabled</Text>}
            </Text>
          </div>
          {mfaEnabled && (
            <Paragraph type="secondary" style={{ marginTop: 8, fontSize: 12 }}>
              You'll be prompted for a verification code after login.
              Use a code from the test page.
            </Paragraph>
          )}
        </Form.Item>
        
        <Form.Item>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={loading} 
            icon={mfaEnabled ? <SafetyOutlined /> : <UserOutlined />}
            block
          >
            Create Test Session
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default MockLoginForm; 