import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Card, Typography, Input, Form, Alert, Space, Divider, Tag } from 'antd';
import { useAuth } from '../../hooks/useAuth';
import { useMFA } from '../../hooks/useMFA';
import { QrCode } from './QrCode';
import { MFAService } from '../../services/auth/mfa.service';

const { Title, Paragraph, Text } = Typography;

const SetupMFA: React.FC = () => {
  const { isAuthenticated, token, user } = useAuth();
  const { setupMFA, enableMFA, setupState, verifyState } = useMFA();
  const [loading, setLoading] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [step, setStep] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [mockState, setMockState] = useState<any>(null);
  const mfaService = MFAService.getInstance();

  useEffect(() => {
    // If user already has 2FA enabled
    if (user?.twoFactorEnabled) {
      setError('Two-factor authentication is already enabled for your account.');
    }
    
    // Get the mock state for testing
    if (mfaService) {
      setMockState(mfaService.getMockState());
    }
  }, [user]);

  const handleSetup = async () => {
    if (!isAuthenticated || !token) {
      setError('You need to be logged in to set up two-factor authentication.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await setupMFA();
      
      if (setupState.error) {
        setError(setupState.error);
      } else {
        setStep(2);
      }
    } catch (err) {
      setError('An error occurred while setting up two-factor authentication.');
      console.error('Setup MFA error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleVerify = async () => {
    if (!verificationCode) {
      setError('Please enter the verification code from your authenticator app.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const success = await enableMFA(verificationCode);
      
      if (success) {
        setStep(3);
      } else if (verifyState.error) {
        setError(verifyState.error);
      }
    } catch (err) {
      setError('An error occurred while verifying the code.');
      console.error('Verify MFA error:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderSetupStep = () => {
    return (
      <>
        <Title level={4}>Step 1: Generate QR Code</Title>
        <Paragraph>
          To set up two-factor authentication, we need to generate a QR code that you'll scan with your authenticator app.
        </Paragraph>
        <Button type="primary" onClick={handleSetup} loading={loading} style={{ marginTop: '16px' }}>
          Generate QR Code
        </Button>
      </>
    );
  };

  const renderQRCodeStep = () => {
    return (
      <>
        <Title level={4}>Step 2: Scan QR Code</Title>
        <Paragraph>
          Scan this QR code with your authenticator app (like Google Authenticator, Authy, or Microsoft Authenticator).
        </Paragraph>
        
        <div style={{ marginTop: '16px', marginBottom: '16px', textAlign: 'center' }}>
          {setupState.qrCode && <QrCode url={setupState.qrCode} />}
        </div>
        
        <Paragraph>
          <Text strong>Secret key (if you can't scan the QR code):</Text>
          <br />
          <Text code copyable>{setupState.secret}</Text>
        </Paragraph>
        
        <Title level={4} style={{ marginTop: '24px' }}>Step 3: Verify Code</Title>
        <Paragraph>
          Enter the 6-digit verification code from your authenticator app to complete the setup.
        </Paragraph>
        
        <Form layout="vertical">
          <Form.Item label="Verification Code" required>
            <Input
              placeholder="Enter 6-digit code"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              maxLength={6}
              style={{ width: '100%' }}
              autoFocus
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" onClick={handleVerify} loading={loading}>
              Verify and Enable 2FA
            </Button>
          </Form.Item>
        </Form>
        
        {mfaService.getMockState && (
          <>
            <Divider>Testing Info</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>Valid test codes:</Text>
              <div>
                {mockState?.validTestCodes?.map((code: string) => (
                  <Tag key={code} color="blue" style={{ marginBottom: 8 }}>{code}</Tag>
                ))}
              </div>
              <Paragraph type="secondary" style={{ fontSize: 12 }}>
                For testing purposes, only these codes will work for verification.
                You can add more codes from the test page.
              </Paragraph>
            </Space>
          </>
        )}
      </>
    );
  };

  const renderSuccessStep = () => {
    return (
      <>
        <Alert
          message="Success!"
          description="Two-factor authentication has been successfully enabled for your account."
          type="success"
          showIcon
          style={{ marginBottom: '16px' }}
        />
        <Title level={4}>What's Next?</Title>
        <Paragraph>
          <ul>
            <li>Every time you log in, you'll need to enter a code from your authenticator app.</li>
            <li>Keep your authenticator app safe and accessible.</li>
            <li>You can disable two-factor authentication from your security settings at any time.</li>
          </ul>
        </Paragraph>
        <Button type="primary" href="/dashboard">
          Go to Dashboard
        </Button>
      </>
    );
  };

  const renderContent = () => {
    if (user?.twoFactorEnabled) {
      return (
        <Alert
          message="2FA Already Enabled"
          description="Two-factor authentication is already enabled for your account. You can manage it from your security settings."
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
          action={
            <Button type="primary" size="small" href="/settings">
              Go to Settings
            </Button>
          }
        />
      );
    }

    switch (step) {
      case 1:
        return renderSetupStep();
      case 2:
        return renderQRCodeStep();
      case 3:
        return renderSuccessStep();
      default:
        return renderSetupStep();
    }
  };

  return (
    <Card style={{ maxWidth: '600px', margin: '0 auto' }}>
      <Title level={3}>Set Up Two-Factor Authentication</Title>
      <Paragraph>
        Two-factor authentication adds an extra layer of security to your account by requiring
        a code from your mobile device in addition to your password.
      </Paragraph>

      {error && <Alert message={error} type="error" style={{ marginBottom: '16px' }} />}

      {renderContent()}
    </Card>
  );
};

export default SetupMFA; 