"""
Script pour exécuter l'API.
"""

import uvicorn
import os
from src.database.connection import init_db
from src.api.app import create_app

if __name__ == "__main__":
    # Initialiser la base de données
    init_db()
    
    # Créer l'application
    app = create_app()
    
    # Exécuter l'application
    uvicorn.run(
        "src.api.app:create_app",
        host="0.0.0.0",
        port=int(os.environ.get("PORT", 8000)),
        reload=True,
        factory=True
    )
