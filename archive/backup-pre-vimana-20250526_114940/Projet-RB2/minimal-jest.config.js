module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/minimal.test.js', '**/security/**/*.test.ts', '**/security/**/*.test.tsx', '**/integration/**/*.test.ts'],
  modulePathIgnorePatterns: [
    '<rootDir>/backup/',
    '<rootDir>/node_modules/'
  ],
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': '<rootDir>/__mocks__/styleMock.js',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/__mocks__/fileMock.js',
    '^localforage$': '<rootDir>/__mocks__/localforage.js',
    '^@/(.*)$': ['<rootDir>/src/$1', '<rootDir>/frontend/src/$1']
  },
  haste: {
    hasteImplModulePath: null
  },
  // Configuration de la couverture de code
  collectCoverage: false, // Désactivé par défaut, activé via --coverage
  coverageDirectory: '<rootDir>/coverage',
  collectCoverageFrom: [
    '<rootDir>/Backend/src/**/*.{js,ts}',
    '<rootDir>/frontend/src/**/*.{js,ts,tsx}',
    '!<rootDir>/**/*.d.ts',
    '!<rootDir>/**/*.test.{js,ts,tsx}',
    '!<rootDir>/**/__tests__/**',
    '!<rootDir>/**/__mocks__/**'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  coverageReporters: ['json', 'lcov', 'text', 'clover', 'html']
};
