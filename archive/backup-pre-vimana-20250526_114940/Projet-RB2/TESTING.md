# Guide des tests

Ce document explique comment exécuter et maintenir les tests du projet.

## Structure des tests

Les tests sont organisés selon la structure suivante:

```
Backend/src/tests/
├── integration/        # Tests d'intégration
├── services/           # Tests des services
│   └── security/       # Tests des services de sécurité
└── unit/               # Tests unitaires

frontend/src/tests/
├── components/         # Tests des composants React
├── hooks/              # Tests des hooks React
├── security/           # Tests de sécurité frontend
└── utils/              # Tests des utilitaires
```

## Exécution des tests

### Exécution standard

Pour exécuter tous les tests:

```bash
npm test
```

### Exécution optimisée

Pour éviter les problèmes de mémoire, utilisez le script `run-tests.js`:

```bash
# Exécuter tous les tests
node --expose-gc run-tests.js

# Exécuter un groupe spécifique de tests
node --expose-gc run-tests.js "Backend/src/tests/services/security/**/*.test.ts"

# Exécuter un test spécifique
node --expose-gc run-tests.js "Backend/src/tests/services/security/SecurityPipeline.test.ts"
```

### Options de configuration

Le script `run-tests.js` accepte deux paramètres:
1. Le motif de recherche des fichiers de test (par défaut: `Backend/src/tests/**/*.test.ts`)
2. Le fichier de configuration Jest à utiliser (par défaut: `minimal-jest.config.js`)

```bash
node --expose-gc run-tests.js "<pattern>" "<config-file>"
```

## Écriture des tests

### Tests unitaires

Les tests unitaires doivent suivre ces principes:
- Tester une seule unité de code à la fois
- Mocker toutes les dépendances externes
- Être rapides et indépendants

Exemple:

```typescript
describe('SecurityUtils', () => {
  it('should validate password strength correctly', () => {
    expect(SecurityUtils.isStrongPassword('weak')).toBe(false);
    expect(SecurityUtils.isStrongPassword('StrongP@ssw0rd')).toBe(true);
  });
});
```

### Tests d'intégration

Les tests d'intégration doivent:
- Tester l'interaction entre plusieurs composants
- Utiliser des mocks pour les services externes (base de données, API, etc.)
- Vérifier que les composants fonctionnent correctement ensemble

Exemple:

```typescript
describe('AuthenticationFlow', () => {
  it('should authenticate user and redirect to dashboard', async () => {
    // Setup
    const authService = new AuthService();
    const userService = new UserService();
    
    // Test
    await authService.login('user', 'password');
    const user = await userService.getCurrentUser();
    
    // Verify
    expect(user).not.toBeNull();
    expect(user.isAuthenticated).toBe(true);
  });
});
```

### Tests de sécurité

Les tests de sécurité doivent:
- Vérifier que les mécanismes de sécurité fonctionnent correctement
- Tester les cas d'attaque courants (injection SQL, XSS, etc.)
- Valider que les données sensibles sont correctement protégées

## Mocks

Les mocks sont centralisés dans le répertoire `__mocks__` à la racine du projet.

Pour utiliser un mock:

```typescript
// Importer le mock
import { mockUser } from '../../__mocks__/userMock';

// Utiliser le mock dans un test
test('should display user name', () => {
  render(<UserProfile user={mockUser} />);
  expect(screen.getByText(mockUser.name)).toBeInTheDocument();
});
```

## Bonnes pratiques

1. **Isolation**: Chaque test doit être indépendant des autres tests.
2. **Lisibilité**: Utilisez des noms descriptifs pour les tests et les variables.
3. **Maintenance**: Gardez les tests à jour avec le code.
4. **Couverture**: Visez une couverture de code élevée, mais privilégiez la qualité à la quantité.
5. **Performance**: Optimisez les tests pour qu'ils s'exécutent rapidement.

## Résolution des problèmes

### Problèmes de mémoire

Si vous rencontrez des erreurs "JavaScript heap out of memory":

1. Utilisez le script `run-tests.js` qui exécute les tests par lots.
2. Augmentez la limite de mémoire dans le script si nécessaire.
3. Réduisez le nombre de tests exécutés simultanément.

### Tests lents

Si les tests sont trop lents:

1. Identifiez les tests les plus lents avec `jest --verbose`.
2. Optimisez les mocks et les fixtures.
3. Exécutez les tests en parallèle si possible.

### Tests instables

Si certains tests échouent de manière aléatoire:

1. Vérifiez les dépendances entre les tests.
2. Assurez-vous que chaque test nettoie correctement après son exécution.
3. Utilisez des timeouts plus longs pour les opérations asynchrones.
