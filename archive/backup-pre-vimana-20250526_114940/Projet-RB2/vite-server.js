import express from 'express';
import { createServer as createViteServer } from 'vite';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Obtenir le répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Port configuration
const PORT = process.env.PORT || 3001;

// Fonction asynchrone pour démarrer le serveur
async function startServer() {
  const app = express();
  
  // Créer un serveur Vite en mode middleware avec une configuration optimisée pour les ESM
  const vite = await createViteServer({
    server: { 
      middlewareMode: true 
    },
    appType: 'spa',
    optimizeDeps: {
      include: [], // Vous pouvez inclure des dépendances spécifiques ici
    },
    build: {
      rollupOptions: {
        output: {
          format: 'es' // S'assurer que la sortie est en format ESM
        }
      }
    }
  });
  
  // Utiliser le middleware Vite en premier pour que Vite gère les fichiers correctement
  app.use(vite.middlewares);
  
  // Middleware personnalisé pour les types MIME des modules JavaScript
  app.use((req, res, next) => {
    if (req.path.endsWith('.js') || req.path.includes('@react-refresh')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
    } else if (req.path.endsWith('.mjs')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
    } else if (req.path.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css; charset=utf-8');
    } else if (req.path.endsWith('.json')) {
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
    }
    next();
  });
  
  // Route API d'exemple
  app.get('/api/hello', (req, res) => {
    res.json({ message: 'Hello from API' });
  });
  
  // Route pour servir index.html en mode développement - UNIQUEMENT SI LE CHEMIN N'EST PAS UN FICHIER
  app.get('*', async (req, res, next) => {
    const url = req.originalUrl;
    
    // Si l'URL semble être un fichier (contient un point), passez au middleware suivant
    if (url.split('/').pop().includes('.')) {
      console.log(`Passing through resource request: ${url}`);
      return next();
    }
    
    try {
      // Chemin absolu vers index.html
      const indexPath = path.resolve(__dirname, 'index.html');
      
      // Lire index.html
      let html = fs.readFileSync(indexPath, 'utf-8');
      
      // Appliquer les transformations Vite
      html = await vite.transformIndexHtml(url, html);
      
      // Envoyer le HTML transformé
      res.status(200).set({ 'Content-Type': 'text/html; charset=utf-8' }).end(html);
    } catch (e) {
      // Laisser Vite gérer l'erreur
      vite.ssrFixStacktrace(e);
      console.error(e);
      res.status(500).end(e.message);
    }
  });
  
  // Démarrer le serveur sur le port 3001
  app.listen(PORT, () => {
    console.log(`Serveur Vite démarré sur http://localhost:${PORT}`);
    console.log(`Tests routes:`);
    console.log(`- http://localhost:${PORT}/`);
    console.log(`- http://localhost:${PORT}/test`);
    console.log(`- http://localhost:${PORT}/login`);
    console.log(`- http://localhost:${PORT}/dashboard`);
  });
}

// Lancer le serveur avec gestion d'erreur
startServer().catch((err) => {
  console.error('Erreur au démarrage du serveur:', err);
  process.exit(1);
}); 