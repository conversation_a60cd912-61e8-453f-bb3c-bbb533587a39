/**
 * Tests d'Accessibilité Complets - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Tests d'accessibilité WCAG AA complets pour toutes
 * les pages et composants de l'application.
 */

describe('Accessibility Tests - WCAG AA Compliance', () => {
  beforeEach(() => {
    // Injecter axe-core avant chaque test
    cy.visit('/');
    cy.injectAxe();
  });

  describe('Homepage Accessibility', () => {
    it('should not have accessibility violations on homepage', () => {
      cy.checkA11y(null, {
        runOnly: {
          type: 'tag',
          values: ['wcag2a', 'wcag2aa', 'wcag21aa']
        }
      });
    });

    it('should have proper heading hierarchy', () => {
      // Vérifier la hiérarchie des titres
      cy.get('h1').should('have.length', 1);
      cy.get('h1').should('contain.text', 'Retreat & Be');
      
      // Vérifier que les h2 suivent h1
      cy.get('h2').each(($h2, index) => {
        cy.wrap($h2).should('be.visible');
      });
      
      // Vérifier qu'il n'y a pas de saut de niveau
      cy.get('h3').each(($h3) => {
        cy.wrap($h3).parent().within(() => {
          cy.get('h2').should('exist');
        });
      });
    });

    it('should have proper landmark roles', () => {
      cy.get('[role="banner"], header').should('exist');
      cy.get('[role="main"], main').should('exist');
      cy.get('[role="navigation"], nav').should('exist');
      cy.get('[role="contentinfo"], footer').should('exist');
    });

    it('should have proper alt text for images', () => {
      cy.get('img').each(($img) => {
        cy.wrap($img).should('have.attr', 'alt');
        cy.wrap($img).then(($el) => {
          const alt = $el.attr('alt');
          expect(alt).to.not.be.empty;
          expect(alt).to.not.equal('image'); // Éviter les alt génériques
        });
      });
    });

    it('should have proper focus management', () => {
      // Tester la navigation au clavier
      cy.get('body').tab();
      cy.focused().should('be.visible');
      
      // Vérifier que tous les éléments interactifs sont focusables
      cy.get('button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])')
        .each(($el) => {
          cy.wrap($el).focus();
          cy.wrap($el).should('have.focus');
        });
    });
  });

  describe('Authentication Pages Accessibility', () => {
    it('should not have violations on login page', () => {
      cy.visit('/auth/login');
      cy.checkA11y();
    });

    it('should have proper form labels and associations', () => {
      cy.visit('/auth/login');
      
      // Vérifier que tous les inputs ont des labels
      cy.get('input').each(($input) => {
        const id = $input.attr('id');
        const ariaLabel = $input.attr('aria-label');
        const ariaLabelledby = $input.attr('aria-labelledby');
        
        if (id) {
          cy.get(`label[for="${id}"]`).should('exist');
        } else {
          expect(ariaLabel || ariaLabelledby).to.exist;
        }
      });
    });

    it('should have proper error message associations', () => {
      cy.visit('/auth/login');
      
      // Déclencher des erreurs de validation
      cy.get('[data-cy="login-button"]').click();
      
      // Vérifier que les messages d'erreur sont associés aux champs
      cy.get('[data-cy="email-input"]').should('have.attr', 'aria-describedby');
      cy.get('[data-cy="password-input"]').should('have.attr', 'aria-describedby');
      
      // Vérifier que les messages d'erreur ont le bon rôle
      cy.get('[data-cy="email-error"]').should('have.attr', 'role', 'alert');
      cy.get('[data-cy="password-error"]').should('have.attr', 'role', 'alert');
    });

    it('should announce form submission states', () => {
      cy.visit('/auth/login');
      
      // Mocker une connexion lente
      cy.intercept('POST', '/api/auth/login', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ statusCode: 200, body: { success: true } });
        });
      });
      
      cy.get('[data-cy="email-input"]').type('<EMAIL>');
      cy.get('[data-cy="password-input"]').type('password123');
      cy.get('[data-cy="login-button"]').click();
      
      // Vérifier que l'état de chargement est annoncé
      cy.get('[data-cy="login-button"]').should('have.attr', 'aria-busy', 'true');
      cy.get('[aria-live="polite"]').should('contain.text', 'Connexion en cours');
    });
  });

  describe('Dashboard Accessibility', () => {
    beforeEach(() => {
      cy.login();
      cy.visitDashboard();
    });

    it('should not have violations on dashboard', () => {
      cy.checkA11y();
    });

    it('should have proper ARIA labels for interactive elements', () => {
      // Vérifier les boutons d'action
      cy.get('button').each(($button) => {
        const ariaLabel = $button.attr('aria-label');
        const text = $button.text().trim();
        
        if (!text || text.length < 2) {
          expect(ariaLabel).to.exist;
          expect(ariaLabel).to.not.be.empty;
        }
      });
      
      // Vérifier les liens
      cy.get('a').each(($link) => {
        const ariaLabel = $link.attr('aria-label');
        const text = $link.text().trim();
        
        if (!text || text.length < 2) {
          expect(ariaLabel).to.exist;
        }
      });
    });

    it('should have proper table accessibility', () => {
      // Si des tableaux sont présents
      cy.get('table').each(($table) => {
        // Vérifier les en-têtes de colonnes
        cy.wrap($table).find('th').should('have.attr', 'scope');
        
        // Vérifier le caption ou aria-label
        cy.wrap($table).should('satisfy', ($el) => {
          return $el.find('caption').length > 0 || $el.attr('aria-label');
        });
      });
    });

    it('should have proper modal accessibility', () => {
      // Ouvrir un modal
      cy.get('[data-cy="open-modal-button"]').first().click();
      
      cy.get('[role="dialog"]').should('exist').within(() => {
        // Vérifier les attributs ARIA
        cy.get('[role="dialog"]').should('have.attr', 'aria-modal', 'true');
        cy.get('[role="dialog"]').should('have.attr', 'aria-labelledby');
        
        // Vérifier que le focus est piégé
        cy.get('button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])')
          .first()
          .should('have.focus');
      });
      
      // Fermer avec Escape
      cy.get('body').type('{esc}');
      cy.get('[role="dialog"]').should('not.exist');
    });
  });

  describe('Retreat Listing Accessibility', () => {
    beforeEach(() => {
      cy.login();
      cy.visitRetreats();
    });

    it('should not have violations on retreat listing', () => {
      cy.checkA11y();
    });

    it('should have proper card accessibility', () => {
      cy.get('[data-cy^="retreat-card-"]').each(($card) => {
        // Vérifier que chaque carte a un titre accessible
        cy.wrap($card).within(() => {
          cy.get('h2, h3, [role="heading"]').should('exist');
        });
        
        // Vérifier les boutons d'action
        cy.wrap($card).find('button').each(($button) => {
          const ariaLabel = $button.attr('aria-label');
          const text = $button.text().trim();
          
          expect(ariaLabel || text).to.exist;
        });
      });
    });

    it('should have accessible search and filters', () => {
      // Vérifier le champ de recherche
      cy.get('[data-cy="search-input"]').should('have.attr', 'aria-label');
      
      // Vérifier les filtres
      cy.get('[data-cy="filters-toggle"]').click();
      
      cy.get('[data-cy="filters-panel"]').within(() => {
        // Vérifier que tous les contrôles de filtre sont accessibles
        cy.get('input, select').each(($control) => {
          const id = $control.attr('id');
          const ariaLabel = $control.attr('aria-label');
          
          if (id) {
            cy.get(`label[for="${id}"]`).should('exist');
          } else {
            expect(ariaLabel).to.exist;
          }
        });
      });
    });

    it('should announce search results', () => {
      cy.get('[data-cy="search-input"]').type('yoga');
      cy.get('[data-cy="search-button"]').click();
      
      // Vérifier que les résultats sont annoncés
      cy.get('[aria-live="polite"]').should('contain.text', 'résultats trouvés');
      cy.get('[data-cy="results-count"]').should('be.visible');
    });
  });

  describe('Color Contrast and Visual Accessibility', () => {
    it('should have sufficient color contrast', () => {
      cy.checkA11y(null, {
        rules: {
          'color-contrast': { enabled: true }
        }
      });
    });

    it('should be usable without color alone', () => {
      // Vérifier que l'information n'est pas transmise uniquement par la couleur
      cy.get('[data-cy="status-indicator"]').each(($indicator) => {
        // Doit avoir du texte ou des icônes en plus de la couleur
        const text = $indicator.text().trim();
        const ariaLabel = $indicator.attr('aria-label');
        const hasIcon = $indicator.find('svg, [class*="icon"]').length > 0;
        
        expect(text || ariaLabel || hasIcon).to.exist;
      });
    });

    it('should support high contrast mode', () => {
      // Simuler le mode contraste élevé
      cy.get('body').invoke('attr', 'style', 'filter: contrast(200%)');
      
      // Vérifier que l'interface reste utilisable
      cy.get('button, a, input').should('be.visible');
      cy.checkA11y();
    });
  });

  describe('Keyboard Navigation', () => {
    it('should support full keyboard navigation', () => {
      cy.visit('/');
      
      // Naviguer avec Tab
      let focusedElements = [];
      
      for (let i = 0; i < 20; i++) {
        cy.get('body').tab();
        cy.focused().then(($el) => {
          focusedElements.push($el[0]);
        });
      }
      
      // Vérifier qu'on peut atteindre tous les éléments interactifs
      cy.get('button:visible, a:visible, input:visible, select:visible, textarea:visible')
        .should('have.length.at.least', 1);
    });

    it('should have visible focus indicators', () => {
      cy.visit('/');
      
      cy.get('button, a, input, select, textarea').each(($el) => {
        cy.wrap($el).focus();
        
        // Vérifier que l'élément focusé a un indicateur visuel
        cy.wrap($el).should('satisfy', ($focused) => {
          const styles = window.getComputedStyle($focused[0]);
          return (
            styles.outline !== 'none' ||
            styles.boxShadow !== 'none' ||
            styles.border !== 'none'
          );
        });
      });
    });

    it('should support keyboard shortcuts', () => {
      cy.login();
      cy.visitDashboard();
      
      // Tester les raccourcis clavier courants
      cy.get('body').type('{ctrl+k}'); // Recherche rapide
      cy.get('[data-cy="quick-search"]').should('be.visible');
      
      cy.get('body').type('{esc}'); // Fermer
      cy.get('[data-cy="quick-search"]').should('not.exist');
    });
  });

  describe('Screen Reader Support', () => {
    it('should have proper ARIA live regions', () => {
      cy.visit('/');
      
      // Vérifier la présence de régions live
      cy.get('[aria-live="polite"], [aria-live="assertive"]').should('exist');
    });

    it('should announce dynamic content changes', () => {
      cy.login();
      cy.visitDashboard();
      
      // Déclencher un changement de contenu
      cy.get('[data-cy="refresh-data"]').click();
      
      // Vérifier que le changement est annoncé
      cy.get('[aria-live="polite"]').should('contain.text', 'Données mises à jour');
    });

    it('should have proper skip links', () => {
      cy.visit('/');
      
      // Vérifier la présence de liens de navigation rapide
      cy.get('body').tab();
      cy.focused().should('contain.text', 'Aller au contenu principal');
      
      // Tester le fonctionnement du lien
      cy.focused().click();
      cy.get('main').should('have.focus');
    });
  });

  describe('Mobile Accessibility', () => {
    beforeEach(() => {
      cy.viewport('iphone-x');
    });

    it('should be accessible on mobile devices', () => {
      cy.visit('/');
      cy.checkA11y();
    });

    it('should have proper touch targets', () => {
      cy.visit('/');
      
      // Vérifier que les cibles tactiles sont assez grandes (44x44px minimum)
      cy.get('button, a, input[type="checkbox"], input[type="radio"]').each(($el) => {
        cy.wrap($el).should('satisfy', ($target) => {
          const rect = $target[0].getBoundingClientRect();
          return rect.width >= 44 && rect.height >= 44;
        });
      });
    });

    it('should support zoom up to 200%', () => {
      cy.visit('/');
      
      // Simuler un zoom à 200%
      cy.get('body').invoke('attr', 'style', 'zoom: 2');
      
      // Vérifier que l'interface reste utilisable
      cy.get('button, a, input').should('be.visible');
      cy.checkA11y();
    });
  });

  describe('Error Handling Accessibility', () => {
    it('should announce errors properly', () => {
      cy.visit('/auth/login');
      
      // Déclencher une erreur
      cy.get('[data-cy="login-button"]').click();
      
      // Vérifier que l'erreur est annoncée
      cy.get('[role="alert"]').should('exist');
      cy.get('[aria-live="assertive"]').should('contain.text', 'erreur');
    });

    it('should provide clear error recovery instructions', () => {
      cy.visit('/auth/login');
      
      cy.get('[data-cy="email-input"]').type('invalid-email');
      cy.get('[data-cy="login-button"]').click();
      
      // Vérifier que les instructions de correction sont claires
      cy.get('[data-cy="email-error"]').should('contain.text', 'format valide');
    });
  });
});
