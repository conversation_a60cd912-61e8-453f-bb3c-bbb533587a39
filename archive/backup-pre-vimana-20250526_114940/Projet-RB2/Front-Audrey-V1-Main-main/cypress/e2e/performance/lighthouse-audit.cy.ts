/**
 * Tests de Performance Lighthouse - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Tests de performance complets avec Lighthouse
 * pour valider les métriques Core Web Vitals.
 */

describe('Lighthouse Performance Audit', () => {
  const lighthouseConfig = {
    performance: 90,
    accessibility: 95,
    'best-practices': 90,
    seo: 85,
    pwa: 70
  };

  const mobileConfig = {
    ...lighthouseConfig,
    performance: 85, // Seuil plus bas pour mobile
  };

  describe('Desktop Performance', () => {
    beforeEach(() => {
      cy.viewport(1280, 720);
    });

    it('should meet performance standards on homepage', () => {
      cy.visit('/');
      cy.lighthouse(lighthouseConfig);
    });

    it('should meet performance standards on dashboard', () => {
      cy.login();
      cy.visitDashboard();
      cy.lighthouse(lighthouseConfig);
    });

    it('should meet performance standards on retreat listing', () => {
      cy.login();
      cy.visitRetreats();
      cy.lighthouse(lighthouseConfig);
    });

    it('should meet performance standards on professional listing', () => {
      cy.login();
      cy.visitProfessionals();
      cy.lighthouse(lighthouseConfig);
    });

    it('should have fast Core Web Vitals', () => {
      cy.visit('/');
      
      // Mesurer les métriques Core Web Vitals
      cy.window().then((win) => {
        return new Promise((resolve) => {
          new win.PerformanceObserver((list) => {
            const entries = list.getEntries();
            const metrics = {};
            
            entries.forEach((entry) => {
              if (entry.entryType === 'largest-contentful-paint') {
                metrics.lcp = entry.startTime;
              }
              if (entry.entryType === 'first-input') {
                metrics.fid = entry.processingStart - entry.startTime;
              }
              if (entry.entryType === 'layout-shift') {
                metrics.cls = entry.value;
              }
            });
            
            resolve(metrics);
          }).observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
          
          // Timeout après 10 secondes
          setTimeout(() => resolve({}), 10000);
        });
      }).then((metrics) => {
        // Vérifier les seuils Core Web Vitals
        if (metrics.lcp) {
          expect(metrics.lcp).to.be.lessThan(2500); // LCP < 2.5s
        }
        if (metrics.fid) {
          expect(metrics.fid).to.be.lessThan(100); // FID < 100ms
        }
        if (metrics.cls) {
          expect(metrics.cls).to.be.lessThan(0.1); // CLS < 0.1
        }
      });
    });
  });

  describe('Mobile Performance', () => {
    beforeEach(() => {
      cy.viewport('iphone-x');
    });

    it('should meet mobile performance standards on homepage', () => {
      cy.visit('/');
      cy.lighthouse(mobileConfig);
    });

    it('should meet mobile performance standards on dashboard', () => {
      cy.login();
      cy.visitDashboard();
      cy.lighthouse(mobileConfig);
    });

    it('should have optimized images for mobile', () => {
      cy.visit('/');
      
      // Vérifier que les images sont optimisées
      cy.get('img').each(($img) => {
        cy.wrap($img).should('have.attr', 'loading', 'lazy');
        
        // Vérifier les formats modernes
        cy.wrap($img).should('satisfy', ($el) => {
          const src = $el.attr('src');
          return src.includes('.webp') || src.includes('.avif') || $el.parent().find('source').length > 0;
        });
      });
    });

    it('should have proper resource hints', () => {
      cy.visit('/');
      
      // Vérifier les resource hints
      cy.get('head').within(() => {
        cy.get('link[rel="preconnect"]').should('exist');
        cy.get('link[rel="dns-prefetch"]').should('exist');
        cy.get('link[rel="preload"]').should('exist');
      });
    });
  });

  describe('Network Performance', () => {
    it('should perform well on slow 3G', () => {
      // Simuler une connexion 3G lente
      cy.intercept('**/*', (req) => {
        req.reply((res) => {
          res.delay(1000); // Délai de 1 seconde
          res.throttle(50); // 50 kbps
        });
      });

      cy.visit('/');
      
      // Vérifier que la page reste utilisable
      cy.get('main').should('be.visible');
      cy.get('[data-cy="loading-spinner"]').should('not.exist');
      
      // Mesurer le temps de chargement
      cy.window().its('performance').invoke('getEntriesByType', 'navigation')
        .then((entries) => {
          const loadTime = entries[0].loadEventEnd - entries[0].navigationStart;
          expect(loadTime).to.be.lessThan(10000); // Moins de 10 secondes sur 3G
        });
    });

    it('should handle offline scenarios gracefully', () => {
      cy.visit('/');
      
      // Simuler une perte de connexion
      cy.window().then((win) => {
        win.navigator.onLine = false;
        win.dispatchEvent(new Event('offline'));
      });
      
      // Vérifier que l'application gère l'état hors ligne
      cy.get('[data-cy="offline-indicator"]').should('be.visible');
      cy.get('[data-cy="offline-message"]').should('contain.text', 'hors ligne');
    });

    it('should have efficient caching strategy', () => {
      cy.visit('/');
      
      // Vérifier les en-têtes de cache
      cy.request('/').then((response) => {
        expect(response.headers).to.have.property('cache-control');
      });
      
      // Vérifier que les ressources statiques sont mises en cache
      cy.get('link[rel="stylesheet"], script[src]').each(($el) => {
        const url = $el.attr('href') || $el.attr('src');
        if (url && !url.startsWith('data:')) {
          cy.request(url).then((response) => {
            expect(response.headers).to.have.property('cache-control');
          });
        }
      });
    });
  });

  describe('Bundle Analysis', () => {
    it('should have optimized bundle sizes', () => {
      cy.visit('/');
      
      // Analyser la taille des bundles
      cy.window().its('performance').invoke('getEntriesByType', 'resource')
        .then((entries) => {
          const jsEntries = entries.filter(entry => 
            entry.name.includes('.js') && !entry.name.includes('node_modules')
          );
          
          jsEntries.forEach((entry) => {
            // Vérifier que les bundles JS ne sont pas trop volumineux
            expect(entry.transferSize).to.be.lessThan(500000); // 500KB max par bundle
          });
          
          const cssEntries = entries.filter(entry => entry.name.includes('.css'));
          cssEntries.forEach((entry) => {
            // Vérifier que les CSS ne sont pas trop volumineux
            expect(entry.transferSize).to.be.lessThan(100000); // 100KB max par CSS
          });
        });
    });

    it('should use code splitting effectively', () => {
      cy.visit('/');
      
      // Vérifier que les routes sont chargées à la demande
      cy.window().its('performance').invoke('getEntriesByType', 'resource')
        .then((initialResources) => {
          const initialJsCount = initialResources.filter(r => r.name.includes('.js')).length;
          
          // Naviguer vers une autre page
          cy.get('[data-cy="nav-retreats"]').click();
          
          cy.window().its('performance').invoke('getEntriesByType', 'resource')
            .then((newResources) => {
              const newJsCount = newResources.filter(r => r.name.includes('.js')).length;
              
              // Vérifier qu'un nouveau chunk a été chargé
              expect(newJsCount).to.be.greaterThan(initialJsCount);
            });
        });
    });

    it('should minimize unused JavaScript', () => {
      cy.visit('/');
      
      // Utiliser Coverage API pour mesurer le code utilisé
      cy.window().then((win) => {
        if ('coverage' in win) {
          return win.coverage.startPreciseCoverage({ callCount: true, detailed: true });
        }
      }).then(() => {
        // Interagir avec la page
        cy.get('button').first().click();
        cy.wait(2000);
        
        // Arrêter la mesure de couverture
        cy.window().then((win) => {
          if ('coverage' in win) {
            return win.coverage.takePreciseCoverage();
          }
        }).then((coverage) => {
          if (coverage) {
            coverage.result.forEach((entry) => {
              if (entry.url.includes('.js')) {
                const usedBytes = entry.functions.reduce((sum, func) => 
                  sum + func.ranges.reduce((rangeSum, range) => 
                    rangeSum + (range.endOffset - range.startOffset), 0), 0);
                const totalBytes = entry.text.length;
                const usagePercentage = (usedBytes / totalBytes) * 100;
                
                // Vérifier qu'au moins 60% du code est utilisé
                expect(usagePercentage).to.be.greaterThan(60);
              }
            });
          }
        });
      });
    });
  });

  describe('SEO Performance', () => {
    it('should have proper meta tags', () => {
      cy.visit('/');
      
      // Vérifier les meta tags essentiels
      cy.get('head').within(() => {
        cy.get('title').should('exist').and('not.be.empty');
        cy.get('meta[name="description"]').should('exist').and('have.attr', 'content');
        cy.get('meta[name="viewport"]').should('exist');
        cy.get('meta[charset]').should('exist');
        
        // Open Graph
        cy.get('meta[property="og:title"]').should('exist');
        cy.get('meta[property="og:description"]').should('exist');
        cy.get('meta[property="og:image"]').should('exist');
        
        // Twitter Cards
        cy.get('meta[name="twitter:card"]').should('exist');
        cy.get('meta[name="twitter:title"]').should('exist');
        cy.get('meta[name="twitter:description"]').should('exist');
      });
    });

    it('should have proper structured data', () => {
      cy.visit('/');
      
      // Vérifier la présence de données structurées
      cy.get('script[type="application/ld+json"]').should('exist')
        .then(($script) => {
          const jsonLd = JSON.parse($script.text());
          expect(jsonLd).to.have.property('@context');
          expect(jsonLd).to.have.property('@type');
        });
    });

    it('should have fast server response times', () => {
      const startTime = Date.now();
      
      cy.request('/').then((response) => {
        const responseTime = Date.now() - startTime;
        
        expect(response.status).to.equal(200);
        expect(responseTime).to.be.lessThan(200); // TTFB < 200ms
      });
    });
  });

  describe('Progressive Web App', () => {
    it('should have a valid web app manifest', () => {
      cy.visit('/');
      
      cy.get('link[rel="manifest"]').should('exist')
        .then(($link) => {
          const manifestUrl = $link.attr('href');
          cy.request(manifestUrl).then((response) => {
            expect(response.status).to.equal(200);
            
            const manifest = response.body;
            expect(manifest).to.have.property('name');
            expect(manifest).to.have.property('short_name');
            expect(manifest).to.have.property('start_url');
            expect(manifest).to.have.property('display');
            expect(manifest).to.have.property('theme_color');
            expect(manifest).to.have.property('background_color');
            expect(manifest).to.have.property('icons');
            expect(manifest.icons).to.be.an('array').with.length.at.least(1);
          });
        });
    });

    it('should register a service worker', () => {
      cy.visit('/');
      
      cy.window().then((win) => {
        expect(win.navigator.serviceWorker).to.exist;
        
        return win.navigator.serviceWorker.getRegistration();
      }).then((registration) => {
        expect(registration).to.exist;
      });
    });

    it('should work offline for cached content', () => {
      cy.visit('/');
      
      // Attendre que le service worker soit installé
      cy.window().then((win) => {
        return win.navigator.serviceWorker.ready;
      });
      
      // Simuler le mode hors ligne
      cy.window().then((win) => {
        win.navigator.onLine = false;
      });
      
      // Recharger la page
      cy.reload();
      
      // Vérifier que la page se charge toujours
      cy.get('main').should('be.visible');
    });
  });

  describe('Performance Monitoring', () => {
    it('should track performance metrics', () => {
      cy.visit('/');
      
      // Vérifier que les métriques sont collectées
      cy.window().then((win) => {
        const navigation = win.performance.getEntriesByType('navigation')[0];
        
        // Métriques de navigation
        expect(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart)
          .to.be.lessThan(1000); // DOMContentLoaded < 1s
        
        expect(navigation.loadEventEnd - navigation.loadEventStart)
          .to.be.lessThan(2000); // Load event < 2s
        
        // Métriques de ressources
        const resources = win.performance.getEntriesByType('resource');
        const slowResources = resources.filter(r => r.duration > 1000);
        
        expect(slowResources.length).to.be.lessThan(3); // Max 3 ressources lentes
      });
    });

    it('should have performance budgets', () => {
      cy.visit('/');
      
      cy.window().its('performance').invoke('getEntriesByType', 'resource')
        .then((resources) => {
          const totalSize = resources.reduce((sum, resource) => 
            sum + (resource.transferSize || 0), 0);
          
          // Budget total de 2MB
          expect(totalSize).to.be.lessThan(2000000);
          
          // Compter les requêtes
          expect(resources.length).to.be.lessThan(50); // Max 50 requêtes
        });
    });
  });
});
