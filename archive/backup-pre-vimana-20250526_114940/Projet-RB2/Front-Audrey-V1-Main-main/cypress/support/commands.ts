/**
 * Commandes Cypress Personnalisées - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Commandes réutilisables pour les tests E2E
 * spécifiques à l'application Retreat And Be.
 */

/// <reference types="cypress" />

// Déclaration des types pour TypeScript
declare global {
  namespace Cypress {
    interface Chainable {
      // Authentification
      login(email?: string, password?: string): Chainable<void>;
      loginAsAdmin(): Chainable<void>;
      loginAsCreator(): Chainable<void>;
      logout(): Chainable<void>;
      
      // Navigation
      visitDashboard(): Chainable<void>;
      visitRetreats(): Chainable<void>;
      visitProfessionals(): Chainable<void>;
      
      // Interactions spécifiques
      searchRetreats(query: string): Chainable<void>;
      bookRetreat(retreatId: string): Chainable<void>;
      contactProfessional(professionalId: string): Chainable<void>;
      
      // Utilitaires
      waitForPageLoad(): Chainable<void>;
      checkToast(message: string, type?: string): Chainable<void>;
      fillForm(formData: Record<string, string>): Chainable<void>;
      
      // Tests d'accessibilité
      checkA11y(context?: string, options?: any): Chainable<void>;
      
      // Tests de performance
      lighthouse(options?: any): Chainable<void>;
    }
  }
}

// Commande de connexion
Cypress.Commands.add('login', (email?: string, password?: string) => {
  const userEmail = email || Cypress.env('testUser.email');
  const userPassword = password || Cypress.env('testUser.password');
  
  cy.visit('/auth/login');
  cy.get('[data-cy="email-input"]').type(userEmail);
  cy.get('[data-cy="password-input"]').type(userPassword);
  cy.get('[data-cy="login-button"]').click();
  
  // Attendre la redirection vers le dashboard
  cy.url().should('include', '/app/dashboard');
  cy.get('[data-cy="user-menu"]').should('be.visible');
});

// Connexion en tant qu'administrateur
Cypress.Commands.add('loginAsAdmin', () => {
  cy.login(Cypress.env('adminUser.email'), Cypress.env('adminUser.password'));
});

// Connexion en tant que créateur
Cypress.Commands.add('loginAsCreator', () => {
  cy.login(Cypress.env('creatorUser.email'), Cypress.env('creatorUser.password'));
});

// Déconnexion
Cypress.Commands.add('logout', () => {
  cy.get('[data-cy="user-menu"]').click();
  cy.get('[data-cy="logout-button"]').click();
  cy.url().should('include', '/auth/login');
});

// Navigation vers le dashboard
Cypress.Commands.add('visitDashboard', () => {
  cy.visit('/app/dashboard');
  cy.waitForPageLoad();
});

// Navigation vers les retraites
Cypress.Commands.add('visitRetreats', () => {
  cy.visit('/app/retreats');
  cy.waitForPageLoad();
});

// Navigation vers les professionnels
Cypress.Commands.add('visitProfessionals', () => {
  cy.visit('/app/professionals');
  cy.waitForPageLoad();
});

// Recherche de retraites
Cypress.Commands.add('searchRetreats', (query: string) => {
  cy.get('[data-cy="search-input"]').type(query);
  cy.get('[data-cy="search-button"]').click();
  cy.get('[data-cy="search-results"]').should('be.visible');
});

// Réservation d'une retraite
Cypress.Commands.add('bookRetreat', (retreatId: string) => {
  cy.get(`[data-cy="retreat-card-${retreatId}"]`).within(() => {
    cy.get('[data-cy="book-button"]').click();
  });
  
  // Vérifier l'ouverture du modal de réservation
  cy.get('[data-cy="booking-modal"]').should('be.visible');
  cy.get('[data-cy="confirm-booking"]').click();
  
  // Vérifier la confirmation
  cy.checkToast('Réservation confirmée', 'success');
});

// Contact d'un professionnel
Cypress.Commands.add('contactProfessional', (professionalId: string) => {
  cy.get(`[data-cy="professional-card-${professionalId}"]`).within(() => {
    cy.get('[data-cy="contact-button"]').click();
  });
  
  cy.get('[data-cy="contact-modal"]').should('be.visible');
  cy.get('[data-cy="message-input"]').type('Bonjour, je souhaiterais prendre rendez-vous.');
  cy.get('[data-cy="send-message"]').click();
  
  cy.checkToast('Message envoyé', 'success');
});

// Attendre le chargement de la page
Cypress.Commands.add('waitForPageLoad', () => {
  // Attendre que les spinners disparaissent
  cy.get('[data-cy="loading-spinner"]', { timeout: 10000 }).should('not.exist');
  
  // Attendre que le contenu principal soit visible
  cy.get('main', { timeout: 10000 }).should('be.visible');
  
  // Attendre que les images soient chargées
  cy.get('img').should('be.visible').and(($img) => {
    expect($img[0].naturalWidth).to.be.greaterThan(0);
  });
});

// Vérifier l'affichage d'un toast
Cypress.Commands.add('checkToast', (message: string, type?: string) => {
  cy.get('[data-cy="toast"]').should('be.visible').and('contain.text', message);
  
  if (type) {
    cy.get('[data-cy="toast"]').should('have.attr', 'data-variant', type);
  }
  
  // Attendre que le toast disparaisse
  cy.get('[data-cy="toast"]', { timeout: 6000 }).should('not.exist');
});

// Remplir un formulaire
Cypress.Commands.add('fillForm', (formData: Record<string, string>) => {
  Object.entries(formData).forEach(([field, value]) => {
    cy.get(`[data-cy="${field}-input"]`).clear().type(value);
  });
});

// Tests d'accessibilité
Cypress.Commands.add('checkA11y', (context?: string, options?: any) => {
  const defaultOptions = {
    runOnly: {
      type: 'tag',
      values: ['wcag2a', 'wcag2aa']
    }
  };
  
  cy.checkA11y(context, { ...defaultOptions, ...options });
});

// Tests de performance avec Lighthouse
Cypress.Commands.add('lighthouse', (options?: any) => {
  const defaultOptions = {
    performance: 90,
    accessibility: 90,
    'best-practices': 90,
    seo: 80,
    pwa: 50
  };
  
  cy.lighthouse({ ...defaultOptions, ...options });
});

// Commandes pour les tests de responsive
Cypress.Commands.add('testResponsive', () => {
  const viewports = [
    { device: 'iphone-x', width: 375, height: 812 },
    { device: 'ipad-2', width: 768, height: 1024 },
    { device: 'macbook-13', width: 1280, height: 800 }
  ];
  
  viewports.forEach(viewport => {
    cy.viewport(viewport.width, viewport.height);
    cy.waitForPageLoad();
    
    // Vérifier que la navigation est adaptée
    if (viewport.width < 768) {
      cy.get('[data-cy="mobile-menu"]').should('be.visible');
      cy.get('[data-cy="desktop-menu"]').should('not.be.visible');
    } else {
      cy.get('[data-cy="desktop-menu"]').should('be.visible');
    }
  });
});

// Commande pour simuler des erreurs réseau
Cypress.Commands.add('simulateNetworkError', () => {
  cy.intercept('GET', '/api/**', { forceNetworkError: true }).as('networkError');
});

// Commande pour simuler une connexion lente
Cypress.Commands.add('simulateSlowNetwork', () => {
  cy.intercept('GET', '/api/**', (req) => {
    req.reply((res) => {
      res.delay(2000); // Délai de 2 secondes
    });
  }).as('slowNetwork');
});

export {};
