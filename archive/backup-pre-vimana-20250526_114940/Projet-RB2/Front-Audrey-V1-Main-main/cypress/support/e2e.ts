/**
 * Support E2E Cypress - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Commandes personnalisées et configuration globale
 * pour les tests End-to-End.
 */

// Import des commandes Cypress
import './commands';

// Import des plugins
import 'cypress-lighthouse';
import '@cypress/code-coverage/support';
import 'cypress-axe';

// Configuration globale
Cypress.on('uncaught:exception', (err, runnable) => {
  // Ignorer certaines erreurs non critiques
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false;
  }
  return true;
});

// Configuration des timeouts par défaut
Cypress.config('defaultCommandTimeout', 10000);
Cypress.config('requestTimeout', 10000);
Cypress.config('responseTimeout', 10000);

// Hooks globaux
beforeEach(() => {
  // Nettoyer le localStorage avant chaque test
  cy.clearLocalStorage();
  
  // Nettoyer les cookies
  cy.clearCookies();
  
  // Intercepter les appels API pour les tests
  cy.intercept('GET', '/api/health', { statusCode: 200, body: { status: 'ok' } }).as('healthCheck');
  
  // Configuration des viewports responsive
  if (Cypress.env('mobile')) {
    cy.viewport('iphone-x');
  } else if (Cypress.env('tablet')) {
    cy.viewport('ipad-2');
  }
});

afterEach(() => {
  // Logs de débogage en cas d'échec
  if (Cypress.currentTest.state === 'failed') {
    cy.task('log', `Test failed: ${Cypress.currentTest.title}`);
    cy.screenshot(`failed-${Cypress.currentTest.title}`);
  }
});

// Configuration pour les tests d'accessibilité
before(() => {
  if (Cypress.env('accessibility')) {
    cy.visit('/');
    cy.injectAxe();
  }
});

// Configuration pour les tests de performance
if (Cypress.env('lighthouse')) {
  beforeEach(() => {
    cy.lighthouse({
      performance: 90,
      accessibility: 90,
      'best-practices': 90,
      seo: 80,
    });
  });
}
