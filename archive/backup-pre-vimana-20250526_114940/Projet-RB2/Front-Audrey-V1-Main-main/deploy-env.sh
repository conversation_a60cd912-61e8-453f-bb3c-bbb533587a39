#!/bin/bash

# Script de déploiement multi-environnement pour Front-Audrey-V1-Main-main
# Ce script permet de déployer l'application dans différents environnements

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
  log "Vérification des prérequis..."
  
  # Vérifier si Docker est installé
  if ! command -v docker &> /dev/null; then
    error "Docker n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si kubectl est installé
  if ! command -v kubectl &> /dev/null; then
    error "kubectl n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si Helm est installé
  if ! command -v helm &> /dev/null; then
    error "Helm n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  success "Tous les prérequis sont installés."
}

# Afficher l'aide
show_help() {
  echo "Usage: $0 [options] <environment>"
  echo
  echo "Options:"
  echo "  -h, --help                Afficher cette aide"
  echo "  -t, --tag <tag>           Spécifier le tag de l'image (par défaut: latest)"
  echo "  -r, --registry <registry> Spécifier le registre Docker (par défaut: registry.retreat-and-be.com)"
  echo "  -n, --namespace <ns>      Spécifier le namespace Kubernetes (par défaut: retreat-and-be)"
  echo "  -s, --skip-build          Ignorer la construction de l'image Docker"
  echo "  -p, --skip-push           Ignorer l'envoi de l'image vers le registre"
  echo "  -d, --dry-run             Exécuter en mode simulation (sans déploiement réel)"
  echo
  echo "Environnements disponibles:"
  echo "  dev                       Environnement de développement"
  echo "  staging                   Environnement de staging"
  echo "  prod                      Environnement de production"
  echo
  echo "Exemples:"
  echo "  $0 dev                    Déployer dans l'environnement de développement"
  echo "  $0 --tag v1.0.0 prod      Déployer la version v1.0.0 en production"
  echo "  $0 --skip-build staging   Déployer en staging sans reconstruire l'image"
}

# Construire l'image Docker
build_docker_image() {
  log "Construction de l'image Docker pour l'environnement ${ENVIRONMENT}..."
  
  # Construire l'image Docker
  docker build -t ${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend:${IMAGE_TAG} .
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la construction de l'image Docker."
    exit 1
  fi
  
  success "Image Docker construite avec succès."
}

# Pousser l'image vers le registre
push_docker_image() {
  log "Envoi de l'image vers le registre Docker..."
  
  # Se connecter au registre Docker si nécessaire
  if [ -z "$DOCKER_LOGGED_IN" ]; then
    echo "Connexion au registre Docker..."
    read -p "Nom d'utilisateur pour le registre Docker: " DOCKER_USERNAME
    read -sp "Mot de passe pour le registre Docker: " DOCKER_PASSWORD
    echo
    
    echo "$DOCKER_PASSWORD" | docker login ${DOCKER_REGISTRY} -u ${DOCKER_USERNAME} --password-stdin
    
    if [ $? -ne 0 ]; then
      error "Erreur lors de la connexion au registre Docker."
      exit 1
    fi
    
    DOCKER_LOGGED_IN=true
  fi
  
  # Pousser l'image
  docker push ${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend:${IMAGE_TAG}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de l'envoi de l'image vers le registre Docker."
    exit 1
  fi
  
  success "Image Docker envoyée avec succès."
}

# Déployer avec Helm
deploy_with_helm() {
  log "Déploiement dans l'environnement ${ENVIRONMENT} avec Helm..."
  
  # Vérifier si le namespace existe, sinon le créer
  if ! kubectl get namespace ${NAMESPACE} &> /dev/null; then
    log "Création du namespace ${NAMESPACE}..."
    kubectl create namespace ${NAMESPACE}
  fi
  
  # Préparer la commande Helm
  HELM_CMD="helm upgrade --install audrey-frontend-${ENVIRONMENT} ./helm -f helm/environments/${ENVIRONMENT}-values.yaml -n ${NAMESPACE} --set image.tag=${IMAGE_TAG}"
  
  # Ajouter l'option dry-run si nécessaire
  if [ "$DRY_RUN" = true ]; then
    HELM_CMD="${HELM_CMD} --dry-run"
  fi
  
  # Exécuter la commande Helm
  log "Exécution de la commande: ${HELM_CMD}"
  eval ${HELM_CMD}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors du déploiement avec Helm."
    exit 1
  fi
  
  success "Déploiement avec Helm réussi."
}

# Vérifier le déploiement
verify_deployment() {
  if [ "$DRY_RUN" = true ]; then
    log "Mode simulation: vérification du déploiement ignorée."
    return
  fi
  
  log "Vérification du déploiement..."
  
  # Attendre que le déploiement soit prêt
  kubectl rollout status deployment/audrey-frontend-${ENVIRONMENT} -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la vérification du déploiement."
    exit 1
  fi
  
  # Afficher les informations du service
  kubectl get service audrey-frontend-${ENVIRONMENT} -n ${NAMESPACE}
  
  # Afficher les informations de l'ingress
  kubectl get ingress -n ${NAMESPACE} | grep audrey-frontend-${ENVIRONMENT}
  
  success "Déploiement vérifié avec succès."
  
  # Afficher l'URL d'accès
  case ${ENVIRONMENT} in
    dev)
      log "L'application est accessible à l'adresse: https://dev.app.retreat-and-be.com"
      ;;
    staging)
      log "L'application est accessible à l'adresse: https://staging.app.retreat-and-be.com"
      ;;
    prod)
      log "L'application est accessible à l'adresse: https://app.retreat-and-be.com"
      ;;
  esac
}

# Variables par défaut
DOCKER_REGISTRY="registry.retreat-and-be.com"
NAMESPACE="retreat-and-be"
IMAGE_TAG="latest"
SKIP_BUILD=false
SKIP_PUSH=false
DRY_RUN=false
DOCKER_LOGGED_IN=false

# Analyser les arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      exit 0
      ;;
    -t|--tag)
      IMAGE_TAG="$2"
      shift
      shift
      ;;
    -r|--registry)
      DOCKER_REGISTRY="$2"
      shift
      shift
      ;;
    -n|--namespace)
      NAMESPACE="$2"
      shift
      shift
      ;;
    -s|--skip-build)
      SKIP_BUILD=true
      shift
      ;;
    -p|--skip-push)
      SKIP_PUSH=true
      shift
      ;;
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    dev|staging|prod)
      ENVIRONMENT="$1"
      shift
      ;;
    *)
      error "Option inconnue: $1"
      show_help
      exit 1
      ;;
  esac
done

# Vérifier si l'environnement est spécifié
if [ -z "$ENVIRONMENT" ]; then
  error "Aucun environnement spécifié."
  show_help
  exit 1
fi

# Vérifier si l'environnement est valide
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "prod" ]]; then
  error "Environnement invalide: $ENVIRONMENT"
  show_help
  exit 1
fi

# Menu principal
main() {
  echo "================================================"
  echo "  Déploiement de Front-Audrey-V1-Main-main"
  echo "  Environnement: ${ENVIRONMENT}"
  echo "================================================"
  
  # Vérifier les prérequis
  check_prerequisites
  
  # Afficher la configuration
  echo
  echo "Configuration:"
  echo "- Environnement: ${ENVIRONMENT}"
  echo "- Registre Docker: ${DOCKER_REGISTRY}"
  echo "- Namespace Kubernetes: ${NAMESPACE}"
  echo "- Tag de l'image: ${IMAGE_TAG}"
  echo "- Ignorer la construction: ${SKIP_BUILD}"
  echo "- Ignorer l'envoi: ${SKIP_PUSH}"
  echo "- Mode simulation: ${DRY_RUN}"
  echo
  
  # Demander confirmation
  read -p "Voulez-vous continuer avec cette configuration? (o/n): " CONFIRM
  if [[ $CONFIRM != "o" && $CONFIRM != "O" ]]; then
    log "Déploiement annulé."
    exit 0
  fi
  
  # Construire l'image Docker si nécessaire
  if [ "$SKIP_BUILD" = false ]; then
    build_docker_image
  else
    log "Construction de l'image Docker ignorée."
  fi
  
  # Pousser l'image vers le registre si nécessaire
  if [ "$SKIP_PUSH" = false ]; then
    push_docker_image
  else
    log "Envoi de l'image vers le registre Docker ignoré."
  fi
  
  # Déployer avec Helm
  deploy_with_helm
  
  # Vérifier le déploiement
  verify_deployment
  
  success "Déploiement dans l'environnement ${ENVIRONMENT} terminé avec succès!"
}

# Exécuter le script
main
