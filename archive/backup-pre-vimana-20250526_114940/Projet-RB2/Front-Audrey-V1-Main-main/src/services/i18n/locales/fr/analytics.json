{"dashboard": {"title": "Tableau de bord d'analyse", "tabs": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "engagement": "Engagement", "audience": "Audience", "revenue": "<PERSON>en<PERSON>", "content": "Contenu", "custom": "<PERSON><PERSON><PERSON><PERSON>"}}, "filters": {"allContentTypes": "Tous les types de contenu", "retreat": "Retraite", "post": "Publication", "video": "Vidéo", "article": "Article", "allTime": "Tout le temps", "last30Days": "30 derniers jours", "last7Days": "7 derniers jours", "custom": "<PERSON><PERSON><PERSON><PERSON>"}, "overview": {"title": "Aperçu des performances", "totalViews": "Vues totales", "totalFollowers": "<PERSON><PERSON><PERSON><PERSON>", "totalRevenue": "<PERSON><PERSON><PERSON>", "engagementSummary": "R<PERSON><PERSON><PERSON> de l'engagement", "audienceSummary": "Résumé de l'audience", "revenueSummary": "Résumé des revenus", "topContent": "Contenu le plus performant"}, "engagement": {"title": "Métriques d'engagement", "views": "<PERSON><PERSON>", "likes": "<PERSON>'aime", "comments": "Commentaires", "shares": "Partages", "bookmarks": "Enregistrements", "clickThroughs": "C<PERSON>s", "engagementRate": "Taux d'engagement", "engagementRateDescription": "Calculé comme (J'aime + Commentaires + Partages) / Vues", "clickThroughRate": "<PERSON><PERSON> de c<PERSON>s", "clickThroughRateDescription": "Calculé comme Clics / Vues", "viewsOverTime": "Vues au fil du temps", "engagementOverTime": "Engagement au fil du temps"}, "audience": {"title": "Métriques d'audience", "totalFollowers": "<PERSON><PERSON><PERSON><PERSON>", "newFollowers": "Nouveaux abonnés", "lostFollowers": "<PERSON><PERSON><PERSON><PERSON> perdus", "netGrowth": "Croissance nette", "activeFollowers": "Abonnés actifs", "demographics": "Démographie", "age": "Âge", "gender": "Genre", "location": "Localisation", "followersOverTime": "Abonnés au fil du temps", "growthOverTime": "Croissance au fil du temps", "retentionRate": "<PERSON>x de ré<PERSON>tion", "retentionRateDescription": "Pourcentage d'abonnés qui restent actifs"}, "revenue": {"title": "Métriques de revenus", "totalRevenue": "<PERSON><PERSON><PERSON>", "bySources": "Revenus par source", "subscription": "Abonnement", "donation": "Don", "productSale": "Vente de produit", "affiliate": "Affiliation", "advertising": "Publicité", "sponsorship": "Parrainage", "revenueOverTime": "Revenus au fil du temps", "topSources": "Principales sources de revenus"}, "content": {"title": "<PERSON><PERSON><PERSON> de conten<PERSON>", "subtitle": "Analyse d<PERSON> pour le contenu de type : {{contentType}}", "views": "<PERSON><PERSON>", "engagementRate": "Taux d'engagement", "interactions": "Interactions", "revenue": "<PERSON>en<PERSON>", "viewsOverTime": "Vues au fil du temps", "engagementOverTime": "Engagement au fil du temps", "revenueOverTime": "Revenus au fil du temps", "topPerforming": "Contenu le plus performant", "table": {"content": "Contenu", "type": "Type", "views": "<PERSON><PERSON>", "engagement": "Engagement", "engagementRate": "Taux d'eng.", "revenue": "<PERSON>en<PERSON>", "actions": "Actions"}, "viewDetails": "Voir les détails", "noContent": "Aucun contenu trouvé", "missingParams": "Paramètres manquants pour l'analyse de contenu", "fetchError": "Erreur lors de la récupération des métriques de contenu"}, "custom": {"title": "Tableaux de bord personnalisés", "noDashboards": "Aucun tableau de bord personnalisé trouvé", "createDashboard": "<PERSON><PERSON>er un tableau de bord", "editDashboard": "Modifier le tableau de bord", "deleteDashboard": "Supp<PERSON>er le tableau de bord", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer ce tableau de bord ?", "form": {"name": "Nom", "namePlaceholder": "Entrez un nom pour le tableau de bord", "description": "Description", "descriptionPlaceholder": "Entrez une description pour le tableau de bord", "isDefault": "Définir comme tableau de bord par défaut", "submit": "Enregistrer le tableau de bord", "cancel": "Annuler"}, "widget": {"addWidget": "Ajouter un widget", "editWidget": "Modifier le widget", "deleteWidget": "Supprimer le widget", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer ce widget ?", "form": {"type": "Type de widget", "title": "Titre", "titlePlaceholder": "Entrez un titre pour le widget", "metric": "Métrique", "timeRange": "Période", "submit": "Enregis<PERSON><PERSON> le widget", "cancel": "Annuler"}, "types": {"lineChart": "Graphique linéaire", "barChart": "Graphique à barres", "pieChart": "Graphique circulaire", "counter": "Compteur", "table": "<PERSON><PERSON>", "heatmap": "<PERSON><PERSON> de chaleur", "map": "<PERSON><PERSON>", "gauge": "Jauge"}}}, "forecasting": {"title": "Prévisions", "engagement": "Prévisions d'engagement", "audience": "Prévisions d'audience", "revenue": "Prévisions de revenus", "confidenceLevel": "Niveau de confiance", "nextMonth": "<PERSON><PERSON> prochain", "next3Months": "3 prochains mois", "next6Months": "6 prochains mois"}, "benchmarks": {"title": "Analyse comparative", "category": "<PERSON><PERSON><PERSON><PERSON>", "yourPerformance": "Votre performance", "categoryAverage": "Moyenne de la catégorie", "percentile": "Percentile", "metrics": {"views": "<PERSON><PERSON>", "engagementRate": "Taux d'engagement", "followers": "Abonnés", "growth": "Croissance", "revenue": "<PERSON>en<PERSON>"}}, "noData": "Aucune donnée disponible pour la période sélectionnée", "accessDeniedMessage": "Vous n'avez pas les autorisations nécessaires pour accéder à ces analyses. Veuillez contacter un administrateur si vous pensez que c'est une erreur."}