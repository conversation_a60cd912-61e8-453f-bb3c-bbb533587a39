import { apiClient } from './apiClient';

export interface ABTest {
  id: string;
  name: string;
  description: string;
  strategies: string[];
  weights: number[];
  startDate: string;
  endDate: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ABTestMetrics {
  testId: string;
  strategy: string;
  impressions: number;
  clicks: number;
  conversions: number;
  revenue: number;
  updatedAt: string;
}

export interface ConversionRates {
  [strategy: string]: {
    ctr: number;
    cvr: number;
    revenue: number;
  };
}

export interface CreateABTestRequest {
  name: string;
  description: string;
  strategies: string[];
  weights?: number[];
  startDate?: string;
  endDate?: string;
}

export interface UpdateABTestRequest {
  name?: string;
  description?: string;
  strategies?: string[];
  weights?: number[];
  startDate?: string;
  endDate?: string;
  isActive?: boolean;
}

export interface TrackEventRequest {
  userId: string;
  testId: string;
  strategy: string;
  recommendationId: string;
  amount?: number;
}

class ABTestingService {
  /**
   * Récupère tous les tests A/B actifs
   */
  async getActiveTests(): Promise<ABTest[]> {
    try {
      const response = await apiClient.get('/ab-testing');
      return response.data;
    } catch (error) {
      console.error('Error fetching active A/B tests:', error);
      throw error;
    }
  }

  /**
   * Récupère un test A/B par son ID
   */
  async getTestById(id: string): Promise<ABTest> {
    try {
      const response = await apiClient.get(`/ab-testing/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching A/B test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Crée un nouveau test A/B
   */
  async createTest(request: CreateABTestRequest): Promise<ABTest> {
    try {
      const response = await apiClient.post('/ab-testing', request);
      return response.data;
    } catch (error) {
      console.error('Error creating A/B test:', error);
      throw error;
    }
  }

  /**
   * Met à jour un test A/B
   */
  async updateTest(id: string, request: UpdateABTestRequest): Promise<ABTest> {
    try {
      const response = await apiClient.put(`/ab-testing/${id}`, request);
      return response.data;
    } catch (error) {
      console.error(`Error updating A/B test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Termine un test A/B
   */
  async endTest(id: string): Promise<ABTest> {
    try {
      const response = await apiClient.delete(`/ab-testing/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error ending A/B test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les métriques d'un test A/B
   */
  async getTestMetrics(id: string): Promise<{
    test: ABTest;
    metrics: ABTestMetrics[];
    conversionRates: ConversionRates;
  }> {
    try {
      const response = await apiClient.get(`/ab-testing/${id}/metrics`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching metrics for A/B test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Détermine la stratégie gagnante d'un test A/B
   */
  async determineWinner(id: string): Promise<{ winner: string | null }> {
    try {
      const response = await apiClient.get(`/ab-testing/${id}/winner`);
      return response.data;
    } catch (error) {
      console.error(`Error determining winner for A/B test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Assigne une stratégie à un utilisateur pour un test A/B
   */
  async assignStrategy(userId: string, testId: string): Promise<{ strategy: string }> {
    try {
      const response = await apiClient.post('/ab-testing/assign', { userId, testId });
      return response.data;
    } catch (error) {
      console.error(`Error assigning strategy for user ${userId} and test ${testId}:`, error);
      throw error;
    }
  }

  /**
   * Enregistre une impression pour une recommandation
   */
  async trackImpression(request: TrackEventRequest): Promise<{ success: boolean }> {
    try {
      const response = await apiClient.post('/ab-testing/track/impression', request);
      return response.data;
    } catch (error) {
      console.error('Error tracking impression:', error);
      throw error;
    }
  }

  /**
   * Enregistre un clic pour une recommandation
   */
  async trackClick(request: TrackEventRequest): Promise<{ success: boolean }> {
    try {
      const response = await apiClient.post('/ab-testing/track/click', request);
      return response.data;
    } catch (error) {
      console.error('Error tracking click:', error);
      throw error;
    }
  }

  /**
   * Enregistre une conversion pour une recommandation
   */
  async trackConversion(request: TrackEventRequest): Promise<{ success: boolean }> {
    try {
      const response = await apiClient.post('/ab-testing/track/conversion', request);
      return response.data;
    } catch (error) {
      console.error('Error tracking conversion:', error);
      throw error;
    }
  }
}

export const abTestingService = new ABTestingService();
