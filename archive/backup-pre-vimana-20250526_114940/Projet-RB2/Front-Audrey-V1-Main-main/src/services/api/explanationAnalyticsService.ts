import { apiClient } from './apiClient';

/**
 * Interface pour les métriques d'explication
 */
export interface ExplanationMetrics {
  /** Nombre total d'explications */
  totalExplanations: number;
  
  /** Nombre d'explications vues */
  viewedExplanations: number;
  
  /** Taux de consultation des explications */
  viewRate: number;
  
  /** Temps moyen passé sur les explications (en secondes) */
  averageTimeSpent: number;
  
  /** Taux de clics sur les explications */
  clickThroughRate: number;
  
  /** Taux de conversion après avoir vu une explication */
  conversionRate: number;
  
  /** Score moyen de satisfaction */
  averageSatisfactionScore: number;
  
  /** Métriques par type de facteur */
  metricsByFactorType: Record<string, {
    count: number;
    viewRate: number;
    clickThroughRate: number;
    conversionRate: number;
    averageSatisfactionScore: number;
  }>;
  
  /** Métriques par segment utilisateur */
  metricsByUserSegment: Record<string, {
    count: number;
    viewRate: number;
    clickThroughRate: number;
    conversionRate: number;
    averageSatisfactionScore: number;
  }>;
}

/**
 * Interface pour les tendances d'explication
 */
export interface ExplanationTrends {
  /** Période de temps */
  period: 'day' | 'week' | 'month';
  
  /** Données de tendance */
  data: Array<{
    /** Date */
    date: string;
    
    /** Nombre d'explications */
    explanationCount: number;
    
    /** Taux de consultation */
    viewRate: number;
    
    /** Taux de clics */
    clickThroughRate: number;
    
    /** Taux de conversion */
    conversionRate: number;
  }>;
}

/**
 * Interface pour l'impact des explications
 */
export interface ExplanationImpact {
  /** Impact sur l'engagement utilisateur */
  userEngagement: {
    /** Changement dans le temps passé sur la plateforme */
    timeSpentChange: number;
    
    /** Changement dans le nombre de pages vues */
    pageViewsChange: number;
    
    /** Changement dans le taux de rebond */
    bounceRateChange: number;
  };
  
  /** Impact sur les conversions */
  conversions: {
    /** Changement dans le taux de conversion */
    conversionRateChange: number;
    
    /** Changement dans le panier moyen */
    averageOrderValueChange: number;
    
    /** Changement dans le taux d'abandon de panier */
    cartAbandonmentRateChange: number;
  };
  
  /** Impact sur la rétention */
  retention: {
    /** Changement dans le taux de rétention */
    retentionRateChange: number;
    
    /** Changement dans la fréquence des visites */
    visitFrequencyChange: number;
    
    /** Changement dans le taux de désabonnement */
    churnRateChange: number;
  };
}

/**
 * Interface pour les paramètres de filtrage des analyses
 */
export interface AnalyticsFilterParams {
  /** Date de début */
  startDate?: Date;
  
  /** Date de fin */
  endDate?: Date;
  
  /** Types de facteur */
  factorTypes?: string[];
  
  /** Segments utilisateur */
  userSegments?: string[];
  
  /** Langues */
  languages?: string[];
  
  /** Types d'explication */
  explanationTypes?: string[];
  
  /** Styles d'explication */
  explanationStyles?: string[];
}

/**
 * Service pour l'analyse des explications
 */
class ExplanationAnalyticsService {
  /**
   * Récupère les métriques d'explication
   * @param filters Paramètres de filtrage
   * @returns Métriques d'explication
   */
  async getMetrics(filters?: AnalyticsFilterParams): Promise<ExplanationMetrics> {
    try {
      const params = this.buildQueryParams(filters);
      const response = await apiClient.get('/recommendation/explanation-analytics/metrics', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques d\'explication:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les tendances d'explication
   * @param period Période de temps
   * @param filters Paramètres de filtrage
   * @returns Tendances d'explication
   */
  async getTrends(
    period: 'day' | 'week' | 'month' = 'day',
    filters?: AnalyticsFilterParams,
  ): Promise<ExplanationTrends> {
    try {
      const params = {
        period,
        ...this.buildQueryParams(filters),
      };
      const response = await apiClient.get('/recommendation/explanation-analytics/trends', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des tendances d\'explication:', error);
      throw error;
    }
  }
  
  /**
   * Récupère l'impact des explications
   * @param filters Paramètres de filtrage
   * @returns Impact des explications
   */
  async getImpact(filters?: AnalyticsFilterParams): Promise<ExplanationImpact> {
    try {
      const params = this.buildQueryParams(filters);
      const response = await apiClient.get('/recommendation/explanation-analytics/impact', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'impact des explications:', error);
      throw error;
    }
  }
  
  /**
   * Construit les paramètres de requête à partir des filtres
   * @param filters Paramètres de filtrage
   * @returns Paramètres de requête
   */
  private buildQueryParams(filters?: AnalyticsFilterParams): Record<string, any> {
    if (!filters) {
      return {};
    }
    
    const params: Record<string, any> = {};
    
    if (filters.startDate) {
      params.startDate = filters.startDate.toISOString();
    }
    
    if (filters.endDate) {
      params.endDate = filters.endDate.toISOString();
    }
    
    if (filters.factorTypes && filters.factorTypes.length > 0) {
      params.factorTypes = filters.factorTypes.join(',');
    }
    
    if (filters.userSegments && filters.userSegments.length > 0) {
      params.userSegments = filters.userSegments.join(',');
    }
    
    if (filters.languages && filters.languages.length > 0) {
      params.languages = filters.languages.join(',');
    }
    
    if (filters.explanationTypes && filters.explanationTypes.length > 0) {
      params.explanationTypes = filters.explanationTypes.join(',');
    }
    
    if (filters.explanationStyles && filters.explanationStyles.length > 0) {
      params.explanationStyles = filters.explanationStyles.join(',');
    }
    
    return params;
  }
  
  /**
   * Récupère les types de facteur disponibles
   * @returns Types de facteur
   */
  getFactorTypes(): string[] {
    return [
      'similarity',
      'popularity',
      'rating',
      'price',
      'location',
      'duration',
      'instructor',
      'amenities',
      'activities',
      'theme',
    ];
  }
  
  /**
   * Récupère les segments utilisateur disponibles
   * @returns Segments utilisateur
   */
  getUserSegments(): string[] {
    return [
      'new_users',
      'returning_users',
      'frequent_users',
      'high_value_users',
    ];
  }
  
  /**
   * Récupère les langues disponibles
   * @returns Langues
   */
  getLanguages(): { code: string; name: string }[] {
    return [
      { code: 'fr', name: 'Français' },
      { code: 'en', name: 'English' },
      { code: 'es', name: 'Español' },
      { code: 'de', name: 'Deutsch' },
      { code: 'it', name: 'Italiano' },
    ];
  }
  
  /**
   * Récupère les types d'explication disponibles
   * @returns Types d'explication
   */
  getExplanationTypes(): string[] {
    return [
      'feature_based',
      'user_based',
      'item_based',
      'hybrid',
    ];
  }
  
  /**
   * Récupère les styles d'explication disponibles
   * @returns Styles d'explication
   */
  getExplanationStyles(): string[] {
    return [
      'TECHNICAL',
      'CONVERSATIONAL',
      'CONCISE',
      'NARRATIVE',
      'EDUCATIONAL',
    ];
  }
  
  /**
   * Formate un pourcentage
   * @param value Valeur (0-1)
   * @returns Pourcentage formaté
   */
  formatPercent(value: number): string {
    return `${(value * 100).toFixed(1)}%`;
  }
  
  /**
   * Formate un changement en pourcentage
   * @param value Valeur (-1 à 1)
   * @returns Changement formaté
   */
  formatChange(value: number): string {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${(value * 100).toFixed(1)}%`;
  }
  
  /**
   * Formate une durée en secondes
   * @param seconds Durée en secondes
   * @returns Durée formatée
   */
  formatDuration(seconds: number): string {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
    }
  }
}

export const explanationAnalyticsService = new ExplanationAnalyticsService();
