import axios, { AxiosInstance, AxiosRequestConfig, AxiosProgressEvent } from 'axios';
import {
  requestInterceptor as monitoringRequestInterceptor,
  responseInterceptor as monitoringResponseInterceptor,
  errorInterceptor as monitoringErrorInterceptor,
} from './interceptors/monitoringInterceptor';
import {
  requestInterceptor as cacheRequestInterceptor,
  responseInterceptor as cacheResponseInterceptor,
} from './interceptors/cacheInterceptor';
import { createRetryInterceptor } from './interceptors/retryInterceptor';

// Configuration de base pour l'API
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:7000';

// Define an extended config interface for type safety with onUploadProgress
export interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
}

/**
 * Client API centralisé pour toutes les communications avec le backend
 * Gère automatiquement l'ajout des tokens d'authentification et le rafraîchissement des tokens
 */
class ApiClient {
  private api: AxiosInstance;
  private refreshTokenRequest: Promise<string> | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: API_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 15000, // 15 secondes
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Intercepteurs de requête
    this.api.interceptors.request.use(monitoringRequestInterceptor);
    this.api.interceptors.request.use(cacheRequestInterceptor);

    // Intercepteur de requête pour ajouter le token d'authentification
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Intercepteurs de réponse
    this.api.interceptors.response.use(monitoringResponseInterceptor);
    this.api.interceptors.response.use(cacheResponseInterceptor);

    // Intercepteur de réponse pour gérer les erreurs et le rafraîchissement du token
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Si l'erreur est 401 (non autorisé) et que nous n'avons pas déjà essayé de rafraîchir le token
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Essayer de rafraîchir le token
            const newToken = await this.refreshToken();

            // Mettre à jour le token dans le localStorage
            localStorage.setItem('token', newToken);

            // Mettre à jour le header d'autorisation
            originalRequest.headers.Authorization = `Bearer ${newToken}`;

            // Réessayer la requête originale
            return this.api(originalRequest);
          } catch (refreshError) {
            // Si le rafraîchissement échoue, déconnecter l'utilisateur
            localStorage.removeItem('token');
            localStorage.removeItem('refreshToken');

            // Rediriger vers la page de connexion
            window.location.href = '/login';

            return Promise.reject(refreshError);
          }
        }

        // Appliquer l'intercepteur de monitoring pour les erreurs
        monitoringErrorInterceptor(error);

        // Appliquer l'intercepteur de retry pour les erreurs réseau
        return createRetryInterceptor(this.api)(error);
      }
    );
  }

  private async refreshToken(): Promise<string> {
    // Si une requête de rafraîchissement est déjà en cours, retourner cette promesse
    if (this.refreshTokenRequest) {
      return this.refreshTokenRequest;
    }

    // Créer une nouvelle requête de rafraîchissement
    this.refreshTokenRequest = new Promise<string>((resolve, reject) => {
      const refreshTokenValue = localStorage.getItem('refreshToken');

      if (!refreshTokenValue) {
        // Reject immediately if no refresh token is available
        // Also, clear the refreshTokenRequest so new attempts can be made if a token becomes available later.
        this.refreshTokenRequest = null;
        reject(new Error('No refresh token available'));
        return;
      }

      axios
        .post(`${API_URL}/auth/refresh`, {
          refreshToken: refreshTokenValue,
        })
        .then((response) => {
          const { accessToken, refreshToken: newRefreshToken } = response.data;
          localStorage.setItem('refreshToken', newRefreshToken);
          resolve(accessToken);
        })
        .catch((error) => {
          // Important: clear refreshTokenRequest on failure to allow future retries
          this.refreshTokenRequest = null;
          reject(error);
        });
    });

    return this.refreshTokenRequest;
  }

  // Méthodes pour effectuer des requêtes HTTP
  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.get<T>(url, config);
    return response.data;
  }

  public async post<T>(url: string, data?: unknown, config?: ExtendedAxiosRequestConfig): Promise<T> {
    const response = await this.api.post<T>(url, data, config);
    return response.data;
  }

  public async put<T>(url: string, data?: unknown, config?: ExtendedAxiosRequestConfig): Promise<T> {
    const response = await this.api.put<T>(url, data, config);
    return response.data;
  }

  public async patch<T>(url: string, data?: unknown, config?: ExtendedAxiosRequestConfig): Promise<T> {
    const response = await this.api.patch<T>(url, data, config);
    return response.data;
  }

  public async delete<T>(url: string, config?: ExtendedAxiosRequestConfig): Promise<T> {
    const response = await this.api.delete<T>(url, config);
    return response.data;
  }
}

// Exporter une instance unique du client API
export const apiClient = new ApiClient();
