import { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// Interface pour les entrées de cache
interface CacheEntry {
  data: AxiosResponse;
  expiry: number;
}

// Configuration du cache
interface CacheConfig {
  maxAge: number; // Durée de vie du cache en millisecondes
  maxSize: number; // Nombre maximum d'entrées dans le cache
  excludedUrls: RegExp[]; // URLs à exclure du cache
  methods: string[]; // Méthodes HTTP à mettre en cache
}

// Configuration par défaut
const defaultConfig: CacheConfig = {
  maxAge: 5 * 60 * 1000, // 5 minutes
  maxSize: 100, // 100 entrées maximum
  excludedUrls: [/\/auth\//], // Exclure les URLs d'authentification
  methods: ['GET'], // Mettre en cache uniquement les requêtes GET
};

// Classe de cache
class ApiCache {
  private cache: Map<string, CacheEntry>;
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.cache = new Map();
    this.config = { ...defaultConfig, ...config };
  }

  // Générer une clé de cache à partir de la configuration de la requête
  private generateCacheKey(config: AxiosRequestConfig): string {
    const { url, method, params, data } = config;
    return `${method}_${url}_${JSON.stringify(params)}_${JSON.stringify(data)}`;
  }

  // Vérifier si une requête est cacheable
  private isCacheable(config: AxiosRequestConfig): boolean {
    // Vérifier la méthode HTTP
    if (!this.config.methods.includes(config.method?.toUpperCase() || '')) {
      return false;
    }

    // Vérifier si l'URL est exclue
    const url = config.url || '';
    return !this.config.excludedUrls.some((pattern) => pattern.test(url));
  }

  // Obtenir une entrée du cache
  get(config: AxiosRequestConfig): AxiosResponse | null {
    if (!this.isCacheable(config)) {
      return null;
    }

    const key = this.generateCacheKey(config);
    const entry = this.cache.get(key);

    // Vérifier si l'entrée existe et n'est pas expirée
    if (entry && entry.expiry > Date.now()) {
      return entry.data;
    }

    // Supprimer l'entrée expirée
    if (entry) {
      this.cache.delete(key);
    }

    return null;
  }

  // Ajouter une entrée au cache
  set(config: AxiosRequestConfig, response: AxiosResponse): void {
    if (!this.isCacheable(config)) {
      return;
    }

    const key = this.generateCacheKey(config);
    const expiry = Date.now() + this.config.maxAge;

    // Limiter la taille du cache
    if (this.cache.size >= this.config.maxSize) {
      // Supprimer l'entrée la plus ancienne
      const oldestKeyIteratorResult = this.cache.keys().next();
      if (!oldestKeyIteratorResult.done && oldestKeyIteratorResult.value !== undefined) {
        const oldestKey = oldestKeyIteratorResult.value;
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, { data: response, expiry });
  }

  // Invalider une entrée du cache
  invalidate(config: AxiosRequestConfig): void {
    const key = this.generateCacheKey(config);
    this.cache.delete(key);
  }

  // Invalider toutes les entrées du cache qui correspondent à un modèle d'URL
  invalidatePattern(urlPattern: RegExp): void {
    for (const key of this.cache.keys()) {
      if (urlPattern.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  // Vider le cache
  clear(): void {
    this.cache.clear();
  }

  // Obtenir la taille actuelle du cache
  size(): number {
    return this.cache.size;
  }

  // Obtenir les statistiques du cache
  getStats(): { size: number; maxSize: number; hitRate: number } {
    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: this.hitRate,
    };
  }

  // Taux de succès du cache (à implémenter)
  private hitRate: number = 0;
  private hits: number = 0;
  private misses: number = 0;

  // Enregistrer un succès du cache
  recordHit(): void {
    this.hits++;
    this.updateHitRate();
  }

  // Enregistrer un échec du cache
  recordMiss(): void {
    this.misses++;
    this.updateHitRate();
  }

  // Mettre à jour le taux de succès
  private updateHitRate(): void {
    const total = this.hits + this.misses;
    this.hitRate = total > 0 ? this.hits / total : 0;
  }
}

// Créer une instance du cache
export const apiCache = new ApiCache();

// Intercepteur de requête
export const requestInterceptor = (
  config: InternalAxiosRequestConfig
): InternalAxiosRequestConfig => {
  // Vérifier si la réponse est dans le cache
  const cachedResponse = apiCache.get(config);

  if (cachedResponse) {
    // Enregistrer un succès du cache
    apiCache.recordHit();

    // Créer une promesse résolue avec la réponse mise en cache
    return {
      ...config,
      adapter: () => Promise.resolve(cachedResponse),
    } as InternalAxiosRequestConfig;
  }

  // Enregistrer un échec du cache
  apiCache.recordMiss();

  return config;
};

// Intercepteur de réponse
export const responseInterceptor = (response: AxiosResponse): AxiosResponse => {
  // Mettre en cache la réponse
  apiCache.set(response.config, response);

  return response;
};
