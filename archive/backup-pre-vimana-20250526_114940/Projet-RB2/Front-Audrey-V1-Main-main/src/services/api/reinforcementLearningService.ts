import { apiClient } from './apiClient';

/**
 * Types d'agents d'apprentissage par renforcement
 */
export enum RLAgentType {
  /** Agent Q-Learning */
  Q_LEARNING = 'Q_LEARNING',
  
  /** Agent SARSA (State-Action-Reward-State-Action) */
  SARSA = 'SARSA',
  
  /** Agent DQN (Deep Q-Network) */
  DQN = 'DQN',
  
  /** Agent A2C (Advantage Actor-Critic) */
  A2C = 'A2C',
  
  /** Agent personnalisé */
  CUSTOM = 'CUSTOM',
}

/**
 * États possibles d'un agent d'apprentissage par renforcement
 */
export enum RLAgentState {
  /** Agent en cours d'initialisation */
  INITIALIZING = 'INITIALIZING',
  
  /** Agent prêt mais inactif */
  IDLE = 'IDLE',
  
  /** Agent en cours d'apprentissage */
  LEARNING = 'LEARNING',
  
  /** Agent en cours d'exploitation (utilisation des connaissances acquises) */
  EXPLOITING = 'EXPLOITING',
  
  /** Agent en pause */
  PAUSED = 'PAUSED',
  
  /** Agent arrêté */
  STOPPED = 'STOPPED',
  
  /** Agent en erreur */
  ERROR = 'ERROR',
}

/**
 * Stratégies d'exploration
 */
export enum ExplorationStrategy {
  /** Epsilon-greedy */
  EPSILON_GREEDY = 'EPSILON_GREEDY',
  
  /** Softmax */
  SOFTMAX = 'SOFTMAX',
  
  /** UCB (Upper Confidence Bound) */
  UCB = 'UCB',
  
  /** Thompson Sampling */
  THOMPSON_SAMPLING = 'THOMPSON_SAMPLING',
}

/**
 * Interface pour la configuration d'un agent
 */
export interface RLAgentConfig {
  /** Taux d'apprentissage (alpha) */
  learningRate: number;
  
  /** Facteur d'actualisation (gamma) */
  discountFactor: number;
  
  /** Taux d'exploration (epsilon) */
  explorationRate: number;
  
  /** Stratégie d'exploration */
  explorationStrategy: ExplorationStrategy;
  
  /** Taille de la mémoire d'expérience */
  experienceMemorySize: number;
  
  /** Taille du lot d'apprentissage */
  batchSize: number;
  
  /** Fréquence de mise à jour du réseau cible (pour DQN) */
  targetNetworkUpdateFrequency?: number;
  
  /** Paramètres spécifiques à l'agent */
  agentSpecificParams?: Record<string, any>;
}

/**
 * Interface pour les statistiques d'un agent
 */
export interface RLAgentStats {
  /** Nombre total d'épisodes */
  totalEpisodes: number;
  
  /** Nombre total d'étapes */
  totalSteps: number;
  
  /** Récompense cumulée */
  cumulativeReward: number;
  
  /** Récompense moyenne par épisode */
  averageRewardPerEpisode: number;
  
  /** Taux de convergence */
  convergenceRate: number;
  
  /** Taux d'exploration actuel */
  currentExplorationRate: number;
  
  /** Historique des récompenses */
  rewardHistory: number[];
  
  /** Historique des taux d'erreur */
  errorHistory: number[];
  
  /** Métriques spécifiques à l'agent */
  agentSpecificMetrics?: Record<string, any>;
}

/**
 * Interface pour un agent d'apprentissage par renforcement
 */
export interface RLAgent {
  /** ID de l'agent */
  id: string;
  
  /** Nom de l'agent */
  name: string;
  
  /** Description de l'agent */
  description: string;
  
  /** Type d'agent */
  agentType: RLAgentType;
  
  /** État actuel de l'agent */
  state: RLAgentState;
  
  /** Configuration de l'agent */
  config: RLAgentConfig;
  
  /** Statistiques de performance */
  stats: RLAgentStats;
  
  /** Date de création */
  createdAt: string;
  
  /** Date de dernière mise à jour */
  updatedAt: string;
}

/**
 * Interface pour la création d'un agent
 */
export interface CreateRLAgentRequest {
  /** Nom de l'agent */
  name: string;
  
  /** Description de l'agent */
  description: string;
  
  /** Type d'agent */
  agentType: RLAgentType;
  
  /** Configuration de l'agent */
  config: RLAgentConfig;
}

/**
 * Interface pour la mise à jour d'un agent
 */
export interface UpdateRLAgentRequest {
  /** Nom de l'agent */
  name?: string;
  
  /** Description de l'agent */
  description?: string;
  
  /** État de l'agent */
  state?: RLAgentState;
  
  /** Configuration de l'agent */
  config?: Partial<RLAgentConfig>;
}

/**
 * Interface pour l'enregistrement d'une interaction
 */
export interface RecordInteractionRequest {
  /** ID de l'explication */
  explanationId: string;
  
  /** Type d'interaction */
  interactionType: string;
  
  /** Données de l'interaction */
  data: Record<string, any>;
  
  /** Durée de l'interaction (en secondes) */
  duration?: number;
}

/**
 * Interface pour une composante de récompense
 */
export interface RewardComponent {
  /** Nom de la composante */
  name: string;
  
  /** Valeur de la composante */
  value: number;
  
  /** Poids de la composante */
  weight: number;
}

/**
 * Interface pour une récompense
 */
export interface RLReward {
  /** Valeur de la récompense */
  value: number;
  
  /** Composantes de la récompense */
  components: RewardComponent[];
  
  /** Métadonnées de la récompense */
  metadata?: Record<string, any>;
}

/**
 * Interface pour la configuration de la fonction de récompense
 */
export interface RewardFunctionConfig {
  /** Poids des composantes de récompense */
  componentWeights: Record<string, number>;
  
  /** Paramètres supplémentaires */
  additionalParams?: Record<string, any>;
}

/**
 * Service pour l'apprentissage par renforcement
 */
class ReinforcementLearningService {
  /**
   * Récupère tous les agents
   * @returns Liste des agents
   */
  async getAllAgents(): Promise<RLAgent[]> {
    try {
      const response = await apiClient.get('/recommendation/reinforcement-learning/agents');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des agents:', error);
      throw error;
    }
  }
  
  /**
   * Récupère un agent par son ID
   * @param agentId ID de l'agent
   * @returns Agent
   */
  async getAgent(agentId: string): Promise<RLAgent> {
    try {
      const response = await apiClient.get(`/recommendation/reinforcement-learning/agents/${agentId}`);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'agent ${agentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Crée un nouvel agent
   * @param createRequest Données de création
   * @returns Agent créé
   */
  async createAgent(createRequest: CreateRLAgentRequest): Promise<RLAgent> {
    try {
      const response = await apiClient.post('/recommendation/reinforcement-learning/agents', createRequest);
      return response;
    } catch (error) {
      console.error('Erreur lors de la création de l\'agent:', error);
      throw error;
    }
  }
  
  /**
   * Met à jour un agent
   * @param agentId ID de l'agent
   * @param updateRequest Données de mise à jour
   * @returns Agent mis à jour
   */
  async updateAgent(agentId: string, updateRequest: UpdateRLAgentRequest): Promise<RLAgent> {
    try {
      const response = await apiClient.put(`/recommendation/reinforcement-learning/agents/${agentId}`, updateRequest);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de l'agent ${agentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Supprime un agent
   * @param agentId ID de l'agent
   * @returns Résultat de la suppression
   */
  async deleteAgent(agentId: string): Promise<void> {
    try {
      await apiClient.delete(`/recommendation/reinforcement-learning/agents/${agentId}`);
    } catch (error) {
      console.error(`Erreur lors de la suppression de l'agent ${agentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Démarre l'apprentissage d'un agent
   * @param agentId ID de l'agent
   * @returns Agent mis à jour
   */
  async startLearning(agentId: string): Promise<RLAgent> {
    try {
      const response = await apiClient.post(`/recommendation/reinforcement-learning/agents/${agentId}/start`);
      return response;
    } catch (error) {
      console.error(`Erreur lors du démarrage de l'apprentissage de l'agent ${agentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Met en pause l'apprentissage d'un agent
   * @param agentId ID de l'agent
   * @returns Agent mis à jour
   */
  async pauseLearning(agentId: string): Promise<RLAgent> {
    try {
      const response = await apiClient.post(`/recommendation/reinforcement-learning/agents/${agentId}/pause`);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la mise en pause de l'apprentissage de l'agent ${agentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Arrête l'apprentissage d'un agent
   * @param agentId ID de l'agent
   * @returns Agent mis à jour
   */
  async stopLearning(agentId: string): Promise<RLAgent> {
    try {
      const response = await apiClient.post(`/recommendation/reinforcement-learning/agents/${agentId}/stop`);
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'arrêt de l'apprentissage de l'agent ${agentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Enregistre une interaction avec une explication
   * @param interactionRequest Données de l'interaction
   * @returns Récompense calculée
   */
  async recordInteraction(interactionRequest: RecordInteractionRequest): Promise<RLReward> {
    try {
      const response = await apiClient.post('/recommendation/reinforcement-learning/interactions', interactionRequest);
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement de l\'interaction:', error);
      throw error;
    }
  }
  
  /**
   * Configure la fonction de récompense pour un agent
   * @param agentId ID de l'agent
   * @param config Configuration de la fonction de récompense
   * @returns Résultat de la configuration
   */
  async configureRewardFunction(agentId: string, config: RewardFunctionConfig): Promise<{ success: boolean }> {
    try {
      const response = await apiClient.put(`/recommendation/reinforcement-learning/agents/${agentId}/reward-function`, config);
      return response;
    } catch (error) {
      console.error(`Erreur lors de la configuration de la fonction de récompense pour l'agent ${agentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Récupère les statistiques globales d'apprentissage par renforcement
   * @returns Statistiques globales
   */
  async getStats(): Promise<any> {
    try {
      const response = await apiClient.get('/recommendation/reinforcement-learning/stats');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }
}

export const reinforcementLearningService = new ReinforcementLearningService();
