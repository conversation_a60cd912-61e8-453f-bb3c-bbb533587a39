import { apiClient, ExtendedAxiosRequestConfig } from './apiClient';
import { API_ENDPOINTS } from './apiConfig';
import { AxiosProgressEvent } from 'axios';

export interface FileUploadResponse {
  id: string;
  name: string;
  path: string;
  mimeType: string;
  size: number;
  category: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Service for file upload operations
 */
export const fileService = {
  /**
   * Upload a single file
   * @param file File to upload
   * @param category File category
   * @param onUploadProgress Optional callback for upload progress.
   * @returns Uploaded file information
   */
  async uploadFile(
    file: File, 
    category: string = 'general', 
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
  ): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    
    const config: ExtendedAxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    };

    return apiClient.post<FileUploadResponse>(
      `${API_ENDPOINTS.FILES.UPLOAD}?category=${category}`,
      formData,
      config
    );
  },

  /**
   * Upload a single video file.
   * This is a convenience method that calls uploadFile with category 'video'.
   * @param videoFile The video file to upload.
   * @param onUploadProgress Optional callback for upload progress.
   * @returns Uploaded file information.
   */
  async uploadVideoFile(
    videoFile: File,
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
  ): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', videoFile);
    // formData.append('category', 'video'); // Alternatively, send category in FormData

    const config: ExtendedAxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    };

    return apiClient.post<FileUploadResponse>(
      `${API_ENDPOINTS.FILES.UPLOAD}?category=video`,
      formData,
      config
    );
  },

  /**
   * Upload multiple files
   * @param files Files to upload
   * @param category File category
   * @param onUploadProgress Optional callback for upload progress.
   * @returns Uploaded files information
   */
  async uploadFiles(
    files: File[], 
    category: string = 'general', 
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
  ): Promise<FileUploadResponse[]> {
    const formData = new FormData();
    
    files.forEach(file => {
      formData.append('files', file);
    });
    
    const config: ExtendedAxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    };

    return apiClient.post<FileUploadResponse[]>(
      `${API_ENDPOINTS.FILES.UPLOAD_MULTIPLE}?category=${category}`,
      formData,
      config
    );
  },

  /**
   * Get files by category
   * @param category File category
   * @returns Files information
   */
  async getFiles(category: string = 'general'): Promise<FileUploadResponse[]> {
    return apiClient.get<FileUploadResponse[]>(`${API_ENDPOINTS.FILES.BASE}?category=${category}`);
  },

  /**
   * Get file by ID
   * @param id File ID
   * @returns File information
   */
  async getFileById(id: string): Promise<FileUploadResponse> {
    return apiClient.get<FileUploadResponse>(`${API_ENDPOINTS.FILES.BASE}/${id}`);
  },

  /**
   * Delete file by ID
   * @param id File ID
   * @returns Deleted file information
   */
  async deleteFile(id: string): Promise<FileUploadResponse> {
    return apiClient.delete<FileUploadResponse>(`${API_ENDPOINTS.FILES.BASE}/${id}`);
  },
}
