/**
 * Service API pour la gestion des alertes
 * Communication avec le système d'alertes et notifications
 */

import { apiClient } from './apiClient';

export interface AlertItem {
  id: string;
  title: string;
  description: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  status: 'active' | 'acknowledged' | 'resolved';
  source: string;
  timestamp: Date;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  metadata?: Record<string, any>;
  actions?: AlertAction[];
}

export interface AlertAction {
  id: string;
  label: string;
  type: 'button' | 'link';
  action: string;
  variant?: 'default' | 'destructive' | 'outline';
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  metric: string;
  condition: 'greater_than' | 'less_than' | 'equals' | 'not_equals';
  threshold: number;
  duration: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  enabled: boolean;
  channels: string[];
}

class AlertsService {
  private readonly baseUrl = '/api/alerts';

  /**
   * Obtenir toutes les alertes
   */
  async getAlerts(options?: {
    status?: string;
    severity?: string;
    source?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ data: AlertItem[] }> {
    try {
      const params = new URLSearchParams();
      if (options?.status) params.append('status', options.status);
      if (options?.severity) params.append('severity', options.severity);
      if (options?.source) params.append('source', options.source);
      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.offset) params.append('offset', options.offset.toString());

      const response = await apiClient.get(`${this.baseUrl}?${params}`);
      
      return {
        data: response.data.map((alert: any) => ({
          ...alert,
          timestamp: new Date(alert.timestamp),
          acknowledgedAt: alert.acknowledgedAt ? new Date(alert.acknowledgedAt) : undefined,
          resolvedAt: alert.resolvedAt ? new Date(alert.resolvedAt) : undefined
        }))
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes:', error);
      
      // Données de simulation
      return {
        data: [
          {
            id: 'alert-1',
            title: 'CPU élevé sur Agent Performance',
            description: 'L\'utilisation CPU de l\'Agent Performance dépasse 85% depuis 5 minutes',
            severity: 'warning',
            status: 'active',
            source: 'agent-performance',
            timestamp: new Date(Date.now() - 300000), // 5 minutes ago
            metadata: {
              metric: 'cpu_usage',
              value: 87.3,
              threshold: 85
            },
            actions: [
              {
                id: 'restart',
                label: 'Redémarrer l\'agent',
                type: 'button',
                action: 'restart_agent',
                variant: 'destructive'
              }
            ]
          },
          {
            id: 'alert-2',
            title: 'Échec de test E2E',
            description: 'Le test E2E "Processus de commande" a échoué 3 fois consécutives',
            severity: 'error',
            status: 'acknowledged',
            source: 'agent-qa',
            timestamp: new Date(Date.now() - 900000), // 15 minutes ago
            acknowledgedBy: 'admin',
            acknowledgedAt: new Date(Date.now() - 600000), // 10 minutes ago
            metadata: {
              test: 'e2e-checkout-process',
              failures: 3,
              lastError: 'Element not found: #checkout-button'
            }
          },
          {
            id: 'alert-3',
            title: 'Vulnérabilité critique détectée',
            description: 'Une vulnérabilité critique (CVE-2024-1234) a été détectée dans les dépendances',
            severity: 'critical',
            status: 'active',
            source: 'agent-security',
            timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
            metadata: {
              cve: 'CVE-2024-1234',
              package: 'express',
              version: '4.17.1',
              severity: 'critical'
            },
            actions: [
              {
                id: 'update',
                label: 'Mettre à jour le package',
                type: 'button',
                action: 'update_package',
                variant: 'default'
              }
            ]
          },
          {
            id: 'alert-4',
            title: 'Temps de réponse élevé',
            description: 'Le temps de réponse moyen dépasse 2 secondes',
            severity: 'warning',
            status: 'resolved',
            source: 'agent-performance',
            timestamp: new Date(Date.now() - 3600000), // 1 hour ago
            resolvedAt: new Date(Date.now() - 1800000), // 30 minutes ago
            metadata: {
              metric: 'response_time',
              value: 2150,
              threshold: 2000
            }
          },
          {
            id: 'alert-5',
            title: 'Problème d\'accessibilité détecté',
            description: 'Score d\'accessibilité inférieur à 90% sur la page d\'accueil',
            severity: 'info',
            status: 'active',
            source: 'agent-uiux',
            timestamp: new Date(Date.now() - 7200000), // 2 hours ago
            metadata: {
              page: '/home',
              score: 87,
              threshold: 90,
              issues: ['missing alt text', 'low contrast']
            }
          }
        ]
      };
    }
  }

  /**
   * Obtenir les règles d'alerte
   */
  async getAlertRules(): Promise<{ data: AlertRule[] }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/rules`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des règles d\'alerte:', error);
      
      // Données de simulation
      return {
        data: [
          {
            id: 'rule-1',
            name: 'CPU élevé',
            description: 'Alerte quand l\'utilisation CPU dépasse 85%',
            metric: 'cpu_usage',
            condition: 'greater_than',
            threshold: 85,
            duration: 300, // 5 minutes
            severity: 'warning',
            enabled: true,
            channels: ['email', 'slack']
          },
          {
            id: 'rule-2',
            name: 'Mémoire critique',
            description: 'Alerte critique quand la mémoire dépasse 95%',
            metric: 'memory_usage',
            condition: 'greater_than',
            threshold: 95,
            duration: 60, // 1 minute
            severity: 'critical',
            enabled: true,
            channels: ['email', 'slack', 'sms']
          },
          {
            id: 'rule-3',
            name: 'Temps de réponse élevé',
            description: 'Alerte quand le temps de réponse dépasse 2 secondes',
            metric: 'response_time',
            condition: 'greater_than',
            threshold: 2000,
            duration: 180, // 3 minutes
            severity: 'warning',
            enabled: true,
            channels: ['email']
          },
          {
            id: 'rule-4',
            name: 'Taux d\'erreur élevé',
            description: 'Alerte quand le taux d\'erreur dépasse 5%',
            metric: 'error_rate',
            condition: 'greater_than',
            threshold: 5,
            duration: 120, // 2 minutes
            severity: 'error',
            enabled: true,
            channels: ['email', 'slack']
          },
          {
            id: 'rule-5',
            name: 'Agent hors ligne',
            description: 'Alerte quand un agent est hors ligne',
            metric: 'agent_status',
            condition: 'equals',
            threshold: 0, // 0 = offline
            duration: 30, // 30 secondes
            severity: 'critical',
            enabled: true,
            channels: ['email', 'slack', 'sms']
          }
        ]
      };
    }
  }

  /**
   * Mettre à jour une alerte (acquitter, résoudre)
   */
  async updateAlert(alertId: string, update: {
    action: 'acknowledge' | 'resolve';
    comment?: string;
  }): Promise<void> {
    try {
      await apiClient.patch(`${this.baseUrl}/${alertId}`, update);
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de l'alerte ${alertId}:`, error);
      throw error;
    }
  }

  /**
   * Créer une règle d'alerte
   */
  async createAlertRule(rule: Omit<AlertRule, 'id'>): Promise<{ data: AlertRule }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/rules`, rule);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la création de la règle d\'alerte:', error);
      throw error;
    }
  }

  /**
   * Mettre à jour une règle d'alerte
   */
  async updateAlertRule(ruleId: string, rule: Partial<AlertRule>): Promise<void> {
    try {
      await apiClient.patch(`${this.baseUrl}/rules/${ruleId}`, rule);
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de la règle ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Supprimer une règle d'alerte
   */
  async deleteAlertRule(ruleId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/rules/${ruleId}`);
    } catch (error) {
      console.error(`Erreur lors de la suppression de la règle ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Tester une règle d'alerte
   */
  async testAlertRule(rule: Omit<AlertRule, 'id'>): Promise<{ data: { triggered: boolean; message: string } }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/rules/test`, rule);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors du test de la règle d\'alerte:', error);
      throw error;
    }
  }

  /**
   * Obtenir l'historique des alertes
   */
  async getAlertHistory(options?: {
    startDate?: Date;
    endDate?: Date;
    severity?: string;
    source?: string;
    limit?: number;
  }): Promise<{ data: AlertItem[]; total: number }> {
    try {
      const params = new URLSearchParams();
      if (options?.startDate) params.append('startDate', options.startDate.toISOString());
      if (options?.endDate) params.append('endDate', options.endDate.toISOString());
      if (options?.severity) params.append('severity', options.severity);
      if (options?.source) params.append('source', options.source);
      if (options?.limit) params.append('limit', options.limit.toString());

      const response = await apiClient.get(`${this.baseUrl}/history?${params}`);
      
      return {
        data: response.data.alerts.map((alert: any) => ({
          ...alert,
          timestamp: new Date(alert.timestamp),
          acknowledgedAt: alert.acknowledgedAt ? new Date(alert.acknowledgedAt) : undefined,
          resolvedAt: alert.resolvedAt ? new Date(alert.resolvedAt) : undefined
        })),
        total: response.data.total
      };
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des alertes:', error);
      return { data: [], total: 0 };
    }
  }

  /**
   * Obtenir les statistiques des alertes
   */
  async getAlertStats(period: string = '24h'): Promise<{ data: any }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/stats?period=${period}`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques d\'alertes:', error);
      return {
        data: {
          total: 0,
          active: 0,
          acknowledged: 0,
          resolved: 0,
          bySource: {},
          bySeverity: {}
        }
      };
    }
  }

  /**
   * Configurer les canaux de notification
   */
  async configureNotificationChannels(config: {
    email?: {
      enabled: boolean;
      recipients: string[];
      template?: string;
    };
    slack?: {
      enabled: boolean;
      webhook: string;
      channel: string;
    };
    sms?: {
      enabled: boolean;
      numbers: string[];
    };
    webhook?: {
      enabled: boolean;
      url: string;
      headers?: Record<string, string>;
    };
  }): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/channels`, config);
    } catch (error) {
      console.error('Erreur lors de la configuration des canaux de notification:', error);
      throw error;
    }
  }

  /**
   * Tester un canal de notification
   */
  async testNotificationChannel(channel: string, message?: string): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/channels/${channel}/test`, {
        message: message || 'Test de notification depuis le dashboard des agents'
      });
    } catch (error) {
      console.error(`Erreur lors du test du canal ${channel}:`, error);
      throw error;
    }
  }

  /**
   * Exécuter une action d'alerte
   */
  async executeAlertAction(alertId: string, actionId: string, parameters?: any): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/${alertId}/actions/${actionId}`, parameters);
    } catch (error) {
      console.error(`Erreur lors de l'exécution de l'action ${actionId} pour l'alerte ${alertId}:`, error);
      throw error;
    }
  }
}

export const alertsService = new AlertsService();
