/**
 * Service API pour la gestion des rapports
 * Communication avec le système de génération de rapports
 */

import { apiClient } from './apiClient';

export interface Report {
  id: string;
  name: string;
  description: string;
  type: 'performance' | 'security' | 'quality' | 'usage' | 'custom';
  status: 'generating' | 'completed' | 'failed' | 'scheduled';
  progress: number;
  createdAt: Date;
  completedAt?: Date;
  size: number;
  format: 'pdf' | 'excel' | 'csv' | 'json';
  downloadUrl?: string;
  parameters: ReportParameters;
  creator: string;
}

export interface ReportParameters {
  dateRange: {
    start: Date;
    end: Date;
  };
  agents?: string[];
  metrics?: string[];
  includeCharts: boolean;
  includeRawData: boolean;
  groupBy?: 'hour' | 'day' | 'week' | 'month';
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  defaultParameters: Partial<ReportParameters>;
  estimatedDuration: number;
}

export interface CreateReportRequest {
  name: string;
  description: string;
  type: 'performance' | 'security' | 'quality' | 'usage' | 'custom';
  format: 'pdf' | 'excel' | 'csv' | 'json';
  template?: string;
  parameters: ReportParameters;
}

class ReportsService {
  private readonly baseUrl = '/api/reports';

  /**
   * Obtenir tous les rapports
   */
  async getReports(options?: {
    status?: string;
    type?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ data: Report[] }> {
    try {
      const params = new URLSearchParams();
      if (options?.status) params.append('status', options.status);
      if (options?.type) params.append('type', options.type);
      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.offset) params.append('offset', options.offset.toString());

      const response = await apiClient.get(`${this.baseUrl}?${params}`);
      
      return {
        data: response.data.map((report: any) => ({
          ...report,
          createdAt: new Date(report.createdAt),
          completedAt: report.completedAt ? new Date(report.completedAt) : undefined,
          parameters: {
            ...report.parameters,
            dateRange: {
              start: new Date(report.parameters.dateRange.start),
              end: new Date(report.parameters.dateRange.end)
            }
          }
        }))
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des rapports:', error);
      
      // Données de simulation
      return {
        data: [
          {
            id: 'report-1',
            name: 'Rapport Performance Hebdomadaire',
            description: 'Analyse des performances de la semaine dernière',
            type: 'performance',
            status: 'completed',
            progress: 100,
            createdAt: new Date(Date.now() - 86400000), // 1 day ago
            completedAt: new Date(Date.now() - 82800000), // 23 hours ago
            size: 2048576, // 2MB
            format: 'pdf',
            downloadUrl: '/downloads/report-1.pdf',
            parameters: {
              dateRange: {
                start: new Date(Date.now() - 7 * 86400000), // 7 days ago
                end: new Date(Date.now() - 86400000) // 1 day ago
              },
              agents: ['agent-performance', 'agent-qa'],
              metrics: ['response_time', 'cpu_usage', 'memory_usage'],
              includeCharts: true,
              includeRawData: false,
              groupBy: 'day'
            },
            creator: 'admin'
          },
          {
            id: 'report-2',
            name: 'Audit Sécurité Mensuel',
            description: 'Rapport de sécurité complet du mois',
            type: 'security',
            status: 'generating',
            progress: 65,
            createdAt: new Date(Date.now() - 3600000), // 1 hour ago
            size: 0,
            format: 'excel',
            parameters: {
              dateRange: {
                start: new Date(Date.now() - 30 * 86400000), // 30 days ago
                end: new Date()
              },
              agents: ['agent-security'],
              includeCharts: true,
              includeRawData: true,
              groupBy: 'week'
            },
            creator: 'security-admin'
          },
          {
            id: 'report-3',
            name: 'Rapport Qualité Sprint',
            description: 'Métriques de qualité du sprint en cours',
            type: 'quality',
            status: 'failed',
            progress: 0,
            createdAt: new Date(Date.now() - 7200000), // 2 hours ago
            size: 0,
            format: 'csv',
            parameters: {
              dateRange: {
                start: new Date(Date.now() - 14 * 86400000), // 14 days ago
                end: new Date()
              },
              agents: ['agent-qa', 'agent-frontend'],
              metrics: ['test_coverage', 'bug_count', 'code_quality'],
              includeCharts: false,
              includeRawData: true,
              groupBy: 'day'
            },
            creator: 'qa-lead'
          },
          {
            id: 'report-4',
            name: 'Utilisation des Ressources',
            description: 'Rapport d\'utilisation des ressources système',
            type: 'usage',
            status: 'scheduled',
            progress: 0,
            createdAt: new Date(Date.now() + 3600000), // 1 hour from now
            size: 0,
            format: 'json',
            parameters: {
              dateRange: {
                start: new Date(Date.now() - 7 * 86400000), // 7 days ago
                end: new Date()
              },
              metrics: ['cpu_usage', 'memory_usage', 'disk_usage', 'network_io'],
              includeCharts: true,
              includeRawData: true,
              groupBy: 'hour'
            },
            creator: 'devops'
          }
        ]
      };
    }
  }

  /**
   * Obtenir les templates de rapport
   */
  async getTemplates(): Promise<{ data: ReportTemplate[] }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/templates`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des templates:', error);
      
      // Données de simulation
      return {
        data: [
          {
            id: 'template-performance',
            name: 'Rapport Performance Standard',
            description: 'Template pour les rapports de performance hebdomadaires',
            type: 'performance',
            estimatedDuration: 300, // 5 minutes
            defaultParameters: {
              metrics: ['response_time', 'cpu_usage', 'memory_usage', 'requests_per_second'],
              includeCharts: true,
              includeRawData: false,
              groupBy: 'day'
            }
          },
          {
            id: 'template-security',
            name: 'Audit Sécurité Complet',
            description: 'Template pour les audits de sécurité mensuels',
            type: 'security',
            estimatedDuration: 900, // 15 minutes
            defaultParameters: {
              agents: ['agent-security'],
              includeCharts: true,
              includeRawData: true,
              groupBy: 'week'
            }
          },
          {
            id: 'template-quality',
            name: 'Métriques Qualité',
            description: 'Template pour les rapports de qualité de code',
            type: 'quality',
            estimatedDuration: 180, // 3 minutes
            defaultParameters: {
              agents: ['agent-qa'],
              metrics: ['test_coverage', 'code_quality', 'bug_density'],
              includeCharts: true,
              includeRawData: false,
              groupBy: 'day'
            }
          },
          {
            id: 'template-usage',
            name: 'Utilisation Ressources',
            description: 'Template pour les rapports d\'utilisation des ressources',
            type: 'usage',
            estimatedDuration: 120, // 2 minutes
            defaultParameters: {
              metrics: ['cpu_usage', 'memory_usage', 'disk_usage', 'network_io'],
              includeCharts: true,
              includeRawData: true,
              groupBy: 'hour'
            }
          },
          {
            id: 'template-custom',
            name: 'Rapport Personnalisé',
            description: 'Template vide pour créer des rapports personnalisés',
            type: 'custom',
            estimatedDuration: 600, // 10 minutes
            defaultParameters: {
              includeCharts: true,
              includeRawData: false,
              groupBy: 'day'
            }
          }
        ]
      };
    }
  }

  /**
   * Créer un nouveau rapport
   */
  async createReport(request: CreateReportRequest): Promise<{ data: Report }> {
    try {
      const response = await apiClient.post(this.baseUrl, {
        ...request,
        parameters: {
          ...request.parameters,
          dateRange: {
            start: request.parameters.dateRange.start.toISOString(),
            end: request.parameters.dateRange.end.toISOString()
          }
        }
      });
      
      return {
        data: {
          ...response.data,
          createdAt: new Date(response.data.createdAt),
          completedAt: response.data.completedAt ? new Date(response.data.completedAt) : undefined,
          parameters: {
            ...response.data.parameters,
            dateRange: {
              start: new Date(response.data.parameters.dateRange.start),
              end: new Date(response.data.parameters.dateRange.end)
            }
          }
        }
      };
    } catch (error) {
      console.error('Erreur lors de la création du rapport:', error);
      throw error;
    }
  }

  /**
   * Télécharger un rapport
   */
  async downloadReport(reportId: string): Promise<{ data: Blob }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${reportId}/download`, {
        responseType: 'blob'
      });
      
      return { data: response.data };
    } catch (error) {
      console.error(`Erreur lors du téléchargement du rapport ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Supprimer un rapport
   */
  async deleteReport(reportId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/${reportId}`);
    } catch (error) {
      console.error(`Erreur lors de la suppression du rapport ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir les détails d'un rapport
   */
  async getReportDetails(reportId: string): Promise<{ data: Report }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${reportId}`);
      
      return {
        data: {
          ...response.data,
          createdAt: new Date(response.data.createdAt),
          completedAt: response.data.completedAt ? new Date(response.data.completedAt) : undefined,
          parameters: {
            ...response.data.parameters,
            dateRange: {
              start: new Date(response.data.parameters.dateRange.start),
              end: new Date(response.data.parameters.dateRange.end)
            }
          }
        }
      };
    } catch (error) {
      console.error(`Erreur lors de la récupération du rapport ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Programmer un rapport récurrent
   */
  async scheduleReport(request: CreateReportRequest & {
    schedule: {
      type: 'daily' | 'weekly' | 'monthly';
      time: string; // HH:MM format
      dayOfWeek?: number; // 0-6 for weekly
      dayOfMonth?: number; // 1-31 for monthly
    };
  }): Promise<{ data: any }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/schedule`, request);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la programmation du rapport:', error);
      throw error;
    }
  }

  /**
   * Obtenir l'historique des rapports
   */
  async getReportHistory(options?: {
    type?: string;
    creator?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): Promise<{ data: Report[]; total: number }> {
    try {
      const params = new URLSearchParams();
      if (options?.type) params.append('type', options.type);
      if (options?.creator) params.append('creator', options.creator);
      if (options?.startDate) params.append('startDate', options.startDate.toISOString());
      if (options?.endDate) params.append('endDate', options.endDate.toISOString());
      if (options?.limit) params.append('limit', options.limit.toString());

      const response = await apiClient.get(`${this.baseUrl}/history?${params}`);
      
      return {
        data: response.data.reports.map((report: any) => ({
          ...report,
          createdAt: new Date(report.createdAt),
          completedAt: report.completedAt ? new Date(report.completedAt) : undefined
        })),
        total: response.data.total
      };
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      return { data: [], total: 0 };
    }
  }

  /**
   * Obtenir les statistiques des rapports
   */
  async getReportStats(period: string = '30d'): Promise<{ data: any }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/stats?period=${period}`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      return {
        data: {
          total: 0,
          completed: 0,
          failed: 0,
          generating: 0,
          totalSize: 0,
          averageGenerationTime: 0,
          byType: {},
          byFormat: {}
        }
      };
    }
  }

  /**
   * Dupliquer un rapport
   */
  async duplicateReport(reportId: string, newName?: string): Promise<{ data: Report }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${reportId}/duplicate`, {
        name: newName
      });
      
      return {
        data: {
          ...response.data,
          createdAt: new Date(response.data.createdAt),
          parameters: {
            ...response.data.parameters,
            dateRange: {
              start: new Date(response.data.parameters.dateRange.start),
              end: new Date(response.data.parameters.dateRange.end)
            }
          }
        }
      };
    } catch (error) {
      console.error(`Erreur lors de la duplication du rapport ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Exporter la configuration d'un rapport
   */
  async exportReportConfig(reportId: string): Promise<{ data: any }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${reportId}/config`);
      return { data: response.data };
    } catch (error) {
      console.error(`Erreur lors de l'export de la configuration du rapport ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Importer une configuration de rapport
   */
  async importReportConfig(config: any): Promise<{ data: Report }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/import`, config);
      
      return {
        data: {
          ...response.data,
          createdAt: new Date(response.data.createdAt),
          parameters: {
            ...response.data.parameters,
            dateRange: {
              start: new Date(response.data.parameters.dateRange.start),
              end: new Date(response.data.parameters.dateRange.end)
            }
          }
        }
      };
    } catch (error) {
      console.error('Erreur lors de l\'import de la configuration:', error);
      throw error;
    }
  }
}

export const reportsService = new ReportsService();
