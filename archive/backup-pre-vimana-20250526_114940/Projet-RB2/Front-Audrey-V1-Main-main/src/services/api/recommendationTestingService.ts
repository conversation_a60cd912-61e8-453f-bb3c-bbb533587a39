import { apiClient } from './apiClient';

export interface TestConfig {
  id: string;
  name: string;
  description: string;
  strategies: string[];
  userSegments: string[];
  metrics: string[];
  sampleSize: number;
  testDuration: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
}

export interface TestResult {
  testId: string;
  strategy: string;
  userSegment: string;
  impressions: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
  averageRelevanceScore: number;
  averageSatisfactionScore: number;
  diversityScore: number;
  noveltyScore: number;
  coverageScore: number;
  createdAt: string;
}

export interface TestRun {
  id: string;
  testId: string;
  startedAt: string;
  completedAt?: string;
  status: 'RUNNING' | 'COMPLETED' | 'FAILED';
  error?: string;
}

export interface CreateTestRequest {
  name: string;
  description: string;
  strategies: string[];
  userSegments: string[];
  metrics: string[];
  sampleSize: number;
  testDuration: number;
}

export interface UpdateTestRequest {
  name?: string;
  description?: string;
  strategies?: string[];
  userSegments?: string[];
  metrics?: string[];
  sampleSize?: number;
  testDuration?: number;
  isActive?: boolean;
}

class RecommendationTestingService {
  /**
   * Récupère tous les tests de recommandation
   */
  async getAllTests(): Promise<TestConfig[]> {
    try {
      const response = await apiClient.get('/recommendation/testing');
      return response.data;
    } catch (error) {
      console.error('Error fetching all tests:', error);
      throw error;
    }
  }

  /**
   * Récupère un test par son ID
   */
  async getTestById(id: string): Promise<TestConfig> {
    try {
      const response = await apiClient.get(`/recommendation/testing/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Crée un nouveau test
   */
  async createTest(request: CreateTestRequest): Promise<TestConfig> {
    try {
      const response = await apiClient.post('/recommendation/testing', request);
      return response.data;
    } catch (error) {
      console.error('Error creating test:', error);
      throw error;
    }
  }

  /**
   * Met à jour un test
   */
  async updateTest(id: string, request: UpdateTestRequest): Promise<TestConfig> {
    try {
      const response = await apiClient.put(`/recommendation/testing/${id}`, request);
      return response.data;
    } catch (error) {
      console.error(`Error updating test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Supprime un test
   */
  async deleteTest(id: string): Promise<{ success: boolean }> {
    try {
      const response = await apiClient.delete(`/recommendation/testing/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Exécute un test
   */
  async runTest(id: string): Promise<TestRun> {
    try {
      const response = await apiClient.post(`/recommendation/testing/${id}/run`);
      return response.data;
    } catch (error) {
      console.error(`Error running test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère le statut d'un test en cours d'exécution
   */
  async getTestRunStatus(id: string): Promise<{ status: string }> {
    try {
      const response = await apiClient.get(`/recommendation/testing/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error getting status for test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les résultats d'un test
   */
  async getTestResults(id: string): Promise<TestResult[]> {
    try {
      const response = await apiClient.get(`/recommendation/testing/${id}/results`);
      return response.data;
    } catch (error) {
      console.error(`Error getting results for test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les exécutions d'un test
   */
  async getTestRuns(id: string): Promise<TestRun[]> {
    try {
      const response = await apiClient.get(`/recommendation/testing/${id}/runs`);
      return response.data;
    } catch (error) {
      console.error(`Error getting runs for test with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les stratégies de recommandation disponibles
   */
  async getAvailableStrategies(): Promise<string[]> {
    try {
      const response = await apiClient.get('/recommendation/testing/strategies');
      return response.data;
    } catch (error) {
      console.error('Error getting available strategies:', error);
      throw error;
    }
  }

  /**
   * Récupère les segments d'utilisateurs disponibles
   */
  async getAvailableSegments(): Promise<string[]> {
    try {
      const response = await apiClient.get('/recommendation/testing/segments');
      return response.data;
    } catch (error) {
      console.error('Error getting available segments:', error);
      throw error;
    }
  }

  /**
   * Récupère les métriques disponibles
   */
  async getAvailableMetrics(): Promise<string[]> {
    try {
      const response = await apiClient.get('/recommendation/testing/metrics');
      return response.data;
    } catch (error) {
      console.error('Error getting available metrics:', error);
      throw error;
    }
  }
}

export const recommendationTestingService = new RecommendationTestingService();
