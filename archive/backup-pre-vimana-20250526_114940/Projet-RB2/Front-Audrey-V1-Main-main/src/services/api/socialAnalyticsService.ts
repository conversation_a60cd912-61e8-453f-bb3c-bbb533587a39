import { apiClient as api } from './apiClient';

// Define basic interfaces for Analytics Data.
// These are placeholders and should be refined based on actual API responses.
export interface OverallSocialAnalytics {
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  totalShares: number;
  activeUsers: number;
  // ... other relevant global stats
}

export interface ContentSpecificAnalytics {
  views: number;
  likes: number;
  comments: number;
  shares: number;
  averageViewDuration?: number; // In seconds
  // ... other content-specific stats
}

export interface UserSocialAnalytics {
  contentCreated: number;
  followers: number;
  following: number;
  engagementRate: number; // Percentage
  // ... other user-specific stats
}

export interface PopularContent {
  id: string;
  title: string;
  type: 'livestream' | 'blog' | 'video';
  score: number; // Or views, likes, etc.
  // ... other details
}

export interface EngagementTrendDataPoint {
  date: string; // Or Date
  value: number;
}

export interface EngagementTrends {
  likes: EngagementTrendDataPoint[];
  comments: EngagementTrendDataPoint[];
  shares: EngagementTrendDataPoint[];
  // ... other engagement metrics
}

export interface ContentAnalyticsData {
  viewsByDay: {
    date: string;
    views: number;
  }[];
  engagementByType: {
    type: string;
    count: number;
  }[];
  contentByStatus: {
    status: string;
    count: number;
  }[];
  topContent: {
    id: string;
    title: string;
    views: number;
    engagement: number;
  }[];
  totalStats: {
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    totalContent: number;
  };
}

export interface TrackEventResponse {
  success: boolean;
  eventId?: string;
}

interface TrackEventParams {
  eventType: string;
  entityId: string;
  entityType: 'livestream' | 'blog' | 'video' | 'user';
  metadata?: Record<string, any>;
}

class SocialAnalyticsService {
  /**
   * Récupère les statistiques sociales globales
   * @param period Période (day, week, month, year)
   * @returns Statistiques sociales
   */
  async getSocialAnalytics(period: string = 'month'): Promise<OverallSocialAnalytics | null> {
    try {
      const response = await api.get<OverallSocialAnalytics>('/social/analytics', { params: { period } });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching social analytics:', error);
      // Depending on requirements, you might return a default object or null
      return null;
    }
  }

  /**
   * Récupère les statistiques d'un livestream spécifique
   * @param livestreamId ID du livestream
   * @returns Statistiques du livestream
   */
  async getLivestreamAnalytics(livestreamId: string): Promise<ContentSpecificAnalytics | null> {
    try {
      const response = await api.get<ContentSpecificAnalytics>(`/social/analytics/livestreams/${livestreamId}`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching livestream analytics:', error);
      return null;
    }
  }

  /**
   * Récupère les statistiques d'un article de blog spécifique
   * @param blogPostId ID de l'article de blog
   * @returns Statistiques de l'article de blog
   */
  async getBlogPostAnalytics(blogPostId: string): Promise<ContentSpecificAnalytics | null> {
    try {
      const response = await api.get<ContentSpecificAnalytics>(`/social/analytics/blog/${blogPostId}`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching blog post analytics:', error);
      return null;
    }
  }

  /**
   * Récupère les statistiques d'un utilisateur spécifique
   * @param userId ID de l'utilisateur
   * @returns Statistiques de l'utilisateur
   */
  async getUserAnalytics(userId: string): Promise<UserSocialAnalytics | null> {
    try {
      const response = await api.get<UserSocialAnalytics>(`/social/analytics/users/${userId}`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      return null;
    }
  }

  /**
   * Récupère les statistiques de contenu populaire
   * @param type Type de contenu (livestream, blog, video)
   * @param limit Nombre de résultats à retourner
   * @param period Période (day, week, month, year)
   * @returns Statistiques de contenu populaire
   */
  async getPopularContent(type?: 'livestream' | 'blog' | 'video', limit: number = 10, period: string = 'month'): Promise<PopularContent[]> {
    try {
      const response = await api.get<PopularContent[]>('/social/analytics/popular', {
        params: { type, limit, period },
      });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching popular content:', error);
      return [];
    }
  }

  /**
   * Enregistre un événement d'analyse
   * @param eventData Données de l'événement
   * @returns Résultat de l'opération
   */
  async trackEvent(eventData: TrackEventParams): Promise<TrackEventResponse | null> {
    try {
      const response = await api.post<TrackEventResponse>('/social/analytics/events', eventData);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error tracking event:', error);
      // Ne pas propager l'erreur pour éviter d'interrompre l'expérience utilisateur
      return null;
    }
  }

  /**
   * Récupère les tendances d'engagement
   * @param period Période (day, week, month, year)
   * @returns Tendances d'engagement
   */
  async getEngagementTrends(period: string = 'month'): Promise<EngagementTrends | null> {
    try {
      const response = await api.get<EngagementTrends>('/social/analytics/engagement', {
        params: { period },
      });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching engagement trends:', error);
      return null;
    }
  }

  /**
   * Enregistre un événement de vue
   * @param entityId ID de l'entité
   * @param entityType Type de l'entité
   * @returns Résultat de l'opération
   */
  async trackView(entityId: string, entityType: 'livestream' | 'blog' | 'video'): Promise<void> {
    try {
      await this.trackEvent({
        eventType: 'view',
        entityId,
        entityType,
        metadata: {
          timestamp: new Date().toISOString(),
          referrer: document.referrer,
        },
      });
    } catch (error) {
      console.error('Error tracking view:', error);
    }
  }

  /**
   * Enregistre un événement d'engagement
   * @param entityId ID de l'entité
   * @param entityType Type de l'entité
   * @param engagementType Type d'engagement (like, comment, share)
   * @returns Résultat de l'opération
   */
  async trackEngagement(
    entityId: string,
    entityType: 'livestream' | 'blog' | 'video',
    engagementType: 'like' | 'comment' | 'share'
  ): Promise<void> {
    try {
      await this.trackEvent({
        eventType: `engagement_${engagementType}`,
        entityId,
        entityType,
        metadata: {
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error('Error tracking engagement:', error);
    }
  }

  /**
   * Récupère les analyses de contenu pour un utilisateur
   * @param params Paramètres de la requête
   * @returns Données d'analyse de contenu
   */
  async getContentAnalytics(params: {
    userId?: string;
    timeRange?: 'week' | 'month' | 'year';
  }): Promise<ContentAnalyticsData> {
    try {
      // Dans une implémentation réelle, nous ferions un appel API
      // const response = await api.get<ContentAnalyticsData>('/social/analytics/content', { params });
      // return response;

      // Pour le moment, nous simulons les données
      await new Promise(resolve => setTimeout(resolve, 800));

      // Générer des données simulées basées sur la plage de temps
      const { timeRange = 'month' } = params;

      // Nombre de points de données basé sur la plage de temps
      const dataPoints = timeRange === 'week' ? 7 : timeRange === 'month' ? 30 : 12;

      // Générer des données de vues par jour/mois
      const viewsByDay = this.generateTimeSeriesData(dataPoints, timeRange);

      // Générer des données d'engagement par type
      const engagementByType = [
        { type: 'J\'aime', count: Math.floor(Math.random() * 1000) + 500 },
        { type: 'Commentaires', count: Math.floor(Math.random() * 500) + 100 },
        { type: 'Partages', count: Math.floor(Math.random() * 300) + 50 },
        { type: 'Enregistrements', count: Math.floor(Math.random() * 200) + 30 },
      ];

      // Générer des données de contenu par statut
      const contentByStatus = [
        { status: 'Publié', count: Math.floor(Math.random() * 50) + 20 },
        { status: 'Archivé', count: Math.floor(Math.random() * 20) + 5 },
        { status: 'Supprimé', count: Math.floor(Math.random() * 10) + 2 },
      ];

      // Générer des données de contenu le plus performant
      const topContent = Array.from({ length: 5 }, (_, i) => ({
        id: `content-${i + 1}`,
        title: `Contenu populaire ${i + 1}`,
        views: Math.floor(Math.random() * 5000) + 1000,
        engagement: Math.floor(Math.random() * 1000) + 100,
      }));

      // Calculer les statistiques totales
      const totalViews = viewsByDay.reduce((sum, day) => sum + day.views, 0);
      const totalLikes = engagementByType.find(item => item.type === 'J\'aime')?.count || 0;
      const totalComments = engagementByType.find(item => item.type === 'Commentaires')?.count || 0;
      const totalShares = engagementByType.find(item => item.type === 'Partages')?.count || 0;
      const totalContent = contentByStatus.reduce((sum, status) => sum + status.count, 0);

      return {
        viewsByDay,
        engagementByType,
        contentByStatus,
        topContent,
        totalStats: {
          totalViews,
          totalLikes,
          totalComments,
          totalShares,
          totalContent,
        },
      };
    } catch (error) {
      console.error('Error fetching content analytics:', error);
      // Retourner des données vides en cas d'erreur
      return {
        viewsByDay: [],
        engagementByType: [],
        contentByStatus: [],
        topContent: [],
        totalStats: {
          totalViews: 0,
          totalLikes: 0,
          totalComments: 0,
          totalShares: 0,
          totalContent: 0,
        },
      };
    }
  }

  /**
   * Méthode auxiliaire pour générer des données de séries temporelles
   * @param count Nombre de points de données
   * @param timeRange Plage de temps
   * @returns Données de séries temporelles
   */
  private generateTimeSeriesData(count: number, timeRange: 'week' | 'month' | 'year'): { date: string; views: number }[] {
    const result = [];
    const today = new Date();

    for (let i = count - 1; i >= 0; i--) {
      const date = new Date();

      if (timeRange === 'week' || timeRange === 'month') {
        // Pour la semaine et le mois, reculer par jours
        date.setDate(today.getDate() - i);

        const formattedDate = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}`;

        result.push({
          date: formattedDate,
          views: Math.floor(Math.random() * 500) + 50,
        });
      } else {
        // Pour l'année, reculer par mois
        date.setMonth(today.getMonth() - i);

        const monthNames = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];
        const formattedDate = monthNames[date.getMonth()];

        result.push({
          date: formattedDate,
          views: Math.floor(Math.random() * 10000) + 1000,
        });
      }
    }

    return result;
  }
}

export const socialAnalyticsService = new SocialAnalyticsService();
