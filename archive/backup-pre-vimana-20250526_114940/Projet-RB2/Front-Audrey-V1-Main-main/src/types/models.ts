/**
 * TypeScript interfaces generated from Prisma schema
 * These interfaces ensure type safety between frontend and backend
 */

// Enums
export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
}

export enum UserRole {
  USER = 'USER',
  ADMIN = 'ADMIN',
  MODERATOR = 'MODERATOR',
  PARTNER = 'PARTNER',
  HOST = 'HOST',
  ORGANIZER = 'ORGANIZER',
  PARTICIPANT = 'PARTICIPANT',
}

export enum Status {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
}

export enum MoodType {
  HAPPY = 'HAPPY',
  SAD = 'SAD',
  NEUTRAL = 'NEUTRAL',
  EXCITED = 'EXCITED',
  ANXIOUS = 'ANXIOUS',
  CALM = 'CALM',
}

export enum PrivacyLevel {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  FRIENDS = 'FRIENDS',
}

export enum ProjectStatus {
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

// User Interface
export interface User {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  emailVerified?: Date;
  image?: string;
  role: UserRole;
  isAdmin: boolean;
  provider?: string;
  address?: string;
  phone?: string;
  bio?: string;
  birthDate?: Date;
  gender?: string;
  language?: string;
  timezone?: string;
  currency?: string;
  isVerified: boolean;
  isActive: boolean;
  lastLoginAt?: Date;
  status: UserStatus;
  twoFactorEnabled: boolean;
}

// Profile Interface
export interface Profile {
  id: number;
  email: string;
  name?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Retreat Interface
export interface Retreat {
  id: string;
  title: string;
  description: string;
  location: string;
  startDate: Date;
  endDate: Date;
  price: number;
  capacity: number;
  status: Status;
  createdAt: Date;
  updatedAt: Date;
  hostId: string;
  host?: User;
  categories?: string[];
  amenities?: string[];
  images?: string[];
}

// Order Interface
export interface Order {
  id: string;
  userId: string;
  productId: string;
  quantity: number;
  totalPrice: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  user?: User;
  metadata?: Record<string, any>;
  shippingInfo?: Record<string, any>;
  billingInfo?: Record<string, any>;
  trackingInfo?: Record<string, any>;
}

// Course Interface
export interface Course {
  id: string;
  title: string;
  description: string;
  level?: string;
  coverImage?: string;
  category?: string;
  createdAt: Date;
  updatedAt: Date;
  lessons?: Lesson[];
}

// Lesson Interface
export interface Lesson {
  id: string;
  title: string;
  content?: string;
  videoUrl?: string;
  order: number;
  courseId: string;
  course?: Course;
  createdAt: Date;
  updatedAt: Date;
}

// Journal Interface
export interface Journal {
  id: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  entries?: JournalEntry[];
}

// JournalEntry Interface
export interface JournalEntry {
  id: string;
  journalId: string;
  title?: string;
  content: string;
  mood: MoodType;
  tags: string[];
  visibility: PrivacyLevel;
  createdAt: Date;
  updatedAt: Date;
}

// Add more interfaces as needed for your application
