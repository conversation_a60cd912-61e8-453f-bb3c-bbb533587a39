import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';
import { explanationLearningService } from '../../services/api/explanationLearningService';
import { t } from '../../services/i18n/i18nService';

/**
 * Interface pour les résultats d'un test A/B
 */
interface ABTestResult {
  testId: string;
  testName: string;
  startDate: string;
  endDate: string;
  status: string;
  totalParticipants: number;
  variants: Array<{
    variantId: string;
    variantName: string;
    participants: number;
    interactionRate: number;
    conversionRate: number;
    averageRating: number;
    improvement: number;
    confidenceLevel: number;
    isWinner: boolean;
  }>;
  metrics: {
    interactionRateImprovement: number;
    conversionRateImprovement: number;
    ratingImprovement: number;
  };
  recommendations: string[];
}

/**
 * Page de résultats détaillés d'un test A/B
 */
const ABTestResultsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<ABTestResult | null>(null);
  const [applyingOptimization, setApplyingOptimization] = useState<boolean>(false);
  
  // Vérifier si l'utilisateur est administrateur
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);
  
  // Charger les résultats du test
  useEffect(() => {
    const loadTestResults = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        setError(null);
        
        // Dans une implémentation réelle, nous récupérerions les résultats depuis l'API
        // Pour cet exemple, nous utilisons des données fictives
        const mockResults: ABTestResult = {
          testId: id,
          testName: 'Test de niveau de détail des explications',
          startDate: '2023-06-01T00:00:00.000Z',
          endDate: '2023-06-30T00:00:00.000Z',
          status: 'COMPLETED',
          totalParticipants: 1000,
          variants: [
            {
              variantId: '123e4567-e89b-12d3-a456-426614174001',
              variantName: 'Variante standard',
              participants: 500,
              interactionRate: 0.42,
              conversionRate: 0.18,
              averageRating: 3.5,
              improvement: 0,
              confidenceLevel: 0,
              isWinner: false,
            },
            {
              variantId: '123e4567-e89b-12d3-a456-426614174002',
              variantName: 'Variante détaillée',
              participants: 500,
              interactionRate: 0.55,
              conversionRate: 0.23,
              averageRating: 4.2,
              improvement: 0.15,
              confidenceLevel: 0.95,
              isWinner: true,
            },
          ],
          metrics: {
            interactionRateImprovement: 0.13,
            conversionRateImprovement: 0.05,
            ratingImprovement: 0.7,
          },
          recommendations: [
            'Utiliser des explications plus détaillées pour les recommandations importantes',
            'Mettre en évidence les facteurs les plus pertinents pour l\'utilisateur',
            'Inclure des éléments visuels pour renforcer la compréhension',
          ],
        };
        
        setTestResults(mockResults);
      } catch (err: any) {
        console.error('Erreur lors du chargement des résultats du test:', err);
        setError(err.message || 'Erreur lors du chargement des résultats du test');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && user.role === 'ADMIN') {
      loadTestResults();
    }
  }, [id, user]);
  
  // Appliquer les optimisations du test
  const handleApplyOptimizations = async () => {
    if (!testResults) return;
    
    try {
      setApplyingOptimization(true);
      
      // Dans une implémentation réelle, nous appellerions l'API pour appliquer les optimisations
      await explanationLearningService.triggerOptimization();
      
      toast.success('Optimisations appliquées avec succès');
    } catch (err: any) {
      console.error('Erreur lors de l\'application des optimisations:', err);
      toast.error(err.message || 'Erreur lors de l\'application des optimisations');
    } finally {
      setApplyingOptimization(false);
    }
  };
  
  // Formater une date
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };
  
  // Formater un pourcentage
  const formatPercent = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Helmet>
          <title>Résultats du Test A/B | Retreat And Be</title>
          <meta
            name="description"
            content="Résultats détaillés d'un test A/B pour les administrateurs de Retreat And Be."
          />
        </Helmet>
        
        <AdminNavBar />
        
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
            </div>
          </div>
        </main>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Résultats du Test A/B | Retreat And Be</title>
        <meta
          name="description"
          content="Résultats détaillés d'un test A/B pour les administrateurs de Retreat And Be."
        />
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          {/* En-tête */}
          <div className="mb-6">
            <div className="flex items-center mb-2">
              <button
                onClick={() => navigate('/admin/ab-testing')}
                className="mr-2 text-retreat-green hover:text-retreat-green-dark"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
              </button>
              <h1 className="text-2xl font-bold text-gray-900">
                {testResults?.testName || 'Résultats du test'}
              </h1>
            </div>
            <div className="flex items-center text-gray-500">
              <span className="mr-4">ID: {testResults?.testId}</span>
              <span className="mr-4">Période: {testResults ? `${formatDate(testResults.startDate)} - ${formatDate(testResults.endDate)}` : ''}</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                testResults?.status === 'COMPLETED' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
              }`}>
                {testResults?.status === 'COMPLETED' ? 'Terminé' : 'En cours'}
              </span>
            </div>
          </div>
          
          {/* Afficher un message d'erreur */}
          {error && (
            <div className="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p>{error}</p>
              </div>
            </div>
          )}
          
          {testResults && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Résumé du test */}
              <div className="md:col-span-3 bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Résumé du test</h2>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-retreat-green">{testResults.totalParticipants}</div>
                      <div className="text-sm text-gray-500">Participants</div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-retreat-green">{formatPercent(testResults.metrics.interactionRateImprovement)}</div>
                      <div className="text-sm text-gray-500">Amélioration des interactions</div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-retreat-green">{formatPercent(testResults.metrics.conversionRateImprovement)}</div>
                      <div className="text-sm text-gray-500">Amélioration des conversions</div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-retreat-green">{testResults.metrics.ratingImprovement.toFixed(1)}</div>
                      <div className="text-sm text-gray-500">Amélioration des évaluations</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Variantes */}
              <div className="md:col-span-2 bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Comparaison des variantes</h2>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Variante
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Participants
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Taux d'interaction
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Taux de conversion
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Évaluation moyenne
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Amélioration
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {testResults.variants.map((variant) => (
                          <tr key={variant.variantId} className={variant.isWinner ? 'bg-green-50' : ''}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="text-sm font-medium text-gray-900">
                                  {variant.variantName}
                                  {variant.isWinner && (
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                      Gagnant
                                    </span>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {variant.participants}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatPercent(variant.interactionRate)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatPercent(variant.conversionRate)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {variant.averageRating.toFixed(1)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {variant.isWinner ? formatPercent(variant.improvement) : '-'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              
              {/* Recommandations */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Recommandations</h2>
                  <ul className="space-y-2">
                    {testResults.recommendations.map((recommendation, index) => (
                      <li key={index} className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-retreat-green mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">{recommendation}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <div className="mt-6">
                    <button
                      onClick={handleApplyOptimizations}
                      disabled={applyingOptimization}
                      className="w-full px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors disabled:bg-gray-300 disabled:text-gray-500"
                    >
                      {applyingOptimization ? 'Application en cours...' : 'Appliquer les optimisations'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default ABTestResultsPage;
