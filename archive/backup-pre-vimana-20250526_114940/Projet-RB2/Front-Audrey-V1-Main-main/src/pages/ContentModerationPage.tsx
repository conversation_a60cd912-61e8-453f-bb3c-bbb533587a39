import React from 'react';
import { ContentModerationForm } from '../components/moderation';
import { useTranslation } from '../hooks/useTranslation';

const ContentModerationPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">{t('moderation.contentModeration.title')}</h1>
      
      <div className="mb-6">
        <p className="text-gray-700">
          {t('moderation.contentModeration.description')}
        </p>
      </div>
      
      <ContentModerationForm />
    </div>
  );
};

export default ContentModerationPage;
