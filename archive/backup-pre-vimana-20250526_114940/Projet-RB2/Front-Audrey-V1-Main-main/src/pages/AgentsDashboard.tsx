/**
 * Dashboard des Agents - Retreat And Be
 * Date de création: Janvier 2024
 * 
 * Dashboard principal pour le monitoring et contrôle du système d'agents distribués
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Helmet } from 'react-helmet-async';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent,
  StatsCard,
  Button,
  Container,
  Grid,
  Stack,
  Badge,
  Table,
  Spinner,
  Alert
} from '../components/ui/design-system';
import { useToast } from '../components/ui/design-system/Toast';
import { AgentMonitoringPanel } from '../components/agents/AgentMonitoringPanel';
import { WorkflowControlPanel } from '../components/agents/WorkflowControlPanel';
import { MetricsVisualization } from '../components/agents/MetricsVisualization';
import { AlertsManagement } from '../components/agents/AlertsManagement';
import { ReportsGeneration } from '../components/agents/ReportsGeneration';
import { AgentConfiguration } from '../components/agents/AgentConfiguration';
import { agentsService } from '../services/api/agentsService';
import { useAuth } from '../store/globalStore';

interface AgentStatus {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
  uptime: number;
  lastHeartbeat: Date;
  activeJobs: number;
  totalJobs: number;
  errorRate: number;
  responseTime: number;
  memoryUsage: number;
  cpuUsage: number;
}

interface SystemMetrics {
  totalAgents: number;
  onlineAgents: number;
  totalJobs: number;
  activeJobs: number;
  completedJobs: number;
  failedJobs: number;
  averageResponseTime: number;
  systemUptime: number;
  alertsCount: number;
  criticalAlertsCount: number;
}

const AgentsDashboard: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  // États
  const [activeTab, setActiveTab] = useState<'overview' | 'monitoring' | 'workflows' | 'metrics' | 'alerts' | 'reports' | 'config'>('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [agents, setAgents] = useState<AgentStatus[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 secondes
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Charger les données des agents
  const loadAgentsData = async () => {
    try {
      setIsLoading(true);
      
      // Charger le statut des agents
      const agentsResponse = await agentsService.getAgentsStatus();
      setAgents(agentsResponse.data);
      
      // Charger les métriques système
      const metricsResponse = await agentsService.getSystemMetrics();
      setSystemMetrics(metricsResponse.data);
      
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les données des agents',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Effet pour le chargement initial et le rafraîchissement automatique
  useEffect(() => {
    loadAgentsData();
    
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(loadAgentsData, refreshInterval);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh, refreshInterval]);

  // Calculer les statistiques
  const getAgentStats = () => {
    if (!systemMetrics) return null;
    
    return {
      totalAgents: systemMetrics.totalAgents,
      onlineAgents: systemMetrics.onlineAgents,
      offlineAgents: systemMetrics.totalAgents - systemMetrics.onlineAgents,
      healthPercentage: Math.round((systemMetrics.onlineAgents / systemMetrics.totalAgents) * 100)
    };
  };

  const stats = getAgentStats();

  // Rendu des onglets
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Statistiques principales */}
            <Grid cols={4} gap="md">
              <StatsCard
                title="Agents en ligne"
                value={stats?.onlineAgents.toString() || '0'}
                icon="🤖"
                change={{ 
                  value: stats?.healthPercentage || 0, 
                  type: (stats?.healthPercentage || 0) > 80 ? 'increase' : 'decrease' 
                }}
                description={`sur ${stats?.totalAgents || 0} agents`}
              />
              <StatsCard
                title="Jobs actifs"
                value={systemMetrics?.activeJobs.toString() || '0'}
                icon="⚡"
                description="en cours d'exécution"
              />
              <StatsCard
                title="Temps de réponse"
                value={`${systemMetrics?.averageResponseTime || 0}ms`}
                icon="⏱️"
                change={{ 
                  value: 15, 
                  type: 'decrease' 
                }}
                description="moyenne"
              />
              <StatsCard
                title="Alertes"
                value={systemMetrics?.alertsCount.toString() || '0'}
                icon="🚨"
                change={{ 
                  value: systemMetrics?.criticalAlertsCount || 0, 
                  type: 'increase' 
                }}
                description="dont critiques"
              />
            </Grid>

            {/* Vue d'ensemble des agents */}
            <Grid cols={2} gap="lg">
              <Card>
                <CardHeader>
                  <CardTitle>État des Agents</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {agents.map((agent) => (
                      <div key={agent.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${
                            agent.status === 'online' ? 'bg-green-500' :
                            agent.status === 'offline' ? 'bg-gray-400' :
                            agent.status === 'error' ? 'bg-red-500' :
                            'bg-yellow-500'
                          }`} />
                          <div>
                            <p className="font-medium">{agent.name}</p>
                            <p className="text-sm text-gray-500">
                              {agent.activeJobs} jobs actifs
                            </p>
                          </div>
                        </div>
                        <Badge 
                          variant={
                            agent.status === 'online' ? 'success' :
                            agent.status === 'offline' ? 'secondary' :
                            agent.status === 'error' ? 'destructive' :
                            'warning'
                          }
                        >
                          {agent.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Activité Récente</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">Benchmark terminé</p>
                        <p className="text-xs text-gray-500">Agent Performance - il y a 2 min</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">Tests QA réussis</p>
                        <p className="text-xs text-gray-500">Agent QA - il y a 5 min</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">Alerte performance</p>
                        <p className="text-xs text-gray-500">Agent Monitoring - il y a 8 min</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Grid>
          </div>
        );

      case 'monitoring':
        return <AgentMonitoringPanel agents={agents} onRefresh={loadAgentsData} />;

      case 'workflows':
        return <WorkflowControlPanel />;

      case 'metrics':
        return <MetricsVisualization systemMetrics={systemMetrics} />;

      case 'alerts':
        return <AlertsManagement />;

      case 'reports':
        return <ReportsGeneration />;

      case 'config':
        return <AgentConfiguration agents={agents} onUpdate={loadAgentsData} />;

      default:
        return null;
    }
  };

  if (isLoading && !systemMetrics) {
    return (
      <Container className="py-8">
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </Container>
    );
  }

  return (
    <>
      <Helmet>
        <title>Dashboard des Agents | Retreat And Be</title>
        <meta name="description" content="Monitoring et contrôle du système d'agents distribués" />
      </Helmet>

      <Container className="py-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* En-tête */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Dashboard des Agents</h1>
              <p className="text-gray-600 mt-2">
                Monitoring et contrôle du système d'agents distribués
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label className="text-sm text-gray-600">Auto-refresh</label>
                <input
                  type="checkbox"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="rounded"
                />
              </div>
              
              <Button
                onClick={loadAgentsData}
                disabled={isLoading}
                leftIcon={isLoading ? "⏳" : "🔄"}
              >
                Actualiser
              </Button>
            </div>
          </div>

          {/* Alertes système */}
          {systemMetrics && systemMetrics.criticalAlertsCount > 0 && (
            <Alert variant="destructive" className="mb-6">
              <strong>Attention :</strong> {systemMetrics.criticalAlertsCount} alerte(s) critique(s) détectée(s)
            </Alert>
          )}

          {/* Navigation par onglets */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', label: 'Vue d\'ensemble', icon: '📊' },
                { id: 'monitoring', label: 'Monitoring', icon: '📈' },
                { id: 'workflows', label: 'Workflows', icon: '🔄' },
                { id: 'metrics', label: 'Métriques', icon: '📉' },
                { id: 'alerts', label: 'Alertes', icon: '🚨' },
                { id: 'reports', label: 'Rapports', icon: '📋' },
                { id: 'config', label: 'Configuration', icon: '⚙️' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Contenu de l'onglet actif */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {renderTabContent()}
          </motion.div>
        </motion.div>
      </Container>
    </>
  );
};

export default AgentsDashboard;
