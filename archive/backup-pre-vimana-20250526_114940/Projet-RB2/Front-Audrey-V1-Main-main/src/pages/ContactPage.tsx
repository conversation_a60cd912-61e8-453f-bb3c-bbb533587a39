/**
 * Page Contact - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Page de contact avec formulaire unifié.
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON>er,
  Grid,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Input,
  TextArea,
  Select,
  Stack,
  Badge
} from '../components/ui/design-system';
import { useToast } from '../components/ui/design-system/Toast';

interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState<ContactForm>({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: keyof ContactForm) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      toast.error('Veuillez remplir tous les champs');
      return;
    }

    setIsSubmitting(true);

    try {
      // Simuler l'envoi
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Message envoyé avec succès ! Nous vous répondrons rapidement.');
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      toast.error('Erreur lors de l\'envoi du message');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
      {/* Navigation simple */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-neutral-200">
        <Container>
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-2">
              <div className="text-2xl">🧘‍♀️</div>
              <span className="text-xl font-bold text-primary-900">Retreat & Be</span>
            </div>
            <Button variant="outline" onClick={() => window.history.back()}>
              Retour
            </Button>
          </div>
        </Container>
      </nav>

      <Container className="py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Hero */}
          <div className="text-center mb-16">
            <Badge variant="info" className="mb-4">Contact</Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Nous sommes là pour vous aider
            </h1>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Une question, une suggestion ou besoin d'aide ? Notre équipe est à votre écoute 
              et vous répondra dans les plus brefs délais.
            </p>
          </div>

          <Grid cols={3} gap="lg">
            {/* Informations de contact */}
            <div className="space-y-6">
              <Card>
                <CardContent className="p-6">
                  <div className="text-center">
                    <div className="text-3xl mb-3">📧</div>
                    <h3 className="font-semibold mb-2">Email</h3>
                    <p className="text-neutral-600"><EMAIL></p>
                    <p className="text-sm text-neutral-500 mt-1">
                      Réponse sous 24h
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="text-center">
                    <div className="text-3xl mb-3">📞</div>
                    <h3 className="font-semibold mb-2">Téléphone</h3>
                    <p className="text-neutral-600">+33 1 23 45 67 89</p>
                    <p className="text-sm text-neutral-500 mt-1">
                      Lun-Ven 9h-18h
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="text-center">
                    <div className="text-3xl mb-3">💬</div>
                    <h3 className="font-semibold mb-2">Chat en direct</h3>
                    <p className="text-neutral-600">Support instantané</p>
                    <Button variant="outline" size="sm" className="mt-2">
                      Ouvrir le chat
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Formulaire de contact */}
            <div className="col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Envoyez-nous un message</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit}>
                    <Stack spacing="md">
                      <Grid cols={2} gap="md">
                        <Input
                          label="Nom complet"
                          placeholder="Jean Dupont"
                          value={formData.name}
                          onChange={handleInputChange('name')}
                          required
                        />
                        <Input
                          type="email"
                          label="Email"
                          placeholder="<EMAIL>"
                          value={formData.email}
                          onChange={handleInputChange('email')}
                          required
                        />
                      </Grid>

                      <Select
                        label="Sujet"
                        value={formData.subject}
                        onChange={handleInputChange('subject')}
                        options={[
                          { value: '', label: 'Sélectionnez un sujet' },
                          { value: 'question-generale', label: 'Question générale' },
                          { value: 'support-technique', label: 'Support technique' },
                          { value: 'partenariat', label: 'Partenariat' },
                          { value: 'devenir-professionnel', label: 'Devenir professionnel' },
                          { value: 'feedback', label: 'Feedback' },
                          { value: 'autre', label: 'Autre' },
                        ]}
                        required
                      />

                      <TextArea
                        label="Message"
                        placeholder="Décrivez votre demande en détail..."
                        value={formData.message}
                        onChange={handleInputChange('message')}
                        rows={6}
                        required
                      />

                      <Button
                        type="submit"
                        variant="primary"
                        size="lg"
                        fullWidth
                        loading={isSubmitting}
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? 'Envoi en cours...' : 'Envoyer le message'}
                      </Button>
                    </Stack>
                  </form>
                </CardContent>
              </Card>
            </div>
          </Grid>

          {/* FAQ rapide */}
          <Card className="mt-16">
            <CardHeader>
              <CardTitle className="text-center">Questions fréquentes</CardTitle>
            </CardHeader>
            <CardContent>
              <Grid cols={2} gap="lg">
                <div>
                  <h3 className="font-semibold mb-2">Comment réserver une retraite ?</h3>
                  <p className="text-neutral-600 text-sm">
                    Créez un compte, explorez nos retraites et suivez le processus de réservation guidé.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Puis-je annuler ma réservation ?</h3>
                  <p className="text-neutral-600 text-sm">
                    Oui, selon les conditions d'annulation spécifiques à chaque retraite.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Comment devenir professionnel partenaire ?</h3>
                  <p className="text-neutral-600 text-sm">
                    Contactez-nous avec votre profil et vos certifications pour rejoindre notre réseau.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Les paiements sont-ils sécurisés ?</h3>
                  <p className="text-neutral-600 text-sm">
                    Absolument, nous utilisons les dernières technologies de sécurisation des paiements.
                  </p>
                </div>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>
      </Container>
    </div>
  );
};

export default ContactPage;
