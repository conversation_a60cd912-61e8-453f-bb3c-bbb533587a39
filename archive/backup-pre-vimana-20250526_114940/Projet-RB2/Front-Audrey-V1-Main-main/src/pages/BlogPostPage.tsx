import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-toastify';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { blogService } from '../services/api/blogService';
import { socialAnalyticsService } from '../services/api/socialAnalyticsService';
import { useAuthContext } from '../hooks/useAuthContext';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import LoadingSpinner from '../components/atoms/LoadingSpinner/LoadingSpinner';

const BlogPostPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [blogPost, setBlogPost] = useState<any>(null);
  const [comments, setComments] = useState<any[]>([]);
  const [newComment, setNewComment] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingComments, setIsLoadingComments] = useState<boolean>(false);
  const [isSendingComment, setIsSendingComment] = useState<boolean>(false);
  const [isLiked, setIsLiked] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Charger les détails de l'article de blog
  useEffect(() => {
    const fetchBlogPostDetails = async () => {
      if (!id) return;
      
      try {
        setIsLoading(true);
        setError(null);
        
        const data = await blogService.getBlogPostById(id);
        setBlogPost(data);
        
        // Vérifier si l'utilisateur a aimé l'article
        if (user && data.likes && Array.isArray(data.likes)) {
          setIsLiked(data.likes.includes(user.id));
        }
        
        // Enregistrer l'événement de vue
        socialAnalyticsService.trackView(id, 'blog');
      } catch (error) {
        console.error('Error fetching blog post details:', error);
        setError('Une erreur est survenue lors du chargement de l\'article de blog.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogPostDetails();
  }, [id, user]);

  // Charger les commentaires
  useEffect(() => {
    const fetchComments = async () => {
      if (!id) return;
      
      try {
        setIsLoadingComments(true);
        
        const data = await blogService.getBlogPostComments(id);
        setComments(data);
      } catch (error) {
        console.error('Error fetching blog post comments:', error);
      } finally {
        setIsLoadingComments(false);
      }
    };
    
    if (blogPost) {
      fetchComments();
    }
  }, [id, blogPost]);

  // Envoyer un commentaire
  const handleSendComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!id || !newComment.trim()) return;
    
    try {
      setIsSendingComment(true);
      
      await blogService.addBlogPostComment(id, newComment);
      
      // Enregistrer l'événement d'engagement
      socialAnalyticsService.trackEngagement(id, 'blog', 'comment');
      
      // Réinitialiser le champ de commentaire
      setNewComment('');
      
      // Recharger les commentaires
      const data = await blogService.getBlogPostComments(id);
      setComments(data);
      
      toast.success('Commentaire ajouté avec succès');
    } catch (error) {
      console.error('Error sending comment:', error);
      toast.error('Erreur lors de l\'envoi du commentaire');
    } finally {
      setIsSendingComment(false);
    }
  };

  // Aimer/Ne plus aimer l'article
  const handleToggleLike = async () => {
    if (!id || !user) return;
    
    try {
      if (isLiked) {
        await blogService.unlikeBlogPost(id);
      } else {
        await blogService.likeBlogPost(id);
        
        // Enregistrer l'événement d'engagement
        socialAnalyticsService.trackEngagement(id, 'blog', 'like');
      }
      
      // Mettre à jour l'état local
      setIsLiked(!isLiked);
      
      // Mettre à jour le nombre de likes dans l'article
      const updatedBlogPost = await blogService.getBlogPostById(id);
      setBlogPost(updatedBlogPost);
    } catch (error) {
      console.error('Error toggling like:', error);
      toast.error('Erreur lors de l\'action');
    }
  };

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'PPP', { locale: fr });
  };

  if (isLoading) {
    return (
      <>
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner />
          </div>
        </div>
        <Footer />
      </>
    );
  }

  if (error || !blogPost) {
    return (
      <>
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-red-600 p-4">
            <p>{error || 'Article de blog non trouvé'}</p>
            <button
              className="mt-2 text-retreat-green hover:text-retreat-green-dark"
              onClick={() => navigate('/blog')}
            >
              Retour au blog
            </button>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  const isAuthor = user?.id === blogPost.authorId;

  return (
    <>
      <Helmet>
        <title>{blogPost.title} | Retreat And Be</title>
        <meta name="description" content={blogPost.content.substring(0, 160)} />
      </Helmet>
      
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          {/* En-tête de l'article */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{blogPost.title}</h1>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-retreat-green-light flex items-center justify-center text-retreat-green font-medium">
                  {blogPost.authorName.charAt(0).toUpperCase()}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">{blogPost.authorName}</p>
                  <p className="text-sm text-gray-500">
                    {formatDate(blogPost.publishDate || blogPost.createdAt)}
                  </p>
                </div>
              </div>
              
              {isAuthor && (
                <Link
                  to={`/blog/edit/${id}`}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                >
                  <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Modifier
                </Link>
              )}
            </div>
          </div>
          
          {/* Image principale */}
          {blogPost.imageUrl && (
            <div className="mb-8">
              <img
                src={blogPost.imageUrl}
                alt={blogPost.title}
                className="w-full h-auto rounded-lg"
              />
            </div>
          )}
          
          {/* Contenu de l'article */}
          <div className="prose prose-lg max-w-none mb-8">
            <div dangerouslySetInnerHTML={{ __html: blogPost.content }} />
          </div>
          
          {/* Tags */}
          {blogPost.tags && blogPost.tags.length > 0 && (
            <div className="mb-8">
              <div className="flex flex-wrap gap-2">
                {blogPost.tags.map((tag: string) => (
                  <Link
                    key={tag}
                    to={`/blog?tags=${tag}`}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-retreat-green-light text-retreat-green hover:bg-retreat-green hover:text-white transition-colors"
                  >
                    #{tag}
                  </Link>
                ))}
              </div>
            </div>
          )}
          
          {/* Actions */}
          <div className="flex items-center justify-between border-t border-b border-gray-200 py-4 mb-8">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleToggleLike}
                className={`inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium ${
                  isLiked
                    ? 'bg-retreat-green text-white'
                    : 'bg-white text-gray-700 border border-gray-300'
                }`}
                disabled={!user}
              >
                <svg className="h-4 w-4 mr-1" fill={isLiked ? 'currentColor' : 'none'} viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                {blogPost.likeCount || 0} J'aime
              </button>
              
              <button
                onClick={() => document.getElementById('comments-section')?.scrollIntoView({ behavior: 'smooth' })}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
                {comments.length} Commentaires
              </button>
            </div>
            
            <div>
              <button
                onClick={() => {
                  navigator.share({
                    title: blogPost.title,
                    text: blogPost.title,
                    url: window.location.href,
                  }).catch(err => console.error('Error sharing:', err));
                }}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                </svg>
                Partager
              </button>
            </div>
          </div>
          
          {/* Section des commentaires */}
          <div id="comments-section" className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Commentaires ({comments.length})</h2>
            
            {/* Formulaire de commentaire */}
            {user ? (
              <form onSubmit={handleSendComment} className="mb-6">
                <div className="mb-4">
                  <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-1">
                    Ajouter un commentaire
                  </label>
                  <textarea
                    id="comment"
                    rows={3}
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
                    placeholder="Partagez vos pensées..."
                    required
                  />
                </div>
                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={isSendingComment || !newComment.trim()}
                  >
                    {isSendingComment ? (
                      <>
                        <LoadingSpinner size="small" color="white" />
                        <span className="ml-2">Envoi en cours...</span>
                      </>
                    ) : (
                      'Publier le commentaire'
                    )}
                  </button>
                </div>
              </form>
            ) : (
              <div className="bg-gray-50 rounded-lg p-4 mb-6 text-center">
                <p className="text-gray-700">
                  <Link to="/login" className="text-retreat-green hover:text-retreat-green-dark font-medium">
                    Connectez-vous
                  </Link>{' '}
                  pour ajouter un commentaire
                </p>
              </div>
            )}
            
            {/* Liste des commentaires */}
            {isLoadingComments ? (
              <div className="flex justify-center items-center h-32">
                <LoadingSpinner />
              </div>
            ) : comments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>Aucun commentaire pour le moment</p>
                <p className="text-sm mt-1">Soyez le premier à commenter cet article</p>
              </div>
            ) : (
              <div className="space-y-6">
                {comments.map((comment) => (
                  <div key={comment.id} className="bg-white rounded-lg shadow-sm p-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 h-10 w-10 rounded-full bg-retreat-green-light flex items-center justify-center text-retreat-green font-medium">
                        {comment.userName.charAt(0).toUpperCase()}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900">{comment.userName}</p>
                          <p className="text-xs text-gray-500">
                            {formatDate(comment.createdAt)}
                          </p>
                        </div>
                        <p className="text-gray-700 mt-1">{comment.content}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
      
      <Footer />
    </>
  );
};

export default BlogPostPage;
