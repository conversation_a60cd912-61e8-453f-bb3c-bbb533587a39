import React, { useState } from 'react';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import {
  UserCircleIcon,
  CalendarIcon,
  HeartIcon,
  CreditCardIcon,
  BellIcon,
  ShieldCheckIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';

interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const AccountPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile');

  const tabs: TabProps[] = [
    {
      label: 'Mon Profil',
      icon: <UserCircleIcon className='w-6 h-6' />,
      content: (
        <div className='space-y-6'>
          <h3 className='text-xl font-semibold'>Informations personnelles</h3>
          <form className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <label htmlFor='firstNameInput' className='block text-sm font-medium text-gray-700'>
                  Prénom
                </label>
                <input
                  id='firstNameInput'
                  type='text'
                  className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                />
              </div>
              <div>
                <label htmlFor='lastNameInput' className='block text-sm font-medium text-gray-700'>
                  Nom
                </label>
                <input
                  id='lastNameInput'
                  type='text'
                  className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                />
              </div>
              <div>
                <label htmlFor='emailInput' className='block text-sm font-medium text-gray-700'>
                  Email
                </label>
                <input
                  id='emailInput'
                  type='email'
                  className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                />
              </div>
              <div>
                <label htmlFor='phoneInput' className='block text-sm font-medium text-gray-700'>
                  Téléphone
                </label>
                <input
                  id='phoneInput'
                  type='tel'
                  className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
                />
              </div>
            </div>
            <button
              type='submit'
              className='mt-4 px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-opacity-90'
            >
              Sauvegarder les modifications
            </button>
          </form>
        </div>
      ),
    },
    {
      label: 'Mes Réservations',
      icon: <CalendarIcon className='w-6 h-6' />,
      content: (
        <div className='space-y-6'>
          <h3 className='text-xl font-semibold'>Réservations en cours</h3>
          <div className='space-y-4'>
            {/* Exemple de réservation */}
            <div className='bg-white p-4 rounded-lg shadow'>
              <div className='flex justify-between items-start'>
                <div>
                  <h4 className='font-medium'>Retraite Yoga & Méditation</h4>
                  <p className='text-sm text-gray-600'>Du 15 au 20 juillet 2024</p>
                  <p className='text-sm text-gray-600'>Provence-Alpes-Côte d&apos;Azur</p>
                </div>
                <button className='text-retreat-green hover:text-opacity-80'>
                  Voir les détails
                </button>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      label: 'Mes Favoris',
      icon: <HeartIcon className='w-6 h-6' />,
      content: (
        <div className='space-y-6'>
          <h3 className='text-xl font-semibold'>Retraites favorites</h3>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            {/* Exemple de favori */}
            <div className='bg-white p-4 rounded-lg shadow'>
              <h4 className='font-medium'>Retraite Détox & Bien-être</h4>
              <p className='text-sm text-gray-600'>Bretagne</p>
              <p className='text-sm text-gray-600'>À partir de 599€</p>
              <div className='mt-2 flex justify-end'>
                <button className='text-retreat-green hover:text-opacity-80'>
                  Voir la retraite
                </button>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      label: 'Paiement',
      icon: <CreditCardIcon className='w-6 h-6' />,
      content: (
        <div className='space-y-6'>
          <h3 className='text-xl font-semibold'>Méthodes de paiement</h3>
          <div className='space-y-4'>
            <button className='w-full px-4 py-2 text-left border rounded-md hover:border-retreat-green flex items-center space-x-2'>
              <CreditCardIcon className='w-5 h-5' />
              <span>Ajouter une carte de paiement</span>
            </button>
          </div>
        </div>
      ),
    },
    {
      label: 'Notifications',
      icon: <BellIcon className='w-6 h-6' />,
      content: (
        <div className='space-y-6'>
          <h3 className='text-xl font-semibold'>Préférences de notification</h3>
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <span>Notifications par email</span>
              <input type='checkbox' className='toggle toggle-retreat-green' />
            </div>
            <div className='flex items-center justify-between'>
              <span>Notifications push</span>
              <input type='checkbox' className='toggle toggle-retreat-green' />
            </div>
          </div>
        </div>
      ),
    },
    {
      label: 'Sécurité',
      icon: <ShieldCheckIcon className='w-6 h-6' />,
      content: (
        <div className='space-y-6'>
          <h3 className='text-xl font-semibold'>Paramètres de sécurité</h3>
          <div className='space-y-4'>
            <button className='w-full px-4 py-2 text-left border rounded-md hover:border-retreat-green'>
              Changer le mot de passe
            </button>
            <button className='w-full px-4 py-2 text-left border rounded-md hover:border-retreat-green'>
              Activer l&apos;authentification à deux facteurs
            </button>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className='min-h-screen bg-gray-50'>
      <NavBarClient />

      <main className='pt-24 pb-16'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='bg-white rounded-lg shadow-lg overflow-hidden'>
            {/* Header */}
            <div className='p-6 bg-retreat-green'>
              <h1 className='text-2xl font-bold text-white'>Mon Compte</h1>
            </div>

            {/* Content */}
            <div className='flex flex-col md:flex-row'>
              {/* Sidebar */}
              <div className='w-full md:w-64 bg-gray-50 p-6'>
                <nav className='space-y-2'>
                  {tabs.map((tab) => (
                    <button
                      key={tab.label}
                      onClick={() => setActiveTab(tab.label)}
                      className={`w-full flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                        activeTab === tab.label
                          ? 'bg-retreat-green text-white'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      {tab.icon}
                      <span>{tab.label}</span>
                    </button>
                  ))}
                  <button className='w-full flex items-center space-x-2 px-4 py-2 rounded-lg text-red-600 hover:bg-red-50'>
                    <ArrowRightOnRectangleIcon className='w-6 h-6' />
                    <span>Déconnexion</span>
                  </button>
                </nav>
              </div>

              {/* Main Content */}
              <div className='flex-1 p-6'>
                {tabs.find((tab) => tab.label === activeTab)?.content}
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default AccountPage;
