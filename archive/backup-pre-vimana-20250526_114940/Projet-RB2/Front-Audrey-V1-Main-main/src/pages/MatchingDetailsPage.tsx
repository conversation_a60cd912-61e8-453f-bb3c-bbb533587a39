import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-toastify';
import { matchingService } from '../services/api/matchingService';
import { matchingAnalyticsService } from '../services/api/matchingAnalyticsService';
import { partnerService } from '../services/api/partnerService';
import { retreatService } from '../services/api/retreatService';
import { useAuthContext } from '../hooks/useAuthContext';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import LoadingSpinner from '../components/atoms/LoadingSpinner/LoadingSpinner';
import MatchingContactForm from '../components/matching/MatchingContactForm';
import MatchingBookingForm from '../components/matching/MatchingBookingForm';
import MatchingVideoRooms from '../components/matching/MatchingVideoRooms';
import MatchingVideoRoomForm from '../components/matching/MatchingVideoRoomForm';
import MatchingScoreChart from '../components/matching/MatchingScoreChart';

const MatchingDetailsPage: React.FC = () => {
  const { partnerId, retreatId } = useParams<{ partnerId: string; retreatId: string }>();
  const navigate = useNavigate();
  const { user } = useAuthContext();

  const [matchingScore, setMatchingScore] = useState<any>(null);
  const [partner, setPartner] = useState<any>(null);
  const [retreat, setRetreat] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isContactModalOpen, setIsContactModalOpen] = useState<boolean>(false);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState<boolean>(false);
  const [isVideoRoomModalOpen, setIsVideoRoomModalOpen] = useState<boolean>(false);

  useEffect(() => {
    const loadData = async () => {
      if (!partnerId || !retreatId) {
        setError('Informations manquantes pour afficher les détails du matching');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // Charger le score de matching
        const scoreData = await matchingService.getMatchingScore(partnerId, retreatId);
        setMatchingScore(scoreData);

        // Charger les détails du partenaire
        const partnerData = await partnerService.getPartnerById(partnerId);
        setPartner(partnerData);

        // Charger les détails de la retraite
        const retreatData = await retreatService.getRetreatById(retreatId);
        setRetreat(retreatData);

        setError(null);
      } catch (error) {
        console.error('Erreur lors du chargement des détails du matching:', error);
        setError('Impossible de charger les détails du matching');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [partnerId, retreatId]);

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  // Fonction pour obtenir la couleur en fonction du score
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-500';
    return 'text-red-600';
  };

  // Fonction pour obtenir la couleur de fond en fonction du score
  const getScoreBackgroundColor = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    if (score >= 40) return 'bg-orange-100';
    return 'bg-red-100';
  };

  // Fonction pour gérer le succès de l'envoi du message
  const handleContactSuccess = (conversationId: string) => {
    setIsContactModalOpen(false);
    // Rediriger vers la conversation
    navigate(`/messaging/conversation/${conversationId}`);
  };

  // Fonction pour gérer le succès de la création de réservation
  const handleBookingSuccess = (bookingId: string) => {
    setIsBookingModalOpen(false);
    // Rediriger vers la page de la réservation
    navigate(`/bookings/${bookingId}`);
  };

  // Fonction pour gérer le succès de la création d'une salle de vidéoconférence
  const handleVideoRoomSuccess = (videoRoom: any) => {
    setIsVideoRoomModalOpen(false);
    // Rafraîchir la page pour afficher la nouvelle salle
    window.location.reload();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Navbar />
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !matchingScore || !partner || !retreat) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Navbar />
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center py-8">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  {error || 'Impossible de charger les détails du matching'}
                </h3>
                <div className="mt-6">
                  <Link
                    to="/partners/matching"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                  >
                    Retour à la recherche
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Détails du Matching | Retreat And Be</title>
        <meta
          name="description"
          content={`Détails de compatibilité entre ${partner.companyName} et ${retreat.title}`}
        />
      </Helmet>

      <Navbar />

      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <div>
                    <Link to="/" className="text-gray-400 hover:text-gray-500">
                      Accueil
                    </Link>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="flex-shrink-0 h-5 w-5 text-gray-300"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                    >
                      <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                    </svg>
                    <Link
                      to="/partners/matching"
                      className="ml-4 text-gray-400 hover:text-gray-500"
                    >
                      Matching
                    </Link>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="flex-shrink-0 h-5 w-5 text-gray-300"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                    >
                      <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                    </svg>
                    <span className="ml-4 text-gray-500">Détails</span>
                  </div>
                </li>
              </ol>
            </nav>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <h1 className="text-2xl font-bold text-gray-900">
                  Compatibilité Partenaire-Retraite
                </h1>
                <div className="mt-4 md:mt-0 flex items-center">
                  <div
                    className={`text-3xl font-bold ${getScoreColor(matchingScore.score)}`}
                  >
                    {matchingScore.score}%
                  </div>
                  <span className="ml-2 text-sm text-gray-500">de compatibilité</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
              {/* Détails du partenaire */}
              <div className="p-6 border-b md:border-b-0 md:border-r border-gray-200">
                <div className="flex items-start">
                  {partner.logo ? (
                    <img
                      src={partner.logo}
                      alt={partner.companyName}
                      className="h-16 w-16 rounded-full object-cover mr-4"
                    />
                  ) : (
                    <div className="h-16 w-16 rounded-full bg-retreat-green flex items-center justify-center text-white text-xl font-bold mr-4">
                      {partner.companyName.charAt(0)}
                    </div>
                  )}
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">{partner.companyName}</h2>
                    <div className="mt-1 flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {partner.type}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {partner.category}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium text-gray-900">À propos</h3>
                  <p className="mt-2 text-gray-600">{partner.description}</p>
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Spécialisations</h3>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {partner.specializations.map((spec: string) => (
                        <span
                          key={spec}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {spec}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Langues parlées</h3>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {partner.languages.map((lang: string) => (
                        <span
                          key={lang}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                        >
                          {lang}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex flex-wrap gap-2">
                  <button
                    onClick={() => {
                      setIsContactModalOpen(true);
                      // Enregistrer l'événement de vue
                      if (matchingScore) {
                        matchingAnalyticsService.recordView(
                          {
                            partnerId: partnerId || '',
                            retreatId: retreatId || '',
                            score: matchingScore.score,
                            compatibilityFactors: matchingScore.compatibilityFactors,
                          },
                          { page: 'MatchingDetailsPage', section: 'partnerDetails' },
                        );
                      }
                    }}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                  >
                    Contacter ce partenaire
                  </button>

                  {/* Bouton de réservation - visible uniquement pour les organisateurs de la retraite */}
                  {user && retreat && user.id === retreat.organizerId && (
                    <button
                      onClick={() => {
                        setIsBookingModalOpen(true);
                        // Enregistrer l'événement de vue
                        if (matchingScore) {
                          matchingAnalyticsService.recordView(
                            {
                              partnerId: partnerId || '',
                              retreatId: retreatId || '',
                              score: matchingScore.score,
                              compatibilityFactors: matchingScore.compatibilityFactors,
                            },
                            { page: 'MatchingDetailsPage', section: 'retreatDetails' },
                          );
                        }
                      }}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Réserver ce partenaire
                    </button>
                  )}

                  {/* Bouton de vidéoconférence */}
                  <button
                    onClick={() => {
                      setIsVideoRoomModalOpen(true);
                      // Enregistrer l'événement de vue
                      if (matchingScore) {
                        matchingAnalyticsService.recordView(
                          {
                            partnerId: partnerId || '',
                            retreatId: retreatId || '',
                            score: matchingScore.score,
                            compatibilityFactors: matchingScore.compatibilityFactors,
                          },
                          { page: 'MatchingDetailsPage', section: 'compatibilityScores' },
                        );
                      }
                    }}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    Vidéoconférence
                  </button>

                  <Link
                    to={`/partners/${partnerId}`}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                  >
                    Voir profil complet
                  </Link>
                </div>
              </div>

              {/* Détails de la retraite */}
              <div className="p-6">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">{retreat.title}</h2>
                  <div className="mt-1 flex items-center">
                    <svg
                      className="flex-shrink-0 h-5 w-5 text-gray-400"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-1.5 text-sm text-gray-500">{retreat.location}</span>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium text-gray-900">Description</h3>
                  <p className="mt-2 text-gray-600">{retreat.description}</p>
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Dates</h3>
                    <p className="mt-2 text-gray-900">
                      Du {formatDate(retreat.startDate)} au {formatDate(retreat.endDate)}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Capacité</h3>
                    <p className="mt-2 text-gray-900">{retreat.capacity} personnes</p>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-sm font-medium text-gray-500">Prix</h3>
                  <p className="mt-2 text-gray-900">{retreat.price} €</p>
                </div>

                <div className="mt-6">
                  <Link
                    to={`/retreats/${retreatId}`}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                  >
                    Voir la retraite
                  </Link>
                </div>
              </div>
            </div>

            {/* Facteurs de compatibilité */}
            <div className="p-6 border-t border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Facteurs de compatibilité
              </h2>

              {/* Visualisation interactive */}
              <div className="mb-6">
                <MatchingScoreChart
                  score={matchingScore.score}
                  compatibilityFactors={matchingScore.compatibilityFactors}
                  width={window.innerWidth < 768 ? 300 : 500}
                  height={window.innerWidth < 768 ? 300 : 400}
                />
              </div>

              {/* Affichage des facteurs en cartes */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className={`p-4 rounded-lg ${getScoreBackgroundColor(matchingScore.compatibilityFactors.skillMatch)}`}>
                  <h3 className="text-sm font-medium text-gray-900">Compétences</h3>
                  <div className="mt-1 flex items-center">
                    <span className={`text-2xl font-bold ${getScoreColor(matchingScore.compatibilityFactors.skillMatch)}`}>
                      {matchingScore.compatibilityFactors.skillMatch}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Poids: 35%</p>
                </div>
                <div className={`p-4 rounded-lg ${getScoreBackgroundColor(matchingScore.compatibilityFactors.availabilityMatch)}`}>
                  <h3 className="text-sm font-medium text-gray-900">Disponibilité</h3>
                  <div className="mt-1 flex items-center">
                    <span className={`text-2xl font-bold ${getScoreColor(matchingScore.compatibilityFactors.availabilityMatch)}`}>
                      {matchingScore.compatibilityFactors.availabilityMatch}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Poids: 15%</p>
                </div>
                <div className={`p-4 rounded-lg ${getScoreBackgroundColor(matchingScore.compatibilityFactors.locationMatch)}`}>
                  <h3 className="text-sm font-medium text-gray-900">Localisation</h3>
                  <div className="mt-1 flex items-center">
                    <span className={`text-2xl font-bold ${getScoreColor(matchingScore.compatibilityFactors.locationMatch)}`}>
                      {matchingScore.compatibilityFactors.locationMatch}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Poids: 20%</p>
                </div>
                <div className={`p-4 rounded-lg ${getScoreBackgroundColor(matchingScore.compatibilityFactors.ratingMatch)}`}>
                  <h3 className="text-sm font-medium text-gray-900">Évaluations</h3>
                  <div className="mt-1 flex items-center">
                    <span className={`text-2xl font-bold ${getScoreColor(matchingScore.compatibilityFactors.ratingMatch)}`}>
                      {matchingScore.compatibilityFactors.ratingMatch}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Poids: 20%</p>
                </div>
                <div className={`p-4 rounded-lg ${getScoreBackgroundColor(matchingScore.compatibilityFactors.budgetMatch)}`}>
                  <h3 className="text-sm font-medium text-gray-900">Budget</h3>
                  <div className="mt-1 flex items-center">
                    <span className={`text-2xl font-bold ${getScoreColor(matchingScore.compatibilityFactors.budgetMatch)}`}>
                      {matchingScore.compatibilityFactors.budgetMatch}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Poids: 10%</p>
                </div>
              </div>
            </div>

            {/* Vidéoconférences */}
            <div className="p-6 border-t border-gray-200">
              <MatchingVideoRooms
                matchingResult={{
                  partnerId: partnerId || '',
                  retreatId: retreatId || '',
                  score: matchingScore.score,
                  compatibilityFactors: matchingScore.compatibilityFactors,
                  partner,
                  retreat,
                }}
                userRole={user?.role || 'USER'}
                userId={user?.id || ''}
                onCreateRoom={() => setIsVideoRoomModalOpen(true)}
              />
            </div>
          </div>
        </div>
      </main>

      {/* Modal de contact */}
      {isContactModalOpen && (
        <div className="fixed inset-0 overflow-y-auto z-50">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={() => setIsContactModalOpen(false)}></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
              &#8203;
            </span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Contacter {partner.companyName}
                  </h3>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-500"
                    onClick={() => setIsContactModalOpen(false)}
                  >
                    <span className="sr-only">Fermer</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <p className="text-sm text-gray-500 mb-4">
                  Envoyez un message à ce partenaire concernant la retraite "{retreat.title}".
                </p>

                <MatchingContactForm
                  matchingResult={{
                    partnerId: partnerId || '',
                    retreatId: retreatId || '',
                    score: matchingScore.score,
                    compatibilityFactors: matchingScore.compatibilityFactors,
                    partner,
                    retreat,
                  }}
                  userRole={user?.role || 'USER'}
                  onSuccess={handleContactSuccess}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de réservation */}
      {isBookingModalOpen && (
        <div className="fixed inset-0 overflow-y-auto z-50">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={() => setIsBookingModalOpen(false)}></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
              &#8203;
            </span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Réserver {partner.companyName}
                  </h3>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-500"
                    onClick={() => setIsBookingModalOpen(false)}
                  >
                    <span className="sr-only">Fermer</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <p className="text-sm text-gray-500 mb-4">
                  Créez une réservation pour ce partenaire pour votre retraite "{retreat.title}".
                </p>

                <MatchingBookingForm
                  matchingResult={{
                    partnerId: partnerId || '',
                    retreatId: retreatId || '',
                    score: matchingScore.score,
                    compatibilityFactors: matchingScore.compatibilityFactors,
                    partner,
                    retreat,
                  }}
                  onSuccess={handleBookingSuccess}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de vidéoconférence */}
      {isVideoRoomModalOpen && (
        <div className="fixed inset-0 overflow-y-auto z-50">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={() => setIsVideoRoomModalOpen(false)}></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
              &#8203;
            </span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Programmer une vidéoconférence
                  </h3>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-500"
                    onClick={() => setIsVideoRoomModalOpen(false)}
                  >
                    <span className="sr-only">Fermer</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <p className="text-sm text-gray-500 mb-4">
                  Programmez une vidéoconférence avec {partner.companyName} pour discuter de la retraite "{retreat.title}".
                </p>

                <MatchingVideoRoomForm
                  matchingResult={{
                    partnerId: partnerId || '',
                    retreatId: retreatId || '',
                    score: matchingScore.score,
                    compatibilityFactors: matchingScore.compatibilityFactors,
                    partner,
                    retreat,
                  }}
                  onSuccess={handleVideoRoomSuccess}
                  onCancel={() => setIsVideoRoomModalOpen(false)}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default MatchingDetailsPage;
