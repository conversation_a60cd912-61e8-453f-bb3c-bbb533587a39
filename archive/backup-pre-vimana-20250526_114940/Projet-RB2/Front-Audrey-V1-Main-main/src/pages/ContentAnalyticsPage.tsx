import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../hooks/useAuthContext';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import ContentAnalytics from '../components/content/ContentAnalytics';

const ContentAnalyticsPage: React.FC = () => {
  const { user } = useAuthContext();

  return (
    <>
      <Helmet>
        <title>Analyses de contenu - Retreat And Be</title>
        <meta
          name="description"
          content="Analysez les performances de votre contenu sur la plateforme Retreat And Be."
        />
      </Helmet>

      <Navbar />

      <main className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Analyses de contenu</h1>
          <p className="mt-2 text-gray-600">
            Suivez les performances de votre contenu et optimisez votre stratégie
          </p>
        </div>

        <ContentAnalytics userId={user?.id} />
      </main>

      <Footer />
    </>
  );
};

export default ContentAnalyticsPage;
