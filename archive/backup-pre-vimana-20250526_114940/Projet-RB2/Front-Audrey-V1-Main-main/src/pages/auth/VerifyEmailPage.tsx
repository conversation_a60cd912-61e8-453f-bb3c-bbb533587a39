'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation'; // Using Next.js App Router hooks
import { useAuth } from '../../contexts/AuthContext';
import MainLayout from '../../components/templates/MainLayout/MainLayout'; // Corrected path
import { toast } from 'react-toastify';

// TODO: Replace with ShadCN/UI Button and Spinner/Loading component if available
// import { Button } from '@/components/ui/button';
// import { Spinner } from '@/components/ui/spinner'; 

const VerifyEmailPage: React.FC = () => {
  const { verifyEmail } = useAuth();
  const params = useParams();
  const router = useRouter();
  const token = params?.token as string | undefined;

  const [isLoading, setIsLoading] = useState(true);
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (token) {
      const attemptVerification = async () => {
        setIsLoading(true);
        setError(null);
        setMessage(null);
        try {
          await verifyEmail(token);
          setMessage("Email verified successfully! You will be redirected to login.");
          toast.success("Email verified successfully!");
          setTimeout(() => router.push('/login'), 3000);
        } catch (err: any) {
          const errorMessage = err.response?.data?.message || err.message || "Failed to verify email. The link may be invalid or expired.";
          setError(errorMessage);
          toast.error(errorMessage);
        } finally {
          setIsLoading(false);
        }
      };
      attemptVerification();
    } else {
      setError("Verification token not found. Please check the link or request a new one.");
      toast.error("Verification token not found.");
      setIsLoading(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, router]); // verifyEmail should be stable from useAuth if wrapped in useCallback

  const handleGoToLogin = () => {
    router.push('/login');
  };
  
  const handleRequestNewToken = () => {
    router.push('/request-verification-email');
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8 flex flex-col items-center text-center">
        <h1 className="text-3xl font-bold mb-6 text-gray-800">Email Verification</h1>
        {isLoading && (
          <div className="flex flex-col items-center">
            {/* <Spinner size="large" /> */}
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mb-3"></div>
            <p className="text-lg text-gray-600">Verifying your email, please wait...</p>
          </div>
        )}
        {!isLoading && message && (
          <div className="p-4 bg-green-100 text-green-700 rounded-md shadow">
            <p className="text-lg">{message}</p>
            <button 
                onClick={handleGoToLogin} 
                className="mt-4 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-md"
            >
                Go to Login
            </button>
          </div>
        )}
        {!isLoading && error && (
          <div className="p-4 bg-red-100 text-red-700 rounded-md shadow">
            <p className="text-lg">{error}</p>
            <button 
                onClick={handleRequestNewToken} 
                className="mt-4 mr-2 px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white font-semibold rounded-md"
            >
                Request New Link
            </button>
            <button 
                onClick={handleGoToLogin} 
                className="mt-4 px-4 py-2 border border-indigo-600 text-indigo-600 hover:bg-indigo-50 font-semibold rounded-md"
            >
                Go to Login
            </button>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default VerifyEmailPage;

 