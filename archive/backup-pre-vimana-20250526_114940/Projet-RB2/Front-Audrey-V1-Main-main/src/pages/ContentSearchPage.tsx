import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../hooks/useAuthContext';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import ContentSearch from '../components/content/ContentSearch';
import EditPostDialog from '../components/content/EditPostDialog';

interface Post {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl?: string;
  createdAt: string;
  userId: string;
  userName: string;
  userAvatar: string;
  likes: number;
  comments: number;
  views: number;
  status: 'published' | 'archived' | 'deleted';
  tags: string[];
  privacy: 'public' | 'friends' | 'private';
}

const ContentSearchPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);

  const handlePostSelect = (post: Post) => {
    setSelectedPost(post);
    setEditDialogOpen(true);
  };

  const handleSaveEdit = async (updatedPost: Post) => {
    // In a real implementation, you would save the changes to the backend
    setEditDialogOpen(false);
    setSuccess('Contenu mis à jour avec succès');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccess(null);
    }, 3000);
  };

  return (
    <>
      <Helmet>
        <title>Recherche de contenu - Retreat And Be</title>
        <meta
          name="description"
          content="Recherchez du contenu sur la plateforme Retreat And Be."
        />
      </Helmet>

      <Navbar />

      <main className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Recherche de contenu</h1>
            <p className="mt-2 text-gray-600">
              Trouvez rapidement le contenu que vous recherchez
            </p>
          </div>
          <div>
            <button
              onClick={() => navigate('/content-management')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Gérer le contenu
            </button>
          </div>
        </div>

        {success && (
          <div className="mb-4 bg-green-50 border border-green-200 text-green-800 rounded-md p-4">
            {success}
          </div>
        )}

        <ContentSearch onSelectPost={handlePostSelect} userId={user?.id} />

        {/* Edit Post Dialog */}
        {selectedPost && (
          <EditPostDialog
            isOpen={editDialogOpen}
            post={selectedPost}
            onSave={handleSaveEdit}
            onCancel={() => setEditDialogOpen(false)}
          />
        )}
      </main>

      <Footer />
    </>
  );
};

export default ContentSearchPage;
