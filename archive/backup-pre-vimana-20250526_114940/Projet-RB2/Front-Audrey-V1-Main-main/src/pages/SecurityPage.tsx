import React from 'react';
import { Helmet } from 'react-helmet-async';
import { SecurityDashboard } from '../components/security';
import MainLayout from '../components/templates/MainLayout/MainLayout';
import { useAuthContext } from '../hooks/useAuthContext';
import { Navigate } from 'react-router-dom';
import { hasRole } from '../utils/auth';

const SecurityPage: React.FC = () => {
  const { user, isAuthenticated: checkAuth } = useAuthContext();

  // Vérifier si l'utilisateur est authentifié et a le rôle d'administrateur
  if (!checkAuth || !hasRole(user, 'admin')) {
    return <Navigate to='/login' replace />;
  }

  return (
    <MainLayout>
      <Helmet>
        <title>Tableau de bord de sécurité | Retreat and Be</title>
        <meta
          name='description'
          content='Tableau de bord de sécurité pour surveiller et gérer la sécurité de la plateforme Retreat and Be.'
        />
      </Helmet>

      <SecurityDashboard />
    </MainLayout>
  );
};

export default SecurityPage;
