import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from '../hooks/useTranslation';
import { useAuthContext } from '../hooks/useAuthContext';
import { analyticsService } from '../services/api/analyticsService';
import { MetricsChart } from '../components/analytics/MetricsChart';

const ContentDetailAnalyticsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const navigate = useNavigate();
  const { creatorId, contentId } = useParams<{ creatorId: string; contentId: string }>();
  
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [metrics, setMetrics] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<{ startDate?: string; endDate?: string }>({});

  useEffect(() => {
    if (!creatorId || !contentId) {
      setError(t('analytics.content.missingParams'));
      setIsLoading(false);
      return;
    }

    fetchContentMetrics();
  }, [creatorId, contentId, dateRange]);

  const fetchContentMetrics = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await analyticsService.getContentMetrics(creatorId, contentId, dateRange);
      setMetrics(response);
    } catch (error) {
      console.error('Error fetching content metrics:', error);
      setError(t('analytics.content.fetchError'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleDateRangeChange = (range: { startDate?: string; endDate?: string }) => {
    setDateRange(range);
  };

  const handleBackClick = () => {
    navigate(`/analytics/${creatorId}`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <h2 className="text-lg font-medium">{t('common.error')}</h2>
          <p>{error}</p>
        </div>
        <button
          onClick={handleBackClick}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          {t('common.back')}
        </button>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12 text-gray-500">
          {t('analytics.content.noData')}
        </div>
        <button
          onClick={handleBackClick}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          {t('common.back')}
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <button
            onClick={handleBackClick}
            className="mb-4 flex items-center text-blue-600 hover:text-blue-800"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            {t('common.back')}
          </button>
          <h1 className="text-3xl font-bold">{t('analytics.content.title')}</h1>
          <p className="text-gray-600 mt-2">{t('analytics.content.subtitle', { contentType: metrics.engagement.contentType })}</p>
        </div>
        
        <div className="flex space-x-2 mt-4 md:mt-0">
          <button
            onClick={() => handleDateRangeChange({ startDate: undefined, endDate: undefined })}
            className={`px-3 py-2 text-sm rounded-md ${
              !dateRange.startDate && !dateRange.endDate
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {t('analytics.filters.allTime')}
          </button>
          
          <button
            onClick={() => {
              const date = new Date();
              date.setDate(date.getDate() - 30);
              handleDateRangeChange({
                startDate: date.toISOString().split('T')[0],
                endDate: new Date().toISOString().split('T')[0],
              });
            }}
            className={`px-3 py-2 text-sm rounded-md ${
              dateRange.startDate && new Date(dateRange.startDate).getDate() === new Date().getDate() - 30
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {t('analytics.filters.last30Days')}
          </button>
          
          <button
            onClick={() => {
              const date = new Date();
              date.setDate(date.getDate() - 7);
              handleDateRangeChange({
                startDate: date.toISOString().split('T')[0],
                endDate: new Date().toISOString().split('T')[0],
              });
            }}
            className={`px-3 py-2 text-sm rounded-md ${
              dateRange.startDate && new Date(dateRange.startDate).getDate() === new Date().getDate() - 7
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {t('analytics.filters.last7Days')}
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.content.views')}</h3>
          <p className="text-2xl font-bold mt-2">{metrics.engagement.summary.totalViews.toLocaleString()}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.content.engagementRate')}</h3>
          <p className="text-2xl font-bold mt-2">{metrics.engagement.summary.engagementRate.toFixed(2)}%</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.content.interactions')}</h3>
          <p className="text-2xl font-bold mt-2">
            {(
              metrics.engagement.summary.totalLikes +
              metrics.engagement.summary.totalComments +
              metrics.engagement.summary.totalShares
            ).toLocaleString()}
          </p>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-500">{t('analytics.content.revenue')}</h3>
          <p className="text-2xl font-bold mt-2">
            {metrics.revenue.totalRevenue.toLocaleString()} €
          </p>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-medium mb-4">{t('analytics.content.viewsOverTime')}</h3>
        <MetricsChart
          data={metrics.engagement.timeSeries}
          dataKeys={['views']}
          xAxisDataKey="date"
          height={300}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('analytics.content.engagementOverTime')}</h3>
          <MetricsChart
            data={metrics.engagement.timeSeries}
            dataKeys={['likes', 'comments', 'shares']}
            xAxisDataKey="date"
            height={300}
          />
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium mb-4">{t('analytics.content.revenueOverTime')}</h3>
          <MetricsChart
            data={metrics.revenue.timeSeries}
            dataKeys={['amount']}
            xAxisDataKey="date"
            height={300}
          />
        </div>
      </div>
    </div>
  );
};

export default ContentDetailAnalyticsPage;
