import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { UserCircleIcon, LockClosedIcon, EnvelopeIcon } from '@heroicons/react/24/outline';

const ProfessionalHomePage: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div className='min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8'>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className='max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-lg'
      >
        <div>
          <h2 className='mt-6 text-center text-3xl font-extrabold text-gray-900'>
            {isLogin ? 'Connexion' : 'Inscription'} Espace Professionnel
          </h2>
          <p className='mt-2 text-center text-sm text-gray-600'>
            {isLogin ? 'Pas encore de compte ? ' : 'Déjà un compte ? '}
            <button
              onClick={() => setIsLogin(!isLogin)}
              className='font-medium text-primary hover:text-opacity-80'
            >
              {isLogin ? "S'inscrire" : 'Se connecter'}
            </button>
          </p>
        </div>
        <form className='mt-8 space-y-6'>
          <div className='rounded-md shadow-sm space-y-4'>
            <div>
              <label htmlFor='email' className='sr-only'>
                Adresse email
              </label>
              <div className='relative'>
                <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                  <EnvelopeIcon className='h-5 w-5 text-gray-400' />
                </div>
                <input
                  id='email'
                  name='email'
                  type='email'
                  required
                  className='appearance-none rounded-lg relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm'
                  placeholder='Adresse email'
                />
              </div>
            </div>
            {!isLogin && (
              <div>
                <label htmlFor='name' className='sr-only'>
                  Nom complet
                </label>
                <div className='relative'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    <UserCircleIcon className='h-5 w-5 text-gray-400' />
                  </div>
                  <input
                    id='name'
                    name='name'
                    type='text'
                    required
                    className='appearance-none rounded-lg relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm'
                    placeholder='Nom complet'
                  />
                </div>
              </div>
            )}
            <div>
              <label htmlFor='password' className='sr-only'>
                Mot de passe
              </label>
              <div className='relative'>
                <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                  <LockClosedIcon className='h-5 w-5 text-gray-400' />
                </div>
                <input
                  id='password'
                  name='password'
                  type='password'
                  required
                  className='appearance-none rounded-lg relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm'
                  placeholder='Mot de passe'
                />
              </div>
            </div>
          </div>

          <div>
            <button
              type='submit'
              className='group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary'
            >
              {isLogin ? 'Se connecter' : "S'inscrire"}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default ProfessionalHomePage;
