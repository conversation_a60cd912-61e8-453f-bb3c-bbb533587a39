import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../hooks/useAuthContext';
import { contextualRecommendationService, UserContext } from '../services/api/contextualRecommendationService';
import { toast } from 'react-toastify';
import { t } from '../services/i18n/i18nService';
import NavBar from '../components/organisms/NavBar/NavBar';
import Footer from '../components/organisms/Footer/Footer';
import RetreatCard from '../components/retreats/RetreatCard';
import LoadingSpinner from '../components/common/LoadingSpinner';

/**
 * Page pour afficher les recommandations contextuelles
 */
const ContextualRecommendationPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [userContext, setUserContext] = useState<UserContext | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [contextFactor, setContextFactor] = useState<number>(0.7);
  const [selectedContextTypes, setSelectedContextTypes] = useState<Array<'location' | 'weather' | 'season' | 'time' | 'events' | 'cultural'>>([
    'location', 'weather', 'season', 'time', 'events', 'cultural',
  ]);
  
  // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
  useEffect(() => {
    if (!user) {
      navigate('/login', { state: { from: '/contextual' } });
    }
  }, [user, navigate]);
  
  // Charger le contexte utilisateur et les recommandations contextuelles
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Récupérer le contexte utilisateur
        const context = await contextualRecommendationService.getMyContext();
        setUserContext(context);
        
        // Récupérer les recommandations contextuelles
        const contextualRecommendations = await contextualRecommendationService.getContextualRecommendations({
          contextFactor,
          maxRecommendations: 12,
          contextTypes: selectedContextTypes,
        });
        
        setRecommendations(contextualRecommendations);
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations contextuelles:', error);
        setError(t('contextual.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (user) {
      loadData();
    }
  }, [user, contextFactor, selectedContextTypes]);
  
  // Gérer le changement de facteur de contexte
  const handleContextFactorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setContextFactor(parseFloat(e.target.value));
  };
  
  // Gérer le changement de types de contexte
  const handleContextTypeChange = (type: 'location' | 'weather' | 'season' | 'time' | 'events' | 'cultural') => {
    if (selectedContextTypes.includes(type)) {
      setSelectedContextTypes(selectedContextTypes.filter(t => t !== type));
    } else {
      setSelectedContextTypes([...selectedContextTypes, type]);
    }
  };
  
  // Gérer le rafraîchissement des recommandations
  const handleRefresh = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Récupérer le contexte utilisateur
      const context = await contextualRecommendationService.getMyContext(true);
      setUserContext(context);
      
      // Récupérer les recommandations contextuelles
      const contextualRecommendations = await contextualRecommendationService.getContextualRecommendations({
        contextFactor,
        maxRecommendations: 12,
        contextTypes: selectedContextTypes,
      });
      
      setRecommendations(contextualRecommendations);
      
      toast.success(t('contextual.refreshSuccess'));
    } catch (error) {
      console.error('Erreur lors du rafraîchissement des recommandations contextuelles:', error);
      setError(t('contextual.refreshError'));
      toast.error(t('contextual.refreshError'));
    } finally {
      setLoading(false);
    }
  };
  
  // Générer un résumé du contexte utilisateur
  const getContextSummary = () => {
    if (!userContext) {
      return null;
    }
    
    return (
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('contextual.yourContext')}</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {userContext.location && (
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center mb-2">
                <svg className="h-5 w-5 text-gray-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">{t('contextual.location')}</span>
              </div>
              <p className="text-gray-700">{userContext.location.city}, {userContext.location.country}</p>
            </div>
          )}
          
          {userContext.weather && (
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center mb-2">
                <svg className="h-5 w-5 text-gray-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z" />
                </svg>
                <span className="font-medium">{t('contextual.weather')}</span>
              </div>
              <p className="text-gray-700">
                {userContext.weather.condition}, {Math.round(userContext.weather.temperature)}°C
              </p>
            </div>
          )}
          
          {userContext.seasonData && (
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center mb-2">
                <svg className="h-5 w-5 text-gray-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">{t('contextual.season')}</span>
              </div>
              <p className="text-gray-700">
                {contextualRecommendationService.getSeasonName(userContext.seasonData.currentSeason)}
              </p>
            </div>
          )}
          
          {userContext.localTime && (
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center mb-2">
                <svg className="h-5 w-5 text-gray-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">{t('contextual.time')}</span>
              </div>
              <p className="text-gray-700">
                {userContext.localTime.hour}:{userContext.localTime.minute < 10 ? '0' : ''}{userContext.localTime.minute},
                {' '}{t(`contextual.timeOfDay.${userContext.localTime.timeOfDay}`)}
              </p>
            </div>
          )}
          
          {userContext.localEvents && userContext.localEvents.length > 0 && (
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center mb-2">
                <svg className="h-5 w-5 text-gray-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">{t('contextual.events')}</span>
              </div>
              <p className="text-gray-700">
                {userContext.localEvents.length} {t('contextual.eventsNearby')}
              </p>
            </div>
          )}
          
          {userContext.culturalData && userContext.culturalData.holidays.length > 0 && (
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center mb-2">
                <svg className="h-5 w-5 text-gray-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                </svg>
                <span className="font-medium">{t('contextual.cultural')}</span>
              </div>
              <p className="text-gray-700">
                {userContext.culturalData.holidays.length} {t('contextual.upcomingHolidays')}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>{t('contextual.pageTitle')} | Retreat And Be</title>
        <meta
          name="description"
          content={t('contextual.pageDescription')}
        />
      </Helmet>
      
      <NavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('contextual.title')}</h1>
            <p className="text-lg text-gray-600">{t('contextual.description')}</p>
          </div>
          
          {getContextSummary()}
          
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('contextual.preferences')}</h2>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('contextual.contextFactor')} ({Math.round(contextFactor * 100)}%)
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={contextFactor}
                onChange={handleContextFactorChange}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{t('contextual.lessContextual')}</span>
                <span>{t('contextual.moreContextual')}</span>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('contextual.contextTypes')}
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedContextTypes.includes('location')}
                    onChange={() => handleContextTypeChange('location')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('contextual.location')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedContextTypes.includes('weather')}
                    onChange={() => handleContextTypeChange('weather')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('contextual.weather')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedContextTypes.includes('season')}
                    onChange={() => handleContextTypeChange('season')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('contextual.season')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedContextTypes.includes('time')}
                    onChange={() => handleContextTypeChange('time')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('contextual.time')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedContextTypes.includes('events')}
                    onChange={() => handleContextTypeChange('events')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('contextual.events')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedContextTypes.includes('cultural')}
                    onChange={() => handleContextTypeChange('cultural')}
                    className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('contextual.cultural')}</span>
                </label>
              </div>
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={handleRefresh}
                className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
              >
                {t('contextual.refresh')}
              </button>
            </div>
          </div>
          
          {/* Contenu principal */}
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          ) : error ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-center">
                <button
                  onClick={handleRefresh}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  {t('contextual.tryAgain')}
                </button>
              </div>
            </div>
          ) : recommendations.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center py-12">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('contextual.noRecommendations')}</h3>
                <p className="text-gray-500 mb-6">{t('contextual.tryDifferentFilters')}</p>
                
                <button
                  onClick={handleRefresh}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  {t('contextual.refresh')}
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recommendations.map((recommendation) => (
                <RetreatCard
                  key={recommendation.id}
                  retreat={recommendation}
                  isContextual={true}
                  contextScore={recommendation.contextScore}
                />
              ))}
            </div>
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ContextualRecommendationPage;
