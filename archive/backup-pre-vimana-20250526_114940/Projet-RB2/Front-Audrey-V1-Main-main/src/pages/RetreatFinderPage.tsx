import React from 'react';
import { motion } from 'framer-motion';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import ScrollToTop from '../components/ui/ScrollToTop';
import IdealRetreatFinder, { SelectionsType } from '../components/RetreatFinder/IdealRetreatFinder';

const RetreatFinderPage: React.FC = () => {
  const handleSearchComplete = (selections: SelectionsType) => {
    // Ici, vous pouvez gérer la redirection vers les résultats
    // ou effectuer d'autres actions avec les sélections
    console.log('Search completed with:', selections);
    // navigate('/search-results', { state: { selections } });
  };

  return (
    <div className='min-h-screen bg-gray-100 flex flex-col'>
      {/* Header */}
      <header className='fixed top-0 left-0 right-0 bg-white shadow-sm z-50'>
        <NavBarClient />
      </header>

      {/* Main Content */}
      <main className='flex-grow pt-20 pb-12'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          {/* Hero Section */}
          <section className='text-center mb-12'>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className='text-4xl font-bold text-retreat-green mb-4'>
                Trouvez votre retraite idéale
              </h1>
              <p className='text-xl text-gray-600 max-w-2xl mx-auto'>
                Laissez-vous guider à travers nos questions pour découvrir la retraite parfaitement
                adaptée à vos besoins et aspirations.
              </p>
            </motion.div>
          </section>

          {/* Finder Section */}
          <section className='bg-white rounded-2xl shadow-xl p-6 mb-12'>
            <IdealRetreatFinder onComplete={handleSearchComplete} />
          </section>

          {/* Additional Information */}
          <section className='grid grid-cols-1 md:grid-cols-3 gap-8 mb-12'>
            <div className='bg-white p-6 rounded-xl shadow-md'>
              <h3 className='text-xl font-semibold text-retreat-green mb-3'>
                Expertise personnalisée
              </h3>
              <p className='text-gray-600'>
                Notre algorithme prend en compte vos préférences pour vous proposer les meilleures
                options.
              </p>
            </div>
            <div className='bg-white p-6 rounded-xl shadow-md'>
              <h3 className='text-xl font-semibold text-retreat-green mb-3'>Large sélection</h3>
              <p className='text-gray-600'>
                Accédez à notre catalogue complet de retraites vérifiées et sélectionnées avec soin.
              </p>
            </div>
            <div className='bg-white p-6 rounded-xl shadow-md'>
              <h3 className='text-xl font-semibold text-retreat-green mb-3'>
                Garantie satisfaction
              </h3>
              <p className='text-gray-600'>
                Nous vous accompagnons dans votre choix pour une expérience qui vous correspond
                vraiment.
              </p>
            </div>
          </section>
        </div>
      </main>

      {/* Footer */}
      <Footer />

      {/* Utilities */}
      <ScrollToTop />
    </div>
  );
};

export default RetreatFinderPage;
