import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { partnerService, PartnerDocument } from '../services/api/partnerService';
import { useAuthContext } from '../hooks/useAuthContext';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';
import DocumentUpload from '../components/partners/DocumentUpload';
import DocumentList from '../components/partners/DocumentList';

const PartnerStatusPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuthContext();
  const [partner, setPartner] = useState<any>(null);
  const [documents, setDocuments] = useState<PartnerDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'status' | 'documents'>('status');

  useEffect(() => {
    const fetchPartnerStatus = async () => {
      try {
        if (!id) {
          setError('ID du partenaire non fourni');
          setLoading(false);
          return;
        }

        const partnerData = await partnerService.getPartnerById(id);
        setPartner(partnerData);

        // Fetch documents if user is authenticated
        if (user) {
          try {
            const documentsData = await partnerService.getDocuments(id);
            setDocuments(documentsData);
          } catch (docErr) {
            console.error('Error fetching partner documents:', docErr);
            // Don't set error for documents, just log it
          }
        }
      } catch (err) {
        console.error('Error fetching partner status:', err);
        setError('Impossible de récupérer les informations du partenaire');
      } finally {
        setLoading(false);
      }
    };

    fetchPartnerStatus();
  }, [id, user]);

  // Refresh documents after adding or deleting
  const refreshDocuments = async () => {
    if (id && user) {
      try {
        const documentsData = await partnerService.getDocuments(id);
        setDocuments(documentsData);
      } catch (err) {
        console.error('Error refreshing documents:', err);
      }
    }
  };

  // Helper function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'SUSPENDED':
        return 'bg-red-100 text-red-800';
      case 'TERMINATED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  // Helper function to get status text in French
  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'En attente';
      case 'ACTIVE':
        return 'Actif';
      case 'SUSPENDED':
        return 'Suspendu';
      case 'TERMINATED':
        return 'Résilié';
      default:
        return status;
    }
  };

  // Helper function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <>
      <Helmet>
        <title>Statut Partenaire | Retreat And Be</title>
        <meta
          name="description"
          content="Consultez le statut de votre demande de partenariat avec Retreat And Be."
        />
      </Helmet>

      <header className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <NavBarClient />
      </header>

      <main className="pt-24 pb-12">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Statut de votre demande</h1>
            <p className="mt-2 text-lg text-gray-600">
              Consultez l'état de votre demande de partenariat
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-8">
            {/* Tabs */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('status')}
                  className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'status'
                      ? 'border-retreat-green text-retreat-green'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Statut
                </button>
                <button
                  onClick={() => setActiveTab('documents')}
                  className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'documents'
                      ? 'border-retreat-green text-retreat-green'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Documents
                </button>
              </nav>
            </div>
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <div className="text-red-500 text-lg mb-4">{error}</div>
                <Link
                  to="/become-partner"
                  className="inline-block px-6 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark transition-colors"
                >
                  Retour à l'inscription
                </Link>
              </div>
            ) : partner ? (
              <>
                {/* Status Tab */}
                {activeTab === 'status' && (
                  <div className="space-y-8">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900">{partner.companyName}</h2>
                        <p className="text-gray-600">ID: {partner.id}</p>
                      </div>
                      <div className="mt-4 md:mt-0">
                        <span
                          className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                            partner.status
                          )}`}
                        >
                          {getStatusText(partner.status)}
                        </span>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 pt-6">
                      <h3 className="text-lg font-semibold mb-4">Informations de la demande</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Type de partenaire</p>
                          <p className="font-medium">{partner.type}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Catégorie</p>
                          <p className="font-medium">{partner.category}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Date de soumission</p>
                          <p className="font-medium">{formatDate(partner.createdAt)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Dernière mise à jour</p>
                          <p className="font-medium">{formatDate(partner.updatedAt)}</p>
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 pt-6">
                      <h3 className="text-lg font-semibold mb-4">Étapes du processus</h3>
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <div
                            className={`flex-shrink-0 h-5 w-5 rounded-full ${
                              true ? 'bg-retreat-green' : 'bg-gray-200'
                            } mt-1`}
                          ></div>
                          <div className="ml-3">
                            <p className="font-medium">Soumission de la demande</p>
                            <p className="text-sm text-gray-500">
                              Votre demande a été soumise avec succès.
                            </p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <div
                            className={`flex-shrink-0 h-5 w-5 rounded-full ${
                              partner.status !== 'PENDING' ? 'bg-retreat-green' : 'bg-gray-200'
                            } mt-1`}
                          ></div>
                          <div className="ml-3">
                            <p className="font-medium">Vérification des informations</p>
                            <p className="text-sm text-gray-500">
                              Notre équipe examine votre demande.
                            </p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <div
                            className={`flex-shrink-0 h-5 w-5 rounded-full ${
                              partner.status === 'ACTIVE' ? 'bg-retreat-green' : 'bg-gray-200'
                            } mt-1`}
                          ></div>
                          <div className="ml-3">
                            <p className="font-medium">Approbation</p>
                            <p className="text-sm text-gray-500">
                              Votre demande est approuvée et votre compte partenaire est actif.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {partner.status === 'PENDING' && (
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-blue-800">
                          Votre demande est en cours d'examen. Nous vous contacterons bientôt pour la
                          suite du processus. Merci de votre patience.
                        </p>
                        <p className="text-blue-800 mt-2">
                          Pour accélérer le processus de vérification, veuillez ajouter les documents
                          requis dans l'onglet "Documents".
                        </p>
                      </div>
                    )}

                    {partner.status === 'ACTIVE' && (
                      <div className="bg-green-50 p-4 rounded-lg">
                        <p className="text-green-800">
                          Félicitations ! Votre demande a été approuvée. Vous pouvez maintenant accéder
                          à votre espace partenaire.
                        </p>
                        <div className="mt-4">
                          <Link
                            to="/partner-dashboard"
                            className="inline-block px-6 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark transition-colors"
                          >
                            Accéder à mon espace partenaire
                          </Link>
                        </div>
                      </div>
                    )}

                    {partner.status === 'SUSPENDED' && (
                      <div className="bg-red-50 p-4 rounded-lg">
                        <p className="text-red-800">
                          Votre compte partenaire est actuellement suspendu. Veuillez nous contacter
                          pour plus d'informations.
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Documents Tab */}
                {activeTab === 'documents' && (
                  <div className="space-y-8">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900">Documents</h2>
                        <p className="text-gray-600">
                          Gérez les documents liés à votre demande de partenariat
                        </p>
                      </div>
                    </div>

                    {user && partner.userId === user.id && (
                      <DocumentUpload partnerId={partner.id} onDocumentAdded={refreshDocuments} />
                    )}

                    <div className="mt-8">
                      <h3 className="text-lg font-semibold mb-4">Documents soumis</h3>
                      <DocumentList documents={documents} onDocumentDeleted={refreshDocuments} />
                    </div>

                    {partner.status === 'PENDING' && (
                      <div className="bg-yellow-50 p-4 rounded-lg mt-6">
                        <h4 className="font-medium text-yellow-800">Documents requis</h4>
                        <ul className="mt-2 list-disc list-inside text-yellow-700">
                          <li>Pièce d'identité du représentant légal</li>
                          <li>Extrait Kbis ou équivalent (moins de 3 mois)</li>
                          <li>Attestation d'assurance professionnelle</li>
                          <li>Certifications professionnelles (si applicable)</li>
                          <li>RIB pour les paiements</li>
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500 text-lg mb-4">
                  Aucune information trouvée pour ce partenaire
                </div>
                <Link
                  to="/become-partner"
                  className="inline-block px-6 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark transition-colors"
                >
                  Retour à l'inscription
                </Link>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </>
  );
};

export default PartnerStatusPage;
