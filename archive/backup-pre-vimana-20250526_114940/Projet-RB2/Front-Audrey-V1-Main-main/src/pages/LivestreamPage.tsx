import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-toastify';
import { livestreamService } from '../services/api/livestreamService';
import { socialAnalyticsService } from '../services/api/socialAnalyticsService';
import { useAuthContext } from '../hooks/useAuthContext';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import LoadingSpinner from '../components/atoms/LoadingSpinner/LoadingSpinner';

const LivestreamPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [livestream, setLivestream] = useState<any>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSendingMessage, setIsSendingMessage] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagePollingInterval = useRef<NodeJS.Timeout | null>(null);

  // Charger les détails du livestream
  useEffect(() => {
    const fetchLivestreamDetails = async () => {
      if (!id) return;
      
      try {
        setIsLoading(true);
        setError(null);
        
        const data = await livestreamService.getLivestreamById(id);
        setLivestream(data);
        
        // Enregistrer l'événement de vue
        socialAnalyticsService.trackView(id, 'livestream');
      } catch (error) {
        console.error('Error fetching livestream details:', error);
        setError('Une erreur est survenue lors du chargement du livestream.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLivestreamDetails();
    
    // Nettoyage
    return () => {
      if (messagePollingInterval.current) {
        clearInterval(messagePollingInterval.current);
      }
    };
  }, [id]);

  // Charger les messages et configurer le polling
  useEffect(() => {
    if (!id || !livestream) return;
    
    const fetchMessages = async () => {
      try {
        const data = await livestreamService.getLivestreamMessages(id);
        setMessages(data);
        
        // Faire défiler jusqu'au dernier message
        scrollToBottom();
      } catch (error) {
        console.error('Error fetching livestream messages:', error);
      }
    };
    
    // Charger les messages initiaux
    fetchMessages();
    
    // Configurer le polling pour les nouveaux messages
    messagePollingInterval.current = setInterval(fetchMessages, 5000);
    
    // Nettoyage
    return () => {
      if (messagePollingInterval.current) {
        clearInterval(messagePollingInterval.current);
      }
    };
  }, [id, livestream]);

  // Faire défiler jusqu'au dernier message
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Envoyer un message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!id || !newMessage.trim()) return;
    
    try {
      setIsSendingMessage(true);
      
      await livestreamService.sendLivestreamMessage(id, {
        content: newMessage,
        type: 'text',
      });
      
      // Enregistrer l'événement d'engagement
      socialAnalyticsService.trackEngagement(id, 'livestream', 'comment');
      
      // Réinitialiser le champ de message
      setNewMessage('');
      
      // Recharger les messages
      const data = await livestreamService.getLivestreamMessages(id);
      setMessages(data);
      
      // Faire défiler jusqu'au dernier message
      scrollToBottom();
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Erreur lors de l\'envoi du message');
    } finally {
      setIsSendingMessage(false);
    }
  };

  // Démarrer le livestream
  const handleStartLivestream = async () => {
    if (!id) return;
    
    try {
      await livestreamService.startLivestream(id);
      
      // Recharger les détails du livestream
      const data = await livestreamService.getLivestreamById(id);
      setLivestream(data);
      
      toast.success('Livestream démarré avec succès');
    } catch (error) {
      console.error('Error starting livestream:', error);
      toast.error('Erreur lors du démarrage du livestream');
    }
  };

  // Terminer le livestream
  const handleEndLivestream = async () => {
    if (!id) return;
    
    try {
      await livestreamService.endLivestream(id);
      
      // Recharger les détails du livestream
      const data = await livestreamService.getLivestreamById(id);
      setLivestream(data);
      
      toast.success('Livestream terminé avec succès');
    } catch (error) {
      console.error('Error ending livestream:', error);
      toast.error('Erreur lors de la fin du livestream');
    }
  };

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <>
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner />
          </div>
        </div>
        <Footer />
      </>
    );
  }

  if (error || !livestream) {
    return (
      <>
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-red-600 p-4">
            <p>{error || 'Livestream non trouvé'}</p>
            <button
              className="mt-2 text-retreat-green hover:text-retreat-green-dark"
              onClick={() => navigate('/livestreams')}
            >
              Retour aux livestreams
            </button>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  const isHost = user?.id === livestream.hostId;
  const isLive = livestream.status === 'live';
  const isScheduled = livestream.status === 'scheduled';
  const isEnded = livestream.status === 'ended' || livestream.status === 'cancelled';

  return (
    <>
      <Helmet>
        <title>{livestream.title} | Retreat And Be</title>
        <meta name="description" content={livestream.description} />
      </Helmet>
      
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">{livestream.title}</h1>
          <div className="flex items-center mt-2 text-sm text-gray-500">
            <span className="mr-4">
              {livestream.hostName}
            </span>
            <span>
              {formatDate(livestream.scheduledStartTime || livestream.createdAt)}
            </span>
            <span className={`ml-4 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              isLive ? 'bg-red-100 text-red-800' :
              isScheduled ? 'bg-blue-100 text-blue-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {isLive ? 'En direct' : isScheduled ? 'Programmé' : 'Terminé'}
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Vidéo */}
          <div className="lg:col-span-2">
            <div className="bg-black rounded-lg overflow-hidden aspect-video">
              {isLive ? (
                <iframe
                  src={livestream.streamUrl}
                  className="w-full h-full"
                  allowFullScreen
                  allow="autoplay; encrypted-media"
                ></iframe>
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-800 text-white">
                  {isScheduled ? (
                    <div className="text-center">
                      <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="mt-2">Ce livestream n'a pas encore commencé</p>
                      <p className="text-sm text-gray-400">Programmé pour le {formatDate(livestream.scheduledStartTime)}</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                      </svg>
                      <p className="mt-2">Ce livestream est terminé</p>
                      {livestream.recordingUrl && (
                        <a
                          href={livestream.recordingUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="mt-2 inline-block text-retreat-green hover:text-retreat-green-dark"
                        >
                          Voir l'enregistrement
                        </a>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
            
            <div className="mt-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">À propos</h2>
              <p className="text-gray-700 whitespace-pre-line">{livestream.description}</p>
            </div>
            
            {isHost && (
              <div className="mt-6 flex space-x-4">
                {isScheduled && (
                  <button
                    onClick={handleStartLivestream}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Démarrer le livestream
                  </button>
                )}
                
                {isLive && (
                  <button
                    onClick={handleEndLivestream}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  >
                    <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                    </svg>
                    Terminer le livestream
                  </button>
                )}
              </div>
            )}
          </div>
          
          {/* Chat */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col h-[600px]">
            <div className="p-4 bg-retreat-green text-white font-medium">
              Chat en direct
            </div>
            
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.length === 0 ? (
                <div className="text-center text-gray-500 py-4">
                  Aucun message pour le moment
                </div>
              ) : (
                messages.map((message) => (
                  <div key={message.id} className="flex items-start space-x-2">
                    <div className="flex-shrink-0 h-8 w-8 rounded-full bg-retreat-green-light flex items-center justify-center text-retreat-green font-medium">
                      {message.userName.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="flex items-baseline">
                        <span className="font-medium text-gray-900">{message.userName}</span>
                        <span className="ml-2 text-xs text-gray-500">
                          {new Date(message.timestamp).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                      <p className="text-gray-700">{message.content}</p>
                    </div>
                  </div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <form onSubmit={handleSendMessage} className="flex">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Écrivez un message..."
                  className="flex-1 rounded-l-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
                  disabled={!isLive || isSendingMessage}
                />
                <button
                  type="submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!isLive || !newMessage.trim() || isSendingMessage}
                >
                  {isSendingMessage ? (
                    <LoadingSpinner size="small" color="white" />
                  ) : (
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  )}
                </button>
              </form>
              {!isLive && (
                <p className="mt-2 text-xs text-gray-500 text-center">
                  {isScheduled ? 'Le chat sera disponible une fois le livestream commencé' : 'Le livestream est terminé, le chat est désactivé'}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </>
  );
};

export default LivestreamPage;
