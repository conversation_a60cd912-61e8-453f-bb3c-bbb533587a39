import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../hooks/useAuthContext';
import Navbar from '../components/organisms/Navbar/Navbar';
import Footer from '../components/organisms/Footer/Footer';
import SocialAnalyticsDashboard from '../components/social/SocialAnalyticsDashboard';
import LivestreamList from '../components/social/LivestreamList';
import BlogPostList from '../components/social/BlogPostList';

const SocialAnalyticsPage: React.FC = () => {
  const { user } = useAuthContext();
  const [activeTab, setActiveTab] = useState<string>('analytics');
  
  // Vérifier si l'utilisateur est administrateur
  const isAdmin = user?.role === 'ADMIN';
  
  // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
  if (!user) {
    window.location.href = '/login?redirect=/social/analytics';
    return null;
  }

  return (
    <>
      <Helmet>
        <title>Analyses sociales | Retreat And Be</title>
        <meta name="description" content="Tableau de bord d'analyses sociales pour Retreat And Be" />
      </Helmet>
      
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Tableau de bord social</h1>
          <p className="mt-2 text-gray-600">
            Suivez les performances de votre contenu social et analysez les tendances d'engagement.
          </p>
        </div>
        
        {/* Onglets */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('analytics')}
              className={`${
                activeTab === 'analytics'
                  ? 'border-retreat-green text-retreat-green'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Analyses
            </button>
            <button
              onClick={() => setActiveTab('livestreams')}
              className={`${
                activeTab === 'livestreams'
                  ? 'border-retreat-green text-retreat-green'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Livestreams
            </button>
            <button
              onClick={() => setActiveTab('blog')}
              className={`${
                activeTab === 'blog'
                  ? 'border-retreat-green text-retreat-green'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Blog
            </button>
            {isAdmin && (
              <button
                onClick={() => setActiveTab('popular')}
                className={`${
                  activeTab === 'popular'
                    ? 'border-retreat-green text-retreat-green'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Contenu populaire
              </button>
            )}
          </nav>
        </div>
        
        {/* Contenu des onglets */}
        <div className="mt-6">
          {activeTab === 'analytics' && (
            <SocialAnalyticsDashboard
              userId={!isAdmin ? user.id : undefined}
              isAdmin={isAdmin}
            />
          )}
          
          {activeTab === 'livestreams' && (
            <div className="space-y-12">
              {/* Livestreams en direct */}
              <div>
                <LivestreamList
                  status="live"
                  showCreateButton={true}
                  onCreateClick={() => {
                    // Rediriger vers la page de création de livestream
                    window.location.href = '/livestreams/create';
                  }}
                />
              </div>
              
              {/* Livestreams programmés */}
              <div>
                <LivestreamList
                  status="scheduled"
                />
              </div>
              
              {/* Mes livestreams */}
              <div>
                <LivestreamList
                  hostId={user.id}
                  showCreateButton={true}
                  onCreateClick={() => {
                    // Rediriger vers la page de création de livestream
                    window.location.href = '/livestreams/create';
                  }}
                />
              </div>
            </div>
          )}
          
          {activeTab === 'blog' && (
            <div className="space-y-12">
              {/* Articles publiés */}
              <div>
                <BlogPostList
                  status="published"
                  showCreateButton={true}
                  onCreateClick={() => {
                    // Rediriger vers la page de création d'article
                    window.location.href = '/blog/create';
                  }}
                />
              </div>
              
              {/* Mes articles */}
              <div>
                <BlogPostList
                  authorId={user.id}
                  showCreateButton={true}
                  onCreateClick={() => {
                    // Rediriger vers la page de création d'article
                    window.location.href = '/blog/create';
                  }}
                />
              </div>
              
              {/* Brouillons (si l'utilisateur est auteur) */}
              <div>
                <BlogPostList
                  authorId={user.id}
                  status="draft"
                />
              </div>
            </div>
          )}
          
          {activeTab === 'popular' && isAdmin && (
            <div className="space-y-8">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Contenu populaire</h2>
                <p className="text-gray-600 mb-4">
                  Analyse du contenu le plus populaire sur la plateforme, basée sur les vues, les likes et les commentaires.
                </p>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Titre
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Auteur
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vues
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Engagement
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {/* Données de contenu populaire */}
                      {/* Ces données seraient normalement chargées depuis l'API */}
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          Comment méditer efficacement
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          Blog
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          Marie Dupont
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          1,245
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          87
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          Yoga pour débutants
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          Livestream
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          Jean Martin
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          982
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          65
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <Footer />
    </>
  );
};

export default SocialAnalyticsPage;
