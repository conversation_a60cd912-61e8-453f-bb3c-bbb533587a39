/**
 * Page Dashboard - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Dashboard principal utilisant le design system unifié
 * pour démontrer l'intégration des composants.
 */

import React, { useState } from 'react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  StatsCard,
  RetreatCard,
  ProfessionalCard,
  Button,
  Input,
  SearchInput,
  Table,
  useToast,
  useModal,
  Modal,
  ModalContent,
  ModalFooter,
  Alert,
} from '../components/ui/design-system';
import { useAuth } from '../store/globalStore';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const welcomeModal = useModal();
  const [searchQuery, setSearchQuery] = useState('');

  // Données d'exemple
  const stats = [
    {
      title: 'Retraites réservées',
      value: '12',
      change: { value: 15, type: 'increase' as const },
      icon: '🧘‍♀️',
      description: 'ce mois',
    },
    {
      title: 'Heures de méditation',
      value: '48h',
      change: { value: 8, type: 'increase' as const },
      icon: '⏰',
      description: 'cette semaine',
    },
    {
      title: 'Points bien-être',
      value: '2,847',
      change: { value: 12, type: 'increase' as const },
      icon: '⭐',
      description: 'total',
    },
    {
      title: 'Économies réalisées',
      value: '€450',
      change: { value: 5, type: 'decrease' as const },
      icon: '💰',
      description: 'vs prix normal',
    },
  ];

  const featuredRetreats = [
    {
      id: '1',
      title: 'Retraite Yoga & Méditation en Provence',
      description: 'Une expérience transformatrice au cœur de la nature provençale avec des maîtres reconnus.',
      image: '/api/placeholder/400/300',
      price: 450,
      duration: '3 jours',
      location: 'Provence, France',
      rating: 4.8,
      category: 'Yoga',
    },
    {
      id: '2',
      title: 'Détox Digital en Bretagne',
      description: 'Reconnectez-vous avec vous-même loin des écrans et du stress urbain.',
      image: '/api/placeholder/400/300',
      price: 320,
      duration: '2 jours',
      location: 'Bretagne, France',
      rating: 4.6,
      category: 'Détox',
    },
  ];

  const recommendedProfessionals = [
    {
      id: '1',
      name: 'Marie Dubois',
      title: 'Professeure de Yoga certifiée',
      avatar: '/api/placeholder/100/100',
      specialties: ['Hatha Yoga', 'Méditation', 'Pranayama'],
      rating: 4.9,
      reviewCount: 127,
      price: 75,
      available: true,
    },
    {
      id: '2',
      name: 'Jean Martin',
      title: 'Thérapeute en méditation',
      avatar: '/api/placeholder/100/100',
      specialties: ['Mindfulness', 'MBSR', 'Méditation guidée'],
      rating: 4.8,
      reviewCount: 89,
      price: 85,
      available: false,
    },
  ];

  const recentActivities = [
    {
      id: '1',
      type: 'booking',
      title: 'Réservation confirmée',
      description: 'Retraite Yoga Provence - 15-17 Juin',
      timestamp: '2025-05-24T10:30:00Z',
      status: 'success',
    },
    {
      id: '2',
      type: 'session',
      title: 'Session complétée',
      description: 'Méditation matinale - 30 minutes',
      timestamp: '2025-05-24T08:00:00Z',
      status: 'completed',
    },
    {
      id: '3',
      type: 'achievement',
      title: 'Objectif atteint',
      description: '7 jours consécutifs de méditation',
      timestamp: '2025-05-23T20:00:00Z',
      status: 'achievement',
    },
  ];

  const activityColumns = [
    {
      key: 'type',
      title: 'Type',
      render: (value: string) => {
        const icons = {
          booking: '📅',
          session: '🧘‍♀️',
          achievement: '🏆',
        };
        return <span className="text-2xl">{icons[value as keyof typeof icons]}</span>;
      },
      width: 60,
      align: 'center' as const,
    },
    {
      key: 'title',
      title: 'Activité',
      render: (value: string, record: any) => (
        <div>
          <div className="font-medium text-neutral-900">{value}</div>
          <div className="text-sm text-neutral-500">{record.description}</div>
        </div>
      ),
    },
    {
      key: 'timestamp',
      title: 'Date',
      render: (value: string) => {
        const date = new Date(value);
        return (
          <div className="text-sm text-neutral-600">
            {date.toLocaleDateString('fr-FR')}
            <br />
            {date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
          </div>
        );
      },
      align: 'right' as const,
    },
  ];

  const handleBookRetreat = (id: string) => {
    toast.success('Réservation initiée !', {
      title: 'Succès',
      action: {
        label: 'Voir les détails',
        onClick: () => console.log('Voir détails réservation', id),
      },
    });
  };

  const handleContactProfessional = (id: string) => {
    toast.info('Redirection vers la messagerie...', {
      title: 'Contact',
    });
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    toast.info(`Recherche pour: "${query}"`, {
      title: 'Recherche',
    });
  };

  // Afficher le modal de bienvenue pour les nouveaux utilisateurs
  React.useEffect(() => {
    if (user && !localStorage.getItem('welcome-shown')) {
      welcomeModal.open();
      localStorage.setItem('welcome-shown', 'true');
    }
  }, [user, welcomeModal]);

  return (
    <div className="space-y-6">
      {/* En-tête de bienvenue */}
      <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg p-6">
        <h1 className="text-3xl font-bold text-neutral-900 mb-2">
          Bonjour {user?.name || 'Utilisateur'} ! 👋
        </h1>
        <p className="text-neutral-600 mb-4">
          Voici un aperçu de votre parcours bien-être aujourd'hui.
        </p>
        
        {/* Barre de recherche */}
        <div className="max-w-md">
          <SearchInput
            placeholder="Rechercher des retraites, professionnels..."
            onSearch={handleSearch}
            value={searchQuery}
          />
        </div>
      </div>

      {/* Alerte d'information */}
      <Alert variant="info" title="Nouvelle fonctionnalité">
        Découvrez notre nouveau système de recommandations IA personnalisées !
        <Button variant="link" className="ml-2 p-0 h-auto">
          En savoir plus
        </Button>
      </Alert>

      {/* Statistiques */}
      <div>
        <h2 className="text-xl font-semibold text-neutral-900 mb-4">
          Vos statistiques
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {stats.map((stat, index) => (
            <StatsCard key={index} {...stat} />
          ))}
        </div>
      </div>

      {/* Retraites recommandées */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-neutral-900">
            Retraites recommandées pour vous
          </h2>
          <Button variant="outline">
            Voir toutes les retraites
          </Button>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {featuredRetreats.map(retreat => (
            <RetreatCard
              key={retreat.id}
              retreat={retreat}
              onBook={handleBookRetreat}
              onFavorite={(id) => toast.success('Ajouté aux favoris !')}
            />
          ))}
        </div>
      </div>

      {/* Professionnels recommandés */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-neutral-900">
            Professionnels recommandés
          </h2>
          <Button variant="outline">
            Voir tous les professionnels
          </Button>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {recommendedProfessionals.map(professional => (
            <ProfessionalCard
              key={professional.id}
              professional={professional}
              onContact={handleContactProfessional}
              onViewProfile={(id) => console.log('Voir profil', id)}
            />
          ))}
        </div>
      </div>

      {/* Activités récentes */}
      <Card>
        <CardHeader>
          <CardTitle>Activités récentes</CardTitle>
        </CardHeader>
        <CardContent>
          <Table
            columns={activityColumns}
            data={recentActivities}
            size="sm"
            pagination={{
              current: 1,
              pageSize: 5,
              total: recentActivities.length,
              onChange: (page, pageSize) => console.log('Pagination:', page, pageSize),
            }}
          />
        </CardContent>
      </Card>

      {/* Modal de bienvenue */}
      <Modal
        isOpen={welcomeModal.isOpen}
        onClose={welcomeModal.close}
        title="Bienvenue sur Retreat & Be !"
        size="md"
      >
        <ModalContent>
          <div className="text-center space-y-4">
            <div className="text-6xl">🧘‍♀️</div>
            <p className="text-neutral-700">
              Nous sommes ravis de vous accueillir dans votre parcours vers le bien-être.
              Découvrez des retraites exceptionnelles et connectez-vous avec des professionnels
              qualifiés pour transformer votre vie.
            </p>
            <div className="bg-primary-50 p-4 rounded-lg">
              <h4 className="font-medium text-primary-900 mb-2">
                🎁 Offre de bienvenue
              </h4>
              <p className="text-sm text-primary-700">
                Profitez de 20% de réduction sur votre première réservation avec le code
                <span className="font-mono bg-primary-100 px-2 py-1 rounded ml-1">
                  BIENVENUE20
                </span>
              </p>
            </div>
          </div>
        </ModalContent>
        <ModalFooter>
          <Button onClick={welcomeModal.close} fullWidth>
            Commencer mon parcours
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default Dashboard;
