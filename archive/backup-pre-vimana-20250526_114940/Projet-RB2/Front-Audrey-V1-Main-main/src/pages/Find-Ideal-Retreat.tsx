import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  SparklesIcon,
  HeartIcon,
  MapPinIcon,
  UserGroupIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';
import Footer from '../components/ui/Footer';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import ScrollToTop from '../components/ui/ScrollToTop';
import IconSearch from '../components/ui/IconSearch';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface Step {
  id: number;
  title: string;
  subtitle: string;
  icon: React.ComponentType<{ className: string }>;
}

const IdealRetreatFinder: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [selections, setSelections] = useState({
    experience: '',
    themes: [] as string[],
    location: '',
    dates: null as [Date | null, Date | null] | null,
    duration: '',
    groupSize: {
      adults: 1,
      children: 0,
    },
    dietaryRestrictions: [] as string[],
    wellnessPreferences: [] as string[],
    priceRange: '',
  });

  const steps: Step[] = [
    {
      id: 1,
      title: "Votre niveau d'expérience",
      subtitle: 'Choisissez votre niveau pour une expérience adaptée',
      icon: UserGroupIcon as React.ComponentType<{ className: string }>,
    },
    {
      id: 2,
      title: "Vos centres d'intérêt",
      subtitle: 'Sélectionnez les thèmes qui vous inspirent',
      icon: SparklesIcon as React.ComponentType<{ className: string }>,
    },
    {
      id: 3,
      title: 'Régimes alimentaires',
      subtitle: 'Précisez vos préférences alimentaires',
      icon: HeartIcon as React.ComponentType<{ className: string }>,
    },
    {
      id: 4,
      title: 'Destination & Dates',
      subtitle: 'Où et quand souhaitez-vous partir ?',
      icon: MapPinIcon as React.ComponentType<{ className: string }>,
    },
    {
      id: 5,
      title: 'Préférences personnelles',
      subtitle: 'Affinez votre expérience',
      icon: HeartIcon as React.ComponentType<{ className: string }>,
    },
  ];

  const experiences = [
    {
      value: 'beginner',
      label: 'Débutant',
      description: 'Première expérience ou pratique occasionnelle',
    },
    {
      value: 'intermediate',
      label: 'Intermédiaire',
      description: 'Pratique régulière, bases acquises',
    },
    { value: 'advanced', label: 'Avancé', description: 'Pratique intensive, expérience solide' },
    {
      value: 'all-levels',
      label: 'Tous niveaux',
      description: 'Adapté à tous les niveaux de pratique',
    },
  ];

  const themes = [
    // Pratiques traditionnelles
    { value: 'yoga', label: 'Yoga' },
    { value: 'meditation', label: 'Méditation' },
    { value: 'wellness', label: 'Bien-être' },
    { value: 'detox', label: 'Detox' },
    // Thérapies alternatives
    { value: 'sophrology', label: 'Sophrologie' },
    { value: 'sound-therapy', label: 'Sonothérapie' },
    { value: 'hypnotherapy', label: 'Hypnothérapie' },
    { value: 'crystal-therapy', label: 'Lithothérapie' },
    { value: 'reflexology', label: 'Réflexologie' },
    // Expression corporelle
    { value: 'contemporary-dance', label: 'Danse contemporaine' },
    { value: 'meditative-dance', label: 'Danse méditative' },
    { value: 'biodanza', label: 'Biodanza' },
    { value: 'qigong', label: 'Qi Gong' },
    // Thérapies naturelles
    { value: 'forest-therapy', label: 'Sylvothérapie' },
    { value: 'aromatherapy', label: 'Aromathérapie' },
    { value: 'herbal-therapy', label: 'Phytothérapie' },
  ];
  const locations = [
    { value: 'mountains', label: 'Montagne' },
    { value: 'sea', label: 'Bord de mer' },
    { value: 'countryside', label: 'Campagne' },
    { value: 'forest', label: 'Forêt' },
  ];

  const dietaryRestrictions = [
    { value: 'vegetarian', label: 'Végétarien' },
    { value: 'vegan', label: 'Végétalien' },
    { value: 'gluten-free', label: 'Sans gluten' },
    { value: 'lactose-free', label: 'Sans lactose' },
  ];

  const wellnessPreferences = [
    { value: 'massage', label: 'Massage' },
    { value: 'spa', label: 'Spa' },
    { value: 'sauna', label: 'Sauna' },
    { value: 'swimming', label: 'Piscine' },
    { value: 'silence', label: 'Retraite silencieuse' },
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            {experiences.map((exp) => (
              <motion.button
                key={exp.value}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  setSelections({ ...selections, experience: exp.value });
                  setCurrentStep(currentStep + 1);
                }}
                className={`p-6 rounded-xl border-2 text-left transition-all duration-300
                  ${
                    selections.experience === exp.value
                      ? 'border-retreat-green bg-retreat-green/5'
                      : 'border-gray-200 hover:border-retreat-green/50'
                  }`}
              >
                <h3 className='text-lg font-semibold text-gray-900'>{exp.label}</h3>
                <p className='mt-2 text-sm text-gray-600'>{exp.description}</p>
              </motion.button>
            ))}
          </div>
        );

      case 2:
        return (
          <div>
            <div className='grid grid-cols-2 md:grid-cols-3 gap-4'>
              {themes.map((theme) => (
                <motion.button
                  key={theme.value}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    const newThemes = selections.themes.includes(theme.value)
                      ? selections.themes.filter((t) => t !== theme.value)
                      : [...selections.themes, theme.value];
                    setSelections({ ...selections, themes: newThemes });
                  }}
                  className={`p-4 rounded-lg text-center transition-all duration-300
                    ${
                      selections.themes.includes(theme.value)
                        ? 'bg-retreat-green text-white'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }`}
                >
                  {theme.label}
                </motion.button>
              ))}
            </div>
            <div className='mt-8 flex justify-between'>
              <button
                onClick={() => setCurrentStep(currentStep - 1)}
                className='px-6 py-3 text-retreat-green hover:text-retreat-green-dark flex items-center gap-2'
              >
                <ArrowLeftIcon className='w-5 h-5' />
                Retour
              </button>
              <button
                onClick={() => setCurrentStep(currentStep + 1)}
                className='px-6 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark flex items-center gap-2'
              >
                Continuer
                <ArrowRightIcon className='w-5 h-5' />
              </button>
            </div>
          </div>
        );

      case 3:
        return (
          <div>
            <div className='grid grid-cols-2 md:grid-cols-3 gap-4'>
              {dietaryRestrictions.map((diet) => (
                <motion.button
                  key={diet.value}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    const newDiet = selections.dietaryRestrictions.includes(diet.value)
                      ? selections.dietaryRestrictions.filter((d) => d !== diet.value)
                      : [...selections.dietaryRestrictions, diet.value];
                    setSelections({ ...selections, dietaryRestrictions: newDiet });
                  }}
                  className={`p-4 rounded-lg text-center transition-all duration-300
                      ${
                        selections.dietaryRestrictions.includes(diet.value)
                          ? 'bg-retreat-green text-white'
                          : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                      }`}
                >
                  <div className='flex flex-col items-center gap-2'>
                    <span className='text-lg font-medium'>{diet.label}</span>
                  </div>
                </motion.button>
              ))}
            </div>
            <div className='mt-8 flex justify-between'>
              <button
                onClick={() => setCurrentStep(currentStep - 1)}
                className='px-6 py-3 text-retreat-green hover:text-retreat-green-dark flex items-center gap-2'
              >
                <ArrowLeftIcon className='w-5 h-5' />
                Retour
              </button>
              <button
                onClick={() => setCurrentStep(currentStep + 1)}
                className='px-6 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark flex items-center gap-2'
              >
                Continuer
                <ArrowRightIcon className='w-5 h-5' />
              </button>
            </div>
          </div>
        );

      case 4:
        return (
          <div className='space-y-8'>
            <div>
              <h3 className='text-lg font-semibold mb-4'>Choisissez votre destination</h3>
              <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
                {locations.map((loc) => (
                  <motion.button
                    key={loc.value}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setSelections({ ...selections, location: loc.value })}
                    className={`p-4 rounded-lg text-center transition-all duration-300
                        ${
                          selections.location === loc.value
                            ? 'bg-retreat-green text-white'
                            : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                        }`}
                  >
                    {loc.label}
                  </motion.button>
                ))}
              </div>
            </div>

            <div>
              <h3 className='text-lg font-semibold mb-4'>Sélectionnez vos dates</h3>
              <div className='relative flex items-center'>
                <DatePicker
                  selectsRange={true}
                  startDate={selections.dates?.[0]}
                  endDate={selections.dates?.[1]}
                  onChange={(update: [Date | null, Date | null]) => {
                    setSelections({ ...selections, dates: update });
                  }}
                  className='w-full p-2 border rounded-lg pr-10'
                  placeholderText='Sélectionnez vos dates :'
                />
                <button
                  onClick={() => {
                    const datePickerInput = document.querySelector(
                      '.react-datepicker-wrapper input'
                    );
                    if (datePickerInput) {
                      (datePickerInput as HTMLElement).click();
                    }
                  }}
                  className='absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-retreat-green transition-colors'
                >
                  <CalendarIcon className='h-5 w-5' />
                </button>
              </div>
            </div>

            <div className='mt-8 flex justify-between'>
              <button
                onClick={() => setCurrentStep(currentStep - 1)}
                className='px-6 py-3 text-retreat-green hover:text-retreat-green-dark flex items-center gap-2'
              >
                <ArrowLeftIcon className='w-5 h-5' />
                Retour
              </button>
              <button
                onClick={() => setCurrentStep(currentStep + 1)}
                disabled={!selections.location || !selections.dates}
                className='px-6 py-3 bg-retreat-green text-white rounded-lg disabled:opacity-50 
                    disabled:cursor-not-allowed flex items-center gap-2'
              >
                Continuer
                <ArrowRightIcon className='w-5 h-5' />
              </button>
            </div>
          </div>
        );

      case 5:
        return (
          <div className='space-y-8'>
            <div>
              <h3 className='text-lg font-semibold mb-4'>Préférences bien-être</h3>
              <div className='grid grid-cols-2 md:grid-cols-3 gap-3'>
                {wellnessPreferences.map((pref) => (
                  <motion.button
                    key={pref.value}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      const newPrefs = selections.wellnessPreferences.includes(pref.value)
                        ? selections.wellnessPreferences.filter((p) => p !== pref.value)
                        : [...selections.wellnessPreferences, pref.value];
                      setSelections({ ...selections, wellnessPreferences: newPrefs });
                    }}
                    className={`p-4 rounded-lg text-center transition-all duration-300
                        ${
                          selections.wellnessPreferences.includes(pref.value)
                            ? 'bg-retreat-green text-white'
                            : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                        }`}
                  >
                    {pref.label}
                  </motion.button>
                ))}
              </div>
            </div>

            <div className='mt-8 flex justify-between'>
              <button
                onClick={() => setCurrentStep(currentStep - 1)}
                className='px-6 py-3 text-retreat-green hover:text-retreat-green-dark flex items-center gap-2'
              >
                <ArrowLeftIcon className='w-5 h-5' />
                Retour
              </button>
              <button
                onClick={() => {
                  navigate('/results', { state: { selections } });
                }}
                className='px-6 py-3 bg-retreat-green text-white rounded-lg flex items-center gap-2'
              >
                Voir les retraites
                <ArrowRightIcon className='w-5 h-5' />
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className='min-h-screen bg-gray-100'>
      {/* Header */}
      <header className='fixed top-0 left-0 right-0 bg-white shadow-sm z-50'>
        <NavBarClient />
      </header>

      <main className='space-y-12 pt-12'>
        <section>
          <IconSearch />
        </section>

        <div className='min-h-screen bg-gray-50 py-12'>
          <div className='max-w-4xl mx-auto px-4'>
            {/* Progress bar */}
            <div className='mb-8'>
              <div className='flex justify-between mb-2'>
                {steps.map((step) => (
                  <div
                    key={step.id}
                    className={`flex items-center ${
                      step.id === currentStep ? 'text-retreat-green' : 'text-gray-400'
                    }`}
                  >
                    <step.icon className='w-6 h-6' />
                  </div>
                ))}
              </div>
              <div className='h-2 bg-gray-200 rounded-full'>
                <motion.div
                  className='h-full bg-retreat-green rounded-full'
                  initial={{ width: '0%' }}
                  animate={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </div>
            {/* Current step content */}
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className='bg-white rounded-2xl shadow-lg p-8'
            >
              <div className='mb-8'>
                <h2 className='text-2xl font-bold text-gray-900'>{steps[currentStep - 1].title}</h2>
                <p className='mt-2 text-gray-600'>{steps[currentStep - 1].subtitle}</p>
              </div>

              {renderStepContent()}
            </motion.div>
            Navigation
            {currentStep > 1 && (
              <div className='mt-6'>
                <button
                  onClick={() => setCurrentStep(currentStep - 1)}
                  className='text-gray-600 hover:text-retreat-green flex items-center gap-2'
                >
                  <ArrowLeftIcon className='w-5 h-5' />
                  Retour
                </button>
              </div>
            )}
          </div>
        </div>
      </main>
      <footer>
        <Footer />
      </footer>
      <ScrollToTop />
    </div>
  );
};

export default IdealRetreatFinder;
