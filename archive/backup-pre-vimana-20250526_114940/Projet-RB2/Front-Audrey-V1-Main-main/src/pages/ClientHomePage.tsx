import React from 'react';
// import { motion } from 'framer-motion'; // Supprimé car inutilisé
// import { Link } from 'react-router-dom'; // Supprimé car inutilisé
// import { FaStar } from 'react-icons/fa'; // Supprimé car inutilisé
import Footer from '../components/ui/Footer';
import NavBarClient from '../components/organisms/NavBarClient/NavBarClient';
import ScrollToTop from '../components/ui/ScrollToTop';
import FeaturedRetreat from '../components/molecules/FeaturedRetreat';
import RetreatSearch from '../components/ui/RetreatSearch';
import Retreats from '../components/molecules/Retreats';
import ParallaxHero from '../components/ui/ParallaxHero';
import IconSearch from '../components/ui/IconSearch';
import MultiCriteriaRecommendationSection from '../components/multicriteria/MultiCriteriaRecommendationSection';

const ClientHomePage: React.FC = () => {
  // const StarIcon = FaStar as React.ComponentType<{ className: string }>; // Supprimé car inutilisé

  return (
    <div className='min-h-screen bg-gray-100'>
      {/* Header */}
      <header className='fixed top-0 left-0 right-0 bg-white shadow-sm z-50'>
        <NavBarClient />
      </header>

      <main className='space-y-12 pt-12'>
        <section>
          <IconSearch />
        </section>
        <section>
          <ParallaxHero />
        </section>
        <section>
          <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
            <h2 className='text-3xl text-center text-color-retreat-green-dark font-bold mb-8'>
              Notre sélection du jour
            </h2>
            <FeaturedRetreat />
          </div>
        </section>

        <section>
          <h2 className='text-3xl text-center text-color-retreat-green-dark font-bold mb-8'>
            Nos prochaines retraites
          </h2>
          <Retreats />
        </section>

        <section>
          <RetreatSearch />
        </section>

        <section className="bg-gray-50 py-8">
          <MultiCriteriaRecommendationSection
            title="Recommandations personnalisées multi-critères"
            description="Découvrez des retraites qui correspondent parfaitement à vos préférences grâce à notre système de recommandation avancé."
            count={4}
          />
        </section>
      </main>
      <Footer />
      <ScrollToTop />
    </div>
  );
};

export default ClientHomePage;
