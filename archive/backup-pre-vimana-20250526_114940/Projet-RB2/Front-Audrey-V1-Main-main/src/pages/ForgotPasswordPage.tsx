import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../hooks/useAuthContext';
import Header from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';

const ForgotPasswordPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const [formError, setFormError] = useState('');

  const { requestPasswordReset, isLoading, error, clearError } = useAuthContext();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    setFormError('');

    // Validation basique
    if (!email) {
      setFormError('Veuillez saisir votre adresse e-mail');
      return;
    }

    try {
      await requestPasswordReset(email);
      setSubmitted(true);
    } catch (err: unknown) {
      console.error('Erreur lors de la demande de réinitialisation:', err);
      // L'erreur est déjà gérée par le contexte d'authentification
      // Si une gestion d'erreur locale est nécessaire, elle peut être ajoutée ici.
      // Par exemple, si err instanceof Error, setFormError(err.message)
    } finally {
      // Il n'y a pas de bloc finally ici, mais le principe serait le même si nécessaire.
    }
  };

  return (
    <>
      <Helmet>
        <title>Mot de passe oublié | Retreat And Be</title>
        <meta
          name='description'
          content='Réinitialisez votre mot de passe pour accéder à votre compte Retreat And Be.'
        />
      </Helmet>

      <Header />

      <main className='min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8'>
        <div className='sm:mx-auto sm:w-full sm:max-w-md'>
          <h2 className='mt-6 text-center text-3xl font-extrabold text-gray-900'>
            Mot de passe oublié
          </h2>
          <p className='mt-2 text-center text-sm text-gray-600'>
            Ou{' '}
            <Link to='/login' className='font-medium text-green-600 hover:text-green-500'>
              retourner à la page de connexion
            </Link>
          </p>
        </div>

        <div className='mt-8 sm:mx-auto sm:w-full sm:max-w-md'>
          <div className='bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10'>
            {submitted ? (
              <div className='rounded-md bg-green-50 p-4'>
                <div className='flex'>
                  <div className='flex-shrink-0'>
                    <svg
                      className='h-5 w-5 text-green-400'
                      xmlns='http://www.w3.org/2000/svg'
                      viewBox='0 0 20 20'
                      fill='currentColor'
                      aria-hidden='true'
                    >
                      <path
                        fillRule='evenodd'
                        d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z'
                        clipRule='evenodd'
                      />
                    </svg>
                  </div>
                  <div className='ml-3'>
                    <h3 className='text-sm font-medium text-green-800'>Demande envoyée</h3>
                    <div className='mt-2 text-sm text-green-700'>
                      <p>
                        Si un compte existe avec cette adresse e-mail, vous recevrez un lien pour
                        réinitialiser votre mot de passe.
                      </p>
                    </div>
                    <div className='mt-4'>
                      <div className='-mx-2 -my-1.5 flex'>
                        <Link
                          to='/login'
                          className='bg-green-50 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                        >
                          Retour à la connexion
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <>
                {(error || formError) && (
                  <div className='mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded'>
                    {error || formError}
                  </div>
                )}

                <form className='space-y-6' onSubmit={handleSubmit}>
                  <div>
                    <label htmlFor='email' className='block text-sm font-medium text-gray-700'>
                      Adresse e-mail
                    </label>
                    <div className='mt-1'>
                      <input
                        id='email'
                        name='email'
                        type='email'
                        autoComplete='email'
                        required
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm'
                      />
                    </div>
                  </div>

                  <div>
                    <button
                      type='submit'
                      disabled={isLoading}
                      className='w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed'
                    >
                      {isLoading ? 'Envoi en cours...' : 'Réinitialiser le mot de passe'}
                    </button>
                  </div>
                </form>

                <div className='mt-6'>
                  <div className='relative'>
                    <div className='absolute inset-0 flex items-center'>
                      <div className='w-full border-t border-gray-300' />
                    </div>
                    <div className='relative flex justify-center text-sm'>
                      <span className='px-2 bg-white text-gray-500'>Ou</span>
                    </div>
                  </div>

                  <div className='mt-6'>
                    <div className='text-center'>
                      <Link
                        to='/register'
                        className='font-medium text-green-600 hover:text-green-500'
                      >
                        Créer un nouveau compte
                      </Link>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </>
  );
};

export default ForgotPasswordPage;
