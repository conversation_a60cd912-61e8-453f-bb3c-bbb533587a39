import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './styles/globals.css';
import setupCSPReporting from './api/cspReport';
import { initCSRFProtection } from './utils/csrfProtection';

// Initialiser le système de reporting CSP
setupCSPReporting();

// Initialiser la protection CSRF
initCSRFProtection();

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
