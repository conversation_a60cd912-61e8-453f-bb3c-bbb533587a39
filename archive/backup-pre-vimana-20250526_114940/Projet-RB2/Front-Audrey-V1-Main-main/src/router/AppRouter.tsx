/**
 * Router Principal Unifié - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Router principal intégrant tous les modules avec lazy loading
 * et protection des routes.
 */

import React, { Suspense } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { AppLayout } from '../components/ui/design-system';
import { Spinner, Container } from '../components/ui/design-system';
import { useAuth } from '../store/globalStore';
import { ToastProvider } from '../components/ui/design-system/Toast';

// Lazy loading des modules
const AuthModule = React.lazy(() => import('../modules/auth/AuthModule'));
const DashboardModule = React.lazy(() => import('../modules/dashboard/DashboardModule'));
const RetreatsModule = React.lazy(() => import('../modules/retreats/RetreatsModule'));
const ProfessionalsModule = React.lazy(() => import('../modules/professionals/ProfessionalsModule'));

// Pages statiques
const HomePage = React.lazy(() => import('../pages/HomePage'));
const AboutPage = React.lazy(() => import('../pages/AboutPage'));
const ContactPage = React.lazy(() => import('../pages/ContactPage'));
const NotFoundPage = React.lazy(() => import('../pages/NotFoundPage'));

// Composant de chargement
const LoadingFallback: React.FC = () => (
  <Container className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <Spinner size="lg" />
      <p className="mt-4 text-neutral-600">Chargement...</p>
    </div>
  </Container>
);

// Composant de protection des routes
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingFallback />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/auth/login" replace />;
  }

  return <>{children}</>;
};

// Composant de redirection pour les utilisateurs connectés
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingFallback />;
  }

  if (isAuthenticated) {
    return <Navigate to="/app/dashboard" replace />;
  }

  return <>{children}</>;
};

const AppRouter: React.FC = () => {
  return (
    <BrowserRouter>
      <ToastProvider>
        <AnimatePresence mode="wait">
          <Suspense fallback={<LoadingFallback />}>
            <Routes>
              {/* Routes publiques */}
              <Route path="/" element={<HomePage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/contact" element={<ContactPage />} />

              {/* Routes d'authentification */}
              <Route
                path="/auth/*"
                element={
                  <PublicRoute>
                    <AuthModule />
                  </PublicRoute>
                }
              />

              {/* Routes protégées de l'application */}
              <Route
                path="/app/*"
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <Routes>
                        <Route path="/" element={<Navigate to="/app/dashboard" replace />} />
                        <Route path="/dashboard" element={<DashboardModule />} />
                        <Route path="/retreats/*" element={<RetreatsModule />} />
                        <Route path="/professionals/*" element={<ProfessionalsModule />} />
                        
                        {/* Routes à implémenter */}
                        <Route path="/bookings" element={<ComingSoonPage title="Mes Réservations" />} />
                        <Route path="/favorites" element={<ComingSoonPage title="Mes Favoris" />} />
                        <Route path="/messages" element={<ComingSoonPage title="Messages" />} />
                        <Route path="/content" element={<ComingSoonPage title="Contenu" />} />
                        <Route path="/community" element={<ComingSoonPage title="Communauté" />} />
                        <Route path="/settings" element={<ComingSoonPage title="Paramètres" />} />
                        <Route path="/help" element={<ComingSoonPage title="Aide" />} />
                        
                        {/* Route 404 pour l'app */}
                        <Route path="*" element={<NotFoundPage />} />
                      </Routes>
                    </AppLayout>
                  </ProtectedRoute>
                }
              />

              {/* Route 404 globale */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Suspense>
        </AnimatePresence>
      </ToastProvider>
    </BrowserRouter>
  );
};

// Composant temporaire pour les pages à venir
const ComingSoonPage: React.FC<{ title: string }> = ({ title }) => {
  return (
    <Container className="py-12">
      <div className="text-center">
        <div className="text-6xl mb-6">🚧</div>
        <h1 className="text-3xl font-bold text-neutral-900 mb-4">{title}</h1>
        <p className="text-neutral-600 mb-8">
          Cette section sera bientôt disponible. Nous travaillons dur pour vous offrir la meilleure expérience possible.
        </p>
        <div className="space-y-4">
          <p className="text-sm text-neutral-500">
            En attendant, vous pouvez :
          </p>
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => window.history.back()}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              Retour
            </button>
            <button
              onClick={() => window.location.href = '/app/dashboard'}
              className="px-4 py-2 border border-neutral-300 text-neutral-700 rounded-lg hover:bg-neutral-50 transition-colors"
            >
              Tableau de bord
            </button>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default AppRouter;
