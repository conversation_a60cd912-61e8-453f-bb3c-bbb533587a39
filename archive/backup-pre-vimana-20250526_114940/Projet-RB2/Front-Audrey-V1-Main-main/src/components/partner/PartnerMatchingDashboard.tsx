import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { matchingService, MatchingResult } from '../../services/api/matchingService';
import { retreatService } from '../../services/api/retreatService';
import { useAuthContext } from '../../hooks/useAuthContext';
import { toast } from 'react-toastify';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface PartnerMatchingDashboardProps {
  partnerId: string;
}

const PartnerMatchingDashboard: React.FC<PartnerMatchingDashboardProps> = ({ partnerId }) => {
  const { user } = useAuthContext();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [matchingResults, setMatchingResults] = useState<MatchingResult[]>([]);
  const [upcomingRetreats, setUpcomingRetreats] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'recommended' | 'upcoming'>('recommended');

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        
        // Charger les matchings recommandés pour le partenaire
        const matchingResponse = await matchingService.findRetreatsForPartner(partnerId, {
          startDate: new Date().toISOString().split('T')[0],
          limit: 10,
        });
        
        setMatchingResults(matchingResponse.results);
        
        // Charger les retraites à venir
        const retreatsResponse = await retreatService.getUpcomingRetreats({
          limit: 5,
          status: 'PUBLISHED',
        });
        
        setUpcomingRetreats(retreatsResponse.retreats);
        
        setError(null);
      } catch (error) {
        console.error('Erreur lors du chargement des données du tableau de bord:', error);
        setError('Impossible de charger les données du tableau de bord');
        toast.error('Erreur lors du chargement des données');
      } finally {
        setIsLoading(false);
      }
    };

    if (partnerId) {
      loadData();
    }
  }, [partnerId]);

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  // Fonction pour obtenir la couleur en fonction du score
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-500';
    return 'text-red-600';
  };

  // Fonction pour obtenir la couleur de fond en fonction du score
  const getScoreBackgroundColor = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    if (score >= 40) return 'bg-orange-100';
    return 'bg-red-100';
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">{error}</h3>
          <p className="mt-1 text-sm text-gray-500">
            Veuillez réessayer ultérieurement.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex">
          <button
            onClick={() => setActiveTab('recommended')}
            className={`py-4 px-6 font-medium text-sm ${
              activeTab === 'recommended'
                ? 'border-b-2 border-retreat-green text-retreat-green'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Retraites recommandées
          </button>
          <button
            onClick={() => setActiveTab('upcoming')}
            className={`py-4 px-6 font-medium text-sm ${
              activeTab === 'upcoming'
                ? 'border-b-2 border-retreat-green text-retreat-green'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Retraites à venir
          </button>
        </nav>
      </div>

      <div className="p-6">
        {activeTab === 'recommended' && (
          <>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Retraites recommandées pour vous
              </h2>
              <Link
                to="/partners/matching"
                className="text-sm text-retreat-green hover:text-retreat-green-dark"
              >
                Voir toutes les opportunités
              </Link>
            </div>

            {matchingResults.length === 0 ? (
              <div className="text-center py-8">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  Aucune retraite recommandée pour le moment
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Consultez régulièrement cette page pour découvrir de nouvelles opportunités.
                </p>
              </div>
            ) : (
              <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                      >
                        Retraite
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        Dates
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        Lieu
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        Compatibilité
                      </th>
                      <th
                        scope="col"
                        className="relative py-3.5 pl-3 pr-4 sm:pr-6"
                      >
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {matchingResults.map((result) => (
                      <tr key={result.retreatId}>
                        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                          <div className="font-medium text-gray-900">
                            {result.retreat?.title}
                          </div>
                          <div className="text-gray-500 truncate max-w-xs">
                            {result.retreat?.description.substring(0, 60)}...
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          <div>Du {formatDate(result.retreat?.startDate || '')}</div>
                          <div>au {formatDate(result.retreat?.endDate || '')}</div>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          {result.retreat?.location}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm">
                          <div className={`font-medium ${getScoreColor(result.score)}`}>
                            {result.score}%
                          </div>
                        </td>
                        <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                          <Link
                            to={`/matching/details/${partnerId}/${result.retreatId}`}
                            className="text-retreat-green hover:text-retreat-green-dark"
                          >
                            Voir détails
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </>
        )}

        {activeTab === 'upcoming' && (
          <>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Retraites à venir
              </h2>
              <Link
                to="/retreats"
                className="text-sm text-retreat-green hover:text-retreat-green-dark"
              >
                Voir toutes les retraites
              </Link>
            </div>

            {upcomingRetreats.length === 0 ? (
              <div className="text-center py-8">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  Aucune retraite à venir
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Consultez régulièrement cette page pour découvrir de nouvelles retraites.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {upcomingRetreats.map((retreat) => (
                  <div
                    key={retreat.id}
                    className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                  >
                    {retreat.images && retreat.images.length > 0 ? (
                      <img
                        src={retreat.images[0]}
                        alt={retreat.title}
                        className="h-40 w-full object-cover"
                      />
                    ) : (
                      <div className="h-40 w-full bg-gray-200 flex items-center justify-center">
                        <svg
                          className="h-12 w-12 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    )}
                    <div className="p-4">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {retreat.title}
                      </h3>
                      <p className="mt-1 text-sm text-gray-500 truncate">
                        {retreat.location}
                      </p>
                      <p className="mt-1 text-sm text-gray-500">
                        Du {formatDate(retreat.startDate)} au {formatDate(retreat.endDate)}
                      </p>
                      <div className="mt-4 flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-900">
                          {retreat.price} €
                        </span>
                        <Link
                          to={`/matching/details/${partnerId}/${retreat.id}`}
                          className="text-sm text-retreat-green hover:text-retreat-green-dark"
                        >
                          Voir compatibilité
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default PartnerMatchingDashboard;
