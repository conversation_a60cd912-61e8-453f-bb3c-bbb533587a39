import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { MatchingResult } from '../../services/api/matchingService';

interface PartnerMatchResultsProps {
  results: MatchingResult[];
  total: number;
  executionTimeMs: number;
  isLoading?: boolean;
  onSelectPartner?: (partnerId: string) => void;
}

const PartnerMatchResults: React.FC<PartnerMatchResultsProps> = ({
  results,
  total,
  executionTimeMs,
  isLoading = false,
  onSelectPartner,
}) => {
  const [selectedPartnerId, setSelectedPartnerId] = useState<string | null>(null);

  const handleSelectPartner = (partnerId: string) => {
    setSelectedPartnerId(partnerId);
    if (onSelectPartner) {
      onSelectPartner(partnerId);
    }
  };

  // Fonction pour formater le type de partenaire
  const formatPartnerType = (type: string) => {
    switch (type) {
      case 'PREMIUM_CERTIFIED':
        return 'Premium Certifié';
      case 'CERTIFIED':
        return 'Certifié';
      case 'STANDARD':
        return 'Standard';
      default:
        return type;
    }
  };

  // Fonction pour formater la catégorie de partenaire
  const formatPartnerCategory = (category: string) => {
    switch (category) {
      case 'ORGANIZER':
        return 'Organisateur';
      case 'TRAVEL_AGENCY':
        return 'Agence de voyage';
      case 'CATERING':
        return 'Restauration';
      case 'GUIDE':
        return 'Guide';
      case 'TRANSPORT':
        return 'Transport';
      case 'WELLNESS':
        return 'Bien-être';
      case 'INSURANCE':
        return 'Assurance';
      case 'ACCOMMODATION':
        return 'Hébergement';
      case 'EQUIPMENT':
        return 'Équipement';
      case 'OTHER':
        return 'Autre';
      default:
        return category;
    }
  };

  // Fonction pour obtenir la couleur du badge de type
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'PREMIUM_CERTIFIED':
        return 'bg-purple-100 text-purple-800';
      case 'CERTIFIED':
        return 'bg-green-100 text-green-800';
      case 'STANDARD':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Fonction pour obtenir la couleur du badge de catégorie
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'ORGANIZER':
        return 'bg-indigo-100 text-indigo-800';
      case 'TRAVEL_AGENCY':
        return 'bg-blue-100 text-blue-800';
      case 'CATERING':
        return 'bg-yellow-100 text-yellow-800';
      case 'GUIDE':
        return 'bg-green-100 text-green-800';
      case 'TRANSPORT':
        return 'bg-gray-100 text-gray-800';
      case 'WELLNESS':
        return 'bg-pink-100 text-pink-800';
      case 'INSURANCE':
        return 'bg-red-100 text-red-800';
      case 'ACCOMMODATION':
        return 'bg-teal-100 text-teal-800';
      case 'EQUIPMENT':
        return 'bg-orange-100 text-orange-800';
      case 'OTHER':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
        </div>
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun résultat trouvé</h3>
          <p className="mt-1 text-sm text-gray-500">
            Essayez de modifier vos critères de recherche pour trouver des partenaires.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">
            {total} partenaire{total > 1 ? 's' : ''} trouvé{total > 1 ? 's' : ''}
          </h3>
          <span className="text-sm text-gray-500">
            Recherche effectuée en {(executionTimeMs / 1000).toFixed(2)} secondes
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-0">
        {/* Liste des partenaires */}
        <div className="col-span-1 border-r border-gray-200 max-h-[600px] overflow-y-auto">
          {results.map((result) => (
            <div
              key={result.partnerId}
              className={`p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors ${
                selectedPartnerId === result.partnerId ? 'bg-blue-50' : ''
              }`}
              onClick={() => handleSelectPartner(result.partnerId)}
            >
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">{result.partner?.companyName}</h4>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(
                        result.partner?.type || ''
                      )}`}
                    >
                      {formatPartnerType(result.partner?.type || '')}
                    </span>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(
                        result.partner?.category || ''
                      )}`}
                    >
                      {formatPartnerCategory(result.partner?.category || '')}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-gray-500 line-clamp-2">
                    {result.partner?.description}
                  </p>
                </div>
                <div className="flex flex-col items-end">
                  <div className="text-lg font-bold text-retreat-green">{result.score}%</div>
                  {result.partner?.averageRating && (
                    <div className="flex items-center mt-1">
                      <svg
                        className="w-4 h-4 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                      <span className="ml-1 text-sm text-gray-600">
                        {result.partner.averageRating.toFixed(1)}
                      </span>
                      <span className="ml-1 text-xs text-gray-500">
                        ({result.partner.totalReviews})
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Détails du partenaire sélectionné */}
        <div className="col-span-2 p-6">
          {selectedPartnerId ? (
            (() => {
              const selectedResult = results.find(
                (result) => result.partnerId === selectedPartnerId
              );
              if (!selectedResult || !selectedResult.partner) return null;

              return (
                <div>
                  <div className="flex justify-between items-start">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">
                        {selectedResult.partner.companyName}
                      </h2>
                      <div className="flex flex-wrap gap-2 mt-2">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(
                            selectedResult.partner.type
                          )}`}
                        >
                          {formatPartnerType(selectedResult.partner.type)}
                        </span>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(
                            selectedResult.partner.category
                          )}`}
                        >
                          {formatPartnerCategory(selectedResult.partner.category)}
                        </span>
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-retreat-green">
                        {selectedResult.score}%
                      </div>
                      <div className="text-sm text-gray-500">Compatibilité</div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h3 className="text-lg font-semibold mb-2">À propos</h3>
                    <p className="text-gray-700">{selectedResult.partner.description}</p>
                  </div>

                  <div className="mt-6 grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Spécialisations</h3>
                      <div className="flex flex-wrap gap-1">
                        {selectedResult.partner.specializations.map((spec) => (
                          <span
                            key={spec}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {spec}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Langues parlées</h3>
                      <div className="flex flex-wrap gap-1">
                        {selectedResult.partner.languages.map((lang) => (
                          <span
                            key={lang}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {lang}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h3 className="text-lg font-semibold mb-2">Facteurs de compatibilité</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">Compétences</span>
                          <span className="text-sm font-medium text-gray-700">
                            {selectedResult.compatibilityFactors.skillMatch}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-retreat-green h-2 rounded-full"
                            style={{
                              width: `${selectedResult.compatibilityFactors.skillMatch}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">Disponibilité</span>
                          <span className="text-sm font-medium text-gray-700">
                            {selectedResult.compatibilityFactors.availabilityMatch}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-retreat-green h-2 rounded-full"
                            style={{
                              width: `${selectedResult.compatibilityFactors.availabilityMatch}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">Localisation</span>
                          <span className="text-sm font-medium text-gray-700">
                            {selectedResult.compatibilityFactors.locationMatch}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-retreat-green h-2 rounded-full"
                            style={{
                              width: `${selectedResult.compatibilityFactors.locationMatch}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">Évaluations</span>
                          <span className="text-sm font-medium text-gray-700">
                            {selectedResult.compatibilityFactors.ratingMatch}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-retreat-green h-2 rounded-full"
                            style={{
                              width: `${selectedResult.compatibilityFactors.ratingMatch}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-8 flex justify-end space-x-4">
                    <Link
                      to={`/matching/details/${selectedResult.partnerId}/${selectedResult.retreatId || ''}`}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                    >
                      Voir les détails du matching
                    </Link>
                    <Link
                      to={`/partners/${selectedResult.partnerId}`}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                    >
                      Voir le profil complet
                    </Link>
                  </div>
                </div>
              );
            })()
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <svg
                className="w-16 h-16 mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              <p className="text-center">
                Sélectionnez un partenaire dans la liste pour voir les détails
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PartnerMatchResults;
