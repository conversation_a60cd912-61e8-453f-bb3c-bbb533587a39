import React, { useState } from 'react';

const NewsletterBlog: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Ici, vous ajouterez l'appel à votre API pour enregistrer l'email
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulation d'appel API
      setStatus('success');
      setEmail('');
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_) {
      setStatus('error');
    } finally {
      setIsSubmitting(false);
      // Reset status after 3 seconds
      setTimeout(() => setStatus('idle'), 3000);
    }
  };

  return (
    <div className='bg-retreat-green text-white rounded-lg shadow-sm p-6'>
      <h3 className='text-lg font-semibold mb-4'>Newsletter du blog</h3>
      <p className='text-sm mb-4 text-white/90'>
        Recevez nos meilleurs articles et conseils directement dans votre boîte mail.
      </p>
      <form onSubmit={handleSubmit} className='space-y-3'>
        <div className='relative'>
          <input
            type='email'
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder='Votre email'
            required
            className='w-full px-4 py-2 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-300'
            disabled={isSubmitting}
          />
        </div>
        <button
          type='submit'
          disabled={isSubmitting}
          className={`
            w-full px-4 py-2 rounded-lg
            transition-all duration-300
            ${isSubmitting ? 'bg-white/50 cursor-not-allowed' : 'bg-white hover:bg-gray-100'}
            text-retreat-green font-medium
          `}
        >
          {isSubmitting ? (
            <span className='flex items-center justify-center'>
              <svg
                className='animate-spin -ml-1 mr-3 h-5 w-5 text-retreat-green'
                xmlns='http://www.w3.org/2000/svg'
                fill='none'
                viewBox='0 0 24 24'
              >
                <circle
                  className='opacity-25'
                  cx='12'
                  cy='12'
                  r='10'
                  stroke='currentColor'
                  strokeWidth='4'
                />
                <path
                  className='opacity-75'
                  fill='currentColor'
                  d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                />
              </svg>
              Inscription en cours...
            </span>
          ) : (
            "S'inscrire à la newsletter"
          )}
        </button>

        {/* Status messages */}
        {status === 'success' && (
          <div className='text-sm text-white bg-white/20 rounded-lg p-2 text-center'>
            Merci pour votre inscription ! 🎉
          </div>
        )}
        {status === 'error' && (
          <div className='text-sm text-white bg-red-500/20 rounded-lg p-2 text-center'>
            Une erreur est survenue. Veuillez réessayer.
          </div>
        )}
      </form>
    </div>
  );
};

export default NewsletterBlog;
