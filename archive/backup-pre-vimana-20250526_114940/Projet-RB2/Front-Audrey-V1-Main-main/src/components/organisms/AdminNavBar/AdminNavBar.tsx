import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuthContext } from '../../../hooks/useAuthContext';
import AdminMenu from '../../admin/AdminMenu';
import AlertCenter from '../../alerts/AlertCenter';
import { alertService, Alert } from '../../../services/api/alertService';

/**
 * Barre de navigation pour l'administration
 */
const AdminNavBar: React.FC = () => {
  const { user, logout } = useAuthContext();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isAlertCenterOpen, setIsAlertCenterOpen] = useState(false);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);

  // Charger les alertes
  useEffect(() => {
    const loadAlerts = async () => {
      try {
        // Récupérer uniquement les alertes non lues
        const alertsData = await alertService.getAlerts(false);
        setAlerts(alertsData);
        setUnreadCount(alertsData.length);
      } catch (error) {
        console.error('Erreur lors du chargement des alertes:', error);
      }
    };

    // Charger les alertes au chargement du composant
    loadAlerts();

    // Rafraîchir les alertes toutes les 5 minutes
    const interval = setInterval(loadAlerts, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  // Gérer la déconnexion
  const handleLogout = () => {
    logout();
  };

  return (
    <nav className="bg-white shadow-md fixed top-0 left-0 right-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link to="/" className="text-retreat-green font-bold text-xl">
                Retreat And Be
              </Link>
              <span className="ml-2 text-gray-500">| Admin</span>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              <Link
                to="/admin/partners"
                className="border-transparent text-gray-500 hover:border-retreat-green hover:text-retreat-green inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
              >
                Partenaires
              </Link>
              <Link
                to="/admin/matching-analytics"
                className="border-transparent text-gray-500 hover:border-retreat-green hover:text-retreat-green inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
              >
                Analyse des Matchings
              </Link>
              <Link
                to="/admin/security"
                className="border-transparent text-gray-500 hover:border-retreat-green hover:text-retreat-green inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
              >
                Sécurité
              </Link>
              <Link
                to="/admin/explanation-dashboard"
                className="border-transparent text-gray-500 hover:border-retreat-green hover:text-retreat-green inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
              >
                Tableau de Bord
              </Link>
              <Link
                to="/admin/ab-testing"
                className="border-transparent text-gray-500 hover:border-retreat-green hover:text-retreat-green inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
              >
                Tests A/B
              </Link>
              <Link
                to="/admin/explanation-learning"
                className="border-transparent text-gray-500 hover:border-retreat-green hover:text-retreat-green inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
              >
                Apprentissage Continu
              </Link>
            </div>
          </div>
          <div className="hidden sm:ml-6 sm:flex sm:items-center">
            {/* Bouton d'alertes */}
            <div className="relative">
              <button
                type="button"
                className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                onClick={() => setIsAlertCenterOpen(!isAlertCenterOpen)}
              >
                <span className="sr-only">Voir les alertes</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                {unreadCount > 0 && (
                  <span className="absolute top-0 right-0 block h-4 w-4 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </span>
                )}
              </button>

              {isAlertCenterOpen && (
                <AlertCenter onClose={() => setIsAlertCenterOpen(false)} />
              )}
            </div>

            {/* Menu utilisateur */}
            <div className="ml-3 relative">
              <div>
                <button
                  type="button"
                  className="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                  id="user-menu-button"
                  aria-expanded="false"
                  aria-haspopup="true"
                  onClick={() => setIsProfileOpen(!isProfileOpen)}
                >
                  <span className="sr-only">Ouvrir le menu utilisateur</span>
                  <div className="h-8 w-8 rounded-full bg-retreat-green text-white flex items-center justify-center">
                    {user?.firstName?.charAt(0) || 'A'}
                  </div>
                </button>
              </div>

              {isProfileOpen && (
                <div
                  className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="user-menu-button"
                  tabIndex={-1}
                >
                  <Link
                    to="/compte"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    role="menuitem"
                    tabIndex={-1}
                    id="user-menu-item-0"
                  >
                    Mon profil
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    role="menuitem"
                    tabIndex={-1}
                    id="user-menu-item-2"
                  >
                    Déconnexion
                  </button>
                </div>
              )}
            </div>
          </div>
          <div className="-mr-2 flex items-center sm:hidden">
            {/* Bouton d'alertes mobile */}
            <button
              type="button"
              className="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green mr-2"
              onClick={() => setIsAlertCenterOpen(!isAlertCenterOpen)}
            >
              <span className="sr-only">Voir les alertes</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
              {unreadCount > 0 && (
                <span className="absolute top-1 right-10 block h-4 w-4 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>

            {/* Bouton de menu mobile */}
            <button
              type="button"
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-retreat-green"
              aria-controls="mobile-menu"
              aria-expanded="false"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <span className="sr-only">Ouvrir le menu principal</span>
              <svg
                className={`${isMenuOpen ? 'hidden' : 'block'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
              <svg
                className={`${isMenuOpen ? 'block' : 'hidden'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Menu mobile */}
      <div
        className={`${isMenuOpen ? 'block' : 'hidden'} sm:hidden`}
        id="mobile-menu"
      >
        <div className="pt-2 pb-3 space-y-1">
          <Link
            to="/admin/partners"
            className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-retreat-green hover:text-retreat-green block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
          >
            Partenaires
          </Link>
          <Link
            to="/admin/matching-analytics"
            className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-retreat-green hover:text-retreat-green block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
          >
            Analyse des Matchings
          </Link>
          <Link
            to="/admin/security"
            className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-retreat-green hover:text-retreat-green block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
          >
            Sécurité
          </Link>
          <Link
            to="/admin/explanation-dashboard"
            className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-retreat-green hover:text-retreat-green block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
          >
            Tableau de Bord
          </Link>
          <Link
            to="/admin/ab-testing"
            className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-retreat-green hover:text-retreat-green block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
          >
            Tests A/B
          </Link>
          <Link
            to="/admin/explanation-learning"
            className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-retreat-green hover:text-retreat-green block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
          >
            Apprentissage Continu
          </Link>
        </div>
        <div className="pt-4 pb-3 border-t border-gray-200">
          <div className="flex items-center px-4">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-retreat-green text-white flex items-center justify-center">
                {user?.firstName?.charAt(0) || 'A'}
              </div>
            </div>
            <div className="ml-3">
              <div className="text-base font-medium text-gray-800">
                {user?.firstName} {user?.lastName}
              </div>
              <div className="text-sm font-medium text-gray-500">{user?.email}</div>
            </div>
          </div>
          <div className="mt-3 space-y-1">
            <Link
              to="/compte"
              className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100"
            >
              Mon profil
            </Link>
            <button
              onClick={handleLogout}
              className="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100"
            >
              Déconnexion
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default AdminNavBar;
