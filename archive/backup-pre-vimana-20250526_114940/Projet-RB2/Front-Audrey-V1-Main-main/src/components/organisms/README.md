# Organismes (Organisms)

Les organismes sont des composants complexes qui combinent des molécules et des atomes pour former des sections distinctes et fonctionnelles de l'interface utilisateur. Ils représentent des composants plus grands et plus autonomes.

## Structure des dossiers

- `NavBar/` : Barre de navigation

  - `NavBarClient.tsx` : Barre de navigation complète

- `Footer/` : Pied de page

  - `Footer.tsx` : Pied de page complet

- `SearchFilters/` : Filtres de recherche

  - `SearchFilters.tsx` : Système de filtres complet

- `RetreatCard/` : Cartes de retraite

  - `RetreatCard.tsx` : Carte de retraite détaillée

- `ChatWindow/` : Fenêtre de chat

  - `ChatWindow.tsx` : Interface de chat complète

- `Hero/` : Section héro

  - `Hero.tsx` : Section héro principale

- `Newsletter/` : Newsletter
  - `Newsletter.tsx` : Formulaire d'inscription newsletter

## Règles d'utilisation

1. Les organismes doivent :

   - Être composés de molécules et d'atomes
   - Avoir une fonction complexe mais bien définie
   - Être autonomes dans leur fonctionnement
   - Gérer leur propre état et logique

2. Chaque organisme doit :

   - Avoir ses propres tests
   - Être documenté avec des props typées
   - Inclure des exemples d'utilisation
   - Gérer ses propres états et effets
   - Suivre les conventions de nommage

3. Bonnes pratiques :
   - Maintenir une séparation claire des responsabilités
   - Utiliser les molécules et atomes existants
   - Documenter les cas d'utilisation complexes
   - Gérer les états et les effets de manière optimisée
   - Implémenter une gestion d'erreur appropriée
