import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Notification from '../../molecules/Notification/Notification';

interface NotificationItem {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message?: string;
  duration?: number;
}

interface NotificationCenterProps {
  className?: string;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  maxNotifications?: number;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  className = '',
  position = 'top-right',
  maxNotifications = 5,
}) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  // Fonction pour ajouter une notification
  const addNotification = (notification: Omit<NotificationItem, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    setNotifications((prev) => {
      const newNotifications = [{ ...notification, id }, ...prev].slice(0, maxNotifications);
      return newNotifications;
    });
  };

  // Fonction pour supprimer une notification
  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id));
  };

  // Exposer la fonction d'ajout de notification via le contexte global
  useEffect(() => {
    // @ts-ignore
    window.addNotification = addNotification;
    return () => {
      // @ts-ignore
      delete window.addNotification;
    };
  }, []);

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
  };

  return (
    <div
      className={`fixed z-50 ${positionClasses[position]} space-y-4 ${className}`}
      style={{ maxWidth: '400px' }}
    >
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <Notification
              type={notification.type}
              title={notification.title}
              message={notification.message}
              onClose={() => removeNotification(notification.id)}
              autoClose={true}
              duration={notification.duration}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default NotificationCenter;
