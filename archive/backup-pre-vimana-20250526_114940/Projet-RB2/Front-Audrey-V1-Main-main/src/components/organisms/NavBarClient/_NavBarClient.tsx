import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MenuButton from '../../atoms/Button/MenuButton';
import NavigationMenu from '../../molecules/NavigationMenu/NavigationMenu';
import SearchPanel from '../../molecules/SearchPanel/SearchPanel';
import LanguageSwitcher from '../../../components/LanguageSwitcher';
import Icon from '../../atoms/Icon/Icon';
import { MagnifyingGlassIcon, UserCircleIcon, ShoppingBagIcon } from '@heroicons/react/24/outline';

interface MenuItem {
  label: string;
  href: string;
  subItems?: MenuItem[];
}

const menuItems: MenuItem[] = [
  { label: 'Aide', href: '/aide' },
  { label: 'Communauté', href: '/communaute' },
  {
    label: 'Blog',
    href: '/blog',
    subItems: [
      {
        label: 'Pratiques & Activités',
        href: '/blog/pratiques',
        subItems: [
          { label: 'Yoga & Méditation', href: '/blog/pratiques/yoga-meditation' },
          { label: 'Bien-être & Spa', href: '/blog/pratiques/bien-etre-spa' },
          { label: 'Développement personnel', href: '/blog/pratiques/developpement-personnel' },
          { label: 'Nutrition & Détox', href: '/blog/pratiques/nutrition-detox' },
          { label: 'Art-thérapie', href: '/blog/pratiques/art-therapie' },
        ],
      },
      // ... autres sous-menus du blog
    ],
  },
  {
    label: 'Espace PRO',
    href: '/professional',
    subItems: [
      { label: 'Devenir hôte', href: '/professional/become-host' },
      { label: 'Dashboard', href: '/professional/dashboard' },
      { label: 'Ressources', href: '/professional/resources' },
    ],
  },
];

const NavBarClient: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const searchButtonRef = useRef<HTMLButtonElement>(null);
  const searchPanelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isSearchOpen &&
        searchPanelRef.current &&
        searchButtonRef.current &&
        !searchPanelRef.current.contains(event.target as Node) &&
        !searchButtonRef.current.contains(event.target as Node)
      ) {
        setIsSearchOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isSearchOpen]);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);

  return (
    <header className='bg-white shadow-sm sticky top-0 z-50'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex justify-between items-center h-16'>
          {/* Logo */}
          <Link to='/' className='flex-shrink-0'>
            <img className='h-8 w-auto' src='/logo.svg' alt='Retreat & Be' />
          </Link>

          {/* Navigation desktop */}
          <nav className='hidden md:flex space-x-8'>
            <NavigationMenu items={menuItems} />
          </nav>

          {/* Actions */}
          <div className='flex items-center space-x-4'>
            <button
              ref={searchButtonRef}
              onClick={toggleSearch}
              className='p-2 rounded-md hover:bg-gray-100 transition-colors duration-200'
              aria-label='Rechercher'
            >
              <Icon icon={MagnifyingGlassIcon} size='md' color='text-gray-600' />
            </button>

            {/* Language Switcher */}
            <div className='hidden md:block'>
              <LanguageSwitcher variant='dropdown' className='mr-2' />
            </div>

            <Link
              to='/account'
              className='p-2 rounded-md hover:bg-gray-100 transition-colors duration-200'
              aria-label='Mon compte'
            >
              <Icon icon={UserCircleIcon} size='md' color='text-gray-600' />
            </Link>

            <Link
              to='/cart'
              className='p-2 rounded-md hover:bg-gray-100 transition-colors duration-200'
              aria-label='Panier'
            >
              <Icon icon={ShoppingBagIcon} size='md' color='text-gray-600' />
            </Link>

            {/* Menu mobile */}
            <div className='md:hidden'>
              <MenuButton isOpen={isMenuOpen} onClick={toggleMenu} />
            </div>
          </div>
        </div>
      </div>

      {/* Menu mobile */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className='md:hidden'
          >
            <div className='px-2 pt-2 pb-3 space-y-1'>
              <NavigationMenu items={menuItems} />
              <div className='mt-4 px-4'>
                <div className='text-sm font-medium text-gray-500 mb-2'>Langue</div>
                <LanguageSwitcher variant='buttons' />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Panneau de recherche */}
      <div ref={searchPanelRef}>
        <SearchPanel isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />
      </div>
    </header>
  );
};

export default NavBarClient;
