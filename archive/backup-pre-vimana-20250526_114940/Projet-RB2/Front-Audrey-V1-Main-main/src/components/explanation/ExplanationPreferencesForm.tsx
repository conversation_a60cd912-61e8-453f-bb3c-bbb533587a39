import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { toast } from 'react-toastify';
import { 
  explanationPreferencesService, 
  ExplanationStyle, 
  DetailLevel, 
  ExplanationFormat,
  ExplanationPreferences,
  UpdateExplanationPreferencesRequest,
} from '../../services/api/explanationPreferencesService';
import { t } from '../../services/i18n/i18nService';

interface ExplanationPreferencesFormProps {
  onPreferencesSaved?: (preferences: ExplanationPreferences) => void;
  className?: string;
}

/**
 * Formulaire de préférences d'explication
 */
const ExplanationPreferencesForm: React.FC<ExplanationPreferencesFormProps> = ({
  onPreferencesSaved,
  className = '',
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [preferences, setPreferences] = useState<ExplanationPreferences | null>(null);
  
  const { control, handleSubmit, reset, formState: { errors } } = useForm<UpdateExplanationPreferencesRequest>();
  
  // Charger les préférences au chargement du composant
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        setLoading(true);
        const userPreferences = await explanationPreferencesService.getUserPreferences();
        setPreferences(userPreferences);
        
        // Initialiser le formulaire avec les préférences actuelles
        reset({
          preferredStyle: userPreferences.preferredStyle,
          detailLevel: userPreferences.detailLevel,
          preferredFormat: userPreferences.preferredFormat,
          highlightedFactors: userPreferences.highlightedFactors,
          hiddenFactors: userPreferences.hiddenFactors,
          language: userPreferences.language,
          culturalPreferences: userPreferences.culturalPreferences,
          accessibilityPreferences: userPreferences.accessibilityPreferences,
        });
      } catch (error) {
        console.error('Erreur lors du chargement des préférences:', error);
        toast.error(t('explanation.preferences.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    loadPreferences();
  }, [reset]);
  
  // Gérer la soumission du formulaire
  const onSubmit = async (data: UpdateExplanationPreferencesRequest) => {
    try {
      setSaving(true);
      const updatedPreferences = await explanationPreferencesService.updateUserPreferences(data);
      setPreferences(updatedPreferences);
      
      toast.success(t('explanation.preferences.saveSuccess'));
      
      if (onPreferencesSaved) {
        onPreferencesSaved(updatedPreferences);
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des préférences:', error);
      toast.error(t('explanation.preferences.saveError'));
    } finally {
      setSaving(false);
    }
  };
  
  // Gérer la réinitialisation des préférences
  const handleReset = async () => {
    try {
      setSaving(true);
      const defaultPreferences = await explanationPreferencesService.resetUserPreferences();
      setPreferences(defaultPreferences);
      
      // Réinitialiser le formulaire avec les préférences par défaut
      reset({
        preferredStyle: defaultPreferences.preferredStyle,
        detailLevel: defaultPreferences.detailLevel,
        preferredFormat: defaultPreferences.preferredFormat,
        highlightedFactors: defaultPreferences.highlightedFactors,
        hiddenFactors: defaultPreferences.hiddenFactors,
        language: defaultPreferences.language,
        culturalPreferences: defaultPreferences.culturalPreferences,
        accessibilityPreferences: defaultPreferences.accessibilityPreferences,
      });
      
      toast.success(t('explanation.preferences.resetSuccess'));
      
      if (onPreferencesSaved) {
        onPreferencesSaved(defaultPreferences);
      }
    } catch (error) {
      console.error('Erreur lors de la réinitialisation des préférences:', error);
      toast.error(t('explanation.preferences.resetError'));
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <div className={`flex justify-center items-center p-6 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-retreat-green"></div>
      </div>
    );
  }
  
  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}>
      <div className="p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('explanation.preferences.title')}</h2>
        
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Style d'explication */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('explanation.preferences.preferredStyle')}
              </label>
              <Controller
                name="preferredStyle"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    disabled={saving}
                  >
                    <option value={ExplanationStyle.CONVERSATIONAL}>{t('explanation.style.conversational')}</option>
                    <option value={ExplanationStyle.TECHNICAL}>{t('explanation.style.technical')}</option>
                    <option value={ExplanationStyle.CONCISE}>{t('explanation.style.concise')}</option>
                    <option value={ExplanationStyle.NARRATIVE}>{t('explanation.style.narrative')}</option>
                    <option value={ExplanationStyle.EDUCATIONAL}>{t('explanation.style.educational')}</option>
                  </select>
                )}
              />
            </div>
            
            {/* Niveau de détail */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('explanation.preferences.detailLevel')}
              </label>
              <Controller
                name="detailLevel"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    disabled={saving}
                  >
                    <option value={DetailLevel.MINIMAL}>{t('explanation.detailLevel.minimal')}</option>
                    <option value={DetailLevel.MODERATE}>{t('explanation.detailLevel.moderate')}</option>
                    <option value={DetailLevel.DETAILED}>{t('explanation.detailLevel.detailed')}</option>
                    <option value={DetailLevel.COMPREHENSIVE}>{t('explanation.detailLevel.comprehensive')}</option>
                  </select>
                )}
              />
            </div>
            
            {/* Format d'explication */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('explanation.preferences.preferredFormat')}
              </label>
              <div className="flex flex-wrap gap-4">
                <Controller
                  name="preferredFormat"
                  control={control}
                  render={({ field }) => (
                    <>
                      {Object.values(ExplanationFormat).map((format) => (
                        <label key={format} className="inline-flex items-center">
                          <input
                            type="checkbox"
                            className="rounded border-gray-300 text-retreat-green focus:ring-retreat-green"
                            value={format}
                            checked={field.value?.includes(format)}
                            onChange={(e) => {
                              const value = e.target.value as ExplanationFormat;
                              const newValue = e.target.checked
                                ? [...(field.value || []), value]
                                : (field.value || []).filter((f) => f !== value);
                              field.onChange(newValue);
                            }}
                            disabled={saving}
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            {t(`explanation.format.${format.toLowerCase()}`)}
                          </span>
                        </label>
                      ))}
                    </>
                  )}
                />
              </div>
            </div>
            
            {/* Langue */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('explanation.preferences.language')}
              </label>
              <Controller
                name="language"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    disabled={saving}
                  >
                    <option value="fr">Français</option>
                    <option value="en">English</option>
                    <option value="es">Español</option>
                    <option value="de">Deutsch</option>
                  </select>
                )}
              />
            </div>
          </div>
          
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleReset}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={saving}
            >
              {saving ? t('common.resetting') : t('common.reset')}
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors disabled:bg-gray-300 disabled:text-gray-500"
              disabled={saving}
            >
              {saving ? t('common.saving') : t('common.save')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExplanationPreferencesForm;
