import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { WalletIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import Notification, { NotificationType } from '../ui/Notification';
import TransactionHistory from './TransactionHistory';

interface WalletProps {
  onTransactionComplete: (amount: number) => void;
  requiredAmount: number;
}

interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal';
  amount: number;
  date: Date;
  status: 'completed' | 'pending' | 'failed';
}

const Wallet: React.FC<WalletProps> = ({ onTransactionComplete, requiredAmount }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [balance, setBalance] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [notification, setNotification] = useState<{
    type: NotificationType;
    message: string;
  } | null>(null);

  const showNotification = (type: NotificationType, message: string) => {
    setNotification({ type, message });
  };

  const connectWallet = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulation de connexion au wallet
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Simulation de récupération du solde
      const mockBalance = Math.random() * 1000;
      setBalance(mockBalance);
      setIsConnected(true);
      showNotification('success', 'Wallet connecté avec succès');
    } catch (err) {
      setError('Erreur lors de la connexion au wallet');
      showNotification('error', 'Erreur lors de la connexion au wallet');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePayment = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (balance < requiredAmount) {
        setError('Solde insuffisant');
        showNotification('error', 'Solde insuffisant pour effectuer la transaction');
        return;
      }

      // Simulation de transaction
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mise à jour du solde
      setBalance((prev) => prev - requiredAmount);

      // Ajout de la transaction à l'historique
      const newTransaction: Transaction = {
        id: Math.random().toString(36).substr(2, 9),
        type: 'withdrawal',
        amount: requiredAmount,
        date: new Date(),
        status: 'completed',
      };

      setTransactions((prev) => [newTransaction, ...prev]);
      showNotification('success', 'Paiement effectué avec succès');
      onTransactionComplete(requiredAmount);
    } catch (err) {
      setError('Erreur lors du paiement');
      showNotification('error', 'Erreur lors du paiement');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='space-y-6'>
      <div className='bg-white rounded-lg shadow-md p-6'>
        <div className='flex items-center justify-between mb-4'>
          <h3 className='text-lg font-semibold'>Wallet RandB</h3>
          {isConnected ? (
            <span className='text-sm text-green-600'>Connecté</span>
          ) : (
            <button
              onClick={connectWallet}
              disabled={isLoading}
              className='px-4 py-2 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark disabled:opacity-50'
            >
              {isLoading ? 'Connexion...' : 'Connecter le wallet'}
            </button>
          )}
        </div>

        {isConnected && (
          <div className='space-y-4'>
            <div className='bg-gray-50 p-4 rounded-lg'>
              <p className='text-sm text-gray-600'>Solde disponible</p>
              <p className='text-2xl font-bold text-gray-900'>{balance.toFixed(4)} RandB</p>
            </div>

            <div className='bg-gray-50 p-4 rounded-lg'>
              <p className='text-sm text-gray-600'>Montant requis</p>
              <p className='text-2xl font-bold text-gray-900'>{requiredAmount.toFixed(4)} RandB</p>
            </div>

            {error && <div className='bg-red-50 text-red-600 p-3 rounded-lg text-sm'>{error}</div>}

            <button
              onClick={handlePayment}
              disabled={isLoading || balance < requiredAmount}
              className='w-full px-4 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark disabled:opacity-50 flex items-center justify-center gap-2'
            >
              {isLoading ? (
                <>
                  <ArrowPathIcon className='w-5 h-5 animate-spin' />
                  Transaction en cours...
                </>
              ) : (
                <>
                  <WalletIcon className='w-5 h-5' />
                  Payer avec RandB
                </>
              )}
            </button>
          </div>
        )}
      </div>

      {notification && (
        <Notification
          type={notification.type}
          message={notification.message}
          onClose={() => setNotification(null)}
        />
      )}
    </div>
  );
};

export default Wallet;
