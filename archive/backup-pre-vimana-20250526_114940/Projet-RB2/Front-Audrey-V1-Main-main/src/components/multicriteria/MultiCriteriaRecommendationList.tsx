import React from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { MultiCriteriaScore, OptimizationMethod } from '../../services/api/multiCriteriaRecommendationService';

interface MultiCriteriaRecommendationListProps {
  recommendations: any[];
  optimizationMethod: OptimizationMethod;
  onViewDetails?: (recommendation: any) => void;
  className?: string;
}

/**
 * Composant pour afficher une liste de recommandations multi-critères
 */
const MultiCriteriaRecommendationList: React.FC<MultiCriteriaRecommendationListProps> = ({
  recommendations,
  optimizationMethod,
  onViewDetails,
  className = '',
}) => {
  const { t } = useTranslation();
  
  // Fonction pour formater un score en pourcentage
  const formatScore = (score: number): string => {
    return `${Math.round(score * 100)}%`;
  };
  
  // Fonction pour obtenir la couleur d'un score
  const getScoreColor = (score: number): string => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-green-500';
    if (score >= 0.4) return 'text-yellow-500';
    if (score >= 0.2) return 'text-orange-500';
    return 'text-red-500';
  };
  
  // Fonction pour obtenir l'icône de direction d'optimisation
  const getDirectionIcon = (direction: string): JSX.Element => {
    if (direction === 'maximize') {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block ml-1" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
        </svg>
      );
    } else {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block ml-1" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      );
    }
  };
  
  // Fonction pour obtenir le badge de Pareto
  const getParetoOptimalBadge = (isParetoOptimal: boolean): JSX.Element | null => {
    if (!isParetoOptimal) return null;
    
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">
        {t('multiCriteria.paretoOptimal')}
      </span>
    );
  };
  
  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="p-4 border-b">
        <h3 className="text-lg font-semibold">{t('multiCriteria.recommendations')}</h3>
        <p className="text-sm text-gray-600 mt-1">
          {t('multiCriteria.optimizedUsing')} {t(`multiCriteria.methods.${optimizationMethod}`)}
        </p>
      </div>
      
      {recommendations.length === 0 ? (
        <div className="p-8 text-center">
          <p className="text-gray-500">{t('multiCriteria.noRecommendations')}</p>
        </div>
      ) : (
        <div className="divide-y">
          {recommendations.map((recommendation) => {
            const multiCriteriaScore: MultiCriteriaScore = recommendation.multiCriteriaScore;
            
            return (
              <div key={recommendation.id} className="p-4 hover:bg-gray-50 transition-colors">
                <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                  {/* Informations sur la retraite */}
                  <div className="flex-1">
                    <h4 className="text-lg font-medium">
                      {recommendation.title}
                      {getParetoOptimalBadge(multiCriteriaScore.isParetoOptimal)}
                    </h4>
                    
                    <div className="flex flex-wrap items-center text-sm text-gray-600 mt-1 gap-x-4">
                      {recommendation.location && (
                        <div>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          {recommendation.location}
                        </div>
                      )}
                      
                      {recommendation.startDate && (
                        <div>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          {new Date(recommendation.startDate).toLocaleDateString()}
                        </div>
                      )}
                      
                      {recommendation.price !== undefined && (
                        <div>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {recommendation.price} €
                        </div>
                      )}
                    </div>
                    
                    <p className="mt-2 text-sm line-clamp-2">
                      {recommendation.description}
                    </p>
                  </div>
                  
                  {/* Score global */}
                  <div className="mt-4 md:mt-0 md:ml-6 flex flex-col items-center">
                    <div className="text-center">
                      <div className="text-xs font-medium text-gray-500 uppercase">
                        {t('multiCriteria.globalScore')}
                      </div>
                      <div className={`text-2xl font-bold ${getScoreColor(multiCriteriaScore.globalScore)}`}>
                        {formatScore(multiCriteriaScore.globalScore)}
                      </div>
                    </div>
                    
                    {onViewDetails && (
                      <button
                        onClick={() => onViewDetails(recommendation)}
                        className="mt-2 px-3 py-1 text-sm bg-retreat-green text-white rounded hover:bg-retreat-green-dark transition-colors"
                      >
                        {t('multiCriteria.viewDetails')}
                      </button>
                    )}
                  </div>
                </div>
                
                {/* Scores par critère */}
                <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                  {multiCriteriaScore.criteriaScores.map((criteriaScore) => (
                    <div
                      key={criteriaScore.criterionId}
                      className="bg-gray-50 rounded p-2 text-sm"
                    >
                      <div className="font-medium flex items-center">
                        {criteriaScore.criterionId}
                        {getDirectionIcon(criteriaScore.criterionId === 'price' || criteriaScore.criterionId === 'distance' ? 'minimize' : 'maximize')}
                      </div>
                      <div className={`${getScoreColor(criteriaScore.normalizedScore)}`}>
                        {formatScore(criteriaScore.normalizedScore)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default MultiCriteriaRecommendationList;
