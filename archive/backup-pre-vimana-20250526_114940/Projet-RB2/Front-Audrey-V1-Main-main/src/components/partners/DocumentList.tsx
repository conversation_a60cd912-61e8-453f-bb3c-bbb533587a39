import React from 'react';
import { PartnerDocument } from '../../services/api/partnerService';
import { partnerService } from '../../services/api/partnerService';
import { toast } from 'react-toastify';

// Document types mapping
const DOCUMENT_TYPES: Record<string, string> = {
  'IDENTITY': 'Pièce d\'identité',
  'BUSINESS_REGISTRATION': 'Immatriculation d\'entreprise',
  'INSURANCE': 'Attestation d\'assurance',
  'CERTIFICATION': 'Certification professionnelle',
  'TAX_DOCUMENT': 'Document fiscal',
  'BANK_DETAILS': 'Coordonnées bancaires',
  'OTHER': 'Autre document',
};

interface DocumentListProps {
  documents: PartnerDocument[];
  onDocumentDeleted: () => void;
}

const DocumentList: React.FC<DocumentListProps> = ({ documents, onDocumentDeleted }) => {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  // Handle document deletion
  const handleDelete = async (documentId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) {
      try {
        await partnerService.deleteDocument(documentId);
        toast.success('Document supprimé avec succès');
        onDocumentDeleted();
      } catch (error) {
        console.error('Error deleting document:', error);
        toast.error('Une erreur est survenue lors de la suppression du document');
      }
    }
  };

  if (documents.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        Aucun document n'a été ajouté
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Type
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Description
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Date d'ajout
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {documents.map((document) => (
            <tr key={document.id}>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="text-sm text-gray-900">
                  {DOCUMENT_TYPES[document.type] || document.type}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="text-sm text-gray-900">{document.description}</span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="text-sm text-gray-500">
                  {formatDate(document.createdAt)}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  onClick={() => handleDelete(document.id)}
                  className="text-red-600 hover:text-red-900"
                >
                  Supprimer
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DocumentList;
