import React from 'react';
import { motion } from 'framer-motion';
import { FaStar } from 'react-icons/fa';
import Card from '../atoms/Card';

const retreatsData = [
  {
    id: 1,
    title: 'Retraite Yoga & Méditation',
    location: 'Provence, France',
    price: 299,
    rating: 4.9,
    reviews: 128,
    image: 'images/retreat-1.jpg',
    secondaryImage: 'images/retreat-1-alt.jpg',
    description: 'Une expérience unique de bien-être dans un cadre exceptionnel.',
    features: ['7 jours immersifs', 'Pension complète', 'Cours quotidiens'],
  },
  {
    id: 2,
    title: 'Séjour Bien-être & Spa',
    location: 'Alpes, Suisse',
    price: 399,
    rating: 4.8,
    reviews: 96,
    image: 'images/retreat-2.jpg',
    secondaryImage: 'images/retreat-2-alt.jpg',
    description: 'Ressourcez-vous dans un cadre alpin majestueux.',
    features: ['5 jours de détente', 'Spa illimité', 'Soins inclus'],
  },
  {
    id: 3,
    title: 'Retraite Mindfulness',
    location: 'Toscane, Italie',
    price: 349,
    rating: 4.7,
    reviews: 84,
    image: 'images/retreat-3.jpg',
    secondaryImage: 'images/retreat-3-alt.jpg',
    description: 'Découvrez la pleine conscience sous le soleil italien.',
    features: ['6 jours de pratique', 'Cuisine bio', 'Méditation guidée'],
  },
  {
    id: 4,
    title: 'Détox & Yoga',
    location: 'Costa Brava, Espagne',
    price: 329,
    rating: 4.9,
    reviews: 112,
    image: 'images/retreat-4.jpg',
    secondaryImage: 'images/retreat-4-alt.jpg',
    description: 'Purifiez corps et esprit face à la Méditerranée.',
    features: ['8 jours complets', 'Programme détox', 'Yoga quotidien'],
  },
  {
    id: 5,
    title: 'Retraite Holistique',
    location: 'Ardèche, France',
    price: 289,
    rating: 4.8,
    reviews: 76,
    image: 'images/retreat-5.jpg',
    secondaryImage: 'images/retreat-5-alt.jpg',
    description: 'Une approche globale du bien-être en pleine nature.',
    features: ['5 jours intensifs', 'Ateliers variés', 'Hébergement eco'],
  },
  {
    id: 6,
    title: 'Méditation & Nature',
    location: 'Forêt Noire, Allemagne',
    price: 379,
    rating: 4.9,
    reviews: 92,
    image: 'images/retreat-6.jpg',
    secondaryImage: 'images/retreat-6-alt.jpg',
    description: 'Reconnectez-vous à la nature profonde.',
    features: ['7 jours immersifs', 'Randonnées guidées', 'Méditation en forêt'],
  },
];

const Retreats: React.FC = () => {
  const handleFavoriteClick = (retreatId: string, isFavorite: boolean) => {
    // Ici vous pouvez ajouter la logique pour sauvegarder les favoris
    console.log(`Retreat ${retreatId} ${isFavorite ? 'added to' : 'removed from'} favorites`);
  };

  const StarIcon = FaStar as React.ComponentType<{ className: string }>;

  return (
    <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-fr'>
        {retreatsData.map((retreat, index) => (
          <motion.div
            key={retreat.id}
            className='h-full'
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card
              images={[
                {
                  src: retreat.image,
                  alt: retreat.title,
                },
                {
                  src: retreat.secondaryImage,
                  alt: `${retreat.title} - Vue alternative`,
                },
              ]}
              title={retreat.title}
              subtitle={retreat.location}
              description={retreat.description}
              features={retreat.features}
              onFavoriteClick={(isFavorite) =>
                handleFavoriteClick(retreat.id.toString(), isFavorite)
              }
            >
              <div className='mt-4'>
                <div className='flex items-center justify-between mb-4'>
                  <div className='flex items-center'>
                    <StarIcon className='h-5 w-5 text-yellow-400' />
                    <span className='ml-1 text-gray-600'>
                      {retreat.rating} ({retreat.reviews} avis)
                    </span>
                  </div>
                  <span className='text-xl font-bold text-retreat-green'>{retreat.price}€</span>
                </div>
                <button className='w-full bg-retreat-green text-white py-2 px-4 rounded-lg hover:bg-opacity-90 transition-colors duration-300'>
                  Réserver
                </button>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default Retreats;
