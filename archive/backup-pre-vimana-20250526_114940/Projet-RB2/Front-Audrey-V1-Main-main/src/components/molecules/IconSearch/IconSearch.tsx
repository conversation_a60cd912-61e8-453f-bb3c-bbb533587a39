import React, { useRef, useState } from 'react';
import {
  GlobeEuropeAfricaIcon,
  GlobeAmericasIcon,
  GlobeAsiaAustraliaIcon,
  HeartIcon,
  SparklesIcon,
  SunIcon,
  UserGroupIcon,
  BookOpenIcon,
  CloudIcon,
  MoonIcon,
  HomeIcon,
  FireIcon,
  CakeIcon,
  CameraIcon,
  PaintBrushIcon,
  MusicalNoteIcon,
  MapIcon,
  BoltIcon,
  BanknotesIcon,
  RocketLaunchIcon,
} from '@heroicons/react/24/outline';
import ScrollButton from '../../atoms/Button/ScrollButton';
import SearchCategory from '../SearchCategory/SearchCategory';
import type { ComponentProps } from 'react';

interface SearchCategory {
  icon: React.ComponentType<ComponentProps<'svg'>>;
  label: string;
  href: string;
}

const categories: SearchCategory[] = [
  // ... existing categories array ...
];

const IconSearch: React.FC = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const scrollAmount = 200;
      const newScrollPosition =
        scrollContainerRef.current.scrollLeft +
        (direction === 'left' ? -scrollAmount : scrollAmount);

      scrollContainerRef.current.scrollTo({
        left: newScrollPosition,
        behavior: 'smooth',
      });
    }
  };

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  return (
    <div className='bg-white shadow-sm pt-4'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative'>
        {showLeftArrow && (
          <ScrollButton
            direction='left'
            onClick={() => scroll('left')}
            ariaLabel='Défiler vers la gauche'
          />
        )}

        {showRightArrow && (
          <ScrollButton
            direction='right'
            onClick={() => scroll('right')}
            ariaLabel='Défiler vers la droite'
          />
        )}

        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className='flex items-center space-x-8 overflow-x-auto hide-scrollbar py-2 px-6'
        >
          {categories.map((category) => (
            <SearchCategory
              key={category.label}
              icon={category.icon}
              label={category.label}
              href={category.href}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default IconSearch;
