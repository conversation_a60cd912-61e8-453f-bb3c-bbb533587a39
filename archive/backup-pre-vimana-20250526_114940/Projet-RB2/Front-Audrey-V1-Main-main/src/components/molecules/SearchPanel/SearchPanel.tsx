import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import DatePicker, { registerLocale } from 'react-datepicker';
import { fr } from 'date-fns/locale';
import Icon from '../../atoms/Icon/Icon';
import { HomeIcon, SparklesIcon, PhoneXMarkIcon } from '@heroicons/react/24/outline';
import type { DatePickerProps } from 'react-datepicker';

registerLocale('fr', fr);

interface SearchPanelProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

interface Preset {
  id: string;
  label: string;
  icon: React.ComponentType<React.ComponentProps<'svg'>>;
  filters: {
    themes: string[];
    adults?: number;
    children?: number;
    dietaryRestrictions?: string[];
    wellnessPreferences?: string[];
  };
}

const presets: Preset[] = [
  {
    id: 'family-wellness',
    label: 'Séjour Famille & Bien-être',
    icon: HomeIcon,
    filters: {
      themes: ['yoga', 'wellness'],
      adults: 2,
      children: 2,
      dietaryRestrictions: ['family-friendly'],
    },
  },
  {
    id: 'detox-retreat',
    label: 'Cure Détox Premium',
    icon: SparklesIcon,
    filters: {
      themes: ['detox', 'wellness'],
      dietaryRestrictions: ['gluten-free', 'vegan'],
    },
  },
  {
    id: 'digital-detox',
    label: 'Digital Detox Nature',
    icon: PhoneXMarkIcon,
    filters: {
      themes: ['meditation', 'nature'],
      wellnessPreferences: ['silence'],
    },
  },
];

const SearchPanel: React.FC<SearchPanelProps> = ({ isOpen, onClose, className = '' }) => {
  const [selectedDates, setSelectedDates] = useState<[Date | null, Date | null]>([null, null]);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string>>({});
  const [priceRange, setPriceRange] = useState([500, 2000]);
  const [flexibleDates, setFlexibleDates] = useState(false);
  const [selectedThemes, setSelectedThemes] = useState<string[]>([]);
  const [groupSize, setGroupSize] = useState(1);
  const [adults, setAdults] = useState(1);
  const [children, setChildren] = useState(0);

  const handleDateChange: DatePickerProps['onChange'] = (dates: [Date | null, Date | null]) => {
    if (dates) {
      setSelectedDates(dates);
    }
  };

  const handlePresetClick = (preset: Preset) => {
    setSelectedThemes(preset.filters.themes);
    if (preset.filters.adults) setAdults(preset.filters.adults);
    if (preset.filters.children) setChildren(preset.filters.children);
    // Appliquer d'autres filtres selon le preset
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className={`fixed top-16 left-0 right-0 bg-white shadow-lg z-50 ${className}`}
        >
          <div className='max-w-7xl mx-auto p-4'>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              {/* Presets */}
              <div className='col-span-1'>
                <h3 className='text-lg font-semibold mb-4'>Suggestions populaires</h3>
                <div className='space-y-3'>
                  {presets.map((preset) => (
                    <button
                      key={preset.id}
                      onClick={() => handlePresetClick(preset)}
                      className='w-full flex items-center p-3 rounded-lg border border-gray-200 hover:border-retreat-green transition-colors duration-200'
                    >
                      <Icon icon={preset.icon} size='sm' className='mr-3 text-retreat-green' />
                      <span className='text-sm text-gray-700'>{preset.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Filtres principaux */}
              <div className='col-span-2'>
                <div className='grid grid-cols-2 gap-4'>
                  {/* Sélecteur de dates */}
                  <div className='col-span-2'>
                    <label className='block text-sm font-medium text-gray-700 mb-2'>Dates</label>
                    <DatePicker
                      selected={selectedDates[0]}
                      onChange={handleDateChange}
                      startDate={selectedDates[0]}
                      endDate={selectedDates[1]}
                      selectsRange
                      locale='fr'
                      dateFormat='dd/MM/yyyy'
                      className='w-full p-2 border border-gray-300 rounded-md'
                      placeholderText='Sélectionner les dates'
                    />
                  </div>

                  {/* Autres filtres à implémenter */}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SearchPanel;
