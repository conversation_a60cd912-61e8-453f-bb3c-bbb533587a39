import React from 'react';
import { ChatMessage } from '../../../hooks/useChatbot';
import { MessageType, Button } from '../../../services/api/chatbotService';
import TextMessage from './message-types/TextMessage';
import ButtonsMessage from './message-types/ButtonsMessage';
import ImageMessage from './message-types/ImageMessage';
import QuickRepliesMessage from './message-types/QuickRepliesMessage';
import CardMessage from './message-types/CardMessage';
import CarouselMessage from './message-types/CarouselMessage';
import AudioMessage from './message-types/AudioMessage';
import VideoMessage from './message-types/VideoMessage';
import FileMessage from './message-types/FileMessage';
import LocationMessage from './message-types/LocationMessage';
import TypingIndicator from './message-types/TypingIndicator';

interface MessageContentProps {
  message: ChatMessage;
  onButtonClick?: (button: Button) => void;
  onFeedback?: (messageId: string, isPositive: boolean) => void;
}

/**
 * Component to render different types of message content
 */
const MessageContent: React.FC<MessageContentProps> = ({ message, onButtonClick, onFeedback }) => {
  // Default to text message if no type is specified
  const messageType = message.messageType || MessageType.TEXT;

  // Render different message types
  switch (messageType) {
    case MessageType.TEXT:
      return <TextMessage message={message} onFeedback={onFeedback} />;

    case MessageType.BUTTONS:
      return (
        <ButtonsMessage message={message} onButtonClick={onButtonClick} onFeedback={onFeedback} />
      );

    case MessageType.IMAGE:
      return <ImageMessage message={message} onFeedback={onFeedback} />;

    case MessageType.QUICK_REPLIES:
      return (
        <QuickRepliesMessage
          message={message}
          onButtonClick={onButtonClick}
          onFeedback={onFeedback}
        />
      );

    case MessageType.CARD:
      return (
        <CardMessage message={message} onButtonClick={onButtonClick} onFeedback={onFeedback} />
      );

    case MessageType.CAROUSEL:
      return (
        <CarouselMessage message={message} onButtonClick={onButtonClick} onFeedback={onFeedback} />
      );

    case MessageType.AUDIO:
      return <AudioMessage message={message} onFeedback={onFeedback} />;

    case MessageType.VIDEO:
      return <VideoMessage message={message} onFeedback={onFeedback} />;

    case MessageType.FILE:
      return <FileMessage message={message} onFeedback={onFeedback} />;

    case MessageType.LOCATION:
      return <LocationMessage message={message} onFeedback={onFeedback} />;

    case MessageType.TYPING:
      return <TypingIndicator />;

    default:
      return <TextMessage message={message} onFeedback={onFeedback} />;
  }
};

export default MessageContent;
