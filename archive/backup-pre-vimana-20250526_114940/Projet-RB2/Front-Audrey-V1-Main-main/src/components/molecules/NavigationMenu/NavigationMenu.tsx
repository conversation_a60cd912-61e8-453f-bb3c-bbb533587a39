import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import Icon from '../../atoms/Icon/Icon';
import { ChevronRightIcon } from '@heroicons/react/24/outline';

interface MenuItem {
  label: string;
  href: string;
  subItems?: MenuItem[];
}

interface NavigationMenuProps {
  items: MenuItem[];
  className?: string;
}

const NavigationMenu: React.FC<NavigationMenuProps> = ({ items, className = '' }) => {
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);

  const toggleSubmenu = (label: string) => {
    setActiveSubmenu(activeSubmenu === label ? null : label);
  };

  const renderMenuItem = (item: MenuItem, depth = 0) => {
    const hasSubItems = item.subItems && item.subItems.length > 0;
    const isActive = activeSubmenu === item.label;

    return (
      <li key={item.label} className={`relative ${depth > 0 ? 'pl-4' : ''}`}>
        <div className='flex items-center justify-between'>
          <Link
            to={item.href}
            className='block py-2 text-gray-700 hover:text-retreat-green transition-colors duration-200'
          >
            {item.label}
          </Link>
          {hasSubItems && (
            <button
              onClick={() => toggleSubmenu(item.label)}
              className='p-1 hover:bg-gray-100 rounded-full transition-colors duration-200'
              aria-expanded={isActive}
            >
              <Icon
                icon={ChevronRightIcon}
                size='sm'
                className={`transform transition-transform duration-200 ${
                  isActive ? 'rotate-90' : ''
                }`}
              />
            </button>
          )}
        </div>

        {hasSubItems && (
          <AnimatePresence>
            {isActive && (
              <motion.ul
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className='overflow-hidden'
              >
                {item.subItems!.map((subItem) => renderMenuItem(subItem, depth + 1))}
              </motion.ul>
            )}
          </AnimatePresence>
        )}
      </li>
    );
  };

  return (
    <nav className={`${className}`}>
      <ul className='space-y-1'>{items.map((item) => renderMenuItem(item))}</ul>
    </nav>
  );
};

export default NavigationMenu;
