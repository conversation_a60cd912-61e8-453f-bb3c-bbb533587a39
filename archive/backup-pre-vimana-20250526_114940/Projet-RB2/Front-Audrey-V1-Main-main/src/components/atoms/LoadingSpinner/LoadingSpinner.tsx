import React from 'react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  color = 'retreat-green',
}) => {
  const sizeClasses = {
    small: 'h-4 w-4 border-2',
    medium: 'h-8 w-8 border-2',
    large: 'h-12 w-12 border-3',
  };

  const colorClasses = {
    'retreat-green': 'border-retreat-green',
    'white': 'border-white',
    'gray': 'border-gray-300',
  };

  return (
    <div
      className={`animate-spin rounded-full ${sizeClasses[size]} border-t-transparent ${
        colorClasses[color as keyof typeof colorClasses] || `border-${color}`
      }`}
    ></div>
  );
};

export default LoadingSpinner;
