import React from 'react';
import Icon from '../Icon/Icon';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface ScrollButtonProps {
  direction: 'left' | 'right';
  onClick: () => void;
  className?: string;
  ariaLabel: string;
}

const ScrollButton: React.FC<ScrollButtonProps> = ({
  direction,
  onClick,
  className = '',
  ariaLabel,
}) => {
  const IconComponent = direction === 'left' ? ChevronLeftIcon : ChevronRightIcon;

  return (
    <button
      onClick={onClick}
      className={`absolute ${direction}-0 top-1/2 -translate-y-1/2 z-10 
        bg-white/80 backdrop-blur-sm rounded-full p-1 
        shadow-md hover:bg-white transition-all duration-200 
        ${className}`}
      aria-label={ariaLabel}
    >
      <Icon icon={IconComponent} size='md' color='text-retreat-green' />
    </button>
  );
};

export default ScrollButton;
