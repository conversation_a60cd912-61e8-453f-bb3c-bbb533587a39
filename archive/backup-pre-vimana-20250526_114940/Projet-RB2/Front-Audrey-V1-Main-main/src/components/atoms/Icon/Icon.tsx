import React from 'react';
import type { ComponentProps } from 'react';

interface IconComponentProps {
  icon: React.ComponentType<ComponentProps<'svg'>>;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

const Icon: React.FC<IconComponentProps> = ({
  icon: IconComponent,
  className = '',
  size = 'md',
  color = 'text-retreat-green',
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <IconComponent className={`${sizeClasses[size]} ${color} ${className}`} aria-hidden='true' />
  );
};

export default Icon;
