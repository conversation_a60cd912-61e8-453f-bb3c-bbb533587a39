# Atomes (Atoms)

Les atomes sont les composants les plus basiques et indivisibles de notre application. Ils représentent les éléments d'interface utilisateur les plus simples qui ne peuvent pas être décomposés davantage.

## Structure des dossiers

- `Button/` : Boutons de base et variantes

  - `Button.tsx` : Bouton standard
  - `IconButton.tsx` : Bouton avec icône

- `Input/` : Champs de saisie

  - `Input.tsx` : Champ de saisie standard
  - `SearchInput.tsx` : Champ de recherche spécialisé

- `Icon/` : Composants d'icônes

  - `IconSearch.tsx` : Icônes de recherche
  - `SocialIcon.tsx` : Icônes sociales

- `Typography/` : Composants textuels

  - `Heading.tsx` : Titres
  - `Text.tsx` : Texte standard

- `Image/` : Composants d'images

  - `LazyImage.tsx` : Image avec chargement différé

- `Badge/` : Badges et étiquettes
  - `Badge.tsx` : Badge standard

## Règles d'utilisation

1. Les atomes doivent être :

   - Indépendants (pas de dépendances à d'autres composants)
   - Réutilisables
   - Sans état (stateless) quand c'est possible
   - Stylisés de manière cohérente

2. Chaque atome doit :
   - Avoir ses propres tests
   - Être documenté avec des props typées
   - Inclure des exemples d'utilisation
   - Suivre les conventions de nommage
