import React, { useState, useEffect } from 'react';
import { ReportList } from './ReportList';
import { ModerationStats } from './ModerationStats';
import { ModerationRules } from './ModerationRules';
import { useTranslation } from '../../hooks/useTranslation';
import { moderationService } from '../../services/api/moderationService';
import { FadeIn } from '../ui/FadeIn';
import { SkeletonText } from '../ui/Skeleton';

export interface ModerationDashboardProps {
  userRole: 'admin' | 'moderator' | 'user';
}

export const ModerationDashboard: React.FC<ModerationDashboardProps> = ({ userRole }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'reports' | 'rules' | 'stats'>('reports');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);
        const response = await moderationService.getStats();
        setStats(response);
      } catch (error) {
        console.error('Error fetching moderation stats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  const renderTabContent = () => {
    if (isLoading) {
      return (
        <div className="py-8">
          <SkeletonText lines={1} lineHeight="1.5rem" className="w-64 mb-6" />
          <SkeletonText lines={4} lineHeight="1rem" className="mb-4" />
          <SkeletonText lines={3} lineHeight="1rem" className="w-3/4" />
        </div>
      );
    }

    switch (activeTab) {
      case 'reports':
        return (
          <FadeIn>
            <ReportList userRole={userRole} />
          </FadeIn>
        );
      case 'rules':
        return (
          <FadeIn>
            <ModerationRules userRole={userRole} />
          </FadeIn>
        );
      case 'stats':
        return (
          <FadeIn>
            <ModerationStats stats={stats} isLoading={false} />
          </FadeIn>
        );
      default:
        return (
          <FadeIn>
            <ReportList userRole={userRole} />
          </FadeIn>
        );
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <FadeIn direction="down" duration={600}>
        <h1 className="text-3xl font-bold mb-6">{t('moderation.dashboard.title')}</h1>
      </FadeIn>

      <FadeIn direction="up" duration={800} delay={200}>
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="flex border-b">
            <button
              className={`px-6 py-3 text-sm font-medium transition-colors duration-300 ${
                activeTab === 'reports'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('reports')}
            >
              {t('moderation.dashboard.tabs.reports')}
            </button>

            {(userRole === 'admin' || userRole === 'moderator') && (
              <button
                className={`px-6 py-3 text-sm font-medium transition-colors duration-300 ${
                  activeTab === 'rules'
                    ? 'text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setActiveTab('rules')}
              >
                {t('moderation.dashboard.tabs.rules')}
              </button>
            )}

            {(userRole === 'admin') && (
              <button
                className={`px-6 py-3 text-sm font-medium transition-colors duration-300 ${
                  activeTab === 'stats'
                    ? 'text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setActiveTab('stats')}
              >
                {t('moderation.dashboard.tabs.stats')}
              </button>
            )}
          </div>

          <div className="p-6">
            {renderTabContent()}
          </div>
        </div>
      </FadeIn>
    </div>
  );
};

export default ModerationDashboard;
