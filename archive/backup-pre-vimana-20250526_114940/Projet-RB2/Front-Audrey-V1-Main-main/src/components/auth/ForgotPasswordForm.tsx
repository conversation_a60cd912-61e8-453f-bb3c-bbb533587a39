import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '../../contexts/AuthContext';

const forgotPasswordSchema = z.object({
  email: z.string().email({ message: 'Adresse e-mail invalide' }),
});

type ForgotPasswordFormInputs = z.infer<typeof forgotPasswordSchema>;

export const ForgotPasswordForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ForgotPasswordFormInputs>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const { requestPasswordReset, error: authError, clearError } = useAuth();
  const [message, setMessage] = useState<string | null>(null);

  const onSubmit = async (data: ForgotPasswordFormInputs) => {
    clearError();
    setMessage(null);
    try {
      await requestPasswordReset(data.email);
      setMessage('Si un compte existe pour cet e-mail, un lien de réinitialisation a été envoyé.');
    } catch (err) {
      // AuthError will be set by AuthContext, or we can set a generic one here
      // setMessage('Une erreur est survenue. Veuillez réessayer.');
      console.error('Forgot password failed:', err);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className='space-y-6 w-full max-w-md p-8 bg-white shadow-xl rounded-lg'>
      <h2 className='text-3xl font-bold text-center text-gray-800'>Mot de passe oublié</h2>
      
      {message && (
        <div role='status' className='p-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg'>
          {message}
        </div>
      )}
      {authError && !message && ( // Display authError only if no specific success/info message is shown
        <div role='alert' className='p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg'>
          {authError}
        </div>
      )}

      <div>
        <label htmlFor='email' className='block text-sm font-medium text-gray-700 mb-1'>
          Adresse E-mail
        </label>
        <input
          id='email'
          type='email'
          {...register('email')}
          className={`mt-1 block w-full px-4 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          aria-invalid={errors.email ? 'true' : 'false'}
        />
        {errors.email && (
          <p role='alert' className='mt-2 text-sm text-red-600'>
            {errors.email.message}
          </p>
        )}
      </div>

      <div>
        <button
          type='submit'
          disabled={isSubmitting}
          className='w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50'
        >
          {isSubmitting ? 'Envoi en cours...' : 'Envoyer le lien de réinitialisation'}
        </button>
      </div>
    </form>
  );
}; 