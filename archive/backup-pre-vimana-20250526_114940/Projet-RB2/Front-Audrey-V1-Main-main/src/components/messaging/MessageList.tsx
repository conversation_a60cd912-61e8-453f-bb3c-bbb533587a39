import React, { useEffect, useRef } from 'react';
import { useMessagingContext } from '../../contexts/MessagingContext';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { IMessage, IReaction, ParticipantLike } from '../../services/api/messagingService';

// Remove or comment out the local Participant type if ParticipantLike is sufficient
// interface Participant {
//   id: string;
//   firstName?: string;
//   lastName?: string;
//   image?: string;
// }

interface MessageListProps {
  conversationId: string;
}

const MessageList: React.FC<MessageListProps> = ({ conversationId }) => {
  const { messages, loadMessages, loading, currentConversation, typingUsers } =
    useMessagingContext();

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const userId = localStorage.getItem('userId');

  useEffect(() => {
    if (conversationId) {
      loadMessages(conversationId);
    }
  }, [conversationId, loadMessages]);

  useEffect(() => {
    // Scroll to bottom when messages change
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const formatMessageTime = (timestamp: string) => {
    return format(new Date(timestamp), 'HH:mm', { locale: fr });
  };

  const formatMessageDate = (timestamp: string) => {
    return format(new Date(timestamp), 'EEEE d MMMM', { locale: fr });
  };

  const handleLoadMore = () => {
    if (messages.length > 0 && conversationId) {
      const oldestMessage = messages[messages.length - 1];
      loadMessages(conversationId, { before: oldestMessage.sentAt });
    }
  };

  const renderMessageContent = (message: IMessage) => {
    switch (message.type) {
      case 'IMAGE':
        return <img src={message.content} alt='Message' className='max-w-xs rounded-lg' />;
      case 'FILE':
        return (
          <div className='flex items-center p-2 bg-gray-100 rounded-lg'>
            <svg
              className='w-6 h-6 mr-2'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
              />
            </svg>
            <a
              href={message.content}
              target='_blank'
              rel='noopener noreferrer'
              className='text-blue-500 underline'
            >
              {typeof message.metadata?.fileName === 'string' && message.metadata.fileName
                ? message.metadata.fileName
                : 'Télécharger le fichier'}
            </a>
          </div>
        );
      default:
        return <p>{message.content}</p>;
    }
  };

  const renderTypingIndicator = () => {
    if (!currentConversation) return null;

    const typingUserIds = typingUsers[currentConversation.id] || [];
    if (typingUserIds.length === 0) return null;

    const typingParticipants = currentConversation.participants.filter(
      (p: ParticipantLike) => typingUserIds.includes(p.id) && p.id !== userId
    );

    if (typingParticipants.length === 0) return null;

    let typingText = '';
    if (typingParticipants.length === 1) {
      typingText = `${typingParticipants[0].firstName} est en train d'écrire...`;
    } else if (typingParticipants.length === 2) {
      typingText = `${typingParticipants[0].firstName} et ${typingParticipants[1].firstName} sont en train d'écrire...`;
    } else {
      typingText = "Plusieurs personnes sont en train d'écrire...";
    }

    return (
      <div className='flex items-center p-2 text-gray-500 text-sm'>
        <div className='flex space-x-1 mr-2'>
          <div
            className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'
            style={{ animationDelay: '0ms' }}
          ></div>
          <div
            className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'
            style={{ animationDelay: '150ms' }}
          ></div>
          <div
            className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'
            style={{ animationDelay: '300ms' }}
          ></div>
        </div>
        <span>{typingText}</span>
      </div>
    );
  };

  if (loading && messages.length === 0) {
    return (
      <div className='flex-1 p-4 overflow-y-auto'>
        <div className='flex justify-center items-center h-full'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-500'></div>
        </div>
      </div>
    );
  }

  // Group messages by date
  const messagesByDate: Record<string, IMessage[]> = {};
  messages.forEach((message) => {
    const date = formatMessageDate(message.sentAt);
    if (!messagesByDate[date]) {
      messagesByDate[date] = [];
    }
    messagesByDate[date].push(message);
  });

  return (
    <div ref={messagesContainerRef} className='flex-1 p-4 overflow-y-auto'>
      {messages.length === 0 ? (
        <div className='flex flex-col items-center justify-center h-full text-gray-500'>
          <p>Aucun message</p>
        </div>
      ) : (
        <>
          {messages.length >= 50 && (
            <div className='flex justify-center mb-4'>
              <button
                onClick={handleLoadMore}
                className='px-4 py-2 text-sm bg-gray-100 rounded-full hover:bg-gray-200'
              >
                Charger plus de messages
              </button>
            </div>
          )}

          {Object.entries(messagesByDate).map(([date, dateMessages]: [string, IMessage[]]) => (
            <div key={date}>
              <div className='flex justify-center my-4'>
                <div className='px-3 py-1 text-xs bg-gray-100 rounded-full'>{date}</div>
              </div>

              {dateMessages.map((message) => (
                <div
                  key={message.id}
                  className={`flex mb-4 ${message.senderId === userId ? 'justify-end' : 'justify-start'}`}
                >
                  {message.senderId !== userId && (
                    <div className='flex-shrink-0 mr-2'>
                      {message.sender.image ? (
                        <img
                          src={message.sender.image}
                          alt={`${message.sender.firstName} ${message.sender.lastName}`}
                          className='w-8 h-8 rounded-full'
                        />
                      ) : (
                        <div className='w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-semibold'>
                          {message.sender.firstName?.[0] || ''}
                          {message.sender.lastName?.[0] || ''}
                        </div>
                      )}
                    </div>
                  )}

                  <div
                    className={`max-w-xs ${message.senderId === userId ? 'order-1' : 'order-2'}`}
                  >
                    {message.senderId !== userId && (
                      <p className='text-xs text-gray-500 mb-1'>
                        <span>
                          {message.sender.firstName} {message.sender.lastName}
                        </span>
                      </p>
                    )}

                    <div
                      className={`p-3 rounded-lg ${
                        message.senderId === userId
                          ? 'bg-green-500 text-white rounded-br-none'
                          : 'bg-gray-100 text-gray-800 rounded-bl-none'
                      }`}
                    >
                      {message.replyTo && (
                        <div
                          className={`p-2 mb-2 text-xs rounded border-l-2 ${
                            message.senderId === userId
                              ? 'bg-green-600 border-white'
                              : 'bg-gray-200 border-gray-400'
                          }`}
                        >
                          <p className='font-semibold'>
                            {message.replyTo.senderId === userId
                              ? 'Vous'
                              : `${message.replyTo.sender?.firstName || ''} ${message.replyTo.sender?.lastName || ''}`}
                          </p>
                          <p className='truncate'>{message.replyTo.content}</p>
                        </div>
                      )}

                      {renderMessageContent(message)}

                      <div className='flex justify-end mt-1'>
                        <span className='text-xs opacity-70'>
                          {formatMessageTime(message.sentAt)}
                          {message.status === 'READ' && message.senderId === userId && (
                            <span className='ml-1'>✓✓</span>
                          )}
                          {message.status === 'DELIVERED' && message.senderId === userId && (
                            <span className='ml-1'>✓</span>
                          )}
                        </span>
                      </div>
                    </div>

                    {message.reactions && message.reactions.length > 0 && (
                      <div className='flex mt-1'>
                        {Array.from(
                          new Set<string>(message.reactions.map((r: IReaction) => r.emoji))
                        ).map((emoji: string) => (
                          <div
                            key={emoji}
                            className='bg-white rounded-full shadow px-2 py-1 text-xs mr-1'
                          >
                            {emoji}{' '}
                            {message.reactions!.filter((r: IReaction) => r.emoji === emoji).length}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ))}

          {renderTypingIndicator()}
          <div ref={messagesEndRef} />
        </>
      )}
    </div>
  );
};

export default MessageList;
