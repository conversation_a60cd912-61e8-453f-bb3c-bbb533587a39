import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ChartBarIcon } from '@heroicons/react/24/outline';

const RevenueCalculator: React.FC = () => {
  const [formData, setFormData] = useState({
    capacity: 10,
    pricePerPerson: 100,
    occupancyRate: 70,
    retreatsPerMonth: 2,
  });

  const [revenue, setRevenue] = useState({
    monthly: 0,
    yearly: 0,
    afterCommission: 0,
  });

  useEffect(() => {
    const calculateRevenue = () => {
      const monthlyRevenue =
        formData.capacity *
        formData.pricePerPerson *
        (formData.occupancyRate / 100) *
        formData.retreatsPerMonth;

      const yearlyRevenue = monthlyRevenue * 12;
      const afterCommission = yearlyRevenue * 0.85; // 15% de commission

      setRevenue({
        monthly: monthlyRevenue,
        yearly: yearlyRevenue,
        afterCommission,
      });
    };

    calculateRevenue();
  }, [formData]);

  return (
    <div className='bg-white rounded-2xl shadow-lg p-8'>
      <div className='flex items-center gap-3 mb-6'>
        <ChartBarIcon className='w-8 h-8 text-retreat-green' />
        <h2 className='text-2xl font-bold text-gray-900'>Calculateur de revenus</h2>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-8'>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>Capacité d'accueil</label>
          <input
            type='number'
            min='1'
            value={formData.capacity}
            onChange={(e) => setFormData({ ...formData, capacity: parseInt(e.target.value) })}
            className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
          />
        </div>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>
            Prix par personne (€)
          </label>
          <input
            type='number'
            min='0'
            value={formData.pricePerPerson}
            onChange={(e) => setFormData({ ...formData, pricePerPerson: parseInt(e.target.value) })}
            className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
          />
        </div>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>
            Taux d'occupation (%)
          </label>
          <input
            type='number'
            min='0'
            max='100'
            value={formData.occupancyRate}
            onChange={(e) => setFormData({ ...formData, occupancyRate: parseInt(e.target.value) })}
            className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
          />
        </div>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>
            Nombre de retraites par mois
          </label>
          <input
            type='number'
            min='1'
            value={formData.retreatsPerMonth}
            onChange={(e) =>
              setFormData({ ...formData, retreatsPerMonth: parseInt(e.target.value) })
            }
            className='w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green'
          />
        </div>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className='bg-gray-50 p-6 rounded-lg'
        >
          <h3 className='text-lg font-semibold mb-2'>Revenu mensuel</h3>
          <p className='text-2xl font-bold text-gray-900'>{revenue.monthly.toLocaleString()} €</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className='bg-gray-50 p-6 rounded-lg'
        >
          <h3 className='text-lg font-semibold mb-2'>Revenu annuel</h3>
          <p className='text-2xl font-bold text-gray-900'>{revenue.yearly.toLocaleString()} €</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className='bg-gray-50 p-6 rounded-lg'
        >
          <h3 className='text-lg font-semibold mb-2'>Après commission</h3>
          <p className='text-2xl font-bold text-gray-900'>
            {revenue.afterCommission.toLocaleString()} €
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default RevenueCalculator;
