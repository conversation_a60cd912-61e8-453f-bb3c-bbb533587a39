import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

interface FAQItem {
  question: string;
  answer: string;
}

const faqItems: FAQItem[] = [
  {
    question: 'Quels sont les critères pour devenir hôte ?',
    answer:
      "Pour devenir hôte, vous devez disposer d'un espace adapté aux retraites bien-être, avoir une expérience dans le domaine du bien-être ou être prêt à accueillir des praticiens, et respecter nos standards de qualité et de sécurité.",
  },
  {
    question: 'Comment sont fixés les tarifs ?',
    answer:
      'Les tarifs sont fixés par vous-même en fonction de votre espace, de sa localisation, des équipements disponibles et de la demande. Nous vous fournissons des recommandations basées sur le marché pour vous aider à fixer vos prix.',
  },
  {
    question: 'Quelle est la commission prélevée ?',
    answer:
      'Nous prélevons une commission de 15% sur chaque réservation. Cette commission couvre les frais de plateforme, le support client, et la visibilité internationale de votre espace.',
  },
  {
    question: 'Comment sont gérées les réservations ?',
    answer:
      'Les réservations sont gérées via notre plateforme. Vous recevez des notifications pour chaque demande, pouvez accepter ou refuser les réservations, et gérez votre calendrier en temps réel.',
  },
  {
    question: "Quel type d'assurance est nécessaire ?",
    answer:
      "Une assurance responsabilité civile professionnelle est obligatoire. Nous vous aidons à trouver l'assurance adaptée à votre activité d'hôte de retraite.",
  },
];

const FAQ: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  return (
    <div className='bg-white rounded-2xl shadow-lg p-8'>
      <h2 className='text-2xl font-bold text-gray-900 mb-6'>Questions fréquentes</h2>
      <div className='space-y-4'>
        {faqItems.map((item, index) => (
          <div key={index} className='border border-gray-200 rounded-lg overflow-hidden'>
            <button
              className='w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50'
              onClick={() => setOpenIndex(openIndex === index ? null : index)}
            >
              <span className='font-medium text-gray-900'>{item.question}</span>
              <ChevronDownIcon
                className={`w-5 h-5 text-gray-500 transition-transform ${
                  openIndex === index ? 'transform rotate-180' : ''
                }`}
              />
            </button>
            <AnimatePresence>
              {openIndex === index && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className='px-6 py-4 bg-gray-50'
                >
                  <p className='text-gray-600'>{item.answer}</p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FAQ;
