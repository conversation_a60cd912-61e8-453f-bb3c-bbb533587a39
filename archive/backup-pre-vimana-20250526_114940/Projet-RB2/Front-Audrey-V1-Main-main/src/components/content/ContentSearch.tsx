import React, { useState, useEffect } from 'react';
import { socialVideoService } from '../../services/api/socialVideoService';
import { socialService } from '../../services/api/socialService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface Post {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl?: string;
  createdAt: string;
  userId: string;
  userName: string;
  userAvatar: string;
  likes: number;
  comments: number;
  views: number;
  status: 'published' | 'archived' | 'deleted';
  tags: string[];
  privacy: 'public' | 'friends' | 'private';
}

interface ContentSearchProps {
  onSelectPost?: (post: Post) => void;
  userId?: string;
}

const ContentSearch: React.FC<ContentSearchProps> = ({ onSelectPost, userId }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    contentType: 'all', // 'all', 'video', 'image', 'text'
    dateRange: 'all', // 'all', 'today', 'week', 'month', 'year'
    status: 'published', // 'published', 'archived', 'deleted', 'all'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [availableTags, setAvailableTags] = useState<string[]>([]);

  // Fetch available tags on component mount
  useEffect(() => {
    const fetchTags = async () => {
      try {
        // In a real implementation, you would fetch tags from your API
        // For now, we'll use a mock list
        setAvailableTags([
          'yoga', 'méditation', 'bien-être', 'nature', 'respiration',
          'nutrition', 'randonnée', 'cuisine', 'relaxation', 'pleine conscience'
        ]);
      } catch (err) {
        console.error('Error fetching tags:', err);
      }
    };

    fetchTags();
  }, []);

  // Search for content when query or filters change
  useEffect(() => {
    if (searchQuery.trim().length === 0 && selectedTags.length === 0) {
      setSearchResults([]);
      return;
    }

    const delayDebounceFn = setTimeout(() => {
      performSearch();
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [searchQuery, filters, selectedTags]);

  const performSearch = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // In a real implementation, you would call your API with the search parameters
      // For now, we'll use our mock service
      const results = await socialVideoService.searchVideoPosts({
        query: searchQuery,
        contentType: filters.contentType !== 'all' ? filters.contentType : undefined,
        dateRange: filters.dateRange !== 'all' ? filters.dateRange : undefined,
        status: filters.status !== 'all' ? filters.status : undefined,
        tags: selectedTags.length > 0 ? selectedTags : undefined,
        userId: userId,
      });

      setSearchResults(results);
    } catch (err) {
      console.error('Error searching content:', err);
      setError('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const clearFilters = () => {
    setFilters({
      contentType: 'all',
      dateRange: 'all',
      status: 'published',
    });
    setSelectedTags([]);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  return (
    <div className="space-y-6">
      {/* Search input */}
      <div className="relative">
        <div className="flex">
          <div className="relative flex-grow">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Rechercher du contenu..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-retreat-green focus:border-retreat-green sm:text-sm"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className="ml-3 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            <svg className="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
            </svg>
            Filtres
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-3 p-4 bg-white border border-gray-200 rounded-md shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="contentType" className="block text-sm font-medium text-gray-700">
                  Type de contenu
                </label>
                <select
                  id="contentType"
                  value={filters.contentType}
                  onChange={(e) => setFilters({ ...filters, contentType: e.target.value })}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm rounded-md"
                >
                  <option value="all">Tous les types</option>
                  <option value="video">Vidéos</option>
                  <option value="image">Images</option>
                  <option value="text">Texte</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="dateRange" className="block text-sm font-medium text-gray-700">
                  Période
                </label>
                <select
                  id="dateRange"
                  value={filters.dateRange}
                  onChange={(e) => setFilters({ ...filters, dateRange: e.target.value })}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm rounded-md"
                >
                  <option value="all">Toutes les dates</option>
                  <option value="today">Aujourd'hui</option>
                  <option value="week">Cette semaine</option>
                  <option value="month">Ce mois</option>
                  <option value="year">Cette année</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                  Statut
                </label>
                <select
                  id="status"
                  value={filters.status}
                  onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm rounded-md"
                >
                  <option value="published">Publié</option>
                  <option value="archived">Archivé</option>
                  <option value="deleted">Supprimé</option>
                  <option value="all">Tous les statuts</option>
                </select>
              </div>
            </div>
            
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <div className="flex flex-wrap gap-2">
                {availableTags.map((tag) => (
                  <button
                    key={tag}
                    onClick={() => handleTagToggle(tag)}
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      selectedTags.includes(tag)
                        ? 'bg-retreat-green text-white'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="mt-4 flex justify-end">
              <button
                type="button"
                onClick={clearFilters}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
              >
                Réinitialiser les filtres
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Search results */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
          {error}
        </div>
      ) : searchResults.length > 0 ? (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {searchResults.map((post) => (
              <li key={post.id}>
                <div className="px-4 py-4 sm:px-6 hover:bg-gray-50 cursor-pointer" onClick={() => onSelectPost && onSelectPost(post)}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-16 w-24 bg-gray-100 rounded overflow-hidden">
                      {post.thumbnailUrl ? (
                        <img
                          src={post.thumbnailUrl}
                          alt={post.title}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center text-gray-400">
                          <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-retreat-green truncate">{post.title}</p>
                        <div className="ml-2 flex-shrink-0 flex">
                          <p className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            post.status === 'published' ? 'bg-green-100 text-green-800' :
                            post.status === 'archived' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {post.status === 'published' ? 'Publié' :
                             post.status === 'archived' ? 'Archivé' : 'Supprimé'}
                          </p>
                        </div>
                      </div>
                      <div className="mt-1 text-sm text-gray-500 line-clamp-2">
                        {post.description}
                      </div>
                      <div className="mt-2 flex items-center justify-between">
                        <div className="flex items-center text-xs text-gray-500">
                          <span>{formatDate(post.createdAt)}</span>
                          <span className="mx-1">•</span>
                          <span>{post.views} vues</span>
                          <span className="mx-1">•</span>
                          <span>{post.likes} j'aime</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {post.tags && post.tags.slice(0, 3).map((tag, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
                            >
                              {tag}
                            </span>
                          ))}
                          {post.tags && post.tags.length > 3 && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                              +{post.tags.length - 3}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      ) : searchQuery.trim().length > 0 || selectedTags.length > 0 ? (
        <div className="bg-gray-50 border border-gray-200 rounded-md p-12 text-center">
          <p className="text-gray-500">Aucun résultat trouvé pour votre recherche.</p>
        </div>
      ) : null}
    </div>
  );
};

export default ContentSearch;
