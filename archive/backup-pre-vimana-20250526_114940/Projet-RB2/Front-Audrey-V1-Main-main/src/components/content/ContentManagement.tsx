import React, { useState, useEffect } from 'react';
import { socialService } from '../../services/api/socialService';
import { socialVideoService } from '../../services/api/socialVideoService';
import PostList from './PostList';
import ConfirmationDialog from '../ui/ConfirmationDialog';
import EditPostDialog from './EditPostDialog';
import UploadContentDialog from './UploadContentDialog';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface Post {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl?: string;
  createdAt: string;
  userId: string;
  userName: string;
  userAvatar: string;
  likes: number;
  comments: number;
  views: number;
  status: 'published' | 'archived' | 'deleted';
  tags: string[];
  privacy: 'public' | 'friends' | 'private';
}

interface ContentManagementProps {
  activeTab: string;
  userId?: string;
}

const ContentManagement: React.FC<ContentManagementProps> = ({ activeTab, userId }) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'archive' | 'delete' | 'restore' | 'permanent-delete'>('archive');
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [currentPost, setCurrentPost] = useState<Post | null>(null);

  useEffect(() => {
    fetchPosts();
  }, [activeTab, userId]);

  const fetchPosts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Determine which API to call based on the active tab
      let fetchedPosts: Post[] = [];

      // In a real implementation, you would call your API with the appropriate filters
      // For now, we'll simulate with mock data
      if (activeTab === 'published') {
        // Fetch published posts
        const response = await socialVideoService.getVideoPosts({
          status: 'published',
          userId: userId
        });
        fetchedPosts = response;
      } else if (activeTab === 'archived') {
        // Fetch archived posts
        const response = await socialVideoService.getVideoPosts({
          status: 'archived',
          userId: userId
        });
        fetchedPosts = response;
      } else if (activeTab === 'deleted') {
        // Fetch deleted posts (recycle bin)
        const response = await socialVideoService.getVideoPosts({
          status: 'deleted',
          userId: userId
        });
        fetchedPosts = response;
      }

      setPosts(fetchedPosts);
    } catch (err) {
      console.error('Error fetching posts:', err);
      setError('Une erreur est survenue lors du chargement des publications.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectPost = (postId: string) => {
    setSelectedPosts(prev =>
      prev.includes(postId)
        ? prev.filter(id => id !== postId)
        : [...prev, postId]
    );
  };

  const handleSelectAllPosts = () => {
    if (selectedPosts.length === posts.length) {
      setSelectedPosts([]);
    } else {
      setSelectedPosts(posts.map(post => post.id));
    }
  };

  const openConfirmDialog = (action: 'archive' | 'delete' | 'restore' | 'permanent-delete') => {
    setConfirmAction(action);
    setConfirmDialogOpen(true);
  };

  const handleConfirmAction = async () => {
    setConfirmDialogOpen(false);

    try {
      const targetPosts = selectedPosts;
      if (targetPosts.length === 0) return;

      setIsLoading(true);

      // Perform the selected action
      switch (confirmAction) {
        case 'archive':
          // In a real implementation, call your API to archive posts
          await Promise.all(targetPosts.map(postId =>
            socialVideoService.updateVideoStatus(postId, 'archived')
          ));
          setSuccess(`${targetPosts.length} publication(s) archivée(s) avec succès`);
          break;

        case 'delete':
          // Move posts to recycle bin
          await Promise.all(targetPosts.map(postId =>
            socialVideoService.updateVideoStatus(postId, 'deleted')
          ));
          setSuccess(`${targetPosts.length} publication(s) déplacée(s) vers la corbeille`);
          break;

        case 'restore':
          // Restore posts from archive or recycle bin
          await Promise.all(targetPosts.map(postId =>
            socialVideoService.updateVideoStatus(postId, 'published')
          ));
          setSuccess(`${targetPosts.length} publication(s) restaurée(s) avec succès`);
          break;

        case 'permanent-delete':
          // Permanently delete posts
          await Promise.all(targetPosts.map(postId =>
            socialVideoService.deleteVideo(postId)
          ));
          setSuccess(`${targetPosts.length} publication(s) supprimée(s) définitivement`);
          break;
      }

      // Refresh the posts list
      fetchPosts();
      setSelectedPosts([]);
    } catch (err) {
      console.error('Error performing action:', err);
      setError('Une erreur est survenue. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditPost = (post: Post) => {
    setCurrentPost(post);
    setEditDialogOpen(true);
  };

  const handleSaveEdit = async (updatedPost: Post) => {
    try {
      setIsLoading(true);

      // In a real implementation, call your API to update the post
      await socialVideoService.updateVideo(updatedPost.id, {
        title: updatedPost.title,
        description: updatedPost.description,
        tags: updatedPost.tags,
        privacy: updatedPost.privacy
      });

      setSuccess('Publication mise à jour avec succès');
      setEditDialogOpen(false);
      fetchPosts();
    } catch (err) {
      console.error('Error updating post:', err);
      setError('Une erreur est survenue lors de la mise à jour de la publication.');
    } finally {
      setIsLoading(false);
    }
  };

  const getActionButtons = () => {
    if (selectedPosts.length === 0) {
      // Show upload button when no posts are selected and we're on the published tab
      if (activeTab === 'published') {
        return (
          <button
            onClick={() => setUploadDialogOpen(true)}
            className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Ajouter du contenu
          </button>
        );
      }
      return null;
    }

    if (activeTab === 'published') {
      return (
        <div className="flex space-x-4">
          <button
            onClick={() => openConfirmDialog('archive')}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
            </svg>
            Archiver
          </button>
          <button
            onClick={() => openConfirmDialog('delete')}
            className="px-4 py-2 bg-red-100 text-red-800 rounded-md hover:bg-red-200 flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Supprimer
          </button>
        </div>
      );
    } else if (activeTab === 'archived') {
      return (
        <div className="flex space-x-4">
          <button
            onClick={() => openConfirmDialog('restore')}
            className="px-4 py-2 bg-green-100 text-green-800 rounded-md hover:bg-green-200 flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Restaurer
          </button>
          <button
            onClick={() => openConfirmDialog('delete')}
            className="px-4 py-2 bg-red-100 text-red-800 rounded-md hover:bg-red-200 flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Supprimer
          </button>
        </div>
      );
    } else if (activeTab === 'deleted') {
      return (
        <div className="flex space-x-4">
          <button
            onClick={() => openConfirmDialog('restore')}
            className="px-4 py-2 bg-green-100 text-green-800 rounded-md hover:bg-green-200 flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Restaurer
          </button>
          <button
            onClick={() => openConfirmDialog('permanent-delete')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Supprimer définitivement
          </button>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="space-y-6">
      {/* Header with action buttons */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">
          {selectedPosts.length > 0
            ? `${selectedPosts.length} élément(s) sélectionné(s)`
            : activeTab === 'published'
              ? 'Publications'
              : activeTab === 'archived'
                ? 'Archives'
                : 'Corbeille'}
        </h2>
        {getActionButtons()}
      </div>

      {/* Content */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
          {error}
        </div>
      ) : posts.length === 0 ? (
        <div className="bg-gray-50 border border-gray-200 rounded-md p-12 text-center">
          <p className="text-gray-500">
            {activeTab === 'published'
              ? 'Aucune publication disponible.'
              : activeTab === 'archived'
                ? 'Aucune publication archivée.'
                : 'La corbeille est vide.'}
          </p>
        </div>
      ) : (
        <PostList
          posts={posts}
          selectedPosts={selectedPosts}
          onSelectPost={handleSelectPost}
          onSelectAll={handleSelectAllPosts}
          onEditPost={handleEditPost}
        />
      )}

      {/* Success message */}
      {success && (
        <div className="fixed bottom-4 right-4 bg-green-100 border border-green-400 text-green-800 rounded-md p-4 shadow-lg">
          {success}
          <button
            className="ml-4 text-green-600 hover:text-green-800"
            onClick={() => setSuccess(null)}
          >
            Fermer
          </button>
        </div>
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        title={
          confirmAction === 'archive' ? 'Archiver les publications' :
          confirmAction === 'delete' ? 'Supprimer les publications' :
          confirmAction === 'restore' ? 'Restaurer les publications' :
          'Supprimer définitivement les publications'
        }
        message={
          confirmAction === 'archive' ? 'Êtes-vous sûr de vouloir archiver les publications sélectionnées ? Elles seront déplacées vers les archives.' :
          confirmAction === 'delete' ? 'Êtes-vous sûr de vouloir supprimer les publications sélectionnées ? Elles seront déplacées vers la corbeille.' :
          confirmAction === 'restore' ? 'Êtes-vous sûr de vouloir restaurer les publications sélectionnées ? Elles seront à nouveau publiées.' :
          'Êtes-vous sûr de vouloir supprimer définitivement les publications sélectionnées ? Cette action est irréversible.'
        }
        confirmLabel={
          confirmAction === 'archive' ? 'Archiver' :
          confirmAction === 'delete' ? 'Supprimer' :
          confirmAction === 'restore' ? 'Restaurer' :
          'Supprimer définitivement'
        }
        confirmVariant={
          confirmAction === 'archive' || confirmAction === 'restore' ? 'primary' : 'danger'
        }
        onConfirm={handleConfirmAction}
        onCancel={() => setConfirmDialogOpen(false)}
      />

      {/* Edit Post Dialog */}
      {currentPost && (
        <EditPostDialog
          isOpen={editDialogOpen}
          post={currentPost}
          onSave={handleSaveEdit}
          onCancel={() => setEditDialogOpen(false)}
        />
      )}

      {/* Upload Content Dialog */}
      <UploadContentDialog
        isOpen={uploadDialogOpen}
        onClose={() => setUploadDialogOpen(false)}
        onSuccess={() => {
          setSuccess('Contenu téléchargé avec succès');
          fetchPosts();
        }}
      />
    </div>
  );
};

export default ContentManagement;
