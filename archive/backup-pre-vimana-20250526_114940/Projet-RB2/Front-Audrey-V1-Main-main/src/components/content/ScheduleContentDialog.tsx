import React, { useState, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { socialVideoService } from '../../services/api/socialVideoService';

interface Post {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl?: string;
  createdAt: string;
  userId: string;
  userName: string;
  userAvatar: string;
  likes: number;
  comments: number;
  views: number;
  status: 'published' | 'archived' | 'deleted';
  tags: string[];
  privacy: 'public' | 'friends' | 'private';
  scheduledDate?: string;
}

interface ScheduleContentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  posts: Post[];
  selectedPosts: string[];
}

const ScheduleContentDialog: React.FC<ScheduleContentDialogProps> = ({
  isOpen,
  onClose,
  onSuccess,
  posts,
  selectedPosts
}) => {
  const [scheduledDate, setScheduledDate] = useState<Date | null>(null);
  const [scheduledTime, setScheduledTime] = useState<Date | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedPostsData, setSelectedPostsData] = useState<Post[]>([]);

  // Set minimum date to today
  const minDate = new Date();
  
  // Set minimum time to current time if date is today
  const minTime = new Date();
  minTime.setSeconds(0);
  minTime.setMilliseconds(0);

  // Set default time to next hour
  const defaultTime = new Date();
  defaultTime.setHours(defaultTime.getHours() + 1);
  defaultTime.setMinutes(0);
  defaultTime.setSeconds(0);
  defaultTime.setMilliseconds(0);

  useEffect(() => {
    if (isOpen) {
      // Reset form when dialog opens
      setScheduledDate(null);
      setScheduledTime(defaultTime);
      setError(null);
      setSuccess(null);
      
      // Get selected posts data
      const postsData = posts.filter(post => selectedPosts.includes(post.id));
      setSelectedPostsData(postsData);
    }
  }, [isOpen, posts, selectedPosts, defaultTime]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!scheduledDate) {
      setError('Veuillez sélectionner une date de publication');
      return;
    }

    if (!scheduledTime) {
      setError('Veuillez sélectionner une heure de publication');
      return;
    }

    // Combine date and time
    const combinedDateTime = new Date(scheduledDate);
    combinedDateTime.setHours(
      scheduledTime.getHours(),
      scheduledTime.getMinutes(),
      0,
      0
    );

    // Check if the combined date/time is in the past
    if (combinedDateTime <= new Date()) {
      setError('La date et l\'heure de publication doivent être dans le futur');
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      // Schedule each selected post
      const promises = selectedPosts.map(postId => 
        socialVideoService.scheduleVideo(postId, combinedDateTime.toISOString())
      );
      
      await Promise.all(promises);
      
      setSuccess(`${selectedPosts.length} contenu(s) programmé(s) pour publication le ${combinedDateTime.toLocaleDateString()} à ${combinedDateTime.toLocaleTimeString()}`);
      
      // Reset form after success
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 2000);
    } catch (err) {
      console.error('Error scheduling content:', err);
      setError('Une erreur est survenue lors de la programmation. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Programmer la publication
                  </h3>
                  
                  {error && (
                    <div className="mb-4 bg-red-50 border border-red-200 text-red-800 rounded-md p-3">
                      {error}
                    </div>
                  )}
                  
                  {success && (
                    <div className="mb-4 bg-green-50 border border-green-200 text-green-800 rounded-md p-3">
                      {success}
                    </div>
                  )}
                  
                  <div className="mt-2 space-y-4">
                    <div>
                      <p className="text-sm text-gray-500 mb-2">
                        Vous avez sélectionné {selectedPosts.length} contenu(s) à programmer :
                      </p>
                      <ul className="text-sm text-gray-700 mb-4 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2">
                        {selectedPostsData.map(post => (
                          <li key={post.id} className="py-1 border-b border-gray-100 last:border-b-0 truncate">
                            {post.title}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-1">
                          Date de publication
                        </label>
                        <DatePicker
                          id="scheduledDate"
                          selected={scheduledDate}
                          onChange={(date) => setScheduledDate(date)}
                          minDate={minDate}
                          dateFormat="dd/MM/yyyy"
                          className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm"
                          placeholderText="Sélectionner une date"
                          required
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="scheduledTime" className="block text-sm font-medium text-gray-700 mb-1">
                          Heure de publication
                        </label>
                        <DatePicker
                          id="scheduledTime"
                          selected={scheduledTime}
                          onChange={(time) => setScheduledTime(time)}
                          showTimeSelect
                          showTimeSelectOnly
                          timeIntervals={15}
                          timeCaption="Heure"
                          dateFormat="HH:mm"
                          minTime={scheduledDate && scheduledDate.getDate() === minDate.getDate() ? minTime : undefined}
                          className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-retreat-green focus:border-retreat-green sm:text-sm"
                          placeholderText="Sélectionner une heure"
                          required
                        />
                      </div>
                    </div>
                    
                    <div className="mt-2">
                      <p className="text-xs text-gray-500">
                        Note : Le contenu sera automatiquement publié à la date et l'heure spécifiées.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={isLoading || !!success}
                className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-retreat-green text-base font-medium text-white hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green sm:ml-3 sm:w-auto sm:text-sm ${
                  (isLoading || !!success) ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isLoading ? 'Programmation...' : 'Programmer'}
              </button>
              <button
                type="button"
                disabled={isLoading || !!success}
                onClick={onClose}
                className={`mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm ${
                  (isLoading || !!success) ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Annuler
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ScheduleContentDialog;
