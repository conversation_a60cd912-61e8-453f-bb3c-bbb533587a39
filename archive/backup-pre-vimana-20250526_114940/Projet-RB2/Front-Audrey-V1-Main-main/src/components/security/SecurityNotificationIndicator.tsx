import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useSecurity } from '../../contexts/SecurityContext';

interface SecurityNotificationIndicatorProps {
  className?: string;
}

interface NotificationType {
  id: string;
  title: string;
  message: string;
  createdAt: string; // Assuming string, adjust if it's a Date object
  read: boolean;
  actionUrl?: string;
  // Add other fields as necessary based on your actual notification structure
  [key: string]: any; // Allow other properties if the structure is very dynamic
}

const SecurityNotificationIndicator: React.FC<SecurityNotificationIndicatorProps> = ({
  className = '',
}) => {
  const { unreadNotificationsCount, getSecurityNotifications, markNotificationAsRead } =
    useSecurity();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [recentNotifications, setRecentNotifications] = useState<NotificationType[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Fetch recent notifications
  useEffect(() => {
    const fetchRecentNotifications = async () => {
      if (isDropdownOpen) {
        const response = await getSecurityNotifications({
          limit: 5,
          page: 1,
        });
        setRecentNotifications(response.notifications);
      }
    };

    fetchRecentNotifications();
  }, [isDropdownOpen, getSecurityNotifications]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleNotificationClick = async (notificationId: string) => {
    await markNotificationAsRead(notificationId);
    setIsDropdownOpen(false);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  if (unreadNotificationsCount === 0) {
    return (
      <Link
        to='/security/notifications'
        className={`relative p-2 text-gray-600 hover:text-gray-900 ${className}`}
        title='Notifications de sécurité'
      >
        <svg
          className='w-6 h-6'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
          />
        </svg>
      </Link>
    );
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        className='relative p-2 text-gray-600 hover:text-gray-900'
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        title='Notifications de sécurité'
      >
        <svg
          className='w-6 h-6'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
          />
        </svg>
        {unreadNotificationsCount > 0 && (
          <span className='absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full'>
            {unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount}
          </span>
        )}
      </button>

      {isDropdownOpen && (
        <div className='absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-50 overflow-hidden'>
          <div className='px-4 py-2 bg-gray-100 border-b border-gray-200'>
            <div className='flex justify-between items-center'>
              <h3 className='text-sm font-medium text-gray-700'>Notifications de sécurité</h3>
              <Link
                to='/security/notifications'
                className='text-xs text-blue-600 hover:text-blue-800'
                onClick={() => setIsDropdownOpen(false)}
              >
                Voir tout
              </Link>
            </div>
          </div>

          <div className='max-h-96 overflow-y-auto'>
            {recentNotifications.length === 0 ? (
              <div className='px-4 py-6 text-center text-gray-500'>
                <p>Aucune notification récente</p>
              </div>
            ) : (
              <ul className='divide-y divide-gray-200'>
                {recentNotifications.map((notification) => (
                  <li key={notification.id} className='px-4 py-3 hover:bg-gray-50'>
                    <Link
                      to={notification.actionUrl || '/security/notifications'}
                      className='block'
                      onClick={() => handleNotificationClick(notification.id)}
                    >
                      <div className='flex justify-between'>
                        <p
                          className={`text-sm font-medium ${
                            notification.read ? 'text-gray-600' : 'text-gray-900'
                          }`}
                        >
                          {notification.title}
                        </p>
                        <span className='text-xs text-gray-500'>
                          {formatDate(notification.createdAt)}
                        </span>
                      </div>
                      <p
                        className={`text-xs mt-1 ${
                          notification.read ? 'text-gray-500' : 'text-gray-700'
                        }`}
                      >
                        {notification.message.length > 100
                          ? `${notification.message.substring(0, 100)}...`
                          : notification.message}
                      </p>
                      {!notification.read && (
                        <span className='inline-block w-2 h-2 bg-blue-600 rounded-full mt-1'></span>
                      )}
                    </Link>
                  </li>
                ))}
              </ul>
            )}
          </div>

          <div className='px-4 py-2 bg-gray-100 border-t border-gray-200'>
            <Link
              to='/security/training'
              className='block text-xs text-center text-blue-600 hover:text-blue-800'
              onClick={() => setIsDropdownOpen(false)}
            >
              Formation à la sécurité
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityNotificationIndicator;
