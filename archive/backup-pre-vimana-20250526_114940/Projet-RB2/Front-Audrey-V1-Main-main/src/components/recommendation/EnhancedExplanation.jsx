import React, { useState, useEffect } from 'react';
import { Card, Typography, Divider, Too<PERSON>ip, Button, Skeleton, Alert, Space, Tag, Progress } from 'antd';
import { InfoCircleOutlined, BarChartOutlined, Radar<PERSON>hartOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { getEnhancedExplanation } from '../../services/api/recommendationService';
import { Bar, Radar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip as ChartTooltip, Legend, RadialLinearScale, PointElement, LineElement, Filler } from 'chart.js';

// Enregistrer les composants nécessaires pour Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Title,
  ChartTooltip,
  Legend
);

const { Title: AntTitle, Text, Paragraph } = Typography;

/**
 * Composant pour afficher une explication améliorée d'une recommandation
 */
const EnhancedExplanation = ({ recommendationId, recommendationType, language = 'fr', detailLevel = 'STANDARD' }) => {
  const [explanation, setExplanation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const fetchExplanation = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await getEnhancedExplanation(recommendationId, recommendationType, {
          includeVisualizations: true,
          detailLevel,
          language,
        });
        
        if (response && response.success) {
          setExplanation(response.data);
        } else {
          setError('Impossible de récupérer l\'explication de la recommandation');
        }
      } catch (err) {
        console.error('Erreur lors de la récupération de l\'explication:', err);
        setError('Une erreur est survenue lors de la récupération de l\'explication');
      } finally {
        setLoading(false);
      }
    };

    if (recommendationId && recommendationType) {
      fetchExplanation();
    }
  }, [recommendationId, recommendationType, detailLevel, language]);

  /**
   * Rendu des facteurs d'explication
   */
  const renderFactors = () => {
    if (!explanation || !explanation.factors || explanation.factors.length === 0) {
      return <Text>Aucun facteur d'explication disponible</Text>;
    }

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {explanation.factors.map((factor, index) => (
          <div key={index}>
            <Space align="center" style={{ marginBottom: 8 }}>
              <Text strong>{factor.name}</Text>
              <Tooltip title={factor.description}>
                <InfoCircleOutlined />
              </Tooltip>
            </Space>
            <Progress 
              percent={Math.round(factor.weight * 100)} 
              status="active" 
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          </div>
        ))}
      </Space>
    );
  };

  /**
   * Rendu des visualisations
   */
  const renderVisualizations = () => {
    if (!explanation || !explanation.visualizations || explanation.visualizations.length === 0) {
      return null;
    }

    return (
      <>
        <Divider />
        <AntTitle level={5}>Visualisations</AntTitle>
        <Space direction="vertical" style={{ width: '100%' }}>
          {explanation.visualizations.map((viz, index) => (
            <Card 
              key={index} 
              size="small" 
              title={
                <Space>
                  {viz.type === 'BAR_CHART' ? <BarChartOutlined /> : <RadarChartOutlined />}
                  <Text>{viz.title}</Text>
                </Space>
              }
              style={{ marginBottom: 16 }}
            >
              {viz.description && <Paragraph type="secondary">{viz.description}</Paragraph>}
              <div style={{ height: 200 }}>
                {viz.type === 'BAR_CHART' && (
                  <Bar 
                    data={{
                      labels: viz.data.labels,
                      datasets: [{
                        label: 'Importance',
                        data: viz.data.values,
                        backgroundColor: viz.data.colors || ['#4285F4', '#34A853', '#FBBC05', '#EA4335'],
                      }]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true,
                          max: 1,
                        }
                      }
                    }}
                  />
                )}
                {viz.type === 'RADAR_CHART' && (
                  <Radar 
                    data={{
                      labels: viz.data.categories,
                      datasets: [{
                        label: 'Correspondance',
                        data: viz.data.values,
                        backgroundColor: 'rgba(66, 133, 244, 0.2)',
                        borderColor: '#4285F4',
                        pointBackgroundColor: '#4285F4',
                      }]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        r: {
                          beginAtZero: true,
                          max: 1,
                        }
                      }
                    }}
                  />
                )}
              </div>
            </Card>
          ))}
        </Space>
      </>
    );
  };

  if (loading) {
    return (
      <Card>
        <Skeleton active paragraph={{ rows: 4 }} />
      </Card>
    );
  }

  if (error) {
    return (
      <Alert
        message="Erreur"
        description={error}
        type="error"
        showIcon
      />
    );
  }

  if (!explanation) {
    return (
      <Alert
        message="Information"
        description="Aucune explication disponible pour cette recommandation"
        type="info"
        showIcon
      />
    );
  }

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }}>
        <AntTitle level={4}>
          Pourquoi cette recommandation ?
          <Tooltip title="Les explications vous aident à comprendre pourquoi cette recommandation vous est proposée">
            <QuestionCircleOutlined style={{ marginLeft: 8, fontSize: 16 }} />
          </Tooltip>
        </AntTitle>
        
        <Paragraph>{explanation.generalExplanation}</Paragraph>
        
        {explanation.personalizedExplanation && (
          <Paragraph strong>{explanation.personalizedExplanation}</Paragraph>
        )}
        
        <Divider />
        
        <AntTitle level={5}>Facteurs principaux</AntTitle>
        {renderFactors()}
        
        {explanation.visualizations && explanation.visualizations.length > 0 && (
          <>
            {showDetails ? (
              <>
                {renderVisualizations()}
                <Button type="link" onClick={() => setShowDetails(false)}>Masquer les détails</Button>
              </>
            ) : (
              <Button type="link" onClick={() => setShowDetails(true)}>Afficher plus de détails</Button>
            )}
          </>
        )}
        
        <Divider />
        
        <Space>
          <Tag color="blue">Niveau de détail: {detailLevel}</Tag>
          {explanation.isPersonalized && <Tag color="green">Personnalisé</Tag>}
          <Tag color="purple">Langue: {language}</Tag>
        </Space>
      </Space>
    </Card>
  );
};

export default EnhancedExplanation;
