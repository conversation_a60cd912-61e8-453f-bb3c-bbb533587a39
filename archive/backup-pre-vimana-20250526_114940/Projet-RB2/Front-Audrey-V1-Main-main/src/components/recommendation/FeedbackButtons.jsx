import React, { useState } from 'react';
import { Button, Space, Tooltip, Modal, Form, Input, Rate, message, Typography } from 'antd';
import { 
  LikeOutlined, 
  DislikeOutlined, 
  LikeFilled, 
  DislikeFilled, 
  BookOutlined, 
  BookFilled, 
  EyeInvisibleOutlined,
  FlagOutlined
} from '@ant-design/icons';
import { recordFeedback, reportRecommendation } from '../../services/api/recommendationService';

const { TextArea } = Input;
const { Text } = Typography;

/**
 * Types de feedback
 */
const FeedbackType = {
  LIKE: 'LIKE',
  DISLIKE: 'DISLIKE',
  SAVE: 'SAVE',
  HIDE: 'HIDE',
  REPORT: 'REPORT',
};

/**
 * Composant pour les boutons de feedback sur une recommandation
 */
const FeedbackButtons = ({ recommendationId, recommendationType, onFeedbackSubmitted }) => {
  const [liked, setLiked] = useState(false);
  const [disliked, setDisliked] = useState(false);
  const [saved, setSaved] = useState(false);
  const [hidden, setHidden] = useState(false);
  const [loading, setLoading] = useState(false);
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);
  const [feedbackType, setFeedbackType] = useState(null);
  const [form] = Form.useForm();

  /**
   * Gère le clic sur un bouton de feedback simple
   */
  const handleFeedbackClick = async (type) => {
    try {
      setLoading(true);
      
      // Mettre à jour l'état local immédiatement pour une meilleure UX
      switch (type) {
        case FeedbackType.LIKE:
          setLiked(!liked);
          if (!liked) setDisliked(false); // Désactiver dislike si on like
          break;
        case FeedbackType.DISLIKE:
          setDisliked(!disliked);
          if (!disliked) setLiked(false); // Désactiver like si on dislike
          break;
        case FeedbackType.SAVE:
          setSaved(!saved);
          break;
        case FeedbackType.HIDE:
          setHidden(!hidden);
          break;
        default:
          break;
      }
      
      // Envoyer le feedback au serveur
      await recordFeedback(recommendationId, recommendationType, type);
      
      // Notifier le composant parent
      if (onFeedbackSubmitted) {
        onFeedbackSubmitted(type);
      }
      
      message.success('Feedback enregistré avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du feedback:', error);
      message.error('Erreur lors de l\'enregistrement du feedback');
      
      // Restaurer l'état précédent en cas d'erreur
      switch (type) {
        case FeedbackType.LIKE:
          setLiked(!liked);
          break;
        case FeedbackType.DISLIKE:
          setDisliked(!disliked);
          break;
        case FeedbackType.SAVE:
          setSaved(!saved);
          break;
        case FeedbackType.HIDE:
          setHidden(!hidden);
          break;
        default:
          break;
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * Ouvre le modal de feedback détaillé
   */
  const openFeedbackModal = (type) => {
    setFeedbackType(type);
    setFeedbackModalVisible(true);
    form.resetFields();
  };

  /**
   * Gère la soumission du feedback détaillé
   */
  const handleFeedbackSubmit = async (values) => {
    try {
      setLoading(true);
      
      // Envoyer le feedback au serveur
      await recordFeedback(recommendationId, recommendationType, feedbackType, {
        comment: values.comment,
        rating: values.rating,
      });
      
      // Mettre à jour l'état local
      switch (feedbackType) {
        case FeedbackType.LIKE:
          setLiked(true);
          setDisliked(false);
          break;
        case FeedbackType.DISLIKE:
          setDisliked(true);
          setLiked(false);
          break;
        default:
          break;
      }
      
      // Notifier le composant parent
      if (onFeedbackSubmitted) {
        onFeedbackSubmitted(feedbackType, values);
      }
      
      message.success('Feedback détaillé enregistré avec succès');
      setFeedbackModalVisible(false);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du feedback détaillé:', error);
      message.error('Erreur lors de l\'enregistrement du feedback détaillé');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Gère la soumission d'un signalement
   */
  const handleReportSubmit = async (values) => {
    try {
      setLoading(true);
      
      // Envoyer le signalement au serveur
      await reportRecommendation(recommendationId, recommendationType, values.reason);
      
      message.success('Signalement enregistré avec succès');
      setReportModalVisible(false);
      
      // Notifier le composant parent
      if (onFeedbackSubmitted) {
        onFeedbackSubmitted(FeedbackType.REPORT, values);
      }
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du signalement:', error);
      message.error('Erreur lors de l\'enregistrement du signalement');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Space>
        <Tooltip title={liked ? "Ne plus aimer" : "J'aime"}>
          <Button
            type={liked ? "primary" : "default"}
            icon={liked ? <LikeFilled /> : <LikeOutlined />}
            onClick={() => handleFeedbackClick(FeedbackType.LIKE)}
            loading={loading}
          />
        </Tooltip>
        
        <Tooltip title="Donner un avis détaillé positif">
          <Button
            type="text"
            onClick={() => openFeedbackModal(FeedbackType.LIKE)}
          >
            <Text type="secondary">Avis détaillé</Text>
          </Button>
        </Tooltip>
        
        <Tooltip title={disliked ? "Ne plus ne pas aimer" : "Je n'aime pas"}>
          <Button
            type={disliked ? "primary" : "default"}
            icon={disliked ? <DislikeFilled /> : <DislikeOutlined />}
            onClick={() => handleFeedbackClick(FeedbackType.DISLIKE)}
            loading={loading}
            danger={disliked}
          />
        </Tooltip>
        
        <Tooltip title="Donner un avis détaillé négatif">
          <Button
            type="text"
            onClick={() => openFeedbackModal(FeedbackType.DISLIKE)}
          >
            <Text type="secondary">Avis détaillé</Text>
          </Button>
        </Tooltip>
        
        <Tooltip title={saved ? "Retirer des favoris" : "Sauvegarder"}>
          <Button
            type={saved ? "primary" : "default"}
            icon={saved ? <BookFilled /> : <BookOutlined />}
            onClick={() => handleFeedbackClick(FeedbackType.SAVE)}
            loading={loading}
          />
        </Tooltip>
        
        <Tooltip title={hidden ? "Afficher à nouveau" : "Masquer"}>
          <Button
            type={hidden ? "primary" : "default"}
            icon={<EyeInvisibleOutlined />}
            onClick={() => handleFeedbackClick(FeedbackType.HIDE)}
            loading={loading}
          />
        </Tooltip>
        
        <Tooltip title="Signaler un problème">
          <Button
            type="default"
            icon={<FlagOutlined />}
            onClick={() => setReportModalVisible(true)}
            loading={loading}
          />
        </Tooltip>
      </Space>
      
      {/* Modal pour le feedback détaillé */}
      <Modal
        title={feedbackType === FeedbackType.LIKE ? "Votre avis positif" : "Votre avis négatif"}
        open={feedbackModalVisible}
        onCancel={() => setFeedbackModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFeedbackSubmit}
        >
          <Form.Item
            name="rating"
            label="Note"
            rules={[{ required: true, message: 'Veuillez donner une note' }]}
          >
            <Rate allowHalf />
          </Form.Item>
          
          <Form.Item
            name="comment"
            label="Commentaire"
            rules={[{ required: true, message: 'Veuillez saisir un commentaire' }]}
          >
            <TextArea rows={4} placeholder="Partagez votre avis sur cette recommandation..." />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Envoyer
              </Button>
              <Button onClick={() => setFeedbackModalVisible(false)}>
                Annuler
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* Modal pour le signalement */}
      <Modal
        title="Signaler un problème"
        open={reportModalVisible}
        onCancel={() => setReportModalVisible(false)}
        footer={null}
      >
        <Form
          layout="vertical"
          onFinish={handleReportSubmit}
        >
          <Form.Item
            name="reason"
            label="Raison du signalement"
            rules={[{ required: true, message: 'Veuillez indiquer la raison du signalement' }]}
          >
            <TextArea rows={4} placeholder="Décrivez le problème avec cette recommandation..." />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" danger htmlType="submit" loading={loading}>
                Signaler
              </Button>
              <Button onClick={() => setReportModalVisible(false)}>
                Annuler
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default FeedbackButtons;
