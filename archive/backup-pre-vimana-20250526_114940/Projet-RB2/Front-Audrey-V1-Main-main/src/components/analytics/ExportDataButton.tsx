import React, { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { exportToCSV, exportToJSON, exportToXML, exportToExcel, exportToPDF } from '../../utils/exportData';

export interface ExportDataButtonProps {
  data: any[];
  filename: string;
  headers?: { [key: string]: string };
  title?: string;
  className?: string;
  buttonText?: string;
}

export const ExportDataButton: React.FC<ExportDataButtonProps> = ({
  data,
  filename,
  headers,
  title,
  className = '',
  buttonText,
}) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  
  const handleExport = async (format: 'csv' | 'json' | 'xml' | 'excel' | 'pdf') => {
    if (!data || !data.length) {
      console.warn('No data to export');
      return;
    }
    
    setIsExporting(true);
    
    try {
      switch (format) {
        case 'csv':
          exportToCSV(data, filename, headers);
          break;
        case 'json':
          exportToJSON(data, filename);
          break;
        case 'xml':
          exportToXML(data, filename, 'data', 'item');
          break;
        case 'excel':
          await exportToExcel(data, filename);
          break;
        case 'pdf':
          await exportToPDF(data, filename, title, headers);
          break;
      }
    } catch (error) {
      console.error(`Error exporting to ${format}:`, error);
    } finally {
      setIsExporting(false);
      setIsOpen(false);
    }
  };
  
  return (
    <div className="relative">
      <button
        type="button"
        className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${className}`}
        onClick={() => setIsOpen(!isOpen)}
        disabled={isExporting}
      >
        {isExporting ? (
          <>
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {t('common.exporting')}
          </>
        ) : (
          <>
            <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            {buttonText || t('common.export')}
          </>
        )}
      </button>
      
      {isOpen && (
        <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
          <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
            <button
              className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              role="menuitem"
              onClick={() => handleExport('csv')}
            >
              {t('export.csv')}
            </button>
            <button
              className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              role="menuitem"
              onClick={() => handleExport('json')}
            >
              {t('export.json')}
            </button>
            <button
              className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              role="menuitem"
              onClick={() => handleExport('xml')}
            >
              {t('export.xml')}
            </button>
            <button
              className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              role="menuitem"
              onClick={() => handleExport('excel')}
            >
              {t('export.excel')}
            </button>
            <button
              className="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              role="menuitem"
              onClick={() => handleExport('pdf')}
            >
              {t('export.pdf')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExportDataButton;
