import React from 'react';
import {
  Respons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Bar<PERSON>hart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

export interface MetricsChartProps {
  data: any[];
  dataKeys: string[];
  xAxisDataKey: string;
  height?: number;
  type?: 'line' | 'bar' | 'pie';
  colors?: string[];
  stacked?: boolean;
}

export const MetricsChart: React.FC<MetricsChartProps> = ({
  data,
  dataKeys,
  xAxisDataKey,
  height = 300,
  type = 'line',
  colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899'],
  stacked = false,
}) => {
  // Format date strings for display
  const formattedData = data.map(item => {
    const formattedItem = { ...item };
    
    if (xAxisDataKey === 'date' && typeof item.date === 'string') {
      // Convert ISO date to more readable format
      const date = new Date(item.date);
      formattedItem.date = date.toLocaleDateString();
    }
    
    return formattedItem;
  });

  const renderChart = () => {
    switch (type) {
      case 'line':
        return (
          <LineChart
            data={formattedData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xAxisDataKey} />
            <YAxis />
            <Tooltip />
            <Legend />
            {dataKeys.map((key, index) => (
              <Line
                key={key}
                type="monotone"
                dataKey={key}
                stroke={colors[index % colors.length]}
                activeDot={{ r: 8 }}
              />
            ))}
          </LineChart>
        );
      
      case 'bar':
        return (
          <BarChart
            data={formattedData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xAxisDataKey} />
            <YAxis />
            <Tooltip />
            <Legend />
            {dataKeys.map((key, index) => (
              <Bar
                key={key}
                dataKey={key}
                fill={colors[index % colors.length]}
                stackId={stacked ? 'stack' : undefined}
              />
            ))}
          </BarChart>
        );
      
      case 'pie':
        // For pie charts, we need to transform the data
        const pieData = dataKeys.map((key, index) => {
          const total = formattedData.reduce((sum, item) => sum + (item[key] || 0), 0);
          return {
            name: key,
            value: total,
          };
        });
        
        return (
          <PieChart>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        );
      
      default:
        return null;
    }
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      {renderChart()}
    </ResponsiveContainer>
  );
};

export default MetricsChart;
