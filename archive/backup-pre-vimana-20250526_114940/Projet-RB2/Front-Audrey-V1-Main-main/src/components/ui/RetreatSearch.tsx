import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { motion, useScroll, useTransform } from 'framer-motion';

const RetreatSearch: React.FC = () => {
  const navigate = useNavigate();
  const [elementTop, setElementTop] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  const { scrollY } = useScroll();
  const y = useTransform(scrollY, [elementTop - 500, elementTop + 500], [-100, 100]);

  const handleNavigate = () => {
    navigate('/find-ideal-retreat');
  };

  useEffect(() => {
    if (ref.current) {
      setElementTop(ref.current.offsetTop);
    }
  }, []);

  return (
    <section className='w-full bg-gray-200' ref={ref}>
      <div className='max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 px-4 sm:px-6 lg:px-8 py-16 sm:py-20'>
        {/* Colonne de gauche : Texte et CTA */}
        <div className='flex flex-col justify-center space-y-6 z-10'>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h2 className='text-3xl sm:text-4xl font-bold text-gray-900 leading-tight'>
              Trouvez la retraite qui vous correspond
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <p className='text-lg sm:text-xl text-gray-600 max-w-xl'>
              Que vous soyez débutant ou pratiquant confirmé, notre moteur de recherche intelligent
              vous guide vers la retraite idéale. Filtrez par thème, niveau d&apos;expérience,
              régime alimentaire, pays, régions, ou préférences bien-être. Des séjours courts aux
              immersions longue durée. Trouvez l&apos;expérience qui correspond à votre budget et
              vos disponibilités.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className='pt-4'
          >
            <button
              onClick={handleNavigate}
              className='inline-flex items-center px-8 py-4 bg-retreat-green text-white rounded-full text-lg font-semibold hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-xl group'
            >
              Commencer ma recherche
              <ArrowRightIcon className='ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300' />
            </button>
          </motion.div>
        </div>

        {/* Colonne de droite : Image cliquable avec parallax et filtre vert */}
        <div
          role='button'
          tabIndex={0}
          onClick={handleNavigate}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleNavigate();
            }
          }}
          className='relative h-[400px] lg:h-[500px] rounded-2xl overflow-hidden group cursor-pointer'
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <motion.div className='absolute inset-0' style={{ y }}>
            <img
              src='/images/mediationscienceImg.jpg'
              alt='Retraite bien-être dans un cadre paisible'
              title='Retraite bien-être dans un cadre paisible'
              className='w-full h-full object-cover'
              aria-label='Retraite bien-être dans un cadre paisible'
            />
            {/* Overlay vert horizontal avec transition douce */}
            <div
              className={`
                absolute inset-0 
                bg-gradient-to-r from-retreat-green to-transparent
                transition-opacity duration-500 ease-in-out
                ${isHovered ? 'opacity-40' : 'opacity-0'}
              `}
            />
            {/* Overlay vert vertical avec transition douce */}
            <div
              className={`
                absolute inset-0 
                bg-gradient-to-t from-retreat-green via-transparent to-transparent
                transition-opacity duration-500 ease-in-out delay-100
                ${isHovered ? 'opacity-40' : 'opacity-0'}
              `}
            />
          </motion.div>

          {/* Effet de profondeur avec transition */}
          <div
            className={`
              absolute inset-0 rounded-2xl shadow-inner
              transition-opacity duration-500 ease-in-out
              ${isHovered ? 'opacity-100' : 'opacity-0'}
            `}
          />
        </div>
      </div>
    </section>
  );
};

export default RetreatSearch;
