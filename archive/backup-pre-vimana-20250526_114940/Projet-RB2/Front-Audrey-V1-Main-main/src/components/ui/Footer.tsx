import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaFacebookF, FaInstagram, FaXTwitter, FaLinkedinIn, FaYoutube } from 'react-icons/fa6';
import type { IconType } from 'react-icons';

const FooterWrapper = styled.footer`
  width: 100%;
`;

const SocialIcons = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  justify-content: flex-start;

  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const FooterCopyright = styled.div`
  background-color: rgba(26, 15, 61, 0.95); // Violet foncé semi-transparent
  padding: 1rem 0;
  text-align: center;
  color: #e6e0ff;
`;

const MainFooter = styled.div`
  background: linear-gradient(135deg, #553c9a 0%, rgb(44, 30, 175) 50%, #1d4ed8 100%);
  padding: 3rem 0;
`;

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  padding: 0 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    text-align: center;
  }
`;

const FooterColumn = styled.div`
  h3 {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
    color: #ffffff; // Blanc pur pour les titres
  }

  ul {
    list-style: none;
    padding: 0;
  }

  li {
    margin-bottom: 0.8rem;
  }

  a {
    color: #e6e0ff; // Violet très clair pour les liens
    text-decoration: none;
    transition: color 0.3s ease;
    &:hover {
      color: white;
      text-decoration: none;
      opacity: 0.9;
    }
  }
`;

const NewsletterForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const NewsletterDescription = styled.p`
  color: #e6e0ff;
  font-size: 0.9rem;
  margin-bottom: 1rem;
`;

const NewsletterInput = styled.input`
  padding: 0.8rem;
  border: none;
  border-radius: 4px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  color: #1a0f3d;

  &::placeholder {
    color: #666;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(230, 224, 255, 0.5);
  }
`;

const SubscribeButton = styled.button`
  padding: 0.8rem;
  background-color: #e6e0ff;
  color: #1a0f3d;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;

  &:hover {
    background-color: white;
    transform: translateY(-2px);
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;
  }
`;

const SocialIcon = styled.a`
  display: flex;
  color: #e6e0ff;
  font-size: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    color: white;
    transform: translateY(-3px);
  }
`;

interface SocialIconComponentProps {
  href: string;
  ariaLabel: string;
  Icon: IconType;
}

const SocialIconComponent: React.FC<SocialIconComponentProps> = ({ href, ariaLabel, Icon }) => {
  const IconElement = Icon as React.ElementType;
  return (
    <SocialIcon href={href} target='_blank' rel='noopener noreferrer' aria-label={ariaLabel}>
      {IconElement && <IconElement />}
    </SocialIcon>
  );
};

const Footer: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Ici, ajoutez la logique pour envoyer l'email à votre API
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulation d'appel API
      setEmail('');
      alert('Merci de votre inscription à notre newsletter !');
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_) {
      alert('Une erreur est survenue. Veuillez réessayer.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const socialLinks = [
    { href: 'https://x.com', ariaLabel: 'X (Twitter)', Icon: FaXTwitter },
    { href: 'https://instagram.com', ariaLabel: 'Instagram', Icon: FaInstagram },
    { href: 'https://youtube.com', ariaLabel: 'YouTube', Icon: FaYoutube },
    { href: 'https://linkedin.com', ariaLabel: 'LinkedIn', Icon: FaLinkedinIn },
    { href: 'https://facebook.com', ariaLabel: 'Facebook', Icon: FaFacebookF },
  ];

  return (
    <FooterWrapper>
      <MainFooter>
        <FooterContent>
          <FooterColumn>
            <h3>Newsletter</h3>
            <NewsletterDescription>
              Inscrivez-vous pour recevoir nos actualités et offres exclusives
            </NewsletterDescription>
            <NewsletterForm onSubmit={handleNewsletterSubmit}>
              <NewsletterInput
                type='email'
                placeholder='Votre adresse email'
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
              <SubscribeButton type='submit' disabled={isSubmitting}>
                {isSubmitting ? 'Inscription...' : "S'inscrire"}
              </SubscribeButton>
            </NewsletterForm>
          </FooterColumn>

          <FooterColumn>
            <h3>Menu rapide</h3>
            <ul>
              <li>
                <Link to='/'>Accueil</Link>
              </li>
              <li>
                <Link to='/retraites'>Nos retraites</Link>
              </li>
              <li>
                <Link to='/blog'>Blog</Link>
              </li>
              <li>
                <Link to='/HomePagePro'>Espace Pro</Link>
              </li>
              <li>
                <Link to='/contact'>Contact</Link>
              </li>
            </ul>
          </FooterColumn>

          <FooterColumn>
            <h3>Suivez-nous</h3>
            <SocialIcons>
              {socialLinks.map((link, index) => (
                <SocialIconComponent
                  key={index}
                  href={link.href}
                  ariaLabel={link.ariaLabel}
                  Icon={link.Icon}
                />
              ))}
            </SocialIcons>
          </FooterColumn>
        </FooterContent>
      </MainFooter>
      <FooterCopyright>
        © {new Date().getFullYear()} Retreat and Be. Tous droits réservés.
      </FooterCopyright>
    </FooterWrapper>
  );
};

export default Footer;
