import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useIsMobile from '../../hooks/useIsMobile';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  role?: string;
}

const LazyImage: React.FC<LazyImageProps> = ({ src, alt, className = '', width, height, role }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          observer.disconnect();
        }
      },
      {
        rootMargin: isMobile ? '20px' : '50px',
        threshold: isMobile ? 0.05 : 0.1,
      }
    );

    const img = new Image();
    img.src = src;
    img.onload = () => setIsLoaded(true);

    return () => observer.disconnect();
  }, [src, isMobile]);

  // Sur mobile, on charge l'image immédiatement
  if (isMobile) {
    return (
      <div className={`relative overflow-hidden ${className}`} style={{ width, height }}>
        <img src={src} alt={alt} className='w-full h-full object-cover' role={role} />
      </div>
    );
  }

  return (
    <div className={`relative overflow-hidden ${className}`} style={{ width, height }}>
      {/* Placeholder */}
      <AnimatePresence>
        {!isLoaded && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className='absolute inset-0 bg-mint-100 animate-pulse'
            transition={{ duration: 0.2 }}
          />
        )}
      </AnimatePresence>

      {/* Image */}
      <AnimatePresence>
        {isLoaded && (
          <motion.img
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            src={src}
            alt={alt}
            className='w-full h-full object-cover'
            role={role}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default LazyImage;
