/**
 * Stories Button - Retreat And Be Design System
 * Date de création: 24 mai 2025
 * 
 * Documentation interactive du composant Button
 * avec tous ses variants et états.
 */

import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { But<PERSON> } from './Button';

const meta = {
  title: 'Design System/Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
Le composant Button est un élément fondamental du design system Retreat & Be.
Il offre plusieurs variantes, tailles et états pour s'adapter à tous les besoins de l'interface.

## Caractéristiques

- **Variantes multiples** : primary, secondary, outline, ghost, link, destructive, success, warning
- **Tailles flexibles** : sm, md, lg, xl, icon
- **États interactifs** : loading, disabled, avec icônes
- **Accessibilité** : support complet du clavier et des lecteurs d'écran
- **Responsive** : adaptation automatique sur tous les écrans
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'ghost', 'link', 'destructive', 'success', 'warning'],
      description: 'Style visuel du bouton',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl', 'icon'],
      description: 'Taille du bouton',
    },
    loading: {
      control: 'boolean',
      description: 'Affiche un spinner de chargement',
    },
    disabled: {
      control: 'boolean',
      description: 'Désactive le bouton',
    },
    fullWidth: {
      control: 'boolean',
      description: 'Étend le bouton sur toute la largeur',
    },
    children: {
      control: 'text',
      description: 'Contenu du bouton',
    },
  },
  args: {
    onClick: fn(),
    children: 'Button',
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// Histoire par défaut
export const Default: Story = {
  args: {
    variant: 'primary',
    size: 'md',
    children: 'Button par défaut',
  },
};

// Toutes les variantes
export const Variants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="link">Link</Button>
      <Button variant="destructive">Destructive</Button>
      <Button variant="success">Success</Button>
      <Button variant="warning">Warning</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Toutes les variantes disponibles du composant Button.',
      },
    },
  },
};

// Toutes les tailles
export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="sm">Small</Button>
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
      <Button size="xl">Extra Large</Button>
      <Button size="icon">🏠</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Différentes tailles disponibles pour le composant Button.',
      },
    },
  },
};

// États du bouton
export const States: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button>Normal</Button>
      <Button loading>Loading</Button>
      <Button disabled>Disabled</Button>
      <Button loading disabled>Loading + Disabled</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Différents états du bouton : normal, loading, disabled.',
      },
    },
  },
};

// Avec icônes
export const WithIcons: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button leftIcon={<span>👍</span>}>Avec icône gauche</Button>
      <Button rightIcon={<span>→</span>}>Avec icône droite</Button>
      <Button leftIcon={<span>📧</span>} rightIcon={<span>↗</span>}>
        Avec les deux icônes
      </Button>
      <Button size="icon">🏠</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Boutons avec icônes à gauche, à droite, ou les deux.',
      },
    },
  },
};

// Largeur complète
export const FullWidth: Story = {
  render: () => (
    <div className="w-80 space-y-4">
      <Button fullWidth>Bouton pleine largeur</Button>
      <Button fullWidth variant="outline">
        Outline pleine largeur
      </Button>
      <Button fullWidth loading>
        Loading pleine largeur
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Boutons qui s\'étendent sur toute la largeur de leur conteneur.',
      },
    },
  },
};

// Cas d'usage réels
export const RealWorldExamples: Story = {
  render: () => (
    <div className="space-y-6">
      {/* Actions principales */}
      <div>
        <h3 className="text-lg font-semibold mb-3">Actions principales</h3>
        <div className="flex gap-3">
          <Button variant="primary">Réserver maintenant</Button>
          <Button variant="outline">En savoir plus</Button>
        </div>
      </div>

      {/* Actions destructives */}
      <div>
        <h3 className="text-lg font-semibold mb-3">Actions destructives</h3>
        <div className="flex gap-3">
          <Button variant="destructive">Supprimer</Button>
          <Button variant="outline">Annuler</Button>
        </div>
      </div>

      {/* États de chargement */}
      <div>
        <h3 className="text-lg font-semibold mb-3">États de chargement</h3>
        <div className="flex gap-3">
          <Button loading>Traitement en cours...</Button>
          <Button variant="success" loading>
            Sauvegarde...
          </Button>
        </div>
      </div>

      {/* Navigation */}
      <div>
        <h3 className="text-lg font-semibold mb-3">Navigation</h3>
        <div className="flex gap-3">
          <Button variant="ghost" leftIcon={<span>←</span>}>
            Retour
          </Button>
          <Button rightIcon={<span>→</span>}>Suivant</Button>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Exemples d\'utilisation réelle du composant Button dans l\'application.',
      },
    },
  },
};

// Test d'accessibilité
export const Accessibility: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-3">Focus et navigation clavier</h3>
        <p className="text-sm text-gray-600 mb-3">
          Utilisez Tab pour naviguer entre les boutons, Entrée ou Espace pour les activer.
        </p>
        <div className="flex gap-3">
          <Button>Premier bouton</Button>
          <Button variant="outline">Deuxième bouton</Button>
          <Button variant="ghost">Troisième bouton</Button>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-3">États désactivés</h3>
        <div className="flex gap-3">
          <Button disabled>Bouton désactivé</Button>
          <Button loading disabled>
            Chargement désactivé
          </Button>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Tests d\'accessibilité : navigation clavier, focus, états désactivés.',
      },
    },
  },
};

// Playground interactif
export const Playground: Story = {
  args: {
    variant: 'primary',
    size: 'md',
    children: 'Bouton personnalisable',
    loading: false,
    disabled: false,
    fullWidth: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Playground interactif pour tester toutes les propriétés du composant Button.',
      },
    },
  },
};
