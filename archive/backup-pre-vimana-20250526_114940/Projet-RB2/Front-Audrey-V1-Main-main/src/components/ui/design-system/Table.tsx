/**
 * Composant Table/DataGrid Unifié - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Composant de tableau avec tri, filtrage, pagination
 * et actions en ligne.
 */

import React, { useState, useMemo } from 'react';
import { cn } from '../../../utils/cn';
import { Button } from './Button';
import { Input } from './Input';
import { Spinner } from './Spinner';

export interface Column<T = any> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

export interface TableProps<T = any> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  rowKey?: keyof T | ((record: T) => string);
  onRow?: (record: T, index: number) => {
    onClick?: () => void;
    onDoubleClick?: () => void;
    className?: string;
  };
  emptyText?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const Table = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  pagination,
  rowKey = 'id',
  onRow,
  emptyText = 'Aucune donnée disponible',
  className,
  size = 'md',
}: TableProps<T>) => {
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [filters, setFilters] = useState<Record<string, string>>({});

  // Fonction pour obtenir la clé de ligne
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return record[rowKey] || index.toString();
  };

  // Tri des données
  const sortedData = useMemo(() => {
    if (!sortConfig) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [data, sortConfig]);

  // Filtrage des données
  const filteredData = useMemo(() => {
    return sortedData.filter(record => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;
        const recordValue = record[key];
        return recordValue?.toString().toLowerCase().includes(value.toLowerCase());
      });
    });
  }, [sortedData, filters]);

  // Gestion du tri
  const handleSort = (key: string) => {
    setSortConfig(current => {
      if (current?.key === key) {
        if (current.direction === 'asc') {
          return { key, direction: 'desc' };
        } else {
          return null; // Retour à l'état non trié
        }
      }
      return { key, direction: 'asc' };
    });
  };

  // Gestion des filtres
  const handleFilter = (key: string, value: string) => {
    setFilters(current => ({
      ...current,
      [key]: value,
    }));
  };

  // Classes CSS selon la taille
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  const paddingClasses = {
    sm: 'px-2 py-1',
    md: 'px-4 py-2',
    lg: 'px-6 py-3',
  };

  return (
    <div className={cn('w-full', className)}>
      {/* Filtres */}
      {columns.some(col => col.filterable) && (
        <div className="mb-4 flex flex-wrap gap-2">
          {columns
            .filter(col => col.filterable)
            .map(column => (
              <Input
                key={column.key}
                placeholder={`Filtrer par ${column.title.toLowerCase()}`}
                value={filters[column.key] || ''}
                onChange={(e) => handleFilter(column.key, e.target.value)}
                size="sm"
                className="w-48"
              />
            ))}
        </div>
      )}

      {/* Tableau */}
      <div className="overflow-x-auto border border-neutral-200 rounded-lg">
        <table className="w-full">
          <thead className="bg-neutral-50 border-b border-neutral-200">
            <tr>
              {columns.map(column => (
                <th
                  key={column.key}
                  className={cn(
                    'font-medium text-neutral-700',
                    paddingClasses[size],
                    sizeClasses[size],
                    {
                      'text-left': column.align === 'left' || !column.align,
                      'text-center': column.align === 'center',
                      'text-right': column.align === 'right',
                      'cursor-pointer hover:bg-neutral-100': column.sortable,
                    },
                    column.className
                  )}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.title}</span>
                    {column.sortable && (
                      <span className="text-neutral-400">
                        {sortConfig?.key === column.key ? (
                          sortConfig.direction === 'asc' ? '↑' : '↓'
                        ) : (
                          '↕'
                        )}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className={cn('text-center', paddingClasses[size])}
                >
                  <div className="flex items-center justify-center py-8">
                    <Spinner size="lg" />
                    <span className="ml-2 text-neutral-600">Chargement...</span>
                  </div>
                </td>
              </tr>
            ) : filteredData.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className={cn('text-center text-neutral-500', paddingClasses[size])}
                >
                  <div className="py-8">
                    <div className="text-4xl mb-2">📭</div>
                    <div>{emptyText}</div>
                  </div>
                </td>
              </tr>
            ) : (
              filteredData.map((record, index) => {
                const rowProps = onRow?.(record, index) || {};
                return (
                  <tr
                    key={getRowKey(record, index)}
                    className={cn(
                      'border-b border-neutral-100 hover:bg-neutral-50 transition-colors',
                      rowProps.className
                    )}
                    onClick={rowProps.onClick}
                    onDoubleClick={rowProps.onDoubleClick}
                  >
                    {columns.map(column => {
                      const value = column.dataIndex ? record[column.dataIndex] : record[column.key];
                      const content = column.render
                        ? column.render(value, record, index)
                        : value;

                      return (
                        <td
                          key={column.key}
                          className={cn(
                            paddingClasses[size],
                            sizeClasses[size],
                            {
                              'text-left': column.align === 'left' || !column.align,
                              'text-center': column.align === 'center',
                              'text-right': column.align === 'right',
                            },
                            column.className
                          )}
                        >
                          {content}
                        </td>
                      );
                    })}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-neutral-600">
            Affichage de {((pagination.current - 1) * pagination.pageSize) + 1} à{' '}
            {Math.min(pagination.current * pagination.pageSize, pagination.total)} sur{' '}
            {pagination.total} éléments
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
              disabled={pagination.current <= 1}
            >
              Précédent
            </Button>
            <span className="text-sm">
              Page {pagination.current} sur {Math.ceil(pagination.total / pagination.pageSize)}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
            >
              Suivant
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

// Exemple d'utilisation avec des données de retraites
export const TableExamples = () => {
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 5,
    total: 50,
  });

  // Données d'exemple
  const retreats = [
    {
      id: '1',
      name: 'Retraite Yoga Provence',
      location: 'Provence, France',
      price: 450,
      duration: '3 jours',
      status: 'active',
      bookings: 12,
      rating: 4.8,
    },
    {
      id: '2',
      name: 'Méditation en Montagne',
      location: 'Alpes, France',
      price: 680,
      duration: '5 jours',
      status: 'active',
      bookings: 8,
      rating: 4.9,
    },
    {
      id: '3',
      name: 'Détox Digital Bretagne',
      location: 'Bretagne, France',
      price: 320,
      duration: '2 jours',
      status: 'inactive',
      bookings: 5,
      rating: 4.6,
    },
  ];

  const columns: Column[] = [
    {
      key: 'name',
      title: 'Nom de la retraite',
      dataIndex: 'name',
      sortable: true,
      filterable: true,
      render: (value, record) => (
        <div>
          <div className="font-medium text-neutral-900">{value}</div>
          <div className="text-sm text-neutral-500">{record.location}</div>
        </div>
      ),
    },
    {
      key: 'price',
      title: 'Prix',
      dataIndex: 'price',
      sortable: true,
      align: 'right',
      render: (value) => (
        <span className="font-medium text-primary-600">{value}€</span>
      ),
    },
    {
      key: 'duration',
      title: 'Durée',
      dataIndex: 'duration',
      align: 'center',
    },
    {
      key: 'status',
      title: 'Statut',
      dataIndex: 'status',
      align: 'center',
      render: (value) => (
        <span
          className={cn(
            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
            value === 'active'
              ? 'bg-success-100 text-success-800'
              : 'bg-neutral-100 text-neutral-800'
          )}
        >
          {value === 'active' ? 'Actif' : 'Inactif'}
        </span>
      ),
    },
    {
      key: 'bookings',
      title: 'Réservations',
      dataIndex: 'bookings',
      sortable: true,
      align: 'center',
    },
    {
      key: 'rating',
      title: 'Note',
      dataIndex: 'rating',
      sortable: true,
      align: 'center',
      render: (value) => (
        <div className="flex items-center justify-center space-x-1">
          <span>⭐</span>
          <span>{value}</span>
        </div>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      align: 'center',
      render: (_, record) => (
        <div className="flex items-center justify-center space-x-2">
          <Button size="sm" variant="outline">
            Modifier
          </Button>
          <Button size="sm" variant="destructive">
            Supprimer
          </Button>
        </div>
      ),
    },
  ];

  const handlePaginationChange = (page: number, pageSize: number) => {
    setLoading(true);
    setPagination(prev => ({ ...prev, current: page, pageSize }));
    
    // Simulation d'un appel API
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-6 p-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Tableau des retraites</h3>
        <Table
          columns={columns}
          data={retreats}
          loading={loading}
          pagination={{
            ...pagination,
            onChange: handlePaginationChange,
          }}
          onRow={(record) => ({
            onClick: () => console.log('Ligne cliquée:', record),
            className: 'cursor-pointer',
          })}
        />
      </div>

      <div className="flex space-x-4">
        <Button
          onClick={() => {
            setLoading(true);
            setTimeout(() => setLoading(false), 2000);
          }}
        >
          Simuler chargement
        </Button>
      </div>
    </div>
  );
};
