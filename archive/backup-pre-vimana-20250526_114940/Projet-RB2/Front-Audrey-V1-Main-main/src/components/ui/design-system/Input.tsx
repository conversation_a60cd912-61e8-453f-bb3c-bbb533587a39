/**
 * Composant Input Unifié - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Composant d'entrée standardisé avec toutes les variantes
 * et états nécessaires pour les formulaires.
 */

import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../../utils/cn';

const inputVariants = cva(
  'flex w-full rounded-md border border-neutral-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'border-neutral-300 focus-visible:ring-primary-500',
        error: 'border-error-500 focus-visible:ring-error-500',
        success: 'border-success-500 focus-visible:ring-success-500',
        warning: 'border-warning-500 focus-visible:ring-warning-500',
      },
      size: {
        sm: 'h-8 px-2 text-xs',
        md: 'h-10 px-3 text-sm',
        lg: 'h-12 px-4 text-base',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  label?: string;
  error?: string;
  success?: string;
  warning?: string;
  helper?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant,
      size,
      type = 'text',
      label,
      error,
      success,
      warning,
      helper,
      leftIcon,
      rightIcon,
      loading,
      disabled,
      ...props
    },
    ref
  ) => {
    const finalVariant = error ? 'error' : success ? 'success' : warning ? 'warning' : variant;
    const isDisabled = disabled || loading;

    return (
      <div className="w-full space-y-2">
        {label && (
          <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {label}
            {props.required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-neutral-400">
              {leftIcon}
            </div>
          )}
          
          <input
            type={type}
            className={cn(
              inputVariants({ variant: finalVariant, size }),
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              className
            )}
            ref={ref}
            disabled={isDisabled}
            {...props}
          />
          
          {(rightIcon || loading) && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400">
              {loading ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                rightIcon
              )}
            </div>
          )}
        </div>
        
        {(error || success || warning || helper) && (
          <div className="text-xs">
            {error && <p className="text-error-500">{error}</p>}
            {success && <p className="text-success-500">{success}</p>}
            {warning && <p className="text-warning-500">{warning}</p>}
            {helper && !error && !success && !warning && (
              <p className="text-neutral-500">{helper}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

// Composant TextArea
export interface TextAreaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    Omit<InputProps, 'type' | 'leftIcon' | 'rightIcon'> {
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(
  (
    {
      className,
      variant,
      size,
      label,
      error,
      success,
      warning,
      helper,
      resize = 'vertical',
      disabled,
      loading,
      ...props
    },
    ref
  ) => {
    const finalVariant = error ? 'error' : success ? 'success' : warning ? 'warning' : variant;
    const isDisabled = disabled || loading;

    return (
      <div className="w-full space-y-2">
        {label && (
          <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {label}
            {props.required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}
        
        <textarea
          className={cn(
            inputVariants({ variant: finalVariant, size }),
            'min-h-[80px]',
            {
              'resize-none': resize === 'none',
              'resize-y': resize === 'vertical',
              'resize-x': resize === 'horizontal',
              'resize': resize === 'both',
            },
            className
          )}
          ref={ref}
          disabled={isDisabled}
          {...props}
        />
        
        {(error || success || warning || helper) && (
          <div className="text-xs">
            {error && <p className="text-error-500">{error}</p>}
            {success && <p className="text-success-500">{success}</p>}
            {warning && <p className="text-warning-500">{warning}</p>}
            {helper && !error && !success && !warning && (
              <p className="text-neutral-500">{helper}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

TextArea.displayName = 'TextArea';

// Composant de recherche spécialisé
export interface SearchInputProps extends Omit<InputProps, 'leftIcon' | 'type'> {
  onSearch?: (value: string) => void;
  onClear?: () => void;
  showClearButton?: boolean;
}

export const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  (
    {
      onSearch,
      onClear,
      showClearButton = true,
      placeholder = 'Rechercher...',
      ...props
    },
    ref
  ) => {
    const [value, setValue] = React.useState(props.value || '');

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setValue(newValue);
      props.onChange?.(e);
    };

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        onSearch?.(value as string);
      }
      props.onKeyPress?.(e);
    };

    const handleClear = () => {
      setValue('');
      onClear?.();
    };

    return (
      <Input
        {...props}
        ref={ref}
        type="search"
        value={value}
        onChange={handleChange}
        onKeyPress={handleKeyPress}
        placeholder={placeholder}
        leftIcon={<span className="text-lg">🔍</span>}
        rightIcon={
          showClearButton && value ? (
            <button
              type="button"
              onClick={handleClear}
              className="hover:text-neutral-600 transition-colors"
            >
              ✕
            </button>
          ) : undefined
        }
      />
    );
  }
);

SearchInput.displayName = 'SearchInput';

// Exemples d'utilisation
export const InputExamples = () => {
  return (
    <div className="space-y-6 p-6 max-w-md">
      <div>
        <h3 className="mb-3 text-lg font-semibold">Tailles</h3>
        <div className="space-y-3">
          <Input size="sm" placeholder="Small input" />
          <Input size="md" placeholder="Medium input" />
          <Input size="lg" placeholder="Large input" />
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-lg font-semibold">États</h3>
        <div className="space-y-3">
          <Input placeholder="Input normal" />
          <Input error="Ce champ est requis" placeholder="Input avec erreur" />
          <Input success="Validation réussie" placeholder="Input avec succès" />
          <Input warning="Attention requise" placeholder="Input avec avertissement" />
          <Input disabled placeholder="Input désactivé" />
          <Input loading placeholder="Input en chargement" />
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-lg font-semibold">Avec icônes</h3>
        <div className="space-y-3">
          <Input
            leftIcon={<span>👤</span>}
            placeholder="Nom d'utilisateur"
          />
          <Input
            type="email"
            leftIcon={<span>📧</span>}
            placeholder="Email"
          />
          <Input
            type="password"
            leftIcon={<span>🔒</span>}
            rightIcon={<span>👁️</span>}
            placeholder="Mot de passe"
          />
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-lg font-semibold">Avec labels</h3>
        <div className="space-y-3">
          <Input
            label="Nom complet"
            placeholder="Entrez votre nom"
            helper="Votre nom tel qu'il apparaîtra sur votre profil"
          />
          <Input
            label="Email"
            type="email"
            placeholder="<EMAIL>"
            required
          />
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-lg font-semibold">TextArea</h3>
        <TextArea
          label="Description"
          placeholder="Décrivez votre expérience..."
          helper="Maximum 500 caractères"
          rows={4}
        />
      </div>

      <div>
        <h3 className="mb-3 text-lg font-semibold">Recherche</h3>
        <SearchInput
          placeholder="Rechercher des retraites..."
          onSearch={(value) => console.log('Recherche:', value)}
        />
      </div>
    </div>
  );
};
