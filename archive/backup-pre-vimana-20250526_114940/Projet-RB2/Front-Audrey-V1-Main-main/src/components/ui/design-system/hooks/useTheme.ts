/**
 * Hook useTheme - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Hook pour la gestion du thème de l'application
 */

import { useContext, createContext, useState, useEffect, ReactNode } from 'react';
import { theme as defaultTheme, type Theme } from '../theme';

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  mode: ThemeMode;
  setMode: (mode: ThemeMode) => void;
  isDark: boolean;
  toggleMode: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  defaultMode?: ThemeMode;
  storageKey?: string;
}

export function ThemeProvider({
  children,
  defaultMode = 'system',
  storageKey = 'retreat-and-be-theme',
}: ThemeProviderProps) {
  const [mode, setModeState] = useState<ThemeMode>(defaultMode);
  const [isDark, setIsDark] = useState(false);

  // Charger le thème depuis le localStorage
  useEffect(() => {
    const stored = localStorage.getItem(storageKey) as ThemeMode;
    if (stored && ['light', 'dark', 'system'].includes(stored)) {
      setModeState(stored);
    }
  }, [storageKey]);

  // Déterminer si le mode sombre est actif
  useEffect(() => {
    const updateTheme = () => {
      let isDarkMode = false;

      if (mode === 'system') {
        isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
      } else {
        isDarkMode = mode === 'dark';
      }

      setIsDark(isDarkMode);

      // Appliquer la classe au document
      const root = document.documentElement;
      if (isDarkMode) {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
    };

    updateTheme();

    // Écouter les changements de préférence système
    if (mode === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', updateTheme);
      return () => mediaQuery.removeEventListener('change', updateTheme);
    }
  }, [mode]);

  const setMode = (newMode: ThemeMode) => {
    setModeState(newMode);
    localStorage.setItem(storageKey, newMode);
  };

  const toggleMode = () => {
    if (mode === 'light') {
      setMode('dark');
    } else if (mode === 'dark') {
      setMode('system');
    } else {
      setMode('light');
    }
  };

  const value: ThemeContextType = {
    theme: defaultTheme,
    mode,
    setMode,
    isDark,
    toggleMode,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook pour obtenir une couleur du thème
export function useThemeColor(colorPath: string) {
  const { theme } = useTheme();
  
  const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  return getNestedValue(theme.colors, colorPath);
}

// Hook pour les breakpoints
export function useBreakpoint() {
  const { theme } = useTheme();
  const [currentBreakpoint, setCurrentBreakpoint] = useState<string>('sm');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width >= parseInt(theme.breakpoints['2xl'])) {
        setCurrentBreakpoint('2xl');
      } else if (width >= parseInt(theme.breakpoints.xl)) {
        setCurrentBreakpoint('xl');
      } else if (width >= parseInt(theme.breakpoints.lg)) {
        setCurrentBreakpoint('lg');
      } else if (width >= parseInt(theme.breakpoints.md)) {
        setCurrentBreakpoint('md');
      } else {
        setCurrentBreakpoint('sm');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, [theme.breakpoints]);

  const isBreakpoint = (breakpoint: string) => {
    const breakpoints = ['sm', 'md', 'lg', 'xl', '2xl'];
    const currentIndex = breakpoints.indexOf(currentBreakpoint);
    const targetIndex = breakpoints.indexOf(breakpoint);
    return currentIndex >= targetIndex;
  };

  return {
    currentBreakpoint,
    isBreakpoint,
    isSm: isBreakpoint('sm'),
    isMd: isBreakpoint('md'),
    isLg: isBreakpoint('lg'),
    isXl: isBreakpoint('xl'),
    is2Xl: isBreakpoint('2xl'),
  };
}

// Composant ThemeToggle pour changer de thème
export function ThemeToggle() {
  const { mode, toggleMode, isDark } = useTheme();

  const getIcon = () => {
    switch (mode) {
      case 'light':
        return '☀️';
      case 'dark':
        return '🌙';
      case 'system':
        return '💻';
      default:
        return '☀️';
    }
  };

  const getLabel = () => {
    switch (mode) {
      case 'light':
        return 'Mode clair';
      case 'dark':
        return 'Mode sombre';
      case 'system':
        return 'Mode système';
      default:
        return 'Mode clair';
    }
  };

  return (
    <button
      onClick={toggleMode}
      className="flex items-center space-x-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-neutral-100 dark:hover:bg-neutral-800"
      title={getLabel()}
    >
      <span className="text-lg">{getIcon()}</span>
      <span className="hidden sm:inline">{getLabel()}</span>
    </button>
  );
}
