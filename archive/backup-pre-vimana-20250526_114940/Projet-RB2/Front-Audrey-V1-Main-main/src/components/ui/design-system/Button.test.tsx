/**
 * Tests Unitaires - Composant Button - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Tests complets du composant Button avec toutes ses variantes,
 * tailles et états possibles.
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from './Button';

// Mock du composant Spinner
vi.mock('./Spinner', () => ({
  Spinner: ({ size, className }: { size?: string; className?: string }) => (
    <div data-testid="spinner" data-size={size} className={className}>
      Loading...
    </div>
  ),
}));

describe('Button Component', () => {
  describe('Rendering', () => {
    it('should render with default props', () => {
      render(<Button>Test Button</Button>);
      
      const button = screen.getByRole('button', { name: /test button/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('bg-primary-600'); // Classe par défaut primary
      expect(button).toHaveClass('h-10'); // Taille par défaut md
    });

    it('should render with custom text', () => {
      render(<Button>Custom Text</Button>);
      
      expect(screen.getByRole('button', { name: /custom text/i })).toBeInTheDocument();
    });

    it('should render with custom className', () => {
      render(<Button className="custom-class">Test</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });
  });

  describe('Variants', () => {
    it('should render primary variant correctly', () => {
      render(<Button variant="primary">Primary</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-primary-600');
      expect(button).toHaveClass('text-white');
    });

    it('should render secondary variant correctly', () => {
      render(<Button variant="secondary">Secondary</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-secondary-600');
      expect(button).toHaveClass('text-white');
    });

    it('should render outline variant correctly', () => {
      render(<Button variant="outline">Outline</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('border');
      expect(button).toHaveClass('border-neutral-300');
      expect(button).toHaveClass('bg-transparent');
    });

    it('should render ghost variant correctly', () => {
      render(<Button variant="ghost">Ghost</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('hover:bg-neutral-100');
    });

    it('should render destructive variant correctly', () => {
      render(<Button variant="destructive">Delete</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-error-500');
      expect(button).toHaveClass('text-white');
    });

    it('should render success variant correctly', () => {
      render(<Button variant="success">Success</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-success-500');
    });

    it('should render warning variant correctly', () => {
      render(<Button variant="warning">Warning</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-warning-500');
    });

    it('should render link variant correctly', () => {
      render(<Button variant="link">Link</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('underline-offset-4');
      expect(button).toHaveClass('hover:underline');
    });
  });

  describe('Sizes', () => {
    it('should render small size correctly', () => {
      render(<Button size="sm">Small</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-8');
      expect(button).toHaveClass('px-3');
      expect(button).toHaveClass('text-xs');
    });

    it('should render medium size correctly', () => {
      render(<Button size="md">Medium</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-10');
      expect(button).toHaveClass('px-4');
    });

    it('should render large size correctly', () => {
      render(<Button size="lg">Large</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-12');
      expect(button).toHaveClass('px-6');
      expect(button).toHaveClass('text-base');
    });

    it('should render extra large size correctly', () => {
      render(<Button size="xl">Extra Large</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-14');
      expect(button).toHaveClass('px-8');
      expect(button).toHaveClass('text-lg');
    });

    it('should render icon size correctly', () => {
      render(<Button size="icon">🏠</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-10');
      expect(button).toHaveClass('w-10');
    });
  });

  describe('States', () => {
    it('should handle disabled state', () => {
      render(<Button disabled>Disabled</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveClass('opacity-50');
      expect(button).toHaveClass('pointer-events-none');
    });

    it('should handle loading state', () => {
      render(<Button loading>Loading</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(screen.getByTestId('spinner')).toBeInTheDocument();
    });

    it('should handle loading and disabled state together', () => {
      render(<Button loading disabled>Loading Disabled</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(screen.getByTestId('spinner')).toBeInTheDocument();
    });

    it('should show spinner with correct size in loading state', () => {
      render(<Button loading size="lg">Loading Large</Button>);
      
      const spinner = screen.getByTestId('spinner');
      expect(spinner).toHaveAttribute('data-size', 'lg');
    });
  });

  describe('Icons', () => {
    it('should render left icon correctly', () => {
      render(
        <Button leftIcon={<span data-testid="left-icon">👍</span>}>
          With Left Icon
        </Button>
      );
      
      const leftIcon = screen.getByTestId('left-icon');
      expect(leftIcon).toBeInTheDocument();
      expect(leftIcon.parentElement).toHaveClass('mr-2');
    });

    it('should render right icon correctly', () => {
      render(
        <Button rightIcon={<span data-testid="right-icon">→</span>}>
          With Right Icon
        </Button>
      );
      
      const rightIcon = screen.getByTestId('right-icon');
      expect(rightIcon).toBeInTheDocument();
      expect(rightIcon.parentElement).toHaveClass('ml-2');
    });

    it('should render both left and right icons', () => {
      render(
        <Button
          leftIcon={<span data-testid="left-icon">👍</span>}
          rightIcon={<span data-testid="right-icon">→</span>}
        >
          Both Icons
        </Button>
      );
      
      expect(screen.getByTestId('left-icon')).toBeInTheDocument();
      expect(screen.getByTestId('right-icon')).toBeInTheDocument();
    });

    it('should not show icons when loading', () => {
      render(
        <Button
          loading
          leftIcon={<span data-testid="left-icon">👍</span>}
          rightIcon={<span data-testid="right-icon">→</span>}
        >
          Loading
        </Button>
      );
      
      expect(screen.queryByTestId('left-icon')).not.toBeInTheDocument();
      expect(screen.queryByTestId('right-icon')).not.toBeInTheDocument();
      expect(screen.getByTestId('spinner')).toBeInTheDocument();
    });
  });

  describe('Full Width', () => {
    it('should render full width correctly', () => {
      render(<Button fullWidth>Full Width</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('w-full');
    });

    it('should not be full width by default', () => {
      render(<Button>Normal Width</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('w-auto');
      expect(button).not.toHaveClass('w-full');
    });
  });

  describe('Interactions', () => {
    it('should handle click events', async () => {
      const handleClick = vi.fn();
      const user = userEvent.setup();
      
      render(<Button onClick={handleClick}>Clickable</Button>);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('should not trigger click when disabled', async () => {
      const handleClick = vi.fn();
      const user = userEvent.setup();
      
      render(<Button onClick={handleClick} disabled>Disabled</Button>);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('should not trigger click when loading', async () => {
      const handleClick = vi.fn();
      const user = userEvent.setup();
      
      render(<Button onClick={handleClick} loading>Loading</Button>);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('should handle keyboard navigation', async () => {
      const handleClick = vi.fn();
      const user = userEvent.setup();
      
      render(<Button onClick={handleClick}>Keyboard</Button>);
      
      const button = screen.getByRole('button');
      button.focus();
      
      expect(button).toHaveFocus();
      
      await user.keyboard('{Enter}');
      expect(handleClick).toHaveBeenCalledTimes(1);
      
      await user.keyboard(' ');
      expect(handleClick).toHaveBeenCalledTimes(2);
    });

    it('should handle focus and blur events', async () => {
      const handleFocus = vi.fn();
      const handleBlur = vi.fn();
      
      render(
        <Button onFocus={handleFocus} onBlur={handleBlur}>
          Focus Test
        </Button>
      );
      
      const button = screen.getByRole('button');
      
      fireEvent.focus(button);
      expect(handleFocus).toHaveBeenCalledTimes(1);
      
      fireEvent.blur(button);
      expect(handleBlur).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<Button aria-label="Custom label">Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Custom label');
    });

    it('should be focusable by default', () => {
      render(<Button>Focusable</Button>);
      
      const button = screen.getByRole('button');
      expect(button).not.toHaveAttribute('tabindex', '-1');
    });

    it('should not be focusable when disabled', () => {
      render(<Button disabled>Not Focusable</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('should have proper role', () => {
      render(<Button>Role Test</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });
  });

  describe('Custom Props', () => {
    it('should pass through HTML button props', () => {
      render(
        <Button
          type="submit"
          form="test-form"
          data-testid="custom-button"
          aria-describedby="description"
        >
          Custom Props
        </Button>
      );
      
      const button = screen.getByTestId('custom-button');
      expect(button).toHaveAttribute('type', 'submit');
      expect(button).toHaveAttribute('form', 'test-form');
      expect(button).toHaveAttribute('aria-describedby', 'description');
    });

    it('should forward ref correctly', () => {
      const ref = vi.fn();
      
      render(<Button ref={ref}>Ref Test</Button>);
      
      expect(ref).toHaveBeenCalledWith(expect.any(HTMLButtonElement));
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty children', () => {
      render(<Button></Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toBeEmptyDOMElement();
    });

    it('should handle complex children', () => {
      render(
        <Button>
          <span>Complex</span>
          <strong>Children</strong>
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveTextContent('ComplexChildren');
      expect(screen.getByText('Complex')).toBeInTheDocument();
      expect(screen.getByText('Children')).toBeInTheDocument();
    });

    it('should handle rapid state changes', async () => {
      const { rerender } = render(<Button loading={false}>Test</Button>);
      
      const button = screen.getByRole('button');
      expect(button).not.toBeDisabled();
      
      rerender(<Button loading={true}>Test</Button>);
      expect(button).toBeDisabled();
      expect(screen.getByTestId('spinner')).toBeInTheDocument();
      
      rerender(<Button loading={false}>Test</Button>);
      expect(button).not.toBeDisabled();
      expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
    });
  });
});
