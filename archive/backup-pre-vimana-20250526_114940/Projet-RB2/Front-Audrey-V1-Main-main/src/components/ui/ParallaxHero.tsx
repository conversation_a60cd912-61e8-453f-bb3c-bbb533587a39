import React, { useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

const ParallaxHero: React.FC = () => {
  const [elementTop, setElementTop] = useState(0);
  const ref = React.useRef<HTMLDivElement>(null);

  const { scrollY } = useScroll();
  const y = useTransform(scrollY, [elementTop - 500, elementTop + 500], [-150, 150]);

  useEffect(() => {
    if (ref.current) {
      setElementTop(ref.current.offsetTop);
    }
  }, []);

  return (
    <section ref={ref} className='relative h-[60vh] overflow-hidden'>
      {/* Image Parallax */}
      <motion.div className='absolute inset-0' style={{ y }}>
        <img
          src='/images/parallax-retreat.jpg'
          alt='Paysage de retraite paisible'
          className='w-full h-[130%] object-cover'
        />
      </motion.div>

      {/* Overlay gradient */}
      <div className='absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white opacity-70' />

      {/* Contenu */}
      <div className='relative h-full w-full px-4 sm:px-6 lg:px-8 flex flex-col justify-center items-center'>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className='backdrop-blur-sm bg-white/40 p-8 sm:p-10 lg:p-12 rounded-3xl shadow-xl border border-white/20 
                     w-full md:w-[90%] lg:w-[85%] xl:w-[80%] mx-auto text-center flex flex-col items-center'
        >
          <h1 className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 drop-shadow-sm mb-4'>
            Découvrez nos retraites d&apos;exception
          </h1>
          <p className='text-lg md:text-xl text-gray-900 max-w-2xl mx-auto mb-6'>
            Des expériences uniques dans des lieux inspirants pour votre bien-être
          </p>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className='px-8 py-3 bg-retreat-green text-white rounded-full text-base font-semibold 
                     hover:bg-opacity-90 transition-all duration-300 shadow-lg hover:shadow-2xl'
          >
            Explorer nos retraites
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default ParallaxHero;
