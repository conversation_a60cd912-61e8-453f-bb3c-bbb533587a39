import React, { useRef, useState } from 'react';
import { motion } from 'framer-motion';
import {
  GlobeEuropeAfricaIcon,
  GlobeAmericasIcon,
  GlobeAsiaAustraliaIcon,
  HeartIcon,
  SparklesIcon,
  SunIcon,
  UserGroupIcon,
  BookOpenIcon,
  CloudIcon,
  MoonIcon,
  HomeIcon,
  FireIcon,
  CakeIcon,
  CameraIcon,
  PaintBrushIcon,
  MusicalNoteIcon,
  MapIcon,
  BoltIcon,
  BanknotesIcon,
  RocketLaunchIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';

interface SearchCategory {
  icon: React.ComponentType<{ className: string }>;
  label: string;
  href: string;
}

const categories: SearchCategory[] = [
  // Continents
  {
    icon: GlobeEuropeAfricaIcon as React.ComponentType<{ className: string }>,
    label: 'Europe',
    href: '/search/europe',
  },
  {
    icon: GlobeAsiaAustraliaIcon as React.ComponentType<{ className: string }>,
    label: '<PERSON><PERSON>',
    href: '/search/asie',
  },
  {
    icon: GlobeAmericasIcon as React.ComponentType<{ className: string }>,
    label: 'Amériques',
    href: '/search/ameriques',
  },
  {
    icon: GlobeEuropeAfricaIcon as React.ComponentType<{ className: string }>,
    label: 'Afrique',
    href: '/search/afrique',
  },
  // Catégories
  {
    icon: CloudIcon as React.ComponentType<{ className: string }>,
    label: 'Nature',
    href: '/search/nature',
  },
  // Hébergements
  {
    icon: HomeIcon as React.ComponentType<{ className: string }>,
    label: 'Villas',
    href: '/search/villas',
  },
  // Pratiques
  {
    icon: HeartIcon as React.ComponentType<{ className: string }>,
    label: 'Bien-être',
    href: '/search/bien-etre',
  },
  {
    icon: SparklesIcon as React.ComponentType<{ className: string }>,
    label: 'Méditation',
    href: '/search/meditation',
  },
  {
    icon: SunIcon as React.ComponentType<{ className: string }>,
    label: 'Yoga',
    href: '/search/yoga',
  },
  {
    icon: MoonIcon as React.ComponentType<{ className: string }>,
    label: 'Sophrologie',
    href: '/search/sophrologie',
  },
  {
    icon: UserGroupIcon as React.ComponentType<{ className: string }>,
    label: 'Développement personnel',
    href: '/search/developpement-personnel',
  },
  {
    icon: BookOpenIcon as React.ComponentType<{ className: string }>,
    label: 'Art-thérapie',
    href: '/search/art-therapie',
  },
  // Nouvelles propositions
  {
    icon: FireIcon as React.ComponentType<{ className: string }>,
    label: 'Detox',
    href: '/search/detox',
  },
  {
    icon: CakeIcon as React.ComponentType<{ className: string }>,
    label: 'Cuisine',
    href: '/search/cuisine',
  },
  {
    icon: CameraIcon as React.ComponentType<{ className: string }>,
    label: 'Photo',
    href: '/search/photo',
  },
  {
    icon: PaintBrushIcon as React.ComponentType<{ className: string }>,
    label: 'Créativité',
    href: '/search/creativite',
  },
  {
    icon: MusicalNoteIcon as React.ComponentType<{ className: string }>,
    label: 'Musique',
    href: '/search/musique',
  },
  // Nouvelles catégories
  {
    icon: MapIcon as React.ComponentType<{ className: string }>,
    label: 'Randonnée',
    href: '/search/randonnee',
  },
  {
    icon: BoltIcon as React.ComponentType<{ className: string }>,
    label: 'Jeûne',
    href: '/search/jeune',
  },
  {
    icon: MusicalNoteIcon as React.ComponentType<{ className: string }>,
    label: 'Danse',
    href: '/search/danse',
  },
  {
    icon: RocketLaunchIcon as React.ComponentType<{ className: string }>,
    label: 'Sport',
    href: '/search/sport',
  },
  {
    icon: HeartIcon as React.ComponentType<{ className: string }>,
    label: 'Couples',
    href: '/search/couples',
  },
  {
    icon: SparklesIcon as React.ComponentType<{ className: string }>,
    label: 'Luxe',
    href: '/search/luxe',
  },
  {
    icon: BanknotesIcon as React.ComponentType<{ className: string }>,
    label: 'Budget',
    href: '/search/budget',
  },
];

const IconSearch: React.FC = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const scrollAmount = 200;
      const newScrollPosition =
        scrollContainerRef.current.scrollLeft +
        (direction === 'left' ? -scrollAmount : scrollAmount);

      scrollContainerRef.current.scrollTo({
        left: newScrollPosition,
        behavior: 'smooth',
      });
    }
  };

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  return (
    <div className='bg-white shadow-sm pt-4'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative'>
        {/* Flèche gauche */}
        {showLeftArrow && (
          <button
            onClick={() => scroll('left')}
            className='absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 backdrop-blur-sm rounded-full p-1 shadow-md hover:bg-white transition-all duration-200'
            aria-label='Défiler vers la gauche'
          >
            <ChevronLeftIcon className='w-6 h-6 text-retreat-green' />
          </button>
        )}

        {/* Flèche droite */}
        {showRightArrow && (
          <button
            onClick={() => scroll('right')}
            className='absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 backdrop-blur-sm rounded-full p-1 shadow-md hover:bg-white transition-all duration-200'
            aria-label='Défiler vers la droite'
          >
            <ChevronRightIcon className='w-6 h-6 text-retreat-green' />
          </button>
        )}

        {/* Conteneur défilant */}
        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className='flex items-center space-x-8 overflow-x-auto hide-scrollbar py-2 px-6'
        >
          {categories.map((category) => (
            <motion.a
              key={category.label}
              href={category.href}
              className='flex flex-col items-center min-w-fit group'
              whileHover={{ y: -2 }}
              transition={{ duration: 0.2 }}
            >
              <div className='p-2 rounded-full bg-gray-50 group-hover:bg-gray-100 transition-colors duration-200'>
                <category.icon className='w-4 h-4 text-retreat-green' />
              </div>
              <span className='mt-0.5 text-xs text-gray-600 group-hover:text-retreat-green transition-colors duration-200 whitespace-nowrap'>
                {category.label}
              </span>
            </motion.a>
          ))}
        </div>
      </div>
    </div>
  );
};

export default IconSearch;
