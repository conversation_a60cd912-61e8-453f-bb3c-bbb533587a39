import React from 'react';
import MainLayout from '../MainLayout/MainLayout';
import { motion } from 'framer-motion';

interface RetreatLayoutProps {
  children: React.ReactNode;
  className?: string;
  heroImage?: string;
  heroTitle?: string;
  heroSubtitle?: string;
  showSearchPanel?: boolean;
}

const RetreatLayout: React.FC<RetreatLayoutProps> = ({
  children,
  className = '',
  heroImage,
  heroTitle,
  heroSubtitle,
  showSearchPanel = true,
}) => {
  return (
    <MainLayout>
      {/* Hero Section */}
      {heroImage && (
        <div className='relative h-[40vh] min-h-[400px] bg-gray-900'>
          <img
            src={heroImage}
            alt={heroTitle}
            className='absolute inset-0 w-full h-full object-cover opacity-60'
          />
          <div className='absolute inset-0 bg-gradient-to-b from-black/50 to-black/20' />

          <div className='relative h-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col justify-center'>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className='text-center'
            >
              {heroTitle && (
                <h1 className='text-4xl md:text-5xl font-bold text-white mb-4'>{heroTitle}</h1>
              )}
              {heroSubtitle && (
                <p className='text-xl text-gray-200 max-w-2xl mx-auto'>{heroSubtitle}</p>
              )}
            </motion.div>
          </div>
        </div>
      )}

      {/* Search Panel */}
      {showSearchPanel && (
        <div className='bg-white shadow-sm border-b'>
          <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4'>
            {/* Le composant SearchPanel sera intégré ici */}
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 ${className}`}>{children}</div>
    </MainLayout>
  );
};

export default RetreatLayout;
