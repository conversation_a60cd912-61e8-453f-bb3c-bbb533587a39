import React from 'react';
import MainLayout from '../MainLayout/MainLayout';
import { motion } from 'framer-motion';

interface BlogLayoutProps {
  children: React.ReactNode;
  className?: string;
  heroImage?: string;
  heroTitle?: string;
  heroSubtitle?: string;
  author?: {
    name: string;
    avatar: string;
    role?: string;
  };
  date?: string;
  readingTime?: string;
  tags?: string[];
}

const BlogLayout: React.FC<BlogLayoutProps> = ({
  children,
  className = '',
  heroImage,
  heroTitle,
  heroSubtitle,
  author,
  date,
  readingTime,
  tags = [],
}) => {
  return (
    <MainLayout>
      {/* Hero Section */}
      {heroImage && (
        <div className='relative h-[50vh] min-h-[500px] bg-gray-900'>
          <img
            src={heroImage}
            alt={heroTitle}
            className='absolute inset-0 w-full h-full object-cover opacity-70'
          />
          <div className='absolute inset-0 bg-gradient-to-b from-black/60 to-black/30' />

          <div className='relative h-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col justify-center'>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className='text-center'
            >
              {tags.length > 0 && (
                <div className='flex justify-center gap-2 mb-4'>
                  {tags.map((tag) => (
                    <span
                      key={tag}
                      className='px-3 py-1 text-sm font-medium text-white bg-white/20 rounded-full backdrop-blur-sm'
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              {heroTitle && (
                <h1 className='text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6'>
                  {heroTitle}
                </h1>
              )}

              {heroSubtitle && (
                <p className='text-xl text-gray-200 max-w-2xl mx-auto mb-8'>{heroSubtitle}</p>
              )}

              {/* Author Info */}
              {author && (
                <div className='flex items-center justify-center gap-4'>
                  <img
                    src={author.avatar}
                    alt={author.name}
                    className='w-12 h-12 rounded-full border-2 border-white'
                  />
                  <div className='text-left'>
                    <p className='text-white font-medium'>{author.name}</p>
                    {author.role && <p className='text-gray-300 text-sm'>{author.role}</p>}
                  </div>
                  {(date || readingTime) && (
                    <div className='text-gray-300 text-sm'>
                      {date && <span>{date}</span>}
                      {date && readingTime && <span className='mx-2'>•</span>}
                      {readingTime && <span>{readingTime} de lecture</span>}
                    </div>
                  )}
                </div>
              )}
            </motion.div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <article className={`max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 ${className}`}>
        {children}
      </article>
    </MainLayout>
  );
};

export default BlogLayout;
