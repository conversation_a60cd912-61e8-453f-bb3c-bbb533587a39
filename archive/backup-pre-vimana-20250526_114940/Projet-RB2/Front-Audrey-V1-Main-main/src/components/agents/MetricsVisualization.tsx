/**
 * Visualisation des Métriques
 * Graphiques et tableaux de bord pour les métriques système
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Grid,
  Select,
  Badge
} from '../ui/design-system';
import { useToast } from '../ui/design-system/Toast';
import { metricsService } from '../../services/api/metricsService';

interface SystemMetrics {
  totalAgents: number;
  onlineAgents: number;
  totalJobs: number;
  activeJobs: number;
  completedJobs: number;
  failedJobs: number;
  averageResponseTime: number;
  systemUptime: number;
  alertsCount: number;
  criticalAlertsCount: number;
}

interface MetricData {
  timestamp: Date;
  value: number;
  label?: string;
}

interface ChartData {
  name: string;
  data: MetricData[];
  color: string;
  unit: string;
}

interface MetricsVisualizationProps {
  systemMetrics: SystemMetrics | null;
}

export const MetricsVisualization: React.FC<MetricsVisualizationProps> = ({
  systemMetrics
}) => {
  const { toast } = useToast();
  
  // États
  const [timeRange, setTimeRange] = useState<'1h' | '6h' | '24h' | '7d' | '30d'>('1h');
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['response_time', 'cpu_usage', 'memory_usage']);
  const [chartsData, setChartsData] = useState<ChartData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Charger les données des métriques
  const loadMetricsData = async () => {
    try {
      setIsLoading(true);
      const response = await metricsService.getMetricsHistory(timeRange, selectedMetrics);
      setChartsData(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement des métriques:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les métriques',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadMetricsData();
  }, [timeRange, selectedMetrics]);

  // Métriques disponibles
  const availableMetrics = [
    { id: 'response_time', name: 'Temps de réponse', color: '#3B82F6', unit: 'ms' },
    { id: 'cpu_usage', name: 'Utilisation CPU', color: '#EF4444', unit: '%' },
    { id: 'memory_usage', name: 'Utilisation mémoire', color: '#10B981', unit: '%' },
    { id: 'disk_usage', name: 'Utilisation disque', color: '#F59E0B', unit: '%' },
    { id: 'network_io', name: 'I/O réseau', color: '#8B5CF6', unit: 'MB/s' },
    { id: 'requests_per_second', name: 'Requêtes/sec', color: '#06B6D4', unit: 'req/s' },
    { id: 'error_rate', name: 'Taux d\'erreur', color: '#DC2626', unit: '%' },
    { id: 'active_connections', name: 'Connexions actives', color: '#059669', unit: 'conn' }
  ];

  // Composant graphique simple (à remplacer par une vraie librairie de charts)
  const SimpleChart: React.FC<{ data: ChartData; height?: number }> = ({ data, height = 200 }) => {
    const maxValue = Math.max(...data.data.map(d => d.value));
    const minValue = Math.min(...data.data.map(d => d.value));
    const range = maxValue - minValue || 1;

    return (
      <div className="relative" style={{ height }}>
        <svg width="100%" height="100%" className="overflow-visible">
          {/* Grille */}
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          {/* Ligne de données */}
          <polyline
            fill="none"
            stroke={data.color}
            strokeWidth="2"
            points={data.data.map((point, index) => {
              const x = (index / (data.data.length - 1)) * 100;
              const y = 100 - ((point.value - minValue) / range) * 100;
              return `${x}%,${y}%`;
            }).join(' ')}
          />
          
          {/* Points */}
          {data.data.map((point, index) => {
            const x = (index / (data.data.length - 1)) * 100;
            const y = 100 - ((point.value - minValue) / range) * 100;
            return (
              <circle
                key={index}
                cx={`${x}%`}
                cy={`${y}%`}
                r="3"
                fill={data.color}
                className="hover:r-5 transition-all"
              />
            );
          })}
        </svg>
        
        {/* Légendes */}
        <div className="absolute top-2 left-2 text-xs text-gray-500">
          Max: {maxValue.toFixed(1)}{data.unit}
        </div>
        <div className="absolute bottom-2 left-2 text-xs text-gray-500">
          Min: {minValue.toFixed(1)}{data.unit}
        </div>
      </div>
    );
  };

  // Composant de métrique en temps réel
  const RealTimeMetric: React.FC<{ title: string; value: number; unit: string; color: string; trend?: 'up' | 'down' | 'stable' }> = ({
    title, value, unit, color, trend
  }) => (
    <Card>
      <CardContent className="pt-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold" style={{ color }}>
                {value.toFixed(1)}
              </span>
              <span className="text-sm text-gray-500">{unit}</span>
              {trend && (
                <span className={`text-sm ${
                  trend === 'up' ? 'text-red-500' : 
                  trend === 'down' ? 'text-green-500' : 
                  'text-gray-500'
                }`}>
                  {trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '➡️'}
                </span>
              )}
            </div>
          </div>
          <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{ backgroundColor: `${color}20` }}>
            <div className="w-6 h-6 rounded-full" style={{ backgroundColor: color }} />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Contrôles */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Métriques du Système</h2>
          <p className="text-gray-600">Visualisation des performances en temps réel</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Période:</label>
            <Select value={timeRange} onValueChange={(value) => setTimeRange(value as any)}>
              <option value="1h">1 heure</option>
              <option value="6h">6 heures</option>
              <option value="24h">24 heures</option>
              <option value="7d">7 jours</option>
              <option value="30d">30 jours</option>
            </Select>
          </div>
          
          <Button onClick={loadMetricsData} disabled={isLoading} leftIcon={isLoading ? "⏳" : "🔄"}>
            Actualiser
          </Button>
        </div>
      </div>

      {/* Métriques en temps réel */}
      {systemMetrics && (
        <div>
          <h3 className="text-lg font-medium mb-4">Métriques en temps réel</h3>
          <Grid cols={4} gap="md">
            <RealTimeMetric
              title="Temps de réponse moyen"
              value={systemMetrics.averageResponseTime}
              unit="ms"
              color="#3B82F6"
              trend="stable"
            />
            <RealTimeMetric
              title="Agents en ligne"
              value={(systemMetrics.onlineAgents / systemMetrics.totalAgents) * 100}
              unit="%"
              color="#10B981"
              trend="up"
            />
            <RealTimeMetric
              title="Jobs actifs"
              value={systemMetrics.activeJobs}
              unit="jobs"
              color="#F59E0B"
              trend="stable"
            />
            <RealTimeMetric
              title="Taux de succès"
              value={((systemMetrics.completedJobs / (systemMetrics.completedJobs + systemMetrics.failedJobs)) * 100) || 0}
              unit="%"
              color="#059669"
              trend="up"
            />
          </Grid>
        </div>
      )}

      {/* Sélection des métriques */}
      <Card>
        <CardHeader>
          <CardTitle>Métriques à afficher</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {availableMetrics.map((metric) => (
              <Badge
                key={metric.id}
                variant={selectedMetrics.includes(metric.id) ? 'default' : 'outline'}
                className="cursor-pointer"
                onClick={() => {
                  if (selectedMetrics.includes(metric.id)) {
                    setSelectedMetrics(selectedMetrics.filter(m => m !== metric.id));
                  } else {
                    setSelectedMetrics([...selectedMetrics, metric.id]);
                  }
                }}
              >
                <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: metric.color }} />
                {metric.name}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {chartsData.map((chartData) => (
          <motion.div
            key={chartData.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: chartData.color }} />
                  <span>{chartData.name}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center h-48">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : (
                  <SimpleChart data={chartData} height={200} />
                )}
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Tableau de données */}
      <Card>
        <CardHeader>
          <CardTitle>Données détaillées</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Métrique</th>
                  <th className="text-left py-2">Valeur actuelle</th>
                  <th className="text-left py-2">Moyenne 1h</th>
                  <th className="text-left py-2">Maximum 24h</th>
                  <th className="text-left py-2">Minimum 24h</th>
                  <th className="text-left py-2">Tendance</th>
                </tr>
              </thead>
              <tbody>
                {availableMetrics.filter(m => selectedMetrics.includes(m.id)).map((metric) => {
                  const chartData = chartsData.find(c => c.name.toLowerCase().includes(metric.id.replace('_', ' ')));
                  const currentValue = chartData?.data[chartData.data.length - 1]?.value || 0;
                  const avgValue = chartData?.data.reduce((sum, d) => sum + d.value, 0) / (chartData?.data.length || 1) || 0;
                  const maxValue = Math.max(...(chartData?.data.map(d => d.value) || [0]));
                  const minValue = Math.min(...(chartData?.data.map(d => d.value) || [0]));
                  
                  return (
                    <tr key={metric.id} className="border-b">
                      <td className="py-2 flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full" style={{ backgroundColor: metric.color }} />
                        <span>{metric.name}</span>
                      </td>
                      <td className="py-2 font-medium">{currentValue.toFixed(1)} {metric.unit}</td>
                      <td className="py-2">{avgValue.toFixed(1)} {metric.unit}</td>
                      <td className="py-2">{maxValue.toFixed(1)} {metric.unit}</td>
                      <td className="py-2">{minValue.toFixed(1)} {metric.unit}</td>
                      <td className="py-2">
                        <span className={`text-sm ${
                          currentValue > avgValue ? 'text-red-500' : 
                          currentValue < avgValue ? 'text-green-500' : 
                          'text-gray-500'
                        }`}>
                          {currentValue > avgValue ? '↗️ Hausse' : 
                           currentValue < avgValue ? '↘️ Baisse' : 
                           '➡️ Stable'}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
