import React from 'react';
import { useNavigate } from 'react-router-dom';
import { socialGroupRecommendationService, CollaborativePlanStatus } from '../../services/api/socialGroupRecommendationService';
import { useAuthContext } from '../../hooks/useAuthContext';
import { t } from '../../services/i18n/i18nService';
import { formatDate } from '../../utils/dateUtils';

/**
 * Interface pour les propriétés du composant
 */
interface CollaborativePlanCardProps {
  /** Plan collaboratif */
  plan: any;
  
  /** Classe CSS supplémentaire */
  className?: string;
  
  /** Fonction appelée lors de l'annulation du plan */
  onCancel?: (planId: string) => void;
}

/**
 * Composant pour afficher une carte de plan collaboratif
 */
const CollaborativePlanCard: React.FC<CollaborativePlanCardProps> = ({
  plan,
  className = '',
  onCancel,
}) => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  // Vérifier si l'utilisateur est le créateur du plan
  const isCreator = user?.id === plan.creatorId;
  
  // Vérifier si l'utilisateur a déjà voté
  const hasVoted = plan.recommendations?.some((recommendation: any) => 
    recommendation.votes?.some((vote: any) => vote.userId === user?.id)
  );
  
  // Calculer le nombre de participants qui ont voté
  const participantsCount = plan.participants?.length || 0;
  const votedParticipantsCount = new Set(
    plan.recommendations?.flatMap((recommendation: any) => 
      recommendation.votes?.map((vote: any) => vote.userId)
    )
  ).size;
  
  // Gérer le clic sur la carte
  const handleClick = () => {
    navigate(`/collaborative-plan/${plan.id}`);
  };
  
  // Gérer l'annulation du plan
  const handleCancel = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (onCancel) {
      onCancel(plan.id);
    }
  };
  
  return (
    <div 
      className={`bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow ${className}`}
      onClick={handleClick}
    >
      <div className={`p-4 ${socialGroupRecommendationService.getPlanStatusBackgroundClass(plan.status)}`}>
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900 truncate">{plan.title}</h3>
          <span className={`text-sm font-medium px-2 py-1 rounded-full ${socialGroupRecommendationService.getPlanStatusColorClass(plan.status)} bg-opacity-20`}>
            {socialGroupRecommendationService.getPlanStatusName(plan.status)}
          </span>
        </div>
      </div>
      
      <div className="p-4">
        {plan.description && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">{plan.description}</p>
        )}
        
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <svg className="h-4 w-4 text-gray-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
          <span>{t('collaborative.createdBy')} {plan.creator?.username || 'Unknown'}</span>
        </div>
        
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <svg className="h-4 w-4 text-gray-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
          </svg>
          <span>{t('collaborative.participantsCount', { count: participantsCount })}</span>
        </div>
        
        {plan.startDate && plan.endDate && (
          <div className="flex items-center text-sm text-gray-500 mb-2">
            <svg className="h-4 w-4 text-gray-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            <span>
              {formatDate(new Date(plan.startDate))} - {formatDate(new Date(plan.endDate))}
            </span>
          </div>
        )}
        
        {plan.status === CollaborativePlanStatus.VOTING && (
          <div className="mt-4">
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-gray-500">{t('collaborative.votingProgress')}</span>
              <span className="text-xs text-gray-500">{votedParticipantsCount}/{participantsCount}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full" 
                style={{ width: `${(votedParticipantsCount / participantsCount) * 100}%` }}
              ></div>
            </div>
          </div>
        )}
        
        {plan.status === CollaborativePlanStatus.FINALIZED && plan.selectedRecommendationIds?.length > 0 && (
          <div className="mt-4">
            <span className="text-xs text-gray-500">{t('collaborative.selectedRecommendationsCount', { count: plan.selectedRecommendationIds.length })}</span>
          </div>
        )}
        
        <div className="mt-4 flex justify-between">
          {plan.status === CollaborativePlanStatus.VOTING && !hasVoted && (
            <span className="text-sm text-yellow-500 font-medium">
              {t('collaborative.needsYourVote')}
            </span>
          )}
          
          {plan.status === CollaborativePlanStatus.VOTING && hasVoted && (
            <span className="text-sm text-green-500 font-medium">
              {t('collaborative.youVoted')}
            </span>
          )}
          
          {plan.status === CollaborativePlanStatus.DRAFT && (
            <span className="text-sm text-gray-500 font-medium">
              {t('collaborative.draft')}
            </span>
          )}
          
          {plan.status === CollaborativePlanStatus.FINALIZED && (
            <span className="text-sm text-green-500 font-medium">
              {t('collaborative.finalized')}
            </span>
          )}
          
          {plan.status === CollaborativePlanStatus.CANCELLED && (
            <span className="text-sm text-red-500 font-medium">
              {t('collaborative.cancelled')}
            </span>
          )}
          
          {isCreator && plan.status === CollaborativePlanStatus.VOTING && (
            <button
              onClick={handleCancel}
              className="text-sm text-red-500 hover:text-red-700 transition-colors"
            >
              {t('collaborative.cancel')}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CollaborativePlanCard;
