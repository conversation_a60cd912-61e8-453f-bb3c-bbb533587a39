/**
 * Setup Tests - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Configuration globale pour les tests unitaires
 * avec mocks et utilitaires de test.
 */

import '@testing-library/jest-dom';
import { expect, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { cleanup } from '@testing-library/react';
import { server } from './mocks/server';

// Configuration globale des mocks
beforeAll(() => {
  // Démarrer le serveur MSW pour les mocks d'API
  server.listen({ onUnhandledRequest: 'error' });
  
  // Mock de window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock de ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock de IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock de localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  vi.stubGlobal('localStorage', localStorageMock);

  // Mock de sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  vi.stubGlobal('sessionStorage', sessionStorageMock);

  // Mock de fetch si nécessaire
  global.fetch = vi.fn();

  // Mock de console pour éviter les logs pendant les tests
  vi.spyOn(console, 'warn').mockImplementation(() => {});
  vi.spyOn(console, 'error').mockImplementation(() => {});
});

afterEach(() => {
  // Nettoyer après chaque test
  cleanup();
  
  // Réinitialiser tous les mocks
  vi.clearAllMocks();
  
  // Réinitialiser le serveur MSW
  server.resetHandlers();
  
  // Nettoyer le localStorage mock
  localStorage.clear();
  sessionStorage.clear();
});

afterAll(() => {
  // Fermer le serveur MSW
  server.close();
  
  // Restaurer tous les mocks
  vi.restoreAllMocks();
});

// Utilitaires de test personnalisés
export const createMockUser = (overrides = {}) => ({
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'user',
  avatar: 'https://example.com/avatar.jpg',
  preferences: {
    theme: 'light',
    language: 'fr',
    notifications: {
      email: true,
      push: true,
      marketing: false,
    },
    privacy: {
      profileVisible: true,
      activityVisible: false,
    },
  },
  ...overrides,
});

export const createMockRetreat = (overrides = {}) => ({
  id: 'retreat-1',
  title: 'Test Yoga Retreat',
  description: 'A wonderful yoga retreat in the mountains',
  image: 'https://example.com/retreat.jpg',
  price: 299,
  duration: '3 days',
  location: 'Provence, France',
  rating: 4.8,
  category: 'Yoga',
  instructor: {
    name: 'Jane Doe',
    bio: 'Experienced yoga instructor',
  },
  amenities: ['Meals included', 'Yoga mats provided', 'Meditation sessions'],
  availableDates: ['2025-06-15', '2025-06-22', '2025-06-29'],
  ...overrides,
});

export const createMockProfessional = (overrides = {}) => ({
  id: 'professional-1',
  name: 'Dr. Smith',
  title: 'Wellness Coach',
  avatar: 'https://example.com/professional.jpg',
  specialties: ['Stress Management', 'Nutrition', 'Mindfulness'],
  rating: 4.9,
  reviewCount: 127,
  price: 75,
  available: true,
  bio: 'Experienced wellness professional with 10+ years of practice',
  ...overrides,
});

// Mock des hooks personnalisés
export const mockUseAuth = (user = null) => {
  vi.mock('../store/globalStore', () => ({
    useAuth: () => ({
      user,
      isAuthenticated: !!user,
      isLoading: false,
      login: vi.fn(),
      logout: vi.fn(),
      setUser: vi.fn(),
    }),
  }));
};

export const mockUseToast = () => {
  const mockToast = {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
    default: vi.fn(),
  };

  vi.mock('../components/ui/design-system/Toast', () => ({
    useToast: () => ({
      toast: mockToast,
      removeToast: vi.fn(),
      clearToasts: vi.fn(),
    }),
  }));

  return mockToast;
};

// Utilitaires pour les tests d'accessibilité
export const axeMatchers = {
  toHaveNoViolations: expect.extend({
    toHaveNoViolations(received) {
      const violations = received.violations || [];
      const pass = violations.length === 0;
      
      if (pass) {
        return {
          message: () => 'Expected to have accessibility violations, but none were found',
          pass: true,
        };
      } else {
        const violationMessages = violations.map((violation: any) => 
          `${violation.id}: ${violation.description}`
        ).join('\n');
        
        return {
          message: () => `Expected no accessibility violations, but found:\n${violationMessages}`,
          pass: false,
        };
      }
    },
  }),
};

// Configuration des timeouts pour les tests async
export const TEST_TIMEOUT = 5000;

// Mock des animations pour les tests
export const mockAnimations = () => {
  vi.mock('framer-motion', () => ({
    motion: {
      div: 'div',
      button: 'button',
      span: 'span',
    },
    AnimatePresence: ({ children }: { children: React.ReactNode }) => children,
  }));
};

// Utilitaire pour attendre les mises à jour async
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

// Mock des API calls
export const mockApiCall = (url: string, response: any, status = 200) => {
  (global.fetch as any).mockResolvedValueOnce({
    ok: status >= 200 && status < 300,
    status,
    json: async () => response,
    text: async () => JSON.stringify(response),
  });
};

// Utilitaire pour simuler les erreurs réseau
export const mockNetworkError = () => {
  (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));
};

// Configuration des variables d'environnement pour les tests
process.env.NODE_ENV = 'test';
process.env.VITE_API_URL = 'http://localhost:3001/api';
process.env.VITE_APP_URL = 'http://localhost:3000';
