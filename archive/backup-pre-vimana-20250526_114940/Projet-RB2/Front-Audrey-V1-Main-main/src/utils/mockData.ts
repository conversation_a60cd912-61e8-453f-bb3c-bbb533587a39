// Utility functions to generate mock data for development

interface Post {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl?: string;
  createdAt: string;
  userId: string;
  userName: string;
  userAvatar: string;
  likes: number;
  comments: number;
  views: number;
  status: 'published' | 'archived' | 'deleted';
  tags: string[];
  privacy: 'public' | 'friends' | 'private';
}

// Generate a random number between min and max (inclusive)
const getRandomInt = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Get a random item from an array
const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

// Generate a random date within the last year
const getRandomDate = (): string => {
  const now = new Date();
  const pastDate = new Date(now.getTime() - getRandomInt(0, 365 * 24 * 60 * 60 * 1000));
  return pastDate.toISOString();
};

// Generate mock posts for development
export const generateMockPosts = (count: number): Post[] => {
  const posts: Post[] = [];
  
  const titles = [
    "Méditation en pleine nature",
    "Yoga au lever du soleil",
    "Retraite bien-être dans les montagnes",
    "Séance de respiration guidée",
    "Atelier de nutrition holistique",
    "Randonnée méditative",
    "Cours de cuisine végétale",
    "Séance de relaxation profonde",
    "Atelier sur la pleine conscience",
    "Exercices de détente corporelle"
  ];
  
  const descriptions = [
    "Découvrez comment la méditation en pleine nature peut transformer votre quotidien et vous apporter sérénité.",
    "Une séance de yoga matinale pour commencer la journée avec énergie et clarté mentale.",
    "Rejoignez-nous pour une retraite inoubliable au cœur des montagnes, loin du stress quotidien.",
    "Apprenez des techniques de respiration qui vous aideront à gérer le stress et l'anxiété.",
    "Découvrez les principes d'une alimentation saine qui nourrit à la fois le corps et l'esprit.",
    "Une expérience unique combinant randonnée et méditation pour se reconnecter à la nature.",
    "Apprenez à préparer des repas délicieux et nutritifs à base de plantes.",
    "Une séance guidée pour relâcher les tensions et atteindre un état de relaxation profonde.",
    "Développez votre pratique de la pleine conscience pour une vie plus équilibrée.",
    "Des exercices simples mais efficaces pour détendre votre corps et apaiser votre esprit."
  ];
  
  const tags = [
    "méditation", "yoga", "bien-être", "nature", "respiration", 
    "nutrition", "randonnée", "cuisine", "relaxation", "pleine conscience",
    "détente", "santé", "équilibre", "énergie", "spiritualité",
    "développement personnel", "zen", "harmonie", "vitalité", "sérénité"
  ];
  
  const thumbnails = [
    "https://images.unsplash.com/photo-1506126613408-eca07ce68773",
    "https://images.unsplash.com/photo-1545389336-cf090694435e",
    "https://images.unsplash.com/photo-1518609878373-06d740f60d8b",
    "https://images.unsplash.com/photo-1447452001602-7090c7ab2db3",
    "https://images.unsplash.com/photo-1600618528240-fb9fc964b853",
    "https://images.unsplash.com/photo-1508672019048-805c876b67e2",
    "https://images.unsplash.com/photo-1512621776951-a57141f2eefd",
    "https://images.unsplash.com/photo-1474418397713-2f1091139b9e",
    "https://images.unsplash.com/photo-1469474968028-56623f02e42e",
    "https://images.unsplash.com/photo-1604881991720-f91add269bed"
  ];
  
  const userNames = [
    "Sophie Martin", "Thomas Dubois", "Emma Bernard", "Lucas Petit",
    "Camille Leroy", "Hugo Moreau", "Léa Fournier", "Jules Girard",
    "Manon Dupont", "Nathan Rousseau"
  ];
  
  const statuses: ('published' | 'archived' | 'deleted')[] = ['published', 'archived', 'deleted'];
  const privacyOptions: ('public' | 'friends' | 'private')[] = ['public', 'friends', 'private'];
  
  for (let i = 0; i < count; i++) {
    // Distribute statuses: 60% published, 30% archived, 10% deleted
    let status: 'published' | 'archived' | 'deleted';
    const statusRandom = Math.random();
    if (statusRandom < 0.6) {
      status = 'published';
    } else if (statusRandom < 0.9) {
      status = 'archived';
    } else {
      status = 'deleted';
    }
    
    // Generate 1-4 random tags
    const postTags: string[] = [];
    const numTags = getRandomInt(1, 4);
    for (let j = 0; j < numTags; j++) {
      const randomTag = getRandomItem(tags);
      if (!postTags.includes(randomTag)) {
        postTags.push(randomTag);
      }
    }
    
    const userName = getRandomItem(userNames);
    
    posts.push({
      id: `post-${i + 1}`,
      title: getRandomItem(titles),
      description: getRandomItem(descriptions),
      thumbnailUrl: `${getRandomItem(thumbnails)}?w=640&h=360&fit=crop&auto=format&q=80`,
      videoUrl: i % 3 === 0 ? `https://example.com/video-${i + 1}.mp4` : undefined,
      createdAt: getRandomDate(),
      userId: `user-${getRandomInt(1, 10)}`,
      userName,
      userAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=random&color=fff&size=128`,
      likes: getRandomInt(0, 500),
      comments: getRandomInt(0, 100),
      views: getRandomInt(50, 5000),
      status,
      tags: postTags,
      privacy: getRandomItem(privacyOptions)
    });
  }
  
  // Sort by date (newest first)
  return posts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};
