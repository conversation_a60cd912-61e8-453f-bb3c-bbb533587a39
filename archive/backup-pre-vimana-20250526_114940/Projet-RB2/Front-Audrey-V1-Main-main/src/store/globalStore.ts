/**
 * Store Global Unifié - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * State management global pour l'application avec Zustand
 * Gère l'état utilisateur, les préférences et la synchronisation inter-modules.
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Types pour l'état global
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'user' | 'creator' | 'pro' | 'admin';
  preferences: UserPreferences;
  subscription?: Subscription;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    marketing: boolean;
  };
  privacy: {
    profileVisible: boolean;
    activityVisible: boolean;
  };
}

export interface Subscription {
  plan: 'free' | 'pro' | 'business' | 'enterprise';
  status: 'active' | 'inactive' | 'cancelled' | 'expired';
  expiresAt?: Date;
  features: string[];
}

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
}

export interface GlobalState {
  // État d'authentification
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;

  // Notifications
  notifications: Notification[];
  unreadCount: number;

  // État de l'application
  isOnline: boolean;
  lastSync: Date | null;
  
  // Cache des données
  cache: {
    retreats: any[];
    professionals: any[];
    content: any[];
  };

  // Actions
  setUser: (user: User | null) => void;
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  
  // Notifications
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  markNotificationAsRead: (id: string) => void;
  clearNotifications: () => void;
  
  // Cache
  updateCache: (key: keyof GlobalState['cache'], data: any[]) => void;
  clearCache: () => void;
  
  // Synchronisation
  syncData: () => Promise<void>;
  setOnlineStatus: (isOnline: boolean) => void;
}

// Store principal
export const useGlobalStore = create<GlobalState>()(
  persist(
    immer((set, get) => ({
      // État initial
      user: null,
      isAuthenticated: false,
      isLoading: false,
      notifications: [],
      unreadCount: 0,
      isOnline: navigator.onLine,
      lastSync: null,
      cache: {
        retreats: [],
        professionals: [],
        content: [],
      },

      // Actions d'authentification
      setUser: (user) => {
        set((state) => {
          state.user = user;
          state.isAuthenticated = !!user;
        });
      },

      updateUserPreferences: (preferences) => {
        set((state) => {
          if (state.user) {
            state.user.preferences = { ...state.user.preferences, ...preferences };
          }
        });
      },

      login: async (email, password) => {
        set((state) => {
          state.isLoading = true;
        });

        try {
          // Simulation d'appel API
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password }),
          });

          if (!response.ok) {
            throw new Error('Échec de la connexion');
          }

          const userData = await response.json();
          
          set((state) => {
            state.user = userData.user;
            state.isAuthenticated = true;
            state.isLoading = false;
          });

          // Synchroniser les données après connexion
          get().syncData();
        } catch (error) {
          set((state) => {
            state.isLoading = false;
          });
          
          get().addNotification({
            type: 'error',
            title: 'Erreur de connexion',
            message: 'Vérifiez vos identifiants et réessayez.',
            read: false,
          });
          
          throw error;
        }
      },

      logout: () => {
        set((state) => {
          state.user = null;
          state.isAuthenticated = false;
          state.notifications = [];
          state.unreadCount = 0;
        });
        
        // Nettoyer le cache
        get().clearCache();
        
        // Rediriger vers la page de connexion
        window.location.href = '/auth/login';
      },

      // Actions de notifications
      addNotification: (notification) => {
        set((state) => {
          const newNotification: Notification = {
            ...notification,
            id: Date.now().toString(),
            timestamp: new Date(),
          };
          
          state.notifications.unshift(newNotification);
          
          if (!newNotification.read) {
            state.unreadCount += 1;
          }
          
          // Limiter à 50 notifications
          if (state.notifications.length > 50) {
            state.notifications = state.notifications.slice(0, 50);
          }
        });
      },

      markNotificationAsRead: (id) => {
        set((state) => {
          const notification = state.notifications.find(n => n.id === id);
          if (notification && !notification.read) {
            notification.read = true;
            state.unreadCount = Math.max(0, state.unreadCount - 1);
          }
        });
      },

      clearNotifications: () => {
        set((state) => {
          state.notifications = [];
          state.unreadCount = 0;
        });
      },

      // Actions de cache
      updateCache: (key, data) => {
        set((state) => {
          state.cache[key] = data;
        });
      },

      clearCache: () => {
        set((state) => {
          state.cache = {
            retreats: [],
            professionals: [],
            content: [],
          };
        });
      },

      // Synchronisation
      syncData: async () => {
        const { user } = get();
        if (!user) return;

        try {
          // Synchroniser les données utilisateur
          const [retreatsRes, professionalsRes, contentRes] = await Promise.all([
            fetch('/api/retreats/favorites'),
            fetch('/api/professionals/followed'),
            fetch('/api/content/bookmarks'),
          ]);

          const [retreats, professionals, content] = await Promise.all([
            retreatsRes.json(),
            professionalsRes.json(),
            contentRes.json(),
          ]);

          set((state) => {
            state.cache.retreats = retreats;
            state.cache.professionals = professionals;
            state.cache.content = content;
            state.lastSync = new Date();
          });
        } catch (error) {
          console.error('Erreur de synchronisation:', error);
        }
      },

      setOnlineStatus: (isOnline) => {
        set((state) => {
          state.isOnline = isOnline;
        });

        // Synchroniser quand on revient en ligne
        if (isOnline) {
          get().syncData();
        }
      },
    })),
    {
      name: 'retreat-and-be-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        notifications: state.notifications,
        unreadCount: state.unreadCount,
        cache: state.cache,
        lastSync: state.lastSync,
      }),
    }
  )
);

// Hooks spécialisés
export const useAuth = () => {
  const { user, isAuthenticated, isLoading, login, logout, setUser } = useGlobalStore();
  return { user, isAuthenticated, isLoading, login, logout, setUser };
};

export const useNotifications = () => {
  const {
    notifications,
    unreadCount,
    addNotification,
    markNotificationAsRead,
    clearNotifications,
  } = useGlobalStore();
  
  return {
    notifications,
    unreadCount,
    addNotification,
    markNotificationAsRead,
    clearNotifications,
  };
};

export const useCache = () => {
  const { cache, updateCache, clearCache } = useGlobalStore();
  return { cache, updateCache, clearCache };
};

export const useSync = () => {
  const { isOnline, lastSync, syncData, setOnlineStatus } = useGlobalStore();
  return { isOnline, lastSync, syncData, setOnlineStatus };
};

// Initialisation du store
export const initializeStore = () => {
  // Écouter les changements de statut en ligne/hors ligne
  window.addEventListener('online', () => {
    useGlobalStore.getState().setOnlineStatus(true);
  });

  window.addEventListener('offline', () => {
    useGlobalStore.getState().setOnlineStatus(false);
  });

  // Synchronisation périodique (toutes les 5 minutes)
  setInterval(() => {
    const { isOnline, isAuthenticated } = useGlobalStore.getState();
    if (isOnline && isAuthenticated) {
      useGlobalStore.getState().syncData();
    }
  }, 5 * 60 * 1000);
};
