/**
 * Module Retraites Unifié - Retreat And Be
 * Date de création: 25 mai 2025
 *
 * Module complet de gestion des retraites utilisant le nouveau design system
 * avec recherche, filtrage, détails et réservation.
 */

import React, { useState, useEffect } from 'react';
import { Routes, Route, useNavigate, useSearchParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Container,
  Grid,
  Stack,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  RetreatCard,
  Button,
  Input,
  SearchInput,
  Select,
  Badge,
  Spinner,
  Pagination
} from '../../components/ui/design-system';
import { useToast } from '../../components/ui/design-system/Toast';

interface Retreat {
  id: string;
  title: string;
  description: string;
  image: string;
  price: number;
  duration: string;
  location: string;
  rating: number;
  reviewCount: number;
  category: string;
  instructor: {
    name: string;
    avatar: string;
    bio: string;
  };
  amenities: string[];
  availableDates: string[];
  maxParticipants: number;
  currentParticipants: number;
  difficulty: 'Débutant' | 'Intermédiaire' | 'Avancé';
  tags: string[];
}

interface SearchFilters {
  query: string;
  category: string;
  location: string;
  priceMin: string;
  priceMax: string;
  duration: string;
  difficulty: string;
  sortBy: string;
}

const RetreatsModule: React.FC = () => {
  return (
    <Container className="py-8">
      <Routes>
        <Route path="/" element={<RetreatsList />} />
        <Route path="/:id" element={<RetreatDetail />} />
      </Routes>
    </Container>
  );
};

const RetreatsList: React.FC = () => {
  const [retreats, setRetreats] = useState<Retreat[]>([]);
  const [filteredRetreats, setFilteredRetreats] = useState<Retreat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const [filters, setFilters] = useState<SearchFilters>({
    query: searchParams.get('q') || '',
    category: searchParams.get('category') || '',
    location: searchParams.get('location') || '',
    priceMin: searchParams.get('priceMin') || '',
    priceMax: searchParams.get('priceMax') || '',
    duration: searchParams.get('duration') || '',
    difficulty: searchParams.get('difficulty') || '',
    sortBy: searchParams.get('sortBy') || 'popularity',
  });

  const itemsPerPage = 12;

  useEffect(() => {
    loadRetreats();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [retreats, filters]);

  useEffect(() => {
    updateSearchParams();
  }, [filters]);

  const loadRetreats = async () => {
    try {
      setIsLoading(true);

      // Simuler le chargement des données
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Données mockées
      const mockRetreats: Retreat[] = [
        {
          id: '1',
          title: 'Retraite Yoga en Provence',
          description: 'Une retraite de yoga transformatrice dans les lavandes de Provence. Reconnectez-vous avec votre essence profonde.',
          image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500',
          price: 450,
          duration: '3 jours',
          location: 'Provence, France',
          rating: 4.8,
          reviewCount: 127,
          category: 'Yoga',
          instructor: {
            name: 'Sophie Laurent',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
            bio: 'Professeure de yoga certifiée avec 15 ans d\'expérience',
          },
          amenities: ['Repas bio', 'Hébergement', 'Matériel fourni', 'Spa'],
          availableDates: ['2025-06-15', '2025-07-20', '2025-08-10'],
          maxParticipants: 20,
          currentParticipants: 15,
          difficulty: 'Débutant',
          tags: ['Hatha Yoga', 'Méditation', 'Nature'],
        },
        {
          id: '2',
          title: 'Méditation Pleine Conscience',
          description: 'Approfondissez votre pratique de la méditation dans un cadre paisible et inspirant.',
          image: 'https://images.unsplash.com/photo-1545389336-cf090694435e?w=500',
          price: 320,
          duration: '5 jours',
          location: 'Annecy, France',
          rating: 4.9,
          reviewCount: 89,
          category: 'Méditation',
          instructor: {
            name: 'Marc Dubois',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
            bio: 'Maître de méditation et auteur de plusieurs ouvrages',
          },
          amenities: ['Repas végétariens', 'Hébergement', 'Silence noble'],
          availableDates: ['2025-07-02', '2025-08-15', '2025-09-05'],
          maxParticipants: 15,
          currentParticipants: 8,
          difficulty: 'Intermédiaire',
          tags: ['Mindfulness', 'Vipassana', 'Silence'],
        },
        {
          id: '3',
          title: 'Détox Numérique en Bretagne',
          description: 'Déconnectez-vous du monde numérique et reconnectez-vous avec la nature bretonne.',
          image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=500',
          price: 280,
          duration: '7 jours',
          location: 'Bretagne, France',
          rating: 4.6,
          reviewCount: 156,
          category: 'Détox',
          instructor: {
            name: 'Claire Martin',
            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',
            bio: 'Coach en bien-être et spécialiste de la détox digitale',
          },
          amenities: ['Randonnées', 'Ateliers créatifs', 'Repas locaux'],
          availableDates: ['2025-07-20', '2025-08-25', '2025-09-15'],
          maxParticipants: 25,
          currentParticipants: 12,
          difficulty: 'Débutant',
          tags: ['Nature', 'Créativité', 'Simplicité'],
        },
        // Ajouter plus de retraites...
      ];

      setRetreats(mockRetreats);
    } catch (error) {
      console.error('Erreur lors du chargement des retraites:', error);
      toast.error('Erreur lors du chargement des retraites');
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...retreats];

    // Recherche textuelle
    if (filters.query) {
      const query = filters.query.toLowerCase();
      filtered = filtered.filter(retreat =>
        retreat.title.toLowerCase().includes(query) ||
        retreat.description.toLowerCase().includes(query) ||
        retreat.location.toLowerCase().includes(query) ||
        retreat.category.toLowerCase().includes(query) ||
        retreat.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filtrage par catégorie
    if (filters.category) {
      filtered = filtered.filter(retreat => retreat.category === filters.category);
    }

    // Filtrage par localisation
    if (filters.location) {
      filtered = filtered.filter(retreat =>
        retreat.location.toLowerCase().includes(filters.location.toLowerCase())
      );
    }

    // Filtrage par prix
    if (filters.priceMin) {
      filtered = filtered.filter(retreat => retreat.price >= parseInt(filters.priceMin));
    }
    if (filters.priceMax) {
      filtered = filtered.filter(retreat => retreat.price <= parseInt(filters.priceMax));
    }

    // Filtrage par durée
    if (filters.duration) {
      filtered = filtered.filter(retreat => retreat.duration.includes(filters.duration));
    }

    // Filtrage par difficulté
    if (filters.difficulty) {
      filtered = filtered.filter(retreat => retreat.difficulty === filters.difficulty);
    }

    // Tri
    switch (filters.sortBy) {
      case 'price-asc':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'duration':
        filtered.sort((a, b) => parseInt(a.duration) - parseInt(b.duration));
        break;
      default: // popularity
        filtered.sort((a, b) => b.reviewCount - a.reviewCount);
    }

    setFilteredRetreats(filtered);
    setCurrentPage(1);
  };

  const updateSearchParams = () => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.set(key, value);
    });
    setSearchParams(params);
  };

  const handleFilterChange = (key: keyof SearchFilters) => (value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      category: '',
      location: '',
      priceMin: '',
      priceMax: '',
      duration: '',
      difficulty: '',
      sortBy: 'popularity',
    });
  };

  const handleRetreatClick = (retreatId: string) => {
    navigate(`/app/retreats/${retreatId}`);
  };

  const handleBookRetreat = (retreatId: string) => {
    // Logique de réservation
    toast.success('Redirection vers la réservation...');
    navigate(`/app/retreats/${retreatId}?book=true`);
  };

  const handleFavoriteRetreat = (retreatId: string) => {
    // Logique de favori
    toast.success('Ajouté aux favoris !');
  };

  // Pagination
  const totalPages = Math.ceil(filteredRetreats.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedRetreats = filteredRetreats.slice(startIndex, startIndex + itemsPerPage);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* En-tête */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-neutral-900 mb-2">
          Découvrez nos retraites
        </h1>
        <p className="text-neutral-600">
          {filteredRetreats.length} retraite{filteredRetreats.length > 1 ? 's' : ''} disponible{filteredRetreats.length > 1 ? 's' : ''}
        </p>
      </div>

      {/* Barre de recherche et filtres */}
      <Card className="mb-6">
        <CardContent className="py-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Rechercher une retraite..."
                value={filters.query}
                onChange={handleFilterChange('query')}
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                leftIcon="🔍"
              >
                Filtres
              </Button>
              <Select
                value={filters.sortBy}
                onChange={handleFilterChange('sortBy')}
                options={[
                  { value: 'popularity', label: 'Popularité' },
                  { value: 'price-asc', label: 'Prix croissant' },
                  { value: 'price-desc', label: 'Prix décroissant' },
                  { value: 'rating', label: 'Mieux notées' },
                  { value: 'duration', label: 'Durée' },
                ]}
              />
            </div>
          </div>

          {/* Filtres avancés */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 pt-4 border-t border-neutral-200"
              >
                <Grid cols={4} gap="md">
                  <Select
                    label="Catégorie"
                    value={filters.category}
                    onChange={handleFilterChange('category')}
                    options={[
                      { value: '', label: 'Toutes' },
                      { value: 'Yoga', label: 'Yoga' },
                      { value: 'Méditation', label: 'Méditation' },
                      { value: 'Détox', label: 'Détox' },
                      { value: 'Wellness', label: 'Wellness' },
                    ]}
                  />
                  <Input
                    label="Localisation"
                    placeholder="Ville, région..."
                    value={filters.location}
                    onChange={handleFilterChange('location')}
                  />
                  <div className="flex gap-2">
                    <Input
                      label="Prix min"
                      type="number"
                      placeholder="0"
                      value={filters.priceMin}
                      onChange={handleFilterChange('priceMin')}
                    />
                    <Input
                      label="Prix max"
                      type="number"
                      placeholder="1000"
                      value={filters.priceMax}
                      onChange={handleFilterChange('priceMax')}
                    />
                  </div>
                  <Select
                    label="Difficulté"
                    value={filters.difficulty}
                    onChange={handleFilterChange('difficulty')}
                    options={[
                      { value: '', label: 'Toutes' },
                      { value: 'Débutant', label: 'Débutant' },
                      { value: 'Intermédiaire', label: 'Intermédiaire' },
                      { value: 'Avancé', label: 'Avancé' },
                    ]}
                  />
                </Grid>
                <div className="mt-4 flex justify-end">
                  <Button variant="ghost" onClick={clearFilters}>
                    Effacer les filtres
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Liste des retraites */}
      {paginatedRetreats.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold mb-2">Aucune retraite trouvée</h3>
            <p className="text-neutral-600 mb-4">
              Essayez de modifier vos critères de recherche
            </p>
            <Button variant="outline" onClick={clearFilters}>
              Effacer les filtres
            </Button>
          </CardContent>
        </Card>
      ) : (
        <>
          <Grid cols={3} gap="lg" className="mb-8">
            {paginatedRetreats.map((retreat) => (
              <motion.div
                key={retreat.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <RetreatCard
                  retreat={retreat}
                  onClick={() => handleRetreatClick(retreat.id)}
                  onBook={() => handleBookRetreat(retreat.id)}
                  onFavorite={() => handleFavoriteRetreat(retreat.id)}
                />
              </motion.div>
            ))}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </div>
          )}
        </>
      )}
    </motion.div>
  );
};

const RetreatDetail: React.FC = () => {
  // Implémentation du détail de retraite
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardContent className="text-center py-12">
          <h2 className="text-2xl font-semibold mb-4">Détail de la retraite</h2>
          <p className="text-neutral-600 mb-6">
            Cette page sera implémentée dans la prochaine itération.
          </p>
          <Button
            variant="primary"
            onClick={() => window.history.back()}
          >
            Retour à la liste
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default RetreatsModule;
