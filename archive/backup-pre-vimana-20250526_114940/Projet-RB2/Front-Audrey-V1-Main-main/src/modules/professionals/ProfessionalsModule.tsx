/**
 * Module Professionnels Unifié - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Module complet de gestion des professionnels utilisant le nouveau design system
 * avec recherche, contact et réservation de sessions.
 */

import React, { useState, useEffect } from 'react';
import { Routes, Route, useNavigate, useSearchParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Container,
  Grid,
  Stack,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  ProfessionalCard,
  Button,
  Input,
  SearchInput,
  Select,
  Badge,
  Spinner,
  Pagination,
  Modal,
  TextArea,
  Avatar
} from '../../components/ui/design-system';
import { useToast } from '../../components/ui/design-system/Toast';

interface Professional {
  id: string;
  name: string;
  title: string;
  avatar: string;
  specialties: string[];
  rating: number;
  reviewCount: number;
  price: number;
  available: boolean;
  bio: string;
  experience: string;
  certifications: string[];
  languages: string[];
  location: string;
  responseTime: string;
  sessionTypes: string[];
}

interface SearchFilters {
  query: string;
  specialty: string;
  location: string;
  priceMin: string;
  priceMax: string;
  available: boolean;
  sortBy: string;
}

const ProfessionalsModule: React.FC = () => {
  return (
    <Container className="py-8">
      <Routes>
        <Route path="/" element={<ProfessionalsList />} />
        <Route path="/:id" element={<ProfessionalDetail />} />
      </Routes>
    </Container>
  );
};

const ProfessionalsList: React.FC = () => {
  const [professionals, setProfessionals] = useState<Professional[]>([]);
  const [filteredProfessionals, setFilteredProfessionals] = useState<Professional[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [contactModal, setContactModal] = useState<{ isOpen: boolean; professional: Professional | null }>({
    isOpen: false,
    professional: null,
  });
  const { toast } = useToast();
  const navigate = useNavigate();

  const [filters, setFilters] = useState<SearchFilters>({
    query: searchParams.get('q') || '',
    specialty: searchParams.get('specialty') || '',
    location: searchParams.get('location') || '',
    priceMin: searchParams.get('priceMin') || '',
    priceMax: searchParams.get('priceMax') || '',
    available: searchParams.get('available') === 'true',
    sortBy: searchParams.get('sortBy') || 'rating',
  });

  const itemsPerPage = 12;

  useEffect(() => {
    loadProfessionals();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [professionals, filters]);

  const loadProfessionals = async () => {
    try {
      setIsLoading(true);
      
      // Simuler le chargement des données
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Données mockées
      const mockProfessionals: Professional[] = [
        {
          id: '1',
          name: 'Dr. Marie Dubois',
          title: 'Coach en Nutrition Holistique',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
          specialties: ['Nutrition', 'Détox', 'Bien-être digestif'],
          rating: 4.9,
          reviewCount: 127,
          price: 85,
          available: true,
          bio: 'Docteure en nutrition avec 12 ans d\'expérience en accompagnement holistique.',
          experience: '12 ans',
          certifications: ['Doctorat en Nutrition', 'Certification Ayurveda', 'Formation Micronutrition'],
          languages: ['Français', 'Anglais', 'Espagnol'],
          location: 'Paris, France',
          responseTime: '< 2h',
          sessionTypes: ['Consultation individuelle', 'Suivi personnalisé', 'Ateliers groupe'],
        },
        {
          id: '2',
          name: 'Jean Martin',
          title: 'Professeur de Yoga Certifié',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
          specialties: ['Hatha Yoga', 'Vinyasa', 'Méditation'],
          rating: 4.8,
          reviewCount: 89,
          price: 65,
          available: true,
          bio: 'Professeur de yoga passionné, formé en Inde et spécialisé dans l\'alignement.',
          experience: '8 ans',
          certifications: ['RYT 500h', 'Formation Inde', 'Spécialisation Anatomie'],
          languages: ['Français', 'Anglais'],
          location: 'Lyon, France',
          responseTime: '< 4h',
          sessionTypes: ['Cours particuliers', 'Cours collectifs', 'Stages weekend'],
        },
        {
          id: '3',
          name: 'Sophie Laurent',
          title: 'Thérapeute en Mindfulness',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
          specialties: ['Mindfulness', 'Gestion du stress', 'Anxiété'],
          rating: 4.7,
          reviewCount: 156,
          price: 75,
          available: false,
          bio: 'Thérapeute spécialisée en pleine conscience et gestion du stress.',
          experience: '10 ans',
          certifications: ['Master Psychologie', 'MBSR Instructor', 'Formation MBCT'],
          languages: ['Français', 'Anglais'],
          location: 'Bordeaux, France',
          responseTime: '< 6h',
          sessionTypes: ['Thérapie individuelle', 'Groupes MBSR', 'Ateliers entreprise'],
        },
      ];

      setProfessionals(mockProfessionals);
    } catch (error) {
      console.error('Erreur lors du chargement des professionnels:', error);
      toast.error('Erreur lors du chargement des professionnels');
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...professionals];

    // Recherche textuelle
    if (filters.query) {
      const query = filters.query.toLowerCase();
      filtered = filtered.filter(professional =>
        professional.name.toLowerCase().includes(query) ||
        professional.title.toLowerCase().includes(query) ||
        professional.specialties.some(specialty => specialty.toLowerCase().includes(query)) ||
        professional.location.toLowerCase().includes(query)
      );
    }

    // Filtrage par spécialité
    if (filters.specialty) {
      filtered = filtered.filter(professional =>
        professional.specialties.some(specialty => 
          specialty.toLowerCase().includes(filters.specialty.toLowerCase())
        )
      );
    }

    // Filtrage par localisation
    if (filters.location) {
      filtered = filtered.filter(professional =>
        professional.location.toLowerCase().includes(filters.location.toLowerCase())
      );
    }

    // Filtrage par prix
    if (filters.priceMin) {
      filtered = filtered.filter(professional => professional.price >= parseInt(filters.priceMin));
    }
    if (filters.priceMax) {
      filtered = filtered.filter(professional => professional.price <= parseInt(filters.priceMax));
    }

    // Filtrage par disponibilité
    if (filters.available) {
      filtered = filtered.filter(professional => professional.available);
    }

    // Tri
    switch (filters.sortBy) {
      case 'price-asc':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'reviews':
        filtered.sort((a, b) => b.reviewCount - a.reviewCount);
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      default:
        filtered.sort((a, b) => b.rating - a.rating);
    }

    setFilteredProfessionals(filtered);
    setCurrentPage(1);
  };

  const handleFilterChange = (key: keyof SearchFilters) => (value: string | boolean) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      specialty: '',
      location: '',
      priceMin: '',
      priceMax: '',
      available: false,
      sortBy: 'rating',
    });
  };

  const handleProfessionalClick = (professionalId: string) => {
    navigate(`/app/professionals/${professionalId}`);
  };

  const handleContactProfessional = (professional: Professional) => {
    if (!professional.available) {
      toast.warning('Ce professionnel n\'est pas disponible actuellement');
      return;
    }
    setContactModal({ isOpen: true, professional });
  };

  const handleViewProfile = (professionalId: string) => {
    navigate(`/app/professionals/${professionalId}`);
  };

  // Pagination
  const totalPages = Math.ceil(filteredProfessionals.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedProfessionals = filteredProfessionals.slice(startIndex, startIndex + itemsPerPage);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* En-tête */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-neutral-900 mb-2">
          Nos professionnels du bien-être
        </h1>
        <p className="text-neutral-600">
          {filteredProfessionals.length} professionnel{filteredProfessionals.length > 1 ? 's' : ''} disponible{filteredProfessionals.length > 1 ? 's' : ''}
        </p>
      </div>

      {/* Barre de recherche et filtres */}
      <Card className="mb-6">
        <CardContent className="py-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Rechercher un professionnel..."
                value={filters.query}
                onChange={handleFilterChange('query')}
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                leftIcon="🔍"
              >
                Filtres
              </Button>
              <Select
                value={filters.sortBy}
                onChange={handleFilterChange('sortBy')}
                options={[
                  { value: 'rating', label: 'Mieux notés' },
                  { value: 'reviews', label: 'Plus d\'avis' },
                  { value: 'price-asc', label: 'Prix croissant' },
                  { value: 'price-desc', label: 'Prix décroissant' },
                  { value: 'name', label: 'Nom A-Z' },
                ]}
              />
            </div>
          </div>

          {/* Filtres avancés */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 pt-4 border-t border-neutral-200"
              >
                <Grid cols={4} gap="md">
                  <Select
                    label="Spécialité"
                    value={filters.specialty}
                    onChange={handleFilterChange('specialty')}
                    options={[
                      { value: '', label: 'Toutes' },
                      { value: 'Yoga', label: 'Yoga' },
                      { value: 'Nutrition', label: 'Nutrition' },
                      { value: 'Mindfulness', label: 'Mindfulness' },
                      { value: 'Méditation', label: 'Méditation' },
                      { value: 'Coaching', label: 'Coaching' },
                    ]}
                  />
                  <Input
                    label="Localisation"
                    placeholder="Ville, région..."
                    value={filters.location}
                    onChange={handleFilterChange('location')}
                  />
                  <div className="flex gap-2">
                    <Input
                      label="Prix min"
                      type="number"
                      placeholder="0"
                      value={filters.priceMin}
                      onChange={handleFilterChange('priceMin')}
                    />
                    <Input
                      label="Prix max"
                      type="number"
                      placeholder="200"
                      value={filters.priceMax}
                      onChange={handleFilterChange('priceMax')}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="available"
                      checked={filters.available}
                      onChange={(e) => handleFilterChange('available')(e.target.checked)}
                    />
                    <label htmlFor="available" className="text-sm">
                      Disponibles uniquement
                    </label>
                  </div>
                </Grid>
                <div className="mt-4 flex justify-end">
                  <Button variant="ghost" onClick={clearFilters}>
                    Effacer les filtres
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Liste des professionnels */}
      {paginatedProfessionals.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-xl font-semibold mb-2">Aucun professionnel trouvé</h3>
            <p className="text-neutral-600 mb-4">
              Essayez de modifier vos critères de recherche
            </p>
            <Button variant="outline" onClick={clearFilters}>
              Effacer les filtres
            </Button>
          </CardContent>
        </Card>
      ) : (
        <>
          <Grid cols={3} gap="lg" className="mb-8">
            {paginatedProfessionals.map((professional) => (
              <motion.div
                key={professional.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <ProfessionalCard
                  professional={professional}
                  onClick={() => handleProfessionalClick(professional.id)}
                  onContact={() => handleContactProfessional(professional)}
                  onViewProfile={() => handleViewProfile(professional.id)}
                />
              </motion.div>
            ))}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </div>
          )}
        </>
      )}

      {/* Modal de contact */}
      <ContactModal
        isOpen={contactModal.isOpen}
        professional={contactModal.professional}
        onClose={() => setContactModal({ isOpen: false, professional: null })}
      />
    </motion.div>
  );
};

const ContactModal: React.FC<{
  isOpen: boolean;
  professional: Professional | null;
  onClose: () => void;
}> = ({ isOpen, professional, onClose }) => {
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!subject || !message) {
      toast.error('Veuillez remplir tous les champs');
      return;
    }

    setIsSubmitting(true);

    try {
      // Simuler l'envoi du message
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Message envoyé avec succès !');
      onClose();
      setSubject('');
      setMessage('');
    } catch (error) {
      toast.error('Erreur lors de l\'envoi du message');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!professional) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Contacter le professionnel">
      <div className="mb-4">
        <div className="flex items-center space-x-3">
          <Avatar
            src={professional.avatar}
            alt={professional.name}
            size="md"
          />
          <div>
            <h3 className="font-semibold">{professional.name}</h3>
            <p className="text-sm text-neutral-600">{professional.title}</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <Stack spacing="md">
          <Select
            label="Sujet"
            value={subject}
            onChange={setSubject}
            options={[
              { value: '', label: 'Sélectionnez un sujet' },
              { value: 'Demande de rendez-vous', label: 'Demande de rendez-vous' },
              { value: 'Question générale', label: 'Question générale' },
              { value: 'Tarifs et modalités', label: 'Tarifs et modalités' },
              { value: 'Autre', label: 'Autre' },
            ]}
            required
          />

          <TextArea
            label="Message"
            placeholder="Décrivez votre demande..."
            value={message}
            onChange={setMessage}
            rows={4}
            required
          />

          <div className="flex gap-2 justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              Envoyer le message
            </Button>
          </div>
        </Stack>
      </form>
    </Modal>
  );
};

const ProfessionalDetail: React.FC = () => {
  // Implémentation du détail professionnel
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardContent className="text-center py-12">
          <h2 className="text-2xl font-semibold mb-4">Profil du professionnel</h2>
          <p className="text-neutral-600 mb-6">
            Cette page sera implémentée dans la prochaine itération.
          </p>
          <Button
            variant="primary"
            onClick={() => window.history.back()}
          >
            Retour à la liste
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ProfessionalsModule;
