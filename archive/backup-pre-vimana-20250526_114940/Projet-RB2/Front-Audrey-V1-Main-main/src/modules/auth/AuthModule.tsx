/**
 * Module d'Authentification Unifié - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Module complet d'authentification utilisant le nouveau design system
 * avec toutes les fonctionnalités de connexion, inscription et récupération.
 */

import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../store/globalStore';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent, 
  CardFooter,
  Button,
  Input,
  Alert,
  Spinner,
  Container,
  Stack,
  Divider
} from '../../components/ui/design-system';
import { useToast } from '../../components/ui/design-system/Toast';

interface AuthFormData {
  email: string;
  password: string;
  confirmPassword?: string;
  firstName?: string;
  lastName?: string;
  acceptTerms?: boolean;
}

interface AuthFormErrors {
  email?: string;
  password?: string;
  confirmPassword?: string;
  firstName?: string;
  lastName?: string;
  acceptTerms?: string;
  general?: string;
}

const AuthModule: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, isAuthenticated, isLoading } = useAuth();

  // Rediriger si déjà connecté
  useEffect(() => {
    if (isAuthenticated && user) {
      const from = (location.state as any)?.from?.pathname || '/app/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, user, navigate, location]);

  if (isLoading) {
    return (
      <Container className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </Container>
    );
  }

  return (
    <Container className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 py-12">
      <div className="max-w-md mx-auto">
        <AnimatePresence mode="wait">
          <Routes>
            <Route path="/login" element={<LoginForm />} />
            <Route path="/register" element={<RegisterForm />} />
            <Route path="/forgot-password" element={<ForgotPasswordForm />} />
            <Route path="/reset-password/:token" element={<ResetPasswordForm />} />
            <Route path="*" element={<Navigate to="/auth/login" replace />} />
          </Routes>
        </AnimatePresence>
      </div>
    </Container>
  );
};

const LoginForm: React.FC = () => {
  const [formData, setFormData] = useState<AuthFormData>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<AuthFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { login } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleInputChange = (field: keyof AuthFormData) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Effacer l'erreur du champ modifié
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: AuthFormErrors = {};

    if (!formData.email) {
      newErrors.email = 'Email requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.password) {
      newErrors.password = 'Mot de passe requis';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Le mot de passe doit contenir au moins 6 caractères';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    setErrors({});

    try {
      await login(formData.email, formData.password);
      toast.success('Connexion réussie !');
      navigate('/app/dashboard');
    } catch (error: any) {
      const errorMessage = error.message || 'Erreur de connexion';
      setErrors({ general: errorMessage });
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="shadow-xl">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-primary-900">
            Connexion
          </CardTitle>
          <p className="text-neutral-600 mt-2">
            Connectez-vous à votre compte Retreat & Be
          </p>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit}>
            <Stack spacing="md">
              {errors.general && (
                <Alert variant="error">
                  {errors.general}
                </Alert>
              )}

              <Input
                type="email"
                label="Email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleInputChange('email')}
                error={errors.email}
                required
                autoComplete="email"
                leftIcon="📧"
              />

              <Input
                type="password"
                label="Mot de passe"
                placeholder="••••••••"
                value={formData.password}
                onChange={handleInputChange('password')}
                error={errors.password}
                required
                autoComplete="current-password"
                leftIcon="🔒"
              />

              <div className="text-right">
                <Button
                  variant="link"
                  size="sm"
                  onClick={() => navigate('/auth/forgot-password')}
                  type="button"
                >
                  Mot de passe oublié ?
                </Button>
              </div>

              <Button
                type="submit"
                variant="primary"
                size="lg"
                fullWidth
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Connexion...' : 'Se connecter'}
              </Button>
            </Stack>
          </form>
        </CardContent>

        <CardFooter>
          <Divider />
          <div className="text-center mt-4">
            <span className="text-neutral-600">Pas encore de compte ? </span>
            <Button
              variant="link"
              onClick={() => navigate('/auth/register')}
              className="p-0"
            >
              Créer un compte
            </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

const RegisterForm: React.FC = () => {
  const [formData, setFormData] = useState<AuthFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    acceptTerms: false,
  });
  const [errors, setErrors] = useState<AuthFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { register } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleInputChange = (field: keyof AuthFormData) => (value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: AuthFormErrors = {};

    if (!formData.firstName) {
      newErrors.firstName = 'Prénom requis';
    }

    if (!formData.lastName) {
      newErrors.lastName = 'Nom requis';
    }

    if (!formData.email) {
      newErrors.email = 'Email requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.password) {
      newErrors.password = 'Mot de passe requis';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }

    if (!formData.acceptTerms) {
      newErrors.acceptTerms = 'Vous devez accepter les conditions d\'utilisation';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    setErrors({});

    try {
      await register({
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName!,
        lastName: formData.lastName!,
      });
      toast.success('Compte créé avec succès !');
      navigate('/app/dashboard');
    } catch (error: any) {
      const errorMessage = error.message || 'Erreur lors de la création du compte';
      setErrors({ general: errorMessage });
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="shadow-xl">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-primary-900">
            Créer un compte
          </CardTitle>
          <p className="text-neutral-600 mt-2">
            Rejoignez la communauté Retreat & Be
          </p>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit}>
            <Stack spacing="md">
              {errors.general && (
                <Alert variant="error">
                  {errors.general}
                </Alert>
              )}

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="Prénom"
                  placeholder="Jean"
                  value={formData.firstName || ''}
                  onChange={handleInputChange('firstName')}
                  error={errors.firstName}
                  required
                  autoComplete="given-name"
                />

                <Input
                  label="Nom"
                  placeholder="Dupont"
                  value={formData.lastName || ''}
                  onChange={handleInputChange('lastName')}
                  error={errors.lastName}
                  required
                  autoComplete="family-name"
                />
              </div>

              <Input
                type="email"
                label="Email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleInputChange('email')}
                error={errors.email}
                required
                autoComplete="email"
                leftIcon="📧"
              />

              <Input
                type="password"
                label="Mot de passe"
                placeholder="••••••••"
                value={formData.password}
                onChange={handleInputChange('password')}
                error={errors.password}
                required
                autoComplete="new-password"
                leftIcon="🔒"
                helperText="Au moins 8 caractères"
              />

              <Input
                type="password"
                label="Confirmer le mot de passe"
                placeholder="••••••••"
                value={formData.confirmPassword || ''}
                onChange={handleInputChange('confirmPassword')}
                error={errors.confirmPassword}
                required
                autoComplete="new-password"
                leftIcon="🔒"
              />

              <div className="flex items-start space-x-2">
                <input
                  type="checkbox"
                  id="acceptTerms"
                  checked={formData.acceptTerms || false}
                  onChange={(e) => handleInputChange('acceptTerms')(e.target.checked)}
                  className="mt-1"
                />
                <label htmlFor="acceptTerms" className="text-sm text-neutral-600">
                  J'accepte les{' '}
                  <Button variant="link" className="p-0 h-auto text-sm">
                    conditions d'utilisation
                  </Button>
                  {' '}et la{' '}
                  <Button variant="link" className="p-0 h-auto text-sm">
                    politique de confidentialité
                  </Button>
                </label>
              </div>
              {errors.acceptTerms && (
                <p className="text-error-600 text-sm">{errors.acceptTerms}</p>
              )}

              <Button
                type="submit"
                variant="primary"
                size="lg"
                fullWidth
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Création...' : 'Créer mon compte'}
              </Button>
            </Stack>
          </form>
        </CardContent>

        <CardFooter>
          <Divider />
          <div className="text-center mt-4">
            <span className="text-neutral-600">Déjà un compte ? </span>
            <Button
              variant="link"
              onClick={() => navigate('/auth/login')}
              className="p-0"
            >
              Se connecter
            </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

const ForgotPasswordForm: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setError('Email requis');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Simuler l'envoi d'email de récupération
      await new Promise(resolve => setTimeout(resolve, 1000));
      setIsSubmitted(true);
      toast.success('Email de récupération envoyé !');
    } catch (error: any) {
      setError('Erreur lors de l\'envoi de l\'email');
      toast.error('Erreur lors de l\'envoi de l\'email');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="shadow-xl text-center">
          <CardContent className="py-8">
            <div className="text-6xl mb-4">📧</div>
            <h2 className="text-xl font-semibold mb-2">Email envoyé !</h2>
            <p className="text-neutral-600 mb-6">
              Vérifiez votre boîte email pour réinitialiser votre mot de passe.
            </p>
            <Button
              variant="outline"
              onClick={() => navigate('/auth/login')}
            >
              Retour à la connexion
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="shadow-xl">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-primary-900">
            Mot de passe oublié
          </CardTitle>
          <p className="text-neutral-600 mt-2">
            Entrez votre email pour recevoir un lien de réinitialisation
          </p>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit}>
            <Stack spacing="md">
              {error && (
                <Alert variant="error">
                  {error}
                </Alert>
              )}

              <Input
                type="email"
                label="Email"
                placeholder="<EMAIL>"
                value={email}
                onChange={setEmail}
                required
                autoComplete="email"
                leftIcon="📧"
              />

              <Button
                type="submit"
                variant="primary"
                size="lg"
                fullWidth
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Envoi...' : 'Envoyer le lien'}
              </Button>
            </Stack>
          </form>
        </CardContent>

        <CardFooter>
          <div className="text-center">
            <Button
              variant="link"
              onClick={() => navigate('/auth/login')}
            >
              ← Retour à la connexion
            </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

const ResetPasswordForm: React.FC = () => {
  // Implémentation similaire pour la réinitialisation
  const navigate = useNavigate();
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="shadow-xl">
        <CardContent className="text-center py-8">
          <h2 className="text-xl font-semibold mb-4">Réinitialisation du mot de passe</h2>
          <p className="text-neutral-600 mb-6">
            Cette fonctionnalité sera implémentée prochainement.
          </p>
          <Button
            variant="primary"
            onClick={() => navigate('/auth/login')}
          >
            Retour à la connexion
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AuthModule;
