/**
 * CSP Report Handler
 *
 * Ce fichier contient la logique pour recevoir et traiter les rapports de violation
 * de la Content Security Policy (CSP).
 *
 * Note: Dans un environnement de production, ce type de rapports devrait idéalement
 * être géré côté serveur. Cette implémentation est conçue pour les environnements
 * de développement ou les déploiements statiques.
 */

/**
 * Interface pour le rapport de violation CSP
 */
interface CSPViolationReport {
  'csp-report': {
    'document-uri'?: string;
    referrer?: string;
    'violated-directive'?: string;
    'effective-directive'?: string;
    'original-policy'?: string;
    'blocked-uri'?: string;
    'source-file'?: string;
    'line-number'?: number;
    'column-number'?: number;
    'status-code'?: number;
  };
}

/**
 * Configuration de l'endpoint de rapport CSP
 */
interface CSPReportConfig {
  endpoint: string;
  // Activer/désactiver la journalisation en développement
  logInDevelopment: boolean;
  // URL du service backend pour les rapports en production
  productionEndpoint?: string;
}

// Configuration par défaut
const defaultConfig: CSPReportConfig = {
  endpoint: '/csp-report-endpoint',
  logInDevelopment: true,
  productionEndpoint: undefined, // À définir en production
};

let config = { ...defaultConfig };

/**
 * Configurer les options de rapport CSP
 * @param userConfig - Options de configuration personnalisées
 */
export const configureCSPReporting = (userConfig: Partial<CSPReportConfig>): void => {
  config = { ...defaultConfig, ...userConfig };
};

/**
 * Traite les rapports de violation CSP
 * @param report - Le rapport de violation CSP
 */
export const handleCSPViolation = (report: CSPViolationReport): void => {
  // En environnement de développement, on affiche les violations dans la console
  if (process.env.NODE_ENV === 'development' && config.logInDevelopment) {
    console.group('CSP Violation');
    console.warn('CSP Violation:', report);
    console.groupEnd();
  }

  // En production, on envoie les rapports à un service d'analyse
  if (process.env.NODE_ENV === 'production' && config.productionEndpoint) {
    // Envoyer à l'endpoint de rapport du backend
    fetch(config.productionEndpoint, {
      method: 'POST',
      body: JSON.stringify(report),
      headers: {
        'Content-Type': 'application/json',
      },
      // Ignorer les erreurs de réseau pour ne pas perturber l'expérience utilisateur
      // avec des erreurs liées aux rapports CSP
      credentials: 'same-origin',
    }).catch(() => {
      // Silencieux en cas d'erreur pour ne pas perturber l'UX
    });

    // Support pour les services de monitoring comme Sentry
    if (typeof window !== 'undefined' && 'Sentry' in window) {
      // Utiliser une interface pour définir le type de Sentry
      interface SentryType {
        captureMessage: (
          message: string,
          options: { level: string; extra: Record<string, unknown> }
        ) => void;
      }

      // Cast de window.Sentry avec l'interface définie
      const sentry = (window as unknown as { Sentry: SentryType }).Sentry;
      sentry.captureMessage('CSP Violation', {
        level: 'warning',
        extra: { report },
      });
    }
  }
};

const createEmptyCSPReport = (): CSPViolationReport => ({
  'csp-report': {},
});

type FetchParams = Parameters<typeof fetch>;

/**
 * Configure un endpoint pour recevoir les rapports CSP
 * Cette fonction devrait être appelée au démarrage de l'application
 */
export const setupCSPReporting = (): void => {
  // Créer un endpoint pour les rapports CSP si nous sommes dans un navigateur
  if (typeof window !== 'undefined') {
    // Créer une route pour recevoir les rapports CSP
    const originalFetch = window.fetch;

    window.fetch = function (...args: FetchParams) {
      const [input, init] = args;
      // Intercepter les requêtes vers l'endpoint CSP
      if (input === config.endpoint) {
        try {
          const reportData =
            init && init.body
              ? (JSON.parse(init.body as string) as CSPViolationReport)
              : createEmptyCSPReport();
          handleCSPViolation(reportData);
          return Promise.resolve(new Response(null, { status: 204 }));
        } catch (error) {
          console.error('Failed to parse CSP report:', error);
          return Promise.resolve(new Response(null, { status: 400 }));
        }
      }

      // Sinon, comportement normal
      return originalFetch.apply(window, args);
    };

    // Écouter également l'événement securitypolicyviolation
    document.addEventListener('securitypolicyviolation', (e: SecurityPolicyViolationEvent) => {
      const report: CSPViolationReport = {
        'csp-report': {
          'document-uri': e.documentURI,
          referrer: e.referrer,
          'violated-directive': e.violatedDirective,
          'effective-directive': e.effectiveDirective,
          'original-policy': e.originalPolicy,
          'blocked-uri': e.blockedURI,
          'source-file': e.sourceFile || undefined,
          'line-number': e.lineNumber || undefined,
          'column-number': e.columnNumber || undefined,
          'status-code': e.statusCode || undefined,
        },
      };

      handleCSPViolation(report);
    });
  }
};

export default setupCSPReporting;
