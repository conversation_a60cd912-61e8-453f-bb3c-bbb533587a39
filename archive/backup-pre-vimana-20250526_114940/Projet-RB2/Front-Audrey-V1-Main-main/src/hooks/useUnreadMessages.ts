import { useState, useEffect, useCallback } from 'react';
import { getUnreadCount } from '../services/api/messagingService';
import { useAuth } from '../contexts/AuthContext';
import { messagingNotificationService } from '../services/messagingNotificationService';

/**
 * Hook pour récupérer et gérer le nombre de messages non lus
 * @returns Le nombre de messages non lus et une fonction pour rafraîchir le compteur
 */
export const useUnreadMessages = () => {
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();

  const fetchUnreadCount = useCallback(async () => {
    if (!isAuthenticated) {
      setUnreadCount(0);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const count = await getUnreadCount();
      setUnreadCount(count);
    } catch (err: unknown) {
      console.error('Erreur lors de la récupération des messages non lus:', err);
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue');
      } else {
        setError('Une erreur inconnue est survenue lors de la récupération des messages non lus');
      }
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Récupérer le nombre de messages non lus au chargement et lorsque l'état d'authentification change
  useEffect(() => {
    fetchUnreadCount();

    // S'abonner aux mises à jour du compteur de messages non lus
    const unsubscribe = messagingNotificationService.onUnreadCountUpdate((count) => {
      setUnreadCount(count);
    });

    // Mettre à jour le compteur toutes les minutes
    const intervalId = setInterval(fetchUnreadCount, 60000);

    return () => {
      unsubscribe();
      clearInterval(intervalId);
    };
  }, [isAuthenticated, fetchUnreadCount]);

  return {
    unreadCount,
    loading,
    error,
    refreshUnreadCount: fetchUnreadCount,
  };
};
