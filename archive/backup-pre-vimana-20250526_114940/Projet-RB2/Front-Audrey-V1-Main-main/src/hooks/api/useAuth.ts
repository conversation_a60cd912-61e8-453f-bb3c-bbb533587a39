import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  authService,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  ChangePasswordRequest,
  User,
} from '../../services/api';

interface UseAuthReturn {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  changePassword: (passwordData: ChangePasswordRequest) => Promise<void>;
  isAuthenticated: boolean;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(authService.isAuthenticated());
  const navigate = useNavigate();

  const login = useCallback(async (credentials: LoginRequest): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      const response: AuthResponse = await authService.login(credentials);
      setUser(response.user);
      setIsAuthenticated(true);
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la connexion');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (userData: RegisterRequest): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      const response: AuthResponse = await authService.register(userData);
      setUser(response.user);
      setIsAuthenticated(true);
    } catch (err: any) {
      setError(err.message || "Une erreur est survenue lors de l'inscription");
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);
      navigate('/login');
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la déconnexion');
    } finally {
      setIsLoading(false);
    }
  }, [navigate]);

  const requestPasswordReset = useCallback(async (email: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await authService.requestPasswordReset(email);
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la demande de réinitialisation');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const resetPassword = useCallback(async (token: string, newPassword: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await authService.resetPassword(token, newPassword);
    } catch (err: any) {
      setError(
        err.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe'
      );
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const changePassword = useCallback(async (passwordData: ChangePasswordRequest): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await authService.changePassword(passwordData);
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors du changement de mot de passe');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    user,
    isLoading,
    error,
    login,
    register,
    logout,
    requestPasswordReset,
    resetPassword,
    changePassword,
    isAuthenticated,
  };
};
