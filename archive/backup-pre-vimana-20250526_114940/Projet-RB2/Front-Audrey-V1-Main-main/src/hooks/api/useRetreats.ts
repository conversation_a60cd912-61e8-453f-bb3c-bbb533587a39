import { useState, useCallback } from 'react';
import {
  retreatService,
  Retreat,
  RetreatFilters,
  CreateRetreatRequest,
  UpdateRetreatRequest,
  PaginatedRetreats,
} from '../../services/api';

interface UseRetreatsReturn {
  retreats: Retreat[];
  totalRetreats: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  error: string | null;
  getRetreats: (filters?: RetreatFilters) => Promise<PaginatedRetreats>;
  getRetreatById: (id: string) => Promise<Retreat>;
  createRetreat: (retreatData: CreateRetreatRequest) => Promise<Retreat>;
  updateRetreat: (id: string, retreatData: UpdateRetreatRequest) => Promise<Retreat>;
  deleteRetreat: (id: string) => Promise<void>;
  uploadRetreatImage: (id: string, file: File) => Promise<string>;
  deleteRetreatImage: (id: string, imageUrl: string) => Promise<void>;
  getMyRetreats: () => Promise<Retreat[]>;
}

export const useRetreats = (): UseRetreatsReturn => {
  const [retreats, setRetreats] = useState<Retreat[]>([]);
  const [totalRetreats, setTotalRetreats] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const getRetreats = useCallback(
    async (filters: RetreatFilters = {}): Promise<PaginatedRetreats> => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await retreatService.getRetreats(filters);
        setRetreats(response.data);
        setTotalRetreats(response.total);
        setCurrentPage(response.page);
        setTotalPages(response.totalPages);
        return response;
      } catch (err: any) {
        setError(err.message || 'Une erreur est survenue lors de la récupération des retraites');
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const getRetreatById = useCallback(async (id: string): Promise<Retreat> => {
    setIsLoading(true);
    setError(null);
    try {
      return await retreatService.getRetreatById(id);
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la récupération de la retraite');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createRetreat = useCallback(async (retreatData: CreateRetreatRequest): Promise<Retreat> => {
    setIsLoading(true);
    setError(null);
    try {
      const newRetreat = await retreatService.createRetreat(retreatData);
      // Mettre à jour la liste des retraites si nécessaire
      setRetreats((prevRetreats) => [newRetreat, ...prevRetreats]);
      setTotalRetreats((prev) => prev + 1);
      return newRetreat;
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la création de la retraite');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateRetreat = useCallback(
    async (id: string, retreatData: UpdateRetreatRequest): Promise<Retreat> => {
      setIsLoading(true);
      setError(null);
      try {
        const updatedRetreat = await retreatService.updateRetreat(id, retreatData);
        // Mettre à jour la liste des retraites si nécessaire
        setRetreats((prevRetreats) =>
          prevRetreats.map((retreat) => (retreat.id === id ? updatedRetreat : retreat))
        );
        return updatedRetreat;
      } catch (err: any) {
        setError(err.message || 'Une erreur est survenue lors de la mise à jour de la retraite');
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const deleteRetreat = useCallback(async (id: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await retreatService.deleteRetreat(id);
      // Mettre à jour la liste des retraites
      setRetreats((prevRetreats) => prevRetreats.filter((retreat) => retreat.id !== id));
      setTotalRetreats((prev) => prev - 1);
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la suppression de la retraite');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const uploadRetreatImage = useCallback(async (id: string, file: File): Promise<string> => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await retreatService.uploadRetreatImage(id, file);
      return response.url;
    } catch (err: any) {
      setError(err.message || "Une erreur est survenue lors du téléchargement de l'image");
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteRetreatImage = useCallback(async (id: string, imageUrl: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await retreatService.deleteRetreatImage(id, imageUrl);
    } catch (err: any) {
      setError(err.message || "Une erreur est survenue lors de la suppression de l'image");
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getMyRetreats = useCallback(async (): Promise<Retreat[]> => {
    setIsLoading(true);
    setError(null);
    try {
      const myRetreats = await retreatService.getMyRetreats();
      return myRetreats;
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue lors de la récupération de vos retraites');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    retreats,
    totalRetreats,
    currentPage,
    totalPages,
    isLoading,
    error,
    getRetreats,
    getRetreatById,
    createRetreat,
    updateRetreat,
    deleteRetreat,
    uploadRetreatImage,
    deleteRetreatImage,
    getMyRetreats,
  };
};
