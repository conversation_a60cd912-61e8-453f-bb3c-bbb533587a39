apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: audrey-frontend-alerts
  labels:
    app: audrey-frontend
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
  - name: audrey-frontend.rules
    rules:
    - alert: AudreyFrontendDown
      expr: absent(up{job="audrey-frontend"} == 1)
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Audrey Frontend is down"
        description: "Audrey Frontend has been down for more than 5 minutes."
        runbook_url: "https://wiki.retreat-and-be.com/runbooks/frontend-down"
    
    - alert: Audrey<PERSON>rontendHighErrorRate
      expr: sum(rate(http_requests_total{job="audrey-frontend", status=~"5.."}[5m])) / sum(rate(http_requests_total{job="audrey-frontend"}[5m])) > 0.05
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Audrey Frontend has a high error rate"
        description: "<PERSON> is returning 5xx errors for more than 5% of requests."
        runbook_url: "https://wiki.retreat-and-be.com/runbooks/frontend-high-error-rate"
    
    - alert: <PERSON><PERSON><PERSON><PERSON>HighResponseTime
      expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job="audrey-frontend"}[5m])) by (le)) > 2
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Audrey Frontend has high response time"
        description: "Audrey Frontend is responding slowly. 95th percentile response time is above 2 seconds."
        runbook_url: "https://wiki.retreat-and-be.com/runbooks/frontend-high-response-time"
    
    - alert: AudreyFrontendHighCPUUsage
      expr: sum(rate(container_cpu_usage_seconds_total{container="audrey-frontend"}[5m])) / sum(kube_pod_container_resource_limits_cpu_cores{container="audrey-frontend"}) > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Audrey Frontend has high CPU usage"
        description: "Audrey Frontend is using more than 80% of its CPU limit."
        runbook_url: "https://wiki.retreat-and-be.com/runbooks/frontend-high-cpu-usage"
    
    - alert: AudreyFrontendHighMemoryUsage
      expr: sum(container_memory_usage_bytes{container="audrey-frontend"}) / sum(kube_pod_container_resource_limits_memory_bytes{container="audrey-frontend"}) > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Audrey Frontend has high memory usage"
        description: "Audrey Frontend is using more than 80% of its memory limit."
        runbook_url: "https://wiki.retreat-and-be.com/runbooks/frontend-high-memory-usage"
    
    - alert: AudreyFrontendPodRestarting
      expr: increase(kube_pod_container_status_restarts_total{container="audrey-frontend"}[15m]) > 3
      labels:
        severity: warning
      annotations:
        summary: "Audrey Frontend pod is restarting frequently"
        description: "Audrey Frontend pod has restarted more than 3 times in the last 15 minutes."
        runbook_url: "https://wiki.retreat-and-be.com/runbooks/frontend-pod-restarting"
    
    - alert: AudreyFrontendCertificateExpiringSoon
      expr: probe_ssl_earliest_cert_expiry{job="audrey-frontend-blackbox"} - time() < 86400 * 30
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: "Audrey Frontend SSL certificate is expiring soon"
        description: "Audrey Frontend SSL certificate will expire in less than 30 days."
        runbook_url: "https://wiki.retreat-and-be.com/runbooks/frontend-certificate-expiring"
