# Configuration des En-têtes de Sécurité HTTP

Ce document explique comment configurer les en-têtes de sécurité HTTP pour l'application `Front-Audrey-V1-Main-main`.

## Importance des En-têtes de Sécurité

Les en-têtes de sécurité HTTP sont essentiels pour renforcer la sécurité de votre application web. Ils permettent de :
- Prévenir de nombreuses attaques comme le XSS, le clickjacking, le MIME-sniffing
- Forcer l'utilisation de HTTPS
- Contrôler les permissions des fonctionnalités sensibles
- Définir des politiques strictes pour l'origine des ressources (CSP)

## En-têtes Implémentés

### Content Security Policy (CSP)
- **Objectif** : Contrôler les sources des ressources chargées (scripts, styles, images, etc.)
- **Configuration** : Déjà implémentée en mode "report-only" via méta-tag, mais devrait être configurée au niveau du serveur en production

### HTTP Strict Transport Security (HSTS)
- **Objectif** : Forcer l'utilisation de HTTPS pour toutes les connexions
- **Configuration** : `Strict-Transport-Security: max-age=31536000; includeSubDomains; preload`

### X-Frame-Options
- **Objectif** : Empêcher votre site d'être affiché dans une iframe (protection contre le clickjacking)
- **Configuration** : `X-Frame-Options: DENY`

### X-Content-Type-Options
- **Objectif** : Empêcher le navigateur de faire du MIME-sniffing
- **Configuration** : `X-Content-Type-Options: nosniff`

### Referrer-Policy
- **Objectif** : Contrôler les informations envoyées via l'en-tête Referer
- **Configuration** : `Referrer-Policy: strict-origin-when-cross-origin`

### Permissions-Policy
- **Objectif** : Contrôler les fonctionnalités et API utilisables
- **Configuration** : Restrictions sur la caméra, le microphone, la géolocalisation, etc.

### X-XSS-Protection
- **Objectif** : Protection supplémentaire contre les attaques XSS pour les anciens navigateurs
- **Configuration** : `X-XSS-Protection: 1; mode=block`

## Mise en Œuvre

### Avec Nginx
Inclure le fichier `security-headers.conf` dans votre configuration Nginx :

```nginx
server {
    # Autres configurations
    include /path/to/security-headers.conf;
    # Suite de la configuration
}
```

### Avec Apache
Adapter les directives du fichier `security-headers.conf` pour Apache dans `.htaccess` :

```apache
# Content-Security-Policy
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://api.mapbox.com https://events.mapbox.com; style-src 'self' 'unsafe-inline' https://api.mapbox.com https://fonts.googleapis.com; img-src 'self' data: blob: https://*.tile.openstreetmap.org https://api.mapbox.com https://*; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' https://api.mapbox.com https://events.mapbox.com https://api.retreat-and-be.com; frame-src 'self'; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; report-uri /csp-report-endpoint;"

# HTTP Strict Transport Security (HSTS)
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

# X-Frame-Options
Header always set X-Frame-Options "DENY"

# X-Content-Type-Options
Header always set X-Content-Type-Options "nosniff"

# Referrer-Policy
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Permissions-Policy
Header always set Permissions-Policy "camera=(), microphone=(), geolocation=(self), interest-cohort=()"

# X-XSS-Protection
Header always set X-XSS-Protection "1; mode=block"
```

### Avec un CDN ou Service d'Hébergement Statique (Netlify, Vercel, etc.)
La plupart des services d'hébergement statique modernes permettent de configurer les en-têtes HTTP via un fichier de configuration :

#### Netlify (`netlify.toml`)
```toml
[[headers]]
  for = "/*"
  [headers.values]
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://api.mapbox.com https://events.mapbox.com; style-src 'self' 'unsafe-inline' https://api.mapbox.com https://fonts.googleapis.com; img-src 'self' data: blob: https://*.tile.openstreetmap.org https://api.mapbox.com https://*; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' https://api.mapbox.com https://events.mapbox.com https://api.retreat-and-be.com; frame-src 'self'; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; report-uri /csp-report-endpoint;"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(self), interest-cohort=()"
    X-XSS-Protection = "1; mode=block"
```

#### Vercel (`vercel.json`)
```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "Content-Security-Policy",
          "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://api.mapbox.com https://events.mapbox.com; style-src 'self' 'unsafe-inline' https://api.mapbox.com https://fonts.googleapis.com; img-src 'self' data: blob: https://*.tile.openstreetmap.org https://api.mapbox.com https://*; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' https://api.mapbox.com https://events.mapbox.com https://api.retreat-and-be.com; frame-src 'self'; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; report-uri /csp-report-endpoint;"
        },
        {
          "key": "Strict-Transport-Security",
          "value": "max-age=31536000; includeSubDomains; preload"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options", 
          "value": "nosniff"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        },
        {
          "key": "Permissions-Policy",
          "value": "camera=(), microphone=(), geolocation=(self), interest-cohort=()"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

## Test des En-têtes de Sécurité

Pour vérifier que vos en-têtes sont correctement configurés, vous pouvez utiliser :

1. [Security Headers](https://securityheaders.com/) - Analyseur en ligne des en-têtes HTTP de sécurité
2. [Mozilla Observatory](https://observatory.mozilla.org/) - Analyse complète de la sécurité web
3. DevTools des navigateurs (onglet "Network", puis sélectionner une requête et regarder les en-têtes)

## Considérations pour l'API

Si votre frontend communique avec une API, assurez-vous que cette dernière implémente également les en-têtes de sécurité appropriés et gère correctement les demandes CORS.

## Protection CSRF Supplémentaire

Pour les applications qui utilisent l'authentification par cookies (et non uniquement par JWT dans localStorage), une protection CSRF supplémentaire est recommandée :

1. Utiliser l'attribut `SameSite=Strict` ou `SameSite=Lax` sur les cookies
2. Mettre en place un système de jetons CSRF pour les formulaires et requêtes modifiant l'état de l'application
3. Vérifier l'en-tête `Origin` ou `Referer` pour les requêtes sensibles 