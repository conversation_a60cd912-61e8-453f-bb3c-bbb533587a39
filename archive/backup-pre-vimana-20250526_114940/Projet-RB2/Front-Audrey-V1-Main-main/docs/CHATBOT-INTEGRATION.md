# Intégration du Chatbot avec Superagent

Ce document explique comment le chatbot de Front-Audrey-V1-Main-main est intégré avec le microservice superagent.

## Architecture

L'intégration du chatbot se compose des éléments suivants :

1. **Interface utilisateur** : Composants React dans `Front-Audrey-V1-Main-main/src/components/`
   - `ChatBot.tsx` : Bouton flottant pour ouvrir le chatbot
   - `ChatWindow.tsx` : Fenêtre de chat avec historique des messages et formulaire d'envoi

2. **Logique métier** : Hook React dans `Front-Audrey-V1-Main-main/src/hooks/`
   - `useChatbot.ts` : Gestion de l'état du chat, envoi et réception des messages

3. **Service API** : Service dans `Front-Audrey-V1-Main-main/src/services/api/`
   - `chatbotService.ts` : Communication avec l'API du microservice superagent

4. **Backend** : Microservice superagent à la racine du projet
   - API REST exposée sur le port 8001 (par défaut)
   - Endpoint principal : `/chat`

## Configuration

### Variables d'environnement

Pour configurer l'intégration, vous devez définir les variables d'environnement suivantes :

```
REACT_APP_SUPERAGENT_URL=http://localhost:8001
```

Vous pouvez copier le fichier `.env.example` vers `.env` et ajuster les valeurs selon votre environnement.

### Démarrage du microservice superagent

Le microservice superagent doit être démarré avant d'utiliser le chatbot. Pour le démarrer :

```bash
cd /chemin/vers/Projet-RB2/superagent
./start.sh
```

Ou pour démarrer uniquement le service chatbot :

```bash
cd /chemin/vers/Projet-RB2/superagent
./scripts/start_chatbot.sh
```

## Utilisation

Le chatbot est accessible via un bouton flottant en bas à droite de l'interface. Cliquez sur ce bouton pour ouvrir la fenêtre de chat.

### Fonctionnalités

- Envoi et réception de messages en temps réel
- Détection d'intention basée sur l'IA
- Extraction d'entités pour une meilleure compréhension des requêtes
- Historique de conversation persistant

### Personnalisation

Vous pouvez personnaliser l'apparence et le comportement du chatbot en modifiant les composants React correspondants.

## Développement

### Ajout de nouvelles fonctionnalités

Pour ajouter de nouvelles fonctionnalités au chatbot :

1. Mettez à jour le service chatbot dans `superagent/ai_engine/chatbot/`
2. Ajoutez les méthodes correspondantes dans `chatbotService.ts`
3. Mettez à jour le hook `useChatbot.ts` pour utiliser ces nouvelles méthodes
4. Modifiez les composants UI pour exposer ces fonctionnalités

### Tests

Pour tester l'intégration du chatbot :

1. Assurez-vous que le microservice superagent est en cours d'exécution
2. Lancez l'application frontend
3. Ouvrez le chatbot et envoyez des messages de test

## Dépannage

### Le chatbot ne se connecte pas

- Vérifiez que le microservice superagent est en cours d'exécution
- Vérifiez que l'URL du microservice est correctement configurée dans les variables d'environnement
- Vérifiez les logs du microservice pour détecter d'éventuelles erreurs

### Les messages ne sont pas envoyés

- Vérifiez la console du navigateur pour détecter d'éventuelles erreurs
- Vérifiez que l'utilisateur est authentifié si l'authentification est requise
- Vérifiez les logs du microservice pour détecter d'éventuelles erreurs de traitement des messages
