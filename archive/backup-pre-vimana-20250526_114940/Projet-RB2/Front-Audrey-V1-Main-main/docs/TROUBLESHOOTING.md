# Guide de dépannage

Ce document fournit des solutions aux problèmes courants rencontrés lors du déploiement et de l'utilisation de Front-Audrey-V1-Main-main.

## Problèmes de déploiement Docker

### L'image Docker ne se construit pas

**Symptôme**: La commande `docker build` échoue avec des erreurs.

**Solutions possibles**:

1. **Vérifier les dépendances**:
   ```bash
   npm ci
   ```

2. **Vérifier l'espace disque**:
   ```bash
   df -h
   ```

3. **Nettoyer Docker**:
   ```bash
   docker system prune -a
   ```

4. **Vérifier le Dockerfile**:
   Assurez-vous que le Dockerfile est correctement formaté et que tous les chemins sont corrects.

### Le conteneur Docker ne démarre pas

**Symptôme**: La commande `docker run` échoue ou le conteneur s'arrête immédiatement.

**Solutions possibles**:

1. **Vérifier les logs**:
   ```bash
   docker logs <container-id>
   ```

2. **Vérifier les variables d'environnement**:
   Assurez-vous que toutes les variables d'environnement requises sont définies.

3. **Vérifier les ports**:
   Assurez-vous que le port 80 n'est pas déjà utilisé par un autre service.

4. **Vérifier les permissions**:
   Assurez-vous que les fichiers dans le conteneur ont les bonnes permissions.

## Problèmes de déploiement Kubernetes

### Le pod ne démarre pas

**Symptôme**: Le pod reste en état `Pending` ou `CrashLoopBackOff`.

**Solutions possibles**:

1. **Vérifier les événements du pod**:
   ```bash
   kubectl describe pod <pod-name> -n <namespace>
   ```

2. **Vérifier les logs du pod**:
   ```bash
   kubectl logs <pod-name> -n <namespace>
   ```

3. **Vérifier les ressources du cluster**:
   Assurez-vous que le cluster a suffisamment de ressources (CPU, mémoire) pour démarrer le pod.

4. **Vérifier les secrets et configmaps**:
   Assurez-vous que tous les secrets et configmaps référencés existent.

### Le service n'est pas accessible

**Symptôme**: Le service n'est pas accessible via son URL.

**Solutions possibles**:

1. **Vérifier le service**:
   ```bash
   kubectl get service <service-name> -n <namespace>
   kubectl describe service <service-name> -n <namespace>
   ```

2. **Vérifier les endpoints**:
   ```bash
   kubectl get endpoints <service-name> -n <namespace>
   ```

3. **Vérifier le sélecteur du service**:
   Assurez-vous que le sélecteur du service correspond aux labels du pod.

4. **Vérifier le réseau**:
   Assurez-vous que le réseau du cluster fonctionne correctement.

### L'ingress ne fonctionne pas

**Symptôme**: L'application n'est pas accessible via l'URL de l'ingress.

**Solutions possibles**:

1. **Vérifier l'ingress**:
   ```bash
   kubectl get ingress <ingress-name> -n <namespace>
   kubectl describe ingress <ingress-name> -n <namespace>
   ```

2. **Vérifier le contrôleur d'ingress**:
   Assurez-vous que le contrôleur d'ingress est installé et fonctionne correctement.

3. **Vérifier les certificats TLS**:
   Assurez-vous que les certificats TLS sont valides et correctement configurés.

4. **Vérifier les règles d'ingress**:
   Assurez-vous que les règles d'ingress sont correctement configurées.

## Problèmes d'application

### L'application ne se charge pas

**Symptôme**: L'application affiche une page blanche ou une erreur dans le navigateur.

**Solutions possibles**:

1. **Vérifier la console du navigateur**:
   Ouvrez les outils de développement du navigateur et vérifiez les erreurs dans la console.

2. **Vérifier les requêtes réseau**:
   Vérifiez les requêtes réseau dans l'onglet "Network" des outils de développement.

3. **Vérifier les logs du conteneur**:
   ```bash
   kubectl logs <pod-name> -n <namespace>
   ```

4. **Vérifier la configuration de l'application**:
   Assurez-vous que toutes les variables d'environnement sont correctement définies.

### Les API ne répondent pas

**Symptôme**: Les requêtes API échouent ou ne répondent pas.

**Solutions possibles**:

1. **Vérifier la connectivité au backend**:
   Assurez-vous que le backend est accessible depuis le frontend.

2. **Vérifier les logs du backend**:
   ```bash
   kubectl logs <backend-pod-name> -n <namespace>
   ```

3. **Vérifier les CORS**:
   Assurez-vous que les en-têtes CORS sont correctement configurés.

4. **Vérifier l'authentification**:
   Assurez-vous que les tokens d'authentification sont valides.

### Problèmes de performance

**Symptôme**: L'application est lente ou ne répond pas.

**Solutions possibles**:

1. **Vérifier les ressources du pod**:
   ```bash
   kubectl top pod <pod-name> -n <namespace>
   ```

2. **Vérifier les métriques Prometheus**:
   Consultez les métriques de performance dans Grafana.

3. **Vérifier le nombre de requêtes**:
   Assurez-vous que l'application ne fait pas trop de requêtes API.

4. **Vérifier la taille des réponses**:
   Assurez-vous que les réponses API ne sont pas trop volumineuses.

## Problèmes de mise à jour

### La mise à jour échoue

**Symptôme**: La mise à jour de l'application échoue.

**Solutions possibles**:

1. **Vérifier les logs de déploiement**:
   ```bash
   kubectl describe deployment <deployment-name> -n <namespace>
   ```

2. **Vérifier les événements**:
   ```bash
   kubectl get events -n <namespace> --sort-by='.lastTimestamp'
   ```

3. **Annuler la mise à jour**:
   ```bash
   kubectl rollout undo deployment/<deployment-name> -n <namespace>
   ```

4. **Vérifier les différences de configuration**:
   Comparez la configuration actuelle avec la nouvelle configuration.

### Problèmes après la mise à jour

**Symptôme**: L'application ne fonctionne pas correctement après la mise à jour.

**Solutions possibles**:

1. **Vérifier les logs**:
   ```bash
   kubectl logs <pod-name> -n <namespace>
   ```

2. **Vérifier les changements de l'API**:
   Assurez-vous que les changements de l'API sont compatibles avec le frontend.

3. **Annuler la mise à jour**:
   ```bash
   kubectl rollout undo deployment/<deployment-name> -n <namespace>
   ```

4. **Vérifier les migrations de données**:
   Assurez-vous que toutes les migrations de données ont été effectuées correctement.

## Problèmes de sécurité

### Problèmes de certificats TLS

**Symptôme**: Les certificats TLS ne fonctionnent pas ou sont expirés.

**Solutions possibles**:

1. **Vérifier les certificats**:
   ```bash
   kubectl get secret <tls-secret-name> -n <namespace>
   ```

2. **Renouveler les certificats**:
   Si vous utilisez cert-manager, vérifiez l'état du certificat:
   ```bash
   kubectl get certificate -n <namespace>
   ```

3. **Vérifier la configuration de l'ingress**:
   Assurez-vous que l'ingress est correctement configuré pour utiliser les certificats TLS.

4. **Vérifier le contrôleur d'ingress**:
   Assurez-vous que le contrôleur d'ingress est configuré pour gérer les certificats TLS.

### Problèmes d'authentification

**Symptôme**: Les utilisateurs ne peuvent pas se connecter ou sont déconnectés de manière inattendue.

**Solutions possibles**:

1. **Vérifier les logs d'authentification**:
   Vérifiez les logs du service d'authentification.

2. **Vérifier les tokens JWT**:
   Assurez-vous que les tokens JWT sont valides et non expirés.

3. **Vérifier la configuration CORS**:
   Assurez-vous que les en-têtes CORS sont correctement configurés.

4. **Vérifier les cookies**:
   Assurez-vous que les cookies sont correctement configurés et non expirés.
