{"Comment": "CloudFront Distribution for Audrey <PERSON>", "Origins": {"Quantity": 1, "Items": [{"Id": "audrey-frontend-origin", "DomainName": "app.retreat-and-be.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "CustomOriginConfig": {"HTTPPort": 80, "HTTPSPort": 443, "OriginProtocolPolicy": "https-only", "OriginSslProtocols": {"Quantity": 1, "Items": ["TLSv1.2"]}, "OriginReadTimeout": 30, "OriginKeepaliveTimeout": 5}}]}, "DefaultCacheBehavior": {"TargetOriginId": "audrey-frontend-origin", "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"], "CachedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"]}}, "Compress": true, "DefaultTTL": 86400, "MinTTL": 0, "MaxTTL": 31536000, "ForwardedValues": {"QueryString": true, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 3, "Items": ["Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"]}, "QueryStringCacheKeys": {"Quantity": 0}}, "SmoothStreaming": false, "FieldLevelEncryptionId": ""}, "CacheBehaviors": {"Quantity": 2, "Items": [{"PathPattern": "/static/*", "TargetOriginId": "audrey-frontend-origin", "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"], "CachedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"]}}, "Compress": true, "DefaultTTL": 86400, "MinTTL": 0, "MaxTTL": 31536000, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "SmoothStreaming": false, "FieldLevelEncryptionId": ""}, {"PathPattern": "/assets/*", "TargetOriginId": "audrey-frontend-origin", "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"], "CachedMethods": {"Quantity": 2, "Items": ["GET", "HEAD"]}}, "Compress": true, "DefaultTTL": 86400, "MinTTL": 0, "MaxTTL": 31536000, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "SmoothStreaming": false, "FieldLevelEncryptionId": ""}]}, "CustomErrorResponses": {"Quantity": 1, "Items": [{"ErrorCode": 404, "ResponsePagePath": "/index.html", "ResponseCode": "200", "ErrorCachingMinTTL": 300}]}, "PriceClass": "PriceClass_All", "Enabled": true, "ViewerCertificate": {"CloudFrontDefaultCertificate": false, "ACMCertificateArn": "arn:aws:acm:us-east-1:ACCOUNT_ID:certificate/CERTIFICATE_ID", "SSLSupportMethod": "sni-only", "MinimumProtocolVersion": "TLSv1.2_2021"}, "Restrictions": {"GeoRestriction": {"RestrictionType": "none", "Quantity": 0}}, "WebACLId": "", "HttpVersion": "http2", "IsIPV6Enabled": true}