{"preferences": {"title": "Préférences d'explication", "pageTitle": "Préférences d'explication", "pageSubtitle": "Personnalisez la façon dont les explications des recommandations vous sont présentées", "preferredStyle": "Style d'explication préféré", "detailLevel": "Ni<PERSON><PERSON> de <PERSON>", "preferredFormat": "Format d'explication préféré", "language": "Langue des explications", "highlightedFactors": "Facteurs à mettre en évidence", "hiddenFactors": "Facteurs à masquer", "saveSuccess": "Préférences d'explication enregistrées avec succès", "saveError": "Erreur lors de l'enregistrement des préférences d'explication", "loadError": "Erreur lors du chargement des préférences d'explication", "resetSuccess": "Préférences d'explication réinitialisées avec succès", "resetError": "Erreur lors de la réinitialisation des préférences d'explication", "helpTitle": "Comment personnaliser vos explications", "styleTitle": "Style d'explication", "styleDescription": "Choisis<PERSON>z le style qui vous convient le mieux pour comprendre les recommandations", "detailTitle": "Ni<PERSON><PERSON> de <PERSON>", "detailDescription": "Définissez la quantité d'informations que vous souhaitez voir dans les explications", "formatTitle": "Format d'explication", "formatDescription": "Sélectionnez les formats dans lesquels vous préférez recevoir les explications", "languageTitle": "<PERSON><PERSON>", "languageDescription": "Choisissez la langue dans laquelle vous souhaitez recevoir les explications", "exampleTitle": "Exemple d'explication", "exampleHeader": "Retraite de yoga en Provence", "exampleDescription": "Voici un exemple d'explication personnalisée selon vos préférences", "exampleFactors": "Facteurs principaux :", "exampleFactor1": "Correspond à vos intérêts pour le yoga et la méditation", "exampleFactor2": "Destination populaire parmi les utilisateurs similaires à vous", "exampleFactor3": "Correspond à votre budget et à vos dates préférées", "exampleSummary": "Résumé :", "exampleText": "Cette retraite vous est recommandée car elle correspond parfaitement à vos intérêts pour le yoga et la méditation. De plus, elle est très appréciée par des utilisateurs ayant des préférences similaires aux vôtres."}, "style": {"conversational": "Conversationnel et amical", "technical": "Technique et précis", "concise": "Concis et direct", "narrative": "Narratif et descriptif", "educational": "Éducatif avec des informations supplémentaires"}, "detailLevel": {"minimal": "Minimal - Juste l'essentiel", "moderate": "Modéré - Équilibré", "detailed": "Détaillé - Informations complètes", "comprehensive": "Complet - Très d<PERSON>lé avec des informations supplémentaires"}, "format": {"text": "Texte simple", "visual": "Visuel (graphiques, icônes)", "mixed": "Mixte (texte et éléments visuels)", "interactive": "Interactif (explorable)"}, "factors": {"similarity": "Similarité avec vos intérêts", "popularity": "Popularité parmi les utilisateurs similaires", "rating": "Évaluations et avis", "price": "Prix et budget", "location": "Emplacement et accessibilité", "duration": "Durée et dates", "instructor": "In<PERSON><PERSON><PERSON>eur ou guide", "amenities": "Équipements et services", "activities": "Activités proposées", "theme": "Thème et ambiance"}}