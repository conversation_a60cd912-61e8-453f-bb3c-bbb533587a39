#!/bin/bash

# Set text colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Correction des problèmes JSX dans les composants profile...${NC}"

# Correction des balises JSX auto-fermantes incorrectes (e = />)
find src/components/profile -name "*.tsx" -exec sed -i '' 's/e = \/>/onChange={e => /g' {} \;

# Correction des balises mal fermées
find src/components/profile -name "*.tsx" -exec sed -i '' 's/<\/SaveIcon>/<\/IconButton>/g' {} \;
find src/components/profile -name "*.tsx" -exec sed -i '' 's/<\/EditProfileDialog>/<\/div>/g' {} \;

# Correction des balises fermantes incorrectes avec MessageCircle
find src/components/profile -name "*.tsx" -exec sed -i '' 's/<\/MessageCircle>/<\/div>/g' {} \;

# Correction des propriétés startIcon incorrectes
find src/components/profile -name "*.tsx" -exec sed -i '' 's/startIcon={<EditIcon >}/startIcon={<EditIcon \/>}/g' {} \;
find src/components/profile -name "*.tsx" -exec sed -i '' 's/startIcon={<UserPlus >}/startIcon={<UserPlus \/>}/g' {} \;

# Correction des problèmes avec isMobile && <Toolbar >
find src/components/profile -name "*.tsx" -exec sed -i '' 's/isMobile && <Toolbar >/isMobile \&\& <Toolbar \/>/g' {} \;

echo -e "${GREEN}Correction des balises JSX terminée${NC}"
echo -e "${BLUE}Vérifiez les résultats après correction${NC}" 