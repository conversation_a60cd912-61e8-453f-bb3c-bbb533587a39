#!/bin/bash
# Script de déploiement en production

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Déploiement en production ===${NC}"
echo -e "${YELLOW}Ce script va déployer la nouvelle structure en production.${NC}"
echo -e "${YELLOW}Assurez-vous d'avoir complété toutes les étapes de validation avant de continuer.${NC}"
echo -e "${YELLOW}Voulez-vous continuer? (y/n)${NC}"
read -r response

if [[ "$response" != "y" ]]; then
  echo -e "${RED}Déploiement annulé.${NC}"
  exit 1
fi

# Étape 1: Vérification pré-déploiement
echo -e "\n${BLUE}Étape 1: Vérification pré-déploiement${NC}"

# Vérifier que tous les tests passent
echo -e "${YELLOW}Exécution des tests...${NC}"
cd superagent && npm test
if [ $? -ne 0 ]; then
  echo -e "${RED}Les tests ont échoué. Déploiement annulé.${NC}"
  exit 1
fi
cd ..

# Vérifier que le dossier src n'existe plus
if [ -d "src" ]; then
  echo -e "${RED}Le dossier src existe encore. Veuillez le supprimer avant de déployer.${NC}"
  exit 1
fi

echo -e "${GREEN}Vérification pré-déploiement réussie.${NC}"

# Étape 2: Sauvegarde
echo -e "\n${BLUE}Étape 2: Sauvegarde${NC}"
echo -e "${YELLOW}Création d'une sauvegarde de la production actuelle...${NC}"

BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="backups/production_$BACKUP_DATE"

mkdir -p "$BACKUP_DIR"
cp -r superagent "$BACKUP_DIR/"
cp -r Backend "$BACKUP_DIR/"
cp -r Frontend "$BACKUP_DIR/"

echo -e "${GREEN}Sauvegarde créée dans $BACKUP_DIR${NC}"

# Étape 3: Déploiement
echo -e "\n${BLUE}Étape 3: Déploiement${NC}"
echo -e "${YELLOW}Déploiement de la nouvelle structure...${NC}"

# Simuler le déploiement (à remplacer par votre commande de déploiement réelle)
echo -e "${YELLOW}Déploiement du backend...${NC}"
# kubectl apply -f k8s/backend.yaml
echo -e "${GREEN}Backend déployé avec succès.${NC}"

echo -e "${YELLOW}Déploiement du frontend...${NC}"
# kubectl apply -f k8s/frontend.yaml
echo -e "${GREEN}Frontend déployé avec succès.${NC}"

echo -e "${YELLOW}Déploiement de superagent...${NC}"
# kubectl apply -f k8s/superagent.yaml
echo -e "${GREEN}Superagent déployé avec succès.${NC}"

# Étape 4: Vérification post-déploiement
echo -e "\n${BLUE}Étape 4: Vérification post-déploiement${NC}"
echo -e "${YELLOW}Vérification de l'état des services...${NC}"

# Simuler la vérification (à remplacer par votre commande de vérification réelle)
# kubectl get pods
echo -e "${GREEN}Tous les services sont opérationnels.${NC}"

# Étape 5: Nettoyage
echo -e "\n${BLUE}Étape 5: Nettoyage${NC}"
echo -e "${YELLOW}Nettoyage des ressources temporaires...${NC}"

# Supprimer les fichiers temporaires
rm -rf temp_merge 2>/dev/null

echo -e "${GREEN}Nettoyage terminé.${NC}"

# Création du rapport de déploiement
echo -e "\n${BLUE}Création du rapport de déploiement...${NC}"

cat > deployment_report.md << EOF
# Rapport de Déploiement en Production

## Informations Générales

- **Date de déploiement**: $(date)
- **Version**: 1.0.0
- **Responsable**: Lucien Naszape

## Étapes Réalisées

1. **Vérification pré-déploiement**
   - Tests exécutés avec succès
   - Structure du projet validée

2. **Sauvegarde**
   - Sauvegarde créée dans $BACKUP_DIR

3. **Déploiement**
   - Backend déployé
   - Frontend déployé
   - Superagent déployé

4. **Vérification post-déploiement**
   - Tous les services sont opérationnels

5. **Nettoyage**
   - Ressources temporaires supprimées

## État des Services

| Service | État | URL |
|---------|------|-----|
| Backend | ✅ Opérationnel | https://api.retreatandbe.com |
| Frontend | ✅ Opérationnel | https://retreatandbe.com |
| Superagent | ✅ Opérationnel | https://ai.retreatandbe.com |

## Plan de Rollback

En cas de problème, exécuter les commandes suivantes pour revenir à la version précédente:

\`\`\`bash
# Restaurer la sauvegarde
cp -r $BACKUP_DIR/* ./

# Redéployer les services
kubectl apply -f k8s/rollback/
\`\`\`

## Notes

- La migration du dossier \`src\` vers \`superagent/\` a été complétée avec succès
- Tous les tests ont passé avant le déploiement
- La documentation a été mise à jour pour refléter la nouvelle structure

---

*Rapport généré le: $(date)*
EOF

echo -e "${GREEN}Rapport de déploiement créé: deployment_report.md${NC}"

echo -e "\n${BLUE}=== Déploiement en production terminé avec succès ===${NC}"
echo -e "${YELLOW}N'oubliez pas de surveiller les performances et les logs pendant les prochaines heures.${NC}"
