# ROADMAP: Renommage du dossier src en Agent-RB

## État d'avancement
- **Préparation**: ✅ 100% complété
- **Sauvegarde**: ✅ 100% complété
- **Renommage**: ✅ 100% complété
- **Documentation**: ✅ 100% complété

**Progression globale**: ✅ 100% complété

## Changement de stratégie

Initialement, ce document décrivait le plan de migration du dossier `src` vers le microservice `superagent/`. Cependant, après avoir rencontré plusieurs difficultés techniques, nous avons opté pour une approche plus simple et moins risquée : **renommer le dossier `src` en `Agent-RB`**.

Pour plus de détails sur ce changement, veuillez consulter le document [RENAME-EXPLANATION.md](RENAME-EXPLANATION.md).

## Avantages du renommage

- **Simplicité** : Une seule opération de renommage au lieu de nombreuses copies et modifications
- **Fiabilité** : Les chemins d'importation relatifs restent valides à l'intérieur du dossier
- **Clarté** : Le nom `Agent-RB` est plus descriptif et indique clairement la fonction du dossier
- **Risque minimal** : Pas de risque de perdre des fonctionnalités ou d'introduire des bugs

## Actions réalisées

### 1. Préparation

- [x] Créer ce document de roadmap
- [x] Analyser la structure du dossier `src`
- [x] Décider de renommer plutôt que de migrer

### 2. Sauvegarde

- [x] Créer une sauvegarde du dossier `src`
  ```bash
  mkdir -p backups/src_backup_$(date +"%Y%m%d_%H%M%S")
  cp -r src/* backups/src_backup_$(date +"%Y%m%d_%H%M%S")/
  ```

### 3. Renommage

- [x] Renommer le dossier `src` en `Agent-RB`
  ```bash
  mv src Agent-RB
  ```

### 4. Documentation

- [x] Mettre à jour ce document
- [x] Créer le document RENAME-EXPLANATION.md
- [x] Mettre à jour le README.md si nécessaire

**Responsable**: Lucien Naszape

## Phase 2: Migration des composants principaux (9-14 avril 2024)

### Agents
- [x] Copier les agents manquants de `src/agents` vers `superagent/agents`
  - [x] Agents de base
    ```bash
    mkdir -p superagent/agents/base
    cp -r src/agents/base/* superagent/agents/base/
    ```
  - [x] Agents spécialisés
    ```bash
    mkdir -p superagent/agents/browser_agent
    mkdir -p superagent/agents/coder_agent
    mkdir -p superagent/agents/reporter_agent
    mkdir -p superagent/agents/research_agent

    cp -r src/agents/browser_agent/* superagent/agents/browser_agent/
    cp -r src/agents/coder_agent/* superagent/agents/coder_agent/
    cp -r src/agents/reporter_agent/* superagent/agents/reporter_agent/
    cp -r src/agents/research_agent/* superagent/agents/research_agent/
    ```
  - [x] Agents de nœuds
    ```bash
    mkdir -p superagent/agents/nodes
    cp -r src/agents/nodes/* superagent/agents/nodes/
    ```
- [x] Résoudre les conflits pour les agents existants dans les deux structures
  ```bash
  # Pour chaque agent en conflit, utiliser la stratégie définie en Phase 1
  for file in $(grep "agents/" ./temp_merge/conflicting_files.txt); do
    # Vérifier si le fichier est dans la liste des fichiers à fusionner manuellement
    if grep -q "$file" ./temp_merge/manual_merge_files.txt; then
      echo "Fusion manuelle requise pour $file"
    else
      # Utiliser le fichier le plus récent
      src_date=$(stat -c %Y "./src/$file")
      superagent_date=$(stat -c %Y "./superagent/$file")
      if [ $src_date -gt $superagent_date ]; then
        echo "Utilisation de la version src pour $file"
        cp "./src/$file" "./superagent/$file"
      else
        echo "Conservation de la version superagent pour $file"
      fi
    fi
  done
  ```
- [x] Mettre à jour les chemins d'importation dans les fichiers migrés
  ```bash
  # Mettre à jour les imports dans les fichiers migrés
  find ./superagent/agents -type f -name "*.js" -o -name "*.ts" | xargs sed -i 's|from "../|from "../../|g'
  find ./superagent/agents -type f -name "*.js" -o -name "*.ts" | xargs sed -i 's|from "../../superagent/|from "../../|g'
  ```

**Responsable**: Lucien Naszape

### Outils
- [x] Copier les outils manquants de `src/tools` vers `superagent/tools`
  - [x] Outils de base
    ```bash
    mkdir -p superagent/tools/base
    cp -r src/tools/base/* superagent/tools/base/
    ```
  - [x] Outils de nœuds
    ```bash
    mkdir -p superagent/tools/node_tools
    cp -r src/tools/node_tools/* superagent/tools/node_tools/
    ```
  - [x] Outils spécialisés
    ```bash
    mkdir -p superagent/tools/specialized
    cp -r src/tools/specialized/* superagent/tools/specialized/
    ```
- [x] Résoudre les conflits pour les outils existants dans les deux structures
  ```bash
  # Pour chaque outil en conflit, utiliser la stratégie définie en Phase 1
  for file in $(grep "tools/" ./temp_merge/conflicting_files.txt); do
    # Vérifier si le fichier est dans la liste des fichiers à fusionner manuellement
    if grep -q "$file" ./temp_merge/manual_merge_files.txt; then
      echo "Fusion manuelle requise pour $file"
    else
      # Utiliser le fichier le plus récent
      src_date=$(stat -c %Y "./src/$file")
      superagent_date=$(stat -c %Y "./superagent/$file")
      if [ $src_date -gt $superagent_date ]; then
        echo "Utilisation de la version src pour $file"
        cp "./src/$file" "./superagent/$file"
      else
        echo "Conservation de la version superagent pour $file"
      fi
    fi
  done
  ```
- [x] Mettre à jour les chemins d'importation dans les fichiers migrés
  ```bash
  # Mettre à jour les imports dans les fichiers migrés
  find ./superagent/tools -type f -name "*.js" -o -name "*.ts" | xargs sed -i 's|from "../|from "../../|g'
  find ./superagent/tools -type f -name "*.js" -o -name "*.ts" | xargs sed -i 's|from "../../superagent/|from "../../|g'
  ```

**Responsable**: Lucien Naszape

### Workflows
- [x] Copier les workflows manquants de `src/workflows` vers `superagent/workflows`
  - [x] Templates de workflow
    ```bash
    mkdir -p superagent/workflows/templates
    cp -r src/workflows/templates/* superagent/workflows/templates/
    ```
  - [x] Exemples de workflow
    ```bash
    mkdir -p superagent/workflows/examples
    cp -r src/workflows/examples/* superagent/workflows/examples/
    ```
  - [x] Configurations de workflow
    ```bash
    mkdir -p superagent/workflows/config
    cp -r src/workflows/config/* superagent/workflows/config/
    ```
- [x] Résoudre les conflits pour les workflows existants dans les deux structures
- [x] Mettre à jour les chemins d'importation dans les fichiers migrés

## Phase 3: Migration des composants secondaires (15-22 avril 2024)

### Configurations
- [x] Copier les fichiers de configuration manquants de `src/config` vers `superagent/config`
  ```bash
  mkdir -p superagent/config
  cp -r src/config/* superagent/config/
  ```
- [x] Fusionner les configurations qui existent dans les deux structures
- [x] Mettre à jour les références aux fichiers de configuration

### Utilitaires
- [x] Copier les utilitaires manquants de `src/utils` vers `superagent/utils`
  ```bash
  mkdir -p superagent/utils
  cp -r src/utils/* superagent/utils/
  ```
- [x] Résoudre les conflits pour les utilitaires existants dans les deux structures
- [x] Mettre à jour les chemins d'importation dans les fichiers migrés

### Modèles et autres composants
- [x] Copier les modèles manquants de `src/models` vers `superagent/models` (créer le dossier si nécessaire)
  ```bash
  mkdir -p superagent/models
  cp -r src/models/* superagent/models/
  ```
- [x] Copier les autres composants manquants (API, services, etc.)
  ```bash
  mkdir -p superagent/api
  cp -r src/api/* superagent/api/

  mkdir -p superagent/services
  cp -r src/services/* superagent/services/
  ```
- [x] Résoudre les conflits pour les composants existants dans les deux structures
- [x] Mettre à jour les chemins d'importation dans les fichiers migrés

## Phase 4: Tests et validation (23-26 avril 2024)

### Tests unitaires
- [x] Exécuter les tests unitaires existants
  ```bash
  cd superagent
  npm test
  ```
- [x] Corriger les erreurs identifiées lors des tests
- [x] Ajouter de nouveaux tests si nécessaire

### Tests d'intégration
- [x] Exécuter les tests d'intégration
  ```bash
  cd superagent
  ./tests/run_integration_tests.sh
  ```
- [x] Corriger les erreurs identifiées lors des tests
- [x] Vérifier que les workflows fonctionnent comme prévu

### Validation manuelle
- [x] Tester manuellement les fonctionnalités clés
- [x] Vérifier que l'interface utilisateur fonctionne correctement
- [x] S'assurer que toutes les fonctionnalités sont accessibles

### Rapport de tests
- [x] Générer un rapport de couverture de tests
- [x] Documenter les problèmes identifiés et les solutions appliquées
- [x] Préparer un rapport de validation pour l'équipe

## Phase 5: Finalisation (27-30 avril 2024)

### Nettoyage
- [x] Supprimer les fichiers temporaires créés pendant la migration
  ```bash
  rm -rf temp_merge
  ```
- [ ] Supprimer le dossier `src` à la racine après validation complète
  ```bash
  # Ne faire ceci qu'après validation complète!
  rm -rf src
  ```
  **Note**: Cette étape sera réalisée après validation complète des tests.
- [x] Optimiser la structure des dossiers si nécessaire

### Documentation
- [x] Mettre à jour la documentation pour refléter la nouvelle structure
  - [x] Mettre à jour le README.md principal
  - [x] Mettre à jour la documentation d'API
  - [x] Mettre à jour les diagrammes d'architecture
- [x] Documenter les changements apportés pendant la migration
  - [x] Créer un journal des modifications
  - [x] Documenter les décisions techniques importantes
- [x] Créer un guide pour les développeurs sur la nouvelle structure
  - [x] Guide d'onboarding pour les nouveaux développeurs
  - [x] Guide de contribution

### Déploiement
- [x] Déployer la nouvelle structure dans un environnement de test
  - [x] Configurer l'environnement de test
  - [x] Déployer la version migrée
  - [x] Exécuter les tests de smoke
- [x] Vérifier que tout fonctionne correctement dans l'environnement de test
  - [x] Vérifier les logs
  - [x] Vérifier les métriques de performance
- [x] Planifier le déploiement en production
  - [x] Préparer le plan de rollback
  - [x] Communiquer le plan de déploiement à l'équipe

## Phase 6: Suivi et amélioration continue (1-10 mai 2024)

### Surveillance
- [x] Surveiller les performances après la migration
  - [x] Mettre en place des alertes de performance
  - [x] Surveiller les temps de réponse
  - [x] Surveiller l'utilisation des ressources
- [x] Identifier et corriger les problèmes qui pourraient survenir
  - [x] Mettre en place un système de suivi des bugs
  - [x] Prioriser les corrections
- [x] Recueillir les commentaires des utilisateurs
  - [x] Mettre en place un formulaire de feedback
  - [x] Analyser les retours

### Optimisation
- [x] Identifier les opportunités d'optimisation
  - [x] Analyser les performances
  - [x] Identifier les goulots d'étranglement
- [x] Améliorer les performances si nécessaire
  - [x] Optimiser les requêtes de base de données
  - [x] Optimiser le chargement des ressources
- [x] Refactoriser le code si nécessaire
  - [x] Identifier le code dupliqué
  - [x] Appliquer les principes SOLID

### Formation
- [x] Former l'équipe sur la nouvelle structure
  - [x] Organiser des sessions de formation
  - [x] Préparer des tutoriels
- [x] Créer des ressources de formation pour les nouveaux développeurs
  - [x] Créer une documentation d'onboarding
  - [x] Préparer des exemples de code
- [x] Organiser des sessions de questions-réponses pour clarifier les doutes
  - [x] Planifier des réunions régulières
  - [x] Mettre en place un canal de communication dédié

## Notes importantes

- Assurez-vous de toujours travailler sur une branche dédiée à la migration
- Faites des commits fréquents avec des messages clairs
- Documentez toutes les décisions importantes prises pendant la migration
- Communiquez régulièrement avec l'équipe sur l'avancement de la migration
- En cas de problème majeur, n'hésitez pas à revenir à la sauvegarde et à recommencer

## Responsables

- **Chef de projet**: Lucien Naszape
- **Développeur principal**: Lucien Naszape
- **Testeur**: Lucien Naszape
- **Documentaliste**: Lucien Naszape

## Dates clés

- **Début de la migration**: 8 avril 2024
- **Fin prévue**: 30 avril 2024
- **Déploiement en production**: 10 mai 2024
