# Instructions Personnalisées pour l'Agent IA - Guide de Codage

## 1. Architecture Base de Données

### Stack Technique Principal
- ORM: Prisma
- Base de données principale: PostgreSQL
- Cache: Redis
- Message Queue: Apache Kafka

### Spécificités par Microservice
- **Backend**: 
  - Prisma + PostgreSQL pour les données principales
  - Redis pour le caching
- **Analyzer**: 
  - Prisma + PostgreSQL pour les métadonnées
  - MongoDB pour les données d'analyse brutes
- **Decentralized-Storage**: 
  - IPFS pour le stockage des fichiers
  - PostgreSQL pour les métadonnées

## 2. Actions Prioritaires

### Nettoyage et Standardisation
1. Supprimer les configurations Mongoose non utilisées
2. Mettre à jour les schémas Prisma
3. Standardiser les patterns d'accès aux données
4. Mettre à jour la documentation technique

### Migration des Données
1. Créer des scripts de migration Mongoose vers Prisma
2. Valider l'intégrité des données
3. Mettre à jour les tests

### Documentation
1. Mettre à jour les guides d'installation
2. Documenter les nouveaux patterns d'accès aux données
3. Créer des exemples de requêtes Prisma

## 3. Directives de Codage et Bonnes Pratiques

- Respecter une architecture modulaire afin de garantir que les composants puissent être développés, testés et déployés de manière indépendante.
- Séparer clairement le code de tests et le code de production. Utiliser des frameworks de tests appropriés (ex: Jest pour le front-end).
- Appliquer les best practices en matière de sécurité et de performance pour chaque microservice.
- Mettre en place des systèmes de logging et de surveillance pour faciliter le débogage et le suivi des performances en production.

## 4. Spécifications pour Chaque Microservice

### Analyzer
- Doit inclure une charte Helm dédiée.
- Prévoir des configurations spécifiques pour l'analyse des données ou l'exécution de tâches analytiques.

### Decentralized-Storage
- Doit inclure une charte Helm dédiée.
- Gérer l'orchestration des données, la distribution et la sécurité du stockage.

### Frontend
- Suivre les directives de l'UI/UX moderne.
- Intégrer des tests unitaires et d'intégration (voir `src/setupTests.ts`).
- Veiller à une bonne séparation des préoccupations entre la logique de présentation et la gestion d'état.

### Backend
- Doit inclure une charte Helm dédiée.
- Organiser clairement la logique métier, la configuration de la base de données (ex: Prisma) et les contrôleurs API.

## 5. Processus de Déploiement

- Utiliser les charts Helm pour automatiser le déploiement de chaque microservice.
- S'assurer que chaque microservice est testé en isolation et en intégration avant déploiement sur l'environnement cible.

## 6. Collaboration et Documentation

- Mettre à jour régulièrement la documentation interne pour refléter les changements de conception ou d'architecture.
- Garder une trace des configurations, des dépendances et des décisions de conception importantes.

## 7. Finalisation du Développement

- L'agent IA doit s'assurer que toutes les parties du projet sont cohérentes et bien intégrées.
- Réaliser des vérifications de tests automatisées pour chaque microservice.
- Préparer des instructions claires et détaillées pour le déploiement final en production, en mettant l'accent sur la configuration Helm pour chaque service.

## 8. Prochaines Étapes et Priorités

### Frontend (Priorité Haute)
1. Finalisation de l'Interface Utilisateur
   - Implémenter les composants UI/UX modernes restants
   - Optimiser l'expérience utilisateur mobile
   - Compléter l'intégration des animations et transitions

2. Tests et Qualité
   - Compléter la suite de tests E2E avec Cypress
   - Ajouter les tests manquants pour les nouveaux composants
   - Implémenter les tests de performance

### Intégration et Sécurité (Priorité Moyenne)
1. Sécurité
   - Finaliser l'implémentation du `SecurityMonitor`
   - Configurer les alertes de sécurité avancées
   - Mettre en place les tests de pénétration automatisés

2. Monitoring
   - Configurer les dashboards Grafana spécifiques
   - Mettre en place les alertes personnalisées
   - Optimiser la collecte de métriques

### Documentation (Priorité Basse)
1. Guides Utilisateurs
   - Créer les guides d'utilisation finaux
   - Documenter les nouvelles fonctionnalités
   - Préparer les supports de formation

2. Documentation Technique
   - Mettre à jour les diagrammes d'architecture
   - Documenter les dernières modifications de sécurité
   - Finaliser la documentation API

## 9. Critères de Validation Finale

### Performance
- [ ] Temps de chargement initial < 2s
- [ ] Score Lighthouse > 90
- [ ] Optimisation des requêtes API

### Sécurité
- [ ] Tests de pénétration réussis
- [ ] Conformité RGPD vérifiée
- [ ] Monitoring de sécurité opérationnel

### Qualité
- [ ] Couverture de tests > 80%
- [ ] Zéro bug critique
- [ ] Documentation complète

## 10. Stack Technique Finale

### Frontend
- React avec TypeScript
- Vite pour le build
- TailwindCSS pour le styling
- React Query pour la gestion des données

### Backend
- Node.js avec TypeScript
- Prisma ORM
- PostgreSQL
- Redis pour le cache
- Kafka pour les messages

### Infrastructure
- Kubernetes avec Helm
- IPFS pour le stockage décentralisé
- Prometheus/Grafana pour le monitoring
- CI/CD avec GitHub Actions

## 11. Standards de Qualité et Processus de Validation

### Tests Automatisés
- Chaque PR doit inclure :
  - Tests unitaires (Jest)
  - Tests d'intégration
  - Tests E2E (Cypress) pour les changements Frontend
  - Tests de performance avec k6
- Couverture de code minimale : 85% (augmenté de 80%)
- Tests de non-régression obligatoires
- Tests de charge avec Artillery pour les APIs critiques

### Patterns de Test
- Utilisation du pattern AAA (Arrange-Act-Assert)
- Mocks et stubs via Jest/TestDouble
- Fixtures standardisées pour les données de test
- Tests paramétrés pour les cas limites
- Snapshots pour les composants UI

### Revue de Code
- Validation obligatoire par 2 développeurs seniors
- Respect des conventions de nommage
- Vérification des performances
- Analyse de sécurité
- Revue des tests automatisés
- Validation de la documentation technique

### Sécurité
- Scan de dépendances avec Snyk
- Tests de pénétration automatisés
- Validation RGPD
- Audit de sécurité trimestriel
- Analyse statique du code avec SonarQube
- Vérification des secrets avec GitGuardian

### CI/CD
- Pipeline GitHub Actions pour :
  - Build et tests automatisés
  - Analyse statique du code
  - Déploiement automatique sur les environnements de test
  - Validation des charts Helm
  - Tests de charge automatisés
  - Génération de rapports de couverture

## 12. Monitoring et Observabilité

### Métriques Clés
- Temps de réponse API < 200ms
- Disponibilité système > 99.9%
- Taux d'erreur < 0.1%
- Utilisation mémoire < 80%

### Outils de Surveillance
- Prometheus pour la collecte de métriques
- Grafana pour la visualisation
- Jaeger pour le tracing distribué
- ELK Stack pour les logs

### Alerting
- Intégration PagerDuty
- Notifications Slack
- Escalade automatique des incidents
- Rapports hebdomadaires

## 13. Gestion des Environnements

### Développement
- Environnement local avec Docker Compose
- Base de données de test
- Mocks pour les services externes
- Hot-reload activé

### Staging
- Configuration identique à la production
- Données anonymisées
- Tests de charge autorisés
- Validation des migrations

### Production
- Haute disponibilité
- Backup automatique
- Scaling automatique
- Monitoring avancé

## 14. Documentation Requise

### Pour les Développeurs
- Guide d'installation
- Documentation API (OpenAPI/Swagger)
- Guides de contribution
- Procédures de déploiement
- Guides de test et qualité
- Standards de code et bonnes pratiques
- Patterns d'architecture

### Pour les Opérations
- Procédures de backup/restore
- Gestion des incidents
- Configurations Kubernetes
- Métriques et alertes
- Procédures de scaling
- Plans de reprise d'activité
- Monitoring et logging

### Pour les Utilisateurs
- Guides d'utilisation
- FAQ
- Changelog
- Documentation API publique
- Guides de dépannage
- Bonnes pratiques d'utilisation

## 15. Standards de Test

### Organisation des Tests
- Tests unitaires dans `__tests__/unit`
- Tests d'intégration dans `__tests__/integration`
- Tests E2E dans `__tests__/e2e`
- Tests de performance dans `__tests__/performance`

### Conventions de Nommage
- Fichiers de test: `*.spec.ts` ou `*.test.ts`
- Descriptions claires et descriptives
- Nommage des mocks: `__mocks__/`
- Fixtures dans `__fixtures__/`

### Structure des Tests
```typescript
describe('Component/Service Name', () => {
  describe('Functionality/Method', () => {
    it('should behave in specific way', () => {
      // Arrange
      // Act
      // Assert
    });
  });
});
```

### Bonnes Pratiques
- Un seul concept par test
- Tests isolés et indépendants
- Utilisation appropriée des hooks (before/after)
- Mocks explicites et documentés
- Gestion des cas d'erreur
- Tests des cas limites
