#!/bin/bash

# C<PERSON>er le répertoire d'analyse s'il n'existe pas
mkdir -p ts-error-analysis

# Extraire les erreurs par type
echo "Extraction des erreurs par type..."
grep "error TS" typescript-errors.log | cut -d ":" -f2 | cut -d " " -f2 | sort | uniq -c | sort -nr > ts-error-analysis/errors-by-type.txt

# Extraire les fichiers les plus problématiques
echo "Identification des fichiers les plus problématiques..."
grep "error TS" typescript-errors.log | cut -d ":" -f1 | sort | uniq -c | sort -nr > ts-error-analysis/problematic-files.txt

# Extraire les erreurs de syntaxe (TS1xxx)
echo "Extraction des erreurs de syntaxe..."
grep "error TS1" typescript-errors.log > ts-error-analysis/syntax-errors.txt

# Extraire les erreurs de type (TS2xxx)
echo "Extraction des erreurs de type..."
grep "error TS2" typescript-errors.log > ts-error-analysis/type-errors.txt

# Extraire les erreurs dans les fichiers source (hors node_modules)
echo "Extraction des erreurs dans les fichiers source..."
grep -v "node_modules" typescript-errors.log > ts-error-analysis/source-errors.txt

echo "Analyse terminée. Résultats dans le dossier ts-error-analysis/"
