/**
 * Configuration Jest universelle pour les tests cross-platform
 * Cette configuration est conçue pour fonctionner avec React Native et React Web
 */

module.exports = {
  preset: 'react-native',
  // Utilise ts-jest pour la transpilation TypeScript
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', {
      presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
      plugins: [
        ['@babel/plugin-proposal-decorators', { legacy: true }],
        ['@babel/plugin-transform-runtime']
      ]
    }]
  },
  // Configuration pour les tests web
  projects: [
    {
      displayName: 'web',
      testEnvironment: 'jsdom',
      testMatch: ['<rootDir>/packages/web/**/__tests__/**/*.test.[jt]s?(x)', '<rootDir>/packages/ui/**/__tests__/**/*.web.test.[jt]s?(x)'],
      moduleNameMapper: {
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
        '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/tests/mocks/fileMock.js'
      },
      setupFilesAfterEnv: ['<rootDir>/tests/setup/web.setup.js']
    },
    {
      displayName: 'mobile',
      preset: 'react-native',
      testMatch: ['<rootDir>/packages/mobile/**/__tests__/**/*.test.[jt]s?(x)', '<rootDir>/packages/ui/**/__tests__/**/*.native.test.[jt]s?(x)'],
      setupFilesAfterEnv: ['<rootDir>/tests/setup/mobile.setup.js']
    },
    {
      displayName: 'shared',
      testEnvironment: 'node',
      testMatch: [
        '<rootDir>/packages/core/**/__tests__/**/*.test.[jt]s?(x)',
        '<rootDir>/packages/api/**/__tests__/**/*.test.[jt]s?(x)',
        '<rootDir>/packages/state/**/__tests__/**/*.test.[jt]s?(x)',
        '<rootDir>/packages/navigation/**/__tests__/**/*.test.[jt]s?(x)'
      ],
      setupFilesAfterEnv: ['<rootDir>/tests/setup/shared.setup.js']
    }
  ],
  // Configuration commune
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  collectCoverageFrom: [
    'packages/*/src/**/*.{js,jsx,ts,tsx}',
    '!**/node_modules/**',
    '!**/vendor/**',
    '!**/__tests__/**'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  verbose: true,
  testTimeout: 30000,
  transformIgnorePatterns: [
    '/node_modules/(?!(@react-native|react-native|react-navigation|@react-navigation)/)',
  ],
  globals: {
    'ts-jest': {
      isolatedModules: true
    }
  }
};