const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// URLs à auditer
const urls = [
  'https://example.com', // Remplacer par l'URL de votre site déployé
  'https://example.com/profile',
  'https://example.com/settings'
];

// Configuration de Lighthouse
const config = {
  extends: 'lighthouse:default',
  settings: {
    formFactor: 'mobile',
    throttling: {
      cpuSlowdownMultiplier: 4,
      downloadThroughputKbps: 1600,
      uploadThroughputKbps: 750,
      rttMs: 150
    },
    screenEmulation: {
      width: 375,
      height: 667,
      deviceScaleFactor: 2,
      mobile: true
    },
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo']
  }
};

// Créer le dossier de résultats s'il n'existe pas
const resultsDir = path.join(__dirname, 'lighthouse-results');
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir);
}

// Fonction pour exécuter Lighthouse
async function runLighthouse(url) {
  console.log(`Auditing ${url}...`);
  
  // Lancer Chrome
  const chrome = await chromeLauncher.launch({
    chromeFlags: ['--headless', '--disable-gpu', '--no-sandbox']
  });
  
  // Configuration de Lighthouse avec le port Chrome
  const options = {
    logLevel: 'info',
    output: 'html',
    port: chrome.port,
    ...config
  };
  
  // Exécuter Lighthouse
  const results = await lighthouse(url, options);
  
  // Générer le nom de fichier
  const hostname = new URL(url).hostname;
  const pathname = new URL(url).pathname.replace(/\//g, '-') || '-home';
  const dateTime = new Date().toISOString().replace(/:/g, '-');
  const fileName = `${hostname}${pathname}-${dateTime}`;
  
  // Enregistrer le rapport HTML
  const htmlReport = path.join(resultsDir, `${fileName}.html`);
  fs.writeFileSync(htmlReport, results.report);
  
  // Enregistrer le rapport JSON
  const jsonReport = path.join(resultsDir, `${fileName}.json`);
  fs.writeFileSync(jsonReport, JSON.stringify(results.lhr, null, 2));
  
  // Fermer Chrome
  await chrome.kill();
  
  console.log(`Audit completed for ${url}`);
  console.log(`HTML report saved to ${htmlReport}`);
  console.log(`JSON report saved to ${jsonReport}`);
  
  // Ouvrir le rapport HTML
  if (process.platform === 'darwin') {
    exec(`open ${htmlReport}`);
  } else if (process.platform === 'win32') {
    exec(`start ${htmlReport}`);
  } else {
    exec(`xdg-open ${htmlReport}`);
  }
  
  return results.lhr;
}

// Exécuter Lighthouse pour chaque URL
async function runAudits() {
  console.log('Starting Lighthouse audits...');
  
  const results = [];
  
  for (const url of urls) {
    try {
      const result = await runLighthouse(url);
      results.push({
        url,
        performance: result.categories.performance.score * 100,
        accessibility: result.categories.accessibility.score * 100,
        bestPractices: result.categories['best-practices'].score * 100,
        seo: result.categories.seo.score * 100
      });
    } catch (error) {
      console.error(`Error auditing ${url}:`, error);
    }
  }
  
  // Afficher un résumé des résultats
  console.log('\nAudit Summary:');
  console.log('=============');
  
  results.forEach(result => {
    console.log(`\nURL: ${result.url}`);
    console.log(`Performance: ${result.performance.toFixed(1)}`);
    console.log(`Accessibility: ${result.accessibility.toFixed(1)}`);
    console.log(`Best Practices: ${result.bestPractices.toFixed(1)}`);
    console.log(`SEO: ${result.seo.toFixed(1)}`);
  });
}

runAudits().catch(error => {
  console.error('Error running audits:', error);
  process.exit(1);
});
