#!/bin/bash

# Script pour lister les erreurs TypeScript dans le répertoire Backend

# Naviguer vers le répertoire Backend
cd Backend || exit 1

# Exécuter le compilateur TypeScript sans émettre de fichiers,
# en utilisant le tsconfig.json du répertoire courant,
# et rediriger toute la sortie (stdout et stderr) vers un fichier log.
echo "Recherche des erreurs TypeScript dans le répertoire Backend..."
tsc --noEmit --project tsconfig.json > ../typescript_errors.log 2>&1

# Vérifier si le fichier log a été créé et s'il contient des erreurs
if [ -s ../typescript_errors.log ]; then
  echo "Erreurs TypeScript trouvées. Voir le fichier typescript_errors.log à la racine du projet."
else
  echo "Aucune erreur TypeScript trouvée."
  # Supprimer le fichier log s'il est vide
  rm ../typescript_errors.log
fi

# Revenir au répertoire précédent (optionnel)
cd ..

echo "Vérification terminée." 