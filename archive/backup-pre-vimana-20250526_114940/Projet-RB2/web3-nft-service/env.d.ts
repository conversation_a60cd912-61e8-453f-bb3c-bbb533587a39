// Type definitions for environment variables;
interface ImportEnv {
  env?: {
    // Infura project ID;
    VITE_INFURA_PROJECT_ID?: string;
    
    // Environment mode;
    DEV?: boolean;
    
    // NFT addresses;
    VITE_MAINNET_NFT_ADDRESS?: string;
    VITE_MAINNET_MARKETPLACE_ADDRESS?: string;
    VITE_GOERLI_NFT_ADDRESS?: string;
    VITE_GOERLI_MARKETPLACE_ADDRESS?: string;
    VITE_MUMBAI_NFT_ADDRESS?: string;
    VITE_MUMBAI_MARKETPLACE_ADDRESS?: string;
  };
}

// Add the interface to global scope;
declare global {
  const importMeta: ImportEnv;
}

export {};
