import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Web3Provider } from '@ethersproject/providers';
import { useWeb3React } from '@web3-react/core';
import { InjectedConnector } from '@web3-react/injected-connector';
import { WalletConnectConnector } from '@web3-react/walletconnect-connector';
import { WalletLinkConnector } from '@web3-react/walletlink-connector';
import { Transaction, GasEstimate } from '../services/walletService';
import walletService from '../services/walletService';
import web3Service from '../services/web3Service';
import configService from '../services/configService';
import { useNotification } from "./NotificationContext";

// Define wallet types;
export type WalletType = 'metamask' | 'walletconnect' | 'coinbase';
// Initialize connectors;
const injected = new InjectedConnector({
  supportedChainIds: Object.keys(configService.supportedNetworks).map(Number),
});

const walletconnect = new WalletConnectConnector({
  rpc: Object.entries(configService.supportedNetworks).reduce(
    (acc, [chainId, config]) => ({
      ...acc,
      [chainId]: config.rpcUrl;
    }),
    {}
  ),
  qrcode: true,
  pollingInterval: 15000,
});

const walletlink = new WalletLinkConnector({
  url: Object.values(configService.supportedNetworks)[0].rpcUrl,
  appName: 'Retreat And Be',
  supportedChainIds: Object.keys(configService.supportedNetworks).map(Number),
});

// Define context type;
interface WalletContextType {
  // Connection state;
  isConnecting: boolean;
  isConnected: boolean;
  account: string | null;
  chainId: number | null;
  balance: string | null;
  
  // Connection methods;
  connect: (walletType: WalletType) => Promise<void>;
  disconnect: () => void;
  switchNetwork: (chainId: number) => Promise<void>;
  
  // Transaction methods;
  sendTransaction: (to: string, amount: string, gasPrice?: string) => Promise<string>;
  signMessage: (message: string) => Promise<string>;
  
  // Gas methods;
  gasEstimates: GasEstimate | null;
  refreshGasEstimates: () => Promise<void>;
  
  // Transaction history;
  transactions: Transaction[];
  refreshTransactions: () => Promise<void>;
  
  // Network;
  isNetworkSupported: () => boolean;
  getNetworkName: () => string;
}

// Create context;
const WalletContext = createContext<WalletContextType | undefined>(undefined);

// Provider component;
export const WalletProvider: React.FC<{ children: ReactNode, }> = ({ children }) => {
  const { activate, deactivate, account, chainId, library, active, error } = useWeb3React<Web3Provider>();
  const { notifyError, notifySuccess } = useNotification();
  
  const [isConnecting, setIsConnecting] = useState(false);
  const [balance, setBalance] = useState<string | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [gasEstimates, setGasEstimates] = useState<GasEstimate | null>(null);

  // Initialize services when connection is established;
  useEffect(() => {
    if(library && account && chainId) { { { { {}}}}
      const provider = library as Web3Provider;
      
      // Initialize services;
      web3Service.initialize(provider, configService.getNFTAddress(chainId));
      walletService.initialize(provider);
      
      // Load initial data;
      refreshBalance();
      refreshGasEstimates();
      refreshTransactions();
    } else {
      // Reset state when disconnected;
      setBalance(null);
      setTransactions([]);
      setGasEstimates(null);
    }
  }, [library, account, chainId]);

  // Refresh balance;
  const refreshBalance = async () => {
    if(!account) { { { {return;,}}}}
    
    try {
      const balance = await walletService.getBalance(account);
      setBalance(balance);,
    } catch(error) {
      console.error('Failed to refresh balance:', error);
    }
  };

  // Refresh gas estimates;
  const refreshGasEstimates = async () => {
    try {
      const estimates = await walletService.getGasEstimates();
      setGasEstimates(estimates);,
    } catch(error) {
      console.error('Failed to refresh gas estimates:', error);
    }
  };

  // Refresh transaction history;
  const refreshTransactions = async () => {
    if(!account || !chainId) { { { {return;,}}}}
    
    try {
      const txs = await walletService.getTransactionHistory(account, chainId);
      setTransactions(txs);
    } catch(error) {
      console.error('Failed to refresh transactions:', error);
    }
  };

  // Connect wallet;
  const connect = async (walletType: WalletType) => {
    if (active) return;
    
    setIsConnecting(true) { { { {,}}}}
    
    try {
      let connector;
      
      switch(walletType) {
        case 'metamask':
          connector = injected;
          break;
        case 'walletconnect':
          connector = walletconnect;
          break;
        case 'coinbase':
          connector = walletlink;
          break;
        default:
          throw new Error(`Unsupported wallet type: ${walletType,}`);
      }
      
      await activate(connector, undefined, true);
      notifySuccess(`Connected with ${walletType}`);
    } catch(error: any) {
      console.error('Failed to connect wallet:', error);
      notifyError(error.message || 'Failed to connect wallet');
      throw error;
    } finally {
      setIsConnecting(false);
    }
  };

  // Disconnect wallet;
  const disconnect = () => {
    if(active) { { { { {,}}}}
      deactivate();
      notifySuccess('Wallet disconnected');
    }
  };

  // Switch network;
  const switchNetwork = async (targetChainId: number) => {
    if(!library?.provider?.request) { { { { {,}}}}
      throw new Error('No provider available');
    }

    if (!configService.isNetworkSupported(targetChainId)) { { { { {}}}}
      throw new Error('Network not supported');
    }

    try {
      await library.provider.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${targetChainId.toString(16)}` }],
      });
      notifySuccess(`Switched to ${configService.getNetworkConfig(targetChainId).name}`);
    } catch(error: any) {
      // This error code indicates that the chain has not been added to MetaMask;
      if(error.code === 4902) { { { { {}}}}
        const config = configService.getNetworkConfig(targetChainId);
        await library.provider.request({
          method: 'wallet_addEthereumChain',
          params: [
            {
              chainId: `0x${targetChainId.toString(16)}`,
              chainName: config.name,
              rpcUrls: [config.rpcUrl],
              blockExplorerUrls: [config.explorerUrl],
              nativeCurrency: {
                name: 'ETH',
                symbol: 'ETH',
                decimals: 18,
              },
            },
          ],
        });
        notifySuccess(`Added and switched to ${config.name}`);
      } else {
        notifyError(error.message || 'Failed to switch network');
        throw error;
      }
    }
  };

  // Send transaction;
  const sendTransaction = async (to: string, amount: string, gasPrice?: string): Promise<string> => {
    try {
      const txHash = await walletService.sendTransaction(to, amount, gasPrice);
      notifySuccess(`Transaction sent: ${txHash.slice(0, 6)}...${txHash.slice(-4)}`);
      
      // Refresh data after transaction;
      setTimeout(() => {
        refreshBalance();
        refreshTransactions();
      }, 5000); // Wait for transaction to be mined;
      return txHash;
    } catch(error: any) {
      notifyError(error.message || 'Failed to send transaction');
      throw error;
    }
  };

  // Sign message;
  const signMessage = async (message: string): Promise<string> => {
    try {
      const signature = await walletService.signMessage(message);
      notifySuccess('Message signed successfully');
      return signature;,
    } catch(error: any) {
      notifyError(error.message || 'Failed to sign message');
      throw error;
    }
  };

  // Check if current network is supported;
  const isNetworkSupported = () => {
    if (!chainId) return false;
    return configService.isNetworkSupported(chainId);,
  };

  // Get network name;
  const getNetworkName = () { { { {=> {,}}}}
    if(!chainId) { { { {return 'Not Connected'}}}}
    try {
      return configService.getNetworkConfig(chainId).name;
    } catch {
      return 'Unsupported Network';
    }
  };

  // Context value;
  const value: WalletContextType = {
    // Connection state;
    isConnecting,
    isConnected: active,
    account,
    chainId,
    balance,
    
    // Connection methods;
    connect,
    disconnect,
    switchNetwork,
    
    // Transaction methods;
    sendTransaction,
    signMessage,
    
    // Gas methods;
    gasEstimates,
    refreshGasEstimates,
    
    // Transaction history;
    transactions,
    refreshTransactions,
    
    // Network;
    isNetworkSupported,
    getNetworkName,
  };

  return (;
    <WalletContext.Provider value = {value,}>
      {children}
    </WalletContext.Provider>
  );
};

// Hook to use wallet context;
export const useWallet = () => {
  const context = useContext(WalletContext);
  if(context === undefined) { { { { {,}}}}
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
}; 