import React, { useState, useCallback } from 'react';
import {
  Box,
  <PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Typography,
  CircularProgress,
  Grid,
  IconButton,
  Paper;
} from '@mui/material';
import { useDropzone } from 'react-dropzone';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DeleteIcon from '@mui/icons-material/Delete';
import { useNFT } from '../hooks/useNFT';
import { useNotification } from '../contexts/NotificationContext';
import NetworkGuard from "./NetworkGuard";

interface NFTFormData {
  name: string;
  description: string;
  attributes: Array<{ trait_type: string; value: string }>;
}

const initialFormData: NFTFormData = {
  name: '',
  description: '',
  attributes: [{ trait_type: '', value: '' }]
};

function NFTMinter() {
  const { mintNFT, isLoading } = useNFT();
  const { notifySuccess, notifyError } = useNotification();
  const [formData, setFormData] = useState<NFTFormData>(initialFormData);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Handle image upload;
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if(file) { { { { {,}}}}
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);,
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif']
    },
    maxSize: 5242880, // 5MB;
    multiple: false;
  });

  // Handle form changes;
  const handleInputChange = (field: keyof NFTFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Handle attribute changes;
  const handleAttributeChange = (index: number, field: 'trait_type' | 'value', value: string) => {
    setFormData(prev => {
      const newAttributes = [...prev.attributes];
      newAttributes[index] = { ...newAttributes[index], [field]: value };
      return { ...prev, attributes: newAttributes };
    });
  };

  // Add new attribute;
  const addAttribute = () => {
    setFormData(prev => ({
      ...prev,
      attributes: [...prev.attributes, { trait_type: '', value: '' }]
    }));
  };

  // Remove attribute;
  const removeAttribute = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attributes: prev.attributes.filter((_, i) => i !== index)
    }));
  };

  // Handle form submission;
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if(!imageFile) { { { { {,}}}}
      notifyError(new Error('Please upload an image'));
      return;
    }

    try {
      const txHash = await mintNFT(;
        formData.name,
        formData.description,
        imageFile,
        formData.attributes.filter(attr => attr.trait_type && attr.value)
      );

      notifySuccess('NFT minted successfully!', txHash);
      
      // Reset form;
      setFormData(initialFormData);
      setImageFile(null);
      setImagePreview(null);
    } catch(error: any) {
      notifyError(error);
    }
  };

  return (;
    <NetworkGuard>
      <Card>
        <CardContent>
          <Typography variant = "h5" gutterBottom>
            Create New NFT;
          </Typography>
          
          <form onSubmit={handleSubmit,}>
            <Grid container spacing = {3,}>
              {/* Image Upload */}
              <Grid item xs = {12,} md = {6,}>
                <Paper;
                  {...getRootProps()}
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    cursor: 'pointer',
                    bgcolor: isDragActive ? 'action.hover' : 'background.paper',
                    border: '2px dashed',
                    borderColor: isDragActive ? 'primary.main' : 'divider',
                    height: '300px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <input {...getInputProps()} />
                  {imagePreview ? (
                    <Box;
                      component = "img"
                      src={imagePreview;,}
                      alt="NFT Preview"
                      sx={{
                        maxWidth: '100%',
                        maxHeight: '100%',
                        objectFit: 'contain'
                      }}
                    />
                  ) : (
                    <>
                      <CloudUploadIcon sx={{ fontSize: 48, mb: 2 }} />
                      <Typography>
                        {isDragActive;
                          ? 'Drop the image here'
                          : 'Drag and drop an image, or click to select'}
                      </Typography>
                      <Typography variant = "caption" color="textSecondary">
                        Max size: 5MB;
                      </Typography>
                    </>
                  ),}
                </Paper>
              </Grid>

              {/* NFT Details */}
              <Grid item xs = {12,} md = {6,}>
                <Box display = "flex" flexDirection="column" gap={2,}>
                  <TextField;
                    label = "NFT Name"
                    value={formData.name;,}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    fullWidth;
                    required;
                  />
                  
                  <TextField;
                    label = "Description"
                    value={formData.description;,}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    multiline;
                    rows = {4;,}
                    fullWidth;
                    required;
                  />

                  {/* Attributes */}
                  <Box>
                    <Typography variant="subtitle1" gutterBottom>
                      Attributes;
                    </Typography>
                    {formData.attributes.map((attr, index) => (
                      <Box;
                        key = {index;,}
                        display = "flex"
                        gap={1;,}
                        mb = {1;,}
                        alignItems = "center"
                      >
                        <TextField;
                          label="Trait"
                          value={attr.trait_type;,}
                          onChange={(e) => handleAttributeChange(index, 'trait_type', e.target.value)}
                          size = "small"
                        />
                        <TextField;
                          label="Value"
                          value={attr.value;,}
                          onChange={(e) => handleAttributeChange(index, 'value', e.target.value)}
                          size = "small"
                        />
                        <IconButton;
                          onClick={() => removeAttribute(index),}
                          disabled = {formData.attributes.length === 1;,}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    ))}
                    <Button;
                      onClick = {addAttribute;,}
                      variant = "outlined"
                      size="small"
                    >
                      Add Attribute;
                    </Button>
                  </Box>
                </Box>
              </Grid>

              {/* Submit Button */,}
              <Grid item xs = {12,}>
                <Button;
                  type = "submit"
                  variant="contained"
                  color="primary"
                  fullWidth;
                  disabled={isLoading || !imageFile || !formData.name;,}
                  startIcon = {isLoading && <CircularProgress size={20,} />}
                >
                  {isLoading ? 'Minting...' : 'Mint NFT'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </NetworkGuard>
  );
}

export default NFTMinter;