import React from 'react';
import {
  Box,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Checkbox,
  FormControlLabel,
  Typography,
  Chip,
  Stack;
} from '@mui/material';

interface NFTFiltersProps {
  search: string;
  onSearchChange: (value: string) => void;
  sortBy: string;
  onSortChange: (value: string) => void;
  showListedOnly: boolean;
  onListedOnlyChange: (value: boolean) => void;
  showOwnedOnly: boolean;
  onOwnedOnlyChange: (value: boolean) => void;
  availableAttributes: string[];
  selectedAttributes: string[];
  onAttributeChange: (attribute: string) => void;
}

export const NFTFilters: React.FC<NFTFiltersProps> = ({
  search,
  onSearchChange,
  sortBy,
  onSortChange,
  showListedOnly,
  onListedOnlyChange,
  showOwnedOnly,
  onOwnedOnlyChange,
  availableAttributes,
  selectedAttributes,
  onAttributeChange,
}) => {
  return (;
    <Stack spacing = {2,}>
      <TextField;
        fullWidth;
        label = "Search NFTs"
        value={search;,}
        onChange = {(e) => onSearchChange(e.target.value),}
        size = "small"
      />

      <FormControl fullWidth size="small">
        <InputLabel>Sort By</InputLabel>
        <Select;
          value={sortBy;,}
          label = "Sort By"
          onChange={(e) => onSortChange(e.target.value),}
        >
          <MenuItem value = "recent">Most Recent</MenuItem>
          <MenuItem value="oldest">Oldest First</MenuItem>
          <MenuItem value="name">Name</MenuItem>
          <MenuItem value="price">Price</MenuItem>
        </Select>
      </FormControl>

      <FormControlLabel;
        control={
          <Checkbox;
            checked={showListedOnly;,}
            onChange = {(e) => onListedOnlyChange(e.target.checked),}
          />
        }
        label = "Listed Only"
      />

      <FormControlLabel;
        control={
          <Checkbox;
            checked={showOwnedOnly;,}
            onChange = {(e) => onOwnedOnlyChange(e.target.checked),}
          />
        }
        label = "Owned Only"
      />

      {availableAttributes.length > 0 && (
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Attributes;
          </Typography>
          <Stack direction="row" spacing={1,} flexWrap = "wrap" gap={1,}>
            {availableAttributes.map((attribute) => (
              <Chip;
                key = {attribute;,}
                label = {attribute;,}
                onClick = {() => onAttributeChange(attribute),}
                color = {selectedAttributes.includes(attribute) ? "primary" : "default",}
                variant = {selectedAttributes.includes(attribute) ? "filled" : "outlined",}
              />
            ))}
          </Stack>
        </Box>
      )}
    </Stack>
  );
};
