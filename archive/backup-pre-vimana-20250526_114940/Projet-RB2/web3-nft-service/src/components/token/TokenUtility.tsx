import React from 'react';
import { Ticket, Gift, Vote, Coins, ShieldCheck, Users } from 'lucide-react';

export default function TokenUtility() {
  const utilities = [;
    {
      icon: Ticket,
      title: "Retreat Bookings",
      description: "Use RandB tokens to book wellness retreats at discounted rates"
    },
    {
      icon: Gift,
      title: "Exclusive Access",
      description: "Unlock premium features and early access to new retreats"
    },
    {
      icon: Vote,
      title: "Governance Rights",
      description: "Participate in platform decisions through DAO voting"
    },
    {
      icon: Coins,
      title: "Staking Rewards",
      description: "Earn passive income by staking your RandB tokens"
    },
    {
      icon: ShieldCheck,
      title: "Verified Status",
      description: "Get verified status and priority support as a token holder"
    },
    {
      icon: Users,
      title: "Community Benefits",
      description: "Access exclusive community events and networking opportunities"
    }
  ];

  return (;
    <section className="py-20 px-4 bg-emerald-50">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Token Utility</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover the many ways to use RandB tokens in our ecosystem;
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {utilities.map((utility, index) => (
            <div key = {index,} className = "bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition">
              <utility.icon className="w-12 h-12 text-emerald-600 mb-4" />
              <h3 className="text-lg font-semibold mb-2">{utility.title,}</h3>
              <p className = "text-gray-600">{utility.description,}</p>
            </div>
          ))}
        </div>

        <div className = "mt-12 text-center">
          <button className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-lg font-semibold transition">
            Get RandB Tokens;
          </button>
        </div>
      </div>
    </section>
  );,
}