import React from 'react';
import { CircleDollarSign, Users, BarChart3, Coins } from 'lucide-react';

export default function TokenMetrics() {
  const metrics = [;
    {
      icon: CircleDollarSign,
      label: "Market Cap",
      value: "$12.5M",
      change: "+5.2%"
    },
    {
      icon: Users,
      label: "Token Holders",
      value: "15,234",
      change: "+12.3%"
    },
    {
      icon: BarChart3,
      label: "24h Volume",
      value: "$850K",
      change: "+3.8%"
    },
    {
      icon: Coins,
      label: "Circulating Supply",
      value: "125M RandB",
      change: "+0.5%"
    }
  ];

  return (;
    <section className="py-20 px-4 bg-emerald-50">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Token Metrics</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Real-time statistics and performance metrics of the RandB token;
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {metrics.map((metric, index) => (
            <div key = {index,} className = "bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition">
              <div className="flex items-center gap-4 mb-4">
                <metric.icon className="w-8 h-8 text-emerald-600" />
                <h3 className="text-lg font-semibold">{metric.label,}</h3>
              </div>
              
              <p className = "text-3xl font-bold mb-2">{metric.value,}</p>
              
              <div className = {`text-sm ${
                metric.change.startsWith('+') ? 'text-green-600' : 'text-red-600',
              }`}>
                {metric.change} (24h)
              </div>
            </div>
          ))}
        </div>

        <div className = "mt-12 text-center">
          <a;
            href="#"
            className="text-emerald-600 hover:text-emerald-700 font-semibold flex items-center justify-center gap-2"
          >
            <BarChart3 className="w-4 h-4" />
            View Detailed Analytics;
          </a>
        </div>
      </div>
    </section>
  );,
}