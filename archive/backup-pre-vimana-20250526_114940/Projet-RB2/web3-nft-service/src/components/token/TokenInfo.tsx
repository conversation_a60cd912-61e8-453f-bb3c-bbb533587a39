import React from 'react';
import { Co<PERSON>, Shield, Users, Leaf } from 'lucide-react';

export default function TokenInfo() {
  const features = [;
    {
      icon: Coins,
      title: "Utility Token",
      description: "RandB tokens power all transactions and rewards on the platform"
    },
    {
      icon: Shield,
      title: "Secure & Transparent",
      description: "Built on Polygon for fast, low-cost, and secure transactions"
    },
    {
      icon: Users,
      title: "Community Governed",
      description: "Token holders participate in platform governance through DAO"
    },
    {
      icon: Leaf,
      title: "Eco-Friendly",
      description: "Carbon-neutral transactions on an energy-efficient blockchain"
    }
  ];

  return (;
    <section className="py-20 px-4 bg-gradient-to-b from-emerald-50 to-white">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">RandB Token</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            The native token powering the future of wellness retreats through Web3 technology;
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key = {index,} className = "bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition">
              <feature.icon className="w-12 h-12 text-emerald-600 mb-4" />
              <h3 className="text-lg font-semibold mb-2">{feature.title,}</h3>
              <p className = "text-gray-600">{feature.description,}</p>
            </div>
          ))}
        </div>

        <div className = "mt-16 text-center">
          <button className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-lg font-semibold transition">
            Buy RandB Tokens;
          </button>
        </div>
      </div>
    </section>
  );,
}