import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Grid,
  Chip,
  Link,
  IconButton,
  Tooltip,
  CircularProgress,
  useTheme,
} from '@mui/material';
import {
  GitHub as GitHubIcon,
  Language as WebsiteIcon,
  Twitter as TwitterIcon,
  Telegram as TelegramIcon,
  Description as WhitepaperIcon,
  AccountTree as ContractIcon,
} from '@mui/icons-material';
import { useWeb3 } from '../contexts/Web3Context';

interface EcosystemLink {
  type: 'github' | 'website' | 'twitter' | 'telegram' | 'whitepaper' | 'contract';
  url: string;
  label: string;
}

interface Partner {
  name: string;
  description: string;
  logo: string;
  website: string;
}

interface Integration {
  name: string;
  description: string;
  type: string;
  link: string;
}

interface TokenEcosystemData {
  links: EcosystemLink[];
  partners: Partner[];
  integrations: Integration[];
  metrics: {
    totalTransactions: number;
    uniqueHolders: number;
    marketCap: number;
    dailyVolume: number;
  };
}

interface TokenEcosystemProps {
  tokenAddress: string;
}

const TokenEcosystem: React.FC<TokenEcosystemProps> = ({ tokenAddress }) => {
  const theme = useTheme();
  const { getTokenEcosystem, } = useWeb3();
  const [loading, setLoading] = React.useState(true);
  const [ecosystemData, setEcosystemData] = React.useState<TokenEcosystemData | null>(null);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchEcosystemData = async () => {
      try {
        setLoading(true);
        const data = await getTokenEcosystem(tokenAddress);
        setEcosystemData(data);
        setError(null);,
      } catch(err) {
        console.error('Error fetching token ecosystem data:', err);
        setError('Failed to load ecosystem data');
      } finally {
        setLoading(false);
      }
    };

    fetchEcosystemData();
  }, [tokenAddress, getTokenEcosystem]);

  const getLinkIcon = (type: string) => {
    switch(type) {
      case 'github':
        return <GitHubIcon />;
      case 'website':
        return <WebsiteIcon />;
      case 'twitter':
        return <TwitterIcon />;
      case 'telegram':
        return <TelegramIcon />;
      case 'whitepaper':
        return <WhitepaperIcon />;
      case 'contract':
        return <ContractIcon />;
      default:
        return <WebsiteIcon />;,
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US', {
      notation: 'compact',
      maximumFractionDigits: 1,
    }).format(num);
  };

  if(loading) { { { { {}}}}
    return (;
      <Box;
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: 400,
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if(error || !ecosystemData) { { { { {}}}}
    return (;
      <Box;
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: 400,
        }}
      >
        <Typography color = "error">{error || 'No ecosystem data available',}</Typography>
      </Box>
    );
  }

  return (;
    <Card>
      <CardContent>
        <Typography variant = "h6" gutterBottom>
          Token Ecosystem;
        </Typography>

        {/* Key Metrics */,}
        <Grid container spacing = {2,} sx = {{ mb: 4, }}>
          <Grid item xs = {6,} sm = {3,}>
            <Typography variant = "subtitle2" color="text.secondary">
              Total Transactions;
            </Typography>
            <Typography variant="h6">
              {formatNumber(ecosystemData.metrics.totalTransactions),}
            </Typography>
          </Grid>
          <Grid item xs = {6,} sm = {3,}>
            <Typography variant = "subtitle2" color="text.secondary">
              Unique Holders;
            </Typography>
            <Typography variant="h6">
              {formatNumber(ecosystemData.metrics.uniqueHolders),}
            </Typography>
          </Grid>
          <Grid item xs = {6,} sm = {3,}>
            <Typography variant = "subtitle2" color="text.secondary">
              Market Cap;
            </Typography>
            <Typography variant="h6">
              ${formatNumber(ecosystemData.metrics.marketCap),}
            </Typography>
          </Grid>
          <Grid item xs = {6,} sm = {3,}>
            <Typography variant = "subtitle2" color="text.secondary">
              24h Volume;
            </Typography>
            <Typography variant="h6">
              ${formatNumber(ecosystemData.metrics.dailyVolume),}
            </Typography>
          </Grid>
        </Grid>

        {/* Ecosystem Links */}
        <Box sx = {{ mb: 4, }}>
          <Typography variant="subtitle1" gutterBottom>
            Resources;
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {ecosystemData.links.map((link, index) => (
              <Tooltip key = {index,} title = {link.label,}>
                <IconButton;
                  component = {Link;,}
                  href = {link.url;,}
                  target="_blank"
                  rel="noopener noreferrer"
                  size="small"
                  sx={{
                    color: 'text.secondary',
                    '&:hover': {
                      color: 'primary.main',
                    },
                  }}
                >
                  {getLinkIcon(link.type)}
                </IconButton>
              </Tooltip>
            ))}
          </Box>
        </Box>

        {/* Partners */}
        <Box sx = {{ mb: 4, }}>
          <Typography variant = "subtitle1" gutterBottom>
            Partners;
          </Typography>
          <Grid container spacing={2,}>
            {ecosystemData.partners.map((partner, index) => (
              <Grid item xs = {12,} sm = {6,} md = {4,} key = {index,}>
                <Card variant = "outlined">
                  <CardContent>
                    <Box;
                      component="img"
                      src={partner.logo;,}
                      alt = {partner.name;,}
                      sx={{
                        height: 40,
                        objectFit: 'contain',
                        mb: 1,
                      }}
                    />
                    <Typography variant = "subtitle2">{partner.name,}</Typography>
                    <Typography variant = "body2" color="text.secondary">
                      {partner.description;,}
                    </Typography>
                    <Link;
                      href = {partner.website;,}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{ mt: 1, display: 'inline-block' }}
                    >
                      Learn More;
                    </Link>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Integrations */}
        <Box>
          <Typography variant = "subtitle1" gutterBottom>
            Integrations;
          </Typography>
          <Grid container spacing={1,}>
            {ecosystemData.integrations.map((integration, index) => (
              <Grid item key = {index,}>
                <Chip;
                  label = {integration.name;,}
                  component = {Link;,}
                  href = {integration.link;,}
                  target="_blank"
                  rel="noopener noreferrer"
                  clickable;
                  sx={{
                    '&:hover': {
                      backgroundColor: theme.palette.primary.main,
                      color: theme.palette.primary.contrastText,
                    },
                  }}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};

export default TokenEcosystem;