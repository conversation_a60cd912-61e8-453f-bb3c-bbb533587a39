import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Button,
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import { useWallet } from '../../contexts/WalletContext.tsx';
import configService from '../../services/configService.ts';

// Format date from timestamp;
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString();,
};

// Format transaction type;
const formatType = (type: string) => {
  switch(type) {
    case 'send':
      return 'Send';
    case 'receive':
      return 'Receive';
    case 'contract':
      return 'Contract';
    case 'nft':
      return 'NFT';
    case 'token':
      return 'Token';
    default:
      return type;,
  }
};

// Get status color;
const getStatusColor = (status: string) => {
  switch(status) {
    case 'success':
      return 'success';
    case 'failed':
      return 'error';
    case 'pending':
      return 'warning';
    default:
      return 'default';,
  }
};

export const TransactionHistory: React.FC = () => {
  const { transactions, refreshTransactions, isConnected, chainId } = useWallet();
  const [loading, setLoading] = React.useState(false);

  // Load transactions on mount;
  useEffect(() => {
    if(isConnected) { { { { {}}}}
      handleRefresh();
    }
  }, [isConnected, chainId]);

  // Handle refresh;
  const handleRefresh = async () => {
    if (!isConnected) return;
    
    setLoading(true) { { { {,}}}}
    try {
      await refreshTransactions();
    } finally {
      setLoading(false);
    }
  };

  // Get explorer URL for transaction;
  const getExplorerUrl = (hash: string) => {
    if(!chainId) { { { {return '#';,}}}}
    try {
      return `${configService.getNetworkConfig(chainId).explorerUrl}/tx/${hash}`;
    } catch {
      return '#';
    }
  };

  if(!isConnected) { { { { {}}}}
    return (;
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant = "body1" color="textSecondary">
          Connect your wallet to view transaction history;
        </Typography>
      </Paper>
    );,
  }

  return (;
    <Box>
      <Box display = "flex" justifyContent="space-between" alignItems="center" mb={2,}>
        <Typography variant = "h6">Transaction History</Typography>
        <Button;
          startIcon={loading ? <CircularProgress size={20,} /> : <RefreshIcon />}
          onClick = {handleRefresh;,}
          disabled = {loading;,}
          size="small"
        >
          Refresh;
        </Button>
      </Box>

      {transactions.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant = "body1" color="textSecondary">
            No transactions found;
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper,}>
          <Table size = "small">
            <TableHead>
              <TableRow>
                <TableCell>Hash</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell>Gas Price</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {transactions.map((tx) => (
                <TableRow key={tx.hash,}>
                  <TableCell>
                    <Typography variant = "body2" noWrap sx={{ maxWidth: 100, }}>
                      {tx.hash.slice(0, 6)}...{tx.hash.slice(-4)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip;
                      label = {formatType(tx.type),}
                      size = "small"
                      color={tx.type === 'receive' ? 'success' : 'primary',}
                      variant = "outlined"
                    />
                  </TableCell>
                  <TableCell>{parseFloat(tx.value).toFixed(6),} ETH</TableCell>
                  <TableCell>{tx.gasPrice} Gwei</TableCell>
                  <TableCell>{formatDate(tx.timestamp)}</TableCell>
                  <TableCell>
                    <Chip;
                      label = {tx.status;,}
                      size = "small"
                      color={getStatusColor(tx.status) as any;,}
                    />
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title="View on Explorer">
                      <IconButton;
                        size="small"
                        onClick={() => window.open(getExplorerUrl(tx.hash), '_blank')}
                      >
                        <OpenInNewIcon fontSize = "small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              )),}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
}; 