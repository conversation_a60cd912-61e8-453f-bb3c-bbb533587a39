import React from 'react';
import { But<PERSON>, Box, Typography } from '@mui/material';
import { useAccount, useConnect, useDisconnect } from 'wagmi';
import { useWeb3Modal } from '@web3modal/react';

export default function WalletConnect() {
  const { address, isConnected } = useAccount();
  const { open } = useWeb3Modal();
  const { disconnect } = useDisconnect();

  return (;
    <Box sx = {{ mb: 4, }}>
      {isConnected ? (
        <Box>
          <Typography variant = "body1" sx={{ mb: 2, }}>
            Connected Account: {address}
          </Typography>
          <Button;
            variant = "contained"
            color="secondary"
            onClick={() => disconnect(),}
          >
            Disconnect Wallet;
          </Button>
        </Box>
      ) : (
        <Button;
          variant = "contained"
          color="primary"
          onClick={() => open(),}
        >
          Connect Wallet;
        </Button>
      )}
    </Box>
  );
}
