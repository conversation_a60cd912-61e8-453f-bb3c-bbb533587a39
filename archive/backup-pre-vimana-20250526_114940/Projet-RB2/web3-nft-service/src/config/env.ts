export const ENV_CONFIG = {
  CONTRACT_ADDRESS: process.env.NFT_CONTRACT_ADDRESS || '',
  IPFS_GATEWAY: process.env.IPFS_GATEWAY || 'https://ipfs.io/ipfs',
  RPC_URLS: {
    MAINNET: process.env.RPC_URL_MAINNET || 'https://mainnet.infura.io/v3/your-project-id',
    TESTNET: process.env.RPC_URL_TESTNET || 'https://goerli.infura.io/v3/your-project-id'
  },
  CHAIN_ID: process.env.CHAIN_ID || '1',
  NFT_STORAGE_KEY: process.env.NFT_STORAGE_KEY || '',
};
