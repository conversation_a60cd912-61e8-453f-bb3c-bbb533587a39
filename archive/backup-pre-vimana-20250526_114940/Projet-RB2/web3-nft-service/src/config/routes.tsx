import React from 'react';
import { RouteObject } from 'react-router-dom';
import { NFTMarketplace } from '../pages/NFTMarketplace.tsx';
import { NFTDetailsPage } from '../pages/NFTDetailsPage.tsx';
import { NetworkGuard } from '../components/NetworkGuard.tsx';

export const routes: RouteObject[] = [;
  {
    path: '/marketplace',
    element: (
      <NetworkGuard>
        <NFTMarketplace />
      </NetworkGuard>
    )
  },
  {
    path: '/nft/:tokenId',
    element: (
      <NetworkGuard>
        <NFTDetailsPage />
      </NetworkGuard>
    )
  }
];
