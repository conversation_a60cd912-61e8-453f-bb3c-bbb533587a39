import { createConfig, configureChains } from 'wagmi';
import { mainnet, polygon, sepolia } from 'wagmi/chains';
import { publicProvider } from 'wagmi/providers/public';
import { InjectedConnector } from 'wagmi/connectors/injected';
import { WalletConnectConnector } from 'wagmi/connectors/walletConnect';
const { chains, publicClient } = configureChains(
  [mainnet, polygon, sepolia],
  [publicProvider()]
)

export const wagmiConfig = createConfig({
  autoConnect: true,
  connectors: [
    new InjectedConnector({
      chains,
      options: {
        name: 'Injected',
        shimDisconnect: true,
      },
    }),
    new WalletConnectConnector({
      chains,
      options: {
        projectId: 'YOUR_PROJECT_ID', // Remplacez par votre Project ID de WalletConnect;
        qrModalOptions: {
          themeMode: 'light'
        },
      },
    }),
  ],
  publicClient,
})