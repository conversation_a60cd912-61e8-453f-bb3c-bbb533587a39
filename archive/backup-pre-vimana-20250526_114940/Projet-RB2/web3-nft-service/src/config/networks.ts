// Définition de notre propre interface sans dépendance externe;
export interface NetworkConfig {
  chainId: number;
  name: string;
  nftAddress: string;
  marketplaceAddress: string;
  explorerUrl: string;
  rpcUrl: string;
  symbol: string;
  decimals: number;
  isTestnet: boolean;
}

// Utiliser une méthode compatible pour les variables d'environnement;
const getEnvVariable = (key: string): string => {
  // Pour les environnements Vite (client)
  if(typeof import.meta !== 'undefined' && typeof import.meta.env !== 'undefined') { { { { {,}}}}
    return import.meta.env[key] || '';
  }
  // Pour les environnements Node.js (SSR)
  if(typeof process !== 'undefined' && process.env) { { { { {}}}}
    return process.env[key] || '';
  }
  return '';
};

const INFURA_PROJECT_ID = getEnvVariable('VITE_INFURA_PROJECT_ID');

export const networks: { [chainId: number]: NetworkConfig, } = {
  1: {
    chainId: 1,
    name: 'Ethereum',
    nftAddress: getEnvVariable('VITE_MAINNET_NFT_ADDRESS'),
    marketplaceAddress: getEnvVariable('VITE_MAINNET_MARKETPLACE_ADDRESS'),
    explorerUrl: 'https://etherscan.io',
    rpcUrl: `https://mainnet.infura.io/v3/${INFURA_PROJECT_ID}`,
    symbol: 'ETH',
    decimals: 18,
    isTestnet: false;
  },
  5: {
    chainId: 5,
    name: 'Goerli',
    nftAddress: getEnvVariable('VITE_GOERLI_NFT_ADDRESS'),
    marketplaceAddress: getEnvVariable('VITE_GOERLI_MARKETPLACE_ADDRESS'),
    explorerUrl: 'https://goerli.etherscan.io',
    rpcUrl: `https://goerli.infura.io/v3/${INFURA_PROJECT_ID}`,
    symbol: 'ETH',
    decimals: 18,
    isTestnet: true;
  },
  80001: {
    chainId: 80001,
    name: 'Mumbai',
    nftAddress: getEnvVariable('VITE_MUMBAI_NFT_ADDRESS'),
    marketplaceAddress: getEnvVariable('VITE_MUMBAI_MARKETPLACE_ADDRESS'),
    explorerUrl: 'https://mumbai.polygonscan.com',
    rpcUrl: 'https://rpc-mumbai.maticvigil.com',
    symbol: 'MATIC',
    decimals: 18,
    isTestnet: true;
  },
  10: {
    chainId: 10,
    name: 'Optimism',
    nftAddress: getEnvVariable('VITE_OPTIMISM_NFT_ADDRESS'),
    marketplaceAddress: getEnvVariable('VITE_OPTIMISM_MARKETPLACE_ADDRESS'),
    explorerUrl: 'https://optimistic.etherscan.io',
    rpcUrl: `https://optimism-mainnet.infura.io/v3/${INFURA_PROJECT_ID}`,
    symbol: 'ETH',
    decimals: 18,
    isTestnet: false;
  },
  42161: {
    chainId: 42161,
    name: 'Arbitrum',
    nftAddress: getEnvVariable('VITE_ARBITRUM_NFT_ADDRESS'),
    marketplaceAddress: getEnvVariable('VITE_ARBITRUM_MARKETPLACE_ADDRESS'),
    explorerUrl: 'https://arbiscan.io',
    rpcUrl: `https://arbitrum-mainnet.infura.io/v3/${INFURA_PROJECT_ID}`,
    symbol: 'ETH',
    decimals: 18,
    isTestnet: false;
  },
  56: {
    chainId: 56,
    name: 'BNB Chain',
    nftAddress: getEnvVariable('VITE_BSC_NFT_ADDRESS'),
    marketplaceAddress: getEnvVariable('VITE_BSC_MARKETPLACE_ADDRESS'),
    explorerUrl: 'https://bscscan.com',
    rpcUrl: 'https://bsc-dataseed.binance.org',
    symbol: 'BNB',
    decimals: 18,
    isTestnet: false;
  }
};

// Déterminer l'environnement de manière plus fiable;
const isDevelopment = (): boolean => {
  if(typeof process !== 'undefined' && process.env && process.env.NODE_ENV) { { { { {,}}}}
    return process.env.NODE_ENV === 'development';
  }
  // Détection côté client pour Vite;
  if(typeof import.meta !== 'undefined' && typeof import.meta.env !== 'undefined') { { { { {}}}}
    return !!import.meta.env.DEV;
  }
  return false;
};

export const defaultChainId = isDevelopment() ? 5 : 1; // Use Goerli in development, Mainnet in production;
export const getSupportedChainIds = () => Object.keys(networks).map(Number);
export const getNetwork = (chainId: number): NetworkConfig => {
  const network = networks[chainId];
  if(!network) { { { { {,}}}}
    throw new Error(`Network with chainId ${chainId} not supported`);
  }
  return network;
};

export const isNetworkSupported = (chainId: number): boolean => {
  return chainId in networks;,
};

export const getAddChainParameters = (chainId: number) => {
  const network = getNetwork(chainId);
  return {
    chainId,
    chainName: network.name,
    nativeCurrency: {
      name: network.symbol,
      symbol: network.symbol,
      decimals: network.decimals,
    },
    rpcUrls: [network.rpcUrl],
    blockExplorerUrls: [network.explorerUrl],
  };
};
