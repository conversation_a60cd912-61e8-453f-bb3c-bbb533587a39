import { createWeb3Modal } from '@web3modal/wagmi';
import { defaultWagmiConfig } from '@web3modal/wagmi/config';
import { mainnet, polygon } from 'wagmi/chains';
// 1. Définissez vos chaînes;
const chains = [mainnet, polygon];
const projectId = 'VOTRE_PROJECT_ID' // Obtenez-le sur https://cloud.walletconnect.com/;
// 2. Créez la configuration wagmi;
const wagmiConfig = defaultWagmiConfig({
  chains,
  projectId,
  metadata: {
    name: 'Retreat And Be',
    description: 'Your Web3 Retreat Platform',
    url: 'https://retreatandbe.com', // Remplacez par votre URL;
    icons: ['https://retreatandbe.com/icon.png'] // Remplacez par votre icône;
  }
})

// 3. Créez le modal;
const web3Modal = createWeb3Modal({
  wagmiConfig,
  projectId,
  chains;
})

export { wagmiConfig, web3Modal };