export interface Web3Profile {
  id: string;
  address: string;
  name: string;
  avatar: string;
  role: string;
  bio: string;
  tags: string[];
  isConnected: boolean;
  achievementCount: number;
  connectionCount: number;
  nfts: NFTItem[];
}

export interface NFTItem {
  id: string;
  tokenId: string;
  contractAddress: string;
  name: string;
  description: string;
  image: string;
  attributes: NFTAttribute[];
}

export interface NFTAttribute {
  trait_type: string;
  value: string | number;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  image: string;
  earnedAt: string;
  nftTokenId?: string;
}

export interface Connection {
  id: string;
  address: string;
  name: string;
  avatar: string;
  role: string;
  tags: string[];
  isConnected: boolean;
  connectedAt: string;
}

export interface Web3Error extends Error {
  code?: number;
  reason?: string;
}
