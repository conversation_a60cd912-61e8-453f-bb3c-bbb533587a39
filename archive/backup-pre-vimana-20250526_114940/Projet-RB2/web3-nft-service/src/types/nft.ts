export interface NFTAttribute {
  trait_type: string;
  value: string;
}

export interface NFTMetadata {
  tokenId: number;
  name: string;
  description: string;
  image: string;
  attributes?: NFTAttribute[];
  owner: string;
  isListed?: boolean;
  price?: string;
  tokenURI?: string;
  contractAddress?: string;
  chainId?: number;
  lastUpdated?: number;
}

export interface NFTCollection {
  address: string;
  name: string;
  symbol: string;
  totalSupply: number;
  chainId: number;
  nfts: NFTMetadata[];
}

export interface NFTListing {
  tokenId: number;
  seller: string;
  price: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  contractAddress: string;
  chainId: number;
}

export interface NFTTransaction {
  hash: string;
  tokenId: number;
  from: string;
  to: string;
  price?: string;
  timestamp: number;
  type: 'MINT' | 'TRANSFER' | 'SALE' | 'LIST' | 'UNLIST';
  contractAddress: string;
  chainId: number;
}
