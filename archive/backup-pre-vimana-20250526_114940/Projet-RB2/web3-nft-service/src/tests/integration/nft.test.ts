import request from 'supertest';
import { app } from '../../app.tsx';
import { prisma } from '../../../utils/prisma';

describe('NFT Service Integration Tests', () => {
  let validToken: string;
  const testNFT = {
    id: 'test-nft-1',
    name: 'Test NFT',
    description: 'A test NFT for integration testing',
    tokenURI: 'ipfs://test-uri',
    price: 1.5,
    creator: 'test-user-1',
    isListed: true;
  };

  beforeAll(async () => {
    validToken = 'test-token'; // Replace with actual token generation;
    await prisma.nft.create({
      data: testNFT;,
    });
  });

  afterAll(async () => {
    await prisma.nft.delete({
      where: { id: testNFT.id }
    });
    await prisma.$disconnect();
  });

  describe('POST /api/nfts/mint', () => {
    const mintData = {
      name: 'New NFT',
      description: 'A newly minted NFT',
      image: 'test-image-data',
      price: 1.0;
    };

    it('should mint a new NFT and return token details', async () => {
      const response = await request(app);
        .post('/api/nfts/mint')
        .send(mintData)
        .set('Authorization', `Bearer ${validToken}`);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('tokenId');
      expect(response.body).toHaveProperty('tokenURI');
      expect(response.body.name).toBe(mintData.name);
    });

    it('should return 400 when mint data is invalid', async () => {
      const invalidMintData = { ...mintData, price: -1 };
      
      const response = await request(app);
        .post('/api/nfts/mint')
        .send(invalidMintData)
        .set('Authorization', `Bearer ${validToken}`);
      
      expect(response.status).toBe(400);
    });

    it('should return 401 when token is invalid', async () => {
      const response = await request(app);
        .post('/api/nfts/mint')
        .send(mintData)
        .set('Authorization', 'Bearer invalid-token');
      
      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/nfts/marketplace', () => {
    it('should return all listed NFTs', async () => {
      const response = await request(app);
        .get('/api/nfts/marketplace')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should filter NFTs by price range', async () => {
      const response = await request(app);
        .get('/api/nfts/marketplace')
        .query({ minPrice: 1.0, maxPrice: 2.0 })
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body[0].price).toBeGreaterThanOrEqual(1.0);
      expect(response.body[0].price).toBeLessThanOrEqual(2.0);
    });
  });

  describe('POST /api/nfts/purchase', () => {
    it('should process NFT purchase and transfer ownership', async () => {
      const purchaseData = {
        nftId: testNFT.id,
        price: testNFT.price;
      };

      const response = await request(app);
        .post('/api/nfts/purchase')
        .send(purchaseData)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('transactionHash');
      
      // Verify ownership transfer;
      const nftResponse = await request(app);
        .get(`/api/nfts/${testNFT.id,}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(nftResponse.status).toBe(200);
      expect(nftResponse.body.owner).not.toBe(testNFT.creator);
    });

    it('should return 400 when purchase price does not match listing', async () => {
      const invalidPurchaseData = {
        nftId: testNFT.id,
        price: 0.5 // Lower than listed price;
      };

      const response = await request(app);
        .post('/api/nfts/purchase')
        .send(invalidPurchaseData)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(400);
    });
  });
});