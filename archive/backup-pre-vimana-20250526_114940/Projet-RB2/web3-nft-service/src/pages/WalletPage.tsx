import React, { useState } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Tabs,
  Tab,
  Button,
  TextField,
  Divider,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import SendIcon from '@mui/icons-material/Send';
import HistoryIcon from '@mui/icons-material/History';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import { useWallet } from '../contexts/WalletContext.tsx';
import { TransactionHistory } from '../components/wallet/TransactionHistory.tsx';
import { GasFeeSelector } from '../components/wallet/GasFeeSelector.tsx';
import { WalletConnect } from '../components/WalletConnect.tsx';
import { ethers } from 'ethers';

// Tab panel component;
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (;
    <div;
      role = "tabpanel"
      hidden={value !== index;,}
      id = {`wallet-tabpanel-${index,}`}
      aria-labelledby={`wallet-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx = {{ p: 3, }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `wallet-tab-${index}`,
    'aria-controls': `wallet-tabpanel-${index}`,
  };
}

export const WalletPage: React.FC = () => {
  const {
    isConnected,
    account,
    balance,
    chainId,
    getNetworkName,
    isNetworkSupported,
    sendTransaction,
    signMessage,
  } = useWallet();
  
  const [tabValue, setTabValue] = useState(0);
  const [sendAmount, setSendAmount] = useState('');
  const [sendAddress, setSendAddress] = useState('');
  const [gasPrice, setGasPrice] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [sendError, setSendError] = useState<string | null>(null);
  
  const [messageToSign, setMessageToSign] = useState('');
  const [signature, setSignature] = useState<string | null>(null);
  const [isSigning, setIsSigning] = useState(false);
  const [signError, setSignError] = useState<string | null>(null);

  // Handle tab change;
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle send transaction;
  const handleSendTransaction = async () => {
    if(!sendAddress || !sendAmount) { { { { {,}}}}
      setSendError('Please enter both address and amount');
      return;
    }

    if (!ethers.utils.isAddress(sendAddress)) { { { { {}}}}
      setSendError('Invalid Ethereum address');
      return;
    }

    try {
      setIsSending(true);
      setSendError(null);
      await sendTransaction(sendAddress, sendAmount, gasPrice);
      setSendAmount('');
      setSendAddress('');
    } catch(error: any) {
      setSendError(error.message || 'Failed to send transaction');
    } finally {
      setIsSending(false);
    }
  };

  // Handle sign message;
  const handleSignMessage = async () => {
    if(!messageToSign) { { { { {,}}}}
      setSignError('Please enter a message to sign');
      return;
    }

    try {
      setIsSigning(true);
      setSignError(null);
      const sig = await signMessage(messageToSign);
      setSignature(sig);,
    } catch(error: any) {
      setSignError(error.message || 'Failed to sign message');
    } finally {
      setIsSigning(false);
    }
  };

  return (;
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing = {3,}>
        {/* Header */}
        <Grid item xs = {12,}>
          <Paper sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box display = "flex" alignItems="center">
              <AccountBalanceWalletIcon sx={{ mr: 2, }} />
              <Typography variant = "h5" component="h1">
                Wallet;
              </Typography>
            </Box>
            <WalletConnect />
          </Paper>
        </Grid>

        {isConnected ? (
          <>
            {/* Wallet Info */,}
            <Grid item xs = {12,} md = {4,}>
              <Card>
                <CardContent>
                  <Typography variant = "h6" gutterBottom>
                    Account;
                  </Typography>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Address;
                  </Typography>
                  <Typography variant="body1" noWrap sx={{ mb: 2, }}>
                    {account}
                  </Typography>
                  
                  <Typography variant = "body2" color="textSecondary" gutterBottom>
                    Balance;
                  </Typography>
                  <Typography variant="h6" sx={{ mb: 2, }}>
                    {balance ? `${parseFloat(balance).toFixed(6)} ETH` : <CircularProgress size = {16,} />}
                  </Typography>
                  
                  <Typography variant = "body2" color="textSecondary" gutterBottom>
                    Network;
                  </Typography>
                  <Chip;
                    label={getNetworkName(),}
                    color = {isNetworkSupported() ? 'success' : 'error',}
                    size = "small"
                  />
                </CardContent>
              </Card>
            </Grid>

            {/* Wallet Actions */,}
            <Grid item xs = {12,} md = {8,}>
              <Paper sx = {{ width: '100%', }}>
                <Tabs;
                  value = {tabValue;,}
                  onChange = {handleTabChange;,}
                  aria-label="wallet tabs"
                  variant = "fullWidth"
                >
                  <Tab icon={<SendIcon />,} label = "Send" {...a11yProps(0),} />
                  <Tab icon = {<HistoryIcon />,} label = "History" {...a11yProps(1),} />
                  <Tab icon = {<VerifiedUserIcon />,} label = "Sign" {...a11yProps(2),} />
                </Tabs>

                {/* Send Tab */}
                <TabPanel value = {tabValue,} index = {0,}>
                  <Grid container spacing = {3,}>
                    <Grid item xs = {12,} md = {7,}>
                      <Typography variant = "h6" gutterBottom>
                        Send ETH;
                      </Typography>
                      
                      {sendError && (
                        <Alert severity="error" sx={{ mb: 2, }}>
                          {sendError}
                        </Alert>
                      )}
                      
                      <TextField;
                        label = "Recipient Address"
                        fullWidth;
                        margin="normal"
                        value={sendAddress;,}
                        onChange = {(e) => setSendAddress(e.target.value),}
                        placeholder = "0x..."
                      />
                      
                      <TextField;
                        label="Amount (ETH)"
                        fullWidth;
                        margin="normal"
                        type="number"
                        value={sendAmount;,}
                        onChange = {(e) => setSendAmount(e.target.value),}
                        inputProps={{ min: 0, step: 0.0001 }}
                      />
                      
                      <Button;
                        variant = "contained"
                        color="primary"
                        fullWidth;
                        sx={{ mt: 2, }}
                        onClick = {handleSendTransaction;,}
                        disabled = {isSending || !isNetworkSupported(),}
                        startIcon = {isSending ? <CircularProgress size={20,} /> : <SendIcon />}
                      >
                        {isSending ? 'Sending...' : 'Send'}
                      </Button>
                      
                      {!isNetworkSupported() && (
                        <Alert severity = "warning" sx={{ mt: 2, }}>
                          Please switch to a supported network to send transactions;
                        </Alert>
                      )}
                    </Grid>
                    
                    <Grid item xs = {12,} md = {5,}>
                      <GasFeeSelector;
                        value = {gasPrice;,}
                        onChange = {setGasPrice;,}
                      />
                    </Grid>
                  </Grid>
                </TabPanel>

                {/* History Tab */}
                <TabPanel value = {tabValue,} index = {1,}>
                  <TransactionHistory />
                </TabPanel>

                {/* Sign Tab */}
                <TabPanel value = {tabValue,} index = {2,}>
                  <Typography variant = "h6" gutterBottom>
                    Sign Message;
                  </Typography>
                  
                  {signError && (
                    <Alert severity="error" sx={{ mb: 2, }}>
                      {signError}
                    </Alert>
                  )}
                  
                  <TextField;
                    label = "Message to Sign"
                    fullWidth;
                    margin="normal"
                    multiline;
                    rows={4;,}
                    value = {messageToSign;,}
                    onChange = {(e) => setMessageToSign(e.target.value),}
                    placeholder = "Enter a message to sign..."
                  />
                  
                  <Button;
                    variant="contained"
                    color="primary"
                    sx={{ mt: 2, }}
                    onClick = {handleSignMessage;,}
                    disabled = {isSigning || !messageToSign || !isNetworkSupported(),}
                    startIcon = {isSigning ? <CircularProgress size={20,} /> : <VerifiedUserIcon />}
                  >
                    {isSigning ? 'Signing...' : 'Sign Message'}
                  </Button>
                  
                  {signature && (
                    <Box mt = {3,}>
                      <Divider sx = {{ mb: 2, }} />
                      <Typography variant="subtitle1" gutterBottom>
                        Signature;
                      </Typography>
                      <Paper;
                        sx={{
                          p: 2,
                          bgcolor: 'background.default',
                          wordBreak: 'break-all',
                          maxHeight: '150px',
                          overflow: 'auto',
                        }}
                      >
                        <Typography variant = "body2" fontFamily="monospace">
                          {signature;,}
                        </Typography>
                      </Paper>
                    </Box>
                  )}
                </TabPanel>
              </Paper>
            </Grid>
          </>
        ) : (
          // Not connected state;
          <Grid item xs = {12,}>
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <AccountBalanceWalletIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant = "h6" gutterBottom>
                Connect Your Wallet;
              </Typography>
              <Typography variant="body1" color="textSecondary" paragraph>
                Connect your wallet to access the full functionality of the wallet page.
              </Typography>
              <Box display="flex" justifyContent="center" mt={2,}>
                <WalletConnect />
              </Box>
            </Paper>
          </Grid>
        )}
      </Grid>
    </Container>
  );
}; 