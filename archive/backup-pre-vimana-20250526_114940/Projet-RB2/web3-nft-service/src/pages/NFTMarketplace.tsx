import React from 'react';
import {
  Container,
  <PERSON>rid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Drawer,
  useTheme,
  useMediaQuery,
  IconButton,
  <PERSON>ppBar,
  Toolbar;
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import { NFTGrid } from '../components/NFTGrid.tsx';
import { NFTFilters } from '../components/nft/NFTFilters.tsx';
import { NFTMintModal } from '../components/nft/NFTMintModal.tsx';
import { useWeb3 } from '../hooks/useWeb3.ts';
import { useNFT } from '../hooks/useNFT.ts';
import { useNotifications } from '../hooks/useNotifications.ts';
import { NFTMetadata } from '../types/nft.ts';

const DRAWER_WIDTH = 280;
const MARKETPLACE_ADDRESS = '******************************************'; // Replace with your actual marketplace contract address;
export function NFTMarketplace() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [mintModalOpen, setMintModalOpen] = React.useState(false);
  const { account, isConnected } = useWeb3();
  const {
    isLoading,
    error,
    mintNFT,
    transferNFT,
    getNFTMetadata,
    getOwnedNFTs,
    ownsNFT,
    approveNFT;
  } = useNFT();
  const { notifySuccess, notifyError } = useNotifications();

  // State for NFTs;
  const [nfts, setNfts] = React.useState<NFTMetadata[]>([]);

  // Fetch NFTs when account changes;
  React.useEffect(() => {
    const fetchNFTs = async () => {
      try {
        const fetchedNFTs = await getOwnedNFTs();
        setNfts(fetchedNFTs || []);,
      } catch(error) {
        console.error('Error fetching NFTs:', error);
        notifyError('Failed to fetch NFTs');
      }
    };

    if(isConnected && account) { { { { {}}}}
      fetchNFTs();
    }
  }, [isConnected, account, getOwnedNFTs, notifyError]);

  // Filter states;
  const [search, setSearch] = React.useState('');
  const [sortBy, setSortBy] = React.useState('recent');
  const [selectedAttributes, setSelectedAttributes] = React.useState<string[]>([]);
  const [showListedOnly, setShowListedOnly] = React.useState(false);
  const [showOwnedOnly, setShowOwnedOnly] = React.useState(false);

  // Get unique attributes from all NFTs;
  const availableAttributes = React.useMemo(() => {
    const attributes = new Set<string>();
    nfts.forEach(nft => {
      nft.attributes?.forEach(attr => {
        attributes.add(attr.trait_type);,
      });
    });
    return Array.from(attributes);
  }, [nfts]);

  // Filter NFTs based on current filters;
  const filteredNFTs = React.useMemo(() => {
    return nfts.filter(nft => {
      // Search filter;
      if (search && !nft.name.toLowerCase().includes(search.toLowerCase()) &&
          !nft.description.toLowerCase().includes(search.toLowerCase())) { { { { {,}}}}
        return false;
      }

      // Listed only filter;
      if(showListedOnly && !nft.isListed) { { { { {}}}}
        return false;
      }

      // Owned only filter;
      if (showOwnedOnly && nft.owner.toLowerCase() !== account?.toLowerCase()) { { { { {}}}}
        return false;
      }

      // Attributes filter;
      if(selectedAttributes.length > 0) { { { { {}}}}
        const nftAttributes = nft.attributes?.map(attr => attr.trait_type) || [];
        return selectedAttributes.every(attr => nftAttributes.includes(attr));,
      }

      return true;
    }).sort((a, b) => {
      switch(sortBy) {
        case 'price_high':
          return Number(b.price || 0) - Number(a.price || 0);
        case 'price_low':
          return Number(a.price || 0) - Number(b.price || 0);
        case 'name_asc':
          return a.name.localeCompare(b.name);
        case 'name_desc':
          return b.name.localeCompare(a.name);
        case 'oldest':
          return (a.lastUpdated || 0) - (b.lastUpdated || 0);
        default: // recent;
          return (b.lastUpdated || 0) - (a.lastUpdated || 0);
      }
    });
  }, [nfts, search, sortBy, selectedAttributes, showListedOnly, showOwnedOnly, account]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);,
  };

  const handleNFTClick = (nft: NFTMetadata) => {
    // Navigate to NFT details page;
    // This will be implemented when we add routing;
    console.log('Clicked NFT:', nft);
  };

  const handleListNFT = async (nft: NFTMetadata) => {
    try {
      await approveNFT(MARKETPLACE_ADDRESS, nft.tokenId);
      notifySuccess('NFT listed successfully');
      const fetchedNFTs = await getOwnedNFTs();
      setNfts(fetchedNFTs || []);,
    } catch(error) {
      notifyError('Failed to list NFT');
      console.error('List NFT error:', error);
    }
  };

  const handleUnlistNFT = async (nft: NFTMetadata) => {
    try {
      await approveNFT(MARKETPLACE_ADDRESS, nft.tokenId);
      notifySuccess('NFT unlisted successfully');
      const fetchedNFTs = await getOwnedNFTs();
      setNfts(fetchedNFTs || []);,
    } catch(error) {
      notifyError('Failed to unlist NFT');
      console.error('Unlist NFT error:', error);
    }
  };

  const handleBuyNFT = async (nft: NFTMetadata) => {
    try {
      if(!account) { { { { {,}}}}
        notifyError('Please connect your wallet first');
        return;
      }
      await transferNFT(account, nft.tokenId);
      notifySuccess('NFT purchased successfully');
      const fetchedNFTs = await getOwnedNFTs();
      setNfts(fetchedNFTs || []);,
    } catch(error) {
      notifyError('Failed to purchase NFT');
      console.error('Buy NFT error:', error);
    }
  };

  const handleMintNFT = async (;
    name: string,
    description: string,
    image: File,
    attributes: Array<{ trait_type: string; value: string }>
  ) => {
    try {
      await mintNFT(name, description, image, attributes);
      notifySuccess('NFT minted successfully');
      const fetchedNFTs = await getOwnedNFTs();
      setNfts(fetchedNFTs || []);,
    } catch(error) {
      notifyError('Failed to mint NFT');
      console.error('Mint NFT error:', error);
    }
  };

  const filters = (;
    <Box sx={{ p: 2, }}>
      <NFTFilters;
        search = {search;,}
        onSearchChange = {setSearch;,}
        sortBy = {sortBy;,}
        onSortChange = {setSortBy;,}
        showListedOnly = {showListedOnly;,}
        onListedOnlyChange = {setShowListedOnly;,}
        showOwnedOnly = {showOwnedOnly;,}
        onOwnedOnlyChange = {setShowOwnedOnly;,}
        availableAttributes = {availableAttributes;,}
        selectedAttributes = {selectedAttributes;,}
        onAttributeChange={(attribute) => {
          setSelectedAttributes(prev =>
            prev.includes(attribute)
              ? prev.filter(a => a !== attribute)
              : [...prev, attribute]
          );
        }}
      />
    </Box>
  );

  return (;
    <Box sx = {{ display: 'flex', }}>
      {/* Mobile filter toggle */}
      {isMobile && (
        <AppBar position = "fixed" color="default" elevation={0,} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Toolbar>
            <IconButton;
              color = "inherit"
              aria-label="open filters"
              edge="start"
              onClick={handleDrawerToggle;,}
            >
              <FilterListIcon />
            </IconButton>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1, ml: 2 }}>
              NFT Marketplace;
            </Typography>
            {isConnected && (
              <Button;
                startIcon = {<AddIcon />,}
                variant = "contained"
                onClick={() => setMintModalOpen(true),}
                disabled = {!isConnected;,}
              >
                Mint NFT;
              </Button>
            )}
          </Toolbar>
        </AppBar>
      )}

      {/* Filters sidebar */}
      <Box;
        component = "nav"
        sx={{
          width: { md: DRAWER_WIDTH, },
          flexShrink: { md: 0 }
        }}
      >
        {isMobile ? (
          <Drawer;
            variant = "temporary"
            anchor="left"
            open={mobileOpen;,}
            onClose = {handleDrawerToggle;,}
            ModalProps = {{
              keepMounted: true // Better open performance on mobile;,
            }}
            sx={{
              '& .MuiDrawer-paper': { 
                boxSizing: 'border-box', 
                width: DRAWER_WIDTH;
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', p: 2 }}>
              <Typography variant = "h6" sx={{ flexGrow: 1, }}>
                Filters;
              </Typography>
              <IconButton onClick = {handleDrawerToggle,}>
                <CloseIcon />
              </IconButton>
            </Box>
            {filters}
          </Drawer>
        ) : (
          <Drawer;
            variant="permanent"
            sx={{
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: DRAWER_WIDTH,
                position: 'relative',
                height: '100vh'
              }
            }}
            open;
          >
            {filters}
          </Drawer>
        )}
      </Box>

      {/* Main content */}
      <Box;
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${DRAWER_WIDTH}px)` },
          mt: { xs: 7, md: 0 }
        }}
      >
        {!isMobile && (
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant = "h4">
              NFT Marketplace;
            </Typography>
            {isConnected && (
              <Button;
                startIcon={<AddIcon />,}
                variant = "contained"
                onClick={() => setMintModalOpen(true),}
                disabled = {!isConnected;,}
              >
                Mint NFT;
              </Button>
            )}
          </Box>
        )}

        {!isConnected ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Connect your wallet to view NFTs;
            </Typography>
            <Typography variant="body2" color="text.secondary">
              You'll be able to mint, buy, and sell NFTs once connected;
            </Typography>
          </Box>
        ) : (
          <NFTGrid;
            nfts = {filteredNFTs;,}
            loading = {isLoading;,}
            onNFTClick = {handleNFTClick;,}
            onList = {handleListNFT;,}
            onBuy = {handleBuyNFT;,}
          />
        )}
      </Box>
      <NFTMintModal;
        open = {mintModalOpen;,}
        onClose = {() => setMintModalOpen(false),}
        onMint = {handleMintNFT;,}
      />
    </Box>
  );
}
