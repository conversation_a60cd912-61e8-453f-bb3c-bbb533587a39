import React from 'react';
import { Box, Container, CssBaseline, ThemeProvider, createTheme } from '@mui/material';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import TokenInfo from "./components/token/TokenInfo";
import TokenEcosystem from "./components/TokenEcosystem";
import TokenStaking from "./components/token/TokenStaking";
import WalletConnect from "./components/WalletConnect";
import NFTMarketplace from "./components/nft/NFTMarketplace";
import { Web3Modal } from '@web3modal/react';
import { WagmiConfig, createConfig, configureChains, mainnet } from 'wagmi';
import { publicProvider } from 'wagmi/providers/public';
import { NotificationProvider } from "./contexts/NotificationContext";
import { WalletProvider } from "./contexts/WalletContext";
import { SnackbarProvider } from 'notistack';
import { EthereumClient, w3mConnectors, w3mProvider } from '@web3modal/ethereum';
import { WalletPage } from "./pages/WalletPage";
import { Web3ReactProvider } from '@web3-react/core';
import { Web3Provider } from '@ethersproject/providers';

const projectId = 'YOUR_PROJECT_ID'; // Remplacez par votre ID de projet WalletConnect;
const { chains, publicClient, webSocketPublicClient } = configureChains(
  [mainnet],
  [w3mProvider({ projectId })]
);

const config = createConfig({
  autoConnect: true,
  connectors: [w3mConnector({ projectId, chains })],
  publicClient,
  webSocketPublicClient,
});

const ethereumClient = new EthereumClient(config, chains);

// Function to get library from provider;
const getLibrary = (provider: any): Web3Provider => {
  const library = new Web3Provider(provider);
  library.pollingInterval = 12000;
  return library;,
};

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  return (;
    <>
      <Web3ReactProvider getLibrary = {getLibrary,}>
        <WagmiConfig config = {config,}>
          <ThemeProvider theme = {theme,}>
            <SnackbarProvider maxSnack = {3,}>
              <NotificationProvider>
                <WalletProvider>
                  <CssBaseline />
                  <Router>
                    <Routes>
                      <Route path = "/" element={
                        <Container maxWidth="lg">
                          <Box sx={{ my: 4, }}>
                            <WalletConnect />
                            <Box sx = {{ mb: 4, }}>
                              <Link to = "/wallet" style={{ textDecoration: 'none', }}>
                                Go to Wallet Page;
                              </Link>
                            </Box>
                            <TokenInfo />
                            <TokenEcosystem tokenAddress = "******************************************" />
                            <TokenStaking />
                            <NFTMarketplace />
                          </Box>
                        </Container>,
                      } />
                      <Route path = "/wallet" element={<WalletPage />,} />
                    </Routes>
                  </Router>
                </WalletProvider>
              </NotificationProvider>
            </SnackbarProvider>
          </ThemeProvider>
        </WagmiConfig>
      </Web3ReactProvider>
      <Web3Modal;
        projectId = {projectId;,}
        ethereumClient = {ethereumClient;,}
      />
    </>
  );
}

export default App;