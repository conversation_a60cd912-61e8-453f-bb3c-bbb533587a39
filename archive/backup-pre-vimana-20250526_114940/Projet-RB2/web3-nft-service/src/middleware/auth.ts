import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

interface TokenPayload {
  userId: string;
  role: string;
  wallet?: string;
}

declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        wallet?: string;
      };
    }
  }
}

export const verifyToken = async (;
  req: Request,
  res: Response,
  next: NextFunction;
): Promise<void> => {
  try {
    const token = req.headers.authorization?.split(' ')[1];

    if(!token) { { { { {,}}}}
      res.status(401).json({ message: 'No token provided' });
      return;
    }

    const secret = process.env.JWT_SECRET || 'default-secret-key';
    const decoded = jwt.verify(token, secret) as TokenPayload;

    // Add user info to request object with standardized structure;
    req.user = {
      id: decoded.userId,
      role: decoded.role,
      wallet: decoded.wallet;
    };

    next();
  } catch(error) {
    if(error instanceof jwt.JsonWebTokenError) { { { { {}}}}
      res.status(401).json({ message: 'Invalid token' });
      return;
    }
    res.status(500).json({ message: 'Internal server error' });
  }
};