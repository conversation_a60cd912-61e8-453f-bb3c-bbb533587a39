import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';

// Basic rate limiter for general API requests;
export const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes;
  max: 100, // limit each IP to 100 requests per windowMs;
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers;
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers;
  message: { status: 'error', message: 'Too many requests, please try again later' }
});

// Stricter rate limiter for sensitive operations like minting NFTs;
export const mintLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour;
  max: 10, // limit each IP to 10 mint requests per hour;
  standardHeaders: true,
  legacyHeaders: false,
  message: { status: 'error', message: 'Minting rate limit exceeded. Try again later.' }
});

// Rate limiter specifically for collection creation;
export const collectionLimiter = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours;
  max: 5, // limit each IP to 5 collection creations per day;
  standardHeaders: true,
  legacyHeaders: false,
  message: { status: 'error', message: 'Collection creation rate limit exceeded. Try again tomorrow.' }
});

// Authentication rate limiter to prevent brute force attacks;
export const authLimiter = rateLimit({
  windowMs: 30 * 60 * 1000, // 30 minutes;
  max: 20, // limit each IP to 20 authentication attempts per 30 minutes;
  standardHeaders: true,
  legacyHeaders: false,
  message: { status: 'error', message: 'Too many authentication attempts, please try again later' }
});

// Custom rate limiter middleware with configurable options;
export const createRateLimiter = (options: {
  windowMs?: number;
  max?: number;
  message?: string;,
}) => {
  return rateLimit({
    windowMs: options.windowMs || 60 * 1000, // default: 1 minute;
    max: options.max || 30, // default: 30 requests per minute;
    standardHeaders: true,
    legacyHeaders: false,
    message: { 
      status: 'error', 
      message: options.message || 'Rate limit exceeded. Please try again later.' 
    }
  });
};
