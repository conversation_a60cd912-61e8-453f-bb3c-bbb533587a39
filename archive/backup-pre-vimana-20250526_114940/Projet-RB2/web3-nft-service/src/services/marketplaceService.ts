import { Web3Provider } from '@ethersproject/providers';
import { Contract } from '@ethersproject/contracts';
import { parseEther } from '@ethersproject/units';
import { web3Service } from './web3Service';

// ABI for the marketplace contract (this would be replaced with actual ABI)
const MARKETPLACE_ABI = [
  // Example ABI entries
  "function listItem(uint256 tokenId, uint256 price) public",
  "function cancelListing(uint256 tokenId) public",
  "function buyItem(uint256 tokenId) public payable",
  "function getListingPrice(uint256 tokenId) public view returns (uint256)",
  "function isListed(uint256 tokenId) public view returns (bool)",
  "function getListings() public view returns (tuple(uint256 tokenId, address seller, uint256 price)[])"
];

export interface NFTListing {
  tokenId: number;
  seller: string;
  price: string;
  name?: string;
  description?: string;
  image?: string;
}

class MarketplaceService {
  private provider: Web3Provider | null = null;
  private marketplaceContract: Contract | null = null;
  private static instance: MarketplaceService;

  private constructor() {}

  public static getInstance(): MarketplaceService {
    if (!MarketplaceService.instance) {
      MarketplaceService.instance = new MarketplaceService();
    }
    return MarketplaceService.instance;
  }

  public initialize(provider: Web3Provider, marketplaceAddress: string) {
    this.provider = provider;
    this.marketplaceContract = new Contract(
      marketplaceAddress,
      MARKETPLACE_ABI,
      provider.getSigner()
    );
  }

  // List NFT for sale
  public async listNFT(tokenId: number, price: string): Promise<string> {
    if (!this.marketplaceContract) throw new Error('Contract not initialized');
    
    try {
      // First approve the marketplace to handle the NFT
      const marketplaceAddress = this.marketplaceContract.address;
      await web3Service.approveNFT(marketplaceAddress, tokenId);

      // List the NFT
      const priceInWei = parseEther(price);
      const tx = await this.marketplaceContract.listItem(tokenId, priceInWei);
      const receipt = await tx.wait();
      return receipt.transactionHash;
    } catch (error) {
      console.error('Listing error:', error);
      throw error;
    }
  }

  // Cancel NFT listing
  public async cancelListing(tokenId: number): Promise<string> {
    if (!this.marketplaceContract) throw new Error('Contract not initialized');
    
    try {
      const tx = await this.marketplaceContract.cancelListing(tokenId);
      const receipt = await tx.wait();
      return receipt.transactionHash;
    } catch (error) {
      console.error('Cancel listing error:', error);
      throw error;
    }
  }

  // Buy NFT
  public async buyNFT(tokenId: number, price: string): Promise<string> {
    if (!this.marketplaceContract) throw new Error('Contract not initialized');
    
    try {
      const tx = await this.marketplaceContract.buyItem(tokenId, {
        value: parseEther(price)
      });
      const receipt = await tx.wait();
      return receipt.transactionHash;
    } catch (error) {
      console.error('Buy error:', error);
      throw error;
    }
  }

  // Get NFT listing price
  public async getListingPrice(tokenId: number): Promise<string> {
    if (!this.marketplaceContract) throw new Error('Contract not initialized');
    
    try {
      const price = await this.marketplaceContract.getListingPrice(tokenId);
      return price.toString();
    } catch (error) {
      console.error('Get price error:', error);
      throw error;
    }
  }

  // Check if NFT is listed
  public async isNFTListed(tokenId: number): Promise<boolean> {
    if (!this.marketplaceContract) throw new Error('Contract not initialized');
    
    try {
      return await this.marketplaceContract.isListed(tokenId);
    } catch (error) {
      console.error('Check listing error:', error);
      throw error;
    }
  }

  // Get all active listings
  public async getAllListings(): Promise<NFTListing[]> {
    if (!this.marketplaceContract) throw new Error('Contract not initialized');
    
    try {
      const listings = await this.marketplaceContract.getListings();
      return listings.map((listing: any) => ({
        tokenId: listing.tokenId.toNumber(),
        seller: listing.seller,
        price: listing.price.toString()
      }));
    } catch (error) {
      console.error('Get listings error:', error);
      throw error;
    }
  }

  // Get listing details with metadata
  public async getListingDetails(tokenId: number): Promise<NFTListing> {
    if (!this.marketplaceContract) throw new Error('Contract not initialized');
    
    try {
      const price = await this.getListingPrice(tokenId);
      const seller = await web3Service.getNFTOwner(tokenId);
      const metadataURI = await web3Service.getNFTMetadata(tokenId);
      
      // Fetch metadata from URI
      const response = await fetch(metadataURI);
      const metadata = await response.json();

      return {
        tokenId,
        seller,
        price,
        name: metadata.name,
        description: metadata.description,
        image: metadata.image
      };
    } catch (error) {
      console.error('Get listing details error:', error);
      throw error;
    }
  }
}

export default MarketplaceService.getInstance();
