import { Contract } from '@ethersproject/contracts';
import { Web3Provider } from '@ethersproject/providers';
import { getNetwork } from '../config/networks';

const BRIDGE_ABI = [;
  "function bridge(uint256 tokenId, uint256 targetChainId) external payable",
  "function estimateFee(uint256 tokenId, uint256 targetChainId) external view returns (uint256)",
  "function claimToken(uint256 tokenId, bytes memory proof) external"
];

class BridgeService {
  private provider: Web3Provider;
  private bridgeContract: Contract;

  constructor(provider: Web3Provider) {
    this.provider = provider;
    this.bridgeContract = new Contract(
      import.meta.env.VITE_BRIDGE_ADDRESS,
      BRIDGE_ABI,
      provider.getSigner()
    );
  }

  async estimateBridgeFee(tokenId: number, targetChainId: number): Promise<string> {
    return await this.bridgeContract.estimateFee(tokenId, targetChainId);
  }

  async bridgeNFT(tokenId: number, targetChainId: number): Promise<string> {
    const fee = await this.estimateBridgeFee(tokenId, targetChainId);
    const tx = await this.bridgeContract.bridge(tokenId, targetChainId, { value: fee });
    return tx.hash;
  }

  async claimNFT(tokenId: number, proof: string): Promise<string> {
    const tx = await this.bridgeContract.claimToken(tokenId, proof);
    return tx.hash;
  }
}

export default BridgeService;