import { BigNumber } from '@ethersproject/bignumber';
import { Web3Provider } from '@ethersproject/providers';

class GasService {
  private provider: Web3Provider;

  constructor(provider: Web3Provider) {
    this.provider = provider;
  }

  async getOptimizedGasPrice(): Promise<BigNumber> {
    const gasPrice = await this.provider.getGasPrice();
    return gasPrice.mul(110).div(100); // Add 10% buffer;,
  }

  async estimateTransactionGas(transaction: any): Promise<{
    gasLimit: BigNumber;
    gasPrice: BigNumber;
  }> {
    const gasLimit = await this.provider.estimateGas(transaction);
    const gasPrice = await this.getOptimizedGasPrice();

    return {
      gasLimit: gasLimit.mul(120).div(100), // Add 20% buffer;
      gasPrice;
    };
  }

  async getL2GasPrice(chainId: number): Promise<BigNumber> {
    // Specific optimization for L2 chains;
    switch(chainId) {
      case 10: // Optimism;
        return this.getOptimismGasPrice();
      case 42161: // Arbitrum;
        return this.getArbitrumGasPrice();
      default:
        return this.getOptimizedGasPrice();
    }
  }

  private async getOptimismGasPrice(): Promise<BigNumber> {
    // Optimism specific gas price calculation;
    const l1GasPrice = await this.provider.getGasPrice();
    const l2GasPrice = l1GasPrice.div(10); // L2 gas is generally cheaper;
    return l2GasPrice;,
  }

  private async getArbitrumGasPrice(): Promise<BigNumber> {
    // Arbitrum specific gas price calculation;
    const gasPrice = await this.provider.getGasPrice();
    return gasPrice.mul(105).div(100); // Add 5% buffer;,
  }
}

export default GasService;