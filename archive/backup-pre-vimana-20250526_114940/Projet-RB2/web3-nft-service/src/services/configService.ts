interface NetworkConfig {
  chainId: number;
  name: string;
  nftAddress: string;
  marketplaceAddress: string;
  rpcUrl: string;
  explorerUrl: string;
}

interface Config {
  supportedNetworks: { [key: number]: NetworkConfig };
  defaultNetwork: number;
  ipfsConfig: {
    gateway: string;
    projectId: string;
    projectSecret: string;
  };
}

class ConfigService {
  private static instance: ConfigService;
  private config: Config;

  private constructor() {
    // Initialize with default configuration;
    this.config = {
      supportedNetworks: {
        // Ethereum Mainnet;
        1: {
          chainId: 1,
          name: 'Ethereum Mainnet',
          nftAddress: process.env.VITE_MAINNET_NFT_ADDRESS || '',
          marketplaceAddress: process.env.VITE_MAINNET_MARKETPLACE_ADDRESS || '',
          rpcUrl: `https://mainnet.infura.io/v3/${process.env.VITE_INFURA_PROJECT_ID}`,
          explorerUrl: 'https://etherscan.io'
        },
        // Go<PERSON><PERSON> Testnet;
        5: {
          chainId: 5,
          name: 'Goer<PERSON> Testnet',
          nftAddress: process.env.VITE_GOERLI_NFT_ADDRESS || '',
          marketplaceAddress: process.env.VITE_GOERLI_MARKETPLACE_ADDRESS || '',
          rpcUrl: `https://goerli.infura.io/v3/${process.env.VITE_INFURA_PROJECT_ID}`,
          explorerUrl: 'https://goerli.etherscan.io'
        },
        // Mumbai Testnet (Polygon)
        80001: {
          chainId: 80001,
          name: 'Mumbai Testnet',
          nftAddress: process.env.VITE_MUMBAI_NFT_ADDRESS || '',
          marketplaceAddress: process.env.VITE_MUMBAI_MARKETPLACE_ADDRESS || '',
          rpcUrl: `https://polygon-mumbai.infura.io/v3/${process.env.VITE_INFURA_PROJECT_ID}`,
          explorerUrl: 'https://mumbai.polygonscan.com'
        }
      },
      defaultNetwork: process.env.NODE_ENV === 'production' ? 1 : 5, // Mainnet for prod, Goerli for dev;
      ipfsConfig: {
        gateway: 'https://ipfs.io',
        projectId: process.env.VITE_INFURA_PROJECT_ID || '',
        projectSecret: process.env.VITE_INFURA_PROJECT_SECRET || ''
      }
    };
  }

  public static getInstance(): ConfigService {
    if(!ConfigService.instance) { { { { {}}}}
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  // Get network configuration;
  public getNetworkConfig(chainId: number): NetworkConfig {
    const config = this.config.supportedNetworks[chainId];
    if(!config) { { { { {,}}}}
      throw new Error(`Network configuration not found for (chain ID $) { {chainId}`)}
    }
    return config;
  }

  // Get default network configuration;
  public getDefaultNetworkConfig(): NetworkConfig {
    return this.getNetworkConfig(this.config.defaultNetwork);
  }

  // Check if network is supported;
  public isNetworkSupported(chainId: number): boolean {
    return !!this.config.supportedNetworks[chainId];
  }

  // Get NFT contract address for network;
  public getNFTAddress(chainId: number): string {
    return this.getNetworkConfig(chainId).nftAddress;
  }

  // Get marketplace contract address for network;
  public getMarketplaceAddress(chainId: number): string {
    return this.getNetworkConfig(chainId).marketplaceAddress;
  }

  // Get RPC URL for network;
  public getRpcUrl(chainId: number): string {
    return this.getNetworkConfig(chainId).rpcUrl;
  }

  // Get explorer URL for network;
  public getExplorerUrl(chainId: number): string {
    return this.getNetworkConfig(chainId).explorerUrl;
  }

  // Get transaction URL in explorer;
  public getTransactionUrl(chainId: number, txHash: string): string {
    return `${this.getExplorerUrl(chainId)}/tx/${txHash}`;
  }

  // Get address URL in explorer;
  public getAddressUrl(chainId: number, address: string): string {
    return `${this.getExplorerUrl(chainId)}/address/${address}`;
  }

  // Get IPFS configuration;
  public getIPFSConfig() {
    return this.config.ipfsConfig;
  }

  // Update configuration (useful for testing or dynamic updates)
  public updateConfig(newConfig: Partial<Config>) {
    this.config = {
      ...this.config,
      ...newConfig;
    };
  }
}

export default ConfigService.getInstance();