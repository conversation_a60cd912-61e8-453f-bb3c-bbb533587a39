import Web3 from 'web3';
import { AbiItem } from 'web3-utils';
import { Contract } from 'web3-eth-contract';
import env from '../../config/env'; // Corrected path

// Types
export interface NFTMetadata {
  name: string;
  description: string;
  image: string;
  attributes: {
    trait_type: string;
    value: string | number;
  }[];
}

export interface MintParams {
  recipient: string;
  tokenURI: string;
  achievementType: 'meditation' | 'booking' | 'achievement';
  metadata: NFTMetadata;
}

class NFTService {
  private web3: Web3;
  private contract: Contract;
  private contractAddress: string;

  constructor() {
    this.contractAddress = env.CONTRACT_ADDRESS;
    this.web3 = new Web3(env.RPC_URLS.MAINNET);
    this.contract = new this.web3.eth.Contract(
      this.getContractABI() as AbiItem[],
      this.contractAddress
    );
  }

  private getContractABI(): any[] {
    return [
      // Fonctions de base ERC721
      {
        "inputs": [
          {
            "internalType": "address",
            "name": "to",
            "type": "address"
          },
          {
            "internalType": "uint256",
            "name": "tokenId",
            "type": "uint256"
          }
        ],
        "name": "approve",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      },
      {
        "inputs": [
          {
            "internalType": "address",
            "name": "owner",
            "type": "address"
          }
        ],
        "name": "balanceOf",
        "outputs": [
          {
            "internalType": "uint256",
            "name": "",
            "type": "uint256"
          }
        ],
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [
          {
            "internalType": "uint256",
            "name": "tokenId",
            "type": "uint256"
          }
        ],
        "name": "ownerOf",
        "outputs": [
          {
            "internalType": "address",
            "name": "",
            "type": "address"
          }
        ],
        "stateMutability": "view",
        "type": "function"
      },
      // Fonctions personnalisées pour notre NFT
      {
        "inputs": [
          {
            "internalType": "address",
            "name": "to",
            "type": "address"
          },
          {
            "internalType": "string",
            "name": "uri",
            "type": "string"
          }
        ],
        "name": "mint",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      }
    ];
  }

  // Méthodes publiques
  public async mintNFT(params: MintParams): Promise<string> {
    try {
      // Upload metadata to IPFS
      const tokenURI = await this.uploadMetadataToIPFS(params.metadata);

      // Mint NFT
      const receipt = await this.contract.methods
        .mint(params.recipient, tokenURI)
        .send({ from: params.recipient });

      return receipt.transactionHash;
    } catch(error) {
      console.error('Error minting NFT:', error);
      throw error;
    }
  }

  public async mintMeditationNFT(
    recipient: string,
    sessionData: any
  ): Promise<string> {
    const metadata: NFTMetadata = {
      name: `Meditation Session - ${sessionData.title}`,
      description: `Completed meditation session: ${sessionData.title}`,
      image: `${env.IPFS_GATEWAY}/meditation-badge.png`,
      attributes: [
        {
          trait_type: 'Session Type',
          value: sessionData.type
        },
        {
          trait_type: 'Duration',
          value: sessionData.duration
        },
        {
          trait_type: 'Mood Improvement',
          value: sessionData.moodImprovement || 0
        }
      ]
    };

    return this.mintNFT({
      recipient,
      tokenURI: '',
      achievementType: 'meditation',
      metadata
    });
  }

  public async mintBookingNFT(
    recipient: string,
    bookingData: any
  ): Promise<string> {
    const metadata: NFTMetadata = {
      name: `Retreat Booking - ${bookingData.title}`,
      description: `Booked wellness retreat: ${bookingData.title}`,
      image: `${env.IPFS_GATEWAY}/booking-badge.png`,
      attributes: [
        {
          trait_type: 'Retreat Type',
          value: bookingData.type
        },
        {
          trait_type: 'Duration',
          value: bookingData.duration
        },
        {
          trait_type: 'Location',
          value: bookingData.location
        }
      ]
    };

    return this.mintNFT({
      recipient,
      tokenURI: '',
      achievementType: 'booking',
      metadata
    });
  }

  public async mintAchievementNFT(
    recipient: string,
    achievementData: any
  ): Promise<string> {
    const metadata: NFTMetadata = {
      name: `Achievement - ${achievementData.title}`,
      description: `Unlocked achievement: ${achievementData.title}`,
      image: `${env.IPFS_GATEWAY}/achievement-badge.png`,
      attributes: [
        {
          trait_type: 'Achievement Type',
          value: achievementData.type
        },
        {
          trait_type: 'Rarity',
          value: achievementData.rarity
        },
        {
          trait_type: 'Points',
          value: achievementData.points || 0
        }
      ]
    };

    return this.mintNFT({
      recipient,
      tokenURI: '',
      achievementType: 'achievement',
      metadata
    });
  }

  private async uploadMetadataToIPFS(metadata: NFTMetadata): Promise<string> {
    try {
      // Simulation - would use a real IPFS service like Pinata or Infura
      console.log('Uploading metadata to IPFS:', metadata);
      const metadataHash = 'QmXgH1bYZG1YR6sA2EQNkJ9rGgSQ6ohwyCvBNmyASGdDLw';
      return metadataHash;
    } catch(error) {
      console.error('Error uploading to IPFS:', error);
      throw error;
    }
  }

  public async getTokenBalance(address: string): Promise<number> {
    return Number(await this.contract.methods.balanceOf(address).call());
  }

  public async getTokenMetadata(tokenId: number): Promise<NFTMetadata> {
    const tokenURI = await this.contract.methods.tokenURI(tokenId).call();
    const response = await fetch(tokenURI.replace('ipfs://', env.IPFS_GATEWAY));
    return await response.json();
  }

  public async isApprovedForAll(owner: string, operator: string): Promise<boolean> {
    return await this.contract.methods.isApprovedForAll(owner, operator).call();
  }
}

export default new NFTService();