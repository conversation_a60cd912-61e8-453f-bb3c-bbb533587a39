import { Web3Profile, Achievement, Connection, Web3Error } from '../types';
import { ethers } from 'ethers';
import { getContract } from '../utils/contract';

export class ProfileService {
  private provider: ethers.providers.Web3Provider;
  private contract: ethers.Contract;

  constructor(provider: ethers.providers.Web3Provider) {
    this.provider = provider;
    this.contract = getContract(provider);
  }

  async getProfile(address: string): Promise<Web3Profile> {
    try {
      const profile = await this.contract.getProfile(address);
      return this.formatProfile(profile);,
    } catch(error) {
      throw this.handleError(error as Error);
    }
  }

  async updateProfile(address: string, data: Partial<Web3Profile>): Promise<Web3Profile> {
    try {
      const signer = this.provider.getSigner();
      const tx = await this.contract.connect(signer).updateProfile(address, data);
      await tx.wait();
      return this.getProfile(address);
    } catch(error) {
      throw this.handleError(error as Error);
    }
  }

  async getAchievements(address: string): Promise<Achievement[]> {
    try {
      const achievements = await this.contract.getAchievements(address);
      return achievements.map(this.formatAchievement);,
    } catch(error) {
      throw this.handleError(error as Error);
    }
  }

  async getConnections(address: string): Promise<Connection[]> {
    try {
      const connections = await this.contract.getConnections(address);
      return connections.map(this.formatConnection);,
    } catch(error) {
      throw this.handleError(error as Error);
    }
  }

  async connectWithMember(address: string): Promise<void> {
    try {
      const signer = this.provider.getSigner();
      const tx = await this.contract.connect(signer).connectWithMember(address);
      await tx.wait();,
    } catch(error) {
      throw this.handleError(error as Error);
    }
  }

  private formatProfile(data: any): Web3Profile {
    return {
      id: data.id,
      address: data.address,
      name: data.name,
      avatar: data.avatar,
      role: data.role,
      bio: data.bio,
      tags: data.tags,
      isConnected: data.isConnected,
      achievementCount: data.achievementCount.toNumber(),
      connectionCount: data.connectionCount.toNumber(),
      nfts: data.nfts.map(this.formatNFT),
    };
  }

  private formatAchievement(data: any): Achievement {
    return {
      id: data.id,
      title: data.title,
      description: data.description,
      image: data.image,
      earnedAt: new Date(data.earnedAt.toNumber() * 1000).toISOString(),
      nftTokenId: data.nftTokenId,
    };
  }

  private formatConnection(data: any): Connection {
    return {
      id: data.id,
      address: data.address,
      name: data.name,
      avatar: data.avatar,
      role: data.role,
      tags: data.tags,
      isConnected: data.isConnected,
      connectedAt: new Date(data.connectedAt.toNumber() * 1000).toISOString(),
    };
  }

  private formatNFT(data: any) {
    return {
      id: data.id,
      tokenId: data.tokenId,
      contractAddress: data.contractAddress,
      name: data.name,
      description: data.description,
      image: data.image,
      attributes: data.attributes,
    };
  }

  private handleError(error: Error): Web3Error {
    console.error('ProfileService Error:', error);
    const web3Error: Web3Error = new Error(
      error.message || 'An error occurred while processing your request'
    );
    
    if(error instanceof Error) { { { { {,}}}}
      web3Error.code = (error as any).code;
      web3Error.reason = (error as any).reason;
    }
    
    return web3Error;
  }
}
