import winston from 'winston';

const logFormat = winston.format.combine(;
  winston.format.timestamp(),
  winston.format.json()
);

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({
      filename: 'error.log',
      level: 'error'
    }),
    new winston.transports.File({
      filename: 'combined.log'
    })
  ]
});

// Add request context if needed;
export const addRequestContext = (req: any) => {
  return {
    requestId: req.id,
    method: req.method,
    path: req.path,
    ip: req.ip;
  };
};

// Export a default instance;
export default logger;