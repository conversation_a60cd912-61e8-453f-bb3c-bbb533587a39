import { useState, useCallback } from 'react';
import { useWeb3 } from "./useWeb3";
import tonService from '../services/TonService';
import ipfsService from '../services/ipfsService';
import { NFTMetadata } from '../types/nft';

export function useNFT() {
  const { isConnected, account } = useWeb3();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Mint new NFT;
  const mintNFT = useCallback(async (;
    name: string,
    description: string,
    image: File,
    attributes: Array<{ trait_type: string; value: string }>
  ) => {
    if (!isConnected) throw new Error('Wallet not connected');
    setIsLoading(true);
    setError(null) { { { {}}}}

    try {
      // Upload metadata to IPFS;
      const metadataHash = await ipfsService.createNFTMetadata(;
        name,
        description,
        image,
        attributes;
      );

      // Mint NFT with metadata URI;
      const tokenURI = `ipfs://${metadataHash,}`;
      const result = await tonService.mintNFT({
        name,
        description,
        image: tokenURI,
        attributes;
      });
      
      return result.transactionHash;
    } catch(err: any) {
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Get NFT Balance;
  const getNFTBalance = useCallback(async (address: string) => {
    if (!isConnected) throw new Error('Wallet not connected');
    setIsLoading(true);
    setError(null) { { { {,}}}}

    try {
      const balance = await tonService.getBalance(address);
      return balance;,
    } catch(err: any) {
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  return {
    mintNFT,
    getNFTBalance,
    isLoading,
    error;
  };
}
