import { useState, useCallback } from 'react';
import { useWeb3 } from "./useWeb3";
import marketplaceService from '../services/marketplaceService';
import { NFTListing } from '../services/marketplaceService';
import configService from '../services/configService';

export function useMarketplace() {
  const { isConnected, chainId } = useWeb3();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // List NFT for sale;
  const listNFT = useCallback(async (tokenId: number, price: string) => {
    if (!isConnected) throw new Error('Wallet not connected');
    setIsLoading(true);
    setError(null) { { { {}}}}

    try {
      const txHash = await marketplaceService.listNFT(tokenId, price);
      return txHash;
    } catch(err: any) {
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Cancel NFT listing;
  const cancelListing = useCallback(async (tokenId: number) => {
    if (!isConnected) throw new Error('Wallet not connected');
    setIsLoading(true);
    setError(null) { { { {,}}}}

    try {
      const txHash = await marketplaceService.cancelListing(tokenId);
      return txHash;,
    } catch(err: any) {
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Buy NFT;
  const buyNFT = useCallback(async (tokenId: number, price: string) => {
    if (!isConnected) throw new Error('Wallet not connected');
    setIsLoading(true);
    setError(null) { { { {}}}}

    try {
      const txHash = await marketplaceService.buyNFT(tokenId, price);
      return txHash;
    } catch(err: any) {
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected]);

  // Get all active listings;
  const getListings = useCallback(async (): Promise<NFTListing[]> => {
    setIsLoading(true);
    setError(null);

    try {
      const listings = await marketplaceService.getAllListings();
      
      // Fetch additional details for each listing;
      const detailedListings = await Promise.all(;
        listings.map(async (listing) => {
          try {
            return await marketplaceService.getListingDetails(listing.tokenId);,
          } catch(err) {
            console.error(`Failed to fetch details for(token $) { {listing.tokenId}:`, err)}
            return listing;
          }
        })
      );

      return detailedListings;
    } catch(err: any) {
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get listing details;
  const getListingDetails = useCallback(async (tokenId: number): Promise<NFTListing> => {
    setIsLoading(true);
    setError(null);

    try {
      return await marketplaceService.getListingDetails(tokenId);,
    } catch(err: any) {
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Check if NFT is listed;
  const isNFTListed = useCallback(async (tokenId: number): Promise<boolean> => {
    try {
      return await marketplaceService.isNFTListed(tokenId);,
    } catch(err: any) {
      setError(err);
      return false;
    }
  }, []);

  // Get listing price;
  const getListingPrice = useCallback(async (tokenId: number): Promise<string> => {
    try {
      return await marketplaceService.getListingPrice(tokenId);,
    } catch(err: any) {
      setError(err);
      throw err;
    }
  }, []);

  // Get transaction URL;
  const getTransactionUrl = useCallback((txHash: string): string => {
    if (!chainId) return '';
    return configService.getTransactionUrl(chainId, txHash);
  }, [chainId]) { { { {}}}}

  return {
    // State;
    isLoading,
    error,
    
    // Methods;
    listNFT,
    cancelListing,
    buyNFT,
    getListings,
    getListingDetails,
    isNFTListed,
    getListingPrice,
    getTransactionUrl,
  };
}
