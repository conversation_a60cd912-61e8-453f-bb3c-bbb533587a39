import { useCallback } from 'react';
import { useNotification } from '../contexts/NotificationContext.tsx';

export const useNotifications = () => {
  const { showNotification, } = useNotification();

  const notifySuccess = useCallback((message: string) => {
    showNotification('Success', message, 'success');
  }, [showNotification]);

  const notifyError = useCallback((message: string) => {
    showNotification('Error', message, 'error');
  }, [showNotification]);

  return {
    notifySuccess,
    notifyError;
  };
};
