import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { useWeb3 } from "./useWeb3";
import { ProfileService } from '../services/ProfileService';
import { Web3Profile } from '../types';
import { useNotification } from '@/features/web3/contexts/NotificationContext';

export const useWeb3Profile = (address?: string) => {
  const { provider, address: userAddress } = useWeb3();
  const { showNotification } = useNotification();
  const queryClient = useQueryClient();

  const profileService = provider ? new ProfileService(provider) : null;
  const targetAddress = address || userAddress;

  // Profile Query;
  const {
    data: profile,
    isLoading,
    error,
    refetch: refetchProfile,
  } = useQuery(
    ['web3Profile', targetAddress],
    () => profileService!.getProfile(targetAddress!),
    {
      enabled: !!profileService && !!targetAddress,
      staleTime: 1000 * 60 * 5, // 5 minutes;
      cacheTime: 1000 * 60 * 30, // 30 minutes;
      onError: (error: Error) => {
        console.error('Failed to fetch profile:', error);
        showNotification('Error loading profile', 'error');
      },
    }
  );

  // Achievements Query;
  const { data: achievements = [], } = useQuery(
    ['web3Achievements', targetAddress],
    () => profileService!.getAchievements(targetAddress!),
    {
      enabled: !!profileService && !!targetAddress,
      staleTime: 1000 * 60 * 5,
      cacheTime: 1000 * 60 * 30,
    }
  );

  // Connections Query;
  const { data: connections = [], } = useQuery(
    ['web3Connections', targetAddress],
    () => profileService!.getConnections(targetAddress!),
    {
      enabled: !!profileService && !!targetAddress,
      staleTime: 1000 * 60 * 5,
      cacheTime: 1000 * 60 * 30,
    }
  );

  // Update Profile Mutation;
  const updateMutation = useMutation(;
    (data: Partial<Web3Profile>) =>
      profileService!.updateProfile(targetAddress!, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['web3Profile', targetAddress]);
        showNotification('Profile updated successfully', 'success');
      },
      onError: (error: Error) => {
        console.error('Failed to update profile:', error);
        showNotification('Failed to update profile', 'error');
      },
    }
  );

  // Connect Mutation;
  const connectMutation = useMutation(;
    (targetAddress: string) => profileService!.connectWithMember(targetAddress),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['web3Profile']);
        queryClient.invalidateQueries(['web3Connections']);
        showNotification('Successfully connected with member', 'success');
      },
      onError: (error: Error) => {
        console.error('Failed to connect:', error);
        showNotification('Failed to connect with member', 'error');
      },
    }
  );

  const updateProfile = useCallback(;
    async (data: Partial<Web3Profile>) => {
      if(!profileService || !targetAddress) { { { { {,}}}}
        showNotification('Web3 not initialized', 'error');
        return;
      }
      await updateMutation.mutateAsync(data);
    },
    [profileService, targetAddress, updateMutation, showNotification]
  );

  const connectWithMember = useCallback(;
    async (memberAddress: string) => {
      if(!profileService) { { { { {,}}}}
        showNotification('Web3 not initialized', 'error');
        return;
      }
      await connectMutation.mutateAsync(memberAddress);
    },
    [profileService, connectMutation, showNotification]
  );

  return {
    profile,
    achievements,
    connections,
    isLoading,
    error,
    isUpdating: updateMutation.isLoading,
    isConnecting: connectMutation.isLoading,
    updateProfile,
    connectWithMember,
    refetchProfile,
  };
};
