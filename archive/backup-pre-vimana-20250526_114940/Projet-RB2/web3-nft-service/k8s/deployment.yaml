apiVersion: apps/v1
kind: Deployment
metadata:
  name: web3-nft-service
  labels:
    app: web3-nft
spec:
  replicas: 2
  selector:
    matchLabels:
      app: web3-nft
  template:
    metadata:
      labels:
        app: web3-nft
    spec:
      containers:
      - name: web3-nft
        image: web3-nft-service:latest
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: "1"
            memory: 512Mi
          requests:
            cpu: "0.5"
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
