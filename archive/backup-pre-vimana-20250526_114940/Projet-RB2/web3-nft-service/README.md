# Web3 NFT Service

A comprehensive NFT marketplace service for the Retreat And Be platform, enabling users to mint, buy, sell, and manage NFTs across multiple blockchain networks.

## Features

- Multi-network support (Ethereum, Goerli, Mumbai)
- NFT minting with IPFS storage
- NFT marketplace functionality
- Advanced filtering and sorting
- Wallet integration
- Transaction history
- Related NFT recommendations

## Tech Stack

- React 18
- TypeScript
- Web3.js / Ethers.js
- Material-UI v5
- Vite
- IPFS

## Prerequisites

- Node.js >= 16
- NPM >= 8
- MetaMask or other Web3 wallet
- Infura account for IPFS and Ethereum node access

## Environment Setup

1. Copy the environment example file:
   ```bash
   cp .env.example .env
   ```

2. Fill in the required environment variables:
   - Infura project credentials
   - Contract addresses for each network
   - IPFS configuration
   - Feature flags

## Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

3. Build for production:
   ```bash
   npm run build
   ```

## Project Structure

```
src/
├── components/          # Reusable UI components
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── pages/              # Page components
├── services/           # Core services
├── types/              # TypeScript types
├── config/             # Configuration files
└── utils/              # Utility functions
```

## Components

### Core Components
- `NFTCard`: Individual NFT display
- `NFTGrid`: Grid layout for NFT collections
- `NFTDetails`: Detailed NFT view
- `NFTActivity`: Transaction history
- `NFTFilters`: Advanced filtering interface
- `WalletConnect`: Wallet connection management
- `NetworkGuard`: Network protection

### Pages
- `NFTMarketplace`: Main marketplace interface
- `NFTDetailsPage`: Individual NFT view

## Services

### Web3Service
Handles blockchain interactions and smart contract calls.

### IPFSService
Manages decentralized storage for NFT images and metadata.

### MarketplaceService
Handles NFT marketplace operations (listing, buying, etc.).

### ConfigService
Manages network configurations and environment settings.

## Development

### Adding a New Network

1. Add network configuration in `src/config/networks.ts`
2. Add contract addresses to environment variables
3. Update NetworkGuard component if needed

### Adding New Features

1. Create necessary components in `src/components`
2. Add required services in `src/services`
3. Update types in `src/types`
4. Add routes in `src/config/routes.tsx`

## Testing

```bash
# Run unit tests
npm run test

# Run e2e tests
npm run test:e2e
```

## Deployment

1. Set up environment variables for production
2. Build the project:
   ```bash
   npm run build
   ```
3. Deploy the `dist` directory to your hosting service

## Security Considerations

- Never commit sensitive information to version control
- Always validate network connections
- Implement proper error handling
- Use secure IPFS gateways
- Follow Web3 security best practices

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is proprietary and confidential.

## Support

For support, please contact the development team.
