# Infura configuration
VITE_INFURA_PROJECT_ID=your_infura_project_id
VITE_INFURA_PROJECT_SECRET=your_infura_project_secret

# NFT Contract Addresses
VITE_MAINNET_NFT_ADDRESS=0x0000000000000000000000000000000000000000
VITE_GOERLI_NFT_ADDRESS=0x0000000000000000000000000000000000000000
VITE_MUMBAI_NFT_ADDRESS=0x0000000000000000000000000000000000000000

# Marketplace Contract Addresses
VITE_MAINNET_MARKETPLACE_ADDRESS=0x0000000000000000000000000000000000000000
VITE_GOERLI_MARKETPLACE_ADDRESS=0x0000000000000000000000000000000000000000
VITE_MUMBAI_MARKETPLACE_ADDRESS=0x0000000000000000000000000000000000000000

# New Network Contract Addresses
VITE_OPTIMISM_NFT_ADDRESS=0x0000000000000000000000000000000000000000
VITE_OPTIMISM_MARKETPLACE_ADDRESS=0x0000000000000000000000000000000000000000

VITE_ARBITRUM_NFT_ADDRESS=0x0000000000000000000000000000000000000000
VITE_ARBITRUM_MARKETPLACE_ADDRESS=0x0000000000000000000000000000000000000000

VITE_BSC_NFT_ADDRESS=0x0000000000000000000000000000000000000000
VITE_BSC_MARKETPLACE_ADDRESS=0x0000000000000000000000000000000000000000

# Bridge Configuration
VITE_BRIDGE_ADDRESS=0x0000000000000000000000000000000000000000
VITE_BRIDGE_FEE_COLLECTOR=0x0000000000000000000000000000000000000000

# IPFS Configuration (if using Infura IPFS)
VITE_IPFS_PROJECT_ID=your_ipfs_project_id
VITE_IPFS_PROJECT_SECRET=your_ipfs_project_secret
VITE_IPFS_GATEWAY=https://ipfs.infura.io/ipfs/

# Feature Flags
VITE_ENABLE_TESTNET=true
VITE_ENABLE_MUMBAI=true
