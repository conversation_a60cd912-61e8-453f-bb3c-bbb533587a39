#!/usr/bin/env python3

"""
Script pour corriger les problèmes de syntaxe TypeScript courants
dans les fichiers du répertoire mobile.
"""

import os
import re
import sys
import glob
from typing import List, Tuple

MOBILE_DIR = "mobile"
BACKUP_SUFFIX = ".bak"

# Problèmes de syntaxe courants à corriger
FIXES = [
    # Supprimer les points-virgules à la fin des déclarations d'interface et de type
    (r'(export\s+(?:interface|type)\s+[^{;]+)\s*{;', r'\1 {'),
    
    # Corriger les déclarations avec accolades vides multiples et points-virgules
    (r'{\s*{\s*{\s*{\s*{\s*}\s*}\s*}\s*}\s*}', r'{ return null; }'),
    
    # Corriger les accolades de fermeture avec points-virgules
    (r'}\);', r'})'),
    
    # Corriger les parenthèses et accolades mal formées
    (r'(\w+)\(\);', r'\1()'),
    (r'Date\.now\(\)\(\)', r'Date.now()'),
    
    # Corriger les types de retour mal formatés
    (r'(\w+)\s*\[\]\s*{', r'\1[] {'),
    
    # Corriger les problèmes dans les constructions if
    (r'if\s*\(\s*([^)]+?)\s*\)\s*{\s*{\s*{\s*{\s*{\s*}\s*}\s*}\s*}\s*}', r'if (\1) { return null; }'),
]

def find_typescript_files(directory: str) -> List[str]:
    """Trouver tous les fichiers TypeScript dans le répertoire spécifié."""
    pattern = os.path.join(directory, "**", "*.ts")
    ts_files = glob.glob(pattern, recursive=True)
    
    pattern = os.path.join(directory, "**", "*.tsx")
    tsx_files = glob.glob(pattern, recursive=True)
    
    return ts_files + tsx_files

def backup_file(file_path: str) -> str:
    """Créer une copie de sauvegarde du fichier."""
    backup_path = f"{file_path}{BACKUP_SUFFIX}"
    with open(file_path, 'r', encoding='utf-8') as source:
        with open(backup_path, 'w', encoding='utf-8') as target:
            target.write(source.read())
    return backup_path

def apply_fixes(file_path: str, patterns: List[Tuple[str, str]]) -> bool:
    """Appliquer les corrections au fichier."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        modified = False
        for pattern, replacement in patterns:
            new_content, count = re.subn(pattern, replacement, content)
            if count > 0:
                content = new_content
                modified = True
        
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
        return modified
    except Exception as e:
        print(f"Erreur lors de la correction de {file_path}: {e}")
        return False

def main():
    """Fonction principale du script."""
    if not os.path.isdir(MOBILE_DIR):
        print(f"Répertoire '{MOBILE_DIR}' non trouvé.")
        sys.exit(1)
    
    ts_files = find_typescript_files(MOBILE_DIR)
    print(f"Trouvé {len(ts_files)} fichiers TypeScript dans '{MOBILE_DIR}'.")
    
    stats = {"fixed": 0, "unchanged": 0, "error": 0}
    fixed_files = []
    
    for file_path in ts_files:
        try:
            backup_path = backup_file(file_path)
            if apply_fixes(file_path, FIXES):
                stats["fixed"] += 1
                fixed_files.append(file_path)
                print(f"✅ Corrigé: {file_path}")
            else:
                stats["unchanged"] += 1
                # Supprimer la sauvegarde si aucune modification n'a été apportée
                os.remove(backup_path)
        except Exception as e:
            stats["error"] += 1
            print(f"❌ Erreur pour {file_path}: {e}")
    
    print("\nRésumé des opérations:")
    print(f"- {stats['fixed']} fichiers corrigés")
    print(f"- {stats['unchanged']} fichiers inchangés")
    print(f"- {stats['error']} fichiers avec erreurs")
    
    if fixed_files:
        print("\nFichiers corrigés:")
        for file in fixed_files:
            print(f"- {file}")

if __name__ == "__main__":
    main() 