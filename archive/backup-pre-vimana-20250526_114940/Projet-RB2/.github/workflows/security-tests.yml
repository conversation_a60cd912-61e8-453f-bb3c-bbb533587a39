name: Security Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 0 * * 0'  # Exécuter tous les dimanches à minuit

jobs:
  security-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        cd Backend && npm ci
        cd ../frontend && npm ci
    
    - name: Install security tools
      run: |
        npm install -g eslint eslint-plugin-security
        
        # Installer Trivy pour l'analyse des conteneurs
        curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
        
        # Installer git-secrets pour la détection de secrets
        git clone https://github.com/awslabs/git-secrets.git
        cd git-secrets
        sudo make install
        cd ..
        rm -rf git-secrets
    
    - name: Run security tests
      run: |
        mkdir -p security-reports
        bash scripts/security/run-security-tests.sh
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: security-reports/
    
    - name: Check for critical vulnerabilities
      run: |
        # Vérifier les vulnérabilités critiques dans les rapports
        if grep -q '"severity":"critical"' security-reports/npm-audit-report.json; then
          echo "Critical vulnerabilities found in dependencies!"
          exit 1
        fi
        
        if [ -f security-reports/trivy-report.json ] && grep -q '"Severity":"CRITICAL"' security-reports/trivy-report.json; then
          echo "Critical vulnerabilities found in container images!"
          exit 1
        fi
        
        # Vérifier les échecs dans le rapport de configuration de sécurité
        if grep -q '"status":"failed"' security-reports/security-config-report.json; then
          echo "Security configuration checks failed!"
          exit 1
        fi
    
    - name: Notify security team
      if: failure()
      uses: actions/github-script@v6
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const { repo, owner } = context.repo;
          const run_id = context.runId;
          const run_url = `https://github.com/${owner}/${repo}/actions/runs/${run_id}`;
          
          github.rest.issues.create({
            owner,
            repo,
            title: `🚨 Security tests failed in ${context.workflow}`,
            body: `Security tests have failed in [workflow run](${run_url}).\n\nPlease check the security reports for details.`,
            labels: ['security', 'bug']
          });
