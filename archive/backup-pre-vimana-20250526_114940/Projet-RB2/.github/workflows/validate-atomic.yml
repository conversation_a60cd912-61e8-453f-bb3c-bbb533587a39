name: Validate Atomic Design

on:
  pull_request:
    paths:
      - 'frontend/src/atomic/**'

jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
      
      - name: Install dependencies
        run: cd frontend && npm install
      
      - name: Validate Atomic components
        run: cd frontend && node src/atomic/utils/ci-validate.js
      
      - name: Generate and attach demo
        if: success()
        run: |
          cd frontend
          node src/atomic/utils/generate-demo.js
          echo "### Demo Success ✅" >> $GITHUB_STEP_SUMMARY
          echo "Une démo des composants a été générée et peut être visualisée en lançant l'application." >> $GITHUB_STEP_SUMMARY
      
      - name: Report failures
        if: failure()
        run: |
          echo "### Validation Failure ❌" >> $GITHUB_STEP_SUMMARY
          echo "Certains composants ne respectent pas les standards Atomic Design. Veuillez exécuter \`npm run atomic:audit:fix\` pour corriger les problèmes." >> $GITHUB_STEP_SUMMARY 