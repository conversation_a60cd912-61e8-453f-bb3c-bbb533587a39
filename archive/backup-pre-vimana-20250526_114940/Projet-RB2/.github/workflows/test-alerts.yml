name: Test Alerts

on:
  workflow_run:
    workflows: ["Tests"]
    types:
      - completed

jobs:
  alert:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'failure' }}
    
    steps:
    - name: Get workflow run information
      uses: actions/github-script@v6
      id: workflow-run
      with:
        script: |
          const run = await github.rest.actions.getWorkflowRun({
            owner: context.repo.owner,
            repo: context.repo.repo,
            run_id: ${{ github.event.workflow_run.id }}
          });
          return run.data;
        result-encoding: string
    
    - name: Send Slack notification
      uses: slackapi/slack-github-action@v1.23.0
      with:
        payload: |
          {
            "text": "❌ Tests failed in ${{ github.repository }}",
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "❌ Tests failed in ${{ github.repository }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Branch:* ${{ from<PERSON>son(steps.workflow-run.outputs.result).head_branch }}\n*Commit:* ${{ from<PERSON>son(steps.workflow-run.outputs.result).head_sha }}\n*Triggered by:* ${{ fromJson(steps.workflow-run.outputs.result).actor.login }}"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*<${{ github.event.workflow_run.html_url }}|View workflow run>*"
                }
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
    
    - name: Send email notification
      uses: dawidd6/action-send-mail@v3
      with:
        server_address: ${{ secrets.MAIL_SERVER }}
        server_port: ${{ secrets.MAIL_PORT }}
        username: ${{ secrets.MAIL_USERNAME }}
        password: ${{ secrets.MAIL_PASSWORD }}
        subject: "❌ Tests failed in ${{ github.repository }}"
        body: |
          Tests have failed in ${{ github.repository }}
          
          Branch: ${{ fromJson(steps.workflow-run.outputs.result).head_branch }}
          Commit: ${{ fromJson(steps.workflow-run.outputs.result).head_sha }}
          Triggered by: ${{ fromJson(steps.workflow-run.outputs.result).actor.login }}
          
          View workflow run: ${{ github.event.workflow_run.html_url }}
        to: ${{ secrets.NOTIFICATION_EMAIL }}
        from: GitHub Actions
