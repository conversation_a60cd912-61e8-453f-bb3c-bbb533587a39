name: TypeScript Validation

on:
  push:
    branches: [ main, develop ]
    paths:
      - '**/*.ts'
      - '**/*.tsx'
      - '**/tsconfig*.json'
      - '.github/workflows/typescript-check.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - '**/*.ts'
      - '**/*.tsx'
      - '**/tsconfig*.json'
  workflow_dispatch:
    inputs:
      verboseOutput:
        description: 'Enable verbose output'
        required: false
        default: 'false'
        type: choice
        options:
          - 'true'
          - 'false'

jobs:
  typescript-check:
    name: Check TypeScript
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: TypeScript validation (frontend)
        working-directory: ./frontend
        run: |
          echo "Running TypeScript check for frontend..."
          npx tsc --noEmit ${{ github.event.inputs.verboseOutput == 'true' && '--diagnostics --verbose' || '' }}
      
      - name: TypeScript validation (packages)
        working-directory: ./packages
        run: |
          echo "Running TypeScript check for packages..."
          for dir in */; do
            if [ -f "${dir}tsconfig.json" ]; then
              echo "Checking TypeScript in ${dir}..."
              cd "$dir"
              npx tsc --noEmit ${{ github.event.inputs.verboseOutput == 'true' && '--diagnostics --verbose' || '' }}
              cd ..
            fi
          done
        
      - name: Count TypeScript errors
        id: count-errors
        run: |
          # Search for TypeScript error files generated during build
          ERROR_COUNT=$(find . -name "ts_fix_report_*.html" | wc -l)
          echo "Found $ERROR_COUNT TypeScript error reports"
          echo "error_count=$ERROR_COUNT" >> $GITHUB_OUTPUT
          
          # If error files exist, save them as artifacts
          if [ "$ERROR_COUNT" -gt 0 ]; then
            mkdir -p ts-error-reports
            cp $(find . -name "ts_fix_report_*.html") ts-error-reports/
          fi
          
      - name: Upload error reports
        if: steps.count-errors.outputs.error_count != '0'
        uses: actions/upload-artifact@v3
        with:
          name: typescript-error-reports
          path: ts-error-reports/
          
      - name: Check for TypeScript errors
        run: |
          if [ "${{ steps.count-errors.outputs.error_count }}" != "0" ]; then
            echo "::error::Found TypeScript errors, check the artifacts for details"
            exit 1
          fi 