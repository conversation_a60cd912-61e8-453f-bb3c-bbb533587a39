name: Tests

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  node-tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install additional dependencies
      run: npm install --save-dev glob

    - name: Run security tests
      run: node --expose-gc run-tests.js "Backend/src/tests/services/security/**/*.test.ts" minimal-jest.config.js

    - name: Run integration tests
      run: node --expose-gc run-tests.js "Backend/src/tests/integration/**/*.test.ts" minimal-jest.config.js

    - name: Run frontend security tests
      run: node --expose-gc run-tests.js "frontend/src/tests/security/**/*.test.ts" minimal-jest.config.js

  python-tests:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio

    - name: Run unit tests
      run: |
        python run_tests.py --unit --coverage

    - name: Run integration tests
      run: |
        python run_tests.py --integration

    - name: Run i18n tests
      run: |
        python run_tests.py --i18n

    - name: Upload coverage report
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: true
