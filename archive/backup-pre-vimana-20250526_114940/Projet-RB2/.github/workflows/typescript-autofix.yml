name: TypeScript Auto-fix

on:
  workflow_dispatch:
    inputs:
      createPullRequest:
        description: 'Create a PR with fixes'
        required: true
        default: 'true'
        type: choice
        options:
          - 'true'
          - 'false'
  schedule:
    # Exécution tous les lundis à 3h du matin
    - cron: '0 3 * * 1'

jobs:
  autofix:
    name: Autofix TypeScript Errors
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
        
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run TypeScript auto-fix scripts
        run: |
          echo "Running TypeScript auto-fix scripts..."
          
          # Vérifier si les scripts existent et les exécuter
          if [ -f "fix-ts-all.py" ]; then
            echo "Running fix-ts-all.py..."
            python3 fix-ts-all.py
          fi
          
          if [ -f "frontend/scripts/fix-typescript-errors.sh" ]; then
            echo "Running fix-typescript-errors.sh..."
            chmod +x frontend/scripts/fix-typescript-errors.sh
            ./frontend/scripts/fix-typescript-errors.sh
          fi
          
          if [ -f "fix-jsx-issues.sh" ]; then
            echo "Running fix-jsx-issues.sh..."
            chmod +x fix-jsx-issues.sh
            ./fix-jsx-issues.sh
          fi
          
          # Générer un rapport des corrections effectuées
          date_time=$(date +"%Y%m%d_%H%M%S")
          report_file="ts_autofix_report_${date_time}.html"
          
          echo "<html><head><title>TypeScript Autofix Report</title>" > $report_file
          echo "<style>body{font-family:Arial,sans-serif;max-width:960px;margin:0 auto;padding:20px;} 
                h1{color:#333;} .fixed{color:green;} .error{color:red;}</style>" >> $report_file
          echo "</head><body>" >> $report_file
          echo "<h1>TypeScript Autofix Report</h1>" >> $report_file
          echo "<p>Generated on: $(date)</p>" >> $report_file
          echo "<h2>Files Modified:</h2><ul>" >> $report_file
          
          # Liste les fichiers modifiés
          git diff --name-only | grep -E "\.tsx?$" | while read file; do
            echo "<li>$file</li>" >> $report_file
          done
          
          echo "</ul>" >> $report_file
          echo "</body></html>" >> $report_file
      
      - name: Upload fix report
        uses: actions/upload-artifact@v3
        with:
          name: typescript-autofix-report
          path: ts_autofix_report_*.html
          
      - name: Check for changes
        id: check-changes
        run: |
          git status
          
          # Vérifier si des fichiers TypeScript ont été modifiés
          CHANGED_TS_FILES=$(git diff --name-only | grep -E "\.tsx?$" | wc -l)
          echo "Changed TypeScript files: $CHANGED_TS_FILES"
          echo "has_changes=$CHANGED_TS_FILES" >> $GITHUB_OUTPUT
      
      - name: Create Pull Request
        if: ${{ steps.check-changes.outputs.has_changes != '0' && (github.event.inputs.createPullRequest == 'true' || github.event_name == 'schedule') }}
        uses: peter-evans/create-pull-request@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "fix: auto-correct TypeScript errors"
          title: "[Automated] Fix TypeScript errors"
          body: |
            Corrections automatiques des erreurs TypeScript.
            
            Ce PR a été généré automatiquement par GitHub Actions pour corriger les erreurs TypeScript détectées dans le code.
            
            **Fichiers modifiés :**
            ```
            ${{ steps.check-changes.outputs.file_list }}
            ```
            
            Veuillez examiner les modifications avant de fusionner.
          branch: fix/typescript-errors
          base: main
          labels: typescript, automated-pr, fix 