/**
 * Script pour exécuter les tests en parallèle
 * 
 * Ce script permet de:
 * 1. Exécuter les tests en parallèle pour améliorer les performances
 * 2. Diviser les tests en groupes pour éviter les problèmes de mémoire
 * 3. Combiner les résultats des différents groupes
 */
const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Configuration
const MEMORY_LIMIT = 1024; // Limite de mémoire en MB
const MAX_WORKERS = 4; // Nombre maximum de workers en parallèle
const CONFIG_FILE = 'minimal-jest.config.js';

// Fonction pour trouver tous les fichiers de test
async function findTestFiles(pattern) {
  try {
    const files = await glob(pattern);
    return files;
  } catch (error) {
    console.error(`Erreur lors de la recherche des fichiers de test: ${error.message}`);
    return [];
  }
}

// Fonction pour diviser les tests en groupes
function splitTestsIntoGroups(testFiles, numGroups) {
  const groups = Array(numGroups).fill().map(() => []);
  
  // Répartir les tests en groupes de manière équilibrée
  testFiles.forEach((file, index) => {
    groups[index % numGroups].push(file);
  });
  
  return groups;
}

// Fonction pour exécuter un groupe de tests
function runTestGroup(groupFiles, groupIndex) {
  return new Promise((resolve, reject) => {
    const testPaths = groupFiles.join(' ');
    const outputFile = `jest-results-group-${groupIndex}.json`;
    
    const command = `NODE_OPTIONS="--max-old-space-size=${MEMORY_LIMIT}" npx jest --config=${CONFIG_FILE} ${testPaths} --json --outputFile=${outputFile} --maxWorkers=2`;
    
    console.log(`Exécution du groupe ${groupIndex + 1} avec ${groupFiles.length} tests...`);
    
    const child = spawn('sh', ['-c', command], { stdio: 'inherit' });
    
    child.on('close', code => {
      if (code === 0) {
        console.log(`Groupe ${groupIndex + 1} terminé avec succès`);
        resolve(outputFile);
      } else {
        console.error(`Groupe ${groupIndex + 1} terminé avec code d'erreur ${code}`);
        resolve(outputFile); // Résoudre quand même pour continuer avec les autres groupes
      }
    });
    
    child.on('error', error => {
      console.error(`Erreur lors de l'exécution du groupe ${groupIndex + 1}: ${error.message}`);
      reject(error);
    });
  });
}

// Fonction pour combiner les résultats des différents groupes
function combineResults(resultFiles) {
  const combinedResults = {
    numFailedTests: 0,
    numPassedTests: 0,
    numPendingTests: 0,
    numTotalTests: 0,
    startTime: Date.now(),
    success: true,
    testResults: [],
    wasInterrupted: false
  };
  
  for (const file of resultFiles) {
    if (fs.existsSync(file)) {
      try {
        const groupResults = JSON.parse(fs.readFileSync(file, 'utf8'));
        
        combinedResults.numFailedTests += groupResults.numFailedTests;
        combinedResults.numPassedTests += groupResults.numPassedTests;
        combinedResults.numPendingTests += groupResults.numPendingTests;
        combinedResults.numTotalTests += groupResults.numTotalTests;
        combinedResults.success = combinedResults.success && groupResults.success;
        combinedResults.testResults.push(...groupResults.testResults);
        combinedResults.wasInterrupted = combinedResults.wasInterrupted || groupResults.wasInterrupted;
        
        // Supprimer le fichier de résultats du groupe
        fs.unlinkSync(file);
      } catch (error) {
        console.error(`Erreur lors de la lecture du fichier ${file}: ${error.message}`);
      }
    }
  }
  
  combinedResults.endTime = Date.now();
  
  // Écrire les résultats combinés
  fs.writeFileSync('jest-results-combined.json', JSON.stringify(combinedResults, null, 2));
  
  return combinedResults;
}

// Fonction pour afficher un résumé des résultats
function displaySummary(results) {
  console.log('\n=== Résumé des tests ===');
  console.log(`Tests totaux: ${results.numTotalTests}`);
  console.log(`Tests réussis: ${results.numPassedTests}`);
  console.log(`Tests échoués: ${results.numFailedTests}`);
  console.log(`Tests en attente: ${results.numPendingTests}`);
  console.log(`Durée totale: ${(results.endTime - results.startTime) / 1000} secondes`);
  console.log(`Statut: ${results.success ? 'Succès' : 'Échec'}`);
  console.log('========================\n');
  
  if (results.numFailedTests > 0) {
    console.log('Tests en échec:');
    results.testResults.forEach(fileResult => {
      if (fileResult.numFailingTests > 0) {
        console.log(`- ${fileResult.name}: ${fileResult.numFailingTests} échecs`);
      }
    });
  }
}

// Fonction principale
async function main() {
  // Vérifier les arguments
  const args = process.argv.slice(2);
  const testPattern = args[0] || 'Backend/src/tests/**/*.test.ts';
  const numGroups = args[1] ? parseInt(args[1]) : MAX_WORKERS;
  
  console.log(`Recherche des fichiers de test correspondant à: ${testPattern}`);
  const testFiles = await findTestFiles(testPattern);
  
  if (testFiles.length === 0) {
    console.log('Aucun fichier de test trouvé');
    return;
  }
  
  console.log(`${testFiles.length} fichiers de test trouvés`);
  
  // Diviser les tests en groupes
  const groups = splitTestsIntoGroups(testFiles, numGroups);
  console.log(`Tests divisés en ${groups.length} groupes`);
  
  // Exécuter les groupes de tests en parallèle
  console.log('Exécution des tests en parallèle...');
  const resultPromises = groups.map((groupFiles, index) => runTestGroup(groupFiles, index));
  
  try {
    const resultFiles = await Promise.all(resultPromises);
    
    // Combiner les résultats
    console.log('Combinaison des résultats...');
    const combinedResults = combineResults(resultFiles);
    
    // Afficher un résumé
    displaySummary(combinedResults);
    
    // Sortir avec le code approprié
    process.exit(combinedResults.success ? 0 : 1);
  } catch (error) {
    console.error(`Erreur lors de l'exécution des tests: ${error.message}`);
    process.exit(1);
  }
}

// Exécuter le script
main().catch(error => {
  console.error(`Erreur lors de l'exécution du script: ${error.message}`);
  process.exit(1);
});
