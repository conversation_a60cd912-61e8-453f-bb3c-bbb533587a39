version: '3.8'

services:
  # Infrastructure services
  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 1G
    labels:
      - "com.retreatandbe.service=mongodb"
      - "com.retreatandbe.type=database"

  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
    labels:
      - "com.retreatandbe.service=redis"
      - "com.retreatandbe.type=cache"

  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 512M

  # Application services
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - ./ssl:/etc/nginx/ssl:ro
    environment:
      - VITE_API_URL=http://localhost:7001
      - VITE_WS_URL=ws://localhost:7001
      - NODE_ENV=development
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 500M
    labels:
      - "com.retreatandbe.service=frontend"
      - "com.retreatandbe.type=web"

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "7001:7000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_data:/app/data
    environment:
      - PORT=7000
      - FRONTEND_URL=http://localhost
      - NODE_ENV=development
      - MONGODB_URI=mongodb://${MONGO_ROOT_USER:-admin}:${MONGO_ROOT_PASSWORD:-secret}@mongodb:27017/retreatandbe?authSource=admin
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-secret}@redis:6379
      - BACKEND_URL=http://localhost:7001
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:7000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.75'
          memory: 1G
    labels:
      - "com.retreatandbe.service=backend"
      - "com.retreatandbe.type=api"

  partner-registration:
    build:
      context: ./partner-registration
      dockerfile: Dockerfile
    ports:
      - "7016:3000"
    volumes:
      - ./partner-registration:/app
      - /app/node_modules
      - partner-uploads:/app/uploads
    environment:
      - NODE_ENV=development
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET:-your_jwt_secret_here}
      - SMTP_HOST=${SMTP_HOST:-smtp.example.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USER=${SMTP_USER:-your_smtp_user}
      - SMTP_PASS=${SMTP_PASS:-your_smtp_password}
      - MONGODB_URI=mongodb://${MONGO_ROOT_USER:-admin}:${MONGO_ROOT_PASSWORD:-secret}@mongodb:27017/partner?authSource=admin
    depends_on:
      backend:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 500M
    labels:
      - "com.retreatandbe.service=partner-registration"
      - "com.retreatandbe.type=api"

  security:
    build:
      context: ./Security
      dockerfile: Dockerfile
    ports:
      - "7010:7010"
    volumes:
      - ./Security:/app
      - /app/node_modules
      - security_data:/var/lib/clamav
    environment:
      - PORT=7010
      - NODE_ENV=development
      - REDIS_URL=redis://default:${REDIS_PASSWORD:-secret}@redis:6379
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:7010/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 500M
    labels:
      - "com.retreatandbe.service=security"
      - "com.retreatandbe.type=api"

  social:
    build:
      context: ./Social
      dockerfile: Dockerfile
    ports:
      - "7006:7006"
    volumes:
      - ./Social:/app
      - /app/node_modules
      - social_data:/app/data
    environment:
      - PORT=7006
      - NODE_ENV=development
      - MONGODB_URI=mongodb://${MONGO_ROOT_USER:-admin}:${MONGO_ROOT_PASSWORD:-secret}@mongodb:27017/social?authSource=admin
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:7006/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 500M
    labels:
      - "com.retreatandbe.service=social"
      - "com.retreatandbe.type=api"

  # Monitoring services
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus:/etc/prometheus
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    networks:
      - app-network

  grafana:
    image: grafana/grafana:10.0.3
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning
      - grafana-data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    networks:
      - app-network

  # Logging service
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-secret}
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:9200/_cluster/health | grep -vq '\"status\":\"red\"'"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
    labels:
      - "com.retreatandbe.service=elasticsearch"
      - "com.retreatandbe.type=logging"

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=${ELASTIC_PASSWORD:-secret}
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:5601/api/status | grep -q 'Looking good'"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 500M
    labels:
      - "com.retreatandbe.service=kibana"
      - "com.retreatandbe.type=logging"

  # Load Balancer
  traefik:
    image: traefik:v2.10
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik:/etc/traefik:ro
      - ./ssl:/ssl:ro
    command:
      - "--api.insecure=false"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.myresolver.acme.tlschallenge=true"
      - "--certificatesresolvers.myresolver.acme.email=${SSL_EMAIL:-<EMAIL>}"
      - "--certificatesresolvers.myresolver.acme.storage=/ssl/acme.json"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 256M
    labels:
      - "com.retreatandbe.service=traefik"
      - "com.retreatandbe.type=proxy"

  # Backup Service (Open Source)
  duplicati:
    image: duplicati/duplicati:latest
    ports:
      - "8200:8200"
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Paris
    volumes:
      - duplicati_config:/config
      - duplicati_backups:/backups
      - mongodb_data:/source/mongodb:ro
      - redis_data:/source/redis:ro
      - ./backup-config.json:/data/backup-config.json:ro
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8200/api/v1/serverstate"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 500M
    labels:
      - "com.retreatandbe.service=duplicati"
      - "com.retreatandbe.type=backup"

  # Cache Service (Open Source)
  varnish:
    image: varnish:stable
    ports:
      - "8080:80"
    volumes:
      - ./varnish/default.vcl:/etc/varnish/default.vcl:ro
    environment:
      - VARNISH_SIZE=2G
    depends_on:
      frontend:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "varnishadm", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.75'
          memory: 2G
    labels:
      - "com.retreatandbe.service=varnish"
      - "com.retreatandbe.type=cache"

  # Alert Manager (Open Source)
  alertmanager:
    image: prom/alertmanager:latest
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9093/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
    labels:
      - "com.retreatandbe.service=alertmanager"
      - "com.retreatandbe.type=monitoring"

  # Log Aggregation (Open Source)
  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    volumes:
      - ./loki/loki-config.yml:/etc/loki/loki-config.yml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/loki-config.yml
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3100/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 500M
    labels:
      - "com.retreatandbe.service=loki"
      - "com.retreatandbe.type=logging"

  promtail:
    image: grafana/promtail:latest
    volumes:
      - ./loki/promtail-config.yml:/etc/promtail/promtail-config.yml:ro
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/promtail-config.yml
    networks:
      - app-network
    depends_on:
      loki:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9080/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
    labels:
      - "com.retreatandbe.service=promtail"
      - "com.retreatandbe.type=logging"

  vault:
    image: vault:1.13.3
    container_name: vault
    ports:
      - "8200:8200"
    volumes:
      - ./vault/config.hcl:/vault/config/config.hcl:ro
      - vault-data:/vault/file
      - vault-logs:/vault/logs
    cap_add:
      - IPC_LOCK
    environment:
      - VAULT_ADDR=http://0.0.0.0:8200
      - VAULT_API_ADDR=http://0.0.0.0:8200
      - VAULT_DEV_ROOT_TOKEN_ID=root
    command: server -config=/vault/config/config.hcl
    networks:
      - app-network

  consul:
    image: consul:1.15.4
    container_name: consul
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    volumes:
      - ./consul/config.json:/consul/config/config.json:ro
      - consul-data:/consul/data
    command: agent -config-file=/consul/config/config.json
    networks:
      - app-network

  envoy:
    image: envoyproxy/envoy:v1.26-latest
    container_name: envoy
    ports:
      - "10000:10000"
      - "9901:9901"
    volumes:
      - ./envoy/envoy.yaml:/etc/envoy/envoy.yaml:ro
    command: /usr/local/bin/envoy -c /etc/envoy/envoy.yaml
    networks:
      - app-network
    depends_on:
      - analyzer

  jaeger:
    image: jaegertracing/all-in-one:1.46
    container_name: jaeger
    ports:
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "4317:4317"
      - "4318:4318"
      - "14250:14250"
      - "14268:14268"
      - "14269:14269"
      - "9411:9411"
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - app-network

  analyzer:
    build:
      context: ./Analyzer
      dockerfile: Dockerfile
    container_name: analyzer
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - VAULT_ADDR=http://vault:8200
      - VAULT_TOKEN=root
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
      - OTEL_EXPORTER_JAEGER_ENDPOINT=http://jaeger:14268/api/traces
      - OTEL_SERVICE_NAME=analyzer-service
    volumes:
      - ./Analyzer:/usr/src/app
      - /usr/src/app/node_modules
    networks:
      - app-network
    depends_on:
      - vault
      - consul
      - jaeger

networks:
  app-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
    labels:
      - "com.retreatandbe.volume=mongodb"
  redis_data:
    driver: local
    labels:
      - "com.retreatandbe.volume=redis"
  postgres_data:
    driver: local
  backend_data:
    labels:
      - "com.retreatandbe.volume=backend"
  partner-uploads:
    labels:
      - "com.retreatandbe.volume=partner-uploads"
  security_data:
    labels:
      - "com.retreatandbe.volume=security"
  social_data:
    labels:
      - "com.retreatandbe.volume=social"
  prometheus-data:
    labels:
      - "com.retreatandbe.volume=prometheus"
  grafana-data:
    labels:
      - "com.retreatandbe.volume=grafana"
  elasticsearch_data:
    labels:
      - "com.retreatandbe.volume=elasticsearch"
  duplicati_config:
    labels:
      - "com.retreatandbe.volume=duplicati-config"
  duplicati_backups:
    labels:
      - "com.retreatandbe.volume=duplicati-backups"
  alertmanager_data:
    labels:
      - "com.retreatandbe.volume=alertmanager"
  loki_data:
    labels:
      - "com.retreatandbe.volume=loki"
  vault-data:
  vault-logs:
  consul-data:
