services:
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    command: mongod --quiet
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: always
    networks:
      - app-network

  search-transport:
    build:
      context: ./search-transport-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    depends_on:
      mongodb:
        condition: service_healthy
    environment:
      - MONGODB_URI=*****************************************************************
      - JWT_SECRET=your_jwt_secret_key
    volumes:
      - ./search-transport-service/src:/app/src
      - ./search-transport-service/package.json:/app/package.json
      - ./search-transport-service/package-lock.json:/app/package-lock.json
      - /app/node_modules
    restart: always
    networks:
      - app-network

  db:
    image: postgres:latest
    environment:
      POSTGRES_USER: your_user
      POSTGRES_PASSWORD: your_password
      POSTGRES_DB: your_database
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U your_user"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - app-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      DATABASE_URL: ${DATABASE_URL}
      BACKEND_URL: ${BACKEND_URL}
      NODE_ENV: development
    ports:
      - "5000:5000"
    networks:
      - app-network

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: ${DATABASE_URL}
      BACKEND_URL: ${BACKEND_URL}
    networks:
      - app-network

  security-service:
    image: security-service:1.0
    ports:
      - "3001:3000"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  mongodb_data:
