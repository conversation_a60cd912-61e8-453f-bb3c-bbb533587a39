import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Obtenir le répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Créer l'application Express
const app = express();

// Middleware pour les logs
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Servir les fichiers statiques depuis le dossier dist
app.use(express.static(path.join(__dirname, 'dist')));

// Configurer les types MIME correctement
app.use((req, res, next) => {
  if (req.path.endsWith('.js')) {
    res.type('application/javascript');
  } else if (req.path.endsWith('.css')) {
    res.type('text/css');
  } else if (req.path.endsWith('.html')) {
    res.type('text/html');
  }
  next();
});

// Route API d'exemple
app.get('/api/hello', (req, res) => {
  res.json({ message: 'Hello from API' });
});

// Route pour toutes les autres requêtes - SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Démarrer le serveur
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`
╔════════════════════════════════════════════════════════╗
║                                                        ║
║   🚀 Serveur de production démarré sur port ${PORT}         ║
║   🌐 http://localhost:${PORT}                              ║
║                                                        ║
║   📝 Assurez-vous d'avoir exécuté 'npm run build' avant  ║
║   🔍 URLs à tester:                                     ║
║      http://localhost:${PORT}/                             ║
║      http://localhost:${PORT}/test                         ║
║      http://localhost:${PORT}/login                        ║
║                                                        ║
╚════════════════════════════════════════════════════════╝
  `);
}); 