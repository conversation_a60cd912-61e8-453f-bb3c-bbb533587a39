/**
 * Script pour exécuter les tests avec une gestion optimisée de la mémoire
 *
 * Ce script permet de:
 * 1. Exécuter les tests par lots pour éviter les problèmes de mémoire
 * 2. Configurer les options de mémoire de Node.js
 * 3. <PERSON>toyer la mémoire entre les exécutions
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Configuration
const MEMORY_LIMIT = 1024; // Limite de mémoire en MB
const BATCH_SIZE = 5; // Nombre de tests à exécuter par lot
const TEST_TIMEOUT = 30000; // Timeout par test en ms

// Fonction pour trouver tous les fichiers de test
async function findTestFiles(pattern) {
  try {
    const files = await glob(pattern);
    return files;
  } catch (error) {
    console.error(`Erreur lors de la recherche des fichiers de test: ${error.message}`);
    return [];
  }
}

// Fonction pour exécuter un lot de tests
async function runTestBatch(testFiles, config) {
  if (testFiles.length === 0) return;

  const testPaths = testFiles.join(' ');
  const command = `NODE_OPTIONS="--max-old-space-size=${MEMORY_LIMIT}" npx jest --config=${config} ${testPaths} --testTimeout=${TEST_TIMEOUT} --runInBand`;

  try {
    console.log(`Exécution de ${testFiles.length} tests...`);
    execSync(command, { stdio: 'inherit' });
    console.log('Lot de tests terminé avec succès');
  } catch (error) {
    console.error(`Erreur lors de l'exécution des tests: ${error.message}`);
  }

  // Forcer la libération de la mémoire
  global.gc && global.gc();
}

// Fonction principale
async function main() {
  // Vérifier les arguments
  const args = process.argv.slice(2);
  const testPattern = args[0] || 'Backend/src/tests/**/*.test.ts';
  const configFile = args[1] || 'minimal-jest.config.js';

  console.log(`Recherche des fichiers de test correspondant à: ${testPattern}`);
  const testFiles = await findTestFiles(testPattern);

  if (testFiles.length === 0) {
    console.log('Aucun fichier de test trouvé');
    return;
  }

  console.log(`${testFiles.length} fichiers de test trouvés`);

  // Diviser les tests en lots
  const batches = [];
  for (let i = 0; i < testFiles.length; i += BATCH_SIZE) {
    batches.push(testFiles.slice(i, i + BATCH_SIZE));
  }

  console.log(`Exécution des tests en ${batches.length} lots`);

  // Exécuter les lots de tests
  for (let i = 0; i < batches.length; i++) {
    console.log(`Exécution du lot ${i + 1}/${batches.length}`);
    await runTestBatch(batches[i], configFile);

    // Attendre un peu entre les lots pour permettre la libération de la mémoire
    if (i < batches.length - 1) {
      console.log('Pause entre les lots...');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  console.log('Tous les tests ont été exécutés');
}

// Exécuter le script
main().catch(error => {
  console.error(`Erreur lors de l'exécution du script: ${error.message}`);
  process.exit(1);
});
