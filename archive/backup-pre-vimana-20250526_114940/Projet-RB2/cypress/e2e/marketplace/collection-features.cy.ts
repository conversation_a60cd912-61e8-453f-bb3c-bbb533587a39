describe('NFT Collection Features', () => {
  beforeEach(() => {
    cy.setupTestData()
    cy.login('<EMAIL>', 'password123')
    cy.connectWallet()
    cy.visit('/collections')
  })

  afterEach(() => {
    cy.cleanupTestData()
  })

  describe('Collection Creation', () => {
    it('should create a new collection', () => {
      cy.get('[data-cy=create-collection-button]').click()
      
      // Fill collection details;
      cy.get('[data-cy=collection-name-input]').type('Test Collection')
      cy.get('[data-cy=collection-description-input]')
        .type('This is a test collection for E2E testing')
      cy.get('[data-cy=collection-image-input]')
        .selectFile('cypress/fixtures/collection-image.png')
      cy.get('[data-cy=collection-category-select]').click()
      cy.get('[data-cy=category-art]').click()
      
      // Set royalties;
      cy.get('[data-cy=royalty-percentage-input]').type('5')
      cy.get('[data-cy=royalty-address-input]')
        .type('******************************************')
      
      cy.get('[data-cy=create-collection-submit]').click()
      
      // Verify collection created;
      cy.get('[data-cy=collection-success-message]').should('be.visible')
      cy.get('[data-cy=collection-card]')
        .should('contain', 'Test Collection')
    })

    it('should validate collection inputs', () => {
      cy.get('[data-cy=create-collection-button]').click()
      cy.get('[data-cy=create-collection-submit]').click()

      // Check validation messages;
      cy.get('[data-cy=name-error]')
        .should('be.visible')
        .and('contain', 'Name is required')
      cy.get('[data-cy=image-error]')
        .should('be.visible')
        .and('contain', 'Image is required')
    })
  })

  describe('Collection Management', () => {
    beforeEach(() => {
      // Create a test collection first;
      cy.get('[data-cy=create-collection-button]').click()
      cy.get('[data-cy=collection-name-input]').type('Test Collection')
      cy.get('[data-cy=collection-description-input]')
        .type('Test description')
      cy.get('[data-cy=collection-image-input]')
        .selectFile('cypress/fixtures/collection-image.png')
      cy.get('[data-cy=create-collection-submit]').click()
    })

    it('should edit collection details', () => {
      cy.get('[data-cy=collection-card]').first().click()
      cy.get('[data-cy=edit-collection-button]').click()
      
      // Update details;
      cy.get('[data-cy=collection-name-input]')
        .clear()
        .type('Updated Collection')
      cy.get('[data-cy=collection-description-input]')
        .clear()
        .type('Updated description')
      
      cy.get('[data-cy=save-changes-button]').click()
      
      // Verify updates;
      cy.get('[data-cy=collection-name]')
        .should('contain', 'Updated Collection')
      cy.get('[data-cy=collection-description]')
        .should('contain', 'Updated description')
    })

    it('should add collaborators', () => {
      cy.get('[data-cy=collection-card]').first().click()
      cy.get('[data-cy=manage-collaborators-button]').click()
      
      // Add collaborator;
      cy.get('[data-cy=add-collaborator-input]')
        .type('<EMAIL>')
      cy.get('[data-cy=add-collaborator-button]').click()
      
      // Verify collaborator added;
      cy.get('[data-cy=collaborator-list]')
        .should('contain', '<EMAIL>')
    })
  })

  describe('Collection Analytics', () => {
    it('should display collection statistics', () => {
      cy.get('[data-cy=collection-card]').first().click()
      cy.get('[data-cy=analytics-tab]').click()

      // Verify stats are visible;
      cy.get('[data-cy=total-volume]').should('be.visible')
      cy.get('[data-cy=floor-price]').should('be.visible')
      cy.get('[data-cy=num-owners]').should('be.visible')
      cy.get('[data-cy=total-items]').should('be.visible')
    })

    it('should show price history chart', () => {
      cy.get('[data-cy=collection-card]').first().click()
      cy.get('[data-cy=analytics-tab]').click()
      
      cy.get('[data-cy=price-history-chart]').should('be.visible')
      cy.get('[data-cy=chart-timeframe-selector]').click()
      cy.get('[data-cy=timeframe-1m]').click()
      
      // Verify chart updates;
      cy.get('[data-cy=chart-title]')
        .should('contain', 'Last Month')
    })

    it('should display activity feed', () => {
      cy.get('[data-cy=collection-card]').first().click()
      cy.get('[data-cy=activity-tab]').click()
      
      cy.get('[data-cy=activity-list]').should('be.visible')
      cy.get('[data-cy=activity-filter]').click()
      cy.get('[data-cy=filter-sales]').click()
      
      // Verify filtered activities;
      cy.get('[data-cy=activity-type]').each(($type) => {
        cy.wrap($type).should('contain', 'Sale')
      })
    })
  })

  describe('Collection Permissions', () => {
    it('should restrict edit access to owner', () => {
      // Switch to non-owner account;
      cy.login('<EMAIL>', 'password123')
      cy.get('[data-cy=collection-card]').first().click()
      
      // Verify edit controls are not visible;
      cy.get('[data-cy=edit-collection-button]')
        .should('not.exist')
      cy.get('[data-cy=manage-collaborators-button]')
        .should('not.exist')
    })

    it('should allow collaborator actions', () => {
      // Switch to collaborator account;
      cy.login('<EMAIL>', 'password123')
      cy.get('[data-cy=collection-card]').first().click()
      
      // Verify allowed actions;
      cy.get('[data-cy=add-item-button]').should('be.visible')
      cy.get('[data-cy=moderate-comments-button]').should('be.visible')
    })
  })
}) 