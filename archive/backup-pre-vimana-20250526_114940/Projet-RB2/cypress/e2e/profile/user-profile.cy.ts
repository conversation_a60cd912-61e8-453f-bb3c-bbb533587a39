describe('User Profile Features', () => {
  beforeEach(() => {
    cy.setupTestData()
    cy.login('<EMAIL>', 'password123')
    cy.connectWallet()
  })

  afterEach(() => {
    cy.cleanupTestData()
  })

  describe('Profile Viewing', () => {
    it('should display user profile information', () => {
      cy.visit('/profile')
      
      // Verify basic profile elements;
      cy.get('[data-cy=profile-username]').should('be.visible')
      cy.get('[data-cy=profile-bio]').should('be.visible')
      cy.get('[data-cy=profile-avatar]').should('be.visible')
      cy.get('[data-cy=wallet-address]').should('be.visible')
      cy.get('[data-cy=join-date]').should('be.visible')
    })

    it('should display user stats', () => {
      cy.visit('/profile')
      
      // Verify stats section;
      cy.get('[data-cy=user-stats]').within(() => {
        cy.get('[data-cy=nfts-created]').should('be.visible')
        cy.get('[data-cy=nfts-owned]').should('be.visible')
        cy.get('[data-cy=total-sales]').should('be.visible')
        cy.get('[data-cy=followers-count]').should('be.visible')
        cy.get('[data-cy=following-count]').should('be.visible')
      })
    })

    it('should display user collections', () => {
      cy.visit('/profile')
      cy.get('[data-cy=collections-tab]').click()
      
      // Verify collections display;
      cy.get('[data-cy=user-collections]').should('be.visible')
      cy.get('[data-cy=collection-card]').should('have.length.at.least', 1)
    })

    it('should display user NFTs', () => {
      cy.visit('/profile')
      cy.get('[data-cy=created-tab]').click()
      
      // Verify created NFTs section;
      cy.get('[data-cy=created-nfts]').should('be.visible')
      
      // Switch to owned tab;
      cy.get('[data-cy=owned-tab]').click()
      
      // Verify owned NFTs section;
      cy.get('[data-cy=owned-nfts]').should('be.visible')
    })
  })

  describe('Profile Editing', () => {
    it('should edit profile information', () => {
      cy.visit('/profile')
      cy.get('[data-cy=edit-profile-button]').click()
      
      // Edit profile details;
      cy.get('[data-cy=username-input]')
        .clear()
        .type('UpdatedUsername')
      
      cy.get('[data-cy=bio-input]')
        .clear()
        .type('This is my updated bio for testing purposes')
      
      cy.get('[data-cy=twitter-input]')
        .clear()
        .type('https://twitter.com/updated_handle')
      
      cy.get('[data-cy=website-input]')
        .clear()
        .type('https://updated-website.com')
      
      // Upload new avatar;
      cy.get('[data-cy=avatar-upload]')
        .selectFile('cypress/fixtures/avatar.png')
      
      // Save changes;
      cy.get('[data-cy=save-profile]').click()
      
      // Verify changes were saved;
      cy.get('[data-cy=profile-username]')
        .should('contain', 'UpdatedUsername')
      cy.get('[data-cy=profile-bio]')
        .should('contain', 'This is my updated bio')
      cy.get('[data-cy=social-twitter]')
        .should('contain', 'updated_handle')
      cy.get('[data-cy=website-link]')
        .should('have.attr', 'href', 'https://updated-website.com')
    })

    it('should validate profile inputs', () => {
      cy.visit('/profile')
      cy.get('[data-cy=edit-profile-button]').click()
      
      // Clear required fields;
      cy.get('[data-cy=username-input]').clear()
      
      // Try to save with empty username;
      cy.get('[data-cy=save-profile]').click()
      
      // Check validation message;
      cy.get('[data-cy=username-error]')
        .should('be.visible')
        .and('contain', 'Username is required')
      
      // Test invalid website format;
      cy.get('[data-cy=username-input]').type('TestUser')
      cy.get('[data-cy=website-input]')
        .clear()
        .type('invalid-website')
      cy.get('[data-cy=save-profile]').click()
      
      // Check validation message for website;
      cy.get('[data-cy=website-error]')
        .should('be.visible')
        .and('contain', 'Please enter a valid URL')
    })

    it('should update profile privacy settings', () => {
      cy.visit('/profile')
      cy.get('[data-cy=edit-profile-button]').click()
      cy.get('[data-cy=privacy-tab]').click()
      
      // Toggle privacy settings;
      cy.get('[data-cy=hide-wallet-toggle]').click()
      cy.get('[data-cy=hide-email-toggle]').click()
      
      // Save changes;
      cy.get('[data-cy=save-privacy]').click()
      
      // Verify changes;
      cy.get('[data-cy=privacy-saved-message]').should('be.visible')
      cy.visit('/profile')
      
      // Wallet should now be hidden;
      cy.get('[data-cy=wallet-address]').should('not.exist')
    })
  })

  describe('Wallet Management', () => {
    it('should connect additional wallet', () => {
      cy.visit('/profile')
      cy.get('[data-cy=edit-profile-button]').click()
      cy.get('[data-cy=wallet-tab]').click()
      
      // Add another wallet;
      cy.get('[data-cy=add-wallet-button]').click()
      
      // Mock wallet connection;
      cy.window().then((win) => {
        win.ethereum.selectedAddress = '******************************************'
      })
      
      cy.get('[data-cy=connect-wallet-button]').click()
      
      // Verify wallet was added;
      cy.get('[data-cy=connected-wallets]')
        .should('contain', '0x9876...3210')
    })

    it('should set primary wallet', () => {
      cy.visit('/profile')
      cy.get('[data-cy=edit-profile-button]').click()
      cy.get('[data-cy=wallet-tab]').click()
      
      // Assuming we have multiple wallets connected;
      cy.get('[data-cy=set-primary-button]').eq(1).click()
      
      // Verify primary wallet changed;
      cy.get('[data-cy=wallet-item]').eq(1)
        .should('have.class', 'primary')
    })

    it('should disconnect wallet', () => {
      cy.visit('/profile')
      cy.get('[data-cy=edit-profile-button]').click()
      cy.get('[data-cy=wallet-tab]').click()
      
      // Disconnect a wallet;
      cy.get('[data-cy=disconnect-wallet]').first().click()
      cy.get('[data-cy=confirm-disconnect]').click()
      
      // Verify wallet removed;
      cy.get('[data-cy=wallet-removed-message]').should('be.visible')
    })
  })

  describe('Activity History', () => {
    it('should display user activity history', () => {
      cy.visit('/profile')
      cy.get('[data-cy=activity-tab]').click()
      
      // Verify activity feed exists;
      cy.get('[data-cy=activity-feed]').should('be.visible')
      cy.get('[data-cy=activity-item]').should('have.length.at.least', 1)
    })

    it('should filter activity by type', () => {
      cy.visit('/profile')
      cy.get('[data-cy=activity-tab]').click()
      
      // Filter by sales;
      cy.get('[data-cy=filter-dropdown]').click()
      cy.get('[data-cy=filter-sales]').click()
      
      // Verify filtered activities;
      cy.get('[data-cy=activity-item]').each(($item) => {
        cy.wrap($item).find('[data-cy=activity-type]')
          .should('contain', 'Sale')
      })
      
      // Change filter to listings;
      cy.get('[data-cy=filter-dropdown]').click()
      cy.get('[data-cy=filter-listings]').click()
      
      // Verify filtered activities;
      cy.get('[data-cy=activity-item]').each(($item) => {
        cy.wrap($item).find('[data-cy=activity-type]')
          .should('contain', 'Listing')
      })
    })

    it('should show transaction details', () => {
      cy.visit('/profile')
      cy.get('[data-cy=activity-tab]').click()
      
      // Click on activity item;
      cy.get('[data-cy=activity-item]').first().click()
      
      // Verify transaction details modal;
      cy.get('[data-cy=transaction-details]').should('be.visible')
      cy.get('[data-cy=transaction-hash]').should('be.visible')
      cy.get('[data-cy=transaction-date]').should('be.visible')
      cy.get('[data-cy=transaction-amount]').should('be.visible')
      cy.get('[data-cy=transaction-gas]').should('be.visible')
    })
  })
}) 