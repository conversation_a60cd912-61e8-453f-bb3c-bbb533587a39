import os

file_path = 'frontend/src/hooks/useAdaptiveBandwidth.ts'

# Lire le contenu du fichier
with open(file_path, 'r') as file:
    lines = file.readlines()

# Corriger la ligne 146 (index 145)
if len(lines) > 145:
    # Remplacer ' || ;' par ' ||'
    lines[145] = lines[145].replace(' || ;', ' ||')

# Corriger la ligne 169 (index 168)
if len(lines) > 168:
    # Remplacer 'return {;' par 'return {'
    lines[168] = lines[168].replace('return {;', 'return {')

# Corriger d'autres lignes avec des return () => {; qui pourraient exister
for i in range(len(lines)):
    lines[i] = lines[i].replace('return () => {;', 'return () => {')

# Écrire le contenu corrigé
with open(file_path, 'w') as file:
    file.writelines(lines)

print(f"Corrections appliquées à {file_path}") 