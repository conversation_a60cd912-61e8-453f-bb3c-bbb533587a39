version: '3.8'

services:
  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./frontend/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
    restart: always
    networks:
      - retreat-network

  # Backend NestJS
  backend:
    build:
      context: ./Backend-NestJS
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=************************************/retreat
      - JWT_SECRET=your-production-jwt-secret
      - JWT_EXPIRATION=1d
      - REFRESH_TOKEN_SECRET=your-production-refresh-token-secret
      - REFRESH_TOKEN_EXPIRATION=7d
    depends_on:
      - db
    restart: always
    networks:
      - retreat-network

  # Security Microservice
  security-service:
    build:
      context: ./Security
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - JWT_SECRET=your-production-jwt-secret
      - JWT_EXPIRATION=1d
    restart: always
    networks:
      - retreat-network

  # Financial Management Microservice
  financial-service:
    build:
      context: ./Financial-Management
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - STRIPE_SECRET_KEY=your-production-stripe-secret-key
      - STRIPE_WEBHOOK_SECRET=your-production-stripe-webhook-secret
    restart: always
    networks:
      - retreat-network

  # Social Platform Video Microservice
  social-service:
    build:
      context: ./Social-Platform-Video
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
    restart: always
    networks:
      - retreat-network

  # Education Microservice
  education-service:
    build:
      context: ./Education
      dockerfile: Dockerfile
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=production
    restart: always
    networks:
      - retreat-network

  # Agent IA Microservice
  agent-service:
    build:
      context: ./Agent-IA
      dockerfile: Dockerfile
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=production
      - OPENAI_API_KEY=your-production-openai-api-key
    restart: always
    networks:
      - retreat-network

  # Database
  db:
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=retreat
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: always
    networks:
      - retreat-network

  # Redis for caching
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: always
    networks:
      - retreat-network

volumes:
  postgres-data:
  redis-data:

networks:
  retreat-network:
    driver: bridge
