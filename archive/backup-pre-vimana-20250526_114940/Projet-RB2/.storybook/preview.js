/** @type { import('@storybook/react').Preview } */
import React from 'react';
import { ThemeProvider } from '@emotion/react';
import { lightTheme, darkTheme } from '../frontend/src/atomic/theme';

const preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        {
          name: 'light',
          value: '#FFFFFF',
        },
        {
          name: 'dark',
          value: '#121212',
        },
      ],
    },
  },
  decorators: [
    (Story, context) => {
      // Determine theme based on selected background
      const theme = context.globals.backgrounds?.value === '#121212' ? darkTheme : lightTheme;
      
      return (
        <ThemeProvider theme={theme}>
          <div style={{ 
            padding: '2rem', 
            backgroundColor: theme.colors.background,
            color: theme.colors.text,
            transition: 'all 0.3s ease-in-out',
            minHeight: '100vh'
          }}>
            <Story />
          </div>
        </ThemeProvider>
      );
    },
  ],
};

export default preview; 