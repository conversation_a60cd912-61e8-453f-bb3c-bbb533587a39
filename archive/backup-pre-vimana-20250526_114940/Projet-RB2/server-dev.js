import { createServer } from 'vite';

// Démarrer Vite en mode serveur
async function startViteDevServer() {
  try {
    const server = await createServer({
      // Configuration détaillée de Vite
      configFile: './vite.config.ts', // Utiliser la configuration Vite existante
      server: {
        port: 3002,
        strictPort: true,
        hmr: true, // Hot Module Replacement
        open: true, // Ouvrir le navigateur automatiquement
      },
      optimizeDeps: {
        force: true, // Forcer l'optimisation des dépendances
      },
      build: {
        sourcemap: true,
        minify: false, // Désactiver la minification en développement
        target: 'esnext', // Cibler les navigateurs modernes
      },
      preview: {
        port: 3002,
      }
    });
    
    // Démarrer le serveur et l'ouvrir directement
    await server.listen();
    
    // Imprimer l'URL du serveur
    const info = server.config.server;
    const protocol = info.https ? 'https' : 'http';
    const host = info.host || 'localhost';
    
    console.log();
    console.log(`  ✅ Serveur Vite démarré avec succès à ${protocol}://${host}:${info.port}`);
    console.log();
    
    // Laisser le serveur ouvert
    ['SIGINT', 'SIGTERM'].forEach(signal => {
      process.on(signal, () => {
        server.close();
        process.exit();
      });
    });
  } catch (e) {
    console.error("Erreur lors du démarrage du serveur Vite:", e);
    process.exit(1);
  }
}

// Lancer le serveur
startViteDevServer(); 