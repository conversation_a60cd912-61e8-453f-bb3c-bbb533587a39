/* eslint-env node */
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';
import tseslint from 'typescript-eslint';

const compat = new FlatCompat();

export default tseslint.config(
  js.configs.recommended,
  ...compat.extends(
    'plugin:@typescript-eslint/recommended'
  ),
  {
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true
        },
        project: ['./tsconfig.json', './*/tsconfig.json', './*/*/tsconfig.json']
      },
    },
    plugins: {
      '@typescript-eslint': tseslint.plugin,
    },
    rules: {
      // Règles de base seulement
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'error',
      'no-extra-semi': 'error',
      'semi': ['error', 'always'],
      'no-var': 'error',
      'prefer-const': 'error',
    },
  },
  {
    files: ['**/*.tsx', '**/*.jsx'],
    ...compat.extends(
      'plugin:react/recommended',
      'plugin:react-hooks/recommended'
    ),
    rules: {
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
    }
  }
); 