/**
 * Centralized message broker service for asynchronous communication between microservices.
 * Supports <PERSON><PERSON><PERSON> as the primary message broker with fallback options.
 */

import { <PERSON><PERSON><PERSON>, Producer, Consumer, Admin, KafkaMessage } from 'kafkajs';
import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';

// Message priority levels
export enum MessagePriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Message types
export enum MessageType {
  COMMAND = 'command',
  EVENT = 'event',
  QUERY = 'query',
  RESPONSE = 'response',
  NOTIFICATION = 'notification'
}

// Message status
export enum MessageStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  PROCESSED = 'processed',
  FAILED = 'failed'
}

// Message interface
export interface Message {
  id: string;
  type: MessageType;
  source: string;
  destination?: string;
  priority: MessagePriority;
  timestamp: string;
  correlationId?: string;
  replyTo?: string;
  payload: any;
  headers?: Record<string, string>;
  status?: MessageStatus;
  retryCount?: number;
}

// Message handler function type
export type MessageHandler = (message: Message) => Promise<void>;

// Subscription options
export interface SubscriptionOptions {
  groupId?: string;
  fromBeginning?: boolean;
  autoCommit?: boolean;
  maxBatchSize?: number;
}

// Message broker configuration
export interface MessageBrokerConfig {
  clientId: string;
  brokers: string[];
  defaultTopic?: string;
  connectionTimeout?: number;
  authenticationTimeout?: number;
  reauthenticationThreshold?: number;
  ssl?: boolean;
  sasl?: {
    mechanism: 'plain' | 'scram-sha-256' | 'scram-sha-512';
    username: string;
    password: string;
  };
  retry?: {
    initialRetryTime: number;
    retries: number;
  };
}

/**
 * Centralized message broker service
 */
export class MessageBroker {
  private static instance: MessageBroker;
  private kafka: Kafka;
  private producer: Producer;
  private consumers: Map<string, Consumer> = new Map();
  private admin: Admin;
  private eventEmitter: EventEmitter = new EventEmitter();
  private isConnected: boolean = false;
  private config: MessageBrokerConfig;
  private defaultTopic: string;
  private pendingMessages: Map<string, Message> = new Map();
  private messageHandlers: Map<string, Set<MessageHandler>> = new Map();
  private topicToGroupMap: Map<string, string> = new Map();

  /**
   * Get the singleton instance of the MessageBroker
   */
  public static getInstance(config?: MessageBrokerConfig): MessageBroker {
    if (!MessageBroker.instance) {
      if (!config) {
        throw new Error('MessageBroker configuration is required for initialization');
      }
      MessageBroker.instance = new MessageBroker(config);
    }
    return MessageBroker.instance;
  }

  /**
   * Private constructor for singleton pattern
   */
  private constructor(config: MessageBrokerConfig) {
    this.config = config;
    this.defaultTopic = config.defaultTopic || 'default-topic';
    
    // Initialize Kafka client
    this.kafka = new Kafka({
      clientId: config.clientId,
      brokers: config.brokers,
      ssl: config.ssl,
      sasl: config.sasl,
      connectionTimeout: config.connectionTimeout,
      authenticationTimeout: config.authenticationTimeout,
      reauthenticationThreshold: config.reauthenticationThreshold,
      retry: config.retry
    });
    
    // Initialize producer
    this.producer = this.kafka.producer({
      allowAutoTopicCreation: true,
      transactionTimeout: 30000
    });
    
    // Initialize admin
    this.admin = this.kafka.admin();
    
    // Set max listeners to avoid memory leak warnings
    this.eventEmitter.setMaxListeners(100);
  }

  /**
   * Initialize the message broker
   */
  public async initialize(): Promise<void> {
    try {
      // Connect the producer
      await this.producer.connect();
      
      // Connect the admin
      await this.admin.connect();
      
      this.isConnected = true;
      logger.info('Message broker initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize message broker:', error);
      throw new Error(`Message broker initialization failed: ${error}`);
    }
  }

  /**
   * Disconnect the message broker
   */
  public async disconnect(): Promise<void> {
    try {
      // Disconnect all consumers
      for (const consumer of this.consumers.values()) {
        await consumer.disconnect();
      }
      
      // Disconnect the producer
      await this.producer.disconnect();
      
      // Disconnect the admin
      await this.admin.disconnect();
      
      this.isConnected = false;
      logger.info('Message broker disconnected successfully');
    } catch (error) {
      logger.error('Failed to disconnect message broker:', error);
      throw new Error(`Message broker disconnection failed: ${error}`);
    }
  }

  /**
   * Check if the message broker is connected
   */
  public isInitialized(): boolean {
    return this.isConnected;
  }

  /**
   * Create a topic if it doesn't exist
   */
  public async createTopic(topic: string, numPartitions: number = 1, replicationFactor: number = 1): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.initialize();
      }
      
      await this.admin.createTopics({
        topics: [
          {
            topic,
            numPartitions,
            replicationFactor
          }
        ]
      });
      
      logger.info(`Topic ${topic} created successfully`);
    } catch (error) {
      logger.error(`Failed to create topic ${topic}:`, error);
      throw new Error(`Failed to create topic ${topic}: ${error}`);
    }
  }

  /**
   * Delete a topic
   */
  public async deleteTopic(topic: string): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.initialize();
      }
      
      await this.admin.deleteTopics({
        topics: [topic]
      });
      
      logger.info(`Topic ${topic} deleted successfully`);
    } catch (error) {
      logger.error(`Failed to delete topic ${topic}:`, error);
      throw new Error(`Failed to delete topic ${topic}: ${error}`);
    }
  }

  /**
   * List all topics
   */
  public async listTopics(): Promise<string[]> {
    try {
      if (!this.isConnected) {
        await this.initialize();
      }
      
      const topics = await this.admin.listTopics();
      logger.info(`Listed ${topics.length} topics`);
      return topics;
    } catch (error) {
      logger.error('Failed to list topics:', error);
      throw new Error(`Failed to list topics: ${error}`);
    }
  }

  /**
   * Publish a message to a topic
   */
  public async publish(
    message: Omit<Message, 'id' | 'timestamp' | 'status'>,
    topic?: string
  ): Promise<string> {
    try {
      if (!this.isConnected) {
        await this.initialize();
      }
      
      const targetTopic = topic || this.defaultTopic;
      
      // Create a complete message
      const completeMessage: Message = {
        ...message,
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        status: MessageStatus.PENDING
      };
      
      // Add to pending messages
      this.pendingMessages.set(completeMessage.id, completeMessage);
      
      // Publish the message
      await this.producer.send({
        topic: targetTopic,
        messages: [
          {
            key: completeMessage.id,
            value: JSON.stringify(completeMessage),
            headers: completeMessage.headers
          }
        ]
      });
      
      // Update message status
      completeMessage.status = MessageStatus.DELIVERED;
      this.pendingMessages.set(completeMessage.id, completeMessage);
      
      logger.info(`Message ${completeMessage.id} published to topic ${targetTopic}`);
      return completeMessage.id;
    } catch (error) {
      logger.error('Failed to publish message:', error);
      throw new Error(`Failed to publish message: ${error}`);
    }
  }

  /**
   * Publish multiple messages to a topic in a batch
   */
  public async publishBatch(
    messages: Array<Omit<Message, 'id' | 'timestamp' | 'status'>>,
    topic?: string
  ): Promise<string[]> {
    try {
      if (!this.isConnected) {
        await this.initialize();
      }
      
      const targetTopic = topic || this.defaultTopic;
      const messageIds: string[] = [];
      
      // Create complete messages
      const completeMessages: Message[] = messages.map(message => {
        const completeMessage: Message = {
          ...message,
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          status: MessageStatus.PENDING
        };
        
        // Add to pending messages
        this.pendingMessages.set(completeMessage.id, completeMessage);
        messageIds.push(completeMessage.id);
        
        return completeMessage;
      });
      
      // Publish the messages
      await this.producer.send({
        topic: targetTopic,
        messages: completeMessages.map(message => ({
          key: message.id,
          value: JSON.stringify(message),
          headers: message.headers
        }))
      });
      
      // Update message statuses
      for (const message of completeMessages) {
        message.status = MessageStatus.DELIVERED;
        this.pendingMessages.set(message.id, message);
      }
      
      logger.info(`Batch of ${completeMessages.length} messages published to topic ${targetTopic}`);
      return messageIds;
    } catch (error) {
      logger.error('Failed to publish batch of messages:', error);
      throw new Error(`Failed to publish batch of messages: ${error}`);
    }
  }

  /**
   * Subscribe to a topic
   */
  public async subscribe(
    topic: string,
    handler: MessageHandler,
    options?: SubscriptionOptions
  ): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.initialize();
      }
      
      // Generate a group ID if not provided
      const groupId = options?.groupId || `${this.config.clientId}-${topic}-group`;
      
      // Store the mapping of topic to group
      this.topicToGroupMap.set(topic, groupId);
      
      // Create a consumer if it doesn't exist for this group
      if (!this.consumers.has(groupId)) {
        const consumer = this.kafka.consumer({
          groupId,
          maxBytesPerPartition: 1048576, // 1MB
          sessionTimeout: 30000,
          rebalanceTimeout: 60000,
          heartbeatInterval: 3000
        });
        
        await consumer.connect();
        this.consumers.set(groupId, consumer);
        
        // Subscribe to the topic
        await consumer.subscribe({
          topic,
          fromBeginning: options?.fromBeginning || false
        });
        
        // Start consuming messages
        await consumer.run({
          autoCommit: options?.autoCommit !== false,
          eachBatchAutoResolve: true,
          partitionsConsumedConcurrently: 3,
          eachBatch: async ({ batch, resolveOffset, heartbeat, isRunning, commitOffsetsIfNecessary }) => {
            for (let i = 0; i < batch.messages.length; i++) {
              if (!isRunning() || !this.isConnected) break;
              
              const kafkaMessage = batch.messages[i];
              
              try {
                // Parse the message
                const message: Message = JSON.parse(kafkaMessage.value?.toString() || '{}');
                
                // Get all handlers for this topic
                const handlers = this.messageHandlers.get(topic) || new Set();
                
                // Process the message with all handlers
                for (const handler of handlers) {
                  try {
                    await handler(message);
                  } catch (handlerError) {
                    logger.error(`Handler error for message ${message.id}:`, handlerError);
                  }
                }
                
                // Mark the message as processed
                if (this.pendingMessages.has(message.id)) {
                  const pendingMessage = this.pendingMessages.get(message.id);
                  if (pendingMessage) {
                    pendingMessage.status = MessageStatus.PROCESSED;
                    this.pendingMessages.set(message.id, pendingMessage);
                  }
                }
                
                // Resolve the offset
                resolveOffset(kafkaMessage.offset);
                
                // Send heartbeat to avoid session timeout
                await heartbeat();
              } catch (error) {
                logger.error(`Error processing message from topic ${topic}:`, error);
              }
            }
            
            // Commit offsets if necessary
            if (options?.autoCommit !== false) {
              await commitOffsetsIfNecessary();
            }
          }
        });
      } else {
        // Consumer already exists, just add the handler
        const consumer = this.consumers.get(groupId);
        
        // Check if the consumer is already subscribed to this topic
        const subscriptions = await consumer?.describeGroup();
        const isSubscribed = subscriptions?.members.some(member => 
          member.memberAssignment && member.memberAssignment.includes(topic)
        );
        
        if (!isSubscribed) {
          // Subscribe to the topic
          await consumer?.subscribe({
            topic,
            fromBeginning: options?.fromBeginning || false
          });
        }
      }
      
      // Add the handler to the topic
      if (!this.messageHandlers.has(topic)) {
        this.messageHandlers.set(topic, new Set());
      }
      
      this.messageHandlers.get(topic)?.add(handler);
      
      logger.info(`Subscribed to topic ${topic} with group ${groupId}`);
    } catch (error) {
      logger.error(`Failed to subscribe to topic ${topic}:`, error);
      throw new Error(`Failed to subscribe to topic ${topic}: ${error}`);
    }
  }

  /**
   * Unsubscribe from a topic
   */
  public async unsubscribe(topic: string, handler?: MessageHandler): Promise<void> {
    try {
      // Get the group ID for this topic
      const groupId = this.topicToGroupMap.get(topic);
      if (!groupId) {
        logger.warn(`No subscription found for topic ${topic}`);
        return;
      }
      
      // If a specific handler is provided, remove only that handler
      if (handler && this.messageHandlers.has(topic)) {
        const handlers = this.messageHandlers.get(topic);
        handlers?.delete(handler);
        
        // If there are no more handlers, remove the topic
        if (handlers?.size === 0) {
          this.messageHandlers.delete(topic);
          
          // If the consumer is not used by any other topic, disconnect it
          const consumer = this.consumers.get(groupId);
          if (consumer) {
            await consumer.disconnect();
            this.consumers.delete(groupId);
          }
          
          this.topicToGroupMap.delete(topic);
        }
      } else {
        // Remove all handlers for this topic
        this.messageHandlers.delete(topic);
        
        // Disconnect the consumer
        const consumer = this.consumers.get(groupId);
        if (consumer) {
          await consumer.disconnect();
          this.consumers.delete(groupId);
        }
        
        this.topicToGroupMap.delete(topic);
      }
      
      logger.info(`Unsubscribed from topic ${topic}`);
    } catch (error) {
      logger.error(`Failed to unsubscribe from topic ${topic}:`, error);
      throw new Error(`Failed to unsubscribe from topic ${topic}: ${error}`);
    }
  }

  /**
   * Request-response pattern implementation
   */
  public async request(
    request: Omit<Message, 'id' | 'timestamp' | 'status' | 'replyTo'>,
    topic: string,
    timeout: number = 30000
  ): Promise<Message> {
    return new Promise(async (resolve, reject) => {
      try {
        if (!this.isConnected) {
          await this.initialize();
        }
        
        // Create a unique reply topic
        const replyTopic = `${this.config.clientId}-reply-${uuidv4()}`;
        
        // Create the request message
        const requestMessage: Omit<Message, 'id' | 'timestamp' | 'status'> = {
          ...request,
          replyTo: replyTopic,
          correlationId: uuidv4()
        };
        
        // Set up a timeout
        const timeoutId = setTimeout(() => {
          // Clean up
          this.unsubscribe(replyTopic).catch(error => {
            logger.error(`Failed to unsubscribe from reply topic ${replyTopic}:`, error);
          });
          
          reject(new Error(`Request timed out after ${timeout}ms`));
        }, timeout);
        
        // Subscribe to the reply topic
        await this.subscribe(replyTopic, async (response) => {
          // Check if this is the response we're waiting for
          if (response.correlationId === requestMessage.correlationId) {
            // Clear the timeout
            clearTimeout(timeoutId);
            
            // Unsubscribe from the reply topic
            await this.unsubscribe(replyTopic);
            
            // Resolve with the response
            resolve(response);
          }
        }, { fromBeginning: true });
        
        // Send the request
        await this.publish(requestMessage, topic);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Get a message by ID
   */
  public getMessage(messageId: string): Message | undefined {
    return this.pendingMessages.get(messageId);
  }

  /**
   * Get all pending messages
   */
  public getPendingMessages(): Message[] {
    return Array.from(this.pendingMessages.values()).filter(
      message => message.status === MessageStatus.PENDING
    );
  }

  /**
   * Clear all pending messages
   */
  public clearPendingMessages(): void {
    this.pendingMessages.clear();
  }
}

// Default configuration
const defaultConfig: MessageBrokerConfig = {
  clientId: 'retreat-and-be-client',
  brokers: process.env.KAFKA_BROKERS ? process.env.KAFKA_BROKERS.split(',') : ['localhost:9092'],
  defaultTopic: process.env.KAFKA_DEFAULT_TOPIC || 'retreat-events',
  retry: {
    initialRetryTime: 100,
    retries: 8
  }
};

// Export the singleton instance with default configuration
export const messageBroker = MessageBroker.getInstance(defaultConfig);
