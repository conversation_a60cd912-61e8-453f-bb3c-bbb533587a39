/**
 * Script d'analyse et d'optimisation des performances des tests
 * 
 * Ce script permet de:
 * 1. Mesurer le temps d'exécution de chaque test
 * 2. Identifier les tests lents
 * 3. Suggérer des optimisations
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Configuration
const SLOW_TEST_THRESHOLD = 1000; // ms
const VERY_SLOW_TEST_THRESHOLD = 5000; // ms
const REPORT_PATH = 'test-performance-report.md';

// Fonction pour exécuter les tests avec mesure du temps
async function runTestsWithTimer(pattern) {
  try {
    const command = `NODE_OPTIONS="--max-old-space-size=1024" npx jest --config=minimal-jest.config.js ${pattern} --json --outputFile=jest-results.json`;
    execSync(command, { stdio: 'pipe' });
    
    if (fs.existsSync('jest-results.json')) {
      const results = JSON.parse(fs.readFileSync('jest-results.json', 'utf8'));
      return results;
    }
    
    return null;
  } catch (error) {
    console.error(`Erreur lors de l'exécution des tests: ${error.message}`);
    
    // Même en cas d'erreur, Jest peut avoir généré un fichier de résultats
    if (fs.existsSync('jest-results.json')) {
      const results = JSON.parse(fs.readFileSync('jest-results.json', 'utf8'));
      return results;
    }
    
    return null;
  }
}

// Fonction pour analyser les résultats des tests
function analyzeTestResults(results) {
  if (!results || !results.testResults) {
    return {
      totalTests: 0,
      totalTime: 0,
      slowTests: [],
      verySlowTests: [],
      fastestTests: [],
      averageTime: 0
    };
  }
  
  const testDetails = [];
  let totalTime = 0;
  let totalTests = 0;
  
  // Parcourir tous les fichiers de test
  results.testResults.forEach(fileResult => {
    const filePath = fileResult.name;
    
    // Parcourir tous les tests dans le fichier
    fileResult.assertionResults.forEach(testResult => {
      const testName = testResult.fullName || testResult.title;
      const testTime = testResult.duration || 0;
      
      totalTime += testTime;
      totalTests++;
      
      testDetails.push({
        file: filePath,
        name: testName,
        time: testTime,
        status: testResult.status
      });
    });
  });
  
  // Trier les tests par temps d'exécution
  testDetails.sort((a, b) => b.time - a.time);
  
  // Identifier les tests lents et rapides
  const slowTests = testDetails.filter(test => test.time >= SLOW_TEST_THRESHOLD && test.time < VERY_SLOW_TEST_THRESHOLD);
  const verySlowTests = testDetails.filter(test => test.time >= VERY_SLOW_TEST_THRESHOLD);
  const fastestTests = testDetails.slice(-5).reverse(); // 5 tests les plus rapides
  
  const averageTime = totalTests > 0 ? totalTime / totalTests : 0;
  
  return {
    totalTests,
    totalTime,
    slowTests,
    verySlowTests,
    fastestTests,
    averageTime,
    testDetails
  };
}

// Fonction pour analyser le contenu des tests lents
function analyzeSlowTests(slowTests) {
  const analysis = [];
  
  for (const test of slowTests) {
    try {
      const content = fs.readFileSync(test.file, 'utf8');
      const lines = content.split('\n');
      
      // Rechercher des patterns qui peuvent causer des tests lents
      const hasNestedDescribe = content.match(/describe\([^)]*describe\(/g) !== null;
      const hasManyExpects = (content.match(/expect\(/g) || []).length > 10;
      const hasComplexBeforeEach = content.includes('beforeEach') && content.includes('await') && content.includes('for');
      const hasSleep = content.includes('setTimeout') || content.includes('setInterval');
      
      analysis.push({
        file: test.file,
        name: test.name,
        time: test.time,
        issues: {
          hasNestedDescribe,
          hasManyExpects,
          hasComplexBeforeEach,
          hasSleep
        }
      });
    } catch (error) {
      console.error(`Erreur lors de l'analyse du test ${test.file}: ${error.message}`);
    }
  }
  
  return analysis;
}

// Fonction pour générer des suggestions d'optimisation
function generateOptimizationSuggestions(analysis) {
  const suggestions = [];
  
  for (const test of analysis) {
    const testSuggestions = [];
    
    if (test.issues.hasNestedDescribe) {
      testSuggestions.push('Éviter les describe imbriqués qui peuvent ralentir l\'exécution des tests');
    }
    
    if (test.issues.hasManyExpects) {
      testSuggestions.push('Réduire le nombre d\'assertions (expects) ou les diviser en plusieurs tests plus petits');
    }
    
    if (test.issues.hasComplexBeforeEach) {
      testSuggestions.push('Simplifier les hooks beforeEach, en particulier ceux avec des opérations asynchrones ou des boucles');
    }
    
    if (test.issues.hasSleep) {
      testSuggestions.push('Remplacer setTimeout/setInterval par des mocks de temps ou des promesses');
    }
    
    suggestions.push({
      file: test.file,
      name: test.name,
      time: test.time,
      suggestions: testSuggestions
    });
  }
  
  return suggestions;
}

// Fonction pour générer un rapport de performance
function generatePerformanceReport(results, analysis, suggestions) {
  const { totalTests, totalTime, slowTests, verySlowTests, fastestTests, averageTime } = results;
  
  const report = `# Rapport de performance des tests

## Résumé

- **Tests exécutés:** ${totalTests}
- **Temps total:** ${(totalTime / 1000).toFixed(2)} secondes
- **Temps moyen par test:** ${averageTime.toFixed(2)} ms
- **Tests lents (>${SLOW_TEST_THRESHOLD} ms):** ${slowTests.length}
- **Tests très lents (>${VERY_SLOW_TEST_THRESHOLD} ms):** ${verySlowTests.length}

## Tests les plus lents

${verySlowTests.length > 0 ? verySlowTests.map(test => `- **${test.name}** (${test.time.toFixed(2)} ms) - ${path.basename(test.file)}`).join('\n') : 'Aucun test très lent.'}

## Tests lents

${slowTests.length > 0 ? slowTests.map(test => `- **${test.name}** (${test.time.toFixed(2)} ms) - ${path.basename(test.file)}`).join('\n') : 'Aucun test lent.'}

## Tests les plus rapides

${fastestTests.length > 0 ? fastestTests.map(test => `- **${test.name}** (${test.time.toFixed(2)} ms) - ${path.basename(test.file)}`).join('\n') : 'Aucun test rapide.'}

## Suggestions d'optimisation

${suggestions.length > 0 ? suggestions.map(sugg => {
  if (sugg.suggestions.length === 0) return '';
  return `### ${path.basename(sugg.file)} - ${sugg.name} (${sugg.time.toFixed(2)} ms)

${sugg.suggestions.map(s => `- ${s}`).join('\n')}
`;
}).join('\n') : 'Aucune suggestion d\'optimisation.'}

## Recommandations générales

1. **Paralléliser les tests**: Utiliser \`jest --parallel\` pour exécuter les tests en parallèle
2. **Mocker les dépendances externes**: Remplacer les appels API, bases de données, etc. par des mocks
3. **Optimiser les beforeEach/afterEach**: Minimiser le code d'initialisation et de nettoyage
4. **Utiliser des snapshots**: Pour les tests de composants UI, les snapshots sont plus rapides que les assertions manuelles
5. **Éviter les timeouts**: Remplacer \`setTimeout\` par \`jest.useFakeTimers()\` et \`jest.runAllTimers()\`

Rapport généré le ${new Date().toISOString()}
`;

  fs.writeFileSync(REPORT_PATH, report);
  console.log(`Rapport de performance généré: ${REPORT_PATH}`);
  
  return report;
}

// Fonction principale
async function main() {
  // Vérifier les arguments
  const args = process.argv.slice(2);
  const testPattern = args[0] || 'Backend/src/tests/**/*.test.ts';
  
  console.log(`Exécution des tests avec mesure du temps: ${testPattern}`);
  const testResults = await runTestsWithTimer(testPattern);
  
  if (!testResults) {
    console.error('Aucun résultat de test disponible');
    return;
  }
  
  console.log('Analyse des résultats des tests...');
  const results = analyzeTestResults(testResults);
  
  console.log('Analyse des tests lents...');
  const slowTestsAnalysis = analyzeSlowTests([...results.slowTests, ...results.verySlowTests]);
  
  console.log('Génération des suggestions d\'optimisation...');
  const optimizationSuggestions = generateOptimizationSuggestions(slowTestsAnalysis);
  
  console.log('Génération du rapport de performance...');
  generatePerformanceReport(results, slowTestsAnalysis, optimizationSuggestions);
  
  // Nettoyage
  if (fs.existsSync('jest-results.json')) {
    fs.unlinkSync('jest-results.json');
  }
  
  console.log('Terminé!');
}

// Exécuter le script
main().catch(error => {
  console.error(`Erreur lors de l'exécution du script: ${error.message}`);
  process.exit(1);
});
