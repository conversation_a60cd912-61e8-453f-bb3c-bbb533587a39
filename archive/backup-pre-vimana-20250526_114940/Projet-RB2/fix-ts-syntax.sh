#!/bin/bash
# Fonction pour corriger les erreurs de syntaxe courantes dans les fichiers TypeScript
find frontend/src -name "*.ts" -o -name "*.tsx" | xargs -I {} cp {} {}.bak
find frontend/src -name "*.ts" -o -name "*.tsx" | xargs -I {} sed -i "" -e "s/;);/);/g" {}
find frontend/src -name "*.ts" -o -name "*.tsx" | xargs -I {} sed -i "" -e "s/,;/,/g" {}
find frontend/src -name "*.ts" -o -name "*.tsx" | xargs -I {} sed -i "" -e "s/};/}/g" {}
find frontend/src -name "*.ts" -o -name "*.tsx" | xargs -I {} sed -i "" -e "s/{;}/{}/g" {}
find frontend/src -name "*.ts" -o -name "*.tsx" | xargs -I {} sed -i "" -e "s/\\'/'/g" {}
find frontend/src -name "*.ts" -o -name "*.tsx" | xargs -I {} sed -i "" -e "s/}\([a-zA-Z][a-zA-Z0-9]*\)/\1/g" {}
echo "Corrections syntaxiques terminées"
