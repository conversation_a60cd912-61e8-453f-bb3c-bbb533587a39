// Configuration globale pour les tests Jest
import 'jest-extended';

// Mock pour winston
jest.mock('winston', () => ({
  format: {
    colorize: jest.fn().mockReturnValue({}),
    combine: jest.fn().mockReturnValue({}),
    timestamp: jest.fn().mockReturnValue({}),
    printf: jest.fn().mockReturnValue({}),
    errors: jest.fn().mockReturnValue({}),
    json: jest.fn().mockReturnValue({}),
    prettyPrint: jest.fn().mockReturnValue({}),
    splat: jest.fn().mockReturnValue({}),
    simple: jest.fn().mockReturnValue({}),
    label: jest.fn().mockReturnValue({})
  },
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    log: jest.fn(),
    on: jest.fn(),
    add: jest.fn(),
    remove: jest.fn()
  }),
  addColors: jest.fn(),
  transports: {
    Console: jest.fn().mockImplementation(() => ({})),
    File: jest.fn().mockImplementation(() => ({}))
  },
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

// Mock pour le module logger utilisé dans le projet
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
  silly: jest.fn(),
  http: jest.fn(),
  log: jest.fn(),
  child: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })
};

jest.mock('../Backend/src/utils/logger', () => ({
  logger: mockLogger
}), { virtual: true });

// Mock pour winston-daily-rotate-file
jest.mock('winston-daily-rotate-file', () => 
  jest.fn().mockImplementation(() => ({
    on: jest.fn()
  }))
);

// Réinitialisation des mocks avant chaque test
beforeEach(() => {
  jest.clearAllMocks();
});

// Éviter les problèmes de timeout dans les tests
jest.setTimeout(30000);

// Utiliser des timers fake par défaut
jest.useFakeTimers();

// Mock pour winston-elasticsearch
jest.mock('winston-elasticsearch', () => {
  return {
    ElasticsearchTransport: jest.fn().mockImplementation(() => ({}))
  };
});

// Mock pour node-cache
jest.mock('node-cache', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    keys: jest.fn().mockReturnValue([]),
    has: jest.fn(),
    close: jest.fn()
  }));
});

// Mock pour @nestjs/common Injectable decorator
jest.mock('@nestjs/common', () => {
  return {
    Injectable: () => jest.fn()
  };
});

// Mock global fetch
global.fetch = require('jest-fetch-mock');

require('@testing-library/jest-native/extend-expect');

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

jest.mock('@react-native-community/netinfo', () => ({
  fetch: jest.fn(() => Promise.resolve({
    isConnected: true,
    type: 'wifi'
  })),
  addEventListener: jest.fn(() => {
    return () => {};
  })
})); 