{"compilerOptions": {"strict": false, "noImplicitAny": false, "allowJs": true, "checkJs": false, "jsx": "react", "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}, "include": ["src"]}