#!/usr/bin/env bash

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Exécution du hook pre-commit...${NC}"

# Récupérer la liste des fichiers modifiés
FILES=$(git diff --cached --name-only --diff-filter=ACMR | grep -E '\.(js|jsx|ts|tsx)$')

if [ -z "$FILES" ]; then
    echo -e "${BLUE}Aucun fichier JavaScript/TypeScript à vérifier.${NC}"
    exit 0
fi

# Afficher le nombre de fichiers à vérifier
echo -e "${BLUE}Fichiers à vérifier: $(echo "$FILES" | wc -l)${NC}"

# Note: La vérification ESLint est temporairement désactivée pendant la transition vers ESLint v9
echo -e "${YELLOW}La vérification ESLint est temporairement désactivée pendant la transition vers ESLint v9${NC}"

# Vérifier les erreurs TypeScript
echo -e "${BLUE}Vérification des erreurs TypeScript...${NC}"
if npx tsc --noEmit; then
    echo -e "${GREEN}Pas d'erreurs TypeScript trouvées.${NC}"
else
    # Temporairement, nous n'annulons pas le commit en cas d'erreurs TypeScript
    echo -e "${YELLOW}Des erreurs TypeScript ont été trouvées, mais le commit est autorisé pendant la transition.${NC}"
fi

echo -e "${GREEN}Toutes les vérifications sont passées !${NC}"
exit 0 