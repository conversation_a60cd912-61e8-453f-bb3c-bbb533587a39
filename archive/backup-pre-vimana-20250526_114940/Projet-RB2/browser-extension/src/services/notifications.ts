// Service de notifications pour l'extension de navigateur

// Interface pour les options de notification
export interface NotificationOptions {
  type: 'basic' | 'image' | 'list' | 'progress';
  iconUrl: string;
  title: string;
  message: string;
  priority?: number;
  contextMessage?: string;
  buttons?: { title: string; iconUrl?: string }[];
  imageUrl?: string;
  items?: { title: string; message: string }[];
  progress?: number;
}

// Classe de service pour gérer les notifications
export class NotificationService {
  // Méthode pour créer une notification
  static async createNotification(options: NotificationOptions): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // Vérifier si l'API chrome.notifications est disponible
        if (!chrome || !chrome.notifications) {
          throw new Error('API de notifications non disponible');
        }

        chrome.notifications.create({
          type: options.type,
          iconUrl: options.iconUrl,
          title: options.title,
          message: options.message,
          priority: options.priority,
          contextMessage: options.contextMessage,
          buttons: options.buttons,
          imageUrl: options.imageUrl,
          items: options.items,
          progress: options.progress
        }, (notificationId) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve(notificationId);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // Méthode pour effacer une notification
  static async clearNotification(notificationId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        // Vérifier si l'API chrome.notifications est disponible
        if (!chrome || !chrome.notifications) {
          throw new Error('API de notifications non disponible');
        }

        chrome.notifications.clear(notificationId, (wasCleared) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve(wasCleared);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // Méthode pour obtenir toutes les notifications
  static async getAll(): Promise<{ [notificationId: string]: any }> {
    return new Promise((resolve, reject) => {
      try {
        // Vérifier si l'API chrome.notifications est disponible
        if (!chrome || !chrome.notifications) {
          throw new Error('API de notifications non disponible');
        }

        chrome.notifications.getAll((notifications) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve(notifications);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}

export default NotificationService; 