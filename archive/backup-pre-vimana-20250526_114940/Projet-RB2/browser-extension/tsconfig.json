{"compilerOptions": {"target": "es2020", "module": "esnext", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"*": ["src/*"]}, "lib": ["dom", "dom.iterable", "esnext"], "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules"]}