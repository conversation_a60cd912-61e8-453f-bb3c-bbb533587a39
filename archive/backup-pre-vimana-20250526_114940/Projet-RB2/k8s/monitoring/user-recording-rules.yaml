apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: user-recording-rules
  namespace: monitoring
  labels:
    app: kube-prometheus-stack
    release: prometheus
spec:
  groups:
    - name: user.rules
      interval: 1m
      rules:
        # User Activity Rules
        - record: user:active_sessions:total
          expr: |
            sum(user_sessions_active)
        
        - record: user:registrations:rate:5m
          expr: |
            sum(rate(user_registrations_total[5m])) by (registration_type)
        
        - record: user:logins:success_rate:5m
          expr: |
            sum(rate(user_login_attempts_total{status="success"}[5m]))
            / sum(rate(user_login_attempts_total[5m])) * 100
        
        # Authentication Performance Rules
        - record: user:auth_duration:p95:5m
          expr: |
            histogram_quantile(0.95, sum(rate(user_auth_duration_seconds_bucket[5m])) by (le, auth_type))
        
        - record: user:auth_duration:p50:5m
          expr: |
            histogram_quantile(0.50, sum(rate(user_auth_duration_seconds_bucket[5m])) by (le, auth_type))
        
        - record: user:auth_errors:rate:5m
          expr: |
            sum(rate(user_auth_errors_total[5m])) by (error_type)
        
        # Profile Management Rules
        - record: user:profile_updates:rate:5m
          expr: |
            sum(rate(user_profile_updates_total[5m])) by (update_type)
        
        - record: user:profile_completeness:avg
          expr: |
            avg(user_profile_completeness_percent)
        
        # API Performance Rules
        - record: user:request_duration:p95:5m
          expr: |
            histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service="user"}[5m])) by (le, endpoint))
        
        - record: user:request_duration:p50:5m
          expr: |
            histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{service="user"}[5m])) by (le, endpoint))
        
        - record: user:error_rate_percent:5m
          expr: |
            sum(rate(http_requests_total{service="user", status=~"5.."}[5m])) by (endpoint)
            / sum(rate(http_requests_total{service="user"}[5m])) by (endpoint) * 100
        
        # Security Rules
        - record: user:failed_logins:rate:5m
          expr: |
            sum(rate(user_failed_login_attempts_total[5m])) by (reason)
        
        - record: user:password_resets:rate:5m
          expr: |
            sum(rate(user_password_reset_total[5m])) by (status)
        
        - record: user:suspicious_activity:rate:5m
          expr: |
            sum(rate(user_suspicious_activity_total[5m])) by (activity_type)
