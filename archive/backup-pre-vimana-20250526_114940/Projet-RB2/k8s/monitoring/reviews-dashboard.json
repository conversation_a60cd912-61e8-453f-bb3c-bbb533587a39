{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"title": "Review Activity", "type": "row", "collapsed": false, "panels": [{"title": "Reviews Submitted", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "sum(rate(reviews_submitted_total[5m])) by (service_type)", "legendFormat": "{{service_type}}"}]}, {"title": "Average Rating", "type": "gauge", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "avg(reviews_rating_average) by (service_type)", "legendFormat": "{{service_type}}"}], "options": {"fieldOptions": {"min": 0, "max": 5, "thresholds": [{"value": 2, "color": "red"}, {"value": 3.5, "color": "yellow"}, {"value": 4, "color": "green"}]}}}]}, {"title": "Review Processing", "type": "row", "collapsed": false, "panels": [{"title": "Processing Duration", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(review_processing_duration_seconds_bucket[5m])) by (le))", "legendFormat": "p95"}, {"expr": "histogram_quantile(0.50, sum(rate(review_processing_duration_seconds_bucket[5m])) by (le))", "legendFormat": "p50"}]}, {"title": "Processing Errors", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "sum(rate(review_processing_errors_total[5m])) by (error_type)", "legendFormat": "{{error_type}}"}]}]}, {"title": "Content Analysis", "type": "row", "collapsed": false, "panels": [{"title": "Sentiment Distribution", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "sum(reviews_sentiment_distribution) by (sentiment)", "legendFormat": "{{sentiment}}"}], "stack": true}, {"title": "Content Moderation", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "sum(rate(reviews_moderation_total[5m])) by (moderation_result)", "legendFormat": "{{moderation_result}}"}]}]}, {"title": "API Performance", "type": "row", "collapsed": false, "panels": [{"title": "Request Latency", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service=\"reviews\"}[5m])) by (le, endpoint))", "legendFormat": "p95 - {{endpoint}}"}, {"expr": "histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{service=\"reviews\"}[5m])) by (le, endpoint))", "legendFormat": "p50 - {{endpoint}}"}]}, {"title": "Error Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "sum(rate(http_requests_total{service=\"reviews\", status=~\"5..\"}[5m])) by (endpoint) / sum(rate(http_requests_total{service=\"reviews\"}[5m])) by (endpoint) * 100", "legendFormat": "{{endpoint}}"}]}]}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["reviews", "monitoring"], "templating": {"list": [{"name": "service_type", "type": "query", "query": "label_values(reviews_submitted_total, service_type)", "refresh": 2}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "Reviews Service Dashboard", "uid": "reviews", "version": 1}