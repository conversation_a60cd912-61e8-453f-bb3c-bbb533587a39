apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: frontend-recording-rules
  namespace: monitoring
  labels:
    app: kube-prometheus-stack
    release: prometheus
spec:
  groups:
    - name: frontend.rules
      interval: 1m
      rules:
        # Page Load Performance Rules
        - record: frontend:page_load_time:p95:5m
          expr: |
            histogram_quantile(0.95, sum(rate(frontend_page_load_time_seconds_bucket[5m])) by (le, page))
        
        - record: frontend:page_load_time:p50:5m
          expr: |
            histogram_quantile(0.50, sum(rate(frontend_page_load_time_seconds_bucket[5m])) by (le, page))
        
        # Error Rate Rules
        - record: frontend:error_rate:5m
          expr: |
            sum(rate(frontend_error_total[5m])) by (error_type, type)
        
        # User Activity Rules
        - record: frontend:active_users:total
          expr: |
            sum(frontend_active_users)
        
        - record: frontend:user_actions:rate:5m
          expr: |
            sum(rate(frontend_user_action_total[5m])) by (action)
        
        # API Performance Rules
        - record: frontend:api_request_duration:p95:5m
          expr: |
            histogram_quantile(0.95, sum(rate(frontend_api_request_duration_seconds_bucket[5m])) by (le, endpoint))
        
        - record: frontend:api_request_duration:p50:5m
          expr: |
            histogram_quantile(0.50, sum(rate(frontend_api_request_duration_seconds_bucket[5m])) by (le, endpoint))
        
        - record: frontend:api_error_rate:5m
          expr: |
            sum(rate(frontend_api_error_total[5m])) by (endpoint, error_type)
        
        # Resource Usage Rules
        - record: frontend:memory_usage:bytes
          expr: |
            process_resident_memory_bytes{service="frontend"}
        
        - record: frontend:cpu_usage:rate:5m
          expr: |
            rate(process_cpu_seconds_total{service="frontend"}[5m])
