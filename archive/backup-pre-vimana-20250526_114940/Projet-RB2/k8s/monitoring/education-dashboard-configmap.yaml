apiVersion: v1
kind: ConfigMap
metadata:
  name: education-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "true"
data:
  education-service-dashboard.json: |
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "panels": [
        {
          "title": "Service Overview",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Request Rate",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
              "targets": [
                {
                  "expr": "sum(rate(http_requests_total{service=\"education\"}[5m])) by (endpoint)",
                  "legendFormat": "{{endpoint}}"
                }
              ]
            },
            {
              "title": "Error Rate",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
              "targets": [
                {
                  "expr": "sum(rate(http_requests_total{service=\"education\",status=~\"5..\"}[5m])) by (endpoint) / sum(rate(http_requests_total{service=\"education\"}[5m])) by (endpoint) * 100",
                  "legendFormat": "{{endpoint}}"
                }
              ]
            }
          ]
        },
        {
          "title": "Course Metrics",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Course Enrollments",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8},
              "targets": [
                {
                  "expr": "sum(rate(education_course_enrollments_total[5m])) by (course_id)",
                  "legendFormat": "{{course_id}}"
                }
              ]
            },
            {
              "title": "Course Completion Rate",
              "type": "gauge",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8},
              "targets": [
                {
                  "expr": "sum(rate(education_course_completions_total[24h])) / sum(rate(education_course_enrollments_total[24h])) * 100",
                  "legendFormat": "Completion Rate"
                }
              ],
              "options": {
                "fieldOptions": {
                  "min": 0,
                  "max": 100,
                  "thresholds": [
                    { "value": 30, "color": "red" },
                    { "value": 50, "color": "yellow" },
                    { "value": 70, "color": "green" }
                  ]
                }
              }
            }
          ]
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": ["education", "monitoring"],
      "templating": {
        "list": [
          {
            "name": "course",
            "type": "query",
            "query": "label_values(education_course_enrollments_total, course_id)",
            "refresh": 2
          }
        ]
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ]
      },
      "timezone": "browser",
      "title": "Education Service Dashboard",
      "uid": "education-service",
      "version": 1
    }
