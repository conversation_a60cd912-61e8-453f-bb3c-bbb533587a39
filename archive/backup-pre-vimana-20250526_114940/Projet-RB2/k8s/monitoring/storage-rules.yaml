apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: storage-monitoring-rules
  namespace: monitoring
  labels:
    release: prometheus
spec:
  groups:
  - name: storage.rules
    rules:
    # IPFS Storage Rules
    - alert: IPFSStorageNearCapacity
      expr: |
        (sum(ipfs_repo_size_bytes) / sum(ipfs_repo_max_size_bytes)) * 100 > 85
      for: 5m
      labels:
        severity: warning
        service: ipfs
      annotations:
        summary: "IPFS Storage Near Capacity"
        description: "IPFS storage usage is above 85% (current value: {{ $value }}%)"
        runbook_url: "https://github.com/your-org/runbooks/ipfs-storage"

    - alert: IPFSStorageCritical
      expr: |
        (sum(ipfs_repo_size_bytes) / sum(ipfs_repo_max_size_bytes)) * 100 > 95
      for: 5m
      labels:
        severity: critical
        service: ipfs
      annotations:
        summary: "IPFS Storage Critical"
        description: "IPFS storage usage is above 95% (current value: {{ $value }}%)"
        runbook_url: "https://github.com/your-org/runbooks/ipfs-storage-critical"

    # IPFS Performance Rules
    - alert: IPFSHighLatency
      expr: |
        rate(ipfs_operation_duration_seconds_sum[5m]) / rate(ipfs_operation_duration_seconds_count[5m]) > 2
      for: 5m
      labels:
        severity: warning
        service: ipfs
      annotations:
        summary: "IPFS High Latency"
        description: "IPFS operations are taking longer than 2 seconds on average"

    # Blockchain Storage Rules
    - alert: BlockchainStorageGrowth
      expr: |
        rate(ethereum_blockchain_size_bytes[24h]) > 1e9
      for: 1h
      labels:
        severity: warning
        service: blockchain
      annotations:
        summary: "High Blockchain Storage Growth"
        description: "Blockchain storage is growing faster than 1GB per day"

    # Node Health Rules
    - alert: IPFSNodeDown
      expr: |
        up{job="ipfs"} == 0
      for: 5m
      labels:
        severity: critical
        service: ipfs
      annotations:
        summary: "IPFS Node Down"
        description: "IPFS node has been down for more than 5 minutes"

    # Peer Connection Rules
    - alert: IPFSLowPeerCount
      expr: |
        ipfs_peer_count < 3
      for: 10m
      labels:
        severity: warning
        service: ipfs
      annotations:
        summary: "Low IPFS Peer Count"
        description: "IPFS node has less than 3 peers connected"

    # File Operation Rules
    - alert: IPFSHighErrorRate
      expr: |
        rate(ipfs_operation_errors_total[5m]) / rate(ipfs_operation_total[5m]) * 100 > 5
      for: 5m
      labels:
        severity: warning
        service: ipfs
      annotations:
        summary: "High IPFS Error Rate"
        description: "IPFS operations error rate is above 5%"

    # Bandwidth Rules
    - alert: IPFSHighBandwidthUsage
      expr: |
        rate(ipfs_bandwidth_bytes_total[5m]) > 1e8
      for: 15m
      labels:
        severity: warning
        service: ipfs
      annotations:
        summary: "High IPFS Bandwidth Usage"
        description: "IPFS bandwidth usage is above 100MB/s for 15 minutes"
