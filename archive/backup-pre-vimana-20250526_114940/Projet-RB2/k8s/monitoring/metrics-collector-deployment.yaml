apiVersion: apps/v1
kind: Deployment
metadata:
  name: metrics-collector
  namespace: monitoring
  labels:
    app: metrics-collector
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metrics-collector
  template:
    metadata:
      labels:
        app: metrics-collector
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: metrics-collector
        image: metrics-collector:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 9090
          name: metrics
        env:
        - name: IPFS_HOST
          valueFrom:
            configMapKeyRef:
              name: ipfs-config
              key: host
        - name: IPFS_PORT
          valueFrom:
            configMapKeyRef:
              name: ipfs-config
              key: port
        - name: ETH_RPC_URL
          valueFrom:
            configMapKeyRef:
              name: ethereum-config
              key: rpc-url
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 9090
          initialDelaySeconds: 15
          periodSeconds: 20
        volumeMounts:
        - name: logs
          mountPath: /usr/src/app/logs
      volumes:
      - name: logs
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: metrics-collector
  namespace: monitoring
spec:
  selector:
    app: metrics-collector
  ports:
  - port: 9090
    targetPort: 9090
    name: metrics
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: metrics-collector
  namespace: monitoring
  labels:
    release: prometheus
spec:
  selector:
    matchLabels:
      app: metrics-collector
  endpoints:
  - port: metrics
    interval: 30s
