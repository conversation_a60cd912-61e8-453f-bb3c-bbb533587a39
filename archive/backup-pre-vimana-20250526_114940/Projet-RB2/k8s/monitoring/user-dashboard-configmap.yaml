apiVersion: v1
kind: ConfigMap
metadata:
  name: user-grafana-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "true"
data:
  user-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "panels": [
        {
          "title": "User Activity",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Active Users",
              "type": "stat",
              "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0},
              "targets": [
                {
                  "expr": "sum(user_sessions_active)",
                  "instant": true
                }
              ],
              "options": {
                "colorMode": "value",
                "graphMode": "area",
                "justifyMode": "auto",
                "orientation": "auto",
                "reduceOptions": {
                  "calcs": ["lastNotNull"],
                  "fields": "",
                  "values": false
                }
              }
            },
            {
              "title": "New User Registrations",
              "type": "graph",
              "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0},
              "targets": [
                {
                  "expr": "sum(rate(user_registrations_total[5m])) by (registration_type)",
                  "legendFormat": "{{registration_type}}"
                }
              ]
            },
            {
              "title": "Login Activity",
              "type": "graph",
              "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0},
              "targets": [
                {
                  "expr": "sum(rate(user_login_attempts_total[5m])) by (status)",
                  "legendFormat": "{{status}}"
                }
              ]
            }
          ]
        },
        {
          "title": "Authentication",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Authentication Duration",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8},
              "targets": [
                {
                  "expr": "histogram_quantile(0.95, sum(rate(user_auth_duration_seconds_bucket[5m])) by (le, auth_type))",
                  "legendFormat": "p95 - {{auth_type}}"
                },
                {
                  "expr": "histogram_quantile(0.50, sum(rate(user_auth_duration_seconds_bucket[5m])) by (le, auth_type))",
                  "legendFormat": "p50 - {{auth_type}}"
                }
              ]
            },
            {
              "title": "Authentication Errors",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8},
              "targets": [
                {
                  "expr": "sum(rate(user_auth_errors_total[5m])) by (error_type)",
                  "legendFormat": "{{error_type}}"
                }
              ]
            }
          ]
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": ["user", "monitoring"],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timezone": "browser",
      "title": "User Service Dashboard",
      "uid": "user",
      "version": 1
    }
