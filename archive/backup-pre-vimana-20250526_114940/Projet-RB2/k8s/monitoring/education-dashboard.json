{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"title": "Service Overview", "type": "row", "collapsed": false, "panels": [{"title": "Request Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "sum(rate(http_requests_total{service=\"education\"}[5m])) by (endpoint)", "legendFormat": "{{endpoint}}"}]}, {"title": "Error Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "sum(rate(http_requests_total{service=\"education\",status=~\"5..\"}[5m])) by (endpoint) / sum(rate(http_requests_total{service=\"education\"}[5m])) by (endpoint) * 100", "legendFormat": "{{endpoint}}"}]}]}, {"title": "Course Metrics", "type": "row", "collapsed": false, "panels": [{"title": "Course Enrollments", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "sum(rate(education_course_enrollments_total[5m])) by (course_id)", "legendFormat": "{{course_id}}"}]}, {"title": "Course Completion Rate", "type": "gauge", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "sum(rate(education_course_completions_total[24h])) / sum(rate(education_course_enrollments_total[24h])) * 100", "legendFormat": "Completion Rate"}], "options": {"fieldOptions": {"min": 0, "max": 100, "thresholds": [{"value": 30, "color": "red"}, {"value": 50, "color": "yellow"}, {"value": 70, "color": "green"}]}}}]}, {"title": "Course Quality", "type": "row", "collapsed": false, "panels": [{"title": "Course Ratings", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "avg_over_time(education_course_ratings_sum[1h]) / avg_over_time(education_course_ratings_count[1h])", "legendFormat": "{{course_id}}"}], "yaxes": [{"min": 0, "max": 5}]}, {"title": "Quiz Performance", "type": "heatmap", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "sum(rate(education_quiz_scores_bucket[1h])) by (le)", "format": "heatmap", "legendFormat": "{{le}}"}]}]}, {"title": "Lesson Analytics", "type": "row", "collapsed": false, "panels": [{"title": "Lesson Duration Distribution", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(education_lesson_duration_seconds_bucket[1h])) by (le))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, sum(rate(education_lesson_duration_seconds_bucket[1h])) by (le))", "legendFormat": "median"}]}, {"title": "Active Lessons", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "count(count by (lesson_id) (rate(education_lesson_duration_seconds_count[5m]) > 0))", "legendFormat": "Active Lessons"}]}]}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["education", "monitoring"], "templating": {"list": [{"name": "course", "type": "query", "query": "label_values(education_course_enrollments_total, course_id)", "refresh": 2}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "Education Service Dashboard", "uid": "education-service", "version": 1}