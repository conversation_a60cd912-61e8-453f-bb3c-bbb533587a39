apiVersion: apps/v1
kind: Deployment
metadata:
  name: ipfs-metrics-exporter
  namespace: default
  labels:
    app: ipfs-metrics
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ipfs-metrics
  template:
    metadata:
      labels:
        app: ipfs-metrics
    spec:
      containers:
        - name: ipfs-metrics-exporter
          image: node:16-alpine
          workingDir: /app
          command: ["node", "metrics/ipfs_exporter.js"]
          ports:
            - name: metrics
              containerPort: 9091
          env:
            - name: IPFS_HOST
              valueFrom:
                configMapKeyRef:
                  name: ipfs-config
                  key: ipfs_host
            - name: IPFS_PORT
              valueFrom:
                configMapKeyRef:
                  name: ipfs-config
                  key: ipfs_port
            - name: IPFS_PROTOCOL
              valueFrom:
                configMapKeyRef:
                  name: ipfs-config
                  key: ipfs_protocol
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 256Mi
          livenessProbe:
            httpGet:
              path: /health
              port: metrics
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: metrics
            initialDelaySeconds: 15
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: ipfs-metrics
  namespace: default
  labels:
    app: ipfs-metrics
spec:
  ports:
    - name: metrics
      port: 9091
      targetPort: metrics
  selector:
    app: ipfs-metrics
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: ipfs-metrics
  namespace: monitoring
  labels:
    release: prometheus
spec:
  selector:
    matchLabels:
      app: ipfs-metrics
  namespaceSelector:
    matchNames:
      - default
  endpoints:
    - port: metrics
      interval: 15s
      path: /metrics
