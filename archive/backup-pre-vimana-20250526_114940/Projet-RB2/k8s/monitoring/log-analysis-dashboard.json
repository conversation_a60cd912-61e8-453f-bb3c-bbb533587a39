{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"title": "Service Performance", "type": "row", "collapsed": false, "panels": [{"title": "API Response Times", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "rate({app=~\".+\"} |~ \"duration=\\\\d+\" | json | duration > 0 [5m]) by (app)", "legendFormat": "{{app}}", "refId": "A"}]}, {"title": "Error Distribution", "type": "pie", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "sum(rate({app=~\".+\"} |~ \"(?i)error|exception|fail\" [5m])) by (app)", "legendFormat": "{{app}}", "refId": "A"}]}]}, {"title": "User Activity", "type": "row", "collapsed": false, "panels": [{"title": "User Service Activity", "type": "graph", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "targets": [{"expr": "sum(rate({app=\"user\"} |~ \"(?i)login|register|profile\" [5m])) by (action)", "legendFormat": "{{action}}", "refId": "A"}]}]}, {"title": "Education Service", "type": "row", "collapsed": false, "panels": [{"title": "Course Views", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "sum(rate({app=\"education\"} |~ \"course viewed\" [5m])) by (course)", "legendFormat": "{{course}}", "refId": "A"}]}, {"title": "Course Completions", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "sum(rate({app=\"education\"} |~ \"course completed\" [5m])) by (course)", "legendFormat": "{{course}}", "refId": "A"}]}]}, {"title": "Insurance Comparisons", "type": "row", "collapsed": false, "panels": [{"title": "Comparison Requests", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "sum(rate({app=\"compare-insurance\"} |~ \"comparison requested\" [5m])) by (type)", "legendFormat": "{{type}}", "refId": "A"}]}, {"title": "Quote Generation", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "sum(rate({app=\"compare-insurance\"} |~ \"quote generated\" [5m])) by (provider)", "legendFormat": "{{provider}}", "refId": "A"}]}]}, {"title": "Reviews Service", "type": "row", "collapsed": false, "panels": [{"title": "Review Submissions", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "sum(rate({app=\"reviews\"} |~ \"review submitted\" [5m])) by (rating)", "legendFormat": "{{rating}} stars", "refId": "A"}]}, {"title": "Review Interactions", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "sum(rate({app=\"reviews\"} |~ \"(?i)like|comment\" [5m])) by (action)", "legendFormat": "{{action}}", "refId": "A"}]}]}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["logs", "analysis"], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": null, "definition": "label_values(app)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "Service", "multi": false, "name": "service", "options": [], "query": {"query": "label_values(app)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "Log Analysis Dashboard", "uid": "log-analysis", "version": 1}