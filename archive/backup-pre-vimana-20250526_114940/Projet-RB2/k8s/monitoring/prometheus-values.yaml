alertmanager:
  enabled: true
  config:
    global:
      resolve_timeout: 5m
    route:
      group_by: ['alertname', 'severity']
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 12h
      receiver: 'slack-notifications'
      routes:
      - receiver: 'pagerduty-critical'
        matchers:
        - severity = 'critical'
      - receiver: 'slack-notifications'
        matchers:
        - severity = 'warning'
    receivers:
    - name: 'slack-notifications'
      slack_configs:
      - channel: '#storage-alerts'
        api_url:
          name: alertmanager-slack-secret
          key: slack-token
    - name: 'pagerduty-critical'
      pagerduty_configs:
      - routing_key:
          name: alertmanager-pagerduty-secret
          key: pagerduty-key
        severity: '{{ if eq .GroupLabels.severity "critical" }}critical{{ else }}warning{{ end }}'
        description: '{{ .CommonAnnotations.description }}'
        client: 'AlertManager'
        client_url: 'https://alertmanager.your-domain.com'

prometheus:
  enabled: true
  prometheusSpec:
    serviceMonitorSelectorNilUsesHelmValues: false
    podMonitorSelectorNilUsesHelmValues: false
    ruleSelectorNilUsesHelmValues: false

grafana:
  enabled: true
  adminPassword: admin

prometheusOperator:
  enabled: true
