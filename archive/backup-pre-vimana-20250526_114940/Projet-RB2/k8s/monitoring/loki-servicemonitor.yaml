apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: loki
  namespace: monitoring
  labels:
    release: prometheus
spec:
  selector:
    matchLabels:
      app: loki
  namespaceSelector:
    matchNames:
      - monitoring
  endpoints:
    - port: http-metrics
      path: /metrics
      interval: 15s
      scrapeTimeout: 14s
      relabelings:
        - sourceLabels: [__meta_kubernetes_pod_node_name]
          targetLabel: node
        - sourceLabels: [__meta_kubernetes_namespace]
          targetLabel: namespace
        - sourceLabels: [__meta_kubernetes_pod_name]
          targetLabel: pod
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: service
      metricRelabelings:
        - sourceLabels: [__name__]
          regex: 'loki_.*'
          action: keep
