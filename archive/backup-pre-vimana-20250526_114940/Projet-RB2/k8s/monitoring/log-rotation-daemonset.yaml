apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: log-rotation
  namespace: monitoring
  labels:
    app: log-rotation
spec:
  selector:
    matchLabels:
      app: log-rotation
  template:
    metadata:
      labels:
        app: log-rotation
    spec:
      containers:
        - name: logrotate
          image: blacklabelops/logrotate:1.3
          env:
            - name: LOGROTATE_INTERVAL
              value: "daily"
            - name: LOGROTATE_CRONSCHEDULE
              value: "0 0 * * *"  # Run at midnight
          volumeMounts:
            - name: config
              mountPath: /etc/logrotate.d/custom
              subPath: logrotate.conf
            - name: varlog
              mountPath: /var/log
            - name: varlibdockercontainers
              mountPath: /var/lib/docker/containers
              readOnly: true
          resources:
            requests:
              cpu: 50m
              memory: 64Mi
            limits:
              cpu: 100m
              memory: 128Mi
          securityContext:
            privileged: true  # Required for log rotation
      volumes:
        - name: config
          configMap:
            name: log-rotation-config
        - name: varlog
          hostPath:
            path: /var/log
        - name: varlibdockercontainers
          hostPath:
            path: /var/lib/docker/containers
