apiVersion: monitoring.coreos.com/v1alpha1
kind: AlertmanagerConfig
metadata:
  name: team-routes
  namespace: monitoring
spec:
  route:
    receiver: 'null'
    routes:
      # Platform Team Route
      - receiver: 'platform-team'
        matchers:
          - name: team
            value: platform
        continue: true
        routes:
          # Critical Platform Issues
          - receiver: 'platform-critical'
            matchers:
              - name: severity
                value: critical
            continue: true
          # Warning Platform Issues
          - receiver: 'platform-warnings'
            matchers:
              - name: severity
                value: warning
            continue: true

      # Security Team Route
      - receiver: 'security-team'
        matchers:
          - name: team
            value: security
        continue: true
        routes:
          # Critical Security Issues
          - receiver: 'security-critical'
            matchers:
              - name: severity
                value: critical
            continue: true
          # Warning Security Issues
          - receiver: 'security-warnings'
            matchers:
              - name: severity
                value: warning
            continue: true

      # Development Team Routes
      - receiver: 'dev-education'
        matchers:
          - name: app
            value: education
        continue: true
        routes:
          - receiver: 'dev-education-critical'
            matchers:
              - name: severity
                value: critical
            continue: true

      - receiver: 'dev-insurance'
        matchers:
          - name: app
            value: compare-insurance
        continue: true
        routes:
          - receiver: 'dev-insurance-critical'
            matchers:
              - name: severity
                value: critical
            continue: true

      - receiver: 'dev-reviews'
        matchers:
          - name: app
            value: reviews
        continue: true
        routes:
          - receiver: 'dev-reviews-critical'
            matchers:
              - name: severity
                value: critical
            continue: true

      # Business Team Route
      - receiver: 'business-team'
        matchers:
          - name: alert_type
            value: business
        continue: true

  receivers:
    # Platform Team Receivers
    - name: 'platform-team'
      slackConfigs:
        - channel: '#platform-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          
    - name: 'platform-critical'
      slackConfigs:
        - channel: '#platform-critical'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
      pagerdutyConfigs:
        - serviceKey: 'platform-critical-key'
          severity: critical
          
    - name: 'platform-warnings'
      slackConfigs:
        - channel: '#platform-warnings'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'

    # Security Team Receivers
    - name: 'security-team'
      slackConfigs:
        - channel: '#security-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          
    - name: 'security-critical'
      slackConfigs:
        - channel: '#security-critical'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
      pagerdutyConfigs:
        - serviceKey: 'security-critical-key'
          severity: critical
          
    - name: 'security-warnings'
      slackConfigs:
        - channel: '#security-warnings'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'

    # Development Team Receivers
    - name: 'dev-education'
      slackConfigs:
        - channel: '#education-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          
    - name: 'dev-education-critical'
      slackConfigs:
        - channel: '#education-critical'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
      emailConfigs:
        - to: '<EMAIL>'
          sendResolved: true
          subject: '{{ template "email.default.subject" . }}'
          html: '{{ template "email.default.html" . }}'

    - name: 'dev-insurance'
      slackConfigs:
        - channel: '#insurance-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          
    - name: 'dev-insurance-critical'
      slackConfigs:
        - channel: '#insurance-critical'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
      emailConfigs:
        - to: '<EMAIL>'
          sendResolved: true
          subject: '{{ template "email.default.subject" . }}'
          html: '{{ template "email.default.html" . }}'

    - name: 'dev-reviews'
      slackConfigs:
        - channel: '#reviews-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          
    - name: 'dev-reviews-critical'
      slackConfigs:
        - channel: '#reviews-critical'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
      emailConfigs:
        - to: '<EMAIL>'
          sendResolved: true
          subject: '{{ template "email.default.subject" . }}'
          html: '{{ template "email.default.html" . }}'

    # Business Team Receiver
    - name: 'business-team'
      slackConfigs:
        - channel: '#business-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
      emailConfigs:
        - to: '<EMAIL>'
          sendResolved: true
          subject: '{{ template "email.default.subject" . }}'
          html: '{{ template "email.default.html" . }}'
