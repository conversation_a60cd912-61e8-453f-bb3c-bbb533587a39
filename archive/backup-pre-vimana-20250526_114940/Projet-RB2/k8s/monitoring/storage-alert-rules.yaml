apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: storage-alerts
  namespace: monitoring
  labels:
    release: prometheus
spec:
  groups:
    - name: storage.rules
      rules:
        # IPFS Storage Alerts
        - alert: IPFSStorageNearCapacity
          expr: |
            (ipfs_storage_bytes_total / ipfs_storage_max_bytes) * 100 > 85
          for: 10m
          labels:
            severity: warning
            service: ipfs
          annotations:
            summary: IPFS storage usage high
            description: IPFS storage is using more than 85% of capacity for more than 10m.
            runbook_url: https://wiki.example.com/ipfs/storage-capacity

        - alert: IPFSStorageCritical
          expr: |
            (ipfs_storage_bytes_total / ipfs_storage_max_bytes) * 100 > 95
          for: 5m
          labels:
            severity: critical
            service: ipfs
          annotations:
            summary: IPFS storage critically full
            description: IPFS storage is using more than 95% of capacity for more than 5m.
            runbook_url: https://wiki.example.com/ipfs/storage-critical

        - alert: IPFSHighLatency
          expr: |
            rate(ipfs_operation_duration_seconds_sum[5m]) / rate(ipfs_operation_duration_seconds_count[5m]) > 2
          for: 5m
          labels:
            severity: warning
            service: ipfs
          annotations:
            summary: IPFS operations high latency
            description: IPFS operations are taking longer than 2 seconds on average.
            runbook_url: https://wiki.example.com/ipfs/high-latency

        - alert: IPFSLowPeerCount
          expr: |
            ipfs_peers_connected < 3
          for: 15m
          labels:
            severity: warning
            service: ipfs
          annotations:
            summary: IPFS low peer count
            description: IPFS node has less than 3 connected peers for more than 15m.
            runbook_url: https://wiki.example.com/ipfs/peer-connectivity

        # Blockchain Storage Alerts
        - alert: BlockchainStorageGrowthHigh
          expr: |
            rate(blockchain_storage_bytes_total[24h]) > 1e9
          for: 1h
          labels:
            severity: warning
            service: blockchain
          annotations:
            summary: High blockchain storage growth
            description: Blockchain storage is growing faster than 1GB per day.
            runbook_url: https://wiki.example.com/blockchain/storage-growth

        - alert: TokenTransferFailureRate
          expr: |
            rate(token_transfer_failures_total[5m]) / rate(token_transfers_total[5m]) * 100 > 5
          for: 5m
          labels:
            severity: critical
            service: blockchain
          annotations:
            summary: High token transfer failure rate
            description: More than 5% of token transfers are failing.
            runbook_url: https://wiki.example.com/blockchain/transfer-failures

        - alert: ContractGasUsageHigh
          expr: |
            rate(contract_gas_used_total[5m]) > 5e6
          for: 15m
          labels:
            severity: warning
            service: blockchain
          annotations:
            summary: High contract gas usage
            description: Smart contract is using more gas than usual.
            runbook_url: https://wiki.example.com/blockchain/gas-usage

        - alert: StorageNodeOffline
          expr: |
            up{job=~"ipfs|blockchain"} == 0
          for: 5m
          labels:
            severity: critical
            service: storage
          annotations:
            summary: Storage node offline
            description: A storage node (IPFS or blockchain) has been offline for more than 5m.
            runbook_url: https://wiki.example.com/storage/node-offline
