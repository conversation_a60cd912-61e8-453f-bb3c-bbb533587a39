apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: application-alerts
  namespace: monitoring
spec:
  groups:
  - name: application.rules
    rules:
    # Alertes de performance
    - alert: APIHighLatency
      expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 0.5
      for: 5m
      labels:
        severity: warning
        service: api
      annotations:
        summary: "API Latency élevée"
        description: "Le temps de réponse moyen est supérieur à 500ms sur 5 minutes"

    - alert: APICriticalLatency
      expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 2
      for: 2m
      labels:
        severity: critical
        service: api
      annotations:
        summary: "API Latency critique"
        description: "Le temps de réponse moyen est supérieur à 2s sur 2 minutes"

    # Alertes de disponibilité
    - alert: APIHighErrorRate
      expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) > 0.05
      for: 5m
      labels:
        severity: warning
        service: api
      annotations:
        summary: "Taux d'erreur élevé"
        description: "Plus de 5% des requêtes retournent des erreurs 5xx"

    - alert: APICriticalErrorRate
      expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) > 0.15
      for: 2m
      labels:
        severity: critical
        service: api
      annotations:
        summary: "Taux d'erreur critique"
        description: "Plus de 15% des requêtes retournent des erreurs 5xx"

    # Alertes de ressources
    - alert: HighCPUUsage
      expr: sum(rate(container_cpu_usage_seconds_total{container!=""}[5m])) by (pod) > 0.8
      for: 5m
      labels:
        severity: warning
        service: api
      annotations:
        summary: "Utilisation CPU élevée"
        description: "Le pod {{ $labels.pod }} utilise plus de 80% du CPU"

    - alert: HighMemoryUsage
      expr: sum(container_memory_working_set_bytes{container!=""}) by (pod) / sum(container_spec_memory_limit_bytes{container!=""}) by (pod) > 0.85
      for: 5m
      labels:
        severity: warning
        service: api
      annotations:
        summary: "Utilisation mémoire élevée"
        description: "Le pod {{ $labels.pod }} utilise plus de 85% de sa mémoire"

  - name: services.rules
    rules:
    # Alertes de service Compare-Insurance
    - alert: CompareInsuranceHighLatency
      expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service="compare-insurance"}[5m])) by (le)) > 1
      for: 5m
      labels:
        severity: warning
        service: compare-insurance
      annotations:
        summary: "Compare-Insurance latence élevée"
        description: "95% des requêtes prennent plus d'1s"

    # Alertes de service Analyzer
    - alert: AnalyzerHighLatency
      expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service="analyzer"}[5m])) by (le)) > 1
      for: 5m
      labels:
        severity: warning
        service: analyzer
      annotations:
        summary: "Analyzer latence élevée"
        description: "95% des requêtes prennent plus d'1s"

  - name: business.rules
    rules:
    # Alertes métier
    - alert: HighFailedComparisons
      expr: rate(insurance_comparison_failed_total[15m]) / rate(insurance_comparison_total[15m]) > 0.1
      for: 15m
      labels:
        severity: warning
        service: business
      annotations:
        summary: "Taux élevé de comparaisons échouées"
        description: "Plus de 10% des comparaisons d'assurance échouent"

    - alert: LowConversionRate
      expr: rate(insurance_purchase_success_total[1h]) / rate(insurance_comparison_total[1h]) < 0.02
      for: 1h
      labels:
        severity: warning
        service: business
      annotations:
        summary: "Taux de conversion faible"
        description: "Le taux de conversion est inférieur à 2% sur la dernière heure"
