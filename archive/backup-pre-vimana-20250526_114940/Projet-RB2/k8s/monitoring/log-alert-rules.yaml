apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: log-alerts
  namespace: monitoring
  labels:
    prometheus: k8s
    role: alert-rules
spec:
  groups:
    - name: log.alerts
      rules:
        # High Error Rate Alerts
        - alert: HighErrorRate
          expr: |
            sum(rate({app=~".+"} |~ "(?i)error|exception|fail" [5m])) by (app) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: High error rate in {{ $labels.app }}
            description: Error rate is above 0.1 errors/second for {{ $labels.app }}

        # Service Down Alert
        - alert: ServiceNotLogging
          expr: |
            absent(rate({app=~".+"}[10m])) == 1
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: No logs from {{ $labels.app }}
            description: No logs received from {{ $labels.app }} in the last 10 minutes

        # Slow API Response
        - alert: SlowAPIResponse
          expr: |
            sum by (app) (
              rate({app=~".+"} |~ "duration=\\d+" | json | duration > 1000 [5m])
            ) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Slow API responses in {{ $labels.app }}
            description: Multiple API requests taking longer than 1 second in {{ $labels.app }}

        # Authentication Failures
        - alert: HighAuthFailures
          expr: |
            sum by (app) (
              rate({app=~".+"} |~ "(?i)authentication failed|unauthorized|forbidden" [5m])
            ) > 0.05
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: High rate of authentication failures
            description: Authentication failures rate is above threshold in {{ $labels.app }}

        # Database Errors
        - alert: DatabaseErrors
          expr: |
            sum by (app) (
              rate({app=~".+"} |~ "(?i)database error|db connection|sql error" [5m])
            ) > 0
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Database errors detected
            description: Database-related errors detected in {{ $labels.app }}

        # High 4xx Error Rate
        - alert: High4xxErrorRate
          expr: |
            sum by (app) (
              rate({app=~".+"} |~ "status=(4|400|401|403|404)" [5m])
            ) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: High rate of 4xx errors
            description: Rate of 4xx errors is above threshold in {{ $labels.app }}

        # High 5xx Error Rate
        - alert: High5xxErrorRate
          expr: |
            sum by (app) (
              rate({app=~".+"} |~ "status=(5|500|501|502|503|504)" [5m])
            ) > 0.05
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: High rate of 5xx errors
            description: Rate of 5xx errors is above threshold in {{ $labels.app }}

        # Memory Leak Indicators
        - alert: PotentialMemoryLeak
          expr: |
            sum by (app) (
              rate({app=~".+"} |~ "(?i)out of memory|memory limit|memory pressure" [5m])
            ) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Potential memory leak detected
            description: Memory-related issues detected in {{ $labels.app }}

        # Business Logic Alerts
        - alert: LowCourseCompletionRate
          expr: |
            (
              sum(rate({app="education"} |~ "course completed" [1h])) /
              sum(rate({app="education"} |~ "course started" [1h]))
            ) < 0.3
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: Low course completion rate
            description: Course completion rate has dropped below 30%

        - alert: HighInsuranceQuoteFailure
          expr: |
            (
              sum(rate({app="compare-insurance"} |~ "quote failed" [5m])) /
              sum(rate({app="compare-insurance"} |~ "quote requested" [5m]))
            ) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: High insurance quote failure rate
            description: More than 10% of insurance quote requests are failing

        - alert: ReviewSubmissionSpike
          expr: |
            sum(rate({app="reviews"} |~ "review submitted" [5m])) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Unusual spike in review submissions
            description: Review submission rate is unusually high
