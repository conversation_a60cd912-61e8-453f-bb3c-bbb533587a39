apiVersion: apps/v1
kind: Deployment
metadata:
  name: blockchain-metrics-exporter
  namespace: default
  labels:
    app: blockchain-metrics
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blockchain-metrics
  template:
    metadata:
      labels:
        app: blockchain-metrics
    spec:
      containers:
        - name: blockchain-metrics-exporter
          image: node:16-alpine
          workingDir: /app
          command: ["node", "metrics/blockchain_exporter.js"]
          ports:
            - name: metrics
              containerPort: 9090
          env:
            - name: WEB3_PROVIDER
              valueFrom:
                configMapKeyRef:
                  name: blockchain-config
                  key: web3_provider
            - name: TOKEN_CONTRACT_ADDRESS
              valueFrom:
                configMapKeyRef:
                  name: blockchain-config
                  key: token_contract_address
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 256Mi
          livenessProbe:
            httpGet:
              path: /health
              port: metrics
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: metrics
            initialDelaySeconds: 15
            periodSeconds: 5
          volumeMounts:
            - name: contract-artifacts
              mountPath: /app/build/contracts
      volumes:
        - name: contract-artifacts
          configMap:
            name: contract-artifacts
