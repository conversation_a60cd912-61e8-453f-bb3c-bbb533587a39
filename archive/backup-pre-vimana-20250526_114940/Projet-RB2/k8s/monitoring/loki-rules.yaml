apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-rules
  namespace: monitoring
data:
  rules.yaml: |
    groups:
      - name: error_rates
        interval: 5m
        rules:
          - record: error_rate_high
            expr: |
              sum(rate({app=~".+"} |~ "(?i)error|exception|fail" [5m])) by (app) > 0.1
            labels:
              severity: warning
            annotations:
              summary: High error rate for {{ $labels.app }}
              description: Error rate is above 0.1 errors/second for {{ $labels.app }}

      - name: service_health
        interval: 5m
        rules:
          - record: service_down
            expr: |
              absent(rate({app=~".+"}[5m])) == 1
            labels:
              severity: critical
            annotations:
              summary: Service {{ $labels.app }} is down
              description: No logs received from {{ $labels.app }} in the last 5 minutes

      - name: api_performance
        interval: 5m
        rules:
          - record: slow_api_requests
            expr: |
              sum by (app) (
                rate({app=~".+"} |~ "(?i)duration.*ms" [5m])
                | json | duration > 1000
              )
            labels:
              severity: warning
            annotations:
              summary: Slow API requests in {{ $labels.app }}
              description: Multiple requests taking longer than 1 second in {{ $labels.app }}

      - name: security
        interval: 5m
        rules:
          - record: auth_failures
            expr: |
              sum by (app) (
                rate({app=~".+"} |~ "(?i)authentication failed|unauthorized|forbidden" [5m])
              )
            labels:
              severity: warning
            annotations:
              summary: High rate of authentication failures in {{ $labels.app }}
              description: Multiple authentication failures detected in {{ $labels.app }}

      - name: business_metrics
        interval: 5m
        rules:
          - record: education_course_views
            expr: |
              sum(rate({app="education"} |~ "course viewed" [5m]))
          - record: insurance_comparisons
            expr: |
              sum(rate({app="compare-insurance"} |~ "insurance compared" [5m]))
          - record: review_submissions
            expr: |
              sum(rate({app="reviews"} |~ "review submitted" [5m]))
          - record: user_registrations
            expr: |
              sum(rate({app="user"} |~ "user registered" [5m]))
