apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-slack-secret
  namespace: monitoring
type: Opaque
data:
  # Token is base64 encoded and should be replaced with a new, secure token
  slack-token: "BASE64_ENCODED_NEW_TOKEN"
---
apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-pagerduty-secret
  namespace: monitoring
type: Opaque
stringData:
  pagerduty-key: "your-pagerduty-routing-key"  # Replace with actual PagerDuty routing key
---
apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-email-secret
  namespace: monitoring
type: Opaque
stringData:
  smtp-auth-username: "<EMAIL>"  # Replace with actual SMTP username
  smtp-auth-password: "your-smtp-password"  # Replace with actual SMTP password
