apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: retreatandbe-alerts
  namespace: monitoring
spec:
  groups:
  - name: node
    rules:
    - alert: HighCPUUsage
      expr: instance:node_cpu_utilisation:rate5m > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High CPU usage on {{ $labels.instance }}
        description: CPU usage is above 80% for 5 minutes

    - alert: HighMemoryUsage
      expr: instance:node_memory_utilisation:rate5m > 0.85
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High memory usage on {{ $labels.instance }}
        description: Memory usage is above 85% for 5 minutes

    - alert: DiskSpaceRunningOut
      expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Disk space running out on {{ $labels.instance }}
        description: Disk space is below 10% on {{ $labels.mountpoint }}

  - name: kubernetes
    rules:
    - alert: PodCrashLooping
      expr: rate(kube_pod_container_status_restarts_total[5m]) > 3
      for: 15m
      labels:
        severity: critical
      annotations:
        summary: Pod {{ $labels.pod }} is crash looping
        description: Pod {{ $labels.pod }} is restarting more than 3 times per 5 minutes

    - alert: PodNotReady
      expr: sum by (pod) (kube_pod_status_phase{phase=~"Pending|Unknown"}) > 0
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: Pod {{ $labels.pod }} is not ready
        description: Pod has been in a non-ready state for more than 15 minutes

    - alert: NodeNotReady
      expr: kube_node_status_condition{condition="Ready",status="false"} == 1
      for: 15m
      labels:
        severity: critical
      annotations:
        summary: Node {{ $labels.node }} not ready
        description: Node has been in NotReady state for more than 15 minutes

  - name: application
    rules:
    - alert: HighErrorRate
      expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: High error rate for {{ $labels.service }}
        description: Error rate is above 5% for 5 minutes

    - alert: SlowResponses
      expr: http_request_duration_seconds{quantile="0.9"} > 2
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Slow responses from {{ $labels.service }}
        description: 90th percentile of response times is above 2 seconds

    - alert: HighRequestLatency
      expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High request latency on {{ $labels.service }}
        description: Average request latency is above 1 second for 5 minutes
