{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"title": "Log Volume", "type": "row", "collapsed": false, "panels": [{"title": "Log Lines by App", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "sum(rate({app=~\".+\"}[5m])) by (app)", "legendFormat": "{{app}}", "refId": "A"}]}, {"title": "Error Rate by App", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "sum(rate({app=~\".+\"} |~ \"(?i)error|exception|fail\" [5m])) by (app)", "legendFormat": "{{app}}", "refId": "A"}]}]}, {"title": "Application Logs", "type": "row", "collapsed": false, "panels": [{"title": "Education Service Logs", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "targets": [{"expr": "{app=\"education\"} | json", "refId": "A"}]}, {"title": "Compare-Insurance Service Logs", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "targets": [{"expr": "{app=\"compare-insurance\"} | json", "refId": "A"}]}, {"title": "Reviews Service Logs", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "targets": [{"expr": "{app=\"reviews\"} | json", "refId": "A"}]}, {"title": "User Service Logs", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "targets": [{"expr": "{app=\"user\"} | json", "refId": "A"}]}]}, {"title": "System Logs", "type": "row", "collapsed": false, "panels": [{"title": "Kubernetes Events", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}, "targets": [{"expr": "{app=\"kube-apiserver\"} | json", "refId": "A"}]}]}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["logs", "monitoring"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "Application Logs Dashboard", "uid": "logs", "version": 1}