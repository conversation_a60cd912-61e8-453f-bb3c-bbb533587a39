apiVersion: monitoring.coreos.com/v1
kind: AlertmanagerConfig
metadata:
  name: insurance-services-alerts
  namespace: monitoring
spec:
  route:
    groupBy: ['alertname', 'service']
    groupWait: 30s
    groupInterval: 5m
    repeatInterval: 4h
    routes:
      - receiver: 'pagerduty'
        matchers:
          - name: 'severity'
            value: 'critical'
      - receiver: 'slack'
        matchers:
          - name: 'severity'
            value: 'warning'
  receivers:
    - name: 'slack'
      slackConfigs:
        - channel: '#alerts'
          apiURL:
            name: 'slack-webhook'
            key: 'url'
          title: '{{ .GroupLabels.alertname }}'
          text: >-
            {{ range .Alerts }}
            *Alert:* {{ .Labels.alertname }}
            *Service:* {{ .Labels.service }}
            *Severity:* {{ .Labels.severity }}
            *Description:* {{ .Annotations.description }}
            *Value:* {{ .Annotations.value }}
            {{ end }}
    - name: 'pagerduty'
      pagerdutyConfigs:
        - routingKey:
            name: 'pagerduty-key'
            key: 'routingKey'
          description: >-
            {{ range .Alerts }}
            Alert: {{ .Labels.alertname }}
            Service: {{ .Labels.service }}
            Severity: {{ .Labels.severity }}
            Description: {{ .Annotations.description }}
            Value: {{ .Annotations.value }}
            {{ end }}
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: insurance-services-rules
  namespace: monitoring
  labels:
    prometheus: k8s
    role: alert-rules
spec:
  groups:
    - name: insurance-services
      rules:
        # Latence API
        - alert: HighAPILatency
          expr: |
            rate(http_request_duration_seconds_sum{route=~"/compare|/analyze"}[5m]) / 
            rate(http_request_duration_seconds_count{route=~"/compare|/analyze"}[5m]) > 2
          for: 5m
          labels:
            severity: warning
            service: '{{ $labels.service }}'
          annotations:
            description: 'API latency is above 2 seconds for {{ $labels.route }}'
            value: '{{ $value }}s'

        - alert: CriticalAPILatency
          expr: |
            rate(http_request_duration_seconds_sum{route=~"/compare|/analyze"}[5m]) / 
            rate(http_request_duration_seconds_count{route=~"/compare|/analyze"}[5m]) > 5
          for: 5m
          labels:
            severity: critical
            service: '{{ $labels.service }}'
          annotations:
            description: 'API latency is above 5 seconds for {{ $labels.route }}'
            value: '{{ $value }}s'

        # Taux d'erreur
        - alert: HighErrorRate
          expr: |
            (
              sum(rate(insurance_comparison_failed_total[5m])) / 
              sum(rate(insurance_comparison_total[5m])) * 100 > 5
            ) or (
              sum(rate(insurance_analysis_errors_total[5m])) / 
              sum(rate(insurance_analysis_total[5m])) * 100 > 5
            )
          for: 5m
          labels:
            severity: warning
            service: '{{ $labels.service }}'
          annotations:
            description: 'Error rate is above 5% for {{ $labels.service }}'
            value: '{{ $value }}%'

        - alert: CriticalErrorRate
          expr: |
            (
              sum(rate(insurance_comparison_failed_total[5m])) / 
              sum(rate(insurance_comparison_total[5m])) * 100 > 15
            ) or (
              sum(rate(insurance_analysis_errors_total[5m])) / 
              sum(rate(insurance_analysis_total[5m])) * 100 > 15
            )
          for: 5m
          labels:
            severity: critical
            service: '{{ $labels.service }}'
          annotations:
            description: 'Error rate is above 15% for {{ $labels.service }}'
            value: '{{ $value }}%'

        # Taux de requêtes
        - alert: HighRequestRate
          expr: |
            sum(rate(http_requests_total{route=~"/compare|/analyze"}[5m])) by (route) > 100
          for: 5m
          labels:
            severity: warning
            service: '{{ $labels.service }}'
          annotations:
            description: 'Request rate is above 100 req/s for {{ $labels.route }}'
            value: '{{ $value }} req/s'

        # Disponibilité du service
        - alert: ServiceDown
          expr: up{job=~"compare-insurance|analyzer"} == 0
          for: 1m
          labels:
            severity: critical
            service: '{{ $labels.job }}'
          annotations:
            description: 'Service {{ $labels.job }} is down'
            value: 'Service is not responding'
