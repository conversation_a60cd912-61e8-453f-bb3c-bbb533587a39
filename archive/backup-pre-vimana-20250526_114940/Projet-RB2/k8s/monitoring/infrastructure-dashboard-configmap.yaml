apiVersion: v1
kind: ConfigMap
metadata:
  name: infrastructure-grafana-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "true"
data:
  infrastructure-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "panels": [
        {
          "title": "Node Resources",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "CPU Usage",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
              "targets": [
                {
                  "expr": "sum by (instance) (rate(node_cpu_seconds_total{mode!=\"idle\"}[5m])) / count by (instance) (node_cpu_seconds_total{mode=\"idle\"}) * 100",
                  "legendFormat": "{{instance}}"
                }
              ],
              "yaxes": [
                {
                  "format": "percent",
                  "label": null,
                  "logBase": 1,
                  "max": "100",
                  "min": "0",
                  "show": true
                }
              ]
            },
            {
              "title": "Memory Usage",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
              "targets": [
                {
                  "expr": "100 - ((node_memory_MemAvailable_bytes * 100) / node_memory_MemTotal_bytes)",
                  "legendFormat": "{{instance}}"
                }
              ],
              "yaxes": [
                {
                  "format": "percent",
                  "label": null,
                  "logBase": 1,
                  "max": "100",
                  "min": "0",
                  "show": true
                }
              ]
            }
          ]
        },
        {
          "title": "Kubernetes Resources",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Pod Status",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16},
              "targets": [
                {
                  "expr": "sum by (phase) (kube_pod_status_phase)",
                  "legendFormat": "{{phase}}"
                }
              ]
            },
            {
              "title": "Container Restarts",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16},
              "targets": [
                {
                  "expr": "sum(kube_pod_container_status_restarts_total) by (container)",
                  "legendFormat": "{{container}}"
                }
              ]
            }
          ]
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": ["infrastructure", "monitoring"],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timezone": "browser",
      "title": "Infrastructure Dashboard",
      "uid": "infrastructure",
      "version": 1
    }
