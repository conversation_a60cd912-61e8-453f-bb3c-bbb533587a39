apiVersion: v1
kind: ConfigMap
metadata:
  name: log-rotation-config
  namespace: monitoring
data:
  logrotate.conf: |
    # Global settings
    compress
    copytruncate
    notifempty
    missingok
    rotate 7
    maxage 31
    dateext
    dateformat -%Y%m%d

    # Application logs
    /var/log/pods/*/*.log {
        rotate 7
        daily
        maxsize 100M
        compress
        delaycompress
        missingok
        notifempty
        create 0644 root root
        postrotate
            # Signal containers to reopen log files
            kill -USR1 $(pgrep -f "^/usr/bin/containerd")
        endscript
    }

    # System logs
    /var/log/kubernetes/*.log {
        rotate 7
        daily
        maxsize 50M
        compress
        delaycompress
        missingok
        notifempty
        create 0644 root root
    }

    # Audit logs
    /var/log/audit/*.log {
        rotate 14
        daily
        maxsize 200M
        compress
        delaycompress
        missingok
        notifempty
        create 0600 root root
    }
