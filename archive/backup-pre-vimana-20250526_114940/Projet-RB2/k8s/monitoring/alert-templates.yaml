apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-templates
  namespace: monitoring
data:
  default.tmpl: |
    {{ define "__alert_severity" }}{{ .CommonLabels.severity | title }}{{ end }}
    {{ define "__alert_summary" }}{{ .CommonAnnotations.summary }}{{ end }}

    {{ define "slack.default.title" }}
    [{{ .__alert_severity }}] {{ .__alert_summary }}
    {{ end }}

    {{ define "slack.default.text" }}
    {{ range .Alerts }}
    *Alert:* {{ .Annotations.summary }}
    *Description:* {{ .Annotations.description }}
    *Service:* {{ .Labels.app }}
    *Severity:* {{ .Labels.severity }}
    *Started:* {{ .StartsAt | since }}
    {{ if .Annotations.runbook }}*Runbook:* {{ .Annotations.runbook }}{{ end }}
    {{ end }}
    {{ end }}

    {{ define "email.default.subject" }}
    [{{ .__alert_severity }}] {{ .__alert_summary }}
    {{ end }}

    {{ define "email.default.html" }}
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Alert: {{ .__alert_summary }}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background: #f5f5f5;
        }
        .alert {
          background: white;
          border-radius: 5px;
          padding: 15px;
          margin-bottom: 10px;
          border-left: 5px solid;
        }
        .critical { border-left-color: #ff0000; }
        .warning { border-left-color: #ffa500; }
        .info { border-left-color: #0000ff; }
        .header { font-weight: bold; margin-bottom: 10px; }
        .detail { margin: 5px 0; }
      </style>
    </head>
    <body>
      {{ range .Alerts }}
      <div class="alert {{ .Labels.severity }}">
        <div class="header">{{ .Annotations.summary }}</div>
        <div class="detail"><strong>Description:</strong> {{ .Annotations.description }}</div>
        <div class="detail"><strong>Service:</strong> {{ .Labels.app }}</div>
        <div class="detail"><strong>Severity:</strong> {{ .Labels.severity }}</div>
        <div class="detail"><strong>Started:</strong> {{ .StartsAt | since }}</div>
        {{ if .Annotations.runbook }}
        <div class="detail"><strong>Runbook:</strong> <a href="{{ .Annotations.runbook }}">View Runbook</a></div>
        {{ end }}
      </div>
      {{ end }}
    </body>
    </html>
    {{ end }}
