apiVersion: v1
kind: ConfigMap
metadata:
  name: promtail-config
  namespace: monitoring
data:
  promtail.yaml: |
    server:
      http_listen_port: 9080
      grpc_listen_port: 0

    positions:
      filename: /run/promtail/positions.yaml

    clients:
      - url: http://loki:3100/loki/api/v1/push

    scrape_configs:
      - job_name: kubernetes-pods
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels:
              - __meta_kubernetes_pod_controller_name
            regex: ([0-9a-z-.]+?)(-[0-9a-f]{8,10})?
            target_label: __tmp_controller_name
          - source_labels:
              - __meta_kubernetes_pod_label_app
              - __tmp_controller_name
            regex: ^;(.*)$
            target_label: app
          - source_labels:
              - __meta_kubernetes_pod_label_app
              - __tmp_controller_name
            regex: ^(.+);.*$
            target_label: app
          - source_labels:
              - __meta_kubernetes_pod_node_name
            target_label: node_name
          - source_labels:
              - __meta_kubernetes_namespace
            target_label: namespace
          - source_labels:
              - __meta_kubernetes_pod_name
            target_label: pod
          - source_labels:
              - __meta_kubernetes_pod_container_name
            target_label: container
          - source_labels:
              - __meta_kubernetes_pod_label_app_kubernetes_io_name
            target_label: app_name
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
              - __meta_kubernetes_pod_uid
              - __meta_kubernetes_pod_container_name
            target_label: __path__
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: kubernetes_namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: kubernetes_pod_name
          - action: replace
            replacement: $1
            separator: /
            source_labels:
              - kubernetes_namespace
              - app
            target_label: job
