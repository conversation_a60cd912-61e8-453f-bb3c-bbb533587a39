apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  labels:
    app: frontend
spec:
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: ${DOCKER_REGISTRY}/${NAMESPACE}/frontend:latest
        livenessProbe:
          httpGet:
            path: /health
            port: 5175
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 5175
          initialDelaySeconds: 15
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  labels:
    app: backend
spec:
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: ${DOCKER_REGISTRY}/${NAMESPACE}/backend:latest
        livenessProbe:
          httpGet:
            path: /health
            port: 7000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 7000
          initialDelaySeconds: 15
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: partner-registration
  labels:
    app: partner-registration
spec:
  selector:
    matchLabels:
      app: partner-registration
  template:
    metadata:
      labels:
        app: partner-registration
    spec:
      containers:
      - name: partner-registration
        image: ${DOCKER_REGISTRY}/${NAMESPACE}/partner-registration:latest
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 5
