apiVersion: apps/v1
kind: Deployment
metadata:
  name: superagent
  labels:
    app: superagent
spec:
  replicas: 2
  selector:
    matchLabels:
      app: superagent
  template:
    metadata:
      labels:
        app: superagent
    spec:
      containers:
      - name: superagent
        image: superagent:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: AGENT_RB_SERVICE_URL
          value: "http://agent-rb-service:5000"
        - name: AGENT_IA_SERVICE_URL
          value: "http://agent-ia-service:5002"
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: superagent-service
spec:
  selector:
    app: superagent
  ports:
  - port: 5001
    targetPort: 8000
  type: ClusterIP
