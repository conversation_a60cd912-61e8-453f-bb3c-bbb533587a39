import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Montée progressive à 100 utilisateurs
    { duration: '5m', target: 100 }, // Maintien à 100 utilisateurs
    { duration: '2m', target: 200 }, // Montée à 200 utilisateurs
    { duration: '5m', target: 200 }, // Test de charge à 200 utilisateurs
    { duration: '2m', target: 300 }, // Test de stress à 300 utilisateurs
    { duration: '5m', target: 300 }, // Maintien du stress
    { duration: '2m', target: 0 },   // Retour au calme
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% des requêtes doivent être sous 500ms
    http_req_failed: ['rate<0.01'],   // Moins de 1% d'erreurs
  },
};

const BASE_URL = 'https://api.retreatandbe.com';

export default function () {
  // Test de l'API d'analyse
  let analyzeResponse = http.post(`${BASE_URL}/analyze`, {
    text: 'Sample text for analysis',
    language: 'en'
  });
  
  check(analyzeResponse, {
    'analyze status is 200': (r) => r.status === 200,
    'analyze response time OK': (r) => r.timings.duration < 500,
  });

  // Test de l'API de comparaison
  let compareResponse = http.post(`${BASE_URL}/compare`, {
    text1: 'First text for comparison',
    text2: 'Second text for comparison'
  });
  
  check(compareResponse, {
    'compare status is 200': (r) => r.status === 200,
    'compare response time OK': (r) => r.timings.duration < 500,
  });

  // Test de l'API de santé
  let healthResponse = http.get(`${BASE_URL}/health`);
  
  check(healthResponse, {
    'health check passed': (r) => r.status === 200,
    'health response time OK': (r) => r.timings.duration < 200,
  });

  sleep(1);
}
