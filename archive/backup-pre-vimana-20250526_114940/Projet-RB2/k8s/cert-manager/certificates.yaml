apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: example-com-cert
  namespace: istio-system
spec:
  secretName: example-com-cert
  duration: 2160h # 90 days
  renewBefore: 360h # 15 days
  commonName: app.example.com
  dnsNames:
  - app.example.com
  - api.example.com
  - partners.example.com
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
    group: cert-manager.io
  usages:
    - digital signature
    - key encipherment
    - server auth
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: monitoring-cert
  namespace: monitoring
spec:
  secretName: monitoring-cert
  duration: 2160h # 90 days
  renewBefore: 360h # 15 days
  commonName: grafana.example.com
  dnsNames:
  - grafana.example.com
  - prometheus.example.com
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
    group: cert-manager.io
  usages:
    - digital signature
    - key encipherment
    - server auth
