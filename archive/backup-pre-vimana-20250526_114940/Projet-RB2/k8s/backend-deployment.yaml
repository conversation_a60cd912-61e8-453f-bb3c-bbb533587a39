apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
  labels:
    app: retreat-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: retreat-backend
  template:
    metadata:
      labels:
        app: retreat-backend
    spec:
      containers:
      - name: backend
        image: retreat-backend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 7000
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: "retreatdb"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PORT
          value: "6379"
        - name: NODE_ENV
          value: "production"
        - name: SESSION_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secret
              key: session-secret
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 7000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 7000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
spec:
  type: ClusterIP
  ports:
  - port: 7000
    targetPort: 7000
  selector:
    app: retreat-backend
