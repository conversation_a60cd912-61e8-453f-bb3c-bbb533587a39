apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: education-vs
  namespace: default
spec:
  hosts:
  - education-service
  http:
  - route:
    - destination:
        host: education-service
        subset: v1
      weight: 90
    - destination:
        host: education-service
        subset: v2
      weight: 10
    mirror:
      host: education-service-shadow
    timeout: 5s
    retries:
      attempts: 3
      perTryTimeout: 2s
      retryOn: connect-failure,refused-stream,unavailable,cancelled,retriable-status-codes
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: education-dr
  namespace: default
spec:
  host: education-service
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 10
    outlierDetection:
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
  subsets:
  - name: v1
    labels:
      version: v1
  - name: v2
    labels:
      version: v2