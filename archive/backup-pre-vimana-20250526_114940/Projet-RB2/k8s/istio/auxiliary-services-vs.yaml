apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: insurance-service-vs
spec:
  hosts:
  - insurance-service
  http:
  - route:
    - destination:
        host: insurance-service
        port:
          number: 8080
    retries:
      attempts: 3
      perTryTimeout: 2s
    timeout: 5s
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: website-creator-vs
spec:
  hosts:
  - website-creator-service
  http:
  - route:
    - destination:
        host: website-creator-service
        port:
          number: 8080
    retries:
      attempts: 3
      perTryTimeout: 2s
    timeout: 10s
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: gps-service-vs
spec:
  hosts:
  - gps-service
  http:
  - route:
    - destination:
        host: gps-service
        port:
          number: 8080
    retries:
      attempts: 3
      perTryTimeout: 1s
    timeout: 3s
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: marketplace-service-vs
spec:
  hosts:
  - marketplace-service
  http:
  - route:
    - destination:
        host: marketplace-service
        port:
          number: 8080
    retries:
      attempts: 3
      perTryTimeout: 2s
    timeout: 5s
