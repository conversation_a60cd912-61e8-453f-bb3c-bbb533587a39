apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: frontend-vs
spec:
  hosts:
  - "app.example.com"
  gateways:
  - main-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: frontend
        port:
          number: 5175
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: backend-vs
spec:
  hosts:
  - "api.example.com"
  gateways:
  - main-gateway
  http:
  - match:
    - uri:
        prefix: /api/v1
    route:
    - destination:
        host: backend
        port:
          number: 7000
    corsPolicy:
      allowOrigins:
      - exact: "https://app.example.com"
      allowMethods:
      - GET
      - POST
      - PUT
      - DELETE
      allowHeaders:
      - Authorization
      - Content-Type
      maxAge: "24h"
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: partner-registration-vs
spec:
  hosts:
  - "partners.example.com"
  gateways:
  - main-gateway
  http:
  - match:
    - uri:
        prefix: /register
    route:
    - destination:
        host: partner-registration
        port:
          number: 8080
    corsPolicy:
      allowOrigins:
      - exact: "https://app.example.com"
      allowMethods:
      - POST
      allowHeaders:
      - Authorization
      - Content-Type
      maxAge: "24h"
