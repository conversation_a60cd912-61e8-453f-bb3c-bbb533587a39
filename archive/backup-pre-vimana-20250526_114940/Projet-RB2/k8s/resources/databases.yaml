apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb
spec:
  template:
    spec:
      containers:
      - name: mongodb
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "1000m"
            memory: "2Gi"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  template:
    spec:
      containers:
      - name: redis
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "400m"
            memory: "1Gi"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
spec:
  template:
    spec:
      containers:
      - name: postgres
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "1000m"
            memory: "2Gi"
