apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-deployment
  labels:
    app: retreat-frontend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: retreat-frontend
  template:
    metadata:
      labels:
        app: retreat-frontend
    spec:
      containers:
      - name: frontend
        image: retreat-frontend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 3000
        env:
        - name: REACT_APP_API_URL
          value: "http://backend-service:7000"
        - name: NODE_ENV
          value: "production"
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 3000
  selector:
    app: retreat-frontend
