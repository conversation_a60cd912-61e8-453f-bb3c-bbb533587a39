apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: microservices
  namespace: argocd
spec:
  project: default
  source:
    repoURL: 'https://github.com/your-org/Project-Final.git'
    targetRevision: HEAD
    path: charts
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: default
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: microservices-set
  namespace: argocd
spec:
  generators:
  - list:
      elements:
      - name: hotel-booking
        path: charts/Hotel-Booking
      - name: transport-booking
        path: charts/Transport-Booking
      - name: flight-finder
        path: charts/Flight-Finder
      - name: marketplace
        path: charts/Marketplace
  template:
    metadata:
      name: '{{name}}'
      namespace: argocd
    spec:
      project: default
      source:
        repoURL: 'https://github.com/your-org/Project-Final.git'
        targetRevision: HEAD
        path: '{{path}}'
      destination:
        server: https://kubernetes.default.svc
        namespace: '{{name}}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
        - CreateNamespace=true