apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: retreatandbe
  namespace: argocd
spec:
  project: default
  source:
    repoURL: 'https://github.com/your-org/retreatandbe.git'
    targetRevision: HEAD
    path: k8s/overlays/production
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: retreatandbe
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m