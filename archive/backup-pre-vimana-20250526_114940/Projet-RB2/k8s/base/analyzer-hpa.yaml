apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: analyzer-hpa
  namespace: retreatandbe
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analyzer
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Pods
        value: 1
        periodSeconds: 60
