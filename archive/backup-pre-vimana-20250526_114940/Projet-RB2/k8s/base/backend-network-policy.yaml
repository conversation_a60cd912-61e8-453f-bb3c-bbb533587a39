apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: backend-network-policy
  namespace: retreatandbe
spec:
  podSelector:
    matchLabels:
      app: retreat-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    - podSelector:
        matchLabels:
          app: retreat-frontend
    ports:
    - protocol: TCP
      port: 7000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres-service
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis-service
    ports:
    - protocol: TCP
      port: 6379