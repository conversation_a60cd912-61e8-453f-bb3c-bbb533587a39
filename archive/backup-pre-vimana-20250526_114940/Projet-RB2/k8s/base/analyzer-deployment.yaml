apiVersion: apps/v1
kind: Deployment
metadata:
  name: analyzer
  namespace: retreatandbe
spec:
  replicas: 3
  selector:
    matchLabels:
      app: analyzer
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: analyzer
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
    spec:
      containers:
      - name: analyzer
        image: analyzer:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 20
        env:
        - name: NODE_ENV
          value: "production"
        - name: VAULT_ADDR
          value: "http://vault:8200"
        - name: VAULT_TOKEN
          valueFrom:
            secretKeyRef:
              name: vault-token
              key: token
        - name: CONSUL_HOST
          value: "consul"
        - name: CONSUL_PORT
          value: "8500"
        - name: JAEGER_ENDPOINT
          value: "http://jaeger:14268/api/traces"
        - name: OTEL_EXPORTER_JAEGER_ENDPOINT
          value: "http://jaeger:14268/api/traces"
        - name: OTEL_SERVICE_NAME
          value: "analyzer-service"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - analyzer
              topologyKey: kubernetes.io/hostname
