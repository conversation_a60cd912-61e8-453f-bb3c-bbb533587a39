apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: analyzer
  namespace: retreatandbe
spec:
  hosts:
  - "api.retreatandbe.com"
  gateways:
  - retreatandbe-gateway
  http:
  - match:
    - uri:
        prefix: "/api/analyze"
    route:
    - destination:
        host: analyzer
        port:
          number: 3000
    retries:
      attempts: 3
      perTryTimeout: "2s"
    timeout: "10s"
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: "5s"
    corsPolicy:
      allowOrigins:
      - exact: "https://www.retreatandbe.com"
      allowMethods:
      - POST
      - GET
      - OPTIONS
      allowHeaders:
      - authorization
      - content-type
      maxAge: "24h"
