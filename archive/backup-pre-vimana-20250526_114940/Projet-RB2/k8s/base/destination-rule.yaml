apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: analyzer
  namespace: retreatandbe
spec:
  host: analyzer
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: "30ms"
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 10
    outlierDetection:
      consecutive5xxErrors: 5
      interval: "30s"
      baseEjectionTime: "30s"
      maxEjectionPercent: 10
  subsets:
  - name: v1
    labels:
      version: v1
    trafficPolicy:
      loadBalancer:
        simple: LEAST_CONN
