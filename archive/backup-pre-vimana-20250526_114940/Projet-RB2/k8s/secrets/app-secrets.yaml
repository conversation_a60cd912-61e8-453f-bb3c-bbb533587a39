apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: default
type: Opaque
stringData:
  MONGODB_URI: "mongodb://mongodb-service:27017/mydatabase"
  REDIS_URL: "redis://redis-service:6379"
  JWT_SECRET: "your-jwt-secret-key"
  POSTGRES_PASSWORD: "your-postgres-password"
  POSTGRES_USER: "postgres"
  POSTGRES_DB: "mydatabase"
---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: monitoring
type: Opaque
stringData:
  admin-password: "your-secure-password"
---
apiVersion: v1
kind: Secret
metadata:
  name: prometheus-secrets
  namespace: monitoring
type: Opaque
stringData:
  auth: "your-prometheus-password"
