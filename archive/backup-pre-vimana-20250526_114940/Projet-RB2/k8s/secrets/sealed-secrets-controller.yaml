apiVersion: apps/v1
kind: Deployment
metadata:
  name: sealed-secrets-controller
  namespace: sealed-secrets
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sealed-secrets-controller
  template:
    metadata:
      labels:
        app: sealed-secrets-controller
    spec:
      containers:
      - name: sealed-secrets-controller
        image: bitnami/sealed-secrets-controller:v0.19.0
        ports:
        - containerPort: 8080
        args:
        - "--update-status"
        - "--key-prefix=sealed-secrets-key"
        securityContext:
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: sealed-secrets-controller
  namespace: sealed-secrets
spec:
  ports:
  - port: 8080
    targetPort: 8080
  selector:
    app: sealed-secrets-controller
