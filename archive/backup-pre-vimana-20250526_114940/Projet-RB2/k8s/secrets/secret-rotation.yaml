apiVersion: batch/v1
kind: CronJob
metadata:
  name: secret-rotation
  namespace: default
spec:
  schedule: "0 0 1 * *"  # Exécution le premier jour de chaque mois
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: secret-rotation
            image: bitnami/kubectl:latest
            command:
            - /bin/sh
            - -c
            - |
              # Script de rotation des secrets
              # Générer de nouveaux secrets
              NEW_JWT_SECRET=$(openssl rand -base64 32)
              
              # Mettre à jour les secrets
              kubectl create secret generic app-secrets \
                --from-literal=JWT_SECRET=$NEW_JWT_SECRET \
                --from-literal=MONGODB_URI=$(kubectl get secret app-secrets -o jsonpath='{.data.MONGODB_URI}' | base64 -d) \
                --from-literal=REDIS_URL=$(kubectl get secret app-secrets -o jsonpath='{.data.REDIS_URL}' | base64 -d) \
                --from-literal=POSTGRES_PASSWORD=$(kubectl get secret app-secrets -o jsonpath='{.data.POSTGRES_PASSWORD}' | base64 -d) \
                --from-literal=POSTGRES_USER=$(kubectl get secret app-secrets -o jsonpath='{.data.POSTGRES_USER}' | base64 -d) \
                --from-literal=POSTGRES_DB=$(kubectl get secret app-secrets -o jsonpath='{.data.POSTGRES_DB}' | base64 -d) \
                -o yaml --dry-run=client | kubectl apply -f -
              
              # Redémarrer les pods qui utilisent ces secrets
              kubectl rollout restart deployment/backend
          serviceAccountName: secret-rotation-sa
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: secret-rotation-sa
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: secret-rotation-role
  namespace: default
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "create", "update", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: secret-rotation-rolebinding
  namespace: default
subjects:
- kind: ServiceAccount
  name: secret-rotation-sa
  namespace: default
roleRef:
  kind: Role
  name: secret-rotation-role
  apiGroup: rbac.authorization.k8s.io
