apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: monitoring-alerts
  namespace: logging
  labels:
    prometheus: k8s
    role: alert-rules
spec:
  groups:
  - name: monitoring.rules
    rules:
    # Règles pour le monitoring-proxy
    - alert: MonitoringProxyHighMemory
      expr: sum(container_memory_working_set_bytes{pod=~"monitoring-proxy.*"}) > 800 * 1024 * 1024
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High memory usage in monitoring-proxy
        description: Monitoring proxy is using more than 800MB of memory for 5 minutes.
        runbook_url: https://runbooks.example.com/monitoring/high-memory

    - alert: MonitoringProxyCPUThrottling
      expr: sum(rate(container_cpu_cfs_throttled_seconds_total{pod=~"monitoring-proxy.*"}[5m])) > 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: CPU throttling detected in monitoring-proxy
        description: Monitoring proxy is experiencing CPU throttling.
        runbook_url: https://runbooks.example.com/monitoring/cpu-throttling

    - alert: MonitoringProxyHighRetryRate
      expr: sum(rate(fluentd_output_status_retry_count{job="monitoring-proxy"}[5m])) > 10
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High retry rate in monitoring-proxy
        description: Monitoring proxy has a high rate of retries (>10 per 5m).
        runbook_url: https://runbooks.example.com/monitoring/high-retry-rate

    # Règles pour la surveillance des logs
    - alert: HighLogVolume
      expr: sum(rate(fluentd_output_status_emit_count{job="monitoring-proxy"}[5m])) > 1000
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High log volume detected
        description: Log emission rate is higher than 1000 events per 5 minutes.
        runbook_url: https://runbooks.example.com/monitoring/high-log-volume

    - alert: LogProcessingDelay
      expr: rate(fluentd_output_status_buffer_queue_length{job="monitoring-proxy"}[5m]) > 100
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Log processing delay detected
        description: Buffer queue length is growing, indicating potential processing delays.
        runbook_url: https://runbooks.example.com/monitoring/processing-delay

    # Règles pour la santé du système
    - alert: MonitoringProxyDown
      expr: up{job="monitoring-proxy"} == 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: Monitoring proxy is down
        description: Monitoring proxy has been down for more than 5 minutes.
        runbook_url: https://runbooks.example.com/monitoring/proxy-down

    - alert: PersistentQueueGrowing
      expr: sum(fluentd_output_status_buffer_total_bytes{job="monitoring-proxy"}) > 100 * 1024 * 1024
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: Persistent queue is growing
        description: The buffer queue is larger than 100MB for 15 minutes.
        runbook_url: https://runbooks.example.com/monitoring/queue-growing

    # Règles pour les erreurs
    - alert: HighErrorRate
      expr: sum(rate(fluentd_output_status_num_errors{job="monitoring-proxy"}[5m])) > 10
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: High error rate detected
        description: Error rate is higher than 10 errors per 5 minutes.
        runbook_url: https://runbooks.example.com/monitoring/high-error-rate

    - alert: OutputPluginErrors
      expr: sum(fluentd_output_status_num_errors{job="monitoring-proxy"}) by (type) > 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Output plugin errors detected
        description: Errors detected in output plugin {{ $labels.type }}.
        runbook_url: https://runbooks.example.com/monitoring/plugin-errors
