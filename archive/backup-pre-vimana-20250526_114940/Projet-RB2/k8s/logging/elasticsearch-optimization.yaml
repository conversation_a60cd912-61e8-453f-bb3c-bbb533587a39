apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-optimization
  namespace: logging
data:
  elasticsearch.yml: |
    # Configuration optimisée pour Elasticsearch
    cluster:
      name: logging-cluster
      routing.allocation.disk.threshold_enabled: true
      routing.allocation.disk.watermark.low: "85%"
      routing.allocation.disk.watermark.high: "90%"
      routing.allocation.disk.watermark.flood_stage: "95%"
      
    node:
      master: ${NODE_MASTER:true}
      data: ${NODE_DATA:true}
      ingest: ${NODE_INGEST:true}
      
    # Optimisation mémoire et performances
    bootstrap.memory_lock: true
    indices.memory.index_buffer_size: "30%"
    indices.queries.cache.size: "20%"
    indices.fielddata.cache.size: "20%"
    indices.breaker.total.use_real_memory: true
    indices.breaker.fielddata.limit: "60%"
    indices.breaker.request.limit: "40%"
    indices.recovery.max_bytes_per_sec: "50mb"
    
    # Optimisation du clustering
    discovery.zen.minimum_master_nodes: ${MINIMUM_MASTER_NODES:2}
    discovery.zen.ping.unicast.hosts: ${DISCOVERY_SERVICE:elasticsearch-discovery}
    discovery.zen.fd.ping_timeout: 30s
    discovery.zen.fd.ping_retries: 3
    
    # Optimisation des index
    index:
      number_of_shards: ${NUMBER_OF_SHARDS:5}
      number_of_replicas: ${NUMBER_OF_REPLICAS:1}
      refresh_interval: "30s"
      translog:
        durability: async
        sync_interval: "5s"
        flush_threshold_size: "256mb"
      merge:
        scheduler:
          max_thread_count: 1
          type: concurrent
      
    # Optimisation des requêtes
    search:
      max_buckets: 100000
      default_search_timeout: "30s"
      max_concurrent_shard_requests: 5
      
    # Sécurité
    xpack.security.enabled: true
    xpack.security.transport.ssl.enabled: true
    xpack.security.transport.ssl.verification_mode: certificate
    xpack.security.transport.ssl.keystore.path: /usr/share/elasticsearch/config/certs/elastic-certificates.p12
    xpack.security.transport.ssl.truststore.path: /usr/share/elasticsearch/config/certs/elastic-certificates.p12
    
    # Monitoring
    xpack.monitoring.enabled: true
    xpack.monitoring.collection.enabled: true

  curator-actions.yml: |
    actions:
      1:
        action: delete_indices
        description: "Nettoyage des vieux indices"
        options:
          ignore_empty_list: True
          timeout_override: 300
        filters:
          - filtertype: pattern
            kind: prefix
            value: logstash-
          - filtertype: age
            source: creation_date
            direction: older
            unit: days
            unit_count: 30
            
      2:
        action: forcemerge
        description: "Optimisation des indices"
        options:
          max_num_segments: 1
          timeout_override: 21600
        filters:
          - filtertype: pattern
            kind: prefix
            value: logstash-
          - filtertype: age
            source: creation_date
            direction: older
            unit: days
            unit_count: 2
            
      3:
        action: snapshot
        description: "Création de snapshots"
        options:
          repository: s3_backup
          name: "snapshot-%Y%m%d"
          ignore_unavailable: True
          include_global_state: True
          partial: False
          wait_for_completion: True
          skip_repo_fs_check: False
        filters:
          - filtertype: pattern
            kind: prefix
            value: logstash-

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: elasticsearch-maintenance
  namespace: logging
spec:
  schedule: "0 1 * * *"  # Tous les jours à 1h du matin
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: curator
            image: elasticsearch-curator:5.8.3
            command:
            - /bin/bash
            - -c
            - |
              curator --config /etc/curator/config/curator.yml /etc/curator/config/curator-actions.yml
            volumeMounts:
            - name: curator-config
              mountPath: /etc/curator/config
          restartPolicy: OnFailure
          volumes:
          - name: curator-config
            configMap:
              name: elasticsearch-optimization
