apiVersion: v1
kind: ConfigMap
metadata:
  name: logging-security-config
  namespace: logging
data:
  security.conf: |
    # Configuration TLS
    <transport tls>
      version TLS1_2
      ciphers HIGH:!aNULL:!MD5
      verify_mode peer
      ca_path /etc/ssl/certs
      cert_path /etc/fluentd/ssl/fluentd.crt
      private_key_path /etc/fluentd/ssl/fluentd.key
      private_key_passphrase "#{ENV['TLS_PASSPHRASE']}"
    </transport>

    # Filtrage des données sensibles
    <filter **>
      @type mask_sensitive
      <mask>
        keys ["password", "token", "api_key", "secret", "credit_card", "ssn"]
        mask_with "[REDACTED]"
      </mask>
      <mask>
        keys ["email"]
        mask_with "****@${domain}"
        use_regex true
        regex_pattern /^[^@]+@(.+)$/
      </mask>
    </filter>

    # Audit logging
    <match security.**>
      @type copy
      <store>
        @type elasticsearch
        host elasticsearch
        port 9200
        logstash_format true
        logstash_prefix security_audit
        include_tag_key true
        tag_key @event_type
        flush_interval 1s
      </store>
    </match>

    # Rate limiting
    <filter **>
      @type throttle
      group_key ${tag}
      group_bucket_period_s 60
      group_bucket_limit 1000
    </filter>

    # IP-based filtering
    <filter **>
      @type grep
      <exclude>
        key client_ip
        pattern /^(10\.0\.0\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|192\.168\.)/
      </exclude>
    </filter>
---
apiVersion: v1
kind: Secret
metadata:
  name: logging-security-credentials
  namespace: logging
type: Opaque
data:
  tls-passphrase: ${TLS_PASSPHRASE_BASE64}
  kibana-admin-password: ${KIBANA_ADMIN_PASSWORD_BASE64}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: logging-network-policy
  namespace: logging
spec:
  podSelector:
    matchLabels:
      app: fluentd
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: kibana
    ports:
    - protocol: TCP
      port: 24224
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: elasticsearch
    ports:
    - protocol: TCP
      port: 9200
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: logging-security-role
  namespace: logging
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get"]
  resourceNames: ["logging-security-config"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: logging-security-binding
  namespace: logging
subjects:
- kind: ServiceAccount
  name: fluentd
  namespace: logging
roleRef:
  kind: Role
  name: logging-security-role
  apiGroup: rbac.authorization.k8s.io
