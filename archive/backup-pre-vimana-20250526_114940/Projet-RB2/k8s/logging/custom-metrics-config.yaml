apiVersion: v1
kind: ConfigMap
metadata:
  name: custom-metrics-config
  namespace: logging
data:
  custom-metrics.conf: |
    # Métriques de performance applicative
    <source>
      @type prometheus
      bind 0.0.0.0
      port 24231
      metrics_path /metrics
    </source>

    <filter kubernetes.**>
      @type prometheus
      <metric>
        name kube_pod_container_memory_usage
        type gauge
        desc Container Memory Usage
        key memory_usage
        <labels>
          container $.kubernetes.container_name
          pod $.kubernetes.pod_name
          namespace $.kubernetes.namespace_name
        </labels>
      </metric>

      <metric>
        name kube_pod_container_cpu_usage
        type gauge
        desc Container CPU Usage
        key cpu_usage
        <labels>
          container $.kubernetes.container_name
          pod $.kubernetes.pod_name
          namespace $.kubernetes.namespace_name
        </labels>
      </metric>

      <metric>
        name http_request_duration_seconds
        type histogram
        desc HTTP Request Duration
        key response_time
        buckets 0.1,0.3,0.5,1.0,2.0,5.0
        <labels>
          method $.http.method
          path $.http.path
          status $.http.status_code
        </labels>
      </metric>

      <metric>
        name http_request_total
        type counter
        desc Total HTTP Requests
        <labels>
          method $.http.method
          path $.http.path
          status $.http.status_code
        </labels>
      </metric>

      <metric>
        name user_session_duration_seconds
        type histogram
        desc User Session Duration
        key session_duration
        buckets 60,300,900,1800,3600
        <labels>
          user_id $.user.id
          user_type $.user.type
        </labels>
      </metric>
    </filter>

    # Agrégation des logs d'erreur par type
    <filter kubernetes.**>
      @type prometheus
      <metric>
        name error_count_total
        type counter
        desc Total Error Count
        <labels>
          error_type $.error.type
          service $.kubernetes.labels.app
          severity $.error.severity
        </labels>
      </metric>
    </filter>

    # Métriques de performance base de données
    <filter kubernetes.**>
      @type prometheus
      <metric>
        name db_query_duration_seconds
        type histogram
        desc Database Query Duration
        key query_time
        buckets 0.01,0.05,0.1,0.5,1.0,2.0
        <labels>
          query_type $.db.query_type
          table $.db.table
          operation $.db.operation
        </labels>
      </metric>

      <metric>
        name db_connection_pool_size
        type gauge
        desc Database Connection Pool Size
        key pool_size
        <labels>
          database $.db.name
          host $.db.host
        </labels>
      </metric>
    </filter>

    # Métriques de cache
    <filter kubernetes.**>
      @type prometheus
      <metric>
        name cache_hit_total
        type counter
        desc Cache Hit Count
        <labels>
          cache_type $.cache.type
          operation $.cache.operation
        </labels>
      </metric>

      <metric>
        name cache_miss_total
        type counter
        desc Cache Miss Count
        <labels>
          cache_type $.cache.type
          operation $.cache.operation
        </labels>
      </metric>
    </filter>
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: custom-metrics-collector
  namespace: logging
spec:
  selector:
    matchLabels:
      app: custom-metrics-collector
  template:
    metadata:
      labels:
        app: custom-metrics-collector
    spec:
      containers:
      - name: metrics-collector
        image: fluent/fluentd-kubernetes-daemonset:v1.14.6-debian-elasticsearch7-1.1
        ports:
        - containerPort: 24231
          name: prometheus
        env:
        - name: FLUENT_ELASTICSEARCH_HOST
          value: "elasticsearch"
        - name: FLUENT_ELASTICSEARCH_PORT
          value: "9200"
        volumeMounts:
        - name: config
          mountPath: /fluentd/etc/custom-metrics.conf
          subPath: custom-metrics.conf
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        resources:
          limits:
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 200Mi
      volumes:
      - name: config
        configMap:
          name: custom-metrics-config
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
---
apiVersion: v1
kind: Service
metadata:
  name: custom-metrics-service
  namespace: logging
  labels:
    app: custom-metrics-collector
spec:
  ports:
  - port: 24231
    name: prometheus
  selector:
    app: custom-metrics-collector
