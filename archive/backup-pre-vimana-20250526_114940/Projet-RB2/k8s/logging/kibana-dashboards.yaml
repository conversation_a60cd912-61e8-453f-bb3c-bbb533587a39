apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-dashboards
  namespace: logging
data:
  dashboards.ndjson: |
    {
      "attributes": {
        "title": "Application Performance Overview",
        "hits": 0,
        "description": "Vue d'ensemble des performances de l'application",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"error-rate\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"Taux d'erreurs\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"response-time\",
            \"panelIndex\":\"2\",
            \"embeddableConfig\":{
              \"title\":\"Temps de réponse\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[]}"
        }
      },
      "type": "dashboard",
      "id": "application-overview"
    }
    {
      "attributes": {
        "title": "System Health Dashboard",
        "hits": 0,
        "description": "Surveillance de la santé du système",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":48,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"pod-restarts\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"Redémarrages de pods\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[]}"
        }
      },
      "type": "dashboard",
      "id": "system-health"
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-dashboards-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: curl
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 45
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/dashboards.ndjson
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: kibana-dashboards
      restartPolicy: OnFailure
