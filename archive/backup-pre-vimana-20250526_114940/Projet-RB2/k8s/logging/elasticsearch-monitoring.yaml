apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-monitoring
  namespace: logging
data:
  elasticsearch.yml: |
    xpack.monitoring.enabled: true
    xpack.monitoring.collection.enabled: true
    xpack.monitoring.elasticsearch.collection.enabled: true
    
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: elasticsearch-monitoring
  namespace: logging
  labels:
    release: prometheus
spec:
  selector:
    matchLabels:
      app: elasticsearch
  endpoints:
  - port: http
    interval: 30s
    path: /_prometheus/metrics
