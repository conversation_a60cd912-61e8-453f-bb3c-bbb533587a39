apiVersion: v1
kind: ConfigMap
metadata:
  name: log-aggregation-rules
  namespace: logging
data:
  aggregation-rules.conf: |
    # Règles d'agrégation temporelle
    <match kubernetes.**>
      @type aggregate
      <rule>
        key time
        pattern ^(?<hour>\d{2}):(?<minute>\d{2}):(?<second>\d{2})
        group_by hour,minute
        replace ${hour}:${minute}
      </rule>
    </match>

    # Agrégation des erreurs similaires
    <filter kubernetes.**>
      @type dedot
      de_dot_nested true
      de_dot_separator _
    </filter>

    <match kubernetes.**>
      @type rewrite_tag_filter
      <rule>
        key log
        pattern /error|exception|fail/i
        tag error.${tag}
      </rule>
      <rule>
        key log
        pattern /warn|warning/i
        tag warning.${tag}
      </rule>
    </match>

    # Enrichissement des logs
    <filter **>
      @type record_transformer
      enable_ruby true
      <record>
        environment ${record["kubernetes"]["namespace_name"] || "default"}
        app_name ${record["kubernetes"]["container_name"] || "unknown"}
        pod_name ${record["kubernetes"]["pod_name"] || "unknown"}
        severity ${if record["log"] =~ /error|exception|fail/i then "error"
                 elsif record["log"] =~ /warn|warning/i then "warning"
                 elsif record["log"] =~ /info/i then "info"
                 else "debug" end}
        timestamp ${Time.now.strftime('%Y-%m-%d %H:%M:%S.%L')}
      </record>
    </filter>

    # Agrégation par service
    <match **>
      @type copy
      <store>
        @type elasticsearch
        host elasticsearch
        port 9200
        logstash_format true
        logstash_prefix service.${record["app_name"]}
        include_tag_key true
        tag_key @log_name
        flush_interval 5s
      </store>
    </match>

    # Agrégation des métriques de performance
    <filter kubernetes.**>
      @type grep
      <regexp>
        key log
        pattern /duration|latency|time|performance/i
      </regexp>
    </filter>

    <match kubernetes.**>
      @type elasticsearch
      host elasticsearch
      port 9200
      logstash_format true
      logstash_prefix performance_metrics
      include_tag_key true
      tag_key @metric_name
      flush_interval 5s
    </match>

    # Configuration des buffers
    <system>
      workers 4
    </system>

    <buffer>
      @type file
      path /var/log/fluentd/buffers/kubernetes
      flush_mode interval
      retry_type exponential_backoff
      flush_interval 5s
      retry_forever false
      retry_max_interval 30
      chunk_limit_size 2M
      queue_limit_length 8
      overflow_action block
    </buffer>
