apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-advanced-metrics
  namespace: logging
data:
  advanced-metrics.ndjson: |
    {
      "attributes": {
        "title": "Security Dashboard",
        "hits": 0,
        "description": "Security monitoring and threat detection",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"auth-attempts\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"Authentication Attempts\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"failed-logins\",
            \"panelIndex\":\"2\",
            \"embeddableConfig\":{
              \"title\":\"Failed Login Distribution\"
            }
          },
          {
            \"gridData\": {\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"3\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"api-auth-errors\",
            \"panelIndex\":\"3\",
            \"embeddableConfig\":{
              \"title\":\"API Authentication Errors\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":15,\"w\":24,\"h\":15,\"i\":\"4\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"suspicious-activities\",
            \"panelIndex\":\"4\",
            \"embeddableConfig\":{
              \"title\":\"Suspicious Activities\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"language\":\"kuery\",\"query\":\"\"},\"filter\":[]}"
        }
      },
      "id": "security-dashboard",
      "type": "dashboard"
    }
    {
      "attributes": {
        "title": "Performance Metrics Dashboard",
        "hits": 0,
        "description": "Detailed performance metrics and resource usage",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"cpu-usage\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"CPU Usage by Service\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"memory-trends\",
            \"panelIndex\":\"2\",
            \"embeddableConfig\":{
              \"title\":\"Memory Usage Trends\"
            }
          },
          {
            \"gridData\": {\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"3\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"disk-usage\",
            \"panelIndex\":\"3\",
            \"embeddableConfig\":{
              \"title\":\"Disk Usage by Pod\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":15,\"w\":24,\"h\":15,\"i\":\"4\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"network-io\",
            \"panelIndex\":\"4\",
            \"embeddableConfig\":{
              \"title\":\"Network I/O\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"language\":\"kuery\",\"query\":\"\"},\"filter\":[]}"
        }
      },
      "id": "performance-metrics",
      "type": "dashboard"
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-advanced-metrics-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: curl
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 120
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/advanced-metrics.ndjson
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: kibana-advanced-metrics
      restartPolicy: OnFailure
