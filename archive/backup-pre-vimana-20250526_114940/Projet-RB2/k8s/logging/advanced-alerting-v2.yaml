apiVersion: v1
kind: ConfigMap
metadata:
  name: advanced-alerting-config
  namespace: logging
data:
  alert-rules.yaml: |
    groups:
      - name: infrastructure_alerts
        rules:
          # Alertes de ressources système
          - alert: HighCPUUsage
            expr: avg(rate(node_cpu_seconds_total{mode="idle"}[5m])) by (instance) < 0.2
            for: 5m
            labels:
              severity: warning
              category: infrastructure
            annotations:
              summary: "High CPU usage on {{ $labels.instance }}"
              description: "CPU usage is above 80% for 5 minutes"
              runbook_url: "https://wiki.example.com/runbooks/high-cpu"
              dashboard_url: "https://grafana.example.com/d/node-exporter/{{ $labels.instance }}"

          - alert: MemoryExhaustion
            expr: node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100 < 10
            for: 5m
            labels:
              severity: critical
              category: infrastructure
            annotations:
              summary: "Low memory on {{ $labels.instance }}"
              description: "Less than 10% memory available"

          # Alertes de performance applicative
          - alert: HighLatency
            expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
            for: 5m
            labels:
              severity: warning
              category: performance
            annotations:
              summary: "High latency detected"
              description: "95th percentile latency is above 2 seconds"

          - alert: HighErrorRate
            expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) > 0.05
            for: 5m
            labels:
              severity: critical
              category: performance
            annotations:
              summary: "High error rate detected"
              description: "Error rate is above 5%"

      - name: security_alerts
        rules:
          # Alertes de sécurité
          - alert: UnauthorizedAccessAttempts
            expr: rate(unauthorized_access_total[5m]) > 10
            for: 5m
            labels:
              severity: critical
              category: security
            annotations:
              summary: "Multiple unauthorized access attempts"
              description: "More than 10 unauthorized access attempts in 5 minutes"

          - alert: SuspiciousActivity
            expr: rate(security_events_total{type="suspicious"}[5m]) > 5
            for: 5m
            labels:
              severity: warning
              category: security
            annotations:
              summary: "Suspicious activity detected"
              description: "Multiple suspicious events detected in the system"

      - name: business_alerts
        rules:
          # Alertes métier
          - alert: HighTransactionFailureRate
            expr: sum(rate(transaction_failures_total[5m])) / sum(rate(transactions_total[5m])) > 0.01
            for: 5m
            labels:
              severity: critical
              category: business
            annotations:
              summary: "High transaction failure rate"
              description: "Transaction failure rate is above 1%"

          - alert: LowUserEngagement
            expr: rate(user_engagement_total[1h]) < 100
            for: 15m
            labels:
              severity: warning
              category: business
            annotations:
              summary: "Low user engagement"
              description: "User engagement is below expected threshold"

  incident-management.yaml: |
    # Configuration de la gestion des incidents
    incident_management:
      severity_levels:
        critical:
          description: "Impact critique sur le service"
          response_time: "5m"
          escalation_time: "15m"
          notification_channels:
            - pagerduty
            - slack-urgent
            - phone
          
        high:
          description: "Impact significatif sur le service"
          response_time: "15m"
          escalation_time: "30m"
          notification_channels:
            - slack-urgent
            - email
          
        medium:
          description: "Impact modéré sur le service"
          response_time: "30m"
          escalation_time: "2h"
          notification_channels:
            - slack
            - email
          
        low:
          description: "Impact mineur sur le service"
          response_time: "2h"
          escalation_time: "4h"
          notification_channels:
            - email

      escalation_policies:
        default:
          levels:
            - team: "platform-ops"
              wait: "15m"
            - team: "senior-ops"
              wait: "15m"
            - team: "management"
              wait: "30m"
        
        security:
          levels:
            - team: "security-ops"
              wait: "5m"
            - team: "security-lead"
              wait: "10m"
            - team: "ciso"
              wait: "15m"

      notification_channels:
        slack:
          type: "slack"
          webhook_url: "${SLACK_WEBHOOK_URL}"
          default_channel: "#monitoring"
          urgent_channel: "#incidents"
          
        email:
          type: "smtp"
          server: "smtp.gmail.com"
          port: 587
          username: "${SMTP_USERNAME}"
          password: "${SMTP_PASSWORD}"
          
        pagerduty:
          type: "pagerduty"
          service_key: "${PAGERDUTY_SERVICE_KEY}"
          
        phone:
          type: "twilio"
          account_sid: "${TWILIO_ACCOUNT_SID}"
          auth_token: "${TWILIO_AUTH_TOKEN}"
          from_number: "${TWILIO_FROM_NUMBER}"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: incident-manager
  namespace: logging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: incident-manager
  template:
    metadata:
      labels:
        app: incident-manager
    spec:
      containers:
      - name: incident-manager
        image: python:3.9-slim
        command:
        - /bin/bash
        - -c
        - |
          pip install prometheus-api-client requests slack-sdk twilio
          python - <<EOF
          import os
          import time
          import yaml
          import json
          from datetime import datetime
          from prometheus_api_client import PrometheusConnect
          from slack_sdk import WebClient
          from twilio.rest import Client

          class IncidentManager:
              def __init__(self):
                  self.load_config()
                  self.setup_clients()
                  self.active_incidents = {}

              def load_config(self):
                  with open('/etc/alerting/config/incident-management.yaml') as f:
                      self.config = yaml.safe_load(f)

              def setup_clients(self):
                  self.prom = PrometheusConnect(url='http://prometheus:9090')
                  self.slack = WebClient(token=os.environ['SLACK_TOKEN'])
                  self.twilio = Client(
                      os.environ['TWILIO_ACCOUNT_SID'],
                      os.environ['TWILIO_AUTH_TOKEN']
                  )

              def handle_alert(self, alert):
                  incident_id = self.create_incident(alert)
                  self.notify_teams(incident_id, alert)
                  self.start_escalation(incident_id, alert)

              def create_incident(self, alert):
                  incident_id = f"INC-{int(time.time())}"
                  severity = alert['labels']['severity']
                  
                  self.active_incidents[incident_id] = {
                      'alert': alert,
                      'severity': severity,
                      'status': 'active',
                      'created_at': datetime.now(),
                      'updated_at': datetime.now(),
                      'notifications': [],
                      'timeline': []
                  }
                  
                  return incident_id

              def notify_teams(self, incident_id, alert):
                  severity = alert['labels']['severity']
                  channels = self.config['severity_levels'][severity]['notification_channels']
                  
                  for channel in channels:
                      if channel == 'slack':
                          self.notify_slack(incident_id, alert)
                      elif channel == 'pagerduty':
                          self.notify_pagerduty(incident_id, alert)
                      elif channel == 'phone':
                          self.notify_phone(incident_id, alert)
                      elif channel == 'email':
                          self.notify_email(incident_id, alert)

              def start_escalation(self, incident_id, alert):
                  category = alert['labels'].get('category', 'default')
                  policy = self.config['escalation_policies'].get(category, 
                          self.config['escalation_policies']['default'])
                  
                  for level in policy['levels']:
                      # Planifier l'escalade si pas de réponse
                      self.schedule_escalation(incident_id, level)

              def update_incident_status(self, incident_id, status, comment=None):
                  if incident_id in self.active_incidents:
                      incident = self.active_incidents[incident_id]
                      incident['status'] = status
                      incident['updated_at'] = datetime.now()
                      
                      if comment:
                          incident['timeline'].append({
                              'timestamp': datetime.now(),
                              'action': status,
                              'comment': comment
                          })

              def notify_slack(self, incident_id, alert):
                  channel = (
                      self.config['notification_channels']['slack']['urgent_channel']
                      if alert['labels']['severity'] in ['critical', 'high']
                      else self.config['notification_channels']['slack']['default_channel']
                  )
                  
                  message = self.format_slack_message(incident_id, alert)
                  self.slack.chat_postMessage(
                      channel=channel,
                      text=message,
                      blocks=self.create_slack_blocks(incident_id, alert)
                  )

              def notify_pagerduty(self, incident_id, alert):
                  # Implémenter la notification PagerDuty
                  pass

              def notify_phone(self, incident_id, alert):
                  # Implémenter la notification téléphonique via Twilio
                  pass

              def notify_email(self, incident_id, alert):
                  # Implémenter la notification par email
                  pass

              def format_slack_message(self, incident_id, alert):
                  return f"""
                  🚨 *Incident {incident_id}*
                  *Severity*: {alert['labels']['severity']}
                  *Summary*: {alert['annotations']['summary']}
                  *Description*: {alert['annotations']['description']}
                  """

              def create_slack_blocks(self, incident_id, alert):
                  return [
                      {
                          "type": "header",
                          "text": {
                              "type": "plain_text",
                              "text": f"🚨 Incident {incident_id}"
                          }
                      },
                      {
                          "type": "section",
                          "fields": [
                              {
                                  "type": "mrkdwn",
                                  "text": f"*Severity:*\n{alert['labels']['severity']}"
                              },
                              {
                                  "type": "mrkdwn",
                                  "text": f"*Category:*\n{alert['labels'].get('category', 'N/A')}"
                              }
                          ]
                      },
                      {
                          "type": "section",
                          "text": {
                              "type": "mrkdwn",
                              "text": f"*Description:*\n{alert['annotations']['description']}"
                          }
                      },
                      {
                          "type": "actions",
                          "elements": [
                              {
                                  "type": "button",
                                  "text": {
                                      "type": "plain_text",
                                      "text": "Acknowledge"
                                  },
                                  "style": "primary",
                                  "value": f"ack_{incident_id}"
                              },
                              {
                                  "type": "button",
                                  "text": {
                                      "type": "plain_text",
                                      "text": "Resolve"
                                  },
                                  "style": "danger",
                                  "value": f"resolve_{incident_id}"
                              }
                          ]
                      }
                  ]

              def schedule_escalation(self, incident_id, level):
                  # Implémenter la logique d'escalade
                  pass

          def main():
              manager = IncidentManager()
              while True:
                  # Vérifier les nouvelles alertes
                  alerts = manager.prom.get_current_alerts()
                  for alert in alerts:
                      manager.handle_alert(alert)
                  time.sleep(30)

          if __name__ == '__main__':
              main()
          EOF
        volumeMounts:
        - name: config-volume
          mountPath: /etc/alerting/config
        env:
        - name: SLACK_TOKEN
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: slack-token
        - name: PAGERDUTY_SERVICE_KEY
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: pagerduty-key
        - name: TWILIO_ACCOUNT_SID
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: twilio-sid
        - name: TWILIO_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: twilio-token
        - name: TWILIO_FROM_NUMBER
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: twilio-number
      volumes:
      - name: config-volume
        configMap:
          name: advanced-alerting-config
