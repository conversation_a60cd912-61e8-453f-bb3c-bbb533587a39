apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-filters
  namespace: logging
data:
  filter.conf: |
    <filter kubernetes.**>
      @type kubernetes_metadata
      @id filter_kube_metadata
    </filter>

    # Filtrer les logs de health check
    <filter kubernetes.**>
      @type grep
      <exclude>
        key message
        pattern /health|readiness|liveness/i
      </exclude>
    </filter>

    # Enrichir les logs avec des métadonnées supplémentaires
    <filter kubernetes.**>
      @type record_transformer
      <record>
        environment ${ENV:-production}
        cluster_name ${CLUSTER_NAME:-default}
        log_level ${tag_parts[3]}
      </record>
    </filter>

    # Parser pour les logs JSON
    <filter kubernetes.**>
      @type parser
      key_name log
      reserve_data true
      remove_key_name_field true
      <parse>
        @type json
        json_parser json
      </parse>
    </filter>

    # Agrégation des logs d'erreur
    <match kubernetes.**>
      @type rewrite_tag_filter
      <rule>
        key log_level
        pattern /^error$/i
        tag error.${tag}
      </rule>
    </match>

    # Buffer configuration pour Elasticsearch
    <match **>
      @type elasticsearch
      host elasticsearch
      port 9200
      logstash_format true
      <buffer>
        @type file
        path /var/log/fluentd-buffers/kubernetes.system.buffer
        flush_mode interval
        retry_type exponential_backoff
        flush_interval 5s
        retry_forever false
        retry_max_interval 30
        chunk_limit_size 2M
        queue_limit_length 8
        overflow_action block
      </buffer>
    </match>
