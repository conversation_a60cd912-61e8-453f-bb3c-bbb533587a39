apiVersion: v1
kind: ConfigMap
metadata:
  name: alerting-config
  namespace: logging
data:
  alert-rules.yaml: |
    groups:
      - name: infrastructure_alerts
        rules:
          # Alertes sur l'espace disque
          - alert: DiskSpaceWarning
            expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} * 100 < 20
            for: 5m
            labels:
              severity: warning
              team: infrastructure
            annotations:
              summary: "Espace disque faible (< 20%)"
              description: "L'espace disque est inférieur à 20% sur {{ $labels.instance }}"

          # Alertes sur la mémoire
          - alert: HighMemoryUsage
            expr: (1 - node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes) * 100 > 85
            for: 10m
            labels:
              severity: warning
              team: infrastructure
            annotations:
              summary: "Utilisation mémoire élevée (> 85%)"
              description: "La mémoire est utilisée à plus de 85% sur {{ $labels.instance }}"

          # Alertes sur le CPU
          - alert: HighCPUUsage
            expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
            for: 15m
            labels:
              severity: warning
              team: infrastructure
            annotations:
              summary: "Utilisation CPU élevée (> 80%)"
              description: "Le CPU est utilisé à plus de 80% sur {{ $labels.instance }}"

      - name: logging_alerts
        rules:
          # Alertes sur les erreurs de logging
          - alert: HighErrorRate
            expr: rate(log_messages_total{level="error"}[5m]) > 10
            for: 5m
            labels:
              severity: critical
              team: logging
            annotations:
              summary: "Taux d'erreurs élevé dans les logs"
              description: "Plus de 10 erreurs par minute détectées"

          # Alertes sur la latence de logging
          - alert: LoggingLatency
            expr: log_processing_duration_seconds > 2
            for: 5m
            labels:
              severity: warning
              team: logging
            annotations:
              summary: "Latence élevée du traitement des logs"
              description: "Le traitement des logs prend plus de 2 secondes"

      - name: security_alerts
        rules:
          # Alertes sur les tentatives d'accès non autorisées
          - alert: UnauthorizedAccess
            expr: rate(unauthorized_access_total[5m]) > 5
            for: 5m
            labels:
              severity: critical
              team: security
            annotations:
              summary: "Tentatives d'accès non autorisées"
              description: "Plusieurs tentatives d'accès non autorisées détectées"

          # Alertes sur les modifications de configuration
          - alert: ConfigurationChange
            expr: config_changes_total > 0
            for: 1m
            labels:
              severity: warning
              team: security
            annotations:
              summary: "Modification de configuration détectée"
              description: "Une modification de configuration a été effectuée"

  notification-channels.yaml: |
    # Configuration Slack
    slack:
      enabled: true
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channels:
        - name: "#alerts-critical"
          severity: critical
        - name: "#alerts-warning"
          severity: warning
        - name: "#alerts-info"
          severity: info
      templates:
        - title: "[{{ .Severity | toUpper }}] {{ .Summary }}"
          message: |
            *Alert:* {{ .Description }}
            *Instance:* {{ .Labels.instance }}
            *Severity:* {{ .Labels.severity }}
            *Team:* {{ .Labels.team }}
            *Time:* {{ .StartsAt | date "2006-01-02 15:04:05" }}

    # Configuration Email
    email:
      enabled: true
      smtp:
        host: "smtp.gmail.com"
        port: 587
        username: "${SMTP_USERNAME}"
        password: "${SMTP_PASSWORD}"
      recipients:
        - email: "<EMAIL>"
          severity: critical
        - email: "<EMAIL>"
          severity: warning
      templates:
        subject: "[{{ .Severity | toUpper }}] {{ .Summary }}"
        body: |
          Alert Details:
          --------------
          Summary: {{ .Summary }}
          Description: {{ .Description }}
          Severity: {{ .Labels.severity }}
          Team: {{ .Labels.team }}
          Instance: {{ .Labels.instance }}
          Time: {{ .StartsAt | date "2006-01-02 15:04:05" }}

    # Configuration PagerDuty
    pagerduty:
      enabled: true
      service_key: "${PAGERDUTY_SERVICE_KEY}"
      routing:
        - service: "infrastructure"
          team: infrastructure
          severity: critical
        - service: "logging"
          team: logging
          severity: critical
        - service: "security"
          team: security
          severity: critical

    # Configuration des webhooks personnalisés
    webhooks:
      - name: "custom-webhook"
        url: "https://api.example.com/webhook"
        headers:
          Authorization: "Bearer ${WEBHOOK_TOKEN}"
        template: |
          {
            "alert": "{{ .Summary }}",
            "description": "{{ .Description }}",
            "severity": "{{ .Labels.severity }}",
            "team": "{{ .Labels.team }}",
            "timestamp": "{{ .StartsAt }}"
          }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alert-manager
  namespace: logging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: alert-manager
  template:
    spec:
      containers:
      - name: alert-manager
        image: prom/alertmanager:latest
        ports:
        - containerPort: 9093
        volumeMounts:
        - name: config-volume
          mountPath: /etc/alertmanager
        env:
        - name: SLACK_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: slack-webhook-url
        - name: SMTP_USERNAME
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: smtp-username
        - name: SMTP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: smtp-password
        - name: PAGERDUTY_SERVICE_KEY
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: pagerduty-service-key
        - name: WEBHOOK_TOKEN
          valueFrom:
            secretKeyRef:
              name: alert-secrets
              key: webhook-token
      volumes:
      - name: config-volume
        configMap:
          name: alerting-config

---
apiVersion: v1
kind: Service
metadata:
  name: alert-manager
  namespace: logging
spec:
  selector:
    app: alert-manager
  ports:
  - port: 9093
    targetPort: 9093
  type: ClusterIP
