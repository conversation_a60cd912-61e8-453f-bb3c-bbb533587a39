apiVersion: v1
kind: Secret
metadata:
  name: kibana-security-credentials
  namespace: logging
type: Opaque
stringData:
  username: admin
  password: secure_password123
---
apiVersion: kibana.k8s.elastic.co/v1
kind: Kibana
metadata:
  name: kibana-secure
  namespace: logging
spec:
  version: 8.9.0
  count: 1
  elasticsearchRef:
    name: retreatandbe
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  podTemplate:
    spec:
      securityContext:
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: kibana
        env:
        - name: ELASTICSEARCH_USERNAME
          valueFrom:
            secretKeyRef:
              name: kibana-security-credentials
              key: username
        - name: ELASTICSEARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kibana-security-credentials
              key: password
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
