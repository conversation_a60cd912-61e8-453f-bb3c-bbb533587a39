apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: logging
data:
  infrastructure-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Infrastructure Overview",
        "tags": ["infrastructure", "overview"],
        "timezone": "browser",
        "panels": [
          {
            "title": "CPU Usage",
            "type": "graph",
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
            "targets": [
              {
                "expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode='idle'}[5m])) * 100)",
                "legendFormat": "{{instance}}"
              }
            ],
            "yaxes": [
              {"format": "percent", "label": "CPU Usage"},
              {"format": "short"}
            ]
          },
          {
            "title": "Memory Usage",
            "type": "graph",
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
            "targets": [
              {
                "expr": "(1 - node_memory_MemAvailable_bytes/node_memory_MemTotal_bytes) * 100",
                "legendFormat": "{{instance}}"
              }
            ],
            "yaxes": [
              {"format": "percent", "label": "Memory Usage"},
              {"format": "short"}
            ]
          },
          {
            "title": "Disk Usage",
            "type": "gauge",
            "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8},
            "targets": [
              {
                "expr": "100 - ((node_filesystem_avail_bytes{mountpoint='/'} * 100) / node_filesystem_size_bytes{mountpoint='/'})",
                "legendFormat": "{{instance}}"
              }
            ],
            "options": {
              "maxValue": 100,
              "minValue": 0,
              "thresholds": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 70},
                {"color": "red", "value": 85}
              ]
            }
          }
        ]
      }
    }

  logging-metrics.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Logging Metrics",
        "tags": ["logging", "metrics"],
        "timezone": "browser",
        "panels": [
          {
            "title": "Log Volume par Niveau",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
            "targets": [
              {
                "expr": "sum(rate(log_messages_total{level=~'error|warn|info'}[5m])) by (level)",
                "legendFormat": "{{level}}"
              }
            ],
            "options": {
              "legend": {"show": true},
              "tooltip": {"shared": true}
            }
          },
          {
            "title": "Latence de Traitement des Logs",
            "type": "heatmap",
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
            "targets": [
              {
                "expr": "rate(log_processing_duration_bucket[5m])",
                "format": "heatmap"
              }
            ],
            "options": {
              "calculate": true,
              "calculation": "95th"
            }
          },
          {
            "title": "Top Applications par Volume de Logs",
            "type": "bar",
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8},
            "targets": [
              {
                "expr": "topk(10, sum(rate(log_messages_total[1h])) by (app))",
                "legendFormat": "{{app}}"
              }
            ],
            "options": {
              "showValue": "auto"
            }
          }
        ]
      }
    }

  security-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Security Overview",
        "tags": ["security"],
        "timezone": "browser",
        "panels": [
          {
            "title": "Tentatives d'Accès Non Autorisées",
            "type": "stat",
            "gridPos": {"h": 4, "w": 8, "x": 0, "y": 0},
            "targets": [
              {
                "expr": "sum(increase(unauthorized_access_total[24h]))",
                "legendFormat": "24h"
              }
            ],
            "options": {
              "colorMode": "value",
              "graphMode": "area",
              "justifyMode": "auto",
              "textMode": "auto",
              "colorThresholds": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 10},
                {"color": "red", "value": 50}
              ]
            }
          },
          {
            "title": "Modifications de Configuration",
            "type": "timeline",
            "gridPos": {"h": 8, "w": 16, "x": 8, "y": 0},
            "targets": [
              {
                "expr": "changes{type='config'}",
                "legendFormat": "{{component}}"
              }
            ]
          },
          {
            "title": "Distribution des Événements de Sécurité",
            "type": "pie",
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8},
            "targets": [
              {
                "expr": "sum(security_events_total) by (type)",
                "legendFormat": "{{type}}"
              }
            ],
            "options": {
              "legend": {"show": true},
              "tooltips": true
            }
          }
        ]
      }
    }

  performance-analysis.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Performance Analysis",
        "tags": ["performance"],
        "timezone": "browser",
        "panels": [
          {
            "title": "Latence de Requête",
            "type": "graph",
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
                "legendFormat": "95th percentile"
              },
              {
                "expr": "histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
                "legendFormat": "median"
              }
            ],
            "yaxes": [
              {"format": "s", "label": "Duration"},
              {"format": "short"}
            ]
          },
          {
            "title": "Taux d'Erreur",
            "type": "timeseries",
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{status=~'5..'}[5m])) / sum(rate(http_requests_total[5m])) * 100",
                "legendFormat": "error rate"
              }
            ],
            "options": {
              "legend": {"show": true},
              "tooltip": {"shared": true}
            }
          }
        ]
      }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: logging
data:
  prometheus.yaml: |
    apiVersion: 1
    datasources:
      - name: Prometheus
        type: prometheus
        access: proxy
        url: http://prometheus:9090
        isDefault: true
        editable: false
        
  elasticsearch.yaml: |
    apiVersion: 1
    datasources:
      - name: Elasticsearch
        type: elasticsearch
        access: proxy
        url: http://elasticsearch:9200
        database: "[logstash-]YYYY.MM.DD"
        jsonData:
          esVersion: 7.17.3
          timeField: "@timestamp"
          interval: Daily

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: logging
data:
  grafana.ini: |
    [server]
    root_url = http://grafana.local
    
    [security]
    admin_user = admin
    admin_password = admin
    
    [auth.anonymous]
    enabled = false
    
    [dashboards]
    default_home_dashboard_path = /etc/grafana/provisioning/dashboards/infrastructure-overview.json
    
    [smtp]
    enabled = true
    host = smtp.gmail.com:587
    user = ${SMTP_USER}
    password = ${SMTP_PASSWORD}
    from_address = <EMAIL>
    
    [metrics]
    enabled = true
    interval_seconds = 10
