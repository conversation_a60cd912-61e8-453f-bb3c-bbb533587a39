#!/bin/bash

# Fonction pour restaurer Elasticsearch
restore_elasticsearch() {
    local snapshot_name=$1
    echo "Restauration d'Elasticsearch depuis le snapshot: $snapshot_name"

    # Vérification de la santé du cluster
    echo "Vérification de la santé du cluster..."
    curl -s "elasticsearch:9200/_cluster/health" || {
        echo "Erreur: Elasticsearch n'est pas accessible"
        exit 1
    }

    # Arrêt de l'indexation
    echo "Arrêt de l'indexation..."
    curl -X PUT "elasticsearch:9200/_cluster/settings" -H 'Content-Type: application/json' -d'
    {
        "persistent": {
            "index.blocks.write": true
        }
    }'

    # Restauration du snapshot
    echo "Restauration du snapshot..."
    curl -X POST "elasticsearch:9200/_snapshot/es-snapshots/$snapshot_name/_restore?wait_for_completion=true" -H 'Content-Type: application/json' -d'
    {
        "indices": ["logstash-*", "metrics-*", ".kibana"],
        "ignore_unavailable": true,
        "include_global_state": true
    }'

    # Vérification de la restauration
    echo "Vérification de la restauration..."
    curl -s "elasticsearch:9200/_cat/indices?v"

    # Reprise de l'indexation
    echo "Reprise de l'indexation..."
    curl -X PUT "elasticsearch:9200/_cluster/settings" -H 'Content-Type: application/json' -d'
    {
        "persistent": {
            "index.blocks.write": null
        }
    }'
}

# Fonction pour restaurer Grafana
restore_grafana() {
    local backup_date=$1
    echo "Restauration de Grafana depuis la sauvegarde du: $backup_date"

    # Vérification du service Grafana
    echo "Vérification du service Grafana..."
    curl -s "http://grafana:3000/api/health" -u admin:admin || {
        echo "Erreur: Grafana n'est pas accessible"
        exit 1
    }

    # Restauration des dashboards
    echo "Restauration des dashboards..."
    curl -X POST "http://grafana:3000/api/dashboards/db" -u admin:admin -H "Content-Type: application/json" -d @/backup/grafana/dashboards.json

    # Restauration des datasources
    echo "Restauration des datasources..."
    curl -X POST "http://grafana:3000/api/datasources" -u admin:admin -H "Content-Type: application/json" -d @/backup/grafana/datasources.json

    # Restauration des alertes
    echo "Restauration des alertes..."
    curl -X POST "http://grafana:3000/api/alerts" -u admin:admin -H "Content-Type: application/json" -d @/backup/grafana/alerts.json
}

# Menu principal
echo "Script de restauration pour l'infrastructure de logging"
echo "1. Restaurer Elasticsearch"
echo "2. Restaurer Grafana"
echo "3. Restaurer les deux"
echo "4. Quitter"

read -p "Choisissez une option (1-4): " choice

case $choice in
    1)
        read -p "Entrez le nom du snapshot Elasticsearch: " snapshot_name
        restore_elasticsearch "$snapshot_name"
        ;;
    2)
        read -p "Entrez la date de la sauvegarde Grafana (YYYYMMDD): " backup_date
        restore_grafana "$backup_date"
        ;;
    3)
        read -p "Entrez le nom du snapshot Elasticsearch: " snapshot_name
        read -p "Entrez la date de la sauvegarde Grafana (YYYYMMDD): " backup_date
        restore_elasticsearch "$snapshot_name"
        restore_grafana "$backup_date"
        ;;
    4)
        echo "Au revoir!"
        exit 0
        ;;
    *)
        echo "Option invalide"
        exit 1
        ;;
esac

echo "Restauration terminée!"
