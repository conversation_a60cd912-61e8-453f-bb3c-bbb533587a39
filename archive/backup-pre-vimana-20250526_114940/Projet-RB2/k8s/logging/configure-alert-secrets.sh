#!/bin/bash

# Couleurs pour le formatage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Fonction pour encoder en base64
encode_base64() {
    echo -n "$1" | base64
}

# Fonction pour créer le fichier de secrets
create_secrets_file() {
    echo "apiVersion: v1
kind: Secret
metadata:
  name: alert-secrets
  namespace: logging
type: Opaque
data:
  slack-webhook-url: $(encode_base64 "$SLACK_WEBHOOK_URL")
  smtp-username: $(encode_base64 "$SMTP_USERNAME")
  smtp-password: $(encode_base64 "$SMTP_PASSWORD")
  pagerduty-service-key: $(encode_base64 "$PAGERDUTY_SERVICE_KEY")
  webhook-token: $(encode_base64 "$WEBHOOK_TOKEN")" > alert-secrets.yaml
}

# Fonction pour valider l'URL Slack
validate_slack_url() {
    if [[ ! "$1" =~ ^https://hooks\.slack\.com/services/.+ ]]; then
        echo -e "${RED}URL Slack webhook invalide${NC}"
        return 1
    fi
    return 0
}

# Fonction pour valider l'email
validate_email() {
    if [[ ! "$1" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
        echo -e "${RED}Adresse email invalide${NC}"
        return 1
    fi
    return 0
}

# Message d'introduction
echo -e "${GREEN}Configuration des secrets pour l'alerting${NC}"
echo "Ce script va vous aider à configurer les secrets nécessaires pour le système d'alerting."
echo

# Collecte des informations
while true; do
    read -p "URL du webhook Slack: " SLACK_WEBHOOK_URL
    validate_slack_url "$SLACK_WEBHOOK_URL" && break
done

while true; do
    read -p "Adresse email SMTP: " SMTP_USERNAME
    validate_email "$SMTP_USERNAME" && break
done

read -s -p "Mot de passe SMTP: " SMTP_PASSWORD
echo

read -p "Clé de service PagerDuty: " PAGERDUTY_SERVICE_KEY

read -p "Token pour les webhooks personnalisés: " WEBHOOK_TOKEN

# Création du fichier de secrets
echo -e "\n${YELLOW}Création du fichier de secrets...${NC}"
create_secrets_file

# Application des secrets
echo -e "\n${YELLOW}Application des secrets dans Kubernetes...${NC}"
kubectl apply -f alert-secrets.yaml

# Nettoyage
echo -e "\n${YELLOW}Nettoyage du fichier temporaire...${NC}"
rm alert-secrets.yaml

echo -e "\n${GREEN}Configuration des secrets terminée !${NC}"
echo "Les secrets ont été configurés avec succès dans le namespace 'logging'."
