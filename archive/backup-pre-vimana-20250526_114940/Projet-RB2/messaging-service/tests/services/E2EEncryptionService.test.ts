import { E2EEncryptionService } from '../../src/services/E2EEncryptionService';

describe('E2EEncryptionService', () => {
  let encryptionService: E2EEncryptionService;
  const senderId = 'sender123';
  const recipientId = 'recipient456';
  let senderKeys: any;
  let recipientKeys: any;
  let sessionId: string;

  beforeEach(() => {
    encryptionService = E2EEncryptionService.getInstance();
    
    // Initialiser les clés des utilisateurs
    senderKeys = encryptionService.initializeUserKeys(senderId);
    recipientKeys = encryptionService.initializeUserKeys(recipientId);
    
    // Créer une session
    sessionId = encryptionService.createSession(
      senderId,
      recipientId,
      {
        identityKey: recipientKeys.identityKeyPair.publicKey,
        signedPreKey: recipientKeys.signedPreKey.publicKey,
        oneTimePreKey: recipientKeys.oneTimePreKeys[0].publicKey
      }
    );
  });

  describe('generateKeyPair', () => {
    it('should generate a valid key pair', () => {
      const keyPair = encryptionService.generateKeyPair();
      
      expect(keyPair).toBeDefined();
      expect(keyPair.publicKey).toBeDefined();
      expect(keyPair.privateKey).toBeDefined();
      expect(typeof keyPair.publicKey).toBe('string');
      expect(typeof keyPair.privateKey).toBe('string');
    });
  });

  describe('initializeUserKeys', () => {
    it('should initialize keys for a user', () => {
      const keys = encryptionService.initializeUserKeys('testUser');
      
      expect(keys).toBeDefined();
      expect(keys.identityKeyPair).toBeDefined();
      expect(keys.signedPreKey).toBeDefined();
      expect(keys.oneTimePreKeys).toBeDefined();
      expect(keys.oneTimePreKeys.length).toBeGreaterThan(0);
    });
  });

  describe('getUserPublicKeys', () => {
    it('should return public keys for a user', () => {
      const publicKeys = encryptionService.getUserPublicKeys(senderId);
      
      expect(publicKeys).toBeDefined();
      expect(publicKeys?.identityKey).toBeDefined();
      expect(publicKeys?.signedPreKey).toBeDefined();
      expect(publicKeys?.oneTimePreKeys).toBeDefined();
      expect(publicKeys?.oneTimePreKeys.length).toBeGreaterThan(0);
    });

    it('should return null for a non-existent user', () => {
      const publicKeys = encryptionService.getUserPublicKeys('nonExistentUser');
      
      expect(publicKeys).toBeNull();
    });
  });

  describe('createSession', () => {
    it('should create a session between two users', () => {
      expect(sessionId).toBeDefined();
      expect(typeof sessionId).toBe('string');
    });

    it('should throw an error if sender keys are not found', () => {
      expect(() => {
        encryptionService.createSession(
          'nonExistentUser',
          recipientId,
          {
            identityKey: recipientKeys.identityKeyPair.publicKey,
            signedPreKey: recipientKeys.signedPreKey.publicKey
          }
        );
      }).toThrow();
    });
  });

  describe('encryptMessage and decryptMessage', () => {
    it('should encrypt and decrypt a message correctly', async () => {
      const message = 'This is a secret message';
      
      // Chiffrer le message
      const encryptedMessage = await encryptionService.encryptMessage(sessionId, message);
      
      expect(encryptedMessage).toBeDefined();
      expect(encryptedMessage.header).toBeDefined();
      expect(encryptedMessage.ciphertext).toBeDefined();
      expect(encryptedMessage.iv).toBeDefined();
      expect(encryptedMessage.authTag).toBeDefined();
      expect(encryptedMessage.hmac).toBeDefined();
      
      // Déchiffrer le message
      const decryptedMessage = await encryptionService.decryptMessage(sessionId, encryptedMessage);
      
      expect(decryptedMessage).toBe(message);
    });

    it('should handle multiple messages in sequence', async () => {
      const messages = [
        'First message',
        'Second message',
        'Third message'
      ];
      
      for (const message of messages) {
        // Chiffrer le message
        const encryptedMessage = await encryptionService.encryptMessage(sessionId, message);
        
        // Déchiffrer le message
        const decryptedMessage = await encryptionService.decryptMessage(sessionId, encryptedMessage);
        
        expect(decryptedMessage).toBe(message);
      }
    });
  });

  describe('deleteSession', () => {
    it('should delete a session', () => {
      // Créer une session
      const tempSessionId = encryptionService.createSession(
        senderId,
        'tempRecipient',
        {
          identityKey: recipientKeys.identityKeyPair.publicKey,
          signedPreKey: recipientKeys.signedPreKey.publicKey
        }
      );
      
      // Supprimer la session
      encryptionService.deleteSession(tempSessionId);
      
      // Vérifier que la session a été supprimée
      // Note: Nous ne pouvons pas tester directement car les sessions sont privées
      // Nous pouvons seulement vérifier que l'utilisation de la session supprimée génère une erreur
      expect(async () => {
        await encryptionService.encryptMessage(tempSessionId, 'Test message');
      }).rejects.toThrow();
    });
  });

  describe('deleteUserKeys', () => {
    it('should delete user keys', () => {
      // Supprimer les clés de l'utilisateur
      encryptionService.deleteUserKeys(senderId);
      
      // Vérifier que les clés ont été supprimées
      const publicKeys = encryptionService.getUserPublicKeys(senderId);
      
      expect(publicKeys).toBeNull();
    });
  });
});
