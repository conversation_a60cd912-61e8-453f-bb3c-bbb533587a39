import { SecureMessagingService } from '../../src/services/SecureMessagingService';
import { E2EEncryptionService } from '../../src/services/E2EEncryptionService';

// Mock du service de chiffrement E2E
jest.mock('../../src/services/E2EEncryptionService', () => {
  const originalModule = jest.requireActual('../../src/services/E2EEncryptionService');
  
  return {
    ...originalModule,
    E2EEncryptionService: {
      getInstance: jest.fn(() => ({
        initializeUserKeys: jest.fn(() => ({
          identityKeyPair: { publicKey: 'mockPublicKey', privateKey: 'mockPrivateKey' },
          signedPreKey: { publicKey: 'mockSignedPreKey', privateKey: 'mockSignedPrivateKey' },
          oneTimePreKeys: [{ publicKey: 'mockOneTimePreKey', privateKey: 'mockOneTimePrivateKey' }]
        })),
        getUserPublicKeys: jest.fn(() => ({
          identityKey: 'mockPublicKey',
          signedPreKey: 'mockSignedPreKey',
          oneTimePreKeys: ['mockOneTimePreKey']
        })),
        createSession: jest.fn(() => 'mockSessionId'),
        encryptMessage: jest.fn(() => ({
          header: { ratchetKey: 'mockRatchetKey', counter: 0, previousCounter: 0 },
          ciphertext: 'mockCiphertext',
          iv: 'mockIv',
          authTag: 'mockAuthTag',
          hmac: 'mockHmac'
        })),
        decryptMessage: jest.fn((sessionId, encryptedMessage) => 'Decrypted: ' + encryptedMessage.ciphertext),
        deleteUserKeys: jest.fn(),
        deleteSession: jest.fn()
      }))
    }
  };
});

describe('SecureMessagingService', () => {
  let messagingService: SecureMessagingService;
  const senderId = 'sender123';
  const recipientId = 'recipient456';

  beforeEach(() => {
    messagingService = SecureMessagingService.getInstance();
  });

  describe('initializeUser', () => {
    it('should initialize a user', () => {
      const publicKeys = messagingService.initializeUser(senderId);
      
      expect(publicKeys).toBeDefined();
      expect(publicKeys.identityKey).toBe('mockPublicKey');
      expect(publicKeys.signedPreKey).toBe('mockSignedPreKey');
      expect(publicKeys.oneTimePreKeys).toEqual(['mockOneTimePreKey']);
      
      // Vérifier que la méthode du service de chiffrement a été appelée
      expect(E2EEncryptionService.getInstance().initializeUserKeys).toHaveBeenCalledWith(senderId);
    });
  });

  describe('establishSession', () => {
    it('should establish a session between two users', () => {
      const sessionId = messagingService.establishSession(
        senderId,
        recipientId,
        {
          identityKey: 'mockPublicKey',
          signedPreKey: 'mockSignedPreKey',
          oneTimePreKey: 'mockOneTimePreKey'
        }
      );
      
      expect(sessionId).toBe('mockSessionId');
      
      // Vérifier que la méthode du service de chiffrement a été appelée
      expect(E2EEncryptionService.getInstance().createSession).toHaveBeenCalledWith(
        senderId,
        recipientId,
        {
          identityKey: 'mockPublicKey',
          signedPreKey: 'mockSignedPreKey',
          oneTimePreKey: 'mockOneTimePreKey'
        }
      );
    });
  });

  describe('sendMessage', () => {
    it('should send a message', async () => {
      // Établir une session
      messagingService.establishSession(
        senderId,
        recipientId,
        {
          identityKey: 'mockPublicKey',
          signedPreKey: 'mockSignedPreKey',
          oneTimePreKey: 'mockOneTimePreKey'
        }
      );
      
      // Envoyer un message
      const message = await messagingService.sendMessage(
        senderId,
        recipientId,
        'Hello, world!',
        'text'
      );
      
      expect(message).toBeDefined();
      expect(message.senderId).toBe(senderId);
      expect(message.recipientId).toBe(recipientId);
      expect(message.content).toBe('Hello, world!');
      expect(message.type).toBe('text');
      
      // Vérifier que la méthode du service de chiffrement a été appelée
      expect(E2EEncryptionService.getInstance().encryptMessage).toHaveBeenCalled();
    });

    it('should throw an error if no session exists', async () => {
      await expect(
        messagingService.sendMessage(
          'unknownSender',
          'unknownRecipient',
          'Hello, world!',
          'text'
        )
      ).rejects.toThrow();
    });
  });

  describe('receiveMessage', () => {
    it('should receive and decrypt a message', async () => {
      // Établir une session
      messagingService.establishSession(
        senderId,
        recipientId,
        {
          identityKey: 'mockPublicKey',
          signedPreKey: 'mockSignedPreKey',
          oneTimePreKey: 'mockOneTimePreKey'
        }
      );
      
      // Envoyer un message
      const sentMessage = await messagingService.sendMessage(
        senderId,
        recipientId,
        'Hello, world!',
        'text'
      );
      
      // Recevoir le message
      const receivedMessage = await messagingService.receiveMessage(recipientId, sentMessage.id);
      
      expect(receivedMessage).toBeDefined();
      expect(receivedMessage?.senderId).toBe(senderId);
      expect(receivedMessage?.recipientId).toBe(recipientId);
      expect(receivedMessage?.content).toBe('Decrypted: mockCiphertext');
      
      // Vérifier que la méthode du service de chiffrement a été appelée
      expect(E2EEncryptionService.getInstance().decryptMessage).toHaveBeenCalled();
    });

    it('should return null if message not found', async () => {
      const message = await messagingService.receiveMessage(recipientId, 'nonExistentMessageId');
      
      expect(message).toBeNull();
    });
  });

  describe('createConversation', () => {
    it('should create a conversation', () => {
      const conversation = messagingService.createConversation([senderId, recipientId]);
      
      expect(conversation).toBeDefined();
      expect(conversation.participants).toEqual([senderId, recipientId]);
      expect(conversation.createdAt).toBeInstanceOf(Date);
      expect(conversation.updatedAt).toBeInstanceOf(Date);
    });

    it('should throw an error if less than two participants', () => {
      expect(() => {
        messagingService.createConversation([senderId]);
      }).toThrow();
    });
  });

  describe('getConversations', () => {
    it('should get conversations for a user', () => {
      // Créer une conversation
      messagingService.createConversation([senderId, recipientId]);
      
      // Récupérer les conversations
      const conversations = messagingService.getConversations(senderId);
      
      expect(conversations).toBeDefined();
      expect(conversations.length).toBeGreaterThan(0);
      expect(conversations[0].participants).toContain(senderId);
    });
  });

  describe('deleteConversation', () => {
    it('should delete a conversation', () => {
      // Créer une conversation
      const conversation = messagingService.createConversation([senderId, recipientId]);
      
      // Supprimer la conversation
      messagingService.deleteConversation(conversation.id);
      
      // Vérifier que la conversation a été supprimée
      const conversations = messagingService.getConversations(senderId);
      
      expect(conversations.find(c => c.id === conversation.id)).toBeUndefined();
    });
  });

  describe('deleteUser', () => {
    it('should delete a user', () => {
      // Initialiser un utilisateur
      messagingService.initializeUser(senderId);
      
      // Créer une conversation
      messagingService.createConversation([senderId, recipientId]);
      
      // Supprimer l'utilisateur
      messagingService.deleteUser(senderId);
      
      // Vérifier que l'utilisateur a été supprimé
      const conversations = messagingService.getConversations(senderId);
      
      expect(conversations.length).toBe(0);
      
      // Vérifier que la méthode du service de chiffrement a été appelée
      expect(E2EEncryptionService.getInstance().deleteUserKeys).toHaveBeenCalledWith(senderId);
    });
  });
});
