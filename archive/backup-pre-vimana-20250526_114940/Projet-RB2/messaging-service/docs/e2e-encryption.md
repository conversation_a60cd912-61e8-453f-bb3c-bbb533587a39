# Chiffrement de bout en bout (E2E) pour la messagerie

Ce document décrit le système de chiffrement de bout en bout implémenté pour la messagerie dans le projet Retreat And Be.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [Protocole de chiffrement](#protocole-de-chiffrement)
4. [Services](#services)
5. [Flux de données](#flux-de-données)
6. [API REST](#api-rest)
7. [Sécurité](#sécurité)
8. [Bonnes pratiques](#bonnes-pratiques)
9. [Dépannage](#dépannage)

## Vue d'ensemble

Le système de chiffrement de bout en bout (E2E) permet de sécuriser les communications entre les utilisateurs de la plateforme Retreat And Be. Il garantit que seuls l'expéditeur et le destinataire peuvent lire les messages échangés, même si les serveurs intermédiaires sont compromis.

## Architecture

Le système de chiffrement E2E est composé de plusieurs composants :

```
┌─────────────────────────┐     ┌─────────────────────────┐
│ E2EEncryptionService    │     │ SecureMessagingService  │
│                         │     │                         │
│ - Gestion des clés      │     │ - Gestion des messages  │
│ - Protocole Double      │     │ - Gestion des           │
│   Ratchet               │     │   conversations         │
│ - Chiffrement/          │     │ - API de messagerie     │
│   déchiffrement         │     │   sécurisée            │
└───────────┬─────────────┘     └───────────┬─────────────┘
            │                               │
            └───────────────────────────────┘
                           │
                           ▼
                  ┌─────────────────┐
                  │ Base de données │
                  │ (messages et    │
                  │ conversations)  │
                  └─────────────────┘
```

## Protocole de chiffrement

Le système utilise un protocole inspiré de Double Ratchet, similaire à celui utilisé par Signal, pour offrir les propriétés de sécurité suivantes :

- **Confidentialité de bout en bout** : Seuls l'expéditeur et le destinataire peuvent lire les messages.
- **Forward secrecy** : La compromission d'une clé à long terme ne compromet pas les messages passés.
- **Future secrecy** : La compromission d'une clé de session ne compromet pas les messages futurs.
- **Authentification** : Les utilisateurs peuvent vérifier l'identité de leurs correspondants.
- **Deniability** : Les messages ne peuvent pas être prouvés comme provenant d'un utilisateur spécifique.

### Composants clés du protocole

1. **Clés d'identité** : Paires de clés à long terme qui identifient les utilisateurs.
2. **Clés pré-signées** : Paires de clés signées par la clé d'identité pour l'authentification.
3. **Clés à usage unique** : Paires de clés éphémères pour l'établissement de session.
4. **Clés de chaîne** : Clés dérivées pour le chiffrement des messages.
5. **Clés de message** : Clés dérivées des clés de chaîne pour chiffrer chaque message.

### Ratchet à double cliquet

Le protocole utilise deux mécanismes de ratchet (cliquet) :

1. **Ratchet de Diffie-Hellman** : Mise à jour des clés de chaîne lors de l'échange de nouvelles clés publiques.
2. **Ratchet symétrique** : Mise à jour des clés de chaîne après chaque message.

## Services

### E2EEncryptionService

Ce service gère le chiffrement et le déchiffrement des messages. Il implémente le protocole Double Ratchet et offre les fonctionnalités suivantes :

- Génération et gestion des clés cryptographiques
- Établissement de sessions sécurisées entre utilisateurs
- Chiffrement et déchiffrement des messages
- Rotation des clés pour garantir la forward secrecy

### SecureMessagingService

Ce service gère la messagerie sécurisée. Il utilise le service E2EEncryptionService pour chiffrer et déchiffrer les messages, et offre les fonctionnalités suivantes :

- Gestion des utilisateurs et de leurs clés
- Gestion des conversations
- Envoi et réception de messages sécurisés
- Partage de fichiers sécurisé

## Flux de données

### Initialisation des utilisateurs

1. L'utilisateur s'inscrit sur la plateforme
2. Le système génère des clés d'identité, pré-signées et à usage unique pour l'utilisateur
3. Les clés publiques sont publiées sur le serveur
4. Les clés privées sont conservées localement

### Établissement de session

1. L'utilisateur A récupère les clés publiques de l'utilisateur B
2. L'utilisateur A établit une session avec l'utilisateur B en utilisant les clés publiques de B
3. Le système génère des clés de session pour la communication entre A et B

### Envoi de message

1. L'utilisateur A compose un message pour l'utilisateur B
2. Le message est chiffré avec la clé de message dérivée de la clé de chaîne
3. Le message chiffré est envoyé au serveur
4. Le serveur stocke le message chiffré et le transmet à l'utilisateur B

### Réception de message

1. L'utilisateur B reçoit le message chiffré
2. Le système déchiffre le message avec la clé de message correspondante
3. Le message déchiffré est affiché à l'utilisateur B

## API REST

Le système expose plusieurs endpoints REST pour interagir avec le service de messagerie sécurisée :

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| `POST` | `/api/secure-messaging/users/:userId/initialize` | Initialise un utilisateur |
| `POST` | `/api/secure-messaging/sessions` | Établit une session sécurisée |
| `POST` | `/api/secure-messaging/messages` | Envoie un message sécurisé |
| `GET` | `/api/secure-messaging/messages/:messageId/users/:userId` | Récupère un message |
| `POST` | `/api/secure-messaging/conversations` | Crée une conversation |
| `GET` | `/api/secure-messaging/conversations/users/:userId` | Récupère les conversations d'un utilisateur |
| `GET` | `/api/secure-messaging/conversations/:conversationId/users/:userId/messages` | Récupère les messages d'une conversation |
| `DELETE` | `/api/secure-messaging/conversations/:conversationId` | Supprime une conversation |
| `DELETE` | `/api/secure-messaging/users/:userId` | Supprime un utilisateur |

## Sécurité

### Propriétés de sécurité

Le système de chiffrement E2E offre les propriétés de sécurité suivantes :

- **Confidentialité** : Les messages sont chiffrés de bout en bout, ce qui signifie que seuls l'expéditeur et le destinataire peuvent les lire.
- **Intégrité** : Les messages sont protégés contre les modifications non autorisées grâce à des codes d'authentification de message (HMAC).
- **Authentification** : Les utilisateurs peuvent vérifier l'identité de leurs correspondants grâce aux clés d'identité.
- **Forward secrecy** : La compromission d'une clé à long terme ne compromet pas les messages passés.
- **Future secrecy** : La compromission d'une clé de session ne compromet pas les messages futurs.
- **Deniability** : Les messages ne peuvent pas être prouvés comme provenant d'un utilisateur spécifique.

### Limites

Le système présente les limites suivantes :

- **Sécurité des endpoints** : La sécurité de bout en bout ne protège pas contre les attaques sur les endpoints (appareils des utilisateurs).
- **Métadonnées** : Les métadonnées (qui parle à qui, quand, etc.) ne sont pas protégées par le chiffrement E2E.
- **Attaques par canal auxiliaire** : Le système ne protège pas contre les attaques par canal auxiliaire (timing, consommation d'énergie, etc.).

## Bonnes pratiques

1. **Vérification des clés** : Les utilisateurs devraient vérifier les clés d'identité de leurs correspondants via un canal secondaire.

2. **Rotation des clés** : Les clés devraient être régulièrement renouvelées pour limiter l'impact d'une compromission.

3. **Sécurité des endpoints** : Les appareils des utilisateurs devraient être sécurisés pour éviter la compromission des clés privées.

4. **Mise à jour du protocole** : Le protocole devrait être régulièrement mis à jour pour intégrer les dernières avancées en matière de sécurité.

5. **Audit de sécurité** : Le système devrait être régulièrement audité par des experts en sécurité.

## Dépannage

### Problèmes courants

1. **Erreur de déchiffrement** : Vérifiez que vous utilisez la bonne session et que les clés n'ont pas été compromises.

2. **Message non reçu** : Vérifiez que la session a été correctement établie et que le message a bien été envoyé.

3. **Session expirée** : Si la session a expiré, établissez une nouvelle session.

4. **Clés compromises** : Si vous suspectez que vos clés ont été compromises, générez de nouvelles clés et établissez de nouvelles sessions.

### Journalisation

Le système utilise un service de journalisation pour enregistrer les événements importants. Consultez les journaux pour obtenir des informations détaillées sur les erreurs et les opérations effectuées.

```typescript
logger.info('Secure session established successfully');
logger.error('Error sending secure message:', error);
```
