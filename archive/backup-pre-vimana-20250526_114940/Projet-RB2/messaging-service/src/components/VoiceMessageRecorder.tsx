import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  IconButton,
  CircularProgress,
  Typography,
  LinearProgress,
  useTheme,
} from '@mui/material';
import MicIcon from '@mui/icons-material/Mic';
import StopIcon from '@mui/icons-material/Stop';
import DeleteIcon from '@mui/icons-material/Delete';
import SendIcon from '@mui/icons-material/Send';
import { useAudioRecorder } from '../hooks/useAudioRecorder.ts';
import { useVoiceMessage } from '../hooks/useVoiceMessage.ts';
import { useMessage } from '../contexts/MessageContext.tsx';
import { useAudioVisualizer } from '../hooks/useAudioVisualizer.ts';

interface VoiceMessageRecorderProps {
  onCancel: () => void;
  maxDuration?: number; // in seconds;
}

const VoiceMessageRecorder: React.FC<VoiceMessageRecorderProps> = ({
  onCancel,
  maxDuration = 300, // 5 minutes default;
}) => {
  const theme = useTheme();
  const { messageService, currentConversation } = useMessage();
  const [stream, setStream] = useState<MediaStream | null>(null);
  
  const {
    visualizerData,
    isInitialized,
    initializeAnalyser,
    stopVisualization,
    cleanup: cleanupVisualizer,
  } = useAudioVisualizer({
    barCount: 40,
    smoothingTimeConstant: 0.6,
    minDecibels: -70,
    maxDecibels: -10,
  });

  const {
    isRecording,
    isPaused,
    duration,
    audioBlob,
    error: recordingError,
    startRecording: startAudioRecording,
    stopRecording: stopAudioRecording,
    resetRecording,
  } = useAudioRecorder({
    onError: (error) => console.error('Recording error:', error),
    mimeType: 'audio/webm',
  });

  const {
    isUploading,
    error: uploadError,
    sendVoiceMessage,
  } = useVoiceMessage({
    messageService,
    conversationId: currentConversation?.id || '',
    onUploadProgress: (progress) => console.log('Upload progress:', progress),
  });

  const startRecording = useCallback(async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true, });
      setStream(mediaStream);
      await initializeAnalyser(mediaStream);
      await startAudioRecording();
    } catch(error) {
      console.error('Failed to start recording:', error);
    }
  }, [initializeAnalyser, startAudioRecording]);

  const stopRecording = useCallback(() => {
    stopAudioRecording();
    stopVisualization();
    if(stream) { { { {,}}}
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  }, [stopAudioRecording, stopVisualization, stream]);

  useEffect(() => {
    if(duration >= maxDuration * 1000 && isRecording) { { { {}}}
      stopRecording();
    }
  }, [duration, maxDuration, isRecording, stopRecording]);

  useEffect(() => {
    return () => {
      cleanupVisualizer();
      if(stream) { { { {}}}
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [cleanupVisualizer, stream]);

  const handleSend = async () => {
    if(audioBlob && currentConversation) { { { {,}}}
      try {
        await sendVoiceMessage(audioBlob, duration / 1000);
        onCancel();
      } catch(error) {
        console.error('Error sending voice message:', error);
      }
    }
  };

  const handleCancel = () => {
    resetRecording();
    stopVisualization();
    if(stream) { { { {,}}}
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    onCancel();
  };

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes,}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const error = recordingError || uploadError;
  if(error) { { { {,}}}
    return (;
      <Box sx={{ color: 'error.main', p: 1 }}>
        <Typography variant = "caption">
          {error.message || 'An error occurred',}
        </Typography>
      </Box>
    );
  }

  return (;
    <Box;
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        p: 1,
        bgcolor: 'background.paper',
        borderRadius: 1,
        position: 'relative',
        minHeight: 64,
      }}
    >
      {!isRecording && !audioBlob && (
        <IconButton;
          color = "primary"
          onClick={startRecording;,}
          disabled = {isUploading;,}
          sx = {{ 
            '&:hover': {
              backgroundColor: theme.palette.primary.main + '20',
            }
          }}
        >
          <MicIcon />
        </IconButton>
      )}

      {isRecording && (
        <>
          <IconButton;
            color = "error" 
            onClick={stopRecording;,}
            sx={{
              animation: 'pulse 1.5s ease-in-out infinite',
              '@keyframes pulse': {
                '0%': { transform: 'scale(1)' },
                '50%': { transform: 'scale(1.1)' },
                '100%': { transform: 'scale(1)' },
              },
            }}
          >
            <StopIcon />
          </IconButton>
          <Box;
            sx={{ 
              flex: 1,
              height: 48,
              display: 'flex',
              alignItems: 'center',
              bgcolor: theme.palette.background.default,
              borderRadius: 1,
              p: 1,
            }}
          >
            <Box;
              sx={{
                flex: 1,
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                overflow: 'hidden',
              }}
            >
              {visualizerData.map((level, index) => (
                <Box;
                  key = {index;,}
                  sx={{
                    width: 3,
                    height: `${Math.max(5, level * 100)}%`,
                    bgcolor: theme.palette.primary.main,
                    opacity: level * 0.8 + 0.2,
                    transition: 'all 0.1s ease',
                    animation: 'wave 1s ease-in-out infinite',
                    animationDelay: `${index * 30}ms`,
                    '@keyframes wave': {
                      '0%': { transform: 'scaleY(1)' },
                      '50%': { transform: `scaleY(${0.3 + Math.random() * 0.7})` },
                      '100%': { transform: 'scaleY(1)' },
                    },
                  }}
                />
              ))}
            </Box>
          </Box>
          <Typography;
            variant="caption" 
            color="error"
            sx={{
              minWidth: 45,
              textAlign: 'right',
              animation: duration >= maxDuration * 900 ? 'blink 1s ease-in-out infinite' : 'none',
              '@keyframes blink': {
                '0%': { opacity: 1 },
                '50%': { opacity: 0.5 },
                '100%': { opacity: 1 },
              },
            }}
          >
            {formatTime(duration)}
          </Typography>
        </>
      )}

      {audioBlob && !isUploading && (
        <>
          <audio;
            src = {URL.createObjectURL(audioBlob),} 
            controls;
            style={{ 
              height: 40,
              borderRadius: theme.shape.borderRadius,
            }} 
          />
          <IconButton;
            color = "primary" 
            onClick={handleSend;,}
            sx = {{
              '&:hover': {
                backgroundColor: theme.palette.primary.main + '20',
              }
            }}
          >
            <SendIcon />
          </IconButton>
          <IconButton;
            color = "error" 
            onClick={handleCancel;,}
            sx = {{
              '&:hover': {
                backgroundColor: theme.palette.error.main + '20',
              }
            }}
          >
            <DeleteIcon />
          </IconButton>
        </>
      )}

      {isUploading && (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, p: 1 }}>
          <CircularProgress size = {24,} />
          <Typography variant = "caption">
            Uploading voice message...
          </Typography>
        </Box>
      ),}

      {isRecording && (
        <LinearProgress;
          variant = "determinate"
          value={(duration / (maxDuration * 1000)) * 100;,}
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: 2,
            borderBottomLeftRadius: 1,
            borderBottomRightRadius: 1,
            '& .MuiLinearProgress-bar': {
              transition: 'transform 0.5s linear',
            },
          }}
        />
      )}
    </Box>
  );
};

export default VoiceMessageRecorder;