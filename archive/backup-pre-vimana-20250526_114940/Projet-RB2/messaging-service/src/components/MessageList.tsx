import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Avatar,
  Paper,
  IconButton,
  Menu,
  MenuItem,
  Fade,
  Chip,
  Tooltip,
  Button,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Reply as ReplyIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  EmojiEmotions as EmojiIcon,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { Message, User, MessageReaction } from '../types/message.js';
import MessageService from '../services/MessageService.js';

interface MessageListProps {
  messages: Message[];
  currentUser: User;
  messageService: MessageService;
  onReply: (message: Message) => void;
  onEdit: (message: Message) => void;
  // Optional: Pass a map or function to resolve user details from senderId
  // getUserDetails?: (userId: string) => User | undefined;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  currentUser,
  messageService,
  onReply,
  onEdit,
  // getUserDetails // Uncomment if implementing user detail resolution
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, message: Message) => {
    setAnchorEl(event.currentTarget);
    setSelectedMessage(message);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedMessage(null);
  };

  const handleDelete = async () => {
    if (selectedMessage) {
      try {
        await messageService.deleteMessage(selectedMessage.id);
        // Optimistic update or refetch messages would be needed here
        handleMenuClose();
      } catch (error) {
        console.error('Failed to delete message:', error);
      }
    }
  };

  const handleReaction = async (emoji: string) => {
    if (selectedMessage) {
      console.log('Adding reaction:', emoji, 'to message:', selectedMessage.id);
      // Actual call to messageService.addReaction(selectedMessage.id, currentUser.id, emoji) needed
      setShowEmojiPicker(false);
      handleMenuClose();
    }
  };
  
  // Helper function to get sender name (simplified)
  const getSenderName = (senderId: string): string => {
    if (senderId === currentUser.id) {
      return currentUser.name;
    }
    // if (getUserDetails) { // Implement if a user details resolver is passed as prop
    //   const user = getUserDetails(senderId);
    //   return user?.name || `User ${senderId.substring(0, 6)}...`;
    // }
    return `User ${senderId.substring(0, 6)}...`; // Fallback
  };

  // Helper function to get sender avatar (simplified)
  const getSenderAvatar = (senderId: string): string | undefined => {
    if (senderId === currentUser.id) {
      return currentUser.avatar; // Assuming User type has an avatar field
    }
    // if (getUserDetails) { // Implement if a user details resolver is passed as prop
    //   const user = getUserDetails(senderId);
    //   return user?.avatar;
    // }
    return undefined; // Fallback, MUI Avatar will show initials or default icon
  };


  const renderMessageContent = (message: Message) => {
    switch (message.type) {
      case 'text':
        return (
          <Typography variant="body1" sx={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>
            {message.content}
          </Typography>
        );
      case 'image':
        return message.attachments?.map((attachment) => (
          <Box
            key={attachment.id}
            component="img"
            src={attachment.url}
            alt={attachment.name || 'image attachment'}
            sx={{
              maxWidth: '100%',
              maxHeight: 300,
              borderRadius: 1,
              cursor: 'pointer',
            }}
            onClick={() => window.open(attachment.url, '_blank')} // Simple click to open image
          />
        ));
      case 'file':
        return message.attachments?.map((attachment) => (
          <Box
            key={attachment.id}
            component="a"
            href={attachment.url}
            target="_blank"
            rel="noopener noreferrer"
            download={attachment.name || 'file'} // Encourage download
            sx={{
              display: 'flex',
              alignItems: 'center',
              p: 1,
              bgcolor: 'background.paper',
              borderRadius: 1,
              textDecoration: 'none',
              color: 'inherit',
              '&:hover': {
                bgcolor: 'action.hover',
              },
            }}
          >
            <Typography variant="body2" sx={{ mr: 1 }}>{attachment.name || 'Attached File'}</Typography>
            {attachment.size && (
              <Typography variant="caption" color="text.secondary">
                ({Math.round(attachment.size / 1024)} KB)
              </Typography>
            )}
          </Box>
        ));
      default:
        return <Typography variant="caption" color="text.secondary">Unsupported message type</Typography>;
    }
  };

  const renderReactions = (reactions: MessageReaction[]) => {
    if (!reactions || reactions.length === 0) return null;

    const groupedReactions = reactions.reduce((acc, reaction) => {
      acc[reaction.emoji] = (acc[reaction.emoji] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
        {Object.entries(groupedReactions).map(([emoji, count]) => (
          <Chip
            key={emoji}
            label={`${emoji} ${count}`}
            size="small"
            onClick={() => handleReaction(emoji)}
            sx={{ height: 'auto', lineHeight: '1.5', padding: '0 6px' }}
          />
        ))}
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        p: 2,
        height: '100%',
        overflowY: 'auto',
        flexGrow: 1,
      }}
    >
      {messages.map((message: Message) => {
        const isOwnMessage = message.senderId === currentUser.id;
        const senderName = getSenderName(message.senderId);
        const senderAvatar = getSenderAvatar(message.senderId);
        const messageIsEdited = message.updatedAt && new Date(message.updatedAt).getTime() !== new Date(message.createdAt).getTime();

        return (
          <Box
            key={message.id}
            sx={{
              display: 'flex',
              flexDirection: isOwnMessage ? 'row-reverse' : 'row',
              alignItems: 'flex-start',
              gap: 1,
            }}
          >
            {!isOwnMessage && (
              <Tooltip title={senderName}>
                <Avatar
                  src={senderAvatar}
                  alt={`${senderName} avatar`}
                  sx={{ width: 32, height: 32 }}
                >
                  {senderName?.charAt(0).toUpperCase()} {/* Fallback to initial if no avatar src */}
                </Avatar>
              </Tooltip>
            )}
            {isOwnMessage && ( /* Add a spacer for own messages if sender avatar is not shown on own messages for alignment */
                 <Box sx={{ width: 32, height: 32 }} /> // Invisible spacer
            )}
            <Box
              sx={{
                maxWidth: '70%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: isOwnMessage ? 'flex-end' : 'flex-start',
              }}
            >
              <Paper
                elevation={1}
                sx={{
                  p: '6px 12px',
                  bgcolor: isOwnMessage ? 'primary.light' : 'background.paper',
                  borderRadius: isOwnMessage ? '12px 12px 0 12px' : '12px 12px 12px 0',
                  position: 'relative',
                }}
              >
                {!isOwnMessage && (
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5, fontWeight: 'bold' }}>
                        {senderName}
                    </Typography>
                )}
                {/* Simplified reply display: Show only if replyTo ID exists */}
                {message.replyTo && (
                  <Box
                    sx={{
                      borderLeft: `3px solid ${isOwnMessage ? 'rgba(0,0,0,0.2)' : 'primary.main'}`,
                      pl: 1, mb: 0.5, py: 0.5, opacity: 0.8, fontSize: '0.8rem',
                      backgroundColor: isOwnMessage ? 'rgba(255,255,255,0.1)' : 'action.hover',
                      borderRadius: '4px',
                    }}
                  >
                    <Typography variant="caption" component="div" sx={{ fontStyle: 'italic'}}>
                      Replying to message ID: {message.replyTo.substring(0,8)}...
                      {/* To show replied message content, it needs to be fetched/passed in */}
                    </Typography>
                  </Box>
                )}
                {renderMessageContent(message)}
                {message.reactions && message.reactions.length > 0 && renderReactions(message.reactions)}
              </Paper>
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, px: 0.5 }}>
                {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                {messageIsEdited && <Tooltip title={`Edited at ${new Date(message.updatedAt!).toLocaleString()}`}><Box component="span" sx={{ml:0.5, fontStyle: 'italic', cursor: 'default'}}>(edited)</Box></Tooltip>}
              </Typography>
            </Box>
            <IconButton
              size="small"
              onClick={(e) => handleMenuOpen(e, message)}
              sx={{ opacity: 0.6, alignSelf: 'center', ml: isOwnMessage ? 0: 0.5, mr: isOwnMessage ? 0.5 : 0 }}
            >
              <MoreVertIcon fontSize="small" />
            </IconButton>
          </Box>
        );
      })}
      <div ref={messagesEndRef} />

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        TransitionComponent={Fade}
        slotProps={{
            paper: {
                sx: {
                    minWidth: 150,
                }
            }
        }}
      >
        <MenuItem onClick={() => {
          if (selectedMessage) onReply(selectedMessage);
          handleMenuClose();
        }}>
          <ReplyIcon fontSize="small" sx={{ mr: 1 }} />
          Reply
        </MenuItem>
        {selectedMessage?.senderId === currentUser.id && (
          <MenuItem onClick={() => {
            if (selectedMessage) onEdit(selectedMessage);
            handleMenuClose();
          }}>
            <EditIcon fontSize="small" sx={{ mr: 1 }} />
            Edit
          </MenuItem>
        )}
        {selectedMessage?.senderId === currentUser.id && (
            <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
                <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
                Delete
            </MenuItem>
        )}
        <MenuItem onClick={() => {
            setShowEmojiPicker(true);
            // Keep menu open for reaction picker, or close then show picker? 
            // For now, just sets state; picker UI below is a mock.
        }}>
            <EmojiIcon fontSize="small" sx={{ mr: 1 }} />
            React
        </MenuItem>
      </Menu>
      {/* Mock Emoji Picker for Reactions - Appears when 'React' is clicked */}
      {showEmojiPicker && selectedMessage && (
        <Paper elevation={4} sx={{position: 'fixed', bottom: anchorEl ? anchorEl.getBoundingClientRect().bottom + 8 : '20px', left: anchorEl ? anchorEl.getBoundingClientRect().left : '20px', zIndex: 1301, padding:1, minWidth: 200 }}>
            <Typography variant='subtitle2' gutterBottom>React to: "{selectedMessage.content.substring(0,20)}..."</Typography>
            <Box sx={{display: 'flex', justifyContent: 'space-around', flexWrap: 'wrap'}}>
                {[ '👍', '❤️', '😂', '😮', '😢', '🙏' ].map(emoji => (
                    <IconButton key={emoji} onClick={() => { handleReaction(emoji); setShowEmojiPicker(false); handleMenuClose();}}>
                        <Typography variant="h6">{emoji}</Typography>
                    </IconButton>
                ))}
            </Box>
            <Button size="small" onClick={() => {setShowEmojiPicker(false); handleMenuClose();}} sx={{mt:1, display: 'block', marginLeft: 'auto'}}>Cancel</Button>
        </Paper>
      )}
    </Box>
  );
};

export default MessageList;