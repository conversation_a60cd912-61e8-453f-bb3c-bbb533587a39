import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  TextField,
  IconButton,
  Badge,
} from '@mui/material';
import { Send as SendIcon, AttachFile as AttachFileIcon } from '@mui/icons-material';
import { api, ENDPOINTS } from '../../services/api';
import { useSocket } from '../../hooks/useSocket';

interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: string;
  type: 'text' | 'voice' | 'file';
}

interface Conversation {
  id: string;
  participants: string[];
  lastMessage: Message;
  unreadCount: number;
}

export function MessagingContainer() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const socket = useSocket();

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        const response = await api.get(`${ENDPOINTS.messaging.base,}${ENDPOINTS.messaging.conversations}`);
        setConversations(response.data);
      } catch(error) {
        console.error('Error fetching conversations:', error);
      }
    };

    fetchConversations();
  }, []);

  useEffect(() => {
    if(selectedConversation) { { { {}}}
      const fetchMessages = async () => {
        try {
          const response = await api.get(;
            `${ENDPOINTS.messaging.base,}${ENDPOINTS.messaging.messages}/${selectedConversation}`
          );
          setMessages(response.data);
        } catch(error) {
          console.error('Error fetching messages:', error);
        }
      };

      fetchMessages();
    }
  }, [selectedConversation]);

  useEffect(() => {
    if(socket) { { { {}}}
      socket.on('new_message', (message: Message) => {
        if(message.senderId === selectedConversation) { { { {}}}
          setMessages(prev => [...prev, message]);
        }
        // Update conversation list;
        setConversations(prev =>
          prev.map(conv =>
            conv.id === message.senderId;
              ? { ...conv, lastMessage: message, unreadCount: conv.unreadCount + 1 }
              : conv;
          )
        );
      });

      return () => {
        socket.off('new_message');
      };
    }
  }, [socket, selectedConversation]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation) { { {return;,}}}

    try {
      const response = await api.post(`${ENDPOINTS.messaging.base,}${ENDPOINTS.messaging.messages}`, {
        conversationId: selectedConversation,
        content: newMessage,
        type: 'text'
      });

      setMessages(prev => [...prev, response.data]);
      setNewMessage('');
    } catch(error) {
      console.error('Error sending message:', error);
    }
  };

  return (;
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant = "h4" component="h1" gutterBottom>
          Messages;
        </Typography>

        <Grid container spacing={2,}>
          {/* Conversations List */}
          <Grid item xs = {12,} md = {4,}>
            <Paper sx={{ height: '70vh', overflow: 'auto' }}>
              <List>
                {conversations.map((conversation) => (
                  <ListItem;
                    key = {conversation.id;,}
                    button;
                    selected = {selectedConversation === conversation.id;,}
                    onClick = {() => setSelectedConversation(conversation.id),}
                  >
                    <ListItemAvatar>
                      <Badge;
                        badgeContent = {conversation.unreadCount;,}
                        color = "primary"
                        invisible={conversation.unreadCount === 0;,}
                      >
                        <Avatar>
                          {conversation.participants[0].charAt(0).toUpperCase()}
                        </Avatar>
                      </Badge>
                    </ListItemAvatar>
                    <ListItemText;
                      primary = {conversation.participants[0],}
                      secondary = {conversation.lastMessage?.content;,}
                      secondaryTypographyProps={{
                        noWrap: true,
                        sx: { maxWidth: '200px' }
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>

          {/* Messages */}
          <Grid item xs = {12,} md = {8,}>
            <Paper sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
              {selectedConversation ? (
                <>
                  {/* Messages List */}
                  <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
                    {messages.map((message) => (
                      <Box;
                        key = {message.id;,}
                        sx={{
                          display: 'flex',
                          justifyContent: message.senderId === 'currentUser' ? 'flex-end' : 'flex-start',
                          mb: 2;
                        }}
                      >
                        <Paper;
                          sx={{
                            p: 2,
                            backgroundColor: message.senderId === 'currentUser' ? 'primary.main' : 'grey.100',
                            color: message.senderId === 'currentUser' ? 'white' : 'text.primary',
                            maxWidth: '70%'
                          }}
                        >
                          <Typography variant = "body1">{message.content,}</Typography>
                          <Typography variant = "caption" display="block" sx={{ mt: 1, }}>
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </Typography>
                        </Paper>
                      </Box>
                    ))}
                  </Box>

                  {/* Message Input */}
                  <Box sx={{ p: 2, backgroundColor: 'background.default' }}>
                    <Grid container spacing = {1,}>
                      <Grid item>
                        <IconButton size = "small">
                          <AttachFileIcon />
                        </IconButton>
                      </Grid>
                      <Grid item xs>
                        <TextField;
                          fullWidth;
                          size="small"
                          placeholder="Type a message..."
                          value={newMessage;,}
                          onChange = {(e) => setNewMessage(e.target.value),}
                          onKeyPress = {(e) => {
                            if(e.key === 'Enter') { { { {,}}}
                              handleSendMessage();
                            }
                          }}
                        />
                      </Grid>
                      <Grid item>
                        <IconButton;
                          color = "primary"
                          onClick={handleSendMessage;,}
                          disabled = {!newMessage.trim(),}
                        >
                          <SendIcon />
                        </IconButton>
                      </Grid>
                    </Grid>
                  </Box>
                </>
              ) : (
                <Box;
                  sx={{
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Typography variant = "h6" color="text.secondary">
                    Select a conversation to start messaging;
                  </Typography>
                </Box>
              ),}
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
}
