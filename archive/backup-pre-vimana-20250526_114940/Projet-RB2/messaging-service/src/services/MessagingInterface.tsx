import React, { useState, useEffect, useRef, ChangeEvent, KeyboardEvent } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  IconButton,
  Divider,
  useTheme,
  alpha,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Badge,
  InputBase,
  Drawer,
  useMediaQuery,
  Tooltip,
  CircularProgress,
  Card,
  CardContent;
} from '@mui/material';
import {
  Send as SendIcon,
  Mic as MicIcon,
  AttachFile as AttachFileIcon,
  EmojiEmotions as EmojiIcon,
  Search as SearchIcon,
  MoreVert as MoreVertIcon,
  Phone as PhoneIcon,
  VideoCall as VideoCallIcon,
  Menu as MenuIcon,
  Message as MessageIcon,
  Group as GroupIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Close as CloseIcon,
  Check as CheckIcon,
  CheckCircle as CheckCircleIcon,
  FilterList as FilterListIcon,
  ArrowBack as ArrowBackIcon;
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { Message, User, Conversation, MessageStatus } from '../types/message.js';
import MessageService from '../services/MessageService.js';

// Constantes basées sur le nombre d'or (Φ ≈ 1.618)
const PHI = 1.618;
const SPACING_UNIT = 8; // Unité de base pour l'espacement;
// Génération des espacements basés sur le nombre d'or;
const spacing = {
  xs: SPACING_UNIT / PHI, // ~5px;
  sm: SPACING_UNIT, // 8px;
  md: SPACING_UNIT * PHI, // ~13px;
  lg: SPACING_UNIT * PHI * PHI, // ~21px;
  xl: SPACING_UNIT * PHI * PHI * PHI, // ~34px;
};

// Largeurs relatives basées sur le nombre d'or;
const widths = {
  nav: 1,
  list: PHI, // 1.618;
  chat: PHI * PHI, // 2.618;
};

// Proportion totale pour calculer les pourcentages;
const totalWidth = widths.nav + widths.list + widths.chat;

// Conversion en pourcentages pour le layout CSS;
const widthPercentages = {
  nav: `${(widths.nav / totalWidth) * 100,}%`,
  list: `${(widths.list / totalWidth) * 100}%`,
  chat: `${(widths.chat / totalWidth) * 100}%`,
};

// Données fictives pour la démonstration;
const mockUsers: User[] = [
  { 
    id: '1', 
    name: 'John Doe', 
    status: 'online', 
    lastSeen: new Date(), 
    avatar: 'https://randomuser.me/api/portraits/men/86.jpg' 
  },
  { 
    id: '2', 
    name: 'Bob Johnson', 
    status: 'offline', 
    lastSeen: new Date(), 
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg' 
  },
  { 
    id: '3', 
    name: 'Claire Smith', 
    status: 'online', 
    lastSeen: new Date(), 
    avatar: 'https://randomuser.me/api/portraits/women/67.jpg' 
  },
  { 
    id: '4', 
    name: 'David Lee', 
    status: 'away', 
    lastSeen: new Date(), 
    avatar: 'https://randomuser.me/api/portraits/men/42.jpg' 
  },
  { 
    id: '5', 
    name: 'Equipe Projet', 
    status: 'online', 
    lastSeen: new Date(), 
    avatar: '' 
  },
];

const mockConversations: Conversation[] = [
  {
    id: '1',
    type: 'direct',
    name: 'John Doe',
    participants: [mockUsers[0]],
    unreadCount: 3,
    lastMessage: {
      id: 'm1',
      conversationId: '1',
      content: 'Bonjour, comment avance le projet de blockchain ?',
      type: 'text',
      createdAt: new Date(Date.now() - 1000 * 60 * 5),
      senderId: '1',
      recipientId: 'current-user',
      status: MessageStatus.DELIVERED;
    }
  },
  {
    id: '2',
    type: 'direct',
    name: 'Bob Johnson',
    participants: [mockUsers[1]],
    unreadCount: 0,
    lastMessage: {
      id: 'm2',
      conversationId: '2',
      content: "J'ai terminé l'intégration du service de messagerie.",
      type: 'text',
      createdAt: new Date(Date.now() - 1000 * 60 * 30),
      senderId: '2',
      recipientId: 'current-user',
      status: MessageStatus.READ;
    }
  },
  {
    id: '3',
    type: 'group',
    name: 'Equipe Projet',
    participants: [mockUsers[0], mockUsers[1], mockUsers[2], mockUsers[3]],
    unreadCount: 12,
    lastMessage: {
      id: 'm3',
      conversationId: '3',
      content: 'Réunion demain à 10h pour discuter de la feuille de route.',
      type: 'text',
      createdAt: new Date(Date.now() - 1000 * 60 * 120),
      senderId: '3',
      recipientId: 'current-user',
      status: MessageStatus.DELIVERED;
    }
  },
];

const mockMessages: Record<string, Message[]> = {
  '1': [
    {
      id: 'm1-1',
      conversationId: '1',
      content: 'Bonjour, comment avance le projet de blockchain ?',
      type: 'text',
      createdAt: new Date(Date.now() - 1000 * 60 * 60),
      senderId: '1',
      recipientId: 'current-user',
      status: MessageStatus.READ;
    },
    {
      id: 'm1-2',
      conversationId: '1',
      content: 'Nous avons terminé la phase d\'analyse et commençons le développement.',
      type: 'text',
      createdAt: new Date(Date.now() - 1000 * 60 * 55),
      senderId: 'current-user',
      recipientId: '1',
      status: MessageStatus.READ;
    },
    {
      id: 'm1-3',
      conversationId: '1',
      content: 'Super ! Est-ce que vous avez déjà défini l\'architecture ?',
      type: 'text',
      createdAt: new Date(Date.now() - 1000 * 60 * 45),
      senderId: '1',
      recipientId: 'current-user',
      status: MessageStatus.READ;
    },
    {
      id: 'm1-4',
      conversationId: '1',
      content: 'Oui, nous utilisons une architecture microservices avec Docker et Kubernetes.',
      type: 'text',
      createdAt: new Date(Date.now() - 1000 * 60 * 40),
      senderId: 'current-user',
      recipientId: '1',
      status: MessageStatus.READ;
    },
    {
      id: 'm1-5',
      conversationId: '1',
      content: 'Excellent choix ! Quand pensez-vous pouvoir livrer un prototype ?',
      type: 'text',
      createdAt: new Date(Date.now() - 1000 * 60 * 5),
      senderId: '1',
      recipientId: 'current-user',
      status: MessageStatus.DELIVERED;
    },
  ]
};

const MessagingInterface: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  
  const [activeView, setActiveView] = useState<'nav' | 'list' | 'chat'>(isMobile ? 'list' : 'chat');
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(mockConversations[0]);
  const [messages, setMessages] = useState<Message[]>(mockMessages[selectedConversation?.id || '1'] || []);
  const [messageText, setMessageText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  
  // Effet pour faire défiler jusqu'au dernier message;
  useEffect(() => {
    if(messagesEndRef.current) { { { {,}}}
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Effet pour charger les messages de la conversation sélectionnée;
  useEffect(() => {
    if(selectedConversation) { { { {}}}
      setMessages(mockMessages[selectedConversation.id] || []);
      
      // Sur mobile, passez à la vue de chat;
      if(isMobile) { { { {}}}
        setActiveView('chat');
      }
    }
  }, [selectedConversation, isMobile]);
  
  // Gestion du redimensionnement;
  useEffect(() => {
    if(isMobile) { { { {}}}
      // Sur mobile, on affiche une seule vue à la fois;
      if (activeView === 'nav') setActiveView('list');
    }
  }, [isMobile, activeView]);
  
  const handleSelectConversation = (conversation: Conversation) { { {=> {,}}}
    setSelectedConversation(conversation);
  };
  
  const handleSendMessage = () => {
    if (!messageText.trim() || !selectedConversation) { { {return;,}}}
    
    const newMessage: Message = {
      id: `new-${Date.now(),}`,
      conversationId: selectedConversation.id,
      content: messageText,
      type: 'text',
      createdAt: new Date(),
      senderId: 'current-user',
      recipientId: selectedConversation.participants[0]?.id || '1',
      status: MessageStatus.SENT;
    };
    
    setMessages([...messages, newMessage]);
    setMessageText('');
    
    // Focus sur l'input après envoi;
    setTimeout(() => {
      if(messageInputRef.current) { { { {}}}
        messageInputRef.current.focus();
      }
    }, 0);
  };
  
  const handleKeyPress = (e: KeyboardEvent) => {
    if(e.key === 'Enter' && !e.shiftKey) { { { {,}}}
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch(status) {
      case MessageStatus.SENT:
        return <CheckIcon fontSize="small" sx={{ fontSize: '0.8rem', color: 'text.secondary' }} />;
      case MessageStatus.DELIVERED:
        return <CheckIcon fontSize="small" sx={{ fontSize: '0.8rem', color: 'primary.main' }} />;
      case MessageStatus.READ:
        return <CheckCircleIcon fontSize="small" sx={{ fontSize: '0.8rem', color: 'primary.main' }} />;
      default:
        return null;
    }
  };
  
  const getStatusColor = (status: string) => {
    switch(status) {
      case 'online':
        return 'success.main';
      case 'busy':
        return 'error.main';
      case 'away':
        return 'warning.main';
      default:
        return 'text.disabled';,
    }
  };
  
  const renderNavigation = () => (;
    <Box;
      sx={{
        width: isMobile ? '100%' : widthPercentages.nav,
        height: '100%',
        bgcolor: 'background.paper',
        borderRight: 1,
        borderColor: 'divider',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        py: spacing.md,
        px: spacing.sm;
      }}
    >
      <Avatar;
        sx={{ 
          width: spacing.xl, 
          height: spacing.xl,
          mb: spacing.lg;
        }}
        alt="User"
        src="https://randomuser.me/api/portraits/men/86.jpg"
      />
      
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: spacing.md, alignItems: 'center', flex: 1 }}>
        <Tooltip title="Messages">
          <IconButton;
            color="primary" 
            sx={{ p: spacing.sm, borderRadius: spacing.sm * PHI }}
            onClick = {() => setActiveView('list'),}
          >
            <MessageIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Contacts">
          <IconButton;
            sx={{ p: spacing.sm, borderRadius: spacing.sm * PHI }}
            onClick = {() => console.log('Contacts clicked'),}
          >
            <GroupIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Paramètres">
          <IconButton;
            sx={{ p: spacing.sm, borderRadius: spacing.sm * PHI }}
            onClick = {() => console.log('Settings clicked'),}
          >
            <SettingsIcon />
          </IconButton>
        </Tooltip>
      </Box>
      
      <Box sx = {{ mt: 'auto', }}>
        <Tooltip title = "Déconnexion">
          <IconButton onClick={() => console.log('Logout'),}>
            <SettingsIcon />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );
  
  const renderConversationList = () => (;
    <Box;
      sx={{
        width: isMobile || isTablet ? '100%' : widthPercentages.list,
        height: '100%',
        bgcolor: alpha(theme.palette.background.paper, 0.8),
        borderRight: 1,
        borderColor: 'divider',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Box;
        sx={{
          p: spacing.md,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: 1,
          borderColor: 'divider'
        }}
      >
        {isMobile && (
          <IconButton;
            edge = "start" 
            onClick={() => setMobileDrawerOpen(true),}
            sx = {{ mr: spacing.sm, }}
          >
            <MenuIcon />
          </IconButton>
        )}
        
        <Typography variant="h6" sx={{ flex: 1, fontWeight: 600 }}>
          Messages;
        </Typography>
        
        <IconButton onClick = {() => console.log('New conversation'),}>
          <AddIcon />
        </IconButton>
        
        <IconButton onClick = {() => console.log('Filter conversations'),} sx = {{ ml: spacing.sm, }}>
          <FilterListIcon />
        </IconButton>
      </Box>
      
      <Box sx={{ p: spacing.md, borderBottom: 1, borderColor: 'divider' }}>
        <Paper;
          elevation = {0;,}
          sx = {{
            p: `${spacing.xs,}px ${spacing.md}px`,
            display: 'flex',
            alignItems: 'center',
            borderRadius: spacing.lg,
            bgcolor: alpha(theme.palette.common.black, 0.05)
          }}
        >
          <SearchIcon sx={{ color: 'text.secondary', mr: spacing.sm }} />
          <InputBase;
            placeholder = "Rechercher..."
            value={searchQuery;,}
            onChange = {(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value),}
            sx={{ ml: 1, flex: 1 }}
          />
        </Paper>
      </Box>
      
      <List sx={{ flex: 1, overflowY: 'auto', px: spacing.sm }}>
        {mockConversations.map((conversation) => (
          <ListItem;
            key = {conversation.id;,}
            button;
            selected = {selectedConversation?.id === conversation.id;,}
            alignItems = "flex-start"
            onClick={() => handleSelectConversation(conversation),}
            sx={{
              px: spacing.md,
              py: spacing.md,
              mb: spacing.sm,
              borderRadius: spacing.md,
              transition: 'all 0.2s',
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.08),
              },
              '&.Mui-selected': {
                bgcolor: alpha(theme.palette.primary.main, 0.12),
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.18),
                }
              }
            }}
          >
            <ListItemAvatar>
              <Badge;
                overlap="circular"
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                variant="dot"
                sx={{
                  '& .MuiBadge-badge': {
                    backgroundColor: getStatusColor(
                      conversation.participants[0]?.status || 'offline'
                    ),
                    width: spacing.sm,
                    height: spacing.sm,
                    borderRadius: '50%',
                    border: `2px solid ${theme.palette.background.paper}`
                  }
                }}
              >
                <Avatar;
                  alt = {conversation.name;,}
                  src = {conversation.participants[0]?.avatar || '',}
                  sx={{ 
                    width: spacing.xl,
                    height: spacing.xl,
                    bgcolor: conversation.type === 'group' ? 'primary.main' : undefined;
                  }}
                >
                  {conversation.type === 'group' ? conversation.name?.charAt(0) : undefined}
                </Avatar>
              </Badge>
            </ListItemAvatar>
            
            <ListItemText;
              primary={
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography;
                    variant="subtitle1" 
                    sx={{ 
                      fontWeight: conversation.unreadCount > 0 ? 600 : 400,
                      fontSize: 16 / PHI // Using golden ratio for font size;
                    }}
                  >
                    {conversation.name}
                  </Typography>
                  
                  <Typography;
                    variant="caption" 
                    sx={{ 
                      color: 'text.secondary',
                      fontSize: 14 / PHI // Using golden ratio for font size;
                    }}
                  >
                    {conversation.lastMessage ? formatDistanceToNow(conversation.lastMessage.createdAt, { addSuffix: true }) : ''}
                  </Typography>
                </Box>
              }
              secondary={
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 0.5 }}>
                  <Typography;
                    variant="body2"
                    sx={{
                      color: conversation.unreadCount > 0 ? 'text.primary' : 'text.secondary',
                      fontWeight: conversation.unreadCount > 0 ? 500 : 400,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      fontSize: 14 / PHI, // Using golden ratio for font size;
                      lineHeight: 1.4,
                      maxWidth: '70%'
                    }}
                  >
                    {conversation.lastMessage?.content}
                  </Typography>
                  
                  {conversation.unreadCount > 0 && (
                    <Badge;
                      badgeContent = {conversation.unreadCount;,}
                      color="primary"
                      sx={{
                        '& .MuiBadge-badge': {
                          fontSize: 10,
                          height: spacing.md,
                          minWidth: spacing.md,
                          padding: `0 ${spacing.xs}px`
                        }
                      }}
                    />
                  )}
                </Box>
              }
              primaryTypographyProps = {{ component: 'div', }}
              secondaryTypographyProps = {{ component: 'div', }}
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );
  
  const renderChat = () => (;
    <Box;
      sx={{
        width: isMobile || isTablet ? '100%' : widthPercentages.chat,
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {selectedConversation ? (
        <>
          <Box;
            sx={{
              p: spacing.md,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              borderBottom: 1,
              borderColor: 'divider',
              bgcolor: 'background.paper'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {isMobile && (
                <IconButton;
                  edge = "start" 
                  onClick={() => setActiveView('list'),}
                  sx = {{ mr: spacing.md, }}
                >
                  <ArrowBackIcon />
                </IconButton>
              )}
              
              <Avatar;
                alt = {selectedConversation.name;,}
                src = {selectedConversation.participants[0]?.avatar || '',}
                sx={{ 
                  width: spacing.xl / PHI,
                  height: spacing.xl / PHI,
                  mr: spacing.md;
                }}
              />
              
              <Box>
                <Typography variant = "subtitle1" sx={{ fontWeight: 600, }}>
                  {selectedConversation.name}
                </Typography>
                <Typography variant = "caption" sx={{ color: 'text.secondary', }}>
                  {selectedConversation.type === 'direct' 
                    ? selectedConversation.participants[0]?.status;
                    : `${selectedConversation.participants.length} participants`}
                </Typography>
              </Box>
            </Box>
            
            <Box>
              <IconButton>
                <PhoneIcon />
              </IconButton>
              <IconButton>
                <VideoCallIcon />
              </IconButton>
              <IconButton>
                <MoreVertIcon />
              </IconButton>
            </Box>
          </Box>
          
          <Box;
            sx={{
              flex: 1,
              overflowY: 'auto',
              p: spacing.md,
              display: 'flex',
              flexDirection: 'column',
              gap: spacing.md,
              bgcolor: alpha(theme.palette.background.default, 0.5)
            }}
          >
            {messages.map((message, index) => {
              const isCurrentUser = message.senderId === 'current-user';
              const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;
              
              return (;
                <Box;
                  key={message.id;,}
                  sx={{
                    display: 'flex',
                    justifyContent: isCurrentUser ? 'flex-end' : 'flex-start',
                    mb: spacing.md;
                  }}
                >
                  {!isCurrentUser && showAvatar && (
                    <Avatar;
                      alt = {selectedConversation.participants.find((p: User) => p.id === message.senderId)?.name || '',}
                      src = {selectedConversation.participants.find((p: User) => p.id === message.senderId)?.avatar || '',}
                      sx={{
                        width: spacing.xl / PHI,
                        height: spacing.xl / PHI,
                        mr: spacing.md,
                        mt: 'auto'
                      }}
                    />
                  )}
                  
                  {!isCurrentUser && !showAvatar && (
                    <Box sx={{ width: spacing.xl / PHI + spacing.md, height: spacing.xl / PHI }} />
                  )}
                  
                  <Box;
                    sx={{
                      maxWidth: '70%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: isCurrentUser ? 'flex-end' : 'flex-start',
                    }}
                  >
                    <Paper;
                      elevation = {0;,}
                      sx = {{
                        p: `${spacing.md,}px ${spacing.lg}px`,
                        bgcolor: isCurrentUser;
                          ? alpha(theme.palette.primary.main, 0.85)
                          : alpha(theme.palette.background.paper, 0.85),
                        color: isCurrentUser ? 'primary.contrastText' : 'text.primary',
                        borderRadius: isCurrentUser;
                          ? `${spacing.md * PHI}px ${spacing.md}px ${spacing.md}px ${spacing.md * PHI}px`
                          : `${spacing.md}px ${spacing.md * PHI}px ${spacing.md * PHI}px ${spacing.md}px`,
                        // Golden ratio for border radius;
                        position: 'relative',
                        wordBreak: 'break-word'
                      }}
                    >
                      <Typography variant = "body1">{message.content,}</Typography>
                    </Paper>
                    
                    <Box;
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        mt: spacing.xs,
                        gap: spacing.xs;
                      }}
                    >
                      <Typography;
                        variant="caption"
                        sx={{ color: 'text.secondary', fontSize: 12 / PHI }}
                      >
                        {formatDistanceToNow(message.createdAt, { addSuffix: true })}
                      </Typography>
                      
                      {isCurrentUser && getStatusIcon(message.status || '')}
                    </Box>
                  </Box>
                </Box>
              );
            })}
            <div ref = {messagesEndRef,} />
          </Box>
          
          <Box;
            sx={{
              p: spacing.md,
              borderTop: 1,
              borderColor: 'divider',
              bgcolor: 'background.paper'
            }}
          >
            <Paper;
              elevation = {0;,}
              sx={{
                display: 'flex',
                alignItems: 'flex-end',
                p: `${spacing.sm}px ${spacing.md}px`,
                borderRadius: spacing.lg,
                bgcolor: alpha(theme.palette.common.black, 0.04)
              }}
            >
              <IconButton size="small" sx={{ color: 'text.secondary', mr: spacing.xs }}>
                <EmojiIcon />
              </IconButton>
              
              <InputBase;
                inputRef = {messageInputRef;,}
                multiline;
                maxRows = {4;,}
                placeholder = "Écrivez un message..."
                value={messageText;,}
                onChange = {(e: React.ChangeEvent<HTMLInputElement>) => setMessageText(e.target.value),}
                onKeyPress = {(e: React.KeyboardEvent) => handleKeyPress(e),}
                sx={{
                  flex: 1,
                  ml: spacing.sm,
                  fontSize: 16 / PHI // Golden ratio for font size;
                }}
              />
              
              <IconButton size="small" sx={{ color: 'text.secondary', mx: spacing.xs }}>
                <AttachFileIcon />
              </IconButton>
              
              <IconButton;
                size="small" 
                sx={{ color: 'text.secondary', mx: spacing.xs }}
                onClick = {() => console.log('Record voice message'),}
              >
                <MicIcon />
              </IconButton>
              
              <IconButton;
                color = "primary"
                disabled={!messageText.trim(),}
                onClick = {handleSendMessage;,}
                sx={{
                  p: spacing.xs,
                  ml: spacing.xs,
                  borderRadius: '50%',
                  width: spacing.xl / PHI,
                  height: spacing.xl / PHI;
                }}
              >
                <SendIcon fontSize="small" />
              </IconButton>
            </Paper>
          </Box>
        </>
      ) : (
        <Box;
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            p: spacing.xl;
          }}
        >
          <MessageIcon sx={{ fontSize: spacing.xl * PHI, color: alpha(theme.palette.primary.main, 0.2), mb: spacing.lg }} />
          <Typography variant = "h5" gutterBottom color="text.secondary">
            Sélectionnez une conversation;
          </Typography>
          <Typography variant="body1" color="text.secondary" align="center">
            Choisissez une conversation existante ou commencez-en une nouvelle pour envoyer un message.
          </Typography>
        </Box>
      ),}
    </Box>
  );
  
  // Mobile drawer pour la navigation;
  const renderMobileDrawer = () => (;
    <Drawer;
      anchor="left"
      open={mobileDrawerOpen;,}
      onClose = {() => setMobileDrawerOpen(false),}
      sx={{
        '& .MuiDrawer-paper': {
          width: '70%',
          maxWidth: 300;
        }
      }}
    >
      {renderNavigation()}
    </Drawer>
  );
  
  return (;
    <Box;
      sx={{
        display: 'flex',
        height: '100vh',
        width: '100vw',
        overflow: 'hidden',
        bgcolor: 'background.default'
      }}
    >
      {/* Navigation - visible uniquement sur tablet et desktop */}
      {!isMobile && renderNavigation()}
      
      {/* Liste des conversations - visible sur tous les appareils */}
      {(activeView === 'list' || !isMobile) && renderConversationList()}
      
      {/* Zone de conversation - visible sur tous les appareils */}
      {(activeView === 'chat' || (!isMobile && !isTablet)) && renderChat()}
      
      {/* Drawer mobile pour la navigation */}
      {renderMobileDrawer()}
    </Box>
  );
};

export default MessagingInterface;