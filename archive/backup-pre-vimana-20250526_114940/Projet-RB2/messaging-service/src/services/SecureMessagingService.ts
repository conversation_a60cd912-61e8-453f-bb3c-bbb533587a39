import { E2EEncryptionService, EncryptedMessage } from './E2EEncryptionService';
import { logger } from '../utils/logger';

/**
 * Interface pour un message
 */
export interface Message {
  id: string;
  senderId: string;
  recipientId: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file';
  metadata?: Record<string, any>;
  attachments?: Array<{
    id: string;
    type: string;
    url: string;
    name: string;
    size: number;
  }>;
}

/**
 * Interface pour un message chiffré
 */
export interface SecureMessage {
  id: string;
  senderId: string;
  recipientId: string;
  encryptedContent: EncryptedMessage;
  timestamp: Date;
  type: 'text' | 'image' | 'file';
  metadata?: Record<string, any>;
  attachments?: Array<{
    id: string;
    type: string;
    encryptedUrl: string;
    encryptedName: EncryptedMessage;
    size: number;
  }>;
}

/**
 * Interface pour une conversation
 */
export interface Conversation {
  id: string;
  participants: string[];
  lastMessage?: Message;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

/**
 * Service de messagerie sécurisée
 * Ce service utilise le chiffrement de bout en bout pour sécuriser les messages
 */
export class SecureMessagingService {
  private static instance: SecureMessagingService;
  private encryptionService: E2EEncryptionService;
  private userSessions: Map<string, Map<string, string>> = new Map(); // userId -> { recipientId -> sessionId }
  private messages: Map<string, SecureMessage> = new Map(); // messageId -> message
  private conversations: Map<string, Conversation> = new Map(); // conversationId -> conversation

  private constructor() {
    this.encryptionService = E2EEncryptionService.getInstance();
  }

  /**
   * Obtient l'instance unique du service (Singleton)
   */
  public static getInstance(): SecureMessagingService {
    if (!SecureMessagingService.instance) {
      SecureMessagingService.instance = new SecureMessagingService();
    }
    return SecureMessagingService.instance;
  }

  /**
   * Initialise les clés pour un utilisateur
   * @param userId Identifiant de l'utilisateur
   * @returns Clés publiques de l'utilisateur
   */
  public initializeUser(userId: string): {
    identityKey: string;
    signedPreKey: string;
    oneTimePreKeys: string[];
  } {
    try {
      // Initialiser les clés de l'utilisateur
      this.encryptionService.initializeUserKeys(userId);
      
      // Récupérer les clés publiques
      const publicKeys = this.encryptionService.getUserPublicKeys(userId);
      
      if (!publicKeys) {
        throw new Error(`Failed to get public keys for user ${userId}`);
      }
      
      return publicKeys;
    } catch (error) {
      logger.error('Error initializing user:', error);
      throw new Error('Failed to initialize user');
    }
  }

  /**
   * Établit une session sécurisée entre deux utilisateurs
   * @param senderId Identifiant de l'expéditeur
   * @param recipientId Identifiant du destinataire
   * @param recipientPublicKeys Clés publiques du destinataire
   * @returns Identifiant de la session
   */
  public establishSession(
    senderId: string,
    recipientId: string,
    recipientPublicKeys: {
      identityKey: string;
      signedPreKey: string;
      oneTimePreKey?: string;
    }
  ): string {
    try {
      // Créer une session de chiffrement
      const sessionId = this.encryptionService.createSession(
        senderId,
        recipientId,
        recipientPublicKeys
      );
      
      // Stocker l'association utilisateur -> destinataire -> session
      if (!this.userSessions.has(senderId)) {
        this.userSessions.set(senderId, new Map());
      }
      
      this.userSessions.get(senderId)!.set(recipientId, sessionId);
      
      return sessionId;
    } catch (error) {
      logger.error('Error establishing session:', error);
      throw new Error('Failed to establish secure session');
    }
  }

  /**
   * Envoie un message sécurisé
   * @param senderId Identifiant de l'expéditeur
   * @param recipientId Identifiant du destinataire
   * @param content Contenu du message
   * @param type Type de message
   * @param metadata Métadonnées du message
   * @param attachments Pièces jointes
   * @returns Message envoyé
   */
  public async sendMessage(
    senderId: string,
    recipientId: string,
    content: string,
    type: 'text' | 'image' | 'file' = 'text',
    metadata?: Record<string, any>,
    attachments?: Array<{
      id: string;
      type: string;
      url: string;
      name: string;
      size: number;
    }>
  ): Promise<Message> {
    try {
      // Vérifier si une session existe
      const sessionId = this.getSessionId(senderId, recipientId);
      
      if (!sessionId) {
        throw new Error(`No secure session established between ${senderId} and ${recipientId}`);
      }
      
      // Chiffrer le contenu du message
      const encryptedContent = await this.encryptionService.encryptMessage(sessionId, content);
      
      // Chiffrer les noms des pièces jointes si présentes
      const encryptedAttachments = attachments
        ? await Promise.all(
            attachments.map(async attachment => {
              const encryptedName = await this.encryptionService.encryptMessage(
                sessionId,
                attachment.name
              );
              
              return {
                id: attachment.id,
                type: attachment.type,
                encryptedUrl: attachment.url, // Dans une implémentation réelle, l'URL serait également chiffrée ou sécurisée
                encryptedName,
                size: attachment.size
              };
            })
          )
        : undefined;
      
      // Créer le message sécurisé
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      const timestamp = new Date();
      
      const secureMessage: SecureMessage = {
        id: messageId,
        senderId,
        recipientId,
        encryptedContent,
        timestamp,
        type,
        metadata,
        attachments: encryptedAttachments
      };
      
      // Stocker le message
      this.messages.set(messageId, secureMessage);
      
      // Mettre à jour ou créer la conversation
      this.updateConversation(senderId, recipientId, {
        id: messageId,
        senderId,
        recipientId,
        content, // Dans une implémentation réelle, on ne stockerait pas le contenu en clair
        timestamp,
        type,
        metadata,
        attachments
      });
      
      // Retourner le message (version non chiffrée pour l'expéditeur)
      return {
        id: messageId,
        senderId,
        recipientId,
        content,
        timestamp,
        type,
        metadata,
        attachments
      };
    } catch (error) {
      logger.error('Error sending secure message:', error);
      throw new Error('Failed to send secure message');
    }
  }

  /**
   * Récupère et déchiffre un message
   * @param userId Identifiant de l'utilisateur
   * @param messageId Identifiant du message
   * @returns Message déchiffré
   */
  public async receiveMessage(userId: string, messageId: string): Promise<Message | null> {
    try {
      // Récupérer le message chiffré
      const secureMessage = this.messages.get(messageId);
      
      if (!secureMessage) {
        return null;
      }
      
      // Vérifier que l'utilisateur est le destinataire
      if (secureMessage.recipientId !== userId) {
        throw new Error('User is not the recipient of this message');
      }
      
      // Récupérer la session
      const sessionId = this.getSessionId(userId, secureMessage.senderId);
      
      if (!sessionId) {
        throw new Error(`No secure session established between ${userId} and ${secureMessage.senderId}`);
      }
      
      // Déchiffrer le contenu du message
      const content = await this.encryptionService.decryptMessage(
        sessionId,
        secureMessage.encryptedContent
      );
      
      // Déchiffrer les noms des pièces jointes si présentes
      const attachments = secureMessage.attachments
        ? await Promise.all(
            secureMessage.attachments.map(async attachment => {
              const name = await this.encryptionService.decryptMessage(
                sessionId,
                attachment.encryptedName
              );
              
              return {
                id: attachment.id,
                type: attachment.type,
                url: attachment.encryptedUrl, // Dans une implémentation réelle, l'URL serait également déchiffrée
                name,
                size: attachment.size
              };
            })
          )
        : undefined;
      
      // Retourner le message déchiffré
      return {
        id: secureMessage.id,
        senderId: secureMessage.senderId,
        recipientId: secureMessage.recipientId,
        content,
        timestamp: secureMessage.timestamp,
        type: secureMessage.type,
        metadata: secureMessage.metadata,
        attachments
      };
    } catch (error) {
      logger.error('Error receiving secure message:', error);
      throw new Error('Failed to receive secure message');
    }
  }

  /**
   * Récupère les messages d'une conversation
   * @param userId Identifiant de l'utilisateur
   * @param conversationId Identifiant de la conversation
   * @returns Messages déchiffrés
   */
  public async getConversationMessages(
    userId: string,
    conversationId: string
  ): Promise<Message[]> {
    try {
      // Récupérer la conversation
      const conversation = this.conversations.get(conversationId);
      
      if (!conversation) {
        return [];
      }
      
      // Vérifier que l'utilisateur est un participant
      if (!conversation.participants.includes(userId)) {
        throw new Error('User is not a participant in this conversation');
      }
      
      // Récupérer tous les messages de la conversation
      const conversationMessages = Array.from(this.messages.values()).filter(
        message =>
          (message.senderId === userId || message.recipientId === userId) &&
          (message.senderId === conversation.participants.find(p => p !== userId) ||
            message.recipientId === conversation.participants.find(p => p !== userId))
      );
      
      // Déchiffrer les messages
      const decryptedMessages = await Promise.all(
        conversationMessages.map(async message => {
          // Si l'utilisateur est l'expéditeur, pas besoin de déchiffrer
          if (message.senderId === userId) {
            // Dans une implémentation réelle, on récupérerait le message en clair depuis une autre source
            return this.receiveMessage(userId, message.id) as Promise<Message>;
          }
          
          // Sinon, déchiffrer le message
          return this.receiveMessage(userId, message.id) as Promise<Message>;
        })
      );
      
      // Filtrer les messages null et trier par date
      return decryptedMessages
        .filter(message => message !== null)
        .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    } catch (error) {
      logger.error('Error getting conversation messages:', error);
      throw new Error('Failed to get conversation messages');
    }
  }

  /**
   * Récupère les conversations d'un utilisateur
   * @param userId Identifiant de l'utilisateur
   * @returns Conversations de l'utilisateur
   */
  public getConversations(userId: string): Conversation[] {
    try {
      // Récupérer toutes les conversations où l'utilisateur est un participant
      return Array.from(this.conversations.values()).filter(conversation =>
        conversation.participants.includes(userId)
      );
    } catch (error) {
      logger.error('Error getting conversations:', error);
      throw new Error('Failed to get conversations');
    }
  }

  /**
   * Crée une nouvelle conversation
   * @param participants Identifiants des participants
   * @param metadata Métadonnées de la conversation
   * @returns Conversation créée
   */
  public createConversation(
    participants: string[],
    metadata?: Record<string, any>
  ): Conversation {
    try {
      // Vérifier qu'il y a au moins deux participants
      if (participants.length < 2) {
        throw new Error('A conversation must have at least two participants');
      }
      
      // Créer un identifiant unique pour la conversation
      const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      const timestamp = new Date();
      
      // Créer la conversation
      const conversation: Conversation = {
        id: conversationId,
        participants,
        createdAt: timestamp,
        updatedAt: timestamp,
        metadata
      };
      
      // Stocker la conversation
      this.conversations.set(conversationId, conversation);
      
      return conversation;
    } catch (error) {
      logger.error('Error creating conversation:', error);
      throw new Error('Failed to create conversation');
    }
  }

  /**
   * Met à jour une conversation
   * @param senderId Identifiant de l'expéditeur
   * @param recipientId Identifiant du destinataire
   * @param lastMessage Dernier message
   * @returns Conversation mise à jour
   */
  private updateConversation(
    senderId: string,
    recipientId: string,
    lastMessage: Message
  ): Conversation {
    // Rechercher une conversation existante entre ces utilisateurs
    let conversation = Array.from(this.conversations.values()).find(
      conv =>
        conv.participants.length === 2 &&
        conv.participants.includes(senderId) &&
        conv.participants.includes(recipientId)
    );
    
    // Si aucune conversation n'existe, en créer une nouvelle
    if (!conversation) {
      conversation = this.createConversation([senderId, recipientId]);
    }
    
    // Mettre à jour la conversation
    conversation.lastMessage = lastMessage;
    conversation.updatedAt = new Date();
    
    // Stocker la conversation mise à jour
    this.conversations.set(conversation.id, conversation);
    
    return conversation;
  }

  /**
   * Récupère l'identifiant de session entre deux utilisateurs
   * @param userId1 Identifiant du premier utilisateur
   * @param userId2 Identifiant du deuxième utilisateur
   * @returns Identifiant de la session ou null si aucune session n'existe
   */
  private getSessionId(userId1: string, userId2: string): string | null {
    // Vérifier si une session existe de userId1 vers userId2
    const sessions1 = this.userSessions.get(userId1);
    if (sessions1 && sessions1.has(userId2)) {
      return sessions1.get(userId2)!;
    }
    
    // Vérifier si une session existe de userId2 vers userId1
    const sessions2 = this.userSessions.get(userId2);
    if (sessions2 && sessions2.has(userId1)) {
      return sessions2.get(userId1)!;
    }
    
    return null;
  }

  /**
   * Supprime une conversation
   * @param conversationId Identifiant de la conversation
   */
  public deleteConversation(conversationId: string): void {
    // Supprimer la conversation
    this.conversations.delete(conversationId);
    
    // Supprimer tous les messages associés à cette conversation
    for (const [messageId, message] of this.messages.entries()) {
      const conversation = Array.from(this.conversations.values()).find(
        conv =>
          conv.participants.includes(message.senderId) &&
          conv.participants.includes(message.recipientId)
      );
      
      if (!conversation) {
        this.messages.delete(messageId);
      }
    }
  }

  /**
   * Supprime un utilisateur
   * @param userId Identifiant de l'utilisateur
   */
  public deleteUser(userId: string): void {
    // Supprimer les clés de l'utilisateur
    this.encryptionService.deleteUserKeys(userId);
    
    // Supprimer les sessions de l'utilisateur
    this.userSessions.delete(userId);
    
    // Supprimer les conversations de l'utilisateur
    for (const [conversationId, conversation] of this.conversations.entries()) {
      if (conversation.participants.includes(userId)) {
        this.conversations.delete(conversationId);
      }
    }
    
    // Supprimer les messages de l'utilisateur
    for (const [messageId, message] of this.messages.entries()) {
      if (message.senderId === userId || message.recipientId === userId) {
        this.messages.delete(messageId);
      }
    }
  }
}
