import axios from 'axios';
import { logger } from '../utils/logger.js';
import { EventEmitter } from 'events';

/**
 * Service d'intégration avec le microservice Social
 * Permet de recevoir et traiter les événements du microservice Social
 */
export class SocialIntegrationService {
  private static instance: SocialIntegrationService;
  private socialServiceUrl: string;
  private socialPlatformVideoUrl: string;
  private apiKey: string;
  private eventEmitter: EventEmitter;

  private constructor() {
    this.socialServiceUrl = process.env.SOCIAL_SERVICE_URL || 'http://localhost:3000/social';
    this.socialPlatformVideoUrl = process.env.SOCIAL_PLATFORM_VIDEO_URL || 'http://localhost:3002';
    this.apiKey = process.env.SOCIAL_API_KEY || 'default-api-key';
    this.eventEmitter = new EventEmitter();
  }

  /**
   * Obtient l'instance unique du service (singleton)
   * @returns Instance du service
   */
  public static getInstance(): SocialIntegrationService {
    if (!SocialIntegrationService.instance) {
      SocialIntegrationService.instance = new SocialIntegrationService();
    }
    return SocialIntegrationService.instance;
  }

  /**
   * Initialise le service et configure les routes d'API
   * @param app Application Express
   */
  public initialize(app: any): void {
    // Route pour recevoir les notifications de livestream
    app.post('/api/notifications', this.handleNotification.bind(this));
    
    // Route pour relayer les messages de livestream
    app.post('/api/messages/relay', this.handleMessageRelay.bind(this));

    logger.info('SocialIntegrationService initialized');
  }

  /**
   * Gère les notifications entrantes du microservice Social
   * @param req Requête Express
   * @param res Réponse Express
   */
  private async handleNotification(req: any, res: any): Promise<void> {
    try {
      const notification = req.body;
      
      if (!notification || !notification.type) {
        res.status(400).json({ message: 'Invalid notification format' });
        return;
      }

      logger.info(`Received notification: ${notification.type}`);

      // Traiter la notification en fonction de son type
      switch (notification.type) {
        case 'livestream_notification':
          await this.processLivestreamNotification(notification);
          break;
        case 'blog_comment':
          await this.processBlogCommentNotification(notification);
          break;
        default:
          logger.warn(`Unknown notification type: ${notification.type}`);
      }

      res.status(200).json({ message: 'Notification processed successfully' });
    } catch (error) {
      logger.error('Error processing notification:', error);
      res.status(500).json({ message: 'Error processing notification' });
    }
  }

  /**
   * Gère les messages relayés du microservice Social
   * @param req Requête Express
   * @param res Réponse Express
   */
  private async handleMessageRelay(req: any, res: any): Promise<void> {
    try {
      const message = req.body;
      
      if (!message || !message.type) {
        res.status(400).json({ message: 'Invalid message format' });
        return;
      }

      logger.info(`Received message relay: ${message.type}`);

      // Traiter le message en fonction de son type
      switch (message.type) {
        case 'livestream_message':
          await this.processLivestreamMessage(message);
          break;
        default:
          logger.warn(`Unknown message type: ${message.type}`);
      }

      res.status(200).json({ message: 'Message relayed successfully' });
    } catch (error) {
      logger.error('Error relaying message:', error);
      res.status(500).json({ message: 'Error relaying message' });
    }
  }

  /**
   * Traite une notification de livestream
   * @param notification Notification de livestream
   */
  private async processLivestreamNotification(notification: any): Promise<void> {
    try {
      // Récupérer les informations du livestream
      const livestreamId = notification.livestreamId;
      const eventType = notification.eventType;
      const message = notification.message;

      // Créer une notification système pour tous les utilisateurs concernés
      const systemNotification = {
        type: 'system',
        subtype: 'livestream',
        eventType,
        livestreamId,
        message,
        timestamp: new Date().toISOString(),
      };

      // Si un destinataire spécifique est indiqué, envoyer uniquement à ce destinataire
      if (notification.recipientId) {
        await this.sendNotificationToUser(notification.recipientId, systemNotification);
      } else {
        // Sinon, envoyer à tous les abonnés du livestream
        await this.sendNotificationToLivestreamSubscribers(livestreamId, systemNotification);
      }

      // Émettre un événement pour que les clients WebSocket puissent être notifiés
      this.eventEmitter.emit('livestream:notification', systemNotification);

      logger.info(`Livestream notification processed: ${eventType} - ${livestreamId}`);
    } catch (error) {
      logger.error('Error processing livestream notification:', error);
    }
  }

  /**
   * Traite un message de livestream
   * @param message Message de livestream
   */
  private async processLivestreamMessage(message: any): Promise<void> {
    try {
      // Créer un message de chat pour le livestream
      const chatMessage = {
        type: 'chat',
        subtype: 'livestream',
        livestreamId: message.livestreamId,
        userId: message.userId,
        userName: message.userName,
        content: message.content,
        timestamp: message.timestamp || new Date().toISOString(),
      };

      // Émettre un événement pour que les clients WebSocket puissent être notifiés
      this.eventEmitter.emit('livestream:message', chatMessage);

      logger.info(`Livestream message processed: ${message.livestreamId} - ${message.userId}`);
    } catch (error) {
      logger.error('Error processing livestream message:', error);
    }
  }

  /**
   * Traite une notification de commentaire sur un article de blog
   * @param notification Notification de commentaire
   */
  private async processBlogCommentNotification(notification: any): Promise<void> {
    try {
      // Récupérer les informations du blog et de l'auteur
      const blogPostId = notification.blogPostId;
      const userId = notification.userId;
      const userName = notification.userName;
      const content = notification.content;

      // Récupérer les informations de l'article de blog pour connaître l'auteur
      const blogPost = await this.fetchBlogPostDetails(blogPostId);

      if (!blogPost) {
        logger.warn(`Blog post not found: ${blogPostId}`);
        return;
      }

      // Créer une notification pour l'auteur de l'article
      const authorNotification = {
        type: 'system',
        subtype: 'blog_comment',
        blogPostId,
        blogPostTitle: blogPost.title,
        commenterId: userId,
        commenterName: userName,
        commentContent: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
        timestamp: new Date().toISOString(),
      };

      // Envoyer la notification à l'auteur de l'article
      await this.sendNotificationToUser(blogPost.authorId, authorNotification);

      logger.info(`Blog comment notification processed: ${blogPostId} - ${userId}`);
    } catch (error) {
      logger.error('Error processing blog comment notification:', error);
    }
  }

  /**
   * Récupère les détails d'un article de blog
   * @param blogPostId ID de l'article de blog
   * @returns Détails de l'article de blog
   */
  private async fetchBlogPostDetails(blogPostId: string): Promise<any> {
    try {
      const response = await axios.get(`${this.socialPlatformVideoUrl}/api/blog/${blogPostId}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      return response.data;
    } catch (error) {
      logger.error(`Error fetching blog post details: ${error.message}`);
      return null;
    }
  }

  /**
   * Envoie une notification à un utilisateur spécifique
   * @param userId ID de l'utilisateur
   * @param notification Notification à envoyer
   */
  private async sendNotificationToUser(userId: string, notification: any): Promise<void> {
    try {
      // Ici, vous pourriez utiliser votre système de notification existant
      // Par exemple, en enregistrant la notification dans la base de données
      // et en émettant un événement WebSocket
      
      // Émettre un événement pour que les clients WebSocket puissent être notifiés
      this.eventEmitter.emit('user:notification', {
        userId,
        notification,
      });
      
      logger.info(`Notification sent to user: ${userId}`);
    } catch (error) {
      logger.error(`Error sending notification to user: ${error.message}`);
    }
  }

  /**
   * Envoie une notification à tous les abonnés d'un livestream
   * @param livestreamId ID du livestream
   * @param notification Notification à envoyer
   */
  private async sendNotificationToLivestreamSubscribers(livestreamId: string, notification: any): Promise<void> {
    try {
      // Récupérer la liste des abonnés du livestream
      const subscribers = await this.fetchLivestreamSubscribers(livestreamId);
      
      // Envoyer la notification à chaque abonné
      for (const subscriber of subscribers) {
        await this.sendNotificationToUser(subscriber.userId, notification);
      }
      
      logger.info(`Notification sent to ${subscribers.length} livestream subscribers`);
    } catch (error) {
      logger.error(`Error sending notification to livestream subscribers: ${error.message}`);
    }
  }

  /**
   * Récupère la liste des abonnés d'un livestream
   * @param livestreamId ID du livestream
   * @returns Liste des abonnés
   */
  private async fetchLivestreamSubscribers(livestreamId: string): Promise<any[]> {
    try {
      const response = await axios.get(`${this.socialPlatformVideoUrl}/api/livestreams/${livestreamId}/subscribers`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      return response.data || [];
    } catch (error) {
      logger.error(`Error fetching livestream subscribers: ${error.message}`);
      return [];
    }
  }

  /**
   * Obtient l'émetteur d'événements pour s'abonner aux événements
   * @returns Émetteur d'événements
   */
  public getEventEmitter(): EventEmitter {
    return this.eventEmitter;
  }
}
