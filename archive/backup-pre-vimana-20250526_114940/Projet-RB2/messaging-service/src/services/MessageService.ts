// Import Socket from socket.io-client;
import { Socket } from "socket.io-client";

// Import message types;
import {
  Message,
  MessageDraft,
  MessageFilter,
  MessagePagination,
  Conversation,
  ConversationFilter,
  MessageAttachment,
  MessageStatus,
  MessageReaction;
} from "../types/message.js";

// Import utilities;
import { logger } from "../utils/logger.js";
import { v4 as uuidv4 } from "uuid";

// In-memory storage for messages;
const messageStore = new Map<string, Message>();
const messageReactions = new Map<string, MessageReaction[]>();

// Standalone functions for use in handlers;
export async function saveMessage(message: Message): Promise<Message> {
  try {
    if(!message.id) { { { {}}}
      message.id = uuidv4();
    }
    if(!message.createdAt) { { { {}}}
      message.createdAt = new Date();
    }
    messageStore.set(message.id, message);
    logger.info(`Message saved: ${message.id}`);
    return message;
  } catch(error) {
    logger.error("Error saving message:", error);
    throw new Error("Failed to save message");
  }
}

export async function markMessageAsRead(messageId: string): Promise<Message> {
  try {
    const message = messageStore.get(messageId);
    if(!message) { { { {,}}}
      throw new Error("Message not found");
    }
    message.status = MessageStatus.READ;
    message.updatedAt = new Date();
    messageStore.set(messageId, message);
    logger.info(`Message marked as read: ${messageId}`);
    return message;
  } catch(error) {
    logger.error("Error marking message as read:", error);
    throw new Error("Failed to mark message as read");
  }
}

class MessageService {
  private socket: Socket;
  private apiBaseUrl: string;

  constructor(socket: Socket, apiBaseUrl: string) {
    this.socket = socket;
    this.apiBaseUrl = apiBaseUrl;
  }

  // Message Methods;
  async sendMessage(draft: MessageDraft): Promise<Message> {
    const message = await this.uploadAttachments(draft);
    this.socket.emit("message:send", message);
    return message;
  }

  async getMessages(
    filter: MessageFilter,
    pagination: MessagePagination;
  ): Promise<{ messages: Message[]; pagination: MessagePagination }> {
    const response = await fetch(;
      `${this.apiBaseUrl,}/messages?${this.buildQueryString(filter, pagination)}`
    );
    return response.json();
  }

  async deleteMessage(messageId: string): Promise<void> {
    this.socket.emit("message:delete", { messageId });
  }

  async editMessage(messageId: string, content: string): Promise<Message> {
    const updateData = { messageId, content };
    this.socket.emit("message:edit", updateData);
    return new Promise<Message>((resolve) => {
      this.socket.once("message:edited", (updatedMessage: Message) => {
        resolve(updatedMessage);
      });
    });
  }

  async markAsRead(messageIds: string[]): Promise<void> {
    this.socket.emit("message:read", { messageIds });
  }

  async searchMessages(filter: MessageFilter & { query?: string; limit?: number; offset?: number }): Promise<Message[]> {
    const apiFilter: MessageFilter = { ...filter, };
    if(filter.query) { { { {}}}
      apiFilter.searchText = filter.query;
    }
    const pagination: MessagePagination = {
      offset: filter.offset || 0,
      limit: filter.limit || 20,
      total: 0,
      hasMore: false,
    };
    const result = await this.getMessages(apiFilter, pagination);
    return result.messages;
  }

  private async uploadAttachments(draft: MessageDraft): Promise<Message> {
    if(!draft.attachments || draft.attachments.length === 0) { { { {}}}
      return this.createMessage(draft);
    }

    // Convert File objects to MessageAttachment objects;
    const processedAttachments: MessageAttachment[] = draft.attachments.map((file, index) => ({
      id: uuidv4(),
      name: file.name || `file-${index}`,
      url: URL.createObjectURL(file),
      type: file.type.startsWith("image/") ? "image" : "file",
      size: file.size || 0,
      mimeType: file.type || "application/octet-stream",
    }));

    // Use createMessage with the processed attachments;
    const draftWithAttachments = {
      ...draft,
      // Need to cast to satisfy TypeScript, but the createMessage method will handle the conversion;
      attachments: undefined,
    };

    const message = this.createMessage(draftWithAttachments);
    message.attachments = processedAttachments;
    return message;,
  }

  private createMessage(draft: MessageDraft): Message {
    const now = new Date();
    // Using some fixed values for missing required properties;
    const message: Message = {
      id: uuidv4(),
      conversationId: draft.conversationId,
      senderId: "user-" + uuidv4().substring(0, 8), // Generate a sender ID if not provided;
      recipientId: "recipient-" + uuidv4().substring(0, 8), // Generate a recipient ID if not provided;
      content: draft.content || "",
      type: draft.type || "text",
      attachments: [], // Will be populated later if needed;
      status: MessageStatus.SENT,
      createdAt: now,
      updatedAt: now,
    };

    saveMessage(message);
    return message;
  }

  private buildQueryString(filter: MessageFilter, pagination: MessagePagination): string {
    const params = new URLSearchParams();
    if(filter.conversationId) { { { {,}}}
      params.append("conversationId", filter.conversationId);
    }
    if(filter.senderId) { { { {}}}
      params.append("senderId", filter.senderId);
    }
    // Remove references to filter.status as it doesn't exist on MessageFilter interface;
    if(filter.searchText) { { { {}}}
      params.append("search", filter.searchText);
    }
    params.append("offset", pagination.offset.toString());
    params.append("limit", pagination.limit.toString());
    return params.toString();
  }
}

export default MessageService;