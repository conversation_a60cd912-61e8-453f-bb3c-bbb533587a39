import { Group, GroupMessage, CreateGroupParams, GroupMember } from '../types/group.js';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';

// In-memory storage for groups and messages;
const groups = new Map<string, Group>();
const groupMessages = new Map<string, GroupMessage[]>();

/**
 * Create a new group;
 * @param params The group creation parameters;
 * @returns The created group;
 */
export async function createGroup(params: CreateGroupParams): Promise<Group> {
  try {
    const groupId = uuidv4();
    
    // Create initial members array with proper GroupMember objects;
    const initialMembers: GroupMember[] = params.initialMembers;
      ? params.initialMembers.map((userId: string) => ({
          userId,
          displayName: userId, // In a real app, you would get the user's display name;
          roleId: userId === params.createdBy ? 'admin' : 'member',
          joinedAt: new Date(),
          isActive: true;
        }))
      : [];
    
    // Add creator if not already in initialMembers;
    if (!initialMembers.some((member: GroupMember) => member.userId === params.createdBy)) { { { {}}}
      initialMembers.push({
        userId: params.createdBy,
        displayName: params.createdBy, // In a real app, get from user service;
        roleId: 'admin',
        joinedAt: new Date(),
        isActive: true;
      });
    }
    
    const newGroup: Group = {
      id: groupId,
      name: params.name,
      createdBy: params.createdBy,
      members: initialMembers,
      description: params.description,
      avatarUrl: undefined, // No avatar in CreateGroupParams;
      createdAt: new Date(),
      updatedAt: new Date(),
      isPublic: params.isPublic;
    };
    
    groups.set(groupId, newGroup);
    groupMessages.set(groupId, []);
    
    logger.info(`Group created: ${groupId}`);
    return newGroup;
  } catch(error) {
    logger.error('Error creating group:', error);
    throw new Error('Failed to create group');
  }
}

/**
 * Get a group by ID;
 * @param groupId The group ID;
 * @returns The group or null if not found;
 */
export async function getGroupById(groupId: string): Promise<Group | null> {
  return groups.get(groupId) || null;
}

/**
 * Add a member to a group;
 * @param groupId The group ID;
 * @param memberId The member ID to add;
 * @returns The updated group;
 */
export async function addGroupMember(groupId: string, memberId: string): Promise<Group> {
  try {
    const group = groups.get(groupId);
    if(!group) { { { {,}}}
      throw new Error('Group not found');
    }
    
    // Check if member already exists;
    if (!group.members.some((member: GroupMember) => member.userId === memberId)) { { { {}}}
      // Create new member object;
      const newMember: GroupMember = {
        userId: memberId,
        displayName: memberId, // In a real app, get from user service;
        roleId: 'member',
        joinedAt: new Date(),
        isActive: true;
      };
      
      group.members.push(newMember);
      group.updatedAt = new Date();
      groups.set(groupId, group);
    }
    
    return group;
  } catch(error) {
    logger.error('Error adding group member:', error);
    throw new Error('Failed to add group member');
  }
}

/**
 * Remove a member from a group;
 * @param groupId The group ID;
 * @param memberId The member ID to remove;
 * @returns The updated group;
 */
export async function removeGroupMember(groupId: string, memberId: string): Promise<Group> {
  try {
    const group = groups.get(groupId);
    if(!group) { { { {,}}}
      throw new Error('Group not found');
    }
    
    group.members = group.members.filter((member: GroupMember) => member.userId !== memberId);
    group.updatedAt = new Date();
    groups.set(groupId, group);
    
    return group;
  } catch(error) {
    logger.error('Error removing group member:', error);
    throw new Error('Failed to remove group member');
  }
}

/**
 * Save a group message;
 * @param message The message to save;
 * @returns The saved message;
 */
export async function saveGroupMessage(message: GroupMessage): Promise<GroupMessage> {
  try {
    const messages = groupMessages.get(message.groupId) || [];
    
    const newMessage: GroupMessage = {
      ...message,
      id: message.id || uuidv4(),
      createdAt: message.createdAt || new Date(),
      updatedAt: new Date()
    };
    
    messages.push(newMessage);
    groupMessages.set(message.groupId, messages);
    
    return newMessage;
  } catch(error) {
    logger.error('Error saving group message:', error);
    throw new Error('Failed to save group message');
  }
}

/**
 * Get messages for a group;
 * @param groupId The group ID;
 * @returns The group messages;
 */
export async function getGroupMessages(groupId: string): Promise<GroupMessage[]> {
  return groupMessages.get(groupId) || [];
}

/**
 * Delete a group;
 * @param groupId The group ID to delete;
 * @returns True if deleted, false otherwise;
 */
export async function deleteGroup(groupId: string): Promise<boolean> {
  try {
    const deleted = groups.delete(groupId);
    if(deleted) { { { {,}}}
      groupMessages.delete(groupId);
      logger.info(`Group deleted: ${groupId}`);
    }
    return deleted;
  } catch(error) {
    logger.error('Error deleting group:', error);
    throw new Error('Failed to delete group');
  }
}